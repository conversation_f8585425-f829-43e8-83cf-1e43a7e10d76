/*
*****************************************************************************
*
 * Copyright (C) 2008-2024 Advanced Micro Devices, Inc. All rights reserved.
 *
*******************************************************************************
*/

/* $NoKeywords:$ */
/**
 * @file
 *
 * Config Fch HwAcpi controller
 *
 * Init HwAcpi Controller features.
 *
 * @xrefitem bom "File Content Label" "Release Content"
 * @e project:     AGESA
 * @e sub-project: FCH
 * @e \$Revision: 309083 $   @e \$Date: 2014-12-09 09:28:24 -0800 (Tue, 09 Dec 2014) $
 *
 */

#include "FchPlatform.h"
#include <Protocol/FabricTopologyServices2.h>
#include <Library/FabricResourceManagerLib.h>
#include <Library/UefiBootServicesTableLib.h>
#include <Library/BaseLib.h>
#include "Filecode.h"
#define FILECODE FCH_KUNLUN_FCHKUNLUNCORE_KUNLUN_KLHWACPI_KLHWACPIENVSERVICE_FILECODE

#define AMD_CPUID_APICID_LPC_BID    0x00000001ul  // Local APIC ID, Logical Processor Count, Brand ID

ACPI_REG_WRITE FchKunlunInitEnvSpecificHwAcpiMmioTable[] =
{
  {00, 00, 0xB0, 0xAC},
  //{PMIO_BASE >> 8,  FCH_PMIOA_REG00 + 2, 0xFB, BIT2},     //PLAT-12059 //hwemu                           // Set ASF SMBUS master function enabled here (temporary)
#ifdef ACPI_SLEEP_TRAP
  {SMI_BASE  >> 8,  FCH_SMI_REGB0, 0xF3, BIT2},                                                    // Set SLP_TYPE as SMI event
  {PMIO_BASE >> 8,  FCH_PMIOA_REGBE, 0xDF, 0x00},                                                  // Disabled SLP function for S1/S3/S4/S5
  {PMIO_BASE >> 8,  FCH_PMIOA_REG08 + 3, 0xFC, BIT1},                                              // Set S state transition disabled (BIT0) force ACPI to
                                                                                                   //  send SMI message when writing to SLP_TYP Acpi register. (BIT1)
  {SMI_BASE  >> 8,  FCH_SMI_REG98 + 3, 0x7F, 0x00},                                                // Enabled Global Smi ( BIT7 clear as 0 to enable )
#endif
  //{PMIO_BASE >> 8,  FCH_PMIOA_REG80 + 1, 0xE7, BIT3 + BIT4},                                 //Kunlun bits removed
  {0xFF, 0xFF, 0xFF, 0xFF},
};

/**
 * FchInitEnvHwAcpiMmioTable - Fch ACPI MMIO initial
 * during POST.
 *
 */
ACPI_REG_WRITE FchKunlunInitEnvHwAcpiMmioTable[] =
{
  {00, 00, 0xB0, 0xAC},                                         /// Signature

  {PMIO_BASE >> 8, FCH_PMIOA_REG74, 0x00, BIT0 + BIT1 + BIT2 + BIT4},
  {PMIO_BASE >> 8, FCH_PMIOA_REG74 + 2, 0x00, BIT1 + BIT2},             //en_shutdown_msg, en_sync_flood
  {PMIO_BASE >> 8, FCH_PMIOA_REG74 + 3, 0xDF, 0},
  {PMIO_BASE >> 8, FCH_PMIOA_REGBA, 0xF7, BIT3},
  {PMIO_BASE >> 8, FCH_PMIOA_REGBC, 0xFD, BIT1},
  {PMIO_BASE >> 8, FCH_PMIOA_REGC8, 0xFD, BIT1}, //enable InstandOffEnable

  {SMI_BASE >> 8, FCH_SMI_Gevent1, 0, 1},
  {SMI_BASE >> 8, FCH_SMI_Gevent3, 0, 3},
  {SMI_BASE >> 8, FCH_SMI_Gevent4, 0, 4},
  {SMI_BASE >> 8, FCH_SMI_Gevent5, 0, 5},
  {SMI_BASE >> 8, FCH_SMI_Gevent6, 0, 6},
  {SMI_BASE >> 8, FCH_SMI_Gevent23, 0, 23},
  {SMI_BASE >> 8, FCH_SMI_Gevent8, 0, 24},
  {SMI_BASE >> 8, FCH_SMI_REG08,  0xE7, 0},
  {SMI_BASE >> 8, FCH_SMI_REG0C + 2, 0xF7, BIT3},
  {SMI_BASE >> 8, FCH_SMI_KL_TWRN, 0, 9},
  {SMI_BASE >> 8, FCH_SMI_KL_FanThGevent, 0, 13},
  {SMI_BASE >> 8, FCH_SMI_KL_NbGppPme, 0, 16},
  {SMI_BASE >> 8, FCH_SMI_KL_NbGppHp, 0, 17},
  {0xFF, 0xFF, 0xFF, 0xFF},
};

/**
 * FchKunlunInitEnvHwAcpiPciTable - PCI device registers
 * initial during early POST.
 *
 */
REG8_MASK FchKunlunInitEnvHwAcpiPciTable[] =
{
  //
  // SMBUS Device (Bus 0, Dev 20, Func 0)
  //
  {0x00, SMBUS_BUS_DEV_FUN, 0},
  {FCH_CFG_REG10, 0X00, (FCH_VERSION_KUNLUN & 0xFF)},                ///Program the version information
  {FCH_CFG_REG11, 0X00, (FCH_VERSION_KUNLUN >> 8)},
  {0xFF, 0xFF, 0xFF},
};

/**
 * FchKunlunInitEnvRasPciTable
 *
 */
REG8_MASK FchKunlunInitEnvRasPciTable[] =
{
  {0x00, SMBUS_BUS_DEV_FUN, 0},           // SMBUS Device (Bus 0, Dev 20, Func 0)
  {FCH_CFG_REG04, 0xBF, BIT6},            // Command Register bit6
  {FCH_CFG_REG04 + 1, 0xFE, BIT0},        // Command Register bit8
  {0xFF, 0xFF, 0xFF},
  {0x00, LPC_BUS_DEV_FUN, 0},             // LPC Device (Bus 0, Dev 20, Func 3)
  {FCH_CFG_REG04, 0xBF, BIT6},            // Command Register bit6
  {FCH_CFG_REG04 + 1, 0xFE, BIT0},        // Command Register bit8
  {0xFF, 0xFF, 0xFF},
};

ACPI_REG_WRITE FchKunlunInitEnvRasMmioTable[] =
{
  {00, 00, 0xB0, 0xAC},
  {SMI_BASE  >> 8,  FCH_SMI_REGB4 + 1, 0xFC, BIT0},         // Enable SERR SMI
  {0xFF, 0xFF, 0xFF, 0xFF},
};

///
/// PCI_IRQ_REG_BLOCK- FCH PCI IRQ registers block
///
typedef struct _PCI_IRQ_REG_BLOCK {
  UINT8                PciIrqIndex;       // PciIrqIndex - selects which PCI interrupt to map
  UINT8                PciIrqData;        // PciIrqData  - Interrupt #
} PCI_IRQ_REG_BLOCK;

STATIC PCI_IRQ_REG_BLOCK FchInternalDeviceIrqForApicMode[] = {
    { (FCH_KL_IRQ_INTA | FCH_IRQ_IOAPIC), 0x1F},
    { (FCH_KL_IRQ_INTB | FCH_IRQ_IOAPIC), 0x1F},
    { (FCH_KL_IRQ_INTC | FCH_IRQ_IOAPIC), 0x1F},
    { (FCH_KL_IRQ_INTD | FCH_IRQ_IOAPIC), 0x1F},
    { (FCH_KL_IRQ_SD | FCH_IRQ_IOAPIC), 0x10},
    { (FCH_KL_IRQ_SDIO | FCH_IRQ_IOAPIC), 0x10},
    { (FCH_KL_IRQ_EMMC | FCH_IRQ_IOAPIC), 0x5},
    { (FCH_KL_IRQ_GPIO | FCH_IRQ_IOAPIC), 0x7},
    { (FCH_KL_IRQ_UART0 | FCH_IRQ_IOAPIC), 0x3},
    { (FCH_KL_IRQ_UART1 | FCH_IRQ_IOAPIC), 0x4},
    { (FCH_KL_IRQ_UART2 | FCH_IRQ_IOAPIC), 0x3},
    { (FCH_KL_IRQ_UART3 | FCH_IRQ_IOAPIC), 0x4},
    { (FCH_KL_IRQ_I2C0 | FCH_IRQ_IOAPIC), 0xa},
    { (FCH_KL_IRQ_I2C1 | FCH_IRQ_IOAPIC), 0xb},
    { (FCH_KL_IRQ_I2C2 | FCH_IRQ_IOAPIC), 0x4},
    { (FCH_KL_IRQ_I2C3 | FCH_IRQ_IOAPIC), 0x6},
    { (FCH_KL_IRQ_I2C4 | FCH_IRQ_IOAPIC), 0xe},
    { (FCH_KL_IRQ_I2C5 | FCH_IRQ_IOAPIC), 0xf},
  };

STATIC PCI_IRQ_REG_BLOCK FchInternalDeviceIrqForVWMode[] = {
    { (FCH_KL_IRQ_INTA | FCH_IRQ_IOAPIC), 0x10},
    { (FCH_KL_IRQ_INTB | FCH_IRQ_IOAPIC), 0x11},
    { (FCH_KL_IRQ_INTC | FCH_IRQ_IOAPIC), 0x12},
    { (FCH_KL_IRQ_INTD | FCH_IRQ_IOAPIC), 0x13},
    { (FCH_KL_IRQ_SD | FCH_IRQ_IOAPIC), 0x10},
    { (FCH_KL_IRQ_SDIO | FCH_IRQ_IOAPIC), 0x10},
    { (FCH_KL_IRQ_EMMC | FCH_IRQ_IOAPIC), 0x5},
    { (FCH_KL_IRQ_GPIO | FCH_IRQ_IOAPIC), 0x7},
    { (FCH_KL_IRQ_UART0 | FCH_IRQ_IOAPIC), 0x3},
    { (FCH_KL_IRQ_UART1 | FCH_IRQ_IOAPIC), 0x4},
    { (FCH_KL_IRQ_UART2 | FCH_IRQ_IOAPIC), 0x3},
    { (FCH_KL_IRQ_UART3 | FCH_IRQ_IOAPIC), 0x4},
    { (FCH_KL_IRQ_I2C0 | FCH_IRQ_IOAPIC), 0xa},
    { (FCH_KL_IRQ_I2C1 | FCH_IRQ_IOAPIC), 0xb},
    { (FCH_KL_IRQ_I2C2 | FCH_IRQ_IOAPIC), 0x4},
    { (FCH_KL_IRQ_I2C3 | FCH_IRQ_IOAPIC), 0x6},
    { (FCH_KL_IRQ_I2C4 | FCH_IRQ_IOAPIC), 0xe},
    { (FCH_KL_IRQ_I2C5 | FCH_IRQ_IOAPIC), 0xf},
  };
#define NUM_OF_DEVICE_FOR_APICIRQ  sizeof (FchInternalDeviceIrqForApicMode) / sizeof (PCI_IRQ_REG_BLOCK)
#define VW_SRCACTIVELOW  0xFFCEF8ul

#define FCH_ESPI_IO_RANGE_MAX 16

typedef struct {
  UINT32    IORangeEnableRegAddr;
  UINT32    IORangeEnableBit;
  UINT32    IORangeBaseRegAddr;
  UINT32    IORangeBaseOff;
  UINT32    IORangeSizeRegAddr;
  UINT32    IORangeSizeOff;
} FCH_ESPI_SLAVE0_IO_DECODE;

FCH_ESPI_SLAVE0_IO_DECODE mESPISlave0Decode[FCH_ESPI_IO_RANGE_MAX] = {
  // Io Range 0
  {
    FCH_REG40,        // Io Range Enable Register Address
    BIT8,             // Io Range Enable Bit
    FCH_REG44,        // Io Range Base Register Address
    0,                // Io Range Base Offset
    FCH_REG4C,        // Io Range Size Register Address
    0                 // Io Range Size Offset
  },

  // Io Range 1
  {
    FCH_REG40,        // Io Range Enable Register Address
    BIT9,             // Io Range Enable Bit
    FCH_REG44,        // Io Range Base Register Address
    16,               // Io Range Base Offset
    FCH_REG4C,        // Io Range Size Register Address
    8                 // Io Range Size Offset
  },

  // Io Range 2
  {
    FCH_REG40,        // Io Range Enable Register Address
    BIT10,            // Io Range Enable Bit
    FCH_REG48,        // Io Range Base Register Address
    0,                // Io Range Base Offset
    FCH_REG4C,        // Io Range Size Register Address
    16                // Io Range Size Offset
  },

  // Io Range 3
  {
    FCH_REG40,        // Io Range Enable Register Address
    BIT11,            // Io Range Enable Bit
    FCH_REG48,        // Io Range Base Register Address
    16,               // Io Range Base Offset
    FCH_REG4C,        // Io Range Size Register Address
    24                // Io Range Size Offset
  },

  // Io Range 4
  {
    FCH_REG40,        // Io Range Enable Register Address
    BIT16,            // Io Range Enable Bit
    FCH_REG80,        // Io Range Base Register Address
    0,                // Io Range Base Offset
    FCH_REG88,        // Io Range Size Register Address
    0                 // Io Range Size Offset
  },

  // Io Range 5
  {
    FCH_REG40,        // Io Range Enable Register Address
    BIT17,            // Io Range Enable Bit
    FCH_REG80,        // Io Range Base Register Address
    16,               // Io Range Base Offset
    FCH_REG88,        // Io Range Size Register Address
    8                 // Io Range Size Offset
  },

  // Io Range 6
  {
    FCH_REG40,        // Io Range Enable Register Address
    BIT18,            // Io Range Enable Bit
    FCH_REG84,        // Io Range Base Register Address
    0,                // Io Range Base Offset
    FCH_REG88,        // Io Range Size Register Address
    16                // Io Range Size Offset
  },

  // Io Range 7
  {
    FCH_REG40,        // Io Range Enable Register Address
    BIT19,            // Io Range Enable Bit
    FCH_REG84,        // Io Range Base Register Address
    16,               // Io Range Base Offset
    FCH_REG88,        // Io Range Size Register Address
    24                // Io Range Size Offset
  },

  // Io Range 8
  {
    FCH_REG40,        // Io Range Enable Register Address
    BIT20,            // Io Range Enable Bit
    FCH_REG8C,        // Io Range Base Register Address
    0,                // Io Range Base Offset
    FCH_REG94,        // Io Range Size Register Address
    0                 // Io Range Size Offset
  },

  // Io Range 9
  {
    FCH_REG40,        // Io Range Enable Register Address
    BIT21,            // Io Range Enable Bit
    FCH_REG8C,        // Io Range Base Register Address
    16,               // Io Range Base Offset
    FCH_REG94,        // Io Range Size Register Address
    8                 // Io Range Size Offset
  },

  // Io Range 10
  {
    FCH_REG40,        // Io Range Enable Register Address
    BIT22,            // Io Range Enable Bit
    FCH_REG90,        // Io Range Base Register Address
    0,                // Io Range Base Offset
    FCH_REG94,        // Io Range Size Register Address
    16                // Io Range Size Offset
  },

  // Io Range 11
  {
    FCH_REG40,        // Io Range Enable Register Address
    BIT23,            // Io Range Enable Bit
    FCH_REG90,        // Io Range Base Register Address
    16,               // Io Range Base Offset
    FCH_REG94,        // Io Range Size Register Address
    24                // Io Range Size Offset
  },

  // Io Range 12
  {
    FCH_REG40,        // Io Range Enable Register Address
    BIT24,            // Io Range Enable Bit
    FCH_REGB0,        // Io Range Base Register Address
    0,                // Io Range Base Offset
    FCH_REGB8,        // Io Range Size Register Address
    0                 // Io Range Size Offset
  },

  // Io Range 13
  {
    FCH_REG40,        // Io Range Enable Register Address
    BIT25,            // Io Range Enable Bit
    FCH_REGB0,        // Io Range Base Register Address
    16,               // Io Range Base Offset
    FCH_REGB8,        // Io Range Size Register Address
    8                 // Io Range Size Offset
  },

  // Io Range 14
  {
    FCH_REG40,        // Io Range Enable Register Address
    BIT26,            // Io Range Enable Bit
    FCH_REGB4,        // Io Range Base Register Address
    0,                // Io Range Base Offset
    FCH_REGB8,        // Io Range Size Register Address
    16                // Io Range Size Offset
  },

  // Io Range 15
  {
    FCH_REG40,        // Io Range Enable Register Address
    BIT27,            // Io Range Enable Bit
    FCH_REGB4,        // Io Range Base Register Address
    16,               // Io Range Base Offset
    FCH_REGB8,        // Io Range Size Register Address
    24                // Io Range Size Offset
  }
};


VOID
DisableESPILegacyUARTDecoding (
  IN  VOID     *FchDataPtr
  )
{
  FCH_DATA_BLOCK  *LocalCfgPtr;
  UINT32          i;
  UINT32          j;
  UINT32          AL2AHBIoEnableRange[4] = {0x2E8, 0x2F8, 0x3E8, 0x3F8};

  LocalCfgPtr     = (FCH_DATA_BLOCK *) FchDataPtr;
  i               = 0;
  j               = 0;

  IDS_HDT_CONSOLE (FCH_TRACE, "%a Start\n", __FUNCTION__);

  for (i = 0; i < FCH_ESPI_IO_RANGE_MAX; i++) {
    IDS_HDT_CONSOLE (FCH_TRACE, "IO Range %d\n", i);
    for (j = 0; j < sizeof (AL2AHBIoEnableRange)/sizeof (UINT32); j++) {
      if ( LocalCfgPtr->FchRunTime.Al2AhbLegacyUartIoEnable & (1<<j)
        && ACPIMMIO32 (FCH_ESPI0_BASE_ADDRESS + mESPISlave0Decode[i].IORangeEnableRegAddr) & mESPISlave0Decode[i].IORangeEnableBit
        && ((ACPIMMIO32 (FCH_ESPI0_BASE_ADDRESS + mESPISlave0Decode[i].IORangeBaseRegAddr) >> mESPISlave0Decode[i].IORangeBaseOff) & 0xFFFF) == AL2AHBIoEnableRange[j]
        )
      {
        IDS_HDT_CONSOLE (
          FCH_TRACE,
          "Disable decode 0x%x Bit 0x%x\n",
          FCH_ESPI0_BASE_ADDRESS + mESPISlave0Decode[i].IORangeBaseRegAddr,
          mESPISlave0Decode[i].IORangeEnableBit
          );
        ACPIMMIO32 (FCH_ESPI0_BASE_ADDRESS + mESPISlave0Decode[i].IORangeBaseRegAddr) &= ~mESPISlave0Decode[i].IORangeEnableBit;
      }
#if FCH_ESPI_COUNT > 1
      if ( LocalCfgPtr->FchRunTime.Al2AhbLegacyUartIoEnable & (1<<j)
        && ACPIMMIO32 (FCH_ESPI1_BASE_ADDRESS + mESPISlave0Decode[i].IORangeEnableRegAddr) & mESPISlave0Decode[i].IORangeEnableBit
        && ((ACPIMMIO32 (FCH_ESPI1_BASE_ADDRESS + mESPISlave0Decode[i].IORangeBaseRegAddr) >> mESPISlave0Decode[i].IORangeBaseOff) & 0xFFFF) == AL2AHBIoEnableRange[j]
        )
      {
        IDS_HDT_CONSOLE (
          FCH_TRACE,
          "Disable decode 0x%x Bit 0x%x\n",
          FCH_ESPI1_BASE_ADDRESS + mESPISlave0Decode[i].IORangeBaseRegAddr,
          mESPISlave0Decode[i].IORangeEnableBit
          );
        ACPIMMIO32 (FCH_ESPI1_BASE_ADDRESS + mESPISlave0Decode[i].IORangeBaseRegAddr) &= ~mESPISlave0Decode[i].IORangeEnableBit;
      }
#endif
    }
  }
}


VOID
ClearThermalTripSts (
  IN  VOID     *FchDataPtr
  )
{
  FCH_DATA_BLOCK         *LocalCfgPtr;
//  AMD_CONFIG_PARAMS      *StdHeader;

  LocalCfgPtr = (FCH_DATA_BLOCK *) FchDataPtr;
//  StdHeader = LocalCfgPtr->StdHeader;

  if (LocalCfgPtr->HwAcpi.NoClearThermalTripSts == FALSE) {
    RwMem (ACPI_MMIO_BASE + PMIO_BASE + FCH_PMIOA_REGC0, AccessWidth8, 0, BIT0);
  }
}

VOID
MapXhcWakeEvent (
  IN  VOID     *FchDataPtr
  )
{
//  FCH_DATA_BLOCK         *LocalCfgPtr;
//  AMD_CONFIG_PARAMS      *StdHeader;

//  LocalCfgPtr = (FCH_DATA_BLOCK *) FchDataPtr;
//  StdHeader = LocalCfgPtr->StdHeader;

  RwMem (ACPI_MMIO_BASE + SMI_BASE + FCH_SMI_KL_Xhc0Wake, AccessWidth8, 0xFFFFFF00, 0x0B);
  RwMem (ACPI_MMIO_BASE + SMI_BASE + FCH_SMI_KL_Xhc1Wake, AccessWidth8, 0xFFFFFF00, 0x0B);
}

/**
 * ProgramEnvPFchAcpiMmio - Config HwAcpi MMIO registers
 *   Acpi S3 resume would not execute this procedure (POST only)
 *
 * @param[in] FchDataPtr Fch configuration structure pointer.
 *
 */
VOID
ProgramEnvPFchAcpiMmio (
  IN  VOID     *FchDataPtr
  )
{
  FCH_DATA_BLOCK         *LocalCfgPtr;
  AMD_CONFIG_PARAMS      *StdHeader;

  LocalCfgPtr = (FCH_DATA_BLOCK *) FchDataPtr;
  StdHeader = LocalCfgPtr->StdHeader;

  ProgramFchAcpiMmioTbl ((ACPI_REG_WRITE*) (&FchKunlunInitEnvHwAcpiMmioTable[0]), StdHeader);
  MapXhcWakeEvent (FchDataPtr);
  ClearThermalTripSts (FchDataPtr);
}

/**
 * ProgramFchEnvHwAcpiPciReg - Config HwAcpi PCI controller
 * before PCI emulation
 *
 *
 *
 * @param[in] FchDataPtr Fch configuration structure pointer.
 *
 */
VOID
ProgramFchEnvHwAcpiPciReg (
  IN  VOID     *FchDataPtr
  )
{
  FCH_DATA_BLOCK         *LocalCfgPtr;
  AMD_CONFIG_PARAMS      *StdHeader;

  LocalCfgPtr = (FCH_DATA_BLOCK *) FchDataPtr;
  StdHeader = LocalCfgPtr->StdHeader;

  //
  //Early post initialization of pci config space
  //
  ProgramPciByteTable ((REG8_MASK*) (&FchKunlunInitEnvHwAcpiPciTable[0]), sizeof (FchKunlunInitEnvHwAcpiPciTable) / sizeof (REG8_MASK), StdHeader);

  if ( LocalCfgPtr->Smbus.SmbusSsid != NULL ) {
    RwPci ((SMBUS_BUS_DEV_FUN << 16) + FCH_CFG_REG2C, AccessWidth32, 0x00, LocalCfgPtr->Smbus.SmbusSsid, StdHeader);
  }
  if ( LocalCfgPtr->Misc.NoneSioKbcSupport ) {
    RwMem (ACPI_MMIO_BASE + PMIO_BASE + FCH_PMIOA_REGED, AccessWidth8, ~(UINT32) (BIT1), BIT1);
  } else {
    RwMem (ACPI_MMIO_BASE + PMIO_BASE + FCH_PMIOA_REGED, AccessWidth8, ~(UINT32) (BIT1), 0);
  }
  ProgramPcieNativeMode (FchDataPtr);
}

/**
 * FchVgaInit - Config VGA CODEC
 *
 * @param[in] VOID empty
 *
 */
VOID
FchVgaInit (
  OUT VOID
  )
{
}

/**
 * ProgramSpecificFchInitEnvAcpiMmio - Config HwAcpi MMIO before
 * PCI emulation
 *
 *
 *
 * @param[in] FchDataPtr Fch configuration structure pointer.
 *
 */
VOID
ProgramSpecificFchInitEnvAcpiMmio (
  IN  VOID     *FchDataPtr
  )
{
  FCH_DATA_BLOCK         *LocalCfgPtr;
  AMD_CONFIG_PARAMS      *StdHeader;

  LocalCfgPtr = (FCH_DATA_BLOCK *) FchDataPtr;
  StdHeader = LocalCfgPtr->StdHeader;
  ProgramFchAcpiMmioTbl ((ACPI_REG_WRITE*) (&FchKunlunInitEnvSpecificHwAcpiMmioTable[0]), StdHeader);

  //Kunlun removed in HW
  //
  // Set LDTSTP# duration
  //
  //RwMem (ACPI_MMIO_BASE + PMIO_BASE + FCH_PMIOA_REG94, AccessWidth8, 0, 0x01);
  //RwMem (ACPI_MMIO_BASE + PMIO_BASE + FCH_PMIOA_REG80 + 3, AccessWidth8, 0xFE, 0x20);
  //
  // LpcClk0DrivenZero & RTC Wake Mode Control
  //
  RwMem (ACPI_MMIO_BASE + MISC_BASE + FCH_MISC_REG6C + 2, AccessWidth8, 0xDF, 0x20);
  //DESSPIODRTL-353 (GPIO120:PD=0/PU=1)
  RwMem (ACPI_MMIO_BASE + GPIO_BANK1_BASE + FCH_REGE0, AccessWidth32, ~(UINT32) (BIT21 + BIT20), BIT20);
  //
  // Ac Loss Control
  //
  AcLossControl ((UINT8) LocalCfgPtr->HwAcpi.PwrFailShadow);
  //
  // FCH VGA Init
  //
  FchVgaInit ();

  //
  // Set ACPIMMIO by OEM Input table
  //
  ProgramFchAcpiMmioTbl ((ACPI_REG_WRITE *) (LocalCfgPtr->HwAcpi.OemProgrammingTablePtr), StdHeader);
}

/**
 * ValidateFchVariant - Validate FCH Variant
 *
 *
 *
 * @param[in] FchDataPtr
 *
 */
VOID
ValidateFchVariant (
  IN  VOID     *FchDataPtr
  )
{
}

/**
 * IsExternalClockMode - Is External Clock Mode?
 *
 *
 * @retval  TRUE or FALSE
 *
 */
BOOLEAN
IsExternalClockMode (
  IN  VOID     *FchDataPtr
  )
{
  UINT8    MISC80;
  ReadMem (ACPI_MMIO_BASE + MISC_BASE + FCH_MISC_REG80 + 2, AccessWidth8, &MISC80);
  return ( (BOOLEAN) ((MISC80 & BIT1) == 0) );
}


/**
 * ProgramFchEnvSpreadSpectrum - Config SpreadSpectrum before
 * PCI emulation
 *
 *
 *
 * @param[in] FchDataPtr Fch configuration structure pointer.
 *
 */
VOID
ProgramFchEnvSpreadSpectrum (
  IN  VOID     *FchDataPtr
  )
{
  UINT8        FchSpreadSpectrum;
  UINT32       FCH_MISC_CGPLLCONFIG1;

  FCH_DATA_BLOCK         *LocalCfgPtr;
  AMD_CONFIG_PARAMS      *StdHeader;

  IDS_HDT_CONSOLE (FCH_TRACE, "%a ent \n", __FUNCTION__);

  LocalCfgPtr           = (FCH_DATA_BLOCK *) FchDataPtr;
  StdHeader             = LocalCfgPtr->StdHeader;
  FchSpreadSpectrum     = LocalCfgPtr->HwAcpi.SpreadSpectrum;
  FCH_MISC_CGPLLCONFIG1 = 0;

  if ( IsExternalClockMode (FchDataPtr) ) {
    IDS_HDT_CONSOLE (FCH_TRACE, "disable SSC \n");
    FchSpreadSpectrum = 0;
  }
//  The SSC -0.3% enable sequence is: (replaced the default SSC enable sequence)
//.    Program MISCx14[23:8] FCH::MISC::CGPLLCONFIG4[cg1pll_fcw1_frac_override] == 0x1333
//.    Program MISCx18[31:16] FCH::MISC::CGPLLCONFIG5[cg1pll_fcw_slew_frac_override] == 0x3A
//.    Program MISCx10[29]=1 FCH::MISC::CGPLLCONFIG3[cg1pll_fracn_en_override]= 1.
//.    Program MISCx08[0]=1  FCH::MISC::CGPLLCONFIG1[cg1_spread_spectrum_enable] = 1
//.    Program MISCx40[25]=1   FCH::MISC::MISCCLKCNTRL0[cg1_fbdiv_loaden] = 1.
//.    Program MISCx40[30]=1 FCH::MISC::MISCCLKCNTRL0[cg1_cfg_update_req] = 1.
  ReadMem (ACPI_MMIO_BASE + MISC_BASE + FCH_MISC_REG08, AccessWidth32, &FCH_MISC_CGPLLCONFIG1);
  IDS_HDT_CONSOLE (FCH_TRACE, "FCH::MISC::CGPLLCONFIG1: 0x%x\n", FCH_MISC_CGPLLCONFIG1);

  if ( FchSpreadSpectrum ) {
    if ( FCH_MISC_CGPLLCONFIG1 & BIT0 ) {
      IDS_HDT_CONSOLE (FCH_TRACE, "SSC is already enabled\n");
    } else {
      IDS_HDT_CONSOLE (FCH_TRACE, "Enable SSC\n");
      FchStall (5, StdHeader);
      RwMem (ACPI_MMIO_BASE + MISC_BASE + FCH_MISC_REG14, AccessWidth32, ~(UINT32) 0xFFFF00,   (UINT32) LShiftU64(0x1333, 8)  );
      RwMem (ACPI_MMIO_BASE + MISC_BASE + FCH_MISC_REG18, AccessWidth32, ~(UINT32) 0xFFFF0000, (UINT32) LShiftU64(0x3A, 16)   );
      RwMem (ACPI_MMIO_BASE + MISC_BASE + FCH_MISC_REG10, AccessWidth32, ~(UINT32) BIT29, BIT29 );
      RwMem (ACPI_MMIO_BASE + MISC_BASE + FCH_MISC_REG08, AccessWidth32, ~(UINT32) BIT0,  BIT0  );
      RwMem (ACPI_MMIO_BASE + MISC_BASE + FCH_MISC_REG40, AccessWidth32, ~(UINT32) BIT25, BIT25 );
      RwMem (ACPI_MMIO_BASE + MISC_BASE + FCH_MISC_REG48, AccessWidth32, ~(UINT32) BIT11, BIT11 );
      RwMem (ACPI_MMIO_BASE + MISC_BASE + FCH_MISC_REG40, AccessWidth32, ~(UINT32) BIT30, BIT30 );
      FchStall (15, StdHeader);
    }
  } else {
    if ( FCH_MISC_CGPLLCONFIG1 & BIT0 ) {
      FchStall (5, StdHeader);
      IDS_HDT_CONSOLE (FCH_TRACE, "Disable SSC\n");
      RwMem (ACPI_MMIO_BASE + MISC_BASE + FCH_MISC_REG10, AccessWidth32, ~(UINT32) BIT29, BIT29);
      RwMem (ACPI_MMIO_BASE + MISC_BASE + FCH_MISC_REG08, AccessWidth32, ~(UINT32) BIT0,  0x00 );
      RwMem (ACPI_MMIO_BASE + MISC_BASE + FCH_MISC_REG48, AccessWidth32, ~(UINT32) BIT11, BIT11);
      RwMem (ACPI_MMIO_BASE + MISC_BASE + FCH_MISC_REG40, AccessWidth32, ~(UINT32) BIT30, BIT30);
      FchStall (15, StdHeader);
    } else {
      IDS_HDT_CONSOLE (FCH_TRACE, "SSC is already disabled\n");
    }
  }

  IDS_HDT_CONSOLE (FCH_TRACE, "%a exit \n", __FUNCTION__);
}

/**
 * TurnOffCG2
 *
 *
 * @retval  VOID
 *
 */
VOID
TurnOffCG2 (
  OUT VOID
  )
{
}

/**
 * BackUpCG2
 *
 *
 * @retval  VOID
 *
 */
VOID
BackUpCG2 (
  OUT VOID
  )
{
}

/**
 * HpetInit - Program Fch HPET function
 *
 *
 *
 * @param[in] FchDataPtr         Fch configuration structure pointer.
 *
 */
VOID
HpetInit (
  IN  VOID     *FchDataPtr
  )
{
  BOOLEAN              FchHpetTimer;
  BOOLEAN              FchHpetMsiDis;
  FCH_DATA_BLOCK         *LocalCfgPtr;

  LocalCfgPtr = (FCH_DATA_BLOCK *) FchDataPtr;
  FchHpetTimer = LocalCfgPtr->Hpet.HpetEnable;
  FchHpetMsiDis = LocalCfgPtr->Hpet.HpetMsiDis;

  if ( FchHpetTimer == TRUE ) {
    //
    //Enabling decoding of HPET MMIOProgram the HPET BAR address
    //
    RwMem (ACPI_MMIO_BASE + PMIO_BASE + FCH_PMIOA_REG00, AccessWidth8, 0xFF, BIT6);

    //
    //Enable HPET MSI support
    //
    if ( FchHpetMsiDis == FALSE ) {
      RwMem (ACPI_MMIO_BASE + PMIO_BASE + FCH_PMIOA_REG00, AccessWidth32, ~(UINT32) BIT29, BIT29);
#ifdef FCH_TIMER_TICK_INTERVAL_WA
      RwMem (ACPI_MMIO_BASE + PMIO_BASE + FCH_PMIOA_REG00, AccessWidth32, ~(UINT32) BIT29, 0);
#endif
    } else {
      RwMem (ACPI_MMIO_BASE + PMIO_BASE + FCH_PMIOA_REG00, AccessWidth32, ~(UINT32) BIT29, 0);
    }

  }
}

/**
 * ProgramPcieNativeMode - Config Pcie Native Mode
 *
 *
 *
 * @param[in] FchDataPtr Fch configuration structure pointer.
 *
 */
VOID
ProgramPcieNativeMode (
  IN  VOID     *FchDataPtr
  )
{
  UINT8        FchNativepciesupport;
  FCH_DATA_BLOCK         *LocalCfgPtr;

  LocalCfgPtr = (FCH_DATA_BLOCK *) FchDataPtr;
  FchNativepciesupport = (UINT8) LocalCfgPtr->Misc.NativePcieSupport;

  //
  // PCIE Native setting
  //
  RwMem (ACPI_MMIO_BASE + PMIO_BASE + FCH_PMIOA_REGBA + 1, AccessWidth8, ~(UINT32) BIT6, 0);
  if ( FchNativepciesupport == 1) {
    RwMem (ACPI_MMIO_BASE + PMIO_BASE + FCH_PMIOA_REG74 + 3, AccessWidth8, ~(UINT32) (BIT3 + BIT1 + BIT0), BIT3 + BIT0);
  } else {
    RwMem (ACPI_MMIO_BASE + PMIO_BASE + FCH_PMIOA_REG74 + 3, AccessWidth8, ~(UINT32) (BIT3 + BIT1 + BIT0), BIT3);
  }
}

/**
 * PciIntVwInit - Config Pci Interrupt routing for VW mode
 *
 *
 *
 * @param[in] FchDataPtr Fch configuration structure pointer.
 *
 */
VOID
PciIntVwInit (
  IN  VOID     *FchDataPtr
  )
{
  FCH_DATA_BLOCK         *LocalCfgPtr;
  AMD_CONFIG_PARAMS      *StdHeader;
  UINT8                  i;

  LocalCfgPtr = (FCH_DATA_BLOCK *) FchDataPtr;
  StdHeader = LocalCfgPtr->StdHeader;

  for (i = 0; i < NUM_OF_DEVICE_FOR_APICIRQ; i++) {
    LibAmdIoWrite (AccessWidth8, FCH_IOMAP_REGC00, &FchInternalDeviceIrqForVWMode[i].PciIrqIndex, StdHeader);
    LibAmdIoWrite (AccessWidth8, FCH_IOMAP_REGC01, &FchInternalDeviceIrqForVWMode[i].PciIrqData, StdHeader);
  }

  RwMem (ACPI_MMIO_BASE + PMIO_BASE + FCH_PMIOA_REGA8, AccessWidth32, 0, BIT31 + VW_SRCACTIVELOW );

}

VOID
FchModifyDeviceIrq (
    IN  VOID     *FchDataPtr
    )
{
  FCH_DATA_BLOCK         *LocalCfgPtr;
  UINT8                  i;

  LocalCfgPtr = (FCH_DATA_BLOCK *) FchDataPtr;
  for (i = 0; i < NUM_OF_DEVICE_FOR_APICIRQ; i++) {
    switch (FchInternalDeviceIrqForApicMode[i].PciIrqIndex){
      case (FCH_KL_IRQ_UART0 | FCH_IRQ_IOAPIC):
        FchInternalDeviceIrqForApicMode[i].PciIrqData = LocalCfgPtr->FchRunTime.Uart0Irq;
        break;
      case (FCH_KL_IRQ_UART1 | FCH_IRQ_IOAPIC):
        FchInternalDeviceIrqForApicMode[i].PciIrqData = LocalCfgPtr->FchRunTime.Uart1Irq;
        break;
      case (FCH_KL_IRQ_UART2 | FCH_IRQ_IOAPIC):
        FchInternalDeviceIrqForApicMode[i].PciIrqData = LocalCfgPtr->FchRunTime.Uart2Irq;
        break;
      case (FCH_KL_IRQ_UART3 | FCH_IRQ_IOAPIC):
        FchInternalDeviceIrqForApicMode[i].PciIrqData = LocalCfgPtr->FchRunTime.Uart3Irq;
        break;
      case (FCH_KL_IRQ_I2C0 | FCH_IRQ_IOAPIC):
        FchInternalDeviceIrqForApicMode[i].PciIrqData = LocalCfgPtr->FchRunTime.I2c0Irq;
        break;
      case (FCH_KL_IRQ_I2C1 | FCH_IRQ_IOAPIC):
        FchInternalDeviceIrqForApicMode[i].PciIrqData = LocalCfgPtr->FchRunTime.I2c1Irq;
        break;
      case (FCH_KL_IRQ_I2C2 | FCH_IRQ_IOAPIC):
        FchInternalDeviceIrqForApicMode[i].PciIrqData = LocalCfgPtr->FchRunTime.I2c2Irq;
        break;
      case (FCH_KL_IRQ_I2C3 | FCH_IRQ_IOAPIC):
        FchInternalDeviceIrqForApicMode[i].PciIrqData = LocalCfgPtr->FchRunTime.I2c3Irq;
        break;
      case (FCH_KL_IRQ_I2C4 | FCH_IRQ_IOAPIC):
        FchInternalDeviceIrqForApicMode[i].PciIrqData = LocalCfgPtr->FchRunTime.I2c4Irq;
        break;
      case (FCH_KL_IRQ_I2C5 | FCH_IRQ_IOAPIC):
        FchInternalDeviceIrqForApicMode[i].PciIrqData = LocalCfgPtr->FchRunTime.I2c5Irq;
        break;
      default:break;
    }
  }
}

/**
 * FchInternalDeviceIrqInit - Config Fch internal Device Interrupt routing
 *
 *
 *
 * @param[in] FchDataPtr Fch configuration structure pointer.
 *
 */
VOID
FchInternalDeviceIrqInit (
  IN  VOID     *FchDataPtr
  )
{
  FCH_DATA_BLOCK         *LocalCfgPtr;
  AMD_CONFIG_PARAMS      *StdHeader;
  UINT8                  i, Data;

  LocalCfgPtr = (FCH_DATA_BLOCK *) FchDataPtr;
  StdHeader = LocalCfgPtr->StdHeader;

  FchModifyDeviceIrq (FchDataPtr);

  for (i = 0; i < NUM_OF_DEVICE_FOR_APICIRQ; i++) {
    LibAmdIoWrite (AccessWidth8, FCH_IOMAP_REGC00, &FchInternalDeviceIrqForApicMode[i].PciIrqIndex, StdHeader);
    LibAmdIoWrite (AccessWidth8, FCH_IOMAP_REGC01, &FchInternalDeviceIrqForApicMode[i].PciIrqData, StdHeader);
    if(FchInternalDeviceIrqForApicMode[i].PciIrqData == 0xe){
      Data = 0x08;
      LibAmdIoWrite (AccessWidth8, FCH_IOMAP_REGC00, &Data, StdHeader);
      LibAmdIoRead(AccessWidth8, FCH_IOMAP_REGC01, &Data , StdHeader);
      Data |= 0x03<<4;
      LibAmdIoWrite (AccessWidth8, FCH_IOMAP_REGC01, &Data , StdHeader);
    }
    if(FchInternalDeviceIrqForApicMode[i].PciIrqData == 0xf){
      Data = 0x08;
      LibAmdIoWrite (AccessWidth8, FCH_IOMAP_REGC00, &Data, StdHeader);
      LibAmdIoRead(AccessWidth8, FCH_IOMAP_REGC01, &Data , StdHeader);
      Data |= 0x03<<6;
      LibAmdIoWrite (AccessWidth8, FCH_IOMAP_REGC01, &Data , StdHeader);
    }
  }
}

/**
 * FchAl2ahbInit - Config Fch AL2AHB init
 *
 *
 *
 * @param[in] FchDataPtr Fch configuration structure pointer.
 *
 */
VOID
FchAl2ahbInit (
  IN  VOID     *FchDataPtr
  )
{
//  FCH_DATA_BLOCK         *LocalCfgPtr;
//  AMD_CONFIG_PARAMS      *StdHeader;

//  LocalCfgPtr = (FCH_DATA_BLOCK *) FchDataPtr;
//  StdHeader = LocalCfgPtr->StdHeader;

  //
  // Enable Clock Gating
  //
  RwMem (FCH_AL2AHB_BASE + 0x10, AccessWidth8, 0xFF, BIT1);
  RwMem (FCH_AL2AHB_BASE + 0x30, AccessWidth8, 0xFF, BIT1);
}


/**
 * FchI2cUartInit - Config Fch AMBA I2C Uart init
 *
 *
 *
 * @param[in] FchDataPtr Fch configuration structure pointer.
 *
 */
VOID
FchI2cUartInit (
  IN  VOID     *FchDataPtr
  )
{
  FCH_DATA_BLOCK          *LocalCfgPtr;
  AMD_CONFIG_PARAMS       *StdHeader;
  UINT32                  FchDeviceEnMap;
  UINT32                  UartLegacyClockSelect;

  UartLegacyClockSelect   = 0;
  LocalCfgPtr             = (FCH_DATA_BLOCK *) FchDataPtr;
  StdHeader               = LocalCfgPtr->StdHeader;
  FchDeviceEnMap          = LocalCfgPtr->FchRunTime.FchDeviceEnableMap;

  IDS_HDT_CONSOLE (FCH_TRACE, "%a Start\n", __FUNCTION__);

  IDS_HDT_CONSOLE (FCH_TRACE, " FchDeviceEnMap = 0x%x\n", FchDeviceEnMap);

  //
  // UART0
  //
  if ( FchDeviceEnMap & BIT11 ) {
    FchAoacPowerOnDev (FCH_AOAC_UART0, 1);
    // IOMUX
    RwMem (ACPI_MMIO_BASE + IOMUX_BASE + 0x87, AccessWidth8, 0, 0x0);
    RwMem (ACPI_MMIO_BASE + IOMUX_BASE + 0x88, AccessWidth8, 0, 0x0);
    RwMem (ACPI_MMIO_BASE + IOMUX_BASE + 0x89, AccessWidth8, 0, 0x0);
    RwMem (ACPI_MMIO_BASE + IOMUX_BASE + 0x8A, AccessWidth8, 0, 0x0);
    RwMem (ACPI_MMIO_BASE + IOMUX_BASE + 0x8B, AccessWidth8, 0, 0x0);

    ACPIMMIO32 (ACPI_MMIO_BASE + GPIO_BANK0_BASE + FCH_GPIO_21C_UART0_CTS_L_UART2_RXD_EGPIO135) &= ~ (FCH_GPIO_OUTPUT_ENABLE << 16);
    ACPIMMIO32 (ACPI_MMIO_BASE + GPIO_BANK0_BASE + FCH_GPIO_220_UART0_RXD_EGPIO136) &= ~ (FCH_GPIO_OUTPUT_ENABLE << 16);
    ACPIMMIO32 (ACPI_MMIO_BASE + GPIO_BANK0_BASE + FCH_GPIO_224_UART0_RTS_L_UART2_TXD_EGPIO137) &= ~ (FCH_GPIO_OUTPUT_ENABLE << 16);
    ACPIMMIO32 (ACPI_MMIO_BASE + GPIO_BANK0_BASE + FCH_GPIO_228_UART0_TXD_EGPIO138) &= ~ (FCH_GPIO_OUTPUT_ENABLE << 16);
    ACPIMMIO32 (ACPI_MMIO_BASE + GPIO_BANK0_BASE + FCH_GPIO_22C_UART0_INTR_AGPIO139) &= ~ (FCH_GPIO_OUTPUT_ENABLE << 16);
  } else {
    //FchAoacPowerOnDev (FCH_AOAC_UART0, 0);
  }

  //
  // UART1
  //
  if ( FchDeviceEnMap & BIT12 ) {
    FchAoacPowerOnDev (FCH_AOAC_UART1, 1);
    // IOMUX
    //RwMem (ACPI_MMIO_BASE + IOMUX_BASE + 0x8C, AccessWidth8, 0, 0x0);
    RwMem (ACPI_MMIO_BASE + IOMUX_BASE + 0x8D, AccessWidth8, 0, 0x0);
    RwMem (ACPI_MMIO_BASE + IOMUX_BASE + 0x8E, AccessWidth8, 0, 0x0);
    //RwMem (ACPI_MMIO_BASE + IOMUX_BASE + 0x8F, AccessWidth8, 0, 0x0);
    //RwMem (ACPI_MMIO_BASE + IOMUX_BASE + 0x90, AccessWidth8, 0, 0x0);

    //ACPIMMIO32 (ACPI_MMIO_BASE + GPIO_BANK0_BASE + FCH_GPIO_230_UART1_CTS_L_UART3_TXD_EGPIO140) &= ~ (FCH_GPIO_OUTPUT_ENABLE << 16);
    ACPIMMIO32 (ACPI_MMIO_BASE + GPIO_BANK0_BASE + FCH_GPIO_234_UART1_RXD_EGPIO141) &= ~ (FCH_GPIO_OUTPUT_ENABLE << 16);
    ACPIMMIO32 (ACPI_MMIO_BASE + GPIO_BANK0_BASE + FCH_GPIO_238_UART1_RTS_L_UART3_RXD_EGPIO142) &= ~ (FCH_GPIO_OUTPUT_ENABLE << 16);
    //ACPIMMIO32 (ACPI_MMIO_BASE + GPIO_BANK0_BASE + FCH_GPIO_23C_UART1_TXD_EGPIO143) &= ~ (FCH_GPIO_OUTPUT_ENABLE << 16);
    //ACPIMMIO32 (ACPI_MMIO_BASE + GPIO_BANK0_BASE + FCH_GPIO_240_UART1_INTR_AGPIO144) &= ~ (FCH_GPIO_OUTPUT_ENABLE << 16);
  } else {
    //FchAoacPowerOnDev (FCH_AOAC_UART1, 0);
  }

  //
  // UART2
  //
  if ( FchDeviceEnMap & BIT16 ) {
    FchAoacPowerOnDev (FCH_AOAC_UART2, 1);
    // IOMUX
    RwMem (ACPI_MMIO_BASE + IOMUX_BASE + 0x87, AccessWidth8, 0, 0x1);
    RwMem (ACPI_MMIO_BASE + IOMUX_BASE + 0x89, AccessWidth8, 0, 0x1);

    ACPIMMIO32 (ACPI_MMIO_BASE + GPIO_BANK0_BASE + FCH_GPIO_21C_UART0_CTS_L_UART2_RXD_EGPIO135) &= ~ (FCH_GPIO_OUTPUT_ENABLE << 16);
    ACPIMMIO32 (ACPI_MMIO_BASE + GPIO_BANK0_BASE + FCH_GPIO_224_UART0_RTS_L_UART2_TXD_EGPIO137) &= ~ (FCH_GPIO_OUTPUT_ENABLE << 16);
  } else {
    //FchAoacPowerOnDev (FCH_AOAC_UART2, 0);
  }

  /*
  //
  // UART3
  //
  if ( FchDeviceEnMap & BIT26 ) {
    FchAoacPowerOnDev (FCH_AOAC_UART3, 1);
    // IOMUX
    RwMem (ACPI_MMIO_BASE + IOMUX_BASE + 0x8C, AccessWidth8, 0, 0x1);
    RwMem (ACPI_MMIO_BASE + IOMUX_BASE + 0x8E, AccessWidth8, 0, 0x1);

    ACPIMMIO32 (ACPI_MMIO_BASE + GPIO_BANK0_BASE + FCH_GPIO_230_UART1_CTS_L_UART3_TXD_EGPIO140) &= ~ (FCH_GPIO_OUTPUT_ENABLE << 16);
    ACPIMMIO32 (ACPI_MMIO_BASE + GPIO_BANK0_BASE + FCH_GPIO_238_UART1_RTS_L_UART3_RXD_EGPIO142) &= ~ (FCH_GPIO_OUTPUT_ENABLE << 16);
  } else {
    //FchAoacPowerOnDev (FCH_AOAC_UART3, 0);
  }
  */

  //
  // PSP_INTR0
  //
  // IOMUX
  RwMem (ACPI_MMIO_BASE + IOMUX_BASE + 0x59, AccessWidth8, 0, 0x1);

  // UART Legacy IO Enable Support
  IDS_HDT_CONSOLE (FCH_TRACE, "FchI2cUartInit-Al2AhbLegacyUartIoEnable = 0x%X\n", LocalCfgPtr->FchRunTime.Al2AhbLegacyUartIoEnable);
  if ( LocalCfgPtr->FchRunTime.Al2AhbLegacyUartIoEnable ) {
    DisableESPILegacyUARTDecoding (FchDataPtr);
    ACPIMMIO16 (FCH_AL2AHBx20_LEGACY_UART_IO_ENABLE) = LocalCfgPtr->FchRunTime.Al2AhbLegacyUartIoEnable;
    if ( LocalCfgPtr->FchRunTime.Al2AhbLegacyUartIoEnable & BIT0 ) {
      UartLegacyClockSelect |= (UINT32) (1 << ((LocalCfgPtr->FchRunTime.Al2AhbLegacyUartIoEnable >> 8) & 3));
    }
    if ( LocalCfgPtr->FchRunTime.Al2AhbLegacyUartIoEnable & BIT1 ) {
      UartLegacyClockSelect |= (UINT32) (1 << ((LocalCfgPtr->FchRunTime.Al2AhbLegacyUartIoEnable >> 10) & 3));
    }
    if ( LocalCfgPtr->FchRunTime.Al2AhbLegacyUartIoEnable & BIT2 ) {
      UartLegacyClockSelect |= (UINT32) (1 << ((LocalCfgPtr->FchRunTime.Al2AhbLegacyUartIoEnable >> 12) & 3));
    }
    if ( LocalCfgPtr->FchRunTime.Al2AhbLegacyUartIoEnable & BIT3 ) {
      UartLegacyClockSelect |= (UINT32) (1 << ((LocalCfgPtr->FchRunTime.Al2AhbLegacyUartIoEnable >> 14) & 3));
    }
    RwPci ((SMBUS_BUS_DEV_FUN << 16) + FCH_CFG_REGFC, AccessWidth32, ~(UINT32)(BIT31 + BIT30 + BIT29 + BIT28), UartLegacyClockSelect << 28, StdHeader);
  }

  IDS_HDT_CONSOLE (FCH_TRACE, "%a End\n", __FUNCTION__);
}

/**
 * FchAlinkRasEnable - Enable FCH A-Link parity error
 *
 *
 *
 * @param[in] FchDataPtr Fch configuration structure pointer.
 *
 */
VOID
FchAlinkRasEnable (
  IN  VOID     *FchDataPtr
  )
{
  FCH_DATA_BLOCK         *LocalCfgPtr;
  AMD_CONFIG_PARAMS      *StdHeader;

  LocalCfgPtr = (FCH_DATA_BLOCK *) FchDataPtr;
  StdHeader = LocalCfgPtr->StdHeader;

  if ( LocalCfgPtr->HwAcpi.FchAlinkRasSupport ) {
    RwAlink (FCH_ABCFG_REG10050 | (UINT32) (ABCFG << 29), ~(UINT32) BIT3, BIT3, StdHeader);
    ProgramPciByteTable ((REG8_MASK*) (&FchKunlunInitEnvRasPciTable[0]), sizeof (FchKunlunInitEnvRasPciTable) / sizeof (REG8_MASK), StdHeader);
    ProgramFchAcpiMmioTbl ((ACPI_REG_WRITE*) (&FchKunlunInitEnvRasMmioTable[0]), StdHeader);
  } else {
    RwAlink (FCH_ABCFG_REG10050 | (UINT32) (ABCFG << 29), ~(UINT32) BIT3, 0, StdHeader);
  }
}


/**
 * ProgramEnvPFchiLa1MTraceMemoryEn - Config iLa 1M Trace Memory
 *   registers Acpi S3 resume would not execute this procedure (POST
 *   only)
 *
 * @param[in] FchDataPtr Fch configuration structure pointer.
 *
 */
VOID
ProgramEnvPFchiLa1MTraceMemoryEn (
  IN  VOID     *FchDataPtr
  )
{
  FCH_DATA_BLOCK                         *LocalCfgPtr;
//  AMD_CONFIG_PARAMS                       *StdHeader;
  FABRIC_TARGET                           MmioTarget = {0};
  FABRIC_MMIO_ATTRIBUTE                   Attributes;
  UINT64                                  MmioBase, Length;
  AMD_FABRIC_TOPOLOGY_SERVICES2_PROTOCOL  *FabricTopology;
  ROOT_BRIDGE_LOCATION                    FchLocation;
  UINT16                                  FchSocket, FchRootBridge;
  EFI_STATUS                              Status;

  LocalCfgPtr = (FCH_DATA_BLOCK *) FchDataPtr;
//  StdHeader = LocalCfgPtr->StdHeader;

  if (LocalCfgPtr->Misc.FchiLa1MTraceMemoryEn) {
    IDS_HDT_CONSOLE (FCH_TRACE, "ProgramEnvPFchiLa1MTraceMemoryEn is started!\n");
    Status = gBS->LocateProtocol (&gAmdFabricTopologyServices2ProtocolGuid, NULL, (VOID**) &FabricTopology);
    if (Status == EFI_SUCCESS && FabricTopology->GetSystemInfo (FabricTopology, NULL, NULL, NULL, &FchLocation, NULL) == EFI_SUCCESS) {
      FchSocket = (UINT16) FchLocation.Socket;
      FchRootBridge = (UINT16) FchLocation.Index;
    } else {
      FchSocket = 0;
      FchRootBridge = 0;
      IDS_HDT_CONSOLE(FCH_TRACE, "OEM-DXE-AllocateiLaTraceMemoryEnMmio- unable to get FCH location, use default value.\n");
    }
    Length = 0x100000;
    MmioTarget.TgtType = TARGET_RB;
    MmioTarget.SocketNum = FchSocket;
    MmioTarget.RbNum =FchRootBridge;
    Attributes.ReadEnable = 1;
    Attributes.WriteEnable = 1;
    Attributes.NonPosted = 0;
    Attributes.MmioType = NON_PCI_DEVICE_BELOW_4G;
    MmioBase  = 0;
    FabricAllocateMmio (&MmioBase, &Length, ALIGN_1M, MmioTarget, &Attributes);
    IDS_HDT_CONSOLE (FCH_TRACE, "iLa1MTraceMemoryBase=0x%x\n",MmioBase);
    IDS_HDT_CONSOLE (FCH_TRACE, "ProgramEnvPFchiLa1MTraceMemoryEn is completed!\n");
    LocalCfgPtr->Misc.FchiLa1MTraceMemoryBase = (UINT32) (MmioBase);
  }
}

/**
 * ProgramFchEnvAoacInit - AOAC configuration
 *
 *
 *
 * @param[in] FchDataPtr Fch configuration structure pointer.
 *
 */
VOID
ProgramFchEnvAoacInit (
  IN  VOID     *FchDataPtr
  )
{
  FCH_DATA_BLOCK         *LocalCfgPtr;

  LocalCfgPtr = (FCH_DATA_BLOCK *) FchDataPtr;

  if (LocalCfgPtr->HwAcpi.FchAoacProgramEnable) {
    //
    //Program AOAC shadow SRAM to allocate space for each of the controllers
    //
    RwMem (ACPI_MMIO_BASE + AOAC_BASE + 0x94, AccessWidth16, 0xFF0FFFFF, 0x00B00000);
    //AB
    RwMem (ACPI_MMIO_BASE + AOAC_BASE + 0x88, AccessWidth16, 0, 0x0001);
    RwMem (ACPI_MMIO_BASE + AOAC_BASE + 0x8C, AccessWidth32, 0, 0x39260080);

    //ACPISMBUS
    RwMem (ACPI_MMIO_BASE + AOAC_BASE + 0x88, AccessWidth16, 0, 0x0002);
    RwMem (ACPI_MMIO_BASE + AOAC_BASE + 0x8C, AccessWidth32, 0, 0x86550100);

    //LPC
    RwMem (ACPI_MMIO_BASE + AOAC_BASE + 0x88, AccessWidth16, 0, 0x0004);
    RwMem (ACPI_MMIO_BASE + AOAC_BASE + 0x8C, AccessWidth32, 0, 0x6E470200);

    //ESPI
    RwMem (ACPI_MMIO_BASE + AOAC_BASE + 0x88, AccessWidth16, 0, 0x0016);
    RwMem (ACPI_MMIO_BASE + AOAC_BASE + 0x8C, AccessWidth32, 0, 0x36320300);
    RwMem (ACPI_MMIO_BASE + AOAC_BASE + 0x88, AccessWidth16, 0, 0x001B);
    RwMem (ACPI_MMIO_BASE + AOAC_BASE + 0x8C, AccessWidth32, 0, 0x363201C0);

    RwMem (ACPI_MMIO_BASE + AOAC_BASE + 0x90, AccessWidth32, 0, 0x00000016);

    if ( ACPIMMIO8 (ACPI_MMIO_BASE + AOAC_BASE + 0x40 + (FCH_AOAC_ESPI << 1)) & BIT3  ) { //check if ESPI is on
      RwMem (ACPI_MMIO_BASE + AOAC_BASE + 0x90, AccessWidth32, ~ (UINT32) (1 << 27) , (UINT32) (1 << 27));
    }

    if ( ACPIMMIO8 (ACPI_MMIO_BASE + AOAC_BASE + 0x40 + (FCH_AOAC_ESPI1 << 1)) & BIT3  ) { //check if ESPI is on
      RwMem (ACPI_MMIO_BASE + AOAC_BASE + 0x90, AccessWidth32, ~ (UINT32) (1 << 22) , (UINT32) (1 << 22));
    }

    //Set these two bits to enable store/restore of HPET and ACPI PM timer automatically.
    RwMem (ACPI_MMIO_BASE + AOAC_BASE + 0x9C, AccessWidth32, 0, BIT0 + BIT1);

    //Start Shadow Timer
    //ShdwSysCtrl.ShdwSysCntRun = 1
    //ShdwSysCtrl.Cnt48M100Run = 1
    RwMem (ACPI_MMIO_BASE + 0x1100 + 0x10, AccessWidth32, 0, BIT0 + BIT3);
  }
}

