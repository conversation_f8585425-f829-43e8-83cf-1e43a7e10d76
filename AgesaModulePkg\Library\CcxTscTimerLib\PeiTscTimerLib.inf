#;*****************************************************************************
#;
#; Copyright (C) 2008-2024 Advanced Micro Devices, Inc. All rights reserved.
#;
#;******************************************************************************

# This file includes code originally published under the following license.

## @file
#  Pei Timer Library which uses the Time Stamp Counter in the processor.
#
#  Note: This library is a sample implementation that depends on chipset ACPI timer.
#    It may not work on new generation chipset. PcAtChipsetPkg AcpiTimerLib is
#    the generic timer library that can replace this one.
#
#  A version of the Timer Library using the processor's TSC.
#  The time stamp counter in newer processors may support an enhancement, referred to as invariant TSC.
#  The invariant TSC runs at a constant rate in all ACPI P-, C-. and T-states.
#  This is the architectural behavior moving forward.
#  TSC reads are much more efficient and do not incur the overhead associated with a ring transition or
#  access to a platform resource.
#
#  Copyright (c) 2009 - 2015, Intel Corporation. All rights reserved.<BR>
#  This program and the accompanying materials
#  are licensed and made available under the terms and conditions of the BSD License
#  which accompanies this distribution. The full text of the license may be found at
#  http://opensource.org/licenses/bsd-license.php
#  THE PROGRAM IS DISTRIBUTED UNDER THE BSD LICENSE ON AN "AS IS" BASIS,
#  WITHOUT WARRANTIES OR REPRESENTATIONS OF ANY KIND, EITHER EXPRESS OR IMPLIED.
#
##

[Defines]
  INF_VERSION                    = 0x00010005
  BASE_NAME                      = PeiTscTimerLib
  FILE_GUID                      = 342C36C0-15DF-43b4-9EC9-FBF748BFB3D1
  MODULE_TYPE                    = PEIM
  VERSION_STRING                 = 1.0
  LIBRARY_CLASS                  = TimerLib|PEIM PEI_CORE

#
#  VALID_ARCHITECTURES           = IA32 X64
#

[Sources.common]
  TscTimerLibShare.c
  PeiTscTimerLib.c
  TscTimerLibPrivate.h

[Packages]
  MdePkg/MdePkg.dec
  AgesaModulePkg/AgesaCommonModulePkg.dec
  AgesaModulePkg/AgesaModuleCcxPkg.dec
  AgesaPkg/AgesaPkg.dec


[LibraryClasses]
  BaseLib
  HobLib

[Guids]
  gAmdTscFrequencyGuid                          ## PRODUCES ## HOB
