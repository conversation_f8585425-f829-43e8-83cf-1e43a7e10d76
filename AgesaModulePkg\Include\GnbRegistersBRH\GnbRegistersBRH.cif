<component>
    name = "GnbRegistersBRH"
    category = ModulePart
    LocalRoot = "AgesaModulePkg\Include\GnbRegistersBRH\"
    RefName = "AgesaModulePkg.Include.GnbRegistersBRH"
[files]
"CXLIO.h"
"GLOBALREGS.h"
"IOAGR.h"
"IOAPIC.h"
"IOHC.h"
"IOMMUL1.h"
"IOMMUL2.h"
"IOMMUMMIO.h"
"MCA_LS.h"
"MCA_NBIO.h"
"MCA_PCIE.h"
"MP_MP1CRU.h"
"NBIFEPF0CFG.h"
"NBIFEPFNCFG.h"
"NBIFMM.h"
"NBIFRCCFG.h"
"NTB.h"
"PCIECORE.h"
"PCIEPORT.h"
"PCIERCCFG.h"
"SDPMUX.h"
"SMU_PWR.h"
"SST.h"
"SYSHUBMM.h"
<endComponent>
