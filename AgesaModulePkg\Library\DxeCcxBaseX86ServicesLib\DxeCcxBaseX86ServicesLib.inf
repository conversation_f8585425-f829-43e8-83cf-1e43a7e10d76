#;*****************************************************************************
#;
#; Copyright (C) 2008-2024 Advanced Micro Devices, Inc. All rights reserved.
#;
#;******************************************************************************
#For EDKII use Only
[Defines]
  INF_VERSION                    = 0x00010005
  BASE_NAME                      = DxeCcxBaseX86ServicesLib
  FILE_GUID                      = E4BE4342-76C0-4c56-B77E-269BDEFED6B4
  MODULE_TYPE                    = DXE_DRIVER
  VERSION_STRING                 = 1.0
  LIBRARY_CLASS                  = DxeCcxBaseX86ServicesLib

[Sources.common]
  CcxBaseX86ServicesDxe.c
  CcxBaseX86ServicesDxe.h

[Packages]
  MdePkg/MdePkg.dec
  AgesaModulePkg/AgesaCommonModulePkg.dec
  AgesaPkg/AgesaPkg.dec

[LibraryClasses]
  AmdBaseLib
  CcxBaseX86Lib

[Guids]


[Protocols]

[Ppis]

[Pcd]
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdNumberOfPhysicalSocket
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdSmee

[Depex]



