/*
 ******************************************************************************
 *
 * Copyright (C) 2024 Advanced Micro Devices, Inc. All rights reserved.
 *
 *******************************************************************************
 */
/**
 * @file
 *
 * AMD Emulation environment auto detection
 *
 * Contains all functions related to IDS Debug Print
 *
 * @xrefitem bom "File Content Label" "Release Content"
 * @e project:      AGESA
 * @e sub-project:  IDS
 * @e \$Revision: 309090 $   @e \$Date: 2014-12-10 02:28:05 +0800 (Wed, 10 Dec 2014) $
 */

 /*----------------------------------------------------------------------------------------
 *                             M O D U L E S    U S E D
 *----------------------------------------------------------------------------------------
 */
#include <Uefi.h>
#include <Library/PciCf8Lib.h>
#include "AMD.h"


 /*----------------------------------------------------------------------------------------
  *                   D E F I N I T I O N S    A N D    M A C R O S
  *----------------------------------------------------------------------------------------
  */
#define FILECODE LIBRARY_AMDEMULATIONAUTODETECTPEILIB_AMDEMULATIONAUTODETECTPEILIB_FILECODE

#define MP0_C2PMSG_97               0x03810A84ul
#define MP0_C2PMSG_98               0x03810A88ul

#ifndef D0F0xB8_ADDRESS
  // **** D0F0xB8 Register Definition ****
  // Address
  #define D0F0xB8_ADDRESS      0xB8
#endif

#ifndef D0F0xBC_ADDRESS
  // **** D0F0xBC Register Definition ****
  // Address
  #define D0F0xBC_ADDRESS      0xBC
#endif

/*----------------------------------------------------------------------------------------*/
/*
 *  Read the SMN register.
 *
 *
 * @param[in]  BusNumber         Bus number of D0F0 of the target die
 * @param[in]  SmnAddress        SMN Register offset
 * @param[out] Value             Return value
 * @retval     VOID
 */
static VOID
SmnRegisterRead (
  IN       UINT32              BusNumber,
  IN       UINT32              SmnAddress,
     OUT   VOID                *Value
  )
{
  PCI_ADDR PciAddress;

  PciAddress.AddressValue = 0;
  PciAddress.Address.Bus = BusNumber;
  PciAddress.Address.Register = D0F0xB8_ADDRESS;

  PciCf8Write32 (PciAddress.AddressValue, SmnAddress);
  PciAddress.Address.Register = D0F0xBC_ADDRESS;
  *((UINT32 *) Value) = PciCf8Read32 (PciAddress.AddressValue);
  return;
}

/**
 *      Detect the system is emulation or real platform.
 *
 *
 *  @retval       TRUE    The system is emulation
 *  @retval       FALSE   The system is real platform
 *
 **/
BOOLEAN
AmdIdsEmulationAutoDetect (
  VOID
  )
{
  UINT32    Data;
  if (PcdGetBool (PcdAmdIdsDebugPrintEmulationAutoDetect)) {
    SmnRegisterRead (0, MP0_C2PMSG_97, &Data);
    if (Data & 0x0F) {
      // non-0 = Emulation system
      return TRUE;
    } else {
      SmnRegisterRead (0, MP0_C2PMSG_98, &Data);
      // 0: Real HW system
      if (Data & BIT16) {
        // Bit[16] = 1 = Emulation system
        return TRUE;
      }
    }
  }
  return FALSE;
}