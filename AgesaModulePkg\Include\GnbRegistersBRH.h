/**
 *  @file GnbRegistersBRH.h
 *  @brief Top level header for NBIO Register definitions
 */
/*
*****************************************************************************
*
 * Copyright (C) 2008-2024 Advanced Micro Devices, Inc. All rights reserved.
 *
*******************************************************************************
*
*/
#ifndef _GNBREGISTERSBRH_H_
#define _GNBREGISTERSBRH_H_

//
#include "GnbRegistersBRH/IOAGR.h"
#include "GnbRegistersBRH/IOAPIC.h"
#include "GnbRegistersBRH/IOHC.h"
#include "GnbRegistersBRH/IOMMUL1.h"
#include "GnbRegistersBRH/IOMMUL2.h"
#include "GnbRegistersBRH/IOMMUMMIO.h"
#include "GnbRegistersBRH/MCA_NBIO.h"
#include "GnbRegistersBRH/MCA_PCIE.h"
#include "GnbRegistersBRH/MP_MP1CRU.h"
#include "GnbRegistersBRH/NBIFEPF0CFG.h"
#include "GnbRegistersBRH/NBIFEPFNCFG.h"
#include "GnbRegistersBRH/NBIFMM.h"
#include "GnbRegistersBRH/NBIFRCCFG.h"
#include "GnbRegistersBRH/NTB.h"
#include "GnbRegistersBRH/PCIECORE.h"
#include "GnbRegistersBRH/PCIEPORT.h"
#include "GnbRegistersBRH/PCIERCCFG.h"
#include "GnbRegistersBRH/SDPMUX.h"
#include "GnbRegistersBRH/SMU_PWR.h"
#include "GnbRegistersBRH/SST.h"
#include "GnbRegistersBRH/SYSHUBMM.h"
#include "GnbRegistersBRH/MCA_LS.h"
#include "GnbRegistersBRH/GLOBALREGS.h"

#define  NBIF0_PORT0_ADDR(Handle)   MAKE_SBDFO(Handle->Address.Address.Segment, Handle->Address.Address.Bus, 7, 1, 0x0)
#define  NBIF0_PORT1_ADDR(Handle)   MAKE_SBDFO(Handle->Address.Address.Segment, Handle->Address.Address.Bus, 7, 2, 0x0)

#define BIG_IOHC(HANDLE)      (HANDLE->RBIndex < 4)
#define LITTLE_IOHC(HANDLE)   (HANDLE->RBIndex >= 4)

#define IOHC_INDEX(HANDLE)   (((HANDLE->RBIndex & 0x4) >> 2) | ((HANDLE->RBIndex & 0x1) << 1))

#define NBIO_INDEX(HANDLE)   ((HANDLE->RBIndex & 0x2) >> 1)

#ifndef NBIO_SPACE
  #define  NBIO_SPACE(HANDLE, ADDRESS)   (ADDRESS + ((HANDLE->RBIndex & 0x3) << 20))
#endif

#ifndef WRAP_SPACE
  #define  WRAP_SPACE(HANDLE, WRAPPER, ADDRESS)   (ADDRESS + (HANDLE->RBIndex << 20) + ((WRAPPER->WrapId == 0) ? 0 : ((WRAPPER->WrapId + 6) << 20)))
#endif

#ifndef PORT_SPACE
  #define  PORT_SPACE(HANDLE, WRAPPER, PORTINDEX, ADDRESS)   (ADDRESS + (HANDLE->RBIndex << 20) + ((WRAPPER->WrapId == 0) ? 0 : ((WRAPPER->WrapId + 6) << 20)) + (PORTINDEX << 12))
#endif

#ifndef IOHC_BRIDGE_SPACE
  #define  IOHC_BRIDGE_SPACE(HANDLE, ENGINE, ADDRESS)   (ADDRESS + ((HANDLE->RBIndex & 0x3) << 20) + (ENGINE->Type.Port.LogicalBridgeId << 10))
#endif

#ifndef IOHC_REMAP_SPACE
  #define  IOHC_REMAP_SPACE(HANDLE, ADDRESS, PORTINDEX)   (ADDRESS + ((HANDLE->RBIndex & 0x3) << 20) + (PORTINDEX << 2) + ((PORTINDEX < 20)? 0 : 0x280))
#endif

#ifndef IOHCRAS_PORT_SPACE
  #define  IOHCRAS_PORT_SPACE(HANDLE, WRAPPER, PORTINDEX, ADDRESS)   (ADDRESS + ((HANDLE->RBIndex & 0x3) << 20) + (WRAPPER->WrapId * 0x120) + (PORTINDEX * 0x20))
#endif

/// CAUTION: This is very specific to SDPMUX registers that use a different address calculation
#ifndef SDPMUX_SPACE
  #define  SDPMUX_SPACE(HANDLE, ADDRESS)   ((HANDLE->RBIndex == 0) ? ADDRESS : (ADDRESS + (1 << 23) + ((HANDLE->RBIndex) << 20)))
#endif

#ifndef PCICFG_OFFSET
  #define PCICFG_OFFSET(SmnAddr) (SmnAddr & 0xFFF)
#endif

#ifndef CNLI_SPACE
  #define CNLI_SPACE(HANDLE, ENGINE, ADDRESS)  (ADDRESS + ((HANDLE->RBIndex & 3) << 16) + (ENGINE->Type.Port.PortId << 13))
#endif

#define SMN_MP0MP0_MP0_C2PMSG_97_ADDRESS    0x3810a84UL

#define CXL_CAPABLE_RB(Handle) ((Handle->RBIndex & 5) == 0) || ((Handle->RBIndex & 5) == 5)

// FASTSIM Definitions
//
#define   FASTSIM_ADDRESS         SMN_IOHUB0NBIO0_NBCFG_SCRATCH_4_ADDRESS
#define   MP0_C2PMSG_97_ADDRESS   SMN_MP0MP0_MP0_C2PMSG_97_ADDRESS



#endif /*_GNBREGISTERSBRH_H_*/



