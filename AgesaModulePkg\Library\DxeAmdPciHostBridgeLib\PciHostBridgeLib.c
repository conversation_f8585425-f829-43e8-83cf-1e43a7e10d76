/*****************************************************************************
 * Copyright (C) 2008-2024 Advanced Micro Devices, Inc. All rights reserved.
 *
*****************************************************************************
*/
/* $NoKeywords:$ */
/**
 * @file
 *
 * AMD instance of the PCI Host Bridge Library.
 *
 *
 * @xrefitem bom "File Content Label" "Release Content"
 * @e project:      AGESA
 * @e sub-project:  Lib
 * @e \$Revision$   @e \$Date$
 *
 */

/** @file
  Library instance of PciHostBridgeLib library class for coreboot.

  Copyright (C) 2016, Red Hat, Inc.
  Copyright (c) 2016, Intel Corporation. All rights reserved.<BR>

  This program and the accompanying materials are licensed and made available
  under the terms and conditions of the BSD License which accompanies this
  distribution.  The full text of the license may be found at
  http://opensource.org/licenses/bsd-license.php.

  THE PROGRAM IS DISTRIBUTED UNDER THE BSD LICENSE ON AN "AS IS" BASIS, WITHOUT
  WARRANTIES OR REPRESENTATIONS OF ANY KIND, EITHER EXPRESS OR IMPLIED.

**/
#include <PiDxe.h>
#include <Library/PciHostBridgeLib.h>
#include <Library/DebugLib.h>
#include <Library/DevicePathLib.h>
#include <Library/BaseMemoryLib.h>
#include <Library/MemoryAllocationLib.h>
#include <Library/UefiBootServicesTableLib.h>
#include <Library/UefiRuntimeServicesTableLib.h>
#include <Protocol/PciRootBridgeIo.h>
#include <Protocol/PciHostBridgeResourceAllocation.h>
#include <Protocol/FabricTopologyServices2.h>
#include <Protocol/FabricResourceManagerServicesProtocol.h>
#include <Protocol/AmdApcbProtocol.h>
#include <Protocol/AmdCxlServicesProtocol.h>
#include "AGESA.h"

#pragma pack(1)
typedef struct {
  ACPI_HID_DEVICE_PATH     AcpiDevicePath;
  EFI_DEVICE_PATH_PROTOCOL EndDevicePath;
} EFI_PCI_ROOT_BRIDGE_DEVICE_PATH;

typedef struct {
  UINT64            Base;
  UINT64            Length;
  UINT64            Alignment;
} RESOURCE_NODE;

typedef struct {
  RESOURCE_NODE     Mem32[MAX_SOCKETS_SUPPORTED * MAX_RBS_PER_SOCKET];
  RESOURCE_NODE     PMem32[MAX_SOCKETS_SUPPORTED * MAX_RBS_PER_SOCKET];
  RESOURCE_NODE     Mem64[MAX_SOCKETS_SUPPORTED * MAX_RBS_PER_SOCKET];
  RESOURCE_NODE     PMem64[MAX_SOCKETS_SUPPORTED * MAX_RBS_PER_SOCKET];
  RESOURCE_NODE     Io[MAX_SOCKETS_SUPPORTED * MAX_RBS_PER_SOCKET];
  RESOURCE_NODE     PciBus[MAX_SOCKETS_SUPPORTED * MAX_RBS_PER_SOCKET];
} ROOT_BRIDGE_RESOURCE;

typedef struct {
  UINTN           RbBusBase;
  UINTN           RbBusLimit;
  UINTN           RbSocket;
  UINTN           RbIndex;
  PCI_ROOT_BRIDGE *RbPtr;
} CXL_ROOT_BRIDGE_INFO;

#pragma pack ()

EFI_STATUS
AmdInitResourceSize (
  IN       ROOT_BRIDGE_RESOURCE                   *CurRbRes,
  IN OUT   FABRIC_RESOURCE_FOR_EACH_RB            *AmdResourceSize,
  IN       AMD_FABRIC_TOPOLOGY_SERVICES2_PROTOCOL *FabricTopology
  );

STATIC EFI_PCI_ROOT_BRIDGE_DEVICE_PATH mEfiPciRootBridgeDevicePath = {
    {
      {
        ACPI_DEVICE_PATH,
        ACPI_DP,
        {
          (UINT8)(sizeof (ACPI_HID_DEVICE_PATH)),
          (UINT8)((sizeof (ACPI_HID_DEVICE_PATH)) >> 8)
        }
      },
      EISA_PNP_ID (0x0A03), // PCI-to-PCI Bridge
      0
    },

    {
      END_DEVICE_PATH_TYPE,
      END_ENTIRE_DEVICE_PATH_SUBTYPE,
      {
        END_DEVICE_PATH_LENGTH,
        0
      }
    }
};

GLOBAL_REMOVE_IF_UNREFERENCED
CHAR16 *mPciHostBridgeLibAcpiAddressSpaceTypeStr[] = {
  L"Mem", L"I/O", L"Bus"
};

/**
  Sort all root bridges in bus ascending order and set DevicePath UIDs
  continuous and ascending from zero

  @param[in,out]  RootBridgeList  Array of root bridges.
  @param[in]      Count           Count of root bridges in RootBridgeList

  @return All the root bridge instances in an array are sorted in bus order.
          DevicePath UID updated to continuous and ascending numbers starting
          with 0.
**/
VOID
EFIAPI
PciHostBridgeSortRootBridges (
  IN  PCI_ROOT_BRIDGE   *RootBridgeList,
  IN  UINTN             Count
  )
{
  PCI_ROOT_BRIDGE                       TempRootBridge;
  BOOLEAN                               SwappedRootBridgeThisPass;
  UINTN                                 i;
  UINTN                                 j;

  if (Count > 1) { // Count is UINTN, prevent wraparound calculations
    // Modified Bubble Sort the Entries.
    for (i = 0; i < Count - 1; i++) {
      SwappedRootBridgeThisPass = FALSE;
      for (j = 0; j < Count - i - 1; j++) {
        if (RootBridgeList[j].Bus.Base > RootBridgeList[j + 1].Bus.Base) {
          // Swap the entries
          CopyMem (&TempRootBridge, &RootBridgeList[j], sizeof (PCI_ROOT_BRIDGE));
          CopyMem (&RootBridgeList[j], &RootBridgeList[j + 1], sizeof (PCI_ROOT_BRIDGE));
          CopyMem (&RootBridgeList[j + 1], &TempRootBridge, sizeof (PCI_ROOT_BRIDGE));
          SwappedRootBridgeThisPass = TRUE;
        }
      } // End of for (j = 0; j < Count - i - 1; j++)
      if (!SwappedRootBridgeThisPass) {
        break;
      }
    } // End of for (i = 0; i < Count - 1; i++)
  }

  // Make DevicePath UID continuous and ascending.
  for (i = 0; i < Count; i++) {
    ((EFI_PCI_ROOT_BRIDGE_DEVICE_PATH *)(RootBridgeList[i].DevicePath))->AcpiDevicePath.UID = (UINT32)i;
  }
}

/**
  Return all the root bridge instances in an array.

  @param Count  Return the count of root bridge instances.

  @return All the root bridge instances in an array.
          The array should be passed into PciHostBridgeFreeRootBridges()
          when it's not used.
**/
PCI_ROOT_BRIDGE *
EFIAPI
PciHostBridgeGetRootBridges (
  UINTN *Count
  )
{
  AMD_FABRIC_TOPOLOGY_SERVICES2_PROTOCOL *FabricTopology;
  FABRIC_RESOURCE_MANAGER_PROTOCOL      *FabricResource;
  FABRIC_RESOURCE_FOR_EACH_RB           AmdResourceSize;
  PCI_ROOT_BRIDGE                       *RootBridge;
  PCI_ROOT_BRIDGE                       *FirstRootBridge;
  EFI_PCI_ROOT_BRIDGE_DEVICE_PATH       *DevicePath;
  CHAR16                                *DevicePathStr;
  UINTN                                 SocketLoop;
  UINTN                                 DieLoop;
  UINTN                                 RootBridgeLoop;
  UINTN                                 RootBridgeInSocket;
  UINTN                                 SocketCount;
  UINTN                                 DiePerSocketCount;
  UINTN                                 RootBridgePerDieCount;
  UINTN                                 RootBridgeCount;
  UINTN                                 RootBridgeAllocCount;
  UINTN                                 PciBusBase;
  UINTN                                 PciBusLimit;
  UINT64                                Base;
  UINT64                                Size;
  UINT64                                PBase;
  UINT64                                PSize;
  EFI_STATUS                            Status;
  UINTN                                 FixedRootBridgePerDieCount;
  UINTN                                 Cxl1Count;
  UINTN                                 Cxl20Count;
  AMD_NBIO_CXL_SERVICES_PROTOCOL        *AmdNbioCxlServicesProtocol;
  UINT8                                 Index;
  AMD_CXL_PORT_INFO_STRUCT              NbioPortInfo;
  CXL_ROOT_BRIDGE_INFO                  CxlRbInfo[MAX_SOCKETS_SUPPORTED * MAX_RBS_PER_SOCKET];
  AMD_CXL_RESOURCES_INFO_STRUCT         CxlMmioResources;
  UINT8                                 CxlRbCount;
  UINTN                                 CxlRbSocket;
  UINTN                                 CxlRbIndex;
  PCI_ROOT_BRIDGE                       *CxlRbPtr;

  AmdNbioCxlServicesProtocol = NULL;
  Cxl1Count = 0;
  Cxl20Count = 0;
  FixedRootBridgePerDieCount = 0;
  CxlRbCount = 0;
  ZeroMem (&CxlRbInfo, sizeof (CXL_ROOT_BRIDGE_INFO) * (MAX_SOCKETS_SUPPORTED * MAX_RBS_PER_SOCKET));
  ZeroMem (&CxlMmioResources, sizeof (AMD_CXL_RESOURCES_INFO_STRUCT));

  // Get number of root bridges from AGESA protocol
  *Count = 0;
  RootBridgeCount = 0;
  Status = gBS->LocateProtocol (
                  &gAmdFabricTopologyServices2ProtocolGuid,
                  NULL,
                  (VOID **)&FabricTopology
                  );
  ASSERT_EFI_ERROR (Status);
  if (EFI_ERROR (Status)) {
    return NULL;
  }
  SetMem (&AmdResourceSize, sizeof (FABRIC_RESOURCE_FOR_EACH_RB), 0);
  FabricTopology->GetSystemInfo (FabricTopology, &SocketCount, NULL, &RootBridgeCount, NULL, NULL);

  // Get available FabricResource
  Status = gBS->LocateProtocol (
                  &gAmdFabricResourceManagerServicesProtocolGuid,
                  NULL,
                  (VOID **)&FabricResource
                  );
  ASSERT_EFI_ERROR (Status);
  if (EFI_ERROR (Status)) {
    return NULL;
  }
  FabricResource->FabricGetAvailableResource (FabricResource, &AmdResourceSize);

  Status = gBS->LocateProtocol (&gAmdNbioCxlServicesProtocolGuid, NULL, (VOID **)&AmdNbioCxlServicesProtocol);
  if (AmdNbioCxlServicesProtocol != NULL) {
    // Get the CXL 2.0 count
    Cxl20Count = 0;
    for (Index = 0; Index < MAX_CXL_ROOT_BRIDGES; Index++) {
      Status = AmdNbioCxlServicesProtocol->CxlFind2p0Devices (AmdNbioCxlServicesProtocol, Index);
      if (!EFI_ERROR (Status)) {
        Cxl20Count++;
      }
    }

    Status = AmdNbioCxlServicesProtocol->GetCxlAvailableResources (AmdNbioCxlServicesProtocol, &AmdResourceSize);
    if (EFI_ERROR (Status)) {
      DEBUG ((EFI_D_ERROR, "%a: CXL resources not available\n", __FUNCTION__));
    } else {
      // Get the CXL 1.1 count
      Cxl1Count = AmdNbioCxlServicesProtocol->CxlCount - Cxl20Count;
      RootBridgeCount = RootBridgeCount + Cxl1Count;
    }
    // For CXL 2.0, we skip resource assignment, as it is done thru enumeration
    if (Cxl1Count != 0) {
      Status = AmdNbioCxlServicesProtocol->GetCxlMmioResources (AmdNbioCxlServicesProtocol, &CxlMmioResources);
      if (EFI_ERROR (Status)) {
        DEBUG ((EFI_D_ERROR, "%a: CXL resources not available\n", __FUNCTION__));
      }
    }
  }
  // Allocate RootBridge(s)
  RootBridge = RootBridgeCount ? AllocateZeroPool (RootBridgeCount * sizeof (PCI_ROOT_BRIDGE)) : NULL;
  if (RootBridge == NULL) {
    DEBUG ((EFI_D_ERROR, "%a: %r\n", __FUNCTION__, EFI_OUT_OF_RESOURCES));
    return NULL;
  }
  FirstRootBridge = RootBridge;

  // Allocate DevicePath(s)
  DevicePath = AllocateZeroPool (RootBridgeCount * sizeof (EFI_PCI_ROOT_BRIDGE_DEVICE_PATH));
  if (DevicePath == NULL) {
    DEBUG ((EFI_D_ERROR, "%a: %r\n", __FUNCTION__, EFI_OUT_OF_RESOURCES));
    FreePool (FirstRootBridge);
    return NULL;
  }

  RootBridgeAllocCount = RootBridgeCount;
  for (SocketLoop = 0; SocketLoop < SocketCount && RootBridgeAllocCount; SocketLoop++) {
    DEBUG ((EFI_D_INFO, "Socket# = %d\n", SocketLoop));

    RootBridgeInSocket = 0;
    FabricTopology->GetProcessorInfo (FabricTopology, SocketLoop, &DiePerSocketCount, NULL);
    for (DieLoop = 0; DieLoop < DiePerSocketCount && RootBridgeAllocCount; DieLoop++) {
      DEBUG ((EFI_D_INFO, " Die# = %d\n", DieLoop));

      FabricTopology->GetDieInfo (FabricTopology, SocketLoop, DieLoop, &FixedRootBridgePerDieCount, NULL, NULL);
      RootBridgePerDieCount = FixedRootBridgePerDieCount;
      //
      // TODO
      // This code is not complete. It asssumes the CXL device is in every die of the socket
      // This will be ok for RS. This needs to be addressed for MI300. We need to know where CXL is
      // when there is more that one die.
      //
      if (AmdNbioCxlServicesProtocol != NULL) {
        for (Index = 0; Index < Cxl1Count; Index++ ) {
          Status = AmdNbioCxlServicesProtocol->CxlGetRootPortInformation (
                    AmdNbioCxlServicesProtocol,
                    Index,
                    &NbioPortInfo
                    );
          if (EFI_ERROR (Status)) {
            continue;
          }
          if ((NbioPortInfo.SocketID == SocketLoop) && (RootBridgeCount > (SocketCount * DiePerSocketCount * FixedRootBridgePerDieCount))) {
            RootBridgePerDieCount++;
          }
        }
      }
      for (RootBridgeLoop = 0; RootBridgeLoop < RootBridgePerDieCount && RootBridgeAllocCount; RootBridgeLoop++) {
        DEBUG ((EFI_D_INFO, "  RootBridge# = %d\n", RootBridgeLoop));

        RootBridge->Supports = EFI_PCI_ATTRIBUTE_IDE_PRIMARY_IO |
          EFI_PCI_ATTRIBUTE_IDE_SECONDARY_IO |
          EFI_PCI_ATTRIBUTE_ISA_IO_16 |
          EFI_PCI_ATTRIBUTE_ISA_MOTHERBOARD_IO |
          EFI_PCI_ATTRIBUTE_VGA_MEMORY |
          EFI_PCI_ATTRIBUTE_VGA_IO |
          EFI_PCI_ATTRIBUTE_VGA_PALETTE_IO;
        RootBridge->Attributes = RootBridge->Supports;

        RootBridge->AllocationAttributes = EFI_PCI_HOST_BRIDGE_MEM64_DECODE;

        RootBridge->DmaAbove4G = TRUE;
        RootBridge->NoExtendedConfigSpace = FALSE;
        if (RootBridgeLoop >= FixedRootBridgePerDieCount) {
         PciBusBase = AmdResourceSize.PciBusNumber[SocketLoop][RootBridgeInSocket];
         PciBusLimit = AmdResourceSize.PciBusNumber[SocketLoop][RootBridgeInSocket];
         RootBridge->Segment = (UINT32) (PciBusBase / MAX_PCI_BUS_NUMBER_PER_SEGMENT);
        } else {
          FabricTopology->GetRootBridgeInfo (FabricTopology, SocketLoop, DieLoop, RootBridgeLoop, NULL, &PciBusBase, &PciBusLimit, NULL, NULL, NULL);
          RootBridge->Segment = (UINT32) (PciBusBase / MAX_PCI_BUS_NUMBER_PER_SEGMENT);
          PciBusBase %= MAX_PCI_BUS_NUMBER_PER_SEGMENT;
          PciBusLimit %= MAX_PCI_BUS_NUMBER_PER_SEGMENT;
        }
        RootBridge->Bus.Base = PciBusBase;
        RootBridge->Bus.Limit = PciBusLimit;

        if (AmdResourceSize.IO[SocketLoop][RootBridgeInSocket].Size != 0) {
          RootBridge->Io.Base = AmdResourceSize.IO[SocketLoop][RootBridgeInSocket].Base;
          RootBridge->Io.Limit = RootBridge->Io.Base + AmdResourceSize.IO[SocketLoop][RootBridgeInSocket].Size - 1;
        } else {
          RootBridge->Io.Base = 0xFFFF;
          RootBridge->Io.Limit = 0;
        }

        //
        // NOTE: MMIO below 4G is assigned from top to bottom.
        // This way, the FCH on Primary RootBridge gets MMIO reosurces right below 4GB's.
        //
        if ((RootBridge->Bus.Base == 0) && (RootBridge->Bus.Limit != 0)) {
          Base = (AmdResourceSize.PrimaryRbSecondNonPrefetchableMmioSizeBelow4G.Size > AmdResourceSize.NonPrefetchableMmioSizeBelow4G[SocketLoop][RootBridgeInSocket].Size) ?
            AmdResourceSize.PrimaryRbSecondNonPrefetchableMmioSizeBelow4G.Base : AmdResourceSize.NonPrefetchableMmioSizeBelow4G[SocketLoop][RootBridgeInSocket].Base;
          Size = (AmdResourceSize.PrimaryRbSecondNonPrefetchableMmioSizeBelow4G.Size > AmdResourceSize.NonPrefetchableMmioSizeBelow4G[SocketLoop][RootBridgeInSocket].Size) ?
            AmdResourceSize.PrimaryRbSecondNonPrefetchableMmioSizeBelow4G.Size : AmdResourceSize.NonPrefetchableMmioSizeBelow4G[SocketLoop][RootBridgeInSocket].Size;

          PBase = (AmdResourceSize.PrimaryRbSecondPrefetchableMmioSizeBelow4G.Size > AmdResourceSize.PrefetchableMmioSizeBelow4G[SocketLoop][RootBridgeInSocket].Size) ?
            AmdResourceSize.PrimaryRbSecondPrefetchableMmioSizeBelow4G.Base : AmdResourceSize.PrefetchableMmioSizeBelow4G[SocketLoop][RootBridgeInSocket].Base;
          PSize = (AmdResourceSize.PrimaryRbSecondPrefetchableMmioSizeBelow4G.Size > AmdResourceSize.PrefetchableMmioSizeBelow4G[SocketLoop][RootBridgeInSocket].Size) ?
            AmdResourceSize.PrimaryRbSecondPrefetchableMmioSizeBelow4G.Size : AmdResourceSize.PrefetchableMmioSizeBelow4G[SocketLoop][RootBridgeInSocket].Size;
        } else {
          Base = AmdResourceSize.NonPrefetchableMmioSizeBelow4G[SocketLoop][RootBridgeInSocket].Base;
          Size = AmdResourceSize.NonPrefetchableMmioSizeBelow4G[SocketLoop][RootBridgeInSocket].Size;

          PBase = AmdResourceSize.PrefetchableMmioSizeBelow4G[SocketLoop][RootBridgeInSocket].Base;
          PSize = AmdResourceSize.PrefetchableMmioSizeBelow4G[SocketLoop][RootBridgeInSocket].Size;
        }

        if (Cxl1Count != 0 && (CxlMmioResources.Mmio32Size[SocketLoop][RootBridgeInSocket] != 0) && (PciBusBase != PciBusLimit)) {
          // Save CXL parent root bridge information
          CxlRbInfo[CxlRbCount].RbBusBase = PciBusBase;
          CxlRbInfo[CxlRbCount].RbBusLimit = PciBusLimit;
          CxlRbInfo[CxlRbCount].RbSocket = SocketLoop;
          CxlRbInfo[CxlRbCount].RbIndex = RootBridgeInSocket;
          CxlRbInfo[CxlRbCount].RbPtr = RootBridge;
          CxlRbCount++;
        }

        if ((Cxl1Count != 0) && (RootBridge->Bus.Base == RootBridge->Bus.Limit)) {
          // CXL, adjust parent and assign CXL nonprefetchable MMIO32 resource
          DEBUG ((EFI_D_INFO, "\nFound CXL, Assign nonprefetch MMIO32\n"));
          CxlRbPtr = NULL;
          for (Index = 0; Index < CxlRbCount; Index++) {
            if (RootBridge->Bus.Base > CxlRbInfo[Index].RbBusBase && RootBridge->Bus.Limit <= CxlRbInfo[Index].RbBusLimit) {
              DEBUG ((EFI_D_INFO, "CXL parent root bridge found, RbBusBase 0x%x RbBusLimit 0x%x Rbsocket %d Rbindex %d\n",
                      CxlRbInfo[Index].RbBusBase, CxlRbInfo[Index].RbBusLimit, CxlRbInfo[Index].RbSocket, CxlRbInfo[Index].RbIndex));
              CxlRbPtr = CxlRbInfo[Index].RbPtr;
              CxlRbSocket = CxlRbInfo[Index].RbSocket;
              CxlRbIndex = CxlRbInfo[Index].RbIndex;
              break;
            }
          }
          if (CxlRbPtr != NULL) {
            if ((CxlRbPtr->Mem.Limit - CxlRbPtr->Mem.Base + 1) >= CxlMmioResources.Mmio32Size[CxlRbSocket][CxlRbIndex]) {
              CxlRbPtr->Mem.Limit -= CxlMmioResources.Mmio32Size[CxlRbSocket][CxlRbIndex];
              Base = CxlRbPtr->Mem.Limit + 1;
              if (Base == CxlRbPtr->Mem.Base) {
                CxlRbPtr->Mem.Base = 0xFFFFFFFFFFFFFFFF;
                CxlRbPtr->Mem.Limit = 0;
              }
              Size = CxlMmioResources.Mmio32Size[CxlRbSocket][CxlRbIndex];
              DevicePathStr = ConvertDevicePathToText (CxlRbPtr->DevicePath, FALSE, FALSE);
              DEBUG ((EFI_D_INFO, "  Adjust Parent RootBridge Path: %s\n", DevicePathStr));
              DEBUG ((EFI_D_INFO, "           Bus: %lx - %lx\n", CxlRbPtr->Bus.Base, CxlRbPtr->Bus.Limit));
              DEBUG ((EFI_D_INFO, "           Mem: %lx - %lx  (Size = %lx)\n", CxlRbPtr->Mem.Base, CxlRbPtr->Mem.Limit,
                CxlRbPtr->Mem.Limit ? (CxlRbPtr->Mem.Limit - CxlRbPtr->Mem.Base + 1) : 0));
            }
          }
        }

        if (Size != 0) {
          RootBridge->Mem.Base = Base;
          RootBridge->Mem.Limit = Base + Size - 1;
        } else {
          RootBridge->Mem.Base = 0xFFFFFFFFFFFFFFFF;
          RootBridge->Mem.Limit = 0;
        }

        if (AmdResourceSize.NonPrefetchableMmioSizeAbove4G[SocketLoop][RootBridgeInSocket].Size != 0) {
          RootBridge->MemAbove4G.Base = AmdResourceSize.NonPrefetchableMmioSizeAbove4G[SocketLoop][RootBridgeInSocket].Base;
          RootBridge->MemAbove4G.Limit = RootBridge->MemAbove4G.Base + AmdResourceSize.NonPrefetchableMmioSizeAbove4G[SocketLoop][RootBridgeInSocket].Size - 1;
        } else {
          RootBridge->MemAbove4G.Base = 0xFFFFFFFFFFFFFFFF;
          RootBridge->MemAbove4G.Limit = 0;
        }

        if (PSize != 0) {
          RootBridge->PMem.Base = PBase;
          RootBridge->PMem.Limit = PBase + PSize - 1;
        } else {
          RootBridge->PMem.Base = 0xFFFFFFFFFFFFFFFF;
          RootBridge->PMem.Limit = 0;
        }

        if (AmdResourceSize.PrefetchableMmioSizeAbove4G[SocketLoop][RootBridgeInSocket].Size != 0) {
          RootBridge->PMemAbove4G.Base = AmdResourceSize.PrefetchableMmioSizeAbove4G[SocketLoop][RootBridgeInSocket].Base;
          RootBridge->PMemAbove4G.Limit = RootBridge->PMemAbove4G.Base + AmdResourceSize.PrefetchableMmioSizeAbove4G[SocketLoop][RootBridgeInSocket].Size - 1;
        } else {
          RootBridge->PMemAbove4G.Base = 0xFFFFFFFFFFFFFFFF;
          RootBridge->PMemAbove4G.Limit = 0;
        }

        CopyMem (DevicePath, &mEfiPciRootBridgeDevicePath, sizeof (EFI_PCI_ROOT_BRIDGE_DEVICE_PATH));
        DevicePath->AcpiDevicePath.UID = RootBridge->Segment * MAX_PCI_BUS_NUMBER_PER_SEGMENT + ((UINT32) PciBusBase);
        RootBridge->DevicePath = (EFI_DEVICE_PATH_PROTOCOL *)DevicePath;

        DevicePathStr = ConvertDevicePathToText (RootBridge->DevicePath, FALSE, FALSE);
        DEBUG ((EFI_D_INFO, "  RootBridge Path: %s\n", DevicePathStr));
        DEBUG ((EFI_D_INFO, "           Bus: %lx - %lx\n", RootBridge->Bus.Base, RootBridge->Bus.Limit));
        DEBUG ((EFI_D_INFO, "            Io: %lx - %lx  (Size = %lx)\n",
          RootBridge->Io.Base, RootBridge->Io.Limit, RootBridge->Io.Limit - RootBridge->Io.Base + 1));
        DEBUG ((EFI_D_INFO, "           Mem: %lx - %lx  (Size = %lx)\n", RootBridge->Mem.Base, RootBridge->Mem.Limit,
          RootBridge->Mem.Limit ? (RootBridge->Mem.Limit - RootBridge->Mem.Base + 1) : 0));
        DEBUG ((EFI_D_INFO, "    MemAbove4G: %lx - %lx  (Size = %lx)\n", RootBridge->MemAbove4G.Base, RootBridge->MemAbove4G.Limit,
          RootBridge->MemAbove4G.Limit ? (RootBridge->MemAbove4G.Limit - RootBridge->MemAbove4G.Base + 1) : 0));
        DEBUG ((EFI_D_INFO, "          PMem: %lx - %lx  (Size = %lx)\n", RootBridge->PMem.Base, RootBridge->PMem.Limit,
          RootBridge->PMem.Limit ? (RootBridge->PMem.Limit - RootBridge->PMem.Base + 1) : 0));
        DEBUG ((EFI_D_INFO, "   PMemAbove4G: %lx - %lx  (Size = %lx)\n", RootBridge->PMemAbove4G.Base, RootBridge->PMemAbove4G.Limit,
          RootBridge->PMemAbove4G.Limit ? (RootBridge->PMemAbove4G.Limit - RootBridge->PMemAbove4G.Base + 1) : 0));

        RootBridge++;
        DevicePath++;
        RootBridgeAllocCount--;
        RootBridgeInSocket++;
      }
    }
  }

  PciHostBridgeSortRootBridges (FirstRootBridge, RootBridgeCount);

  DEBUG ((EFI_D_INFO, "%a: PCI_ROOT_BRIDGE Array after sorting and updating UID:\n", __FUNCTION__));
  for (Index = 0; Index < RootBridgeCount; Index++) {
    DevicePathStr = ConvertDevicePathToText (FirstRootBridge[Index].DevicePath, FALSE, FALSE);
    DEBUG ((EFI_D_INFO, "  RootBridge Path: %s\n", DevicePathStr));
    DEBUG ((EFI_D_INFO, "           Bus: %lx - %lx\n", FirstRootBridge[Index].Bus.Base, FirstRootBridge[Index].Bus.Limit));
    DEBUG ((EFI_D_INFO, "            Io: %lx - %lx  (Size = %lx)\n",
      FirstRootBridge[Index].Io.Base, FirstRootBridge[Index].Io.Limit, FirstRootBridge[Index].Io.Limit - FirstRootBridge[Index].Io.Base + 1));
    DEBUG ((EFI_D_INFO, "           Mem: %lx - %lx  (Size = %lx)\n", FirstRootBridge[Index].Mem.Base, FirstRootBridge[Index].Mem.Limit,
      FirstRootBridge[Index].Mem.Limit ? (FirstRootBridge[Index].Mem.Limit - FirstRootBridge[Index].Mem.Base + 1) : 0));
    DEBUG ((EFI_D_INFO, "    MemAbove4G: %lx - %lx  (Size = %lx)\n", FirstRootBridge[Index].MemAbove4G.Base, FirstRootBridge[Index].MemAbove4G.Limit,
      FirstRootBridge[Index].MemAbove4G.Limit ? (FirstRootBridge[Index].MemAbove4G.Limit - FirstRootBridge[Index].MemAbove4G.Base + 1) : 0));
    DEBUG ((EFI_D_INFO, "          PMem: %lx - %lx  (Size = %lx)\n", FirstRootBridge[Index].PMem.Base, FirstRootBridge[Index].PMem.Limit,
      FirstRootBridge[Index].PMem.Limit ? (FirstRootBridge[Index].PMem.Limit - FirstRootBridge[Index].PMem.Base + 1) : 0));
    DEBUG ((EFI_D_INFO, "   PMemAbove4G: %lx - %lx  (Size = %lx)\n", FirstRootBridge[Index].PMemAbove4G.Base, FirstRootBridge[Index].PMemAbove4G.Limit,
      FirstRootBridge[Index].PMemAbove4G.Limit ? (FirstRootBridge[Index].PMemAbove4G.Limit - FirstRootBridge[Index].PMemAbove4G.Base + 1) : 0));
  }

  *Count = RootBridgeCount;
  return FirstRootBridge;
}

/**
  Free the root bridge instances array returned from PciHostBridgeGetRootBridges().

  @param Bridges The root bridge instances array.
  @param Count   The count of the array.
**/
VOID
EFIAPI
PciHostBridgeFreeRootBridges (
  PCI_ROOT_BRIDGE *Bridges,
  UINTN           Count
  )
{
  FreePool (Bridges);
}

/**
  Fixup the PCI bus number with the segment number

  @param HostBridgeHandle Handle of the Host Bridge.
  @param CurRbRes         The pointer to the root bridge resource structure
  @param RootBridgeCount  The count of root bridges
**/
VOID
EFIAPI
PciSegmentNumberFixup (
  EFI_HANDLE               HostBridgeHandle,
  IN ROOT_BRIDGE_RESOURCE  *CurRbRes,
  IN UINTN                 RootBridgeCount
  )
{
  EFI_PCI_HOST_BRIDGE_RESOURCE_ALLOCATION_PROTOCOL *HostBridgeResourceAlloc;
  EFI_PCI_ROOT_BRIDGE_IO_PROTOCOL                  *RootBridgeIo;
  EFI_HANDLE                                       RootBridgeHandle;
  EFI_STATUS                                       Status;
  UINTN                                            Index;
  EFI_ACPI_ADDRESS_SPACE_DESCRIPTOR                *Descriptor;
  VOID                                             *Configuration;
  BOOLEAN                                          IsMatched;
  BOOLEAN                                          *SegNumFixed;
  struct {
    UINT64 Base;
    UINT64 Length;
  } Io, Mem32, PMem32, Mem64, PMem64;

  Status = gBS->HandleProtocol (HostBridgeHandle, &gEfiPciHostBridgeResourceAllocationProtocolGuid, (VOID **)&HostBridgeResourceAlloc);
  ASSERT_EFI_ERROR (Status);
  if (EFI_ERROR (Status)) {
    return;
  }

  SegNumFixed = AllocateZeroPool (RootBridgeCount);
  if (SegNumFixed == NULL) {
    ASSERT (SegNumFixed != NULL);
    return;
  }

  RootBridgeHandle = NULL;
  while (HostBridgeResourceAlloc->GetNextRootBridge (HostBridgeResourceAlloc, &RootBridgeHandle) == EFI_SUCCESS) {
    Status = gBS->HandleProtocol (RootBridgeHandle, &gEfiPciRootBridgeIoProtocolGuid, (VOID **)&RootBridgeIo);
    ASSERT_EFI_ERROR (Status);
    if (EFI_ERROR (Status)) {
      continue;
    }

    Status = HostBridgeResourceAlloc->GetProposedResources (HostBridgeResourceAlloc, RootBridgeHandle, &Configuration);
    ASSERT_EFI_ERROR (Status);
    if (EFI_ERROR (Status)) {
      continue;
    }

    //Collect the resources for this host bridge
    gBS->SetMem (&Io,     sizeof (Io),     0);
    gBS->SetMem (&Mem32,  sizeof (Mem32),  0);
    gBS->SetMem (&PMem32, sizeof (PMem32), 0);
    gBS->SetMem (&Mem64,  sizeof (Mem64),  0);
    gBS->SetMem (&PMem64, sizeof (PMem64), 0);
    for (Descriptor = Configuration; Descriptor->Desc == ACPI_ADDRESS_SPACE_DESCRIPTOR; Descriptor++) {
      switch (Descriptor->ResType) {
      case ACPI_ADDRESS_SPACE_TYPE_IO:
        Io.Base = Descriptor->AddrRangeMin;
        Io.Length = Descriptor->AddrLen;
        break;
      case ACPI_ADDRESS_SPACE_TYPE_MEM:
        if (Descriptor->AddrSpaceGranularity == 32) {
          if (Descriptor->SpecificFlag == EFI_ACPI_MEMORY_RESOURCE_SPECIFIC_FLAG_CACHEABLE_PREFETCHABLE) {
            PMem32.Base = Descriptor->AddrRangeMin;
            PMem32.Length = Descriptor->AddrLen;
          } else {
            Mem32.Base = Descriptor->AddrRangeMin;
            Mem32.Length = Descriptor->AddrLen;
          }
        } else {
          if (Descriptor->SpecificFlag == EFI_ACPI_MEMORY_RESOURCE_SPECIFIC_FLAG_CACHEABLE_PREFETCHABLE) {
            PMem64.Base = Descriptor->AddrRangeMin;
            PMem64.Length = Descriptor->AddrLen;
          } else {
            Mem64.Base = Descriptor->AddrRangeMin;
            Mem64.Length = Descriptor->AddrLen;
          }
        }
        break;
      }
    }

    //Find the matched root bridge
    for (Index = 0; Index < RootBridgeCount; Index++) {
      IsMatched = TRUE;
      if (Io.Base != CurRbRes->Io[Index].Base) {
        IsMatched = FALSE;
      }
      if (Io.Length != CurRbRes->Io[Index].Length) {
        IsMatched = FALSE;
      }
      if (Mem32.Base != CurRbRes->Mem32[Index].Base) {
        IsMatched = FALSE;
      }
      if (Mem32.Length != CurRbRes->Mem32[Index].Length) {
        IsMatched = FALSE;
      }
      if (PMem32.Base != CurRbRes->PMem32[Index].Base) {
        IsMatched = FALSE;
      }
      if (PMem32.Length != CurRbRes->PMem32[Index].Length) {
        IsMatched = FALSE;
      }
      if (Mem64.Base != CurRbRes->Mem64[Index].Base) {
        IsMatched = FALSE;
      }
      if (Mem64.Length != CurRbRes->Mem64[Index].Length) {
        IsMatched = FALSE;
      }
      if (PMem64.Base != CurRbRes->PMem64[Index].Base) {
        IsMatched = FALSE;
      }
      if (PMem64.Length != CurRbRes->PMem64[Index].Length) {
        IsMatched = FALSE;
      }

      // Fixup the PCI bus number
      if (IsMatched && (SegNumFixed[Index] == FALSE)) {
        CurRbRes->PciBus[Index].Base += RootBridgeIo->SegmentNumber * MAX_PCI_BUS_NUMBER_PER_SEGMENT;
        SegNumFixed[Index] = TRUE;
        break;
      }
    }
  }
  FreePool (SegNumFixed);
}

/**
  Inform the platform that the resource conflict happens.

  @param HostBridgeHandle Handle of the Host Bridge.
  @param Configuration    Pointer to PCI I/O and PCI memory resource
                          descriptors. The Configuration contains the resources
                          for all the root bridges. The resource for each root
                          bridge is terminated with END descriptor and an
                          additional END is appended indicating the end of the
                          entire resources. The resource descriptor field
                          values follow the description in
                          EFI_PCI_HOST_BRIDGE_RESOURCE_ALLOCATION_PROTOCOL
                          .SubmitResources().
**/
VOID
EFIAPI
PciHostBridgeResourceConflict (
  EFI_HANDLE                        HostBridgeHandle,
  VOID                              *Configuration
  )
{
  EFI_ACPI_ADDRESS_SPACE_DESCRIPTOR *Descriptor;
  UINTN                             RootBridgeIndex;
  UINT32                            TokenSize;
  UINT64                            TOM;
  UINT64                            TomToken;
  EFI_STATUS                        Status;
  FABRIC_RESOURCE_FOR_EACH_RB           AmdResourceSize;
  AMD_FABRIC_TOPOLOGY_SERVICES2_PROTOCOL *FabricTopology;
  FABRIC_RESOURCE_MANAGER_PROTOCOL      *FabricResource;
  ROOT_BRIDGE_RESOURCE                  *CurRbRes;
  FABRIC_ADDR_SPACE_SIZE                SpaceStatus;
  AMD_APCB_SERVICE_PROTOCOL             *ApcbDxeServiceProtocol;

  Status = gBS->LocateProtocol (&gAmdFabricTopologyServices2ProtocolGuid, NULL, (VOID **)&FabricTopology);
  ASSERT_EFI_ERROR (Status);
  if (EFI_ERROR (Status)) {
    return;
  }
  Status = gBS->LocateProtocol (&gAmdFabricResourceManagerServicesProtocolGuid, NULL, (VOID **)&FabricResource);
  ASSERT_EFI_ERROR (Status);
  if (EFI_ERROR (Status)) {
    return;
  }

  CurRbRes = AllocateZeroPool (sizeof (*CurRbRes));
  if (CurRbRes == NULL) {
    ASSERT (CurRbRes != NULL);
    return;
  }

  // Get current resource for each RootBridge
  RootBridgeIndex = 0;
  Descriptor = (EFI_ACPI_ADDRESS_SPACE_DESCRIPTOR *) Configuration;
  while (Descriptor->Desc == ACPI_ADDRESS_SPACE_DESCRIPTOR) {
    for (; Descriptor->Desc == ACPI_ADDRESS_SPACE_DESCRIPTOR; Descriptor++) {
      switch (Descriptor->ResType) {
      case ACPI_ADDRESS_SPACE_TYPE_MEM:
        if (Descriptor->AddrSpaceGranularity == 32) {
          if (Descriptor->SpecificFlag == EFI_ACPI_MEMORY_RESOURCE_SPECIFIC_FLAG_CACHEABLE_PREFETCHABLE) {
            // PMem32
            CurRbRes->PMem32[RootBridgeIndex].Base = Descriptor->AddrRangeMin;
            CurRbRes->PMem32[RootBridgeIndex].Length = Descriptor->AddrLen;
            CurRbRes->PMem32[RootBridgeIndex].Alignment = Descriptor->AddrRangeMax;
          } else {
            // Mem32
            CurRbRes->Mem32[RootBridgeIndex].Base = Descriptor->AddrRangeMin;
            CurRbRes->Mem32[RootBridgeIndex].Length = Descriptor->AddrLen;
            CurRbRes->Mem32[RootBridgeIndex].Alignment = Descriptor->AddrRangeMax;
          }
        } else {
          if (Descriptor->SpecificFlag == EFI_ACPI_MEMORY_RESOURCE_SPECIFIC_FLAG_CACHEABLE_PREFETCHABLE) {
            // PMem64
            CurRbRes->PMem64[RootBridgeIndex].Base = Descriptor->AddrRangeMin;
            CurRbRes->PMem64[RootBridgeIndex].Length = Descriptor->AddrLen;
            CurRbRes->PMem64[RootBridgeIndex].Alignment = Descriptor->AddrRangeMax;
          } else {
            // Mem64
            CurRbRes->Mem64[RootBridgeIndex].Base = Descriptor->AddrRangeMin;
            CurRbRes->Mem64[RootBridgeIndex].Length = Descriptor->AddrLen;
            CurRbRes->Mem64[RootBridgeIndex].Alignment = Descriptor->AddrRangeMax;
          }
        }
        break;
      case ACPI_ADDRESS_SPACE_TYPE_IO:
        // IO
        CurRbRes->Io[RootBridgeIndex].Base = Descriptor->AddrRangeMin;
        CurRbRes->Io[RootBridgeIndex].Length = Descriptor->AddrLen;
        CurRbRes->Io[RootBridgeIndex].Alignment = Descriptor->AddrRangeMax;
        break;
      case ACPI_ADDRESS_SPACE_TYPE_BUS:
        // PCI BUS
        CurRbRes->PciBus[RootBridgeIndex].Base = Descriptor->AddrRangeMin;
        CurRbRes->PciBus[RootBridgeIndex].Length = Descriptor->AddrLen;
        CurRbRes->PciBus[RootBridgeIndex].Alignment = Descriptor->AddrRangeMax;
        break;
      default:
        break;
      }
    }
    RootBridgeIndex++;
    //
    // Skip the END descriptor for root bridge
    //
    ASSERT (Descriptor->Desc == ACPI_END_TAG_DESCRIPTOR);
    Descriptor = (EFI_ACPI_ADDRESS_SPACE_DESCRIPTOR *) ((EFI_ACPI_END_TAG_DESCRIPTOR *)Descriptor + 1);
  }
  PciSegmentNumberFixup (HostBridgeHandle, CurRbRes, RootBridgeIndex);

  // Init AmdResourceSize
  AmdInitResourceSize (CurRbRes, &AmdResourceSize, FabricTopology);
  FreePool (CurRbRes);

  //
  if (FabricResource->FabricReallocateResourceForEachRb (FabricResource, &AmdResourceSize, &SpaceStatus) == EFI_SUCCESS) {
    gRT->ResetSystem (EfiResetWarm, EFI_SUCCESS, 0, NULL);
  } else {
    if (SpaceStatus.MmioSizeBelow4GReqInc != 0) {
      //lower TOM
      TOM = AsmReadMsr64 (TOP_MEM);
      TOM = (TOM - SpaceStatus.MmioSizeBelow4GReqInc) & 0xF0000000; // TOM must be 256MB alignment

      Status = gBS->LocateProtocol (&gAmdApcbDxeServiceProtocolGuid, NULL, (VOID **)&ApcbDxeServiceProtocol);
      if (EFI_ERROR (Status)) {
        DEBUG ((EFI_D_INFO, " Locate AmdApcbDxeServiceProtocol failed\n"));
        return;
      }
      TomToken = TOM >> 24; // Set APCB_TOKEN_CONFIG_DF_BOTTOMIO to TOM[31:24]
      TokenSize = sizeof(UINT8);
      Status = ApcbDxeServiceProtocol->ApcbSetConfigParameter (ApcbDxeServiceProtocol, APCB_ID_CONFIG_DF_BOTTOMIO, &TokenSize, &TomToken);
      if (EFI_ERROR (Status)) {
        DEBUG ((EFI_D_INFO, " Set Token APCB_ID_CONFIG_DF_BOTTOMIO failed\n"));
        return;
      }
      Status = ApcbDxeServiceProtocol->ApcbFlushData (ApcbDxeServiceProtocol);
      if (EFI_ERROR (Status)) {
        DEBUG ((EFI_D_INFO, " Flush Data failed\n"));
        return;
      }
    }
    if (SpaceStatus.MmioSizeAbove4GReqInc != 0) {
      // Normally, MMIO above 4G should be enough. In case we run out of resource, put a debug message here
      DEBUG ((EFI_D_INFO, "  No enough MMIO space above 4G, need 0x%X more\n", SpaceStatus.MmioSizeAbove4GReqInc));
    }
    gRT->ResetSystem (EfiResetWarm, EFI_SUCCESS, 0, NULL);
  }
}

EFI_STATUS
AmdInitResourceSize (
  IN       ROOT_BRIDGE_RESOURCE                   *CurRbRes,
  IN OUT   FABRIC_RESOURCE_FOR_EACH_RB            *AmdResourceSize,
  IN       AMD_FABRIC_TOPOLOGY_SERVICES2_PROTOCOL *FabricTopology
  )
{
  UINTN                                 i;
  UINTN                                 SocketCount;
  UINTN                                 SocketLoop;
  UINTN                                 DiePerSocket;
  UINTN                                 DieLoop;
  UINTN                                 RbPerDie;
  UINTN                                 RbLoop;
  UINTN                                 PciBusBase[MAX_SOCKETS_SUPPORTED][MAX_RBS_PER_SOCKET];
  UINTN                                 PciBusLimit[MAX_SOCKETS_SUPPORTED][MAX_RBS_PER_SOCKET];
  BOOLEAN                               PciBusReassign;
  AMD_NBIO_CXL_SERVICES_PROTOCOL        *AmdNbioCxlServicesProtocol;
  UINTN                                 CxlCount;

  FabricTopology->GetSystemInfo (FabricTopology, &SocketCount, NULL, NULL, NULL, NULL);
  DiePerSocket = 1;
  RbPerDie = 1;
  // Zero out AmdResourceSize
  SetMem (AmdResourceSize, sizeof (FABRIC_RESOURCE_FOR_EACH_RB), 0);

  // Set PciBusBase to 0xFF
  SetMem (PciBusBase, (sizeof (UINTN) * MAX_SOCKETS_SUPPORTED * MAX_RBS_PER_SOCKET), 0xFF);

  // Zero out PciBusLimit
  SetMem (PciBusLimit, (sizeof (UINTN) * MAX_SOCKETS_SUPPORTED * MAX_RBS_PER_SOCKET), 0);

  // Get PciBusLimit for each RootBridge
  for (SocketLoop = 0; SocketLoop < SocketCount; SocketLoop++) {
    FabricTopology->GetProcessorInfo (FabricTopology, SocketLoop, &DiePerSocket, NULL);
    for (DieLoop = 0; DieLoop < DiePerSocket; DieLoop++) {
      FabricTopology->GetDieInfo (FabricTopology, SocketLoop, DieLoop, &RbPerDie, NULL, NULL);
      for (RbLoop = 0; RbLoop < RbPerDie; RbLoop++) {
        FabricTopology->GetRootBridgeInfo (FabricTopology, SocketLoop, DieLoop, RbLoop, NULL,
                                           &PciBusBase[SocketLoop][DieLoop * RbPerDie + RbLoop],
                                           &PciBusLimit[SocketLoop][DieLoop * RbPerDie + RbLoop],
                                           NULL, NULL, NULL);
      }
    }
  }
  PciBusReassign = FALSE;
  // By comparing CurRbRes->PciBusLimit with PciBusLimit get from FabricTopology, we could know Mem and IO resource is on which socket and which RootBridge
  for (i = 0; i < (MAX_SOCKETS_SUPPORTED * MAX_RBS_PER_SOCKET); i++) {
    if (CurRbRes->PciBus[i].Length != 0) {
      for (SocketLoop = 0; SocketLoop < SocketCount; SocketLoop++) {
        for (RbLoop = 0; RbLoop < (DiePerSocket * RbPerDie); RbLoop++) {
          if ((PciBusBase[SocketLoop][RbLoop] != 0xFF) && (CurRbRes->PciBus[i].Base == PciBusBase[SocketLoop][RbLoop])) {
            // Mem32
            AmdResourceSize->NonPrefetchableMmioSizeBelow4G[SocketLoop][RbLoop].Base = CurRbRes->Mem32[i].Base;
            AmdResourceSize->NonPrefetchableMmioSizeBelow4G[SocketLoop][RbLoop].Size = CurRbRes->Mem32[i].Length;
            AmdResourceSize->NonPrefetchableMmioSizeBelow4G[SocketLoop][RbLoop].Alignment = CurRbRes->Mem32[i].Alignment;
            // PMem32
            AmdResourceSize->PrefetchableMmioSizeBelow4G[SocketLoop][RbLoop].Base = CurRbRes->PMem32[i].Base;
            AmdResourceSize->PrefetchableMmioSizeBelow4G[SocketLoop][RbLoop].Size = CurRbRes->PMem32[i].Length;
            AmdResourceSize->PrefetchableMmioSizeBelow4G[SocketLoop][RbLoop].Alignment = CurRbRes->PMem32[i].Alignment;
            // Mem64
            AmdResourceSize->NonPrefetchableMmioSizeAbove4G[SocketLoop][RbLoop].Base = CurRbRes->Mem64[i].Base;
            AmdResourceSize->NonPrefetchableMmioSizeAbove4G[SocketLoop][RbLoop].Size = CurRbRes->Mem64[i].Length;
            AmdResourceSize->NonPrefetchableMmioSizeAbove4G[SocketLoop][RbLoop].Alignment = CurRbRes->Mem64[i].Alignment;
            // PMem64
            AmdResourceSize->PrefetchableMmioSizeAbove4G[SocketLoop][RbLoop].Base = CurRbRes->PMem64[i].Base;
            AmdResourceSize->PrefetchableMmioSizeAbove4G[SocketLoop][RbLoop].Size = CurRbRes->PMem64[i].Length;
            AmdResourceSize->PrefetchableMmioSizeAbove4G[SocketLoop][RbLoop].Alignment = CurRbRes->PMem64[i].Alignment;
            // IO
            AmdResourceSize->IO[SocketLoop][RbLoop].Base = CurRbRes->Io[i].Base;
            AmdResourceSize->IO[SocketLoop][RbLoop].Size = CurRbRes->Io[i].Length;
            AmdResourceSize->IO[SocketLoop][RbLoop].Alignment = CurRbRes->Io[i].Alignment;
            // PCI BUS
            // Compare with currently allocated number of PCI bus
            // dont downgrade/reduced the existing number of PCI bus
            if (CurRbRes->PciBus[i].Length <= (PciBusLimit[SocketLoop][RbLoop] - PciBusBase[SocketLoop][RbLoop] + 1)) {
              AmdResourceSize->PciBusNumber[SocketLoop][RbLoop] = (UINT16) (PciBusLimit[SocketLoop][RbLoop] - PciBusBase[SocketLoop][RbLoop] + 1);
            } else {
              DEBUG ((
                DEBUG_INFO,
                "Socket[%d]:RB[%d] number of PCI Bus allocated = 0x%x, requires 0x%x. reassigning...\n",
                SocketLoop,
                RbLoop,
                PciBusLimit[SocketLoop][RbLoop] - PciBusBase[SocketLoop][RbLoop] + 1,
                CurRbRes->PciBus[i].Length
                ));
              PciBusReassign = TRUE;
            }
          }
        }
      }
    }
  }
  if (PciBusReassign) {
    for (i = 0; i < (MAX_SOCKETS_SUPPORTED * MAX_RBS_PER_SOCKET); i++) {
      if (CurRbRes->PciBus[i].Length != 0) {
        for (SocketLoop = 0; SocketLoop < SocketCount; SocketLoop++) {
          for (RbLoop = 0; RbLoop < (DiePerSocket * RbPerDie); RbLoop++) {
            if ((PciBusBase[SocketLoop][RbLoop] != 0xFF) && (CurRbRes->PciBus[i].Base == PciBusBase[SocketLoop][RbLoop])) {
              // PCI BUS
              AmdResourceSize->PciBusNumber[SocketLoop][RbLoop] = (UINT16) CurRbRes->PciBus[i].Length;
            }
          }
        }
      }
    }
  }

  //CXL MMIO Resource fixup
  AmdNbioCxlServicesProtocol = NULL;
  gBS->LocateProtocol (&gAmdNbioCxlServicesProtocolGuid, NULL, (VOID **)&AmdNbioCxlServicesProtocol);
  if (AmdNbioCxlServicesProtocol != NULL) {
    CxlCount = AmdNbioCxlServicesProtocol->CxlCount;
  } else {
    CxlCount = 0;
  }
  if (CxlCount) {
    for (i = 0; i < (MAX_SOCKETS_SUPPORTED * MAX_RBS_PER_SOCKET); i++) {
      // Check if it is CXL 1.1 virtual bridge
      if (CurRbRes->PciBus[i].Length == 1) {
        for (SocketLoop = 0; SocketLoop < SocketCount; SocketLoop++) {
          for (RbLoop = 0; RbLoop < (DiePerSocket * RbPerDie); RbLoop++) {
            // Check which root bridge have the CXL virtual root bridge
            if ((CurRbRes->PciBus[i].Base > PciBusBase[SocketLoop][RbLoop]) && (CurRbRes->PciBus[i].Base <= PciBusLimit[SocketLoop][RbLoop])) {
              DEBUG ((EFI_D_INFO, "Socket[%d]:RB[%d] Fixup CXL Bus: %x\n", SocketLoop, RbLoop, CurRbRes->PciBus[i].Base));
              DEBUG ((EFI_D_INFO, " Mem32 Size/Alignment = %x/%x\n",
                        AmdResourceSize->NonPrefetchableMmioSizeBelow4G[SocketLoop][RbLoop].Size, AmdResourceSize->NonPrefetchableMmioSizeBelow4G[SocketLoop][RbLoop].Alignment));
              DEBUG ((EFI_D_INFO, " PMem32 Size/Alignment = %x/%x\n",
                        AmdResourceSize->PrefetchableMmioSizeBelow4G[SocketLoop][RbLoop].Size, AmdResourceSize->PrefetchableMmioSizeBelow4G[SocketLoop][RbLoop].Alignment));
              DEBUG ((EFI_D_INFO, " Mem64 Size/Alignment = %lx/%lx\n",
                        AmdResourceSize->NonPrefetchableMmioSizeAbove4G[SocketLoop][RbLoop].Size, AmdResourceSize->NonPrefetchableMmioSizeAbove4G[SocketLoop][RbLoop].Alignment));
              DEBUG ((EFI_D_INFO, " PMem64 Size/Alignment = %lx/%lx\n",
                        AmdResourceSize->PrefetchableMmioSizeAbove4G[SocketLoop][RbLoop].Size, AmdResourceSize->PrefetchableMmioSizeAbove4G[SocketLoop][RbLoop].Alignment));

              // Check if CXL need Mem32 resource
              if (CurRbRes->Mem32[i].Length != 0) {
                DEBUG ((EFI_D_INFO, "CXL Mem32 Length/Alginment = %x/%x\n", CurRbRes->Mem32[i].Length, CurRbRes->Mem32[i].Alignment));
                AmdResourceSize->NonPrefetchableMmioSizeBelow4G[SocketLoop][RbLoop].Alignment =
                                   MAX(CurRbRes->Mem32[i].Alignment, AmdResourceSize->NonPrefetchableMmioSizeBelow4G[SocketLoop][RbLoop].Alignment);
                AmdResourceSize->NonPrefetchableMmioSizeBelow4G[SocketLoop][RbLoop].Size =
                                   CurRbRes->Mem32[i].Length + ALIGN_VALUE (AmdResourceSize->NonPrefetchableMmioSizeBelow4G[SocketLoop][RbLoop].Size, CurRbRes->Mem32[i].Alignment + 1);
              }

              // Check if CXL need PMem32 resource
              if (CurRbRes->PMem32[i].Length != 0) {
                DEBUG ((EFI_D_INFO, "CXL PMem32 Length/Alginment = %x/%x\n", CurRbRes->PMem32[i].Length, CurRbRes->PMem32[i].Alignment));
                AmdResourceSize->PrefetchableMmioSizeBelow4G[SocketLoop][RbLoop].Alignment =
                                   MAX(CurRbRes->PMem32[i].Alignment, AmdResourceSize->PrefetchableMmioSizeBelow4G[SocketLoop][RbLoop].Alignment);
                AmdResourceSize->PrefetchableMmioSizeBelow4G[SocketLoop][RbLoop].Size =
                                   CurRbRes->PMem32[i].Length + ALIGN_VALUE (AmdResourceSize->PrefetchableMmioSizeBelow4G[SocketLoop][RbLoop].Size, CurRbRes->PMem32[i].Alignment + 1);
              }

              // Check if CXL need Mem64 resource
              if (CurRbRes->Mem64[i].Length != 0) {
                DEBUG ((EFI_D_INFO, "CXL Mem64 Length/Alginment = %lx/%lx\n", CurRbRes->Mem64[i].Length, CurRbRes->Mem64[i].Alignment));
                AmdResourceSize->NonPrefetchableMmioSizeAbove4G[SocketLoop][RbLoop].Alignment =
                                   MAX(CurRbRes->Mem64[i].Alignment, AmdResourceSize->NonPrefetchableMmioSizeAbove4G[SocketLoop][RbLoop].Alignment);
                AmdResourceSize->NonPrefetchableMmioSizeAbove4G[SocketLoop][RbLoop].Size =
                                   CurRbRes->Mem64[i].Length + ALIGN_VALUE (AmdResourceSize->NonPrefetchableMmioSizeAbove4G[SocketLoop][RbLoop].Size, CurRbRes->Mem64[i].Alignment + 1);
              }

              // Check if CXL need PMem64 resource
              if (CurRbRes->PMem64[i].Length != 0) {
                DEBUG ((EFI_D_INFO, "CXL PMem64 Length/Alginment = %lx/%lx\n", CurRbRes->PMem64[i].Length, CurRbRes->PMem64[i].Alignment));
                AmdResourceSize->PrefetchableMmioSizeAbove4G[SocketLoop][RbLoop].Alignment =
                                   MAX(CurRbRes->PMem64[i].Alignment, AmdResourceSize->PrefetchableMmioSizeAbove4G[SocketLoop][RbLoop].Alignment);
                AmdResourceSize->PrefetchableMmioSizeAbove4G[SocketLoop][RbLoop].Size =
                                   CurRbRes->PMem64[i].Length + ALIGN_VALUE (AmdResourceSize->PrefetchableMmioSizeAbove4G[SocketLoop][RbLoop].Size, CurRbRes->PMem64[i].Alignment + 1);
              }

              // Dump Root Bridge MMIO Resource
              DEBUG ((EFI_D_INFO, " After CXL MMIO resource adjustment\n"));
              DEBUG ((EFI_D_INFO, " Mem32 Size/Alignment = %x/%x\n",
                        AmdResourceSize->NonPrefetchableMmioSizeBelow4G[SocketLoop][RbLoop].Size, AmdResourceSize->NonPrefetchableMmioSizeBelow4G[SocketLoop][RbLoop].Alignment));
              DEBUG ((EFI_D_INFO, " PMem32 Size/Alignment = %x/%x\n",
                        AmdResourceSize->PrefetchableMmioSizeBelow4G[SocketLoop][RbLoop].Size, AmdResourceSize->PrefetchableMmioSizeBelow4G[SocketLoop][RbLoop].Alignment));
              DEBUG ((EFI_D_INFO, " Mem64 Size/Alignment = %lx/%lx\n",
                        AmdResourceSize->NonPrefetchableMmioSizeAbove4G[SocketLoop][RbLoop].Size, AmdResourceSize->NonPrefetchableMmioSizeAbove4G[SocketLoop][RbLoop].Alignment));
              DEBUG ((EFI_D_INFO, " PMem64 Size/Alignment = %lx/%lx\n",
                        AmdResourceSize->PrefetchableMmioSizeAbove4G[SocketLoop][RbLoop].Size, AmdResourceSize->PrefetchableMmioSizeAbove4G[SocketLoop][RbLoop].Alignment));
            }
          }
        }
      }
    }
  }

  return EFI_SUCCESS;
}

