<component>
    name = "Protocol"
    category = ModulePart
    LocalRoot = "AgesaModulePkg\Include\Protocol\"
    RefName = "AgesaModulePkg.Include.Protocol"
[files]
"AmdAcpiCditServicesProtocol.h"
"AmdAcpiCppcServicesProtocol.h"
"AmdAcpiCpuSsdtServicesProtocol.h"
"AmdAcpiCratServicesProtocol.h"
"AmdAcpiHmatServicesProtocol.h"
"AmdAcpiMsctServicesProtocol.h"
"AmdAcpiPcctServicesProtocol.h"
"AmdAcpiRasServicesProtocol.h"
"AmdAcpiSlitServicesProtocol.h"
"AmdAcpiSlitServicesV2Protocol.h"
"AmdAcpiSratServicesProtocol.h"
"AmdAcpiSratServicesProtocolCommon.h"
"AmdAcpiSratServicesV2Protocol.h"
"AmdApcbProtocol.h"
"AmdCapsuleSmmHookProtocol.h"
"AmdCcxProtocol.h"
"AmdCoreTopologyProtocol.h"
"AmdCoreTopologyV2Protocol.h"
"AmdCoreTopologyV3Protocol.h"
"AmdErrorLogProtocol.h"
"AmdI3cMasterConsumerSPD5Protocol.h"
"AmdIdsDebugPrintProtocol.h"
"AmdMemChanXlatProtocol.h"
"AmdMemPprProtocol.h"
"AmdNbioBaseServicesProtocol.h"
"AmdNbioCoreRankingTableServicesProtocol.h"
"AmdNbioCppcServicesProtocol.h"
"AmdNbioPcieServicesProtocol.h"
"AmdNbioServicesProtocol.h"
"AmdNbioSmuServicesProtocol.h"
"AmdPspArsServiceProtocol.h"
"AmdPspFirmwareVersionProtocol.h"
"AmdPspRuntimePprServiceProtocol.h"
"AmdSmbiosServicesProtocol.h"
"ApobCommonServiceProtocol.h"
"CcxBaseServicesProtocol.h"
"FabricNumaServices2.h"
"FabricNumaServicesProtocol.h"
"FabricTopologyServices2.h"
"FchEspiCmdProtocol.h"
"FchI3cMasterProtocol.h"
"FchI3cProtocol.h"
"FchInitDonePolicyProtocol.h"
"FchInitProtocol.h"
"FchMultiFchInitProtocol.h"
"FchSmmApuRasDispatch.h"
"FchSmmI2cMasterProtocol.h"
"FchSmmInitProtocol.h"
"PspMboxSmmBufferAddressProtocol.h"
"PspMboxSmmReadyToSendCmdProtocol.h"
"SocLogicalIdProtocol.h"
"SocZen5ServicesProtocol.h"
<endComponent>
