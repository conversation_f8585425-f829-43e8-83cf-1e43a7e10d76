/*****************************************************************************
 * Copyright (C) 2008-2024 Advanced Micro Devices, Inc. All rights reserved.
 *
*****************************************************************************
*/

#ifndef _AMD_NBIO_CORE_RANKING_TABLE_SERVICES_PROTOCOL_H_
#define _AMD_NBIO_CORE_RANKING_TABLE_SERVICES_PROTOCOL_H_


// Current PROTOCOL revision
#define DXE_AMD_NBIO_CORE_RANKING_TABLE_SERVICES_DXE_REVISION   0x00

///
/// Forward declaration for the NBIO_CORE_RANKING_TABLE_SERVICES_PROTOCOL
///
typedef struct _DXE_AMD_NBIO_CORE_RANKING_TABLE_SERVICES_PROTOCOL DXE_AMD_NBIO_CORE_RANKING_TABLE_SERVICES_PROTOCOL;

///
/// Core Ranking Table Info Structure
///
typedef struct {
  BOOLEAN              IsEnabled;          ///< Is CoreRankingTable enabled
  UINT32               CoreRankingTableVersionInfo;        ///< CoreRankingTable version info
} AMD_CORE_RANKING_TABLE_INFO;

typedef struct {
  UINT64               DoorbellRegister;
  UINT64               PlatformInterruptAckRegister;
  UINT64               CommandCompleteCheckRegister;
  UINT64               ErrorStatusRegister;
} PCCT_SUBSPACES_TYPE4_REGISTERS_STRUCT;

typedef struct {
  UINT32               Signature; // The PCC signature. The signature of a subspace is computed by a bitwiseor of the value 0x50434300 with the subspace ID.
  UINT32               Flags;     // bit 0 - Notify on completion.
  UINT32               Length;    // Length of payload being transmitted including command field.
  UINT32               Command;   // Command being sent over the subspace.
} EXTENDED_PCC_SUBSPACES_SHARED_MEMORY_REGION_HEADER_STRUCT;

typedef struct {
  UINT8                VersionNumber;                 // version number of the table
  UINT8                NumberOfLogicalProcessors;     // number of logical processors
  UINT8                NumberOfRankingDimensions;     // number of ranking dimensions (i.e. performance, efficiency, etc)
  UINT8                TableUpdateContext;            // table update context
  UINT8                NumberOf32bitBitmaps;          // Number of 32-bit Bitmaps to enumerate all the APIC IDs. This is based on the Maximum Apic ID enumerated in the system
  UINT8                NumberOfClasses;               // Number of Classes
  UINT8                Reserved[2];                   // Spare
} MEMORY_MAPPED_RANKING_TABLE_HEADER_STRUCT;

//
// The Structure appends at the end of the Hetero SSDT for the _DSM method
// for the OS to check the PCCT subspace table they are using are the right one.
//
typedef struct {
  UINT32               SharedMemoryRegionSubspaceId;  // The PCC signature lowest byte is subspace ID, the 0x50434300 subspace ID is 0, the 0x50434303 subspace ID is 3.
  UINT32               SharedMemoryRegionLength;      // Length of payload being transmitted including command field.
  // The OSPM driver will check the shared memory region size, the size is the total shared memory size to subtract the shared memory header 0x10 then add the command 4 bytes.
  // The PCCT subspace type 4 Memory Length is total shared memory size, the Length in the Initiator Responder Communications Channel Shared Memory Region is the total size to subtract 12 bytes.
  UINT8                DynamicRankingTableSupport;    // The CPUID Fn80000027 EBX bit 0-3 Dynamic Ranking Table Support
} HETERO_DSM_ACPI_VARIABLE_STRUCT;

//
// Protocol Definitions
//

/**
  Get Core Ranking Table info

  @param This                   Instance of this protocol
  @param CoreRankingTableInfo   Core Ranking Table info structure

  @return EFI_STATUS   Returns EFI_SUCCESS, unless NULL inputs (EFI_INVALID_PARAMETER) or RankingTable broken/off (EFI_NOT_FOUND)
**/
typedef
EFI_STATUS
(EFIAPI * AMD_CORE_RANKING_TABLE_SERVICES_GET_INFO) (
  IN   DXE_AMD_NBIO_CORE_RANKING_TABLE_SERVICES_PROTOCOL   *This,
  OUT  AMD_CORE_RANKING_TABLE_INFO                         *CoreRankingTableInfo
  );

/**
  Send location of Core Ranking Table shared memory region to SMU

  @param SharedRegionStart  Location of first byte of table in memory

  @return EFI_STATUS        Always returns EFI_SUCCESS
**/
typedef
EFI_STATUS
(EFIAPI * AMD_CORE_RANKING_TABLE_SERVICES_SET_TABLE) (
  IN  EFI_PHYSICAL_ADDRESS                     SharedRegionStart,
  IN  UINT32                                   TableSize,
  IN  PCCT_SUBSPACES_TYPE4_REGISTERS_STRUCT   *PcctSubspaceType4Registers
  );


///
/// The Protocol Prototype of Core Ranking Table Services
///
struct _DXE_AMD_NBIO_CORE_RANKING_TABLE_SERVICES_PROTOCOL {
  UINT32                                    Revision;            ///< revision
  AMD_CORE_RANKING_TABLE_SERVICES_GET_INFO                CoreRankingTableGetInfo;   ///< Get CoreRankingTable Info
  AMD_CORE_RANKING_TABLE_SERVICES_SET_TABLE               SetTable;                  ///< Set location of shared memory
};

extern EFI_GUID gAmdNbioCoreRankingTableServicesProtocolGuid;

#endif /* _AMD_NBIO_CORE_RANKING_TABLE_SERVICES_PROTOCOL_H_ */


