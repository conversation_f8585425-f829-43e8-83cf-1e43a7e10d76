/*****************************************************************************
 *
 * Copyright (C) 2008-2024 Advanced Micro Devices, Inc. All rights reserved.
 *
 *******************************************************************************
 **/

#ifndef _IOAPIC_H_
#define _IOAPIC_H_


/***********************************************************
* Register Name : EOI_REGISTER
************************************************************/

#define EOI_REGISTER_Vector_OFFSET                             0
#define EOI_REGISTER_Vector_MASK                               0xff

#define EOI_REGISTER_Reserved_31_8_OFFSET                      8
#define EOI_REGISTER_Reserved_31_8_MASK                        0xffffff00

typedef union {
  struct {
    UINT32                                              Vector:8;
    UINT32                                       Reserved_31_8:24;
  } Field;
  UINT32 Value;
} EOI_REGISTER_STRUCT;

#define SMN_EOI_REGISTER_ADDRESS                                      0x2800040UL
#define SMN_IOHUB0NBIO0_EOI_REGISTER_ADDRESS                          0x2800040UL
#define SMN_IOHUB0NBIO1_EOI_REGISTER_ADDRESS                          0x2a00040UL
#define SMN_IOHUB1NBIO0_EOI_REGISTER_ADDRESS                          0x1d000040UL
#define SMN_IOHUB1NBIO1_EOI_REGISTER_ADDRESS                          0x1d200040UL
#define SMN_IOHUB2NBIO0_EOI_REGISTER_ADDRESS                          0x2900040UL
#define SMN_IOHUB2NBIO1_EOI_REGISTER_ADDRESS                          0x2b00040UL
#define SMN_IOHUB3NBIO0_EOI_REGISTER_ADDRESS                          0x1d100040UL
#define SMN_IOHUB3NBIO1_EOI_REGISTER_ADDRESS                          0x1d300040UL


/***********************************************************
* Register Name : FEATURES_ENABLE
************************************************************/

#define FEATURES_ENABLE_Reserved_1_0_OFFSET                    0
#define FEATURES_ENABLE_Reserved_1_0_MASK                      0x3

#define FEATURES_ENABLE_Ioapic_id_ext_en_OFFSET                2
#define FEATURES_ENABLE_Ioapic_id_ext_en_MASK                  0x4

#define FEATURES_ENABLE_Reserved_3_3_OFFSET                    3
#define FEATURES_ENABLE_Reserved_3_3_MASK                      0x8

#define FEATURES_ENABLE_Ioapic_sb_feature_en_OFFSET            4
#define FEATURES_ENABLE_Ioapic_sb_feature_en_MASK              0x10

#define FEATURES_ENABLE_Ioapic_secondary_en_OFFSET             5
#define FEATURES_ENABLE_Ioapic_secondary_en_MASK               0x20

#define FEATURES_ENABLE_Reserved_7_6_OFFSET                    6
#define FEATURES_ENABLE_Reserved_7_6_MASK                      0xc0

#define FEATURES_ENABLE_Ioapic_processor_mode_OFFSET           8
#define FEATURES_ENABLE_Ioapic_processor_mode_MASK             0x100

#define FEATURES_ENABLE_INTx_LevelOnlyMode_OFFSET              9
#define FEATURES_ENABLE_INTx_LevelOnlyMode_MASK                0x200

#define FEATURES_ENABLE_Reserved_31_10_OFFSET                  10
#define FEATURES_ENABLE_Reserved_31_10_MASK                    0xfffffc00

typedef union {
  struct {
    UINT32                                        Reserved_1_0:2;
    UINT32                                    Ioapic_id_ext_en:1;
    UINT32                                        Reserved_3_3:1;
    UINT32                                Ioapic_sb_feature_en:1;
    UINT32                                 Ioapic_secondary_en:1;
    UINT32                                        Reserved_7_6:2;
    UINT32                               Ioapic_processor_mode:1;
    UINT32                                  INTx_LevelOnlyMode:1;
    UINT32                                      Reserved_31_10:22;
  } Field;
  UINT32 Value;
} FEATURES_ENABLE_STRUCT;

#define SMN_FEATURES_ENABLE_ADDRESS                                   0x14300000UL
#define SMN_IOHUB0NBIO0_FEATURES_ENABLE_ADDRESS                       0x14300000UL
#define SMN_IOHUB0NBIO1_FEATURES_ENABLE_ADDRESS                       0x14500000UL
#define SMN_IOHUB1NBIO0_FEATURES_ENABLE_ADDRESS                       0x1d800000UL
#define SMN_IOHUB1NBIO1_FEATURES_ENABLE_ADDRESS                       0x1da00000UL
#define SMN_IOHUB2NBIO0_FEATURES_ENABLE_ADDRESS                       0x14400000UL
#define SMN_IOHUB2NBIO1_FEATURES_ENABLE_ADDRESS                       0x14600000UL
#define SMN_IOHUB3NBIO0_FEATURES_ENABLE_ADDRESS                       0x1d900000UL
#define SMN_IOHUB3NBIO1_FEATURES_ENABLE_ADDRESS                       0x1db00000UL


/***********************************************************
* Register Name : IOAPICMIO_DATA
************************************************************/

#define IOAPICMIO_DATA_IOAPICMIO_DATA_OFFSET                   0
#define IOAPICMIO_DATA_IOAPICMIO_DATA_MASK                     0xffffffff

typedef union {
  struct {
    UINT32                                      IOAPICMIO_DATA:32;
  } Field;
  UINT32 Value;
} IOAPICMIO_DATA_STRUCT;

#define SMN_IOAPICMIO_DATA_ADDRESS                                    0x2800010UL
#define SMN_IOHUB0NBIO0_IOAPICMIO_DATA_ADDRESS                        0x2800010UL
#define SMN_IOHUB0NBIO1_IOAPICMIO_DATA_ADDRESS                        0x2a00010UL
#define SMN_IOHUB1NBIO0_IOAPICMIO_DATA_ADDRESS                        0x1d000010UL
#define SMN_IOHUB1NBIO1_IOAPICMIO_DATA_ADDRESS                        0x1d200010UL
#define SMN_IOHUB2NBIO0_IOAPICMIO_DATA_ADDRESS                        0x2900010UL
#define SMN_IOHUB2NBIO1_IOAPICMIO_DATA_ADDRESS                        0x2b00010UL
#define SMN_IOHUB3NBIO0_IOAPICMIO_DATA_ADDRESS                        0x1d100010UL
#define SMN_IOHUB3NBIO1_IOAPICMIO_DATA_ADDRESS                        0x1d300010UL


/***********************************************************
* Register Name : IOAPICMIO_INDEX
************************************************************/

#define IOAPICMIO_INDEX_IOAPICMIO_INDEX_data_OFFSET            0
#define IOAPICMIO_INDEX_IOAPICMIO_INDEX_data_MASK              0xffffffff

typedef union {
  struct {
    UINT32                                IOAPICMIO_INDEX_data:32;
  } Field;
  UINT32 Value;
} IOAPICMIO_INDEX_STRUCT;

#define SMN_IOAPICMIO_INDEX_ADDRESS                                   0x2800000UL
#define SMN_IOHUB0NBIO0_IOAPICMIO_INDEX_ADDRESS                       0x2800000UL
#define SMN_IOHUB0NBIO1_IOAPICMIO_INDEX_ADDRESS                       0x2a00000UL
#define SMN_IOHUB1NBIO0_IOAPICMIO_INDEX_ADDRESS                       0x1d000000UL
#define SMN_IOHUB1NBIO1_IOAPICMIO_INDEX_ADDRESS                       0x1d200000UL
#define SMN_IOHUB2NBIO0_IOAPICMIO_INDEX_ADDRESS                       0x2900000UL
#define SMN_IOHUB2NBIO1_IOAPICMIO_INDEX_ADDRESS                       0x2b00000UL
#define SMN_IOHUB3NBIO0_IOAPICMIO_INDEX_ADDRESS                       0x1d100000UL
#define SMN_IOHUB3NBIO1_IOAPICMIO_INDEX_ADDRESS                       0x1d300000UL


/***********************************************************
* Register Name : IOAPIC_APERTURE_ID
************************************************************/

#define IOAPIC_APERTURE_ID_IoapicCfg_aperture_id_OFFSET        0
#define IOAPIC_APERTURE_ID_IoapicCfg_aperture_id_MASK          0xfff

#define IOAPIC_APERTURE_ID_Reserved_15_12_OFFSET               12
#define IOAPIC_APERTURE_ID_Reserved_15_12_MASK                 0xf000

#define IOAPIC_APERTURE_ID_IoapicMMIO_aperture_id_OFFSET       16
#define IOAPIC_APERTURE_ID_IoapicMMIO_aperture_id_MASK         0xfff0000

#define IOAPIC_APERTURE_ID_Reserved_31_28_OFFSET               28
#define IOAPIC_APERTURE_ID_Reserved_31_28_MASK                 0xf0000000

typedef union {
  struct {
    UINT32                               IoapicCfg_aperture_id:12;
    UINT32                                      Reserved_15_12:4;
    UINT32                              IoapicMMIO_aperture_id:12;
    UINT32                                      Reserved_31_28:4;
  } Field;
  UINT32 Value;
} IOAPIC_APERTURE_ID_STRUCT;

#define SMN_IOAPIC_APERTURE_ID_ADDRESS                                0x14300104UL
#define SMN_IOHUB0NBIO0_IOAPIC_APERTURE_ID_ADDRESS                    0x14300104UL
#define SMN_IOHUB0NBIO1_IOAPIC_APERTURE_ID_ADDRESS                    0x14500104UL
#define SMN_IOHUB1NBIO0_IOAPIC_APERTURE_ID_ADDRESS                    0x1d800104UL
#define SMN_IOHUB1NBIO1_IOAPIC_APERTURE_ID_ADDRESS                    0x1da00104UL
#define SMN_IOHUB2NBIO0_IOAPIC_APERTURE_ID_ADDRESS                    0x14400104UL
#define SMN_IOHUB2NBIO1_IOAPIC_APERTURE_ID_ADDRESS                    0x14600104UL
#define SMN_IOHUB3NBIO0_IOAPIC_APERTURE_ID_ADDRESS                    0x1d900104UL
#define SMN_IOHUB3NBIO1_IOAPIC_APERTURE_ID_ADDRESS                    0x1db00104UL


/***********************************************************
* Register Name : IOAPIC_ARBITRATION_REGISTER
************************************************************/

#define IOAPIC_ARBITRATION_REGISTER_Reserved_23_0_OFFSET       0
#define IOAPIC_ARBITRATION_REGISTER_Reserved_23_0_MASK         0xffffff

#define IOAPIC_ARBITRATION_REGISTER_Arbitration_ID_OFFSET      24
#define IOAPIC_ARBITRATION_REGISTER_Arbitration_ID_MASK        0xf000000

#define IOAPIC_ARBITRATION_REGISTER_Reserved_31_28_OFFSET      28
#define IOAPIC_ARBITRATION_REGISTER_Reserved_31_28_MASK        0xf0000000

typedef union {
  struct {
    UINT32                                       Reserved_23_0:24;
    UINT32                                      Arbitration_ID:4;
    UINT32                                      Reserved_31_28:4;
  } Field;
  UINT32 Value;
} IOAPIC_ARBITRATION_REGISTER_STRUCT;

#define SMN_IOAPIC_ARBITRATION_REGISTER_ADDRESS                       0x2801008UL
#define SMN_IOHUB0NBIO0_IOAPIC_ARBITRATION_REGISTER_ADDRESS           0x2801008UL
#define SMN_IOHUB0NBIO1_IOAPIC_ARBITRATION_REGISTER_ADDRESS           0x2a01008UL
#define SMN_IOHUB1NBIO0_IOAPIC_ARBITRATION_REGISTER_ADDRESS           0x1d001008UL
#define SMN_IOHUB1NBIO1_IOAPIC_ARBITRATION_REGISTER_ADDRESS           0x1d201008UL
#define SMN_IOHUB2NBIO0_IOAPIC_ARBITRATION_REGISTER_ADDRESS           0x2901008UL
#define SMN_IOHUB2NBIO1_IOAPIC_ARBITRATION_REGISTER_ADDRESS           0x2b01008UL
#define SMN_IOHUB3NBIO0_IOAPIC_ARBITRATION_REGISTER_ADDRESS           0x1d101008UL
#define SMN_IOHUB3NBIO1_IOAPIC_ARBITRATION_REGISTER_ADDRESS           0x1d301008UL


/***********************************************************
* Register Name : IOAPIC_BR_INTERRUPT_ROUTING
************************************************************/

#define IOAPIC_BR_INTERRUPT_ROUTING_Br_ext_Intr_grp_OFFSET     0
#define IOAPIC_BR_INTERRUPT_ROUTING_Br_ext_Intr_grp_MASK       0x7

#define IOAPIC_BR_INTERRUPT_ROUTING_Reserved_3_3_OFFSET        3
#define IOAPIC_BR_INTERRUPT_ROUTING_Reserved_3_3_MASK          0x8

#define IOAPIC_BR_INTERRUPT_ROUTING_Br_ext_Intr_swz_OFFSET     4
#define IOAPIC_BR_INTERRUPT_ROUTING_Br_ext_Intr_swz_MASK       0x30

#define IOAPIC_BR_INTERRUPT_ROUTING_Reserved_15_6_OFFSET       6
#define IOAPIC_BR_INTERRUPT_ROUTING_Reserved_15_6_MASK         0xffc0

#define IOAPIC_BR_INTERRUPT_ROUTING_Br_ext_Intr_map_OFFSET     16
#define IOAPIC_BR_INTERRUPT_ROUTING_Br_ext_Intr_map_MASK       0x1f0000

#define IOAPIC_BR_INTERRUPT_ROUTING_Reserved_31_21_OFFSET      21
#define IOAPIC_BR_INTERRUPT_ROUTING_Reserved_31_21_MASK        0xffe00000

typedef union {
  struct {
    UINT32                                     Br_ext_Intr_grp:3;
    UINT32                                        Reserved_3_3:1;
    UINT32                                     Br_ext_Intr_swz:2;
    UINT32                                       Reserved_15_6:10;
    UINT32                                     Br_ext_Intr_map:5;
    UINT32                                      Reserved_31_21:11;
  } Field;
  UINT32 Value;
} IOAPIC_BR_INTERRUPT_ROUTING_STRUCT;

#define SMN_IOAPIC_BR_INTERRUPT_ROUTING_ADDRESS                       0x14300040UL
#define SMN_IOHUB0_N0NBIO0_IOAPIC_BR_INTERRUPT_ROUTING_ADDRESS        0x14300040UL
#define SMN_IOHUB0_N0NBIO1_IOAPIC_BR_INTERRUPT_ROUTING_ADDRESS        0x14500040UL
#define SMN_IOHUB0_N10NBIO0_IOAPIC_BR_INTERRUPT_ROUTING_ADDRESS       0x14300068UL
#define SMN_IOHUB0_N10NBIO1_IOAPIC_BR_INTERRUPT_ROUTING_ADDRESS       0x14500068UL
#define SMN_IOHUB0_N11NBIO0_IOAPIC_BR_INTERRUPT_ROUTING_ADDRESS       0x1430006cUL
#define SMN_IOHUB0_N11NBIO1_IOAPIC_BR_INTERRUPT_ROUTING_ADDRESS       0x1450006cUL
#define SMN_IOHUB0_N12NBIO0_IOAPIC_BR_INTERRUPT_ROUTING_ADDRESS       0x14300070UL
#define SMN_IOHUB0_N12NBIO1_IOAPIC_BR_INTERRUPT_ROUTING_ADDRESS       0x14500070UL
#define SMN_IOHUB0_N13NBIO0_IOAPIC_BR_INTERRUPT_ROUTING_ADDRESS       0x14300074UL
#define SMN_IOHUB0_N13NBIO1_IOAPIC_BR_INTERRUPT_ROUTING_ADDRESS       0x14500074UL
#define SMN_IOHUB0_N14NBIO0_IOAPIC_BR_INTERRUPT_ROUTING_ADDRESS       0x14300078UL
#define SMN_IOHUB0_N14NBIO1_IOAPIC_BR_INTERRUPT_ROUTING_ADDRESS       0x14500078UL
#define SMN_IOHUB0_N15NBIO0_IOAPIC_BR_INTERRUPT_ROUTING_ADDRESS       0x1430007cUL
#define SMN_IOHUB0_N15NBIO1_IOAPIC_BR_INTERRUPT_ROUTING_ADDRESS       0x1450007cUL
#define SMN_IOHUB0_N16NBIO0_IOAPIC_BR_INTERRUPT_ROUTING_ADDRESS       0x14300080UL
#define SMN_IOHUB0_N16NBIO1_IOAPIC_BR_INTERRUPT_ROUTING_ADDRESS       0x14500080UL
#define SMN_IOHUB0_N17NBIO0_IOAPIC_BR_INTERRUPT_ROUTING_ADDRESS       0x14300084UL
#define SMN_IOHUB0_N17NBIO1_IOAPIC_BR_INTERRUPT_ROUTING_ADDRESS       0x14500084UL
#define SMN_IOHUB0_N18NBIO0_IOAPIC_BR_INTERRUPT_ROUTING_ADDRESS       0x14300088UL
#define SMN_IOHUB0_N18NBIO1_IOAPIC_BR_INTERRUPT_ROUTING_ADDRESS       0x14500088UL
#define SMN_IOHUB0_N19NBIO0_IOAPIC_BR_INTERRUPT_ROUTING_ADDRESS       0x1430008cUL
#define SMN_IOHUB0_N19NBIO1_IOAPIC_BR_INTERRUPT_ROUTING_ADDRESS       0x1450008cUL
#define SMN_IOHUB0_N1NBIO0_IOAPIC_BR_INTERRUPT_ROUTING_ADDRESS        0x14300044UL
#define SMN_IOHUB0_N1NBIO1_IOAPIC_BR_INTERRUPT_ROUTING_ADDRESS        0x14500044UL
#define SMN_IOHUB0_N20NBIO0_IOAPIC_BR_INTERRUPT_ROUTING_ADDRESS       0x14300090UL
#define SMN_IOHUB0_N20NBIO1_IOAPIC_BR_INTERRUPT_ROUTING_ADDRESS       0x14500090UL
#define SMN_IOHUB0_N21NBIO0_IOAPIC_BR_INTERRUPT_ROUTING_ADDRESS       0x14300094UL
#define SMN_IOHUB0_N21NBIO1_IOAPIC_BR_INTERRUPT_ROUTING_ADDRESS       0x14500094UL
#define SMN_IOHUB0_N2NBIO0_IOAPIC_BR_INTERRUPT_ROUTING_ADDRESS        0x14300048UL
#define SMN_IOHUB0_N2NBIO1_IOAPIC_BR_INTERRUPT_ROUTING_ADDRESS        0x14500048UL
#define SMN_IOHUB0_N3NBIO0_IOAPIC_BR_INTERRUPT_ROUTING_ADDRESS        0x1430004cUL
#define SMN_IOHUB0_N3NBIO1_IOAPIC_BR_INTERRUPT_ROUTING_ADDRESS        0x1450004cUL
#define SMN_IOHUB0_N4NBIO0_IOAPIC_BR_INTERRUPT_ROUTING_ADDRESS        0x14300050UL
#define SMN_IOHUB0_N4NBIO1_IOAPIC_BR_INTERRUPT_ROUTING_ADDRESS        0x14500050UL
#define SMN_IOHUB0_N5NBIO0_IOAPIC_BR_INTERRUPT_ROUTING_ADDRESS        0x14300054UL
#define SMN_IOHUB0_N5NBIO1_IOAPIC_BR_INTERRUPT_ROUTING_ADDRESS        0x14500054UL
#define SMN_IOHUB0_N6NBIO0_IOAPIC_BR_INTERRUPT_ROUTING_ADDRESS        0x14300058UL
#define SMN_IOHUB0_N6NBIO1_IOAPIC_BR_INTERRUPT_ROUTING_ADDRESS        0x14500058UL
#define SMN_IOHUB0_N7NBIO0_IOAPIC_BR_INTERRUPT_ROUTING_ADDRESS        0x1430005cUL
#define SMN_IOHUB0_N7NBIO1_IOAPIC_BR_INTERRUPT_ROUTING_ADDRESS        0x1450005cUL
#define SMN_IOHUB0_N8NBIO0_IOAPIC_BR_INTERRUPT_ROUTING_ADDRESS        0x14300060UL
#define SMN_IOHUB0_N8NBIO1_IOAPIC_BR_INTERRUPT_ROUTING_ADDRESS        0x14500060UL
#define SMN_IOHUB0_N9NBIO0_IOAPIC_BR_INTERRUPT_ROUTING_ADDRESS        0x14300064UL
#define SMN_IOHUB0_N9NBIO1_IOAPIC_BR_INTERRUPT_ROUTING_ADDRESS        0x14500064UL
#define SMN_IOHUB1_N0NBIO0_IOAPIC_BR_INTERRUPT_ROUTING_ADDRESS        0x1d800040UL
#define SMN_IOHUB1_N0NBIO1_IOAPIC_BR_INTERRUPT_ROUTING_ADDRESS        0x1da00040UL
#define SMN_IOHUB1_N1NBIO0_IOAPIC_BR_INTERRUPT_ROUTING_ADDRESS        0x1d800044UL
#define SMN_IOHUB1_N1NBIO1_IOAPIC_BR_INTERRUPT_ROUTING_ADDRESS        0x1da00044UL
#define SMN_IOHUB1_N2NBIO0_IOAPIC_BR_INTERRUPT_ROUTING_ADDRESS        0x1d800048UL
#define SMN_IOHUB1_N2NBIO1_IOAPIC_BR_INTERRUPT_ROUTING_ADDRESS        0x1da00048UL
#define SMN_IOHUB1_N3NBIO0_IOAPIC_BR_INTERRUPT_ROUTING_ADDRESS        0x1d80004cUL
#define SMN_IOHUB1_N3NBIO1_IOAPIC_BR_INTERRUPT_ROUTING_ADDRESS        0x1da0004cUL
#define SMN_IOHUB1_N4NBIO0_IOAPIC_BR_INTERRUPT_ROUTING_ADDRESS        0x1d800050UL
#define SMN_IOHUB1_N4NBIO1_IOAPIC_BR_INTERRUPT_ROUTING_ADDRESS        0x1da00050UL
#define SMN_IOHUB1_N5NBIO0_IOAPIC_BR_INTERRUPT_ROUTING_ADDRESS        0x1d800054UL
#define SMN_IOHUB1_N5NBIO1_IOAPIC_BR_INTERRUPT_ROUTING_ADDRESS        0x1da00054UL
#define SMN_IOHUB1_N6NBIO0_IOAPIC_BR_INTERRUPT_ROUTING_ADDRESS        0x1d800058UL
#define SMN_IOHUB1_N6NBIO1_IOAPIC_BR_INTERRUPT_ROUTING_ADDRESS        0x1da00058UL
#define SMN_IOHUB1_N7NBIO0_IOAPIC_BR_INTERRUPT_ROUTING_ADDRESS        0x1d80005cUL
#define SMN_IOHUB1_N7NBIO1_IOAPIC_BR_INTERRUPT_ROUTING_ADDRESS        0x1da0005cUL
#define SMN_IOHUB1_N8NBIO0_IOAPIC_BR_INTERRUPT_ROUTING_ADDRESS        0x1d800060UL
#define SMN_IOHUB1_N8NBIO1_IOAPIC_BR_INTERRUPT_ROUTING_ADDRESS        0x1da00060UL
#define SMN_IOHUB2_N0NBIO0_IOAPIC_BR_INTERRUPT_ROUTING_ADDRESS        0x14400040UL
#define SMN_IOHUB2_N0NBIO1_IOAPIC_BR_INTERRUPT_ROUTING_ADDRESS        0x14600040UL
#define SMN_IOHUB2_N10NBIO0_IOAPIC_BR_INTERRUPT_ROUTING_ADDRESS       0x14400068UL
#define SMN_IOHUB2_N10NBIO1_IOAPIC_BR_INTERRUPT_ROUTING_ADDRESS       0x14600068UL
#define SMN_IOHUB2_N11NBIO0_IOAPIC_BR_INTERRUPT_ROUTING_ADDRESS       0x1440006cUL
#define SMN_IOHUB2_N11NBIO1_IOAPIC_BR_INTERRUPT_ROUTING_ADDRESS       0x1460006cUL
#define SMN_IOHUB2_N12NBIO0_IOAPIC_BR_INTERRUPT_ROUTING_ADDRESS       0x14400070UL
#define SMN_IOHUB2_N12NBIO1_IOAPIC_BR_INTERRUPT_ROUTING_ADDRESS       0x14600070UL
#define SMN_IOHUB2_N13NBIO0_IOAPIC_BR_INTERRUPT_ROUTING_ADDRESS       0x14400074UL
#define SMN_IOHUB2_N13NBIO1_IOAPIC_BR_INTERRUPT_ROUTING_ADDRESS       0x14600074UL
#define SMN_IOHUB2_N14NBIO0_IOAPIC_BR_INTERRUPT_ROUTING_ADDRESS       0x14400078UL
#define SMN_IOHUB2_N14NBIO1_IOAPIC_BR_INTERRUPT_ROUTING_ADDRESS       0x14600078UL
#define SMN_IOHUB2_N15NBIO0_IOAPIC_BR_INTERRUPT_ROUTING_ADDRESS       0x1440007cUL
#define SMN_IOHUB2_N15NBIO1_IOAPIC_BR_INTERRUPT_ROUTING_ADDRESS       0x1460007cUL
#define SMN_IOHUB2_N16NBIO0_IOAPIC_BR_INTERRUPT_ROUTING_ADDRESS       0x14400080UL
#define SMN_IOHUB2_N16NBIO1_IOAPIC_BR_INTERRUPT_ROUTING_ADDRESS       0x14600080UL
#define SMN_IOHUB2_N17NBIO0_IOAPIC_BR_INTERRUPT_ROUTING_ADDRESS       0x14400084UL
#define SMN_IOHUB2_N17NBIO1_IOAPIC_BR_INTERRUPT_ROUTING_ADDRESS       0x14600084UL
#define SMN_IOHUB2_N18NBIO0_IOAPIC_BR_INTERRUPT_ROUTING_ADDRESS       0x14400088UL
#define SMN_IOHUB2_N18NBIO1_IOAPIC_BR_INTERRUPT_ROUTING_ADDRESS       0x14600088UL
#define SMN_IOHUB2_N19NBIO0_IOAPIC_BR_INTERRUPT_ROUTING_ADDRESS       0x1440008cUL
#define SMN_IOHUB2_N19NBIO1_IOAPIC_BR_INTERRUPT_ROUTING_ADDRESS       0x1460008cUL
#define SMN_IOHUB2_N1NBIO0_IOAPIC_BR_INTERRUPT_ROUTING_ADDRESS        0x14400044UL
#define SMN_IOHUB2_N1NBIO1_IOAPIC_BR_INTERRUPT_ROUTING_ADDRESS        0x14600044UL
#define SMN_IOHUB2_N20NBIO0_IOAPIC_BR_INTERRUPT_ROUTING_ADDRESS       0x14400090UL
#define SMN_IOHUB2_N20NBIO1_IOAPIC_BR_INTERRUPT_ROUTING_ADDRESS       0x14600090UL
#define SMN_IOHUB2_N21NBIO0_IOAPIC_BR_INTERRUPT_ROUTING_ADDRESS       0x14400094UL
#define SMN_IOHUB2_N21NBIO1_IOAPIC_BR_INTERRUPT_ROUTING_ADDRESS       0x14600094UL
#define SMN_IOHUB2_N2NBIO0_IOAPIC_BR_INTERRUPT_ROUTING_ADDRESS        0x14400048UL
#define SMN_IOHUB2_N2NBIO1_IOAPIC_BR_INTERRUPT_ROUTING_ADDRESS        0x14600048UL
#define SMN_IOHUB2_N3NBIO0_IOAPIC_BR_INTERRUPT_ROUTING_ADDRESS        0x1440004cUL
#define SMN_IOHUB2_N3NBIO1_IOAPIC_BR_INTERRUPT_ROUTING_ADDRESS        0x1460004cUL
#define SMN_IOHUB2_N4NBIO0_IOAPIC_BR_INTERRUPT_ROUTING_ADDRESS        0x14400050UL
#define SMN_IOHUB2_N4NBIO1_IOAPIC_BR_INTERRUPT_ROUTING_ADDRESS        0x14600050UL
#define SMN_IOHUB2_N5NBIO0_IOAPIC_BR_INTERRUPT_ROUTING_ADDRESS        0x14400054UL
#define SMN_IOHUB2_N5NBIO1_IOAPIC_BR_INTERRUPT_ROUTING_ADDRESS        0x14600054UL
#define SMN_IOHUB2_N6NBIO0_IOAPIC_BR_INTERRUPT_ROUTING_ADDRESS        0x14400058UL
#define SMN_IOHUB2_N6NBIO1_IOAPIC_BR_INTERRUPT_ROUTING_ADDRESS        0x14600058UL
#define SMN_IOHUB2_N7NBIO0_IOAPIC_BR_INTERRUPT_ROUTING_ADDRESS        0x1440005cUL
#define SMN_IOHUB2_N7NBIO1_IOAPIC_BR_INTERRUPT_ROUTING_ADDRESS        0x1460005cUL
#define SMN_IOHUB2_N8NBIO0_IOAPIC_BR_INTERRUPT_ROUTING_ADDRESS        0x14400060UL
#define SMN_IOHUB2_N8NBIO1_IOAPIC_BR_INTERRUPT_ROUTING_ADDRESS        0x14600060UL
#define SMN_IOHUB2_N9NBIO0_IOAPIC_BR_INTERRUPT_ROUTING_ADDRESS        0x14400064UL
#define SMN_IOHUB2_N9NBIO1_IOAPIC_BR_INTERRUPT_ROUTING_ADDRESS        0x14600064UL
#define SMN_IOHUB3_N0NBIO0_IOAPIC_BR_INTERRUPT_ROUTING_ADDRESS        0x1d900040UL
#define SMN_IOHUB3_N0NBIO1_IOAPIC_BR_INTERRUPT_ROUTING_ADDRESS        0x1db00040UL
#define SMN_IOHUB3_N1NBIO0_IOAPIC_BR_INTERRUPT_ROUTING_ADDRESS        0x1d900044UL
#define SMN_IOHUB3_N1NBIO1_IOAPIC_BR_INTERRUPT_ROUTING_ADDRESS        0x1db00044UL
#define SMN_IOHUB3_N2NBIO0_IOAPIC_BR_INTERRUPT_ROUTING_ADDRESS        0x1d900048UL
#define SMN_IOHUB3_N2NBIO1_IOAPIC_BR_INTERRUPT_ROUTING_ADDRESS        0x1db00048UL
#define SMN_IOHUB3_N3NBIO0_IOAPIC_BR_INTERRUPT_ROUTING_ADDRESS        0x1d90004cUL
#define SMN_IOHUB3_N3NBIO1_IOAPIC_BR_INTERRUPT_ROUTING_ADDRESS        0x1db0004cUL
#define SMN_IOHUB3_N4NBIO0_IOAPIC_BR_INTERRUPT_ROUTING_ADDRESS        0x1d900050UL
#define SMN_IOHUB3_N4NBIO1_IOAPIC_BR_INTERRUPT_ROUTING_ADDRESS        0x1db00050UL
#define SMN_IOHUB3_N5NBIO0_IOAPIC_BR_INTERRUPT_ROUTING_ADDRESS        0x1d900054UL
#define SMN_IOHUB3_N5NBIO1_IOAPIC_BR_INTERRUPT_ROUTING_ADDRESS        0x1db00054UL
#define SMN_IOHUB3_N6NBIO0_IOAPIC_BR_INTERRUPT_ROUTING_ADDRESS        0x1d900058UL
#define SMN_IOHUB3_N6NBIO1_IOAPIC_BR_INTERRUPT_ROUTING_ADDRESS        0x1db00058UL
#define SMN_IOHUB3_N7NBIO0_IOAPIC_BR_INTERRUPT_ROUTING_ADDRESS        0x1d90005cUL
#define SMN_IOHUB3_N7NBIO1_IOAPIC_BR_INTERRUPT_ROUTING_ADDRESS        0x1db0005cUL
#define SMN_IOHUB3_N8NBIO0_IOAPIC_BR_INTERRUPT_ROUTING_ADDRESS        0x1d900060UL
#define SMN_IOHUB3_N8NBIO1_IOAPIC_BR_INTERRUPT_ROUTING_ADDRESS        0x1db00060UL


/***********************************************************
* Register Name : IOAPIC_GLUE_CG_LCLK_CTRL_0
************************************************************/

#define IOAPIC_GLUE_CG_LCLK_CTRL_0_Reserved_3_0_OFFSET         0
#define IOAPIC_GLUE_CG_LCLK_CTRL_0_Reserved_3_0_MASK           0xf

#define IOAPIC_GLUE_CG_LCLK_CTRL_0_CG_OFF_HYSTERESIS_OFFSET    4
#define IOAPIC_GLUE_CG_LCLK_CTRL_0_CG_OFF_HYSTERESIS_MASK      0xff0

#define IOAPIC_GLUE_CG_LCLK_CTRL_0_Reserved_21_12_OFFSET       12
#define IOAPIC_GLUE_CG_LCLK_CTRL_0_Reserved_21_12_MASK         0x3ff000

#define IOAPIC_GLUE_CG_LCLK_CTRL_0_SOFT_OVERRIDE_CLK9_OFFSET   22
#define IOAPIC_GLUE_CG_LCLK_CTRL_0_SOFT_OVERRIDE_CLK9_MASK     0x400000
#define IOAPIC_GLUE_CG_LCLK_CTRL_0_SOFT_OVERRIDE_CLK9_DEFAULT     0x0

#define IOAPIC_GLUE_CG_LCLK_CTRL_0_SOFT_OVERRIDE_CLK8_OFFSET   23
#define IOAPIC_GLUE_CG_LCLK_CTRL_0_SOFT_OVERRIDE_CLK8_MASK     0x800000
#define IOAPIC_GLUE_CG_LCLK_CTRL_0_SOFT_OVERRIDE_CLK8_DEFAULT     0x0

#define IOAPIC_GLUE_CG_LCLK_CTRL_0_SOFT_OVERRIDE_CLK7_OFFSET   24
#define IOAPIC_GLUE_CG_LCLK_CTRL_0_SOFT_OVERRIDE_CLK7_MASK     0x1000000
#define IOAPIC_GLUE_CG_LCLK_CTRL_0_SOFT_OVERRIDE_CLK7_DEFAULT     0x0

#define IOAPIC_GLUE_CG_LCLK_CTRL_0_SOFT_OVERRIDE_CLK6_OFFSET   25
#define IOAPIC_GLUE_CG_LCLK_CTRL_0_SOFT_OVERRIDE_CLK6_MASK     0x2000000
#define IOAPIC_GLUE_CG_LCLK_CTRL_0_SOFT_OVERRIDE_CLK6_DEFAULT     0x0

#define IOAPIC_GLUE_CG_LCLK_CTRL_0_SOFT_OVERRIDE_CLK5_OFFSET   26
#define IOAPIC_GLUE_CG_LCLK_CTRL_0_SOFT_OVERRIDE_CLK5_MASK     0x4000000
#define IOAPIC_GLUE_CG_LCLK_CTRL_0_SOFT_OVERRIDE_CLK5_DEFAULT     0x0

#define IOAPIC_GLUE_CG_LCLK_CTRL_0_SOFT_OVERRIDE_CLK4_OFFSET   27
#define IOAPIC_GLUE_CG_LCLK_CTRL_0_SOFT_OVERRIDE_CLK4_MASK     0x8000000
#define IOAPIC_GLUE_CG_LCLK_CTRL_0_SOFT_OVERRIDE_CLK4_DEFAULT     0x0

#define IOAPIC_GLUE_CG_LCLK_CTRL_0_SOFT_OVERRIDE_CLK3_OFFSET   28
#define IOAPIC_GLUE_CG_LCLK_CTRL_0_SOFT_OVERRIDE_CLK3_MASK     0x10000000
#define IOAPIC_GLUE_CG_LCLK_CTRL_0_SOFT_OVERRIDE_CLK3_DEFAULT     0x0

#define IOAPIC_GLUE_CG_LCLK_CTRL_0_SOFT_OVERRIDE_CLK2_OFFSET   29
#define IOAPIC_GLUE_CG_LCLK_CTRL_0_SOFT_OVERRIDE_CLK2_MASK     0x20000000
#define IOAPIC_GLUE_CG_LCLK_CTRL_0_SOFT_OVERRIDE_CLK2_DEFAULT     0x0

#define IOAPIC_GLUE_CG_LCLK_CTRL_0_SOFT_OVERRIDE_CLK1_OFFSET   30
#define IOAPIC_GLUE_CG_LCLK_CTRL_0_SOFT_OVERRIDE_CLK1_MASK     0x40000000
#define IOAPIC_GLUE_CG_LCLK_CTRL_0_SOFT_OVERRIDE_CLK1_DEFAULT     0x0

#define IOAPIC_GLUE_CG_LCLK_CTRL_0_SOFT_OVERRIDE_CLK0_OFFSET   31
#define IOAPIC_GLUE_CG_LCLK_CTRL_0_SOFT_OVERRIDE_CLK0_MASK     0x80000000
#define IOAPIC_GLUE_CG_LCLK_CTRL_0_SOFT_OVERRIDE_CLK0_DEFAULT     0x0

typedef union {
  struct {
    UINT32                                        Reserved_3_0:4;
    UINT32                                   CG_OFF_HYSTERESIS:8;
    UINT32                                      Reserved_21_12:10;
    UINT32                                  SOFT_OVERRIDE_CLK9:1;
    UINT32                                  SOFT_OVERRIDE_CLK8:1;
    UINT32                                  SOFT_OVERRIDE_CLK7:1;
    UINT32                                  SOFT_OVERRIDE_CLK6:1;
    UINT32                                  SOFT_OVERRIDE_CLK5:1;
    UINT32                                  SOFT_OVERRIDE_CLK4:1;
    UINT32                                  SOFT_OVERRIDE_CLK3:1;
    UINT32                                  SOFT_OVERRIDE_CLK2:1;
    UINT32                                  SOFT_OVERRIDE_CLK1:1;
    UINT32                                  SOFT_OVERRIDE_CLK0:1;
  } Field;
  UINT32 Value;
} IOAPIC_GLUE_CG_LCLK_CTRL_0_STRUCT;

#define SMN_IOAPIC_GLUE_CG_LCLK_CTRL_0_ADDRESS                        0x14300100UL
#define SMN_IOHUB0NBIO0_IOAPIC_GLUE_CG_LCLK_CTRL_0_ADDRESS            0x14300100UL
#define SMN_IOHUB0NBIO1_IOAPIC_GLUE_CG_LCLK_CTRL_0_ADDRESS            0x14500100UL
#define SMN_IOHUB1NBIO0_IOAPIC_GLUE_CG_LCLK_CTRL_0_ADDRESS            0x1d800100UL
#define SMN_IOHUB1NBIO1_IOAPIC_GLUE_CG_LCLK_CTRL_0_ADDRESS            0x1da00100UL
#define SMN_IOHUB2NBIO0_IOAPIC_GLUE_CG_LCLK_CTRL_0_ADDRESS            0x14400100UL
#define SMN_IOHUB2NBIO1_IOAPIC_GLUE_CG_LCLK_CTRL_0_ADDRESS            0x14600100UL
#define SMN_IOHUB3NBIO0_IOAPIC_GLUE_CG_LCLK_CTRL_0_ADDRESS            0x1d900100UL
#define SMN_IOHUB3NBIO1_IOAPIC_GLUE_CG_LCLK_CTRL_0_ADDRESS            0x1db00100UL


/***********************************************************
* Register Name : IOAPIC_ID_REGISTER
************************************************************/

#define IOAPIC_ID_REGISTER_Reserved_23_0_OFFSET                0
#define IOAPIC_ID_REGISTER_Reserved_23_0_MASK                  0xffffff

#define IOAPIC_ID_REGISTER_DEV_ID_OFFSET                       24
#define IOAPIC_ID_REGISTER_DEV_ID_MASK                         0xf000000

#define IOAPIC_ID_REGISTER_EXTEND_ID_OFFSET                    28
#define IOAPIC_ID_REGISTER_EXTEND_ID_MASK                      0xf0000000

typedef union {
  struct {
    UINT32                                       Reserved_23_0:24;
    UINT32                                              DEV_ID:4;
    UINT32                                           EXTEND_ID:4;
  } Field;
  UINT32 Value;
} IOAPIC_ID_REGISTER_STRUCT;

#define SMN_IOAPIC_ID_REGISTER_ADDRESS                                0x2801000UL
#define SMN_IOHUB0NBIO0_IOAPIC_ID_REGISTER_ADDRESS                    0x2801000UL
#define SMN_IOHUB0NBIO1_IOAPIC_ID_REGISTER_ADDRESS                    0x2a01000UL
#define SMN_IOHUB1NBIO0_IOAPIC_ID_REGISTER_ADDRESS                    0x1d001000UL
#define SMN_IOHUB1NBIO1_IOAPIC_ID_REGISTER_ADDRESS                    0x1d201000UL
#define SMN_IOHUB2NBIO0_IOAPIC_ID_REGISTER_ADDRESS                    0x2901000UL
#define SMN_IOHUB2NBIO1_IOAPIC_ID_REGISTER_ADDRESS                    0x2b01000UL
#define SMN_IOHUB3NBIO0_IOAPIC_ID_REGISTER_ADDRESS                    0x1d101000UL
#define SMN_IOHUB3NBIO1_IOAPIC_ID_REGISTER_ADDRESS                    0x1d301000UL


/***********************************************************
* Register Name : IOAPIC_PERF_CNTL
************************************************************/

#define IOAPIC_PERF_CNTL_EVENT0_SEL_OFFSET                     0
#define IOAPIC_PERF_CNTL_EVENT0_SEL_MASK                       0xff

#define IOAPIC_PERF_CNTL_EVENT1_SEL_OFFSET                     8
#define IOAPIC_PERF_CNTL_EVENT1_SEL_MASK                       0xff00

#define IOAPIC_PERF_CNTL_EVENT2_SEL_OFFSET                     16
#define IOAPIC_PERF_CNTL_EVENT2_SEL_MASK                       0xff0000

#define IOAPIC_PERF_CNTL_EVENT3_SEL_OFFSET                     24
#define IOAPIC_PERF_CNTL_EVENT3_SEL_MASK                       0xff000000

typedef union {
  struct {
    UINT32                                          EVENT0_SEL:8;
    UINT32                                          EVENT1_SEL:8;
    UINT32                                          EVENT2_SEL:8;
    UINT32                                          EVENT3_SEL:8;
  } Field;
  UINT32 Value;
} IOAPIC_PERF_CNTL_STRUCT;

#define SMN_IOAPIC_PERF_CNTL_ADDRESS                                  0x14300114UL
#define SMN_IOHUB0NBIO0_IOAPIC_PERF_CNTL_ADDRESS                      0x14300114UL
#define SMN_IOHUB0NBIO1_IOAPIC_PERF_CNTL_ADDRESS                      0x14500114UL
#define SMN_IOHUB1NBIO0_IOAPIC_PERF_CNTL_ADDRESS                      0x1d800114UL
#define SMN_IOHUB1NBIO1_IOAPIC_PERF_CNTL_ADDRESS                      0x1da00114UL
#define SMN_IOHUB2NBIO0_IOAPIC_PERF_CNTL_ADDRESS                      0x14400114UL
#define SMN_IOHUB2NBIO1_IOAPIC_PERF_CNTL_ADDRESS                      0x14600114UL
#define SMN_IOHUB3NBIO0_IOAPIC_PERF_CNTL_ADDRESS                      0x1d900114UL
#define SMN_IOHUB3NBIO1_IOAPIC_PERF_CNTL_ADDRESS                      0x1db00114UL


/***********************************************************
* Register Name : IOAPIC_PERF_COUNT0
************************************************************/

#define IOAPIC_PERF_COUNT0_COUNTER0_OFFSET                     0
#define IOAPIC_PERF_COUNT0_COUNTER0_MASK                       0xffffffff

typedef union {
  struct {
    UINT32                                            COUNTER0:32;
  } Field;
  UINT32 Value;
} IOAPIC_PERF_COUNT0_STRUCT;

#define SMN_IOAPIC_PERF_COUNT0_ADDRESS                                0x14300118UL
#define SMN_IOHUB0NBIO0_IOAPIC_PERF_COUNT0_ADDRESS                    0x14300118UL
#define SMN_IOHUB0NBIO1_IOAPIC_PERF_COUNT0_ADDRESS                    0x14500118UL
#define SMN_IOHUB1NBIO0_IOAPIC_PERF_COUNT0_ADDRESS                    0x1d800118UL
#define SMN_IOHUB1NBIO1_IOAPIC_PERF_COUNT0_ADDRESS                    0x1da00118UL
#define SMN_IOHUB2NBIO0_IOAPIC_PERF_COUNT0_ADDRESS                    0x14400118UL
#define SMN_IOHUB2NBIO1_IOAPIC_PERF_COUNT0_ADDRESS                    0x14600118UL
#define SMN_IOHUB3NBIO0_IOAPIC_PERF_COUNT0_ADDRESS                    0x1d900118UL
#define SMN_IOHUB3NBIO1_IOAPIC_PERF_COUNT0_ADDRESS                    0x1db00118UL


/***********************************************************
* Register Name : IOAPIC_PERF_COUNT0_UPPER
************************************************************/

#define IOAPIC_PERF_COUNT0_UPPER_COUNTER0_UPPER_OFFSET         0
#define IOAPIC_PERF_COUNT0_UPPER_COUNTER0_UPPER_MASK           0xffffff

#define IOAPIC_PERF_COUNT0_UPPER_Reserved_31_24_OFFSET         24
#define IOAPIC_PERF_COUNT0_UPPER_Reserved_31_24_MASK           0xff000000

typedef union {
  struct {
    UINT32                                      COUNTER0_UPPER:24;
    UINT32                                      Reserved_31_24:8;
  } Field;
  UINT32 Value;
} IOAPIC_PERF_COUNT0_UPPER_STRUCT;

#define SMN_IOAPIC_PERF_COUNT0_UPPER_ADDRESS                          0x1430011cUL
#define SMN_IOHUB0NBIO0_IOAPIC_PERF_COUNT0_UPPER_ADDRESS              0x1430011cUL
#define SMN_IOHUB0NBIO1_IOAPIC_PERF_COUNT0_UPPER_ADDRESS              0x1450011cUL
#define SMN_IOHUB1NBIO0_IOAPIC_PERF_COUNT0_UPPER_ADDRESS              0x1d80011cUL
#define SMN_IOHUB1NBIO1_IOAPIC_PERF_COUNT0_UPPER_ADDRESS              0x1da0011cUL
#define SMN_IOHUB2NBIO0_IOAPIC_PERF_COUNT0_UPPER_ADDRESS              0x1440011cUL
#define SMN_IOHUB2NBIO1_IOAPIC_PERF_COUNT0_UPPER_ADDRESS              0x1460011cUL
#define SMN_IOHUB3NBIO0_IOAPIC_PERF_COUNT0_UPPER_ADDRESS              0x1d90011cUL
#define SMN_IOHUB3NBIO1_IOAPIC_PERF_COUNT0_UPPER_ADDRESS              0x1db0011cUL


/***********************************************************
* Register Name : IOAPIC_PERF_COUNT1
************************************************************/

#define IOAPIC_PERF_COUNT1_COUNTER1_OFFSET                     0
#define IOAPIC_PERF_COUNT1_COUNTER1_MASK                       0xffffffff

typedef union {
  struct {
    UINT32                                            COUNTER1:32;
  } Field;
  UINT32 Value;
} IOAPIC_PERF_COUNT1_STRUCT;

#define SMN_IOAPIC_PERF_COUNT1_ADDRESS                                0x14300120UL
#define SMN_IOHUB0NBIO0_IOAPIC_PERF_COUNT1_ADDRESS                    0x14300120UL
#define SMN_IOHUB0NBIO1_IOAPIC_PERF_COUNT1_ADDRESS                    0x14500120UL
#define SMN_IOHUB1NBIO0_IOAPIC_PERF_COUNT1_ADDRESS                    0x1d800120UL
#define SMN_IOHUB1NBIO1_IOAPIC_PERF_COUNT1_ADDRESS                    0x1da00120UL
#define SMN_IOHUB2NBIO0_IOAPIC_PERF_COUNT1_ADDRESS                    0x14400120UL
#define SMN_IOHUB2NBIO1_IOAPIC_PERF_COUNT1_ADDRESS                    0x14600120UL
#define SMN_IOHUB3NBIO0_IOAPIC_PERF_COUNT1_ADDRESS                    0x1d900120UL
#define SMN_IOHUB3NBIO1_IOAPIC_PERF_COUNT1_ADDRESS                    0x1db00120UL


/***********************************************************
* Register Name : IOAPIC_PERF_COUNT1_UPPER
************************************************************/

#define IOAPIC_PERF_COUNT1_UPPER_COUNTER1_UPPER_OFFSET         0
#define IOAPIC_PERF_COUNT1_UPPER_COUNTER1_UPPER_MASK           0xffffff

#define IOAPIC_PERF_COUNT1_UPPER_Reserved_31_24_OFFSET         24
#define IOAPIC_PERF_COUNT1_UPPER_Reserved_31_24_MASK           0xff000000

typedef union {
  struct {
    UINT32                                      COUNTER1_UPPER:24;
    UINT32                                      Reserved_31_24:8;
  } Field;
  UINT32 Value;
} IOAPIC_PERF_COUNT1_UPPER_STRUCT;

#define SMN_IOAPIC_PERF_COUNT1_UPPER_ADDRESS                          0x14300124UL
#define SMN_IOHUB0NBIO0_IOAPIC_PERF_COUNT1_UPPER_ADDRESS              0x14300124UL
#define SMN_IOHUB0NBIO1_IOAPIC_PERF_COUNT1_UPPER_ADDRESS              0x14500124UL
#define SMN_IOHUB1NBIO0_IOAPIC_PERF_COUNT1_UPPER_ADDRESS              0x1d800124UL
#define SMN_IOHUB1NBIO1_IOAPIC_PERF_COUNT1_UPPER_ADDRESS              0x1da00124UL
#define SMN_IOHUB2NBIO0_IOAPIC_PERF_COUNT1_UPPER_ADDRESS              0x14400124UL
#define SMN_IOHUB2NBIO1_IOAPIC_PERF_COUNT1_UPPER_ADDRESS              0x14600124UL
#define SMN_IOHUB3NBIO0_IOAPIC_PERF_COUNT1_UPPER_ADDRESS              0x1d900124UL
#define SMN_IOHUB3NBIO1_IOAPIC_PERF_COUNT1_UPPER_ADDRESS              0x1db00124UL


/***********************************************************
* Register Name : IOAPIC_PERF_COUNT2
************************************************************/

#define IOAPIC_PERF_COUNT2_COUNTER2_OFFSET                     0
#define IOAPIC_PERF_COUNT2_COUNTER2_MASK                       0xffffffff

typedef union {
  struct {
    UINT32                                            COUNTER2:32;
  } Field;
  UINT32 Value;
} IOAPIC_PERF_COUNT2_STRUCT;

#define SMN_IOAPIC_PERF_COUNT2_ADDRESS                                0x14300128UL
#define SMN_IOHUB0NBIO0_IOAPIC_PERF_COUNT2_ADDRESS                    0x14300128UL
#define SMN_IOHUB0NBIO1_IOAPIC_PERF_COUNT2_ADDRESS                    0x14500128UL
#define SMN_IOHUB1NBIO0_IOAPIC_PERF_COUNT2_ADDRESS                    0x1d800128UL
#define SMN_IOHUB1NBIO1_IOAPIC_PERF_COUNT2_ADDRESS                    0x1da00128UL
#define SMN_IOHUB2NBIO0_IOAPIC_PERF_COUNT2_ADDRESS                    0x14400128UL
#define SMN_IOHUB2NBIO1_IOAPIC_PERF_COUNT2_ADDRESS                    0x14600128UL
#define SMN_IOHUB3NBIO0_IOAPIC_PERF_COUNT2_ADDRESS                    0x1d900128UL
#define SMN_IOHUB3NBIO1_IOAPIC_PERF_COUNT2_ADDRESS                    0x1db00128UL


/***********************************************************
* Register Name : IOAPIC_PERF_COUNT2_UPPER
************************************************************/

#define IOAPIC_PERF_COUNT2_UPPER_COUNTER2_UPPER_OFFSET         0
#define IOAPIC_PERF_COUNT2_UPPER_COUNTER2_UPPER_MASK           0xffffff

#define IOAPIC_PERF_COUNT2_UPPER_Reserved_31_24_OFFSET         24
#define IOAPIC_PERF_COUNT2_UPPER_Reserved_31_24_MASK           0xff000000

typedef union {
  struct {
    UINT32                                      COUNTER2_UPPER:24;
    UINT32                                      Reserved_31_24:8;
  } Field;
  UINT32 Value;
} IOAPIC_PERF_COUNT2_UPPER_STRUCT;

#define SMN_IOAPIC_PERF_COUNT2_UPPER_ADDRESS                          0x1430012cUL
#define SMN_IOHUB0NBIO0_IOAPIC_PERF_COUNT2_UPPER_ADDRESS              0x1430012cUL
#define SMN_IOHUB0NBIO1_IOAPIC_PERF_COUNT2_UPPER_ADDRESS              0x1450012cUL
#define SMN_IOHUB1NBIO0_IOAPIC_PERF_COUNT2_UPPER_ADDRESS              0x1d80012cUL
#define SMN_IOHUB1NBIO1_IOAPIC_PERF_COUNT2_UPPER_ADDRESS              0x1da0012cUL
#define SMN_IOHUB2NBIO0_IOAPIC_PERF_COUNT2_UPPER_ADDRESS              0x1440012cUL
#define SMN_IOHUB2NBIO1_IOAPIC_PERF_COUNT2_UPPER_ADDRESS              0x1460012cUL
#define SMN_IOHUB3NBIO0_IOAPIC_PERF_COUNT2_UPPER_ADDRESS              0x1d90012cUL
#define SMN_IOHUB3NBIO1_IOAPIC_PERF_COUNT2_UPPER_ADDRESS              0x1db0012cUL


/***********************************************************
* Register Name : IOAPIC_PERF_COUNT3
************************************************************/

#define IOAPIC_PERF_COUNT3_COUNTER3_OFFSET                     0
#define IOAPIC_PERF_COUNT3_COUNTER3_MASK                       0xffffffff

typedef union {
  struct {
    UINT32                                            COUNTER3:32;
  } Field;
  UINT32 Value;
} IOAPIC_PERF_COUNT3_STRUCT;

#define SMN_IOAPIC_PERF_COUNT3_ADDRESS                                0x14300130UL
#define SMN_IOHUB0NBIO0_IOAPIC_PERF_COUNT3_ADDRESS                    0x14300130UL
#define SMN_IOHUB0NBIO1_IOAPIC_PERF_COUNT3_ADDRESS                    0x14500130UL
#define SMN_IOHUB1NBIO0_IOAPIC_PERF_COUNT3_ADDRESS                    0x1d800130UL
#define SMN_IOHUB1NBIO1_IOAPIC_PERF_COUNT3_ADDRESS                    0x1da00130UL
#define SMN_IOHUB2NBIO0_IOAPIC_PERF_COUNT3_ADDRESS                    0x14400130UL
#define SMN_IOHUB2NBIO1_IOAPIC_PERF_COUNT3_ADDRESS                    0x14600130UL
#define SMN_IOHUB3NBIO0_IOAPIC_PERF_COUNT3_ADDRESS                    0x1d900130UL
#define SMN_IOHUB3NBIO1_IOAPIC_PERF_COUNT3_ADDRESS                    0x1db00130UL


/***********************************************************
* Register Name : IOAPIC_PERF_COUNT3_UPPER
************************************************************/

#define IOAPIC_PERF_COUNT3_UPPER_COUNTER3_UPPER_OFFSET         0
#define IOAPIC_PERF_COUNT3_UPPER_COUNTER3_UPPER_MASK           0xffffff

#define IOAPIC_PERF_COUNT3_UPPER_Reserved_31_24_OFFSET         24
#define IOAPIC_PERF_COUNT3_UPPER_Reserved_31_24_MASK           0xff000000

typedef union {
  struct {
    UINT32                                      COUNTER3_UPPER:24;
    UINT32                                      Reserved_31_24:8;
  } Field;
  UINT32 Value;
} IOAPIC_PERF_COUNT3_UPPER_STRUCT;

#define SMN_IOAPIC_PERF_COUNT3_UPPER_ADDRESS                          0x14300134UL
#define SMN_IOHUB0NBIO0_IOAPIC_PERF_COUNT3_UPPER_ADDRESS              0x14300134UL
#define SMN_IOHUB0NBIO1_IOAPIC_PERF_COUNT3_UPPER_ADDRESS              0x14500134UL
#define SMN_IOHUB1NBIO0_IOAPIC_PERF_COUNT3_UPPER_ADDRESS              0x1d800134UL
#define SMN_IOHUB1NBIO1_IOAPIC_PERF_COUNT3_UPPER_ADDRESS              0x1da00134UL
#define SMN_IOHUB2NBIO0_IOAPIC_PERF_COUNT3_UPPER_ADDRESS              0x14400134UL
#define SMN_IOHUB2NBIO1_IOAPIC_PERF_COUNT3_UPPER_ADDRESS              0x14600134UL
#define SMN_IOHUB3NBIO0_IOAPIC_PERF_COUNT3_UPPER_ADDRESS              0x1d900134UL
#define SMN_IOHUB3NBIO1_IOAPIC_PERF_COUNT3_UPPER_ADDRESS              0x1db00134UL


/***********************************************************
* Register Name : IOAPIC_PGSLV_CONTROL
************************************************************/

#define IOAPIC_PGSLV_CONTROL_PGSLV_Hysteresis_OFFSET           0
#define IOAPIC_PGSLV_CONTROL_PGSLV_Hysteresis_MASK             0x1f

#define IOAPIC_PGSLV_CONTROL_Reserved_31_5_OFFSET              5
#define IOAPIC_PGSLV_CONTROL_Reserved_31_5_MASK                0xffffffe0

typedef union {
  struct {
    UINT32                                    PGSLV_Hysteresis:5;
    UINT32                                       Reserved_31_5:27;
  } Field;
  UINT32 Value;
} IOAPIC_PGSLV_CONTROL_STRUCT;

#define SMN_IOAPIC_PGSLV_CONTROL_ADDRESS                              0x14300140UL
#define SMN_IOHUB0NBIO0_IOAPIC_PGSLV_CONTROL_ADDRESS                  0x14300140UL
#define SMN_IOHUB0NBIO1_IOAPIC_PGSLV_CONTROL_ADDRESS                  0x14500140UL
#define SMN_IOHUB1NBIO0_IOAPIC_PGSLV_CONTROL_ADDRESS                  0x1d800140UL
#define SMN_IOHUB1NBIO1_IOAPIC_PGSLV_CONTROL_ADDRESS                  0x1da00140UL
#define SMN_IOHUB2NBIO0_IOAPIC_PGSLV_CONTROL_ADDRESS                  0x14400140UL
#define SMN_IOHUB2NBIO1_IOAPIC_PGSLV_CONTROL_ADDRESS                  0x14600140UL
#define SMN_IOHUB3NBIO0_IOAPIC_PGSLV_CONTROL_ADDRESS                  0x1d900140UL
#define SMN_IOHUB3NBIO1_IOAPIC_PGSLV_CONTROL_ADDRESS                  0x1db00140UL


/***********************************************************
* Register Name : IOAPIC_RCEC_APERTURE_ID
************************************************************/

#define IOAPIC_RCEC_APERTURE_ID_IoapicRcec_aperture_id_OFFSET  0
#define IOAPIC_RCEC_APERTURE_ID_IoapicRcec_aperture_id_MASK    0xfff

#define IOAPIC_RCEC_APERTURE_ID_Reserved_31_12_OFFSET          12
#define IOAPIC_RCEC_APERTURE_ID_Reserved_31_12_MASK            0xfffff000

typedef union {
  struct {
    UINT32                              IoapicRcec_aperture_id:12;
    UINT32                                      Reserved_31_12:20;
  } Field;
  UINT32 Value;
} IOAPIC_RCEC_APERTURE_ID_STRUCT;

#define SMN_IOAPIC_RCEC_APERTURE_ID_ADDRESS                           0x14300108UL
#define SMN_IOHUB0NBIO0_IOAPIC_RCEC_APERTURE_ID_ADDRESS               0x14300108UL
#define SMN_IOHUB0NBIO1_IOAPIC_RCEC_APERTURE_ID_ADDRESS               0x14500108UL
#define SMN_IOHUB1NBIO0_IOAPIC_RCEC_APERTURE_ID_ADDRESS               0x1d800108UL
#define SMN_IOHUB1NBIO1_IOAPIC_RCEC_APERTURE_ID_ADDRESS               0x1da00108UL
#define SMN_IOHUB2NBIO0_IOAPIC_RCEC_APERTURE_ID_ADDRESS               0x14400108UL
#define SMN_IOHUB2NBIO1_IOAPIC_RCEC_APERTURE_ID_ADDRESS               0x14600108UL
#define SMN_IOHUB3NBIO0_IOAPIC_RCEC_APERTURE_ID_ADDRESS               0x1d900108UL
#define SMN_IOHUB3NBIO1_IOAPIC_RCEC_APERTURE_ID_ADDRESS               0x1db00108UL


/***********************************************************
* Register Name : IOAPIC_RSMU_HCID
************************************************************/

#define IOAPIC_RSMU_HCID_RSMU_HCID_HwRev_OFFSET                0
#define IOAPIC_RSMU_HCID_RSMU_HCID_HwRev_MASK                  0x3f

#define IOAPIC_RSMU_HCID_Reserved_7_6_OFFSET                   6
#define IOAPIC_RSMU_HCID_Reserved_7_6_MASK                     0xc0

#define IOAPIC_RSMU_HCID_RSMU_HCID_HwMinVer_OFFSET             8
#define IOAPIC_RSMU_HCID_RSMU_HCID_HwMinVer_MASK               0x7f00

#define IOAPIC_RSMU_HCID_Reserved_15_15_OFFSET                 15
#define IOAPIC_RSMU_HCID_Reserved_15_15_MASK                   0x8000

#define IOAPIC_RSMU_HCID_RSMU_HCID_HwMajVer_OFFSET             16
#define IOAPIC_RSMU_HCID_RSMU_HCID_HwMajVer_MASK               0x7f0000

#define IOAPIC_RSMU_HCID_Reserved_31_23_OFFSET                 23
#define IOAPIC_RSMU_HCID_Reserved_31_23_MASK                   0xff800000

typedef union {
  struct {
    UINT32                                     RSMU_HCID_HwRev:6;
    UINT32                                        Reserved_7_6:2;
    UINT32                                  RSMU_HCID_HwMinVer:7;
    UINT32                                      Reserved_15_15:1;
    UINT32                                  RSMU_HCID_HwMajVer:7;
    UINT32                                      Reserved_31_23:9;
  } Field;
  UINT32 Value;
} IOAPIC_RSMU_HCID_STRUCT;

#define SMN_IOAPIC_RSMU_HCID_ADDRESS                                  0x14300144UL
#define SMN_IOHUB0NBIO0_IOAPIC_RSMU_HCID_ADDRESS                      0x14300144UL
#define SMN_IOHUB0NBIO1_IOAPIC_RSMU_HCID_ADDRESS                      0x14500144UL
#define SMN_IOHUB1NBIO0_IOAPIC_RSMU_HCID_ADDRESS                      0x1d800144UL
#define SMN_IOHUB1NBIO1_IOAPIC_RSMU_HCID_ADDRESS                      0x1da00144UL
#define SMN_IOHUB2NBIO0_IOAPIC_RSMU_HCID_ADDRESS                      0x14400144UL
#define SMN_IOHUB2NBIO1_IOAPIC_RSMU_HCID_ADDRESS                      0x14600144UL
#define SMN_IOHUB3NBIO0_IOAPIC_RSMU_HCID_ADDRESS                      0x1d900144UL
#define SMN_IOHUB3NBIO1_IOAPIC_RSMU_HCID_ADDRESS                      0x1db00144UL


/***********************************************************
* Register Name : IOAPIC_RSMU_SIID
************************************************************/

#define IOAPIC_RSMU_SIID_RSMU_SIID_SwIfRev_OFFSET              0
#define IOAPIC_RSMU_SIID_RSMU_SIID_SwIfRev_MASK                0x3f

#define IOAPIC_RSMU_SIID_Reserved_7_6_OFFSET                   6
#define IOAPIC_RSMU_SIID_Reserved_7_6_MASK                     0xc0

#define IOAPIC_RSMU_SIID_RSMU_SIID_SwIfMinVer_OFFSET           8
#define IOAPIC_RSMU_SIID_RSMU_SIID_SwIfMinVer_MASK             0x7f00

#define IOAPIC_RSMU_SIID_Reserved_15_15_OFFSET                 15
#define IOAPIC_RSMU_SIID_Reserved_15_15_MASK                   0x8000

#define IOAPIC_RSMU_SIID_RSMU_SIID_SwIfMajVer_OFFSET           16
#define IOAPIC_RSMU_SIID_RSMU_SIID_SwIfMajVer_MASK             0x7f0000

#define IOAPIC_RSMU_SIID_Reserved_31_23_OFFSET                 23
#define IOAPIC_RSMU_SIID_Reserved_31_23_MASK                   0xff800000

typedef union {
  struct {
    UINT32                                   RSMU_SIID_SwIfRev:6;
    UINT32                                        Reserved_7_6:2;
    UINT32                                RSMU_SIID_SwIfMinVer:7;
    UINT32                                      Reserved_15_15:1;
    UINT32                                RSMU_SIID_SwIfMajVer:7;
    UINT32                                      Reserved_31_23:9;
  } Field;
  UINT32 Value;
} IOAPIC_RSMU_SIID_STRUCT;

#define SMN_IOAPIC_RSMU_SIID_ADDRESS                                  0x14300148UL
#define SMN_IOHUB0NBIO0_IOAPIC_RSMU_SIID_ADDRESS                      0x14300148UL
#define SMN_IOHUB0NBIO1_IOAPIC_RSMU_SIID_ADDRESS                      0x14500148UL
#define SMN_IOHUB1NBIO0_IOAPIC_RSMU_SIID_ADDRESS                      0x1d800148UL
#define SMN_IOHUB1NBIO1_IOAPIC_RSMU_SIID_ADDRESS                      0x1da00148UL
#define SMN_IOHUB2NBIO0_IOAPIC_RSMU_SIID_ADDRESS                      0x14400148UL
#define SMN_IOHUB2NBIO1_IOAPIC_RSMU_SIID_ADDRESS                      0x14600148UL
#define SMN_IOHUB3NBIO0_IOAPIC_RSMU_SIID_ADDRESS                      0x1d900148UL
#define SMN_IOHUB3NBIO1_IOAPIC_RSMU_SIID_ADDRESS                      0x1db00148UL


/***********************************************************
* Register Name : IOAPIC_SCRATCH_0
************************************************************/

#define IOAPIC_SCRATCH_0_Scratch_0_OFFSET                      0
#define IOAPIC_SCRATCH_0_Scratch_0_MASK                        0xffffffff

typedef union {
  struct {
    UINT32                                           Scratch_0:32;
  } Field;
  UINT32 Value;
} IOAPIC_SCRATCH_0_STRUCT;

#define SMN_IOAPIC_SCRATCH_0_ADDRESS                                  0x143000f8UL
#define SMN_IOHUB0NBIO0_IOAPIC_SCRATCH_0_ADDRESS                      0x143000f8UL
#define SMN_IOHUB0NBIO1_IOAPIC_SCRATCH_0_ADDRESS                      0x145000f8UL
#define SMN_IOHUB1NBIO0_IOAPIC_SCRATCH_0_ADDRESS                      0x1d8000f8UL
#define SMN_IOHUB1NBIO1_IOAPIC_SCRATCH_0_ADDRESS                      0x1da000f8UL
#define SMN_IOHUB2NBIO0_IOAPIC_SCRATCH_0_ADDRESS                      0x144000f8UL
#define SMN_IOHUB2NBIO1_IOAPIC_SCRATCH_0_ADDRESS                      0x146000f8UL
#define SMN_IOHUB3NBIO0_IOAPIC_SCRATCH_0_ADDRESS                      0x1d9000f8UL
#define SMN_IOHUB3NBIO1_IOAPIC_SCRATCH_0_ADDRESS                      0x1db000f8UL


/***********************************************************
* Register Name : IOAPIC_SCRATCH_1
************************************************************/

#define IOAPIC_SCRATCH_1_Scratch_1_OFFSET                      0
#define IOAPIC_SCRATCH_1_Scratch_1_MASK                        0xffffffff

typedef union {
  struct {
    UINT32                                           Scratch_1:32;
  } Field;
  UINT32 Value;
} IOAPIC_SCRATCH_1_STRUCT;

#define SMN_IOAPIC_SCRATCH_1_ADDRESS                                  0x143000fcUL
#define SMN_IOHUB0NBIO0_IOAPIC_SCRATCH_1_ADDRESS                      0x143000fcUL
#define SMN_IOHUB0NBIO1_IOAPIC_SCRATCH_1_ADDRESS                      0x145000fcUL
#define SMN_IOHUB1NBIO0_IOAPIC_SCRATCH_1_ADDRESS                      0x1d8000fcUL
#define SMN_IOHUB1NBIO1_IOAPIC_SCRATCH_1_ADDRESS                      0x1da000fcUL
#define SMN_IOHUB2NBIO0_IOAPIC_SCRATCH_1_ADDRESS                      0x144000fcUL
#define SMN_IOHUB2NBIO1_IOAPIC_SCRATCH_1_ADDRESS                      0x146000fcUL
#define SMN_IOHUB3NBIO0_IOAPIC_SCRATCH_1_ADDRESS                      0x1d9000fcUL
#define SMN_IOHUB3NBIO1_IOAPIC_SCRATCH_1_ADDRESS                      0x1db000fcUL


/***********************************************************
* Register Name : IOAPIC_SDP_PORT_CONTROL
************************************************************/

#define IOAPIC_SDP_PORT_CONTROL_Port_Disconnect_Hysteresis_OFFSET 0
#define IOAPIC_SDP_PORT_CONTROL_Port_Disconnect_Hysteresis_MASK 0xfff

#define IOAPIC_SDP_PORT_CONTROL_Reserved_31_12_OFFSET          12
#define IOAPIC_SDP_PORT_CONTROL_Reserved_31_12_MASK            0xfffff000

typedef union {
  struct {
    UINT32                          Port_Disconnect_Hysteresis:12;
    UINT32                                      Reserved_31_12:20;
  } Field;
  UINT32 Value;
} IOAPIC_SDP_PORT_CONTROL_STRUCT;

#define SMN_IOAPIC_SDP_PORT_CONTROL_ADDRESS                           0x14300110UL
#define SMN_IOHUB0NBIO0_IOAPIC_SDP_PORT_CONTROL_ADDRESS               0x14300110UL
#define SMN_IOHUB0NBIO1_IOAPIC_SDP_PORT_CONTROL_ADDRESS               0x14500110UL
#define SMN_IOHUB1NBIO0_IOAPIC_SDP_PORT_CONTROL_ADDRESS               0x1d800110UL
#define SMN_IOHUB1NBIO1_IOAPIC_SDP_PORT_CONTROL_ADDRESS               0x1da00110UL
#define SMN_IOHUB2NBIO0_IOAPIC_SDP_PORT_CONTROL_ADDRESS               0x14400110UL
#define SMN_IOHUB2NBIO1_IOAPIC_SDP_PORT_CONTROL_ADDRESS               0x14600110UL
#define SMN_IOHUB3NBIO0_IOAPIC_SDP_PORT_CONTROL_ADDRESS               0x1d900110UL
#define SMN_IOHUB3NBIO1_IOAPIC_SDP_PORT_CONTROL_ADDRESS               0x1db00110UL


/***********************************************************
* Register Name : IOAPIC_SERIAL_IRQ_STATUS
************************************************************/

#define IOAPIC_SERIAL_IRQ_STATUS_Internal_irq_sts_OFFSET       0
#define IOAPIC_SERIAL_IRQ_STATUS_Internal_irq_sts_MASK         0xffffffff

typedef union {
  struct {
    UINT32                                    Internal_irq_sts:32;
  } Field;
  UINT32 Value;
} IOAPIC_SERIAL_IRQ_STATUS_STRUCT;

#define SMN_IOAPIC_SERIAL_IRQ_STATUS_ADDRESS                          0x143000c4UL
#define SMN_IOHUB0NBIO0_IOAPIC_SERIAL_IRQ_STATUS_ADDRESS              0x143000c4UL
#define SMN_IOHUB0NBIO1_IOAPIC_SERIAL_IRQ_STATUS_ADDRESS              0x145000c4UL
#define SMN_IOHUB1NBIO0_IOAPIC_SERIAL_IRQ_STATUS_ADDRESS              0x1d8000c4UL
#define SMN_IOHUB1NBIO1_IOAPIC_SERIAL_IRQ_STATUS_ADDRESS              0x1da000c4UL
#define SMN_IOHUB2NBIO0_IOAPIC_SERIAL_IRQ_STATUS_ADDRESS              0x144000c4UL
#define SMN_IOHUB2NBIO1_IOAPIC_SERIAL_IRQ_STATUS_ADDRESS              0x146000c4UL
#define SMN_IOHUB3NBIO0_IOAPIC_SERIAL_IRQ_STATUS_ADDRESS              0x1d9000c4UL
#define SMN_IOHUB3NBIO1_IOAPIC_SERIAL_IRQ_STATUS_ADDRESS              0x1db000c4UL


/***********************************************************
* Register Name : IOAPIC_VERSION_REGISTER
************************************************************/

#define IOAPIC_VERSION_REGISTER_Version_OFFSET                 0
#define IOAPIC_VERSION_REGISTER_Version_MASK                   0xff

#define IOAPIC_VERSION_REGISTER_Reserved_14_8_OFFSET           8
#define IOAPIC_VERSION_REGISTER_Reserved_14_8_MASK             0x7f00

#define IOAPIC_VERSION_REGISTER_PRQ_OFFSET                     15
#define IOAPIC_VERSION_REGISTER_PRQ_MASK                       0x8000

#define IOAPIC_VERSION_REGISTER_Max_Redirection_Entries_OFFSET 16
#define IOAPIC_VERSION_REGISTER_Max_Redirection_Entries_MASK   0xff0000

#define IOAPIC_VERSION_REGISTER_Reserved_31_24_OFFSET          24
#define IOAPIC_VERSION_REGISTER_Reserved_31_24_MASK            0xff000000

typedef union {
  struct {
    UINT32                                             Version:8;
    UINT32                                       Reserved_14_8:7;
    UINT32                                                 PRQ:1;
    UINT32                             Max_Redirection_Entries:8;
    UINT32                                      Reserved_31_24:8;
  } Field;
  UINT32 Value;
} IOAPIC_VERSION_REGISTER_STRUCT;

#define SMN_IOAPIC_VERSION_REGISTER_ADDRESS                           0x2801004UL
#define SMN_IOHUB0NBIO0_IOAPIC_VERSION_REGISTER_ADDRESS               0x2801004UL
#define SMN_IOHUB0NBIO1_IOAPIC_VERSION_REGISTER_ADDRESS               0x2a01004UL
#define SMN_IOHUB1NBIO0_IOAPIC_VERSION_REGISTER_ADDRESS               0x1d001004UL
#define SMN_IOHUB1NBIO1_IOAPIC_VERSION_REGISTER_ADDRESS               0x1d201004UL
#define SMN_IOHUB2NBIO0_IOAPIC_VERSION_REGISTER_ADDRESS               0x2901004UL
#define SMN_IOHUB2NBIO1_IOAPIC_VERSION_REGISTER_ADDRESS               0x2b01004UL
#define SMN_IOHUB3NBIO0_IOAPIC_VERSION_REGISTER_ADDRESS               0x1d101004UL
#define SMN_IOHUB3NBIO1_IOAPIC_VERSION_REGISTER_ADDRESS               0x1d301004UL


/***********************************************************
* Register Name : IRQ_PIN_ASSERTION_REGISTER
************************************************************/

#define IRQ_PIN_ASSERTION_REGISTER_Input_IRQ_OFFSET            0
#define IRQ_PIN_ASSERTION_REGISTER_Input_IRQ_MASK              0xff

#define IRQ_PIN_ASSERTION_REGISTER_Reserved_31_8_OFFSET        8
#define IRQ_PIN_ASSERTION_REGISTER_Reserved_31_8_MASK          0xffffff00

typedef union {
  struct {
    UINT32                                           Input_IRQ:8;
    UINT32                                       Reserved_31_8:24;
  } Field;
  UINT32 Value;
} IRQ_PIN_ASSERTION_REGISTER_STRUCT;

#define SMN_IRQ_PIN_ASSERTION_REGISTER_ADDRESS                        0x2800020UL
#define SMN_IOHUB0NBIO0_IRQ_PIN_ASSERTION_REGISTER_ADDRESS            0x2800020UL
#define SMN_IOHUB0NBIO1_IRQ_PIN_ASSERTION_REGISTER_ADDRESS            0x2a00020UL
#define SMN_IOHUB1NBIO0_IRQ_PIN_ASSERTION_REGISTER_ADDRESS            0x1d000020UL
#define SMN_IOHUB1NBIO1_IRQ_PIN_ASSERTION_REGISTER_ADDRESS            0x1d200020UL
#define SMN_IOHUB2NBIO0_IRQ_PIN_ASSERTION_REGISTER_ADDRESS            0x2900020UL
#define SMN_IOHUB2NBIO1_IRQ_PIN_ASSERTION_REGISTER_ADDRESS            0x2b00020UL
#define SMN_IOHUB3NBIO0_IRQ_PIN_ASSERTION_REGISTER_ADDRESS            0x1d100020UL
#define SMN_IOHUB3NBIO1_IRQ_PIN_ASSERTION_REGISTER_ADDRESS            0x1d300020UL


/***********************************************************
* Register Name : REDIRECTION_TABLE_ENTRY_HIGH
************************************************************/

#define REDIRECTION_TABLE_ENTRY_HIGH_Reserved_23_0_OFFSET      0
#define REDIRECTION_TABLE_ENTRY_HIGH_Reserved_23_0_MASK        0xffffff

#define REDIRECTION_TABLE_ENTRY_HIGH_Destination_id_OFFSET     24
#define REDIRECTION_TABLE_ENTRY_HIGH_Destination_id_MASK       0xff000000

typedef union {
  struct {
    UINT32                                       Reserved_23_0:24;
    UINT32                                      Destination_id:8;
  } Field;
  UINT32 Value;
} REDIRECTION_TABLE_ENTRY_HIGH_STRUCT;

#define SMN_REDIRECTION_TABLE_ENTRY_HIGH_ADDRESS                      0x2801044UL
#define SMN_IOHUB0_N0NBIO0_REDIRECTION_TABLE_ENTRY_HIGH_ADDRESS       0x2801044UL
#define SMN_IOHUB0_N0NBIO1_REDIRECTION_TABLE_ENTRY_HIGH_ADDRESS       0x2a01044UL
#define SMN_IOHUB0_N10NBIO0_REDIRECTION_TABLE_ENTRY_HIGH_ADDRESS      0x2801094UL
#define SMN_IOHUB0_N10NBIO1_REDIRECTION_TABLE_ENTRY_HIGH_ADDRESS      0x2a01094UL
#define SMN_IOHUB0_N11NBIO0_REDIRECTION_TABLE_ENTRY_HIGH_ADDRESS      0x280109cUL
#define SMN_IOHUB0_N11NBIO1_REDIRECTION_TABLE_ENTRY_HIGH_ADDRESS      0x2a0109cUL
#define SMN_IOHUB0_N12NBIO0_REDIRECTION_TABLE_ENTRY_HIGH_ADDRESS      0x28010a4UL
#define SMN_IOHUB0_N12NBIO1_REDIRECTION_TABLE_ENTRY_HIGH_ADDRESS      0x2a010a4UL
#define SMN_IOHUB0_N13NBIO0_REDIRECTION_TABLE_ENTRY_HIGH_ADDRESS      0x28010acUL
#define SMN_IOHUB0_N13NBIO1_REDIRECTION_TABLE_ENTRY_HIGH_ADDRESS      0x2a010acUL
#define SMN_IOHUB0_N14NBIO0_REDIRECTION_TABLE_ENTRY_HIGH_ADDRESS      0x28010b4UL
#define SMN_IOHUB0_N14NBIO1_REDIRECTION_TABLE_ENTRY_HIGH_ADDRESS      0x2a010b4UL
#define SMN_IOHUB0_N15NBIO0_REDIRECTION_TABLE_ENTRY_HIGH_ADDRESS      0x28010bcUL
#define SMN_IOHUB0_N15NBIO1_REDIRECTION_TABLE_ENTRY_HIGH_ADDRESS      0x2a010bcUL
#define SMN_IOHUB0_N16NBIO0_REDIRECTION_TABLE_ENTRY_HIGH_ADDRESS      0x28010c4UL
#define SMN_IOHUB0_N16NBIO1_REDIRECTION_TABLE_ENTRY_HIGH_ADDRESS      0x2a010c4UL
#define SMN_IOHUB0_N17NBIO0_REDIRECTION_TABLE_ENTRY_HIGH_ADDRESS      0x28010ccUL
#define SMN_IOHUB0_N17NBIO1_REDIRECTION_TABLE_ENTRY_HIGH_ADDRESS      0x2a010ccUL
#define SMN_IOHUB0_N18NBIO0_REDIRECTION_TABLE_ENTRY_HIGH_ADDRESS      0x28010d4UL
#define SMN_IOHUB0_N18NBIO1_REDIRECTION_TABLE_ENTRY_HIGH_ADDRESS      0x2a010d4UL
#define SMN_IOHUB0_N19NBIO0_REDIRECTION_TABLE_ENTRY_HIGH_ADDRESS      0x28010dcUL
#define SMN_IOHUB0_N19NBIO1_REDIRECTION_TABLE_ENTRY_HIGH_ADDRESS      0x2a010dcUL
#define SMN_IOHUB0_N1NBIO0_REDIRECTION_TABLE_ENTRY_HIGH_ADDRESS       0x280104cUL
#define SMN_IOHUB0_N1NBIO1_REDIRECTION_TABLE_ENTRY_HIGH_ADDRESS       0x2a0104cUL
#define SMN_IOHUB0_N20NBIO0_REDIRECTION_TABLE_ENTRY_HIGH_ADDRESS      0x28010e4UL
#define SMN_IOHUB0_N20NBIO1_REDIRECTION_TABLE_ENTRY_HIGH_ADDRESS      0x2a010e4UL
#define SMN_IOHUB0_N21NBIO0_REDIRECTION_TABLE_ENTRY_HIGH_ADDRESS      0x28010ecUL
#define SMN_IOHUB0_N21NBIO1_REDIRECTION_TABLE_ENTRY_HIGH_ADDRESS      0x2a010ecUL
#define SMN_IOHUB0_N22NBIO0_REDIRECTION_TABLE_ENTRY_HIGH_ADDRESS      0x28010f4UL
#define SMN_IOHUB0_N22NBIO1_REDIRECTION_TABLE_ENTRY_HIGH_ADDRESS      0x2a010f4UL
#define SMN_IOHUB0_N23NBIO0_REDIRECTION_TABLE_ENTRY_HIGH_ADDRESS      0x28010fcUL
#define SMN_IOHUB0_N23NBIO1_REDIRECTION_TABLE_ENTRY_HIGH_ADDRESS      0x2a010fcUL
#define SMN_IOHUB0_N24NBIO0_REDIRECTION_TABLE_ENTRY_HIGH_ADDRESS      0x2801104UL
#define SMN_IOHUB0_N24NBIO1_REDIRECTION_TABLE_ENTRY_HIGH_ADDRESS      0x2a01104UL
#define SMN_IOHUB0_N25NBIO0_REDIRECTION_TABLE_ENTRY_HIGH_ADDRESS      0x280110cUL
#define SMN_IOHUB0_N25NBIO1_REDIRECTION_TABLE_ENTRY_HIGH_ADDRESS      0x2a0110cUL
#define SMN_IOHUB0_N26NBIO0_REDIRECTION_TABLE_ENTRY_HIGH_ADDRESS      0x2801114UL
#define SMN_IOHUB0_N26NBIO1_REDIRECTION_TABLE_ENTRY_HIGH_ADDRESS      0x2a01114UL
#define SMN_IOHUB0_N27NBIO0_REDIRECTION_TABLE_ENTRY_HIGH_ADDRESS      0x280111cUL
#define SMN_IOHUB0_N27NBIO1_REDIRECTION_TABLE_ENTRY_HIGH_ADDRESS      0x2a0111cUL
#define SMN_IOHUB0_N28NBIO0_REDIRECTION_TABLE_ENTRY_HIGH_ADDRESS      0x2801124UL
#define SMN_IOHUB0_N28NBIO1_REDIRECTION_TABLE_ENTRY_HIGH_ADDRESS      0x2a01124UL
#define SMN_IOHUB0_N29NBIO0_REDIRECTION_TABLE_ENTRY_HIGH_ADDRESS      0x280112cUL
#define SMN_IOHUB0_N29NBIO1_REDIRECTION_TABLE_ENTRY_HIGH_ADDRESS      0x2a0112cUL
#define SMN_IOHUB0_N2NBIO0_REDIRECTION_TABLE_ENTRY_HIGH_ADDRESS       0x2801054UL
#define SMN_IOHUB0_N2NBIO1_REDIRECTION_TABLE_ENTRY_HIGH_ADDRESS       0x2a01054UL
#define SMN_IOHUB0_N30NBIO0_REDIRECTION_TABLE_ENTRY_HIGH_ADDRESS      0x2801134UL
#define SMN_IOHUB0_N30NBIO1_REDIRECTION_TABLE_ENTRY_HIGH_ADDRESS      0x2a01134UL
#define SMN_IOHUB0_N31NBIO0_REDIRECTION_TABLE_ENTRY_HIGH_ADDRESS      0x280113cUL
#define SMN_IOHUB0_N31NBIO1_REDIRECTION_TABLE_ENTRY_HIGH_ADDRESS      0x2a0113cUL
#define SMN_IOHUB0_N3NBIO0_REDIRECTION_TABLE_ENTRY_HIGH_ADDRESS       0x280105cUL
#define SMN_IOHUB0_N3NBIO1_REDIRECTION_TABLE_ENTRY_HIGH_ADDRESS       0x2a0105cUL
#define SMN_IOHUB0_N4NBIO0_REDIRECTION_TABLE_ENTRY_HIGH_ADDRESS       0x2801064UL
#define SMN_IOHUB0_N4NBIO1_REDIRECTION_TABLE_ENTRY_HIGH_ADDRESS       0x2a01064UL
#define SMN_IOHUB0_N5NBIO0_REDIRECTION_TABLE_ENTRY_HIGH_ADDRESS       0x280106cUL
#define SMN_IOHUB0_N5NBIO1_REDIRECTION_TABLE_ENTRY_HIGH_ADDRESS       0x2a0106cUL
#define SMN_IOHUB0_N6NBIO0_REDIRECTION_TABLE_ENTRY_HIGH_ADDRESS       0x2801074UL
#define SMN_IOHUB0_N6NBIO1_REDIRECTION_TABLE_ENTRY_HIGH_ADDRESS       0x2a01074UL
#define SMN_IOHUB0_N7NBIO0_REDIRECTION_TABLE_ENTRY_HIGH_ADDRESS       0x280107cUL
#define SMN_IOHUB0_N7NBIO1_REDIRECTION_TABLE_ENTRY_HIGH_ADDRESS       0x2a0107cUL
#define SMN_IOHUB0_N8NBIO0_REDIRECTION_TABLE_ENTRY_HIGH_ADDRESS       0x2801084UL
#define SMN_IOHUB0_N8NBIO1_REDIRECTION_TABLE_ENTRY_HIGH_ADDRESS       0x2a01084UL
#define SMN_IOHUB0_N9NBIO0_REDIRECTION_TABLE_ENTRY_HIGH_ADDRESS       0x280108cUL
#define SMN_IOHUB0_N9NBIO1_REDIRECTION_TABLE_ENTRY_HIGH_ADDRESS       0x2a0108cUL
#define SMN_IOHUB1_N0NBIO0_REDIRECTION_TABLE_ENTRY_HIGH_ADDRESS       0x1d001044UL
#define SMN_IOHUB1_N0NBIO1_REDIRECTION_TABLE_ENTRY_HIGH_ADDRESS       0x1d201044UL
#define SMN_IOHUB1_N10NBIO0_REDIRECTION_TABLE_ENTRY_HIGH_ADDRESS      0x1d001094UL
#define SMN_IOHUB1_N10NBIO1_REDIRECTION_TABLE_ENTRY_HIGH_ADDRESS      0x1d201094UL
#define SMN_IOHUB1_N11NBIO0_REDIRECTION_TABLE_ENTRY_HIGH_ADDRESS      0x1d00109cUL
#define SMN_IOHUB1_N11NBIO1_REDIRECTION_TABLE_ENTRY_HIGH_ADDRESS      0x1d20109cUL
#define SMN_IOHUB1_N12NBIO0_REDIRECTION_TABLE_ENTRY_HIGH_ADDRESS      0x1d0010a4UL
#define SMN_IOHUB1_N12NBIO1_REDIRECTION_TABLE_ENTRY_HIGH_ADDRESS      0x1d2010a4UL
#define SMN_IOHUB1_N13NBIO0_REDIRECTION_TABLE_ENTRY_HIGH_ADDRESS      0x1d0010acUL
#define SMN_IOHUB1_N13NBIO1_REDIRECTION_TABLE_ENTRY_HIGH_ADDRESS      0x1d2010acUL
#define SMN_IOHUB1_N14NBIO0_REDIRECTION_TABLE_ENTRY_HIGH_ADDRESS      0x1d0010b4UL
#define SMN_IOHUB1_N14NBIO1_REDIRECTION_TABLE_ENTRY_HIGH_ADDRESS      0x1d2010b4UL
#define SMN_IOHUB1_N15NBIO0_REDIRECTION_TABLE_ENTRY_HIGH_ADDRESS      0x1d0010bcUL
#define SMN_IOHUB1_N15NBIO1_REDIRECTION_TABLE_ENTRY_HIGH_ADDRESS      0x1d2010bcUL
#define SMN_IOHUB1_N16NBIO0_REDIRECTION_TABLE_ENTRY_HIGH_ADDRESS      0x1d0010c4UL
#define SMN_IOHUB1_N16NBIO1_REDIRECTION_TABLE_ENTRY_HIGH_ADDRESS      0x1d2010c4UL
#define SMN_IOHUB1_N17NBIO0_REDIRECTION_TABLE_ENTRY_HIGH_ADDRESS      0x1d0010ccUL
#define SMN_IOHUB1_N17NBIO1_REDIRECTION_TABLE_ENTRY_HIGH_ADDRESS      0x1d2010ccUL
#define SMN_IOHUB1_N18NBIO0_REDIRECTION_TABLE_ENTRY_HIGH_ADDRESS      0x1d0010d4UL
#define SMN_IOHUB1_N18NBIO1_REDIRECTION_TABLE_ENTRY_HIGH_ADDRESS      0x1d2010d4UL
#define SMN_IOHUB1_N19NBIO0_REDIRECTION_TABLE_ENTRY_HIGH_ADDRESS      0x1d0010dcUL
#define SMN_IOHUB1_N19NBIO1_REDIRECTION_TABLE_ENTRY_HIGH_ADDRESS      0x1d2010dcUL
#define SMN_IOHUB1_N1NBIO0_REDIRECTION_TABLE_ENTRY_HIGH_ADDRESS       0x1d00104cUL
#define SMN_IOHUB1_N1NBIO1_REDIRECTION_TABLE_ENTRY_HIGH_ADDRESS       0x1d20104cUL
#define SMN_IOHUB1_N20NBIO0_REDIRECTION_TABLE_ENTRY_HIGH_ADDRESS      0x1d0010e4UL
#define SMN_IOHUB1_N20NBIO1_REDIRECTION_TABLE_ENTRY_HIGH_ADDRESS      0x1d2010e4UL
#define SMN_IOHUB1_N21NBIO0_REDIRECTION_TABLE_ENTRY_HIGH_ADDRESS      0x1d0010ecUL
#define SMN_IOHUB1_N21NBIO1_REDIRECTION_TABLE_ENTRY_HIGH_ADDRESS      0x1d2010ecUL
#define SMN_IOHUB1_N22NBIO0_REDIRECTION_TABLE_ENTRY_HIGH_ADDRESS      0x1d0010f4UL
#define SMN_IOHUB1_N22NBIO1_REDIRECTION_TABLE_ENTRY_HIGH_ADDRESS      0x1d2010f4UL
#define SMN_IOHUB1_N23NBIO0_REDIRECTION_TABLE_ENTRY_HIGH_ADDRESS      0x1d0010fcUL
#define SMN_IOHUB1_N23NBIO1_REDIRECTION_TABLE_ENTRY_HIGH_ADDRESS      0x1d2010fcUL
#define SMN_IOHUB1_N24NBIO0_REDIRECTION_TABLE_ENTRY_HIGH_ADDRESS      0x1d001104UL
#define SMN_IOHUB1_N24NBIO1_REDIRECTION_TABLE_ENTRY_HIGH_ADDRESS      0x1d201104UL
#define SMN_IOHUB1_N25NBIO0_REDIRECTION_TABLE_ENTRY_HIGH_ADDRESS      0x1d00110cUL
#define SMN_IOHUB1_N25NBIO1_REDIRECTION_TABLE_ENTRY_HIGH_ADDRESS      0x1d20110cUL
#define SMN_IOHUB1_N26NBIO0_REDIRECTION_TABLE_ENTRY_HIGH_ADDRESS      0x1d001114UL
#define SMN_IOHUB1_N26NBIO1_REDIRECTION_TABLE_ENTRY_HIGH_ADDRESS      0x1d201114UL
#define SMN_IOHUB1_N27NBIO0_REDIRECTION_TABLE_ENTRY_HIGH_ADDRESS      0x1d00111cUL
#define SMN_IOHUB1_N27NBIO1_REDIRECTION_TABLE_ENTRY_HIGH_ADDRESS      0x1d20111cUL
#define SMN_IOHUB1_N28NBIO0_REDIRECTION_TABLE_ENTRY_HIGH_ADDRESS      0x1d001124UL
#define SMN_IOHUB1_N28NBIO1_REDIRECTION_TABLE_ENTRY_HIGH_ADDRESS      0x1d201124UL
#define SMN_IOHUB1_N29NBIO0_REDIRECTION_TABLE_ENTRY_HIGH_ADDRESS      0x1d00112cUL
#define SMN_IOHUB1_N29NBIO1_REDIRECTION_TABLE_ENTRY_HIGH_ADDRESS      0x1d20112cUL
#define SMN_IOHUB1_N2NBIO0_REDIRECTION_TABLE_ENTRY_HIGH_ADDRESS       0x1d001054UL
#define SMN_IOHUB1_N2NBIO1_REDIRECTION_TABLE_ENTRY_HIGH_ADDRESS       0x1d201054UL
#define SMN_IOHUB1_N30NBIO0_REDIRECTION_TABLE_ENTRY_HIGH_ADDRESS      0x1d001134UL
#define SMN_IOHUB1_N30NBIO1_REDIRECTION_TABLE_ENTRY_HIGH_ADDRESS      0x1d201134UL
#define SMN_IOHUB1_N31NBIO0_REDIRECTION_TABLE_ENTRY_HIGH_ADDRESS      0x1d00113cUL
#define SMN_IOHUB1_N31NBIO1_REDIRECTION_TABLE_ENTRY_HIGH_ADDRESS      0x1d20113cUL
#define SMN_IOHUB1_N3NBIO0_REDIRECTION_TABLE_ENTRY_HIGH_ADDRESS       0x1d00105cUL
#define SMN_IOHUB1_N3NBIO1_REDIRECTION_TABLE_ENTRY_HIGH_ADDRESS       0x1d20105cUL
#define SMN_IOHUB1_N4NBIO0_REDIRECTION_TABLE_ENTRY_HIGH_ADDRESS       0x1d001064UL
#define SMN_IOHUB1_N4NBIO1_REDIRECTION_TABLE_ENTRY_HIGH_ADDRESS       0x1d201064UL
#define SMN_IOHUB1_N5NBIO0_REDIRECTION_TABLE_ENTRY_HIGH_ADDRESS       0x1d00106cUL
#define SMN_IOHUB1_N5NBIO1_REDIRECTION_TABLE_ENTRY_HIGH_ADDRESS       0x1d20106cUL
#define SMN_IOHUB1_N6NBIO0_REDIRECTION_TABLE_ENTRY_HIGH_ADDRESS       0x1d001074UL
#define SMN_IOHUB1_N6NBIO1_REDIRECTION_TABLE_ENTRY_HIGH_ADDRESS       0x1d201074UL
#define SMN_IOHUB1_N7NBIO0_REDIRECTION_TABLE_ENTRY_HIGH_ADDRESS       0x1d00107cUL
#define SMN_IOHUB1_N7NBIO1_REDIRECTION_TABLE_ENTRY_HIGH_ADDRESS       0x1d20107cUL
#define SMN_IOHUB1_N8NBIO0_REDIRECTION_TABLE_ENTRY_HIGH_ADDRESS       0x1d001084UL
#define SMN_IOHUB1_N8NBIO1_REDIRECTION_TABLE_ENTRY_HIGH_ADDRESS       0x1d201084UL
#define SMN_IOHUB1_N9NBIO0_REDIRECTION_TABLE_ENTRY_HIGH_ADDRESS       0x1d00108cUL
#define SMN_IOHUB1_N9NBIO1_REDIRECTION_TABLE_ENTRY_HIGH_ADDRESS       0x1d20108cUL
#define SMN_IOHUB2_N0NBIO0_REDIRECTION_TABLE_ENTRY_HIGH_ADDRESS       0x2901044UL
#define SMN_IOHUB2_N0NBIO1_REDIRECTION_TABLE_ENTRY_HIGH_ADDRESS       0x2b01044UL
#define SMN_IOHUB2_N10NBIO0_REDIRECTION_TABLE_ENTRY_HIGH_ADDRESS      0x2901094UL
#define SMN_IOHUB2_N10NBIO1_REDIRECTION_TABLE_ENTRY_HIGH_ADDRESS      0x2b01094UL
#define SMN_IOHUB2_N11NBIO0_REDIRECTION_TABLE_ENTRY_HIGH_ADDRESS      0x290109cUL
#define SMN_IOHUB2_N11NBIO1_REDIRECTION_TABLE_ENTRY_HIGH_ADDRESS      0x2b0109cUL
#define SMN_IOHUB2_N12NBIO0_REDIRECTION_TABLE_ENTRY_HIGH_ADDRESS      0x29010a4UL
#define SMN_IOHUB2_N12NBIO1_REDIRECTION_TABLE_ENTRY_HIGH_ADDRESS      0x2b010a4UL
#define SMN_IOHUB2_N13NBIO0_REDIRECTION_TABLE_ENTRY_HIGH_ADDRESS      0x29010acUL
#define SMN_IOHUB2_N13NBIO1_REDIRECTION_TABLE_ENTRY_HIGH_ADDRESS      0x2b010acUL
#define SMN_IOHUB2_N14NBIO0_REDIRECTION_TABLE_ENTRY_HIGH_ADDRESS      0x29010b4UL
#define SMN_IOHUB2_N14NBIO1_REDIRECTION_TABLE_ENTRY_HIGH_ADDRESS      0x2b010b4UL
#define SMN_IOHUB2_N15NBIO0_REDIRECTION_TABLE_ENTRY_HIGH_ADDRESS      0x29010bcUL
#define SMN_IOHUB2_N15NBIO1_REDIRECTION_TABLE_ENTRY_HIGH_ADDRESS      0x2b010bcUL
#define SMN_IOHUB2_N16NBIO0_REDIRECTION_TABLE_ENTRY_HIGH_ADDRESS      0x29010c4UL
#define SMN_IOHUB2_N16NBIO1_REDIRECTION_TABLE_ENTRY_HIGH_ADDRESS      0x2b010c4UL
#define SMN_IOHUB2_N17NBIO0_REDIRECTION_TABLE_ENTRY_HIGH_ADDRESS      0x29010ccUL
#define SMN_IOHUB2_N17NBIO1_REDIRECTION_TABLE_ENTRY_HIGH_ADDRESS      0x2b010ccUL
#define SMN_IOHUB2_N18NBIO0_REDIRECTION_TABLE_ENTRY_HIGH_ADDRESS      0x29010d4UL
#define SMN_IOHUB2_N18NBIO1_REDIRECTION_TABLE_ENTRY_HIGH_ADDRESS      0x2b010d4UL
#define SMN_IOHUB2_N19NBIO0_REDIRECTION_TABLE_ENTRY_HIGH_ADDRESS      0x29010dcUL
#define SMN_IOHUB2_N19NBIO1_REDIRECTION_TABLE_ENTRY_HIGH_ADDRESS      0x2b010dcUL
#define SMN_IOHUB2_N1NBIO0_REDIRECTION_TABLE_ENTRY_HIGH_ADDRESS       0x290104cUL
#define SMN_IOHUB2_N1NBIO1_REDIRECTION_TABLE_ENTRY_HIGH_ADDRESS       0x2b0104cUL
#define SMN_IOHUB2_N20NBIO0_REDIRECTION_TABLE_ENTRY_HIGH_ADDRESS      0x29010e4UL
#define SMN_IOHUB2_N20NBIO1_REDIRECTION_TABLE_ENTRY_HIGH_ADDRESS      0x2b010e4UL
#define SMN_IOHUB2_N21NBIO0_REDIRECTION_TABLE_ENTRY_HIGH_ADDRESS      0x29010ecUL
#define SMN_IOHUB2_N21NBIO1_REDIRECTION_TABLE_ENTRY_HIGH_ADDRESS      0x2b010ecUL
#define SMN_IOHUB2_N22NBIO0_REDIRECTION_TABLE_ENTRY_HIGH_ADDRESS      0x29010f4UL
#define SMN_IOHUB2_N22NBIO1_REDIRECTION_TABLE_ENTRY_HIGH_ADDRESS      0x2b010f4UL
#define SMN_IOHUB2_N23NBIO0_REDIRECTION_TABLE_ENTRY_HIGH_ADDRESS      0x29010fcUL
#define SMN_IOHUB2_N23NBIO1_REDIRECTION_TABLE_ENTRY_HIGH_ADDRESS      0x2b010fcUL
#define SMN_IOHUB2_N24NBIO0_REDIRECTION_TABLE_ENTRY_HIGH_ADDRESS      0x2901104UL
#define SMN_IOHUB2_N24NBIO1_REDIRECTION_TABLE_ENTRY_HIGH_ADDRESS      0x2b01104UL
#define SMN_IOHUB2_N25NBIO0_REDIRECTION_TABLE_ENTRY_HIGH_ADDRESS      0x290110cUL
#define SMN_IOHUB2_N25NBIO1_REDIRECTION_TABLE_ENTRY_HIGH_ADDRESS      0x2b0110cUL
#define SMN_IOHUB2_N26NBIO0_REDIRECTION_TABLE_ENTRY_HIGH_ADDRESS      0x2901114UL
#define SMN_IOHUB2_N26NBIO1_REDIRECTION_TABLE_ENTRY_HIGH_ADDRESS      0x2b01114UL
#define SMN_IOHUB2_N27NBIO0_REDIRECTION_TABLE_ENTRY_HIGH_ADDRESS      0x290111cUL
#define SMN_IOHUB2_N27NBIO1_REDIRECTION_TABLE_ENTRY_HIGH_ADDRESS      0x2b0111cUL
#define SMN_IOHUB2_N28NBIO0_REDIRECTION_TABLE_ENTRY_HIGH_ADDRESS      0x2901124UL
#define SMN_IOHUB2_N28NBIO1_REDIRECTION_TABLE_ENTRY_HIGH_ADDRESS      0x2b01124UL
#define SMN_IOHUB2_N29NBIO0_REDIRECTION_TABLE_ENTRY_HIGH_ADDRESS      0x290112cUL
#define SMN_IOHUB2_N29NBIO1_REDIRECTION_TABLE_ENTRY_HIGH_ADDRESS      0x2b0112cUL
#define SMN_IOHUB2_N2NBIO0_REDIRECTION_TABLE_ENTRY_HIGH_ADDRESS       0x2901054UL
#define SMN_IOHUB2_N2NBIO1_REDIRECTION_TABLE_ENTRY_HIGH_ADDRESS       0x2b01054UL
#define SMN_IOHUB2_N30NBIO0_REDIRECTION_TABLE_ENTRY_HIGH_ADDRESS      0x2901134UL
#define SMN_IOHUB2_N30NBIO1_REDIRECTION_TABLE_ENTRY_HIGH_ADDRESS      0x2b01134UL
#define SMN_IOHUB2_N31NBIO0_REDIRECTION_TABLE_ENTRY_HIGH_ADDRESS      0x290113cUL
#define SMN_IOHUB2_N31NBIO1_REDIRECTION_TABLE_ENTRY_HIGH_ADDRESS      0x2b0113cUL
#define SMN_IOHUB2_N3NBIO0_REDIRECTION_TABLE_ENTRY_HIGH_ADDRESS       0x290105cUL
#define SMN_IOHUB2_N3NBIO1_REDIRECTION_TABLE_ENTRY_HIGH_ADDRESS       0x2b0105cUL
#define SMN_IOHUB2_N4NBIO0_REDIRECTION_TABLE_ENTRY_HIGH_ADDRESS       0x2901064UL
#define SMN_IOHUB2_N4NBIO1_REDIRECTION_TABLE_ENTRY_HIGH_ADDRESS       0x2b01064UL
#define SMN_IOHUB2_N5NBIO0_REDIRECTION_TABLE_ENTRY_HIGH_ADDRESS       0x290106cUL
#define SMN_IOHUB2_N5NBIO1_REDIRECTION_TABLE_ENTRY_HIGH_ADDRESS       0x2b0106cUL
#define SMN_IOHUB2_N6NBIO0_REDIRECTION_TABLE_ENTRY_HIGH_ADDRESS       0x2901074UL
#define SMN_IOHUB2_N6NBIO1_REDIRECTION_TABLE_ENTRY_HIGH_ADDRESS       0x2b01074UL
#define SMN_IOHUB2_N7NBIO0_REDIRECTION_TABLE_ENTRY_HIGH_ADDRESS       0x290107cUL
#define SMN_IOHUB2_N7NBIO1_REDIRECTION_TABLE_ENTRY_HIGH_ADDRESS       0x2b0107cUL
#define SMN_IOHUB2_N8NBIO0_REDIRECTION_TABLE_ENTRY_HIGH_ADDRESS       0x2901084UL
#define SMN_IOHUB2_N8NBIO1_REDIRECTION_TABLE_ENTRY_HIGH_ADDRESS       0x2b01084UL
#define SMN_IOHUB2_N9NBIO0_REDIRECTION_TABLE_ENTRY_HIGH_ADDRESS       0x290108cUL
#define SMN_IOHUB2_N9NBIO1_REDIRECTION_TABLE_ENTRY_HIGH_ADDRESS       0x2b0108cUL
#define SMN_IOHUB3_N0NBIO0_REDIRECTION_TABLE_ENTRY_HIGH_ADDRESS       0x1d101044UL
#define SMN_IOHUB3_N0NBIO1_REDIRECTION_TABLE_ENTRY_HIGH_ADDRESS       0x1d301044UL
#define SMN_IOHUB3_N10NBIO0_REDIRECTION_TABLE_ENTRY_HIGH_ADDRESS      0x1d101094UL
#define SMN_IOHUB3_N10NBIO1_REDIRECTION_TABLE_ENTRY_HIGH_ADDRESS      0x1d301094UL
#define SMN_IOHUB3_N11NBIO0_REDIRECTION_TABLE_ENTRY_HIGH_ADDRESS      0x1d10109cUL
#define SMN_IOHUB3_N11NBIO1_REDIRECTION_TABLE_ENTRY_HIGH_ADDRESS      0x1d30109cUL
#define SMN_IOHUB3_N12NBIO0_REDIRECTION_TABLE_ENTRY_HIGH_ADDRESS      0x1d1010a4UL
#define SMN_IOHUB3_N12NBIO1_REDIRECTION_TABLE_ENTRY_HIGH_ADDRESS      0x1d3010a4UL
#define SMN_IOHUB3_N13NBIO0_REDIRECTION_TABLE_ENTRY_HIGH_ADDRESS      0x1d1010acUL
#define SMN_IOHUB3_N13NBIO1_REDIRECTION_TABLE_ENTRY_HIGH_ADDRESS      0x1d3010acUL
#define SMN_IOHUB3_N14NBIO0_REDIRECTION_TABLE_ENTRY_HIGH_ADDRESS      0x1d1010b4UL
#define SMN_IOHUB3_N14NBIO1_REDIRECTION_TABLE_ENTRY_HIGH_ADDRESS      0x1d3010b4UL
#define SMN_IOHUB3_N15NBIO0_REDIRECTION_TABLE_ENTRY_HIGH_ADDRESS      0x1d1010bcUL
#define SMN_IOHUB3_N15NBIO1_REDIRECTION_TABLE_ENTRY_HIGH_ADDRESS      0x1d3010bcUL
#define SMN_IOHUB3_N16NBIO0_REDIRECTION_TABLE_ENTRY_HIGH_ADDRESS      0x1d1010c4UL
#define SMN_IOHUB3_N16NBIO1_REDIRECTION_TABLE_ENTRY_HIGH_ADDRESS      0x1d3010c4UL
#define SMN_IOHUB3_N17NBIO0_REDIRECTION_TABLE_ENTRY_HIGH_ADDRESS      0x1d1010ccUL
#define SMN_IOHUB3_N17NBIO1_REDIRECTION_TABLE_ENTRY_HIGH_ADDRESS      0x1d3010ccUL
#define SMN_IOHUB3_N18NBIO0_REDIRECTION_TABLE_ENTRY_HIGH_ADDRESS      0x1d1010d4UL
#define SMN_IOHUB3_N18NBIO1_REDIRECTION_TABLE_ENTRY_HIGH_ADDRESS      0x1d3010d4UL
#define SMN_IOHUB3_N19NBIO0_REDIRECTION_TABLE_ENTRY_HIGH_ADDRESS      0x1d1010dcUL
#define SMN_IOHUB3_N19NBIO1_REDIRECTION_TABLE_ENTRY_HIGH_ADDRESS      0x1d3010dcUL
#define SMN_IOHUB3_N1NBIO0_REDIRECTION_TABLE_ENTRY_HIGH_ADDRESS       0x1d10104cUL
#define SMN_IOHUB3_N1NBIO1_REDIRECTION_TABLE_ENTRY_HIGH_ADDRESS       0x1d30104cUL
#define SMN_IOHUB3_N20NBIO0_REDIRECTION_TABLE_ENTRY_HIGH_ADDRESS      0x1d1010e4UL
#define SMN_IOHUB3_N20NBIO1_REDIRECTION_TABLE_ENTRY_HIGH_ADDRESS      0x1d3010e4UL
#define SMN_IOHUB3_N21NBIO0_REDIRECTION_TABLE_ENTRY_HIGH_ADDRESS      0x1d1010ecUL
#define SMN_IOHUB3_N21NBIO1_REDIRECTION_TABLE_ENTRY_HIGH_ADDRESS      0x1d3010ecUL
#define SMN_IOHUB3_N22NBIO0_REDIRECTION_TABLE_ENTRY_HIGH_ADDRESS      0x1d1010f4UL
#define SMN_IOHUB3_N22NBIO1_REDIRECTION_TABLE_ENTRY_HIGH_ADDRESS      0x1d3010f4UL
#define SMN_IOHUB3_N23NBIO0_REDIRECTION_TABLE_ENTRY_HIGH_ADDRESS      0x1d1010fcUL
#define SMN_IOHUB3_N23NBIO1_REDIRECTION_TABLE_ENTRY_HIGH_ADDRESS      0x1d3010fcUL
#define SMN_IOHUB3_N24NBIO0_REDIRECTION_TABLE_ENTRY_HIGH_ADDRESS      0x1d101104UL
#define SMN_IOHUB3_N24NBIO1_REDIRECTION_TABLE_ENTRY_HIGH_ADDRESS      0x1d301104UL
#define SMN_IOHUB3_N25NBIO0_REDIRECTION_TABLE_ENTRY_HIGH_ADDRESS      0x1d10110cUL
#define SMN_IOHUB3_N25NBIO1_REDIRECTION_TABLE_ENTRY_HIGH_ADDRESS      0x1d30110cUL
#define SMN_IOHUB3_N26NBIO0_REDIRECTION_TABLE_ENTRY_HIGH_ADDRESS      0x1d101114UL
#define SMN_IOHUB3_N26NBIO1_REDIRECTION_TABLE_ENTRY_HIGH_ADDRESS      0x1d301114UL
#define SMN_IOHUB3_N27NBIO0_REDIRECTION_TABLE_ENTRY_HIGH_ADDRESS      0x1d10111cUL
#define SMN_IOHUB3_N27NBIO1_REDIRECTION_TABLE_ENTRY_HIGH_ADDRESS      0x1d30111cUL
#define SMN_IOHUB3_N28NBIO0_REDIRECTION_TABLE_ENTRY_HIGH_ADDRESS      0x1d101124UL
#define SMN_IOHUB3_N28NBIO1_REDIRECTION_TABLE_ENTRY_HIGH_ADDRESS      0x1d301124UL
#define SMN_IOHUB3_N29NBIO0_REDIRECTION_TABLE_ENTRY_HIGH_ADDRESS      0x1d10112cUL
#define SMN_IOHUB3_N29NBIO1_REDIRECTION_TABLE_ENTRY_HIGH_ADDRESS      0x1d30112cUL
#define SMN_IOHUB3_N2NBIO0_REDIRECTION_TABLE_ENTRY_HIGH_ADDRESS       0x1d101054UL
#define SMN_IOHUB3_N2NBIO1_REDIRECTION_TABLE_ENTRY_HIGH_ADDRESS       0x1d301054UL
#define SMN_IOHUB3_N30NBIO0_REDIRECTION_TABLE_ENTRY_HIGH_ADDRESS      0x1d101134UL
#define SMN_IOHUB3_N30NBIO1_REDIRECTION_TABLE_ENTRY_HIGH_ADDRESS      0x1d301134UL
#define SMN_IOHUB3_N31NBIO0_REDIRECTION_TABLE_ENTRY_HIGH_ADDRESS      0x1d10113cUL
#define SMN_IOHUB3_N31NBIO1_REDIRECTION_TABLE_ENTRY_HIGH_ADDRESS      0x1d30113cUL
#define SMN_IOHUB3_N3NBIO0_REDIRECTION_TABLE_ENTRY_HIGH_ADDRESS       0x1d10105cUL
#define SMN_IOHUB3_N3NBIO1_REDIRECTION_TABLE_ENTRY_HIGH_ADDRESS       0x1d30105cUL
#define SMN_IOHUB3_N4NBIO0_REDIRECTION_TABLE_ENTRY_HIGH_ADDRESS       0x1d101064UL
#define SMN_IOHUB3_N4NBIO1_REDIRECTION_TABLE_ENTRY_HIGH_ADDRESS       0x1d301064UL
#define SMN_IOHUB3_N5NBIO0_REDIRECTION_TABLE_ENTRY_HIGH_ADDRESS       0x1d10106cUL
#define SMN_IOHUB3_N5NBIO1_REDIRECTION_TABLE_ENTRY_HIGH_ADDRESS       0x1d30106cUL
#define SMN_IOHUB3_N6NBIO0_REDIRECTION_TABLE_ENTRY_HIGH_ADDRESS       0x1d101074UL
#define SMN_IOHUB3_N6NBIO1_REDIRECTION_TABLE_ENTRY_HIGH_ADDRESS       0x1d301074UL
#define SMN_IOHUB3_N7NBIO0_REDIRECTION_TABLE_ENTRY_HIGH_ADDRESS       0x1d10107cUL
#define SMN_IOHUB3_N7NBIO1_REDIRECTION_TABLE_ENTRY_HIGH_ADDRESS       0x1d30107cUL
#define SMN_IOHUB3_N8NBIO0_REDIRECTION_TABLE_ENTRY_HIGH_ADDRESS       0x1d101084UL
#define SMN_IOHUB3_N8NBIO1_REDIRECTION_TABLE_ENTRY_HIGH_ADDRESS       0x1d301084UL
#define SMN_IOHUB3_N9NBIO0_REDIRECTION_TABLE_ENTRY_HIGH_ADDRESS       0x1d10108cUL
#define SMN_IOHUB3_N9NBIO1_REDIRECTION_TABLE_ENTRY_HIGH_ADDRESS       0x1d30108cUL


/***********************************************************
* Register Name : REDIRECTION_TABLE_ENTRY_LOW
************************************************************/

#define REDIRECTION_TABLE_ENTRY_LOW_Vector_OFFSET              0
#define REDIRECTION_TABLE_ENTRY_LOW_Vector_MASK                0xff

#define REDIRECTION_TABLE_ENTRY_LOW_Delivery_Mode_OFFSET       8
#define REDIRECTION_TABLE_ENTRY_LOW_Delivery_Mode_MASK         0x700

#define REDIRECTION_TABLE_ENTRY_LOW_Destination_Mode_OFFSET    11
#define REDIRECTION_TABLE_ENTRY_LOW_Destination_Mode_MASK      0x800

#define REDIRECTION_TABLE_ENTRY_LOW_Delivery_status_OFFSET     12
#define REDIRECTION_TABLE_ENTRY_LOW_Delivery_status_MASK       0x1000

#define REDIRECTION_TABLE_ENTRY_LOW_Interrupt_Pin_Polarity_OFFSET 13
#define REDIRECTION_TABLE_ENTRY_LOW_Interrupt_Pin_Polarity_MASK 0x2000

#define REDIRECTION_TABLE_ENTRY_LOW_Remote_IRP_OFFSET          14
#define REDIRECTION_TABLE_ENTRY_LOW_Remote_IRP_MASK            0x4000

#define REDIRECTION_TABLE_ENTRY_LOW_Trigger_Mode_OFFSET        15
#define REDIRECTION_TABLE_ENTRY_LOW_Trigger_Mode_MASK          0x8000

#define REDIRECTION_TABLE_ENTRY_LOW_Mask_OFFSET                16
#define REDIRECTION_TABLE_ENTRY_LOW_Mask_MASK                  0x10000

#define REDIRECTION_TABLE_ENTRY_LOW_Reserved_31_17_OFFSET      17
#define REDIRECTION_TABLE_ENTRY_LOW_Reserved_31_17_MASK        0xfffe0000

typedef union {
  struct {
    UINT32                                              Vector:8;
    UINT32                                       Delivery_Mode:3;
    UINT32                                    Destination_Mode:1;
    UINT32                                     Delivery_status:1;
    UINT32                              Interrupt_Pin_Polarity:1;
    UINT32                                          Remote_IRP:1;
    UINT32                                        Trigger_Mode:1;
    UINT32                                                Mask:1;
    UINT32                                      Reserved_31_17:15;
  } Field;
  UINT32 Value;
} REDIRECTION_TABLE_ENTRY_LOW_STRUCT;

#define SMN_REDIRECTION_TABLE_ENTRY_LOW_ADDRESS                       0x2801040UL
#define SMN_IOHUB0_N0NBIO0_REDIRECTION_TABLE_ENTRY_LOW_ADDRESS        0x2801040UL
#define SMN_IOHUB0_N0NBIO1_REDIRECTION_TABLE_ENTRY_LOW_ADDRESS        0x2a01040UL
#define SMN_IOHUB0_N10NBIO0_REDIRECTION_TABLE_ENTRY_LOW_ADDRESS       0x2801090UL
#define SMN_IOHUB0_N10NBIO1_REDIRECTION_TABLE_ENTRY_LOW_ADDRESS       0x2a01090UL
#define SMN_IOHUB0_N11NBIO0_REDIRECTION_TABLE_ENTRY_LOW_ADDRESS       0x2801098UL
#define SMN_IOHUB0_N11NBIO1_REDIRECTION_TABLE_ENTRY_LOW_ADDRESS       0x2a01098UL
#define SMN_IOHUB0_N12NBIO0_REDIRECTION_TABLE_ENTRY_LOW_ADDRESS       0x28010a0UL
#define SMN_IOHUB0_N12NBIO1_REDIRECTION_TABLE_ENTRY_LOW_ADDRESS       0x2a010a0UL
#define SMN_IOHUB0_N13NBIO0_REDIRECTION_TABLE_ENTRY_LOW_ADDRESS       0x28010a8UL
#define SMN_IOHUB0_N13NBIO1_REDIRECTION_TABLE_ENTRY_LOW_ADDRESS       0x2a010a8UL
#define SMN_IOHUB0_N14NBIO0_REDIRECTION_TABLE_ENTRY_LOW_ADDRESS       0x28010b0UL
#define SMN_IOHUB0_N14NBIO1_REDIRECTION_TABLE_ENTRY_LOW_ADDRESS       0x2a010b0UL
#define SMN_IOHUB0_N15NBIO0_REDIRECTION_TABLE_ENTRY_LOW_ADDRESS       0x28010b8UL
#define SMN_IOHUB0_N15NBIO1_REDIRECTION_TABLE_ENTRY_LOW_ADDRESS       0x2a010b8UL
#define SMN_IOHUB0_N16NBIO0_REDIRECTION_TABLE_ENTRY_LOW_ADDRESS       0x28010c0UL
#define SMN_IOHUB0_N16NBIO1_REDIRECTION_TABLE_ENTRY_LOW_ADDRESS       0x2a010c0UL
#define SMN_IOHUB0_N17NBIO0_REDIRECTION_TABLE_ENTRY_LOW_ADDRESS       0x28010c8UL
#define SMN_IOHUB0_N17NBIO1_REDIRECTION_TABLE_ENTRY_LOW_ADDRESS       0x2a010c8UL
#define SMN_IOHUB0_N18NBIO0_REDIRECTION_TABLE_ENTRY_LOW_ADDRESS       0x28010d0UL
#define SMN_IOHUB0_N18NBIO1_REDIRECTION_TABLE_ENTRY_LOW_ADDRESS       0x2a010d0UL
#define SMN_IOHUB0_N19NBIO0_REDIRECTION_TABLE_ENTRY_LOW_ADDRESS       0x28010d8UL
#define SMN_IOHUB0_N19NBIO1_REDIRECTION_TABLE_ENTRY_LOW_ADDRESS       0x2a010d8UL
#define SMN_IOHUB0_N1NBIO0_REDIRECTION_TABLE_ENTRY_LOW_ADDRESS        0x2801048UL
#define SMN_IOHUB0_N1NBIO1_REDIRECTION_TABLE_ENTRY_LOW_ADDRESS        0x2a01048UL
#define SMN_IOHUB0_N20NBIO0_REDIRECTION_TABLE_ENTRY_LOW_ADDRESS       0x28010e0UL
#define SMN_IOHUB0_N20NBIO1_REDIRECTION_TABLE_ENTRY_LOW_ADDRESS       0x2a010e0UL
#define SMN_IOHUB0_N21NBIO0_REDIRECTION_TABLE_ENTRY_LOW_ADDRESS       0x28010e8UL
#define SMN_IOHUB0_N21NBIO1_REDIRECTION_TABLE_ENTRY_LOW_ADDRESS       0x2a010e8UL
#define SMN_IOHUB0_N22NBIO0_REDIRECTION_TABLE_ENTRY_LOW_ADDRESS       0x28010f0UL
#define SMN_IOHUB0_N22NBIO1_REDIRECTION_TABLE_ENTRY_LOW_ADDRESS       0x2a010f0UL
#define SMN_IOHUB0_N23NBIO0_REDIRECTION_TABLE_ENTRY_LOW_ADDRESS       0x28010f8UL
#define SMN_IOHUB0_N23NBIO1_REDIRECTION_TABLE_ENTRY_LOW_ADDRESS       0x2a010f8UL
#define SMN_IOHUB0_N24NBIO0_REDIRECTION_TABLE_ENTRY_LOW_ADDRESS       0x2801100UL
#define SMN_IOHUB0_N24NBIO1_REDIRECTION_TABLE_ENTRY_LOW_ADDRESS       0x2a01100UL
#define SMN_IOHUB0_N25NBIO0_REDIRECTION_TABLE_ENTRY_LOW_ADDRESS       0x2801108UL
#define SMN_IOHUB0_N25NBIO1_REDIRECTION_TABLE_ENTRY_LOW_ADDRESS       0x2a01108UL
#define SMN_IOHUB0_N26NBIO0_REDIRECTION_TABLE_ENTRY_LOW_ADDRESS       0x2801110UL
#define SMN_IOHUB0_N26NBIO1_REDIRECTION_TABLE_ENTRY_LOW_ADDRESS       0x2a01110UL
#define SMN_IOHUB0_N27NBIO0_REDIRECTION_TABLE_ENTRY_LOW_ADDRESS       0x2801118UL
#define SMN_IOHUB0_N27NBIO1_REDIRECTION_TABLE_ENTRY_LOW_ADDRESS       0x2a01118UL
#define SMN_IOHUB0_N28NBIO0_REDIRECTION_TABLE_ENTRY_LOW_ADDRESS       0x2801120UL
#define SMN_IOHUB0_N28NBIO1_REDIRECTION_TABLE_ENTRY_LOW_ADDRESS       0x2a01120UL
#define SMN_IOHUB0_N29NBIO0_REDIRECTION_TABLE_ENTRY_LOW_ADDRESS       0x2801128UL
#define SMN_IOHUB0_N29NBIO1_REDIRECTION_TABLE_ENTRY_LOW_ADDRESS       0x2a01128UL
#define SMN_IOHUB0_N2NBIO0_REDIRECTION_TABLE_ENTRY_LOW_ADDRESS        0x2801050UL
#define SMN_IOHUB0_N2NBIO1_REDIRECTION_TABLE_ENTRY_LOW_ADDRESS        0x2a01050UL
#define SMN_IOHUB0_N30NBIO0_REDIRECTION_TABLE_ENTRY_LOW_ADDRESS       0x2801130UL
#define SMN_IOHUB0_N30NBIO1_REDIRECTION_TABLE_ENTRY_LOW_ADDRESS       0x2a01130UL
#define SMN_IOHUB0_N31NBIO0_REDIRECTION_TABLE_ENTRY_LOW_ADDRESS       0x2801138UL
#define SMN_IOHUB0_N31NBIO1_REDIRECTION_TABLE_ENTRY_LOW_ADDRESS       0x2a01138UL
#define SMN_IOHUB0_N3NBIO0_REDIRECTION_TABLE_ENTRY_LOW_ADDRESS        0x2801058UL
#define SMN_IOHUB0_N3NBIO1_REDIRECTION_TABLE_ENTRY_LOW_ADDRESS        0x2a01058UL
#define SMN_IOHUB0_N4NBIO0_REDIRECTION_TABLE_ENTRY_LOW_ADDRESS        0x2801060UL
#define SMN_IOHUB0_N4NBIO1_REDIRECTION_TABLE_ENTRY_LOW_ADDRESS        0x2a01060UL
#define SMN_IOHUB0_N5NBIO0_REDIRECTION_TABLE_ENTRY_LOW_ADDRESS        0x2801068UL
#define SMN_IOHUB0_N5NBIO1_REDIRECTION_TABLE_ENTRY_LOW_ADDRESS        0x2a01068UL
#define SMN_IOHUB0_N6NBIO0_REDIRECTION_TABLE_ENTRY_LOW_ADDRESS        0x2801070UL
#define SMN_IOHUB0_N6NBIO1_REDIRECTION_TABLE_ENTRY_LOW_ADDRESS        0x2a01070UL
#define SMN_IOHUB0_N7NBIO0_REDIRECTION_TABLE_ENTRY_LOW_ADDRESS        0x2801078UL
#define SMN_IOHUB0_N7NBIO1_REDIRECTION_TABLE_ENTRY_LOW_ADDRESS        0x2a01078UL
#define SMN_IOHUB0_N8NBIO0_REDIRECTION_TABLE_ENTRY_LOW_ADDRESS        0x2801080UL
#define SMN_IOHUB0_N8NBIO1_REDIRECTION_TABLE_ENTRY_LOW_ADDRESS        0x2a01080UL
#define SMN_IOHUB0_N9NBIO0_REDIRECTION_TABLE_ENTRY_LOW_ADDRESS        0x2801088UL
#define SMN_IOHUB0_N9NBIO1_REDIRECTION_TABLE_ENTRY_LOW_ADDRESS        0x2a01088UL
#define SMN_IOHUB1_N0NBIO0_REDIRECTION_TABLE_ENTRY_LOW_ADDRESS        0x1d001040UL
#define SMN_IOHUB1_N0NBIO1_REDIRECTION_TABLE_ENTRY_LOW_ADDRESS        0x1d201040UL
#define SMN_IOHUB1_N10NBIO0_REDIRECTION_TABLE_ENTRY_LOW_ADDRESS       0x1d001090UL
#define SMN_IOHUB1_N10NBIO1_REDIRECTION_TABLE_ENTRY_LOW_ADDRESS       0x1d201090UL
#define SMN_IOHUB1_N11NBIO0_REDIRECTION_TABLE_ENTRY_LOW_ADDRESS       0x1d001098UL
#define SMN_IOHUB1_N11NBIO1_REDIRECTION_TABLE_ENTRY_LOW_ADDRESS       0x1d201098UL
#define SMN_IOHUB1_N12NBIO0_REDIRECTION_TABLE_ENTRY_LOW_ADDRESS       0x1d0010a0UL
#define SMN_IOHUB1_N12NBIO1_REDIRECTION_TABLE_ENTRY_LOW_ADDRESS       0x1d2010a0UL
#define SMN_IOHUB1_N13NBIO0_REDIRECTION_TABLE_ENTRY_LOW_ADDRESS       0x1d0010a8UL
#define SMN_IOHUB1_N13NBIO1_REDIRECTION_TABLE_ENTRY_LOW_ADDRESS       0x1d2010a8UL
#define SMN_IOHUB1_N14NBIO0_REDIRECTION_TABLE_ENTRY_LOW_ADDRESS       0x1d0010b0UL
#define SMN_IOHUB1_N14NBIO1_REDIRECTION_TABLE_ENTRY_LOW_ADDRESS       0x1d2010b0UL
#define SMN_IOHUB1_N15NBIO0_REDIRECTION_TABLE_ENTRY_LOW_ADDRESS       0x1d0010b8UL
#define SMN_IOHUB1_N15NBIO1_REDIRECTION_TABLE_ENTRY_LOW_ADDRESS       0x1d2010b8UL
#define SMN_IOHUB1_N16NBIO0_REDIRECTION_TABLE_ENTRY_LOW_ADDRESS       0x1d0010c0UL
#define SMN_IOHUB1_N16NBIO1_REDIRECTION_TABLE_ENTRY_LOW_ADDRESS       0x1d2010c0UL
#define SMN_IOHUB1_N17NBIO0_REDIRECTION_TABLE_ENTRY_LOW_ADDRESS       0x1d0010c8UL
#define SMN_IOHUB1_N17NBIO1_REDIRECTION_TABLE_ENTRY_LOW_ADDRESS       0x1d2010c8UL
#define SMN_IOHUB1_N18NBIO0_REDIRECTION_TABLE_ENTRY_LOW_ADDRESS       0x1d0010d0UL
#define SMN_IOHUB1_N18NBIO1_REDIRECTION_TABLE_ENTRY_LOW_ADDRESS       0x1d2010d0UL
#define SMN_IOHUB1_N19NBIO0_REDIRECTION_TABLE_ENTRY_LOW_ADDRESS       0x1d0010d8UL
#define SMN_IOHUB1_N19NBIO1_REDIRECTION_TABLE_ENTRY_LOW_ADDRESS       0x1d2010d8UL
#define SMN_IOHUB1_N1NBIO0_REDIRECTION_TABLE_ENTRY_LOW_ADDRESS        0x1d001048UL
#define SMN_IOHUB1_N1NBIO1_REDIRECTION_TABLE_ENTRY_LOW_ADDRESS        0x1d201048UL
#define SMN_IOHUB1_N20NBIO0_REDIRECTION_TABLE_ENTRY_LOW_ADDRESS       0x1d0010e0UL
#define SMN_IOHUB1_N20NBIO1_REDIRECTION_TABLE_ENTRY_LOW_ADDRESS       0x1d2010e0UL
#define SMN_IOHUB1_N21NBIO0_REDIRECTION_TABLE_ENTRY_LOW_ADDRESS       0x1d0010e8UL
#define SMN_IOHUB1_N21NBIO1_REDIRECTION_TABLE_ENTRY_LOW_ADDRESS       0x1d2010e8UL
#define SMN_IOHUB1_N22NBIO0_REDIRECTION_TABLE_ENTRY_LOW_ADDRESS       0x1d0010f0UL
#define SMN_IOHUB1_N22NBIO1_REDIRECTION_TABLE_ENTRY_LOW_ADDRESS       0x1d2010f0UL
#define SMN_IOHUB1_N23NBIO0_REDIRECTION_TABLE_ENTRY_LOW_ADDRESS       0x1d0010f8UL
#define SMN_IOHUB1_N23NBIO1_REDIRECTION_TABLE_ENTRY_LOW_ADDRESS       0x1d2010f8UL
#define SMN_IOHUB1_N24NBIO0_REDIRECTION_TABLE_ENTRY_LOW_ADDRESS       0x1d001100UL
#define SMN_IOHUB1_N24NBIO1_REDIRECTION_TABLE_ENTRY_LOW_ADDRESS       0x1d201100UL
#define SMN_IOHUB1_N25NBIO0_REDIRECTION_TABLE_ENTRY_LOW_ADDRESS       0x1d001108UL
#define SMN_IOHUB1_N25NBIO1_REDIRECTION_TABLE_ENTRY_LOW_ADDRESS       0x1d201108UL
#define SMN_IOHUB1_N26NBIO0_REDIRECTION_TABLE_ENTRY_LOW_ADDRESS       0x1d001110UL
#define SMN_IOHUB1_N26NBIO1_REDIRECTION_TABLE_ENTRY_LOW_ADDRESS       0x1d201110UL
#define SMN_IOHUB1_N27NBIO0_REDIRECTION_TABLE_ENTRY_LOW_ADDRESS       0x1d001118UL
#define SMN_IOHUB1_N27NBIO1_REDIRECTION_TABLE_ENTRY_LOW_ADDRESS       0x1d201118UL
#define SMN_IOHUB1_N28NBIO0_REDIRECTION_TABLE_ENTRY_LOW_ADDRESS       0x1d001120UL
#define SMN_IOHUB1_N28NBIO1_REDIRECTION_TABLE_ENTRY_LOW_ADDRESS       0x1d201120UL
#define SMN_IOHUB1_N29NBIO0_REDIRECTION_TABLE_ENTRY_LOW_ADDRESS       0x1d001128UL
#define SMN_IOHUB1_N29NBIO1_REDIRECTION_TABLE_ENTRY_LOW_ADDRESS       0x1d201128UL
#define SMN_IOHUB1_N2NBIO0_REDIRECTION_TABLE_ENTRY_LOW_ADDRESS        0x1d001050UL
#define SMN_IOHUB1_N2NBIO1_REDIRECTION_TABLE_ENTRY_LOW_ADDRESS        0x1d201050UL
#define SMN_IOHUB1_N30NBIO0_REDIRECTION_TABLE_ENTRY_LOW_ADDRESS       0x1d001130UL
#define SMN_IOHUB1_N30NBIO1_REDIRECTION_TABLE_ENTRY_LOW_ADDRESS       0x1d201130UL
#define SMN_IOHUB1_N31NBIO0_REDIRECTION_TABLE_ENTRY_LOW_ADDRESS       0x1d001138UL
#define SMN_IOHUB1_N31NBIO1_REDIRECTION_TABLE_ENTRY_LOW_ADDRESS       0x1d201138UL
#define SMN_IOHUB1_N3NBIO0_REDIRECTION_TABLE_ENTRY_LOW_ADDRESS        0x1d001058UL
#define SMN_IOHUB1_N3NBIO1_REDIRECTION_TABLE_ENTRY_LOW_ADDRESS        0x1d201058UL
#define SMN_IOHUB1_N4NBIO0_REDIRECTION_TABLE_ENTRY_LOW_ADDRESS        0x1d001060UL
#define SMN_IOHUB1_N4NBIO1_REDIRECTION_TABLE_ENTRY_LOW_ADDRESS        0x1d201060UL
#define SMN_IOHUB1_N5NBIO0_REDIRECTION_TABLE_ENTRY_LOW_ADDRESS        0x1d001068UL
#define SMN_IOHUB1_N5NBIO1_REDIRECTION_TABLE_ENTRY_LOW_ADDRESS        0x1d201068UL
#define SMN_IOHUB1_N6NBIO0_REDIRECTION_TABLE_ENTRY_LOW_ADDRESS        0x1d001070UL
#define SMN_IOHUB1_N6NBIO1_REDIRECTION_TABLE_ENTRY_LOW_ADDRESS        0x1d201070UL
#define SMN_IOHUB1_N7NBIO0_REDIRECTION_TABLE_ENTRY_LOW_ADDRESS        0x1d001078UL
#define SMN_IOHUB1_N7NBIO1_REDIRECTION_TABLE_ENTRY_LOW_ADDRESS        0x1d201078UL
#define SMN_IOHUB1_N8NBIO0_REDIRECTION_TABLE_ENTRY_LOW_ADDRESS        0x1d001080UL
#define SMN_IOHUB1_N8NBIO1_REDIRECTION_TABLE_ENTRY_LOW_ADDRESS        0x1d201080UL
#define SMN_IOHUB1_N9NBIO0_REDIRECTION_TABLE_ENTRY_LOW_ADDRESS        0x1d001088UL
#define SMN_IOHUB1_N9NBIO1_REDIRECTION_TABLE_ENTRY_LOW_ADDRESS        0x1d201088UL
#define SMN_IOHUB2_N0NBIO0_REDIRECTION_TABLE_ENTRY_LOW_ADDRESS        0x2901040UL
#define SMN_IOHUB2_N0NBIO1_REDIRECTION_TABLE_ENTRY_LOW_ADDRESS        0x2b01040UL
#define SMN_IOHUB2_N10NBIO0_REDIRECTION_TABLE_ENTRY_LOW_ADDRESS       0x2901090UL
#define SMN_IOHUB2_N10NBIO1_REDIRECTION_TABLE_ENTRY_LOW_ADDRESS       0x2b01090UL
#define SMN_IOHUB2_N11NBIO0_REDIRECTION_TABLE_ENTRY_LOW_ADDRESS       0x2901098UL
#define SMN_IOHUB2_N11NBIO1_REDIRECTION_TABLE_ENTRY_LOW_ADDRESS       0x2b01098UL
#define SMN_IOHUB2_N12NBIO0_REDIRECTION_TABLE_ENTRY_LOW_ADDRESS       0x29010a0UL
#define SMN_IOHUB2_N12NBIO1_REDIRECTION_TABLE_ENTRY_LOW_ADDRESS       0x2b010a0UL
#define SMN_IOHUB2_N13NBIO0_REDIRECTION_TABLE_ENTRY_LOW_ADDRESS       0x29010a8UL
#define SMN_IOHUB2_N13NBIO1_REDIRECTION_TABLE_ENTRY_LOW_ADDRESS       0x2b010a8UL
#define SMN_IOHUB2_N14NBIO0_REDIRECTION_TABLE_ENTRY_LOW_ADDRESS       0x29010b0UL
#define SMN_IOHUB2_N14NBIO1_REDIRECTION_TABLE_ENTRY_LOW_ADDRESS       0x2b010b0UL
#define SMN_IOHUB2_N15NBIO0_REDIRECTION_TABLE_ENTRY_LOW_ADDRESS       0x29010b8UL
#define SMN_IOHUB2_N15NBIO1_REDIRECTION_TABLE_ENTRY_LOW_ADDRESS       0x2b010b8UL
#define SMN_IOHUB2_N16NBIO0_REDIRECTION_TABLE_ENTRY_LOW_ADDRESS       0x29010c0UL
#define SMN_IOHUB2_N16NBIO1_REDIRECTION_TABLE_ENTRY_LOW_ADDRESS       0x2b010c0UL
#define SMN_IOHUB2_N17NBIO0_REDIRECTION_TABLE_ENTRY_LOW_ADDRESS       0x29010c8UL
#define SMN_IOHUB2_N17NBIO1_REDIRECTION_TABLE_ENTRY_LOW_ADDRESS       0x2b010c8UL
#define SMN_IOHUB2_N18NBIO0_REDIRECTION_TABLE_ENTRY_LOW_ADDRESS       0x29010d0UL
#define SMN_IOHUB2_N18NBIO1_REDIRECTION_TABLE_ENTRY_LOW_ADDRESS       0x2b010d0UL
#define SMN_IOHUB2_N19NBIO0_REDIRECTION_TABLE_ENTRY_LOW_ADDRESS       0x29010d8UL
#define SMN_IOHUB2_N19NBIO1_REDIRECTION_TABLE_ENTRY_LOW_ADDRESS       0x2b010d8UL
#define SMN_IOHUB2_N1NBIO0_REDIRECTION_TABLE_ENTRY_LOW_ADDRESS        0x2901048UL
#define SMN_IOHUB2_N1NBIO1_REDIRECTION_TABLE_ENTRY_LOW_ADDRESS        0x2b01048UL
#define SMN_IOHUB2_N20NBIO0_REDIRECTION_TABLE_ENTRY_LOW_ADDRESS       0x29010e0UL
#define SMN_IOHUB2_N20NBIO1_REDIRECTION_TABLE_ENTRY_LOW_ADDRESS       0x2b010e0UL
#define SMN_IOHUB2_N21NBIO0_REDIRECTION_TABLE_ENTRY_LOW_ADDRESS       0x29010e8UL
#define SMN_IOHUB2_N21NBIO1_REDIRECTION_TABLE_ENTRY_LOW_ADDRESS       0x2b010e8UL
#define SMN_IOHUB2_N22NBIO0_REDIRECTION_TABLE_ENTRY_LOW_ADDRESS       0x29010f0UL
#define SMN_IOHUB2_N22NBIO1_REDIRECTION_TABLE_ENTRY_LOW_ADDRESS       0x2b010f0UL
#define SMN_IOHUB2_N23NBIO0_REDIRECTION_TABLE_ENTRY_LOW_ADDRESS       0x29010f8UL
#define SMN_IOHUB2_N23NBIO1_REDIRECTION_TABLE_ENTRY_LOW_ADDRESS       0x2b010f8UL
#define SMN_IOHUB2_N24NBIO0_REDIRECTION_TABLE_ENTRY_LOW_ADDRESS       0x2901100UL
#define SMN_IOHUB2_N24NBIO1_REDIRECTION_TABLE_ENTRY_LOW_ADDRESS       0x2b01100UL
#define SMN_IOHUB2_N25NBIO0_REDIRECTION_TABLE_ENTRY_LOW_ADDRESS       0x2901108UL
#define SMN_IOHUB2_N25NBIO1_REDIRECTION_TABLE_ENTRY_LOW_ADDRESS       0x2b01108UL
#define SMN_IOHUB2_N26NBIO0_REDIRECTION_TABLE_ENTRY_LOW_ADDRESS       0x2901110UL
#define SMN_IOHUB2_N26NBIO1_REDIRECTION_TABLE_ENTRY_LOW_ADDRESS       0x2b01110UL
#define SMN_IOHUB2_N27NBIO0_REDIRECTION_TABLE_ENTRY_LOW_ADDRESS       0x2901118UL
#define SMN_IOHUB2_N27NBIO1_REDIRECTION_TABLE_ENTRY_LOW_ADDRESS       0x2b01118UL
#define SMN_IOHUB2_N28NBIO0_REDIRECTION_TABLE_ENTRY_LOW_ADDRESS       0x2901120UL
#define SMN_IOHUB2_N28NBIO1_REDIRECTION_TABLE_ENTRY_LOW_ADDRESS       0x2b01120UL
#define SMN_IOHUB2_N29NBIO0_REDIRECTION_TABLE_ENTRY_LOW_ADDRESS       0x2901128UL
#define SMN_IOHUB2_N29NBIO1_REDIRECTION_TABLE_ENTRY_LOW_ADDRESS       0x2b01128UL
#define SMN_IOHUB2_N2NBIO0_REDIRECTION_TABLE_ENTRY_LOW_ADDRESS        0x2901050UL
#define SMN_IOHUB2_N2NBIO1_REDIRECTION_TABLE_ENTRY_LOW_ADDRESS        0x2b01050UL
#define SMN_IOHUB2_N30NBIO0_REDIRECTION_TABLE_ENTRY_LOW_ADDRESS       0x2901130UL
#define SMN_IOHUB2_N30NBIO1_REDIRECTION_TABLE_ENTRY_LOW_ADDRESS       0x2b01130UL
#define SMN_IOHUB2_N31NBIO0_REDIRECTION_TABLE_ENTRY_LOW_ADDRESS       0x2901138UL
#define SMN_IOHUB2_N31NBIO1_REDIRECTION_TABLE_ENTRY_LOW_ADDRESS       0x2b01138UL
#define SMN_IOHUB2_N3NBIO0_REDIRECTION_TABLE_ENTRY_LOW_ADDRESS        0x2901058UL
#define SMN_IOHUB2_N3NBIO1_REDIRECTION_TABLE_ENTRY_LOW_ADDRESS        0x2b01058UL
#define SMN_IOHUB2_N4NBIO0_REDIRECTION_TABLE_ENTRY_LOW_ADDRESS        0x2901060UL
#define SMN_IOHUB2_N4NBIO1_REDIRECTION_TABLE_ENTRY_LOW_ADDRESS        0x2b01060UL
#define SMN_IOHUB2_N5NBIO0_REDIRECTION_TABLE_ENTRY_LOW_ADDRESS        0x2901068UL
#define SMN_IOHUB2_N5NBIO1_REDIRECTION_TABLE_ENTRY_LOW_ADDRESS        0x2b01068UL
#define SMN_IOHUB2_N6NBIO0_REDIRECTION_TABLE_ENTRY_LOW_ADDRESS        0x2901070UL
#define SMN_IOHUB2_N6NBIO1_REDIRECTION_TABLE_ENTRY_LOW_ADDRESS        0x2b01070UL
#define SMN_IOHUB2_N7NBIO0_REDIRECTION_TABLE_ENTRY_LOW_ADDRESS        0x2901078UL
#define SMN_IOHUB2_N7NBIO1_REDIRECTION_TABLE_ENTRY_LOW_ADDRESS        0x2b01078UL
#define SMN_IOHUB2_N8NBIO0_REDIRECTION_TABLE_ENTRY_LOW_ADDRESS        0x2901080UL
#define SMN_IOHUB2_N8NBIO1_REDIRECTION_TABLE_ENTRY_LOW_ADDRESS        0x2b01080UL
#define SMN_IOHUB2_N9NBIO0_REDIRECTION_TABLE_ENTRY_LOW_ADDRESS        0x2901088UL
#define SMN_IOHUB2_N9NBIO1_REDIRECTION_TABLE_ENTRY_LOW_ADDRESS        0x2b01088UL
#define SMN_IOHUB3_N0NBIO0_REDIRECTION_TABLE_ENTRY_LOW_ADDRESS        0x1d101040UL
#define SMN_IOHUB3_N0NBIO1_REDIRECTION_TABLE_ENTRY_LOW_ADDRESS        0x1d301040UL
#define SMN_IOHUB3_N10NBIO0_REDIRECTION_TABLE_ENTRY_LOW_ADDRESS       0x1d101090UL
#define SMN_IOHUB3_N10NBIO1_REDIRECTION_TABLE_ENTRY_LOW_ADDRESS       0x1d301090UL
#define SMN_IOHUB3_N11NBIO0_REDIRECTION_TABLE_ENTRY_LOW_ADDRESS       0x1d101098UL
#define SMN_IOHUB3_N11NBIO1_REDIRECTION_TABLE_ENTRY_LOW_ADDRESS       0x1d301098UL
#define SMN_IOHUB3_N12NBIO0_REDIRECTION_TABLE_ENTRY_LOW_ADDRESS       0x1d1010a0UL
#define SMN_IOHUB3_N12NBIO1_REDIRECTION_TABLE_ENTRY_LOW_ADDRESS       0x1d3010a0UL
#define SMN_IOHUB3_N13NBIO0_REDIRECTION_TABLE_ENTRY_LOW_ADDRESS       0x1d1010a8UL
#define SMN_IOHUB3_N13NBIO1_REDIRECTION_TABLE_ENTRY_LOW_ADDRESS       0x1d3010a8UL
#define SMN_IOHUB3_N14NBIO0_REDIRECTION_TABLE_ENTRY_LOW_ADDRESS       0x1d1010b0UL
#define SMN_IOHUB3_N14NBIO1_REDIRECTION_TABLE_ENTRY_LOW_ADDRESS       0x1d3010b0UL
#define SMN_IOHUB3_N15NBIO0_REDIRECTION_TABLE_ENTRY_LOW_ADDRESS       0x1d1010b8UL
#define SMN_IOHUB3_N15NBIO1_REDIRECTION_TABLE_ENTRY_LOW_ADDRESS       0x1d3010b8UL
#define SMN_IOHUB3_N16NBIO0_REDIRECTION_TABLE_ENTRY_LOW_ADDRESS       0x1d1010c0UL
#define SMN_IOHUB3_N16NBIO1_REDIRECTION_TABLE_ENTRY_LOW_ADDRESS       0x1d3010c0UL
#define SMN_IOHUB3_N17NBIO0_REDIRECTION_TABLE_ENTRY_LOW_ADDRESS       0x1d1010c8UL
#define SMN_IOHUB3_N17NBIO1_REDIRECTION_TABLE_ENTRY_LOW_ADDRESS       0x1d3010c8UL
#define SMN_IOHUB3_N18NBIO0_REDIRECTION_TABLE_ENTRY_LOW_ADDRESS       0x1d1010d0UL
#define SMN_IOHUB3_N18NBIO1_REDIRECTION_TABLE_ENTRY_LOW_ADDRESS       0x1d3010d0UL
#define SMN_IOHUB3_N19NBIO0_REDIRECTION_TABLE_ENTRY_LOW_ADDRESS       0x1d1010d8UL
#define SMN_IOHUB3_N19NBIO1_REDIRECTION_TABLE_ENTRY_LOW_ADDRESS       0x1d3010d8UL
#define SMN_IOHUB3_N1NBIO0_REDIRECTION_TABLE_ENTRY_LOW_ADDRESS        0x1d101048UL
#define SMN_IOHUB3_N1NBIO1_REDIRECTION_TABLE_ENTRY_LOW_ADDRESS        0x1d301048UL
#define SMN_IOHUB3_N20NBIO0_REDIRECTION_TABLE_ENTRY_LOW_ADDRESS       0x1d1010e0UL
#define SMN_IOHUB3_N20NBIO1_REDIRECTION_TABLE_ENTRY_LOW_ADDRESS       0x1d3010e0UL
#define SMN_IOHUB3_N21NBIO0_REDIRECTION_TABLE_ENTRY_LOW_ADDRESS       0x1d1010e8UL
#define SMN_IOHUB3_N21NBIO1_REDIRECTION_TABLE_ENTRY_LOW_ADDRESS       0x1d3010e8UL
#define SMN_IOHUB3_N22NBIO0_REDIRECTION_TABLE_ENTRY_LOW_ADDRESS       0x1d1010f0UL
#define SMN_IOHUB3_N22NBIO1_REDIRECTION_TABLE_ENTRY_LOW_ADDRESS       0x1d3010f0UL
#define SMN_IOHUB3_N23NBIO0_REDIRECTION_TABLE_ENTRY_LOW_ADDRESS       0x1d1010f8UL
#define SMN_IOHUB3_N23NBIO1_REDIRECTION_TABLE_ENTRY_LOW_ADDRESS       0x1d3010f8UL
#define SMN_IOHUB3_N24NBIO0_REDIRECTION_TABLE_ENTRY_LOW_ADDRESS       0x1d101100UL
#define SMN_IOHUB3_N24NBIO1_REDIRECTION_TABLE_ENTRY_LOW_ADDRESS       0x1d301100UL
#define SMN_IOHUB3_N25NBIO0_REDIRECTION_TABLE_ENTRY_LOW_ADDRESS       0x1d101108UL
#define SMN_IOHUB3_N25NBIO1_REDIRECTION_TABLE_ENTRY_LOW_ADDRESS       0x1d301108UL
#define SMN_IOHUB3_N26NBIO0_REDIRECTION_TABLE_ENTRY_LOW_ADDRESS       0x1d101110UL
#define SMN_IOHUB3_N26NBIO1_REDIRECTION_TABLE_ENTRY_LOW_ADDRESS       0x1d301110UL
#define SMN_IOHUB3_N27NBIO0_REDIRECTION_TABLE_ENTRY_LOW_ADDRESS       0x1d101118UL
#define SMN_IOHUB3_N27NBIO1_REDIRECTION_TABLE_ENTRY_LOW_ADDRESS       0x1d301118UL
#define SMN_IOHUB3_N28NBIO0_REDIRECTION_TABLE_ENTRY_LOW_ADDRESS       0x1d101120UL
#define SMN_IOHUB3_N28NBIO1_REDIRECTION_TABLE_ENTRY_LOW_ADDRESS       0x1d301120UL
#define SMN_IOHUB3_N29NBIO0_REDIRECTION_TABLE_ENTRY_LOW_ADDRESS       0x1d101128UL
#define SMN_IOHUB3_N29NBIO1_REDIRECTION_TABLE_ENTRY_LOW_ADDRESS       0x1d301128UL
#define SMN_IOHUB3_N2NBIO0_REDIRECTION_TABLE_ENTRY_LOW_ADDRESS        0x1d101050UL
#define SMN_IOHUB3_N2NBIO1_REDIRECTION_TABLE_ENTRY_LOW_ADDRESS        0x1d301050UL
#define SMN_IOHUB3_N30NBIO0_REDIRECTION_TABLE_ENTRY_LOW_ADDRESS       0x1d101130UL
#define SMN_IOHUB3_N30NBIO1_REDIRECTION_TABLE_ENTRY_LOW_ADDRESS       0x1d301130UL
#define SMN_IOHUB3_N31NBIO0_REDIRECTION_TABLE_ENTRY_LOW_ADDRESS       0x1d101138UL
#define SMN_IOHUB3_N31NBIO1_REDIRECTION_TABLE_ENTRY_LOW_ADDRESS       0x1d301138UL
#define SMN_IOHUB3_N3NBIO0_REDIRECTION_TABLE_ENTRY_LOW_ADDRESS        0x1d101058UL
#define SMN_IOHUB3_N3NBIO1_REDIRECTION_TABLE_ENTRY_LOW_ADDRESS        0x1d301058UL
#define SMN_IOHUB3_N4NBIO0_REDIRECTION_TABLE_ENTRY_LOW_ADDRESS        0x1d101060UL
#define SMN_IOHUB3_N4NBIO1_REDIRECTION_TABLE_ENTRY_LOW_ADDRESS        0x1d301060UL
#define SMN_IOHUB3_N5NBIO0_REDIRECTION_TABLE_ENTRY_LOW_ADDRESS        0x1d101068UL
#define SMN_IOHUB3_N5NBIO1_REDIRECTION_TABLE_ENTRY_LOW_ADDRESS        0x1d301068UL
#define SMN_IOHUB3_N6NBIO0_REDIRECTION_TABLE_ENTRY_LOW_ADDRESS        0x1d101070UL
#define SMN_IOHUB3_N6NBIO1_REDIRECTION_TABLE_ENTRY_LOW_ADDRESS        0x1d301070UL
#define SMN_IOHUB3_N7NBIO0_REDIRECTION_TABLE_ENTRY_LOW_ADDRESS        0x1d101078UL
#define SMN_IOHUB3_N7NBIO1_REDIRECTION_TABLE_ENTRY_LOW_ADDRESS        0x1d301078UL
#define SMN_IOHUB3_N8NBIO0_REDIRECTION_TABLE_ENTRY_LOW_ADDRESS        0x1d101080UL
#define SMN_IOHUB3_N8NBIO1_REDIRECTION_TABLE_ENTRY_LOW_ADDRESS        0x1d301080UL
#define SMN_IOHUB3_N9NBIO0_REDIRECTION_TABLE_ENTRY_LOW_ADDRESS        0x1d101088UL
#define SMN_IOHUB3_N9NBIO1_REDIRECTION_TABLE_ENTRY_LOW_ADDRESS        0x1d301088UL

#endif /* _IOAPIC_H_ */

