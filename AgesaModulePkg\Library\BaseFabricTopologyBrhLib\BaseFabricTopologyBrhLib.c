/*
 ******************************************************************************
 *
 * Copyright (C) 2020-2024 Advanced Micro Devices, Inc. All rights reserved.
 *
 *******************************************************************************
 */

/**
 * @file
 *
 * Fabric Topology Base Lib implementation for BRH
 *
 * @xrefitem bom "File Content Label" "Release Content"
 * @e project:      AGESA
 * @e sub-project:  Fabric
 * @e \$Revision$   @e \$Date$
 *
 */

/*----------------------------------------------------------------------------------------
 *                             M O D U L E S    U S E D
 *----------------------------------------------------------------------------------------
 */
#include <Uefi.h>
#include <Library/BaseLib.h>
#include <Library/BaseMemoryLib.h>
#include <Library/FabricRegisterAccLib.h>
#include <Library/IdsLib.h>
#include <Library/AmdHeapLib.h>
#include <Library/AmdSocBaseLib.h>
#include <Library/BaseFabricTopologyLib.h>
#include <FabricInfoBrh.h>
#include <FabricInfoRs.h>
#include <Filecode.h>

/*----------------------------------------------------------------------------------------
 *                   D E F I N I T I O N S    A N D    M A C R O S
 *----------------------------------------------------------------------------------------
 */
#define FILECODE LIBRARY_BASEFABRICTOPOLOGYBRHRSLIB_BASEFABRICTOPOLOGYBRHRSLIB_FILECODE

#define NO_FABRIC_ID                     0xFFFFFFFF

#define SYSTEMCFG_FUNC                   0x4
#define SYSTEMCFG_REG                    0x180
#define SYSTEM_COMPONENT_CNT_FUNC        0x4
#define SYSTEM_COMPONENT_CNT_REG         0x184
#define CLUSTERCFG_FUNC                  0x4
#define CLUSTERCFG_REG                   0x1A8
#define CFGBASEADDRESS_0_FUNC            0x0
#define CFGBASEADDRESS_0_REG             0xC80
#define CFGLIMITADDRESS_0_FUNC           0x0
#define CFGLIMITADDRESS_0_REG            0xC84

#define REGION_REGISTER_OFFSET 8

#define MAX_NUMBER_OF_DEVICE_IDS   0x100

/*----------------------------------------------------------------------------------------
 *                  T Y P E D E F S     A N D     S T R U C T U R E S
 *----------------------------------------------------------------------------------------
 */

/// DF4 Configuration Base Address Maps Register
typedef union {
  struct {
    UINT32 RE:1;                            ///< Read Enable.
    UINT32 WE:1;                            ///< Write Enable.
    UINT32 :6;                              ///< Reserved
    UINT32 SegmentNum:8;                    ///< Segment Number for this configuration-space address map.
    UINT32 BusNumBase:8;                    ///< Bus Number Base Bits[7:0] for this configuration-space address map. See DF::CfgAddressCntl[SecBusNum].
    UINT32 :8;                              ///< Reserved
  } Field;
  UINT32  Value;
} CFG_BASE_ADDRESS_REGISTER;

/// DF4 Configuration Address Map Control Register
typedef union {
  struct {
    UINT32 DstFabricID:12;                   ///< For transactions which hit in this range, this field specifies the destination FabricID which may be
                                            //   local or on a remote die.
    UINT32 :4;                              ///< Reserved
    UINT32 BusNumLimit:8;                   ///< Bus Number Limit Bits[7:0] for this IO Space.
    UINT32 :8;                              ///< Reserved
  } Field;
  UINT32  Value;
} CFG_LIMIT_ADDRESS_REGISTER;

/// Structure for program specific DF instance information
typedef struct {
  FABRIC_DEVICE_TYPE DeviceType;         ///< The instance type of the fabric device
  UINT32             BaseInstanceId;     ///< The instance ID of the first fabric device of the devices with the same instance type
  UINT32             InstanceCount;      ///< The count of the fabric devices with the same instance type
  BOOLEAN            HasFabricId;        ///< This type has fabric ID or not.
} BASE_DEVICE_INFO;

/// Structure for the device map to be stored to the heap
typedef struct {
  FABRIC_DEVICE_TYPE   Type;             ///< The instance type of the fabric device
  UINT32               Count;            ///< The count of the fabric devices with the same instance type
  UINT32               IDsIndex;         ///< The index of the array of the FABRIC_TOPOLOGY_MAP[DeviceIds] to this instance type
} BASE_DEVICE_MAP;

/// Structure for the device IDS to be stored to the heap
typedef struct {
  UINT32  FabricID;                      ///< Fabric ID
  UINT32  InstanceID;                    ///< Instance ID
} BASE_DEVICE_IDS;

/// Structure for the fabric topology information
typedef struct {
  UINT32                             FabricIdSocketShift;    ///< The socket shift in the fabric ID
  UINT32                             FabricIdDieShift;       ///< The die shift in the fabric ID
  UINT32                             NumberOfBusRegions;     ///< The number of the PCI bus regions
  UINT32                             FchLocationInstanceId;  ///< The instance ID of FCH located
  UINT32                             SmuLocationInstanceId;  ///< The instance ID of SMU located
  UINT32                             Ios0InstanceId;         ///< The instance ID of IOS0
  UINT32                             IosDeviceMapIndex;      ///< The index of the IosDeviceMap in FABRIC_TOPOLOGY_MAP[Map]
} BASE_FABRIC_TOPOLOGY_INFO;

/// Structure for the fabric topology map to be stored to the heap
typedef struct {
  BASE_FABRIC_TOPOLOGY_INFO          BaseFabricTopologyInfo;               ///< The base fabric topology information
  BASE_DEVICE_MAP                    Map[FabricDeviceTypeMax + 1];         ///< The buffer for the device map
  BASE_DEVICE_IDS                    DeviceIds[MAX_NUMBER_OF_DEVICE_IDS];  ///< The buffer for the device IDs
} FABRIC_TOPOLOGY_MAP;

UINT32 RsFchLocationInstanceId = RS_IOS2_INSTANCE_ID;
UINT32 RsSmuLocationInstanceId = RS_IOS2_INSTANCE_ID;
BASE_DEVICE_INFO RsDeviceBaseInfo[] = {
  {Cs,                  RS_CS0_INSTANCE_ID,    RS_NUM_CS_BLOCKS,     TRUE  },
  {CsUmc,               RS_CSUMC0_INSTANCE_ID, RS_NUM_CS_UMC_BLOCKS, TRUE  },
  {CsCmp,               RS_CSCMP0_INSTANCE_ID, RS_NUM_CS_CMP_BLOCKS, TRUE  },
  {Ccm,                 RS_CCM0_INSTANCE_ID,   RS_NUM_CCM_BLOCKS,    TRUE  },
  {Acm,                 RS_ACM0_INSTANCE_ID,   RS_NUM_ACM_BLOCKS,    TRUE  },
  {Ncm,                 RS_NCM0_INSTANCE_ID,   RS_NUM_NCM_BLOCKS,    TRUE  },
  {Iom,                 RS_IOMS0_INSTANCE_ID,  RS_NUM_IOMS_BLOCKS,   TRUE  },
  {Ios,                 RS_IOS0_INSTANCE_ID,   RS_NUM_IOS_BLOCKS,    TRUE  },
  {Icng,                RS_ICNG0_INSTANCE_ID,  RS_NUM_ICNG_BLOCKS,   TRUE  },
  {Pie,                 RS_PIE_INSTANCE_ID,    RS_NUM_PIE_BLOCKS,    TRUE  },
  {Cake,                RS_CAKE0_INSTANCE_ID,  RS_NUM_CAKE_BLOCKS,   FALSE },
  {Cnli,                RS_CNLI0_INSTANCE_ID,  RS_NUM_CNLI_BLOCKS,   FALSE },
  {Pfx,                 RS_PFX0_INSTANCE_ID,   RS_NUM_PFX_BLOCKS,    FALSE },
  {Spf,                 RS_SPF0_INSTANCE_ID,   RS_NUM_SPF_BLOCKS,    FALSE },
  {Tcdx,                RS_TCDX0_INSTANCE_ID,  RS_NUM_TCDX_BLOCKS,   FALSE },
  {FabricDeviceTypeMax, 0,                     0,                    FALSE },
};

UINT32 BrhFchLocationInstanceId = BRH_IOS4_INSTANCE_ID;
UINT32 BrhSmuLocationInstanceId = BRH_IOS4_INSTANCE_ID;
BASE_DEVICE_INFO BrhDeviceBaseInfo[] = {
  {Cs,                  BRH_CS0_INSTANCE_ID,    BRH_NUM_CS_BLOCKS,     TRUE  },
  {CsUmc,               BRH_CSUMC0_INSTANCE_ID, BRH_NUM_CS_UMC_BLOCKS, TRUE  },
  {CsCmp,               BRH_CSCMP0_INSTANCE_ID, BRH_NUM_CS_CMP_BLOCKS, TRUE  },
  {Ccm,                 BRH_CCM0_INSTANCE_ID,   BRH_NUM_CCM_BLOCKS,    TRUE  },
  {Acm,                 BRH_ACM0_INSTANCE_ID,   BRH_NUM_ACM_BLOCKS,    TRUE  },
  {Ncm,                 BRH_NCM0_INSTANCE_ID,   BRH_NUM_NCM_BLOCKS,    TRUE  },
  {Iom,                 BRH_IOMS0_INSTANCE_ID,  BRH_NUM_IOMS_BLOCKS,   TRUE  },
  {Ios,                 BRH_IOS0_INSTANCE_ID,   BRH_NUM_IOS_BLOCKS,    TRUE  },
  {Icng,                BRH_ICNG0_INSTANCE_ID,  BRH_NUM_ICNG_BLOCKS,   TRUE  },
  {Pie,                 BRH_PIE_INSTANCE_ID,    BRH_NUM_PIE_BLOCKS,    TRUE  },
  {Cake,                BRH_CAKE0_INSTANCE_ID,  BRH_NUM_CAKE_BLOCKS,   FALSE },
  {Cnli,                BRH_CNLI0_INSTANCE_ID,  BRH_NUM_CNLI_BLOCKS,   FALSE },
  {Pfx,                 BRH_PFX0_INSTANCE_ID,   BRH_NUM_PFX_BLOCKS,    FALSE },
  {Spf,                 BRH_SPF0_INSTANCE_ID,   BRH_NUM_SPF_BLOCKS,    FALSE },
  {Tcdx,                BRH_TCDX0_INSTANCE_ID,  BRH_NUM_TCDX_BLOCKS,   FALSE },
  {FabricDeviceTypeMax, 0,                      0,                     FALSE },
};

/// Buffer for base fabric topology information
BASE_FABRIC_TOPOLOGY_INFO gBaseFabricTopologyInfo = {
  MAX_UINT32,
  MAX_UINT32,
  MAX_UINT32,
  MAX_UINT32,
  MAX_UINT32,
  MAX_UINT32,
  MAX_UINT32,
};

/// Buffer for the device map array
AMD_FABRIC_TOPOLOGY_DIE_DEVICE_MAP gDeviceMap[FabricDeviceTypeMax + 1];

/// Buffer for device IDs array
DEVICE_IDS gDeviceIds[MAX_NUMBER_OF_DEVICE_IDS];

/*----------------------------------------------------------------------------------------
 *           P R O T O T Y P E S     O F     L O C A L     F U N C T I O N S
 *----------------------------------------------------------------------------------------
 */

UINT8
GetNumberOfBusRegions (
  VOID
  );

/*---------------------------------------------------------------------------------------*/
/**
 * Get Number of Bus Regions
 *
 * Return number of PCIe config register instance.
 *
 * @return  The number of PCI bus regions
 */
UINT8
GetNumberOfBusRegions (VOID)
{
  return (UINT8) gBaseFabricTopologyInfo.NumberOfBusRegions;
}

UINTN
FabricTopologyGetNumberOfProcessorsPresent (
  VOID
  )
{
  return (UINTN) (BitFieldRead32 (FabricRegisterAccRead (0, 0, CLUSTERCFG_FUNC, CLUSTERCFG_REG, FABRIC_REG_ACC_BC), 30, 31) + 1);
}

UINTN
FabricTopologyGetNumberOfSystemDies (
  VOID
  )
{
  UINT32 Value32 = FabricRegisterAccRead (0, 0, SYSTEM_COMPONENT_CNT_FUNC, SYSTEM_COMPONENT_CNT_REG, FABRIC_REG_ACC_BC);

  return (UINTN) (Value32 & 0xFF);
}

UINTN
FabricTopologyGetNumberOfSystemRootBridges (
  VOID
  )
{
  UINTN                    RbCount;
  UINTN                    SocketCount;
  UINTN                    Socket;

  SocketCount = FabricTopologyGetNumberOfProcessorsPresent ();
  RbCount = 0;
  for (Socket = 0; Socket < SocketCount; Socket++) {
    RbCount += FabricTopologyGetNumberOfRootBridgesOnSocket (Socket);
  }

  return RbCount;
}

UINTN
FabricTopologyGetNumberOfPciSegments (
  VOID
  )
{
  UINTN                    Socket;
  UINTN                    SocketCount;
  UINTN                    Die;
  UINTN                    DieCount;
  UINTN                    RB;
  UINTN                    RBCount;
  UINTN                    CurrentPciLimit;
  UINTN                    PciLimit;

  CurrentPciLimit = 0;

  SocketCount = FabricTopologyGetNumberOfProcessorsPresent ();
  for (Socket = 0; Socket < SocketCount; Socket++) {
    DieCount = FabricTopologyGetNumberOfDiesOnSocket (Socket);
    for (Die = 0; Die < DieCount; Die++) {
      RBCount = FabricTopologyGetNumberOfRootBridgesOnDie (Socket, Die);
      for (RB = 0; RB < RBCount; RB++) {
        PciLimit = FabricTopologyGetHostBridgeBusLimit (Socket, Die, RB);
        if (PciLimit > CurrentPciLimit) {
          CurrentPciLimit = PciLimit;
        }
      }
    }
  }

  CurrentPciLimit += 1;
  CurrentPciLimit = (CurrentPciLimit / MAX_PCI_BUS_NUMBER_PER_SEGMENT) + (((CurrentPciLimit % MAX_PCI_BUS_NUMBER_PER_SEGMENT) == 0) ? 0 : 1);

  return CurrentPciLimit;
}

UINTN
FabricTopologyGetNumberOfDiesOnSocket (
  IN     UINTN Socket
  )
{
  //TODO: the current code is assuming the same die number on every sockets
  return (FabricTopologyGetNumberOfSystemDies () / FabricTopologyGetNumberOfProcessorsPresent ());
}

UINTN
FabricTopologyGetNumberOfRootBridgesOnSocket (
  IN     UINTN Socket
  )
{
  UINTN                    RbCount;
  UINTN                    DieCount;
  UINTN                    Die;

  DieCount = FabricTopologyGetNumberOfDiesOnSocket (Socket);
  RbCount = 0;
  for (Die = 0; Die < DieCount; Die++) {
    RbCount += FabricTopologyGetNumberOfRootBridgesOnDie (Socket, Die);
  }

  return RbCount;
}

UINTN
FabricTopologyGetNumberOfRootBridgesOnDie (
  IN     UINTN Socket,
  IN     UINTN Die
  )
{
  return gDeviceMap[gBaseFabricTopologyInfo.IosDeviceMapIndex].Count;
}

UINTN
FabricTopologyGetDieSystemOffset (
  IN     UINTN Socket,
  IN     UINTN Die
  )
{
  return (Socket << gBaseFabricTopologyInfo.FabricIdSocketShift) + (Die << gBaseFabricTopologyInfo.FabricIdDieShift);
}

CONST
AMD_FABRIC_TOPOLOGY_DIE_DEVICE_MAP *
FabricTopologyGetDeviceMapOnDie (
  IN     UINTN Socket,
  IN     UINTN Die
  )
{
  //TODO: Create the device map for each die
  return gDeviceMap;
}

UINTN
FabricTopologyGetHostBridgeSystemFabricID (
  IN     UINTN Socket,
  IN     UINTN Die,
  IN     UINTN Index
  )
{
  CONST AMD_FABRIC_TOPOLOGY_DIE_DEVICE_MAP *IosEntry = FindDeviceTypeEntryInMap (Socket, Die, Ios);

  ASSERT (IosEntry != NULL);
  ASSERT (Index < IosEntry->Count);

  return (FabricTopologyGetDieSystemOffset (Socket, Die) + IosEntry->IDs[Index].FabricID);
}

UINTN
FabricTopologyGetHostBridgeBusBase (
  IN     UINTN Socket,
  IN     UINTN Die,
  IN     UINTN Index
  )
{
  CFG_LIMIT_ADDRESS_REGISTER CfgLimit;
  CFG_BASE_ADDRESS_REGISTER  CfgBase;
  UINT8                      NumberOfBusRegions;
  UINTN                      TargetFabricId;

  NumberOfBusRegions = GetNumberOfBusRegions ();
  TargetFabricId     = FabricTopologyGetHostBridgeSystemFabricID (Socket, Die, Index);

  for (UINTN CfgAddrMapIndex = 0; CfgAddrMapIndex < NumberOfBusRegions; CfgAddrMapIndex++) {
    CfgLimit.Value = FabricRegisterAccRead (Socket,
                                            Die,
                                            CFGLIMITADDRESS_0_FUNC,
                                            CFGLIMITADDRESS_0_REG + (CfgAddrMapIndex * REGION_REGISTER_OFFSET),
                                            FABRIC_REG_ACC_BC);

    CfgBase.Value = FabricRegisterAccRead (Socket,
                                           Die,
                                           CFGBASEADDRESS_0_FUNC,
                                           CFGBASEADDRESS_0_REG + (CfgAddrMapIndex * REGION_REGISTER_OFFSET),
                                           FABRIC_REG_ACC_BC);

    // FWDEV-61901: Clear the WA for Turin C0 to set CfgBase.Field.SegmentNum as 0x80 for local segment(s)
    CfgBase.Field.SegmentNum = BRH_GET_SEGMENT_NUMBER (CfgBase);

    if ((CfgBase.Field.RE == 1) &&
        (CfgBase.Field.WE == 1) &&
        (CfgLimit.Field.DstFabricID == (UINT32) TargetFabricId)) {
      return ((UINTN) CfgBase.Field.SegmentNum) * MAX_PCI_BUS_NUMBER_PER_SEGMENT + ((UINTN) CfgBase.Field.BusNumBase);
    }
  }

  return (UINTN)-1;
}

UINTN
FabricTopologyGetHostBridgeBusLimit (
  IN     UINTN Socket,
  IN     UINTN Die,
  IN     UINTN Index
  )
{
  CFG_LIMIT_ADDRESS_REGISTER CfgLimit;
  CFG_BASE_ADDRESS_REGISTER  CfgBase;
  UINT8                      NumberOfBusRegions;
  UINTN                      TargetFabricId;

  NumberOfBusRegions = GetNumberOfBusRegions ();
  TargetFabricId     = FabricTopologyGetHostBridgeSystemFabricID (Socket, Die, Index);

  for (UINTN CfgAddrMapIndex = 0; CfgAddrMapIndex < NumberOfBusRegions; CfgAddrMapIndex++) {
    CfgLimit.Value = FabricRegisterAccRead (0, 0, CFGLIMITADDRESS_0_FUNC, (CFGLIMITADDRESS_0_REG + (CfgAddrMapIndex * REGION_REGISTER_OFFSET)), FABRIC_REG_ACC_BC);

    CfgBase.Value = FabricRegisterAccRead (0, 0, CFGBASEADDRESS_0_FUNC, (CFGBASEADDRESS_0_REG + (CfgAddrMapIndex * REGION_REGISTER_OFFSET)), FABRIC_REG_ACC_BC);

    // FWDEV-61901: Clear the WA for Turin C0 to set CfgBase.Field.SegmentNum as 0x80 for local segment(s)
    CfgBase.Field.SegmentNum = BRH_GET_SEGMENT_NUMBER (CfgBase);

    if ((CfgBase.Field.RE == 1) &&
        (CfgBase.Field.WE == 1) &&
        (CfgLimit.Field.DstFabricID == (UINT32) TargetFabricId)) {
      return ((UINTN) CfgBase.Field.SegmentNum) * MAX_PCI_BUS_NUMBER_PER_SEGMENT + ((UINTN) CfgLimit.Field.BusNumLimit);
    }
  }

  return 0xFF;
}

BOOLEAN
FabricTopologyGetSystemComponentRootBridgeLocation (
  IN     COMPONENT_TYPE        Component,
  IN OUT ROOT_BRIDGE_LOCATION *Location
  )
{
  AMD_FABRIC_TOPOLOGY_DIE_DEVICE_MAP *IosMap;
  UINT32                             TargetInstanceId;
  UINTN                              Index;

  if (!Location) {
    return FALSE;
  }

  switch (Component) {
  case PrimaryFch:
    TargetInstanceId = gBaseFabricTopologyInfo.FchLocationInstanceId;
    break;
  case PrimarySmu:
    TargetInstanceId = gBaseFabricTopologyInfo.SmuLocationInstanceId;
    break;
  default:
    ASSERT (FALSE);
    return FALSE;
  }

  IosMap = &gDeviceMap[gBaseFabricTopologyInfo.IosDeviceMapIndex];
  if (IosMap) {
    for (Index = 0; Index < IosMap->Count; Index++) {
      if (IosMap->IDs[Index].InstanceID == TargetInstanceId) {
        Location->Socket = 0;
        Location->Die = 0;
        Location->Index = Index;
        return TRUE;
      }
    }
  }

  ASSERT (FALSE);
  return FALSE;
}

BOOLEAN
FabricTopologyHasFch (
  IN     UINTN Socket,
  IN     UINTN Die,
  IN     UINTN Index
  )
{
  if (Index < gDeviceMap[gBaseFabricTopologyInfo.IosDeviceMapIndex].Count) {
    return (gDeviceMap[gBaseFabricTopologyInfo.IosDeviceMapIndex].IDs[Index].InstanceID == gBaseFabricTopologyInfo.FchLocationInstanceId);
  }

  ASSERT (FALSE);
  return FALSE;
}

BOOLEAN
FabricTopologyHasSmu (
  IN     UINTN Socket,
  IN     UINTN Die,
  IN     UINTN Index
  )
{
  if (Index < gDeviceMap[gBaseFabricTopologyInfo.IosDeviceMapIndex].Count) {
    return (gDeviceMap[gBaseFabricTopologyInfo.IosDeviceMapIndex].IDs[Index].InstanceID == gBaseFabricTopologyInfo.SmuLocationInstanceId);
  }

  ASSERT (FALSE);
  return FALSE;
}

UINTN
FabricTopologyGetPhysRootBridgeNumber (
  IN     UINTN Socket,
  IN     UINTN Die,
  IN     UINTN Index
  )
{
  if (Index < gDeviceMap[gBaseFabricTopologyInfo.IosDeviceMapIndex].Count) {
    return gDeviceMap[gBaseFabricTopologyInfo.IosDeviceMapIndex].IDs[Index].InstanceID - gBaseFabricTopologyInfo.Ios0InstanceId;
  }

  ASSERT (FALSE);
  return 0;
}

CONST
AMD_FABRIC_TOPOLOGY_DIE_DEVICE_MAP *
FindDeviceTypeEntryInMap (
  IN    UINTN              Socket,
  IN    UINTN              Die,
  IN    FABRIC_DEVICE_TYPE Type
  )
{
  CONST AMD_FABRIC_TOPOLOGY_DIE_DEVICE_MAP *DeviceMap = FabricTopologyGetDeviceMapOnDie (Socket, Die);

  for (UINTN i = 0; DeviceMap[i].Type < FabricDeviceTypeMax; i++) {
    if (DeviceMap[i].Type == Type) {
      return &DeviceMap[i];
    }
  }

  return NULL;
}

/**
 * @brief Prepare the topology map buffer to be stored to the heap
 * 
 * @return The pointer to the topology map buffer
 */
FABRIC_TOPOLOGY_MAP *
PrepareTopologyMap (
  VOID
  )
{
  FABRIC_TOPOLOGY_MAP                *FabricTopologyMap;
  BASE_DEVICE_INFO                   *BaseDeviceInfo;
  BASE_DEVICE_MAP                    *DeviceMap;
  BASE_DEVICE_IDS                    *DeviceIds;
  UINT32                             BaseDeviceInfoCount;
  UINT32                             Index;
  UINT32                             DeviceIndex;
  UINT32                             TypeIndex;
  UINT32                             TypeCount;
  UINT32                             InstanceCount;
  UINT32                             InstanceId;
  UINT32                             FabricId;
  UINT32                             FchLocationInstanceId;
  UINT32                             SmuLocationInstanceId;
  UINT32                             FabricIdSocketShift;
  UINT32                             FabricIdDieShift;
  UINT32                             NumberOfBusRegions;
  BOOLEAN                            Enabled;
  ALLOCATE_HEAP_PARAMS               AllocateHeapParams;
  BOOLEAN                            IsIodBrhAxWA;

  // Get the program specific data
  IsIodBrhAxWA = FALSE;
  if (ISSOCRS || ISSOCRSDN) {
    BaseDeviceInfo = RsDeviceBaseInfo;
    BaseDeviceInfoCount = ARRAY_SIZE (RsDeviceBaseInfo);
    FchLocationInstanceId = RsFchLocationInstanceId;
    SmuLocationInstanceId = RsSmuLocationInstanceId;
    FabricIdSocketShift = RS_FABRIC_ID_SOCKET_SHIFT;
    FabricIdDieShift = RS_FABRIC_ID_DIE_SHIFT;
    NumberOfBusRegions = RS_NUMBER_OF_BUS_REGIONS;
  } else if (ISSOCBRH || ISSOCBRHD) {
    BaseDeviceInfo = BrhDeviceBaseInfo;
    BaseDeviceInfoCount = ARRAY_SIZE (BrhDeviceBaseInfo);
    FchLocationInstanceId = BrhFchLocationInstanceId;
    SmuLocationInstanceId = BrhSmuLocationInstanceId;
    FabricIdSocketShift = BRH_FABRIC_ID_SOCKET_SHIFT;
    FabricIdDieShift = BRH_FABRIC_ID_DIE_SHIFT;
    NumberOfBusRegions = BRH_NUMBER_OF_BUS_REGIONS;
    if (IS_SOC_BRH_Ax) {
      NumberOfBusRegions = BRH_NUMBER_OF_BUS_REGIONS_Ax;
      if (FabricTopologyGetNumberOfProcessorsPresent() > 1) {
        IsIodBrhAxWA = TRUE;
      }
    }
  } else {
    ASSERT (FALSE);
    return NULL;
  }

  // Get the total instance count
  InstanceCount = 0;
  TypeCount = 0;
  for (Index = 0; BaseDeviceInfo[Index].DeviceType != FabricDeviceTypeMax; Index++) {
    InstanceCount += BaseDeviceInfo[Index].InstanceCount;
    TypeCount++;
  }
  if (TypeCount > FabricDeviceTypeMax) {
    ASSERT (FALSE);
    return NULL;
  }

  // Allocate the heap
  AllocateHeapParams.RequestedBufferSize = sizeof (FABRIC_TOPOLOGY_MAP);
  AllocateHeapParams.BufferHandle        = AMD_DF_FABRIC_ID_HANDLE;
  AllocateHeapParams.Persist             = HEAP_SYSTEM_MEM;
  if (HeapAllocateBuffer (&AllocateHeapParams, NULL) != AGESA_SUCCESS) {
    return NULL;
  }

  FabricTopologyMap = (FABRIC_TOPOLOGY_MAP *) (VOID *) AllocateHeapParams.BufferPtr;
  DeviceMap = FabricTopologyMap->Map;
  DeviceIds = FabricTopologyMap->DeviceIds;

  // Update the base fabric topology information
  FabricTopologyMap->BaseFabricTopologyInfo.FabricIdSocketShift = FabricIdSocketShift;
  FabricTopologyMap->BaseFabricTopologyInfo.FabricIdDieShift = FabricIdDieShift;
  FabricTopologyMap->BaseFabricTopologyInfo.NumberOfBusRegions = NumberOfBusRegions;
  FabricTopologyMap->BaseFabricTopologyInfo.FchLocationInstanceId = FchLocationInstanceId;
  FabricTopologyMap->BaseFabricTopologyInfo.SmuLocationInstanceId = SmuLocationInstanceId;
  FabricTopologyMap->BaseFabricTopologyInfo.Ios0InstanceId = MAX_UINT32;
  FabricTopologyMap->BaseFabricTopologyInfo.IosDeviceMapIndex = MAX_UINT32;

  // Check for each instance and update the DeviceMap and DeviceIds
  DeviceIndex = 0;
  for (TypeIndex = 0; TypeIndex < TypeCount; TypeIndex++) {
    if ((TypeIndex >= ARRAY_SIZE (FabricTopologyMap->Map)) || (TypeIndex >= BaseDeviceInfoCount)) {
      ASSERT (FALSE);
      break;
    }
    DeviceMap[TypeIndex].Type = BaseDeviceInfo[TypeIndex].DeviceType;
    DeviceMap[TypeIndex].Count = 0;
    DeviceMap[TypeIndex].IDsIndex = DeviceIndex;
    if ((DeviceMap[TypeIndex].Type == Ios) && (BaseDeviceInfo[TypeIndex].InstanceCount > 0)) {
      FabricTopologyMap->BaseFabricTopologyInfo.Ios0InstanceId = BaseDeviceInfo[TypeIndex].BaseInstanceId;
      FabricTopologyMap->BaseFabricTopologyInfo.IosDeviceMapIndex = TypeIndex;
    }
    for (Index = 0; Index < BaseDeviceInfo[TypeIndex].InstanceCount; Index++) {
      InstanceId = BaseDeviceInfo[TypeIndex].BaseInstanceId + Index;
      FabricId = BaseDeviceInfo[TypeIndex].HasFabricId ? BitFieldRead32 (FabricRegisterAccRead (0, 0, 0, 0x50, InstanceId), 8, 15) : NO_FABRIC_ID;
      Enabled = (FabricRegisterAccRead (0, 0, 0, 0x44, InstanceId) != 0);

      // for SimNow behavior
      if (FabricId == 0x55) {
        Enabled = FALSE;
      }

      if ((DeviceMap[TypeIndex].Type == Iom) || (DeviceMap[TypeIndex].Type == Ios)) {
        if (IsIodBrhAxWA && ((Index % 2) != 0)) {
          Enabled = FALSE;
        }
      }

      // Do not add the instance in the map if it's disabled
      if (Enabled) {
        ASSERT (DeviceIndex < MAX_NUMBER_OF_DEVICE_IDS);
        if (DeviceIndex < MAX_NUMBER_OF_DEVICE_IDS) {
          DeviceIds[DeviceIndex].InstanceID = InstanceId;
          DeviceIds[DeviceIndex].FabricID = FabricId;
          DeviceMap[TypeIndex].Count++;
          DeviceIndex++;
        }
      }
    }
  }
  //Prepare for FabricDeviceTypeMax
  DeviceMap[TypeIndex].Type = FabricDeviceTypeMax;
  DeviceMap[TypeIndex].Count = 0;
  DeviceMap[TypeIndex].IDsIndex = 0;

  ASSERT (FabricTopologyMap->BaseFabricTopologyInfo.Ios0InstanceId != MAX_UINT32);
  ASSERT (FabricTopologyMap->BaseFabricTopologyInfo.IosDeviceMapIndex != MAX_UINT32);

  return FabricTopologyMap;
}

/**
 *
 *  BaseFabricTopologyLib Constructor
 *  Prepare/locate the fabric topology map, and update to the gBaseFabricTopologyInfo, gDeviceMap, and gDeviceIds
 *
 *  @return     RETURN_STATUS
 */
RETURN_STATUS
EFIAPI
BaseFabricTopologyLibConstructor (
  VOID
  )
{
  FABRIC_TOPOLOGY_MAP *FabricTopologyMap;
  LOCATE_HEAP_PTR     LocateHeapParams;
  UINT32              TypeIndex;
  UINT32              DeviceIndex;
  BASE_DEVICE_MAP     *BaseDeviceMap;
  BASE_DEVICE_IDS     *BaseDeviceIds;

  LocateHeapParams.BufferHandle = AMD_DF_FABRIC_ID_HANDLE;
  if (HeapLocateBuffer (&LocateHeapParams, NULL) != AGESA_SUCCESS) {
    FabricTopologyMap = PrepareTopologyMap ();
  } else {
    FabricTopologyMap = (FABRIC_TOPOLOGY_MAP *) LocateHeapParams.BufferPtr;
  }

  ASSERT (FabricTopologyMap != NULL);
  if (FabricTopologyMap) {
    CopyMem (&gBaseFabricTopologyInfo, &FabricTopologyMap->BaseFabricTopologyInfo, sizeof (gBaseFabricTopologyInfo));
    BaseDeviceMap = &FabricTopologyMap->Map[0];
    BaseDeviceIds = &FabricTopologyMap->DeviceIds[0];

    for (TypeIndex = 0; BaseDeviceMap[TypeIndex].Type != FabricDeviceTypeMax; TypeIndex++) {
      ASSERT (TypeIndex < FabricDeviceTypeMax);
      gDeviceMap[TypeIndex].Type = BaseDeviceMap[TypeIndex].Type;
      gDeviceMap[TypeIndex].Count = BaseDeviceMap[TypeIndex].Count;
      gDeviceMap[TypeIndex].IDs = &gDeviceIds[BaseDeviceMap[TypeIndex].IDsIndex];
      for (DeviceIndex = 0; DeviceIndex < BaseDeviceMap[TypeIndex].Count; DeviceIndex++) {
        gDeviceIds[BaseDeviceMap[TypeIndex].IDsIndex + DeviceIndex].FabricID = BaseDeviceIds[BaseDeviceMap[TypeIndex].IDsIndex + DeviceIndex].FabricID;
        gDeviceIds[BaseDeviceMap[TypeIndex].IDsIndex + DeviceIndex].InstanceID = BaseDeviceIds[BaseDeviceMap[TypeIndex].IDsIndex + DeviceIndex].InstanceID;
      }
    }
    gDeviceMap[TypeIndex].Type = FabricDeviceTypeMax;
    gDeviceMap[TypeIndex].Count = 0;
    gDeviceMap[TypeIndex].IDs = NULL;
  }

  return EFI_SUCCESS;
}

