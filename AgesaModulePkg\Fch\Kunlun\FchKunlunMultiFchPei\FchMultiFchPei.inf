#;*****************************************************************************
#;
#; Copyright (C) 2008-2024 Advanced Micro Devices, Inc. All rights reserved.
#;
#;******************************************************************************

[defines]

  INF_VERSION                   = 0x00010005
  BASE_NAME                     = FchKunlunMultiFchPei
  FILE_GUID                     = 7c9aaa66-0d1a-4a23-bea3-25195f6bc22c
  MODULE_TYPE                   = PEIM
  VERSION_STRING                = 1.0
  ENTRY_POINT                   = MultiFchPeiInit

[sources.common]
  FchMultiFchPei.c
  FchMultiFchPei.h


[sources.IA32]

[sources.X64]

[LibraryClasses.IA32]

[LibraryClasses]
  FchBaseLib
  FchPeiLibV9
  FchKunlunPeiLib
  NbioHandleLib
  NbioSmuBrhLib
  FabricResourceManagerLib
  FabricRegisterAccLib
  AmdCapsuleLib

  PeimEntryPoint
  BaseLib
  DebugLib
  IoLib
  PeiServicesLib
  HobLib

[Guids]
  gFchMultiFchResetDataHobGuid

[Protocols]

[Ppis]
   gAmdFchInitPpiGuid                 #CONSUMED
   gAmdFabricTopologyServices2PpiGuid #CONSUMED
   gAmdFchMultiFchInitPpiGuid         #PRODUCED

[Packages]
  MdePkg/MdePkg.dec
  AgesaPkg/AgesaPkg.dec
  AgesaModulePkg/AgesaModuleDfPkg.dec
  AgesaModulePkg/AgesaModuleFchPkg.dec
  AgesaModulePkg/Fch/Kunlun/FchKunlun.dec

[Pcd]
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdSataEnable2
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdXhci2Enable
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdXhci3Enable
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdXhci0DevRemovable
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdXhciUsb3PortDisable
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdXhciUsb2PortDisable
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdXhciUsb20OcPinSelect
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdXhciUsb31OcPinSelect
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdALinkClkGateOff
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdBLinkClkGateOff
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdAbClockGating
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdSbgMemoryPowerSaving
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdSbgClockGating
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdXdmaDmaWrite16ByteMode
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdXdmaMemoryPowerSaving
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdXdmaPendingNprThreshold
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdXdmaDncplOrderDis
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdSdphostBypassDataPack
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdSdphostDisNpmwrProtect
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdXhciForceGen1
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdSataMultiDiePortRxPolarity

[Depex]
  gAmdFchInitPpiGuid AND
  gAmdFchKLMultiFchDepexPpiGuid AND
  gAmdFabricTopologyServices2PpiGuid

[BuildOptions]





