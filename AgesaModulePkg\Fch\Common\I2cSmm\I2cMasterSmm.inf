#;*****************************************************************************
#;
#; Copyright (C) 2008-2024 Advanced Micro Devices, Inc. All rights reserved.
#;
#;******************************************************************************

[defines]
  INF_VERSION                    = 0x00010005
  BASE_NAME                      = I2cMasterSmm
  FILE_GUID                      = 259495ea-2dd9-4ea5-a111-61f58781499d
  MODULE_TYPE                    = DXE_SMM_DRIVER
  PI_SPECIFICATION_VERSION       = 0x0001000A
  VERSION_STRING                 = 1.1
  ENTRY_POINT                    = I2cMasterSmmInit

[sources]
  I2cMasterSmm.c
  I2cMasterSmm.h

[LibraryClasses]
  UefiDriverEntryPoint
  BaseMemoryLib
  BaseLib
  SmmServicesTableLib
  UefiRuntimeServicesTableLib
  UefiBootServicesTableLib
  UefiLib
  FchI2cLib
  FchSocLib
  FchBaseLib
  IdsMiscLib
[Pcd]


[Guids]

[Protocols]
  gAmdFchSmmI2cMasterProtocolGuid #produced
  gAmdFabricTopologyServices2SmmProtocolGuid    #CONSUMED
[Packages]
  MdePkg/MdePkg.dec
  MdeModulePkg/MdeModulePkg.dec
  AgesaPkg/AgesaPkg.dec
  AgesaModulePkg/AgesaModuleFchPkg.dec
  AgesaModulePkg/AgesaModuleDfPkg.dec

[Depex]
  gEfiI2cMasterProtocolGuid AND
  gAmdFabricTopologyServices2SmmProtocolGuid



