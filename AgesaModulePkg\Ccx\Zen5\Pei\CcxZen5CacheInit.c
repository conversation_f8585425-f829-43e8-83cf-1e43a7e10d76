/*****************************************************************************
 * Copyright (C) 2008-2024 Advanced Micro Devices, Inc. All rights reserved.
 *
 *******************************************************************************
 */
/*++
Module Name:

  CcxZen5CacheInit.c
  Initialize Cache

Abstract:
--*/

#include <Library/BaseLib.h>
#include <AGESA.h>
#include <Filecode.h>
#include <Library/AmdBaseLib.h>
#include <cpuRegisters.h>

#define FILECODE CCX_ZEN5_PEI_CCXZEN5CACHEINIT_FILECODE

/* -----------------------------------------------------------------------------*/
/**
 *
 * Initialize vMTRRs and Program 0~TOM as WB.
 *
 */
VOID
InitializeVariableMTRRs (
  IN       AMD_CONFIG_PARAMS *StdHeader
  )
{
  UINT64 LocalMsrRegister;
  UINT32 CurBase;
  UINT32 CurLimit;
  UINT32 CurSize;
  UINT32 CurAddr;
  UINT32 Value32;
  UINT32 Cache32bTOP;
  UINT8  PhysicalAddressBits;
  UINT64 VariableMtrrMask;

  AsmDisableCache ();

  Value32 = 0;
  AsmCpuid (CPUID_LONG_MODE_ADDR, &Value32, NULL, NULL, NULL);
  PhysicalAddressBits = (UINT8)Value32;
  VariableMtrrMask = LShiftU64 (1, PhysicalAddressBits) - 1;
  VariableMtrrMask &= 0xFFFFFFFF00000000ull;

  LocalMsrRegister = AsmReadMsr64 (MSR_TOM);
  Cache32bTOP = (UINT32) (RShiftU64 (LocalMsrRegister, 16) & 0xFFFFFFFF);

  CurBase = 0;
  CurLimit = Cache32bTOP;
  CurAddr = AMD_MTRR_VARIABLE_BASE0;

  while ((CurAddr >= AMD_MTRR_VARIABLE_BASE0) && (CurAddr < AMD_MTRR_VARIABLE_BASE7) && (CurBase < Cache32bTOP)) {
    CurSize = CurLimit = (UINT32) 1 << (LowBitSet32 (CurBase) == -1 ? 31 : LowBitSet32 (CurBase)) ;
    CurLimit += CurBase;
    if ((CurBase == 0) || (Cache32bTOP < CurLimit)) {
      CurLimit = Cache32bTOP - CurBase;
      CurSize = CurLimit = (UINT32) 1 << (HighBitSet32 (CurLimit) == -1 ? 31 : HighBitSet32 (CurLimit));
      CurLimit += CurBase;
    }

    // prog. MTRR with current region Base
    LocalMsrRegister = (CurBase << 16) | 6;
    AsmWriteMsr64 (CurAddr, LocalMsrRegister);

    // prog. MTRR with current region Mask
    CurAddr++;                              // other half of MSR pair
    Value32 = CurSize - (UINT32) 1;
    Value32 = ~Value32;
    LocalMsrRegister = VariableMtrrMask | (Value32 << 16) | BIT11;
    AsmWriteMsr64 (CurAddr, LocalMsrRegister);

    CurBase = CurLimit;
    CurAddr++;                              // next MSR pair
  }

  if (CurLimit < Cache32bTOP) {
    // Announce failure
    IDS_ERROR_TRAP;
  }

  // Clear the vMTRR
  while ((CurAddr >= AMD_MTRR_VARIABLE_BASE0) && (CurAddr <= AMD_MTRR_VARIABLE_MASK7)) {
    if ((CurAddr % 2) == 0) {
      AsmWriteMsr64 (CurAddr + 1, 0);
      AsmWriteMsr64 (CurAddr, 0);
      CurAddr += 2;
      continue;
    }
    CurAddr++;
  }

  AsmEnableCache ();
}

/*++

Routine Description:

  Zen5 cache initialization

Arguments:
  AMD_CONFIG_PARAMS *

Returns:

--*/
VOID
CcxZen5CacheInit (
  IN       AMD_CONFIG_PARAMS  *StdHeader
  )
{
  volatile UINT64        LocalMsrRegister;

  //
  //======================================================================
  // Set default values for CPU registers
  //======================================================================
  //
  AsmMsrOr64 (MSR_SYS_CFG, BIT19);

  AsmWriteMsr64 (AMD_MTRR_FIX64k_00000, AMD_MTRR_FIX64K_WB_DRAM); // 0 - 512K = WB Mem
  AsmWriteMsr64 (AMD_MTRR_FIX16k_80000, AMD_MTRR_FIX16K_WB_DRAM); // 512K - 640K = WB Mem

  AsmMsrOr64 (MSR_SYS_CFG, (BIT18 | BIT20));

  LocalMsrRegister = AsmReadMsr64 (MSR_SYS_CFG);
  if ((LocalMsrRegister & BIT21) != 0) {
    LocalMsrRegister |= BIT22;
  }

  LocalMsrRegister &= ~BIT19;
  AsmWriteMsr64 (MSR_SYS_CFG, LocalMsrRegister);
}

