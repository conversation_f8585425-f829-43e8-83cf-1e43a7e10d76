#;*****************************************************************************
#;
#; Copyright (C) 2008-2024 Advanced Micro Devices, Inc. All rights reserved.
#;
#;******************************************************************************
#For EDKII use Only
[Defines]
  INF_VERSION                    = 0x00010006
  BASE_NAME                      = CcxIdsCustomPstatesNullLib
  FILE_GUID                      = F78BED89-53B0-46E4-BAC0-0B1E09A0ECE8
  MODULE_TYPE                    = BASE
  VERSION_STRING                 = 1.0
  LIBRARY_CLASS                  = CcxIdsCustomPstatesLib | DXE_DRIVER DXE_SMM_DRIVER

[Sources.common]
  CcxIdsCustomPstatesNullLib.c

[Packages]
  MdePkg/MdePkg.dec
  AgesaModulePkg/AgesaCommonModulePkg.dec
  AgesaModulePkg/AgesaModuleCcxPkg.dec

[LibraryClasses]

[Guids]

[Protocols]

[Ppis]

[Pcd]

[Depex]



