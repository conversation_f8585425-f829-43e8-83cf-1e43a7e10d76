#;*****************************************************************************
#;
#; Copyright (C) 2008-2024 Advanced Micro Devices, Inc. All rights reserved.
#;
#;******************************************************************************
#For EDKII use Only
[Defines]
  INF_VERSION                    = 0x00010006
  BASE_NAME                      = CcxSetMmioCfgBasePeiLib
  FILE_GUID                      = 354CFFF8-131F-471B-8F35-D32070B2DBB8
  MODULE_TYPE                    = BASE
  VERSION_STRING                 = 1.0
  LIBRARY_CLASS                  = CcxSetMmioCfgBaseLib | PEIM

[Sources.common]
  CcxSetMmioCfgBaseLib.c

[Packages]
  MdePkg/MdePkg.dec
  AgesaModulePkg/AgesaCommonModulePkg.dec
  AgesaModulePkg/AgesaModuleCcxPkg.dec

[LibraryClasses]
  BaseLib
  CcxRolesLib

[Guids]

[Protocols]

[Ppis]

[Pcd]
  gEfiMdePkgTokenSpaceGuid.PcdPciExpressBaseAddress

[Depex]



