/*****************************************************************************
 * Copyright (C) 2008-2024 Advanced Micro Devices, Inc. All rights reserved.
 *
*****************************************************************************
*/
/* $NoKeywords:$ */
/**
 * @file
 *
 * FCH SMM Driver
 *
 *
 * @xrefitem bom "File Content Label" "Release Content"
 * @e project:      AGESA
 * @e sub-project   FCH SMM Driver
 * @e \$Revision: 309090 $   @e \$Date: 2014-12-09 10:28:05 -0800 (Tue, 09 Dec 2014) $
 *
 */

#ifndef _FCH_SMM_I2C_MASTER_H_
#define _FCH_SMM_I2C_MASTER_H_
#include <Protocol/FchSmmI2cMasterProtocol.h>

#pragma pack (push, 1)
//
// Module data structure
//
/// Private data and access defines
typedef struct _I2C_MASTER_SMM_PRIVATE {
  UINTN                            Signature;                  ///< Signature
  FCH_SMM_I2C_MASTER_PROTOCOL      I2cSmmController;           ///< Protocol data
  UINT32                           I2cBaseAddress;             // I2cBaseAddress
  UINT32                           I2cSdaHoldTime;             // I2cSdaHoldTime
  UINT32                           I2cBusFrequency;            // I2cBusFrequency
  UINT32                           RxFifoDepth;                // RxFifoDepth
  UINT32                           TxFifoDepth;                // TxFifoDepth
} I2C_MASTER_SMM_PRIVATE;

#define I2C_MASTER_SMM_PRIVATE_DATA_SIGNATURE   SIGNATURE_32 ('I', '2', 'C', 'M')

#define I2C_MASTER_SMM_PRIVATE_INSTANCE_FROM_THIS(a) \
  CR (a, I2C_MASTER_SMM_PRIVATE, I2cSmmController, I2C_MASTER_SMM_PRIVATE_DATA_SIGNATURE)

#pragma pack (pop)


/**
   *  @brief Set Bus Frequency
   *  @param[in] *This - EFI_I2C_MASTER_PROTOCOL table
   *  @param[in] *BusClockHertz - pointer to the BUS Clock Hertz
   *  @returns EFI_STATUS
   *  @retval EFI_SUCCESS : Set Bus Frequency successfully
   *          EFI_ERROR   : Failed (see error for more details)
   */
EFI_STATUS
EFIAPI
SetBusFrequency (
  IN  CONST FCH_SMM_I2C_MASTER_PROTOCOL   *This,
  IN  OUT UINTN                           *BusClockHertz
  );


/**
   *  @brief Start request
   *  @param[in] *This - EFI_I2C_MASTER_PROTOCOL table
   *  @param[in] SlaveAddress -
   *  @param[in] *RequestPacket - EFI_I2C_REQUEST_PACKET table
   *  @param[in] Event -
   *  @param[out] *I2cStatus -
   *  @returns EFI_STATUS
   *  @retval EFI_SUCCESS :
   */
EFI_STATUS
EFIAPI
StartRequest (
  IN  CONST FCH_SMM_I2C_MASTER_PROTOCOL   *This,
  IN  UINTN                               SlaveAddress,
  IN  EFI_I2C_REQUEST_PACKET              *RequestPacket
  );

#endif



