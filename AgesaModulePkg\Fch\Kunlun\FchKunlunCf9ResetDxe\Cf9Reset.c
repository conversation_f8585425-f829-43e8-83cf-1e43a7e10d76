/*****************************************************************************
 * Copyright (C) 2008-2024 Advanced Micro Devices, Inc. All rights reserved.
 *
*****************************************************************************
*/
/* $NoKeywords:$ */
/**
 * @file
 *
 * FCH DXE Driver
 *
 *
 * @xrefitem bom "File Content Label" "Release Content"
 * @e project:      AGESA
 * @e sub-project   FCH DXE Driver
 *
 */

#include "Cf9Reset.h"

#define FILECODE FCH_KUNLUN_FCHKUNLUNCF9RESETDXE_CF9RESET_FILECODE

extern EFI_GUID gEfiAmdAgesaSpecificWarmResetGuid;

//
// The handle onto which the Reset Architectural Protocol is installed
//
EFI_HANDLE  mResetHandle = NULL;


UINT8
ReadPmio8 (
  IN       UINT8        Index
  )
{
  UINT8 Value8;

  Value8 = 0;

  LibAmdMemRead (
    AccessWidth8,
    (UINT64)(ACPI_MMIO_BASE + PMIO_BASE + (UINT64)Index),
    &Value8,
    NULL
    );

  return Value8;
}


VOID
WritePmio8 (
  IN       UINT8        Index,
  IN       UINT8        Value
)
{
  LibAmdMemWrite (
    AccessWidth8,
    (UINT64)(ACPI_MMIO_BASE + PMIO_BASE + (UINT64)Index),
    &Value,
    NULL
    );
}


UINT16
ReadPmio16 (
  IN       UINT8        Index
  )
{
  UINT16 Value16;

  Value16 = 0;

  LibAmdMemRead (
    AccessWidth16,
    (UINT64)(ACPI_MMIO_BASE + PMIO_BASE + (UINT64)Index),
    &Value16,
    NULL
    );

  return Value16;
}


VOID
WritePmio16 (
  IN       UINT8        Index,
  IN       UINT16       Value
  )
{
  LibAmdMemWrite (
    AccessWidth16,
    (UINT64)(ACPI_MMIO_BASE + PMIO_BASE + (UINT64)Index),
    &Value,
    NULL
    );
}


VOID SpecificWarmResetSystem (
  IN EFI_RESET_TYPE ResetType
)
{
  UINT8 InitialData;
  UINT8 OutputData;
  UINT8 PwrRsrCfg;

  IDS_HDT_CONSOLE (FCH_TRACE, "[SpecificWarmResetSystem] ResetSystem invoked:  ResetType = %d\n", ResetType);

  InitialData = HARDSTARTSTATE;
  OutputData = HARDRESET;
  PwrRsrCfg   = 0;

  PwrRsrCfg = ReadPmio8 (0x10);
  PwrRsrCfg = PwrRsrCfg & 0xFD; //clear ToggleAllPwrGoodOnCf9
  WritePmio8 (0x10, PwrRsrCfg);

  IoWrite8 (FCH_IOMAP_REGCF9, InitialData);
  IoWrite8 (FCH_IOMAP_REGCF9, OutputData);
  //
  // Given we should have reset getting here would be bad
  //
  FCH_DEADLOOP ();
}


/**
 * @brief Resets the entire platform.
 *
 * @param[in] ResetType warm or cold
 * @param[in] ResetStatus possible cause of reset
 * @param[in] DataSize Size of ResetData in bytes
 * @param[in] ResetData Optional Unicode string
 *
 * @returns VOID
 */
VOID
EFIAPI
Cf9ResetSystem (
  IN EFI_RESET_TYPE   ResetType,
  IN EFI_STATUS       ResetStatus,
  IN UINTN            DataSize,
  IN VOID             *ResetData OPTIONAL
  )
{
  UINT8      InitialData;
  UINT8      OutputData;
  UINT8      PwrRsrCfg;
  UINT16     AcpiGpeBase;
  UINT16     AcpiPm1StsBase;
  UINT16     AcpiPm1CntBase;
  UINT32     Gpe0Enable;
  UINT16     PmCntl;
  UINT16     PwrSts;
  BOOLEAN    IsFullHardReset = FALSE;
  UINTN      ResetDataStringSize;
  EFI_GUID   *ResetTypeGuid;

  IDS_HDT_CONSOLE (FCH_TRACE, "[FchCf9Reset] ResetSystem invoked:  ResetType = %d\n", ResetType);
  switch (ResetType) {
  //
  // For update resets, the reset data is a null-terminated string followed
  // by a VOID * to the capsule descriptors. Get the pointer and set the
  // capsule variable before we do a warm reset. Per the EFI 1.10 spec, the
  // reset data is only valid if ResetStatus != EFI_SUCCESS.
  //
  case EfiResetWarm:
    InitialData = HARDSTARTSTATE;
    OutputData  = HARDRESET;
    PwrRsrCfg = 0;
    PwrRsrCfg = ReadPmio8 (0x10);
    PwrRsrCfg = PwrRsrCfg & 0xFD; //clear ToggleAllPwrGoodOnCf9
    WritePmio8 (0x10, PwrRsrCfg);
    break;

  case EfiResetCold:
    IsFullHardReset = PcdGetBool (PcdFchFullHardReset);
    if (IsFullHardReset){
      InitialData = FULLSTARTSTATE;
      OutputData  = FULLRESET;
    }else{
      InitialData = HARDSTARTSTATE;
      OutputData  = HARDRESET;
    }
    PwrRsrCfg = ReadPmio8 (0x10);
    PwrRsrCfg = PwrRsrCfg | BIT1; //set ToggleAllPwrGoodOnCf9
    WritePmio8 (0x10, PwrRsrCfg);
    break;

  case EfiResetPlatformSpecific:
    InitialData = HARDSTARTSTATE;
    OutputData  = HARDRESET;
    if ((DataSize >= sizeof (EFI_GUID)) && (ResetData != NULL)){
      ResetDataStringSize = StrnSizeS (ResetData, (DataSize / sizeof (CHAR16)));
      if ((ResetDataStringSize < DataSize) && ((DataSize - ResetDataStringSize) >= sizeof (EFI_GUID))){
        ResetTypeGuid = (EFI_GUID *)((UINT8 *)ResetData + ResetDataStringSize);
        if (CompareGuid (&gEfiAmdAgesaSpecificWarmResetGuid, ResetTypeGuid)){
          SpecificWarmResetSystem (EfiResetWarm);
        }
      }
    }
    break;

  case EfiResetShutdown:
    // Disable all GPE0 Event
    AcpiGpeBase = ReadPmio16 (FCH_PMIOA_REG68);
    AcpiGpeBase += 4; //Get enable base
    Gpe0Enable  = 0;
    IoWrite32 (AcpiGpeBase, Gpe0Enable);

    // Clear Power Button status.
    AcpiPm1StsBase = ReadPmio16 (FCH_PMIOA_REG60);
    PwrSts  = BIT8 | BIT15; //Clear WakeStatus with PwrBtnStatus
    IoWrite16 (AcpiPm1StsBase, PwrSts);

    // Transform system into S5 sleep state
    AcpiPm1CntBase = ReadPmio16 (FCH_PMIOA_REG62);
    PmCntl  = IoRead16 (AcpiPm1CntBase);
    PmCntl  = (PmCntl & ~SLP_TYPE) | SUS_S5 | SLP_EN;
    IoWrite16 (AcpiPm1CntBase, PmCntl);
    return ;

  default:
    return ;
  }

  IoWrite8 (FCH_IOMAP_REGCF9, InitialData);
  IoWrite8 (FCH_IOMAP_REGCF9, OutputData);

  //
  // Given we should have reset getting here would be bad
  //
  FCH_DEADLOOP ();
}


/**
 * @brief Initialize Cf9 Reset.
 *
 * @param[in] ImageHandle EFI Image Handle for the DXE driver
 * @param[in] SystemTable Pointer to the EFI system table
 *
 * @returns EFI_STATUS
 * @retval EFI_SUCCESS   Procedure successfully
 * @retval EFI_ERROR     Procedure failed (see error for more details)
 */
EFI_STATUS
EFIAPI
InitializeCf9Reset (
  IN EFI_HANDLE        ImageHandle,
  IN EFI_SYSTEM_TABLE  *SystemTable
  )
{
  EFI_STATUS  Status;
  UINT32      ProtocolPointer;

  //
  // Check if the Reset Architectural Protocol is already installed in the system
  //
  Status = gBS->LocateProtocol (&gEfiResetArchProtocolGuid, NULL, (VOID **) &ProtocolPointer);
  if ( Status != EFI_NOT_FOUND) {
    //Already installed
    return EFI_UNSUPPORTED;
  }
  //
  // Hook the runtime service table
  //
  SystemTable->RuntimeServices->ResetSystem = Cf9ResetSystem;

  //
  // Now install the Reset RT AP on a new handle
  //
  Status = gBS->InstallMultipleProtocolInterfaces (
                  &mResetHandle,
                  &gEfiResetArchProtocolGuid,
                  NULL,
                  NULL
                  );
  ASSERT_EFI_ERROR (Status);

  return Status;
}


