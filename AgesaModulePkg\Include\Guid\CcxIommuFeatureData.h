/*****************************************************************************
 *
 * Copyright (C) 2008-2024 Advanced Micro Devices, Inc. All rights reserved.
 *
 *******************************************************************************
 */

#ifndef _AMD_CCX_IOMMU_FEATURE_DATA_H_
#define _AMD_CCX_IOMMU_FEATURE_DATA_H_

#define AMD_CCX_IOMMU_FEATURE_DATA_GUID \
  { \
    0x55C08824, 0x3018, 0x4613, {0xB3, 0xEB, 0x83, 0x62, 0x69, 0x41, 0x79, 0x55} \
  }

extern EFI_GUID gAmdCcxIommuFeatureDataGuid;

typedef struct {
  UINT32                Iommu_Sup:1;
  UINT32                XT_Sup:1;
  UINT32                UNUSED:30;
} CCX_IOMMU_FEATURE_INFO;

#endif


