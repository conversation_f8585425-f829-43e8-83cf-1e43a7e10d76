#;*****************************************************************************
#;
#; Copyright (C) 2023-2024 Advanced Micro Devices, Inc. All rights reserved.
#;
#;******************************************************************************
[Defines]
  INF_VERSION                    = 0x00010006
  BASE_NAME                      = AmdPspApobLib
  FILE_GUID                      = D79D8DAE-F309-404E-9F12-3CC2623420F8
  MODULE_TYPE                    = BASE
  VERSION_STRING                 = 1.0
  LIBRARY_CLASS                  = AmdPspSfsLib

[Sources.common]
  AmdPspSfsLib.c

[Packages]
  MdePkg/MdePkg.dec
  AgesaPkg/AgesaPkg.dec
  AgesaModulePkg/AgesaCommonModulePkg.dec
  AgesaModulePkg/AgesaModulePspPkg.dec

[LibraryClasses]
  BaseMemoryLib
  HobLib
  PcdLib
  MemoryAllocationLib
  AmdDirectoryBaseLib
  AmdSocBaseLib

[Guids]

[Protocols]
  gAmdApcbDxeServiceProtocolGuid
[Ppis]

[Pcd]

