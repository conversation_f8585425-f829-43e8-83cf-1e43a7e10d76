/*****************************************************************************
 * Copyright (C) 2008-2024 Advanced Micro Devices, Inc. All rights reserved.
 *
*****************************************************************************
*/
/* $NoKeywords:$ */
/**
 * @file
 *
 * Config Fch HwAcpi controller
 *
 * Init HwAcpi Controller features.
 *
 * @xrefitem bom "File Content Label" "Release Content"
 * @e project:     AGESA
 * @e sub-project: FCH
 * @e \$Revision: 309083 $   @e \$Date: 2014-12-09 09:28:24 -0800 (Tue, 09 Dec 2014) $
 *
 */
#include "FchPlatform.h"
#include "Filecode.h"
#include "FchAoacLinkListData.h"
#define FILECODE FCH_KUNLUN_FCHKUNLUNCORE_KUNLUN_KLHWACPI_KLHWACPILATE_FILECODE

#define AMD_CPUID_APICID_LPC_BID    0x00000001ul  // Local APIC ID, Logical Processor Count, Brand ID

extern VOID  HpetInit                          (IN VOID  *FchDataPtr);
extern VOID  GcpuRelatedSetting              (IN VOID  *FchDataPtr);
extern VOID  StressResetModeLate               (IN VOID  *FchDataPtr);
VOID
FchInitLateHwAcpiP (
  IN  VOID     *FchDataPtr
  );

/**
 * FchInitLateHwAcpi - Prepare HwAcpi controller to boot to OS.
 *
 * @param[in] FchDataPtr Fch configuration structure pointer.
 *
 */
VOID
FchInitLateHwAcpi (
  IN  VOID     *FchDataPtr
  )
{
  //UINT32                 RegEax;
  FCH_DATA_BLOCK         *LocalCfgPtr;
//  AMD_CONFIG_PARAMS      *StdHeader;

  LocalCfgPtr = (FCH_DATA_BLOCK *) FchDataPtr;
//  StdHeader = LocalCfgPtr->StdHeader;

  AGESA_TESTPOINT (TpFchInitLateHwAcpi, NULL);

  GcpuRelatedSetting (LocalCfgPtr);

  // Mt C1E Enable
  //MtC1eEnable (LocalCfgPtr);           //Kunlun remove HW register

  if (LocalCfgPtr->Gpp.SerialDebugBusEnable == TRUE ) {
    RwMem (ACPI_MMIO_BASE + SERIAL_DEBUG_BASE +  FCH_SDB_REG00, AccessWidth8, 0xFF, 0x05);
  }

  StressResetModeLate (LocalCfgPtr);
  SbSleepTrapControl (TRUE);

  //huashan remove WA for ZP A0
  // PLAT-12314 APIC INIT may hit PIC/SMI/NMI/DMA
  //AsmCpuid (0x80000001, &RegEax, NULL, NULL, NULL);
  //if ((RegEax & 0xF0) == 0) {
  //  RwPmio (FCH_PMIOA_REG08, AccessWidth8, 0x9F, 0x00, StdHeader);
  //}

  FchInternalDeviceIrqInit (FchDataPtr);

  if (LocalCfgPtr->Misc.S3Resume == 0) {
    FchEventInitUsbGpe (FchDataPtr);
    FchEventInitLate (FchDataPtr);
  }

  FchI2cUartInitLate (FchDataPtr);

  FchCppcSciInit(FchDataPtr);

  FchI2cReleaseControlLate(FchDataPtr);
}
VOID
FchInitLateHwAcpiP (
  IN  VOID     *FchDataPtr
  )
{
  UINT16                  FchSmiData;
  UINT16                  SwSmiCmdAddress;
  FCH_DATA_BLOCK         *LocalCfgPtr;

  LocalCfgPtr = (FCH_DATA_BLOCK *) FchDataPtr;

  FchSmiData = (FCH_SMI_DATA_POST_LATE << 8) | FCH_AOAC_SW_VALUE;
  SwSmiCmdAddress = ACPIMMIO16 (ACPI_MMIO_BASE + PMIO_BASE + FCH_PMIOA_REG6A);
  LibAmdIoWrite (AccessWidth16, SwSmiCmdAddress, &FchSmiData, LocalCfgPtr->StdHeader);
}




