/*****************************************************************************
 *
 * Copyright (C) 2008-2024 Advanced Micro Devices, Inc. All rights reserved.
 *
 *******************************************************************************
 **/

#include <Uefi.h>
#include <Pi/PiMultiPhase.h>
#include <Library/HobLib.h>
#include <Library/ApobApcbLib.h>
// #include <Filecode.h>

#define FILECODE LIBRARY_APOBAPCBLIB_APOBAPCBLIB_FILECODE

/**
 * @brief Read the updated value of APCB token
 *
 * @param[in] Token      APCB Token UID
 * @param[out] Value     Updated value for APCB token
 *
 * @retval    <PERSON>FI_SUCCESS             Token read successfully
 * @retval    <PERSON>_<PERSON>_<PERSON>OUN<PERSON>           Unable to find the updated APCB token
 * @retval    <PERSON>FI_INVALID_PARAMETER   The Value pointer is null
 */
EFI_STATUS
EFIAPI
ApobReadApcbUpdate (
  IN       UINT32 Token,
  OUT      UINT32 *Value
  )
{
  VOID                *GuidHob;
  APOB_APCB_TOKEN_HOB *ApobApcbTokenHob;
  UINT32              Index;

  if (!Value) {
    return EFI_INVALID_PARAMETER;
  }

  GuidHob = GetFirstGuidHob (&gAmdApobApcbHobGuid);
  if (GuidHob) {
    ApobApcbTokenHob = GET_GUID_HOB_DATA (GuidHob);

    for (Index = 0; Index < ApobApcbTokenHob->NumberOfItems; Index++) {
      if (ApobApcbTokenHob->Items[Index].Token == Token) {
        *Value = ApobApcbTokenHob->Items[Index].Value;
        return EFI_SUCCESS;
      }
    }
  }

  return EFI_NOT_FOUND;
}

/**
 * @brief Read the APOB APCB values with given instance ID
 *
 * @param[in]  SocketId     The socket ID to reading APOB
 * @param[in]  DieId        The die ID to reading APOB
 * @param[in]  Token        Token ID
 * @param[out] Value        The value
 * @return EFI_STATUS       The return status
 */
EFI_STATUS
EFIAPI
ApobReadApcbUpdateEx (
  IN       UINT32 SocketId,
  IN       UINT32 DieId,
  IN       UINT32 Token,
  OUT      UINT32 *Value
  )
{
  VOID                *GuidHob;
  APOB_APCB_TOKEN_HOB *ApobApcbTokenHob;
  UINT32              Index;

  if (!Value) {
    return EFI_INVALID_PARAMETER;
  }

  for(GuidHob = GetFirstGuidHob (&gAmdApobApcbHobGuid); GuidHob != NULL; GuidHob = GetNextGuidHob (&gAmdApobApcbHobGuid, GET_NEXT_HOB (GuidHob))) {
    ApobApcbTokenHob = GET_GUID_HOB_DATA (GuidHob);
    if ((SocketId != ApobApcbTokenHob->SocketId) || (DieId != ApobApcbTokenHob->DieId)) {
      continue;
    }

    for (Index = 0; Index < ApobApcbTokenHob->NumberOfItems; Index++) {
      if (ApobApcbTokenHob->Items[Index].Token == Token) {
        *Value = ApobApcbTokenHob->Items[Index].Value;
        return EFI_SUCCESS;
      }
    }
  }

  return EFI_NOT_FOUND;
}