/*
 ******************************************************************************
 *
 * Copyright (C) 2008-2024 Advanced Micro Devices, Inc. All rights reserved.
 *
 *******************************************************************************
 **/

#ifndef _AMD_DIRECTORY_BASELIB_H_
#define _AMD_DIRECTORY_BASELIB_H_

#include <AMD.h>
#include <AmdPspDirectory.h>

#define IS_ADDRESS_MODE_1(a) (RShiftU64 ((a), 62) == 1 ? TRUE : FALSE)  //relative to BIOS image base 0
#define IS_ADDRESS_MODE_2(a) (RShiftU64 ((a), 62) == 2 ? TRUE : FALSE)  //relative to current directory header
#define IS_ADDRESS_MODE_3(a) (RShiftU64 ((a), 62) == 3 ? TRUE : FALSE)  //relative to active image slot address (as of now, active image slot address is equal to PSP L2 base address)
#define IS_SPI_OFFSET(a) (((a) & 0xFF000000) != 0xFF000000 ? TRUE : FALSE)

#define MaxPspEntryNumber   64
#define MaxPspDirSize       sizeof(PSP_DIRECTORY_HEADER) + (sizeof(PSP_DIRECTORY_ENTRY) * MaxPspEntryNumber)

#define MaxBiosEntryNumber  52
#define MaxBiosDirSize      sizeof(PSP_DIRECTORY_HEADER) + (sizeof(BIOS_DIRECTORY_ENTRY) * MaxBiosEntryNumber)

#define ALIGNMENT_4K                    BASE_4KB
#define ALIGN_CHECK(addr, alignment)    ((((UINTN)(addr)) & ((alignment) - 1)) == 0)
#define ALIGN_4K_CHECK(addr)            ALIGN_CHECK((addr), ALIGNMENT_4K)

#define IS_VALID_ADDR32(addr)           (((UINT32)(addr) != 0) && (UINT32)(addr) != 0xFFFFFFFF)
#define MaxImageSlotInfoSize            sizeof(IMAGE_SLOT_INFO)

#pragma pack (push, 1)

/// Directory type
typedef enum _DIRECTORY_TYPE {
  DIR_TYPE_PSP_LV2               = 0,         ///< Level 2 PSP DIR
  DIR_TYPE_BIOS_LV2              = 1,         ///< Level 2 BIOS DIR
} DIRECTORY_TYPE;

/**
 * @brief PSP/BIOS entry region with start address and size
 *
 */
typedef struct {
  UINT64 Address;       ///< Entry Region Address
  UINT32 Size;          ///< Entry Region Size
} ENTRY_REGION;

/// RECOVERY_REASON_VERSION
typedef enum {
  RECOVERY_REASON_VERSION_IGNORE = 0xFFFFFFFFul, //before RN
  RECOVERY_REASON_VERSION_1 = 1,   //RN, CZN
  RECOVERY_REASON_VERSION_2 = 2,   //Starting from VN
} RECOVERY_REASON_VERSION;

/**
 * @brief directory level for recovery reason, please refer to RECOVERY_REASON_V2
 *
 */
typedef enum {
  DIRECTORY_LEVEL_PSP_L1_ENTRY = 1,       ///< b'01 - The entry is from PSP directory L1
  DIRECTORY_LEVEL_PSP_L2_ENTRY = 2,       ///< b'10 - The entry is from PSP directory L2
  DIRECTORY_LEVEL_BIOS_L2_ENTRY = 3,      ///< b'11 - The entry is from BIOS directory L2
  DIRECTORY_LEVEL_PSP_L1_HEADER = 4,      ///< b'100 - PSP L1 directory header
  DIRECTORY_LEVEL_PSP_L2_HEADER = 5,      ///< b'101 - PSP L2 directory header
  DIRECTORY_LEVEL_BIOS_L2_HEADER = 6,     ///< b'110 - BIOS directory L2 header
  DIRECTORY_LEVEL_IMAGE_SLOT_HEADER = 7,  ///< b'111 - Image Slot Header
} RECOVERY_REASON_DIRECTORY_LEVEL;

/// PSP Recovery Reason V1
typedef struct {
  UINT32 EntryType: 16;          ///< [0:15], Entry type ID of the binary in PSP/BIOS entry whose corruption caused recovery
  UINT32 DirectoryLevel: 2;      ///< [16:17],b'01 - The entry is from PSP directory L1
                                 ///          b'10 - The entry is from PSP directory L2
                                 ///          b'11 - The entry is from BIOS directory L2
  UINT32 Instance: 4;            ///< [18:21],the instance number of the corrupted entry
  UINT32 PartitionNumber: 3;     ///< [22:24],Which partition this log is from
  UINT32 Reserved: 7;            ///< [25:31] Reserve for future use
} RECOVERY_REASON_V1;

/// PSP Recovery Reason V2
typedef struct {
  UINT32 EntryType: 8;           ///< [0:7],  Entry type ID of the binary in PSP/BIOS entry whose corruption caused recovery
  UINT32 Instance: 4;            ///< [8:11],the instance number of the corrupted entry
  UINT32 SubProgram: 4;          ///< [12:15], SubProgram
  UINT32 DirectoryLevel: 4;      ///< [16:19], refer to RECOVERY_REASON_DIRECTORY_LEVEL
                                 ///          b'01 - The entry is from PSP directory L1
                                 ///          b'10 - The entry is from PSP directory L2
                                 ///          b'11 - The entry is from BIOS directory L2
                                 ///          b'100 - PSP L1 directory header
                                 ///          b'101 - PSP L2 directory header
                                 ///          b'110 - BIOS directory L2 header
                                 ///          b'111 - Image Slot Header
  UINT32 Reserved: 2;            ///< [20:21], Reserved
  UINT32 PartitionNumber: 3;     ///< [22:24],Which partition this log is from
  UINT32 Reserved2: 7;           ///< [25:31] Reserve for future use
} RECOVERY_REASON_V2;

/// PSP_DIRECTORY_ID scheme selection
typedef enum {
  PSP_DIRECTORY_ID_USE_ENTRY_TYPE = 1, // Used in most client, server program
  PSP_DIRECTORY_ID_USE_FW_ID      = 2, // Only used in very few programs, e.g. MI300
} PSP_DIRECTORY_ID_SCHEME;

/// @cond !RS
#define ZP_PSP_CHIP_ID        0xBC090000  ///< ZP Chip ID in combo structure
#define RV_PSP_CHIP_ID        0xBC0A0000  ///< RV Chip ID in combo structure
#define RV2_PSP_CHIP_ID       0xBC0A0100  ///< RV2 Chip ID in combo structure
#define SSP_PSP_CHIP_ID       0xBC0B0000  ///< SSP Chip ID in combo structure
#define MTS_PSP_CHIP_ID       0xBC0B0500  ///< MTS Chip ID in combo structure
#define RN_PSP_CHIP_ID        0xBC0C0000  ///< RN Chip ID in combo structure
#define VMR_PSP_CHIP_ID       0xBC0B0500  ///< VMR Chip ID in combo structure
#define GN_PSP_CHIP_ID        0xBC0B0D00  ///< GN Chip ID in combo structure
#define CZN_PSP_CHIP_ID       0xBC0C0140  ///< CZN Chip ID in combo structure
#define BA_PSP_CHIP_ID        0xBC0B0F00  ///< BA Chip ID in combo structure
#define MR_PSP_CHIP_ID        0xBC0B0800  ///< MR Chip ID in combo structure
#define VN_PSP_CHIP_ID        0xBC0B0800  ///< VN Chip ID in combo structure
#define RMB_PSP_CHIP_ID       0xBC0D0200  ///< RMB chip ID
/// @endcond

/// @cond RS
#define RS_PSP_CHIP_ID        0XBC0D0111  ///< Stone chip id
/// @endcond

/// @cond !RS
#define RPL_PSP_CHIP_ID       0xBC0D0300  ///< RPL chip ID
#define GNR_PSP_CHIP_ID       0xBC0D0300  ///< GNR chip ID
#define PHX_PSP_CHIP_ID       0xBC0D0400  ///< PHX chip ID
#define PHX2_PSP_CHIP_ID      0xBC0D0B00  ///< PHX 2 chip ID
#define HPT2_PSP_CHIP_ID      0xBC0D0B00  ///< HPT 2 chip ID
#define MDN_PSP_CHIP_ID       0xBC0D0900  ///< MDN chip ID
#define MI3_PSP_CHIP_ID       0xBC0D0600  ///< MI300 chip ID
#define BRH_PSP_CHIP_ID       0xBC0E0000  ///< Turin chip ID
#define BRH_A0_PSP_CHIP_ID    0xBC0E0000  ///< Turin chip ID
#define BRH_B0_PSP_CHIP_ID    0xBC0E0C00  ///< Turin chip ID
#define BRH_C0_PSP_CHIP_ID    0xBC0E0D00  ///< Turin chip ID
#define BRH_C1_PSP_CHIP_ID    0xBC0E1100  ///< Turin chip ID
#define STX1_PSP_CHIP_ID      0xBC0E0200  ///< Strix 1 chip ID
#define STXH_IOD_PSP_CHIP_ID  0xBC0E0900  ///< Strix Halo IOD chip ID
#define KRK_CPD_PSP_CHIP_ID   0xBC0E0B00  ///< Krackan CPD chip ID
/// @endcond

#pragma pack (pop)

/**
 * @brief This function is to calculate the crc checksum
 *
 * @param[in,out]   data    Pointer to content
 * @param[in]       words   Length of content
 *
 * @retval CRC value
 */
UINT32
Fletcher32 (
  IN OUT   VOID  *data,
  IN       UINTN   words
  );

/**
 * @brief Check if current system is using Psp Fw Id or not
 *
 * @return BOOLEAN TRUE: Current system using FW ID FALSE: Not used
 */
BOOLEAN
UseFwId (
  VOID
  );

/**
 *  @brief Map SPI data to the Buffer
 *  @details If SPI offset is specified, call FchSpiRomRead to get data, otherwise, access directly
 *
 *  @param[in]     Address    Where to get the data, it may be a SPI offset or physical address
 *  @param[in,out] Buffer     Where the data save to, the buffer must be allocated by the caller
 *  @param[in]     Size       How big the data is
 *
 *  @retval TRUE   Success to get the content from Address
 *  @retval FALSE  Fail to get the content from Address
 *
 **/
BOOLEAN
MapSpiDataToBuffer (
  IN       UINT32                      Address,
  IN OUT   VOID                        *Buffer,
  IN       UINT32                      Size
  );

/**
 * @brief This function is to validate PSP directory address on signature and crc checksum
 *
 * @param[in]  PspDir         Pointer to PSP directory base
 * @param[in]  Signature      Value of the signature,
 * @param[in]  IsPSPL1Dir     Is PSP L1 directory or not
 *
 * @retval TRUE            the PSP directory base address is valid.
 * @retval FALSE           the PSP directory base address is not valid.
**/
BOOLEAN
ValidatePspDir (
  IN       PSP_DIRECTORY     *PspDir,
  IN       UINT32            Signature,
  IN       BOOLEAN           IsPSPL1Dir
  );

/**
 * @brief This function is to validate BIOS directory address on signature and crc checksum
 *
 * @param[in]  BiosDir      Pointer to BIOS directory base
 * @param[in]  Signature    Value of the signature,
 *
 * @retval TRUE            The BIOS directory base address is valid.
 * @retval FALSE           The BIOS directory base address is not valid.
**/
BOOLEAN
ValidateBiosDir (
  IN       BIOS_DIRECTORY     *BiosDir,
  IN       UINT32     Signature
  );

/**
 * @brief Get Efs Table
 *
 * @param[in,out]   Efs Pointer of Efs Table
 *
 * @return  TRUE -  Efs Table is find
 * @return  FALSE - Efs Table is not find
 */
BOOLEAN
GetEfs (
  IN OUT   FIRMWARE_ENTRY_TABLEV2     *Efs
  );

/**
 * @brief translate entry offset to correct location based on address mode
 *
 * @param[in] EntryLocation     The location of the entry before translation
 * @param[in] DirectoryHdrAddr  Directory header address
 * @param[in] ImageSlotAddr     Image slot address if applicable, if no image slot, leave it as 0
 *
 * @return UINT64           return translated entry location
 */
UINT64
TranslateEntryLocation (
  IN       UINT64                      EntryLocation,
  IN       UINT64                      DirectoryHdrAddr,
  IN       UINT32                      ImageSlotAddr
  );

/**
 * @brief This function is to get the PSP level 2 directory buffer.
 *
 * @param[in]       PspLevel2BaseAddress  Psp Level2 Base Address
 * @param[in,out]   PspLv2Dir             Pointer to PSP level 2 directory base
 *
 * @retval TRUE     Successfully get the valid PSP level 2 directory.
 * @retval FALSE    Valid PSP level 2 directory is not found.
 */
BOOLEAN
GetPspLv2DirBaseV2 (
  IN       UINT64             PspLevel2BaseAddress,
  IN OUT   PSP_DIRECTORY     **PspLv2Dir
  );

/**
 * @brief This function is to search EFS structure and get the valid PSP L1 directory base address.
 *
 * @param[in,out]  Pointer to PSP directory base
 *
 * @retval TRUE            Successfully get the valid PSP directory base address.
 * @retval FALSE           Valid PSP directory is not found.
**/
BOOLEAN
GetPspDirBaseV2 (
  IN OUT   PSP_DIRECTORY     **PspDir
  );

/**
 * @brief This function is to get the BIOS level 2 directory buffer.
 *
 * @param[in]       BiosLevel2BaseAddress  Bios Level2 Base Address
 * @param[in,out]   BiosLv2Dir             Pointer to BIOS level 2 directory base
 *
 * @retval TRUE     Successfully get the valid BIOS level 2 directory.
 * @retval FALSE    Valid BIOS level 2 directory is not found.
 */
BOOLEAN
GetBiosLv2DirBaseV2 (
  IN       UINT64             BiosLevel2BaseAddress,
  IN OUT   BIOS_DIRECTORY     **BiosLv2Dir
  );

/**
 * @brief This function is to get the BIOS Level 1 directory.
 *
 * @param[in,out]  Pointer to BIOS directory base
 *
 * @retval TRUE            Successfully get the valid BIOS directory base address.
 * @retval FALSE           Valid BIOS directory is not found.
**/
BOOLEAN
GetBiosDirBaseV2 (
  IN OUT   BIOS_DIRECTORY     **BiosDir
  );

/**
 * @brief This function is to get the PSP entry information for given PSP entry type
 *
 * @param[in]        EntryType      Value of given PSP entry type
 * @param[in,out]    EntryAddress   Pointer to PSP entry address
 * @param[in,out]    EntrySize      Pointer to PSP entry size
 *
 * @retval TRUE      The given entry type is found
 * @retval FALSE     The given entry type is not found
 */
BOOLEAN
EFIAPI
PSPEntryInfoV2 (
  IN       UINT32                      EntryType,
  IN OUT   UINT64                      *EntryAddress,
  IN OUT   UINT32                      *EntrySize
  );

#define INSTANCE_IGNORED 0xFF
#define SUBPROGRAM_IGNORED 0xFF

/**
 * @brief Get BIOS Directory Entry 's properties by EntryType and EntryInstance
 * this function will ignore SubProgram, if you care about SubProgram, call BIOSEntryInfoByAttributes instead.
 *
 * @param[in]       EntryType       BIOS Directory Entry type
 * @param[in]       EntryInstance   If input with INSTANCE_IGNORED, will return 1st Entry type matched
 *                                  If input with Non INSTANCE_IGNORED, will return the entry which both Type & Instance matched
 * @param[in,out]   TypeAttrib      TypeAttrib of entry
 * @param[in,out]   EntryAddress    Address of entry
 * @param[in,out]   EntrySize       Size of entry
 * @param[in,out]   EntryDest       Destination of entry
 *
 * @retval TRUE   Success to get the Entry 's properties
 * @retval FALSE  Fail to get the Entry 's properties
 */
BOOLEAN
BIOSEntryInfo (
  IN       UINT8                       EntryType,
  IN       UINT8                       EntryInstance,
  IN OUT   TYPE_ATTRIB                 *TypeAttrib,
  IN OUT   UINT64                      *EntryAddress,
  IN OUT   UINT32                      *EntrySize,
  IN OUT   UINT64                      *EntryDest
  );

/**
 * @brief Get the PSP level 2 Base Address
 *
 * @param[in,out]  PspLv2BaseAddress  The pointer to save the PSP level 2 base address
 *
 * @return BOOLEAN           return TRUE if correct address found, otherwise, return FALSE
 */
BOOLEAN
GetPspLv2BaseAddr (
  IN OUT UINT64                      *PspLv2BaseAddress
  );

/**
 * @brief Get the Base Addresses of both PSP level 2 and BIOS level 2
 *
 * @param[in,out]  PspLv2BaseAddress   The pointer to save the PSP level 2 base address
 * @param[in,out]  BiosLv2BaseAddress  The pointer to save the BIOS level 2 base address
 *
 * @return BOOLEAN            return TRUE if correct addresses found, otherwise, return FALSE
 */
BOOLEAN
GetPspBiosLv2BaseAddr (
  IN OUT UINT64                      *PspLv2BaseAddress,
  IN OUT UINT64                      *BiosLv2BaseAddress
  );

/**
 *
 *  @brief Get BIOS Directory Entry 's properties by 3 Attributes: EntryType, EntryInstance, SubProgram.
 *
 *  @param[in]      EntryType        BIOS Directory Entry type
 *  @param[in]      EntryInstance    If input with INSTANCE_IGNORED, will return 1st Entry type matched
 *                                   If input with Non INSTANCE_IGNORED, will return the entry which both Type & Instance matched
 *  @param[in]      SubProgram       If input with SUBPROGRAM_IGNORED, will return 1st Entry type & Instance matched
 *                                   If input with Non SUBPROGRAM_IGNORED, will return the entry which all Type & Instance & SubProgram matched
 *  @param[in,out]  TypeAttrib       TypeAttrib of entry
 *  @param[in,out]  EntryAddress     Address of entry
 *  @param[in,out]  EntrySize        Size of entry
 *  @param[in,out]  EntryDest        Destination of entry
 *
 *  @retval TRUE   Success to get the Entry 's properties
 *  @retval FALSE  Fail to get the Entry 's properties
 *
 */
BOOLEAN
BIOSEntryInfoByAttributes (
  IN       UINT8                       EntryType,
  IN       UINT8                       EntryInstance,
  IN       UINT8                       SubProgram,
  IN OUT   TYPE_ATTRIB                 *TypeAttrib,
  IN OUT   UINT64                      *EntryAddress,
  IN OUT   UINT32                      *EntrySize,
  IN OUT   UINT64                      *EntryDest
  );

/**
 *  @brief Get PSP Directory Entry's properties from Level 2
 *
 *  @param[in]      EntryType             BIOS Directory Entry type
 *  @param[in]      PspLevel2BaseAddress  The PSP Level 2 Directory base address
 *  @param[in,out]  EntryAddress          Entry Address
 *  @param[in,out]  EntrySize             Size of entry
 *
 *  @retval TRUE   Success to get the Entry 's properties
 *  @retval FALSE  Fail to get the Entry 's properties
 *
 */
BOOLEAN
GetLevel2PSPEntryInfo (
  IN       UINT32                      EntryType,
  IN       UINT64                      PspLevel2BaseAddress,
  IN OUT   UINT64                      *EntryAddress,
  IN OUT   UINT32                      *EntrySize
  );

/**
 *
 *  @brief Get BIOS Directory Entry 's properties from Level 2
 *
 *  @param[in]     EntryType          BIOS Directory Entry type
 *  @param[in]     EntryInstance      If input with INSTANCE_IGNORED, will return 1st Entry type matched
 *                                    If input with Non INSTANCE_IGNORED, will return the entry which both Type & Instance matched
 *  @param[in]     SubProgram         If input with SUBPROGRAM_IGNORED, will return 1st Entry type & Instance matched
 *                                    If input with Non SUBPROGRAM_IGNORED, will return the entry which all Type & Instance & SubProgram matched
 *  @param[in]     DirectoryType      For programs with multiple slot layout(VN/MR/RMB), specify DIR_TYPE_PSP_LV2, it will find BIOS entry in PSP L2,
                                      For programs without multiple slot layout(RN/CZN/MTS), specify DIR_TYPE_BIOS_LV2, it will find BIOS entry in BIOS L2.
                                      Note that in VN/MR, BIOS L2 is part of PSP L2 directory
 *  @param[in]     Level2BaseAddress  if DirectoryType=DIR_TYPE_PSP_LV2, please specify PSP L2 base address(only for VN/MR and beyond),
 *                                    if DirectoryType=DIR_TYPE_BIOS_LV2, please specify BIOS L2 base address(for programs before VN, such as RN/CZN/MTS)
 *  @param[in,out]  TypeAttrib        TypeAttrib of entry
 *  @param[in,out]  EntryAddress      Address of entry
 *  @param[in,out]  EntrySize         Size of entry
 *  @param[in,out]  EntryDest         Destination of entry
 *
 *  @retval TRUE   Success to get the Entry 's properties
 *  @retval FALSE  Fail to get the Entry 's properties
 *
 **/
BOOLEAN
GetLevel2BIOSEntryInfo (
  IN       UINT32                      EntryType,
  IN       UINT8                       EntryInstance,
  IN       UINT8                       SubProgram,
  IN       DIRECTORY_TYPE              DirectoryType,
  IN       UINT64                      Level2BaseAddress,
  IN OUT   TYPE_ATTRIB                 *TypeAttrib,
  IN OUT   UINT64                      *EntryAddress,
  IN OUT   UINT32                      *EntrySize,
  IN OUT   UINT64                      *EntryDest
  );

/**
 * @brief This function checks if the PSP L1 is A/B recovery layout,
 * no matter legacy A/B recovery(RN) or multiple slots A/B recovery layout (VN & MR)
 *
 * @param[in]       PspL1Dir                  Pointer to PSP directory base
 * @param[in,out]   PspRegionAEntryAddress    Pointer to PSP region A base
 * @param[in,out]   PspRegionBEntryAddress    Pointer to PSP region B base
 *
 * @retval TRUE   PSP AB recovery is supported
 * @retval FALSE  PSP AB recovery is not supported
 */
BOOLEAN
IsABrecovery (
  IN         PSP_DIRECTORY               *PspL1Dir,
  IN OUT     UINT64                      *PspRegionAEntryAddress,
  IN OUT     UINT64                      *PspRegionBEntryAddress
  );

/**
 * @brief This function checks if the PSP L1 is multi slot layout
 *
 * @param[in]  PspL1Dir     Pointer to PSP L1 directory base
 *
 * @retval TRUE             The PSP L1 directory has multi slots (>=2)
 * @retval FALSE            The PSP L1 directory has not multi slots (>=2)
 *
 */
BOOLEAN
IsMultiSlotLayout (
  IN PSP_DIRECTORY       *PspL1Dir   //PSP L1 directory
  );

/**
 * @brief This function checks if the PSP L1 is two slot A/B recovery layout (VN)
 *
 * @param[in]  PspL1Dir     Pointer to PSP L1 directory base
 *
 * @retval TRUE             The PSP L1 directory has 2 slots with A/B recovery
 * @retval FALSE            The PSP L1 directory does not have 2 slots with A/B recovery
*/
BOOLEAN
IsTwoSlotAbRecovery (
  IN PSP_DIRECTORY       *PspL1Dir   //PSP L1 directory
  );

/**
 * @brief Get PSP Entry SCS binary information
 *
 * @param[in,out] EntryAddress        Address of SCS binary
 * @param[in,out] EntrySize           Size of the SCS binary
 *
 * @retval TRUE      The given entry type is found
 * @retval FALSE     The given entry type is not found
 */
BOOLEAN
GetPspEntryScsBinaryV2 (
  IN OUT   UINT64                      *EntryAddress,
  IN OUT   UINT32                      *EntrySize
  );

/**
 * @brief Get PSP Entry PSP NV data information
 *
 * @param[in,out] EntryAddress        Address of PSP NV data
 * @param[in,out] EntrySize           Size of the PSP NV data
 *
 * @retval TRUE      The given entry type is found
 * @retval FALSE     The given entry type is not found
 */
BOOLEAN
GetPspEntryPspNvDataV2 (
  IN OUT   UINT64                      *EntryAddress,
  IN OUT   UINT32                      *EntrySize
  );

/**
 * @brief Check PSP Recovery Flag using SMN to remove MMIO initial dependency
 * Target will set Recovery flag if some PSP entry point by PSP directory has been corrupted.
 *
 * @retval BOOLEAN  0: Recovery Flag is cleared, 1: Recovery Flag has been set
 */
BOOLEAN
CheckPspRecoveryFlagSmn (
  VOID
  );

/**
 * @brief Get Image Slot Information
 * @details For ISH V2 (RMB and later), 0x48 points to Slot A, 0x4A points to Slot B.
 * A BIOS build should verify that the 0x48 entry ISH has a higher boot priority than
 * 0x4A entry for a given SoC. In other words, ISH.A should have higher boot priority
 * than ISH.B, and this is a requirement from both BIOS and PSP Firmware.
 *
 * @param[in]      PspL1Dir   PSP L1 Directory
 * @param[in,out]  Slot       Image Slot Information
 *
 * @return BOLLEN  TRUE       Valid slot entry found
 *                 FALSE      Image slot is not found
 */
BOOLEAN
GetImageSlotInfo (
  IN      PSP_DIRECTORY     *PspL1Dir,
  IN OUT  IMAGE_SLOT_INFO   **Slot
);

/**
 * @brief Return the PSP Entry Address of given PSP entry type and level
 *
 * @param[in]        EntryType             Value of given PSP entry type, for system using fw id defined in PSP_DIRECTORY_FW_ID,
 *                                         or else PSP_DIRECTORY_ENTRY_TYPE_ID
 * @param[in]        IsLevel2              The given entry is in PSP level 2 directory
 * @param[in, out]   EntryAddress          Pointer to PSP entry address
 *
 * @retval TRUE      The given entry type is found
 * @retval FALSE     The given entry type is not found
**/
BOOLEAN
GetPspEntryAddress (
  IN      UINT16     EntryType,
  IN      BOOLEAN   IsLevel2,
  IN OUT  UINT64    *EntryAddress
  );

/**
 *
 *  @brief Get A/B Recovery reason
 *
 *  @param[in,out]  Reason         Buffer to save recovery reason
 *  @param[in,out]  Size           Buffer size
 *  @param[in,out]  ReasonVersion  Recovery reason version, optional parameter
 *        with this info, you can know which type(RECOVERY_REASON_V1/RECOVERY_REASON_V2) to convert the buffer into.
 *        specify NULL if you do not need to know the recovery reason version info
 *
 *  @retval EFI_STATUS            0: Success, NonZero Error
 *
 **/
EFI_STATUS
GetRecoveryReason (
  IN OUT VOID                    *Reason,
  IN OUT UINT32                  *Size,
  IN OUT RECOVERY_REASON_VERSION *ReasonVersion
  );

/**
 * @brief Set A/B Recovery Reason to BIOS RAM
 *
 * @param[in] RecoveryReason  you need to convert RECOVERY_REASON_V1 or RECOVERY_REASON_V2 to UINT32 before this call
 *
 * @return EFI_STATUS     0: Success, NonZero Error
 */
EFI_STATUS
SetRecoveryReason (
  IN UINT32      RecoveryReason
  );

#endif // _AMD_DIRECTORY_BASELIB_H_



