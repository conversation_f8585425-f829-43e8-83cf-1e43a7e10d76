/*****************************************************************************
 * Copyright (C) 2008-2024 Advanced Micro Devices, Inc. All rights reserved.
 *
*****************************************************************************
*/
/* $NoKeywords:$ */
/**
 * @file
 *
 * FCH SMM Driver
 *
 *
 * @xrefitem bom "File Content Label" "Release Content"
 * @e project:      AGESA
 * @e sub-project   FCH SMM Driver
 * @e \$Revision: 309090 $   @e \$Date: 2014-12-09 10:28:05 -0800 (Tue, 09 Dec 2014) $
 *
 */
#include "FchSmm.h"
#define FILECODE FCH_KUNLUN_FCHKUNLUNSMM_SXSMI_FILECODE

extern  UINT8                       *mFchPciIrqRoutingTable;
extern  FCH_DATA_BLOCK              gFchDataInSmm;


/**
 * @brief Register sleep handler.
 *
 * @returns EFI_STATUS
 * @retval EFI_SUCCESS   Module initialized successfully
 * @retval EFI_ERROR     Initialization failed (see error for more details)
 */
EFI_STATUS
FchSmmRegisterSxSmi (
  VOID
  )
{
  EFI_STATUS                               Status;
  FCH_SMM_SX_DISPATCH2_PROTOCOL            *AmdSxDispatch;
  FCH_SMM_SX_REGISTER_CONTEXT              SxRegisterContext;
  EFI_HANDLE                               SxHandle;

  //
  // Register AMD SX SMM
  //
  Status = gSmst->SmmLocateProtocol (
                  &gFchSmmSxDispatch2ProtocolGuid,
                  NULL,
                  (VOID **)&AmdSxDispatch
                  );
  ASSERT_EFI_ERROR (Status);

  SxRegisterContext.Type  = SxS3;
  SxRegisterContext.Phase = SxEntry;
  SxRegisterContext.Order = 1;

  Status = AmdSxDispatch->Register (
                             AmdSxDispatch,
                             AmdSmiS3SleepEntryCallback,
                             &SxRegisterContext,
                             &SxHandle
                             );

  SxRegisterContext.Type  = SxS4;
  SxRegisterContext.Phase = SxEntry;
  SxRegisterContext.Order = 1;

  Status = AmdSxDispatch->Register (
                             AmdSxDispatch,
                             AmdSmiS4SleepEntryCallback,
                             &SxRegisterContext,
                             &SxHandle
                             );

  SxRegisterContext.Type  = SxS5;
  SxRegisterContext.Phase = SxEntry;
  SxRegisterContext.Order = 1;

  Status = AmdSxDispatch->Register (
                             AmdSxDispatch,
                             AmdSmiS5SleepEntryCallback,
                             &SxRegisterContext,
                             &SxHandle
                             );

  return Status;
}


/**
 * @brief Handler for sleep S3.
 *
 * @param[in] DispatchHandle   The handle of this callback, obtained when registering
 * @param[in] DispatchContext  Pointer to the FCH_SMM_SX_REGISTER_CONTEXT
 * @param[in] CommBuffer       Pointer to the communication buffer
 * @param[in] CommBufferSize   Pointer to the communication buffer size
 *
 * @returns EFI_STATUS
 * @retval EFI_SUCCESS   Module initialized successfully
 */
EFI_STATUS
EFIAPI
AmdSmiS3SleepEntryCallback (
  IN       EFI_HANDLE                        DispatchHandle,
  IN       CONST FCH_SMM_SX_REGISTER_CONTEXT *DispatchContext,
  IN OUT   VOID                              *CommBuffer OPTIONAL,
  IN OUT   UINTN                             *CommBufferSize  OPTIONAL
  )
{
  UINT8          Index;
  UINT8          *pData;
  FCH_DATA_BLOCK      *pFchPolicy;
  pFchPolicy = &gFchDataInSmm;
  // Save entire FCH PCI IRQ routing space (C00/C01)
  pData = mFchPciIrqRoutingTable;
  Index = 0xFF;
  do {
    Index++;
    LibFchSmmIoWrite (&gSmst->SmmIo, SMM_IO_UINT8, FCH_IOMAP_REGC00, &Index);
    LibFchSmmIoRead (&gSmst->SmmIo, SMM_IO_UINT8, FCH_IOMAP_REGC01, pData++);
  } while (Index != 0xFF);

  //Put Usb3 to S0 power rail
  RwMem (ACPI_MMIO_BASE + PMIO_BASE + FCH_PMIOA_REGEE, AccessWidth8, (UINT32)~(BIT0 + BIT1), (BIT1 + BIT0));
  BackUpCG2 ();
  FchKLXhciInitS3EntryProgram (0, pFchPolicy);
  FchKLXhciIohcPmeDisable (0, FALSE);

  if ( pFchPolicy->HwAcpi.FchSxEntryXhciPmeEn ) {
    FchProgramXhciPmeEn (
      0,
      PcdGetBool (PcdXhci0Enable),
      PcdGetBool (PcdXhci1Enable)
      );
  }

  return EFI_SUCCESS;
}


/**
 * @brief Handler for sleep S4.
 *
 * @param[in] DispatchHandle   The handle of this callback, obtained when registering
 * @param[in] DispatchContext  Pointer to the FCH_SMM_SX_REGISTER_CONTEXT
 * @param[in] CommBuffer       Pointer to the communication buffer
 * @param[in] CommBufferSize   Pointer to the communication buffer size
 *
 * @returns EFI_STATUS
 * @retval EFI_SUCCESS   Module initialized successfully
 */
EFI_STATUS
EFIAPI
AmdSmiS4SleepEntryCallback (
  IN       EFI_HANDLE                        DispatchHandle,
  IN       CONST FCH_SMM_SX_REGISTER_CONTEXT *DispatchContext,
  IN OUT   VOID                              *CommBuffer OPTIONAL,
  IN OUT   UINTN                             *CommBufferSize  OPTIONAL
  )
{
  FCH_DATA_BLOCK      *pFchPolicy;
  pFchPolicy = &gFchDataInSmm;

  //Put Usb3 to S0 power rail
  RwMem (ACPI_MMIO_BASE + PMIO_BASE + FCH_PMIOA_REGEE, AccessWidth8, (UINT32)~(BIT0 + BIT1), (BIT1 + BIT0));
  BackUpCG2 ();
  FchKLXhciInitS3EntryProgram (0, pFchPolicy);
  FchKLXhciIohcPmeDisable (0, FALSE);

  if ( pFchPolicy->HwAcpi.FchSxEntryXhciPmeEn ) {
    FchProgramXhciPmeEn (
      0,
      PcdGetBool (PcdXhci0Enable),
      PcdGetBool (PcdXhci1Enable)
      );
  }

  return EFI_SUCCESS;
}


/**
 * @brief Handler for sleep S5.
 *
 * @param[in] DispatchHandle   The handle of this callback, obtained when registering
 * @param[in] DispatchContext  Pointer to the FCH_SMM_SX_REGISTER_CONTEXT
 * @param[in] CommBuffer       Pointer to the communication buffer
 * @param[in] CommBufferSize   Pointer to the communication buffer size
 *
 * @returns EFI_STATUS
 * @retval EFI_SUCCESS   Module initialized successfully
 */
EFI_STATUS
EFIAPI
AmdSmiS5SleepEntryCallback (
  IN       EFI_HANDLE                        DispatchHandle,
  IN       CONST FCH_SMM_SX_REGISTER_CONTEXT *DispatchContext,
  IN OUT   VOID                              *CommBuffer OPTIONAL,
  IN OUT   UINTN                             *CommBufferSize  OPTIONAL
  )
{
  FCH_DATA_BLOCK      *pFchPolicy;
  pFchPolicy = &gFchDataInSmm;

  BackUpCG2 ();
  //RwMem (ACPI_MMIO_BASE + SMI_BASE + FCH_SMI_REG04 + 2, AccessWidth8, ~BIT3, BIT3);
  FchKLXhciInitS3EntryProgram (0, pFchPolicy);
  return EFI_SUCCESS;
}


