/*****************************************************************************
 * Copyright (C) 2008-2024 Advanced Micro Devices, Inc. All rights reserved.
 *
*****************************************************************************
*/
/* $NoKeywords:$ */
/**
 * @file
 *
 * Config Fch HwAcpi controller
 *
 * Init Spread Spectrum features.
 *
 * @xrefitem bom "File Content Label" "Release Content"
 * @e project:     AGESA
 * @e sub-project: FCH
 * @e \$Revision: 309083 $   @e \$Date: 2014-12-09 09:28:24 -0800 (Tue, 09 Dec 2014) $
 *
 */
#include "FchPlatform.h"
#include "Filecode.h"
#define FILECODE FCH_KUNLUN_FCHKUNLUNCORE_KUNLUN_KLHWACPI_KLSSSERVICE_FILECODE

/**
 * FchInitResetAcpiMmioTable - Fch ACPI MMIO initial
 * during the power on stage.
 *
 *
 *
 *
 */
ACPI_REG_WRITE FchInitResetAcpiMmioTable[] =
{
  {00, 00, 0xB0, 0xAC},                                         /// Signature

  {PMIO_BASE >> 8,  FCH_PMIOA_REG00 + 2, 0xFB, BIT2},           //PLAT-44454 Move ASF Clk switch code to PEI
  {PMIO_BASE >> 8,  FCH_PMIOA_REGD2, 0xCF, 0x00},
  {PMIO_BASE >> 8,  FCH_PMIOA_REG00, 0xF7, 0x77},
  {PMIO_BASE >> 8,  FCH_PMIOA_REG08, 0xFE, BIT2 + BIT4},
  {PMIO_BASE >> 8,  FCH_PMIOA_REG10, 0xFC, 0x00},
  {PMIO_BASE >> 8,  FCH_PMIOA_REG54, 0x00, BIT4 + BIT6},
  {PMIO_BASE >> 8,  FCH_PMIOA_REG74, 0xF6, BIT0},
  {PMIO_BASE >> 8,  FCH_PMIOA_REGC4, 0xEE, 0x04},                /// Release NB_PCIE_RST
  {PMIO_BASE >> 8,  FCH_PMIOA_REGC0 + 2, 0xBF, 0x40},
  {PMIO_BASE >> 8,  FCH_PMIOA_REGC4, 0xFB, 0},
  {PMIO_BASE >> 8,  FCH_PMIOA_REGDC, 0x00, 0},                   /// Clear XDC SATA configuration

  {PMIO_BASE >> 8,  FCH_PMIOA_REG3C + 2, (UINT8)(~BIT2), BIT2},  // Set PMIO_3E SMERR_L pad to output mode
  {PMIO_BASE >> 8,  FCH_PMIOA_REGC0 + 3, 0x00, BIT5},            // Clear PMIO_C0 mp1_wdtout

  //{MISC_BASE >> 8,  FCH_MISC_REG6C + 2, 0x7F, BIT7},           // MISC 0x6C BIT23
  {MISC_BASE >> 8,  FCH_MISC_REG6C + 3, 0xF7, BIT3},             // MISC 0x6C BIT27
  {MISC_BASE >> 8,  FCH_MISC_REG38 + 2, (UINT8)(~BIT2), 0x00},   // clear MISC 0x38 BIT18  for BTS issue.
  {0xFF, 0xFF, 0xFF, 0xFF},
};

ACPI_REG_WRITE FchKunlunInitResetRtcextTable[] =
{
  {00, 00, 0xB0, 0xAC},
  {PMIO_BASE >> 8,  FCH_PMIOA_REG5E, 0x00, 0x00},
  {PMIO_BASE >> 8,  FCH_PMIOA_REG5F, 0x00, 0x00},
  {PMIO_BASE >> 8,  FCH_PMIOA_REG5E, 0x00, 0x01},
  {PMIO_BASE >> 8,  FCH_PMIOA_REG5F, 0x00, 0x00},
  {PMIO_BASE >> 8,  FCH_PMIOA_REG5E, 0x00, 0x02},
  {PMIO_BASE >> 8,  FCH_PMIOA_REG5F, 0x00, 0x00},
  {PMIO_BASE >> 8,  FCH_PMIOA_REG5E, 0x00, 0x03},
  {PMIO_BASE >> 8,  FCH_PMIOA_REG5F, 0x00, 0x00},
  {PMIO_BASE >> 8,  FCH_PMIOA_REG5E, 0x00, 0x04},
  {PMIO_BASE >> 8,  FCH_PMIOA_REG5F, 0x00, 0x00},
  {PMIO_BASE >> 8,  FCH_PMIOA_REG5E, 0x00, 0x10},
  {PMIO_BASE >> 8,  FCH_PMIOA_REG5F, 0x00, 0x00},
  {PMIO_BASE >> 8,  FCH_PMIOA_REG5E, 0x00, 0x11},
  {PMIO_BASE >> 8,  FCH_PMIOA_REG5F, 0x00, 0x00},
  {PMIO_BASE >> 8,  FCH_PMIOA_REG5E, 0x00, 0x12},
  {PMIO_BASE >> 8,  FCH_PMIOA_REG5F, 0x00, 0x00},
  {PMIO_BASE >> 8,  FCH_PMIOA_REG5E, 0x00, 0x13},
  {PMIO_BASE >> 8,  FCH_PMIOA_REG5F, 0x00, 0x00},
  {PMIO_BASE >> 8,  FCH_PMIOA_REG5E, 0x00, 0x14},
  {PMIO_BASE >> 8,  FCH_PMIOA_REG5F, 0x00, 0x00},
  {0xFF, 0xFF, 0xFF, 0xFF},
};

/**
 * ProgramFchHwAcpiResetP  - Config SpreadSpectrum before PCI
 * emulation
 *
 *
 *
 * @param[in] FchDataPtr Fch configuration structure pointer.
 *
 */
VOID
ProgramFchHwAcpiResetP (
  IN VOID  *FchDataPtr
  )
{
  //UINT32                    RegEax;
  FCH_RESET_DATA_BLOCK      *LocalCfgPtr;
  AMD_CONFIG_PARAMS         *StdHeader;

  LocalCfgPtr = (FCH_RESET_DATA_BLOCK *) FchDataPtr;
  StdHeader = LocalCfgPtr->StdHeader;

  //
  // Clear UseAcpiStraps, PMIO_C8[4]
  //
  RwPmio (FCH_PMIOA_REGC8, AccessWidth8, 0xEF, 0x0, StdHeader);
}

VOID
FchInitEnableIxC (
  IN  VOID     *FchDataPtr
  )
{
  FCH_RESET_DATA_BLOCK      *LocalCfgPtr;

  LocalCfgPtr = (FCH_RESET_DATA_BLOCK *) FchDataPtr;

  //
  // I2C0
  //
  if ( LocalCfgPtr->I2CEnable & BIT5 ) {
    // ABL.FCH do I2C0 initialization
  } else {
    FchAoacPowerOnDev (FCH_AOAC_I2C0, 0);
  }

  //
  // I2C1
  //
  if ( LocalCfgPtr->I2CEnable & BIT6 ) {
    // ABL.FCH do I2C1 initialization
  } else {
    FchAoacPowerOnDev (FCH_AOAC_I2C1, 0);
  }

  //
  // I2C2
  //
  if ( LocalCfgPtr->I2CEnable & BIT7 ) {
    // ABL.FCH do I2C2 initialization
  } else {
    FchAoacPowerOnDev (FCH_AOAC_I2C2, 0);
  }
  //
  // I2C3
  //
  if ( LocalCfgPtr->I2CEnable & BIT8 ) {
    // ABL.FCH do I2C3 initialization
  } else {
    FchAoacPowerOnDev (FCH_AOAC_I2C3, 0);
  }

  //
  // I2C4
  //
  if ( LocalCfgPtr->I2CEnable & BIT9 ) {
    // ABL.FCH do I2C4 initialization
  } else {
    FchAoacPowerOnDev (FCH_AOAC_I2C4, 0);
  }

  //
  // I2C5
  //
  if ( LocalCfgPtr->I2CEnable & BIT10 ) {
    // ABL.FCH do I2C5 initialization
  } else {
    FchAoacPowerOnDev (FCH_AOAC_I2C5, 0);
  }

  //
  // I3C0
  //
  if ( LocalCfgPtr->I2CEnable & BIT21 ) {
    // ABL.FCH do I3C0 initialization
  } else {
    FchAoacPowerOnDev (FCH_AOAC_I3C0, 0);
  }

  //
  // I3C1
  //
  if ( LocalCfgPtr->I2CEnable & BIT13 ) {
    // ABL.FCH do I3C1 initialization
  } else {
    FchAoacPowerOnDev (FCH_AOAC_I3C1, 0);
  }

  //
  // I3C2
  //
  if ( LocalCfgPtr->I2CEnable & BIT14 ) {
    // ABL.FCH do I3C2 initialization
  } else {
    FchAoacPowerOnDev (FCH_AOAC_I3C2, 0);
  }

  //
  // I3C3
  //
  if ( LocalCfgPtr->I2CEnable & BIT15 ) {
    // ABL.FCH do I3C3 initialization
  } else {
    FchAoacPowerOnDev (FCH_AOAC_I3C3, 0);
  }
}


VOID
FchInitEnableWdt (
  IN  VOID     *FchDataPtr
  )
{
  FCH_RESET_DATA_BLOCK      *LocalCfgPtr;

  LocalCfgPtr = (FCH_RESET_DATA_BLOCK *) FchDataPtr;

  if (LocalCfgPtr->WdtEnable) {
    RwMem (ACPI_MMIO_BASE + PMIO_BASE + FCH_PMIOA_REG00, AccessWidth8, 0x7F, BIT7);
  } else {
    RwMem (ACPI_MMIO_BASE + PMIO_BASE + FCH_PMIOA_REG00, AccessWidth8, 0x7F, 0);
  }
}

VOID
FchInitEnableBootTimer (
  IN  VOID     *FchDataPtr
  )
{
  FCH_RESET_DATA_BLOCK      *LocalCfgPtr;

  LocalCfgPtr = (FCH_RESET_DATA_BLOCK *) FchDataPtr;

  if (LocalCfgPtr->BootTimerEnable) {
    // Set boot timer cold reset bit
    if (LocalCfgPtr->BootTimerResetType) {
      RwMem (ACPI_MMIO_BASE + PMIO_BASE + FCH_PMIOA_REG90+3, AccessWidth8, 0x7F, 0x80); // Cold Reset
    } else {
      RwMem (ACPI_MMIO_BASE + PMIO_BASE + FCH_PMIOA_REG90+3, AccessWidth8, 0x7F, 0x00); // Warm Reset
    }

    // clear boot timer, enable boot timer
    RwMem (ACPI_MMIO_BASE + PMIO_BASE + FCH_PMIOA_REG44+3, AccessWidth8, 0x67, 0x88);
  } else {
    // clear boot timer, disable boot timer
    RwMem (ACPI_MMIO_BASE + PMIO_BASE + FCH_PMIOA_REG44+3, AccessWidth8, 0x67, 0x80);
  }
}


/**
 * ProgramResetRtcExt - Config RTC External registers
 *
 * @param[in] FchDataPtr Fch configuration structure pointer.
 *
 */
VOID
ProgramResetRtcExt (
  IN  VOID     *FchDataPtr
  )
{
  UINT8                  RtcExtData;
  FCH_RESET_DATA_BLOCK      *LocalCfgPtr;
  AMD_CONFIG_PARAMS         *StdHeader;

  LocalCfgPtr = (FCH_RESET_DATA_BLOCK *) FchDataPtr;
  StdHeader = LocalCfgPtr->StdHeader;

  //check if RTCext data lost
  RwMem (ACPI_MMIO_BASE + PMIO_BASE + FCH_PMIOA_REG5E, AccessWidth8, 0, 0x01);
  ReadMem (ACPI_MMIO_BASE + PMIO_BASE + FCH_PMIOA_REG5F, AccessWidth8, &RtcExtData);
  if ( RtcExtData == 0xFF ) {
    ProgramFchAcpiMmioTbl ((ACPI_REG_WRITE*) (&FchKunlunInitResetRtcextTable[0]), StdHeader);
  }
}

/**
 * ProgramCpuRstBTmr - Config RESET_L time
 *
 * @param[in] FchDataPtr Fch configuration structure pointer.
 *
 */
VOID
ProgramCpuRstBTmr (
  IN  VOID     *FchDataPtr
  )
{
//   UINT32        RegEbx;
//   UINT8         PkgType;

//   //
//   // Check for SP3/SP4
//   RegEbx = 0;
//   AsmCpuid (0x80000001, NULL, &RegEbx, NULL, NULL);
//   // if (PkgType == PKG_SP3) {
//   //   RwMem (ACPI_MMIO_BASE + PMIO_BASE + FCH_PMIOA_REG20 + 2, AccessWidth8, 0, 0xFF);
//   //   RwMem (ACPI_MMIO_BASE + PMIO_BASE + FCH_PMIOA_REG20 + 3, AccessWidth8, 0xF8, 0x07);
//   // }
}



