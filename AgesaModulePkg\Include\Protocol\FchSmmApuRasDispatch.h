/*****************************************************************************
 * Copyright (C) 2008-2024 Advanced Micro Devices, Inc. All rights reserved.
 *
*****************************************************************************
*/
/* $NoKeywords:$ */
/**
 * @file
 *
 * AMD FCH APU RAS SMI Dispacther Protocol
 *
 *
 *
 * @xrefitem bom "File Content Label" "Release Content"
 * @e project:      AMD FCH UEFI Drivers
 * @e sub-project:  Protocols
 * @e \$Revision: 309090 $   @e \$Date: 2014-12-09 10:28:05 -0800 (Tue, 09 Dec 2014) $
 *
 */
#ifndef _FCH_SMM_APURAS_DISPATCH_PROTOCOL_H_
#define _FCH_SMM_APURAS_DISPATCH_PROTOCOL_H_

/**
 * @brief AMD FCH SMM APU RAS Dispatch protocol GUID
 */
extern EFI_GUID gFchSmmApuRasDispatchProtocolGuid;

/// Forward declaration for the FCH_SMM_APURAS_DISPATCH_PROTOCOL
typedef struct _FCH_SMM_APURAS_DISPATCH_PROTOCOL FCH_SMM_APURAS_DISPATCH_PROTOCOL;

/**
 * @brief AMD FCH SMM APU RAS Register Context
 * @param Socket Socket number  0-3 CPU, 4-11 MI200
 * @param Die Die number
 * @param Bus Bus number
 * @param NbioNumber NBIO number
 * @param Order Priority 0-Highest (reserved), 0xFF-Lowest (reserved)
 */
typedef struct {
  UINT8                     Socket;
  UINT8                     Die;                // obsolete since Turin
  UINT8                     Bus;                // obsolete since Turin
  UINT8                     Seg; 
  UINT8                     NbioNumber;
  UINT8                     Order;
} FCH_SMM_APURAS_REGISTER_CONTEXT;

/**
 * @brief Prototype of FCH_SMM_APURAS_HANDLER_ENTRY_POINT
 * @param DispatchHandle Dispatch handle
 * @param MiscRegisterContext Pointer to FCH_SMM_APURAS_REGISTER_CONTEXT structure
 */
typedef EFI_STATUS (EFIAPI *FCH_SMM_APURAS_HANDLER_ENTRY_POINT) (
  IN       EFI_HANDLE                        DispatchHandle,
  IN       FCH_SMM_APURAS_REGISTER_CONTEXT   *MiscRegisterContext
  );

/**
 * @brief Prototype of FCH_SMM_APURAS_DISPATCH_REGISTER
 * @param This Pointer to SMM APURAS DISPATCH PROTOCOL
 * @param CallBackFunction Callback function for SMM APURAS
 * @param MiscRegisterContext Pointer to the Misc Register Context
 * @param DispatchHandle Dispatch Handle
 */
typedef EFI_STATUS (EFIAPI *FCH_SMM_APURAS_DISPATCH_REGISTER) (
  IN CONST FCH_SMM_APURAS_DISPATCH_PROTOCOL        *This,
  IN       FCH_SMM_APURAS_HANDLER_ENTRY_POINT      CallBackFunction,
  IN OUT   FCH_SMM_APURAS_REGISTER_CONTEXT         *MiscRegisterContext,
  OUT      EFI_HANDLE                              *DispatchHandle
);

/**
 * @brief Prototype of FCH_SMM_APURAS_DISPATCH_UNREGISTER
 * @param This Pointer to FCH_SMM_APURAS_DISPATCH_PROTOCOL
 * @param DispatchHandle Dispatch Handle
 */
typedef EFI_STATUS (EFIAPI *FCH_SMM_APURAS_DISPATCH_UNREGISTER) (
  IN       CONST FCH_SMM_APURAS_DISPATCH_PROTOCOL   *This,
  IN       EFI_HANDLE                               DispatchHandle
);

/**
 * @brief FCH_SMM_APURAS_DISPATCH_PROTOCOL Structure
 * @param Register Register function
 * @param UnRegister UnRegister function
 */
struct  _FCH_SMM_APURAS_DISPATCH_PROTOCOL {
  FCH_SMM_APURAS_DISPATCH_REGISTER    Register;
  FCH_SMM_APURAS_DISPATCH_UNREGISTER  UnRegister;
};

#endif


