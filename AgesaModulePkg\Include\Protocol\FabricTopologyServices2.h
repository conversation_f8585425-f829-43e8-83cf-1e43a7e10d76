/*****************************************************************************
 * Copyright (C) 2008-2024 Advanced Micro Devices, Inc. All rights reserved.
 *
*****************************************************************************
*/
/* $NoKeywords:$ */
/**
 * @file
 *
 * Fabric Topology Services V2 Protocol prototype definition
 *
 *
 * @xrefitem bom "File Content Label" "Release Content"
 * @e project:      AGESA
 * @e sub-project:  Fabric
 * @e \$Revision$   @e \$Date$
 */
#ifndef _FABRIC_TOPOLOGY_SERVICES2_H_
#define _FABRIC_TOPOLOGY_SERVICES2_H_

#include <Library/BaseFabricTopologyLib.h>

///
/// Forward declaration for the AMD_FABRIC_TOPOLOGY_SERVICES2_PROTOCOL.
///
typedef struct _AMD_FABRIC_TOPOLOGY_SERVICES2_PROTOCOL AMD_FABRIC_TOPOLOGY_SERVICES2_PROTOCOL;

/**
 * This service retrieves information about the overall system with respect to data fabric.
 *
 * @param[in]  This                           A pointer to the
 *                                            AMD_FABRIC_TOPOLOGY_SERVICES2_PROTOCOL instance.
 * @param[out] NumberOfInstalledProcessors    Pointer to the total number of populated
 *                                            processor sockets in the system.
 * @param[out] TotalNumberOfDie               Pointer to the total number of die in the system.
 * @param[out] TotalNumberOfRootBridges       Pointer to the total number of root PCI bridges in
 *                                            the system.
 * @param[out] SystemFchRootBridgeLocation    System primary FCH location.
 * @param[out] SystemSmuRootBridgeLocation    System primary SMU location.
 *
 * @retval EFI_SUCCESS                        The system topology information was successfully retrieved.
 * @retval EFI_INVALID_PARAMETER              All output parameter pointers are NULL.
 *
 **/
typedef
EFI_STATUS
(EFIAPI *AMD_FABRIC_TOPOLOGY_SERVICES2_GET_SYSTEM_INFO) (
  IN       AMD_FABRIC_TOPOLOGY_SERVICES2_PROTOCOL     *This,
     OUT   UINTN                                      *NumberOfInstalledProcessors,
     OUT   UINTN                                      *TotalNumberOfDie,
     OUT   UINTN                                      *TotalNumberOfRootBridges,
     OUT   ROOT_BRIDGE_LOCATION                       *SystemFchRootBridgeLocation,
     OUT   ROOT_BRIDGE_LOCATION                       *SystemSmuRootBridgeLocation
  );

/**
 * This service retrieves information about the processor installed in the given socket.
 *
 * If no processor is installed in Socket, then EFI_INVALID_PARAMETER is returned.
 *
 * @param[in]  This                           A pointer to the
 *                                            AMD_FABRIC_TOPOLOGY_SERVICES2_PROTOCOL instance.
 * @param[in]  Socket                         Zero-based socket number to check.
 * @param[out] NumberOfDie                    Pointer to the number of die present on the
 *                                            given processor.
 * @param[out] NumberOfRootBridges            Pointer to the number of root PCI bridges on
 *                                            the given processor.
 *
 * @retval EFI_SUCCESS                        The processor information was retrieved successfully.
 * @retval EFI_INVALID_PARAMETER              Socket is non-existent.
 * @retval EFI_INVALID_PARAMETER              All output parameter pointers are NULL.
 *
 **/
typedef
EFI_STATUS
(EFIAPI *AMD_FABRIC_TOPOLOGY_SERVICES2_GET_PROCESSOR_INFO) (
  IN       AMD_FABRIC_TOPOLOGY_SERVICES2_PROTOCOL     *This,
  IN       UINTN                                       Socket,
     OUT   UINTN                                      *NumberOfDie,
     OUT   UINTN                                      *NumberOfRootBridges
  );

/**
 * This service retrieves information about the given die.
 *
 * @param[in]  This                  A pointer to the
 *                                   AMD_FABRIC_TOPOLOGY_SERVICES2_PROTOCOL instance.
 * @param[in]  Socket                The target die's socket identifier.
 * @param[in]  Die                   The target die's identifier within Socket.
 * @param[out] NumberOfRootBridges   Pointer to the number of root PCI bridges
 *                                   present on the given die.
 * @param[out] SystemIdOffset        Pointer to the die's offset for all of its
 *                                   devices.
 * @param[out] FabricIdMap           Pointer to an array describing the devices
 *                                   present within the given die's fabric.
 *
 * @retval EFI_SUCCESS               The die information was retrieved successfully.
 * @retval EFI_INVALID_PARAMETER     Socket is non-existent.
 * @retval EFI_INVALID_PARAMETER     Die is non-existent.
 * @retval EFI_INVALID_PARAMETER     All output parameter pointers are NULL.
 *
 **/
typedef
EFI_STATUS
(EFIAPI *AMD_FABRIC_TOPOLOGY_SERVICES2_GET_DIE_INFO) (
  IN       AMD_FABRIC_TOPOLOGY_SERVICES2_PROTOCOL     *This,
  IN       UINTN                                       Socket,
  IN       UINTN                                       Die,
     OUT   UINTN                                      *NumberOfRootBridges,
     OUT   UINTN                                      *SystemIdOffset,
     OUT   AMD_FABRIC_TOPOLOGY_DIE_DEVICE_MAP        **FabricIdMap
  );

/**
 * This service retrieves information about the given root PCI bridge.
 *
 * @param[in]  This                  A pointer to the
 *                                   AMD_FABRIC_TOPOLOGY_SERVICES2_PROTOCOL instance.
 * @param[in]  Socket                The target root bridge's socket identifier.
 * @param[in]  Die                   The target root bridge's die identifier within Socket.
 * @param[in]  Index                 The target root bridge's 0-based index on die
 * @param[out] SystemFabricID        Pointer to the root bridge's fabric identifier
 *                                   within the system.
 * @param[out] BusNumberBase         Pointer to the root bridge's base PCI bus
 *                                   number in the system.
 * @param[out] BusNumberLimit        Pointer to the root bridge's maximum decoded
 *                                   PCI bus number in the system.
 * @param[out] PhysicalRootBridgeNumber Physical RootBridge number of RootBridge specified by Socket/Die/Index.
 * @param[out] HasFchDevice          If this RootBridge has FCH.
 * @param[out] HasSystemMgmtUnit     If this RootBridge has SMU.
 *
 * @retval EFI_SUCCESS               The root bridge's information was retrieved successfully.
 * @retval EFI_INVALID_PARAMETER     Socket is non-existent.
 * @retval EFI_INVALID_PARAMETER     Die is non-existent.
 * @retval EFI_INVALID_PARAMETER     FabricId is non-existent.
 * @retval EFI_INVALID_PARAMETER     Socket / Die / FabricId does not have a PCI bus range.
 * @retval EFI_INVALID_PARAMETER     All output parameter pointers are NULL.
 *
 **/
typedef
EFI_STATUS
(EFIAPI *AMD_FABRIC_TOPOLOGY_SERVICES2_GET_ROOT_BRIDGE_INFO) (
  IN       AMD_FABRIC_TOPOLOGY_SERVICES2_PROTOCOL     *This,
  IN       UINTN                                       Socket,
  IN       UINTN                                       Die,
  IN       UINTN                                       Index,
     OUT   UINTN                                      *SystemFabricID,
     OUT   UINTN                                      *BusNumberBase,
     OUT   UINTN                                      *BusNumberLimit,
     OUT   UINTN                                      *PhysicalRootBridgeNumber,
     OUT   BOOLEAN                                    *HasFchDevice,
     OUT   BOOLEAN                                    *HasSystemMgmtUnit
      );

///
/// When installed, the Fabric Topology Services produces a collection of
/// services that provide information on Fabric Topology.
///
struct _AMD_FABRIC_TOPOLOGY_SERVICES2_PROTOCOL {
  AMD_FABRIC_TOPOLOGY_SERVICES2_GET_SYSTEM_INFO        GetSystemInfo;      ///< This service retrieves information about the overall system with respect to data fabric.
  AMD_FABRIC_TOPOLOGY_SERVICES2_GET_PROCESSOR_INFO     GetProcessorInfo;   ///< This service retrieves information about the processor installed in the given socket.
  AMD_FABRIC_TOPOLOGY_SERVICES2_GET_DIE_INFO           GetDieInfo;         ///< This service retrieves information about the given die.
  AMD_FABRIC_TOPOLOGY_SERVICES2_GET_ROOT_BRIDGE_INFO   GetRootBridgeInfo;  ///< This service retrieves information about the given root PCI bridge.
};

///
/// Guid declaration for the AMD_FABRIC_TOPOLOGY_SERVICES2_PROTOCOL.
///
extern EFI_GUID gAmdFabricTopologyServices2ProtocolGuid;

///
/// Guid declaration for the SMM AMD_FABRIC_TOPOLOGY_SERVICES2_PROTOCOL.
///
extern EFI_GUID gAmdFabricTopologyServices2SmmProtocolGuid;

#endif



