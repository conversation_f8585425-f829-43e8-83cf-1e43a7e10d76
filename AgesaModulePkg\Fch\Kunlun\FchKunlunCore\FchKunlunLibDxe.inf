#;*****************************************************************************
#;
#; Copyright (C) 2008-2024 Advanced Micro Devices, Inc. All rights reserved.
#;
#;******************************************************************************

[Defines]
  LIBRARY_CLASS         = FchKunlunDxeLib | DXE_DRIVER DXE_SMM_DRIVER DXE_RUNTIME_DRIVER
  BASE_NAME             = FchKunlunDxeLib
  INF_VERSION           = 0x00010006
  FILE_GUID             = d93f67b4-c002-4968-8cd3-ac338e139ffe
  MODULE_TYPE           = BASE
  VERSION_STRING        = 1.0

[Sources]
  Fch.h
  FchPage.h
  FchPlatform.h
  FchAoacLinkListData.h
  Common/AcpiLib.c
  Common/AcpiLib.h
  Common/FchAoacLib.c
  Common/FchAoacLib.h
  Common/FchCommon.c
  Common/FchCommonCfg.h
  Common/FchCommonSmm.c
  Common/FchDef.h
  Common/FchInterface.h
  Common/FchLib.c
  Common/FchPeLib.c
  Common/MemLib.c
  Common/PciLib.c
  Kunlun/EnvDefKL.c
  Kunlun/ResetDefKL.c
  Kunlun/KLHwAcpi/KLHwAcpiEnv.c
  Kunlun/KLHwAcpi/KLHwAcpiEnvService.c
  Kunlun/KLHwAcpi/KLHwAcpiReset.c
  Kunlun/KLHwAcpi/KLSSService.c
  Kunlun/KLInterface/KLFchInitEnv.c
  Kunlun/KLInterface/KLFchTaskLauncher.c
  Kunlun/KLInterface/KLFchTaskLauncher.h
  Kunlun/KLPcie/KLAbEnv.c
  Kunlun/KLPcie/KLAbEnvService.c
  Kunlun/KLPcie/KLAbReset.c
  Kunlun/KLPcie/KLAbResetService.c
  Kunlun/KLPcie/KLAbService.c
  Kunlun/KLSata/KLAhciEnv.c
  Kunlun/KLSata/KLAhciLib.c
  Kunlun/KLSata/KLRaidEnv.c
  Kunlun/KLSata/KLRaidLib.c
  Kunlun/KLSata/KLSataEnv.c
  Kunlun/KLSata/KLSataEnvLib.c
  Kunlun/KLSata/KLSataEnvService.c
  Kunlun/KLSata/KLSataLib.c
  Kunlun/KLSata/KLSataReset.c
  Kunlun/KLSata/KLSataResetService.c
  Kunlun/KLSd/KLSdEnv.c
  Kunlun/KLSd/KLSdEnvService.c
  Kunlun/KLSd/KLSdResetService.c
  Kunlun/KLLpcSpi/KLLpcEnv.c
  Kunlun/KLLpcSpi/KLLpcEnvService.c
  Kunlun/KLLpcSpi/KLLpcReset.c
  Kunlun/KLLpcSpi/KLLpcResetService.c
  Kunlun/KLLpcSpi/KLSpiEnv.c
  Kunlun/KLLpcSpi/KLSpiReset.c
  Kunlun/KLUsb/KLUsbEnv.c
  Kunlun/KLUsb/KLUsbReset.c
  Kunlun/KLUsb/KLXhciEnv.c
  Kunlun/KLUsb/KLXhciRecovery.c
  Kunlun/KLUsb/KLXhciReset.c
  Kunlun/KLUsb/KLXhciResetService.c


  Kunlun/KLHwAcpi/KLHwAcpiLate.c
  Kunlun/KLHwAcpi/KLHwAcpiLateService.c
  Kunlun/KLHwAcpi/KLHwAcpiMid.c
  Kunlun/KLHwAcpi/KLHwAcpiMidService.c
  Kunlun/KLInterface/KLFchInitLate.c
  Kunlun/KLInterface/KLFchInitMid.c
  Kunlun/KLInterface/KLFchInitS3.c
  Kunlun/KLPcie/KLAbLate.c
  Kunlun/KLPcie/KLAbMid.c
  Kunlun/KLSata/KLAhciLate.c
  Kunlun/KLSata/KLAhciMid.c
  Kunlun/KLSata/KLRaidLate.c
  Kunlun/KLSata/KLRaidMid.c
  Kunlun/KLSata/KLSataLate.c
  Kunlun/KLSata/KLSataMid.c
  Kunlun/KLSata/KLSataService.c
  Kunlun/KLSd/KLSdLate.c
  Kunlun/KLSd/KLSdMid.c
  Kunlun/KLSd/KLSdService.c
  Kunlun/KLLpcSpi/KLLpcLate.c
  Kunlun/KLLpcSpi/KLLpcMid.c
  Kunlun/KLLpcSpi/KLSpiLate.c
  Kunlun/KLLpcSpi/KLSpiMid.c
  Kunlun/KLUsb/KLUsbLate.c
  Kunlun/KLUsb/KLUsbMid.c
  Kunlun/KLUsb/KLXhciLate.c
  Kunlun/KLUsb/KLXhciMid.c
  Kunlun/KLUsb/KLXhciService.c
  Kunlun/KLEspi/KLEspiLib.c

[Packages]
  MdePkg/MdePkg.dec
  AgesaPkg/AgesaPkg.dec
  AgesaModulePkg/AgesaCommonModulePkg.dec
  AgesaModulePkg/AgesaModuleDfPkg.dec
  AgesaModulePkg/AgesaModuleFchPkg.dec
  AgesaModulePkg/Fch/Kunlun/FchKunlun.dec
  AgesaPkg/AgesaPkg.dec

[LibraryClasses]
  BaseMemoryLib
  AmdBaseLib
  AmdSocBaseLib
  NbioHandleLib
  NbioSmuBrhLib
  FabricResourceManagerLib
  FchBaseLib
  BaseLib
  AmdCapsuleLib
  UefiBootServicesTableLib
  AmdPspMboxLibV2

[Guids]

[Protocols]
  gAmdFabricTopologyServices2ProtocolGuid
[Ppis]

[Pcd]
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdAmdEnvironmentFlag
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdAmdPreSilCtrl0
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdAmdMemMaxDimmPerChannelV2
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdFchIxcTelemetryPortsFenceControl

[BuildOptions]





