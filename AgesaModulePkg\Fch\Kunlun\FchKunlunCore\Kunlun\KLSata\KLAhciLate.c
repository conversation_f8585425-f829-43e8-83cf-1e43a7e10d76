/*****************************************************************************
 * Copyright (C) 2008-2024 Advanced Micro Devices, Inc. All rights reserved.
 *
*****************************************************************************
*/
/* $NoKeywords:$ */
/**
 * @file
 *
 * Config Fch SATA controller (AHCI mode)
 *
 * Init SATA AHCI features.
 *
 * @xrefitem bom "File Content Label" "Release Content"
 * @e project:     AGESA
 * @e sub-project: FCH
 * @e \$Revision: 309083 $   @e \$Date: 2014-12-09 09:28:24 -0800 (Tue, 09 Dec 2014) $
 *
 */
#include "FchPlatform.h"
#include "Filecode.h"
#define FILECODE FCH_KUNLUN_FCHKUNLUNCORE_KUNLUN_KLSATA_KLAHCILATE_FILECODE

/**
 * FchInitLateSataAhciKL - Prepare SATA AHCI controller to boot
 * to OS.
 *
 * @param[in] DieBusNum  Bus Number of current Die.
 * @param[in] Controller Sata controller number.
 * @param[in] FchDataPtr Fch configuration structure pointer.
 *
 */
VOID
FchInitLateSataAhciKL (
  IN  UINT32   DieBusNum,
  IN  UINT32   Controller,
  IN  VOID     *FchDataPtr
  )
{
  FchKLSataShutdownUnconnectedSataPortClock (DieBusNum, Controller, FchDataPtr);
}



