#;*****************************************************************************
#;
#; Copyright (C) 2015-2024 Advanced Micro Devices, Inc. All rights reserved.
#;
#;******************************************************************************
[Defines]
  INF_VERSION                    = 0x00010005
  BASE_NAME                      = AmdSocBaseLib
  FILE_GUID                      = 95A0F124-BBBA-4280-A56D-F46C3F360F3F
  MODULE_TYPE                    = BASE
  VERSION_STRING                 = 1.0
  LIBRARY_CLASS                  = AmdSocBaseLib|PEIM DXE_DRIVER DXE_SMM_DRIVER DXE_RUNTIME_DRIVER DXE_CORE UEFI_APPLICATION

#
#  VALID_ARCHITECTURES           = IA32 X64
#

[Sources]
  AmdSocBaseLib.c

[Packages]
  MdePkg/MdePkg.dec
  AgesaModulePkg/AgesaCommonModulePkg.dec
  AgesaPkg/AgesaPkg.dec

[LibraryClasses]
  BaseLib
  AmdBaseLib
  PciLib

[Pcd]



