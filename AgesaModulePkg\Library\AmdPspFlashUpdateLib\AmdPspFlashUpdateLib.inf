#;*****************************************************************************
#;
#; Copyright (C) 2008-2024 Advanced Micro Devices, Inc. All rights reserved.
#;
#;******************************************************************************
[Defines]
  INF_VERSION                    = 0x00010006
  BASE_NAME                      = AmdPspFlashUpdateLib
  FILE_GUID                      = 31EDDDF7-6112-40E5-8D23-7BB3C9BC0C48
  MODULE_TYPE                    = DXE_DRIVER
  VERSION_STRING                 = 1.0
  LIBRARY_CLASS                  = AmdPspFlashUpdateLib | DXE_DRIVER  DXE_SMM_DRIVER
  CONSTRUCTOR                    = AmdPspFlashUpdateLibConstructor

[Sources.common]
  AmdPspFlashUpdateLib.c

[Packages]
  MdePkg/MdePkg.dec
  AgesaPkg/AgesaPkg.dec
  AgesaModulePkg/AgesaCommonModulePkg.dec
  AgesaModulePkg/AgesaModulePspPkg.dec

[LibraryClasses]
  IdsLib
  AmdBaseLib
  MemoryAllocationLib
  BaseMemoryLib
  UefiBootServicesTableLib
  AmdPspFlashAccLib
  AmdPspBaseLibV2
  PcdLib

[Guids]

[Protocols]
  gSmmFlashUpdateTempBufferAddressProtocolGuid
  gSmmFlashUpdateEnableFlagProtocolGuid
  gEfiSmmBase2ProtocolGuid

[Pcd]
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdFlashUpdateTempRuntimeBufferAddress
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdPspSpiUpdateEnable
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdAmdPspFlashEraseTimeout

[Depex]
  TRUE


