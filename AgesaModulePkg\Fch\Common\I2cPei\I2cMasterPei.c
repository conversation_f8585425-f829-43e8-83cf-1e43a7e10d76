/*****************************************************************************
 *
 * Copyright (C) 2008-2024 Advanced Micro Devices, Inc. All rights reserved.
 *
 *******************************************************************************
 */

/*----------------------------------------------------------------------------------------
 *                             M O D U L E S    U S E D
 *----------------------------------------------------------------------------------------
 */

#include <DwI2cRegs.h>

#include <Ppi/FchI2cMasterPostPpi.h>
#include <Library/DebugLib.h>
#include <Library/IoLib.h>
#include <Library/IdsLib.h>
#include <Library/BaseMemoryLib.h>

#include <Ppi/I2cMaster.h>
#include "I2cMasterPei.h"
#include <Library/FchI2cLib.h>

#include <Pi/PiPeiCis.h>
#include <Library/FchBaseLib.h>
#include <Library/FchSocLib.h>
#include <Library/BaseFabricTopologyLib.h>

STATIC PEI_FCH_I2C_MASTER_POST_PPI mI2cMasterPostPpi = {
  PEI_FCH_I2C_MASTER_post_PPI_REVISION,
};

STATIC EFI_PEI_PPI_DESCRIPTOR mI2cMasterPost = {
  (EFI_PEI_PPI_DESCRIPTOR_PPI | EFI_PEI_PPI_DESCRIPTOR_TERMINATE_LIST),
  &gPeiI2cMasterPostPpiGuid,
  &mI2cMasterPostPpi
};

EFI_PEI_NOTIFY_DESCRIPTOR   mFChI2CInitCallback = {
  (EFI_PEI_PPI_DESCRIPTOR_NOTIFY_DISPATCH | EFI_PEI_PPI_DESCRIPTOR_TERMINATE_LIST),
  &gAmdFabricTopologyServices2PpiGuid,
  FchI2CInitCallback
};
/*----------------------------------------------------------------------------------------
 *                   D E F I N I T I O N S    A N D    M A C R O S
 *----------------------------------------------------------------------------------------
 */
#define FILECODE FCH_COMMON_I2CPEI_I2CMASTERPEI_FILECODE

/*----------------------------------------------------------------------------------------
 *                  T Y P E D E F S     A N D     S T R U C T U R E S
 *----------------------------------------------------------------------------------------
 */


/*----------------------------------------------------------------------------------------
 *           P R O T O T Y P E S     O F     L O C A L     F U N C T I O N S
 *----------------------------------------------------------------------------------------
 */


/*----------------------------------------------------------------------------------------
 *                          E X P O R T E D    F U N C T I O N S
 *----------------------------------------------------------------------------------------
 */

EFI_GUID AmdI2cMasterID = { 0x6C710936, 0xE5EC, 0x4A68, { 0xA9, 0x76, 0xDB, 0x5B, 0x1C, 0x87, 0xE2, 0xE3 }};

/**
   *  @brief This function writes a value to SMNIO
   *  @param Addr - address to write
   *  @param Value
   *  @retval VOIDs
   */
VOID
SmnioWrite32 (
  IN  UINTN   Addr,
  IN  UINT32  Value
  )
{
  UINT32 Val = Value;
  UINT32 Skt1FchBus;

  Skt1FchBus = PcdGet8(PcdSkt1FchBus);
  FchSmnWrite (Skt1FchBus, (UINT32)Addr, &Val, NULL);
}

/**
   *  @brief This function reads a value from SMNIO
   *  @param addr - address to read
   *  @retval UINT32 : value of the address
   */
UINT32
SmnioRead32 (
  IN  UINTN   Addr
  )
{
  UINT32 Value;
  UINT32 Skt1FchBus;

  Skt1FchBus = PcdGet8(PcdSkt1FchBus);
  FchSmnRead (Skt1FchBus, (UINT32)Addr, &Value, NULL);
  return Value;
}

/**
   *  @brief This function writes a value to MMIO
   *  @param Addr - address to write
   *  @param Value
   *  @retval VOIDs
   */
VOID
EFIAPI
I2cMmioWrite32 (
  IN  UINTN   Addr,
  IN  UINT32  Value
  )
{
  MmioWrite32(Addr, Value);
}


/**
   *  @brief Entry point of the AMD I2cMaster PEI driver
   *  @details Perform the configuration init, resource reservation, early post init
   *  and publish the PPI
   *  @param[in] FileHandle - EFI File Handle for the PEI driver
   *  @param[in] **PeiServices - pointer to PEI services
   *  @returns EFI_STATUS
   *  @retval EFI_SUCCESS : Module initialized successfully
   *          EFI_ERROR   : Initialization failed (see error for more details)
   */
EFI_STATUS
AmdI2cMasterPeiInit (
  IN  EFI_PEI_FILE_HANDLE     FileHandle,
  IN  CONST EFI_PEI_SERVICES  **PeiServices
  )
{
  EFI_STATUS                             Status;
  UINT32                                 Index;
  UINT32                                 SocketNum;
  UINT32                                 ControllerNum;
  UINTN                                  NumOfSockets;
  UINT8                                  I2cControllerCount;
  UINT8                                  NumOfSupportedSockets;
  ROOT_BRIDGE_LOCATION                   FchRb = {0};
  EFI_PEI_PPI_DESCRIPTOR                 *PpiListI2c;
  FCH_EFI_PEI_I2C_MASTER_PPI             *FchI2cController;
  EFI_I2C_CONTROLLER_CAPABILITIES        *I2cControllerCapabilities;
  UINTN                                  Skt1FchBus = 0;

  IDS_HDT_CONSOLE (FCH_TRACE, "[Debug] AmdI2cMasterPeiInit Entry point.\n");

  NumOfSockets = FabricTopologyGetNumberOfProcessorsPresent ();
  if (NumOfSockets >= 2) {
    FabricTopologyGetSystemComponentRootBridgeLocation(PrimaryFch, &FchRb);
    Skt1FchBus = FabricTopologyGetHostBridgeBusBase(1,FchRb.Die, FchRb.Index);
  }

  IDS_HDT_CONSOLE (FCH_TRACE, "Primary FCH index =0x%x Skt1FchBus=0x%x\n",  FchRb.Index, Skt1FchBus);
  PcdSet8S (PcdSkt1FchBus, (UINT8)Skt1FchBus);

  I2cControllerCount = FchSocI2cGetControllerCount ();
  IDS_HDT_CONSOLE (FCH_TRACE, "[Info] I2cMasterPei.c NumberOfI2cControllerPerSocket: %u.\n", I2cControllerCount);

  NumOfSupportedSockets = FchSocI2cGetNumOfSupportedSockets ();
  IDS_HDT_CONSOLE (FCH_TRACE, "[Info] I2cMasterPei.c NumOfSockets: %u, MaxNumOfSupportedSockets: %u.\n", NumOfSockets, NumOfSupportedSockets);

  if (NumOfSockets > NumOfSupportedSockets) {
    IDS_HDT_CONSOLE (FCH_TRACE, "[Warning] I2cMasterPei.c NumOfSockets (%u) is greater than number of supported sockets (%u).\n", NumOfSockets, NumOfSupportedSockets);
    NumOfSockets = NumOfSupportedSockets;
  }

  //
  // Create the PPIs
  //
  Status = (*PeiServices)->AllocatePool (
                             PeiServices,
                             NumOfSockets * I2cControllerCount * sizeof (FCH_EFI_PEI_I2C_MASTER_PPI),
                             (VOID **) &FchI2cController
                             );
  if ( Status != EFI_SUCCESS ) {
    IDS_HDT_CONSOLE (EFI_D_ERROR, "[Warning] AllocatePool for FchI2cController failed (Status: 0x%x).\n", Status);
    ASSERT_EFI_ERROR (Status);
    return Status;
  } else {
    ZeroMem (FchI2cController, NumOfSockets * I2cControllerCount * sizeof (FCH_EFI_PEI_I2C_MASTER_PPI));
  }

  Status = (*PeiServices)->AllocatePool (
                             PeiServices,
                             sizeof (EFI_I2C_CONTROLLER_CAPABILITIES),
                             (VOID **) &I2cControllerCapabilities
                             );
  if ( Status != EFI_SUCCESS ) {
    IDS_HDT_CONSOLE (EFI_D_ERROR, "[Warning] AllocatePool for I2cControllerCapabilities failed (Status: 0x%x).\n", Status);
    ASSERT_EFI_ERROR (Status);
    return Status;
  } else {
    ZeroMem (I2cControllerCapabilities, sizeof (EFI_I2C_CONTROLLER_CAPABILITIES));
    I2cControllerCapabilities->StructureSizeInBytes = sizeof (EFI_I2C_CONTROLLER_CAPABILITIES);
    I2cControllerCapabilities->MaximumReceiveBytes  = 0xFFFFFFFF;
    I2cControllerCapabilities->MaximumTotalBytes    = 0xFFFFFFFF;
    I2cControllerCapabilities->MaximumTransmitBytes = 0xFFFFFFFF;
  }

  Status = (*PeiServices)->AllocatePool (
                             PeiServices,
                             NumOfSockets * I2cControllerCount * sizeof (EFI_PEI_PPI_DESCRIPTOR),
                             (VOID **) &PpiListI2c
                             );
  if ( Status != EFI_SUCCESS ) {
    IDS_HDT_CONSOLE (EFI_D_ERROR, "[Warning] AllocatePool for PpiListI2c failed (Status: 0x%x).\n", Status);
    ASSERT_EFI_ERROR (Status);
    return Status;
  } else {
    ZeroMem (PpiListI2c, NumOfSockets * I2cControllerCount * sizeof (EFI_PEI_PPI_DESCRIPTOR));
  }

  for (SocketNum = 0; SocketNum < NumOfSockets; SocketNum++) {
    for (ControllerNum = 0; ControllerNum < I2cControllerCount; ControllerNum++) {
      Index = SocketNum * I2cControllerCount + ControllerNum;
      FchI2cController[Index].I2cPpi.SetBusFrequency            = SetBusFrequency;
      FchI2cController[Index].I2cPpi.Reset                      = Reset;
      FchI2cController[Index].I2cPpi.StartRequest               = StartRequest;
      FchI2cController[Index].I2cPpi.I2cControllerCapabilities  = I2cControllerCapabilities;
      FchI2cController[Index].I2cBaseAddress                    = FchSocI2cGetBaseAddress (Index);
      FchI2cController[Index].I2cControllerInitialized          = 0x00;
      FchI2cController[Index].ControllerNum                     = Index;

      CopyGuid (&FchI2cController[Index].I2cPpi.Identifier, &AmdI2cMasterID);

      // Check if the I2C controller is enabled by PCD
      if (FchSocI2cIsControllerEnabled (Index)) {
        FchI2cController[Index].I2cControllerInitialized = 0x01;

        if (SocketNum == 0) {
          FchI2cController[Index].RxFifoDepth = I2cGetRxTxFifoDepth (FchI2cController[Index].I2cBaseAddress, 0, MmioRead32);
          FchI2cController[Index].TxFifoDepth = I2cGetRxTxFifoDepth (FchI2cController[Index].I2cBaseAddress, 1, MmioRead32);
        } else {
          FchI2cController[Index].RxFifoDepth = I2cGetRxTxFifoDepth (FchI2cController[Index].I2cBaseAddress, 0, SmnioRead32);
          FchI2cController[Index].TxFifoDepth = I2cGetRxTxFifoDepth (FchI2cController[Index].I2cBaseAddress, 1, SmnioRead32);
        }
      }

      PpiListI2c[Index].Guid  = &gEfiPeiI2cMasterPpiGuid;
      PpiListI2c[Index].Ppi   = &(FchI2cController[Index].I2cPpi);
      PpiListI2c[Index].Flags = (Index != (UINT8) (NumOfSockets * I2cControllerCount - 1)) ?
                                  EFI_PEI_PPI_DESCRIPTOR_PPI :
                                  EFI_PEI_PPI_DESCRIPTOR_PPI | EFI_PEI_PPI_DESCRIPTOR_TERMINATE_LIST;

      IDS_HDT_CONSOLE (
        FCH_TRACE,
        "[Info] I2C controller (SocketNum: %d, ControllerNum: %d) init %d\n",
        SocketNum,
        ControllerNum,
        FchI2cController[Index].I2cControllerInitialized
        );
    }
  }

  //
  // Publish the PPI
  //
  Status = (*PeiServices)->InstallPpi (PeiServices, PpiListI2c);

  if (Status != EFI_SUCCESS) {
    IDS_HDT_CONSOLE (FCH_TRACE, "[Warning] I2C controller InstallPpi failed (Status: 0x%x).\n", Status);
  }
  Status = (**PeiServices).NotifyPpi (PeiServices, &mFChI2CInitCallback);

  return (Status);
}


/**
   *  @brief Set Bus Frequency
   *  @param[in] *This - EFI_PEI_I2C_MASTER_PPI table
   *  @param[in] *BusClockHertz - pointer to the BUS Clock Hertz
   *  @returns EFI_STATUS
   *  @retval EFI_SUCCESS : Set Bus Frequency successfully
   *          EFI_ERROR   : Failed (see error for more details)
   */
EFI_STATUS
EFIAPI
SetBusFrequency (
  IN  EFI_PEI_I2C_MASTER_PPI    *This,
  IN  UINTN                     *BusClockHertz
  )
{
  EFI_STATUS    Status = EFI_SUCCESS;
  FCH_EFI_PEI_I2C_MASTER_PPI *Private;
  UINT32  settings;
  UINT32  Base;
  UINT8   IsOnMainSocket;
  UINT32  hcnt;
  UINT32  lcnt;

  I2cRegisterRead32 I2cRegRead32;
  I2cRegisterWrite32 I2cRegWrite32;

  settings = 0;

  Private = (FCH_EFI_PEI_I2C_MASTER_PPI*)This;

  // Check if the controller is initialied.
  if (Private->I2cControllerInitialized == 0x00) {
    return EFI_DEVICE_ERROR;
  }

  // Get the base address for the controller
  Base = Private->I2cBaseAddress;

  // Get this controller is in Socket 0 or 1
  IsOnMainSocket = ((Base & 0xFFFF0000) == 0xFEDC0000) ? 0x01 : 0x00;

  if (IsOnMainSocket == 0x01) {
    I2cRegRead32 = (I2cRegisterRead32) MmioRead32;
    I2cRegWrite32 = I2cMmioWrite32;
  } else {
    I2cRegRead32 = (I2cRegisterRead32) SmnioRead32;
    I2cRegWrite32 = (I2cRegisterWrite32) SmnioWrite32;
  }

  // Disable the interface
  I2cRegWrite32 (Base + DW_IC_ENABLE, 0);
  if (I2cDwWaitI2cEnable (Base, CHECK_IC_EN_HIGH, I2cRegRead32)) {
    return EFI_NOT_READY;
  }

  settings |= DW_I2C_CON_MASTER_MODE | DW_I2C_CON_IC_SLAVE_DISABLE;

  if (HS_SPEED <= *BusClockHertz) {
    settings |= DW_I2C_CON_SPEED_HS;
    //*BusClockHertz = FS_SPEED;        //Return actually clock setting
    *BusClockHertz = HS_SPEED;
  }
  else if (FS_SPEED <= *BusClockHertz) {
    settings |= DW_I2C_CON_SPEED_FS;
    *BusClockHertz = FS_SPEED;        //Return actually clock setting
  }
  else {
    settings |= DW_I2C_CON_SPEED_SS;
    *BusClockHertz = SS_SPEED;        //Return actually clock setting
  }

  settings |= DW_I2C_CON_IC_RESTART_EN;

  I2cRegWrite32 (Base + DW_IC_CON, settings);

  // Setup spike suppression for SS and FS at 50ns
  I2cRegWrite32 (Base + DW_IC_FS_SPKLEN, configI2C_FS_GLITCH_FILTER);

  // Setup spike suppression for HS at 10ns
  I2cRegWrite32 (Base + DW_IC_FS_SPKLEN, configI2C_FS_GLITCH_FILTER);

  // Standard-mode 100Khz
  hcnt = AMD_SS_SCL_HCNT;
  lcnt = AMD_SS_SCL_LCNT;
  I2cRegWrite32 (Base + DW_IC_SS_SCL_HCNT, hcnt); // std speed high, 4us
  I2cRegWrite32 (Base + DW_IC_SS_SCL_LCNT, lcnt); // std speed low, 4.7us

  IDS_HDT_CONSOLE (FCH_TRACE, "[Debug] Standard-mode HCNT:LCNT = %d:%d\n", hcnt, lcnt);

  // Fast-mode 400Khz
  hcnt = AMD_FS_SCL_HCNT;
  lcnt = AMD_FS_SCL_LCNT;
  I2cRegWrite32 (Base + DW_IC_FS_SCL_HCNT, hcnt);
  I2cRegWrite32 (Base + DW_IC_FS_SCL_LCNT, lcnt);
  I2cRegWrite32 (Base + DW_IC_HS_SCL_HCNT, hcnt);
  I2cRegWrite32 (Base + DW_IC_HS_SCL_LCNT, lcnt);

  IDS_HDT_CONSOLE (FCH_TRACE, "[Debug] Fast-mode HCNT:LCNT = %d:%d\n", hcnt, lcnt);

  return Status;
}

/**
   *  @brief Reset
   *  @param[in] *This - EFI_PEI_I2C_MASTER_PPI table
   *  @returns EFI_STATUS
   *  @retval EFI_UNSUPPORTED :
   */
EFI_STATUS
EFIAPI
Reset (
  IN CONST EFI_PEI_I2C_MASTER_PPI   *This
  )
{
   return EFI_UNSUPPORTED;
}

/**
   *  @brief Start request
   *  @param[in] *This - EFI_PEI_I2C_MASTER_PPI table
   *  @param[in] SlaveAddress -
   *  @param[in] *RequestPacket - EFI_I2C_REQUEST_PACKET table
   *  @returns EFI_STATUS
   *  @retval EFI_SUCCESS :
   */
EFI_STATUS
EFIAPI
StartRequest (
  IN  CONST EFI_PEI_I2C_MASTER_PPI      *This,
  IN  UINTN                             SlaveAddress,
  IN  EFI_I2C_REQUEST_PACKET            *RequestPacket
  )
{
  EFI_STATUS Status;
  FCH_EFI_PEI_I2C_MASTER_PPI *Private;
  UINT32  Base;
  UINT8   IsOnMainSocket;
  UINT32  Index;
  UINTN   OperationCount;
  EFI_I2C_OPERATION *Operation;

  I2cRegisterRead32 I2cRegRead32;
  I2cRegisterWrite32 I2cRegWrite32;

  Private = (FCH_EFI_PEI_I2C_MASTER_PPI*)This;

  if (Private->I2cControllerInitialized == 0x00) {
    return EFI_DEVICE_ERROR;
  }

  // Get the base address of the controller
  Base = Private->I2cBaseAddress;
  // Check if the controller base address is main Socket
  IsOnMainSocket = ((Base & 0xFFFF0000) == 0xFEDC0000) ? 0x01 : 0x00;

  if (IsOnMainSocket == 0x01) {
    I2cRegRead32 = (I2cRegisterRead32) MmioRead32;
    I2cRegWrite32 = I2cMmioWrite32;
  } else {
    I2cRegRead32 = (I2cRegisterRead32) SmnioRead32;
    I2cRegWrite32 = (I2cRegisterWrite32) SmnioWrite32;
  }

  //
  // I2cAccess for Read/Write data
  //
  Operation = RequestPacket->Operation;
  OperationCount = RequestPacket->OperationCount;

  if (OperationCount == 0x00) {
    return EFI_UNSUPPORTED;
  }

  for (Index=0; Index < OperationCount; Index++) {
    if (Operation[Index].LengthInBytes == 0x00) {
      // We do not support quick read/write
      return EFI_UNSUPPORTED;
    } else if (Operation[Index].Flags & (I2C_FLAG_SMBUS_PEC | I2C_FLAG_SMBUS_PROCESS_CALL)) {
      // No PEC, ProcessCall and BlkProcessCall either
      return Status = EFI_UNSUPPORTED;
    }
  }

  //Set target device slave address
  if (I2cSetTarget ((UINT32)SlaveAddress, Base, I2cRegRead32, I2cRegWrite32) != EFI_SUCCESS) {
    return EFI_DEVICE_ERROR;
  }

  if (I2cDwWaitBusNotBusy (Base, I2cRegRead32)) {
    return EFI_NOT_READY;
  }

  I2cRegWrite32 (Base + DW_IC_INTR_MASK, 0);
  (VOID)I2cRegRead32 (Base + DW_IC_CLR_INTR);

  IDS_HDT_CONSOLE (FCH_TRACE, "[Debug] Enable I2cInterface\n");
  // Enable the interface
  I2cRegWrite32 (Base + DW_IC_ENABLE, DW_I2C_ENABLE);
  if (I2cDwWaitI2cEnable (Base, CHECK_IC_EN_LOW, I2cRegRead32)) {
    return EFI_NOT_READY;
  }

  for (Index=0; Index < OperationCount; Index++) {
    if (Operation[Index].Flags == 0x00) {
      // Write operation
      //  - if OperationCount == 2, it is 2 step write-and-read. We do not STOP on write operation in this case.
      IDS_HDT_CONSOLE (FCH_TRACE, "[Debug] I2cMasterDxe.c StartRequest Operation (Write) Index: %d.\n", Index);
      Status = I2cPrivateWrite (Base, Operation[Index].Buffer, Operation[Index].LengthInBytes, Private->TxFifoDepth, OperationCount, I2cRegRead32, I2cRegWrite32);
    } else {
      // Read operation
      IDS_HDT_CONSOLE (FCH_TRACE, "[Debug] I2cMasterDxe.c StartRequest Operation (Read) Index: %d.\n", Index);
      Status = I2cPrivateRead (Base, Operation[Index].Buffer, Operation[Index].LengthInBytes, Private->RxFifoDepth, I2cRegRead32, I2cRegWrite32);
    }

    if (Status != EFI_SUCCESS) {
      return Status;
    }
  }

  // Disable the interface
  IDS_HDT_CONSOLE (FCH_TRACE, "[Debug] Disable I2cInterface\n");
  I2cRegWrite32 (Base + DW_IC_ENABLE, 0);
  I2cDwWaitI2cEnable (Base, CHECK_IC_EN_HIGH, I2cRegRead32);  //Wait controller status change

  return Status;
}


EFI_STATUS
EFIAPI
FchI2CInitCallback (
  IN  EFI_PEI_SERVICES                **PeiServices,
  IN  EFI_PEI_NOTIFY_DESCRIPTOR       *NotifyDesc,
  IN  VOID                            *InvokePpi
  )
{
  EFI_STATUS                             Status = EFI_SUCCESS;
  UINTN                                  NumOfSockets;
  UINT8                                  I2cControllerCount;
  UINT8                                  NumOfSupportedSockets;
  UINT8                                  i,j;
  UINT8                                  I2CControllIndex;
  EFI_PEI_I2C_MASTER_PPI                 *I2cMaster;
  FCH_EFI_PEI_I2C_MASTER_PPI             *I2cPrivate;
  I2C_BUS_CLEAR_DATA                     I2cBusClearData = {0};
  I2C_BUS_CLEAR_DATA                     *I2cBusClearDataPtr = NULL;
  ROOT_BRIDGE_LOCATION                   FchRb = {0};
  UINTN                                  Skt1FchBus = 0;

  IDS_HDT_CONSOLE (FCH_TRACE, "%a ent\n", __FUNCTION__);
  //check the totoal I2Cc controller counts
  NumOfSockets = FabricTopologyGetNumberOfProcessorsPresent ();
  I2cControllerCount = FchSocI2cGetControllerCount ();
  NumOfSupportedSockets = FchSocI2cGetNumOfSupportedSockets ();
  if (NumOfSockets > NumOfSupportedSockets)
  {
    NumOfSockets = NumOfSupportedSockets;
  }
  if (NumOfSockets >= 2) {
    FabricTopologyGetSystemComponentRootBridgeLocation(PrimaryFch, &FchRb);
    Skt1FchBus = FabricTopologyGetHostBridgeBusBase(1,FchRb.Die, FchRb.Index);
    IDS_HDT_CONSOLE (FCH_TRACE, "Skt1FchBus=0x%x\n", Skt1FchBus);
    PcdSet8S (PcdSkt1FchBus, (UINT8)Skt1FchBus);
  }

  //get every I2C master ppi instance and private data for base, frequence , SDA hold time
  for ( i  = 0; i < NumOfSockets; i++) {
    for ( j = 0;  j <  I2cControllerCount ; j++) {
      // Check if the I2C controller is enabled by PCD
      if (FchSocI2cIsControllerEnabled (i * I2cControllerCount + j) == 1) {
        continue;
      }

      I2CControllIndex = (UINT8)( j+(i*I2cControllerCount) );
       Status = (*PeiServices)->LocatePpi ( (CONST EFI_PEI_SERVICES **)PeiServices, &gEfiPeiI2cMasterPpiGuid, I2CControllIndex, NULL,(VOID **)&I2cMaster );
       if ( EFI_ERROR(Status)){
         IDS_HDT_CONSOLE (FCH_TRACE, "failed locate I2CMasterPpi[%d] Status:%r\n", I2CControllIndex, Status);
         continue;
       }
       I2cPrivate = (FCH_EFI_PEI_I2C_MASTER_PPI*)I2cMaster;
       //if the parameter has been updated, re-run I2C init
        if ( I2cPrivate->I2cSdaHoldTime != FchSocI2cSetSdaHoldTime (j)
           || I2cPrivate->I2cBusFrequency != FchSocI2cSetBusFrequency (j)
                                                                         )
        {
            IDS_HDT_CONSOLE (
              FCH_TRACE,
              "I2CMaster[%d]: org SDA hold time=0x%x, BusFreq=0x%x  new SDA hold time=0x%x, BusFreq=0x%x \n",
              I2CControllIndex,
              I2cPrivate->I2cSdaHoldTime,
              I2cPrivate->I2cBusFrequency,
              FchSocI2cSetSdaHoldTime (j),
              FchSocI2cSetBusFrequency (j)
              );

          I2cPrivate->I2cSdaHoldTime = FchSocI2cSetSdaHoldTime (j);
          I2cPrivate->I2cBusFrequency = FchSocI2cSetBusFrequency (j);
          if ( TRUE == FchSocI2cIsSupportBusClearFeature(j) ) {
             I2cBusClearData.SCLStuckLowTime = FchSocI2cSetSCLStuckTime (j);
             I2cBusClearData.SDAStuckLowTime = FchSocI2cSetSDAStuckTime (j);
             I2cBusClearDataPtr = &I2cBusClearData;
          } else {
             I2cBusClearDataPtr = NULL;
          }
          // re-run I2C init
          if ( i == 0 ){
            // main sokcet
            Status = I2cInit (
              I2cPrivate->I2cBaseAddress,
              I2cPrivate->I2cBusFrequency,
              I2cPrivate->I2cSdaHoldTime,
              MmioRead32,
              I2cMmioWrite32,
              I2cBusClearDataPtr
              );
          }
          else {
            Status = I2cInit (
              I2cPrivate->I2cBaseAddress,
              I2cPrivate->I2cBusFrequency,
              I2cPrivate->I2cSdaHoldTime,
              SmnioRead32,
              SmnioWrite32,
              I2cBusClearDataPtr
              );
          }

           IDS_HDT_CONSOLE (FCH_TRACE, "I2CMaster[%d]: re-run I2Cinit Status:%r\n", I2CControllIndex, Status);
           if (Status == EFI_SUCCESS) {
             I2cPrivate->I2cControllerInitialized = 0x01;
           } else {
             I2cPrivate->I2cControllerInitialized = 0;
           }
        }
    }
  }

  //install I2C master post ppi
  Status = (*PeiServices)->InstallPpi ((CONST EFI_PEI_SERVICES **)PeiServices, &mI2cMasterPost);
  IDS_HDT_CONSOLE (FCH_TRACE, "install I2CMasterPost Status:%r\n", Status);
  return EFI_SUCCESS;
}
