/*
 ******************************************************************************
 *
 * Copyright (C) 2008-2024 Advanced Micro Devices, Inc. All rights reserved.
 *
 *******************************************************************************
 */

/*----------------------------------------------------------------------------------------
 *                             M O D U L E S    U S E D
 *----------------------------------------------------------------------------------------
 */
#include <PiPei.h>
#include "AGESA.h"
#include "Filecode.h"
#include <Library/BaseLib.h>
#include <Library/IdsLib.h>
#include <Library/PciLib.h>
#include <Library/CcxRolesLib.h>
#include <Library/AmdPspBaseLibV2.h>
#include <Library/AmdPspRegBaseLib.h>
#include <Library/PeiServicesTablePointerLib.h>
#include <Library/FabricResourceManagerLib.h>
#include <Ppi/FabricResourceManagerServicesPpi.h>


#define FILECODE LIBRARY_AMDPSPBARINITLIBV2_AMDPSPBARINITLIBV2_FILECODE

/*----------------------------------------------------------------------------------------
 *                   D E F I N I T I O N S    A N D    M A C R O S
 *----------------------------------------------------------------------------------------
 */
#define PSP_BAR_SIZE            0x100000ul                        ///< Size of PSP BAR

#define NB_SMN_INDEX_2_PCI_ADDR (MAKE_SBDFO (0, 0, 0, 0, 0xB8))   ///< PCI Addr of NB_SMN_INDEX_2
#define NB_SMN_DATA_2_PCI_ADDR  (MAKE_SBDFO (0, 0, 0, 0, 0xBC))   ///< PCI Addr of NB_SMN_DATA_2

/*----------------------------------------------------------------------------------------
 *                  T Y P E D E F S     A N D     S T R U C T U R E S
 *----------------------------------------------------------------------------------------
 */


/*----------------------------------------------------------------------------------------
 *           P R O T O T Y P E S     O F     L O C A L     F U N C T I O N S
 *----------------------------------------------------------------------------------------
 */

/**
 * @brief Psp Bar initialize
 */
VOID
PspBarInitEarlyV2 (
  VOID
  )
{
  EFI_STATUS                           Status;
  UINT32                               Value32;
  UINT64                               PspMmioBase;
  UINTN                                PciAddress;
  UINT64                               Length;
  FABRIC_TARGET                        MmioTarget = {0};
  FABRIC_MMIO_ATTRIBUTE                Attributes;
  UINT32                               SmnBase;
  UINT8                                RbNumber;
  CONST EFI_PEI_SERVICES               **PeiServices;
  FABRIC_RESOURCE_MANAGER_PPI          *FabricResourceMgrPpi;

  Status                = EFI_SUCCESS;
  Value32               = 0;
  SmnBase               = 0;
  RbNumber              = 0;
  FabricResourceMgrPpi  = NULL;

  if ((CcxIsBsp (NULL) == FALSE)) {
    return;
  }

  //Check if PSP BAR has been assigned, if not do the PSP BAR initialation
  if (GetPspMmioBase (&Value32) == TRUE) {
    return;
  }

  GetPspIOHCxNbMiscSmnBase (&SmnBase, &RbNumber);

  //Allocate MMIO Region from MMIO manager
  Length                  = PSP_BAR_SIZE;
  MmioTarget.PciBusNum    = 0;
  MmioTarget.TgtType      = TARGET_RB;
  MmioTarget.SocketNum    = 0;
  MmioTarget.RbNum        = RbNumber;
  MmioTarget.PciSegNum    = 0;
  Attributes.ReadEnable   = 1;
  Attributes.WriteEnable  = 1;
  Attributes.NonPosted    = 0;
  Attributes.MmioType     = NON_PCI_DEVICE_BELOW_4G;
  PspMmioBase             = 0;

  Status      = EFI_OUT_OF_RESOURCES;
  PeiServices = GetPeiServicesTablePointer();
  if (PeiServices != NULL) {
    Status = (*PeiServices)->LocatePpi (
                              PeiServices,
                              &gAmdFabricResourceManagerServicesPpiGuid,
                              0,
                              NULL,
                              (VOID **) &FabricResourceMgrPpi
                              );

    if (!EFI_ERROR (Status)) {
      Status = FabricResourceMgrPpi->FabricAllocateMmio (
                                       &PspMmioBase,
                                       &Length,
                                       ALIGN_1M,
                                       MmioTarget,
                                       &Attributes
                                       );
      IDS_HDT_CONSOLE_PSP_TRACE ("[%a] Get PspMmioBase from FabricResourceMgrPpi Status: %r\n",
        __FUNCTION__, Status);
    } else {
      IDS_HDT_CONSOLE_PSP_TRACE ("[%a] Locate FabricResourceMgrPpi Fail\n", __FUNCTION__);
    }
  }

  //
  // If locate FabricResourceManagerServicesPpi failure, use FabricAllocateMmio to get PspMmioBase again.
  // If the FabricAllocateMmio library does not map the correct libraries, the system may experience a hang problem.
  // Please make sure FabricResourceManagerLib was linked correctly in the program.
  //
  if (EFI_ERROR(Status)) {
    Status = FabricAllocateMmio (
               &PspMmioBase,
               &Length,
               ALIGN_1M,
               MmioTarget,
               &Attributes
               );
    IDS_HDT_CONSOLE_PSP_TRACE ("[%a] Get PspMmioBase from FabricAllocateMmio Status: %r\n",
      __FUNCTION__, Status);
  }

  if (EFI_ERROR (Status)) {
    IDS_HDT_CONSOLE_PSP_TRACE ("[%a] Allocate MMIO Fail\n", __FUNCTION__);
    ASSERT (FALSE);
    return;
  } else {
    IDS_HDT_CONSOLE_PSP_TRACE ("[%a] Allocate MMIO @0x%lx\n", __FUNCTION__, PspMmioBase);
  }

  //Set PSP BASE Address in NBMISC, and enable lock the MMIO
  PciAddress = NB_SMN_INDEX_2_PCI_ADDR;
  Value32    = SmnBase + NBMSIC_PSP_BASE_ADDR_LO_OFFSET;
  PciWrite32 (PciAddress, Value32);
  PciAddress = NB_SMN_DATA_2_PCI_ADDR;
  Value32    = (UINT32) (PspMmioBase | (BIT0 + BIT8));
  PciWrite32 (PciAddress, Value32);

  PciAddress = NB_SMN_INDEX_2_PCI_ADDR;
  Value32    = SmnBase + NBMSIC_PSP_BASE_ADDR_HI_OFFSET;
  PciWrite32 (PciAddress, Value32);
  PciAddress = NB_SMN_DATA_2_PCI_ADDR;
  Value32    = (UINT32) RShiftU64 (PspMmioBase, 32);
  PciWrite32 (PciAddress, Value32);
}

