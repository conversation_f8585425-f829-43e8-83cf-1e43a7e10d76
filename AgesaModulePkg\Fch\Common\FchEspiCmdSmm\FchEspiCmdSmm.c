/*****************************************************************************
 *
 * Copyright (C) 2008-2024 Advanced Micro Devices, Inc. All rights reserved.
 *
 *******************************************************************************
 */

#include "FchEspiCmdSmm.h"

#define FILECODE FCH_COMMON_FCHESPICMDSMM_FCHESPICMDSMM_FILECODE

/**
  * SmmInBandRst - eSPI In Band Reset
  *
  * @param This                   Pointer to an EFI_ESPI_CMD_PROTOCOL structure.
  * @param[in]  EspiBase          Espi MMIO base
  *
  */
VOID
EFIAPI
SmmInBandRst (
  IN CONST EFI_ESPI_CMD_PROTOCOL  *This,
  IN  UINT32                      EspiBase
  )
{
  FchEspiCmd_InBandRst(EspiBase);
}

/**
  *  SmmGetConfiguration - eSPI Get Configuration
  *
  * @param This                   Pointer to an EFI_ESPI_CMD_PROTOCOL structure.
  * @param[in]  EspiBase          Espi MMIO base
  * @param[in]  RegAddr           Slave register address
  *
  * @retval    Register Value
  *
  */
UINT32
EFIAPI
SmmGetConfiguration (
  IN CONST EFI_ESPI_CMD_PROTOCOL  *This,
  IN  UINT32                      EspiBase,
  IN  UINT32                      RegAddr
 )
{
  return (FchEspiCmd_GetConfiguration(EspiBase, RegAddr));
}

/**
  * SmmSetConfiguration - eSPI Set Configuration
  *
  * @param This                   Pointer to an EFI_ESPI_CMD_PROTOCOL structure.
  * @param[in]  EspiBase          Espi MMIO base
  * @param[in]  RegAddr           Slave register address
  * @param[in]  Value             Slave register value
  *
  */
VOID
EFIAPI
SmmSetConfiguration (
  IN CONST EFI_ESPI_CMD_PROTOCOL  *This,
  IN  UINT32                      EspiBase,
  IN  UINT32                      RegAddr,
  IN  UINT32                      Value
)
{
  FchEspiCmd_SetConfiguration(EspiBase, RegAddr, Value);
}

/**
  * SmmSafsFlashRead - eSPI SAFS FLASH Read
  *
  * @param This                   Pointer to an EFI_ESPI_CMD_PROTOCOL structure.
  * @param[in]  EspiBase          Espi MMIO base
  * @param[in]  Address           Address to read
  * @param[in]  Length            Length in byte to read
  * @param[in]  Buffer            Pointer to the data read to
  *
  * @retval EFI_STATUS
  *
  */
EFI_STATUS
EFIAPI
SmmSafsFlashRead (
  IN CONST EFI_ESPI_CMD_PROTOCOL  *This,
  IN  UINT32                      EspiBase,
  IN  UINT32                      Address,
  IN  UINT32                      Length,
  OUT UINT8                       *Buffer
  )
{
  return (FchEspiCmd_SafsFlashRead(EspiBase, Address, Length, Buffer));
}

/**
  * SmmSafsFlashWrite - eSPI SAFS FLASH Write
  *
  * @param  This                  Pointer to an EFI_ESPI_CMD_PROTOCOL structure.
  * @param[in]  EspiBase          Espi MMIO base
  * @param[in]  Address           Address to write
  * @param[in]  Length            Length in byte to write
  * @param[in]  Value             Pointer to the data to write
  *
  * @retval EFI_STATUS
  *
  */
EFI_STATUS
EFIAPI
SmmSafsFlashWrite (
  IN CONST EFI_ESPI_CMD_PROTOCOL  *This,
  IN  UINT32                      EspiBase,
  IN  UINT32                      Address,
  IN  UINT32                      Length,
  IN  UINT8                       *Value
  )
{
  return (FchEspiCmd_SafsFlashWrite(EspiBase, Address, Length, Value));
}

/**
  * SmmSafsFlashErase - eSPI SAFS FLASH Erase
  *
  * @param  This                  Pointer to an EFI_ESPI_CMD_PROTOCOL structure.
  * @param[in]  EspiBase          Espi MMIO base
  * @param[in]  Address           Address to erase
  * @param[in]  Length            Block Size to erase
  *
  *  @retval EFI_STATUS
  *
  */
EFI_STATUS
EFIAPI
SmmSafsFlashErase (
  IN CONST EFI_ESPI_CMD_PROTOCOL  *This,
  IN  UINT32                      EspiBase,
  IN  UINT32                      Address,
  IN  UINT32                      Length
  )
{
  return (FchEspiCmd_SafsFlashErase(EspiBase, Address, Length));
}

/**
  * SmmSafsRpmcOp1 - eSPI SAFS RPMC OP1
  *
  * @param  This                  Pointer to an EFI_ESPI_CMD_PROTOCOL structure.
  * @param[in]  EspiBase          Espi MMIO base
  * @param[in]  RpmcFlashDev      Which RPMC flash device is targeted to
  * @param[in]  Length            Length in byte
  * @param[in]  Data              Pointer to data to send
  *
  *  @retval EFI_STATUS
  *
  */
EFI_STATUS
EFIAPI
SmmSafsRpmcOp1 (
  IN CONST EFI_ESPI_CMD_PROTOCOL  *This,
  IN  UINT32                      EspiBase,
  IN  UINT8                       RpmcFlashDev,
  IN  UINT32                      Length,
  IN  UINT8                       *Data
  )
{
  return (FchEspiCmd_SafsRpmcOp1(EspiBase, RpmcFlashDev, Length, Data));
}

/**
  * SmmSafsRpmcOp2 - eSPI SAFS RPMC OP2
  *
  * @param  This                  Pointer to an EFI_ESPI_CMD_PROTOCOL structure.
  * @param[in]  EspiBase          Espi MMIO base
  * @param[in]  RpmcFlashDev      Which RPMC flash device is targeted to
  * @param[in]  Length            Length in byte
  * @param[in]  Buffer            Pointer to buffer to receive
  *
  *  @retval EFI_STATUS
  *
  */
EFI_STATUS
EFIAPI
SmmSafsRpmcOp2 (
  IN CONST EFI_ESPI_CMD_PROTOCOL  *This,
  IN  UINT32                      EspiBase,
  IN  UINT8                       RpmcFlashDev,
  IN  UINT32                      Length,
  IN  UINT8                       *Buffer
  )
{
  return (FchEspiCmd_SafsRpmcOp2(EspiBase, RpmcFlashDev, Length, Buffer));
}

EFI_ESPI_CMD_PROTOCOL SmmFchEspiCmdProt = {
  SmmInBandRst,
  SmmGetConfiguration,
  SmmSetConfiguration,
  SmmSafsFlashRead,
  SmmSafsFlashWrite,
  SmmSafsFlashErase,
  SmmSafsRpmcOp1,
  SmmSafsRpmcOp2
};

/**
  *  @brief Entry point of the AMD FCH eSPI Command SMM driver
  *  @details Install supported protocol
  *  @param[in] ImageHandle - EFI Image Handle for the SMM driver
  *  @param[in] SystemTable - pointer to the EFI system table
  *  @returns EFI_STATUS
  *  @retval EFI_SUCCESS : Module initialized successfully
  *          EFI_ERROR   : Initialization failed (see error for more details)
  */
EFI_STATUS
EFIAPI
AmdFchEspiCmdSmmInit (
  IN  EFI_HANDLE        ImageHandle,
  IN  EFI_SYSTEM_TABLE  *SystemTable
  )
{
  EFI_STATUS            Status;
  EFI_HANDLE            Handle;

  DEBUG ((EFI_D_ERROR, "FchEspiCmdSmm entry point: AmdFchEspiCmdSmmInit.\n"));


  // Install Protocol
  Handle = NULL;
  Status = gSmst->SmmInstallProtocolInterface (
                  &Handle,
                  &gAmdFchEspiCmdSmmProtocolGuid,
                  EFI_NATIVE_INTERFACE,
                  &SmmFchEspiCmdProt
                  );

    if (Status != EFI_SUCCESS) {
      DEBUG ((EFI_D_ERROR, "[Warning] InstallProtocolInterface failed (Status: 0x%x).\n", Status));
    }
  return (Status);
}

