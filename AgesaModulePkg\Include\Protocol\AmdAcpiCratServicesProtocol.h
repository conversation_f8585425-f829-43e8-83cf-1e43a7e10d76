/*****************************************************************************
 * Copyright (C) 2008-2024 Advanced Micro Devices, Inc. All rights reserved.
 *
*****************************************************************************
*/
/* $NoKeywords:$ */
/**
 * @file
 *
 * AMD CRAT Services Protocol prototype definition
 *
 *
 * @xrefitem bom "File Content Label" "Release Content"
 * @e project:      AGESA
 * @e sub-project:  Library
 * @e \$Revision: 313706 $   @e \$Date: 2015-02-25 21:00:43 -0600 (Wed, 25 Feb 2015) $
 */
#ifndef _AMD_ACPI_CRAT_SERVICES_PROTOCOL_H_
#define _AMD_ACPI_CRAT_SERVICES_PROTOCOL_H_
#include <Gnb.h>

#pragma pack (push, 1)

/*----------------------------------------------------------------------------------------
 *                    T Y P E D E F S     A N D     S T R U C T U R E S
 *                            Component Resource Affinity Table
 *----------------------------------------------------------------------------------------
 */

/// L1 cache.
#define L1_CACHE 1

/// L2 cache.
#define L2_CACHE 2

/// L3 cache.
#define L3_CACHE 3

/// Format for CRAT Header.
typedef struct {
  ACPI_TABLE_HEADER Common; ///< Common ACPI table elements
  UINT32 TotalEntries;      ///< total number[n] of entries in the CRAT.
  UINT16 NumNodes;          ///< Number of HSA nodes as defined by NUMA "memory locality".
  UINT8  Reserved[6];       ///< Reserved.
} CRAT_HEADER;

/// CRAT entry type
typedef enum {
  CRAT_HSA_PROC_UNIT_TYPE = 0,       ///< 0 - CRAT HSA Processing Unit Structure
  CRAT_MEMORY_TYPE,                  ///< 1 - CRAT Memory Affinity Structure
  CRAT_CACHE_TYPE,                   ///< 2 - CRAT Cache Affinity Structure
  CRAT_TLB_TYPE,                     ///< 3 - CRAT TLB  Affinity Structure
  CRAT_FPU_TYPE,                     ///< 4 - CRAT FPU Affinity Structure
  CRAT_IO_TYPE,                      ///< 5 - CRAT IO Affinity Structure
  CRAT_MAX_TYPE,                     ///< MaxValue
} CRAT_ENTRY_TYPE;

/// Flags field of the CRAT HSA Processing Unit Affinity Structure
typedef struct {
  UINT32 Enabled       :1;  ///< Enabled.
  UINT32 HotPluggable  :1;  ///< Hot Pluggable.
  UINT32 CpuPresent    :1;  ///< Cpu Present.
  UINT32 GpuPresent    :1;  ///< Gpu Present.
  UINT32 HSAmmuPresent :1;  ///< HSAmmu Present.
  UINT32 VALimits      :3;  ///< VALimits.
  UINT32 AtomicOps     :2;  ///< AtomicOps.
  UINT32               :22; ///< Reserved.
} CRAT_HSA_PROCESSING_UNIT_FLAG;

/// CRAT HSA Processing Unit Affinity Structure
typedef struct {
  UINT8                         Type;              ///< CRAT_HSA_PROC_UNIT_TYPE - CRAT HSA Processing Unit Structure. @see CRAT_ENTRY_TYPE
  UINT8                         Length;            ///< 40 - the size of this structure in bytes.
  UINT16                        Reserved;          ///< Reserved.
  CRAT_HSA_PROCESSING_UNIT_FLAG Flags;             ///< Flags - HSA Processing Unit Affinity Structure. @see CRAT_HSA_PROCESSING_UNIT_FLAG
  UINT32                        ProximityNode;     ///< Integer that represents the proximity node to which the node belongs to.
  UINT32                        ProcessorIdLow;    ///< Low value  of the logical processor included in this HSA proximity domain.
  UINT16                        NumCPUCores;       ///< Indicates count of CCompute execution units present in this APU node.
  UINT16                        NumSIMDCores;      ///< Indicates maximum count of HCompute SIMD cores present in this node.
  UINT16                        MaxWavesSIMD;      ///< This identifies the max. number of launched waves per SIMD.
  UINT16                        IoCount;           ///< Number of discoverable IO Interfaces connecting this node to other components.
  UINT16                        HSACapability;     ///< Must be 0.
  UINT16                        LDSSizeInKB;       ///< Size of LDS memory per SIMD Wavefront.
  UINT8                         WaveFrontSize;     ///< 64, may be 32 for some FSA based architectures.
  UINT8                         NumShaderBanks;    ///< Number of Banks or "Shader Engines", typically 1 or 2.
  UINT16                        uEngineIdentifier; ///< Identifier (Rev) of the GPU uEngine or firmware.
  UINT8                         NumArrays;         ///< Number of SIMD Arrays per Engine.
  UINT8                         NumCUPerArray;     ///< Number of Compute Units (CU) per SIMD Array.
  UINT8                         NumSIMDPerCU;      ///< Number of SIMD representing a Compute Unit.
  UINT8                         MaxSlotsScratchCU; ///< Maximum number of temporary memory ("scratch") wave slots available to access, may be 0 if HW has no restrictions.
  UINT8                         Reserved1[4];      ///< Reserved.
} CRAT_HSA_PROCESSING_UNIT;

/// Flags field of the CRAT Memory Affinity Structure
typedef struct {
  UINT32 Enabled         :1;  ///< Enabled.
  UINT32 HotPluggable    :1;  ///< Hot Pluggable.
  UINT32 NonVolatile     :1;  ///< If set, the memory region represents Non-Volatile memory.
  UINT32 AtomicOpsTarget :2;  ///< AtomicOpsTarget.
  UINT32                 :27; ///< Reserved.
} CRAT_MEMORY_FLAG;

/// CRAT Memory Affinity Structure
typedef struct {
  UINT8            Type;            ///< CRAT_MEMORY_TYPE - CRAT Memory Affinity Structure. @see CRAT_ENTRY_TYPE
  UINT8            Length;          ///< 40 - the size of this structure in bytes.
  UINT16           Reserved;        ///< Reserved.
  CRAT_MEMORY_FLAG Flags;           ///< Flags - Memory Affinity Structure. Indicates whether the region of memory is enabled and can be hot plugged. @see CRAT_MEMORY_FLAG
  UINT32           ProximityDomain; ///< Integer that represents the proximity domain to which the node belongs to.
  UINT32           BaseAddressLow;  ///< Low 32Bits of the Base Address of the memory range.
  UINT32           BaseAddressHigh; ///< High 32Bits of the Base Address of the memory range.
  UINT32           LengthLow;       ///< Low 32Bits of the length of the memory range.
  UINT32           LengthHigh;      ///< High 32Bits of the length of the memory range.
  UINT32           Width;           ///< Memory width - Specifies the number of parallel bits of the memory interface.
  UINT8            Reserved1[8];    ///< Reserved.
} CRAT_MEMORY;

/// Flags field of the CRAT Cache Affinity structure
typedef struct {
  UINT32 Enabled          :1;  ///< Enabled.
  UINT32 DataCache        :1;  ///< 1 if cache includes data.
  UINT32 InstructionCache :1;  ///< 1 if cache includes instructions.
  UINT32 CpuCache         :1;  ///< 1 if cache is part of CPU functionality.
  UINT32 SimdCache        :1;  ///< 1 if cache is part of SIMD functionality.
  UINT32 GlobalDataStore  :1;  ///< GDS is an explicitly addressed type  of GPU-on chip memory used as cache, this property must be combined with "specialty cache" attribute.
  UINT32 AtomicOpsCache   :1;  ///< Indicates if AtomicOps are supported within the cache, typically used for explicitly addressed SIMD caches (e.g. L2).
  UINT32                  :25; ///< Reserved.
} CRAT_CACHE_FLAG;

/// CRAT Cache Affinity Structure
typedef struct {
  UINT8           Type;            ///< CRAT_CACHE_TYPE - CRAT Cache Affinity Structure. @see CRAT_ENTRY_TYPE
  UINT8           Length;          ///< 64 - the size of this structure in bytes.
  UINT16          Reserved;        ///< Reserved.
  CRAT_CACHE_FLAG Flags;           ///< Flags - Cache Affinity Structure. Indicates whether the region of cache is enabled.
  UINT32          ProcessorIdLow;  ///< Low value of a logical processor which includes this component.
  UINT8           SiblingMap[32];  ///< Bitmask of  Processor Id sharing this component. 1 bit per logical processor.
  UINT32          CacheSize;       ///< Cache size in KB.
  UINT8           CacheLevel;      ///< Integer representing level: 1, 2, 3, 4, etc.
  UINT8           LinesPerTag;     ///< Cache Lines per tag.
  UINT16          CacheLineSize;   ///< Cache line size in bytes.
  UINT8           Associativity;   ///< Cache associativity. The associativity fields are encoded as follows:
                                   ///<   00h:     Reserved
                                   ///<   01h:     Direct mapped
                                   ///<   02h-FEh: Associativity (e.g., 04h = 4-way associative)
                                   ///<   FFh:     Fully associative
  UINT8           CacheProperties; ///< Cache Properties bits [2:0] represent Inclusive/Exclusive property encoded.
                                   ///<   0: Cache is strictly exclusive to lower level caches.
                                   ///<   1: Cache is mostly exclusive to lower level caches.
                                   ///<   2: Cache is strictly inclusive to lower level caches.
                                   ///<   3: Cache is mostly inclusive to lower level caches.
                                   ///<   4: Cache is a "constant cache" (= explicit update)
                                   ///<   5: Cache is a "specialty cache" (e.g. Texture cache)
                                   ///<   6-7: Reserved
                                   ///< CacheProperties bits [7:3] are reserved.
  UINT16          CacheLatency;    ///< Cost of time to access cache described in nanoseconds.
  UINT8           Reserved1[8];    ///< Reserved.
} CRAT_CACHE;

/// Flags field of the CRAT TLB Affinity structure
typedef struct {
  UINT32 Enabled        :1;  ///< Enabled.
  UINT32 DataTLB        :1;  ///< 1 if TLB includes translation information for data.
  UINT32 InstructionTLB :1;  ///< 1 if TLB includes translation information for instructions.
  UINT32 CpuTLB         :1;  ///< 1 if TLB is part of CPU functionality.
  UINT32 SimdTLB        :1;  ///< 1 if TLB is part of SIMD functionality.
  UINT32 TLB4KBase256   :1;  ///< 1 if value in table is factored by 256 to get number of 4K entries.
  UINT32                :1;  ///< Reserved.
  UINT32 TLB2MBase256   :1;  ///< 1 if value in table is factored by 256 to get number of 2M entries.
  UINT32                :1;  ///< Reserved.
  UINT32 TLB1GBase256   :1;  ///< 1 if value in table is factored by 256 to get number of 1G entries.
  UINT32                :22; ///< Reserved.
} CRAT_TLB_FLAG;

/// CRAT TLB Affinity Structure
typedef struct {
  UINT8         Type;                           ///< CRAT_TLB_TYPE - CRAT TLB Affinity Structure. @see CRAT_ENTRY_TYPE
  UINT8         Length;                         ///< 64 - the size of this structure in bytes.
  UINT16        Reserved;                       ///< Reserved.
  CRAT_TLB_FLAG Flags;                          ///< Flags - TLB Affinity Structure. Indicates whether the TLB is enabled and defined. @see CRAT_TLB_FLAG
  UINT32        ProcessorIdLow;                 ///< Low value of a logical processor which includes this component.
  UINT8         SiblingMap[32];                 ///< Bitmask of Processor Id sharing this component. 1 bit per logical processor.
  UINT32        TlbLevel;                       ///< Integer representing level: 1, 2, 3, 4, etc.
  UINT8         DataTlbAssociativity2MB;        ///< Data TLB associativity for 2MB pages. The associativity fields are encoded as follows:
                                                ///<   00h:     Reserved
                                                ///<   01h:     Direct mapped
                                                ///<   02h-FEh: Associativity (e.g., 04h = 4-way associative)
                                                ///<   FFh:     Fully associative
  UINT8         DataTlbSize2MB;                 ///< Data TLB number of entries for 2MB.
  UINT8         InstructionTlbAssociativity2MB; ///< Instruction TLB associativity for 2MB pages. The associativity fields are encoded as follows:
                                                ///<   00h:     Reserved
                                                ///<   01h:     Direct mapped
                                                ///<   02h-FEh: Associativity (e.g., 04h = 4-way associative)
                                                ///<   FFh:     Fully associative
  UINT8         InstructionTlbSize2MB;          ///< Instruction TLB number of entries for 2MB pages.
  UINT8         DTLB4KAssoc;                    ///< Data TLB Associativity for 4KB pages.
  UINT8         DTLB4KSize;                     ///< Data TLB number of entries for 4KB pages.
  UINT8         ITLB4KAssoc;                    ///< Instruction TLB Associativity for 4KB pages.
  UINT8         ITLB4KSize;                     ///< Instruction TLB number of entries for 4KB pages.
  UINT8         DTLB1GAssoc;                    ///< Data TLB Associativity for 1GB pages.
  UINT8         DTLB1GSize;                     ///< Data TLB number of entries for 1GB pages.
  UINT8         ITLB1GAssoc;                    ///< Instruction TLB Associativity for 1GB pages.
  UINT8         ITLB1GSize;                     ///< Instruction TLB number of entries for 1GB pages.
  UINT8         Reserved1[4];                   ///< Reserved.
} CRAT_TLB;

/// Flags field of the CRAT FPU Affinity structure
typedef struct {
  UINT32 Enabled :1;  ///< Enabled.
  UINT32         :31; ///< Reserved.
} CRAT_FPU_FLAG;

/// CRAT FPU Affinity Structure
typedef struct {
  UINT8         Type;           ///< CRAT_FPU_TYPE - CRAT FPU Affinity Structure. @see CRAT_ENTRY_TYPE
  UINT8         Length;         ///< 64 - the size of this structure in bytes.
  UINT16        Reserved;       ///< Reserved.
  CRAT_FPU_FLAG Flags;          ///< Flags - FPU Affinity Structure. Indicates whether the region of FPU affinity structure is enabled and defined. @see CRAT_FPU_FLAG
  UINT32        ProcessorIdLow; ///< Low value of a logical processor which includes this component.
  UINT8         SiblingMap[32]; ///< Bitmask of  Processor Id sharing this component. 1 bit per logical processor.
  UINT32        FPUSize;        ///< Product specific.
  UINT8         Reserved1[16];  ///< Reserved.
} CRAT_FPU;

/// Flags field of the CRAT IO Affinity structure
typedef struct {
  UINT32 Enabled   :1;  ///< Enabled.
  UINT32 Coherency :1;  ///< If set, IO interface supports coherent transactions (natively or through protocol extensions).
  UINT32           :30; ///< Reserved.
} CRAT_IO_FLAG;

/// CRAT IO Affinity Structure
typedef struct {
  UINT8        Type;                    ///< CRAT_IO_TYPE - CRAT IO Affinity Structure. @see CRAT_ENTRY_TYPE
  UINT8        Length;                  ///< 64 - the size of this structure in bytes.
  UINT16       Reserved;                ///< Reserved.
  CRAT_IO_FLAG Flags;                   ///< Flags - IO Affinity Structure. Indicates whether the region of IO affinity structure is enabled and defined. @see CRAT_IO_FLAG
  UINT32       ProximityDomainFrom;     ///< Integer that represents the proximity domain to which the IO Interface belongs to.
  UINT32       ProximityDomainTo;       ///< Integer that represents the other proximity domain to which the IO Interface belongs to.
  UINT8        IoType;                  ///< IO Interface type. Values defined are:
                                        ///<   0:     Undefined
                                        ///<   1:     Hypertransport
                                        ///<   2:     PCI Express
                                        ///<   3:     Other (e.g. internal)
                                        ///<   4-255: Reserved
  UINT8        VersionMajor;            ///< Major version of the Bus interface.
  UINT16       VersionMinor;            ///< Minor version of the Bus interface (optional).
  UINT32       MinimumLatency;          ///< Cost of time to transfer, described in nanoseconds.
  UINT32       MaximumLatency;          ///< Cost of time to transfer, described in nanoseconds.
  UINT32       MinimumBandwidth;        ///< Minimum interface Bandwidth in MB/s.
  UINT32       MaximumBandwidth;        ///< Maximum interface Bandwidth in MB/s.
  UINT32       RecommendedTransferSize; ///< Recommended transfer size to reach maximum interface bandwidth in Bytes.
  UINT8        Reserved1[24];           ///< Reserved.
} CRAT_IO;


/*----------------------------------------------------------------------------------------
 *                 D E F I N I T I O N S     A N D     M A C R O S
 *----------------------------------------------------------------------------------------
 */

/*----------------------------------------------------------------------------------------
 *                H S A   C A C H E   T L B
 *----------------------------------------------------------------------------------------
 */

/// Forward declaration for the AMD_CCX_ACPI_CRAT_SERVICES_PROTOCOL.
typedef struct _AMD_CCX_ACPI_CRAT_SERVICES_PROTOCOL AMD_CCX_ACPI_CRAT_SERVICES_PROTOCOL;

/**
 * @brief Retrieves CRAT information about the HSA.
 *
 * @param[in]      This                Pointer to the CCX ACPI CRAT services protocol instance. @see AMD_CCX_ACPI_CRAT_SERVICES_PROTOCOL
 * @param[in]      CratHeaderStructPtr Pointer to the CRAT table structure. @see CRAT_HEADER
 * @param[in, out] TableEnd            Point to the end of this table
 *
 * @return EFI_STATUS - status of the operation.
 */
typedef
EFI_STATUS
(EFIAPI *AMD_CRAT_SERVICES_GET_HSA_PROC_CCX_ENTRY) (
  IN     AMD_CCX_ACPI_CRAT_SERVICES_PROTOCOL *This,
  IN     CRAT_HEADER                         *CratHeaderStructPtr,
  IN OUT UINT8                              **TableEnd
  );

/**
 * @brief Retrieves information about the cache.
 *
 * @param[in]      This                Pointer to the CCX ACPI CRAT services protocol instance. @see AMD_CCX_ACPI_CRAT_SERVICES_PROTOCOL
 * @param[in]      CratHeaderStructPtr Pointer to the CRAT table structure. @see CRAT_HEADER
 * @param[in, out] TableEnd            Pointer to the end of the CRAT table.
 *
 * @return EFI_STATUS - status of the operation.
 */
typedef
EFI_STATUS
(EFIAPI *AMD_CRAT_SERVICES_GET_CACHE_ENTRY) (
  IN     AMD_CCX_ACPI_CRAT_SERVICES_PROTOCOL *This,
  IN     CRAT_HEADER                         *CratHeaderStructPtr,
  IN OUT UINT8                              **TableEnd
  );

/**
 * @brief Retrieves information about the TLB.
 *
 * @param[in]      This                Pointer to the CCX ACPI CRAT services protocol instance. @see AMD_CCX_ACPI_CRAT_SERVICES_PROTOCOL
 * @param[in]      CratHeaderStructPtr Pointer to the CRAT table structure. @see CRAT_HEADER
 * @param[in, out] TableEnd            Pointer to the end of the CRAT table.
 *
 * @return EFI_STATUS - status of the operation.
 */
typedef
EFI_STATUS
(EFIAPI *AMD_CRAT_SERVICES_GET_TLB_ENTRY) (
  IN     AMD_CCX_ACPI_CRAT_SERVICES_PROTOCOL *This,
  IN     CRAT_HEADER                         *CratHeaderStructPtr,
  IN OUT UINT8                              **TableEnd
  );

/// When installed, the CRAT Services Protocol produces a collection of services that return various information to generate CRAT.
struct _AMD_CCX_ACPI_CRAT_SERVICES_PROTOCOL {
  UINTN                                    Revision;               ///< Revision Number.
  AMD_CRAT_SERVICES_GET_HSA_PROC_CCX_ENTRY GetCratHsaProcCcxEntry; ///< @see AMD_CRAT_SERVICES_GET_HSA_PROC_CCX_ENTRY
  AMD_CRAT_SERVICES_GET_CACHE_ENTRY        GetCratCacheEntry;      ///< @see AMD_CRAT_SERVICES_GET_CACHE_ENTRY
  AMD_CRAT_SERVICES_GET_TLB_ENTRY          GetCratTlbEntry;        ///< @see AMD_CRAT_SERVICES_GET_TLB_ENTRY
};

/*----------------------------------------------------------------------------------------
 *                M E M O R Y
 *----------------------------------------------------------------------------------------
 */

/// Forward declaration for the AMD_FABRIC_ACPI_CRAT_SERVICES_PROTOCOL.
typedef struct _AMD_FABRIC_ACPI_CRAT_SERVICES_PROTOCOL AMD_FABRIC_ACPI_CRAT_SERVICES_PROTOCOL;

/**
 * @brief Adds the system's CRAT memory entries to the CRAT table and updates the table headers total entry count accordingly.
 *
 * @param[in]      This                Pointer to the Fabric ACPI CRAT services protocol instance. @see AMD_FABRIC_ACPI_CRAT_SERVICES_PROTOCOL
 * @param[in]      CratHeaderStructPtr Pointer to the CRAT table structure. @see CRAT_HEADER
 * @param[in, out] TableEnd            Pointer to the end of the CRAT table.
 *
 * @return EFI_STATUS - status of the operation.
 */
typedef
EFI_STATUS
(EFIAPI *AMD_CRAT_SERVICES_CREATE_MEMORY) (
  IN     AMD_FABRIC_ACPI_CRAT_SERVICES_PROTOCOL *This,
  IN     CRAT_HEADER                            *CratHeaderStructPtr,
  IN OUT UINT8                                 **TableEnd
  );

/// When installed, the CRAT Services Protocol produces a collection of services that return various information to generate CRAT.
struct _AMD_FABRIC_ACPI_CRAT_SERVICES_PROTOCOL {
  UINTN                           Revision;     ///< Revision Number.
  AMD_CRAT_SERVICES_CREATE_MEMORY CreateMemory; ///< @see AMD_CRAT_SERVICES_CREATE_MEMORY
};

/*----------------------------------------------------------------------------------------
 *               i G P U
 *----------------------------------------------------------------------------------------
 */

/// Forward declaration for the AMD_IGPU_ACPI_CRAT_SERVICES_PROTOCOL.
typedef struct _AMD_IGPU_ACPI_CRAT_SERVICES_PROTOCOL AMD_IGPU_ACPI_CRAT_SERVICES_PROTOCOL;

/**
 * @brief Retrieves CRAT information about the HSA.
 *
 * @param[in]      This         Pointer to the IGpu ACPI CRAT services protocol instance. @see AMD_IGPU_ACPI_CRAT_SERVICES_PROTOCOL
 * @param[in, out] CratHsaEntry Pointer to the HSA processor information structure. @see CRAT_HSA_PROCESSING_UNIT
 *
 * @return EFI_STATUS - status of the operation.
 */
typedef
EFI_STATUS
(EFIAPI *AMD_CRAT_SERVICES_GET_HSA_PROC_IGPU_ENTRY) (
  IN     AMD_IGPU_ACPI_CRAT_SERVICES_PROTOCOL *This,
  IN OUT CRAT_HSA_PROCESSING_UNIT             *CratHsaEntry
  );

/// When installed, the CRAT Services Protocol produces a collection of services that return various information to generate CRAT.
struct _AMD_IGPU_ACPI_CRAT_SERVICES_PROTOCOL {
  UINTN                                     Revision;                ///< Revision Number.
  AMD_CRAT_SERVICES_GET_HSA_PROC_IGPU_ENTRY GetCratHsaProcIGpuEntry; ///< @see AMD_CRAT_SERVICES_GET_HSA_PROC_IGPU_ENTRY
};

/// GUID for CCX ACPI CRAT services protocol.
extern EFI_GUID gAmdCcxAcpiCratServicesProtocolGuid;

/// GUID for Fabric ACPI CRAT services protocol.
extern EFI_GUID gAmdFabricAcpiCratServicesProtocolGuid;

/// GUID for IGpu ACPI CRAT services protocol.
extern EFI_GUID gAmdIGpuAcpiCratServicesProtocolGuid;

#pragma pack (pop)
#endif // _AMD_ACPI_CRAT_SERVICES_PROTOCOL_H_

