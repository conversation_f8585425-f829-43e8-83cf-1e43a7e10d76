/*****************************************************************************
 * Copyright (C) 2008-2024 Advanced Micro Devices, Inc. All rights reserved.
 *
*****************************************************************************
*/
/* $NoKeywords:$ */
/**
 * @file
 *
 * Config Fch Xhci controller
 *
 * Init Xhci Controller features (PEI phase).
 *
 * @xrefitem bom "File Content Label" "Release Content"
 * @e project:     AGESA
 * @e sub-project: FCH
 * @e \$Revision: 309083 $   @e \$Date: 2014-12-09 09:28:24 -0800 (Tue, 09 Dec 2014) $
 *
 */
#include "FchPlatform.h"
#include "Filecode.h"
#include <Library/AmdCapsuleLib.h>
#define FILECODE FCH_KUNLUN_FCHKUNLUNCORE_KUNLUN_KLUSB_KLXHCIRESET_FILECODE

/**
 * FchInitResetXhci - Config Xhci controller during Power-On
 *
 *
 *
 * @param[in] FchDataPtr Fch configuration structure pointer.
 *
 */
VOID
FchInitResetXhci (
  IN  VOID     *FchDataPtr
  )
{
  FCH_RESET_DATA_BLOCK        *LocalCfgPtr;
  LocalCfgPtr = (FCH_RESET_DATA_BLOCK *)FchDataPtr;

  AGESA_TESTPOINT (TpFchInitResetUsb, NULL);
  IDS_HDT_CONSOLE (FCH_TRACE, "[FCH]FchInitResetXhci - Entry\n");


  /*
  Because of security consideration, x86 is forbidden to access nBIF straps.
  Move code to ABL.

  FchInitXhciEnableKL (FchDataPtr);
  */

  IDS_HDT_CONSOLE (
    FCH_TRACE,
    "[FCH]Xhci0Enable = 0x%x, Xhci1Enable = 0x%x\n",
    LocalCfgPtr->FchReset.Xhci0Enable,
    LocalCfgPtr->FchReset.Xhci1Enable
    );

  if (LocalCfgPtr->FchReset.Xhci0Enable || LocalCfgPtr->FchReset.Xhci1Enable) {
    IDS_HDT_CONSOLE (FCH_TRACE, "[FCH]Xhci0Enable Xhci1Enable need to be set.\n");

    if ((ReadFchSleepType (LocalCfgPtr->StdHeader) == ACPI_SLPTYP_S3) || (AmdCapsuleGetStatus ()) ) {
      IDS_HDT_CONSOLE (FCH_TRACE, "[FCH]FchInitResetXhci - call FchKLXhciInitS3ExitProgram\n");
      FchKLXhciInitS3ExitProgram (0, FchDataPtr);
    } else {
      IDS_HDT_CONSOLE (FCH_TRACE, "[FCH]FchInitResetXhci - call FchKLXhciInitBootProgram\n");
      FchKLXhciInitBootProgram (0, FchDataPtr);
    }
  }

  IDS_HDT_CONSOLE (FCH_TRACE, "[FCH]FchInitResetXhci - Exit\n");
}



