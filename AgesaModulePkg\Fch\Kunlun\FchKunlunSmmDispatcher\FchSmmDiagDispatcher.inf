#;*****************************************************************************
#;
#; Copyright (C) 2008-2024 Advanced Micro Devices, Inc. All rights reserved.
#;
#;******************************************************************************

[defines]
  INF_VERSION                    = 0x00010005
  BASE_NAME                      = FchSmmDiagDispatcher
  FILE_GUID                      = 02d7dfe6-cb36-4ff9-9ed9-31806b797251
  MODULE_TYPE                    = DXE_SMM_DRIVER
  VERSION_STRING                 = 1.0
  PI_SPECIFICATION_VERSION       = 0x0001000A
  ENTRY_POINT                    = FchSmmDiagDispatcherEntry

[sources]
  FchSmmDiagDispatcher.c

[sources.ia32]

[sources.x64]

[LibraryClasses]
  FchSmmLibV9
  UefiDriverEntryPoint
  BaseMemoryLib
  BaseLib
  DebugLib
  FchSmmLibV9
  UefiRuntimeServicesTableLib
  UefiBootServicesTableLib
  SmmServicesTableLib
  DevicePathLib
  MemoryAllocationLib
  SmmMemLib

[Guids]
  gFchSmmDiagDispatchProtocolGuid

[Protocols]
  gEfiSmmConfigurationProtocolGuid          #CONSUMED
  gEfiSmmBase2ProtocolGuid                  #CONSUMED
  gEfiSmmCpuProtocolGuid                    #CONSUMED

[Pcd]
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdFchSmiDiagEnable

[Packages]
  MdePkg/MdePkg.dec
  AgesaModulePkg/AgesaModuleFchPkg.dec
  AgesaModulePkg/Fch/Kunlun/FchKunlun.dec
  AgesaPkg/AgesaPkg.dec

[Depex]
  gEfiSmmBase2ProtocolGuid
  AND
  gEfiSmmCpuProtocolGuid
  AND
  gAmdFchKLSmmDispacherDepexProtocolGuid


