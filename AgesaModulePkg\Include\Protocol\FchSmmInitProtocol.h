/*****************************************************************************
 * Copyright (C) 2008-2024 Advanced Micro Devices, Inc. All rights reserved.
 *
*****************************************************************************
*/
/* $NoKeywords:$ */
/**
 * @file
 *
 * FCH DXE
 *
 *
 * @xrefitem bom "File Content Label" "Release Content"
 * @e project:      AGESA
 * @e sub-project   FCH SMM
 * @e \$Revision: 309090 $   @e \$Date: 2014-12-09 10:28:05 -0800 (Tue, 09 Dec 2014) $
 *
 */

#ifndef _FCH_SMM_INIT_PROTOCOL_H_
#define _FCH_SMM_INIT_PROTOCOL_H_

/**
 * @brief AMD FCH SMM Init protocol GUID
 */
extern EFI_GUID gFchSmmInitProtocolGuid;
/**
 * @brief AMD FCH SMM OEM Secure Complete protocol GUID
 */
extern EFI_GUID gFchSmmOemSecureCompleteProtocolGuid;

/**
 * @brief AMD FCH OEM SECURE PROTOCOL REVISION
 */
#define AMD_FCH_OEM_SECURE_PROTOCOL_REVISION   0x00

/// Forward declaration for the FCH_SMM_INIT_PROTOCOL
typedef struct _FCH_SMM_INIT_PROTOCOL FCH_SMM_INIT_PROTOCOL;
/// Forward declaration for the FCH_SMM_OEM_SECURE_COMPLETE_PROTOCOL
typedef struct _FCH_SMM_OEM_SECURE_COMPLETE_PROTOCOL FCH_SMM_OEM_SECURE_COMPLETE_PROTOCOL;

//
// Protocol prototypes
//

/**
 * @brief USB port disable function
 * @param This A pointer to the FCH_SMM_INIT_PROTOCOL instance
 */
typedef EFI_STATUS (EFIAPI *FP_FCH_SMM_SECURE_USB_PORT_DISABLE) (
  IN       CONST FCH_SMM_INIT_PROTOCOL   *This
);


/**
 * @brief FCH INIT Protocol
 * @param Revision Pointer to Protocol Revision
 * @param FchRev FCH Revision
 * @param FchSmmPolicy Fch Config Data Block
 * @param FchSmmSecureUsbPortDisable Fch Smm Secure Usb Port Disable
 */
typedef struct _FCH_SMM_INIT_PROTOCOL {
  UINTN                               Revision;
  UINTN                               FchRev;
  VOID                                *FchSmmPolicy;
  FP_FCH_SMM_SECURE_USB_PORT_DISABLE  FchSmmSecureUsbPortDisable;
} FCH_SMM_INIT_PROTOCOL;

/**
 * @brief FchOemSecure complete protocol
 * @param Revision Revision Number
 */
typedef struct _FCH_SMM_OEM_SECURE_COMPLETE_PROTOCOL {
  UINTN    Revision;
} FCH_SMM_OEM_SECURE_COMPLETE_PROTOCOL;

#endif // _FCH_INIT_PROTOCOL_H_



