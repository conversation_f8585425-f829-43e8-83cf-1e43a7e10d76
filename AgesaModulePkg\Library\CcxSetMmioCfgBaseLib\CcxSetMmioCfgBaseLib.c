/*****************************************************************************
 * Copyright (C) 2008-2024 Advanced Micro Devices, Inc. All rights reserved.
 *
*****************************************************************************
*/
/* $NoKeywords:$ */
/**
 * @file
 *
 * AMD Ccx set MMIO configuration space base address LIB
 *
 *
 * @xrefitem bom "File Content Label" "Release Content"
 * @e project:      AGESA
 * @e sub-project:  Lib
 * @e \$Revision$   @e \$Date$
 *
 */
/*++
Module Name:

  CcxSetMmioCfgBaseLib.c
  Set MMIO configuration space base address

Abstract:
--*/

#include <AGESA.h>
#include <Library/BaseLib.h>
#include <cpuRegisters.h>
#include <Filecode.h>
#include <Library/CcxRolesLib.h>

#define FILECODE LIBRARY_CCXSETMMIOCFGBASELIB_CCXSETMMIOCFGBASELIB_FILECODE

/* -----------------------------------------------------------------------------*/
/**
 *
 *  CcxSetMmioCfgBaseLib
 *
 *  Description:
 *    This routine sets MSR_MMIO_Cfg_Base register
 *
 */
VOID
CcxSetMmioCfgBaseLib (
  )
{
  UINT64            MmioCfgBase;
  CPUID_DATA        CpuId;
  UINT64            MmioCfgBaseAddrMask;

  CpuId.EAX_Reg = 0;
  CpuId.EBX_Reg = 0;
  CpuId.ECX_Reg = 0;
  CpuId.EDX_Reg = 0;

  if (CcxIsBsp (NULL)) {
    MmioCfgBase = AsmReadMsr64 (MSR_MMIO_Cfg_Base);
    if ((MmioCfgBase & BIT0) == 0) {
      // CPUID_Fn80000008_EAX[7:0] is PhysAddrSize
      AsmCpuid (0x80000008,
                &(CpuId.EAX_Reg),
                &(CpuId.EBX_Reg),
                &(CpuId.ECX_Reg),
                &(CpuId.EDX_Reg));
      MmioCfgBaseAddrMask = (LShiftU64 (1, CpuId.EAX_Reg & 0xFF) - 1) & 0xFFFFFFFFFFF00000;

      // Set [PhysAddrSize-1:20][MmioCfgBaseAddr]
      // Set [0][Enable]
      // [5:2][BusRange] has been set by ABL
      MmioCfgBase = (MmioCfgBase & ~MmioCfgBaseAddrMask) + (PcdGet64 (PcdPciExpressBaseAddress) & MmioCfgBaseAddrMask);
      MmioCfgBase |= BIT0;
      AsmWriteMsr64 (MSR_MMIO_Cfg_Base, MmioCfgBase);
    }
  }
}




