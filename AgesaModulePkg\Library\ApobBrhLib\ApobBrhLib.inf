#;*****************************************************************************
#;
#; Copyright (C) 2008-2024 Advanced Micro Devices, Inc. All rights reserved.
#;
#;******************************************************************************
[Defines]
  INF_VERSION                    = 0x00010006
  BASE_NAME                      = ApobBrhLib
  FILE_GUID                      = c3dc68b1-45ac-4dd3-8b30-2057915ecea9
  MODULE_TYPE                    = BASE
  VERSION_STRING                 = 1.0
  LIBRARY_CLASS                  = ApobBrhLib

[Sources.common]
  ApobBrhLib.c

[Packages]
  MdePkg/MdePkg.dec
  AgesaPkg/AgesaPkg.dec
  AgesaModulePkg/AgesaCommonModulePkg.dec
  AgesaModulePkg/AgesaModulePspPkg.dec

[LibraryClasses]
  BaseMemoryLib
  HobLib
  DebugLib
  PcdLib
  MemoryAllocationLib
  AmdPspApobLib
  IdsLib

[Guids]

[Protocols]


[Ppis]

[Pcd]
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdPspApcbRecoveryEnable

