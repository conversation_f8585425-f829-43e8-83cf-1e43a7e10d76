/*****************************************************************************
 *
 * Copyright (C) 2008-2024 Advanced Micro Devices, Inc. All rights reserved.
 *
 *******************************************************************************
 */

#include <PiDxe.h>
#include <Library/BaseLib.h>
#include <Library/CcxDxeLib.h>
#include <Library/PcdLib.h>
#include <Library/AmdBaseLib.h>
#include <Library/AmdIdsHookLib.h>
#include <Library/FabricRegisterAccLib.h>
#include <Library/UefiBootServicesTableLib.h>
#include <FabricRegistersBrh.h>
#include <FabricInfoBrh.h>
#include <Filecode.h>
#include <Protocol/FabricResourceManagerServicesProtocol.h>
#include <Protocol/FabricTopologyServices2.h>

#define FILECODE LIBRARY_CCXZEN5BRHDXELIB_CCXZEN5BRHDXELIB_FILECODE

#define RMP_MAX_RB_COUNT_ABOVE_4GB 1

/*----------------------------------------------------------------------------------------
 *                               D E F I N I T I O N S
 *----------------------------------------------------------------------------------------
 */
#define INVALID_SOCKET_ID 0xFFFFFFFF

/*----------------------------------------------------------------------------------------
 *                           G L O B A L   V A R I A B L E S
 *----------------------------------------------------------------------------------------
 */

/*----------------------------------------------------------------------------------------
 *           P R O T O T Y P E S     O F     L O C A L     F U N C T I O N S
 *----------------------------------------------------------------------------------------
 */

/**
 *
 *  CcxGetRmpTableBaseAcrossSocket
 *
 *  @param[in]  RmpTableSize    The size of RMP table
 *  @param[out] RmpTableBase    The base address of the RMP table across sockets
 *
 *  Description:
 *    Find the RmpTableBase.
 *    The found RmpTableBase should make the RMP be split evenly across sockets.
 *
 *  @retval TRUE   Success to find RmpTableBase across sockets
 *  @retval FALSE  Unable to find RmpTableBase across sockets
 *
 */
BOOLEAN
EFIAPI
CcxGetRmpTableBaseAcrossSocket (
  IN  UINT64               RmpTableSize,
  OUT EFI_PHYSICAL_ADDRESS *RmpTableBase
  )
{
  UINT32                       DramMapIndex;
  UINT32                       PrevSocketId;
  UINT32                       SocketId;
  DRAM_ADDRESS_CTL_REGISTER    DramAddressCtl;
  DRAM_BASE_ADDRESS_REGISTER   DramBaseAddr;

  if ((RmpTableSize < 2) || (RmpTableBase == NULL)) {
    return FALSE;
  }

  PrevSocketId = INVALID_SOCKET_ID;

  // Collect information about the memory ranges discovered by ABL
  // This code assumes the DRAM ranges are ordered from small to big.
  for (DramMapIndex = 0; DramMapIndex < BRH_NUMBER_OF_DRAM_REGIONS; DramMapIndex++) {
    DramAddressCtl.Value = FabricRegisterAccRead (0, 0, DRAMADDRESSCTL_0_FUNC, (DRAMADDRESSCTL_0_REG + (DramMapIndex * BRH_DRAM_REGION_REGISTER_OFFSET)), BRH_IOMS2_INSTANCE_ID);
    if (DramAddressCtl.Field.AddrRngVal == 1) {
      SocketId = (DramAddressCtl.Field.DstFabricID >> BRH_FABRIC_ID_SOCKET_SHIFT) & BRH_FABRIC_ID_SOCKET_SIZE_MASK;
      if (PrevSocketId == INVALID_SOCKET_ID) {
        PrevSocketId = SocketId;
      } else {
        if (SocketId != PrevSocketId) {
          DramBaseAddr.Value = FabricRegisterAccRead (0, 0, DRAMBASEADDRESS_0_FUNC, (DRAMBASEADDRESS_0_REG + (DramMapIndex * BRH_DRAM_REGION_REGISTER_OFFSET)), BRH_IOMS2_INSTANCE_ID);
          *RmpTableBase = (((UINT64) DramBaseAddr.Field.DramBaseAddr) << 28) + (RmpTableSize / 2);
          return TRUE;
        }
      }
    }
  }
  return FALSE;
}

/**
 *
 *  CcxGetAbove4GMMIOSnpMemSize
 *
 *  @param[OUT] UINT64* SnpMemSizeToCoverAbove4GMMIO
 *
 *  Description:
 *    Get SNP Memory Size Including Above 4GB MMIO Range
 *
 *  @retval EFI_UNSUPPORTED SNP Memory Size including above 4GB MMIO range is not retrieved
 *  @retval EFI_DEVICE_ERROR Failed to get SNP Memory Size including above 4GB MMIO range
 *  @retval EFI_INVALID_PARAMETER Invalid incoming parameter
 *  @retval EFI_SUCCESS  SNP Memory Size including above 4GB MMIO range is retrieved
 *
 */
EFI_STATUS
EFIAPI
CcxGetAbove4GMMIOSnpMemSize (
  OUT UINT64* SnpMemSizeToCoverAbove4GMMIO
  )
{
  UINT8                                  i;
  UINT8                                  j;
  FABRIC_RESOURCE_MANAGER_PROTOCOL*      FabricResourceManager = NULL;
  FABRIC_RESOURCE_FOR_EACH_RB            ResourceForEachRb;
  EFI_STATUS                             Status;
  UINT64                                 SnpMemSizeToCoverMax;
  UINT64                                 RmpTableSize;
  EFI_PHYSICAL_ADDRESS                   RmpTableBase;
  UINTN                                  SocketCount;
  UINTN                                  RootBridgeCountPerSocket;
  AMD_FABRIC_TOPOLOGY_SERVICES2_PROTOCOL *FabricTopologyServices = NULL;

   // init variables
  Status = EFI_DEVICE_ERROR;
  SnpMemSizeToCoverMax = 0;
  RmpTableSize = 0;
  RmpTableBase = 0;

  if (!PcdGetBool(PcdAmdRmpCover64BitMMIORanges)) {
    IDS_HDT_CONSOLE (CPU_TRACE, "CcxGetAbove4GMMIOSnpMemSize PcdAmdRmpCover64BitMMIORanges is FALSE\n");
    return EFI_UNSUPPORTED;
  }

  if (SnpMemSizeToCoverAbove4GMMIO == NULL) {
    IDS_HDT_CONSOLE (CPU_TRACE, "CcxGetAbove4GMMIOSnpMemSize SnpMemSizeToCoverAbove4GMMIO is NULL\n");
    return EFI_INVALID_PARAMETER;
  }

  Status = gBS->LocateProtocol (&gAmdFabricTopologyServices2ProtocolGuid, NULL, (VOID **)&FabricTopologyServices);
  if (EFI_ERROR(Status) || EFI_ERROR (FabricTopologyServices->GetSystemInfo (FabricTopologyServices, &SocketCount, NULL, NULL, NULL, NULL))) {
    SocketCount = 1;
  }

    // Get available FabricResource
  Status = gBS->LocateProtocol (
                  &gAmdFabricResourceManagerServicesProtocolGuid,
                  NULL,
                  (VOID **)&FabricResourceManager
                  );
  ASSERT_EFI_ERROR (Status);
  if (EFI_ERROR (Status)) {
    IDS_HDT_CONSOLE (CPU_TRACE, "CcxGetAbove4GMMIOSnpMemSize Failed to locate FabricResourceManagerServicesProtocol\n");
    return Status;
  }

  Status = FabricResourceManager->FabricGetAvailableResource (FabricResourceManager, &ResourceForEachRb);

  if (!EFI_ERROR(Status)) {
    // To cover 64Bit MMIO above 4GB, find the MMIO ranges for RBs
    for (i = 0; i < SocketCount; i++) {
      if (!FabricTopologyServices || (EFI_ERROR (FabricTopologyServices->GetProcessorInfo(FabricTopologyServices, i, NULL, &RootBridgeCountPerSocket)))) {
            RootBridgeCountPerSocket = 1;
      }

      for (j = 0; j < RootBridgeCountPerSocket; j++) {
        IDS_HDT_CONSOLE (CPU_TRACE, "CcxZen5InitSnpRmp 64bit MMIO Ranges, Socket# %d, RB# %d, Base: 0x%lx, Size: 0x%lx\n", i,j,\
        ResourceForEachRb.PrefetchableMmioSizeAbove4G[i][j].Base,\
        ResourceForEachRb.PrefetchableMmioSizeAbove4G[i][j].Size);

        if ((j + (i * RootBridgeCountPerSocket) + 1) >= RMP_MAX_RB_COUNT_ABOVE_4GB) {
          IDS_HDT_CONSOLE (CPU_TRACE, "CcxZen5InitSnpRmp Reached the limit of Max RB to be covered Limit: %d\n", RMP_MAX_RB_COUNT_ABOVE_4GB);
          SnpMemSizeToCoverMax = ResourceForEachRb.PrefetchableMmioSizeAbove4G[i][j].Base + ResourceForEachRb.PrefetchableMmioSizeAbove4G[i][j].Size;
          break;
        }
      }
    }
  }

  if (SnpMemSizeToCoverMax != 0) {
    // 16KB + ((Bytes/4K-page) * (# of pages [SnpMemSizeToCover/4K]))
    RmpTableSize = SIZE_16KB + ((UINT64) 16 * (SnpMemSizeToCoverMax >> 12));

    // round RMP table size up to nearest 1MB
    RmpTableSize = (RmpTableSize + SIZE_1MB - 1) & ~(SIZE_1MB - 1);

    // find enough memory for RMP table to fit on MB boundary
    RmpTableBase = SnpMemSizeToCoverMax;
    IDS_HDT_CONSOLE (CPU_TRACE, "CcxZen5InitSnpRmp Try allocation attempt with new memory base-limit to include 64bit MMIO Coverage.\n" \
                                "SnpMemSizeToCoverMax=0x%lx,  RmpTableBase=0x%lx, RmpTableSize=0x%lx\n",
                                SnpMemSizeToCoverMax, RmpTableBase, RmpTableSize);

    Status = gBS->AllocatePages (
               AllocateMaxAddress,
               EfiReservedMemoryType,
               EFI_SIZE_TO_PAGES (RmpTableSize + SIZE_1MB),
               &RmpTableBase
            );
    if (!EFI_ERROR (Status)) {
      IDS_HDT_CONSOLE (CPU_TRACE, "CcxZen5InitSnpRmp SnpMemSizeToCoverMax can be allocated: 0x%lx\n", SnpMemSizeToCoverMax);

      // free pages since this is just a trial
      gBS->FreePages (RmpTableBase, EFI_SIZE_TO_PAGES (RmpTableSize + SIZE_1MB));
      *SnpMemSizeToCoverAbove4GMMIO = SnpMemSizeToCoverMax;
      Status = EFI_SUCCESS;
    }
  }
  else {
    Status = EFI_DEVICE_ERROR;
  }

  return Status;
}
