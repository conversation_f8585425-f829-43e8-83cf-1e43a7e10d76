/*****************************************************************************
 *
 * Copyright (C) 2015-2024 Advanced Micro Devices, Inc. All rights reserved.
 *
 *******************************************************************************
 */

#ifndef _AMD_I3C_MASTER_DXE_H_
#define _AMD_I3C_MASTER_DXE_H_

#include <Library/DebugLib.h>
#include <Library/PcdLib.h>
#include <Library/BaseMemoryLib.h>
#include <Library/FchBaseLib.h>
#include <Protocol/FchI3cMasterProtocol.h>

EFI_STATUS
EFIAPI
SetBusFrequencyI3c (
  IN       EFI_DXE_I3C_MASTER_PROTOCOL *This,
  IN       UINTN                       *BusClockHertz
  );

EFI_STATUS
EFIAPI
Reset (
  IN CONST EFI_DXE_I3C_MASTER_PROTOCOL *This
  );

EFI_STATUS
EFIAPI
StartRequest (
  IN CONST EFI_DXE_I3C_MASTER_PROTOCOL *This,
  IN       UINTN                       SlaveAddress,
  IN       EFI_I3C_REQUEST_PACKET      *RequestPacket,
  IN       BOOLEAN                     ReStartEnable
  );

EFI_STATUS
EFIAPI
AmdI3cMasterDxeInit (
  IN       EFI_HANDLE       ImageHandle,
  IN       EFI_SYSTEM_TABLE *SystemTable
  );

#endif // _AMD_I3C_MASTER_DXE_H_

