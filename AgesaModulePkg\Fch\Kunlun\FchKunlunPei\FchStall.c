/*****************************************************************************
 * Copyright (C) 2008-2024 Advanced Micro Devices, Inc. All rights reserved.
 *
*****************************************************************************
*/
/* $NoKeywords:$ */
/**
 * @file
 *
 * FCH PEIM
 *
 *
 * @xrefitem bom "File Content Label" "Release Content"
 * @e project:      AGESA
 * @e sub-project   FCH PEIM
 * @e \$Revision: 309090 $   @e \$Date: 2014-12-09 10:28:05 -0800 (Tue, 09 Dec 2014) $
 *
 */

#include <Library/TimerLib.h>
#include "FchStall.h"
#define FILECODE FCH_KUNLUN_FCHKUNLUNPEI_FCHSTALL_FILECODE


/**
 * @brief Blocking Stall.
 *
 * @details Stall the system for at least the specified number of microsecond
 *
 * @param[in] PeiServices Pointer to the PEI service table
 *
 * @returns EFI_STATUS
 * @retval EFI_SUCCESS   Module initialized successfully
 */

EFI_STATUS
EFIAPI
FchPeiStall (
  IN  CONST     EFI_PEI_SERVICES   **PeiServices,
  IN  CONST     EFI_PEI_STALL_PPI  *This,
  IN       UINTN              Microseconds
  )
{
  MicroSecondDelay(Microseconds);
  return (EFI_SUCCESS);
}


