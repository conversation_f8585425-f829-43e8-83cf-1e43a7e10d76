/*
 ******************************************************************************
 *
 * Copyright (C) 2024 Advanced Micro Devices, Inc. All rights reserved.
 *
 *******************************************************************************
 */

/*----------------------------------------------------------------------------------------
 *                             M O D U L E S    U S E D
 *----------------------------------------------------------------------------------------
 */
#include <Uefi.h>
#include <Base.h>
#include <Library/IdsLib.h>
#include <Addendum/Apcb/Inc/CommonV3/ApcbV3Arch.h>
#include <Addendum/Apcb/Inc/CommonV3/ApcbDataGroups.h>
#include <Addendum/Apcb/Inc/CommonV3/ApcbV3Token.h>
#include <Library/ApcbCoreLib.h>

#include <Filecode.h>

#define FILECODE LIBRARY_APCBCORELIB_APCBCORELIB_FILECODE

/*----------------------------------------------------------------------------------------
 *                   D E F I N I T I O N S    A N D    M A C R O S
 *----------------------------------------------------------------------------------------
 */
#define customPrint(...) IDS_HDT_CONSOLE_PSP_TRACE(__VA_ARGS__)

/*----------------------------------------------------------------------------------------
 *                  T Y P E D E F S     A N D     S T R U C T U R E S
 *----------------------------------------------------------------------------------------
 */


/*----------------------------------------------------------------------------------------
 *           P R O T O T Y P E S     O F     L O C A L     F U N C T I O N S
 *----------------------------------------------------------------------------------------
 */


/*----------------------------------------------------------------------------------------
 *                          E X P O R T E D    F U N C T I O N S
 *----------------------------------------------------------------------------------------
 */

/*----------------------------------------------------------------------------------------
 *                          G L O B A L        V A L U E S
 *----------------------------------------------------------------------------------------
 */

/*---------------------------------------------------------------------------------------*/
/**
 * @brief This function retrieves a boolean APCB token
 *
 * @param[in]         apcbBuf         - Pointer to the APCB binary
 * @param[in]         priorityMask    - Priority Mask
 * @param[in]         boardMask       - Board Mask
 * @param[in]         typeInstance    - Type Instance
 * @param[in]         apcbToken       - APCB token ID
 * @param[out]        bValue          - APCB token value
 *
 * @retval            CORE_APCB_OK
 *                    CORE_APCB_ERR_TOKEN_NOT_FOUND
 *
 */
CORE_APCB_RET_CODE
coreApcbGetBool (
  IN         UINT8        *apcbBuf,
  IN         UINT8        priorityMask,
  IN         UINT16       boardMask,
  IN         UINT8        typeInstance,
  IN         UINT32       apcbToken,
      OUT    BOOLEAN      *bValue
  )
{
  UINT8                 *dataBuf;
  UINT32                dataSize;
  APCB_TOKEN_PAIR       *tempPair;

  customPrint ("\t[CORE APCB INFO] coreApcbGetBool Entry\n");

  if (CORE_APCB_OK ==
      coreApcbGetType (apcbBuf, APCB_GROUP_TOKEN, APCB_TYPE_TOKEN_BOOLEAN, priorityMask, boardMask,
                       typeInstance, &dataBuf, &dataSize)) {
    for (tempPair = (APCB_TOKEN_PAIR *) dataBuf; (UINT8 *) tempPair < &dataBuf[dataSize]; tempPair ++) {
      if (tempPair->token == apcbToken) {
        *bValue = tempPair->value.bValue;
        customPrint ("\t[CORE APCB INFO] APCB token found: [%08X] = %08X\n", apcbToken, *bValue);
        return CORE_APCB_OK;
      }
    }
  }

  customPrint ("\t[CORE APCB ERR] APCB token not found\n");

  return CORE_APCB_ERR_TOKEN_NOT_FOUND;
}


/*---------------------------------------------------------------------------------------*/
/**
 * @brief This function retrieves a UINT8 APCB token
 *
 * @param[in]         apcbBuf         - Pointer to the APCB binary
 * @param[in]         priorityMask    - Priority Mask
 * @param[in]         boardMask       - Board Mask
 * @param[in]         typeInstance    - Type Instance
 * @param[in]         apcbToken       - APCB token ID
 * @param[out]        Value8          - APCB token value
 *
 * @retval            CORE_APCB_OK
 *                    CORE_APCB_ERR_TOKEN_NOT_FOUND
 *
 */
CORE_APCB_RET_CODE
coreApcbGet8 (
  IN         UINT8        *apcbBuf,
  IN         UINT8        priorityMask,
  IN         UINT16       boardMask,
  IN         UINT8        typeInstance,
  IN         UINT32       apcbToken,
      OUT    UINT8        *value8
  )
{
  UINT8                 *dataBuf;
  UINT32                dataSize;
  APCB_TOKEN_PAIR       *tempPair;

  customPrint ("\t[CORE APCB INFO] coreApcbGet8 Entry\n");

  if (CORE_APCB_OK ==
      coreApcbGetType (apcbBuf, APCB_GROUP_TOKEN, APCB_TYPE_TOKEN_1BYTE, priorityMask, boardMask,
                       typeInstance, &dataBuf, &dataSize)) {
    for (tempPair = (APCB_TOKEN_PAIR *) dataBuf; (UINT8 *) tempPair < &dataBuf[dataSize]; tempPair ++) {
      if (tempPair->token == apcbToken) {
        *value8 = tempPair->value.value8;
        customPrint ("\t[CORE APCB INFO] APCB token found: [%08X] = %08X\n", apcbToken, *value8);
        return CORE_APCB_OK;
      }
    }
  }

  customPrint ("\t[CORE APCB ERR] APCB token not found\n");

  return CORE_APCB_ERR_TOKEN_NOT_FOUND;
}


/*---------------------------------------------------------------------------------------*/
/**
 * @brief This function retrieves a UINT16 APCB token
 *
 * @param[in]         apcbBuf         - Pointer to the APCB binary
 * @param[in]         priorityMask    - Priority Mask
 * @param[in]         boardMask       - Board Mask
 * @param[in]         typeInstance    - Type Instance
 * @param[in]         apcbToken       - APCB token ID
 * @param[out]        value16         - APCB token value
 *
 * @retval            CORE_APCB_OK
 *                    CORE_APCB_ERR_TOKEN_NOT_FOUND
 *
 */
CORE_APCB_RET_CODE
coreApcbGet16 (
  IN         UINT8        *apcbBuf,
  IN         UINT8        priorityMask,
  IN         UINT16       boardMask,
  IN         UINT8        typeInstance,
  IN         UINT32       apcbToken,
      OUT    UINT16       *value16
  )
{
  UINT8                 *dataBuf;
  UINT32                dataSize;
  APCB_TOKEN_PAIR       *tempPair;

  customPrint ("\t[CORE APCB INFO] coreApcbGet16 Entry\n");

  if (CORE_APCB_OK ==
      coreApcbGetType (apcbBuf, APCB_GROUP_TOKEN, APCB_TYPE_TOKEN_2BYTES, priorityMask, boardMask,
                       typeInstance, &dataBuf, &dataSize)) {
    for (tempPair = (APCB_TOKEN_PAIR *)dataBuf; (UINT8 *)tempPair < &dataBuf[dataSize]; tempPair ++) {
      if (tempPair->token == apcbToken) {
        *value16 = tempPair->value.value16;
        customPrint ("\t[CORE APCB INFO] APCB token found: [%08X] = %08X\n", apcbToken, *value16);
        return CORE_APCB_OK;
      }
    }
  }

  customPrint ("\t[CORE APCB ERR] APCB token not found\n");

  return CORE_APCB_ERR_TOKEN_NOT_FOUND;
}


/*---------------------------------------------------------------------------------------*/
/**
 * @brief This function retrieves a UINT32 APCB token
 *
 * @param[in]         apcbBuf         - Pointer to the APCB binary
 * @param[in]         priorityMask    - Priority Mask
 * @param[in]         boardMask       - Board Mask
 * @param[in]         typeInstance    - Type Instance
 * @param[in]         apcbToken       - APCB token ID
 * @param[out]        value32         - APCB token value
 *
 * @retval            CORE_APCB_OK
 *                    CORE_APCB_ERR_TOKEN_NOT_FOUND
 *
 */
CORE_APCB_RET_CODE
coreApcbGet32 (
  IN          UINT8       *apcbBuf,
  IN          UINT8       priorityMask,
  IN          UINT16      boardMask,
  IN          UINT8       typeInstance,
  IN          UINT32      apcbToken,
      OUT     UINT32      *value32
  )
{
  UINT8                 *dataBuf;
  UINT32                dataSize;
  APCB_TOKEN_PAIR       *tempPair;

  customPrint ("\t[CORE APCB INFO] coreApcbGet32 Entry\n");

  if (CORE_APCB_OK ==
      coreApcbGetType (apcbBuf, APCB_GROUP_TOKEN, APCB_TYPE_TOKEN_4BYTES, priorityMask, boardMask,
                       typeInstance, &dataBuf, &dataSize)) {
    for (tempPair = (APCB_TOKEN_PAIR *)dataBuf; (UINT8 *)tempPair < &dataBuf[dataSize]; tempPair ++) {
      if (tempPair->token == apcbToken) {
        *value32 = tempPair->value.value32;
        customPrint ("\t[CORE APCB INFO] APCB token found: [%08X] = %08X\n", apcbToken, *value32);
        return CORE_APCB_OK;
      }
    }
  }

  customPrint ("\t[CORE APCB ERR] APCB token not found\n");

  return CORE_APCB_ERR_TOKEN_NOT_FOUND;
}


/*---------------------------------------------------------------------------------------*/
/**
 * @brief This function retrieves the APCB type by the specified attributes
 *
 * @param[in]         apcbBuf         - Pointer to the APCB binary
 * @param[in]         groupId         - Group Id of the specified type
 * @param[in]         typeId          - Type Id of the specified type
 * @param[in]         priorityMask    - Priority Mask
 * @param[in]         boardMask       - Board Mask
 * @param[in]         instance        - Instance Id of the specified type
 * @param[out]        dataBuf         - Pointer to the data of the specified type
 * @param[out]        dataSize        - size of data of the specified type
 *
 * @retval            CORE_APCB_OK
 *                    CORE_APCB_ERR_INVALID
 *                    CORE_APCB_ERR_TYPE_NOT_FOUND
 *
 */
CORE_APCB_RET_CODE
coreApcbGetType (
  IN           UINT8       *apcbBuf,
  IN           UINT16      groupId,
  IN           UINT16      typeId,
  IN           UINT8       priorityMask,
  IN           UINT16      boardMask,
  IN           UINT16      instance,
      OUT      UINT8       **dataBuf,
      OUT      UINT32      *dataSize
  )
{
  APCB_V3_HEADER        *apcbHeader;
  APCB_GROUP_HEADER     *apcbGroupHeader;
  APCB_V3_TYPE_HEADER   *apcbTypeHeader;
  UINT8                 *apcbTempBuf;
  UINT8                 *apcbEnding;
  UINT8                 *apcbGroupEnding;

  customPrint ("\t[CORE APCB INFO] coreApcbGetType Entry\n");

  if (NULL == apcbBuf) {
    customPrint ("\t[CORE APCB ERR] NULL APCB data\n");
    return CORE_APCB_ERR_INVALID;
  }

  apcbTempBuf       = apcbBuf;
  apcbHeader        = (APCB_V3_HEADER *)apcbBuf;
  apcbEnding        = &apcbTempBuf[apcbHeader->SizeOfApcb];
  apcbGroupHeader   = (APCB_GROUP_HEADER *)&apcbHeader[1];

  if (APCB_SIGNATURE != apcbHeader->Signature) {
    customPrint ("\t[CORE APCB ERR] Invalid APCB Signature\n");
    return CORE_APCB_ERR_INVALID;
  }

  while ((UINT8 *)apcbGroupHeader < apcbEnding) {
    apcbTempBuf       = (UINT8 *)apcbGroupHeader;
    apcbGroupEnding   = &apcbTempBuf[apcbGroupHeader->SizeOfGroup];
    apcbTypeHeader    = (APCB_V3_TYPE_HEADER *)&apcbGroupHeader[1];
    while ((UINT8 *)apcbTypeHeader < apcbGroupEnding) {
      if (groupId == apcbTypeHeader->GroupId && typeId == apcbTypeHeader->TypeId &&
        (priorityMask & apcbTypeHeader->sApcbTypeExtV3.PriorityMask) != 0 &&
        ((boardMask & (UINT16) (apcbTypeHeader->sApcbTypeExtV3.BoardMask)) != 0 ||
         (UINT16) (apcbTypeHeader->sApcbTypeExtV3.BoardMask + 1) == 0)) {
        *dataBuf          = (UINT8 *)&apcbTypeHeader[1];
        *dataSize         = apcbTypeHeader->SizeOfType - sizeof (APCB_V3_TYPE_HEADER);
        customPrint ("\t[CORE APCB INFO] APCB type found: GroupId = 0x%x, TypeId = 0x%x, "
          "InstanceId = %d, PriorityMask = 0x%x, BoardMask = 0x%x, dataBuf = %08X, dataSize = %08X\n",
          groupId, typeId, instance, apcbTypeHeader->sApcbTypeExtV3.PriorityMask, apcbTypeHeader->sApcbTypeExtV3.BoardMask,
          *dataBuf, *dataSize);
        return CORE_APCB_OK;
      } else {
        apcbTempBuf       = (UINT8 *)apcbTypeHeader;
        apcbTypeHeader    = (APCB_V3_TYPE_HEADER *)&apcbTempBuf[apcbTypeHeader->SizeOfType];
      }
    }
    apcbGroupHeader = (APCB_GROUP_HEADER *)apcbTypeHeader;
  }

  customPrint ("\t[CORE APCB ERR] APCB type not found\n");

  return CORE_APCB_ERR_TYPE_NOT_FOUND;
}

