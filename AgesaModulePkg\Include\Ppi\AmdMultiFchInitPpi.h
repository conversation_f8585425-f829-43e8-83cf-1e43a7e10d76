/*****************************************************************************
 * Copyright (C) 2008-2024 Advanced Micro Devices, Inc. All rights reserved.
 *
*****************************************************************************
*/
/* $NoKeywords:$ */
 /**
 * @file
 *
 * Multiple FCH Init PPI.
 *
 * @xrefitem bom "File Content Label" "Release Content"
 * @e project:      AGESA
 * @e sub-project:  UEFI
 * @e \$Revision: 309090 $   @e \$Date: 2014-12-09 10:28:05 -0800 (Tue, 09 Dec 2014) $
 *
 */

#ifndef _AMD_MULTI_FCH_INIT_PPI_H_
#define _AMD_MULTI_FCH_INIT_PPI_H_

extern EFI_GUID gAmdFchMultiFchInitPpiGuid;

#define MULTI_FCH_MAX_NUMBER_OF_DIE_PER_SOCKET 8
#define MULTI_FCH_MAX_NUMBER_OF_SOCKETS        8
#define MULTI_FCH_MAX_NUMBER_OF_MMIO           ((MULTI_FCH_MAX_NUMBER_OF_DIE_PER_SOCKET)*(MULTI_FCH_MAX_NUMBER_OF_SOCKETS))

typedef struct _AMD_FCH_MULTI_FCH_INIT_PPI  AMD_FCH_MULTI_FCH_INIT_PPI;

typedef EFI_STATUS (*FCH_MULITI_FCH_GET_MMIO_BASE_ADDR_PEI) (
  AMD_FCH_MULTI_FCH_INIT_PPI *This,
  UINTN                      SocketNum,
  UINTN                      DieNum,
  UINT64                     *MmioBaseAddress
  );

typedef struct _FCH_MULITI_FCH_RESET_DATA_BLOCK {
  UINT64                FchAcpiMmioBase[MULTI_FCH_MAX_NUMBER_OF_MMIO];            ///< FCH ACPI MMIO Base
} FCH_MULITI_FCH_RESET_DATA_BLOCK;

/// PPI definition
typedef struct _AMD_FCH_MULTI_FCH_INIT_PPI {
  UINTN                                  Revision;         ///< Revision number
  FCH_MULITI_FCH_RESET_DATA_BLOCK        FchMfResetData;   ///< Data Block
  UINTN                                  TotalNumberOfSockets;
  UINTN                                  TotalNumberOfDies;
  UINTN                                  TotalNumberOfRootBridges;
  UINTN                                  DataBlockSize;
  FCH_MULITI_FCH_GET_MMIO_BASE_ADDR_PEI  FchMfGetMmioBaseAddr;
} AMD_FCH_MULTI_FCH_INIT_PPI;

//
// current PPI revision
//
#define AMD_MULTI_FCH_INIT_PPI_REV  0x02


#endif // _AMD_MULTI_FCH_INIT_PPI_H_



