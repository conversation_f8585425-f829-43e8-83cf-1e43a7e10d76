/*****************************************************************************
 * Copyright (C) 2008-2024 Advanced Micro Devices, Inc. All rights reserved.
 *
*****************************************************************************
*/
/* $NoKeywords:$ */
/**
 * @file
 *
 * FCH SMM Driver
 *
 *
 * @xrefitem bom "File Content Label" "Release Content"
 * @e project:      AGESA
 * @e sub-project   FCH SMM Driver
 * @e \$Revision: 309083 $   @e \$Date: 2014-12-09 09:28:24 -0800 (Tue, 09 Dec 2014) $
 *
 */
#include "FchMultiFchSmm.h"
#define FILECODE FCH_KUNLUN_FCHKUNLUNMULTIFCHSMM_FCHMULTIFCHSMM_FILECODE


//
// Driver Global Data
//
UINT32 Socket1FchBusNum;
UINT8  SmmPcdSataEnable2;
UINT8  SmmPcdSataSgpioMultiDieEnable;
UINT64 SmmPcdSataMultiDiePortShutDown;
UINT64 SmmPcdSataMultiDiePortESP;
UINT64 SmmPcdSataMultiDieDevSlp;
UINT64 SmmPcdSataIoDie1PortMode;
FCH_SMM_INIT_PROTOCOL *pFchSmmInitProtocol;
FCH_MULITI_FCH_DATA_BLOCK  FchSmmMfData;
FCH_DATA_BLOCK             MultiFchDataInSmm;

/**
 * @brief Get Socket 1 Bus Number.
 *
 * @returns UINTN        2nd socket bus number
 */
UINTN
FchSmmGetSocket1Bus (
  VOID
  )
{
  EFI_STATUS                              Status;
  UINTN                                   BusNumberBase;
  AMD_FABRIC_TOPOLOGY_SERVICES2_PROTOCOL  *FabricTopology;
  BOOLEAN                                 HasFchDevice;
  UINTN                                   i;
  UINTN                                   NumberOfRootBridges;

  Status          = EFI_SUCCESS;
  FabricTopology  = NULL;
  BusNumberBase   = 0;

  Status = gSmst->SmmLocateProtocol (
                    &gAmdFabricTopologyServices2SmmProtocolGuid,
                    NULL,
                    (VOID **) &FabricTopology
                    );
  if (Status == EFI_SUCCESS) {
    Status = FabricTopology->GetDieInfo (
                               FabricTopology,
                               FCH_SECOND_SOCKET,
                               FCH_SECOND_SOCKET_DIE,
                               &NumberOfRootBridges,
                               NULL,
                               NULL
                               );
    if (Status == EFI_SUCCESS) {
      for (i = 0; i < NumberOfRootBridges; i++) {
        Status = FabricTopology->GetRootBridgeInfo (
                                   FabricTopology,
                                   FCH_SECOND_SOCKET,
                                   FCH_SECOND_SOCKET_DIE,
                                   i,
                                   NULL,
                                   &BusNumberBase,
                                   NULL,
                                   NULL,
                                   &HasFchDevice,
                                   NULL
                                   );
        if (!EFI_ERROR (Status) && HasFchDevice) {
          DEBUG ((DEBUG_INFO, "[%a] Multi FCH SMM Get 2nd Socket Bus Number = 0x%x\n", __FUNCTION__, BusNumberBase));
          return BusNumberBase;
        }
      }
    }
  }
  ASSERT (FALSE);
  return 0;
}

static
VOID
FchSmmDisableUsbPort (
  VOID
  )
{
  EFI_STATUS                          Status;
  PSP_MBOX_SMMBUFFER_ADDRESS_PROTOCOL *PspMboxSmmBufferAddressProtocol;
  UINT8                               *PspMboxSmmBuffer;
  BOOLEAN                             *PspMboxSmmFlagAddr;

  Status                              = EFI_SUCCESS;
  PspMboxSmmBufferAddressProtocol     = NULL;
  PspMboxSmmBuffer                    = NULL;
  PspMboxSmmFlagAddr                  = NULL;

  IDS_HDT_CONSOLE (FCH_TRACE, "%a Start\n", __FUNCTION__);

  Status = gSmst->SmmLocateProtocol (
                    &gPspMboxSmmBufferAddressProtocolGuid,
                    NULL,
                    (VOID**)&PspMboxSmmBufferAddressProtocol
                    );
  ASSERT (!EFI_ERROR (Status));
  if ( Status == EFI_SUCCESS ) {
    PspMboxSmmBuffer   = PspMboxSmmBufferAddressProtocol->PspMboxSmmBuffer;
    PspMboxSmmFlagAddr = PspMboxSmmBufferAddressProtocol->PspMboxSmmFlagAddr;

    FchKLXhciDisablePortByPspMbox (
      1,
      (UINT8)(PcdGetBool (PcdXhci2Enable)) | (((UINT8)(PcdGetBool (PcdXhci3Enable)))<<1),
      (PcdGet32 (PcdXhciUsb2PortDisable) >> 4) & 0xF,
      (PcdGet32 (PcdXhciUsb3PortDisable) >> 4) & 0xF,
      PspMboxSmmBuffer,
      PspMboxSmmFlagAddr
      );
  } else {
    IDS_HDT_CONSOLE (FCH_TRACE, "Fail to get Psp Mbox SMM buffer.\n");
  }

  IDS_HDT_CONSOLE (FCH_TRACE, "%a End\n", __FUNCTION__);
}


EFI_STATUS
EFIAPI
FchMultiFchSmmReadyToBootCallBack (
  IN  CONST EFI_GUID    *Protocol,
  IN        VOID        *Interface,
  IN        EFI_HANDLE  Handle
  )
{
  IDS_HDT_CONSOLE (FCH_TRACE, "%a Start\n", __FUNCTION__);

  //
  // Disable USB port by PSP mail box if needed
  //
  if ( MultiFchDataInSmm.FchResetDataBlock.DisableXhciPortLate ) {
    FchSmmDisableUsbPort ();
  }

  IDS_HDT_CONSOLE (FCH_TRACE, "%a End\n", __FUNCTION__);
  return EFI_SUCCESS;
}


/**
 * @brief Entry point of the Kunlun multi FCH SMM Driver.
 *
 * @param[in] ImageHandle EFI Image Handle for the DXE driver
 * @param[in] SystemTable Pointer to the EFI system table
 *
 * @returns EFI_STATUS
 * @retval EFI_SUCCESS   Module initialized successfully
 * @retval EFI_ERROR     Initialization failed (see error for more details)
 */
EFI_STATUS
EFIAPI
MultiFchSmmInit (
  IN       EFI_HANDLE         ImageHandle,
  IN       EFI_SYSTEM_TABLE   *SystemTable
  )
{
  FCH_MULTI_FCH_INIT_PROTOCOL             *FchMfInit;
  EFI_STATUS                              Status;
  FCH_INIT_PROTOCOL                       *pFchCimxInitProtocol;

  UINTN                                   NumberOfInstalledProcessors;
  UINTN                                   TotalNumberOfDie;
  UINTN                                   TotalNumberOfRootBridges;
  AMD_FABRIC_TOPOLOGY_SERVICES2_PROTOCOL  *FabricTopology;
  VOID                                    *Registration;

  pFchCimxInitProtocol        = NULL;
  NumberOfInstalledProcessors = 0;
  TotalNumberOfDie            = 0;
  TotalNumberOfRootBridges    = 0;
  FabricTopology              = NULL;

  //
  // Check if socket1 exist
  //
  Status = gSmst->SmmLocateProtocol (
                    &gAmdFabricTopologyServices2SmmProtocolGuid,
                    NULL,
                    (VOID**)&FabricTopology
                    );
  ASSERT_EFI_ERROR ( Status);
  Status = FabricTopology->GetSystemInfo (
                             FabricTopology,
                             &NumberOfInstalledProcessors,
                             &TotalNumberOfDie,
                             &TotalNumberOfRootBridges,
                             NULL,
                             NULL
                             );
  ASSERT_EFI_ERROR ( Status);
  IDS_HDT_CONSOLE (
    FCH_TRACE,
    "[%a] NumberOfInstalledProcessors = 0x%x TotalNumberOfDie = 0x%x TotalNumberOfRootBridges = 0x%x\n",
    __FUNCTION__,
    NumberOfInstalledProcessors,
    TotalNumberOfDie,
    TotalNumberOfRootBridges
    );
  if (NumberOfInstalledProcessors == 1) {
    AGESA_TESTPOINT (TpFchMultiFchDxeExit, NULL);
    IDS_HDT_CONSOLE (FCH_TRACE, "[%a] No Socket1 FCH...Exit\n", __FUNCTION__);
    return EFI_SUCCESS;
  }

  Status = gBS->LocateProtocol (
                  &gFchMultiFchInitProtocolGuid,
                  NULL,
                  (VOID **)&FchMfInit
                  );
  ASSERT_EFI_ERROR (Status);

  Status = gSmst->SmmLocateProtocol (
                  &gFchSmmInitProtocolGuid,
                  NULL,
                  (VOID **)&pFchSmmInitProtocol
                  );
  if (EFI_ERROR (Status)) {
    return Status;
  }

  Status = gBS->LocateProtocol (
                  &gFchInitProtocolGuid,
                  NULL,
                  (VOID **)&pFchCimxInitProtocol
                  );
  ASSERT_EFI_ERROR (Status);
  if (!EFI_ERROR (Status) && pFchCimxInitProtocol != NULL ) {
    CopyMem (
      &MultiFchDataInSmm,
      pFchCimxInitProtocol->FchPolicy,
      sizeof (FCH_DATA_BLOCK)
      );
  } else {
    return EFI_ABORTED;
  }

  SmmPcdSataEnable2              = PcdGet8 (PcdSataEnable2);
  SmmPcdSataMultiDiePortShutDown = PcdGet64 (PcdSataMultiDiePortShutDown);
  SmmPcdSataMultiDiePortESP      = PcdGet64 (PcdSataMultiDiePortESP);
  SmmPcdSataMultiDieDevSlp       = PcdGet64 (PcdSataMultiDieDevSlp);
  SmmPcdSataSgpioMultiDieEnable  = PcdGet8 (PcdSataSgpioMultiDieEnable);
  SmmPcdSataIoDie1PortMode       = PcdGet64 (PcdSataIoDie1PortMode);

  Socket1FchBusNum = (UINT32) FchSmmGetSocket1Bus ();
  IDS_HDT_CONSOLE (FCH_TRACE, "[%a] Socket1 Bus Number = 0x%x\n", __FUNCTION__, Socket1FchBusNum);

  gBS->CopyMem (
         &FchSmmMfData,
         &FchMfInit->FchMfData,
         sizeof (FCH_MULITI_FCH_DATA_BLOCK)
         );

  Status = MultiFchSmmRegisterSxSmi ();

  Status = MultiFchSmmRegisterSwSmi ();

  //
  // Disable USB port by PSP mail box if needed
  //
  if ( !MultiFchDataInSmm.FchResetDataBlock.DisableXhciPortLate ) {
    FchSmmDisableUsbPort ();
  }

  gSmst->SmmRegisterProtocolNotify (
           &gEdkiiSmmReadyToBootProtocolGuid,
           FchMultiFchSmmReadyToBootCallBack,
           &Registration
           );

  return Status;
}

EFI_STATUS
MultiFchSmmRegisterSxSmi (
  VOID
  )
{
  EFI_STATUS                               Status;
  FCH_SMM_SX_DISPATCH2_PROTOCOL            *AmdSxDispatch;
  FCH_SMM_SX_REGISTER_CONTEXT              SxRegisterContext;
  EFI_HANDLE                               SxHandle;

  //
  // Register AMD SX SMM
  //
  Status = gSmst->SmmLocateProtocol (
                  &gFchSmmSxDispatch2ProtocolGuid,
                  NULL,
                  (VOID **)&AmdSxDispatch
                  );
  ASSERT_EFI_ERROR (Status);

  SxRegisterContext.Type  = SxS3;
  SxRegisterContext.Phase = SxEntry;
  SxRegisterContext.Order = 0x90;
  Status = AmdSxDispatch->Register (
                             AmdSxDispatch,
                             MultiFchS3SleepEntryCallback,
                             &SxRegisterContext,
                             &SxHandle
                             );

  SxRegisterContext.Type  = SxS4;
  SxRegisterContext.Phase = SxEntry;
  SxRegisterContext.Order = 0x90;

  Status = AmdSxDispatch->Register (
                             AmdSxDispatch,
                             MultiFchS4SleepEntryCallback,
                             &SxRegisterContext,
                             &SxHandle
                             );


  return Status;
}

EFI_STATUS
MultiFchSmmRegisterSwSmi (
  VOID
  )
{
  EFI_STATUS                               Status;
  FCH_SMM_SW_DISPATCH2_PROTOCOL            *AmdSwDispatch;
  FCH_SMM_SW_REGISTER_CONTEXT              SwRegisterContext;
  EFI_HANDLE                               SwHandle;

  //
  //  Locate SMM SW dispatch protocol
  //
  Status = gSmst->SmmLocateProtocol (
                  &gFchSmmSwDispatch2ProtocolGuid,
                  NULL,
                  (VOID **)&AmdSwDispatch
                  );
  ASSERT_EFI_ERROR (Status);

  SwRegisterContext.AmdSwValue  = PcdGet8 (PcdFchOemBeforePciRestoreSwSmi); // use of PCD in place of FCHOEM_BEFORE_PCI_RESTORE_SWSMI    0xD3
  if (SwRegisterContext.AmdSwValue != 0) {
    SwRegisterContext.Order       = 0x90;
    Status = AmdSwDispatch->Register (
                              AmdSwDispatch,
                              MultiFchBeforePciS3RestoreCallback,
                              &SwRegisterContext,
                              &SwHandle
                              );
    if (EFI_ERROR (Status)) {
      return Status;
    }
  }

  SwRegisterContext.AmdSwValue  = PcdGet8 (PcdFchOemAfterPciRestoreSwSmi); // use of PCD in place of FCHOEM_AFTER_PCI_RESTORE_SWSMI    0xD4
  if (SwRegisterContext.AmdSwValue != 0) {
    SwRegisterContext.Order       = 0x90;
    Status = AmdSwDispatch->Register (
                              AmdSwDispatch,
                              MultiFchAfterPciS3RestoreCallback,
                              &SwRegisterContext,
                              &SwHandle
                              );
    if (EFI_ERROR (Status)) {
      return Status;
    }
  }

#if FCH_CAPTURE_RELEASE_SPD_BUS
  SwRegisterContext.AmdSwValue  = PcdGet8 (PcdFchOemCaptureSPDBusSmi);
  SwRegisterContext.Order       = 0x90;
  Status = AmdSwDispatch->Register (
                            AmdSwDispatch,
                            MultiFchOemCaptureSPDBusSmiCallback,
                            &SwRegisterContext,
                            &SwHandle
                            );
  if (EFI_ERROR (Status)) {
    return Status;
  }
  SwRegisterContext.AmdSwValue  = PcdGet8 (PcdFchOemReleaseSPDBusSmi);
  SwRegisterContext.Order       = 0x90;
  Status = AmdSwDispatch->Register (
                            AmdSwDispatch,
                            MultiFchOemReleaseSPDBusSmiCallback,
                            &SwRegisterContext,
                            &SwHandle
                            );
  if (EFI_ERROR (Status)) {
    return Status;
  }
#endif

  return Status;
}


/**
 * @brief Handler for slave die sleep S3.
 *
 * @param[in] DispatchHandle   The handle of this callback, obtained when registering
 * @param[in] DispatchContext  Pointer to the FCH_SMM_SX_REGISTER_CONTEXT
 * @param[in] CommBuffer       Pointer to the communication buffer
 * @param[in] CommBufferSize   Pointer to the communication buffer size
 *
 * @returns EFI_STATUS
 * @retval EFI_SUCCESS   Module initialized successfully
 */
EFI_STATUS
EFIAPI
MultiFchS3SleepEntryCallback (
  IN       EFI_HANDLE                        DispatchHandle,
  IN       CONST FCH_SMM_SX_REGISTER_CONTEXT *DispatchContext,
  IN OUT   VOID                              *CommBuffer OPTIONAL,
  IN OUT   UINTN                             *CommBufferSize  OPTIONAL
  )
{
  UINT32          DieBusNum32;
  FCH_DATA_BLOCK  *pFchPolicy;

  DieBusNum32     = (UINT32) Socket1FchBusNum;
  pFchPolicy      = &MultiFchDataInSmm;

  FchKLXhciInitS3EntryProgram (DieBusNum32, pFchPolicy);

  if ( pFchPolicy->HwAcpi.FchSxEntryXhciPmeEn ) {
    FchProgramXhciPmeEn (
      DieBusNum32,
      PcdGetBool (PcdXhci2Enable),
      PcdGetBool (PcdXhci3Enable)
      );
  }

  return EFI_SUCCESS;
}


/**
 * @brief Handler for slave die sleep S4.
 *
 * @param[in] DispatchHandle   The handle of this callback, obtained when registering
 * @param[in] DispatchContext  Pointer to the FCH_SMM_SX_REGISTER_CONTEXT
 * @param[in] CommBuffer       Pointer to the communication buffer
 * @param[in] CommBufferSize   Pointer to the communication buffer size
 *
 * @returns EFI_STATUS
 * @retval EFI_SUCCESS   Module initialized successfully
 */
EFI_STATUS
EFIAPI
MultiFchS4SleepEntryCallback (
  IN       EFI_HANDLE                        DispatchHandle,
  IN       CONST FCH_SMM_SX_REGISTER_CONTEXT *DispatchContext,
  IN OUT   VOID                              *CommBuffer OPTIONAL,
  IN OUT   UINTN                             *CommBufferSize  OPTIONAL
  )
{
  UINT32          DieBusNum32;
  FCH_DATA_BLOCK  *pFchPolicy;

  DieBusNum32     = (UINT32) Socket1FchBusNum;
  pFchPolicy      = &MultiFchDataInSmm;

  FchKLXhciInitS3EntryProgram (DieBusNum32, pFchPolicy);

  if ( pFchPolicy->HwAcpi.FchSxEntryXhciPmeEn ) {
    FchProgramXhciPmeEn (
      DieBusNum32,
      PcdGetBool (PcdXhci2Enable),
      PcdGetBool (PcdXhci3Enable)
      );
  }

  return EFI_SUCCESS;
}


/**
 * @brief Handler for Slave Fch before PCI S3 restore.
 *
 * @param[in] DispatchHandle   The handle of this callback, obtained when registering
 * @param[in] DispatchContext  Pointer to the FCH_SMM_SW_REGISTER_CONTEXT
 * @param[in] SwContext        Pointer to the FCH_SMM_SW_CONTEXT context buffer
 * @param[in] SizeOfSwContext  Pointer to the context size
 *
 * @returns EFI_STATUS
 * @retval EFI_SUCCESS   Module initialized successfully
 * @retval EFI_ERROR     Initialization failed (see error for more details)
 */
EFI_STATUS
EFIAPI
MultiFchBeforePciS3RestoreCallback (
  IN       EFI_HANDLE                        DispatchHandle,
  IN       CONST FCH_SMM_SW_REGISTER_CONTEXT *DispatchContext,
  IN OUT   FCH_SMM_SW_CONTEXT                *SwContext,
  IN OUT   UINTN                             *SizeOfSwContext
  )
{
  FCH_DATA_BLOCK      FchData;
  FCH_DATA_BLOCK      *FchDataPtr;
  EFI_STATUS          Status;
  UINT8               SataController;
  UINT8               SataEnable2;
  UINT8               SataSgpioMultiDieEnable;
  UINT64              SataMultiDiePortShutDown;
  UINT64              SataMultiDiePortESP;
  UINT64              SataMultiDieDevSlp;
  UINT64              SataPortMode;
  UINT8               DevSlp0ControllerNum;
  UINT8               DevSlp1ControllerNum;

  Status = EFI_SUCCESS;

  SataEnable2              = SmmPcdSataEnable2 >> 4;
  SataMultiDiePortShutDown = SmmPcdSataMultiDiePortShutDown >> 32;
  SataMultiDiePortESP      = SmmPcdSataMultiDiePortESP >> 32;
  SataMultiDieDevSlp       = SmmPcdSataMultiDieDevSlp;
  SataSgpioMultiDieEnable  = SmmPcdSataSgpioMultiDieEnable >> 4;
  SataPortMode             = SmmPcdSataIoDie1PortMode;

  FchDataPtr = &FchData;
  CopyMem (FchDataPtr, pFchSmmInitProtocol->FchSmmPolicy, sizeof (FCH_DATA_BLOCK));

  //
  // Update local Data Structure
  //
  for (SataController = 0; SataController < KUNLUN_SATA_CONTROLLER_NUM; SataController++) {
    FchDataPtr->Sata[SataController].SataEnable    = (SataEnable2 >> SataController) & BIT0;
    FchDataPtr->Sata[SataController].SataEspPort   = (UINT8)(SataMultiDiePortESP >> (8 * SataController));
    FchDataPtr->Sata[SataController].SataPortPower = (UINT8)(SataMultiDiePortShutDown >> (8 * SataController));
    FchDataPtr->Sata[SataController].SataPortMd    = (UINT16)(SataPortMode >> (16 * SataController));
    FchDataPtr->Sata[SataController].SataDevSlpPort0  = FALSE;
    FchDataPtr->Sata[SataController].SataDevSlpPort1  = FALSE;
  }
  FchDataPtr->Sata[0].SataSgpio0                   = (UINT8)(SataSgpioMultiDieEnable & BIT0);

  if ((SataMultiDieDevSlp & BIT0) == BIT0) {
    DevSlp0ControllerNum = ((SataMultiDieDevSlp & 0xF0) >> 4) - 4; // Note: Second socket controller number starts from 4
    FchDataPtr->Sata[DevSlp0ControllerNum].SataDevSlpPort0 = TRUE;
    FchDataPtr->Sata[DevSlp0ControllerNum].SataDevSlpPort0Num = (SataMultiDieDevSlp & 0x0F) >> 1;
  }

  if ((SataMultiDieDevSlp & BIT8) == BIT8) {
    DevSlp1ControllerNum = ((SataMultiDieDevSlp & 0xF000) >> 12) - 4; // Note: Second socket controller number starts from 4
    FchDataPtr->Sata[DevSlp1ControllerNum].SataDevSlpPort1 = TRUE;
    FchDataPtr->Sata[DevSlp1ControllerNum].SataDevSlpPort1Num = (SataMultiDieDevSlp & 0xF00) >> 9;
  }

  FchDataPtr->HwAcpi.FchAcpiMmioBase               = (UINT32) FchSmmMfData.FchAcpiMmioBase[1];

  //USB
  FchKLXhciIohcPmeDisable (Socket1FchBusNum, TRUE);
  //SATA
  MultiFchSataInitSmm (1, Socket1FchBusNum, FchDataPtr);

  return Status;
}


/**
 * @brief Handler for Slave Fch after PCI S3 restore.
 *
 * @param[in] DispatchHandle   The handle of this callback, obtained when registering
 * @param[in] DispatchContext  Pointer to the FCH_SMM_SW_REGISTER_CONTEXT
 * @param[in] SwContext        Pointer to the FCH_SMM_SW_CONTEXT context buffer
 * @param[in] SizeOfSwContext  Pointer to the context size
 *
 * @returns EFI_STATUS
 * @retval EFI_SUCCESS   Module initialized successfully
 * @retval EFI_ERROR     Initialization failed (see error for more details)
 */
EFI_STATUS
EFIAPI
MultiFchAfterPciS3RestoreCallback (
  IN       EFI_HANDLE                        DispatchHandle,
  IN       CONST FCH_SMM_SW_REGISTER_CONTEXT *DispatchContext,
  IN OUT   FCH_SMM_SW_CONTEXT                *SwContext,
  IN OUT   UINTN                             *SizeOfSwContext
  )
{
  FCH_DATA_BLOCK      FchData;
  FCH_DATA_BLOCK      *FchDataPtr;
  EFI_STATUS          Status;
  UINT8               SataController;
  UINT8               SataEnable2;
  UINT8               SataSgpioMultiDieEnable;
  UINT64              SataMultiDiePortShutDown;
  UINT64              SataMultiDiePortESP;
  UINT64              SataMultiDieDevSlp;
  UINT64              SataPortMode;
  UINT8               DevSlp0ControllerNum;
  UINT8               DevSlp1ControllerNum;

  Status = EFI_SUCCESS;

  SataEnable2              = SmmPcdSataEnable2 >> 4;
  SataMultiDiePortShutDown = SmmPcdSataMultiDiePortShutDown >> 32;
  SataMultiDiePortESP      = SmmPcdSataMultiDiePortESP >> 32;
  SataMultiDieDevSlp       = SmmPcdSataMultiDieDevSlp;
  SataSgpioMultiDieEnable  = SmmPcdSataSgpioMultiDieEnable >> 4;
  SataPortMode             = SmmPcdSataIoDie1PortMode;

  FchDataPtr = &FchData;
  CopyMem (FchDataPtr, pFchSmmInitProtocol->FchSmmPolicy, sizeof (FCH_DATA_BLOCK));

  //
  // Update local Data Structure
  //
  for (SataController = 0; SataController < KUNLUN_SATA_CONTROLLER_NUM; SataController++) {
    FchDataPtr->Sata[SataController].SataEnable       = (SataEnable2 >> SataController) & BIT0;
    FchDataPtr->Sata[SataController].SataEspPort      = (UINT8)(SataMultiDiePortESP >> (8 * SataController));
    FchDataPtr->Sata[SataController].SataPortPower    = (UINT8)(SataMultiDiePortShutDown >> (8 * SataController));
    FchDataPtr->Sata[SataController].SataPortMd       = (UINT16)(SataPortMode >> (16 * SataController));
    FchDataPtr->Sata[SataController].SataSgpio0       = (UINT8)((SataSgpioMultiDieEnable >> SataController) & BIT0);
    FchDataPtr->Sata[SataController].SataUBMDiagMode  = PcdGetBool (PcdSataUBMDiagMode);
    FchDataPtr->Sata[SataController].SataDevSlpPort0  = FALSE;
    FchDataPtr->Sata[SataController].SataDevSlpPort1  = FALSE;
  }
  FchDataPtr->Sata[0].SataSgpio0                   = (UINT8)(SataSgpioMultiDieEnable & BIT0);
  FchDataPtr->HwAcpi.FchAcpiMmioBase               = (UINT32) FchSmmMfData.FchAcpiMmioBase[1];

  if ((SataMultiDieDevSlp & BIT0) == BIT0) {
    DevSlp0ControllerNum = ((SataMultiDieDevSlp & 0xF0) >> 4) - 4; // Note: Second socket controller number starts from 4
    FchDataPtr->Sata[DevSlp0ControllerNum].SataDevSlpPort0 = TRUE;
    FchDataPtr->Sata[DevSlp0ControllerNum].SataDevSlpPort0Num = (SataMultiDieDevSlp & 0x0F) >> 1;
  }

  if ((SataMultiDieDevSlp & BIT8) == BIT8) {
    DevSlp1ControllerNum = ((SataMultiDieDevSlp & 0xF000) >> 12) - 4; // Note: Second socket controller number starts from 4
    FchDataPtr->Sata[DevSlp1ControllerNum].SataDevSlpPort1 = TRUE;
    FchDataPtr->Sata[DevSlp1ControllerNum].SataDevSlpPort1Num = (SataMultiDieDevSlp & 0xF00) >> 9;
  }

  //SATA
  MultiFchSataInit2Smm (1, Socket1FchBusNum, FchDataPtr);

  return Status;
}

VOID
MultiFchSataInitSmm (
  IN  UINT8       Die,
  IN  UINT32      DieBusNum,
  IN  VOID        *FchDataPtr
  )
{
  UINT8                     SataController;
  UINT32                    DieBusNum32;
  FCH_DATA_BLOCK            *LocalCfgPtr;

  DieBusNum32 = (UINT32)DieBusNum;
  LocalCfgPtr = (FCH_DATA_BLOCK *)FchDataPtr;

  for (SataController = 0; SataController < KUNLUN_SATA_CONTROLLER_NUM; SataController++) {
    //
    // Check if Sata is enabled by NBIO
    //
    //if (!FchKLSataInitCheckSataPci (DieBusNum32, SataController, FchDataPtr)) {
    //  LocalCfgPtr->Sata[SataController].SataEnable = FALSE;
    //}

    if (LocalCfgPtr->Sata[SataController].SataEnable) {
      SataEnableWriteAccessKL (DieBusNum32, SataController);
      FchKLInitEnvProgramSata (DieBusNum32, SataController, FchDataPtr);

      /*
      Because of security consideration, x86 is forbidden to access nBIF straps.
      Move code to ABL.

      //
      // Call Sub-function for each Sata mode
      //
      if (( LocalCfgPtr->Sata[SataController].SataClass == SataAhci7804) || (LocalCfgPtr->Sata[SataController].SataClass == SataAhci )) {
        FchInitEnvSataAhciKL ( DieBusNum32, SataController, FchDataPtr );
      }

      if ( LocalCfgPtr->Sata[SataController].SataClass == SataRaid) {
        FchInitEnvSataRaidKL ( DieBusNum32, SataController, FchDataPtr );
      }
      */

      if ( LocalCfgPtr->Sata[SataController].SataClass == SataRaid) {
        FchKLSataShutdownUnconnectedSataPortClock (DieBusNum, SataController, FchDataPtr);
      }

      SataDisableWriteAccessKL (DieBusNum32, SataController);
    } else {
      FchKLSataInitDisableSata (DieBusNum32, SataController, FchDataPtr);
      /*
      Because of security consideration, x86 is forbidden to access nBIF straps.
      Move code to ABL.
      FchKLSataInitHideSataPci (DieBusNum32, SataController, FchDataPtr);
      */
    }
  }
}

VOID
MultiFchSataInit2Smm (
  IN  UINT8       Die,
  IN  UINT32      DieBusNum,
  IN  VOID        *FchDataPtr
  )
{
  UINT8                     SataController;
  UINT32                    DieBusNum32;
  FCH_DATA_BLOCK            *LocalCfgPtr;

  DieBusNum32 = (UINT32)DieBusNum;
  LocalCfgPtr = (FCH_DATA_BLOCK *)FchDataPtr;

  for (SataController = 0; SataController < KUNLUN_SATA_CONTROLLER_NUM; SataController++) {
    //
    // Check if Sata is enabled by NBIO
    //
    if (LocalCfgPtr->Sata[SataController].SataEnable) {
      SataEnableWriteAccessKL (DieBusNum32, SataController);
      //
      // Call Sub-function for each Sata mode
      //
      if (( LocalCfgPtr->Sata[SataController].SataClass == SataAhci7804) || (LocalCfgPtr->Sata[SataController].SataClass == SataAhci )) {
        FchInitLateSataAhciKL ( DieBusNum32, SataController, FchDataPtr );
      }

      if ( LocalCfgPtr->Sata[SataController].SataClass == SataRaid) {
        FchInitLateSataRaidKL ( DieBusNum32, SataController, FchDataPtr );
      }

      FchKLInitLateProgramSataRegs (DieBusNum32, SataController, FchDataPtr);
      SataDisableWriteAccessKL (DieBusNum32, SataController);
    }
  }
}


#if FCH_CAPTURE_RELEASE_SPD_BUS
EFI_STATUS
EFIAPI
MultiFchOemCaptureSPDBusSmiCallback (
  IN       EFI_HANDLE                        DispatchHandle,
  IN       CONST FCH_SMM_SW_REGISTER_CONTEXT *DispatchContext,
  IN OUT   FCH_SMM_SW_CONTEXT                *SwContext,
  IN OUT   UINTN                             *SizeOfSwContext
  )
{
  EFI_STATUS      Status;
  PCI_ADDR        NbioPciAddress;
  UINT32          SmuArg[6];
  UINT32          SmuStatus;
  UINT32          BusNumberBase;

  Status                      = EFI_SUCCESS;
  SmuStatus                   = 0;
  NbioPciAddress.AddressValue = 0;
  BusNumberBase               = 0;

  IDS_HDT_CONSOLE (FCH_TRACE, "%a - Entry\n", __FUNCTION__);

  // Stop PMFW Telemetry
  if ( !MultiFchDataInSmm.HwAcpi.SpdHostCtrlRelease && MultiFchDataInSmm.HwAcpi.DimmTelemetry ) {
    IDS_HDT_CONSOLE (FCH_TRACE, "Send message to SMU for PMFW stop polling DIMM telemetry.\n");
    BusNumberBase = (UINT32) FchSmmGetSocket1Bus ();
    IDS_HDT_CONSOLE (FCH_TRACE, "BusNumberBase = 0x%x\n", BusNumberBase);

    NbioSmuServiceCommonInitArguments (SmuArg);
    NbioPciAddress.AddressValue = MAKE_SBDFO (DF_GET_SEGMENT (BusNumberBase), DF_GET_BUS (BusNumberBase), 0, 0, 0);
    if ( MultiFchDataInSmm.FchRunTime.FchDeviceEnableMap & BIT21 ) {
      SmuArg[0] = 1;  // Use I3C controller
    } else if ( MultiFchDataInSmm.FchRunTime.FchDeviceEnableMap & BIT5 ) {
      SmuArg[0] = 0;  // Use I2C controller
    }

    SmuStatus = NbioSmuServiceRequest (
                  NbioPciAddress,
                  BIOSSMC_MSG_StopDimmTelemetryReading,
                  SmuArg,
                  0
                  );
    if ( BIOSSMC_Result_OK == SmuStatus ) {
      IDS_HDT_CONSOLE (FCH_TRACE, "Send message to SMU successfully.\n");
    } else {
      IDS_HDT_CONSOLE (FCH_TRACE, "Send message to SMU failed.\n");
      Status = EFI_DEVICE_ERROR;
    }
  }

  IDS_HDT_CONSOLE (FCH_TRACE, "%a - Exit\n", __FUNCTION__);
  return Status;
}


EFI_STATUS
EFIAPI
MultiFchOemReleaseSPDBusSmiCallback (
  IN       EFI_HANDLE                        DispatchHandle,
  IN       CONST FCH_SMM_SW_REGISTER_CONTEXT *DispatchContext,
  IN OUT   FCH_SMM_SW_CONTEXT                *SwContext,
  IN OUT   UINTN                             *SizeOfSwContext
  )
{
  EFI_STATUS      Status;
  PCI_ADDR        NbioPciAddress;
  UINT32          SmuArg[6];
  UINT32          SmuStatus;
  UINT32          BusNumberBase;

  Status                      = EFI_SUCCESS;
  SmuStatus                   = 0;
  NbioPciAddress.AddressValue = 0;
  BusNumberBase               = 0;

  IDS_HDT_CONSOLE (FCH_TRACE, "%a - Entry\n", __FUNCTION__);

  if ( !MultiFchDataInSmm.HwAcpi.SpdHostCtrlRelease && MultiFchDataInSmm.HwAcpi.DimmTelemetry ) {
    IDS_HDT_CONSOLE (FCH_TRACE, "Send message to SMU for PMFW resume polling DIMM telemetry.\n");
    BusNumberBase = (UINT32) FchSmmGetSocket1Bus ();
    IDS_HDT_CONSOLE (FCH_TRACE, "BusNumberBase = 0x%x\n", BusNumberBase);

    NbioSmuServiceCommonInitArguments (SmuArg);
    NbioPciAddress.AddressValue = MAKE_SBDFO (DF_GET_SEGMENT (BusNumberBase), DF_GET_BUS (BusNumberBase), 0, 0, 0);
    if ( MultiFchDataInSmm.FchRunTime.FchDeviceEnableMap & BIT21 ) {
      SmuArg[0] = 1;  // Use I3C controller
    } else if ( MultiFchDataInSmm.FchRunTime.FchDeviceEnableMap & BIT5 ) {
      SmuArg[0] = 0;  // Use I2C controller
    }

    SmuStatus = NbioSmuServiceRequest (
                  NbioPciAddress,
                  BIOSSMC_MSG_StartDimmTelemetryReading,
                  SmuArg,
                  0
                  );
    if ( BIOSSMC_Result_OK == SmuStatus ) {
      IDS_HDT_CONSOLE (FCH_TRACE, "Send message to SMU successfully.\n");
    } else {
      IDS_HDT_CONSOLE (FCH_TRACE, "Send message to SMU failed.\n");
      Status = EFI_DEVICE_ERROR;
    }
  }

  IDS_HDT_CONSOLE (FCH_TRACE, "%a - Exit\n", __FUNCTION__);
  return Status;
}
#endif


