/*****************************************************************************
 *
 * Copyright (C) 2008-2024 Advanced Micro Devices, Inc. All rights reserved.
 *
 *******************************************************************************
 **/

#ifndef _CXLIO_H_
#define _CXLIO_H_


/***********************************************************
* Register Name : CXLIO_SCRATCH
************************************************************/

#define CXLIO_SCRATCH_CXLIO_SCRATCH_OFFSET                     0
#define CXLIO_SCRATCH_CXLIO_SCRATCH_MASK                       0xffffffff

typedef union {
  struct {
    UINT32                                       CXLIO_SCRATCH:32;
  } Field;
  UINT32 Value;
} CXLIO_SCRATCH_STRUCT;
#define SMN_FUNC0_PCIE0NBIO0_CXLIO_SCRATCH_ADDRESS                    0x80000004UL
#define SMN_FUNC0_PCIE0NBIO1_CXLIO_SCRATCH_ADDRESS                    0x80200004UL
#define SMN_FUNC0_PCIE3NBIO0_CXLIO_SCRATCH_ADDRESS                    0x80500004UL
#define SMN_FUNC0_PCIE3NBIO1_CXLIO_SCRATCH_ADDRESS                    0x80700004UL
#define SMN_FUNC1_PCIE0NBIO0_CXLIO_SCRATCH_ADDRESS                    0x80010004UL
#define SMN_FUNC1_PCIE0NBIO1_CXLIO_SCRATCH_ADDRESS                    0x80210004UL
#define SMN_FUNC1_PCIE3NBIO0_CXLIO_SCRATCH_ADDRESS                    0x80510004UL
#define SMN_FUNC1_PCIE3NBIO1_CXLIO_SCRATCH_ADDRESS                    0x80710004UL
#define SMN_FUNC2_PCIE0NBIO0_CXLIO_SCRATCH_ADDRESS                    0x80020004UL
#define SMN_FUNC2_PCIE0NBIO1_CXLIO_SCRATCH_ADDRESS                    0x80220004UL
#define SMN_FUNC2_PCIE3NBIO0_CXLIO_SCRATCH_ADDRESS                    0x80520004UL
#define SMN_FUNC2_PCIE3NBIO1_CXLIO_SCRATCH_ADDRESS                    0x80720004UL
#define SMN_FUNC3_PCIE0NBIO0_CXLIO_SCRATCH_ADDRESS                    0x80030004UL
#define SMN_FUNC3_PCIE0NBIO1_CXLIO_SCRATCH_ADDRESS                    0x80230004UL
#define SMN_FUNC3_PCIE3NBIO0_CXLIO_SCRATCH_ADDRESS                    0x80530004UL
#define SMN_FUNC3_PCIE3NBIO1_CXLIO_SCRATCH_ADDRESS                    0x80730004UL

#endif /* _CXLIO_H_ */

