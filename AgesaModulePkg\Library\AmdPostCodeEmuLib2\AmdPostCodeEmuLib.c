/*****************************************************************************
 * Copyright (C) 2008-2024 Advanced Micro Devices, Inc. All rights reserved.
 *
*****************************************************************************
*/

/// NOTE, to optimize the performance, this library will cache some value to global variable
/// Which may conflict with some platform requirement "keep PEI memory intact"
/// If your platform has this requirement, please consider using "AmdPostCodeEmuLib\AmdPostCodeEmuLib.inf"
 /*----------------------------------------------------------------------------------------
 *                             M O D U L E S    U S E D
 *----------------------------------------------------------------------------------------
 */
#include <Uefi.h>
#include <Library/AmdBaseLib.h>
#include <Library/BaseLib.h>
#include <Library/PciLib.h>
#include <Library/IoLib.h>
#include <Library/IdsLib.h>
#include <Library/PcdLib.h>
#include <Filecode.h>

#define FILECODE  LIBRARY_IDSMISCLIB_IDSMISCLIB_FILECODE
#define NBCFG_SCRATCH_0_SMN_ADDR                  0x13B00068
#define MP0_C2PMSG_97                             0x03810A84ul
#define L3_CORE_CPLB_SCRATCHCMDREG0               0xC0013398
#define POSTCODE_SCRATCH_MSR_ADDR                 L3_CORE_CPLB_SCRATCHCMDREG0
#define NB_SMN_INDEX_2_PCI_ADDR (PCI_LIB_ADDRESS (0, 0, 0, 0xA0))  ///< PCI Addr of NB_SMN_INDEX_1
#define NB_SMN_DATA_2_PCI_ADDR (PCI_LIB_ADDRESS (0, 0, 0, 0xA4))  ///< PCI Addr of NB_SMN_DATA_1
#define POSTCODE_BY_MSR_MASK                BIT18
#define POSTCODE_BY_SMN_MASK                BIT15

/// the value 0xFF indicates the variable gCachedC2P97RegValue is not initialized
#define UNINITIALIZED_CACHED_C2P97_REGVAL   0xFF
#define INVALID_C2P97_REGVAL                0xFFFFFFFF

STATIC UINT32 gCachedC2P97RegValue = UNINITIALIZED_CACHED_C2P97_REGVAL;

/**
  Light version SMN read with less depx, SmnAccessLib still have too many depex

  @param[in]  Address, SMN address

**/
STATIC
UINT32
SmnRegRead (
  UINT32 Address
  )
{
  UINTN                  PciAddress;

  PciAddress = NB_SMN_INDEX_2_PCI_ADDR;
  PciWrite32 (PciAddress, Address);
  PciAddress = NB_SMN_DATA_2_PCI_ADDR;
  return PciRead32 (PciAddress);
}

/**
  Light version SMN write with less depx

  @param[in]  Address, SMN address
  @param[in]  Value, Value to be writen to the address

**/
STATIC
VOID
SmnRegWriteWidth (
  IN        ACCESS_WIDTH AccessWidth,
  UINT32    Address,
  IN        VOID *Value
  )
{
  UINTN                  PciAddress;
  UINT32                 Value32;
  Value32 = 0;
  switch (AccessWidth) {
  case AccessWidth8:
  case AccessS3SaveWidth8:
    Value32 |= *(UINT8 *)Value;
    break;
  case AccessWidth16:
  case AccessS3SaveWidth16:
    Value32 |= *(UINT16 *)Value;
    break;
  case AccessWidth32:
  case AccessS3SaveWidth32:
    Value32 |= *(UINT32 *)Value;
    break;
  default:
    Value32 |= *(UINT32 *)Value;
  }
  PciAddress = NB_SMN_INDEX_2_PCI_ADDR;
  PciWrite32 (PciAddress, Address);
  PciAddress = NB_SMN_DATA_2_PCI_ADDR;
  PciWrite32 (PciAddress, Value32);
}


/**
  Light version IO write with less depx

  @param[in]  Address, IO address
  @param[in]  Value, Value to be writen to the address

**/
STATIC
VOID
IoWriteWidth (
  IN        ACCESS_WIDTH AccessWidth,
  UINT32    Address,
  IN        VOID *Value
  )
{
  switch (AccessWidth) {
  case AccessWidth8:
  case AccessS3SaveWidth8:
    IoWrite8 (Address, *(UINT8 *)Value);
    break;
  case AccessWidth16:
  case AccessS3SaveWidth16:
    IoWrite16 (Address, *(UINT16 *)Value);
    break;
  case AccessWidth32:
  case AccessS3SaveWidth32:
    IoWrite32 (Address, *(UINT32 *)Value);
    break;
  default:
    IoWrite32 (Address, *(UINT32 *)Value);
  }

}

/**
 * @brief Output Postcode to IO Port PcdIdsDebugPort specified, output to NBCFG_SCRATCH_0 in emulation case
 *
 * @param[in] AccessWidth   Access width
 * @param[in] Value         Pointer to data
 * @return VOID
 */
VOID
LibAmdPostCode (
  IN       ACCESS_WIDTH AccessWidth,
  IN       VOID *Value
  )
{
  UINT32                C2P97RegVal;

  C2P97RegVal = 0;
  //This library will be called even before HOB service established, use direct C2P register read instead of "EmulationFlagCheck"
  // UINT32 Port80Redirect:1;               ///< BIT15 1= Redirect the port80 writes to NBCFG_SCRATCH_0
  if (gCachedC2P97RegValue == UNINITIALIZED_CACHED_C2P97_REGVAL) {
    C2P97RegVal = SmnRegRead (MP0_C2PMSG_97);
    if (C2P97RegVal == INVALID_C2P97_REGVAL) {                  //If get invalid value 0xFFFFFFFF
      C2P97RegVal = 0;                                          //Force C2P97RegVal to 0 so output to IO port 80 only
      gCachedC2P97RegValue = UNINITIALIZED_CACHED_C2P97_REGVAL; //and keep gCachedC2P97RegValue as uninitialized value
    } else {
      C2P97RegVal = (C2P97RegVal & POSTCODE_BY_SMN_MASK) | (C2P97RegVal & POSTCODE_BY_MSR_MASK);
      gCachedC2P97RegValue = C2P97RegVal;    //Cache the configuration to global variable to optimize performance
    }
  } else {
    C2P97RegVal = gCachedC2P97RegValue;
  }

  if (C2P97RegVal & POSTCODE_BY_MSR_MASK) {
    switch (AccessWidth) {
    case AccessWidth32:
    case AccessS3SaveWidth32:
      AsmWriteMsr64 (POSTCODE_SCRATCH_MSR_ADDR, (UINT64) *(UINT32*) Value);
      break;
    default:
      AsmWriteMsr64 (POSTCODE_SCRATCH_MSR_ADDR, (UINT64) *(UINT8*) Value);
    }
  } else if (C2P97RegVal & POSTCODE_BY_SMN_MASK) {
    SmnRegWriteWidth (AccessWidth, NBCFG_SCRATCH_0_SMN_ADDR, Value);
  } else {
    IoWriteWidth (AccessWidth, PcdGet16 (PcdIdsDebugPort), Value);
  }
}

