/*****************************************************************************
 *
 * Copyright (C) 2008-2024 Advanced Micro Devices, Inc. All rights reserved.
 *
 *******************************************************************************
 */

/**
 * @file
 *
 * Implement Custom Core Pstates
 *
 * Contains code that Custom Core Pstates
 *
 * @xrefitem bom "File Content Label" "Release Content"
 * @e project:      AGESA
 * @e sub-project:  Library
 * @e \$Revision$   @e \$Date$
 *
 */

/*****************************************************************************
 *
 * This software package can be used to enable the Overclocking of certain
 * AMD processors and its use is subject to the terms and conditions of the
 * AMD Overclocking Waiver. Enabling overclocking through use of the low-level
 * routines included in this package and operating an AMD processor outside of
 * the applicable AMD product specifications will void any AMD warranty and can
 * result in damage to the processor or the system into which the processor has
 * been integrated. The user of this software assumes, and AMD disclaims, all
 * risk, liability, costs and damages relating to or arising from the overclocking
 * of AMD processors.
 *
 ******************************************************************************
 */
#include <PiDxe.h>
#include <Library/BaseLib.h>
#include <Library/AmdBaseLib.h>
#include <Library/IdsLib.h>
#include <Library/AmdIdsHookLib.h>
#include <Library/UefiBootServicesTableLib.h>
#include <Library/CcxRolesLib.h>
#include <Library/CcxPstatesLib.h>
#include <Addendum/Apcb/Inc/BRH/APOB.h>
#include <Library/AmdPspApobLib.h>
#include <Library/CcxMpServicesLib.h>
#include <Library/CcxIdsCustomPstatesLib.h>
#include <Library/NbioHandleLib.h>
#include <Library/ApobApcbLib.h>
#include <Protocol/AmdNbioSmuServicesProtocol.h>
#include <Protocol/SocZen5ServicesProtocol.h>
#include <CcxRegistersZen5.h>
#include "CcxZen5BrhIdsCustomPstates.h"
#include <IdsNvIdBRH.h>
#include <Filecode.h>

/*----------------------------------------------------------------------------------------
 *                   D E F I N I T I O N S    A N D    M A C R O S
 *----------------------------------------------------------------------------------------
 */
#define FILECODE LIBRARY_CCXZEN5BRHIDSHOOKLIB_DXE_CCXZEN5BRHIDSCUSTOMPSTATES_FILECODE

/*----------------------------------------------------------------------------------------
 *                  T Y P E D E F S     A N D     S T R U C T U R E S
 *----------------------------------------------------------------------------------------
 */
typedef struct {
  UINT32     MsrAddr;
  PSTATE_MSR MsrValue;
} MSR_PSTATE_SETTING;

typedef struct {
  UINT32 MsrAddr;
  UINT32 Setting;
  UINT32 Freq;
  UINT32 Vid;
} PSTATE_ITEM;

/*----------------------------------------------------------------------------------------
 *                           G L O B A L   V A R I A B L E S
 *----------------------------------------------------------------------------------------
 */
PSTATE_ITEM PstateItemList[] = {
 {MSR_PSTATE_0, PcdToken (PcdAmdCcxP0Setting), PcdToken (PcdAmdCcxP0Freq), PcdToken (PcdAmdCcxP0Vid32)},
 {MSR_PSTATE_1, PcdToken (PcdAmdCcxPxAutoSetting), PcdToken (PcdAmdCcxPxAutoFreq), PcdToken (PcdAmdCcxPxAutoVid)},
 {MSR_PSTATE_2, PcdToken (PcdAmdCcxPxAutoSetting), PcdToken (PcdAmdCcxPxAutoFreq), PcdToken (PcdAmdCcxPxAutoVid)}
};

/*----------------------------------------------------------------------------------------
 *           P R O T O T Y P E S     O F     L O C A L     F U  N C T I O N S
 *----------------------------------------------------------------------------------------
 */
VOID
EFIAPI
UpdatePstateOnAllCores (
  IN      VOID    *Buffer
  );

/*----------------------------------------------------------------------------------------
 *                          E X P O R T E D    F U N C T I O N S
 *----------------------------------------------------------------------------------------
 */
/*---------------------------------------------------------------------------------------*/
/**
 * CcxZen5BrhIdsDxeCustomPstates - routine to read Custom Pstates information from IDS
 *
 *
 * @param[in]  HookId       IDS Hook Id
 * @param[in]  *Handle      Unused
 * @param[in]  *Data        Unused
 *
 * @retval     IDS_HOOK_STATUS
 *
 */
IDS_HOOK_STATUS
CcxZen5BrhIdsDxeCustomPstates (
  IN       HOOK_ID HookId,
  IN       VOID *Handle,
  IN       VOID *Data
  )
{
  UINT32                                  Vid;
  UINT32                                  Freq;
  UINT32                                  SmuArg[6];
  UINT32                                  SmuReturn[6];
  UINT32                                  MaxCpuCofMhz;
  UINT32                                  MinCpuFreqMhz;
  UINT16                                  OcVoltageMax;
  UINT16                                  OcFrequencyMax;
  UINT8                                   NumOfPstates;
  UINT8                                   i;
  DXE_AMD_NBIO_SMU_SERVICES_PROTOCOL      *NbioSmuServices;
  AMD_SOC_ZEN5_SERVICES_PROTOCOL          *SocZen5Services;
  SOC_ZEN5_OC_MSG_ID_TABLE                *OcMsgIdTable;
  MSR_PSTATE_SETTING                      Pstate;
  PWR_MGMT_MISC_MSR                       PmgtMisc;
  EFI_STATUS                              Status;

  if ((!PcdGetBool (PcdOcDisable)) && (CcxIsBsp (NULL))) {
    IDS_HDT_CONSOLE (CPU_TRACE, "CcxZen5BrhIdsDxeCustomPstates Options\n");

    Status = gBS->LocateProtocol (&gAmdNbioSmuServicesProtocolGuid, NULL, (VOID **)&NbioSmuServices);
    ASSERT (NbioSmuServices != NULL);

    if (!EFI_ERROR (Status)) {
      Status = gBS->LocateProtocol (&gAmdSocZen5ServicesProtocolGuid, NULL, (VOID **)&SocZen5Services);
      ASSERT (SocZen5Services != NULL);
      if (!EFI_ERROR (Status)) {
        OcMsgIdTable = NULL;
        SocZen5Services->GetOcSmuMsgId (SocZen5Services, &OcMsgIdTable);
        ASSERT (OcMsgIdTable != NULL);
        ASSERT (OcMsgIdTable->Revision == SOC_OC_MSG_ID_MAP_REVISION);

        Status = ApobReadApcbUpdate (APOB_APCB_FUSE_MP1_FMIN, &MinCpuFreqMhz);
        if (EFI_ERROR (Status)) {
          MinCpuFreqMhz = 0;
        }
        MinCpuFreqMhz *= 5;

        PmgtMisc.Value = AsmReadMsr64 (MSR_PWR_MGMT_MISC);
        MaxCpuCofMhz = (UINT32) ((PmgtMisc.Field.MaxCpuCof * 100) + (PmgtMisc.Field.MaxCpuCofPlus50 * 50));

        OcVoltageMax = (PcdGet16 (PcdOcVoltageMax) == 0) ? 0xFFFF : PcdGet16 (PcdOcVoltageMax);
        OcFrequencyMax = (PcdGet16 (PcdOcFrequencyMax) == 0) ? 0xFFFF : PcdGet16 (PcdOcFrequencyMax);

        NumOfPstates = sizeof (PstateItemList) / sizeof (PstateItemList[0]);
        for (i = 0; i < NumOfPstates; i++) {
          if (LibPcdGet8 (PstateItemList[i].Setting) == CCX_IDS_CORE_PSTATES_CUSTOM) {

            Vid  = LibPcdGet32 (PstateItemList[i].Vid);
            Freq = LibPcdGet32 (PstateItemList[i].Freq);

            Pstate.MsrAddr = PstateItemList[i].MsrAddr;
            Pstate.MsrValue.Value = AsmReadMsr64 (PstateItemList[i].MsrAddr);

            LibAmdMemFill (SmuArg, 0x00, 24, NULL);

            // Send BIOSSMC_MSG_EnableOverclocking
            NbioSmuServices->AmdSmuServiceRequest (NbioSmuServices, 0, OcMsgIdTable->EnableOverclocking, SmuArg, SmuReturn);

            // Send BIOSSMC_MSG_SetOverclockVID
            if (Vid != PcdGet32 (PcdAmdCcxPxAutoVid)) {
              if (Vid > OcVoltageMax) {
                IDS_HDT_CONSOLE (CPU_TRACE, "  P%dVid %d exceeds OcVoltageMax, limit to %d\n", i, Vid, OcVoltageMax);
                Vid = OcVoltageMax;
              }
              SmuArg[0] = Vid & 0x1FF;
              NbioSmuServices->AmdSmuServiceRequest (NbioSmuServices, 0, OcMsgIdTable->SetOverclockVID, SmuArg, SmuReturn);
              Pstate.MsrValue.Field.CpuVid = Vid & 0xFF;
              Pstate.MsrValue.Field.CpuVid_8 = (Vid >> 8) & 1;
            }

            // Send BIOSSMC_MSG_SetOverclockFreqAllCores
            if (Freq != 0 && Freq != PcdGet32 (PcdAmdCcxPxAutoFreq)) {
              if (Freq > OcFrequencyMax) {
                IDS_HDT_CONSOLE (CPU_TRACE, "  P%xFreq %d exceeds OcFrequencyMax, limit to %d\n", i, Freq, OcFrequencyMax);
                Freq = OcFrequencyMax;
              }

              if (Freq > MaxCpuCofMhz) {
                IDS_HDT_CONSOLE (CPU_TRACE, "  P%dFreq %d exceeds Frequency Max, limit to %d\n", i, Freq, MaxCpuCofMhz);
                Freq = MaxCpuCofMhz;
              }

              if (Freq < MinCpuFreqMhz) {
                IDS_HDT_CONSOLE (CPU_TRACE, "  P%dFreq %d is below Frequency Min, limit to %d\n", i, Freq, MinCpuFreqMhz);
                Freq = MinCpuFreqMhz;
              }

              SmuArg[0] = Freq;
              NbioSmuServices->AmdSmuServiceRequest (NbioSmuServices, 0, OcMsgIdTable->SetOverclockFreqAllCores, SmuArg, SmuReturn);
              Pstate.MsrValue.Field.CpuFid = (Freq / 5) & 0xFFF;
            }

            // By writing to MSR, we force ucode to re-evaluate the TSC
            CcxRunFunctionOnAps (ALL_THREADS, UpdatePstateOnAllCores, &Pstate, NON_BLOCKING_MODE);
            UpdatePstateOnAllCores (&Pstate);

            IDS_HDT_CONSOLE (CPU_TRACE, "  OC - P%d Freq: %d; VID: %X\n", i, Freq, Vid);

            // collapse Pstates when freq set below higher Pstate
            if ((i+1) < NumOfPstates) {
              PSTATE_MSR  ThisPstate;
              PSTATE_MSR  NextPstate;

              ThisPstate.Value = AsmReadMsr64 (PstateItemList[i].MsrAddr);
              NextPstate.Value = AsmReadMsr64 (PstateItemList[i+1].MsrAddr);
              if (ThisPstate.Field.CpuFid < NextPstate.Field.CpuFid) {
                PstateItemList[i+1].Setting = PstateItemList[i].Setting;
                PstateItemList[i+1].Freq    = PstateItemList[i].Freq;
              }
            }
          }
        }
      } else {
        IDS_HDT_CONSOLE (CPU_TRACE, "  ERROR: SOC service unavailable!\n");
      }
    } else {
      IDS_HDT_CONSOLE (CPU_TRACE, "  ERROR: NBIO SMU service unavailable!\n");
    }
  } // End of CcxIsBsp

  return IDS_HOOK_SUCCESS;
}

VOID
EFIAPI
UpdatePstateOnAllCores (
  IN      VOID    *Buffer
  )
{
  MSR_PSTATE_SETTING *Pstate;
  Pstate = Buffer;

  AsmWriteMsr64 (Pstate->MsrAddr, Pstate->MsrValue.Value);
}

