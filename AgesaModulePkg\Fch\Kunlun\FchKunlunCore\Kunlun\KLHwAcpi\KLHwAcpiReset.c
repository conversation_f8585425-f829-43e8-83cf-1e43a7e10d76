/*****************************************************************************
 * Copyright (C) 2008-2024 Advanced Micro Devices, Inc. All rights reserved.
 *
*****************************************************************************
*/
/* $NoKeywords:$ */
/**
 * @file
 *
 * Config Fch HwAcpi controller
 *
 * Init HwAcpi Controller features (PEI phase).
 *
 * @xrefitem bom "File Content Label" "Release Content"
 * @e project:     AGESA
 * @e sub-project: FCH
 * @e \$Revision: 309083 $   @e \$Date: 2014-12-09 09:28:24 -0800 (Tue, 09 Dec 2014) $
 *
 */
#include "FchPlatform.h"
#include "Filecode.h"
#define FILECODE FCH_KUNLUN_FCHKUNLUNCORE_KUNLUN_KLHWACPI_KLHWACPIRESET_FILECODE

extern ACPI_REG_WRITE FchInitResetAcpiMmioTable[];
extern VOID  ProgramFchHwAcpiResetP (IN VOID  *FchDataPtr);

/**
 * FchInitResetHwAcpiP - Config HwAcpi controller ( Preliminary
 * ) during Power-On
 *
 *
 *
 * @param[in] FchDataPtr Fch configuration structure pointer.
 *
 */
VOID
FchInitResetHwAcpiP (
  IN  VOID     *FchDataPtr
  )
{
  FCH_RESET_DATA_BLOCK      *LocalCfgPtr;
  AMD_CONFIG_PARAMS         *StdHeader;

  AGESA_TESTPOINT (TpFchInitResetHwAcpiP, NULL);

  LocalCfgPtr = (FCH_RESET_DATA_BLOCK *) FchDataPtr;

  StdHeader = LocalCfgPtr->StdHeader;
  //Kunlun RwPmio (FCH_PMIOA_REG08 + 2, AccessWidth8, 0xFF, BIT3, StdHeader);
  //
  // Enabled (Mmio_mem_enable)
  //
  RwPmio (FCH_PMIOA_REG04, AccessWidth8, 0xFF, BIT1, StdHeader);

  //ACPIMMIO32 (FCH_AOACx94S0I3_CONTROL) &= ~ (FCH_AOACx94S0I3_CONTROL_ARBITER_DIS + FCH_AOACx94S0I3_CONTROL_INTERRUPT_DIS);
  ACPIMMIO32 (ACPI_MMIO_BASE + AOAC_BASE + FCH_AOAC_REG94) &= ~ (S0I3CTRL_ARBITERDIS + S0I3CTRL_INTERRUPTDIS);

  RwPmio (FCH_PMIOA_REG08 + 2, AccessWidth8, 0xFF, BIT4, StdHeader);

  ProgramFchHwAcpiResetP (FchDataPtr);

  //
  // enable CF9
  //
  RwPmio (FCH_PMIOA_REGD2, AccessWidth8, ~(UINT32) BIT6, 0, StdHeader);

  //
  // enable LpcClockDriveStrength
  //
  if ( LocalCfgPtr->Emmc.EmmcEnable == 0 ) {
    ProgramEmmcPins (FALSE);
    ACPIMMIO8 (FCH_EMMC_CFG_REGBB) = (LocalCfgPtr->LpcClockDriveStrengthRiseTime & 0xf) | ((LocalCfgPtr->LpcClockDriveStrengthFallTime & 0xf) << 4);
  }

  RwPmio (FCH_PMIOA_REGC0, AccessWidth8, 0, BIT1, StdHeader);          //Clear 4s shutdown event status

  //
  // PLAT-55628
  //
  RwMem (ACPI_MMIO_BASE + MISC_BASE + FCH_MISC_REG50, AccessWidth32, ~(UINT32)(BIT20), (BIT20));
  RwMem (ACPI_MMIO_BASE + MISC_BASE + FCH_MISC_REG50, AccessWidth32, ~(UINT32)(BIT20), 0);
}

/**
 * FchInitResetHwAcpi - Config HwAcpi controller during Power-On
 *
 *
 *
 * @param[in] FchDataPtr Fch configuration structure pointer.
 *
 */
VOID
FchInitResetHwAcpi (
  IN  VOID     *FchDataPtr
  )
{
  UINT16       SmbusBase;
  UINT8        Value;
  UINT16       AsfPort;
  UINT32       GeventEnableBits;
  UINT32       GeventValue;
  UINT32       ApicIdValue;
  UINT32       ApicReg;
  UINT32       ApicIndexBackup;
  FCH_RESET_DATA_BLOCK      *LocalCfgPtr;
  AMD_CONFIG_PARAMS         *StdHeader;

  AGESA_TESTPOINT (TpFchInitResetHwAcpi, NULL);

  LocalCfgPtr = (FCH_RESET_DATA_BLOCK *) FchDataPtr;
  StdHeader = LocalCfgPtr->StdHeader;

  //
  // Set Build option into SB
  //

  // Enable LPC IO decoding if requested by platform, using WideIO0 Kunlun
  if ((LocalCfgPtr->FchBldCfg.CfgSioPmeBaseAddress != 0x00) && (LocalCfgPtr->FchBldCfg.CfgSioPmeBaseAddress != 0xFFFF)) {
    WritePci ((LPC_BUS_DEV_FUN << 16) + FCH_LPC_REG64, AccessWidth16, &(LocalCfgPtr->FchBldCfg.CfgSioPmeBaseAddress), StdHeader);
    RwPci ((LPC_BUS_DEV_FUN << 16) + FCH_LPC_REG48, AccessWidth8, 0x00, BIT2, StdHeader);
  }

  //
  // Enabled Base Address
  //
  SmbusBase = LocalCfgPtr->FchBldCfg.CfgSmbus0BaseAddress;
  SmbusBase &= 0xFF00;
  RwMem (ACPI_MMIO_BASE + PMIO_BASE + FCH_PMIOA_REG00, AccessWidth16, 0x00FF, SmbusBase + BIT4);
  RwMem (ACPI_MMIO_BASE + PMIO_BASE + FCH_PMIOA_REG60, AccessWidth16, 00, (LocalCfgPtr->FchBldCfg.CfgAcpiPm1EvtBlkAddr));
  RwMem (ACPI_MMIO_BASE + PMIO_BASE + FCH_PMIOA_REG62, AccessWidth16, 00, (LocalCfgPtr->FchBldCfg.CfgAcpiPm1CntBlkAddr));
  RwMem (ACPI_MMIO_BASE + PMIO_BASE + FCH_PMIOA_REG64, AccessWidth16, 00, (LocalCfgPtr->FchBldCfg.CfgAcpiPmTmrBlkAddr));
  RwMem (ACPI_MMIO_BASE + PMIO_BASE + FCH_PMIOA_REG66, AccessWidth16, 00, (LocalCfgPtr->FchBldCfg.CfgCpuControlBlkAddr));
  RwMem (ACPI_MMIO_BASE + PMIO_BASE + FCH_PMIOA_REG68, AccessWidth16, 00, (LocalCfgPtr->FchBldCfg.CfgAcpiGpe0BlkAddr));
  RwMem (ACPI_MMIO_BASE + PMIO_BASE + FCH_PMIOA_REG6A, AccessWidth16, 00, (LocalCfgPtr->FchBldCfg.CfgSmiCmdPortAddr));
  RwMem (ACPI_MMIO_BASE + PMIO_BASE + FCH_PMIOA_REG6E, AccessWidth16, 00, (LocalCfgPtr->FchBldCfg.CfgSmiCmdPortAddr) + 8);
  RwMem (ACPI_MMIO_BASE + PMIO_BASE + FCH_PMIOA_REG6C, AccessWidth16, 00, 0xFFFF);

  //
  // SmBus init
  //
  RwMem (ACPI_MMIO_BASE + PMIO_BASE + FCH_PMIOA_REG00, AccessWidth32, ~(UINT32) (BIT19 + BIT20), 0);
  Value = 0x00;
  LibAmdIoWrite (AccessWidth8, SmbusBase + 0x14, &Value, StdHeader);



  ProgramFchAcpiMmioTbl ((ACPI_REG_WRITE*) (&FchInitResetAcpiMmioTable[0]), StdHeader);

  //
  //Boot Timer configuration
  //
  FchInitEnableBootTimer(LocalCfgPtr);

  FchInitEnableWdt (LocalCfgPtr);

  ProgramCpuRstBTmr (LocalCfgPtr);

  FchResetRtcExt ();

  if (LocalCfgPtr->FchBldCfg.CfgFchSciMapControl != NULL) {
    ProgramFchSciMapTbl ((LocalCfgPtr->FchBldCfg.CfgFchSciMapControl), LocalCfgPtr);
  }

//  if (LocalCfgPtr->FchBldCfg.CfgFchGpioControl != NULL) {
//    ProgramFchGpioTbl ((LocalCfgPtr->FchBldCfg.CfgFchGpioControl), LocalCfgPtr);
//  }

  if (LocalCfgPtr->FchBldCfg.CfgFchSataPhyControl != NULL) {
    ProgramFchSataPhyTbl ((LocalCfgPtr->FchBldCfg.CfgFchSataPhyControl), LocalCfgPtr);
  }
  //
  // RTC Workaround for Daylight saving time enable bit
  //
  RwMem (ACPI_MMIO_BASE + PMIO_BASE + FCH_PMIOA_REG5E, AccessWidth8, 0, 0);
  RwMem (ACPI_MMIO_BASE + PMIO_BASE + FCH_PMIOA_REG5F, AccessWidth8, 0xFE, BIT0 );   // Enable DltSavEnable
  Value = 0x0B;
  LibAmdIoWrite (AccessWidth8, FCH_IOMAP_REG70, &Value, StdHeader);
  LibAmdIoRead (AccessWidth8, FCH_IOMAP_REG71, &Value, StdHeader);
  Value &= 0xFE;
  LibAmdIoWrite (AccessWidth8, FCH_IOMAP_REG71, &Value, StdHeader);
  RwMem (ACPI_MMIO_BASE + PMIO_BASE + FCH_PMIOA_REG5E, AccessWidth8, 0, 0);
  RwMem (ACPI_MMIO_BASE + PMIO_BASE + FCH_PMIOA_REG5F, AccessWidth8, 0xFE, 0 );      // Disable DltSavEnable
  //
  // Prevent RTC error
  //
  Value = 0x0A;
  LibAmdIoWrite (AccessWidth8, FCH_IOMAP_REG70, &Value, StdHeader);
  LibAmdIoRead (AccessWidth8, FCH_IOMAP_REG71, &Value, StdHeader);
  Value &= 0xEF;
  LibAmdIoWrite (AccessWidth8, FCH_IOMAP_REG71, &Value, StdHeader);

  if ( LocalCfgPtr->FchBldCfg.CfgFchRtcWorkAround ) {
    Value = RTC_WORKAROUND_SECOND;
    LibAmdIoWrite (AccessWidth8, FCH_IOMAP_REG70, &Value, StdHeader);
    LibAmdIoRead (AccessWidth8, FCH_IOMAP_REG71, &Value, StdHeader);
    if ( Value > RTC_VALID_SECOND_VALUE ) {
      Value = RTC_SECOND_RESET_VALUE;
      LibAmdIoWrite (AccessWidth8, FCH_IOMAP_REG71, &Value, StdHeader);
    }
    LibAmdIoRead (AccessWidth8, FCH_IOMAP_REG71, &Value, StdHeader);
    Value &= RTC_SECOND_LOWER_NIBBLE;
    if ( Value > RTC_VALID_SECOND_VALUE_LN ) {
      LibAmdIoRead (AccessWidth8, FCH_IOMAP_REG71, &Value, StdHeader);
      Value = RTC_SECOND_RESET_VALUE;
      LibAmdIoWrite (AccessWidth8, FCH_IOMAP_REG71, &Value, StdHeader);
    }
  }

  Value = 0x08;
  LibAmdIoWrite (AccessWidth8, FCH_IOMAP_REGC00, &Value, StdHeader);
  LibAmdIoRead (AccessWidth8, FCH_IOMAP_REGC01, &Value, StdHeader);

  if ( !LocalCfgPtr->EcKbd ) {
    //
    // Route SIO IRQ1/IRQ12 to USB IRQ1/IRQ12 input
    //
    Value = Value | 0x0A;
  }
  LibAmdIoWrite (AccessWidth8, FCH_IOMAP_REGC01, &Value, StdHeader);

  Value = 0x09;
  LibAmdIoWrite (AccessWidth8, FCH_IOMAP_REGC00, &Value, StdHeader);
  LibAmdIoRead (AccessWidth8, FCH_IOMAP_REGC01, &Value, StdHeader);
  if ( !LocalCfgPtr->EcKbd ) {
    //
    // Route SIO IRQ1/IRQ12 to USB IRQ1/IRQ12 input
    //
    Value = Value & 0xF9;
  }

//PLAT-12168  if ( LocalCfgPtr->LegacyFree ) {
    //
    // Disable IRQ1/IRQ12 filter enable for Legacy free with USB KBC emulation.
    //
  Value = Value & 0x9F;
//PLAT-12168  }
  //
  // Enabled IRQ input
  //
  Value = Value | BIT4;
  LibAmdIoWrite (AccessWidth8, FCH_IOMAP_REGC01, &Value, StdHeader);

  AsfPort = SmbusBase + 0x20;
  if ( AsfPort != 0 ) {
    UINT8  dbValue;
    dbValue = 0x2F;
    LibAmdIoWrite (AccessWidth8, AsfPort + 0x0A, &dbValue, StdHeader);
  }
  //
  // PciExpWakeStatus workaround
  //
  ReadMem (ACPI_MMIO_BASE + PMIO_BASE + FCH_PMIOA_REG60, AccessWidth16, &AsfPort);
  AsfPort++;
  ReadMem (ACPI_MMIO_BASE + SMI_BASE + FCH_SMI_REG04, AccessWidth32, &GeventEnableBits);
  ReadMem (ACPI_MMIO_BASE + SMI_BASE + FCH_SMI_REG00, AccessWidth32, &GeventValue);
  if ( (GeventValue & GeventEnableBits) != 0 ) {
    Value = 0x40;
    LibAmdIoWrite (AccessWidth8, AsfPort, &Value, StdHeader);
  }
  LibAmdIoRead (AccessWidth8, AsfPort, &Value, StdHeader);
  if ((Value & (BIT2 + BIT0)) != 0) {
    Value = 0x40;
    LibAmdIoWrite (AccessWidth8, AsfPort, &Value, StdHeader);
  }

  if ( LocalCfgPtr->FchOscout1ClkContinous ) {
    RwMem (ACPI_MMIO_BASE + PMIO_BASE + FCH_PMIOA_REG54, AccessWidth8, 0xBF, 0);
  }
  if (LocalCfgPtr->SerialIrqEnable){
    RwMem (ACPI_MMIO_BASE + PMIO_BASE + FCH_PMIOA_REG54, AccessWidth8, 0x7F, BIT7);
  }
  //
  // Clear RTCD Date Alarm
  //
  Value = 0x0D;
  LibAmdIoWrite (AccessWidth8, FCH_IOMAP_REG70, &Value, StdHeader);
  LibAmdIoRead (AccessWidth8, FCH_IOMAP_REG71, &Value, StdHeader);
  if ((Value & 0x3F) == 0x3F) {
    Value &= 0x80;
    LibAmdIoWrite (AccessWidth8, FCH_IOMAP_REG71, &Value, StdHeader);
  }

  //
  // Disable ASF slave
  //
  if (LocalCfgPtr->FchAsfCfg.DisableSlave) {
    RwMem (ACPI_MMIO_BASE + ASF_BASE + 0x15, AccessWidth8, 0xEF, BIT4);
  } else {
    RwMem (ACPI_MMIO_BASE + ASF_BASE + 0x15, AccessWidth8, 0xEF, 0);
  }

  FchInitEnableIxC(LocalCfgPtr);

  // FCH IOAPIC ID configuration

  {
    ApicIdValue = (LocalCfgPtr->FchIoApicId << 24);
    ApicReg = FCH_IOAPIC_ID_REG;
    //Backup IOAPIC Index
    LibAmdMemRead (AccessWidth32, FCH_IOAPIC_INDEX, &ApicIndexBackup, (AMD_CONFIG_PARAMS *)NULL);
    //Program IOAPIC Index to IOAPIC ID REG
    LibAmdMemWrite (AccessWidth32, FCH_IOAPIC_INDEX, &ApicReg, (AMD_CONFIG_PARAMS *)NULL);
    //Program IOAPIC ID
    LibAmdMemWrite (AccessWidth32, FCH_IOAPIC_DATA, &ApicIdValue, (AMD_CONFIG_PARAMS *)NULL);
    //restore IOAPIC Index
    LibAmdMemWrite (AccessWidth32, FCH_IOAPIC_INDEX, &ApicIndexBackup, (AMD_CONFIG_PARAMS *)NULL);

    IDS_HDT_CONSOLE (FCH_TRACE, "FchIoapicValue Value 0x%x\n", ApicIdValue);
  }

  if (FchCheckBrhClient ()) {
    if ( LocalCfgPtr->ToggleAllPwrGoodOnCf9 ) {
      RwMem (ACPI_MMIO_BASE + PMIO_BASE + FCH_PMIOA_REG10, AccessWidth8, 0xFD, 2);
    } else {
      RwMem (ACPI_MMIO_BASE + PMIO_BASE + FCH_PMIOA_REG10, AccessWidth8, 0xFD, 0);
    }
  }
}


