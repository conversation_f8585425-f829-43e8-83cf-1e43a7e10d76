#;*****************************************************************************
#;
#; Copyright (C) 2008-2024 Advanced Micro Devices, Inc. All rights reserved.
#;
#;******************************************************************************

[defines]

  INF_VERSION                   = 0x00010005
  BASE_NAME                     = FchI2cMasterPei
  FILE_GUID                     = 85b79d5d-eaad-4a85-ba28-03d1362e668d
  MODULE_TYPE                   = PEIM
  VERSION_STRING                = 1.0
  ENTRY_POINT                   = AmdI2cMasterPeiInit


[sources.common]
  I2cMasterPei.c
  I2cMasterPei.h

[LibraryClasses]
  PeimEntryPoint
  FchI2cLib
  FchSocLib
  BaseLib
  DebugLib
  TimerLib
  IoLib
  PeiServicesLib
  HobLib
  AmdBaseLib
  FchBaseLib
  FabricRegisterAccLib
  BaseFabricTopologyLib

[Guids]

[Protocols]

[Ppis]
  gEfiPeiI2cMasterPpiGuid             #PRODUCED
  gPeiI2cMasterPostPpiGuid            #PRODUCED
  gAmdFabricTopologyServices2PpiGuid  #CONSUMED
  gAmdPcdInitReadyPpiGuid             #CONSUMED

[Packages]
  MdePkg/MdePkg.dec
  AgesaPkg/AgesaPkg.dec
  AgesaModulePkg/AgesaModuleFchPkg.dec
  AgesaModulePkg/AgesaCommonModulePkg.dec
  AgesaModulePkg/AgesaModuleDfPkg.dec
[Pcd]
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdAmdEnvironmentFlag
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdSkt1FchBus
[Depex]
  TRUE


