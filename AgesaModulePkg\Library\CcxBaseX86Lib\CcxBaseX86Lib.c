/*
 ******************************************************************************
 *
 * Copyright (C) 2008-2024 Advanced Micro Devices, Inc. All rights reserved.
 *
 *******************************************************************************
 */


/*----------------------------------------------------------------------------------------
 *                             M O D U L E S    U S E D
 *----------------------------------------------------------------------------------------
 */

#include "Uefi.h"
#include <Library/BaseLib.h>
#include "AMD.h"
#include "Filecode.h"
#include <Library/AmdBaseLib.h>
#include <Library/IdsLib.h>
#include <Library/CcxBaseX86Lib.h>
#include <cpuRegisters.h>

#define FILECODE LIBRARY_CCXBASEX86LIB_CCXBASEX86LIB_FILECODE

/*----------------------------------------------------------------------------------------
 *           P R O T O T Y P E S     O F     L O C A L     F U  N C T I O N S
 *----------------------------------------------------------------------------------------
 */

/*---------------------------------------------------------------------------------------*/
/**
 * Returns the MMIO address used for PCI extended configuration space
 *
 * @param[in]     MmioAddress   Mmio Address
 * @param[in]     MmioSize      Mmio Size
 * @param[in]     StdHeader     Standard configuration header (Optional)
 */
BOOLEAN
CcxGetPciMmioAddress (
     OUT   UINT64            *MmioAddress,
     OUT   UINT32            *MmioSize,
  IN       AMD_CONFIG_PARAMS *StdHeader
  )
{
  BOOLEAN MmioIsEnabled;
  UINT32  EncodedSize;
  UINT64  LocalMsrRegister;

  MmioIsEnabled = FALSE;
  LocalMsrRegister = AsmReadMsr64 (MSR_MMIO_Cfg_Base);
  if ((LocalMsrRegister & BIT0) != 0) {
    *MmioAddress = LocalMsrRegister & 0xFFFFFFFFFFF00000;
    EncodedSize = (UINT32) RShiftU64 ((LocalMsrRegister & 0x3C), 2);
    *MmioSize = ((1 << EncodedSize) * 0x100000);
    MmioIsEnabled = TRUE;
  }
  return MmioIsEnabled;
}

/*---------------------------------------------------------------------------------------*/
/**
 * Returns the package type mask for the processor
 *
 *
 * @param[in]     StdHeader     Standard configuration header (Optional)
 */
UINT32
CcxGetProcessorPackageType (
  IN       AMD_CONFIG_PARAMS *StdHeader
  )
{
  UINT32      ProcessorPackageType;
  UINT32      EBX_Reg = 0;

  AsmCpuid (
      AMD_CPUID_FMF,
      NULL,
      &EBX_Reg,
      NULL,
      NULL
      );
  ProcessorPackageType = (UINT32) (EBX_Reg >> 28) & 0xF; // bit 31:28
  return (UINT32) (1 << ProcessorPackageType);
}

/*---------------------------------------------------------------------------------------*/
/**
 * Returns the core's APIC ID at reset
 *
 *
 * @param[in]     StdHeader     Standard configuration header (Optional)
 */
UINT32
CcxGetInitialApicID (
  IN       AMD_CONFIG_PARAMS *StdHeader
  )
{
  UINT32      EDX_Reg = 0;

  AsmCpuid (
      AMD_CPUID_EXTENDED_TOPOLOGY_ENUMERATION,
      NULL,
      NULL,
      NULL,
      &EDX_Reg
      );
  return EDX_Reg;
}

/*---------------------------------------------------------------------------------------*/
/**
 * Returns the value of ThreadsPerCore
 *
 *
 * @retval Value of ThreadsPerCore + 1
 */
UINT8
CcxGetThreadsPerCore (
  )
{
  UINT32      EBX_Reg = 0;

  AsmCpuid (
      0x8000001E,
      NULL,
      &EBX_Reg,
      NULL,
      NULL
    );

  return (UINT8) (((EBX_Reg >> 8) & 0xFF) + 1);
}

/*---------------------------------------------------------------------------------------*/
/**
 * Returns the value of PhysAddrSize
 *
 *
 * @retval Value of CPUID_Fn8000_00008_EAX[7:0, PhysAddrSize]
 */
UINT8
CcxGetPhysAddrSize (
  )
{
  UINT32      EAX_Reg = 0;

  AsmCpuid (
      0x80000008,
      &EAX_Reg,
      NULL,
      NULL,
      NULL
    );

  return (UINT8) (EAX_Reg & 0xFF);
}

/*---------------------------------------------------------------------------------------*/
/**
 * Returns the value of ApicIdCoreIdSize
 *
 *
 * @retval Value of CPUID_Fn8000_00008_ECX[15:12, ApicIdCoreIdSize]
 */
UINT8
CcxGetApicIdCoreIdSize (
  )
{
  UINT32      ECX_Reg = 0;

  AsmCpuid (
      0x80000008,
      NULL,
      NULL,
      &ECX_Reg,
      NULL
    );

  return (UINT8) ((ECX_Reg >> 12) & 0xF);
}

/*---------------------------------------------------------------------------------------*/
/**
 * Returns the value of LFuncExt
 *
 *
 * @retval Value of CPUID_Fn8000_00000_EAX[31:0, LFuncExt]
 */
UINT32
CcxGetLFuncExt (
  )
{
  UINT32      EAX_Reg = 0;

  AsmCpuid (
      0x80000000,
      &EAX_Reg,
      NULL,
      NULL,
      NULL
    );

  return (EAX_Reg);
}

