/*****************************************************************************
 *
 * Copyright (C) 2008-2024 Advanced Micro Devices, Inc. All rights reserved.
 *
 *******************************************************************************
 */

//#include "FchSmm.h"
#include "FchPlatform.h"

#define FILECODE FCH_KUNLUN_FCHKUNLUNSMM_IOTRAPSMI_FILECODE


EFI_STATUS
FchSmmRegisterIoTrapSmi (
  VOID
  )
{
  EFI_STATUS                                 Status = EFI_SUCCESS;
  // FCH_SMM_IO_TRAP_DISPATCH2_PROTOCOL         *AmdIoTrapDispatch;
  // FCH_SMM_IO_TRAP_REGISTER_CONTEXT           IoTrapRegisterContext;

  // Status = gSmst->SmmLocateProtocol (
  //                 &gFchSmmIoTrapDispatch2ProtocolGuid,
  //                 NULL,
  //                 (VOID **)&AmdIoTrapDispatch
  //                   );


  return Status;
}

