/*****************************************************************************
 *
 * Copyright (C) 2015-2024 Advanced Micro Devices, Inc. All rights reserved.
 *
 *******************************************************************************
 **/
#ifndef _AMD_ERROR_LOG_PROTOCOL_H_
#define _AMD_ERROR_LOG_PROTOCOL_H_

#include <AmdErrorLog.h>

/**
 * @brief Current protocol revision
 * @details
 */
#define AMD_ERROR_LOG_PROTOCOL_REVISION   0x00  ///< Current protocol revision

///
/// Forward declaration for the DXE_AMD_ERROR_LOG_SERVICES_PROTOCOL
///
typedef struct _DXE_AMD_ERROR_LOG_PROTOCOL DXE_AMD_ERROR_LOG_PROTOCOL;

//
// Protocol Definitions
//

/**
 * @brief This function logs AGESA errors into the Error log.
 *
 * @details It will put the information in a circular buffer consisting of 128 such log
 *          entries. If the buffer gets full, then the next Error log entry will be written
 *          over the oldest Error log entry.
 *
 * @param[in]  This                A pointer to the DXE_AMD_ERROR_LOG_PROTOCOL instance.
 * @param[in]  ErrorClass          The severity of the error, its associated AGESA_STATUS.
 * @param[in]  ErrorInfo           Uniquely identifies the error.
 * @param[in]  DataParam1          Error specific additional data
 * @param[in]  DataParam2          Error specific additional data
 * @param[in]  DataParam3          Error specific additional data
 * @param[in]  DataParam4          Error specific additional data
 * @retval EFI_STATUS              0: Success, NonZero: Standard EFI Error.
 */
typedef
EFI_STATUS
(EFIAPI * AMD_ERROR_LOG_DXE) (
  IN  DXE_AMD_ERROR_LOG_PROTOCOL   *This,
  IN  AMD_STATUS ErrorClass,
  IN  UINT32 ErrorInfo,
  IN  UINT32 DataParam1,
  IN  UINT32 DataParam2,
  IN  UINT32 DataParam3,
  IN  UINT32 DataParam4
  );

/**
 * @brief Check if all IP drivers are complete before publishing Error Log Available Protocol.
 *
 * @details Check if all IP drivers are complete before publishing Error Log Available Protocol.
 *
 * @param[in]  This                A pointer to the DXE_AMD_ERROR_LOG_PROTOCOL instance.
 * @param[in]  *SiliconDriverId    The silicon Driver Id with Guid formate.
 * @retval EFI_STATUS              0: Success, NonZero: Standard EFI Error.
 */
typedef
EFI_STATUS
(EFIAPI * AMD_ERROR_LOG_IP_COMPLETE_DXE) (
  IN  DXE_AMD_ERROR_LOG_PROTOCOL   *This,
  IN CONST EFI_GUID            *SiliconDriverId
  );

///
/// DXE protocol prototype
///
struct _DXE_AMD_ERROR_LOG_PROTOCOL {
  UINTN                          Revision;                 ///< Revision Number
  AMD_ERROR_LOG_DXE              AmdErrorLogDxe;           ///< logs AGESA errors into the Error log
  AMD_ERROR_LOG_IP_COMPLETE_DXE  AmdErrorLogIpCompleteDxe; ///< Publish Error Log Available Protocol
};

/**
 * @brief Guid declaration for the DXE_AMD_ERROR_LOG_SERVICES_PROTOCOL.
 *
 */
extern EFI_GUID gAmdErrorLogReadyProtocolGuid;

/**
 * @brief Guid for publishing Error Log service Protocol For IP driver.
 *
 */
extern EFI_GUID gAmdErrorLogProtocolGuid;

#endif //_AMD_ERROR_LOG_PROTOCOL_H_



