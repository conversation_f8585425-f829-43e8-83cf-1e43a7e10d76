/*****************************************************************************
 * Copyright (C) 2008-2024 Advanced Micro Devices, Inc. All rights reserved.
 *
*****************************************************************************
*/

/**
 * @file
 *
 * AMD CCX ZEN5 BRH API, and related functions.
 *
 * Contains code that initializes the core complex
 *
 * @xrefitem bom "File Content Label" "Release Content"
 * @e project:      AGESA
 * @e sub-project:  CCX
 * @e \$Revision$   @e \$Date$
 *
 */
#include <Library/AmdBaseLib.h>
#include <Library/IdsLib.h>
#include <Library/AmdIdsHookLib.h>
#include <IdsHookId.h>
#include <IdsNvIdBRH.h>
#include <Filecode.h>

#define FILECODE LIBRARY_CCXZEN5BRHIDSHOOKLIB_PEI_CCXZEN5BRHIDSHOOKLIBPEI_FILECODE

/*----------------------------------------------------------------------------------------
 *                           G L O B A L   V A R I A B L E S
 *----------------------------------------------------------------------------------------
 */
/*----------------------------------------------------------------------------------------
 *           P R O T O T Y P E S     O F     L O C A L     F U  N C T I O N S
 *----------------------------------------------------------------------------------------
 */


/*----------------------------------------------------------------------------------------
 *                          L O C A L    F U N C T I O N S
 *----------------------------------------------------------------------------------------
 */

#ifndef IDS_HOOK_INTERNAL_SUPPORT
  #define CCX_IDS_HOOKS_INT
#else
  #include <Internal/CcxZen5BrhIdsHookLibIntPei.h>
#endif

STATIC IDS_HOOK_ELEMENT CcxZen5BrhIdsHooks[] = {
  CCX_IDS_HOOKS_INT
  IDS_HOOKS_END
};

STATIC IDS_HOOK_TABLE CcxZen5BrhIdsHookTable = {
  IDS_HOOK_TABLE_HEADER_REV1_DATA,
  CcxZen5BrhIdsHooks
};

AGESA_STATUS
GetIdsHookTable (
  IDS_HOOK_TABLE **IdsHookTable
  )
{
  *IdsHookTable = &CcxZen5BrhIdsHookTable;

  return AGESA_SUCCESS;
}

