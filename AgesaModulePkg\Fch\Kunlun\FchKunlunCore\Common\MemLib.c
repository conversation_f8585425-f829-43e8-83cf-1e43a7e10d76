/*****************************************************************************
 * Copyright (C) 2008-2024 Advanced Micro Devices, Inc. All rights reserved.
 *
*****************************************************************************
*/
/* $NoKeywords:$ */
/**
 * @file
 *
 * FCH memory access lib
 *
 *
 *
 * @xrefitem bom "File Content Label" "Release Content"
 * @e project:     AGESA
 * @e sub-project: FCH
 * @e \$Revision: 309090 $   @e \$Date: 2014-12-09 10:28:05 -0800 (Tue, 09 Dec 2014) $
 *
 */
#include "FchPlatform.h"
#define FILECODE FCH_KUNLUN_FCHKUNLUNCORE_COMMON_MEMLIB_FILECODE


/**
 * ReadMem - Read FCH BAR Memory
 *
 * @param[in] Address    - Memory BAR address
 * @param[in] OpFlag     - Access width
 * @param[in] *ValuePtr  - In/Out value pointer
 *
 */
VOID
ReadMem (
  IN  UINT32     Address,
  IN  UINT8      OpFlag,
  IN  VOID       *ValuePtr
  )
{
  OpFlag = OpFlag & 0x7f;

  switch ( OpFlag ) {
  case AccessWidth8:
    *((UINT8*)ValuePtr) = *((volatile UINT8*) ((UINTN)Address));
#if FCH_DEBUG
    IDS_HDT_CONSOLE (FCH_TRACE, "%a Address 0x%08x value 0x%02x\n", __FUNCTION__, Address, *((UINT8*)ValuePtr));
#endif
    break;

  case AccessWidth16:
    *((UINT16*)ValuePtr) = *((volatile UINT16*) ((UINTN)Address));
#if FCH_DEBUG
    IDS_HDT_CONSOLE (FCH_TRACE, "%a Address 0x%08x value 0x%04x\n", __FUNCTION__, Address, *((UINT16*)ValuePtr));
#endif
    break;

  case AccessWidth32:
    *((UINT32*)ValuePtr) = *((volatile UINT32*) ((UINTN)Address));
#if FCH_DEBUG
    IDS_HDT_CONSOLE (FCH_TRACE, "%a Address 0x%08x value 0x%08x\n", __FUNCTION__, Address, *((UINT32*)ValuePtr));
#endif
    break;

  default:
    ASSERT (FALSE);
    break;
  }
}

/**
 * WriteMem - Write FCH BAR Memory
 *
 * @param[in] Address    - Memory BAR address
 * @param[in] OpFlag     - Access width
 * @param[in] *ValuePtr  - In/Out Value pointer
 *
 */
VOID
WriteMem (
  IN  UINT32     Address,
  IN  UINT8      OpFlag,
  IN  VOID       *ValuePtr
  )
{
  OpFlag = OpFlag & 0x7f;

  switch ( OpFlag ) {
  case AccessWidth8 :
    *((volatile UINT8*) ((UINTN)Address)) = *((UINT8*)ValuePtr);
#if FCH_DEBUG
    IDS_HDT_CONSOLE (
      FCH_TRACE,
      "%a Address 0x%08x the value to write 0x%02x, read back value 0x%02x\n",
      __FUNCTION__,
      Address,
      *((UINT8*)ValuePtr),
      *((volatile UINT8*) ((UINTN)Address))
      );
#endif
    break;

  case AccessWidth16:
    *((volatile UINT16*) ((UINTN)Address)) = *((UINT16*)ValuePtr);
#if FCH_DEBUG
    IDS_HDT_CONSOLE (
      FCH_TRACE,
      "%a Address 0x%08x the value to write 0x%04x, read back value 0x%04x\n",
      __FUNCTION__,
      Address,
      *((UINT16*)ValuePtr),
      *((volatile UINT16*) ((UINTN)Address))
      );
#endif

    break;

  case AccessWidth32:
    *((volatile UINT32*) ((UINTN)Address)) = *((UINT32*)ValuePtr);
#if FCH_DEBUG
    IDS_HDT_CONSOLE (
      FCH_TRACE,
      "%a Address 0x%08x the value to write 0x%08x, read back value 0x%08x\n",
      __FUNCTION__,
      Address,
      *((UINT32*)ValuePtr),
      *((volatile UINT32*) ((UINTN)Address))
      );
#endif
    break;

  default:
    ASSERT (FALSE);
    break;
  }
}

/**
 * RwMem - Read & Write FCH BAR Memory
 *
 * @param[in] Address    - Memory BAR address
 * @param[in] OpFlag     - Access width
 * @param[in] Mask       - Mask Value of data
 * @param[in] Data       - Write data
 *
 */
VOID
RwMem (
  IN  UINT32     Address,
  IN  UINT8      OpFlag,
  IN  UINT32     Mask,
  IN  UINT32     Data
  )
{
  UINT32  Result;

  Result  = 0;

  ReadMem (Address, OpFlag, &Result);
#if FCH_DEBUG
  IDS_HDT_CONSOLE (FCH_TRACE, "%a Address 0x%08x original value 0x%08x\n", __FUNCTION__, Address, Result);
#endif
  Result = (Result & Mask) | Data;
#if FCH_DEBUG
  IDS_HDT_CONSOLE (FCH_TRACE, "%a Modified value 0x%08x\n", __FUNCTION__, Result);
#endif
  WriteMem (Address, OpFlag, &Result);
  ReadMem (Address, OpFlag, &Result);
#if FCH_DEBUG
  IDS_HDT_CONSOLE (FCH_TRACE, "%a Address 0x%08x modified value 0x%08x\n", __FUNCTION__, Address, Result);
#endif
}




