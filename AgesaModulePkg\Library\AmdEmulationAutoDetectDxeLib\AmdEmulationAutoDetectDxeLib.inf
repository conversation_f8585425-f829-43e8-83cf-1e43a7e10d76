#;*****************************************************************************
#;
#; Copyright (C) 2024 Advanced Micro Devices, Inc. All rights reserved
#;
#;******************************************************************************
[Defines]
  INF_VERSION                    = 0x00010006
  BASE_NAME                      = AmdEmulationAutoDetectDxe
  FILE_GUID                      = F97C3E81-98FC-408c-848D-F464B07081DD
  MODULE_TYPE                    = BASE
  VERSION_STRING                 = 1.0
  LIBRARY_CLASS                  = AmdEmulationAutoDetectLib|DXE_CORE DXE_DRIVER DXE_RUNTIME_DRIVER DXE_SMM_DRIVER UEFI_APPLICATION UEFI_DRIVER SMM_CORE

[Sources.common]
  AmdEmulationAutoDetectDxeLib.c

[Sources.IA32]

[Sources.X64]

[Packages]
  MdePkg/MdePkg.dec
  AgesaModulePkg/AgesaCommonModulePkg.dec
  AgesaModulePkg/AgesaModuleFchPkg.dec
  AgesaPkg/AgesaPkg.dec


[LibraryClasses]
  BaseLib
  AmdBaseLib
  PciCf8Lib
  PcdLib

[Guids]

[Protocols]

[Ppis]

[FeaturePcd]

[Pcd]
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdIdsDebugPrintEmulationAutoDetect
[Depex]
  TRUE




