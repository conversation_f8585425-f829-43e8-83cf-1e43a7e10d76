/*****************************************************************************
 *
 * Copyright (C) 2008-2024 Advanced Micro Devices, Inc. All rights reserved.
 *
 *******************************************************************************
 */

#ifndef __FCH_ESPI_CMD_PPI_H__
#define __FCH_ESPI_CMD_PPI_H__

extern EFI_GUID gAmdFchEspiCmdPpiGuid;

typedef struct _EFI_PEI_ESPI_CMD_PPI EFI_PEI_ESPI_CMD_PPI;

/**
  * eSPI In Band Reset
  *
  * @param This                   Pointer to an EFI_PEI_ESPI_CMD_PPI structure.
  * @param[in]  EspiBase          Espi MMIO base
  *
  */
typedef
VOID
(EFIAPI *EFI_PEI_ESPI_CMD_PPI_IN_BAND_RST) (
  IN CONST EFI_PEI_ESPI_CMD_PPI  *This,
  IN  UINT32                      EspiBase
  );

/**
  * eSPI Get Configuration
  *
  * @param This                   Pointer to an EFI_PEI_ESPI_CMD_PPI structure.
  * @param[in]  EspiBase          Espi MMIO base
  * @param[in]  RegAddr           Slave register address
  *
  * @retval    Register Value
  *
  */
typedef
UINT32
(EFIAPI *EFI_PEI_ESPI_CMD_PPI_GET_CONFIGURATION) (
  IN CONST EFI_PEI_ESPI_CMD_PPI  *This,
  IN  UINT32                      EspiBase,
  IN  UINT32                      RegAddr
 );

/**
  * eSPI Set Configuration
  *
  * @param This                   Pointer to an EFI_PEI_ESPI_CMD_PPI structure.
  * @param[in]  EspiBase          Espi MMIO base
  * @param[in]  RegAddr           Slave register address
  * @param[in]  Value             Slave register value
  *
  */
typedef
VOID
(EFIAPI *EFI_PEI_ESPI_CMD_PPI_SET_CONFIGURATION) (
  IN CONST EFI_PEI_ESPI_CMD_PPI  *This,
  IN  UINT32                      EspiBase,
  IN  UINT32                      RegAddr,
  IN  UINT32                      Value
  );

/**
  * eSPI SAFS FLASH Read
  *
  * @param This                   Pointer to an EFI_PEI_ESPI_CMD_PPI structure.
  * @param[in]  EspiBase          Espi MMIO base
  * @param[in]  Address           Address to read
  * @param[in]  Length            Length in byte to read
  * @param[in]  Buffer            Pointer to the data read to
  *
  * @retval EFI_STATUS
  *
  */
typedef
EFI_STATUS
(EFIAPI *EFI_PEI_ESPI_CMD_PPI_SAFS_FLASH_READ) (
  IN CONST EFI_PEI_ESPI_CMD_PPI  *This,
  IN  UINT32                      EspiBase,
  IN  UINT32                      Address,
  IN  UINT32                      Length,
  OUT UINT8                       *Buffer
  );

/**
  * eSPI SAFS FLASH Write
  *
  * @param  This                  Pointer to an EFI_PEI_ESPI_CMD_PPI structure.
  * @param[in]  EspiBase          Espi MMIO base
  * @param[in]  Address           Address to write
  * @param[in]  Length            Length in byte to write
  * @param[in]  Value             Pointer to the data to write
  *
  * @retval EFI_STATUS
  *
  */
typedef
EFI_STATUS
(EFIAPI *EFI_PEI_ESPI_CMD_PPI_SAFS_FLASH_WRITE) (
  IN CONST EFI_PEI_ESPI_CMD_PPI  *This,
  IN  UINT32                      EspiBase,
  IN  UINT32                      Address,
  IN  UINT32                      Length,
  IN  UINT8                       *Value
  );

/**
  * eSPI SAFS FLASH Erase
  *
  * @param  This                  Pointer to an EFI_PEI_ESPI_CMD_PPI structure.
  * @param[in]  EspiBase          Espi MMIO base
  * @param[in]  Address           Address to erase
  * @param[in]  Length            Block Size to erase
  *
  * @retval EFI_STATUS
  *
  */
typedef
EFI_STATUS
(EFIAPI *EFI_PEI_ESPI_CMD_PPI_SAFS_FLASH_ERASE) (
  IN CONST EFI_PEI_ESPI_CMD_PPI  *This,
  IN  UINT32                      EspiBase,
  IN  UINT32                      Address,
  IN  UINT32                      Length
  );

/**
  * eSPI SAFS RPMC OP1
  *
  * @param  This                  Pointer to an EFI_ESPI_CMD_PROTOCOL structure.
  * @param[in]  EspiBase          Espi MMIO base
  * @param[in]  RpmcFlashDev      Which RPMC flash device is targeted to
  * @param[in]  Length            Length in byte
  * @param[in]  Data              Pointer to data to send
  *
  * @retval EFI_STATUS
  *
  */
typedef
EFI_STATUS
(EFIAPI *EFI_PEI_ESPI_CMD_PPI_SAFS_RPMC_OP1) (
  IN CONST EFI_PEI_ESPI_CMD_PPI  *This,
  IN  UINT32                      EspiBase,
  IN  UINT8                       RpmcFlashDev,
  IN  UINT32                      Length,
  OUT UINT8                       *Data
  );

/**
  * eSPI SAFS RPMC OP2
  *
  * @param  This                  Pointer to an EFI_ESPI_CMD_PROTOCOL structure.
  * @param[in]  EspiBase          Espi MMIO base
  * @param[in]  RpmcFlashDev      Which RPMC flash device is targeted to
  * @param[in]  Length            Length in byte
  * @param[in]  Buffer            Pointer to buffer to receive
  *
  * @retval EFI_STATUS
  *
  */
typedef
EFI_STATUS
(EFIAPI *EFI_PEI_ESPI_CMD_PPI_SAFS_RPMC_OP2) (
  IN CONST EFI_PEI_ESPI_CMD_PPI  *This,
  IN  UINT32                      EspiBase,
  IN  UINT8                       RpmcFlashDev,
  IN  UINT32                      Length,
  IN  UINT8                       *Buffer
  );

/**
  * @brief This PPI provides the functions for SAFS eSpi flash commands
  * @param InBandRst             eSPI In Band Reset
  * @param GetConfiguration      eSPI Get Configuration
  * @param SetConfiguration      eSPI Set Configuration
  * @param SafsFlashRead         eSPI SAFS FLASH Read
  * @param SafsFlashWrite        eSPI SAFS FLASH Write
  * @param SafsFlashErase        eSPI SAFS FLASH Erase
  * @param SafsRpmcOp1           eSPI SAFS RPMC OP1
  * @param SafsRpmcOp2           eSPI SAFS RPMC OP2
  */
typedef struct _EFI_PEI_ESPI_CMD_PPI {
  EFI_PEI_ESPI_CMD_PPI_IN_BAND_RST       InBandRst;
  EFI_PEI_ESPI_CMD_PPI_GET_CONFIGURATION GetConfiguration;
  EFI_PEI_ESPI_CMD_PPI_SET_CONFIGURATION SetConfiguration;
  EFI_PEI_ESPI_CMD_PPI_SAFS_FLASH_READ   SafsFlashRead;
  EFI_PEI_ESPI_CMD_PPI_SAFS_FLASH_WRITE  SafsFlashWrite;
  EFI_PEI_ESPI_CMD_PPI_SAFS_FLASH_ERASE  SafsFlashErase;
  EFI_PEI_ESPI_CMD_PPI_SAFS_RPMC_OP1     SafsRpmcOp1;
  EFI_PEI_ESPI_CMD_PPI_SAFS_RPMC_OP2     SafsRpmcOp2;
} EFI_PEI_ESPI_CMD_PPI;

#endif

