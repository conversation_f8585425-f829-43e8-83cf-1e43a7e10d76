/*****************************************************************************
 * Copyright (C) 2008-2024 Advanced Micro Devices, Inc. All rights reserved.
 *
*****************************************************************************
*/
/* $NoKeywords:$ */
/**
 * @file
 *
 * Config Fch Spi (Lpc) controller
 *
 * Init Spi (Lpc) Controller features.
 *
 * @xrefitem bom "File Content Label" "Release Content"
 * @e project:     AGESA
 * @e sub-project: FCH
 * @e \$Revision: 309083 $   @e \$Date: 2014-12-09 09:28:24 -0800 (Tue, 09 Dec 2014) $
 *
 */
#include "FchPlatform.h"
#define FILECODE FCH_KUNLUN_FCHKUNLUNCORE_KUNLUN_KLLPCSPI_KLSPIENV_FILECODE

VOID
FchSetSpi (
  IN  VOID     *FchDataPtr
  );

/**
 * FchSetSpi - Config Spi controller before PCI emulation
 *
 *
 *
 * @param[in] FchDataPtr Fch configuration structure pointer.
 *
 */
VOID
FchSetSpi (
  IN  VOID     *FchDataPtr
  )
{

//  FCH_DATA_BLOCK         *LocalCfgPtr;
//  AMD_CONFIG_PARAMS      *StdHeader;
//  LocalCfgPtr = (FCH_DATA_BLOCK *) FchDataPtr;
//  StdHeader = LocalCfgPtr->StdHeader;
}

/**
 * FchInitEnvSpi - Config Spi controller before PCI emulation
 *
 *
 *
 * @param[in] FchDataPtr Fch configuration structure pointer.
 *
 */
VOID
FchInitEnvSpi (
  IN  VOID     *FchDataPtr
  )
{
  FCH_DATA_BLOCK         *LocalCfgPtr;
  AMD_CONFIG_PARAMS      *StdHeader;

  LocalCfgPtr = (FCH_DATA_BLOCK *) FchDataPtr;
  StdHeader = LocalCfgPtr->StdHeader;

  AGESA_TESTPOINT (TpFchInitEnvSpi, NULL);
  FchSetSpi (FchDataPtr);

  //
  // SSID for LPC Controller
  //
  if (LocalCfgPtr->Spi.LpcSsid != NULL ) {
    RwPci ((LPC_BUS_DEV_FUN << 16) + FCH_LPC_REG2C, AccessWidth32, 0x00, LocalCfgPtr->Spi.LpcSsid, StdHeader);
  }

}



