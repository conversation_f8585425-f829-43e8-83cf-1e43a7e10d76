#;*****************************************************************************
#;
#; Copyright (C) 2008-2024 Advanced Micro Devices, Inc. All rights reserved.
#;
#;******************************************************************************
#For EDKII use Only
[Defines]
  INF_VERSION                    = 0x00010006
  BASE_NAME                      = CcxPstatesZen5Lib
  FILE_GUID                      = F8C0F2CA-8436-4780-A814-3561B6935D21
  MODULE_TYPE                    = BASE
  VERSION_STRING                 = 1.0
  LIBRARY_CLASS                  = CcxPstatesLib

[Sources.common]
  CcxPstatesZen5Lib.c

[Packages]
  MdePkg/MdePkg.dec
  AgesaModulePkg/AgesaCommonModulePkg.dec
  AgesaModulePkg/AgesaModuleCcxPkg.dec
  AgesaPkg/AgesaPkg.dec

[LibraryClasses]
  BaseLib
  AmdBaseLib
  IdsLib

[Guids]

[Protocols]

[Ppis]

[Pcd]
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdCcxP0Setting
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdCcxP0Vid32
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdCcxP0Freq
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdAmdCcxPxAutoVid
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdAmdCcxPxAutoFreq

[Depex]



