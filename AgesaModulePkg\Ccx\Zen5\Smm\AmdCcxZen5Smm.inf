#;*****************************************************************************
#;
#; Copyright (C) 2008-2024 Advanced Micro Devices, Inc. All rights reserved.
#;
#;******************************************************************************
[Defines]
  INF_VERSION                    = 0x00010005
  BASE_NAME                      = AmdCcxZen5Smm
  FILE_GUID                      = 395B0181-C9B9-4E2E-A82E-6B0D027119F9
  MODULE_TYPE                    = DXE_SMM_DRIVER
  VERSION_STRING                 = 1.0
  PI_SPECIFICATION_VERSION       = 0x0001000A
  ENTRY_POINT                    = AmdCcxZen5SmmDriverEntryPoint

[Sources]
  AmdCcxZen5Smm.c

[Sources.ia32]

[Sources.x64]

[Packages]
  MdePkg/MdePkg.dec
  MdeModulePkg/MdeModulePkg.dec
  AgesaModulePkg/AgesaCommonModulePkg.dec
  AgesaModulePkg/AgesaFamily1AModulePkg.dec
  AgesaModulePkg/AgesaModuleCcxPkg.dec
  AgesaModulePkg/AgesaModulePspPkg.dec
  AgesaPkg/AgesaPkg.dec

[LibraryClasses]
  BaseLib
  PcdLib
  AmdBaseLib
  UefiDriverEntryPoint
  SmmServicesTableLib
  IdsLib
  AmdIdsHookLib
  CcxZen5IdsHookLibSmm
  CcxRolesLib
  CcxSetMcaLib
  HobLib

[Protocols]
  gFchSmmSwDispatch2ProtocolGuid   #CONSUME
  gAmdPspResumeServiceProtocolGuid #CONSUME
  gFchSmmSxDispatch2ProtocolGuid   #CONSUME

[Guids]
  gEfiAcpiVariableGuid

[Pcd]
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdAmdCcxS3SaveSmi
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdFchOemBeforePciRestoreSwSmi
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdPspS3WakeFromSmm
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdTransparentErrorLoggingEnable

[Depex]
  gAmdCcxZen5DepexSmmProtocolGuid AND
  gAmdCapsuleSmmHookProtocolGuid AND
  gFchSmmSwDispatch2ProtocolGuid AND
  gFchSmmSxDispatch2ProtocolGuid



