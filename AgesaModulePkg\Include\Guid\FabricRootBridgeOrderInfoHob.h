/*****************************************************************************
 * Copyright (C) 2019-2024 Advanced Micro Devices, Inc. All rights reserved.
 *
*****************************************************************************
*/

#ifndef FABRIC_ROOT_BRIDGE_ORDER_INFO_HOB_H
#define FABRIC_ROOT_BRIDGE_ORDER_INFO_HOB_H

// {ECF73CEA-86AE-4A41-8CB6-CDACDCE1F86C}
#define FABRIC_ROOT_BRIDGE_ORDER_INFO_HOB_GUID \
{ 0xecf73cea, 0x86<PERSON>, 0x4a41, { 0x8c, 0xb6, 0xcd, 0xac, 0xdc, 0xe1, 0xf8, 0x6c } }

extern EFI_GUID gFabricRootBridgeOrderInfoHobGuid;

#pragma pack (push, 1)

/**
 * @brief Fabric Root Bridge Order Info Hob
 */
typedef struct {
  UINT32 NumberOfRootBridges;  ///< The number of root bridges
  UINT32 RootBridgeOrder[];    ///< The order array of root bridges
} FABRIC_ROOT_BRIDGE_ORDER_INFO_HOB;

#pragma pack (pop)

#endif



