/*****************************************************************************
 * Copyright (C) 2008-2024 Advanced Micro Devices, Inc. All rights reserved.
 *
*****************************************************************************
*/
#include <Uefi.h>
#include <AGESA.h>
#include <Pi/PiMultiPhase.h>
#include <Library/HobLib.h>
#include <Library/IdsLib.h>
#include <Library/AmdPspApobLib.h>
#include <Guid/FabricRootBridgeOrderInfoHob.h>
#include <APOBCMN.h>
#include <Filecode.h>

/*----------------------------------------------------------------------------------------
 *                   D E F I N I T I O N S    A N D    M A C R O S
 *----------------------------------------------------------------------------------------
 */
#define FILECODE LIBRARY_FABRICROOTBRIDGEORDERLIB_FABRICROOTBRIDGEORDERLIB_FILECODE


/*----------------------------------------------------------------------------------------
 *           P R O T O T Y P E S     O F     L O C A L     F U  N C T I O N S
 *----------------------------------------------------------------------------------------
 */


/*----------------------------------------------------------------------------------------
 *                          E X P O R T E D    F U N C T I O N S
 *----------------------------------------------------------------------------------------
 */

/**
 * @brief Initial Fabric Root Bridge Order Hob
 */
VOID
EFIAPI
FabricRootBridgeOrderInit (
  VOID
  )
{
  APOB_TYPE_HEADER                               *ApobEntry;
  APOB_SYSTEM_ROOT_BRIDGE_ORDER_INFO_TYPE_STRUCT *ApobRBOrder;;
  EFI_STATUS                                     Status;
  FABRIC_ROOT_BRIDGE_ORDER_INFO_HOB              *RBOrderHob;
  UINT32                                         Index;

  Status = AmdPspGetApobEntryInstance (APOB_FABRIC, APOB_SYS_RB_ORDER_INFO_TYPE, 0, FALSE, &ApobEntry);
  if (EFI_ERROR (Status)) {
    IDS_HDT_CONSOLE (TOPO_TRACE, "Get APOB_SYS_RB_ORDER_INFO_TYPE from APOB fail\n");
    ASSERT (FALSE);
    return;
  }
  ApobRBOrder = (APOB_SYSTEM_ROOT_BRIDGE_ORDER_INFO_TYPE_STRUCT *) ApobEntry;

  RBOrderHob = BuildGuidHob (&gFabricRootBridgeOrderInfoHobGuid, sizeof (RBOrderHob[0]) + sizeof (RBOrderHob->RootBridgeOrder[0]) * ApobRBOrder->NumberOfRootBridges);
  if (RBOrderHob == NULL) {
    IDS_HDT_CONSOLE (TOPO_TRACE, "Create FABRIC_ROOT_BRIDGE_ORDER_INFO_HOB fail\n");
    ASSERT (FALSE);
    return;
  }

  RBOrderHob->NumberOfRootBridges = ApobRBOrder->NumberOfRootBridges;
  for (Index = 0; Index < RBOrderHob->NumberOfRootBridges; Index++) {
    RBOrderHob->RootBridgeOrder[Index] = ApobRBOrder->RootBridgeOrder[Index];
  }

  IDS_HDT_CONSOLE (TOPO_TRACE, "Root Bridge (IOM/S) Order: ");
  for (Index = 0; Index < RBOrderHob->NumberOfRootBridges; Index++) {
    IDS_HDT_CONSOLE (TOPO_TRACE, "%d ", RBOrderHob->RootBridgeOrder[Index]);
  }
  IDS_HDT_CONSOLE (TOPO_TRACE, "\n");
}
