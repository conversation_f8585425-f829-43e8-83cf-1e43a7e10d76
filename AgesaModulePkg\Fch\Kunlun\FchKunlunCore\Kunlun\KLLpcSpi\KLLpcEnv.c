/*****************************************************************************
 * Copyright (C) 2008-2024 Advanced Micro Devices, Inc. All rights reserved.
 *
*****************************************************************************
*/
/* $NoKeywords:$ */
/**
 * @file
 *
 * Config Fch LPC controller
 *
 * Init LPC Controller features.
 *
 * @xrefitem bom "File Content Label" "Release Content"
 * @e project:     AGESA
 * @e sub-project: FCH
 * @e \$Revision: 309083 $   @e \$Date: 2014-12-09 09:28:24 -0800 (Tue, 09 Dec 2014) $
 *
 */
#include "FchPlatform.h"
#define FILECODE FCH_KUNLUN_FCHKUNLUNCORE_KUNLUN_KLLPCSPI_KLLPCENV_FILECODE

extern VOID  FchInitEnvLpcProgram            (IN VOID  *FchDataPtr);

/**
 * FchInitEnvLpc - Config LPC controller before PCI emulation
 *
 *
 *
 * @param[in] FchDataPtr Fch configuration structure pointer.
 *
 */
VOID
FchInitEnvLpc (
  IN  VOID     *FchDataPtr
  )
{
  FCH_DATA_BLOCK         *LocalCfgPtr;
  AMD_CONFIG_PARAMS      *StdHeader;

  LocalCfgPtr = (FCH_DATA_BLOCK *) FchDataPtr;
  StdHeader = LocalCfgPtr->StdHeader;

  AGESA_TESTPOINT (TpFchInitEnvLpc, NULL);
  //
  // LPC CFG programming
  //
  if ( LocalCfgPtr->Spi.LpcEnable ) {
    //FchAoacPowerOnDev (FCH_AOAC_LPC, 1);
    LocalCfgPtr->FchRunTime.FchDeviceEnableMap |= BIT4;
  } else {
    //FchAoacPowerOnDev (FCH_AOAC_LPC, 0);
    LocalCfgPtr->FchRunTime.FchDeviceEnableMap &= (~BIT4);
    return; //return if LPC is disabled
  }
  //
  // Initialization of pci config space
  //
  FchInitEnvLpcProgram (FchDataPtr);

  //
  // LPC MSI
  //
  if ( LocalCfgPtr->Spi.LpcMsiEnable ) {
    RwPci ((LPC_BUS_DEV_FUN << 16) + FCH_LPC_REG78, AccessWidth32, ~(UINT32) BIT1, BIT1, StdHeader);
  }
}




