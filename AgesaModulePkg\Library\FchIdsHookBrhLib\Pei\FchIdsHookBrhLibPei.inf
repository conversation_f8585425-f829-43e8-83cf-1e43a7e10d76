#;*****************************************************************************
#;
#; Copyright (C) 2008-2024 Advanced Micro Devices, Inc. All rights reserved.
#;
#;*****************************************************************************
[Defines]
  INF_VERSION                    = 0x00010006
  BASE_NAME                      = FchIdsHookBrhLibPei
  FILE_GUID                      = 12b93f4b-f14e-4228-8159-7e6a4d9b182a
  MODULE_TYPE                    = PEIM
  VERSION_STRING                 = 1.0
  LIBRARY_CLASS                  = FchIdsHookLib

[Sources.common]
  FchIdsHookBrhLibPei.c

[Packages]
  MdePkg/MdePkg.dec
  AgesaPkg/AgesaPkg.dec
  AgesaModulePkg/AgesaCommonModulePkg.dec
  AgesaModulePkg/AgesaModuleFchPkg.dec
  AgesaModulePkg/Fch/Kunlun/FchKunlun.dec

[LibraryClasses]
  AmdBaseLib
  AmdIdsDebugPrintLib

[Guids]

[Protocols]

[Ppis]

[FeaturePcd]

[Pcd]
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdSataEnable
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdSataEnable2
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdSataSgpioMultiDieEnable
  gEfiAmdAgesaModulePkgTokenSpaceGuid.FchRTDeviceEnableMap
  gEfiAmdAgesaModulePkgTokenSpaceGuid.FchRTDeviceEnableMapEx

[Depex]
  TRUE

[BuildOptions]

