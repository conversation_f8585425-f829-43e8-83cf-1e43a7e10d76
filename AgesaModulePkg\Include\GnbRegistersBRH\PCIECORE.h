/*****************************************************************************
 *
 * Copyright (C) 2008-2024 Advanced Micro Devices, Inc. All rights reserved.
 *
 *******************************************************************************
 **/

#ifndef _PCIECORE_H_
#define _PCIECORE_H_


/***********************************************************
* Register Name : PCIE_LC_STATE10
************************************************************/

#define PCIE_LC_STATE10_LC_PREV_STATE40_OFFSET                 0
#define PCIE_LC_STATE10_LC_PREV_STATE40_MASK                   0x3f

#define PCIE_LC_STATE10_Reserved_7_6_OFFSET                    6
#define PCIE_LC_STATE10_Reserved_7_6_MASK                      0xc0

#define PCIE_LC_STATE10_LC_PREV_STATE41_OFFSET                 8
#define PCIE_LC_STATE10_LC_PREV_STATE41_MASK                   0x3f00

#define PCIE_LC_STATE10_Reserved_15_14_OFFSET                  14
#define PCIE_LC_STATE10_Reserved_15_14_MASK                    0xc000

#define PCIE_LC_STATE10_LC_PREV_STATE42_OFFSET                 16
#define PCIE_LC_STATE10_LC_PREV_STATE42_MASK                   0x3f0000

#define PCIE_LC_STATE10_Reserved_23_22_OFFSET                  22
#define PCIE_LC_STATE10_Reserved_23_22_MASK                    0xc00000

#define PCIE_LC_STATE10_LC_PREV_STATE43_OFFSET                 24
#define PCIE_LC_STATE10_LC_PREV_STATE43_MASK                   0x3f000000

#define PCIE_LC_STATE10_Reserved_31_30_OFFSET                  30
#define PCIE_LC_STATE10_Reserved_31_30_MASK                    0xc0000000

typedef union {
  struct {
    UINT32                                     LC_PREV_STATE40:6;
    UINT32                                        Reserved_7_6:2;
    UINT32                                     LC_PREV_STATE41:6;
    UINT32                                      Reserved_15_14:2;
    UINT32                                     LC_PREV_STATE42:6;
    UINT32                                      Reserved_23_22:2;
    UINT32                                     LC_PREV_STATE43:6;
    UINT32                                      Reserved_31_30:2;
  } Field;
  UINT32 Value;
} PCIE_LC_STATE10_STRUCT;
#define SMN_PCIE0NBIO0_PCIE_LC_STATE10_ADDRESS                        0x1a380098UL
#define SMN_PCIE0NBIO1_PCIE_LC_STATE10_ADDRESS                        0x1a580098UL
#define SMN_PCIE1NBIO0_PCIE_LC_STATE10_ADDRESS                        0x1a480098UL
#define SMN_PCIE1NBIO1_PCIE_LC_STATE10_ADDRESS                        0x1a680098UL
#define SMN_PCIE2NBIO0_PCIE_LC_STATE10_ADDRESS                        0x1a780098UL
#define SMN_PCIE2NBIO1_PCIE_LC_STATE10_ADDRESS                        0x1a980098UL
#define SMN_PCIE3NBIO0_PCIE_LC_STATE10_ADDRESS                        0x1a880098UL
#define SMN_PCIE3NBIO1_PCIE_LC_STATE10_ADDRESS                        0x1aa80098UL
#define SMN_PCIE5NBIO0_PCIE_LC_STATE10_ADDRESS                        0x1ab80098UL
#define SMN_PCIE5NBIO1_PCIE_LC_STATE10_ADDRESS                        0x1ad80098UL


/***********************************************************
* Register Name : PCIE_LC_STATE11
************************************************************/

#define PCIE_LC_STATE11_LC_PREV_STATE44_OFFSET                 0
#define PCIE_LC_STATE11_LC_PREV_STATE44_MASK                   0x3f

#define PCIE_LC_STATE11_Reserved_7_6_OFFSET                    6
#define PCIE_LC_STATE11_Reserved_7_6_MASK                      0xc0

#define PCIE_LC_STATE11_LC_PREV_STATE45_OFFSET                 8
#define PCIE_LC_STATE11_LC_PREV_STATE45_MASK                   0x3f00

#define PCIE_LC_STATE11_Reserved_15_14_OFFSET                  14
#define PCIE_LC_STATE11_Reserved_15_14_MASK                    0xc000

#define PCIE_LC_STATE11_LC_PREV_STATE46_OFFSET                 16
#define PCIE_LC_STATE11_LC_PREV_STATE46_MASK                   0x3f0000

#define PCIE_LC_STATE11_Reserved_23_22_OFFSET                  22
#define PCIE_LC_STATE11_Reserved_23_22_MASK                    0xc00000

#define PCIE_LC_STATE11_LC_PREV_STATE47_OFFSET                 24
#define PCIE_LC_STATE11_LC_PREV_STATE47_MASK                   0x3f000000

#define PCIE_LC_STATE11_Reserved_31_30_OFFSET                  30
#define PCIE_LC_STATE11_Reserved_31_30_MASK                    0xc0000000

typedef union {
  struct {
    UINT32                                     LC_PREV_STATE44:6;
    UINT32                                        Reserved_7_6:2;
    UINT32                                     LC_PREV_STATE45:6;
    UINT32                                      Reserved_15_14:2;
    UINT32                                     LC_PREV_STATE46:6;
    UINT32                                      Reserved_23_22:2;
    UINT32                                     LC_PREV_STATE47:6;
    UINT32                                      Reserved_31_30:2;
  } Field;
  UINT32 Value;
} PCIE_LC_STATE11_STRUCT;
#define SMN_PCIE0NBIO0_PCIE_LC_STATE11_ADDRESS                        0x1a38009cUL
#define SMN_PCIE0NBIO1_PCIE_LC_STATE11_ADDRESS                        0x1a58009cUL
#define SMN_PCIE1NBIO0_PCIE_LC_STATE11_ADDRESS                        0x1a48009cUL
#define SMN_PCIE1NBIO1_PCIE_LC_STATE11_ADDRESS                        0x1a68009cUL
#define SMN_PCIE2NBIO0_PCIE_LC_STATE11_ADDRESS                        0x1a78009cUL
#define SMN_PCIE2NBIO1_PCIE_LC_STATE11_ADDRESS                        0x1a98009cUL
#define SMN_PCIE3NBIO0_PCIE_LC_STATE11_ADDRESS                        0x1a88009cUL
#define SMN_PCIE3NBIO1_PCIE_LC_STATE11_ADDRESS                        0x1aa8009cUL
#define SMN_PCIE5NBIO0_PCIE_LC_STATE11_ADDRESS                        0x1ab8009cUL
#define SMN_PCIE5NBIO1_PCIE_LC_STATE11_ADDRESS                        0x1ad8009cUL


/***********************************************************
* Register Name : PCIE_LC_STATE6
************************************************************/

#define PCIE_LC_STATE6_LC_PREV_STATE24_OFFSET                  0
#define PCIE_LC_STATE6_LC_PREV_STATE24_MASK                    0x3f

#define PCIE_LC_STATE6_Reserved_7_6_OFFSET                     6
#define PCIE_LC_STATE6_Reserved_7_6_MASK                       0xc0

#define PCIE_LC_STATE6_LC_PREV_STATE25_OFFSET                  8
#define PCIE_LC_STATE6_LC_PREV_STATE25_MASK                    0x3f00

#define PCIE_LC_STATE6_Reserved_15_14_OFFSET                   14
#define PCIE_LC_STATE6_Reserved_15_14_MASK                     0xc000

#define PCIE_LC_STATE6_LC_PREV_STATE26_OFFSET                  16
#define PCIE_LC_STATE6_LC_PREV_STATE26_MASK                    0x3f0000

#define PCIE_LC_STATE6_Reserved_23_22_OFFSET                   22
#define PCIE_LC_STATE6_Reserved_23_22_MASK                     0xc00000

#define PCIE_LC_STATE6_LC_PREV_STATE27_OFFSET                  24
#define PCIE_LC_STATE6_LC_PREV_STATE27_MASK                    0x3f000000

#define PCIE_LC_STATE6_Reserved_31_30_OFFSET                   30
#define PCIE_LC_STATE6_Reserved_31_30_MASK                     0xc0000000

typedef union {
  struct {
    UINT32                                     LC_PREV_STATE24:6;
    UINT32                                        Reserved_7_6:2;
    UINT32                                     LC_PREV_STATE25:6;
    UINT32                                      Reserved_15_14:2;
    UINT32                                     LC_PREV_STATE26:6;
    UINT32                                      Reserved_23_22:2;
    UINT32                                     LC_PREV_STATE27:6;
    UINT32                                      Reserved_31_30:2;
  } Field;
  UINT32 Value;
} PCIE_LC_STATE6_STRUCT;
#define SMN_PCIE0NBIO0_PCIE_LC_STATE6_ADDRESS                         0x1a380088UL
#define SMN_PCIE0NBIO1_PCIE_LC_STATE6_ADDRESS                         0x1a580088UL
#define SMN_PCIE1NBIO0_PCIE_LC_STATE6_ADDRESS                         0x1a480088UL
#define SMN_PCIE1NBIO1_PCIE_LC_STATE6_ADDRESS                         0x1a680088UL
#define SMN_PCIE2NBIO0_PCIE_LC_STATE6_ADDRESS                         0x1a780088UL
#define SMN_PCIE2NBIO1_PCIE_LC_STATE6_ADDRESS                         0x1a980088UL
#define SMN_PCIE3NBIO0_PCIE_LC_STATE6_ADDRESS                         0x1a880088UL
#define SMN_PCIE3NBIO1_PCIE_LC_STATE6_ADDRESS                         0x1aa80088UL
#define SMN_PCIE5NBIO0_PCIE_LC_STATE6_ADDRESS                         0x1ab80088UL
#define SMN_PCIE5NBIO1_PCIE_LC_STATE6_ADDRESS                         0x1ad80088UL


/***********************************************************
* Register Name : PCIE_LC_STATE7
************************************************************/

#define PCIE_LC_STATE7_LC_PREV_STATE28_OFFSET                  0
#define PCIE_LC_STATE7_LC_PREV_STATE28_MASK                    0x3f

#define PCIE_LC_STATE7_Reserved_7_6_OFFSET                     6
#define PCIE_LC_STATE7_Reserved_7_6_MASK                       0xc0

#define PCIE_LC_STATE7_LC_PREV_STATE29_OFFSET                  8
#define PCIE_LC_STATE7_LC_PREV_STATE29_MASK                    0x3f00

#define PCIE_LC_STATE7_Reserved_15_14_OFFSET                   14
#define PCIE_LC_STATE7_Reserved_15_14_MASK                     0xc000

#define PCIE_LC_STATE7_LC_PREV_STATE30_OFFSET                  16
#define PCIE_LC_STATE7_LC_PREV_STATE30_MASK                    0x3f0000

#define PCIE_LC_STATE7_Reserved_23_22_OFFSET                   22
#define PCIE_LC_STATE7_Reserved_23_22_MASK                     0xc00000

#define PCIE_LC_STATE7_LC_PREV_STATE31_OFFSET                  24
#define PCIE_LC_STATE7_LC_PREV_STATE31_MASK                    0x3f000000

#define PCIE_LC_STATE7_Reserved_31_30_OFFSET                   30
#define PCIE_LC_STATE7_Reserved_31_30_MASK                     0xc0000000

typedef union {
  struct {
    UINT32                                     LC_PREV_STATE28:6;
    UINT32                                        Reserved_7_6:2;
    UINT32                                     LC_PREV_STATE29:6;
    UINT32                                      Reserved_15_14:2;
    UINT32                                     LC_PREV_STATE30:6;
    UINT32                                      Reserved_23_22:2;
    UINT32                                     LC_PREV_STATE31:6;
    UINT32                                      Reserved_31_30:2;
  } Field;
  UINT32 Value;
} PCIE_LC_STATE7_STRUCT;
#define SMN_PCIE0NBIO0_PCIE_LC_STATE7_ADDRESS                         0x1a38008cUL
#define SMN_PCIE0NBIO1_PCIE_LC_STATE7_ADDRESS                         0x1a58008cUL
#define SMN_PCIE1NBIO0_PCIE_LC_STATE7_ADDRESS                         0x1a48008cUL
#define SMN_PCIE1NBIO1_PCIE_LC_STATE7_ADDRESS                         0x1a68008cUL
#define SMN_PCIE2NBIO0_PCIE_LC_STATE7_ADDRESS                         0x1a78008cUL
#define SMN_PCIE2NBIO1_PCIE_LC_STATE7_ADDRESS                         0x1a98008cUL
#define SMN_PCIE3NBIO0_PCIE_LC_STATE7_ADDRESS                         0x1a88008cUL
#define SMN_PCIE3NBIO1_PCIE_LC_STATE7_ADDRESS                         0x1aa8008cUL
#define SMN_PCIE5NBIO0_PCIE_LC_STATE7_ADDRESS                         0x1ab8008cUL
#define SMN_PCIE5NBIO1_PCIE_LC_STATE7_ADDRESS                         0x1ad8008cUL


/***********************************************************
* Register Name : PCIE_LC_STATE8
************************************************************/

#define PCIE_LC_STATE8_LC_PREV_STATE32_OFFSET                  0
#define PCIE_LC_STATE8_LC_PREV_STATE32_MASK                    0x3f

#define PCIE_LC_STATE8_Reserved_7_6_OFFSET                     6
#define PCIE_LC_STATE8_Reserved_7_6_MASK                       0xc0

#define PCIE_LC_STATE8_LC_PREV_STATE33_OFFSET                  8
#define PCIE_LC_STATE8_LC_PREV_STATE33_MASK                    0x3f00

#define PCIE_LC_STATE8_Reserved_15_14_OFFSET                   14
#define PCIE_LC_STATE8_Reserved_15_14_MASK                     0xc000

#define PCIE_LC_STATE8_LC_PREV_STATE34_OFFSET                  16
#define PCIE_LC_STATE8_LC_PREV_STATE34_MASK                    0x3f0000

#define PCIE_LC_STATE8_Reserved_23_22_OFFSET                   22
#define PCIE_LC_STATE8_Reserved_23_22_MASK                     0xc00000

#define PCIE_LC_STATE8_LC_PREV_STATE35_OFFSET                  24
#define PCIE_LC_STATE8_LC_PREV_STATE35_MASK                    0x3f000000

#define PCIE_LC_STATE8_Reserved_31_30_OFFSET                   30
#define PCIE_LC_STATE8_Reserved_31_30_MASK                     0xc0000000

typedef union {
  struct {
    UINT32                                     LC_PREV_STATE32:6;
    UINT32                                        Reserved_7_6:2;
    UINT32                                     LC_PREV_STATE33:6;
    UINT32                                      Reserved_15_14:2;
    UINT32                                     LC_PREV_STATE34:6;
    UINT32                                      Reserved_23_22:2;
    UINT32                                     LC_PREV_STATE35:6;
    UINT32                                      Reserved_31_30:2;
  } Field;
  UINT32 Value;
} PCIE_LC_STATE8_STRUCT;
#define SMN_PCIE0NBIO0_PCIE_LC_STATE8_ADDRESS                         0x1a380090UL
#define SMN_PCIE0NBIO1_PCIE_LC_STATE8_ADDRESS                         0x1a580090UL
#define SMN_PCIE1NBIO0_PCIE_LC_STATE8_ADDRESS                         0x1a480090UL
#define SMN_PCIE1NBIO1_PCIE_LC_STATE8_ADDRESS                         0x1a680090UL
#define SMN_PCIE2NBIO0_PCIE_LC_STATE8_ADDRESS                         0x1a780090UL
#define SMN_PCIE2NBIO1_PCIE_LC_STATE8_ADDRESS                         0x1a980090UL
#define SMN_PCIE3NBIO0_PCIE_LC_STATE8_ADDRESS                         0x1a880090UL
#define SMN_PCIE3NBIO1_PCIE_LC_STATE8_ADDRESS                         0x1aa80090UL
#define SMN_PCIE5NBIO0_PCIE_LC_STATE8_ADDRESS                         0x1ab80090UL
#define SMN_PCIE5NBIO1_PCIE_LC_STATE8_ADDRESS                         0x1ad80090UL


/***********************************************************
* Register Name : PCIE_LC_STATE9
************************************************************/

#define PCIE_LC_STATE9_LC_PREV_STATE36_OFFSET                  0
#define PCIE_LC_STATE9_LC_PREV_STATE36_MASK                    0x3f

#define PCIE_LC_STATE9_Reserved_7_6_OFFSET                     6
#define PCIE_LC_STATE9_Reserved_7_6_MASK                       0xc0

#define PCIE_LC_STATE9_LC_PREV_STATE37_OFFSET                  8
#define PCIE_LC_STATE9_LC_PREV_STATE37_MASK                    0x3f00

#define PCIE_LC_STATE9_Reserved_15_14_OFFSET                   14
#define PCIE_LC_STATE9_Reserved_15_14_MASK                     0xc000

#define PCIE_LC_STATE9_LC_PREV_STATE38_OFFSET                  16
#define PCIE_LC_STATE9_LC_PREV_STATE38_MASK                    0x3f0000

#define PCIE_LC_STATE9_Reserved_23_22_OFFSET                   22
#define PCIE_LC_STATE9_Reserved_23_22_MASK                     0xc00000

#define PCIE_LC_STATE9_LC_PREV_STATE39_OFFSET                  24
#define PCIE_LC_STATE9_LC_PREV_STATE39_MASK                    0x3f000000

#define PCIE_LC_STATE9_Reserved_31_30_OFFSET                   30
#define PCIE_LC_STATE9_Reserved_31_30_MASK                     0xc0000000

typedef union {
  struct {
    UINT32                                     LC_PREV_STATE36:6;
    UINT32                                        Reserved_7_6:2;
    UINT32                                     LC_PREV_STATE37:6;
    UINT32                                      Reserved_15_14:2;
    UINT32                                     LC_PREV_STATE38:6;
    UINT32                                      Reserved_23_22:2;
    UINT32                                     LC_PREV_STATE39:6;
    UINT32                                      Reserved_31_30:2;
  } Field;
  UINT32 Value;
} PCIE_LC_STATE9_STRUCT;
#define SMN_PCIE0NBIO0_PCIE_LC_STATE9_ADDRESS                         0x1a380094UL
#define SMN_PCIE0NBIO1_PCIE_LC_STATE9_ADDRESS                         0x1a580094UL
#define SMN_PCIE1NBIO0_PCIE_LC_STATE9_ADDRESS                         0x1a480094UL
#define SMN_PCIE1NBIO1_PCIE_LC_STATE9_ADDRESS                         0x1a680094UL
#define SMN_PCIE2NBIO0_PCIE_LC_STATE9_ADDRESS                         0x1a780094UL
#define SMN_PCIE2NBIO1_PCIE_LC_STATE9_ADDRESS                         0x1a980094UL
#define SMN_PCIE3NBIO0_PCIE_LC_STATE9_ADDRESS                         0x1a880094UL
#define SMN_PCIE3NBIO1_PCIE_LC_STATE9_ADDRESS                         0x1aa80094UL
#define SMN_PCIE5NBIO0_PCIE_LC_STATE9_ADDRESS                         0x1ab80094UL
#define SMN_PCIE5NBIO1_PCIE_LC_STATE9_ADDRESS                         0x1ad80094UL


/***********************************************************
* Register Name : SWRST_COMMAND_STATUS
************************************************************/

#define SWRST_COMMAND_STATUS_RECONFIGURE_OFFSET                0
#define SWRST_COMMAND_STATUS_RECONFIGURE_MASK                  0x1

#define SWRST_COMMAND_STATUS_ATOMIC_RESET_OFFSET               1
#define SWRST_COMMAND_STATUS_ATOMIC_RESET_MASK                 0x2

#define SWRST_COMMAND_STATUS_Reserved_15_2_OFFSET              2
#define SWRST_COMMAND_STATUS_Reserved_15_2_MASK                0xfffc

#define SWRST_COMMAND_STATUS_RESET_COMPLETE_OFFSET             16
#define SWRST_COMMAND_STATUS_RESET_COMPLETE_MASK               0x10000

#define SWRST_COMMAND_STATUS_WAIT_STATE_OFFSET                 17
#define SWRST_COMMAND_STATUS_WAIT_STATE_MASK                   0x20000

#define SWRST_COMMAND_STATUS_PERST_ASRT_OFFSET                 18
#define SWRST_COMMAND_STATUS_PERST_ASRT_MASK                   0x40000

#define SWRST_COMMAND_STATUS_Reserved_23_19_OFFSET             19
#define SWRST_COMMAND_STATUS_Reserved_23_19_MASK               0xf80000

#define SWRST_COMMAND_STATUS_SWUS_LINK_RESET_OFFSET            24
#define SWRST_COMMAND_STATUS_SWUS_LINK_RESET_MASK              0x1000000

#define SWRST_COMMAND_STATUS_SWUS_LINK_RESET_CFG_ONLY_OFFSET   25
#define SWRST_COMMAND_STATUS_SWUS_LINK_RESET_CFG_ONLY_MASK     0x2000000

#define SWRST_COMMAND_STATUS_SWUS_LINK_RESET_PHY_CALIB_OFFSET  26
#define SWRST_COMMAND_STATUS_SWUS_LINK_RESET_PHY_CALIB_MASK    0x4000000

#define SWRST_COMMAND_STATUS_SWDS_LINK_RESET_OFFSET            27
#define SWRST_COMMAND_STATUS_SWDS_LINK_RESET_MASK              0x8000000

#define SWRST_COMMAND_STATUS_SWDS_LINK_RESET_CFG_ONLY_OFFSET   28
#define SWRST_COMMAND_STATUS_SWDS_LINK_RESET_CFG_ONLY_MASK     0x10000000

#define SWRST_COMMAND_STATUS_LINK_RESET_TYPE_HOT_RESET_OFFSET  29
#define SWRST_COMMAND_STATUS_LINK_RESET_TYPE_HOT_RESET_MASK    0x20000000

#define SWRST_COMMAND_STATUS_LINK_RESET_TYPE_LINK_DISABLE_OFFSET 30
#define SWRST_COMMAND_STATUS_LINK_RESET_TYPE_LINK_DISABLE_MASK 0x40000000

#define SWRST_COMMAND_STATUS_LINK_RESET_TYPE_LINK_DOWN_OFFSET  31
#define SWRST_COMMAND_STATUS_LINK_RESET_TYPE_LINK_DOWN_MASK    0x80000000

typedef union {
  struct {
    UINT32                                         RECONFIGURE:1;
    UINT32                                        ATOMIC_RESET:1;
    UINT32                                       Reserved_15_2:14;
    UINT32                                      RESET_COMPLETE:1;
    UINT32                                          WAIT_STATE:1;
    UINT32                                          PERST_ASRT:1;
    UINT32                                      Reserved_23_19:5;
    UINT32                                     SWUS_LINK_RESET:1;
    UINT32                            SWUS_LINK_RESET_CFG_ONLY:1;
    UINT32                           SWUS_LINK_RESET_PHY_CALIB:1;
    UINT32                                     SWDS_LINK_RESET:1;
    UINT32                            SWDS_LINK_RESET_CFG_ONLY:1;
    UINT32                           LINK_RESET_TYPE_HOT_RESET:1;
    UINT32                        LINK_RESET_TYPE_LINK_DISABLE:1;
    UINT32                           LINK_RESET_TYPE_LINK_DOWN:1;
  } Field;
  UINT32 Value;
} SWRST_COMMAND_STATUS_STRUCT;
#define SMN_PCIE0NBIO0_SWRST_COMMAND_STATUS_ADDRESS                   0x1a380400UL
#define SMN_PCIE0NBIO1_SWRST_COMMAND_STATUS_ADDRESS                   0x1a580400UL
#define SMN_PCIE1NBIO0_SWRST_COMMAND_STATUS_ADDRESS                   0x1a480400UL
#define SMN_PCIE1NBIO1_SWRST_COMMAND_STATUS_ADDRESS                   0x1a680400UL
#define SMN_PCIE2NBIO0_SWRST_COMMAND_STATUS_ADDRESS                   0x1a780400UL
#define SMN_PCIE2NBIO1_SWRST_COMMAND_STATUS_ADDRESS                   0x1a980400UL
#define SMN_PCIE3NBIO0_SWRST_COMMAND_STATUS_ADDRESS                   0x1a880400UL
#define SMN_PCIE3NBIO1_SWRST_COMMAND_STATUS_ADDRESS                   0x1aa80400UL
#define SMN_PCIE5NBIO0_SWRST_COMMAND_STATUS_ADDRESS                   0x1ab80400UL
#define SMN_PCIE5NBIO1_SWRST_COMMAND_STATUS_ADDRESS                   0x1ad80400UL


/***********************************************************
* Register Name : SWRST_CONTROL_6
************************************************************/

#define SWRST_CONTROL_6_HOLD_TRAINING_A_OFFSET                 0
#define SWRST_CONTROL_6_HOLD_TRAINING_A_MASK                   0x1

#define SWRST_CONTROL_6_HOLD_TRAINING_B_OFFSET                 1
#define SWRST_CONTROL_6_HOLD_TRAINING_B_MASK                   0x2

#define SWRST_CONTROL_6_HOLD_TRAINING_C_OFFSET                 2
#define SWRST_CONTROL_6_HOLD_TRAINING_C_MASK                   0x4

#define SWRST_CONTROL_6_HOLD_TRAINING_D_OFFSET                 3
#define SWRST_CONTROL_6_HOLD_TRAINING_D_MASK                   0x8

#define SWRST_CONTROL_6_HOLD_TRAINING_E_OFFSET                 4
#define SWRST_CONTROL_6_HOLD_TRAINING_E_MASK                   0x10

#define SWRST_CONTROL_6_HOLD_TRAINING_F_OFFSET                 5
#define SWRST_CONTROL_6_HOLD_TRAINING_F_MASK                   0x20

#define SWRST_CONTROL_6_HOLD_TRAINING_G_OFFSET                 6
#define SWRST_CONTROL_6_HOLD_TRAINING_G_MASK                   0x40

#define SWRST_CONTROL_6_HOLD_TRAINING_H_OFFSET                 7
#define SWRST_CONTROL_6_HOLD_TRAINING_H_MASK                   0x80

#define SWRST_CONTROL_6_HOLD_TRAINING_I_OFFSET                 8
#define SWRST_CONTROL_6_HOLD_TRAINING_I_MASK                   0x100

#define SWRST_CONTROL_6_HOLD_TRAINING_J_OFFSET                 9
#define SWRST_CONTROL_6_HOLD_TRAINING_J_MASK                   0x200

#define SWRST_CONTROL_6_HOLD_TRAINING_K_OFFSET                 10
#define SWRST_CONTROL_6_HOLD_TRAINING_K_MASK                   0x400

#define SWRST_CONTROL_6_Reserved_31_11_OFFSET                  11
#define SWRST_CONTROL_6_Reserved_31_11_MASK                    0xfffff800

typedef union {
  struct {
    UINT32                                     HOLD_TRAINING_A:1;
    UINT32                                     HOLD_TRAINING_B:1;
    UINT32                                     HOLD_TRAINING_C:1;
    UINT32                                     HOLD_TRAINING_D:1;
    UINT32                                     HOLD_TRAINING_E:1;
    UINT32                                     HOLD_TRAINING_F:1;
    UINT32                                     HOLD_TRAINING_G:1;
    UINT32                                     HOLD_TRAINING_H:1;
    UINT32                                     HOLD_TRAINING_I:1;
    UINT32                                     HOLD_TRAINING_J:1;
    UINT32                                     HOLD_TRAINING_K:1;
    UINT32                                      Reserved_31_11:21;
  } Field;
  UINT32 Value;
} SWRST_CONTROL_6_STRUCT;
#define SMN_PCIE0NBIO0_SWRST_CONTROL_6_ADDRESS                        0x1a380428UL
#define SMN_PCIE0NBIO1_SWRST_CONTROL_6_ADDRESS                        0x1a580428UL
#define SMN_PCIE1NBIO0_SWRST_CONTROL_6_ADDRESS                        0x1a480428UL
#define SMN_PCIE1NBIO1_SWRST_CONTROL_6_ADDRESS                        0x1a680428UL
#define SMN_PCIE2NBIO0_SWRST_CONTROL_6_ADDRESS                        0x1a780428UL
#define SMN_PCIE2NBIO1_SWRST_CONTROL_6_ADDRESS                        0x1a980428UL
#define SMN_PCIE3NBIO0_SWRST_CONTROL_6_ADDRESS                        0x1a880428UL
#define SMN_PCIE3NBIO1_SWRST_CONTROL_6_ADDRESS                        0x1aa80428UL
#define SMN_PCIE5NBIO0_SWRST_CONTROL_6_ADDRESS                        0x1ab80428UL
#define SMN_PCIE5NBIO1_SWRST_CONTROL_6_ADDRESS                        0x1ad80428UL

#endif /* _PCIECORE_H_ */

