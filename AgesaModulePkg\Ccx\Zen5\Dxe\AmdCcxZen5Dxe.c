/*****************************************************************************
 *
 * Copyright (C) 2008-2024 Advanced Micro Devices, Inc. All rights reserved.
 *
 *******************************************************************************
 */


#include <PiDxe.h>
#include <Library/BaseLib.h>
#include <Library/PcdLib.h>
#include <Protocol/MpService.h>
#include <Library/CcxRolesLib.h>
#include <Library/CcxBaseX86Lib.h>
#include <Library/AmdBaseLib.h>
#include <Library/AmdPspBaseLibV2.h>
#include <Library/AmdIdsHookLib.h>
#include <Library/AmdPspMboxLibV2.h>
#include <Library/AmdHeapLib.h>
#include <Library/CcxSmmAccess2Lib.h>
#include <Library/DxeCcxBaseX86ServicesLib.h>
#include <Library/UefiBootServicesTableLib.h>
#include <Library/CcxDxeLib.h>
#include <Library/CcxZen5SegRmpDxeLib.h>
#include <Protocol/AmdMpServicesPreReqProtocol.h>
#include <Protocol/AmdCcxProtocol.h>
#include <Protocol/SmmControl2.h>
#include <Protocol/AmdNbioServicesProtocol.h>
#include <Protocol/FabricTopologyServices2.h>
#include <Protocol/SocZen5ServicesProtocol.h>
#include "AmdCcxZen5Dxe.h"
#include "CcxZen5AcpiServicesDxe.h"
#include "CcxZen5SmbiosDxe.h"
#include "CcxRegistersZen5.h"
#include <cpuRegisters.h>
#include <Filecode.h>

#define FILECODE CCX_ZEN5_DXE_AMDCCXZEN5DXE_FILECODE


/*----------------------------------------------------------------------------------------
 *                               D E F I N I T I O N S
 *----------------------------------------------------------------------------------------
 */
#define CPPC_MIN_MHZ      1200
#define CPPC_MAX_MHZ      (CPPC_MIN_MHZ * 2)
#define CPPC_SCALAR       0xFF

/*----------------------------------------------------------------------------------------
 *                           G L O B A L   V A R I A B L E S
 *----------------------------------------------------------------------------------------
 */
EFI_EVENT            CcxZen5InitWithMpServicesEvent;
EFI_EVENT            CcxZen5OcCallbackEvent;
EFI_EVENT            CcxZen5OcCallbackOnMpServicesEvent;
AP_MSR_SYNC          mRmpMsrSyncTable[] =
{
  { 0xC0010132, 0x0000000000000000, 0xFFFFFFFFFFFFFFFF  },
  { 0xC0010133, 0x0000000000000000, 0xFFFFFFFFFFFFFFFF  },
  { MSR_SYS_CFG, 0x0000000000000000, 0xFFFFFFFFFFFFFFFF  },
  { 0xC0010136, 0x0000000000000000, 0xFFFFFFFFFFFFFFFF  },
};

VOID                 *mRegistrationForCcxZen5InitWithMpServicesEvent;
VOID                 *mRegistrationForCcxZen5OcCallbackEvent;
VOID                 *mRegistrationForCcxZen5OcOnMpServicesCallbackEvent;
BOOLEAN              mSnpSupported;
EFI_PHYSICAL_ADDRESS mRmpTableBase = 0;
EFI_PHYSICAL_ADDRESS mRmpTableLimit = 0;
DXE_AMD_CCX_INIT_COMPLETE_PROTOCOL    mCcxDxeInitCompleteProtocol;
DXE_AMD_CCX_OC_COMPLETE_PROTOCOL      mCcxOcCompleteProtocol;
STATIC EFI_MP_SERVICES_PROTOCOL      *mMpServices = NULL;
BOOLEAN              mSvmEnable;
BOOLEAN              mSvmLock;
UINT8                mCppcReqMinPerf = 0;

/*----------------------------------------------------------------------------------------
 *           P R O T O T Y P E S     O F     L O C A L     F U N C T I O N S
 *----------------------------------------------------------------------------------------
 */
VOID
EFIAPI
CcxZen5InitWithMpServices (
  IN EFI_EVENT        Event,
  IN VOID             *Context
  );

VOID
EFIAPI
CcxZen5OcCallback (
  IN EFI_EVENT        Event,
  IN VOID             *Context
  );

VOID
EFIAPI
CcxZen5OcCallbackOnMpServices (
  IN EFI_EVENT        Event,
  IN VOID             *Context
  );

VOID
EFIAPI
CcxReadyToBoot (
  IN EFI_EVENT        Event,
  IN VOID             *Context
  );

VOID
EFIAPI
CcxZen5SyncRmpMsrs (
  IN       VOID  *Void
  );

VOID
CcxZen5SetSnpRmp (
  VOID
  );

VOID
CcxZen5InitSnpRmp (
  UINT8   SnpMemCover
  );

VOID
EFIAPI
CcxZen5InitSvm (
  IN       VOID  *Void
  );

VOID
EFIAPI
CcxZen5CppcReq (
  VOID
  );

/* -----------------------------------------------------------------------------*/
/**
 *
 *  AmdCcxZen5DxeInit
 *
 *  @param[in]  ImageHandle     EFI Image Handle for the DXE driver
 *  @param[in]  SystemTable     Pointer to the EFI system table
 *
 *  Description:
 *    Zen5 Driver Entry.  Initialize the core complex.
 *
 *  @retval EFI_STATUS
 *
 */
EFI_STATUS
EFIAPI
AmdCcxZen5DxeInit (
  IN       EFI_HANDLE         ImageHandle,
  IN       EFI_SYSTEM_TABLE   *SystemTable
  )
{
  EFI_STATUS                               Status;
  EFI_STATUS                               CalledStatus;
  DXE_AMD_MP_SERVICES_PREREQ_PROTOCOL      AmdMpServicesPreReqProtocol;
  EFI_EVENT                                ReadyToBootEvent;
  ALLOCATE_HEAP_PARAMS                     AllocParams;
  UINT8                                    SnpMemCover;
  SECURE_ENCRYPTION_EAX                    SecureEncruptionEax;
  DXE_AMD_NBIO_SMU_INIT_COMPLETE_PROTOCOL *NbioSmuInitCompleteProtocol;

  AGESA_TESTPOINT (TpCcxDxeEntry, NULL);

  IDS_HDT_CONSOLE (CPU_TRACE, "  AmdCcxZen5DxeInit Entry\n");

  Status = EFI_SUCCESS;

  SecureEncruptionEax.Value = 0;
  AsmCpuid (CPUID_AMD_SECURE_ENCRYPTION, &(SecureEncruptionEax.Value), NULL, NULL, NULL);
  if (SecureEncruptionEax.Field.SNP != 0) {
    mSnpSupported = TRUE;
  } else {
    mSnpSupported = FALSE;
  }

  IDS_HDT_CONSOLE (CPU_TRACE, "  SNP %aabled\n", mSnpSupported ? "En" : "Dis");

  if (mSnpSupported) {
    CalledStatus = InitSegmentedRmp();
    if (CalledStatus == EFI_UNSUPPORTED) {
      // Initialize SNP/RMP memory
      SnpMemCover = PcdGet8 (PcdAmdSnpMemCover);
      if (SnpMemCover > 0) {
        // Allocate & set RMP table
        CcxZen5InitSnpRmp (SnpMemCover);
        CcxZen5SetSnpRmp ();
      }
    }
  }

  // Publish CCX services protocol
  CcxBaseServicesProtocolInstall (ImageHandle, SystemTable);

  // Install gAmdMpServicesPreReqProtocolGuid protocol
  AmdMpServicesPreReqProtocol.Revision = AMD_MP_SERVICES_PREREQ_PROTOCOL_REVISION;
  CalledStatus = gBS->InstallProtocolInterface (
                        &ImageHandle,
                        &gAmdMpServicesPreReqProtocolGuid,
                        EFI_NATIVE_INTERFACE,
                        &AmdMpServicesPreReqProtocol
                        );
  Status = (CalledStatus > Status) ? CalledStatus : Status;

  //
  // Set up call back after MP services are available.
  //
  CalledStatus = gBS->LocateProtocol (
                        &gEfiMpServiceProtocolGuid,
                        NULL,
                        (VOID **)&mMpServices
                        );
  if (!EFI_ERROR (CalledStatus)) {
    CcxZen5InitWithMpServices (NULL, NULL);
  } else {
    CalledStatus = gBS->CreateEventEx (
                          EVT_NOTIFY_SIGNAL,
                          TPL_NOTIFY,
                          CcxZen5InitWithMpServices,
                          NULL,
                          NULL,
                          &CcxZen5InitWithMpServicesEvent
                          );
    Status = (CalledStatus > Status) ? CalledStatus : Status;

    CalledStatus = gBS->RegisterProtocolNotify (
                          &gEfiMpServiceProtocolGuid,
                          CcxZen5InitWithMpServicesEvent,
                          &(mRegistrationForCcxZen5InitWithMpServicesEvent)
                          );
    Status = (CalledStatus > Status) ? CalledStatus : Status;
  }

  //
  // Set up call back for OverClock.
  //
  CalledStatus = gBS->LocateProtocol (
                        &gAmdNbioSmuInitCompleteProtocolGuid,
                        NULL,
                        (VOID **)&NbioSmuInitCompleteProtocol
                        );
  if (!EFI_ERROR (CalledStatus)) {
    CcxZen5OcCallback (NULL, NULL);
  } else {
    CalledStatus = gBS->CreateEventEx (
                          EVT_NOTIFY_SIGNAL,
                          TPL_NOTIFY,
                          CcxZen5OcCallback,
                          NULL,
                          NULL,
                          &CcxZen5OcCallbackEvent
                          );
    Status = (CalledStatus > Status) ? CalledStatus : Status;

    CalledStatus = gBS->RegisterProtocolNotify (
                          &gAmdNbioSmuInitCompleteProtocolGuid,
                          CcxZen5OcCallbackEvent,
                          &(mRegistrationForCcxZen5OcCallbackEvent)
                          );
    Status = (CalledStatus > Status) ? CalledStatus : Status;
  }

  CalledStatus = gBS->CreateEventEx (
                        EVT_NOTIFY_SIGNAL,
                        TPL_NOTIFY,
                        CcxReadyToBoot,
                        NULL,
                        &gEfiEventReadyToBootGuid,
                        &ReadyToBootEvent
                        );
  Status = (CalledStatus > Status) ? CalledStatus : Status;

  // Publish ACPI CPU SSDT services protocol
  CalledStatus = CcxZen5AcpiCpuSsdtServicesProtocolInstall (ImageHandle, SystemTable);
  Status = (CalledStatus > Status) ? CalledStatus : Status;

  // Publish ACPI Ccx CRAT services protocol
  CalledStatus = CcxZen5CratServicesProtocolInstall (ImageHandle, SystemTable);
  Status = (CalledStatus > Status) ? CalledStatus : Status;

  // Publish ACPI Ccx SRAT services protocol
  CalledStatus = CcxZen5SratServicesProtocolInstall (ImageHandle, SystemTable);
  Status = (CalledStatus > Status) ? CalledStatus : Status;

  // Publish ACPI Ccx PCCT services protocol
  CalledStatus = CcxZen5PcctServicesProtocolInstall (ImageHandle, SystemTable);
  Status = (CalledStatus > Status) ? CalledStatus : Status;

  // Publish ACPI Ccx CPPC services protocol
  CalledStatus = CcxZen5CppcServicesProtocolInstall (ImageHandle, SystemTable);
  Status = (CalledStatus > Status) ? CalledStatus : Status;

  // Publish ACPI Ccx RAS services protocol
  CalledStatus = CcxZen5RasServicesProtocolInstall (ImageHandle, SystemTable);
  Status = (CalledStatus > Status) ? CalledStatus : Status;

  // Publish SMBIOS services protocol
  CalledStatus = CcxZen5SmbiosServicesProtocolInstall (ImageHandle, SystemTable);
  Status = (CalledStatus > Status) ? CalledStatus : Status;

  // Install gAmdCcxDxeInitCompleteProtocolGuid protocol
  mCcxDxeInitCompleteProtocol.Revision = AMD_CCX_PROTOCOL_REVISION;
  AGESA_TESTPOINT (TpCcxDxeBeforeInstallCcxDoneProtocol, NULL);
  CalledStatus = gBS->InstallProtocolInterface (
                        &ImageHandle,
                        &gAmdCcxDxeInitCompleteProtocolGuid,
                        EFI_NATIVE_INTERFACE,
                        &mCcxDxeInitCompleteProtocol
                        );
  Status = (CalledStatus > Status) ? CalledStatus : Status;
  AGESA_TESTPOINT (TpCcxDxeAfterInstallCcxDoneProtocol, NULL);

  // Save PcdAmdCcxCfgPFEHEnable to heap so it could be gotten in SMI handler
  AllocParams.RequestedBufferSize = sizeof (BOOLEAN);
  AllocParams.BufferHandle = AMD_PFEH_HANDLE;
  AllocParams.Persist = HEAP_SYSTEM_MEM;

  if (HeapAllocateBuffer (&AllocParams, NULL) == AGESA_SUCCESS) {
    *((BOOLEAN *) AllocParams.BufferPtr) = PcdGetBool (PcdAmdCcxCfgPFEHEnable);
  }

  AGESA_TESTPOINT (TpCcxIdsAfterApLaunch, NULL);
  IDS_HOOK (IDS_HOOK_CCX_AFTER_AP_LAUNCH, NULL, NULL);

  IDS_HDT_CONSOLE (CPU_TRACE, "  AmdCcxZen5DxeInit End\n");

  AGESA_TESTPOINT (TpCcxDxeExit, NULL);

  return (Status);
}

/* -----------------------------------------------------------------------------*/
/**
 *
 *  CcxZen5InitWithMpServices
 *
 *  @param[in] Event        The event that invoked this routine
 *  @param[in] Context      Unused
 *
 *  Description:
 *    This routine runs necessary routines across all APs
 *
 */
VOID
EFIAPI
CcxZen5InitWithMpServices (
  IN EFI_EVENT        Event,
  IN VOID             *Context
  )
{
  EFI_STATUS                Status;
  UINTN                     i;

  if (!mMpServices) {
    Status = gBS->LocateProtocol (&gEfiMpServiceProtocolGuid, NULL, (VOID **)&mMpServices);
    if (EFI_ERROR(Status)) {
      return;
    }
  }

  AGESA_TESTPOINT (TpCcxDxeMpCallbackEntry, NULL);

  IDS_HDT_CONSOLE (CPU_TRACE, "  CcxZen5InitWithMpServices Entry\n");

  Status = CcxSmmAccess2ProtocolInstall ();
  if (Status != EFI_SUCCESS) {
    IDS_HDT_CONSOLE (CPU_TRACE, "   WARNING! CcxSmmAccess2ProtocolInstall was not installed\n");
  }

  if (mSnpSupported) {
    for (i = 0; i < (sizeof (mRmpMsrSyncTable) / sizeof (mRmpMsrSyncTable[0])); i++) {
      mRmpMsrSyncTable[i].MsrData = AsmReadMsr64 (mRmpMsrSyncTable[i].MsrAddr);
    }
    mMpServices->StartupAllAPs (
        mMpServices,
        CcxZen5SyncRmpMsrs,
        PcdGetBool (PcdAmdStartupAllAPsSingleThread),
        NULL,
        0,
        NULL,
        NULL
    );
  }

  mSvmEnable = PcdGetBool(PcdAmdSvmEnable);
  mSvmLock = PcdGetBool(PcdAmdSvmLock);
  mMpServices->StartupAllAPs (
      mMpServices,
      CcxZen5InitSvm,
      PcdGetBool (PcdAmdStartupAllAPsSingleThread),
      NULL,
      0,
      NULL,
      NULL
  );
  CcxZen5InitSvm (NULL);

  CcxZen5CppcReq ();

  IDS_HOOK (IDS_HOOK_CCX_AFTER_PWR_MNG, NULL, NULL);

  IDS_HDT_CONSOLE (CPU_TRACE, "  CcxZen5InitWithMpServices Exit\n");

  AGESA_TESTPOINT (TpCcxDxeMpCallbackExit, NULL);

  gBS->CloseEvent (Event);
}

/* -----------------------------------------------------------------------------*/
/**
 *
 *  CcxZen5OcCallback
 *
 *  @param[in] Event        The event that invoked this routine
 *  @param[in] Context      Unused
 *
 *  Description:
 *    This routine runs necessary routines for OC callback
 *
 */
VOID
EFIAPI
CcxZen5OcCallback (
  IN EFI_EVENT        Event,
  IN VOID             *Context
  )
{
  EFI_STATUS                 Status;

  if ((!mMpServices) &&
      (EFI_ERROR ( gBS->LocateProtocol (
                    &gEfiMpServiceProtocolGuid,
                    NULL,
                    (VOID **)&mMpServices))))
  {
    Status = gBS->CreateEventEx (
                    EVT_NOTIFY_SIGNAL,
                    TPL_NOTIFY,
                    CcxZen5OcCallbackOnMpServices,
                    NULL,
                    NULL,
                    &CcxZen5OcCallbackOnMpServicesEvent
                    );
    ASSERT (!EFI_ERROR (Status));
    Status = gBS->RegisterProtocolNotify (
                    &gEfiMpServiceProtocolGuid,
                    CcxZen5OcCallbackOnMpServicesEvent,
                    &(mRegistrationForCcxZen5OcOnMpServicesCallbackEvent)
                    );
    ASSERT (!EFI_ERROR (Status));
  } else {
    CcxZen5OcCallbackOnMpServices (NULL, NULL);
  }

  gBS->CloseEvent (Event);
}

/* -----------------------------------------------------------------------------*/
/**
 *
 *  CcxZen5OcCallbackOnMpServices
 *
 *  @param[in] Event        The event that invoked this routine
 *  @param[in] Context      Unused
 *
 *  Description:
 *    This routine runs necessary routines for OC callback
 *
 */
VOID
EFIAPI
CcxZen5OcCallbackOnMpServices (
  IN EFI_EVENT        Event,
  IN VOID             *Context
  )
{
  EFI_HANDLE Handle;

  AGESA_TESTPOINT (TpCcxDxeOcCallbackEntry, NULL);
  IDS_HDT_CONSOLE (CPU_TRACE, "  CcxZen5OcCallback Entry\n");

  IDS_HOOK (IDS_HOOK_CCX_CUSTOM_PSTATES, NULL, NULL);

  // Install gAmdCcxOcCompleteProtocolGuid protocol after custom Pstate feature
  mCcxOcCompleteProtocol.Revision = 0;
  Handle = NULL;
  gBS->InstallProtocolInterface (
         &Handle,
         &gAmdCcxOcCompleteProtocolGuid,
         EFI_NATIVE_INTERFACE,
         &mCcxOcCompleteProtocol
         );

  IDS_HDT_CONSOLE (CPU_TRACE, "  CcxZen5OcCallback Exit\n");
  AGESA_TESTPOINT (TpCcxDxeOcCallbackExit, NULL);

  gBS->CloseEvent (Event);
}

/*---------------------------------------------------------------------------------------*/
/**
 * CcxReadyToBoot
 *
 * Calls CcxReadyToBoot
 *
 *  Parameters:
 *    @param[in]     Event
 *    @param[in]     *Context
 *
 *    @retval        VOID
 */
VOID
EFIAPI
CcxReadyToBoot (
  IN EFI_EVENT        Event,
  IN VOID             *Context
  )
{
  EFI_STATUS                   Status;
  EFI_SMM_CONTROL2_PROTOCOL    *SmmControl;
  UINT8                        SmiDataValue;

  IDS_HDT_CONSOLE (CPU_TRACE, "  CcxReadyToBoot");
  AGESA_TESTPOINT (TpCcxDxeRtbCallBackEntry, NULL);

  Status = gBS->LocateProtocol (
                  &gEfiSmmControl2ProtocolGuid,
                  NULL,
                  (VOID **)&SmmControl
                  );
  if (EFI_ERROR (Status)) {
    return;
  }

  SmiDataValue = PcdGet8 (PcdAmdCcxS3SaveSmi);

  SmmControl->Trigger (
                SmmControl,
                &SmiDataValue,
                NULL,
                0,
                0
                );

  gBS->CloseEvent (Event);

  IDS_HOOK (IDS_HOOK_CCX_READY_TO_BOOT, NULL, NULL);

  AGESA_TESTPOINT (TpCcxDxeRtbCallBackExit, NULL);
}

/* -----------------------------------------------------------------------------*/
/**
 *
 *  CcxZen5SyncRmpMsrs
 *
 *  Description:
 *    This routine synchronizes the MSRs in mRmpMsrSyncTable across all APs
 *
 */
VOID
EFIAPI
CcxZen5SyncRmpMsrs (
  IN       VOID  *Void
  )
{
  UINTN  i;

  for (i = 0; i < (sizeof (mRmpMsrSyncTable) / sizeof (mRmpMsrSyncTable[0])); i++) {
    AsmMsrAndThenOr64 (
        mRmpMsrSyncTable[i].MsrAddr,
        ~(mRmpMsrSyncTable[i].MsrMask),
        (mRmpMsrSyncTable[i].MsrData & mRmpMsrSyncTable[i].MsrMask)
        );
  }
}

/* -----------------------------------------------------------------------------*/
/**
 *
 *  CcxZen5SetSnpRmp
 *
 *  Description:
 *    This routine sets Reverse Map Table (RMP) base and limit
 *
 */
VOID
CcxZen5SetSnpRmp (
  VOID
  )
{
  UINT64    MsrData;

  MsrData = AsmReadMsr64 (0xC0010132) & 0xFFF0000000001FFFull;
  MsrData |= mRmpTableBase;
  AsmWriteMsr64 (0xC0010132, MsrData);

  MsrData = AsmReadMsr64 (0xC0010133) & 0xFFF0000000001FFFull;
  MsrData |= mRmpTableLimit;
  AsmWriteMsr64 (0xC0010133, MsrData);
}

/* -----------------------------------------------------------------------------*/
/**
 *
 *  CcxZen5InitSnpRmp
 *
 *  Description:
 *    This routine allocates the Secure Nested Paging (SNP)
 *    Reverse Map Table (RMP)
 *
 */
VOID
CcxZen5InitSnpRmp (
  UINT8   SnpMemCover
  )
{
  EFI_STATUS                             Status;
  UINT8                                  BytesPer4kPage;
  UINT64                                 SnpMemSizeToCover;
  UINT64                                 SnpMemSizeToCoverMax;
  UINT64                                 RmpTableSize;
  EFI_PHYSICAL_ADDRESS                   RmpTableBase;
  EFI_PHYSICAL_ADDRESS                   RmpTableBaseAcrossSocket;
  SECURE_ENCRYPTION_EAX                  SecureEncruptionEax;
  UINTN                                  SocketCount;

  AMD_FABRIC_TOPOLOGY_SERVICES2_PROTOCOL *FabricTopologyServices;

  IDS_HDT_CONSOLE (CPU_TRACE, "  CcxZen5InitSnpRmp Entry\n");

  // init variables
  Status = EFI_SUCCESS;
  BytesPer4kPage = 8;
  SnpMemSizeToCover = 0;
  SnpMemSizeToCoverMax = 0;
  RmpTableSize = 0;
  RmpTableBase = 0;

  Status = gBS->LocateProtocol (&gAmdFabricTopologyServices2ProtocolGuid, NULL, (VOID **)&FabricTopologyServices);
  if (EFI_ERROR(Status) || EFI_ERROR (FabricTopologyServices->GetSystemInfo (FabricTopologyServices, &SocketCount, NULL, NULL, NULL, NULL))) {
    SocketCount = 1;
  }

  // if VMPL enabled
  if (PcdGetBool (PcdAmdVmplEnable)) {
    SecureEncruptionEax.Value = 0;
    AsmCpuid (CPUID_AMD_SECURE_ENCRYPTION, &(SecureEncruptionEax.Value), NULL, NULL, NULL);
    if (SecureEncruptionEax.Field.VMPL != 0) {
      IDS_HDT_CONSOLE (CPU_TRACE, "  VMPL Enabled\n");
      AsmMsrOr64 (MSR_SYS_CFG, BIT25);
      BytesPer4kPage = 16;
    }
  }


  // use TOM2 for total memory to cover
  SnpMemSizeToCover = AsmReadMsr64 (MSR_TOM2);

  // if custom memory size, use specified size
  if (SnpMemCover == 2) {
    SnpMemSizeToCoverMax = SnpMemSizeToCover;
    // PcdAmdSnpMemSize is in MB, so convert to bytes
    SnpMemSizeToCover = ((UINT64) PcdGet32 (PcdAmdSnpMemSize)) << 20;
    if (SnpMemSizeToCover < SIZE_16MB) {
      SnpMemSizeToCover = SIZE_16MB;
    } else if (SnpMemSizeToCover > SnpMemSizeToCoverMax) {
      SnpMemSizeToCover = SnpMemSizeToCoverMax;
    }
  }

  IDS_HDT_CONSOLE (CPU_TRACE, "CcxZen5InitSnpRmp SnpMemSizeToCover=0x%lx,\n"\
                              "      SnpMemSizeToCoverMax=0x%lx\n", SnpMemSizeToCover, SnpMemSizeToCoverMax);
  Status = CcxGetAbove4GMMIOSnpMemSize(&SnpMemSizeToCoverMax);
  if (!EFI_ERROR(Status)) {
    SnpMemSizeToCover = SnpMemSizeToCoverMax;
    IDS_HDT_CONSOLE (CPU_TRACE, "CcxZen5InitSnpRmp Including Above 4GB MMIO SnpMemSizeToCover=0x%lx", SnpMemSizeToCover);
  }

  // 16KB + ((Bytes/4K-page) * (# of pages [SnpMemSizeToCover/4K]))
  RmpTableSize = SIZE_16KB + ((UINT64) BytesPer4kPage * (SnpMemSizeToCover >> 12));

  // round RMP table size up to nearest 1MB
  RmpTableSize = (RmpTableSize + SIZE_1MB - 1) & ~(SIZE_1MB - 1);

  // find enough memory for RMP table to fit on MB boundary
  RmpTableBase = SnpMemSizeToCover;

  // For case that system is with multiple DRAM range in different sockets and the PcdAmdSplitRmpTable is enabled,
  // the RMP should be split evenly across the DRAM regions of the sockets.
  if ((SocketCount > 1) && (PcdGet8 (PcdAmdSplitRmpTable) > 0)) {
    if (CcxGetRmpTableBaseAcrossSocket (RmpTableSize, &RmpTableBaseAcrossSocket)) {
      if (RmpTableBaseAcrossSocket < SnpMemSizeToCover) {
        RmpTableBase = RmpTableBaseAcrossSocket;
      }
    }
  }

  Status = gBS->AllocatePages (
                 AllocateMaxAddress,
                 EfiReservedMemoryType,
                 EFI_SIZE_TO_PAGES (RmpTableSize + SIZE_1MB),
                 &RmpTableBase
                 );
  ASSERT (!EFI_ERROR (Status));
  if (EFI_ERROR (Status)) {
    IDS_HDT_CONSOLE (CPU_TRACE, "  [ERROR] Failed to allocate RMP Table.\n");
    return;
  }

  // align on MB boundary
  mRmpTableBase = (RmpTableBase + SIZE_1MB - 1) & ~(SIZE_1MB - 1);

  // free pages before reallocating on MB boundary
  gBS->FreePages (RmpTableBase, EFI_SIZE_TO_PAGES (RmpTableSize + SIZE_1MB));

  // reserve memory for RMP Table on MB boundary
  Status = gBS->AllocatePages (AllocateAddress,
                               EfiReservedMemoryType,
                               EFI_SIZE_TO_PAGES (RmpTableSize),
                               &mRmpTableBase
                               );
  ASSERT (!EFI_ERROR (Status));
  if (EFI_ERROR (Status)) {
    IDS_HDT_CONSOLE (CPU_TRACE, "  [ERROR] Try to allocate RMP Table at 0x%x but failed.\n", mRmpTableBase);
    return;
  }

  IDS_HDT_CONSOLE (CPU_TRACE, "CcxZen5InitSnpRmp Finally Allocated RMP Table\n" \
                              "   SnpMemSizeToCover=0x%lx,  RmpTableBase=0x%lx, RmpTableSize=0x%lx\n",
                              SnpMemSizeToCover, mRmpTableBase, RmpTableSize);

  // zero out RMP table
  gBS->SetMem ((VOID *) mRmpTableBase,
               RmpTableSize,
               0
               );

  // save RMP limit
  mRmpTableLimit = mRmpTableBase + RmpTableSize - 1;

  IDS_HDT_CONSOLE (CPU_TRACE, "  CcxZen5InitSnpRmp Exit\n");
}

VOID
EFIAPI
CcxZen5InitSvm (
  IN       VOID  *Void
  )
{
  // Set VM_CR[SvmeDisable] & SvmLockKey register before set Svm Lock
  if (mSvmLock) {
    AsmWriteMsr64 (MSR_SVM_LOCK_KEY, 0);
  }

  if (mSvmEnable) {
    AsmMsrAnd64 (MSR_VM_CR, ~((UINT64) BIT4));
  } else {
    AsmMsrOr64 (MSR_VM_CR, (UINT64) BIT4);
  }

  if (mSvmLock) {
    AsmMsrOr64 (MSR_VM_CR, (UINT64) BIT3);
  } else {
    AsmMsrAnd64 (MSR_VM_CR, ~((UINT64) BIT3));
  }
}

VOID
EFIAPI
CcxZen5SetCppcReqMsr (
  IN       VOID  *Void
  )
{
  CPPC_REQUEST  CppcReq;

  CppcReq.Value = AsmReadMsr64 (CPPC_REQ_MSR);
  CppcReq.Field.MinPerf = mCppcReqMinPerf;
  AsmWriteMsr64 (CPPC_REQ_MSR, CppcReq.Value);
}

VOID
EFIAPI
CcxZen5CppcReq (
  VOID
  )
{
  AMD_SOC_ZEN5_SERVICES_PROTOCOL *SocZen5Services;
  UINT16                          CpuReqMinFreq;
  UINT32                          Fmax;
  EFI_STATUS                      Status;

  IDS_HDT_CONSOLE (CPU_TRACE, "  %a Entry\n", __func__);

  CpuReqMinFreq     = PcdGet16 (PcdAmdCpuReqMinFreq);
  SocZen5Services   = NULL;
  Fmax              = CPPC_MAX_MHZ;

  // MSRC001_02B3[15:8] : CPPC Request[MinPerf]
  if (PcdGetBool (PcdAmdCpuReqMinFreqEn) && (CpuReqMinFreq > 0)) {
    Status = gBS->LocateProtocol (
                    &gAmdSocZen5ServicesProtocolGuid,
                    NULL,
                    (VOID **)&SocZen5Services
                    );
    ASSERT_EFI_ERROR (Status);

    if (!EFI_ERROR (Status)) {
      Status = SocZen5Services->GetOpnFmax (SocZen5Services, &Fmax);
      ASSERT_EFI_ERROR (Status);

      Fmax = EFI_ERROR (Status) ? CPPC_MAX_MHZ : Fmax;
    }
    ASSERT (Fmax > 0);

    if (Fmax > 0) {
      if (CpuReqMinFreq > Fmax) {
        IDS_HDT_CONSOLE (CPU_TRACE, "  Requested CPU min frequency %d exceeds frequency max, limit to %d\n", CpuReqMinFreq, Fmax);
        CpuReqMinFreq = (UINT16) Fmax;
      }

      if (CpuReqMinFreq < CPPC_MIN_MHZ) {
        IDS_HDT_CONSOLE (CPU_TRACE, "  Requested CPU min frequency %d is below allowed frequency min, limit to %d\n", CpuReqMinFreq, CPPC_MIN_MHZ);
        CpuReqMinFreq = CPPC_MIN_MHZ;
      }

      // calc & set CPPC request
      mCppcReqMinPerf = (UINT8) (CpuReqMinFreq * CPPC_SCALAR / Fmax);

      ASSERT (mCppcReqMinPerf <= 0xFF);
      if (mCppcReqMinPerf <= 0xFF) {
        if (mCppcReqMinPerf < 0xFF) {
          // round CPPC scale value up if closer to requested freq
          UINT16  F1 = (UINT16) (mCppcReqMinPerf * (UINT16) Fmax / CPPC_SCALAR);
          UINT16  F2 = (UINT16) ((mCppcReqMinPerf + 1) * (UINT16) Fmax / CPPC_SCALAR);
          UINT8   Delta1 = (UINT8) ((CpuReqMinFreq > F1) ? (CpuReqMinFreq - F1) : (F1 - CpuReqMinFreq));
          UINT8   Delta2 = (UINT8) ((CpuReqMinFreq > F2) ? (CpuReqMinFreq - F2) : (F2 - CpuReqMinFreq));
          if (Delta1 > Delta2) {mCppcReqMinPerf++;}
        }

        CcxZen5SetCppcReqMsr (NULL);

        ASSERT (mMpServices);
        if (mMpServices) {
          mMpServices->StartupAllAPs (
              mMpServices,
              CcxZen5SetCppcReqMsr,
              PcdGetBool (PcdAmdStartupAllAPsSingleThread),
              NULL,
              0,
              NULL,
              NULL
          );
        }
      }
    }
  }
  IDS_HDT_CONSOLE (CPU_TRACE, "  %a Exit\n", __func__);
}
