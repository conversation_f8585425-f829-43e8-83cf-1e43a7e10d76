/*****************************************************************************
 * Copyright (C) 2008-2024 Advanced Micro Devices, Inc. All rights reserved.
 *
*****************************************************************************
*/
/* $NoKeywords:$ */
/**
 * @file
 *
 * MPIO services
 *
 *
 *
 * @xrefitem bom "File Content Label" "Release Content"
 * @e project:     AGESA
 * @e sub-project: GNB
 * @e \$Revision: 313706 $   @e \$Date: 2020-12-11 16:53:43 -0600 (Fri, 11 Dec 2020) $
 *
 */
#ifndef _NBIOMPIOLIB_H_
#define _NBIOMPIOLIB_H_

#include <NbioRegisterTypes.h>


#define INVALID_MPIO_MESSAGE                  0xFF

// MPIO Response Codes:
#define BIOSMPIO_Result_OK                    0x1
#define BIOSMPIO_Result_Failed                0xFF
#define BIOSMPIO_Result_UnknownCmd            0xFE
#define BIOSMPIO_Result_CmdRejectedPrereq     0xFD
#define BIOSMPIO_Result_CmdRejectedBusy       0xFC


/// MPIO basic lib

VOID
EFIAPI
NbioMpioServiceCommonInitArguments (
  IN OUT   UINT32                   *MpioArg
  );

UINT32
EFIAPI
MpioServiceRequest (
  IN       PCI_ADDR                 NbioPciAddress,
  IN       UINT32                   RequestId,
  IN       UINT32                   *RequestArgument,
  IN       UINT32                   AccessFlags
  );

VOID
EFIAPI
SmnPrivateRegRead (
  IN       GNB_HANDLE               *GnbHandle,
  IN       UINT32                   RegisterIndex,
  IN       UINT32                   *RegisterValue
);

VOID
EFIAPI
SmnPrivateRegWrite (
  IN       GNB_HANDLE               *GnbHandle,
  IN       UINT32                   RegisterIndex,
  IN       UINT32                   *RegisterValue,
  IN       UINT32                   Flags
);

VOID
EFIAPI
SmnPrivateRegRMW (
  IN       GNB_HANDLE               *GnbHandle,
  IN       UINT32                   RegisterIndex,
  IN       UINT32                   AndMask,
  IN       UINT32                   OrValue,
  IN       UINT32                   Flags
);

#endif



