#;*****************************************************************************
#;
#; Copyright (C) 2008-2024 Advanced Micro Devices, Inc. All rights reserved.
#;
#;******************************************************************************
#For EDKII use Only
[Defines]
  INF_VERSION                    = 0x00010006
  BASE_NAME                      = CcxResetTablesZen5Lib
  FILE_GUID                      = EA679CBD-BFB4-460F-93D2-E6760B58F960
  MODULE_TYPE                    = BASE
  VERSION_STRING                 = 1.0
  LIBRARY_CLASS                  = CcxResetTablesLib

[Sources.common]
  CcxResetTablesZen5Lib.c

[Packages]
  MdePkg/MdePkg.dec
  AgesaModulePkg/AgesaCommonModulePkg.dec
  AgesaModulePkg/AgesaModuleCcxPkg.dec

[LibraryClasses]
  AmdBaseLib
  IdsLib
  CcxRolesLib
  AmdTableLib

[Guids]

[Protocols]

[Ppis]

[Pcd]

[Depex]



