#;*****************************************************************************
#;
#; Copyright (C) 2008-2024 Advanced Micro Devices, Inc. All rights reserved.
#;
#;******************************************************************************

[defines]
  INF_VERSION                    = 0x00010005
  BASE_NAME                      = AmdSmmControl
  FILE_GUID                      = d53e9f23-cf7f-4270-adc4-05e2920471ef
  MODULE_TYPE                    = DXE_RUNTIME_DRIVER
  VERSION_STRING                 = 1.0
  ENTRY_POINT                    = AmdInstallSmmControl

[sources.common]
  SmmControl.c
  SmmControl.h

[sources.ia32]

[sources.x64]

[LibraryClasses.common.DXE_RUNTIME_DRIVER]
  BaseLib
  UefiLib
  PrintLib
  IoLib
  AmdPspRegMuxLibV2

[LibraryClasses]
  FchDxeLibV9

  UefiDriverEntryPoint
  UefiBootServicesTableLib
  DebugLib
  DxeServicesTableLib
  UefiRuntimeLib

[Guids]
  gEfiEventVirtualAddressChangeGuid     #CONSUMES #EVENT

[Protocols]
  gEfiSmmControl2ProtocolGuid           #PRODUCED

[Packages]
  MdePkg/MdePkg.dec
  AgesaPkg/AgesaPkg.dec
  AgesaModulePkg/AgesaModuleFchPkg.dec
  AgesaModulePkg/Fch/Kunlun/FchKunlun.dec

[Depex]
  gEfiPciRootBridgeIoProtocolGuid
  AND
  gEfiCpuIo2ProtocolGuid
  AND
  gEfiCpuArchProtocolGuid
  AND
  gAmdFchKLSmmControlDepexProtocolGuid



