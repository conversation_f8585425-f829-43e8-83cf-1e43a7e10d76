#;*****************************************************************************
#;
#; Copyright (C) 2008-2024 Advanced Micro Devices, Inc. All rights reserved.
#;
#;******************************************************************************
[Defines]
  INF_VERSION                    = 0x00010006
  BASE_NAME                      = AmdPostCodeEmuLib
  FILE_GUID                      = a479f9d3-a724-48a5-bbb8-e00fde57a314
  MODULE_TYPE                    = BASE
  VERSION_STRING                 = 1.0
  LIBRARY_CLASS                  = AmdPostCodeLib

[Sources.common]
  AmdPostCodeEmuLib.c

[Packages]
  MdePkg/MdePkg.dec
  AgesaModulePkg/AgesaCommonModulePkg.dec
  AgesaPkg/AgesaPkg.dec

[LibraryClasses]
  BaseLib
  IoLib
  PcdLib
  PciLib


[Guids]

[Protocols]

[Ppis]


[Pcd]
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdIdsDebugPort


[Depex]
  TRUE

[BuildOptions]



