/*****************************************************************************
 *
 * Copyright (C) 2008-2024 Advanced Micro Devices, Inc. All rights reserved.
 *
 *******************************************************************************
 **/

#ifndef _PCIEPORT_H_
#define _PCIEPORT_H_


/***********************************************************
* Register Name : PCIE_ERR_CNTL
************************************************************/

#define PCIE_ERR_CNTL_ERR_REPORTING_DIS_OFFSET                 0
#define PCIE_ERR_CNTL_ERR_REPORTING_DIS_MASK                   0x1

#define PCIE_ERR_CNTL_STRAP_FIRST_RCVD_ERR_LOG_OFFSET          1
#define PCIE_ERR_CNTL_STRAP_FIRST_RCVD_ERR_LOG_MASK            0x2

#define PCIE_ERR_CNTL_RX_DROP_ECRC_FAILURES_OFFSET             2
#define PCIE_ERR_CNTL_RX_DROP_ECRC_FAILURES_MASK               0x4

#define PCIE_ERR_CNTL_Reserved_4_3_OFFSET                      3
#define PCIE_ERR_CNTL_Reserved_4_3_MASK                        0x18

#define PCIE_ERR_CNTL_RX_GENERATE_LCRC_ERR_OFFSET              5
#define PCIE_ERR_CNTL_RX_GENERATE_LCRC_ERR_MASK                0x20

#define PCIE_ERR_CNTL_RX_GENERATE_POIS_TLP_OFFSET              6
#define PCIE_ERR_CNTL_RX_GENERATE_POIS_TLP_MASK                0x40

#define PCIE_ERR_CNTL_RX_GENERATE_ECRC_ERR_OFFSET              7
#define PCIE_ERR_CNTL_RX_GENERATE_ECRC_ERR_MASK                0x80

#define PCIE_ERR_CNTL_AER_HDR_LOG_TIMEOUT_OFFSET               8
#define PCIE_ERR_CNTL_AER_HDR_LOG_TIMEOUT_MASK                 0x700

#define PCIE_ERR_CNTL_AER_HDR_LOG_F0_TIMER_EXPIRED_OFFSET      11
#define PCIE_ERR_CNTL_AER_HDR_LOG_F0_TIMER_EXPIRED_MASK        0x800

#define PCIE_ERR_CNTL_AER_PRIV_MASK_RCV_ERR_OFFSET             12
#define PCIE_ERR_CNTL_AER_PRIV_MASK_RCV_ERR_MASK               0x1000

#define PCIE_ERR_CNTL_AER_PRIV_MASK_REPLAY_NUM_ROLLOVER_OFFSET 13
#define PCIE_ERR_CNTL_AER_PRIV_MASK_REPLAY_NUM_ROLLOVER_MASK   0x2000

#define PCIE_ERR_CNTL_CI_P_SLV_BUF_RD_HALT_STATUS_OFFSET       14
#define PCIE_ERR_CNTL_CI_P_SLV_BUF_RD_HALT_STATUS_MASK         0x4000

#define PCIE_ERR_CNTL_CI_NP_SLV_BUF_RD_HALT_STATUS_OFFSET      15
#define PCIE_ERR_CNTL_CI_NP_SLV_BUF_RD_HALT_STATUS_MASK        0x8000

#define PCIE_ERR_CNTL_CI_SLV_BUF_HALT_RESET_OFFSET             16
#define PCIE_ERR_CNTL_CI_SLV_BUF_HALT_RESET_MASK               0x10000

#define PCIE_ERR_CNTL_SEND_ERR_MSG_IMMEDIATELY_OFFSET          17
#define PCIE_ERR_CNTL_SEND_ERR_MSG_IMMEDIATELY_MASK            0x20000

#define PCIE_ERR_CNTL_STRAP_POISONED_ADVISORY_NONFATAL_OFFSET  18
#define PCIE_ERR_CNTL_STRAP_POISONED_ADVISORY_NONFATAL_MASK    0x40000

#define PCIE_ERR_CNTL_AER_PRIV_MASK_BAD_DLLP_OFFSET            19
#define PCIE_ERR_CNTL_AER_PRIV_MASK_BAD_DLLP_MASK              0x80000

#define PCIE_ERR_CNTL_AER_PRIV_MASK_BAD_TLP_OFFSET             20
#define PCIE_ERR_CNTL_AER_PRIV_MASK_BAD_TLP_MASK               0x100000

#define PCIE_ERR_CNTL_AER_PRIV_MASK_INTERNAL_ERR_OFFSET        21
#define PCIE_ERR_CNTL_AER_PRIV_MASK_INTERNAL_ERR_MASK          0x200000

#define PCIE_ERR_CNTL_AER_PRIV_MASK_REPLAY_TIMER_TIMEOUT_OFFSET 22
#define PCIE_ERR_CNTL_AER_PRIV_MASK_REPLAY_TIMER_TIMEOUT_MASK  0x400000

#define PCIE_ERR_CNTL_AER_PRIV_MASK_CORR_INT_ERR_OFFSET        23
#define PCIE_ERR_CNTL_AER_PRIV_MASK_CORR_INT_ERR_MASK          0x800000

#define PCIE_ERR_CNTL_PRIV_SURP_DIS_VEC_OFFSET                 24
#define PCIE_ERR_CNTL_PRIV_SURP_DIS_VEC_MASK                   0xff000000

typedef union {
  struct {
    UINT32                                   ERR_REPORTING_DIS:1;
    UINT32                            STRAP_FIRST_RCVD_ERR_LOG:1;
    UINT32                               RX_DROP_ECRC_FAILURES:1;
    UINT32                                        Reserved_4_3:2;
    UINT32                                RX_GENERATE_LCRC_ERR:1;
    UINT32                                RX_GENERATE_POIS_TLP:1;
    UINT32                                RX_GENERATE_ECRC_ERR:1;
    UINT32                                 AER_HDR_LOG_TIMEOUT:3;
    UINT32                        AER_HDR_LOG_F0_TIMER_EXPIRED:1;
    UINT32                               AER_PRIV_MASK_RCV_ERR:1;
    UINT32                   AER_PRIV_MASK_REPLAY_NUM_ROLLOVER:1;
    UINT32                         CI_P_SLV_BUF_RD_HALT_STATUS:1;
    UINT32                        CI_NP_SLV_BUF_RD_HALT_STATUS:1;
    UINT32                               CI_SLV_BUF_HALT_RESET:1;
    UINT32                            SEND_ERR_MSG_IMMEDIATELY:1;
    UINT32                    STRAP_POISONED_ADVISORY_NONFATAL:1;
    UINT32                              AER_PRIV_MASK_BAD_DLLP:1;
    UINT32                               AER_PRIV_MASK_BAD_TLP:1;
    UINT32                          AER_PRIV_MASK_INTERNAL_ERR:1;
    UINT32                  AER_PRIV_MASK_REPLAY_TIMER_TIMEOUT:1;
    UINT32                          AER_PRIV_MASK_CORR_INT_ERR:1;
    UINT32                                   PRIV_SURP_DIS_VEC:8;
  } Field;
  UINT32 Value;
} PCIE_ERR_CNTL_PCIEPORT_STRUCT;

#define PCICFG_PCIEPORT_PCIE_ERR_CNTL_OFFSET                        0x1a8
#define SMN_FUNC0_PCIE0NBIO0_PCIE_ERR_CNTL_ADDRESS                    0x1a3401a8UL
#define SMN_FUNC0_PCIE0NBIO1_PCIE_ERR_CNTL_ADDRESS                    0x1a5401a8UL
#define SMN_FUNC0_PCIE1NBIO0_PCIE_ERR_CNTL_ADDRESS                    0x1a4401a8UL
#define SMN_FUNC0_PCIE1NBIO1_PCIE_ERR_CNTL_ADDRESS                    0x1a6401a8UL
#define SMN_FUNC0_PCIE2NBIO0_PCIE_ERR_CNTL_ADDRESS                    0x1a7401a8UL
#define SMN_FUNC0_PCIE2NBIO1_PCIE_ERR_CNTL_ADDRESS                    0x1a9401a8UL
#define SMN_FUNC0_PCIE3NBIO0_PCIE_ERR_CNTL_ADDRESS                    0x1a8401a8UL
#define SMN_FUNC0_PCIE3NBIO1_PCIE_ERR_CNTL_ADDRESS                    0x1aa401a8UL
#define SMN_FUNC0_PCIE5NBIO0_PCIE_ERR_CNTL_ADDRESS                    0x1ab401a8UL
#define SMN_FUNC0_PCIE5NBIO1_PCIE_ERR_CNTL_ADDRESS                    0x1ad401a8UL
#define SMN_FUNC1_PCIE0NBIO0_PCIE_ERR_CNTL_ADDRESS                    0x1a3411a8UL
#define SMN_FUNC1_PCIE0NBIO1_PCIE_ERR_CNTL_ADDRESS                    0x1a5411a8UL
#define SMN_FUNC1_PCIE1NBIO0_PCIE_ERR_CNTL_ADDRESS                    0x1a4411a8UL
#define SMN_FUNC1_PCIE1NBIO1_PCIE_ERR_CNTL_ADDRESS                    0x1a6411a8UL
#define SMN_FUNC1_PCIE2NBIO0_PCIE_ERR_CNTL_ADDRESS                    0x1a7411a8UL
#define SMN_FUNC1_PCIE2NBIO1_PCIE_ERR_CNTL_ADDRESS                    0x1a9411a8UL
#define SMN_FUNC1_PCIE3NBIO0_PCIE_ERR_CNTL_ADDRESS                    0x1a8411a8UL
#define SMN_FUNC1_PCIE3NBIO1_PCIE_ERR_CNTL_ADDRESS                    0x1aa411a8UL
#define SMN_FUNC1_PCIE5NBIO0_PCIE_ERR_CNTL_ADDRESS                    0x1ab411a8UL
#define SMN_FUNC1_PCIE5NBIO1_PCIE_ERR_CNTL_ADDRESS                    0x1ad411a8UL
#define SMN_FUNC2_PCIE0NBIO0_PCIE_ERR_CNTL_ADDRESS                    0x1a3421a8UL
#define SMN_FUNC2_PCIE0NBIO1_PCIE_ERR_CNTL_ADDRESS                    0x1a5421a8UL
#define SMN_FUNC2_PCIE1NBIO0_PCIE_ERR_CNTL_ADDRESS                    0x1a4421a8UL
#define SMN_FUNC2_PCIE1NBIO1_PCIE_ERR_CNTL_ADDRESS                    0x1a6421a8UL
#define SMN_FUNC2_PCIE2NBIO0_PCIE_ERR_CNTL_ADDRESS                    0x1a7421a8UL
#define SMN_FUNC2_PCIE2NBIO1_PCIE_ERR_CNTL_ADDRESS                    0x1a9421a8UL
#define SMN_FUNC2_PCIE3NBIO0_PCIE_ERR_CNTL_ADDRESS                    0x1a8421a8UL
#define SMN_FUNC2_PCIE3NBIO1_PCIE_ERR_CNTL_ADDRESS                    0x1aa421a8UL
#define SMN_FUNC2_PCIE5NBIO0_PCIE_ERR_CNTL_ADDRESS                    0x1ab421a8UL
#define SMN_FUNC2_PCIE5NBIO1_PCIE_ERR_CNTL_ADDRESS                    0x1ad421a8UL
#define SMN_FUNC3_PCIE0NBIO0_PCIE_ERR_CNTL_ADDRESS                    0x1a3431a8UL
#define SMN_FUNC3_PCIE0NBIO1_PCIE_ERR_CNTL_ADDRESS                    0x1a5431a8UL
#define SMN_FUNC3_PCIE1NBIO0_PCIE_ERR_CNTL_ADDRESS                    0x1a4431a8UL
#define SMN_FUNC3_PCIE1NBIO1_PCIE_ERR_CNTL_ADDRESS                    0x1a6431a8UL
#define SMN_FUNC3_PCIE2NBIO0_PCIE_ERR_CNTL_ADDRESS                    0x1a7431a8UL
#define SMN_FUNC3_PCIE2NBIO1_PCIE_ERR_CNTL_ADDRESS                    0x1a9431a8UL
#define SMN_FUNC3_PCIE3NBIO0_PCIE_ERR_CNTL_ADDRESS                    0x1a8431a8UL
#define SMN_FUNC3_PCIE3NBIO1_PCIE_ERR_CNTL_ADDRESS                    0x1aa431a8UL
#define SMN_FUNC3_PCIE5NBIO0_PCIE_ERR_CNTL_ADDRESS                    0x1ab431a8UL
#define SMN_FUNC3_PCIE5NBIO1_PCIE_ERR_CNTL_ADDRESS                    0x1ad431a8UL
#define SMN_FUNC4_PCIE0NBIO0_PCIE_ERR_CNTL_ADDRESS                    0x1a3441a8UL
#define SMN_FUNC4_PCIE0NBIO1_PCIE_ERR_CNTL_ADDRESS                    0x1a5441a8UL
#define SMN_FUNC4_PCIE1NBIO0_PCIE_ERR_CNTL_ADDRESS                    0x1a4441a8UL
#define SMN_FUNC4_PCIE1NBIO1_PCIE_ERR_CNTL_ADDRESS                    0x1a6441a8UL
#define SMN_FUNC4_PCIE2NBIO0_PCIE_ERR_CNTL_ADDRESS                    0x1a7441a8UL
#define SMN_FUNC4_PCIE2NBIO1_PCIE_ERR_CNTL_ADDRESS                    0x1a9441a8UL
#define SMN_FUNC4_PCIE3NBIO0_PCIE_ERR_CNTL_ADDRESS                    0x1a8441a8UL
#define SMN_FUNC4_PCIE3NBIO1_PCIE_ERR_CNTL_ADDRESS                    0x1aa441a8UL
#define SMN_FUNC4_PCIE5NBIO0_PCIE_ERR_CNTL_ADDRESS                    0x1ab441a8UL
#define SMN_FUNC4_PCIE5NBIO1_PCIE_ERR_CNTL_ADDRESS                    0x1ad441a8UL
#define SMN_FUNC5_PCIE0NBIO0_PCIE_ERR_CNTL_ADDRESS                    0x1a3451a8UL
#define SMN_FUNC5_PCIE0NBIO1_PCIE_ERR_CNTL_ADDRESS                    0x1a5451a8UL
#define SMN_FUNC5_PCIE1NBIO0_PCIE_ERR_CNTL_ADDRESS                    0x1a4451a8UL
#define SMN_FUNC5_PCIE1NBIO1_PCIE_ERR_CNTL_ADDRESS                    0x1a6451a8UL
#define SMN_FUNC5_PCIE2NBIO0_PCIE_ERR_CNTL_ADDRESS                    0x1a7451a8UL
#define SMN_FUNC5_PCIE2NBIO1_PCIE_ERR_CNTL_ADDRESS                    0x1a9451a8UL
#define SMN_FUNC5_PCIE3NBIO0_PCIE_ERR_CNTL_ADDRESS                    0x1a8451a8UL
#define SMN_FUNC5_PCIE3NBIO1_PCIE_ERR_CNTL_ADDRESS                    0x1aa451a8UL
#define SMN_FUNC5_PCIE5NBIO0_PCIE_ERR_CNTL_ADDRESS                    0x1ab451a8UL
#define SMN_FUNC5_PCIE5NBIO1_PCIE_ERR_CNTL_ADDRESS                    0x1ad451a8UL
#define SMN_FUNC6_PCIE0NBIO0_PCIE_ERR_CNTL_ADDRESS                    0x1a3461a8UL
#define SMN_FUNC6_PCIE0NBIO1_PCIE_ERR_CNTL_ADDRESS                    0x1a5461a8UL
#define SMN_FUNC6_PCIE1NBIO0_PCIE_ERR_CNTL_ADDRESS                    0x1a4461a8UL
#define SMN_FUNC6_PCIE1NBIO1_PCIE_ERR_CNTL_ADDRESS                    0x1a6461a8UL
#define SMN_FUNC6_PCIE2NBIO0_PCIE_ERR_CNTL_ADDRESS                    0x1a7461a8UL
#define SMN_FUNC6_PCIE2NBIO1_PCIE_ERR_CNTL_ADDRESS                    0x1a9461a8UL
#define SMN_FUNC6_PCIE3NBIO0_PCIE_ERR_CNTL_ADDRESS                    0x1a8461a8UL
#define SMN_FUNC6_PCIE3NBIO1_PCIE_ERR_CNTL_ADDRESS                    0x1aa461a8UL
#define SMN_FUNC6_PCIE5NBIO0_PCIE_ERR_CNTL_ADDRESS                    0x1ab461a8UL
#define SMN_FUNC6_PCIE5NBIO1_PCIE_ERR_CNTL_ADDRESS                    0x1ad461a8UL
#define SMN_FUNC7_PCIE0NBIO0_PCIE_ERR_CNTL_ADDRESS                    0x1a3471a8UL
#define SMN_FUNC7_PCIE0NBIO1_PCIE_ERR_CNTL_ADDRESS                    0x1a5471a8UL
#define SMN_FUNC7_PCIE1NBIO0_PCIE_ERR_CNTL_ADDRESS                    0x1a4471a8UL
#define SMN_FUNC7_PCIE1NBIO1_PCIE_ERR_CNTL_ADDRESS                    0x1a6471a8UL
#define SMN_FUNC7_PCIE2NBIO0_PCIE_ERR_CNTL_ADDRESS                    0x1a7471a8UL
#define SMN_FUNC7_PCIE2NBIO1_PCIE_ERR_CNTL_ADDRESS                    0x1a9471a8UL
#define SMN_FUNC7_PCIE3NBIO0_PCIE_ERR_CNTL_ADDRESS                    0x1a8471a8UL
#define SMN_FUNC7_PCIE3NBIO1_PCIE_ERR_CNTL_ADDRESS                    0x1aa471a8UL
#define SMN_FUNC7_PCIE5NBIO0_PCIE_ERR_CNTL_ADDRESS                    0x1ab471a8UL
#define SMN_FUNC7_PCIE5NBIO1_PCIE_ERR_CNTL_ADDRESS                    0x1ad471a8UL
#define SMN_FUNC8_PCIE0NBIO0_PCIE_ERR_CNTL_ADDRESS                    0x1a3481a8UL
#define SMN_FUNC8_PCIE0NBIO1_PCIE_ERR_CNTL_ADDRESS                    0x1a5481a8UL
#define SMN_FUNC8_PCIE1NBIO0_PCIE_ERR_CNTL_ADDRESS                    0x1a4481a8UL
#define SMN_FUNC8_PCIE1NBIO1_PCIE_ERR_CNTL_ADDRESS                    0x1a6481a8UL
#define SMN_FUNC8_PCIE2NBIO0_PCIE_ERR_CNTL_ADDRESS                    0x1a7481a8UL
#define SMN_FUNC8_PCIE2NBIO1_PCIE_ERR_CNTL_ADDRESS                    0x1a9481a8UL
#define SMN_FUNC8_PCIE3NBIO0_PCIE_ERR_CNTL_ADDRESS                    0x1a8481a8UL
#define SMN_FUNC8_PCIE3NBIO1_PCIE_ERR_CNTL_ADDRESS                    0x1aa481a8UL


/***********************************************************
* Register Name : PCIE_LC_BEST_EQ_SETTINGS
************************************************************/

#define PCIE_LC_BEST_EQ_SETTINGS_LC_BEST_PRESET_OFFSET         0
#define PCIE_LC_BEST_EQ_SETTINGS_LC_BEST_PRESET_MASK           0xf

#define PCIE_LC_BEST_EQ_SETTINGS_LC_BEST_PRECURSOR_OFFSET      4
#define PCIE_LC_BEST_EQ_SETTINGS_LC_BEST_PRECURSOR_MASK        0x3f0

#define PCIE_LC_BEST_EQ_SETTINGS_LC_BEST_CURSOR_OFFSET         10
#define PCIE_LC_BEST_EQ_SETTINGS_LC_BEST_CURSOR_MASK           0xfc00

#define PCIE_LC_BEST_EQ_SETTINGS_LC_BEST_POSTCURSOR_OFFSET     16
#define PCIE_LC_BEST_EQ_SETTINGS_LC_BEST_POSTCURSOR_MASK       0x3f0000

#define PCIE_LC_BEST_EQ_SETTINGS_LC_BEST_FOM_OFFSET            22
#define PCIE_LC_BEST_EQ_SETTINGS_LC_BEST_FOM_MASK              0x3fc00000

#define PCIE_LC_BEST_EQ_SETTINGS_LC_BEST_SETTINGS_RATE_OFFSET  30
#define PCIE_LC_BEST_EQ_SETTINGS_LC_BEST_SETTINGS_RATE_MASK    0xc0000000

typedef union {
  struct {
    UINT32                                      LC_BEST_PRESET:4;
    UINT32                                   LC_BEST_PRECURSOR:6;
    UINT32                                      LC_BEST_CURSOR:6;
    UINT32                                  LC_BEST_POSTCURSOR:6;
    UINT32                                         LC_BEST_FOM:8;
    UINT32                               LC_BEST_SETTINGS_RATE:2;
  } Field;
  UINT32 Value;
} PCIE_LC_BEST_EQ_SETTINGS_STRUCT;

#define PCICFG_PCIEPORT_PCIE_LC_BEST_EQ_SETTINGS_OFFSET             0x2e4
#define SMN_FUNC0_PCIE0NBIO0_PCIE_LC_BEST_EQ_SETTINGS_ADDRESS         0x1a3402e4UL
#define SMN_FUNC0_PCIE0NBIO1_PCIE_LC_BEST_EQ_SETTINGS_ADDRESS         0x1a5402e4UL
#define SMN_FUNC0_PCIE1NBIO0_PCIE_LC_BEST_EQ_SETTINGS_ADDRESS         0x1a4402e4UL
#define SMN_FUNC0_PCIE1NBIO1_PCIE_LC_BEST_EQ_SETTINGS_ADDRESS         0x1a6402e4UL
#define SMN_FUNC0_PCIE2NBIO0_PCIE_LC_BEST_EQ_SETTINGS_ADDRESS         0x1a7402e4UL
#define SMN_FUNC0_PCIE2NBIO1_PCIE_LC_BEST_EQ_SETTINGS_ADDRESS         0x1a9402e4UL
#define SMN_FUNC0_PCIE3NBIO0_PCIE_LC_BEST_EQ_SETTINGS_ADDRESS         0x1a8402e4UL
#define SMN_FUNC0_PCIE3NBIO1_PCIE_LC_BEST_EQ_SETTINGS_ADDRESS         0x1aa402e4UL
#define SMN_FUNC0_PCIE5NBIO0_PCIE_LC_BEST_EQ_SETTINGS_ADDRESS         0x1ab402e4UL
#define SMN_FUNC0_PCIE5NBIO1_PCIE_LC_BEST_EQ_SETTINGS_ADDRESS         0x1ad402e4UL
#define SMN_FUNC1_PCIE0NBIO0_PCIE_LC_BEST_EQ_SETTINGS_ADDRESS         0x1a3412e4UL
#define SMN_FUNC1_PCIE0NBIO1_PCIE_LC_BEST_EQ_SETTINGS_ADDRESS         0x1a5412e4UL
#define SMN_FUNC1_PCIE1NBIO0_PCIE_LC_BEST_EQ_SETTINGS_ADDRESS         0x1a4412e4UL
#define SMN_FUNC1_PCIE1NBIO1_PCIE_LC_BEST_EQ_SETTINGS_ADDRESS         0x1a6412e4UL
#define SMN_FUNC1_PCIE2NBIO0_PCIE_LC_BEST_EQ_SETTINGS_ADDRESS         0x1a7412e4UL
#define SMN_FUNC1_PCIE2NBIO1_PCIE_LC_BEST_EQ_SETTINGS_ADDRESS         0x1a9412e4UL
#define SMN_FUNC1_PCIE3NBIO0_PCIE_LC_BEST_EQ_SETTINGS_ADDRESS         0x1a8412e4UL
#define SMN_FUNC1_PCIE3NBIO1_PCIE_LC_BEST_EQ_SETTINGS_ADDRESS         0x1aa412e4UL
#define SMN_FUNC1_PCIE5NBIO0_PCIE_LC_BEST_EQ_SETTINGS_ADDRESS         0x1ab412e4UL
#define SMN_FUNC1_PCIE5NBIO1_PCIE_LC_BEST_EQ_SETTINGS_ADDRESS         0x1ad412e4UL
#define SMN_FUNC2_PCIE0NBIO0_PCIE_LC_BEST_EQ_SETTINGS_ADDRESS         0x1a3422e4UL
#define SMN_FUNC2_PCIE0NBIO1_PCIE_LC_BEST_EQ_SETTINGS_ADDRESS         0x1a5422e4UL
#define SMN_FUNC2_PCIE1NBIO0_PCIE_LC_BEST_EQ_SETTINGS_ADDRESS         0x1a4422e4UL
#define SMN_FUNC2_PCIE1NBIO1_PCIE_LC_BEST_EQ_SETTINGS_ADDRESS         0x1a6422e4UL
#define SMN_FUNC2_PCIE2NBIO0_PCIE_LC_BEST_EQ_SETTINGS_ADDRESS         0x1a7422e4UL
#define SMN_FUNC2_PCIE2NBIO1_PCIE_LC_BEST_EQ_SETTINGS_ADDRESS         0x1a9422e4UL
#define SMN_FUNC2_PCIE3NBIO0_PCIE_LC_BEST_EQ_SETTINGS_ADDRESS         0x1a8422e4UL
#define SMN_FUNC2_PCIE3NBIO1_PCIE_LC_BEST_EQ_SETTINGS_ADDRESS         0x1aa422e4UL
#define SMN_FUNC2_PCIE5NBIO0_PCIE_LC_BEST_EQ_SETTINGS_ADDRESS         0x1ab422e4UL
#define SMN_FUNC2_PCIE5NBIO1_PCIE_LC_BEST_EQ_SETTINGS_ADDRESS         0x1ad422e4UL
#define SMN_FUNC3_PCIE0NBIO0_PCIE_LC_BEST_EQ_SETTINGS_ADDRESS         0x1a3432e4UL
#define SMN_FUNC3_PCIE0NBIO1_PCIE_LC_BEST_EQ_SETTINGS_ADDRESS         0x1a5432e4UL
#define SMN_FUNC3_PCIE1NBIO0_PCIE_LC_BEST_EQ_SETTINGS_ADDRESS         0x1a4432e4UL
#define SMN_FUNC3_PCIE1NBIO1_PCIE_LC_BEST_EQ_SETTINGS_ADDRESS         0x1a6432e4UL
#define SMN_FUNC3_PCIE2NBIO0_PCIE_LC_BEST_EQ_SETTINGS_ADDRESS         0x1a7432e4UL
#define SMN_FUNC3_PCIE2NBIO1_PCIE_LC_BEST_EQ_SETTINGS_ADDRESS         0x1a9432e4UL
#define SMN_FUNC3_PCIE3NBIO0_PCIE_LC_BEST_EQ_SETTINGS_ADDRESS         0x1a8432e4UL
#define SMN_FUNC3_PCIE3NBIO1_PCIE_LC_BEST_EQ_SETTINGS_ADDRESS         0x1aa432e4UL
#define SMN_FUNC3_PCIE5NBIO0_PCIE_LC_BEST_EQ_SETTINGS_ADDRESS         0x1ab432e4UL
#define SMN_FUNC3_PCIE5NBIO1_PCIE_LC_BEST_EQ_SETTINGS_ADDRESS         0x1ad432e4UL
#define SMN_FUNC4_PCIE0NBIO0_PCIE_LC_BEST_EQ_SETTINGS_ADDRESS         0x1a3442e4UL
#define SMN_FUNC4_PCIE0NBIO1_PCIE_LC_BEST_EQ_SETTINGS_ADDRESS         0x1a5442e4UL
#define SMN_FUNC4_PCIE1NBIO0_PCIE_LC_BEST_EQ_SETTINGS_ADDRESS         0x1a4442e4UL
#define SMN_FUNC4_PCIE1NBIO1_PCIE_LC_BEST_EQ_SETTINGS_ADDRESS         0x1a6442e4UL
#define SMN_FUNC4_PCIE2NBIO0_PCIE_LC_BEST_EQ_SETTINGS_ADDRESS         0x1a7442e4UL
#define SMN_FUNC4_PCIE2NBIO1_PCIE_LC_BEST_EQ_SETTINGS_ADDRESS         0x1a9442e4UL
#define SMN_FUNC4_PCIE3NBIO0_PCIE_LC_BEST_EQ_SETTINGS_ADDRESS         0x1a8442e4UL
#define SMN_FUNC4_PCIE3NBIO1_PCIE_LC_BEST_EQ_SETTINGS_ADDRESS         0x1aa442e4UL
#define SMN_FUNC4_PCIE5NBIO0_PCIE_LC_BEST_EQ_SETTINGS_ADDRESS         0x1ab442e4UL
#define SMN_FUNC4_PCIE5NBIO1_PCIE_LC_BEST_EQ_SETTINGS_ADDRESS         0x1ad442e4UL
#define SMN_FUNC5_PCIE0NBIO0_PCIE_LC_BEST_EQ_SETTINGS_ADDRESS         0x1a3452e4UL
#define SMN_FUNC5_PCIE0NBIO1_PCIE_LC_BEST_EQ_SETTINGS_ADDRESS         0x1a5452e4UL
#define SMN_FUNC5_PCIE1NBIO0_PCIE_LC_BEST_EQ_SETTINGS_ADDRESS         0x1a4452e4UL
#define SMN_FUNC5_PCIE1NBIO1_PCIE_LC_BEST_EQ_SETTINGS_ADDRESS         0x1a6452e4UL
#define SMN_FUNC5_PCIE2NBIO0_PCIE_LC_BEST_EQ_SETTINGS_ADDRESS         0x1a7452e4UL
#define SMN_FUNC5_PCIE2NBIO1_PCIE_LC_BEST_EQ_SETTINGS_ADDRESS         0x1a9452e4UL
#define SMN_FUNC5_PCIE3NBIO0_PCIE_LC_BEST_EQ_SETTINGS_ADDRESS         0x1a8452e4UL
#define SMN_FUNC5_PCIE3NBIO1_PCIE_LC_BEST_EQ_SETTINGS_ADDRESS         0x1aa452e4UL
#define SMN_FUNC5_PCIE5NBIO0_PCIE_LC_BEST_EQ_SETTINGS_ADDRESS         0x1ab452e4UL
#define SMN_FUNC5_PCIE5NBIO1_PCIE_LC_BEST_EQ_SETTINGS_ADDRESS         0x1ad452e4UL
#define SMN_FUNC6_PCIE0NBIO0_PCIE_LC_BEST_EQ_SETTINGS_ADDRESS         0x1a3462e4UL
#define SMN_FUNC6_PCIE0NBIO1_PCIE_LC_BEST_EQ_SETTINGS_ADDRESS         0x1a5462e4UL
#define SMN_FUNC6_PCIE1NBIO0_PCIE_LC_BEST_EQ_SETTINGS_ADDRESS         0x1a4462e4UL
#define SMN_FUNC6_PCIE1NBIO1_PCIE_LC_BEST_EQ_SETTINGS_ADDRESS         0x1a6462e4UL
#define SMN_FUNC6_PCIE2NBIO0_PCIE_LC_BEST_EQ_SETTINGS_ADDRESS         0x1a7462e4UL
#define SMN_FUNC6_PCIE2NBIO1_PCIE_LC_BEST_EQ_SETTINGS_ADDRESS         0x1a9462e4UL
#define SMN_FUNC6_PCIE3NBIO0_PCIE_LC_BEST_EQ_SETTINGS_ADDRESS         0x1a8462e4UL
#define SMN_FUNC6_PCIE3NBIO1_PCIE_LC_BEST_EQ_SETTINGS_ADDRESS         0x1aa462e4UL
#define SMN_FUNC6_PCIE5NBIO0_PCIE_LC_BEST_EQ_SETTINGS_ADDRESS         0x1ab462e4UL
#define SMN_FUNC6_PCIE5NBIO1_PCIE_LC_BEST_EQ_SETTINGS_ADDRESS         0x1ad462e4UL
#define SMN_FUNC7_PCIE0NBIO0_PCIE_LC_BEST_EQ_SETTINGS_ADDRESS         0x1a3472e4UL
#define SMN_FUNC7_PCIE0NBIO1_PCIE_LC_BEST_EQ_SETTINGS_ADDRESS         0x1a5472e4UL
#define SMN_FUNC7_PCIE1NBIO0_PCIE_LC_BEST_EQ_SETTINGS_ADDRESS         0x1a4472e4UL
#define SMN_FUNC7_PCIE1NBIO1_PCIE_LC_BEST_EQ_SETTINGS_ADDRESS         0x1a6472e4UL
#define SMN_FUNC7_PCIE2NBIO0_PCIE_LC_BEST_EQ_SETTINGS_ADDRESS         0x1a7472e4UL
#define SMN_FUNC7_PCIE2NBIO1_PCIE_LC_BEST_EQ_SETTINGS_ADDRESS         0x1a9472e4UL
#define SMN_FUNC7_PCIE3NBIO0_PCIE_LC_BEST_EQ_SETTINGS_ADDRESS         0x1a8472e4UL
#define SMN_FUNC7_PCIE3NBIO1_PCIE_LC_BEST_EQ_SETTINGS_ADDRESS         0x1aa472e4UL
#define SMN_FUNC7_PCIE5NBIO0_PCIE_LC_BEST_EQ_SETTINGS_ADDRESS         0x1ab472e4UL
#define SMN_FUNC7_PCIE5NBIO1_PCIE_LC_BEST_EQ_SETTINGS_ADDRESS         0x1ad472e4UL
#define SMN_FUNC8_PCIE0NBIO0_PCIE_LC_BEST_EQ_SETTINGS_ADDRESS         0x1a3482e4UL
#define SMN_FUNC8_PCIE0NBIO1_PCIE_LC_BEST_EQ_SETTINGS_ADDRESS         0x1a5482e4UL
#define SMN_FUNC8_PCIE1NBIO0_PCIE_LC_BEST_EQ_SETTINGS_ADDRESS         0x1a4482e4UL
#define SMN_FUNC8_PCIE1NBIO1_PCIE_LC_BEST_EQ_SETTINGS_ADDRESS         0x1a6482e4UL
#define SMN_FUNC8_PCIE2NBIO0_PCIE_LC_BEST_EQ_SETTINGS_ADDRESS         0x1a7482e4UL
#define SMN_FUNC8_PCIE2NBIO1_PCIE_LC_BEST_EQ_SETTINGS_ADDRESS         0x1a9482e4UL
#define SMN_FUNC8_PCIE3NBIO0_PCIE_LC_BEST_EQ_SETTINGS_ADDRESS         0x1a8482e4UL
#define SMN_FUNC8_PCIE3NBIO1_PCIE_LC_BEST_EQ_SETTINGS_ADDRESS         0x1aa482e4UL


/***********************************************************
* Register Name : PCIE_LC_BW_CHANGE_CNTL
************************************************************/

#define PCIE_LC_BW_CHANGE_CNTL_LC_BW_CHANGE_INT_EN_OFFSET      0
#define PCIE_LC_BW_CHANGE_CNTL_LC_BW_CHANGE_INT_EN_MASK        0x1

#define PCIE_LC_BW_CHANGE_CNTL_LC_HW_INIT_SPEED_CHANGE_OFFSET  1
#define PCIE_LC_BW_CHANGE_CNTL_LC_HW_INIT_SPEED_CHANGE_MASK    0x2

#define PCIE_LC_BW_CHANGE_CNTL_LC_SW_INIT_SPEED_CHANGE_OFFSET  2
#define PCIE_LC_BW_CHANGE_CNTL_LC_SW_INIT_SPEED_CHANGE_MASK    0x4

#define PCIE_LC_BW_CHANGE_CNTL_LC_OTHER_INIT_SPEED_CHANGE_OFFSET 3
#define PCIE_LC_BW_CHANGE_CNTL_LC_OTHER_INIT_SPEED_CHANGE_MASK 0x8

#define PCIE_LC_BW_CHANGE_CNTL_LC_RELIABILITY_SPEED_CHANGE_OFFSET 4
#define PCIE_LC_BW_CHANGE_CNTL_LC_RELIABILITY_SPEED_CHANGE_MASK 0x10

#define PCIE_LC_BW_CHANGE_CNTL_LC_FAILED_SPEED_NEG_OFFSET      5
#define PCIE_LC_BW_CHANGE_CNTL_LC_FAILED_SPEED_NEG_MASK        0x20

#define PCIE_LC_BW_CHANGE_CNTL_LC_LONG_LW_CHANGE_OFFSET        6
#define PCIE_LC_BW_CHANGE_CNTL_LC_LONG_LW_CHANGE_MASK          0x40

#define PCIE_LC_BW_CHANGE_CNTL_LC_SHORT_LW_CHANGE_OFFSET       7
#define PCIE_LC_BW_CHANGE_CNTL_LC_SHORT_LW_CHANGE_MASK         0x80

#define PCIE_LC_BW_CHANGE_CNTL_LC_LW_CHANGE_OTHER_OFFSET       8
#define PCIE_LC_BW_CHANGE_CNTL_LC_LW_CHANGE_OTHER_MASK         0x100

#define PCIE_LC_BW_CHANGE_CNTL_LC_LW_CHANGE_FAILED_OFFSET      9
#define PCIE_LC_BW_CHANGE_CNTL_LC_LW_CHANGE_FAILED_MASK        0x200

#define PCIE_LC_BW_CHANGE_CNTL_LC_LINK_BW_NOTIFICATION_DETECT_MODE_OFFSET 10
#define PCIE_LC_BW_CHANGE_CNTL_LC_LINK_BW_NOTIFICATION_DETECT_MODE_MASK 0x400

#define PCIE_LC_BW_CHANGE_CNTL_LC_SPEED_NEG_UNSUCCESSFUL_OFFSET 11
#define PCIE_LC_BW_CHANGE_CNTL_LC_SPEED_NEG_UNSUCCESSFUL_MASK  0x800

#define PCIE_LC_BW_CHANGE_CNTL_Reserved_31_12_OFFSET           12
#define PCIE_LC_BW_CHANGE_CNTL_Reserved_31_12_MASK             0xfffff000

typedef union {
  struct {
    UINT32                                 LC_BW_CHANGE_INT_EN:1;
    UINT32                             LC_HW_INIT_SPEED_CHANGE:1;
    UINT32                             LC_SW_INIT_SPEED_CHANGE:1;
    UINT32                          LC_OTHER_INIT_SPEED_CHANGE:1;
    UINT32                         LC_RELIABILITY_SPEED_CHANGE:1;
    UINT32                                 LC_FAILED_SPEED_NEG:1;
    UINT32                                   LC_LONG_LW_CHANGE:1;
    UINT32                                  LC_SHORT_LW_CHANGE:1;
    UINT32                                  LC_LW_CHANGE_OTHER:1;
    UINT32                                 LC_LW_CHANGE_FAILED:1;
    UINT32                 LC_LINK_BW_NOTIFICATION_DETECT_MODE:1;
    UINT32                           LC_SPEED_NEG_UNSUCCESSFUL:1;
    UINT32                                      Reserved_31_12:20;
  } Field;
  UINT32 Value;
} PCIE_LC_BW_CHANGE_CNTL_STRUCT;

#define PCICFG_PCIEPORT_PCIE_LC_BW_CHANGE_CNTL_OFFSET               0x2c8
#define SMN_FUNC0_PCIE0NBIO0_PCIE_LC_BW_CHANGE_CNTL_ADDRESS           0x1a3402c8UL
#define SMN_FUNC0_PCIE0NBIO1_PCIE_LC_BW_CHANGE_CNTL_ADDRESS           0x1a5402c8UL
#define SMN_FUNC0_PCIE1NBIO0_PCIE_LC_BW_CHANGE_CNTL_ADDRESS           0x1a4402c8UL
#define SMN_FUNC0_PCIE1NBIO1_PCIE_LC_BW_CHANGE_CNTL_ADDRESS           0x1a6402c8UL
#define SMN_FUNC0_PCIE2NBIO0_PCIE_LC_BW_CHANGE_CNTL_ADDRESS           0x1a7402c8UL
#define SMN_FUNC0_PCIE2NBIO1_PCIE_LC_BW_CHANGE_CNTL_ADDRESS           0x1a9402c8UL
#define SMN_FUNC0_PCIE3NBIO0_PCIE_LC_BW_CHANGE_CNTL_ADDRESS           0x1a8402c8UL
#define SMN_FUNC0_PCIE3NBIO1_PCIE_LC_BW_CHANGE_CNTL_ADDRESS           0x1aa402c8UL
#define SMN_FUNC0_PCIE5NBIO0_PCIE_LC_BW_CHANGE_CNTL_ADDRESS           0x1ab402c8UL
#define SMN_FUNC0_PCIE5NBIO1_PCIE_LC_BW_CHANGE_CNTL_ADDRESS           0x1ad402c8UL
#define SMN_FUNC1_PCIE0NBIO0_PCIE_LC_BW_CHANGE_CNTL_ADDRESS           0x1a3412c8UL
#define SMN_FUNC1_PCIE0NBIO1_PCIE_LC_BW_CHANGE_CNTL_ADDRESS           0x1a5412c8UL
#define SMN_FUNC1_PCIE1NBIO0_PCIE_LC_BW_CHANGE_CNTL_ADDRESS           0x1a4412c8UL
#define SMN_FUNC1_PCIE1NBIO1_PCIE_LC_BW_CHANGE_CNTL_ADDRESS           0x1a6412c8UL
#define SMN_FUNC1_PCIE2NBIO0_PCIE_LC_BW_CHANGE_CNTL_ADDRESS           0x1a7412c8UL
#define SMN_FUNC1_PCIE2NBIO1_PCIE_LC_BW_CHANGE_CNTL_ADDRESS           0x1a9412c8UL
#define SMN_FUNC1_PCIE3NBIO0_PCIE_LC_BW_CHANGE_CNTL_ADDRESS           0x1a8412c8UL
#define SMN_FUNC1_PCIE3NBIO1_PCIE_LC_BW_CHANGE_CNTL_ADDRESS           0x1aa412c8UL
#define SMN_FUNC1_PCIE5NBIO0_PCIE_LC_BW_CHANGE_CNTL_ADDRESS           0x1ab412c8UL
#define SMN_FUNC1_PCIE5NBIO1_PCIE_LC_BW_CHANGE_CNTL_ADDRESS           0x1ad412c8UL
#define SMN_FUNC2_PCIE0NBIO0_PCIE_LC_BW_CHANGE_CNTL_ADDRESS           0x1a3422c8UL
#define SMN_FUNC2_PCIE0NBIO1_PCIE_LC_BW_CHANGE_CNTL_ADDRESS           0x1a5422c8UL
#define SMN_FUNC2_PCIE1NBIO0_PCIE_LC_BW_CHANGE_CNTL_ADDRESS           0x1a4422c8UL
#define SMN_FUNC2_PCIE1NBIO1_PCIE_LC_BW_CHANGE_CNTL_ADDRESS           0x1a6422c8UL
#define SMN_FUNC2_PCIE2NBIO0_PCIE_LC_BW_CHANGE_CNTL_ADDRESS           0x1a7422c8UL
#define SMN_FUNC2_PCIE2NBIO1_PCIE_LC_BW_CHANGE_CNTL_ADDRESS           0x1a9422c8UL
#define SMN_FUNC2_PCIE3NBIO0_PCIE_LC_BW_CHANGE_CNTL_ADDRESS           0x1a8422c8UL
#define SMN_FUNC2_PCIE3NBIO1_PCIE_LC_BW_CHANGE_CNTL_ADDRESS           0x1aa422c8UL
#define SMN_FUNC2_PCIE5NBIO0_PCIE_LC_BW_CHANGE_CNTL_ADDRESS           0x1ab422c8UL
#define SMN_FUNC2_PCIE5NBIO1_PCIE_LC_BW_CHANGE_CNTL_ADDRESS           0x1ad422c8UL
#define SMN_FUNC3_PCIE0NBIO0_PCIE_LC_BW_CHANGE_CNTL_ADDRESS           0x1a3432c8UL
#define SMN_FUNC3_PCIE0NBIO1_PCIE_LC_BW_CHANGE_CNTL_ADDRESS           0x1a5432c8UL
#define SMN_FUNC3_PCIE1NBIO0_PCIE_LC_BW_CHANGE_CNTL_ADDRESS           0x1a4432c8UL
#define SMN_FUNC3_PCIE1NBIO1_PCIE_LC_BW_CHANGE_CNTL_ADDRESS           0x1a6432c8UL
#define SMN_FUNC3_PCIE2NBIO0_PCIE_LC_BW_CHANGE_CNTL_ADDRESS           0x1a7432c8UL
#define SMN_FUNC3_PCIE2NBIO1_PCIE_LC_BW_CHANGE_CNTL_ADDRESS           0x1a9432c8UL
#define SMN_FUNC3_PCIE3NBIO0_PCIE_LC_BW_CHANGE_CNTL_ADDRESS           0x1a8432c8UL
#define SMN_FUNC3_PCIE3NBIO1_PCIE_LC_BW_CHANGE_CNTL_ADDRESS           0x1aa432c8UL
#define SMN_FUNC3_PCIE5NBIO0_PCIE_LC_BW_CHANGE_CNTL_ADDRESS           0x1ab432c8UL
#define SMN_FUNC3_PCIE5NBIO1_PCIE_LC_BW_CHANGE_CNTL_ADDRESS           0x1ad432c8UL
#define SMN_FUNC4_PCIE0NBIO0_PCIE_LC_BW_CHANGE_CNTL_ADDRESS           0x1a3442c8UL
#define SMN_FUNC4_PCIE0NBIO1_PCIE_LC_BW_CHANGE_CNTL_ADDRESS           0x1a5442c8UL
#define SMN_FUNC4_PCIE1NBIO0_PCIE_LC_BW_CHANGE_CNTL_ADDRESS           0x1a4442c8UL
#define SMN_FUNC4_PCIE1NBIO1_PCIE_LC_BW_CHANGE_CNTL_ADDRESS           0x1a6442c8UL
#define SMN_FUNC4_PCIE2NBIO0_PCIE_LC_BW_CHANGE_CNTL_ADDRESS           0x1a7442c8UL
#define SMN_FUNC4_PCIE2NBIO1_PCIE_LC_BW_CHANGE_CNTL_ADDRESS           0x1a9442c8UL
#define SMN_FUNC4_PCIE3NBIO0_PCIE_LC_BW_CHANGE_CNTL_ADDRESS           0x1a8442c8UL
#define SMN_FUNC4_PCIE3NBIO1_PCIE_LC_BW_CHANGE_CNTL_ADDRESS           0x1aa442c8UL
#define SMN_FUNC4_PCIE5NBIO0_PCIE_LC_BW_CHANGE_CNTL_ADDRESS           0x1ab442c8UL
#define SMN_FUNC4_PCIE5NBIO1_PCIE_LC_BW_CHANGE_CNTL_ADDRESS           0x1ad442c8UL
#define SMN_FUNC5_PCIE0NBIO0_PCIE_LC_BW_CHANGE_CNTL_ADDRESS           0x1a3452c8UL
#define SMN_FUNC5_PCIE0NBIO1_PCIE_LC_BW_CHANGE_CNTL_ADDRESS           0x1a5452c8UL
#define SMN_FUNC5_PCIE1NBIO0_PCIE_LC_BW_CHANGE_CNTL_ADDRESS           0x1a4452c8UL
#define SMN_FUNC5_PCIE1NBIO1_PCIE_LC_BW_CHANGE_CNTL_ADDRESS           0x1a6452c8UL
#define SMN_FUNC5_PCIE2NBIO0_PCIE_LC_BW_CHANGE_CNTL_ADDRESS           0x1a7452c8UL
#define SMN_FUNC5_PCIE2NBIO1_PCIE_LC_BW_CHANGE_CNTL_ADDRESS           0x1a9452c8UL
#define SMN_FUNC5_PCIE3NBIO0_PCIE_LC_BW_CHANGE_CNTL_ADDRESS           0x1a8452c8UL
#define SMN_FUNC5_PCIE3NBIO1_PCIE_LC_BW_CHANGE_CNTL_ADDRESS           0x1aa452c8UL
#define SMN_FUNC5_PCIE5NBIO0_PCIE_LC_BW_CHANGE_CNTL_ADDRESS           0x1ab452c8UL
#define SMN_FUNC5_PCIE5NBIO1_PCIE_LC_BW_CHANGE_CNTL_ADDRESS           0x1ad452c8UL
#define SMN_FUNC6_PCIE0NBIO0_PCIE_LC_BW_CHANGE_CNTL_ADDRESS           0x1a3462c8UL
#define SMN_FUNC6_PCIE0NBIO1_PCIE_LC_BW_CHANGE_CNTL_ADDRESS           0x1a5462c8UL
#define SMN_FUNC6_PCIE1NBIO0_PCIE_LC_BW_CHANGE_CNTL_ADDRESS           0x1a4462c8UL
#define SMN_FUNC6_PCIE1NBIO1_PCIE_LC_BW_CHANGE_CNTL_ADDRESS           0x1a6462c8UL
#define SMN_FUNC6_PCIE2NBIO0_PCIE_LC_BW_CHANGE_CNTL_ADDRESS           0x1a7462c8UL
#define SMN_FUNC6_PCIE2NBIO1_PCIE_LC_BW_CHANGE_CNTL_ADDRESS           0x1a9462c8UL
#define SMN_FUNC6_PCIE3NBIO0_PCIE_LC_BW_CHANGE_CNTL_ADDRESS           0x1a8462c8UL
#define SMN_FUNC6_PCIE3NBIO1_PCIE_LC_BW_CHANGE_CNTL_ADDRESS           0x1aa462c8UL
#define SMN_FUNC6_PCIE5NBIO0_PCIE_LC_BW_CHANGE_CNTL_ADDRESS           0x1ab462c8UL
#define SMN_FUNC6_PCIE5NBIO1_PCIE_LC_BW_CHANGE_CNTL_ADDRESS           0x1ad462c8UL
#define SMN_FUNC7_PCIE0NBIO0_PCIE_LC_BW_CHANGE_CNTL_ADDRESS           0x1a3472c8UL
#define SMN_FUNC7_PCIE0NBIO1_PCIE_LC_BW_CHANGE_CNTL_ADDRESS           0x1a5472c8UL
#define SMN_FUNC7_PCIE1NBIO0_PCIE_LC_BW_CHANGE_CNTL_ADDRESS           0x1a4472c8UL
#define SMN_FUNC7_PCIE1NBIO1_PCIE_LC_BW_CHANGE_CNTL_ADDRESS           0x1a6472c8UL
#define SMN_FUNC7_PCIE2NBIO0_PCIE_LC_BW_CHANGE_CNTL_ADDRESS           0x1a7472c8UL
#define SMN_FUNC7_PCIE2NBIO1_PCIE_LC_BW_CHANGE_CNTL_ADDRESS           0x1a9472c8UL
#define SMN_FUNC7_PCIE3NBIO0_PCIE_LC_BW_CHANGE_CNTL_ADDRESS           0x1a8472c8UL
#define SMN_FUNC7_PCIE3NBIO1_PCIE_LC_BW_CHANGE_CNTL_ADDRESS           0x1aa472c8UL
#define SMN_FUNC7_PCIE5NBIO0_PCIE_LC_BW_CHANGE_CNTL_ADDRESS           0x1ab472c8UL
#define SMN_FUNC7_PCIE5NBIO1_PCIE_LC_BW_CHANGE_CNTL_ADDRESS           0x1ad472c8UL
#define SMN_FUNC8_PCIE0NBIO0_PCIE_LC_BW_CHANGE_CNTL_ADDRESS           0x1a3482c8UL
#define SMN_FUNC8_PCIE0NBIO1_PCIE_LC_BW_CHANGE_CNTL_ADDRESS           0x1a5482c8UL
#define SMN_FUNC8_PCIE1NBIO0_PCIE_LC_BW_CHANGE_CNTL_ADDRESS           0x1a4482c8UL
#define SMN_FUNC8_PCIE1NBIO1_PCIE_LC_BW_CHANGE_CNTL_ADDRESS           0x1a6482c8UL
#define SMN_FUNC8_PCIE2NBIO0_PCIE_LC_BW_CHANGE_CNTL_ADDRESS           0x1a7482c8UL
#define SMN_FUNC8_PCIE2NBIO1_PCIE_LC_BW_CHANGE_CNTL_ADDRESS           0x1a9482c8UL
#define SMN_FUNC8_PCIE3NBIO0_PCIE_LC_BW_CHANGE_CNTL_ADDRESS           0x1a8482c8UL
#define SMN_FUNC8_PCIE3NBIO1_PCIE_LC_BW_CHANGE_CNTL_ADDRESS           0x1aa482c8UL


/***********************************************************
* Register Name : PCIE_LC_CNTL5
************************************************************/

#define PCIE_LC_CNTL5_LC_LOCAL_EQ_SETTINGS_RATE_OFFSET         0
#define PCIE_LC_CNTL5_LC_LOCAL_EQ_SETTINGS_RATE_MASK           0x3

#define PCIE_LC_CNTL5_LC_LOCAL_PRESET_OFFSET                   2
#define PCIE_LC_CNTL5_LC_LOCAL_PRESET_MASK                     0x3c

#define PCIE_LC_CNTL5_LC_LOCAL_PRE_CURSOR_OFFSET               6
#define PCIE_LC_CNTL5_LC_LOCAL_PRE_CURSOR_MASK                 0x3c0

#define PCIE_LC_CNTL5_LC_LOCAL_CURSOR_OFFSET                   10
#define PCIE_LC_CNTL5_LC_LOCAL_CURSOR_MASK                     0xfc00

#define PCIE_LC_CNTL5_LC_LOCAL_POST_CURSOR_OFFSET              16
#define PCIE_LC_CNTL5_LC_LOCAL_POST_CURSOR_MASK                0x1f0000

#define PCIE_LC_CNTL5_LC_LOCAL_USE_PRESET_OFFSET               21
#define PCIE_LC_CNTL5_LC_LOCAL_USE_PRESET_MASK                 0x200000

#define PCIE_LC_CNTL5_LC_SAFE_RECOVER_CNTL_OFFSET              22
#define PCIE_LC_CNTL5_LC_SAFE_RECOVER_CNTL_MASK                0xc00000

#define PCIE_LC_CNTL5_LC_DSC_EQ_FS_LF_INVALID_TO_PRESETS_OFFSET 24
#define PCIE_LC_CNTL5_LC_DSC_EQ_FS_LF_INVALID_TO_PRESETS_MASK  0x1000000

#define PCIE_LC_CNTL5_LC_TX_SWING_OVERRIDE_OFFSET              25
#define PCIE_LC_CNTL5_LC_TX_SWING_OVERRIDE_MASK                0x2000000

#define PCIE_LC_CNTL5_LC_ACCEPT_ALL_PRESETS_OFFSET             26
#define PCIE_LC_CNTL5_LC_ACCEPT_ALL_PRESETS_MASK               0x4000000

#define PCIE_LC_CNTL5_LC_ACCEPT_ALL_PRESETS_TEST_OFFSET        27
#define PCIE_LC_CNTL5_LC_ACCEPT_ALL_PRESETS_TEST_MASK          0x8000000

#define PCIE_LC_CNTL5_LC_WAIT_IN_DETECT_OFFSET                 28
#define PCIE_LC_CNTL5_LC_WAIT_IN_DETECT_MASK                   0x10000000

#define PCIE_LC_CNTL5_LC_HOLD_TRAINING_MODE_OFFSET             29
#define PCIE_LC_CNTL5_LC_HOLD_TRAINING_MODE_MASK               0xe0000000

typedef union {
  struct {
    UINT32                           LC_LOCAL_EQ_SETTINGS_RATE:2;
    UINT32                                     LC_LOCAL_PRESET:4;
    UINT32                                 LC_LOCAL_PRE_CURSOR:4;
    UINT32                                     LC_LOCAL_CURSOR:6;
    UINT32                                LC_LOCAL_POST_CURSOR:5;
    UINT32                                 LC_LOCAL_USE_PRESET:1;
    UINT32                                LC_SAFE_RECOVER_CNTL:2;
    UINT32                  LC_DSC_EQ_FS_LF_INVALID_TO_PRESETS:1;
    UINT32                                LC_TX_SWING_OVERRIDE:1;
    UINT32                               LC_ACCEPT_ALL_PRESETS:1;
    UINT32                          LC_ACCEPT_ALL_PRESETS_TEST:1;
    UINT32                                   LC_WAIT_IN_DETECT:1;
    UINT32                               LC_HOLD_TRAINING_MODE:3;
  } Field;
  UINT32 Value;
} PCIE_LC_CNTL5_STRUCT;

#define PCICFG_PCIEPORT_PCIE_LC_CNTL5_OFFSET                        0x2dc
#define SMN_FUNC0_PCIE0NBIO0_PCIE_LC_CNTL5_ADDRESS                    0x1a3402dcUL
#define SMN_FUNC0_PCIE0NBIO1_PCIE_LC_CNTL5_ADDRESS                    0x1a5402dcUL
#define SMN_FUNC0_PCIE1NBIO0_PCIE_LC_CNTL5_ADDRESS                    0x1a4402dcUL
#define SMN_FUNC0_PCIE1NBIO1_PCIE_LC_CNTL5_ADDRESS                    0x1a6402dcUL
#define SMN_FUNC0_PCIE2NBIO0_PCIE_LC_CNTL5_ADDRESS                    0x1a7402dcUL
#define SMN_FUNC0_PCIE2NBIO1_PCIE_LC_CNTL5_ADDRESS                    0x1a9402dcUL
#define SMN_FUNC0_PCIE3NBIO0_PCIE_LC_CNTL5_ADDRESS                    0x1a8402dcUL
#define SMN_FUNC0_PCIE3NBIO1_PCIE_LC_CNTL5_ADDRESS                    0x1aa402dcUL
#define SMN_FUNC0_PCIE5NBIO0_PCIE_LC_CNTL5_ADDRESS                    0x1ab402dcUL
#define SMN_FUNC0_PCIE5NBIO1_PCIE_LC_CNTL5_ADDRESS                    0x1ad402dcUL
#define SMN_FUNC1_PCIE0NBIO0_PCIE_LC_CNTL5_ADDRESS                    0x1a3412dcUL
#define SMN_FUNC1_PCIE0NBIO1_PCIE_LC_CNTL5_ADDRESS                    0x1a5412dcUL
#define SMN_FUNC1_PCIE1NBIO0_PCIE_LC_CNTL5_ADDRESS                    0x1a4412dcUL
#define SMN_FUNC1_PCIE1NBIO1_PCIE_LC_CNTL5_ADDRESS                    0x1a6412dcUL
#define SMN_FUNC1_PCIE2NBIO0_PCIE_LC_CNTL5_ADDRESS                    0x1a7412dcUL
#define SMN_FUNC1_PCIE2NBIO1_PCIE_LC_CNTL5_ADDRESS                    0x1a9412dcUL
#define SMN_FUNC1_PCIE3NBIO0_PCIE_LC_CNTL5_ADDRESS                    0x1a8412dcUL
#define SMN_FUNC1_PCIE3NBIO1_PCIE_LC_CNTL5_ADDRESS                    0x1aa412dcUL
#define SMN_FUNC1_PCIE5NBIO0_PCIE_LC_CNTL5_ADDRESS                    0x1ab412dcUL
#define SMN_FUNC1_PCIE5NBIO1_PCIE_LC_CNTL5_ADDRESS                    0x1ad412dcUL
#define SMN_FUNC2_PCIE0NBIO0_PCIE_LC_CNTL5_ADDRESS                    0x1a3422dcUL
#define SMN_FUNC2_PCIE0NBIO1_PCIE_LC_CNTL5_ADDRESS                    0x1a5422dcUL
#define SMN_FUNC2_PCIE1NBIO0_PCIE_LC_CNTL5_ADDRESS                    0x1a4422dcUL
#define SMN_FUNC2_PCIE1NBIO1_PCIE_LC_CNTL5_ADDRESS                    0x1a6422dcUL
#define SMN_FUNC2_PCIE2NBIO0_PCIE_LC_CNTL5_ADDRESS                    0x1a7422dcUL
#define SMN_FUNC2_PCIE2NBIO1_PCIE_LC_CNTL5_ADDRESS                    0x1a9422dcUL
#define SMN_FUNC2_PCIE3NBIO0_PCIE_LC_CNTL5_ADDRESS                    0x1a8422dcUL
#define SMN_FUNC2_PCIE3NBIO1_PCIE_LC_CNTL5_ADDRESS                    0x1aa422dcUL
#define SMN_FUNC2_PCIE5NBIO0_PCIE_LC_CNTL5_ADDRESS                    0x1ab422dcUL
#define SMN_FUNC2_PCIE5NBIO1_PCIE_LC_CNTL5_ADDRESS                    0x1ad422dcUL
#define SMN_FUNC3_PCIE0NBIO0_PCIE_LC_CNTL5_ADDRESS                    0x1a3432dcUL
#define SMN_FUNC3_PCIE0NBIO1_PCIE_LC_CNTL5_ADDRESS                    0x1a5432dcUL
#define SMN_FUNC3_PCIE1NBIO0_PCIE_LC_CNTL5_ADDRESS                    0x1a4432dcUL
#define SMN_FUNC3_PCIE1NBIO1_PCIE_LC_CNTL5_ADDRESS                    0x1a6432dcUL
#define SMN_FUNC3_PCIE2NBIO0_PCIE_LC_CNTL5_ADDRESS                    0x1a7432dcUL
#define SMN_FUNC3_PCIE2NBIO1_PCIE_LC_CNTL5_ADDRESS                    0x1a9432dcUL
#define SMN_FUNC3_PCIE3NBIO0_PCIE_LC_CNTL5_ADDRESS                    0x1a8432dcUL
#define SMN_FUNC3_PCIE3NBIO1_PCIE_LC_CNTL5_ADDRESS                    0x1aa432dcUL
#define SMN_FUNC3_PCIE5NBIO0_PCIE_LC_CNTL5_ADDRESS                    0x1ab432dcUL
#define SMN_FUNC3_PCIE5NBIO1_PCIE_LC_CNTL5_ADDRESS                    0x1ad432dcUL
#define SMN_FUNC4_PCIE0NBIO0_PCIE_LC_CNTL5_ADDRESS                    0x1a3442dcUL
#define SMN_FUNC4_PCIE0NBIO1_PCIE_LC_CNTL5_ADDRESS                    0x1a5442dcUL
#define SMN_FUNC4_PCIE1NBIO0_PCIE_LC_CNTL5_ADDRESS                    0x1a4442dcUL
#define SMN_FUNC4_PCIE1NBIO1_PCIE_LC_CNTL5_ADDRESS                    0x1a6442dcUL
#define SMN_FUNC4_PCIE2NBIO0_PCIE_LC_CNTL5_ADDRESS                    0x1a7442dcUL
#define SMN_FUNC4_PCIE2NBIO1_PCIE_LC_CNTL5_ADDRESS                    0x1a9442dcUL
#define SMN_FUNC4_PCIE3NBIO0_PCIE_LC_CNTL5_ADDRESS                    0x1a8442dcUL
#define SMN_FUNC4_PCIE3NBIO1_PCIE_LC_CNTL5_ADDRESS                    0x1aa442dcUL
#define SMN_FUNC4_PCIE5NBIO0_PCIE_LC_CNTL5_ADDRESS                    0x1ab442dcUL
#define SMN_FUNC4_PCIE5NBIO1_PCIE_LC_CNTL5_ADDRESS                    0x1ad442dcUL
#define SMN_FUNC5_PCIE0NBIO0_PCIE_LC_CNTL5_ADDRESS                    0x1a3452dcUL
#define SMN_FUNC5_PCIE0NBIO1_PCIE_LC_CNTL5_ADDRESS                    0x1a5452dcUL
#define SMN_FUNC5_PCIE1NBIO0_PCIE_LC_CNTL5_ADDRESS                    0x1a4452dcUL
#define SMN_FUNC5_PCIE1NBIO1_PCIE_LC_CNTL5_ADDRESS                    0x1a6452dcUL
#define SMN_FUNC5_PCIE2NBIO0_PCIE_LC_CNTL5_ADDRESS                    0x1a7452dcUL
#define SMN_FUNC5_PCIE2NBIO1_PCIE_LC_CNTL5_ADDRESS                    0x1a9452dcUL
#define SMN_FUNC5_PCIE3NBIO0_PCIE_LC_CNTL5_ADDRESS                    0x1a8452dcUL
#define SMN_FUNC5_PCIE3NBIO1_PCIE_LC_CNTL5_ADDRESS                    0x1aa452dcUL
#define SMN_FUNC5_PCIE5NBIO0_PCIE_LC_CNTL5_ADDRESS                    0x1ab452dcUL
#define SMN_FUNC5_PCIE5NBIO1_PCIE_LC_CNTL5_ADDRESS                    0x1ad452dcUL
#define SMN_FUNC6_PCIE0NBIO0_PCIE_LC_CNTL5_ADDRESS                    0x1a3462dcUL
#define SMN_FUNC6_PCIE0NBIO1_PCIE_LC_CNTL5_ADDRESS                    0x1a5462dcUL
#define SMN_FUNC6_PCIE1NBIO0_PCIE_LC_CNTL5_ADDRESS                    0x1a4462dcUL
#define SMN_FUNC6_PCIE1NBIO1_PCIE_LC_CNTL5_ADDRESS                    0x1a6462dcUL
#define SMN_FUNC6_PCIE2NBIO0_PCIE_LC_CNTL5_ADDRESS                    0x1a7462dcUL
#define SMN_FUNC6_PCIE2NBIO1_PCIE_LC_CNTL5_ADDRESS                    0x1a9462dcUL
#define SMN_FUNC6_PCIE3NBIO0_PCIE_LC_CNTL5_ADDRESS                    0x1a8462dcUL
#define SMN_FUNC6_PCIE3NBIO1_PCIE_LC_CNTL5_ADDRESS                    0x1aa462dcUL
#define SMN_FUNC6_PCIE5NBIO0_PCIE_LC_CNTL5_ADDRESS                    0x1ab462dcUL
#define SMN_FUNC6_PCIE5NBIO1_PCIE_LC_CNTL5_ADDRESS                    0x1ad462dcUL
#define SMN_FUNC7_PCIE0NBIO0_PCIE_LC_CNTL5_ADDRESS                    0x1a3472dcUL
#define SMN_FUNC7_PCIE0NBIO1_PCIE_LC_CNTL5_ADDRESS                    0x1a5472dcUL
#define SMN_FUNC7_PCIE1NBIO0_PCIE_LC_CNTL5_ADDRESS                    0x1a4472dcUL
#define SMN_FUNC7_PCIE1NBIO1_PCIE_LC_CNTL5_ADDRESS                    0x1a6472dcUL
#define SMN_FUNC7_PCIE2NBIO0_PCIE_LC_CNTL5_ADDRESS                    0x1a7472dcUL
#define SMN_FUNC7_PCIE2NBIO1_PCIE_LC_CNTL5_ADDRESS                    0x1a9472dcUL
#define SMN_FUNC7_PCIE3NBIO0_PCIE_LC_CNTL5_ADDRESS                    0x1a8472dcUL
#define SMN_FUNC7_PCIE3NBIO1_PCIE_LC_CNTL5_ADDRESS                    0x1aa472dcUL
#define SMN_FUNC7_PCIE5NBIO0_PCIE_LC_CNTL5_ADDRESS                    0x1ab472dcUL
#define SMN_FUNC7_PCIE5NBIO1_PCIE_LC_CNTL5_ADDRESS                    0x1ad472dcUL
#define SMN_FUNC8_PCIE0NBIO0_PCIE_LC_CNTL5_ADDRESS                    0x1a3482dcUL
#define SMN_FUNC8_PCIE0NBIO1_PCIE_LC_CNTL5_ADDRESS                    0x1a5482dcUL
#define SMN_FUNC8_PCIE1NBIO0_PCIE_LC_CNTL5_ADDRESS                    0x1a4482dcUL
#define SMN_FUNC8_PCIE1NBIO1_PCIE_LC_CNTL5_ADDRESS                    0x1a6482dcUL
#define SMN_FUNC8_PCIE2NBIO0_PCIE_LC_CNTL5_ADDRESS                    0x1a7482dcUL
#define SMN_FUNC8_PCIE2NBIO1_PCIE_LC_CNTL5_ADDRESS                    0x1a9482dcUL
#define SMN_FUNC8_PCIE3NBIO0_PCIE_LC_CNTL5_ADDRESS                    0x1a8482dcUL
#define SMN_FUNC8_PCIE3NBIO1_PCIE_LC_CNTL5_ADDRESS                    0x1aa482dcUL


/***********************************************************
* Register Name : PCIE_LC_STATE0
************************************************************/

#define PCIE_LC_STATE0_LC_CURRENT_STATE_OFFSET                 0
#define PCIE_LC_STATE0_LC_CURRENT_STATE_MASK                   0x3f

#define PCIE_LC_STATE0_Reserved_7_6_OFFSET                     6
#define PCIE_LC_STATE0_Reserved_7_6_MASK                       0xc0

#define PCIE_LC_STATE0_LC_PREV_STATE1_OFFSET                   8
#define PCIE_LC_STATE0_LC_PREV_STATE1_MASK                     0x3f00

#define PCIE_LC_STATE0_Reserved_15_14_OFFSET                   14
#define PCIE_LC_STATE0_Reserved_15_14_MASK                     0xc000

#define PCIE_LC_STATE0_LC_PREV_STATE2_OFFSET                   16
#define PCIE_LC_STATE0_LC_PREV_STATE2_MASK                     0x3f0000

#define PCIE_LC_STATE0_Reserved_23_22_OFFSET                   22
#define PCIE_LC_STATE0_Reserved_23_22_MASK                     0xc00000

#define PCIE_LC_STATE0_LC_PREV_STATE3_OFFSET                   24
#define PCIE_LC_STATE0_LC_PREV_STATE3_MASK                     0x3f000000

#define PCIE_LC_STATE0_Reserved_31_30_OFFSET                   30
#define PCIE_LC_STATE0_Reserved_31_30_MASK                     0xc0000000

typedef union {
  struct {
    UINT32                                    LC_CURRENT_STATE:6;
    UINT32                                        Reserved_7_6:2;
    UINT32                                      LC_PREV_STATE1:6;
    UINT32                                      Reserved_15_14:2;
    UINT32                                      LC_PREV_STATE2:6;
    UINT32                                      Reserved_23_22:2;
    UINT32                                      LC_PREV_STATE3:6;
    UINT32                                      Reserved_31_30:2;
  } Field;
  UINT32 Value;
} PCIE_LC_STATE0_STRUCT;

#define PCICFG_PCIEPORT_PCIE_LC_STATE0_OFFSET                       0x294
#define SMN_FUNC0_PCIE0NBIO0_PCIE_LC_STATE0_ADDRESS                   0x1a340294UL
#define SMN_FUNC0_PCIE0NBIO1_PCIE_LC_STATE0_ADDRESS                   0x1a540294UL
#define SMN_FUNC0_PCIE1NBIO0_PCIE_LC_STATE0_ADDRESS                   0x1a440294UL
#define SMN_FUNC0_PCIE1NBIO1_PCIE_LC_STATE0_ADDRESS                   0x1a640294UL
#define SMN_FUNC0_PCIE2NBIO0_PCIE_LC_STATE0_ADDRESS                   0x1a740294UL
#define SMN_FUNC0_PCIE2NBIO1_PCIE_LC_STATE0_ADDRESS                   0x1a940294UL
#define SMN_FUNC0_PCIE3NBIO0_PCIE_LC_STATE0_ADDRESS                   0x1a840294UL
#define SMN_FUNC0_PCIE3NBIO1_PCIE_LC_STATE0_ADDRESS                   0x1aa40294UL
#define SMN_FUNC0_PCIE5NBIO0_PCIE_LC_STATE0_ADDRESS                   0x1ab40294UL
#define SMN_FUNC0_PCIE5NBIO1_PCIE_LC_STATE0_ADDRESS                   0x1ad40294UL
#define SMN_FUNC1_PCIE0NBIO0_PCIE_LC_STATE0_ADDRESS                   0x1a341294UL
#define SMN_FUNC1_PCIE0NBIO1_PCIE_LC_STATE0_ADDRESS                   0x1a541294UL
#define SMN_FUNC1_PCIE1NBIO0_PCIE_LC_STATE0_ADDRESS                   0x1a441294UL
#define SMN_FUNC1_PCIE1NBIO1_PCIE_LC_STATE0_ADDRESS                   0x1a641294UL
#define SMN_FUNC1_PCIE2NBIO0_PCIE_LC_STATE0_ADDRESS                   0x1a741294UL
#define SMN_FUNC1_PCIE2NBIO1_PCIE_LC_STATE0_ADDRESS                   0x1a941294UL
#define SMN_FUNC1_PCIE3NBIO0_PCIE_LC_STATE0_ADDRESS                   0x1a841294UL
#define SMN_FUNC1_PCIE3NBIO1_PCIE_LC_STATE0_ADDRESS                   0x1aa41294UL
#define SMN_FUNC1_PCIE5NBIO0_PCIE_LC_STATE0_ADDRESS                   0x1ab41294UL
#define SMN_FUNC1_PCIE5NBIO1_PCIE_LC_STATE0_ADDRESS                   0x1ad41294UL
#define SMN_FUNC2_PCIE0NBIO0_PCIE_LC_STATE0_ADDRESS                   0x1a342294UL
#define SMN_FUNC2_PCIE0NBIO1_PCIE_LC_STATE0_ADDRESS                   0x1a542294UL
#define SMN_FUNC2_PCIE1NBIO0_PCIE_LC_STATE0_ADDRESS                   0x1a442294UL
#define SMN_FUNC2_PCIE1NBIO1_PCIE_LC_STATE0_ADDRESS                   0x1a642294UL
#define SMN_FUNC2_PCIE2NBIO0_PCIE_LC_STATE0_ADDRESS                   0x1a742294UL
#define SMN_FUNC2_PCIE2NBIO1_PCIE_LC_STATE0_ADDRESS                   0x1a942294UL
#define SMN_FUNC2_PCIE3NBIO0_PCIE_LC_STATE0_ADDRESS                   0x1a842294UL
#define SMN_FUNC2_PCIE3NBIO1_PCIE_LC_STATE0_ADDRESS                   0x1aa42294UL
#define SMN_FUNC2_PCIE5NBIO0_PCIE_LC_STATE0_ADDRESS                   0x1ab42294UL
#define SMN_FUNC2_PCIE5NBIO1_PCIE_LC_STATE0_ADDRESS                   0x1ad42294UL
#define SMN_FUNC3_PCIE0NBIO0_PCIE_LC_STATE0_ADDRESS                   0x1a343294UL
#define SMN_FUNC3_PCIE0NBIO1_PCIE_LC_STATE0_ADDRESS                   0x1a543294UL
#define SMN_FUNC3_PCIE1NBIO0_PCIE_LC_STATE0_ADDRESS                   0x1a443294UL
#define SMN_FUNC3_PCIE1NBIO1_PCIE_LC_STATE0_ADDRESS                   0x1a643294UL
#define SMN_FUNC3_PCIE2NBIO0_PCIE_LC_STATE0_ADDRESS                   0x1a743294UL
#define SMN_FUNC3_PCIE2NBIO1_PCIE_LC_STATE0_ADDRESS                   0x1a943294UL
#define SMN_FUNC3_PCIE3NBIO0_PCIE_LC_STATE0_ADDRESS                   0x1a843294UL
#define SMN_FUNC3_PCIE3NBIO1_PCIE_LC_STATE0_ADDRESS                   0x1aa43294UL
#define SMN_FUNC3_PCIE5NBIO0_PCIE_LC_STATE0_ADDRESS                   0x1ab43294UL
#define SMN_FUNC3_PCIE5NBIO1_PCIE_LC_STATE0_ADDRESS                   0x1ad43294UL
#define SMN_FUNC4_PCIE0NBIO0_PCIE_LC_STATE0_ADDRESS                   0x1a344294UL
#define SMN_FUNC4_PCIE0NBIO1_PCIE_LC_STATE0_ADDRESS                   0x1a544294UL
#define SMN_FUNC4_PCIE1NBIO0_PCIE_LC_STATE0_ADDRESS                   0x1a444294UL
#define SMN_FUNC4_PCIE1NBIO1_PCIE_LC_STATE0_ADDRESS                   0x1a644294UL
#define SMN_FUNC4_PCIE2NBIO0_PCIE_LC_STATE0_ADDRESS                   0x1a744294UL
#define SMN_FUNC4_PCIE2NBIO1_PCIE_LC_STATE0_ADDRESS                   0x1a944294UL
#define SMN_FUNC4_PCIE3NBIO0_PCIE_LC_STATE0_ADDRESS                   0x1a844294UL
#define SMN_FUNC4_PCIE3NBIO1_PCIE_LC_STATE0_ADDRESS                   0x1aa44294UL
#define SMN_FUNC4_PCIE5NBIO0_PCIE_LC_STATE0_ADDRESS                   0x1ab44294UL
#define SMN_FUNC4_PCIE5NBIO1_PCIE_LC_STATE0_ADDRESS                   0x1ad44294UL
#define SMN_FUNC5_PCIE0NBIO0_PCIE_LC_STATE0_ADDRESS                   0x1a345294UL
#define SMN_FUNC5_PCIE0NBIO1_PCIE_LC_STATE0_ADDRESS                   0x1a545294UL
#define SMN_FUNC5_PCIE1NBIO0_PCIE_LC_STATE0_ADDRESS                   0x1a445294UL
#define SMN_FUNC5_PCIE1NBIO1_PCIE_LC_STATE0_ADDRESS                   0x1a645294UL
#define SMN_FUNC5_PCIE2NBIO0_PCIE_LC_STATE0_ADDRESS                   0x1a745294UL
#define SMN_FUNC5_PCIE2NBIO1_PCIE_LC_STATE0_ADDRESS                   0x1a945294UL
#define SMN_FUNC5_PCIE3NBIO0_PCIE_LC_STATE0_ADDRESS                   0x1a845294UL
#define SMN_FUNC5_PCIE3NBIO1_PCIE_LC_STATE0_ADDRESS                   0x1aa45294UL
#define SMN_FUNC5_PCIE5NBIO0_PCIE_LC_STATE0_ADDRESS                   0x1ab45294UL
#define SMN_FUNC5_PCIE5NBIO1_PCIE_LC_STATE0_ADDRESS                   0x1ad45294UL
#define SMN_FUNC6_PCIE0NBIO0_PCIE_LC_STATE0_ADDRESS                   0x1a346294UL
#define SMN_FUNC6_PCIE0NBIO1_PCIE_LC_STATE0_ADDRESS                   0x1a546294UL
#define SMN_FUNC6_PCIE1NBIO0_PCIE_LC_STATE0_ADDRESS                   0x1a446294UL
#define SMN_FUNC6_PCIE1NBIO1_PCIE_LC_STATE0_ADDRESS                   0x1a646294UL
#define SMN_FUNC6_PCIE2NBIO0_PCIE_LC_STATE0_ADDRESS                   0x1a746294UL
#define SMN_FUNC6_PCIE2NBIO1_PCIE_LC_STATE0_ADDRESS                   0x1a946294UL
#define SMN_FUNC6_PCIE3NBIO0_PCIE_LC_STATE0_ADDRESS                   0x1a846294UL
#define SMN_FUNC6_PCIE3NBIO1_PCIE_LC_STATE0_ADDRESS                   0x1aa46294UL
#define SMN_FUNC6_PCIE5NBIO0_PCIE_LC_STATE0_ADDRESS                   0x1ab46294UL
#define SMN_FUNC6_PCIE5NBIO1_PCIE_LC_STATE0_ADDRESS                   0x1ad46294UL
#define SMN_FUNC7_PCIE0NBIO0_PCIE_LC_STATE0_ADDRESS                   0x1a347294UL
#define SMN_FUNC7_PCIE0NBIO1_PCIE_LC_STATE0_ADDRESS                   0x1a547294UL
#define SMN_FUNC7_PCIE1NBIO0_PCIE_LC_STATE0_ADDRESS                   0x1a447294UL
#define SMN_FUNC7_PCIE1NBIO1_PCIE_LC_STATE0_ADDRESS                   0x1a647294UL
#define SMN_FUNC7_PCIE2NBIO0_PCIE_LC_STATE0_ADDRESS                   0x1a747294UL
#define SMN_FUNC7_PCIE2NBIO1_PCIE_LC_STATE0_ADDRESS                   0x1a947294UL
#define SMN_FUNC7_PCIE3NBIO0_PCIE_LC_STATE0_ADDRESS                   0x1a847294UL
#define SMN_FUNC7_PCIE3NBIO1_PCIE_LC_STATE0_ADDRESS                   0x1aa47294UL
#define SMN_FUNC7_PCIE5NBIO0_PCIE_LC_STATE0_ADDRESS                   0x1ab47294UL
#define SMN_FUNC7_PCIE5NBIO1_PCIE_LC_STATE0_ADDRESS                   0x1ad47294UL
#define SMN_FUNC8_PCIE0NBIO0_PCIE_LC_STATE0_ADDRESS                   0x1a348294UL
#define SMN_FUNC8_PCIE0NBIO1_PCIE_LC_STATE0_ADDRESS                   0x1a548294UL
#define SMN_FUNC8_PCIE1NBIO0_PCIE_LC_STATE0_ADDRESS                   0x1a448294UL
#define SMN_FUNC8_PCIE1NBIO1_PCIE_LC_STATE0_ADDRESS                   0x1a648294UL
#define SMN_FUNC8_PCIE2NBIO0_PCIE_LC_STATE0_ADDRESS                   0x1a748294UL
#define SMN_FUNC8_PCIE2NBIO1_PCIE_LC_STATE0_ADDRESS                   0x1a948294UL
#define SMN_FUNC8_PCIE3NBIO0_PCIE_LC_STATE0_ADDRESS                   0x1a848294UL
#define SMN_FUNC8_PCIE3NBIO1_PCIE_LC_STATE0_ADDRESS                   0x1aa48294UL


/***********************************************************
* Register Name : PCIE_LC_STATE1
************************************************************/

#define PCIE_LC_STATE1_LC_PREV_STATE4_OFFSET                   0
#define PCIE_LC_STATE1_LC_PREV_STATE4_MASK                     0x3f

#define PCIE_LC_STATE1_Reserved_7_6_OFFSET                     6
#define PCIE_LC_STATE1_Reserved_7_6_MASK                       0xc0

#define PCIE_LC_STATE1_LC_PREV_STATE5_OFFSET                   8
#define PCIE_LC_STATE1_LC_PREV_STATE5_MASK                     0x3f00

#define PCIE_LC_STATE1_Reserved_15_14_OFFSET                   14
#define PCIE_LC_STATE1_Reserved_15_14_MASK                     0xc000

#define PCIE_LC_STATE1_LC_PREV_STATE6_OFFSET                   16
#define PCIE_LC_STATE1_LC_PREV_STATE6_MASK                     0x3f0000

#define PCIE_LC_STATE1_Reserved_23_22_OFFSET                   22
#define PCIE_LC_STATE1_Reserved_23_22_MASK                     0xc00000

#define PCIE_LC_STATE1_LC_PREV_STATE7_OFFSET                   24
#define PCIE_LC_STATE1_LC_PREV_STATE7_MASK                     0x3f000000

#define PCIE_LC_STATE1_Reserved_31_30_OFFSET                   30
#define PCIE_LC_STATE1_Reserved_31_30_MASK                     0xc0000000

typedef union {
  struct {
    UINT32                                      LC_PREV_STATE4:6;
    UINT32                                        Reserved_7_6:2;
    UINT32                                      LC_PREV_STATE5:6;
    UINT32                                      Reserved_15_14:2;
    UINT32                                      LC_PREV_STATE6:6;
    UINT32                                      Reserved_23_22:2;
    UINT32                                      LC_PREV_STATE7:6;
    UINT32                                      Reserved_31_30:2;
  } Field;
  UINT32 Value;
} PCIE_LC_STATE1_STRUCT;

#define PCICFG_PCIEPORT_PCIE_LC_STATE1_OFFSET                       0x298
#define SMN_FUNC0_PCIE0NBIO0_PCIE_LC_STATE1_ADDRESS                   0x1a340298UL
#define SMN_FUNC0_PCIE0NBIO1_PCIE_LC_STATE1_ADDRESS                   0x1a540298UL
#define SMN_FUNC0_PCIE1NBIO0_PCIE_LC_STATE1_ADDRESS                   0x1a440298UL
#define SMN_FUNC0_PCIE1NBIO1_PCIE_LC_STATE1_ADDRESS                   0x1a640298UL
#define SMN_FUNC0_PCIE2NBIO0_PCIE_LC_STATE1_ADDRESS                   0x1a740298UL
#define SMN_FUNC0_PCIE2NBIO1_PCIE_LC_STATE1_ADDRESS                   0x1a940298UL
#define SMN_FUNC0_PCIE3NBIO0_PCIE_LC_STATE1_ADDRESS                   0x1a840298UL
#define SMN_FUNC0_PCIE3NBIO1_PCIE_LC_STATE1_ADDRESS                   0x1aa40298UL
#define SMN_FUNC0_PCIE5NBIO0_PCIE_LC_STATE1_ADDRESS                   0x1ab40298UL
#define SMN_FUNC0_PCIE5NBIO1_PCIE_LC_STATE1_ADDRESS                   0x1ad40298UL
#define SMN_FUNC1_PCIE0NBIO0_PCIE_LC_STATE1_ADDRESS                   0x1a341298UL
#define SMN_FUNC1_PCIE0NBIO1_PCIE_LC_STATE1_ADDRESS                   0x1a541298UL
#define SMN_FUNC1_PCIE1NBIO0_PCIE_LC_STATE1_ADDRESS                   0x1a441298UL
#define SMN_FUNC1_PCIE1NBIO1_PCIE_LC_STATE1_ADDRESS                   0x1a641298UL
#define SMN_FUNC1_PCIE2NBIO0_PCIE_LC_STATE1_ADDRESS                   0x1a741298UL
#define SMN_FUNC1_PCIE2NBIO1_PCIE_LC_STATE1_ADDRESS                   0x1a941298UL
#define SMN_FUNC1_PCIE3NBIO0_PCIE_LC_STATE1_ADDRESS                   0x1a841298UL
#define SMN_FUNC1_PCIE3NBIO1_PCIE_LC_STATE1_ADDRESS                   0x1aa41298UL
#define SMN_FUNC1_PCIE5NBIO0_PCIE_LC_STATE1_ADDRESS                   0x1ab41298UL
#define SMN_FUNC1_PCIE5NBIO1_PCIE_LC_STATE1_ADDRESS                   0x1ad41298UL
#define SMN_FUNC2_PCIE0NBIO0_PCIE_LC_STATE1_ADDRESS                   0x1a342298UL
#define SMN_FUNC2_PCIE0NBIO1_PCIE_LC_STATE1_ADDRESS                   0x1a542298UL
#define SMN_FUNC2_PCIE1NBIO0_PCIE_LC_STATE1_ADDRESS                   0x1a442298UL
#define SMN_FUNC2_PCIE1NBIO1_PCIE_LC_STATE1_ADDRESS                   0x1a642298UL
#define SMN_FUNC2_PCIE2NBIO0_PCIE_LC_STATE1_ADDRESS                   0x1a742298UL
#define SMN_FUNC2_PCIE2NBIO1_PCIE_LC_STATE1_ADDRESS                   0x1a942298UL
#define SMN_FUNC2_PCIE3NBIO0_PCIE_LC_STATE1_ADDRESS                   0x1a842298UL
#define SMN_FUNC2_PCIE3NBIO1_PCIE_LC_STATE1_ADDRESS                   0x1aa42298UL
#define SMN_FUNC2_PCIE5NBIO0_PCIE_LC_STATE1_ADDRESS                   0x1ab42298UL
#define SMN_FUNC2_PCIE5NBIO1_PCIE_LC_STATE1_ADDRESS                   0x1ad42298UL
#define SMN_FUNC3_PCIE0NBIO0_PCIE_LC_STATE1_ADDRESS                   0x1a343298UL
#define SMN_FUNC3_PCIE0NBIO1_PCIE_LC_STATE1_ADDRESS                   0x1a543298UL
#define SMN_FUNC3_PCIE1NBIO0_PCIE_LC_STATE1_ADDRESS                   0x1a443298UL
#define SMN_FUNC3_PCIE1NBIO1_PCIE_LC_STATE1_ADDRESS                   0x1a643298UL
#define SMN_FUNC3_PCIE2NBIO0_PCIE_LC_STATE1_ADDRESS                   0x1a743298UL
#define SMN_FUNC3_PCIE2NBIO1_PCIE_LC_STATE1_ADDRESS                   0x1a943298UL
#define SMN_FUNC3_PCIE3NBIO0_PCIE_LC_STATE1_ADDRESS                   0x1a843298UL
#define SMN_FUNC3_PCIE3NBIO1_PCIE_LC_STATE1_ADDRESS                   0x1aa43298UL
#define SMN_FUNC3_PCIE5NBIO0_PCIE_LC_STATE1_ADDRESS                   0x1ab43298UL
#define SMN_FUNC3_PCIE5NBIO1_PCIE_LC_STATE1_ADDRESS                   0x1ad43298UL
#define SMN_FUNC4_PCIE0NBIO0_PCIE_LC_STATE1_ADDRESS                   0x1a344298UL
#define SMN_FUNC4_PCIE0NBIO1_PCIE_LC_STATE1_ADDRESS                   0x1a544298UL
#define SMN_FUNC4_PCIE1NBIO0_PCIE_LC_STATE1_ADDRESS                   0x1a444298UL
#define SMN_FUNC4_PCIE1NBIO1_PCIE_LC_STATE1_ADDRESS                   0x1a644298UL
#define SMN_FUNC4_PCIE2NBIO0_PCIE_LC_STATE1_ADDRESS                   0x1a744298UL
#define SMN_FUNC4_PCIE2NBIO1_PCIE_LC_STATE1_ADDRESS                   0x1a944298UL
#define SMN_FUNC4_PCIE3NBIO0_PCIE_LC_STATE1_ADDRESS                   0x1a844298UL
#define SMN_FUNC4_PCIE3NBIO1_PCIE_LC_STATE1_ADDRESS                   0x1aa44298UL
#define SMN_FUNC4_PCIE5NBIO0_PCIE_LC_STATE1_ADDRESS                   0x1ab44298UL
#define SMN_FUNC4_PCIE5NBIO1_PCIE_LC_STATE1_ADDRESS                   0x1ad44298UL
#define SMN_FUNC5_PCIE0NBIO0_PCIE_LC_STATE1_ADDRESS                   0x1a345298UL
#define SMN_FUNC5_PCIE0NBIO1_PCIE_LC_STATE1_ADDRESS                   0x1a545298UL
#define SMN_FUNC5_PCIE1NBIO0_PCIE_LC_STATE1_ADDRESS                   0x1a445298UL
#define SMN_FUNC5_PCIE1NBIO1_PCIE_LC_STATE1_ADDRESS                   0x1a645298UL
#define SMN_FUNC5_PCIE2NBIO0_PCIE_LC_STATE1_ADDRESS                   0x1a745298UL
#define SMN_FUNC5_PCIE2NBIO1_PCIE_LC_STATE1_ADDRESS                   0x1a945298UL
#define SMN_FUNC5_PCIE3NBIO0_PCIE_LC_STATE1_ADDRESS                   0x1a845298UL
#define SMN_FUNC5_PCIE3NBIO1_PCIE_LC_STATE1_ADDRESS                   0x1aa45298UL
#define SMN_FUNC5_PCIE5NBIO0_PCIE_LC_STATE1_ADDRESS                   0x1ab45298UL
#define SMN_FUNC5_PCIE5NBIO1_PCIE_LC_STATE1_ADDRESS                   0x1ad45298UL
#define SMN_FUNC6_PCIE0NBIO0_PCIE_LC_STATE1_ADDRESS                   0x1a346298UL
#define SMN_FUNC6_PCIE0NBIO1_PCIE_LC_STATE1_ADDRESS                   0x1a546298UL
#define SMN_FUNC6_PCIE1NBIO0_PCIE_LC_STATE1_ADDRESS                   0x1a446298UL
#define SMN_FUNC6_PCIE1NBIO1_PCIE_LC_STATE1_ADDRESS                   0x1a646298UL
#define SMN_FUNC6_PCIE2NBIO0_PCIE_LC_STATE1_ADDRESS                   0x1a746298UL
#define SMN_FUNC6_PCIE2NBIO1_PCIE_LC_STATE1_ADDRESS                   0x1a946298UL
#define SMN_FUNC6_PCIE3NBIO0_PCIE_LC_STATE1_ADDRESS                   0x1a846298UL
#define SMN_FUNC6_PCIE3NBIO1_PCIE_LC_STATE1_ADDRESS                   0x1aa46298UL
#define SMN_FUNC6_PCIE5NBIO0_PCIE_LC_STATE1_ADDRESS                   0x1ab46298UL
#define SMN_FUNC6_PCIE5NBIO1_PCIE_LC_STATE1_ADDRESS                   0x1ad46298UL
#define SMN_FUNC7_PCIE0NBIO0_PCIE_LC_STATE1_ADDRESS                   0x1a347298UL
#define SMN_FUNC7_PCIE0NBIO1_PCIE_LC_STATE1_ADDRESS                   0x1a547298UL
#define SMN_FUNC7_PCIE1NBIO0_PCIE_LC_STATE1_ADDRESS                   0x1a447298UL
#define SMN_FUNC7_PCIE1NBIO1_PCIE_LC_STATE1_ADDRESS                   0x1a647298UL
#define SMN_FUNC7_PCIE2NBIO0_PCIE_LC_STATE1_ADDRESS                   0x1a747298UL
#define SMN_FUNC7_PCIE2NBIO1_PCIE_LC_STATE1_ADDRESS                   0x1a947298UL
#define SMN_FUNC7_PCIE3NBIO0_PCIE_LC_STATE1_ADDRESS                   0x1a847298UL
#define SMN_FUNC7_PCIE3NBIO1_PCIE_LC_STATE1_ADDRESS                   0x1aa47298UL
#define SMN_FUNC7_PCIE5NBIO0_PCIE_LC_STATE1_ADDRESS                   0x1ab47298UL
#define SMN_FUNC7_PCIE5NBIO1_PCIE_LC_STATE1_ADDRESS                   0x1ad47298UL
#define SMN_FUNC8_PCIE0NBIO0_PCIE_LC_STATE1_ADDRESS                   0x1a348298UL
#define SMN_FUNC8_PCIE0NBIO1_PCIE_LC_STATE1_ADDRESS                   0x1a548298UL
#define SMN_FUNC8_PCIE1NBIO0_PCIE_LC_STATE1_ADDRESS                   0x1a448298UL
#define SMN_FUNC8_PCIE1NBIO1_PCIE_LC_STATE1_ADDRESS                   0x1a648298UL
#define SMN_FUNC8_PCIE2NBIO0_PCIE_LC_STATE1_ADDRESS                   0x1a748298UL
#define SMN_FUNC8_PCIE2NBIO1_PCIE_LC_STATE1_ADDRESS                   0x1a948298UL
#define SMN_FUNC8_PCIE3NBIO0_PCIE_LC_STATE1_ADDRESS                   0x1a848298UL
#define SMN_FUNC8_PCIE3NBIO1_PCIE_LC_STATE1_ADDRESS                   0x1aa48298UL


/***********************************************************
* Register Name : PCIE_LC_STATE2
************************************************************/

#define PCIE_LC_STATE2_LC_PREV_STATE8_OFFSET                   0
#define PCIE_LC_STATE2_LC_PREV_STATE8_MASK                     0x3f

#define PCIE_LC_STATE2_Reserved_7_6_OFFSET                     6
#define PCIE_LC_STATE2_Reserved_7_6_MASK                       0xc0

#define PCIE_LC_STATE2_LC_PREV_STATE9_OFFSET                   8
#define PCIE_LC_STATE2_LC_PREV_STATE9_MASK                     0x3f00

#define PCIE_LC_STATE2_Reserved_15_14_OFFSET                   14
#define PCIE_LC_STATE2_Reserved_15_14_MASK                     0xc000

#define PCIE_LC_STATE2_LC_PREV_STATE10_OFFSET                  16
#define PCIE_LC_STATE2_LC_PREV_STATE10_MASK                    0x3f0000

#define PCIE_LC_STATE2_Reserved_23_22_OFFSET                   22
#define PCIE_LC_STATE2_Reserved_23_22_MASK                     0xc00000

#define PCIE_LC_STATE2_LC_PREV_STATE11_OFFSET                  24
#define PCIE_LC_STATE2_LC_PREV_STATE11_MASK                    0x3f000000

#define PCIE_LC_STATE2_Reserved_31_30_OFFSET                   30
#define PCIE_LC_STATE2_Reserved_31_30_MASK                     0xc0000000

typedef union {
  struct {
    UINT32                                      LC_PREV_STATE8:6;
    UINT32                                        Reserved_7_6:2;
    UINT32                                      LC_PREV_STATE9:6;
    UINT32                                      Reserved_15_14:2;
    UINT32                                     LC_PREV_STATE10:6;
    UINT32                                      Reserved_23_22:2;
    UINT32                                     LC_PREV_STATE11:6;
    UINT32                                      Reserved_31_30:2;
  } Field;
  UINT32 Value;
} PCIE_LC_STATE2_STRUCT;

#define PCICFG_PCIEPORT_PCIE_LC_STATE2_OFFSET                       0x29c
#define SMN_FUNC0_PCIE0NBIO0_PCIE_LC_STATE2_ADDRESS                   0x1a34029cUL
#define SMN_FUNC0_PCIE0NBIO1_PCIE_LC_STATE2_ADDRESS                   0x1a54029cUL
#define SMN_FUNC0_PCIE1NBIO0_PCIE_LC_STATE2_ADDRESS                   0x1a44029cUL
#define SMN_FUNC0_PCIE1NBIO1_PCIE_LC_STATE2_ADDRESS                   0x1a64029cUL
#define SMN_FUNC0_PCIE2NBIO0_PCIE_LC_STATE2_ADDRESS                   0x1a74029cUL
#define SMN_FUNC0_PCIE2NBIO1_PCIE_LC_STATE2_ADDRESS                   0x1a94029cUL
#define SMN_FUNC0_PCIE3NBIO0_PCIE_LC_STATE2_ADDRESS                   0x1a84029cUL
#define SMN_FUNC0_PCIE3NBIO1_PCIE_LC_STATE2_ADDRESS                   0x1aa4029cUL
#define SMN_FUNC0_PCIE5NBIO0_PCIE_LC_STATE2_ADDRESS                   0x1ab4029cUL
#define SMN_FUNC0_PCIE5NBIO1_PCIE_LC_STATE2_ADDRESS                   0x1ad4029cUL
#define SMN_FUNC1_PCIE0NBIO0_PCIE_LC_STATE2_ADDRESS                   0x1a34129cUL
#define SMN_FUNC1_PCIE0NBIO1_PCIE_LC_STATE2_ADDRESS                   0x1a54129cUL
#define SMN_FUNC1_PCIE1NBIO0_PCIE_LC_STATE2_ADDRESS                   0x1a44129cUL
#define SMN_FUNC1_PCIE1NBIO1_PCIE_LC_STATE2_ADDRESS                   0x1a64129cUL
#define SMN_FUNC1_PCIE2NBIO0_PCIE_LC_STATE2_ADDRESS                   0x1a74129cUL
#define SMN_FUNC1_PCIE2NBIO1_PCIE_LC_STATE2_ADDRESS                   0x1a94129cUL
#define SMN_FUNC1_PCIE3NBIO0_PCIE_LC_STATE2_ADDRESS                   0x1a84129cUL
#define SMN_FUNC1_PCIE3NBIO1_PCIE_LC_STATE2_ADDRESS                   0x1aa4129cUL
#define SMN_FUNC1_PCIE5NBIO0_PCIE_LC_STATE2_ADDRESS                   0x1ab4129cUL
#define SMN_FUNC1_PCIE5NBIO1_PCIE_LC_STATE2_ADDRESS                   0x1ad4129cUL
#define SMN_FUNC2_PCIE0NBIO0_PCIE_LC_STATE2_ADDRESS                   0x1a34229cUL
#define SMN_FUNC2_PCIE0NBIO1_PCIE_LC_STATE2_ADDRESS                   0x1a54229cUL
#define SMN_FUNC2_PCIE1NBIO0_PCIE_LC_STATE2_ADDRESS                   0x1a44229cUL
#define SMN_FUNC2_PCIE1NBIO1_PCIE_LC_STATE2_ADDRESS                   0x1a64229cUL
#define SMN_FUNC2_PCIE2NBIO0_PCIE_LC_STATE2_ADDRESS                   0x1a74229cUL
#define SMN_FUNC2_PCIE2NBIO1_PCIE_LC_STATE2_ADDRESS                   0x1a94229cUL
#define SMN_FUNC2_PCIE3NBIO0_PCIE_LC_STATE2_ADDRESS                   0x1a84229cUL
#define SMN_FUNC2_PCIE3NBIO1_PCIE_LC_STATE2_ADDRESS                   0x1aa4229cUL
#define SMN_FUNC2_PCIE5NBIO0_PCIE_LC_STATE2_ADDRESS                   0x1ab4229cUL
#define SMN_FUNC2_PCIE5NBIO1_PCIE_LC_STATE2_ADDRESS                   0x1ad4229cUL
#define SMN_FUNC3_PCIE0NBIO0_PCIE_LC_STATE2_ADDRESS                   0x1a34329cUL
#define SMN_FUNC3_PCIE0NBIO1_PCIE_LC_STATE2_ADDRESS                   0x1a54329cUL
#define SMN_FUNC3_PCIE1NBIO0_PCIE_LC_STATE2_ADDRESS                   0x1a44329cUL
#define SMN_FUNC3_PCIE1NBIO1_PCIE_LC_STATE2_ADDRESS                   0x1a64329cUL
#define SMN_FUNC3_PCIE2NBIO0_PCIE_LC_STATE2_ADDRESS                   0x1a74329cUL
#define SMN_FUNC3_PCIE2NBIO1_PCIE_LC_STATE2_ADDRESS                   0x1a94329cUL
#define SMN_FUNC3_PCIE3NBIO0_PCIE_LC_STATE2_ADDRESS                   0x1a84329cUL
#define SMN_FUNC3_PCIE3NBIO1_PCIE_LC_STATE2_ADDRESS                   0x1aa4329cUL
#define SMN_FUNC3_PCIE5NBIO0_PCIE_LC_STATE2_ADDRESS                   0x1ab4329cUL
#define SMN_FUNC3_PCIE5NBIO1_PCIE_LC_STATE2_ADDRESS                   0x1ad4329cUL
#define SMN_FUNC4_PCIE0NBIO0_PCIE_LC_STATE2_ADDRESS                   0x1a34429cUL
#define SMN_FUNC4_PCIE0NBIO1_PCIE_LC_STATE2_ADDRESS                   0x1a54429cUL
#define SMN_FUNC4_PCIE1NBIO0_PCIE_LC_STATE2_ADDRESS                   0x1a44429cUL
#define SMN_FUNC4_PCIE1NBIO1_PCIE_LC_STATE2_ADDRESS                   0x1a64429cUL
#define SMN_FUNC4_PCIE2NBIO0_PCIE_LC_STATE2_ADDRESS                   0x1a74429cUL
#define SMN_FUNC4_PCIE2NBIO1_PCIE_LC_STATE2_ADDRESS                   0x1a94429cUL
#define SMN_FUNC4_PCIE3NBIO0_PCIE_LC_STATE2_ADDRESS                   0x1a84429cUL
#define SMN_FUNC4_PCIE3NBIO1_PCIE_LC_STATE2_ADDRESS                   0x1aa4429cUL
#define SMN_FUNC4_PCIE5NBIO0_PCIE_LC_STATE2_ADDRESS                   0x1ab4429cUL
#define SMN_FUNC4_PCIE5NBIO1_PCIE_LC_STATE2_ADDRESS                   0x1ad4429cUL
#define SMN_FUNC5_PCIE0NBIO0_PCIE_LC_STATE2_ADDRESS                   0x1a34529cUL
#define SMN_FUNC5_PCIE0NBIO1_PCIE_LC_STATE2_ADDRESS                   0x1a54529cUL
#define SMN_FUNC5_PCIE1NBIO0_PCIE_LC_STATE2_ADDRESS                   0x1a44529cUL
#define SMN_FUNC5_PCIE1NBIO1_PCIE_LC_STATE2_ADDRESS                   0x1a64529cUL
#define SMN_FUNC5_PCIE2NBIO0_PCIE_LC_STATE2_ADDRESS                   0x1a74529cUL
#define SMN_FUNC5_PCIE2NBIO1_PCIE_LC_STATE2_ADDRESS                   0x1a94529cUL
#define SMN_FUNC5_PCIE3NBIO0_PCIE_LC_STATE2_ADDRESS                   0x1a84529cUL
#define SMN_FUNC5_PCIE3NBIO1_PCIE_LC_STATE2_ADDRESS                   0x1aa4529cUL
#define SMN_FUNC5_PCIE5NBIO0_PCIE_LC_STATE2_ADDRESS                   0x1ab4529cUL
#define SMN_FUNC5_PCIE5NBIO1_PCIE_LC_STATE2_ADDRESS                   0x1ad4529cUL
#define SMN_FUNC6_PCIE0NBIO0_PCIE_LC_STATE2_ADDRESS                   0x1a34629cUL
#define SMN_FUNC6_PCIE0NBIO1_PCIE_LC_STATE2_ADDRESS                   0x1a54629cUL
#define SMN_FUNC6_PCIE1NBIO0_PCIE_LC_STATE2_ADDRESS                   0x1a44629cUL
#define SMN_FUNC6_PCIE1NBIO1_PCIE_LC_STATE2_ADDRESS                   0x1a64629cUL
#define SMN_FUNC6_PCIE2NBIO0_PCIE_LC_STATE2_ADDRESS                   0x1a74629cUL
#define SMN_FUNC6_PCIE2NBIO1_PCIE_LC_STATE2_ADDRESS                   0x1a94629cUL
#define SMN_FUNC6_PCIE3NBIO0_PCIE_LC_STATE2_ADDRESS                   0x1a84629cUL
#define SMN_FUNC6_PCIE3NBIO1_PCIE_LC_STATE2_ADDRESS                   0x1aa4629cUL
#define SMN_FUNC6_PCIE5NBIO0_PCIE_LC_STATE2_ADDRESS                   0x1ab4629cUL
#define SMN_FUNC6_PCIE5NBIO1_PCIE_LC_STATE2_ADDRESS                   0x1ad4629cUL
#define SMN_FUNC7_PCIE0NBIO0_PCIE_LC_STATE2_ADDRESS                   0x1a34729cUL
#define SMN_FUNC7_PCIE0NBIO1_PCIE_LC_STATE2_ADDRESS                   0x1a54729cUL
#define SMN_FUNC7_PCIE1NBIO0_PCIE_LC_STATE2_ADDRESS                   0x1a44729cUL
#define SMN_FUNC7_PCIE1NBIO1_PCIE_LC_STATE2_ADDRESS                   0x1a64729cUL
#define SMN_FUNC7_PCIE2NBIO0_PCIE_LC_STATE2_ADDRESS                   0x1a74729cUL
#define SMN_FUNC7_PCIE2NBIO1_PCIE_LC_STATE2_ADDRESS                   0x1a94729cUL
#define SMN_FUNC7_PCIE3NBIO0_PCIE_LC_STATE2_ADDRESS                   0x1a84729cUL
#define SMN_FUNC7_PCIE3NBIO1_PCIE_LC_STATE2_ADDRESS                   0x1aa4729cUL
#define SMN_FUNC7_PCIE5NBIO0_PCIE_LC_STATE2_ADDRESS                   0x1ab4729cUL
#define SMN_FUNC7_PCIE5NBIO1_PCIE_LC_STATE2_ADDRESS                   0x1ad4729cUL
#define SMN_FUNC8_PCIE0NBIO0_PCIE_LC_STATE2_ADDRESS                   0x1a34829cUL
#define SMN_FUNC8_PCIE0NBIO1_PCIE_LC_STATE2_ADDRESS                   0x1a54829cUL
#define SMN_FUNC8_PCIE1NBIO0_PCIE_LC_STATE2_ADDRESS                   0x1a44829cUL
#define SMN_FUNC8_PCIE1NBIO1_PCIE_LC_STATE2_ADDRESS                   0x1a64829cUL
#define SMN_FUNC8_PCIE2NBIO0_PCIE_LC_STATE2_ADDRESS                   0x1a74829cUL
#define SMN_FUNC8_PCIE2NBIO1_PCIE_LC_STATE2_ADDRESS                   0x1a94829cUL
#define SMN_FUNC8_PCIE3NBIO0_PCIE_LC_STATE2_ADDRESS                   0x1a84829cUL
#define SMN_FUNC8_PCIE3NBIO1_PCIE_LC_STATE2_ADDRESS                   0x1aa4829cUL


/***********************************************************
* Register Name : PCIE_LC_STATE3
************************************************************/

#define PCIE_LC_STATE3_LC_PREV_STATE12_OFFSET                  0
#define PCIE_LC_STATE3_LC_PREV_STATE12_MASK                    0x3f

#define PCIE_LC_STATE3_Reserved_7_6_OFFSET                     6
#define PCIE_LC_STATE3_Reserved_7_6_MASK                       0xc0

#define PCIE_LC_STATE3_LC_PREV_STATE13_OFFSET                  8
#define PCIE_LC_STATE3_LC_PREV_STATE13_MASK                    0x3f00

#define PCIE_LC_STATE3_Reserved_15_14_OFFSET                   14
#define PCIE_LC_STATE3_Reserved_15_14_MASK                     0xc000

#define PCIE_LC_STATE3_LC_PREV_STATE14_OFFSET                  16
#define PCIE_LC_STATE3_LC_PREV_STATE14_MASK                    0x3f0000

#define PCIE_LC_STATE3_Reserved_23_22_OFFSET                   22
#define PCIE_LC_STATE3_Reserved_23_22_MASK                     0xc00000

#define PCIE_LC_STATE3_LC_PREV_STATE15_OFFSET                  24
#define PCIE_LC_STATE3_LC_PREV_STATE15_MASK                    0x3f000000

#define PCIE_LC_STATE3_Reserved_31_30_OFFSET                   30
#define PCIE_LC_STATE3_Reserved_31_30_MASK                     0xc0000000

typedef union {
  struct {
    UINT32                                     LC_PREV_STATE12:6;
    UINT32                                        Reserved_7_6:2;
    UINT32                                     LC_PREV_STATE13:6;
    UINT32                                      Reserved_15_14:2;
    UINT32                                     LC_PREV_STATE14:6;
    UINT32                                      Reserved_23_22:2;
    UINT32                                     LC_PREV_STATE15:6;
    UINT32                                      Reserved_31_30:2;
  } Field;
  UINT32 Value;
} PCIE_LC_STATE3_STRUCT;

#define PCICFG_PCIEPORT_PCIE_LC_STATE3_OFFSET                       0x2a0
#define SMN_FUNC0_PCIE0NBIO0_PCIE_LC_STATE3_ADDRESS                   0x1a3402a0UL
#define SMN_FUNC0_PCIE0NBIO1_PCIE_LC_STATE3_ADDRESS                   0x1a5402a0UL
#define SMN_FUNC0_PCIE1NBIO0_PCIE_LC_STATE3_ADDRESS                   0x1a4402a0UL
#define SMN_FUNC0_PCIE1NBIO1_PCIE_LC_STATE3_ADDRESS                   0x1a6402a0UL
#define SMN_FUNC0_PCIE2NBIO0_PCIE_LC_STATE3_ADDRESS                   0x1a7402a0UL
#define SMN_FUNC0_PCIE2NBIO1_PCIE_LC_STATE3_ADDRESS                   0x1a9402a0UL
#define SMN_FUNC0_PCIE3NBIO0_PCIE_LC_STATE3_ADDRESS                   0x1a8402a0UL
#define SMN_FUNC0_PCIE3NBIO1_PCIE_LC_STATE3_ADDRESS                   0x1aa402a0UL
#define SMN_FUNC0_PCIE5NBIO0_PCIE_LC_STATE3_ADDRESS                   0x1ab402a0UL
#define SMN_FUNC0_PCIE5NBIO1_PCIE_LC_STATE3_ADDRESS                   0x1ad402a0UL
#define SMN_FUNC1_PCIE0NBIO0_PCIE_LC_STATE3_ADDRESS                   0x1a3412a0UL
#define SMN_FUNC1_PCIE0NBIO1_PCIE_LC_STATE3_ADDRESS                   0x1a5412a0UL
#define SMN_FUNC1_PCIE1NBIO0_PCIE_LC_STATE3_ADDRESS                   0x1a4412a0UL
#define SMN_FUNC1_PCIE1NBIO1_PCIE_LC_STATE3_ADDRESS                   0x1a6412a0UL
#define SMN_FUNC1_PCIE2NBIO0_PCIE_LC_STATE3_ADDRESS                   0x1a7412a0UL
#define SMN_FUNC1_PCIE2NBIO1_PCIE_LC_STATE3_ADDRESS                   0x1a9412a0UL
#define SMN_FUNC1_PCIE3NBIO0_PCIE_LC_STATE3_ADDRESS                   0x1a8412a0UL
#define SMN_FUNC1_PCIE3NBIO1_PCIE_LC_STATE3_ADDRESS                   0x1aa412a0UL
#define SMN_FUNC1_PCIE5NBIO0_PCIE_LC_STATE3_ADDRESS                   0x1ab412a0UL
#define SMN_FUNC1_PCIE5NBIO1_PCIE_LC_STATE3_ADDRESS                   0x1ad412a0UL
#define SMN_FUNC2_PCIE0NBIO0_PCIE_LC_STATE3_ADDRESS                   0x1a3422a0UL
#define SMN_FUNC2_PCIE0NBIO1_PCIE_LC_STATE3_ADDRESS                   0x1a5422a0UL
#define SMN_FUNC2_PCIE1NBIO0_PCIE_LC_STATE3_ADDRESS                   0x1a4422a0UL
#define SMN_FUNC2_PCIE1NBIO1_PCIE_LC_STATE3_ADDRESS                   0x1a6422a0UL
#define SMN_FUNC2_PCIE2NBIO0_PCIE_LC_STATE3_ADDRESS                   0x1a7422a0UL
#define SMN_FUNC2_PCIE2NBIO1_PCIE_LC_STATE3_ADDRESS                   0x1a9422a0UL
#define SMN_FUNC2_PCIE3NBIO0_PCIE_LC_STATE3_ADDRESS                   0x1a8422a0UL
#define SMN_FUNC2_PCIE3NBIO1_PCIE_LC_STATE3_ADDRESS                   0x1aa422a0UL
#define SMN_FUNC2_PCIE5NBIO0_PCIE_LC_STATE3_ADDRESS                   0x1ab422a0UL
#define SMN_FUNC2_PCIE5NBIO1_PCIE_LC_STATE3_ADDRESS                   0x1ad422a0UL
#define SMN_FUNC3_PCIE0NBIO0_PCIE_LC_STATE3_ADDRESS                   0x1a3432a0UL
#define SMN_FUNC3_PCIE0NBIO1_PCIE_LC_STATE3_ADDRESS                   0x1a5432a0UL
#define SMN_FUNC3_PCIE1NBIO0_PCIE_LC_STATE3_ADDRESS                   0x1a4432a0UL
#define SMN_FUNC3_PCIE1NBIO1_PCIE_LC_STATE3_ADDRESS                   0x1a6432a0UL
#define SMN_FUNC3_PCIE2NBIO0_PCIE_LC_STATE3_ADDRESS                   0x1a7432a0UL
#define SMN_FUNC3_PCIE2NBIO1_PCIE_LC_STATE3_ADDRESS                   0x1a9432a0UL
#define SMN_FUNC3_PCIE3NBIO0_PCIE_LC_STATE3_ADDRESS                   0x1a8432a0UL
#define SMN_FUNC3_PCIE3NBIO1_PCIE_LC_STATE3_ADDRESS                   0x1aa432a0UL
#define SMN_FUNC3_PCIE5NBIO0_PCIE_LC_STATE3_ADDRESS                   0x1ab432a0UL
#define SMN_FUNC3_PCIE5NBIO1_PCIE_LC_STATE3_ADDRESS                   0x1ad432a0UL
#define SMN_FUNC4_PCIE0NBIO0_PCIE_LC_STATE3_ADDRESS                   0x1a3442a0UL
#define SMN_FUNC4_PCIE0NBIO1_PCIE_LC_STATE3_ADDRESS                   0x1a5442a0UL
#define SMN_FUNC4_PCIE1NBIO0_PCIE_LC_STATE3_ADDRESS                   0x1a4442a0UL
#define SMN_FUNC4_PCIE1NBIO1_PCIE_LC_STATE3_ADDRESS                   0x1a6442a0UL
#define SMN_FUNC4_PCIE2NBIO0_PCIE_LC_STATE3_ADDRESS                   0x1a7442a0UL
#define SMN_FUNC4_PCIE2NBIO1_PCIE_LC_STATE3_ADDRESS                   0x1a9442a0UL
#define SMN_FUNC4_PCIE3NBIO0_PCIE_LC_STATE3_ADDRESS                   0x1a8442a0UL
#define SMN_FUNC4_PCIE3NBIO1_PCIE_LC_STATE3_ADDRESS                   0x1aa442a0UL
#define SMN_FUNC4_PCIE5NBIO0_PCIE_LC_STATE3_ADDRESS                   0x1ab442a0UL
#define SMN_FUNC4_PCIE5NBIO1_PCIE_LC_STATE3_ADDRESS                   0x1ad442a0UL
#define SMN_FUNC5_PCIE0NBIO0_PCIE_LC_STATE3_ADDRESS                   0x1a3452a0UL
#define SMN_FUNC5_PCIE0NBIO1_PCIE_LC_STATE3_ADDRESS                   0x1a5452a0UL
#define SMN_FUNC5_PCIE1NBIO0_PCIE_LC_STATE3_ADDRESS                   0x1a4452a0UL
#define SMN_FUNC5_PCIE1NBIO1_PCIE_LC_STATE3_ADDRESS                   0x1a6452a0UL
#define SMN_FUNC5_PCIE2NBIO0_PCIE_LC_STATE3_ADDRESS                   0x1a7452a0UL
#define SMN_FUNC5_PCIE2NBIO1_PCIE_LC_STATE3_ADDRESS                   0x1a9452a0UL
#define SMN_FUNC5_PCIE3NBIO0_PCIE_LC_STATE3_ADDRESS                   0x1a8452a0UL
#define SMN_FUNC5_PCIE3NBIO1_PCIE_LC_STATE3_ADDRESS                   0x1aa452a0UL
#define SMN_FUNC5_PCIE5NBIO0_PCIE_LC_STATE3_ADDRESS                   0x1ab452a0UL
#define SMN_FUNC5_PCIE5NBIO1_PCIE_LC_STATE3_ADDRESS                   0x1ad452a0UL
#define SMN_FUNC6_PCIE0NBIO0_PCIE_LC_STATE3_ADDRESS                   0x1a3462a0UL
#define SMN_FUNC6_PCIE0NBIO1_PCIE_LC_STATE3_ADDRESS                   0x1a5462a0UL
#define SMN_FUNC6_PCIE1NBIO0_PCIE_LC_STATE3_ADDRESS                   0x1a4462a0UL
#define SMN_FUNC6_PCIE1NBIO1_PCIE_LC_STATE3_ADDRESS                   0x1a6462a0UL
#define SMN_FUNC6_PCIE2NBIO0_PCIE_LC_STATE3_ADDRESS                   0x1a7462a0UL
#define SMN_FUNC6_PCIE2NBIO1_PCIE_LC_STATE3_ADDRESS                   0x1a9462a0UL
#define SMN_FUNC6_PCIE3NBIO0_PCIE_LC_STATE3_ADDRESS                   0x1a8462a0UL
#define SMN_FUNC6_PCIE3NBIO1_PCIE_LC_STATE3_ADDRESS                   0x1aa462a0UL
#define SMN_FUNC6_PCIE5NBIO0_PCIE_LC_STATE3_ADDRESS                   0x1ab462a0UL
#define SMN_FUNC6_PCIE5NBIO1_PCIE_LC_STATE3_ADDRESS                   0x1ad462a0UL
#define SMN_FUNC7_PCIE0NBIO0_PCIE_LC_STATE3_ADDRESS                   0x1a3472a0UL
#define SMN_FUNC7_PCIE0NBIO1_PCIE_LC_STATE3_ADDRESS                   0x1a5472a0UL
#define SMN_FUNC7_PCIE1NBIO0_PCIE_LC_STATE3_ADDRESS                   0x1a4472a0UL
#define SMN_FUNC7_PCIE1NBIO1_PCIE_LC_STATE3_ADDRESS                   0x1a6472a0UL
#define SMN_FUNC7_PCIE2NBIO0_PCIE_LC_STATE3_ADDRESS                   0x1a7472a0UL
#define SMN_FUNC7_PCIE2NBIO1_PCIE_LC_STATE3_ADDRESS                   0x1a9472a0UL
#define SMN_FUNC7_PCIE3NBIO0_PCIE_LC_STATE3_ADDRESS                   0x1a8472a0UL
#define SMN_FUNC7_PCIE3NBIO1_PCIE_LC_STATE3_ADDRESS                   0x1aa472a0UL
#define SMN_FUNC7_PCIE5NBIO0_PCIE_LC_STATE3_ADDRESS                   0x1ab472a0UL
#define SMN_FUNC7_PCIE5NBIO1_PCIE_LC_STATE3_ADDRESS                   0x1ad472a0UL
#define SMN_FUNC8_PCIE0NBIO0_PCIE_LC_STATE3_ADDRESS                   0x1a3482a0UL
#define SMN_FUNC8_PCIE0NBIO1_PCIE_LC_STATE3_ADDRESS                   0x1a5482a0UL
#define SMN_FUNC8_PCIE1NBIO0_PCIE_LC_STATE3_ADDRESS                   0x1a4482a0UL
#define SMN_FUNC8_PCIE1NBIO1_PCIE_LC_STATE3_ADDRESS                   0x1a6482a0UL
#define SMN_FUNC8_PCIE2NBIO0_PCIE_LC_STATE3_ADDRESS                   0x1a7482a0UL
#define SMN_FUNC8_PCIE2NBIO1_PCIE_LC_STATE3_ADDRESS                   0x1a9482a0UL
#define SMN_FUNC8_PCIE3NBIO0_PCIE_LC_STATE3_ADDRESS                   0x1a8482a0UL
#define SMN_FUNC8_PCIE3NBIO1_PCIE_LC_STATE3_ADDRESS                   0x1aa482a0UL


/***********************************************************
* Register Name : PCIE_LC_STATE4
************************************************************/

#define PCIE_LC_STATE4_LC_PREV_STATE16_OFFSET                  0
#define PCIE_LC_STATE4_LC_PREV_STATE16_MASK                    0x3f

#define PCIE_LC_STATE4_Reserved_7_6_OFFSET                     6
#define PCIE_LC_STATE4_Reserved_7_6_MASK                       0xc0

#define PCIE_LC_STATE4_LC_PREV_STATE17_OFFSET                  8
#define PCIE_LC_STATE4_LC_PREV_STATE17_MASK                    0x3f00

#define PCIE_LC_STATE4_Reserved_15_14_OFFSET                   14
#define PCIE_LC_STATE4_Reserved_15_14_MASK                     0xc000

#define PCIE_LC_STATE4_LC_PREV_STATE18_OFFSET                  16
#define PCIE_LC_STATE4_LC_PREV_STATE18_MASK                    0x3f0000

#define PCIE_LC_STATE4_Reserved_23_22_OFFSET                   22
#define PCIE_LC_STATE4_Reserved_23_22_MASK                     0xc00000

#define PCIE_LC_STATE4_LC_PREV_STATE19_OFFSET                  24
#define PCIE_LC_STATE4_LC_PREV_STATE19_MASK                    0x3f000000

#define PCIE_LC_STATE4_Reserved_31_30_OFFSET                   30
#define PCIE_LC_STATE4_Reserved_31_30_MASK                     0xc0000000

typedef union {
  struct {
    UINT32                                     LC_PREV_STATE16:6;
    UINT32                                        Reserved_7_6:2;
    UINT32                                     LC_PREV_STATE17:6;
    UINT32                                      Reserved_15_14:2;
    UINT32                                     LC_PREV_STATE18:6;
    UINT32                                      Reserved_23_22:2;
    UINT32                                     LC_PREV_STATE19:6;
    UINT32                                      Reserved_31_30:2;
  } Field;
  UINT32 Value;
} PCIE_LC_STATE4_STRUCT;

#define PCICFG_PCIEPORT_PCIE_LC_STATE4_OFFSET                       0x2a4
#define SMN_FUNC0_PCIE0NBIO0_PCIE_LC_STATE4_ADDRESS                   0x1a3402a4UL
#define SMN_FUNC0_PCIE0NBIO1_PCIE_LC_STATE4_ADDRESS                   0x1a5402a4UL
#define SMN_FUNC0_PCIE1NBIO0_PCIE_LC_STATE4_ADDRESS                   0x1a4402a4UL
#define SMN_FUNC0_PCIE1NBIO1_PCIE_LC_STATE4_ADDRESS                   0x1a6402a4UL
#define SMN_FUNC0_PCIE2NBIO0_PCIE_LC_STATE4_ADDRESS                   0x1a7402a4UL
#define SMN_FUNC0_PCIE2NBIO1_PCIE_LC_STATE4_ADDRESS                   0x1a9402a4UL
#define SMN_FUNC0_PCIE3NBIO0_PCIE_LC_STATE4_ADDRESS                   0x1a8402a4UL
#define SMN_FUNC0_PCIE3NBIO1_PCIE_LC_STATE4_ADDRESS                   0x1aa402a4UL
#define SMN_FUNC0_PCIE5NBIO0_PCIE_LC_STATE4_ADDRESS                   0x1ab402a4UL
#define SMN_FUNC0_PCIE5NBIO1_PCIE_LC_STATE4_ADDRESS                   0x1ad402a4UL
#define SMN_FUNC1_PCIE0NBIO0_PCIE_LC_STATE4_ADDRESS                   0x1a3412a4UL
#define SMN_FUNC1_PCIE0NBIO1_PCIE_LC_STATE4_ADDRESS                   0x1a5412a4UL
#define SMN_FUNC1_PCIE1NBIO0_PCIE_LC_STATE4_ADDRESS                   0x1a4412a4UL
#define SMN_FUNC1_PCIE1NBIO1_PCIE_LC_STATE4_ADDRESS                   0x1a6412a4UL
#define SMN_FUNC1_PCIE2NBIO0_PCIE_LC_STATE4_ADDRESS                   0x1a7412a4UL
#define SMN_FUNC1_PCIE2NBIO1_PCIE_LC_STATE4_ADDRESS                   0x1a9412a4UL
#define SMN_FUNC1_PCIE3NBIO0_PCIE_LC_STATE4_ADDRESS                   0x1a8412a4UL
#define SMN_FUNC1_PCIE3NBIO1_PCIE_LC_STATE4_ADDRESS                   0x1aa412a4UL
#define SMN_FUNC1_PCIE5NBIO0_PCIE_LC_STATE4_ADDRESS                   0x1ab412a4UL
#define SMN_FUNC1_PCIE5NBIO1_PCIE_LC_STATE4_ADDRESS                   0x1ad412a4UL
#define SMN_FUNC2_PCIE0NBIO0_PCIE_LC_STATE4_ADDRESS                   0x1a3422a4UL
#define SMN_FUNC2_PCIE0NBIO1_PCIE_LC_STATE4_ADDRESS                   0x1a5422a4UL
#define SMN_FUNC2_PCIE1NBIO0_PCIE_LC_STATE4_ADDRESS                   0x1a4422a4UL
#define SMN_FUNC2_PCIE1NBIO1_PCIE_LC_STATE4_ADDRESS                   0x1a6422a4UL
#define SMN_FUNC2_PCIE2NBIO0_PCIE_LC_STATE4_ADDRESS                   0x1a7422a4UL
#define SMN_FUNC2_PCIE2NBIO1_PCIE_LC_STATE4_ADDRESS                   0x1a9422a4UL
#define SMN_FUNC2_PCIE3NBIO0_PCIE_LC_STATE4_ADDRESS                   0x1a8422a4UL
#define SMN_FUNC2_PCIE3NBIO1_PCIE_LC_STATE4_ADDRESS                   0x1aa422a4UL
#define SMN_FUNC2_PCIE5NBIO0_PCIE_LC_STATE4_ADDRESS                   0x1ab422a4UL
#define SMN_FUNC2_PCIE5NBIO1_PCIE_LC_STATE4_ADDRESS                   0x1ad422a4UL
#define SMN_FUNC3_PCIE0NBIO0_PCIE_LC_STATE4_ADDRESS                   0x1a3432a4UL
#define SMN_FUNC3_PCIE0NBIO1_PCIE_LC_STATE4_ADDRESS                   0x1a5432a4UL
#define SMN_FUNC3_PCIE1NBIO0_PCIE_LC_STATE4_ADDRESS                   0x1a4432a4UL
#define SMN_FUNC3_PCIE1NBIO1_PCIE_LC_STATE4_ADDRESS                   0x1a6432a4UL
#define SMN_FUNC3_PCIE2NBIO0_PCIE_LC_STATE4_ADDRESS                   0x1a7432a4UL
#define SMN_FUNC3_PCIE2NBIO1_PCIE_LC_STATE4_ADDRESS                   0x1a9432a4UL
#define SMN_FUNC3_PCIE3NBIO0_PCIE_LC_STATE4_ADDRESS                   0x1a8432a4UL
#define SMN_FUNC3_PCIE3NBIO1_PCIE_LC_STATE4_ADDRESS                   0x1aa432a4UL
#define SMN_FUNC3_PCIE5NBIO0_PCIE_LC_STATE4_ADDRESS                   0x1ab432a4UL
#define SMN_FUNC3_PCIE5NBIO1_PCIE_LC_STATE4_ADDRESS                   0x1ad432a4UL
#define SMN_FUNC4_PCIE0NBIO0_PCIE_LC_STATE4_ADDRESS                   0x1a3442a4UL
#define SMN_FUNC4_PCIE0NBIO1_PCIE_LC_STATE4_ADDRESS                   0x1a5442a4UL
#define SMN_FUNC4_PCIE1NBIO0_PCIE_LC_STATE4_ADDRESS                   0x1a4442a4UL
#define SMN_FUNC4_PCIE1NBIO1_PCIE_LC_STATE4_ADDRESS                   0x1a6442a4UL
#define SMN_FUNC4_PCIE2NBIO0_PCIE_LC_STATE4_ADDRESS                   0x1a7442a4UL
#define SMN_FUNC4_PCIE2NBIO1_PCIE_LC_STATE4_ADDRESS                   0x1a9442a4UL
#define SMN_FUNC4_PCIE3NBIO0_PCIE_LC_STATE4_ADDRESS                   0x1a8442a4UL
#define SMN_FUNC4_PCIE3NBIO1_PCIE_LC_STATE4_ADDRESS                   0x1aa442a4UL
#define SMN_FUNC4_PCIE5NBIO0_PCIE_LC_STATE4_ADDRESS                   0x1ab442a4UL
#define SMN_FUNC4_PCIE5NBIO1_PCIE_LC_STATE4_ADDRESS                   0x1ad442a4UL
#define SMN_FUNC5_PCIE0NBIO0_PCIE_LC_STATE4_ADDRESS                   0x1a3452a4UL
#define SMN_FUNC5_PCIE0NBIO1_PCIE_LC_STATE4_ADDRESS                   0x1a5452a4UL
#define SMN_FUNC5_PCIE1NBIO0_PCIE_LC_STATE4_ADDRESS                   0x1a4452a4UL
#define SMN_FUNC5_PCIE1NBIO1_PCIE_LC_STATE4_ADDRESS                   0x1a6452a4UL
#define SMN_FUNC5_PCIE2NBIO0_PCIE_LC_STATE4_ADDRESS                   0x1a7452a4UL
#define SMN_FUNC5_PCIE2NBIO1_PCIE_LC_STATE4_ADDRESS                   0x1a9452a4UL
#define SMN_FUNC5_PCIE3NBIO0_PCIE_LC_STATE4_ADDRESS                   0x1a8452a4UL
#define SMN_FUNC5_PCIE3NBIO1_PCIE_LC_STATE4_ADDRESS                   0x1aa452a4UL
#define SMN_FUNC5_PCIE5NBIO0_PCIE_LC_STATE4_ADDRESS                   0x1ab452a4UL
#define SMN_FUNC5_PCIE5NBIO1_PCIE_LC_STATE4_ADDRESS                   0x1ad452a4UL
#define SMN_FUNC6_PCIE0NBIO0_PCIE_LC_STATE4_ADDRESS                   0x1a3462a4UL
#define SMN_FUNC6_PCIE0NBIO1_PCIE_LC_STATE4_ADDRESS                   0x1a5462a4UL
#define SMN_FUNC6_PCIE1NBIO0_PCIE_LC_STATE4_ADDRESS                   0x1a4462a4UL
#define SMN_FUNC6_PCIE1NBIO1_PCIE_LC_STATE4_ADDRESS                   0x1a6462a4UL
#define SMN_FUNC6_PCIE2NBIO0_PCIE_LC_STATE4_ADDRESS                   0x1a7462a4UL
#define SMN_FUNC6_PCIE2NBIO1_PCIE_LC_STATE4_ADDRESS                   0x1a9462a4UL
#define SMN_FUNC6_PCIE3NBIO0_PCIE_LC_STATE4_ADDRESS                   0x1a8462a4UL
#define SMN_FUNC6_PCIE3NBIO1_PCIE_LC_STATE4_ADDRESS                   0x1aa462a4UL
#define SMN_FUNC6_PCIE5NBIO0_PCIE_LC_STATE4_ADDRESS                   0x1ab462a4UL
#define SMN_FUNC6_PCIE5NBIO1_PCIE_LC_STATE4_ADDRESS                   0x1ad462a4UL
#define SMN_FUNC7_PCIE0NBIO0_PCIE_LC_STATE4_ADDRESS                   0x1a3472a4UL
#define SMN_FUNC7_PCIE0NBIO1_PCIE_LC_STATE4_ADDRESS                   0x1a5472a4UL
#define SMN_FUNC7_PCIE1NBIO0_PCIE_LC_STATE4_ADDRESS                   0x1a4472a4UL
#define SMN_FUNC7_PCIE1NBIO1_PCIE_LC_STATE4_ADDRESS                   0x1a6472a4UL
#define SMN_FUNC7_PCIE2NBIO0_PCIE_LC_STATE4_ADDRESS                   0x1a7472a4UL
#define SMN_FUNC7_PCIE2NBIO1_PCIE_LC_STATE4_ADDRESS                   0x1a9472a4UL
#define SMN_FUNC7_PCIE3NBIO0_PCIE_LC_STATE4_ADDRESS                   0x1a8472a4UL
#define SMN_FUNC7_PCIE3NBIO1_PCIE_LC_STATE4_ADDRESS                   0x1aa472a4UL
#define SMN_FUNC7_PCIE5NBIO0_PCIE_LC_STATE4_ADDRESS                   0x1ab472a4UL
#define SMN_FUNC7_PCIE5NBIO1_PCIE_LC_STATE4_ADDRESS                   0x1ad472a4UL
#define SMN_FUNC8_PCIE0NBIO0_PCIE_LC_STATE4_ADDRESS                   0x1a3482a4UL
#define SMN_FUNC8_PCIE0NBIO1_PCIE_LC_STATE4_ADDRESS                   0x1a5482a4UL
#define SMN_FUNC8_PCIE1NBIO0_PCIE_LC_STATE4_ADDRESS                   0x1a4482a4UL
#define SMN_FUNC8_PCIE1NBIO1_PCIE_LC_STATE4_ADDRESS                   0x1a6482a4UL
#define SMN_FUNC8_PCIE2NBIO0_PCIE_LC_STATE4_ADDRESS                   0x1a7482a4UL
#define SMN_FUNC8_PCIE2NBIO1_PCIE_LC_STATE4_ADDRESS                   0x1a9482a4UL
#define SMN_FUNC8_PCIE3NBIO0_PCIE_LC_STATE4_ADDRESS                   0x1a8482a4UL
#define SMN_FUNC8_PCIE3NBIO1_PCIE_LC_STATE4_ADDRESS                   0x1aa482a4UL


/***********************************************************
* Register Name : PCIE_LC_STATE5
************************************************************/

#define PCIE_LC_STATE5_LC_PREV_STATE20_OFFSET                  0
#define PCIE_LC_STATE5_LC_PREV_STATE20_MASK                    0x3f

#define PCIE_LC_STATE5_Reserved_7_6_OFFSET                     6
#define PCIE_LC_STATE5_Reserved_7_6_MASK                       0xc0

#define PCIE_LC_STATE5_LC_PREV_STATE21_OFFSET                  8
#define PCIE_LC_STATE5_LC_PREV_STATE21_MASK                    0x3f00

#define PCIE_LC_STATE5_Reserved_15_14_OFFSET                   14
#define PCIE_LC_STATE5_Reserved_15_14_MASK                     0xc000

#define PCIE_LC_STATE5_LC_PREV_STATE22_OFFSET                  16
#define PCIE_LC_STATE5_LC_PREV_STATE22_MASK                    0x3f0000

#define PCIE_LC_STATE5_Reserved_23_22_OFFSET                   22
#define PCIE_LC_STATE5_Reserved_23_22_MASK                     0xc00000

#define PCIE_LC_STATE5_LC_PREV_STATE23_OFFSET                  24
#define PCIE_LC_STATE5_LC_PREV_STATE23_MASK                    0x3f000000

#define PCIE_LC_STATE5_Reserved_31_30_OFFSET                   30
#define PCIE_LC_STATE5_Reserved_31_30_MASK                     0xc0000000

typedef union {
  struct {
    UINT32                                     LC_PREV_STATE20:6;
    UINT32                                        Reserved_7_6:2;
    UINT32                                     LC_PREV_STATE21:6;
    UINT32                                      Reserved_15_14:2;
    UINT32                                     LC_PREV_STATE22:6;
    UINT32                                      Reserved_23_22:2;
    UINT32                                     LC_PREV_STATE23:6;
    UINT32                                      Reserved_31_30:2;
  } Field;
  UINT32 Value;
} PCIE_LC_STATE5_STRUCT;

#define PCICFG_PCIEPORT_PCIE_LC_STATE5_OFFSET                       0x2a8
#define SMN_FUNC0_PCIE0NBIO0_PCIE_LC_STATE5_ADDRESS                   0x1a3402a8UL
#define SMN_FUNC0_PCIE0NBIO1_PCIE_LC_STATE5_ADDRESS                   0x1a5402a8UL
#define SMN_FUNC0_PCIE1NBIO0_PCIE_LC_STATE5_ADDRESS                   0x1a4402a8UL
#define SMN_FUNC0_PCIE1NBIO1_PCIE_LC_STATE5_ADDRESS                   0x1a6402a8UL
#define SMN_FUNC0_PCIE2NBIO0_PCIE_LC_STATE5_ADDRESS                   0x1a7402a8UL
#define SMN_FUNC0_PCIE2NBIO1_PCIE_LC_STATE5_ADDRESS                   0x1a9402a8UL
#define SMN_FUNC0_PCIE3NBIO0_PCIE_LC_STATE5_ADDRESS                   0x1a8402a8UL
#define SMN_FUNC0_PCIE3NBIO1_PCIE_LC_STATE5_ADDRESS                   0x1aa402a8UL
#define SMN_FUNC0_PCIE5NBIO0_PCIE_LC_STATE5_ADDRESS                   0x1ab402a8UL
#define SMN_FUNC0_PCIE5NBIO1_PCIE_LC_STATE5_ADDRESS                   0x1ad402a8UL
#define SMN_FUNC1_PCIE0NBIO0_PCIE_LC_STATE5_ADDRESS                   0x1a3412a8UL
#define SMN_FUNC1_PCIE0NBIO1_PCIE_LC_STATE5_ADDRESS                   0x1a5412a8UL
#define SMN_FUNC1_PCIE1NBIO0_PCIE_LC_STATE5_ADDRESS                   0x1a4412a8UL
#define SMN_FUNC1_PCIE1NBIO1_PCIE_LC_STATE5_ADDRESS                   0x1a6412a8UL
#define SMN_FUNC1_PCIE2NBIO0_PCIE_LC_STATE5_ADDRESS                   0x1a7412a8UL
#define SMN_FUNC1_PCIE2NBIO1_PCIE_LC_STATE5_ADDRESS                   0x1a9412a8UL
#define SMN_FUNC1_PCIE3NBIO0_PCIE_LC_STATE5_ADDRESS                   0x1a8412a8UL
#define SMN_FUNC1_PCIE3NBIO1_PCIE_LC_STATE5_ADDRESS                   0x1aa412a8UL
#define SMN_FUNC1_PCIE5NBIO0_PCIE_LC_STATE5_ADDRESS                   0x1ab412a8UL
#define SMN_FUNC1_PCIE5NBIO1_PCIE_LC_STATE5_ADDRESS                   0x1ad412a8UL
#define SMN_FUNC2_PCIE0NBIO0_PCIE_LC_STATE5_ADDRESS                   0x1a3422a8UL
#define SMN_FUNC2_PCIE0NBIO1_PCIE_LC_STATE5_ADDRESS                   0x1a5422a8UL
#define SMN_FUNC2_PCIE1NBIO0_PCIE_LC_STATE5_ADDRESS                   0x1a4422a8UL
#define SMN_FUNC2_PCIE1NBIO1_PCIE_LC_STATE5_ADDRESS                   0x1a6422a8UL
#define SMN_FUNC2_PCIE2NBIO0_PCIE_LC_STATE5_ADDRESS                   0x1a7422a8UL
#define SMN_FUNC2_PCIE2NBIO1_PCIE_LC_STATE5_ADDRESS                   0x1a9422a8UL
#define SMN_FUNC2_PCIE3NBIO0_PCIE_LC_STATE5_ADDRESS                   0x1a8422a8UL
#define SMN_FUNC2_PCIE3NBIO1_PCIE_LC_STATE5_ADDRESS                   0x1aa422a8UL
#define SMN_FUNC2_PCIE5NBIO0_PCIE_LC_STATE5_ADDRESS                   0x1ab422a8UL
#define SMN_FUNC2_PCIE5NBIO1_PCIE_LC_STATE5_ADDRESS                   0x1ad422a8UL
#define SMN_FUNC3_PCIE0NBIO0_PCIE_LC_STATE5_ADDRESS                   0x1a3432a8UL
#define SMN_FUNC3_PCIE0NBIO1_PCIE_LC_STATE5_ADDRESS                   0x1a5432a8UL
#define SMN_FUNC3_PCIE1NBIO0_PCIE_LC_STATE5_ADDRESS                   0x1a4432a8UL
#define SMN_FUNC3_PCIE1NBIO1_PCIE_LC_STATE5_ADDRESS                   0x1a6432a8UL
#define SMN_FUNC3_PCIE2NBIO0_PCIE_LC_STATE5_ADDRESS                   0x1a7432a8UL
#define SMN_FUNC3_PCIE2NBIO1_PCIE_LC_STATE5_ADDRESS                   0x1a9432a8UL
#define SMN_FUNC3_PCIE3NBIO0_PCIE_LC_STATE5_ADDRESS                   0x1a8432a8UL
#define SMN_FUNC3_PCIE3NBIO1_PCIE_LC_STATE5_ADDRESS                   0x1aa432a8UL
#define SMN_FUNC3_PCIE5NBIO0_PCIE_LC_STATE5_ADDRESS                   0x1ab432a8UL
#define SMN_FUNC3_PCIE5NBIO1_PCIE_LC_STATE5_ADDRESS                   0x1ad432a8UL
#define SMN_FUNC4_PCIE0NBIO0_PCIE_LC_STATE5_ADDRESS                   0x1a3442a8UL
#define SMN_FUNC4_PCIE0NBIO1_PCIE_LC_STATE5_ADDRESS                   0x1a5442a8UL
#define SMN_FUNC4_PCIE1NBIO0_PCIE_LC_STATE5_ADDRESS                   0x1a4442a8UL
#define SMN_FUNC4_PCIE1NBIO1_PCIE_LC_STATE5_ADDRESS                   0x1a6442a8UL
#define SMN_FUNC4_PCIE2NBIO0_PCIE_LC_STATE5_ADDRESS                   0x1a7442a8UL
#define SMN_FUNC4_PCIE2NBIO1_PCIE_LC_STATE5_ADDRESS                   0x1a9442a8UL
#define SMN_FUNC4_PCIE3NBIO0_PCIE_LC_STATE5_ADDRESS                   0x1a8442a8UL
#define SMN_FUNC4_PCIE3NBIO1_PCIE_LC_STATE5_ADDRESS                   0x1aa442a8UL
#define SMN_FUNC4_PCIE5NBIO0_PCIE_LC_STATE5_ADDRESS                   0x1ab442a8UL
#define SMN_FUNC4_PCIE5NBIO1_PCIE_LC_STATE5_ADDRESS                   0x1ad442a8UL
#define SMN_FUNC5_PCIE0NBIO0_PCIE_LC_STATE5_ADDRESS                   0x1a3452a8UL
#define SMN_FUNC5_PCIE0NBIO1_PCIE_LC_STATE5_ADDRESS                   0x1a5452a8UL
#define SMN_FUNC5_PCIE1NBIO0_PCIE_LC_STATE5_ADDRESS                   0x1a4452a8UL
#define SMN_FUNC5_PCIE1NBIO1_PCIE_LC_STATE5_ADDRESS                   0x1a6452a8UL
#define SMN_FUNC5_PCIE2NBIO0_PCIE_LC_STATE5_ADDRESS                   0x1a7452a8UL
#define SMN_FUNC5_PCIE2NBIO1_PCIE_LC_STATE5_ADDRESS                   0x1a9452a8UL
#define SMN_FUNC5_PCIE3NBIO0_PCIE_LC_STATE5_ADDRESS                   0x1a8452a8UL
#define SMN_FUNC5_PCIE3NBIO1_PCIE_LC_STATE5_ADDRESS                   0x1aa452a8UL
#define SMN_FUNC5_PCIE5NBIO0_PCIE_LC_STATE5_ADDRESS                   0x1ab452a8UL
#define SMN_FUNC5_PCIE5NBIO1_PCIE_LC_STATE5_ADDRESS                   0x1ad452a8UL
#define SMN_FUNC6_PCIE0NBIO0_PCIE_LC_STATE5_ADDRESS                   0x1a3462a8UL
#define SMN_FUNC6_PCIE0NBIO1_PCIE_LC_STATE5_ADDRESS                   0x1a5462a8UL
#define SMN_FUNC6_PCIE1NBIO0_PCIE_LC_STATE5_ADDRESS                   0x1a4462a8UL
#define SMN_FUNC6_PCIE1NBIO1_PCIE_LC_STATE5_ADDRESS                   0x1a6462a8UL
#define SMN_FUNC6_PCIE2NBIO0_PCIE_LC_STATE5_ADDRESS                   0x1a7462a8UL
#define SMN_FUNC6_PCIE2NBIO1_PCIE_LC_STATE5_ADDRESS                   0x1a9462a8UL
#define SMN_FUNC6_PCIE3NBIO0_PCIE_LC_STATE5_ADDRESS                   0x1a8462a8UL
#define SMN_FUNC6_PCIE3NBIO1_PCIE_LC_STATE5_ADDRESS                   0x1aa462a8UL
#define SMN_FUNC6_PCIE5NBIO0_PCIE_LC_STATE5_ADDRESS                   0x1ab462a8UL
#define SMN_FUNC6_PCIE5NBIO1_PCIE_LC_STATE5_ADDRESS                   0x1ad462a8UL
#define SMN_FUNC7_PCIE0NBIO0_PCIE_LC_STATE5_ADDRESS                   0x1a3472a8UL
#define SMN_FUNC7_PCIE0NBIO1_PCIE_LC_STATE5_ADDRESS                   0x1a5472a8UL
#define SMN_FUNC7_PCIE1NBIO0_PCIE_LC_STATE5_ADDRESS                   0x1a4472a8UL
#define SMN_FUNC7_PCIE1NBIO1_PCIE_LC_STATE5_ADDRESS                   0x1a6472a8UL
#define SMN_FUNC7_PCIE2NBIO0_PCIE_LC_STATE5_ADDRESS                   0x1a7472a8UL
#define SMN_FUNC7_PCIE2NBIO1_PCIE_LC_STATE5_ADDRESS                   0x1a9472a8UL
#define SMN_FUNC7_PCIE3NBIO0_PCIE_LC_STATE5_ADDRESS                   0x1a8472a8UL
#define SMN_FUNC7_PCIE3NBIO1_PCIE_LC_STATE5_ADDRESS                   0x1aa472a8UL
#define SMN_FUNC7_PCIE5NBIO0_PCIE_LC_STATE5_ADDRESS                   0x1ab472a8UL
#define SMN_FUNC7_PCIE5NBIO1_PCIE_LC_STATE5_ADDRESS                   0x1ad472a8UL
#define SMN_FUNC8_PCIE0NBIO0_PCIE_LC_STATE5_ADDRESS                   0x1a3482a8UL
#define SMN_FUNC8_PCIE0NBIO1_PCIE_LC_STATE5_ADDRESS                   0x1a5482a8UL
#define SMN_FUNC8_PCIE1NBIO0_PCIE_LC_STATE5_ADDRESS                   0x1a4482a8UL
#define SMN_FUNC8_PCIE1NBIO1_PCIE_LC_STATE5_ADDRESS                   0x1a6482a8UL
#define SMN_FUNC8_PCIE2NBIO0_PCIE_LC_STATE5_ADDRESS                   0x1a7482a8UL
#define SMN_FUNC8_PCIE2NBIO1_PCIE_LC_STATE5_ADDRESS                   0x1a9482a8UL
#define SMN_FUNC8_PCIE3NBIO0_PCIE_LC_STATE5_ADDRESS                   0x1a8482a8UL
#define SMN_FUNC8_PCIE3NBIO1_PCIE_LC_STATE5_ADDRESS                   0x1aa482a8UL

/***********************************************************
* Register Name : PCIE_LC_FORCE_COEFF
************************************************************/

#define PCIE_LC_FORCE_COEFF_LC_FORCE_COEFF_8GT_OFFSET          0
#define PCIE_LC_FORCE_COEFF_LC_FORCE_COEFF_8GT_MASK            0x1u

#define PCIE_LC_FORCE_COEFF_LC_FORCE_PRE_CURSOR_8GT_OFFSET     1
#define PCIE_LC_FORCE_COEFF_LC_FORCE_PRE_CURSOR_8GT_MASK       0x7eu

#define PCIE_LC_FORCE_COEFF_LC_FORCE_CURSOR_8GT_OFFSET         7
#define PCIE_LC_FORCE_COEFF_LC_FORCE_CURSOR_8GT_MASK           0x1f80u

#define PCIE_LC_FORCE_COEFF_LC_FORCE_POST_CURSOR_8GT_OFFSET    13
#define PCIE_LC_FORCE_COEFF_LC_FORCE_POST_CURSOR_8GT_MASK      0x7e000u

#define PCIE_LC_FORCE_COEFF_LC_3X3_COEFF_SEARCH_EN_8GT_OFFSET  19
#define PCIE_LC_FORCE_COEFF_LC_3X3_COEFF_SEARCH_EN_8GT_MASK    0x80000u

#define PCIE_LC_FORCE_COEFF_LC_PRESET_10_EN_OFFSET             20
#define PCIE_LC_FORCE_COEFF_LC_PRESET_10_EN_MASK               0x100000u

#define PCIE_LC_FORCE_COEFF_RESERVED_31_21_OFFSET              21
#define PCIE_LC_FORCE_COEFF_RESERVED_31_21_MASK                0xffe00000u

typedef union {
  struct {
    UINT32                                  LC_FORCE_COEFF_8GT:1;
    UINT32                             LC_FORCE_PRE_CURSOR_8GT:6;
    UINT32                                 LC_FORCE_CURSOR_8GT:6;
    UINT32                            LC_FORCE_POST_CURSOR_8GT:6;
    UINT32                          LC_3X3_COEFF_SEARCH_EN_8GT:1;
    UINT32                                     LC_PRESET_10_EN:1;
    UINT32                                      Reserved_31_21:11;
  } Field;
  UINT32 Value;
} PCIE_LC_FORCE_COEFF_STRUCT;

#define SMN_PCIEPORT_PCIE_LC_FORCE_COEFF_ADDRESS                      0x1a3402e0u
#define SMN_FUNC0_PCIE0NBIO0_PCIE_LC_FORCE_COEFF_ADDRESS              0x1a3402e0u
#define SMN_FUNC0_PCIE0NBIO1_PCIE_LC_FORCE_COEFF_ADDRESS              0x1a5402e0u
#define SMN_FUNC0_PCIE1NBIO0_PCIE_LC_FORCE_COEFF_ADDRESS              0x1a4402e0u
#define SMN_FUNC0_PCIE1NBIO1_PCIE_LC_FORCE_COEFF_ADDRESS              0x1a6402e0u
#define SMN_FUNC0_PCIE2NBIO0_PCIE_LC_FORCE_COEFF_ADDRESS              0x1a7402e0u
#define SMN_FUNC0_PCIE2NBIO1_PCIE_LC_FORCE_COEFF_ADDRESS              0x1a9402e0u
#define SMN_FUNC0_PCIE3NBIO0_PCIE_LC_FORCE_COEFF_ADDRESS              0x1a8402e0u
#define SMN_FUNC0_PCIE3NBIO1_PCIE_LC_FORCE_COEFF_ADDRESS              0x1aa402e0u
#define SMN_FUNC0_PCIE5NBIO0_PCIE_LC_FORCE_COEFF_ADDRESS              0x1ab402e0u
#define SMN_FUNC1_PCIE0NBIO0_PCIE_LC_FORCE_COEFF_ADDRESS              0x1a3412e0u
#define SMN_FUNC1_PCIE0NBIO1_PCIE_LC_FORCE_COEFF_ADDRESS              0x1a5412e0u
#define SMN_FUNC1_PCIE1NBIO0_PCIE_LC_FORCE_COEFF_ADDRESS              0x1a4412e0u
#define SMN_FUNC1_PCIE1NBIO1_PCIE_LC_FORCE_COEFF_ADDRESS              0x1a6412e0u
#define SMN_FUNC1_PCIE2NBIO0_PCIE_LC_FORCE_COEFF_ADDRESS              0x1a7412e0u
#define SMN_FUNC1_PCIE2NBIO1_PCIE_LC_FORCE_COEFF_ADDRESS              0x1a9412e0u
#define SMN_FUNC1_PCIE3NBIO0_PCIE_LC_FORCE_COEFF_ADDRESS              0x1a8412e0u
#define SMN_FUNC1_PCIE3NBIO1_PCIE_LC_FORCE_COEFF_ADDRESS              0x1aa412e0u
#define SMN_FUNC1_PCIE5NBIO0_PCIE_LC_FORCE_COEFF_ADDRESS              0x1ab412e0u
#define SMN_FUNC2_PCIE0NBIO0_PCIE_LC_FORCE_COEFF_ADDRESS              0x1a3422e0u
#define SMN_FUNC2_PCIE0NBIO1_PCIE_LC_FORCE_COEFF_ADDRESS              0x1a5422e0u
#define SMN_FUNC2_PCIE1NBIO0_PCIE_LC_FORCE_COEFF_ADDRESS              0x1a4422e0u
#define SMN_FUNC2_PCIE1NBIO1_PCIE_LC_FORCE_COEFF_ADDRESS              0x1a6422e0u
#define SMN_FUNC2_PCIE2NBIO0_PCIE_LC_FORCE_COEFF_ADDRESS              0x1a7422e0u
#define SMN_FUNC2_PCIE2NBIO1_PCIE_LC_FORCE_COEFF_ADDRESS              0x1a9422e0u
#define SMN_FUNC2_PCIE3NBIO0_PCIE_LC_FORCE_COEFF_ADDRESS              0x1a8422e0u
#define SMN_FUNC2_PCIE3NBIO1_PCIE_LC_FORCE_COEFF_ADDRESS              0x1aa422e0u
#define SMN_FUNC2_PCIE5NBIO0_PCIE_LC_FORCE_COEFF_ADDRESS              0x1ab422e0u
#define SMN_FUNC3_PCIE0NBIO0_PCIE_LC_FORCE_COEFF_ADDRESS              0x1a3432e0u
#define SMN_FUNC3_PCIE0NBIO1_PCIE_LC_FORCE_COEFF_ADDRESS              0x1a5432e0u
#define SMN_FUNC3_PCIE1NBIO0_PCIE_LC_FORCE_COEFF_ADDRESS              0x1a4432e0u
#define SMN_FUNC3_PCIE1NBIO1_PCIE_LC_FORCE_COEFF_ADDRESS              0x1a6432e0u
#define SMN_FUNC3_PCIE2NBIO0_PCIE_LC_FORCE_COEFF_ADDRESS              0x1a7432e0u
#define SMN_FUNC3_PCIE2NBIO1_PCIE_LC_FORCE_COEFF_ADDRESS              0x1a9432e0u
#define SMN_FUNC3_PCIE3NBIO0_PCIE_LC_FORCE_COEFF_ADDRESS              0x1a8432e0u
#define SMN_FUNC3_PCIE3NBIO1_PCIE_LC_FORCE_COEFF_ADDRESS              0x1aa432e0u
#define SMN_FUNC3_PCIE5NBIO0_PCIE_LC_FORCE_COEFF_ADDRESS              0x1ab432e0u
#define SMN_FUNC4_PCIE0NBIO0_PCIE_LC_FORCE_COEFF_ADDRESS              0x1a3442e0u
#define SMN_FUNC4_PCIE0NBIO1_PCIE_LC_FORCE_COEFF_ADDRESS              0x1a5442e0u
#define SMN_FUNC4_PCIE1NBIO0_PCIE_LC_FORCE_COEFF_ADDRESS              0x1a4442e0u
#define SMN_FUNC4_PCIE1NBIO1_PCIE_LC_FORCE_COEFF_ADDRESS              0x1a6442e0u
#define SMN_FUNC4_PCIE2NBIO0_PCIE_LC_FORCE_COEFF_ADDRESS              0x1a7442e0u
#define SMN_FUNC4_PCIE2NBIO1_PCIE_LC_FORCE_COEFF_ADDRESS              0x1a9442e0u
#define SMN_FUNC4_PCIE3NBIO0_PCIE_LC_FORCE_COEFF_ADDRESS              0x1a8442e0u
#define SMN_FUNC4_PCIE3NBIO1_PCIE_LC_FORCE_COEFF_ADDRESS              0x1aa442e0u
#define SMN_FUNC4_PCIE5NBIO0_PCIE_LC_FORCE_COEFF_ADDRESS              0x1ab442e0u
#define SMN_FUNC5_PCIE0NBIO0_PCIE_LC_FORCE_COEFF_ADDRESS              0x1a3452e0u
#define SMN_FUNC5_PCIE0NBIO1_PCIE_LC_FORCE_COEFF_ADDRESS              0x1a5452e0u
#define SMN_FUNC5_PCIE1NBIO0_PCIE_LC_FORCE_COEFF_ADDRESS              0x1a4452e0u
#define SMN_FUNC5_PCIE1NBIO1_PCIE_LC_FORCE_COEFF_ADDRESS              0x1a6452e0u
#define SMN_FUNC5_PCIE2NBIO0_PCIE_LC_FORCE_COEFF_ADDRESS              0x1a7452e0u
#define SMN_FUNC5_PCIE2NBIO1_PCIE_LC_FORCE_COEFF_ADDRESS              0x1a9452e0u
#define SMN_FUNC5_PCIE3NBIO0_PCIE_LC_FORCE_COEFF_ADDRESS              0x1a8452e0u
#define SMN_FUNC5_PCIE3NBIO1_PCIE_LC_FORCE_COEFF_ADDRESS              0x1aa452e0u
#define SMN_FUNC5_PCIE5NBIO0_PCIE_LC_FORCE_COEFF_ADDRESS              0x1ab452e0u
#define SMN_FUNC6_PCIE0NBIO0_PCIE_LC_FORCE_COEFF_ADDRESS              0x1a3462e0u
#define SMN_FUNC6_PCIE0NBIO1_PCIE_LC_FORCE_COEFF_ADDRESS              0x1a5462e0u
#define SMN_FUNC6_PCIE1NBIO0_PCIE_LC_FORCE_COEFF_ADDRESS              0x1a4462e0u
#define SMN_FUNC6_PCIE1NBIO1_PCIE_LC_FORCE_COEFF_ADDRESS              0x1a6462e0u
#define SMN_FUNC6_PCIE2NBIO0_PCIE_LC_FORCE_COEFF_ADDRESS              0x1a7462e0u
#define SMN_FUNC6_PCIE2NBIO1_PCIE_LC_FORCE_COEFF_ADDRESS              0x1a9462e0u
#define SMN_FUNC6_PCIE3NBIO0_PCIE_LC_FORCE_COEFF_ADDRESS              0x1a8462e0u
#define SMN_FUNC6_PCIE3NBIO1_PCIE_LC_FORCE_COEFF_ADDRESS              0x1aa462e0u
#define SMN_FUNC6_PCIE5NBIO0_PCIE_LC_FORCE_COEFF_ADDRESS              0x1ab462e0u
#define SMN_FUNC7_PCIE0NBIO0_PCIE_LC_FORCE_COEFF_ADDRESS              0x1a3472e0u
#define SMN_FUNC7_PCIE0NBIO1_PCIE_LC_FORCE_COEFF_ADDRESS              0x1a5472e0u
#define SMN_FUNC7_PCIE1NBIO0_PCIE_LC_FORCE_COEFF_ADDRESS              0x1a4472e0u
#define SMN_FUNC7_PCIE1NBIO1_PCIE_LC_FORCE_COEFF_ADDRESS              0x1a6472e0u
#define SMN_FUNC7_PCIE2NBIO0_PCIE_LC_FORCE_COEFF_ADDRESS              0x1a7472e0u
#define SMN_FUNC7_PCIE2NBIO1_PCIE_LC_FORCE_COEFF_ADDRESS              0x1a9472e0u
#define SMN_FUNC7_PCIE3NBIO0_PCIE_LC_FORCE_COEFF_ADDRESS              0x1a8472e0u
#define SMN_FUNC7_PCIE3NBIO1_PCIE_LC_FORCE_COEFF_ADDRESS              0x1aa472e0u
#define SMN_FUNC7_PCIE5NBIO0_PCIE_LC_FORCE_COEFF_ADDRESS              0x1ab472e0u
#define SMN_FUNC8_PCIE0NBIO0_PCIE_LC_FORCE_COEFF_ADDRESS              0x1a3482e0u
#define SMN_FUNC8_PCIE0NBIO1_PCIE_LC_FORCE_COEFF_ADDRESS              0x1a5482e0u
#define SMN_FUNC8_PCIE1NBIO0_PCIE_LC_FORCE_COEFF_ADDRESS              0x1a4482e0u
#define SMN_FUNC8_PCIE1NBIO1_PCIE_LC_FORCE_COEFF_ADDRESS              0x1a6482e0u
#define SMN_FUNC8_PCIE2NBIO0_PCIE_LC_FORCE_COEFF_ADDRESS              0x1a7482e0u
#define SMN_FUNC8_PCIE2NBIO1_PCIE_LC_FORCE_COEFF_ADDRESS              0x1a9482e0u
#define SMN_FUNC8_PCIE3NBIO0_PCIE_LC_FORCE_COEFF_ADDRESS              0x1a8482e0u
#define SMN_FUNC8_PCIE3NBIO1_PCIE_LC_FORCE_COEFF_ADDRESS              0x1aa482e0u

/***********************************************************
* Register Name : PCIE_LC_FORCE_COEFF2
************************************************************/

#define PCIE_LC_FORCE_COEFF2_LC_FORCE_COEFF_16GT_OFFSET        0
#define PCIE_LC_FORCE_COEFF2_LC_FORCE_COEFF_16GT_MASK          0x1u

#define PCIE_LC_FORCE_COEFF2_LC_FORCE_PRE_CURSOR_16GT_OFFSET   1
#define PCIE_LC_FORCE_COEFF2_LC_FORCE_PRE_CURSOR_16GT_MASK     0x7eu

#define PCIE_LC_FORCE_COEFF2_LC_FORCE_CURSOR_16GT_OFFSET       7
#define PCIE_LC_FORCE_COEFF2_LC_FORCE_CURSOR_16GT_MASK         0x1f80u

#define PCIE_LC_FORCE_COEFF2_LC_FORCE_POST_CURSOR_16GT_OFFSET  13
#define PCIE_LC_FORCE_COEFF2_LC_FORCE_POST_CURSOR_16GT_MASK    0x7e000u

#define PCIE_LC_FORCE_COEFF2_LC_3X3_COEFF_SEARCH_EN_16GT_OFFSET 19
#define PCIE_LC_FORCE_COEFF2_LC_3X3_COEFF_SEARCH_EN_16GT_MASK  0x80000u

#define PCIE_LC_FORCE_COEFF2_RESERVED_31_20_OFFSET             20
#define PCIE_LC_FORCE_COEFF2_RESERVED_31_20_MASK               0xfff00000u

typedef union {
  struct {
    UINT32                                 LC_FORCE_COEFF_16GT:1;
    UINT32                            LC_FORCE_PRE_CURSOR_16GT:6;
    UINT32                                LC_FORCE_CURSOR_16GT:6;
    UINT32                           LC_FORCE_POST_CURSOR_16GT:6;
    UINT32                         LC_3X3_COEFF_SEARCH_EN_16GT:1;
    UINT32                                      Reserved_31_20:12;
  } Field;
  UINT32 Value;
} PCIE_LC_FORCE_COEFF2_STRUCT;

#define SMN_PCIEPORT_PCIE_LC_FORCE_COEFF2_ADDRESS                     0x1a34037cu
#define SMN_FUNC0_PCIE0NBIO0_PCIE_LC_FORCE_COEFF2_ADDRESS             0x1a34037cu
#define SMN_FUNC0_PCIE0NBIO1_PCIE_LC_FORCE_COEFF2_ADDRESS             0x1a54037cu
#define SMN_FUNC0_PCIE1NBIO0_PCIE_LC_FORCE_COEFF2_ADDRESS             0x1a44037cu
#define SMN_FUNC0_PCIE1NBIO1_PCIE_LC_FORCE_COEFF2_ADDRESS             0x1a64037cu
#define SMN_FUNC0_PCIE2NBIO0_PCIE_LC_FORCE_COEFF2_ADDRESS             0x1a74037cu
#define SMN_FUNC0_PCIE2NBIO1_PCIE_LC_FORCE_COEFF2_ADDRESS             0x1a94037cu
#define SMN_FUNC0_PCIE3NBIO0_PCIE_LC_FORCE_COEFF2_ADDRESS             0x1a84037cu
#define SMN_FUNC0_PCIE3NBIO1_PCIE_LC_FORCE_COEFF2_ADDRESS             0x1aa4037cu
#define SMN_FUNC0_PCIE5NBIO0_PCIE_LC_FORCE_COEFF2_ADDRESS             0x1ab4037cu
#define SMN_FUNC1_PCIE0NBIO0_PCIE_LC_FORCE_COEFF2_ADDRESS             0x1a34137cu
#define SMN_FUNC1_PCIE0NBIO1_PCIE_LC_FORCE_COEFF2_ADDRESS             0x1a54137cu
#define SMN_FUNC1_PCIE1NBIO0_PCIE_LC_FORCE_COEFF2_ADDRESS             0x1a44137cu
#define SMN_FUNC1_PCIE1NBIO1_PCIE_LC_FORCE_COEFF2_ADDRESS             0x1a64137cu
#define SMN_FUNC1_PCIE2NBIO0_PCIE_LC_FORCE_COEFF2_ADDRESS             0x1a74137cu
#define SMN_FUNC1_PCIE2NBIO1_PCIE_LC_FORCE_COEFF2_ADDRESS             0x1a94137cu
#define SMN_FUNC1_PCIE3NBIO0_PCIE_LC_FORCE_COEFF2_ADDRESS             0x1a84137cu
#define SMN_FUNC1_PCIE3NBIO1_PCIE_LC_FORCE_COEFF2_ADDRESS             0x1aa4137cu
#define SMN_FUNC1_PCIE5NBIO0_PCIE_LC_FORCE_COEFF2_ADDRESS             0x1ab4137cu
#define SMN_FUNC2_PCIE0NBIO0_PCIE_LC_FORCE_COEFF2_ADDRESS             0x1a34237cu
#define SMN_FUNC2_PCIE0NBIO1_PCIE_LC_FORCE_COEFF2_ADDRESS             0x1a54237cu
#define SMN_FUNC2_PCIE1NBIO0_PCIE_LC_FORCE_COEFF2_ADDRESS             0x1a44237cu
#define SMN_FUNC2_PCIE1NBIO1_PCIE_LC_FORCE_COEFF2_ADDRESS             0x1a64237cu
#define SMN_FUNC2_PCIE2NBIO0_PCIE_LC_FORCE_COEFF2_ADDRESS             0x1a74237cu
#define SMN_FUNC2_PCIE2NBIO1_PCIE_LC_FORCE_COEFF2_ADDRESS             0x1a94237cu
#define SMN_FUNC2_PCIE3NBIO0_PCIE_LC_FORCE_COEFF2_ADDRESS             0x1a84237cu
#define SMN_FUNC2_PCIE3NBIO1_PCIE_LC_FORCE_COEFF2_ADDRESS             0x1aa4237cu
#define SMN_FUNC2_PCIE5NBIO0_PCIE_LC_FORCE_COEFF2_ADDRESS             0x1ab4237cu
#define SMN_FUNC3_PCIE0NBIO0_PCIE_LC_FORCE_COEFF2_ADDRESS             0x1a34337cu
#define SMN_FUNC3_PCIE0NBIO1_PCIE_LC_FORCE_COEFF2_ADDRESS             0x1a54337cu
#define SMN_FUNC3_PCIE1NBIO0_PCIE_LC_FORCE_COEFF2_ADDRESS             0x1a44337cu
#define SMN_FUNC3_PCIE1NBIO1_PCIE_LC_FORCE_COEFF2_ADDRESS             0x1a64337cu
#define SMN_FUNC3_PCIE2NBIO0_PCIE_LC_FORCE_COEFF2_ADDRESS             0x1a74337cu
#define SMN_FUNC3_PCIE2NBIO1_PCIE_LC_FORCE_COEFF2_ADDRESS             0x1a94337cu
#define SMN_FUNC3_PCIE3NBIO0_PCIE_LC_FORCE_COEFF2_ADDRESS             0x1a84337cu
#define SMN_FUNC3_PCIE3NBIO1_PCIE_LC_FORCE_COEFF2_ADDRESS             0x1aa4337cu
#define SMN_FUNC3_PCIE5NBIO0_PCIE_LC_FORCE_COEFF2_ADDRESS             0x1ab4337cu
#define SMN_FUNC4_PCIE0NBIO0_PCIE_LC_FORCE_COEFF2_ADDRESS             0x1a34437cu
#define SMN_FUNC4_PCIE0NBIO1_PCIE_LC_FORCE_COEFF2_ADDRESS             0x1a54437cu
#define SMN_FUNC4_PCIE1NBIO0_PCIE_LC_FORCE_COEFF2_ADDRESS             0x1a44437cu
#define SMN_FUNC4_PCIE1NBIO1_PCIE_LC_FORCE_COEFF2_ADDRESS             0x1a64437cu
#define SMN_FUNC4_PCIE2NBIO0_PCIE_LC_FORCE_COEFF2_ADDRESS             0x1a74437cu
#define SMN_FUNC4_PCIE2NBIO1_PCIE_LC_FORCE_COEFF2_ADDRESS             0x1a94437cu
#define SMN_FUNC4_PCIE3NBIO0_PCIE_LC_FORCE_COEFF2_ADDRESS             0x1a84437cu
#define SMN_FUNC4_PCIE3NBIO1_PCIE_LC_FORCE_COEFF2_ADDRESS             0x1aa4437cu
#define SMN_FUNC4_PCIE5NBIO0_PCIE_LC_FORCE_COEFF2_ADDRESS             0x1ab4437cu
#define SMN_FUNC5_PCIE0NBIO0_PCIE_LC_FORCE_COEFF2_ADDRESS             0x1a34537cu
#define SMN_FUNC5_PCIE0NBIO1_PCIE_LC_FORCE_COEFF2_ADDRESS             0x1a54537cu
#define SMN_FUNC5_PCIE1NBIO0_PCIE_LC_FORCE_COEFF2_ADDRESS             0x1a44537cu
#define SMN_FUNC5_PCIE1NBIO1_PCIE_LC_FORCE_COEFF2_ADDRESS             0x1a64537cu
#define SMN_FUNC5_PCIE2NBIO0_PCIE_LC_FORCE_COEFF2_ADDRESS             0x1a74537cu
#define SMN_FUNC5_PCIE2NBIO1_PCIE_LC_FORCE_COEFF2_ADDRESS             0x1a94537cu
#define SMN_FUNC5_PCIE3NBIO0_PCIE_LC_FORCE_COEFF2_ADDRESS             0x1a84537cu
#define SMN_FUNC5_PCIE3NBIO1_PCIE_LC_FORCE_COEFF2_ADDRESS             0x1aa4537cu
#define SMN_FUNC5_PCIE5NBIO0_PCIE_LC_FORCE_COEFF2_ADDRESS             0x1ab4537cu
#define SMN_FUNC6_PCIE0NBIO0_PCIE_LC_FORCE_COEFF2_ADDRESS             0x1a34637cu
#define SMN_FUNC6_PCIE0NBIO1_PCIE_LC_FORCE_COEFF2_ADDRESS             0x1a54637cu
#define SMN_FUNC6_PCIE1NBIO0_PCIE_LC_FORCE_COEFF2_ADDRESS             0x1a44637cu
#define SMN_FUNC6_PCIE1NBIO1_PCIE_LC_FORCE_COEFF2_ADDRESS             0x1a64637cu
#define SMN_FUNC6_PCIE2NBIO0_PCIE_LC_FORCE_COEFF2_ADDRESS             0x1a74637cu
#define SMN_FUNC6_PCIE2NBIO1_PCIE_LC_FORCE_COEFF2_ADDRESS             0x1a94637cu
#define SMN_FUNC6_PCIE3NBIO0_PCIE_LC_FORCE_COEFF2_ADDRESS             0x1a84637cu
#define SMN_FUNC6_PCIE3NBIO1_PCIE_LC_FORCE_COEFF2_ADDRESS             0x1aa4637cu
#define SMN_FUNC6_PCIE5NBIO0_PCIE_LC_FORCE_COEFF2_ADDRESS             0x1ab4637cu
#define SMN_FUNC7_PCIE0NBIO0_PCIE_LC_FORCE_COEFF2_ADDRESS             0x1a34737cu
#define SMN_FUNC7_PCIE0NBIO1_PCIE_LC_FORCE_COEFF2_ADDRESS             0x1a54737cu
#define SMN_FUNC7_PCIE1NBIO0_PCIE_LC_FORCE_COEFF2_ADDRESS             0x1a44737cu
#define SMN_FUNC7_PCIE1NBIO1_PCIE_LC_FORCE_COEFF2_ADDRESS             0x1a64737cu
#define SMN_FUNC7_PCIE2NBIO0_PCIE_LC_FORCE_COEFF2_ADDRESS             0x1a74737cu
#define SMN_FUNC7_PCIE2NBIO1_PCIE_LC_FORCE_COEFF2_ADDRESS             0x1a94737cu
#define SMN_FUNC7_PCIE3NBIO0_PCIE_LC_FORCE_COEFF2_ADDRESS             0x1a84737cu
#define SMN_FUNC7_PCIE3NBIO1_PCIE_LC_FORCE_COEFF2_ADDRESS             0x1aa4737cu
#define SMN_FUNC7_PCIE5NBIO0_PCIE_LC_FORCE_COEFF2_ADDRESS             0x1ab4737cu
#define SMN_FUNC8_PCIE0NBIO0_PCIE_LC_FORCE_COEFF2_ADDRESS             0x1a34837cu
#define SMN_FUNC8_PCIE0NBIO1_PCIE_LC_FORCE_COEFF2_ADDRESS             0x1a54837cu
#define SMN_FUNC8_PCIE1NBIO0_PCIE_LC_FORCE_COEFF2_ADDRESS             0x1a44837cu
#define SMN_FUNC8_PCIE1NBIO1_PCIE_LC_FORCE_COEFF2_ADDRESS             0x1a64837cu
#define SMN_FUNC8_PCIE2NBIO0_PCIE_LC_FORCE_COEFF2_ADDRESS             0x1a74837cu
#define SMN_FUNC8_PCIE2NBIO1_PCIE_LC_FORCE_COEFF2_ADDRESS             0x1a94837cu
#define SMN_FUNC8_PCIE3NBIO0_PCIE_LC_FORCE_COEFF2_ADDRESS             0x1a84837cu
#define SMN_FUNC8_PCIE3NBIO1_PCIE_LC_FORCE_COEFF2_ADDRESS             0x1aa4837cu

/***********************************************************
* Register Name : PCIE_LC_FORCE_COEFF3
************************************************************/

#define PCIE_LC_FORCE_COEFF3_LC_FORCE_COEFF_32GT_OFFSET        0
#define PCIE_LC_FORCE_COEFF3_LC_FORCE_COEFF_32GT_MASK          0x1u

#define PCIE_LC_FORCE_COEFF3_LC_FORCE_PRE_CURSOR_32GT_OFFSET   1
#define PCIE_LC_FORCE_COEFF3_LC_FORCE_PRE_CURSOR_32GT_MASK     0x7eu

#define PCIE_LC_FORCE_COEFF3_LC_FORCE_CURSOR_32GT_OFFSET       7
#define PCIE_LC_FORCE_COEFF3_LC_FORCE_CURSOR_32GT_MASK         0x1f80u

#define PCIE_LC_FORCE_COEFF3_LC_FORCE_POST_CURSOR_32GT_OFFSET  13
#define PCIE_LC_FORCE_COEFF3_LC_FORCE_POST_CURSOR_32GT_MASK    0x7e000u

#define PCIE_LC_FORCE_COEFF3_LC_3X3_COEFF_SEARCH_EN_32GT_OFFSET 19
#define PCIE_LC_FORCE_COEFF3_LC_3X3_COEFF_SEARCH_EN_32GT_MASK  0x80000u

#define PCIE_LC_FORCE_COEFF3_RESERVED_31_20_OFFSET             20
#define PCIE_LC_FORCE_COEFF3_RESERVED_31_20_MASK               0xfff00000u

typedef union {
  struct {
    UINT32                                 LC_FORCE_COEFF_32GT:1;
    UINT32                            LC_FORCE_PRE_CURSOR_32GT:6;
    UINT32                                LC_FORCE_CURSOR_32GT:6;
    UINT32                           LC_FORCE_POST_CURSOR_32GT:6;
    UINT32                         LC_3X3_COEFF_SEARCH_EN_32GT:1;
    UINT32                                      Reserved_31_20:12;
  } Field;
  UINT32 Value;
} PCIE_LC_FORCE_COEFF3_STRUCT;

#define SMN_PCIEPORT_PCIE_LC_FORCE_COEFF3_ADDRESS                     0x1a340418u
#define SMN_FUNC0_PCIE0NBIO0_PCIE_LC_FORCE_COEFF3_ADDRESS             0x1a340418u
#define SMN_FUNC0_PCIE0NBIO1_PCIE_LC_FORCE_COEFF3_ADDRESS             0x1a540418u
#define SMN_FUNC0_PCIE1NBIO0_PCIE_LC_FORCE_COEFF3_ADDRESS             0x1a440418u
#define SMN_FUNC0_PCIE1NBIO1_PCIE_LC_FORCE_COEFF3_ADDRESS             0x1a640418u
#define SMN_FUNC0_PCIE2NBIO0_PCIE_LC_FORCE_COEFF3_ADDRESS             0x1a740418u
#define SMN_FUNC0_PCIE2NBIO1_PCIE_LC_FORCE_COEFF3_ADDRESS             0x1a940418u
#define SMN_FUNC0_PCIE3NBIO0_PCIE_LC_FORCE_COEFF3_ADDRESS             0x1a840418u
#define SMN_FUNC0_PCIE3NBIO1_PCIE_LC_FORCE_COEFF3_ADDRESS             0x1aa40418u
#define SMN_FUNC0_PCIE5NBIO0_PCIE_LC_FORCE_COEFF3_ADDRESS             0x1ab40418u
#define SMN_FUNC1_PCIE0NBIO0_PCIE_LC_FORCE_COEFF3_ADDRESS             0x1a341418u
#define SMN_FUNC1_PCIE0NBIO1_PCIE_LC_FORCE_COEFF3_ADDRESS             0x1a541418u
#define SMN_FUNC1_PCIE1NBIO0_PCIE_LC_FORCE_COEFF3_ADDRESS             0x1a441418u
#define SMN_FUNC1_PCIE1NBIO1_PCIE_LC_FORCE_COEFF3_ADDRESS             0x1a641418u
#define SMN_FUNC1_PCIE2NBIO0_PCIE_LC_FORCE_COEFF3_ADDRESS             0x1a741418u
#define SMN_FUNC1_PCIE2NBIO1_PCIE_LC_FORCE_COEFF3_ADDRESS             0x1a941418u
#define SMN_FUNC1_PCIE3NBIO0_PCIE_LC_FORCE_COEFF3_ADDRESS             0x1a841418u
#define SMN_FUNC1_PCIE3NBIO1_PCIE_LC_FORCE_COEFF3_ADDRESS             0x1aa41418u
#define SMN_FUNC1_PCIE5NBIO0_PCIE_LC_FORCE_COEFF3_ADDRESS             0x1ab41418u
#define SMN_FUNC2_PCIE0NBIO0_PCIE_LC_FORCE_COEFF3_ADDRESS             0x1a342418u
#define SMN_FUNC2_PCIE0NBIO1_PCIE_LC_FORCE_COEFF3_ADDRESS             0x1a542418u
#define SMN_FUNC2_PCIE1NBIO0_PCIE_LC_FORCE_COEFF3_ADDRESS             0x1a442418u
#define SMN_FUNC2_PCIE1NBIO1_PCIE_LC_FORCE_COEFF3_ADDRESS             0x1a642418u
#define SMN_FUNC2_PCIE2NBIO0_PCIE_LC_FORCE_COEFF3_ADDRESS             0x1a742418u
#define SMN_FUNC2_PCIE2NBIO1_PCIE_LC_FORCE_COEFF3_ADDRESS             0x1a942418u
#define SMN_FUNC2_PCIE3NBIO0_PCIE_LC_FORCE_COEFF3_ADDRESS             0x1a842418u
#define SMN_FUNC2_PCIE3NBIO1_PCIE_LC_FORCE_COEFF3_ADDRESS             0x1aa42418u
#define SMN_FUNC2_PCIE5NBIO0_PCIE_LC_FORCE_COEFF3_ADDRESS             0x1ab42418u
#define SMN_FUNC3_PCIE0NBIO0_PCIE_LC_FORCE_COEFF3_ADDRESS             0x1a343418u
#define SMN_FUNC3_PCIE0NBIO1_PCIE_LC_FORCE_COEFF3_ADDRESS             0x1a543418u
#define SMN_FUNC3_PCIE1NBIO0_PCIE_LC_FORCE_COEFF3_ADDRESS             0x1a443418u
#define SMN_FUNC3_PCIE1NBIO1_PCIE_LC_FORCE_COEFF3_ADDRESS             0x1a643418u
#define SMN_FUNC3_PCIE2NBIO0_PCIE_LC_FORCE_COEFF3_ADDRESS             0x1a743418u
#define SMN_FUNC3_PCIE2NBIO1_PCIE_LC_FORCE_COEFF3_ADDRESS             0x1a943418u
#define SMN_FUNC3_PCIE3NBIO0_PCIE_LC_FORCE_COEFF3_ADDRESS             0x1a843418u
#define SMN_FUNC3_PCIE3NBIO1_PCIE_LC_FORCE_COEFF3_ADDRESS             0x1aa43418u
#define SMN_FUNC3_PCIE5NBIO0_PCIE_LC_FORCE_COEFF3_ADDRESS             0x1ab43418u
#define SMN_FUNC4_PCIE0NBIO0_PCIE_LC_FORCE_COEFF3_ADDRESS             0x1a344418u
#define SMN_FUNC4_PCIE0NBIO1_PCIE_LC_FORCE_COEFF3_ADDRESS             0x1a544418u
#define SMN_FUNC4_PCIE1NBIO0_PCIE_LC_FORCE_COEFF3_ADDRESS             0x1a444418u
#define SMN_FUNC4_PCIE1NBIO1_PCIE_LC_FORCE_COEFF3_ADDRESS             0x1a644418u
#define SMN_FUNC4_PCIE2NBIO0_PCIE_LC_FORCE_COEFF3_ADDRESS             0x1a744418u
#define SMN_FUNC4_PCIE2NBIO1_PCIE_LC_FORCE_COEFF3_ADDRESS             0x1a944418u
#define SMN_FUNC4_PCIE3NBIO0_PCIE_LC_FORCE_COEFF3_ADDRESS             0x1a844418u
#define SMN_FUNC4_PCIE3NBIO1_PCIE_LC_FORCE_COEFF3_ADDRESS             0x1aa44418u
#define SMN_FUNC4_PCIE5NBIO0_PCIE_LC_FORCE_COEFF3_ADDRESS             0x1ab44418u
#define SMN_FUNC5_PCIE0NBIO0_PCIE_LC_FORCE_COEFF3_ADDRESS             0x1a345418u
#define SMN_FUNC5_PCIE0NBIO1_PCIE_LC_FORCE_COEFF3_ADDRESS             0x1a545418u
#define SMN_FUNC5_PCIE1NBIO0_PCIE_LC_FORCE_COEFF3_ADDRESS             0x1a445418u
#define SMN_FUNC5_PCIE1NBIO1_PCIE_LC_FORCE_COEFF3_ADDRESS             0x1a645418u
#define SMN_FUNC5_PCIE2NBIO0_PCIE_LC_FORCE_COEFF3_ADDRESS             0x1a745418u
#define SMN_FUNC5_PCIE2NBIO1_PCIE_LC_FORCE_COEFF3_ADDRESS             0x1a945418u
#define SMN_FUNC5_PCIE3NBIO0_PCIE_LC_FORCE_COEFF3_ADDRESS             0x1a845418u
#define SMN_FUNC5_PCIE3NBIO1_PCIE_LC_FORCE_COEFF3_ADDRESS             0x1aa45418u
#define SMN_FUNC5_PCIE5NBIO0_PCIE_LC_FORCE_COEFF3_ADDRESS             0x1ab45418u
#define SMN_FUNC6_PCIE0NBIO0_PCIE_LC_FORCE_COEFF3_ADDRESS             0x1a346418u
#define SMN_FUNC6_PCIE0NBIO1_PCIE_LC_FORCE_COEFF3_ADDRESS             0x1a546418u
#define SMN_FUNC6_PCIE1NBIO0_PCIE_LC_FORCE_COEFF3_ADDRESS             0x1a446418u
#define SMN_FUNC6_PCIE1NBIO1_PCIE_LC_FORCE_COEFF3_ADDRESS             0x1a646418u
#define SMN_FUNC6_PCIE2NBIO0_PCIE_LC_FORCE_COEFF3_ADDRESS             0x1a746418u
#define SMN_FUNC6_PCIE2NBIO1_PCIE_LC_FORCE_COEFF3_ADDRESS             0x1a946418u
#define SMN_FUNC6_PCIE3NBIO0_PCIE_LC_FORCE_COEFF3_ADDRESS             0x1a846418u
#define SMN_FUNC6_PCIE3NBIO1_PCIE_LC_FORCE_COEFF3_ADDRESS             0x1aa46418u
#define SMN_FUNC6_PCIE5NBIO0_PCIE_LC_FORCE_COEFF3_ADDRESS             0x1ab46418u
#define SMN_FUNC7_PCIE0NBIO0_PCIE_LC_FORCE_COEFF3_ADDRESS             0x1a347418u
#define SMN_FUNC7_PCIE0NBIO1_PCIE_LC_FORCE_COEFF3_ADDRESS             0x1a547418u
#define SMN_FUNC7_PCIE1NBIO0_PCIE_LC_FORCE_COEFF3_ADDRESS             0x1a447418u
#define SMN_FUNC7_PCIE1NBIO1_PCIE_LC_FORCE_COEFF3_ADDRESS             0x1a647418u
#define SMN_FUNC7_PCIE2NBIO0_PCIE_LC_FORCE_COEFF3_ADDRESS             0x1a747418u
#define SMN_FUNC7_PCIE2NBIO1_PCIE_LC_FORCE_COEFF3_ADDRESS             0x1a947418u
#define SMN_FUNC7_PCIE3NBIO0_PCIE_LC_FORCE_COEFF3_ADDRESS             0x1a847418u
#define SMN_FUNC7_PCIE3NBIO1_PCIE_LC_FORCE_COEFF3_ADDRESS             0x1aa47418u
#define SMN_FUNC7_PCIE5NBIO0_PCIE_LC_FORCE_COEFF3_ADDRESS             0x1ab47418u
#define SMN_FUNC8_PCIE0NBIO0_PCIE_LC_FORCE_COEFF3_ADDRESS             0x1a348418u
#define SMN_FUNC8_PCIE0NBIO1_PCIE_LC_FORCE_COEFF3_ADDRESS             0x1a548418u
#define SMN_FUNC8_PCIE1NBIO0_PCIE_LC_FORCE_COEFF3_ADDRESS             0x1a448418u
#define SMN_FUNC8_PCIE1NBIO1_PCIE_LC_FORCE_COEFF3_ADDRESS             0x1a648418u
#define SMN_FUNC8_PCIE2NBIO0_PCIE_LC_FORCE_COEFF3_ADDRESS             0x1a748418u
#define SMN_FUNC8_PCIE2NBIO1_PCIE_LC_FORCE_COEFF3_ADDRESS             0x1a948418u
#define SMN_FUNC8_PCIE3NBIO0_PCIE_LC_FORCE_COEFF3_ADDRESS             0x1a848418u
#define SMN_FUNC8_PCIE3NBIO1_PCIE_LC_FORCE_COEFF3_ADDRESS             0x1aa48418u

/***********************************************************
* Register Name : PCIE_TX_ERR_CTRL
************************************************************/

#define PCIE_TX_ERR_CTRL_TX_GENERATE_LCRC_ERR_OFFSET           0
#define PCIE_TX_ERR_CTRL_TX_GENERATE_LCRC_ERR_MASK             0x1

#define PCIE_TX_ERR_CTRL_TX_GENERATE_ECRC_ERR_OFFSET           1
#define PCIE_TX_ERR_CTRL_TX_GENERATE_ECRC_ERR_MASK             0x2

#define PCIE_TX_ERR_CTRL_TX_GENERATE_POIS_TLP_OFFSET           2
#define PCIE_TX_ERR_CTRL_TX_GENERATE_POIS_TLP_MASK             0x4

#define PCIE_TX_ERR_CTRL_Reserved_31_3_OFFSET                  3
#define PCIE_TX_ERR_CTRL_Reserved_31_3_MASK                    0xfffffff8

typedef union {
  struct {
    UINT32                                TX_GENERATE_LCRC_ERR:1;
    UINT32                                TX_GENERATE_ECRC_ERR:1;
    UINT32                                TX_GENERATE_POIS_TLP:1;
    UINT32                                       Reserved_31_3:29;
  } Field;
  UINT32 Value;
} PCIE_TX_ERR_CTRL_STRUCT;

#define PCICFG_PCIEPORT_PCIE_TX_ERR_CTRL_OFFSET                     0x670
#define SMN_FUNC0_PCIE0NBIO0_PCIE_TX_ERR_CTRL_ADDRESS                 0x1a340670UL
#define SMN_FUNC0_PCIE0NBIO1_PCIE_TX_ERR_CTRL_ADDRESS                 0x1a540670UL
#define SMN_FUNC0_PCIE1NBIO0_PCIE_TX_ERR_CTRL_ADDRESS                 0x1a440670UL
#define SMN_FUNC0_PCIE1NBIO1_PCIE_TX_ERR_CTRL_ADDRESS                 0x1a640670UL
#define SMN_FUNC0_PCIE2NBIO0_PCIE_TX_ERR_CTRL_ADDRESS                 0x1a740670UL
#define SMN_FUNC0_PCIE2NBIO1_PCIE_TX_ERR_CTRL_ADDRESS                 0x1a940670UL
#define SMN_FUNC0_PCIE3NBIO0_PCIE_TX_ERR_CTRL_ADDRESS                 0x1a840670UL
#define SMN_FUNC0_PCIE3NBIO1_PCIE_TX_ERR_CTRL_ADDRESS                 0x1aa40670UL
#define SMN_FUNC0_PCIE5NBIO0_PCIE_TX_ERR_CTRL_ADDRESS                 0x1ab40670UL
#define SMN_FUNC0_PCIE5NBIO1_PCIE_TX_ERR_CTRL_ADDRESS                 0x1ad40670UL
#define SMN_FUNC1_PCIE0NBIO0_PCIE_TX_ERR_CTRL_ADDRESS                 0x1a341670UL
#define SMN_FUNC1_PCIE0NBIO1_PCIE_TX_ERR_CTRL_ADDRESS                 0x1a541670UL
#define SMN_FUNC1_PCIE1NBIO0_PCIE_TX_ERR_CTRL_ADDRESS                 0x1a441670UL
#define SMN_FUNC1_PCIE1NBIO1_PCIE_TX_ERR_CTRL_ADDRESS                 0x1a641670UL
#define SMN_FUNC1_PCIE2NBIO0_PCIE_TX_ERR_CTRL_ADDRESS                 0x1a741670UL
#define SMN_FUNC1_PCIE2NBIO1_PCIE_TX_ERR_CTRL_ADDRESS                 0x1a941670UL
#define SMN_FUNC1_PCIE3NBIO0_PCIE_TX_ERR_CTRL_ADDRESS                 0x1a841670UL
#define SMN_FUNC1_PCIE3NBIO1_PCIE_TX_ERR_CTRL_ADDRESS                 0x1aa41670UL
#define SMN_FUNC1_PCIE5NBIO0_PCIE_TX_ERR_CTRL_ADDRESS                 0x1ab41670UL
#define SMN_FUNC1_PCIE5NBIO1_PCIE_TX_ERR_CTRL_ADDRESS                 0x1ad41670UL
#define SMN_FUNC2_PCIE0NBIO0_PCIE_TX_ERR_CTRL_ADDRESS                 0x1a342670UL
#define SMN_FUNC2_PCIE0NBIO1_PCIE_TX_ERR_CTRL_ADDRESS                 0x1a542670UL
#define SMN_FUNC2_PCIE1NBIO0_PCIE_TX_ERR_CTRL_ADDRESS                 0x1a442670UL
#define SMN_FUNC2_PCIE1NBIO1_PCIE_TX_ERR_CTRL_ADDRESS                 0x1a642670UL
#define SMN_FUNC2_PCIE2NBIO0_PCIE_TX_ERR_CTRL_ADDRESS                 0x1a742670UL
#define SMN_FUNC2_PCIE2NBIO1_PCIE_TX_ERR_CTRL_ADDRESS                 0x1a942670UL
#define SMN_FUNC2_PCIE3NBIO0_PCIE_TX_ERR_CTRL_ADDRESS                 0x1a842670UL
#define SMN_FUNC2_PCIE3NBIO1_PCIE_TX_ERR_CTRL_ADDRESS                 0x1aa42670UL
#define SMN_FUNC2_PCIE5NBIO0_PCIE_TX_ERR_CTRL_ADDRESS                 0x1ab42670UL
#define SMN_FUNC2_PCIE5NBIO1_PCIE_TX_ERR_CTRL_ADDRESS                 0x1ad42670UL
#define SMN_FUNC3_PCIE0NBIO0_PCIE_TX_ERR_CTRL_ADDRESS                 0x1a343670UL
#define SMN_FUNC3_PCIE0NBIO1_PCIE_TX_ERR_CTRL_ADDRESS                 0x1a543670UL
#define SMN_FUNC3_PCIE1NBIO0_PCIE_TX_ERR_CTRL_ADDRESS                 0x1a443670UL
#define SMN_FUNC3_PCIE1NBIO1_PCIE_TX_ERR_CTRL_ADDRESS                 0x1a643670UL
#define SMN_FUNC3_PCIE2NBIO0_PCIE_TX_ERR_CTRL_ADDRESS                 0x1a743670UL
#define SMN_FUNC3_PCIE2NBIO1_PCIE_TX_ERR_CTRL_ADDRESS                 0x1a943670UL
#define SMN_FUNC3_PCIE3NBIO0_PCIE_TX_ERR_CTRL_ADDRESS                 0x1a843670UL
#define SMN_FUNC3_PCIE3NBIO1_PCIE_TX_ERR_CTRL_ADDRESS                 0x1aa43670UL
#define SMN_FUNC3_PCIE5NBIO0_PCIE_TX_ERR_CTRL_ADDRESS                 0x1ab43670UL
#define SMN_FUNC3_PCIE5NBIO1_PCIE_TX_ERR_CTRL_ADDRESS                 0x1ad43670UL
#define SMN_FUNC4_PCIE0NBIO0_PCIE_TX_ERR_CTRL_ADDRESS                 0x1a344670UL
#define SMN_FUNC4_PCIE0NBIO1_PCIE_TX_ERR_CTRL_ADDRESS                 0x1a544670UL
#define SMN_FUNC4_PCIE1NBIO0_PCIE_TX_ERR_CTRL_ADDRESS                 0x1a444670UL
#define SMN_FUNC4_PCIE1NBIO1_PCIE_TX_ERR_CTRL_ADDRESS                 0x1a644670UL
#define SMN_FUNC4_PCIE2NBIO0_PCIE_TX_ERR_CTRL_ADDRESS                 0x1a744670UL
#define SMN_FUNC4_PCIE2NBIO1_PCIE_TX_ERR_CTRL_ADDRESS                 0x1a944670UL
#define SMN_FUNC4_PCIE3NBIO0_PCIE_TX_ERR_CTRL_ADDRESS                 0x1a844670UL
#define SMN_FUNC4_PCIE3NBIO1_PCIE_TX_ERR_CTRL_ADDRESS                 0x1aa44670UL
#define SMN_FUNC4_PCIE5NBIO0_PCIE_TX_ERR_CTRL_ADDRESS                 0x1ab44670UL
#define SMN_FUNC4_PCIE5NBIO1_PCIE_TX_ERR_CTRL_ADDRESS                 0x1ad44670UL
#define SMN_FUNC5_PCIE0NBIO0_PCIE_TX_ERR_CTRL_ADDRESS                 0x1a345670UL
#define SMN_FUNC5_PCIE0NBIO1_PCIE_TX_ERR_CTRL_ADDRESS                 0x1a545670UL
#define SMN_FUNC5_PCIE1NBIO0_PCIE_TX_ERR_CTRL_ADDRESS                 0x1a445670UL
#define SMN_FUNC5_PCIE1NBIO1_PCIE_TX_ERR_CTRL_ADDRESS                 0x1a645670UL
#define SMN_FUNC5_PCIE2NBIO0_PCIE_TX_ERR_CTRL_ADDRESS                 0x1a745670UL
#define SMN_FUNC5_PCIE2NBIO1_PCIE_TX_ERR_CTRL_ADDRESS                 0x1a945670UL
#define SMN_FUNC5_PCIE3NBIO0_PCIE_TX_ERR_CTRL_ADDRESS                 0x1a845670UL
#define SMN_FUNC5_PCIE3NBIO1_PCIE_TX_ERR_CTRL_ADDRESS                 0x1aa45670UL
#define SMN_FUNC5_PCIE5NBIO0_PCIE_TX_ERR_CTRL_ADDRESS                 0x1ab45670UL
#define SMN_FUNC5_PCIE5NBIO1_PCIE_TX_ERR_CTRL_ADDRESS                 0x1ad45670UL
#define SMN_FUNC6_PCIE0NBIO0_PCIE_TX_ERR_CTRL_ADDRESS                 0x1a346670UL
#define SMN_FUNC6_PCIE0NBIO1_PCIE_TX_ERR_CTRL_ADDRESS                 0x1a546670UL
#define SMN_FUNC6_PCIE1NBIO0_PCIE_TX_ERR_CTRL_ADDRESS                 0x1a446670UL
#define SMN_FUNC6_PCIE1NBIO1_PCIE_TX_ERR_CTRL_ADDRESS                 0x1a646670UL
#define SMN_FUNC6_PCIE2NBIO0_PCIE_TX_ERR_CTRL_ADDRESS                 0x1a746670UL
#define SMN_FUNC6_PCIE2NBIO1_PCIE_TX_ERR_CTRL_ADDRESS                 0x1a946670UL
#define SMN_FUNC6_PCIE3NBIO0_PCIE_TX_ERR_CTRL_ADDRESS                 0x1a846670UL
#define SMN_FUNC6_PCIE3NBIO1_PCIE_TX_ERR_CTRL_ADDRESS                 0x1aa46670UL
#define SMN_FUNC6_PCIE5NBIO0_PCIE_TX_ERR_CTRL_ADDRESS                 0x1ab46670UL
#define SMN_FUNC6_PCIE5NBIO1_PCIE_TX_ERR_CTRL_ADDRESS                 0x1ad46670UL
#define SMN_FUNC7_PCIE0NBIO0_PCIE_TX_ERR_CTRL_ADDRESS                 0x1a347670UL
#define SMN_FUNC7_PCIE0NBIO1_PCIE_TX_ERR_CTRL_ADDRESS                 0x1a547670UL
#define SMN_FUNC7_PCIE1NBIO0_PCIE_TX_ERR_CTRL_ADDRESS                 0x1a447670UL
#define SMN_FUNC7_PCIE1NBIO1_PCIE_TX_ERR_CTRL_ADDRESS                 0x1a647670UL
#define SMN_FUNC7_PCIE2NBIO0_PCIE_TX_ERR_CTRL_ADDRESS                 0x1a747670UL
#define SMN_FUNC7_PCIE2NBIO1_PCIE_TX_ERR_CTRL_ADDRESS                 0x1a947670UL
#define SMN_FUNC7_PCIE3NBIO0_PCIE_TX_ERR_CTRL_ADDRESS                 0x1a847670UL
#define SMN_FUNC7_PCIE3NBIO1_PCIE_TX_ERR_CTRL_ADDRESS                 0x1aa47670UL
#define SMN_FUNC7_PCIE5NBIO0_PCIE_TX_ERR_CTRL_ADDRESS                 0x1ab47670UL
#define SMN_FUNC7_PCIE5NBIO1_PCIE_TX_ERR_CTRL_ADDRESS                 0x1ad47670UL
#define SMN_FUNC8_PCIE0NBIO0_PCIE_TX_ERR_CTRL_ADDRESS                 0x1a348670UL
#define SMN_FUNC8_PCIE0NBIO1_PCIE_TX_ERR_CTRL_ADDRESS                 0x1a548670UL
#define SMN_FUNC8_PCIE1NBIO0_PCIE_TX_ERR_CTRL_ADDRESS                 0x1a448670UL
#define SMN_FUNC8_PCIE1NBIO1_PCIE_TX_ERR_CTRL_ADDRESS                 0x1a648670UL
#define SMN_FUNC8_PCIE2NBIO0_PCIE_TX_ERR_CTRL_ADDRESS                 0x1a748670UL
#define SMN_FUNC8_PCIE2NBIO1_PCIE_TX_ERR_CTRL_ADDRESS                 0x1a948670UL
#define SMN_FUNC8_PCIE3NBIO0_PCIE_TX_ERR_CTRL_ADDRESS                 0x1a848670UL
#define SMN_FUNC8_PCIE3NBIO1_PCIE_TX_ERR_CTRL_ADDRESS                 0x1aa48670UL

#endif /* _PCIEPORT_H_ */

