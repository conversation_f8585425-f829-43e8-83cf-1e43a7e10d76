#;*****************************************************************************
#;
#; Copyright (C) 2008-2024 Advanced Micro Devices, Inc. All rights reserved.
#;
#;******************************************************************************

[Defines]
  INF_VERSION                    = 0x00010006
  BASE_NAME                      = FchSocLib
  FILE_GUID                      = 70b59ea6-8054-4c6d-a27d-be32fee067a1
  MODULE_TYPE                    = BASE
  VERSION_STRING                 = 1.0
  LIBRARY_CLASS                  = FchSocLib

[Sources]
  FchBreithornLib.c

[Packages]
  MdePkg/MdePkg.dec
  AgesaPkg/AgesaPkg.dec
  AgesaModulePkg/AgesaModuleFchPkg.dec
  AgesaModulePkg/AgesaCommonModulePkg.dec
  AgesaModulePkg/AgesaModuleDfPkg.dec

[LibraryClasses]
  BaseLib
  IdsLib
  FchBaseLib
  BaseMemoryLib
  FabricRegisterAccLib
  BaseFabricTopologyLib

[Pcd]

  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdFchI2cI3c0
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdFchI2cI3c1
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdFchI2cI3c2
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdFchI2cI3c3
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdFchI2c4
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdFchI2c5
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdFchI2c0SdaHold
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdFchI2c1SdaHold
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdFchI2c2SdaHold
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdFchI2c3SdaHold
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdFchI2c4SdaHold
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdFchI2c5SdaHold
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdFchI2c0BusFrequency
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdFchI2c1BusFrequency
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdFchI2c2BusFrequency
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdFchI2c3BusFrequency
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdFchI2c4BusFrequency
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdFchI2c5BusFrequency
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdFchI3cSdaHoldOverride
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdFchI3c0SdaHold
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdFchI3c1SdaHold
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdFchI3c2SdaHold
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdFchI3c3SdaHold
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdFchI3cSpeed
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdFchI3cPPHcnt
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdFchI2cSdaHoldOverride
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdFchI2c0SdaTxHold
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdFchI2c1SdaTxHold
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdFchI2c2SdaTxHold
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdFchI2c3SdaTxHold
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdFchI2c4SdaTxHold
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdFchI2c5SdaTxHold
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdFchI2c0SdaRxHold
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdFchI2c1SdaRxHold
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdFchI2c2SdaRxHold
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdFchI2c3SdaRxHold
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdFchI2c4SdaRxHold
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdFchI2c5SdaRxHold
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdAmdMemMaxDimmPerChannelV2
