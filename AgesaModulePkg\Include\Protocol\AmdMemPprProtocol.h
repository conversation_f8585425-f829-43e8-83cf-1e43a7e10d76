/*****************************************************************************
 * Copyright (C) 2008-2024 Advanced Micro Devices, Inc. All rights reserved.
 *
*****************************************************************************
*/
/* $NoKeywords:$ */
/**
 * @file
 *
 * AMD Memory API, and related functions.
 *
 * Contains code that initializes memory
 *
 * @xrefitem bom "File Content Label" "Release Content"
 * @e project:      AGESA
 * @e sub-project:  Mem
 * @e \$Revision:  $   @e \$Date:  $
 *
 */
#ifndef _AMD_MEM_PPR_PROTOCOL_H_
#define _AMD_MEM_PPR_PROTOCOL_H_

#include <PiDxe.h>
#include <Library/BaseLib.h>
#include <Library/HobLib.h>
#include <Library/UefiLib.h>
#include <Library/PcdLib.h>
#include <Guid/HobList.h>

#ifndef AMD_MEM_MAX_SOCKETS_SUPPORTED
 #define AMD_MEM_MAX_SOCKETS_SUPPORTED       FixedPcdGet8(PcdAmdMemMaxSocketSupportedV2)    ///< Max number of sockets in system
#endif

#ifndef AMD_MEM_MAX_DIES_PER_SOCKET
  #define AMD_MEM_MAX_DIES_PER_SOCKET        FixedPcdGet8(PcdAmdMemMaxDiePerSocketV2)    ///< Max dies per socket
#endif
#ifndef AMD_MEM_MAX_CHANNELS_PER_DIE
 #define AMD_MEM_MAX_CHANNELS_PER_DIE        FixedPcdGet8(PcdAmdMemMaxChannelPerDieV2)    ///< Max channels per die
#endif

#ifndef AMD_MEM_MAX_CHANNELS_PER_SOCKET
  #define AMD_MEM_MAX_CHANNELS_PER_SOCKET    AMD_MEM_MAX_CHANNELS_PER_DIE * AMD_MEM_MAX_DIES_PER_SOCKET    ///< Max Channels per sockets
#endif

#define AMD_MEM_MAX_DIMMS_PER_CHANNEL        2   ///< Max dimms per channel


/**
 * @brief DIMM Info Struct
 */
typedef struct _AMD_DIMM_INFO {
    UINT8  SocketId;              ///< DIMM Socket ID
    UINT8  DieId;                 ///< DIMM Die ID
    UINT8  ChannelId;             ///< DIMM Channel ID
    UINT8  Chipselect;            ///< DIMM Chipselect
} AMD_DIMM_INFO;

/**
 * @brief Post Package Repair Info Struct
 *
 */
typedef struct _AMD_PPR_INFO {
    BOOLEAN IsValidRecord;      ///< Indicates if PPR record is valid
    BOOLEAN DpprSupported;      ///< Indicates if DPPR supported
    BOOLEAN SpprSupported;      ///< Indicates if SPPR supported
    UINT8   DeviceWidth;        ///< DIMM Device width
    UINT32  SerialNumber;       ///< DIMM Serial number
    UINT8   LogicalRanks;       ///< Number of logical ranks on the DIMM
} AMD_PPR_INFO;

/**
 * @brief Data Table containing PPR info per DIMM
 */
typedef struct _AMD_MEM_PPR_DATA_TABLE {
  AMD_PPR_INFO    DimmSpdInfo[AMD_MEM_MAX_SOCKETS_SUPPORTED * AMD_MEM_MAX_CHANNELS_PER_SOCKET * AMD_MEM_MAX_DIMMS_PER_CHANNEL]; ///< PPR info per DIMM
} AMD_MEM_PPR_DATA_TABLE;

typedef struct _AMD_POST_PACKAGE_REPAIR_INFO_PROTOCOL AMD_POST_PACKAGE_REPAIR_INFO_PROTOCOL; ///< AMD Post Package Repair Protocol Info


/**
 * @brief Get the Post Package Repair Status Info
 * @param[in] This - A pointer to the PPR Protocol struct
 * @param[in] AmdDimmInfo - A pointer to the DIMM info struct for a specific DIMM
 * @param[out] PprInfo - A pointer to the PPR info struct to be filled
 * @return EFI_SUCCESS - PPR Record is valid
 * @return EFI_NOT_FOUND - PPR Record is invalid
 */
typedef
EFI_STATUS
(EFIAPI *AMD_GET_PPR_INFO) (
  IN       AMD_POST_PACKAGE_REPAIR_INFO_PROTOCOL    *This,
  IN       AMD_DIMM_INFO                            *AmdDimmInfo,
     OUT   AMD_PPR_INFO                             *PprInfo
  );

/**
 * @brief AMD Post Package Repair Protocol Info
 */
struct _AMD_POST_PACKAGE_REPAIR_INFO_PROTOCOL {
  AMD_MEM_PPR_DATA_TABLE          AmdPprArray;    ///< DIMM SPD Info needed for the repair
  AMD_GET_PPR_INFO                AmdGetPprInfo;  ///< SMM Driver to extract the Post Package Repair Status Info @see AMD_GET_PPR_INFO
};

extern EFI_GUID  gAmdPostPackageRepairInfoProtocolGuid; ///< AMD Post Package Repair Protocol Info GUID

#endif


