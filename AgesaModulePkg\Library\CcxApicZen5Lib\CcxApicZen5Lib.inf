#;*****************************************************************************
#;
#; Copyright (C) 2008-2024 Advanced Micro Devices, Inc. All rights reserved.
#;
#;******************************************************************************
#For EDKII use Only
[Defines]
  INF_VERSION                    = 0x00010006
  BASE_NAME                      = CcxApicZen5Lib
  FILE_GUID                      = 584D90D7-D45E-4EF1-A4DD-F4D1BF0A0DD5
  MODULE_TYPE                    = BASE
  VERSION_STRING                 = 1.0
  LIBRARY_CLASS                  = CcxApicZen5Lib

[Sources.common]
  CcxApicZen5Lib.c

[Packages]
  MdePkg/MdePkg.dec
  AgesaModulePkg/AgesaCommonModulePkg.dec
  AgesaModulePkg/AgesaModuleCcxPkg.dec
  AgesaModulePkg/AgesaModuleNbioPkg.dec

[LibraryClasses]
  BaseLib
  CcxBaseX86Lib

[Guids]

[Protocols]
  gAmdCoreTopologyServicesV2ProtocolGuid  #CONSUMED

[Ppis]

[Pcd]

[Depex]

