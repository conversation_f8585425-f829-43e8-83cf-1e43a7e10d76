/*********************************************************************************
;
 * Copyright (C) 2008-2024 Advanced Micro Devices, Inc. All rights reserved.
 *
;******************************************************************************
;*/

/* $NoKeywords:$ */
/**
 * @file
 *
 * FCH Function Support Definition
 *
 *
 *
 * @xrefitem bom "File Content Label" "Release Content"
 * @e project:     AGESA
 * @e sub-project: FCH
 * @e \$Revision: 311507 $   @e \$Date: 2015-01-21 14:57:51 -0800 (Wed, 21 Jan 2015) $
 *
 */

#ifndef _FCH_DATA_BLOCK_H_
#define _FCH_DATA_BLOCK_H_

#pragma pack (push, 1)

//-----------------------------------------------------------------------------
//                     FCH DEFINITIONS AND MACROS
//-----------------------------------------------------------------------------

//
// FCH Component Data Structure Definitions
//

/// PCI_ADDRESS - PCI access structure
#define PCI_ADDRESS(bus, dev, func, reg) \
                   (UINT32) ( (((UINT32)bus) << 24) + (((UINT32)dev) << 19) + (((UINT32)func) << 16) + ((UINT32)reg) )

#define CPUID_FMF        0x80000001ul  // Family Model Features information

#include <AGESA.h>
#include <FchPT.h>
#include <FchBiosRamUsage.h>

///
///  - Byte Register R/W structure
///
typedef struct _REG8_MASK {
  UINT8                 RegIndex;                       /// RegIndex - Reserved
  UINT8                 AndMask;                        /// AndMask - Reserved
  UINT8                 OrMask;                         /// OrMask - Reserved
} REG8_MASK;

///
/// Fch Run Time Parameters - todo: review C and ASL code to remove unused parameters
///
typedef struct {
  UINT64                PcieMmioBase;                              ///< PcieMmioBase
  UINT32                FchDeviceEnableMap;                        ///< FchDeviceEnableMap
                                                                   ///< Indicate FCH devices map
                                                                   ///< BIT4  - LPC   : PcdLpcEnable
                                                                   ///< BIT5  - I2C0  : FchRTDeviceEnableMap[BIT5]
                                                                   ///< BIT6  - I2C1  : FchRTDeviceEnableMap[BIT6]
                                                                   ///< BIT7  - I2C2  : FchRTDeviceEnableMap[BIT7]
                                                                   ///< BIT8  - I2C3  : FchRTDeviceEnableMap[BIT8]
                                                                   ///< BIT9  - I2C4  : FchRTDeviceEnableMap[BIT9]
                                                                   ///< BIT10 - I2C5  : FchRTDeviceEnableMap[BIT10]
                                                                   ///< BIT11 - UART0 : FchRTDeviceEnableMap[BIT11]
                                                                   ///< BIT12 - UART1 : FchRTDeviceEnableMap[BIT12]
                                                                   ///< BIT13 - I3C1  : FchRTDeviceEnableMap[BIT13]
                                                                   ///< BIT14 - I3C2  : FchRTDeviceEnableMap[BIT14]
                                                                   ///< BIT15 - I3C3  : FchRTDeviceEnableMap[BIT15]
                                                                   ///< BIT16 - UART2 : FchRTDeviceEnableMap[BIT16]
                                                                   ///< BIT18 - SD    : PcdEmmcEnable and PcdEmmcType < 5
                                                                   ///< BIT21 - I3C0  : FchRTDeviceEnableMap[BIT21]
                                                                   ///< BIT26 - UART3 : FchRTDeviceEnableMap[BIT26]
                                                                   ///< BIT27 - eSPI  : PcdEspiEnable
                                                                   ///< BIT28 - eMMC  : PcdEmmcEnable
  UINT32                FchDeviceD3ColdMap;                        ///< FchDeviceD3ColdMap - Offset (0xC)
  UINT16                Al2AhbLegacyUartIoEnable;                  ///< Al2Ahb Legacy Uart Io Enable
  UINT8                 Uart0Irq;                                  ///< Uart 0 Irq
  UINT8                 Uart1Irq;                                  ///< Uart 1 Irq
  UINT8                 Uart2Irq;                                  ///< Uart 2 Irq
  UINT8                 Uart3Irq;                                  ///< Uart 3 Irq
  UINT8                 I2c0Irq;                                   ///< I2c 0 Irq
  UINT8                 I2c1Irq;                                   ///< I2c 1 Irq
  UINT8                 I2c2Irq;                                   ///< I2c 2 Irq
  UINT8                 I2c3Irq;                                   ///< I2c 3 Irq
  UINT8                 I2c4Irq;                                   ///< I2c 4 Irq
  UINT8                 I2c5Irq;                                   ///< I2c 5 Irq
  UINT32                FchDeviceEnableMapEx;                      ///< FchDeviceEnableMap extension  - Offset(0x2A)
                                                                   ///< BIT00 - I3C0 I3C Mode
                                                                   ///< BIT01 - I3C1 I3C Mode
                                                                   ///< BIT02 - I3C2 I3C Mode
                                                                   ///< BIT03 - I3C3 I3C Mode
                                                                   ///< BIT04-31 - Reserved
  UINT16                FchAcpiDeviceInvisibleMap;                 ///< FchAcpiDeviceInvisibleMap - Offset (0x2E)
                                                                   ///< BIT0 = 1: UART0 ACPI Device is invisible
                                                                   ///< BIT1 = 1: UART1 ACPI Device is invisible
                                                                   ///< BIT2 = 1: UART2 ACPI Device is invisible
                                                                   ///< BIT3 = 1: UART3 ACPI Device is invisible
                                                                   ///< BIT4 = 1: UART4 ACPI Device is invisible
                                                                   ///< BIT8  = 1: UART0 Legacy IO ACPI Device is invisible
                                                                   ///< BIT9  = 1: UART1 Legacy IO ACPI Device is invisible
                                                                   ///< BIT10 = 1: UART2 Legacy IO ACPI Device is invisible
                                                                   ///< BIT11 = 1: UART3 Legacy IO ACPI Device is invisible
UINT16                FchAcpiDeviceInvisibleMapEx;                 //FchAcpiDeviceInvisibleMapEx - Offset (0x30)
                                                                   ///< BIT0 = 1: I2C0 ACPI Device is invisible
                                                                   ///< BIT1 = 1: I2C1 ACPI Device is invisible
                                                                   ///< BIT2 = 1: I2C2 ACPI Device is invisible
                                                                   ///< BIT3 = 1: I2C3 ACPI Device is invisible
                                                                   ///< BIT4 = 1: I2C4 ACPI Device is invisible
                                                                   ///< BIT5 = 1: I2C5 ACPI Device is invisible
                                                                   ///< BIT6 = 1: I3C0 ACPI Device is invisible
                                                                   ///< BIT7 = 1: I3C1 ACPI Device is invisible
                                                                   ///< BIT8 = 1: I3C2 ACPI Device is invisible
                                                                   ///< BIT9 = 1: I3C3 ACPI Device is invisible
UINT8                 DimmsPerChannel;                             ///< Identify Dimms per channel    - Offset (0x32)
} FCH_RUNTIME;

///
/// SD structure - todo to remove unused parameters
///
typedef struct {
  SD_MODE               SdConfig;                       ///< SD Mode configuration
                                                        ///   @li <b>00</b> - Disabled
} FCH_SD;

///
/// SPI_LPC structure
///
typedef struct {
  BOOLEAN               LpcEnable;                      ///< LPC enable
  BOOLEAN               LpcMsiEnable;                   ///< LPC MSI capability
  UINT32                LpcSsid;                        ///< LPC Subsystem ID
  UINT32                RomBaseAddress;                 ///< SpiRomBaseAddress
                                                        ///   @par
                                                        ///   SPI ROM BASE Address
                                                        ///
  UINT8                 SpiSpeed;                       ///< SpiSpeed - Spi Frequency
                                                        ///  @par
                                                        ///  SPI Speed [1.0] - the clock speed for non-fast read command
                                                        ///   @li <b>00</b> - 66Mhz
                                                        ///   @li <b>01</b> - 33Mhz
                                                        ///   @li <b>10</b> - 22Mhz
                                                        ///   @li <b>11</b> - 16.5Mhz
                                                        ///
  UINT8                 SpiFastSpeed;                   ///< FastSpeed  - Spi Fast Speed feature
                                                        ///  SPIFastSpeed [1.0] - the clock speed for Fast Speed Feature
                                                        ///   @li <b>00</b> - 66Mhz
                                                        ///   @li <b>01</b> - 33Mhz
                                                        ///   @li <b>10</b> - 22Mhz
                                                        ///   @li <b>11</b> - 16.5Mhz
                                                        ///
  UINT8                 WriteSpeed;                     ///< WriteSpeed - Spi Write Speed
                                                        /// @par
                                                        ///  WriteSpeed [1.0] - the clock speed for Spi write command
                                                        ///   @li <b>00</b> - 66Mhz
                                                        ///   @li <b>01</b> - 33Mhz
                                                        ///   @li <b>10</b> - 22Mhz
                                                        ///   @li <b>11</b> - 16.5Mhz
                                                        ///
  BOOLEAN               LpcClk0;                        ///< Lclk0En - LPCCLK0
                                                        /// @par
                                                        ///  LPC Clock 0 mode
                                                        ///  @li <b>0</b> - forced to stop
                                                        ///  @li <b>1</b> - functioning with CLKRUN protocol
  BOOLEAN               LpcClk1;                        ///< Lclk1En - LPCCLK1
                                                        /// @par
                                                        ///  LPC Clock 1 mode
                                                        ///  @li <b>0</b> - forced to stop
                                                        ///  @li <b>1</b> - functioning with CLKRUN protocol
} FCH_SPI_LPC;


///
/// Hpet structure
///
typedef struct {
  BOOLEAN               HpetEnable;                     ///< HPET function switch

  BOOLEAN               HpetMsiDis;                     ///< HpetMsiDis - South Bridge HPET MSI Configuration
                                                        ///   @par
                                                        ///   @li <b>1</b> - disable
                                                        ///   @li <b>0</b> - enable
} FCH_HPET;


///
/// GCPU related parameters
///
typedef struct {
  UINT8                 AcDcMsg;                        ///< Send a message to CPU to indicate the power mode (AC vs battery)
                                                        ///   @li <b>1</b> - disable
                                                        ///   @li <b>0</b> - enable

  UINT8                 TimerTickTrack;                 ///< Send a message to CPU to indicate the latest periodic timer interval
                                                        ///   @li <b>1</b> - disable
                                                        ///   @li <b>0</b> - enable

  UINT8                 ClockInterruptTag;              ///< Mark the periodic timer interrupt
                                                        ///   @li <b>1</b> - disable
                                                        ///   @li <b>0</b> - enable
} FCH_GCPU;


///
/// Timer
///
typedef struct {
  BOOLEAN               Enable;                         ///< Whether to register timer SMI in POST
  BOOLEAN               StartNow;                       ///< Whether to start the SMI immediately during registration
  UINT16                CycleDuration;                  ///< [14:0] - Actual cycle duration = CycleDuration + 1
} TIMER_SMI;

///
/// CPPC support
///
typedef struct {
  BOOLEAN               CppcSupport;                     ///< CPPC feature support, FCH need configure SCI for CPPC
  UINT32                SciBit;                          ///< SCI bit reserved for CPPC
} FCH_CPPC;

///
/// MISC structure
///
typedef struct {
  BOOLEAN               NativePcieSupport;              /// PCIe NativePcieSupport - Debug function. 1:Enabled, 0:Disabled
  BOOLEAN               S3Resume;                       /// S3Resume - Flag of ACPI S3 Resume.
  TIMER_SMI             LongTimer;                      ///< Long Timer SMI
  TIMER_SMI             ShortTimer;                     ///< Short Timer SMI
  BOOLEAN               NoneSioKbcSupport;              ///< NoneSioKbcSupport - No KBC/SIO controller ( Turn on Inchip KBC emulation function )
  FCH_CPPC              Cppc;                           ///< FCH CPPC support
//Jeff move to driver  BOOLEAN               FchiLa1MTraceMemoryEn;          ///< FchiLa1MTraceMemoryEn - Fch iLa 1M Trace Memory Enable
//  UINT32                FchiLa1MTraceMemoryBase;        ///< FchiLa1MTraceMemoryBase - Fch iLa 1M Trace Memory Base
  BOOLEAN               SerialDebugBusEnable;           ///< Serial Debug Bus Enable
} FCH_MISC;


///
/// SMBus structure
///
typedef struct {
  UINT32                SmbusSsid;                      ///< SMBUS controller Subsystem ID
} FCH_SMBUS;


///
/// Acpi structure
///
typedef struct {
  UINT16                Smbus0BaseAddress;              ///< Smbus0BaseAddress
                                                        ///   @par
                                                        ///  Smbus BASE Address
                                                        ///
  UINT16                Smbus1BaseAddress;              ///< Smbus1BaseAddress
                                                        ///   @par
                                                        ///  Smbus1 (ASF) BASE Address
                                                        ///
  UINT16                SioPmeBaseAddress;              ///< SioPmeBaseAddress
                                                        ///   @par
                                                        ///  SIO PME BASE Address
                                                        ///
  UINT32                WatchDogTimerBase;              ///< WatchDogTimerBase
                                                        ///   @par
                                                        ///  Watch Dog Timer Address
                                                        ///
  UINT16                AcpiPm1EvtBlkAddr;              ///< AcpiPm1EvtBlkAddr
                                                        ///   @par
                                                        ///  ACPI PM1 event block Address
                                                        ///
  UINT16                AcpiPm1CntBlkAddr;              ///< AcpiPm1CntBlkAddr
                                                        ///   @par
                                                        ///  ACPI PM1 Control block Address
                                                        ///
  UINT16                AcpiPmTmrBlkAddr;               ///< AcpiPmTmrBlkAddr
                                                        ///   @par
                                                        ///  ACPI PM timer block Address
                                                        ///
  UINT16                CpuControlBlkAddr;              ///< CpuControlBlkAddr
                                                        ///   @par
                                                        ///  ACPI CPU control block Address
                                                        ///
  UINT16                AcpiGpe0BlkAddr;                ///< AcpiGpe0BlkAddr
                                                        ///   @par
                                                        ///  ACPI GPE0 block Address
                                                        ///
  UINT16                SmiCmdPortAddr;                 ///< SmiCmdPortAddr
                                                        ///   @par
                                                        ///  SMI command port Address
                                                        ///
  UINT16                AcpiPmaCntBlkAddr;              ///< AcpiPmaCntBlkAddr
                                                        ///   @par
                                                        ///  ACPI PMA Control block Address
                                                        ///
  BOOLEAN               SpreadSpectrum;                 ///< SpreadSpectrum
                                                        ///  @par
                                                        ///  Spread Spectrum function
                                                        ///   @li <b>0</b> - disable
                                                        ///   @li <b>1</b> - enable
                                                        ///
  POWER_FAIL            PwrFailShadow;                  ///< PwrFailShadow = PM_Reg: 5Bh [3:0]
                                                        ///  @par
                                                        ///   @li  <b>00</b> - Always off
                                                        ///   @li  <b>01</b> - Always on
                                                        ///   @li  <b>11</b> - Use previous
                                                        ///
  UINT8                 StressResetMode;                ///< StressResetMode 01-10
                                                        ///   @li  <b>00</b> - Disabed
                                                        ///   @li  <b>01</b> - Io Write 0x64 with 0xfe
                                                        ///   @li  <b>10</b> - Io Write 0xcf9 with 0x06
                                                        ///   @li  <b>11</b> - Io Write 0xcf9 with 0x0e
                                                        ///
  BOOLEAN               MtC1eEnable;                    /// MtC1eEnable - Enable MtC1e
  VOID*                 OemProgrammingTablePtr;         /// Pointer of ACPI OEM table
  UINT8                 SpreadSpectrumOptions;          /// SpreadSpectrumOptions - Spread Spectrum Option
  BOOLEAN               NoClearThermalTripSts;          /// Skip clearing ThermalTrip status
  UINT32                FchAcpiMmioBase;                ///< FCH ACPI MMIO Base
  BOOLEAN               FchAlinkRasSupport;             ///< FCH A-Link parity error support
  UINT32                I2c0SdaHold;                    ///< I2C0 SDA_HOLD
  UINT32                I2c1SdaHold;                    ///< I2C1 SDA_HOLD
  UINT32                I2c2SdaHold;                    ///< I2C2 SDA_HOLD
  UINT32                I2c3SdaHold;                    ///< I2C3 SDA_HOLD
  UINT32                I2c4SdaHold;                    ///< I2C4 SDA_HOLD
  UINT32                I2c5SdaHold;                    ///< I2C5 SDA_HOLD
  BOOLEAN               SpdHostCtrlRelease;             ///< Release SPD Host Ctrl
  BOOLEAN               DimmTelemetry;                  ///< Send message to PMFW to start DIMM telemetry
  BOOLEAN               FchAoacProgramEnable;           ///< Enable/disable AOAC init programming
  BOOLEAN               FchSxEntryXhciPmeEn;            ///< Enable XHCI controller PME bit in Sx Entry
} FCH_ACPI;


///
/// _ABTblEntry - AB link register table R/W structure
///
typedef struct _AB_TBL_ENTRY {
  UINT8                 RegType;                        /// RegType  : AB Register Type (ABCFG, AXCFG and so on)
  UINT32                RegIndex;                       /// RegIndex : AB Register Index
  UINT32                RegMask;                        /// RegMask  : AB Register Mask
  UINT32                RegData;                        /// RegData  : AB Register Data
} AB_TBL_ENTRY;

///
/// AB structure
///
typedef struct {
  UINT8                 ALinkClkGateOff;                /// Alink Clock Gate-Off function - 0:disable, 1:enable *KR
  UINT8                 BLinkClkGateOff;                /// Blink Clock Gate-Off function - 0:disable, 1:enable *KR
  UINT8                 AbClockGating;                  /// AB Clock Gating - 0:disable, 1:enable *KR *CZ
  UINT8                 SlowSpeedAbLinkClock;           /// Slow Speed AB Link Clock - 0:disable, 1:enable *KR
  BOOLEAN               ResetCpuOnSyncFlood;            /// Reset Cpu On Sync Flood - 0:disable, 1:enable *KR
  BOOLEAN               AbDmaMemoryWrtie3264B;          /// AB DMA Memory Write 32/64 BYTE Support *KR only
  BOOLEAN               AbMemoryPowerSaving;            /// AB Memory Power Saving *KR *CZ
  BOOLEAN               SbgMemoryPowerSaving;           /// SBG Memory Power Saving *KR *CZ
  BOOLEAN               SbgClockGating;                 /// SBG Clock Gate *CZ
  BOOLEAN               XdmaDmaWrite16ByteMode;         /// XDMA DMA Write 16 byte mode *CZ
  BOOLEAN               XdmaMemoryPowerSaving;          /// XDMA memory power saving *CZ
  UINT8                 XdmaPendingNprThreshold;        /// XDMA PENDING NPR THRESHOLD *CZ
  BOOLEAN               XdmaDncplOrderDis;              /// XDMA DNCPL ORDER DIS *CZ
  BOOLEAN               SdphostBypassDataPack;          /// SdphostBypassDataPack
  BOOLEAN               SdphostDisNpmwrProtect;         /// Disable NPMWR interleaving protection
} FCH_AB;


/**
 * PCIE_CAP_ID - PCIe Cap ID
 *
 */
#define  PCIE_CAP_ID    0x10


typedef struct {
  UINT32                Usb20OcPin; ///< XHCI OverCurrent OC Pin, [3:0] - Port0, [7:4] Port1, etc.
  UINT16                Usb31OcPin; ///< XHCI OverCurrent OC Pin, [3:0] - Port0, [7:4] Port1, etc.
} XHCI_OC;

typedef struct {
  UINT8                    COMPDSTUNE;                             ///< COMPDSTUNE
  UINT8                    SQRXTUNE;                               ///< SQRXTUNE
  UINT8                    TXFSLSTUNE;                             ///< TXFSLSTUNE
  UINT8                    TXPREEMPAMPTUNE;                        ///< TXPREEMPAMPTUNE
  UINT8                    TXPREEMPPULSETUNE;                      ///< TXPREEMPPULSETUNE
  UINT8                    TXRISETUNE;                             ///< TXRISETUNE
  UINT8                    TXVREFTUNE;                             ///< TXVREFTUNE
  UINT8                    TXHSXVTUNE;                             ///< TXHSXVTUNE
  UINT8                    TXRESTUNE;                              ///< TXRESTUNE
} FCH_USB20_PHY;

typedef struct {
  UINT8                    RX_ANA_IQ_PHASE_ADJUST;                 ///< RX_ANA_IQ_PHASE_ADJUST
  UINT8                    RX_EQ_DELTA_IQ_OVRD_EN;                 ///< RX_EQ_DELTA_IQ_OVRD_EN
  UINT8                    RX_EQ_DELTA_IQ_OVRD_VAL;                ///< RX_EQ_DELTA_IQ_OVRD_VAL
  UINT8                    RX_IQ_PHASE_ADJUST;                     ///< RX_IQ_PHASE_ADJUST
  UINT8                    TX_VBOOST_LVL_EN;                       ///< TX_VBOOST_LVL_EN (SUP_DIG_LVL_OVRD_IN)
  UINT8                    TX_VBOOST_LVL;                          ///< TX_VBOOST_LVL (SUP_DIG_LVL_OVRD_IN)
  UINT8                    RX_VREF_CTRL_EN;                        ///< RX_VREF_CTRL_EN (SUP_DIG_LVL_OVRD_IN)
  UINT8                    RX_VREF_CTRL;                           ///< RX_VREF_CTRL (SUP_DIG_LVL_OVRD_IN)
  UINT8                    TX_VBOOST_LVL_EN_X;                     ///< TX_VBOOST_LVL_EN (SUPX_DIG_LVL_OVRD_IN)
  UINT8                    TX_VBOOST_LVL_X;                        ///< TX_VBOOST_LVL (SUPX_DIG_LVL_OVRD_IN)
  UINT8                    RX_VREF_CTRL_EN_X;                      ///< RX_VREF_CTRL_EN (SUPX_DIG_LVL_OVRD_IN)
  UINT8                    RX_VREF_CTRL_X;                         ///< RX_VREF_CTRL (SUPX_DIG_LVL_OVRD_IN)
} FCH_USB31_PHY;

typedef struct {
  UINT8                         Version_Major;                     ///< USB IP version
  UINT8                         Version_Minor;                     ///< USB IP version
  UINT8                         TableLength;                       ///< TableLength
  UINT8                         Reserved0;
  UINT8                         Usb20PhyEnable;                    ///< Enable USB 2.0 PHY setting
  FCH_USB20_PHY                 Usb20PhyPort[4];                   ///< USB 2.0 Driving Strength
  UINT8                         Reserved1;                         ///< Device Removable: bit0 - Port0, bit1 - Port1, etc.
  UINT8                         S1Usb20PhyEnable;                  ///< Enable USB 2.0 PHY setting
  FCH_USB20_PHY                 S1Usb20PhyPort[4];                 ///< USB 2.0 Driving Strength
  UINT8                         Usb31PhyEnable;                    ///< Enable USB 3.1 PHY setting
  FCH_USB31_PHY                 Usb31PhyPort[4];                   ///< USB 3.1 Driving Strength
  UINT8                         S1Usb31PhyEnable;                  ///< Enable USB 3.1 PHY setting
  FCH_USB31_PHY                 S1Usb31PhyPort[4];                 ///< USB 3.1 Driving Strength
} FCH_USB_OEM_PLATFORM_TABLE;

///
/// FCH USB sturcture
///
typedef struct {
  BOOLEAN               Xhci0Enable;                    ///< XHCI0 controller enable
  BOOLEAN               Xhci1Enable;                    ///< XHCI1 controller enable
  UINT32                XhciSsid;                       ///< XHCI SSID
} FCH_USB;

///
/// ASF structure
///
typedef struct {
  BOOLEAN               DisableMaster;                  ///Disable ASF Master
  BOOLEAN               DisableSlave;                  ///Disable ASF Slave
} FCH_ASF;



/// Private: FCH_DATA_BLOCK_RESET
typedef struct _FCH_RESET_DATA_BLOCK {
  //
  // FCH generic configuration
  //
  FCH_PLATFORM_POLICY   FchBldCfg;                      /// Build Options
  UINT32                FchAcpiMmioBase;                ///< FCH ACPI MMIO Base
  UINT8                 FchIoApicId;                    ///< IoApicID
  FCH_ASF               FchAsfCfg;
  BOOLEAN               LegacyFree;                     ///< Legacy Free - 0:disable, 1:enable
  BOOLEAN               FchOscout1ClkContinous;         ///< FCH OSCOUT1_CLK Continous
  BOOLEAN               WdtEnable;                      ///< enable FCH WatchDog Timer (WDT)
  BOOLEAN               BootTimerEnable;                ///< enable FCH Boot Timer
  UINT8                 BootTimerResetType;             ///< FCH Boot Timer Reset Type -- 0:Warm Reset, 1:Cold Reset
  BOOLEAN               SerialIrqEnable;                ///< Enable the serial IRQ function
  BOOLEAN               ToggleAllPwrGoodOnCf9;          ///< Toggle All PwrGood On Cf9
  //
  // LPC/SPI
  //
  BOOLEAN               LpcEnable;                      ///< Lpc controller enable
  BOOLEAN               LpcClk0;                        ///< Lpc Clock0 enable
  BOOLEAN               LpcClk1;                        ///< Lpc Clock1 enable
  UINT8                 LpcClockDriveStrength;          ///< Lpc Clock Drive Strength
  UINT8                 LpcClockDriveStrengthRiseTime;  ///< Lpc Clock Drive Strength Rise Time
  UINT8                 LpcClockDriveStrengthFallTime;  ///< Lpc Clock Drive Strength Fall Time
  UINT8                 SpiSpeed;                       ///< SPI NormSpeed: 1-66MHz, 2-33MHz, 3-22MHz, 4-16.5MHz, 5-100Mhz
  UINT8                 FastSpeed;                      ///< SPI FastSpeed: 1-66MHz, 2-33MHz, 3-22MHz, 4-16.5MHz, 5-100Mhz, 6-800KHz
  UINT8                 WriteSpeed;                     ///< SPI Write Speed: 1-66MHz, 2-33MHz, 3-22MHz, 4-16.5MHz, 5-100Mhz
  UINT8                 SpiTpmSpeed;                    ///< SPI TPM Read/Write Speed
                                                        ///  @li   <b>1</b> - 66MHz
                                                        ///  @li   <b>2</b> - 33MHz
                                                        ///  @li   <b>3</b> - 22MHz
                                                        ///  @li   <b>4</b> - 16.5MHz
                                                        ///  @li   <b>5</b> - 100MHz
                                                        ///  @li   <b>6</b> - 800KHz
  //
  // I2C/I3C/UART
  //
  UINT32                I2CEnable;                      ///< I2C controller enable.
  UINT32                I2CSdaHold[6];                  ///< I2C sda hold time
  // USB
  BOOLEAN               Xhci0Enable;                    ///< XHCI0 controller function
                                                        ///<   @li <b>FALSE</b> - XHCI0 controller disabled
                                                        ///<   @li <b>TRUE</b> - XHCI0 controller enabled

  BOOLEAN               Xhci1Enable;                    ///< XHCI1 controller function
                                                        ///<   @li <b>FALSE</b> - XHCI1 controller disabled
                                                        ///<   @li <b>TRUE</b> - XHCI1 controller enabled
  BOOLEAN               Xhci2Enable;
  BOOLEAN               Xhci3Enable;
  UINT32                Xhci0DevRemovable;              ///< XHCI Device removable
  BOOLEAN               XhciOcPolarityCfgLow;           ///< Over-current input polarity configure, 0-High, 1-Low.
  BOOLEAN               DisableXhciPortLate;            ///< Disable Xhci Port on Late Phase
  UINT32                XhciUsb3PortDisable;            ///< XHCI Usb3 Port Disable, Bit0-3 - Port0-3
  UINT32                XhciUsb2PortDisable;            ///< XHCI Usb2 Port Disable, Bit0-3 - Port0-3
  XHCI_OC               XhciOCpinSelect[2];             ///< XHCI OverCurrent OC Pin, [3:0] - Port0, [7:4] Port1, etc.
  UINT8                 Usb3PortForceGen1;              ///< Xhci USB3.1 port force to Gen 1 bitmap
  VOID*                 OemUsbConfigurationTablePtr;    /// Pointer of USB OEM table
  BOOLEAN               UsbSparseModeEnable;            ///< FCHUSBDWCUSB31CNTR0::GUCTL bit 17 SprsCtrlTransEn
  BOOLEAN               UsbDbgSCPipeSwitchEnable;       ///< Enable/Disable USB DbgSC PipeSwitchEnable
  //
  // PT
  //
  FCH_PT                Promontory;                     ///< Promontory structure
} FCH_RESET_DATA_BLOCK;


/// Private: FCH_DATA_BLOCK
typedef struct _FCH_DATA_BLOCK {
  FCH_RUNTIME           FchRunTime;                     ///< FCH Run Time Parameters
  FCH_ACPI              HwAcpi;                         ///< ACPI structure
  FCH_AB                Ab;                             ///< AB structure
  FCH_SMBUS             Smbus;                          ///< SMBus structure
  FCH_SPI_LPC           Spi;                            ///< SPI structure
  FCH_SD                Sd;                             ///< SD structure
  FCH_USB               Usb;                            ///< USB structure
  FCH_HPET              Hpet;                           ///< HPET structure
  FCH_GCPU              Gcpu;                           ///< GCPU structure
  FCH_MISC              Misc;                           ///< MISC structure
  VOID*                 PostOemGpioTable;               /// Pointer of Post OEM GPIO table
  FCH_PT                Promontory;                     ///< Promontory structure
  FCH_RESET_DATA_BLOCK  FchResetDataBlock;             ///< Reset data structure
} FCH_DATA_BLOCK;

#pragma pack (pop)

#endif



