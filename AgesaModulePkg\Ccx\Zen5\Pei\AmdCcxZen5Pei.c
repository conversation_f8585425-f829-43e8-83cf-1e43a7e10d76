/*****************************************************************************
 *
 * Copyright (C) 2008-2024 Advanced Micro Devices, Inc. All rights reserved.
 *
 *******************************************************************************
 */
/*----------------------------------------------------------------------------------------
 *                             M O D U L E S    U S E D
 *----------------------------------------------------------------------------------------
 */
#include <PiPei.h>
#include <AGESA.h>
#include "AmdCcxZen5Pei.h"
#include "CcxZen5CacheInit.h"
#include "CcxZen5BrandString.h"
#include "CcxRegistersZen5.h"
#include <Library/CcxRolesLib.h>
#include <Library/CcxResetTablesLibV2.h>
#include <Library/CcxApicLib.h>
#include <Library/CcxPeiSmmAccessLib.h>
#include <Library/AmdIdsHookLib.h>
#include <Library/IdsLib.h>
#include <Library/FchBaseLib.h>
#include <Library/AmdCapsuleLib.h>
#include <Library/CcxSetMcaLib.h>
#include <Library/CcxHaltLib.h>
#include <Library/BaseMemoryLib.h>
#include <Library/MemoryAllocationLib.h>
#include <Library/AmdPspBaseLibV2.h>
#include <Library/CcxMicrocodePatchLib.h>
#include <Library/FabricWdtLib.h>
#include <Library/HobLib.h>
#include <Library/IoLib.h>
#include <Library/AmdSocBaseLib.h>
#include <Ppi/AmdCcxPpi.h>
#include <Ppi/SocZen5ServicesPpi.h>
#include <Ppi/AmdCoreTopologyServicesV3Ppi.h>
#include <Ppi/FabricTopologyServices2Ppi.h>
#include <Ppi/ReadOnlyVariable2.h>
#include <Ppi/SmmAccess.h>
#include <Ppi/NbioIommuFeaturePpi.h>
#include <Guid/CcxIommuFeatureData.h>
#include <PspResumeMemInfo.h>
#include <cpuRegisters.h>
#include <CcxCommon.h>
#include <Filecode.h>

/*----------------------------------------------------------------------------------------
 *                   D E F I N I T I O N S    A N D    M A C R O S
 *----------------------------------------------------------------------------------------
 */
#define FILECODE CCX_ZEN5_PEI_AMDCCXZEN5PEI_FILECODE

/*----------------------------------------------------------------------------------------
 *                           G L O B A L   V A R I A B L E S
 *----------------------------------------------------------------------------------------
 */
STATIC PEI_AMD_CCX_INIT_COMPLETE_PPI mCcxPeiInitCompletePpi = {
  AMD_CCX_PPI_REVISION
};

STATIC EFI_PEI_PPI_DESCRIPTOR mCcxPeiInitCompletePpiList =
{
  (EFI_PEI_PPI_DESCRIPTOR_PPI | EFI_PEI_PPI_DESCRIPTOR_TERMINATE_LIST),
  &gAmdCcxPeiInitCompletePpiGuid,
  &mCcxPeiInitCompletePpi
};

EFI_PEI_NOTIFY_DESCRIPTOR mNotifyList[] = {
  {
    EFI_PEI_PPI_DESCRIPTOR_NOTIFY_CALLBACK | EFI_PEI_PPI_DESCRIPTOR_TERMINATE_LIST,
    &gEfiPeiMpServicesPpiGuid,
    CcxInitWithMpServices,
  }
};

AP_MTRR_SETTINGS mApMtrrSyncList[] = {
  { MSR_MMIO_CFG_BASE,        0x0000000000000000  },
  { AMD_AP_MTRR_FIX64k_00000, 0x0000000000000000  },
  { AMD_AP_MTRR_FIX16k_80000, 0x0000000000000000  },
  { AMD_AP_MTRR_FIX16k_A0000, 0x0000000000000000  },
  { AMD_AP_MTRR_FIX4k_C0000,  0x0000000000000000  },
  { AMD_AP_MTRR_FIX4k_C8000,  0x0000000000000000  },
  { AMD_AP_MTRR_FIX4k_D0000,  0x0000000000000000  },
  { AMD_AP_MTRR_FIX4k_D8000,  0x0000000000000000  },
  { AMD_AP_MTRR_FIX4k_E0000,  0x0000000000000000  },
  { AMD_AP_MTRR_FIX4k_E8000,  0x0000000000000000  },
  { AMD_AP_MTRR_FIX4k_F0000,  0x0000000000000000  },
  { AMD_AP_MTRR_FIX4k_F8000,  0x0000000000000000  },
  { AMD_MTRR_VARIABLE_BASE0,  0x0000000000000000  },
  { AMD_MTRR_VARIABLE_MASK0,  0x0000000000000000  },
  { AMD_MTRR_VARIABLE_BASE1,  0x0000000000000000  },
  { AMD_MTRR_VARIABLE_MASK1,  0x0000000000000000  },
  { AMD_MTRR_VARIABLE_BASE2,  0x0000000000000000  },
  { AMD_MTRR_VARIABLE_MASK2,  0x0000000000000000  },
  { AMD_MTRR_VARIABLE_BASE3,  0x0000000000000000  },
  { AMD_MTRR_VARIABLE_MASK3,  0x0000000000000000  },
  { AMD_MTRR_VARIABLE_BASE4,  0x0000000000000000  },
  { AMD_MTRR_VARIABLE_MASK4,  0x0000000000000000  },
  { AMD_MTRR_VARIABLE_BASE5,  0x0000000000000000  },
  { AMD_MTRR_VARIABLE_MASK5,  0x0000000000000000  },
  { AMD_MTRR_VARIABLE_BASE6,  0x0000000000000000  },
  { AMD_MTRR_VARIABLE_MASK6,  0x0000000000000000  },
  { AMD_MTRR_VARIABLE_BASE7,  0x0000000000000000  },
  { AMD_MTRR_VARIABLE_MASK7,  0x0000000000000000  },
  { 0x000002FF,               0x0000000000000000  },
  { CPU_LIST_TERMINAL,        0x0000000000000000  }
};

AP_MSR_SYNC mApMsrSyncList[] = {
  { 0xC0010030, 0x0000000000000000, 0xFFFFFFFFFFFFFFFF                 },  // CcxZen5Brandstring
  { 0xC0010031, 0x0000000000000000, 0xFFFFFFFFFFFFFFFF                 },  // CcxZen5Brandstring
  { 0xC0010032, 0x0000000000000000, 0xFFFFFFFFFFFFFFFF                 },  // CcxZen5Brandstring
  { 0xC0010033, 0x0000000000000000, 0xFFFFFFFFFFFFFFFF                 },  // CcxZen5Brandstring
  { 0xC0010034, 0x0000000000000000, 0xFFFFFFFFFFFFFFFF                 },  // CcxZen5Brandstring
  { 0xC0010035, 0x0000000000000000, 0xFFFFFFFFFFFFFFFF                 },  // CcxZen5BrandString
  { 0xC0011022, 0x0000000000000000, 0xFFFFFFFFFFFFFFFF                 },  // CcxZen5Prefetcher
  { 0xC001102B, 0x0000000000000000, 0xFFFFFFFFFFFFFFFF                 },  // CcxZen5Prefetcher
  { 0xC00110E7, 0x0000000000000000, 0xFFFFFFFFFFFFFFFF                 },  // CcxZen5Prefetcher
  { 0xC0010010, 0x0000000000000000, 0xFFFFFFFFFFFFFFFF                 },  // CcxZen5EnableSmeeHmkee
  { 0xC0010296, 0x0000000000000000, 0xFFFFFFFFFFFFFFFF                 },  // CcxZen5InitializeC6
  { 0xC0010298, 0x0000000000000000, 0xFFFFFFFFFFFFFFFF                 },  // CcxZen5InitializeC6
  { 0xC0010073, 0x0000000000000000, 0xFFFFFFFFFFFFFFFF                 },  // CcxZen5InitializeC6
  { 0xC0010015, 0x0000000000000000, BIT25                              },  // CcxZen5InitializeCpb
  { 0xC0011029, 0x0000000000000000, 0xFFFFFFFFFFFFFFFF                 },  // CcxZen5InitializePrefetchMode/PcdAmdRedirectForReturnDis
  { 0xC0011021, 0x0000000000000000, BIT5                               },  // PcdAmdOpcacheCtrl
  { 0xC0011020, 0x0000000000000000, BIT28                              },  // PcdAmdStreamingStoresCtrl/PcdAmdIbsHardwareEn
  { 0xC00110DF, 0x0000000000000000, BIT36                              },  // PcdAmdEnableFSRM
  { 0xC0011002, 0x0000000000000000, BIT9                               },  // PcdAmdEnableERMS
  { 0xC0011000, 0x0000000000000000, BIT15                              },  // PcdAmdEnableRMSS
  { 0xC0011002, 0x0000000000000000, BIT16|BIT17|BIT21|BIT28|BIT30|BIT31},  // PcdAmdCcxEnableAvx512
  { 0xC00110DD, 0x0000000000000000, BIT13                              },  // PcdAmdEnableSvmAVIC
  { 0xC00110DD, 0x0000000000000000, BIT18                              },  // PcdAmdEnableSvmX2AVIC
  { 0xC0010015, 0x0000000000000000, BIT9                               },  // PcdAmdMonMwaitDis
  { 0xC001102D, 0x0000000000000000, BIT4|BIT56                         },  // PcdAmdCpuSpeculativeStoreMode/PcdAmdCcxDisFstStrErmsb
  { 0xC00110E5, 0x0000000000000000, BIT19|BIT23|BIT26                  },  // PcdAmdCpuSpeculativeStoreMode
  { 0xC00110EC, 0x0000000000000000, BIT0|BIT39                         },  // PcdAmdCpuSpeculativeStoreMode
  { 0xC00110E2, 0x0000000000000000, BIT30                              },  // PcdAmdCpuSpeculativeStoreMode
  { 0xC00110E6, 0x0000000000000000, BIT7                               },  // PcdAmdCpuSpeculativeStoreMode
  { 0xC0011020, 0x0000000000000000, BIT53                              },  // PcdAmdSmallHammerConfiguration
  { 0xC00110E5, 0x0000000000000000, BIT30                              },  // PcdAmdSmallHammerConfiguration
  { 0xC0011004, 0x0000000000000000, BIT53                              },  // PcdAmdApicMode
  { 0xC00110DE, 0x0000000000000000, BIT15                              },  // PcdAmdCcxErmsbRepo
  { 0xC00110E3, 0x0000000000000000, BIT18|BIT19|BIT20|BIT21|BIT22|BIT23|BIT24|BIT25},  // PcdAmdCcxErmsbIntermThld
  { CPU_LIST_TERMINAL, 0, 0}
};

extern X86_ASSEMBLY_PATCH_LABEL  gApStartupCode;
extern X86_ASSEMBLY_PATCH_LABEL  gApStartupCodeEnd;

/*----------------------------------------------------------------------------------------
 *           P R O T O T Y P E S     O F     L O C A L     F U N C T I O N S
 *----------------------------------------------------------------------------------------
 */

/*----------------------------------------------------------------------------------------
 *                  T Y P E D E F S     A N D     S T R U C T U R E S
 *----------------------------------------------------------------------------------------
 */

/*++

Routine Description:

  Zen5 Driver Entry.  Initialize the core complex.

Arguments:

Returns:

  EFI_STATUS

--*/
EFI_STATUS
EFIAPI
AmdCcxZen5PeiInit (
  IN       EFI_PEI_FILE_HANDLE  FileHandle,
  IN CONST EFI_PEI_SERVICES     **PeiServices
  )
{
  UINT32                                  i;
  UINTN                                   Socket;
  UINTN                                   Die;
  UINTN                                   Ccd;
  UINTN                                   Ccx;
  UINTN                                   Core;
  UINTN                                   Thread;
  UINTN                                   NumberOfSockets;
  UINTN                                   NumberOfDies;
  UINTN                                   NumberOfCcds;
  UINTN                                   NumberOfComplexes;
  UINTN                                   NumberOfCores;
  UINTN                                   NumberOfThreads;
  UINTN                                   VariableSize;
  VOLATILE UINT32                         *ApSyncFlag;
  UINT32                                  ApNumBfLaunch;
  EFI_STATUS                              Status;
  EFI_STATUS                              TopologyStatus;
  EFI_STATUS                              CalledStatus;
  AMD_CONFIG_PARAMS                       StdHeader;
  AMD_CORE_TOPOLOGY_SERVICES_V3_PPI       *CoreTopologyServices;
  AMD_PEI_FABRIC_TOPOLOGY_SERVICES2_PPI   *FabricTopologyServices;
  AMD_PEI_SOC_ZEN5_SERVICES_PPI           *SocZen5Services;
  PEI_AMD_NBIO_SMU_SERVICES_PPI           *NbioSmuServices;
  EFI_PEI_READ_ONLY_VARIABLE2_PPI         *Variable;
  PEI_AMD_NBIO_IOMMU_FEATURE_PPI          *NbioIoMmuFeaturePpi;
  UINT8                                   ApicMode;
  PSP_SMM_HDR_DATA                        *PspSmmHdrData = NULL;
  VOID                                    *ApStartupBuffer;
  VOID                                    *MemoryContentCopy;
  UINTN                                   MemoryContentCopySize;
  IOMMU_FEATURE_INFO                      IommuFeatureData;
  CCX_IOMMU_FEATURE_INFO                  CcxIommuFeatureData;
  VOLATILE AMD_CCX_AP_LAUNCH_GLOBAL_DATA  ApLaunchGlobalData;
  VOLATILE AP_MTRR_SETTINGS               *ApMtrrSyncList;
  VOLATILE AP_MSR_SYNC                    *ApMsrSyncList;

  AGESA_TESTPOINT (TpCcxPeiEntry, NULL);
  IDS_HDT_CONSOLE (CPU_TRACE, "  AmdCcxZen5PeiInit Entry\n");

  Status = EFI_SUCCESS;
  MemoryContentCopy = NULL;
  ApMtrrSyncList = NULL;
  ApMsrSyncList = NULL;
  ApMtrrSyncList = AllocateCopyPool (sizeof (mApMtrrSyncList), (const void *)mApMtrrSyncList);
  if (ApMtrrSyncList == NULL) {
    IDS_HDT_CONSOLE (CPU_TRACE, "%a:%d Cannot allocate memory.\n", __func__, __LINE__);
    FreePool ((void *)ApMtrrSyncList);
    ASSERT_EFI_ERROR (EFI_OUT_OF_RESOURCES);
    return EFI_OUT_OF_RESOURCES;
  }
  ApMsrSyncList = AllocateCopyPool (sizeof (mApMsrSyncList), (const void *)mApMsrSyncList);
  if (ApMsrSyncList == NULL) {
    IDS_HDT_CONSOLE (CPU_TRACE, "%a:%d Cannot allocate memory.\n", __func__, __LINE__);
    FreePool ((void *)ApMsrSyncList);
    ASSERT_EFI_ERROR (EFI_OUT_OF_RESOURCES);
    return EFI_OUT_OF_RESOURCES;
  }
  SetMem ((VOID *) &ApLaunchGlobalData, sizeof (AMD_CCX_AP_LAUNCH_GLOBAL_DATA), 0);

  if (CcxIsBsp (&StdHeader)) {

    IDS_HOOK (IDS_HOOK_CCX_PEI, NULL, NULL);

    if ((FchReadSleepType () == 0x3) || AmdCapsuleGetStatus ()) {
      ApLaunchGlobalData.SleepType = 3;
    }

    if (ApLaunchGlobalData.SleepType != 3) {
      CcxZen5CacheInit (&StdHeader);
      IDS_HDT_CONSOLE (CPU_TRACE, "  CcxZen5CacheInit Done\n");

      CcxZen5SetBrandString (&StdHeader);
      IDS_HDT_CONSOLE (CPU_TRACE, "  CcxZen5SetBrandString Done\n");
    } else if (AmdCapsuleGetStatus()) {
      //Before S3 resume capsule mode, FW/OS may set some vMTRRs.
      //It's better to re-init vMTRR to avoid FW codes load to the vMTRR regions.
      InitializeVariableMTRRs (&StdHeader);
      IDS_HDT_CONSOLE (CPU_TRACE, "  InitializeVariableMTRRs Done\n");
    }

    CalledStatus = (*PeiServices)->LocatePpi (
                                   PeiServices,
                                   &gAmdCoreTopologyServicesV3PpiGuid,
                                   0,
                                   NULL,
                                   (VOID **)&CoreTopologyServices
                                   );
    ASSERT (CalledStatus == EFI_SUCCESS);
    Status = (CalledStatus > Status) ? CalledStatus : Status;

    CalledStatus = (*PeiServices)->LocatePpi (
                                   PeiServices,
                                   &gAmdFabricTopologyServices2PpiGuid,
                                   0,
                                   NULL,
                                   (VOID **)&FabricTopologyServices
                                   );
    ASSERT (CalledStatus == EFI_SUCCESS);
    Status = (CalledStatus > Status) ? CalledStatus : Status;

    CalledStatus = (*PeiServices)->LocatePpi(
                                   PeiServices,
                                   &gAmdSocZen5ServicesPpiGuid,
                                   0,
                                   NULL,
                                   (VOID **)&SocZen5Services
                                   );
    ASSERT(CalledStatus == EFI_SUCCESS);
    Status = (CalledStatus > Status) ? CalledStatus : Status;
    //
    // get NumberOfComplexes, NumberOfCores, NumberOfThreads of Socket 0
    //
    FabricTopologyServices->GetSystemInfo (&NumberOfSockets, NULL, NULL, NULL, NULL);
    CoreTopologyServices->GetCoreTopologyOnDieMax (
                            PeiServices,
                            CoreTopologyServices,
                            0,
                            0,
                            &NumberOfCcds,
                            &NumberOfComplexes,
                            &NumberOfCores,
                            &NumberOfThreads
                            );

    ApLaunchGlobalData.TransparentErrorLoggingEnable = PcdGetBool (PcdAmdTransparentErrorLoggingEnable);

    //When IOMMU is disabled, IRQs cant be remapped, so no X2APIC support
    if (!PcdGetBool (PcdCfgIommuSupport)) {
      ApicMode = xApicMode;
      IDS_HDT_CONSOLE (CPU_TRACE, "  IOMMU disabled, PcdAmdApicMode is changed to 0x%X\n", ApicMode);
      PcdSet8S (PcdAmdApicMode, ApicMode);
    }
    NbioIoMmuFeaturePpi = NULL;
    CalledStatus = (*PeiServices)->LocatePpi (
                                       PeiServices,
                                       &gAmdNbioIommuFeaturePpiGuid,
                                       0,
                                       NULL,
                                       (VOID **)&NbioIoMmuFeaturePpi
                                       );
    if (EFI_ERROR (CalledStatus)) {
      NbioIoMmuFeaturePpi = NULL;
    } else {
      CalledStatus = NbioIoMmuFeaturePpi->IommuFeatureGetInfo (
                                              NbioIoMmuFeaturePpi,
                                              &IommuFeatureData
                                              );
      ASSERT (!EFI_ERROR (CalledStatus));
      if (!EFI_ERROR (CalledStatus)) {
        ZeroMem (&CcxIommuFeatureData, sizeof(CCX_IOMMU_FEATURE_INFO));
        CcxIommuFeatureData.Iommu_Sup = IommuFeatureData.Iommu_Sup;
        CcxIommuFeatureData.XT_Sup = IommuFeatureData.XT_Sup;
        BuildGuidDataHob (&gAmdCcxIommuFeatureDataGuid, &CcxIommuFeatureData, sizeof(CCX_IOMMU_FEATURE_INFO));
      } else {
        NbioIoMmuFeaturePpi = NULL;
      }
    }

    ApicMode = PcdGet8 (PcdAmdApicMode);
    /* defeature Compatibility mode */
    if (ApicMode == ApicCompatibilityMode) {
      ApicMode = xApicMode;
      IDS_HDT_CONSOLE (CPU_TRACE, "  PcdAmdApicMode corrected to 0x%X\n", ApicMode);
      PcdSet8S (PcdAmdApicMode, xApicMode);
    }

    switch (ApicMode) {
      case xApicMode:
        break;
      case x2ApicMode:
        if (NbioIoMmuFeaturePpi != NULL) {
          if ((IommuFeatureData.Iommu_Sup == 0) || (IommuFeatureData.XT_Sup == 0)) {
            ApicMode = xApicMode;
            IDS_HDT_CONSOLE (CPU_TRACE, "IOMMU disabled or has no XT_Sup, Fall back PcdAmdApicMode to 0x%X\n", ApicMode);
            PcdSet8S (PcdAmdApicMode, ApicMode);
          }
        }
        break;
      case ApicAutoMode:
        ApicMode = SocZen5Services->GetAmdApicMode (NULL, NumberOfSockets, NumberOfCcds, NumberOfComplexes, NumberOfCores, NumberOfThreads);
        IDS_HDT_CONSOLE(CPU_TRACE, " ApicAutoMode is set to 0x%X\n", ApicMode);
        PcdSet8S (PcdAmdApicMode, ApicMode);
        break;
      default:
        // ERROR - Undefined Apic mode
        ASSERT(FALSE);
        break;
    }

    ApNumBfLaunch = 0;
    ApSyncFlag = NULL;

    if (ApLaunchGlobalData.SleepType == 3) {
      CalledStatus = (**PeiServices).LocatePpi (PeiServices, &gEfiPeiReadOnlyVariable2PpiGuid, 0, NULL, (VOID **)&Variable);
      ASSERT (CalledStatus == EFI_SUCCESS);

      if (PcdGetBool (PcdAmdPspS3WakeFromSmm)) {
        VariableSize = sizeof (UINT32);
        CalledStatus = Variable->GetVariable (
                        Variable,
                        L"ApSyncFlagNv",
                        &gApSyncFlagNvVariableGuid,
                        NULL,
                        &VariableSize,
                        (VOID *) &ApSyncFlag
                        );
        ASSERT (CalledStatus == EFI_SUCCESS);
        ASSERT (ApSyncFlag != NULL);
        IDS_HDT_CONSOLE (CPU_TRACE, " ApSyncFlagAddr %x\n", ApSyncFlag);
      } else {
        PspSmmHdrData = (PSP_SMM_HDR_DATA *)(UINTN)AsmReadMsr64 (0xC0010112); //SMMADDR_ADDRESS - Start of TSEG
        PspSmmHdrData->PageTableAddress = (UINT32)AsmReadCr3 ();
        ApSyncFlag = (VOLATILE UINT32*)&PspSmmHdrData->ApSyncFlag;
        ASSERT (ApSyncFlag != NULL);
        *ApSyncFlag = 0;
        PspSmmHdrData->ApStackTop = PspSmmHdrData->PspSmmRsmMemInfo.StackPtr + PspSmmHdrData->PspSmmRsmMemInfo.BspStackSize + PspSmmHdrData->PspSmmRsmMemInfo.ApStackSize;
        PspSmmHdrData->CcxEarlyInit = (EFI_PHYSICAL_ADDRESS)(UINTN)CcxSetMca;  // Call for APs when they are launched.
        CcxSetMca (); // Call for BSP.
        CcxSetTransparentErrorLoggingMca (ApLaunchGlobalData.TransparentErrorLoggingEnable);
      }
    } else {
      ApLaunchGlobalData.ApMtrrSyncList = ApMtrrSyncList;
      ApLaunchGlobalData.SizeOfApMtrr = sizeof (mApMtrrSyncList);
      ApLaunchGlobalData.ApMsrSyncList = ApMsrSyncList;

      // Load microcode on the BSP
      if (PcdGetBool (PcdAmdCcxLoadUcodePatch)) {
        CcxZen5MicrocodeInit ((UINT64 *) &ApLaunchGlobalData.UcodePatchAddr, &StdHeader);
      }

      // Reset Table
      GetPlatformFeatures (&StdHeader, (PLATFORM_FEATS *) &ApLaunchGlobalData.ResetTableCriteria.PlatformFeats);
      GetPerformanceFeatures (&StdHeader, (PROFILE_FEATS *) &ApLaunchGlobalData.ResetTableCriteria.ProfileFeats);
      GetSocLogicalIdOnCurrentCore (&StdHeader, (SOC_LOGICAL_ID *) &ApLaunchGlobalData.ResetTableCriteria.SocLogicalId);
      GetCoreLogicalIdOnCurrentCore (&StdHeader, (CORE_LOGICAL_ID *) &ApLaunchGlobalData.ResetTableCriteria.CoreLogicalId);
      CcxProgramTablesAtReset (ApLaunchGlobalData.SleepType, (ENTRY_CRITERIA *) &ApLaunchGlobalData.ResetTableCriteria, &StdHeader);
      IDS_HDT_CONSOLE (CPU_TRACE, "  CcxProgramTablesAtReset Done\n");

      // L1, L2 HW Stream Prefetcher
      CcxZen5Prefetcher ();

      // MSR misc setting
      CcxZen5SetMiscMsrs ();

      // Get PCD setting for CPU WDT
      ApLaunchGlobalData.CpuWdt.CpuWdtEn = PcdGetBool (PcdAmdCpuWdtEn);
      ApLaunchGlobalData.CpuWdt.CpuWdtTimeOut = PcdGet16 (PcdAmdCpuWdtTimeout);
      if (ApLaunchGlobalData.CpuWdt.CpuWdtTimeOut == 0xFFFF) {
        // If it's 'Auto', set timeout to 2.682s to make sure it's smaller than DF WDT
        ApLaunchGlobalData.CpuWdt.CpuWdtTimeOut = 0x100;
      }
      ApLaunchGlobalData.CpuWdt.CpuWdTmrCfgSeverity = PcdGet8 (PcdAmdCpuWdtSeverity);
      if ((ApLaunchGlobalData.CpuWdt.CpuWdTmrCfgSeverity != 0xFF) && (ApLaunchGlobalData.CpuWdt.CpuWdTmrCfgSeverity > 5)) {
        // 0 ~ 5 is valid
        ApLaunchGlobalData.CpuWdt.CpuWdTmrCfgSeverity = 0xFF;
      }
      // Enable watchdog timer
      CcxZen5EnableWdt ((CORE_WATCHDOG *) &ApLaunchGlobalData.CpuWdt);

      // Enable SMEE/HMKEE
      CcxZen5EnableSmeeHmkee ();

      // Mca initialization
      CcxSetMca ();
      CcxSetTransparentErrorLoggingMca (ApLaunchGlobalData.TransparentErrorLoggingEnable);

      CalledStatus = (*PeiServices)->LocatePpi (PeiServices, &gAmdNbioSmuServicesPpiGuid, 0, NULL, (VOID **)&NbioSmuServices);
      Status = (CalledStatus > Status) ? CalledStatus : Status;

      // CacWeights initialization is split into two routines as APs will not be getting
      // CacWeigts, but will be setting up CacWeights in AP flow
      ApLaunchGlobalData.SetCacWeightsEnable = (CcxZen5GetCacWeights (NbioSmuServices, (UINT64 *) &ApLaunchGlobalData.CacWeights[0]) == EFI_SUCCESS) ? TRUE : FALSE;
      if (ApLaunchGlobalData.SetCacWeightsEnable) {
        AGESA_TESTPOINT (TpCcxDxeCacWeights, NULL);
        CcxZen5SetCacWeights ((UINT64 *) &ApLaunchGlobalData.CacWeights[0]);
      }

      CcxZen5InitializeC6 (&StdHeader);

      CcxZen5InitializeCpb (&StdHeader);

      CcxZen5InitializePrefetchMode (&StdHeader);

      for (i = 0; ApLaunchGlobalData.ApMsrSyncList[i].MsrAddr != CPU_LIST_TERMINAL; i++) {
        ApLaunchGlobalData.ApMsrSyncList[i].MsrData = AsmReadMsr64 (ApLaunchGlobalData.ApMsrSyncList[i].MsrAddr);
      }

      AGESA_TESTPOINT(TpCCxCreateRestoreRegion, NULL);

      SetupApStartupRegion (&ApLaunchGlobalData, &ApStartupBuffer, &MemoryContentCopy, &MemoryContentCopySize);
      ApSyncFlag = (VOLATILE UINT32*) ((UINTN) ApLaunchGlobalData.AllowToLaunchNextThreadLocation);

    }

    if (ApLaunchGlobalData.SleepType == 3) {
      AGESA_TESTPOINT (TpCcxPeiStartLaunchApsForS3, NULL);
    } else {
      AGESA_TESTPOINT (TpCcxDxeStartLaunchAp, NULL);
    }
    IDS_HDT_CONSOLE (CPU_TRACE, "    Launching APs in PEI\n");

    TopologyStatus = CoreTopologyServices->GetSocketCountOnSystem (
                                             PeiServices,
                                             CoreTopologyServices,
                                             &NumberOfSockets
                                             );
    if (EFI_ERROR (TopologyStatus)) {
      NumberOfSockets = 0;
    }
    IDS_HDT_CONSOLE (CPU_TRACE, "    System has %d Socket(s)\n", NumberOfSockets);
    for (Socket = 0; Socket < NumberOfSockets; Socket++) {
      TopologyStatus = CoreTopologyServices->GetDieCountOnSocket (
                                               PeiServices,
                                               CoreTopologyServices,
                                               Socket,
                                               &NumberOfDies
                                               );
      if (EFI_ERROR (TopologyStatus)) {
        NumberOfDies = 0;
      }
      IDS_HDT_CONSOLE (CPU_TRACE, "    Socket %d has %d Die(s)\n", Socket, NumberOfDies);
      for (Die = 0; Die < NumberOfDies; Die++) {
        TopologyStatus = CoreTopologyServices->GetCcdCountOnDie (
                                                 PeiServices,
                                                 CoreTopologyServices,
                                                 Socket,
                                                 Die,
                                                 &NumberOfCcds
                                                 );
        if (EFI_ERROR (TopologyStatus)) {
          NumberOfCcds = 0;
        }
        IDS_HDT_CONSOLE (CPU_TRACE, "    Socket %d Die %d has %d CCD(s)\n", Socket, Die, NumberOfCcds);
        for (Ccd = 0; Ccd < NumberOfCcds; Ccd++) {
          TopologyStatus = CoreTopologyServices->GetComplexCountOnCcd (
                                                   PeiServices,
                                                   CoreTopologyServices,
                                                   Socket,
                                                   Die,
                                                   Ccd,
                                                   &NumberOfComplexes
                                                   );
          if (EFI_ERROR (TopologyStatus)) {
            NumberOfComplexes = 0;
          }
          IDS_HDT_CONSOLE (CPU_TRACE, "    Socket %d Die %d CCD %d has %d CCXs\n", Socket, Die, Ccd, NumberOfComplexes);
          for (Ccx = 0; Ccx < NumberOfComplexes; Ccx++) {
            TopologyStatus = CoreTopologyServices->GetCoreCountOnComplex (
                                                     PeiServices,
                                                     CoreTopologyServices,
                                                     Socket,
                                                     Die,
                                                     Ccd,
                                                     Ccx,
                                                     &NumberOfCores
                                                     );
            if (EFI_ERROR (TopologyStatus)) {
              NumberOfCores = 0;
            }
            IDS_HDT_CONSOLE (CPU_TRACE, "    Socket %d Die %d CCD %d CCX %d has %d Core(s)\n", Socket, Die, Ccd, Ccx, NumberOfCores);
            for (Core = 0; Core < NumberOfCores; Core++) {
              TopologyStatus = CoreTopologyServices->GetThreadCountOnCore (
                                                       PeiServices,
                                                       CoreTopologyServices,
                                                       Socket,
                                                       Die,
                                                       Ccd,
                                                       Ccx,
                                                       Core,
                                                       &NumberOfThreads
                                                       );
              IDS_HDT_CONSOLE (CPU_TRACE, "    Socket %d Die %d CCD %d CCX %d Core %d has %d Thread(s)\n", Socket, Die, Ccd, Ccx, Core, NumberOfThreads);
              if (EFI_ERROR (TopologyStatus)) {
                NumberOfThreads = 0;
              }
              for (Thread = 0; Thread < NumberOfThreads; Thread++) {
                if (!((Socket == 0) && (Die == 0) && (Ccd == 0) && (Ccx == 0) && (Core == 0) && (Thread == 0))) {

                  AGESA_TESTPOINT(TpCcxEnterLaunchThread, NULL);

                  CalledStatus = CoreTopologyServices->LaunchThread (PeiServices, CoreTopologyServices, Socket, Die, Ccd, Ccx, Core, Thread);

                  IDS_HDT_CONSOLE (CPU_TRACE,
                                   "      Launching socket %X die %X ccd %X complex %X core %X thread %X ...\n",
                                   Socket,
                                   Die,
                                   Ccd,
                                   Ccx,
                                   Core,
                                   Thread
                                   );

                  if (EFI_ERROR (CalledStatus)) {
                    continue;
                  }

                  ApNumBfLaunch++;

                  AGESA_TESTPOINT(TpCcxWaitForApSyncFlag, NULL);

                  // Wait until the core launch
                  if (ApSyncFlag != NULL) {
                    IDS_HDT_CONSOLE (CPU_TRACE, "Wait till launch AP complete\n");
                    while (*ApSyncFlag != ApNumBfLaunch) {
                      ;
                    }
                    IDS_HDT_CONSOLE (CPU_TRACE, "      Launch Succeed\n");   // AP launch Succeed
                  }
                  if ((!PcdGetBool (PcdAmdPspS3WakeFromSmm)) && (PspSmmHdrData != NULL)) {
                    PspSmmHdrData->ApStackTop += PspSmmHdrData->PspSmmRsmMemInfo.ApStackSize;
                  }
                }
              }
            }
          }
        }
      }
    }

    AGESA_TESTPOINT(TpCcxAllThreadsStarted, NULL);

    AsmMsrOr64 (MSR_TW_CFG, BIT49);

    // Restore the data located at the reset vector
    if (ApLaunchGlobalData.SleepType != 3) {
      RestoreResetVector (&ApLaunchGlobalData, ApNumBfLaunch, ApStartupBuffer, MemoryContentCopy, MemoryContentCopySize);
    }

    if (ApLaunchGlobalData.SleepType == 3) {
      AGESA_TESTPOINT (TpCcxPeiEndLaunchApsForS3, NULL);
    } else {
      AGESA_TESTPOINT (TpCcxDxeEndLaunchAp, NULL);
    }

    CalledStatus = (**PeiServices).InstallPpi (PeiServices, &mCcxPeiInitCompletePpiList);
    Status = (CalledStatus > Status) ? CalledStatus : Status;
  } else {
    Status = EFI_DEVICE_ERROR;
  }

  if (ApLaunchGlobalData.SleepType == 3) {
    (**PeiServices).NotifyPpi (PeiServices, mNotifyList);
  }

  IDS_HDT_CONSOLE (CPU_TRACE, "  AmdCcxZen5PeiInit End\n");

  AGESA_TESTPOINT (TpCcxPeiExit, NULL);

  if (MemoryContentCopy != NULL) {
    FreePool (MemoryContentCopy);
  }

  FreePool ((void *)ApMtrrSyncList);
  FreePool ((void *)ApMsrSyncList);
  return Status;
}

STATIC CONST UINT64 mGdtEntries[] =
{
    0x0000000000000000,  // [00h] Null descriptor
    0x00CF92000000FFFF,  // [08h] Linear data segment descriptor
    0x00CF9A000000FFFF,  // [10h] Linear code segment descriptor
    0x00CF92000000FFFF,  // [18h] System data segment descriptor
    0x00CF9A000000FFFF,  // [20h] System code segment descriptor
    0x0000000000000000,  // [28h] Spare segment descriptor
    0x00CF93000000FFFF,  // [30h] System data segment descriptor
    0x00AF9B000000FFFF,  // [38h] System code segment descriptor
    0x0000000000000000   // [40h] Spare segment descriptor
};

STATIC CONST UINT8 mAsmNearJump[] =
  {
    //[00]
    0x90,         // nop
    //[01]
    0xE9,         // near jmp
    //[02], [03]
    0x00, 0x00    // 0x10000 + (0xFFF0 - AP_STARTUP_CODE_OFFSET) - 0xFFF4
  };

/* -----------------------------------------------------------------------------*/
/**
 *
 *  SetupApStartupRegion
 *
 *
 *  Description:
 *    This routine sets up the necessary code and data to launch APs.
 *
 */
VOID
SetupApStartupRegion (
  IN       VOLATILE AMD_CCX_AP_LAUNCH_GLOBAL_DATA *ApLaunchGlobalData,
  OUT      VOID                 **ApStartupBuffer,
  OUT      VOID                 **MemoryContentCopy,
  OUT      UINTN                *MemoryContentCopySize
  )
{
  UINT8             i;
  GDT32_DESCRIPTOR  BspGdtr;
  TYPE_ATTRIB       TypeAttrib;
  UINT64            EntryAddress;
  UINT32            EntrySize;
  UINT64            EntryDest;
  UINT32            Segment;
  UINT32            ApBufferSize;
//  UINT8             ApcbPurpose;
//  UINT32            Apcb32;
  VOID                  *ApGdt;
  UINT32                StartupCodeSize;
  AP_STARTUP_MEMORY_MAP ApStartupMemoryMap;
  EFI_PHYSICAL_ADDRESS   ApStartupVector;

  UINT32 Cr3Value = (UINT32) AsmReadCr3 ();

  if (BIOSEntryInfo (BIOS_FIRMWARE, INSTANCE_IGNORED, &TypeAttrib, &EntryAddress, &EntrySize, &EntryDest) == FALSE) {
   IDS_HDT_CONSOLE (CPU_TRACE, "Fail to get the correct Entry of BIOS firmware\n");
  }
  IDS_HDT_CONSOLE (CPU_TRACE, "EntryAddress %lX EntrySize %X EntryDest %lX\n", EntryAddress, EntrySize, EntryDest);
  if (TypeAttrib.Copy == 0) {
    IDS_HDT_CONSOLE (CPU_TRACE, "  First fetch is pointing to SPI\n");

    // Get AP base address from APCB
//    if (ApcbGet32 (APCB_TOKEN_UID_CPU_FETCH_FROM_SPI_AP_BASE, Apcb32)) {
//      IDS_HDT_CONSOLE (CPU_TRACE, "APCB_TOKEN_UID_CPU_FETCH_FROM_SPI_AP_BASE Found = 0x%x\n", Apcb32);
//      EntryDest = Apcb32 & 0xFFFF0000;
//    } else {
      EntryDest = 0x10000000;  // This is default value if there's no APCB APCB_TOKEN_UID_CPU_FETCH_FROM_SPI_AP_BASE
                               // This value MUST match value of FETCH_FROM_SPI_AP_BASE which is defined in ABL
//    }
    EntrySize = 0x100000;

    //IDS_HDT_CONSOLE (CPU_TRACE, "  AP CS Base = 0x%x\n", ((UINT32) EntryDest + EntrySize - 0x10000));
  }

  Segment = ((UINT32) EntryDest + EntrySize - 0x10000);
  IDS_HDT_CONSOLE (CPU_TRACE, "Segment %X\n", Segment);
  ApStartupVector = (EFI_PHYSICAL_ADDRESS) EntryDest + EntrySize - 0x10000 + 0xFFF0;

  IDS_HDT_CONSOLE (CPU_TRACE, "  ApStartupVector = 0x%lX\n", ApStartupVector);

  // Allocate some space for APs to use as stack space
  ApLaunchGlobalData->ApStackBasePtr = (UINT32)(UINTN)AllocatePool (AP_STACK_SIZE);
  ASSERT (ApLaunchGlobalData->ApStackBasePtr != 0);
  IDS_HDT_CONSOLE (CPU_TRACE, "  ApStackBasePtr  = 0x%x, Size = 0x%x\n", ApLaunchGlobalData->ApStackBasePtr, EFI_SIZE_TO_PAGES(ZEN5_MAX_NUMBER_OF_APS_STACK_ALLOCATE * AP_STACK_SIZE));

  //
  // Allocate space to store data at reset vector
  //

  StartupCodeSize = (UINT32)((UINTN)(gApStartupCodeEnd) - (UINTN)(gApStartupCode));

  ApStartupMemoryMap.TopOfApRegion = (UINTN)ApStartupVector;
  ApStartupMemoryMap.PageTable = (UINTN)ApStartupVector - CR3_SAVE_SIZE;            // Should always be at ApStartup Vector - 8
  ApStartupMemoryMap.GdtTable = ApStartupMemoryMap.PageTable - sizeof (mGdtEntries);
  ApStartupMemoryMap.ApMttrSyncList = ApStartupMemoryMap.GdtTable - ApLaunchGlobalData->SizeOfApMtrr;
  ApStartupMemoryMap.ApStartupCodeBase = ApStartupMemoryMap.ApMttrSyncList - StartupCodeSize;

  *ApStartupBuffer = (VOID*)ApStartupMemoryMap.ApStartupCodeBase;
  ApBufferSize = CR3_SAVE_SIZE + sizeof (mGdtEntries) + ApLaunchGlobalData->SizeOfApMtrr + StartupCodeSize + 0x10;
  *MemoryContentCopySize = ApBufferSize;

  *MemoryContentCopy = AllocatePool (ApBufferSize);
  ASSERT (*MemoryContentCopy != NULL);

  // Copy data at reset vector to temporary buffer so we
  // can temporarily replace it with AP start up code.
  CopyMem (*MemoryContentCopy, *ApStartupBuffer, ApBufferSize);

  // Copy AP start up code to Segment + 0xFFF0 - AP_STARTUP_CODE_OFFSET
  CopyMem ((VOID *)(UINTN)ApStartupMemoryMap.ApStartupCodeBase, (VOID *)(UINTN)&gApStartupCode, (UINTN)StartupCodeSize);
  IDS_HDT_CONSOLE (CPU_TRACE, "  ApStartupVector = 0x%X\n", (UINTN)ApStartupMemoryMap.ApStartupCodeBase);

  ApStartupFixups(&ApStartupMemoryMap, (VOID *)ApLaunchGlobalData);

  ApLaunchGlobalData->ApSyncCount = 0;
  ApLaunchGlobalData->AllowToLaunchNextThreadLocation = (UINT32) (ApStartupVector + 0xA);
  *(UINT32*)(UINTN)ApLaunchGlobalData->AllowToLaunchNextThreadLocation = 0;

  // Copy the near jump to AP startup code to reset vector. The near jump
  // forces execution to start from CS:FFF0 - AP_STARTUP_CODE_OFFSET
  CopyMem ((VOID *)(UINTN)ApStartupVector, (VOID *)mAsmNearJump, sizeof (mAsmNearJump));

  // Fixup AsmNearJump
  *(UINT16*)((UINTN)ApStartupVector + 2) = (UINT16)(0x10000 + (0xFFF0 - ((UINTN)ApStartupVector - ApStartupMemoryMap.ApStartupCodeBase)) - 0xFFF4);

  CopyMem ((VOID *) ApStartupMemoryMap.GdtTable, (VOID *) &mGdtEntries, sizeof (mGdtEntries));

  // Sync Fixed-MTRRs with BSP
  AsmMsrOr64 (MSR_SYS_CFG, BIT19);

  for (i = 0; ApLaunchGlobalData->ApMtrrSyncList[i].MsrAddr != CPU_LIST_TERMINAL; i++) {
    ApLaunchGlobalData->ApMtrrSyncList[i].MsrData = AsmReadMsr64 (ApLaunchGlobalData->ApMtrrSyncList[i].MsrAddr);
  }

  // Some Fixed-MTRRs should be set according to PCDs
  UpdateApMtrrSettings ((AP_MTRR_SETTINGS *) ApLaunchGlobalData->ApMtrrSyncList);

  AsmMsrAnd64 (MSR_SYS_CFG, ~((UINT64) BIT19));

  ApLaunchGlobalData->BspMsrLocation = (UINT32)ApStartupMemoryMap.ApMttrSyncList;
  CopyMem ((VOID *)ApStartupMemoryMap.ApMttrSyncList, (VOID *) ApLaunchGlobalData->ApMtrrSyncList, ApLaunchGlobalData->SizeOfApMtrr);

  BspGdtr.Limit = sizeof (mGdtEntries) - 1;
  BspGdtr.Base = (UINT32)ApStartupMemoryMap.GdtTable;

  // Copy pointer to GDT entries to Segment + 0xFFF4
  CopyMem ((VOID *) ((UINTN)ApStartupVector + (UINTN)sizeof (mAsmNearJump)), (VOID *) &BspGdtr, sizeof (BspGdtr));

  // Copy the value of C3 to Segment + 0xFFE8
  *(UINT32 *)ApStartupMemoryMap.PageTable = Cr3Value;

  // Save BSP's patch level so that AP can use it to determine whether microcode patch
  // loading should be skipped
  ApLaunchGlobalData->BspPatchLevel = AsmReadMsr64 (MSR_PATCH_LEVEL);

  ApGdt = AllocateRuntimePages (EFI_SIZE_TO_PAGES (sizeof (mGdtEntries)));
  ASSERT (ApGdt != NULL);
  CopyMem (ApGdt, (VOID *) &mGdtEntries[0], sizeof (mGdtEntries));

  ApLaunchGlobalData->ApGdtDescriptor.Limit = (sizeof (mGdtEntries)) - 1;
  ApLaunchGlobalData->ApGdtDescriptor.Base = (UINTN)ApGdt;

  AsmWbinvd ();
}

/* -----------------------------------------------------------------------------*/
/**
 *
 *  RestoreResetVector
 *
 *  @param[in] TotalCoresLaunched      The number of cores that were launched by the BSC
 *
 *  Description:
 *    This routine restores the code in the AP reset vector once all the APs that
 *    were launched are done running AP code
 *
 */
VOID
RestoreResetVector (
  IN       VOLATILE AMD_CCX_AP_LAUNCH_GLOBAL_DATA *ApLaunchGlobalData,
  IN       UINT32   TotalApCoresLaunched,
  IN       VOID     *ApStartupBuffer,
  IN       VOID     *MemoryContentCopy,
  IN       UINTN    MemoryContentCopySize
  )
{
  while (ApLaunchGlobalData->ApSyncCount != TotalApCoresLaunched);

  CopyMem (ApStartupBuffer, MemoryContentCopy, MemoryContentCopySize);

  // Clean up memory allocations
  // PEI phase does not support FreePool
  // FreePool (*MemoryContentCopy);
  // FreePool (ApLaunchGlobalData->ApStackBasePtr);
}

/* -----------------------------------------------------------------------------*/
/**
 *
 *  RegSettingBeforeLaunchingNextThread
 *
 *  Description:
 *    Necessary register setting before launching next thread
 *
 */
VOID
RegSettingBeforeLaunchingNextThread (
  )
{
  AMD_CONFIG_PARAMS    StdHeader;
  UINT32               EAX_Reg = 0;
  BOOLEAN              IsSmtDisabled;

  AsmCpuidEx (
      0x8000001D,
      0,
      &EAX_Reg,
      NULL,
      NULL,
      NULL
      );

  IsSmtDisabled = (BOOLEAN) (((EAX_Reg >> 14) & 0xFFF) == 0);

  // Thread 0s are launched before thread 1s.  We want the last thread of a core to set the bit.
  // When SMT is disabled, thread 0 should set the bit.
  if (IsSmtDisabled || !CcxIsComputeUnitPrimary (&StdHeader)) {
    // MSRC001_1023[49, TwCfgCombineCr0Cd] = 1
    AsmMsrOr64 (MSR_TW_CFG, BIT49);
  }
}
//Use the light version lib to write postcode on AP
#define AGESA_AP_POST_CODE(pc)   IoWrite32 (PcdGet16 (PcdIdsDebugPort),  AGESA_POST_CODE (pc))

/* -----------------------------------------------------------------------------*/
/**
 *
 *  ApEntryPointInC
 *
 *  Description:
 *    This routine is the C entry point for APs and is called from ApAsmCode
 *
 */
VOID
ApEntryPointInC (
  IN       VOLATILE AMD_CCX_AP_LAUNCH_GLOBAL_DATA *ApLaunchGlobalData
  )
{
  AMD_CONFIG_PARAMS    StdHeader;

  AGESA_AP_POST_CODE (TpCcxApLauchApEntryPointInC);

  // Enable watchdog timer
  if (CcxIsComputeUnitPrimary (&StdHeader)) {
     CcxZen5EnableWdt ((CORE_WATCHDOG *) &ApLaunchGlobalData->CpuWdt);
  }

  // Skip loading microcode patch on AP if BSP's patch level is 0.
  if (ApLaunchGlobalData->BspPatchLevel != 0) {
    // Using the address saved by BSP previously
    if (ApLaunchGlobalData->UcodePatchAddr != 0) {
      AsmWriteMsr64 (MSR_PATCH_LOADER, ApLaunchGlobalData->UcodePatchAddr);
    }
  }

  AGESA_AP_POST_CODE (TpCcxApLauchProgramTablesAtReset);
  CcxProgramTablesAtReset (ApLaunchGlobalData->SleepType, (ENTRY_CRITERIA *) &ApLaunchGlobalData->ResetTableCriteria, &StdHeader);

  // Mca initialization
  AGESA_AP_POST_CODE (TpCcxApLauchMcaInit);
  CcxSetMca ();
  CcxSetTransparentErrorLoggingMca (ApLaunchGlobalData->TransparentErrorLoggingEnable);

  // Cac Weights initialization
  if (ApLaunchGlobalData->SetCacWeightsEnable) {
    AGESA_AP_POST_CODE (TpCcxApLauchCacWeights);
    CcxZen5SetCacWeights ((UINT64 *) &ApLaunchGlobalData->CacWeights[0]);
  }

  // Last step: Sync up MSRs with BSP
  AGESA_AP_POST_CODE (TpCcxApLauchSyncMsr);
  CcxZen5SyncMiscMsrs (ApLaunchGlobalData);
}

/* -----------------------------------------------------------------------------*/
/**
 *
 *  CcxZen5SetMiscMsrs
 *
 *  This routine is only executed on the BSP. APs will sync the applicable MSRs
 *  settings through ApMsrSyncList

 *  Description:
 *    This routine sets miscellaneous MSRs
 *
 */
VOID
CcxZen5SetMiscMsrs (
  )
{
  UINT64    LocalMsrRegister;
  UINT8     RedirectForReturnDis;
  UINT8     OpCacheCtrl;
  UINT8     StreamingStoresCtrl;
  UINT8     EnableAvx512;
  UINT8     MonMwaitDis;
  UINT8     DisFstStrErmsb;
  UINT8     SpeculativeStoreMode;
  UINT8     CpuPauseCntSel_1_0;
  UINT8     AdaptiveAlloc;
  BOOLEAN   EnableFSRM;
  BOOLEAN   EnableERMS;
  BOOLEAN   EnableRMSS;
  BOOLEAN   IbsHardwareEn;
  BOOLEAN   EnableSvmAVIC;
  BOOLEAN   EnableSvmX2AVIC;
  UINT8     SmallHammer;
  UINT8     ApicMode;
  UINT8     SetCntThreshold;
  UINT8     AmdErmsb;

  RedirectForReturnDis = PcdGet8 (PcdAmdRedirectForReturnDis);
  OpCacheCtrl          = PcdGet8 (PcdAmdOpcacheCtrl);
  StreamingStoresCtrl  = PcdGet8 (PcdAmdStreamingStoresCtrl);
  EnableAvx512         = PcdGet8 (PcdAmdCcxEnableAvx512);
  MonMwaitDis          = PcdGet8 (PcdAmdMonMwaitDis);
  DisFstStrErmsb       = PcdGet8 (PcdAmdCcxDisFstStrErmsb);
  SpeculativeStoreMode = PcdGet8 (PcdAmdCpuSpeculativeStoreMode);
  CpuPauseCntSel_1_0   = PcdGet8 (PcdAmdCpuPauseCntSel_1_0);
  AdaptiveAlloc        = PcdGet8 (PcdAmdCpuAdaptiveAlloc);
  EnableFSRM           = PcdGetBool (PcdAmdEnableFSRM);
  EnableERMS           = PcdGetBool (PcdAmdEnableERMS);
  EnableRMSS           = PcdGetBool (PcdAmdEnableRMSS);
  IbsHardwareEn        = PcdGetBool (PcdAmdIbsHardwareEn);
  EnableSvmAVIC        = PcdGetBool (PcdAmdEnableSvmAVIC);
  EnableSvmX2AVIC      = PcdGetBool (PcdAmdEnableSvmX2AVIC);
  SmallHammer          = PcdGet8 (PcdAmdSmallHammerConfiguration);
  ApicMode             = PcdGet8 (PcdAmdApicMode);
  SetCntThreshold      = PcdGet8 (PcdAmdCcxErmsbIntermThld);
  AmdErmsb             = PcdGet8 (PcdAmdCcxErmsbRepo);

  // Force recalc of TSC on all threads after loading patch
  LocalMsrRegister = AsmReadMsr64 (0xC0010064);
  AsmWriteMsr64 (0xC0010064, LocalMsrRegister);

  // MSR_C001_1029[14]
  if (RedirectForReturnDis != 0xFF) {
    AsmMsrAndThenOr64 (MSR_DE_CFG, ~(UINT64) BIT14, LShiftU64 ((RedirectForReturnDis & 1), 14));
  }

  // MSR_C001_1021[5]
  if (OpCacheCtrl != 0xFF) {
    AsmMsrAndThenOr64 (MSR_IC_CFG, ~(UINT64) BIT5, LShiftU64 ((OpCacheCtrl & 1), 5));
  }

  // MSR_C001_1020[28]
  if (StreamingStoresCtrl != 0xFF) {
    AsmMsrAndThenOr64 (MSR_LS_CFG, ~(UINT64) BIT28, LShiftU64 ((StreamingStoresCtrl & 1), 28));
  }

  // MSR_C001_1002[16,17,21,28,30,31]
  if (EnableAvx512 != 0xFF) {
    AsmMsrAndThenOr64 (0xC0011002, ~(UINT64) 0xD0230000, EnableAvx512 ? 0xD0230000 : 0);
  }


  //MSRC001_1029[55:54]: PauseCntSel_1_0
  switch(CpuPauseCntSel_1_0){
  case CPU_PAUSECNTSEL_1_0_AUTO:
    break;
  case CPU_PAUSECNTSEL_1_0_16CYCLES:
    AsmMsrAnd64 (MSR_DE_CFG, ~((UINT64)(BIT54|BIT55)));
    break;
  case CPU_PAUSECNTSEL_1_0_32CYCLES:
    AsmMsrAndThenOr64 (MSR_DE_CFG, ~((UINT64)(BIT54|BIT55)), BIT54);
    break;
  case CPU_PAUSECNTSEL_1_0_64CYCLES:
    AsmMsrAndThenOr64 (MSR_DE_CFG, ~((UINT64)(BIT54|BIT55)), BIT55);
    break;
  case CPU_PAUSECNTSEL_1_0_128CYCLES:
    AsmMsrAndThenOr64 (MSR_DE_CFG, ~((UINT64)(BIT54|BIT55)), (BIT54|BIT55));
    break;
  default:
    ASSERT (FALSE);
    break;
  }

  // MSR_C001_10E8[21] : Adaptive Allocation (AA)
  if(AdaptiveAlloc != 0xFF){
    AsmMsrAndThenOr64 (MSR_CH_L2_AA_CFG, ~(UINT64) BIT21, LShiftU64 ((AdaptiveAlloc & 1), 21));
  }

  // MSR_C001_10DF[36] : FSRM
  if (EnableFSRM) {
    AsmMsrOr64 (0xC00110DF, (UINT64) BIT36);
  } else {
    AsmMsrAnd64 (0xC00110DF, ~((UINT64) BIT36));
  }

  // MSR_C001_1002[9] : ERMS
  if (EnableERMS) {
    AsmMsrOr64 (0xC0011002, (UINT64) BIT9);
  } else {
    AsmMsrAnd64 (0xC0011002, ~((UINT64) BIT9));
  }

  // MSR_C001_1000[15] : RMSS = 0 (Enable)
  if (EnableRMSS) {
    AsmMsrAnd64 (0xC0011000, ~((UINT64) BIT15));
  } else {
    AsmMsrOr64 (0xC0011000, (UINT64) BIT15);
  }

  // MSR_C001_1020[54]
  if (IbsHardwareEn) {
    AsmMsrOr64 (MSR_LS_CFG, (UINT64) BIT54);
  }

  // MSR_C001_10DD[13]
  if (EnableSvmAVIC) {
    AsmMsrOr64 (0xC00110DD, (UINT64) BIT13);
  } else {
    AsmMsrAnd64 (0xC00110DD, ~((UINT64) BIT13));
  }

  // MSR_C001_10DD[18]
  if (EnableSvmX2AVIC) {
    AsmMsrOr64 (0xC00110DD, (UINT64) BIT18);
  } else {
    AsmMsrAnd64 (0xC00110DD, ~((UINT64) BIT18));
  }

  // MSR_C001_0015[9]
  if (MonMwaitDis != 0xFF) {
    AsmMsrAndThenOr64 (MSR_HWCR, ~(UINT64) BIT9, LShiftU64 ((MonMwaitDis & 1), 9));
  }

  // MSR_C001_102D[56]
  if (DisFstStrErmsb != 0xFF) {
    AsmMsrAndThenOr64 (MSR_LS_CFG2, ~(UINT64) BIT56, LShiftU64 ((DisFstStrErmsb & 1), 56));
  }

  // MSRC001_10E5[26]
  // MSRC001_10E5[23]
  // MSRC001_10E5[19]
  // MSRC001_10EC[0]
  // MSRC001_10EC[39]
  // MSRC001_10E2[30]
  // MSRC001_102D[4]
  // MSRC001_10E6[7]
  switch (SpeculativeStoreMode) {
  case 0:
    // Balanced
    AsmMsrAnd64 (0xC00110E5, ~(UINT64) BIT23);
    AsmMsrOr64 (0xC00110E5, (UINT64) BIT26);
    AsmMsrOr64 (0xC00110EC, (UINT64) BIT0);
    AsmMsrAnd64 (0xC00110E2, ~(UINT64) BIT30);
    break;
  case 1:
    // More Speculative
    AsmMsrOr64 (0xC00110E5, (UINT64) BIT26);
    AsmMsrOr64 (0xC00110E5, (UINT64) BIT23);
    AsmMsrOr64 (0xC00110EC, (UINT64) BIT0);
    AsmMsrAnd64 (0xC00110E2, ~(UINT64) BIT30);
    break;
  case 2:
    // Less Speculative
    AsmMsrAnd64 (0xC00110E5, ~(UINT64) BIT23);
    AsmMsrAnd64 (0xC00110E5, ~(UINT64) BIT26);
    AsmMsrAnd64 (0xC00110EC, ~(UINT64) BIT0);
    AsmMsrAnd64 (0xC00110E2, ~(UINT64) BIT30);
    break;
  case 3:
    // Aggressive
    AsmMsrOr64 (0xC00110E5, (UINT64) BIT23);
    AsmMsrOr64 (0xC00110E5, (UINT64) BIT19);
    AsmMsrOr64 (0xC00110EC, (UINT64) BIT39);
    AsmMsrOr64 (0xC001102D, (UINT64) BIT4);
    AsmMsrOr64 (0xC00110E6, (UINT64) BIT7);
    break;
  }

  // MSR_C001_1020[53]
  // MSR_C001_10E5[30]
  if (SmallHammer != 0xFF) {
    AsmMsrAnd64 (MSR_LS_CFG, ~((UINT64) BIT53));
    AsmMsrAndThenOr64 (0xC00110E5, ~(UINT64) 0x040000000, SmallHammer ? 0x040000000 : 0);
  }

  // MSR_C001_1004[53] : X2APIC
  // clear x2Apic support when in APIC mode
  if (ApicMode == xApicMode) {
    AsmMsrAnd64 (0xC0011004, ~((UINT64) BIT53));
  }

  // MSR_C001_10E3[25:18]
  if (SetCntThreshold >= 0x02) {
    AsmMsrAndThenOr64 (0xC00110E3, ~(UINT64) 0x3FC0000, LShiftU64 (SetCntThreshold, 18));
  }

  // MSR_C001_10DE[15]
  if (AmdErmsb != 0xFF){
    AsmMsrAndThenOr64 (0xC00110DE, ~(UINT64) BIT15, LShiftU64 ((AmdErmsb & 1), 15));
  }
}

/* -----------------------------------------------------------------------------*/
/**
 *
 *  CcxZen5Prefetcher
 *
 *  This routine is only executed on the BSP. APs will sync the applicable MSRs
 *  settings through ApMsrSyncList
 *
 *  Description:
 *    This routine initializes L1/L2 prefetcher per user configurations
 *
 */
VOID
CcxZen5Prefetcher (
  )
{
  UINT64 LocalMsr;

  // L1 Burst Prefetch Mode
  if (PcdGetBool (PcdAmdL1BurstPrefetch)) {
    // MSR C001_102B[22] = 1
    AsmMsrOr64 (MSR_CH_L2_PF_CFG, BIT22);

    // MSR C001_10E7[EnableBurstPfs] = 1
    // MSR C001_10E7[EnLowConfBurstPfs] = 0
    // MSR C001_10E7[EnPfHistoryOnEveryStreamHit] = 1
    LocalMsr = AsmReadMsr64 (MSR_DC_PF_CFG);
    IDS_HDT_CONSOLE(CPU_TRACE, "MSR_DC_PF_CFG current value is 0x%lX\n", LocalMsr);

    LocalMsr &= ~(BIT6 | BIT10 | BIT12);
    LocalMsr |= BIT6 | BIT12;
    IDS_HDT_CONSOLE(CPU_TRACE, "MSR_DC_PF_CFG updated value is 0x%lX\n", LocalMsr);
    AsmWriteMsr64 (MSR_DC_PF_CFG, LocalMsr);

    LocalMsr = AsmReadMsr64 (MSR_DC_PF_CFG);
    IDS_HDT_CONSOLE(CPU_TRACE, "MSR_DC_PF_CFG current value is 0x%lX\n", LocalMsr);
  } else {
    // MSR C001_102B[22] = 0
    AsmMsrAnd64 (MSR_CH_L2_PF_CFG, ~((UINT64)BIT22));

    // MSR C001_10E7[EnableBurstPfs] = 0
    // MSR C001_10E7[EnLowConfBurstPfs] = 1
    // MSR C001_10E7[EnPfHistoryOnEveryStreamHit] = 0
    LocalMsr = AsmReadMsr64 (MSR_DC_PF_CFG);
    IDS_HDT_CONSOLE(CPU_TRACE, " MSR_DC_PF_CFG current value is 0x%lX\n", LocalMsr);

    LocalMsr &= ~(BIT6 | BIT10 | BIT12);
    LocalMsr |= BIT10;
    IDS_HDT_CONSOLE(CPU_TRACE, " MSR_DC_PF_CFG updated value is 0x%lX\n", LocalMsr);
    AsmWriteMsr64 (MSR_DC_PF_CFG, LocalMsr);

    LocalMsr = AsmReadMsr64 (MSR_DC_PF_CFG);
    IDS_HDT_CONSOLE(CPU_TRACE, " MSR_DC_PF_CFG current value is 0x%lX\n", LocalMsr);
  }

  // L1 Stream HW Prefetcher
  if (!PcdGetBool (PcdAmdL1StreamPrefetcher)) {
    // MSR C001_10E7[DisStreamHwPf] = 1
    AsmMsrOr64 (MSR_DC_PF_CFG, BIT16);
  }

  // L1 Stride Prefetcher
  if (!PcdGetBool (PcdAmdL1StridePrefetcher)) {
    // MSR C001_10E7[DisStrideHwPf] = 1
    AsmMsrOr64 (MSR_DC_PF_CFG, BIT17);
  }

  // L1 Region Prefetcher
  if (!PcdGetBool (PcdAmdL1RegionPrefetcher)) {
    // MSR C001_10E7[DisRegionHwPf] = 1
    AsmMsrOr64 (MSR_DC_PF_CFG, BIT18);
  }

  // L2 Stream Prefetcher
  if (!PcdGetBool (PcdAmdL2StreamPrefetcher)) {
    // MSR C001_102B[0] = 0
    AsmMsrAnd64 (MSR_CH_L2_PF_CFG, ~((UINT64)BIT0));
  }

  // L2 Up/Down Prefetcher
  if (!PcdGetBool (PcdAmdL2UpDownPrefetcher)) {
    // MSR C001_102B[2] = 0
    AsmMsrAnd64 (MSR_CH_L2_PF_CFG, ~((UINT64)BIT2));
  }

}

/* -----------------------------------------------------------------------------*/
/**
 *
 *  CcxZen4EnableSmeeHmkee
 *
 *  This routine is only executed on the BSP. APs will sync the applicable MSRs
 *  settings through ApMsrSyncList
 *
 *  Description:
 *    This routine enables either secure memory encryption
 *    or host multi-Key encryption.
 *    Attempting to enable both simultaneously will result in #GP
 *
 */
VOID
CcxZen5EnableSmeeHmkee (
  )
{
  if (PcdGetBool (PcdAmdSmee) && PcdGetBool (PcdAmdHmkee)) {
    IDS_HDT_CONSOLE (CPU_TRACE, "ERROR: Attempting to enable SMEE & HMKEE together. Neither enabled.\n");
    ASSERT (FALSE);
    return;
  }
  if (PcdGetBool (PcdAmdSmee)) {
    AsmMsrOr64 (MSR_SYS_CFG, BIT23);
  } else if (PcdGetBool (PcdAmdHmkee)) {
    AsmMsrOr64 (MSR_SYS_CFG, BIT26);
  }
}


/*++

Routine Description:

  Zen5 Microcode Patch loading

Arguments:
 *  @param[in,out] UcodePatchAddr        - The selected UcodePatch address, return 0 if not found
 *  @param[in] StdHeader                 - Config handle for library and services.
Returns:

--*/
VOID
CcxZen5MicrocodeInit (
  IN OUT   UINT64             *UcodePatchAddr,
  IN       AMD_CONFIG_PARAMS  *StdHeader
  )
{
  if ((PcdGet32(PcdAmdCcxEnabledFeatures) & CCX_FEATURE_SIGNED_MICROCODE) == 0) {
    LoadMicrocodePatch (UcodePatchAddr, StdHeader);
  } else {
    LoadMicrocodePatchV2 (UcodePatchAddr, StdHeader);
  }
  if (AsmReadMsr32(MSR_PATCH_LEVEL) == 0) {
    IDS_HDT_CONSOLE (CPU_TRACE, "  [Warning] CPU microcode is not loaded.\n");
  } else {
    AGESA_TESTPOINT (TpCcxMicrocodeLoaded, NULL);
  }
}

UINT32 CpuWdtCountSelDecode [] =
{
  4095,
  2047,
  1023,
  511,
  255,
  127,
  63,
  31,
  8191,
  16383
};
#define NumberOfCpuWdtCountSel (sizeof (CpuWdtCountSelDecode) / sizeof (CpuWdtCountSelDecode[0]))
#define MinCpuWdtCountSel 7
#define MaxCpuWdtCountSel 9

UINT8 CpuWdtCountSelBumpUp [] =
{
  7,
  6,
  5,
  4,
  3,
  2,
  1,
  0,
  8,
  9
};

UINT64 CpuWdtTimeBaseDecode [] =
{
  1310000,
  1280
};
#define NumberOfCpuWdtTimeBase (sizeof (CpuWdtTimeBaseDecode) / sizeof (CpuWdtTimeBaseDecode[0]))
#define MinCpuWdtTimeBase 1
#define MaxCpuWdtTimeBase 0


/* -----------------------------------------------------------------------------*/
/**
 *
 *  CcxZen5EnableWdt
 *
 *  Description:
 *    This routine enables watchdog
 *
 */
VOID
CcxZen5EnableWdt (
  IN       CORE_WATCHDOG  *CpuWdt
  )
{
  UINT8           BumpUpIndex;
  UINT16          CpuWdtTimeBase;
  UINT16          CpuWdtCountSel;
  UINT64          CpuWdtTime;
  UINT64          DfCcmWdtTime;
  CPU_WDT_CFG_MSR CpuWdtCfg;


  if ((CcxIsBsp (NULL)) && (FabricGetCcmWdtInfo (&DfCcmWdtTime)) && (CpuWdt->CpuWdtEn)) {
    CpuWdtTimeBase = CpuWdt->CpuWdtTimeOut & 0xFF;
    CpuWdtCountSel = (CpuWdt->CpuWdtTimeOut & 0xFF00) >> 8;
    ASSERT (CpuWdtTimeBase < NumberOfCpuWdtTimeBase);
    ASSERT (CpuWdtCountSel < NumberOfCpuWdtCountSel);

    // RESTRICTION: When both CPU WDT & DF WDT are enable, the DF CCM WDT timeout must be greater than the CPU WDT timeout limit
    CpuWdtTime = MultU64x64 (CpuWdtTimeBaseDecode[CpuWdtTimeBase], CpuWdtCountSelDecode[CpuWdtCountSel]);
    if (DfCcmWdtTime <= CpuWdtTime) {
      //IDS_HDT_CONSOLE (
      //               CPU_TRACE,
      //               "  WARNING: CPU WDT (%ld.%ld S) is greater than or equal to the DF CCM WDT (%ld.%ld S)\n",
      //               (CpuWdtTime / 1000000000),
      //               (CpuWdtTime % 1000000000),
      //               (DfCcmWdtTime / 1000000000),
      //               (DfCcmWdtTime % 1000000000)
      //               );
      IDS_HDT_CONSOLE (CPU_TRACE, "  Current CPU WDT setting: CpuWdtCountSel %X, CpuWdtTimeBase %X\n", CpuWdtCountSel, CpuWdtTimeBase);
      // slow down the CPU WDT to at least the value of the DF CCM WDT
      IDS_HDT_CONSOLE (CPU_TRACE, "  Try to slow down CPU WDT\n");
      CpuWdt->CpuWdtEn = FALSE; // Disable CPU WDT
      while ((CpuWdtCountSel != MinCpuWdtCountSel) || (CpuWdtTimeBase != MinCpuWdtTimeBase)) {
        if ((CpuWdtCountSel == MinCpuWdtCountSel) && (CpuWdtTimeBase != MinCpuWdtTimeBase)) {
          // Try to change TimeBase
          CpuWdtCountSel = MaxCpuWdtCountSel;
          CpuWdtTimeBase = MinCpuWdtTimeBase;
        } else {
          // Try to change CountSel
          for (BumpUpIndex = (NumberOfCpuWdtCountSel - 1); BumpUpIndex > 0; BumpUpIndex--) {
            if (CpuWdtCountSelBumpUp[BumpUpIndex] == CpuWdtCountSel) {
              CpuWdtCountSel = CpuWdtCountSelBumpUp[BumpUpIndex - 1];
              break;
            }
          }
        }

        CpuWdtTime = MultU64x64 (CpuWdtTimeBaseDecode[CpuWdtTimeBase], CpuWdtCountSelDecode[CpuWdtCountSel]);

        if (DfCcmWdtTime > CpuWdtTime) {
          // Slow down succeed, enable CPU WDT
          CpuWdt->CpuWdtEn = TRUE;
          CpuWdt->CpuWdtTimeOut = CpuWdtTimeBase | (CpuWdtCountSel << 8);
          //IDS_HDT_CONSOLE (CPU_TRACE, "  Succeed! New CPU WDT %ld.%ld S\n", (CpuWdtTime / 1000000000), (CpuWdtTime % 1000000000));
          IDS_HDT_CONSOLE (CPU_TRACE, "  New CPU WDT setting: CpuWdtCountSel %X, CpuWdtTimeBase %X\n", CpuWdtCountSel, CpuWdtTimeBase);
          break;
        }
      }
      if (!CpuWdt->CpuWdtEn) {
        IDS_HDT_CONSOLE (CPU_TRACE, "  Failed, we would not enable CPU WDT\n");
      }
    }
  }

  CpuWdtCfg.Value = AsmReadMsr64 (CPU_WDT_CFG);
  if (CpuWdt->CpuWdtEn) {
    CpuWdtCfg.Field.CpuWdTmrCfgEn = 1;
    CpuWdtCfg.Field.CpuWdTmrTimebaseSel = CpuWdt->CpuWdtTimeOut & 0xFF;
    CpuWdtCfg.Field.CpuWdTmrCfgCount = (CpuWdt->CpuWdtTimeOut & 0xFF00) >> 8;
    if (CpuWdt->CpuWdTmrCfgSeverity != 0xFF) {
      CpuWdtCfg.Field.CpuWdTmrCfgSeverity = CpuWdt->CpuWdTmrCfgSeverity;
    }
  } else {
    CpuWdtCfg.Field.CpuWdTmrCfgEn = 0;
    CpuWdtCfg.Field.CpuWdTmrTimebaseSel = 0;
    CpuWdtCfg.Field.CpuWdTmrCfgCount = 0;
  }
  AsmWriteMsr64 (CPU_WDT_CFG, CpuWdtCfg.Value);
}

/* -----------------------------------------------------------------------------*/
/**
 *
 *  CcxZen5GetCacWeights
 *
 *  This routine is executed by BSP and data will be shared with APs through
 *  ApLaunchGlobalData
 *
 *  @param[in] NbioSmuServices        SMU services
 *
 *  Description:
 *    This routine gets CAC weights from SMU
 *
 */
EFI_STATUS
CcxZen5GetCacWeights (
  IN       PEI_AMD_NBIO_SMU_SERVICES_PPI  *NbioSmuServices,
  IN       UINT64                         *CacWeights
  )
{
  if (SocIdentificationCheck (0x00B20F00, (FIXED_EXT_MODEL|ANY_BASE_MODEL|ANY_STEPPING|ANY_PKG_TYPE)) ||
      SocIdentificationCheck (0x00B60F00, (FIXED_EXT_MODEL|ANY_BASE_MODEL|ANY_STEPPING|ANY_PKG_TYPE))) {
    //
    // STX1 and KRK doesn't support.
    //
    return EFI_UNSUPPORTED;
  }
  return NbioSmuServices->SmuReadCacWeights (NbioSmuServices, ZEN5_CAC_WEIGHT_NUM, CacWeights);
}

/* -----------------------------------------------------------------------------*/
/**
 *
 *  CcxZen5SetCacWeights
 *
 *  This routine is executed by both the BSP and AP(s)
 *
 *  Description:
 *    This routine sets all CAC weights
 *
 */
VOID
CcxZen5SetCacWeights (
  IN       UINT64 *CacWeights
  )
{
  UINT8  WeightIndex;
  UINT64 LocalMsr;

  if (CcxIsComputeUnitPrimary (NULL)) {
    LocalMsr = AsmReadMsr64 (0xC0011074);
    AsmWriteMsr64 (0xC0011074, 0);

    for (WeightIndex = 0; WeightIndex < ZEN5_CAC_WEIGHT_NUM; WeightIndex++) {
      AsmWriteMsr64 (0xC0011076, WeightIndex);
      AsmWriteMsr64 (0xC0011077, CacWeights[WeightIndex]);
    }

    AsmWriteMsr64 (0xC0011074, (LocalMsr | BIT63));
  }
}

/*---------------------------------------------------------------------------------------*/
/**
 * Set up the BIOS owned registers associated with CC6 according to the PPR
 *
 * This routine is only executed on the BSP. APs will sync the applicable MSRs
 * settings through ApMsrSyncList
 *
 * @param[in]  StdHeader               Config handle for library and services.
 *
 * @retval     AGESA_SUCCESS           Always succeeds.
 *
 */
AGESA_STATUS
CcxZen5InitializeC6 (
  IN       AMD_CONFIG_PARAMS         *StdHeader
  )
{
  UINT8                     CStateMode;
  CSTATE_CFG_MSR            CstateCfg;
  CSTATE_CFG2_MSR           CstateCfg2;
  CSTATE_ADDRESS_MSR        CstateAddr;
  UINT8                     Cc6Enable;


  CStateMode = PcdGet8 (PcdAmdCStateMode);

  ASSERT (CStateMode <= 1);
  Cc6Enable = (PcdGet8 (PcdAmdCc6Ctrl) & 1);

  if (CStateMode == 1) {
    IDS_HDT_CONSOLE (CPU_TRACE, "  CcxZen5InitializeC6 - Enable C6\n");

    if (CcxIsComputeUnitPrimary (StdHeader)) {
      CstateCfg.Value = AsmReadMsr64 (MSR_CSTATE_CFG);
      CstateCfg2.Value = AsmReadMsr64 (MSR_CSTATE_CFG2);
      CstateCfg.Field.CCR0_CC1DFSID = 8;
      CstateCfg.Field.CCR0_CC6EN = Cc6Enable;
      CstateCfg.Field.CCR1_CC1DFSID = 8;
      CstateCfg.Field.CCR1_CC6EN = Cc6Enable;
      CstateCfg.Field.CCR2_CC1DFSID = 8;
      CstateCfg.Field.CCR2_CC6EN = Cc6Enable;
      CstateCfg.Field.CCR3_CC1DFSID = 8;
      CstateCfg.Field.CCR3_CC6EN = Cc6Enable;
      CstateCfg2.Field.CCR4_CC1DFSID = 8;
      CstateCfg2.Field.CCR4_CC6EN = Cc6Enable;
      CstateCfg2.Field.CCR5_CC1DFSID = 8;
      CstateCfg2.Field.CCR5_CC6EN = Cc6Enable;
      CstateCfg2.Field.CCR6_CC1DFSID = 8;
      CstateCfg2.Field.CCR6_CC6EN = Cc6Enable;
      CstateCfg2.Field.CCR7_CC1DFSID = 8;
      CstateCfg2.Field.CCR7_CC6EN = Cc6Enable;
      AsmWriteMsr64 (MSR_CSTATE_CFG, CstateCfg.Value);
      AsmWriteMsr64 (MSR_CSTATE_CFG2, CstateCfg2.Value);
    }

    CstateAddr.Value = 0;
    CstateAddr.Field.CstateAddr = (UINT32) PcdGet16 (PcdAmdCStateIoBaseAddress);
    AsmWriteMsr64 (MSR_CSTATE_ADDRESS, CstateAddr.Value);
  }
  return AGESA_SUCCESS;
}

/*---------------------------------------------------------------------------------------*/
/**
 * Initializes Core Performance Boost.
 *
 * Set up D18F4x15C[BoostSrc] and start the PDMs according to the BKDG.
 *
 * This routine is only executed on the BSP. APs will sync applicable
 * settings through ApMsrSyncList
 *
 * @param[in]  StdHeader               Config handle for library and services.
 *
 * @retval     AGESA_SUCCESS           Always succeeds.
 *
 */
AGESA_STATUS
CcxZen5InitializeCpb (
  IN       AMD_CONFIG_PARAMS         *StdHeader
  )
{
  UINT8                     CpbMode;
  EFI_STATUS                Status;

  Status = AGESA_SUCCESS;

  CpbMode = PcdGet8 (PcdAmdCpbMode);

  if (CpbMode == 0) {
    AsmMsrOr64 (MSR_HWCR, BIT25);
  }
  return Status;
}

/* -----------------------------------------------------------------------------*/
/**
 *
 *  CcxZen5InitializePrefetchMode
 *
 *  Description:
 *    This function provides for performance tuning to optimize for specific
 *    workloads. For general performance use the recommended settings.
 *
 *  Parameters:
 *    @param[in]  StdHeader                 Config handle for library and services
 *
 *    @retval     AGESA_STATUS
 *
 *
 */
AGESA_STATUS
CcxZen5InitializePrefetchMode (
  IN       AMD_CONFIG_PARAMS                *StdHeader
  )
{
  CCX_PREFETCH_MODE         PrefetchMode;

  PrefetchMode.SoftwarePrefetchMode = PcdGet8 (PcdAmdSoftwarePrefetchMode);
  if (PrefetchMode.SoftwarePrefetchMode != SOFTWARE_PREFETCHES_AUTO) {
    IDS_HDT_CONSOLE (CPU_TRACE, "  CcxZen5InitializePrefetchMode:\n");
    IDS_HDT_CONSOLE (CPU_TRACE, "    SoftwarePrefetchMode - %d\n", PrefetchMode.SoftwarePrefetchMode);

    // DISABLE_SOFTWARE_PREFETCHES
    if (PrefetchMode.SoftwarePrefetchMode == DISABLE_SOFTWARE_PREFETCHES) {
      // MSR_DE_CFG (MSR_C001_1029)
      //  [7:2] = 0x3F
      if (CcxIsComputeUnitPrimary (NULL)) {
        AsmMsrOr64 (MSR_DE_CFG, 0xFC);
      }
    }
  }
  return AGESA_SUCCESS;
}

/* -----------------------------------------------------------------------------*/
/**
 *
 *  CcxZen5SyncMiscMsrs
 *
 *  This routine is only executed by the APs to sync MSR data to match BSP
 *  Description:
 *    This routine synchronizes the MSRs in ApLaunchGlobalData->ApMsrSyncList across all APs
 *
 */
VOID
CcxZen5SyncMiscMsrs (
  IN       VOLATILE AMD_CCX_AP_LAUNCH_GLOBAL_DATA *ApLaunchGlobalData
  )
{
  UINTN  i;

  for (i = 0; ApLaunchGlobalData->ApMsrSyncList[i].MsrAddr != CPU_LIST_TERMINAL; i++) {
    AsmMsrAndThenOr64 (
        ApLaunchGlobalData->ApMsrSyncList[i].MsrAddr,
        ~(ApLaunchGlobalData->ApMsrSyncList[i].MsrMask),
        (ApLaunchGlobalData->ApMsrSyncList[i].MsrData & ApLaunchGlobalData->ApMsrSyncList[i].MsrMask)
        );
  }
}

/* -----------------------------------------------------------------------------*/
/**
 *  Notification function called when MP Services Ppi is installed.
 *
 *  This installs Smm Access PPI
 */
EFI_STATUS
EFIAPI
CcxInitWithMpServices (
  IN EFI_PEI_SERVICES           **PeiServices,
  IN EFI_PEI_NOTIFY_DESCRIPTOR  *NotifyDescriptor,
  IN VOID                       *Ppi
  )
{
  return CcxSmmAccessPpiInstall ((CONST EFI_PEI_SERVICES **)PeiServices);
}
