/*
 ******************************************************************************
 *
 * Copyright (C) 2008-2024 Advanced Micro Devices, Inc. All rights reserved.
 *
 *******************************************************************************
 */
/* $NoKeywords:$ */
/**
 * @file
 *
 * PSP Base Library
 *
 * Contains interface to the PSP library
 *
 * @xrefitem bom "File Content Label" "Release Content"
 * @e project:      AGESA
 * @e sub-project:  PSP
 * @e \$Revision: 309090 $   @e \$Date: 2014-12-10 02:28:05 +0800 (Wed, 10 Dec 2014) $
 *
 */

/*----------------------------------------------------------------------------------------
 *                             M O D U L E S    U S E D
 *----------------------------------------------------------------------------------------
 */
#include <Uefi.h>
#include <Base.h>
#include "AGESA.h"
#include "Filecode.h"
#include <Library/BaseLib.h>
#include <Library/BaseMemoryLib.h>
#include <Library/AmdBaseLib.h>
#include <Library/AmdSocBaseLib.h>
#include <Library/AmdPspBaseLibV2.h>
#include <Library/AmdPspCommonLib.h>
#include <Library/PciLib.h>
#include <Library/AmdPspMmioLib.h>
#include <Library/AmdPspRegMuxLibV2.h>
#include <Library/TimerLib.h>
#include <Library/AmdPspRegBaseLib.h>

#define FILECODE LIBRARY_AMDPSPBASELIBV2_AMDPSPBASELIBV2_FILECODE

/*----------------------------------------------------------------------------------------
 *                   D E F I N I T I O N S    A N D    M A C R O S
 *----------------------------------------------------------------------------------------
 */
#define TIMEOUT_WAIT_INFINITELY 0xFFFFFFFFL

#define C2PMSG_37_OFFSET 0x10594
#define C2PMSG_38_OFFSET 0x10598

#define NB_SMN_INDEX_2_PCI_ADDR (MAKE_SBDFO (0, 0, 0, 0, 0xB8))   ///< PCI Addr of NB_SMN_INDEX_2
#define NB_SMN_DATA_2_PCI_ADDR  (MAKE_SBDFO (0, 0, 0, 0, 0xBC))   ///< PCI Addr of NB_SMN_DATA_2

/*----------------------------------------------------------------------------------------
 *                  T Y P E D E F S     A N D     S T R U C T U R E S
 *----------------------------------------------------------------------------------------
 */


/*----------------------------------------------------------------------------------------
 *           P R O T O T Y P E S     O F     L O C A L     F U N C T I O N S
 *----------------------------------------------------------------------------------------
 */

/**
 * @brief Woker function, implemented in assembly
 *
 * @param Rand
 *
 * @return BOOLEAN
 */
BOOLEAN
EFIAPI
InternalX86RdSeed32 (
  OUT     UINT32                    *Rand
  );

/**
 * @brief Check if PSP device is present
 *
 * @retval BOOLEAN  0: PSP Disabled, 1: PSP Enabled
 */
BOOLEAN
EFIAPI
CheckPspDevicePresentV2 (
  VOID
  )
{
  return (TRUE);
}

/**
 * @brief Check PSP Platform Seucre Enable State
 * HVB & Secure S3 (Resume vector set to Dram, & core content will restore by uCode)
 * will be applied if Psp Plaform Secure is enabled
 *
 * @retval BOOLEAN  0: PSP Platform Secure Disabled, 1: PSP Platform Secure  Enabled
 */
BOOLEAN
CheckPspPlatformSecureEnableV2 (
  VOID
  )
{
  ///@todo Add code to read SMU FUSE shadow register
  return (TRUE);
}

/**
 * @brief Return the PspMbox MMIO location
 *
 * @retval EFI_STATUS  0: Success, NonZero Error
 */
BOOLEAN
GetPspMboxLocation (
  IN OUT   PSP_MBOX_V2 **PspMbox
  )
{
  UINT32  PspMmioBase;
  UINT32  PspC2pMsgRegBaseOffset;

  if (GetPspMmioBase (&PspMmioBase) == FALSE) {
    return FALSE;
  }

  PspC2pMsgRegBaseOffset = GetPspC2pMsgRegBaseOffset ();

  *PspMbox = (PSP_MBOX_V2 *) (UINTN) (PspMmioBase + PspC2pMsgRegBaseOffset + BIOS_MAILBOX_BASE);

  return TRUE;
}

/**
 * @brief Check PSP Recovery Flag
 * Target will set Recovery flag if some PSP entry point by PSP directory has been corrupted.
 *
 * @retval BOOLEAN  0: Recovery Flag is cleared, 1: Recovery Flag has been set
 */
BOOLEAN
CheckPspRecoveryFlagV2 (
  VOID
  )
{
  PSP_MBOX_V2 *PspMbox;

  PspMbox = NULL;

  if (PcdGetBool (PcdAmdPspRecoveryFlagDetectEnable) == FALSE) {
    return FALSE;
  }

  // If Psp Mmio is not ready, read recovery flag from Smn register.
  if (GetPspMboxLocation (&PspMbox) == FALSE) {
    return CheckPspRecoveryFlagSmn ();
  }

  ASSERT (PspMbox->Cmd.Value != 0xFFFFFFFF);

  return (BOOLEAN) (PspMbox->Cmd.Field.Recovery);
}

/**
 * @brief Return the PspMMIO MMIO location
 *
 * @param[in, out] PspMmioBase Pointer to Psp MMIO address
 *
 * @retval BOOLEAN  0: Error, 1 Success
 */
BOOLEAN
GetPspMmioBase (
  IN OUT   UINT32 *PspMmioBase
  )
{
  UINT32    Value32;
  UINTN     PciAddress;
  UINT32    SmnBase;

  *PspMmioBase = 0;

  GetPspIOHCxNbMiscSmnBase (&SmnBase, NULL);

  PciAddress = NB_SMN_INDEX_2_PCI_ADDR;
  Value32 = SmnBase + NBMSIC_PSP_BASE_ADDR_LO_OFFSET;
  PciWrite32 (PciAddress, Value32);
  PciAddress = NB_SMN_DATA_2_PCI_ADDR;
  Value32 = PciRead32 (PciAddress);
  //Mask out the lower bits
  Value32 &= 0xFFF00000;

  if (Value32 == 0) {
    return (FALSE);
  }

  *PspMmioBase = Value32;
  return (TRUE);
}

///@todo remove below FCH definition when it has been declared by FCH module
#define ACPI_MMIO_BASE  0xFED80000ul
#define PMIO_BASE       0x300   // DWORD
#define FCH_PMIOA_REG64          0x64         // AcpiPmTmrBlk

/**
 * @brief Check if time has been used up, if not, sleep and count 1 microsecond down
 *
 * @param[in, out]  CountdownInMicroseconds   Count down number in microseconds
 *
 * @retval BOOLEAN  TRUE: timedout, FALSE: not timedout
 */
BOOLEAN
IsTimedOut (
  IN OUT UINT32 *CountdownInMicroseconds
  )
{
  //time has been used up, so timed out
  if ((*CountdownInMicroseconds) == 0) {
    return TRUE;
  }

  //wait infinitely, so never timed out
  if ((*CountdownInMicroseconds) == TIMEOUT_WAIT_INFINITELY) {
    return FALSE;
  }

  //sleep 1 microsecond
  MicroSecondDelay(1);
  (*CountdownInMicroseconds)--;

  //not timed out this time
  return FALSE;
}

/**
 * @brief PSP Time out function with conditioner
 * The routine will exit in two conditions:
 * 1. Time out of input uSec
 * 2. Conditioner function return TRUE
 *
 * @param[in] uSec         Timer in microseconds
 * @param[in] Conditioner  Function for check condition of exit the timeout routine
 * @param[in] Context      Conditioner function context
 *
 * @retval BOOLEAN  FALSE: Timer exceed, TRUE: condition match
 */
BOOLEAN
EFIAPI
PspLibTimeOutV2 (
  IN       UINT64              uSec,
  IN       FP_CONDITIONER      Conditioner,
  IN       VOID                *Context
  )
{
  UINT16 timerAddr;
  UINT32 startTime;
  UINT64 elapsedTime;

  // Return immediately if condition already match
  if (Conditioner (Context) == TRUE) {
    return TRUE;
  }

  //Check if infinitely flag set
  if (uSec == PSPLIB_WAIT_INFINITELY) {
    IDS_HDT_CONSOLE_PSP_TRACE ("Wait infinitely ..\n");
    while (Conditioner (Context) == FALSE) {
      ;
    }
    return TRUE;
  }

  //Check availibitly of ACPI timer
  LibAmdMemRead (AccessWidth16, (UINT64) (ACPI_MMIO_BASE + PMIO_BASE + FCH_PMIOA_REG64), &timerAddr, NULL);
  //Using IO delay, if ACPI timer is not available, it is not accurate, since the conditioner function will take time
  if ( timerAddr == 0 ) {
    uSec = DivU64x32 (uSec, 2);
    while ( uSec != 0) {
      if (Conditioner (Context) == TRUE) {
        return TRUE;
      }
      LibAmdIoRead (AccessWidth8, 0xEB, (UINT8*)&elapsedTime, NULL);
      uSec--;
    }
  } else {
    LibAmdIoRead (AccessWidth32, timerAddr, &startTime, NULL);
    elapsedTime = 0;
    for ( ;; ) {
      LibAmdIoRead (AccessWidth32, timerAddr, &elapsedTime, NULL);
      if ( elapsedTime <= startTime ) {
        elapsedTime = elapsedTime + 0xFFFFFFFFul - startTime;
      } else {
        elapsedTime = elapsedTime - startTime;
      }

      if (Conditioner (Context) == TRUE) {
        return TRUE;
      }

      if (DivU64x32 ( MultU64x32 (elapsedTime, 28), 100 ) > uSec ) {
        break;
      }
    }
  }
  return FALSE;
}

/**
 * @brief Get the Ftpm Control Area object
 *
 * @param[in,out] FtpmControlArea Get the Ftpm Control Area Value
 *
 * @return BOOLEAN  FALSE: Error, TRUE: Success
 */
BOOLEAN
EFIAPI
GetFtpmControlAreaV2 (
  IN OUT   VOID **FtpmControlArea
  )
{
  UINT32  PspMmioBase;
  UINT32  PspC2pMsgRegBaseOffset;

  if (GetPspMmioBase (&PspMmioBase) == FALSE) {
    return FALSE;
  }

  PspC2pMsgRegBaseOffset = GetPspC2pMsgRegBaseOffset ();

  *FtpmControlArea = (VOID *) (UINTN) (PspMmioBase + PspC2pMsgRegBaseOffset);

  return TRUE;
}


VOID
EFIAPI
SwitchPspMmioDecodeV2 (
  IN       BOOLEAN SwitchFlag,
  IN OUT   UINT32 *RegisterCopy
  )
{
}


/**
 * @brief Acquire the Mutex for access PSP,X86 co-accessed register
 * Call this routine before access SMIx98 & SMIxA8
 */
VOID
AcquirePspSmiRegMutexV2 (
  VOID
  )
{
  AcquirePspAccRegMutex ();
}

/**
 * @brief Release the Mutex for access PSP,X86 co-accessed register
 * Call this routine after access SMIx98 & SMIxA8
 */
VOID
ReleasePspSmiRegMutexV2 (
  VOID
  )
{
  ReleasePspAccRegMutex ();
}

/**
 * @brief Get the Psb Status from C2P register
 *
 * @param PsbStatus2 Pointer of PsbStatus2
 *
 * @return BOOLEAN  FALSE: Error, TRUE: Success
 */
BOOLEAN
GetPsbStatus2 (
  PSB_STATUS_2 *PsbStatus2
  )
{
  UINT32            PspMmioBase;
  UINT32            RegValue;
  UINT32            C2pMsg37Offset;

  PspMmioBase    = 0;
  C2pMsg37Offset = C2PMSG_37_OFFSET;

  if (GetPspMmioBase (&PspMmioBase) == FALSE) {
    return FALSE;
  }

  // MP0_C2PMSG_37 register address is different between family
  if (SocFamilyIdentificationCheck (F17_ZP_RAW_ID)) {
    RegValue = PspMmioRead (C2pMsg37Offset);
  } else {
    C2pMsg37Offset = GetPspC2pMsgRegOffset (PSP_C2PMSG_37);
    RegValue = PspMmioRead (C2pMsg37Offset);
  }

  ASSERT (sizeof (PSB_STATUS_2) == sizeof (UINT32));
  CopyMem (PsbStatus2, &RegValue, sizeof (PSB_STATUS_2));

  return TRUE;
}

/**
 * @brief Get PsbHstiStatus from C2P register
 *
 * @return BOOLEAN  FALSE: Error, TRUE: Success
 */
BOOLEAN
GetPsbHstiStatus1 (
  PSB_HSTI_STATUS_1 *PsbHstiStatus1
  )
{
  UINT32            RegValue;
  UINT32            PspMmioBase;
  UINT32            C2pMsg38Offset;

  PspMmioBase    = 0;
  C2pMsg38Offset = C2PMSG_38_OFFSET;

  if (GetPspMmioBase (&PspMmioBase) == FALSE) {
    return FALSE;
  }

  // MP0_C2PMSG_38 register address is different between family
  if (SocFamilyIdentificationCheck (F17_ZP_RAW_ID)) {
    RegValue = PspMmioRead (C2pMsg38Offset);
  } else {
    C2pMsg38Offset = GetPspC2pMsgRegOffset (PSP_C2PMSG_38);
    RegValue = PspMmioRead (C2pMsg38Offset);
  }

  ASSERT (sizeof (PSB_HSTI_STATUS_1) == sizeof (UINT32));
  CopyMem (PsbHstiStatus1, &RegValue, sizeof (PSB_HSTI_STATUS_1));

  return TRUE;
}


/**
 * @brief Loads the destination register with a hardware-generated random "seed" value
 *
 * @param Seed  buffer to store 32bits seed value
 *
 * @return BOOLEAN TRUE: RDSEED succeed FALSE: error
 */
BOOLEAN
EFIAPI
AsmRdSeed32 (
  OUT     UINT32                    *Seed
  )
{
  ASSERT (Seed != NULL);
  return InternalX86RdSeed32 (Seed);
}

/**
 * @brief Dump RDRAND, RDSEED related information
 *
 * @return VOID
 */
VOID
DumpRdInstructionInfo (
  )
{
  UINT32 Value32;
  UINT64 Value64;
  UINT32 RdValue1;
  UINT32 RdValue2;
  BOOLEAN RdStatus1;
  BOOLEAN RdStatus2;
  Value32 = 0;
  Value64 = 0;
  RdStatus1 = FALSE;

  //Print RdInstruction capability from CPUID
  // CPUID_Fn00000001_ECX [Feature Identifiers] (Core::X86::Cpuid::FeatureIdEcx)
  // BIT30 | RDRAND. Read-only. Reset: Fixed,1. RDRAND instruction support
  AsmCpuid (0x00000001, NULL, NULL, &Value32, NULL);
  IDS_HDT_CONSOLE_PSP_TRACE ("CPUID_Fn00000001_ECX_RDRAND %a\n", (Value32 & BIT30) ? "Supported" : "Unsupported");
  // CPUID_Fn00000007_EBX_x00 [Structured Extended Feature Identifiers]
  // BIT18 | RDSEED. Read-only. Reset: Fixed,1. RDSEED is present.
  AsmCpuidEx (0x00000007, 0, NULL, &Value32, NULL, NULL);
  IDS_HDT_CONSOLE_PSP_TRACE ("CPUID_Fn00000007_EBX_RDSEED %a\n", (Value32 & BIT18)? "Supported" : "Unsupported");
  //Print RdInstruction enable status from MSR
  // MSRC001_1004 [CPUID Features for CPUID Fn00000001_E[C,D]X] (Core::X86::Msr::CPUID_Features)
  // BIT62 RDRAND
  Value64 = AsmReadMsr64 (0xC0011004);
  IDS_HDT_CONSOLE_PSP_TRACE ("MSRC001_1004_RDRAND %a\n", (Value64 & BIT62) ? "Enabled" : "Disabled");
  //MSRC001_1002 [CPUID Features for CPUID Fn00000007_E[A,B]X] (Core::X86::Msr::CPUID_7_Features)
  // BIT18 RDSEED
  Value64 = AsmReadMsr64 (0xC0011002);
  IDS_HDT_CONSOLE_PSP_TRACE ("MSRC001_1002_RDSEED %a\n", (Value64 & BIT18) ? "Enabled" : "Disabled");
  //Verify RdInstruction function by generate random value twice and check if identical


  RdValue1 = 0xFFFFFFFFul;
  RdValue2 = 0xFFFFFFFFul;
  RdStatus1 = AsmRdRand32 (&RdValue1);
  RdStatus2 = AsmRdRand32 (&RdValue2);

  IDS_HDT_CONSOLE_PSP_TRACE ("RDRAND verify: RdValue1:0x%x RdValue2:0x%x\n", RdValue1, RdValue2);
  if (((RdStatus1 & RdStatus2) == FALSE) || (RdValue1 == 0xFFFFFFFFul) || (RdValue1 == RdValue2)) {
    IDS_HDT_CONSOLE_PSP_TRACE ("%a\n", "FAIL");
  } else {
    IDS_HDT_CONSOLE_PSP_TRACE ("%a\n", "PASS");
  }

  RdValue1 = 0xFFFFFFFFul;
  RdValue2 = 0xFFFFFFFFul;
  RdStatus1 = AsmRdSeed32 (&RdValue1);
  RdStatus2 = AsmRdSeed32 (&RdValue2);
  IDS_HDT_CONSOLE_PSP_TRACE ("RDSEED verify: RdValue1:0x%x RdValue2:0x%x\n", RdValue1, RdValue2);
  if (((RdStatus1 & RdStatus2) == FALSE) || (RdValue1 == 0xFFFFFFFFul) || (RdValue1 == RdValue2)) {
    IDS_HDT_CONSOLE_PSP_TRACE ("%a\n", "FAIL");
  } else {
    IDS_HDT_CONSOLE_PSP_TRACE ("%a\n", "PASS");
  }

}

