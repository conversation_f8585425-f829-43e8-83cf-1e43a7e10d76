/*****************************************************************************
 *
 * Copyright (C) 2008-2024 Advanced Micro Devices, Inc. All rights reserved.
 *
 *******************************************************************************
 */
/*++
Module Name:

  CcxResetTablesZen5Lib.c
  Apply register table settings

Abstract:
--*/

#include <AGESA.h>
#include <Library/AmdTableLibV2.h>
#include <SocLogicalId.h>
#include <Filecode.h>

#define FILECODE LIBRARY_CCXRESETTABLESZEN5LIB_CCXRESETTABLESZEN5LIB_FILECODE

CONST UINT8 ROMDATA CcxZen5AllCoreRegistersAfterApLaunch[] = {
// To enable x2APIC mode, we have to set ApicEn first then set x2ApicEn
// Transition from disabled mode to x2APIC mode is illegal per x2APIC spec

// APIC_BAR (0x0000001B)
// bit[11]     ApicEn = 1
  MAKE_MSR_ENTRY (0x0000001B, 0x0000000000000800, 0x0000000000000800),

// APIC_BAR (0x0000001B)
// bit[10]     x2ApicEn = 1
  MAKE_MSR_PLATFORM_FEAT_ENTRY (AMD_PF_X2APIC, 0x0000001B, 0x0000000000000400, 0x0000000000000400),

// HWCR (0xC0010015)
// bit[32]     FastTprLoweringDis = 1 for xApic
// bit[27]     EffFreqReadOnlyLock = 1
// bit[14]     RsmSpCycDis = 1
// bit[13]     SmiSpCycDis = 1
  MAKE_MSR_ENTRY (0xC0010015, 0x0000000008006000, 0x0000000008006000),
  MAKE_MSR_PLATFORM_FEAT_ENTRY (AMD_PF_APIC, 0xC0010015, 0x0000000100000000, 0x0000000100000000),

// MCA_CTL_MASK_LS (0xC0010400)
// bit [25]    SystemReadDataErrorWcb = 1
// bit [10]    SystemReadDataErrorMab = 1
// bit [09]    SystemReadDataErrorUcode = 1
  MAKE_MSR_ENTRY (0xC0010400, 0x0000000002000600, 0x0000000002000600),

// MCA_CTL_MASK_IF (0xC0010401)
// bit [13]    SystemReadDataError = 1
  MAKE_MSR_ENTRY (0xC0010401, 0x0000000000002000, 0x0000000000002000),

// MCA_CTL_MASK_L2 (0xC0010402)
// bit[3]     Hwa = 1
  MAKE_MSR_ENTRY (0xC0010402, 0x0000000000000008, 0x0000000000000008),

// MCA_CTL_MASK_FP (0xC0010406)
// bit [6]     Hwa = 1
  MAKE_MSR_ENTRY (0xC0010406, 0x0000000000000040, 0x0000000000000040),

// MCA_CTL_MASK_NBIO (0xC001041B)
// bit [5]     Int_ErrEvent = 1 for GNR, BRH, BRHD
// bit [2]     Ext_ErrEvent = 1 for GNR, BRH, BRHD
// bit [1]     PCIE_Sideband = 1 for GNR, BRH, BRHD
  MAKE_MSR_CPU_REV_ENTRY (AMD_FAMILY_GNR,  AMD_REV_F1A_GNR_ALL,  0xC001041B, 0x0000000000000026, 0x0000000000000026),
  MAKE_MSR_CPU_REV_ENTRY (AMD_FAMILY_BRH,  AMD_REV_F1A_BRH_ALL,  0xC001041B, 0x0000000000000026, 0x0000000000000026),
  MAKE_MSR_CPU_REV_ENTRY (AMD_FAMILY_BRHD, AMD_REV_F1A_BRHD_ALL, 0xC001041B, 0x0000000000000026, 0x0000000000000026),

// MCA_CTL_MASK_NBIO (0xC001041C)
// bit [5]     Int_ErrEvent = 1 for KRK
// bit [2]     Ext_ErrEvent = 1 for KRK
// bit [1]     PCIE_Sideband = 1 for STX, KRK
  MAKE_MSR_CPU_REV_ENTRY (AMD_FAMILY_STX, AMD_REV_F1A_STX_ALL, 0xC001041C, 0x0000000000000002, 0x0000000000000002),
  MAKE_MSR_CPU_REV_ENTRY (AMD_FAMILY_KRK, AMD_REV_F1A_KRK1_KRK2_ALL, 0xC001041C, 0x0000000000000026, 0x0000000000000026),

// MCA_CTL_MASK_NBIO (0xC001041D)
// bit [5]     Int_ErrEvent = 1 for STXH
// bit [2]     Ext_ErrEvent = 1 for STXH
// bit [1]     PCIE_Sideband = 1 for STXH
  MAKE_MSR_CPU_REV_ENTRY (AMD_FAMILY_STXH, AMD_REV_F1A_STXH_ALL, 0xC001041D, 0x0000000000000026, 0x0000000000000026),

// CPUID_FEATURES (0xC0011004)
// bit[62]     RDRAND = 0
  MAKE_MSR_PLATFORM_FEAT_ENTRY (AMD_PF_EMULATION, 0xC0011004, 0x0000000000000000, 0x4000000000000000),

// CPUID_FEATURES (0xC0011002)
// bit[18]     RDSEED = 0
// bit[9]      ERMS = 1 for STXH
  MAKE_MSR_PLATFORM_FEAT_ENTRY (AMD_PF_EMULATION, 0xC0011002, 0x0000000000000000, 0x0000000000040000),
  MAKE_MSR_CPU_REV_ENTRY (AMD_FAMILY_STXH, AMD_REV_F1A_STXH_ALL, 0xC0011002, 0x0000000000000200, 0x0000000000000200),

// Reserved (0xC0011000)
// bit [20]    Reserved = 1 for STX, GNR Ax
  MAKE_MSR_CPU_REV_ENTRY (AMD_FAMILY_STX,  AMD_REV_F1A_STX_ALL,  0xC0011000, 0x0000000000100000, 0x0000000000100000),
  MAKE_MSR_CPU_REV_ENTRY (AMD_FAMILY_GNR,  AMD_REV_F1A_GNR_Ax,   0xC0011000, 0x0000000000100000, 0x0000000000100000),

// Table Terminator
  MAKE_TABLE_TERMINATOR
};

STATIC CONST UINT8 ROMDATA CcxZen5ComputeUnitRegistersAfterApLaunch[] =
{
//  M S R    T a b l e s
// ----------------------
//MSR C000_0410 McaIntrCfg
// bit[3] Reserved = 1
  MAKE_MSR_CPU_REV_ENTRY (AMD_FAMILY_BRH, AMD_REV_F1A_BRH_ALL, 0xC0000410, BIT3, BIT3),

// SYS_CFG (0xC0010010)
// bit[20]    MtrrVarDramEn = 1
// bit[18]    MtrrFixDramEn = 1
  MAKE_MSR_ENTRY (0xC0010010, 0x0000000000140000, 0x0000000000140000),

// LS_CFG (0xC0011020)
// bit[62]    Reserved = 1 for BRH, BRHD, STX, STXH, GNR, KRK
// bit[52:50] Reserved = 1 for STXH, KRK, GNR
// bit[46]    Reserved = 1 for STXH, KRK, GNR
// bit[29]    Reserved = 1 for STX Ax, GNR Ax
  MAKE_MSR_ENTRY (0xC0011020, 0x0004400000000000, 0x001C400000000000),
  MAKE_MSR_CPU_REV_ENTRY (AMD_FAMILY_BRH, AMD_REV_F1A_BRH_ALL,  0xC0011020, 0x4000000000000000, 0x4000000000000000),
  MAKE_MSR_CPU_REV_ENTRY (AMD_FAMILY_BRH, AMD_REV_F1A_BRHD_ALL, 0xC0011020, 0x4000000000000000, 0x4000000000000000),
  MAKE_MSR_CPU_REV_ENTRY (AMD_FAMILY_STX, AMD_REV_F1A_STX_ALL,  0xC0011020, 0x4000000000000000, 0x4000000000000000),
  MAKE_MSR_CPU_REV_ENTRY (AMD_FAMILY_STX, AMD_REV_F1A_STX_Ax,   0xC0011020, 0x0000000020000000, 0x0000000020000000),
  MAKE_MSR_CPU_REV_ENTRY (AMD_FAMILY_STXH,AMD_REV_F1A_STXH_ALL, 0xC0011020, 0x4004400000000000, 0x401C400000000000),
  MAKE_MSR_CPU_REV_ENTRY (AMD_FAMILY_KRK, AMD_REV_F1A_KRK1_KRK2_ALL,  0xC0011020, 0x4004400000000000, 0x401C400000000000),
  MAKE_MSR_CPU_REV_ENTRY (AMD_FAMILY_GNR, AMD_REV_F1A_GNR_ALL,  0xC0011020, 0x4000000000000000, 0x4000000000000000),
  MAKE_MSR_CPU_REV_ENTRY (AMD_FAMILY_GNR, AMD_REV_F1A_GNR_Ax,   0xC0011020, 0x0000000020000000, 0x0000000020000000),

// IC_CFG (0xC0011021)
// bit[8]     Reserved = 1 for STX Ax, GNR Ax
// bit[6]     Reserved = 1 for STX, STXH, GNR, KRK
// bit[4]     Reserved = 1 for STX Ax, GNR Ax
  MAKE_MSR_CPU_REV_ENTRY (AMD_FAMILY_STX,  AMD_REV_F1A_STX_ALL, 0xC0011021, 0x0000000000000040, 0x0000000000000040),
  MAKE_MSR_CPU_REV_ENTRY (AMD_FAMILY_STX,  AMD_REV_F1A_STX_Ax,  0xC0011021, 0x0000000000000110, 0x0000000000000110),
  MAKE_MSR_CPU_REV_ENTRY (AMD_FAMILY_STXH, AMD_REV_F1A_STXH_ALL,0xC0011021, 0x0000000000000040, 0x0000000000000040),
  MAKE_MSR_CPU_REV_ENTRY (AMD_FAMILY_GNR,  AMD_REV_F1A_GNR_ALL, 0xC0011021, 0x0000000000000040, 0x0000000000000040),
  MAKE_MSR_CPU_REV_ENTRY (AMD_FAMILY_GNR,  AMD_REV_F1A_GNR_Ax,  0xC0011021, 0x0000000000000110, 0x0000000000000110),
  MAKE_MSR_CPU_REV_ENTRY (AMD_FAMILY_KRK,  AMD_REV_F1A_KRK1_KRK2_ALL, 0xC0011021, 0x0000000000000040, 0x0000000000000040),

// TW_CFG (0xC0011023)
// bit[46]    TlbiBackToBackCntAlways = 1 for BRH Ax, BRH B0, BRHD Ax, GNR Ax, STX, STXH, KRK
  MAKE_MSR_CPU_REV_ENTRY (AMD_FAMILY_BRH,  AMD_REV_F1A_BRH_Ax,   0xC0011023, 0x0000400000000000, 0x0000400000000000),
  MAKE_MSR_CPU_REV_ENTRY (AMD_FAMILY_BRH,  AMD_REV_F1A_BRH_B0,   0xC0011023, 0x0000400000000000, 0x0000400000000000),
  MAKE_MSR_CPU_REV_ENTRY (AMD_FAMILY_BRHD, AMD_REV_F1A_BRHD_Ax,  0xC0011023, 0x0000400000000000, 0x0000400000000000),
  MAKE_MSR_CPU_REV_ENTRY (AMD_FAMILY_GNR,  AMD_REV_F1A_GNR_Ax,   0xC0011023, 0x0000400000000000, 0x0000400000000000),
  MAKE_MSR_CPU_REV_ENTRY (AMD_FAMILY_STX,  AMD_REV_F1A_STX_ALL,  0xC0011023, 0x0000400000000000, 0x0000400000000000),
  MAKE_MSR_CPU_REV_ENTRY (AMD_FAMILY_STXH, AMD_REV_F1A_STXH_ALL, 0xC0011023, 0x0000400000000000, 0x0000400000000000),
  MAKE_MSR_CPU_REV_ENTRY (AMD_FAMILY_KRK,  AMD_REV_F1A_KRK1_KRK2_ALL,  0xC0011023, 0x0000400000000000, 0x0000400000000000),

// Reserved (0xC0011028)
// bit[41]    Reserved = 1 for STX Ax, GNR Ax
// bit[33]    Reserved = 1 for STX Ax, GNR Ax
// bit[33]    Reserved = 0 for STX Bx, GNR Bx
// bit[32]    Reserved = 0 for STX Ax, GNR
  MAKE_MSR_CPU_REV_ENTRY (AMD_FAMILY_STX,  AMD_REV_F1A_STX_Ax, 0xC0011028, 0x0000020200000000, 0x0000020300000000),
  MAKE_MSR_CPU_REV_ENTRY (AMD_FAMILY_STX,  AMD_REV_F1A_STX_Bx, 0xC0011028, 0x0000000000000000, 0x0000000300000000),
  MAKE_MSR_CPU_REV_ENTRY (AMD_FAMILY_GNR,  AMD_REV_F1A_GNR_Ax, 0xC0011028, 0x0000020200000000, 0x0000020300000000),
  MAKE_MSR_CPU_REV_ENTRY (AMD_FAMILY_GNR,  AMD_REV_F1A_GNR_Bx, 0xC0011028, 0x0000000000000000, 0x0000000300000000),

// ChL2Cfg0 (0xC001102A)
// bit[28]    Reserved = 1 for STX Ax, GNR Ax
// bit[15]    Reserved = 1
// bit[7]     Reserved = 1
  MAKE_MSR_ENTRY (0xC001102A, 0x0000000000008080, 0x0000000000008080),
  MAKE_MSR_CPU_REV_ENTRY (AMD_FAMILY_STX,  AMD_REV_F1A_STX_Ax, 0xC001102A, 0x0000000010000000, 0x0000000010000000),
  MAKE_MSR_CPU_REV_ENTRY (AMD_FAMILY_GNR,  AMD_REV_F1A_GNR_Ax, 0xC001102A, 0x0000000010000000, 0x0000000010000000),

// Reserved (0xC001102C)
// bit[42]    Reserved = 1 for STX, GNR
// bit[40]    Reserved = 1 for STX, GNR
// bit[39]    Reserved = 1 for STX, GNR
// bit[33:35] Reserved = 6 for STX, GNR
// bit[28]    Reserved = 1 for STX Ax, GNR Ax
  MAKE_MSR_CPU_REV_ENTRY (AMD_FAMILY_STX,  AMD_REV_F1A_STX_Ax, 0xC001102C, 0x0000058C10000000, 0x0000058E10000000),
  MAKE_MSR_CPU_REV_ENTRY (AMD_FAMILY_STX,  AMD_REV_F1A_STX_Bx, 0xC001102C, 0x0000058C00000000, 0x0000058E00000000),
  MAKE_MSR_CPU_REV_ENTRY (AMD_FAMILY_GNR,  AMD_REV_F1A_GNR_ALL,0xC001102C, 0x0000058C00000000, 0x0000058E00000000),
  MAKE_MSR_CPU_REV_ENTRY (AMD_FAMILY_GNR,  AMD_REV_F1A_GNR_Ax, 0xC001102C, 0x0000000010000000, 0x0000000010000000),

// LS_CFG2 (0xC001102D)
// bit[56]    Reserved = 1 for BRH Ax/Bx, BRHD Ax, STX Ax, GNR, KRK
// bit[56]    Reserved = 0 for STX Bx, STXH
// bit[55]    Reserved = 0 for STX Ax, GNR Ax
// bit[37]    Reserved = 1 for STX, STXH, GNR, KRK
// bit[36:35] Reserved = 1
// bit[34]    Reserved = 1 for BRH, BRHD, STX, STXH, GNR, KRK
// bit[28]    Reserved = 0 for STX Ax
// bit[28]    Reserved = 1 for GNR Ax
// bit[5]     Reserved = 1 for STX, STXH, GNR, KRK
  MAKE_MSR_ENTRY (0xC001102D, 0x0000000800000000, 0x0000001800000000),
  MAKE_MSR_CPU_REV_ENTRY (AMD_FAMILY_BRH,   AMD_REV_F1A_BRH_Ax,    0xC001102D, 0x0100000000000000, 0x0100000000000000),
  MAKE_MSR_CPU_REV_ENTRY (AMD_FAMILY_BRH,   AMD_REV_F1A_BRH_Bx,    0xC001102D, 0x0100000000000000, 0x0100000000000000),
  MAKE_MSR_CPU_REV_ENTRY (AMD_FAMILY_BRHD,  AMD_REV_F1A_BRHD_Ax,   0xC001102D, 0x0100000000000000, 0x0100000000000000),
  MAKE_MSR_CPU_REV_ENTRY (AMD_FAMILY_BRH,   AMD_REV_F1A_BRH_ALL,   0xC001102D, 0x0000000400000000, 0x0000000400000000),
  MAKE_MSR_CPU_REV_ENTRY (AMD_FAMILY_BRHD,  AMD_REV_F1A_BRHD_ALL,  0xC001102D, 0x0000000400000000, 0x0000000400000000),
  MAKE_MSR_CPU_REV_ENTRY (AMD_FAMILY_STX,   AMD_REV_F1A_STX_ALL,   0xC001102D, 0x0100000000000000, 0x0100000000000000),
  MAKE_MSR_CPU_REV_ENTRY (AMD_FAMILY_GNR,   AMD_REV_F1A_GNR_ALL,   0xC001102D, 0x0100002400000020, 0x0100002400000020),
  MAKE_MSR_CPU_REV_ENTRY (AMD_FAMILY_GNR,   AMD_REV_F1A_GNR_Ax,    0xC001102D, 0x0000000010000000, 0x0080000010000000),
  MAKE_MSR_CPU_REV_ENTRY (AMD_FAMILY_STX,   AMD_REV_F1A_STX_Ax,    0xC001102D, 0x0100002410000020, 0x0180002410000020),
  MAKE_MSR_CPU_REV_ENTRY (AMD_FAMILY_STX,   AMD_REV_F1A_STX_Bx,    0xC001102D, 0x0000002400000020, 0x0100002400000020),
  MAKE_MSR_CPU_REV_ENTRY (AMD_FAMILY_STXH,  AMD_REV_F1A_STXH_ALL,  0xC001102D, 0x0000002400000020, 0x0100002400000020),
  MAKE_MSR_CPU_REV_ENTRY (AMD_FAMILY_KRK,   AMD_REV_F1A_KRK1_KRK2_ALL,   0xC001102D, 0x0100002400000020, 0x0100002400000020),

// Reserved (0xC001102E)
// bit[42]    Reserved = 1 for STX Bx, GNR Bx
// bit[17]    Reserved = 1 for STX Bx, GNR Bx
  MAKE_MSR_CPU_REV_ENTRY (AMD_FAMILY_STX,  AMD_REV_F1A_STX_Bx, 0xC001102E, 0x0000040000020000, 0x0000040000020000),
  MAKE_MSR_CPU_REV_ENTRY (AMD_FAMILY_GNR,  AMD_REV_F1A_GNR_Bx, 0xC001102E, 0x0000040000020000, 0x0000040000020000),

// DeTokenCfg (0xC00110E1)
// bit[62:56] Reserved = 0x36 for STX Ax, GNR Ax
// bit[7:0]   Reserved = 0xBF for GNR, STX
  MAKE_MSR_CPU_REV_ENTRY (AMD_FAMILY_GNR, AMD_REV_F1A_GNR_ALL, 0xC00110E1, 0x00000000000000BF, 0x00000000000000FF),
  MAKE_MSR_CPU_REV_ENTRY (AMD_FAMILY_GNR, AMD_REV_F1A_GNR_Ax,  0xC00110E1, 0x3600000000000000, 0x7F00000000000000),
  MAKE_MSR_CPU_REV_ENTRY (AMD_FAMILY_STX, AMD_REV_F1A_STX_Ax,  0xC00110E1, 0x36000000000000BF, 0x7F000000000000FF),
  MAKE_MSR_CPU_REV_ENTRY (AMD_FAMILY_STX, AMD_REV_F1A_STX_Bx,  0xC00110E1, 0x00000000000000BF, 0x00000000000000FF),

// ChL2Cfg1 (0xC00110E2)
// bit[49]    Reserved = 1
// bit[46:45] Reserved = 1
// bit[44]    Reserved = 1
// bit[4]     Reserved = 1
// bit[3]     Reserved = 1
  MAKE_MSR_ENTRY (0xC00110E2, 0x0002300000000018, 0x0002700000000018),

// DE_CFG2 (0xC00110E3)
// bit[57:56] Reserved = 1 for STX, STXH, GNR, KRK
// bit[51:49] Reserved = 5 for STX, STXH, GNR, KRK
// bit[48]    Reserved = 1 for STX, STXH, GNR, KRK
// bit[47:46] Reserved = 2 for STX, STXH, GNR, KRK
// bit[45:43] Reserved = 5 for STX, STXH, GNR, KRK
// bit[42]    Reserved = 1 for STX, STXH, GNR
// bit[41:40] Reserved = 2 for STX, STXH, GNR
  MAKE_MSR_CPU_REV_ENTRY (AMD_FAMILY_STX,  AMD_REV_F1A_STX_ALL,  0xC00110E3, 0x010BAE0000000000, 0x030FFF0000000000),
  MAKE_MSR_CPU_REV_ENTRY (AMD_FAMILY_STXH, AMD_REV_F1A_STXH_ALL, 0xC00110E3, 0x010BAE0000000000, 0x030FFF0000000000),
  MAKE_MSR_CPU_REV_ENTRY (AMD_FAMILY_GNR,  AMD_REV_F1A_GNR_ALL,  0xC00110E3, 0x010BAE0000000000, 0x030FFF0000000000),
  MAKE_MSR_CPU_REV_ENTRY (AMD_FAMILY_KRK,  AMD_REV_F1A_KRK1_KRK2_ALL,  0xC00110E3, 0x010BAE0000000000, 0x030FFF0000000000),

// Reserved (0xC00110E4)
// bit[61]    Reserved = 1 for STX Ax, GNR Ax
// bit[60]    Reserved = 1 for STX Ax, GNR Ax
  MAKE_MSR_CPU_REV_ENTRY (AMD_FAMILY_STX,  AMD_REV_F1A_STX_Ax,   0xC00110E4, 0x3000000000000000, 0x3000000000000000),
  MAKE_MSR_CPU_REV_ENTRY (AMD_FAMILY_GNR,  AMD_REV_F1A_GNR_Ax,   0xC00110E4, 0x3000000000000000, 0x3000000000000000),

// LS_CFG3 (0xC00110E5)
// bit[44:43] DeadLockCfg = 3 for STX, STXH, GNR, STXH
// bit[30]    Reserved = 1 for STX, GNR Ax, STXH
// bit[26]    EnSpecStFill = 1, STXH
// bit[25]    DisSwPfStFill = 1 for STXH, KRK, STX Bx, STXH
  MAKE_MSR_ENTRY (0xC00110E5, 0x0000000004000000, 0x0000000004000000),
  MAKE_MSR_CPU_REV_ENTRY (AMD_FAMILY_GNR,  AMD_REV_F1A_GNR_ALL, 0xC00110E5, 0x0000180000000000, 0x0000180000000000),
  MAKE_MSR_CPU_REV_ENTRY (AMD_FAMILY_GNR,  AMD_REV_F1A_GNR_Ax,  0xC00110E5, 0x0000000040000000, 0x0000000040000000),
  MAKE_MSR_CPU_REV_ENTRY (AMD_FAMILY_STX,  AMD_REV_F1A_STX_ALL, 0xC00110E5, 0x0000180044000000, 0x0000180044000000),
  MAKE_MSR_CPU_REV_ENTRY (AMD_FAMILY_STX,  AMD_REV_F1A_STX_Bx,  0xC00110E5, 0x0000000002000000, 0x0000000002000000),
  MAKE_MSR_CPU_REV_ENTRY (AMD_FAMILY_KRK,  AMD_REV_F1A_KRK1_KRK2_ALL, 0xC00110E5, 0x0000180002000000, 0x0000180002000000),
  MAKE_MSR_CPU_REV_ENTRY (AMD_FAMILY_STXH, AMD_REV_F1A_STXH_ALL,0xC00110E5, 0x0000180046000000, 0x0000180046000000),

// LS_CFG4 (0xC00110E6)
// bit[24]   Reserved = 1 for STX Bx, GNR Bx
// bit[6]    Reserved = 1 for BRH, BRHD, STX, STXH, GNR, KRK
  MAKE_MSR_CPU_REV_ENTRY (AMD_FAMILY_BRH, AMD_REV_F1A_BRH_ALL,  0xC00110E6, 0x0000000000000040, 0x0000000000000040),
  MAKE_MSR_CPU_REV_ENTRY (AMD_FAMILY_BRH, AMD_REV_F1A_BRHD_ALL, 0xC00110E6, 0x0000000000000040, 0x0000000000000040),
  MAKE_MSR_CPU_REV_ENTRY (AMD_FAMILY_STX,  AMD_REV_F1A_STX_ALL, 0xC00110E6, 0x0000000000000040, 0x0000000000000040),
  MAKE_MSR_CPU_REV_ENTRY (AMD_FAMILY_STX,  AMD_REV_F1A_STX_Bx,  0xC00110E6, 0x0000000001000000, 0x0000000001000000),
  MAKE_MSR_CPU_REV_ENTRY (AMD_FAMILY_STXH, AMD_REV_F1A_STXH_ALL,0xC00110E6, 0x0000000000000040, 0x0000000000000040),
  MAKE_MSR_CPU_REV_ENTRY (AMD_FAMILY_GNR,  AMD_REV_F1A_GNR_ALL, 0xC00110E6, 0x0000000000000040, 0x0000000000000040),
  MAKE_MSR_CPU_REV_ENTRY (AMD_FAMILY_GNR,  AMD_REV_F1A_GNR_Bx,  0xC00110E6, 0x0000000001000000, 0x0000000001000000),
  MAKE_MSR_CPU_REV_ENTRY (AMD_FAMILY_KRK,  AMD_REV_F1A_KRK1_KRK2_ALL,  0xC00110E6, 0x0000000000000040, 0x0000000000000040),

// DC_PF_CFG (0xC00110E7)
// bit[55:52] Reserved = 1 for STX Bx, GNR Bx
// bit[47]    Reserved = 1 for STX Ax, STXH, GNR Ax, KRK
// bit[46]    Reserved = 1 for STX Ax, GNR Ax
// bit[34]    Reserved = 1 for STX, STXH, GNR, KRK
// bit[32]    Reserved = 1 for STX Ax, GNR Ax
// bit[31:30] Reserved = 3 for STX Ax, GNR Ax
// bit[32:30] Reserved = 1 for BRH Ax/Bx
// bit[25]    Reserved = 1 for STX, GNR Ax
// bit[24]    Reserved = 1 for STX, STXH, GNR, KRK
// bit[12]    Reserved = 1 for GNR, STX, STXH, KRK
// bit[6]     Reserved = 1 for GNR, STX, STXH, KRK
// bit[4]     Reserved = 1 for GNR, STX, STXH, KRK
  MAKE_MSR_CPU_REV_ENTRY (AMD_FAMILY_GNR, AMD_REV_F1A_GNR_Ax,   0xC00110E7, 0x0000C005C1000050, 0x0000C005C0001050),
  MAKE_MSR_CPU_REV_ENTRY (AMD_FAMILY_GNR, AMD_REV_F1A_GNR_Bx,   0xC00110E7, 0x0010000401001050, 0x0030000401001050),
  MAKE_MSR_CPU_REV_ENTRY (AMD_FAMILY_BRH, AMD_REV_F1A_BRH_Ax,   0xC00110E7, 0x00000001C0000000, 0x00000001C0000000),
  MAKE_MSR_CPU_REV_ENTRY (AMD_FAMILY_BRH, AMD_REV_F1A_BRH_Bx,   0xC00110E7, 0x00000001C0000000, 0x00000001C0000000),
  MAKE_MSR_CPU_REV_ENTRY (AMD_FAMILY_STX,  AMD_REV_F1A_STX_Ax,  0xC00110E7, 0x0000C005C3001050, 0x0000C005C3001050),
  MAKE_MSR_CPU_REV_ENTRY (AMD_FAMILY_STX,  AMD_REV_F1A_STX_Bx,  0xC00110E7, 0x0010000403001050, 0x00F0000403001050),
  MAKE_MSR_CPU_REV_ENTRY (AMD_FAMILY_STXH,AMD_REV_F1A_STXH_ALL, 0xC00110E7, 0x0000800401001050, 0x0000800401001050),
  MAKE_MSR_CPU_REV_ENTRY (AMD_FAMILY_KRK, AMD_REV_F1A_KRK1_KRK2_ALL,  0xC00110E7, 0x0000800401001050, 0x0000800401001050),

// Reserved (0xC00110E9)
// bit[7:4]   Reserved = 0 for STX, STXH, KRK, BRH Bx, BRH Cx, BRHD, GNR
// bit[3:0]   Reserved = 0 for STX, STXH, KRK, BRH Bx, BRH Cx, BRHD, GNR
  MAKE_MSR_CPU_REV_ENTRY (AMD_FAMILY_STX,  AMD_REV_F1A_STX_ALL,  0xC00110E9, 0x0000000000000000, 0x00000000000000FF),
  MAKE_MSR_CPU_REV_ENTRY (AMD_FAMILY_STXH, AMD_REV_F1A_STXH_ALL, 0xC00110E9, 0x0000000000000000, 0x00000000000000FF),
  MAKE_MSR_CPU_REV_ENTRY (AMD_FAMILY_KRK,  AMD_REV_F1A_KRK1_KRK2_ALL,  0xC00110E9, 0x0000000000000000, 0x00000000000000FF),
  MAKE_MSR_CPU_REV_ENTRY (AMD_FAMILY_BRH,  AMD_REV_F1A_BRH_Bx,   0xC00110E9, 0x0000000000000000, 0x00000000000000FF),
  MAKE_MSR_CPU_REV_ENTRY (AMD_FAMILY_BRH,  AMD_REV_F1A_BRH_Cx,   0xC00110E9, 0x0000000000000000, 0x00000000000000FF),
  MAKE_MSR_CPU_REV_ENTRY (AMD_FAMILY_BRHD, AMD_REV_F1A_BRHD_ALL, 0xC00110E9, 0x0000000000000000, 0x00000000000000FF),
  MAKE_MSR_CPU_REV_ENTRY (AMD_FAMILY_GNR,  AMD_REV_F1A_GNR_ALL,  0xC00110E9, 0x0000000000000000, 0x00000000000000FF),

// Reserved (0xC00110EA)
// bit[7:4]   Reserved = 3 for STX, STXH, KRK, BRH Bx, BRH Cx, BRHD, GNR
// bit[3:0]   Reserved = 2 for STX, STXH, KRK, BRH Bx, BRH Cx, BRHD, GNR
  MAKE_MSR_CPU_REV_ENTRY (AMD_FAMILY_STX,  AMD_REV_F1A_STX_ALL,  0xC00110EA, 0x0000000000000032, 0x00000000000000FF),
  MAKE_MSR_CPU_REV_ENTRY (AMD_FAMILY_STXH, AMD_REV_F1A_STXH_ALL, 0xC00110EA, 0x0000000000000032, 0x00000000000000FF),
  MAKE_MSR_CPU_REV_ENTRY (AMD_FAMILY_KRK,  AMD_REV_F1A_KRK1_KRK2_ALL,  0xC00110EA, 0x0000000000000032, 0x00000000000000FF),
  MAKE_MSR_CPU_REV_ENTRY (AMD_FAMILY_BRH,  AMD_REV_F1A_BRH_Bx,   0xC00110EA, 0x0000000000000032, 0x00000000000000FF),
  MAKE_MSR_CPU_REV_ENTRY (AMD_FAMILY_BRH,  AMD_REV_F1A_BRH_Cx,   0xC00110EA, 0x0000000000000032, 0x00000000000000FF),
  MAKE_MSR_CPU_REV_ENTRY (AMD_FAMILY_BRHD, AMD_REV_F1A_BRHD_ALL, 0xC00110EA, 0x0000000000000032, 0x00000000000000FF),
  MAKE_MSR_CPU_REV_ENTRY (AMD_FAMILY_GNR,  AMD_REV_F1A_GNR_ALL,  0xC00110EA, 0x0000000000000032, 0x00000000000000FF),

// Reserved (0xC00110EB)
// bit[63:48] Reserved = 0x80 for STX Bx, GNR Bx, STXH
// bit[22]    Reserved = 1 for STX Ax, GNR Ax
// bit[20]    Reserved = 1 for STX Ax, GNR Ax
// bit[18]    Reserved = 1 for BRH A0, B0
// bit[3]     Reserved = 1 for STX Ax, GNR Ax, KRK
// bit[2]     Reserved = 1 for STX, GNR, STXH
  MAKE_MSR_CPU_REV_ENTRY (AMD_FAMILY_STX,  AMD_REV_F1A_STX_Ax,  0xC00110EB, 0x000000000050000C, 0x000000000050000C),
  MAKE_MSR_CPU_REV_ENTRY (AMD_FAMILY_STX,  AMD_REV_F1A_STX_Bx,  0xC00110EB, 0x0080000000000004, 0xFFFF000000000004),
  MAKE_MSR_CPU_REV_ENTRY (AMD_FAMILY_BRH,  AMD_REV_F1A_BRH_A0,  0xC00110EB, 0x0000000000040000, 0x0000000000040000),
  MAKE_MSR_CPU_REV_ENTRY (AMD_FAMILY_BRH,  AMD_REV_F1A_BRH_B0,  0xC00110EB, 0x0000000000040000, 0x0000000000040000),
  MAKE_MSR_CPU_REV_ENTRY (AMD_FAMILY_GNR,  AMD_REV_F1A_GNR_Ax,  0xC00110EB, 0x000000000050000C, 0x000000000050000C),
  MAKE_MSR_CPU_REV_ENTRY (AMD_FAMILY_GNR,  AMD_REV_F1A_GNR_Bx,  0xC00110EB, 0x0080000000000004, 0xFFFF000000000004),
  MAKE_MSR_CPU_REV_ENTRY (AMD_FAMILY_STXH,  AMD_REV_F1A_STXH_ALL,  0xC00110EB, 0x0080000000000004, 0xFFFF000000000004),
  MAKE_MSR_CPU_REV_ENTRY (AMD_FAMILY_KRK,  AMD_REV_F1A_KRK1_KRK2_ALL,  0xC00110EB, 0x0000000000000008, 0x0000000000000008),

// Reserved (0xC00110EC)
// bit[9:5] Reserved = 0x1F for STX, GNR Bx, BRH Bx, BRH Cx, BRHD, STXH, KRK
  MAKE_MSR_CPU_REV_ENTRY (AMD_FAMILY_STX,  AMD_REV_F1A_STX_ALL, 0xC00110EC, 0x00000000000003E0, 0x00000000000003E0),
  MAKE_MSR_CPU_REV_ENTRY (AMD_FAMILY_GNR,  AMD_REV_F1A_GNR_Bx,  0xC00110EC, 0x00000000000003E0, 0x00000000000003E0),
  MAKE_MSR_CPU_REV_ENTRY (AMD_FAMILY_BRH,  AMD_REV_F1A_BRH_Cx,  0xC00110EC, 0x00000000000003E0, 0x00000000000003E0),
  MAKE_MSR_CPU_REV_ENTRY (AMD_FAMILY_BRHD, AMD_REV_F1A_BRHD_ALL,0xC00110EC, 0x00000000000003E0, 0x00000000000003E0),
  MAKE_MSR_CPU_REV_ENTRY (AMD_FAMILY_STXH, AMD_REV_F1A_STXH_ALL, 0xC00110EC, 0x00000000000003E0, 0x00000000000003E0),
  MAKE_MSR_CPU_REV_ENTRY (AMD_FAMILY_KRK,  AMD_REV_F1A_KRK1_KRK2_ALL, 0xC00110EC, 0x00000000000003E0, 0x00000000000003E0),

// DE_CFG3 (0xC00110ED)
// bit[63:33] Rsvd63to33 = 1 for STX, STXH, GNR, KRK
// bit[13]    Reserved = 1 for STX Ax, GNR Ax
// bit[5]     RetIohcEnable = 0 for STX, STXH, GNR, KRK
  MAKE_MSR_CPU_REV_ENTRY (AMD_FAMILY_STX,  AMD_REV_F1A_STX_ALL, 0xC00110ED, 0x0000000200000000, 0xFFFFFFFE00000020),
  MAKE_MSR_CPU_REV_ENTRY (AMD_FAMILY_STX,  AMD_REV_F1A_STX_Ax,  0xC00110ED, 0x0000000000002000, 0x0000000000002000),
  MAKE_MSR_CPU_REV_ENTRY (AMD_FAMILY_STXH, AMD_REV_F1A_STXH_ALL,0xC00110ED, 0x0000000200000000, 0xFFFFFFFE00000020),
  MAKE_MSR_CPU_REV_ENTRY (AMD_FAMILY_GNR,  AMD_REV_F1A_GNR_ALL, 0xC00110ED, 0x0000000200000000, 0xFFFFFFFE00000020),
  MAKE_MSR_CPU_REV_ENTRY (AMD_FAMILY_GNR,  AMD_REV_F1A_GNR_Ax,  0xC00110ED, 0x0000000000002000, 0x0000000000002000),
  MAKE_MSR_CPU_REV_ENTRY (AMD_FAMILY_KRK,  AMD_REV_F1A_KRK1_KRK2_ALL, 0xC00110ED, 0x0000000200000000, 0xFFFFFFFE00000020),

// Table Terminator
  MAKE_TABLE_TERMINATOR
};

CONST UINT8 ROMDATA CcxZen5ComplexRegistersAfterApLaunch[] = {

// MCA_CONFIG_L3 (0xC00020[7~E]4)
// bit[40]    IntEn = 0 for STXH
// bit[39]    McaFatalMask = 0 for STXH
// bit[34]    LogDeferredInMcaStat = 1 for STXH
// bit[33]    TransparentErrorLoggingEnable = 0 for STXH
// bit[32]    McaXEnable = 1 for STXH
// bit[9]     McaFruTextInMca = 0 for STXH
  MAKE_MSR_CPU_REV_ENTRY (AMD_FAMILY_STXH, AMD_REV_F1A_STXH_ALL, 0xC0002074, 0x0000000500000000, 0x0000018700000200),
  MAKE_MSR_CPU_REV_ENTRY (AMD_FAMILY_STXH, AMD_REV_F1A_STXH_ALL, 0xC0002084, 0x0000000500000000, 0x0000018700000200),
  MAKE_MSR_CPU_REV_ENTRY (AMD_FAMILY_STXH, AMD_REV_F1A_STXH_ALL, 0xC0002094, 0x0000000500000000, 0x0000018700000200),
  MAKE_MSR_CPU_REV_ENTRY (AMD_FAMILY_STXH, AMD_REV_F1A_STXH_ALL, 0xC00020A4, 0x0000000500000000, 0x0000018700000200),
  MAKE_MSR_CPU_REV_ENTRY (AMD_FAMILY_STXH, AMD_REV_F1A_STXH_ALL, 0xC00020B4, 0x0000000500000000, 0x0000018700000200),
  MAKE_MSR_CPU_REV_ENTRY (AMD_FAMILY_STXH, AMD_REV_F1A_STXH_ALL, 0xC00020C4, 0x0000000500000000, 0x0000018700000200),
  MAKE_MSR_CPU_REV_ENTRY (AMD_FAMILY_STXH, AMD_REV_F1A_STXH_ALL, 0xC00020D4, 0x0000000500000000, 0x0000018700000200),
  MAKE_MSR_CPU_REV_ENTRY (AMD_FAMILY_STXH, AMD_REV_F1A_STXH_ALL, 0xC00020E4, 0x0000000500000000, 0x0000018700000200),

// MCA_CONFIG_MP5 (0xC00020F4)
// bit[40]    IntEn = 0 for STXH
// bit[39]    McaFatalMask = 0 for STXH
// bit[33]    TransparentErrorLoggingEnable = 0 for STXH
// bit[32]    McaXEnable = 1 for STXH
// bit[9]     McaFruTextInMca = 0 for STXH
  MAKE_MSR_CPU_REV_ENTRY (AMD_FAMILY_STXH, AMD_REV_F1A_STXH_ALL, 0xC00020F4, 0x0000000100000000, 0x0000018300000200),

// MCA_CONFIG_UMC (0xC00021[0~3]4)
// bit[40]    IntEn = 0 for STXH
// bit[39]    McaFatalMask = 0 for STXH
// bit[34]    LogDeferredInMcaStat = 1 for STXH
// bit[33]    TransparentErrorLoggingEnable = 0 for STXH
// bit[32]    McaXEnable = 1 for STXH
// bit[9]     McaFruTextInMca = 0 for STXH
  MAKE_MSR_CPU_REV_ENTRY (AMD_FAMILY_STXH, AMD_REV_F1A_STXH_ALL, 0xC0002104, 0x0000000500000000, 0x0000018700000200),
  MAKE_MSR_CPU_REV_ENTRY (AMD_FAMILY_STXH, AMD_REV_F1A_STXH_ALL, 0xC0002114, 0x0000000500000000, 0x0000018700000200),
  MAKE_MSR_CPU_REV_ENTRY (AMD_FAMILY_STXH, AMD_REV_F1A_STXH_ALL, 0xC0002124, 0x0000000500000000, 0x0000018700000200),
  MAKE_MSR_CPU_REV_ENTRY (AMD_FAMILY_STXH, AMD_REV_F1A_STXH_ALL, 0xC0002134, 0x0000000500000000, 0x0000018700000200),

// MCA_CONFIG_CS (0xC00021[4~7]4)
// bit[40]    IntEn = 0 for STXH
// bit[39]    McaFatalMask = 0 for STXH
// bit[34]    LogDeferredInMcaStat = 1 for STXH
// bit[33]    TransparentErrorLoggingEnable = 0 for STXH
// bit[32]    McaXEnable = 1 for STXH
// bit[9]     McaFruTextInMca = 0 for STXH
  MAKE_MSR_CPU_REV_ENTRY (AMD_FAMILY_STXH, AMD_REV_F1A_STXH_ALL, 0xC0002144, 0x0000000500000000, 0x0000018700000200),
  MAKE_MSR_CPU_REV_ENTRY (AMD_FAMILY_STXH, AMD_REV_F1A_STXH_ALL, 0xC0002154, 0x0000000500000000, 0x0000018700000200),
  MAKE_MSR_CPU_REV_ENTRY (AMD_FAMILY_STXH, AMD_REV_F1A_STXH_ALL, 0xC0002164, 0x0000000500000000, 0x0000018700000200),
  MAKE_MSR_CPU_REV_ENTRY (AMD_FAMILY_STXH, AMD_REV_F1A_STXH_ALL, 0xC0002174, 0x0000000500000000, 0x0000018700000200),

// MCA_CONFIG_MALL (0xC00021[8~B]4)
// bit[40]    IntEn = 0 for STXH
// bit[39]    McaFatalMask = 0 for STXH
// bit[34]    LogDeferredInMcaStat = 1 for STXH
// bit[33]    TransparentErrorLoggingEnable = 0 for STXH
// bit[32]    McaXEnable = 1 for STXH
  MAKE_MSR_CPU_REV_ENTRY (AMD_FAMILY_STXH, AMD_REV_F1A_STXH_ALL, 0xC0002184, 0x0000000500000000, 0x0000018700000200),
  MAKE_MSR_CPU_REV_ENTRY (AMD_FAMILY_STXH, AMD_REV_F1A_STXH_ALL, 0xC0002194, 0x0000000500000000, 0x0000018700000200),
  MAKE_MSR_CPU_REV_ENTRY (AMD_FAMILY_STXH, AMD_REV_F1A_STXH_ALL, 0xC00021A4, 0x0000000500000000, 0x0000018700000200),
  MAKE_MSR_CPU_REV_ENTRY (AMD_FAMILY_STXH, AMD_REV_F1A_STXH_ALL, 0xC00021B4, 0x0000000500000000, 0x0000018700000200),

// MCA_CONFIG_NBIO (0xC00021D4)
// bit[40]    IntEn = 0 for STXH
// bit[39]    McaFatalMask = 0 for STXH
// bit[34]    LogDeferredInMcaStat = 1 for STXH
// bit[32]    McaXEnable = 1 for STXH
// bit[9]     McaFruTextInMca = 0 for STXH
  MAKE_MSR_CPU_REV_ENTRY (AMD_FAMILY_STXH, AMD_REV_F1A_STXH_ALL, 0xC00021D4, 0x0000000500000000, 0x0000018500000200),

// MCA_CONFIG_KPX_SERDES (0xC00021[E~F]4)
// bit[40]    IntEn = 0 for STXH
// bit[39]    McaFatalMask = 0 for STXH
// bit[34]    LogDeferredInMcaStat = 1 for STXH
// bit[32]    McaXEnable = 1 for STXH
// bit[9]     McaFruTextInMca = 0 for STXH
  MAKE_MSR_CPU_REV_ENTRY (AMD_FAMILY_STXH, AMD_REV_F1A_STXH_ALL, 0xC00021E4, 0x0000000500000000, 0x0000018500000200),
  MAKE_MSR_CPU_REV_ENTRY (AMD_FAMILY_STXH, AMD_REV_F1A_STXH_ALL, 0xC00021F4, 0x0000000500000000, 0x0000018500000200),

// MCA_CTL_MASK_L3_n0~15 (0xC0010407~E)
// bit [7]     Hwa = 1 for STX, GNR, STXH, KRK
  MAKE_MSR_CPU_REV_ENTRY (AMD_FAMILY_STXH, AMD_REV_F1A_STXH_ALL, 0xC0010407, 0x0000000000000080, 0x0000000000000080),
  MAKE_MSR_CPU_REV_ENTRY (AMD_FAMILY_STXH, AMD_REV_F1A_STXH_ALL, 0xC0010408, 0x0000000000000080, 0x0000000000000080),
  MAKE_MSR_CPU_REV_ENTRY (AMD_FAMILY_STXH, AMD_REV_F1A_STXH_ALL, 0xC0010409, 0x0000000000000080, 0x0000000000000080),
  MAKE_MSR_CPU_REV_ENTRY (AMD_FAMILY_STXH, AMD_REV_F1A_STXH_ALL, 0xC001040A, 0x0000000000000080, 0x0000000000000080),
  MAKE_MSR_CPU_REV_ENTRY (AMD_FAMILY_STXH, AMD_REV_F1A_STXH_ALL, 0xC001040B, 0x0000000000000080, 0x0000000000000080),
  MAKE_MSR_CPU_REV_ENTRY (AMD_FAMILY_STXH, AMD_REV_F1A_STXH_ALL, 0xC001040C, 0x0000000000000080, 0x0000000000000080),
  MAKE_MSR_CPU_REV_ENTRY (AMD_FAMILY_STXH, AMD_REV_F1A_STXH_ALL, 0xC001040D, 0x0000000000000080, 0x0000000000000080),
  MAKE_MSR_CPU_REV_ENTRY (AMD_FAMILY_STXH, AMD_REV_F1A_STXH_ALL, 0xC001040E, 0x0000000000000080, 0x0000000000000080),

  MAKE_MSR_CPU_REV_ENTRY (AMD_FAMILY_STX, AMD_REV_F1A_STX_ALL, 0xC0010407, 0x0000000000000080, 0x0000000000000080),
  MAKE_MSR_CPU_REV_ENTRY (AMD_FAMILY_STX, AMD_REV_F1A_STX_ALL, 0xC0010408, 0x0000000000000080, 0x0000000000000080),
  MAKE_MSR_CPU_REV_ENTRY (AMD_FAMILY_STX, AMD_REV_F1A_STX_ALL, 0xC0010409, 0x0000000000000080, 0x0000000000000080),
  MAKE_MSR_CPU_REV_ENTRY (AMD_FAMILY_STX, AMD_REV_F1A_STX_ALL, 0xC001040A, 0x0000000000000080, 0x0000000000000080),
  MAKE_MSR_CPU_REV_ENTRY (AMD_FAMILY_STX, AMD_REV_F1A_STX_ALL, 0xC001040B, 0x0000000000000080, 0x0000000000000080),
  MAKE_MSR_CPU_REV_ENTRY (AMD_FAMILY_STX, AMD_REV_F1A_STX_ALL, 0xC001040C, 0x0000000000000080, 0x0000000000000080),
  MAKE_MSR_CPU_REV_ENTRY (AMD_FAMILY_STX, AMD_REV_F1A_STX_ALL, 0xC001040D, 0x0000000000000080, 0x0000000000000080),
  MAKE_MSR_CPU_REV_ENTRY (AMD_FAMILY_STX, AMD_REV_F1A_STX_ALL, 0xC001040E, 0x0000000000000080, 0x0000000000000080),

  MAKE_MSR_CPU_REV_ENTRY (AMD_FAMILY_GNR, AMD_REV_F1A_GNR_ALL, 0xC0010407, 0x0000000000000080, 0x0000000000000080),
  MAKE_MSR_CPU_REV_ENTRY (AMD_FAMILY_GNR, AMD_REV_F1A_GNR_ALL, 0xC0010408, 0x0000000000000080, 0x0000000000000080),
  MAKE_MSR_CPU_REV_ENTRY (AMD_FAMILY_GNR, AMD_REV_F1A_GNR_ALL, 0xC0010409, 0x0000000000000080, 0x0000000000000080),
  MAKE_MSR_CPU_REV_ENTRY (AMD_FAMILY_GNR, AMD_REV_F1A_GNR_ALL, 0xC001040A, 0x0000000000000080, 0x0000000000000080),
  MAKE_MSR_CPU_REV_ENTRY (AMD_FAMILY_GNR, AMD_REV_F1A_GNR_ALL, 0xC001040B, 0x0000000000000080, 0x0000000000000080),
  MAKE_MSR_CPU_REV_ENTRY (AMD_FAMILY_GNR, AMD_REV_F1A_GNR_ALL, 0xC001040C, 0x0000000000000080, 0x0000000000000080),
  MAKE_MSR_CPU_REV_ENTRY (AMD_FAMILY_GNR, AMD_REV_F1A_GNR_ALL, 0xC001040D, 0x0000000000000080, 0x0000000000000080),
  MAKE_MSR_CPU_REV_ENTRY (AMD_FAMILY_GNR, AMD_REV_F1A_GNR_ALL, 0xC001040E, 0x0000000000000080, 0x0000000000000080),

  MAKE_MSR_CPU_REV_ENTRY (AMD_FAMILY_KRK, AMD_REV_F1A_KRK1_KRK2_ALL, 0xC0010407, 0x0000000000000080, 0x0000000000000080),
  MAKE_MSR_CPU_REV_ENTRY (AMD_FAMILY_KRK, AMD_REV_F1A_KRK1_KRK2_ALL, 0xC0010408, 0x0000000000000080, 0x0000000000000080),
  MAKE_MSR_CPU_REV_ENTRY (AMD_FAMILY_KRK, AMD_REV_F1A_KRK1_KRK2_ALL, 0xC0010409, 0x0000000000000080, 0x0000000000000080),
  MAKE_MSR_CPU_REV_ENTRY (AMD_FAMILY_KRK, AMD_REV_F1A_KRK1_KRK2_ALL, 0xC001040A, 0x0000000000000080, 0x0000000000000080),
  MAKE_MSR_CPU_REV_ENTRY (AMD_FAMILY_KRK, AMD_REV_F1A_KRK1_KRK2_ALL, 0xC001040B, 0x0000000000000080, 0x0000000000000080),
  MAKE_MSR_CPU_REV_ENTRY (AMD_FAMILY_KRK, AMD_REV_F1A_KRK1_KRK2_ALL, 0xC001040C, 0x0000000000000080, 0x0000000000000080),
  MAKE_MSR_CPU_REV_ENTRY (AMD_FAMILY_KRK, AMD_REV_F1A_KRK1_KRK2_ALL, 0xC001040D, 0x0000000000000080, 0x0000000000000080),
  MAKE_MSR_CPU_REV_ENTRY (AMD_FAMILY_KRK, AMD_REV_F1A_KRK1_KRK2_ALL, 0xC001040E, 0x0000000000000080, 0x0000000000000080),

// Table Terminator
  MAKE_TABLE_TERMINATOR
};

CONST REGISTER_TABLE ROMDATA CcxZen5ComplexRegTableAfterApLaunch = {
  ComplexPrimary,
  CcxZen5ComplexRegistersAfterApLaunch,
};

CONST REGISTER_TABLE ROMDATA CcxZen5CURegTableAfterApLaunch = {
  ComputeUnitPrimary,
  CcxZen5ComputeUnitRegistersAfterApLaunch,
};

CONST REGISTER_TABLE ROMDATA CcxZen5AllCoreRegTableAfterApLaunch = {
  AllCores,
  CcxZen5AllCoreRegistersAfterApLaunch,
};

CONST REGISTER_TABLE ROMDATA *CcxZen5RegisterTablesAfterApLaunch[] = {
  &CcxZen5AllCoreRegTableAfterApLaunch,
  &CcxZen5CURegTableAfterApLaunch,
  &CcxZen5ComplexRegTableAfterApLaunch,
  NULL
};

CONST REGISTER_TABLE ROMDATA *CcxZen5RegisterTablesAfterApLaunchSecureS3[] = {
  NULL
};

CONST REGISTER_TABLE_AT_GIVEN_TP ROMDATA CcxZen5RegTableListAtGivenTP[] = {
  {AmdRegisterTableTpAfterApLaunch, CcxZen5RegisterTablesAfterApLaunch},
  {AmdRegisterTableTpAfterApLaunchSecureS3, CcxZen5RegisterTablesAfterApLaunchSecureS3},
  {MaxAmdRegisterTableTps, NULL}
};


/*++

Routine Description:

  Zen5 Register table programming

Arguments:
  SleepType
  AMD_CONFIG_PARAMS *

Returns:

--*/
VOID
CcxProgramTablesAtReset (
  IN       UINT8               SleepType,
  IN       ENTRY_CRITERIA     *InitializedCriteria,
  IN OUT   AMD_CONFIG_PARAMS  *StdHeader
  )
{
  REGISTER_TABLE_TIME_POINT  Timepoint;

  Timepoint = (SleepType == 0x03) ? AmdRegisterTableTpAfterApLaunchSecureS3 : AmdRegisterTableTpAfterApLaunch;
  SetRegistersFromTablesAtGivenTimePoint (StdHeader, (REGISTER_TABLE_AT_GIVEN_TP *) &CcxZen5RegTableListAtGivenTP[0], Timepoint, InitializedCriteria);
}

