#;*****************************************************************************
#;
#; Copyright (C) 2008-2024 Advanced Micro Devices, Inc. All rights reserved.
#;
#;******************************************************************************
#For EDKII use Only
[Defines]
  INF_VERSION                    = 0x00010005
  BASE_NAME                      = BaseFabricTopologyBrhLib
  FILE_GUID                      = 92CA2E3D-A8C5-47E0-879D-9D56BDBD8B13
  MODULE_TYPE                    = BASE
  VERSION_STRING                 = 1.0
  LIBRARY_CLASS                  = BaseFabricTopologyLib
  CONSTRUCTOR                    = BaseFabricTopologyLibConstructor

[Sources]
  BaseFabricTopologyBrhLib.c

[Packages]
  MdePkg/MdePkg.dec
  AgesaPkg/AgesaPkg.dec
  AgesaModulePkg/AgesaCommonModulePkg.dec
  AgesaModulePkg/AgesaModuleDfPkg.dec

[LibraryClasses]
  BaseLib
  BaseMemoryLib
  FabricRegisterAccLib
  IdsLib
  AmdHeapLib
  AmdSocBaseLib

