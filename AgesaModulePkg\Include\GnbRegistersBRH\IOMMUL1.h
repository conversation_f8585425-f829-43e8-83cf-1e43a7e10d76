/*****************************************************************************
 *
 * Copyright (C) 2008-2024 Advanced Micro Devices, Inc. All rights reserved.
 *
 *******************************************************************************
 **/

#ifndef _IOMMUL1_H_
#define _IOMMUL1_H_


/***********************************************************
* Register Name : IOMMU_PGSLV_CONTROL
************************************************************/

#define IOMMU_PGSLV_CONTROL_CFG_IDLE_HYSTERESIS_OFFSET         0
#define IOMMU_PGSLV_CONTROL_CFG_IDLE_HYSTERESIS_MASK           0x1f

#define IOMMU_PGSLV_CONTROL_L1_PG_STATUS_OFFSET                5
#define IOMMU_PGSLV_CONTROL_L1_PG_STATUS_MASK                  0x20

#define IOMMU_PGSLV_CONTROL_Reserved_31_6_OFFSET               6
#define IOMMU_PGSLV_CONTROL_Reserved_31_6_MASK                 0xffffffc0

typedef union {
  struct {
    UINT32                                 CFG_IDLE_HYSTERESIS:5;
    UINT32                                        L1_PG_STATUS:1;
    UINT32                                       Reserved_31_6:26;
  } Field;
  UINT32 Value;
} IOMMU_PGSLV_CONTROL_STRUCT;

#define SMN_IOMMU_PGSLV_CONTROL_ADDRESS                               0x153000f4UL
#define SMN_IOMMU0IOAGRNBIO0_IOMMU_PGSLV_CONTROL_ADDRESS              0x153000f4UL
#define SMN_IOMMU0IOAGRNBIO1_IOMMU_PGSLV_CONTROL_ADDRESS              0x155000f4UL
#define SMN_IOMMU0PCIE0NBIO0_IOMMU_PGSLV_CONTROL_ADDRESS              0x147000f4UL
#define SMN_IOMMU0PCIE0NBIO1_IOMMU_PGSLV_CONTROL_ADDRESS              0x149000f4UL
#define SMN_IOMMU0PCIE1NBIO0_IOMMU_PGSLV_CONTROL_ADDRESS              0x14b000f4UL
#define SMN_IOMMU0PCIE1NBIO1_IOMMU_PGSLV_CONTROL_ADDRESS              0x14d000f4UL
#define SMN_IOMMU1IOAGRNBIO0_IOMMU_PGSLV_CONTROL_ADDRESS              0x154000f4UL
#define SMN_IOMMU1IOAGRNBIO1_IOMMU_PGSLV_CONTROL_ADDRESS              0x156000f4UL
#define SMN_IOMMU1PCIE0NBIO0_IOMMU_PGSLV_CONTROL_ADDRESS              0x148000f4UL
#define SMN_IOMMU1PCIE0NBIO1_IOMMU_PGSLV_CONTROL_ADDRESS              0x14a000f4UL
#define SMN_IOMMU1PCIE1NBIO0_IOMMU_PGSLV_CONTROL_ADDRESS              0x14c000f4UL
#define SMN_IOMMU1PCIE1NBIO1_IOMMU_PGSLV_CONTROL_ADDRESS              0x14e000f4UL


/***********************************************************
* Register Name : L1_ATS_RESP_CTRL_0
************************************************************/

#define L1_ATS_RESP_CTRL_0_L1_ATS_Resp_allow_timer_OFFSET      0
#define L1_ATS_RESP_CTRL_0_L1_ATS_Resp_allow_timer_MASK        0xff

#define L1_ATS_RESP_CTRL_0_L1_ATS_Resp_delay_timer_OFFSET      8
#define L1_ATS_RESP_CTRL_0_L1_ATS_Resp_delay_timer_MASK        0xff00

#define L1_ATS_RESP_CTRL_0_Reserved_30_16_OFFSET               16
#define L1_ATS_RESP_CTRL_0_Reserved_30_16_MASK                 0x7fff0000

#define L1_ATS_RESP_CTRL_0_L1_ATSdely_on_PPRAutoResp_en_OFFSET 31
#define L1_ATS_RESP_CTRL_0_L1_ATSdely_on_PPRAutoResp_en_MASK   0x80000000

typedef union {
  struct {
    UINT32                             L1_ATS_Resp_allow_timer:8;
    UINT32                             L1_ATS_Resp_delay_timer:8;
    UINT32                                      Reserved_30_16:15;
    UINT32                        L1_ATSdely_on_PPRAutoResp_en:1;
  } Field;
  UINT32 Value;
} L1_ATS_RESP_CTRL_0_STRUCT;

#define SMN_L1_ATS_RESP_CTRL_0_ADDRESS                                0x153000f8UL
#define SMN_IOMMU0IOAGRNBIO0_L1_ATS_RESP_CTRL_0_ADDRESS               0x153000f8UL
#define SMN_IOMMU0IOAGRNBIO1_L1_ATS_RESP_CTRL_0_ADDRESS               0x155000f8UL
#define SMN_IOMMU0PCIE0NBIO0_L1_ATS_RESP_CTRL_0_ADDRESS               0x147000f8UL
#define SMN_IOMMU0PCIE0NBIO1_L1_ATS_RESP_CTRL_0_ADDRESS               0x149000f8UL
#define SMN_IOMMU0PCIE1NBIO0_L1_ATS_RESP_CTRL_0_ADDRESS               0x14b000f8UL
#define SMN_IOMMU0PCIE1NBIO1_L1_ATS_RESP_CTRL_0_ADDRESS               0x14d000f8UL
#define SMN_IOMMU1IOAGRNBIO0_L1_ATS_RESP_CTRL_0_ADDRESS               0x154000f8UL
#define SMN_IOMMU1IOAGRNBIO1_L1_ATS_RESP_CTRL_0_ADDRESS               0x156000f8UL
#define SMN_IOMMU1PCIE0NBIO0_L1_ATS_RESP_CTRL_0_ADDRESS               0x148000f8UL
#define SMN_IOMMU1PCIE0NBIO1_L1_ATS_RESP_CTRL_0_ADDRESS               0x14a000f8UL
#define SMN_IOMMU1PCIE1NBIO0_L1_ATS_RESP_CTRL_0_ADDRESS               0x14c000f8UL
#define SMN_IOMMU1PCIE1NBIO1_L1_ATS_RESP_CTRL_0_ADDRESS               0x14e000f8UL


/***********************************************************
* Register Name : L1_AVIC_QUEUE_CTRL
************************************************************/

#define L1_AVIC_QUEUE_CTRL_L1_AVIC_Queue_En_OFFSET             0
#define L1_AVIC_QUEUE_CTRL_L1_AVIC_Queue_En_MASK               0x1

#define L1_AVIC_QUEUE_CTRL_L1_AVIC_L1L2_Hipri_OFFSET           1
#define L1_AVIC_QUEUE_CTRL_L1_AVIC_L1L2_Hipri_MASK             0x2

#define L1_AVIC_QUEUE_CTRL_L1_AVIC_Squash_Flushes_OFFSET       2
#define L1_AVIC_QUEUE_CTRL_L1_AVIC_Squash_Flushes_MASK         0x4

#define L1_AVIC_QUEUE_CTRL_L1_AVIC_Abort_Parity_Err_OFFSET     3
#define L1_AVIC_QUEUE_CTRL_L1_AVIC_Abort_Parity_Err_MASK       0x8

#define L1_AVIC_QUEUE_CTRL_Reserved_31_4_OFFSET                4
#define L1_AVIC_QUEUE_CTRL_Reserved_31_4_MASK                  0xfffffff0

typedef union {
  struct {
    UINT32                                    L1_AVIC_Queue_En:1;
    UINT32                                  L1_AVIC_L1L2_Hipri:1;
    UINT32                              L1_AVIC_Squash_Flushes:1;
    UINT32                            L1_AVIC_Abort_Parity_Err:1;
    UINT32                                       Reserved_31_4:28;
  } Field;
  UINT32 Value;
} L1_AVIC_QUEUE_CTRL_STRUCT;

#define SMN_L1_AVIC_QUEUE_CTRL_ADDRESS                                0x1530027cUL
#define SMN_IOMMU0IOAGRNBIO0_L1_AVIC_QUEUE_CTRL_ADDRESS               0x1530027cUL
#define SMN_IOMMU0IOAGRNBIO1_L1_AVIC_QUEUE_CTRL_ADDRESS               0x1550027cUL
#define SMN_IOMMU0PCIE0NBIO0_L1_AVIC_QUEUE_CTRL_ADDRESS               0x1470027cUL
#define SMN_IOMMU0PCIE0NBIO1_L1_AVIC_QUEUE_CTRL_ADDRESS               0x1490027cUL
#define SMN_IOMMU0PCIE1NBIO0_L1_AVIC_QUEUE_CTRL_ADDRESS               0x14b0027cUL
#define SMN_IOMMU0PCIE1NBIO1_L1_AVIC_QUEUE_CTRL_ADDRESS               0x14d0027cUL
#define SMN_IOMMU1IOAGRNBIO0_L1_AVIC_QUEUE_CTRL_ADDRESS               0x1540027cUL
#define SMN_IOMMU1IOAGRNBIO1_L1_AVIC_QUEUE_CTRL_ADDRESS               0x1560027cUL
#define SMN_IOMMU1PCIE0NBIO0_L1_AVIC_QUEUE_CTRL_ADDRESS               0x1480027cUL
#define SMN_IOMMU1PCIE0NBIO1_L1_AVIC_QUEUE_CTRL_ADDRESS               0x14a0027cUL
#define SMN_IOMMU1PCIE1NBIO0_L1_AVIC_QUEUE_CTRL_ADDRESS               0x14c0027cUL
#define SMN_IOMMU1PCIE1NBIO1_L1_AVIC_QUEUE_CTRL_ADDRESS               0x14e0027cUL


/***********************************************************
* Register Name : L1_BANDWIDTH_DEBUG_TOOLS
************************************************************/

#define L1_BANDWIDTH_DEBUG_TOOLS_REG_num_wq_disable_OFFSET     0
#define L1_BANDWIDTH_DEBUG_TOOLS_REG_num_wq_disable_MASK       0xff

#define L1_BANDWIDTH_DEBUG_TOOLS_REG_num_tlp_disable_OFFSET    8
#define L1_BANDWIDTH_DEBUG_TOOLS_REG_num_tlp_disable_MASK      0x3ff00

#define L1_BANDWIDTH_DEBUG_TOOLS_REG_num_data_disable_OFFSET   18
#define L1_BANDWIDTH_DEBUG_TOOLS_REG_num_data_disable_MASK     0xffc0000

#define L1_BANDWIDTH_DEBUG_TOOLS_Reserved_31_28_OFFSET         28
#define L1_BANDWIDTH_DEBUG_TOOLS_Reserved_31_28_MASK           0xf0000000

typedef union {
  struct {
    UINT32                                  REG_num_wq_disable:8;
    UINT32                                 REG_num_tlp_disable:10;
    UINT32                                REG_num_data_disable:10;
    UINT32                                      Reserved_31_28:4;
  } Field;
  UINT32 Value;
} L1_BANDWIDTH_DEBUG_TOOLS_STRUCT;

#define SMN_L1_BANDWIDTH_DEBUG_TOOLS_ADDRESS                          0x1530018cUL
#define SMN_IOMMU0IOAGRNBIO0_L1_BANDWIDTH_DEBUG_TOOLS_ADDRESS         0x1530018cUL
#define SMN_IOMMU0IOAGRNBIO1_L1_BANDWIDTH_DEBUG_TOOLS_ADDRESS         0x1550018cUL
#define SMN_IOMMU0PCIE0NBIO0_L1_BANDWIDTH_DEBUG_TOOLS_ADDRESS         0x1470018cUL
#define SMN_IOMMU0PCIE0NBIO1_L1_BANDWIDTH_DEBUG_TOOLS_ADDRESS         0x1490018cUL
#define SMN_IOMMU0PCIE1NBIO0_L1_BANDWIDTH_DEBUG_TOOLS_ADDRESS         0x14b0018cUL
#define SMN_IOMMU0PCIE1NBIO1_L1_BANDWIDTH_DEBUG_TOOLS_ADDRESS         0x14d0018cUL
#define SMN_IOMMU1IOAGRNBIO0_L1_BANDWIDTH_DEBUG_TOOLS_ADDRESS         0x1540018cUL
#define SMN_IOMMU1IOAGRNBIO1_L1_BANDWIDTH_DEBUG_TOOLS_ADDRESS         0x1560018cUL
#define SMN_IOMMU1PCIE0NBIO0_L1_BANDWIDTH_DEBUG_TOOLS_ADDRESS         0x1480018cUL
#define SMN_IOMMU1PCIE0NBIO1_L1_BANDWIDTH_DEBUG_TOOLS_ADDRESS         0x14a0018cUL
#define SMN_IOMMU1PCIE1NBIO0_L1_BANDWIDTH_DEBUG_TOOLS_ADDRESS         0x14c0018cUL
#define SMN_IOMMU1PCIE1NBIO1_L1_BANDWIDTH_DEBUG_TOOLS_ADDRESS         0x14e0018cUL


/***********************************************************
* Register Name : L1_BANK_DISABLE_0
************************************************************/

#define L1_BANK_DISABLE_0_L1CacheLineDis_0_OFFSET              0
#define L1_BANK_DISABLE_0_L1CacheLineDis_0_MASK                0x7f

#define L1_BANK_DISABLE_0_Reserved_7_7_OFFSET                  7
#define L1_BANK_DISABLE_0_Reserved_7_7_MASK                    0x80

#define L1_BANK_DISABLE_0_L1CacheLineDis_1_OFFSET              8
#define L1_BANK_DISABLE_0_L1CacheLineDis_1_MASK                0x7f00

#define L1_BANK_DISABLE_0_L1CacheLineDis_qos_OFFSET            15
#define L1_BANK_DISABLE_0_L1CacheLineDis_qos_MASK              0x3f8000

#define L1_BANK_DISABLE_0_Reserved_31_22_OFFSET                22
#define L1_BANK_DISABLE_0_Reserved_31_22_MASK                  0xffc00000

typedef union {
  struct {
    UINT32                                    L1CacheLineDis_0:7;
    UINT32                                        Reserved_7_7:1;
    UINT32                                    L1CacheLineDis_1:7;
    UINT32                                  L1CacheLineDis_qos:7;
    UINT32                                      Reserved_31_22:10;
  } Field;
  UINT32 Value;
} L1_BANK_DISABLE_0_STRUCT;

#define SMN_L1_BANK_DISABLE_0_ADDRESS                                 0x1530004cUL
#define SMN_IOMMU0IOAGRNBIO0_L1_BANK_DISABLE_0_ADDRESS                0x1530004cUL
#define SMN_IOMMU0IOAGRNBIO1_L1_BANK_DISABLE_0_ADDRESS                0x1550004cUL
#define SMN_IOMMU0PCIE0NBIO0_L1_BANK_DISABLE_0_ADDRESS                0x1470004cUL
#define SMN_IOMMU0PCIE0NBIO1_L1_BANK_DISABLE_0_ADDRESS                0x1490004cUL
#define SMN_IOMMU0PCIE1NBIO0_L1_BANK_DISABLE_0_ADDRESS                0x14b0004cUL
#define SMN_IOMMU0PCIE1NBIO1_L1_BANK_DISABLE_0_ADDRESS                0x14d0004cUL
#define SMN_IOMMU1IOAGRNBIO0_L1_BANK_DISABLE_0_ADDRESS                0x1540004cUL
#define SMN_IOMMU1IOAGRNBIO1_L1_BANK_DISABLE_0_ADDRESS                0x1560004cUL
#define SMN_IOMMU1PCIE0NBIO0_L1_BANK_DISABLE_0_ADDRESS                0x1480004cUL
#define SMN_IOMMU1PCIE0NBIO1_L1_BANK_DISABLE_0_ADDRESS                0x14a0004cUL
#define SMN_IOMMU1PCIE1NBIO0_L1_BANK_DISABLE_0_ADDRESS                0x14c0004cUL
#define SMN_IOMMU1PCIE1NBIO1_L1_BANK_DISABLE_0_ADDRESS                0x14e0004cUL


/***********************************************************
* Register Name : L1_BANK_SEL_0
************************************************************/

#define L1_BANK_SEL_0_L1CacheBankSel_0_OFFSET                  0
#define L1_BANK_SEL_0_L1CacheBankSel_0_MASK                    0xffff

#define L1_BANK_SEL_0_Reserved_31_16_OFFSET                    16
#define L1_BANK_SEL_0_Reserved_31_16_MASK                      0xffff0000

typedef union {
  struct {
    UINT32                                    L1CacheBankSel_0:16;
    UINT32                                      Reserved_31_16:16;
  } Field;
  UINT32 Value;
} L1_BANK_SEL_0_STRUCT;

#define SMN_L1_BANK_SEL_0_ADDRESS                                     0x15300040UL
#define SMN_IOMMU0IOAGRNBIO0_L1_BANK_SEL_0_ADDRESS                    0x15300040UL
#define SMN_IOMMU0IOAGRNBIO1_L1_BANK_SEL_0_ADDRESS                    0x15500040UL
#define SMN_IOMMU0PCIE0NBIO0_L1_BANK_SEL_0_ADDRESS                    0x14700040UL
#define SMN_IOMMU0PCIE0NBIO1_L1_BANK_SEL_0_ADDRESS                    0x14900040UL
#define SMN_IOMMU0PCIE1NBIO0_L1_BANK_SEL_0_ADDRESS                    0x14b00040UL
#define SMN_IOMMU0PCIE1NBIO1_L1_BANK_SEL_0_ADDRESS                    0x14d00040UL
#define SMN_IOMMU1IOAGRNBIO0_L1_BANK_SEL_0_ADDRESS                    0x15400040UL
#define SMN_IOMMU1IOAGRNBIO1_L1_BANK_SEL_0_ADDRESS                    0x15600040UL
#define SMN_IOMMU1PCIE0NBIO0_L1_BANK_SEL_0_ADDRESS                    0x14800040UL
#define SMN_IOMMU1PCIE0NBIO1_L1_BANK_SEL_0_ADDRESS                    0x14a00040UL
#define SMN_IOMMU1PCIE1NBIO0_L1_BANK_SEL_0_ADDRESS                    0x14c00040UL
#define SMN_IOMMU1PCIE1NBIO1_L1_BANK_SEL_0_ADDRESS                    0x14e00040UL


/***********************************************************
* Register Name : L1_CACHE_CNTRL_0
************************************************************/

#define L1_CACHE_CNTRL_0_L1CacheSoftInvalidate_OFFSET          0
#define L1_CACHE_CNTRL_0_L1CacheSoftInvalidate_MASK            0xffffffff

typedef union {
  struct {
    UINT32                               L1CacheSoftInvalidate:32;
  } Field;
  UINT32 Value;
} L1_CACHE_CNTRL_0_STRUCT;

#define SMN_L1_CACHE_CNTRL_0_ADDRESS                                  0x15300050UL
#define SMN_IOMMU0IOAGRNBIO0_L1_CACHE_CNTRL_0_ADDRESS                 0x15300050UL
#define SMN_IOMMU0IOAGRNBIO1_L1_CACHE_CNTRL_0_ADDRESS                 0x15500050UL
#define SMN_IOMMU0PCIE0NBIO0_L1_CACHE_CNTRL_0_ADDRESS                 0x14700050UL
#define SMN_IOMMU0PCIE0NBIO1_L1_CACHE_CNTRL_0_ADDRESS                 0x14900050UL
#define SMN_IOMMU0PCIE1NBIO0_L1_CACHE_CNTRL_0_ADDRESS                 0x14b00050UL
#define SMN_IOMMU0PCIE1NBIO1_L1_CACHE_CNTRL_0_ADDRESS                 0x14d00050UL
#define SMN_IOMMU1IOAGRNBIO0_L1_CACHE_CNTRL_0_ADDRESS                 0x15400050UL
#define SMN_IOMMU1IOAGRNBIO1_L1_CACHE_CNTRL_0_ADDRESS                 0x15600050UL
#define SMN_IOMMU1PCIE0NBIO0_L1_CACHE_CNTRL_0_ADDRESS                 0x14800050UL
#define SMN_IOMMU1PCIE0NBIO1_L1_CACHE_CNTRL_0_ADDRESS                 0x14a00050UL
#define SMN_IOMMU1PCIE1NBIO0_L1_CACHE_CNTRL_0_ADDRESS                 0x14c00050UL
#define SMN_IOMMU1PCIE1NBIO1_L1_CACHE_CNTRL_0_ADDRESS                 0x14e00050UL


/***********************************************************
* Register Name : L1_CACHE_CTRL_0
************************************************************/

#define L1_CACHE_CTRL_0_CacheByPass_OFFSET                     0
#define L1_CACHE_CTRL_0_CacheByPass_MASK                       0xffffffff

typedef union {
  struct {
    UINT32                                         CacheByPass:32;
  } Field;
  UINT32 Value;
} L1_CACHE_CTRL_0_STRUCT;

#define SMN_L1_CACHE_CTRL_0_ADDRESS                                   0x15300170UL
#define SMN_IOMMU0IOAGRNBIO0_L1_CACHE_CTRL_0_ADDRESS                  0x15300170UL
#define SMN_IOMMU0IOAGRNBIO1_L1_CACHE_CTRL_0_ADDRESS                  0x15500170UL
#define SMN_IOMMU0PCIE0NBIO0_L1_CACHE_CTRL_0_ADDRESS                  0x14700170UL
#define SMN_IOMMU0PCIE0NBIO1_L1_CACHE_CTRL_0_ADDRESS                  0x14900170UL
#define SMN_IOMMU0PCIE1NBIO0_L1_CACHE_CTRL_0_ADDRESS                  0x14b00170UL
#define SMN_IOMMU0PCIE1NBIO1_L1_CACHE_CTRL_0_ADDRESS                  0x14d00170UL
#define SMN_IOMMU1IOAGRNBIO0_L1_CACHE_CTRL_0_ADDRESS                  0x15400170UL
#define SMN_IOMMU1IOAGRNBIO1_L1_CACHE_CTRL_0_ADDRESS                  0x15600170UL
#define SMN_IOMMU1PCIE0NBIO0_L1_CACHE_CTRL_0_ADDRESS                  0x14800170UL
#define SMN_IOMMU1PCIE0NBIO1_L1_CACHE_CTRL_0_ADDRESS                  0x14a00170UL
#define SMN_IOMMU1PCIE1NBIO0_L1_CACHE_CTRL_0_ADDRESS                  0x14c00170UL
#define SMN_IOMMU1PCIE1NBIO1_L1_CACHE_CTRL_0_ADDRESS                  0x14e00170UL


/***********************************************************
* Register Name : L1_CACHE_CTRL_1
************************************************************/

#define L1_CACHE_CTRL_1_TLBMode_OFFSET                         0
#define L1_CACHE_CTRL_1_TLBMode_MASK                           0x7

#define L1_CACHE_CTRL_1_Reserved_31_3_OFFSET                   3
#define L1_CACHE_CTRL_1_Reserved_31_3_MASK                     0xfffffff8

typedef union {
  struct {
    UINT32                                             TLBMode:3;
    UINT32                                       Reserved_31_3:29;
  } Field;
  UINT32 Value;
} L1_CACHE_CTRL_1_STRUCT;

#define SMN_L1_CACHE_CTRL_1_ADDRESS                                   0x15300174UL
#define SMN_IOMMU0IOAGRNBIO0_L1_CACHE_CTRL_1_ADDRESS                  0x15300174UL
#define SMN_IOMMU0IOAGRNBIO1_L1_CACHE_CTRL_1_ADDRESS                  0x15500174UL
#define SMN_IOMMU0PCIE0NBIO0_L1_CACHE_CTRL_1_ADDRESS                  0x14700174UL
#define SMN_IOMMU0PCIE0NBIO1_L1_CACHE_CTRL_1_ADDRESS                  0x14900174UL
#define SMN_IOMMU0PCIE1NBIO0_L1_CACHE_CTRL_1_ADDRESS                  0x14b00174UL
#define SMN_IOMMU0PCIE1NBIO1_L1_CACHE_CTRL_1_ADDRESS                  0x14d00174UL
#define SMN_IOMMU1IOAGRNBIO0_L1_CACHE_CTRL_1_ADDRESS                  0x15400174UL
#define SMN_IOMMU1IOAGRNBIO1_L1_CACHE_CTRL_1_ADDRESS                  0x15600174UL
#define SMN_IOMMU1PCIE0NBIO0_L1_CACHE_CTRL_1_ADDRESS                  0x14800174UL
#define SMN_IOMMU1PCIE0NBIO1_L1_CACHE_CTRL_1_ADDRESS                  0x14a00174UL
#define SMN_IOMMU1PCIE1NBIO0_L1_CACHE_CTRL_1_ADDRESS                  0x14c00174UL
#define SMN_IOMMU1PCIE1NBIO1_L1_CACHE_CTRL_1_ADDRESS                  0x14e00174UL


/***********************************************************
* Register Name : L1_CLKCNTRL_0
************************************************************/

#define L1_CLKCNTRL_0_Reserved_3_0_OFFSET                      0
#define L1_CLKCNTRL_0_Reserved_3_0_MASK                        0xf

#define L1_CLKCNTRL_0_L1_DMA_CLKGATE_EN_OFFSET                 4
#define L1_CLKCNTRL_0_L1_DMA_CLKGATE_EN_MASK                   0x10
#define L1_CLKCNTRL_0_L1_DMA_CLKGATE_EN_DEFAULT     0x1

#define L1_CLKCNTRL_0_L1_CACHE_CLKGATE_EN_OFFSET               5
#define L1_CLKCNTRL_0_L1_CACHE_CLKGATE_EN_MASK                 0x20
#define L1_CLKCNTRL_0_L1_CACHE_CLKGATE_EN_DEFAULT     0x1

#define L1_CLKCNTRL_0_L1_CPSLV_CLKGATE_EN_OFFSET               6
#define L1_CLKCNTRL_0_L1_CPSLV_CLKGATE_EN_MASK                 0x40
#define L1_CLKCNTRL_0_L1_CPSLV_CLKGATE_EN_DEFAULT     0x1

#define L1_CLKCNTRL_0_Reserved_7_7_OFFSET                      7
#define L1_CLKCNTRL_0_Reserved_7_7_MASK                        0x80

#define L1_CLKCNTRL_0_L1_PERF_CLKGATE_EN_OFFSET                8
#define L1_CLKCNTRL_0_L1_PERF_CLKGATE_EN_MASK                  0x100
#define L1_CLKCNTRL_0_L1_PERF_CLKGATE_EN_DEFAULT     0x1

#define L1_CLKCNTRL_0_L1_MEMORY_CLKGATE_EN_OFFSET              9
#define L1_CLKCNTRL_0_L1_MEMORY_CLKGATE_EN_MASK                0x200
#define L1_CLKCNTRL_0_L1_MEMORY_CLKGATE_EN_DEFAULT     0x1

#define L1_CLKCNTRL_0_L1_REG_CLKGATE_EN_OFFSET                 10
#define L1_CLKCNTRL_0_L1_REG_CLKGATE_EN_MASK                   0x400
#define L1_CLKCNTRL_0_L1_REG_CLKGATE_EN_DEFAULT     0x1

#define L1_CLKCNTRL_0_L1_HOSTREQ_CLKGATE_EN_OFFSET             11
#define L1_CLKCNTRL_0_L1_HOSTREQ_CLKGATE_EN_MASK               0x800
#define L1_CLKCNTRL_0_L1_HOSTREQ_CLKGATE_EN_DEFAULT     0x1

#define L1_CLKCNTRL_0_L1_DMARSP_CLKGATE_EN_OFFSET              12
#define L1_CLKCNTRL_0_L1_DMARSP_CLKGATE_EN_MASK                0x1000
#define L1_CLKCNTRL_0_L1_DMARSP_CLKGATE_EN_DEFAULT     0x1

#define L1_CLKCNTRL_0_L1_HOSTRSP_CLKGATE_EN_OFFSET             13
#define L1_CLKCNTRL_0_L1_HOSTRSP_CLKGATE_EN_MASK               0x2000
#define L1_CLKCNTRL_0_L1_HOSTRSP_CLKGATE_EN_DEFAULT     0x1

#define L1_CLKCNTRL_0_L1_SION_PERF_CLKGATE_EN_OFFSET           14
#define L1_CLKCNTRL_0_L1_SION_PERF_CLKGATE_EN_MASK             0x4000
#define L1_CLKCNTRL_0_L1_SION_PERF_CLKGATE_EN_DEFAULT     0x1

#define L1_CLKCNTRL_0_L1_CLKGATE_HYSTERESIS_OFFSET             15
#define L1_CLKCNTRL_0_L1_CLKGATE_HYSTERESIS_MASK               0x7f8000
#define L1_CLKCNTRL_0_L1_CLKGATE_HYSTERESIS_DEFAULT     0x20

#define L1_CLKCNTRL_0_L1_SMMIOREQ_CLKGATE_EN_OFFSET            23
#define L1_CLKCNTRL_0_L1_SMMIOREQ_CLKGATE_EN_MASK              0x800000

#define L1_CLKCNTRL_0_Reserved_30_24_OFFSET                    24
#define L1_CLKCNTRL_0_Reserved_30_24_MASK                      0x7f000000

#define L1_CLKCNTRL_0_L1_L2_CLKGATE_EN_OFFSET                  31
#define L1_CLKCNTRL_0_L1_L2_CLKGATE_EN_MASK                    0x80000000
#define L1_CLKCNTRL_0_L1_L2_CLKGATE_EN_DEFAULT     0x1

typedef union {
  struct {
    UINT32                                        Reserved_3_0:4;
    UINT32                                   L1_DMA_CLKGATE_EN:1;
    UINT32                                 L1_CACHE_CLKGATE_EN:1;
    UINT32                                 L1_CPSLV_CLKGATE_EN:1;
    UINT32                                        Reserved_7_7:1;
    UINT32                                  L1_PERF_CLKGATE_EN:1;
    UINT32                                L1_MEMORY_CLKGATE_EN:1;
    UINT32                                   L1_REG_CLKGATE_EN:1;
    UINT32                               L1_HOSTREQ_CLKGATE_EN:1;
    UINT32                                L1_DMARSP_CLKGATE_EN:1;
    UINT32                               L1_HOSTRSP_CLKGATE_EN:1;
    UINT32                             L1_SION_PERF_CLKGATE_EN:1;
    UINT32                               L1_CLKGATE_HYSTERESIS:8;
    UINT32                              L1_SMMIOREQ_CLKGATE_EN:1;
    UINT32                                      Reserved_30_24:7;
    UINT32                                    L1_L2_CLKGATE_EN:1;
  } Field;
  UINT32 Value;
} L1_CLKCNTRL_0_STRUCT;

#define SMN_L1_CLKCNTRL_0_ADDRESS                                     0x153000ccUL
#define SMN_IOMMU0IOAGRNBIO0_L1_CLKCNTRL_0_ADDRESS                    0x153000ccUL
#define SMN_IOMMU0IOAGRNBIO1_L1_CLKCNTRL_0_ADDRESS                    0x155000ccUL
#define SMN_IOMMU0PCIE0NBIO0_L1_CLKCNTRL_0_ADDRESS                    0x147000ccUL
#define SMN_IOMMU0PCIE0NBIO1_L1_CLKCNTRL_0_ADDRESS                    0x149000ccUL
#define SMN_IOMMU0PCIE1NBIO0_L1_CLKCNTRL_0_ADDRESS                    0x14b000ccUL
#define SMN_IOMMU0PCIE1NBIO1_L1_CLKCNTRL_0_ADDRESS                    0x14d000ccUL
#define SMN_IOMMU1IOAGRNBIO0_L1_CLKCNTRL_0_ADDRESS                    0x154000ccUL
#define SMN_IOMMU1IOAGRNBIO1_L1_CLKCNTRL_0_ADDRESS                    0x156000ccUL
#define SMN_IOMMU1PCIE0NBIO0_L1_CLKCNTRL_0_ADDRESS                    0x148000ccUL
#define SMN_IOMMU1PCIE0NBIO1_L1_CLKCNTRL_0_ADDRESS                    0x14a000ccUL
#define SMN_IOMMU1PCIE1NBIO0_L1_CLKCNTRL_0_ADDRESS                    0x14c000ccUL
#define SMN_IOMMU1PCIE1NBIO1_L1_CLKCNTRL_0_ADDRESS                    0x14e000ccUL


/***********************************************************
* Register Name : L1_CNTRL_0
************************************************************/

#define L1_CNTRL_0_Reserved_0_0_OFFSET                         0
#define L1_CNTRL_0_Reserved_0_0_MASK                           0x1

#define L1_CNTRL_0_Fragment_dis_OFFSET                         1
#define L1_CNTRL_0_Fragment_dis_MASK                           0x2

#define L1_CNTRL_0_CacheIR_only_OFFSET                         2
#define L1_CNTRL_0_CacheIR_only_MASK                           0x4

#define L1_CNTRL_0_CacheIW_only_OFFSET                         3
#define L1_CNTRL_0_CacheIW_only_MASK                           0x8

#define L1_CNTRL_0_L1ForceOrderedAbort_OFFSET                  4
#define L1_CNTRL_0_L1ForceOrderedAbort_MASK                    0x10

#define L1_CNTRL_0_L2Credits_OFFSET                            5
#define L1_CNTRL_0_L2Credits_MASK                              0x7e0

#define L1_CNTRL_0_L2RTCredits_OFFSET                          11
#define L1_CNTRL_0_L2RTCredits_MASK                            0x1f800

#define L1_CNTRL_0_Reserved_19_17_OFFSET                       17
#define L1_CNTRL_0_Reserved_19_17_MASK                         0xe0000

#define L1_CNTRL_0_L1Banks_OFFSET                              20
#define L1_CNTRL_0_L1Banks_MASK                                0x700000

#define L1_CNTRL_0_Reserved_23_23_OFFSET                       23
#define L1_CNTRL_0_Reserved_23_23_MASK                         0x800000

#define L1_CNTRL_0_L1Entries_OFFSET                            24
#define L1_CNTRL_0_L1Entries_MASK                              0xf000000

#define L1_CNTRL_0_L1ErrEventDetectDis_OFFSET                  28
#define L1_CNTRL_0_L1ErrEventDetectDis_MASK                    0x10000000

#define L1_CNTRL_0_L1ForceHostRspPassPWHigh_OFFSET             29
#define L1_CNTRL_0_L1ForceHostRspPassPWHigh_MASK               0x60000000

#define L1_CNTRL_0_L1InterruptHalfDwDis_OFFSET                 31
#define L1_CNTRL_0_L1InterruptHalfDwDis_MASK                   0x80000000

typedef union {
  struct {
    UINT32                                        Reserved_0_0:1;
    UINT32                                        Fragment_dis:1;
    UINT32                                        CacheIR_only:1;
    UINT32                                        CacheIW_only:1;
    UINT32                                 L1ForceOrderedAbort:1;
    UINT32                                           L2Credits:6;
    UINT32                                         L2RTCredits:6;
    UINT32                                      Reserved_19_17:3;
    UINT32                                             L1Banks:3;
    UINT32                                      Reserved_23_23:1;
    UINT32                                           L1Entries:4;
    UINT32                                 L1ErrEventDetectDis:1;
    UINT32                            L1ForceHostRspPassPWHigh:2;
    UINT32                                L1InterruptHalfDwDis:1;
  } Field;
  UINT32 Value;
} L1_CNTRL_0_STRUCT;

#define SMN_L1_CNTRL_0_ADDRESS                                        0x15300030UL
#define SMN_IOMMU0IOAGRNBIO0_L1_CNTRL_0_ADDRESS                       0x15300030UL
#define SMN_IOMMU0IOAGRNBIO1_L1_CNTRL_0_ADDRESS                       0x15500030UL
#define SMN_IOMMU0PCIE0NBIO0_L1_CNTRL_0_ADDRESS                       0x14700030UL
#define SMN_IOMMU0PCIE0NBIO1_L1_CNTRL_0_ADDRESS                       0x14900030UL
#define SMN_IOMMU0PCIE1NBIO0_L1_CNTRL_0_ADDRESS                       0x14b00030UL
#define SMN_IOMMU0PCIE1NBIO1_L1_CNTRL_0_ADDRESS                       0x14d00030UL
#define SMN_IOMMU1IOAGRNBIO0_L1_CNTRL_0_ADDRESS                       0x15400030UL
#define SMN_IOMMU1IOAGRNBIO1_L1_CNTRL_0_ADDRESS                       0x15600030UL
#define SMN_IOMMU1PCIE0NBIO0_L1_CNTRL_0_ADDRESS                       0x14800030UL
#define SMN_IOMMU1PCIE0NBIO1_L1_CNTRL_0_ADDRESS                       0x14a00030UL
#define SMN_IOMMU1PCIE1NBIO0_L1_CNTRL_0_ADDRESS                       0x14c00030UL
#define SMN_IOMMU1PCIE1NBIO1_L1_CNTRL_0_ADDRESS                       0x14e00030UL


/***********************************************************
* Register Name : L1_CNTRL_1
************************************************************/

#define L1_CNTRL_1_Reserved_1_0_OFFSET                         0
#define L1_CNTRL_1_Reserved_1_0_MASK                           0x3

#define L1_CNTRL_1_timeout_on_any_migr_state_OFFSET            2
#define L1_CNTRL_1_timeout_on_any_migr_state_MASK              0x4

#define L1_CNTRL_1_WaitAllChainLinks_s1_OFFSET                 3
#define L1_CNTRL_1_WaitAllChainLinks_s1_MASK                   0x8

#define L1_CNTRL_1_QoSAlwaysPasses_dis_OFFSET                  4
#define L1_CNTRL_1_QoSAlwaysPasses_dis_MASK                    0x10

#define L1_CNTRL_1_WQMISS_ARB_burst_OFFSET                     5
#define L1_CNTRL_1_WQMISS_ARB_burst_MASK                       0xe0

#define L1_CNTRL_1_TLBRdPred_dis_OFFSET                        8
#define L1_CNTRL_1_TLBRdPred_dis_MASK                          0x100

#define L1_CNTRL_1_WaitAllChainLinks_OFFSET                    9
#define L1_CNTRL_1_WaitAllChainLinks_MASK                      0x200

#define L1_CNTRL_1_L1CacheParityEn_OFFSET                      10
#define L1_CNTRL_1_L1CacheParityEn_MASK                        0x400

#define L1_CNTRL_1_L1ParityEn_OFFSET                           11
#define L1_CNTRL_1_L1ParityEn_MASK                             0x800

#define L1_CNTRL_1_L1DTEDis_OFFSET                             12
#define L1_CNTRL_1_L1DTEDis_MASK                               0x1000

#define L1_CNTRL_1_WQ_EntryDis_OFFSET                          13
#define L1_CNTRL_1_WQ_EntryDis_MASK                            0xfe000

#define L1_CNTRL_1_Snd_filter_dis_OFFSET                       20
#define L1_CNTRL_1_Snd_filter_dis_MASK                         0x100000

#define L1_CNTRL_1_L1Order_en_OFFSET                           21
#define L1_CNTRL_1_L1Order_en_MASK                             0x200000

#define L1_CNTRL_1_L1CacheInvAllEn_OFFSET                      22
#define L1_CNTRL_1_L1CacheInvAllEn_MASK                        0x400000

#define L1_CNTRL_1_Select_timeout_pulse_OFFSET                 23
#define L1_CNTRL_1_Select_timeout_pulse_MASK                   0x3800000

#define L1_CNTRL_1_L1_cache_sel_reqid_OFFSET                   26
#define L1_CNTRL_1_L1_cache_sel_reqid_MASK                     0x4000000

#define L1_CNTRL_1_L1_cache_sel_interleave_OFFSET              27
#define L1_CNTRL_1_L1_cache_sel_interleave_MASK                0x8000000

#define L1_CNTRL_1_Pretrans_noVA_filterEn_OFFSET               28
#define L1_CNTRL_1_Pretrans_noVA_filterEn_MASK                 0x10000000

#define L1_CNTRL_1_UnTrans_2M_filterEn_OFFSET                  29
#define L1_CNTRL_1_UnTrans_2M_filterEn_MASK                    0x20000000

#define L1_CNTRL_1_L1StrictVCOrder_En_OFFSET                   30
#define L1_CNTRL_1_L1StrictVCOrder_En_MASK                     0x40000000

#define L1_CNTRL_1_L1DmaUseChainAll_En_OFFSET                  31
#define L1_CNTRL_1_L1DmaUseChainAll_En_MASK                    0x80000000

typedef union {
  struct {
    UINT32                                        Reserved_1_0:2;
    UINT32                           timeout_on_any_migr_state:1;
    UINT32                                WaitAllChainLinks_s1:1;
    UINT32                                 QoSAlwaysPasses_dis:1;
    UINT32                                    WQMISS_ARB_burst:3;
    UINT32                                       TLBRdPred_dis:1;
    UINT32                                   WaitAllChainLinks:1;
    UINT32                                     L1CacheParityEn:1;
    UINT32                                          L1ParityEn:1;
    UINT32                                            L1DTEDis:1;
    UINT32                                         WQ_EntryDis:7;
    UINT32                                      Snd_filter_dis:1;
    UINT32                                          L1Order_en:1;
    UINT32                                     L1CacheInvAllEn:1;
    UINT32                                Select_timeout_pulse:3;
    UINT32                                  L1_cache_sel_reqid:1;
    UINT32                             L1_cache_sel_interleave:1;
    UINT32                              Pretrans_noVA_filterEn:1;
    UINT32                                 UnTrans_2M_filterEn:1;
    UINT32                                  L1StrictVCOrder_En:1;
    UINT32                                 L1DmaUseChainAll_En:1;
  } Field;
  UINT32 Value;
} L1_CNTRL_1_STRUCT;

#define SMN_L1_CNTRL_1_ADDRESS                                        0x15300034UL
#define SMN_IOMMU0IOAGRNBIO0_L1_CNTRL_1_ADDRESS                       0x15300034UL
#define SMN_IOMMU0IOAGRNBIO1_L1_CNTRL_1_ADDRESS                       0x15500034UL
#define SMN_IOMMU0PCIE0NBIO0_L1_CNTRL_1_ADDRESS                       0x14700034UL
#define SMN_IOMMU0PCIE0NBIO1_L1_CNTRL_1_ADDRESS                       0x14900034UL
#define SMN_IOMMU0PCIE1NBIO0_L1_CNTRL_1_ADDRESS                       0x14b00034UL
#define SMN_IOMMU0PCIE1NBIO1_L1_CNTRL_1_ADDRESS                       0x14d00034UL
#define SMN_IOMMU1IOAGRNBIO0_L1_CNTRL_1_ADDRESS                       0x15400034UL
#define SMN_IOMMU1IOAGRNBIO1_L1_CNTRL_1_ADDRESS                       0x15600034UL
#define SMN_IOMMU1PCIE0NBIO0_L1_CNTRL_1_ADDRESS                       0x14800034UL
#define SMN_IOMMU1PCIE0NBIO1_L1_CNTRL_1_ADDRESS                       0x14a00034UL
#define SMN_IOMMU1PCIE1NBIO0_L1_CNTRL_1_ADDRESS                       0x14c00034UL
#define SMN_IOMMU1PCIE1NBIO1_L1_CNTRL_1_ADDRESS                       0x14e00034UL


/***********************************************************
* Register Name : L1_CNTRL_2
************************************************************/

#define L1_CNTRL_2_L1Disable_OFFSET                            0
#define L1_CNTRL_2_L1Disable_MASK                              0x1

#define L1_CNTRL_2_MSI_to_HT_remap_dis_OFFSET                  1
#define L1_CNTRL_2_MSI_to_HT_remap_dis_MASK                    0x2

#define L1_CNTRL_2_L1_abrt_ats_dis_OFFSET                      2
#define L1_CNTRL_2_L1_abrt_ats_dis_MASK                        0x4

#define L1_CNTRL_2_L1ATSDataErrorSignalEn_OFFSET               3
#define L1_CNTRL_2_L1ATSDataErrorSignalEn_MASK                 0x8

#define L1_CNTRL_2_dGPUMode_OFFSET                             4
#define L1_CNTRL_2_dGPUMode_MASK                               0x10

#define L1_CNTRL_2_L1_IOHC_WrRsp_ordering_OFFSET               5
#define L1_CNTRL_2_L1_IOHC_WrRsp_ordering_MASK                 0x60

#define L1_CNTRL_2_L1_IOHC_RdRsp_ordering_OFFSET               7
#define L1_CNTRL_2_L1_IOHC_RdRsp_ordering_MASK                 0x180

#define L1_CNTRL_2_L2PRICredits_OFFSET                         9
#define L1_CNTRL_2_L2PRICredits_MASK                           0x7e00

#define L1_CNTRL_2_Reserved_23_15_OFFSET                       15
#define L1_CNTRL_2_Reserved_23_15_MASK                         0xff8000

#define L1_CNTRL_2_CPD_RESP_MODE_OFFSET                        24
#define L1_CNTRL_2_CPD_RESP_MODE_MASK                          0x7000000

#define L1_CNTRL_2_L1NonConsumedDataErrorSignalEn_OFFSET       27
#define L1_CNTRL_2_L1NonConsumedDataErrorSignalEn_MASK         0x8000000

#define L1_CNTRL_2_L1ConsumedDataErrorSignalEn_OFFSET          28
#define L1_CNTRL_2_L1ConsumedDataErrorSignalEn_MASK            0x10000000

#define L1_CNTRL_2_L1SDPParityEn_OFFSET                        29
#define L1_CNTRL_2_L1SDPParityEn_MASK                          0x20000000

#define L1_CNTRL_2_WQ_EntryDis1_OFFSET                         30
#define L1_CNTRL_2_WQ_EntryDis1_MASK                           0xc0000000

typedef union {
  struct {
    UINT32                                           L1Disable:1;
    UINT32                                 MSI_to_HT_remap_dis:1;
    UINT32                                     L1_abrt_ats_dis:1;
    UINT32                              L1ATSDataErrorSignalEn:1;
    UINT32                                            dGPUMode:1;
    UINT32                              L1_IOHC_WrRsp_ordering:2;
    UINT32                              L1_IOHC_RdRsp_ordering:2;
    UINT32                                        L2PRICredits:6;
    UINT32                                      Reserved_23_15:9;
    UINT32                                       CPD_RESP_MODE:3;
    UINT32                      L1NonConsumedDataErrorSignalEn:1;
    UINT32                         L1ConsumedDataErrorSignalEn:1;
    UINT32                                       L1SDPParityEn:1;
    UINT32                                        WQ_EntryDis1:2;
  } Field;
  UINT32 Value;
} L1_CNTRL_2_STRUCT;

#define SMN_L1_CNTRL_2_ADDRESS                                        0x15300038UL
#define SMN_IOMMU0IOAGRNBIO0_L1_CNTRL_2_ADDRESS                       0x15300038UL
#define SMN_IOMMU0IOAGRNBIO1_L1_CNTRL_2_ADDRESS                       0x15500038UL
#define SMN_IOMMU0PCIE0NBIO0_L1_CNTRL_2_ADDRESS                       0x14700038UL
#define SMN_IOMMU0PCIE0NBIO1_L1_CNTRL_2_ADDRESS                       0x14900038UL
#define SMN_IOMMU0PCIE1NBIO0_L1_CNTRL_2_ADDRESS                       0x14b00038UL
#define SMN_IOMMU0PCIE1NBIO1_L1_CNTRL_2_ADDRESS                       0x14d00038UL
#define SMN_IOMMU1IOAGRNBIO0_L1_CNTRL_2_ADDRESS                       0x15400038UL
#define SMN_IOMMU1IOAGRNBIO1_L1_CNTRL_2_ADDRESS                       0x15600038UL
#define SMN_IOMMU1PCIE0NBIO0_L1_CNTRL_2_ADDRESS                       0x14800038UL
#define SMN_IOMMU1PCIE0NBIO1_L1_CNTRL_2_ADDRESS                       0x14a00038UL
#define SMN_IOMMU1PCIE1NBIO0_L1_CNTRL_2_ADDRESS                       0x14c00038UL
#define SMN_IOMMU1PCIE1NBIO1_L1_CNTRL_2_ADDRESS                       0x14e00038UL


/***********************************************************
* Register Name : L1_CNTRL_3
************************************************************/

#define L1_CNTRL_3_ATS_tlbinv_pulse_width_OFFSET               0
#define L1_CNTRL_3_ATS_tlbinv_pulse_width_MASK                 0xffffffff

typedef union {
  struct {
    UINT32                              ATS_tlbinv_pulse_width:32;
  } Field;
  UINT32 Value;
} L1_CNTRL_3_STRUCT;

#define SMN_L1_CNTRL_3_ADDRESS                                        0x1530003cUL
#define SMN_IOMMU0IOAGRNBIO0_L1_CNTRL_3_ADDRESS                       0x1530003cUL
#define SMN_IOMMU0IOAGRNBIO1_L1_CNTRL_3_ADDRESS                       0x1550003cUL
#define SMN_IOMMU0PCIE0NBIO0_L1_CNTRL_3_ADDRESS                       0x1470003cUL
#define SMN_IOMMU0PCIE0NBIO1_L1_CNTRL_3_ADDRESS                       0x1490003cUL
#define SMN_IOMMU0PCIE1NBIO0_L1_CNTRL_3_ADDRESS                       0x14b0003cUL
#define SMN_IOMMU0PCIE1NBIO1_L1_CNTRL_3_ADDRESS                       0x14d0003cUL
#define SMN_IOMMU1IOAGRNBIO0_L1_CNTRL_3_ADDRESS                       0x1540003cUL
#define SMN_IOMMU1IOAGRNBIO1_L1_CNTRL_3_ADDRESS                       0x1560003cUL
#define SMN_IOMMU1PCIE0NBIO0_L1_CNTRL_3_ADDRESS                       0x1480003cUL
#define SMN_IOMMU1PCIE0NBIO1_L1_CNTRL_3_ADDRESS                       0x14a0003cUL
#define SMN_IOMMU1PCIE1NBIO0_L1_CNTRL_3_ADDRESS                       0x14c0003cUL
#define SMN_IOMMU1PCIE1NBIO1_L1_CNTRL_3_ADDRESS                       0x14e0003cUL


/***********************************************************
* Register Name : L1_CNTRL_4
************************************************************/

#define L1_CNTRL_4_ATS_multiple_resp_en_OFFSET                 0
#define L1_CNTRL_4_ATS_multiple_resp_en_MASK                   0x1

#define L1_CNTRL_4_Reserved_1_1_OFFSET                         1
#define L1_CNTRL_4_Reserved_1_1_MASK                           0x2

#define L1_CNTRL_4_Timeout_pulse_ext_En_OFFSET                 2
#define L1_CNTRL_4_Timeout_pulse_ext_En_MASK                   0x4

#define L1_CNTRL_4_L1SDP_RdRsp_ParityEn_OFFSET                 3
#define L1_CNTRL_4_L1SDP_RdRsp_ParityEn_MASK                   0x8

#define L1_CNTRL_4_L1SDP_RdRsp_DataStatusErrEn_OFFSET          4
#define L1_CNTRL_4_L1SDP_RdRsp_DataStatusErrEn_MASK            0x10

#define L1_CNTRL_4_L1_sMMIO_block_RMPfetch_cache_wr_OFFSET     5
#define L1_CNTRL_4_L1_sMMIO_block_RMPfetch_cache_wr_MASK       0x20

#define L1_CNTRL_4_L1_SMMIO_ABM_DISABLE_OFFSET                 6
#define L1_CNTRL_4_L1_SMMIO_ABM_DISABLE_MASK                   0x40

#define L1_CNTRL_4_L1_MaskPRGCompletion_ITAG_OFFSET            7
#define L1_CNTRL_4_L1_MaskPRGCompletion_ITAG_MASK              0x80

#define L1_CNTRL_4_Reserved_22_8_OFFSET                        8
#define L1_CNTRL_4_Reserved_22_8_MASK                          0x7fff00

#define L1_CNTRL_4_AtsRsp_send_mem_type_en_OFFSET              23
#define L1_CNTRL_4_AtsRsp_send_mem_type_en_MASK                0x800000

#define L1_CNTRL_4_IntGfx_UnitID_Val_OFFSET                    24
#define L1_CNTRL_4_IntGfx_UnitID_Val_MASK                      0x7f000000

#define L1_CNTRL_4_Reserved_31_31_OFFSET                       31
#define L1_CNTRL_4_Reserved_31_31_MASK                         0x80000000

typedef union {
  struct {
    UINT32                                ATS_multiple_resp_en:1;
    UINT32                                        Reserved_1_1:1;
    UINT32                                Timeout_pulse_ext_En:1;
    UINT32                                L1SDP_RdRsp_ParityEn:1;
    UINT32                         L1SDP_RdRsp_DataStatusErrEn:1;
    UINT32                    L1_sMMIO_block_RMPfetch_cache_wr:1;
    UINT32                                L1_SMMIO_ABM_DISABLE:1;
    UINT32                           L1_MaskPRGCompletion_ITAG:1;
    UINT32                                       Reserved_22_8:15;
    UINT32                             AtsRsp_send_mem_type_en:1;
    UINT32                                   IntGfx_UnitID_Val:7;
    UINT32                                      Reserved_31_31:1;
  } Field;
  UINT32 Value;
} L1_CNTRL_4_STRUCT;

#define SMN_L1_CNTRL_4_ADDRESS                                        0x153000c8UL
#define SMN_IOMMU0IOAGRNBIO0_L1_CNTRL_4_ADDRESS                       0x153000c8UL
#define SMN_IOMMU0IOAGRNBIO1_L1_CNTRL_4_ADDRESS                       0x155000c8UL
#define SMN_IOMMU0PCIE0NBIO0_L1_CNTRL_4_ADDRESS                       0x147000c8UL
#define SMN_IOMMU0PCIE0NBIO1_L1_CNTRL_4_ADDRESS                       0x149000c8UL
#define SMN_IOMMU0PCIE1NBIO0_L1_CNTRL_4_ADDRESS                       0x14b000c8UL
#define SMN_IOMMU0PCIE1NBIO1_L1_CNTRL_4_ADDRESS                       0x14d000c8UL
#define SMN_IOMMU1IOAGRNBIO0_L1_CNTRL_4_ADDRESS                       0x154000c8UL
#define SMN_IOMMU1IOAGRNBIO1_L1_CNTRL_4_ADDRESS                       0x156000c8UL
#define SMN_IOMMU1PCIE0NBIO0_L1_CNTRL_4_ADDRESS                       0x148000c8UL
#define SMN_IOMMU1PCIE0NBIO1_L1_CNTRL_4_ADDRESS                       0x14a000c8UL
#define SMN_IOMMU1PCIE1NBIO0_L1_CNTRL_4_ADDRESS                       0x14c000c8UL
#define SMN_IOMMU1PCIE1NBIO1_L1_CNTRL_4_ADDRESS                       0x14e000c8UL


/***********************************************************
* Register Name : L1_CNTRL_5
************************************************************/

#define L1_CNTRL_5_Reserved_31_0_OFFSET                        0
#define L1_CNTRL_5_Reserved_31_0_MASK                          0xffffffff

typedef union {
  struct {
    UINT32                                       Reserved_31_0:32;
  } Field;
  UINT32 Value;
} L1_CNTRL_5_STRUCT;

#define SMN_L1_CNTRL_5_ADDRESS                                        0x153000e0UL
#define SMN_IOMMU0IOAGRNBIO0_L1_CNTRL_5_ADDRESS                       0x153000e0UL
#define SMN_IOMMU0IOAGRNBIO1_L1_CNTRL_5_ADDRESS                       0x155000e0UL
#define SMN_IOMMU0PCIE0NBIO0_L1_CNTRL_5_ADDRESS                       0x147000e0UL
#define SMN_IOMMU0PCIE0NBIO1_L1_CNTRL_5_ADDRESS                       0x149000e0UL
#define SMN_IOMMU0PCIE1NBIO0_L1_CNTRL_5_ADDRESS                       0x14b000e0UL
#define SMN_IOMMU0PCIE1NBIO1_L1_CNTRL_5_ADDRESS                       0x14d000e0UL
#define SMN_IOMMU1IOAGRNBIO0_L1_CNTRL_5_ADDRESS                       0x154000e0UL
#define SMN_IOMMU1IOAGRNBIO1_L1_CNTRL_5_ADDRESS                       0x156000e0UL
#define SMN_IOMMU1PCIE0NBIO0_L1_CNTRL_5_ADDRESS                       0x148000e0UL
#define SMN_IOMMU1PCIE0NBIO1_L1_CNTRL_5_ADDRESS                       0x14a000e0UL
#define SMN_IOMMU1PCIE1NBIO0_L1_CNTRL_5_ADDRESS                       0x14c000e0UL
#define SMN_IOMMU1PCIE1NBIO1_L1_CNTRL_5_ADDRESS                       0x14e000e0UL


/***********************************************************
* Register Name : L1_CREDIT_RST_CTRL
************************************************************/

#define L1_CREDIT_RST_CTRL_DS_Client_egress_req_rstcredcnt_OFFSET 0
#define L1_CREDIT_RST_CTRL_DS_Client_egress_req_rstcredcnt_MASK 0x1

#define L1_CREDIT_RST_CTRL_DS_Client_egress_rsp_rstcredcnt_OFFSET 1
#define L1_CREDIT_RST_CTRL_DS_Client_egress_rsp_rstcredcnt_MASK 0x2

#define L1_CREDIT_RST_CTRL_DS_Client_ingress_req_rstcredrel_OFFSET 2
#define L1_CREDIT_RST_CTRL_DS_Client_ingress_req_rstcredrel_MASK 0x4

#define L1_CREDIT_RST_CTRL_DS_Client_ingress_rsp_rstcredrel_OFFSET 3
#define L1_CREDIT_RST_CTRL_DS_Client_ingress_rsp_rstcredrel_MASK 0x8

#define L1_CREDIT_RST_CTRL_US_Client_egress_req_rstcredcnt_OFFSET 4
#define L1_CREDIT_RST_CTRL_US_Client_egress_req_rstcredcnt_MASK 0x10

#define L1_CREDIT_RST_CTRL_US_Client_egress_rsp_rstcredcnt_OFFSET 5
#define L1_CREDIT_RST_CTRL_US_Client_egress_rsp_rstcredcnt_MASK 0x20

#define L1_CREDIT_RST_CTRL_US_Client_ingress_req_rstcredrel_OFFSET 6
#define L1_CREDIT_RST_CTRL_US_Client_ingress_req_rstcredrel_MASK 0x40

#define L1_CREDIT_RST_CTRL_US_Client_ingress_rsp_rstcredrel_OFFSET 7
#define L1_CREDIT_RST_CTRL_US_Client_ingress_rsp_rstcredrel_MASK 0x80

#define L1_CREDIT_RST_CTRL_Reserved_30_8_OFFSET                8
#define L1_CREDIT_RST_CTRL_Reserved_30_8_MASK                  0x7fffff00

#define L1_CREDIT_RST_CTRL_rst_wait_port_discon_OFFSET         31
#define L1_CREDIT_RST_CTRL_rst_wait_port_discon_MASK           0x80000000

typedef union {
  struct {
    UINT32                     DS_Client_egress_req_rstcredcnt:1;
    UINT32                     DS_Client_egress_rsp_rstcredcnt:1;
    UINT32                    DS_Client_ingress_req_rstcredrel:1;
    UINT32                    DS_Client_ingress_rsp_rstcredrel:1;
    UINT32                     US_Client_egress_req_rstcredcnt:1;
    UINT32                     US_Client_egress_rsp_rstcredcnt:1;
    UINT32                    US_Client_ingress_req_rstcredrel:1;
    UINT32                    US_Client_ingress_rsp_rstcredrel:1;
    UINT32                                       Reserved_30_8:23;
    UINT32                                rst_wait_port_discon:1;
  } Field;
  UINT32 Value;
} L1_CREDIT_RST_CTRL_STRUCT;

#define SMN_L1_CREDIT_RST_CTRL_ADDRESS                                0x1530019cUL
#define SMN_IOMMU0IOAGRNBIO0_L1_CREDIT_RST_CTRL_ADDRESS               0x1530019cUL
#define SMN_IOMMU0IOAGRNBIO1_L1_CREDIT_RST_CTRL_ADDRESS               0x1550019cUL
#define SMN_IOMMU0PCIE0NBIO0_L1_CREDIT_RST_CTRL_ADDRESS               0x1470019cUL
#define SMN_IOMMU0PCIE0NBIO1_L1_CREDIT_RST_CTRL_ADDRESS               0x1490019cUL
#define SMN_IOMMU0PCIE1NBIO0_L1_CREDIT_RST_CTRL_ADDRESS               0x14b0019cUL
#define SMN_IOMMU0PCIE1NBIO1_L1_CREDIT_RST_CTRL_ADDRESS               0x14d0019cUL
#define SMN_IOMMU1IOAGRNBIO0_L1_CREDIT_RST_CTRL_ADDRESS               0x1540019cUL
#define SMN_IOMMU1IOAGRNBIO1_L1_CREDIT_RST_CTRL_ADDRESS               0x1560019cUL
#define SMN_IOMMU1PCIE0NBIO0_L1_CREDIT_RST_CTRL_ADDRESS               0x1480019cUL
#define SMN_IOMMU1PCIE0NBIO1_L1_CREDIT_RST_CTRL_ADDRESS               0x14a0019cUL
#define SMN_IOMMU1PCIE1NBIO0_L1_CREDIT_RST_CTRL_ADDRESS               0x14c0019cUL
#define SMN_IOMMU1PCIE1NBIO1_L1_CREDIT_RST_CTRL_ADDRESS               0x14e0019cUL


/***********************************************************
* Register Name : L1_DVM_INV_CASES_CAM
************************************************************/

#define L1_DVM_INV_CASES_CAM_APV0_MODE_CAM_OFFSET              0
#define L1_DVM_INV_CASES_CAM_APV0_MODE_CAM_MASK                0x3

#define L1_DVM_INV_CASES_CAM_APV1_MODE_CAM_OFFSET              2
#define L1_DVM_INV_CASES_CAM_APV1_MODE_CAM_MASK                0xc

#define L1_DVM_INV_CASES_CAM_APV2_MODE_CAM_OFFSET              4
#define L1_DVM_INV_CASES_CAM_APV2_MODE_CAM_MASK                0x30

#define L1_DVM_INV_CASES_CAM_APV3_MODE_CAM_OFFSET              6
#define L1_DVM_INV_CASES_CAM_APV3_MODE_CAM_MASK                0xc0

#define L1_DVM_INV_CASES_CAM_APV4_MODE_CAM_OFFSET              8
#define L1_DVM_INV_CASES_CAM_APV4_MODE_CAM_MASK                0x300

#define L1_DVM_INV_CASES_CAM_APV5_MODE_CAM_OFFSET              10
#define L1_DVM_INV_CASES_CAM_APV5_MODE_CAM_MASK                0xc00

#define L1_DVM_INV_CASES_CAM_APV6_MODE_CAM_OFFSET              12
#define L1_DVM_INV_CASES_CAM_APV6_MODE_CAM_MASK                0x3000

#define L1_DVM_INV_CASES_CAM_APV7_MODE_CAM_OFFSET              14
#define L1_DVM_INV_CASES_CAM_APV7_MODE_CAM_MASK                0xc000

#define L1_DVM_INV_CASES_CAM_Reserved_31_16_OFFSET             16
#define L1_DVM_INV_CASES_CAM_Reserved_31_16_MASK               0xffff0000

typedef union {
  struct {
    UINT32                                       APV0_MODE_CAM:2;
    UINT32                                       APV1_MODE_CAM:2;
    UINT32                                       APV2_MODE_CAM:2;
    UINT32                                       APV3_MODE_CAM:2;
    UINT32                                       APV4_MODE_CAM:2;
    UINT32                                       APV5_MODE_CAM:2;
    UINT32                                       APV6_MODE_CAM:2;
    UINT32                                       APV7_MODE_CAM:2;
    UINT32                                      Reserved_31_16:16;
  } Field;
  UINT32 Value;
} L1_DVM_INV_CASES_CAM_STRUCT;

#define SMN_L1_DVM_INV_CASES_CAM_ADDRESS                              0x15300284UL
#define SMN_IOMMU0IOAGRNBIO0_L1_DVM_INV_CASES_CAM_ADDRESS             0x15300284UL
#define SMN_IOMMU0IOAGRNBIO1_L1_DVM_INV_CASES_CAM_ADDRESS             0x15500284UL
#define SMN_IOMMU0PCIE0NBIO0_L1_DVM_INV_CASES_CAM_ADDRESS             0x14700284UL
#define SMN_IOMMU0PCIE0NBIO1_L1_DVM_INV_CASES_CAM_ADDRESS             0x14900284UL
#define SMN_IOMMU0PCIE1NBIO0_L1_DVM_INV_CASES_CAM_ADDRESS             0x14b00284UL
#define SMN_IOMMU0PCIE1NBIO1_L1_DVM_INV_CASES_CAM_ADDRESS             0x14d00284UL
#define SMN_IOMMU1IOAGRNBIO0_L1_DVM_INV_CASES_CAM_ADDRESS             0x15400284UL
#define SMN_IOMMU1IOAGRNBIO1_L1_DVM_INV_CASES_CAM_ADDRESS             0x15600284UL
#define SMN_IOMMU1PCIE0NBIO0_L1_DVM_INV_CASES_CAM_ADDRESS             0x14800284UL
#define SMN_IOMMU1PCIE0NBIO1_L1_DVM_INV_CASES_CAM_ADDRESS             0x14a00284UL
#define SMN_IOMMU1PCIE1NBIO0_L1_DVM_INV_CASES_CAM_ADDRESS             0x14c00284UL
#define SMN_IOMMU1PCIE1NBIO1_L1_DVM_INV_CASES_CAM_ADDRESS             0x14e00284UL


/***********************************************************
* Register Name : L1_FEATURE_CNTRL
************************************************************/

#define L1_FEATURE_CNTRL_Rsv_sticky_bits0_OFFSET               0
#define L1_FEATURE_CNTRL_Rsv_sticky_bits0_MASK                 0x1

#define L1_FEATURE_CNTRL_PMR_lock_bit_OFFSET                   1
#define L1_FEATURE_CNTRL_PMR_lock_bit_MASK                     0x2

#define L1_FEATURE_CNTRL_EXE_lock_bit_OFFSET                   2
#define L1_FEATURE_CNTRL_EXE_lock_bit_MASK                     0x4

#define L1_FEATURE_CNTRL_Rsv_sticky_bits7to3_OFFSET            3
#define L1_FEATURE_CNTRL_Rsv_sticky_bits7to3_MASK              0xf8

#define L1_FEATURE_CNTRL_Reserved_31_8_OFFSET                  8
#define L1_FEATURE_CNTRL_Reserved_31_8_MASK                    0xffffff00

typedef union {
  struct {
    UINT32                                    Rsv_sticky_bits0:1;
    UINT32                                        PMR_lock_bit:1;
    UINT32                                        EXE_lock_bit:1;
    UINT32                                 Rsv_sticky_bits7to3:5;
    UINT32                                       Reserved_31_8:24;
  } Field;
  UINT32 Value;
} L1_FEATURE_CNTRL_STRUCT;

#define SMN_L1_FEATURE_CNTRL_ADDRESS                                  0x1530009cUL
#define SMN_IOMMU0IOAGRNBIO0_L1_FEATURE_CNTRL_ADDRESS                 0x1530009cUL
#define SMN_IOMMU0IOAGRNBIO1_L1_FEATURE_CNTRL_ADDRESS                 0x1550009cUL
#define SMN_IOMMU0PCIE0NBIO0_L1_FEATURE_CNTRL_ADDRESS                 0x1470009cUL
#define SMN_IOMMU0PCIE0NBIO1_L1_FEATURE_CNTRL_ADDRESS                 0x1490009cUL
#define SMN_IOMMU0PCIE1NBIO0_L1_FEATURE_CNTRL_ADDRESS                 0x14b0009cUL
#define SMN_IOMMU0PCIE1NBIO1_L1_FEATURE_CNTRL_ADDRESS                 0x14d0009cUL
#define SMN_IOMMU1IOAGRNBIO0_L1_FEATURE_CNTRL_ADDRESS                 0x1540009cUL
#define SMN_IOMMU1IOAGRNBIO1_L1_FEATURE_CNTRL_ADDRESS                 0x1560009cUL
#define SMN_IOMMU1PCIE0NBIO0_L1_FEATURE_CNTRL_ADDRESS                 0x1480009cUL
#define SMN_IOMMU1PCIE0NBIO1_L1_FEATURE_CNTRL_ADDRESS                 0x14a0009cUL
#define SMN_IOMMU1PCIE1NBIO0_L1_FEATURE_CNTRL_ADDRESS                 0x14c0009cUL
#define SMN_IOMMU1PCIE1NBIO1_L1_FEATURE_CNTRL_ADDRESS                 0x14e0009cUL


/***********************************************************
* Register Name : L1_FEATURE_SUP_CNTRL
************************************************************/

#define L1_FEATURE_SUP_CNTRL_L1_EFR_SUP_OFFSET                 0
#define L1_FEATURE_SUP_CNTRL_L1_EFR_SUP_MASK                   0x1

#define L1_FEATURE_SUP_CNTRL_L1_PPR_SUP_OFFSET                 1
#define L1_FEATURE_SUP_CNTRL_L1_PPR_SUP_MASK                   0x2

#define L1_FEATURE_SUP_CNTRL_L1_DTE_seg_W_OFFSET               2
#define L1_FEATURE_SUP_CNTRL_L1_DTE_seg_W_MASK                 0xc

#define L1_FEATURE_SUP_CNTRL_L1_GT_SUP_W_OFFSET                4
#define L1_FEATURE_SUP_CNTRL_L1_GT_SUP_W_MASK                  0x10

#define L1_FEATURE_SUP_CNTRL_L1_XT_SUP_W_OFFSET                5
#define L1_FEATURE_SUP_CNTRL_L1_XT_SUP_W_MASK                  0x20

#define L1_FEATURE_SUP_CNTRL_L1_SVEIO_SUP_W_OFFSET             6
#define L1_FEATURE_SUP_CNTRL_L1_SVEIO_SUP_W_MASK               0x40

#define L1_FEATURE_SUP_CNTRL_L1_SEVSNPIO_SUP_W_OFFSET          7
#define L1_FEATURE_SUP_CNTRL_L1_SEVSNPIO_SUP_W_MASK            0x80

#define L1_FEATURE_SUP_CNTRL_L1_NUM_INT_SUP_W_OFFSET           8
#define L1_FEATURE_SUP_CNTRL_L1_NUM_INT_SUP_W_MASK             0x300

#define L1_FEATURE_SUP_CNTRL_Reserved_31_10_OFFSET             10
#define L1_FEATURE_SUP_CNTRL_Reserved_31_10_MASK               0xfffffc00

typedef union {
  struct {
    UINT32                                          L1_EFR_SUP:1;
    UINT32                                          L1_PPR_SUP:1;
    UINT32                                        L1_DTE_seg_W:2;
    UINT32                                         L1_GT_SUP_W:1;
    UINT32                                         L1_XT_SUP_W:1;
    UINT32                                      L1_SVEIO_SUP_W:1;
    UINT32                                   L1_SEVSNPIO_SUP_W:1;
    UINT32                                    L1_NUM_INT_SUP_W:2;
    UINT32                                      Reserved_31_10:22;
  } Field;
  UINT32 Value;
} L1_FEATURE_SUP_CNTRL_STRUCT;

#define SMN_L1_FEATURE_SUP_CNTRL_ADDRESS                              0x153000dcUL
#define SMN_IOMMU0IOAGRNBIO0_L1_FEATURE_SUP_CNTRL_ADDRESS             0x153000dcUL
#define SMN_IOMMU0IOAGRNBIO1_L1_FEATURE_SUP_CNTRL_ADDRESS             0x155000dcUL
#define SMN_IOMMU0PCIE0NBIO0_L1_FEATURE_SUP_CNTRL_ADDRESS             0x147000dcUL
#define SMN_IOMMU0PCIE0NBIO1_L1_FEATURE_SUP_CNTRL_ADDRESS             0x149000dcUL
#define SMN_IOMMU0PCIE1NBIO0_L1_FEATURE_SUP_CNTRL_ADDRESS             0x14b000dcUL
#define SMN_IOMMU0PCIE1NBIO1_L1_FEATURE_SUP_CNTRL_ADDRESS             0x14d000dcUL
#define SMN_IOMMU1IOAGRNBIO0_L1_FEATURE_SUP_CNTRL_ADDRESS             0x154000dcUL
#define SMN_IOMMU1IOAGRNBIO1_L1_FEATURE_SUP_CNTRL_ADDRESS             0x156000dcUL
#define SMN_IOMMU1PCIE0NBIO0_L1_FEATURE_SUP_CNTRL_ADDRESS             0x148000dcUL
#define SMN_IOMMU1PCIE0NBIO1_L1_FEATURE_SUP_CNTRL_ADDRESS             0x14a000dcUL
#define SMN_IOMMU1PCIE1NBIO0_L1_FEATURE_SUP_CNTRL_ADDRESS             0x14c000dcUL
#define SMN_IOMMU1PCIE1NBIO1_L1_FEATURE_SUP_CNTRL_ADDRESS             0x14e000dcUL


/***********************************************************
* Register Name : L1_GUEST_ADDR_CNTRL
************************************************************/

#define L1_GUEST_ADDR_CNTRL_L1_CANONICAL_ERR_EN_OFFSET         0
#define L1_GUEST_ADDR_CNTRL_L1_CANONICAL_ERR_EN_MASK           0x1

#define L1_GUEST_ADDR_CNTRL_Reserved_7_1_OFFSET                1
#define L1_GUEST_ADDR_CNTRL_Reserved_7_1_MASK                  0xfe

#define L1_GUEST_ADDR_CNTRL_L1_GUEST_ADDR_MSK_OFFSET           8
#define L1_GUEST_ADDR_CNTRL_L1_GUEST_ADDR_MSK_MASK             0xffffff00

typedef union {
  struct {
    UINT32                                 L1_CANONICAL_ERR_EN:1;
    UINT32                                        Reserved_7_1:7;
    UINT32                                   L1_GUEST_ADDR_MSK:24;
  } Field;
  UINT32 Value;
} L1_GUEST_ADDR_CNTRL_STRUCT;

#define SMN_L1_GUEST_ADDR_CNTRL_ADDRESS                               0x153000d8UL
#define SMN_IOMMU0IOAGRNBIO0_L1_GUEST_ADDR_CNTRL_ADDRESS              0x153000d8UL
#define SMN_IOMMU0IOAGRNBIO1_L1_GUEST_ADDR_CNTRL_ADDRESS              0x155000d8UL
#define SMN_IOMMU0PCIE0NBIO0_L1_GUEST_ADDR_CNTRL_ADDRESS              0x147000d8UL
#define SMN_IOMMU0PCIE0NBIO1_L1_GUEST_ADDR_CNTRL_ADDRESS              0x149000d8UL
#define SMN_IOMMU0PCIE1NBIO0_L1_GUEST_ADDR_CNTRL_ADDRESS              0x14b000d8UL
#define SMN_IOMMU0PCIE1NBIO1_L1_GUEST_ADDR_CNTRL_ADDRESS              0x14d000d8UL
#define SMN_IOMMU1IOAGRNBIO0_L1_GUEST_ADDR_CNTRL_ADDRESS              0x154000d8UL
#define SMN_IOMMU1IOAGRNBIO1_L1_GUEST_ADDR_CNTRL_ADDRESS              0x156000d8UL
#define SMN_IOMMU1PCIE0NBIO0_L1_GUEST_ADDR_CNTRL_ADDRESS              0x148000d8UL
#define SMN_IOMMU1PCIE0NBIO1_L1_GUEST_ADDR_CNTRL_ADDRESS              0x14a000d8UL
#define SMN_IOMMU1PCIE1NBIO0_L1_GUEST_ADDR_CNTRL_ADDRESS              0x14c000d8UL
#define SMN_IOMMU1PCIE1NBIO1_L1_GUEST_ADDR_CNTRL_ADDRESS              0x14e000d8UL


/***********************************************************
* Register Name : L1_HT_RANGE_IGNORE
************************************************************/

#define L1_HT_RANGE_IGNORE_L1_HTR_IGN_EN_OFFSET                0
#define L1_HT_RANGE_IGNORE_L1_HTR_IGN_EN_MASK                  0x1ff

#define L1_HT_RANGE_IGNORE_Reserved_29_9_OFFSET                9
#define L1_HT_RANGE_IGNORE_Reserved_29_9_MASK                  0x3ffffe00

#define L1_HT_RANGE_IGNORE_HTR_IGN_SUP_W_OFFSET                30
#define L1_HT_RANGE_IGNORE_HTR_IGN_SUP_W_MASK                  0x40000000

#define L1_HT_RANGE_IGNORE_L1_HTR_MASK_CBIT_OFFSET             31
#define L1_HT_RANGE_IGNORE_L1_HTR_MASK_CBIT_MASK               0x80000000

typedef union {
  struct {
    UINT32                                       L1_HTR_IGN_EN:9;
    UINT32                                       Reserved_29_9:21;
    UINT32                                       HTR_IGN_SUP_W:1;
    UINT32                                    L1_HTR_MASK_CBIT:1;
  } Field;
  UINT32 Value;
} L1_HT_RANGE_IGNORE_STRUCT;

#define SMN_L1_HT_RANGE_IGNORE_ADDRESS                                0x15300288UL
#define SMN_IOMMU0IOAGRNBIO0_L1_HT_RANGE_IGNORE_ADDRESS               0x15300288UL
#define SMN_IOMMU0IOAGRNBIO1_L1_HT_RANGE_IGNORE_ADDRESS               0x15500288UL
#define SMN_IOMMU0PCIE0NBIO0_L1_HT_RANGE_IGNORE_ADDRESS               0x14700288UL
#define SMN_IOMMU0PCIE0NBIO1_L1_HT_RANGE_IGNORE_ADDRESS               0x14900288UL
#define SMN_IOMMU0PCIE1NBIO0_L1_HT_RANGE_IGNORE_ADDRESS               0x14b00288UL
#define SMN_IOMMU0PCIE1NBIO1_L1_HT_RANGE_IGNORE_ADDRESS               0x14d00288UL
#define SMN_IOMMU1IOAGRNBIO0_L1_HT_RANGE_IGNORE_ADDRESS               0x15400288UL
#define SMN_IOMMU1IOAGRNBIO1_L1_HT_RANGE_IGNORE_ADDRESS               0x15600288UL
#define SMN_IOMMU1PCIE0NBIO0_L1_HT_RANGE_IGNORE_ADDRESS               0x14800288UL
#define SMN_IOMMU1PCIE0NBIO1_L1_HT_RANGE_IGNORE_ADDRESS               0x14a00288UL
#define SMN_IOMMU1PCIE1NBIO0_L1_HT_RANGE_IGNORE_ADDRESS               0x14c00288UL
#define SMN_IOMMU1PCIE1NBIO1_L1_HT_RANGE_IGNORE_ADDRESS               0x14e00288UL


/***********************************************************
* Register Name : L1_INTRPT_ORDER_CTRL
************************************************************/

#define L1_INTRPT_ORDER_CTRL_L1_Relaxed_Intrpt_Order_En_OFFSET 0
#define L1_INTRPT_ORDER_CTRL_L1_Relaxed_Intrpt_Order_En_MASK   0x7ffff

#define L1_INTRPT_ORDER_CTRL_Reserved_31_19_OFFSET             19
#define L1_INTRPT_ORDER_CTRL_Reserved_31_19_MASK               0xfff80000

typedef union {
  struct {
    UINT32                          L1_Relaxed_Intrpt_Order_En:19;
    UINT32                                      Reserved_31_19:13;
  } Field;
  UINT32 Value;
} L1_INTRPT_ORDER_CTRL_STRUCT;

#define SMN_L1_INTRPT_ORDER_CTRL_ADDRESS                              0x15300274UL
#define SMN_IOMMU0IOAGRNBIO0_L1_INTRPT_ORDER_CTRL_ADDRESS             0x15300274UL
#define SMN_IOMMU0IOAGRNBIO1_L1_INTRPT_ORDER_CTRL_ADDRESS             0x15500274UL
#define SMN_IOMMU0PCIE0NBIO0_L1_INTRPT_ORDER_CTRL_ADDRESS             0x14700274UL
#define SMN_IOMMU0PCIE0NBIO1_L1_INTRPT_ORDER_CTRL_ADDRESS             0x14900274UL
#define SMN_IOMMU0PCIE1NBIO0_L1_INTRPT_ORDER_CTRL_ADDRESS             0x14b00274UL
#define SMN_IOMMU0PCIE1NBIO1_L1_INTRPT_ORDER_CTRL_ADDRESS             0x14d00274UL
#define SMN_IOMMU1IOAGRNBIO0_L1_INTRPT_ORDER_CTRL_ADDRESS             0x15400274UL
#define SMN_IOMMU1IOAGRNBIO1_L1_INTRPT_ORDER_CTRL_ADDRESS             0x15600274UL
#define SMN_IOMMU1PCIE0NBIO0_L1_INTRPT_ORDER_CTRL_ADDRESS             0x14800274UL
#define SMN_IOMMU1PCIE0NBIO1_L1_INTRPT_ORDER_CTRL_ADDRESS             0x14a00274UL
#define SMN_IOMMU1PCIE1NBIO0_L1_INTRPT_ORDER_CTRL_ADDRESS             0x14c00274UL
#define SMN_IOMMU1PCIE1NBIO1_L1_INTRPT_ORDER_CTRL_ADDRESS             0x14e00274UL


/***********************************************************
* Register Name : L1_IOMMU_DISABLE_CNTRL
************************************************************/

#define L1_IOMMU_DISABLE_CNTRL_L1_CAM_RST_IOMMUDis_OFFSET      0
#define L1_IOMMU_DISABLE_CNTRL_L1_CAM_RST_IOMMUDis_MASK        0x1

#define L1_IOMMU_DISABLE_CNTRL_Reserved_31_1_OFFSET            1
#define L1_IOMMU_DISABLE_CNTRL_Reserved_31_1_MASK              0xfffffffe

typedef union {
  struct {
    UINT32                                 L1_CAM_RST_IOMMUDis:1;
    UINT32                                       Reserved_31_1:31;
  } Field;
  UINT32 Value;
} L1_IOMMU_DISABLE_CNTRL_STRUCT;

#define SMN_L1_IOMMU_DISABLE_CNTRL_ADDRESS                            0x15300278UL
#define SMN_IOMMU0IOAGRNBIO0_L1_IOMMU_DISABLE_CNTRL_ADDRESS           0x15300278UL
#define SMN_IOMMU0IOAGRNBIO1_L1_IOMMU_DISABLE_CNTRL_ADDRESS           0x15500278UL
#define SMN_IOMMU0PCIE0NBIO0_L1_IOMMU_DISABLE_CNTRL_ADDRESS           0x14700278UL
#define SMN_IOMMU0PCIE0NBIO1_L1_IOMMU_DISABLE_CNTRL_ADDRESS           0x14900278UL
#define SMN_IOMMU0PCIE1NBIO0_L1_IOMMU_DISABLE_CNTRL_ADDRESS           0x14b00278UL
#define SMN_IOMMU0PCIE1NBIO1_L1_IOMMU_DISABLE_CNTRL_ADDRESS           0x14d00278UL
#define SMN_IOMMU1IOAGRNBIO0_L1_IOMMU_DISABLE_CNTRL_ADDRESS           0x15400278UL
#define SMN_IOMMU1IOAGRNBIO1_L1_IOMMU_DISABLE_CNTRL_ADDRESS           0x15600278UL
#define SMN_IOMMU1PCIE0NBIO0_L1_IOMMU_DISABLE_CNTRL_ADDRESS           0x14800278UL
#define SMN_IOMMU1PCIE0NBIO1_L1_IOMMU_DISABLE_CNTRL_ADDRESS           0x14a00278UL
#define SMN_IOMMU1PCIE1NBIO0_L1_IOMMU_DISABLE_CNTRL_ADDRESS           0x14c00278UL
#define SMN_IOMMU1PCIE1NBIO1_L1_IOMMU_DISABLE_CNTRL_ADDRESS           0x14e00278UL


/***********************************************************
* Register Name : L1_L2_RSP_RETRY_CNTRL
************************************************************/

#define L1_L2_RSP_RETRY_CNTRL_L2Rsp_TranslationRetry_En_OFFSET 0
#define L1_L2_RSP_RETRY_CNTRL_L2Rsp_TranslationRetry_En_MASK   0x1

#define L1_L2_RSP_RETRY_CNTRL_Reserved_4_1_OFFSET              1
#define L1_L2_RSP_RETRY_CNTRL_Reserved_4_1_MASK                0x1e

#define L1_L2_RSP_RETRY_CNTRL_L2Rsp_TranslationRetry_Debug_OFFSET 5
#define L1_L2_RSP_RETRY_CNTRL_L2Rsp_TranslationRetry_Debug_MASK 0xe0

#define L1_L2_RSP_RETRY_CNTRL_Reserved_31_8_OFFSET             8
#define L1_L2_RSP_RETRY_CNTRL_Reserved_31_8_MASK               0xffffff00

typedef union {
  struct {
    UINT32                           L2Rsp_TranslationRetry_En:1;
    UINT32                                        Reserved_4_1:4;
    UINT32                        L2Rsp_TranslationRetry_Debug:3;
    UINT32                                       Reserved_31_8:24;
  } Field;
  UINT32 Value;
} L1_L2_RSP_RETRY_CNTRL_STRUCT;

#define SMN_L1_L2_RSP_RETRY_CNTRL_ADDRESS                             0x15300280UL
#define SMN_IOMMU0IOAGRNBIO0_L1_L2_RSP_RETRY_CNTRL_ADDRESS            0x15300280UL
#define SMN_IOMMU0IOAGRNBIO1_L1_L2_RSP_RETRY_CNTRL_ADDRESS            0x15500280UL
#define SMN_IOMMU0PCIE0NBIO0_L1_L2_RSP_RETRY_CNTRL_ADDRESS            0x14700280UL
#define SMN_IOMMU0PCIE0NBIO1_L1_L2_RSP_RETRY_CNTRL_ADDRESS            0x14900280UL
#define SMN_IOMMU0PCIE1NBIO0_L1_L2_RSP_RETRY_CNTRL_ADDRESS            0x14b00280UL
#define SMN_IOMMU0PCIE1NBIO1_L1_L2_RSP_RETRY_CNTRL_ADDRESS            0x14d00280UL
#define SMN_IOMMU1IOAGRNBIO0_L1_L2_RSP_RETRY_CNTRL_ADDRESS            0x15400280UL
#define SMN_IOMMU1IOAGRNBIO1_L1_L2_RSP_RETRY_CNTRL_ADDRESS            0x15600280UL
#define SMN_IOMMU1PCIE0NBIO0_L1_L2_RSP_RETRY_CNTRL_ADDRESS            0x14800280UL
#define SMN_IOMMU1PCIE0NBIO1_L1_L2_RSP_RETRY_CNTRL_ADDRESS            0x14a00280UL
#define SMN_IOMMU1PCIE1NBIO0_L1_L2_RSP_RETRY_CNTRL_ADDRESS            0x14c00280UL
#define SMN_IOMMU1PCIE1NBIO1_L1_L2_RSP_RETRY_CNTRL_ADDRESS            0x14e00280UL


/***********************************************************
* Register Name : L1_LATENCY_COUNTER_CNTRL
************************************************************/

#define L1_LATENCY_COUNTER_CNTRL_LAT_CNT_EN_OFFSET             0
#define L1_LATENCY_COUNTER_CNTRL_LAT_CNT_EN_MASK               0x1

#define L1_LATENCY_COUNTER_CNTRL_LAT_CNT_RST_OFFSET            1
#define L1_LATENCY_COUNTER_CNTRL_LAT_CNT_RST_MASK              0x2

#define L1_LATENCY_COUNTER_CNTRL_LAT_CNT_REQ_HALT_OFFSET       2
#define L1_LATENCY_COUNTER_CNTRL_LAT_CNT_REQ_HALT_MASK         0x4

#define L1_LATENCY_COUNTER_CNTRL_LAT_CNT_WAIT_TIME_HALT_OFFSET 3
#define L1_LATENCY_COUNTER_CNTRL_LAT_CNT_WAIT_TIME_HALT_MASK   0x8

#define L1_LATENCY_COUNTER_CNTRL_Reserved_31_4_OFFSET          4
#define L1_LATENCY_COUNTER_CNTRL_Reserved_31_4_MASK            0xfffffff0

typedef union {
  struct {
    UINT32                                          LAT_CNT_EN:1;
    UINT32                                         LAT_CNT_RST:1;
    UINT32                                    LAT_CNT_REQ_HALT:1;
    UINT32                              LAT_CNT_WAIT_TIME_HALT:1;
    UINT32                                       Reserved_31_4:28;
  } Field;
  UINT32 Value;
} L1_LATENCY_COUNTER_CNTRL_STRUCT;

#define SMN_L1_LATENCY_COUNTER_CNTRL_ADDRESS                          0x15300134UL
#define SMN_IOMMU0IOAGRNBIO0_L1_LATENCY_COUNTER_CNTRL_ADDRESS         0x15300134UL
#define SMN_IOMMU0IOAGRNBIO1_L1_LATENCY_COUNTER_CNTRL_ADDRESS         0x15500134UL
#define SMN_IOMMU0PCIE0NBIO0_L1_LATENCY_COUNTER_CNTRL_ADDRESS         0x14700134UL
#define SMN_IOMMU0PCIE0NBIO1_L1_LATENCY_COUNTER_CNTRL_ADDRESS         0x14900134UL
#define SMN_IOMMU0PCIE1NBIO0_L1_LATENCY_COUNTER_CNTRL_ADDRESS         0x14b00134UL
#define SMN_IOMMU0PCIE1NBIO1_L1_LATENCY_COUNTER_CNTRL_ADDRESS         0x14d00134UL
#define SMN_IOMMU1IOAGRNBIO0_L1_LATENCY_COUNTER_CNTRL_ADDRESS         0x15400134UL
#define SMN_IOMMU1IOAGRNBIO1_L1_LATENCY_COUNTER_CNTRL_ADDRESS         0x15600134UL
#define SMN_IOMMU1PCIE0NBIO0_L1_LATENCY_COUNTER_CNTRL_ADDRESS         0x14800134UL
#define SMN_IOMMU1PCIE0NBIO1_L1_LATENCY_COUNTER_CNTRL_ADDRESS         0x14a00134UL
#define SMN_IOMMU1PCIE1NBIO0_L1_LATENCY_COUNTER_CNTRL_ADDRESS         0x14c00134UL
#define SMN_IOMMU1PCIE1NBIO1_L1_LATENCY_COUNTER_CNTRL_ADDRESS         0x14e00134UL


/***********************************************************
* Register Name : L1_PGMEM_CTRL_4
************************************************************/

#define L1_PGMEM_CTRL_4_L1_SD_thres_OFFSET                     0
#define L1_PGMEM_CTRL_4_L1_SD_thres_MASK                       0xffffffff

typedef union {
  struct {
    UINT32                                         L1_SD_thres:32;
  } Field;
  UINT32 Value;
} L1_PGMEM_CTRL_4_STRUCT;

#define SMN_L1_PGMEM_CTRL_4_ADDRESS                                   0x153000f0UL
#define SMN_IOMMU0IOAGRNBIO0_L1_PGMEM_CTRL_4_ADDRESS                  0x153000f0UL
#define SMN_IOMMU0IOAGRNBIO1_L1_PGMEM_CTRL_4_ADDRESS                  0x155000f0UL
#define SMN_IOMMU0PCIE0NBIO0_L1_PGMEM_CTRL_4_ADDRESS                  0x147000f0UL
#define SMN_IOMMU0PCIE0NBIO1_L1_PGMEM_CTRL_4_ADDRESS                  0x149000f0UL
#define SMN_IOMMU0PCIE1NBIO0_L1_PGMEM_CTRL_4_ADDRESS                  0x14b000f0UL
#define SMN_IOMMU0PCIE1NBIO1_L1_PGMEM_CTRL_4_ADDRESS                  0x14d000f0UL
#define SMN_IOMMU1IOAGRNBIO0_L1_PGMEM_CTRL_4_ADDRESS                  0x154000f0UL
#define SMN_IOMMU1IOAGRNBIO1_L1_PGMEM_CTRL_4_ADDRESS                  0x156000f0UL
#define SMN_IOMMU1PCIE0NBIO0_L1_PGMEM_CTRL_4_ADDRESS                  0x148000f0UL
#define SMN_IOMMU1PCIE0NBIO1_L1_PGMEM_CTRL_4_ADDRESS                  0x14a000f0UL
#define SMN_IOMMU1PCIE1NBIO0_L1_PGMEM_CTRL_4_ADDRESS                  0x14c000f0UL
#define SMN_IOMMU1PCIE1NBIO1_L1_PGMEM_CTRL_4_ADDRESS                  0x14e000f0UL


/***********************************************************
* Register Name : L1_PM_STALL_RETRY_CNTRL
************************************************************/

#define L1_PM_STALL_RETRY_CNTRL_resend_freq_OFFSET             0
#define L1_PM_STALL_RETRY_CNTRL_resend_freq_MASK               0xff

#define L1_PM_STALL_RETRY_CNTRL_resend_limit_OFFSET            8
#define L1_PM_STALL_RETRY_CNTRL_resend_limit_MASK              0x3ff00

#define L1_PM_STALL_RETRY_CNTRL_Reserved_31_18_OFFSET          18
#define L1_PM_STALL_RETRY_CNTRL_Reserved_31_18_MASK            0xfffc0000

typedef union {
  struct {
    UINT32                                         resend_freq:8;
    UINT32                                        resend_limit:10;
    UINT32                                      Reserved_31_18:14;
  } Field;
  UINT32 Value;
} L1_PM_STALL_RETRY_CNTRL_STRUCT;

#define SMN_L1_PM_STALL_RETRY_CNTRL_ADDRESS                           0x15300240UL
#define SMN_IOMMU0IOAGRNBIO0_L1_PM_STALL_RETRY_CNTRL_ADDRESS          0x15300240UL
#define SMN_IOMMU0IOAGRNBIO1_L1_PM_STALL_RETRY_CNTRL_ADDRESS          0x15500240UL
#define SMN_IOMMU0PCIE0NBIO0_L1_PM_STALL_RETRY_CNTRL_ADDRESS          0x14700240UL
#define SMN_IOMMU0PCIE0NBIO1_L1_PM_STALL_RETRY_CNTRL_ADDRESS          0x14900240UL
#define SMN_IOMMU0PCIE1NBIO0_L1_PM_STALL_RETRY_CNTRL_ADDRESS          0x14b00240UL
#define SMN_IOMMU0PCIE1NBIO1_L1_PM_STALL_RETRY_CNTRL_ADDRESS          0x14d00240UL
#define SMN_IOMMU1IOAGRNBIO0_L1_PM_STALL_RETRY_CNTRL_ADDRESS          0x15400240UL
#define SMN_IOMMU1IOAGRNBIO1_L1_PM_STALL_RETRY_CNTRL_ADDRESS          0x15600240UL
#define SMN_IOMMU1PCIE0NBIO0_L1_PM_STALL_RETRY_CNTRL_ADDRESS          0x14800240UL
#define SMN_IOMMU1PCIE0NBIO1_L1_PM_STALL_RETRY_CNTRL_ADDRESS          0x14a00240UL
#define SMN_IOMMU1PCIE1NBIO0_L1_PM_STALL_RETRY_CNTRL_ADDRESS          0x14c00240UL
#define SMN_IOMMU1PCIE1NBIO1_L1_PM_STALL_RETRY_CNTRL_ADDRESS          0x14e00240UL


/***********************************************************
* Register Name : L1_QOS_VC_CTRL_0
************************************************************/

#define L1_QOS_VC_CTRL_0_REG_qos_tw_vc_OFFSET                  0
#define L1_QOS_VC_CTRL_0_REG_qos_tw_vc_MASK                    0xff

#define L1_QOS_VC_CTRL_0_REG_qos_order_vc_OFFSET               8
#define L1_QOS_VC_CTRL_0_REG_qos_order_vc_MASK                 0xff00

#define L1_QOS_VC_CTRL_0_Reserved_31_16_OFFSET                 16
#define L1_QOS_VC_CTRL_0_Reserved_31_16_MASK                   0xffff0000

typedef union {
  struct {
    UINT32                                       REG_qos_tw_vc:8;
    UINT32                                    REG_qos_order_vc:8;
    UINT32                                      Reserved_31_16:16;
  } Field;
  UINT32 Value;
} L1_QOS_VC_CTRL_0_STRUCT;

#define SMN_L1_QOS_VC_CTRL_0_ADDRESS                                  0x15300198UL
#define SMN_IOMMU0IOAGRNBIO0_L1_QOS_VC_CTRL_0_ADDRESS                 0x15300198UL
#define SMN_IOMMU0IOAGRNBIO1_L1_QOS_VC_CTRL_0_ADDRESS                 0x15500198UL
#define SMN_IOMMU0PCIE0NBIO0_L1_QOS_VC_CTRL_0_ADDRESS                 0x14700198UL
#define SMN_IOMMU0PCIE0NBIO1_L1_QOS_VC_CTRL_0_ADDRESS                 0x14900198UL
#define SMN_IOMMU0PCIE1NBIO0_L1_QOS_VC_CTRL_0_ADDRESS                 0x14b00198UL
#define SMN_IOMMU0PCIE1NBIO1_L1_QOS_VC_CTRL_0_ADDRESS                 0x14d00198UL
#define SMN_IOMMU1IOAGRNBIO0_L1_QOS_VC_CTRL_0_ADDRESS                 0x15400198UL
#define SMN_IOMMU1IOAGRNBIO1_L1_QOS_VC_CTRL_0_ADDRESS                 0x15600198UL
#define SMN_IOMMU1PCIE0NBIO0_L1_QOS_VC_CTRL_0_ADDRESS                 0x14800198UL
#define SMN_IOMMU1PCIE0NBIO1_L1_QOS_VC_CTRL_0_ADDRESS                 0x14a00198UL
#define SMN_IOMMU1PCIE1NBIO0_L1_QOS_VC_CTRL_0_ADDRESS                 0x14c00198UL
#define SMN_IOMMU1PCIE1NBIO1_L1_QOS_VC_CTRL_0_ADDRESS                 0x14e00198UL


/***********************************************************
* Register Name : L1_SB_LOCATION
************************************************************/

#define L1_SB_LOCATION_SBlocated_Port_OFFSET                   0
#define L1_SB_LOCATION_SBlocated_Port_MASK                     0xffff

#define L1_SB_LOCATION_SBlocated_Core_OFFSET                   16
#define L1_SB_LOCATION_SBlocated_Core_MASK                     0xffff0000

typedef union {
  struct {
    UINT32                                      SBlocated_Port:16;
    UINT32                                      SBlocated_Core:16;
  } Field;
  UINT32 Value;
} L1_SB_LOCATION_STRUCT;

#define SMN_L1_SB_LOCATION_ADDRESS                                    0x15300024UL
#define SMN_IOMMU0IOAGRNBIO0_L1_SB_LOCATION_ADDRESS                   0x15300024UL
#define SMN_IOMMU0IOAGRNBIO1_L1_SB_LOCATION_ADDRESS                   0x15500024UL
#define SMN_IOMMU0PCIE0NBIO0_L1_SB_LOCATION_ADDRESS                   0x14700024UL
#define SMN_IOMMU0PCIE0NBIO1_L1_SB_LOCATION_ADDRESS                   0x14900024UL
#define SMN_IOMMU0PCIE1NBIO0_L1_SB_LOCATION_ADDRESS                   0x14b00024UL
#define SMN_IOMMU0PCIE1NBIO1_L1_SB_LOCATION_ADDRESS                   0x14d00024UL
#define SMN_IOMMU1IOAGRNBIO0_L1_SB_LOCATION_ADDRESS                   0x15400024UL
#define SMN_IOMMU1IOAGRNBIO1_L1_SB_LOCATION_ADDRESS                   0x15600024UL
#define SMN_IOMMU1PCIE0NBIO0_L1_SB_LOCATION_ADDRESS                   0x14800024UL
#define SMN_IOMMU1PCIE0NBIO1_L1_SB_LOCATION_ADDRESS                   0x14a00024UL
#define SMN_IOMMU1PCIE1NBIO0_L1_SB_LOCATION_ADDRESS                   0x14c00024UL
#define SMN_IOMMU1PCIE1NBIO1_L1_SB_LOCATION_ADDRESS                   0x14e00024UL


/***********************************************************
* Register Name : L1_SDP_CLKREQ_CNTRL
************************************************************/

#define L1_SDP_CLKREQ_CNTRL_HW_PG_WAKEUP_EN_DMA_OFFSET         0
#define L1_SDP_CLKREQ_CNTRL_HW_PG_WAKEUP_EN_DMA_MASK           0x1

#define L1_SDP_CLKREQ_CNTRL_HW_PG_WAKEUP_EN_HOST_OFFSET        1
#define L1_SDP_CLKREQ_CNTRL_HW_PG_WAKEUP_EN_HOST_MASK          0x2

#define L1_SDP_CLKREQ_CNTRL_HW_PG_WAKEUP_EN_DMA_RSP_OFFSET     2
#define L1_SDP_CLKREQ_CNTRL_HW_PG_WAKEUP_EN_DMA_RSP_MASK       0x4

#define L1_SDP_CLKREQ_CNTRL_HW_PG_WAKEUP_EN_HOST_RSP_OFFSET    3
#define L1_SDP_CLKREQ_CNTRL_HW_PG_WAKEUP_EN_HOST_RSP_MASK      0x8

#define L1_SDP_CLKREQ_CNTRL_NBIO_DS_IGNORE_PCTRL_OFFSET        4
#define L1_SDP_CLKREQ_CNTRL_NBIO_DS_IGNORE_PCTRL_MASK          0x10

#define L1_SDP_CLKREQ_CNTRL_Reserved_31_5_OFFSET               5
#define L1_SDP_CLKREQ_CNTRL_Reserved_31_5_MASK                 0xffffffe0

typedef union {
  struct {
    UINT32                                 HW_PG_WAKEUP_EN_DMA:1;
    UINT32                                HW_PG_WAKEUP_EN_HOST:1;
    UINT32                             HW_PG_WAKEUP_EN_DMA_RSP:1;
    UINT32                            HW_PG_WAKEUP_EN_HOST_RSP:1;
    UINT32                                NBIO_DS_IGNORE_PCTRL:1;
    UINT32                                       Reserved_31_5:27;
  } Field;
  UINT32 Value;
} L1_SDP_CLKREQ_CNTRL_STRUCT;

#define SMN_L1_SDP_CLKREQ_CNTRL_ADDRESS                               0x153000d4UL
#define SMN_IOMMU0IOAGRNBIO0_L1_SDP_CLKREQ_CNTRL_ADDRESS              0x153000d4UL
#define SMN_IOMMU0IOAGRNBIO1_L1_SDP_CLKREQ_CNTRL_ADDRESS              0x155000d4UL
#define SMN_IOMMU0PCIE0NBIO0_L1_SDP_CLKREQ_CNTRL_ADDRESS              0x147000d4UL
#define SMN_IOMMU0PCIE0NBIO1_L1_SDP_CLKREQ_CNTRL_ADDRESS              0x149000d4UL
#define SMN_IOMMU0PCIE1NBIO0_L1_SDP_CLKREQ_CNTRL_ADDRESS              0x14b000d4UL
#define SMN_IOMMU0PCIE1NBIO1_L1_SDP_CLKREQ_CNTRL_ADDRESS              0x14d000d4UL
#define SMN_IOMMU1IOAGRNBIO0_L1_SDP_CLKREQ_CNTRL_ADDRESS              0x154000d4UL
#define SMN_IOMMU1IOAGRNBIO1_L1_SDP_CLKREQ_CNTRL_ADDRESS              0x156000d4UL
#define SMN_IOMMU1PCIE0NBIO0_L1_SDP_CLKREQ_CNTRL_ADDRESS              0x148000d4UL
#define SMN_IOMMU1PCIE0NBIO1_L1_SDP_CLKREQ_CNTRL_ADDRESS              0x14a000d4UL
#define SMN_IOMMU1PCIE1NBIO0_L1_SDP_CLKREQ_CNTRL_ADDRESS              0x14c000d4UL
#define SMN_IOMMU1PCIE1NBIO1_L1_SDP_CLKREQ_CNTRL_ADDRESS              0x14e000d4UL


/***********************************************************
* Register Name : L1_SDP_DMA_DATA_CREDIT_0
************************************************************/

#define L1_SDP_DMA_DATA_CREDIT_0_VC0_DMA_DATA_CREDIT_OFFSET    0
#define L1_SDP_DMA_DATA_CREDIT_0_VC0_DMA_DATA_CREDIT_MASK      0xff

#define L1_SDP_DMA_DATA_CREDIT_0_VC1_DMA_DATA_CREDIT_OFFSET    8
#define L1_SDP_DMA_DATA_CREDIT_0_VC1_DMA_DATA_CREDIT_MASK      0xff00

#define L1_SDP_DMA_DATA_CREDIT_0_VC2_DMA_DATA_CREDIT_OFFSET    16
#define L1_SDP_DMA_DATA_CREDIT_0_VC2_DMA_DATA_CREDIT_MASK      0xff0000

#define L1_SDP_DMA_DATA_CREDIT_0_VC3_DMA_DATA_CREDIT_OFFSET    24
#define L1_SDP_DMA_DATA_CREDIT_0_VC3_DMA_DATA_CREDIT_MASK      0xff000000

typedef union {
  struct {
    UINT32                                 VC0_DMA_DATA_CREDIT:8;
    UINT32                                 VC1_DMA_DATA_CREDIT:8;
    UINT32                                 VC2_DMA_DATA_CREDIT:8;
    UINT32                                 VC3_DMA_DATA_CREDIT:8;
  } Field;
  UINT32 Value;
} L1_SDP_DMA_DATA_CREDIT_0_STRUCT;

#define SMN_L1_SDP_DMA_DATA_CREDIT_0_ADDRESS                          0x1530011cUL
#define SMN_IOMMU0IOAGRNBIO0_L1_SDP_DMA_DATA_CREDIT_0_ADDRESS         0x1530011cUL
#define SMN_IOMMU0IOAGRNBIO1_L1_SDP_DMA_DATA_CREDIT_0_ADDRESS         0x1550011cUL
#define SMN_IOMMU0PCIE0NBIO0_L1_SDP_DMA_DATA_CREDIT_0_ADDRESS         0x1470011cUL
#define SMN_IOMMU0PCIE0NBIO1_L1_SDP_DMA_DATA_CREDIT_0_ADDRESS         0x1490011cUL
#define SMN_IOMMU0PCIE1NBIO0_L1_SDP_DMA_DATA_CREDIT_0_ADDRESS         0x14b0011cUL
#define SMN_IOMMU0PCIE1NBIO1_L1_SDP_DMA_DATA_CREDIT_0_ADDRESS         0x14d0011cUL
#define SMN_IOMMU1IOAGRNBIO0_L1_SDP_DMA_DATA_CREDIT_0_ADDRESS         0x1540011cUL
#define SMN_IOMMU1IOAGRNBIO1_L1_SDP_DMA_DATA_CREDIT_0_ADDRESS         0x1560011cUL
#define SMN_IOMMU1PCIE0NBIO0_L1_SDP_DMA_DATA_CREDIT_0_ADDRESS         0x1480011cUL
#define SMN_IOMMU1PCIE0NBIO1_L1_SDP_DMA_DATA_CREDIT_0_ADDRESS         0x14a0011cUL
#define SMN_IOMMU1PCIE1NBIO0_L1_SDP_DMA_DATA_CREDIT_0_ADDRESS         0x14c0011cUL
#define SMN_IOMMU1PCIE1NBIO1_L1_SDP_DMA_DATA_CREDIT_0_ADDRESS         0x14e0011cUL


/***********************************************************
* Register Name : L1_SDP_DMA_DATA_CREDIT_1
************************************************************/

#define L1_SDP_DMA_DATA_CREDIT_1_VC4_DMA_DATA_CREDIT_OFFSET    0
#define L1_SDP_DMA_DATA_CREDIT_1_VC4_DMA_DATA_CREDIT_MASK      0xff

#define L1_SDP_DMA_DATA_CREDIT_1_VC5_DMA_DATA_CREDIT_OFFSET    8
#define L1_SDP_DMA_DATA_CREDIT_1_VC5_DMA_DATA_CREDIT_MASK      0xff00

#define L1_SDP_DMA_DATA_CREDIT_1_VC6_DMA_DATA_CREDIT_OFFSET    16
#define L1_SDP_DMA_DATA_CREDIT_1_VC6_DMA_DATA_CREDIT_MASK      0xff0000

#define L1_SDP_DMA_DATA_CREDIT_1_VC7_DMA_DATA_CREDIT_OFFSET    24
#define L1_SDP_DMA_DATA_CREDIT_1_VC7_DMA_DATA_CREDIT_MASK      0xff000000

typedef union {
  struct {
    UINT32                                 VC4_DMA_DATA_CREDIT:8;
    UINT32                                 VC5_DMA_DATA_CREDIT:8;
    UINT32                                 VC6_DMA_DATA_CREDIT:8;
    UINT32                                 VC7_DMA_DATA_CREDIT:8;
  } Field;
  UINT32 Value;
} L1_SDP_DMA_DATA_CREDIT_1_STRUCT;

#define SMN_L1_SDP_DMA_DATA_CREDIT_1_ADDRESS                          0x15300120UL
#define SMN_IOMMU0IOAGRNBIO0_L1_SDP_DMA_DATA_CREDIT_1_ADDRESS         0x15300120UL
#define SMN_IOMMU0IOAGRNBIO1_L1_SDP_DMA_DATA_CREDIT_1_ADDRESS         0x15500120UL
#define SMN_IOMMU0PCIE0NBIO0_L1_SDP_DMA_DATA_CREDIT_1_ADDRESS         0x14700120UL
#define SMN_IOMMU0PCIE0NBIO1_L1_SDP_DMA_DATA_CREDIT_1_ADDRESS         0x14900120UL
#define SMN_IOMMU0PCIE1NBIO0_L1_SDP_DMA_DATA_CREDIT_1_ADDRESS         0x14b00120UL
#define SMN_IOMMU0PCIE1NBIO1_L1_SDP_DMA_DATA_CREDIT_1_ADDRESS         0x14d00120UL
#define SMN_IOMMU1IOAGRNBIO0_L1_SDP_DMA_DATA_CREDIT_1_ADDRESS         0x15400120UL
#define SMN_IOMMU1IOAGRNBIO1_L1_SDP_DMA_DATA_CREDIT_1_ADDRESS         0x15600120UL
#define SMN_IOMMU1PCIE0NBIO0_L1_SDP_DMA_DATA_CREDIT_1_ADDRESS         0x14800120UL
#define SMN_IOMMU1PCIE0NBIO1_L1_SDP_DMA_DATA_CREDIT_1_ADDRESS         0x14a00120UL
#define SMN_IOMMU1PCIE1NBIO0_L1_SDP_DMA_DATA_CREDIT_1_ADDRESS         0x14c00120UL
#define SMN_IOMMU1PCIE1NBIO1_L1_SDP_DMA_DATA_CREDIT_1_ADDRESS         0x14e00120UL


/***********************************************************
* Register Name : L1_SDP_DMA_RDRSP_CREDIT
************************************************************/

#define L1_SDP_DMA_RDRSP_CREDIT_VC0_DMA_RDRSP_CREDIT_OFFSET    0
#define L1_SDP_DMA_RDRSP_CREDIT_VC0_DMA_RDRSP_CREDIT_MASK      0xf

#define L1_SDP_DMA_RDRSP_CREDIT_VC1_DMA_RDRSP_CREDIT_OFFSET    4
#define L1_SDP_DMA_RDRSP_CREDIT_VC1_DMA_RDRSP_CREDIT_MASK      0xf0

#define L1_SDP_DMA_RDRSP_CREDIT_VC2_DMA_RDRSP_CREDIT_OFFSET    8
#define L1_SDP_DMA_RDRSP_CREDIT_VC2_DMA_RDRSP_CREDIT_MASK      0xf00

#define L1_SDP_DMA_RDRSP_CREDIT_VC3_DMA_RDRSP_CREDIT_OFFSET    12
#define L1_SDP_DMA_RDRSP_CREDIT_VC3_DMA_RDRSP_CREDIT_MASK      0xf000

#define L1_SDP_DMA_RDRSP_CREDIT_VC4_DMA_RDRSP_CREDIT_OFFSET    16
#define L1_SDP_DMA_RDRSP_CREDIT_VC4_DMA_RDRSP_CREDIT_MASK      0xf0000

#define L1_SDP_DMA_RDRSP_CREDIT_VC5_DMA_RDRSP_CREDIT_OFFSET    20
#define L1_SDP_DMA_RDRSP_CREDIT_VC5_DMA_RDRSP_CREDIT_MASK      0xf00000

#define L1_SDP_DMA_RDRSP_CREDIT_VC6_DMA_RDRSP_CREDIT_OFFSET    24
#define L1_SDP_DMA_RDRSP_CREDIT_VC6_DMA_RDRSP_CREDIT_MASK      0xf000000

#define L1_SDP_DMA_RDRSP_CREDIT_VC7_DMA_RDRSP_CREDIT_OFFSET    28
#define L1_SDP_DMA_RDRSP_CREDIT_VC7_DMA_RDRSP_CREDIT_MASK      0xf0000000

typedef union {
  struct {
    UINT32                                VC0_DMA_RDRSP_CREDIT:4;
    UINT32                                VC1_DMA_RDRSP_CREDIT:4;
    UINT32                                VC2_DMA_RDRSP_CREDIT:4;
    UINT32                                VC3_DMA_RDRSP_CREDIT:4;
    UINT32                                VC4_DMA_RDRSP_CREDIT:4;
    UINT32                                VC5_DMA_RDRSP_CREDIT:4;
    UINT32                                VC6_DMA_RDRSP_CREDIT:4;
    UINT32                                VC7_DMA_RDRSP_CREDIT:4;
  } Field;
  UINT32 Value;
} L1_SDP_DMA_RDRSP_CREDIT_STRUCT;

#define SMN_L1_SDP_DMA_RDRSP_CREDIT_ADDRESS                           0x15300124UL
#define SMN_IOMMU0IOAGRNBIO0_L1_SDP_DMA_RDRSP_CREDIT_ADDRESS          0x15300124UL
#define SMN_IOMMU0IOAGRNBIO1_L1_SDP_DMA_RDRSP_CREDIT_ADDRESS          0x15500124UL
#define SMN_IOMMU0PCIE0NBIO0_L1_SDP_DMA_RDRSP_CREDIT_ADDRESS          0x14700124UL
#define SMN_IOMMU0PCIE0NBIO1_L1_SDP_DMA_RDRSP_CREDIT_ADDRESS          0x14900124UL
#define SMN_IOMMU0PCIE1NBIO0_L1_SDP_DMA_RDRSP_CREDIT_ADDRESS          0x14b00124UL
#define SMN_IOMMU0PCIE1NBIO1_L1_SDP_DMA_RDRSP_CREDIT_ADDRESS          0x14d00124UL
#define SMN_IOMMU1IOAGRNBIO0_L1_SDP_DMA_RDRSP_CREDIT_ADDRESS          0x15400124UL
#define SMN_IOMMU1IOAGRNBIO1_L1_SDP_DMA_RDRSP_CREDIT_ADDRESS          0x15600124UL
#define SMN_IOMMU1PCIE0NBIO0_L1_SDP_DMA_RDRSP_CREDIT_ADDRESS          0x14800124UL
#define SMN_IOMMU1PCIE0NBIO1_L1_SDP_DMA_RDRSP_CREDIT_ADDRESS          0x14a00124UL
#define SMN_IOMMU1PCIE1NBIO0_L1_SDP_DMA_RDRSP_CREDIT_ADDRESS          0x14c00124UL
#define SMN_IOMMU1PCIE1NBIO1_L1_SDP_DMA_RDRSP_CREDIT_ADDRESS          0x14e00124UL


/***********************************************************
* Register Name : L1_SDP_DMA_REQ_CREDIT_0
************************************************************/

#define L1_SDP_DMA_REQ_CREDIT_0_VC0_DMA_REQ_CREDIT_OFFSET      0
#define L1_SDP_DMA_REQ_CREDIT_0_VC0_DMA_REQ_CREDIT_MASK        0xff

#define L1_SDP_DMA_REQ_CREDIT_0_VC1_DMA_REQ_CREDIT_OFFSET      8
#define L1_SDP_DMA_REQ_CREDIT_0_VC1_DMA_REQ_CREDIT_MASK        0xff00

#define L1_SDP_DMA_REQ_CREDIT_0_VC2_DMA_REQ_CREDIT_OFFSET      16
#define L1_SDP_DMA_REQ_CREDIT_0_VC2_DMA_REQ_CREDIT_MASK        0xff0000

#define L1_SDP_DMA_REQ_CREDIT_0_VC3_DMA_REQ_CREDIT_OFFSET      24
#define L1_SDP_DMA_REQ_CREDIT_0_VC3_DMA_REQ_CREDIT_MASK        0xff000000

typedef union {
  struct {
    UINT32                                  VC0_DMA_REQ_CREDIT:8;
    UINT32                                  VC1_DMA_REQ_CREDIT:8;
    UINT32                                  VC2_DMA_REQ_CREDIT:8;
    UINT32                                  VC3_DMA_REQ_CREDIT:8;
  } Field;
  UINT32 Value;
} L1_SDP_DMA_REQ_CREDIT_0_STRUCT;

#define SMN_L1_SDP_DMA_REQ_CREDIT_0_ADDRESS                           0x15300114UL
#define SMN_IOMMU0IOAGRNBIO0_L1_SDP_DMA_REQ_CREDIT_0_ADDRESS          0x15300114UL
#define SMN_IOMMU0IOAGRNBIO1_L1_SDP_DMA_REQ_CREDIT_0_ADDRESS          0x15500114UL
#define SMN_IOMMU0PCIE0NBIO0_L1_SDP_DMA_REQ_CREDIT_0_ADDRESS          0x14700114UL
#define SMN_IOMMU0PCIE0NBIO1_L1_SDP_DMA_REQ_CREDIT_0_ADDRESS          0x14900114UL
#define SMN_IOMMU0PCIE1NBIO0_L1_SDP_DMA_REQ_CREDIT_0_ADDRESS          0x14b00114UL
#define SMN_IOMMU0PCIE1NBIO1_L1_SDP_DMA_REQ_CREDIT_0_ADDRESS          0x14d00114UL
#define SMN_IOMMU1IOAGRNBIO0_L1_SDP_DMA_REQ_CREDIT_0_ADDRESS          0x15400114UL
#define SMN_IOMMU1IOAGRNBIO1_L1_SDP_DMA_REQ_CREDIT_0_ADDRESS          0x15600114UL
#define SMN_IOMMU1PCIE0NBIO0_L1_SDP_DMA_REQ_CREDIT_0_ADDRESS          0x14800114UL
#define SMN_IOMMU1PCIE0NBIO1_L1_SDP_DMA_REQ_CREDIT_0_ADDRESS          0x14a00114UL
#define SMN_IOMMU1PCIE1NBIO0_L1_SDP_DMA_REQ_CREDIT_0_ADDRESS          0x14c00114UL
#define SMN_IOMMU1PCIE1NBIO1_L1_SDP_DMA_REQ_CREDIT_0_ADDRESS          0x14e00114UL


/***********************************************************
* Register Name : L1_SDP_DMA_REQ_CREDIT_1
************************************************************/

#define L1_SDP_DMA_REQ_CREDIT_1_VC4_DMA_REQ_CREDIT_OFFSET      0
#define L1_SDP_DMA_REQ_CREDIT_1_VC4_DMA_REQ_CREDIT_MASK        0xff

#define L1_SDP_DMA_REQ_CREDIT_1_VC5_DMA_REQ_CREDIT_OFFSET      8
#define L1_SDP_DMA_REQ_CREDIT_1_VC5_DMA_REQ_CREDIT_MASK        0xff00

#define L1_SDP_DMA_REQ_CREDIT_1_VC6_DMA_REQ_CREDIT_OFFSET      16
#define L1_SDP_DMA_REQ_CREDIT_1_VC6_DMA_REQ_CREDIT_MASK        0xff0000

#define L1_SDP_DMA_REQ_CREDIT_1_VC7_DMA_REQ_CREDIT_OFFSET      24
#define L1_SDP_DMA_REQ_CREDIT_1_VC7_DMA_REQ_CREDIT_MASK        0xff000000

typedef union {
  struct {
    UINT32                                  VC4_DMA_REQ_CREDIT:8;
    UINT32                                  VC5_DMA_REQ_CREDIT:8;
    UINT32                                  VC6_DMA_REQ_CREDIT:8;
    UINT32                                  VC7_DMA_REQ_CREDIT:8;
  } Field;
  UINT32 Value;
} L1_SDP_DMA_REQ_CREDIT_1_STRUCT;

#define SMN_L1_SDP_DMA_REQ_CREDIT_1_ADDRESS                           0x15300118UL
#define SMN_IOMMU0IOAGRNBIO0_L1_SDP_DMA_REQ_CREDIT_1_ADDRESS          0x15300118UL
#define SMN_IOMMU0IOAGRNBIO1_L1_SDP_DMA_REQ_CREDIT_1_ADDRESS          0x15500118UL
#define SMN_IOMMU0PCIE0NBIO0_L1_SDP_DMA_REQ_CREDIT_1_ADDRESS          0x14700118UL
#define SMN_IOMMU0PCIE0NBIO1_L1_SDP_DMA_REQ_CREDIT_1_ADDRESS          0x14900118UL
#define SMN_IOMMU0PCIE1NBIO0_L1_SDP_DMA_REQ_CREDIT_1_ADDRESS          0x14b00118UL
#define SMN_IOMMU0PCIE1NBIO1_L1_SDP_DMA_REQ_CREDIT_1_ADDRESS          0x14d00118UL
#define SMN_IOMMU1IOAGRNBIO0_L1_SDP_DMA_REQ_CREDIT_1_ADDRESS          0x15400118UL
#define SMN_IOMMU1IOAGRNBIO1_L1_SDP_DMA_REQ_CREDIT_1_ADDRESS          0x15600118UL
#define SMN_IOMMU1PCIE0NBIO0_L1_SDP_DMA_REQ_CREDIT_1_ADDRESS          0x14800118UL
#define SMN_IOMMU1PCIE0NBIO1_L1_SDP_DMA_REQ_CREDIT_1_ADDRESS          0x14a00118UL
#define SMN_IOMMU1PCIE1NBIO0_L1_SDP_DMA_REQ_CREDIT_1_ADDRESS          0x14c00118UL
#define SMN_IOMMU1PCIE1NBIO1_L1_SDP_DMA_REQ_CREDIT_1_ADDRESS          0x14e00118UL


/***********************************************************
* Register Name : L1_SDP_DMA_WRRSP_CREDIT
************************************************************/

#define L1_SDP_DMA_WRRSP_CREDIT_VC0_DMA_WRRSP_CREDIT_OFFSET    0
#define L1_SDP_DMA_WRRSP_CREDIT_VC0_DMA_WRRSP_CREDIT_MASK      0xf

#define L1_SDP_DMA_WRRSP_CREDIT_VC1_DMA_WRRSP_CREDIT_OFFSET    4
#define L1_SDP_DMA_WRRSP_CREDIT_VC1_DMA_WRRSP_CREDIT_MASK      0xf0

#define L1_SDP_DMA_WRRSP_CREDIT_VC2_DMA_WRRSP_CREDIT_OFFSET    8
#define L1_SDP_DMA_WRRSP_CREDIT_VC2_DMA_WRRSP_CREDIT_MASK      0xf00

#define L1_SDP_DMA_WRRSP_CREDIT_VC3_DMA_WRRSP_CREDIT_OFFSET    12
#define L1_SDP_DMA_WRRSP_CREDIT_VC3_DMA_WRRSP_CREDIT_MASK      0xf000

#define L1_SDP_DMA_WRRSP_CREDIT_VC4_DMA_WRRSP_CREDIT_OFFSET    16
#define L1_SDP_DMA_WRRSP_CREDIT_VC4_DMA_WRRSP_CREDIT_MASK      0xf0000

#define L1_SDP_DMA_WRRSP_CREDIT_VC5_DMA_WRRSP_CREDIT_OFFSET    20
#define L1_SDP_DMA_WRRSP_CREDIT_VC5_DMA_WRRSP_CREDIT_MASK      0xf00000

#define L1_SDP_DMA_WRRSP_CREDIT_VC6_DMA_WRRSP_CREDIT_OFFSET    24
#define L1_SDP_DMA_WRRSP_CREDIT_VC6_DMA_WRRSP_CREDIT_MASK      0xf000000

#define L1_SDP_DMA_WRRSP_CREDIT_VC7_DMA_WRRSP_CREDIT_OFFSET    28
#define L1_SDP_DMA_WRRSP_CREDIT_VC7_DMA_WRRSP_CREDIT_MASK      0xf0000000

typedef union {
  struct {
    UINT32                                VC0_DMA_WRRSP_CREDIT:4;
    UINT32                                VC1_DMA_WRRSP_CREDIT:4;
    UINT32                                VC2_DMA_WRRSP_CREDIT:4;
    UINT32                                VC3_DMA_WRRSP_CREDIT:4;
    UINT32                                VC4_DMA_WRRSP_CREDIT:4;
    UINT32                                VC5_DMA_WRRSP_CREDIT:4;
    UINT32                                VC6_DMA_WRRSP_CREDIT:4;
    UINT32                                VC7_DMA_WRRSP_CREDIT:4;
  } Field;
  UINT32 Value;
} L1_SDP_DMA_WRRSP_CREDIT_STRUCT;

#define SMN_L1_SDP_DMA_WRRSP_CREDIT_ADDRESS                           0x15300128UL
#define SMN_IOMMU0IOAGRNBIO0_L1_SDP_DMA_WRRSP_CREDIT_ADDRESS          0x15300128UL
#define SMN_IOMMU0IOAGRNBIO1_L1_SDP_DMA_WRRSP_CREDIT_ADDRESS          0x15500128UL
#define SMN_IOMMU0PCIE0NBIO0_L1_SDP_DMA_WRRSP_CREDIT_ADDRESS          0x14700128UL
#define SMN_IOMMU0PCIE0NBIO1_L1_SDP_DMA_WRRSP_CREDIT_ADDRESS          0x14900128UL
#define SMN_IOMMU0PCIE1NBIO0_L1_SDP_DMA_WRRSP_CREDIT_ADDRESS          0x14b00128UL
#define SMN_IOMMU0PCIE1NBIO1_L1_SDP_DMA_WRRSP_CREDIT_ADDRESS          0x14d00128UL
#define SMN_IOMMU1IOAGRNBIO0_L1_SDP_DMA_WRRSP_CREDIT_ADDRESS          0x15400128UL
#define SMN_IOMMU1IOAGRNBIO1_L1_SDP_DMA_WRRSP_CREDIT_ADDRESS          0x15600128UL
#define SMN_IOMMU1PCIE0NBIO0_L1_SDP_DMA_WRRSP_CREDIT_ADDRESS          0x14800128UL
#define SMN_IOMMU1PCIE0NBIO1_L1_SDP_DMA_WRRSP_CREDIT_ADDRESS          0x14a00128UL
#define SMN_IOMMU1PCIE1NBIO0_L1_SDP_DMA_WRRSP_CREDIT_ADDRESS          0x14c00128UL
#define SMN_IOMMU1PCIE1NBIO1_L1_SDP_DMA_WRRSP_CREDIT_ADDRESS          0x14e00128UL


/***********************************************************
* Register Name : L1_SDP_HOST_DATA_CREDIT
************************************************************/

#define L1_SDP_HOST_DATA_CREDIT_VC0_HOST_DATA_CREDIT_OFFSET    0
#define L1_SDP_HOST_DATA_CREDIT_VC0_HOST_DATA_CREDIT_MASK      0xf

#define L1_SDP_HOST_DATA_CREDIT_VC1_HOST_DATA_CREDIT_OFFSET    4
#define L1_SDP_HOST_DATA_CREDIT_VC1_HOST_DATA_CREDIT_MASK      0xf0

#define L1_SDP_HOST_DATA_CREDIT_VC2_HOST_DATA_CREDIT_OFFSET    8
#define L1_SDP_HOST_DATA_CREDIT_VC2_HOST_DATA_CREDIT_MASK      0xf00

#define L1_SDP_HOST_DATA_CREDIT_VC3_HOST_DATA_CREDIT_OFFSET    12
#define L1_SDP_HOST_DATA_CREDIT_VC3_HOST_DATA_CREDIT_MASK      0xf000

#define L1_SDP_HOST_DATA_CREDIT_VC4_HOST_DATA_CREDIT_OFFSET    16
#define L1_SDP_HOST_DATA_CREDIT_VC4_HOST_DATA_CREDIT_MASK      0xf0000

#define L1_SDP_HOST_DATA_CREDIT_VC5_HOST_DATA_CREDIT_OFFSET    20
#define L1_SDP_HOST_DATA_CREDIT_VC5_HOST_DATA_CREDIT_MASK      0xf00000

#define L1_SDP_HOST_DATA_CREDIT_VC6_HOST_DATA_CREDIT_OFFSET    24
#define L1_SDP_HOST_DATA_CREDIT_VC6_HOST_DATA_CREDIT_MASK      0xf000000

#define L1_SDP_HOST_DATA_CREDIT_VC7_HOST_DATA_CREDIT_OFFSET    28
#define L1_SDP_HOST_DATA_CREDIT_VC7_HOST_DATA_CREDIT_MASK      0xf0000000

typedef union {
  struct {
    UINT32                                VC0_HOST_DATA_CREDIT:4;
    UINT32                                VC1_HOST_DATA_CREDIT:4;
    UINT32                                VC2_HOST_DATA_CREDIT:4;
    UINT32                                VC3_HOST_DATA_CREDIT:4;
    UINT32                                VC4_HOST_DATA_CREDIT:4;
    UINT32                                VC5_HOST_DATA_CREDIT:4;
    UINT32                                VC6_HOST_DATA_CREDIT:4;
    UINT32                                VC7_HOST_DATA_CREDIT:4;
  } Field;
  UINT32 Value;
} L1_SDP_HOST_DATA_CREDIT_STRUCT;

#define SMN_L1_SDP_HOST_DATA_CREDIT_ADDRESS                           0x15300130UL
#define SMN_IOMMU0IOAGRNBIO0_L1_SDP_HOST_DATA_CREDIT_ADDRESS          0x15300130UL
#define SMN_IOMMU0IOAGRNBIO1_L1_SDP_HOST_DATA_CREDIT_ADDRESS          0x15500130UL
#define SMN_IOMMU0PCIE0NBIO0_L1_SDP_HOST_DATA_CREDIT_ADDRESS          0x14700130UL
#define SMN_IOMMU0PCIE0NBIO1_L1_SDP_HOST_DATA_CREDIT_ADDRESS          0x14900130UL
#define SMN_IOMMU0PCIE1NBIO0_L1_SDP_HOST_DATA_CREDIT_ADDRESS          0x14b00130UL
#define SMN_IOMMU0PCIE1NBIO1_L1_SDP_HOST_DATA_CREDIT_ADDRESS          0x14d00130UL
#define SMN_IOMMU1IOAGRNBIO0_L1_SDP_HOST_DATA_CREDIT_ADDRESS          0x15400130UL
#define SMN_IOMMU1IOAGRNBIO1_L1_SDP_HOST_DATA_CREDIT_ADDRESS          0x15600130UL
#define SMN_IOMMU1PCIE0NBIO0_L1_SDP_HOST_DATA_CREDIT_ADDRESS          0x14800130UL
#define SMN_IOMMU1PCIE0NBIO1_L1_SDP_HOST_DATA_CREDIT_ADDRESS          0x14a00130UL
#define SMN_IOMMU1PCIE1NBIO0_L1_SDP_HOST_DATA_CREDIT_ADDRESS          0x14c00130UL
#define SMN_IOMMU1PCIE1NBIO1_L1_SDP_HOST_DATA_CREDIT_ADDRESS          0x14e00130UL


/***********************************************************
* Register Name : L1_SDP_HOST_RDRSP_CREDIT
************************************************************/

#define L1_SDP_HOST_RDRSP_CREDIT_Reserved_31_0_OFFSET          0
#define L1_SDP_HOST_RDRSP_CREDIT_Reserved_31_0_MASK            0xffffffff

typedef union {
  struct {
    UINT32                                       Reserved_31_0:32;
  } Field;
  UINT32 Value;
} L1_SDP_HOST_RDRSP_CREDIT_STRUCT;

#define SMN_L1_SDP_HOST_RDRSP_CREDIT_ADDRESS                          0x1530017cUL
#define SMN_IOMMU0IOAGRNBIO0_L1_SDP_HOST_RDRSP_CREDIT_ADDRESS         0x1530017cUL
#define SMN_IOMMU0IOAGRNBIO1_L1_SDP_HOST_RDRSP_CREDIT_ADDRESS         0x1550017cUL
#define SMN_IOMMU0PCIE0NBIO0_L1_SDP_HOST_RDRSP_CREDIT_ADDRESS         0x1470017cUL
#define SMN_IOMMU0PCIE0NBIO1_L1_SDP_HOST_RDRSP_CREDIT_ADDRESS         0x1490017cUL
#define SMN_IOMMU0PCIE1NBIO0_L1_SDP_HOST_RDRSP_CREDIT_ADDRESS         0x14b0017cUL
#define SMN_IOMMU0PCIE1NBIO1_L1_SDP_HOST_RDRSP_CREDIT_ADDRESS         0x14d0017cUL
#define SMN_IOMMU1IOAGRNBIO0_L1_SDP_HOST_RDRSP_CREDIT_ADDRESS         0x1540017cUL
#define SMN_IOMMU1IOAGRNBIO1_L1_SDP_HOST_RDRSP_CREDIT_ADDRESS         0x1560017cUL
#define SMN_IOMMU1PCIE0NBIO0_L1_SDP_HOST_RDRSP_CREDIT_ADDRESS         0x1480017cUL
#define SMN_IOMMU1PCIE0NBIO1_L1_SDP_HOST_RDRSP_CREDIT_ADDRESS         0x14a0017cUL
#define SMN_IOMMU1PCIE1NBIO0_L1_SDP_HOST_RDRSP_CREDIT_ADDRESS         0x14c0017cUL
#define SMN_IOMMU1PCIE1NBIO1_L1_SDP_HOST_RDRSP_CREDIT_ADDRESS         0x14e0017cUL


/***********************************************************
* Register Name : L1_SDP_HOST_REQ_CREDIT
************************************************************/

#define L1_SDP_HOST_REQ_CREDIT_VC0_HOST_REQ_CREDIT_OFFSET      0
#define L1_SDP_HOST_REQ_CREDIT_VC0_HOST_REQ_CREDIT_MASK        0xf

#define L1_SDP_HOST_REQ_CREDIT_VC1_HOST_REQ_CREDIT_OFFSET      4
#define L1_SDP_HOST_REQ_CREDIT_VC1_HOST_REQ_CREDIT_MASK        0xf0

#define L1_SDP_HOST_REQ_CREDIT_VC2_HOST_REQ_CREDIT_OFFSET      8
#define L1_SDP_HOST_REQ_CREDIT_VC2_HOST_REQ_CREDIT_MASK        0xf00

#define L1_SDP_HOST_REQ_CREDIT_VC3_HOST_REQ_CREDIT_OFFSET      12
#define L1_SDP_HOST_REQ_CREDIT_VC3_HOST_REQ_CREDIT_MASK        0xf000

#define L1_SDP_HOST_REQ_CREDIT_VC4_HOST_REQ_CREDIT_OFFSET      16
#define L1_SDP_HOST_REQ_CREDIT_VC4_HOST_REQ_CREDIT_MASK        0xf0000

#define L1_SDP_HOST_REQ_CREDIT_VC5_HOST_REQ_CREDIT_OFFSET      20
#define L1_SDP_HOST_REQ_CREDIT_VC5_HOST_REQ_CREDIT_MASK        0xf00000

#define L1_SDP_HOST_REQ_CREDIT_VC6_HOST_REQ_CREDIT_OFFSET      24
#define L1_SDP_HOST_REQ_CREDIT_VC6_HOST_REQ_CREDIT_MASK        0xf000000

#define L1_SDP_HOST_REQ_CREDIT_VC7_HOST_REQ_CREDIT_OFFSET      28
#define L1_SDP_HOST_REQ_CREDIT_VC7_HOST_REQ_CREDIT_MASK        0xf0000000

typedef union {
  struct {
    UINT32                                 VC0_HOST_REQ_CREDIT:4;
    UINT32                                 VC1_HOST_REQ_CREDIT:4;
    UINT32                                 VC2_HOST_REQ_CREDIT:4;
    UINT32                                 VC3_HOST_REQ_CREDIT:4;
    UINT32                                 VC4_HOST_REQ_CREDIT:4;
    UINT32                                 VC5_HOST_REQ_CREDIT:4;
    UINT32                                 VC6_HOST_REQ_CREDIT:4;
    UINT32                                 VC7_HOST_REQ_CREDIT:4;
  } Field;
  UINT32 Value;
} L1_SDP_HOST_REQ_CREDIT_STRUCT;

#define SMN_L1_SDP_HOST_REQ_CREDIT_ADDRESS                            0x1530012cUL
#define SMN_IOMMU0IOAGRNBIO0_L1_SDP_HOST_REQ_CREDIT_ADDRESS           0x1530012cUL
#define SMN_IOMMU0IOAGRNBIO1_L1_SDP_HOST_REQ_CREDIT_ADDRESS           0x1550012cUL
#define SMN_IOMMU0PCIE0NBIO0_L1_SDP_HOST_REQ_CREDIT_ADDRESS           0x1470012cUL
#define SMN_IOMMU0PCIE0NBIO1_L1_SDP_HOST_REQ_CREDIT_ADDRESS           0x1490012cUL
#define SMN_IOMMU0PCIE1NBIO0_L1_SDP_HOST_REQ_CREDIT_ADDRESS           0x14b0012cUL
#define SMN_IOMMU0PCIE1NBIO1_L1_SDP_HOST_REQ_CREDIT_ADDRESS           0x14d0012cUL
#define SMN_IOMMU1IOAGRNBIO0_L1_SDP_HOST_REQ_CREDIT_ADDRESS           0x1540012cUL
#define SMN_IOMMU1IOAGRNBIO1_L1_SDP_HOST_REQ_CREDIT_ADDRESS           0x1560012cUL
#define SMN_IOMMU1PCIE0NBIO0_L1_SDP_HOST_REQ_CREDIT_ADDRESS           0x1480012cUL
#define SMN_IOMMU1PCIE0NBIO1_L1_SDP_HOST_REQ_CREDIT_ADDRESS           0x14a0012cUL
#define SMN_IOMMU1PCIE1NBIO0_L1_SDP_HOST_REQ_CREDIT_ADDRESS           0x14c0012cUL
#define SMN_IOMMU1PCIE1NBIO1_L1_SDP_HOST_REQ_CREDIT_ADDRESS           0x14e0012cUL


/***********************************************************
* Register Name : L1_SDP_HOST_WRRSP_CREDIT
************************************************************/

#define L1_SDP_HOST_WRRSP_CREDIT_VC0_HOST_WRRSP_CREDIT_OFFSET  0
#define L1_SDP_HOST_WRRSP_CREDIT_VC0_HOST_WRRSP_CREDIT_MASK    0xf

#define L1_SDP_HOST_WRRSP_CREDIT_VC1_HOST_WRRSP_CREDIT_OFFSET  4
#define L1_SDP_HOST_WRRSP_CREDIT_VC1_HOST_WRRSP_CREDIT_MASK    0xf0

#define L1_SDP_HOST_WRRSP_CREDIT_Reserved_31_8_OFFSET          8
#define L1_SDP_HOST_WRRSP_CREDIT_Reserved_31_8_MASK            0xffffff00

typedef union {
  struct {
    UINT32                               VC0_HOST_WRRSP_CREDIT:4;
    UINT32                               VC1_HOST_WRRSP_CREDIT:4;
    UINT32                                       Reserved_31_8:24;
  } Field;
  UINT32 Value;
} L1_SDP_HOST_WRRSP_CREDIT_STRUCT;

#define SMN_L1_SDP_HOST_WRRSP_CREDIT_ADDRESS                          0x15300178UL
#define SMN_IOMMU0IOAGRNBIO0_L1_SDP_HOST_WRRSP_CREDIT_ADDRESS         0x15300178UL
#define SMN_IOMMU0IOAGRNBIO1_L1_SDP_HOST_WRRSP_CREDIT_ADDRESS         0x15500178UL
#define SMN_IOMMU0PCIE0NBIO0_L1_SDP_HOST_WRRSP_CREDIT_ADDRESS         0x14700178UL
#define SMN_IOMMU0PCIE0NBIO1_L1_SDP_HOST_WRRSP_CREDIT_ADDRESS         0x14900178UL
#define SMN_IOMMU0PCIE1NBIO0_L1_SDP_HOST_WRRSP_CREDIT_ADDRESS         0x14b00178UL
#define SMN_IOMMU0PCIE1NBIO1_L1_SDP_HOST_WRRSP_CREDIT_ADDRESS         0x14d00178UL
#define SMN_IOMMU1IOAGRNBIO0_L1_SDP_HOST_WRRSP_CREDIT_ADDRESS         0x15400178UL
#define SMN_IOMMU1IOAGRNBIO1_L1_SDP_HOST_WRRSP_CREDIT_ADDRESS         0x15600178UL
#define SMN_IOMMU1PCIE0NBIO0_L1_SDP_HOST_WRRSP_CREDIT_ADDRESS         0x14800178UL
#define SMN_IOMMU1PCIE0NBIO1_L1_SDP_HOST_WRRSP_CREDIT_ADDRESS         0x14a00178UL
#define SMN_IOMMU1PCIE1NBIO0_L1_SDP_HOST_WRRSP_CREDIT_ADDRESS         0x14c00178UL
#define SMN_IOMMU1PCIE1NBIO1_L1_SDP_HOST_WRRSP_CREDIT_ADDRESS         0x14e00178UL


/***********************************************************
* Register Name : L1_SDP_MAXCRED_0
************************************************************/

#define L1_SDP_MAXCRED_0_L1_FLUSHRSP_MAXCRED_OFFSET            0
#define L1_SDP_MAXCRED_0_L1_FLUSHRSP_MAXCRED_MASK              0xf

#define L1_SDP_MAXCRED_0_L1_HOSTRDRSP_MAXCRED_OFFSET           4
#define L1_SDP_MAXCRED_0_L1_HOSTRDRSP_MAXCRED_MASK             0x3f0

#define L1_SDP_MAXCRED_0_L1_HOSTWRRSP_MAXCRED_OFFSET           10
#define L1_SDP_MAXCRED_0_L1_HOSTWRRSP_MAXCRED_MASK             0xfc00

#define L1_SDP_MAXCRED_0_L1_MMIORDRSP_MAXCRED_OFFSET           16
#define L1_SDP_MAXCRED_0_L1_MMIORDRSP_MAXCRED_MASK             0xf0000

#define L1_SDP_MAXCRED_0_Reserved_30_20_OFFSET                 20
#define L1_SDP_MAXCRED_0_Reserved_30_20_MASK                   0x7ff00000

#define L1_SDP_MAXCRED_0_L1_DYNAMIC_CRED_RELOCATION_EN_OFFSET  31
#define L1_SDP_MAXCRED_0_L1_DYNAMIC_CRED_RELOCATION_EN_MASK    0x80000000

typedef union {
  struct {
    UINT32                                 L1_FLUSHRSP_MAXCRED:4;
    UINT32                                L1_HOSTRDRSP_MAXCRED:6;
    UINT32                                L1_HOSTWRRSP_MAXCRED:6;
    UINT32                                L1_MMIORDRSP_MAXCRED:4;
    UINT32                                      Reserved_30_20:11;
    UINT32                       L1_DYNAMIC_CRED_RELOCATION_EN:1;
  } Field;
  UINT32 Value;
} L1_SDP_MAXCRED_0_STRUCT;

#define SMN_L1_SDP_MAXCRED_0_ADDRESS                                  0x1530010cUL
#define SMN_IOMMU0IOAGRNBIO0_L1_SDP_MAXCRED_0_ADDRESS                 0x1530010cUL
#define SMN_IOMMU0IOAGRNBIO1_L1_SDP_MAXCRED_0_ADDRESS                 0x1550010cUL
#define SMN_IOMMU0PCIE0NBIO0_L1_SDP_MAXCRED_0_ADDRESS                 0x1470010cUL
#define SMN_IOMMU0PCIE0NBIO1_L1_SDP_MAXCRED_0_ADDRESS                 0x1490010cUL
#define SMN_IOMMU0PCIE1NBIO0_L1_SDP_MAXCRED_0_ADDRESS                 0x14b0010cUL
#define SMN_IOMMU0PCIE1NBIO1_L1_SDP_MAXCRED_0_ADDRESS                 0x14d0010cUL
#define SMN_IOMMU1IOAGRNBIO0_L1_SDP_MAXCRED_0_ADDRESS                 0x1540010cUL
#define SMN_IOMMU1IOAGRNBIO1_L1_SDP_MAXCRED_0_ADDRESS                 0x1560010cUL
#define SMN_IOMMU1PCIE0NBIO0_L1_SDP_MAXCRED_0_ADDRESS                 0x1480010cUL
#define SMN_IOMMU1PCIE0NBIO1_L1_SDP_MAXCRED_0_ADDRESS                 0x14a0010cUL
#define SMN_IOMMU1PCIE1NBIO0_L1_SDP_MAXCRED_0_ADDRESS                 0x14c0010cUL
#define SMN_IOMMU1PCIE1NBIO1_L1_SDP_MAXCRED_0_ADDRESS                 0x14e0010cUL


/***********************************************************
* Register Name : L1_SION_PERF_CNT_CNTL0
************************************************************/

#define L1_SION_PERF_CNT_CNTL0_L1_SION_CNT_EN_OFFSET           0
#define L1_SION_PERF_CNT_CNTL0_L1_SION_CNT_EN_MASK             0x1

#define L1_SION_PERF_CNT_CNTL0_L1_SION_SHADOW_WR_OFFSET        1
#define L1_SION_PERF_CNT_CNTL0_L1_SION_SHADOW_WR_MASK          0x2

#define L1_SION_PERF_CNT_CNTL0_L1_SION_PERF_RESET_OFFSET       2
#define L1_SION_PERF_CNT_CNTL0_L1_SION_PERF_RESET_MASK         0x4

#define L1_SION_PERF_CNT_CNTL0_Reserved_7_3_OFFSET             3
#define L1_SION_PERF_CNT_CNTL0_Reserved_7_3_MASK               0xf8

#define L1_SION_PERF_CNT_CNTL0_L1_SION_SHADOW_DELAY_OFFSET     8
#define L1_SION_PERF_CNT_CNTL0_L1_SION_SHADOW_DELAY_MASK       0xf00

#define L1_SION_PERF_CNT_CNTL0_Reserved_14_12_OFFSET           12
#define L1_SION_PERF_CNT_CNTL0_Reserved_14_12_MASK             0x7000

#define L1_SION_PERF_CNT_CNTL0_L1_SION_SHADOW_DELAY_EN_OFFSET  15
#define L1_SION_PERF_CNT_CNTL0_L1_SION_SHADOW_DELAY_EN_MASK    0x8000

#define L1_SION_PERF_CNT_CNTL0_L1_SION_PERF_RESET_DELAY_OFFSET 16
#define L1_SION_PERF_CNT_CNTL0_L1_SION_PERF_RESET_DELAY_MASK   0xf0000

#define L1_SION_PERF_CNT_CNTL0_Reserved_22_20_OFFSET           20
#define L1_SION_PERF_CNT_CNTL0_Reserved_22_20_MASK             0x700000

#define L1_SION_PERF_CNT_CNTL0_L1_SION_PERF_RESET_DELAY_EN_OFFSET 23
#define L1_SION_PERF_CNT_CNTL0_L1_SION_PERF_RESET_DELAY_EN_MASK 0x800000

#define L1_SION_PERF_CNT_CNTL0_Reserved_31_24_OFFSET           24
#define L1_SION_PERF_CNT_CNTL0_Reserved_31_24_MASK             0xff000000

typedef union {
  struct {
    UINT32                                      L1_SION_CNT_EN:1;
    UINT32                                   L1_SION_SHADOW_WR:1;
    UINT32                                  L1_SION_PERF_RESET:1;
    UINT32                                        Reserved_7_3:5;
    UINT32                                L1_SION_SHADOW_DELAY:4;
    UINT32                                      Reserved_14_12:3;
    UINT32                             L1_SION_SHADOW_DELAY_EN:1;
    UINT32                            L1_SION_PERF_RESET_DELAY:4;
    UINT32                                      Reserved_22_20:3;
    UINT32                         L1_SION_PERF_RESET_DELAY_EN:1;
    UINT32                                      Reserved_31_24:8;
  } Field;
  UINT32 Value;
} L1_SION_PERF_CNT_CNTL0_STRUCT;

#define SMN_L1_SION_PERF_CNT_CNTL0_ADDRESS                            0x15300148UL
#define SMN_IOMMU0IOAGRNBIO0_L1_SION_PERF_CNT_CNTL0_ADDRESS           0x15300148UL
#define SMN_IOMMU0IOAGRNBIO1_L1_SION_PERF_CNT_CNTL0_ADDRESS           0x15500148UL
#define SMN_IOMMU0PCIE0NBIO0_L1_SION_PERF_CNT_CNTL0_ADDRESS           0x14700148UL
#define SMN_IOMMU0PCIE0NBIO1_L1_SION_PERF_CNT_CNTL0_ADDRESS           0x14900148UL
#define SMN_IOMMU0PCIE1NBIO0_L1_SION_PERF_CNT_CNTL0_ADDRESS           0x14b00148UL
#define SMN_IOMMU0PCIE1NBIO1_L1_SION_PERF_CNT_CNTL0_ADDRESS           0x14d00148UL
#define SMN_IOMMU1IOAGRNBIO0_L1_SION_PERF_CNT_CNTL0_ADDRESS           0x15400148UL
#define SMN_IOMMU1IOAGRNBIO1_L1_SION_PERF_CNT_CNTL0_ADDRESS           0x15600148UL
#define SMN_IOMMU1PCIE0NBIO0_L1_SION_PERF_CNT_CNTL0_ADDRESS           0x14800148UL
#define SMN_IOMMU1PCIE0NBIO1_L1_SION_PERF_CNT_CNTL0_ADDRESS           0x14a00148UL
#define SMN_IOMMU1PCIE1NBIO0_L1_SION_PERF_CNT_CNTL0_ADDRESS           0x14c00148UL
#define SMN_IOMMU1PCIE1NBIO1_L1_SION_PERF_CNT_CNTL0_ADDRESS           0x14e00148UL


/***********************************************************
* Register Name : L1_SION_PERF_CNT_CNTL1
************************************************************/

#define L1_SION_PERF_CNT_CNTL1_L1_SION_EVENT0_SEL_OFFSET       0
#define L1_SION_PERF_CNT_CNTL1_L1_SION_EVENT0_SEL_MASK         0xff

#define L1_SION_PERF_CNT_CNTL1_L1_SION_EVENT1_SEL_OFFSET       8
#define L1_SION_PERF_CNT_CNTL1_L1_SION_EVENT1_SEL_MASK         0xff00

#define L1_SION_PERF_CNT_CNTL1_L1_SION_EVENT2_SEL_OFFSET       16
#define L1_SION_PERF_CNT_CNTL1_L1_SION_EVENT2_SEL_MASK         0xff0000

#define L1_SION_PERF_CNT_CNTL1_L1_SION_EVENT3_SEL_OFFSET       24
#define L1_SION_PERF_CNT_CNTL1_L1_SION_EVENT3_SEL_MASK         0xff000000

typedef union {
  struct {
    UINT32                                  L1_SION_EVENT0_SEL:8;
    UINT32                                  L1_SION_EVENT1_SEL:8;
    UINT32                                  L1_SION_EVENT2_SEL:8;
    UINT32                                  L1_SION_EVENT3_SEL:8;
  } Field;
  UINT32 Value;
} L1_SION_PERF_CNT_CNTL1_STRUCT;

#define SMN_L1_SION_PERF_CNT_CNTL1_ADDRESS                            0x1530014cUL
#define SMN_IOMMU0IOAGRNBIO0_L1_SION_PERF_CNT_CNTL1_ADDRESS           0x1530014cUL
#define SMN_IOMMU0IOAGRNBIO1_L1_SION_PERF_CNT_CNTL1_ADDRESS           0x1550014cUL
#define SMN_IOMMU0PCIE0NBIO0_L1_SION_PERF_CNT_CNTL1_ADDRESS           0x1470014cUL
#define SMN_IOMMU0PCIE0NBIO1_L1_SION_PERF_CNT_CNTL1_ADDRESS           0x1490014cUL
#define SMN_IOMMU0PCIE1NBIO0_L1_SION_PERF_CNT_CNTL1_ADDRESS           0x14b0014cUL
#define SMN_IOMMU0PCIE1NBIO1_L1_SION_PERF_CNT_CNTL1_ADDRESS           0x14d0014cUL
#define SMN_IOMMU1IOAGRNBIO0_L1_SION_PERF_CNT_CNTL1_ADDRESS           0x1540014cUL
#define SMN_IOMMU1IOAGRNBIO1_L1_SION_PERF_CNT_CNTL1_ADDRESS           0x1560014cUL
#define SMN_IOMMU1PCIE0NBIO0_L1_SION_PERF_CNT_CNTL1_ADDRESS           0x1480014cUL
#define SMN_IOMMU1PCIE0NBIO1_L1_SION_PERF_CNT_CNTL1_ADDRESS           0x14a0014cUL
#define SMN_IOMMU1PCIE1NBIO0_L1_SION_PERF_CNT_CNTL1_ADDRESS           0x14c0014cUL
#define SMN_IOMMU1PCIE1NBIO1_L1_SION_PERF_CNT_CNTL1_ADDRESS           0x14e0014cUL


/***********************************************************
* Register Name : L1_SION_PERF_COUNT0
************************************************************/

#define L1_SION_PERF_COUNT0_L1_SION_COUNTER0_OFFSET            0
#define L1_SION_PERF_COUNT0_L1_SION_COUNTER0_MASK              0xffffffff

typedef union {
  struct {
    UINT32                                    L1_SION_COUNTER0:32;
  } Field;
  UINT32 Value;
} L1_SION_PERF_COUNT0_STRUCT;

#define SMN_L1_SION_PERF_COUNT0_ADDRESS                               0x15300150UL
#define SMN_IOMMU0IOAGRNBIO0_L1_SION_PERF_COUNT0_ADDRESS              0x15300150UL
#define SMN_IOMMU0IOAGRNBIO1_L1_SION_PERF_COUNT0_ADDRESS              0x15500150UL
#define SMN_IOMMU0PCIE0NBIO0_L1_SION_PERF_COUNT0_ADDRESS              0x14700150UL
#define SMN_IOMMU0PCIE0NBIO1_L1_SION_PERF_COUNT0_ADDRESS              0x14900150UL
#define SMN_IOMMU0PCIE1NBIO0_L1_SION_PERF_COUNT0_ADDRESS              0x14b00150UL
#define SMN_IOMMU0PCIE1NBIO1_L1_SION_PERF_COUNT0_ADDRESS              0x14d00150UL
#define SMN_IOMMU1IOAGRNBIO0_L1_SION_PERF_COUNT0_ADDRESS              0x15400150UL
#define SMN_IOMMU1IOAGRNBIO1_L1_SION_PERF_COUNT0_ADDRESS              0x15600150UL
#define SMN_IOMMU1PCIE0NBIO0_L1_SION_PERF_COUNT0_ADDRESS              0x14800150UL
#define SMN_IOMMU1PCIE0NBIO1_L1_SION_PERF_COUNT0_ADDRESS              0x14a00150UL
#define SMN_IOMMU1PCIE1NBIO0_L1_SION_PERF_COUNT0_ADDRESS              0x14c00150UL
#define SMN_IOMMU1PCIE1NBIO1_L1_SION_PERF_COUNT0_ADDRESS              0x14e00150UL


/***********************************************************
* Register Name : L1_SION_PERF_COUNT0_UPPER
************************************************************/

#define L1_SION_PERF_COUNT0_UPPER_L1_SION_COUNTER0_UPPER_OFFSET 0
#define L1_SION_PERF_COUNT0_UPPER_L1_SION_COUNTER0_UPPER_MASK  0xffffff

#define L1_SION_PERF_COUNT0_UPPER_Reserved_31_24_OFFSET        24
#define L1_SION_PERF_COUNT0_UPPER_Reserved_31_24_MASK          0xff000000

typedef union {
  struct {
    UINT32                              L1_SION_COUNTER0_UPPER:24;
    UINT32                                      Reserved_31_24:8;
  } Field;
  UINT32 Value;
} L1_SION_PERF_COUNT0_UPPER_STRUCT;

#define SMN_L1_SION_PERF_COUNT0_UPPER_ADDRESS                         0x15300154UL
#define SMN_IOMMU0IOAGRNBIO0_L1_SION_PERF_COUNT0_UPPER_ADDRESS        0x15300154UL
#define SMN_IOMMU0IOAGRNBIO1_L1_SION_PERF_COUNT0_UPPER_ADDRESS        0x15500154UL
#define SMN_IOMMU0PCIE0NBIO0_L1_SION_PERF_COUNT0_UPPER_ADDRESS        0x14700154UL
#define SMN_IOMMU0PCIE0NBIO1_L1_SION_PERF_COUNT0_UPPER_ADDRESS        0x14900154UL
#define SMN_IOMMU0PCIE1NBIO0_L1_SION_PERF_COUNT0_UPPER_ADDRESS        0x14b00154UL
#define SMN_IOMMU0PCIE1NBIO1_L1_SION_PERF_COUNT0_UPPER_ADDRESS        0x14d00154UL
#define SMN_IOMMU1IOAGRNBIO0_L1_SION_PERF_COUNT0_UPPER_ADDRESS        0x15400154UL
#define SMN_IOMMU1IOAGRNBIO1_L1_SION_PERF_COUNT0_UPPER_ADDRESS        0x15600154UL
#define SMN_IOMMU1PCIE0NBIO0_L1_SION_PERF_COUNT0_UPPER_ADDRESS        0x14800154UL
#define SMN_IOMMU1PCIE0NBIO1_L1_SION_PERF_COUNT0_UPPER_ADDRESS        0x14a00154UL
#define SMN_IOMMU1PCIE1NBIO0_L1_SION_PERF_COUNT0_UPPER_ADDRESS        0x14c00154UL
#define SMN_IOMMU1PCIE1NBIO1_L1_SION_PERF_COUNT0_UPPER_ADDRESS        0x14e00154UL


/***********************************************************
* Register Name : L1_SION_PERF_COUNT1
************************************************************/

#define L1_SION_PERF_COUNT1_L1_SION_COUNTER1_OFFSET            0
#define L1_SION_PERF_COUNT1_L1_SION_COUNTER1_MASK              0xffffffff

typedef union {
  struct {
    UINT32                                    L1_SION_COUNTER1:32;
  } Field;
  UINT32 Value;
} L1_SION_PERF_COUNT1_STRUCT;

#define SMN_L1_SION_PERF_COUNT1_ADDRESS                               0x15300158UL
#define SMN_IOMMU0IOAGRNBIO0_L1_SION_PERF_COUNT1_ADDRESS              0x15300158UL
#define SMN_IOMMU0IOAGRNBIO1_L1_SION_PERF_COUNT1_ADDRESS              0x15500158UL
#define SMN_IOMMU0PCIE0NBIO0_L1_SION_PERF_COUNT1_ADDRESS              0x14700158UL
#define SMN_IOMMU0PCIE0NBIO1_L1_SION_PERF_COUNT1_ADDRESS              0x14900158UL
#define SMN_IOMMU0PCIE1NBIO0_L1_SION_PERF_COUNT1_ADDRESS              0x14b00158UL
#define SMN_IOMMU0PCIE1NBIO1_L1_SION_PERF_COUNT1_ADDRESS              0x14d00158UL
#define SMN_IOMMU1IOAGRNBIO0_L1_SION_PERF_COUNT1_ADDRESS              0x15400158UL
#define SMN_IOMMU1IOAGRNBIO1_L1_SION_PERF_COUNT1_ADDRESS              0x15600158UL
#define SMN_IOMMU1PCIE0NBIO0_L1_SION_PERF_COUNT1_ADDRESS              0x14800158UL
#define SMN_IOMMU1PCIE0NBIO1_L1_SION_PERF_COUNT1_ADDRESS              0x14a00158UL
#define SMN_IOMMU1PCIE1NBIO0_L1_SION_PERF_COUNT1_ADDRESS              0x14c00158UL
#define SMN_IOMMU1PCIE1NBIO1_L1_SION_PERF_COUNT1_ADDRESS              0x14e00158UL


/***********************************************************
* Register Name : L1_SION_PERF_COUNT1_UPPER
************************************************************/

#define L1_SION_PERF_COUNT1_UPPER_L1_SION_COUNTER1_UPPER_OFFSET 0
#define L1_SION_PERF_COUNT1_UPPER_L1_SION_COUNTER1_UPPER_MASK  0xffffff

#define L1_SION_PERF_COUNT1_UPPER_Reserved_31_24_OFFSET        24
#define L1_SION_PERF_COUNT1_UPPER_Reserved_31_24_MASK          0xff000000

typedef union {
  struct {
    UINT32                              L1_SION_COUNTER1_UPPER:24;
    UINT32                                      Reserved_31_24:8;
  } Field;
  UINT32 Value;
} L1_SION_PERF_COUNT1_UPPER_STRUCT;

#define SMN_L1_SION_PERF_COUNT1_UPPER_ADDRESS                         0x1530015cUL
#define SMN_IOMMU0IOAGRNBIO0_L1_SION_PERF_COUNT1_UPPER_ADDRESS        0x1530015cUL
#define SMN_IOMMU0IOAGRNBIO1_L1_SION_PERF_COUNT1_UPPER_ADDRESS        0x1550015cUL
#define SMN_IOMMU0PCIE0NBIO0_L1_SION_PERF_COUNT1_UPPER_ADDRESS        0x1470015cUL
#define SMN_IOMMU0PCIE0NBIO1_L1_SION_PERF_COUNT1_UPPER_ADDRESS        0x1490015cUL
#define SMN_IOMMU0PCIE1NBIO0_L1_SION_PERF_COUNT1_UPPER_ADDRESS        0x14b0015cUL
#define SMN_IOMMU0PCIE1NBIO1_L1_SION_PERF_COUNT1_UPPER_ADDRESS        0x14d0015cUL
#define SMN_IOMMU1IOAGRNBIO0_L1_SION_PERF_COUNT1_UPPER_ADDRESS        0x1540015cUL
#define SMN_IOMMU1IOAGRNBIO1_L1_SION_PERF_COUNT1_UPPER_ADDRESS        0x1560015cUL
#define SMN_IOMMU1PCIE0NBIO0_L1_SION_PERF_COUNT1_UPPER_ADDRESS        0x1480015cUL
#define SMN_IOMMU1PCIE0NBIO1_L1_SION_PERF_COUNT1_UPPER_ADDRESS        0x14a0015cUL
#define SMN_IOMMU1PCIE1NBIO0_L1_SION_PERF_COUNT1_UPPER_ADDRESS        0x14c0015cUL
#define SMN_IOMMU1PCIE1NBIO1_L1_SION_PERF_COUNT1_UPPER_ADDRESS        0x14e0015cUL


/***********************************************************
* Register Name : L1_SION_PERF_COUNT2
************************************************************/

#define L1_SION_PERF_COUNT2_L1_SION_COUNTER2_OFFSET            0
#define L1_SION_PERF_COUNT2_L1_SION_COUNTER2_MASK              0xffffffff

typedef union {
  struct {
    UINT32                                    L1_SION_COUNTER2:32;
  } Field;
  UINT32 Value;
} L1_SION_PERF_COUNT2_STRUCT;

#define SMN_L1_SION_PERF_COUNT2_ADDRESS                               0x15300160UL
#define SMN_IOMMU0IOAGRNBIO0_L1_SION_PERF_COUNT2_ADDRESS              0x15300160UL
#define SMN_IOMMU0IOAGRNBIO1_L1_SION_PERF_COUNT2_ADDRESS              0x15500160UL
#define SMN_IOMMU0PCIE0NBIO0_L1_SION_PERF_COUNT2_ADDRESS              0x14700160UL
#define SMN_IOMMU0PCIE0NBIO1_L1_SION_PERF_COUNT2_ADDRESS              0x14900160UL
#define SMN_IOMMU0PCIE1NBIO0_L1_SION_PERF_COUNT2_ADDRESS              0x14b00160UL
#define SMN_IOMMU0PCIE1NBIO1_L1_SION_PERF_COUNT2_ADDRESS              0x14d00160UL
#define SMN_IOMMU1IOAGRNBIO0_L1_SION_PERF_COUNT2_ADDRESS              0x15400160UL
#define SMN_IOMMU1IOAGRNBIO1_L1_SION_PERF_COUNT2_ADDRESS              0x15600160UL
#define SMN_IOMMU1PCIE0NBIO0_L1_SION_PERF_COUNT2_ADDRESS              0x14800160UL
#define SMN_IOMMU1PCIE0NBIO1_L1_SION_PERF_COUNT2_ADDRESS              0x14a00160UL
#define SMN_IOMMU1PCIE1NBIO0_L1_SION_PERF_COUNT2_ADDRESS              0x14c00160UL
#define SMN_IOMMU1PCIE1NBIO1_L1_SION_PERF_COUNT2_ADDRESS              0x14e00160UL


/***********************************************************
* Register Name : L1_SION_PERF_COUNT2_UPPER
************************************************************/

#define L1_SION_PERF_COUNT2_UPPER_L1_SION_COUNTER2_UPPER_OFFSET 0
#define L1_SION_PERF_COUNT2_UPPER_L1_SION_COUNTER2_UPPER_MASK  0xffffff

#define L1_SION_PERF_COUNT2_UPPER_Reserved_31_24_OFFSET        24
#define L1_SION_PERF_COUNT2_UPPER_Reserved_31_24_MASK          0xff000000

typedef union {
  struct {
    UINT32                              L1_SION_COUNTER2_UPPER:24;
    UINT32                                      Reserved_31_24:8;
  } Field;
  UINT32 Value;
} L1_SION_PERF_COUNT2_UPPER_STRUCT;

#define SMN_L1_SION_PERF_COUNT2_UPPER_ADDRESS                         0x15300164UL
#define SMN_IOMMU0IOAGRNBIO0_L1_SION_PERF_COUNT2_UPPER_ADDRESS        0x15300164UL
#define SMN_IOMMU0IOAGRNBIO1_L1_SION_PERF_COUNT2_UPPER_ADDRESS        0x15500164UL
#define SMN_IOMMU0PCIE0NBIO0_L1_SION_PERF_COUNT2_UPPER_ADDRESS        0x14700164UL
#define SMN_IOMMU0PCIE0NBIO1_L1_SION_PERF_COUNT2_UPPER_ADDRESS        0x14900164UL
#define SMN_IOMMU0PCIE1NBIO0_L1_SION_PERF_COUNT2_UPPER_ADDRESS        0x14b00164UL
#define SMN_IOMMU0PCIE1NBIO1_L1_SION_PERF_COUNT2_UPPER_ADDRESS        0x14d00164UL
#define SMN_IOMMU1IOAGRNBIO0_L1_SION_PERF_COUNT2_UPPER_ADDRESS        0x15400164UL
#define SMN_IOMMU1IOAGRNBIO1_L1_SION_PERF_COUNT2_UPPER_ADDRESS        0x15600164UL
#define SMN_IOMMU1PCIE0NBIO0_L1_SION_PERF_COUNT2_UPPER_ADDRESS        0x14800164UL
#define SMN_IOMMU1PCIE0NBIO1_L1_SION_PERF_COUNT2_UPPER_ADDRESS        0x14a00164UL
#define SMN_IOMMU1PCIE1NBIO0_L1_SION_PERF_COUNT2_UPPER_ADDRESS        0x14c00164UL
#define SMN_IOMMU1PCIE1NBIO1_L1_SION_PERF_COUNT2_UPPER_ADDRESS        0x14e00164UL


/***********************************************************
* Register Name : L1_SION_PERF_COUNT3
************************************************************/

#define L1_SION_PERF_COUNT3_L1_SION_COUNTER3_OFFSET            0
#define L1_SION_PERF_COUNT3_L1_SION_COUNTER3_MASK              0xffffffff

typedef union {
  struct {
    UINT32                                    L1_SION_COUNTER3:32;
  } Field;
  UINT32 Value;
} L1_SION_PERF_COUNT3_STRUCT;

#define SMN_L1_SION_PERF_COUNT3_ADDRESS                               0x15300168UL
#define SMN_IOMMU0IOAGRNBIO0_L1_SION_PERF_COUNT3_ADDRESS              0x15300168UL
#define SMN_IOMMU0IOAGRNBIO1_L1_SION_PERF_COUNT3_ADDRESS              0x15500168UL
#define SMN_IOMMU0PCIE0NBIO0_L1_SION_PERF_COUNT3_ADDRESS              0x14700168UL
#define SMN_IOMMU0PCIE0NBIO1_L1_SION_PERF_COUNT3_ADDRESS              0x14900168UL
#define SMN_IOMMU0PCIE1NBIO0_L1_SION_PERF_COUNT3_ADDRESS              0x14b00168UL
#define SMN_IOMMU0PCIE1NBIO1_L1_SION_PERF_COUNT3_ADDRESS              0x14d00168UL
#define SMN_IOMMU1IOAGRNBIO0_L1_SION_PERF_COUNT3_ADDRESS              0x15400168UL
#define SMN_IOMMU1IOAGRNBIO1_L1_SION_PERF_COUNT3_ADDRESS              0x15600168UL
#define SMN_IOMMU1PCIE0NBIO0_L1_SION_PERF_COUNT3_ADDRESS              0x14800168UL
#define SMN_IOMMU1PCIE0NBIO1_L1_SION_PERF_COUNT3_ADDRESS              0x14a00168UL
#define SMN_IOMMU1PCIE1NBIO0_L1_SION_PERF_COUNT3_ADDRESS              0x14c00168UL
#define SMN_IOMMU1PCIE1NBIO1_L1_SION_PERF_COUNT3_ADDRESS              0x14e00168UL


/***********************************************************
* Register Name : L1_SION_PERF_COUNT3_UPPER
************************************************************/

#define L1_SION_PERF_COUNT3_UPPER_L1_SION_COUNTER3_UPPER_OFFSET 0
#define L1_SION_PERF_COUNT3_UPPER_L1_SION_COUNTER3_UPPER_MASK  0xffffff

#define L1_SION_PERF_COUNT3_UPPER_Reserved_31_24_OFFSET        24
#define L1_SION_PERF_COUNT3_UPPER_Reserved_31_24_MASK          0xff000000

typedef union {
  struct {
    UINT32                              L1_SION_COUNTER3_UPPER:24;
    UINT32                                      Reserved_31_24:8;
  } Field;
  UINT32 Value;
} L1_SION_PERF_COUNT3_UPPER_STRUCT;

#define SMN_L1_SION_PERF_COUNT3_UPPER_ADDRESS                         0x1530016cUL
#define SMN_IOMMU0IOAGRNBIO0_L1_SION_PERF_COUNT3_UPPER_ADDRESS        0x1530016cUL
#define SMN_IOMMU0IOAGRNBIO1_L1_SION_PERF_COUNT3_UPPER_ADDRESS        0x1550016cUL
#define SMN_IOMMU0PCIE0NBIO0_L1_SION_PERF_COUNT3_UPPER_ADDRESS        0x1470016cUL
#define SMN_IOMMU0PCIE0NBIO1_L1_SION_PERF_COUNT3_UPPER_ADDRESS        0x1490016cUL
#define SMN_IOMMU0PCIE1NBIO0_L1_SION_PERF_COUNT3_UPPER_ADDRESS        0x14b0016cUL
#define SMN_IOMMU0PCIE1NBIO1_L1_SION_PERF_COUNT3_UPPER_ADDRESS        0x14d0016cUL
#define SMN_IOMMU1IOAGRNBIO0_L1_SION_PERF_COUNT3_UPPER_ADDRESS        0x1540016cUL
#define SMN_IOMMU1IOAGRNBIO1_L1_SION_PERF_COUNT3_UPPER_ADDRESS        0x1560016cUL
#define SMN_IOMMU1PCIE0NBIO0_L1_SION_PERF_COUNT3_UPPER_ADDRESS        0x1480016cUL
#define SMN_IOMMU1PCIE0NBIO1_L1_SION_PERF_COUNT3_UPPER_ADDRESS        0x14a0016cUL
#define SMN_IOMMU1PCIE1NBIO0_L1_SION_PERF_COUNT3_UPPER_ADDRESS        0x14c0016cUL
#define SMN_IOMMU1PCIE1NBIO1_L1_SION_PERF_COUNT3_UPPER_ADDRESS        0x14e0016cUL


/***********************************************************
* Register Name : L1_SMMIO_HOST_MAXCRED_0
************************************************************/

#define L1_SMMIO_HOST_MAXCRED_0_L1_SMMIO_HOSTREQ_MAXCRED_OFFSET 0
#define L1_SMMIO_HOST_MAXCRED_0_L1_SMMIO_HOSTREQ_MAXCRED_MASK  0x1ff

#define L1_SMMIO_HOST_MAXCRED_0_Reserved_15_9_OFFSET           9
#define L1_SMMIO_HOST_MAXCRED_0_Reserved_15_9_MASK             0xfe00

#define L1_SMMIO_HOST_MAXCRED_0_L1_SMMIO_HOSTDATA_MAXCRED_OFFSET 16
#define L1_SMMIO_HOST_MAXCRED_0_L1_SMMIO_HOSTDATA_MAXCRED_MASK 0x1ff0000

#define L1_SMMIO_HOST_MAXCRED_0_Reserved_31_25_OFFSET          25
#define L1_SMMIO_HOST_MAXCRED_0_Reserved_31_25_MASK            0xfe000000

typedef union {
  struct {
    UINT32                            L1_SMMIO_HOSTREQ_MAXCRED:9;
    UINT32                                       Reserved_15_9:7;
    UINT32                           L1_SMMIO_HOSTDATA_MAXCRED:9;
    UINT32                                      Reserved_31_25:7;
  } Field;
  UINT32 Value;
} L1_SMMIO_HOST_MAXCRED_0_STRUCT;

#define SMN_L1_SMMIO_HOST_MAXCRED_0_ADDRESS                           0x153001a0UL
#define SMN_IOMMU0IOAGRNBIO0_L1_SMMIO_HOST_MAXCRED_0_ADDRESS          0x153001a0UL
#define SMN_IOMMU0IOAGRNBIO1_L1_SMMIO_HOST_MAXCRED_0_ADDRESS          0x155001a0UL
#define SMN_IOMMU0PCIE0NBIO0_L1_SMMIO_HOST_MAXCRED_0_ADDRESS          0x147001a0UL
#define SMN_IOMMU0PCIE0NBIO1_L1_SMMIO_HOST_MAXCRED_0_ADDRESS          0x149001a0UL
#define SMN_IOMMU0PCIE1NBIO0_L1_SMMIO_HOST_MAXCRED_0_ADDRESS          0x14b001a0UL
#define SMN_IOMMU0PCIE1NBIO1_L1_SMMIO_HOST_MAXCRED_0_ADDRESS          0x14d001a0UL
#define SMN_IOMMU1IOAGRNBIO0_L1_SMMIO_HOST_MAXCRED_0_ADDRESS          0x154001a0UL
#define SMN_IOMMU1IOAGRNBIO1_L1_SMMIO_HOST_MAXCRED_0_ADDRESS          0x156001a0UL
#define SMN_IOMMU1PCIE0NBIO0_L1_SMMIO_HOST_MAXCRED_0_ADDRESS          0x148001a0UL
#define SMN_IOMMU1PCIE0NBIO1_L1_SMMIO_HOST_MAXCRED_0_ADDRESS          0x14a001a0UL
#define SMN_IOMMU1PCIE1NBIO0_L1_SMMIO_HOST_MAXCRED_0_ADDRESS          0x14c001a0UL
#define SMN_IOMMU1PCIE1NBIO1_L1_SMMIO_HOST_MAXCRED_0_ADDRESS          0x14e001a0UL


/***********************************************************
* Register Name : L1_STAGE2_CREDIT_RESERVATION
************************************************************/

#define L1_STAGE2_CREDIT_RESERVATION_VC0_STAGE2_CREDIT_OFFSET  0
#define L1_STAGE2_CREDIT_RESERVATION_VC0_STAGE2_CREDIT_MASK    0x7f

#define L1_STAGE2_CREDIT_RESERVATION_VC1_STAGE2_CREDIT_OFFSET  7
#define L1_STAGE2_CREDIT_RESERVATION_VC1_STAGE2_CREDIT_MASK    0x3f80

#define L1_STAGE2_CREDIT_RESERVATION_Reserved_31_14_OFFSET     14
#define L1_STAGE2_CREDIT_RESERVATION_Reserved_31_14_MASK       0xffffc000

typedef union {
  struct {
    UINT32                                   VC0_STAGE2_CREDIT:7;
    UINT32                                   VC1_STAGE2_CREDIT:7;
    UINT32                                      Reserved_31_14:18;
  } Field;
  UINT32 Value;
} L1_STAGE2_CREDIT_RESERVATION_STRUCT;

#define SMN_L1_STAGE2_CREDIT_RESERVATION_ADDRESS                      0x15300188UL
#define SMN_IOMMU0IOAGRNBIO0_L1_STAGE2_CREDIT_RESERVATION_ADDRESS     0x15300188UL
#define SMN_IOMMU0IOAGRNBIO1_L1_STAGE2_CREDIT_RESERVATION_ADDRESS     0x15500188UL
#define SMN_IOMMU0PCIE0NBIO0_L1_STAGE2_CREDIT_RESERVATION_ADDRESS     0x14700188UL
#define SMN_IOMMU0PCIE0NBIO1_L1_STAGE2_CREDIT_RESERVATION_ADDRESS     0x14900188UL
#define SMN_IOMMU0PCIE1NBIO0_L1_STAGE2_CREDIT_RESERVATION_ADDRESS     0x14b00188UL
#define SMN_IOMMU0PCIE1NBIO1_L1_STAGE2_CREDIT_RESERVATION_ADDRESS     0x14d00188UL
#define SMN_IOMMU1IOAGRNBIO0_L1_STAGE2_CREDIT_RESERVATION_ADDRESS     0x15400188UL
#define SMN_IOMMU1IOAGRNBIO1_L1_STAGE2_CREDIT_RESERVATION_ADDRESS     0x15600188UL
#define SMN_IOMMU1PCIE0NBIO0_L1_STAGE2_CREDIT_RESERVATION_ADDRESS     0x14800188UL
#define SMN_IOMMU1PCIE0NBIO1_L1_STAGE2_CREDIT_RESERVATION_ADDRESS     0x14a00188UL
#define SMN_IOMMU1PCIE1NBIO0_L1_STAGE2_CREDIT_RESERVATION_ADDRESS     0x14c00188UL
#define SMN_IOMMU1PCIE1NBIO1_L1_STAGE2_CREDIT_RESERVATION_ADDRESS     0x14e00188UL


/***********************************************************
* Register Name : L1_TF_REQ_ERROR_0
************************************************************/

#define L1_TF_REQ_ERROR_0_TF_DMAERR_VLD_OFFSET                 0
#define L1_TF_REQ_ERROR_0_TF_DMAERR_VLD_MASK                   0x1

#define L1_TF_REQ_ERROR_0_TF_DMAERR_OVERFLOW_OFFSET            1
#define L1_TF_REQ_ERROR_0_TF_DMAERR_OVERFLOW_MASK              0x2

#define L1_TF_REQ_ERROR_0_Reserved_3_2_OFFSET                  2
#define L1_TF_REQ_ERROR_0_Reserved_3_2_MASK                    0xc

#define L1_TF_REQ_ERROR_0_TF_DMAERR_CODE_OFFSET                4
#define L1_TF_REQ_ERROR_0_TF_DMAERR_CODE_MASK                  0xf0

#define L1_TF_REQ_ERROR_0_TF_DMAERR_CTXT_OFFSET                8
#define L1_TF_REQ_ERROR_0_TF_DMAERR_CTXT_MASK                  0xf00

#define L1_TF_REQ_ERROR_0_Reserved_15_12_OFFSET                12
#define L1_TF_REQ_ERROR_0_Reserved_15_12_MASK                  0xf000

#define L1_TF_REQ_ERROR_0_TF_DMAERR_REQID_OFFSET               16
#define L1_TF_REQ_ERROR_0_TF_DMAERR_REQID_MASK                 0xffff0000

typedef union {
  struct {
    UINT32                                       TF_DMAERR_VLD:1;
    UINT32                                  TF_DMAERR_OVERFLOW:1;
    UINT32                                        Reserved_3_2:2;
    UINT32                                      TF_DMAERR_CODE:4;
    UINT32                                      TF_DMAERR_CTXT:4;
    UINT32                                      Reserved_15_12:4;
    UINT32                                     TF_DMAERR_REQID:16;
  } Field;
  UINT32 Value;
} L1_TF_REQ_ERROR_0_STRUCT;

#define SMN_L1_TF_REQ_ERROR_0_ADDRESS                                 0x15300264UL
#define SMN_IOMMU0IOAGRNBIO0_L1_TF_REQ_ERROR_0_ADDRESS                0x15300264UL
#define SMN_IOMMU0IOAGRNBIO1_L1_TF_REQ_ERROR_0_ADDRESS                0x15500264UL
#define SMN_IOMMU0PCIE0NBIO0_L1_TF_REQ_ERROR_0_ADDRESS                0x14700264UL
#define SMN_IOMMU0PCIE0NBIO1_L1_TF_REQ_ERROR_0_ADDRESS                0x14900264UL
#define SMN_IOMMU0PCIE1NBIO0_L1_TF_REQ_ERROR_0_ADDRESS                0x14b00264UL
#define SMN_IOMMU0PCIE1NBIO1_L1_TF_REQ_ERROR_0_ADDRESS                0x14d00264UL
#define SMN_IOMMU1IOAGRNBIO0_L1_TF_REQ_ERROR_0_ADDRESS                0x15400264UL
#define SMN_IOMMU1IOAGRNBIO1_L1_TF_REQ_ERROR_0_ADDRESS                0x15600264UL
#define SMN_IOMMU1PCIE0NBIO0_L1_TF_REQ_ERROR_0_ADDRESS                0x14800264UL
#define SMN_IOMMU1PCIE0NBIO1_L1_TF_REQ_ERROR_0_ADDRESS                0x14a00264UL
#define SMN_IOMMU1PCIE1NBIO0_L1_TF_REQ_ERROR_0_ADDRESS                0x14c00264UL
#define SMN_IOMMU1PCIE1NBIO1_L1_TF_REQ_ERROR_0_ADDRESS                0x14e00264UL


/***********************************************************
* Register Name : L1_TF_REQ_ERROR_1
************************************************************/

#define L1_TF_REQ_ERROR_1_TF_DMAERR_UNITID_OFFSET              0
#define L1_TF_REQ_ERROR_1_TF_DMAERR_UNITID_MASK                0x7ff

#define L1_TF_REQ_ERROR_1_Reserved_14_11_OFFSET                11
#define L1_TF_REQ_ERROR_1_Reserved_14_11_MASK                  0x7800

#define L1_TF_REQ_ERROR_1_TF_DMAERR_CMD_OFFSET                 15
#define L1_TF_REQ_ERROR_1_TF_DMAERR_CMD_MASK                   0x1f8000

#define L1_TF_REQ_ERROR_1_Reserved_31_21_OFFSET                21
#define L1_TF_REQ_ERROR_1_Reserved_31_21_MASK                  0xffe00000

typedef union {
  struct {
    UINT32                                    TF_DMAERR_UNITID:11;
    UINT32                                      Reserved_14_11:4;
    UINT32                                       TF_DMAERR_CMD:6;
    UINT32                                      Reserved_31_21:11;
  } Field;
  UINT32 Value;
} L1_TF_REQ_ERROR_1_STRUCT;

#define SMN_L1_TF_REQ_ERROR_1_ADDRESS                                 0x15300268UL
#define SMN_IOMMU0IOAGRNBIO0_L1_TF_REQ_ERROR_1_ADDRESS                0x15300268UL
#define SMN_IOMMU0IOAGRNBIO1_L1_TF_REQ_ERROR_1_ADDRESS                0x15500268UL
#define SMN_IOMMU0PCIE0NBIO0_L1_TF_REQ_ERROR_1_ADDRESS                0x14700268UL
#define SMN_IOMMU0PCIE0NBIO1_L1_TF_REQ_ERROR_1_ADDRESS                0x14900268UL
#define SMN_IOMMU0PCIE1NBIO0_L1_TF_REQ_ERROR_1_ADDRESS                0x14b00268UL
#define SMN_IOMMU0PCIE1NBIO1_L1_TF_REQ_ERROR_1_ADDRESS                0x14d00268UL
#define SMN_IOMMU1IOAGRNBIO0_L1_TF_REQ_ERROR_1_ADDRESS                0x15400268UL
#define SMN_IOMMU1IOAGRNBIO1_L1_TF_REQ_ERROR_1_ADDRESS                0x15600268UL
#define SMN_IOMMU1PCIE0NBIO0_L1_TF_REQ_ERROR_1_ADDRESS                0x14800268UL
#define SMN_IOMMU1PCIE0NBIO1_L1_TF_REQ_ERROR_1_ADDRESS                0x14a00268UL
#define SMN_IOMMU1PCIE1NBIO0_L1_TF_REQ_ERROR_1_ADDRESS                0x14c00268UL
#define SMN_IOMMU1PCIE1NBIO1_L1_TF_REQ_ERROR_1_ADDRESS                0x14e00268UL


/***********************************************************
* Register Name : L1_TF_REQ_ERROR_2
************************************************************/

#define L1_TF_REQ_ERROR_2_TF_DMAERR_ADDR_0_OFFSET              0
#define L1_TF_REQ_ERROR_2_TF_DMAERR_ADDR_0_MASK                0xffffffff

typedef union {
  struct {
    UINT32                                    TF_DMAERR_ADDR_0:32;
  } Field;
  UINT32 Value;
} L1_TF_REQ_ERROR_2_STRUCT;

#define SMN_L1_TF_REQ_ERROR_2_ADDRESS                                 0x1530026cUL
#define SMN_IOMMU0IOAGRNBIO0_L1_TF_REQ_ERROR_2_ADDRESS                0x1530026cUL
#define SMN_IOMMU0IOAGRNBIO1_L1_TF_REQ_ERROR_2_ADDRESS                0x1550026cUL
#define SMN_IOMMU0PCIE0NBIO0_L1_TF_REQ_ERROR_2_ADDRESS                0x1470026cUL
#define SMN_IOMMU0PCIE0NBIO1_L1_TF_REQ_ERROR_2_ADDRESS                0x1490026cUL
#define SMN_IOMMU0PCIE1NBIO0_L1_TF_REQ_ERROR_2_ADDRESS                0x14b0026cUL
#define SMN_IOMMU0PCIE1NBIO1_L1_TF_REQ_ERROR_2_ADDRESS                0x14d0026cUL
#define SMN_IOMMU1IOAGRNBIO0_L1_TF_REQ_ERROR_2_ADDRESS                0x1540026cUL
#define SMN_IOMMU1IOAGRNBIO1_L1_TF_REQ_ERROR_2_ADDRESS                0x1560026cUL
#define SMN_IOMMU1PCIE0NBIO0_L1_TF_REQ_ERROR_2_ADDRESS                0x1480026cUL
#define SMN_IOMMU1PCIE0NBIO1_L1_TF_REQ_ERROR_2_ADDRESS                0x14a0026cUL
#define SMN_IOMMU1PCIE1NBIO0_L1_TF_REQ_ERROR_2_ADDRESS                0x14c0026cUL
#define SMN_IOMMU1PCIE1NBIO1_L1_TF_REQ_ERROR_2_ADDRESS                0x14e0026cUL


/***********************************************************
* Register Name : L1_TF_REQ_ERROR_3
************************************************************/

#define L1_TF_REQ_ERROR_3_TF_DMAERR_ADDR_1_OFFSET              0
#define L1_TF_REQ_ERROR_3_TF_DMAERR_ADDR_1_MASK                0xffffffff

typedef union {
  struct {
    UINT32                                    TF_DMAERR_ADDR_1:32;
  } Field;
  UINT32 Value;
} L1_TF_REQ_ERROR_3_STRUCT;

#define SMN_L1_TF_REQ_ERROR_3_ADDRESS                                 0x15300270UL
#define SMN_IOMMU0IOAGRNBIO0_L1_TF_REQ_ERROR_3_ADDRESS                0x15300270UL
#define SMN_IOMMU0IOAGRNBIO1_L1_TF_REQ_ERROR_3_ADDRESS                0x15500270UL
#define SMN_IOMMU0PCIE0NBIO0_L1_TF_REQ_ERROR_3_ADDRESS                0x14700270UL
#define SMN_IOMMU0PCIE0NBIO1_L1_TF_REQ_ERROR_3_ADDRESS                0x14900270UL
#define SMN_IOMMU0PCIE1NBIO0_L1_TF_REQ_ERROR_3_ADDRESS                0x14b00270UL
#define SMN_IOMMU0PCIE1NBIO1_L1_TF_REQ_ERROR_3_ADDRESS                0x14d00270UL
#define SMN_IOMMU1IOAGRNBIO0_L1_TF_REQ_ERROR_3_ADDRESS                0x15400270UL
#define SMN_IOMMU1IOAGRNBIO1_L1_TF_REQ_ERROR_3_ADDRESS                0x15600270UL
#define SMN_IOMMU1PCIE0NBIO0_L1_TF_REQ_ERROR_3_ADDRESS                0x14800270UL
#define SMN_IOMMU1PCIE0NBIO1_L1_TF_REQ_ERROR_3_ADDRESS                0x14a00270UL
#define SMN_IOMMU1PCIE1NBIO0_L1_TF_REQ_ERROR_3_ADDRESS                0x14c00270UL
#define SMN_IOMMU1PCIE1NBIO1_L1_TF_REQ_ERROR_3_ADDRESS                0x14e00270UL


/***********************************************************
* Register Name : L1_TOTAL_REQUESTS_LOWER
************************************************************/

#define L1_TOTAL_REQUESTS_LOWER_L1_TOTAL_REQUESTS_LOWER_OFFSET 0
#define L1_TOTAL_REQUESTS_LOWER_L1_TOTAL_REQUESTS_LOWER_MASK   0xffffffff

typedef union {
  struct {
    UINT32                             L1_TOTAL_REQUESTS_LOWER:32;
  } Field;
  UINT32 Value;
} L1_TOTAL_REQUESTS_LOWER_STRUCT;

#define SMN_L1_TOTAL_REQUESTS_LOWER_ADDRESS                           0x15300140UL
#define SMN_IOMMU0IOAGRNBIO0_L1_TOTAL_REQUESTS_LOWER_ADDRESS          0x15300140UL
#define SMN_IOMMU0IOAGRNBIO1_L1_TOTAL_REQUESTS_LOWER_ADDRESS          0x15500140UL
#define SMN_IOMMU0PCIE0NBIO0_L1_TOTAL_REQUESTS_LOWER_ADDRESS          0x14700140UL
#define SMN_IOMMU0PCIE0NBIO1_L1_TOTAL_REQUESTS_LOWER_ADDRESS          0x14900140UL
#define SMN_IOMMU0PCIE1NBIO0_L1_TOTAL_REQUESTS_LOWER_ADDRESS          0x14b00140UL
#define SMN_IOMMU0PCIE1NBIO1_L1_TOTAL_REQUESTS_LOWER_ADDRESS          0x14d00140UL
#define SMN_IOMMU1IOAGRNBIO0_L1_TOTAL_REQUESTS_LOWER_ADDRESS          0x15400140UL
#define SMN_IOMMU1IOAGRNBIO1_L1_TOTAL_REQUESTS_LOWER_ADDRESS          0x15600140UL
#define SMN_IOMMU1PCIE0NBIO0_L1_TOTAL_REQUESTS_LOWER_ADDRESS          0x14800140UL
#define SMN_IOMMU1PCIE0NBIO1_L1_TOTAL_REQUESTS_LOWER_ADDRESS          0x14a00140UL
#define SMN_IOMMU1PCIE1NBIO0_L1_TOTAL_REQUESTS_LOWER_ADDRESS          0x14c00140UL
#define SMN_IOMMU1PCIE1NBIO1_L1_TOTAL_REQUESTS_LOWER_ADDRESS          0x14e00140UL


/***********************************************************
* Register Name : L1_TOTAL_REQUESTS_UPPER
************************************************************/

#define L1_TOTAL_REQUESTS_UPPER_L1_TOTAL_REQUESTS_UPPER_OFFSET 0
#define L1_TOTAL_REQUESTS_UPPER_L1_TOTAL_REQUESTS_UPPER_MASK   0xffffffff

typedef union {
  struct {
    UINT32                             L1_TOTAL_REQUESTS_UPPER:32;
  } Field;
  UINT32 Value;
} L1_TOTAL_REQUESTS_UPPER_STRUCT;

#define SMN_L1_TOTAL_REQUESTS_UPPER_ADDRESS                           0x15300144UL
#define SMN_IOMMU0IOAGRNBIO0_L1_TOTAL_REQUESTS_UPPER_ADDRESS          0x15300144UL
#define SMN_IOMMU0IOAGRNBIO1_L1_TOTAL_REQUESTS_UPPER_ADDRESS          0x15500144UL
#define SMN_IOMMU0PCIE0NBIO0_L1_TOTAL_REQUESTS_UPPER_ADDRESS          0x14700144UL
#define SMN_IOMMU0PCIE0NBIO1_L1_TOTAL_REQUESTS_UPPER_ADDRESS          0x14900144UL
#define SMN_IOMMU0PCIE1NBIO0_L1_TOTAL_REQUESTS_UPPER_ADDRESS          0x14b00144UL
#define SMN_IOMMU0PCIE1NBIO1_L1_TOTAL_REQUESTS_UPPER_ADDRESS          0x14d00144UL
#define SMN_IOMMU1IOAGRNBIO0_L1_TOTAL_REQUESTS_UPPER_ADDRESS          0x15400144UL
#define SMN_IOMMU1IOAGRNBIO1_L1_TOTAL_REQUESTS_UPPER_ADDRESS          0x15600144UL
#define SMN_IOMMU1PCIE0NBIO0_L1_TOTAL_REQUESTS_UPPER_ADDRESS          0x14800144UL
#define SMN_IOMMU1PCIE0NBIO1_L1_TOTAL_REQUESTS_UPPER_ADDRESS          0x14a00144UL
#define SMN_IOMMU1PCIE1NBIO0_L1_TOTAL_REQUESTS_UPPER_ADDRESS          0x14c00144UL
#define SMN_IOMMU1PCIE1NBIO1_L1_TOTAL_REQUESTS_UPPER_ADDRESS          0x14e00144UL


/***********************************************************
* Register Name : L1_TOTAL_WAIT_TIME_LOWER
************************************************************/

#define L1_TOTAL_WAIT_TIME_LOWER_L1_TOTAL_WAIT_TIME_LOWER_OFFSET 0
#define L1_TOTAL_WAIT_TIME_LOWER_L1_TOTAL_WAIT_TIME_LOWER_MASK 0xffffffff

typedef union {
  struct {
    UINT32                            L1_TOTAL_WAIT_TIME_LOWER:32;
  } Field;
  UINT32 Value;
} L1_TOTAL_WAIT_TIME_LOWER_STRUCT;

#define SMN_L1_TOTAL_WAIT_TIME_LOWER_ADDRESS                          0x15300138UL
#define SMN_IOMMU0IOAGRNBIO0_L1_TOTAL_WAIT_TIME_LOWER_ADDRESS         0x15300138UL
#define SMN_IOMMU0IOAGRNBIO1_L1_TOTAL_WAIT_TIME_LOWER_ADDRESS         0x15500138UL
#define SMN_IOMMU0PCIE0NBIO0_L1_TOTAL_WAIT_TIME_LOWER_ADDRESS         0x14700138UL
#define SMN_IOMMU0PCIE0NBIO1_L1_TOTAL_WAIT_TIME_LOWER_ADDRESS         0x14900138UL
#define SMN_IOMMU0PCIE1NBIO0_L1_TOTAL_WAIT_TIME_LOWER_ADDRESS         0x14b00138UL
#define SMN_IOMMU0PCIE1NBIO1_L1_TOTAL_WAIT_TIME_LOWER_ADDRESS         0x14d00138UL
#define SMN_IOMMU1IOAGRNBIO0_L1_TOTAL_WAIT_TIME_LOWER_ADDRESS         0x15400138UL
#define SMN_IOMMU1IOAGRNBIO1_L1_TOTAL_WAIT_TIME_LOWER_ADDRESS         0x15600138UL
#define SMN_IOMMU1PCIE0NBIO0_L1_TOTAL_WAIT_TIME_LOWER_ADDRESS         0x14800138UL
#define SMN_IOMMU1PCIE0NBIO1_L1_TOTAL_WAIT_TIME_LOWER_ADDRESS         0x14a00138UL
#define SMN_IOMMU1PCIE1NBIO0_L1_TOTAL_WAIT_TIME_LOWER_ADDRESS         0x14c00138UL
#define SMN_IOMMU1PCIE1NBIO1_L1_TOTAL_WAIT_TIME_LOWER_ADDRESS         0x14e00138UL


/***********************************************************
* Register Name : L1_TOTAL_WAIT_TIME_UPPER
************************************************************/

#define L1_TOTAL_WAIT_TIME_UPPER_L1_TOTAL_WAIT_TIME_UPPER_OFFSET 0
#define L1_TOTAL_WAIT_TIME_UPPER_L1_TOTAL_WAIT_TIME_UPPER_MASK 0xffffffff

typedef union {
  struct {
    UINT32                            L1_TOTAL_WAIT_TIME_UPPER:32;
  } Field;
  UINT32 Value;
} L1_TOTAL_WAIT_TIME_UPPER_STRUCT;

#define SMN_L1_TOTAL_WAIT_TIME_UPPER_ADDRESS                          0x1530013cUL
#define SMN_IOMMU0IOAGRNBIO0_L1_TOTAL_WAIT_TIME_UPPER_ADDRESS         0x1530013cUL
#define SMN_IOMMU0IOAGRNBIO1_L1_TOTAL_WAIT_TIME_UPPER_ADDRESS         0x1550013cUL
#define SMN_IOMMU0PCIE0NBIO0_L1_TOTAL_WAIT_TIME_UPPER_ADDRESS         0x1470013cUL
#define SMN_IOMMU0PCIE0NBIO1_L1_TOTAL_WAIT_TIME_UPPER_ADDRESS         0x1490013cUL
#define SMN_IOMMU0PCIE1NBIO0_L1_TOTAL_WAIT_TIME_UPPER_ADDRESS         0x14b0013cUL
#define SMN_IOMMU0PCIE1NBIO1_L1_TOTAL_WAIT_TIME_UPPER_ADDRESS         0x14d0013cUL
#define SMN_IOMMU1IOAGRNBIO0_L1_TOTAL_WAIT_TIME_UPPER_ADDRESS         0x1540013cUL
#define SMN_IOMMU1IOAGRNBIO1_L1_TOTAL_WAIT_TIME_UPPER_ADDRESS         0x1560013cUL
#define SMN_IOMMU1PCIE0NBIO0_L1_TOTAL_WAIT_TIME_UPPER_ADDRESS         0x1480013cUL
#define SMN_IOMMU1PCIE0NBIO1_L1_TOTAL_WAIT_TIME_UPPER_ADDRESS         0x14a0013cUL
#define SMN_IOMMU1PCIE1NBIO0_L1_TOTAL_WAIT_TIME_UPPER_ADDRESS         0x14c0013cUL
#define SMN_IOMMU1PCIE1NBIO1_L1_TOTAL_WAIT_TIME_UPPER_ADDRESS         0x14e0013cUL

#endif /* _IOMMUL1_H_ */

