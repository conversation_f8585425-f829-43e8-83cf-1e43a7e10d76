/*****************************************************************************
 *
 * Copyright (C) 2008-2024 Advanced Micro Devices, Inc. All rights reserved.
 *
 *******************************************************************************
 */

#ifndef _FCH_ESPI_CMD_PEI_H_
#define _FCH_ESPI_CMD_PEI_H_

#include <Pi/PiPeiCis.h>
#include <Library/DebugLib.h>
#include <Library/PcdLib.h>
#include <Library/BaseMemoryLib.h>
#include <Library/FchBaseLib.h>
#include <Library/DebugLib.h>
#include <Library/FchEspiCmdLib.h>
#include <Ppi/FchEspiCmdPpi.h>

VOID
EFIAPI
PeiInBandRst (
  IN CONST EFI_PEI_ESPI_CMD_PPI  *This,
  IN  UINT32                      EspiBase
  );

UINT32
EFIAPI
PeiGetConfiguration (
  IN CONST EFI_PEI_ESPI_CMD_PPI  *This,
  IN  UINT32                      EspiBase,
  IN  UINT32                      RegAddr
  );

VOID
EFIAPI
PeiSetConfiguration (
  IN CONST EFI_PEI_ESPI_CMD_PPI  *This,
  IN  UINT32                      EspiBase,
  IN  UINT32                      RegAddr,
  IN  UINT32                      Value
  );

EFI_STATUS
EFIAPI
PeiSafsFlashRead (
  IN CONST EFI_PEI_ESPI_CMD_PPI  *This,
  IN  UINT32                      EspiBase,
  IN  UINT32                      Address,
  IN  UINT32                      Length,
  OUT UINT8                       *Buffer
  );

EFI_STATUS
EFIAPI
PeiSafsFlashWrite (
  IN CONST EFI_PEI_ESPI_CMD_PPI  *This,
  IN  UINT32                      EspiBase,
  IN  UINT32                      Address,
  IN  UINT32                      Length,
  IN  UINT8                       *Value
  );

EFI_STATUS
EFIAPI
PeiSafsFlashErase (
  IN CONST EFI_PEI_ESPI_CMD_PPI  *This,
  IN  UINT32                      EspiBase,
  IN  UINT32                      Address,
  IN  UINT32                      Length
  );

EFI_STATUS
EFIAPI
PeiSafsRpmcOp1 (
  IN CONST EFI_PEI_ESPI_CMD_PPI  *This,
  IN  UINT32                      EspiBase,
  IN  UINT8                       RpmcFlashDev,
  IN  UINT32                      Length,
  OUT UINT8                       *Data
  );

EFI_STATUS
EFIAPI
PeiSafsRpmcOp2 (
  IN CONST EFI_PEI_ESPI_CMD_PPI  *This,
  IN  UINT32                      EspiBase,
  IN  UINT8                       RpmcFlashDev,
  IN  UINT32                      Length,
  IN  UINT8                       *Buffer
  );

EFI_STATUS
EFIAPI
AmdFchEspiCmdPeiInit (
  IN       EFI_PEI_FILE_HANDLE FileHandle,
  IN CONST EFI_PEI_SERVICES    **PeiServices
  );

#endif // _FCH_ESPI_CMD_PEI_H_

