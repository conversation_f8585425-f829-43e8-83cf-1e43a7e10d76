/*****************************************************************************
 * Copyright (C) 2019-2024 Advanced Micro Devices, Inc. All rights reserved.
 *
 *******************************************************************************
*/
/* $NoKeywords:$ */
/**
 * @file
 *
 * AMD PSP Runtime PPR Service Protocol prototype definition
 *
 *
 * @xrefitem bom "File Content Label" "Release Content"
 * @e project:      AGESA
 * @e sub-project:  PSP
 * @e \$Revision$   @e \$Date$
 */
#ifndef _AMD_PSP_RUNTIME_PPR_SERVICE_PROTOCOL_H_
#define _AMD_PSP_RUNTIME_PPR_SERVICE_PROTOCOL_H_

/// GUID for the AMD PSP Runtime PPR Service Protocol.
extern EFI_GUID gAmdPspRuntimePprServiceProtocolGuid;

/*----------------------------------------------------------------------------------------
 *                  T Y P E D E F S     A N D     S T R U C T U R E S
 *----------------------------------------------------------------------------------------
 */


/*----------------------------------------------------------------------------------------
 *           P R O T O T Y P E S     O F     L O C A L     F U N C T I O N S
 *----------------------------------------------------------------------------------------
 */
/// Forward declaration for the AMD_PSP_RUNTIME_PPR_SERVICE_PROTOCOL.
typedef struct _AMD_PSP_RUNTIME_PPR_SERVICE_PROTOCOL AMD_PSP_RUNTIME_PPR_SERVICE_PROTOCOL;

/**
 * PSP FW invokes this service to notify the SBIOS that the Runtime PPR is complete.
 *
 * @param[in]  RtPprStatusBuffer   Pointer to the Runtime PPR status returned by ABL through PSP FW.
 *
 * @retval EFI_SUCCESS             SBIOS was successfully notified.
 * @retval Others                  An error occurred while notifying the SBIOS.
 *
 **/
typedef
EFI_STATUS
(EFIAPI *PSP_RUNTIME_PPR_COMPLETION) (
  IN       VOID               *RtPprStatusBuffer
);

struct _AMD_PSP_RUNTIME_PPR_SERVICE_PROTOCOL {
  PSP_RUNTIME_PPR_COMPLETION           PspRuntimePprCompletion; ///< PSP FW invokes this service to notify the SBIOS that the runtime PPR is complete.
};

#endif //_AMD_PSP_RUNTIME_PPR_SERVICE_PROTOCOL_H_
