/*****************************************************************************
 * Copyright (C) 2008-2024 Advanced Micro Devices, Inc. All rights reserved.
 *
*****************************************************************************
*/
/* $NoKeywords:$ */
/**
 * @file
 *
 * AMD Fabric ACPI HMAT.
 *
 * @xrefitem bom "File Content Label" "Release Content"
 * @e project:      AGESA
 * @e sub-project:  Fabric
 * @e \$Revision$   @e \$Date$
 *
 */

/*----------------------------------------------------------------------------------------
 *                             M O D U L E S    U S E D
 *----------------------------------------------------------------------------------------
 */
#include <Porting.h>
#include <AMD.h>
#include <Library/BaseLib.h>
#include "Library/AmdBaseLib.h"
#include <FabricRegistersBrh.h>
#include <FabricInfoBrh.h>
#include <Library/UefiBootServicesTableLib.h>
#include <Library/FabricRegisterAccLib.h>
#include <Library/NbioCxlCdatLib.h>
#include <Library/CoreTopologyV3Lib.h>
#include <Protocol/AmdAcpiHmatServicesProtocol.h>
#include <Protocol/FabricNumaServices2.h>
#include <Protocol/AmdCxlServicesProtocol.h>
#include <Protocol/FabricTopologyServices2.h>
#include <Protocol/AmdCoreTopologyV3Protocol.h>
#include <Protocol/AmdSmbiosServicesProtocol.h>
#include <Protocol/AmdApcbProtocol.h>
#include <Protocol/AmdMemChanXlatProtocol.h>
#include <Filecode.h>
#include <BRH/ApcbV3TokenUid.h>
#include "FabricAcpiDomainInfo.h"
#include "FabricAcpiTable.h"

#define FILECODE FABRIC_BRH_FABRICBRHDXE_FABRICACPIHMAT_FILECODE

/*----------------------------------------------------------------------------------------
 *                   D E F I N I T I O N S    A N D    M A C R O S
 *----------------------------------------------------------------------------------------
 */
#define LATENCY_IN_SAME_SOCKET   110
#define LATENCY_IN_OTHER_SOCKET  225
#define ENTRY_BASE_UNIT_IN_MB    100
#define CXL_DEFAULT_LATENCY      400
#define CXL_DEFAULT_BANDWIDTH  10000

/*----------------------------------------------------------------------------------------
 *           P R O T O T Y P E S     O F     L O C A L     F U N C T I O N S
 *----------------------------------------------------------------------------------------
 */
EFI_STATUS
GetCcxAvailableDomainMap (
  IN  FABRIC_NUMA_SERVICES2_PROTOCOL          *FabricNuma,
  OUT UINT64                                  *CcxAvailableDomainMap
  );

EFI_STATUS
GetDramAvailableDomainMap (
  IN  FABRIC_NUMA_SERVICES2_PROTOCOL          *FabricNuma,
  OUT UINT64                                  *DramAvailableDomainMap
  );

UINT32
GetDramPeakBandwidthInGroupInMB (
  IN DMI_INFO        *DmiInfo,
  IN UINT32          CsMap
  );

UINT32
GetXgmiBandwidthInMB (
  VOID
  );

VOID
GetCxlLatencyBandwidth (
  IN  AMD_NBIO_CXL_SERVICES_PROTOCOL   *CxlServices,
  IN  UINT32                           CsMap,
  OUT UINT32                           *ReadLatency,
  OUT UINT32                           *WriteLatency,
  OUT UINT32                           *ReadBandwidth,
  OUT UINT32                           *WriteBandwidth
  );

/*----------------------------------------------------------------------------------------
 *                           G L O B A L   V A R I A B L E S
 *----------------------------------------------------------------------------------------
 */
UINT32 gMpioXgmiLinkFreqIndexTranslate[] = {
  16000, //DF_XGMI_LINK_SPEED_640   Not supported (set as DF_XGMI_LINK_SPEED_1600)
  16000, //DF_XGMI_LINK_SPEED_746   Not supported (set as DF_XGMI_LINK_SPEED_1600)
  16000, //DF_XGMI_LINK_SPEED_853   Not supported (set as DF_XGMI_LINK_SPEED_1600)
  16000, //DF_XGMI_LINK_SPEED_960   Not supported (set as DF_XGMI_LINK_SPEED_1600)
  16000, //DF_XGMI_LINK_SPEED_1066  Not supported (set as DF_XGMI_LINK_SPEED_1600)
  16000, //DF_XGMI_LINK_SPEED_1100  Not supported (set as DF_XGMI_LINK_SPEED_1600)
  12000, //DF_XGMI_LINK_SPEED_1200
  16000, //DF_XGMI_LINK_SPEED_1300  Not supported (set as DF_XGMI_LINK_SPEED_1600)
  16000, //DF_XGMI_LINK_SPEED_1400  Not supported (set as DF_XGMI_LINK_SPEED_1600)
  16000, //DF_XGMI_LINK_SPEED_1500  Not supported (set as DF_XGMI_LINK_SPEED_1600)
  16000, //DF_XGMI_LINK_SPEED_1600
  17000, //DF_XGMI_LINK_SPEED_1700
  18000, //DF_XGMI_LINK_SPEED_1800
  16000, //DF_XGMI_LINK_SPEED_1900  Not supported (set as DF_XGMI_LINK_SPEED_1600)
  20000, //DF_XGMI_LINK_SPEED_2000
  16000, //DF_XGMI_LINK_SPEED_2100  Not supported (set as DF_XGMI_LINK_SPEED_1600)
  22000, //DF_XGMI_LINK_SPEED_2200
  23000, //DF_XGMI_LINK_SPEED_2300
  24000, //DF_XGMI_LINK_SPEED_2400
  25000, //DF_XGMI_LINK_SPEED_2500
  26000, //DF_XGMI_LINK_SPEED_2600
  27000, //DF_XGMI_LINK_SPEED_2700
  16000, //DF_XGMI_LINK_SPEED_2800  Not supported (set as DF_XGMI_LINK_SPEED_1600)
  16000, //DF_XGMI_LINK_SPEED_2900  Not supported (set as DF_XGMI_LINK_SPEED_1600)
  30000, //DF_XGMI_LINK_SPEED_3000
  16000, //DF_XGMI_LINK_SPEED_3100  Not supported (set as DF_XGMI_LINK_SPEED_1600)
  32000  //DF_XGMI_LINK_SPEED_3200
};

UINT32 TargetDomainReadLatency[MAX_REPORTED_DOMAINS];
UINT32 TargetDomainWriteLatency[MAX_REPORTED_DOMAINS];
UINT32 TargetDomainReadBandwidth[MAX_REPORTED_DOMAINS];
UINT32 TargetDomainWriteBandwidth[MAX_REPORTED_DOMAINS];

/*----------------------------------------------------------------------------------------
 *                  T Y P E D E F S     A N D     S T R U C T U R E S
 *----------------------------------------------------------------------------------------
 */

/*----------------------------------------------------------------------------------------
 *                          E X P O R T E D    F U N C T I O N S
 *----------------------------------------------------------------------------------------
 */

/**
 *---------------------------------------------------------------------------------------
 *
 *  FabricBrhAcpiHmatInstall
 *
 *  Description:
 *    This function will create the contents of ACPI HMAT table
 *
 *  Parameters:
 *    @param[in]     Event      Event whose notification function is being invoked.
 *    @param[in]     Context    Pointer to the notification function's context.
 *
 *    @retval         EFI_SUCCESS                Services protocol installed
 *
 *---------------------------------------------------------------------------------------
 */
VOID
EFIAPI
FabricBrhAcpiHmatInstall (
  IN       EFI_EVENT         Event,
  IN       VOID              *Context
  )
{
  EFI_STATUS                                     Status;
  FABRIC_NUMA_SERVICES2_PROTOCOL                 *FabricNuma;
  AMD_ACPI_HMAT_SERVICES_PROTOCOL                *HmatServices;
  AMD_NBIO_CXL_SERVICES_PROTOCOL                 *CxlServices;
  AMD_MEM_SMBIOS_SERVICES_PROTOCOL               *MemSmbios;
  HMAT_MEM_PROXIMITY_DOMAIN_ATTRIBUTES_FLAGS     MemProxAttributes;
  HMAT_SYS_LOCALITY_LATENCY_BANDWIDTH_INFO_FLAGS SysLocalInfoFlags;
  UINT32                                         NumaNodesPerSocket;
  UINT32                                         NumberOfDomainsInSystem;
  UINT32                                         NumberOfPhysDomainsInSystem;
  DOMAIN_INFO2                                   *DomainInfo;
  PHYS_DOMAIN_INFO                               *PhyDomainInfo;
  BOOLEAN                                        CcxAsNuma;
  UINT32                                         i;
  UINT32                                         j;
  UINT32                                         InitiatorDomain;
  UINT32                                         TargetDomain;
  UINT32                                         InitiatorDomainCount;
  UINT32                                         TargetDomainCount;
  UINT32                                         *InitiatorDomainList;
  UINT32                                         *TargetDomainList;
  UINT16                                         *Entries;
  UINT32                                         Bandwidth;
  UINT32                                         HalfxGMIBandwidth;
  UINT64                                         CcxAvailableDomainMap;
  UINT64                                         DramAvailableDomainMap;
  DMI_INFO                                       *MemDmiInfo;
  DMI_INFO                                       *SortedMemDmiInfo;
  UINT8                                          DataType[2];
  UINT32                                         *EntryPtr[2];
  UINT32                                         EntryCount;
  UINT32                                         EntryIndex;
  BOOLEAN                                        IsSame;
  AMD_MEMCHANXLAT_SERVICES_PROTOCOL              *MemXlatServices;
  UINT8                                          Board;
  UINT8                                          Umc;

  IDS_HDT_CONSOLE (MAIN_FLOW, "%a - Entry\n", __FUNCTION__);

  // Locate required protocols
  Status = gBS->LocateProtocol (&gAmdFabricNumaServices2ProtocolGuid, NULL, (VOID **) &FabricNuma);
  ASSERT (Status == EFI_SUCCESS);
  if (Status != EFI_SUCCESS) {
    return;
  }

  Status = gBS->LocateProtocol (&gAmdAcpiHmatServicesProtocolGuid, NULL, (VOID **) &HmatServices);
  ASSERT (Status == EFI_SUCCESS);
  if (Status != EFI_SUCCESS) {
    return;
  }

  Status = gBS->LocateProtocol (&gAmdNbioCxlServicesProtocolGuid, NULL, (VOID **) &CxlServices);
  if (Status != EFI_SUCCESS) {
    CxlServices = NULL;
  }

  Status = gBS->LocateProtocol (&gAmdMemSmbiosServicesProtocolGuid, NULL, (VOID **) &MemSmbios);
  if (Status != EFI_SUCCESS) {
    return;
  }


  // Get required information
  Status = FabricNuma->GetDomainInfo (FabricNuma, &NumberOfDomainsInSystem, &DomainInfo, &CcxAsNuma);
  ASSERT (Status == EFI_SUCCESS);
  if (Status != EFI_SUCCESS) {
    return;
  }

  Status = FabricNuma->GetPhysDomainInfo (FabricNuma, &NumberOfPhysDomainsInSystem, &PhyDomainInfo, &NumaNodesPerSocket, NULL);
  ASSERT (Status == EFI_SUCCESS);
  if (Status != EFI_SUCCESS) {
    return;
  }

  Status = gBS->AllocatePool (EfiBootServicesData, sizeof (DMI_INFO), (VOID **) &MemDmiInfo);
  ASSERT (Status == EFI_SUCCESS);
  if (Status != EFI_SUCCESS) {
    return;
  }
  Status = MemSmbios->GetMemDmiInfo (MemSmbios, MemDmiInfo);
  ASSERT (Status == EFI_SUCCESS);
  if (Status != EFI_SUCCESS) {
    gBS->FreePool (MemDmiInfo);
    return;
  }

  Status = gBS->AllocatePool (EfiBootServicesData, sizeof (DMI_INFO), (VOID **) &SortedMemDmiInfo);
  ASSERT (Status == EFI_SUCCESS);
  if (Status != EFI_SUCCESS) {
    return;
  }

  // Translate the DMI Info from the board channel index to the UMC channel index
  Status = gBS->LocateProtocol (&gAmdMemChanXLatProtocolGuid, NULL, (VOID **) &MemXlatServices);
  if (Status == EFI_SUCCESS) {
    gBS->SetMem (SortedMemDmiInfo, sizeof (DMI_INFO), 0);
    for (Board = 0; Board < BRH_NUM_CS_UMC_BLOCKS; Board++) {
      Status = MemXlatServices->GetMemUmcInfo (MemXlatServices, Board, &Umc);
      if ((Status == EFI_SUCCESS) && (Umc != 0xFF)) {
        ASSERT (Umc < MAX_CHANNELS_PER_SOCKET);
        if (Umc < MAX_CHANNELS_PER_SOCKET) {
          for (i = 0; i < MAX_SOCKETS_SUPPORTED; i++) {
            for (j = 0; j < MAX_DIMMS_PER_CHANNEL; j++) {
              gBS->CopyMem (&SortedMemDmiInfo->T17[i][Umc][j], &MemDmiInfo->T17[i][Board][j], sizeof (TYPE17_DMI_INFO));
            }
          }
        }
      }
    }
  } else {
    ASSERT_EFI_ERROR (Status);
    gBS->CopyMem (SortedMemDmiInfo, MemDmiInfo, sizeof (DMI_INFO));
  }
  gBS->FreePool (MemDmiInfo);

  if (GetCcxAvailableDomainMap (FabricNuma, &CcxAvailableDomainMap) != EFI_SUCCESS) {
    CcxAvailableDomainMap = (UINT64)-1;
  }
  IDS_HDT_CONSOLE (MAIN_FLOW, "  CcxAvailableDomainMap %x\n", CcxAvailableDomainMap);

  if (GetDramAvailableDomainMap (FabricNuma, &DramAvailableDomainMap) != EFI_SUCCESS) {
    DramAvailableDomainMap = (UINT64)-1;
  }
  IDS_HDT_CONSOLE (MAIN_FLOW, "  DramAvailableDomainMap %x\n", DramAvailableDomainMap);

  HalfxGMIBandwidth = GetXgmiBandwidthInMB () / 2;
  IDS_HDT_CONSOLE (MAIN_FLOW, "  HalfxGMIBandwidth %d\n", HalfxGMIBandwidth);


  // Calculate DRAM and CXL domain count, and add Proximity Domain Attributes structures
  InitiatorDomainCount = 0;
  TargetDomainCount = 0;
  MemProxAttributes.Value = 0;
  for (i = 0; i < NumberOfDomainsInSystem; i++) {
    MemProxAttributes.Fields.InitiatorProximityDomainValid = 0;
    if (DomainInfo[i].Type == NumaDram) {
      if (CcxAvailableDomainMap & LShiftU64 (1, i)) {
        InitiatorDomainCount++;
        MemProxAttributes.Fields.InitiatorProximityDomainValid = 1;
      }
      if (DramAvailableDomainMap & LShiftU64 (1, i)) {
        TargetDomainCount++;
        Status = HmatServices->AddMemoryProximityDomainAttributes (HmatServices, MemProxAttributes, i, i);
        ASSERT (Status == EFI_SUCCESS);
      }
    }
    if (DomainInfo[i].Type == NumaCxl) {
      if (CxlServices != NULL) {
        TargetDomainCount++;
        Status = HmatServices->AddMemoryProximityDomainAttributes (HmatServices, MemProxAttributes, i, i);
        ASSERT (Status == EFI_SUCCESS);
      }
    }
  }


  // Prepare the buffers
  Status = gBS->AllocatePool (EfiBootServicesData, sizeof (InitiatorDomainList[0]) * InitiatorDomainCount, (VOID **) &InitiatorDomainList);
  ASSERT (Status == EFI_SUCCESS);
  if (Status != EFI_SUCCESS) {
    InitiatorDomainList = NULL;
  }
  Status = gBS->AllocatePool (EfiBootServicesData, sizeof (TargetDomainList[0]) * TargetDomainCount, (VOID **) &TargetDomainList);
  ASSERT (Status == EFI_SUCCESS);
  if (Status != EFI_SUCCESS) {
    TargetDomainList = NULL;
  }
  Status = gBS->AllocatePool (EfiBootServicesData, sizeof (Entries[0]) * InitiatorDomainCount * TargetDomainCount, (VOID **) &Entries);
  ASSERT (Status == EFI_SUCCESS);
  if (Status != EFI_SUCCESS) {
    Entries = NULL;
  }


  if (InitiatorDomainList != NULL && TargetDomainList != NULL && Entries != NULL) {
    // Prepare the Initiator Domain List and Target Domain List
    for (i = 0, j = 0; i < NumberOfDomainsInSystem; i++) {
      if (DomainInfo[i].Type == NumaDram) {
        if (CcxAvailableDomainMap & LShiftU64 (1, i)) {
          InitiatorDomainList[j] = i;
          j++;
        }
      }
    }
    ASSERT (j == InitiatorDomainCount);
    for (i = 0, j = 0; i < NumberOfDomainsInSystem; i++) {
      if (DomainInfo[i].Type == NumaDram) {
        if (DramAvailableDomainMap & LShiftU64 (1, i)) {
          TargetDomainList[j] = i;
          j++;
        }
      } else if (DomainInfo[i].Type == NumaCxl) {
        if (CxlServices != NULL) {
          TargetDomainList[j] = i;
          j++;
        }
      } else {
        ASSERT (FALSE);
      }
    }
    ASSERT (j == TargetDomainCount);


    // Calculate the target latency and bandwidth of each domain
    gBS->SetMem (TargetDomainReadLatency, sizeof (TargetDomainReadLatency), 0);
    gBS->SetMem (TargetDomainWriteLatency, sizeof (TargetDomainWriteLatency), 0);
    gBS->SetMem (TargetDomainReadBandwidth, sizeof (TargetDomainReadBandwidth), 0);
    gBS->SetMem (TargetDomainWriteBandwidth, sizeof (TargetDomainWriteBandwidth), 0);
    for (i = 0; i < TargetDomainCount; i++) {
      TargetDomain = TargetDomainList[i];
      if (DomainInfo[TargetDomain].Type == NumaDram) {
        TargetDomainReadBandwidth[i]  = GetDramPeakBandwidthInGroupInMB (
                                          SortedMemDmiInfo,
                                          PhyDomainInfo[DomainInfo[TargetDomain].PhysicalDomain].NormalizedCsMap
                                          );
        TargetDomainReadBandwidth[i]  = TargetDomainReadBandwidth[i] * 3 / 4;   // 75% of peak DRAM b/w populated in target NUMA node
        TargetDomainWriteBandwidth[i] = TargetDomainReadBandwidth[i];           // Same bandwidth in read / write for DRAM
        TargetDomainReadLatency[i]    = 0;                                      // no extra latency in the SoC
        TargetDomainWriteLatency[i]   = 0;                                      // no extra latency in the SoC
      } else if (DomainInfo[TargetDomain].Type == NumaCxl) {
        if (CxlServices != NULL) {
          GetCxlLatencyBandwidth(
            CxlServices,
            PhyDomainInfo[DomainInfo[TargetDomain].PhysicalDomain].NormalizedCsMap,
            &TargetDomainReadLatency[i],
            &TargetDomainWriteLatency[i],
            &TargetDomainReadBandwidth[i],
            &TargetDomainWriteBandwidth[i]
            );
          TargetDomainReadBandwidth[i]  = TargetDomainReadBandwidth[i]  / 2; // 50% of CXL_reported_bandwidth
          TargetDomainWriteBandwidth[i] = TargetDomainWriteBandwidth[i] / 2; // 50% of CXL_reported_bandwidth
        }
      } else {
        ASSERT (FALSE);
      }
    }


    // Prepare the latency table
    IsSame = TRUE;
    for (i = 0; i < TargetDomainCount; i++) {
      if (TargetDomainReadLatency[i] != TargetDomainWriteLatency[i]) {
        IsSame = FALSE;
        break;
      }
    }
    if (IsSame) {
      EntryCount = 1;
      DataType[0] = HMAT_SYSTEM_LOCALITY_LATENCY_AND_BANDWIDTH_INFO_DATA_TYPE_ACCESS_LATENCY;
      EntryPtr[0] = &TargetDomainReadLatency[0];
    } else {
      EntryCount = 2;
      DataType[0] = HMAT_SYSTEM_LOCALITY_LATENCY_AND_BANDWIDTH_INFO_DATA_TYPE_READ_LATENCY;
      EntryPtr[0] = &TargetDomainReadLatency[0];
      DataType[1] = HMAT_SYSTEM_LOCALITY_LATENCY_AND_BANDWIDTH_INFO_DATA_TYPE_WRITE_LATENCY;
      EntryPtr[1] = &TargetDomainWriteLatency[0];
    }
    for (EntryIndex = 0; EntryIndex < EntryCount; EntryIndex++) {
      gBS->SetMem (Entries, sizeof (Entries[0]) * InitiatorDomainCount * TargetDomainCount, 0);
      for (i = 0; i < InitiatorDomainCount; i++) {
        for (j = 0; j < TargetDomainCount; j++) {
          InitiatorDomain = InitiatorDomainList[i];
          TargetDomain = TargetDomainList[j];
          Entries[i * TargetDomainCount + j] = (UINT16) EntryPtr[EntryIndex][j];
          if (DomainInfo[InitiatorDomain].SocketMap & DomainInfo[TargetDomain].SocketMap) {
            Entries[i * TargetDomainCount + j] += LATENCY_IN_SAME_SOCKET;
          } else {
            Entries[i * TargetDomainCount + j] += LATENCY_IN_OTHER_SOCKET;
          }
        }
      }
      SysLocalInfoFlags.Value = 0;
      Status = HmatServices->AddSystemLocalityLatencyBandwidthInfo (
                              HmatServices,
                              SysLocalInfoFlags,
                              DataType[EntryIndex],
                              0,                         // MinTransferSize
                              InitiatorDomainCount,
                              TargetDomainCount,
                              1000,                      // 1000 pecoseconds is 1 nanosecond
                              InitiatorDomainList,
                              TargetDomainList,
                              Entries
                              );
      ASSERT (Status == EFI_SUCCESS);
    }


    // Prepare the bandwidth table
    IsSame = TRUE;
    for (i = 0; i < TargetDomainCount; i++) {
      if (TargetDomainReadBandwidth[i] != TargetDomainWriteBandwidth[i]) {
        IsSame = FALSE;
        break;
      }
    }
    if (IsSame) {
      EntryCount = 1;
      DataType[0] = HMAT_SYSTEM_LOCALITY_LATENCY_AND_BANDWIDTH_INFO_DATA_TYPE_ACCESS_BANDWIDTH;
      EntryPtr[0] = &TargetDomainReadBandwidth[0];
    } else {
      EntryCount = 2;
      DataType[0] = HMAT_SYSTEM_LOCALITY_LATENCY_AND_BANDWIDTH_INFO_DATA_TYPE_READ_BANDWIDTH;
      EntryPtr[0] = &TargetDomainReadBandwidth[0];
      DataType[1] = HMAT_SYSTEM_LOCALITY_LATENCY_AND_BANDWIDTH_INFO_DATA_TYPE_WRITE_BANDWIDTH;
      EntryPtr[1] = &TargetDomainWriteBandwidth[0];
    }
    for (EntryIndex = 0; EntryIndex < EntryCount; EntryIndex++) {
      gBS->SetMem (Entries, sizeof (Entries[0]) * InitiatorDomainCount * TargetDomainCount, 0);
      for (i = 0; i < InitiatorDomainCount; i++) {
        for (j = 0; j < TargetDomainCount; j++) {
          InitiatorDomain = InitiatorDomainList[i];
          TargetDomain = TargetDomainList[j];
          Bandwidth = EntryPtr[EntryIndex][j];
          if ((DomainInfo[InitiatorDomain].SocketMap & DomainInfo[TargetDomain].SocketMap) == 0) {
            // In other socket
            // min(50% of peak xGMI b/w, 75% of peak target NUMA node b/w or 70% of CXL_reported_bandwidth)
            Bandwidth = Bandwidth < HalfxGMIBandwidth ? Bandwidth : HalfxGMIBandwidth;
          }
          Entries[i * TargetDomainCount + j] = (UINT16) (Bandwidth / ENTRY_BASE_UNIT_IN_MB);
        }
      }
      SysLocalInfoFlags.Value = 0;
      Status = HmatServices->AddSystemLocalityLatencyBandwidthInfo (
                              HmatServices,
                              SysLocalInfoFlags,
                              DataType[EntryIndex],
                              0,                         // MinTransferSize
                              InitiatorDomainCount,
                              TargetDomainCount,
                              ENTRY_BASE_UNIT_IN_MB,
                              InitiatorDomainList,
                              TargetDomainList,
                              Entries
                              );
      ASSERT (Status == EFI_SUCCESS);
    }
  } //if (InitiatorDomainList != NULL && TargetDomainList != NULL && Entries != NULL) {


  // Free the buffers
  if (InitiatorDomainList) {
    gBS->FreePool (InitiatorDomainList);
  }
  if (TargetDomainList) {
    gBS->FreePool (TargetDomainList);
  }
  if (Entries) {
    gBS->FreePool (Entries);
  }
  gBS->FreePool (SortedMemDmiInfo);

  IDS_HDT_CONSOLE (MAIN_FLOW, "%a - Exit\n", __FUNCTION__);
}

/**
 *---------------------------------------------------------------------------------------
 *
 *  GetCcxAvailableDomainMap
 *
 *  Description:
 *    Return the domain bitmap that CCX is available in the domain
 *
 *  Parameters:
 *    @param[in]     FabricNuma              Pointer to the FABRIC_NUMA_SERVICES2_PROTOCOL
 *    @param[out]    CcxAvailableDomainMap   The returned bitmap
 *
 *    @retval        EFI_SUCCESS             The bitmap is returned successfully
 *    @retval        EFI_INVALID_PARAMETER   Failed to get the bitmap
 *
 *---------------------------------------------------------------------------------------
 */
EFI_STATUS
GetCcxAvailableDomainMap (
  IN  FABRIC_NUMA_SERVICES2_PROTOCOL          *FabricNuma,
  OUT UINT64                                  *CcxAvailableDomainMap
  )
{
  UINTN                                    SocketLoop;
  UINTN                                    DieLoop;
  UINTN                                    CcdLoop;
  UINTN                                    ComplexLoop;
  UINT32                                   Domain;
  EFI_STATUS                               CalledStatus;
  UINT32                                   TotalDomainNumber;
  AMD_CORE_TOPOLOGY_SERVICES_V3_PROTOCOL   *CoreTopologyServices;
  CORE_TOPOLOGY_ITERATION_RESULT           IterationResult;

  if (!FabricNuma || !CcxAvailableDomainMap) {
    return EFI_INVALID_PARAMETER;
  }

  *CcxAvailableDomainMap = 0;

  CalledStatus = gBS->LocateProtocol (&gAmdCoreTopologyServicesV3ProtocolGuid, NULL, (VOID **)&CoreTopologyServices);
  ASSERT (CalledStatus == EFI_SUCCESS);
  if (CalledStatus != EFI_SUCCESS) {
    return EFI_INVALID_PARAMETER;
  }

  CalledStatus = FabricNuma->GetDomainInfo (FabricNuma, &TotalDomainNumber, NULL, NULL);
  ASSERT (CalledStatus == EFI_SUCCESS);
  if (CalledStatus != EFI_SUCCESS) {
    return EFI_INVALID_PARAMETER;
  }

  CORE_TOPOLOGY_V3_FOR_EACH_COMPLEX (CoreTopologyServices, IterationResult, SocketLoop, DieLoop, CcdLoop, ComplexLoop) {
    if (FabricNuma->DomainXlat (FabricNuma, SocketLoop, DieLoop, CcdLoop, ComplexLoop, &Domain) == EFI_SUCCESS) {
      ASSERT (Domain < TotalDomainNumber);
      if (Domain < TotalDomainNumber) {
        *CcxAvailableDomainMap |= LShiftU64 (1, Domain);
      }
    }
  }

  return EFI_SUCCESS;
}

/**
 *---------------------------------------------------------------------------------------
 *
 *  GetDramAvailableDomainMap
 *
 *  Description:
 *    Return the domain bitmap that DRAM is available in the domain
 *
 *  Parameters:
 *    @param[in]     FabricNuma              Pointer to the FABRIC_NUMA_SERVICES2_PROTOCOL
 *    @param[out]    DramAvailableDomainMap  The returned bitmap
 *
 *    @retval        EFI_SUCCESS             The bitmap is returned successfully
 *    @retval        EFI_INVALID_PARAMETER   Failed to get the bitmap
 *
 *---------------------------------------------------------------------------------------
 */
EFI_STATUS
GetDramAvailableDomainMap (
  IN  FABRIC_NUMA_SERVICES2_PROTOCOL          *FabricNuma,
  OUT UINT64                                  *DramAvailableDomainMap
  )
{
  EFI_STATUS                                     Status;
  UINT32                                         NumaNodesPerSocket;
  UINT32                                         NumberOfDomainsInSystem;
  UINT32                                         NumberOfPhysDomainsInSystem;
  DOMAIN_INFO2                                   *DomainInfo;
  PHYS_DOMAIN_INFO                               *PhyDomainInfo;
  UINT32                                         i;
  UINT32                                         CsMap;
  UINT32                                         Socket;
  UINT32                                         CsUmc;
  DRAM_ADDRESS_CTL_REGISTER                      DramAddressCtl;

  if (!FabricNuma || !DramAvailableDomainMap) {
    return EFI_INVALID_PARAMETER;
  }

  *DramAvailableDomainMap = 0;

  Status = FabricNuma->GetDomainInfo (FabricNuma, &NumberOfDomainsInSystem, &DomainInfo, NULL);
  ASSERT (Status == EFI_SUCCESS);
  if (Status != EFI_SUCCESS) {
    return EFI_INVALID_PARAMETER;
  }

  Status = FabricNuma->GetPhysDomainInfo (FabricNuma, &NumberOfPhysDomainsInSystem, &PhyDomainInfo, &NumaNodesPerSocket, NULL);
  ASSERT (Status == EFI_SUCCESS);
  if (Status != EFI_SUCCESS) {
    return EFI_INVALID_PARAMETER;
  }

  for (i = 0; i < NumberOfDomainsInSystem; i++) {
    if (DomainInfo[i].Type != NumaDram) {
      continue;
    }
    ASSERT (DomainInfo[i].PhysicalDomain < NumberOfPhysDomainsInSystem);
    CsMap = PhyDomainInfo[DomainInfo[i].PhysicalDomain].NormalizedCsMap;
    for (Socket = 0; Socket < BRH_MAX_SOCKETS; Socket++) {
      for (CsUmc = 0; CsUmc < BRH_NUM_CS_UMC_BLOCKS; CsUmc++) {
        if (CsMap & (1 << (CsUmc + (Socket * NORMALIZED_SOCKET_SHIFT)))) {
          DramAddressCtl.Value = FabricRegisterAccRead (Socket, 0, DRAMADDRESSCTL_0_FUNC, DRAMADDRESSCTL_0_REG, BRH_CS0_INSTANCE_ID + CsUmc);
          if (DramAddressCtl.Field.AddrRngVal == 1) {
            *DramAvailableDomainMap |= LShiftU64 (1, i);
            Socket = BRH_MAX_SOCKETS; //To break the socket for loop
            break;
          }
        }
      }
    }
  }

  return EFI_SUCCESS;
}

/**
 *---------------------------------------------------------------------------------------
 *
 *  GetDramFreq
 *
 *  Description:
 *    Get the Dram frequency of given socket number and channel number
 *
 *  Parameters:
 *    @param[in]     DmiInfo                 The DMI information buffer
 *    @param[in]     Socket                  The socket number
 *    @param[in]     Channel                 The channel number
 *
 *    @return The DRAM frequency
 *
 *---------------------------------------------------------------------------------------
 */
UINT32
GetDramFreq (
  IN DMI_INFO        *DmiInfo,
  IN UINT32          Socket,
  IN UINT32          Channel
  )
{
  UINT32 Dimm;
  UINT32 Freq;

  if (Socket >= BRH_MAX_SOCKETS) {
    return 0;
  }
  if (Channel >= BRH_NUM_CS_UMC_BLOCKS) {
    return 0;
  }

  Freq = (UINT32) -1;
  for (Dimm = 0; Dimm < MAX_DIMMS_PER_CHANNEL; Dimm++) {
    if ((DmiInfo->T17[Socket][Channel][Dimm].MemorySize != 0) && (DmiInfo->T17[Socket][Channel][Dimm].MemoryType != UnknownMemType)) {
      if (DmiInfo->T17[Socket][Channel][Dimm].ConfigSpeed < Freq) {
        Freq = DmiInfo->T17[Socket][Channel][Dimm].ConfigSpeed;
      }
    }
  }
  if (Freq == (UINT32) -1) {
    Freq = 0;
  }

  Freq = Freq * 2; //double the rate for DDR
  IDS_HDT_CONSOLE (MAIN_FLOW, "  Socket %d  Channel %d  Freq = %d\n", Socket, Channel, Freq);


  return Freq;
}

/**
 *---------------------------------------------------------------------------------------
 *
 *  GetDramPeakBandwidthInGroupInMB
 *
 *  Description:
 *    Get the peak Dram frequency of the group of given CsMap
 *
 *  Parameters:
 *    @param[in]     DmiInfo                 The DMI information buffer
 *    @param[in]     CsMap                   The CS bitmap
 *
 *    @return The DRAM frequency of the group
 *
 *---------------------------------------------------------------------------------------
 */
UINT32
GetDramPeakBandwidthInGroupInMB (
  IN DMI_INFO        *DmiInfo,
  IN UINT32          CsMap
  )
{
  UINT32    Socket;
  UINT32    CsUmc;
  UINT32    ChannelCount;
  UINT32    MinFreq;
  UINT32    Freq;

  ChannelCount = 0;
  MinFreq = (UINT32) -1;
  for (Socket = 0; Socket < BRH_MAX_SOCKETS; Socket++) {
    for (CsUmc = 0; CsUmc < BRH_NUM_CS_UMC_BLOCKS; CsUmc++) {
      if (CsMap & (1 << (CsUmc + (Socket * NORMALIZED_SOCKET_SHIFT)))) {
        Freq = GetDramFreq (DmiInfo, Socket, CsUmc);
        if (Freq) {
          ChannelCount++;
          if (Freq < MinFreq) {
            MinFreq = Freq;
          }
        }
      }
    }
  }
  IDS_HDT_CONSOLE (MAIN_FLOW, "  CsMap %x  MinFreq %d  ChannelCount = %d\n\n", CsMap, MinFreq, ChannelCount);

  // DRAM B/W = # of DRAM channels x min DDR x 8B.
  // E.g., for DDR5-4800 in 12-ch (NPS1): DRAM B/W = 12 x 4.8 GT/s x 8B = 460.8 GB/s.
  return MinFreq * ChannelCount * 8;
}

/**
 *---------------------------------------------------------------------------------------
 *
 *  GetXgmiBandwidthInMB
 *
 *  Description:
 *    Get the xGMI bandwidth
 *
 *    @return The xGMI bandwidth in MB/s
 *
 *---------------------------------------------------------------------------------------
 */
UINT32
GetXgmiBandwidthInMB (
  VOID
  )
{
  EFI_STATUS                   Status;
  AMD_APCB_SERVICE_PROTOCOL    *ApcbDxeServiceProtocol;
  UINT8                        ApcbPurpose;
  UINT8                        Apcb8;
  UINT32                       LinkCount;
  UINT32                       Speed;
  UINT32                       SpeedTokenId;

  //TODO Get actual speed instead of APCB

  Status = gBS->LocateProtocol (&gAmdApcbDxeServiceProtocolGuid, NULL, (VOID **) &ApcbDxeServiceProtocol);
  ASSERT(!EFI_ERROR(Status));
  if (EFI_ERROR (Status)) {
    LinkCount = 4;
    Speed = gMpioXgmiLinkFreqIndexTranslate[DF_XGMI_LINK_SPEED_1600];
  } else {
    Status = ApcbDxeServiceProtocol->ApcbGetToken8 (ApcbDxeServiceProtocol, &ApcbPurpose, APCB_TOKEN_UID_DF3_XGMI2_LINK_CFG, &Apcb8);
    if (EFI_ERROR (Status)) {
      Apcb8 = DF_XGMI_LINK_CFG_AUTO;
    }
    switch (Apcb8) {
    case DF_XGMI_LINK_CFG_3XGMILINKS:
      LinkCount = 3;
      SpeedTokenId = APCB_TOKEN_UID_DF_3LINK_MAX_XGMI_SPEED;
      break;
    default:
      LinkCount = 4;
      SpeedTokenId = APCB_TOKEN_UID_DF_4LINK_MAX_XGMI_SPEED;
    }

    Status = ApcbDxeServiceProtocol->ApcbGetToken8 (ApcbDxeServiceProtocol, &ApcbPurpose, SpeedTokenId, &Apcb8);
    if (EFI_ERROR (Status)) {
      Apcb8 = DF_XGMI_LINK_SPEED_1600;
    }
    if (Apcb8 >= sizeof (gMpioXgmiLinkFreqIndexTranslate) / sizeof (gMpioXgmiLinkFreqIndexTranslate[0])) {
        Apcb8 = DF_XGMI_LINK_SPEED_1600;
    }
    Speed = gMpioXgmiLinkFreqIndexTranslate[Apcb8];
  }

  // Depends on xGMI speed & # of links.
  // xGMI B/W = # of xGMI links x xGMI lanes x 2 directions x xGMI speed / 8 .
  // E.g., for 32 Gbps xGMI with four x16 links: xGMI B/W = 4 x 16 x 2 x 32 Gbps / 8 = 512 GB/s..
  return LinkCount * 16 * 2 * Speed;
}

/**
 *---------------------------------------------------------------------------------------
 *
 *  GetCxlLatencyBandwidth
 *
 *  Description:
 *    Get the CXL latency and bandwidth in the given CsMap group with the contents in CDAT
 *
 *    @param[in]     CxlServices             The pointer to AMD_NBIO_CXL_SERVICES_PROTOCOL
 *    @param[in]     CsMap                   The CS bitmap
 *    @param[out]    ReadLatency             The pointer to the read latency value
 *    @param[out]    WriteLatency            The pointer to the write latency value
 *    @param[out]    ReadBandwidth           The pointer to the read bandwidth value
 *    @param[out]    WriteBandwidth          The pointer to the write bandwidth value
 *
 *---------------------------------------------------------------------------------------
 */
VOID
GetCxlLatencyBandwidth (
  IN  AMD_NBIO_CXL_SERVICES_PROTOCOL   *CxlServices,
  IN  UINT32                           CsMap,
  OUT UINT32                           *ReadLatency,
  OUT UINT32                           *WriteLatency,
  OUT UINT32                           *ReadBandwidth,
  OUT UINT32                           *WriteBandwidth
  )
{
  UINT32                              Socket;
  UINT32                              Index;
  UINT32                              CdatIndex;
  AMD_CXL_PORT_INFO_STRUCT            NbioPortInfo;
  EFI_STATUS                          Status;
  CDAT_TABLE                          CdatTable;
  DSLBIS_CDAT                         Dslbis[MAX_DSLBIS_RECORDS_PER_CXL_DEVICE];
  UINT32                              *CdatReadLatency;
  UINT32                              *CdatWriteLatency;
  UINT32                              *CdatReadBandwidth;
  UINT32                              *CdatWriteBandwidth;
  UINT32                              CdatLatencyCount;
  UINT32                              CdatBandwidthCount;
  UINT32                              TempAccessLatency;
  UINT32                              TempReadLatency;
  UINT32                              TempWriteLatency;
  UINT32                              TempAccessBandwidth;
  UINT32                              TempReadBandwidth;
  UINT32                              TempWriteBandwidth;
  UINT64                              Value;

  if (ReadLatency) {
    *ReadLatency = 0;
  }
  if (WriteLatency) {
    *WriteLatency = 0;
  }
  if (ReadBandwidth) {
    *ReadBandwidth = 0;
  }
  if (WriteBandwidth) {
    *WriteBandwidth = 0;
  }
  if (!CxlServices) {
    return;
  }

  Status = gBS->AllocatePool (EfiBootServicesData, sizeof (CdatReadLatency[0]) * CxlServices->CxlCount * BRH_MAX_SOCKETS, (VOID **)&CdatReadLatency);
  ASSERT (Status == EFI_SUCCESS);
  if (Status != EFI_SUCCESS) {
    CdatReadLatency = NULL;
  }
  Status = gBS->AllocatePool (EfiBootServicesData, sizeof (CdatWriteLatency[0]) * CxlServices->CxlCount * BRH_MAX_SOCKETS, (VOID **)&CdatWriteLatency);
  ASSERT (Status == EFI_SUCCESS);
  if (Status != EFI_SUCCESS) {
    CdatWriteLatency = NULL;
  }
  Status = gBS->AllocatePool (EfiBootServicesData, sizeof (CdatReadBandwidth[0]) * CxlServices->CxlCount * BRH_MAX_SOCKETS, (VOID **)&CdatReadBandwidth);
  ASSERT (Status == EFI_SUCCESS);
  if (Status != EFI_SUCCESS) {
    CdatReadBandwidth = NULL;
  }
  Status = gBS->AllocatePool (EfiBootServicesData, sizeof (CdatWriteBandwidth[0]) * CxlServices->CxlCount * BRH_MAX_SOCKETS, (VOID **)&CdatWriteBandwidth);
  ASSERT (Status == EFI_SUCCESS);
  if (Status != EFI_SUCCESS) {
    CdatWriteBandwidth = NULL;
  }

  CdatLatencyCount = 0;
  CdatBandwidthCount = 0;
  if (CdatReadLatency && CdatWriteLatency && CdatReadBandwidth && CdatWriteBandwidth) {
    for (Socket = 0; Socket < BRH_MAX_SOCKETS; Socket++) {
      if ((CsMap & ((1 << NORMALIZED_SOCKET_SHIFT) - 1) << (NORMALIZED_SOCKET_SHIFT * Socket)) == 0) {
        continue;
      }

      for (Index = 0; Index < CxlServices->CxlCount; Index++) {
        Status = CxlServices->CxlGetRootPortInformation (
                  CxlServices,
                  Index,
                  &NbioPortInfo
                  );
        if (Status != EFI_SUCCESS) {
          continue;
        }

        if (Socket != NbioPortInfo.SocketID) {
          continue;
        }

        TempAccessLatency   = 0;
        TempReadLatency     = 0;
        TempWriteLatency    = 0;
        TempAccessBandwidth = 0;
        TempReadBandwidth   = 0;
        TempWriteBandwidth  = 0;

        IDS_HDT_CONSOLE (MAIN_FLOW, "CXL Port %u\n", Index);
        gBS->SetMem (&CdatTable, sizeof (CdatTable), 0);
        Status = CxlGetCdat (NbioPortInfo.EndPointBDF.AddressValue, &CdatTable);
        if (Status == EFI_SUCCESS) {
          gBS->SetMem (&Dslbis, sizeof (Dslbis), 0);
          Status = CxlParseCdat (NbioPortInfo.EndPointBDF.AddressValue, CdatTable, CdatTypeDslbis, Dslbis);
          if (Status == EFI_SUCCESS) {
            for (CdatIndex = 0; CdatIndex < MAX_DSLBIS_RECORDS_PER_CXL_DEVICE; CdatIndex++) {
              if (Dslbis[CdatIndex].Length == 0) {
                continue;
              }
              Value =  ((UINT64) Dslbis[CdatIndex].Entry[0]) * Dslbis[CdatIndex].EntryBaseUnit;
              switch (Dslbis[CdatIndex].DataType) {
              case HMAT_SYSTEM_LOCALITY_LATENCY_AND_BANDWIDTH_INFO_DATA_TYPE_ACCESS_LATENCY:
                TempAccessLatency = (UINT32) Value;
                break;
              case HMAT_SYSTEM_LOCALITY_LATENCY_AND_BANDWIDTH_INFO_DATA_TYPE_READ_LATENCY:
                TempReadLatency = (UINT32) Value;
                break;
              case HMAT_SYSTEM_LOCALITY_LATENCY_AND_BANDWIDTH_INFO_DATA_TYPE_WRITE_LATENCY:
                TempWriteLatency = (UINT32) Value;
                break;
              case HMAT_SYSTEM_LOCALITY_LATENCY_AND_BANDWIDTH_INFO_DATA_TYPE_ACCESS_BANDWIDTH:
                TempAccessBandwidth = (UINT32) Value;
                break;
              case HMAT_SYSTEM_LOCALITY_LATENCY_AND_BANDWIDTH_INFO_DATA_TYPE_READ_BANDWIDTH:
                TempReadBandwidth = (UINT32) Value;
                break;
              case HMAT_SYSTEM_LOCALITY_LATENCY_AND_BANDWIDTH_INFO_DATA_TYPE_WRITE_BANDWIDTH:
                TempWriteBandwidth = (UINT32) Value;
                break;
              default:
                ASSERT (FALSE);
              }
            }
          }
          IDS_HDT_CONSOLE (MAIN_FLOW, "  CDAT DSLBIS\n");
          IDS_HDT_CONSOLE (MAIN_FLOW, "    AccessLatency   = %u\n", TempAccessLatency);
          IDS_HDT_CONSOLE (MAIN_FLOW, "    ReadLatency     = %u\n", TempReadLatency);
          IDS_HDT_CONSOLE (MAIN_FLOW, "    WriteLatency    = %u\n", TempWriteLatency);
          IDS_HDT_CONSOLE (MAIN_FLOW, "    AccessBandwidth = %u\n", TempAccessBandwidth);
          IDS_HDT_CONSOLE (MAIN_FLOW, "    ReadBandwidth   = %u\n", TempReadBandwidth);
          IDS_HDT_CONSOLE (MAIN_FLOW, "    WriteBandwidth  = %u\n", TempWriteBandwidth);
          if (CdatTable.Entries != NULL) {
            gBS->FreePool (CdatTable.Entries);
          }
        }

        if (TempAccessLatency == 0) {
          TempAccessLatency = CXL_DEFAULT_LATENCY;
        }
        // Use the access latency if no read latency or write latency
        // Save the latency for the CXL device
        if (TempReadLatency == 0) {
          TempReadLatency = TempAccessLatency;
        }
        if (TempWriteLatency == 0) {
          TempWriteLatency = TempAccessLatency;
        }
        CdatReadLatency[CdatLatencyCount] = TempReadLatency;
        CdatWriteLatency[CdatLatencyCount] = TempWriteLatency;
        IDS_HDT_CONSOLE (MAIN_FLOW, "  CdatReadLatency[%u]   = %u\n", CdatLatencyCount, CdatReadLatency[CdatLatencyCount]);
        IDS_HDT_CONSOLE (MAIN_FLOW, "  CdatWriteLatency[%u]  = %u\n", CdatLatencyCount, CdatWriteLatency[CdatLatencyCount]);
        CdatLatencyCount++;

        if (TempAccessBandwidth == 0) {
          TempAccessBandwidth = CXL_DEFAULT_BANDWIDTH;
        }
        // Use the access bandwidth if no read bandwidth or write bandwidth
        // Save the bandwidth for the CXL device
        if (TempReadBandwidth == 0) {
          TempReadBandwidth = TempAccessBandwidth;
        }
        if (TempWriteBandwidth == 0) {
          TempWriteBandwidth = TempAccessBandwidth;
        }
        CdatReadBandwidth[CdatBandwidthCount] = TempReadBandwidth;
        CdatWriteBandwidth[CdatBandwidthCount] = TempWriteBandwidth;
        IDS_HDT_CONSOLE (MAIN_FLOW, "  CdatReadBandwidth[%u]   = %u\n", CdatBandwidthCount, CdatReadBandwidth[CdatBandwidthCount]);
        IDS_HDT_CONSOLE (MAIN_FLOW, "  CdatWriteBandwidth[%u]  = %u\n", CdatBandwidthCount, CdatWriteBandwidth[CdatBandwidthCount]);
        CdatBandwidthCount++;
      } //for (Index = 0; Index < CxlServices->CxlCount; Index++)
    } //for (Socket = 0; Socket < BRH_MAX_SOCKETS; Socket++)
  } //if (CdatReadLatency && CdatWriteLatency && CdatReadBandwidth && CdatWriteBandwidth)

  // Use the max latency
  TempReadLatency = 0;
  TempWriteLatency = 0;
  ASSERT (CdatLatencyCount != 0);
  if (CdatLatencyCount) {
    for (Index = 0; Index < CdatLatencyCount; Index++) {
      if (CdatReadLatency[Index] > TempReadLatency) {
        TempReadLatency = CdatReadLatency[Index];
      }
      if (CdatWriteLatency[Index] > TempWriteLatency) {
        TempWriteLatency = CdatWriteLatency[Index];
      }
    }
    ASSERT (TempReadLatency != 0);
    ASSERT (TempWriteLatency != 0);
    if (ReadLatency) {
      *ReadLatency = TempReadLatency;
    }
    if (WriteLatency) {
      *WriteLatency = TempWriteLatency;
    }
    IDS_HDT_CONSOLE (MAIN_FLOW, "Min Read Latency  = %d\n", TempReadLatency);
    IDS_HDT_CONSOLE (MAIN_FLOW, "Min Write Latency = %d\n", TempWriteLatency);
    IDS_HDT_CONSOLE (MAIN_FLOW, "Latency Count     = %d\n", CdatLatencyCount);
  }

  // min_reported_bw x # of devices.
  TempReadBandwidth = (UINT32) -1;
  TempWriteBandwidth = (UINT32) -1;
  ASSERT (CdatBandwidthCount != 0);
  if (CdatBandwidthCount) {
    for (Index = 0; Index < CdatBandwidthCount; Index++) {
      if (CdatReadBandwidth[Index] < TempReadBandwidth) {
        TempReadBandwidth = CdatReadBandwidth[Index];
      }
      if (CdatWriteBandwidth[Index] < TempWriteBandwidth) {
        TempWriteBandwidth = CdatWriteBandwidth[Index];
      }
    }
    ASSERT (TempReadBandwidth != (UINT32) -1);
    ASSERT (TempWriteBandwidth != (UINT32) -1);
    if (ReadBandwidth) {
      *ReadBandwidth = TempReadBandwidth * CdatBandwidthCount;
    }
    if (WriteBandwidth) {
      *WriteBandwidth = TempWriteBandwidth * CdatBandwidthCount;
    }
    IDS_HDT_CONSOLE (MAIN_FLOW, "Min Read Bandwidth  = %d\n", TempReadBandwidth);
    IDS_HDT_CONSOLE (MAIN_FLOW, "Min Write Bandwidth = %d\n", TempWriteBandwidth);
    IDS_HDT_CONSOLE (MAIN_FLOW, "Bandwidth Count     = %d\n", CdatBandwidthCount);
  }

  if (CdatReadLatency) {
    gBS->FreePool (CdatReadLatency);
  }
  if (CdatWriteLatency) {
    gBS->FreePool (CdatWriteLatency);
  }
  if (CdatReadBandwidth) {
    gBS->FreePool (CdatReadBandwidth);
  }
  if (CdatWriteBandwidth) {
    gBS->FreePool (CdatWriteBandwidth);
  }
}

