/*****************************************************************************
 * Copyright (C) 2008-2024 Advanced Micro Devices, Inc. All rights reserved.
 *
 *****************************************************************************/

/**
 * @file
 *
 * AMD Fabric SMM Driver
 *
 *
 * @xrefitem bom "File Content Label" "Release Content"
 * @e project:      AGESA
 * @e sub-project   AMD Fabric SMM Driver
 *
 */

#include "AGESA.h"
#include "PiSmm.h"
#include <Library/AmdIdsHookLib.h>
#include "Library/IdsLib.h"
#include <Library/SmmFabricTopologyServices2Lib.h>
#include "Filecode.h"

/*----------------------------------------------------------------------------------------
 *                   D E F I N I T I O N S    A N D    M A C R O S
 *----------------------------------------------------------------------------------------
 */

#define FILECODE FABRIC_BRH_FABRICBRHSMM_AMDFABRICBRHSMM_FILECODE

/*----------------------------------------------------------------------------------------
 *           P R O T O T Y P E S     O F     L O C A L     F U N C T I O N S
 *----------------------------------------------------------------------------------------
 */

/*----------------------------------------------------------------------------------------
 *                           G L O B A L   V A R I A B L E S
 *----------------------------------------------------------------------------------------
 */

/*----------------------------------------------------------------------------------------
 *                  T Y P E D E F S     A N D     S T R U C T U R E S
 *----------------------------------------------------------------------------------------
 */

/*----------------------------------------------------------------------------------------
 *                          E X P O R T E D    F U N C T I O N S
 *----------------------------------------------------------------------------------------
 */

/*----------------------------------------------------------------------------------------*/
/**
 * @brief AMD Fabric SMM driver main entry point
 *
 *
 * @param[in] ImageHandle Image Handle
 * @param[in] SystemTable Pointer to system globals
 *
 * @return EFI_STATUS
 */
EFI_STATUS
EFIAPI
AmdFabricBrhSmmDriverEntryPoint (
  IN     EFI_HANDLE        ImageHandle,
  IN     EFI_SYSTEM_TABLE *SystemTable
  )
{
  EFI_STATUS Status;

  AGESA_TESTPOINT (TpFabricSmmEntry, NULL);

  Status = FabricTopologyService2SmmProtocolInstall (ImageHandle, SystemTable);

  AGESA_TESTPOINT (TpFabricSmmExit, NULL);

  return Status;
}

