/*****************************************************************************
 *
 * Copyright (C) 2019-2024 Advanced Micro Devices, Inc. All rights reserved.
 *
 *****************************************************************************
 */

/*----------------------------------------------------------------------------------------
 *                             M O D U L E S    U S E D
 *----------------------------------------------------------------------------------------
 */
#include <PiDxe.h>
#include <Library/UefiLib.h>
#include <Filecode.h>
#include <Library/DxeFabricTopologyServices2Lib.h>
#include <Library/DxeFabricResourceManagerServicesLib.h>
#include <Library/FabricResourceReportToGcdLib.h>
#include <Library/FabricResourceSizeForEachRbLib.h>
#include <Protocol/SocLogicalIdProtocol.h>
#include <Protocol/FabricNumaServicesProtocol.h>
#include <Protocol/AmdNbioSmuServicesProtocol.h>
#include <Protocol/FabricTopologyServices2.h>
#include <Protocol/AmdApcbProtocol.h>
#include "FabricAcpiTable.h"
#include "FabricReadyToBoot.h"
#include <Library/UefiBootServicesTableLib.h>
#include <Library/IdsLib.h>
#include <Library/AmdIdsHookLib.h>
#include <Library/AmdPspBaseLibV2.h>
#include <Library/PcdLib.h>
#include <Library/ApobCommonServiceLib.h>
#include <BRH/ApcbV3TokenUid.h>

/*----------------------------------------------------------------------------------------
 *                   D E F I N I T I O N S    A N D    M A C R O S
 *----------------------------------------------------------------------------------------
 */
#define FILECODE FABRIC_BRH_FABRICBRHDXE_AMDFABRICBRHDXE_FILECODE

/*----------------------------------------------------------------------------------------
 *           P R O T O T Y P E S     O F     L O C A L     F U N C T I O N S
 *----------------------------------------------------------------------------------------
 */
VOID
EFIAPI
FabricNbioSmuServicesCallback (
  IN EFI_EVENT  Event,
  IN VOID       *Context
  );

EFI_STATUS
FabricUpdateNvSocketRbCount (
  VOID
  );

VOID
FabricApcbPcieMmioBaseAddressCheck (VOID);

VOID
EFIAPI
FabricUpdateNvSocketRbCountCallback (
  IN EFI_EVENT  Event,
  IN VOID       *Context
  );

/*----------------------------------------------------------------------------------------
 *                           G L O B A L   V A R I A B L E S
 *----------------------------------------------------------------------------------------
 */
EFI_EVENT  FabricNbioSmuServicesEvent;
VOID       *mRegistrationForFabricNbioSmuServicesEvent;



/*----------------------------------------------------------------------------------------
 *                  T Y P E D E F S     A N D     S T R U C T U R E S
 *----------------------------------------------------------------------------------------
 */

/*----------------------------------------------------------------------------------------
 *                          E X P O R T E D    F U N C T I O N S
 *----------------------------------------------------------------------------------------
 */

/*----------------------------------------------------------------------------------------*/
/**
 * @brief Fabric DXE Breithorn Driver Entry.
 *
 * @param[in] ImageHandle
 * @param[in] SystemTable
 *
 * @return EFI_STATUS
 *
 */
EFI_STATUS
EFIAPI
AmdFabricBrhDxeInit (
  IN EFI_HANDLE        ImageHandle,
  IN EFI_SYSTEM_TABLE  *SystemTable
  )
{
  EFI_STATUS                      Status = EFI_SUCCESS;
  EFI_STATUS                      CalledStatus;
  EFI_EVENT                       ReadyToBootEvent;
  FABRIC_NUMA_SERVICES2_PROTOCOL *FabricNuma;
  UINT32                          NumaDomainCount;
  VOID                            *Protocol;
  EFI_EVENT                       EfiVariableWriteEvent;
  VOID                            *RegistrationForEfiVariableWriteEvent;
  EFI_EVENT                       PciEnumerationCompleteEvent;
  VOID                            *RegistrationForPciEnumerationCompleteEvent;

  AGESA_TESTPOINT (TpDfDxeEntry, NULL);

  IDS_HDT_CONSOLE (CPU_TRACE, "  AmdFabriBrhDxeInit Entry\n");

  // Check the APCB and PCD PCIE MMIO Base Address
  FabricApcbPcieMmioBaseAddressCheck ();

  // Publish DXE topology services
  CalledStatus = FabricTopologyService2ProtocolInstall (ImageHandle, SystemTable);
  Status = (CalledStatus > Status) ? CalledStatus : Status;

  // Publish DF Resource Manager services
  CalledStatus = FabricResourceManagerServiceProtocolInstall (ImageHandle, SystemTable);
  Status = (CalledStatus > Status) ? CalledStatus : Status;

  // Report MMIO Region to GCD
  // NOTE: this must be after FabricResourceManagerServiceProtocolInstall
  FabricResourceReportToGcd ();

  // Publish NUMA services 2 protocol
  CalledStatus = FabricBrhNumaServices2ProtocolInstall (ImageHandle, SystemTable);
  Status = (CalledStatus > Status) ? CalledStatus : Status;

  if (gBS->LocateProtocol (&gAmdFabricNumaServices2ProtocolGuid, NULL, (VOID **) &FabricNuma) == EFI_SUCCESS) {
    if (FabricNuma->GetDomainInfo (FabricNuma, &NumaDomainCount, NULL, NULL) == EFI_SUCCESS) {
      if ((NumaDomainCount > 1) || (PcdGetBool (PcdAmdFabricSratSlitInstallOverride))) {
        // Publish ACPI Fabric SRAT services protocol
        CalledStatus = FabricBrhAcpiSratProtocolInstall (ImageHandle, SystemTable);
        Status = (CalledStatus > Status) ? CalledStatus : Status;

        // Publish ACPI Fabric MSCT services protocol
        CalledStatus = FabricBrhAcpiMsctProtocolInstall (ImageHandle, SystemTable);
        Status = (CalledStatus > Status) ? CalledStatus : Status;

        // Publish ACPI SLIT services protocol
        CalledStatus = FabricBrhAcpiSlitProtocolInstall (ImageHandle, SystemTable);
        Status = (CalledStatus > Status) ? CalledStatus : Status;
      }
    }

    // Publish ACPI Fabric CRAT services protocol
    CalledStatus = FabricBrhAcpiCratProtocolInstall (ImageHandle, SystemTable);
    Status = (CalledStatus > Status) ? CalledStatus : Status;

    // Publish ACPI CDIT services
    CalledStatus = FabricBrhAcpiCditProtocolInstall (ImageHandle, SystemTable);
    Status = (CalledStatus > Status) ? CalledStatus : Status;
  }

  //
  // Set up call back after NBIO SMU services are available.
  //
  CalledStatus = gBS->CreateEventEx (
                           EVT_NOTIFY_SIGNAL,
                           TPL_NOTIFY,
                           FabricNbioSmuServicesCallback,
                           NULL,
                           NULL,
                           &FabricNbioSmuServicesEvent
                           );
  ASSERT (CalledStatus == EFI_SUCCESS);
  Status = (CalledStatus > Status) ? CalledStatus : Status;

  CalledStatus = gBS->RegisterProtocolNotify (
                          &gAmdNbioSmuServicesProtocolGuid,
                          FabricNbioSmuServicesEvent,
                          &(mRegistrationForFabricNbioSmuServicesEvent)
                          );
  ASSERT (CalledStatus == EFI_SUCCESS);
  Status = (CalledStatus > Status) ? CalledStatus : Status;

  CalledStatus = gBS->CreateEventEx (
                      EVT_NOTIFY_SIGNAL,
                      TPL_NOTIFY,
                      FabricReadyToBoot,
                      NULL,
                      &gEfiEventReadyToBootGuid,
                      &ReadyToBootEvent
                      );
  Status = (CalledStatus > Status) ? CalledStatus : Status;

  CalledStatus = gBS->LocateProtocol (&gEfiVariableWriteArchProtocolGuid, NULL, (VOID **)&Protocol);
  if (!EFI_ERROR (CalledStatus)) {
    FabricUpdateNvSocketRbCount();
  } else {
    CalledStatus = gBS->CreateEventEx (
                           EVT_NOTIFY_SIGNAL,
                           TPL_NOTIFY,
                           FabricUpdateNvSocketRbCountCallback,
                           NULL,
                           NULL,
                           &EfiVariableWriteEvent
                           );
    ASSERT (CalledStatus == EFI_SUCCESS);
    Status = (CalledStatus > Status) ? CalledStatus : Status;

    CalledStatus = gBS->RegisterProtocolNotify (
                          &gEfiVariableWriteArchProtocolGuid,
                          EfiVariableWriteEvent,
                          &(RegistrationForEfiVariableWriteEvent)
                          );
    ASSERT (CalledStatus == EFI_SUCCESS);
    Status = (CalledStatus > Status) ? CalledStatus : Status;
  }

  if (PcdGetBool (PcdAmdAcpiHmat)) {
    CalledStatus = gBS->CreateEventEx (
                          EVT_NOTIFY_SIGNAL,
                          TPL_NOTIFY,
                          FabricBrhAcpiHmatInstall,
                          NULL,
                          NULL,
                          &PciEnumerationCompleteEvent
                          );
    ASSERT (CalledStatus == EFI_SUCCESS);
    Status = (CalledStatus > Status) ? CalledStatus : Status;
    if (CalledStatus == EFI_SUCCESS) {
      CalledStatus = gBS->RegisterProtocolNotify (
                            &gEfiPciEnumerationCompleteProtocolGuid,
                            PciEnumerationCompleteEvent,
                            &RegistrationForPciEnumerationCompleteEvent
                            );
      ASSERT (CalledStatus == EFI_SUCCESS);
      Status = (CalledStatus > Status) ? CalledStatus : Status;
    }
  }

  IDS_HOOK (IDS_HOOK_DF_DXE_INIT, NULL, NULL);

  IDS_HDT_CONSOLE (CPU_TRACE, "  AmdFabricBrhDxeInit End\n");

  AGESA_TESTPOINT (TpDfDxeExit, NULL);

  return Status;
}

VOID
EFIAPI
FabricNbioSmuServicesCallback (
  IN EFI_EVENT  Event,
  IN VOID       *Context
  )
{
  EFI_STATUS                   CalledStatus;
  AMD_SOC_LOGICAL_ID_PROTOCOL *SocLogicalIdProtocol;

  AGESA_TESTPOINT (TpDfNbioSmuServicesProtocolCallbackEntry, NULL);

  IDS_HDT_CONSOLE (CPU_TRACE, "  FabricNbioSmuServicesCallback Entry\n");

  CalledStatus = gBS->LocateProtocol (&gAmdSocLogicalIdProtocolGuid,
                                      NULL,
                                      (VOID **)&SocLogicalIdProtocol);

  ASSERT (CalledStatus == EFI_SUCCESS);

  IDS_HDT_CONSOLE (CPU_TRACE, "  FabricNbioSmuServicesCallback Exit\n");

  AGESA_TESTPOINT (TpDfNbioSmuServicesProtocolCallbackExit, NULL);
}

VOID
EFIAPI
FabricUpdateNvSocketRbCountCallback (
  IN EFI_EVENT  Event,
  IN VOID       *Context
  )
{
  IDS_HDT_CONSOLE (CPU_TRACE, "  FabricUpdateNvSocketRbCountCallback Entry\n");

  FabricUpdateNvSocketRbCount();

  gBS->CloseEvent (Event);

  IDS_HDT_CONSOLE (CPU_TRACE, "  FabricUpdateNvSocketRbCountCallback Exit\n");
}

EFI_STATUS
FabricUpdateNvSocketRbCount (
  VOID
  )
{
  EFI_STATUS                              Status;
  AMD_FABRIC_TOPOLOGY_SERVICES2_PROTOCOL *FabricTopology;
  UINT8                                   NvTotalNumberOfSockets;
  UINT8                                   NvTotalNumberOfRootBridges;
  UINT8                                   NvTotalNumberOfPciSegments;
  UINT8                                   CurrentNumberOfInstalledProcessors;
  UINT8                                   CurrentTotalNumberOfRootBridges;
  UINT8                                   CurrentTotalNumberOfPciSegments;
  UINTN                                   NumberOfInstalledProcessors;
  UINTN                                   TotalNumberOfRootBridges;
  BOOLEAN                                 ClearNvResources;

  // locate FabricTopologyGetSystemInfo2
  Status = gBS->LocateProtocol (&gAmdFabricTopologyServices2ProtocolGuid, NULL, (VOID **)&FabricTopology);
  ASSERT (!EFI_ERROR (Status));
  if (EFI_ERROR (Status)) {
    return Status;
  }

  // get current total number of rootbridges
  Status = FabricTopology->GetSystemInfo (FabricTopology, &NumberOfInstalledProcessors, NULL, &TotalNumberOfRootBridges, NULL, NULL);
  ASSERT(!EFI_ERROR(Status));
  if (EFI_ERROR (Status)) {
    return Status;
  }
  CurrentNumberOfInstalledProcessors = (UINT8)NumberOfInstalledProcessors;
  CurrentTotalNumberOfRootBridges = (UINT8)TotalNumberOfRootBridges;
  CurrentTotalNumberOfPciSegments = (UINT8)FabricTopologyGetNumberOfPciSegments ();

  ClearNvResources = FALSE;
  // read current saved Num of sockets
  Status = FabricGetNvTotalNumberOfSockets (&NvTotalNumberOfSockets);
  if (!EFI_ERROR(Status)) {
    // compare to saved number
    if (CurrentNumberOfInstalledProcessors != NvTotalNumberOfSockets) {
      FabricSetNvTotalNumberOfSockets (&CurrentNumberOfInstalledProcessors);
      ClearNvResources = TRUE;
    }
  } else if (Status == EFI_NOT_FOUND) {
    // Cannot get NvVariable, so set
    FabricSetNvTotalNumberOfSockets (&CurrentNumberOfInstalledProcessors);
  } else {
    ASSERT(!EFI_ERROR(Status));
  }

  // read current saved Num of Rbs
  Status = FabricGetNvTotalNumberOfRootBridges (&NvTotalNumberOfRootBridges);
  if (!EFI_ERROR(Status)) {
    // compare to saved number
    if (CurrentTotalNumberOfRootBridges != NvTotalNumberOfRootBridges) {
      FabricSetNvTotalNumberOfRootBridges (&CurrentTotalNumberOfRootBridges);
      ClearNvResources = TRUE;
    }
  } else if (Status == EFI_NOT_FOUND) {
    // Cannot get NvVariable, so set
    FabricSetNvTotalNumberOfRootBridges (&CurrentTotalNumberOfRootBridges);
  } else {
    ASSERT(!EFI_ERROR(Status));
  }

  // read current saved Num of PCI segments
  Status = FabricGetNvTotalNumberOfPciSegments (&NvTotalNumberOfPciSegments);
  if (!EFI_ERROR(Status)) {
    // compare to saved number
    if (CurrentTotalNumberOfPciSegments != NvTotalNumberOfPciSegments) {
      FabricSetNvTotalNumberOfPciSegments (&CurrentTotalNumberOfPciSegments);
      ClearNvResources = TRUE;
    }
  } else if (Status == EFI_NOT_FOUND) {
    // Cannot get NvVariable, so set
    FabricSetNvTotalNumberOfPciSegments (&CurrentTotalNumberOfPciSegments);
  } else {
    ASSERT(!EFI_ERROR(Status));
  }

  if (ClearNvResources) {
    FabricClearResourceSizeForEachRb ();
  }

  return Status;
}

VOID
FabricApcbPcieMmioBaseAddressCheck (
  VOID
  )
{
  EFI_STATUS                   Status;
  AMD_APCB_SERVICE_PROTOCOL    *ApcbDxeServiceProtocol;
  UINT8                        ApcbPurpose;
  UINT32                       Apcb32;
  UINT64                       ApcbPcieMmioBase;
  BOOLEAN                      ApcbRecoveryFlag;

  IDS_HDT_CONSOLE (MAIN_FLOW, "FabricApcbPcieMmioBaseAddressCheck Entry\n");

  ApobGetApcbRecoveryFlag (&ApcbRecoveryFlag);
  if (ApcbRecoveryFlag) {
    IDS_HDT_CONSOLE (MAIN_FLOW, "Bypasss PCIE MMIO base check in APCB Recovery Mode\n");
    return;
  }

  if (CheckPspRecoveryFlagV2 () == TRUE) {
    IDS_HDT_CONSOLE (MAIN_FLOW, "Bypass PCIE MMIO base check in PSP recovery mode\n");
    return;
  }

  Status = gBS->LocateProtocol (&gAmdApcbDxeServiceProtocolGuid, NULL, (VOID **) &ApcbDxeServiceProtocol);
  ASSERT(!EFI_ERROR(Status));
  if (EFI_ERROR (Status)) {
    return;
  }

  Status = ApcbDxeServiceProtocol->ApcbGetToken32 (ApcbDxeServiceProtocol, &ApcbPurpose, APCB_TOKEN_UID_DF_PCI_MMIO_BASE, &Apcb32);
  if (EFI_ERROR (Status)) {
    Apcb32 = 0;
  }
  ApcbPcieMmioBase = Apcb32;

  Status = ApcbDxeServiceProtocol->ApcbGetToken32 (ApcbDxeServiceProtocol, &ApcbPurpose, APCB_TOKEN_UID_DF_PCI_MMIO_HI_BASE, &Apcb32);
  if (EFI_ERROR (Status)) {
    Apcb32 = 0;
  }
  ApcbPcieMmioBase += LShiftU64 (Apcb32, 32);

  IDS_HDT_CONSOLE (MAIN_FLOW, "ApcbPcieMmioBase = %lx\n", ApcbPcieMmioBase);
  IDS_HDT_CONSOLE (MAIN_FLOW, "PcdGet64 (PcdPciExpressBaseAddress) = %lx\n", PcdGet64 (PcdPciExpressBaseAddress));
  if (PcdGet64 (PcdPciExpressBaseAddress) != ApcbPcieMmioBase) {
    IDS_HDT_CONSOLE (MAIN_FLOW, "APCB_TOKEN_UID_DF_PCI_MMIO_BASE/APCB_TOKEN_UID_DF_PCI_MMIO_HI_BASE and PcdPciExpressBaseAddress are different!!!\n");
    ASSERT (FALSE);
  }
}

