## @file
#
# Cxl Error Log DXE Driver.
#
##
#;*****************************************************************************
#; Copyright (C) 2024 Advanced Micro Devices, Inc. All rights reserved.
#;
#;*****************************************************************************

[Defines]
  INF_VERSION                    = 0x00010005
  BASE_NAME                      = CxlErrorLogDxe
  FILE_GUID                      = 4BCB4D93-46D2-94EF-BB82-5C8671B24D26
  MODULE_TYPE                    = DXE_DRIVER
  VERSION_STRING                 = 1.0
  ENTRY_POINT                    = CxlErrorLogDxeInit

[Sources]
  AmdCxlErrorLogDxe.h
  AmdCxlErrorLogDxe.c

[Packages]
  MdePkg/MdePkg.dec
  MdeModulePkg/MdeModulePkg.dec
  AgesaModulePkg/AgesaCommonModulePkg.dec
  AgesaPkg/AgesaPkg.dec
  AgesaModulePkg/AgesaFamily1AModulePkg.dec

[LibraryClasses]
  BaseLib
  BaseMemoryLib
  MemoryAllocationLib
  DebugLib
  DxeServicesTableLib
  HobLib
  PcdLib
  UefiLib
  UefiBootServicesTableLib
  UefiRuntimeServicesTableLib
  UefiDriverEntryPoint
  AmdErrorLogLib

[FixedPcd]

[Pcd]

[Guids]

[Protocols]
  gAmdErrorLogProtocolGuid               #CONSUME
  gAmdErrorLogServiceProtocolGuid        #CONSUME
  gAmdCxlErrorLogProtocolGuid            ## PRODUCE

[Depex]
  gAmdErrorLogProtocolGuid AND
  gAmdErrorLogServiceProtocolGuid

