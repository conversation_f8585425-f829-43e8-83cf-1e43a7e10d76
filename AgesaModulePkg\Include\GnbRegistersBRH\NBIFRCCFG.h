/*****************************************************************************
 *
 * Copyright (C) 2008-2024 Advanced Micro Devices, Inc. All rights reserved.
 *
 *******************************************************************************
 **/

#ifndef _NBIFRCCFG_H_
#define _NBIFRCCFG_H_


/***********************************************************
* Register Name : AP_CAP
************************************************************/

#define AP_CAP_AP_COUNT_OFFSET                                 0
#define AP_CAP_AP_COUNT_MASK                                   0xff

#define AP_CAP_AP_SELECTIVE_ENABLE_SUPPORTED_OFFSET            8
#define AP_CAP_AP_SELECTIVE_ENABLE_SUPPORTED_MASK              0x100

#define AP_CAP_Reserved_31_9_OFFSET                            9
#define AP_CAP_Reserved_31_9_MASK                              0xfffffe00

typedef union {
  struct {
    UINT32                                            AP_COUNT:8;
    UINT32                       AP_SELECTIVE_ENABLE_SUPPORTED:1;
    UINT32                                       Reserved_31_9:23;
  } Field;
  UINT32 Value;
} AP_CAP_NBIFRCCFG_STRUCT;

#define PCICFG_NBIFRCCFG_AP_CAP_OFFSET                              0x534
#define SMN_DEV0_NBIF0RCNBIO0_AP_CAP_ADDRESS                          0x10100534UL
#define SMN_DEV0_NBIF0RCNBIO1_AP_CAP_ADDRESS                          0x10300534UL
#define SMN_DEV0_NBIF1RCNBIO0_AP_CAP_ADDRESS                          0x10200534UL
#define SMN_DEV0_NBIF1RCNBIO1_AP_CAP_ADDRESS                          0x10400534UL
#define SMN_DEV1_NBIF0RCNBIO0_AP_CAP_ADDRESS                          0x10101534UL
#define SMN_DEV1_NBIF0RCNBIO1_AP_CAP_ADDRESS                          0x10301534UL
#define SMN_DEV1_NBIF1RCNBIO0_AP_CAP_ADDRESS                          0x10201534UL
#define SMN_DEV1_NBIF1RCNBIO1_AP_CAP_ADDRESS                          0x10401534UL


/***********************************************************
* Register Name : AP_CNTL
************************************************************/

#define AP_CNTL_AP_INDEX_SELECT_OFFSET                         0
#define AP_CNTL_AP_INDEX_SELECT_MASK                           0xff

#define AP_CNTL_AP_NEGOTIATION_GLOBAL_EN_OFFSET                8
#define AP_CNTL_AP_NEGOTIATION_GLOBAL_EN_MASK                  0x100

#define AP_CNTL_Reserved_31_9_OFFSET                           9
#define AP_CNTL_Reserved_31_9_MASK                             0xfffffe00

typedef union {
  struct {
    UINT32                                     AP_INDEX_SELECT:8;
    UINT32                            AP_NEGOTIATION_GLOBAL_EN:1;
    UINT32                                       Reserved_31_9:23;
  } Field;
  UINT32 Value;
} AP_CNTL_NBIFRCCFG_STRUCT;

#define PCICFG_NBIFRCCFG_AP_CNTL_OFFSET                             0x538
#define SMN_DEV0_NBIF0RCNBIO0_AP_CNTL_ADDRESS                         0x10100538UL
#define SMN_DEV0_NBIF0RCNBIO1_AP_CNTL_ADDRESS                         0x10300538UL
#define SMN_DEV0_NBIF1RCNBIO0_AP_CNTL_ADDRESS                         0x10200538UL
#define SMN_DEV0_NBIF1RCNBIO1_AP_CNTL_ADDRESS                         0x10400538UL
#define SMN_DEV1_NBIF0RCNBIO0_AP_CNTL_ADDRESS                         0x10101538UL
#define SMN_DEV1_NBIF0RCNBIO1_AP_CNTL_ADDRESS                         0x10301538UL
#define SMN_DEV1_NBIF1RCNBIO0_AP_CNTL_ADDRESS                         0x10201538UL
#define SMN_DEV1_NBIF1RCNBIO1_AP_CNTL_ADDRESS                         0x10401538UL


/***********************************************************
* Register Name : AP_DATA1
************************************************************/

#define AP_DATA1_AP_USAGE_INFORMATION_OFFSET                   0
#define AP_DATA1_AP_USAGE_INFORMATION_MASK                     0x7

#define AP_DATA1_Reserved_4_3_OFFSET                           3
#define AP_DATA1_Reserved_4_3_MASK                             0x18

#define AP_DATA1_AP_DETAILS_OFFSET                             5
#define AP_DATA1_AP_DETAILS_MASK                               0xffe0

#define AP_DATA1_AP_VENDOR_ID_OFFSET                           16
#define AP_DATA1_AP_VENDOR_ID_MASK                             0xffff0000

typedef union {
  struct {
    UINT32                                AP_USAGE_INFORMATION:3;
    UINT32                                        Reserved_4_3:2;
    UINT32                                          AP_DETAILS:11;
    UINT32                                        AP_VENDOR_ID:16;
  } Field;
  UINT32 Value;
} AP_DATA1_NBIFRCCFG_STRUCT;

#define PCICFG_NBIFRCCFG_AP_DATA1_OFFSET                            0x53c
#define SMN_DEV0_NBIF0RCNBIO0_AP_DATA1_ADDRESS                        0x1010053cUL
#define SMN_DEV0_NBIF0RCNBIO1_AP_DATA1_ADDRESS                        0x1030053cUL
#define SMN_DEV0_NBIF1RCNBIO0_AP_DATA1_ADDRESS                        0x1020053cUL
#define SMN_DEV0_NBIF1RCNBIO1_AP_DATA1_ADDRESS                        0x1040053cUL
#define SMN_DEV1_NBIF0RCNBIO0_AP_DATA1_ADDRESS                        0x1010153cUL
#define SMN_DEV1_NBIF0RCNBIO1_AP_DATA1_ADDRESS                        0x1030153cUL
#define SMN_DEV1_NBIF1RCNBIO0_AP_DATA1_ADDRESS                        0x1020153cUL
#define SMN_DEV1_NBIF1RCNBIO1_AP_DATA1_ADDRESS                        0x1040153cUL


/***********************************************************
* Register Name : AP_DATA2
************************************************************/

#define AP_DATA2_MODIFIED_TS_INFORMATION_2_OFFSET              0
#define AP_DATA2_MODIFIED_TS_INFORMATION_2_MASK                0xffffff

#define AP_DATA2_Reserved_31_24_OFFSET                         24
#define AP_DATA2_Reserved_31_24_MASK                           0xff000000

typedef union {
  struct {
    UINT32                           MODIFIED_TS_INFORMATION_2:24;
    UINT32                                      Reserved_31_24:8;
  } Field;
  UINT32 Value;
} AP_DATA2_NBIFRCCFG_STRUCT;

#define PCICFG_NBIFRCCFG_AP_DATA2_OFFSET                            0x540
#define SMN_DEV0_NBIF0RCNBIO0_AP_DATA2_ADDRESS                        0x10100540UL
#define SMN_DEV0_NBIF0RCNBIO1_AP_DATA2_ADDRESS                        0x10300540UL
#define SMN_DEV0_NBIF1RCNBIO0_AP_DATA2_ADDRESS                        0x10200540UL
#define SMN_DEV0_NBIF1RCNBIO1_AP_DATA2_ADDRESS                        0x10400540UL
#define SMN_DEV1_NBIF0RCNBIO0_AP_DATA2_ADDRESS                        0x10101540UL
#define SMN_DEV1_NBIF0RCNBIO1_AP_DATA2_ADDRESS                        0x10301540UL
#define SMN_DEV1_NBIF1RCNBIO0_AP_DATA2_ADDRESS                        0x10201540UL
#define SMN_DEV1_NBIF1RCNBIO1_AP_DATA2_ADDRESS                        0x10401540UL


/***********************************************************
* Register Name : AP_SELECTIVE_ENABLE_MASK
************************************************************/

#define AP_SELECTIVE_ENABLE_MASK_AP_SELECTIVE_ENABLE_MASK_0_OFFSET 0
#define AP_SELECTIVE_ENABLE_MASK_AP_SELECTIVE_ENABLE_MASK_0_MASK 0x1

#define AP_SELECTIVE_ENABLE_MASK_AP_SELECTIVE_ENABLE_MASK_31_1_OFFSET 1
#define AP_SELECTIVE_ENABLE_MASK_AP_SELECTIVE_ENABLE_MASK_31_1_MASK 0xfffffffe

typedef union {
  struct {
    UINT32                          AP_SELECTIVE_ENABLE_MASK_0:1;
    UINT32                       AP_SELECTIVE_ENABLE_MASK_31_1:31;
  } Field;
  UINT32 Value;
} AP_SELECTIVE_ENABLE_MASK_NBIFRCCFG_STRUCT;

#define PCICFG_NBIFRCCFG_AP_SELECTIVE_ENABLE_MASK_OFFSET            0x544
#define SMN_DEV0_NBIF0RCNBIO0_AP_SELECTIVE_ENABLE_MASK_ADDRESS        0x10100544UL
#define SMN_DEV0_NBIF0RCNBIO1_AP_SELECTIVE_ENABLE_MASK_ADDRESS        0x10300544UL
#define SMN_DEV0_NBIF1RCNBIO0_AP_SELECTIVE_ENABLE_MASK_ADDRESS        0x10200544UL
#define SMN_DEV0_NBIF1RCNBIO1_AP_SELECTIVE_ENABLE_MASK_ADDRESS        0x10400544UL
#define SMN_DEV1_NBIF0RCNBIO0_AP_SELECTIVE_ENABLE_MASK_ADDRESS        0x10101544UL
#define SMN_DEV1_NBIF0RCNBIO1_AP_SELECTIVE_ENABLE_MASK_ADDRESS        0x10301544UL
#define SMN_DEV1_NBIF1RCNBIO0_AP_SELECTIVE_ENABLE_MASK_ADDRESS        0x10201544UL
#define SMN_DEV1_NBIF1RCNBIO1_AP_SELECTIVE_ENABLE_MASK_ADDRESS        0x10401544UL


/***********************************************************
* Register Name : BASE_ADDR_1
************************************************************/

#define BASE_ADDR_1_BASE_ADDR_OFFSET                           0
#define BASE_ADDR_1_BASE_ADDR_MASK                             0xffffffff

typedef union {
  struct {
    UINT32                                           BASE_ADDR:32;
  } Field;
  UINT32 Value;
} BASE_ADDR_1_NBIFRCCFG_STRUCT;

#define PCICFG_NBIFRCCFG_BASE_ADDR_1_OFFSET                         0x10
#define SMN_DEV0_NBIF0RCNBIO0_BASE_ADDR_1_ADDRESS                     0x10100010UL
#define SMN_DEV0_NBIF0RCNBIO1_BASE_ADDR_1_ADDRESS                     0x10300010UL
#define SMN_DEV0_NBIF1RCNBIO0_BASE_ADDR_1_ADDRESS                     0x10200010UL
#define SMN_DEV0_NBIF1RCNBIO1_BASE_ADDR_1_ADDRESS                     0x10400010UL
#define SMN_DEV1_NBIF0RCNBIO0_BASE_ADDR_1_ADDRESS                     0x10101010UL
#define SMN_DEV1_NBIF0RCNBIO1_BASE_ADDR_1_ADDRESS                     0x10301010UL
#define SMN_DEV1_NBIF1RCNBIO0_BASE_ADDR_1_ADDRESS                     0x10201010UL
#define SMN_DEV1_NBIF1RCNBIO1_BASE_ADDR_1_ADDRESS                     0x10401010UL


/***********************************************************
* Register Name : BASE_ADDR_2
************************************************************/

#define BASE_ADDR_2_BASE_ADDR_OFFSET                           0
#define BASE_ADDR_2_BASE_ADDR_MASK                             0xffffffff

typedef union {
  struct {
    UINT32                                           BASE_ADDR:32;
  } Field;
  UINT32 Value;
} BASE_ADDR_2_NBIFRCCFG_STRUCT;

#define PCICFG_NBIFRCCFG_BASE_ADDR_2_OFFSET                         0x14
#define SMN_DEV0_NBIF0RCNBIO0_BASE_ADDR_2_ADDRESS                     0x10100014UL
#define SMN_DEV0_NBIF0RCNBIO1_BASE_ADDR_2_ADDRESS                     0x10300014UL
#define SMN_DEV0_NBIF1RCNBIO0_BASE_ADDR_2_ADDRESS                     0x10200014UL
#define SMN_DEV0_NBIF1RCNBIO1_BASE_ADDR_2_ADDRESS                     0x10400014UL
#define SMN_DEV1_NBIF0RCNBIO0_BASE_ADDR_2_ADDRESS                     0x10101014UL
#define SMN_DEV1_NBIF0RCNBIO1_BASE_ADDR_2_ADDRESS                     0x10301014UL
#define SMN_DEV1_NBIF1RCNBIO0_BASE_ADDR_2_ADDRESS                     0x10201014UL
#define SMN_DEV1_NBIF1RCNBIO1_BASE_ADDR_2_ADDRESS                     0x10401014UL


/***********************************************************
* Register Name : BASE_CLASS
************************************************************/

#define BASE_CLASS_BASE_CLASS_OFFSET                           0
#define BASE_CLASS_BASE_CLASS_MASK                             0xff

typedef union {
  struct {
    UINT8                                          BASE_CLASS:8;
  } Field;
  UINT8 Value;
} BASE_CLASS_NBIFRCCFG_STRUCT;

#define PCICFG_NBIFRCCFG_BASE_CLASS_OFFSET                          0xb
#define SMN_DEV0_NBIF0RCNBIO0_BASE_CLASS_ADDRESS                      0x1010000bUL
#define SMN_DEV0_NBIF0RCNBIO1_BASE_CLASS_ADDRESS                      0x1030000bUL
#define SMN_DEV0_NBIF1RCNBIO0_BASE_CLASS_ADDRESS                      0x1020000bUL
#define SMN_DEV0_NBIF1RCNBIO1_BASE_CLASS_ADDRESS                      0x1040000bUL
#define SMN_DEV1_NBIF0RCNBIO0_BASE_CLASS_ADDRESS                      0x1010100bUL
#define SMN_DEV1_NBIF0RCNBIO1_BASE_CLASS_ADDRESS                      0x1030100bUL
#define SMN_DEV1_NBIF1RCNBIO0_BASE_CLASS_ADDRESS                      0x1020100bUL
#define SMN_DEV1_NBIF1RCNBIO1_BASE_CLASS_ADDRESS                      0x1040100bUL


/***********************************************************
* Register Name : BIST
************************************************************/

#define BIST_BIST_COMP_OFFSET                                  0
#define BIST_BIST_COMP_MASK                                    0xf

#define BIST_Reserved_5_4_OFFSET                               4
#define BIST_Reserved_5_4_MASK                                 0x30

#define BIST_BIST_STRT_OFFSET                                  6
#define BIST_BIST_STRT_MASK                                    0x40

#define BIST_BIST_CAP_OFFSET                                   7
#define BIST_BIST_CAP_MASK                                     0x80

typedef union {
  struct {
    UINT8                                           BIST_COMP:4;
    UINT8                                        Reserved_5_4:2;
    UINT8                                           BIST_STRT:1;
    UINT8                                            BIST_CAP:1;
  } Field;
  UINT8 Value;
} BIST_NBIFRCCFG_STRUCT;

#define PCICFG_NBIFRCCFG_BIST_OFFSET                                0xf
#define SMN_DEV0_NBIF0RCNBIO0_BIST_ADDRESS                            0x1010000fUL
#define SMN_DEV0_NBIF0RCNBIO1_BIST_ADDRESS                            0x1030000fUL
#define SMN_DEV0_NBIF1RCNBIO0_BIST_ADDRESS                            0x1020000fUL
#define SMN_DEV0_NBIF1RCNBIO1_BIST_ADDRESS                            0x1040000fUL
#define SMN_DEV1_NBIF0RCNBIO0_BIST_ADDRESS                            0x1010100fUL
#define SMN_DEV1_NBIF0RCNBIO1_BIST_ADDRESS                            0x1030100fUL
#define SMN_DEV1_NBIF1RCNBIO0_BIST_ADDRESS                            0x1020100fUL
#define SMN_DEV1_NBIF1RCNBIO1_BIST_ADDRESS                            0x1040100fUL


/***********************************************************
* Register Name : BRIDGE_CNTL
************************************************************/

#define BRIDGE_CNTL_PARITY_RESPONSE_EN_OFFSET                  0
#define BRIDGE_CNTL_PARITY_RESPONSE_EN_MASK                    0x1

#define BRIDGE_CNTL_SERR_EN_OFFSET                             1
#define BRIDGE_CNTL_SERR_EN_MASK                               0x2

#define BRIDGE_CNTL_ISA_EN_OFFSET                              2
#define BRIDGE_CNTL_ISA_EN_MASK                                0x4

#define BRIDGE_CNTL_VGA_EN_OFFSET                              3
#define BRIDGE_CNTL_VGA_EN_MASK                                0x8

#define BRIDGE_CNTL_VGA_DEC_OFFSET                             4
#define BRIDGE_CNTL_VGA_DEC_MASK                               0x10

#define BRIDGE_CNTL_MASTER_ABORT_MODE_OFFSET                   5
#define BRIDGE_CNTL_MASTER_ABORT_MODE_MASK                     0x20

#define BRIDGE_CNTL_SECONDARY_BUS_RESET_OFFSET                 6
#define BRIDGE_CNTL_SECONDARY_BUS_RESET_MASK                   0x40

#define BRIDGE_CNTL_FAST_B2B_EN_OFFSET                         7
#define BRIDGE_CNTL_FAST_B2B_EN_MASK                           0x80

#define BRIDGE_CNTL_PRIMARY_DISCARD_TIMER_OFFSET               8
#define BRIDGE_CNTL_PRIMARY_DISCARD_TIMER_MASK                 0x100

#define BRIDGE_CNTL_SECONDARY_DISCARD_TIMER_OFFSET             9
#define BRIDGE_CNTL_SECONDARY_DISCARD_TIMER_MASK               0x200

#define BRIDGE_CNTL_DISCARD_TIMER_STATUS_OFFSET                10
#define BRIDGE_CNTL_DISCARD_TIMER_STATUS_MASK                  0x400

#define BRIDGE_CNTL_DISCARD_TIMER_SERR_ENABLE_OFFSET           11
#define BRIDGE_CNTL_DISCARD_TIMER_SERR_ENABLE_MASK             0x800

#define BRIDGE_CNTL_Reserved_15_12_OFFSET                      12
#define BRIDGE_CNTL_Reserved_15_12_MASK                        0xf000

typedef union {
  struct {
    UINT16                                  PARITY_RESPONSE_EN:1;
    UINT16                                             SERR_EN:1;
    UINT16                                              ISA_EN:1;
    UINT16                                              VGA_EN:1;
    UINT16                                             VGA_DEC:1;
    UINT16                                   MASTER_ABORT_MODE:1;
    UINT16                                 SECONDARY_BUS_RESET:1;
    UINT16                                         FAST_B2B_EN:1;
    UINT16                               PRIMARY_DISCARD_TIMER:1;
    UINT16                             SECONDARY_DISCARD_TIMER:1;
    UINT16                                DISCARD_TIMER_STATUS:1;
    UINT16                           DISCARD_TIMER_SERR_ENABLE:1;
    UINT16                                      Reserved_15_12:4;
  } Field;
  UINT16 Value;
} BRIDGE_CNTL_STRUCT;

#define PCICFG_NBIFRCCFG_BRIDGE_CNTL_OFFSET                         0x3e
#define SMN_DEV0_NBIF0RCNBIO0_BRIDGE_CNTL_ADDRESS                     0x1010003eUL
#define SMN_DEV0_NBIF0RCNBIO1_BRIDGE_CNTL_ADDRESS                     0x1030003eUL
#define SMN_DEV0_NBIF1RCNBIO0_BRIDGE_CNTL_ADDRESS                     0x1020003eUL
#define SMN_DEV0_NBIF1RCNBIO1_BRIDGE_CNTL_ADDRESS                     0x1040003eUL
#define SMN_DEV1_NBIF0RCNBIO0_BRIDGE_CNTL_ADDRESS                     0x1010103eUL
#define SMN_DEV1_NBIF0RCNBIO1_BRIDGE_CNTL_ADDRESS                     0x1030103eUL
#define SMN_DEV1_NBIF1RCNBIO0_BRIDGE_CNTL_ADDRESS                     0x1020103eUL
#define SMN_DEV1_NBIF1RCNBIO1_BRIDGE_CNTL_ADDRESS                     0x1040103eUL


/***********************************************************
* Register Name : CACHE_LINE
************************************************************/

#define CACHE_LINE_CACHE_LINE_SIZE_OFFSET                      0
#define CACHE_LINE_CACHE_LINE_SIZE_MASK                        0xff

typedef union {
  struct {
    UINT8                                     CACHE_LINE_SIZE:8;
  } Field;
  UINT8 Value;
} CACHE_LINE_NBIFRCCFG_STRUCT;

#define PCICFG_NBIFRCCFG_CACHE_LINE_OFFSET                          0xc
#define SMN_DEV0_NBIF0RCNBIO0_CACHE_LINE_ADDRESS                      0x1010000cUL
#define SMN_DEV0_NBIF0RCNBIO1_CACHE_LINE_ADDRESS                      0x1030000cUL
#define SMN_DEV0_NBIF1RCNBIO0_CACHE_LINE_ADDRESS                      0x1020000cUL
#define SMN_DEV0_NBIF1RCNBIO1_CACHE_LINE_ADDRESS                      0x1040000cUL
#define SMN_DEV1_NBIF0RCNBIO0_CACHE_LINE_ADDRESS                      0x1010100cUL
#define SMN_DEV1_NBIF0RCNBIO1_CACHE_LINE_ADDRESS                      0x1030100cUL
#define SMN_DEV1_NBIF1RCNBIO0_CACHE_LINE_ADDRESS                      0x1020100cUL
#define SMN_DEV1_NBIF1RCNBIO1_CACHE_LINE_ADDRESS                      0x1040100cUL


/***********************************************************
* Register Name : CAP_PTR
************************************************************/

#define CAP_PTR_CAP_PTR_OFFSET                                 0
#define CAP_PTR_CAP_PTR_MASK                                   0xff

typedef union {
  struct {
    UINT8                                             CAP_PTR:8;
  } Field;
  UINT8 Value;
} CAP_PTR_NBIFRCCFG_STRUCT;

#define PCICFG_NBIFRCCFG_CAP_PTR_OFFSET                             0x34
#define SMN_DEV0_NBIF0RCNBIO0_CAP_PTR_ADDRESS                         0x10100034UL
#define SMN_DEV0_NBIF0RCNBIO1_CAP_PTR_ADDRESS                         0x10300034UL
#define SMN_DEV0_NBIF1RCNBIO0_CAP_PTR_ADDRESS                         0x10200034UL
#define SMN_DEV0_NBIF1RCNBIO1_CAP_PTR_ADDRESS                         0x10400034UL
#define SMN_DEV1_NBIF0RCNBIO0_CAP_PTR_ADDRESS                         0x10101034UL
#define SMN_DEV1_NBIF0RCNBIO1_CAP_PTR_ADDRESS                         0x10301034UL
#define SMN_DEV1_NBIF1RCNBIO0_CAP_PTR_ADDRESS                         0x10201034UL
#define SMN_DEV1_NBIF1RCNBIO1_CAP_PTR_ADDRESS                         0x10401034UL


/***********************************************************
* Register Name : COMMAND
************************************************************/

#define COMMAND_IOEN_DN_OFFSET                                 0
#define COMMAND_IOEN_DN_MASK                                   0x1

#define COMMAND_MEMEN_DN_OFFSET                                1
#define COMMAND_MEMEN_DN_MASK                                  0x2

#define COMMAND_BUS_MASTER_EN_OFFSET                           2
#define COMMAND_BUS_MASTER_EN_MASK                             0x4

#define COMMAND_SPECIAL_CYCLE_EN_OFFSET                        3
#define COMMAND_SPECIAL_CYCLE_EN_MASK                          0x8

#define COMMAND_MEM_WRITE_INVALIDATE_EN_OFFSET                 4
#define COMMAND_MEM_WRITE_INVALIDATE_EN_MASK                   0x10

#define COMMAND_PAL_SNOOP_EN_OFFSET                            5
#define COMMAND_PAL_SNOOP_EN_MASK                              0x20

#define COMMAND_PARITY_ERROR_RESPONSE_OFFSET                   6
#define COMMAND_PARITY_ERROR_RESPONSE_MASK                     0x40

#define COMMAND_AD_STEPPING_OFFSET                             7
#define COMMAND_AD_STEPPING_MASK                               0x80

#define COMMAND_SERR_EN_OFFSET                                 8
#define COMMAND_SERR_EN_MASK                                   0x100

#define COMMAND_FAST_B2B_EN_OFFSET                             9
#define COMMAND_FAST_B2B_EN_MASK                               0x200

#define COMMAND_INT_DIS_OFFSET                                 10
#define COMMAND_INT_DIS_MASK                                   0x400

#define COMMAND_Reserved_15_11_OFFSET                          11
#define COMMAND_Reserved_15_11_MASK                            0xf800

typedef union {
  struct {
    UINT16                                             IOEN_DN:1;
    UINT16                                            MEMEN_DN:1;
    UINT16                                       BUS_MASTER_EN:1;
    UINT16                                    SPECIAL_CYCLE_EN:1;
    UINT16                             MEM_WRITE_INVALIDATE_EN:1;
    UINT16                                        PAL_SNOOP_EN:1;
    UINT16                               PARITY_ERROR_RESPONSE:1;
    UINT16                                         AD_STEPPING:1;
    UINT16                                             SERR_EN:1;
    UINT16                                         FAST_B2B_EN:1;
    UINT16                                             INT_DIS:1;
    UINT16                                      Reserved_15_11:5;
  } Field;
  UINT16 Value;
} COMMAND_NBIFRCCFG_STRUCT;

#define PCICFG_NBIFRCCFG_COMMAND_OFFSET                             0x4
#define SMN_DEV0_NBIF0RCNBIO0_COMMAND_ADDRESS                         0x10100004UL
#define SMN_DEV0_NBIF0RCNBIO1_COMMAND_ADDRESS                         0x10300004UL
#define SMN_DEV0_NBIF1RCNBIO0_COMMAND_ADDRESS                         0x10200004UL
#define SMN_DEV0_NBIF1RCNBIO1_COMMAND_ADDRESS                         0x10400004UL
#define SMN_DEV1_NBIF0RCNBIO0_COMMAND_ADDRESS                         0x10101004UL
#define SMN_DEV1_NBIF0RCNBIO1_COMMAND_ADDRESS                         0x10301004UL
#define SMN_DEV1_NBIF1RCNBIO0_COMMAND_ADDRESS                         0x10201004UL
#define SMN_DEV1_NBIF1RCNBIO1_COMMAND_ADDRESS                         0x10401004UL


/***********************************************************
* Register Name : DATA_LINK_FEATURE_CAP
************************************************************/

#define DATA_LINK_FEATURE_CAP_LOCAL_DLF_SUPPORTED_OFFSET       0
#define DATA_LINK_FEATURE_CAP_LOCAL_DLF_SUPPORTED_MASK         0x7fffff

#define DATA_LINK_FEATURE_CAP_Reserved_30_23_OFFSET            23
#define DATA_LINK_FEATURE_CAP_Reserved_30_23_MASK              0x7f800000

#define DATA_LINK_FEATURE_CAP_DLF_EXCHANGE_ENABLE_OFFSET       31
#define DATA_LINK_FEATURE_CAP_DLF_EXCHANGE_ENABLE_MASK         0x80000000

typedef union {
  struct {
    UINT32                                 LOCAL_DLF_SUPPORTED:23;
    UINT32                                      Reserved_30_23:8;
    UINT32                                 DLF_EXCHANGE_ENABLE:1;
  } Field;
  UINT32 Value;
} DATA_LINK_FEATURE_CAP_NBIFRCCFG_STRUCT;

#define PCICFG_NBIFRCCFG_DATA_LINK_FEATURE_CAP_OFFSET               0x404
#define SMN_DEV0_NBIF0RCNBIO0_DATA_LINK_FEATURE_CAP_ADDRESS           0x10100404UL
#define SMN_DEV0_NBIF0RCNBIO1_DATA_LINK_FEATURE_CAP_ADDRESS           0x10300404UL
#define SMN_DEV0_NBIF1RCNBIO0_DATA_LINK_FEATURE_CAP_ADDRESS           0x10200404UL
#define SMN_DEV0_NBIF1RCNBIO1_DATA_LINK_FEATURE_CAP_ADDRESS           0x10400404UL
#define SMN_DEV1_NBIF0RCNBIO0_DATA_LINK_FEATURE_CAP_ADDRESS           0x10101404UL
#define SMN_DEV1_NBIF0RCNBIO1_DATA_LINK_FEATURE_CAP_ADDRESS           0x10301404UL
#define SMN_DEV1_NBIF1RCNBIO0_DATA_LINK_FEATURE_CAP_ADDRESS           0x10201404UL
#define SMN_DEV1_NBIF1RCNBIO1_DATA_LINK_FEATURE_CAP_ADDRESS           0x10401404UL


/***********************************************************
* Register Name : DATA_LINK_FEATURE_STATUS
************************************************************/

#define DATA_LINK_FEATURE_STATUS_REMOTE_DLF_SUPPORTED_OFFSET   0
#define DATA_LINK_FEATURE_STATUS_REMOTE_DLF_SUPPORTED_MASK     0x7fffff

#define DATA_LINK_FEATURE_STATUS_Reserved_30_23_OFFSET         23
#define DATA_LINK_FEATURE_STATUS_Reserved_30_23_MASK           0x7f800000

#define DATA_LINK_FEATURE_STATUS_REMOTE_DLF_SUPPORTED_VALID_OFFSET 31
#define DATA_LINK_FEATURE_STATUS_REMOTE_DLF_SUPPORTED_VALID_MASK 0x80000000

typedef union {
  struct {
    UINT32                                REMOTE_DLF_SUPPORTED:23;
    UINT32                                      Reserved_30_23:8;
    UINT32                          REMOTE_DLF_SUPPORTED_VALID:1;
  } Field;
  UINT32 Value;
} DATA_LINK_FEATURE_STATUS_NBIFRCCFG_STRUCT;

#define PCICFG_NBIFRCCFG_DATA_LINK_FEATURE_STATUS_OFFSET            0x408
#define SMN_DEV0_NBIF0RCNBIO0_DATA_LINK_FEATURE_STATUS_ADDRESS        0x10100408UL
#define SMN_DEV0_NBIF0RCNBIO1_DATA_LINK_FEATURE_STATUS_ADDRESS        0x10300408UL
#define SMN_DEV0_NBIF1RCNBIO0_DATA_LINK_FEATURE_STATUS_ADDRESS        0x10200408UL
#define SMN_DEV0_NBIF1RCNBIO1_DATA_LINK_FEATURE_STATUS_ADDRESS        0x10400408UL
#define SMN_DEV1_NBIF0RCNBIO0_DATA_LINK_FEATURE_STATUS_ADDRESS        0x10101408UL
#define SMN_DEV1_NBIF0RCNBIO1_DATA_LINK_FEATURE_STATUS_ADDRESS        0x10301408UL
#define SMN_DEV1_NBIF1RCNBIO0_DATA_LINK_FEATURE_STATUS_ADDRESS        0x10201408UL
#define SMN_DEV1_NBIF1RCNBIO1_DATA_LINK_FEATURE_STATUS_ADDRESS        0x10401408UL


/***********************************************************
* Register Name : DEVICE_CAP
************************************************************/

#define DEVICE_CAP_MAX_PAYLOAD_SUPPORT_OFFSET                  0
#define DEVICE_CAP_MAX_PAYLOAD_SUPPORT_MASK                    0x7

#define DEVICE_CAP_PHANTOM_FUNC_OFFSET                         3
#define DEVICE_CAP_PHANTOM_FUNC_MASK                           0x18

#define DEVICE_CAP_EXTENDED_TAG_OFFSET                         5
#define DEVICE_CAP_EXTENDED_TAG_MASK                           0x20

#define DEVICE_CAP_L0S_ACCEPTABLE_LATENCY_OFFSET               6
#define DEVICE_CAP_L0S_ACCEPTABLE_LATENCY_MASK                 0x1c0

#define DEVICE_CAP_L1_ACCEPTABLE_LATENCY_OFFSET                9
#define DEVICE_CAP_L1_ACCEPTABLE_LATENCY_MASK                  0xe00

#define DEVICE_CAP_Reserved_14_12_OFFSET                       12
#define DEVICE_CAP_Reserved_14_12_MASK                         0x7000

#define DEVICE_CAP_ROLE_BASED_ERR_REPORTING_OFFSET             15
#define DEVICE_CAP_ROLE_BASED_ERR_REPORTING_MASK               0x8000

#define DEVICE_CAP_ERR_COR_SUBCLASS_CAPABLE_OFFSET             16
#define DEVICE_CAP_ERR_COR_SUBCLASS_CAPABLE_MASK               0x10000

#define DEVICE_CAP_Reserved_17_17_OFFSET                       17
#define DEVICE_CAP_Reserved_17_17_MASK                         0x20000

#define DEVICE_CAP_CAPTURED_SLOT_POWER_LIMIT_OFFSET            18
#define DEVICE_CAP_CAPTURED_SLOT_POWER_LIMIT_MASK              0x3fc0000

#define DEVICE_CAP_CAPTURED_SLOT_POWER_SCALE_OFFSET            26
#define DEVICE_CAP_CAPTURED_SLOT_POWER_SCALE_MASK              0xc000000

#define DEVICE_CAP_FLR_CAPABLE_OFFSET                          28
#define DEVICE_CAP_FLR_CAPABLE_MASK                            0x10000000

#define DEVICE_CAP_Reserved_31_29_OFFSET                       29
#define DEVICE_CAP_Reserved_31_29_MASK                         0xe0000000

typedef union {
  struct {
    UINT32                                 MAX_PAYLOAD_SUPPORT:3;
    UINT32                                        PHANTOM_FUNC:2;
    UINT32                                        EXTENDED_TAG:1;
    UINT32                              L0S_ACCEPTABLE_LATENCY:3;
    UINT32                               L1_ACCEPTABLE_LATENCY:3;
    UINT32                                      Reserved_14_12:3;
    UINT32                            ROLE_BASED_ERR_REPORTING:1;
    UINT32                            ERR_COR_SUBCLASS_CAPABLE:1;
    UINT32                                      Reserved_17_17:1;
    UINT32                           CAPTURED_SLOT_POWER_LIMIT:8;
    UINT32                           CAPTURED_SLOT_POWER_SCALE:2;
    UINT32                                         FLR_CAPABLE:1;
    UINT32                                      Reserved_31_29:3;
  } Field;
  UINT32 Value;
} DEVICE_CAP_NBIFRCCFG_STRUCT;

#define PCICFG_NBIFRCCFG_DEVICE_CAP_OFFSET                          0x5c
#define SMN_DEV0_NBIF0RCNBIO0_DEVICE_CAP_ADDRESS                      0x1010005cUL
#define SMN_DEV0_NBIF0RCNBIO1_DEVICE_CAP_ADDRESS                      0x1030005cUL
#define SMN_DEV0_NBIF1RCNBIO0_DEVICE_CAP_ADDRESS                      0x1020005cUL
#define SMN_DEV0_NBIF1RCNBIO1_DEVICE_CAP_ADDRESS                      0x1040005cUL
#define SMN_DEV1_NBIF0RCNBIO0_DEVICE_CAP_ADDRESS                      0x1010105cUL
#define SMN_DEV1_NBIF0RCNBIO1_DEVICE_CAP_ADDRESS                      0x1030105cUL
#define SMN_DEV1_NBIF1RCNBIO0_DEVICE_CAP_ADDRESS                      0x1020105cUL
#define SMN_DEV1_NBIF1RCNBIO1_DEVICE_CAP_ADDRESS                      0x1040105cUL


/***********************************************************
* Register Name : DEVICE_CAP2
************************************************************/

#define DEVICE_CAP2_CPL_TIMEOUT_RANGE_SUPPORTED_OFFSET         0
#define DEVICE_CAP2_CPL_TIMEOUT_RANGE_SUPPORTED_MASK           0xf

#define DEVICE_CAP2_CPL_TIMEOUT_DIS_SUPPORTED_OFFSET           4
#define DEVICE_CAP2_CPL_TIMEOUT_DIS_SUPPORTED_MASK             0x10

#define DEVICE_CAP2_ARI_FORWARDING_SUPPORTED_OFFSET            5
#define DEVICE_CAP2_ARI_FORWARDING_SUPPORTED_MASK              0x20

#define DEVICE_CAP2_ATOMICOP_ROUTING_SUPPORTED_OFFSET          6
#define DEVICE_CAP2_ATOMICOP_ROUTING_SUPPORTED_MASK            0x40

#define DEVICE_CAP2_ATOMICOP_32CMPLT_SUPPORTED_OFFSET          7
#define DEVICE_CAP2_ATOMICOP_32CMPLT_SUPPORTED_MASK            0x80

#define DEVICE_CAP2_ATOMICOP_64CMPLT_SUPPORTED_OFFSET          8
#define DEVICE_CAP2_ATOMICOP_64CMPLT_SUPPORTED_MASK            0x100

#define DEVICE_CAP2_CAS128_CMPLT_SUPPORTED_OFFSET              9
#define DEVICE_CAP2_CAS128_CMPLT_SUPPORTED_MASK                0x200

#define DEVICE_CAP2_NO_RO_ENABLED_P2P_PASSING_OFFSET           10
#define DEVICE_CAP2_NO_RO_ENABLED_P2P_PASSING_MASK             0x400

#define DEVICE_CAP2_LTR_SUPPORTED_OFFSET                       11
#define DEVICE_CAP2_LTR_SUPPORTED_MASK                         0x800

#define DEVICE_CAP2_TPH_CPLR_SUPPORTED_OFFSET                  12
#define DEVICE_CAP2_TPH_CPLR_SUPPORTED_MASK                    0x3000

#define DEVICE_CAP2_LN_SYSTEM_CLS_OFFSET                       14
#define DEVICE_CAP2_LN_SYSTEM_CLS_MASK                         0xc000

#define DEVICE_CAP2_TEN_BIT_TAG_COMPLETER_SUPPORTED_OFFSET     16
#define DEVICE_CAP2_TEN_BIT_TAG_COMPLETER_SUPPORTED_MASK       0x10000

#define DEVICE_CAP2_TEN_BIT_TAG_REQUESTER_SUPPORTED_OFFSET     17
#define DEVICE_CAP2_TEN_BIT_TAG_REQUESTER_SUPPORTED_MASK       0x20000

#define DEVICE_CAP2_OBFF_SUPPORTED_OFFSET                      18
#define DEVICE_CAP2_OBFF_SUPPORTED_MASK                        0xc0000

#define DEVICE_CAP2_EXTENDED_FMT_FIELD_SUPPORTED_OFFSET        20
#define DEVICE_CAP2_EXTENDED_FMT_FIELD_SUPPORTED_MASK          0x100000

#define DEVICE_CAP2_END_END_TLP_PREFIX_SUPPORTED_OFFSET        21
#define DEVICE_CAP2_END_END_TLP_PREFIX_SUPPORTED_MASK          0x200000

#define DEVICE_CAP2_MAX_END_END_TLP_PREFIXES_OFFSET            22
#define DEVICE_CAP2_MAX_END_END_TLP_PREFIXES_MASK              0xc00000

#define DEVICE_CAP2_EMER_POWER_REDUCTION_SUPPORTED_OFFSET      24
#define DEVICE_CAP2_EMER_POWER_REDUCTION_SUPPORTED_MASK        0x3000000

#define DEVICE_CAP2_EMER_POWER_REDUCTION_INIT_REQ_OFFSET       26
#define DEVICE_CAP2_EMER_POWER_REDUCTION_INIT_REQ_MASK         0x4000000

#define DEVICE_CAP2_Reserved_30_27_OFFSET                      27
#define DEVICE_CAP2_Reserved_30_27_MASK                        0x78000000

#define DEVICE_CAP2_FRS_SUPPORTED_OFFSET                       31
#define DEVICE_CAP2_FRS_SUPPORTED_MASK                         0x80000000

typedef union {
  struct {
    UINT32                         CPL_TIMEOUT_RANGE_SUPPORTED:4;
    UINT32                           CPL_TIMEOUT_DIS_SUPPORTED:1;
    UINT32                            ARI_FORWARDING_SUPPORTED:1;
    UINT32                          ATOMICOP_ROUTING_SUPPORTED:1;
    UINT32                          ATOMICOP_32CMPLT_SUPPORTED:1;
    UINT32                          ATOMICOP_64CMPLT_SUPPORTED:1;
    UINT32                              CAS128_CMPLT_SUPPORTED:1;
    UINT32                           NO_RO_ENABLED_P2P_PASSING:1;
    UINT32                                       LTR_SUPPORTED:1;
    UINT32                                  TPH_CPLR_SUPPORTED:2;
    UINT32                                       LN_SYSTEM_CLS:2;
    UINT32                     TEN_BIT_TAG_COMPLETER_SUPPORTED:1;
    UINT32                     TEN_BIT_TAG_REQUESTER_SUPPORTED:1;
    UINT32                                      OBFF_SUPPORTED:2;
    UINT32                        EXTENDED_FMT_FIELD_SUPPORTED:1;
    UINT32                        END_END_TLP_PREFIX_SUPPORTED:1;
    UINT32                            MAX_END_END_TLP_PREFIXES:2;
    UINT32                      EMER_POWER_REDUCTION_SUPPORTED:2;
    UINT32                       EMER_POWER_REDUCTION_INIT_REQ:1;
    UINT32                                      Reserved_30_27:4;
    UINT32                                       FRS_SUPPORTED:1;
  } Field;
  UINT32 Value;
} DEVICE_CAP2_NBIFRCCFG_STRUCT;

#define PCICFG_NBIFRCCFG_DEVICE_CAP2_OFFSET                         0x7c
#define SMN_DEV0_NBIF0RCNBIO0_DEVICE_CAP2_ADDRESS                     0x1010007cUL
#define SMN_DEV0_NBIF0RCNBIO1_DEVICE_CAP2_ADDRESS                     0x1030007cUL
#define SMN_DEV0_NBIF1RCNBIO0_DEVICE_CAP2_ADDRESS                     0x1020007cUL
#define SMN_DEV0_NBIF1RCNBIO1_DEVICE_CAP2_ADDRESS                     0x1040007cUL
#define SMN_DEV1_NBIF0RCNBIO0_DEVICE_CAP2_ADDRESS                     0x1010107cUL
#define SMN_DEV1_NBIF0RCNBIO1_DEVICE_CAP2_ADDRESS                     0x1030107cUL
#define SMN_DEV1_NBIF1RCNBIO0_DEVICE_CAP2_ADDRESS                     0x1020107cUL
#define SMN_DEV1_NBIF1RCNBIO1_DEVICE_CAP2_ADDRESS                     0x1040107cUL


/***********************************************************
* Register Name : DEVICE_CNTL
************************************************************/

#define DEVICE_CNTL_CORR_ERR_EN_OFFSET                         0
#define DEVICE_CNTL_CORR_ERR_EN_MASK                           0x1

#define DEVICE_CNTL_NON_FATAL_ERR_EN_OFFSET                    1
#define DEVICE_CNTL_NON_FATAL_ERR_EN_MASK                      0x2

#define DEVICE_CNTL_FATAL_ERR_EN_OFFSET                        2
#define DEVICE_CNTL_FATAL_ERR_EN_MASK                          0x4

#define DEVICE_CNTL_USR_REPORT_EN_OFFSET                       3
#define DEVICE_CNTL_USR_REPORT_EN_MASK                         0x8

#define DEVICE_CNTL_RELAXED_ORD_EN_OFFSET                      4
#define DEVICE_CNTL_RELAXED_ORD_EN_MASK                        0x10

#define DEVICE_CNTL_MAX_PAYLOAD_SIZE_OFFSET                    5
#define DEVICE_CNTL_MAX_PAYLOAD_SIZE_MASK                      0xe0

#define DEVICE_CNTL_EXTENDED_TAG_EN_OFFSET                     8
#define DEVICE_CNTL_EXTENDED_TAG_EN_MASK                       0x100

#define DEVICE_CNTL_PHANTOM_FUNC_EN_OFFSET                     9
#define DEVICE_CNTL_PHANTOM_FUNC_EN_MASK                       0x200

#define DEVICE_CNTL_AUX_POWER_PM_EN_OFFSET                     10
#define DEVICE_CNTL_AUX_POWER_PM_EN_MASK                       0x400

#define DEVICE_CNTL_NO_SNOOP_EN_OFFSET                         11
#define DEVICE_CNTL_NO_SNOOP_EN_MASK                           0x800

#define DEVICE_CNTL_MAX_READ_REQUEST_SIZE_OFFSET               12
#define DEVICE_CNTL_MAX_READ_REQUEST_SIZE_MASK                 0x7000

#define DEVICE_CNTL_BRIDGE_CFG_RETRY_EN_OFFSET                 15
#define DEVICE_CNTL_BRIDGE_CFG_RETRY_EN_MASK                   0x8000

typedef union {
  struct {
    UINT16                                         CORR_ERR_EN:1;
    UINT16                                    NON_FATAL_ERR_EN:1;
    UINT16                                        FATAL_ERR_EN:1;
    UINT16                                       USR_REPORT_EN:1;
    UINT16                                      RELAXED_ORD_EN:1;
    UINT16                                    MAX_PAYLOAD_SIZE:3;
    UINT16                                     EXTENDED_TAG_EN:1;
    UINT16                                     PHANTOM_FUNC_EN:1;
    UINT16                                     AUX_POWER_PM_EN:1;
    UINT16                                         NO_SNOOP_EN:1;
    UINT16                               MAX_READ_REQUEST_SIZE:3;
    UINT16                                 BRIDGE_CFG_RETRY_EN:1;
  } Field;
  UINT16 Value;
} DEVICE_CNTL_NBIFRCCFG_STRUCT;

#define PCICFG_NBIFRCCFG_DEVICE_CNTL_OFFSET                         0x60
#define SMN_DEV0_NBIF0RCNBIO0_DEVICE_CNTL_ADDRESS                     0x10100060UL
#define SMN_DEV0_NBIF0RCNBIO1_DEVICE_CNTL_ADDRESS                     0x10300060UL
#define SMN_DEV0_NBIF1RCNBIO0_DEVICE_CNTL_ADDRESS                     0x10200060UL
#define SMN_DEV0_NBIF1RCNBIO1_DEVICE_CNTL_ADDRESS                     0x10400060UL
#define SMN_DEV1_NBIF0RCNBIO0_DEVICE_CNTL_ADDRESS                     0x10101060UL
#define SMN_DEV1_NBIF0RCNBIO1_DEVICE_CNTL_ADDRESS                     0x10301060UL
#define SMN_DEV1_NBIF1RCNBIO0_DEVICE_CNTL_ADDRESS                     0x10201060UL
#define SMN_DEV1_NBIF1RCNBIO1_DEVICE_CNTL_ADDRESS                     0x10401060UL


/***********************************************************
* Register Name : DEVICE_CNTL2
************************************************************/

#define DEVICE_CNTL2_CPL_TIMEOUT_VALUE_OFFSET                  0
#define DEVICE_CNTL2_CPL_TIMEOUT_VALUE_MASK                    0xf

#define DEVICE_CNTL2_CPL_TIMEOUT_DIS_OFFSET                    4
#define DEVICE_CNTL2_CPL_TIMEOUT_DIS_MASK                      0x10

#define DEVICE_CNTL2_ARI_FORWARDING_EN_OFFSET                  5
#define DEVICE_CNTL2_ARI_FORWARDING_EN_MASK                    0x20

#define DEVICE_CNTL2_ATOMICOP_REQUEST_EN_OFFSET                6
#define DEVICE_CNTL2_ATOMICOP_REQUEST_EN_MASK                  0x40

#define DEVICE_CNTL2_ATOMICOP_EGRESS_BLOCKING_OFFSET           7
#define DEVICE_CNTL2_ATOMICOP_EGRESS_BLOCKING_MASK             0x80

#define DEVICE_CNTL2_IDO_REQUEST_ENABLE_OFFSET                 8
#define DEVICE_CNTL2_IDO_REQUEST_ENABLE_MASK                   0x100

#define DEVICE_CNTL2_IDO_COMPLETION_ENABLE_OFFSET              9
#define DEVICE_CNTL2_IDO_COMPLETION_ENABLE_MASK                0x200

#define DEVICE_CNTL2_LTR_EN_OFFSET                             10
#define DEVICE_CNTL2_LTR_EN_MASK                               0x400

#define DEVICE_CNTL2_EMER_POWER_REDUCTION_REQUEST_OFFSET       11
#define DEVICE_CNTL2_EMER_POWER_REDUCTION_REQUEST_MASK         0x800

#define DEVICE_CNTL2_TEN_BIT_TAG_REQUESTER_ENABLE_OFFSET       12
#define DEVICE_CNTL2_TEN_BIT_TAG_REQUESTER_ENABLE_MASK         0x1000

#define DEVICE_CNTL2_OBFF_EN_OFFSET                            13
#define DEVICE_CNTL2_OBFF_EN_MASK                              0x6000

#define DEVICE_CNTL2_END_END_TLP_PREFIX_BLOCKING_OFFSET        15
#define DEVICE_CNTL2_END_END_TLP_PREFIX_BLOCKING_MASK          0x8000

typedef union {
  struct {
    UINT16                                   CPL_TIMEOUT_VALUE:4;
    UINT16                                     CPL_TIMEOUT_DIS:1;
    UINT16                                   ARI_FORWARDING_EN:1;
    UINT16                                 ATOMICOP_REQUEST_EN:1;
    UINT16                            ATOMICOP_EGRESS_BLOCKING:1;
    UINT16                                  IDO_REQUEST_ENABLE:1;
    UINT16                               IDO_COMPLETION_ENABLE:1;
    UINT16                                              LTR_EN:1;
    UINT16                        EMER_POWER_REDUCTION_REQUEST:1;
    UINT16                        TEN_BIT_TAG_REQUESTER_ENABLE:1;
    UINT16                                             OBFF_EN:2;
    UINT16                         END_END_TLP_PREFIX_BLOCKING:1;
  } Field;
  UINT16 Value;
} DEVICE_CNTL2_NBIFRCCFG_STRUCT;

#define PCICFG_NBIFRCCFG_DEVICE_CNTL2_OFFSET                        0x80
#define SMN_DEV0_NBIF0RCNBIO0_DEVICE_CNTL2_ADDRESS                    0x10100080UL
#define SMN_DEV0_NBIF0RCNBIO1_DEVICE_CNTL2_ADDRESS                    0x10300080UL
#define SMN_DEV0_NBIF1RCNBIO0_DEVICE_CNTL2_ADDRESS                    0x10200080UL
#define SMN_DEV0_NBIF1RCNBIO1_DEVICE_CNTL2_ADDRESS                    0x10400080UL
#define SMN_DEV1_NBIF0RCNBIO0_DEVICE_CNTL2_ADDRESS                    0x10101080UL
#define SMN_DEV1_NBIF0RCNBIO1_DEVICE_CNTL2_ADDRESS                    0x10301080UL
#define SMN_DEV1_NBIF1RCNBIO0_DEVICE_CNTL2_ADDRESS                    0x10201080UL
#define SMN_DEV1_NBIF1RCNBIO1_DEVICE_CNTL2_ADDRESS                    0x10401080UL


/***********************************************************
* Register Name : DEVICE_ID
************************************************************/

#define DEVICE_ID_DEVICE_ID_OFFSET                             0
#define DEVICE_ID_DEVICE_ID_MASK                               0xffff

typedef union {
  struct {
    UINT16                                           DEVICE_ID:16;
  } Field;
  UINT16 Value;
} DEVICE_ID_NBIFRCCFG_STRUCT;

#define PCICFG_NBIFRCCFG_DEVICE_ID_OFFSET                           0x2
#define SMN_DEV0_NBIF0RCNBIO0_DEVICE_ID_ADDRESS                       0x10100002UL
#define SMN_DEV0_NBIF0RCNBIO1_DEVICE_ID_ADDRESS                       0x10300002UL
#define SMN_DEV0_NBIF1RCNBIO0_DEVICE_ID_ADDRESS                       0x10200002UL
#define SMN_DEV0_NBIF1RCNBIO1_DEVICE_ID_ADDRESS                       0x10400002UL
#define SMN_DEV1_NBIF0RCNBIO0_DEVICE_ID_ADDRESS                       0x10101002UL
#define SMN_DEV1_NBIF0RCNBIO1_DEVICE_ID_ADDRESS                       0x10301002UL
#define SMN_DEV1_NBIF1RCNBIO0_DEVICE_ID_ADDRESS                       0x10201002UL
#define SMN_DEV1_NBIF1RCNBIO1_DEVICE_ID_ADDRESS                       0x10401002UL


/***********************************************************
* Register Name : DEVICE_STATUS
************************************************************/

#define DEVICE_STATUS_CORR_ERR_OFFSET                          0
#define DEVICE_STATUS_CORR_ERR_MASK                            0x1

#define DEVICE_STATUS_NON_FATAL_ERR_OFFSET                     1
#define DEVICE_STATUS_NON_FATAL_ERR_MASK                       0x2

#define DEVICE_STATUS_FATAL_ERR_OFFSET                         2
#define DEVICE_STATUS_FATAL_ERR_MASK                           0x4

#define DEVICE_STATUS_USR_DETECTED_OFFSET                      3
#define DEVICE_STATUS_USR_DETECTED_MASK                        0x8

#define DEVICE_STATUS_AUX_PWR_OFFSET                           4
#define DEVICE_STATUS_AUX_PWR_MASK                             0x10

#define DEVICE_STATUS_TRANSACTIONS_PEND_OFFSET                 5
#define DEVICE_STATUS_TRANSACTIONS_PEND_MASK                   0x20

#define DEVICE_STATUS_EMER_POWER_REDUCTION_DETECTED_OFFSET     6
#define DEVICE_STATUS_EMER_POWER_REDUCTION_DETECTED_MASK       0x40

#define DEVICE_STATUS_Reserved_15_7_OFFSET                     7
#define DEVICE_STATUS_Reserved_15_7_MASK                       0xff80

typedef union {
  struct {
    UINT16                                            CORR_ERR:1;
    UINT16                                       NON_FATAL_ERR:1;
    UINT16                                           FATAL_ERR:1;
    UINT16                                        USR_DETECTED:1;
    UINT16                                             AUX_PWR:1;
    UINT16                                   TRANSACTIONS_PEND:1;
    UINT16                       EMER_POWER_REDUCTION_DETECTED:1;
    UINT16                                       Reserved_15_7:9;
  } Field;
  UINT16 Value;
} DEVICE_STATUS_NBIFRCCFG_STRUCT;

#define PCICFG_NBIFRCCFG_DEVICE_STATUS_OFFSET                       0x62
#define SMN_DEV0_NBIF0RCNBIO0_DEVICE_STATUS_ADDRESS                   0x10100062UL
#define SMN_DEV0_NBIF0RCNBIO1_DEVICE_STATUS_ADDRESS                   0x10300062UL
#define SMN_DEV0_NBIF1RCNBIO0_DEVICE_STATUS_ADDRESS                   0x10200062UL
#define SMN_DEV0_NBIF1RCNBIO1_DEVICE_STATUS_ADDRESS                   0x10400062UL
#define SMN_DEV1_NBIF0RCNBIO0_DEVICE_STATUS_ADDRESS                   0x10101062UL
#define SMN_DEV1_NBIF0RCNBIO1_DEVICE_STATUS_ADDRESS                   0x10301062UL
#define SMN_DEV1_NBIF1RCNBIO0_DEVICE_STATUS_ADDRESS                   0x10201062UL
#define SMN_DEV1_NBIF1RCNBIO1_DEVICE_STATUS_ADDRESS                   0x10401062UL


/***********************************************************
* Register Name : DEVICE_STATUS2
************************************************************/

#define DEVICE_STATUS2_Reserved_15_0_OFFSET                    0
#define DEVICE_STATUS2_Reserved_15_0_MASK                      0xffff

typedef union {
  struct {
    UINT16                                       Reserved_15_0:16;
  } Field;
  UINT16 Value;
} DEVICE_STATUS2_NBIFRCCFG_STRUCT;

#define PCICFG_NBIFRCCFG_DEVICE_STATUS2_OFFSET                      0x82
#define SMN_DEV0_NBIF0RCNBIO0_DEVICE_STATUS2_ADDRESS                  0x10100082UL
#define SMN_DEV0_NBIF0RCNBIO1_DEVICE_STATUS2_ADDRESS                  0x10300082UL
#define SMN_DEV0_NBIF1RCNBIO0_DEVICE_STATUS2_ADDRESS                  0x10200082UL
#define SMN_DEV0_NBIF1RCNBIO1_DEVICE_STATUS2_ADDRESS                  0x10400082UL
#define SMN_DEV1_NBIF0RCNBIO0_DEVICE_STATUS2_ADDRESS                  0x10101082UL
#define SMN_DEV1_NBIF0RCNBIO1_DEVICE_STATUS2_ADDRESS                  0x10301082UL
#define SMN_DEV1_NBIF1RCNBIO0_DEVICE_STATUS2_ADDRESS                  0x10201082UL
#define SMN_DEV1_NBIF1RCNBIO1_DEVICE_STATUS2_ADDRESS                  0x10401082UL


/***********************************************************
* Register Name : EXT_BRIDGE_CNTL
************************************************************/

#define EXT_BRIDGE_CNTL_IO_PORT_80_EN_OFFSET                   0
#define EXT_BRIDGE_CNTL_IO_PORT_80_EN_MASK                     0x1

#define EXT_BRIDGE_CNTL_Reserved_7_1_OFFSET                    1
#define EXT_BRIDGE_CNTL_Reserved_7_1_MASK                      0xfe

typedef union {
  struct {
    UINT8                                       IO_PORT_80_EN:1;
    UINT8                                        Reserved_7_1:7;
  } Field;
  UINT8 Value;
} EXT_BRIDGE_CNTL_NBIFRCCFG_STRUCT;

#define PCICFG_NBIFRCCFG_EXT_BRIDGE_CNTL_OFFSET                     0x40
#define SMN_DEV0_NBIF0RCNBIO0_EXT_BRIDGE_CNTL_ADDRESS                 0x10100040UL
#define SMN_DEV0_NBIF0RCNBIO1_EXT_BRIDGE_CNTL_ADDRESS                 0x10300040UL
#define SMN_DEV0_NBIF1RCNBIO0_EXT_BRIDGE_CNTL_ADDRESS                 0x10200040UL
#define SMN_DEV0_NBIF1RCNBIO1_EXT_BRIDGE_CNTL_ADDRESS                 0x10400040UL
#define SMN_DEV1_NBIF0RCNBIO0_EXT_BRIDGE_CNTL_ADDRESS                 0x10101040UL
#define SMN_DEV1_NBIF0RCNBIO1_EXT_BRIDGE_CNTL_ADDRESS                 0x10301040UL
#define SMN_DEV1_NBIF1RCNBIO0_EXT_BRIDGE_CNTL_ADDRESS                 0x10201040UL
#define SMN_DEV1_NBIF1RCNBIO1_EXT_BRIDGE_CNTL_ADDRESS                 0x10401040UL


/***********************************************************
* Register Name : HEADER
************************************************************/

#define HEADER_HEADER_TYPE_OFFSET                              0
#define HEADER_HEADER_TYPE_MASK                                0x7f

#define HEADER_DEVICE_TYPE_OFFSET                              7
#define HEADER_DEVICE_TYPE_MASK                                0x80

typedef union {
  struct {
    UINT8                                         HEADER_TYPE:7;
    UINT8                                         DEVICE_TYPE:1;
  } Field;
  UINT8 Value;
} HEADER_NBIFRCCFG_STRUCT;

#define PCICFG_NBIFRCCFG_HEADER_OFFSET                              0xe
#define SMN_DEV0_NBIF0RCNBIO0_HEADER_ADDRESS                          0x1010000eUL
#define SMN_DEV0_NBIF0RCNBIO1_HEADER_ADDRESS                          0x1030000eUL
#define SMN_DEV0_NBIF1RCNBIO0_HEADER_ADDRESS                          0x1020000eUL
#define SMN_DEV0_NBIF1RCNBIO1_HEADER_ADDRESS                          0x1040000eUL
#define SMN_DEV1_NBIF0RCNBIO0_HEADER_ADDRESS                          0x1010100eUL
#define SMN_DEV1_NBIF0RCNBIO1_HEADER_ADDRESS                          0x1030100eUL
#define SMN_DEV1_NBIF1RCNBIO0_HEADER_ADDRESS                          0x1020100eUL
#define SMN_DEV1_NBIF1RCNBIO1_HEADER_ADDRESS                          0x1040100eUL


/***********************************************************
* Register Name : INTERRUPT_LINE
************************************************************/

#define INTERRUPT_LINE_INTERRUPT_LINE_OFFSET                   0
#define INTERRUPT_LINE_INTERRUPT_LINE_MASK                     0xff

typedef union {
  struct {
    UINT8                                      INTERRUPT_LINE:8;
  } Field;
  UINT8 Value;
} INTERRUPT_LINE_NBIFRCCFG_STRUCT;

#define PCICFG_NBIFRCCFG_INTERRUPT_LINE_OFFSET                      0x3c
#define SMN_DEV0_NBIF0RCNBIO0_INTERRUPT_LINE_ADDRESS                  0x1010003cUL
#define SMN_DEV0_NBIF0RCNBIO1_INTERRUPT_LINE_ADDRESS                  0x1030003cUL
#define SMN_DEV0_NBIF1RCNBIO0_INTERRUPT_LINE_ADDRESS                  0x1020003cUL
#define SMN_DEV0_NBIF1RCNBIO1_INTERRUPT_LINE_ADDRESS                  0x1040003cUL
#define SMN_DEV1_NBIF0RCNBIO0_INTERRUPT_LINE_ADDRESS                  0x1010103cUL
#define SMN_DEV1_NBIF0RCNBIO1_INTERRUPT_LINE_ADDRESS                  0x1030103cUL
#define SMN_DEV1_NBIF1RCNBIO0_INTERRUPT_LINE_ADDRESS                  0x1020103cUL
#define SMN_DEV1_NBIF1RCNBIO1_INTERRUPT_LINE_ADDRESS                  0x1040103cUL


/***********************************************************
* Register Name : INTERRUPT_PIN
************************************************************/

#define INTERRUPT_PIN_INTERRUPT_PIN_OFFSET                     0
#define INTERRUPT_PIN_INTERRUPT_PIN_MASK                       0xff

typedef union {
  struct {
    UINT8                                       INTERRUPT_PIN:8;
  } Field;
  UINT8 Value;
} INTERRUPT_PIN_NBIFRCCFG_STRUCT;

#define PCICFG_NBIFRCCFG_INTERRUPT_PIN_OFFSET                       0x3d
#define SMN_DEV0_NBIF0RCNBIO0_INTERRUPT_PIN_ADDRESS                   0x1010003dUL
#define SMN_DEV0_NBIF0RCNBIO1_INTERRUPT_PIN_ADDRESS                   0x1030003dUL
#define SMN_DEV0_NBIF1RCNBIO0_INTERRUPT_PIN_ADDRESS                   0x1020003dUL
#define SMN_DEV0_NBIF1RCNBIO1_INTERRUPT_PIN_ADDRESS                   0x1040003dUL
#define SMN_DEV1_NBIF0RCNBIO0_INTERRUPT_PIN_ADDRESS                   0x1010103dUL
#define SMN_DEV1_NBIF0RCNBIO1_INTERRUPT_PIN_ADDRESS                   0x1030103dUL
#define SMN_DEV1_NBIF1RCNBIO0_INTERRUPT_PIN_ADDRESS                   0x1020103dUL
#define SMN_DEV1_NBIF1RCNBIO1_INTERRUPT_PIN_ADDRESS                   0x1040103dUL


/***********************************************************
* Register Name : IO_BASE_LIMIT
************************************************************/

#define IO_BASE_LIMIT_IO_BASE_TYPE_OFFSET                      0
#define IO_BASE_LIMIT_IO_BASE_TYPE_MASK                        0xf

#define IO_BASE_LIMIT_IO_BASE_OFFSET                           4
#define IO_BASE_LIMIT_IO_BASE_MASK                             0xf0

#define IO_BASE_LIMIT_IO_LIMIT_TYPE_OFFSET                     8
#define IO_BASE_LIMIT_IO_LIMIT_TYPE_MASK                       0xf00

#define IO_BASE_LIMIT_IO_LIMIT_OFFSET                          12
#define IO_BASE_LIMIT_IO_LIMIT_MASK                            0xf000

typedef union {
  struct {
    UINT16                                        IO_BASE_TYPE:4;
    UINT16                                             IO_BASE:4;
    UINT16                                       IO_LIMIT_TYPE:4;
    UINT16                                            IO_LIMIT:4;
  } Field;
  UINT16 Value;
} IO_BASE_LIMIT_NBIFRCCFG_STRUCT;

#define PCICFG_NBIFRCCFG_IO_BASE_LIMIT_OFFSET                       0x1c
#define SMN_DEV0_NBIF0RCNBIO0_IO_BASE_LIMIT_ADDRESS                   0x1010001cUL
#define SMN_DEV0_NBIF0RCNBIO1_IO_BASE_LIMIT_ADDRESS                   0x1030001cUL
#define SMN_DEV0_NBIF1RCNBIO0_IO_BASE_LIMIT_ADDRESS                   0x1020001cUL
#define SMN_DEV0_NBIF1RCNBIO1_IO_BASE_LIMIT_ADDRESS                   0x1040001cUL
#define SMN_DEV1_NBIF0RCNBIO0_IO_BASE_LIMIT_ADDRESS                   0x1010101cUL
#define SMN_DEV1_NBIF0RCNBIO1_IO_BASE_LIMIT_ADDRESS                   0x1030101cUL
#define SMN_DEV1_NBIF1RCNBIO0_IO_BASE_LIMIT_ADDRESS                   0x1020101cUL
#define SMN_DEV1_NBIF1RCNBIO1_IO_BASE_LIMIT_ADDRESS                   0x1040101cUL


/***********************************************************
* Register Name : IO_BASE_LIMIT_HI
************************************************************/

#define IO_BASE_LIMIT_HI_IO_BASE_31_16_OFFSET                  0
#define IO_BASE_LIMIT_HI_IO_BASE_31_16_MASK                    0xffff

#define IO_BASE_LIMIT_HI_IO_LIMIT_31_16_OFFSET                 16
#define IO_BASE_LIMIT_HI_IO_LIMIT_31_16_MASK                   0xffff0000

typedef union {
  struct {
    UINT32                                       IO_BASE_31_16:16;
    UINT32                                      IO_LIMIT_31_16:16;
  } Field;
  UINT32 Value;
} IO_BASE_LIMIT_HI_NBIFRCCFG_STRUCT;

#define PCICFG_NBIFRCCFG_IO_BASE_LIMIT_HI_OFFSET                    0x30
#define SMN_DEV0_NBIF0RCNBIO0_IO_BASE_LIMIT_HI_ADDRESS                0x10100030UL
#define SMN_DEV0_NBIF0RCNBIO1_IO_BASE_LIMIT_HI_ADDRESS                0x10300030UL
#define SMN_DEV0_NBIF1RCNBIO0_IO_BASE_LIMIT_HI_ADDRESS                0x10200030UL
#define SMN_DEV0_NBIF1RCNBIO1_IO_BASE_LIMIT_HI_ADDRESS                0x10400030UL
#define SMN_DEV1_NBIF0RCNBIO0_IO_BASE_LIMIT_HI_ADDRESS                0x10101030UL
#define SMN_DEV1_NBIF0RCNBIO1_IO_BASE_LIMIT_HI_ADDRESS                0x10301030UL
#define SMN_DEV1_NBIF1RCNBIO0_IO_BASE_LIMIT_HI_ADDRESS                0x10201030UL
#define SMN_DEV1_NBIF1RCNBIO1_IO_BASE_LIMIT_HI_ADDRESS                0x10401030UL


/***********************************************************
* Register Name : LANE_EQUALIZATION_CNTL_16GT
************************************************************/

#define LANE_EQUALIZATION_CNTL_16GT_LANE_DSP_16GT_TX_PRESET_OFFSET 0
#define LANE_EQUALIZATION_CNTL_16GT_LANE_DSP_16GT_TX_PRESET_MASK 0xf

#define LANE_EQUALIZATION_CNTL_16GT_LANE_USP_16GT_TX_PRESET_OFFSET 4
#define LANE_EQUALIZATION_CNTL_16GT_LANE_USP_16GT_TX_PRESET_MASK 0xf0

typedef union {
  struct {
    UINT8                             LANE_DSP_16GT_TX_PRESET:4;
    UINT8                             LANE_USP_16GT_TX_PRESET:4;
  } Field;
  UINT8 Value;
} LANE_EQUALIZATION_CNTL_16GT_NBIFRCCFG_STRUCT;

#define PCICFG_NBIFRCCFG_LANE_EQUALIZATION_CNTL_16GT_OFFSET         0x430
#define SMN_DEV0_NBIF0RC_N0NBIO0_LANE_EQUALIZATION_CNTL_16GT_ADDRESS  0x10100430UL
#define SMN_DEV0_NBIF0RC_N0NBIO1_LANE_EQUALIZATION_CNTL_16GT_ADDRESS  0x10300430UL
#define SMN_DEV0_NBIF0RC_N10NBIO0_LANE_EQUALIZATION_CNTL_16GT_ADDRESS 0x1010043aUL
#define SMN_DEV0_NBIF0RC_N10NBIO1_LANE_EQUALIZATION_CNTL_16GT_ADDRESS 0x1030043aUL
#define SMN_DEV0_NBIF0RC_N11NBIO0_LANE_EQUALIZATION_CNTL_16GT_ADDRESS 0x1010043bUL
#define SMN_DEV0_NBIF0RC_N11NBIO1_LANE_EQUALIZATION_CNTL_16GT_ADDRESS 0x1030043bUL
#define SMN_DEV0_NBIF0RC_N12NBIO0_LANE_EQUALIZATION_CNTL_16GT_ADDRESS 0x1010043cUL
#define SMN_DEV0_NBIF0RC_N12NBIO1_LANE_EQUALIZATION_CNTL_16GT_ADDRESS 0x1030043cUL
#define SMN_DEV0_NBIF0RC_N13NBIO0_LANE_EQUALIZATION_CNTL_16GT_ADDRESS 0x1010043dUL
#define SMN_DEV0_NBIF0RC_N13NBIO1_LANE_EQUALIZATION_CNTL_16GT_ADDRESS 0x1030043dUL
#define SMN_DEV0_NBIF0RC_N14NBIO0_LANE_EQUALIZATION_CNTL_16GT_ADDRESS 0x1010043eUL
#define SMN_DEV0_NBIF0RC_N14NBIO1_LANE_EQUALIZATION_CNTL_16GT_ADDRESS 0x1030043eUL
#define SMN_DEV0_NBIF0RC_N15NBIO0_LANE_EQUALIZATION_CNTL_16GT_ADDRESS 0x1010043fUL
#define SMN_DEV0_NBIF0RC_N15NBIO1_LANE_EQUALIZATION_CNTL_16GT_ADDRESS 0x1030043fUL
#define SMN_DEV0_NBIF0RC_N1NBIO0_LANE_EQUALIZATION_CNTL_16GT_ADDRESS  0x10100431UL
#define SMN_DEV0_NBIF0RC_N1NBIO1_LANE_EQUALIZATION_CNTL_16GT_ADDRESS  0x10300431UL
#define SMN_DEV0_NBIF0RC_N2NBIO0_LANE_EQUALIZATION_CNTL_16GT_ADDRESS  0x10100432UL
#define SMN_DEV0_NBIF0RC_N2NBIO1_LANE_EQUALIZATION_CNTL_16GT_ADDRESS  0x10300432UL
#define SMN_DEV0_NBIF0RC_N3NBIO0_LANE_EQUALIZATION_CNTL_16GT_ADDRESS  0x10100433UL
#define SMN_DEV0_NBIF0RC_N3NBIO1_LANE_EQUALIZATION_CNTL_16GT_ADDRESS  0x10300433UL
#define SMN_DEV0_NBIF0RC_N4NBIO0_LANE_EQUALIZATION_CNTL_16GT_ADDRESS  0x10100434UL
#define SMN_DEV0_NBIF0RC_N4NBIO1_LANE_EQUALIZATION_CNTL_16GT_ADDRESS  0x10300434UL
#define SMN_DEV0_NBIF0RC_N5NBIO0_LANE_EQUALIZATION_CNTL_16GT_ADDRESS  0x10100435UL
#define SMN_DEV0_NBIF0RC_N5NBIO1_LANE_EQUALIZATION_CNTL_16GT_ADDRESS  0x10300435UL
#define SMN_DEV0_NBIF0RC_N6NBIO0_LANE_EQUALIZATION_CNTL_16GT_ADDRESS  0x10100436UL
#define SMN_DEV0_NBIF0RC_N6NBIO1_LANE_EQUALIZATION_CNTL_16GT_ADDRESS  0x10300436UL
#define SMN_DEV0_NBIF0RC_N7NBIO0_LANE_EQUALIZATION_CNTL_16GT_ADDRESS  0x10100437UL
#define SMN_DEV0_NBIF0RC_N7NBIO1_LANE_EQUALIZATION_CNTL_16GT_ADDRESS  0x10300437UL
#define SMN_DEV0_NBIF0RC_N8NBIO0_LANE_EQUALIZATION_CNTL_16GT_ADDRESS  0x10100438UL
#define SMN_DEV0_NBIF0RC_N8NBIO1_LANE_EQUALIZATION_CNTL_16GT_ADDRESS  0x10300438UL
#define SMN_DEV0_NBIF0RC_N9NBIO0_LANE_EQUALIZATION_CNTL_16GT_ADDRESS  0x10100439UL
#define SMN_DEV0_NBIF0RC_N9NBIO1_LANE_EQUALIZATION_CNTL_16GT_ADDRESS  0x10300439UL
#define SMN_DEV0_NBIF1RC_N0NBIO0_LANE_EQUALIZATION_CNTL_16GT_ADDRESS  0x10200430UL
#define SMN_DEV0_NBIF1RC_N0NBIO1_LANE_EQUALIZATION_CNTL_16GT_ADDRESS  0x10400430UL
#define SMN_DEV0_NBIF1RC_N10NBIO0_LANE_EQUALIZATION_CNTL_16GT_ADDRESS 0x1020043aUL
#define SMN_DEV0_NBIF1RC_N10NBIO1_LANE_EQUALIZATION_CNTL_16GT_ADDRESS 0x1040043aUL
#define SMN_DEV0_NBIF1RC_N11NBIO0_LANE_EQUALIZATION_CNTL_16GT_ADDRESS 0x1020043bUL
#define SMN_DEV0_NBIF1RC_N11NBIO1_LANE_EQUALIZATION_CNTL_16GT_ADDRESS 0x1040043bUL
#define SMN_DEV0_NBIF1RC_N12NBIO0_LANE_EQUALIZATION_CNTL_16GT_ADDRESS 0x1020043cUL
#define SMN_DEV0_NBIF1RC_N12NBIO1_LANE_EQUALIZATION_CNTL_16GT_ADDRESS 0x1040043cUL
#define SMN_DEV0_NBIF1RC_N13NBIO0_LANE_EQUALIZATION_CNTL_16GT_ADDRESS 0x1020043dUL
#define SMN_DEV0_NBIF1RC_N13NBIO1_LANE_EQUALIZATION_CNTL_16GT_ADDRESS 0x1040043dUL
#define SMN_DEV0_NBIF1RC_N14NBIO0_LANE_EQUALIZATION_CNTL_16GT_ADDRESS 0x1020043eUL
#define SMN_DEV0_NBIF1RC_N14NBIO1_LANE_EQUALIZATION_CNTL_16GT_ADDRESS 0x1040043eUL
#define SMN_DEV0_NBIF1RC_N15NBIO0_LANE_EQUALIZATION_CNTL_16GT_ADDRESS 0x1020043fUL
#define SMN_DEV0_NBIF1RC_N15NBIO1_LANE_EQUALIZATION_CNTL_16GT_ADDRESS 0x1040043fUL
#define SMN_DEV0_NBIF1RC_N1NBIO0_LANE_EQUALIZATION_CNTL_16GT_ADDRESS  0x10200431UL
#define SMN_DEV0_NBIF1RC_N1NBIO1_LANE_EQUALIZATION_CNTL_16GT_ADDRESS  0x10400431UL
#define SMN_DEV0_NBIF1RC_N2NBIO0_LANE_EQUALIZATION_CNTL_16GT_ADDRESS  0x10200432UL
#define SMN_DEV0_NBIF1RC_N2NBIO1_LANE_EQUALIZATION_CNTL_16GT_ADDRESS  0x10400432UL
#define SMN_DEV0_NBIF1RC_N3NBIO0_LANE_EQUALIZATION_CNTL_16GT_ADDRESS  0x10200433UL
#define SMN_DEV0_NBIF1RC_N3NBIO1_LANE_EQUALIZATION_CNTL_16GT_ADDRESS  0x10400433UL
#define SMN_DEV0_NBIF1RC_N4NBIO0_LANE_EQUALIZATION_CNTL_16GT_ADDRESS  0x10200434UL
#define SMN_DEV0_NBIF1RC_N4NBIO1_LANE_EQUALIZATION_CNTL_16GT_ADDRESS  0x10400434UL
#define SMN_DEV0_NBIF1RC_N5NBIO0_LANE_EQUALIZATION_CNTL_16GT_ADDRESS  0x10200435UL
#define SMN_DEV0_NBIF1RC_N5NBIO1_LANE_EQUALIZATION_CNTL_16GT_ADDRESS  0x10400435UL
#define SMN_DEV0_NBIF1RC_N6NBIO0_LANE_EQUALIZATION_CNTL_16GT_ADDRESS  0x10200436UL
#define SMN_DEV0_NBIF1RC_N6NBIO1_LANE_EQUALIZATION_CNTL_16GT_ADDRESS  0x10400436UL
#define SMN_DEV0_NBIF1RC_N7NBIO0_LANE_EQUALIZATION_CNTL_16GT_ADDRESS  0x10200437UL
#define SMN_DEV0_NBIF1RC_N7NBIO1_LANE_EQUALIZATION_CNTL_16GT_ADDRESS  0x10400437UL
#define SMN_DEV0_NBIF1RC_N8NBIO0_LANE_EQUALIZATION_CNTL_16GT_ADDRESS  0x10200438UL
#define SMN_DEV0_NBIF1RC_N8NBIO1_LANE_EQUALIZATION_CNTL_16GT_ADDRESS  0x10400438UL
#define SMN_DEV0_NBIF1RC_N9NBIO0_LANE_EQUALIZATION_CNTL_16GT_ADDRESS  0x10200439UL
#define SMN_DEV0_NBIF1RC_N9NBIO1_LANE_EQUALIZATION_CNTL_16GT_ADDRESS  0x10400439UL
#define SMN_DEV1_NBIF0RC_N0NBIO0_LANE_EQUALIZATION_CNTL_16GT_ADDRESS  0x10101430UL
#define SMN_DEV1_NBIF0RC_N0NBIO1_LANE_EQUALIZATION_CNTL_16GT_ADDRESS  0x10301430UL
#define SMN_DEV1_NBIF0RC_N10NBIO0_LANE_EQUALIZATION_CNTL_16GT_ADDRESS 0x1010143aUL
#define SMN_DEV1_NBIF0RC_N10NBIO1_LANE_EQUALIZATION_CNTL_16GT_ADDRESS 0x1030143aUL
#define SMN_DEV1_NBIF0RC_N11NBIO0_LANE_EQUALIZATION_CNTL_16GT_ADDRESS 0x1010143bUL
#define SMN_DEV1_NBIF0RC_N11NBIO1_LANE_EQUALIZATION_CNTL_16GT_ADDRESS 0x1030143bUL
#define SMN_DEV1_NBIF0RC_N12NBIO0_LANE_EQUALIZATION_CNTL_16GT_ADDRESS 0x1010143cUL
#define SMN_DEV1_NBIF0RC_N12NBIO1_LANE_EQUALIZATION_CNTL_16GT_ADDRESS 0x1030143cUL
#define SMN_DEV1_NBIF0RC_N13NBIO0_LANE_EQUALIZATION_CNTL_16GT_ADDRESS 0x1010143dUL
#define SMN_DEV1_NBIF0RC_N13NBIO1_LANE_EQUALIZATION_CNTL_16GT_ADDRESS 0x1030143dUL
#define SMN_DEV1_NBIF0RC_N14NBIO0_LANE_EQUALIZATION_CNTL_16GT_ADDRESS 0x1010143eUL
#define SMN_DEV1_NBIF0RC_N14NBIO1_LANE_EQUALIZATION_CNTL_16GT_ADDRESS 0x1030143eUL
#define SMN_DEV1_NBIF0RC_N15NBIO0_LANE_EQUALIZATION_CNTL_16GT_ADDRESS 0x1010143fUL
#define SMN_DEV1_NBIF0RC_N15NBIO1_LANE_EQUALIZATION_CNTL_16GT_ADDRESS 0x1030143fUL
#define SMN_DEV1_NBIF0RC_N1NBIO0_LANE_EQUALIZATION_CNTL_16GT_ADDRESS  0x10101431UL
#define SMN_DEV1_NBIF0RC_N1NBIO1_LANE_EQUALIZATION_CNTL_16GT_ADDRESS  0x10301431UL
#define SMN_DEV1_NBIF0RC_N2NBIO0_LANE_EQUALIZATION_CNTL_16GT_ADDRESS  0x10101432UL
#define SMN_DEV1_NBIF0RC_N2NBIO1_LANE_EQUALIZATION_CNTL_16GT_ADDRESS  0x10301432UL
#define SMN_DEV1_NBIF0RC_N3NBIO0_LANE_EQUALIZATION_CNTL_16GT_ADDRESS  0x10101433UL
#define SMN_DEV1_NBIF0RC_N3NBIO1_LANE_EQUALIZATION_CNTL_16GT_ADDRESS  0x10301433UL
#define SMN_DEV1_NBIF0RC_N4NBIO0_LANE_EQUALIZATION_CNTL_16GT_ADDRESS  0x10101434UL
#define SMN_DEV1_NBIF0RC_N4NBIO1_LANE_EQUALIZATION_CNTL_16GT_ADDRESS  0x10301434UL
#define SMN_DEV1_NBIF0RC_N5NBIO0_LANE_EQUALIZATION_CNTL_16GT_ADDRESS  0x10101435UL
#define SMN_DEV1_NBIF0RC_N5NBIO1_LANE_EQUALIZATION_CNTL_16GT_ADDRESS  0x10301435UL
#define SMN_DEV1_NBIF0RC_N6NBIO0_LANE_EQUALIZATION_CNTL_16GT_ADDRESS  0x10101436UL
#define SMN_DEV1_NBIF0RC_N6NBIO1_LANE_EQUALIZATION_CNTL_16GT_ADDRESS  0x10301436UL
#define SMN_DEV1_NBIF0RC_N7NBIO0_LANE_EQUALIZATION_CNTL_16GT_ADDRESS  0x10101437UL
#define SMN_DEV1_NBIF0RC_N7NBIO1_LANE_EQUALIZATION_CNTL_16GT_ADDRESS  0x10301437UL
#define SMN_DEV1_NBIF0RC_N8NBIO0_LANE_EQUALIZATION_CNTL_16GT_ADDRESS  0x10101438UL
#define SMN_DEV1_NBIF0RC_N8NBIO1_LANE_EQUALIZATION_CNTL_16GT_ADDRESS  0x10301438UL
#define SMN_DEV1_NBIF0RC_N9NBIO0_LANE_EQUALIZATION_CNTL_16GT_ADDRESS  0x10101439UL
#define SMN_DEV1_NBIF0RC_N9NBIO1_LANE_EQUALIZATION_CNTL_16GT_ADDRESS  0x10301439UL
#define SMN_DEV1_NBIF1RC_N0NBIO0_LANE_EQUALIZATION_CNTL_16GT_ADDRESS  0x10201430UL
#define SMN_DEV1_NBIF1RC_N0NBIO1_LANE_EQUALIZATION_CNTL_16GT_ADDRESS  0x10401430UL
#define SMN_DEV1_NBIF1RC_N10NBIO0_LANE_EQUALIZATION_CNTL_16GT_ADDRESS 0x1020143aUL
#define SMN_DEV1_NBIF1RC_N10NBIO1_LANE_EQUALIZATION_CNTL_16GT_ADDRESS 0x1040143aUL
#define SMN_DEV1_NBIF1RC_N11NBIO0_LANE_EQUALIZATION_CNTL_16GT_ADDRESS 0x1020143bUL
#define SMN_DEV1_NBIF1RC_N11NBIO1_LANE_EQUALIZATION_CNTL_16GT_ADDRESS 0x1040143bUL
#define SMN_DEV1_NBIF1RC_N12NBIO0_LANE_EQUALIZATION_CNTL_16GT_ADDRESS 0x1020143cUL
#define SMN_DEV1_NBIF1RC_N12NBIO1_LANE_EQUALIZATION_CNTL_16GT_ADDRESS 0x1040143cUL
#define SMN_DEV1_NBIF1RC_N13NBIO0_LANE_EQUALIZATION_CNTL_16GT_ADDRESS 0x1020143dUL
#define SMN_DEV1_NBIF1RC_N13NBIO1_LANE_EQUALIZATION_CNTL_16GT_ADDRESS 0x1040143dUL
#define SMN_DEV1_NBIF1RC_N14NBIO0_LANE_EQUALIZATION_CNTL_16GT_ADDRESS 0x1020143eUL
#define SMN_DEV1_NBIF1RC_N14NBIO1_LANE_EQUALIZATION_CNTL_16GT_ADDRESS 0x1040143eUL
#define SMN_DEV1_NBIF1RC_N15NBIO0_LANE_EQUALIZATION_CNTL_16GT_ADDRESS 0x1020143fUL
#define SMN_DEV1_NBIF1RC_N15NBIO1_LANE_EQUALIZATION_CNTL_16GT_ADDRESS 0x1040143fUL
#define SMN_DEV1_NBIF1RC_N1NBIO0_LANE_EQUALIZATION_CNTL_16GT_ADDRESS  0x10201431UL
#define SMN_DEV1_NBIF1RC_N1NBIO1_LANE_EQUALIZATION_CNTL_16GT_ADDRESS  0x10401431UL
#define SMN_DEV1_NBIF1RC_N2NBIO0_LANE_EQUALIZATION_CNTL_16GT_ADDRESS  0x10201432UL
#define SMN_DEV1_NBIF1RC_N2NBIO1_LANE_EQUALIZATION_CNTL_16GT_ADDRESS  0x10401432UL
#define SMN_DEV1_NBIF1RC_N3NBIO0_LANE_EQUALIZATION_CNTL_16GT_ADDRESS  0x10201433UL
#define SMN_DEV1_NBIF1RC_N3NBIO1_LANE_EQUALIZATION_CNTL_16GT_ADDRESS  0x10401433UL
#define SMN_DEV1_NBIF1RC_N4NBIO0_LANE_EQUALIZATION_CNTL_16GT_ADDRESS  0x10201434UL
#define SMN_DEV1_NBIF1RC_N4NBIO1_LANE_EQUALIZATION_CNTL_16GT_ADDRESS  0x10401434UL
#define SMN_DEV1_NBIF1RC_N5NBIO0_LANE_EQUALIZATION_CNTL_16GT_ADDRESS  0x10201435UL
#define SMN_DEV1_NBIF1RC_N5NBIO1_LANE_EQUALIZATION_CNTL_16GT_ADDRESS  0x10401435UL
#define SMN_DEV1_NBIF1RC_N6NBIO0_LANE_EQUALIZATION_CNTL_16GT_ADDRESS  0x10201436UL
#define SMN_DEV1_NBIF1RC_N6NBIO1_LANE_EQUALIZATION_CNTL_16GT_ADDRESS  0x10401436UL
#define SMN_DEV1_NBIF1RC_N7NBIO0_LANE_EQUALIZATION_CNTL_16GT_ADDRESS  0x10201437UL
#define SMN_DEV1_NBIF1RC_N7NBIO1_LANE_EQUALIZATION_CNTL_16GT_ADDRESS  0x10401437UL
#define SMN_DEV1_NBIF1RC_N8NBIO0_LANE_EQUALIZATION_CNTL_16GT_ADDRESS  0x10201438UL
#define SMN_DEV1_NBIF1RC_N8NBIO1_LANE_EQUALIZATION_CNTL_16GT_ADDRESS  0x10401438UL
#define SMN_DEV1_NBIF1RC_N9NBIO0_LANE_EQUALIZATION_CNTL_16GT_ADDRESS  0x10201439UL
#define SMN_DEV1_NBIF1RC_N9NBIO1_LANE_EQUALIZATION_CNTL_16GT_ADDRESS  0x10401439UL


/***********************************************************
* Register Name : LANE_EQUALIZATION_CNTL_32GT
************************************************************/

#define LANE_EQUALIZATION_CNTL_32GT_LANE_DSP_32GT_TX_PRESET_OFFSET 0
#define LANE_EQUALIZATION_CNTL_32GT_LANE_DSP_32GT_TX_PRESET_MASK 0xf

#define LANE_EQUALIZATION_CNTL_32GT_LANE_USP_32GT_TX_PRESET_OFFSET 4
#define LANE_EQUALIZATION_CNTL_32GT_LANE_USP_32GT_TX_PRESET_MASK 0xf0

typedef union {
  struct {
    UINT8                             LANE_DSP_32GT_TX_PRESET:4;
    UINT8                             LANE_USP_32GT_TX_PRESET:4;
  } Field;
  UINT8 Value;
} LANE_EQUALIZATION_CNTL_32GT_NBIFRCCFG_STRUCT;

#define PCICFG_NBIFRCCFG_LANE_EQUALIZATION_CNTL_32GT_OFFSET         0x520
#define SMN_DEV0_NBIF0RC_N0NBIO0_LANE_EQUALIZATION_CNTL_32GT_ADDRESS  0x10100520UL
#define SMN_DEV0_NBIF0RC_N0NBIO1_LANE_EQUALIZATION_CNTL_32GT_ADDRESS  0x10300520UL
#define SMN_DEV0_NBIF0RC_N10NBIO0_LANE_EQUALIZATION_CNTL_32GT_ADDRESS 0x1010052aUL
#define SMN_DEV0_NBIF0RC_N10NBIO1_LANE_EQUALIZATION_CNTL_32GT_ADDRESS 0x1030052aUL
#define SMN_DEV0_NBIF0RC_N11NBIO0_LANE_EQUALIZATION_CNTL_32GT_ADDRESS 0x1010052bUL
#define SMN_DEV0_NBIF0RC_N11NBIO1_LANE_EQUALIZATION_CNTL_32GT_ADDRESS 0x1030052bUL
#define SMN_DEV0_NBIF0RC_N12NBIO0_LANE_EQUALIZATION_CNTL_32GT_ADDRESS 0x1010052cUL
#define SMN_DEV0_NBIF0RC_N12NBIO1_LANE_EQUALIZATION_CNTL_32GT_ADDRESS 0x1030052cUL
#define SMN_DEV0_NBIF0RC_N13NBIO0_LANE_EQUALIZATION_CNTL_32GT_ADDRESS 0x1010052dUL
#define SMN_DEV0_NBIF0RC_N13NBIO1_LANE_EQUALIZATION_CNTL_32GT_ADDRESS 0x1030052dUL
#define SMN_DEV0_NBIF0RC_N14NBIO0_LANE_EQUALIZATION_CNTL_32GT_ADDRESS 0x1010052eUL
#define SMN_DEV0_NBIF0RC_N14NBIO1_LANE_EQUALIZATION_CNTL_32GT_ADDRESS 0x1030052eUL
#define SMN_DEV0_NBIF0RC_N15NBIO0_LANE_EQUALIZATION_CNTL_32GT_ADDRESS 0x1010052fUL
#define SMN_DEV0_NBIF0RC_N15NBIO1_LANE_EQUALIZATION_CNTL_32GT_ADDRESS 0x1030052fUL
#define SMN_DEV0_NBIF0RC_N1NBIO0_LANE_EQUALIZATION_CNTL_32GT_ADDRESS  0x10100521UL
#define SMN_DEV0_NBIF0RC_N1NBIO1_LANE_EQUALIZATION_CNTL_32GT_ADDRESS  0x10300521UL
#define SMN_DEV0_NBIF0RC_N2NBIO0_LANE_EQUALIZATION_CNTL_32GT_ADDRESS  0x10100522UL
#define SMN_DEV0_NBIF0RC_N2NBIO1_LANE_EQUALIZATION_CNTL_32GT_ADDRESS  0x10300522UL
#define SMN_DEV0_NBIF0RC_N3NBIO0_LANE_EQUALIZATION_CNTL_32GT_ADDRESS  0x10100523UL
#define SMN_DEV0_NBIF0RC_N3NBIO1_LANE_EQUALIZATION_CNTL_32GT_ADDRESS  0x10300523UL
#define SMN_DEV0_NBIF0RC_N4NBIO0_LANE_EQUALIZATION_CNTL_32GT_ADDRESS  0x10100524UL
#define SMN_DEV0_NBIF0RC_N4NBIO1_LANE_EQUALIZATION_CNTL_32GT_ADDRESS  0x10300524UL
#define SMN_DEV0_NBIF0RC_N5NBIO0_LANE_EQUALIZATION_CNTL_32GT_ADDRESS  0x10100525UL
#define SMN_DEV0_NBIF0RC_N5NBIO1_LANE_EQUALIZATION_CNTL_32GT_ADDRESS  0x10300525UL
#define SMN_DEV0_NBIF0RC_N6NBIO0_LANE_EQUALIZATION_CNTL_32GT_ADDRESS  0x10100526UL
#define SMN_DEV0_NBIF0RC_N6NBIO1_LANE_EQUALIZATION_CNTL_32GT_ADDRESS  0x10300526UL
#define SMN_DEV0_NBIF0RC_N7NBIO0_LANE_EQUALIZATION_CNTL_32GT_ADDRESS  0x10100527UL
#define SMN_DEV0_NBIF0RC_N7NBIO1_LANE_EQUALIZATION_CNTL_32GT_ADDRESS  0x10300527UL
#define SMN_DEV0_NBIF0RC_N8NBIO0_LANE_EQUALIZATION_CNTL_32GT_ADDRESS  0x10100528UL
#define SMN_DEV0_NBIF0RC_N8NBIO1_LANE_EQUALIZATION_CNTL_32GT_ADDRESS  0x10300528UL
#define SMN_DEV0_NBIF0RC_N9NBIO0_LANE_EQUALIZATION_CNTL_32GT_ADDRESS  0x10100529UL
#define SMN_DEV0_NBIF0RC_N9NBIO1_LANE_EQUALIZATION_CNTL_32GT_ADDRESS  0x10300529UL
#define SMN_DEV0_NBIF1RC_N0NBIO0_LANE_EQUALIZATION_CNTL_32GT_ADDRESS  0x10200520UL
#define SMN_DEV0_NBIF1RC_N0NBIO1_LANE_EQUALIZATION_CNTL_32GT_ADDRESS  0x10400520UL
#define SMN_DEV0_NBIF1RC_N10NBIO0_LANE_EQUALIZATION_CNTL_32GT_ADDRESS 0x1020052aUL
#define SMN_DEV0_NBIF1RC_N10NBIO1_LANE_EQUALIZATION_CNTL_32GT_ADDRESS 0x1040052aUL
#define SMN_DEV0_NBIF1RC_N11NBIO0_LANE_EQUALIZATION_CNTL_32GT_ADDRESS 0x1020052bUL
#define SMN_DEV0_NBIF1RC_N11NBIO1_LANE_EQUALIZATION_CNTL_32GT_ADDRESS 0x1040052bUL
#define SMN_DEV0_NBIF1RC_N12NBIO0_LANE_EQUALIZATION_CNTL_32GT_ADDRESS 0x1020052cUL
#define SMN_DEV0_NBIF1RC_N12NBIO1_LANE_EQUALIZATION_CNTL_32GT_ADDRESS 0x1040052cUL
#define SMN_DEV0_NBIF1RC_N13NBIO0_LANE_EQUALIZATION_CNTL_32GT_ADDRESS 0x1020052dUL
#define SMN_DEV0_NBIF1RC_N13NBIO1_LANE_EQUALIZATION_CNTL_32GT_ADDRESS 0x1040052dUL
#define SMN_DEV0_NBIF1RC_N14NBIO0_LANE_EQUALIZATION_CNTL_32GT_ADDRESS 0x1020052eUL
#define SMN_DEV0_NBIF1RC_N14NBIO1_LANE_EQUALIZATION_CNTL_32GT_ADDRESS 0x1040052eUL
#define SMN_DEV0_NBIF1RC_N15NBIO0_LANE_EQUALIZATION_CNTL_32GT_ADDRESS 0x1020052fUL
#define SMN_DEV0_NBIF1RC_N15NBIO1_LANE_EQUALIZATION_CNTL_32GT_ADDRESS 0x1040052fUL
#define SMN_DEV0_NBIF1RC_N1NBIO0_LANE_EQUALIZATION_CNTL_32GT_ADDRESS  0x10200521UL
#define SMN_DEV0_NBIF1RC_N1NBIO1_LANE_EQUALIZATION_CNTL_32GT_ADDRESS  0x10400521UL
#define SMN_DEV0_NBIF1RC_N2NBIO0_LANE_EQUALIZATION_CNTL_32GT_ADDRESS  0x10200522UL
#define SMN_DEV0_NBIF1RC_N2NBIO1_LANE_EQUALIZATION_CNTL_32GT_ADDRESS  0x10400522UL
#define SMN_DEV0_NBIF1RC_N3NBIO0_LANE_EQUALIZATION_CNTL_32GT_ADDRESS  0x10200523UL
#define SMN_DEV0_NBIF1RC_N3NBIO1_LANE_EQUALIZATION_CNTL_32GT_ADDRESS  0x10400523UL
#define SMN_DEV0_NBIF1RC_N4NBIO0_LANE_EQUALIZATION_CNTL_32GT_ADDRESS  0x10200524UL
#define SMN_DEV0_NBIF1RC_N4NBIO1_LANE_EQUALIZATION_CNTL_32GT_ADDRESS  0x10400524UL
#define SMN_DEV0_NBIF1RC_N5NBIO0_LANE_EQUALIZATION_CNTL_32GT_ADDRESS  0x10200525UL
#define SMN_DEV0_NBIF1RC_N5NBIO1_LANE_EQUALIZATION_CNTL_32GT_ADDRESS  0x10400525UL
#define SMN_DEV0_NBIF1RC_N6NBIO0_LANE_EQUALIZATION_CNTL_32GT_ADDRESS  0x10200526UL
#define SMN_DEV0_NBIF1RC_N6NBIO1_LANE_EQUALIZATION_CNTL_32GT_ADDRESS  0x10400526UL
#define SMN_DEV0_NBIF1RC_N7NBIO0_LANE_EQUALIZATION_CNTL_32GT_ADDRESS  0x10200527UL
#define SMN_DEV0_NBIF1RC_N7NBIO1_LANE_EQUALIZATION_CNTL_32GT_ADDRESS  0x10400527UL
#define SMN_DEV0_NBIF1RC_N8NBIO0_LANE_EQUALIZATION_CNTL_32GT_ADDRESS  0x10200528UL
#define SMN_DEV0_NBIF1RC_N8NBIO1_LANE_EQUALIZATION_CNTL_32GT_ADDRESS  0x10400528UL
#define SMN_DEV0_NBIF1RC_N9NBIO0_LANE_EQUALIZATION_CNTL_32GT_ADDRESS  0x10200529UL
#define SMN_DEV0_NBIF1RC_N9NBIO1_LANE_EQUALIZATION_CNTL_32GT_ADDRESS  0x10400529UL
#define SMN_DEV1_NBIF0RC_N0NBIO0_LANE_EQUALIZATION_CNTL_32GT_ADDRESS  0x10101520UL
#define SMN_DEV1_NBIF0RC_N0NBIO1_LANE_EQUALIZATION_CNTL_32GT_ADDRESS  0x10301520UL
#define SMN_DEV1_NBIF0RC_N10NBIO0_LANE_EQUALIZATION_CNTL_32GT_ADDRESS 0x1010152aUL
#define SMN_DEV1_NBIF0RC_N10NBIO1_LANE_EQUALIZATION_CNTL_32GT_ADDRESS 0x1030152aUL
#define SMN_DEV1_NBIF0RC_N11NBIO0_LANE_EQUALIZATION_CNTL_32GT_ADDRESS 0x1010152bUL
#define SMN_DEV1_NBIF0RC_N11NBIO1_LANE_EQUALIZATION_CNTL_32GT_ADDRESS 0x1030152bUL
#define SMN_DEV1_NBIF0RC_N12NBIO0_LANE_EQUALIZATION_CNTL_32GT_ADDRESS 0x1010152cUL
#define SMN_DEV1_NBIF0RC_N12NBIO1_LANE_EQUALIZATION_CNTL_32GT_ADDRESS 0x1030152cUL
#define SMN_DEV1_NBIF0RC_N13NBIO0_LANE_EQUALIZATION_CNTL_32GT_ADDRESS 0x1010152dUL
#define SMN_DEV1_NBIF0RC_N13NBIO1_LANE_EQUALIZATION_CNTL_32GT_ADDRESS 0x1030152dUL
#define SMN_DEV1_NBIF0RC_N14NBIO0_LANE_EQUALIZATION_CNTL_32GT_ADDRESS 0x1010152eUL
#define SMN_DEV1_NBIF0RC_N14NBIO1_LANE_EQUALIZATION_CNTL_32GT_ADDRESS 0x1030152eUL
#define SMN_DEV1_NBIF0RC_N15NBIO0_LANE_EQUALIZATION_CNTL_32GT_ADDRESS 0x1010152fUL
#define SMN_DEV1_NBIF0RC_N15NBIO1_LANE_EQUALIZATION_CNTL_32GT_ADDRESS 0x1030152fUL
#define SMN_DEV1_NBIF0RC_N1NBIO0_LANE_EQUALIZATION_CNTL_32GT_ADDRESS  0x10101521UL
#define SMN_DEV1_NBIF0RC_N1NBIO1_LANE_EQUALIZATION_CNTL_32GT_ADDRESS  0x10301521UL
#define SMN_DEV1_NBIF0RC_N2NBIO0_LANE_EQUALIZATION_CNTL_32GT_ADDRESS  0x10101522UL
#define SMN_DEV1_NBIF0RC_N2NBIO1_LANE_EQUALIZATION_CNTL_32GT_ADDRESS  0x10301522UL
#define SMN_DEV1_NBIF0RC_N3NBIO0_LANE_EQUALIZATION_CNTL_32GT_ADDRESS  0x10101523UL
#define SMN_DEV1_NBIF0RC_N3NBIO1_LANE_EQUALIZATION_CNTL_32GT_ADDRESS  0x10301523UL
#define SMN_DEV1_NBIF0RC_N4NBIO0_LANE_EQUALIZATION_CNTL_32GT_ADDRESS  0x10101524UL
#define SMN_DEV1_NBIF0RC_N4NBIO1_LANE_EQUALIZATION_CNTL_32GT_ADDRESS  0x10301524UL
#define SMN_DEV1_NBIF0RC_N5NBIO0_LANE_EQUALIZATION_CNTL_32GT_ADDRESS  0x10101525UL
#define SMN_DEV1_NBIF0RC_N5NBIO1_LANE_EQUALIZATION_CNTL_32GT_ADDRESS  0x10301525UL
#define SMN_DEV1_NBIF0RC_N6NBIO0_LANE_EQUALIZATION_CNTL_32GT_ADDRESS  0x10101526UL
#define SMN_DEV1_NBIF0RC_N6NBIO1_LANE_EQUALIZATION_CNTL_32GT_ADDRESS  0x10301526UL
#define SMN_DEV1_NBIF0RC_N7NBIO0_LANE_EQUALIZATION_CNTL_32GT_ADDRESS  0x10101527UL
#define SMN_DEV1_NBIF0RC_N7NBIO1_LANE_EQUALIZATION_CNTL_32GT_ADDRESS  0x10301527UL
#define SMN_DEV1_NBIF0RC_N8NBIO0_LANE_EQUALIZATION_CNTL_32GT_ADDRESS  0x10101528UL
#define SMN_DEV1_NBIF0RC_N8NBIO1_LANE_EQUALIZATION_CNTL_32GT_ADDRESS  0x10301528UL
#define SMN_DEV1_NBIF0RC_N9NBIO0_LANE_EQUALIZATION_CNTL_32GT_ADDRESS  0x10101529UL
#define SMN_DEV1_NBIF0RC_N9NBIO1_LANE_EQUALIZATION_CNTL_32GT_ADDRESS  0x10301529UL
#define SMN_DEV1_NBIF1RC_N0NBIO0_LANE_EQUALIZATION_CNTL_32GT_ADDRESS  0x10201520UL
#define SMN_DEV1_NBIF1RC_N0NBIO1_LANE_EQUALIZATION_CNTL_32GT_ADDRESS  0x10401520UL
#define SMN_DEV1_NBIF1RC_N10NBIO0_LANE_EQUALIZATION_CNTL_32GT_ADDRESS 0x1020152aUL
#define SMN_DEV1_NBIF1RC_N10NBIO1_LANE_EQUALIZATION_CNTL_32GT_ADDRESS 0x1040152aUL
#define SMN_DEV1_NBIF1RC_N11NBIO0_LANE_EQUALIZATION_CNTL_32GT_ADDRESS 0x1020152bUL
#define SMN_DEV1_NBIF1RC_N11NBIO1_LANE_EQUALIZATION_CNTL_32GT_ADDRESS 0x1040152bUL
#define SMN_DEV1_NBIF1RC_N12NBIO0_LANE_EQUALIZATION_CNTL_32GT_ADDRESS 0x1020152cUL
#define SMN_DEV1_NBIF1RC_N12NBIO1_LANE_EQUALIZATION_CNTL_32GT_ADDRESS 0x1040152cUL
#define SMN_DEV1_NBIF1RC_N13NBIO0_LANE_EQUALIZATION_CNTL_32GT_ADDRESS 0x1020152dUL
#define SMN_DEV1_NBIF1RC_N13NBIO1_LANE_EQUALIZATION_CNTL_32GT_ADDRESS 0x1040152dUL
#define SMN_DEV1_NBIF1RC_N14NBIO0_LANE_EQUALIZATION_CNTL_32GT_ADDRESS 0x1020152eUL
#define SMN_DEV1_NBIF1RC_N14NBIO1_LANE_EQUALIZATION_CNTL_32GT_ADDRESS 0x1040152eUL
#define SMN_DEV1_NBIF1RC_N15NBIO0_LANE_EQUALIZATION_CNTL_32GT_ADDRESS 0x1020152fUL
#define SMN_DEV1_NBIF1RC_N15NBIO1_LANE_EQUALIZATION_CNTL_32GT_ADDRESS 0x1040152fUL
#define SMN_DEV1_NBIF1RC_N1NBIO0_LANE_EQUALIZATION_CNTL_32GT_ADDRESS  0x10201521UL
#define SMN_DEV1_NBIF1RC_N1NBIO1_LANE_EQUALIZATION_CNTL_32GT_ADDRESS  0x10401521UL
#define SMN_DEV1_NBIF1RC_N2NBIO0_LANE_EQUALIZATION_CNTL_32GT_ADDRESS  0x10201522UL
#define SMN_DEV1_NBIF1RC_N2NBIO1_LANE_EQUALIZATION_CNTL_32GT_ADDRESS  0x10401522UL
#define SMN_DEV1_NBIF1RC_N3NBIO0_LANE_EQUALIZATION_CNTL_32GT_ADDRESS  0x10201523UL
#define SMN_DEV1_NBIF1RC_N3NBIO1_LANE_EQUALIZATION_CNTL_32GT_ADDRESS  0x10401523UL
#define SMN_DEV1_NBIF1RC_N4NBIO0_LANE_EQUALIZATION_CNTL_32GT_ADDRESS  0x10201524UL
#define SMN_DEV1_NBIF1RC_N4NBIO1_LANE_EQUALIZATION_CNTL_32GT_ADDRESS  0x10401524UL
#define SMN_DEV1_NBIF1RC_N5NBIO0_LANE_EQUALIZATION_CNTL_32GT_ADDRESS  0x10201525UL
#define SMN_DEV1_NBIF1RC_N5NBIO1_LANE_EQUALIZATION_CNTL_32GT_ADDRESS  0x10401525UL
#define SMN_DEV1_NBIF1RC_N6NBIO0_LANE_EQUALIZATION_CNTL_32GT_ADDRESS  0x10201526UL
#define SMN_DEV1_NBIF1RC_N6NBIO1_LANE_EQUALIZATION_CNTL_32GT_ADDRESS  0x10401526UL
#define SMN_DEV1_NBIF1RC_N7NBIO0_LANE_EQUALIZATION_CNTL_32GT_ADDRESS  0x10201527UL
#define SMN_DEV1_NBIF1RC_N7NBIO1_LANE_EQUALIZATION_CNTL_32GT_ADDRESS  0x10401527UL
#define SMN_DEV1_NBIF1RC_N8NBIO0_LANE_EQUALIZATION_CNTL_32GT_ADDRESS  0x10201528UL
#define SMN_DEV1_NBIF1RC_N8NBIO1_LANE_EQUALIZATION_CNTL_32GT_ADDRESS  0x10401528UL
#define SMN_DEV1_NBIF1RC_N9NBIO0_LANE_EQUALIZATION_CNTL_32GT_ADDRESS  0x10201529UL
#define SMN_DEV1_NBIF1RC_N9NBIO1_LANE_EQUALIZATION_CNTL_32GT_ADDRESS  0x10401529UL


/***********************************************************
* Register Name : LATENCY
************************************************************/

#define LATENCY_LATENCY_TIMER_OFFSET                           0
#define LATENCY_LATENCY_TIMER_MASK                             0xff

typedef union {
  struct {
    UINT8                                       LATENCY_TIMER:8;
  } Field;
  UINT8 Value;
} LATENCY_NBIFRCCFG_STRUCT;

#define PCICFG_NBIFRCCFG_LATENCY_OFFSET                             0xd
#define SMN_DEV0_NBIF0RCNBIO0_LATENCY_ADDRESS                         0x1010000dUL
#define SMN_DEV0_NBIF0RCNBIO1_LATENCY_ADDRESS                         0x1030000dUL
#define SMN_DEV0_NBIF1RCNBIO0_LATENCY_ADDRESS                         0x1020000dUL
#define SMN_DEV0_NBIF1RCNBIO1_LATENCY_ADDRESS                         0x1040000dUL
#define SMN_DEV1_NBIF0RCNBIO0_LATENCY_ADDRESS                         0x1010100dUL
#define SMN_DEV1_NBIF0RCNBIO1_LATENCY_ADDRESS                         0x1030100dUL
#define SMN_DEV1_NBIF1RCNBIO0_LATENCY_ADDRESS                         0x1020100dUL
#define SMN_DEV1_NBIF1RCNBIO1_LATENCY_ADDRESS                         0x1040100dUL


/***********************************************************
* Register Name : LINK_CAP
************************************************************/

#define LINK_CAP_LINK_SPEED_OFFSET                             0
#define LINK_CAP_LINK_SPEED_MASK                               0xf

#define LINK_CAP_LINK_WIDTH_OFFSET                             4
#define LINK_CAP_LINK_WIDTH_MASK                               0x3f0

#define LINK_CAP_PM_SUPPORT_OFFSET                             10
#define LINK_CAP_PM_SUPPORT_MASK                               0xc00

#define LINK_CAP_L0S_EXIT_LATENCY_OFFSET                       12
#define LINK_CAP_L0S_EXIT_LATENCY_MASK                         0x7000

#define LINK_CAP_L1_EXIT_LATENCY_OFFSET                        15
#define LINK_CAP_L1_EXIT_LATENCY_MASK                          0x38000

#define LINK_CAP_CLOCK_POWER_MANAGEMENT_OFFSET                 18
#define LINK_CAP_CLOCK_POWER_MANAGEMENT_MASK                   0x40000

#define LINK_CAP_SURPRISE_DOWN_ERR_REPORTING_OFFSET            19
#define LINK_CAP_SURPRISE_DOWN_ERR_REPORTING_MASK              0x80000

#define LINK_CAP_DL_ACTIVE_REPORTING_CAPABLE_OFFSET            20
#define LINK_CAP_DL_ACTIVE_REPORTING_CAPABLE_MASK              0x100000

#define LINK_CAP_LINK_BW_NOTIFICATION_CAP_OFFSET               21
#define LINK_CAP_LINK_BW_NOTIFICATION_CAP_MASK                 0x200000

#define LINK_CAP_ASPM_OPTIONALITY_COMPLIANCE_OFFSET            22
#define LINK_CAP_ASPM_OPTIONALITY_COMPLIANCE_MASK              0x400000

#define LINK_CAP_Reserved_23_23_OFFSET                         23
#define LINK_CAP_Reserved_23_23_MASK                           0x800000

#define LINK_CAP_PORT_NUMBER_OFFSET                            24
#define LINK_CAP_PORT_NUMBER_MASK                              0xff000000

typedef union {
  struct {
    UINT32                                          LINK_SPEED:4;
    UINT32                                          LINK_WIDTH:6;
    UINT32                                          PM_SUPPORT:2;
    UINT32                                    L0S_EXIT_LATENCY:3;
    UINT32                                     L1_EXIT_LATENCY:3;
    UINT32                              CLOCK_POWER_MANAGEMENT:1;
    UINT32                         SURPRISE_DOWN_ERR_REPORTING:1;
    UINT32                         DL_ACTIVE_REPORTING_CAPABLE:1;
    UINT32                            LINK_BW_NOTIFICATION_CAP:1;
    UINT32                         ASPM_OPTIONALITY_COMPLIANCE:1;
    UINT32                                      Reserved_23_23:1;
    UINT32                                         PORT_NUMBER:8;
  } Field;
  UINT32 Value;
} LINK_CAP_NBIFRCCFG_STRUCT;

#define PCICFG_NBIFRCCFG_LINK_CAP_OFFSET                            0x64
#define SMN_DEV0_NBIF0RCNBIO0_LINK_CAP_ADDRESS                        0x10100064UL
#define SMN_DEV0_NBIF0RCNBIO1_LINK_CAP_ADDRESS                        0x10300064UL
#define SMN_DEV0_NBIF1RCNBIO0_LINK_CAP_ADDRESS                        0x10200064UL
#define SMN_DEV0_NBIF1RCNBIO1_LINK_CAP_ADDRESS                        0x10400064UL
#define SMN_DEV1_NBIF0RCNBIO0_LINK_CAP_ADDRESS                        0x10101064UL
#define SMN_DEV1_NBIF0RCNBIO1_LINK_CAP_ADDRESS                        0x10301064UL
#define SMN_DEV1_NBIF1RCNBIO0_LINK_CAP_ADDRESS                        0x10201064UL
#define SMN_DEV1_NBIF1RCNBIO1_LINK_CAP_ADDRESS                        0x10401064UL


/***********************************************************
* Register Name : LINK_CAP2
************************************************************/

#define LINK_CAP2_Reserved_0_0_OFFSET                          0
#define LINK_CAP2_Reserved_0_0_MASK                            0x1

#define LINK_CAP2_SUPPORTED_LINK_SPEED_OFFSET                  1
#define LINK_CAP2_SUPPORTED_LINK_SPEED_MASK                    0xfe

#define LINK_CAP2_CROSSLINK_SUPPORTED_OFFSET                   8
#define LINK_CAP2_CROSSLINK_SUPPORTED_MASK                     0x100

#define LINK_CAP2_LOWER_SKP_OS_GEN_SUPPORT_OFFSET              9
#define LINK_CAP2_LOWER_SKP_OS_GEN_SUPPORT_MASK                0xfe00

#define LINK_CAP2_LOWER_SKP_OS_RCV_SUPPORT_OFFSET              16
#define LINK_CAP2_LOWER_SKP_OS_RCV_SUPPORT_MASK                0x7f0000

#define LINK_CAP2_RTM1_PRESENCE_DET_SUPPORT_OFFSET             23
#define LINK_CAP2_RTM1_PRESENCE_DET_SUPPORT_MASK               0x800000

#define LINK_CAP2_RTM2_PRESENCE_DET_SUPPORT_OFFSET             24
#define LINK_CAP2_RTM2_PRESENCE_DET_SUPPORT_MASK               0x1000000

#define LINK_CAP2_Reserved_30_25_OFFSET                        25
#define LINK_CAP2_Reserved_30_25_MASK                          0x7e000000

#define LINK_CAP2_DRS_SUPPORTED_OFFSET                         31
#define LINK_CAP2_DRS_SUPPORTED_MASK                           0x80000000

typedef union {
  struct {
    UINT32                                        Reserved_0_0:1;
    UINT32                                SUPPORTED_LINK_SPEED:7;
    UINT32                                 CROSSLINK_SUPPORTED:1;
    UINT32                            LOWER_SKP_OS_GEN_SUPPORT:7;
    UINT32                            LOWER_SKP_OS_RCV_SUPPORT:7;
    UINT32                           RTM1_PRESENCE_DET_SUPPORT:1;
    UINT32                           RTM2_PRESENCE_DET_SUPPORT:1;
    UINT32                                      Reserved_30_25:6;
    UINT32                                       DRS_SUPPORTED:1;
  } Field;
  UINT32 Value;
} LINK_CAP2_NBIFRCCFG_STRUCT;

#define PCICFG_NBIFRCCFG_LINK_CAP2_OFFSET                           0x84
#define SMN_DEV0_NBIF0RCNBIO0_LINK_CAP2_ADDRESS                       0x10100084UL
#define SMN_DEV0_NBIF0RCNBIO1_LINK_CAP2_ADDRESS                       0x10300084UL
#define SMN_DEV0_NBIF1RCNBIO0_LINK_CAP2_ADDRESS                       0x10200084UL
#define SMN_DEV0_NBIF1RCNBIO1_LINK_CAP2_ADDRESS                       0x10400084UL
#define SMN_DEV1_NBIF0RCNBIO0_LINK_CAP2_ADDRESS                       0x10101084UL
#define SMN_DEV1_NBIF0RCNBIO1_LINK_CAP2_ADDRESS                       0x10301084UL
#define SMN_DEV1_NBIF1RCNBIO0_LINK_CAP2_ADDRESS                       0x10201084UL
#define SMN_DEV1_NBIF1RCNBIO1_LINK_CAP2_ADDRESS                       0x10401084UL


/***********************************************************
* Register Name : LINK_CAP_16GT
************************************************************/

#define LINK_CAP_16GT_Reserved_31_0_OFFSET                     0
#define LINK_CAP_16GT_Reserved_31_0_MASK                       0xffffffff

typedef union {
  struct {
    UINT32                                       Reserved_31_0:32;
  } Field;
  UINT32 Value;
} LINK_CAP_16GT_NBIFRCCFG_STRUCT;

#define PCICFG_NBIFRCCFG_LINK_CAP_16GT_OFFSET                       0x414
#define SMN_DEV0_NBIF0RCNBIO0_LINK_CAP_16GT_ADDRESS                   0x10100414UL
#define SMN_DEV0_NBIF0RCNBIO1_LINK_CAP_16GT_ADDRESS                   0x10300414UL
#define SMN_DEV0_NBIF1RCNBIO0_LINK_CAP_16GT_ADDRESS                   0x10200414UL
#define SMN_DEV0_NBIF1RCNBIO1_LINK_CAP_16GT_ADDRESS                   0x10400414UL
#define SMN_DEV1_NBIF0RCNBIO0_LINK_CAP_16GT_ADDRESS                   0x10101414UL
#define SMN_DEV1_NBIF0RCNBIO1_LINK_CAP_16GT_ADDRESS                   0x10301414UL
#define SMN_DEV1_NBIF1RCNBIO0_LINK_CAP_16GT_ADDRESS                   0x10201414UL
#define SMN_DEV1_NBIF1RCNBIO1_LINK_CAP_16GT_ADDRESS                   0x10401414UL


/***********************************************************
* Register Name : LINK_CAP_32GT
************************************************************/

#define LINK_CAP_32GT_EQ_BYPASS_TO_HIGHEST_RATE_SUPPORTED_OFFSET 0
#define LINK_CAP_32GT_EQ_BYPASS_TO_HIGHEST_RATE_SUPPORTED_MASK 0x1

#define LINK_CAP_32GT_NO_EQ_NEEDED_SUPPORTED_OFFSET            1
#define LINK_CAP_32GT_NO_EQ_NEEDED_SUPPORTED_MASK              0x2

#define LINK_CAP_32GT_Reserved_7_2_OFFSET                      2
#define LINK_CAP_32GT_Reserved_7_2_MASK                        0xfc

#define LINK_CAP_32GT_MODIFIED_TS_USAGE_MODE0_SUPPORTED_OFFSET 8
#define LINK_CAP_32GT_MODIFIED_TS_USAGE_MODE0_SUPPORTED_MASK   0x100

#define LINK_CAP_32GT_MODIFIED_TS_USAGE_MODE1_SUPPORTED_OFFSET 9
#define LINK_CAP_32GT_MODIFIED_TS_USAGE_MODE1_SUPPORTED_MASK   0x200

#define LINK_CAP_32GT_MODIFIED_TS_USAGE_MODE2_SUPPORTED_OFFSET 10
#define LINK_CAP_32GT_MODIFIED_TS_USAGE_MODE2_SUPPORTED_MASK   0x400

#define LINK_CAP_32GT_MODIFIED_TS_RESERVED_USAGE_MODES_OFFSET  11
#define LINK_CAP_32GT_MODIFIED_TS_RESERVED_USAGE_MODES_MASK    0xf800

#define LINK_CAP_32GT_Reserved_31_16_OFFSET                    16
#define LINK_CAP_32GT_Reserved_31_16_MASK                      0xffff0000

typedef union {
  struct {
    UINT32                 EQ_BYPASS_TO_HIGHEST_RATE_SUPPORTED:1;
    UINT32                              NO_EQ_NEEDED_SUPPORTED:1;
    UINT32                                        Reserved_7_2:6;
    UINT32                   MODIFIED_TS_USAGE_MODE0_SUPPORTED:1;
    UINT32                   MODIFIED_TS_USAGE_MODE1_SUPPORTED:1;
    UINT32                   MODIFIED_TS_USAGE_MODE2_SUPPORTED:1;
    UINT32                    MODIFIED_TS_RESERVED_USAGE_MODES:5;
    UINT32                                      Reserved_31_16:16;
  } Field;
  UINT32 Value;
} LINK_CAP_32GT_NBIFRCCFG_STRUCT;

#define PCICFG_NBIFRCCFG_LINK_CAP_32GT_OFFSET                       0x504
#define SMN_DEV0_NBIF0RCNBIO0_LINK_CAP_32GT_ADDRESS                   0x10100504UL
#define SMN_DEV0_NBIF0RCNBIO1_LINK_CAP_32GT_ADDRESS                   0x10300504UL
#define SMN_DEV0_NBIF1RCNBIO0_LINK_CAP_32GT_ADDRESS                   0x10200504UL
#define SMN_DEV0_NBIF1RCNBIO1_LINK_CAP_32GT_ADDRESS                   0x10400504UL
#define SMN_DEV1_NBIF0RCNBIO0_LINK_CAP_32GT_ADDRESS                   0x10101504UL
#define SMN_DEV1_NBIF0RCNBIO1_LINK_CAP_32GT_ADDRESS                   0x10301504UL
#define SMN_DEV1_NBIF1RCNBIO0_LINK_CAP_32GT_ADDRESS                   0x10201504UL
#define SMN_DEV1_NBIF1RCNBIO1_LINK_CAP_32GT_ADDRESS                   0x10401504UL


/***********************************************************
* Register Name : LINK_CNTL
************************************************************/

#define LINK_CNTL_PM_CONTROL_OFFSET                            0
#define LINK_CNTL_PM_CONTROL_MASK                              0x3

#define LINK_CNTL_PTM_PROP_DELAY_ADAPT_INTER_B_OFFSET          2
#define LINK_CNTL_PTM_PROP_DELAY_ADAPT_INTER_B_MASK            0x4

#define LINK_CNTL_READ_CPL_BOUNDARY_OFFSET                     3
#define LINK_CNTL_READ_CPL_BOUNDARY_MASK                       0x8

#define LINK_CNTL_LINK_DIS_OFFSET                              4
#define LINK_CNTL_LINK_DIS_MASK                                0x10

#define LINK_CNTL_RETRAIN_LINK_OFFSET                          5
#define LINK_CNTL_RETRAIN_LINK_MASK                            0x20

#define LINK_CNTL_COMMON_CLOCK_CFG_OFFSET                      6
#define LINK_CNTL_COMMON_CLOCK_CFG_MASK                        0x40

#define LINK_CNTL_EXTENDED_SYNC_OFFSET                         7
#define LINK_CNTL_EXTENDED_SYNC_MASK                           0x80

#define LINK_CNTL_CLOCK_POWER_MANAGEMENT_EN_OFFSET             8
#define LINK_CNTL_CLOCK_POWER_MANAGEMENT_EN_MASK               0x100

#define LINK_CNTL_HW_AUTONOMOUS_WIDTH_DISABLE_OFFSET           9
#define LINK_CNTL_HW_AUTONOMOUS_WIDTH_DISABLE_MASK             0x200

#define LINK_CNTL_LINK_BW_MANAGEMENT_INT_EN_OFFSET             10
#define LINK_CNTL_LINK_BW_MANAGEMENT_INT_EN_MASK               0x400

#define LINK_CNTL_LINK_AUTONOMOUS_BW_INT_EN_OFFSET             11
#define LINK_CNTL_LINK_AUTONOMOUS_BW_INT_EN_MASK               0x800

#define LINK_CNTL_Reserved_13_12_OFFSET                        12
#define LINK_CNTL_Reserved_13_12_MASK                          0x3000

#define LINK_CNTL_DRS_SIGNALING_CONTROL_OFFSET                 14
#define LINK_CNTL_DRS_SIGNALING_CONTROL_MASK                   0xc000

typedef union {
  struct {
    UINT16                                          PM_CONTROL:2;
    UINT16                        PTM_PROP_DELAY_ADAPT_INTER_B:1;
    UINT16                                   READ_CPL_BOUNDARY:1;
    UINT16                                            LINK_DIS:1;
    UINT16                                        RETRAIN_LINK:1;
    UINT16                                    COMMON_CLOCK_CFG:1;
    UINT16                                       EXTENDED_SYNC:1;
    UINT16                           CLOCK_POWER_MANAGEMENT_EN:1;
    UINT16                         HW_AUTONOMOUS_WIDTH_DISABLE:1;
    UINT16                           LINK_BW_MANAGEMENT_INT_EN:1;
    UINT16                           LINK_AUTONOMOUS_BW_INT_EN:1;
    UINT16                                      Reserved_13_12:2;
    UINT16                               DRS_SIGNALING_CONTROL:2;
  } Field;
  UINT16 Value;
} LINK_CNTL_NBIFRCCFG_STRUCT;

#define PCICFG_NBIFRCCFG_LINK_CNTL_OFFSET                           0x68
#define SMN_DEV0_NBIF0RCNBIO0_LINK_CNTL_ADDRESS                       0x10100068UL
#define SMN_DEV0_NBIF0RCNBIO1_LINK_CNTL_ADDRESS                       0x10300068UL
#define SMN_DEV0_NBIF1RCNBIO0_LINK_CNTL_ADDRESS                       0x10200068UL
#define SMN_DEV0_NBIF1RCNBIO1_LINK_CNTL_ADDRESS                       0x10400068UL
#define SMN_DEV1_NBIF0RCNBIO0_LINK_CNTL_ADDRESS                       0x10101068UL
#define SMN_DEV1_NBIF0RCNBIO1_LINK_CNTL_ADDRESS                       0x10301068UL
#define SMN_DEV1_NBIF1RCNBIO0_LINK_CNTL_ADDRESS                       0x10201068UL
#define SMN_DEV1_NBIF1RCNBIO1_LINK_CNTL_ADDRESS                       0x10401068UL


/***********************************************************
* Register Name : LINK_CNTL2
************************************************************/

#define LINK_CNTL2_TARGET_LINK_SPEED_OFFSET                    0
#define LINK_CNTL2_TARGET_LINK_SPEED_MASK                      0xf

#define LINK_CNTL2_ENTER_COMPLIANCE_OFFSET                     4
#define LINK_CNTL2_ENTER_COMPLIANCE_MASK                       0x10

#define LINK_CNTL2_HW_AUTONOMOUS_SPEED_DISABLE_OFFSET          5
#define LINK_CNTL2_HW_AUTONOMOUS_SPEED_DISABLE_MASK            0x20

#define LINK_CNTL2_SELECTABLE_DEEMPHASIS_OFFSET                6
#define LINK_CNTL2_SELECTABLE_DEEMPHASIS_MASK                  0x40

#define LINK_CNTL2_XMIT_MARGIN_OFFSET                          7
#define LINK_CNTL2_XMIT_MARGIN_MASK                            0x380

#define LINK_CNTL2_ENTER_MOD_COMPLIANCE_OFFSET                 10
#define LINK_CNTL2_ENTER_MOD_COMPLIANCE_MASK                   0x400

#define LINK_CNTL2_COMPLIANCE_SOS_OFFSET                       11
#define LINK_CNTL2_COMPLIANCE_SOS_MASK                         0x800

#define LINK_CNTL2_COMPLIANCE_DEEMPHASIS_OFFSET                12
#define LINK_CNTL2_COMPLIANCE_DEEMPHASIS_MASK                  0xf000

typedef union {
  struct {
    UINT16                                   TARGET_LINK_SPEED:4;
    UINT16                                    ENTER_COMPLIANCE:1;
    UINT16                         HW_AUTONOMOUS_SPEED_DISABLE:1;
    UINT16                               SELECTABLE_DEEMPHASIS:1;
    UINT16                                         XMIT_MARGIN:3;
    UINT16                                ENTER_MOD_COMPLIANCE:1;
    UINT16                                      COMPLIANCE_SOS:1;
    UINT16                               COMPLIANCE_DEEMPHASIS:4;
  } Field;
  UINT16 Value;
} LINK_CNTL2_NBIFRCCFG_STRUCT;

#define PCICFG_NBIFRCCFG_LINK_CNTL2_OFFSET                          0x88
#define SMN_DEV0_NBIF0RCNBIO0_LINK_CNTL2_ADDRESS                      0x10100088UL
#define SMN_DEV0_NBIF0RCNBIO1_LINK_CNTL2_ADDRESS                      0x10300088UL
#define SMN_DEV0_NBIF1RCNBIO0_LINK_CNTL2_ADDRESS                      0x10200088UL
#define SMN_DEV0_NBIF1RCNBIO1_LINK_CNTL2_ADDRESS                      0x10400088UL
#define SMN_DEV1_NBIF0RCNBIO0_LINK_CNTL2_ADDRESS                      0x10101088UL
#define SMN_DEV1_NBIF0RCNBIO1_LINK_CNTL2_ADDRESS                      0x10301088UL
#define SMN_DEV1_NBIF1RCNBIO0_LINK_CNTL2_ADDRESS                      0x10201088UL
#define SMN_DEV1_NBIF1RCNBIO1_LINK_CNTL2_ADDRESS                      0x10401088UL


/***********************************************************
* Register Name : LINK_CNTL_16GT
************************************************************/

#define LINK_CNTL_16GT_Reserved_31_0_OFFSET                    0
#define LINK_CNTL_16GT_Reserved_31_0_MASK                      0xffffffff

typedef union {
  struct {
    UINT32                                       Reserved_31_0:32;
  } Field;
  UINT32 Value;
} LINK_CNTL_16GT_NBIFRCCFG_STRUCT;

#define PCICFG_NBIFRCCFG_LINK_CNTL_16GT_OFFSET                      0x418
#define SMN_DEV0_NBIF0RCNBIO0_LINK_CNTL_16GT_ADDRESS                  0x10100418UL
#define SMN_DEV0_NBIF0RCNBIO1_LINK_CNTL_16GT_ADDRESS                  0x10300418UL
#define SMN_DEV0_NBIF1RCNBIO0_LINK_CNTL_16GT_ADDRESS                  0x10200418UL
#define SMN_DEV0_NBIF1RCNBIO1_LINK_CNTL_16GT_ADDRESS                  0x10400418UL
#define SMN_DEV1_NBIF0RCNBIO0_LINK_CNTL_16GT_ADDRESS                  0x10101418UL
#define SMN_DEV1_NBIF0RCNBIO1_LINK_CNTL_16GT_ADDRESS                  0x10301418UL
#define SMN_DEV1_NBIF1RCNBIO0_LINK_CNTL_16GT_ADDRESS                  0x10201418UL
#define SMN_DEV1_NBIF1RCNBIO1_LINK_CNTL_16GT_ADDRESS                  0x10401418UL


/***********************************************************
* Register Name : LINK_CNTL_32GT
************************************************************/

#define LINK_CNTL_32GT_EQ_BYPASS_TO_HIGHEST_RATE_DIS_OFFSET    0
#define LINK_CNTL_32GT_EQ_BYPASS_TO_HIGHEST_RATE_DIS_MASK      0x1

#define LINK_CNTL_32GT_NO_EQ_NEEDED_DIS_OFFSET                 1
#define LINK_CNTL_32GT_NO_EQ_NEEDED_DIS_MASK                   0x2

#define LINK_CNTL_32GT_Reserved_7_2_OFFSET                     2
#define LINK_CNTL_32GT_Reserved_7_2_MASK                       0xfc

#define LINK_CNTL_32GT_MODIFIED_TS_USAGE_MODE_SEL_OFFSET       8
#define LINK_CNTL_32GT_MODIFIED_TS_USAGE_MODE_SEL_MASK         0x700

#define LINK_CNTL_32GT_Reserved_31_11_OFFSET                   11
#define LINK_CNTL_32GT_Reserved_31_11_MASK                     0xfffff800

typedef union {
  struct {
    UINT32                       EQ_BYPASS_TO_HIGHEST_RATE_DIS:1;
    UINT32                                    NO_EQ_NEEDED_DIS:1;
    UINT32                                        Reserved_7_2:6;
    UINT32                          MODIFIED_TS_USAGE_MODE_SEL:3;
    UINT32                                      Reserved_31_11:21;
  } Field;
  UINT32 Value;
} LINK_CNTL_32GT_NBIFRCCFG_STRUCT;

#define PCICFG_NBIFRCCFG_LINK_CNTL_32GT_OFFSET                      0x508
#define SMN_DEV0_NBIF0RCNBIO0_LINK_CNTL_32GT_ADDRESS                  0x10100508UL
#define SMN_DEV0_NBIF0RCNBIO1_LINK_CNTL_32GT_ADDRESS                  0x10300508UL
#define SMN_DEV0_NBIF1RCNBIO0_LINK_CNTL_32GT_ADDRESS                  0x10200508UL
#define SMN_DEV0_NBIF1RCNBIO1_LINK_CNTL_32GT_ADDRESS                  0x10400508UL
#define SMN_DEV1_NBIF0RCNBIO0_LINK_CNTL_32GT_ADDRESS                  0x10101508UL
#define SMN_DEV1_NBIF0RCNBIO1_LINK_CNTL_32GT_ADDRESS                  0x10301508UL
#define SMN_DEV1_NBIF1RCNBIO0_LINK_CNTL_32GT_ADDRESS                  0x10201508UL
#define SMN_DEV1_NBIF1RCNBIO1_LINK_CNTL_32GT_ADDRESS                  0x10401508UL


/***********************************************************
* Register Name : LINK_STATUS
************************************************************/

#define LINK_STATUS_CURRENT_LINK_SPEED_OFFSET                  0
#define LINK_STATUS_CURRENT_LINK_SPEED_MASK                    0xf

#define LINK_STATUS_NEGOTIATED_LINK_WIDTH_OFFSET               4
#define LINK_STATUS_NEGOTIATED_LINK_WIDTH_MASK                 0x3f0

#define LINK_STATUS_Reserved_10_10_OFFSET                      10
#define LINK_STATUS_Reserved_10_10_MASK                        0x400

#define LINK_STATUS_LINK_TRAINING_OFFSET                       11
#define LINK_STATUS_LINK_TRAINING_MASK                         0x800

#define LINK_STATUS_SLOT_CLOCK_CFG_OFFSET                      12
#define LINK_STATUS_SLOT_CLOCK_CFG_MASK                        0x1000

#define LINK_STATUS_DL_ACTIVE_OFFSET                           13
#define LINK_STATUS_DL_ACTIVE_MASK                             0x2000

#define LINK_STATUS_LINK_BW_MANAGEMENT_STATUS_OFFSET           14
#define LINK_STATUS_LINK_BW_MANAGEMENT_STATUS_MASK             0x4000

#define LINK_STATUS_LINK_AUTONOMOUS_BW_STATUS_OFFSET           15
#define LINK_STATUS_LINK_AUTONOMOUS_BW_STATUS_MASK             0x8000

typedef union {
  struct {
    UINT16                                  CURRENT_LINK_SPEED:4;
    UINT16                               NEGOTIATED_LINK_WIDTH:6;
    UINT16                                      Reserved_10_10:1;
    UINT16                                       LINK_TRAINING:1;
    UINT16                                      SLOT_CLOCK_CFG:1;
    UINT16                                           DL_ACTIVE:1;
    UINT16                           LINK_BW_MANAGEMENT_STATUS:1;
    UINT16                           LINK_AUTONOMOUS_BW_STATUS:1;
  } Field;
  UINT16 Value;
} LINK_STATUS_NBIFRCCFG_STRUCT;

#define PCICFG_NBIFRCCFG_LINK_STATUS_OFFSET                         0x6a
#define SMN_DEV0_NBIF0RCNBIO0_LINK_STATUS_ADDRESS                     0x1010006aUL
#define SMN_DEV0_NBIF0RCNBIO1_LINK_STATUS_ADDRESS                     0x1030006aUL
#define SMN_DEV0_NBIF1RCNBIO0_LINK_STATUS_ADDRESS                     0x1020006aUL
#define SMN_DEV0_NBIF1RCNBIO1_LINK_STATUS_ADDRESS                     0x1040006aUL
#define SMN_DEV1_NBIF0RCNBIO0_LINK_STATUS_ADDRESS                     0x1010106aUL
#define SMN_DEV1_NBIF0RCNBIO1_LINK_STATUS_ADDRESS                     0x1030106aUL
#define SMN_DEV1_NBIF1RCNBIO0_LINK_STATUS_ADDRESS                     0x1020106aUL
#define SMN_DEV1_NBIF1RCNBIO1_LINK_STATUS_ADDRESS                     0x1040106aUL


/***********************************************************
* Register Name : LINK_STATUS2
************************************************************/

#define LINK_STATUS2_CUR_DEEMPHASIS_LEVEL_OFFSET               0
#define LINK_STATUS2_CUR_DEEMPHASIS_LEVEL_MASK                 0x1

#define LINK_STATUS2_EQUALIZATION_COMPLETE_8GT_OFFSET          1
#define LINK_STATUS2_EQUALIZATION_COMPLETE_8GT_MASK            0x2

#define LINK_STATUS2_EQUALIZATION_PHASE1_SUCCESS_8GT_OFFSET    2
#define LINK_STATUS2_EQUALIZATION_PHASE1_SUCCESS_8GT_MASK      0x4

#define LINK_STATUS2_EQUALIZATION_PHASE2_SUCCESS_8GT_OFFSET    3
#define LINK_STATUS2_EQUALIZATION_PHASE2_SUCCESS_8GT_MASK      0x8

#define LINK_STATUS2_EQUALIZATION_PHASE3_SUCCESS_8GT_OFFSET    4
#define LINK_STATUS2_EQUALIZATION_PHASE3_SUCCESS_8GT_MASK      0x10

#define LINK_STATUS2_LINK_EQUALIZATION_REQUEST_8GT_OFFSET      5
#define LINK_STATUS2_LINK_EQUALIZATION_REQUEST_8GT_MASK        0x20

#define LINK_STATUS2_RTM1_PRESENCE_DET_OFFSET                  6
#define LINK_STATUS2_RTM1_PRESENCE_DET_MASK                    0x40

#define LINK_STATUS2_RTM2_PRESENCE_DET_OFFSET                  7
#define LINK_STATUS2_RTM2_PRESENCE_DET_MASK                    0x80

#define LINK_STATUS2_CROSSLINK_RESOLUTION_OFFSET               8
#define LINK_STATUS2_CROSSLINK_RESOLUTION_MASK                 0x300

#define LINK_STATUS2_Reserved_11_10_OFFSET                     10
#define LINK_STATUS2_Reserved_11_10_MASK                       0xc00

#define LINK_STATUS2_DOWNSTREAM_COMPONENT_PRESENCE_OFFSET      12
#define LINK_STATUS2_DOWNSTREAM_COMPONENT_PRESENCE_MASK        0x7000

#define LINK_STATUS2_DRS_MESSAGE_RECEIVED_OFFSET               15
#define LINK_STATUS2_DRS_MESSAGE_RECEIVED_MASK                 0x8000

typedef union {
  struct {
    UINT16                                CUR_DEEMPHASIS_LEVEL:1;
    UINT16                           EQUALIZATION_COMPLETE_8GT:1;
    UINT16                     EQUALIZATION_PHASE1_SUCCESS_8GT:1;
    UINT16                     EQUALIZATION_PHASE2_SUCCESS_8GT:1;
    UINT16                     EQUALIZATION_PHASE3_SUCCESS_8GT:1;
    UINT16                       LINK_EQUALIZATION_REQUEST_8GT:1;
    UINT16                                   RTM1_PRESENCE_DET:1;
    UINT16                                   RTM2_PRESENCE_DET:1;
    UINT16                                CROSSLINK_RESOLUTION:2;
    UINT16                                      Reserved_11_10:2;
    UINT16                       DOWNSTREAM_COMPONENT_PRESENCE:3;
    UINT16                                DRS_MESSAGE_RECEIVED:1;
  } Field;
  UINT16 Value;
} LINK_STATUS2_NBIFRCCFG_STRUCT;

#define PCICFG_NBIFRCCFG_LINK_STATUS2_OFFSET                        0x8a
#define SMN_DEV0_NBIF0RCNBIO0_LINK_STATUS2_ADDRESS                    0x1010008aUL
#define SMN_DEV0_NBIF0RCNBIO1_LINK_STATUS2_ADDRESS                    0x1030008aUL
#define SMN_DEV0_NBIF1RCNBIO0_LINK_STATUS2_ADDRESS                    0x1020008aUL
#define SMN_DEV0_NBIF1RCNBIO1_LINK_STATUS2_ADDRESS                    0x1040008aUL
#define SMN_DEV1_NBIF0RCNBIO0_LINK_STATUS2_ADDRESS                    0x1010108aUL
#define SMN_DEV1_NBIF0RCNBIO1_LINK_STATUS2_ADDRESS                    0x1030108aUL
#define SMN_DEV1_NBIF1RCNBIO0_LINK_STATUS2_ADDRESS                    0x1020108aUL
#define SMN_DEV1_NBIF1RCNBIO1_LINK_STATUS2_ADDRESS                    0x1040108aUL


/***********************************************************
* Register Name : LINK_STATUS_16GT
************************************************************/

#define LINK_STATUS_16GT_EQUALIZATION_COMPLETE_16GT_OFFSET     0
#define LINK_STATUS_16GT_EQUALIZATION_COMPLETE_16GT_MASK       0x1

#define LINK_STATUS_16GT_EQUALIZATION_PHASE1_SUCCESS_16GT_OFFSET 1
#define LINK_STATUS_16GT_EQUALIZATION_PHASE1_SUCCESS_16GT_MASK 0x2

#define LINK_STATUS_16GT_EQUALIZATION_PHASE2_SUCCESS_16GT_OFFSET 2
#define LINK_STATUS_16GT_EQUALIZATION_PHASE2_SUCCESS_16GT_MASK 0x4

#define LINK_STATUS_16GT_EQUALIZATION_PHASE3_SUCCESS_16GT_OFFSET 3
#define LINK_STATUS_16GT_EQUALIZATION_PHASE3_SUCCESS_16GT_MASK 0x8

#define LINK_STATUS_16GT_LINK_EQUALIZATION_REQUEST_16GT_OFFSET 4
#define LINK_STATUS_16GT_LINK_EQUALIZATION_REQUEST_16GT_MASK   0x10

#define LINK_STATUS_16GT_Reserved_31_5_OFFSET                  5
#define LINK_STATUS_16GT_Reserved_31_5_MASK                    0xffffffe0

typedef union {
  struct {
    UINT32                          EQUALIZATION_COMPLETE_16GT:1;
    UINT32                    EQUALIZATION_PHASE1_SUCCESS_16GT:1;
    UINT32                    EQUALIZATION_PHASE2_SUCCESS_16GT:1;
    UINT32                    EQUALIZATION_PHASE3_SUCCESS_16GT:1;
    UINT32                      LINK_EQUALIZATION_REQUEST_16GT:1;
    UINT32                                       Reserved_31_5:27;
  } Field;
  UINT32 Value;
} LINK_STATUS_16GT_NBIFRCCFG_STRUCT;

#define PCICFG_NBIFRCCFG_LINK_STATUS_16GT_OFFSET                    0x41c
#define SMN_DEV0_NBIF0RCNBIO0_LINK_STATUS_16GT_ADDRESS                0x1010041cUL
#define SMN_DEV0_NBIF0RCNBIO1_LINK_STATUS_16GT_ADDRESS                0x1030041cUL
#define SMN_DEV0_NBIF1RCNBIO0_LINK_STATUS_16GT_ADDRESS                0x1020041cUL
#define SMN_DEV0_NBIF1RCNBIO1_LINK_STATUS_16GT_ADDRESS                0x1040041cUL
#define SMN_DEV1_NBIF0RCNBIO0_LINK_STATUS_16GT_ADDRESS                0x1010141cUL
#define SMN_DEV1_NBIF0RCNBIO1_LINK_STATUS_16GT_ADDRESS                0x1030141cUL
#define SMN_DEV1_NBIF1RCNBIO0_LINK_STATUS_16GT_ADDRESS                0x1020141cUL
#define SMN_DEV1_NBIF1RCNBIO1_LINK_STATUS_16GT_ADDRESS                0x1040141cUL


/***********************************************************
* Register Name : LINK_STATUS_32GT
************************************************************/

#define LINK_STATUS_32GT_EQUALIZATION_COMPLETE_32GT_OFFSET     0
#define LINK_STATUS_32GT_EQUALIZATION_COMPLETE_32GT_MASK       0x1

#define LINK_STATUS_32GT_EQUALIZATION_PHASE1_SUCCESS_32GT_OFFSET 1
#define LINK_STATUS_32GT_EQUALIZATION_PHASE1_SUCCESS_32GT_MASK 0x2

#define LINK_STATUS_32GT_EQUALIZATION_PHASE2_SUCCESS_32GT_OFFSET 2
#define LINK_STATUS_32GT_EQUALIZATION_PHASE2_SUCCESS_32GT_MASK 0x4

#define LINK_STATUS_32GT_EQUALIZATION_PHASE3_SUCCESS_32GT_OFFSET 3
#define LINK_STATUS_32GT_EQUALIZATION_PHASE3_SUCCESS_32GT_MASK 0x8

#define LINK_STATUS_32GT_LINK_EQUALIZATION_REQUEST_32GT_OFFSET 4
#define LINK_STATUS_32GT_LINK_EQUALIZATION_REQUEST_32GT_MASK   0x10

#define LINK_STATUS_32GT_MODIFIED_TS_RECEIVED_OFFSET           5
#define LINK_STATUS_32GT_MODIFIED_TS_RECEIVED_MASK             0x20

#define LINK_STATUS_32GT_RECEIVED_ENHANCED_LINK_BEHAVIOR_CNTL_OFFSET 6
#define LINK_STATUS_32GT_RECEIVED_ENHANCED_LINK_BEHAVIOR_CNTL_MASK 0xc0

#define LINK_STATUS_32GT_TRANSMITTER_PRECODING_ON_OFFSET       8
#define LINK_STATUS_32GT_TRANSMITTER_PRECODING_ON_MASK         0x100

#define LINK_STATUS_32GT_TRANSMITTER_PRECODE_REQUEST_OFFSET    9
#define LINK_STATUS_32GT_TRANSMITTER_PRECODE_REQUEST_MASK      0x200

#define LINK_STATUS_32GT_NO_EQ_NEEDED_RECEIVED_OFFSET          10
#define LINK_STATUS_32GT_NO_EQ_NEEDED_RECEIVED_MASK            0x400

#define LINK_STATUS_32GT_Reserved_31_11_OFFSET                 11
#define LINK_STATUS_32GT_Reserved_31_11_MASK                   0xfffff800

typedef union {
  struct {
    UINT32                          EQUALIZATION_COMPLETE_32GT:1;
    UINT32                    EQUALIZATION_PHASE1_SUCCESS_32GT:1;
    UINT32                    EQUALIZATION_PHASE2_SUCCESS_32GT:1;
    UINT32                    EQUALIZATION_PHASE3_SUCCESS_32GT:1;
    UINT32                      LINK_EQUALIZATION_REQUEST_32GT:1;
    UINT32                                MODIFIED_TS_RECEIVED:1;
    UINT32                RECEIVED_ENHANCED_LINK_BEHAVIOR_CNTL:2;
    UINT32                            TRANSMITTER_PRECODING_ON:1;
    UINT32                         TRANSMITTER_PRECODE_REQUEST:1;
    UINT32                               NO_EQ_NEEDED_RECEIVED:1;
    UINT32                                      Reserved_31_11:21;
  } Field;
  UINT32 Value;
} LINK_STATUS_32GT_NBIFRCCFG_STRUCT;

#define PCICFG_NBIFRCCFG_LINK_STATUS_32GT_OFFSET                    0x50c
#define SMN_DEV0_NBIF0RCNBIO0_LINK_STATUS_32GT_ADDRESS                0x1010050cUL
#define SMN_DEV0_NBIF0RCNBIO1_LINK_STATUS_32GT_ADDRESS                0x1030050cUL
#define SMN_DEV0_NBIF1RCNBIO0_LINK_STATUS_32GT_ADDRESS                0x1020050cUL
#define SMN_DEV0_NBIF1RCNBIO1_LINK_STATUS_32GT_ADDRESS                0x1040050cUL
#define SMN_DEV1_NBIF0RCNBIO0_LINK_STATUS_32GT_ADDRESS                0x1010150cUL
#define SMN_DEV1_NBIF0RCNBIO1_LINK_STATUS_32GT_ADDRESS                0x1030150cUL
#define SMN_DEV1_NBIF1RCNBIO0_LINK_STATUS_32GT_ADDRESS                0x1020150cUL
#define SMN_DEV1_NBIF1RCNBIO1_LINK_STATUS_32GT_ADDRESS                0x1040150cUL


/***********************************************************
* Register Name : LOCAL_PARITY_MISMATCH_STATUS_16GT
************************************************************/

#define LOCAL_PARITY_MISMATCH_STATUS_16GT_LOCAL_PARITY_MISMATCH_STATUS_BITS_OFFSET 0
#define LOCAL_PARITY_MISMATCH_STATUS_16GT_LOCAL_PARITY_MISMATCH_STATUS_BITS_MASK 0xffff

#define LOCAL_PARITY_MISMATCH_STATUS_16GT_Reserved_31_16_OFFSET 16
#define LOCAL_PARITY_MISMATCH_STATUS_16GT_Reserved_31_16_MASK  0xffff0000

typedef union {
  struct {
    UINT32                   LOCAL_PARITY_MISMATCH_STATUS_BITS:16;
    UINT32                                      Reserved_31_16:16;
  } Field;
  UINT32 Value;
} LOCAL_PARITY_MISMATCH_STATUS_16GT_NBIFRCCFG_STRUCT;

#define PCICFG_NBIFRCCFG_LOCAL_PARITY_MISMATCH_STATUS_16GT_OFFSET   0x420
#define SMN_DEV0_NBIF0RCNBIO0_LOCAL_PARITY_MISMATCH_STATUS_16GT_ADDRESS 0x10100420UL
#define SMN_DEV0_NBIF0RCNBIO1_LOCAL_PARITY_MISMATCH_STATUS_16GT_ADDRESS 0x10300420UL
#define SMN_DEV0_NBIF1RCNBIO0_LOCAL_PARITY_MISMATCH_STATUS_16GT_ADDRESS 0x10200420UL
#define SMN_DEV0_NBIF1RCNBIO1_LOCAL_PARITY_MISMATCH_STATUS_16GT_ADDRESS 0x10400420UL
#define SMN_DEV1_NBIF0RCNBIO0_LOCAL_PARITY_MISMATCH_STATUS_16GT_ADDRESS 0x10101420UL
#define SMN_DEV1_NBIF0RCNBIO1_LOCAL_PARITY_MISMATCH_STATUS_16GT_ADDRESS 0x10301420UL
#define SMN_DEV1_NBIF1RCNBIO0_LOCAL_PARITY_MISMATCH_STATUS_16GT_ADDRESS 0x10201420UL
#define SMN_DEV1_NBIF1RCNBIO1_LOCAL_PARITY_MISMATCH_STATUS_16GT_ADDRESS 0x10401420UL


/***********************************************************
* Register Name : MARGINING_LANE_CNTL
************************************************************/

#define MARGINING_LANE_CNTL_RECEIVER_NUMBER_OFFSET             0
#define MARGINING_LANE_CNTL_RECEIVER_NUMBER_MASK               0x7

#define MARGINING_LANE_CNTL_MARGIN_TYPE_OFFSET                 3
#define MARGINING_LANE_CNTL_MARGIN_TYPE_MASK                   0x38

#define MARGINING_LANE_CNTL_USAGE_MODEL_OFFSET                 6
#define MARGINING_LANE_CNTL_USAGE_MODEL_MASK                   0x40

#define MARGINING_LANE_CNTL_Reserved_7_7_OFFSET                7
#define MARGINING_LANE_CNTL_Reserved_7_7_MASK                  0x80

#define MARGINING_LANE_CNTL_MARGIN_PAYLOAD_OFFSET              8
#define MARGINING_LANE_CNTL_MARGIN_PAYLOAD_MASK                0xff00

typedef union {
  struct {
    UINT16                                     RECEIVER_NUMBER:3;
    UINT16                                         MARGIN_TYPE:3;
    UINT16                                         USAGE_MODEL:1;
    UINT16                                        Reserved_7_7:1;
    UINT16                                      MARGIN_PAYLOAD:8;
  } Field;
  UINT16 Value;
} MARGINING_LANE_CNTL_NBIFRCCFG_STRUCT;

#define PCICFG_NBIFRCCFG_MARGINING_LANE_CNTL_OFFSET                 0x458
#define SMN_DEV0_NBIF0RC_N0NBIO0_MARGINING_LANE_CNTL_ADDRESS          0x10100458UL
#define SMN_DEV0_NBIF0RC_N0NBIO1_MARGINING_LANE_CNTL_ADDRESS          0x10300458UL
#define SMN_DEV0_NBIF0RC_N10NBIO0_MARGINING_LANE_CNTL_ADDRESS         0x10100480UL
#define SMN_DEV0_NBIF0RC_N10NBIO1_MARGINING_LANE_CNTL_ADDRESS         0x10300480UL
#define SMN_DEV0_NBIF0RC_N11NBIO0_MARGINING_LANE_CNTL_ADDRESS         0x10100484UL
#define SMN_DEV0_NBIF0RC_N11NBIO1_MARGINING_LANE_CNTL_ADDRESS         0x10300484UL
#define SMN_DEV0_NBIF0RC_N12NBIO0_MARGINING_LANE_CNTL_ADDRESS         0x10100488UL
#define SMN_DEV0_NBIF0RC_N12NBIO1_MARGINING_LANE_CNTL_ADDRESS         0x10300488UL
#define SMN_DEV0_NBIF0RC_N13NBIO0_MARGINING_LANE_CNTL_ADDRESS         0x1010048cUL
#define SMN_DEV0_NBIF0RC_N13NBIO1_MARGINING_LANE_CNTL_ADDRESS         0x1030048cUL
#define SMN_DEV0_NBIF0RC_N14NBIO0_MARGINING_LANE_CNTL_ADDRESS         0x10100490UL
#define SMN_DEV0_NBIF0RC_N14NBIO1_MARGINING_LANE_CNTL_ADDRESS         0x10300490UL
#define SMN_DEV0_NBIF0RC_N15NBIO0_MARGINING_LANE_CNTL_ADDRESS         0x10100494UL
#define SMN_DEV0_NBIF0RC_N15NBIO1_MARGINING_LANE_CNTL_ADDRESS         0x10300494UL
#define SMN_DEV0_NBIF0RC_N1NBIO0_MARGINING_LANE_CNTL_ADDRESS          0x1010045cUL
#define SMN_DEV0_NBIF0RC_N1NBIO1_MARGINING_LANE_CNTL_ADDRESS          0x1030045cUL
#define SMN_DEV0_NBIF0RC_N2NBIO0_MARGINING_LANE_CNTL_ADDRESS          0x10100460UL
#define SMN_DEV0_NBIF0RC_N2NBIO1_MARGINING_LANE_CNTL_ADDRESS          0x10300460UL
#define SMN_DEV0_NBIF0RC_N3NBIO0_MARGINING_LANE_CNTL_ADDRESS          0x10100464UL
#define SMN_DEV0_NBIF0RC_N3NBIO1_MARGINING_LANE_CNTL_ADDRESS          0x10300464UL
#define SMN_DEV0_NBIF0RC_N4NBIO0_MARGINING_LANE_CNTL_ADDRESS          0x10100468UL
#define SMN_DEV0_NBIF0RC_N4NBIO1_MARGINING_LANE_CNTL_ADDRESS          0x10300468UL
#define SMN_DEV0_NBIF0RC_N5NBIO0_MARGINING_LANE_CNTL_ADDRESS          0x1010046cUL
#define SMN_DEV0_NBIF0RC_N5NBIO1_MARGINING_LANE_CNTL_ADDRESS          0x1030046cUL
#define SMN_DEV0_NBIF0RC_N6NBIO0_MARGINING_LANE_CNTL_ADDRESS          0x10100470UL
#define SMN_DEV0_NBIF0RC_N6NBIO1_MARGINING_LANE_CNTL_ADDRESS          0x10300470UL
#define SMN_DEV0_NBIF0RC_N7NBIO0_MARGINING_LANE_CNTL_ADDRESS          0x10100474UL
#define SMN_DEV0_NBIF0RC_N7NBIO1_MARGINING_LANE_CNTL_ADDRESS          0x10300474UL
#define SMN_DEV0_NBIF0RC_N8NBIO0_MARGINING_LANE_CNTL_ADDRESS          0x10100478UL
#define SMN_DEV0_NBIF0RC_N8NBIO1_MARGINING_LANE_CNTL_ADDRESS          0x10300478UL
#define SMN_DEV0_NBIF0RC_N9NBIO0_MARGINING_LANE_CNTL_ADDRESS          0x1010047cUL
#define SMN_DEV0_NBIF0RC_N9NBIO1_MARGINING_LANE_CNTL_ADDRESS          0x1030047cUL
#define SMN_DEV0_NBIF1RC_N0NBIO0_MARGINING_LANE_CNTL_ADDRESS          0x10200458UL
#define SMN_DEV0_NBIF1RC_N0NBIO1_MARGINING_LANE_CNTL_ADDRESS          0x10400458UL
#define SMN_DEV0_NBIF1RC_N10NBIO0_MARGINING_LANE_CNTL_ADDRESS         0x10200480UL
#define SMN_DEV0_NBIF1RC_N10NBIO1_MARGINING_LANE_CNTL_ADDRESS         0x10400480UL
#define SMN_DEV0_NBIF1RC_N11NBIO0_MARGINING_LANE_CNTL_ADDRESS         0x10200484UL
#define SMN_DEV0_NBIF1RC_N11NBIO1_MARGINING_LANE_CNTL_ADDRESS         0x10400484UL
#define SMN_DEV0_NBIF1RC_N12NBIO0_MARGINING_LANE_CNTL_ADDRESS         0x10200488UL
#define SMN_DEV0_NBIF1RC_N12NBIO1_MARGINING_LANE_CNTL_ADDRESS         0x10400488UL
#define SMN_DEV0_NBIF1RC_N13NBIO0_MARGINING_LANE_CNTL_ADDRESS         0x1020048cUL
#define SMN_DEV0_NBIF1RC_N13NBIO1_MARGINING_LANE_CNTL_ADDRESS         0x1040048cUL
#define SMN_DEV0_NBIF1RC_N14NBIO0_MARGINING_LANE_CNTL_ADDRESS         0x10200490UL
#define SMN_DEV0_NBIF1RC_N14NBIO1_MARGINING_LANE_CNTL_ADDRESS         0x10400490UL
#define SMN_DEV0_NBIF1RC_N15NBIO0_MARGINING_LANE_CNTL_ADDRESS         0x10200494UL
#define SMN_DEV0_NBIF1RC_N15NBIO1_MARGINING_LANE_CNTL_ADDRESS         0x10400494UL
#define SMN_DEV0_NBIF1RC_N1NBIO0_MARGINING_LANE_CNTL_ADDRESS          0x1020045cUL
#define SMN_DEV0_NBIF1RC_N1NBIO1_MARGINING_LANE_CNTL_ADDRESS          0x1040045cUL
#define SMN_DEV0_NBIF1RC_N2NBIO0_MARGINING_LANE_CNTL_ADDRESS          0x10200460UL
#define SMN_DEV0_NBIF1RC_N2NBIO1_MARGINING_LANE_CNTL_ADDRESS          0x10400460UL
#define SMN_DEV0_NBIF1RC_N3NBIO0_MARGINING_LANE_CNTL_ADDRESS          0x10200464UL
#define SMN_DEV0_NBIF1RC_N3NBIO1_MARGINING_LANE_CNTL_ADDRESS          0x10400464UL
#define SMN_DEV0_NBIF1RC_N4NBIO0_MARGINING_LANE_CNTL_ADDRESS          0x10200468UL
#define SMN_DEV0_NBIF1RC_N4NBIO1_MARGINING_LANE_CNTL_ADDRESS          0x10400468UL
#define SMN_DEV0_NBIF1RC_N5NBIO0_MARGINING_LANE_CNTL_ADDRESS          0x1020046cUL
#define SMN_DEV0_NBIF1RC_N5NBIO1_MARGINING_LANE_CNTL_ADDRESS          0x1040046cUL
#define SMN_DEV0_NBIF1RC_N6NBIO0_MARGINING_LANE_CNTL_ADDRESS          0x10200470UL
#define SMN_DEV0_NBIF1RC_N6NBIO1_MARGINING_LANE_CNTL_ADDRESS          0x10400470UL
#define SMN_DEV0_NBIF1RC_N7NBIO0_MARGINING_LANE_CNTL_ADDRESS          0x10200474UL
#define SMN_DEV0_NBIF1RC_N7NBIO1_MARGINING_LANE_CNTL_ADDRESS          0x10400474UL
#define SMN_DEV0_NBIF1RC_N8NBIO0_MARGINING_LANE_CNTL_ADDRESS          0x10200478UL
#define SMN_DEV0_NBIF1RC_N8NBIO1_MARGINING_LANE_CNTL_ADDRESS          0x10400478UL
#define SMN_DEV0_NBIF1RC_N9NBIO0_MARGINING_LANE_CNTL_ADDRESS          0x1020047cUL
#define SMN_DEV0_NBIF1RC_N9NBIO1_MARGINING_LANE_CNTL_ADDRESS          0x1040047cUL
#define SMN_DEV1_NBIF0RC_N0NBIO0_MARGINING_LANE_CNTL_ADDRESS          0x10101458UL
#define SMN_DEV1_NBIF0RC_N0NBIO1_MARGINING_LANE_CNTL_ADDRESS          0x10301458UL
#define SMN_DEV1_NBIF0RC_N10NBIO0_MARGINING_LANE_CNTL_ADDRESS         0x10101480UL
#define SMN_DEV1_NBIF0RC_N10NBIO1_MARGINING_LANE_CNTL_ADDRESS         0x10301480UL
#define SMN_DEV1_NBIF0RC_N11NBIO0_MARGINING_LANE_CNTL_ADDRESS         0x10101484UL
#define SMN_DEV1_NBIF0RC_N11NBIO1_MARGINING_LANE_CNTL_ADDRESS         0x10301484UL
#define SMN_DEV1_NBIF0RC_N12NBIO0_MARGINING_LANE_CNTL_ADDRESS         0x10101488UL
#define SMN_DEV1_NBIF0RC_N12NBIO1_MARGINING_LANE_CNTL_ADDRESS         0x10301488UL
#define SMN_DEV1_NBIF0RC_N13NBIO0_MARGINING_LANE_CNTL_ADDRESS         0x1010148cUL
#define SMN_DEV1_NBIF0RC_N13NBIO1_MARGINING_LANE_CNTL_ADDRESS         0x1030148cUL
#define SMN_DEV1_NBIF0RC_N14NBIO0_MARGINING_LANE_CNTL_ADDRESS         0x10101490UL
#define SMN_DEV1_NBIF0RC_N14NBIO1_MARGINING_LANE_CNTL_ADDRESS         0x10301490UL
#define SMN_DEV1_NBIF0RC_N15NBIO0_MARGINING_LANE_CNTL_ADDRESS         0x10101494UL
#define SMN_DEV1_NBIF0RC_N15NBIO1_MARGINING_LANE_CNTL_ADDRESS         0x10301494UL
#define SMN_DEV1_NBIF0RC_N1NBIO0_MARGINING_LANE_CNTL_ADDRESS          0x1010145cUL
#define SMN_DEV1_NBIF0RC_N1NBIO1_MARGINING_LANE_CNTL_ADDRESS          0x1030145cUL
#define SMN_DEV1_NBIF0RC_N2NBIO0_MARGINING_LANE_CNTL_ADDRESS          0x10101460UL
#define SMN_DEV1_NBIF0RC_N2NBIO1_MARGINING_LANE_CNTL_ADDRESS          0x10301460UL
#define SMN_DEV1_NBIF0RC_N3NBIO0_MARGINING_LANE_CNTL_ADDRESS          0x10101464UL
#define SMN_DEV1_NBIF0RC_N3NBIO1_MARGINING_LANE_CNTL_ADDRESS          0x10301464UL
#define SMN_DEV1_NBIF0RC_N4NBIO0_MARGINING_LANE_CNTL_ADDRESS          0x10101468UL
#define SMN_DEV1_NBIF0RC_N4NBIO1_MARGINING_LANE_CNTL_ADDRESS          0x10301468UL
#define SMN_DEV1_NBIF0RC_N5NBIO0_MARGINING_LANE_CNTL_ADDRESS          0x1010146cUL
#define SMN_DEV1_NBIF0RC_N5NBIO1_MARGINING_LANE_CNTL_ADDRESS          0x1030146cUL
#define SMN_DEV1_NBIF0RC_N6NBIO0_MARGINING_LANE_CNTL_ADDRESS          0x10101470UL
#define SMN_DEV1_NBIF0RC_N6NBIO1_MARGINING_LANE_CNTL_ADDRESS          0x10301470UL
#define SMN_DEV1_NBIF0RC_N7NBIO0_MARGINING_LANE_CNTL_ADDRESS          0x10101474UL
#define SMN_DEV1_NBIF0RC_N7NBIO1_MARGINING_LANE_CNTL_ADDRESS          0x10301474UL
#define SMN_DEV1_NBIF0RC_N8NBIO0_MARGINING_LANE_CNTL_ADDRESS          0x10101478UL
#define SMN_DEV1_NBIF0RC_N8NBIO1_MARGINING_LANE_CNTL_ADDRESS          0x10301478UL
#define SMN_DEV1_NBIF0RC_N9NBIO0_MARGINING_LANE_CNTL_ADDRESS          0x1010147cUL
#define SMN_DEV1_NBIF0RC_N9NBIO1_MARGINING_LANE_CNTL_ADDRESS          0x1030147cUL
#define SMN_DEV1_NBIF1RC_N0NBIO0_MARGINING_LANE_CNTL_ADDRESS          0x10201458UL
#define SMN_DEV1_NBIF1RC_N0NBIO1_MARGINING_LANE_CNTL_ADDRESS          0x10401458UL
#define SMN_DEV1_NBIF1RC_N10NBIO0_MARGINING_LANE_CNTL_ADDRESS         0x10201480UL
#define SMN_DEV1_NBIF1RC_N10NBIO1_MARGINING_LANE_CNTL_ADDRESS         0x10401480UL
#define SMN_DEV1_NBIF1RC_N11NBIO0_MARGINING_LANE_CNTL_ADDRESS         0x10201484UL
#define SMN_DEV1_NBIF1RC_N11NBIO1_MARGINING_LANE_CNTL_ADDRESS         0x10401484UL
#define SMN_DEV1_NBIF1RC_N12NBIO0_MARGINING_LANE_CNTL_ADDRESS         0x10201488UL
#define SMN_DEV1_NBIF1RC_N12NBIO1_MARGINING_LANE_CNTL_ADDRESS         0x10401488UL
#define SMN_DEV1_NBIF1RC_N13NBIO0_MARGINING_LANE_CNTL_ADDRESS         0x1020148cUL
#define SMN_DEV1_NBIF1RC_N13NBIO1_MARGINING_LANE_CNTL_ADDRESS         0x1040148cUL
#define SMN_DEV1_NBIF1RC_N14NBIO0_MARGINING_LANE_CNTL_ADDRESS         0x10201490UL
#define SMN_DEV1_NBIF1RC_N14NBIO1_MARGINING_LANE_CNTL_ADDRESS         0x10401490UL
#define SMN_DEV1_NBIF1RC_N15NBIO0_MARGINING_LANE_CNTL_ADDRESS         0x10201494UL
#define SMN_DEV1_NBIF1RC_N15NBIO1_MARGINING_LANE_CNTL_ADDRESS         0x10401494UL
#define SMN_DEV1_NBIF1RC_N1NBIO0_MARGINING_LANE_CNTL_ADDRESS          0x1020145cUL
#define SMN_DEV1_NBIF1RC_N1NBIO1_MARGINING_LANE_CNTL_ADDRESS          0x1040145cUL
#define SMN_DEV1_NBIF1RC_N2NBIO0_MARGINING_LANE_CNTL_ADDRESS          0x10201460UL
#define SMN_DEV1_NBIF1RC_N2NBIO1_MARGINING_LANE_CNTL_ADDRESS          0x10401460UL
#define SMN_DEV1_NBIF1RC_N3NBIO0_MARGINING_LANE_CNTL_ADDRESS          0x10201464UL
#define SMN_DEV1_NBIF1RC_N3NBIO1_MARGINING_LANE_CNTL_ADDRESS          0x10401464UL
#define SMN_DEV1_NBIF1RC_N4NBIO0_MARGINING_LANE_CNTL_ADDRESS          0x10201468UL
#define SMN_DEV1_NBIF1RC_N4NBIO1_MARGINING_LANE_CNTL_ADDRESS          0x10401468UL
#define SMN_DEV1_NBIF1RC_N5NBIO0_MARGINING_LANE_CNTL_ADDRESS          0x1020146cUL
#define SMN_DEV1_NBIF1RC_N5NBIO1_MARGINING_LANE_CNTL_ADDRESS          0x1040146cUL
#define SMN_DEV1_NBIF1RC_N6NBIO0_MARGINING_LANE_CNTL_ADDRESS          0x10201470UL
#define SMN_DEV1_NBIF1RC_N6NBIO1_MARGINING_LANE_CNTL_ADDRESS          0x10401470UL
#define SMN_DEV1_NBIF1RC_N7NBIO0_MARGINING_LANE_CNTL_ADDRESS          0x10201474UL
#define SMN_DEV1_NBIF1RC_N7NBIO1_MARGINING_LANE_CNTL_ADDRESS          0x10401474UL
#define SMN_DEV1_NBIF1RC_N8NBIO0_MARGINING_LANE_CNTL_ADDRESS          0x10201478UL
#define SMN_DEV1_NBIF1RC_N8NBIO1_MARGINING_LANE_CNTL_ADDRESS          0x10401478UL
#define SMN_DEV1_NBIF1RC_N9NBIO0_MARGINING_LANE_CNTL_ADDRESS          0x1020147cUL
#define SMN_DEV1_NBIF1RC_N9NBIO1_MARGINING_LANE_CNTL_ADDRESS          0x1040147cUL


/***********************************************************
* Register Name : MARGINING_LANE_STATUS
************************************************************/

#define MARGINING_LANE_STATUS_RECEIVER_NUMBER_STATUS_OFFSET    0
#define MARGINING_LANE_STATUS_RECEIVER_NUMBER_STATUS_MASK      0x7

#define MARGINING_LANE_STATUS_MARGIN_TYPE_STATUS_OFFSET        3
#define MARGINING_LANE_STATUS_MARGIN_TYPE_STATUS_MASK          0x38

#define MARGINING_LANE_STATUS_USAGE_MODEL_STATUS_OFFSET        6
#define MARGINING_LANE_STATUS_USAGE_MODEL_STATUS_MASK          0x40

#define MARGINING_LANE_STATUS_Reserved_7_7_OFFSET              7
#define MARGINING_LANE_STATUS_Reserved_7_7_MASK                0x80

#define MARGINING_LANE_STATUS_MARGIN_PAYLOAD_STATUS_OFFSET     8
#define MARGINING_LANE_STATUS_MARGIN_PAYLOAD_STATUS_MASK       0xff00

typedef union {
  struct {
    UINT16                              RECEIVER_NUMBER_STATUS:3;
    UINT16                                  MARGIN_TYPE_STATUS:3;
    UINT16                                  USAGE_MODEL_STATUS:1;
    UINT16                                        Reserved_7_7:1;
    UINT16                               MARGIN_PAYLOAD_STATUS:8;
  } Field;
  UINT16 Value;
} MARGINING_LANE_STATUS_NBIFRCCFG_STRUCT;

#define PCICFG_NBIFRCCFG_MARGINING_LANE_STATUS_OFFSET               0x45a
#define SMN_DEV0_NBIF0RC_N0NBIO0_MARGINING_LANE_STATUS_ADDRESS        0x1010045aUL
#define SMN_DEV0_NBIF0RC_N0NBIO1_MARGINING_LANE_STATUS_ADDRESS        0x1030045aUL
#define SMN_DEV0_NBIF0RC_N10NBIO0_MARGINING_LANE_STATUS_ADDRESS       0x10100482UL
#define SMN_DEV0_NBIF0RC_N10NBIO1_MARGINING_LANE_STATUS_ADDRESS       0x10300482UL
#define SMN_DEV0_NBIF0RC_N11NBIO0_MARGINING_LANE_STATUS_ADDRESS       0x10100486UL
#define SMN_DEV0_NBIF0RC_N11NBIO1_MARGINING_LANE_STATUS_ADDRESS       0x10300486UL
#define SMN_DEV0_NBIF0RC_N12NBIO0_MARGINING_LANE_STATUS_ADDRESS       0x1010048aUL
#define SMN_DEV0_NBIF0RC_N12NBIO1_MARGINING_LANE_STATUS_ADDRESS       0x1030048aUL
#define SMN_DEV0_NBIF0RC_N13NBIO0_MARGINING_LANE_STATUS_ADDRESS       0x1010048eUL
#define SMN_DEV0_NBIF0RC_N13NBIO1_MARGINING_LANE_STATUS_ADDRESS       0x1030048eUL
#define SMN_DEV0_NBIF0RC_N14NBIO0_MARGINING_LANE_STATUS_ADDRESS       0x10100492UL
#define SMN_DEV0_NBIF0RC_N14NBIO1_MARGINING_LANE_STATUS_ADDRESS       0x10300492UL
#define SMN_DEV0_NBIF0RC_N15NBIO0_MARGINING_LANE_STATUS_ADDRESS       0x10100496UL
#define SMN_DEV0_NBIF0RC_N15NBIO1_MARGINING_LANE_STATUS_ADDRESS       0x10300496UL
#define SMN_DEV0_NBIF0RC_N1NBIO0_MARGINING_LANE_STATUS_ADDRESS        0x1010045eUL
#define SMN_DEV0_NBIF0RC_N1NBIO1_MARGINING_LANE_STATUS_ADDRESS        0x1030045eUL
#define SMN_DEV0_NBIF0RC_N2NBIO0_MARGINING_LANE_STATUS_ADDRESS        0x10100462UL
#define SMN_DEV0_NBIF0RC_N2NBIO1_MARGINING_LANE_STATUS_ADDRESS        0x10300462UL
#define SMN_DEV0_NBIF0RC_N3NBIO0_MARGINING_LANE_STATUS_ADDRESS        0x10100466UL
#define SMN_DEV0_NBIF0RC_N3NBIO1_MARGINING_LANE_STATUS_ADDRESS        0x10300466UL
#define SMN_DEV0_NBIF0RC_N4NBIO0_MARGINING_LANE_STATUS_ADDRESS        0x1010046aUL
#define SMN_DEV0_NBIF0RC_N4NBIO1_MARGINING_LANE_STATUS_ADDRESS        0x1030046aUL
#define SMN_DEV0_NBIF0RC_N5NBIO0_MARGINING_LANE_STATUS_ADDRESS        0x1010046eUL
#define SMN_DEV0_NBIF0RC_N5NBIO1_MARGINING_LANE_STATUS_ADDRESS        0x1030046eUL
#define SMN_DEV0_NBIF0RC_N6NBIO0_MARGINING_LANE_STATUS_ADDRESS        0x10100472UL
#define SMN_DEV0_NBIF0RC_N6NBIO1_MARGINING_LANE_STATUS_ADDRESS        0x10300472UL
#define SMN_DEV0_NBIF0RC_N7NBIO0_MARGINING_LANE_STATUS_ADDRESS        0x10100476UL
#define SMN_DEV0_NBIF0RC_N7NBIO1_MARGINING_LANE_STATUS_ADDRESS        0x10300476UL
#define SMN_DEV0_NBIF0RC_N8NBIO0_MARGINING_LANE_STATUS_ADDRESS        0x1010047aUL
#define SMN_DEV0_NBIF0RC_N8NBIO1_MARGINING_LANE_STATUS_ADDRESS        0x1030047aUL
#define SMN_DEV0_NBIF0RC_N9NBIO0_MARGINING_LANE_STATUS_ADDRESS        0x1010047eUL
#define SMN_DEV0_NBIF0RC_N9NBIO1_MARGINING_LANE_STATUS_ADDRESS        0x1030047eUL
#define SMN_DEV0_NBIF1RC_N0NBIO0_MARGINING_LANE_STATUS_ADDRESS        0x1020045aUL
#define SMN_DEV0_NBIF1RC_N0NBIO1_MARGINING_LANE_STATUS_ADDRESS        0x1040045aUL
#define SMN_DEV0_NBIF1RC_N10NBIO0_MARGINING_LANE_STATUS_ADDRESS       0x10200482UL
#define SMN_DEV0_NBIF1RC_N10NBIO1_MARGINING_LANE_STATUS_ADDRESS       0x10400482UL
#define SMN_DEV0_NBIF1RC_N11NBIO0_MARGINING_LANE_STATUS_ADDRESS       0x10200486UL
#define SMN_DEV0_NBIF1RC_N11NBIO1_MARGINING_LANE_STATUS_ADDRESS       0x10400486UL
#define SMN_DEV0_NBIF1RC_N12NBIO0_MARGINING_LANE_STATUS_ADDRESS       0x1020048aUL
#define SMN_DEV0_NBIF1RC_N12NBIO1_MARGINING_LANE_STATUS_ADDRESS       0x1040048aUL
#define SMN_DEV0_NBIF1RC_N13NBIO0_MARGINING_LANE_STATUS_ADDRESS       0x1020048eUL
#define SMN_DEV0_NBIF1RC_N13NBIO1_MARGINING_LANE_STATUS_ADDRESS       0x1040048eUL
#define SMN_DEV0_NBIF1RC_N14NBIO0_MARGINING_LANE_STATUS_ADDRESS       0x10200492UL
#define SMN_DEV0_NBIF1RC_N14NBIO1_MARGINING_LANE_STATUS_ADDRESS       0x10400492UL
#define SMN_DEV0_NBIF1RC_N15NBIO0_MARGINING_LANE_STATUS_ADDRESS       0x10200496UL
#define SMN_DEV0_NBIF1RC_N15NBIO1_MARGINING_LANE_STATUS_ADDRESS       0x10400496UL
#define SMN_DEV0_NBIF1RC_N1NBIO0_MARGINING_LANE_STATUS_ADDRESS        0x1020045eUL
#define SMN_DEV0_NBIF1RC_N1NBIO1_MARGINING_LANE_STATUS_ADDRESS        0x1040045eUL
#define SMN_DEV0_NBIF1RC_N2NBIO0_MARGINING_LANE_STATUS_ADDRESS        0x10200462UL
#define SMN_DEV0_NBIF1RC_N2NBIO1_MARGINING_LANE_STATUS_ADDRESS        0x10400462UL
#define SMN_DEV0_NBIF1RC_N3NBIO0_MARGINING_LANE_STATUS_ADDRESS        0x10200466UL
#define SMN_DEV0_NBIF1RC_N3NBIO1_MARGINING_LANE_STATUS_ADDRESS        0x10400466UL
#define SMN_DEV0_NBIF1RC_N4NBIO0_MARGINING_LANE_STATUS_ADDRESS        0x1020046aUL
#define SMN_DEV0_NBIF1RC_N4NBIO1_MARGINING_LANE_STATUS_ADDRESS        0x1040046aUL
#define SMN_DEV0_NBIF1RC_N5NBIO0_MARGINING_LANE_STATUS_ADDRESS        0x1020046eUL
#define SMN_DEV0_NBIF1RC_N5NBIO1_MARGINING_LANE_STATUS_ADDRESS        0x1040046eUL
#define SMN_DEV0_NBIF1RC_N6NBIO0_MARGINING_LANE_STATUS_ADDRESS        0x10200472UL
#define SMN_DEV0_NBIF1RC_N6NBIO1_MARGINING_LANE_STATUS_ADDRESS        0x10400472UL
#define SMN_DEV0_NBIF1RC_N7NBIO0_MARGINING_LANE_STATUS_ADDRESS        0x10200476UL
#define SMN_DEV0_NBIF1RC_N7NBIO1_MARGINING_LANE_STATUS_ADDRESS        0x10400476UL
#define SMN_DEV0_NBIF1RC_N8NBIO0_MARGINING_LANE_STATUS_ADDRESS        0x1020047aUL
#define SMN_DEV0_NBIF1RC_N8NBIO1_MARGINING_LANE_STATUS_ADDRESS        0x1040047aUL
#define SMN_DEV0_NBIF1RC_N9NBIO0_MARGINING_LANE_STATUS_ADDRESS        0x1020047eUL
#define SMN_DEV0_NBIF1RC_N9NBIO1_MARGINING_LANE_STATUS_ADDRESS        0x1040047eUL
#define SMN_DEV1_NBIF0RC_N0NBIO0_MARGINING_LANE_STATUS_ADDRESS        0x1010145aUL
#define SMN_DEV1_NBIF0RC_N0NBIO1_MARGINING_LANE_STATUS_ADDRESS        0x1030145aUL
#define SMN_DEV1_NBIF0RC_N10NBIO0_MARGINING_LANE_STATUS_ADDRESS       0x10101482UL
#define SMN_DEV1_NBIF0RC_N10NBIO1_MARGINING_LANE_STATUS_ADDRESS       0x10301482UL
#define SMN_DEV1_NBIF0RC_N11NBIO0_MARGINING_LANE_STATUS_ADDRESS       0x10101486UL
#define SMN_DEV1_NBIF0RC_N11NBIO1_MARGINING_LANE_STATUS_ADDRESS       0x10301486UL
#define SMN_DEV1_NBIF0RC_N12NBIO0_MARGINING_LANE_STATUS_ADDRESS       0x1010148aUL
#define SMN_DEV1_NBIF0RC_N12NBIO1_MARGINING_LANE_STATUS_ADDRESS       0x1030148aUL
#define SMN_DEV1_NBIF0RC_N13NBIO0_MARGINING_LANE_STATUS_ADDRESS       0x1010148eUL
#define SMN_DEV1_NBIF0RC_N13NBIO1_MARGINING_LANE_STATUS_ADDRESS       0x1030148eUL
#define SMN_DEV1_NBIF0RC_N14NBIO0_MARGINING_LANE_STATUS_ADDRESS       0x10101492UL
#define SMN_DEV1_NBIF0RC_N14NBIO1_MARGINING_LANE_STATUS_ADDRESS       0x10301492UL
#define SMN_DEV1_NBIF0RC_N15NBIO0_MARGINING_LANE_STATUS_ADDRESS       0x10101496UL
#define SMN_DEV1_NBIF0RC_N15NBIO1_MARGINING_LANE_STATUS_ADDRESS       0x10301496UL
#define SMN_DEV1_NBIF0RC_N1NBIO0_MARGINING_LANE_STATUS_ADDRESS        0x1010145eUL
#define SMN_DEV1_NBIF0RC_N1NBIO1_MARGINING_LANE_STATUS_ADDRESS        0x1030145eUL
#define SMN_DEV1_NBIF0RC_N2NBIO0_MARGINING_LANE_STATUS_ADDRESS        0x10101462UL
#define SMN_DEV1_NBIF0RC_N2NBIO1_MARGINING_LANE_STATUS_ADDRESS        0x10301462UL
#define SMN_DEV1_NBIF0RC_N3NBIO0_MARGINING_LANE_STATUS_ADDRESS        0x10101466UL
#define SMN_DEV1_NBIF0RC_N3NBIO1_MARGINING_LANE_STATUS_ADDRESS        0x10301466UL
#define SMN_DEV1_NBIF0RC_N4NBIO0_MARGINING_LANE_STATUS_ADDRESS        0x1010146aUL
#define SMN_DEV1_NBIF0RC_N4NBIO1_MARGINING_LANE_STATUS_ADDRESS        0x1030146aUL
#define SMN_DEV1_NBIF0RC_N5NBIO0_MARGINING_LANE_STATUS_ADDRESS        0x1010146eUL
#define SMN_DEV1_NBIF0RC_N5NBIO1_MARGINING_LANE_STATUS_ADDRESS        0x1030146eUL
#define SMN_DEV1_NBIF0RC_N6NBIO0_MARGINING_LANE_STATUS_ADDRESS        0x10101472UL
#define SMN_DEV1_NBIF0RC_N6NBIO1_MARGINING_LANE_STATUS_ADDRESS        0x10301472UL
#define SMN_DEV1_NBIF0RC_N7NBIO0_MARGINING_LANE_STATUS_ADDRESS        0x10101476UL
#define SMN_DEV1_NBIF0RC_N7NBIO1_MARGINING_LANE_STATUS_ADDRESS        0x10301476UL
#define SMN_DEV1_NBIF0RC_N8NBIO0_MARGINING_LANE_STATUS_ADDRESS        0x1010147aUL
#define SMN_DEV1_NBIF0RC_N8NBIO1_MARGINING_LANE_STATUS_ADDRESS        0x1030147aUL
#define SMN_DEV1_NBIF0RC_N9NBIO0_MARGINING_LANE_STATUS_ADDRESS        0x1010147eUL
#define SMN_DEV1_NBIF0RC_N9NBIO1_MARGINING_LANE_STATUS_ADDRESS        0x1030147eUL
#define SMN_DEV1_NBIF1RC_N0NBIO0_MARGINING_LANE_STATUS_ADDRESS        0x1020145aUL
#define SMN_DEV1_NBIF1RC_N0NBIO1_MARGINING_LANE_STATUS_ADDRESS        0x1040145aUL
#define SMN_DEV1_NBIF1RC_N10NBIO0_MARGINING_LANE_STATUS_ADDRESS       0x10201482UL
#define SMN_DEV1_NBIF1RC_N10NBIO1_MARGINING_LANE_STATUS_ADDRESS       0x10401482UL
#define SMN_DEV1_NBIF1RC_N11NBIO0_MARGINING_LANE_STATUS_ADDRESS       0x10201486UL
#define SMN_DEV1_NBIF1RC_N11NBIO1_MARGINING_LANE_STATUS_ADDRESS       0x10401486UL
#define SMN_DEV1_NBIF1RC_N12NBIO0_MARGINING_LANE_STATUS_ADDRESS       0x1020148aUL
#define SMN_DEV1_NBIF1RC_N12NBIO1_MARGINING_LANE_STATUS_ADDRESS       0x1040148aUL
#define SMN_DEV1_NBIF1RC_N13NBIO0_MARGINING_LANE_STATUS_ADDRESS       0x1020148eUL
#define SMN_DEV1_NBIF1RC_N13NBIO1_MARGINING_LANE_STATUS_ADDRESS       0x1040148eUL
#define SMN_DEV1_NBIF1RC_N14NBIO0_MARGINING_LANE_STATUS_ADDRESS       0x10201492UL
#define SMN_DEV1_NBIF1RC_N14NBIO1_MARGINING_LANE_STATUS_ADDRESS       0x10401492UL
#define SMN_DEV1_NBIF1RC_N15NBIO0_MARGINING_LANE_STATUS_ADDRESS       0x10201496UL
#define SMN_DEV1_NBIF1RC_N15NBIO1_MARGINING_LANE_STATUS_ADDRESS       0x10401496UL
#define SMN_DEV1_NBIF1RC_N1NBIO0_MARGINING_LANE_STATUS_ADDRESS        0x1020145eUL
#define SMN_DEV1_NBIF1RC_N1NBIO1_MARGINING_LANE_STATUS_ADDRESS        0x1040145eUL
#define SMN_DEV1_NBIF1RC_N2NBIO0_MARGINING_LANE_STATUS_ADDRESS        0x10201462UL
#define SMN_DEV1_NBIF1RC_N2NBIO1_MARGINING_LANE_STATUS_ADDRESS        0x10401462UL
#define SMN_DEV1_NBIF1RC_N3NBIO0_MARGINING_LANE_STATUS_ADDRESS        0x10201466UL
#define SMN_DEV1_NBIF1RC_N3NBIO1_MARGINING_LANE_STATUS_ADDRESS        0x10401466UL
#define SMN_DEV1_NBIF1RC_N4NBIO0_MARGINING_LANE_STATUS_ADDRESS        0x1020146aUL
#define SMN_DEV1_NBIF1RC_N4NBIO1_MARGINING_LANE_STATUS_ADDRESS        0x1040146aUL
#define SMN_DEV1_NBIF1RC_N5NBIO0_MARGINING_LANE_STATUS_ADDRESS        0x1020146eUL
#define SMN_DEV1_NBIF1RC_N5NBIO1_MARGINING_LANE_STATUS_ADDRESS        0x1040146eUL
#define SMN_DEV1_NBIF1RC_N6NBIO0_MARGINING_LANE_STATUS_ADDRESS        0x10201472UL
#define SMN_DEV1_NBIF1RC_N6NBIO1_MARGINING_LANE_STATUS_ADDRESS        0x10401472UL
#define SMN_DEV1_NBIF1RC_N7NBIO0_MARGINING_LANE_STATUS_ADDRESS        0x10201476UL
#define SMN_DEV1_NBIF1RC_N7NBIO1_MARGINING_LANE_STATUS_ADDRESS        0x10401476UL
#define SMN_DEV1_NBIF1RC_N8NBIO0_MARGINING_LANE_STATUS_ADDRESS        0x1020147aUL
#define SMN_DEV1_NBIF1RC_N8NBIO1_MARGINING_LANE_STATUS_ADDRESS        0x1040147aUL
#define SMN_DEV1_NBIF1RC_N9NBIO0_MARGINING_LANE_STATUS_ADDRESS        0x1020147eUL
#define SMN_DEV1_NBIF1RC_N9NBIO1_MARGINING_LANE_STATUS_ADDRESS        0x1040147eUL


/***********************************************************
* Register Name : MARGINING_PORT_CAP
************************************************************/

#define MARGINING_PORT_CAP_MARGINING_USES_SOFTWARE_OFFSET      0
#define MARGINING_PORT_CAP_MARGINING_USES_SOFTWARE_MASK        0x1

#define MARGINING_PORT_CAP_Reserved_15_1_OFFSET                1
#define MARGINING_PORT_CAP_Reserved_15_1_MASK                  0xfffe

typedef union {
  struct {
    UINT16                             MARGINING_USES_SOFTWARE:1;
    UINT16                                       Reserved_15_1:15;
  } Field;
  UINT16 Value;
} MARGINING_PORT_CAP_NBIFRCCFG_STRUCT;

#define PCICFG_NBIFRCCFG_MARGINING_PORT_CAP_OFFSET                  0x454
#define SMN_DEV0_NBIF0RCNBIO0_MARGINING_PORT_CAP_ADDRESS              0x10100454UL
#define SMN_DEV0_NBIF0RCNBIO1_MARGINING_PORT_CAP_ADDRESS              0x10300454UL
#define SMN_DEV0_NBIF1RCNBIO0_MARGINING_PORT_CAP_ADDRESS              0x10200454UL
#define SMN_DEV0_NBIF1RCNBIO1_MARGINING_PORT_CAP_ADDRESS              0x10400454UL
#define SMN_DEV1_NBIF0RCNBIO0_MARGINING_PORT_CAP_ADDRESS              0x10101454UL
#define SMN_DEV1_NBIF0RCNBIO1_MARGINING_PORT_CAP_ADDRESS              0x10301454UL
#define SMN_DEV1_NBIF1RCNBIO0_MARGINING_PORT_CAP_ADDRESS              0x10201454UL
#define SMN_DEV1_NBIF1RCNBIO1_MARGINING_PORT_CAP_ADDRESS              0x10401454UL


/***********************************************************
* Register Name : MARGINING_PORT_STATUS
************************************************************/

#define MARGINING_PORT_STATUS_MARGINING_READY_OFFSET           0
#define MARGINING_PORT_STATUS_MARGINING_READY_MASK             0x1

#define MARGINING_PORT_STATUS_MARGINING_SOFTWARE_READY_OFFSET  1
#define MARGINING_PORT_STATUS_MARGINING_SOFTWARE_READY_MASK    0x2

#define MARGINING_PORT_STATUS_Reserved_15_2_OFFSET             2
#define MARGINING_PORT_STATUS_Reserved_15_2_MASK               0xfffc

typedef union {
  struct {
    UINT16                                     MARGINING_READY:1;
    UINT16                            MARGINING_SOFTWARE_READY:1;
    UINT16                                       Reserved_15_2:14;
  } Field;
  UINT16 Value;
} MARGINING_PORT_STATUS_NBIFRCCFG_STRUCT;

#define PCICFG_NBIFRCCFG_MARGINING_PORT_STATUS_OFFSET               0x456
#define SMN_DEV0_NBIF0RCNBIO0_MARGINING_PORT_STATUS_ADDRESS           0x10100456UL
#define SMN_DEV0_NBIF0RCNBIO1_MARGINING_PORT_STATUS_ADDRESS           0x10300456UL
#define SMN_DEV0_NBIF1RCNBIO0_MARGINING_PORT_STATUS_ADDRESS           0x10200456UL
#define SMN_DEV0_NBIF1RCNBIO1_MARGINING_PORT_STATUS_ADDRESS           0x10400456UL
#define SMN_DEV1_NBIF0RCNBIO0_MARGINING_PORT_STATUS_ADDRESS           0x10101456UL
#define SMN_DEV1_NBIF0RCNBIO1_MARGINING_PORT_STATUS_ADDRESS           0x10301456UL
#define SMN_DEV1_NBIF1RCNBIO0_MARGINING_PORT_STATUS_ADDRESS           0x10201456UL
#define SMN_DEV1_NBIF1RCNBIO1_MARGINING_PORT_STATUS_ADDRESS           0x10401456UL


/***********************************************************
* Register Name : MEM_BASE_LIMIT
************************************************************/

#define MEM_BASE_LIMIT_MEM_BASE_TYPE_OFFSET                    0
#define MEM_BASE_LIMIT_MEM_BASE_TYPE_MASK                      0xf

#define MEM_BASE_LIMIT_MEM_BASE_31_20_OFFSET                   4
#define MEM_BASE_LIMIT_MEM_BASE_31_20_MASK                     0xfff0

#define MEM_BASE_LIMIT_MEM_LIMIT_TYPE_OFFSET                   16
#define MEM_BASE_LIMIT_MEM_LIMIT_TYPE_MASK                     0xf0000

#define MEM_BASE_LIMIT_MEM_LIMIT_31_20_OFFSET                  20
#define MEM_BASE_LIMIT_MEM_LIMIT_31_20_MASK                    0xfff00000

typedef union {
  struct {
    UINT32                                       MEM_BASE_TYPE:4;
    UINT32                                      MEM_BASE_31_20:12;
    UINT32                                      MEM_LIMIT_TYPE:4;
    UINT32                                     MEM_LIMIT_31_20:12;
  } Field;
  UINT32 Value;
} MEM_BASE_LIMIT_NBIFRCCFG_STRUCT;

#define PCICFG_NBIFRCCFG_MEM_BASE_LIMIT_OFFSET                      0x20
#define SMN_DEV0_NBIF0RCNBIO0_MEM_BASE_LIMIT_ADDRESS                  0x10100020UL
#define SMN_DEV0_NBIF0RCNBIO1_MEM_BASE_LIMIT_ADDRESS                  0x10300020UL
#define SMN_DEV0_NBIF1RCNBIO0_MEM_BASE_LIMIT_ADDRESS                  0x10200020UL
#define SMN_DEV0_NBIF1RCNBIO1_MEM_BASE_LIMIT_ADDRESS                  0x10400020UL
#define SMN_DEV1_NBIF0RCNBIO0_MEM_BASE_LIMIT_ADDRESS                  0x10101020UL
#define SMN_DEV1_NBIF0RCNBIO1_MEM_BASE_LIMIT_ADDRESS                  0x10301020UL
#define SMN_DEV1_NBIF1RCNBIO0_MEM_BASE_LIMIT_ADDRESS                  0x10201020UL
#define SMN_DEV1_NBIF1RCNBIO1_MEM_BASE_LIMIT_ADDRESS                  0x10401020UL


/***********************************************************
* Register Name : MSI_CAP_LIST
************************************************************/

#define MSI_CAP_LIST_CAP_ID_OFFSET                             0
#define MSI_CAP_LIST_CAP_ID_MASK                               0xff

#define MSI_CAP_LIST_NEXT_PTR_OFFSET                           8
#define MSI_CAP_LIST_NEXT_PTR_MASK                             0xff00

typedef union {
  struct {
    UINT16                                              CAP_ID:8;
    UINT16                                            NEXT_PTR:8;
  } Field;
  UINT16 Value;
} MSI_CAP_LIST_NBIFRCCFG_STRUCT;

#define PCICFG_NBIFRCCFG_MSI_CAP_LIST_OFFSET                        0xa0
#define SMN_DEV0_NBIF0RCNBIO0_MSI_CAP_LIST_ADDRESS                    0x101000a0UL
#define SMN_DEV0_NBIF0RCNBIO1_MSI_CAP_LIST_ADDRESS                    0x103000a0UL
#define SMN_DEV0_NBIF1RCNBIO0_MSI_CAP_LIST_ADDRESS                    0x102000a0UL
#define SMN_DEV0_NBIF1RCNBIO1_MSI_CAP_LIST_ADDRESS                    0x104000a0UL
#define SMN_DEV1_NBIF0RCNBIO0_MSI_CAP_LIST_ADDRESS                    0x101010a0UL
#define SMN_DEV1_NBIF0RCNBIO1_MSI_CAP_LIST_ADDRESS                    0x103010a0UL
#define SMN_DEV1_NBIF1RCNBIO0_MSI_CAP_LIST_ADDRESS                    0x102010a0UL
#define SMN_DEV1_NBIF1RCNBIO1_MSI_CAP_LIST_ADDRESS                    0x104010a0UL


/***********************************************************
* Register Name : MSI_EXT_MSG_DATA
************************************************************/

#define MSI_EXT_MSG_DATA_MSI_EXT_DATA_OFFSET                   0
#define MSI_EXT_MSG_DATA_MSI_EXT_DATA_MASK                     0xffff

typedef union {
  struct {
    UINT16                                        MSI_EXT_DATA:16;
  } Field;
  UINT16 Value;
} MSI_EXT_MSG_DATA_NBIFRCCFG_STRUCT;

#define PCICFG_NBIFRCCFG_MSI_EXT_MSG_DATA_OFFSET                    0x0
#define SMN_DEV0_NBIF0RCNBIO0_MSI_EXT_MSG_DATA_ADDRESS                0x10100000UL
#define SMN_DEV0_NBIF0RCNBIO1_MSI_EXT_MSG_DATA_ADDRESS                0x10300000UL
#define SMN_DEV0_NBIF1RCNBIO0_MSI_EXT_MSG_DATA_ADDRESS                0x10200000UL
#define SMN_DEV0_NBIF1RCNBIO1_MSI_EXT_MSG_DATA_ADDRESS                0x10400000UL
#define SMN_DEV1_NBIF0RCNBIO0_MSI_EXT_MSG_DATA_ADDRESS                0x10100000UL
#define SMN_DEV1_NBIF0RCNBIO1_MSI_EXT_MSG_DATA_ADDRESS                0x10300000UL
#define SMN_DEV1_NBIF1RCNBIO0_MSI_EXT_MSG_DATA_ADDRESS                0x10200000UL
#define SMN_DEV1_NBIF1RCNBIO1_MSI_EXT_MSG_DATA_ADDRESS                0x10400000UL


/***********************************************************
* Register Name : MSI_EXT_MSG_DATA_64
************************************************************/

#define MSI_EXT_MSG_DATA_64_MSI_EXT_DATA_64_OFFSET             0
#define MSI_EXT_MSG_DATA_64_MSI_EXT_DATA_64_MASK               0xffff

typedef union {
  struct {
    UINT16                                     MSI_EXT_DATA_64:16;
  } Field;
  UINT16 Value;
} MSI_EXT_MSG_DATA_64_NBIFRCCFG_STRUCT;

#define PCICFG_NBIFRCCFG_MSI_EXT_MSG_DATA_64_OFFSET                 0x1
#define SMN_DEV0_NBIF0RCNBIO0_MSI_EXT_MSG_DATA_64_ADDRESS             0x10100001UL
#define SMN_DEV0_NBIF0RCNBIO1_MSI_EXT_MSG_DATA_64_ADDRESS             0x10300001UL
#define SMN_DEV0_NBIF1RCNBIO0_MSI_EXT_MSG_DATA_64_ADDRESS             0x10200001UL
#define SMN_DEV0_NBIF1RCNBIO1_MSI_EXT_MSG_DATA_64_ADDRESS             0x10400001UL
#define SMN_DEV1_NBIF0RCNBIO0_MSI_EXT_MSG_DATA_64_ADDRESS             0x10100001UL
#define SMN_DEV1_NBIF0RCNBIO1_MSI_EXT_MSG_DATA_64_ADDRESS             0x10300001UL
#define SMN_DEV1_NBIF1RCNBIO0_MSI_EXT_MSG_DATA_64_ADDRESS             0x10200001UL
#define SMN_DEV1_NBIF1RCNBIO1_MSI_EXT_MSG_DATA_64_ADDRESS             0x10400001UL


/***********************************************************
* Register Name : MSI_MSG_ADDR_HI
************************************************************/

#define MSI_MSG_ADDR_HI_MSI_MSG_ADDR_HI_OFFSET                 0
#define MSI_MSG_ADDR_HI_MSI_MSG_ADDR_HI_MASK                   0xffffffff

typedef union {
  struct {
    UINT32                                     MSI_MSG_ADDR_HI:32;
  } Field;
  UINT32 Value;
} MSI_MSG_ADDR_HI_NBIFRCCFG_STRUCT;

#define PCICFG_NBIFRCCFG_MSI_MSG_ADDR_HI_OFFSET                     0x1
#define SMN_DEV0_NBIF0RCNBIO0_MSI_MSG_ADDR_HI_ADDRESS                 0x10100001UL
#define SMN_DEV0_NBIF0RCNBIO1_MSI_MSG_ADDR_HI_ADDRESS                 0x10300001UL
#define SMN_DEV0_NBIF1RCNBIO0_MSI_MSG_ADDR_HI_ADDRESS                 0x10200001UL
#define SMN_DEV0_NBIF1RCNBIO1_MSI_MSG_ADDR_HI_ADDRESS                 0x10400001UL
#define SMN_DEV1_NBIF0RCNBIO0_MSI_MSG_ADDR_HI_ADDRESS                 0x10100001UL
#define SMN_DEV1_NBIF0RCNBIO1_MSI_MSG_ADDR_HI_ADDRESS                 0x10300001UL
#define SMN_DEV1_NBIF1RCNBIO0_MSI_MSG_ADDR_HI_ADDRESS                 0x10200001UL
#define SMN_DEV1_NBIF1RCNBIO1_MSI_MSG_ADDR_HI_ADDRESS                 0x10400001UL


/***********************************************************
* Register Name : MSI_MSG_ADDR_LO
************************************************************/

#define MSI_MSG_ADDR_LO_Reserved_1_0_OFFSET                    0
#define MSI_MSG_ADDR_LO_Reserved_1_0_MASK                      0x3

#define MSI_MSG_ADDR_LO_MSI_MSG_ADDR_LO_OFFSET                 2
#define MSI_MSG_ADDR_LO_MSI_MSG_ADDR_LO_MASK                   0xfffffffc

typedef union {
  struct {
    UINT32                                        Reserved_1_0:2;
    UINT32                                     MSI_MSG_ADDR_LO:30;
  } Field;
  UINT32 Value;
} MSI_MSG_ADDR_LO_NBIFRCCFG_STRUCT;

#define PCICFG_NBIFRCCFG_MSI_MSG_ADDR_LO_OFFSET                     0xa4
#define SMN_DEV0_NBIF0RCNBIO0_MSI_MSG_ADDR_LO_ADDRESS                 0x101000a4UL
#define SMN_DEV0_NBIF0RCNBIO1_MSI_MSG_ADDR_LO_ADDRESS                 0x103000a4UL
#define SMN_DEV0_NBIF1RCNBIO0_MSI_MSG_ADDR_LO_ADDRESS                 0x102000a4UL
#define SMN_DEV0_NBIF1RCNBIO1_MSI_MSG_ADDR_LO_ADDRESS                 0x104000a4UL
#define SMN_DEV1_NBIF0RCNBIO0_MSI_MSG_ADDR_LO_ADDRESS                 0x101010a4UL
#define SMN_DEV1_NBIF0RCNBIO1_MSI_MSG_ADDR_LO_ADDRESS                 0x103010a4UL
#define SMN_DEV1_NBIF1RCNBIO0_MSI_MSG_ADDR_LO_ADDRESS                 0x102010a4UL
#define SMN_DEV1_NBIF1RCNBIO1_MSI_MSG_ADDR_LO_ADDRESS                 0x104010a4UL


/***********************************************************
* Register Name : MSI_MSG_CNTL
************************************************************/

#define MSI_MSG_CNTL_MSI_EN_OFFSET                             0
#define MSI_MSG_CNTL_MSI_EN_MASK                               0x1

#define MSI_MSG_CNTL_MSI_MULTI_CAP_OFFSET                      1
#define MSI_MSG_CNTL_MSI_MULTI_CAP_MASK                        0xe

#define MSI_MSG_CNTL_MSI_MULTI_EN_OFFSET                       4
#define MSI_MSG_CNTL_MSI_MULTI_EN_MASK                         0x70

#define MSI_MSG_CNTL_MSI_64BIT_OFFSET                          7
#define MSI_MSG_CNTL_MSI_64BIT_MASK                            0x80

#define MSI_MSG_CNTL_MSI_PERVECTOR_MASKING_CAP_OFFSET          8
#define MSI_MSG_CNTL_MSI_PERVECTOR_MASKING_CAP_MASK            0x100

#define MSI_MSG_CNTL_MSI_EXT_MSG_DATA_CAP_OFFSET               9
#define MSI_MSG_CNTL_MSI_EXT_MSG_DATA_CAP_MASK                 0x200

#define MSI_MSG_CNTL_MSI_EXT_MSG_DATA_EN_OFFSET                10
#define MSI_MSG_CNTL_MSI_EXT_MSG_DATA_EN_MASK                  0x400

#define MSI_MSG_CNTL_Reserved_15_11_OFFSET                     11
#define MSI_MSG_CNTL_Reserved_15_11_MASK                       0xf800

typedef union {
  struct {
    UINT16                                              MSI_EN:1;
    UINT16                                       MSI_MULTI_CAP:3;
    UINT16                                        MSI_MULTI_EN:3;
    UINT16                                           MSI_64BIT:1;
    UINT16                           MSI_PERVECTOR_MASKING_CAP:1;
    UINT16                                MSI_EXT_MSG_DATA_CAP:1;
    UINT16                                 MSI_EXT_MSG_DATA_EN:1;
    UINT16                                      Reserved_15_11:5;
  } Field;
  UINT16 Value;
} MSI_MSG_CNTL_NBIFRCCFG_STRUCT;

#define PCICFG_NBIFRCCFG_MSI_MSG_CNTL_OFFSET                        0xa2
#define SMN_DEV0_NBIF0RCNBIO0_MSI_MSG_CNTL_ADDRESS                    0x101000a2UL
#define SMN_DEV0_NBIF0RCNBIO1_MSI_MSG_CNTL_ADDRESS                    0x103000a2UL
#define SMN_DEV0_NBIF1RCNBIO0_MSI_MSG_CNTL_ADDRESS                    0x102000a2UL
#define SMN_DEV0_NBIF1RCNBIO1_MSI_MSG_CNTL_ADDRESS                    0x104000a2UL
#define SMN_DEV1_NBIF0RCNBIO0_MSI_MSG_CNTL_ADDRESS                    0x101010a2UL
#define SMN_DEV1_NBIF0RCNBIO1_MSI_MSG_CNTL_ADDRESS                    0x103010a2UL
#define SMN_DEV1_NBIF1RCNBIO0_MSI_MSG_CNTL_ADDRESS                    0x102010a2UL
#define SMN_DEV1_NBIF1RCNBIO1_MSI_MSG_CNTL_ADDRESS                    0x104010a2UL


/***********************************************************
* Register Name : MSI_MSG_DATA
************************************************************/

#define MSI_MSG_DATA_MSI_DATA_OFFSET                           0
#define MSI_MSG_DATA_MSI_DATA_MASK                             0xffff

typedef union {
  struct {
    UINT16                                            MSI_DATA:16;
  } Field;
  UINT16 Value;
} MSI_MSG_DATA_NBIFRCCFG_STRUCT;

#define PCICFG_NBIFRCCFG_MSI_MSG_DATA_OFFSET                        0x0
#define SMN_DEV0_NBIF0RCNBIO0_MSI_MSG_DATA_ADDRESS                    0x10100000UL
#define SMN_DEV0_NBIF0RCNBIO1_MSI_MSG_DATA_ADDRESS                    0x10300000UL
#define SMN_DEV0_NBIF1RCNBIO0_MSI_MSG_DATA_ADDRESS                    0x10200000UL
#define SMN_DEV0_NBIF1RCNBIO1_MSI_MSG_DATA_ADDRESS                    0x10400000UL
#define SMN_DEV1_NBIF0RCNBIO0_MSI_MSG_DATA_ADDRESS                    0x10100000UL
#define SMN_DEV1_NBIF0RCNBIO1_MSI_MSG_DATA_ADDRESS                    0x10300000UL
#define SMN_DEV1_NBIF1RCNBIO0_MSI_MSG_DATA_ADDRESS                    0x10200000UL
#define SMN_DEV1_NBIF1RCNBIO1_MSI_MSG_DATA_ADDRESS                    0x10400000UL


/***********************************************************
* Register Name : MSI_MSG_DATA_64
************************************************************/

#define MSI_MSG_DATA_64_MSI_DATA_64_OFFSET                     0
#define MSI_MSG_DATA_64_MSI_DATA_64_MASK                       0xffff

typedef union {
  struct {
    UINT16                                         MSI_DATA_64:16;
  } Field;
  UINT16 Value;
} MSI_MSG_DATA_64_NBIFRCCFG_STRUCT;

#define PCICFG_NBIFRCCFG_MSI_MSG_DATA_64_OFFSET                     0x1
#define SMN_DEV0_NBIF0RCNBIO0_MSI_MSG_DATA_64_ADDRESS                 0x10100001UL
#define SMN_DEV0_NBIF0RCNBIO1_MSI_MSG_DATA_64_ADDRESS                 0x10300001UL
#define SMN_DEV0_NBIF1RCNBIO0_MSI_MSG_DATA_64_ADDRESS                 0x10200001UL
#define SMN_DEV0_NBIF1RCNBIO1_MSI_MSG_DATA_64_ADDRESS                 0x10400001UL
#define SMN_DEV1_NBIF0RCNBIO0_MSI_MSG_DATA_64_ADDRESS                 0x10100001UL
#define SMN_DEV1_NBIF0RCNBIO1_MSI_MSG_DATA_64_ADDRESS                 0x10300001UL
#define SMN_DEV1_NBIF1RCNBIO0_MSI_MSG_DATA_64_ADDRESS                 0x10200001UL
#define SMN_DEV1_NBIF1RCNBIO1_MSI_MSG_DATA_64_ADDRESS                 0x10400001UL


/***********************************************************
* Register Name : PCIE_ACS_CAP
************************************************************/

#define NBIF_ACS_CAP_SOURCE_VALIDATION_OFFSET                  0
#define NBIF_ACS_CAP_SOURCE_VALIDATION_MASK                    0x1

#define NBIF_ACS_CAP_TRANSLATION_BLOCKING_OFFSET               1
#define NBIF_ACS_CAP_TRANSLATION_BLOCKING_MASK                 0x2

#define NBIF_ACS_CAP_P2P_REQUEST_REDIRECT_OFFSET               2
#define NBIF_ACS_CAP_P2P_REQUEST_REDIRECT_MASK                 0x4

#define NBIF_ACS_CAP_P2P_COMPLETION_REDIRECT_OFFSET            3
#define NBIF_ACS_CAP_P2P_COMPLETION_REDIRECT_MASK              0x8

#define NBIF_ACS_CAP_UPSTREAM_FORWARDING_OFFSET                4
#define NBIF_ACS_CAP_UPSTREAM_FORWARDING_MASK                  0x10

#define NBIF_ACS_CAP_P2P_EGRESS_CONTROL_OFFSET                 5
#define NBIF_ACS_CAP_P2P_EGRESS_CONTROL_MASK                   0x20

#define NBIF_ACS_CAP_DIRECT_TRANSLATED_P2P_OFFSET              6
#define NBIF_ACS_CAP_DIRECT_TRANSLATED_P2P_MASK                0x40

#define NBIF_ACS_CAP_ENHANCED_CAPABILITY_OFFSET                7
#define NBIF_ACS_CAP_ENHANCED_CAPABILITY_MASK                  0x80

#define NBIF_ACS_CAP_EGRESS_CONTROL_VECTOR_SIZE_OFFSET         8
#define NBIF_ACS_CAP_EGRESS_CONTROL_VECTOR_SIZE_MASK           0xff00

typedef union {
  struct {
    UINT16                                   SOURCE_VALIDATION:1;
    UINT16                                TRANSLATION_BLOCKING:1;
    UINT16                                P2P_REQUEST_REDIRECT:1;
    UINT16                             P2P_COMPLETION_REDIRECT:1;
    UINT16                                 UPSTREAM_FORWARDING:1;
    UINT16                                  P2P_EGRESS_CONTROL:1;
    UINT16                               DIRECT_TRANSLATED_P2P:1;
    UINT16                                 ENHANCED_CAPABILITY:1;
    UINT16                          EGRESS_CONTROL_VECTOR_SIZE:8;
  } Field;
  UINT16 Value;
} PCIE_ACS_CAP_NBIFRCCFG_STRUCT;

#define PCICFG_NBIFRCCFG_PCIE_ACS_CAP_OFFSET                        0x2a4
#define SMN_DEV0_NBIF0RCNBIO0_PCIE_ACS_CAP_ADDRESS                    0x101002a4UL
#define SMN_DEV0_NBIF0RCNBIO1_PCIE_ACS_CAP_ADDRESS                    0x103002a4UL
#define SMN_DEV0_NBIF1RCNBIO0_PCIE_ACS_CAP_ADDRESS                    0x102002a4UL
#define SMN_DEV0_NBIF1RCNBIO1_PCIE_ACS_CAP_ADDRESS                    0x104002a4UL
#define SMN_DEV1_NBIF0RCNBIO0_PCIE_ACS_CAP_ADDRESS                    0x101012a4UL
#define SMN_DEV1_NBIF0RCNBIO1_PCIE_ACS_CAP_ADDRESS                    0x103012a4UL
#define SMN_DEV1_NBIF1RCNBIO0_PCIE_ACS_CAP_ADDRESS                    0x102012a4UL
#define SMN_DEV1_NBIF1RCNBIO1_PCIE_ACS_CAP_ADDRESS                    0x104012a4UL


/***********************************************************
* Register Name : PCIE_ACS_CNTL
************************************************************/

#define NBIF_ACS_CNTL_SOURCE_VALIDATION_EN_OFFSET              0
#define NBIF_ACS_CNTL_SOURCE_VALIDATION_EN_MASK                0x1

#define NBIF_ACS_CNTL_TRANSLATION_BLOCKING_EN_OFFSET           1
#define NBIF_ACS_CNTL_TRANSLATION_BLOCKING_EN_MASK             0x2

#define NBIF_ACS_CNTL_P2P_REQUEST_REDIRECT_EN_OFFSET           2
#define NBIF_ACS_CNTL_P2P_REQUEST_REDIRECT_EN_MASK             0x4

#define NBIF_ACS_CNTL_P2P_COMPLETION_REDIRECT_EN_OFFSET        3
#define NBIF_ACS_CNTL_P2P_COMPLETION_REDIRECT_EN_MASK          0x8

#define NBIF_ACS_CNTL_UPSTREAM_FORWARDING_EN_OFFSET            4
#define NBIF_ACS_CNTL_UPSTREAM_FORWARDING_EN_MASK              0x10

#define NBIF_ACS_CNTL_P2P_EGRESS_CONTROL_EN_OFFSET             5
#define NBIF_ACS_CNTL_P2P_EGRESS_CONTROL_EN_MASK               0x20

#define NBIF_ACS_CNTL_DIRECT_TRANSLATED_P2P_EN_OFFSET          6
#define NBIF_ACS_CNTL_DIRECT_TRANSLATED_P2P_EN_MASK            0x40

#define NBIF_ACS_CNTL_IO_REQUEST_BLOCKING_EN_OFFSET            7
#define NBIF_ACS_CNTL_IO_REQUEST_BLOCKING_EN_MASK              0x80

#define NBIF_ACS_CNTL_DSP_MEMORY_TARGET_ACCESS_CNTL_OFFSET     8
#define NBIF_ACS_CNTL_DSP_MEMORY_TARGET_ACCESS_CNTL_MASK       0x300

#define NBIF_ACS_CNTL_USP_MEMORY_TARGET_ACCESS_CNTL_OFFSET     10
#define NBIF_ACS_CNTL_USP_MEMORY_TARGET_ACCESS_CNTL_MASK       0xc00

#define NBIF_ACS_CNTL_UNCLAIMED_REQUEST_REDIRECT_CNTL_OFFSET   12
#define NBIF_ACS_CNTL_UNCLAIMED_REQUEST_REDIRECT_CNTL_MASK     0x1000

#define NBIF_ACS_CNTL_Reserved_15_13_OFFSET                    13
#define NBIF_ACS_CNTL_Reserved_15_13_MASK                      0xe000

typedef union {
  struct {
    UINT16                                SOURCE_VALIDATION_EN:1;
    UINT16                             TRANSLATION_BLOCKING_EN:1;
    UINT16                             P2P_REQUEST_REDIRECT_EN:1;
    UINT16                          P2P_COMPLETION_REDIRECT_EN:1;
    UINT16                              UPSTREAM_FORWARDING_EN:1;
    UINT16                               P2P_EGRESS_CONTROL_EN:1;
    UINT16                            DIRECT_TRANSLATED_P2P_EN:1;
    UINT16                              IO_REQUEST_BLOCKING_EN:1;
    UINT16                       DSP_MEMORY_TARGET_ACCESS_CNTL:2;
    UINT16                       USP_MEMORY_TARGET_ACCESS_CNTL:2;
    UINT16                     UNCLAIMED_REQUEST_REDIRECT_CNTL:1;
    UINT16                                      Reserved_15_13:3;
  } Field;
  UINT16 Value;
} PCIE_ACS_CNTL_NBIFRCCFG_STRUCT;

#define PCICFG_NBIFRCCFG_PCIE_ACS_CNTL_OFFSET                       0x2a6
#define SMN_DEV0_NBIF0RCNBIO0_PCIE_ACS_CNTL_ADDRESS                   0x101002a6UL
#define SMN_DEV0_NBIF0RCNBIO1_PCIE_ACS_CNTL_ADDRESS                   0x103002a6UL
#define SMN_DEV0_NBIF1RCNBIO0_PCIE_ACS_CNTL_ADDRESS                   0x102002a6UL
#define SMN_DEV0_NBIF1RCNBIO1_PCIE_ACS_CNTL_ADDRESS                   0x104002a6UL
#define SMN_DEV1_NBIF0RCNBIO0_PCIE_ACS_CNTL_ADDRESS                   0x101012a6UL
#define SMN_DEV1_NBIF0RCNBIO1_PCIE_ACS_CNTL_ADDRESS                   0x103012a6UL
#define SMN_DEV1_NBIF1RCNBIO0_PCIE_ACS_CNTL_ADDRESS                   0x102012a6UL
#define SMN_DEV1_NBIF1RCNBIO1_PCIE_ACS_CNTL_ADDRESS                   0x104012a6UL


/***********************************************************
* Register Name : PCIE_ACS_ENH_CAP_LIST
************************************************************/

#define NBIF_ACS_ENH_CAP_LIST_CAP_ID_OFFSET                    0
#define NBIF_ACS_ENH_CAP_LIST_CAP_ID_MASK                      0xffff

#define NBIF_ACS_ENH_CAP_LIST_CAP_VER_OFFSET                   16
#define NBIF_ACS_ENH_CAP_LIST_CAP_VER_MASK                     0xf0000

#define NBIF_ACS_ENH_CAP_LIST_NEXT_PTR_OFFSET                  20
#define NBIF_ACS_ENH_CAP_LIST_NEXT_PTR_MASK                    0xfff00000

typedef union {
  struct {
    UINT32                                              CAP_ID:16;
    UINT32                                             CAP_VER:4;
    UINT32                                            NEXT_PTR:12;
  } Field;
  UINT32 Value;
} PCIE_ACS_ENH_CAP_LIST_NBIFRCCFG_STRUCT;

#define PCICFG_NBIFRCCFG_PCIE_ACS_ENH_CAP_LIST_OFFSET               0x2a0
#define SMN_DEV0_NBIF0RCNBIO0_PCIE_ACS_ENH_CAP_LIST_ADDRESS           0x101002a0UL
#define SMN_DEV0_NBIF0RCNBIO1_PCIE_ACS_ENH_CAP_LIST_ADDRESS           0x103002a0UL
#define SMN_DEV0_NBIF1RCNBIO0_PCIE_ACS_ENH_CAP_LIST_ADDRESS           0x102002a0UL
#define SMN_DEV0_NBIF1RCNBIO1_PCIE_ACS_ENH_CAP_LIST_ADDRESS           0x104002a0UL
#define SMN_DEV1_NBIF0RCNBIO0_PCIE_ACS_ENH_CAP_LIST_ADDRESS           0x101012a0UL
#define SMN_DEV1_NBIF0RCNBIO1_PCIE_ACS_ENH_CAP_LIST_ADDRESS           0x103012a0UL
#define SMN_DEV1_NBIF1RCNBIO0_PCIE_ACS_ENH_CAP_LIST_ADDRESS           0x102012a0UL
#define SMN_DEV1_NBIF1RCNBIO1_PCIE_ACS_ENH_CAP_LIST_ADDRESS           0x104012a0UL


/***********************************************************
* Register Name : PCIE_ADV_ERR_CAP_CNTL
************************************************************/

#define NBIF_ADV_ERR_CAP_CNTL_FIRST_ERR_PTR_OFFSET             0
#define NBIF_ADV_ERR_CAP_CNTL_FIRST_ERR_PTR_MASK               0x1f

#define NBIF_ADV_ERR_CAP_CNTL_ECRC_GEN_CAP_OFFSET              5
#define NBIF_ADV_ERR_CAP_CNTL_ECRC_GEN_CAP_MASK                0x20

#define NBIF_ADV_ERR_CAP_CNTL_ECRC_GEN_EN_OFFSET               6
#define NBIF_ADV_ERR_CAP_CNTL_ECRC_GEN_EN_MASK                 0x40

#define NBIF_ADV_ERR_CAP_CNTL_ECRC_CHECK_CAP_OFFSET            7
#define NBIF_ADV_ERR_CAP_CNTL_ECRC_CHECK_CAP_MASK              0x80

#define NBIF_ADV_ERR_CAP_CNTL_ECRC_CHECK_EN_OFFSET             8
#define NBIF_ADV_ERR_CAP_CNTL_ECRC_CHECK_EN_MASK               0x100

#define NBIF_ADV_ERR_CAP_CNTL_MULTI_HDR_RECD_CAP_OFFSET        9
#define NBIF_ADV_ERR_CAP_CNTL_MULTI_HDR_RECD_CAP_MASK          0x200

#define NBIF_ADV_ERR_CAP_CNTL_MULTI_HDR_RECD_EN_OFFSET         10
#define NBIF_ADV_ERR_CAP_CNTL_MULTI_HDR_RECD_EN_MASK           0x400

#define NBIF_ADV_ERR_CAP_CNTL_TLP_PREFIX_LOG_PRESENT_OFFSET    11
#define NBIF_ADV_ERR_CAP_CNTL_TLP_PREFIX_LOG_PRESENT_MASK      0x800

#define NBIF_ADV_ERR_CAP_CNTL_COMPLETION_TIMEOUT_LOG_CAPABLE_OFFSET 12
#define NBIF_ADV_ERR_CAP_CNTL_COMPLETION_TIMEOUT_LOG_CAPABLE_MASK 0x1000

#define NBIF_ADV_ERR_CAP_CNTL_Reserved_31_13_OFFSET            13
#define NBIF_ADV_ERR_CAP_CNTL_Reserved_31_13_MASK              0xffffe000

typedef union {
  struct {
    UINT32                                       FIRST_ERR_PTR:5;
    UINT32                                        ECRC_GEN_CAP:1;
    UINT32                                         ECRC_GEN_EN:1;
    UINT32                                      ECRC_CHECK_CAP:1;
    UINT32                                       ECRC_CHECK_EN:1;
    UINT32                                  MULTI_HDR_RECD_CAP:1;
    UINT32                                   MULTI_HDR_RECD_EN:1;
    UINT32                              TLP_PREFIX_LOG_PRESENT:1;
    UINT32                      COMPLETION_TIMEOUT_LOG_CAPABLE:1;
    UINT32                                      Reserved_31_13:19;
  } Field;
  UINT32 Value;
} PCIE_ADV_ERR_CAP_CNTL_NBIFRCCFG_STRUCT;

#define PCICFG_NBIFRCCFG_PCIE_ADV_ERR_CAP_CNTL_OFFSET               0x168
#define SMN_DEV0_NBIF0RCNBIO0_PCIE_ADV_ERR_CAP_CNTL_ADDRESS           0x10100168UL
#define SMN_DEV0_NBIF0RCNBIO1_PCIE_ADV_ERR_CAP_CNTL_ADDRESS           0x10300168UL
#define SMN_DEV0_NBIF1RCNBIO0_PCIE_ADV_ERR_CAP_CNTL_ADDRESS           0x10200168UL
#define SMN_DEV0_NBIF1RCNBIO1_PCIE_ADV_ERR_CAP_CNTL_ADDRESS           0x10400168UL
#define SMN_DEV1_NBIF0RCNBIO0_PCIE_ADV_ERR_CAP_CNTL_ADDRESS           0x10101168UL
#define SMN_DEV1_NBIF0RCNBIO1_PCIE_ADV_ERR_CAP_CNTL_ADDRESS           0x10301168UL
#define SMN_DEV1_NBIF1RCNBIO0_PCIE_ADV_ERR_CAP_CNTL_ADDRESS           0x10201168UL
#define SMN_DEV1_NBIF1RCNBIO1_PCIE_ADV_ERR_CAP_CNTL_ADDRESS           0x10401168UL


/***********************************************************
* Register Name : PCIE_ADV_ERR_RPT_ENH_CAP_LIST
************************************************************/

#define NBIF_ADV_ERR_RPT_ENH_CAP_LIST_CAP_ID_OFFSET            0
#define NBIF_ADV_ERR_RPT_ENH_CAP_LIST_CAP_ID_MASK              0xffff

#define NBIF_ADV_ERR_RPT_ENH_CAP_LIST_CAP_VER_OFFSET           16
#define NBIF_ADV_ERR_RPT_ENH_CAP_LIST_CAP_VER_MASK             0xf0000

#define NBIF_ADV_ERR_RPT_ENH_CAP_LIST_NEXT_PTR_OFFSET          20
#define NBIF_ADV_ERR_RPT_ENH_CAP_LIST_NEXT_PTR_MASK            0xfff00000

typedef union {
  struct {
    UINT32                                              CAP_ID:16;
    UINT32                                             CAP_VER:4;
    UINT32                                            NEXT_PTR:12;
  } Field;
  UINT32 Value;
} PCIE_ADV_ERR_RPT_ENH_CAP_LIST_NBIFRCCFG_STRUCT;

#define PCICFG_NBIFRCCFG_PCIE_ADV_ERR_RPT_ENH_CAP_LIST_OFFSET       0x150
#define SMN_DEV0_NBIF0RCNBIO0_PCIE_ADV_ERR_RPT_ENH_CAP_LIST_ADDRESS   0x10100150UL
#define SMN_DEV0_NBIF0RCNBIO1_PCIE_ADV_ERR_RPT_ENH_CAP_LIST_ADDRESS   0x10300150UL
#define SMN_DEV0_NBIF1RCNBIO0_PCIE_ADV_ERR_RPT_ENH_CAP_LIST_ADDRESS   0x10200150UL
#define SMN_DEV0_NBIF1RCNBIO1_PCIE_ADV_ERR_RPT_ENH_CAP_LIST_ADDRESS   0x10400150UL
#define SMN_DEV1_NBIF0RCNBIO0_PCIE_ADV_ERR_RPT_ENH_CAP_LIST_ADDRESS   0x10101150UL
#define SMN_DEV1_NBIF0RCNBIO1_PCIE_ADV_ERR_RPT_ENH_CAP_LIST_ADDRESS   0x10301150UL
#define SMN_DEV1_NBIF1RCNBIO0_PCIE_ADV_ERR_RPT_ENH_CAP_LIST_ADDRESS   0x10201150UL
#define SMN_DEV1_NBIF1RCNBIO1_PCIE_ADV_ERR_RPT_ENH_CAP_LIST_ADDRESS   0x10401150UL


/***********************************************************
* Register Name : PCIE_AP_ENH_CAP_LIST
************************************************************/

#define NBIF_AP_ENH_CAP_LIST_CAP_ID_OFFSET                     0
#define NBIF_AP_ENH_CAP_LIST_CAP_ID_MASK                       0xffff

#define NBIF_AP_ENH_CAP_LIST_CAP_VER_OFFSET                    16
#define NBIF_AP_ENH_CAP_LIST_CAP_VER_MASK                      0xf0000

#define NBIF_AP_ENH_CAP_LIST_NEXT_PTR_OFFSET                   20
#define NBIF_AP_ENH_CAP_LIST_NEXT_PTR_MASK                     0xfff00000

typedef union {
  struct {
    UINT32                                              CAP_ID:16;
    UINT32                                             CAP_VER:4;
    UINT32                                            NEXT_PTR:12;
  } Field;
  UINT32 Value;
} PCIE_AP_ENH_CAP_LIST_NBIFRCCFG_STRUCT;

#define PCICFG_NBIFRCCFG_PCIE_AP_ENH_CAP_LIST_OFFSET                0x530
#define SMN_DEV0_NBIF0RCNBIO0_PCIE_AP_ENH_CAP_LIST_ADDRESS            0x10100530UL
#define SMN_DEV0_NBIF0RCNBIO1_PCIE_AP_ENH_CAP_LIST_ADDRESS            0x10300530UL
#define SMN_DEV0_NBIF1RCNBIO0_PCIE_AP_ENH_CAP_LIST_ADDRESS            0x10200530UL
#define SMN_DEV0_NBIF1RCNBIO1_PCIE_AP_ENH_CAP_LIST_ADDRESS            0x10400530UL
#define SMN_DEV1_NBIF0RCNBIO0_PCIE_AP_ENH_CAP_LIST_ADDRESS            0x10101530UL
#define SMN_DEV1_NBIF0RCNBIO1_PCIE_AP_ENH_CAP_LIST_ADDRESS            0x10301530UL
#define SMN_DEV1_NBIF1RCNBIO0_PCIE_AP_ENH_CAP_LIST_ADDRESS            0x10201530UL
#define SMN_DEV1_NBIF1RCNBIO1_PCIE_AP_ENH_CAP_LIST_ADDRESS            0x10401530UL


/***********************************************************
* Register Name : PCIE_CAP
************************************************************/

#define NBIF_CAP_VERSION_OFFSET                                0
#define NBIF_CAP_VERSION_MASK                                  0xf

#define NBIF_CAP_DEVICE_TYPE_OFFSET                            4
#define NBIF_CAP_DEVICE_TYPE_MASK                              0xf0

#define NBIF_CAP_SLOT_IMPLEMENTED_OFFSET                       8
#define NBIF_CAP_SLOT_IMPLEMENTED_MASK                         0x100

#define NBIF_CAP_INT_MESSAGE_NUM_OFFSET                        9
#define NBIF_CAP_INT_MESSAGE_NUM_MASK                          0x3e00

#define NBIF_CAP_Reserved_15_14_OFFSET                         14
#define NBIF_CAP_Reserved_15_14_MASK                           0xc000

typedef union {
  struct {
    UINT16                                             VERSION:4;
    UINT16                                         DEVICE_TYPE:4;
    UINT16                                    SLOT_IMPLEMENTED:1;
    UINT16                                     INT_MESSAGE_NUM:5;
    UINT16                                      Reserved_15_14:2;
  } Field;
  UINT16 Value;
} PCIE_CAP_NBIFRCCFG_STRUCT;

#define PCICFG_NBIFRCCFG_PCIE_CAP_OFFSET                            0x5a
#define SMN_DEV0_NBIF0RCNBIO0_PCIE_CAP_ADDRESS                        0x1010005aUL
#define SMN_DEV0_NBIF0RCNBIO1_PCIE_CAP_ADDRESS                        0x1030005aUL
#define SMN_DEV0_NBIF1RCNBIO0_PCIE_CAP_ADDRESS                        0x1020005aUL
#define SMN_DEV0_NBIF1RCNBIO1_PCIE_CAP_ADDRESS                        0x1040005aUL
#define SMN_DEV1_NBIF0RCNBIO0_PCIE_CAP_ADDRESS                        0x1010105aUL
#define SMN_DEV1_NBIF0RCNBIO1_PCIE_CAP_ADDRESS                        0x1030105aUL
#define SMN_DEV1_NBIF1RCNBIO0_PCIE_CAP_ADDRESS                        0x1020105aUL
#define SMN_DEV1_NBIF1RCNBIO1_PCIE_CAP_ADDRESS                        0x1040105aUL


/***********************************************************
* Register Name : PCIE_CAP_LIST
************************************************************/

#define NBIF_CAP_LIST_CAP_ID_OFFSET                            0
#define NBIF_CAP_LIST_CAP_ID_MASK                              0xff

#define NBIF_CAP_LIST_NEXT_PTR_OFFSET                          8
#define NBIF_CAP_LIST_NEXT_PTR_MASK                            0xff00

typedef union {
  struct {
    UINT16                                              CAP_ID:8;
    UINT16                                            NEXT_PTR:8;
  } Field;
  UINT16 Value;
} PCIE_CAP_LIST_NBIFRCCFG_STRUCT;

#define PCICFG_NBIFRCCFG_PCIE_CAP_LIST_OFFSET                       0x58
#define SMN_DEV0_NBIF0RCNBIO0_PCIE_CAP_LIST_ADDRESS                   0x10100058UL
#define SMN_DEV0_NBIF0RCNBIO1_PCIE_CAP_LIST_ADDRESS                   0x10300058UL
#define SMN_DEV0_NBIF1RCNBIO0_PCIE_CAP_LIST_ADDRESS                   0x10200058UL
#define SMN_DEV0_NBIF1RCNBIO1_PCIE_CAP_LIST_ADDRESS                   0x10400058UL
#define SMN_DEV1_NBIF0RCNBIO0_PCIE_CAP_LIST_ADDRESS                   0x10101058UL
#define SMN_DEV1_NBIF0RCNBIO1_PCIE_CAP_LIST_ADDRESS                   0x10301058UL
#define SMN_DEV1_NBIF1RCNBIO0_PCIE_CAP_LIST_ADDRESS                   0x10201058UL
#define SMN_DEV1_NBIF1RCNBIO1_PCIE_CAP_LIST_ADDRESS                   0x10401058UL


/***********************************************************
* Register Name : PCIE_CORR_ERR_MASK
************************************************************/

#define NBIF_CORR_ERR_MASK_RCV_ERR_MASK_OFFSET                 0
#define NBIF_CORR_ERR_MASK_RCV_ERR_MASK_MASK                   0x1

#define NBIF_CORR_ERR_MASK_Reserved_5_1_OFFSET                 1
#define NBIF_CORR_ERR_MASK_Reserved_5_1_MASK                   0x3e

#define NBIF_CORR_ERR_MASK_BAD_TLP_MASK_OFFSET                 6
#define NBIF_CORR_ERR_MASK_BAD_TLP_MASK_MASK                   0x40

#define NBIF_CORR_ERR_MASK_BAD_DLLP_MASK_OFFSET                7
#define NBIF_CORR_ERR_MASK_BAD_DLLP_MASK_MASK                  0x80

#define NBIF_CORR_ERR_MASK_REPLAY_NUM_ROLLOVER_MASK_OFFSET     8
#define NBIF_CORR_ERR_MASK_REPLAY_NUM_ROLLOVER_MASK_MASK       0x100

#define NBIF_CORR_ERR_MASK_Reserved_11_9_OFFSET                9
#define NBIF_CORR_ERR_MASK_Reserved_11_9_MASK                  0xe00

#define NBIF_CORR_ERR_MASK_REPLAY_TIMER_TIMEOUT_MASK_OFFSET    12
#define NBIF_CORR_ERR_MASK_REPLAY_TIMER_TIMEOUT_MASK_MASK      0x1000

#define NBIF_CORR_ERR_MASK_ADVISORY_NONFATAL_ERR_MASK_OFFSET   13
#define NBIF_CORR_ERR_MASK_ADVISORY_NONFATAL_ERR_MASK_MASK     0x2000

#define NBIF_CORR_ERR_MASK_CORR_INT_ERR_MASK_OFFSET            14
#define NBIF_CORR_ERR_MASK_CORR_INT_ERR_MASK_MASK              0x4000

#define NBIF_CORR_ERR_MASK_HDR_LOG_OVFL_MASK_OFFSET            15
#define NBIF_CORR_ERR_MASK_HDR_LOG_OVFL_MASK_MASK              0x8000

#define NBIF_CORR_ERR_MASK_Reserved_31_16_OFFSET               16
#define NBIF_CORR_ERR_MASK_Reserved_31_16_MASK                 0xffff0000

typedef union {
  struct {
    UINT32                                        RCV_ERR_MASK:1;
    UINT32                                        Reserved_5_1:5;
    UINT32                                        BAD_TLP_MASK:1;
    UINT32                                       BAD_DLLP_MASK:1;
    UINT32                            REPLAY_NUM_ROLLOVER_MASK:1;
    UINT32                                       Reserved_11_9:3;
    UINT32                           REPLAY_TIMER_TIMEOUT_MASK:1;
    UINT32                          ADVISORY_NONFATAL_ERR_MASK:1;
    UINT32                                   CORR_INT_ERR_MASK:1;
    UINT32                                   HDR_LOG_OVFL_MASK:1;
    UINT32                                      Reserved_31_16:16;
  } Field;
  UINT32 Value;
} PCIE_CORR_ERR_MASK_NBIFRCCFG_STRUCT;

#define PCICFG_NBIFRCCFG_PCIE_CORR_ERR_MASK_OFFSET                  0x164
#define SMN_DEV0_NBIF0RCNBIO0_PCIE_CORR_ERR_MASK_ADDRESS              0x10100164UL
#define SMN_DEV0_NBIF0RCNBIO1_PCIE_CORR_ERR_MASK_ADDRESS              0x10300164UL
#define SMN_DEV0_NBIF1RCNBIO0_PCIE_CORR_ERR_MASK_ADDRESS              0x10200164UL
#define SMN_DEV0_NBIF1RCNBIO1_PCIE_CORR_ERR_MASK_ADDRESS              0x10400164UL
#define SMN_DEV1_NBIF0RCNBIO0_PCIE_CORR_ERR_MASK_ADDRESS              0x10101164UL
#define SMN_DEV1_NBIF0RCNBIO1_PCIE_CORR_ERR_MASK_ADDRESS              0x10301164UL
#define SMN_DEV1_NBIF1RCNBIO0_PCIE_CORR_ERR_MASK_ADDRESS              0x10201164UL
#define SMN_DEV1_NBIF1RCNBIO1_PCIE_CORR_ERR_MASK_ADDRESS              0x10401164UL


/***********************************************************
* Register Name : PCIE_CORR_ERR_STATUS
************************************************************/

#define NBIF_CORR_ERR_STATUS_RCV_ERR_STATUS_OFFSET             0
#define NBIF_CORR_ERR_STATUS_RCV_ERR_STATUS_MASK               0x1

#define NBIF_CORR_ERR_STATUS_Reserved_5_1_OFFSET               1
#define NBIF_CORR_ERR_STATUS_Reserved_5_1_MASK                 0x3e

#define NBIF_CORR_ERR_STATUS_BAD_TLP_STATUS_OFFSET             6
#define NBIF_CORR_ERR_STATUS_BAD_TLP_STATUS_MASK               0x40

#define NBIF_CORR_ERR_STATUS_BAD_DLLP_STATUS_OFFSET            7
#define NBIF_CORR_ERR_STATUS_BAD_DLLP_STATUS_MASK              0x80

#define NBIF_CORR_ERR_STATUS_REPLAY_NUM_ROLLOVER_STATUS_OFFSET 8
#define NBIF_CORR_ERR_STATUS_REPLAY_NUM_ROLLOVER_STATUS_MASK   0x100

#define NBIF_CORR_ERR_STATUS_Reserved_11_9_OFFSET              9
#define NBIF_CORR_ERR_STATUS_Reserved_11_9_MASK                0xe00

#define NBIF_CORR_ERR_STATUS_REPLAY_TIMER_TIMEOUT_STATUS_OFFSET 12
#define NBIF_CORR_ERR_STATUS_REPLAY_TIMER_TIMEOUT_STATUS_MASK  0x1000

#define NBIF_CORR_ERR_STATUS_ADVISORY_NONFATAL_ERR_STATUS_OFFSET 13
#define NBIF_CORR_ERR_STATUS_ADVISORY_NONFATAL_ERR_STATUS_MASK 0x2000

#define NBIF_CORR_ERR_STATUS_CORR_INT_ERR_STATUS_OFFSET        14
#define NBIF_CORR_ERR_STATUS_CORR_INT_ERR_STATUS_MASK          0x4000

#define NBIF_CORR_ERR_STATUS_HDR_LOG_OVFL_STATUS_OFFSET        15
#define NBIF_CORR_ERR_STATUS_HDR_LOG_OVFL_STATUS_MASK          0x8000

#define NBIF_CORR_ERR_STATUS_Reserved_31_16_OFFSET             16
#define NBIF_CORR_ERR_STATUS_Reserved_31_16_MASK               0xffff0000

typedef union {
  struct {
    UINT32                                      RCV_ERR_STATUS:1;
    UINT32                                        Reserved_5_1:5;
    UINT32                                      BAD_TLP_STATUS:1;
    UINT32                                     BAD_DLLP_STATUS:1;
    UINT32                          REPLAY_NUM_ROLLOVER_STATUS:1;
    UINT32                                       Reserved_11_9:3;
    UINT32                         REPLAY_TIMER_TIMEOUT_STATUS:1;
    UINT32                        ADVISORY_NONFATAL_ERR_STATUS:1;
    UINT32                                 CORR_INT_ERR_STATUS:1;
    UINT32                                 HDR_LOG_OVFL_STATUS:1;
    UINT32                                      Reserved_31_16:16;
  } Field;
  UINT32 Value;
} PCIE_CORR_ERR_STATUS_NBIFRCCFG_STRUCT;

#define PCICFG_NBIFRCCFG_PCIE_CORR_ERR_STATUS_OFFSET                0x160
#define SMN_DEV0_NBIF0RCNBIO0_PCIE_CORR_ERR_STATUS_ADDRESS            0x10100160UL
#define SMN_DEV0_NBIF0RCNBIO1_PCIE_CORR_ERR_STATUS_ADDRESS            0x10300160UL
#define SMN_DEV0_NBIF1RCNBIO0_PCIE_CORR_ERR_STATUS_ADDRESS            0x10200160UL
#define SMN_DEV0_NBIF1RCNBIO1_PCIE_CORR_ERR_STATUS_ADDRESS            0x10400160UL
#define SMN_DEV1_NBIF0RCNBIO0_PCIE_CORR_ERR_STATUS_ADDRESS            0x10101160UL
#define SMN_DEV1_NBIF0RCNBIO1_PCIE_CORR_ERR_STATUS_ADDRESS            0x10301160UL
#define SMN_DEV1_NBIF1RCNBIO0_PCIE_CORR_ERR_STATUS_ADDRESS            0x10201160UL
#define SMN_DEV1_NBIF1RCNBIO1_PCIE_CORR_ERR_STATUS_ADDRESS            0x10401160UL


/***********************************************************
* Register Name : PCIE_DEV_SERIAL_NUM_DW1
************************************************************/

#define NBIF_DEV_SERIAL_NUM_DW1_SERIAL_NUMBER_LO_OFFSET        0
#define NBIF_DEV_SERIAL_NUM_DW1_SERIAL_NUMBER_LO_MASK          0xffffffff

typedef union {
  struct {
    UINT32                                    SERIAL_NUMBER_LO:32;
  } Field;
  UINT32 Value;
} PCIE_DEV_SERIAL_NUM_DW1_STRUCT;

#define PCICFG_NBIFRCCFG_PCIE_DEV_SERIAL_NUM_DW1_OFFSET             0x144
#define SMN_DEV0_NBIF0RCNBIO0_PCIE_DEV_SERIAL_NUM_DW1_ADDRESS         0x10100144UL
#define SMN_DEV0_NBIF0RCNBIO1_PCIE_DEV_SERIAL_NUM_DW1_ADDRESS         0x10300144UL
#define SMN_DEV0_NBIF1RCNBIO0_PCIE_DEV_SERIAL_NUM_DW1_ADDRESS         0x10200144UL
#define SMN_DEV0_NBIF1RCNBIO1_PCIE_DEV_SERIAL_NUM_DW1_ADDRESS         0x10400144UL
#define SMN_DEV1_NBIF0RCNBIO0_PCIE_DEV_SERIAL_NUM_DW1_ADDRESS         0x10101144UL
#define SMN_DEV1_NBIF0RCNBIO1_PCIE_DEV_SERIAL_NUM_DW1_ADDRESS         0x10301144UL
#define SMN_DEV1_NBIF1RCNBIO0_PCIE_DEV_SERIAL_NUM_DW1_ADDRESS         0x10201144UL
#define SMN_DEV1_NBIF1RCNBIO1_PCIE_DEV_SERIAL_NUM_DW1_ADDRESS         0x10401144UL


/***********************************************************
* Register Name : PCIE_DEV_SERIAL_NUM_DW2
************************************************************/

#define NBIF_DEV_SERIAL_NUM_DW2_SERIAL_NUMBER_HI_OFFSET        0
#define NBIF_DEV_SERIAL_NUM_DW2_SERIAL_NUMBER_HI_MASK          0xffffffff

typedef union {
  struct {
    UINT32                                    SERIAL_NUMBER_HI:32;
  } Field;
  UINT32 Value;
} PCIE_DEV_SERIAL_NUM_DW2_STRUCT;

#define PCICFG_NBIFRCCFG_PCIE_DEV_SERIAL_NUM_DW2_OFFSET             0x148
#define SMN_DEV0_NBIF0RCNBIO0_PCIE_DEV_SERIAL_NUM_DW2_ADDRESS         0x10100148UL
#define SMN_DEV0_NBIF0RCNBIO1_PCIE_DEV_SERIAL_NUM_DW2_ADDRESS         0x10300148UL
#define SMN_DEV0_NBIF1RCNBIO0_PCIE_DEV_SERIAL_NUM_DW2_ADDRESS         0x10200148UL
#define SMN_DEV0_NBIF1RCNBIO1_PCIE_DEV_SERIAL_NUM_DW2_ADDRESS         0x10400148UL
#define SMN_DEV1_NBIF0RCNBIO0_PCIE_DEV_SERIAL_NUM_DW2_ADDRESS         0x10101148UL
#define SMN_DEV1_NBIF0RCNBIO1_PCIE_DEV_SERIAL_NUM_DW2_ADDRESS         0x10301148UL
#define SMN_DEV1_NBIF1RCNBIO0_PCIE_DEV_SERIAL_NUM_DW2_ADDRESS         0x10201148UL
#define SMN_DEV1_NBIF1RCNBIO1_PCIE_DEV_SERIAL_NUM_DW2_ADDRESS         0x10401148UL


/***********************************************************
* Register Name : PCIE_DEV_SERIAL_NUM_ENH_CAP_LIST
************************************************************/

#define NBIF_DEV_SERIAL_NUM_ENH_CAP_LIST_CAP_ID_OFFSET         0
#define NBIF_DEV_SERIAL_NUM_ENH_CAP_LIST_CAP_ID_MASK           0xffff

#define NBIF_DEV_SERIAL_NUM_ENH_CAP_LIST_CAP_VER_OFFSET        16
#define NBIF_DEV_SERIAL_NUM_ENH_CAP_LIST_CAP_VER_MASK          0xf0000

#define NBIF_DEV_SERIAL_NUM_ENH_CAP_LIST_NEXT_PTR_OFFSET       20
#define NBIF_DEV_SERIAL_NUM_ENH_CAP_LIST_NEXT_PTR_MASK         0xfff00000

typedef union {
  struct {
    UINT32                                              CAP_ID:16;
    UINT32                                             CAP_VER:4;
    UINT32                                            NEXT_PTR:12;
  } Field;
  UINT32 Value;
} PCIE_DEV_SERIAL_NUM_ENH_CAP_LIST_STRUCT;

#define PCICFG_NBIFRCCFG_PCIE_DEV_SERIAL_NUM_ENH_CAP_LIST_OFFSET    0x140
#define SMN_DEV0_NBIF0RCNBIO0_PCIE_DEV_SERIAL_NUM_ENH_CAP_LIST_ADDRESS 0x10100140UL
#define SMN_DEV0_NBIF0RCNBIO1_PCIE_DEV_SERIAL_NUM_ENH_CAP_LIST_ADDRESS 0x10300140UL
#define SMN_DEV0_NBIF1RCNBIO0_PCIE_DEV_SERIAL_NUM_ENH_CAP_LIST_ADDRESS 0x10200140UL
#define SMN_DEV0_NBIF1RCNBIO1_PCIE_DEV_SERIAL_NUM_ENH_CAP_LIST_ADDRESS 0x10400140UL
#define SMN_DEV1_NBIF0RCNBIO0_PCIE_DEV_SERIAL_NUM_ENH_CAP_LIST_ADDRESS 0x10101140UL
#define SMN_DEV1_NBIF0RCNBIO1_PCIE_DEV_SERIAL_NUM_ENH_CAP_LIST_ADDRESS 0x10301140UL
#define SMN_DEV1_NBIF1RCNBIO0_PCIE_DEV_SERIAL_NUM_ENH_CAP_LIST_ADDRESS 0x10201140UL
#define SMN_DEV1_NBIF1RCNBIO1_PCIE_DEV_SERIAL_NUM_ENH_CAP_LIST_ADDRESS 0x10401140UL


/***********************************************************
* Register Name : PCIE_DLF_ENH_CAP_LIST
************************************************************/

#define NBIF_DLF_ENH_CAP_LIST_CAP_ID_OFFSET                    0
#define NBIF_DLF_ENH_CAP_LIST_CAP_ID_MASK                      0xffff

#define NBIF_DLF_ENH_CAP_LIST_CAP_VER_OFFSET                   16
#define NBIF_DLF_ENH_CAP_LIST_CAP_VER_MASK                     0xf0000

#define NBIF_DLF_ENH_CAP_LIST_NEXT_PTR_OFFSET                  20
#define NBIF_DLF_ENH_CAP_LIST_NEXT_PTR_MASK                    0xfff00000

typedef union {
  struct {
    UINT32                                              CAP_ID:16;
    UINT32                                             CAP_VER:4;
    UINT32                                            NEXT_PTR:12;
  } Field;
  UINT32 Value;
} PCIE_DLF_ENH_CAP_LIST_NBIFRCCFG_STRUCT;

#define PCICFG_NBIFRCCFG_PCIE_DLF_ENH_CAP_LIST_OFFSET               0x400
#define SMN_DEV0_NBIF0RCNBIO0_PCIE_DLF_ENH_CAP_LIST_ADDRESS           0x10100400UL
#define SMN_DEV0_NBIF0RCNBIO1_PCIE_DLF_ENH_CAP_LIST_ADDRESS           0x10300400UL
#define SMN_DEV0_NBIF1RCNBIO0_PCIE_DLF_ENH_CAP_LIST_ADDRESS           0x10200400UL
#define SMN_DEV0_NBIF1RCNBIO1_PCIE_DLF_ENH_CAP_LIST_ADDRESS           0x10400400UL
#define SMN_DEV1_NBIF0RCNBIO0_PCIE_DLF_ENH_CAP_LIST_ADDRESS           0x10101400UL
#define SMN_DEV1_NBIF0RCNBIO1_PCIE_DLF_ENH_CAP_LIST_ADDRESS           0x10301400UL
#define SMN_DEV1_NBIF1RCNBIO0_PCIE_DLF_ENH_CAP_LIST_ADDRESS           0x10201400UL
#define SMN_DEV1_NBIF1RCNBIO1_PCIE_DLF_ENH_CAP_LIST_ADDRESS           0x10401400UL


/***********************************************************
* Register Name : PCIE_ERR_SRC_ID
************************************************************/

#define NBIF_ERR_SRC_ID_ERR_CORR_SRC_ID_OFFSET                 0
#define NBIF_ERR_SRC_ID_ERR_CORR_SRC_ID_MASK                   0xffff

#define NBIF_ERR_SRC_ID_ERR_FATAL_NONFATAL_SRC_ID_OFFSET       16
#define NBIF_ERR_SRC_ID_ERR_FATAL_NONFATAL_SRC_ID_MASK         0xffff0000

typedef union {
  struct {
    UINT32                                     ERR_CORR_SRC_ID:16;
    UINT32                           ERR_FATAL_NONFATAL_SRC_ID:16;
  } Field;
  UINT32 Value;
} PCIE_ERR_SRC_ID_STRUCT;

#define PCICFG_NBIFRCCFG_PCIE_ERR_SRC_ID_OFFSET                     0x184
#define SMN_DEV0_NBIF0RCNBIO0_PCIE_ERR_SRC_ID_ADDRESS                 0x10100184UL
#define SMN_DEV0_NBIF0RCNBIO1_PCIE_ERR_SRC_ID_ADDRESS                 0x10300184UL
#define SMN_DEV0_NBIF1RCNBIO0_PCIE_ERR_SRC_ID_ADDRESS                 0x10200184UL
#define SMN_DEV0_NBIF1RCNBIO1_PCIE_ERR_SRC_ID_ADDRESS                 0x10400184UL
#define SMN_DEV1_NBIF0RCNBIO0_PCIE_ERR_SRC_ID_ADDRESS                 0x10101184UL
#define SMN_DEV1_NBIF0RCNBIO1_PCIE_ERR_SRC_ID_ADDRESS                 0x10301184UL
#define SMN_DEV1_NBIF1RCNBIO0_PCIE_ERR_SRC_ID_ADDRESS                 0x10201184UL
#define SMN_DEV1_NBIF1RCNBIO1_PCIE_ERR_SRC_ID_ADDRESS                 0x10401184UL


/***********************************************************
* Register Name : PCIE_HDR_LOG0
************************************************************/

#define NBIF_HDR_LOG0_TLP_HDR_OFFSET                           0
#define NBIF_HDR_LOG0_TLP_HDR_MASK                             0xffffffff

typedef union {
  struct {
    UINT32                                             TLP_HDR:32;
  } Field;
  UINT32 Value;
} PCIE_HDR_LOG0_NBIFRCCFG_STRUCT;

#define PCICFG_NBIFRCCFG_PCIE_HDR_LOG0_OFFSET                       0x16c
#define SMN_DEV0_NBIF0RCNBIO0_PCIE_HDR_LOG0_ADDRESS                   0x1010016cUL
#define SMN_DEV0_NBIF0RCNBIO1_PCIE_HDR_LOG0_ADDRESS                   0x1030016cUL
#define SMN_DEV0_NBIF1RCNBIO0_PCIE_HDR_LOG0_ADDRESS                   0x1020016cUL
#define SMN_DEV0_NBIF1RCNBIO1_PCIE_HDR_LOG0_ADDRESS                   0x1040016cUL
#define SMN_DEV1_NBIF0RCNBIO0_PCIE_HDR_LOG0_ADDRESS                   0x1010116cUL
#define SMN_DEV1_NBIF0RCNBIO1_PCIE_HDR_LOG0_ADDRESS                   0x1030116cUL
#define SMN_DEV1_NBIF1RCNBIO0_PCIE_HDR_LOG0_ADDRESS                   0x1020116cUL
#define SMN_DEV1_NBIF1RCNBIO1_PCIE_HDR_LOG0_ADDRESS                   0x1040116cUL


/***********************************************************
* Register Name : PCIE_HDR_LOG1
************************************************************/

#define NBIF_HDR_LOG1_TLP_HDR_OFFSET                           0
#define NBIF_HDR_LOG1_TLP_HDR_MASK                             0xffffffff

typedef union {
  struct {
    UINT32                                             TLP_HDR:32;
  } Field;
  UINT32 Value;
} PCIE_HDR_LOG1_NBIFRCCFG_STRUCT;

#define PCICFG_NBIFRCCFG_PCIE_HDR_LOG1_OFFSET                       0x170
#define SMN_DEV0_NBIF0RCNBIO0_PCIE_HDR_LOG1_ADDRESS                   0x10100170UL
#define SMN_DEV0_NBIF0RCNBIO1_PCIE_HDR_LOG1_ADDRESS                   0x10300170UL
#define SMN_DEV0_NBIF1RCNBIO0_PCIE_HDR_LOG1_ADDRESS                   0x10200170UL
#define SMN_DEV0_NBIF1RCNBIO1_PCIE_HDR_LOG1_ADDRESS                   0x10400170UL
#define SMN_DEV1_NBIF0RCNBIO0_PCIE_HDR_LOG1_ADDRESS                   0x10101170UL
#define SMN_DEV1_NBIF0RCNBIO1_PCIE_HDR_LOG1_ADDRESS                   0x10301170UL
#define SMN_DEV1_NBIF1RCNBIO0_PCIE_HDR_LOG1_ADDRESS                   0x10201170UL
#define SMN_DEV1_NBIF1RCNBIO1_PCIE_HDR_LOG1_ADDRESS                   0x10401170UL


/***********************************************************
* Register Name : PCIE_HDR_LOG2
************************************************************/

#define NBIF_HDR_LOG2_TLP_HDR_OFFSET                           0
#define NBIF_HDR_LOG2_TLP_HDR_MASK                             0xffffffff

typedef union {
  struct {
    UINT32                                             TLP_HDR:32;
  } Field;
  UINT32 Value;
} PCIE_HDR_LOG2_NBIFRCCFG_STRUCT;

#define PCICFG_NBIFRCCFG_PCIE_HDR_LOG2_OFFSET                       0x174
#define SMN_DEV0_NBIF0RCNBIO0_PCIE_HDR_LOG2_ADDRESS                   0x10100174UL
#define SMN_DEV0_NBIF0RCNBIO1_PCIE_HDR_LOG2_ADDRESS                   0x10300174UL
#define SMN_DEV0_NBIF1RCNBIO0_PCIE_HDR_LOG2_ADDRESS                   0x10200174UL
#define SMN_DEV0_NBIF1RCNBIO1_PCIE_HDR_LOG2_ADDRESS                   0x10400174UL
#define SMN_DEV1_NBIF0RCNBIO0_PCIE_HDR_LOG2_ADDRESS                   0x10101174UL
#define SMN_DEV1_NBIF0RCNBIO1_PCIE_HDR_LOG2_ADDRESS                   0x10301174UL
#define SMN_DEV1_NBIF1RCNBIO0_PCIE_HDR_LOG2_ADDRESS                   0x10201174UL
#define SMN_DEV1_NBIF1RCNBIO1_PCIE_HDR_LOG2_ADDRESS                   0x10401174UL


/***********************************************************
* Register Name : PCIE_HDR_LOG3
************************************************************/

#define NBIF_HDR_LOG3_TLP_HDR_OFFSET                           0
#define NBIF_HDR_LOG3_TLP_HDR_MASK                             0xffffffff

typedef union {
  struct {
    UINT32                                             TLP_HDR:32;
  } Field;
  UINT32 Value;
} PCIE_HDR_LOG3_NBIFRCCFG_STRUCT;

#define PCICFG_NBIFRCCFG_PCIE_HDR_LOG3_OFFSET                       0x178
#define SMN_DEV0_NBIF0RCNBIO0_PCIE_HDR_LOG3_ADDRESS                   0x10100178UL
#define SMN_DEV0_NBIF0RCNBIO1_PCIE_HDR_LOG3_ADDRESS                   0x10300178UL
#define SMN_DEV0_NBIF1RCNBIO0_PCIE_HDR_LOG3_ADDRESS                   0x10200178UL
#define SMN_DEV0_NBIF1RCNBIO1_PCIE_HDR_LOG3_ADDRESS                   0x10400178UL
#define SMN_DEV1_NBIF0RCNBIO0_PCIE_HDR_LOG3_ADDRESS                   0x10101178UL
#define SMN_DEV1_NBIF0RCNBIO1_PCIE_HDR_LOG3_ADDRESS                   0x10301178UL
#define SMN_DEV1_NBIF1RCNBIO0_PCIE_HDR_LOG3_ADDRESS                   0x10201178UL
#define SMN_DEV1_NBIF1RCNBIO1_PCIE_HDR_LOG3_ADDRESS                   0x10401178UL


/***********************************************************
* Register Name : PCIE_LANE_EQUALIZATION_CNTL
************************************************************/

#define NBIF_LANE_EQUALIZATION_CNTL_DOWNSTREAM_PORT_8GT_TX_PRESET_OFFSET 0
#define NBIF_LANE_EQUALIZATION_CNTL_DOWNSTREAM_PORT_8GT_TX_PRESET_MASK 0xf

#define NBIF_LANE_EQUALIZATION_CNTL_DOWNSTREAM_PORT_8GT_RX_PRESET_HINT_OFFSET 4
#define NBIF_LANE_EQUALIZATION_CNTL_DOWNSTREAM_PORT_8GT_RX_PRESET_HINT_MASK 0x70

#define NBIF_LANE_EQUALIZATION_CNTL_Reserved_7_7_OFFSET        7
#define NBIF_LANE_EQUALIZATION_CNTL_Reserved_7_7_MASK          0x80

#define NBIF_LANE_EQUALIZATION_CNTL_UPSTREAM_PORT_8GT_TX_PRESET_OFFSET 8
#define NBIF_LANE_EQUALIZATION_CNTL_UPSTREAM_PORT_8GT_TX_PRESET_MASK 0xf00

#define NBIF_LANE_EQUALIZATION_CNTL_UPSTREAM_PORT_8GT_RX_PRESET_HINT_OFFSET 12
#define NBIF_LANE_EQUALIZATION_CNTL_UPSTREAM_PORT_8GT_RX_PRESET_HINT_MASK 0x7000

#define NBIF_LANE_EQUALIZATION_CNTL_Reserved_15_15_OFFSET      15
#define NBIF_LANE_EQUALIZATION_CNTL_Reserved_15_15_MASK        0x8000

typedef union {
  struct {
    UINT16                       DOWNSTREAM_PORT_8GT_TX_PRESET:4;
    UINT16                  DOWNSTREAM_PORT_8GT_RX_PRESET_HINT:3;
    UINT16                                        Reserved_7_7:1;
    UINT16                         UPSTREAM_PORT_8GT_TX_PRESET:4;
    UINT16                    UPSTREAM_PORT_8GT_RX_PRESET_HINT:3;
    UINT16                                      Reserved_15_15:1;
  } Field;
  UINT16 Value;
} PCIE_LANE_EQUALIZATION_CNTL_NBIFRCCFG_STRUCT;

#define PCICFG_NBIFRCCFG_PCIE_LANE_EQUALIZATION_CNTL_OFFSET         0x27c
#define SMN_DEV0_NBIF0RC_N0NBIO0_PCIE_LANE_EQUALIZATION_CNTL_ADDRESS  0x1010027cUL
#define SMN_DEV0_NBIF0RC_N0NBIO1_PCIE_LANE_EQUALIZATION_CNTL_ADDRESS  0x1030027cUL
#define SMN_DEV0_NBIF0RC_N10NBIO0_PCIE_LANE_EQUALIZATION_CNTL_ADDRESS 0x10100290UL
#define SMN_DEV0_NBIF0RC_N10NBIO1_PCIE_LANE_EQUALIZATION_CNTL_ADDRESS 0x10300290UL
#define SMN_DEV0_NBIF0RC_N11NBIO0_PCIE_LANE_EQUALIZATION_CNTL_ADDRESS 0x10100292UL
#define SMN_DEV0_NBIF0RC_N11NBIO1_PCIE_LANE_EQUALIZATION_CNTL_ADDRESS 0x10300292UL
#define SMN_DEV0_NBIF0RC_N12NBIO0_PCIE_LANE_EQUALIZATION_CNTL_ADDRESS 0x10100294UL
#define SMN_DEV0_NBIF0RC_N12NBIO1_PCIE_LANE_EQUALIZATION_CNTL_ADDRESS 0x10300294UL
#define SMN_DEV0_NBIF0RC_N13NBIO0_PCIE_LANE_EQUALIZATION_CNTL_ADDRESS 0x10100296UL
#define SMN_DEV0_NBIF0RC_N13NBIO1_PCIE_LANE_EQUALIZATION_CNTL_ADDRESS 0x10300296UL
#define SMN_DEV0_NBIF0RC_N14NBIO0_PCIE_LANE_EQUALIZATION_CNTL_ADDRESS 0x10100298UL
#define SMN_DEV0_NBIF0RC_N14NBIO1_PCIE_LANE_EQUALIZATION_CNTL_ADDRESS 0x10300298UL
#define SMN_DEV0_NBIF0RC_N15NBIO0_PCIE_LANE_EQUALIZATION_CNTL_ADDRESS 0x1010029aUL
#define SMN_DEV0_NBIF0RC_N15NBIO1_PCIE_LANE_EQUALIZATION_CNTL_ADDRESS 0x1030029aUL
#define SMN_DEV0_NBIF0RC_N1NBIO0_PCIE_LANE_EQUALIZATION_CNTL_ADDRESS  0x1010027eUL
#define SMN_DEV0_NBIF0RC_N1NBIO1_PCIE_LANE_EQUALIZATION_CNTL_ADDRESS  0x1030027eUL
#define SMN_DEV0_NBIF0RC_N2NBIO0_PCIE_LANE_EQUALIZATION_CNTL_ADDRESS  0x10100280UL
#define SMN_DEV0_NBIF0RC_N2NBIO1_PCIE_LANE_EQUALIZATION_CNTL_ADDRESS  0x10300280UL
#define SMN_DEV0_NBIF0RC_N3NBIO0_PCIE_LANE_EQUALIZATION_CNTL_ADDRESS  0x10100282UL
#define SMN_DEV0_NBIF0RC_N3NBIO1_PCIE_LANE_EQUALIZATION_CNTL_ADDRESS  0x10300282UL
#define SMN_DEV0_NBIF0RC_N4NBIO0_PCIE_LANE_EQUALIZATION_CNTL_ADDRESS  0x10100284UL
#define SMN_DEV0_NBIF0RC_N4NBIO1_PCIE_LANE_EQUALIZATION_CNTL_ADDRESS  0x10300284UL
#define SMN_DEV0_NBIF0RC_N5NBIO0_PCIE_LANE_EQUALIZATION_CNTL_ADDRESS  0x10100286UL
#define SMN_DEV0_NBIF0RC_N5NBIO1_PCIE_LANE_EQUALIZATION_CNTL_ADDRESS  0x10300286UL
#define SMN_DEV0_NBIF0RC_N6NBIO0_PCIE_LANE_EQUALIZATION_CNTL_ADDRESS  0x10100288UL
#define SMN_DEV0_NBIF0RC_N6NBIO1_PCIE_LANE_EQUALIZATION_CNTL_ADDRESS  0x10300288UL
#define SMN_DEV0_NBIF0RC_N7NBIO0_PCIE_LANE_EQUALIZATION_CNTL_ADDRESS  0x1010028aUL
#define SMN_DEV0_NBIF0RC_N7NBIO1_PCIE_LANE_EQUALIZATION_CNTL_ADDRESS  0x1030028aUL
#define SMN_DEV0_NBIF0RC_N8NBIO0_PCIE_LANE_EQUALIZATION_CNTL_ADDRESS  0x1010028cUL
#define SMN_DEV0_NBIF0RC_N8NBIO1_PCIE_LANE_EQUALIZATION_CNTL_ADDRESS  0x1030028cUL
#define SMN_DEV0_NBIF0RC_N9NBIO0_PCIE_LANE_EQUALIZATION_CNTL_ADDRESS  0x1010028eUL
#define SMN_DEV0_NBIF0RC_N9NBIO1_PCIE_LANE_EQUALIZATION_CNTL_ADDRESS  0x1030028eUL
#define SMN_DEV0_NBIF1RC_N0NBIO0_PCIE_LANE_EQUALIZATION_CNTL_ADDRESS  0x1020027cUL
#define SMN_DEV0_NBIF1RC_N0NBIO1_PCIE_LANE_EQUALIZATION_CNTL_ADDRESS  0x1040027cUL
#define SMN_DEV0_NBIF1RC_N10NBIO0_PCIE_LANE_EQUALIZATION_CNTL_ADDRESS 0x10200290UL
#define SMN_DEV0_NBIF1RC_N10NBIO1_PCIE_LANE_EQUALIZATION_CNTL_ADDRESS 0x10400290UL
#define SMN_DEV0_NBIF1RC_N11NBIO0_PCIE_LANE_EQUALIZATION_CNTL_ADDRESS 0x10200292UL
#define SMN_DEV0_NBIF1RC_N11NBIO1_PCIE_LANE_EQUALIZATION_CNTL_ADDRESS 0x10400292UL
#define SMN_DEV0_NBIF1RC_N12NBIO0_PCIE_LANE_EQUALIZATION_CNTL_ADDRESS 0x10200294UL
#define SMN_DEV0_NBIF1RC_N12NBIO1_PCIE_LANE_EQUALIZATION_CNTL_ADDRESS 0x10400294UL
#define SMN_DEV0_NBIF1RC_N13NBIO0_PCIE_LANE_EQUALIZATION_CNTL_ADDRESS 0x10200296UL
#define SMN_DEV0_NBIF1RC_N13NBIO1_PCIE_LANE_EQUALIZATION_CNTL_ADDRESS 0x10400296UL
#define SMN_DEV0_NBIF1RC_N14NBIO0_PCIE_LANE_EQUALIZATION_CNTL_ADDRESS 0x10200298UL
#define SMN_DEV0_NBIF1RC_N14NBIO1_PCIE_LANE_EQUALIZATION_CNTL_ADDRESS 0x10400298UL
#define SMN_DEV0_NBIF1RC_N15NBIO0_PCIE_LANE_EQUALIZATION_CNTL_ADDRESS 0x1020029aUL
#define SMN_DEV0_NBIF1RC_N15NBIO1_PCIE_LANE_EQUALIZATION_CNTL_ADDRESS 0x1040029aUL
#define SMN_DEV0_NBIF1RC_N1NBIO0_PCIE_LANE_EQUALIZATION_CNTL_ADDRESS  0x1020027eUL
#define SMN_DEV0_NBIF1RC_N1NBIO1_PCIE_LANE_EQUALIZATION_CNTL_ADDRESS  0x1040027eUL
#define SMN_DEV0_NBIF1RC_N2NBIO0_PCIE_LANE_EQUALIZATION_CNTL_ADDRESS  0x10200280UL
#define SMN_DEV0_NBIF1RC_N2NBIO1_PCIE_LANE_EQUALIZATION_CNTL_ADDRESS  0x10400280UL
#define SMN_DEV0_NBIF1RC_N3NBIO0_PCIE_LANE_EQUALIZATION_CNTL_ADDRESS  0x10200282UL
#define SMN_DEV0_NBIF1RC_N3NBIO1_PCIE_LANE_EQUALIZATION_CNTL_ADDRESS  0x10400282UL
#define SMN_DEV0_NBIF1RC_N4NBIO0_PCIE_LANE_EQUALIZATION_CNTL_ADDRESS  0x10200284UL
#define SMN_DEV0_NBIF1RC_N4NBIO1_PCIE_LANE_EQUALIZATION_CNTL_ADDRESS  0x10400284UL
#define SMN_DEV0_NBIF1RC_N5NBIO0_PCIE_LANE_EQUALIZATION_CNTL_ADDRESS  0x10200286UL
#define SMN_DEV0_NBIF1RC_N5NBIO1_PCIE_LANE_EQUALIZATION_CNTL_ADDRESS  0x10400286UL
#define SMN_DEV0_NBIF1RC_N6NBIO0_PCIE_LANE_EQUALIZATION_CNTL_ADDRESS  0x10200288UL
#define SMN_DEV0_NBIF1RC_N6NBIO1_PCIE_LANE_EQUALIZATION_CNTL_ADDRESS  0x10400288UL
#define SMN_DEV0_NBIF1RC_N7NBIO0_PCIE_LANE_EQUALIZATION_CNTL_ADDRESS  0x1020028aUL
#define SMN_DEV0_NBIF1RC_N7NBIO1_PCIE_LANE_EQUALIZATION_CNTL_ADDRESS  0x1040028aUL
#define SMN_DEV0_NBIF1RC_N8NBIO0_PCIE_LANE_EQUALIZATION_CNTL_ADDRESS  0x1020028cUL
#define SMN_DEV0_NBIF1RC_N8NBIO1_PCIE_LANE_EQUALIZATION_CNTL_ADDRESS  0x1040028cUL
#define SMN_DEV0_NBIF1RC_N9NBIO0_PCIE_LANE_EQUALIZATION_CNTL_ADDRESS  0x1020028eUL
#define SMN_DEV0_NBIF1RC_N9NBIO1_PCIE_LANE_EQUALIZATION_CNTL_ADDRESS  0x1040028eUL
#define SMN_DEV1_NBIF0RC_N0NBIO0_PCIE_LANE_EQUALIZATION_CNTL_ADDRESS  0x1010127cUL
#define SMN_DEV1_NBIF0RC_N0NBIO1_PCIE_LANE_EQUALIZATION_CNTL_ADDRESS  0x1030127cUL
#define SMN_DEV1_NBIF0RC_N10NBIO0_PCIE_LANE_EQUALIZATION_CNTL_ADDRESS 0x10101290UL
#define SMN_DEV1_NBIF0RC_N10NBIO1_PCIE_LANE_EQUALIZATION_CNTL_ADDRESS 0x10301290UL
#define SMN_DEV1_NBIF0RC_N11NBIO0_PCIE_LANE_EQUALIZATION_CNTL_ADDRESS 0x10101292UL
#define SMN_DEV1_NBIF0RC_N11NBIO1_PCIE_LANE_EQUALIZATION_CNTL_ADDRESS 0x10301292UL
#define SMN_DEV1_NBIF0RC_N12NBIO0_PCIE_LANE_EQUALIZATION_CNTL_ADDRESS 0x10101294UL
#define SMN_DEV1_NBIF0RC_N12NBIO1_PCIE_LANE_EQUALIZATION_CNTL_ADDRESS 0x10301294UL
#define SMN_DEV1_NBIF0RC_N13NBIO0_PCIE_LANE_EQUALIZATION_CNTL_ADDRESS 0x10101296UL
#define SMN_DEV1_NBIF0RC_N13NBIO1_PCIE_LANE_EQUALIZATION_CNTL_ADDRESS 0x10301296UL
#define SMN_DEV1_NBIF0RC_N14NBIO0_PCIE_LANE_EQUALIZATION_CNTL_ADDRESS 0x10101298UL
#define SMN_DEV1_NBIF0RC_N14NBIO1_PCIE_LANE_EQUALIZATION_CNTL_ADDRESS 0x10301298UL
#define SMN_DEV1_NBIF0RC_N15NBIO0_PCIE_LANE_EQUALIZATION_CNTL_ADDRESS 0x1010129aUL
#define SMN_DEV1_NBIF0RC_N15NBIO1_PCIE_LANE_EQUALIZATION_CNTL_ADDRESS 0x1030129aUL
#define SMN_DEV1_NBIF0RC_N1NBIO0_PCIE_LANE_EQUALIZATION_CNTL_ADDRESS  0x1010127eUL
#define SMN_DEV1_NBIF0RC_N1NBIO1_PCIE_LANE_EQUALIZATION_CNTL_ADDRESS  0x1030127eUL
#define SMN_DEV1_NBIF0RC_N2NBIO0_PCIE_LANE_EQUALIZATION_CNTL_ADDRESS  0x10101280UL
#define SMN_DEV1_NBIF0RC_N2NBIO1_PCIE_LANE_EQUALIZATION_CNTL_ADDRESS  0x10301280UL
#define SMN_DEV1_NBIF0RC_N3NBIO0_PCIE_LANE_EQUALIZATION_CNTL_ADDRESS  0x10101282UL
#define SMN_DEV1_NBIF0RC_N3NBIO1_PCIE_LANE_EQUALIZATION_CNTL_ADDRESS  0x10301282UL
#define SMN_DEV1_NBIF0RC_N4NBIO0_PCIE_LANE_EQUALIZATION_CNTL_ADDRESS  0x10101284UL
#define SMN_DEV1_NBIF0RC_N4NBIO1_PCIE_LANE_EQUALIZATION_CNTL_ADDRESS  0x10301284UL
#define SMN_DEV1_NBIF0RC_N5NBIO0_PCIE_LANE_EQUALIZATION_CNTL_ADDRESS  0x10101286UL
#define SMN_DEV1_NBIF0RC_N5NBIO1_PCIE_LANE_EQUALIZATION_CNTL_ADDRESS  0x10301286UL
#define SMN_DEV1_NBIF0RC_N6NBIO0_PCIE_LANE_EQUALIZATION_CNTL_ADDRESS  0x10101288UL
#define SMN_DEV1_NBIF0RC_N6NBIO1_PCIE_LANE_EQUALIZATION_CNTL_ADDRESS  0x10301288UL
#define SMN_DEV1_NBIF0RC_N7NBIO0_PCIE_LANE_EQUALIZATION_CNTL_ADDRESS  0x1010128aUL
#define SMN_DEV1_NBIF0RC_N7NBIO1_PCIE_LANE_EQUALIZATION_CNTL_ADDRESS  0x1030128aUL
#define SMN_DEV1_NBIF0RC_N8NBIO0_PCIE_LANE_EQUALIZATION_CNTL_ADDRESS  0x1010128cUL
#define SMN_DEV1_NBIF0RC_N8NBIO1_PCIE_LANE_EQUALIZATION_CNTL_ADDRESS  0x1030128cUL
#define SMN_DEV1_NBIF0RC_N9NBIO0_PCIE_LANE_EQUALIZATION_CNTL_ADDRESS  0x1010128eUL
#define SMN_DEV1_NBIF0RC_N9NBIO1_PCIE_LANE_EQUALIZATION_CNTL_ADDRESS  0x1030128eUL
#define SMN_DEV1_NBIF1RC_N0NBIO0_PCIE_LANE_EQUALIZATION_CNTL_ADDRESS  0x1020127cUL
#define SMN_DEV1_NBIF1RC_N0NBIO1_PCIE_LANE_EQUALIZATION_CNTL_ADDRESS  0x1040127cUL
#define SMN_DEV1_NBIF1RC_N10NBIO0_PCIE_LANE_EQUALIZATION_CNTL_ADDRESS 0x10201290UL
#define SMN_DEV1_NBIF1RC_N10NBIO1_PCIE_LANE_EQUALIZATION_CNTL_ADDRESS 0x10401290UL
#define SMN_DEV1_NBIF1RC_N11NBIO0_PCIE_LANE_EQUALIZATION_CNTL_ADDRESS 0x10201292UL
#define SMN_DEV1_NBIF1RC_N11NBIO1_PCIE_LANE_EQUALIZATION_CNTL_ADDRESS 0x10401292UL
#define SMN_DEV1_NBIF1RC_N12NBIO0_PCIE_LANE_EQUALIZATION_CNTL_ADDRESS 0x10201294UL
#define SMN_DEV1_NBIF1RC_N12NBIO1_PCIE_LANE_EQUALIZATION_CNTL_ADDRESS 0x10401294UL
#define SMN_DEV1_NBIF1RC_N13NBIO0_PCIE_LANE_EQUALIZATION_CNTL_ADDRESS 0x10201296UL
#define SMN_DEV1_NBIF1RC_N13NBIO1_PCIE_LANE_EQUALIZATION_CNTL_ADDRESS 0x10401296UL
#define SMN_DEV1_NBIF1RC_N14NBIO0_PCIE_LANE_EQUALIZATION_CNTL_ADDRESS 0x10201298UL
#define SMN_DEV1_NBIF1RC_N14NBIO1_PCIE_LANE_EQUALIZATION_CNTL_ADDRESS 0x10401298UL
#define SMN_DEV1_NBIF1RC_N15NBIO0_PCIE_LANE_EQUALIZATION_CNTL_ADDRESS 0x1020129aUL
#define SMN_DEV1_NBIF1RC_N15NBIO1_PCIE_LANE_EQUALIZATION_CNTL_ADDRESS 0x1040129aUL
#define SMN_DEV1_NBIF1RC_N1NBIO0_PCIE_LANE_EQUALIZATION_CNTL_ADDRESS  0x1020127eUL
#define SMN_DEV1_NBIF1RC_N1NBIO1_PCIE_LANE_EQUALIZATION_CNTL_ADDRESS  0x1040127eUL
#define SMN_DEV1_NBIF1RC_N2NBIO0_PCIE_LANE_EQUALIZATION_CNTL_ADDRESS  0x10201280UL
#define SMN_DEV1_NBIF1RC_N2NBIO1_PCIE_LANE_EQUALIZATION_CNTL_ADDRESS  0x10401280UL
#define SMN_DEV1_NBIF1RC_N3NBIO0_PCIE_LANE_EQUALIZATION_CNTL_ADDRESS  0x10201282UL
#define SMN_DEV1_NBIF1RC_N3NBIO1_PCIE_LANE_EQUALIZATION_CNTL_ADDRESS  0x10401282UL
#define SMN_DEV1_NBIF1RC_N4NBIO0_PCIE_LANE_EQUALIZATION_CNTL_ADDRESS  0x10201284UL
#define SMN_DEV1_NBIF1RC_N4NBIO1_PCIE_LANE_EQUALIZATION_CNTL_ADDRESS  0x10401284UL
#define SMN_DEV1_NBIF1RC_N5NBIO0_PCIE_LANE_EQUALIZATION_CNTL_ADDRESS  0x10201286UL
#define SMN_DEV1_NBIF1RC_N5NBIO1_PCIE_LANE_EQUALIZATION_CNTL_ADDRESS  0x10401286UL
#define SMN_DEV1_NBIF1RC_N6NBIO0_PCIE_LANE_EQUALIZATION_CNTL_ADDRESS  0x10201288UL
#define SMN_DEV1_NBIF1RC_N6NBIO1_PCIE_LANE_EQUALIZATION_CNTL_ADDRESS  0x10401288UL
#define SMN_DEV1_NBIF1RC_N7NBIO0_PCIE_LANE_EQUALIZATION_CNTL_ADDRESS  0x1020128aUL
#define SMN_DEV1_NBIF1RC_N7NBIO1_PCIE_LANE_EQUALIZATION_CNTL_ADDRESS  0x1040128aUL
#define SMN_DEV1_NBIF1RC_N8NBIO0_PCIE_LANE_EQUALIZATION_CNTL_ADDRESS  0x1020128cUL
#define SMN_DEV1_NBIF1RC_N8NBIO1_PCIE_LANE_EQUALIZATION_CNTL_ADDRESS  0x1040128cUL
#define SMN_DEV1_NBIF1RC_N9NBIO0_PCIE_LANE_EQUALIZATION_CNTL_ADDRESS  0x1020128eUL
#define SMN_DEV1_NBIF1RC_N9NBIO1_PCIE_LANE_EQUALIZATION_CNTL_ADDRESS  0x1040128eUL


/***********************************************************
* Register Name : PCIE_LANE_ERROR_STATUS
************************************************************/

#define NBIF_LANE_ERROR_STATUS_LANE_ERROR_STATUS_BITS_OFFSET   0
#define NBIF_LANE_ERROR_STATUS_LANE_ERROR_STATUS_BITS_MASK     0xffff

#define NBIF_LANE_ERROR_STATUS_Reserved_31_16_OFFSET           16
#define NBIF_LANE_ERROR_STATUS_Reserved_31_16_MASK             0xffff0000

typedef union {
  struct {
    UINT32                              LANE_ERROR_STATUS_BITS:16;
    UINT32                                      Reserved_31_16:16;
  } Field;
  UINT32 Value;
} PCIE_LANE_ERROR_STATUS_NBIFRCCFG_STRUCT;

#define PCICFG_NBIFRCCFG_PCIE_LANE_ERROR_STATUS_OFFSET              0x278
#define SMN_DEV0_NBIF0RCNBIO0_PCIE_LANE_ERROR_STATUS_ADDRESS          0x10100278UL
#define SMN_DEV0_NBIF0RCNBIO1_PCIE_LANE_ERROR_STATUS_ADDRESS          0x10300278UL
#define SMN_DEV0_NBIF1RCNBIO0_PCIE_LANE_ERROR_STATUS_ADDRESS          0x10200278UL
#define SMN_DEV0_NBIF1RCNBIO1_PCIE_LANE_ERROR_STATUS_ADDRESS          0x10400278UL
#define SMN_DEV1_NBIF0RCNBIO0_PCIE_LANE_ERROR_STATUS_ADDRESS          0x10101278UL
#define SMN_DEV1_NBIF0RCNBIO1_PCIE_LANE_ERROR_STATUS_ADDRESS          0x10301278UL
#define SMN_DEV1_NBIF1RCNBIO0_PCIE_LANE_ERROR_STATUS_ADDRESS          0x10201278UL
#define SMN_DEV1_NBIF1RCNBIO1_PCIE_LANE_ERROR_STATUS_ADDRESS          0x10401278UL


/***********************************************************
* Register Name : PCIE_LINK_CNTL3
************************************************************/

#define NBIF_LINK_CNTL3_PERFORM_EQUALIZATION_OFFSET            0
#define NBIF_LINK_CNTL3_PERFORM_EQUALIZATION_MASK              0x1

#define NBIF_LINK_CNTL3_LINK_EQUALIZATION_REQ_INT_EN_OFFSET    1
#define NBIF_LINK_CNTL3_LINK_EQUALIZATION_REQ_INT_EN_MASK      0x2

#define NBIF_LINK_CNTL3_Reserved_8_2_OFFSET                    2
#define NBIF_LINK_CNTL3_Reserved_8_2_MASK                      0x1fc

#define NBIF_LINK_CNTL3_ENABLE_LOWER_SKP_OS_GEN_OFFSET         9
#define NBIF_LINK_CNTL3_ENABLE_LOWER_SKP_OS_GEN_MASK           0xfe00

#define NBIF_LINK_CNTL3_Reserved_31_16_OFFSET                  16
#define NBIF_LINK_CNTL3_Reserved_31_16_MASK                    0xffff0000

typedef union {
  struct {
    UINT32                                PERFORM_EQUALIZATION:1;
    UINT32                        LINK_EQUALIZATION_REQ_INT_EN:1;
    UINT32                                        Reserved_8_2:7;
    UINT32                             ENABLE_LOWER_SKP_OS_GEN:7;
    UINT32                                      Reserved_31_16:16;
  } Field;
  UINT32 Value;
} PCIE_LINK_CNTL3_NBIFRCCFG_STRUCT;

#define PCICFG_NBIFRCCFG_PCIE_LINK_CNTL3_OFFSET                     0x274
#define SMN_DEV0_NBIF0RCNBIO0_PCIE_LINK_CNTL3_ADDRESS                 0x10100274UL
#define SMN_DEV0_NBIF0RCNBIO1_PCIE_LINK_CNTL3_ADDRESS                 0x10300274UL
#define SMN_DEV0_NBIF1RCNBIO0_PCIE_LINK_CNTL3_ADDRESS                 0x10200274UL
#define SMN_DEV0_NBIF1RCNBIO1_PCIE_LINK_CNTL3_ADDRESS                 0x10400274UL
#define SMN_DEV1_NBIF0RCNBIO0_PCIE_LINK_CNTL3_ADDRESS                 0x10101274UL
#define SMN_DEV1_NBIF0RCNBIO1_PCIE_LINK_CNTL3_ADDRESS                 0x10301274UL
#define SMN_DEV1_NBIF1RCNBIO0_PCIE_LINK_CNTL3_ADDRESS                 0x10201274UL
#define SMN_DEV1_NBIF1RCNBIO1_PCIE_LINK_CNTL3_ADDRESS                 0x10401274UL


/***********************************************************
* Register Name : PCIE_MARGINING_ENH_CAP_LIST
************************************************************/

#define NBIF_MARGINING_ENH_CAP_LIST_CAP_ID_OFFSET              0
#define NBIF_MARGINING_ENH_CAP_LIST_CAP_ID_MASK                0xffff

#define NBIF_MARGINING_ENH_CAP_LIST_CAP_VER_OFFSET             16
#define NBIF_MARGINING_ENH_CAP_LIST_CAP_VER_MASK               0xf0000

#define NBIF_MARGINING_ENH_CAP_LIST_NEXT_PTR_OFFSET            20
#define NBIF_MARGINING_ENH_CAP_LIST_NEXT_PTR_MASK              0xfff00000

typedef union {
  struct {
    UINT32                                              CAP_ID:16;
    UINT32                                             CAP_VER:4;
    UINT32                                            NEXT_PTR:12;
  } Field;
  UINT32 Value;
} PCIE_MARGINING_ENH_CAP_LIST_NBIFRCCFG_STRUCT;

#define PCICFG_NBIFRCCFG_PCIE_MARGINING_ENH_CAP_LIST_OFFSET         0x450
#define SMN_DEV0_NBIF0RCNBIO0_PCIE_MARGINING_ENH_CAP_LIST_ADDRESS     0x10100450UL
#define SMN_DEV0_NBIF0RCNBIO1_PCIE_MARGINING_ENH_CAP_LIST_ADDRESS     0x10300450UL
#define SMN_DEV0_NBIF1RCNBIO0_PCIE_MARGINING_ENH_CAP_LIST_ADDRESS     0x10200450UL
#define SMN_DEV0_NBIF1RCNBIO1_PCIE_MARGINING_ENH_CAP_LIST_ADDRESS     0x10400450UL
#define SMN_DEV1_NBIF0RCNBIO0_PCIE_MARGINING_ENH_CAP_LIST_ADDRESS     0x10101450UL
#define SMN_DEV1_NBIF0RCNBIO1_PCIE_MARGINING_ENH_CAP_LIST_ADDRESS     0x10301450UL
#define SMN_DEV1_NBIF1RCNBIO0_PCIE_MARGINING_ENH_CAP_LIST_ADDRESS     0x10201450UL
#define SMN_DEV1_NBIF1RCNBIO1_PCIE_MARGINING_ENH_CAP_LIST_ADDRESS     0x10401450UL


/***********************************************************
* Register Name : PCIE_PHY_16GT_ENH_CAP_LIST
************************************************************/

#define NBIF_PHY_16GT_ENH_CAP_LIST_CAP_ID_OFFSET               0
#define NBIF_PHY_16GT_ENH_CAP_LIST_CAP_ID_MASK                 0xffff

#define NBIF_PHY_16GT_ENH_CAP_LIST_CAP_VER_OFFSET              16
#define NBIF_PHY_16GT_ENH_CAP_LIST_CAP_VER_MASK                0xf0000

#define NBIF_PHY_16GT_ENH_CAP_LIST_NEXT_PTR_OFFSET             20
#define NBIF_PHY_16GT_ENH_CAP_LIST_NEXT_PTR_MASK               0xfff00000

typedef union {
  struct {
    UINT32                                              CAP_ID:16;
    UINT32                                             CAP_VER:4;
    UINT32                                            NEXT_PTR:12;
  } Field;
  UINT32 Value;
} PCIE_PHY_16GT_ENH_CAP_LIST_NBIFRCCFG_STRUCT;

#define PCICFG_NBIFRCCFG_PCIE_PHY_16GT_ENH_CAP_LIST_OFFSET          0x410
#define SMN_DEV0_NBIF0RCNBIO0_PCIE_PHY_16GT_ENH_CAP_LIST_ADDRESS      0x10100410UL
#define SMN_DEV0_NBIF0RCNBIO1_PCIE_PHY_16GT_ENH_CAP_LIST_ADDRESS      0x10300410UL
#define SMN_DEV0_NBIF1RCNBIO0_PCIE_PHY_16GT_ENH_CAP_LIST_ADDRESS      0x10200410UL
#define SMN_DEV0_NBIF1RCNBIO1_PCIE_PHY_16GT_ENH_CAP_LIST_ADDRESS      0x10400410UL
#define SMN_DEV1_NBIF0RCNBIO0_PCIE_PHY_16GT_ENH_CAP_LIST_ADDRESS      0x10101410UL
#define SMN_DEV1_NBIF0RCNBIO1_PCIE_PHY_16GT_ENH_CAP_LIST_ADDRESS      0x10301410UL
#define SMN_DEV1_NBIF1RCNBIO0_PCIE_PHY_16GT_ENH_CAP_LIST_ADDRESS      0x10201410UL
#define SMN_DEV1_NBIF1RCNBIO1_PCIE_PHY_16GT_ENH_CAP_LIST_ADDRESS      0x10401410UL


/***********************************************************
* Register Name : PCIE_PHY_32GT_ENH_CAP_LIST
************************************************************/

#define NBIF_PHY_32GT_ENH_CAP_LIST_CAP_ID_OFFSET               0
#define NBIF_PHY_32GT_ENH_CAP_LIST_CAP_ID_MASK                 0xffff

#define NBIF_PHY_32GT_ENH_CAP_LIST_CAP_VER_OFFSET              16
#define NBIF_PHY_32GT_ENH_CAP_LIST_CAP_VER_MASK                0xf0000

#define NBIF_PHY_32GT_ENH_CAP_LIST_NEXT_PTR_OFFSET             20
#define NBIF_PHY_32GT_ENH_CAP_LIST_NEXT_PTR_MASK               0xfff00000

typedef union {
  struct {
    UINT32                                              CAP_ID:16;
    UINT32                                             CAP_VER:4;
    UINT32                                            NEXT_PTR:12;
  } Field;
  UINT32 Value;
} PCIE_PHY_32GT_ENH_CAP_LIST_NBIFRCCFG_STRUCT;

#define PCICFG_NBIFRCCFG_PCIE_PHY_32GT_ENH_CAP_LIST_OFFSET          0x500
#define SMN_DEV0_NBIF0RCNBIO0_PCIE_PHY_32GT_ENH_CAP_LIST_ADDRESS      0x10100500UL
#define SMN_DEV0_NBIF0RCNBIO1_PCIE_PHY_32GT_ENH_CAP_LIST_ADDRESS      0x10300500UL
#define SMN_DEV0_NBIF1RCNBIO0_PCIE_PHY_32GT_ENH_CAP_LIST_ADDRESS      0x10200500UL
#define SMN_DEV0_NBIF1RCNBIO1_PCIE_PHY_32GT_ENH_CAP_LIST_ADDRESS      0x10400500UL
#define SMN_DEV1_NBIF0RCNBIO0_PCIE_PHY_32GT_ENH_CAP_LIST_ADDRESS      0x10101500UL
#define SMN_DEV1_NBIF0RCNBIO1_PCIE_PHY_32GT_ENH_CAP_LIST_ADDRESS      0x10301500UL
#define SMN_DEV1_NBIF1RCNBIO0_PCIE_PHY_32GT_ENH_CAP_LIST_ADDRESS      0x10201500UL
#define SMN_DEV1_NBIF1RCNBIO1_PCIE_PHY_32GT_ENH_CAP_LIST_ADDRESS      0x10401500UL


/***********************************************************
* Register Name : PCIE_PORT_VC_CAP_REG1
************************************************************/

#define NBIF_PORT_VC_CAP_REG1_EXT_VC_COUNT_OFFSET              0
#define NBIF_PORT_VC_CAP_REG1_EXT_VC_COUNT_MASK                0x7

#define NBIF_PORT_VC_CAP_REG1_Reserved_3_3_OFFSET              3
#define NBIF_PORT_VC_CAP_REG1_Reserved_3_3_MASK                0x8

#define NBIF_PORT_VC_CAP_REG1_LOW_PRIORITY_EXT_VC_COUNT_OFFSET 4
#define NBIF_PORT_VC_CAP_REG1_LOW_PRIORITY_EXT_VC_COUNT_MASK   0x70

#define NBIF_PORT_VC_CAP_REG1_Reserved_7_7_OFFSET              7
#define NBIF_PORT_VC_CAP_REG1_Reserved_7_7_MASK                0x80

#define NBIF_PORT_VC_CAP_REG1_REF_CLK_OFFSET                   8
#define NBIF_PORT_VC_CAP_REG1_REF_CLK_MASK                     0x300

#define NBIF_PORT_VC_CAP_REG1_PORT_ARB_TABLE_ENTRY_SIZE_OFFSET 10
#define NBIF_PORT_VC_CAP_REG1_PORT_ARB_TABLE_ENTRY_SIZE_MASK   0xc00

#define NBIF_PORT_VC_CAP_REG1_Reserved_31_12_OFFSET            12
#define NBIF_PORT_VC_CAP_REG1_Reserved_31_12_MASK              0xfffff000

typedef union {
  struct {
    UINT32                                        EXT_VC_COUNT:3;
    UINT32                                        Reserved_3_3:1;
    UINT32                           LOW_PRIORITY_EXT_VC_COUNT:3;
    UINT32                                        Reserved_7_7:1;
    UINT32                                             REF_CLK:2;
    UINT32                           PORT_ARB_TABLE_ENTRY_SIZE:2;
    UINT32                                      Reserved_31_12:20;
  } Field;
  UINT32 Value;
} PCIE_PORT_VC_CAP_REG1_NBIFRCCFG_STRUCT;

#define PCICFG_NBIFRCCFG_PCIE_PORT_VC_CAP_REG1_OFFSET               0x114
#define SMN_DEV0_NBIF0RCNBIO0_PCIE_PORT_VC_CAP_REG1_ADDRESS           0x10100114UL
#define SMN_DEV0_NBIF0RCNBIO1_PCIE_PORT_VC_CAP_REG1_ADDRESS           0x10300114UL
#define SMN_DEV0_NBIF1RCNBIO0_PCIE_PORT_VC_CAP_REG1_ADDRESS           0x10200114UL
#define SMN_DEV0_NBIF1RCNBIO1_PCIE_PORT_VC_CAP_REG1_ADDRESS           0x10400114UL
#define SMN_DEV1_NBIF0RCNBIO0_PCIE_PORT_VC_CAP_REG1_ADDRESS           0x10101114UL
#define SMN_DEV1_NBIF0RCNBIO1_PCIE_PORT_VC_CAP_REG1_ADDRESS           0x10301114UL
#define SMN_DEV1_NBIF1RCNBIO0_PCIE_PORT_VC_CAP_REG1_ADDRESS           0x10201114UL
#define SMN_DEV1_NBIF1RCNBIO1_PCIE_PORT_VC_CAP_REG1_ADDRESS           0x10401114UL


/***********************************************************
* Register Name : PCIE_PORT_VC_CAP_REG2
************************************************************/

#define NBIF_PORT_VC_CAP_REG2_VC_ARB_CAP_OFFSET                0
#define NBIF_PORT_VC_CAP_REG2_VC_ARB_CAP_MASK                  0xff

#define NBIF_PORT_VC_CAP_REG2_Reserved_23_8_OFFSET             8
#define NBIF_PORT_VC_CAP_REG2_Reserved_23_8_MASK               0xffff00

#define NBIF_PORT_VC_CAP_REG2_VC_ARB_TABLE_OFFSET_OFFSET       24
#define NBIF_PORT_VC_CAP_REG2_VC_ARB_TABLE_OFFSET_MASK         0xff000000

typedef union {
  struct {
    UINT32                                          VC_ARB_CAP:8;
    UINT32                                       Reserved_23_8:16;
    UINT32                                 VC_ARB_TABLE_OFFSET:8;
  } Field;
  UINT32 Value;
} PCIE_PORT_VC_CAP_REG2_NBIFRCCFG_STRUCT;

#define PCICFG_NBIFRCCFG_PCIE_PORT_VC_CAP_REG2_OFFSET               0x118
#define SMN_DEV0_NBIF0RCNBIO0_PCIE_PORT_VC_CAP_REG2_ADDRESS           0x10100118UL
#define SMN_DEV0_NBIF0RCNBIO1_PCIE_PORT_VC_CAP_REG2_ADDRESS           0x10300118UL
#define SMN_DEV0_NBIF1RCNBIO0_PCIE_PORT_VC_CAP_REG2_ADDRESS           0x10200118UL
#define SMN_DEV0_NBIF1RCNBIO1_PCIE_PORT_VC_CAP_REG2_ADDRESS           0x10400118UL
#define SMN_DEV1_NBIF0RCNBIO0_PCIE_PORT_VC_CAP_REG2_ADDRESS           0x10101118UL
#define SMN_DEV1_NBIF0RCNBIO1_PCIE_PORT_VC_CAP_REG2_ADDRESS           0x10301118UL
#define SMN_DEV1_NBIF1RCNBIO0_PCIE_PORT_VC_CAP_REG2_ADDRESS           0x10201118UL
#define SMN_DEV1_NBIF1RCNBIO1_PCIE_PORT_VC_CAP_REG2_ADDRESS           0x10401118UL


/***********************************************************
* Register Name : PCIE_PORT_VC_CNTL
************************************************************/

#define NBIF_PORT_VC_CNTL_LOAD_VC_ARB_TABLE_OFFSET             0
#define NBIF_PORT_VC_CNTL_LOAD_VC_ARB_TABLE_MASK               0x1

#define NBIF_PORT_VC_CNTL_VC_ARB_SELECT_OFFSET                 1
#define NBIF_PORT_VC_CNTL_VC_ARB_SELECT_MASK                   0xe

#define NBIF_PORT_VC_CNTL_Reserved_15_4_OFFSET                 4
#define NBIF_PORT_VC_CNTL_Reserved_15_4_MASK                   0xfff0

typedef union {
  struct {
    UINT16                                   LOAD_VC_ARB_TABLE:1;
    UINT16                                       VC_ARB_SELECT:3;
    UINT16                                       Reserved_15_4:12;
  } Field;
  UINT16 Value;
} PCIE_PORT_VC_CNTL_NBIFRCCFG_STRUCT;

#define PCICFG_NBIFRCCFG_PCIE_PORT_VC_CNTL_OFFSET                   0x11c
#define SMN_DEV0_NBIF0RCNBIO0_PCIE_PORT_VC_CNTL_ADDRESS               0x1010011cUL
#define SMN_DEV0_NBIF0RCNBIO1_PCIE_PORT_VC_CNTL_ADDRESS               0x1030011cUL
#define SMN_DEV0_NBIF1RCNBIO0_PCIE_PORT_VC_CNTL_ADDRESS               0x1020011cUL
#define SMN_DEV0_NBIF1RCNBIO1_PCIE_PORT_VC_CNTL_ADDRESS               0x1040011cUL
#define SMN_DEV1_NBIF0RCNBIO0_PCIE_PORT_VC_CNTL_ADDRESS               0x1010111cUL
#define SMN_DEV1_NBIF0RCNBIO1_PCIE_PORT_VC_CNTL_ADDRESS               0x1030111cUL
#define SMN_DEV1_NBIF1RCNBIO0_PCIE_PORT_VC_CNTL_ADDRESS               0x1020111cUL
#define SMN_DEV1_NBIF1RCNBIO1_PCIE_PORT_VC_CNTL_ADDRESS               0x1040111cUL


/***********************************************************
* Register Name : PCIE_PORT_VC_STATUS
************************************************************/

#define NBIF_PORT_VC_STATUS_VC_ARB_TABLE_STATUS_OFFSET         0
#define NBIF_PORT_VC_STATUS_VC_ARB_TABLE_STATUS_MASK           0x1

#define NBIF_PORT_VC_STATUS_Reserved_15_1_OFFSET               1
#define NBIF_PORT_VC_STATUS_Reserved_15_1_MASK                 0xfffe

typedef union {
  struct {
    UINT16                                 VC_ARB_TABLE_STATUS:1;
    UINT16                                       Reserved_15_1:15;
  } Field;
  UINT16 Value;
} PCIE_PORT_VC_STATUS_NBIFRCCFG_STRUCT;

#define PCICFG_NBIFRCCFG_PCIE_PORT_VC_STATUS_OFFSET                 0x11e
#define SMN_DEV0_NBIF0RCNBIO0_PCIE_PORT_VC_STATUS_ADDRESS             0x1010011eUL
#define SMN_DEV0_NBIF0RCNBIO1_PCIE_PORT_VC_STATUS_ADDRESS             0x1030011eUL
#define SMN_DEV0_NBIF1RCNBIO0_PCIE_PORT_VC_STATUS_ADDRESS             0x1020011eUL
#define SMN_DEV0_NBIF1RCNBIO1_PCIE_PORT_VC_STATUS_ADDRESS             0x1040011eUL
#define SMN_DEV1_NBIF0RCNBIO0_PCIE_PORT_VC_STATUS_ADDRESS             0x1010111eUL
#define SMN_DEV1_NBIF0RCNBIO1_PCIE_PORT_VC_STATUS_ADDRESS             0x1030111eUL
#define SMN_DEV1_NBIF1RCNBIO0_PCIE_PORT_VC_STATUS_ADDRESS             0x1020111eUL
#define SMN_DEV1_NBIF1RCNBIO1_PCIE_PORT_VC_STATUS_ADDRESS             0x1040111eUL


/***********************************************************
* Register Name : PCIE_ROOT_ERR_CMD
************************************************************/

#define NBIF_ROOT_ERR_CMD_CORR_ERR_REP_EN_OFFSET               0
#define NBIF_ROOT_ERR_CMD_CORR_ERR_REP_EN_MASK                 0x1

#define NBIF_ROOT_ERR_CMD_NONFATAL_ERR_REP_EN_OFFSET           1
#define NBIF_ROOT_ERR_CMD_NONFATAL_ERR_REP_EN_MASK             0x2

#define NBIF_ROOT_ERR_CMD_FATAL_ERR_REP_EN_OFFSET              2
#define NBIF_ROOT_ERR_CMD_FATAL_ERR_REP_EN_MASK                0x4

#define NBIF_ROOT_ERR_CMD_Reserved_31_3_OFFSET                 3
#define NBIF_ROOT_ERR_CMD_Reserved_31_3_MASK                   0xfffffff8

typedef union {
  struct {
    UINT32                                     CORR_ERR_REP_EN:1;
    UINT32                                 NONFATAL_ERR_REP_EN:1;
    UINT32                                    FATAL_ERR_REP_EN:1;
    UINT32                                       Reserved_31_3:29;
  } Field;
  UINT32 Value;
} PCIE_ROOT_ERR_CMD_STRUCT;

#define PCICFG_NBIFRCCFG_PCIE_ROOT_ERR_CMD_OFFSET                   0x17c
#define SMN_DEV0_NBIF0RCNBIO0_PCIE_ROOT_ERR_CMD_ADDRESS               0x1010017cUL
#define SMN_DEV0_NBIF0RCNBIO1_PCIE_ROOT_ERR_CMD_ADDRESS               0x1030017cUL
#define SMN_DEV0_NBIF1RCNBIO0_PCIE_ROOT_ERR_CMD_ADDRESS               0x1020017cUL
#define SMN_DEV0_NBIF1RCNBIO1_PCIE_ROOT_ERR_CMD_ADDRESS               0x1040017cUL
#define SMN_DEV1_NBIF0RCNBIO0_PCIE_ROOT_ERR_CMD_ADDRESS               0x1010117cUL
#define SMN_DEV1_NBIF0RCNBIO1_PCIE_ROOT_ERR_CMD_ADDRESS               0x1030117cUL
#define SMN_DEV1_NBIF1RCNBIO0_PCIE_ROOT_ERR_CMD_ADDRESS               0x1020117cUL
#define SMN_DEV1_NBIF1RCNBIO1_PCIE_ROOT_ERR_CMD_ADDRESS               0x1040117cUL


/***********************************************************
* Register Name : PCIE_ROOT_ERR_STATUS
************************************************************/

#define NBIF_ROOT_ERR_STATUS_ERR_CORR_RCVD_OFFSET              0
#define NBIF_ROOT_ERR_STATUS_ERR_CORR_RCVD_MASK                0x1

#define NBIF_ROOT_ERR_STATUS_MULT_ERR_CORR_RCVD_OFFSET         1
#define NBIF_ROOT_ERR_STATUS_MULT_ERR_CORR_RCVD_MASK           0x2

#define NBIF_ROOT_ERR_STATUS_ERR_FATAL_NONFATAL_RCVD_OFFSET    2
#define NBIF_ROOT_ERR_STATUS_ERR_FATAL_NONFATAL_RCVD_MASK      0x4

#define NBIF_ROOT_ERR_STATUS_MULT_ERR_FATAL_NONFATAL_RCVD_OFFSET 3
#define NBIF_ROOT_ERR_STATUS_MULT_ERR_FATAL_NONFATAL_RCVD_MASK 0x8

#define NBIF_ROOT_ERR_STATUS_FIRST_UNCORRECTABLE_FATAL_OFFSET  4
#define NBIF_ROOT_ERR_STATUS_FIRST_UNCORRECTABLE_FATAL_MASK    0x10

#define NBIF_ROOT_ERR_STATUS_NONFATAL_ERROR_MSG_RCVD_OFFSET    5
#define NBIF_ROOT_ERR_STATUS_NONFATAL_ERROR_MSG_RCVD_MASK      0x20

#define NBIF_ROOT_ERR_STATUS_FATAL_ERROR_MSG_RCVD_OFFSET       6
#define NBIF_ROOT_ERR_STATUS_FATAL_ERROR_MSG_RCVD_MASK         0x40

#define NBIF_ROOT_ERR_STATUS_ERR_COR_SUBCLASS_OFFSET           7
#define NBIF_ROOT_ERR_STATUS_ERR_COR_SUBCLASS_MASK             0x180

#define NBIF_ROOT_ERR_STATUS_Reserved_26_9_OFFSET              9
#define NBIF_ROOT_ERR_STATUS_Reserved_26_9_MASK                0x7fffe00

#define NBIF_ROOT_ERR_STATUS_ADV_ERR_INT_MSG_NUM_OFFSET        27
#define NBIF_ROOT_ERR_STATUS_ADV_ERR_INT_MSG_NUM_MASK          0xf8000000

typedef union {
  struct {
    UINT32                                       ERR_CORR_RCVD:1;
    UINT32                                  MULT_ERR_CORR_RCVD:1;
    UINT32                             ERR_FATAL_NONFATAL_RCVD:1;
    UINT32                        MULT_ERR_FATAL_NONFATAL_RCVD:1;
    UINT32                           FIRST_UNCORRECTABLE_FATAL:1;
    UINT32                             NONFATAL_ERROR_MSG_RCVD:1;
    UINT32                                FATAL_ERROR_MSG_RCVD:1;
    UINT32                                    ERR_COR_SUBCLASS:2;
    UINT32                                       Reserved_26_9:18;
    UINT32                                 ADV_ERR_INT_MSG_NUM:5;
  } Field;
  UINT32 Value;
} PCIE_ROOT_ERR_STATUS_STRUCT;

#define PCICFG_NBIFRCCFG_PCIE_ROOT_ERR_STATUS_OFFSET                0x180
#define SMN_DEV0_NBIF0RCNBIO0_PCIE_ROOT_ERR_STATUS_ADDRESS            0x10100180UL
#define SMN_DEV0_NBIF0RCNBIO1_PCIE_ROOT_ERR_STATUS_ADDRESS            0x10300180UL
#define SMN_DEV0_NBIF1RCNBIO0_PCIE_ROOT_ERR_STATUS_ADDRESS            0x10200180UL
#define SMN_DEV0_NBIF1RCNBIO1_PCIE_ROOT_ERR_STATUS_ADDRESS            0x10400180UL
#define SMN_DEV1_NBIF0RCNBIO0_PCIE_ROOT_ERR_STATUS_ADDRESS            0x10101180UL
#define SMN_DEV1_NBIF0RCNBIO1_PCIE_ROOT_ERR_STATUS_ADDRESS            0x10301180UL
#define SMN_DEV1_NBIF1RCNBIO0_PCIE_ROOT_ERR_STATUS_ADDRESS            0x10201180UL
#define SMN_DEV1_NBIF1RCNBIO1_PCIE_ROOT_ERR_STATUS_ADDRESS            0x10401180UL


/***********************************************************
* Register Name : PCIE_RTR_ENH_CAP_LIST
************************************************************/

#define NBIF_RTR_ENH_CAP_LIST_CAP_ID_OFFSET                    0
#define NBIF_RTR_ENH_CAP_LIST_CAP_ID_MASK                      0xffff

#define NBIF_RTR_ENH_CAP_LIST_CAP_VER_OFFSET                   16
#define NBIF_RTR_ENH_CAP_LIST_CAP_VER_MASK                     0xf0000

#define NBIF_RTR_ENH_CAP_LIST_NEXT_PTR_OFFSET                  20
#define NBIF_RTR_ENH_CAP_LIST_NEXT_PTR_MASK                    0xfff00000

typedef union {
  struct {
    UINT32                                              CAP_ID:16;
    UINT32                                             CAP_VER:4;
    UINT32                                            NEXT_PTR:12;
  } Field;
  UINT32 Value;
} PCIE_RTR_ENH_CAP_LIST_NBIFRCCFG_STRUCT;

#define PCICFG_NBIFRCCFG_PCIE_RTR_ENH_CAP_LIST_OFFSET               0x570
#define SMN_DEV0_NBIF0RCNBIO0_PCIE_RTR_ENH_CAP_LIST_ADDRESS           0x10100570UL
#define SMN_DEV0_NBIF0RCNBIO1_PCIE_RTR_ENH_CAP_LIST_ADDRESS           0x10300570UL
#define SMN_DEV0_NBIF1RCNBIO0_PCIE_RTR_ENH_CAP_LIST_ADDRESS           0x10200570UL
#define SMN_DEV0_NBIF1RCNBIO1_PCIE_RTR_ENH_CAP_LIST_ADDRESS           0x10400570UL
#define SMN_DEV1_NBIF0RCNBIO0_PCIE_RTR_ENH_CAP_LIST_ADDRESS           0x10101570UL
#define SMN_DEV1_NBIF0RCNBIO1_PCIE_RTR_ENH_CAP_LIST_ADDRESS           0x10301570UL
#define SMN_DEV1_NBIF1RCNBIO0_PCIE_RTR_ENH_CAP_LIST_ADDRESS           0x10201570UL
#define SMN_DEV1_NBIF1RCNBIO1_PCIE_RTR_ENH_CAP_LIST_ADDRESS           0x10401570UL


/***********************************************************
* Register Name : PCIE_SECONDARY_ENH_CAP_LIST
************************************************************/

#define NBIF_SECONDARY_ENH_CAP_LIST_CAP_ID_OFFSET              0
#define NBIF_SECONDARY_ENH_CAP_LIST_CAP_ID_MASK                0xffff

#define NBIF_SECONDARY_ENH_CAP_LIST_CAP_VER_OFFSET             16
#define NBIF_SECONDARY_ENH_CAP_LIST_CAP_VER_MASK               0xf0000

#define NBIF_SECONDARY_ENH_CAP_LIST_NEXT_PTR_OFFSET            20
#define NBIF_SECONDARY_ENH_CAP_LIST_NEXT_PTR_MASK              0xfff00000

typedef union {
  struct {
    UINT32                                              CAP_ID:16;
    UINT32                                             CAP_VER:4;
    UINT32                                            NEXT_PTR:12;
  } Field;
  UINT32 Value;
} PCIE_SECONDARY_ENH_CAP_LIST_NBIFRCCFG_STRUCT;

#define PCICFG_NBIFRCCFG_PCIE_SECONDARY_ENH_CAP_LIST_OFFSET         0x270
#define SMN_DEV0_NBIF0RCNBIO0_PCIE_SECONDARY_ENH_CAP_LIST_ADDRESS     0x10100270UL
#define SMN_DEV0_NBIF0RCNBIO1_PCIE_SECONDARY_ENH_CAP_LIST_ADDRESS     0x10300270UL
#define SMN_DEV0_NBIF1RCNBIO0_PCIE_SECONDARY_ENH_CAP_LIST_ADDRESS     0x10200270UL
#define SMN_DEV0_NBIF1RCNBIO1_PCIE_SECONDARY_ENH_CAP_LIST_ADDRESS     0x10400270UL
#define SMN_DEV1_NBIF0RCNBIO0_PCIE_SECONDARY_ENH_CAP_LIST_ADDRESS     0x10101270UL
#define SMN_DEV1_NBIF0RCNBIO1_PCIE_SECONDARY_ENH_CAP_LIST_ADDRESS     0x10301270UL
#define SMN_DEV1_NBIF1RCNBIO0_PCIE_SECONDARY_ENH_CAP_LIST_ADDRESS     0x10201270UL
#define SMN_DEV1_NBIF1RCNBIO1_PCIE_SECONDARY_ENH_CAP_LIST_ADDRESS     0x10401270UL


/***********************************************************
* Register Name : PCIE_TLP_PREFIX_LOG0
************************************************************/

#define NBIF_TLP_PREFIX_LOG0_TLP_PREFIX_OFFSET                 0
#define NBIF_TLP_PREFIX_LOG0_TLP_PREFIX_MASK                   0xffffffff

typedef union {
  struct {
    UINT32                                          TLP_PREFIX:32;
  } Field;
  UINT32 Value;
} PCIE_TLP_PREFIX_LOG0_NBIFRCCFG_STRUCT;

#define PCICFG_NBIFRCCFG_PCIE_TLP_PREFIX_LOG0_OFFSET                0x188
#define SMN_DEV0_NBIF0RCNBIO0_PCIE_TLP_PREFIX_LOG0_ADDRESS            0x10100188UL
#define SMN_DEV0_NBIF0RCNBIO1_PCIE_TLP_PREFIX_LOG0_ADDRESS            0x10300188UL
#define SMN_DEV0_NBIF1RCNBIO0_PCIE_TLP_PREFIX_LOG0_ADDRESS            0x10200188UL
#define SMN_DEV0_NBIF1RCNBIO1_PCIE_TLP_PREFIX_LOG0_ADDRESS            0x10400188UL
#define SMN_DEV1_NBIF0RCNBIO0_PCIE_TLP_PREFIX_LOG0_ADDRESS            0x10101188UL
#define SMN_DEV1_NBIF0RCNBIO1_PCIE_TLP_PREFIX_LOG0_ADDRESS            0x10301188UL
#define SMN_DEV1_NBIF1RCNBIO0_PCIE_TLP_PREFIX_LOG0_ADDRESS            0x10201188UL
#define SMN_DEV1_NBIF1RCNBIO1_PCIE_TLP_PREFIX_LOG0_ADDRESS            0x10401188UL


/***********************************************************
* Register Name : PCIE_TLP_PREFIX_LOG1
************************************************************/

#define NBIF_TLP_PREFIX_LOG1_TLP_PREFIX_OFFSET                 0
#define NBIF_TLP_PREFIX_LOG1_TLP_PREFIX_MASK                   0xffffffff

typedef union {
  struct {
    UINT32                                          TLP_PREFIX:32;
  } Field;
  UINT32 Value;
} PCIE_TLP_PREFIX_LOG1_NBIFRCCFG_STRUCT;

#define PCICFG_NBIFRCCFG_PCIE_TLP_PREFIX_LOG1_OFFSET                0x18c
#define SMN_DEV0_NBIF0RCNBIO0_PCIE_TLP_PREFIX_LOG1_ADDRESS            0x1010018cUL
#define SMN_DEV0_NBIF0RCNBIO1_PCIE_TLP_PREFIX_LOG1_ADDRESS            0x1030018cUL
#define SMN_DEV0_NBIF1RCNBIO0_PCIE_TLP_PREFIX_LOG1_ADDRESS            0x1020018cUL
#define SMN_DEV0_NBIF1RCNBIO1_PCIE_TLP_PREFIX_LOG1_ADDRESS            0x1040018cUL
#define SMN_DEV1_NBIF0RCNBIO0_PCIE_TLP_PREFIX_LOG1_ADDRESS            0x1010118cUL
#define SMN_DEV1_NBIF0RCNBIO1_PCIE_TLP_PREFIX_LOG1_ADDRESS            0x1030118cUL
#define SMN_DEV1_NBIF1RCNBIO0_PCIE_TLP_PREFIX_LOG1_ADDRESS            0x1020118cUL
#define SMN_DEV1_NBIF1RCNBIO1_PCIE_TLP_PREFIX_LOG1_ADDRESS            0x1040118cUL


/***********************************************************
* Register Name : PCIE_TLP_PREFIX_LOG2
************************************************************/

#define NBIF_TLP_PREFIX_LOG2_TLP_PREFIX_OFFSET                 0
#define NBIF_TLP_PREFIX_LOG2_TLP_PREFIX_MASK                   0xffffffff

typedef union {
  struct {
    UINT32                                          TLP_PREFIX:32;
  } Field;
  UINT32 Value;
} PCIE_TLP_PREFIX_LOG2_NBIFRCCFG_STRUCT;

#define PCICFG_NBIFRCCFG_PCIE_TLP_PREFIX_LOG2_OFFSET                0x190
#define SMN_DEV0_NBIF0RCNBIO0_PCIE_TLP_PREFIX_LOG2_ADDRESS            0x10100190UL
#define SMN_DEV0_NBIF0RCNBIO1_PCIE_TLP_PREFIX_LOG2_ADDRESS            0x10300190UL
#define SMN_DEV0_NBIF1RCNBIO0_PCIE_TLP_PREFIX_LOG2_ADDRESS            0x10200190UL
#define SMN_DEV0_NBIF1RCNBIO1_PCIE_TLP_PREFIX_LOG2_ADDRESS            0x10400190UL
#define SMN_DEV1_NBIF0RCNBIO0_PCIE_TLP_PREFIX_LOG2_ADDRESS            0x10101190UL
#define SMN_DEV1_NBIF0RCNBIO1_PCIE_TLP_PREFIX_LOG2_ADDRESS            0x10301190UL
#define SMN_DEV1_NBIF1RCNBIO0_PCIE_TLP_PREFIX_LOG2_ADDRESS            0x10201190UL
#define SMN_DEV1_NBIF1RCNBIO1_PCIE_TLP_PREFIX_LOG2_ADDRESS            0x10401190UL


/***********************************************************
* Register Name : PCIE_TLP_PREFIX_LOG3
************************************************************/

#define NBIF_TLP_PREFIX_LOG3_TLP_PREFIX_OFFSET                 0
#define NBIF_TLP_PREFIX_LOG3_TLP_PREFIX_MASK                   0xffffffff

typedef union {
  struct {
    UINT32                                          TLP_PREFIX:32;
  } Field;
  UINT32 Value;
} PCIE_TLP_PREFIX_LOG3_NBIFRCCFG_STRUCT;

#define PCICFG_NBIFRCCFG_PCIE_TLP_PREFIX_LOG3_OFFSET                0x194
#define SMN_DEV0_NBIF0RCNBIO0_PCIE_TLP_PREFIX_LOG3_ADDRESS            0x10100194UL
#define SMN_DEV0_NBIF0RCNBIO1_PCIE_TLP_PREFIX_LOG3_ADDRESS            0x10300194UL
#define SMN_DEV0_NBIF1RCNBIO0_PCIE_TLP_PREFIX_LOG3_ADDRESS            0x10200194UL
#define SMN_DEV0_NBIF1RCNBIO1_PCIE_TLP_PREFIX_LOG3_ADDRESS            0x10400194UL
#define SMN_DEV1_NBIF0RCNBIO0_PCIE_TLP_PREFIX_LOG3_ADDRESS            0x10101194UL
#define SMN_DEV1_NBIF0RCNBIO1_PCIE_TLP_PREFIX_LOG3_ADDRESS            0x10301194UL
#define SMN_DEV1_NBIF1RCNBIO0_PCIE_TLP_PREFIX_LOG3_ADDRESS            0x10201194UL
#define SMN_DEV1_NBIF1RCNBIO1_PCIE_TLP_PREFIX_LOG3_ADDRESS            0x10401194UL


/***********************************************************
* Register Name : PCIE_UNCORR_ERR_MASK
************************************************************/

#define NBIF_UNCORR_ERR_MASK_Reserved_3_0_OFFSET               0
#define NBIF_UNCORR_ERR_MASK_Reserved_3_0_MASK                 0xf

#define NBIF_UNCORR_ERR_MASK_DLP_ERR_MASK_OFFSET               4
#define NBIF_UNCORR_ERR_MASK_DLP_ERR_MASK_MASK                 0x10

#define NBIF_UNCORR_ERR_MASK_SURPDN_ERR_MASK_OFFSET            5
#define NBIF_UNCORR_ERR_MASK_SURPDN_ERR_MASK_MASK              0x20

#define NBIF_UNCORR_ERR_MASK_Reserved_11_6_OFFSET              6
#define NBIF_UNCORR_ERR_MASK_Reserved_11_6_MASK                0xfc0

#define NBIF_UNCORR_ERR_MASK_PSN_ERR_MASK_OFFSET               12
#define NBIF_UNCORR_ERR_MASK_PSN_ERR_MASK_MASK                 0x1000

#define NBIF_UNCORR_ERR_MASK_FC_ERR_MASK_OFFSET                13
#define NBIF_UNCORR_ERR_MASK_FC_ERR_MASK_MASK                  0x2000

#define NBIF_UNCORR_ERR_MASK_CPL_TIMEOUT_MASK_OFFSET           14
#define NBIF_UNCORR_ERR_MASK_CPL_TIMEOUT_MASK_MASK             0x4000

#define NBIF_UNCORR_ERR_MASK_CPL_ABORT_ERR_MASK_OFFSET         15
#define NBIF_UNCORR_ERR_MASK_CPL_ABORT_ERR_MASK_MASK           0x8000

#define NBIF_UNCORR_ERR_MASK_UNEXP_CPL_MASK_OFFSET             16
#define NBIF_UNCORR_ERR_MASK_UNEXP_CPL_MASK_MASK               0x10000

#define NBIF_UNCORR_ERR_MASK_RCV_OVFL_MASK_OFFSET              17
#define NBIF_UNCORR_ERR_MASK_RCV_OVFL_MASK_MASK                0x20000

#define NBIF_UNCORR_ERR_MASK_MAL_TLP_MASK_OFFSET               18
#define NBIF_UNCORR_ERR_MASK_MAL_TLP_MASK_MASK                 0x40000

#define NBIF_UNCORR_ERR_MASK_ECRC_ERR_MASK_OFFSET              19
#define NBIF_UNCORR_ERR_MASK_ECRC_ERR_MASK_MASK                0x80000

#define NBIF_UNCORR_ERR_MASK_UNSUPP_REQ_ERR_MASK_OFFSET        20
#define NBIF_UNCORR_ERR_MASK_UNSUPP_REQ_ERR_MASK_MASK          0x100000

#define NBIF_UNCORR_ERR_MASK_ACS_VIOLATION_MASK_OFFSET         21
#define NBIF_UNCORR_ERR_MASK_ACS_VIOLATION_MASK_MASK           0x200000

#define NBIF_UNCORR_ERR_MASK_UNCORR_INT_ERR_MASK_OFFSET        22
#define NBIF_UNCORR_ERR_MASK_UNCORR_INT_ERR_MASK_MASK          0x400000

#define NBIF_UNCORR_ERR_MASK_MC_BLOCKED_TLP_MASK_OFFSET        23
#define NBIF_UNCORR_ERR_MASK_MC_BLOCKED_TLP_MASK_MASK          0x800000

#define NBIF_UNCORR_ERR_MASK_ATOMICOP_EGRESS_BLOCKED_MASK_OFFSET 24
#define NBIF_UNCORR_ERR_MASK_ATOMICOP_EGRESS_BLOCKED_MASK_MASK 0x1000000

#define NBIF_UNCORR_ERR_MASK_TLP_PREFIX_BLOCKED_ERR_MASK_OFFSET 25
#define NBIF_UNCORR_ERR_MASK_TLP_PREFIX_BLOCKED_ERR_MASK_MASK  0x2000000

#define NBIF_UNCORR_ERR_MASK_POISONED_TLP_EGRESS_BLOCKED_MASK_OFFSET 26
#define NBIF_UNCORR_ERR_MASK_POISONED_TLP_EGRESS_BLOCKED_MASK_MASK 0x4000000

#define NBIF_UNCORR_ERR_MASK_Reserved_31_27_OFFSET             27
#define NBIF_UNCORR_ERR_MASK_Reserved_31_27_MASK               0xf8000000

typedef union {
  struct {
    UINT32                                        Reserved_3_0:4;
    UINT32                                        DLP_ERR_MASK:1;
    UINT32                                     SURPDN_ERR_MASK:1;
    UINT32                                       Reserved_11_6:6;
    UINT32                                        PSN_ERR_MASK:1;
    UINT32                                         FC_ERR_MASK:1;
    UINT32                                    CPL_TIMEOUT_MASK:1;
    UINT32                                  CPL_ABORT_ERR_MASK:1;
    UINT32                                      UNEXP_CPL_MASK:1;
    UINT32                                       RCV_OVFL_MASK:1;
    UINT32                                        MAL_TLP_MASK:1;
    UINT32                                       ECRC_ERR_MASK:1;
    UINT32                                 UNSUPP_REQ_ERR_MASK:1;
    UINT32                                  ACS_VIOLATION_MASK:1;
    UINT32                                 UNCORR_INT_ERR_MASK:1;
    UINT32                                 MC_BLOCKED_TLP_MASK:1;
    UINT32                        ATOMICOP_EGRESS_BLOCKED_MASK:1;
    UINT32                         TLP_PREFIX_BLOCKED_ERR_MASK:1;
    UINT32                    POISONED_TLP_EGRESS_BLOCKED_MASK:1;
    UINT32                                      Reserved_31_27:5;
  } Field;
  UINT32 Value;
} PCIE_UNCORR_ERR_MASK_NBIFRCCFG_STRUCT;

#define PCICFG_NBIFRCCFG_PCIE_UNCORR_ERR_MASK_OFFSET                0x158
#define SMN_DEV0_NBIF0RCNBIO0_PCIE_UNCORR_ERR_MASK_ADDRESS            0x10100158UL
#define SMN_DEV0_NBIF0RCNBIO1_PCIE_UNCORR_ERR_MASK_ADDRESS            0x10300158UL
#define SMN_DEV0_NBIF1RCNBIO0_PCIE_UNCORR_ERR_MASK_ADDRESS            0x10200158UL
#define SMN_DEV0_NBIF1RCNBIO1_PCIE_UNCORR_ERR_MASK_ADDRESS            0x10400158UL
#define SMN_DEV1_NBIF0RCNBIO0_PCIE_UNCORR_ERR_MASK_ADDRESS            0x10101158UL
#define SMN_DEV1_NBIF0RCNBIO1_PCIE_UNCORR_ERR_MASK_ADDRESS            0x10301158UL
#define SMN_DEV1_NBIF1RCNBIO0_PCIE_UNCORR_ERR_MASK_ADDRESS            0x10201158UL
#define SMN_DEV1_NBIF1RCNBIO1_PCIE_UNCORR_ERR_MASK_ADDRESS            0x10401158UL


/***********************************************************
* Register Name : PCIE_UNCORR_ERR_SEVERITY
************************************************************/

#define NBIF_UNCORR_ERR_SEVERITY_Reserved_3_0_OFFSET           0
#define NBIF_UNCORR_ERR_SEVERITY_Reserved_3_0_MASK             0xf

#define NBIF_UNCORR_ERR_SEVERITY_DLP_ERR_SEVERITY_OFFSET       4
#define NBIF_UNCORR_ERR_SEVERITY_DLP_ERR_SEVERITY_MASK         0x10

#define NBIF_UNCORR_ERR_SEVERITY_SURPDN_ERR_SEVERITY_OFFSET    5
#define NBIF_UNCORR_ERR_SEVERITY_SURPDN_ERR_SEVERITY_MASK      0x20

#define NBIF_UNCORR_ERR_SEVERITY_Reserved_11_6_OFFSET          6
#define NBIF_UNCORR_ERR_SEVERITY_Reserved_11_6_MASK            0xfc0

#define NBIF_UNCORR_ERR_SEVERITY_PSN_ERR_SEVERITY_OFFSET       12
#define NBIF_UNCORR_ERR_SEVERITY_PSN_ERR_SEVERITY_MASK         0x1000

#define NBIF_UNCORR_ERR_SEVERITY_FC_ERR_SEVERITY_OFFSET        13
#define NBIF_UNCORR_ERR_SEVERITY_FC_ERR_SEVERITY_MASK          0x2000

#define NBIF_UNCORR_ERR_SEVERITY_CPL_TIMEOUT_SEVERITY_OFFSET   14
#define NBIF_UNCORR_ERR_SEVERITY_CPL_TIMEOUT_SEVERITY_MASK     0x4000

#define NBIF_UNCORR_ERR_SEVERITY_CPL_ABORT_ERR_SEVERITY_OFFSET 15
#define NBIF_UNCORR_ERR_SEVERITY_CPL_ABORT_ERR_SEVERITY_MASK   0x8000

#define NBIF_UNCORR_ERR_SEVERITY_UNEXP_CPL_SEVERITY_OFFSET     16
#define NBIF_UNCORR_ERR_SEVERITY_UNEXP_CPL_SEVERITY_MASK       0x10000

#define NBIF_UNCORR_ERR_SEVERITY_RCV_OVFL_SEVERITY_OFFSET      17
#define NBIF_UNCORR_ERR_SEVERITY_RCV_OVFL_SEVERITY_MASK        0x20000

#define NBIF_UNCORR_ERR_SEVERITY_MAL_TLP_SEVERITY_OFFSET       18
#define NBIF_UNCORR_ERR_SEVERITY_MAL_TLP_SEVERITY_MASK         0x40000

#define NBIF_UNCORR_ERR_SEVERITY_ECRC_ERR_SEVERITY_OFFSET      19
#define NBIF_UNCORR_ERR_SEVERITY_ECRC_ERR_SEVERITY_MASK        0x80000

#define NBIF_UNCORR_ERR_SEVERITY_UNSUPP_REQ_ERR_SEVERITY_OFFSET 20
#define NBIF_UNCORR_ERR_SEVERITY_UNSUPP_REQ_ERR_SEVERITY_MASK  0x100000

#define NBIF_UNCORR_ERR_SEVERITY_ACS_VIOLATION_SEVERITY_OFFSET 21
#define NBIF_UNCORR_ERR_SEVERITY_ACS_VIOLATION_SEVERITY_MASK   0x200000

#define NBIF_UNCORR_ERR_SEVERITY_UNCORR_INT_ERR_SEVERITY_OFFSET 22
#define NBIF_UNCORR_ERR_SEVERITY_UNCORR_INT_ERR_SEVERITY_MASK  0x400000

#define NBIF_UNCORR_ERR_SEVERITY_MC_BLOCKED_TLP_SEVERITY_OFFSET 23
#define NBIF_UNCORR_ERR_SEVERITY_MC_BLOCKED_TLP_SEVERITY_MASK  0x800000

#define NBIF_UNCORR_ERR_SEVERITY_ATOMICOP_EGRESS_BLOCKED_SEVERITY_OFFSET 24
#define NBIF_UNCORR_ERR_SEVERITY_ATOMICOP_EGRESS_BLOCKED_SEVERITY_MASK 0x1000000

#define NBIF_UNCORR_ERR_SEVERITY_TLP_PREFIX_BLOCKED_ERR_SEVERITY_OFFSET 25
#define NBIF_UNCORR_ERR_SEVERITY_TLP_PREFIX_BLOCKED_ERR_SEVERITY_MASK 0x2000000

#define NBIF_UNCORR_ERR_SEVERITY_POISONED_TLP_EGRESS_BLOCKED_SEVERITY_OFFSET 26
#define NBIF_UNCORR_ERR_SEVERITY_POISONED_TLP_EGRESS_BLOCKED_SEVERITY_MASK 0x4000000

#define NBIF_UNCORR_ERR_SEVERITY_Reserved_31_27_OFFSET         27
#define NBIF_UNCORR_ERR_SEVERITY_Reserved_31_27_MASK           0xf8000000

typedef union {
  struct {
    UINT32                                        Reserved_3_0:4;
    UINT32                                    DLP_ERR_SEVERITY:1;
    UINT32                                 SURPDN_ERR_SEVERITY:1;
    UINT32                                       Reserved_11_6:6;
    UINT32                                    PSN_ERR_SEVERITY:1;
    UINT32                                     FC_ERR_SEVERITY:1;
    UINT32                                CPL_TIMEOUT_SEVERITY:1;
    UINT32                              CPL_ABORT_ERR_SEVERITY:1;
    UINT32                                  UNEXP_CPL_SEVERITY:1;
    UINT32                                   RCV_OVFL_SEVERITY:1;
    UINT32                                    MAL_TLP_SEVERITY:1;
    UINT32                                   ECRC_ERR_SEVERITY:1;
    UINT32                             UNSUPP_REQ_ERR_SEVERITY:1;
    UINT32                              ACS_VIOLATION_SEVERITY:1;
    UINT32                             UNCORR_INT_ERR_SEVERITY:1;
    UINT32                             MC_BLOCKED_TLP_SEVERITY:1;
    UINT32                    ATOMICOP_EGRESS_BLOCKED_SEVERITY:1;
    UINT32                     TLP_PREFIX_BLOCKED_ERR_SEVERITY:1;
    UINT32                POISONED_TLP_EGRESS_BLOCKED_SEVERITY:1;
    UINT32                                      Reserved_31_27:5;
  } Field;
  UINT32 Value;
} PCIE_UNCORR_ERR_SEVERITY_NBIFRCCFG_STRUCT;

#define PCICFG_NBIFRCCFG_PCIE_UNCORR_ERR_SEVERITY_OFFSET            0x15c
#define SMN_DEV0_NBIF0RCNBIO0_PCIE_UNCORR_ERR_SEVERITY_ADDRESS        0x1010015cUL
#define SMN_DEV0_NBIF0RCNBIO1_PCIE_UNCORR_ERR_SEVERITY_ADDRESS        0x1030015cUL
#define SMN_DEV0_NBIF1RCNBIO0_PCIE_UNCORR_ERR_SEVERITY_ADDRESS        0x1020015cUL
#define SMN_DEV0_NBIF1RCNBIO1_PCIE_UNCORR_ERR_SEVERITY_ADDRESS        0x1040015cUL
#define SMN_DEV1_NBIF0RCNBIO0_PCIE_UNCORR_ERR_SEVERITY_ADDRESS        0x1010115cUL
#define SMN_DEV1_NBIF0RCNBIO1_PCIE_UNCORR_ERR_SEVERITY_ADDRESS        0x1030115cUL
#define SMN_DEV1_NBIF1RCNBIO0_PCIE_UNCORR_ERR_SEVERITY_ADDRESS        0x1020115cUL
#define SMN_DEV1_NBIF1RCNBIO1_PCIE_UNCORR_ERR_SEVERITY_ADDRESS        0x1040115cUL


/***********************************************************
* Register Name : PCIE_UNCORR_ERR_STATUS
************************************************************/

#define NBIF_UNCORR_ERR_STATUS_Reserved_3_0_OFFSET             0
#define NBIF_UNCORR_ERR_STATUS_Reserved_3_0_MASK               0xf

#define NBIF_UNCORR_ERR_STATUS_DLP_ERR_STATUS_OFFSET           4
#define NBIF_UNCORR_ERR_STATUS_DLP_ERR_STATUS_MASK             0x10

#define NBIF_UNCORR_ERR_STATUS_SURPDN_ERR_STATUS_OFFSET        5
#define NBIF_UNCORR_ERR_STATUS_SURPDN_ERR_STATUS_MASK          0x20

#define NBIF_UNCORR_ERR_STATUS_Reserved_11_6_OFFSET            6
#define NBIF_UNCORR_ERR_STATUS_Reserved_11_6_MASK              0xfc0

#define NBIF_UNCORR_ERR_STATUS_PSN_ERR_STATUS_OFFSET           12
#define NBIF_UNCORR_ERR_STATUS_PSN_ERR_STATUS_MASK             0x1000

#define NBIF_UNCORR_ERR_STATUS_FC_ERR_STATUS_OFFSET            13
#define NBIF_UNCORR_ERR_STATUS_FC_ERR_STATUS_MASK              0x2000

#define NBIF_UNCORR_ERR_STATUS_CPL_TIMEOUT_STATUS_OFFSET       14
#define NBIF_UNCORR_ERR_STATUS_CPL_TIMEOUT_STATUS_MASK         0x4000

#define NBIF_UNCORR_ERR_STATUS_CPL_ABORT_ERR_STATUS_OFFSET     15
#define NBIF_UNCORR_ERR_STATUS_CPL_ABORT_ERR_STATUS_MASK       0x8000

#define NBIF_UNCORR_ERR_STATUS_UNEXP_CPL_STATUS_OFFSET         16
#define NBIF_UNCORR_ERR_STATUS_UNEXP_CPL_STATUS_MASK           0x10000

#define NBIF_UNCORR_ERR_STATUS_RCV_OVFL_STATUS_OFFSET          17
#define NBIF_UNCORR_ERR_STATUS_RCV_OVFL_STATUS_MASK            0x20000

#define NBIF_UNCORR_ERR_STATUS_MAL_TLP_STATUS_OFFSET           18
#define NBIF_UNCORR_ERR_STATUS_MAL_TLP_STATUS_MASK             0x40000

#define NBIF_UNCORR_ERR_STATUS_ECRC_ERR_STATUS_OFFSET          19
#define NBIF_UNCORR_ERR_STATUS_ECRC_ERR_STATUS_MASK            0x80000

#define NBIF_UNCORR_ERR_STATUS_UNSUPP_REQ_ERR_STATUS_OFFSET    20
#define NBIF_UNCORR_ERR_STATUS_UNSUPP_REQ_ERR_STATUS_MASK      0x100000

#define NBIF_UNCORR_ERR_STATUS_ACS_VIOLATION_STATUS_OFFSET     21
#define NBIF_UNCORR_ERR_STATUS_ACS_VIOLATION_STATUS_MASK       0x200000

#define NBIF_UNCORR_ERR_STATUS_UNCORR_INT_ERR_STATUS_OFFSET    22
#define NBIF_UNCORR_ERR_STATUS_UNCORR_INT_ERR_STATUS_MASK      0x400000

#define NBIF_UNCORR_ERR_STATUS_MC_BLOCKED_TLP_STATUS_OFFSET    23
#define NBIF_UNCORR_ERR_STATUS_MC_BLOCKED_TLP_STATUS_MASK      0x800000

#define NBIF_UNCORR_ERR_STATUS_ATOMICOP_EGRESS_BLOCKED_STATUS_OFFSET 24
#define NBIF_UNCORR_ERR_STATUS_ATOMICOP_EGRESS_BLOCKED_STATUS_MASK 0x1000000

#define NBIF_UNCORR_ERR_STATUS_TLP_PREFIX_BLOCKED_ERR_STATUS_OFFSET 25
#define NBIF_UNCORR_ERR_STATUS_TLP_PREFIX_BLOCKED_ERR_STATUS_MASK 0x2000000

#define NBIF_UNCORR_ERR_STATUS_POISONED_TLP_EGRESS_BLOCKED_STATUS_OFFSET 26
#define NBIF_UNCORR_ERR_STATUS_POISONED_TLP_EGRESS_BLOCKED_STATUS_MASK 0x4000000

#define NBIF_UNCORR_ERR_STATUS_Reserved_31_27_OFFSET           27
#define NBIF_UNCORR_ERR_STATUS_Reserved_31_27_MASK             0xf8000000

typedef union {
  struct {
    UINT32                                        Reserved_3_0:4;
    UINT32                                      DLP_ERR_STATUS:1;
    UINT32                                   SURPDN_ERR_STATUS:1;
    UINT32                                       Reserved_11_6:6;
    UINT32                                      PSN_ERR_STATUS:1;
    UINT32                                       FC_ERR_STATUS:1;
    UINT32                                  CPL_TIMEOUT_STATUS:1;
    UINT32                                CPL_ABORT_ERR_STATUS:1;
    UINT32                                    UNEXP_CPL_STATUS:1;
    UINT32                                     RCV_OVFL_STATUS:1;
    UINT32                                      MAL_TLP_STATUS:1;
    UINT32                                     ECRC_ERR_STATUS:1;
    UINT32                               UNSUPP_REQ_ERR_STATUS:1;
    UINT32                                ACS_VIOLATION_STATUS:1;
    UINT32                               UNCORR_INT_ERR_STATUS:1;
    UINT32                               MC_BLOCKED_TLP_STATUS:1;
    UINT32                      ATOMICOP_EGRESS_BLOCKED_STATUS:1;
    UINT32                       TLP_PREFIX_BLOCKED_ERR_STATUS:1;
    UINT32                  POISONED_TLP_EGRESS_BLOCKED_STATUS:1;
    UINT32                                      Reserved_31_27:5;
  } Field;
  UINT32 Value;
} PCIE_UNCORR_ERR_STATUS_NBIFRCCFG_STRUCT;

#define PCICFG_NBIFRCCFG_PCIE_UNCORR_ERR_STATUS_OFFSET              0x154
#define SMN_DEV0_NBIF0RCNBIO0_PCIE_UNCORR_ERR_STATUS_ADDRESS          0x10100154UL
#define SMN_DEV0_NBIF0RCNBIO1_PCIE_UNCORR_ERR_STATUS_ADDRESS          0x10300154UL
#define SMN_DEV0_NBIF1RCNBIO0_PCIE_UNCORR_ERR_STATUS_ADDRESS          0x10200154UL
#define SMN_DEV0_NBIF1RCNBIO1_PCIE_UNCORR_ERR_STATUS_ADDRESS          0x10400154UL
#define SMN_DEV1_NBIF0RCNBIO0_PCIE_UNCORR_ERR_STATUS_ADDRESS          0x10101154UL
#define SMN_DEV1_NBIF0RCNBIO1_PCIE_UNCORR_ERR_STATUS_ADDRESS          0x10301154UL
#define SMN_DEV1_NBIF1RCNBIO0_PCIE_UNCORR_ERR_STATUS_ADDRESS          0x10201154UL
#define SMN_DEV1_NBIF1RCNBIO1_PCIE_UNCORR_ERR_STATUS_ADDRESS          0x10401154UL


/***********************************************************
* Register Name : PCIE_VC0_RESOURCE_CAP
************************************************************/

#define NBIF_VC0_RESOURCE_CAP_PORT_ARB_CAP_OFFSET              0
#define NBIF_VC0_RESOURCE_CAP_PORT_ARB_CAP_MASK                0xff

#define NBIF_VC0_RESOURCE_CAP_Reserved_14_8_OFFSET             8
#define NBIF_VC0_RESOURCE_CAP_Reserved_14_8_MASK               0x7f00

#define NBIF_VC0_RESOURCE_CAP_REJECT_SNOOP_TRANS_OFFSET        15
#define NBIF_VC0_RESOURCE_CAP_REJECT_SNOOP_TRANS_MASK          0x8000

#define NBIF_VC0_RESOURCE_CAP_MAX_TIME_SLOTS_OFFSET            16
#define NBIF_VC0_RESOURCE_CAP_MAX_TIME_SLOTS_MASK              0x7f0000

#define NBIF_VC0_RESOURCE_CAP_Reserved_23_23_OFFSET            23
#define NBIF_VC0_RESOURCE_CAP_Reserved_23_23_MASK              0x800000

#define NBIF_VC0_RESOURCE_CAP_PORT_ARB_TABLE_OFFSET_OFFSET     24
#define NBIF_VC0_RESOURCE_CAP_PORT_ARB_TABLE_OFFSET_MASK       0xff000000

typedef union {
  struct {
    UINT32                                        PORT_ARB_CAP:8;
    UINT32                                       Reserved_14_8:7;
    UINT32                                  REJECT_SNOOP_TRANS:1;
    UINT32                                      MAX_TIME_SLOTS:7;
    UINT32                                      Reserved_23_23:1;
    UINT32                               PORT_ARB_TABLE_OFFSET:8;
  } Field;
  UINT32 Value;
} PCIE_VC0_RESOURCE_CAP_NBIFRCCFG_STRUCT;

#define PCICFG_NBIFRCCFG_PCIE_VC0_RESOURCE_CAP_OFFSET               0x120
#define SMN_DEV0_NBIF0RCNBIO0_PCIE_VC0_RESOURCE_CAP_ADDRESS           0x10100120UL
#define SMN_DEV0_NBIF0RCNBIO1_PCIE_VC0_RESOURCE_CAP_ADDRESS           0x10300120UL
#define SMN_DEV0_NBIF1RCNBIO0_PCIE_VC0_RESOURCE_CAP_ADDRESS           0x10200120UL
#define SMN_DEV0_NBIF1RCNBIO1_PCIE_VC0_RESOURCE_CAP_ADDRESS           0x10400120UL
#define SMN_DEV1_NBIF0RCNBIO0_PCIE_VC0_RESOURCE_CAP_ADDRESS           0x10101120UL
#define SMN_DEV1_NBIF0RCNBIO1_PCIE_VC0_RESOURCE_CAP_ADDRESS           0x10301120UL
#define SMN_DEV1_NBIF1RCNBIO0_PCIE_VC0_RESOURCE_CAP_ADDRESS           0x10201120UL
#define SMN_DEV1_NBIF1RCNBIO1_PCIE_VC0_RESOURCE_CAP_ADDRESS           0x10401120UL


/***********************************************************
* Register Name : PCIE_VC0_RESOURCE_CNTL
************************************************************/

#define NBIF_VC0_RESOURCE_CNTL_TC_VC_MAP_TC0_OFFSET            0
#define NBIF_VC0_RESOURCE_CNTL_TC_VC_MAP_TC0_MASK              0x1

#define NBIF_VC0_RESOURCE_CNTL_TC_VC_MAP_TC1_7_OFFSET          1
#define NBIF_VC0_RESOURCE_CNTL_TC_VC_MAP_TC1_7_MASK            0xfe

#define NBIF_VC0_RESOURCE_CNTL_Reserved_15_8_OFFSET            8
#define NBIF_VC0_RESOURCE_CNTL_Reserved_15_8_MASK              0xff00

#define NBIF_VC0_RESOURCE_CNTL_LOAD_PORT_ARB_TABLE_OFFSET      16
#define NBIF_VC0_RESOURCE_CNTL_LOAD_PORT_ARB_TABLE_MASK        0x10000

#define NBIF_VC0_RESOURCE_CNTL_PORT_ARB_SELECT_OFFSET          17
#define NBIF_VC0_RESOURCE_CNTL_PORT_ARB_SELECT_MASK            0xe0000

#define NBIF_VC0_RESOURCE_CNTL_Reserved_23_20_OFFSET           20
#define NBIF_VC0_RESOURCE_CNTL_Reserved_23_20_MASK             0xf00000

#define NBIF_VC0_RESOURCE_CNTL_VC_ID_OFFSET                    24
#define NBIF_VC0_RESOURCE_CNTL_VC_ID_MASK                      0x7000000

#define NBIF_VC0_RESOURCE_CNTL_Reserved_30_27_OFFSET           27
#define NBIF_VC0_RESOURCE_CNTL_Reserved_30_27_MASK             0x78000000

#define NBIF_VC0_RESOURCE_CNTL_VC_ENABLE_OFFSET                31
#define NBIF_VC0_RESOURCE_CNTL_VC_ENABLE_MASK                  0x80000000

typedef union {
  struct {
    UINT32                                       TC_VC_MAP_TC0:1;
    UINT32                                     TC_VC_MAP_TC1_7:7;
    UINT32                                       Reserved_15_8:8;
    UINT32                                 LOAD_PORT_ARB_TABLE:1;
    UINT32                                     PORT_ARB_SELECT:3;
    UINT32                                      Reserved_23_20:4;
    UINT32                                               VC_ID:3;
    UINT32                                      Reserved_30_27:4;
    UINT32                                           VC_ENABLE:1;
  } Field;
  UINT32 Value;
} PCIE_VC0_RESOURCE_CNTL_NBIFRCCFG_STRUCT;

#define PCICFG_NBIFRCCFG_PCIE_VC0_RESOURCE_CNTL_OFFSET              0x124
#define SMN_DEV0_NBIF0RCNBIO0_PCIE_VC0_RESOURCE_CNTL_ADDRESS          0x10100124UL
#define SMN_DEV0_NBIF0RCNBIO1_PCIE_VC0_RESOURCE_CNTL_ADDRESS          0x10300124UL
#define SMN_DEV0_NBIF1RCNBIO0_PCIE_VC0_RESOURCE_CNTL_ADDRESS          0x10200124UL
#define SMN_DEV0_NBIF1RCNBIO1_PCIE_VC0_RESOURCE_CNTL_ADDRESS          0x10400124UL
#define SMN_DEV1_NBIF0RCNBIO0_PCIE_VC0_RESOURCE_CNTL_ADDRESS          0x10101124UL
#define SMN_DEV1_NBIF0RCNBIO1_PCIE_VC0_RESOURCE_CNTL_ADDRESS          0x10301124UL
#define SMN_DEV1_NBIF1RCNBIO0_PCIE_VC0_RESOURCE_CNTL_ADDRESS          0x10201124UL
#define SMN_DEV1_NBIF1RCNBIO1_PCIE_VC0_RESOURCE_CNTL_ADDRESS          0x10401124UL


/***********************************************************
* Register Name : PCIE_VC0_RESOURCE_STATUS
************************************************************/

#define NBIF_VC0_RESOURCE_STATUS_PORT_ARB_TABLE_STATUS_OFFSET  0
#define NBIF_VC0_RESOURCE_STATUS_PORT_ARB_TABLE_STATUS_MASK    0x1

#define NBIF_VC0_RESOURCE_STATUS_VC_NEGOTIATION_PENDING_OFFSET 1
#define NBIF_VC0_RESOURCE_STATUS_VC_NEGOTIATION_PENDING_MASK   0x2

#define NBIF_VC0_RESOURCE_STATUS_Reserved_15_2_OFFSET          2
#define NBIF_VC0_RESOURCE_STATUS_Reserved_15_2_MASK            0xfffc

typedef union {
  struct {
    UINT16                               PORT_ARB_TABLE_STATUS:1;
    UINT16                              VC_NEGOTIATION_PENDING:1;
    UINT16                                       Reserved_15_2:14;
  } Field;
  UINT16 Value;
} PCIE_VC0_RESOURCE_STATUS_NBIFRCCFG_STRUCT;

#define PCICFG_NBIFRCCFG_PCIE_VC0_RESOURCE_STATUS_OFFSET            0x12a
#define SMN_DEV0_NBIF0RCNBIO0_PCIE_VC0_RESOURCE_STATUS_ADDRESS        0x1010012aUL
#define SMN_DEV0_NBIF0RCNBIO1_PCIE_VC0_RESOURCE_STATUS_ADDRESS        0x1030012aUL
#define SMN_DEV0_NBIF1RCNBIO0_PCIE_VC0_RESOURCE_STATUS_ADDRESS        0x1020012aUL
#define SMN_DEV0_NBIF1RCNBIO1_PCIE_VC0_RESOURCE_STATUS_ADDRESS        0x1040012aUL
#define SMN_DEV1_NBIF0RCNBIO0_PCIE_VC0_RESOURCE_STATUS_ADDRESS        0x1010112aUL
#define SMN_DEV1_NBIF0RCNBIO1_PCIE_VC0_RESOURCE_STATUS_ADDRESS        0x1030112aUL
#define SMN_DEV1_NBIF1RCNBIO0_PCIE_VC0_RESOURCE_STATUS_ADDRESS        0x1020112aUL
#define SMN_DEV1_NBIF1RCNBIO1_PCIE_VC0_RESOURCE_STATUS_ADDRESS        0x1040112aUL


/***********************************************************
* Register Name : PCIE_VC1_RESOURCE_CAP
************************************************************/

#define NBIF_VC1_RESOURCE_CAP_PORT_ARB_CAP_OFFSET              0
#define NBIF_VC1_RESOURCE_CAP_PORT_ARB_CAP_MASK                0xff

#define NBIF_VC1_RESOURCE_CAP_Reserved_14_8_OFFSET             8
#define NBIF_VC1_RESOURCE_CAP_Reserved_14_8_MASK               0x7f00

#define NBIF_VC1_RESOURCE_CAP_REJECT_SNOOP_TRANS_OFFSET        15
#define NBIF_VC1_RESOURCE_CAP_REJECT_SNOOP_TRANS_MASK          0x8000

#define NBIF_VC1_RESOURCE_CAP_MAX_TIME_SLOTS_OFFSET            16
#define NBIF_VC1_RESOURCE_CAP_MAX_TIME_SLOTS_MASK              0x3f0000

#define NBIF_VC1_RESOURCE_CAP_Reserved_23_22_OFFSET            22
#define NBIF_VC1_RESOURCE_CAP_Reserved_23_22_MASK              0xc00000

#define NBIF_VC1_RESOURCE_CAP_PORT_ARB_TABLE_OFFSET_OFFSET     24
#define NBIF_VC1_RESOURCE_CAP_PORT_ARB_TABLE_OFFSET_MASK       0xff000000

typedef union {
  struct {
    UINT32                                        PORT_ARB_CAP:8;
    UINT32                                       Reserved_14_8:7;
    UINT32                                  REJECT_SNOOP_TRANS:1;
    UINT32                                      MAX_TIME_SLOTS:6;
    UINT32                                      Reserved_23_22:2;
    UINT32                               PORT_ARB_TABLE_OFFSET:8;
  } Field;
  UINT32 Value;
} PCIE_VC1_RESOURCE_CAP_NBIFRCCFG_STRUCT;

#define PCICFG_NBIFRCCFG_PCIE_VC1_RESOURCE_CAP_OFFSET               0x12c
#define SMN_DEV0_NBIF0RCNBIO0_PCIE_VC1_RESOURCE_CAP_ADDRESS           0x1010012cUL
#define SMN_DEV0_NBIF0RCNBIO1_PCIE_VC1_RESOURCE_CAP_ADDRESS           0x1030012cUL
#define SMN_DEV0_NBIF1RCNBIO0_PCIE_VC1_RESOURCE_CAP_ADDRESS           0x1020012cUL
#define SMN_DEV0_NBIF1RCNBIO1_PCIE_VC1_RESOURCE_CAP_ADDRESS           0x1040012cUL
#define SMN_DEV1_NBIF0RCNBIO0_PCIE_VC1_RESOURCE_CAP_ADDRESS           0x1010112cUL
#define SMN_DEV1_NBIF0RCNBIO1_PCIE_VC1_RESOURCE_CAP_ADDRESS           0x1030112cUL
#define SMN_DEV1_NBIF1RCNBIO0_PCIE_VC1_RESOURCE_CAP_ADDRESS           0x1020112cUL
#define SMN_DEV1_NBIF1RCNBIO1_PCIE_VC1_RESOURCE_CAP_ADDRESS           0x1040112cUL


/***********************************************************
* Register Name : PCIE_VC1_RESOURCE_CNTL
************************************************************/

#define NBIF_VC1_RESOURCE_CNTL_TC_VC_MAP_TC0_OFFSET            0
#define NBIF_VC1_RESOURCE_CNTL_TC_VC_MAP_TC0_MASK              0x1

#define NBIF_VC1_RESOURCE_CNTL_TC_VC_MAP_TC1_7_OFFSET          1
#define NBIF_VC1_RESOURCE_CNTL_TC_VC_MAP_TC1_7_MASK            0xfe

#define NBIF_VC1_RESOURCE_CNTL_Reserved_15_8_OFFSET            8
#define NBIF_VC1_RESOURCE_CNTL_Reserved_15_8_MASK              0xff00

#define NBIF_VC1_RESOURCE_CNTL_LOAD_PORT_ARB_TABLE_OFFSET      16
#define NBIF_VC1_RESOURCE_CNTL_LOAD_PORT_ARB_TABLE_MASK        0x10000

#define NBIF_VC1_RESOURCE_CNTL_PORT_ARB_SELECT_OFFSET          17
#define NBIF_VC1_RESOURCE_CNTL_PORT_ARB_SELECT_MASK            0xe0000

#define NBIF_VC1_RESOURCE_CNTL_Reserved_23_20_OFFSET           20
#define NBIF_VC1_RESOURCE_CNTL_Reserved_23_20_MASK             0xf00000

#define NBIF_VC1_RESOURCE_CNTL_VC_ID_OFFSET                    24
#define NBIF_VC1_RESOURCE_CNTL_VC_ID_MASK                      0x7000000

#define NBIF_VC1_RESOURCE_CNTL_Reserved_30_27_OFFSET           27
#define NBIF_VC1_RESOURCE_CNTL_Reserved_30_27_MASK             0x78000000

#define NBIF_VC1_RESOURCE_CNTL_VC_ENABLE_OFFSET                31
#define NBIF_VC1_RESOURCE_CNTL_VC_ENABLE_MASK                  0x80000000

typedef union {
  struct {
    UINT32                                       TC_VC_MAP_TC0:1;
    UINT32                                     TC_VC_MAP_TC1_7:7;
    UINT32                                       Reserved_15_8:8;
    UINT32                                 LOAD_PORT_ARB_TABLE:1;
    UINT32                                     PORT_ARB_SELECT:3;
    UINT32                                      Reserved_23_20:4;
    UINT32                                               VC_ID:3;
    UINT32                                      Reserved_30_27:4;
    UINT32                                           VC_ENABLE:1;
  } Field;
  UINT32 Value;
} PCIE_VC1_RESOURCE_CNTL_NBIFRCCFG_STRUCT;

#define PCICFG_NBIFRCCFG_PCIE_VC1_RESOURCE_CNTL_OFFSET              0x130
#define SMN_DEV0_NBIF0RCNBIO0_PCIE_VC1_RESOURCE_CNTL_ADDRESS          0x10100130UL
#define SMN_DEV0_NBIF0RCNBIO1_PCIE_VC1_RESOURCE_CNTL_ADDRESS          0x10300130UL
#define SMN_DEV0_NBIF1RCNBIO0_PCIE_VC1_RESOURCE_CNTL_ADDRESS          0x10200130UL
#define SMN_DEV0_NBIF1RCNBIO1_PCIE_VC1_RESOURCE_CNTL_ADDRESS          0x10400130UL
#define SMN_DEV1_NBIF0RCNBIO0_PCIE_VC1_RESOURCE_CNTL_ADDRESS          0x10101130UL
#define SMN_DEV1_NBIF0RCNBIO1_PCIE_VC1_RESOURCE_CNTL_ADDRESS          0x10301130UL
#define SMN_DEV1_NBIF1RCNBIO0_PCIE_VC1_RESOURCE_CNTL_ADDRESS          0x10201130UL
#define SMN_DEV1_NBIF1RCNBIO1_PCIE_VC1_RESOURCE_CNTL_ADDRESS          0x10401130UL


/***********************************************************
* Register Name : PCIE_VC1_RESOURCE_STATUS
************************************************************/

#define NBIF_VC1_RESOURCE_STATUS_PORT_ARB_TABLE_STATUS_OFFSET  0
#define NBIF_VC1_RESOURCE_STATUS_PORT_ARB_TABLE_STATUS_MASK    0x1

#define NBIF_VC1_RESOURCE_STATUS_VC_NEGOTIATION_PENDING_OFFSET 1
#define NBIF_VC1_RESOURCE_STATUS_VC_NEGOTIATION_PENDING_MASK   0x2

#define NBIF_VC1_RESOURCE_STATUS_Reserved_15_2_OFFSET          2
#define NBIF_VC1_RESOURCE_STATUS_Reserved_15_2_MASK            0xfffc

typedef union {
  struct {
    UINT16                               PORT_ARB_TABLE_STATUS:1;
    UINT16                              VC_NEGOTIATION_PENDING:1;
    UINT16                                       Reserved_15_2:14;
  } Field;
  UINT16 Value;
} PCIE_VC1_RESOURCE_STATUS_NBIFRCCFG_STRUCT;

#define PCICFG_NBIFRCCFG_PCIE_VC1_RESOURCE_STATUS_OFFSET            0x136
#define SMN_DEV0_NBIF0RCNBIO0_PCIE_VC1_RESOURCE_STATUS_ADDRESS        0x10100136UL
#define SMN_DEV0_NBIF0RCNBIO1_PCIE_VC1_RESOURCE_STATUS_ADDRESS        0x10300136UL
#define SMN_DEV0_NBIF1RCNBIO0_PCIE_VC1_RESOURCE_STATUS_ADDRESS        0x10200136UL
#define SMN_DEV0_NBIF1RCNBIO1_PCIE_VC1_RESOURCE_STATUS_ADDRESS        0x10400136UL
#define SMN_DEV1_NBIF0RCNBIO0_PCIE_VC1_RESOURCE_STATUS_ADDRESS        0x10101136UL
#define SMN_DEV1_NBIF0RCNBIO1_PCIE_VC1_RESOURCE_STATUS_ADDRESS        0x10301136UL
#define SMN_DEV1_NBIF1RCNBIO0_PCIE_VC1_RESOURCE_STATUS_ADDRESS        0x10201136UL
#define SMN_DEV1_NBIF1RCNBIO1_PCIE_VC1_RESOURCE_STATUS_ADDRESS        0x10401136UL


/***********************************************************
* Register Name : PCIE_VC_ENH_CAP_LIST
************************************************************/

#define NBIF_VC_ENH_CAP_LIST_CAP_ID_OFFSET                     0
#define NBIF_VC_ENH_CAP_LIST_CAP_ID_MASK                       0xffff

#define NBIF_VC_ENH_CAP_LIST_CAP_VER_OFFSET                    16
#define NBIF_VC_ENH_CAP_LIST_CAP_VER_MASK                      0xf0000

#define NBIF_VC_ENH_CAP_LIST_NEXT_PTR_OFFSET                   20
#define NBIF_VC_ENH_CAP_LIST_NEXT_PTR_MASK                     0xfff00000

typedef union {
  struct {
    UINT32                                              CAP_ID:16;
    UINT32                                             CAP_VER:4;
    UINT32                                            NEXT_PTR:12;
  } Field;
  UINT32 Value;
} PCIE_VC_ENH_CAP_LIST_NBIFRCCFG_STRUCT;

#define PCICFG_NBIFRCCFG_PCIE_VC_ENH_CAP_LIST_OFFSET                0x110
#define SMN_DEV0_NBIF0RCNBIO0_PCIE_VC_ENH_CAP_LIST_ADDRESS            0x10100110UL
#define SMN_DEV0_NBIF0RCNBIO1_PCIE_VC_ENH_CAP_LIST_ADDRESS            0x10300110UL
#define SMN_DEV0_NBIF1RCNBIO0_PCIE_VC_ENH_CAP_LIST_ADDRESS            0x10200110UL
#define SMN_DEV0_NBIF1RCNBIO1_PCIE_VC_ENH_CAP_LIST_ADDRESS            0x10400110UL
#define SMN_DEV1_NBIF0RCNBIO0_PCIE_VC_ENH_CAP_LIST_ADDRESS            0x10101110UL
#define SMN_DEV1_NBIF0RCNBIO1_PCIE_VC_ENH_CAP_LIST_ADDRESS            0x10301110UL
#define SMN_DEV1_NBIF1RCNBIO0_PCIE_VC_ENH_CAP_LIST_ADDRESS            0x10201110UL
#define SMN_DEV1_NBIF1RCNBIO1_PCIE_VC_ENH_CAP_LIST_ADDRESS            0x10401110UL


/***********************************************************
* Register Name : PCIE_VENDOR_SPECIFIC1
************************************************************/

#define NBIF_VENDOR_SPECIFIC1_SCRATCH_OFFSET                   0
#define NBIF_VENDOR_SPECIFIC1_SCRATCH_MASK                     0xffffffff

typedef union {
  struct {
    UINT32                                             SCRATCH:32;
  } Field;
  UINT32 Value;
} PCIE_VENDOR_SPECIFIC1_NBIFRCCFG_STRUCT;

#define PCICFG_NBIFRCCFG_PCIE_VENDOR_SPECIFIC1_OFFSET               0x108
#define SMN_DEV0_NBIF0RCNBIO0_PCIE_VENDOR_SPECIFIC1_ADDRESS           0x10100108UL
#define SMN_DEV0_NBIF0RCNBIO1_PCIE_VENDOR_SPECIFIC1_ADDRESS           0x10300108UL
#define SMN_DEV0_NBIF1RCNBIO0_PCIE_VENDOR_SPECIFIC1_ADDRESS           0x10200108UL
#define SMN_DEV0_NBIF1RCNBIO1_PCIE_VENDOR_SPECIFIC1_ADDRESS           0x10400108UL
#define SMN_DEV1_NBIF0RCNBIO0_PCIE_VENDOR_SPECIFIC1_ADDRESS           0x10101108UL
#define SMN_DEV1_NBIF0RCNBIO1_PCIE_VENDOR_SPECIFIC1_ADDRESS           0x10301108UL
#define SMN_DEV1_NBIF1RCNBIO0_PCIE_VENDOR_SPECIFIC1_ADDRESS           0x10201108UL
#define SMN_DEV1_NBIF1RCNBIO1_PCIE_VENDOR_SPECIFIC1_ADDRESS           0x10401108UL


/***********************************************************
* Register Name : PCIE_VENDOR_SPECIFIC2
************************************************************/

#define NBIF_VENDOR_SPECIFIC2_SCRATCH_OFFSET                   0
#define NBIF_VENDOR_SPECIFIC2_SCRATCH_MASK                     0xffffffff

typedef union {
  struct {
    UINT32                                             SCRATCH:32;
  } Field;
  UINT32 Value;
} PCIE_VENDOR_SPECIFIC2_NBIFRCCFG_STRUCT;

#define PCICFG_NBIFRCCFG_PCIE_VENDOR_SPECIFIC2_OFFSET               0x10c
#define SMN_DEV0_NBIF0RCNBIO0_PCIE_VENDOR_SPECIFIC2_ADDRESS           0x1010010cUL
#define SMN_DEV0_NBIF0RCNBIO1_PCIE_VENDOR_SPECIFIC2_ADDRESS           0x1030010cUL
#define SMN_DEV0_NBIF1RCNBIO0_PCIE_VENDOR_SPECIFIC2_ADDRESS           0x1020010cUL
#define SMN_DEV0_NBIF1RCNBIO1_PCIE_VENDOR_SPECIFIC2_ADDRESS           0x1040010cUL
#define SMN_DEV1_NBIF0RCNBIO0_PCIE_VENDOR_SPECIFIC2_ADDRESS           0x1010110cUL
#define SMN_DEV1_NBIF0RCNBIO1_PCIE_VENDOR_SPECIFIC2_ADDRESS           0x1030110cUL
#define SMN_DEV1_NBIF1RCNBIO0_PCIE_VENDOR_SPECIFIC2_ADDRESS           0x1020110cUL
#define SMN_DEV1_NBIF1RCNBIO1_PCIE_VENDOR_SPECIFIC2_ADDRESS           0x1040110cUL


/***********************************************************
* Register Name : PCIE_VENDOR_SPECIFIC_ENH_CAP_LIST
************************************************************/

#define NBIF_VENDOR_SPECIFIC_ENH_CAP_LIST_CAP_ID_OFFSET        0
#define NBIF_VENDOR_SPECIFIC_ENH_CAP_LIST_CAP_ID_MASK          0xffff

#define NBIF_VENDOR_SPECIFIC_ENH_CAP_LIST_CAP_VER_OFFSET       16
#define NBIF_VENDOR_SPECIFIC_ENH_CAP_LIST_CAP_VER_MASK         0xf0000

#define NBIF_VENDOR_SPECIFIC_ENH_CAP_LIST_NEXT_PTR_OFFSET      20
#define NBIF_VENDOR_SPECIFIC_ENH_CAP_LIST_NEXT_PTR_MASK        0xfff00000

typedef union {
  struct {
    UINT32                                              CAP_ID:16;
    UINT32                                             CAP_VER:4;
    UINT32                                            NEXT_PTR:12;
  } Field;
  UINT32 Value;
} PCIE_VENDOR_SPECIFIC_ENH_CAP_LIST_NBIFRCCFG_STRUCT;

#define PCICFG_NBIFRCCFG_PCIE_VENDOR_SPECIFIC_ENH_CAP_LIST_OFFSET   0x100
#define SMN_DEV0_NBIF0RCNBIO0_PCIE_VENDOR_SPECIFIC_ENH_CAP_LIST_ADDRESS 0x10100100UL
#define SMN_DEV0_NBIF0RCNBIO1_PCIE_VENDOR_SPECIFIC_ENH_CAP_LIST_ADDRESS 0x10300100UL
#define SMN_DEV0_NBIF1RCNBIO0_PCIE_VENDOR_SPECIFIC_ENH_CAP_LIST_ADDRESS 0x10200100UL
#define SMN_DEV0_NBIF1RCNBIO1_PCIE_VENDOR_SPECIFIC_ENH_CAP_LIST_ADDRESS 0x10400100UL
#define SMN_DEV1_NBIF0RCNBIO0_PCIE_VENDOR_SPECIFIC_ENH_CAP_LIST_ADDRESS 0x10101100UL
#define SMN_DEV1_NBIF0RCNBIO1_PCIE_VENDOR_SPECIFIC_ENH_CAP_LIST_ADDRESS 0x10301100UL
#define SMN_DEV1_NBIF1RCNBIO0_PCIE_VENDOR_SPECIFIC_ENH_CAP_LIST_ADDRESS 0x10201100UL
#define SMN_DEV1_NBIF1RCNBIO1_PCIE_VENDOR_SPECIFIC_ENH_CAP_LIST_ADDRESS 0x10401100UL


/***********************************************************
* Register Name : PCIE_VENDOR_SPECIFIC_HDR
************************************************************/

#define NBIF_VENDOR_SPECIFIC_HDR_VSEC_ID_OFFSET                0
#define NBIF_VENDOR_SPECIFIC_HDR_VSEC_ID_MASK                  0xffff

#define NBIF_VENDOR_SPECIFIC_HDR_VSEC_REV_OFFSET               16
#define NBIF_VENDOR_SPECIFIC_HDR_VSEC_REV_MASK                 0xf0000

#define NBIF_VENDOR_SPECIFIC_HDR_VSEC_LENGTH_OFFSET            20
#define NBIF_VENDOR_SPECIFIC_HDR_VSEC_LENGTH_MASK              0xfff00000

typedef union {
  struct {
    UINT32                                             VSEC_ID:16;
    UINT32                                            VSEC_REV:4;
    UINT32                                         VSEC_LENGTH:12;
  } Field;
  UINT32 Value;
} PCIE_VENDOR_SPECIFIC_HDR_NBIFRCCFG_STRUCT;

#define PCICFG_NBIFRCCFG_PCIE_VENDOR_SPECIFIC_HDR_OFFSET            0x104
#define SMN_DEV0_NBIF0RCNBIO0_PCIE_VENDOR_SPECIFIC_HDR_ADDRESS        0x10100104UL
#define SMN_DEV0_NBIF0RCNBIO1_PCIE_VENDOR_SPECIFIC_HDR_ADDRESS        0x10300104UL
#define SMN_DEV0_NBIF1RCNBIO0_PCIE_VENDOR_SPECIFIC_HDR_ADDRESS        0x10200104UL
#define SMN_DEV0_NBIF1RCNBIO1_PCIE_VENDOR_SPECIFIC_HDR_ADDRESS        0x10400104UL
#define SMN_DEV1_NBIF0RCNBIO0_PCIE_VENDOR_SPECIFIC_HDR_ADDRESS        0x10101104UL
#define SMN_DEV1_NBIF0RCNBIO1_PCIE_VENDOR_SPECIFIC_HDR_ADDRESS        0x10301104UL
#define SMN_DEV1_NBIF1RCNBIO0_PCIE_VENDOR_SPECIFIC_HDR_ADDRESS        0x10201104UL
#define SMN_DEV1_NBIF1RCNBIO1_PCIE_VENDOR_SPECIFIC_HDR_ADDRESS        0x10401104UL


/***********************************************************
* Register Name : PMI_CAP
************************************************************/

#define PMI_CAP_VERSION_OFFSET                                 0
#define PMI_CAP_VERSION_MASK                                   0x7

#define PMI_CAP_PME_CLOCK_OFFSET                               3
#define PMI_CAP_PME_CLOCK_MASK                                 0x8

#define PMI_CAP_IMMEDIATE_READINESS_ON_RETURN_TO_D0_OFFSET     4
#define PMI_CAP_IMMEDIATE_READINESS_ON_RETURN_TO_D0_MASK       0x10

#define PMI_CAP_DEV_SPECIFIC_INIT_OFFSET                       5
#define PMI_CAP_DEV_SPECIFIC_INIT_MASK                         0x20

#define PMI_CAP_AUX_CURRENT_OFFSET                             6
#define PMI_CAP_AUX_CURRENT_MASK                               0x1c0

#define PMI_CAP_D1_SUPPORT_OFFSET                              9
#define PMI_CAP_D1_SUPPORT_MASK                                0x200

#define PMI_CAP_D2_SUPPORT_OFFSET                              10
#define PMI_CAP_D2_SUPPORT_MASK                                0x400

#define PMI_CAP_PME_SUPPORT_OFFSET                             11
#define PMI_CAP_PME_SUPPORT_MASK                               0xf800

typedef union {
  struct {
    UINT16                                             VERSION:3;
    UINT16                                           PME_CLOCK:1;
    UINT16                 IMMEDIATE_READINESS_ON_RETURN_TO_D0:1;
    UINT16                                   DEV_SPECIFIC_INIT:1;
    UINT16                                         AUX_CURRENT:3;
    UINT16                                          D1_SUPPORT:1;
    UINT16                                          D2_SUPPORT:1;
    UINT16                                         PME_SUPPORT:5;
  } Field;
  UINT16 Value;
} PMI_CAP_NBIFRCCFG_STRUCT;

#define PCICFG_NBIFRCCFG_PMI_CAP_OFFSET                             0x52
#define SMN_DEV0_NBIF0RCNBIO0_PMI_CAP_ADDRESS                         0x10100052UL
#define SMN_DEV0_NBIF0RCNBIO1_PMI_CAP_ADDRESS                         0x10300052UL
#define SMN_DEV0_NBIF1RCNBIO0_PMI_CAP_ADDRESS                         0x10200052UL
#define SMN_DEV0_NBIF1RCNBIO1_PMI_CAP_ADDRESS                         0x10400052UL
#define SMN_DEV1_NBIF0RCNBIO0_PMI_CAP_ADDRESS                         0x10101052UL
#define SMN_DEV1_NBIF0RCNBIO1_PMI_CAP_ADDRESS                         0x10301052UL
#define SMN_DEV1_NBIF1RCNBIO0_PMI_CAP_ADDRESS                         0x10201052UL
#define SMN_DEV1_NBIF1RCNBIO1_PMI_CAP_ADDRESS                         0x10401052UL


/***********************************************************
* Register Name : PMI_CAP_LIST
************************************************************/

#define PMI_CAP_LIST_CAP_ID_OFFSET                             0
#define PMI_CAP_LIST_CAP_ID_MASK                               0xff

#define PMI_CAP_LIST_NEXT_PTR_OFFSET                           8
#define PMI_CAP_LIST_NEXT_PTR_MASK                             0xff00

typedef union {
  struct {
    UINT16                                              CAP_ID:8;
    UINT16                                            NEXT_PTR:8;
  } Field;
  UINT16 Value;
} PMI_CAP_LIST_NBIFRCCFG_STRUCT;

#define PCICFG_NBIFRCCFG_PMI_CAP_LIST_OFFSET                        0x50
#define SMN_DEV0_NBIF0RCNBIO0_PMI_CAP_LIST_ADDRESS                    0x10100050UL
#define SMN_DEV0_NBIF0RCNBIO1_PMI_CAP_LIST_ADDRESS                    0x10300050UL
#define SMN_DEV0_NBIF1RCNBIO0_PMI_CAP_LIST_ADDRESS                    0x10200050UL
#define SMN_DEV0_NBIF1RCNBIO1_PMI_CAP_LIST_ADDRESS                    0x10400050UL
#define SMN_DEV1_NBIF0RCNBIO0_PMI_CAP_LIST_ADDRESS                    0x10101050UL
#define SMN_DEV1_NBIF0RCNBIO1_PMI_CAP_LIST_ADDRESS                    0x10301050UL
#define SMN_DEV1_NBIF1RCNBIO0_PMI_CAP_LIST_ADDRESS                    0x10201050UL
#define SMN_DEV1_NBIF1RCNBIO1_PMI_CAP_LIST_ADDRESS                    0x10401050UL


/***********************************************************
* Register Name : PMI_STATUS_CNTL
************************************************************/

#define PMI_STATUS_CNTL_POWER_STATE_OFFSET                     0
#define PMI_STATUS_CNTL_POWER_STATE_MASK                       0x3

#define PMI_STATUS_CNTL_Reserved_2_2_OFFSET                    2
#define PMI_STATUS_CNTL_Reserved_2_2_MASK                      0x4

#define PMI_STATUS_CNTL_NO_SOFT_RESET_OFFSET                   3
#define PMI_STATUS_CNTL_NO_SOFT_RESET_MASK                     0x8

#define PMI_STATUS_CNTL_Reserved_7_4_OFFSET                    4
#define PMI_STATUS_CNTL_Reserved_7_4_MASK                      0xf0

#define PMI_STATUS_CNTL_PME_EN_OFFSET                          8
#define PMI_STATUS_CNTL_PME_EN_MASK                            0x100

#define PMI_STATUS_CNTL_DATA_SELECT_OFFSET                     9
#define PMI_STATUS_CNTL_DATA_SELECT_MASK                       0x1e00

#define PMI_STATUS_CNTL_DATA_SCALE_OFFSET                      13
#define PMI_STATUS_CNTL_DATA_SCALE_MASK                        0x6000

#define PMI_STATUS_CNTL_PME_STATUS_OFFSET                      15
#define PMI_STATUS_CNTL_PME_STATUS_MASK                        0x8000

#define PMI_STATUS_CNTL_Reserved_21_16_OFFSET                  16
#define PMI_STATUS_CNTL_Reserved_21_16_MASK                    0x3f0000

#define PMI_STATUS_CNTL_B2_B3_SUPPORT_OFFSET                   22
#define PMI_STATUS_CNTL_B2_B3_SUPPORT_MASK                     0x400000

#define PMI_STATUS_CNTL_BUS_PWR_EN_OFFSET                      23
#define PMI_STATUS_CNTL_BUS_PWR_EN_MASK                        0x800000

#define PMI_STATUS_CNTL_PMI_DATA_OFFSET                        24
#define PMI_STATUS_CNTL_PMI_DATA_MASK                          0xff000000

typedef union {
  struct {
    UINT32                                         POWER_STATE:2;
    UINT32                                        Reserved_2_2:1;
    UINT32                                       NO_SOFT_RESET:1;
    UINT32                                        Reserved_7_4:4;
    UINT32                                              PME_EN:1;
    UINT32                                         DATA_SELECT:4;
    UINT32                                          DATA_SCALE:2;
    UINT32                                          PME_STATUS:1;
    UINT32                                      Reserved_21_16:6;
    UINT32                                       B2_B3_SUPPORT:1;
    UINT32                                          BUS_PWR_EN:1;
    UINT32                                            PMI_DATA:8;
  } Field;
  UINT32 Value;
} PMI_STATUS_CNTL_NBIFRCCFG_STRUCT;

#define PCICFG_NBIFRCCFG_PMI_STATUS_CNTL_OFFSET                     0x54
#define SMN_DEV0_NBIF0RCNBIO0_PMI_STATUS_CNTL_ADDRESS                 0x10100054UL
#define SMN_DEV0_NBIF0RCNBIO1_PMI_STATUS_CNTL_ADDRESS                 0x10300054UL
#define SMN_DEV0_NBIF1RCNBIO0_PMI_STATUS_CNTL_ADDRESS                 0x10200054UL
#define SMN_DEV0_NBIF1RCNBIO1_PMI_STATUS_CNTL_ADDRESS                 0x10400054UL
#define SMN_DEV1_NBIF0RCNBIO0_PMI_STATUS_CNTL_ADDRESS                 0x10101054UL
#define SMN_DEV1_NBIF0RCNBIO1_PMI_STATUS_CNTL_ADDRESS                 0x10301054UL
#define SMN_DEV1_NBIF1RCNBIO0_PMI_STATUS_CNTL_ADDRESS                 0x10201054UL
#define SMN_DEV1_NBIF1RCNBIO1_PMI_STATUS_CNTL_ADDRESS                 0x10401054UL


/***********************************************************
* Register Name : PREF_BASE_LIMIT
************************************************************/

#define PREF_BASE_LIMIT_PREF_MEM_BASE_TYPE_OFFSET              0
#define PREF_BASE_LIMIT_PREF_MEM_BASE_TYPE_MASK                0xf

#define PREF_BASE_LIMIT_PREF_MEM_BASE_31_20_OFFSET             4
#define PREF_BASE_LIMIT_PREF_MEM_BASE_31_20_MASK               0xfff0

#define PREF_BASE_LIMIT_PREF_MEM_LIMIT_TYPE_OFFSET             16
#define PREF_BASE_LIMIT_PREF_MEM_LIMIT_TYPE_MASK               0xf0000

#define PREF_BASE_LIMIT_PREF_MEM_LIMIT_31_20_OFFSET            20
#define PREF_BASE_LIMIT_PREF_MEM_LIMIT_31_20_MASK              0xfff00000

typedef union {
  struct {
    UINT32                                  PREF_MEM_BASE_TYPE:4;
    UINT32                                 PREF_MEM_BASE_31_20:12;
    UINT32                                 PREF_MEM_LIMIT_TYPE:4;
    UINT32                                PREF_MEM_LIMIT_31_20:12;
  } Field;
  UINT32 Value;
} PREF_BASE_LIMIT_NBIFRCCFG_STRUCT;

#define PCICFG_NBIFRCCFG_PREF_BASE_LIMIT_OFFSET                     0x24
#define SMN_DEV0_NBIF0RCNBIO0_PREF_BASE_LIMIT_ADDRESS                 0x10100024UL
#define SMN_DEV0_NBIF0RCNBIO1_PREF_BASE_LIMIT_ADDRESS                 0x10300024UL
#define SMN_DEV0_NBIF1RCNBIO0_PREF_BASE_LIMIT_ADDRESS                 0x10200024UL
#define SMN_DEV0_NBIF1RCNBIO1_PREF_BASE_LIMIT_ADDRESS                 0x10400024UL
#define SMN_DEV1_NBIF0RCNBIO0_PREF_BASE_LIMIT_ADDRESS                 0x10101024UL
#define SMN_DEV1_NBIF0RCNBIO1_PREF_BASE_LIMIT_ADDRESS                 0x10301024UL
#define SMN_DEV1_NBIF1RCNBIO0_PREF_BASE_LIMIT_ADDRESS                 0x10201024UL
#define SMN_DEV1_NBIF1RCNBIO1_PREF_BASE_LIMIT_ADDRESS                 0x10401024UL


/***********************************************************
* Register Name : PREF_BASE_UPPER
************************************************************/

#define PREF_BASE_UPPER_PREF_BASE_UPPER_OFFSET                 0
#define PREF_BASE_UPPER_PREF_BASE_UPPER_MASK                   0xffffffff

typedef union {
  struct {
    UINT32                                     PREF_BASE_UPPER:32;
  } Field;
  UINT32 Value;
} PREF_BASE_UPPER_NBIFRCCFG_STRUCT;

#define PCICFG_NBIFRCCFG_PREF_BASE_UPPER_OFFSET                     0x28
#define SMN_DEV0_NBIF0RCNBIO0_PREF_BASE_UPPER_ADDRESS                 0x10100028UL
#define SMN_DEV0_NBIF0RCNBIO1_PREF_BASE_UPPER_ADDRESS                 0x10300028UL
#define SMN_DEV0_NBIF1RCNBIO0_PREF_BASE_UPPER_ADDRESS                 0x10200028UL
#define SMN_DEV0_NBIF1RCNBIO1_PREF_BASE_UPPER_ADDRESS                 0x10400028UL
#define SMN_DEV1_NBIF0RCNBIO0_PREF_BASE_UPPER_ADDRESS                 0x10101028UL
#define SMN_DEV1_NBIF0RCNBIO1_PREF_BASE_UPPER_ADDRESS                 0x10301028UL
#define SMN_DEV1_NBIF1RCNBIO0_PREF_BASE_UPPER_ADDRESS                 0x10201028UL
#define SMN_DEV1_NBIF1RCNBIO1_PREF_BASE_UPPER_ADDRESS                 0x10401028UL


/***********************************************************
* Register Name : PREF_LIMIT_UPPER
************************************************************/

#define PREF_LIMIT_UPPER_PREF_LIMIT_UPPER_OFFSET               0
#define PREF_LIMIT_UPPER_PREF_LIMIT_UPPER_MASK                 0xffffffff

typedef union {
  struct {
    UINT32                                    PREF_LIMIT_UPPER:32;
  } Field;
  UINT32 Value;
} PREF_LIMIT_UPPER_NBIFRCCFG_STRUCT;

#define PCICFG_NBIFRCCFG_PREF_LIMIT_UPPER_OFFSET                    0x2c
#define SMN_DEV0_NBIF0RCNBIO0_PREF_LIMIT_UPPER_ADDRESS                0x1010002cUL
#define SMN_DEV0_NBIF0RCNBIO1_PREF_LIMIT_UPPER_ADDRESS                0x1030002cUL
#define SMN_DEV0_NBIF1RCNBIO0_PREF_LIMIT_UPPER_ADDRESS                0x1020002cUL
#define SMN_DEV0_NBIF1RCNBIO1_PREF_LIMIT_UPPER_ADDRESS                0x1040002cUL
#define SMN_DEV1_NBIF0RCNBIO0_PREF_LIMIT_UPPER_ADDRESS                0x1010102cUL
#define SMN_DEV1_NBIF0RCNBIO1_PREF_LIMIT_UPPER_ADDRESS                0x1030102cUL
#define SMN_DEV1_NBIF1RCNBIO0_PREF_LIMIT_UPPER_ADDRESS                0x1020102cUL
#define SMN_DEV1_NBIF1RCNBIO1_PREF_LIMIT_UPPER_ADDRESS                0x1040102cUL


/***********************************************************
* Register Name : PROG_INTERFACE
************************************************************/

#define PROG_INTERFACE_PROG_INTERFACE_OFFSET                   0
#define PROG_INTERFACE_PROG_INTERFACE_MASK                     0xff

typedef union {
  struct {
    UINT8                                      PROG_INTERFACE:8;
  } Field;
  UINT8 Value;
} PROG_INTERFACE_NBIFRCCFG_STRUCT;

#define PCICFG_NBIFRCCFG_PROG_INTERFACE_OFFSET                      0x9
#define SMN_DEV0_NBIF0RCNBIO0_PROG_INTERFACE_ADDRESS                  0x10100009UL
#define SMN_DEV0_NBIF0RCNBIO1_PROG_INTERFACE_ADDRESS                  0x10300009UL
#define SMN_DEV0_NBIF1RCNBIO0_PROG_INTERFACE_ADDRESS                  0x10200009UL
#define SMN_DEV0_NBIF1RCNBIO1_PROG_INTERFACE_ADDRESS                  0x10400009UL
#define SMN_DEV1_NBIF0RCNBIO0_PROG_INTERFACE_ADDRESS                  0x10101009UL
#define SMN_DEV1_NBIF0RCNBIO1_PROG_INTERFACE_ADDRESS                  0x10301009UL
#define SMN_DEV1_NBIF1RCNBIO0_PROG_INTERFACE_ADDRESS                  0x10201009UL
#define SMN_DEV1_NBIF1RCNBIO1_PROG_INTERFACE_ADDRESS                  0x10401009UL


/***********************************************************
* Register Name : RECEIVED_MODIFIED_TS_DATA1
************************************************************/

#define RECEIVED_MODIFIED_TS_DATA1_RECEIVED_MODIFIED_TS_USAGE_MODE_OFFSET 0
#define RECEIVED_MODIFIED_TS_DATA1_RECEIVED_MODIFIED_TS_USAGE_MODE_MASK 0x7

#define RECEIVED_MODIFIED_TS_DATA1_RECEIVED_MODIFIED_TS_INFORMATION_1_OFFSET 3
#define RECEIVED_MODIFIED_TS_DATA1_RECEIVED_MODIFIED_TS_INFORMATION_1_MASK 0xfff8

#define RECEIVED_MODIFIED_TS_DATA1_RECEIVED_MODIFIED_TS_VENDOR_ID_OFFSET 16
#define RECEIVED_MODIFIED_TS_DATA1_RECEIVED_MODIFIED_TS_VENDOR_ID_MASK 0xffff0000

typedef union {
  struct {
    UINT32                     RECEIVED_MODIFIED_TS_USAGE_MODE:3;
    UINT32                  RECEIVED_MODIFIED_TS_INFORMATION_1:13;
    UINT32                      RECEIVED_MODIFIED_TS_VENDOR_ID:16;
  } Field;
  UINT32 Value;
} RECEIVED_MODIFIED_TS_DATA1_NBIFRCCFG_STRUCT;

#define PCICFG_NBIFRCCFG_RECEIVED_MODIFIED_TS_DATA1_OFFSET          0x510
#define SMN_DEV0_NBIF0RCNBIO0_RECEIVED_MODIFIED_TS_DATA1_ADDRESS      0x10100510UL
#define SMN_DEV0_NBIF0RCNBIO1_RECEIVED_MODIFIED_TS_DATA1_ADDRESS      0x10300510UL
#define SMN_DEV0_NBIF1RCNBIO0_RECEIVED_MODIFIED_TS_DATA1_ADDRESS      0x10200510UL
#define SMN_DEV0_NBIF1RCNBIO1_RECEIVED_MODIFIED_TS_DATA1_ADDRESS      0x10400510UL
#define SMN_DEV1_NBIF0RCNBIO0_RECEIVED_MODIFIED_TS_DATA1_ADDRESS      0x10101510UL
#define SMN_DEV1_NBIF0RCNBIO1_RECEIVED_MODIFIED_TS_DATA1_ADDRESS      0x10301510UL
#define SMN_DEV1_NBIF1RCNBIO0_RECEIVED_MODIFIED_TS_DATA1_ADDRESS      0x10201510UL
#define SMN_DEV1_NBIF1RCNBIO1_RECEIVED_MODIFIED_TS_DATA1_ADDRESS      0x10401510UL


/***********************************************************
* Register Name : RECEIVED_MODIFIED_TS_DATA2
************************************************************/

#define RECEIVED_MODIFIED_TS_DATA2_RECEIVED_MODIFIED_TS_INFORMATION_2_OFFSET 0
#define RECEIVED_MODIFIED_TS_DATA2_RECEIVED_MODIFIED_TS_INFORMATION_2_MASK 0xffffff

#define RECEIVED_MODIFIED_TS_DATA2_RECEIVED_ALTERNATE_PROTOCOL_NEGOTIATION_STATUS_OFFSET 24
#define RECEIVED_MODIFIED_TS_DATA2_RECEIVED_ALTERNATE_PROTOCOL_NEGOTIATION_STATUS_MASK 0x3000000

#define RECEIVED_MODIFIED_TS_DATA2_Reserved_31_26_OFFSET       26
#define RECEIVED_MODIFIED_TS_DATA2_Reserved_31_26_MASK         0xfc000000

typedef union {
  struct {
    UINT32                  RECEIVED_MODIFIED_TS_INFORMATION_2:24;
    UINT32      RECEIVED_ALTERNATE_PROTOCOL_NEGOTIATION_STATUS:2;
    UINT32                                      Reserved_31_26:6;
  } Field;
  UINT32 Value;
} RECEIVED_MODIFIED_TS_DATA2_NBIFRCCFG_STRUCT;

#define PCICFG_NBIFRCCFG_RECEIVED_MODIFIED_TS_DATA2_OFFSET          0x514
#define SMN_DEV0_NBIF0RCNBIO0_RECEIVED_MODIFIED_TS_DATA2_ADDRESS      0x10100514UL
#define SMN_DEV0_NBIF0RCNBIO1_RECEIVED_MODIFIED_TS_DATA2_ADDRESS      0x10300514UL
#define SMN_DEV0_NBIF1RCNBIO0_RECEIVED_MODIFIED_TS_DATA2_ADDRESS      0x10200514UL
#define SMN_DEV0_NBIF1RCNBIO1_RECEIVED_MODIFIED_TS_DATA2_ADDRESS      0x10400514UL
#define SMN_DEV1_NBIF0RCNBIO0_RECEIVED_MODIFIED_TS_DATA2_ADDRESS      0x10101514UL
#define SMN_DEV1_NBIF0RCNBIO1_RECEIVED_MODIFIED_TS_DATA2_ADDRESS      0x10301514UL
#define SMN_DEV1_NBIF1RCNBIO0_RECEIVED_MODIFIED_TS_DATA2_ADDRESS      0x10201514UL
#define SMN_DEV1_NBIF1RCNBIO1_RECEIVED_MODIFIED_TS_DATA2_ADDRESS      0x10401514UL


/***********************************************************
* Register Name : REVISION_ID
************************************************************/

#define REVISION_ID_MINOR_REV_ID_OFFSET                        0
#define REVISION_ID_MINOR_REV_ID_MASK                          0xf

#define REVISION_ID_MAJOR_REV_ID_OFFSET                        4
#define REVISION_ID_MAJOR_REV_ID_MASK                          0xf0

typedef union {
  struct {
    UINT8                                        MINOR_REV_ID:4;
    UINT8                                        MAJOR_REV_ID:4;
  } Field;
  UINT8 Value;
} REVISION_ID_NBIFRCCFG_STRUCT;

#define PCICFG_NBIFRCCFG_REVISION_ID_OFFSET                         0x8
#define SMN_DEV0_NBIF0RCNBIO0_REVISION_ID_ADDRESS                     0x10100008UL
#define SMN_DEV0_NBIF0RCNBIO1_REVISION_ID_ADDRESS                     0x10300008UL
#define SMN_DEV0_NBIF1RCNBIO0_REVISION_ID_ADDRESS                     0x10200008UL
#define SMN_DEV0_NBIF1RCNBIO1_REVISION_ID_ADDRESS                     0x10400008UL
#define SMN_DEV1_NBIF0RCNBIO0_REVISION_ID_ADDRESS                     0x10101008UL
#define SMN_DEV1_NBIF0RCNBIO1_REVISION_ID_ADDRESS                     0x10301008UL
#define SMN_DEV1_NBIF1RCNBIO0_REVISION_ID_ADDRESS                     0x10201008UL
#define SMN_DEV1_NBIF1RCNBIO1_REVISION_ID_ADDRESS                     0x10401008UL


/***********************************************************
* Register Name : ROM_BASE_ADDR
************************************************************/


/***********************************************************
* Register Name : ROOT_CAP
************************************************************/

#define ROOT_CAP_CRS_SOFTWARE_VISIBILITY_OFFSET                0
#define ROOT_CAP_CRS_SOFTWARE_VISIBILITY_MASK                  0x1

#define ROOT_CAP_Reserved_15_1_OFFSET                          1
#define ROOT_CAP_Reserved_15_1_MASK                            0xfffe

typedef union {
  struct {
    UINT16                             CRS_SOFTWARE_VISIBILITY:1;
    UINT16                                       Reserved_15_1:15;
  } Field;
  UINT16 Value;
} ROOT_CAP_STRUCT;

#define PCICFG_NBIFRCCFG_ROOT_CAP_OFFSET                            0x76
#define SMN_DEV0_NBIF0RCNBIO0_ROOT_CAP_ADDRESS                        0x10100076UL
#define SMN_DEV0_NBIF0RCNBIO1_ROOT_CAP_ADDRESS                        0x10300076UL
#define SMN_DEV0_NBIF1RCNBIO0_ROOT_CAP_ADDRESS                        0x10200076UL
#define SMN_DEV0_NBIF1RCNBIO1_ROOT_CAP_ADDRESS                        0x10400076UL
#define SMN_DEV1_NBIF0RCNBIO0_ROOT_CAP_ADDRESS                        0x10101076UL
#define SMN_DEV1_NBIF0RCNBIO1_ROOT_CAP_ADDRESS                        0x10301076UL
#define SMN_DEV1_NBIF1RCNBIO0_ROOT_CAP_ADDRESS                        0x10201076UL
#define SMN_DEV1_NBIF1RCNBIO1_ROOT_CAP_ADDRESS                        0x10401076UL


/***********************************************************
* Register Name : ROOT_CNTL
************************************************************/

#define ROOT_CNTL_SERR_ON_CORR_ERR_EN_OFFSET                   0
#define ROOT_CNTL_SERR_ON_CORR_ERR_EN_MASK                     0x1

#define ROOT_CNTL_SERR_ON_NONFATAL_ERR_EN_OFFSET               1
#define ROOT_CNTL_SERR_ON_NONFATAL_ERR_EN_MASK                 0x2

#define ROOT_CNTL_SERR_ON_FATAL_ERR_EN_OFFSET                  2
#define ROOT_CNTL_SERR_ON_FATAL_ERR_EN_MASK                    0x4

#define ROOT_CNTL_PM_INTERRUPT_EN_OFFSET                       3
#define ROOT_CNTL_PM_INTERRUPT_EN_MASK                         0x8

#define ROOT_CNTL_CRS_SOFTWARE_VISIBILITY_EN_OFFSET            4
#define ROOT_CNTL_CRS_SOFTWARE_VISIBILITY_EN_MASK              0x10

#define ROOT_CNTL_Reserved_15_5_OFFSET                         5
#define ROOT_CNTL_Reserved_15_5_MASK                           0xffe0

typedef union {
  struct {
    UINT16                                 SERR_ON_CORR_ERR_EN:1;
    UINT16                             SERR_ON_NONFATAL_ERR_EN:1;
    UINT16                                SERR_ON_FATAL_ERR_EN:1;
    UINT16                                     PM_INTERRUPT_EN:1;
    UINT16                          CRS_SOFTWARE_VISIBILITY_EN:1;
    UINT16                                       Reserved_15_5:11;
  } Field;
  UINT16 Value;
} ROOT_CNTL_NBIFRCCFG_STRUCT;

#define PCICFG_NBIFRCCFG_ROOT_CNTL_OFFSET                           0x74
#define SMN_DEV0_NBIF0RCNBIO0_ROOT_CNTL_ADDRESS                       0x10100074UL
#define SMN_DEV0_NBIF0RCNBIO1_ROOT_CNTL_ADDRESS                       0x10300074UL
#define SMN_DEV0_NBIF1RCNBIO0_ROOT_CNTL_ADDRESS                       0x10200074UL
#define SMN_DEV0_NBIF1RCNBIO1_ROOT_CNTL_ADDRESS                       0x10400074UL
#define SMN_DEV1_NBIF0RCNBIO0_ROOT_CNTL_ADDRESS                       0x10101074UL
#define SMN_DEV1_NBIF0RCNBIO1_ROOT_CNTL_ADDRESS                       0x10301074UL
#define SMN_DEV1_NBIF1RCNBIO0_ROOT_CNTL_ADDRESS                       0x10201074UL
#define SMN_DEV1_NBIF1RCNBIO1_ROOT_CNTL_ADDRESS                       0x10401074UL


/***********************************************************
* Register Name : ROOT_STATUS
************************************************************/

#define ROOT_STATUS_PME_REQUESTOR_ID_OFFSET                    0
#define ROOT_STATUS_PME_REQUESTOR_ID_MASK                      0xffff

#define ROOT_STATUS_PME_STATUS_OFFSET                          16
#define ROOT_STATUS_PME_STATUS_MASK                            0x10000

#define ROOT_STATUS_PME_PENDING_OFFSET                         17
#define ROOT_STATUS_PME_PENDING_MASK                           0x20000

#define ROOT_STATUS_Reserved_31_18_OFFSET                      18
#define ROOT_STATUS_Reserved_31_18_MASK                        0xfffc0000

typedef union {
  struct {
    UINT32                                    PME_REQUESTOR_ID:16;
    UINT32                                          PME_STATUS:1;
    UINT32                                         PME_PENDING:1;
    UINT32                                      Reserved_31_18:14;
  } Field;
  UINT32 Value;
} ROOT_STATUS_STRUCT;

#define PCICFG_NBIFRCCFG_ROOT_STATUS_OFFSET                         0x78
#define SMN_DEV0_NBIF0RCNBIO0_ROOT_STATUS_ADDRESS                     0x10100078UL
#define SMN_DEV0_NBIF0RCNBIO1_ROOT_STATUS_ADDRESS                     0x10300078UL
#define SMN_DEV0_NBIF1RCNBIO0_ROOT_STATUS_ADDRESS                     0x10200078UL
#define SMN_DEV0_NBIF1RCNBIO1_ROOT_STATUS_ADDRESS                     0x10400078UL
#define SMN_DEV1_NBIF0RCNBIO0_ROOT_STATUS_ADDRESS                     0x10101078UL
#define SMN_DEV1_NBIF0RCNBIO1_ROOT_STATUS_ADDRESS                     0x10301078UL
#define SMN_DEV1_NBIF1RCNBIO0_ROOT_STATUS_ADDRESS                     0x10201078UL
#define SMN_DEV1_NBIF1RCNBIO1_ROOT_STATUS_ADDRESS                     0x10401078UL


/***********************************************************
* Register Name : RTM1_PARITY_MISMATCH_STATUS_16GT
************************************************************/

#define RTM1_PARITY_MISMATCH_STATUS_16GT_RTM1_PARITY_MISMATCH_STATUS_BITS_OFFSET 0
#define RTM1_PARITY_MISMATCH_STATUS_16GT_RTM1_PARITY_MISMATCH_STATUS_BITS_MASK 0xffff

#define RTM1_PARITY_MISMATCH_STATUS_16GT_Reserved_31_16_OFFSET 16
#define RTM1_PARITY_MISMATCH_STATUS_16GT_Reserved_31_16_MASK   0xffff0000

typedef union {
  struct {
    UINT32                    RTM1_PARITY_MISMATCH_STATUS_BITS:16;
    UINT32                                      Reserved_31_16:16;
  } Field;
  UINT32 Value;
} RTM1_PARITY_MISMATCH_STATUS_16GT_NBIFRCCFG_STRUCT;

#define PCICFG_NBIFRCCFG_RTM1_PARITY_MISMATCH_STATUS_16GT_OFFSET    0x424
#define SMN_DEV0_NBIF0RCNBIO0_RTM1_PARITY_MISMATCH_STATUS_16GT_ADDRESS 0x10100424UL
#define SMN_DEV0_NBIF0RCNBIO1_RTM1_PARITY_MISMATCH_STATUS_16GT_ADDRESS 0x10300424UL
#define SMN_DEV0_NBIF1RCNBIO0_RTM1_PARITY_MISMATCH_STATUS_16GT_ADDRESS 0x10200424UL
#define SMN_DEV0_NBIF1RCNBIO1_RTM1_PARITY_MISMATCH_STATUS_16GT_ADDRESS 0x10400424UL
#define SMN_DEV1_NBIF0RCNBIO0_RTM1_PARITY_MISMATCH_STATUS_16GT_ADDRESS 0x10101424UL
#define SMN_DEV1_NBIF0RCNBIO1_RTM1_PARITY_MISMATCH_STATUS_16GT_ADDRESS 0x10301424UL
#define SMN_DEV1_NBIF1RCNBIO0_RTM1_PARITY_MISMATCH_STATUS_16GT_ADDRESS 0x10201424UL
#define SMN_DEV1_NBIF1RCNBIO1_RTM1_PARITY_MISMATCH_STATUS_16GT_ADDRESS 0x10401424UL


/***********************************************************
* Register Name : RTM2_PARITY_MISMATCH_STATUS_16GT
************************************************************/

#define RTM2_PARITY_MISMATCH_STATUS_16GT_RTM2_PARITY_MISMATCH_STATUS_BITS_OFFSET 0
#define RTM2_PARITY_MISMATCH_STATUS_16GT_RTM2_PARITY_MISMATCH_STATUS_BITS_MASK 0xffff

#define RTM2_PARITY_MISMATCH_STATUS_16GT_Reserved_31_16_OFFSET 16
#define RTM2_PARITY_MISMATCH_STATUS_16GT_Reserved_31_16_MASK   0xffff0000

typedef union {
  struct {
    UINT32                    RTM2_PARITY_MISMATCH_STATUS_BITS:16;
    UINT32                                      Reserved_31_16:16;
  } Field;
  UINT32 Value;
} RTM2_PARITY_MISMATCH_STATUS_16GT_NBIFRCCFG_STRUCT;

#define PCICFG_NBIFRCCFG_RTM2_PARITY_MISMATCH_STATUS_16GT_OFFSET    0x428
#define SMN_DEV0_NBIF0RCNBIO0_RTM2_PARITY_MISMATCH_STATUS_16GT_ADDRESS 0x10100428UL
#define SMN_DEV0_NBIF0RCNBIO1_RTM2_PARITY_MISMATCH_STATUS_16GT_ADDRESS 0x10300428UL
#define SMN_DEV0_NBIF1RCNBIO0_RTM2_PARITY_MISMATCH_STATUS_16GT_ADDRESS 0x10200428UL
#define SMN_DEV0_NBIF1RCNBIO1_RTM2_PARITY_MISMATCH_STATUS_16GT_ADDRESS 0x10400428UL
#define SMN_DEV1_NBIF0RCNBIO0_RTM2_PARITY_MISMATCH_STATUS_16GT_ADDRESS 0x10101428UL
#define SMN_DEV1_NBIF0RCNBIO1_RTM2_PARITY_MISMATCH_STATUS_16GT_ADDRESS 0x10301428UL
#define SMN_DEV1_NBIF1RCNBIO0_RTM2_PARITY_MISMATCH_STATUS_16GT_ADDRESS 0x10201428UL
#define SMN_DEV1_NBIF1RCNBIO1_RTM2_PARITY_MISMATCH_STATUS_16GT_ADDRESS 0x10401428UL


/***********************************************************
* Register Name : RTR_DATA1
************************************************************/

#define RTR_DATA1_RESET_TIME_OFFSET                            0
#define RTR_DATA1_RESET_TIME_MASK                              0xfff

#define RTR_DATA1_DLUP_TIME_OFFSET                             12
#define RTR_DATA1_DLUP_TIME_MASK                               0xfff000

#define RTR_DATA1_Reserved_30_24_OFFSET                        24
#define RTR_DATA1_Reserved_30_24_MASK                          0x7f000000

#define RTR_DATA1_VALID_OFFSET                                 31
#define RTR_DATA1_VALID_MASK                                   0x80000000

typedef union {
  struct {
    UINT32                                          RESET_TIME:12;
    UINT32                                           DLUP_TIME:12;
    UINT32                                      Reserved_30_24:7;
    UINT32                                               VALID:1;
  } Field;
  UINT32 Value;
} RTR_DATA1_NBIFRCCFG_STRUCT;

#define PCICFG_NBIFRCCFG_RTR_DATA1_OFFSET                           0x574
#define SMN_DEV0_NBIF0RCNBIO0_RTR_DATA1_ADDRESS                       0x10100574UL
#define SMN_DEV0_NBIF0RCNBIO1_RTR_DATA1_ADDRESS                       0x10300574UL
#define SMN_DEV0_NBIF1RCNBIO0_RTR_DATA1_ADDRESS                       0x10200574UL
#define SMN_DEV0_NBIF1RCNBIO1_RTR_DATA1_ADDRESS                       0x10400574UL
#define SMN_DEV1_NBIF0RCNBIO0_RTR_DATA1_ADDRESS                       0x10101574UL
#define SMN_DEV1_NBIF0RCNBIO1_RTR_DATA1_ADDRESS                       0x10301574UL
#define SMN_DEV1_NBIF1RCNBIO0_RTR_DATA1_ADDRESS                       0x10201574UL
#define SMN_DEV1_NBIF1RCNBIO1_RTR_DATA1_ADDRESS                       0x10401574UL


/***********************************************************
* Register Name : RTR_DATA2
************************************************************/

#define RTR_DATA2_FLR_TIME_OFFSET                              0
#define RTR_DATA2_FLR_TIME_MASK                                0xfff

#define RTR_DATA2_D3HOTD0_TIME_OFFSET                          12
#define RTR_DATA2_D3HOTD0_TIME_MASK                            0xfff000

#define RTR_DATA2_Reserved_31_24_OFFSET                        24
#define RTR_DATA2_Reserved_31_24_MASK                          0xff000000

typedef union {
  struct {
    UINT32                                            FLR_TIME:12;
    UINT32                                        D3HOTD0_TIME:12;
    UINT32                                      Reserved_31_24:8;
  } Field;
  UINT32 Value;
} RTR_DATA2_NBIFRCCFG_STRUCT;

#define PCICFG_NBIFRCCFG_RTR_DATA2_OFFSET                           0x578
#define SMN_DEV0_NBIF0RCNBIO0_RTR_DATA2_ADDRESS                       0x10100578UL
#define SMN_DEV0_NBIF0RCNBIO1_RTR_DATA2_ADDRESS                       0x10300578UL
#define SMN_DEV0_NBIF1RCNBIO0_RTR_DATA2_ADDRESS                       0x10200578UL
#define SMN_DEV0_NBIF1RCNBIO1_RTR_DATA2_ADDRESS                       0x10400578UL
#define SMN_DEV1_NBIF0RCNBIO0_RTR_DATA2_ADDRESS                       0x10101578UL
#define SMN_DEV1_NBIF0RCNBIO1_RTR_DATA2_ADDRESS                       0x10301578UL
#define SMN_DEV1_NBIF1RCNBIO0_RTR_DATA2_ADDRESS                       0x10201578UL
#define SMN_DEV1_NBIF1RCNBIO1_RTR_DATA2_ADDRESS                       0x10401578UL


/***********************************************************
* Register Name : SECONDARY_STATUS
************************************************************/

#define SECONDARY_STATUS_Reserved_4_0_OFFSET                   0
#define SECONDARY_STATUS_Reserved_4_0_MASK                     0x1f

#define SECONDARY_STATUS_PCI_66_CAP_OFFSET                     5
#define SECONDARY_STATUS_PCI_66_CAP_MASK                       0x20

#define SECONDARY_STATUS_Reserved_6_6_OFFSET                   6
#define SECONDARY_STATUS_Reserved_6_6_MASK                     0x40

#define SECONDARY_STATUS_FAST_BACK_CAPABLE_OFFSET              7
#define SECONDARY_STATUS_FAST_BACK_CAPABLE_MASK                0x80

#define SECONDARY_STATUS_MASTER_DATA_PARITY_ERROR_OFFSET       8
#define SECONDARY_STATUS_MASTER_DATA_PARITY_ERROR_MASK         0x100

#define SECONDARY_STATUS_DEVSEL_TIMING_OFFSET                  9
#define SECONDARY_STATUS_DEVSEL_TIMING_MASK                    0x600

#define SECONDARY_STATUS_SIGNAL_TARGET_ABORT_OFFSET            11
#define SECONDARY_STATUS_SIGNAL_TARGET_ABORT_MASK              0x800

#define SECONDARY_STATUS_RECEIVED_TARGET_ABORT_OFFSET          12
#define SECONDARY_STATUS_RECEIVED_TARGET_ABORT_MASK            0x1000

#define SECONDARY_STATUS_RECEIVED_MASTER_ABORT_OFFSET          13
#define SECONDARY_STATUS_RECEIVED_MASTER_ABORT_MASK            0x2000

#define SECONDARY_STATUS_RECEIVED_SYSTEM_ERROR_OFFSET          14
#define SECONDARY_STATUS_RECEIVED_SYSTEM_ERROR_MASK            0x4000

#define SECONDARY_STATUS_PARITY_ERROR_DETECTED_OFFSET          15
#define SECONDARY_STATUS_PARITY_ERROR_DETECTED_MASK            0x8000

typedef union {
  struct {
    UINT16                                        Reserved_4_0:5;
    UINT16                                          PCI_66_CAP:1;
    UINT16                                        Reserved_6_6:1;
    UINT16                                   FAST_BACK_CAPABLE:1;
    UINT16                            MASTER_DATA_PARITY_ERROR:1;
    UINT16                                       DEVSEL_TIMING:2;
    UINT16                                 SIGNAL_TARGET_ABORT:1;
    UINT16                               RECEIVED_TARGET_ABORT:1;
    UINT16                               RECEIVED_MASTER_ABORT:1;
    UINT16                               RECEIVED_SYSTEM_ERROR:1;
    UINT16                               PARITY_ERROR_DETECTED:1;
  } Field;
  UINT16 Value;
} SECONDARY_STATUS_STRUCT;

#define PCICFG_NBIFRCCFG_SECONDARY_STATUS_OFFSET                    0x1e
#define SMN_DEV0_NBIF0RCNBIO0_SECONDARY_STATUS_ADDRESS                0x1010001eUL
#define SMN_DEV0_NBIF0RCNBIO1_SECONDARY_STATUS_ADDRESS                0x1030001eUL
#define SMN_DEV0_NBIF1RCNBIO0_SECONDARY_STATUS_ADDRESS                0x1020001eUL
#define SMN_DEV0_NBIF1RCNBIO1_SECONDARY_STATUS_ADDRESS                0x1040001eUL
#define SMN_DEV1_NBIF0RCNBIO0_SECONDARY_STATUS_ADDRESS                0x1010101eUL
#define SMN_DEV1_NBIF0RCNBIO1_SECONDARY_STATUS_ADDRESS                0x1030101eUL
#define SMN_DEV1_NBIF1RCNBIO0_SECONDARY_STATUS_ADDRESS                0x1020101eUL
#define SMN_DEV1_NBIF1RCNBIO1_SECONDARY_STATUS_ADDRESS                0x1040101eUL


/***********************************************************
* Register Name : SLOT_CAP
************************************************************/

#define SLOT_CAP_ATTN_BUTTON_PRESENT_OFFSET                    0
#define SLOT_CAP_ATTN_BUTTON_PRESENT_MASK                      0x1

#define SLOT_CAP_PWR_CONTROLLER_PRESENT_OFFSET                 1
#define SLOT_CAP_PWR_CONTROLLER_PRESENT_MASK                   0x2

#define SLOT_CAP_MRL_SENSOR_PRESENT_OFFSET                     2
#define SLOT_CAP_MRL_SENSOR_PRESENT_MASK                       0x4

#define SLOT_CAP_ATTN_INDICATOR_PRESENT_OFFSET                 3
#define SLOT_CAP_ATTN_INDICATOR_PRESENT_MASK                   0x8

#define SLOT_CAP_PWR_INDICATOR_PRESENT_OFFSET                  4
#define SLOT_CAP_PWR_INDICATOR_PRESENT_MASK                    0x10

#define SLOT_CAP_HOTPLUG_SURPRISE_OFFSET                       5
#define SLOT_CAP_HOTPLUG_SURPRISE_MASK                         0x20

#define SLOT_CAP_HOTPLUG_CAPABLE_OFFSET                        6
#define SLOT_CAP_HOTPLUG_CAPABLE_MASK                          0x40

#define SLOT_CAP_SLOT_PWR_LIMIT_VALUE_OFFSET                   7
#define SLOT_CAP_SLOT_PWR_LIMIT_VALUE_MASK                     0x7f80

#define SLOT_CAP_SLOT_PWR_LIMIT_SCALE_OFFSET                   15
#define SLOT_CAP_SLOT_PWR_LIMIT_SCALE_MASK                     0x18000

#define SLOT_CAP_ELECTROMECH_INTERLOCK_PRESENT_OFFSET          17
#define SLOT_CAP_ELECTROMECH_INTERLOCK_PRESENT_MASK            0x20000

#define SLOT_CAP_NO_COMMAND_COMPLETED_SUPPORTED_OFFSET         18
#define SLOT_CAP_NO_COMMAND_COMPLETED_SUPPORTED_MASK           0x40000

#define SLOT_CAP_PHYSICAL_SLOT_NUM_OFFSET                      19
#define SLOT_CAP_PHYSICAL_SLOT_NUM_MASK                        0xfff80000

typedef union {
  struct {
    UINT32                                 ATTN_BUTTON_PRESENT:1;
    UINT32                              PWR_CONTROLLER_PRESENT:1;
    UINT32                                  MRL_SENSOR_PRESENT:1;
    UINT32                              ATTN_INDICATOR_PRESENT:1;
    UINT32                               PWR_INDICATOR_PRESENT:1;
    UINT32                                    HOTPLUG_SURPRISE:1;
    UINT32                                     HOTPLUG_CAPABLE:1;
    UINT32                                SLOT_PWR_LIMIT_VALUE:8;
    UINT32                                SLOT_PWR_LIMIT_SCALE:2;
    UINT32                       ELECTROMECH_INTERLOCK_PRESENT:1;
    UINT32                      NO_COMMAND_COMPLETED_SUPPORTED:1;
    UINT32                                   PHYSICAL_SLOT_NUM:13;
  } Field;
  UINT32 Value;
} SLOT_CAP_NBIFRCCFG_STRUCT;

#define PCICFG_NBIFRCCFG_SLOT_CAP_OFFSET                            0x6c
#define SMN_DEV0_NBIF0RCNBIO0_SLOT_CAP_ADDRESS                        0x1010006cUL
#define SMN_DEV0_NBIF0RCNBIO1_SLOT_CAP_ADDRESS                        0x1030006cUL
#define SMN_DEV0_NBIF1RCNBIO0_SLOT_CAP_ADDRESS                        0x1020006cUL
#define SMN_DEV0_NBIF1RCNBIO1_SLOT_CAP_ADDRESS                        0x1040006cUL
#define SMN_DEV1_NBIF0RCNBIO0_SLOT_CAP_ADDRESS                        0x1010106cUL
#define SMN_DEV1_NBIF0RCNBIO1_SLOT_CAP_ADDRESS                        0x1030106cUL
#define SMN_DEV1_NBIF1RCNBIO0_SLOT_CAP_ADDRESS                        0x1020106cUL
#define SMN_DEV1_NBIF1RCNBIO1_SLOT_CAP_ADDRESS                        0x1040106cUL


/***********************************************************
* Register Name : SLOT_CAP2
************************************************************/

#define SLOT_CAP2_INBAND_PD_DISABLE_SUPPORTED_OFFSET           0
#define SLOT_CAP2_INBAND_PD_DISABLE_SUPPORTED_MASK             0x1

#define SLOT_CAP2_Reserved_31_1_OFFSET                         1
#define SLOT_CAP2_Reserved_31_1_MASK                           0xfffffffe

typedef union {
  struct {
    UINT32                         INBAND_PD_DISABLE_SUPPORTED:1;
    UINT32                                       Reserved_31_1:31;
  } Field;
  UINT32 Value;
} SLOT_CAP2_STRUCT;

#define PCICFG_NBIFRCCFG_SLOT_CAP2_OFFSET                           0x8c
#define SMN_DEV0_NBIF0RCNBIO0_SLOT_CAP2_ADDRESS                       0x1010008cUL
#define SMN_DEV0_NBIF0RCNBIO1_SLOT_CAP2_ADDRESS                       0x1030008cUL
#define SMN_DEV0_NBIF1RCNBIO0_SLOT_CAP2_ADDRESS                       0x1020008cUL
#define SMN_DEV0_NBIF1RCNBIO1_SLOT_CAP2_ADDRESS                       0x1040008cUL
#define SMN_DEV1_NBIF0RCNBIO0_SLOT_CAP2_ADDRESS                       0x1010108cUL
#define SMN_DEV1_NBIF0RCNBIO1_SLOT_CAP2_ADDRESS                       0x1030108cUL
#define SMN_DEV1_NBIF1RCNBIO0_SLOT_CAP2_ADDRESS                       0x1020108cUL
#define SMN_DEV1_NBIF1RCNBIO1_SLOT_CAP2_ADDRESS                       0x1040108cUL


/***********************************************************
* Register Name : SLOT_CNTL
************************************************************/

#define SLOT_CNTL_ATTN_BUTTON_PRESSED_EN_OFFSET                0
#define SLOT_CNTL_ATTN_BUTTON_PRESSED_EN_MASK                  0x1

#define SLOT_CNTL_PWR_FAULT_DETECTED_EN_OFFSET                 1
#define SLOT_CNTL_PWR_FAULT_DETECTED_EN_MASK                   0x2

#define SLOT_CNTL_MRL_SENSOR_CHANGED_EN_OFFSET                 2
#define SLOT_CNTL_MRL_SENSOR_CHANGED_EN_MASK                   0x4

#define SLOT_CNTL_PRESENCE_DETECT_CHANGED_EN_OFFSET            3
#define SLOT_CNTL_PRESENCE_DETECT_CHANGED_EN_MASK              0x8

#define SLOT_CNTL_COMMAND_COMPLETED_INTR_EN_OFFSET             4
#define SLOT_CNTL_COMMAND_COMPLETED_INTR_EN_MASK               0x10

#define SLOT_CNTL_HOTPLUG_INTR_EN_OFFSET                       5
#define SLOT_CNTL_HOTPLUG_INTR_EN_MASK                         0x20

#define SLOT_CNTL_ATTN_INDICATOR_CNTL_OFFSET                   6
#define SLOT_CNTL_ATTN_INDICATOR_CNTL_MASK                     0xc0

#define SLOT_CNTL_PWR_INDICATOR_CNTL_OFFSET                    8
#define SLOT_CNTL_PWR_INDICATOR_CNTL_MASK                      0x300

#define SLOT_CNTL_PWR_CONTROLLER_CNTL_OFFSET                   10
#define SLOT_CNTL_PWR_CONTROLLER_CNTL_MASK                     0x400

#define SLOT_CNTL_ELECTROMECH_INTERLOCK_CNTL_OFFSET            11
#define SLOT_CNTL_ELECTROMECH_INTERLOCK_CNTL_MASK              0x800

#define SLOT_CNTL_DL_STATE_CHANGED_EN_OFFSET                   12
#define SLOT_CNTL_DL_STATE_CHANGED_EN_MASK                     0x1000

#define SLOT_CNTL_AUTO_SLOT_PWR_LIMIT_DISABLE_OFFSET           13
#define SLOT_CNTL_AUTO_SLOT_PWR_LIMIT_DISABLE_MASK             0x2000

#define SLOT_CNTL_INBAND_PD_DISABLE_OFFSET                     14
#define SLOT_CNTL_INBAND_PD_DISABLE_MASK                       0x4000

#define SLOT_CNTL_Reserved_15_15_OFFSET                        15
#define SLOT_CNTL_Reserved_15_15_MASK                          0x8000

typedef union {
  struct {
    UINT16                              ATTN_BUTTON_PRESSED_EN:1;
    UINT16                               PWR_FAULT_DETECTED_EN:1;
    UINT16                               MRL_SENSOR_CHANGED_EN:1;
    UINT16                          PRESENCE_DETECT_CHANGED_EN:1;
    UINT16                           COMMAND_COMPLETED_INTR_EN:1;
    UINT16                                     HOTPLUG_INTR_EN:1;
    UINT16                                 ATTN_INDICATOR_CNTL:2;
    UINT16                                  PWR_INDICATOR_CNTL:2;
    UINT16                                 PWR_CONTROLLER_CNTL:1;
    UINT16                          ELECTROMECH_INTERLOCK_CNTL:1;
    UINT16                                 DL_STATE_CHANGED_EN:1;
    UINT16                         AUTO_SLOT_PWR_LIMIT_DISABLE:1;
    UINT16                                   INBAND_PD_DISABLE:1;
    UINT16                                      Reserved_15_15:1;
  } Field;
  UINT16 Value;
} SLOT_CNTL_STRUCT;

#define PCICFG_NBIFRCCFG_SLOT_CNTL_OFFSET                           0x70
#define SMN_DEV0_NBIF0RCNBIO0_SLOT_CNTL_ADDRESS                       0x10100070UL
#define SMN_DEV0_NBIF0RCNBIO1_SLOT_CNTL_ADDRESS                       0x10300070UL
#define SMN_DEV0_NBIF1RCNBIO0_SLOT_CNTL_ADDRESS                       0x10200070UL
#define SMN_DEV0_NBIF1RCNBIO1_SLOT_CNTL_ADDRESS                       0x10400070UL
#define SMN_DEV1_NBIF0RCNBIO0_SLOT_CNTL_ADDRESS                       0x10101070UL
#define SMN_DEV1_NBIF0RCNBIO1_SLOT_CNTL_ADDRESS                       0x10301070UL
#define SMN_DEV1_NBIF1RCNBIO0_SLOT_CNTL_ADDRESS                       0x10201070UL
#define SMN_DEV1_NBIF1RCNBIO1_SLOT_CNTL_ADDRESS                       0x10401070UL


/***********************************************************
* Register Name : SLOT_CNTL2
************************************************************/

#define SLOT_CNTL2_Reserved_15_0_OFFSET                        0
#define SLOT_CNTL2_Reserved_15_0_MASK                          0xffff

typedef union {
  struct {
    UINT16                                       Reserved_15_0:16;
  } Field;
  UINT16 Value;
} SLOT_CNTL2_STRUCT;

#define PCICFG_NBIFRCCFG_SLOT_CNTL2_OFFSET                          0x90
#define SMN_DEV0_NBIF0RCNBIO0_SLOT_CNTL2_ADDRESS                      0x10100090UL
#define SMN_DEV0_NBIF0RCNBIO1_SLOT_CNTL2_ADDRESS                      0x10300090UL
#define SMN_DEV0_NBIF1RCNBIO0_SLOT_CNTL2_ADDRESS                      0x10200090UL
#define SMN_DEV0_NBIF1RCNBIO1_SLOT_CNTL2_ADDRESS                      0x10400090UL
#define SMN_DEV1_NBIF0RCNBIO0_SLOT_CNTL2_ADDRESS                      0x10101090UL
#define SMN_DEV1_NBIF0RCNBIO1_SLOT_CNTL2_ADDRESS                      0x10301090UL
#define SMN_DEV1_NBIF1RCNBIO0_SLOT_CNTL2_ADDRESS                      0x10201090UL
#define SMN_DEV1_NBIF1RCNBIO1_SLOT_CNTL2_ADDRESS                      0x10401090UL


/***********************************************************
* Register Name : SLOT_STATUS
************************************************************/

#define SLOT_STATUS_ATTN_BUTTON_PRESSED_OFFSET                 0
#define SLOT_STATUS_ATTN_BUTTON_PRESSED_MASK                   0x1

#define SLOT_STATUS_PWR_FAULT_DETECTED_OFFSET                  1
#define SLOT_STATUS_PWR_FAULT_DETECTED_MASK                    0x2

#define SLOT_STATUS_MRL_SENSOR_CHANGED_OFFSET                  2
#define SLOT_STATUS_MRL_SENSOR_CHANGED_MASK                    0x4

#define SLOT_STATUS_PRESENCE_DETECT_CHANGED_OFFSET             3
#define SLOT_STATUS_PRESENCE_DETECT_CHANGED_MASK               0x8

#define SLOT_STATUS_COMMAND_COMPLETED_OFFSET                   4
#define SLOT_STATUS_COMMAND_COMPLETED_MASK                     0x10

#define SLOT_STATUS_MRL_SENSOR_STATE_OFFSET                    5
#define SLOT_STATUS_MRL_SENSOR_STATE_MASK                      0x20

#define SLOT_STATUS_PRESENCE_DETECT_STATE_OFFSET               6
#define SLOT_STATUS_PRESENCE_DETECT_STATE_MASK                 0x40

#define SLOT_STATUS_ELECTROMECH_INTERLOCK_STATUS_OFFSET        7
#define SLOT_STATUS_ELECTROMECH_INTERLOCK_STATUS_MASK          0x80

#define SLOT_STATUS_DL_STATE_CHANGED_OFFSET                    8
#define SLOT_STATUS_DL_STATE_CHANGED_MASK                      0x100

#define SLOT_STATUS_Reserved_15_9_OFFSET                       9
#define SLOT_STATUS_Reserved_15_9_MASK                         0xfe00

typedef union {
  struct {
    UINT16                                 ATTN_BUTTON_PRESSED:1;
    UINT16                                  PWR_FAULT_DETECTED:1;
    UINT16                                  MRL_SENSOR_CHANGED:1;
    UINT16                             PRESENCE_DETECT_CHANGED:1;
    UINT16                                   COMMAND_COMPLETED:1;
    UINT16                                    MRL_SENSOR_STATE:1;
    UINT16                               PRESENCE_DETECT_STATE:1;
    UINT16                        ELECTROMECH_INTERLOCK_STATUS:1;
    UINT16                                    DL_STATE_CHANGED:1;
    UINT16                                       Reserved_15_9:7;
  } Field;
  UINT16 Value;
} SLOT_STATUS_STRUCT;

#define PCICFG_NBIFRCCFG_SLOT_STATUS_OFFSET                         0x72
#define SMN_DEV0_NBIF0RCNBIO0_SLOT_STATUS_ADDRESS                     0x10100072UL
#define SMN_DEV0_NBIF0RCNBIO1_SLOT_STATUS_ADDRESS                     0x10300072UL
#define SMN_DEV0_NBIF1RCNBIO0_SLOT_STATUS_ADDRESS                     0x10200072UL
#define SMN_DEV0_NBIF1RCNBIO1_SLOT_STATUS_ADDRESS                     0x10400072UL
#define SMN_DEV1_NBIF0RCNBIO0_SLOT_STATUS_ADDRESS                     0x10101072UL
#define SMN_DEV1_NBIF0RCNBIO1_SLOT_STATUS_ADDRESS                     0x10301072UL
#define SMN_DEV1_NBIF1RCNBIO0_SLOT_STATUS_ADDRESS                     0x10201072UL
#define SMN_DEV1_NBIF1RCNBIO1_SLOT_STATUS_ADDRESS                     0x10401072UL


/***********************************************************
* Register Name : SLOT_STATUS2
************************************************************/

#define SLOT_STATUS2_Reserved_15_0_OFFSET                      0
#define SLOT_STATUS2_Reserved_15_0_MASK                        0xffff

typedef union {
  struct {
    UINT16                                       Reserved_15_0:16;
  } Field;
  UINT16 Value;
} SLOT_STATUS2_STRUCT;

#define PCICFG_NBIFRCCFG_SLOT_STATUS2_OFFSET                        0x92
#define SMN_DEV0_NBIF0RCNBIO0_SLOT_STATUS2_ADDRESS                    0x10100092UL
#define SMN_DEV0_NBIF0RCNBIO1_SLOT_STATUS2_ADDRESS                    0x10300092UL
#define SMN_DEV0_NBIF1RCNBIO0_SLOT_STATUS2_ADDRESS                    0x10200092UL
#define SMN_DEV0_NBIF1RCNBIO1_SLOT_STATUS2_ADDRESS                    0x10400092UL
#define SMN_DEV1_NBIF0RCNBIO0_SLOT_STATUS2_ADDRESS                    0x10101092UL
#define SMN_DEV1_NBIF0RCNBIO1_SLOT_STATUS2_ADDRESS                    0x10301092UL
#define SMN_DEV1_NBIF1RCNBIO0_SLOT_STATUS2_ADDRESS                    0x10201092UL
#define SMN_DEV1_NBIF1RCNBIO1_SLOT_STATUS2_ADDRESS                    0x10401092UL


/***********************************************************
* Register Name : SSID_CAP
************************************************************/

#define SSID_CAP_SUBSYSTEM_VENDOR_ID_OFFSET                    0
#define SSID_CAP_SUBSYSTEM_VENDOR_ID_MASK                      0xffff

#define SSID_CAP_SUBSYSTEM_ID_OFFSET                           16
#define SSID_CAP_SUBSYSTEM_ID_MASK                             0xffff0000

typedef union {
  struct {
    UINT32                                 SUBSYSTEM_VENDOR_ID:16;
    UINT32                                        SUBSYSTEM_ID:16;
  } Field;
  UINT32 Value;
} SSID_CAP_STRUCT;

#define PCICFG_NBIFRCCFG_SSID_CAP_OFFSET                            0xc4
#define SMN_DEV0_NBIF0RCNBIO0_SSID_CAP_ADDRESS                        0x101000c4UL
#define SMN_DEV0_NBIF0RCNBIO1_SSID_CAP_ADDRESS                        0x103000c4UL
#define SMN_DEV0_NBIF1RCNBIO0_SSID_CAP_ADDRESS                        0x102000c4UL
#define SMN_DEV0_NBIF1RCNBIO1_SSID_CAP_ADDRESS                        0x104000c4UL
#define SMN_DEV1_NBIF0RCNBIO0_SSID_CAP_ADDRESS                        0x101010c4UL
#define SMN_DEV1_NBIF0RCNBIO1_SSID_CAP_ADDRESS                        0x103010c4UL
#define SMN_DEV1_NBIF1RCNBIO0_SSID_CAP_ADDRESS                        0x102010c4UL
#define SMN_DEV1_NBIF1RCNBIO1_SSID_CAP_ADDRESS                        0x104010c4UL


/***********************************************************
* Register Name : SSID_CAP_LIST
************************************************************/

#define SSID_CAP_LIST_CAP_ID_OFFSET                            0
#define SSID_CAP_LIST_CAP_ID_MASK                              0xff

#define SSID_CAP_LIST_NEXT_PTR_OFFSET                          8
#define SSID_CAP_LIST_NEXT_PTR_MASK                            0xff00

typedef union {
  struct {
    UINT16                                              CAP_ID:8;
    UINT16                                            NEXT_PTR:8;
  } Field;
  UINT16 Value;
} SSID_CAP_LIST_STRUCT;

#define PCICFG_NBIFRCCFG_SSID_CAP_LIST_OFFSET                       0xc0
#define SMN_DEV0_NBIF0RCNBIO0_SSID_CAP_LIST_ADDRESS                   0x101000c0UL
#define SMN_DEV0_NBIF0RCNBIO1_SSID_CAP_LIST_ADDRESS                   0x103000c0UL
#define SMN_DEV0_NBIF1RCNBIO0_SSID_CAP_LIST_ADDRESS                   0x102000c0UL
#define SMN_DEV0_NBIF1RCNBIO1_SSID_CAP_LIST_ADDRESS                   0x104000c0UL
#define SMN_DEV1_NBIF0RCNBIO0_SSID_CAP_LIST_ADDRESS                   0x101010c0UL
#define SMN_DEV1_NBIF0RCNBIO1_SSID_CAP_LIST_ADDRESS                   0x103010c0UL
#define SMN_DEV1_NBIF1RCNBIO0_SSID_CAP_LIST_ADDRESS                   0x102010c0UL
#define SMN_DEV1_NBIF1RCNBIO1_SSID_CAP_LIST_ADDRESS                   0x104010c0UL


/***********************************************************
* Register Name : STATUS
************************************************************/

#define STATUS_IMMEDIATE_READINESS_OFFSET                      0
#define STATUS_IMMEDIATE_READINESS_MASK                        0x1

#define STATUS_Reserved_2_1_OFFSET                             1
#define STATUS_Reserved_2_1_MASK                               0x6

#define STATUS_INT_STATUS_OFFSET                               3
#define STATUS_INT_STATUS_MASK                                 0x8

#define STATUS_CAP_LIST_OFFSET                                 4
#define STATUS_CAP_LIST_MASK                                   0x10

#define STATUS_PCI_66_CAP_OFFSET                               5
#define STATUS_PCI_66_CAP_MASK                                 0x20

#define STATUS_Reserved_6_6_OFFSET                             6
#define STATUS_Reserved_6_6_MASK                               0x40

#define STATUS_FAST_BACK_CAPABLE_OFFSET                        7
#define STATUS_FAST_BACK_CAPABLE_MASK                          0x80

#define STATUS_MASTER_DATA_PARITY_ERROR_OFFSET                 8
#define STATUS_MASTER_DATA_PARITY_ERROR_MASK                   0x100

#define STATUS_DEVSEL_TIMING_OFFSET                            9
#define STATUS_DEVSEL_TIMING_MASK                              0x600

#define STATUS_SIGNAL_TARGET_ABORT_OFFSET                      11
#define STATUS_SIGNAL_TARGET_ABORT_MASK                        0x800

#define STATUS_RECEIVED_TARGET_ABORT_OFFSET                    12
#define STATUS_RECEIVED_TARGET_ABORT_MASK                      0x1000

#define STATUS_RECEIVED_MASTER_ABORT_OFFSET                    13
#define STATUS_RECEIVED_MASTER_ABORT_MASK                      0x2000

#define STATUS_SIGNALED_SYSTEM_ERROR_OFFSET                    14
#define STATUS_SIGNALED_SYSTEM_ERROR_MASK                      0x4000

#define STATUS_PARITY_ERROR_DETECTED_OFFSET                    15
#define STATUS_PARITY_ERROR_DETECTED_MASK                      0x8000

typedef union {
  struct {
    UINT16                                 IMMEDIATE_READINESS:1;
    UINT16                                        Reserved_2_1:2;
    UINT16                                          INT_STATUS:1;
    UINT16                                            CAP_LIST:1;
    UINT16                                          PCI_66_CAP:1;
    UINT16                                        Reserved_6_6:1;
    UINT16                                   FAST_BACK_CAPABLE:1;
    UINT16                            MASTER_DATA_PARITY_ERROR:1;
    UINT16                                       DEVSEL_TIMING:2;
    UINT16                                 SIGNAL_TARGET_ABORT:1;
    UINT16                               RECEIVED_TARGET_ABORT:1;
    UINT16                               RECEIVED_MASTER_ABORT:1;
    UINT16                               SIGNALED_SYSTEM_ERROR:1;
    UINT16                               PARITY_ERROR_DETECTED:1;
  } Field;
  UINT16 Value;
} STATUS_NBIFRCCFG_STRUCT;

#define PCICFG_NBIFRCCFG_STATUS_OFFSET                              0x6
#define SMN_DEV0_NBIF0RCNBIO0_STATUS_ADDRESS                          0x10100006UL
#define SMN_DEV0_NBIF0RCNBIO1_STATUS_ADDRESS                          0x10300006UL
#define SMN_DEV0_NBIF1RCNBIO0_STATUS_ADDRESS                          0x10200006UL
#define SMN_DEV0_NBIF1RCNBIO1_STATUS_ADDRESS                          0x10400006UL
#define SMN_DEV1_NBIF0RCNBIO0_STATUS_ADDRESS                          0x10101006UL
#define SMN_DEV1_NBIF0RCNBIO1_STATUS_ADDRESS                          0x10301006UL
#define SMN_DEV1_NBIF1RCNBIO0_STATUS_ADDRESS                          0x10201006UL
#define SMN_DEV1_NBIF1RCNBIO1_STATUS_ADDRESS                          0x10401006UL


/***********************************************************
* Register Name : SUB_BUS_NUMBER_LATENCY
************************************************************/

#define SUB_BUS_NUMBER_LATENCY_PRIMARY_BUS_OFFSET              0
#define SUB_BUS_NUMBER_LATENCY_PRIMARY_BUS_MASK                0xff

#define SUB_BUS_NUMBER_LATENCY_SECONDARY_BUS_OFFSET            8
#define SUB_BUS_NUMBER_LATENCY_SECONDARY_BUS_MASK              0xff00

#define SUB_BUS_NUMBER_LATENCY_SUB_BUS_NUM_OFFSET              16
#define SUB_BUS_NUMBER_LATENCY_SUB_BUS_NUM_MASK                0xff0000

#define SUB_BUS_NUMBER_LATENCY_SECONDARY_LATENCY_TIMER_OFFSET  24
#define SUB_BUS_NUMBER_LATENCY_SECONDARY_LATENCY_TIMER_MASK    0xff000000

typedef union {
  struct {
    UINT32                                         PRIMARY_BUS:8;
    UINT32                                       SECONDARY_BUS:8;
    UINT32                                         SUB_BUS_NUM:8;
    UINT32                             SECONDARY_LATENCY_TIMER:8;
  } Field;
  UINT32 Value;
} SUB_BUS_NUMBER_LATENCY_NBIFRCCFG_STRUCT;

#define PCICFG_NBIFRCCFG_SUB_BUS_NUMBER_LATENCY_OFFSET              0x18
#define SMN_DEV0_NBIF0RCNBIO0_SUB_BUS_NUMBER_LATENCY_ADDRESS          0x10100018UL
#define SMN_DEV0_NBIF0RCNBIO1_SUB_BUS_NUMBER_LATENCY_ADDRESS          0x10300018UL
#define SMN_DEV0_NBIF1RCNBIO0_SUB_BUS_NUMBER_LATENCY_ADDRESS          0x10200018UL
#define SMN_DEV0_NBIF1RCNBIO1_SUB_BUS_NUMBER_LATENCY_ADDRESS          0x10400018UL
#define SMN_DEV1_NBIF0RCNBIO0_SUB_BUS_NUMBER_LATENCY_ADDRESS          0x10101018UL
#define SMN_DEV1_NBIF0RCNBIO1_SUB_BUS_NUMBER_LATENCY_ADDRESS          0x10301018UL
#define SMN_DEV1_NBIF1RCNBIO0_SUB_BUS_NUMBER_LATENCY_ADDRESS          0x10201018UL
#define SMN_DEV1_NBIF1RCNBIO1_SUB_BUS_NUMBER_LATENCY_ADDRESS          0x10401018UL


/***********************************************************
* Register Name : SUB_CLASS
************************************************************/

#define SUB_CLASS_SUB_CLASS_OFFSET                             0
#define SUB_CLASS_SUB_CLASS_MASK                               0xff

typedef union {
  struct {
    UINT8                                           SUB_CLASS:8;
  } Field;
  UINT8 Value;
} SUB_CLASS_NBIFRCCFG_STRUCT;

#define PCICFG_NBIFRCCFG_SUB_CLASS_OFFSET                           0xa
#define SMN_DEV0_NBIF0RCNBIO0_SUB_CLASS_ADDRESS                       0x1010000aUL
#define SMN_DEV0_NBIF0RCNBIO1_SUB_CLASS_ADDRESS                       0x1030000aUL
#define SMN_DEV0_NBIF1RCNBIO0_SUB_CLASS_ADDRESS                       0x1020000aUL
#define SMN_DEV0_NBIF1RCNBIO1_SUB_CLASS_ADDRESS                       0x1040000aUL
#define SMN_DEV1_NBIF0RCNBIO0_SUB_CLASS_ADDRESS                       0x1010100aUL
#define SMN_DEV1_NBIF0RCNBIO1_SUB_CLASS_ADDRESS                       0x1030100aUL
#define SMN_DEV1_NBIF1RCNBIO0_SUB_CLASS_ADDRESS                       0x1020100aUL
#define SMN_DEV1_NBIF1RCNBIO1_SUB_CLASS_ADDRESS                       0x1040100aUL


/***********************************************************
* Register Name : TRANSMITTED_MODIFIED_TS_DATA1
************************************************************/

#define TRANSMITTED_MODIFIED_TS_DATA1_TRANSMITTED_MODIFIED_TS_USAGE_MODE_OFFSET 0
#define TRANSMITTED_MODIFIED_TS_DATA1_TRANSMITTED_MODIFIED_TS_USAGE_MODE_MASK 0x7

#define TRANSMITTED_MODIFIED_TS_DATA1_TRANSMITTED_MODIFIED_TS_INFORMATION_1_OFFSET 3
#define TRANSMITTED_MODIFIED_TS_DATA1_TRANSMITTED_MODIFIED_TS_INFORMATION_1_MASK 0xfff8

#define TRANSMITTED_MODIFIED_TS_DATA1_TRANSMITTED_MODIFIED_TS_VENDOR_ID_OFFSET 16
#define TRANSMITTED_MODIFIED_TS_DATA1_TRANSMITTED_MODIFIED_TS_VENDOR_ID_MASK 0xffff0000

typedef union {
  struct {
    UINT32                  TRANSMITTED_MODIFIED_TS_USAGE_MODE:3;
    UINT32               TRANSMITTED_MODIFIED_TS_INFORMATION_1:13;
    UINT32                   TRANSMITTED_MODIFIED_TS_VENDOR_ID:16;
  } Field;
  UINT32 Value;
} TRANSMITTED_MODIFIED_TS_DATA1_NBIFRCCFG_STRUCT;

#define PCICFG_NBIFRCCFG_TRANSMITTED_MODIFIED_TS_DATA1_OFFSET       0x518
#define SMN_DEV0_NBIF0RCNBIO0_TRANSMITTED_MODIFIED_TS_DATA1_ADDRESS   0x10100518UL
#define SMN_DEV0_NBIF0RCNBIO1_TRANSMITTED_MODIFIED_TS_DATA1_ADDRESS   0x10300518UL
#define SMN_DEV0_NBIF1RCNBIO0_TRANSMITTED_MODIFIED_TS_DATA1_ADDRESS   0x10200518UL
#define SMN_DEV0_NBIF1RCNBIO1_TRANSMITTED_MODIFIED_TS_DATA1_ADDRESS   0x10400518UL
#define SMN_DEV1_NBIF0RCNBIO0_TRANSMITTED_MODIFIED_TS_DATA1_ADDRESS   0x10101518UL
#define SMN_DEV1_NBIF0RCNBIO1_TRANSMITTED_MODIFIED_TS_DATA1_ADDRESS   0x10301518UL
#define SMN_DEV1_NBIF1RCNBIO0_TRANSMITTED_MODIFIED_TS_DATA1_ADDRESS   0x10201518UL
#define SMN_DEV1_NBIF1RCNBIO1_TRANSMITTED_MODIFIED_TS_DATA1_ADDRESS   0x10401518UL


/***********************************************************
* Register Name : TRANSMITTED_MODIFIED_TS_DATA2
************************************************************/

#define TRANSMITTED_MODIFIED_TS_DATA2_TRANSMITTED_MODIFIED_TS_INFORMATION_2_OFFSET 0
#define TRANSMITTED_MODIFIED_TS_DATA2_TRANSMITTED_MODIFIED_TS_INFORMATION_2_MASK 0xffffff

#define TRANSMITTED_MODIFIED_TS_DATA2_TRANSMITTED_ALTERNATE_PROTOCOL_NEGOTIATION_STATUS_OFFSET 24
#define TRANSMITTED_MODIFIED_TS_DATA2_TRANSMITTED_ALTERNATE_PROTOCOL_NEGOTIATION_STATUS_MASK 0x3000000

#define TRANSMITTED_MODIFIED_TS_DATA2_Reserved_31_26_OFFSET    26
#define TRANSMITTED_MODIFIED_TS_DATA2_Reserved_31_26_MASK      0xfc000000

typedef union {
  struct {
    UINT32               TRANSMITTED_MODIFIED_TS_INFORMATION_2:24;
    UINT32   TRANSMITTED_ALTERNATE_PROTOCOL_NEGOTIATION_STATUS:2;
    UINT32                                      Reserved_31_26:6;
  } Field;
  UINT32 Value;
} TRANSMITTED_MODIFIED_TS_DATA2_NBIFRCCFG_STRUCT;

#define PCICFG_NBIFRCCFG_TRANSMITTED_MODIFIED_TS_DATA2_OFFSET       0x51c
#define SMN_DEV0_NBIF0RCNBIO0_TRANSMITTED_MODIFIED_TS_DATA2_ADDRESS   0x1010051cUL
#define SMN_DEV0_NBIF0RCNBIO1_TRANSMITTED_MODIFIED_TS_DATA2_ADDRESS   0x1030051cUL
#define SMN_DEV0_NBIF1RCNBIO0_TRANSMITTED_MODIFIED_TS_DATA2_ADDRESS   0x1020051cUL
#define SMN_DEV0_NBIF1RCNBIO1_TRANSMITTED_MODIFIED_TS_DATA2_ADDRESS   0x1040051cUL
#define SMN_DEV1_NBIF0RCNBIO0_TRANSMITTED_MODIFIED_TS_DATA2_ADDRESS   0x1010151cUL
#define SMN_DEV1_NBIF0RCNBIO1_TRANSMITTED_MODIFIED_TS_DATA2_ADDRESS   0x1030151cUL
#define SMN_DEV1_NBIF1RCNBIO0_TRANSMITTED_MODIFIED_TS_DATA2_ADDRESS   0x1020151cUL
#define SMN_DEV1_NBIF1RCNBIO1_TRANSMITTED_MODIFIED_TS_DATA2_ADDRESS   0x1040151cUL


/***********************************************************
* Register Name : VENDOR_ID
************************************************************/

#define VENDOR_ID_VENDOR_ID_OFFSET                             0
#define VENDOR_ID_VENDOR_ID_MASK                               0xffff

typedef union {
  struct {
    UINT16                                           VENDOR_ID:16;
  } Field;
  UINT16 Value;
} VENDOR_ID_NBIFRCCFG_STRUCT;

#define PCICFG_NBIFRCCFG_VENDOR_ID_OFFSET                           0x0
#define SMN_DEV0_NBIF0RCNBIO0_VENDOR_ID_ADDRESS                       0x10100000UL
#define SMN_DEV0_NBIF0RCNBIO1_VENDOR_ID_ADDRESS                       0x10300000UL
#define SMN_DEV0_NBIF1RCNBIO0_VENDOR_ID_ADDRESS                       0x10200000UL
#define SMN_DEV0_NBIF1RCNBIO1_VENDOR_ID_ADDRESS                       0x10400000UL
#define SMN_DEV1_NBIF0RCNBIO0_VENDOR_ID_ADDRESS                       0x10101000UL
#define SMN_DEV1_NBIF0RCNBIO1_VENDOR_ID_ADDRESS                       0x10301000UL
#define SMN_DEV1_NBIF1RCNBIO0_VENDOR_ID_ADDRESS                       0x10201000UL
#define SMN_DEV1_NBIF1RCNBIO1_VENDOR_ID_ADDRESS                       0x10401000UL

#endif /* _NBIFRCCFG_H_ */

