/*****************************************************************************
 * Copyright (C) 2008-2024 Advanced Micro Devices, Inc. All rights reserved.
 *
*****************************************************************************
*/
/* $NoKeywords:$ */
/**
 * @file
 *
 * FCH DXE
 *
 *
 * @xrefitem bom "File Content Label" "Release Content"
 * @e project:      AGESA
 * @e sub-project   FCH DXE
 * @e \$Revision: 309090 $   @e \$Date: 2014-12-09 10:28:05 -0800 (Tue, 09 Dec 2014) $
 *
 */

#ifndef _FCH_INIT_PROTOCOL_H_
#define _FCH_INIT_PROTOCOL_H_

/**
 * @brief FCH Initialization protocol GUID
 */
extern EFI_GUID gFchInitProtocolGuid;

/**
 * @brief Forward declaration for the FCH_INIT_PROTOCOL
 */
typedef struct _FCH_INIT_PROTOCOL FCH_INIT_PROTOCOL;

/**
 * @brief Prototype of FCH USB Over Current Data Block
 */
typedef struct {
  UINT32       ChipType;          ///< Applied chip types
  UINT8        Usb1OcPinMap[5];   ///< USB1 over current pin mapping
  UINT8        Usb2OcPinMap[5];   ///< USB2 over current pin mapping
  UINT8        Usb3OcPinMap[4];   ///< USB3 over current pin mapping
  UINT8        Usb4OcPinMap[2];   ///< USB4 over current pin mapping
  UINT8        Xhci0OcPinMap[2];  ///< XHCI0 over current pin mapping
  UINT8        Xhci1OcPinMap[2];  ///< XHCI1 over current pin mapping
} USB_OC;

/**
 * @brief Protocol prototypes for FCH USB OC mapping
 * @param Pointer to USB OC
 */
typedef
VOID
(EFIAPI *FP_FCH_USB_OC) (
  USB_OC      *UsbOc
  );

/**
 * @brief Rev2 for Protocol prototypes of FCH USB OC2
 * @param This FCH INIT Protocol
 * @param Socket CPU/Socket number in system
 * @param Port Bitmap (Bit0 - Port0, Bit1 - Port1, etc.) to disable USB3 ports
 * @param OCPin Bitmap (Bit0 - Port0, Bit1 - Port1, etc.) to disable USB2 ports
 */
typedef EFI_STATUS (EFIAPI *FP_FCH_USB_OC2) (
  IN       CONST FCH_INIT_PROTOCOL   *This,
  IN       UINT8                     Socket,
  IN       UINT8                     Port,
  IN       UINT8                     OCPin
);

/**
 * @brief Prototype for FCH USB port disable function.
 *
 * @param This Pointer to FCH INIT Protocol
 * @param Socket CPU/Socket number in system
 * @param USB3DisableMap Bitmap (Bit0 - Port0, Bit1 - Port1, etc.) to disable USB3 ports
 * @param Bitmap (Bit0 - Port0, Bit1 - Port1, etc.) to disable USB2 ports
 *
 * @returns EFI_STATUS
 * @retval EFI_SUCCESS   Module initialized successfully
 * @retval EFI_ERROR     Initialization failed (see error for more details)
 */
typedef EFI_STATUS (EFIAPI *FP_FCH_USB_PORT_DISABLE2) (
  IN       CONST FCH_INIT_PROTOCOL   *This,
  IN       UINT8                     Socket,
  IN       UINT32                    USB3DisableMap,
  IN       UINT32                    USB2DisableMap
);

/**
 * @brief FCH INIT Protocol
 */
typedef struct _FCH_INIT_PROTOCOL {
  UINTN                     Revision;                 ///< Protocol Revision
  UINTN                     FchRev;                   ///< FCH Revision
  VOID                      *FchPolicy;               ///< Fch Config Data Block
  VOID                      *FchPtPolicy;             ///< PT Data Block
  FP_FCH_USB_OC             FpUsbOverCurrent;         ///< Obsolete
  FP_FCH_USB_OC2            FpUsbOverCurrent2;        ///< Obsolete
  FP_FCH_USB_PORT_DISABLE2  FpUsbPortDisable2;        ///< Obsolete
} FCH_INIT_PROTOCOL;

/**
 * @brief current FCH Protocol revision
 */
#define FCH_INIT_REV  0x01

#endif // _FCH_INIT_PROTOCOL_H_



