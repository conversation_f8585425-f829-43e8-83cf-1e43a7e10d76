/*****************************************************************************
 * Copyright (C) 2008-2024 Advanced Micro Devices, Inc. All rights reserved.
 *
*****************************************************************************
*/
/* $NoKeywords:$ */
/**
 * @file
 *
 * PSP FIRMWARE VERSION PROTOCOL prototype definition
 *
 *
 * @xrefitem bom "File Content Label" "Release Content"
 * @e project:      AGESA
 * @e sub-project:  PSP
 * @e \$Revision: 309090 $   @e \$Date: 2014-12-10 02:28:05 +0800 (Wed, 10 Dec 2014) $
 */
#ifndef _AMD_PSP_FIRMWARE_VERSION_PROTOCOL_H_
#define _AMD_PSP_FIRMWARE_VERSION_PROTOCOL_H_

#include <AmdPspDirectory.h>

extern EFI_GUID gAmdPspFirmwareVersionProtocolGuid;  ///< Psp Firmware Version Protocol Guid

typedef struct _AMD_PSP_FIRMWARE_VERSION_PROTOCOL AMD_PSP_FIRMWARE_VERSION_PROTOCOL; ///< The Struct Type Definition of Psp Firmware Version Protocol

typedef enum  {
  FirmwareTypePspBootLoader,
  FirmwareTypePspRecoveryBootLoader,
  FirmwareTypeSmu,
  FirmwareTypeAegsaBootLoader,
  FirmwareTypeApcb,
  FirmwareTypeApob,
  FirmwareTypeAppb,
  FirmwareTypeSev,
  FirmwareTypePhy,
  FirmwareTypeMpio,
  FirmwareTypeMmpdma,
  FirmwareTypePm,
  FirmwareTypeGmi,
  FirmwareTypeRib,
  FirmwareTypeSec,
  FirmwareTypePmu,
  FirmwareTypeEmcr,
  FirmwareTypePspBootLoader2,
  FirmwareTypeTee,
  FirmwareTypeTeeDb,
  FirmwareTypeSecSriov,
  FirmwareTypeIpConfig,
  FirmwareTypeUbl,
  FirmwareTypeUmc,
  FirmwareTypeUmcCfg,
  FirmwareTypeMp5Ccd,
  FirmwareTypeMp5Xcd,
  
} AMD_PSP_FIRMWARE_TYPE;

typedef enum  {
  FirmwareLevel1,
  FirmwareLevel2
} AMD_PSP_FIRMWARE_LEVEL;

/**
 * @brief Get firmware version
 *
 * @param[in]     This                       Point to AMD_PSP_FIRMWARE_VERSION_PROTOCOL itself
 * @param[in]     FirmwareType               Firmware type
 * @param[in]     FirmwareLevel              Firmware level
 * @param[in,out] FirmwareVersion            Point to the firmware version
 *
 * @retval       EFI_SUCCESS                Success to get the firmware version
 * @retval       EFI_INVALID_PARAMETER      Input parameter is invaid
 * @retval       EFI_NOT_FOUND              Fail to find firmware
 */
typedef
EFI_STATUS
(EFIAPI *GET_FIRMWARE_VERSION) (
  IN     AMD_PSP_FIRMWARE_VERSION_PROTOCOL  *This,
  IN     AMD_PSP_FIRMWARE_TYPE              FirmwareType,
  IN     AMD_PSP_FIRMWARE_LEVEL             FirmwareLevel,
  IN OUT UINT32                             *FirmwareVersion
  );

typedef struct _AMD_PSP_FIRMWARE_VERSION_PROTOCOL {
  GET_FIRMWARE_VERSION            GetFirmwareVersion;  ///< Method to get firmware version
} AMD_PSP_FIRMWARE_VERSION_PROTOCOL;

#endif //_AMD_PSP_FIRMWARE_VERSION_PROTOCOL_H_



