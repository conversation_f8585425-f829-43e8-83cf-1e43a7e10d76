/*****************************************************************************
 *
 * Copyright (C) 2008-2024 Advanced Micro Devices, Inc. All rights reserved.
 *
 *******************************************************************************
 **/

#ifndef _IOAGR_H_
#define _IOAGR_H_


/***********************************************************
* Register Name : IOAGR_DMA_COMP_EARLY_WAKE_UP_EN
************************************************************/

#define IOAGR_DMA_COMP_EARLY_WAKE_UP_EN_DMA_EnableEarlyCompClkReqIngress_OFFSET 0
#define IOAGR_DMA_COMP_EARLY_WAKE_UP_EN_DMA_EnableEarlyCompClkReqIngress_MASK 0xffff

#define IOAGR_DMA_COMP_EARLY_WAKE_UP_EN_DMA_EnableEarlyCompClkReqEgress_OFFSET 16
#define IOAGR_DMA_COMP_EARLY_WAKE_UP_EN_DMA_EnableEarlyCompClkReqEgress_MASK 0xffff0000

typedef union {
  struct {
    UINT32                    DMA_EnableEarlyCompClkReqIngress:16;
    UINT32                     DMA_EnableEarlyCompClkReqEgress:16;
  } Field;
  UINT32 Value;
} IOAGR_DMA_COMP_EARLY_WAKE_UP_EN_STRUCT;

#define SMN_IOAGR_DMA_COMP_EARLY_WAKE_UP_EN_ADDRESS                   0x15b000bcUL
#define SMN_IOHUB0NBIO0_IOAGR_DMA_COMP_EARLY_WAKE_UP_EN_ADDRESS       0x15b000bcUL
#define SMN_IOHUB0NBIO1_IOAGR_DMA_COMP_EARLY_WAKE_UP_EN_ADDRESS       0x15d000bcUL
#define SMN_IOHUB1NBIO0_IOAGR_DMA_COMP_EARLY_WAKE_UP_EN_ADDRESS       0x1e0000bcUL
#define SMN_IOHUB1NBIO1_IOAGR_DMA_COMP_EARLY_WAKE_UP_EN_ADDRESS       0x1e2000bcUL
#define SMN_IOHUB2NBIO0_IOAGR_DMA_COMP_EARLY_WAKE_UP_EN_ADDRESS       0x15c000bcUL
#define SMN_IOHUB2NBIO1_IOAGR_DMA_COMP_EARLY_WAKE_UP_EN_ADDRESS       0x15e000bcUL
#define SMN_IOHUB3NBIO0_IOAGR_DMA_COMP_EARLY_WAKE_UP_EN_ADDRESS       0x1e1000bcUL
#define SMN_IOHUB3NBIO1_IOAGR_DMA_COMP_EARLY_WAKE_UP_EN_ADDRESS       0x1e3000bcUL


/***********************************************************
* Register Name : IOAGR_DMA_ORIG_EARLY_WAKE_UP_EN
************************************************************/

#define IOAGR_DMA_ORIG_EARLY_WAKE_UP_EN_DMA_EnableEarlyOrigClkReqIngress_OFFSET 0
#define IOAGR_DMA_ORIG_EARLY_WAKE_UP_EN_DMA_EnableEarlyOrigClkReqIngress_MASK 0xffff
#define IOAGR_DMA_ORIG_EARLY_WAKE_UP_EN_DMA_EnableEarlyOrigClkReqIngress_DEFAULT     0xfe

#define IOAGR_DMA_ORIG_EARLY_WAKE_UP_EN_DMA_EnableEarlyOrigClkReqEgress_OFFSET 16
#define IOAGR_DMA_ORIG_EARLY_WAKE_UP_EN_DMA_EnableEarlyOrigClkReqEgress_MASK 0xffff0000
#define IOAGR_DMA_ORIG_EARLY_WAKE_UP_EN_DMA_EnableEarlyOrigClkReqEgress_DEFAULT     0x1

typedef union {
  struct {
    UINT32                    DMA_EnableEarlyOrigClkReqIngress:16;
    UINT32                     DMA_EnableEarlyOrigClkReqEgress:16;
  } Field;
  UINT32 Value;
} IOAGR_DMA_ORIG_EARLY_WAKE_UP_EN_STRUCT;

#define SMN_IOAGR_DMA_ORIG_EARLY_WAKE_UP_EN_ADDRESS                   0x15b000b0UL
#define SMN_IOHUB0NBIO0_IOAGR_DMA_ORIG_EARLY_WAKE_UP_EN_ADDRESS       0x15b000b0UL
#define SMN_IOHUB0NBIO1_IOAGR_DMA_ORIG_EARLY_WAKE_UP_EN_ADDRESS       0x15d000b0UL
#define SMN_IOHUB1NBIO0_IOAGR_DMA_ORIG_EARLY_WAKE_UP_EN_ADDRESS       0x1e0000b0UL
#define SMN_IOHUB1NBIO1_IOAGR_DMA_ORIG_EARLY_WAKE_UP_EN_ADDRESS       0x1e2000b0UL
#define SMN_IOHUB2NBIO0_IOAGR_DMA_ORIG_EARLY_WAKE_UP_EN_ADDRESS       0x15c000b0UL
#define SMN_IOHUB2NBIO1_IOAGR_DMA_ORIG_EARLY_WAKE_UP_EN_ADDRESS       0x15e000b0UL
#define SMN_IOHUB3NBIO0_IOAGR_DMA_ORIG_EARLY_WAKE_UP_EN_ADDRESS       0x1e1000b0UL
#define SMN_IOHUB3NBIO1_IOAGR_DMA_ORIG_EARLY_WAKE_UP_EN_ADDRESS       0x1e3000b0UL


/***********************************************************
* Register Name : IOAGR_GLUE_CG_LCLK_CTRL_0
************************************************************/

#define IOAGR_GLUE_CG_LCLK_CTRL_0_Reserved_3_0_OFFSET          0
#define IOAGR_GLUE_CG_LCLK_CTRL_0_Reserved_3_0_MASK            0xf

#define IOAGR_GLUE_CG_LCLK_CTRL_0_CG_OFF_HYSTERESIS_OFFSET     4
#define IOAGR_GLUE_CG_LCLK_CTRL_0_CG_OFF_HYSTERESIS_MASK       0xff0

#define IOAGR_GLUE_CG_LCLK_CTRL_0_Reserved_21_12_OFFSET        12
#define IOAGR_GLUE_CG_LCLK_CTRL_0_Reserved_21_12_MASK          0x3ff000

#define IOAGR_GLUE_CG_LCLK_CTRL_0_SOFT_OVERRIDE_CLK9_OFFSET    22
#define IOAGR_GLUE_CG_LCLK_CTRL_0_SOFT_OVERRIDE_CLK9_MASK      0x400000
#define IOAGR_GLUE_CG_LCLK_CTRL_0_SOFT_OVERRIDE_CLK9_DEFAULT     0x0

#define IOAGR_GLUE_CG_LCLK_CTRL_0_SOFT_OVERRIDE_CLK8_OFFSET    23
#define IOAGR_GLUE_CG_LCLK_CTRL_0_SOFT_OVERRIDE_CLK8_MASK      0x800000
#define IOAGR_GLUE_CG_LCLK_CTRL_0_SOFT_OVERRIDE_CLK8_DEFAULT     0x0

#define IOAGR_GLUE_CG_LCLK_CTRL_0_SOFT_OVERRIDE_CLK7_OFFSET    24
#define IOAGR_GLUE_CG_LCLK_CTRL_0_SOFT_OVERRIDE_CLK7_MASK      0x1000000
#define IOAGR_GLUE_CG_LCLK_CTRL_0_SOFT_OVERRIDE_CLK7_DEFAULT     0x0

#define IOAGR_GLUE_CG_LCLK_CTRL_0_SOFT_OVERRIDE_CLK6_OFFSET    25
#define IOAGR_GLUE_CG_LCLK_CTRL_0_SOFT_OVERRIDE_CLK6_MASK      0x2000000
#define IOAGR_GLUE_CG_LCLK_CTRL_0_SOFT_OVERRIDE_CLK6_DEFAULT     0x0

#define IOAGR_GLUE_CG_LCLK_CTRL_0_SOFT_OVERRIDE_CLK5_OFFSET    26
#define IOAGR_GLUE_CG_LCLK_CTRL_0_SOFT_OVERRIDE_CLK5_MASK      0x4000000
#define IOAGR_GLUE_CG_LCLK_CTRL_0_SOFT_OVERRIDE_CLK5_DEFAULT     0x0

#define IOAGR_GLUE_CG_LCLK_CTRL_0_SOFT_OVERRIDE_CLK4_OFFSET    27
#define IOAGR_GLUE_CG_LCLK_CTRL_0_SOFT_OVERRIDE_CLK4_MASK      0x8000000
#define IOAGR_GLUE_CG_LCLK_CTRL_0_SOFT_OVERRIDE_CLK4_DEFAULT     0x0

#define IOAGR_GLUE_CG_LCLK_CTRL_0_SOFT_OVERRIDE_CLK3_OFFSET    28
#define IOAGR_GLUE_CG_LCLK_CTRL_0_SOFT_OVERRIDE_CLK3_MASK      0x10000000
#define IOAGR_GLUE_CG_LCLK_CTRL_0_SOFT_OVERRIDE_CLK3_DEFAULT     0x0

#define IOAGR_GLUE_CG_LCLK_CTRL_0_SOFT_OVERRIDE_CLK2_OFFSET    29
#define IOAGR_GLUE_CG_LCLK_CTRL_0_SOFT_OVERRIDE_CLK2_MASK      0x20000000
#define IOAGR_GLUE_CG_LCLK_CTRL_0_SOFT_OVERRIDE_CLK2_DEFAULT     0x0

#define IOAGR_GLUE_CG_LCLK_CTRL_0_SOFT_OVERRIDE_CLK1_OFFSET    30
#define IOAGR_GLUE_CG_LCLK_CTRL_0_SOFT_OVERRIDE_CLK1_MASK      0x40000000
#define IOAGR_GLUE_CG_LCLK_CTRL_0_SOFT_OVERRIDE_CLK1_DEFAULT     0x0

#define IOAGR_GLUE_CG_LCLK_CTRL_0_SOFT_OVERRIDE_CLK0_OFFSET    31
#define IOAGR_GLUE_CG_LCLK_CTRL_0_SOFT_OVERRIDE_CLK0_MASK      0x80000000
#define IOAGR_GLUE_CG_LCLK_CTRL_0_SOFT_OVERRIDE_CLK0_DEFAULT     0x0

typedef union {
  struct {
    UINT32                                        Reserved_3_0:4;
    UINT32                                   CG_OFF_HYSTERESIS:8;
    UINT32                                      Reserved_21_12:10;
    UINT32                                  SOFT_OVERRIDE_CLK9:1;
    UINT32                                  SOFT_OVERRIDE_CLK8:1;
    UINT32                                  SOFT_OVERRIDE_CLK7:1;
    UINT32                                  SOFT_OVERRIDE_CLK6:1;
    UINT32                                  SOFT_OVERRIDE_CLK5:1;
    UINT32                                  SOFT_OVERRIDE_CLK4:1;
    UINT32                                  SOFT_OVERRIDE_CLK3:1;
    UINT32                                  SOFT_OVERRIDE_CLK2:1;
    UINT32                                  SOFT_OVERRIDE_CLK1:1;
    UINT32                                  SOFT_OVERRIDE_CLK0:1;
  } Field;
  UINT32 Value;
} IOAGR_GLUE_CG_LCLK_CTRL_0_STRUCT;

#define SMN_IOAGR_GLUE_CG_LCLK_CTRL_0_ADDRESS                         0x15b00000UL
#define SMN_IOHUB0NBIO0_IOAGR_GLUE_CG_LCLK_CTRL_0_ADDRESS             0x15b00000UL
#define SMN_IOHUB0NBIO1_IOAGR_GLUE_CG_LCLK_CTRL_0_ADDRESS             0x15d00000UL
#define SMN_IOHUB1NBIO0_IOAGR_GLUE_CG_LCLK_CTRL_0_ADDRESS             0x1e000000UL
#define SMN_IOHUB1NBIO1_IOAGR_GLUE_CG_LCLK_CTRL_0_ADDRESS             0x1e200000UL
#define SMN_IOHUB2NBIO0_IOAGR_GLUE_CG_LCLK_CTRL_0_ADDRESS             0x15c00000UL
#define SMN_IOHUB2NBIO1_IOAGR_GLUE_CG_LCLK_CTRL_0_ADDRESS             0x15e00000UL
#define SMN_IOHUB3NBIO0_IOAGR_GLUE_CG_LCLK_CTRL_0_ADDRESS             0x1e100000UL
#define SMN_IOHUB3NBIO1_IOAGR_GLUE_CG_LCLK_CTRL_0_ADDRESS             0x1e300000UL


/***********************************************************
* Register Name : IOAGR_GLUE_CG_LCLK_CTRL_1
************************************************************/

#define IOAGR_GLUE_CG_LCLK_CTRL_1_Reserved_21_0_OFFSET         0
#define IOAGR_GLUE_CG_LCLK_CTRL_1_Reserved_21_0_MASK           0x3fffff

#define IOAGR_GLUE_CG_LCLK_CTRL_1_SOFT_OVERRIDE_CLK9_OFFSET    22
#define IOAGR_GLUE_CG_LCLK_CTRL_1_SOFT_OVERRIDE_CLK9_MASK      0x400000
#define IOAGR_GLUE_CG_LCLK_CTRL_1_SOFT_OVERRIDE_CLK9_DEFAULT     0x0

#define IOAGR_GLUE_CG_LCLK_CTRL_1_SOFT_OVERRIDE_CLK8_OFFSET    23
#define IOAGR_GLUE_CG_LCLK_CTRL_1_SOFT_OVERRIDE_CLK8_MASK      0x800000
#define IOAGR_GLUE_CG_LCLK_CTRL_1_SOFT_OVERRIDE_CLK8_DEFAULT     0x0

#define IOAGR_GLUE_CG_LCLK_CTRL_1_SOFT_OVERRIDE_CLK7_OFFSET    24
#define IOAGR_GLUE_CG_LCLK_CTRL_1_SOFT_OVERRIDE_CLK7_MASK      0x1000000
#define IOAGR_GLUE_CG_LCLK_CTRL_1_SOFT_OVERRIDE_CLK7_DEFAULT     0x0

#define IOAGR_GLUE_CG_LCLK_CTRL_1_SOFT_OVERRIDE_CLK6_OFFSET    25
#define IOAGR_GLUE_CG_LCLK_CTRL_1_SOFT_OVERRIDE_CLK6_MASK      0x2000000
#define IOAGR_GLUE_CG_LCLK_CTRL_1_SOFT_OVERRIDE_CLK6_DEFAULT     0x0

#define IOAGR_GLUE_CG_LCLK_CTRL_1_SOFT_OVERRIDE_CLK5_OFFSET    26
#define IOAGR_GLUE_CG_LCLK_CTRL_1_SOFT_OVERRIDE_CLK5_MASK      0x4000000
#define IOAGR_GLUE_CG_LCLK_CTRL_1_SOFT_OVERRIDE_CLK5_DEFAULT     0x0

#define IOAGR_GLUE_CG_LCLK_CTRL_1_SOFT_OVERRIDE_CLK4_OFFSET    27
#define IOAGR_GLUE_CG_LCLK_CTRL_1_SOFT_OVERRIDE_CLK4_MASK      0x8000000
#define IOAGR_GLUE_CG_LCLK_CTRL_1_SOFT_OVERRIDE_CLK4_DEFAULT     0x0

#define IOAGR_GLUE_CG_LCLK_CTRL_1_SOFT_OVERRIDE_CLK3_OFFSET    28
#define IOAGR_GLUE_CG_LCLK_CTRL_1_SOFT_OVERRIDE_CLK3_MASK      0x10000000
#define IOAGR_GLUE_CG_LCLK_CTRL_1_SOFT_OVERRIDE_CLK3_DEFAULT     0x0

#define IOAGR_GLUE_CG_LCLK_CTRL_1_SOFT_OVERRIDE_CLK2_OFFSET    29
#define IOAGR_GLUE_CG_LCLK_CTRL_1_SOFT_OVERRIDE_CLK2_MASK      0x20000000
#define IOAGR_GLUE_CG_LCLK_CTRL_1_SOFT_OVERRIDE_CLK2_DEFAULT     0x0

#define IOAGR_GLUE_CG_LCLK_CTRL_1_SOFT_OVERRIDE_CLK1_OFFSET    30
#define IOAGR_GLUE_CG_LCLK_CTRL_1_SOFT_OVERRIDE_CLK1_MASK      0x40000000
#define IOAGR_GLUE_CG_LCLK_CTRL_1_SOFT_OVERRIDE_CLK1_DEFAULT     0x0

#define IOAGR_GLUE_CG_LCLK_CTRL_1_SOFT_OVERRIDE_CLK0_OFFSET    31
#define IOAGR_GLUE_CG_LCLK_CTRL_1_SOFT_OVERRIDE_CLK0_MASK      0x80000000
#define IOAGR_GLUE_CG_LCLK_CTRL_1_SOFT_OVERRIDE_CLK0_DEFAULT     0x0

typedef union {
  struct {
    UINT32                                       Reserved_21_0:22;
    UINT32                                  SOFT_OVERRIDE_CLK9:1;
    UINT32                                  SOFT_OVERRIDE_CLK8:1;
    UINT32                                  SOFT_OVERRIDE_CLK7:1;
    UINT32                                  SOFT_OVERRIDE_CLK6:1;
    UINT32                                  SOFT_OVERRIDE_CLK5:1;
    UINT32                                  SOFT_OVERRIDE_CLK4:1;
    UINT32                                  SOFT_OVERRIDE_CLK3:1;
    UINT32                                  SOFT_OVERRIDE_CLK2:1;
    UINT32                                  SOFT_OVERRIDE_CLK1:1;
    UINT32                                  SOFT_OVERRIDE_CLK0:1;
  } Field;
  UINT32 Value;
} IOAGR_GLUE_CG_LCLK_CTRL_1_STRUCT;

#define SMN_IOAGR_GLUE_CG_LCLK_CTRL_1_ADDRESS                         0x15b00004UL
#define SMN_IOHUB0NBIO0_IOAGR_GLUE_CG_LCLK_CTRL_1_ADDRESS             0x15b00004UL
#define SMN_IOHUB0NBIO1_IOAGR_GLUE_CG_LCLK_CTRL_1_ADDRESS             0x15d00004UL
#define SMN_IOHUB1NBIO0_IOAGR_GLUE_CG_LCLK_CTRL_1_ADDRESS             0x1e000004UL
#define SMN_IOHUB1NBIO1_IOAGR_GLUE_CG_LCLK_CTRL_1_ADDRESS             0x1e200004UL
#define SMN_IOHUB2NBIO0_IOAGR_GLUE_CG_LCLK_CTRL_1_ADDRESS             0x15c00004UL
#define SMN_IOHUB2NBIO1_IOAGR_GLUE_CG_LCLK_CTRL_1_ADDRESS             0x15e00004UL
#define SMN_IOHUB3NBIO0_IOAGR_GLUE_CG_LCLK_CTRL_1_ADDRESS             0x1e100004UL
#define SMN_IOHUB3NBIO1_IOAGR_GLUE_CG_LCLK_CTRL_1_ADDRESS             0x1e300004UL


/***********************************************************
* Register Name : IOAGR_HST_COMP_EARLY_WAKE_UP_EN
************************************************************/

#define IOAGR_HST_COMP_EARLY_WAKE_UP_EN_HST_EnableEarlyCompClkReqIngress_OFFSET 0
#define IOAGR_HST_COMP_EARLY_WAKE_UP_EN_HST_EnableEarlyCompClkReqIngress_MASK 0xffff

#define IOAGR_HST_COMP_EARLY_WAKE_UP_EN_HST_EnableEarlyCompClkReqEgress_OFFSET 16
#define IOAGR_HST_COMP_EARLY_WAKE_UP_EN_HST_EnableEarlyCompClkReqEgress_MASK 0xffff0000

typedef union {
  struct {
    UINT32                    HST_EnableEarlyCompClkReqIngress:16;
    UINT32                     HST_EnableEarlyCompClkReqEgress:16;
  } Field;
  UINT32 Value;
} IOAGR_HST_COMP_EARLY_WAKE_UP_EN_STRUCT;

#define SMN_IOAGR_HST_COMP_EARLY_WAKE_UP_EN_ADDRESS                   0x15b000b8UL
#define SMN_IOHUB0NBIO0_IOAGR_HST_COMP_EARLY_WAKE_UP_EN_ADDRESS       0x15b000b8UL
#define SMN_IOHUB0NBIO1_IOAGR_HST_COMP_EARLY_WAKE_UP_EN_ADDRESS       0x15d000b8UL
#define SMN_IOHUB1NBIO0_IOAGR_HST_COMP_EARLY_WAKE_UP_EN_ADDRESS       0x1e0000b8UL
#define SMN_IOHUB1NBIO1_IOAGR_HST_COMP_EARLY_WAKE_UP_EN_ADDRESS       0x1e2000b8UL
#define SMN_IOHUB2NBIO0_IOAGR_HST_COMP_EARLY_WAKE_UP_EN_ADDRESS       0x15c000b8UL
#define SMN_IOHUB2NBIO1_IOAGR_HST_COMP_EARLY_WAKE_UP_EN_ADDRESS       0x15e000b8UL
#define SMN_IOHUB3NBIO0_IOAGR_HST_COMP_EARLY_WAKE_UP_EN_ADDRESS       0x1e1000b8UL
#define SMN_IOHUB3NBIO1_IOAGR_HST_COMP_EARLY_WAKE_UP_EN_ADDRESS       0x1e3000b8UL


/***********************************************************
* Register Name : IOAGR_HST_ORIG_EARLY_WAKE_UP_EN
************************************************************/

#define IOAGR_HST_ORIG_EARLY_WAKE_UP_EN_HST_EnableEarlyOrigClkReqIngress_OFFSET 0
#define IOAGR_HST_ORIG_EARLY_WAKE_UP_EN_HST_EnableEarlyOrigClkReqIngress_MASK 0xffff

#define IOAGR_HST_ORIG_EARLY_WAKE_UP_EN_HST_EnableEarlyOrigClkReqEgress_OFFSET 16
#define IOAGR_HST_ORIG_EARLY_WAKE_UP_EN_HST_EnableEarlyOrigClkReqEgress_MASK 0xffff0000

typedef union {
  struct {
    UINT32                    HST_EnableEarlyOrigClkReqIngress:16;
    UINT32                     HST_EnableEarlyOrigClkReqEgress:16;
  } Field;
  UINT32 Value;
} IOAGR_HST_ORIG_EARLY_WAKE_UP_EN_STRUCT;

#define SMN_IOAGR_HST_ORIG_EARLY_WAKE_UP_EN_ADDRESS                   0x15b000acUL
#define SMN_IOHUB0NBIO0_IOAGR_HST_ORIG_EARLY_WAKE_UP_EN_ADDRESS       0x15b000acUL
#define SMN_IOHUB0NBIO1_IOAGR_HST_ORIG_EARLY_WAKE_UP_EN_ADDRESS       0x15d000acUL
#define SMN_IOHUB1NBIO0_IOAGR_HST_ORIG_EARLY_WAKE_UP_EN_ADDRESS       0x1e0000acUL
#define SMN_IOHUB1NBIO1_IOAGR_HST_ORIG_EARLY_WAKE_UP_EN_ADDRESS       0x1e2000acUL
#define SMN_IOHUB2NBIO0_IOAGR_HST_ORIG_EARLY_WAKE_UP_EN_ADDRESS       0x15c000acUL
#define SMN_IOHUB2NBIO1_IOAGR_HST_ORIG_EARLY_WAKE_UP_EN_ADDRESS       0x15e000acUL
#define SMN_IOHUB3NBIO0_IOAGR_HST_ORIG_EARLY_WAKE_UP_EN_ADDRESS       0x1e1000acUL
#define SMN_IOHUB3NBIO1_IOAGR_HST_ORIG_EARLY_WAKE_UP_EN_ADDRESS       0x1e3000acUL


/***********************************************************
* Register Name : IOAGR_NCM_COMP_EARLY_WAKE_UP_EN
************************************************************/

#define IOAGR_NCM_COMP_EARLY_WAKE_UP_EN_NCM_EnableEarlyCompClkReqIngress_OFFSET 0
#define IOAGR_NCM_COMP_EARLY_WAKE_UP_EN_NCM_EnableEarlyCompClkReqIngress_MASK 0xffff

#define IOAGR_NCM_COMP_EARLY_WAKE_UP_EN_NCM_EnableEarlyCompClkReqEgress_OFFSET 16
#define IOAGR_NCM_COMP_EARLY_WAKE_UP_EN_NCM_EnableEarlyCompClkReqEgress_MASK 0xffff0000

typedef union {
  struct {
    UINT32                    NCM_EnableEarlyCompClkReqIngress:16;
    UINT32                     NCM_EnableEarlyCompClkReqEgress:16;
  } Field;
  UINT32 Value;
} IOAGR_NCM_COMP_EARLY_WAKE_UP_EN_STRUCT;

#define SMN_IOAGR_NCM_COMP_EARLY_WAKE_UP_EN_ADDRESS                   0x15b000c0UL
#define SMN_IOHUB0NBIO0_IOAGR_NCM_COMP_EARLY_WAKE_UP_EN_ADDRESS       0x15b000c0UL
#define SMN_IOHUB0NBIO1_IOAGR_NCM_COMP_EARLY_WAKE_UP_EN_ADDRESS       0x15d000c0UL
#define SMN_IOHUB1NBIO0_IOAGR_NCM_COMP_EARLY_WAKE_UP_EN_ADDRESS       0x1e0000c0UL
#define SMN_IOHUB1NBIO1_IOAGR_NCM_COMP_EARLY_WAKE_UP_EN_ADDRESS       0x1e2000c0UL
#define SMN_IOHUB2NBIO0_IOAGR_NCM_COMP_EARLY_WAKE_UP_EN_ADDRESS       0x15c000c0UL
#define SMN_IOHUB2NBIO1_IOAGR_NCM_COMP_EARLY_WAKE_UP_EN_ADDRESS       0x15e000c0UL
#define SMN_IOHUB3NBIO0_IOAGR_NCM_COMP_EARLY_WAKE_UP_EN_ADDRESS       0x1e1000c0UL
#define SMN_IOHUB3NBIO1_IOAGR_NCM_COMP_EARLY_WAKE_UP_EN_ADDRESS       0x1e3000c0UL


/***********************************************************
* Register Name : IOAGR_NCM_ORIG_EARLY_WAKE_UP_EN
************************************************************/

#define IOAGR_NCM_ORIG_EARLY_WAKE_UP_EN_NCM_EnableEarlyOrigClkReqIngress_OFFSET 0
#define IOAGR_NCM_ORIG_EARLY_WAKE_UP_EN_NCM_EnableEarlyOrigClkReqIngress_MASK 0xffff

#define IOAGR_NCM_ORIG_EARLY_WAKE_UP_EN_NCM_EnableEarlyOrigClkReqEgress_OFFSET 16
#define IOAGR_NCM_ORIG_EARLY_WAKE_UP_EN_NCM_EnableEarlyOrigClkReqEgress_MASK 0xffff0000

typedef union {
  struct {
    UINT32                    NCM_EnableEarlyOrigClkReqIngress:16;
    UINT32                     NCM_EnableEarlyOrigClkReqEgress:16;
  } Field;
  UINT32 Value;
} IOAGR_NCM_ORIG_EARLY_WAKE_UP_EN_STRUCT;

#define SMN_IOAGR_NCM_ORIG_EARLY_WAKE_UP_EN_ADDRESS                   0x15b000b4UL
#define SMN_IOHUB0NBIO0_IOAGR_NCM_ORIG_EARLY_WAKE_UP_EN_ADDRESS       0x15b000b4UL
#define SMN_IOHUB0NBIO1_IOAGR_NCM_ORIG_EARLY_WAKE_UP_EN_ADDRESS       0x15d000b4UL
#define SMN_IOHUB1NBIO0_IOAGR_NCM_ORIG_EARLY_WAKE_UP_EN_ADDRESS       0x1e0000b4UL
#define SMN_IOHUB1NBIO1_IOAGR_NCM_ORIG_EARLY_WAKE_UP_EN_ADDRESS       0x1e2000b4UL
#define SMN_IOHUB2NBIO0_IOAGR_NCM_ORIG_EARLY_WAKE_UP_EN_ADDRESS       0x15c000b4UL
#define SMN_IOHUB2NBIO1_IOAGR_NCM_ORIG_EARLY_WAKE_UP_EN_ADDRESS       0x15e000b4UL
#define SMN_IOHUB3NBIO0_IOAGR_NCM_ORIG_EARLY_WAKE_UP_EN_ADDRESS       0x1e1000b4UL
#define SMN_IOHUB3NBIO1_IOAGR_NCM_ORIG_EARLY_WAKE_UP_EN_ADDRESS       0x1e3000b4UL


/***********************************************************
* Register Name : IOAGR_ORIG_TO_COMP_EN
************************************************************/

#define IOAGR_ORIG_TO_COMP_EN_EnableOrigtoCompClkReq_OFFSET    0
#define IOAGR_ORIG_TO_COMP_EN_EnableOrigtoCompClkReq_MASK      0xffffffff

typedef union {
  struct {
    UINT32                              EnableOrigtoCompClkReq:32;
  } Field;
  UINT32 Value;
} IOAGR_ORIG_TO_COMP_EN_STRUCT;

#define SMN_IOAGR_ORIG_TO_COMP_EN_ADDRESS                             0x15b000c4UL
#define SMN_IOHUB0NBIO0_IOAGR_ORIG_TO_COMP_EN_ADDRESS                 0x15b000c4UL
#define SMN_IOHUB0NBIO1_IOAGR_ORIG_TO_COMP_EN_ADDRESS                 0x15d000c4UL
#define SMN_IOHUB1NBIO0_IOAGR_ORIG_TO_COMP_EN_ADDRESS                 0x1e0000c4UL
#define SMN_IOHUB1NBIO1_IOAGR_ORIG_TO_COMP_EN_ADDRESS                 0x1e2000c4UL
#define SMN_IOHUB2NBIO0_IOAGR_ORIG_TO_COMP_EN_ADDRESS                 0x15c000c4UL
#define SMN_IOHUB2NBIO1_IOAGR_ORIG_TO_COMP_EN_ADDRESS                 0x15e000c4UL
#define SMN_IOHUB3NBIO0_IOAGR_ORIG_TO_COMP_EN_ADDRESS                 0x1e1000c4UL
#define SMN_IOHUB3NBIO1_IOAGR_ORIG_TO_COMP_EN_ADDRESS                 0x1e3000c4UL


/***********************************************************
* Register Name : IOAGR_PERF_CNTL
************************************************************/

#define IOAGR_PERF_CNTL_EVENT0_SEL_OFFSET                      0
#define IOAGR_PERF_CNTL_EVENT0_SEL_MASK                        0xff

#define IOAGR_PERF_CNTL_EVENT1_SEL_OFFSET                      8
#define IOAGR_PERF_CNTL_EVENT1_SEL_MASK                        0xff00

#define IOAGR_PERF_CNTL_EVENT2_SEL_OFFSET                      16
#define IOAGR_PERF_CNTL_EVENT2_SEL_MASK                        0xff0000

#define IOAGR_PERF_CNTL_EVENT3_SEL_OFFSET                      24
#define IOAGR_PERF_CNTL_EVENT3_SEL_MASK                        0xff000000

typedef union {
  struct {
    UINT32                                          EVENT0_SEL:8;
    UINT32                                          EVENT1_SEL:8;
    UINT32                                          EVENT2_SEL:8;
    UINT32                                          EVENT3_SEL:8;
  } Field;
  UINT32 Value;
} IOAGR_PERF_CNTL_STRUCT;

#define SMN_IOAGR_PERF_CNTL_ADDRESS                                   0x15b00044UL
#define SMN_IOHUB0NBIO0_IOAGR_PERF_CNTL_ADDRESS                       0x15b00044UL
#define SMN_IOHUB0NBIO1_IOAGR_PERF_CNTL_ADDRESS                       0x15d00044UL
#define SMN_IOHUB1NBIO0_IOAGR_PERF_CNTL_ADDRESS                       0x1e000044UL
#define SMN_IOHUB1NBIO1_IOAGR_PERF_CNTL_ADDRESS                       0x1e200044UL
#define SMN_IOHUB2NBIO0_IOAGR_PERF_CNTL_ADDRESS                       0x15c00044UL
#define SMN_IOHUB2NBIO1_IOAGR_PERF_CNTL_ADDRESS                       0x15e00044UL
#define SMN_IOHUB3NBIO0_IOAGR_PERF_CNTL_ADDRESS                       0x1e100044UL
#define SMN_IOHUB3NBIO1_IOAGR_PERF_CNTL_ADDRESS                       0x1e300044UL


/***********************************************************
* Register Name : IOAGR_PERF_COUNT0
************************************************************/

#define IOAGR_PERF_COUNT0_COUNTER0_OFFSET                      0
#define IOAGR_PERF_COUNT0_COUNTER0_MASK                        0xffffffff

typedef union {
  struct {
    UINT32                                            COUNTER0:32;
  } Field;
  UINT32 Value;
} IOAGR_PERF_COUNT0_STRUCT;

#define SMN_IOAGR_PERF_COUNT0_ADDRESS                                 0x15b00048UL
#define SMN_IOHUB0NBIO0_IOAGR_PERF_COUNT0_ADDRESS                     0x15b00048UL
#define SMN_IOHUB0NBIO1_IOAGR_PERF_COUNT0_ADDRESS                     0x15d00048UL
#define SMN_IOHUB1NBIO0_IOAGR_PERF_COUNT0_ADDRESS                     0x1e000048UL
#define SMN_IOHUB1NBIO1_IOAGR_PERF_COUNT0_ADDRESS                     0x1e200048UL
#define SMN_IOHUB2NBIO0_IOAGR_PERF_COUNT0_ADDRESS                     0x15c00048UL
#define SMN_IOHUB2NBIO1_IOAGR_PERF_COUNT0_ADDRESS                     0x15e00048UL
#define SMN_IOHUB3NBIO0_IOAGR_PERF_COUNT0_ADDRESS                     0x1e100048UL
#define SMN_IOHUB3NBIO1_IOAGR_PERF_COUNT0_ADDRESS                     0x1e300048UL


/***********************************************************
* Register Name : IOAGR_PERF_COUNT0_UPPER
************************************************************/

#define IOAGR_PERF_COUNT0_UPPER_COUNTER0_UPPER_OFFSET          0
#define IOAGR_PERF_COUNT0_UPPER_COUNTER0_UPPER_MASK            0xffffff

#define IOAGR_PERF_COUNT0_UPPER_Reserved_31_24_OFFSET          24
#define IOAGR_PERF_COUNT0_UPPER_Reserved_31_24_MASK            0xff000000

typedef union {
  struct {
    UINT32                                      COUNTER0_UPPER:24;
    UINT32                                      Reserved_31_24:8;
  } Field;
  UINT32 Value;
} IOAGR_PERF_COUNT0_UPPER_STRUCT;

#define SMN_IOAGR_PERF_COUNT0_UPPER_ADDRESS                           0x15b0004cUL
#define SMN_IOHUB0NBIO0_IOAGR_PERF_COUNT0_UPPER_ADDRESS               0x15b0004cUL
#define SMN_IOHUB0NBIO1_IOAGR_PERF_COUNT0_UPPER_ADDRESS               0x15d0004cUL
#define SMN_IOHUB1NBIO0_IOAGR_PERF_COUNT0_UPPER_ADDRESS               0x1e00004cUL
#define SMN_IOHUB1NBIO1_IOAGR_PERF_COUNT0_UPPER_ADDRESS               0x1e20004cUL
#define SMN_IOHUB2NBIO0_IOAGR_PERF_COUNT0_UPPER_ADDRESS               0x15c0004cUL
#define SMN_IOHUB2NBIO1_IOAGR_PERF_COUNT0_UPPER_ADDRESS               0x15e0004cUL
#define SMN_IOHUB3NBIO0_IOAGR_PERF_COUNT0_UPPER_ADDRESS               0x1e10004cUL
#define SMN_IOHUB3NBIO1_IOAGR_PERF_COUNT0_UPPER_ADDRESS               0x1e30004cUL


/***********************************************************
* Register Name : IOAGR_PERF_COUNT1
************************************************************/

#define IOAGR_PERF_COUNT1_COUNTER1_OFFSET                      0
#define IOAGR_PERF_COUNT1_COUNTER1_MASK                        0xffffffff

typedef union {
  struct {
    UINT32                                            COUNTER1:32;
  } Field;
  UINT32 Value;
} IOAGR_PERF_COUNT1_STRUCT;

#define SMN_IOAGR_PERF_COUNT1_ADDRESS                                 0x15b00050UL
#define SMN_IOHUB0NBIO0_IOAGR_PERF_COUNT1_ADDRESS                     0x15b00050UL
#define SMN_IOHUB0NBIO1_IOAGR_PERF_COUNT1_ADDRESS                     0x15d00050UL
#define SMN_IOHUB1NBIO0_IOAGR_PERF_COUNT1_ADDRESS                     0x1e000050UL
#define SMN_IOHUB1NBIO1_IOAGR_PERF_COUNT1_ADDRESS                     0x1e200050UL
#define SMN_IOHUB2NBIO0_IOAGR_PERF_COUNT1_ADDRESS                     0x15c00050UL
#define SMN_IOHUB2NBIO1_IOAGR_PERF_COUNT1_ADDRESS                     0x15e00050UL
#define SMN_IOHUB3NBIO0_IOAGR_PERF_COUNT1_ADDRESS                     0x1e100050UL
#define SMN_IOHUB3NBIO1_IOAGR_PERF_COUNT1_ADDRESS                     0x1e300050UL


/***********************************************************
* Register Name : IOAGR_PERF_COUNT1_UPPER
************************************************************/

#define IOAGR_PERF_COUNT1_UPPER_COUNTER1_UPPER_OFFSET          0
#define IOAGR_PERF_COUNT1_UPPER_COUNTER1_UPPER_MASK            0xffffff

#define IOAGR_PERF_COUNT1_UPPER_Reserved_31_24_OFFSET          24
#define IOAGR_PERF_COUNT1_UPPER_Reserved_31_24_MASK            0xff000000

typedef union {
  struct {
    UINT32                                      COUNTER1_UPPER:24;
    UINT32                                      Reserved_31_24:8;
  } Field;
  UINT32 Value;
} IOAGR_PERF_COUNT1_UPPER_STRUCT;

#define SMN_IOAGR_PERF_COUNT1_UPPER_ADDRESS                           0x15b00054UL
#define SMN_IOHUB0NBIO0_IOAGR_PERF_COUNT1_UPPER_ADDRESS               0x15b00054UL
#define SMN_IOHUB0NBIO1_IOAGR_PERF_COUNT1_UPPER_ADDRESS               0x15d00054UL
#define SMN_IOHUB1NBIO0_IOAGR_PERF_COUNT1_UPPER_ADDRESS               0x1e000054UL
#define SMN_IOHUB1NBIO1_IOAGR_PERF_COUNT1_UPPER_ADDRESS               0x1e200054UL
#define SMN_IOHUB2NBIO0_IOAGR_PERF_COUNT1_UPPER_ADDRESS               0x15c00054UL
#define SMN_IOHUB2NBIO1_IOAGR_PERF_COUNT1_UPPER_ADDRESS               0x15e00054UL
#define SMN_IOHUB3NBIO0_IOAGR_PERF_COUNT1_UPPER_ADDRESS               0x1e100054UL
#define SMN_IOHUB3NBIO1_IOAGR_PERF_COUNT1_UPPER_ADDRESS               0x1e300054UL


/***********************************************************
* Register Name : IOAGR_PERF_COUNT2
************************************************************/

#define IOAGR_PERF_COUNT2_COUNTER2_OFFSET                      0
#define IOAGR_PERF_COUNT2_COUNTER2_MASK                        0xffffffff

typedef union {
  struct {
    UINT32                                            COUNTER2:32;
  } Field;
  UINT32 Value;
} IOAGR_PERF_COUNT2_STRUCT;

#define SMN_IOAGR_PERF_COUNT2_ADDRESS                                 0x15b00058UL
#define SMN_IOHUB0NBIO0_IOAGR_PERF_COUNT2_ADDRESS                     0x15b00058UL
#define SMN_IOHUB0NBIO1_IOAGR_PERF_COUNT2_ADDRESS                     0x15d00058UL
#define SMN_IOHUB1NBIO0_IOAGR_PERF_COUNT2_ADDRESS                     0x1e000058UL
#define SMN_IOHUB1NBIO1_IOAGR_PERF_COUNT2_ADDRESS                     0x1e200058UL
#define SMN_IOHUB2NBIO0_IOAGR_PERF_COUNT2_ADDRESS                     0x15c00058UL
#define SMN_IOHUB2NBIO1_IOAGR_PERF_COUNT2_ADDRESS                     0x15e00058UL
#define SMN_IOHUB3NBIO0_IOAGR_PERF_COUNT2_ADDRESS                     0x1e100058UL
#define SMN_IOHUB3NBIO1_IOAGR_PERF_COUNT2_ADDRESS                     0x1e300058UL


/***********************************************************
* Register Name : IOAGR_PERF_COUNT2_UPPER
************************************************************/

#define IOAGR_PERF_COUNT2_UPPER_COUNTER2_UPPER_OFFSET          0
#define IOAGR_PERF_COUNT2_UPPER_COUNTER2_UPPER_MASK            0xffffff

#define IOAGR_PERF_COUNT2_UPPER_Reserved_31_24_OFFSET          24
#define IOAGR_PERF_COUNT2_UPPER_Reserved_31_24_MASK            0xff000000

typedef union {
  struct {
    UINT32                                      COUNTER2_UPPER:24;
    UINT32                                      Reserved_31_24:8;
  } Field;
  UINT32 Value;
} IOAGR_PERF_COUNT2_UPPER_STRUCT;

#define SMN_IOAGR_PERF_COUNT2_UPPER_ADDRESS                           0x15b0005cUL
#define SMN_IOHUB0NBIO0_IOAGR_PERF_COUNT2_UPPER_ADDRESS               0x15b0005cUL
#define SMN_IOHUB0NBIO1_IOAGR_PERF_COUNT2_UPPER_ADDRESS               0x15d0005cUL
#define SMN_IOHUB1NBIO0_IOAGR_PERF_COUNT2_UPPER_ADDRESS               0x1e00005cUL
#define SMN_IOHUB1NBIO1_IOAGR_PERF_COUNT2_UPPER_ADDRESS               0x1e20005cUL
#define SMN_IOHUB2NBIO0_IOAGR_PERF_COUNT2_UPPER_ADDRESS               0x15c0005cUL
#define SMN_IOHUB2NBIO1_IOAGR_PERF_COUNT2_UPPER_ADDRESS               0x15e0005cUL
#define SMN_IOHUB3NBIO0_IOAGR_PERF_COUNT2_UPPER_ADDRESS               0x1e10005cUL
#define SMN_IOHUB3NBIO1_IOAGR_PERF_COUNT2_UPPER_ADDRESS               0x1e30005cUL


/***********************************************************
* Register Name : IOAGR_PERF_COUNT3
************************************************************/

#define IOAGR_PERF_COUNT3_COUNTER3_OFFSET                      0
#define IOAGR_PERF_COUNT3_COUNTER3_MASK                        0xffffffff

typedef union {
  struct {
    UINT32                                            COUNTER3:32;
  } Field;
  UINT32 Value;
} IOAGR_PERF_COUNT3_STRUCT;

#define SMN_IOAGR_PERF_COUNT3_ADDRESS                                 0x15b00060UL
#define SMN_IOHUB0NBIO0_IOAGR_PERF_COUNT3_ADDRESS                     0x15b00060UL
#define SMN_IOHUB0NBIO1_IOAGR_PERF_COUNT3_ADDRESS                     0x15d00060UL
#define SMN_IOHUB1NBIO0_IOAGR_PERF_COUNT3_ADDRESS                     0x1e000060UL
#define SMN_IOHUB1NBIO1_IOAGR_PERF_COUNT3_ADDRESS                     0x1e200060UL
#define SMN_IOHUB2NBIO0_IOAGR_PERF_COUNT3_ADDRESS                     0x15c00060UL
#define SMN_IOHUB2NBIO1_IOAGR_PERF_COUNT3_ADDRESS                     0x15e00060UL
#define SMN_IOHUB3NBIO0_IOAGR_PERF_COUNT3_ADDRESS                     0x1e100060UL
#define SMN_IOHUB3NBIO1_IOAGR_PERF_COUNT3_ADDRESS                     0x1e300060UL


/***********************************************************
* Register Name : IOAGR_PERF_COUNT3_UPPER
************************************************************/

#define IOAGR_PERF_COUNT3_UPPER_COUNTER3_UPPER_OFFSET          0
#define IOAGR_PERF_COUNT3_UPPER_COUNTER3_UPPER_MASK            0xffffff

#define IOAGR_PERF_COUNT3_UPPER_Reserved_31_24_OFFSET          24
#define IOAGR_PERF_COUNT3_UPPER_Reserved_31_24_MASK            0xff000000

typedef union {
  struct {
    UINT32                                      COUNTER3_UPPER:24;
    UINT32                                      Reserved_31_24:8;
  } Field;
  UINT32 Value;
} IOAGR_PERF_COUNT3_UPPER_STRUCT;

#define SMN_IOAGR_PERF_COUNT3_UPPER_ADDRESS                           0x15b00064UL
#define SMN_IOHUB0NBIO0_IOAGR_PERF_COUNT3_UPPER_ADDRESS               0x15b00064UL
#define SMN_IOHUB0NBIO1_IOAGR_PERF_COUNT3_UPPER_ADDRESS               0x15d00064UL
#define SMN_IOHUB1NBIO0_IOAGR_PERF_COUNT3_UPPER_ADDRESS               0x1e000064UL
#define SMN_IOHUB1NBIO1_IOAGR_PERF_COUNT3_UPPER_ADDRESS               0x1e200064UL
#define SMN_IOHUB2NBIO0_IOAGR_PERF_COUNT3_UPPER_ADDRESS               0x15c00064UL
#define SMN_IOHUB2NBIO1_IOAGR_PERF_COUNT3_UPPER_ADDRESS               0x15e00064UL
#define SMN_IOHUB3NBIO0_IOAGR_PERF_COUNT3_UPPER_ADDRESS               0x1e100064UL
#define SMN_IOHUB3NBIO1_IOAGR_PERF_COUNT3_UPPER_ADDRESS               0x1e300064UL


/***********************************************************
* Register Name : IOAGR_PGMST_CNTL
************************************************************/

#define IOAGR_PGMST_CNTL_CFG_PG_HYSTERESIS_OFFSET              0
#define IOAGR_PGMST_CNTL_CFG_PG_HYSTERESIS_MASK                0xff

#define IOAGR_PGMST_CNTL_CFG_PG_EN_OFFSET                      8
#define IOAGR_PGMST_CNTL_CFG_PG_EN_MASK                        0x100

#define IOAGR_PGMST_CNTL_Reserved_9_9_OFFSET                   9
#define IOAGR_PGMST_CNTL_Reserved_9_9_MASK                     0x200

#define IOAGR_PGMST_CNTL_CFG_IDLENESS_COUNT_EN_OFFSET          10
#define IOAGR_PGMST_CNTL_CFG_IDLENESS_COUNT_EN_MASK            0x3c00

#define IOAGR_PGMST_CNTL_CFG_FW_PG_EXIT_EN_OFFSET              14
#define IOAGR_PGMST_CNTL_CFG_FW_PG_EXIT_EN_MASK                0xc000

#define IOAGR_PGMST_CNTL_Reserved_31_16_OFFSET                 16
#define IOAGR_PGMST_CNTL_Reserved_31_16_MASK                   0xffff0000

typedef union {
  struct {
    UINT32                                   CFG_PG_HYSTERESIS:8;
    UINT32                                           CFG_PG_EN:1;
    UINT32                                        Reserved_9_9:1;
    UINT32                               CFG_IDLENESS_COUNT_EN:4;
    UINT32                                   CFG_FW_PG_EXIT_EN:2;
    UINT32                                      Reserved_31_16:16;
  } Field;
  UINT32 Value;
} IOAGR_PGMST_CNTL_STRUCT;

#define SMN_IOAGR_PGMST_CNTL_ADDRESS                                  0x15b00080UL
#define SMN_IOHUB0NBIO0_IOAGR_PGMST_CNTL_ADDRESS                      0x15b00080UL
#define SMN_IOHUB0NBIO1_IOAGR_PGMST_CNTL_ADDRESS                      0x15d00080UL
#define SMN_IOHUB1NBIO0_IOAGR_PGMST_CNTL_ADDRESS                      0x1e000080UL
#define SMN_IOHUB1NBIO1_IOAGR_PGMST_CNTL_ADDRESS                      0x1e200080UL
#define SMN_IOHUB2NBIO0_IOAGR_PGMST_CNTL_ADDRESS                      0x15c00080UL
#define SMN_IOHUB2NBIO1_IOAGR_PGMST_CNTL_ADDRESS                      0x15e00080UL
#define SMN_IOHUB3NBIO0_IOAGR_PGMST_CNTL_ADDRESS                      0x1e100080UL
#define SMN_IOHUB3NBIO1_IOAGR_PGMST_CNTL_ADDRESS                      0x1e300080UL


/***********************************************************
* Register Name : IOAGR_PGSLV_CNTL
************************************************************/

#define IOAGR_PGSLV_CNTL_CFG_IDLE_HYSTERESIS_OFFSET            0
#define IOAGR_PGSLV_CNTL_CFG_IDLE_HYSTERESIS_MASK              0x1f

#define IOAGR_PGSLV_CNTL_Reserved_31_5_OFFSET                  5
#define IOAGR_PGSLV_CNTL_Reserved_31_5_MASK                    0xffffffe0

typedef union {
  struct {
    UINT32                                 CFG_IDLE_HYSTERESIS:5;
    UINT32                                       Reserved_31_5:27;
  } Field;
  UINT32 Value;
} IOAGR_PGSLV_CNTL_STRUCT;

#define SMN_IOAGR_PGSLV_CNTL_ADDRESS                                  0x15b00084UL
#define SMN_IOHUB0NBIO0_IOAGR_PGSLV_CNTL_ADDRESS                      0x15b00084UL
#define SMN_IOHUB0NBIO1_IOAGR_PGSLV_CNTL_ADDRESS                      0x15d00084UL
#define SMN_IOHUB1NBIO0_IOAGR_PGSLV_CNTL_ADDRESS                      0x1e000084UL
#define SMN_IOHUB1NBIO1_IOAGR_PGSLV_CNTL_ADDRESS                      0x1e200084UL
#define SMN_IOHUB2NBIO0_IOAGR_PGSLV_CNTL_ADDRESS                      0x15c00084UL
#define SMN_IOHUB2NBIO1_IOAGR_PGSLV_CNTL_ADDRESS                      0x15e00084UL
#define SMN_IOHUB3NBIO0_IOAGR_PGSLV_CNTL_ADDRESS                      0x1e100084UL
#define SMN_IOHUB3NBIO1_IOAGR_PGSLV_CNTL_ADDRESS                      0x1e300084UL


/***********************************************************
* Register Name : IOAGR_PORTID_RANGE
************************************************************/

#define IOAGR_PORTID_RANGE_PortID_range_low0_OFFSET            0
#define IOAGR_PORTID_RANGE_PortID_range_low0_MASK              0xf

#define IOAGR_PORTID_RANGE_PortID_range_high0_OFFSET           4
#define IOAGR_PORTID_RANGE_PortID_range_high0_MASK             0xf0

#define IOAGR_PORTID_RANGE_PortID_range_low1_OFFSET            8
#define IOAGR_PORTID_RANGE_PortID_range_low1_MASK              0xf00

#define IOAGR_PORTID_RANGE_PortID_range_high1_OFFSET           12
#define IOAGR_PORTID_RANGE_PortID_range_high1_MASK             0xf000

#define IOAGR_PORTID_RANGE_PortID_range_low2_OFFSET            16
#define IOAGR_PORTID_RANGE_PortID_range_low2_MASK              0xf0000

#define IOAGR_PORTID_RANGE_PortID_range_high2_OFFSET           20
#define IOAGR_PORTID_RANGE_PortID_range_high2_MASK             0xf00000

#define IOAGR_PORTID_RANGE_PortID_range_low3_OFFSET            24
#define IOAGR_PORTID_RANGE_PortID_range_low3_MASK              0xf000000

#define IOAGR_PORTID_RANGE_PortID_range_high3_OFFSET           28
#define IOAGR_PORTID_RANGE_PortID_range_high3_MASK             0xf0000000

typedef union {
  struct {
    UINT32                                   PortID_range_low0:4;
    UINT32                                  PortID_range_high0:4;
    UINT32                                   PortID_range_low1:4;
    UINT32                                  PortID_range_high1:4;
    UINT32                                   PortID_range_low2:4;
    UINT32                                  PortID_range_high2:4;
    UINT32                                   PortID_range_low3:4;
    UINT32                                  PortID_range_high3:4;
  } Field;
  UINT32 Value;
} IOAGR_PORTID_RANGE_STRUCT;

#define SMN_IOAGR_PORTID_RANGE_ADDRESS                                0x15b00014UL
#define SMN_IOHUB0NBIO0_IOAGR_PORTID_RANGE_ADDRESS                    0x15b00014UL
#define SMN_IOHUB0NBIO1_IOAGR_PORTID_RANGE_ADDRESS                    0x15d00014UL
#define SMN_IOHUB1NBIO0_IOAGR_PORTID_RANGE_ADDRESS                    0x1e000014UL
#define SMN_IOHUB1NBIO1_IOAGR_PORTID_RANGE_ADDRESS                    0x1e200014UL
#define SMN_IOHUB2NBIO0_IOAGR_PORTID_RANGE_ADDRESS                    0x15c00014UL
#define SMN_IOHUB2NBIO1_IOAGR_PORTID_RANGE_ADDRESS                    0x15e00014UL
#define SMN_IOHUB3NBIO0_IOAGR_PORTID_RANGE_ADDRESS                    0x1e100014UL
#define SMN_IOHUB3NBIO1_IOAGR_PORTID_RANGE_ADDRESS                    0x1e300014UL


/***********************************************************
* Register Name : IOAGR_PORTID_RANGE1
************************************************************/

#define IOAGR_PORTID_RANGE1_PortID_range_low4_OFFSET           0
#define IOAGR_PORTID_RANGE1_PortID_range_low4_MASK             0xf

#define IOAGR_PORTID_RANGE1_PortID_range_high4_OFFSET          4
#define IOAGR_PORTID_RANGE1_PortID_range_high4_MASK            0xf0

#define IOAGR_PORTID_RANGE1_Reserved_31_8_OFFSET               8
#define IOAGR_PORTID_RANGE1_Reserved_31_8_MASK                 0xffffff00

typedef union {
  struct {
    UINT32                                   PortID_range_low4:4;
    UINT32                                  PortID_range_high4:4;
    UINT32                                       Reserved_31_8:24;
  } Field;
  UINT32 Value;
} IOAGR_PORTID_RANGE1_STRUCT;

#define SMN_IOAGR_PORTID_RANGE1_ADDRESS                               0x15b00018UL
#define SMN_IOHUB0NBIO0_IOAGR_PORTID_RANGE1_ADDRESS                   0x15b00018UL
#define SMN_IOHUB0NBIO1_IOAGR_PORTID_RANGE1_ADDRESS                   0x15d00018UL
#define SMN_IOHUB2NBIO0_IOAGR_PORTID_RANGE1_ADDRESS                   0x15c00018UL
#define SMN_IOHUB2NBIO1_IOAGR_PORTID_RANGE1_ADDRESS                   0x15e00018UL


/***********************************************************
* Register Name : IOAGR_RSMU_HCID
************************************************************/

#define IOAGR_RSMU_HCID_RSMU_HCID_HwRev_OFFSET                 0
#define IOAGR_RSMU_HCID_RSMU_HCID_HwRev_MASK                   0x3f

#define IOAGR_RSMU_HCID_Reserved_7_6_OFFSET                    6
#define IOAGR_RSMU_HCID_Reserved_7_6_MASK                      0xc0

#define IOAGR_RSMU_HCID_RSMU_HCID_HwMinVer_OFFSET              8
#define IOAGR_RSMU_HCID_RSMU_HCID_HwMinVer_MASK                0x7f00

#define IOAGR_RSMU_HCID_Reserved_15_15_OFFSET                  15
#define IOAGR_RSMU_HCID_Reserved_15_15_MASK                    0x8000

#define IOAGR_RSMU_HCID_RSMU_HCID_HwMajVer_OFFSET              16
#define IOAGR_RSMU_HCID_RSMU_HCID_HwMajVer_MASK                0x7f0000

#define IOAGR_RSMU_HCID_Reserved_31_23_OFFSET                  23
#define IOAGR_RSMU_HCID_Reserved_31_23_MASK                    0xff800000

typedef union {
  struct {
    UINT32                                     RSMU_HCID_HwRev:6;
    UINT32                                        Reserved_7_6:2;
    UINT32                                  RSMU_HCID_HwMinVer:7;
    UINT32                                      Reserved_15_15:1;
    UINT32                                  RSMU_HCID_HwMajVer:7;
    UINT32                                      Reserved_31_23:9;
  } Field;
  UINT32 Value;
} IOAGR_RSMU_HCID_STRUCT;

#define SMN_IOAGR_RSMU_HCID_ADDRESS                                   0x15b00088UL
#define SMN_IOHUB0NBIO0_IOAGR_RSMU_HCID_ADDRESS                       0x15b00088UL
#define SMN_IOHUB0NBIO1_IOAGR_RSMU_HCID_ADDRESS                       0x15d00088UL
#define SMN_IOHUB1NBIO0_IOAGR_RSMU_HCID_ADDRESS                       0x1e000088UL
#define SMN_IOHUB1NBIO1_IOAGR_RSMU_HCID_ADDRESS                       0x1e200088UL
#define SMN_IOHUB2NBIO0_IOAGR_RSMU_HCID_ADDRESS                       0x15c00088UL
#define SMN_IOHUB2NBIO1_IOAGR_RSMU_HCID_ADDRESS                       0x15e00088UL
#define SMN_IOHUB3NBIO0_IOAGR_RSMU_HCID_ADDRESS                       0x1e100088UL
#define SMN_IOHUB3NBIO1_IOAGR_RSMU_HCID_ADDRESS                       0x1e300088UL


/***********************************************************
* Register Name : IOAGR_RSMU_SIID
************************************************************/

#define IOAGR_RSMU_SIID_RSMU_SIID_SwIfRev_OFFSET               0
#define IOAGR_RSMU_SIID_RSMU_SIID_SwIfRev_MASK                 0x3f

#define IOAGR_RSMU_SIID_Reserved_7_6_OFFSET                    6
#define IOAGR_RSMU_SIID_Reserved_7_6_MASK                      0xc0

#define IOAGR_RSMU_SIID_RSMU_SIID_SwIfMinVer_OFFSET            8
#define IOAGR_RSMU_SIID_RSMU_SIID_SwIfMinVer_MASK              0x7f00

#define IOAGR_RSMU_SIID_Reserved_15_15_OFFSET                  15
#define IOAGR_RSMU_SIID_Reserved_15_15_MASK                    0x8000

#define IOAGR_RSMU_SIID_RSMU_SIID_SwIfMajVer_OFFSET            16
#define IOAGR_RSMU_SIID_RSMU_SIID_SwIfMajVer_MASK              0x7f0000

#define IOAGR_RSMU_SIID_Reserved_31_23_OFFSET                  23
#define IOAGR_RSMU_SIID_Reserved_31_23_MASK                    0xff800000

typedef union {
  struct {
    UINT32                                   RSMU_SIID_SwIfRev:6;
    UINT32                                        Reserved_7_6:2;
    UINT32                                RSMU_SIID_SwIfMinVer:7;
    UINT32                                      Reserved_15_15:1;
    UINT32                                RSMU_SIID_SwIfMajVer:7;
    UINT32                                      Reserved_31_23:9;
  } Field;
  UINT32 Value;
} IOAGR_RSMU_SIID_STRUCT;

#define SMN_IOAGR_RSMU_SIID_ADDRESS                                   0x15b0008cUL
#define SMN_IOHUB0NBIO0_IOAGR_RSMU_SIID_ADDRESS                       0x15b0008cUL
#define SMN_IOHUB0NBIO1_IOAGR_RSMU_SIID_ADDRESS                       0x15d0008cUL
#define SMN_IOHUB1NBIO0_IOAGR_RSMU_SIID_ADDRESS                       0x1e00008cUL
#define SMN_IOHUB1NBIO1_IOAGR_RSMU_SIID_ADDRESS                       0x1e20008cUL
#define SMN_IOHUB2NBIO0_IOAGR_RSMU_SIID_ADDRESS                       0x15c0008cUL
#define SMN_IOHUB2NBIO1_IOAGR_RSMU_SIID_ADDRESS                       0x15e0008cUL
#define SMN_IOHUB3NBIO0_IOAGR_RSMU_SIID_ADDRESS                       0x1e10008cUL
#define SMN_IOHUB3NBIO1_IOAGR_RSMU_SIID_ADDRESS                       0x1e30008cUL


/***********************************************************
* Register Name : IOAGR_SDP_PORT_CONTROL
************************************************************/

#define IOAGR_SDP_PORT_CONTROL_Port_Disconnect_Hysteresis_OFFSET 0
#define IOAGR_SDP_PORT_CONTROL_Port_Disconnect_Hysteresis_MASK 0xfff

#define IOAGR_SDP_PORT_CONTROL_Reserved_31_12_OFFSET           12
#define IOAGR_SDP_PORT_CONTROL_Reserved_31_12_MASK             0xfffff000

typedef union {
  struct {
    UINT32                          Port_Disconnect_Hysteresis:12;
    UINT32                                      Reserved_31_12:20;
  } Field;
  UINT32 Value;
} IOAGR_SDP_PORT_CONTROL_STRUCT;

#define SMN_IOAGR_SDP_PORT_CONTROL_ADDRESS                            0x15b00040UL
#define SMN_IOHUB0NBIO0_IOAGR_SDP_PORT_CONTROL_ADDRESS                0x15b00040UL
#define SMN_IOHUB0NBIO1_IOAGR_SDP_PORT_CONTROL_ADDRESS                0x15d00040UL
#define SMN_IOHUB1NBIO0_IOAGR_SDP_PORT_CONTROL_ADDRESS                0x1e000040UL
#define SMN_IOHUB1NBIO1_IOAGR_SDP_PORT_CONTROL_ADDRESS                0x1e200040UL
#define SMN_IOHUB2NBIO0_IOAGR_SDP_PORT_CONTROL_ADDRESS                0x15c00040UL
#define SMN_IOHUB2NBIO1_IOAGR_SDP_PORT_CONTROL_ADDRESS                0x15e00040UL
#define SMN_IOHUB3NBIO0_IOAGR_SDP_PORT_CONTROL_ADDRESS                0x1e100040UL
#define SMN_IOHUB3NBIO1_IOAGR_SDP_PORT_CONTROL_ADDRESS                0x1e300040UL


/***********************************************************
* Register Name : IOAGR_SION_Client_DataPoolCredit_Alloc_0
************************************************************/

#define IOAGR_SION_Client_DataPoolCredit_Alloc_0_IOAGR_SION_Client_DataPoolCredit_Alloc_0_OFFSET 0
#define IOAGR_SION_Client_DataPoolCredit_Alloc_0_IOAGR_SION_Client_DataPoolCredit_Alloc_0_MASK 0xffffffff

typedef union {
  struct {
    UINT32            IOAGR_SION_Client_DataPoolCredit_Alloc_0:32;
  } Field;
  UINT32 Value;
} IOAGR_SION_Client_DataPoolCredit_Alloc_0_STRUCT;

#define SMN_IOAGR_SION_Client_DataPoolCredit_Alloc_0_ADDRESS          0x15b00470UL
#define SMN_IOHUB0_N0NBIO0_IOAGR_SION_Client_DataPoolCredit_Alloc_0_ADDRESS 0x15b00470UL
#define SMN_IOHUB0_N0NBIO1_IOAGR_SION_Client_DataPoolCredit_Alloc_0_ADDRESS 0x15d00470UL
#define SMN_IOHUB0_N1NBIO0_IOAGR_SION_Client_DataPoolCredit_Alloc_0_ADDRESS 0x15b00870UL
#define SMN_IOHUB0_N1NBIO1_IOAGR_SION_Client_DataPoolCredit_Alloc_0_ADDRESS 0x15d00870UL
#define SMN_IOHUB0_N2NBIO0_IOAGR_SION_Client_DataPoolCredit_Alloc_0_ADDRESS 0x15b00c70UL
#define SMN_IOHUB0_N2NBIO1_IOAGR_SION_Client_DataPoolCredit_Alloc_0_ADDRESS 0x15d00c70UL
#define SMN_IOHUB0_N3NBIO0_IOAGR_SION_Client_DataPoolCredit_Alloc_0_ADDRESS 0x15b01070UL
#define SMN_IOHUB0_N3NBIO1_IOAGR_SION_Client_DataPoolCredit_Alloc_0_ADDRESS 0x15d01070UL
#define SMN_IOHUB0_N4NBIO0_IOAGR_SION_Client_DataPoolCredit_Alloc_0_ADDRESS 0x15b01470UL
#define SMN_IOHUB0_N4NBIO1_IOAGR_SION_Client_DataPoolCredit_Alloc_0_ADDRESS 0x15d01470UL
#define SMN_IOHUB0_N5NBIO0_IOAGR_SION_Client_DataPoolCredit_Alloc_0_ADDRESS 0x15b01870UL
#define SMN_IOHUB0_N5NBIO1_IOAGR_SION_Client_DataPoolCredit_Alloc_0_ADDRESS 0x15d01870UL
#define SMN_IOHUB1_N0NBIO0_IOAGR_SION_Client_DataPoolCredit_Alloc_0_ADDRESS 0x1e000470UL
#define SMN_IOHUB1_N0NBIO1_IOAGR_SION_Client_DataPoolCredit_Alloc_0_ADDRESS 0x1e200470UL
#define SMN_IOHUB1_N1NBIO0_IOAGR_SION_Client_DataPoolCredit_Alloc_0_ADDRESS 0x1e000870UL
#define SMN_IOHUB1_N1NBIO1_IOAGR_SION_Client_DataPoolCredit_Alloc_0_ADDRESS 0x1e200870UL
#define SMN_IOHUB1_N2NBIO0_IOAGR_SION_Client_DataPoolCredit_Alloc_0_ADDRESS 0x1e000c70UL
#define SMN_IOHUB1_N2NBIO1_IOAGR_SION_Client_DataPoolCredit_Alloc_0_ADDRESS 0x1e200c70UL
#define SMN_IOHUB2_N0NBIO0_IOAGR_SION_Client_DataPoolCredit_Alloc_0_ADDRESS 0x15c00470UL
#define SMN_IOHUB2_N0NBIO1_IOAGR_SION_Client_DataPoolCredit_Alloc_0_ADDRESS 0x15e00470UL
#define SMN_IOHUB2_N1NBIO0_IOAGR_SION_Client_DataPoolCredit_Alloc_0_ADDRESS 0x15c00870UL
#define SMN_IOHUB2_N1NBIO1_IOAGR_SION_Client_DataPoolCredit_Alloc_0_ADDRESS 0x15e00870UL
#define SMN_IOHUB2_N2NBIO0_IOAGR_SION_Client_DataPoolCredit_Alloc_0_ADDRESS 0x15c00c70UL
#define SMN_IOHUB2_N2NBIO1_IOAGR_SION_Client_DataPoolCredit_Alloc_0_ADDRESS 0x15e00c70UL
#define SMN_IOHUB2_N3NBIO0_IOAGR_SION_Client_DataPoolCredit_Alloc_0_ADDRESS 0x15c01070UL
#define SMN_IOHUB2_N3NBIO1_IOAGR_SION_Client_DataPoolCredit_Alloc_0_ADDRESS 0x15e01070UL
#define SMN_IOHUB2_N4NBIO0_IOAGR_SION_Client_DataPoolCredit_Alloc_0_ADDRESS 0x15c01470UL
#define SMN_IOHUB2_N4NBIO1_IOAGR_SION_Client_DataPoolCredit_Alloc_0_ADDRESS 0x15e01470UL
#define SMN_IOHUB2_N5NBIO0_IOAGR_SION_Client_DataPoolCredit_Alloc_0_ADDRESS 0x15c01870UL
#define SMN_IOHUB2_N5NBIO1_IOAGR_SION_Client_DataPoolCredit_Alloc_0_ADDRESS 0x15e01870UL
#define SMN_IOHUB3_N0NBIO0_IOAGR_SION_Client_DataPoolCredit_Alloc_0_ADDRESS 0x1e100470UL
#define SMN_IOHUB3_N0NBIO1_IOAGR_SION_Client_DataPoolCredit_Alloc_0_ADDRESS 0x1e300470UL
#define SMN_IOHUB3_N1NBIO0_IOAGR_SION_Client_DataPoolCredit_Alloc_0_ADDRESS 0x1e100870UL
#define SMN_IOHUB3_N1NBIO1_IOAGR_SION_Client_DataPoolCredit_Alloc_0_ADDRESS 0x1e300870UL
#define SMN_IOHUB3_N2NBIO0_IOAGR_SION_Client_DataPoolCredit_Alloc_0_ADDRESS 0x1e100c70UL
#define SMN_IOHUB3_N2NBIO1_IOAGR_SION_Client_DataPoolCredit_Alloc_0_ADDRESS 0x1e300c70UL


/***********************************************************
* Register Name : IOAGR_SION_Client_DataPoolCredit_Alloc_1
************************************************************/

#define IOAGR_SION_Client_DataPoolCredit_Alloc_1_IOAGR_SION_Client_DataPoolCredit_Alloc_1_OFFSET 0
#define IOAGR_SION_Client_DataPoolCredit_Alloc_1_IOAGR_SION_Client_DataPoolCredit_Alloc_1_MASK 0xffffffff

typedef union {
  struct {
    UINT32            IOAGR_SION_Client_DataPoolCredit_Alloc_1:32;
  } Field;
  UINT32 Value;
} IOAGR_SION_Client_DataPoolCredit_Alloc_1_STRUCT;

#define SMN_IOAGR_SION_Client_DataPoolCredit_Alloc_1_ADDRESS          0x15b00474UL
#define SMN_IOHUB0_N0NBIO0_IOAGR_SION_Client_DataPoolCredit_Alloc_1_ADDRESS 0x15b00474UL
#define SMN_IOHUB0_N0NBIO1_IOAGR_SION_Client_DataPoolCredit_Alloc_1_ADDRESS 0x15d00474UL
#define SMN_IOHUB0_N1NBIO0_IOAGR_SION_Client_DataPoolCredit_Alloc_1_ADDRESS 0x15b00874UL
#define SMN_IOHUB0_N1NBIO1_IOAGR_SION_Client_DataPoolCredit_Alloc_1_ADDRESS 0x15d00874UL
#define SMN_IOHUB0_N2NBIO0_IOAGR_SION_Client_DataPoolCredit_Alloc_1_ADDRESS 0x15b00c74UL
#define SMN_IOHUB0_N2NBIO1_IOAGR_SION_Client_DataPoolCredit_Alloc_1_ADDRESS 0x15d00c74UL
#define SMN_IOHUB0_N3NBIO0_IOAGR_SION_Client_DataPoolCredit_Alloc_1_ADDRESS 0x15b01074UL
#define SMN_IOHUB0_N3NBIO1_IOAGR_SION_Client_DataPoolCredit_Alloc_1_ADDRESS 0x15d01074UL
#define SMN_IOHUB0_N4NBIO0_IOAGR_SION_Client_DataPoolCredit_Alloc_1_ADDRESS 0x15b01474UL
#define SMN_IOHUB0_N4NBIO1_IOAGR_SION_Client_DataPoolCredit_Alloc_1_ADDRESS 0x15d01474UL
#define SMN_IOHUB0_N5NBIO0_IOAGR_SION_Client_DataPoolCredit_Alloc_1_ADDRESS 0x15b01874UL
#define SMN_IOHUB0_N5NBIO1_IOAGR_SION_Client_DataPoolCredit_Alloc_1_ADDRESS 0x15d01874UL
#define SMN_IOHUB1_N0NBIO0_IOAGR_SION_Client_DataPoolCredit_Alloc_1_ADDRESS 0x1e000474UL
#define SMN_IOHUB1_N0NBIO1_IOAGR_SION_Client_DataPoolCredit_Alloc_1_ADDRESS 0x1e200474UL
#define SMN_IOHUB1_N1NBIO0_IOAGR_SION_Client_DataPoolCredit_Alloc_1_ADDRESS 0x1e000874UL
#define SMN_IOHUB1_N1NBIO1_IOAGR_SION_Client_DataPoolCredit_Alloc_1_ADDRESS 0x1e200874UL
#define SMN_IOHUB1_N2NBIO0_IOAGR_SION_Client_DataPoolCredit_Alloc_1_ADDRESS 0x1e000c74UL
#define SMN_IOHUB1_N2NBIO1_IOAGR_SION_Client_DataPoolCredit_Alloc_1_ADDRESS 0x1e200c74UL
#define SMN_IOHUB2_N0NBIO0_IOAGR_SION_Client_DataPoolCredit_Alloc_1_ADDRESS 0x15c00474UL
#define SMN_IOHUB2_N0NBIO1_IOAGR_SION_Client_DataPoolCredit_Alloc_1_ADDRESS 0x15e00474UL
#define SMN_IOHUB2_N1NBIO0_IOAGR_SION_Client_DataPoolCredit_Alloc_1_ADDRESS 0x15c00874UL
#define SMN_IOHUB2_N1NBIO1_IOAGR_SION_Client_DataPoolCredit_Alloc_1_ADDRESS 0x15e00874UL
#define SMN_IOHUB2_N2NBIO0_IOAGR_SION_Client_DataPoolCredit_Alloc_1_ADDRESS 0x15c00c74UL
#define SMN_IOHUB2_N2NBIO1_IOAGR_SION_Client_DataPoolCredit_Alloc_1_ADDRESS 0x15e00c74UL
#define SMN_IOHUB2_N3NBIO0_IOAGR_SION_Client_DataPoolCredit_Alloc_1_ADDRESS 0x15c01074UL
#define SMN_IOHUB2_N3NBIO1_IOAGR_SION_Client_DataPoolCredit_Alloc_1_ADDRESS 0x15e01074UL
#define SMN_IOHUB2_N4NBIO0_IOAGR_SION_Client_DataPoolCredit_Alloc_1_ADDRESS 0x15c01474UL
#define SMN_IOHUB2_N4NBIO1_IOAGR_SION_Client_DataPoolCredit_Alloc_1_ADDRESS 0x15e01474UL
#define SMN_IOHUB2_N5NBIO0_IOAGR_SION_Client_DataPoolCredit_Alloc_1_ADDRESS 0x15c01874UL
#define SMN_IOHUB2_N5NBIO1_IOAGR_SION_Client_DataPoolCredit_Alloc_1_ADDRESS 0x15e01874UL
#define SMN_IOHUB3_N0NBIO0_IOAGR_SION_Client_DataPoolCredit_Alloc_1_ADDRESS 0x1e100474UL
#define SMN_IOHUB3_N0NBIO1_IOAGR_SION_Client_DataPoolCredit_Alloc_1_ADDRESS 0x1e300474UL
#define SMN_IOHUB3_N1NBIO0_IOAGR_SION_Client_DataPoolCredit_Alloc_1_ADDRESS 0x1e100874UL
#define SMN_IOHUB3_N1NBIO1_IOAGR_SION_Client_DataPoolCredit_Alloc_1_ADDRESS 0x1e300874UL
#define SMN_IOHUB3_N2NBIO0_IOAGR_SION_Client_DataPoolCredit_Alloc_1_ADDRESS 0x1e100c74UL
#define SMN_IOHUB3_N2NBIO1_IOAGR_SION_Client_DataPoolCredit_Alloc_1_ADDRESS 0x1e300c74UL


/***********************************************************
* Register Name : IOAGR_SION_Client_DataPoolCredit_Alloc_2
************************************************************/

#define IOAGR_SION_Client_DataPoolCredit_Alloc_2_IOAGR_SION_Client_DataPoolCredit_Alloc_2_OFFSET 0
#define IOAGR_SION_Client_DataPoolCredit_Alloc_2_IOAGR_SION_Client_DataPoolCredit_Alloc_2_MASK 0xffffffff

typedef union {
  struct {
    UINT32            IOAGR_SION_Client_DataPoolCredit_Alloc_2:32;
  } Field;
  UINT32 Value;
} IOAGR_SION_Client_DataPoolCredit_Alloc_2_STRUCT;

#define SMN_IOAGR_SION_Client_DataPoolCredit_Alloc_2_ADDRESS          0x15b00478UL
#define SMN_IOHUB0_N0NBIO0_IOAGR_SION_Client_DataPoolCredit_Alloc_2_ADDRESS 0x15b00478UL
#define SMN_IOHUB0_N0NBIO1_IOAGR_SION_Client_DataPoolCredit_Alloc_2_ADDRESS 0x15d00478UL
#define SMN_IOHUB0_N1NBIO0_IOAGR_SION_Client_DataPoolCredit_Alloc_2_ADDRESS 0x15b00878UL
#define SMN_IOHUB0_N1NBIO1_IOAGR_SION_Client_DataPoolCredit_Alloc_2_ADDRESS 0x15d00878UL
#define SMN_IOHUB0_N2NBIO0_IOAGR_SION_Client_DataPoolCredit_Alloc_2_ADDRESS 0x15b00c78UL
#define SMN_IOHUB0_N2NBIO1_IOAGR_SION_Client_DataPoolCredit_Alloc_2_ADDRESS 0x15d00c78UL
#define SMN_IOHUB0_N3NBIO0_IOAGR_SION_Client_DataPoolCredit_Alloc_2_ADDRESS 0x15b01078UL
#define SMN_IOHUB0_N3NBIO1_IOAGR_SION_Client_DataPoolCredit_Alloc_2_ADDRESS 0x15d01078UL
#define SMN_IOHUB0_N4NBIO0_IOAGR_SION_Client_DataPoolCredit_Alloc_2_ADDRESS 0x15b01478UL
#define SMN_IOHUB0_N4NBIO1_IOAGR_SION_Client_DataPoolCredit_Alloc_2_ADDRESS 0x15d01478UL
#define SMN_IOHUB0_N5NBIO0_IOAGR_SION_Client_DataPoolCredit_Alloc_2_ADDRESS 0x15b01878UL
#define SMN_IOHUB0_N5NBIO1_IOAGR_SION_Client_DataPoolCredit_Alloc_2_ADDRESS 0x15d01878UL
#define SMN_IOHUB1_N0NBIO0_IOAGR_SION_Client_DataPoolCredit_Alloc_2_ADDRESS 0x1e000478UL
#define SMN_IOHUB1_N0NBIO1_IOAGR_SION_Client_DataPoolCredit_Alloc_2_ADDRESS 0x1e200478UL
#define SMN_IOHUB1_N1NBIO0_IOAGR_SION_Client_DataPoolCredit_Alloc_2_ADDRESS 0x1e000878UL
#define SMN_IOHUB1_N1NBIO1_IOAGR_SION_Client_DataPoolCredit_Alloc_2_ADDRESS 0x1e200878UL
#define SMN_IOHUB1_N2NBIO0_IOAGR_SION_Client_DataPoolCredit_Alloc_2_ADDRESS 0x1e000c78UL
#define SMN_IOHUB1_N2NBIO1_IOAGR_SION_Client_DataPoolCredit_Alloc_2_ADDRESS 0x1e200c78UL
#define SMN_IOHUB2_N0NBIO0_IOAGR_SION_Client_DataPoolCredit_Alloc_2_ADDRESS 0x15c00478UL
#define SMN_IOHUB2_N0NBIO1_IOAGR_SION_Client_DataPoolCredit_Alloc_2_ADDRESS 0x15e00478UL
#define SMN_IOHUB2_N1NBIO0_IOAGR_SION_Client_DataPoolCredit_Alloc_2_ADDRESS 0x15c00878UL
#define SMN_IOHUB2_N1NBIO1_IOAGR_SION_Client_DataPoolCredit_Alloc_2_ADDRESS 0x15e00878UL
#define SMN_IOHUB2_N2NBIO0_IOAGR_SION_Client_DataPoolCredit_Alloc_2_ADDRESS 0x15c00c78UL
#define SMN_IOHUB2_N2NBIO1_IOAGR_SION_Client_DataPoolCredit_Alloc_2_ADDRESS 0x15e00c78UL
#define SMN_IOHUB2_N3NBIO0_IOAGR_SION_Client_DataPoolCredit_Alloc_2_ADDRESS 0x15c01078UL
#define SMN_IOHUB2_N3NBIO1_IOAGR_SION_Client_DataPoolCredit_Alloc_2_ADDRESS 0x15e01078UL
#define SMN_IOHUB2_N4NBIO0_IOAGR_SION_Client_DataPoolCredit_Alloc_2_ADDRESS 0x15c01478UL
#define SMN_IOHUB2_N4NBIO1_IOAGR_SION_Client_DataPoolCredit_Alloc_2_ADDRESS 0x15e01478UL
#define SMN_IOHUB2_N5NBIO0_IOAGR_SION_Client_DataPoolCredit_Alloc_2_ADDRESS 0x15c01878UL
#define SMN_IOHUB2_N5NBIO1_IOAGR_SION_Client_DataPoolCredit_Alloc_2_ADDRESS 0x15e01878UL
#define SMN_IOHUB3_N0NBIO0_IOAGR_SION_Client_DataPoolCredit_Alloc_2_ADDRESS 0x1e100478UL
#define SMN_IOHUB3_N0NBIO1_IOAGR_SION_Client_DataPoolCredit_Alloc_2_ADDRESS 0x1e300478UL
#define SMN_IOHUB3_N1NBIO0_IOAGR_SION_Client_DataPoolCredit_Alloc_2_ADDRESS 0x1e100878UL
#define SMN_IOHUB3_N1NBIO1_IOAGR_SION_Client_DataPoolCredit_Alloc_2_ADDRESS 0x1e300878UL
#define SMN_IOHUB3_N2NBIO0_IOAGR_SION_Client_DataPoolCredit_Alloc_2_ADDRESS 0x1e100c78UL
#define SMN_IOHUB3_N2NBIO1_IOAGR_SION_Client_DataPoolCredit_Alloc_2_ADDRESS 0x1e300c78UL


/***********************************************************
* Register Name : IOAGR_SION_Client_DataPoolCredit_Alloc_3
************************************************************/

#define IOAGR_SION_Client_DataPoolCredit_Alloc_3_IOAGR_SION_Client_DataPoolCredit_Alloc_3_OFFSET 0
#define IOAGR_SION_Client_DataPoolCredit_Alloc_3_IOAGR_SION_Client_DataPoolCredit_Alloc_3_MASK 0xffffffff

typedef union {
  struct {
    UINT32            IOAGR_SION_Client_DataPoolCredit_Alloc_3:32;
  } Field;
  UINT32 Value;
} IOAGR_SION_Client_DataPoolCredit_Alloc_3_STRUCT;

#define SMN_IOAGR_SION_Client_DataPoolCredit_Alloc_3_ADDRESS          0x15b0047cUL
#define SMN_IOHUB0_N0NBIO0_IOAGR_SION_Client_DataPoolCredit_Alloc_3_ADDRESS 0x15b0047cUL
#define SMN_IOHUB0_N0NBIO1_IOAGR_SION_Client_DataPoolCredit_Alloc_3_ADDRESS 0x15d0047cUL
#define SMN_IOHUB0_N1NBIO0_IOAGR_SION_Client_DataPoolCredit_Alloc_3_ADDRESS 0x15b0087cUL
#define SMN_IOHUB0_N1NBIO1_IOAGR_SION_Client_DataPoolCredit_Alloc_3_ADDRESS 0x15d0087cUL
#define SMN_IOHUB0_N2NBIO0_IOAGR_SION_Client_DataPoolCredit_Alloc_3_ADDRESS 0x15b00c7cUL
#define SMN_IOHUB0_N2NBIO1_IOAGR_SION_Client_DataPoolCredit_Alloc_3_ADDRESS 0x15d00c7cUL
#define SMN_IOHUB0_N3NBIO0_IOAGR_SION_Client_DataPoolCredit_Alloc_3_ADDRESS 0x15b0107cUL
#define SMN_IOHUB0_N3NBIO1_IOAGR_SION_Client_DataPoolCredit_Alloc_3_ADDRESS 0x15d0107cUL
#define SMN_IOHUB0_N4NBIO0_IOAGR_SION_Client_DataPoolCredit_Alloc_3_ADDRESS 0x15b0147cUL
#define SMN_IOHUB0_N4NBIO1_IOAGR_SION_Client_DataPoolCredit_Alloc_3_ADDRESS 0x15d0147cUL
#define SMN_IOHUB0_N5NBIO0_IOAGR_SION_Client_DataPoolCredit_Alloc_3_ADDRESS 0x15b0187cUL
#define SMN_IOHUB0_N5NBIO1_IOAGR_SION_Client_DataPoolCredit_Alloc_3_ADDRESS 0x15d0187cUL
#define SMN_IOHUB1_N0NBIO0_IOAGR_SION_Client_DataPoolCredit_Alloc_3_ADDRESS 0x1e00047cUL
#define SMN_IOHUB1_N0NBIO1_IOAGR_SION_Client_DataPoolCredit_Alloc_3_ADDRESS 0x1e20047cUL
#define SMN_IOHUB1_N1NBIO0_IOAGR_SION_Client_DataPoolCredit_Alloc_3_ADDRESS 0x1e00087cUL
#define SMN_IOHUB1_N1NBIO1_IOAGR_SION_Client_DataPoolCredit_Alloc_3_ADDRESS 0x1e20087cUL
#define SMN_IOHUB1_N2NBIO0_IOAGR_SION_Client_DataPoolCredit_Alloc_3_ADDRESS 0x1e000c7cUL
#define SMN_IOHUB1_N2NBIO1_IOAGR_SION_Client_DataPoolCredit_Alloc_3_ADDRESS 0x1e200c7cUL
#define SMN_IOHUB2_N0NBIO0_IOAGR_SION_Client_DataPoolCredit_Alloc_3_ADDRESS 0x15c0047cUL
#define SMN_IOHUB2_N0NBIO1_IOAGR_SION_Client_DataPoolCredit_Alloc_3_ADDRESS 0x15e0047cUL
#define SMN_IOHUB2_N1NBIO0_IOAGR_SION_Client_DataPoolCredit_Alloc_3_ADDRESS 0x15c0087cUL
#define SMN_IOHUB2_N1NBIO1_IOAGR_SION_Client_DataPoolCredit_Alloc_3_ADDRESS 0x15e0087cUL
#define SMN_IOHUB2_N2NBIO0_IOAGR_SION_Client_DataPoolCredit_Alloc_3_ADDRESS 0x15c00c7cUL
#define SMN_IOHUB2_N2NBIO1_IOAGR_SION_Client_DataPoolCredit_Alloc_3_ADDRESS 0x15e00c7cUL
#define SMN_IOHUB2_N3NBIO0_IOAGR_SION_Client_DataPoolCredit_Alloc_3_ADDRESS 0x15c0107cUL
#define SMN_IOHUB2_N3NBIO1_IOAGR_SION_Client_DataPoolCredit_Alloc_3_ADDRESS 0x15e0107cUL
#define SMN_IOHUB2_N4NBIO0_IOAGR_SION_Client_DataPoolCredit_Alloc_3_ADDRESS 0x15c0147cUL
#define SMN_IOHUB2_N4NBIO1_IOAGR_SION_Client_DataPoolCredit_Alloc_3_ADDRESS 0x15e0147cUL
#define SMN_IOHUB2_N5NBIO0_IOAGR_SION_Client_DataPoolCredit_Alloc_3_ADDRESS 0x15c0187cUL
#define SMN_IOHUB2_N5NBIO1_IOAGR_SION_Client_DataPoolCredit_Alloc_3_ADDRESS 0x15e0187cUL
#define SMN_IOHUB3_N0NBIO0_IOAGR_SION_Client_DataPoolCredit_Alloc_3_ADDRESS 0x1e10047cUL
#define SMN_IOHUB3_N0NBIO1_IOAGR_SION_Client_DataPoolCredit_Alloc_3_ADDRESS 0x1e30047cUL
#define SMN_IOHUB3_N1NBIO0_IOAGR_SION_Client_DataPoolCredit_Alloc_3_ADDRESS 0x1e10087cUL
#define SMN_IOHUB3_N1NBIO1_IOAGR_SION_Client_DataPoolCredit_Alloc_3_ADDRESS 0x1e30087cUL
#define SMN_IOHUB3_N2NBIO0_IOAGR_SION_Client_DataPoolCredit_Alloc_3_ADDRESS 0x1e100c7cUL
#define SMN_IOHUB3_N2NBIO1_IOAGR_SION_Client_DataPoolCredit_Alloc_3_ADDRESS 0x1e300c7cUL


/***********************************************************
* Register Name : IOAGR_SION_Client_RdRspPoolCredit_Alloc_0
************************************************************/

#define IOAGR_SION_Client_RdRspPoolCredit_Alloc_0_IOAGR_SION_Client_RdRspPoolCredit_Alloc_0_OFFSET 0
#define IOAGR_SION_Client_RdRspPoolCredit_Alloc_0_IOAGR_SION_Client_RdRspPoolCredit_Alloc_0_MASK 0xffffffff

typedef union {
  struct {
    UINT32           IOAGR_SION_Client_RdRspPoolCredit_Alloc_0:32;
  } Field;
  UINT32 Value;
} IOAGR_SION_Client_RdRspPoolCredit_Alloc_0_STRUCT;

#define SMN_IOAGR_SION_Client_RdRspPoolCredit_Alloc_0_ADDRESS         0x15b00480UL
#define SMN_IOHUB0_N0NBIO0_IOAGR_SION_Client_RdRspPoolCredit_Alloc_0_ADDRESS 0x15b00480UL
#define SMN_IOHUB0_N0NBIO1_IOAGR_SION_Client_RdRspPoolCredit_Alloc_0_ADDRESS 0x15d00480UL
#define SMN_IOHUB0_N1NBIO0_IOAGR_SION_Client_RdRspPoolCredit_Alloc_0_ADDRESS 0x15b00880UL
#define SMN_IOHUB0_N1NBIO1_IOAGR_SION_Client_RdRspPoolCredit_Alloc_0_ADDRESS 0x15d00880UL
#define SMN_IOHUB0_N2NBIO0_IOAGR_SION_Client_RdRspPoolCredit_Alloc_0_ADDRESS 0x15b00c80UL
#define SMN_IOHUB0_N2NBIO1_IOAGR_SION_Client_RdRspPoolCredit_Alloc_0_ADDRESS 0x15d00c80UL
#define SMN_IOHUB0_N3NBIO0_IOAGR_SION_Client_RdRspPoolCredit_Alloc_0_ADDRESS 0x15b01080UL
#define SMN_IOHUB0_N3NBIO1_IOAGR_SION_Client_RdRspPoolCredit_Alloc_0_ADDRESS 0x15d01080UL
#define SMN_IOHUB0_N4NBIO0_IOAGR_SION_Client_RdRspPoolCredit_Alloc_0_ADDRESS 0x15b01480UL
#define SMN_IOHUB0_N4NBIO1_IOAGR_SION_Client_RdRspPoolCredit_Alloc_0_ADDRESS 0x15d01480UL
#define SMN_IOHUB0_N5NBIO0_IOAGR_SION_Client_RdRspPoolCredit_Alloc_0_ADDRESS 0x15b01880UL
#define SMN_IOHUB0_N5NBIO1_IOAGR_SION_Client_RdRspPoolCredit_Alloc_0_ADDRESS 0x15d01880UL
#define SMN_IOHUB1_N0NBIO0_IOAGR_SION_Client_RdRspPoolCredit_Alloc_0_ADDRESS 0x1e000480UL
#define SMN_IOHUB1_N0NBIO1_IOAGR_SION_Client_RdRspPoolCredit_Alloc_0_ADDRESS 0x1e200480UL
#define SMN_IOHUB1_N1NBIO0_IOAGR_SION_Client_RdRspPoolCredit_Alloc_0_ADDRESS 0x1e000880UL
#define SMN_IOHUB1_N1NBIO1_IOAGR_SION_Client_RdRspPoolCredit_Alloc_0_ADDRESS 0x1e200880UL
#define SMN_IOHUB1_N2NBIO0_IOAGR_SION_Client_RdRspPoolCredit_Alloc_0_ADDRESS 0x1e000c80UL
#define SMN_IOHUB1_N2NBIO1_IOAGR_SION_Client_RdRspPoolCredit_Alloc_0_ADDRESS 0x1e200c80UL
#define SMN_IOHUB2_N0NBIO0_IOAGR_SION_Client_RdRspPoolCredit_Alloc_0_ADDRESS 0x15c00480UL
#define SMN_IOHUB2_N0NBIO1_IOAGR_SION_Client_RdRspPoolCredit_Alloc_0_ADDRESS 0x15e00480UL
#define SMN_IOHUB2_N1NBIO0_IOAGR_SION_Client_RdRspPoolCredit_Alloc_0_ADDRESS 0x15c00880UL
#define SMN_IOHUB2_N1NBIO1_IOAGR_SION_Client_RdRspPoolCredit_Alloc_0_ADDRESS 0x15e00880UL
#define SMN_IOHUB2_N2NBIO0_IOAGR_SION_Client_RdRspPoolCredit_Alloc_0_ADDRESS 0x15c00c80UL
#define SMN_IOHUB2_N2NBIO1_IOAGR_SION_Client_RdRspPoolCredit_Alloc_0_ADDRESS 0x15e00c80UL
#define SMN_IOHUB2_N3NBIO0_IOAGR_SION_Client_RdRspPoolCredit_Alloc_0_ADDRESS 0x15c01080UL
#define SMN_IOHUB2_N3NBIO1_IOAGR_SION_Client_RdRspPoolCredit_Alloc_0_ADDRESS 0x15e01080UL
#define SMN_IOHUB2_N4NBIO0_IOAGR_SION_Client_RdRspPoolCredit_Alloc_0_ADDRESS 0x15c01480UL
#define SMN_IOHUB2_N4NBIO1_IOAGR_SION_Client_RdRspPoolCredit_Alloc_0_ADDRESS 0x15e01480UL
#define SMN_IOHUB2_N5NBIO0_IOAGR_SION_Client_RdRspPoolCredit_Alloc_0_ADDRESS 0x15c01880UL
#define SMN_IOHUB2_N5NBIO1_IOAGR_SION_Client_RdRspPoolCredit_Alloc_0_ADDRESS 0x15e01880UL
#define SMN_IOHUB3_N0NBIO0_IOAGR_SION_Client_RdRspPoolCredit_Alloc_0_ADDRESS 0x1e100480UL
#define SMN_IOHUB3_N0NBIO1_IOAGR_SION_Client_RdRspPoolCredit_Alloc_0_ADDRESS 0x1e300480UL
#define SMN_IOHUB3_N1NBIO0_IOAGR_SION_Client_RdRspPoolCredit_Alloc_0_ADDRESS 0x1e100880UL
#define SMN_IOHUB3_N1NBIO1_IOAGR_SION_Client_RdRspPoolCredit_Alloc_0_ADDRESS 0x1e300880UL
#define SMN_IOHUB3_N2NBIO0_IOAGR_SION_Client_RdRspPoolCredit_Alloc_0_ADDRESS 0x1e100c80UL
#define SMN_IOHUB3_N2NBIO1_IOAGR_SION_Client_RdRspPoolCredit_Alloc_0_ADDRESS 0x1e300c80UL


/***********************************************************
* Register Name : IOAGR_SION_Client_RdRspPoolCredit_Alloc_1
************************************************************/

#define IOAGR_SION_Client_RdRspPoolCredit_Alloc_1_IOAGR_SION_Client_RdRspPoolCredit_Alloc_1_OFFSET 0
#define IOAGR_SION_Client_RdRspPoolCredit_Alloc_1_IOAGR_SION_Client_RdRspPoolCredit_Alloc_1_MASK 0xffffffff

typedef union {
  struct {
    UINT32           IOAGR_SION_Client_RdRspPoolCredit_Alloc_1:32;
  } Field;
  UINT32 Value;
} IOAGR_SION_Client_RdRspPoolCredit_Alloc_1_STRUCT;

#define SMN_IOAGR_SION_Client_RdRspPoolCredit_Alloc_1_ADDRESS         0x15b00484UL
#define SMN_IOHUB0_N0NBIO0_IOAGR_SION_Client_RdRspPoolCredit_Alloc_1_ADDRESS 0x15b00484UL
#define SMN_IOHUB0_N0NBIO1_IOAGR_SION_Client_RdRspPoolCredit_Alloc_1_ADDRESS 0x15d00484UL
#define SMN_IOHUB0_N1NBIO0_IOAGR_SION_Client_RdRspPoolCredit_Alloc_1_ADDRESS 0x15b00884UL
#define SMN_IOHUB0_N1NBIO1_IOAGR_SION_Client_RdRspPoolCredit_Alloc_1_ADDRESS 0x15d00884UL
#define SMN_IOHUB0_N2NBIO0_IOAGR_SION_Client_RdRspPoolCredit_Alloc_1_ADDRESS 0x15b00c84UL
#define SMN_IOHUB0_N2NBIO1_IOAGR_SION_Client_RdRspPoolCredit_Alloc_1_ADDRESS 0x15d00c84UL
#define SMN_IOHUB0_N3NBIO0_IOAGR_SION_Client_RdRspPoolCredit_Alloc_1_ADDRESS 0x15b01084UL
#define SMN_IOHUB0_N3NBIO1_IOAGR_SION_Client_RdRspPoolCredit_Alloc_1_ADDRESS 0x15d01084UL
#define SMN_IOHUB0_N4NBIO0_IOAGR_SION_Client_RdRspPoolCredit_Alloc_1_ADDRESS 0x15b01484UL
#define SMN_IOHUB0_N4NBIO1_IOAGR_SION_Client_RdRspPoolCredit_Alloc_1_ADDRESS 0x15d01484UL
#define SMN_IOHUB0_N5NBIO0_IOAGR_SION_Client_RdRspPoolCredit_Alloc_1_ADDRESS 0x15b01884UL
#define SMN_IOHUB0_N5NBIO1_IOAGR_SION_Client_RdRspPoolCredit_Alloc_1_ADDRESS 0x15d01884UL
#define SMN_IOHUB1_N0NBIO0_IOAGR_SION_Client_RdRspPoolCredit_Alloc_1_ADDRESS 0x1e000484UL
#define SMN_IOHUB1_N0NBIO1_IOAGR_SION_Client_RdRspPoolCredit_Alloc_1_ADDRESS 0x1e200484UL
#define SMN_IOHUB1_N1NBIO0_IOAGR_SION_Client_RdRspPoolCredit_Alloc_1_ADDRESS 0x1e000884UL
#define SMN_IOHUB1_N1NBIO1_IOAGR_SION_Client_RdRspPoolCredit_Alloc_1_ADDRESS 0x1e200884UL
#define SMN_IOHUB1_N2NBIO0_IOAGR_SION_Client_RdRspPoolCredit_Alloc_1_ADDRESS 0x1e000c84UL
#define SMN_IOHUB1_N2NBIO1_IOAGR_SION_Client_RdRspPoolCredit_Alloc_1_ADDRESS 0x1e200c84UL
#define SMN_IOHUB2_N0NBIO0_IOAGR_SION_Client_RdRspPoolCredit_Alloc_1_ADDRESS 0x15c00484UL
#define SMN_IOHUB2_N0NBIO1_IOAGR_SION_Client_RdRspPoolCredit_Alloc_1_ADDRESS 0x15e00484UL
#define SMN_IOHUB2_N1NBIO0_IOAGR_SION_Client_RdRspPoolCredit_Alloc_1_ADDRESS 0x15c00884UL
#define SMN_IOHUB2_N1NBIO1_IOAGR_SION_Client_RdRspPoolCredit_Alloc_1_ADDRESS 0x15e00884UL
#define SMN_IOHUB2_N2NBIO0_IOAGR_SION_Client_RdRspPoolCredit_Alloc_1_ADDRESS 0x15c00c84UL
#define SMN_IOHUB2_N2NBIO1_IOAGR_SION_Client_RdRspPoolCredit_Alloc_1_ADDRESS 0x15e00c84UL
#define SMN_IOHUB2_N3NBIO0_IOAGR_SION_Client_RdRspPoolCredit_Alloc_1_ADDRESS 0x15c01084UL
#define SMN_IOHUB2_N3NBIO1_IOAGR_SION_Client_RdRspPoolCredit_Alloc_1_ADDRESS 0x15e01084UL
#define SMN_IOHUB2_N4NBIO0_IOAGR_SION_Client_RdRspPoolCredit_Alloc_1_ADDRESS 0x15c01484UL
#define SMN_IOHUB2_N4NBIO1_IOAGR_SION_Client_RdRspPoolCredit_Alloc_1_ADDRESS 0x15e01484UL
#define SMN_IOHUB2_N5NBIO0_IOAGR_SION_Client_RdRspPoolCredit_Alloc_1_ADDRESS 0x15c01884UL
#define SMN_IOHUB2_N5NBIO1_IOAGR_SION_Client_RdRspPoolCredit_Alloc_1_ADDRESS 0x15e01884UL
#define SMN_IOHUB3_N0NBIO0_IOAGR_SION_Client_RdRspPoolCredit_Alloc_1_ADDRESS 0x1e100484UL
#define SMN_IOHUB3_N0NBIO1_IOAGR_SION_Client_RdRspPoolCredit_Alloc_1_ADDRESS 0x1e300484UL
#define SMN_IOHUB3_N1NBIO0_IOAGR_SION_Client_RdRspPoolCredit_Alloc_1_ADDRESS 0x1e100884UL
#define SMN_IOHUB3_N1NBIO1_IOAGR_SION_Client_RdRspPoolCredit_Alloc_1_ADDRESS 0x1e300884UL
#define SMN_IOHUB3_N2NBIO0_IOAGR_SION_Client_RdRspPoolCredit_Alloc_1_ADDRESS 0x1e100c84UL
#define SMN_IOHUB3_N2NBIO1_IOAGR_SION_Client_RdRspPoolCredit_Alloc_1_ADDRESS 0x1e300c84UL


/***********************************************************
* Register Name : IOAGR_SION_Client_RdRspPoolCredit_Alloc_2
************************************************************/

#define IOAGR_SION_Client_RdRspPoolCredit_Alloc_2_IOAGR_SION_Client_RdRspPoolCredit_Alloc_2_OFFSET 0
#define IOAGR_SION_Client_RdRspPoolCredit_Alloc_2_IOAGR_SION_Client_RdRspPoolCredit_Alloc_2_MASK 0xffffffff

typedef union {
  struct {
    UINT32           IOAGR_SION_Client_RdRspPoolCredit_Alloc_2:32;
  } Field;
  UINT32 Value;
} IOAGR_SION_Client_RdRspPoolCredit_Alloc_2_STRUCT;

#define SMN_IOAGR_SION_Client_RdRspPoolCredit_Alloc_2_ADDRESS         0x15b00488UL
#define SMN_IOHUB0_N0NBIO0_IOAGR_SION_Client_RdRspPoolCredit_Alloc_2_ADDRESS 0x15b00488UL
#define SMN_IOHUB0_N0NBIO1_IOAGR_SION_Client_RdRspPoolCredit_Alloc_2_ADDRESS 0x15d00488UL
#define SMN_IOHUB0_N1NBIO0_IOAGR_SION_Client_RdRspPoolCredit_Alloc_2_ADDRESS 0x15b00888UL
#define SMN_IOHUB0_N1NBIO1_IOAGR_SION_Client_RdRspPoolCredit_Alloc_2_ADDRESS 0x15d00888UL
#define SMN_IOHUB0_N2NBIO0_IOAGR_SION_Client_RdRspPoolCredit_Alloc_2_ADDRESS 0x15b00c88UL
#define SMN_IOHUB0_N2NBIO1_IOAGR_SION_Client_RdRspPoolCredit_Alloc_2_ADDRESS 0x15d00c88UL
#define SMN_IOHUB0_N3NBIO0_IOAGR_SION_Client_RdRspPoolCredit_Alloc_2_ADDRESS 0x15b01088UL
#define SMN_IOHUB0_N3NBIO1_IOAGR_SION_Client_RdRspPoolCredit_Alloc_2_ADDRESS 0x15d01088UL
#define SMN_IOHUB0_N4NBIO0_IOAGR_SION_Client_RdRspPoolCredit_Alloc_2_ADDRESS 0x15b01488UL
#define SMN_IOHUB0_N4NBIO1_IOAGR_SION_Client_RdRspPoolCredit_Alloc_2_ADDRESS 0x15d01488UL
#define SMN_IOHUB0_N5NBIO0_IOAGR_SION_Client_RdRspPoolCredit_Alloc_2_ADDRESS 0x15b01888UL
#define SMN_IOHUB0_N5NBIO1_IOAGR_SION_Client_RdRspPoolCredit_Alloc_2_ADDRESS 0x15d01888UL
#define SMN_IOHUB1_N0NBIO0_IOAGR_SION_Client_RdRspPoolCredit_Alloc_2_ADDRESS 0x1e000488UL
#define SMN_IOHUB1_N0NBIO1_IOAGR_SION_Client_RdRspPoolCredit_Alloc_2_ADDRESS 0x1e200488UL
#define SMN_IOHUB1_N1NBIO0_IOAGR_SION_Client_RdRspPoolCredit_Alloc_2_ADDRESS 0x1e000888UL
#define SMN_IOHUB1_N1NBIO1_IOAGR_SION_Client_RdRspPoolCredit_Alloc_2_ADDRESS 0x1e200888UL
#define SMN_IOHUB1_N2NBIO0_IOAGR_SION_Client_RdRspPoolCredit_Alloc_2_ADDRESS 0x1e000c88UL
#define SMN_IOHUB1_N2NBIO1_IOAGR_SION_Client_RdRspPoolCredit_Alloc_2_ADDRESS 0x1e200c88UL
#define SMN_IOHUB2_N0NBIO0_IOAGR_SION_Client_RdRspPoolCredit_Alloc_2_ADDRESS 0x15c00488UL
#define SMN_IOHUB2_N0NBIO1_IOAGR_SION_Client_RdRspPoolCredit_Alloc_2_ADDRESS 0x15e00488UL
#define SMN_IOHUB2_N1NBIO0_IOAGR_SION_Client_RdRspPoolCredit_Alloc_2_ADDRESS 0x15c00888UL
#define SMN_IOHUB2_N1NBIO1_IOAGR_SION_Client_RdRspPoolCredit_Alloc_2_ADDRESS 0x15e00888UL
#define SMN_IOHUB2_N2NBIO0_IOAGR_SION_Client_RdRspPoolCredit_Alloc_2_ADDRESS 0x15c00c88UL
#define SMN_IOHUB2_N2NBIO1_IOAGR_SION_Client_RdRspPoolCredit_Alloc_2_ADDRESS 0x15e00c88UL
#define SMN_IOHUB2_N3NBIO0_IOAGR_SION_Client_RdRspPoolCredit_Alloc_2_ADDRESS 0x15c01088UL
#define SMN_IOHUB2_N3NBIO1_IOAGR_SION_Client_RdRspPoolCredit_Alloc_2_ADDRESS 0x15e01088UL
#define SMN_IOHUB2_N4NBIO0_IOAGR_SION_Client_RdRspPoolCredit_Alloc_2_ADDRESS 0x15c01488UL
#define SMN_IOHUB2_N4NBIO1_IOAGR_SION_Client_RdRspPoolCredit_Alloc_2_ADDRESS 0x15e01488UL
#define SMN_IOHUB2_N5NBIO0_IOAGR_SION_Client_RdRspPoolCredit_Alloc_2_ADDRESS 0x15c01888UL
#define SMN_IOHUB2_N5NBIO1_IOAGR_SION_Client_RdRspPoolCredit_Alloc_2_ADDRESS 0x15e01888UL
#define SMN_IOHUB3_N0NBIO0_IOAGR_SION_Client_RdRspPoolCredit_Alloc_2_ADDRESS 0x1e100488UL
#define SMN_IOHUB3_N0NBIO1_IOAGR_SION_Client_RdRspPoolCredit_Alloc_2_ADDRESS 0x1e300488UL
#define SMN_IOHUB3_N1NBIO0_IOAGR_SION_Client_RdRspPoolCredit_Alloc_2_ADDRESS 0x1e100888UL
#define SMN_IOHUB3_N1NBIO1_IOAGR_SION_Client_RdRspPoolCredit_Alloc_2_ADDRESS 0x1e300888UL
#define SMN_IOHUB3_N2NBIO0_IOAGR_SION_Client_RdRspPoolCredit_Alloc_2_ADDRESS 0x1e100c88UL
#define SMN_IOHUB3_N2NBIO1_IOAGR_SION_Client_RdRspPoolCredit_Alloc_2_ADDRESS 0x1e300c88UL


/***********************************************************
* Register Name : IOAGR_SION_Client_RdRspPoolCredit_Alloc_3
************************************************************/

#define IOAGR_SION_Client_RdRspPoolCredit_Alloc_3_IOAGR_SION_Client_RdRspPoolCredit_Alloc_3_OFFSET 0
#define IOAGR_SION_Client_RdRspPoolCredit_Alloc_3_IOAGR_SION_Client_RdRspPoolCredit_Alloc_3_MASK 0xffffffff

typedef union {
  struct {
    UINT32           IOAGR_SION_Client_RdRspPoolCredit_Alloc_3:32;
  } Field;
  UINT32 Value;
} IOAGR_SION_Client_RdRspPoolCredit_Alloc_3_STRUCT;

#define SMN_IOAGR_SION_Client_RdRspPoolCredit_Alloc_3_ADDRESS         0x15b0048cUL
#define SMN_IOHUB0_N0NBIO0_IOAGR_SION_Client_RdRspPoolCredit_Alloc_3_ADDRESS 0x15b0048cUL
#define SMN_IOHUB0_N0NBIO1_IOAGR_SION_Client_RdRspPoolCredit_Alloc_3_ADDRESS 0x15d0048cUL
#define SMN_IOHUB0_N1NBIO0_IOAGR_SION_Client_RdRspPoolCredit_Alloc_3_ADDRESS 0x15b0088cUL
#define SMN_IOHUB0_N1NBIO1_IOAGR_SION_Client_RdRspPoolCredit_Alloc_3_ADDRESS 0x15d0088cUL
#define SMN_IOHUB0_N2NBIO0_IOAGR_SION_Client_RdRspPoolCredit_Alloc_3_ADDRESS 0x15b00c8cUL
#define SMN_IOHUB0_N2NBIO1_IOAGR_SION_Client_RdRspPoolCredit_Alloc_3_ADDRESS 0x15d00c8cUL
#define SMN_IOHUB0_N3NBIO0_IOAGR_SION_Client_RdRspPoolCredit_Alloc_3_ADDRESS 0x15b0108cUL
#define SMN_IOHUB0_N3NBIO1_IOAGR_SION_Client_RdRspPoolCredit_Alloc_3_ADDRESS 0x15d0108cUL
#define SMN_IOHUB0_N4NBIO0_IOAGR_SION_Client_RdRspPoolCredit_Alloc_3_ADDRESS 0x15b0148cUL
#define SMN_IOHUB0_N4NBIO1_IOAGR_SION_Client_RdRspPoolCredit_Alloc_3_ADDRESS 0x15d0148cUL
#define SMN_IOHUB0_N5NBIO0_IOAGR_SION_Client_RdRspPoolCredit_Alloc_3_ADDRESS 0x15b0188cUL
#define SMN_IOHUB0_N5NBIO1_IOAGR_SION_Client_RdRspPoolCredit_Alloc_3_ADDRESS 0x15d0188cUL
#define SMN_IOHUB1_N0NBIO0_IOAGR_SION_Client_RdRspPoolCredit_Alloc_3_ADDRESS 0x1e00048cUL
#define SMN_IOHUB1_N0NBIO1_IOAGR_SION_Client_RdRspPoolCredit_Alloc_3_ADDRESS 0x1e20048cUL
#define SMN_IOHUB1_N1NBIO0_IOAGR_SION_Client_RdRspPoolCredit_Alloc_3_ADDRESS 0x1e00088cUL
#define SMN_IOHUB1_N1NBIO1_IOAGR_SION_Client_RdRspPoolCredit_Alloc_3_ADDRESS 0x1e20088cUL
#define SMN_IOHUB1_N2NBIO0_IOAGR_SION_Client_RdRspPoolCredit_Alloc_3_ADDRESS 0x1e000c8cUL
#define SMN_IOHUB1_N2NBIO1_IOAGR_SION_Client_RdRspPoolCredit_Alloc_3_ADDRESS 0x1e200c8cUL
#define SMN_IOHUB2_N0NBIO0_IOAGR_SION_Client_RdRspPoolCredit_Alloc_3_ADDRESS 0x15c0048cUL
#define SMN_IOHUB2_N0NBIO1_IOAGR_SION_Client_RdRspPoolCredit_Alloc_3_ADDRESS 0x15e0048cUL
#define SMN_IOHUB2_N1NBIO0_IOAGR_SION_Client_RdRspPoolCredit_Alloc_3_ADDRESS 0x15c0088cUL
#define SMN_IOHUB2_N1NBIO1_IOAGR_SION_Client_RdRspPoolCredit_Alloc_3_ADDRESS 0x15e0088cUL
#define SMN_IOHUB2_N2NBIO0_IOAGR_SION_Client_RdRspPoolCredit_Alloc_3_ADDRESS 0x15c00c8cUL
#define SMN_IOHUB2_N2NBIO1_IOAGR_SION_Client_RdRspPoolCredit_Alloc_3_ADDRESS 0x15e00c8cUL
#define SMN_IOHUB2_N3NBIO0_IOAGR_SION_Client_RdRspPoolCredit_Alloc_3_ADDRESS 0x15c0108cUL
#define SMN_IOHUB2_N3NBIO1_IOAGR_SION_Client_RdRspPoolCredit_Alloc_3_ADDRESS 0x15e0108cUL
#define SMN_IOHUB2_N4NBIO0_IOAGR_SION_Client_RdRspPoolCredit_Alloc_3_ADDRESS 0x15c0148cUL
#define SMN_IOHUB2_N4NBIO1_IOAGR_SION_Client_RdRspPoolCredit_Alloc_3_ADDRESS 0x15e0148cUL
#define SMN_IOHUB2_N5NBIO0_IOAGR_SION_Client_RdRspPoolCredit_Alloc_3_ADDRESS 0x15c0188cUL
#define SMN_IOHUB2_N5NBIO1_IOAGR_SION_Client_RdRspPoolCredit_Alloc_3_ADDRESS 0x15e0188cUL
#define SMN_IOHUB3_N0NBIO0_IOAGR_SION_Client_RdRspPoolCredit_Alloc_3_ADDRESS 0x1e10048cUL
#define SMN_IOHUB3_N0NBIO1_IOAGR_SION_Client_RdRspPoolCredit_Alloc_3_ADDRESS 0x1e30048cUL
#define SMN_IOHUB3_N1NBIO0_IOAGR_SION_Client_RdRspPoolCredit_Alloc_3_ADDRESS 0x1e10088cUL
#define SMN_IOHUB3_N1NBIO1_IOAGR_SION_Client_RdRspPoolCredit_Alloc_3_ADDRESS 0x1e30088cUL
#define SMN_IOHUB3_N2NBIO0_IOAGR_SION_Client_RdRspPoolCredit_Alloc_3_ADDRESS 0x1e100c8cUL
#define SMN_IOHUB3_N2NBIO1_IOAGR_SION_Client_RdRspPoolCredit_Alloc_3_ADDRESS 0x1e300c8cUL


/***********************************************************
* Register Name : IOAGR_SION_Client_ReqPoolCredit_Alloc_0
************************************************************/

#define IOAGR_SION_Client_ReqPoolCredit_Alloc_0_IOAGR_SION_Client_ReqPoolCredit_Alloc_0_OFFSET 0
#define IOAGR_SION_Client_ReqPoolCredit_Alloc_0_IOAGR_SION_Client_ReqPoolCredit_Alloc_0_MASK 0xffffffff

typedef union {
  struct {
    UINT32             IOAGR_SION_Client_ReqPoolCredit_Alloc_0:32;
  } Field;
  UINT32 Value;
} IOAGR_SION_Client_ReqPoolCredit_Alloc_0_STRUCT;

#define SMN_IOAGR_SION_Client_ReqPoolCredit_Alloc_0_ADDRESS           0x15b00460UL
#define SMN_IOHUB0_N0NBIO0_IOAGR_SION_Client_ReqPoolCredit_Alloc_0_ADDRESS 0x15b00460UL
#define SMN_IOHUB0_N0NBIO1_IOAGR_SION_Client_ReqPoolCredit_Alloc_0_ADDRESS 0x15d00460UL
#define SMN_IOHUB0_N1NBIO0_IOAGR_SION_Client_ReqPoolCredit_Alloc_0_ADDRESS 0x15b00860UL
#define SMN_IOHUB0_N1NBIO1_IOAGR_SION_Client_ReqPoolCredit_Alloc_0_ADDRESS 0x15d00860UL
#define SMN_IOHUB0_N2NBIO0_IOAGR_SION_Client_ReqPoolCredit_Alloc_0_ADDRESS 0x15b00c60UL
#define SMN_IOHUB0_N2NBIO1_IOAGR_SION_Client_ReqPoolCredit_Alloc_0_ADDRESS 0x15d00c60UL
#define SMN_IOHUB0_N3NBIO0_IOAGR_SION_Client_ReqPoolCredit_Alloc_0_ADDRESS 0x15b01060UL
#define SMN_IOHUB0_N3NBIO1_IOAGR_SION_Client_ReqPoolCredit_Alloc_0_ADDRESS 0x15d01060UL
#define SMN_IOHUB0_N4NBIO0_IOAGR_SION_Client_ReqPoolCredit_Alloc_0_ADDRESS 0x15b01460UL
#define SMN_IOHUB0_N4NBIO1_IOAGR_SION_Client_ReqPoolCredit_Alloc_0_ADDRESS 0x15d01460UL
#define SMN_IOHUB0_N5NBIO0_IOAGR_SION_Client_ReqPoolCredit_Alloc_0_ADDRESS 0x15b01860UL
#define SMN_IOHUB0_N5NBIO1_IOAGR_SION_Client_ReqPoolCredit_Alloc_0_ADDRESS 0x15d01860UL
#define SMN_IOHUB1_N0NBIO0_IOAGR_SION_Client_ReqPoolCredit_Alloc_0_ADDRESS 0x1e000460UL
#define SMN_IOHUB1_N0NBIO1_IOAGR_SION_Client_ReqPoolCredit_Alloc_0_ADDRESS 0x1e200460UL
#define SMN_IOHUB1_N1NBIO0_IOAGR_SION_Client_ReqPoolCredit_Alloc_0_ADDRESS 0x1e000860UL
#define SMN_IOHUB1_N1NBIO1_IOAGR_SION_Client_ReqPoolCredit_Alloc_0_ADDRESS 0x1e200860UL
#define SMN_IOHUB1_N2NBIO0_IOAGR_SION_Client_ReqPoolCredit_Alloc_0_ADDRESS 0x1e000c60UL
#define SMN_IOHUB1_N2NBIO1_IOAGR_SION_Client_ReqPoolCredit_Alloc_0_ADDRESS 0x1e200c60UL
#define SMN_IOHUB2_N0NBIO0_IOAGR_SION_Client_ReqPoolCredit_Alloc_0_ADDRESS 0x15c00460UL
#define SMN_IOHUB2_N0NBIO1_IOAGR_SION_Client_ReqPoolCredit_Alloc_0_ADDRESS 0x15e00460UL
#define SMN_IOHUB2_N1NBIO0_IOAGR_SION_Client_ReqPoolCredit_Alloc_0_ADDRESS 0x15c00860UL
#define SMN_IOHUB2_N1NBIO1_IOAGR_SION_Client_ReqPoolCredit_Alloc_0_ADDRESS 0x15e00860UL
#define SMN_IOHUB2_N2NBIO0_IOAGR_SION_Client_ReqPoolCredit_Alloc_0_ADDRESS 0x15c00c60UL
#define SMN_IOHUB2_N2NBIO1_IOAGR_SION_Client_ReqPoolCredit_Alloc_0_ADDRESS 0x15e00c60UL
#define SMN_IOHUB2_N3NBIO0_IOAGR_SION_Client_ReqPoolCredit_Alloc_0_ADDRESS 0x15c01060UL
#define SMN_IOHUB2_N3NBIO1_IOAGR_SION_Client_ReqPoolCredit_Alloc_0_ADDRESS 0x15e01060UL
#define SMN_IOHUB2_N4NBIO0_IOAGR_SION_Client_ReqPoolCredit_Alloc_0_ADDRESS 0x15c01460UL
#define SMN_IOHUB2_N4NBIO1_IOAGR_SION_Client_ReqPoolCredit_Alloc_0_ADDRESS 0x15e01460UL
#define SMN_IOHUB2_N5NBIO0_IOAGR_SION_Client_ReqPoolCredit_Alloc_0_ADDRESS 0x15c01860UL
#define SMN_IOHUB2_N5NBIO1_IOAGR_SION_Client_ReqPoolCredit_Alloc_0_ADDRESS 0x15e01860UL
#define SMN_IOHUB3_N0NBIO0_IOAGR_SION_Client_ReqPoolCredit_Alloc_0_ADDRESS 0x1e100460UL
#define SMN_IOHUB3_N0NBIO1_IOAGR_SION_Client_ReqPoolCredit_Alloc_0_ADDRESS 0x1e300460UL
#define SMN_IOHUB3_N1NBIO0_IOAGR_SION_Client_ReqPoolCredit_Alloc_0_ADDRESS 0x1e100860UL
#define SMN_IOHUB3_N1NBIO1_IOAGR_SION_Client_ReqPoolCredit_Alloc_0_ADDRESS 0x1e300860UL
#define SMN_IOHUB3_N2NBIO0_IOAGR_SION_Client_ReqPoolCredit_Alloc_0_ADDRESS 0x1e100c60UL
#define SMN_IOHUB3_N2NBIO1_IOAGR_SION_Client_ReqPoolCredit_Alloc_0_ADDRESS 0x1e300c60UL


/***********************************************************
* Register Name : IOAGR_SION_Client_ReqPoolCredit_Alloc_1
************************************************************/

#define IOAGR_SION_Client_ReqPoolCredit_Alloc_1_IOAGR_SION_Client_ReqPoolCredit_Alloc_1_OFFSET 0
#define IOAGR_SION_Client_ReqPoolCredit_Alloc_1_IOAGR_SION_Client_ReqPoolCredit_Alloc_1_MASK 0xffffffff

typedef union {
  struct {
    UINT32             IOAGR_SION_Client_ReqPoolCredit_Alloc_1:32;
  } Field;
  UINT32 Value;
} IOAGR_SION_Client_ReqPoolCredit_Alloc_1_STRUCT;

#define SMN_IOAGR_SION_Client_ReqPoolCredit_Alloc_1_ADDRESS           0x15b00464UL
#define SMN_IOHUB0_N0NBIO0_IOAGR_SION_Client_ReqPoolCredit_Alloc_1_ADDRESS 0x15b00464UL
#define SMN_IOHUB0_N0NBIO1_IOAGR_SION_Client_ReqPoolCredit_Alloc_1_ADDRESS 0x15d00464UL
#define SMN_IOHUB0_N1NBIO0_IOAGR_SION_Client_ReqPoolCredit_Alloc_1_ADDRESS 0x15b00864UL
#define SMN_IOHUB0_N1NBIO1_IOAGR_SION_Client_ReqPoolCredit_Alloc_1_ADDRESS 0x15d00864UL
#define SMN_IOHUB0_N2NBIO0_IOAGR_SION_Client_ReqPoolCredit_Alloc_1_ADDRESS 0x15b00c64UL
#define SMN_IOHUB0_N2NBIO1_IOAGR_SION_Client_ReqPoolCredit_Alloc_1_ADDRESS 0x15d00c64UL
#define SMN_IOHUB0_N3NBIO0_IOAGR_SION_Client_ReqPoolCredit_Alloc_1_ADDRESS 0x15b01064UL
#define SMN_IOHUB0_N3NBIO1_IOAGR_SION_Client_ReqPoolCredit_Alloc_1_ADDRESS 0x15d01064UL
#define SMN_IOHUB0_N4NBIO0_IOAGR_SION_Client_ReqPoolCredit_Alloc_1_ADDRESS 0x15b01464UL
#define SMN_IOHUB0_N4NBIO1_IOAGR_SION_Client_ReqPoolCredit_Alloc_1_ADDRESS 0x15d01464UL
#define SMN_IOHUB0_N5NBIO0_IOAGR_SION_Client_ReqPoolCredit_Alloc_1_ADDRESS 0x15b01864UL
#define SMN_IOHUB0_N5NBIO1_IOAGR_SION_Client_ReqPoolCredit_Alloc_1_ADDRESS 0x15d01864UL
#define SMN_IOHUB1_N0NBIO0_IOAGR_SION_Client_ReqPoolCredit_Alloc_1_ADDRESS 0x1e000464UL
#define SMN_IOHUB1_N0NBIO1_IOAGR_SION_Client_ReqPoolCredit_Alloc_1_ADDRESS 0x1e200464UL
#define SMN_IOHUB1_N1NBIO0_IOAGR_SION_Client_ReqPoolCredit_Alloc_1_ADDRESS 0x1e000864UL
#define SMN_IOHUB1_N1NBIO1_IOAGR_SION_Client_ReqPoolCredit_Alloc_1_ADDRESS 0x1e200864UL
#define SMN_IOHUB1_N2NBIO0_IOAGR_SION_Client_ReqPoolCredit_Alloc_1_ADDRESS 0x1e000c64UL
#define SMN_IOHUB1_N2NBIO1_IOAGR_SION_Client_ReqPoolCredit_Alloc_1_ADDRESS 0x1e200c64UL
#define SMN_IOHUB2_N0NBIO0_IOAGR_SION_Client_ReqPoolCredit_Alloc_1_ADDRESS 0x15c00464UL
#define SMN_IOHUB2_N0NBIO1_IOAGR_SION_Client_ReqPoolCredit_Alloc_1_ADDRESS 0x15e00464UL
#define SMN_IOHUB2_N1NBIO0_IOAGR_SION_Client_ReqPoolCredit_Alloc_1_ADDRESS 0x15c00864UL
#define SMN_IOHUB2_N1NBIO1_IOAGR_SION_Client_ReqPoolCredit_Alloc_1_ADDRESS 0x15e00864UL
#define SMN_IOHUB2_N2NBIO0_IOAGR_SION_Client_ReqPoolCredit_Alloc_1_ADDRESS 0x15c00c64UL
#define SMN_IOHUB2_N2NBIO1_IOAGR_SION_Client_ReqPoolCredit_Alloc_1_ADDRESS 0x15e00c64UL
#define SMN_IOHUB2_N3NBIO0_IOAGR_SION_Client_ReqPoolCredit_Alloc_1_ADDRESS 0x15c01064UL
#define SMN_IOHUB2_N3NBIO1_IOAGR_SION_Client_ReqPoolCredit_Alloc_1_ADDRESS 0x15e01064UL
#define SMN_IOHUB2_N4NBIO0_IOAGR_SION_Client_ReqPoolCredit_Alloc_1_ADDRESS 0x15c01464UL
#define SMN_IOHUB2_N4NBIO1_IOAGR_SION_Client_ReqPoolCredit_Alloc_1_ADDRESS 0x15e01464UL
#define SMN_IOHUB2_N5NBIO0_IOAGR_SION_Client_ReqPoolCredit_Alloc_1_ADDRESS 0x15c01864UL
#define SMN_IOHUB2_N5NBIO1_IOAGR_SION_Client_ReqPoolCredit_Alloc_1_ADDRESS 0x15e01864UL
#define SMN_IOHUB3_N0NBIO0_IOAGR_SION_Client_ReqPoolCredit_Alloc_1_ADDRESS 0x1e100464UL
#define SMN_IOHUB3_N0NBIO1_IOAGR_SION_Client_ReqPoolCredit_Alloc_1_ADDRESS 0x1e300464UL
#define SMN_IOHUB3_N1NBIO0_IOAGR_SION_Client_ReqPoolCredit_Alloc_1_ADDRESS 0x1e100864UL
#define SMN_IOHUB3_N1NBIO1_IOAGR_SION_Client_ReqPoolCredit_Alloc_1_ADDRESS 0x1e300864UL
#define SMN_IOHUB3_N2NBIO0_IOAGR_SION_Client_ReqPoolCredit_Alloc_1_ADDRESS 0x1e100c64UL
#define SMN_IOHUB3_N2NBIO1_IOAGR_SION_Client_ReqPoolCredit_Alloc_1_ADDRESS 0x1e300c64UL


/***********************************************************
* Register Name : IOAGR_SION_Client_ReqPoolCredit_Alloc_2
************************************************************/

#define IOAGR_SION_Client_ReqPoolCredit_Alloc_2_IOAGR_SION_Client_ReqPoolCredit_Alloc_2_OFFSET 0
#define IOAGR_SION_Client_ReqPoolCredit_Alloc_2_IOAGR_SION_Client_ReqPoolCredit_Alloc_2_MASK 0xffffffff

typedef union {
  struct {
    UINT32             IOAGR_SION_Client_ReqPoolCredit_Alloc_2:32;
  } Field;
  UINT32 Value;
} IOAGR_SION_Client_ReqPoolCredit_Alloc_2_STRUCT;

#define SMN_IOAGR_SION_Client_ReqPoolCredit_Alloc_2_ADDRESS           0x15b00468UL
#define SMN_IOHUB0_N0NBIO0_IOAGR_SION_Client_ReqPoolCredit_Alloc_2_ADDRESS 0x15b00468UL
#define SMN_IOHUB0_N0NBIO1_IOAGR_SION_Client_ReqPoolCredit_Alloc_2_ADDRESS 0x15d00468UL
#define SMN_IOHUB0_N1NBIO0_IOAGR_SION_Client_ReqPoolCredit_Alloc_2_ADDRESS 0x15b00868UL
#define SMN_IOHUB0_N1NBIO1_IOAGR_SION_Client_ReqPoolCredit_Alloc_2_ADDRESS 0x15d00868UL
#define SMN_IOHUB0_N2NBIO0_IOAGR_SION_Client_ReqPoolCredit_Alloc_2_ADDRESS 0x15b00c68UL
#define SMN_IOHUB0_N2NBIO1_IOAGR_SION_Client_ReqPoolCredit_Alloc_2_ADDRESS 0x15d00c68UL
#define SMN_IOHUB0_N3NBIO0_IOAGR_SION_Client_ReqPoolCredit_Alloc_2_ADDRESS 0x15b01068UL
#define SMN_IOHUB0_N3NBIO1_IOAGR_SION_Client_ReqPoolCredit_Alloc_2_ADDRESS 0x15d01068UL
#define SMN_IOHUB0_N4NBIO0_IOAGR_SION_Client_ReqPoolCredit_Alloc_2_ADDRESS 0x15b01468UL
#define SMN_IOHUB0_N4NBIO1_IOAGR_SION_Client_ReqPoolCredit_Alloc_2_ADDRESS 0x15d01468UL
#define SMN_IOHUB0_N5NBIO0_IOAGR_SION_Client_ReqPoolCredit_Alloc_2_ADDRESS 0x15b01868UL
#define SMN_IOHUB0_N5NBIO1_IOAGR_SION_Client_ReqPoolCredit_Alloc_2_ADDRESS 0x15d01868UL
#define SMN_IOHUB1_N0NBIO0_IOAGR_SION_Client_ReqPoolCredit_Alloc_2_ADDRESS 0x1e000468UL
#define SMN_IOHUB1_N0NBIO1_IOAGR_SION_Client_ReqPoolCredit_Alloc_2_ADDRESS 0x1e200468UL
#define SMN_IOHUB1_N1NBIO0_IOAGR_SION_Client_ReqPoolCredit_Alloc_2_ADDRESS 0x1e000868UL
#define SMN_IOHUB1_N1NBIO1_IOAGR_SION_Client_ReqPoolCredit_Alloc_2_ADDRESS 0x1e200868UL
#define SMN_IOHUB1_N2NBIO0_IOAGR_SION_Client_ReqPoolCredit_Alloc_2_ADDRESS 0x1e000c68UL
#define SMN_IOHUB1_N2NBIO1_IOAGR_SION_Client_ReqPoolCredit_Alloc_2_ADDRESS 0x1e200c68UL
#define SMN_IOHUB2_N0NBIO0_IOAGR_SION_Client_ReqPoolCredit_Alloc_2_ADDRESS 0x15c00468UL
#define SMN_IOHUB2_N0NBIO1_IOAGR_SION_Client_ReqPoolCredit_Alloc_2_ADDRESS 0x15e00468UL
#define SMN_IOHUB2_N1NBIO0_IOAGR_SION_Client_ReqPoolCredit_Alloc_2_ADDRESS 0x15c00868UL
#define SMN_IOHUB2_N1NBIO1_IOAGR_SION_Client_ReqPoolCredit_Alloc_2_ADDRESS 0x15e00868UL
#define SMN_IOHUB2_N2NBIO0_IOAGR_SION_Client_ReqPoolCredit_Alloc_2_ADDRESS 0x15c00c68UL
#define SMN_IOHUB2_N2NBIO1_IOAGR_SION_Client_ReqPoolCredit_Alloc_2_ADDRESS 0x15e00c68UL
#define SMN_IOHUB2_N3NBIO0_IOAGR_SION_Client_ReqPoolCredit_Alloc_2_ADDRESS 0x15c01068UL
#define SMN_IOHUB2_N3NBIO1_IOAGR_SION_Client_ReqPoolCredit_Alloc_2_ADDRESS 0x15e01068UL
#define SMN_IOHUB2_N4NBIO0_IOAGR_SION_Client_ReqPoolCredit_Alloc_2_ADDRESS 0x15c01468UL
#define SMN_IOHUB2_N4NBIO1_IOAGR_SION_Client_ReqPoolCredit_Alloc_2_ADDRESS 0x15e01468UL
#define SMN_IOHUB2_N5NBIO0_IOAGR_SION_Client_ReqPoolCredit_Alloc_2_ADDRESS 0x15c01868UL
#define SMN_IOHUB2_N5NBIO1_IOAGR_SION_Client_ReqPoolCredit_Alloc_2_ADDRESS 0x15e01868UL
#define SMN_IOHUB3_N0NBIO0_IOAGR_SION_Client_ReqPoolCredit_Alloc_2_ADDRESS 0x1e100468UL
#define SMN_IOHUB3_N0NBIO1_IOAGR_SION_Client_ReqPoolCredit_Alloc_2_ADDRESS 0x1e300468UL
#define SMN_IOHUB3_N1NBIO0_IOAGR_SION_Client_ReqPoolCredit_Alloc_2_ADDRESS 0x1e100868UL
#define SMN_IOHUB3_N1NBIO1_IOAGR_SION_Client_ReqPoolCredit_Alloc_2_ADDRESS 0x1e300868UL
#define SMN_IOHUB3_N2NBIO0_IOAGR_SION_Client_ReqPoolCredit_Alloc_2_ADDRESS 0x1e100c68UL
#define SMN_IOHUB3_N2NBIO1_IOAGR_SION_Client_ReqPoolCredit_Alloc_2_ADDRESS 0x1e300c68UL


/***********************************************************
* Register Name : IOAGR_SION_Client_ReqPoolCredit_Alloc_3
************************************************************/

#define IOAGR_SION_Client_ReqPoolCredit_Alloc_3_IOAGR_SION_Client_ReqPoolCredit_Alloc_3_OFFSET 0
#define IOAGR_SION_Client_ReqPoolCredit_Alloc_3_IOAGR_SION_Client_ReqPoolCredit_Alloc_3_MASK 0xffffffff

typedef union {
  struct {
    UINT32             IOAGR_SION_Client_ReqPoolCredit_Alloc_3:32;
  } Field;
  UINT32 Value;
} IOAGR_SION_Client_ReqPoolCredit_Alloc_3_STRUCT;

#define SMN_IOAGR_SION_Client_ReqPoolCredit_Alloc_3_ADDRESS           0x15b0046cUL
#define SMN_IOHUB0_N0NBIO0_IOAGR_SION_Client_ReqPoolCredit_Alloc_3_ADDRESS 0x15b0046cUL
#define SMN_IOHUB0_N0NBIO1_IOAGR_SION_Client_ReqPoolCredit_Alloc_3_ADDRESS 0x15d0046cUL
#define SMN_IOHUB0_N1NBIO0_IOAGR_SION_Client_ReqPoolCredit_Alloc_3_ADDRESS 0x15b0086cUL
#define SMN_IOHUB0_N1NBIO1_IOAGR_SION_Client_ReqPoolCredit_Alloc_3_ADDRESS 0x15d0086cUL
#define SMN_IOHUB0_N2NBIO0_IOAGR_SION_Client_ReqPoolCredit_Alloc_3_ADDRESS 0x15b00c6cUL
#define SMN_IOHUB0_N2NBIO1_IOAGR_SION_Client_ReqPoolCredit_Alloc_3_ADDRESS 0x15d00c6cUL
#define SMN_IOHUB0_N3NBIO0_IOAGR_SION_Client_ReqPoolCredit_Alloc_3_ADDRESS 0x15b0106cUL
#define SMN_IOHUB0_N3NBIO1_IOAGR_SION_Client_ReqPoolCredit_Alloc_3_ADDRESS 0x15d0106cUL
#define SMN_IOHUB0_N4NBIO0_IOAGR_SION_Client_ReqPoolCredit_Alloc_3_ADDRESS 0x15b0146cUL
#define SMN_IOHUB0_N4NBIO1_IOAGR_SION_Client_ReqPoolCredit_Alloc_3_ADDRESS 0x15d0146cUL
#define SMN_IOHUB0_N5NBIO0_IOAGR_SION_Client_ReqPoolCredit_Alloc_3_ADDRESS 0x15b0186cUL
#define SMN_IOHUB0_N5NBIO1_IOAGR_SION_Client_ReqPoolCredit_Alloc_3_ADDRESS 0x15d0186cUL
#define SMN_IOHUB1_N0NBIO0_IOAGR_SION_Client_ReqPoolCredit_Alloc_3_ADDRESS 0x1e00046cUL
#define SMN_IOHUB1_N0NBIO1_IOAGR_SION_Client_ReqPoolCredit_Alloc_3_ADDRESS 0x1e20046cUL
#define SMN_IOHUB1_N1NBIO0_IOAGR_SION_Client_ReqPoolCredit_Alloc_3_ADDRESS 0x1e00086cUL
#define SMN_IOHUB1_N1NBIO1_IOAGR_SION_Client_ReqPoolCredit_Alloc_3_ADDRESS 0x1e20086cUL
#define SMN_IOHUB1_N2NBIO0_IOAGR_SION_Client_ReqPoolCredit_Alloc_3_ADDRESS 0x1e000c6cUL
#define SMN_IOHUB1_N2NBIO1_IOAGR_SION_Client_ReqPoolCredit_Alloc_3_ADDRESS 0x1e200c6cUL
#define SMN_IOHUB2_N0NBIO0_IOAGR_SION_Client_ReqPoolCredit_Alloc_3_ADDRESS 0x15c0046cUL
#define SMN_IOHUB2_N0NBIO1_IOAGR_SION_Client_ReqPoolCredit_Alloc_3_ADDRESS 0x15e0046cUL
#define SMN_IOHUB2_N1NBIO0_IOAGR_SION_Client_ReqPoolCredit_Alloc_3_ADDRESS 0x15c0086cUL
#define SMN_IOHUB2_N1NBIO1_IOAGR_SION_Client_ReqPoolCredit_Alloc_3_ADDRESS 0x15e0086cUL
#define SMN_IOHUB2_N2NBIO0_IOAGR_SION_Client_ReqPoolCredit_Alloc_3_ADDRESS 0x15c00c6cUL
#define SMN_IOHUB2_N2NBIO1_IOAGR_SION_Client_ReqPoolCredit_Alloc_3_ADDRESS 0x15e00c6cUL
#define SMN_IOHUB2_N3NBIO0_IOAGR_SION_Client_ReqPoolCredit_Alloc_3_ADDRESS 0x15c0106cUL
#define SMN_IOHUB2_N3NBIO1_IOAGR_SION_Client_ReqPoolCredit_Alloc_3_ADDRESS 0x15e0106cUL
#define SMN_IOHUB2_N4NBIO0_IOAGR_SION_Client_ReqPoolCredit_Alloc_3_ADDRESS 0x15c0146cUL
#define SMN_IOHUB2_N4NBIO1_IOAGR_SION_Client_ReqPoolCredit_Alloc_3_ADDRESS 0x15e0146cUL
#define SMN_IOHUB2_N5NBIO0_IOAGR_SION_Client_ReqPoolCredit_Alloc_3_ADDRESS 0x15c0186cUL
#define SMN_IOHUB2_N5NBIO1_IOAGR_SION_Client_ReqPoolCredit_Alloc_3_ADDRESS 0x15e0186cUL
#define SMN_IOHUB3_N0NBIO0_IOAGR_SION_Client_ReqPoolCredit_Alloc_3_ADDRESS 0x1e10046cUL
#define SMN_IOHUB3_N0NBIO1_IOAGR_SION_Client_ReqPoolCredit_Alloc_3_ADDRESS 0x1e30046cUL
#define SMN_IOHUB3_N1NBIO0_IOAGR_SION_Client_ReqPoolCredit_Alloc_3_ADDRESS 0x1e10086cUL
#define SMN_IOHUB3_N1NBIO1_IOAGR_SION_Client_ReqPoolCredit_Alloc_3_ADDRESS 0x1e30086cUL
#define SMN_IOHUB3_N2NBIO0_IOAGR_SION_Client_ReqPoolCredit_Alloc_3_ADDRESS 0x1e100c6cUL
#define SMN_IOHUB3_N2NBIO1_IOAGR_SION_Client_ReqPoolCredit_Alloc_3_ADDRESS 0x1e300c6cUL


/***********************************************************
* Register Name : IOAGR_SION_Client_WrRspPoolCredit_Alloc_0
************************************************************/

#define IOAGR_SION_Client_WrRspPoolCredit_Alloc_0_IOAGR_SION_Client_WrRspPoolCredit_Alloc_0_OFFSET 0
#define IOAGR_SION_Client_WrRspPoolCredit_Alloc_0_IOAGR_SION_Client_WrRspPoolCredit_Alloc_0_MASK 0xffffffff

typedef union {
  struct {
    UINT32           IOAGR_SION_Client_WrRspPoolCredit_Alloc_0:32;
  } Field;
  UINT32 Value;
} IOAGR_SION_Client_WrRspPoolCredit_Alloc_0_STRUCT;

#define SMN_IOAGR_SION_Client_WrRspPoolCredit_Alloc_0_ADDRESS         0x15b00490UL
#define SMN_IOHUB0_N0NBIO0_IOAGR_SION_Client_WrRspPoolCredit_Alloc_0_ADDRESS 0x15b00490UL
#define SMN_IOHUB0_N0NBIO1_IOAGR_SION_Client_WrRspPoolCredit_Alloc_0_ADDRESS 0x15d00490UL
#define SMN_IOHUB0_N1NBIO0_IOAGR_SION_Client_WrRspPoolCredit_Alloc_0_ADDRESS 0x15b00890UL
#define SMN_IOHUB0_N1NBIO1_IOAGR_SION_Client_WrRspPoolCredit_Alloc_0_ADDRESS 0x15d00890UL
#define SMN_IOHUB0_N2NBIO0_IOAGR_SION_Client_WrRspPoolCredit_Alloc_0_ADDRESS 0x15b00c90UL
#define SMN_IOHUB0_N2NBIO1_IOAGR_SION_Client_WrRspPoolCredit_Alloc_0_ADDRESS 0x15d00c90UL
#define SMN_IOHUB0_N3NBIO0_IOAGR_SION_Client_WrRspPoolCredit_Alloc_0_ADDRESS 0x15b01090UL
#define SMN_IOHUB0_N3NBIO1_IOAGR_SION_Client_WrRspPoolCredit_Alloc_0_ADDRESS 0x15d01090UL
#define SMN_IOHUB0_N4NBIO0_IOAGR_SION_Client_WrRspPoolCredit_Alloc_0_ADDRESS 0x15b01490UL
#define SMN_IOHUB0_N4NBIO1_IOAGR_SION_Client_WrRspPoolCredit_Alloc_0_ADDRESS 0x15d01490UL
#define SMN_IOHUB0_N5NBIO0_IOAGR_SION_Client_WrRspPoolCredit_Alloc_0_ADDRESS 0x15b01890UL
#define SMN_IOHUB0_N5NBIO1_IOAGR_SION_Client_WrRspPoolCredit_Alloc_0_ADDRESS 0x15d01890UL
#define SMN_IOHUB1_N0NBIO0_IOAGR_SION_Client_WrRspPoolCredit_Alloc_0_ADDRESS 0x1e000490UL
#define SMN_IOHUB1_N0NBIO1_IOAGR_SION_Client_WrRspPoolCredit_Alloc_0_ADDRESS 0x1e200490UL
#define SMN_IOHUB1_N1NBIO0_IOAGR_SION_Client_WrRspPoolCredit_Alloc_0_ADDRESS 0x1e000890UL
#define SMN_IOHUB1_N1NBIO1_IOAGR_SION_Client_WrRspPoolCredit_Alloc_0_ADDRESS 0x1e200890UL
#define SMN_IOHUB1_N2NBIO0_IOAGR_SION_Client_WrRspPoolCredit_Alloc_0_ADDRESS 0x1e000c90UL
#define SMN_IOHUB1_N2NBIO1_IOAGR_SION_Client_WrRspPoolCredit_Alloc_0_ADDRESS 0x1e200c90UL
#define SMN_IOHUB2_N0NBIO0_IOAGR_SION_Client_WrRspPoolCredit_Alloc_0_ADDRESS 0x15c00490UL
#define SMN_IOHUB2_N0NBIO1_IOAGR_SION_Client_WrRspPoolCredit_Alloc_0_ADDRESS 0x15e00490UL
#define SMN_IOHUB2_N1NBIO0_IOAGR_SION_Client_WrRspPoolCredit_Alloc_0_ADDRESS 0x15c00890UL
#define SMN_IOHUB2_N1NBIO1_IOAGR_SION_Client_WrRspPoolCredit_Alloc_0_ADDRESS 0x15e00890UL
#define SMN_IOHUB2_N2NBIO0_IOAGR_SION_Client_WrRspPoolCredit_Alloc_0_ADDRESS 0x15c00c90UL
#define SMN_IOHUB2_N2NBIO1_IOAGR_SION_Client_WrRspPoolCredit_Alloc_0_ADDRESS 0x15e00c90UL
#define SMN_IOHUB2_N3NBIO0_IOAGR_SION_Client_WrRspPoolCredit_Alloc_0_ADDRESS 0x15c01090UL
#define SMN_IOHUB2_N3NBIO1_IOAGR_SION_Client_WrRspPoolCredit_Alloc_0_ADDRESS 0x15e01090UL
#define SMN_IOHUB2_N4NBIO0_IOAGR_SION_Client_WrRspPoolCredit_Alloc_0_ADDRESS 0x15c01490UL
#define SMN_IOHUB2_N4NBIO1_IOAGR_SION_Client_WrRspPoolCredit_Alloc_0_ADDRESS 0x15e01490UL
#define SMN_IOHUB2_N5NBIO0_IOAGR_SION_Client_WrRspPoolCredit_Alloc_0_ADDRESS 0x15c01890UL
#define SMN_IOHUB2_N5NBIO1_IOAGR_SION_Client_WrRspPoolCredit_Alloc_0_ADDRESS 0x15e01890UL
#define SMN_IOHUB3_N0NBIO0_IOAGR_SION_Client_WrRspPoolCredit_Alloc_0_ADDRESS 0x1e100490UL
#define SMN_IOHUB3_N0NBIO1_IOAGR_SION_Client_WrRspPoolCredit_Alloc_0_ADDRESS 0x1e300490UL
#define SMN_IOHUB3_N1NBIO0_IOAGR_SION_Client_WrRspPoolCredit_Alloc_0_ADDRESS 0x1e100890UL
#define SMN_IOHUB3_N1NBIO1_IOAGR_SION_Client_WrRspPoolCredit_Alloc_0_ADDRESS 0x1e300890UL
#define SMN_IOHUB3_N2NBIO0_IOAGR_SION_Client_WrRspPoolCredit_Alloc_0_ADDRESS 0x1e100c90UL
#define SMN_IOHUB3_N2NBIO1_IOAGR_SION_Client_WrRspPoolCredit_Alloc_0_ADDRESS 0x1e300c90UL


/***********************************************************
* Register Name : IOAGR_SION_Client_WrRspPoolCredit_Alloc_1
************************************************************/

#define IOAGR_SION_Client_WrRspPoolCredit_Alloc_1_IOAGR_SION_Client_WrRspPoolCredit_Alloc_1_OFFSET 0
#define IOAGR_SION_Client_WrRspPoolCredit_Alloc_1_IOAGR_SION_Client_WrRspPoolCredit_Alloc_1_MASK 0xffffffff

typedef union {
  struct {
    UINT32           IOAGR_SION_Client_WrRspPoolCredit_Alloc_1:32;
  } Field;
  UINT32 Value;
} IOAGR_SION_Client_WrRspPoolCredit_Alloc_1_STRUCT;

#define SMN_IOAGR_SION_Client_WrRspPoolCredit_Alloc_1_ADDRESS         0x15b00494UL
#define SMN_IOHUB0_N0NBIO0_IOAGR_SION_Client_WrRspPoolCredit_Alloc_1_ADDRESS 0x15b00494UL
#define SMN_IOHUB0_N0NBIO1_IOAGR_SION_Client_WrRspPoolCredit_Alloc_1_ADDRESS 0x15d00494UL
#define SMN_IOHUB0_N1NBIO0_IOAGR_SION_Client_WrRspPoolCredit_Alloc_1_ADDRESS 0x15b00894UL
#define SMN_IOHUB0_N1NBIO1_IOAGR_SION_Client_WrRspPoolCredit_Alloc_1_ADDRESS 0x15d00894UL
#define SMN_IOHUB0_N2NBIO0_IOAGR_SION_Client_WrRspPoolCredit_Alloc_1_ADDRESS 0x15b00c94UL
#define SMN_IOHUB0_N2NBIO1_IOAGR_SION_Client_WrRspPoolCredit_Alloc_1_ADDRESS 0x15d00c94UL
#define SMN_IOHUB0_N3NBIO0_IOAGR_SION_Client_WrRspPoolCredit_Alloc_1_ADDRESS 0x15b01094UL
#define SMN_IOHUB0_N3NBIO1_IOAGR_SION_Client_WrRspPoolCredit_Alloc_1_ADDRESS 0x15d01094UL
#define SMN_IOHUB0_N4NBIO0_IOAGR_SION_Client_WrRspPoolCredit_Alloc_1_ADDRESS 0x15b01494UL
#define SMN_IOHUB0_N4NBIO1_IOAGR_SION_Client_WrRspPoolCredit_Alloc_1_ADDRESS 0x15d01494UL
#define SMN_IOHUB0_N5NBIO0_IOAGR_SION_Client_WrRspPoolCredit_Alloc_1_ADDRESS 0x15b01894UL
#define SMN_IOHUB0_N5NBIO1_IOAGR_SION_Client_WrRspPoolCredit_Alloc_1_ADDRESS 0x15d01894UL
#define SMN_IOHUB1_N0NBIO0_IOAGR_SION_Client_WrRspPoolCredit_Alloc_1_ADDRESS 0x1e000494UL
#define SMN_IOHUB1_N0NBIO1_IOAGR_SION_Client_WrRspPoolCredit_Alloc_1_ADDRESS 0x1e200494UL
#define SMN_IOHUB1_N1NBIO0_IOAGR_SION_Client_WrRspPoolCredit_Alloc_1_ADDRESS 0x1e000894UL
#define SMN_IOHUB1_N1NBIO1_IOAGR_SION_Client_WrRspPoolCredit_Alloc_1_ADDRESS 0x1e200894UL
#define SMN_IOHUB1_N2NBIO0_IOAGR_SION_Client_WrRspPoolCredit_Alloc_1_ADDRESS 0x1e000c94UL
#define SMN_IOHUB1_N2NBIO1_IOAGR_SION_Client_WrRspPoolCredit_Alloc_1_ADDRESS 0x1e200c94UL
#define SMN_IOHUB2_N0NBIO0_IOAGR_SION_Client_WrRspPoolCredit_Alloc_1_ADDRESS 0x15c00494UL
#define SMN_IOHUB2_N0NBIO1_IOAGR_SION_Client_WrRspPoolCredit_Alloc_1_ADDRESS 0x15e00494UL
#define SMN_IOHUB2_N1NBIO0_IOAGR_SION_Client_WrRspPoolCredit_Alloc_1_ADDRESS 0x15c00894UL
#define SMN_IOHUB2_N1NBIO1_IOAGR_SION_Client_WrRspPoolCredit_Alloc_1_ADDRESS 0x15e00894UL
#define SMN_IOHUB2_N2NBIO0_IOAGR_SION_Client_WrRspPoolCredit_Alloc_1_ADDRESS 0x15c00c94UL
#define SMN_IOHUB2_N2NBIO1_IOAGR_SION_Client_WrRspPoolCredit_Alloc_1_ADDRESS 0x15e00c94UL
#define SMN_IOHUB2_N3NBIO0_IOAGR_SION_Client_WrRspPoolCredit_Alloc_1_ADDRESS 0x15c01094UL
#define SMN_IOHUB2_N3NBIO1_IOAGR_SION_Client_WrRspPoolCredit_Alloc_1_ADDRESS 0x15e01094UL
#define SMN_IOHUB2_N4NBIO0_IOAGR_SION_Client_WrRspPoolCredit_Alloc_1_ADDRESS 0x15c01494UL
#define SMN_IOHUB2_N4NBIO1_IOAGR_SION_Client_WrRspPoolCredit_Alloc_1_ADDRESS 0x15e01494UL
#define SMN_IOHUB2_N5NBIO0_IOAGR_SION_Client_WrRspPoolCredit_Alloc_1_ADDRESS 0x15c01894UL
#define SMN_IOHUB2_N5NBIO1_IOAGR_SION_Client_WrRspPoolCredit_Alloc_1_ADDRESS 0x15e01894UL
#define SMN_IOHUB3_N0NBIO0_IOAGR_SION_Client_WrRspPoolCredit_Alloc_1_ADDRESS 0x1e100494UL
#define SMN_IOHUB3_N0NBIO1_IOAGR_SION_Client_WrRspPoolCredit_Alloc_1_ADDRESS 0x1e300494UL
#define SMN_IOHUB3_N1NBIO0_IOAGR_SION_Client_WrRspPoolCredit_Alloc_1_ADDRESS 0x1e100894UL
#define SMN_IOHUB3_N1NBIO1_IOAGR_SION_Client_WrRspPoolCredit_Alloc_1_ADDRESS 0x1e300894UL
#define SMN_IOHUB3_N2NBIO0_IOAGR_SION_Client_WrRspPoolCredit_Alloc_1_ADDRESS 0x1e100c94UL
#define SMN_IOHUB3_N2NBIO1_IOAGR_SION_Client_WrRspPoolCredit_Alloc_1_ADDRESS 0x1e300c94UL


/***********************************************************
* Register Name : IOAGR_SION_Client_WrRspPoolCredit_Alloc_2
************************************************************/

#define IOAGR_SION_Client_WrRspPoolCredit_Alloc_2_IOAGR_SION_Client_WrRspPoolCredit_Alloc_2_OFFSET 0
#define IOAGR_SION_Client_WrRspPoolCredit_Alloc_2_IOAGR_SION_Client_WrRspPoolCredit_Alloc_2_MASK 0xffffffff

typedef union {
  struct {
    UINT32           IOAGR_SION_Client_WrRspPoolCredit_Alloc_2:32;
  } Field;
  UINT32 Value;
} IOAGR_SION_Client_WrRspPoolCredit_Alloc_2_STRUCT;

#define SMN_IOAGR_SION_Client_WrRspPoolCredit_Alloc_2_ADDRESS         0x15b00498UL
#define SMN_IOHUB0_N0NBIO0_IOAGR_SION_Client_WrRspPoolCredit_Alloc_2_ADDRESS 0x15b00498UL
#define SMN_IOHUB0_N0NBIO1_IOAGR_SION_Client_WrRspPoolCredit_Alloc_2_ADDRESS 0x15d00498UL
#define SMN_IOHUB0_N1NBIO0_IOAGR_SION_Client_WrRspPoolCredit_Alloc_2_ADDRESS 0x15b00898UL
#define SMN_IOHUB0_N1NBIO1_IOAGR_SION_Client_WrRspPoolCredit_Alloc_2_ADDRESS 0x15d00898UL
#define SMN_IOHUB0_N2NBIO0_IOAGR_SION_Client_WrRspPoolCredit_Alloc_2_ADDRESS 0x15b00c98UL
#define SMN_IOHUB0_N2NBIO1_IOAGR_SION_Client_WrRspPoolCredit_Alloc_2_ADDRESS 0x15d00c98UL
#define SMN_IOHUB0_N3NBIO0_IOAGR_SION_Client_WrRspPoolCredit_Alloc_2_ADDRESS 0x15b01098UL
#define SMN_IOHUB0_N3NBIO1_IOAGR_SION_Client_WrRspPoolCredit_Alloc_2_ADDRESS 0x15d01098UL
#define SMN_IOHUB0_N4NBIO0_IOAGR_SION_Client_WrRspPoolCredit_Alloc_2_ADDRESS 0x15b01498UL
#define SMN_IOHUB0_N4NBIO1_IOAGR_SION_Client_WrRspPoolCredit_Alloc_2_ADDRESS 0x15d01498UL
#define SMN_IOHUB0_N5NBIO0_IOAGR_SION_Client_WrRspPoolCredit_Alloc_2_ADDRESS 0x15b01898UL
#define SMN_IOHUB0_N5NBIO1_IOAGR_SION_Client_WrRspPoolCredit_Alloc_2_ADDRESS 0x15d01898UL
#define SMN_IOHUB1_N0NBIO0_IOAGR_SION_Client_WrRspPoolCredit_Alloc_2_ADDRESS 0x1e000498UL
#define SMN_IOHUB1_N0NBIO1_IOAGR_SION_Client_WrRspPoolCredit_Alloc_2_ADDRESS 0x1e200498UL
#define SMN_IOHUB1_N1NBIO0_IOAGR_SION_Client_WrRspPoolCredit_Alloc_2_ADDRESS 0x1e000898UL
#define SMN_IOHUB1_N1NBIO1_IOAGR_SION_Client_WrRspPoolCredit_Alloc_2_ADDRESS 0x1e200898UL
#define SMN_IOHUB1_N2NBIO0_IOAGR_SION_Client_WrRspPoolCredit_Alloc_2_ADDRESS 0x1e000c98UL
#define SMN_IOHUB1_N2NBIO1_IOAGR_SION_Client_WrRspPoolCredit_Alloc_2_ADDRESS 0x1e200c98UL
#define SMN_IOHUB2_N0NBIO0_IOAGR_SION_Client_WrRspPoolCredit_Alloc_2_ADDRESS 0x15c00498UL
#define SMN_IOHUB2_N0NBIO1_IOAGR_SION_Client_WrRspPoolCredit_Alloc_2_ADDRESS 0x15e00498UL
#define SMN_IOHUB2_N1NBIO0_IOAGR_SION_Client_WrRspPoolCredit_Alloc_2_ADDRESS 0x15c00898UL
#define SMN_IOHUB2_N1NBIO1_IOAGR_SION_Client_WrRspPoolCredit_Alloc_2_ADDRESS 0x15e00898UL
#define SMN_IOHUB2_N2NBIO0_IOAGR_SION_Client_WrRspPoolCredit_Alloc_2_ADDRESS 0x15c00c98UL
#define SMN_IOHUB2_N2NBIO1_IOAGR_SION_Client_WrRspPoolCredit_Alloc_2_ADDRESS 0x15e00c98UL
#define SMN_IOHUB2_N3NBIO0_IOAGR_SION_Client_WrRspPoolCredit_Alloc_2_ADDRESS 0x15c01098UL
#define SMN_IOHUB2_N3NBIO1_IOAGR_SION_Client_WrRspPoolCredit_Alloc_2_ADDRESS 0x15e01098UL
#define SMN_IOHUB2_N4NBIO0_IOAGR_SION_Client_WrRspPoolCredit_Alloc_2_ADDRESS 0x15c01498UL
#define SMN_IOHUB2_N4NBIO1_IOAGR_SION_Client_WrRspPoolCredit_Alloc_2_ADDRESS 0x15e01498UL
#define SMN_IOHUB2_N5NBIO0_IOAGR_SION_Client_WrRspPoolCredit_Alloc_2_ADDRESS 0x15c01898UL
#define SMN_IOHUB2_N5NBIO1_IOAGR_SION_Client_WrRspPoolCredit_Alloc_2_ADDRESS 0x15e01898UL
#define SMN_IOHUB3_N0NBIO0_IOAGR_SION_Client_WrRspPoolCredit_Alloc_2_ADDRESS 0x1e100498UL
#define SMN_IOHUB3_N0NBIO1_IOAGR_SION_Client_WrRspPoolCredit_Alloc_2_ADDRESS 0x1e300498UL
#define SMN_IOHUB3_N1NBIO0_IOAGR_SION_Client_WrRspPoolCredit_Alloc_2_ADDRESS 0x1e100898UL
#define SMN_IOHUB3_N1NBIO1_IOAGR_SION_Client_WrRspPoolCredit_Alloc_2_ADDRESS 0x1e300898UL
#define SMN_IOHUB3_N2NBIO0_IOAGR_SION_Client_WrRspPoolCredit_Alloc_2_ADDRESS 0x1e100c98UL
#define SMN_IOHUB3_N2NBIO1_IOAGR_SION_Client_WrRspPoolCredit_Alloc_2_ADDRESS 0x1e300c98UL


/***********************************************************
* Register Name : IOAGR_SION_Client_WrRspPoolCredit_Alloc_3
************************************************************/

#define IOAGR_SION_Client_WrRspPoolCredit_Alloc_3_IOAGR_SION_Client_WrRspPoolCredit_Alloc_3_OFFSET 0
#define IOAGR_SION_Client_WrRspPoolCredit_Alloc_3_IOAGR_SION_Client_WrRspPoolCredit_Alloc_3_MASK 0xffffffff

typedef union {
  struct {
    UINT32           IOAGR_SION_Client_WrRspPoolCredit_Alloc_3:32;
  } Field;
  UINT32 Value;
} IOAGR_SION_Client_WrRspPoolCredit_Alloc_3_STRUCT;

#define SMN_IOAGR_SION_Client_WrRspPoolCredit_Alloc_3_ADDRESS         0x15b0049cUL
#define SMN_IOHUB0_N0NBIO0_IOAGR_SION_Client_WrRspPoolCredit_Alloc_3_ADDRESS 0x15b0049cUL
#define SMN_IOHUB0_N0NBIO1_IOAGR_SION_Client_WrRspPoolCredit_Alloc_3_ADDRESS 0x15d0049cUL
#define SMN_IOHUB0_N1NBIO0_IOAGR_SION_Client_WrRspPoolCredit_Alloc_3_ADDRESS 0x15b0089cUL
#define SMN_IOHUB0_N1NBIO1_IOAGR_SION_Client_WrRspPoolCredit_Alloc_3_ADDRESS 0x15d0089cUL
#define SMN_IOHUB0_N2NBIO0_IOAGR_SION_Client_WrRspPoolCredit_Alloc_3_ADDRESS 0x15b00c9cUL
#define SMN_IOHUB0_N2NBIO1_IOAGR_SION_Client_WrRspPoolCredit_Alloc_3_ADDRESS 0x15d00c9cUL
#define SMN_IOHUB0_N3NBIO0_IOAGR_SION_Client_WrRspPoolCredit_Alloc_3_ADDRESS 0x15b0109cUL
#define SMN_IOHUB0_N3NBIO1_IOAGR_SION_Client_WrRspPoolCredit_Alloc_3_ADDRESS 0x15d0109cUL
#define SMN_IOHUB0_N4NBIO0_IOAGR_SION_Client_WrRspPoolCredit_Alloc_3_ADDRESS 0x15b0149cUL
#define SMN_IOHUB0_N4NBIO1_IOAGR_SION_Client_WrRspPoolCredit_Alloc_3_ADDRESS 0x15d0149cUL
#define SMN_IOHUB0_N5NBIO0_IOAGR_SION_Client_WrRspPoolCredit_Alloc_3_ADDRESS 0x15b0189cUL
#define SMN_IOHUB0_N5NBIO1_IOAGR_SION_Client_WrRspPoolCredit_Alloc_3_ADDRESS 0x15d0189cUL
#define SMN_IOHUB1_N0NBIO0_IOAGR_SION_Client_WrRspPoolCredit_Alloc_3_ADDRESS 0x1e00049cUL
#define SMN_IOHUB1_N0NBIO1_IOAGR_SION_Client_WrRspPoolCredit_Alloc_3_ADDRESS 0x1e20049cUL
#define SMN_IOHUB1_N1NBIO0_IOAGR_SION_Client_WrRspPoolCredit_Alloc_3_ADDRESS 0x1e00089cUL
#define SMN_IOHUB1_N1NBIO1_IOAGR_SION_Client_WrRspPoolCredit_Alloc_3_ADDRESS 0x1e20089cUL
#define SMN_IOHUB1_N2NBIO0_IOAGR_SION_Client_WrRspPoolCredit_Alloc_3_ADDRESS 0x1e000c9cUL
#define SMN_IOHUB1_N2NBIO1_IOAGR_SION_Client_WrRspPoolCredit_Alloc_3_ADDRESS 0x1e200c9cUL
#define SMN_IOHUB2_N0NBIO0_IOAGR_SION_Client_WrRspPoolCredit_Alloc_3_ADDRESS 0x15c0049cUL
#define SMN_IOHUB2_N0NBIO1_IOAGR_SION_Client_WrRspPoolCredit_Alloc_3_ADDRESS 0x15e0049cUL
#define SMN_IOHUB2_N1NBIO0_IOAGR_SION_Client_WrRspPoolCredit_Alloc_3_ADDRESS 0x15c0089cUL
#define SMN_IOHUB2_N1NBIO1_IOAGR_SION_Client_WrRspPoolCredit_Alloc_3_ADDRESS 0x15e0089cUL
#define SMN_IOHUB2_N2NBIO0_IOAGR_SION_Client_WrRspPoolCredit_Alloc_3_ADDRESS 0x15c00c9cUL
#define SMN_IOHUB2_N2NBIO1_IOAGR_SION_Client_WrRspPoolCredit_Alloc_3_ADDRESS 0x15e00c9cUL
#define SMN_IOHUB2_N3NBIO0_IOAGR_SION_Client_WrRspPoolCredit_Alloc_3_ADDRESS 0x15c0109cUL
#define SMN_IOHUB2_N3NBIO1_IOAGR_SION_Client_WrRspPoolCredit_Alloc_3_ADDRESS 0x15e0109cUL
#define SMN_IOHUB2_N4NBIO0_IOAGR_SION_Client_WrRspPoolCredit_Alloc_3_ADDRESS 0x15c0149cUL
#define SMN_IOHUB2_N4NBIO1_IOAGR_SION_Client_WrRspPoolCredit_Alloc_3_ADDRESS 0x15e0149cUL
#define SMN_IOHUB2_N5NBIO0_IOAGR_SION_Client_WrRspPoolCredit_Alloc_3_ADDRESS 0x15c0189cUL
#define SMN_IOHUB2_N5NBIO1_IOAGR_SION_Client_WrRspPoolCredit_Alloc_3_ADDRESS 0x15e0189cUL
#define SMN_IOHUB3_N0NBIO0_IOAGR_SION_Client_WrRspPoolCredit_Alloc_3_ADDRESS 0x1e10049cUL
#define SMN_IOHUB3_N0NBIO1_IOAGR_SION_Client_WrRspPoolCredit_Alloc_3_ADDRESS 0x1e30049cUL
#define SMN_IOHUB3_N1NBIO0_IOAGR_SION_Client_WrRspPoolCredit_Alloc_3_ADDRESS 0x1e10089cUL
#define SMN_IOHUB3_N1NBIO1_IOAGR_SION_Client_WrRspPoolCredit_Alloc_3_ADDRESS 0x1e30089cUL
#define SMN_IOHUB3_N2NBIO0_IOAGR_SION_Client_WrRspPoolCredit_Alloc_3_ADDRESS 0x1e100c9cUL
#define SMN_IOHUB3_N2NBIO1_IOAGR_SION_Client_WrRspPoolCredit_Alloc_3_ADDRESS 0x1e300c9cUL


/***********************************************************
* Register Name : IOAGR_SION_LiveLock_WatchDog_Threshold
************************************************************/

#define IOAGR_SION_LiveLock_WatchDog_Threshold_IOAGR_SION_LiveLock_WatchDog_Threshold_OFFSET 0
#define IOAGR_SION_LiveLock_WatchDog_Threshold_IOAGR_SION_LiveLock_WatchDog_Threshold_MASK 0xff

#define IOAGR_SION_LiveLock_WatchDog_Threshold_Reserved_31_8_OFFSET 8
#define IOAGR_SION_LiveLock_WatchDog_Threshold_Reserved_31_8_MASK 0xffffff00

typedef union {
  struct {
    UINT32              IOAGR_SION_LiveLock_WatchDog_Threshold:8;
    UINT32                                       Reserved_31_8:24;
  } Field;
  UINT32 Value;
} IOAGR_SION_LiveLock_WatchDog_Threshold_STRUCT;

#define SMN_IOAGR_SION_LiveLock_WatchDog_Threshold_ADDRESS            0x15b018a0UL
#define SMN_IOHUB0NBIO0_IOAGR_SION_LiveLock_WatchDog_Threshold_ADDRESS 0x15b018a0UL
#define SMN_IOHUB0NBIO1_IOAGR_SION_LiveLock_WatchDog_Threshold_ADDRESS 0x15d018a0UL
#define SMN_IOHUB1NBIO0_IOAGR_SION_LiveLock_WatchDog_Threshold_ADDRESS 0x1e000ca0UL
#define SMN_IOHUB1NBIO1_IOAGR_SION_LiveLock_WatchDog_Threshold_ADDRESS 0x1e200ca0UL
#define SMN_IOHUB2NBIO0_IOAGR_SION_LiveLock_WatchDog_Threshold_ADDRESS 0x15c018a0UL
#define SMN_IOHUB2NBIO1_IOAGR_SION_LiveLock_WatchDog_Threshold_ADDRESS 0x15e018a0UL
#define SMN_IOHUB3NBIO0_IOAGR_SION_LiveLock_WatchDog_Threshold_ADDRESS 0x1e100ca0UL
#define SMN_IOHUB3NBIO1_IOAGR_SION_LiveLock_WatchDog_Threshold_ADDRESS 0x1e300ca0UL


/***********************************************************
* Register Name : IOAGR_SION_PERF_CNT_CNTL0
************************************************************/

#define IOAGR_SION_PERF_CNT_CNTL0_IOAGR_SION_CNT_EN_OFFSET     0
#define IOAGR_SION_PERF_CNT_CNTL0_IOAGR_SION_CNT_EN_MASK       0x1

#define IOAGR_SION_PERF_CNT_CNTL0_IOAGR_SION_SHADOW_WR_OFFSET  1
#define IOAGR_SION_PERF_CNT_CNTL0_IOAGR_SION_SHADOW_WR_MASK    0x2

#define IOAGR_SION_PERF_CNT_CNTL0_IOAGR_SION_PERF_RESET_OFFSET 2
#define IOAGR_SION_PERF_CNT_CNTL0_IOAGR_SION_PERF_RESET_MASK   0x4

#define IOAGR_SION_PERF_CNT_CNTL0_Reserved_7_3_OFFSET          3
#define IOAGR_SION_PERF_CNT_CNTL0_Reserved_7_3_MASK            0xf8

#define IOAGR_SION_PERF_CNT_CNTL0_IOAGR_SION_SHADOW_DELAY_OFFSET 8
#define IOAGR_SION_PERF_CNT_CNTL0_IOAGR_SION_SHADOW_DELAY_MASK 0xf00

#define IOAGR_SION_PERF_CNT_CNTL0_Reserved_14_12_OFFSET        12
#define IOAGR_SION_PERF_CNT_CNTL0_Reserved_14_12_MASK          0x7000

#define IOAGR_SION_PERF_CNT_CNTL0_IOAGR_SION_SHADOW_DELAY_EN_OFFSET 15
#define IOAGR_SION_PERF_CNT_CNTL0_IOAGR_SION_SHADOW_DELAY_EN_MASK 0x8000

#define IOAGR_SION_PERF_CNT_CNTL0_IOAGR_SION_PERF_RESET_DELAY_OFFSET 16
#define IOAGR_SION_PERF_CNT_CNTL0_IOAGR_SION_PERF_RESET_DELAY_MASK 0xf0000

#define IOAGR_SION_PERF_CNT_CNTL0_Reserved_22_20_OFFSET        20
#define IOAGR_SION_PERF_CNT_CNTL0_Reserved_22_20_MASK          0x700000

#define IOAGR_SION_PERF_CNT_CNTL0_IOAGR_SION_PERF_RESET_DELAY_EN_OFFSET 23
#define IOAGR_SION_PERF_CNT_CNTL0_IOAGR_SION_PERF_RESET_DELAY_EN_MASK 0x800000

#define IOAGR_SION_PERF_CNT_CNTL0_Reserved_31_24_OFFSET        24
#define IOAGR_SION_PERF_CNT_CNTL0_Reserved_31_24_MASK          0xff000000

typedef union {
  struct {
    UINT32                                   IOAGR_SION_CNT_EN:1;
    UINT32                                IOAGR_SION_SHADOW_WR:1;
    UINT32                               IOAGR_SION_PERF_RESET:1;
    UINT32                                        Reserved_7_3:5;
    UINT32                             IOAGR_SION_SHADOW_DELAY:4;
    UINT32                                      Reserved_14_12:3;
    UINT32                          IOAGR_SION_SHADOW_DELAY_EN:1;
    UINT32                         IOAGR_SION_PERF_RESET_DELAY:4;
    UINT32                                      Reserved_22_20:3;
    UINT32                      IOAGR_SION_PERF_RESET_DELAY_EN:1;
    UINT32                                      Reserved_31_24:8;
  } Field;
  UINT32 Value;
} IOAGR_SION_PERF_CNT_CNTL0_STRUCT;

#define SMN_IOAGR_SION_PERF_CNT_CNTL0_ADDRESS                         0x15b018a4UL
#define SMN_IOHUB0NBIO0_IOAGR_SION_PERF_CNT_CNTL0_ADDRESS             0x15b018a4UL
#define SMN_IOHUB0NBIO1_IOAGR_SION_PERF_CNT_CNTL0_ADDRESS             0x15d018a4UL
#define SMN_IOHUB1NBIO0_IOAGR_SION_PERF_CNT_CNTL0_ADDRESS             0x1e000ca4UL
#define SMN_IOHUB1NBIO1_IOAGR_SION_PERF_CNT_CNTL0_ADDRESS             0x1e200ca4UL
#define SMN_IOHUB2NBIO0_IOAGR_SION_PERF_CNT_CNTL0_ADDRESS             0x15c018a4UL
#define SMN_IOHUB2NBIO1_IOAGR_SION_PERF_CNT_CNTL0_ADDRESS             0x15e018a4UL
#define SMN_IOHUB3NBIO0_IOAGR_SION_PERF_CNT_CNTL0_ADDRESS             0x1e100ca4UL
#define SMN_IOHUB3NBIO1_IOAGR_SION_PERF_CNT_CNTL0_ADDRESS             0x1e300ca4UL


/***********************************************************
* Register Name : IOAGR_SION_PERF_CNT_CNTL1
************************************************************/

#define IOAGR_SION_PERF_CNT_CNTL1_IOAGR_SION_EVENT0_SEL_OFFSET 0
#define IOAGR_SION_PERF_CNT_CNTL1_IOAGR_SION_EVENT0_SEL_MASK   0xff

#define IOAGR_SION_PERF_CNT_CNTL1_IOAGR_SION_EVENT1_SEL_OFFSET 8
#define IOAGR_SION_PERF_CNT_CNTL1_IOAGR_SION_EVENT1_SEL_MASK   0xff00

#define IOAGR_SION_PERF_CNT_CNTL1_IOAGR_SION_EVENT2_SEL_OFFSET 16
#define IOAGR_SION_PERF_CNT_CNTL1_IOAGR_SION_EVENT2_SEL_MASK   0xff0000

#define IOAGR_SION_PERF_CNT_CNTL1_IOAGR_SION_EVENT3_SEL_OFFSET 24
#define IOAGR_SION_PERF_CNT_CNTL1_IOAGR_SION_EVENT3_SEL_MASK   0xff000000

typedef union {
  struct {
    UINT32                               IOAGR_SION_EVENT0_SEL:8;
    UINT32                               IOAGR_SION_EVENT1_SEL:8;
    UINT32                               IOAGR_SION_EVENT2_SEL:8;
    UINT32                               IOAGR_SION_EVENT3_SEL:8;
  } Field;
  UINT32 Value;
} IOAGR_SION_PERF_CNT_CNTL1_STRUCT;

#define SMN_IOAGR_SION_PERF_CNT_CNTL1_ADDRESS                         0x15b018a8UL
#define SMN_IOHUB0NBIO0_IOAGR_SION_PERF_CNT_CNTL1_ADDRESS             0x15b018a8UL
#define SMN_IOHUB0NBIO1_IOAGR_SION_PERF_CNT_CNTL1_ADDRESS             0x15d018a8UL
#define SMN_IOHUB1NBIO0_IOAGR_SION_PERF_CNT_CNTL1_ADDRESS             0x1e000ca8UL
#define SMN_IOHUB1NBIO1_IOAGR_SION_PERF_CNT_CNTL1_ADDRESS             0x1e200ca8UL
#define SMN_IOHUB2NBIO0_IOAGR_SION_PERF_CNT_CNTL1_ADDRESS             0x15c018a8UL
#define SMN_IOHUB2NBIO1_IOAGR_SION_PERF_CNT_CNTL1_ADDRESS             0x15e018a8UL
#define SMN_IOHUB3NBIO0_IOAGR_SION_PERF_CNT_CNTL1_ADDRESS             0x1e100ca8UL
#define SMN_IOHUB3NBIO1_IOAGR_SION_PERF_CNT_CNTL1_ADDRESS             0x1e300ca8UL


/***********************************************************
* Register Name : IOAGR_SION_PERF_COUNT0
************************************************************/

#define IOAGR_SION_PERF_COUNT0_IOAGR_SION_COUNTER0_OFFSET      0
#define IOAGR_SION_PERF_COUNT0_IOAGR_SION_COUNTER0_MASK        0xffffffff

typedef union {
  struct {
    UINT32                                 IOAGR_SION_COUNTER0:32;
  } Field;
  UINT32 Value;
} IOAGR_SION_PERF_COUNT0_STRUCT;

#define SMN_IOAGR_SION_PERF_COUNT0_ADDRESS                            0x15b018acUL
#define SMN_IOHUB0NBIO0_IOAGR_SION_PERF_COUNT0_ADDRESS                0x15b018acUL
#define SMN_IOHUB0NBIO1_IOAGR_SION_PERF_COUNT0_ADDRESS                0x15d018acUL
#define SMN_IOHUB1NBIO0_IOAGR_SION_PERF_COUNT0_ADDRESS                0x1e000cacUL
#define SMN_IOHUB1NBIO1_IOAGR_SION_PERF_COUNT0_ADDRESS                0x1e200cacUL
#define SMN_IOHUB2NBIO0_IOAGR_SION_PERF_COUNT0_ADDRESS                0x15c018acUL
#define SMN_IOHUB2NBIO1_IOAGR_SION_PERF_COUNT0_ADDRESS                0x15e018acUL
#define SMN_IOHUB3NBIO0_IOAGR_SION_PERF_COUNT0_ADDRESS                0x1e100cacUL
#define SMN_IOHUB3NBIO1_IOAGR_SION_PERF_COUNT0_ADDRESS                0x1e300cacUL


/***********************************************************
* Register Name : IOAGR_SION_PERF_COUNT0_UPPER
************************************************************/

#define IOAGR_SION_PERF_COUNT0_UPPER_IOAGR_SION_COUNTER0_UPPER_OFFSET 0
#define IOAGR_SION_PERF_COUNT0_UPPER_IOAGR_SION_COUNTER0_UPPER_MASK 0xffffff

#define IOAGR_SION_PERF_COUNT0_UPPER_Reserved_31_24_OFFSET     24
#define IOAGR_SION_PERF_COUNT0_UPPER_Reserved_31_24_MASK       0xff000000

typedef union {
  struct {
    UINT32                           IOAGR_SION_COUNTER0_UPPER:24;
    UINT32                                      Reserved_31_24:8;
  } Field;
  UINT32 Value;
} IOAGR_SION_PERF_COUNT0_UPPER_STRUCT;

#define SMN_IOAGR_SION_PERF_COUNT0_UPPER_ADDRESS                      0x15b018b0UL
#define SMN_IOHUB0NBIO0_IOAGR_SION_PERF_COUNT0_UPPER_ADDRESS          0x15b018b0UL
#define SMN_IOHUB0NBIO1_IOAGR_SION_PERF_COUNT0_UPPER_ADDRESS          0x15d018b0UL
#define SMN_IOHUB1NBIO0_IOAGR_SION_PERF_COUNT0_UPPER_ADDRESS          0x1e000cb0UL
#define SMN_IOHUB1NBIO1_IOAGR_SION_PERF_COUNT0_UPPER_ADDRESS          0x1e200cb0UL
#define SMN_IOHUB2NBIO0_IOAGR_SION_PERF_COUNT0_UPPER_ADDRESS          0x15c018b0UL
#define SMN_IOHUB2NBIO1_IOAGR_SION_PERF_COUNT0_UPPER_ADDRESS          0x15e018b0UL
#define SMN_IOHUB3NBIO0_IOAGR_SION_PERF_COUNT0_UPPER_ADDRESS          0x1e100cb0UL
#define SMN_IOHUB3NBIO1_IOAGR_SION_PERF_COUNT0_UPPER_ADDRESS          0x1e300cb0UL


/***********************************************************
* Register Name : IOAGR_SION_PERF_COUNT1
************************************************************/

#define IOAGR_SION_PERF_COUNT1_IOAGR_SION_COUNTER1_OFFSET      0
#define IOAGR_SION_PERF_COUNT1_IOAGR_SION_COUNTER1_MASK        0xffffffff

typedef union {
  struct {
    UINT32                                 IOAGR_SION_COUNTER1:32;
  } Field;
  UINT32 Value;
} IOAGR_SION_PERF_COUNT1_STRUCT;

#define SMN_IOAGR_SION_PERF_COUNT1_ADDRESS                            0x15b018b4UL
#define SMN_IOHUB0NBIO0_IOAGR_SION_PERF_COUNT1_ADDRESS                0x15b018b4UL
#define SMN_IOHUB0NBIO1_IOAGR_SION_PERF_COUNT1_ADDRESS                0x15d018b4UL
#define SMN_IOHUB1NBIO0_IOAGR_SION_PERF_COUNT1_ADDRESS                0x1e000cb4UL
#define SMN_IOHUB1NBIO1_IOAGR_SION_PERF_COUNT1_ADDRESS                0x1e200cb4UL
#define SMN_IOHUB2NBIO0_IOAGR_SION_PERF_COUNT1_ADDRESS                0x15c018b4UL
#define SMN_IOHUB2NBIO1_IOAGR_SION_PERF_COUNT1_ADDRESS                0x15e018b4UL
#define SMN_IOHUB3NBIO0_IOAGR_SION_PERF_COUNT1_ADDRESS                0x1e100cb4UL
#define SMN_IOHUB3NBIO1_IOAGR_SION_PERF_COUNT1_ADDRESS                0x1e300cb4UL


/***********************************************************
* Register Name : IOAGR_SION_PERF_COUNT1_UPPER
************************************************************/

#define IOAGR_SION_PERF_COUNT1_UPPER_IOAGR_SION_COUNTER1_UPPER_OFFSET 0
#define IOAGR_SION_PERF_COUNT1_UPPER_IOAGR_SION_COUNTER1_UPPER_MASK 0xffffff

#define IOAGR_SION_PERF_COUNT1_UPPER_Reserved_31_24_OFFSET     24
#define IOAGR_SION_PERF_COUNT1_UPPER_Reserved_31_24_MASK       0xff000000

typedef union {
  struct {
    UINT32                           IOAGR_SION_COUNTER1_UPPER:24;
    UINT32                                      Reserved_31_24:8;
  } Field;
  UINT32 Value;
} IOAGR_SION_PERF_COUNT1_UPPER_STRUCT;

#define SMN_IOAGR_SION_PERF_COUNT1_UPPER_ADDRESS                      0x15b018b8UL
#define SMN_IOHUB0NBIO0_IOAGR_SION_PERF_COUNT1_UPPER_ADDRESS          0x15b018b8UL
#define SMN_IOHUB0NBIO1_IOAGR_SION_PERF_COUNT1_UPPER_ADDRESS          0x15d018b8UL
#define SMN_IOHUB1NBIO0_IOAGR_SION_PERF_COUNT1_UPPER_ADDRESS          0x1e000cb8UL
#define SMN_IOHUB1NBIO1_IOAGR_SION_PERF_COUNT1_UPPER_ADDRESS          0x1e200cb8UL
#define SMN_IOHUB2NBIO0_IOAGR_SION_PERF_COUNT1_UPPER_ADDRESS          0x15c018b8UL
#define SMN_IOHUB2NBIO1_IOAGR_SION_PERF_COUNT1_UPPER_ADDRESS          0x15e018b8UL
#define SMN_IOHUB3NBIO0_IOAGR_SION_PERF_COUNT1_UPPER_ADDRESS          0x1e100cb8UL
#define SMN_IOHUB3NBIO1_IOAGR_SION_PERF_COUNT1_UPPER_ADDRESS          0x1e300cb8UL


/***********************************************************
* Register Name : IOAGR_SION_PERF_COUNT2
************************************************************/

#define IOAGR_SION_PERF_COUNT2_IOAGR_SION_COUNTER2_OFFSET      0
#define IOAGR_SION_PERF_COUNT2_IOAGR_SION_COUNTER2_MASK        0xffffffff

typedef union {
  struct {
    UINT32                                 IOAGR_SION_COUNTER2:32;
  } Field;
  UINT32 Value;
} IOAGR_SION_PERF_COUNT2_STRUCT;

#define SMN_IOAGR_SION_PERF_COUNT2_ADDRESS                            0x15b018bcUL
#define SMN_IOHUB0NBIO0_IOAGR_SION_PERF_COUNT2_ADDRESS                0x15b018bcUL
#define SMN_IOHUB0NBIO1_IOAGR_SION_PERF_COUNT2_ADDRESS                0x15d018bcUL
#define SMN_IOHUB1NBIO0_IOAGR_SION_PERF_COUNT2_ADDRESS                0x1e000cbcUL
#define SMN_IOHUB1NBIO1_IOAGR_SION_PERF_COUNT2_ADDRESS                0x1e200cbcUL
#define SMN_IOHUB2NBIO0_IOAGR_SION_PERF_COUNT2_ADDRESS                0x15c018bcUL
#define SMN_IOHUB2NBIO1_IOAGR_SION_PERF_COUNT2_ADDRESS                0x15e018bcUL
#define SMN_IOHUB3NBIO0_IOAGR_SION_PERF_COUNT2_ADDRESS                0x1e100cbcUL
#define SMN_IOHUB3NBIO1_IOAGR_SION_PERF_COUNT2_ADDRESS                0x1e300cbcUL


/***********************************************************
* Register Name : IOAGR_SION_PERF_COUNT2_UPPER
************************************************************/

#define IOAGR_SION_PERF_COUNT2_UPPER_IOAGR_SION_COUNTER2_UPPER_OFFSET 0
#define IOAGR_SION_PERF_COUNT2_UPPER_IOAGR_SION_COUNTER2_UPPER_MASK 0xffffff

#define IOAGR_SION_PERF_COUNT2_UPPER_Reserved_31_24_OFFSET     24
#define IOAGR_SION_PERF_COUNT2_UPPER_Reserved_31_24_MASK       0xff000000

typedef union {
  struct {
    UINT32                           IOAGR_SION_COUNTER2_UPPER:24;
    UINT32                                      Reserved_31_24:8;
  } Field;
  UINT32 Value;
} IOAGR_SION_PERF_COUNT2_UPPER_STRUCT;

#define SMN_IOAGR_SION_PERF_COUNT2_UPPER_ADDRESS                      0x15b018c0UL
#define SMN_IOHUB0NBIO0_IOAGR_SION_PERF_COUNT2_UPPER_ADDRESS          0x15b018c0UL
#define SMN_IOHUB0NBIO1_IOAGR_SION_PERF_COUNT2_UPPER_ADDRESS          0x15d018c0UL
#define SMN_IOHUB1NBIO0_IOAGR_SION_PERF_COUNT2_UPPER_ADDRESS          0x1e000cc0UL
#define SMN_IOHUB1NBIO1_IOAGR_SION_PERF_COUNT2_UPPER_ADDRESS          0x1e200cc0UL
#define SMN_IOHUB2NBIO0_IOAGR_SION_PERF_COUNT2_UPPER_ADDRESS          0x15c018c0UL
#define SMN_IOHUB2NBIO1_IOAGR_SION_PERF_COUNT2_UPPER_ADDRESS          0x15e018c0UL
#define SMN_IOHUB3NBIO0_IOAGR_SION_PERF_COUNT2_UPPER_ADDRESS          0x1e100cc0UL
#define SMN_IOHUB3NBIO1_IOAGR_SION_PERF_COUNT2_UPPER_ADDRESS          0x1e300cc0UL


/***********************************************************
* Register Name : IOAGR_SION_PERF_COUNT3
************************************************************/

#define IOAGR_SION_PERF_COUNT3_IOAGR_SION_COUNTER3_OFFSET      0
#define IOAGR_SION_PERF_COUNT3_IOAGR_SION_COUNTER3_MASK        0xffffffff

typedef union {
  struct {
    UINT32                                 IOAGR_SION_COUNTER3:32;
  } Field;
  UINT32 Value;
} IOAGR_SION_PERF_COUNT3_STRUCT;

#define SMN_IOAGR_SION_PERF_COUNT3_ADDRESS                            0x15b018c4UL
#define SMN_IOHUB0NBIO0_IOAGR_SION_PERF_COUNT3_ADDRESS                0x15b018c4UL
#define SMN_IOHUB0NBIO1_IOAGR_SION_PERF_COUNT3_ADDRESS                0x15d018c4UL
#define SMN_IOHUB1NBIO0_IOAGR_SION_PERF_COUNT3_ADDRESS                0x1e000cc4UL
#define SMN_IOHUB1NBIO1_IOAGR_SION_PERF_COUNT3_ADDRESS                0x1e200cc4UL
#define SMN_IOHUB2NBIO0_IOAGR_SION_PERF_COUNT3_ADDRESS                0x15c018c4UL
#define SMN_IOHUB2NBIO1_IOAGR_SION_PERF_COUNT3_ADDRESS                0x15e018c4UL
#define SMN_IOHUB3NBIO0_IOAGR_SION_PERF_COUNT3_ADDRESS                0x1e100cc4UL
#define SMN_IOHUB3NBIO1_IOAGR_SION_PERF_COUNT3_ADDRESS                0x1e300cc4UL


/***********************************************************
* Register Name : IOAGR_SION_PERF_COUNT3_UPPER
************************************************************/

#define IOAGR_SION_PERF_COUNT3_UPPER_IOAGR_SION_COUNTER3_UPPER_OFFSET 0
#define IOAGR_SION_PERF_COUNT3_UPPER_IOAGR_SION_COUNTER3_UPPER_MASK 0xffffff

#define IOAGR_SION_PERF_COUNT3_UPPER_Reserved_31_24_OFFSET     24
#define IOAGR_SION_PERF_COUNT3_UPPER_Reserved_31_24_MASK       0xff000000

typedef union {
  struct {
    UINT32                           IOAGR_SION_COUNTER3_UPPER:24;
    UINT32                                      Reserved_31_24:8;
  } Field;
  UINT32 Value;
} IOAGR_SION_PERF_COUNT3_UPPER_STRUCT;

#define SMN_IOAGR_SION_PERF_COUNT3_UPPER_ADDRESS                      0x15b018c8UL
#define SMN_IOHUB0NBIO0_IOAGR_SION_PERF_COUNT3_UPPER_ADDRESS          0x15b018c8UL
#define SMN_IOHUB0NBIO1_IOAGR_SION_PERF_COUNT3_UPPER_ADDRESS          0x15d018c8UL
#define SMN_IOHUB1NBIO0_IOAGR_SION_PERF_COUNT3_UPPER_ADDRESS          0x1e000cc8UL
#define SMN_IOHUB1NBIO1_IOAGR_SION_PERF_COUNT3_UPPER_ADDRESS          0x1e200cc8UL
#define SMN_IOHUB2NBIO0_IOAGR_SION_PERF_COUNT3_UPPER_ADDRESS          0x15c018c8UL
#define SMN_IOHUB2NBIO1_IOAGR_SION_PERF_COUNT3_UPPER_ADDRESS          0x15e018c8UL
#define SMN_IOHUB3NBIO0_IOAGR_SION_PERF_COUNT3_UPPER_ADDRESS          0x1e100cc8UL
#define SMN_IOHUB3NBIO1_IOAGR_SION_PERF_COUNT3_UPPER_ADDRESS          0x1e300cc8UL


/***********************************************************
* Register Name : IOAGR_SION_S0_Client_RdRsp_BurstTarget_Lower
************************************************************/

#define IOAGR_SION_S0_Client_RdRsp_BurstTarget_Lower_IOAGR_SION_S0_Client_RdRsp_BurstTarget_Lower_OFFSET 0
#define IOAGR_SION_S0_Client_RdRsp_BurstTarget_Lower_IOAGR_SION_S0_Client_RdRsp_BurstTarget_Lower_MASK 0xffffffff

typedef union {
  struct {
    UINT32        IOAGR_SION_S0_Client_RdRsp_BurstTarget_Lower:32;
  } Field;
  UINT32 Value;
} IOAGR_SION_S0_Client_RdRsp_BurstTarget_Lower_STRUCT;

#define SMN_IOAGR_SION_S0_Client_RdRsp_BurstTarget_Lower_ADDRESS      0x15b00410UL
#define SMN_IOHUB0_N0NBIO0_IOAGR_SION_S0_Client_RdRsp_BurstTarget_Lower_ADDRESS 0x15b00410UL
#define SMN_IOHUB0_N0NBIO1_IOAGR_SION_S0_Client_RdRsp_BurstTarget_Lower_ADDRESS 0x15d00410UL
#define SMN_IOHUB0_N1NBIO0_IOAGR_SION_S0_Client_RdRsp_BurstTarget_Lower_ADDRESS 0x15b00810UL
#define SMN_IOHUB0_N1NBIO1_IOAGR_SION_S0_Client_RdRsp_BurstTarget_Lower_ADDRESS 0x15d00810UL
#define SMN_IOHUB0_N2NBIO0_IOAGR_SION_S0_Client_RdRsp_BurstTarget_Lower_ADDRESS 0x15b00c10UL
#define SMN_IOHUB0_N2NBIO1_IOAGR_SION_S0_Client_RdRsp_BurstTarget_Lower_ADDRESS 0x15d00c10UL
#define SMN_IOHUB0_N3NBIO0_IOAGR_SION_S0_Client_RdRsp_BurstTarget_Lower_ADDRESS 0x15b01010UL
#define SMN_IOHUB0_N3NBIO1_IOAGR_SION_S0_Client_RdRsp_BurstTarget_Lower_ADDRESS 0x15d01010UL
#define SMN_IOHUB0_N4NBIO0_IOAGR_SION_S0_Client_RdRsp_BurstTarget_Lower_ADDRESS 0x15b01410UL
#define SMN_IOHUB0_N4NBIO1_IOAGR_SION_S0_Client_RdRsp_BurstTarget_Lower_ADDRESS 0x15d01410UL
#define SMN_IOHUB0_N5NBIO0_IOAGR_SION_S0_Client_RdRsp_BurstTarget_Lower_ADDRESS 0x15b01810UL
#define SMN_IOHUB0_N5NBIO1_IOAGR_SION_S0_Client_RdRsp_BurstTarget_Lower_ADDRESS 0x15d01810UL
#define SMN_IOHUB1_N0NBIO0_IOAGR_SION_S0_Client_RdRsp_BurstTarget_Lower_ADDRESS 0x1e000410UL
#define SMN_IOHUB1_N0NBIO1_IOAGR_SION_S0_Client_RdRsp_BurstTarget_Lower_ADDRESS 0x1e200410UL
#define SMN_IOHUB1_N1NBIO0_IOAGR_SION_S0_Client_RdRsp_BurstTarget_Lower_ADDRESS 0x1e000810UL
#define SMN_IOHUB1_N1NBIO1_IOAGR_SION_S0_Client_RdRsp_BurstTarget_Lower_ADDRESS 0x1e200810UL
#define SMN_IOHUB1_N2NBIO0_IOAGR_SION_S0_Client_RdRsp_BurstTarget_Lower_ADDRESS 0x1e000c10UL
#define SMN_IOHUB1_N2NBIO1_IOAGR_SION_S0_Client_RdRsp_BurstTarget_Lower_ADDRESS 0x1e200c10UL
#define SMN_IOHUB2_N0NBIO0_IOAGR_SION_S0_Client_RdRsp_BurstTarget_Lower_ADDRESS 0x15c00410UL
#define SMN_IOHUB2_N0NBIO1_IOAGR_SION_S0_Client_RdRsp_BurstTarget_Lower_ADDRESS 0x15e00410UL
#define SMN_IOHUB2_N1NBIO0_IOAGR_SION_S0_Client_RdRsp_BurstTarget_Lower_ADDRESS 0x15c00810UL
#define SMN_IOHUB2_N1NBIO1_IOAGR_SION_S0_Client_RdRsp_BurstTarget_Lower_ADDRESS 0x15e00810UL
#define SMN_IOHUB2_N2NBIO0_IOAGR_SION_S0_Client_RdRsp_BurstTarget_Lower_ADDRESS 0x15c00c10UL
#define SMN_IOHUB2_N2NBIO1_IOAGR_SION_S0_Client_RdRsp_BurstTarget_Lower_ADDRESS 0x15e00c10UL
#define SMN_IOHUB2_N3NBIO0_IOAGR_SION_S0_Client_RdRsp_BurstTarget_Lower_ADDRESS 0x15c01010UL
#define SMN_IOHUB2_N3NBIO1_IOAGR_SION_S0_Client_RdRsp_BurstTarget_Lower_ADDRESS 0x15e01010UL
#define SMN_IOHUB2_N4NBIO0_IOAGR_SION_S0_Client_RdRsp_BurstTarget_Lower_ADDRESS 0x15c01410UL
#define SMN_IOHUB2_N4NBIO1_IOAGR_SION_S0_Client_RdRsp_BurstTarget_Lower_ADDRESS 0x15e01410UL
#define SMN_IOHUB2_N5NBIO0_IOAGR_SION_S0_Client_RdRsp_BurstTarget_Lower_ADDRESS 0x15c01810UL
#define SMN_IOHUB2_N5NBIO1_IOAGR_SION_S0_Client_RdRsp_BurstTarget_Lower_ADDRESS 0x15e01810UL
#define SMN_IOHUB3_N0NBIO0_IOAGR_SION_S0_Client_RdRsp_BurstTarget_Lower_ADDRESS 0x1e100410UL
#define SMN_IOHUB3_N0NBIO1_IOAGR_SION_S0_Client_RdRsp_BurstTarget_Lower_ADDRESS 0x1e300410UL
#define SMN_IOHUB3_N1NBIO0_IOAGR_SION_S0_Client_RdRsp_BurstTarget_Lower_ADDRESS 0x1e100810UL
#define SMN_IOHUB3_N1NBIO1_IOAGR_SION_S0_Client_RdRsp_BurstTarget_Lower_ADDRESS 0x1e300810UL
#define SMN_IOHUB3_N2NBIO0_IOAGR_SION_S0_Client_RdRsp_BurstTarget_Lower_ADDRESS 0x1e100c10UL
#define SMN_IOHUB3_N2NBIO1_IOAGR_SION_S0_Client_RdRsp_BurstTarget_Lower_ADDRESS 0x1e300c10UL


/***********************************************************
* Register Name : IOAGR_SION_S0_Client_RdRsp_BurstTarget_Upper
************************************************************/

#define IOAGR_SION_S0_Client_RdRsp_BurstTarget_Upper_IOAGR_SION_S0_Client_RdRsp_BurstTarget_Upper_OFFSET 0
#define IOAGR_SION_S0_Client_RdRsp_BurstTarget_Upper_IOAGR_SION_S0_Client_RdRsp_BurstTarget_Upper_MASK 0xffffffff

typedef union {
  struct {
    UINT32        IOAGR_SION_S0_Client_RdRsp_BurstTarget_Upper:32;
  } Field;
  UINT32 Value;
} IOAGR_SION_S0_Client_RdRsp_BurstTarget_Upper_STRUCT;

#define SMN_IOAGR_SION_S0_Client_RdRsp_BurstTarget_Upper_ADDRESS      0x15b00414UL
#define SMN_IOHUB0_N0NBIO0_IOAGR_SION_S0_Client_RdRsp_BurstTarget_Upper_ADDRESS 0x15b00414UL
#define SMN_IOHUB0_N0NBIO1_IOAGR_SION_S0_Client_RdRsp_BurstTarget_Upper_ADDRESS 0x15d00414UL
#define SMN_IOHUB0_N1NBIO0_IOAGR_SION_S0_Client_RdRsp_BurstTarget_Upper_ADDRESS 0x15b00814UL
#define SMN_IOHUB0_N1NBIO1_IOAGR_SION_S0_Client_RdRsp_BurstTarget_Upper_ADDRESS 0x15d00814UL
#define SMN_IOHUB0_N2NBIO0_IOAGR_SION_S0_Client_RdRsp_BurstTarget_Upper_ADDRESS 0x15b00c14UL
#define SMN_IOHUB0_N2NBIO1_IOAGR_SION_S0_Client_RdRsp_BurstTarget_Upper_ADDRESS 0x15d00c14UL
#define SMN_IOHUB0_N3NBIO0_IOAGR_SION_S0_Client_RdRsp_BurstTarget_Upper_ADDRESS 0x15b01014UL
#define SMN_IOHUB0_N3NBIO1_IOAGR_SION_S0_Client_RdRsp_BurstTarget_Upper_ADDRESS 0x15d01014UL
#define SMN_IOHUB0_N4NBIO0_IOAGR_SION_S0_Client_RdRsp_BurstTarget_Upper_ADDRESS 0x15b01414UL
#define SMN_IOHUB0_N4NBIO1_IOAGR_SION_S0_Client_RdRsp_BurstTarget_Upper_ADDRESS 0x15d01414UL
#define SMN_IOHUB0_N5NBIO0_IOAGR_SION_S0_Client_RdRsp_BurstTarget_Upper_ADDRESS 0x15b01814UL
#define SMN_IOHUB0_N5NBIO1_IOAGR_SION_S0_Client_RdRsp_BurstTarget_Upper_ADDRESS 0x15d01814UL
#define SMN_IOHUB1_N0NBIO0_IOAGR_SION_S0_Client_RdRsp_BurstTarget_Upper_ADDRESS 0x1e000414UL
#define SMN_IOHUB1_N0NBIO1_IOAGR_SION_S0_Client_RdRsp_BurstTarget_Upper_ADDRESS 0x1e200414UL
#define SMN_IOHUB1_N1NBIO0_IOAGR_SION_S0_Client_RdRsp_BurstTarget_Upper_ADDRESS 0x1e000814UL
#define SMN_IOHUB1_N1NBIO1_IOAGR_SION_S0_Client_RdRsp_BurstTarget_Upper_ADDRESS 0x1e200814UL
#define SMN_IOHUB1_N2NBIO0_IOAGR_SION_S0_Client_RdRsp_BurstTarget_Upper_ADDRESS 0x1e000c14UL
#define SMN_IOHUB1_N2NBIO1_IOAGR_SION_S0_Client_RdRsp_BurstTarget_Upper_ADDRESS 0x1e200c14UL
#define SMN_IOHUB2_N0NBIO0_IOAGR_SION_S0_Client_RdRsp_BurstTarget_Upper_ADDRESS 0x15c00414UL
#define SMN_IOHUB2_N0NBIO1_IOAGR_SION_S0_Client_RdRsp_BurstTarget_Upper_ADDRESS 0x15e00414UL
#define SMN_IOHUB2_N1NBIO0_IOAGR_SION_S0_Client_RdRsp_BurstTarget_Upper_ADDRESS 0x15c00814UL
#define SMN_IOHUB2_N1NBIO1_IOAGR_SION_S0_Client_RdRsp_BurstTarget_Upper_ADDRESS 0x15e00814UL
#define SMN_IOHUB2_N2NBIO0_IOAGR_SION_S0_Client_RdRsp_BurstTarget_Upper_ADDRESS 0x15c00c14UL
#define SMN_IOHUB2_N2NBIO1_IOAGR_SION_S0_Client_RdRsp_BurstTarget_Upper_ADDRESS 0x15e00c14UL
#define SMN_IOHUB2_N3NBIO0_IOAGR_SION_S0_Client_RdRsp_BurstTarget_Upper_ADDRESS 0x15c01014UL
#define SMN_IOHUB2_N3NBIO1_IOAGR_SION_S0_Client_RdRsp_BurstTarget_Upper_ADDRESS 0x15e01014UL
#define SMN_IOHUB2_N4NBIO0_IOAGR_SION_S0_Client_RdRsp_BurstTarget_Upper_ADDRESS 0x15c01414UL
#define SMN_IOHUB2_N4NBIO1_IOAGR_SION_S0_Client_RdRsp_BurstTarget_Upper_ADDRESS 0x15e01414UL
#define SMN_IOHUB2_N5NBIO0_IOAGR_SION_S0_Client_RdRsp_BurstTarget_Upper_ADDRESS 0x15c01814UL
#define SMN_IOHUB2_N5NBIO1_IOAGR_SION_S0_Client_RdRsp_BurstTarget_Upper_ADDRESS 0x15e01814UL
#define SMN_IOHUB3_N0NBIO0_IOAGR_SION_S0_Client_RdRsp_BurstTarget_Upper_ADDRESS 0x1e100414UL
#define SMN_IOHUB3_N0NBIO1_IOAGR_SION_S0_Client_RdRsp_BurstTarget_Upper_ADDRESS 0x1e300414UL
#define SMN_IOHUB3_N1NBIO0_IOAGR_SION_S0_Client_RdRsp_BurstTarget_Upper_ADDRESS 0x1e100814UL
#define SMN_IOHUB3_N1NBIO1_IOAGR_SION_S0_Client_RdRsp_BurstTarget_Upper_ADDRESS 0x1e300814UL
#define SMN_IOHUB3_N2NBIO0_IOAGR_SION_S0_Client_RdRsp_BurstTarget_Upper_ADDRESS 0x1e100c14UL
#define SMN_IOHUB3_N2NBIO1_IOAGR_SION_S0_Client_RdRsp_BurstTarget_Upper_ADDRESS 0x1e300c14UL


/***********************************************************
* Register Name : IOAGR_SION_S0_Client_RdRsp_TimeSlot_Lower
************************************************************/

#define IOAGR_SION_S0_Client_RdRsp_TimeSlot_Lower_IOAGR_SION_S0_Client_RdRsp_TimeSlot_Lower_OFFSET 0
#define IOAGR_SION_S0_Client_RdRsp_TimeSlot_Lower_IOAGR_SION_S0_Client_RdRsp_TimeSlot_Lower_MASK 0xffffffff

typedef union {
  struct {
    UINT32           IOAGR_SION_S0_Client_RdRsp_TimeSlot_Lower:32;
  } Field;
  UINT32 Value;
} IOAGR_SION_S0_Client_RdRsp_TimeSlot_Lower_STRUCT;

#define SMN_IOAGR_SION_S0_Client_RdRsp_TimeSlot_Lower_ADDRESS         0x15b00418UL
#define SMN_IOHUB0_N0NBIO0_IOAGR_SION_S0_Client_RdRsp_TimeSlot_Lower_ADDRESS 0x15b00418UL
#define SMN_IOHUB0_N0NBIO1_IOAGR_SION_S0_Client_RdRsp_TimeSlot_Lower_ADDRESS 0x15d00418UL
#define SMN_IOHUB0_N1NBIO0_IOAGR_SION_S0_Client_RdRsp_TimeSlot_Lower_ADDRESS 0x15b00818UL
#define SMN_IOHUB0_N1NBIO1_IOAGR_SION_S0_Client_RdRsp_TimeSlot_Lower_ADDRESS 0x15d00818UL
#define SMN_IOHUB0_N2NBIO0_IOAGR_SION_S0_Client_RdRsp_TimeSlot_Lower_ADDRESS 0x15b00c18UL
#define SMN_IOHUB0_N2NBIO1_IOAGR_SION_S0_Client_RdRsp_TimeSlot_Lower_ADDRESS 0x15d00c18UL
#define SMN_IOHUB0_N3NBIO0_IOAGR_SION_S0_Client_RdRsp_TimeSlot_Lower_ADDRESS 0x15b01018UL
#define SMN_IOHUB0_N3NBIO1_IOAGR_SION_S0_Client_RdRsp_TimeSlot_Lower_ADDRESS 0x15d01018UL
#define SMN_IOHUB0_N4NBIO0_IOAGR_SION_S0_Client_RdRsp_TimeSlot_Lower_ADDRESS 0x15b01418UL
#define SMN_IOHUB0_N4NBIO1_IOAGR_SION_S0_Client_RdRsp_TimeSlot_Lower_ADDRESS 0x15d01418UL
#define SMN_IOHUB0_N5NBIO0_IOAGR_SION_S0_Client_RdRsp_TimeSlot_Lower_ADDRESS 0x15b01818UL
#define SMN_IOHUB0_N5NBIO1_IOAGR_SION_S0_Client_RdRsp_TimeSlot_Lower_ADDRESS 0x15d01818UL
#define SMN_IOHUB1_N0NBIO0_IOAGR_SION_S0_Client_RdRsp_TimeSlot_Lower_ADDRESS 0x1e000418UL
#define SMN_IOHUB1_N0NBIO1_IOAGR_SION_S0_Client_RdRsp_TimeSlot_Lower_ADDRESS 0x1e200418UL
#define SMN_IOHUB1_N1NBIO0_IOAGR_SION_S0_Client_RdRsp_TimeSlot_Lower_ADDRESS 0x1e000818UL
#define SMN_IOHUB1_N1NBIO1_IOAGR_SION_S0_Client_RdRsp_TimeSlot_Lower_ADDRESS 0x1e200818UL
#define SMN_IOHUB1_N2NBIO0_IOAGR_SION_S0_Client_RdRsp_TimeSlot_Lower_ADDRESS 0x1e000c18UL
#define SMN_IOHUB1_N2NBIO1_IOAGR_SION_S0_Client_RdRsp_TimeSlot_Lower_ADDRESS 0x1e200c18UL
#define SMN_IOHUB2_N0NBIO0_IOAGR_SION_S0_Client_RdRsp_TimeSlot_Lower_ADDRESS 0x15c00418UL
#define SMN_IOHUB2_N0NBIO1_IOAGR_SION_S0_Client_RdRsp_TimeSlot_Lower_ADDRESS 0x15e00418UL
#define SMN_IOHUB2_N1NBIO0_IOAGR_SION_S0_Client_RdRsp_TimeSlot_Lower_ADDRESS 0x15c00818UL
#define SMN_IOHUB2_N1NBIO1_IOAGR_SION_S0_Client_RdRsp_TimeSlot_Lower_ADDRESS 0x15e00818UL
#define SMN_IOHUB2_N2NBIO0_IOAGR_SION_S0_Client_RdRsp_TimeSlot_Lower_ADDRESS 0x15c00c18UL
#define SMN_IOHUB2_N2NBIO1_IOAGR_SION_S0_Client_RdRsp_TimeSlot_Lower_ADDRESS 0x15e00c18UL
#define SMN_IOHUB2_N3NBIO0_IOAGR_SION_S0_Client_RdRsp_TimeSlot_Lower_ADDRESS 0x15c01018UL
#define SMN_IOHUB2_N3NBIO1_IOAGR_SION_S0_Client_RdRsp_TimeSlot_Lower_ADDRESS 0x15e01018UL
#define SMN_IOHUB2_N4NBIO0_IOAGR_SION_S0_Client_RdRsp_TimeSlot_Lower_ADDRESS 0x15c01418UL
#define SMN_IOHUB2_N4NBIO1_IOAGR_SION_S0_Client_RdRsp_TimeSlot_Lower_ADDRESS 0x15e01418UL
#define SMN_IOHUB2_N5NBIO0_IOAGR_SION_S0_Client_RdRsp_TimeSlot_Lower_ADDRESS 0x15c01818UL
#define SMN_IOHUB2_N5NBIO1_IOAGR_SION_S0_Client_RdRsp_TimeSlot_Lower_ADDRESS 0x15e01818UL
#define SMN_IOHUB3_N0NBIO0_IOAGR_SION_S0_Client_RdRsp_TimeSlot_Lower_ADDRESS 0x1e100418UL
#define SMN_IOHUB3_N0NBIO1_IOAGR_SION_S0_Client_RdRsp_TimeSlot_Lower_ADDRESS 0x1e300418UL
#define SMN_IOHUB3_N1NBIO0_IOAGR_SION_S0_Client_RdRsp_TimeSlot_Lower_ADDRESS 0x1e100818UL
#define SMN_IOHUB3_N1NBIO1_IOAGR_SION_S0_Client_RdRsp_TimeSlot_Lower_ADDRESS 0x1e300818UL
#define SMN_IOHUB3_N2NBIO0_IOAGR_SION_S0_Client_RdRsp_TimeSlot_Lower_ADDRESS 0x1e100c18UL
#define SMN_IOHUB3_N2NBIO1_IOAGR_SION_S0_Client_RdRsp_TimeSlot_Lower_ADDRESS 0x1e300c18UL


/***********************************************************
* Register Name : IOAGR_SION_S0_Client_RdRsp_TimeSlot_Upper
************************************************************/

#define IOAGR_SION_S0_Client_RdRsp_TimeSlot_Upper_IOAGR_SION_S0_Client_RdRsp_TimeSlot_Upper_OFFSET 0
#define IOAGR_SION_S0_Client_RdRsp_TimeSlot_Upper_IOAGR_SION_S0_Client_RdRsp_TimeSlot_Upper_MASK 0xffffffff

typedef union {
  struct {
    UINT32           IOAGR_SION_S0_Client_RdRsp_TimeSlot_Upper:32;
  } Field;
  UINT32 Value;
} IOAGR_SION_S0_Client_RdRsp_TimeSlot_Upper_STRUCT;

#define SMN_IOAGR_SION_S0_Client_RdRsp_TimeSlot_Upper_ADDRESS         0x15b0041cUL
#define SMN_IOHUB0_N0NBIO0_IOAGR_SION_S0_Client_RdRsp_TimeSlot_Upper_ADDRESS 0x15b0041cUL
#define SMN_IOHUB0_N0NBIO1_IOAGR_SION_S0_Client_RdRsp_TimeSlot_Upper_ADDRESS 0x15d0041cUL
#define SMN_IOHUB0_N1NBIO0_IOAGR_SION_S0_Client_RdRsp_TimeSlot_Upper_ADDRESS 0x15b0081cUL
#define SMN_IOHUB0_N1NBIO1_IOAGR_SION_S0_Client_RdRsp_TimeSlot_Upper_ADDRESS 0x15d0081cUL
#define SMN_IOHUB0_N2NBIO0_IOAGR_SION_S0_Client_RdRsp_TimeSlot_Upper_ADDRESS 0x15b00c1cUL
#define SMN_IOHUB0_N2NBIO1_IOAGR_SION_S0_Client_RdRsp_TimeSlot_Upper_ADDRESS 0x15d00c1cUL
#define SMN_IOHUB0_N3NBIO0_IOAGR_SION_S0_Client_RdRsp_TimeSlot_Upper_ADDRESS 0x15b0101cUL
#define SMN_IOHUB0_N3NBIO1_IOAGR_SION_S0_Client_RdRsp_TimeSlot_Upper_ADDRESS 0x15d0101cUL
#define SMN_IOHUB0_N4NBIO0_IOAGR_SION_S0_Client_RdRsp_TimeSlot_Upper_ADDRESS 0x15b0141cUL
#define SMN_IOHUB0_N4NBIO1_IOAGR_SION_S0_Client_RdRsp_TimeSlot_Upper_ADDRESS 0x15d0141cUL
#define SMN_IOHUB0_N5NBIO0_IOAGR_SION_S0_Client_RdRsp_TimeSlot_Upper_ADDRESS 0x15b0181cUL
#define SMN_IOHUB0_N5NBIO1_IOAGR_SION_S0_Client_RdRsp_TimeSlot_Upper_ADDRESS 0x15d0181cUL
#define SMN_IOHUB1_N0NBIO0_IOAGR_SION_S0_Client_RdRsp_TimeSlot_Upper_ADDRESS 0x1e00041cUL
#define SMN_IOHUB1_N0NBIO1_IOAGR_SION_S0_Client_RdRsp_TimeSlot_Upper_ADDRESS 0x1e20041cUL
#define SMN_IOHUB1_N1NBIO0_IOAGR_SION_S0_Client_RdRsp_TimeSlot_Upper_ADDRESS 0x1e00081cUL
#define SMN_IOHUB1_N1NBIO1_IOAGR_SION_S0_Client_RdRsp_TimeSlot_Upper_ADDRESS 0x1e20081cUL
#define SMN_IOHUB1_N2NBIO0_IOAGR_SION_S0_Client_RdRsp_TimeSlot_Upper_ADDRESS 0x1e000c1cUL
#define SMN_IOHUB1_N2NBIO1_IOAGR_SION_S0_Client_RdRsp_TimeSlot_Upper_ADDRESS 0x1e200c1cUL
#define SMN_IOHUB2_N0NBIO0_IOAGR_SION_S0_Client_RdRsp_TimeSlot_Upper_ADDRESS 0x15c0041cUL
#define SMN_IOHUB2_N0NBIO1_IOAGR_SION_S0_Client_RdRsp_TimeSlot_Upper_ADDRESS 0x15e0041cUL
#define SMN_IOHUB2_N1NBIO0_IOAGR_SION_S0_Client_RdRsp_TimeSlot_Upper_ADDRESS 0x15c0081cUL
#define SMN_IOHUB2_N1NBIO1_IOAGR_SION_S0_Client_RdRsp_TimeSlot_Upper_ADDRESS 0x15e0081cUL
#define SMN_IOHUB2_N2NBIO0_IOAGR_SION_S0_Client_RdRsp_TimeSlot_Upper_ADDRESS 0x15c00c1cUL
#define SMN_IOHUB2_N2NBIO1_IOAGR_SION_S0_Client_RdRsp_TimeSlot_Upper_ADDRESS 0x15e00c1cUL
#define SMN_IOHUB2_N3NBIO0_IOAGR_SION_S0_Client_RdRsp_TimeSlot_Upper_ADDRESS 0x15c0101cUL
#define SMN_IOHUB2_N3NBIO1_IOAGR_SION_S0_Client_RdRsp_TimeSlot_Upper_ADDRESS 0x15e0101cUL
#define SMN_IOHUB2_N4NBIO0_IOAGR_SION_S0_Client_RdRsp_TimeSlot_Upper_ADDRESS 0x15c0141cUL
#define SMN_IOHUB2_N4NBIO1_IOAGR_SION_S0_Client_RdRsp_TimeSlot_Upper_ADDRESS 0x15e0141cUL
#define SMN_IOHUB2_N5NBIO0_IOAGR_SION_S0_Client_RdRsp_TimeSlot_Upper_ADDRESS 0x15c0181cUL
#define SMN_IOHUB2_N5NBIO1_IOAGR_SION_S0_Client_RdRsp_TimeSlot_Upper_ADDRESS 0x15e0181cUL
#define SMN_IOHUB3_N0NBIO0_IOAGR_SION_S0_Client_RdRsp_TimeSlot_Upper_ADDRESS 0x1e10041cUL
#define SMN_IOHUB3_N0NBIO1_IOAGR_SION_S0_Client_RdRsp_TimeSlot_Upper_ADDRESS 0x1e30041cUL
#define SMN_IOHUB3_N1NBIO0_IOAGR_SION_S0_Client_RdRsp_TimeSlot_Upper_ADDRESS 0x1e10081cUL
#define SMN_IOHUB3_N1NBIO1_IOAGR_SION_S0_Client_RdRsp_TimeSlot_Upper_ADDRESS 0x1e30081cUL
#define SMN_IOHUB3_N2NBIO0_IOAGR_SION_S0_Client_RdRsp_TimeSlot_Upper_ADDRESS 0x1e100c1cUL
#define SMN_IOHUB3_N2NBIO1_IOAGR_SION_S0_Client_RdRsp_TimeSlot_Upper_ADDRESS 0x1e300c1cUL


/***********************************************************
* Register Name : IOAGR_SION_S0_Client_Req_BurstTarget_Lower
************************************************************/

#define IOAGR_SION_S0_Client_Req_BurstTarget_Lower_IOAGR_SION_S0_Client_Req_BurstTarget_Lower_OFFSET 0
#define IOAGR_SION_S0_Client_Req_BurstTarget_Lower_IOAGR_SION_S0_Client_Req_BurstTarget_Lower_MASK 0xffffffff
#define IOAGR_SION_S0_Client_Req_BurstTarget_Lower_IOAGR_SION_S0_Client_Req_BurstTarget_Lower_DEFAULT     0x4040404

typedef union {
  struct {
    UINT32          IOAGR_SION_S0_Client_Req_BurstTarget_Lower:32;
  } Field;
  UINT32 Value;
} IOAGR_SION_S0_Client_Req_BurstTarget_Lower_STRUCT;

#define SMN_IOAGR_SION_S0_Client_Req_BurstTarget_Lower_ADDRESS        0x15b00400UL
#define SMN_IOHUB0_N0NBIO0_IOAGR_SION_S0_Client_Req_BurstTarget_Lower_ADDRESS 0x15b00400UL
#define SMN_IOHUB0_N0NBIO1_IOAGR_SION_S0_Client_Req_BurstTarget_Lower_ADDRESS 0x15d00400UL
#define SMN_IOHUB0_N1NBIO0_IOAGR_SION_S0_Client_Req_BurstTarget_Lower_ADDRESS 0x15b00800UL
#define SMN_IOHUB0_N1NBIO1_IOAGR_SION_S0_Client_Req_BurstTarget_Lower_ADDRESS 0x15d00800UL
#define SMN_IOHUB0_N2NBIO0_IOAGR_SION_S0_Client_Req_BurstTarget_Lower_ADDRESS 0x15b00c00UL
#define SMN_IOHUB0_N2NBIO1_IOAGR_SION_S0_Client_Req_BurstTarget_Lower_ADDRESS 0x15d00c00UL
#define SMN_IOHUB0_N3NBIO0_IOAGR_SION_S0_Client_Req_BurstTarget_Lower_ADDRESS 0x15b01000UL
#define SMN_IOHUB0_N3NBIO1_IOAGR_SION_S0_Client_Req_BurstTarget_Lower_ADDRESS 0x15d01000UL
#define SMN_IOHUB0_N4NBIO0_IOAGR_SION_S0_Client_Req_BurstTarget_Lower_ADDRESS 0x15b01400UL
#define SMN_IOHUB0_N4NBIO1_IOAGR_SION_S0_Client_Req_BurstTarget_Lower_ADDRESS 0x15d01400UL
#define SMN_IOHUB0_N5NBIO0_IOAGR_SION_S0_Client_Req_BurstTarget_Lower_ADDRESS 0x15b01800UL
#define SMN_IOHUB0_N5NBIO1_IOAGR_SION_S0_Client_Req_BurstTarget_Lower_ADDRESS 0x15d01800UL
#define SMN_IOHUB1_N0NBIO0_IOAGR_SION_S0_Client_Req_BurstTarget_Lower_ADDRESS 0x1e000400UL
#define SMN_IOHUB1_N0NBIO1_IOAGR_SION_S0_Client_Req_BurstTarget_Lower_ADDRESS 0x1e200400UL
#define SMN_IOHUB1_N1NBIO0_IOAGR_SION_S0_Client_Req_BurstTarget_Lower_ADDRESS 0x1e000800UL
#define SMN_IOHUB1_N1NBIO1_IOAGR_SION_S0_Client_Req_BurstTarget_Lower_ADDRESS 0x1e200800UL
#define SMN_IOHUB1_N2NBIO0_IOAGR_SION_S0_Client_Req_BurstTarget_Lower_ADDRESS 0x1e000c00UL
#define SMN_IOHUB1_N2NBIO1_IOAGR_SION_S0_Client_Req_BurstTarget_Lower_ADDRESS 0x1e200c00UL
#define SMN_IOHUB2_N0NBIO0_IOAGR_SION_S0_Client_Req_BurstTarget_Lower_ADDRESS 0x15c00400UL
#define SMN_IOHUB2_N0NBIO1_IOAGR_SION_S0_Client_Req_BurstTarget_Lower_ADDRESS 0x15e00400UL
#define SMN_IOHUB2_N1NBIO0_IOAGR_SION_S0_Client_Req_BurstTarget_Lower_ADDRESS 0x15c00800UL
#define SMN_IOHUB2_N1NBIO1_IOAGR_SION_S0_Client_Req_BurstTarget_Lower_ADDRESS 0x15e00800UL
#define SMN_IOHUB2_N2NBIO0_IOAGR_SION_S0_Client_Req_BurstTarget_Lower_ADDRESS 0x15c00c00UL
#define SMN_IOHUB2_N2NBIO1_IOAGR_SION_S0_Client_Req_BurstTarget_Lower_ADDRESS 0x15e00c00UL
#define SMN_IOHUB2_N3NBIO0_IOAGR_SION_S0_Client_Req_BurstTarget_Lower_ADDRESS 0x15c01000UL
#define SMN_IOHUB2_N3NBIO1_IOAGR_SION_S0_Client_Req_BurstTarget_Lower_ADDRESS 0x15e01000UL
#define SMN_IOHUB2_N4NBIO0_IOAGR_SION_S0_Client_Req_BurstTarget_Lower_ADDRESS 0x15c01400UL
#define SMN_IOHUB2_N4NBIO1_IOAGR_SION_S0_Client_Req_BurstTarget_Lower_ADDRESS 0x15e01400UL
#define SMN_IOHUB2_N5NBIO0_IOAGR_SION_S0_Client_Req_BurstTarget_Lower_ADDRESS 0x15c01800UL
#define SMN_IOHUB2_N5NBIO1_IOAGR_SION_S0_Client_Req_BurstTarget_Lower_ADDRESS 0x15e01800UL
#define SMN_IOHUB3_N0NBIO0_IOAGR_SION_S0_Client_Req_BurstTarget_Lower_ADDRESS 0x1e100400UL
#define SMN_IOHUB3_N0NBIO1_IOAGR_SION_S0_Client_Req_BurstTarget_Lower_ADDRESS 0x1e300400UL
#define SMN_IOHUB3_N1NBIO0_IOAGR_SION_S0_Client_Req_BurstTarget_Lower_ADDRESS 0x1e100800UL
#define SMN_IOHUB3_N1NBIO1_IOAGR_SION_S0_Client_Req_BurstTarget_Lower_ADDRESS 0x1e300800UL
#define SMN_IOHUB3_N2NBIO0_IOAGR_SION_S0_Client_Req_BurstTarget_Lower_ADDRESS 0x1e100c00UL
#define SMN_IOHUB3_N2NBIO1_IOAGR_SION_S0_Client_Req_BurstTarget_Lower_ADDRESS 0x1e300c00UL


/***********************************************************
* Register Name : IOAGR_SION_S0_Client_Req_BurstTarget_Upper
************************************************************/

#define IOAGR_SION_S0_Client_Req_BurstTarget_Upper_IOAGR_SION_S0_Client_Req_BurstTarget_Upper_OFFSET 0
#define IOAGR_SION_S0_Client_Req_BurstTarget_Upper_IOAGR_SION_S0_Client_Req_BurstTarget_Upper_MASK 0xffffffff
#define IOAGR_SION_S0_Client_Req_BurstTarget_Upper_IOAGR_SION_S0_Client_Req_BurstTarget_Upper_DEFAULT     0x4040404

typedef union {
  struct {
    UINT32          IOAGR_SION_S0_Client_Req_BurstTarget_Upper:32;
  } Field;
  UINT32 Value;
} IOAGR_SION_S0_Client_Req_BurstTarget_Upper_STRUCT;

#define SMN_IOAGR_SION_S0_Client_Req_BurstTarget_Upper_ADDRESS        0x15b00404UL
#define SMN_IOHUB0_N0NBIO0_IOAGR_SION_S0_Client_Req_BurstTarget_Upper_ADDRESS 0x15b00404UL
#define SMN_IOHUB0_N0NBIO1_IOAGR_SION_S0_Client_Req_BurstTarget_Upper_ADDRESS 0x15d00404UL
#define SMN_IOHUB0_N1NBIO0_IOAGR_SION_S0_Client_Req_BurstTarget_Upper_ADDRESS 0x15b00804UL
#define SMN_IOHUB0_N1NBIO1_IOAGR_SION_S0_Client_Req_BurstTarget_Upper_ADDRESS 0x15d00804UL
#define SMN_IOHUB0_N2NBIO0_IOAGR_SION_S0_Client_Req_BurstTarget_Upper_ADDRESS 0x15b00c04UL
#define SMN_IOHUB0_N2NBIO1_IOAGR_SION_S0_Client_Req_BurstTarget_Upper_ADDRESS 0x15d00c04UL
#define SMN_IOHUB0_N3NBIO0_IOAGR_SION_S0_Client_Req_BurstTarget_Upper_ADDRESS 0x15b01004UL
#define SMN_IOHUB0_N3NBIO1_IOAGR_SION_S0_Client_Req_BurstTarget_Upper_ADDRESS 0x15d01004UL
#define SMN_IOHUB0_N4NBIO0_IOAGR_SION_S0_Client_Req_BurstTarget_Upper_ADDRESS 0x15b01404UL
#define SMN_IOHUB0_N4NBIO1_IOAGR_SION_S0_Client_Req_BurstTarget_Upper_ADDRESS 0x15d01404UL
#define SMN_IOHUB0_N5NBIO0_IOAGR_SION_S0_Client_Req_BurstTarget_Upper_ADDRESS 0x15b01804UL
#define SMN_IOHUB0_N5NBIO1_IOAGR_SION_S0_Client_Req_BurstTarget_Upper_ADDRESS 0x15d01804UL
#define SMN_IOHUB1_N0NBIO0_IOAGR_SION_S0_Client_Req_BurstTarget_Upper_ADDRESS 0x1e000404UL
#define SMN_IOHUB1_N0NBIO1_IOAGR_SION_S0_Client_Req_BurstTarget_Upper_ADDRESS 0x1e200404UL
#define SMN_IOHUB1_N1NBIO0_IOAGR_SION_S0_Client_Req_BurstTarget_Upper_ADDRESS 0x1e000804UL
#define SMN_IOHUB1_N1NBIO1_IOAGR_SION_S0_Client_Req_BurstTarget_Upper_ADDRESS 0x1e200804UL
#define SMN_IOHUB1_N2NBIO0_IOAGR_SION_S0_Client_Req_BurstTarget_Upper_ADDRESS 0x1e000c04UL
#define SMN_IOHUB1_N2NBIO1_IOAGR_SION_S0_Client_Req_BurstTarget_Upper_ADDRESS 0x1e200c04UL
#define SMN_IOHUB2_N0NBIO0_IOAGR_SION_S0_Client_Req_BurstTarget_Upper_ADDRESS 0x15c00404UL
#define SMN_IOHUB2_N0NBIO1_IOAGR_SION_S0_Client_Req_BurstTarget_Upper_ADDRESS 0x15e00404UL
#define SMN_IOHUB2_N1NBIO0_IOAGR_SION_S0_Client_Req_BurstTarget_Upper_ADDRESS 0x15c00804UL
#define SMN_IOHUB2_N1NBIO1_IOAGR_SION_S0_Client_Req_BurstTarget_Upper_ADDRESS 0x15e00804UL
#define SMN_IOHUB2_N2NBIO0_IOAGR_SION_S0_Client_Req_BurstTarget_Upper_ADDRESS 0x15c00c04UL
#define SMN_IOHUB2_N2NBIO1_IOAGR_SION_S0_Client_Req_BurstTarget_Upper_ADDRESS 0x15e00c04UL
#define SMN_IOHUB2_N3NBIO0_IOAGR_SION_S0_Client_Req_BurstTarget_Upper_ADDRESS 0x15c01004UL
#define SMN_IOHUB2_N3NBIO1_IOAGR_SION_S0_Client_Req_BurstTarget_Upper_ADDRESS 0x15e01004UL
#define SMN_IOHUB2_N4NBIO0_IOAGR_SION_S0_Client_Req_BurstTarget_Upper_ADDRESS 0x15c01404UL
#define SMN_IOHUB2_N4NBIO1_IOAGR_SION_S0_Client_Req_BurstTarget_Upper_ADDRESS 0x15e01404UL
#define SMN_IOHUB2_N5NBIO0_IOAGR_SION_S0_Client_Req_BurstTarget_Upper_ADDRESS 0x15c01804UL
#define SMN_IOHUB2_N5NBIO1_IOAGR_SION_S0_Client_Req_BurstTarget_Upper_ADDRESS 0x15e01804UL
#define SMN_IOHUB3_N0NBIO0_IOAGR_SION_S0_Client_Req_BurstTarget_Upper_ADDRESS 0x1e100404UL
#define SMN_IOHUB3_N0NBIO1_IOAGR_SION_S0_Client_Req_BurstTarget_Upper_ADDRESS 0x1e300404UL
#define SMN_IOHUB3_N1NBIO0_IOAGR_SION_S0_Client_Req_BurstTarget_Upper_ADDRESS 0x1e100804UL
#define SMN_IOHUB3_N1NBIO1_IOAGR_SION_S0_Client_Req_BurstTarget_Upper_ADDRESS 0x1e300804UL
#define SMN_IOHUB3_N2NBIO0_IOAGR_SION_S0_Client_Req_BurstTarget_Upper_ADDRESS 0x1e100c04UL
#define SMN_IOHUB3_N2NBIO1_IOAGR_SION_S0_Client_Req_BurstTarget_Upper_ADDRESS 0x1e300c04UL


/***********************************************************
* Register Name : IOAGR_SION_S0_Client_Req_TimeSlot_Lower
************************************************************/

#define IOAGR_SION_S0_Client_Req_TimeSlot_Lower_IOAGR_SION_S0_Client_Req_TimeSlot_Lower_OFFSET 0
#define IOAGR_SION_S0_Client_Req_TimeSlot_Lower_IOAGR_SION_S0_Client_Req_TimeSlot_Lower_MASK 0xffffffff

typedef union {
  struct {
    UINT32             IOAGR_SION_S0_Client_Req_TimeSlot_Lower:32;
  } Field;
  UINT32 Value;
} IOAGR_SION_S0_Client_Req_TimeSlot_Lower_STRUCT;

#define SMN_IOAGR_SION_S0_Client_Req_TimeSlot_Lower_ADDRESS           0x15b00408UL
#define SMN_IOHUB0_N0NBIO0_IOAGR_SION_S0_Client_Req_TimeSlot_Lower_ADDRESS 0x15b00408UL
#define SMN_IOHUB0_N0NBIO1_IOAGR_SION_S0_Client_Req_TimeSlot_Lower_ADDRESS 0x15d00408UL
#define SMN_IOHUB0_N1NBIO0_IOAGR_SION_S0_Client_Req_TimeSlot_Lower_ADDRESS 0x15b00808UL
#define SMN_IOHUB0_N1NBIO1_IOAGR_SION_S0_Client_Req_TimeSlot_Lower_ADDRESS 0x15d00808UL
#define SMN_IOHUB0_N2NBIO0_IOAGR_SION_S0_Client_Req_TimeSlot_Lower_ADDRESS 0x15b00c08UL
#define SMN_IOHUB0_N2NBIO1_IOAGR_SION_S0_Client_Req_TimeSlot_Lower_ADDRESS 0x15d00c08UL
#define SMN_IOHUB0_N3NBIO0_IOAGR_SION_S0_Client_Req_TimeSlot_Lower_ADDRESS 0x15b01008UL
#define SMN_IOHUB0_N3NBIO1_IOAGR_SION_S0_Client_Req_TimeSlot_Lower_ADDRESS 0x15d01008UL
#define SMN_IOHUB0_N4NBIO0_IOAGR_SION_S0_Client_Req_TimeSlot_Lower_ADDRESS 0x15b01408UL
#define SMN_IOHUB0_N4NBIO1_IOAGR_SION_S0_Client_Req_TimeSlot_Lower_ADDRESS 0x15d01408UL
#define SMN_IOHUB0_N5NBIO0_IOAGR_SION_S0_Client_Req_TimeSlot_Lower_ADDRESS 0x15b01808UL
#define SMN_IOHUB0_N5NBIO1_IOAGR_SION_S0_Client_Req_TimeSlot_Lower_ADDRESS 0x15d01808UL
#define SMN_IOHUB1_N0NBIO0_IOAGR_SION_S0_Client_Req_TimeSlot_Lower_ADDRESS 0x1e000408UL
#define SMN_IOHUB1_N0NBIO1_IOAGR_SION_S0_Client_Req_TimeSlot_Lower_ADDRESS 0x1e200408UL
#define SMN_IOHUB1_N1NBIO0_IOAGR_SION_S0_Client_Req_TimeSlot_Lower_ADDRESS 0x1e000808UL
#define SMN_IOHUB1_N1NBIO1_IOAGR_SION_S0_Client_Req_TimeSlot_Lower_ADDRESS 0x1e200808UL
#define SMN_IOHUB1_N2NBIO0_IOAGR_SION_S0_Client_Req_TimeSlot_Lower_ADDRESS 0x1e000c08UL
#define SMN_IOHUB1_N2NBIO1_IOAGR_SION_S0_Client_Req_TimeSlot_Lower_ADDRESS 0x1e200c08UL
#define SMN_IOHUB2_N0NBIO0_IOAGR_SION_S0_Client_Req_TimeSlot_Lower_ADDRESS 0x15c00408UL
#define SMN_IOHUB2_N0NBIO1_IOAGR_SION_S0_Client_Req_TimeSlot_Lower_ADDRESS 0x15e00408UL
#define SMN_IOHUB2_N1NBIO0_IOAGR_SION_S0_Client_Req_TimeSlot_Lower_ADDRESS 0x15c00808UL
#define SMN_IOHUB2_N1NBIO1_IOAGR_SION_S0_Client_Req_TimeSlot_Lower_ADDRESS 0x15e00808UL
#define SMN_IOHUB2_N2NBIO0_IOAGR_SION_S0_Client_Req_TimeSlot_Lower_ADDRESS 0x15c00c08UL
#define SMN_IOHUB2_N2NBIO1_IOAGR_SION_S0_Client_Req_TimeSlot_Lower_ADDRESS 0x15e00c08UL
#define SMN_IOHUB2_N3NBIO0_IOAGR_SION_S0_Client_Req_TimeSlot_Lower_ADDRESS 0x15c01008UL
#define SMN_IOHUB2_N3NBIO1_IOAGR_SION_S0_Client_Req_TimeSlot_Lower_ADDRESS 0x15e01008UL
#define SMN_IOHUB2_N4NBIO0_IOAGR_SION_S0_Client_Req_TimeSlot_Lower_ADDRESS 0x15c01408UL
#define SMN_IOHUB2_N4NBIO1_IOAGR_SION_S0_Client_Req_TimeSlot_Lower_ADDRESS 0x15e01408UL
#define SMN_IOHUB2_N5NBIO0_IOAGR_SION_S0_Client_Req_TimeSlot_Lower_ADDRESS 0x15c01808UL
#define SMN_IOHUB2_N5NBIO1_IOAGR_SION_S0_Client_Req_TimeSlot_Lower_ADDRESS 0x15e01808UL
#define SMN_IOHUB3_N0NBIO0_IOAGR_SION_S0_Client_Req_TimeSlot_Lower_ADDRESS 0x1e100408UL
#define SMN_IOHUB3_N0NBIO1_IOAGR_SION_S0_Client_Req_TimeSlot_Lower_ADDRESS 0x1e300408UL
#define SMN_IOHUB3_N1NBIO0_IOAGR_SION_S0_Client_Req_TimeSlot_Lower_ADDRESS 0x1e100808UL
#define SMN_IOHUB3_N1NBIO1_IOAGR_SION_S0_Client_Req_TimeSlot_Lower_ADDRESS 0x1e300808UL
#define SMN_IOHUB3_N2NBIO0_IOAGR_SION_S0_Client_Req_TimeSlot_Lower_ADDRESS 0x1e100c08UL
#define SMN_IOHUB3_N2NBIO1_IOAGR_SION_S0_Client_Req_TimeSlot_Lower_ADDRESS 0x1e300c08UL


/***********************************************************
* Register Name : IOAGR_SION_S0_Client_Req_TimeSlot_Upper
************************************************************/

#define IOAGR_SION_S0_Client_Req_TimeSlot_Upper_IOAGR_SION_S0_Client_Req_TimeSlot_Upper_OFFSET 0
#define IOAGR_SION_S0_Client_Req_TimeSlot_Upper_IOAGR_SION_S0_Client_Req_TimeSlot_Upper_MASK 0xffffffff

typedef union {
  struct {
    UINT32             IOAGR_SION_S0_Client_Req_TimeSlot_Upper:32;
  } Field;
  UINT32 Value;
} IOAGR_SION_S0_Client_Req_TimeSlot_Upper_STRUCT;

#define SMN_IOAGR_SION_S0_Client_Req_TimeSlot_Upper_ADDRESS           0x15b0040cUL
#define SMN_IOHUB0_N0NBIO0_IOAGR_SION_S0_Client_Req_TimeSlot_Upper_ADDRESS 0x15b0040cUL
#define SMN_IOHUB0_N0NBIO1_IOAGR_SION_S0_Client_Req_TimeSlot_Upper_ADDRESS 0x15d0040cUL
#define SMN_IOHUB0_N1NBIO0_IOAGR_SION_S0_Client_Req_TimeSlot_Upper_ADDRESS 0x15b0080cUL
#define SMN_IOHUB0_N1NBIO1_IOAGR_SION_S0_Client_Req_TimeSlot_Upper_ADDRESS 0x15d0080cUL
#define SMN_IOHUB0_N2NBIO0_IOAGR_SION_S0_Client_Req_TimeSlot_Upper_ADDRESS 0x15b00c0cUL
#define SMN_IOHUB0_N2NBIO1_IOAGR_SION_S0_Client_Req_TimeSlot_Upper_ADDRESS 0x15d00c0cUL
#define SMN_IOHUB0_N3NBIO0_IOAGR_SION_S0_Client_Req_TimeSlot_Upper_ADDRESS 0x15b0100cUL
#define SMN_IOHUB0_N3NBIO1_IOAGR_SION_S0_Client_Req_TimeSlot_Upper_ADDRESS 0x15d0100cUL
#define SMN_IOHUB0_N4NBIO0_IOAGR_SION_S0_Client_Req_TimeSlot_Upper_ADDRESS 0x15b0140cUL
#define SMN_IOHUB0_N4NBIO1_IOAGR_SION_S0_Client_Req_TimeSlot_Upper_ADDRESS 0x15d0140cUL
#define SMN_IOHUB0_N5NBIO0_IOAGR_SION_S0_Client_Req_TimeSlot_Upper_ADDRESS 0x15b0180cUL
#define SMN_IOHUB0_N5NBIO1_IOAGR_SION_S0_Client_Req_TimeSlot_Upper_ADDRESS 0x15d0180cUL
#define SMN_IOHUB1_N0NBIO0_IOAGR_SION_S0_Client_Req_TimeSlot_Upper_ADDRESS 0x1e00040cUL
#define SMN_IOHUB1_N0NBIO1_IOAGR_SION_S0_Client_Req_TimeSlot_Upper_ADDRESS 0x1e20040cUL
#define SMN_IOHUB1_N1NBIO0_IOAGR_SION_S0_Client_Req_TimeSlot_Upper_ADDRESS 0x1e00080cUL
#define SMN_IOHUB1_N1NBIO1_IOAGR_SION_S0_Client_Req_TimeSlot_Upper_ADDRESS 0x1e20080cUL
#define SMN_IOHUB1_N2NBIO0_IOAGR_SION_S0_Client_Req_TimeSlot_Upper_ADDRESS 0x1e000c0cUL
#define SMN_IOHUB1_N2NBIO1_IOAGR_SION_S0_Client_Req_TimeSlot_Upper_ADDRESS 0x1e200c0cUL
#define SMN_IOHUB2_N0NBIO0_IOAGR_SION_S0_Client_Req_TimeSlot_Upper_ADDRESS 0x15c0040cUL
#define SMN_IOHUB2_N0NBIO1_IOAGR_SION_S0_Client_Req_TimeSlot_Upper_ADDRESS 0x15e0040cUL
#define SMN_IOHUB2_N1NBIO0_IOAGR_SION_S0_Client_Req_TimeSlot_Upper_ADDRESS 0x15c0080cUL
#define SMN_IOHUB2_N1NBIO1_IOAGR_SION_S0_Client_Req_TimeSlot_Upper_ADDRESS 0x15e0080cUL
#define SMN_IOHUB2_N2NBIO0_IOAGR_SION_S0_Client_Req_TimeSlot_Upper_ADDRESS 0x15c00c0cUL
#define SMN_IOHUB2_N2NBIO1_IOAGR_SION_S0_Client_Req_TimeSlot_Upper_ADDRESS 0x15e00c0cUL
#define SMN_IOHUB2_N3NBIO0_IOAGR_SION_S0_Client_Req_TimeSlot_Upper_ADDRESS 0x15c0100cUL
#define SMN_IOHUB2_N3NBIO1_IOAGR_SION_S0_Client_Req_TimeSlot_Upper_ADDRESS 0x15e0100cUL
#define SMN_IOHUB2_N4NBIO0_IOAGR_SION_S0_Client_Req_TimeSlot_Upper_ADDRESS 0x15c0140cUL
#define SMN_IOHUB2_N4NBIO1_IOAGR_SION_S0_Client_Req_TimeSlot_Upper_ADDRESS 0x15e0140cUL
#define SMN_IOHUB2_N5NBIO0_IOAGR_SION_S0_Client_Req_TimeSlot_Upper_ADDRESS 0x15c0180cUL
#define SMN_IOHUB2_N5NBIO1_IOAGR_SION_S0_Client_Req_TimeSlot_Upper_ADDRESS 0x15e0180cUL
#define SMN_IOHUB3_N0NBIO0_IOAGR_SION_S0_Client_Req_TimeSlot_Upper_ADDRESS 0x1e10040cUL
#define SMN_IOHUB3_N0NBIO1_IOAGR_SION_S0_Client_Req_TimeSlot_Upper_ADDRESS 0x1e30040cUL
#define SMN_IOHUB3_N1NBIO0_IOAGR_SION_S0_Client_Req_TimeSlot_Upper_ADDRESS 0x1e10080cUL
#define SMN_IOHUB3_N1NBIO1_IOAGR_SION_S0_Client_Req_TimeSlot_Upper_ADDRESS 0x1e30080cUL
#define SMN_IOHUB3_N2NBIO0_IOAGR_SION_S0_Client_Req_TimeSlot_Upper_ADDRESS 0x1e100c0cUL
#define SMN_IOHUB3_N2NBIO1_IOAGR_SION_S0_Client_Req_TimeSlot_Upper_ADDRESS 0x1e300c0cUL


/***********************************************************
* Register Name : IOAGR_SION_S0_Client_WrRsp_BurstTarget_Lower
************************************************************/

#define IOAGR_SION_S0_Client_WrRsp_BurstTarget_Lower_IOAGR_SION_S0_Client_WrRsp_BurstTarget_Lower_OFFSET 0
#define IOAGR_SION_S0_Client_WrRsp_BurstTarget_Lower_IOAGR_SION_S0_Client_WrRsp_BurstTarget_Lower_MASK 0xffffffff

typedef union {
  struct {
    UINT32        IOAGR_SION_S0_Client_WrRsp_BurstTarget_Lower:32;
  } Field;
  UINT32 Value;
} IOAGR_SION_S0_Client_WrRsp_BurstTarget_Lower_STRUCT;

#define SMN_IOAGR_SION_S0_Client_WrRsp_BurstTarget_Lower_ADDRESS      0x15b00420UL
#define SMN_IOHUB0_N0NBIO0_IOAGR_SION_S0_Client_WrRsp_BurstTarget_Lower_ADDRESS 0x15b00420UL
#define SMN_IOHUB0_N0NBIO1_IOAGR_SION_S0_Client_WrRsp_BurstTarget_Lower_ADDRESS 0x15d00420UL
#define SMN_IOHUB0_N1NBIO0_IOAGR_SION_S0_Client_WrRsp_BurstTarget_Lower_ADDRESS 0x15b00820UL
#define SMN_IOHUB0_N1NBIO1_IOAGR_SION_S0_Client_WrRsp_BurstTarget_Lower_ADDRESS 0x15d00820UL
#define SMN_IOHUB0_N2NBIO0_IOAGR_SION_S0_Client_WrRsp_BurstTarget_Lower_ADDRESS 0x15b00c20UL
#define SMN_IOHUB0_N2NBIO1_IOAGR_SION_S0_Client_WrRsp_BurstTarget_Lower_ADDRESS 0x15d00c20UL
#define SMN_IOHUB0_N3NBIO0_IOAGR_SION_S0_Client_WrRsp_BurstTarget_Lower_ADDRESS 0x15b01020UL
#define SMN_IOHUB0_N3NBIO1_IOAGR_SION_S0_Client_WrRsp_BurstTarget_Lower_ADDRESS 0x15d01020UL
#define SMN_IOHUB0_N4NBIO0_IOAGR_SION_S0_Client_WrRsp_BurstTarget_Lower_ADDRESS 0x15b01420UL
#define SMN_IOHUB0_N4NBIO1_IOAGR_SION_S0_Client_WrRsp_BurstTarget_Lower_ADDRESS 0x15d01420UL
#define SMN_IOHUB0_N5NBIO0_IOAGR_SION_S0_Client_WrRsp_BurstTarget_Lower_ADDRESS 0x15b01820UL
#define SMN_IOHUB0_N5NBIO1_IOAGR_SION_S0_Client_WrRsp_BurstTarget_Lower_ADDRESS 0x15d01820UL
#define SMN_IOHUB1_N0NBIO0_IOAGR_SION_S0_Client_WrRsp_BurstTarget_Lower_ADDRESS 0x1e000420UL
#define SMN_IOHUB1_N0NBIO1_IOAGR_SION_S0_Client_WrRsp_BurstTarget_Lower_ADDRESS 0x1e200420UL
#define SMN_IOHUB1_N1NBIO0_IOAGR_SION_S0_Client_WrRsp_BurstTarget_Lower_ADDRESS 0x1e000820UL
#define SMN_IOHUB1_N1NBIO1_IOAGR_SION_S0_Client_WrRsp_BurstTarget_Lower_ADDRESS 0x1e200820UL
#define SMN_IOHUB1_N2NBIO0_IOAGR_SION_S0_Client_WrRsp_BurstTarget_Lower_ADDRESS 0x1e000c20UL
#define SMN_IOHUB1_N2NBIO1_IOAGR_SION_S0_Client_WrRsp_BurstTarget_Lower_ADDRESS 0x1e200c20UL
#define SMN_IOHUB2_N0NBIO0_IOAGR_SION_S0_Client_WrRsp_BurstTarget_Lower_ADDRESS 0x15c00420UL
#define SMN_IOHUB2_N0NBIO1_IOAGR_SION_S0_Client_WrRsp_BurstTarget_Lower_ADDRESS 0x15e00420UL
#define SMN_IOHUB2_N1NBIO0_IOAGR_SION_S0_Client_WrRsp_BurstTarget_Lower_ADDRESS 0x15c00820UL
#define SMN_IOHUB2_N1NBIO1_IOAGR_SION_S0_Client_WrRsp_BurstTarget_Lower_ADDRESS 0x15e00820UL
#define SMN_IOHUB2_N2NBIO0_IOAGR_SION_S0_Client_WrRsp_BurstTarget_Lower_ADDRESS 0x15c00c20UL
#define SMN_IOHUB2_N2NBIO1_IOAGR_SION_S0_Client_WrRsp_BurstTarget_Lower_ADDRESS 0x15e00c20UL
#define SMN_IOHUB2_N3NBIO0_IOAGR_SION_S0_Client_WrRsp_BurstTarget_Lower_ADDRESS 0x15c01020UL
#define SMN_IOHUB2_N3NBIO1_IOAGR_SION_S0_Client_WrRsp_BurstTarget_Lower_ADDRESS 0x15e01020UL
#define SMN_IOHUB2_N4NBIO0_IOAGR_SION_S0_Client_WrRsp_BurstTarget_Lower_ADDRESS 0x15c01420UL
#define SMN_IOHUB2_N4NBIO1_IOAGR_SION_S0_Client_WrRsp_BurstTarget_Lower_ADDRESS 0x15e01420UL
#define SMN_IOHUB2_N5NBIO0_IOAGR_SION_S0_Client_WrRsp_BurstTarget_Lower_ADDRESS 0x15c01820UL
#define SMN_IOHUB2_N5NBIO1_IOAGR_SION_S0_Client_WrRsp_BurstTarget_Lower_ADDRESS 0x15e01820UL
#define SMN_IOHUB3_N0NBIO0_IOAGR_SION_S0_Client_WrRsp_BurstTarget_Lower_ADDRESS 0x1e100420UL
#define SMN_IOHUB3_N0NBIO1_IOAGR_SION_S0_Client_WrRsp_BurstTarget_Lower_ADDRESS 0x1e300420UL
#define SMN_IOHUB3_N1NBIO0_IOAGR_SION_S0_Client_WrRsp_BurstTarget_Lower_ADDRESS 0x1e100820UL
#define SMN_IOHUB3_N1NBIO1_IOAGR_SION_S0_Client_WrRsp_BurstTarget_Lower_ADDRESS 0x1e300820UL
#define SMN_IOHUB3_N2NBIO0_IOAGR_SION_S0_Client_WrRsp_BurstTarget_Lower_ADDRESS 0x1e100c20UL
#define SMN_IOHUB3_N2NBIO1_IOAGR_SION_S0_Client_WrRsp_BurstTarget_Lower_ADDRESS 0x1e300c20UL


/***********************************************************
* Register Name : IOAGR_SION_S0_Client_WrRsp_BurstTarget_Upper
************************************************************/

#define IOAGR_SION_S0_Client_WrRsp_BurstTarget_Upper_IOAGR_SION_S0_Client_WrRsp_BurstTarget_Upper_OFFSET 0
#define IOAGR_SION_S0_Client_WrRsp_BurstTarget_Upper_IOAGR_SION_S0_Client_WrRsp_BurstTarget_Upper_MASK 0xffffffff

typedef union {
  struct {
    UINT32        IOAGR_SION_S0_Client_WrRsp_BurstTarget_Upper:32;
  } Field;
  UINT32 Value;
} IOAGR_SION_S0_Client_WrRsp_BurstTarget_Upper_STRUCT;

#define SMN_IOAGR_SION_S0_Client_WrRsp_BurstTarget_Upper_ADDRESS      0x15b00424UL
#define SMN_IOHUB0_N0NBIO0_IOAGR_SION_S0_Client_WrRsp_BurstTarget_Upper_ADDRESS 0x15b00424UL
#define SMN_IOHUB0_N0NBIO1_IOAGR_SION_S0_Client_WrRsp_BurstTarget_Upper_ADDRESS 0x15d00424UL
#define SMN_IOHUB0_N1NBIO0_IOAGR_SION_S0_Client_WrRsp_BurstTarget_Upper_ADDRESS 0x15b00824UL
#define SMN_IOHUB0_N1NBIO1_IOAGR_SION_S0_Client_WrRsp_BurstTarget_Upper_ADDRESS 0x15d00824UL
#define SMN_IOHUB0_N2NBIO0_IOAGR_SION_S0_Client_WrRsp_BurstTarget_Upper_ADDRESS 0x15b00c24UL
#define SMN_IOHUB0_N2NBIO1_IOAGR_SION_S0_Client_WrRsp_BurstTarget_Upper_ADDRESS 0x15d00c24UL
#define SMN_IOHUB0_N3NBIO0_IOAGR_SION_S0_Client_WrRsp_BurstTarget_Upper_ADDRESS 0x15b01024UL
#define SMN_IOHUB0_N3NBIO1_IOAGR_SION_S0_Client_WrRsp_BurstTarget_Upper_ADDRESS 0x15d01024UL
#define SMN_IOHUB0_N4NBIO0_IOAGR_SION_S0_Client_WrRsp_BurstTarget_Upper_ADDRESS 0x15b01424UL
#define SMN_IOHUB0_N4NBIO1_IOAGR_SION_S0_Client_WrRsp_BurstTarget_Upper_ADDRESS 0x15d01424UL
#define SMN_IOHUB0_N5NBIO0_IOAGR_SION_S0_Client_WrRsp_BurstTarget_Upper_ADDRESS 0x15b01824UL
#define SMN_IOHUB0_N5NBIO1_IOAGR_SION_S0_Client_WrRsp_BurstTarget_Upper_ADDRESS 0x15d01824UL
#define SMN_IOHUB1_N0NBIO0_IOAGR_SION_S0_Client_WrRsp_BurstTarget_Upper_ADDRESS 0x1e000424UL
#define SMN_IOHUB1_N0NBIO1_IOAGR_SION_S0_Client_WrRsp_BurstTarget_Upper_ADDRESS 0x1e200424UL
#define SMN_IOHUB1_N1NBIO0_IOAGR_SION_S0_Client_WrRsp_BurstTarget_Upper_ADDRESS 0x1e000824UL
#define SMN_IOHUB1_N1NBIO1_IOAGR_SION_S0_Client_WrRsp_BurstTarget_Upper_ADDRESS 0x1e200824UL
#define SMN_IOHUB1_N2NBIO0_IOAGR_SION_S0_Client_WrRsp_BurstTarget_Upper_ADDRESS 0x1e000c24UL
#define SMN_IOHUB1_N2NBIO1_IOAGR_SION_S0_Client_WrRsp_BurstTarget_Upper_ADDRESS 0x1e200c24UL
#define SMN_IOHUB2_N0NBIO0_IOAGR_SION_S0_Client_WrRsp_BurstTarget_Upper_ADDRESS 0x15c00424UL
#define SMN_IOHUB2_N0NBIO1_IOAGR_SION_S0_Client_WrRsp_BurstTarget_Upper_ADDRESS 0x15e00424UL
#define SMN_IOHUB2_N1NBIO0_IOAGR_SION_S0_Client_WrRsp_BurstTarget_Upper_ADDRESS 0x15c00824UL
#define SMN_IOHUB2_N1NBIO1_IOAGR_SION_S0_Client_WrRsp_BurstTarget_Upper_ADDRESS 0x15e00824UL
#define SMN_IOHUB2_N2NBIO0_IOAGR_SION_S0_Client_WrRsp_BurstTarget_Upper_ADDRESS 0x15c00c24UL
#define SMN_IOHUB2_N2NBIO1_IOAGR_SION_S0_Client_WrRsp_BurstTarget_Upper_ADDRESS 0x15e00c24UL
#define SMN_IOHUB2_N3NBIO0_IOAGR_SION_S0_Client_WrRsp_BurstTarget_Upper_ADDRESS 0x15c01024UL
#define SMN_IOHUB2_N3NBIO1_IOAGR_SION_S0_Client_WrRsp_BurstTarget_Upper_ADDRESS 0x15e01024UL
#define SMN_IOHUB2_N4NBIO0_IOAGR_SION_S0_Client_WrRsp_BurstTarget_Upper_ADDRESS 0x15c01424UL
#define SMN_IOHUB2_N4NBIO1_IOAGR_SION_S0_Client_WrRsp_BurstTarget_Upper_ADDRESS 0x15e01424UL
#define SMN_IOHUB2_N5NBIO0_IOAGR_SION_S0_Client_WrRsp_BurstTarget_Upper_ADDRESS 0x15c01824UL
#define SMN_IOHUB2_N5NBIO1_IOAGR_SION_S0_Client_WrRsp_BurstTarget_Upper_ADDRESS 0x15e01824UL
#define SMN_IOHUB3_N0NBIO0_IOAGR_SION_S0_Client_WrRsp_BurstTarget_Upper_ADDRESS 0x1e100424UL
#define SMN_IOHUB3_N0NBIO1_IOAGR_SION_S0_Client_WrRsp_BurstTarget_Upper_ADDRESS 0x1e300424UL
#define SMN_IOHUB3_N1NBIO0_IOAGR_SION_S0_Client_WrRsp_BurstTarget_Upper_ADDRESS 0x1e100824UL
#define SMN_IOHUB3_N1NBIO1_IOAGR_SION_S0_Client_WrRsp_BurstTarget_Upper_ADDRESS 0x1e300824UL
#define SMN_IOHUB3_N2NBIO0_IOAGR_SION_S0_Client_WrRsp_BurstTarget_Upper_ADDRESS 0x1e100c24UL
#define SMN_IOHUB3_N2NBIO1_IOAGR_SION_S0_Client_WrRsp_BurstTarget_Upper_ADDRESS 0x1e300c24UL


/***********************************************************
* Register Name : IOAGR_SION_S0_Client_WrRsp_TimeSlot_Lower
************************************************************/

#define IOAGR_SION_S0_Client_WrRsp_TimeSlot_Lower_IOAGR_SION_S0_Client_WrRsp_TimeSlot_Lower_OFFSET 0
#define IOAGR_SION_S0_Client_WrRsp_TimeSlot_Lower_IOAGR_SION_S0_Client_WrRsp_TimeSlot_Lower_MASK 0xffffffff

typedef union {
  struct {
    UINT32           IOAGR_SION_S0_Client_WrRsp_TimeSlot_Lower:32;
  } Field;
  UINT32 Value;
} IOAGR_SION_S0_Client_WrRsp_TimeSlot_Lower_STRUCT;

#define SMN_IOAGR_SION_S0_Client_WrRsp_TimeSlot_Lower_ADDRESS         0x15b00428UL
#define SMN_IOHUB0_N0NBIO0_IOAGR_SION_S0_Client_WrRsp_TimeSlot_Lower_ADDRESS 0x15b00428UL
#define SMN_IOHUB0_N0NBIO1_IOAGR_SION_S0_Client_WrRsp_TimeSlot_Lower_ADDRESS 0x15d00428UL
#define SMN_IOHUB0_N1NBIO0_IOAGR_SION_S0_Client_WrRsp_TimeSlot_Lower_ADDRESS 0x15b00828UL
#define SMN_IOHUB0_N1NBIO1_IOAGR_SION_S0_Client_WrRsp_TimeSlot_Lower_ADDRESS 0x15d00828UL
#define SMN_IOHUB0_N2NBIO0_IOAGR_SION_S0_Client_WrRsp_TimeSlot_Lower_ADDRESS 0x15b00c28UL
#define SMN_IOHUB0_N2NBIO1_IOAGR_SION_S0_Client_WrRsp_TimeSlot_Lower_ADDRESS 0x15d00c28UL
#define SMN_IOHUB0_N3NBIO0_IOAGR_SION_S0_Client_WrRsp_TimeSlot_Lower_ADDRESS 0x15b01028UL
#define SMN_IOHUB0_N3NBIO1_IOAGR_SION_S0_Client_WrRsp_TimeSlot_Lower_ADDRESS 0x15d01028UL
#define SMN_IOHUB0_N4NBIO0_IOAGR_SION_S0_Client_WrRsp_TimeSlot_Lower_ADDRESS 0x15b01428UL
#define SMN_IOHUB0_N4NBIO1_IOAGR_SION_S0_Client_WrRsp_TimeSlot_Lower_ADDRESS 0x15d01428UL
#define SMN_IOHUB0_N5NBIO0_IOAGR_SION_S0_Client_WrRsp_TimeSlot_Lower_ADDRESS 0x15b01828UL
#define SMN_IOHUB0_N5NBIO1_IOAGR_SION_S0_Client_WrRsp_TimeSlot_Lower_ADDRESS 0x15d01828UL
#define SMN_IOHUB1_N0NBIO0_IOAGR_SION_S0_Client_WrRsp_TimeSlot_Lower_ADDRESS 0x1e000428UL
#define SMN_IOHUB1_N0NBIO1_IOAGR_SION_S0_Client_WrRsp_TimeSlot_Lower_ADDRESS 0x1e200428UL
#define SMN_IOHUB1_N1NBIO0_IOAGR_SION_S0_Client_WrRsp_TimeSlot_Lower_ADDRESS 0x1e000828UL
#define SMN_IOHUB1_N1NBIO1_IOAGR_SION_S0_Client_WrRsp_TimeSlot_Lower_ADDRESS 0x1e200828UL
#define SMN_IOHUB1_N2NBIO0_IOAGR_SION_S0_Client_WrRsp_TimeSlot_Lower_ADDRESS 0x1e000c28UL
#define SMN_IOHUB1_N2NBIO1_IOAGR_SION_S0_Client_WrRsp_TimeSlot_Lower_ADDRESS 0x1e200c28UL
#define SMN_IOHUB2_N0NBIO0_IOAGR_SION_S0_Client_WrRsp_TimeSlot_Lower_ADDRESS 0x15c00428UL
#define SMN_IOHUB2_N0NBIO1_IOAGR_SION_S0_Client_WrRsp_TimeSlot_Lower_ADDRESS 0x15e00428UL
#define SMN_IOHUB2_N1NBIO0_IOAGR_SION_S0_Client_WrRsp_TimeSlot_Lower_ADDRESS 0x15c00828UL
#define SMN_IOHUB2_N1NBIO1_IOAGR_SION_S0_Client_WrRsp_TimeSlot_Lower_ADDRESS 0x15e00828UL
#define SMN_IOHUB2_N2NBIO0_IOAGR_SION_S0_Client_WrRsp_TimeSlot_Lower_ADDRESS 0x15c00c28UL
#define SMN_IOHUB2_N2NBIO1_IOAGR_SION_S0_Client_WrRsp_TimeSlot_Lower_ADDRESS 0x15e00c28UL
#define SMN_IOHUB2_N3NBIO0_IOAGR_SION_S0_Client_WrRsp_TimeSlot_Lower_ADDRESS 0x15c01028UL
#define SMN_IOHUB2_N3NBIO1_IOAGR_SION_S0_Client_WrRsp_TimeSlot_Lower_ADDRESS 0x15e01028UL
#define SMN_IOHUB2_N4NBIO0_IOAGR_SION_S0_Client_WrRsp_TimeSlot_Lower_ADDRESS 0x15c01428UL
#define SMN_IOHUB2_N4NBIO1_IOAGR_SION_S0_Client_WrRsp_TimeSlot_Lower_ADDRESS 0x15e01428UL
#define SMN_IOHUB2_N5NBIO0_IOAGR_SION_S0_Client_WrRsp_TimeSlot_Lower_ADDRESS 0x15c01828UL
#define SMN_IOHUB2_N5NBIO1_IOAGR_SION_S0_Client_WrRsp_TimeSlot_Lower_ADDRESS 0x15e01828UL
#define SMN_IOHUB3_N0NBIO0_IOAGR_SION_S0_Client_WrRsp_TimeSlot_Lower_ADDRESS 0x1e100428UL
#define SMN_IOHUB3_N0NBIO1_IOAGR_SION_S0_Client_WrRsp_TimeSlot_Lower_ADDRESS 0x1e300428UL
#define SMN_IOHUB3_N1NBIO0_IOAGR_SION_S0_Client_WrRsp_TimeSlot_Lower_ADDRESS 0x1e100828UL
#define SMN_IOHUB3_N1NBIO1_IOAGR_SION_S0_Client_WrRsp_TimeSlot_Lower_ADDRESS 0x1e300828UL
#define SMN_IOHUB3_N2NBIO0_IOAGR_SION_S0_Client_WrRsp_TimeSlot_Lower_ADDRESS 0x1e100c28UL
#define SMN_IOHUB3_N2NBIO1_IOAGR_SION_S0_Client_WrRsp_TimeSlot_Lower_ADDRESS 0x1e300c28UL


/***********************************************************
* Register Name : IOAGR_SION_S0_Client_WrRsp_TimeSlot_Upper
************************************************************/

#define IOAGR_SION_S0_Client_WrRsp_TimeSlot_Upper_IOAGR_SION_S0_Client_WrRsp_TimeSlot_Upper_OFFSET 0
#define IOAGR_SION_S0_Client_WrRsp_TimeSlot_Upper_IOAGR_SION_S0_Client_WrRsp_TimeSlot_Upper_MASK 0xffffffff

typedef union {
  struct {
    UINT32           IOAGR_SION_S0_Client_WrRsp_TimeSlot_Upper:32;
  } Field;
  UINT32 Value;
} IOAGR_SION_S0_Client_WrRsp_TimeSlot_Upper_STRUCT;

#define SMN_IOAGR_SION_S0_Client_WrRsp_TimeSlot_Upper_ADDRESS         0x15b0042cUL
#define SMN_IOHUB0_N0NBIO0_IOAGR_SION_S0_Client_WrRsp_TimeSlot_Upper_ADDRESS 0x15b0042cUL
#define SMN_IOHUB0_N0NBIO1_IOAGR_SION_S0_Client_WrRsp_TimeSlot_Upper_ADDRESS 0x15d0042cUL
#define SMN_IOHUB0_N1NBIO0_IOAGR_SION_S0_Client_WrRsp_TimeSlot_Upper_ADDRESS 0x15b0082cUL
#define SMN_IOHUB0_N1NBIO1_IOAGR_SION_S0_Client_WrRsp_TimeSlot_Upper_ADDRESS 0x15d0082cUL
#define SMN_IOHUB0_N2NBIO0_IOAGR_SION_S0_Client_WrRsp_TimeSlot_Upper_ADDRESS 0x15b00c2cUL
#define SMN_IOHUB0_N2NBIO1_IOAGR_SION_S0_Client_WrRsp_TimeSlot_Upper_ADDRESS 0x15d00c2cUL
#define SMN_IOHUB0_N3NBIO0_IOAGR_SION_S0_Client_WrRsp_TimeSlot_Upper_ADDRESS 0x15b0102cUL
#define SMN_IOHUB0_N3NBIO1_IOAGR_SION_S0_Client_WrRsp_TimeSlot_Upper_ADDRESS 0x15d0102cUL
#define SMN_IOHUB0_N4NBIO0_IOAGR_SION_S0_Client_WrRsp_TimeSlot_Upper_ADDRESS 0x15b0142cUL
#define SMN_IOHUB0_N4NBIO1_IOAGR_SION_S0_Client_WrRsp_TimeSlot_Upper_ADDRESS 0x15d0142cUL
#define SMN_IOHUB0_N5NBIO0_IOAGR_SION_S0_Client_WrRsp_TimeSlot_Upper_ADDRESS 0x15b0182cUL
#define SMN_IOHUB0_N5NBIO1_IOAGR_SION_S0_Client_WrRsp_TimeSlot_Upper_ADDRESS 0x15d0182cUL
#define SMN_IOHUB1_N0NBIO0_IOAGR_SION_S0_Client_WrRsp_TimeSlot_Upper_ADDRESS 0x1e00042cUL
#define SMN_IOHUB1_N0NBIO1_IOAGR_SION_S0_Client_WrRsp_TimeSlot_Upper_ADDRESS 0x1e20042cUL
#define SMN_IOHUB1_N1NBIO0_IOAGR_SION_S0_Client_WrRsp_TimeSlot_Upper_ADDRESS 0x1e00082cUL
#define SMN_IOHUB1_N1NBIO1_IOAGR_SION_S0_Client_WrRsp_TimeSlot_Upper_ADDRESS 0x1e20082cUL
#define SMN_IOHUB1_N2NBIO0_IOAGR_SION_S0_Client_WrRsp_TimeSlot_Upper_ADDRESS 0x1e000c2cUL
#define SMN_IOHUB1_N2NBIO1_IOAGR_SION_S0_Client_WrRsp_TimeSlot_Upper_ADDRESS 0x1e200c2cUL
#define SMN_IOHUB2_N0NBIO0_IOAGR_SION_S0_Client_WrRsp_TimeSlot_Upper_ADDRESS 0x15c0042cUL
#define SMN_IOHUB2_N0NBIO1_IOAGR_SION_S0_Client_WrRsp_TimeSlot_Upper_ADDRESS 0x15e0042cUL
#define SMN_IOHUB2_N1NBIO0_IOAGR_SION_S0_Client_WrRsp_TimeSlot_Upper_ADDRESS 0x15c0082cUL
#define SMN_IOHUB2_N1NBIO1_IOAGR_SION_S0_Client_WrRsp_TimeSlot_Upper_ADDRESS 0x15e0082cUL
#define SMN_IOHUB2_N2NBIO0_IOAGR_SION_S0_Client_WrRsp_TimeSlot_Upper_ADDRESS 0x15c00c2cUL
#define SMN_IOHUB2_N2NBIO1_IOAGR_SION_S0_Client_WrRsp_TimeSlot_Upper_ADDRESS 0x15e00c2cUL
#define SMN_IOHUB2_N3NBIO0_IOAGR_SION_S0_Client_WrRsp_TimeSlot_Upper_ADDRESS 0x15c0102cUL
#define SMN_IOHUB2_N3NBIO1_IOAGR_SION_S0_Client_WrRsp_TimeSlot_Upper_ADDRESS 0x15e0102cUL
#define SMN_IOHUB2_N4NBIO0_IOAGR_SION_S0_Client_WrRsp_TimeSlot_Upper_ADDRESS 0x15c0142cUL
#define SMN_IOHUB2_N4NBIO1_IOAGR_SION_S0_Client_WrRsp_TimeSlot_Upper_ADDRESS 0x15e0142cUL
#define SMN_IOHUB2_N5NBIO0_IOAGR_SION_S0_Client_WrRsp_TimeSlot_Upper_ADDRESS 0x15c0182cUL
#define SMN_IOHUB2_N5NBIO1_IOAGR_SION_S0_Client_WrRsp_TimeSlot_Upper_ADDRESS 0x15e0182cUL
#define SMN_IOHUB3_N0NBIO0_IOAGR_SION_S0_Client_WrRsp_TimeSlot_Upper_ADDRESS 0x1e10042cUL
#define SMN_IOHUB3_N0NBIO1_IOAGR_SION_S0_Client_WrRsp_TimeSlot_Upper_ADDRESS 0x1e30042cUL
#define SMN_IOHUB3_N1NBIO0_IOAGR_SION_S0_Client_WrRsp_TimeSlot_Upper_ADDRESS 0x1e10082cUL
#define SMN_IOHUB3_N1NBIO1_IOAGR_SION_S0_Client_WrRsp_TimeSlot_Upper_ADDRESS 0x1e30082cUL
#define SMN_IOHUB3_N2NBIO0_IOAGR_SION_S0_Client_WrRsp_TimeSlot_Upper_ADDRESS 0x1e100c2cUL
#define SMN_IOHUB3_N2NBIO1_IOAGR_SION_S0_Client_WrRsp_TimeSlot_Upper_ADDRESS 0x1e300c2cUL


/***********************************************************
* Register Name : IOAGR_SION_S1_Client_RdRsp_BurstTarget_Lower
************************************************************/

#define IOAGR_SION_S1_Client_RdRsp_BurstTarget_Lower_IOAGR_SION_S1_Client_RdRsp_BurstTarget_Lower_OFFSET 0
#define IOAGR_SION_S1_Client_RdRsp_BurstTarget_Lower_IOAGR_SION_S1_Client_RdRsp_BurstTarget_Lower_MASK 0xffffffff

typedef union {
  struct {
    UINT32        IOAGR_SION_S1_Client_RdRsp_BurstTarget_Lower:32;
  } Field;
  UINT32 Value;
} IOAGR_SION_S1_Client_RdRsp_BurstTarget_Lower_STRUCT;

#define SMN_IOAGR_SION_S1_Client_RdRsp_BurstTarget_Lower_ADDRESS      0x15b00440UL
#define SMN_IOHUB0_N0NBIO0_IOAGR_SION_S1_Client_RdRsp_BurstTarget_Lower_ADDRESS 0x15b00440UL
#define SMN_IOHUB0_N0NBIO1_IOAGR_SION_S1_Client_RdRsp_BurstTarget_Lower_ADDRESS 0x15d00440UL
#define SMN_IOHUB0_N1NBIO0_IOAGR_SION_S1_Client_RdRsp_BurstTarget_Lower_ADDRESS 0x15b00840UL
#define SMN_IOHUB0_N1NBIO1_IOAGR_SION_S1_Client_RdRsp_BurstTarget_Lower_ADDRESS 0x15d00840UL
#define SMN_IOHUB0_N2NBIO0_IOAGR_SION_S1_Client_RdRsp_BurstTarget_Lower_ADDRESS 0x15b00c40UL
#define SMN_IOHUB0_N2NBIO1_IOAGR_SION_S1_Client_RdRsp_BurstTarget_Lower_ADDRESS 0x15d00c40UL
#define SMN_IOHUB0_N3NBIO0_IOAGR_SION_S1_Client_RdRsp_BurstTarget_Lower_ADDRESS 0x15b01040UL
#define SMN_IOHUB0_N3NBIO1_IOAGR_SION_S1_Client_RdRsp_BurstTarget_Lower_ADDRESS 0x15d01040UL
#define SMN_IOHUB0_N4NBIO0_IOAGR_SION_S1_Client_RdRsp_BurstTarget_Lower_ADDRESS 0x15b01440UL
#define SMN_IOHUB0_N4NBIO1_IOAGR_SION_S1_Client_RdRsp_BurstTarget_Lower_ADDRESS 0x15d01440UL
#define SMN_IOHUB0_N5NBIO0_IOAGR_SION_S1_Client_RdRsp_BurstTarget_Lower_ADDRESS 0x15b01840UL
#define SMN_IOHUB0_N5NBIO1_IOAGR_SION_S1_Client_RdRsp_BurstTarget_Lower_ADDRESS 0x15d01840UL
#define SMN_IOHUB1_N0NBIO0_IOAGR_SION_S1_Client_RdRsp_BurstTarget_Lower_ADDRESS 0x1e000440UL
#define SMN_IOHUB1_N0NBIO1_IOAGR_SION_S1_Client_RdRsp_BurstTarget_Lower_ADDRESS 0x1e200440UL
#define SMN_IOHUB1_N1NBIO0_IOAGR_SION_S1_Client_RdRsp_BurstTarget_Lower_ADDRESS 0x1e000840UL
#define SMN_IOHUB1_N1NBIO1_IOAGR_SION_S1_Client_RdRsp_BurstTarget_Lower_ADDRESS 0x1e200840UL
#define SMN_IOHUB1_N2NBIO0_IOAGR_SION_S1_Client_RdRsp_BurstTarget_Lower_ADDRESS 0x1e000c40UL
#define SMN_IOHUB1_N2NBIO1_IOAGR_SION_S1_Client_RdRsp_BurstTarget_Lower_ADDRESS 0x1e200c40UL
#define SMN_IOHUB2_N0NBIO0_IOAGR_SION_S1_Client_RdRsp_BurstTarget_Lower_ADDRESS 0x15c00440UL
#define SMN_IOHUB2_N0NBIO1_IOAGR_SION_S1_Client_RdRsp_BurstTarget_Lower_ADDRESS 0x15e00440UL
#define SMN_IOHUB2_N1NBIO0_IOAGR_SION_S1_Client_RdRsp_BurstTarget_Lower_ADDRESS 0x15c00840UL
#define SMN_IOHUB2_N1NBIO1_IOAGR_SION_S1_Client_RdRsp_BurstTarget_Lower_ADDRESS 0x15e00840UL
#define SMN_IOHUB2_N2NBIO0_IOAGR_SION_S1_Client_RdRsp_BurstTarget_Lower_ADDRESS 0x15c00c40UL
#define SMN_IOHUB2_N2NBIO1_IOAGR_SION_S1_Client_RdRsp_BurstTarget_Lower_ADDRESS 0x15e00c40UL
#define SMN_IOHUB2_N3NBIO0_IOAGR_SION_S1_Client_RdRsp_BurstTarget_Lower_ADDRESS 0x15c01040UL
#define SMN_IOHUB2_N3NBIO1_IOAGR_SION_S1_Client_RdRsp_BurstTarget_Lower_ADDRESS 0x15e01040UL
#define SMN_IOHUB2_N4NBIO0_IOAGR_SION_S1_Client_RdRsp_BurstTarget_Lower_ADDRESS 0x15c01440UL
#define SMN_IOHUB2_N4NBIO1_IOAGR_SION_S1_Client_RdRsp_BurstTarget_Lower_ADDRESS 0x15e01440UL
#define SMN_IOHUB2_N5NBIO0_IOAGR_SION_S1_Client_RdRsp_BurstTarget_Lower_ADDRESS 0x15c01840UL
#define SMN_IOHUB2_N5NBIO1_IOAGR_SION_S1_Client_RdRsp_BurstTarget_Lower_ADDRESS 0x15e01840UL
#define SMN_IOHUB3_N0NBIO0_IOAGR_SION_S1_Client_RdRsp_BurstTarget_Lower_ADDRESS 0x1e100440UL
#define SMN_IOHUB3_N0NBIO1_IOAGR_SION_S1_Client_RdRsp_BurstTarget_Lower_ADDRESS 0x1e300440UL
#define SMN_IOHUB3_N1NBIO0_IOAGR_SION_S1_Client_RdRsp_BurstTarget_Lower_ADDRESS 0x1e100840UL
#define SMN_IOHUB3_N1NBIO1_IOAGR_SION_S1_Client_RdRsp_BurstTarget_Lower_ADDRESS 0x1e300840UL
#define SMN_IOHUB3_N2NBIO0_IOAGR_SION_S1_Client_RdRsp_BurstTarget_Lower_ADDRESS 0x1e100c40UL
#define SMN_IOHUB3_N2NBIO1_IOAGR_SION_S1_Client_RdRsp_BurstTarget_Lower_ADDRESS 0x1e300c40UL


/***********************************************************
* Register Name : IOAGR_SION_S1_Client_RdRsp_BurstTarget_Upper
************************************************************/

#define IOAGR_SION_S1_Client_RdRsp_BurstTarget_Upper_IOAGR_SION_S1_Client_RdRsp_BurstTarget_Upper_OFFSET 0
#define IOAGR_SION_S1_Client_RdRsp_BurstTarget_Upper_IOAGR_SION_S1_Client_RdRsp_BurstTarget_Upper_MASK 0xffffffff

typedef union {
  struct {
    UINT32        IOAGR_SION_S1_Client_RdRsp_BurstTarget_Upper:32;
  } Field;
  UINT32 Value;
} IOAGR_SION_S1_Client_RdRsp_BurstTarget_Upper_STRUCT;

#define SMN_IOAGR_SION_S1_Client_RdRsp_BurstTarget_Upper_ADDRESS      0x15b00444UL
#define SMN_IOHUB0_N0NBIO0_IOAGR_SION_S1_Client_RdRsp_BurstTarget_Upper_ADDRESS 0x15b00444UL
#define SMN_IOHUB0_N0NBIO1_IOAGR_SION_S1_Client_RdRsp_BurstTarget_Upper_ADDRESS 0x15d00444UL
#define SMN_IOHUB0_N1NBIO0_IOAGR_SION_S1_Client_RdRsp_BurstTarget_Upper_ADDRESS 0x15b00844UL
#define SMN_IOHUB0_N1NBIO1_IOAGR_SION_S1_Client_RdRsp_BurstTarget_Upper_ADDRESS 0x15d00844UL
#define SMN_IOHUB0_N2NBIO0_IOAGR_SION_S1_Client_RdRsp_BurstTarget_Upper_ADDRESS 0x15b00c44UL
#define SMN_IOHUB0_N2NBIO1_IOAGR_SION_S1_Client_RdRsp_BurstTarget_Upper_ADDRESS 0x15d00c44UL
#define SMN_IOHUB0_N3NBIO0_IOAGR_SION_S1_Client_RdRsp_BurstTarget_Upper_ADDRESS 0x15b01044UL
#define SMN_IOHUB0_N3NBIO1_IOAGR_SION_S1_Client_RdRsp_BurstTarget_Upper_ADDRESS 0x15d01044UL
#define SMN_IOHUB0_N4NBIO0_IOAGR_SION_S1_Client_RdRsp_BurstTarget_Upper_ADDRESS 0x15b01444UL
#define SMN_IOHUB0_N4NBIO1_IOAGR_SION_S1_Client_RdRsp_BurstTarget_Upper_ADDRESS 0x15d01444UL
#define SMN_IOHUB0_N5NBIO0_IOAGR_SION_S1_Client_RdRsp_BurstTarget_Upper_ADDRESS 0x15b01844UL
#define SMN_IOHUB0_N5NBIO1_IOAGR_SION_S1_Client_RdRsp_BurstTarget_Upper_ADDRESS 0x15d01844UL
#define SMN_IOHUB1_N0NBIO0_IOAGR_SION_S1_Client_RdRsp_BurstTarget_Upper_ADDRESS 0x1e000444UL
#define SMN_IOHUB1_N0NBIO1_IOAGR_SION_S1_Client_RdRsp_BurstTarget_Upper_ADDRESS 0x1e200444UL
#define SMN_IOHUB1_N1NBIO0_IOAGR_SION_S1_Client_RdRsp_BurstTarget_Upper_ADDRESS 0x1e000844UL
#define SMN_IOHUB1_N1NBIO1_IOAGR_SION_S1_Client_RdRsp_BurstTarget_Upper_ADDRESS 0x1e200844UL
#define SMN_IOHUB1_N2NBIO0_IOAGR_SION_S1_Client_RdRsp_BurstTarget_Upper_ADDRESS 0x1e000c44UL
#define SMN_IOHUB1_N2NBIO1_IOAGR_SION_S1_Client_RdRsp_BurstTarget_Upper_ADDRESS 0x1e200c44UL
#define SMN_IOHUB2_N0NBIO0_IOAGR_SION_S1_Client_RdRsp_BurstTarget_Upper_ADDRESS 0x15c00444UL
#define SMN_IOHUB2_N0NBIO1_IOAGR_SION_S1_Client_RdRsp_BurstTarget_Upper_ADDRESS 0x15e00444UL
#define SMN_IOHUB2_N1NBIO0_IOAGR_SION_S1_Client_RdRsp_BurstTarget_Upper_ADDRESS 0x15c00844UL
#define SMN_IOHUB2_N1NBIO1_IOAGR_SION_S1_Client_RdRsp_BurstTarget_Upper_ADDRESS 0x15e00844UL
#define SMN_IOHUB2_N2NBIO0_IOAGR_SION_S1_Client_RdRsp_BurstTarget_Upper_ADDRESS 0x15c00c44UL
#define SMN_IOHUB2_N2NBIO1_IOAGR_SION_S1_Client_RdRsp_BurstTarget_Upper_ADDRESS 0x15e00c44UL
#define SMN_IOHUB2_N3NBIO0_IOAGR_SION_S1_Client_RdRsp_BurstTarget_Upper_ADDRESS 0x15c01044UL
#define SMN_IOHUB2_N3NBIO1_IOAGR_SION_S1_Client_RdRsp_BurstTarget_Upper_ADDRESS 0x15e01044UL
#define SMN_IOHUB2_N4NBIO0_IOAGR_SION_S1_Client_RdRsp_BurstTarget_Upper_ADDRESS 0x15c01444UL
#define SMN_IOHUB2_N4NBIO1_IOAGR_SION_S1_Client_RdRsp_BurstTarget_Upper_ADDRESS 0x15e01444UL
#define SMN_IOHUB2_N5NBIO0_IOAGR_SION_S1_Client_RdRsp_BurstTarget_Upper_ADDRESS 0x15c01844UL
#define SMN_IOHUB2_N5NBIO1_IOAGR_SION_S1_Client_RdRsp_BurstTarget_Upper_ADDRESS 0x15e01844UL
#define SMN_IOHUB3_N0NBIO0_IOAGR_SION_S1_Client_RdRsp_BurstTarget_Upper_ADDRESS 0x1e100444UL
#define SMN_IOHUB3_N0NBIO1_IOAGR_SION_S1_Client_RdRsp_BurstTarget_Upper_ADDRESS 0x1e300444UL
#define SMN_IOHUB3_N1NBIO0_IOAGR_SION_S1_Client_RdRsp_BurstTarget_Upper_ADDRESS 0x1e100844UL
#define SMN_IOHUB3_N1NBIO1_IOAGR_SION_S1_Client_RdRsp_BurstTarget_Upper_ADDRESS 0x1e300844UL
#define SMN_IOHUB3_N2NBIO0_IOAGR_SION_S1_Client_RdRsp_BurstTarget_Upper_ADDRESS 0x1e100c44UL
#define SMN_IOHUB3_N2NBIO1_IOAGR_SION_S1_Client_RdRsp_BurstTarget_Upper_ADDRESS 0x1e300c44UL


/***********************************************************
* Register Name : IOAGR_SION_S1_Client_RdRsp_TimeSlot_Lower
************************************************************/

#define IOAGR_SION_S1_Client_RdRsp_TimeSlot_Lower_IOAGR_SION_S1_Client_RdRsp_TimeSlot_Lower_OFFSET 0
#define IOAGR_SION_S1_Client_RdRsp_TimeSlot_Lower_IOAGR_SION_S1_Client_RdRsp_TimeSlot_Lower_MASK 0xffffffff

typedef union {
  struct {
    UINT32           IOAGR_SION_S1_Client_RdRsp_TimeSlot_Lower:32;
  } Field;
  UINT32 Value;
} IOAGR_SION_S1_Client_RdRsp_TimeSlot_Lower_STRUCT;

#define SMN_IOAGR_SION_S1_Client_RdRsp_TimeSlot_Lower_ADDRESS         0x15b00448UL
#define SMN_IOHUB0_N0NBIO0_IOAGR_SION_S1_Client_RdRsp_TimeSlot_Lower_ADDRESS 0x15b00448UL
#define SMN_IOHUB0_N0NBIO1_IOAGR_SION_S1_Client_RdRsp_TimeSlot_Lower_ADDRESS 0x15d00448UL
#define SMN_IOHUB0_N1NBIO0_IOAGR_SION_S1_Client_RdRsp_TimeSlot_Lower_ADDRESS 0x15b00848UL
#define SMN_IOHUB0_N1NBIO1_IOAGR_SION_S1_Client_RdRsp_TimeSlot_Lower_ADDRESS 0x15d00848UL
#define SMN_IOHUB0_N2NBIO0_IOAGR_SION_S1_Client_RdRsp_TimeSlot_Lower_ADDRESS 0x15b00c48UL
#define SMN_IOHUB0_N2NBIO1_IOAGR_SION_S1_Client_RdRsp_TimeSlot_Lower_ADDRESS 0x15d00c48UL
#define SMN_IOHUB0_N3NBIO0_IOAGR_SION_S1_Client_RdRsp_TimeSlot_Lower_ADDRESS 0x15b01048UL
#define SMN_IOHUB0_N3NBIO1_IOAGR_SION_S1_Client_RdRsp_TimeSlot_Lower_ADDRESS 0x15d01048UL
#define SMN_IOHUB0_N4NBIO0_IOAGR_SION_S1_Client_RdRsp_TimeSlot_Lower_ADDRESS 0x15b01448UL
#define SMN_IOHUB0_N4NBIO1_IOAGR_SION_S1_Client_RdRsp_TimeSlot_Lower_ADDRESS 0x15d01448UL
#define SMN_IOHUB0_N5NBIO0_IOAGR_SION_S1_Client_RdRsp_TimeSlot_Lower_ADDRESS 0x15b01848UL
#define SMN_IOHUB0_N5NBIO1_IOAGR_SION_S1_Client_RdRsp_TimeSlot_Lower_ADDRESS 0x15d01848UL
#define SMN_IOHUB1_N0NBIO0_IOAGR_SION_S1_Client_RdRsp_TimeSlot_Lower_ADDRESS 0x1e000448UL
#define SMN_IOHUB1_N0NBIO1_IOAGR_SION_S1_Client_RdRsp_TimeSlot_Lower_ADDRESS 0x1e200448UL
#define SMN_IOHUB1_N1NBIO0_IOAGR_SION_S1_Client_RdRsp_TimeSlot_Lower_ADDRESS 0x1e000848UL
#define SMN_IOHUB1_N1NBIO1_IOAGR_SION_S1_Client_RdRsp_TimeSlot_Lower_ADDRESS 0x1e200848UL
#define SMN_IOHUB1_N2NBIO0_IOAGR_SION_S1_Client_RdRsp_TimeSlot_Lower_ADDRESS 0x1e000c48UL
#define SMN_IOHUB1_N2NBIO1_IOAGR_SION_S1_Client_RdRsp_TimeSlot_Lower_ADDRESS 0x1e200c48UL
#define SMN_IOHUB2_N0NBIO0_IOAGR_SION_S1_Client_RdRsp_TimeSlot_Lower_ADDRESS 0x15c00448UL
#define SMN_IOHUB2_N0NBIO1_IOAGR_SION_S1_Client_RdRsp_TimeSlot_Lower_ADDRESS 0x15e00448UL
#define SMN_IOHUB2_N1NBIO0_IOAGR_SION_S1_Client_RdRsp_TimeSlot_Lower_ADDRESS 0x15c00848UL
#define SMN_IOHUB2_N1NBIO1_IOAGR_SION_S1_Client_RdRsp_TimeSlot_Lower_ADDRESS 0x15e00848UL
#define SMN_IOHUB2_N2NBIO0_IOAGR_SION_S1_Client_RdRsp_TimeSlot_Lower_ADDRESS 0x15c00c48UL
#define SMN_IOHUB2_N2NBIO1_IOAGR_SION_S1_Client_RdRsp_TimeSlot_Lower_ADDRESS 0x15e00c48UL
#define SMN_IOHUB2_N3NBIO0_IOAGR_SION_S1_Client_RdRsp_TimeSlot_Lower_ADDRESS 0x15c01048UL
#define SMN_IOHUB2_N3NBIO1_IOAGR_SION_S1_Client_RdRsp_TimeSlot_Lower_ADDRESS 0x15e01048UL
#define SMN_IOHUB2_N4NBIO0_IOAGR_SION_S1_Client_RdRsp_TimeSlot_Lower_ADDRESS 0x15c01448UL
#define SMN_IOHUB2_N4NBIO1_IOAGR_SION_S1_Client_RdRsp_TimeSlot_Lower_ADDRESS 0x15e01448UL
#define SMN_IOHUB2_N5NBIO0_IOAGR_SION_S1_Client_RdRsp_TimeSlot_Lower_ADDRESS 0x15c01848UL
#define SMN_IOHUB2_N5NBIO1_IOAGR_SION_S1_Client_RdRsp_TimeSlot_Lower_ADDRESS 0x15e01848UL
#define SMN_IOHUB3_N0NBIO0_IOAGR_SION_S1_Client_RdRsp_TimeSlot_Lower_ADDRESS 0x1e100448UL
#define SMN_IOHUB3_N0NBIO1_IOAGR_SION_S1_Client_RdRsp_TimeSlot_Lower_ADDRESS 0x1e300448UL
#define SMN_IOHUB3_N1NBIO0_IOAGR_SION_S1_Client_RdRsp_TimeSlot_Lower_ADDRESS 0x1e100848UL
#define SMN_IOHUB3_N1NBIO1_IOAGR_SION_S1_Client_RdRsp_TimeSlot_Lower_ADDRESS 0x1e300848UL
#define SMN_IOHUB3_N2NBIO0_IOAGR_SION_S1_Client_RdRsp_TimeSlot_Lower_ADDRESS 0x1e100c48UL
#define SMN_IOHUB3_N2NBIO1_IOAGR_SION_S1_Client_RdRsp_TimeSlot_Lower_ADDRESS 0x1e300c48UL


/***********************************************************
* Register Name : IOAGR_SION_S1_Client_RdRsp_TimeSlot_Upper
************************************************************/

#define IOAGR_SION_S1_Client_RdRsp_TimeSlot_Upper_IOAGR_SION_S1_Client_RdRsp_TimeSlot_Upper_OFFSET 0
#define IOAGR_SION_S1_Client_RdRsp_TimeSlot_Upper_IOAGR_SION_S1_Client_RdRsp_TimeSlot_Upper_MASK 0xffffffff

typedef union {
  struct {
    UINT32           IOAGR_SION_S1_Client_RdRsp_TimeSlot_Upper:32;
  } Field;
  UINT32 Value;
} IOAGR_SION_S1_Client_RdRsp_TimeSlot_Upper_STRUCT;

#define SMN_IOAGR_SION_S1_Client_RdRsp_TimeSlot_Upper_ADDRESS         0x15b0044cUL
#define SMN_IOHUB0_N0NBIO0_IOAGR_SION_S1_Client_RdRsp_TimeSlot_Upper_ADDRESS 0x15b0044cUL
#define SMN_IOHUB0_N0NBIO1_IOAGR_SION_S1_Client_RdRsp_TimeSlot_Upper_ADDRESS 0x15d0044cUL
#define SMN_IOHUB0_N1NBIO0_IOAGR_SION_S1_Client_RdRsp_TimeSlot_Upper_ADDRESS 0x15b0084cUL
#define SMN_IOHUB0_N1NBIO1_IOAGR_SION_S1_Client_RdRsp_TimeSlot_Upper_ADDRESS 0x15d0084cUL
#define SMN_IOHUB0_N2NBIO0_IOAGR_SION_S1_Client_RdRsp_TimeSlot_Upper_ADDRESS 0x15b00c4cUL
#define SMN_IOHUB0_N2NBIO1_IOAGR_SION_S1_Client_RdRsp_TimeSlot_Upper_ADDRESS 0x15d00c4cUL
#define SMN_IOHUB0_N3NBIO0_IOAGR_SION_S1_Client_RdRsp_TimeSlot_Upper_ADDRESS 0x15b0104cUL
#define SMN_IOHUB0_N3NBIO1_IOAGR_SION_S1_Client_RdRsp_TimeSlot_Upper_ADDRESS 0x15d0104cUL
#define SMN_IOHUB0_N4NBIO0_IOAGR_SION_S1_Client_RdRsp_TimeSlot_Upper_ADDRESS 0x15b0144cUL
#define SMN_IOHUB0_N4NBIO1_IOAGR_SION_S1_Client_RdRsp_TimeSlot_Upper_ADDRESS 0x15d0144cUL
#define SMN_IOHUB0_N5NBIO0_IOAGR_SION_S1_Client_RdRsp_TimeSlot_Upper_ADDRESS 0x15b0184cUL
#define SMN_IOHUB0_N5NBIO1_IOAGR_SION_S1_Client_RdRsp_TimeSlot_Upper_ADDRESS 0x15d0184cUL
#define SMN_IOHUB1_N0NBIO0_IOAGR_SION_S1_Client_RdRsp_TimeSlot_Upper_ADDRESS 0x1e00044cUL
#define SMN_IOHUB1_N0NBIO1_IOAGR_SION_S1_Client_RdRsp_TimeSlot_Upper_ADDRESS 0x1e20044cUL
#define SMN_IOHUB1_N1NBIO0_IOAGR_SION_S1_Client_RdRsp_TimeSlot_Upper_ADDRESS 0x1e00084cUL
#define SMN_IOHUB1_N1NBIO1_IOAGR_SION_S1_Client_RdRsp_TimeSlot_Upper_ADDRESS 0x1e20084cUL
#define SMN_IOHUB1_N2NBIO0_IOAGR_SION_S1_Client_RdRsp_TimeSlot_Upper_ADDRESS 0x1e000c4cUL
#define SMN_IOHUB1_N2NBIO1_IOAGR_SION_S1_Client_RdRsp_TimeSlot_Upper_ADDRESS 0x1e200c4cUL
#define SMN_IOHUB2_N0NBIO0_IOAGR_SION_S1_Client_RdRsp_TimeSlot_Upper_ADDRESS 0x15c0044cUL
#define SMN_IOHUB2_N0NBIO1_IOAGR_SION_S1_Client_RdRsp_TimeSlot_Upper_ADDRESS 0x15e0044cUL
#define SMN_IOHUB2_N1NBIO0_IOAGR_SION_S1_Client_RdRsp_TimeSlot_Upper_ADDRESS 0x15c0084cUL
#define SMN_IOHUB2_N1NBIO1_IOAGR_SION_S1_Client_RdRsp_TimeSlot_Upper_ADDRESS 0x15e0084cUL
#define SMN_IOHUB2_N2NBIO0_IOAGR_SION_S1_Client_RdRsp_TimeSlot_Upper_ADDRESS 0x15c00c4cUL
#define SMN_IOHUB2_N2NBIO1_IOAGR_SION_S1_Client_RdRsp_TimeSlot_Upper_ADDRESS 0x15e00c4cUL
#define SMN_IOHUB2_N3NBIO0_IOAGR_SION_S1_Client_RdRsp_TimeSlot_Upper_ADDRESS 0x15c0104cUL
#define SMN_IOHUB2_N3NBIO1_IOAGR_SION_S1_Client_RdRsp_TimeSlot_Upper_ADDRESS 0x15e0104cUL
#define SMN_IOHUB2_N4NBIO0_IOAGR_SION_S1_Client_RdRsp_TimeSlot_Upper_ADDRESS 0x15c0144cUL
#define SMN_IOHUB2_N4NBIO1_IOAGR_SION_S1_Client_RdRsp_TimeSlot_Upper_ADDRESS 0x15e0144cUL
#define SMN_IOHUB2_N5NBIO0_IOAGR_SION_S1_Client_RdRsp_TimeSlot_Upper_ADDRESS 0x15c0184cUL
#define SMN_IOHUB2_N5NBIO1_IOAGR_SION_S1_Client_RdRsp_TimeSlot_Upper_ADDRESS 0x15e0184cUL
#define SMN_IOHUB3_N0NBIO0_IOAGR_SION_S1_Client_RdRsp_TimeSlot_Upper_ADDRESS 0x1e10044cUL
#define SMN_IOHUB3_N0NBIO1_IOAGR_SION_S1_Client_RdRsp_TimeSlot_Upper_ADDRESS 0x1e30044cUL
#define SMN_IOHUB3_N1NBIO0_IOAGR_SION_S1_Client_RdRsp_TimeSlot_Upper_ADDRESS 0x1e10084cUL
#define SMN_IOHUB3_N1NBIO1_IOAGR_SION_S1_Client_RdRsp_TimeSlot_Upper_ADDRESS 0x1e30084cUL
#define SMN_IOHUB3_N2NBIO0_IOAGR_SION_S1_Client_RdRsp_TimeSlot_Upper_ADDRESS 0x1e100c4cUL
#define SMN_IOHUB3_N2NBIO1_IOAGR_SION_S1_Client_RdRsp_TimeSlot_Upper_ADDRESS 0x1e300c4cUL


/***********************************************************
* Register Name : IOAGR_SION_S1_Client_Req_BurstTarget_Lower
************************************************************/

#define IOAGR_SION_S1_Client_Req_BurstTarget_Lower_IOAGR_SION_S1_Client_Req_BurstTarget_Lower_OFFSET 0
#define IOAGR_SION_S1_Client_Req_BurstTarget_Lower_IOAGR_SION_S1_Client_Req_BurstTarget_Lower_MASK 0xffffffff
#define IOAGR_SION_S1_Client_Req_BurstTarget_Lower_IOAGR_SION_S1_Client_Req_BurstTarget_Lower_DEFAULT     0x4040404

typedef union {
  struct {
    UINT32          IOAGR_SION_S1_Client_Req_BurstTarget_Lower:32;
  } Field;
  UINT32 Value;
} IOAGR_SION_S1_Client_Req_BurstTarget_Lower_STRUCT;

#define SMN_IOAGR_SION_S1_Client_Req_BurstTarget_Lower_ADDRESS        0x15b00430UL
#define SMN_IOHUB0_N0NBIO0_IOAGR_SION_S1_Client_Req_BurstTarget_Lower_ADDRESS 0x15b00430UL
#define SMN_IOHUB0_N0NBIO1_IOAGR_SION_S1_Client_Req_BurstTarget_Lower_ADDRESS 0x15d00430UL
#define SMN_IOHUB0_N1NBIO0_IOAGR_SION_S1_Client_Req_BurstTarget_Lower_ADDRESS 0x15b00830UL
#define SMN_IOHUB0_N1NBIO1_IOAGR_SION_S1_Client_Req_BurstTarget_Lower_ADDRESS 0x15d00830UL
#define SMN_IOHUB0_N2NBIO0_IOAGR_SION_S1_Client_Req_BurstTarget_Lower_ADDRESS 0x15b00c30UL
#define SMN_IOHUB0_N2NBIO1_IOAGR_SION_S1_Client_Req_BurstTarget_Lower_ADDRESS 0x15d00c30UL
#define SMN_IOHUB0_N3NBIO0_IOAGR_SION_S1_Client_Req_BurstTarget_Lower_ADDRESS 0x15b01030UL
#define SMN_IOHUB0_N3NBIO1_IOAGR_SION_S1_Client_Req_BurstTarget_Lower_ADDRESS 0x15d01030UL
#define SMN_IOHUB0_N4NBIO0_IOAGR_SION_S1_Client_Req_BurstTarget_Lower_ADDRESS 0x15b01430UL
#define SMN_IOHUB0_N4NBIO1_IOAGR_SION_S1_Client_Req_BurstTarget_Lower_ADDRESS 0x15d01430UL
#define SMN_IOHUB0_N5NBIO0_IOAGR_SION_S1_Client_Req_BurstTarget_Lower_ADDRESS 0x15b01830UL
#define SMN_IOHUB0_N5NBIO1_IOAGR_SION_S1_Client_Req_BurstTarget_Lower_ADDRESS 0x15d01830UL
#define SMN_IOHUB1_N0NBIO0_IOAGR_SION_S1_Client_Req_BurstTarget_Lower_ADDRESS 0x1e000430UL
#define SMN_IOHUB1_N0NBIO1_IOAGR_SION_S1_Client_Req_BurstTarget_Lower_ADDRESS 0x1e200430UL
#define SMN_IOHUB1_N1NBIO0_IOAGR_SION_S1_Client_Req_BurstTarget_Lower_ADDRESS 0x1e000830UL
#define SMN_IOHUB1_N1NBIO1_IOAGR_SION_S1_Client_Req_BurstTarget_Lower_ADDRESS 0x1e200830UL
#define SMN_IOHUB1_N2NBIO0_IOAGR_SION_S1_Client_Req_BurstTarget_Lower_ADDRESS 0x1e000c30UL
#define SMN_IOHUB1_N2NBIO1_IOAGR_SION_S1_Client_Req_BurstTarget_Lower_ADDRESS 0x1e200c30UL
#define SMN_IOHUB2_N0NBIO0_IOAGR_SION_S1_Client_Req_BurstTarget_Lower_ADDRESS 0x15c00430UL
#define SMN_IOHUB2_N0NBIO1_IOAGR_SION_S1_Client_Req_BurstTarget_Lower_ADDRESS 0x15e00430UL
#define SMN_IOHUB2_N1NBIO0_IOAGR_SION_S1_Client_Req_BurstTarget_Lower_ADDRESS 0x15c00830UL
#define SMN_IOHUB2_N1NBIO1_IOAGR_SION_S1_Client_Req_BurstTarget_Lower_ADDRESS 0x15e00830UL
#define SMN_IOHUB2_N2NBIO0_IOAGR_SION_S1_Client_Req_BurstTarget_Lower_ADDRESS 0x15c00c30UL
#define SMN_IOHUB2_N2NBIO1_IOAGR_SION_S1_Client_Req_BurstTarget_Lower_ADDRESS 0x15e00c30UL
#define SMN_IOHUB2_N3NBIO0_IOAGR_SION_S1_Client_Req_BurstTarget_Lower_ADDRESS 0x15c01030UL
#define SMN_IOHUB2_N3NBIO1_IOAGR_SION_S1_Client_Req_BurstTarget_Lower_ADDRESS 0x15e01030UL
#define SMN_IOHUB2_N4NBIO0_IOAGR_SION_S1_Client_Req_BurstTarget_Lower_ADDRESS 0x15c01430UL
#define SMN_IOHUB2_N4NBIO1_IOAGR_SION_S1_Client_Req_BurstTarget_Lower_ADDRESS 0x15e01430UL
#define SMN_IOHUB2_N5NBIO0_IOAGR_SION_S1_Client_Req_BurstTarget_Lower_ADDRESS 0x15c01830UL
#define SMN_IOHUB2_N5NBIO1_IOAGR_SION_S1_Client_Req_BurstTarget_Lower_ADDRESS 0x15e01830UL
#define SMN_IOHUB3_N0NBIO0_IOAGR_SION_S1_Client_Req_BurstTarget_Lower_ADDRESS 0x1e100430UL
#define SMN_IOHUB3_N0NBIO1_IOAGR_SION_S1_Client_Req_BurstTarget_Lower_ADDRESS 0x1e300430UL
#define SMN_IOHUB3_N1NBIO0_IOAGR_SION_S1_Client_Req_BurstTarget_Lower_ADDRESS 0x1e100830UL
#define SMN_IOHUB3_N1NBIO1_IOAGR_SION_S1_Client_Req_BurstTarget_Lower_ADDRESS 0x1e300830UL
#define SMN_IOHUB3_N2NBIO0_IOAGR_SION_S1_Client_Req_BurstTarget_Lower_ADDRESS 0x1e100c30UL
#define SMN_IOHUB3_N2NBIO1_IOAGR_SION_S1_Client_Req_BurstTarget_Lower_ADDRESS 0x1e300c30UL


/***********************************************************
* Register Name : IOAGR_SION_S1_Client_Req_BurstTarget_Upper
************************************************************/

#define IOAGR_SION_S1_Client_Req_BurstTarget_Upper_IOAGR_SION_S1_Client_Req_BurstTarget_Upper_OFFSET 0
#define IOAGR_SION_S1_Client_Req_BurstTarget_Upper_IOAGR_SION_S1_Client_Req_BurstTarget_Upper_MASK 0xffffffff
#define IOAGR_SION_S1_Client_Req_BurstTarget_Upper_IOAGR_SION_S1_Client_Req_BurstTarget_Upper_DEFAULT     0x4040404

typedef union {
  struct {
    UINT32          IOAGR_SION_S1_Client_Req_BurstTarget_Upper:32;
  } Field;
  UINT32 Value;
} IOAGR_SION_S1_Client_Req_BurstTarget_Upper_STRUCT;

#define SMN_IOAGR_SION_S1_Client_Req_BurstTarget_Upper_ADDRESS        0x15b00434UL
#define SMN_IOHUB0_N0NBIO0_IOAGR_SION_S1_Client_Req_BurstTarget_Upper_ADDRESS 0x15b00434UL
#define SMN_IOHUB0_N0NBIO1_IOAGR_SION_S1_Client_Req_BurstTarget_Upper_ADDRESS 0x15d00434UL
#define SMN_IOHUB0_N1NBIO0_IOAGR_SION_S1_Client_Req_BurstTarget_Upper_ADDRESS 0x15b00834UL
#define SMN_IOHUB0_N1NBIO1_IOAGR_SION_S1_Client_Req_BurstTarget_Upper_ADDRESS 0x15d00834UL
#define SMN_IOHUB0_N2NBIO0_IOAGR_SION_S1_Client_Req_BurstTarget_Upper_ADDRESS 0x15b00c34UL
#define SMN_IOHUB0_N2NBIO1_IOAGR_SION_S1_Client_Req_BurstTarget_Upper_ADDRESS 0x15d00c34UL
#define SMN_IOHUB0_N3NBIO0_IOAGR_SION_S1_Client_Req_BurstTarget_Upper_ADDRESS 0x15b01034UL
#define SMN_IOHUB0_N3NBIO1_IOAGR_SION_S1_Client_Req_BurstTarget_Upper_ADDRESS 0x15d01034UL
#define SMN_IOHUB0_N4NBIO0_IOAGR_SION_S1_Client_Req_BurstTarget_Upper_ADDRESS 0x15b01434UL
#define SMN_IOHUB0_N4NBIO1_IOAGR_SION_S1_Client_Req_BurstTarget_Upper_ADDRESS 0x15d01434UL
#define SMN_IOHUB0_N5NBIO0_IOAGR_SION_S1_Client_Req_BurstTarget_Upper_ADDRESS 0x15b01834UL
#define SMN_IOHUB0_N5NBIO1_IOAGR_SION_S1_Client_Req_BurstTarget_Upper_ADDRESS 0x15d01834UL
#define SMN_IOHUB1_N0NBIO0_IOAGR_SION_S1_Client_Req_BurstTarget_Upper_ADDRESS 0x1e000434UL
#define SMN_IOHUB1_N0NBIO1_IOAGR_SION_S1_Client_Req_BurstTarget_Upper_ADDRESS 0x1e200434UL
#define SMN_IOHUB1_N1NBIO0_IOAGR_SION_S1_Client_Req_BurstTarget_Upper_ADDRESS 0x1e000834UL
#define SMN_IOHUB1_N1NBIO1_IOAGR_SION_S1_Client_Req_BurstTarget_Upper_ADDRESS 0x1e200834UL
#define SMN_IOHUB1_N2NBIO0_IOAGR_SION_S1_Client_Req_BurstTarget_Upper_ADDRESS 0x1e000c34UL
#define SMN_IOHUB1_N2NBIO1_IOAGR_SION_S1_Client_Req_BurstTarget_Upper_ADDRESS 0x1e200c34UL
#define SMN_IOHUB2_N0NBIO0_IOAGR_SION_S1_Client_Req_BurstTarget_Upper_ADDRESS 0x15c00434UL
#define SMN_IOHUB2_N0NBIO1_IOAGR_SION_S1_Client_Req_BurstTarget_Upper_ADDRESS 0x15e00434UL
#define SMN_IOHUB2_N1NBIO0_IOAGR_SION_S1_Client_Req_BurstTarget_Upper_ADDRESS 0x15c00834UL
#define SMN_IOHUB2_N1NBIO1_IOAGR_SION_S1_Client_Req_BurstTarget_Upper_ADDRESS 0x15e00834UL
#define SMN_IOHUB2_N2NBIO0_IOAGR_SION_S1_Client_Req_BurstTarget_Upper_ADDRESS 0x15c00c34UL
#define SMN_IOHUB2_N2NBIO1_IOAGR_SION_S1_Client_Req_BurstTarget_Upper_ADDRESS 0x15e00c34UL
#define SMN_IOHUB2_N3NBIO0_IOAGR_SION_S1_Client_Req_BurstTarget_Upper_ADDRESS 0x15c01034UL
#define SMN_IOHUB2_N3NBIO1_IOAGR_SION_S1_Client_Req_BurstTarget_Upper_ADDRESS 0x15e01034UL
#define SMN_IOHUB2_N4NBIO0_IOAGR_SION_S1_Client_Req_BurstTarget_Upper_ADDRESS 0x15c01434UL
#define SMN_IOHUB2_N4NBIO1_IOAGR_SION_S1_Client_Req_BurstTarget_Upper_ADDRESS 0x15e01434UL
#define SMN_IOHUB2_N5NBIO0_IOAGR_SION_S1_Client_Req_BurstTarget_Upper_ADDRESS 0x15c01834UL
#define SMN_IOHUB2_N5NBIO1_IOAGR_SION_S1_Client_Req_BurstTarget_Upper_ADDRESS 0x15e01834UL
#define SMN_IOHUB3_N0NBIO0_IOAGR_SION_S1_Client_Req_BurstTarget_Upper_ADDRESS 0x1e100434UL
#define SMN_IOHUB3_N0NBIO1_IOAGR_SION_S1_Client_Req_BurstTarget_Upper_ADDRESS 0x1e300434UL
#define SMN_IOHUB3_N1NBIO0_IOAGR_SION_S1_Client_Req_BurstTarget_Upper_ADDRESS 0x1e100834UL
#define SMN_IOHUB3_N1NBIO1_IOAGR_SION_S1_Client_Req_BurstTarget_Upper_ADDRESS 0x1e300834UL
#define SMN_IOHUB3_N2NBIO0_IOAGR_SION_S1_Client_Req_BurstTarget_Upper_ADDRESS 0x1e100c34UL
#define SMN_IOHUB3_N2NBIO1_IOAGR_SION_S1_Client_Req_BurstTarget_Upper_ADDRESS 0x1e300c34UL


/***********************************************************
* Register Name : IOAGR_SION_S1_Client_Req_TimeSlot_Lower
************************************************************/

#define IOAGR_SION_S1_Client_Req_TimeSlot_Lower_IOAGR_SION_S1_Client_Req_TimeSlot_Lower_OFFSET 0
#define IOAGR_SION_S1_Client_Req_TimeSlot_Lower_IOAGR_SION_S1_Client_Req_TimeSlot_Lower_MASK 0xffffffff

typedef union {
  struct {
    UINT32             IOAGR_SION_S1_Client_Req_TimeSlot_Lower:32;
  } Field;
  UINT32 Value;
} IOAGR_SION_S1_Client_Req_TimeSlot_Lower_STRUCT;

#define SMN_IOAGR_SION_S1_Client_Req_TimeSlot_Lower_ADDRESS           0x15b00438UL
#define SMN_IOHUB0_N0NBIO0_IOAGR_SION_S1_Client_Req_TimeSlot_Lower_ADDRESS 0x15b00438UL
#define SMN_IOHUB0_N0NBIO1_IOAGR_SION_S1_Client_Req_TimeSlot_Lower_ADDRESS 0x15d00438UL
#define SMN_IOHUB0_N1NBIO0_IOAGR_SION_S1_Client_Req_TimeSlot_Lower_ADDRESS 0x15b00838UL
#define SMN_IOHUB0_N1NBIO1_IOAGR_SION_S1_Client_Req_TimeSlot_Lower_ADDRESS 0x15d00838UL
#define SMN_IOHUB0_N2NBIO0_IOAGR_SION_S1_Client_Req_TimeSlot_Lower_ADDRESS 0x15b00c38UL
#define SMN_IOHUB0_N2NBIO1_IOAGR_SION_S1_Client_Req_TimeSlot_Lower_ADDRESS 0x15d00c38UL
#define SMN_IOHUB0_N3NBIO0_IOAGR_SION_S1_Client_Req_TimeSlot_Lower_ADDRESS 0x15b01038UL
#define SMN_IOHUB0_N3NBIO1_IOAGR_SION_S1_Client_Req_TimeSlot_Lower_ADDRESS 0x15d01038UL
#define SMN_IOHUB0_N4NBIO0_IOAGR_SION_S1_Client_Req_TimeSlot_Lower_ADDRESS 0x15b01438UL
#define SMN_IOHUB0_N4NBIO1_IOAGR_SION_S1_Client_Req_TimeSlot_Lower_ADDRESS 0x15d01438UL
#define SMN_IOHUB0_N5NBIO0_IOAGR_SION_S1_Client_Req_TimeSlot_Lower_ADDRESS 0x15b01838UL
#define SMN_IOHUB0_N5NBIO1_IOAGR_SION_S1_Client_Req_TimeSlot_Lower_ADDRESS 0x15d01838UL
#define SMN_IOHUB1_N0NBIO0_IOAGR_SION_S1_Client_Req_TimeSlot_Lower_ADDRESS 0x1e000438UL
#define SMN_IOHUB1_N0NBIO1_IOAGR_SION_S1_Client_Req_TimeSlot_Lower_ADDRESS 0x1e200438UL
#define SMN_IOHUB1_N1NBIO0_IOAGR_SION_S1_Client_Req_TimeSlot_Lower_ADDRESS 0x1e000838UL
#define SMN_IOHUB1_N1NBIO1_IOAGR_SION_S1_Client_Req_TimeSlot_Lower_ADDRESS 0x1e200838UL
#define SMN_IOHUB1_N2NBIO0_IOAGR_SION_S1_Client_Req_TimeSlot_Lower_ADDRESS 0x1e000c38UL
#define SMN_IOHUB1_N2NBIO1_IOAGR_SION_S1_Client_Req_TimeSlot_Lower_ADDRESS 0x1e200c38UL
#define SMN_IOHUB2_N0NBIO0_IOAGR_SION_S1_Client_Req_TimeSlot_Lower_ADDRESS 0x15c00438UL
#define SMN_IOHUB2_N0NBIO1_IOAGR_SION_S1_Client_Req_TimeSlot_Lower_ADDRESS 0x15e00438UL
#define SMN_IOHUB2_N1NBIO0_IOAGR_SION_S1_Client_Req_TimeSlot_Lower_ADDRESS 0x15c00838UL
#define SMN_IOHUB2_N1NBIO1_IOAGR_SION_S1_Client_Req_TimeSlot_Lower_ADDRESS 0x15e00838UL
#define SMN_IOHUB2_N2NBIO0_IOAGR_SION_S1_Client_Req_TimeSlot_Lower_ADDRESS 0x15c00c38UL
#define SMN_IOHUB2_N2NBIO1_IOAGR_SION_S1_Client_Req_TimeSlot_Lower_ADDRESS 0x15e00c38UL
#define SMN_IOHUB2_N3NBIO0_IOAGR_SION_S1_Client_Req_TimeSlot_Lower_ADDRESS 0x15c01038UL
#define SMN_IOHUB2_N3NBIO1_IOAGR_SION_S1_Client_Req_TimeSlot_Lower_ADDRESS 0x15e01038UL
#define SMN_IOHUB2_N4NBIO0_IOAGR_SION_S1_Client_Req_TimeSlot_Lower_ADDRESS 0x15c01438UL
#define SMN_IOHUB2_N4NBIO1_IOAGR_SION_S1_Client_Req_TimeSlot_Lower_ADDRESS 0x15e01438UL
#define SMN_IOHUB2_N5NBIO0_IOAGR_SION_S1_Client_Req_TimeSlot_Lower_ADDRESS 0x15c01838UL
#define SMN_IOHUB2_N5NBIO1_IOAGR_SION_S1_Client_Req_TimeSlot_Lower_ADDRESS 0x15e01838UL
#define SMN_IOHUB3_N0NBIO0_IOAGR_SION_S1_Client_Req_TimeSlot_Lower_ADDRESS 0x1e100438UL
#define SMN_IOHUB3_N0NBIO1_IOAGR_SION_S1_Client_Req_TimeSlot_Lower_ADDRESS 0x1e300438UL
#define SMN_IOHUB3_N1NBIO0_IOAGR_SION_S1_Client_Req_TimeSlot_Lower_ADDRESS 0x1e100838UL
#define SMN_IOHUB3_N1NBIO1_IOAGR_SION_S1_Client_Req_TimeSlot_Lower_ADDRESS 0x1e300838UL
#define SMN_IOHUB3_N2NBIO0_IOAGR_SION_S1_Client_Req_TimeSlot_Lower_ADDRESS 0x1e100c38UL
#define SMN_IOHUB3_N2NBIO1_IOAGR_SION_S1_Client_Req_TimeSlot_Lower_ADDRESS 0x1e300c38UL


/***********************************************************
* Register Name : IOAGR_SION_S1_Client_Req_TimeSlot_Upper
************************************************************/

#define IOAGR_SION_S1_Client_Req_TimeSlot_Upper_IOAGR_SION_S1_Client_Req_TimeSlot_Upper_OFFSET 0
#define IOAGR_SION_S1_Client_Req_TimeSlot_Upper_IOAGR_SION_S1_Client_Req_TimeSlot_Upper_MASK 0xffffffff

typedef union {
  struct {
    UINT32             IOAGR_SION_S1_Client_Req_TimeSlot_Upper:32;
  } Field;
  UINT32 Value;
} IOAGR_SION_S1_Client_Req_TimeSlot_Upper_STRUCT;

#define SMN_IOAGR_SION_S1_Client_Req_TimeSlot_Upper_ADDRESS           0x15b0043cUL
#define SMN_IOHUB0_N0NBIO0_IOAGR_SION_S1_Client_Req_TimeSlot_Upper_ADDRESS 0x15b0043cUL
#define SMN_IOHUB0_N0NBIO1_IOAGR_SION_S1_Client_Req_TimeSlot_Upper_ADDRESS 0x15d0043cUL
#define SMN_IOHUB0_N1NBIO0_IOAGR_SION_S1_Client_Req_TimeSlot_Upper_ADDRESS 0x15b0083cUL
#define SMN_IOHUB0_N1NBIO1_IOAGR_SION_S1_Client_Req_TimeSlot_Upper_ADDRESS 0x15d0083cUL
#define SMN_IOHUB0_N2NBIO0_IOAGR_SION_S1_Client_Req_TimeSlot_Upper_ADDRESS 0x15b00c3cUL
#define SMN_IOHUB0_N2NBIO1_IOAGR_SION_S1_Client_Req_TimeSlot_Upper_ADDRESS 0x15d00c3cUL
#define SMN_IOHUB0_N3NBIO0_IOAGR_SION_S1_Client_Req_TimeSlot_Upper_ADDRESS 0x15b0103cUL
#define SMN_IOHUB0_N3NBIO1_IOAGR_SION_S1_Client_Req_TimeSlot_Upper_ADDRESS 0x15d0103cUL
#define SMN_IOHUB0_N4NBIO0_IOAGR_SION_S1_Client_Req_TimeSlot_Upper_ADDRESS 0x15b0143cUL
#define SMN_IOHUB0_N4NBIO1_IOAGR_SION_S1_Client_Req_TimeSlot_Upper_ADDRESS 0x15d0143cUL
#define SMN_IOHUB0_N5NBIO0_IOAGR_SION_S1_Client_Req_TimeSlot_Upper_ADDRESS 0x15b0183cUL
#define SMN_IOHUB0_N5NBIO1_IOAGR_SION_S1_Client_Req_TimeSlot_Upper_ADDRESS 0x15d0183cUL
#define SMN_IOHUB1_N0NBIO0_IOAGR_SION_S1_Client_Req_TimeSlot_Upper_ADDRESS 0x1e00043cUL
#define SMN_IOHUB1_N0NBIO1_IOAGR_SION_S1_Client_Req_TimeSlot_Upper_ADDRESS 0x1e20043cUL
#define SMN_IOHUB1_N1NBIO0_IOAGR_SION_S1_Client_Req_TimeSlot_Upper_ADDRESS 0x1e00083cUL
#define SMN_IOHUB1_N1NBIO1_IOAGR_SION_S1_Client_Req_TimeSlot_Upper_ADDRESS 0x1e20083cUL
#define SMN_IOHUB1_N2NBIO0_IOAGR_SION_S1_Client_Req_TimeSlot_Upper_ADDRESS 0x1e000c3cUL
#define SMN_IOHUB1_N2NBIO1_IOAGR_SION_S1_Client_Req_TimeSlot_Upper_ADDRESS 0x1e200c3cUL
#define SMN_IOHUB2_N0NBIO0_IOAGR_SION_S1_Client_Req_TimeSlot_Upper_ADDRESS 0x15c0043cUL
#define SMN_IOHUB2_N0NBIO1_IOAGR_SION_S1_Client_Req_TimeSlot_Upper_ADDRESS 0x15e0043cUL
#define SMN_IOHUB2_N1NBIO0_IOAGR_SION_S1_Client_Req_TimeSlot_Upper_ADDRESS 0x15c0083cUL
#define SMN_IOHUB2_N1NBIO1_IOAGR_SION_S1_Client_Req_TimeSlot_Upper_ADDRESS 0x15e0083cUL
#define SMN_IOHUB2_N2NBIO0_IOAGR_SION_S1_Client_Req_TimeSlot_Upper_ADDRESS 0x15c00c3cUL
#define SMN_IOHUB2_N2NBIO1_IOAGR_SION_S1_Client_Req_TimeSlot_Upper_ADDRESS 0x15e00c3cUL
#define SMN_IOHUB2_N3NBIO0_IOAGR_SION_S1_Client_Req_TimeSlot_Upper_ADDRESS 0x15c0103cUL
#define SMN_IOHUB2_N3NBIO1_IOAGR_SION_S1_Client_Req_TimeSlot_Upper_ADDRESS 0x15e0103cUL
#define SMN_IOHUB2_N4NBIO0_IOAGR_SION_S1_Client_Req_TimeSlot_Upper_ADDRESS 0x15c0143cUL
#define SMN_IOHUB2_N4NBIO1_IOAGR_SION_S1_Client_Req_TimeSlot_Upper_ADDRESS 0x15e0143cUL
#define SMN_IOHUB2_N5NBIO0_IOAGR_SION_S1_Client_Req_TimeSlot_Upper_ADDRESS 0x15c0183cUL
#define SMN_IOHUB2_N5NBIO1_IOAGR_SION_S1_Client_Req_TimeSlot_Upper_ADDRESS 0x15e0183cUL
#define SMN_IOHUB3_N0NBIO0_IOAGR_SION_S1_Client_Req_TimeSlot_Upper_ADDRESS 0x1e10043cUL
#define SMN_IOHUB3_N0NBIO1_IOAGR_SION_S1_Client_Req_TimeSlot_Upper_ADDRESS 0x1e30043cUL
#define SMN_IOHUB3_N1NBIO0_IOAGR_SION_S1_Client_Req_TimeSlot_Upper_ADDRESS 0x1e10083cUL
#define SMN_IOHUB3_N1NBIO1_IOAGR_SION_S1_Client_Req_TimeSlot_Upper_ADDRESS 0x1e30083cUL
#define SMN_IOHUB3_N2NBIO0_IOAGR_SION_S1_Client_Req_TimeSlot_Upper_ADDRESS 0x1e100c3cUL
#define SMN_IOHUB3_N2NBIO1_IOAGR_SION_S1_Client_Req_TimeSlot_Upper_ADDRESS 0x1e300c3cUL


/***********************************************************
* Register Name : IOAGR_SION_S1_Client_WrRsp_BurstTarget_Lower
************************************************************/

#define IOAGR_SION_S1_Client_WrRsp_BurstTarget_Lower_IOAGR_SION_S1_Client_WrRsp_BurstTarget_Lower_OFFSET 0
#define IOAGR_SION_S1_Client_WrRsp_BurstTarget_Lower_IOAGR_SION_S1_Client_WrRsp_BurstTarget_Lower_MASK 0xffffffff

typedef union {
  struct {
    UINT32        IOAGR_SION_S1_Client_WrRsp_BurstTarget_Lower:32;
  } Field;
  UINT32 Value;
} IOAGR_SION_S1_Client_WrRsp_BurstTarget_Lower_STRUCT;

#define SMN_IOAGR_SION_S1_Client_WrRsp_BurstTarget_Lower_ADDRESS      0x15b00450UL
#define SMN_IOHUB0_N0NBIO0_IOAGR_SION_S1_Client_WrRsp_BurstTarget_Lower_ADDRESS 0x15b00450UL
#define SMN_IOHUB0_N0NBIO1_IOAGR_SION_S1_Client_WrRsp_BurstTarget_Lower_ADDRESS 0x15d00450UL
#define SMN_IOHUB0_N1NBIO0_IOAGR_SION_S1_Client_WrRsp_BurstTarget_Lower_ADDRESS 0x15b00850UL
#define SMN_IOHUB0_N1NBIO1_IOAGR_SION_S1_Client_WrRsp_BurstTarget_Lower_ADDRESS 0x15d00850UL
#define SMN_IOHUB0_N2NBIO0_IOAGR_SION_S1_Client_WrRsp_BurstTarget_Lower_ADDRESS 0x15b00c50UL
#define SMN_IOHUB0_N2NBIO1_IOAGR_SION_S1_Client_WrRsp_BurstTarget_Lower_ADDRESS 0x15d00c50UL
#define SMN_IOHUB0_N3NBIO0_IOAGR_SION_S1_Client_WrRsp_BurstTarget_Lower_ADDRESS 0x15b01050UL
#define SMN_IOHUB0_N3NBIO1_IOAGR_SION_S1_Client_WrRsp_BurstTarget_Lower_ADDRESS 0x15d01050UL
#define SMN_IOHUB0_N4NBIO0_IOAGR_SION_S1_Client_WrRsp_BurstTarget_Lower_ADDRESS 0x15b01450UL
#define SMN_IOHUB0_N4NBIO1_IOAGR_SION_S1_Client_WrRsp_BurstTarget_Lower_ADDRESS 0x15d01450UL
#define SMN_IOHUB0_N5NBIO0_IOAGR_SION_S1_Client_WrRsp_BurstTarget_Lower_ADDRESS 0x15b01850UL
#define SMN_IOHUB0_N5NBIO1_IOAGR_SION_S1_Client_WrRsp_BurstTarget_Lower_ADDRESS 0x15d01850UL
#define SMN_IOHUB1_N0NBIO0_IOAGR_SION_S1_Client_WrRsp_BurstTarget_Lower_ADDRESS 0x1e000450UL
#define SMN_IOHUB1_N0NBIO1_IOAGR_SION_S1_Client_WrRsp_BurstTarget_Lower_ADDRESS 0x1e200450UL
#define SMN_IOHUB1_N1NBIO0_IOAGR_SION_S1_Client_WrRsp_BurstTarget_Lower_ADDRESS 0x1e000850UL
#define SMN_IOHUB1_N1NBIO1_IOAGR_SION_S1_Client_WrRsp_BurstTarget_Lower_ADDRESS 0x1e200850UL
#define SMN_IOHUB1_N2NBIO0_IOAGR_SION_S1_Client_WrRsp_BurstTarget_Lower_ADDRESS 0x1e000c50UL
#define SMN_IOHUB1_N2NBIO1_IOAGR_SION_S1_Client_WrRsp_BurstTarget_Lower_ADDRESS 0x1e200c50UL
#define SMN_IOHUB2_N0NBIO0_IOAGR_SION_S1_Client_WrRsp_BurstTarget_Lower_ADDRESS 0x15c00450UL
#define SMN_IOHUB2_N0NBIO1_IOAGR_SION_S1_Client_WrRsp_BurstTarget_Lower_ADDRESS 0x15e00450UL
#define SMN_IOHUB2_N1NBIO0_IOAGR_SION_S1_Client_WrRsp_BurstTarget_Lower_ADDRESS 0x15c00850UL
#define SMN_IOHUB2_N1NBIO1_IOAGR_SION_S1_Client_WrRsp_BurstTarget_Lower_ADDRESS 0x15e00850UL
#define SMN_IOHUB2_N2NBIO0_IOAGR_SION_S1_Client_WrRsp_BurstTarget_Lower_ADDRESS 0x15c00c50UL
#define SMN_IOHUB2_N2NBIO1_IOAGR_SION_S1_Client_WrRsp_BurstTarget_Lower_ADDRESS 0x15e00c50UL
#define SMN_IOHUB2_N3NBIO0_IOAGR_SION_S1_Client_WrRsp_BurstTarget_Lower_ADDRESS 0x15c01050UL
#define SMN_IOHUB2_N3NBIO1_IOAGR_SION_S1_Client_WrRsp_BurstTarget_Lower_ADDRESS 0x15e01050UL
#define SMN_IOHUB2_N4NBIO0_IOAGR_SION_S1_Client_WrRsp_BurstTarget_Lower_ADDRESS 0x15c01450UL
#define SMN_IOHUB2_N4NBIO1_IOAGR_SION_S1_Client_WrRsp_BurstTarget_Lower_ADDRESS 0x15e01450UL
#define SMN_IOHUB2_N5NBIO0_IOAGR_SION_S1_Client_WrRsp_BurstTarget_Lower_ADDRESS 0x15c01850UL
#define SMN_IOHUB2_N5NBIO1_IOAGR_SION_S1_Client_WrRsp_BurstTarget_Lower_ADDRESS 0x15e01850UL
#define SMN_IOHUB3_N0NBIO0_IOAGR_SION_S1_Client_WrRsp_BurstTarget_Lower_ADDRESS 0x1e100450UL
#define SMN_IOHUB3_N0NBIO1_IOAGR_SION_S1_Client_WrRsp_BurstTarget_Lower_ADDRESS 0x1e300450UL
#define SMN_IOHUB3_N1NBIO0_IOAGR_SION_S1_Client_WrRsp_BurstTarget_Lower_ADDRESS 0x1e100850UL
#define SMN_IOHUB3_N1NBIO1_IOAGR_SION_S1_Client_WrRsp_BurstTarget_Lower_ADDRESS 0x1e300850UL
#define SMN_IOHUB3_N2NBIO0_IOAGR_SION_S1_Client_WrRsp_BurstTarget_Lower_ADDRESS 0x1e100c50UL
#define SMN_IOHUB3_N2NBIO1_IOAGR_SION_S1_Client_WrRsp_BurstTarget_Lower_ADDRESS 0x1e300c50UL


/***********************************************************
* Register Name : IOAGR_SION_S1_Client_WrRsp_BurstTarget_Upper
************************************************************/

#define IOAGR_SION_S1_Client_WrRsp_BurstTarget_Upper_IOAGR_SION_S1_Client_WrRsp_BurstTarget_Upper_OFFSET 0
#define IOAGR_SION_S1_Client_WrRsp_BurstTarget_Upper_IOAGR_SION_S1_Client_WrRsp_BurstTarget_Upper_MASK 0xffffffff

typedef union {
  struct {
    UINT32        IOAGR_SION_S1_Client_WrRsp_BurstTarget_Upper:32;
  } Field;
  UINT32 Value;
} IOAGR_SION_S1_Client_WrRsp_BurstTarget_Upper_STRUCT;

#define SMN_IOAGR_SION_S1_Client_WrRsp_BurstTarget_Upper_ADDRESS      0x15b00454UL
#define SMN_IOHUB0_N0NBIO0_IOAGR_SION_S1_Client_WrRsp_BurstTarget_Upper_ADDRESS 0x15b00454UL
#define SMN_IOHUB0_N0NBIO1_IOAGR_SION_S1_Client_WrRsp_BurstTarget_Upper_ADDRESS 0x15d00454UL
#define SMN_IOHUB0_N1NBIO0_IOAGR_SION_S1_Client_WrRsp_BurstTarget_Upper_ADDRESS 0x15b00854UL
#define SMN_IOHUB0_N1NBIO1_IOAGR_SION_S1_Client_WrRsp_BurstTarget_Upper_ADDRESS 0x15d00854UL
#define SMN_IOHUB0_N2NBIO0_IOAGR_SION_S1_Client_WrRsp_BurstTarget_Upper_ADDRESS 0x15b00c54UL
#define SMN_IOHUB0_N2NBIO1_IOAGR_SION_S1_Client_WrRsp_BurstTarget_Upper_ADDRESS 0x15d00c54UL
#define SMN_IOHUB0_N3NBIO0_IOAGR_SION_S1_Client_WrRsp_BurstTarget_Upper_ADDRESS 0x15b01054UL
#define SMN_IOHUB0_N3NBIO1_IOAGR_SION_S1_Client_WrRsp_BurstTarget_Upper_ADDRESS 0x15d01054UL
#define SMN_IOHUB0_N4NBIO0_IOAGR_SION_S1_Client_WrRsp_BurstTarget_Upper_ADDRESS 0x15b01454UL
#define SMN_IOHUB0_N4NBIO1_IOAGR_SION_S1_Client_WrRsp_BurstTarget_Upper_ADDRESS 0x15d01454UL
#define SMN_IOHUB0_N5NBIO0_IOAGR_SION_S1_Client_WrRsp_BurstTarget_Upper_ADDRESS 0x15b01854UL
#define SMN_IOHUB0_N5NBIO1_IOAGR_SION_S1_Client_WrRsp_BurstTarget_Upper_ADDRESS 0x15d01854UL
#define SMN_IOHUB1_N0NBIO0_IOAGR_SION_S1_Client_WrRsp_BurstTarget_Upper_ADDRESS 0x1e000454UL
#define SMN_IOHUB1_N0NBIO1_IOAGR_SION_S1_Client_WrRsp_BurstTarget_Upper_ADDRESS 0x1e200454UL
#define SMN_IOHUB1_N1NBIO0_IOAGR_SION_S1_Client_WrRsp_BurstTarget_Upper_ADDRESS 0x1e000854UL
#define SMN_IOHUB1_N1NBIO1_IOAGR_SION_S1_Client_WrRsp_BurstTarget_Upper_ADDRESS 0x1e200854UL
#define SMN_IOHUB1_N2NBIO0_IOAGR_SION_S1_Client_WrRsp_BurstTarget_Upper_ADDRESS 0x1e000c54UL
#define SMN_IOHUB1_N2NBIO1_IOAGR_SION_S1_Client_WrRsp_BurstTarget_Upper_ADDRESS 0x1e200c54UL
#define SMN_IOHUB2_N0NBIO0_IOAGR_SION_S1_Client_WrRsp_BurstTarget_Upper_ADDRESS 0x15c00454UL
#define SMN_IOHUB2_N0NBIO1_IOAGR_SION_S1_Client_WrRsp_BurstTarget_Upper_ADDRESS 0x15e00454UL
#define SMN_IOHUB2_N1NBIO0_IOAGR_SION_S1_Client_WrRsp_BurstTarget_Upper_ADDRESS 0x15c00854UL
#define SMN_IOHUB2_N1NBIO1_IOAGR_SION_S1_Client_WrRsp_BurstTarget_Upper_ADDRESS 0x15e00854UL
#define SMN_IOHUB2_N2NBIO0_IOAGR_SION_S1_Client_WrRsp_BurstTarget_Upper_ADDRESS 0x15c00c54UL
#define SMN_IOHUB2_N2NBIO1_IOAGR_SION_S1_Client_WrRsp_BurstTarget_Upper_ADDRESS 0x15e00c54UL
#define SMN_IOHUB2_N3NBIO0_IOAGR_SION_S1_Client_WrRsp_BurstTarget_Upper_ADDRESS 0x15c01054UL
#define SMN_IOHUB2_N3NBIO1_IOAGR_SION_S1_Client_WrRsp_BurstTarget_Upper_ADDRESS 0x15e01054UL
#define SMN_IOHUB2_N4NBIO0_IOAGR_SION_S1_Client_WrRsp_BurstTarget_Upper_ADDRESS 0x15c01454UL
#define SMN_IOHUB2_N4NBIO1_IOAGR_SION_S1_Client_WrRsp_BurstTarget_Upper_ADDRESS 0x15e01454UL
#define SMN_IOHUB2_N5NBIO0_IOAGR_SION_S1_Client_WrRsp_BurstTarget_Upper_ADDRESS 0x15c01854UL
#define SMN_IOHUB2_N5NBIO1_IOAGR_SION_S1_Client_WrRsp_BurstTarget_Upper_ADDRESS 0x15e01854UL
#define SMN_IOHUB3_N0NBIO0_IOAGR_SION_S1_Client_WrRsp_BurstTarget_Upper_ADDRESS 0x1e100454UL
#define SMN_IOHUB3_N0NBIO1_IOAGR_SION_S1_Client_WrRsp_BurstTarget_Upper_ADDRESS 0x1e300454UL
#define SMN_IOHUB3_N1NBIO0_IOAGR_SION_S1_Client_WrRsp_BurstTarget_Upper_ADDRESS 0x1e100854UL
#define SMN_IOHUB3_N1NBIO1_IOAGR_SION_S1_Client_WrRsp_BurstTarget_Upper_ADDRESS 0x1e300854UL
#define SMN_IOHUB3_N2NBIO0_IOAGR_SION_S1_Client_WrRsp_BurstTarget_Upper_ADDRESS 0x1e100c54UL
#define SMN_IOHUB3_N2NBIO1_IOAGR_SION_S1_Client_WrRsp_BurstTarget_Upper_ADDRESS 0x1e300c54UL


/***********************************************************
* Register Name : IOAGR_SION_S1_Client_WrRsp_TimeSlot_Lower
************************************************************/

#define IOAGR_SION_S1_Client_WrRsp_TimeSlot_Lower_IOAGR_SION_S1_Client_WrRsp_TimeSlot_Lower_OFFSET 0
#define IOAGR_SION_S1_Client_WrRsp_TimeSlot_Lower_IOAGR_SION_S1_Client_WrRsp_TimeSlot_Lower_MASK 0xffffffff

typedef union {
  struct {
    UINT32           IOAGR_SION_S1_Client_WrRsp_TimeSlot_Lower:32;
  } Field;
  UINT32 Value;
} IOAGR_SION_S1_Client_WrRsp_TimeSlot_Lower_STRUCT;

#define SMN_IOAGR_SION_S1_Client_WrRsp_TimeSlot_Lower_ADDRESS         0x15b00458UL
#define SMN_IOHUB0_N0NBIO0_IOAGR_SION_S1_Client_WrRsp_TimeSlot_Lower_ADDRESS 0x15b00458UL
#define SMN_IOHUB0_N0NBIO1_IOAGR_SION_S1_Client_WrRsp_TimeSlot_Lower_ADDRESS 0x15d00458UL
#define SMN_IOHUB0_N1NBIO0_IOAGR_SION_S1_Client_WrRsp_TimeSlot_Lower_ADDRESS 0x15b00858UL
#define SMN_IOHUB0_N1NBIO1_IOAGR_SION_S1_Client_WrRsp_TimeSlot_Lower_ADDRESS 0x15d00858UL
#define SMN_IOHUB0_N2NBIO0_IOAGR_SION_S1_Client_WrRsp_TimeSlot_Lower_ADDRESS 0x15b00c58UL
#define SMN_IOHUB0_N2NBIO1_IOAGR_SION_S1_Client_WrRsp_TimeSlot_Lower_ADDRESS 0x15d00c58UL
#define SMN_IOHUB0_N3NBIO0_IOAGR_SION_S1_Client_WrRsp_TimeSlot_Lower_ADDRESS 0x15b01058UL
#define SMN_IOHUB0_N3NBIO1_IOAGR_SION_S1_Client_WrRsp_TimeSlot_Lower_ADDRESS 0x15d01058UL
#define SMN_IOHUB0_N4NBIO0_IOAGR_SION_S1_Client_WrRsp_TimeSlot_Lower_ADDRESS 0x15b01458UL
#define SMN_IOHUB0_N4NBIO1_IOAGR_SION_S1_Client_WrRsp_TimeSlot_Lower_ADDRESS 0x15d01458UL
#define SMN_IOHUB0_N5NBIO0_IOAGR_SION_S1_Client_WrRsp_TimeSlot_Lower_ADDRESS 0x15b01858UL
#define SMN_IOHUB0_N5NBIO1_IOAGR_SION_S1_Client_WrRsp_TimeSlot_Lower_ADDRESS 0x15d01858UL
#define SMN_IOHUB1_N0NBIO0_IOAGR_SION_S1_Client_WrRsp_TimeSlot_Lower_ADDRESS 0x1e000458UL
#define SMN_IOHUB1_N0NBIO1_IOAGR_SION_S1_Client_WrRsp_TimeSlot_Lower_ADDRESS 0x1e200458UL
#define SMN_IOHUB1_N1NBIO0_IOAGR_SION_S1_Client_WrRsp_TimeSlot_Lower_ADDRESS 0x1e000858UL
#define SMN_IOHUB1_N1NBIO1_IOAGR_SION_S1_Client_WrRsp_TimeSlot_Lower_ADDRESS 0x1e200858UL
#define SMN_IOHUB1_N2NBIO0_IOAGR_SION_S1_Client_WrRsp_TimeSlot_Lower_ADDRESS 0x1e000c58UL
#define SMN_IOHUB1_N2NBIO1_IOAGR_SION_S1_Client_WrRsp_TimeSlot_Lower_ADDRESS 0x1e200c58UL
#define SMN_IOHUB2_N0NBIO0_IOAGR_SION_S1_Client_WrRsp_TimeSlot_Lower_ADDRESS 0x15c00458UL
#define SMN_IOHUB2_N0NBIO1_IOAGR_SION_S1_Client_WrRsp_TimeSlot_Lower_ADDRESS 0x15e00458UL
#define SMN_IOHUB2_N1NBIO0_IOAGR_SION_S1_Client_WrRsp_TimeSlot_Lower_ADDRESS 0x15c00858UL
#define SMN_IOHUB2_N1NBIO1_IOAGR_SION_S1_Client_WrRsp_TimeSlot_Lower_ADDRESS 0x15e00858UL
#define SMN_IOHUB2_N2NBIO0_IOAGR_SION_S1_Client_WrRsp_TimeSlot_Lower_ADDRESS 0x15c00c58UL
#define SMN_IOHUB2_N2NBIO1_IOAGR_SION_S1_Client_WrRsp_TimeSlot_Lower_ADDRESS 0x15e00c58UL
#define SMN_IOHUB2_N3NBIO0_IOAGR_SION_S1_Client_WrRsp_TimeSlot_Lower_ADDRESS 0x15c01058UL
#define SMN_IOHUB2_N3NBIO1_IOAGR_SION_S1_Client_WrRsp_TimeSlot_Lower_ADDRESS 0x15e01058UL
#define SMN_IOHUB2_N4NBIO0_IOAGR_SION_S1_Client_WrRsp_TimeSlot_Lower_ADDRESS 0x15c01458UL
#define SMN_IOHUB2_N4NBIO1_IOAGR_SION_S1_Client_WrRsp_TimeSlot_Lower_ADDRESS 0x15e01458UL
#define SMN_IOHUB2_N5NBIO0_IOAGR_SION_S1_Client_WrRsp_TimeSlot_Lower_ADDRESS 0x15c01858UL
#define SMN_IOHUB2_N5NBIO1_IOAGR_SION_S1_Client_WrRsp_TimeSlot_Lower_ADDRESS 0x15e01858UL
#define SMN_IOHUB3_N0NBIO0_IOAGR_SION_S1_Client_WrRsp_TimeSlot_Lower_ADDRESS 0x1e100458UL
#define SMN_IOHUB3_N0NBIO1_IOAGR_SION_S1_Client_WrRsp_TimeSlot_Lower_ADDRESS 0x1e300458UL
#define SMN_IOHUB3_N1NBIO0_IOAGR_SION_S1_Client_WrRsp_TimeSlot_Lower_ADDRESS 0x1e100858UL
#define SMN_IOHUB3_N1NBIO1_IOAGR_SION_S1_Client_WrRsp_TimeSlot_Lower_ADDRESS 0x1e300858UL
#define SMN_IOHUB3_N2NBIO0_IOAGR_SION_S1_Client_WrRsp_TimeSlot_Lower_ADDRESS 0x1e100c58UL
#define SMN_IOHUB3_N2NBIO1_IOAGR_SION_S1_Client_WrRsp_TimeSlot_Lower_ADDRESS 0x1e300c58UL


/***********************************************************
* Register Name : IOAGR_SION_S1_Client_WrRsp_TimeSlot_Upper
************************************************************/

#define IOAGR_SION_S1_Client_WrRsp_TimeSlot_Upper_IOAGR_SION_S1_Client_WrRsp_TimeSlot_Upper_OFFSET 0
#define IOAGR_SION_S1_Client_WrRsp_TimeSlot_Upper_IOAGR_SION_S1_Client_WrRsp_TimeSlot_Upper_MASK 0xffffffff

typedef union {
  struct {
    UINT32           IOAGR_SION_S1_Client_WrRsp_TimeSlot_Upper:32;
  } Field;
  UINT32 Value;
} IOAGR_SION_S1_Client_WrRsp_TimeSlot_Upper_STRUCT;

#define SMN_IOAGR_SION_S1_Client_WrRsp_TimeSlot_Upper_ADDRESS         0x15b0045cUL
#define SMN_IOHUB0_N0NBIO0_IOAGR_SION_S1_Client_WrRsp_TimeSlot_Upper_ADDRESS 0x15b0045cUL
#define SMN_IOHUB0_N0NBIO1_IOAGR_SION_S1_Client_WrRsp_TimeSlot_Upper_ADDRESS 0x15d0045cUL
#define SMN_IOHUB0_N1NBIO0_IOAGR_SION_S1_Client_WrRsp_TimeSlot_Upper_ADDRESS 0x15b0085cUL
#define SMN_IOHUB0_N1NBIO1_IOAGR_SION_S1_Client_WrRsp_TimeSlot_Upper_ADDRESS 0x15d0085cUL
#define SMN_IOHUB0_N2NBIO0_IOAGR_SION_S1_Client_WrRsp_TimeSlot_Upper_ADDRESS 0x15b00c5cUL
#define SMN_IOHUB0_N2NBIO1_IOAGR_SION_S1_Client_WrRsp_TimeSlot_Upper_ADDRESS 0x15d00c5cUL
#define SMN_IOHUB0_N3NBIO0_IOAGR_SION_S1_Client_WrRsp_TimeSlot_Upper_ADDRESS 0x15b0105cUL
#define SMN_IOHUB0_N3NBIO1_IOAGR_SION_S1_Client_WrRsp_TimeSlot_Upper_ADDRESS 0x15d0105cUL
#define SMN_IOHUB0_N4NBIO0_IOAGR_SION_S1_Client_WrRsp_TimeSlot_Upper_ADDRESS 0x15b0145cUL
#define SMN_IOHUB0_N4NBIO1_IOAGR_SION_S1_Client_WrRsp_TimeSlot_Upper_ADDRESS 0x15d0145cUL
#define SMN_IOHUB0_N5NBIO0_IOAGR_SION_S1_Client_WrRsp_TimeSlot_Upper_ADDRESS 0x15b0185cUL
#define SMN_IOHUB0_N5NBIO1_IOAGR_SION_S1_Client_WrRsp_TimeSlot_Upper_ADDRESS 0x15d0185cUL
#define SMN_IOHUB1_N0NBIO0_IOAGR_SION_S1_Client_WrRsp_TimeSlot_Upper_ADDRESS 0x1e00045cUL
#define SMN_IOHUB1_N0NBIO1_IOAGR_SION_S1_Client_WrRsp_TimeSlot_Upper_ADDRESS 0x1e20045cUL
#define SMN_IOHUB1_N1NBIO0_IOAGR_SION_S1_Client_WrRsp_TimeSlot_Upper_ADDRESS 0x1e00085cUL
#define SMN_IOHUB1_N1NBIO1_IOAGR_SION_S1_Client_WrRsp_TimeSlot_Upper_ADDRESS 0x1e20085cUL
#define SMN_IOHUB1_N2NBIO0_IOAGR_SION_S1_Client_WrRsp_TimeSlot_Upper_ADDRESS 0x1e000c5cUL
#define SMN_IOHUB1_N2NBIO1_IOAGR_SION_S1_Client_WrRsp_TimeSlot_Upper_ADDRESS 0x1e200c5cUL
#define SMN_IOHUB2_N0NBIO0_IOAGR_SION_S1_Client_WrRsp_TimeSlot_Upper_ADDRESS 0x15c0045cUL
#define SMN_IOHUB2_N0NBIO1_IOAGR_SION_S1_Client_WrRsp_TimeSlot_Upper_ADDRESS 0x15e0045cUL
#define SMN_IOHUB2_N1NBIO0_IOAGR_SION_S1_Client_WrRsp_TimeSlot_Upper_ADDRESS 0x15c0085cUL
#define SMN_IOHUB2_N1NBIO1_IOAGR_SION_S1_Client_WrRsp_TimeSlot_Upper_ADDRESS 0x15e0085cUL
#define SMN_IOHUB2_N2NBIO0_IOAGR_SION_S1_Client_WrRsp_TimeSlot_Upper_ADDRESS 0x15c00c5cUL
#define SMN_IOHUB2_N2NBIO1_IOAGR_SION_S1_Client_WrRsp_TimeSlot_Upper_ADDRESS 0x15e00c5cUL
#define SMN_IOHUB2_N3NBIO0_IOAGR_SION_S1_Client_WrRsp_TimeSlot_Upper_ADDRESS 0x15c0105cUL
#define SMN_IOHUB2_N3NBIO1_IOAGR_SION_S1_Client_WrRsp_TimeSlot_Upper_ADDRESS 0x15e0105cUL
#define SMN_IOHUB2_N4NBIO0_IOAGR_SION_S1_Client_WrRsp_TimeSlot_Upper_ADDRESS 0x15c0145cUL
#define SMN_IOHUB2_N4NBIO1_IOAGR_SION_S1_Client_WrRsp_TimeSlot_Upper_ADDRESS 0x15e0145cUL
#define SMN_IOHUB2_N5NBIO0_IOAGR_SION_S1_Client_WrRsp_TimeSlot_Upper_ADDRESS 0x15c0185cUL
#define SMN_IOHUB2_N5NBIO1_IOAGR_SION_S1_Client_WrRsp_TimeSlot_Upper_ADDRESS 0x15e0185cUL
#define SMN_IOHUB3_N0NBIO0_IOAGR_SION_S1_Client_WrRsp_TimeSlot_Upper_ADDRESS 0x1e10045cUL
#define SMN_IOHUB3_N0NBIO1_IOAGR_SION_S1_Client_WrRsp_TimeSlot_Upper_ADDRESS 0x1e30045cUL
#define SMN_IOHUB3_N1NBIO0_IOAGR_SION_S1_Client_WrRsp_TimeSlot_Upper_ADDRESS 0x1e10085cUL
#define SMN_IOHUB3_N1NBIO1_IOAGR_SION_S1_Client_WrRsp_TimeSlot_Upper_ADDRESS 0x1e30085cUL
#define SMN_IOHUB3_N2NBIO0_IOAGR_SION_S1_Client_WrRsp_TimeSlot_Upper_ADDRESS 0x1e100c5cUL
#define SMN_IOHUB3_N2NBIO1_IOAGR_SION_S1_Client_WrRsp_TimeSlot_Upper_ADDRESS 0x1e300c5cUL

#endif /* _IOAGR_H_ */

