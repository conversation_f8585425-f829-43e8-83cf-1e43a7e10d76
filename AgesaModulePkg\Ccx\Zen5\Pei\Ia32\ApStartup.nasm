;****************************************************************************
; Copyright (C) 2024 Advanced Micro Devices, Inc. All rights reserved.
;
;****************************************************************************
;/**
; * @file
; *
; *
; * @xrefitem bom "File Content Label" "Release Content"
; * @e project:      AGESA
; * @e sub-project:  Ccx
; */

SECTION .text

%define LINEAR_CODE_SEL 10h
%define SYS_DATA_SEL    18h

ALIGN   16

global ASM_PFX(gApStartupCode)
global ASM_PFX(gPatchProtectedModeJump)
global ASM_PFX(gPatchApEntryInCOffset)
global ASM_PFX(gPatchApLaunchGlobalData)
global ASM_PFX(gApStartupCodeEnd)

BITS 16
ASM_PFX(gApStartupCode):
  mov eax, 0B000ACB0h          ; TpCcxApLauchStartupCode
  out 80h, eax
  mov si, 0FFF4h
o32  lgdt       cs:[si]
  mov        eax, cr0                    ; Get control register 0
  or         eax, 000000003h             ; Set PE bit (bit #0)
  mov        cr0, eax
  mov        eax, cr4
  or         eax, 00000600h
  mov        cr4, eax
  mov        ax,  SYS_DATA_SEL
  mov        ds,  ax
  mov        es,  ax
  mov        fs,  ax
  mov        gs,  ax
  mov        ss,  ax
  jmp        LINEAR_CODE_SEL:dword 0
ASM_PFX(gPatchProtectedModeJump):
BITS 32
  mov eax, 0
ASM_PFX(gPatchApEntryInCOffset):
  mov edi, 0
ASM_PFX(gPatchApLaunchGlobalData):
  jmp eax
ASM_PFX(gApStartupCodeEnd):

