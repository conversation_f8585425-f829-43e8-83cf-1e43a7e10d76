/******************************************************************************
*
 * Copyright (C) 2008-2024 Advanced Micro Devices, Inc. All rights reserved.
 *
*******************************************************************************
**/
/* $NoKeywords:$ */
/**
 * @file
 *
 * AMD Fch APU RAS SMI Dispatcher Driver
 *
 *
 * @xrefitem bom "File Content Label" "Release Content"
 * @e project:      AGESA
 * @e sub-project:  UEFI
 * @e \$Revision: 309090 $   @e \$Date: 2014-12-09 10:28:05 -0800 (Tue, 09 Dec 2014) $
 *
 */

#include "FchSmmDispatcher.h"
#include "Filecode.h"
#include "FchPlatform.h"
#include "FchRasSmiLib.h"
#include "FchSmmUsbDispatcher.h"


#define FILECODE FCH_KUNLUN_FCHKUNLUNSMMDISPATCHER_FCHSMMAPURASDISPATCHER_FCHSSPRASSMILIB_FILECODE

extern FCH_SMM_USB_NODE     *HeadFchSmmUsbNodePtr;
extern FCH_USB_SMI_SYSINFO  FchUsbSmiSysInfo;
extern UINTN                gSmmNumberOfSocketEnabled;

STATIC
FCH_SMM_APURAS_SYSINFO RasSystemInfo = {0};


/*
+================================================================+
|          IOHC            | Socket |  Die   |   Bus  |NbioNumber|
+================================================================+
|Socket[0].NBIO[0].IOHC[0] |   0    |   0    |   0    |    0     |
+----------------------------------------------------------------+
|Socket[0].NBIO[0].IOHC[1] |   0    |   0    |   0    |    3     |
+----------------------------------------------------------------+
|Socket[0].NBIO[0].IOHC[2] |   0    |   0    |   0    |    2     |
+----------------------------------------------------------------+
|Socket[0].NBIO[0].IOHC[3] |   0    |   0    |   0    |    1     |
+----------------------------------------------------------------+
|Socket[0].NBIO[1].IOHC[0] |   0    |   0    |   0    |    4     |
+----------------------------------------------------------------+
|Socket[0].NBIO[1].IOHC[1] |   0    |   0    |   0    |    7     |
+----------------------------------------------------------------+
|Socket[0].NBIO[1].IOHC[2] |   0    |   0    |   0    |    6     |
+----------------------------------------------------------------+
|Socket[0].NBIO[1].IOHC[3] |   0    |   0    |   0    |    5     |
+----------------------------------------------------------------+
|Socket[1].NBIO[0].IOHC[0] |   1    |   0    |  TBD   |    0     |
+----------------------------------------------------------------+
|Socket[1].NBIO[0].IOHC[1] |   1    |   0    |  TBD   |    3     |
+----------------------------------------------------------------+
|Socket[1].NBIO[0].IOHC[2] |   1    |   0    |  TBD   |    2     |
+----------------------------------------------------------------+
|Socket[1].NBIO[0].IOHC[3] |   1    |   0    |  TBD   |    1     |
+----------------------------------------------------------------+
|Socket[1].NBIO[1].IOHC[0] |   1    |   0    |  TBD   |    4     |
+----------------------------------------------------------------+
|Socket[1].NBIO[1].IOHC[1] |   1    |   0    |  TBD   |    7     |
+----------------------------------------------------------------+
|Socket[1].NBIO[1].IOHC[2] |   1    |   0    |  TBD   |    6     |
+----------------------------------------------------------------+
|Socket[1].NBIO[1].IOHC[3] |   1    |   0    |  TBD   |    5     |
+================================================================+
*/

FCH_IOHC_RAS_REGISTER FchIochRasRegisters[FCH_MAX_NBIO_PER_SOCKET*FCH_MAX_IOHC_PER_NBIO] = {
  // NBIO[0].IOHC[0]
  {
    SMN_IOHUB0NBIO0_RAS_GLOBAL_STATUS_LO_ADDRESS,     // RAS_GLOBAL_STATUS_LO
    SMN_IOHUB0NBIO0_MISC_RAS_CONTROL_ADDRESS,         // MISC_RAS_CONTROL
    SMN_IOHUB0NBIO0_IOHC_INTERRUPT_EOI_ADDRESS,       // IOHC_INTERRUPT_EOI
    BIT0
  },

  // NBIO[0].IOHC[3]
  {
    SMN_IOHUB3NBIO0_RAS_GLOBAL_STATUS_LO_ADDRESS,     // RAS_GLOBAL_STATUS_LO
    SMN_IOHUB3NBIO0_MISC_RAS_CONTROL_ADDRESS,         // MISC_RAS_CONTROL
    SMN_IOHUB3NBIO0_IOHC_INTERRUPT_EOI_ADDRESS,       // IOHC_INTERRUPT_EOI
    0
  },

  // NBIO[0].IOHC[2]
  {
    SMN_IOHUB2NBIO0_RAS_GLOBAL_STATUS_LO_ADDRESS,     // RAS_GLOBAL_STATUS_LO
    SMN_IOHUB2NBIO0_MISC_RAS_CONTROL_ADDRESS,         // MISC_RAS_CONTROL
    SMN_IOHUB2NBIO0_IOHC_INTERRUPT_EOI_ADDRESS,       // IOHC_INTERRUPT_EOI
    0
  },

  // NBIO[0].IOHC[1]
  {
    SMN_IOHUB1NBIO0_RAS_GLOBAL_STATUS_LO_ADDRESS,     // RAS_GLOBAL_STATUS_LO
    SMN_IOHUB1NBIO0_MISC_RAS_CONTROL_ADDRESS,         // MISC_RAS_CONTROL
    SMN_IOHUB1NBIO0_IOHC_INTERRUPT_EOI_ADDRESS,       // IOHC_INTERRUPT_EOI
    0
  },

  // NBIO[1].IOHC[0]
  {
    SMN_IOHUB0NBIO1_RAS_GLOBAL_STATUS_LO_ADDRESS,     // RAS_GLOBAL_STATUS_LO
    SMN_IOHUB0NBIO1_MISC_RAS_CONTROL_ADDRESS,         // MISC_RAS_CONTROL
    SMN_IOHUB0NBIO1_IOHC_INTERRUPT_EOI_ADDRESS,       // IOHC_INTERRUPT_EOI
    BIT0
  },

  // NBIO[1].IOHC[3]
  {
    SMN_IOHUB3NBIO1_RAS_GLOBAL_STATUS_LO_ADDRESS,     // RAS_GLOBAL_STATUS_LO
    SMN_IOHUB3NBIO1_MISC_RAS_CONTROL_ADDRESS,         // MISC_RAS_CONTROL
    SMN_IOHUB3NBIO1_IOHC_INTERRUPT_EOI_ADDRESS,       // IOHC_INTERRUPT_EOI
    0
  },

  // NBIO[1].IOHC[2]
  {
    SMN_IOHUB2NBIO1_RAS_GLOBAL_STATUS_LO_ADDRESS,     // RAS_GLOBAL_STATUS_LO
    SMN_IOHUB2NBIO1_MISC_RAS_CONTROL_ADDRESS,         // MISC_RAS_CONTROL
    SMN_IOHUB2NBIO1_IOHC_INTERRUPT_EOI_ADDRESS,       // IOHC_INTERRUPT_EOI
    0
  },

  // NBIO[1].IOHC[1]
  {
    SMN_IOHUB1NBIO1_RAS_GLOBAL_STATUS_LO_ADDRESS,     // RAS_GLOBAL_STATUS_LO
    SMN_IOHUB1NBIO1_MISC_RAS_CONTROL_ADDRESS,         // MISC_RAS_CONTROL
    SMN_IOHUB1NBIO1_IOHC_INTERRUPT_EOI_ADDRESS,       // IOHC_INTERRUPT_EOI
    0
  }
};

UINT32  FchIochRasRegistersCount = sizeof (FchIochRasRegisters) / sizeof (FCH_IOHC_RAS_REGISTER);


BOOLEAN
IsFchSmmApuRasEnabled (
  VOID
  )
{
  UINT32  Value32;

  Value32 = ACPIMMIO32 (ACPI_MMIO_BASE + SMI_BASE + FCH_SMI_REGAC);
  Value32 = (Value32 >> 10) & 3;

  if (Value32 == 1) {
    return TRUE;
  } else {
    return FALSE;
  }
}

BOOLEAN
IsAnyIohcRasSmiEnabled (
  VOID
  )
{
  UINT32 i;

  for (i = 0; i < FCH_MAX_SOCKET; i++) {
    if (RasSystemInfo.SocketInfo[i].IohcRasSwiEn) {
      return TRUE;
    }
  }

  return FALSE;
}


/*----------------------------------------------------------------------------------------*/
/**
 * Name: FchSspSmmApuRasClearSmiSts
 *    FCH SMM APU RAS Clear SMI status
 *
 *
 */
/*----------------------------------------------------------------------------------------*/
VOID
FchSmmApuRasClearSmiSts (
  VOID
  )
{
  UINT8   Iohc;
  UINT32  i;

  Iohc    = 0;
  i       = 0;

  //
  // 1. Clear SMI event status bit in FCH
  //
  ACPIMMIO32 (ACPI_MMIO_BASE + SMI_BASE + FCH_SMI_REG84) = ApuRasSmi;

  //
  // 2. Clear NBIO/IOHC status if registered
  //
  for (i = 0; i < gSmmNumberOfSocketEnabled; i++) {
    if (RasSystemInfo.SocketInfo[i].IohcRasSwiEn) {               //Socket i
      for (Iohc = 0; Iohc < FchIochRasRegistersCount; Iohc++) {
        if (RasSystemInfo.SocketInfo[i].IohcRasSwiEn & (1 << Iohc)) {
          FchSmnRW (RasSystemInfo.SocketInfo[i].Bus, FchIochRasRegisters[Iohc].IOHC_INTERRUPT_EOI,   0xfffffffe, BIT0, NULL);
          FchSmnRW (RasSystemInfo.SocketInfo[i].Bus, FchIochRasRegisters[Iohc].RAS_GLOBAL_STATUS_LO, 0xfffffeff, BIT8, NULL);
        }
      }
    }
  }
}


/**
 * @brief Get Socket 1 Bus Number.
 *
 * @returns UINTN        2nd socket bus number
 */
UINTN
FchSmmRasGetSocket1Bus (
  VOID
  )
{
  EFI_STATUS                              Status;
  UINTN                                   BusNumberBase;
  AMD_FABRIC_TOPOLOGY_SERVICES2_PROTOCOL  *FabricTopology;
  BOOLEAN                                 HasFchDevice;
  UINTN                                   i;
  UINTN                                   NumberOfRootBridges;

  Status          = EFI_SUCCESS;
  FabricTopology  = NULL;
  BusNumberBase   = 0;

  Status = gSmst->SmmLocateProtocol (
                    &gAmdFabricTopologyServices2SmmProtocolGuid,
                    NULL,
                    (VOID **) &FabricTopology
                    );
  if (Status == EFI_SUCCESS) {
    Status = FabricTopology->GetDieInfo (
                               FabricTopology,
                               FCH_SECOND_SOCKET,
                               FCH_SECOND_SOCKET_DIE,
                               &NumberOfRootBridges,
                               NULL,
                               NULL
                               );
    if (Status == EFI_SUCCESS) {
      for (i = 0; i < NumberOfRootBridges; i++) {
        Status = FabricTopology->GetRootBridgeInfo (
                                   FabricTopology,
                                   FCH_SECOND_SOCKET,
                                   FCH_SECOND_SOCKET_DIE,
                                   i,
                                   NULL,
                                   &BusNumberBase,
                                   NULL,
                                   NULL,
                                   &HasFchDevice,
                                   NULL
                                   );
        if (!EFI_ERROR (Status) && HasFchDevice) {
          DEBUG ((DEBUG_INFO, "[%a] Multi FCH SMM Get 2nd Socket Bus Number = 0x%x\n", __FUNCTION__, BusNumberBase));
          return BusNumberBase;
        }
      }
    }
  }

  return 0;
}

/*----------------------------------------------------------------------------------------*/
/**
 * Name: FchSspSmmApuRasSmiEnable
 *    FCH SMM APU RAS Enable RAS SMI
 *
 *
 * @param[in]       ApuRasRegisterContext        RAS SMI registeration context info
 *
 */
/*----------------------------------------------------------------------------------------*/
VOID
FchSmmApuRasSmiEnable (
  IN       FCH_SMM_APURAS_REGISTER_CONTEXT          *ApuRasRegisterContext
  )
{
  ASSERT (ApuRasRegisterContext);
  ASSERT (ApuRasRegisterContext->Socket < 2);
  ASSERT (ApuRasRegisterContext->NbioNumber < 8);

  if ( ApuRasRegisterContext->Socket >= gSmmNumberOfSocketEnabled ) {
    IDS_HDT_CONSOLE (
      FCH_TRACE,
      "[%a] ERROR: Total %d socket(s), the value of socket argument %d is invalid.\n",
      __FUNCTION__,
      gSmmNumberOfSocketEnabled,
      ApuRasRegisterContext->Socket
      );
    return;
  }

  //
  // Enable SMI on NBIO/IOHC side
  //
  if (RasSystemInfo.SocketInfo[ApuRasRegisterContext->Socket].IohcRasSwiEn & (1 << ApuRasRegisterContext->NbioNumber)) {
    IDS_HDT_CONSOLE (
      FCH_TRACE,
      "[%a] RAS SMI of socket %d Iohc %d is already enabled\n",
      __FUNCTION__,
      ApuRasRegisterContext->Socket,
      ApuRasRegisterContext->NbioNumber
      );
  } else {
    IDS_HDT_CONSOLE (
      FCH_TRACE,
      "[%a] Enable socket %d Iohc %d RAS SMI\n",
      __FUNCTION__,
      ApuRasRegisterContext->Socket,
      ApuRasRegisterContext->NbioNumber
      );

    if ( ApuRasRegisterContext->Socket == 0 ) {
      RasSystemInfo.SocketInfo[ApuRasRegisterContext->Socket].Bus = 0;
    } else {
      RasSystemInfo.SocketInfo[ApuRasRegisterContext->Socket].Bus = (UINT32) FchSmmRasGetSocket1Bus ();
      // Bus equal 0 means get 2nd bus number failed.
      ASSERT (RasSystemInfo.SocketInfo[ApuRasRegisterContext->Socket].Bus != 0);
    }

    IDS_HDT_CONSOLE (
      FCH_TRACE,
      "[%a] socket %d bus number 0x%x\n",
      __FUNCTION__,
      ApuRasRegisterContext->Socket,
      RasSystemInfo.SocketInfo[ApuRasRegisterContext->Socket].Bus
      );

    // Set IOHC::MISC_RAS_CONTROL
    FchSmnRW (
      RasSystemInfo.SocketInfo[ApuRasRegisterContext->Socket].Bus,
      FchIochRasRegisters[ApuRasRegisterContext->NbioNumber].MISC_RAS_CONTROL,
      0xffffbfff,
      BIT14,
      NULL
      );

    // Clear IOHC::RAS_GLOBAL_STATUS_LO:HPLGWA_SMI
    FchSmnRW (
      RasSystemInfo.SocketInfo[ApuRasRegisterContext->Socket].Bus,
      FchIochRasRegisters[ApuRasRegisterContext->NbioNumber].RAS_GLOBAL_STATUS_LO,
      0xfffffeff,
      BIT8,
      NULL
      );

    // Clear IOHC::IOHC_INTERRUPT_EOI:SMI_EOI
    FchSmnRW (
      RasSystemInfo.SocketInfo[ApuRasRegisterContext->Socket].Bus,
      FchIochRasRegisters[ApuRasRegisterContext->NbioNumber].IOHC_INTERRUPT_EOI,
      0xfffffffe,
      BIT0,
      NULL
      );

    // Set IOHC bitmap
    RasSystemInfo.SocketInfo[ApuRasRegisterContext->Socket].IohcRasSwiEn |= 1 << ApuRasRegisterContext->NbioNumber;
  }

  IDS_HDT_CONSOLE (
    FCH_TRACE,
    "[%a] IsFchSmmApuRasEnabled = 0x%x, SocketNbioRasSmiEn[0] = 0x%x, SocketNbioRasSmiEn[1] = 0x%x\n",
    __FUNCTION__,
    IsFchSmmApuRasEnabled (),
    RasSystemInfo.SocketInfo[0].IohcRasSwiEn,
    RasSystemInfo.SocketInfo[1].IohcRasSwiEn
    );

  //
  // Enable APU HW SMI control in FCH if required
  //
  if (!IsFchSmmApuRasEnabled () && IsAnyIohcRasSmiEnabled ()) {
    IDS_HDT_CONSOLE (
      FCH_TRACE,
      "[%a] FCH APU SMI is not enabled and IOHC RAS SMI is enabled, enable FCH APU SMI\n",
      __FUNCTION__
      );

    ACPIMMIO32 (ACPI_MMIO_BASE + SMI_BASE + FCH_SMI_REG84)  = ApuRasSmi;
    ACPIMMIO32 (ACPI_MMIO_BASE + SMI_BASE + FCH_SMI_REGAC) &= ~(UINT32)(3 << 10);
    ACPIMMIO32 (ACPI_MMIO_BASE + SMI_BASE + FCH_SMI_REGAC) |= BIT10;
    ACPIMMIO32 (ACPI_MMIO_BASE + SMI_BASE + FCH_SMI_REG84)  = ApuRasSmi;

    IDS_HDT_CONSOLE (
      FCH_TRACE,
      "[%a] Check again IsFchSmmApuRasEnabled = 0x%x\n",
      __FUNCTION__,
      IsFchSmmApuRasEnabled ()
      );
  }
}


VOID
FchSmmApuRasSmiDisable (
  IN       FCH_SMM_APURAS_REGISTER_CONTEXT          *ApuRasRegisterContext
  )
{
  ASSERT (ApuRasRegisterContext);
  ASSERT (ApuRasRegisterContext->Socket < 2);
  ASSERT (ApuRasRegisterContext->NbioNumber < 8);

  if ( ApuRasRegisterContext->Socket >= gSmmNumberOfSocketEnabled ) {
    IDS_HDT_CONSOLE (
      FCH_TRACE,
      "[%a] ERROR: Total %d socket(s), the value of socket argument %d is invalid.\n",
      __FUNCTION__,
      gSmmNumberOfSocketEnabled,
      ApuRasRegisterContext->Socket
      );
    return;
  }

  //
  // Disable SMI on NBIO/IOHC side if it is enabled
  //
  if (RasSystemInfo.SocketInfo[ApuRasRegisterContext->Socket].IohcRasSwiEn & (1 << ApuRasRegisterContext->NbioNumber)) {

    // Before disable NBIO/IOHC SMI, we also need check if USB SMI is installed.
    // USB is under NBIO[1:0]->IOHC[0]->SysHub and it use APU SMI to trigger SMI.
    // We should not disable IOHC SMI if USB SMI is enabled.
    if (HeadFchSmmUsbNodePtr->FchUsbNodePtr != NULL
      && IsXhciInside (FchIochRasRegisters[ApuRasRegisterContext->NbioNumber].Flag))
    {
      IDS_HDT_CONSOLE (
        FCH_TRACE,
        "[%a] USB SMI is installed, do not disable socket %d iohc %d RAS SMI",
        __FUNCTION__,
        ApuRasRegisterContext->Socket,
        ApuRasRegisterContext->NbioNumber
        );
    } else {
      IDS_HDT_CONSOLE (
        FCH_TRACE,
        "[%a] Disable socket %d iohc %d RAS SMI\n",
        __FUNCTION__,
        ApuRasRegisterContext->Socket,
        ApuRasRegisterContext->NbioNumber
        );

      // Clear IOHC::MISC_RAS_CONTROL
      FchSmnRW (
        RasSystemInfo.SocketInfo[ApuRasRegisterContext->Socket].Bus,
        FchIochRasRegisters[ApuRasRegisterContext->NbioNumber].MISC_RAS_CONTROL,
        0xffffbfff,
        0,
        NULL
        );

      // Clear IOHC::RAS_GLOBAL_STATUS_LO:HPLGWA_SMI
      FchSmnRW (
        RasSystemInfo.SocketInfo[ApuRasRegisterContext->Socket].Bus,
        FchIochRasRegisters[ApuRasRegisterContext->NbioNumber].RAS_GLOBAL_STATUS_LO,
        0xfffffeff,
        BIT8,
        NULL
        );

      // Clear IOHC::IOHC_INTERRUPT_EOI:SMI_EOI
      FchSmnRW (
        RasSystemInfo.SocketInfo[ApuRasRegisterContext->Socket].Bus,
        FchIochRasRegisters[ApuRasRegisterContext->NbioNumber].IOHC_INTERRUPT_EOI,
        0xfffffffe,
        BIT0,
        NULL
        );

      // Clear IOHC bitmap
      RasSystemInfo.SocketInfo[ApuRasRegisterContext->Socket].IohcRasSwiEn &= ~(1 << ApuRasRegisterContext->NbioNumber);
    }
  } else {
    IDS_HDT_CONSOLE (
      FCH_TRACE,
      "[%a] RAS SMI of socket %d iohc %d is not enabled, just quit\n",
      __FUNCTION__,
      ApuRasRegisterContext->Socket,
      ApuRasRegisterContext->NbioNumber
      );
  }

  IDS_HDT_CONSOLE (
    FCH_TRACE,
    "[%a] IsFchSmmApuRasEnabled = 0x%x, SocketNbioRasSmiEn[0] = 0x%x, SocketNbioRasSmiEn[1] = 0x%x\n",
    __FUNCTION__,
    IsFchSmmApuRasEnabled (),
    RasSystemInfo.SocketInfo[0].IohcRasSwiEn,
    RasSystemInfo.SocketInfo[1].IohcRasSwiEn
    );

  //
  // Disable APU HW SMI control in FCH if no IOHC APU RAS SMI enabled
  //
  if (!IsAnyIohcRasSmiEnabled () && IsFchSmmApuRasEnabled ()) {
    IDS_HDT_CONSOLE (
      FCH_TRACE,
      "[%a] No IOHC RAS SMI is enabled and FCH APU SMI is enabled, disable FCH APU SMI\n",
      __FUNCTION__
      );
    ACPIMMIO32 (ACPI_MMIO_BASE + SMI_BASE + FCH_SMI_REG84)  = ApuRasSmi;
    ACPIMMIO32 (ACPI_MMIO_BASE + SMI_BASE + FCH_SMI_REGAC) &= ~(UINT32)(3 << 10);
    ACPIMMIO32 (ACPI_MMIO_BASE + SMI_BASE + FCH_SMI_REG84)  = ApuRasSmi;

    IDS_HDT_CONSOLE (
      FCH_TRACE,
      "[%a] Check again IsFchSmmApuRasEnabled = 0x%x\n",
      __FUNCTION__,
      IsFchSmmApuRasEnabled ()
      );
  }
}



