#;*****************************************************************************
#;
#; Copyright (C) 2008-2024 Advanced Micro Devices, Inc. All rights reserved.
#;
#;******************************************************************************

[Defines]
  INF_VERSION                    = 0x00010005
  BASE_NAME                      = AmdCcxZen5Dxe
  FILE_GUID                      = 9FB8AC55-D266-4F86-AC43-BF2BA74F5A7A
  MODULE_TYPE                    = DXE_DRIVER
  VERSION_STRING                 = 1.0
  ENTRY_POINT                    = AmdCcxZen5DxeInit

[Packages]
  MdePkg/MdePkg.dec
  AgesaModulePkg/AgesaCommonModulePkg.dec
  AgesaModulePkg/AgesaModuleCcxPkg.dec
  AgesaModulePkg/AgesaModuleDfPkg.dec
  AgesaModulePkg/AgesaModuleNbioPkg.dec
  AgesaModulePkg/AgesaModuleRasPkg.dec
  AgesaModulePkg/AgesaFamily1AModulePkg.dec
  AgesaModulePkg/AgesaModulePspPkg.dec
  AgesaPkg/AgesaPkg.dec

[LibraryClasses.common.DXE_DRIVER]
  BaseLib
  UefiLib

[LibraryClasses]
  BaseLib
  BaseMemoryLib
  PcdLib
  UefiDriverEntryPoint
  CcxBaseX86Lib
  CcxRolesLib
  CcxResetTablesLib
  CcxSetMcaLib
  AmdBaseLib
  AmdHeapLib
  AmdPspBaseLibV2
  AmdIdsHookLib
  CcxZen5IdsHookLibDxe
  CcxPstatesLib
  CcxSmbiosLib
  AmdPspApobLib
  CcxHaltLib
  FabricRegisterAccLib
  AmdCapsuleLib
  FchBaseLib
  FabricWdtLib
  CcxSmmAccess2Lib
  DxeCcxBaseX86ServicesLib
  CcxApicZen5Lib
  CcxCppcLib
  CcxCcdReorderLib
  CcxZen5DxeLib
  ApobCommonServiceLib
  CcxZen5SegRmpDxeLib
  AmlGenerationLib
  AcpiTableHelperLib

[Sources]
  AmdCcxZen5Dxe.c
  AmdCcxZen5Dxe.h
  CcxZen5AcpiRas.c
  CcxZen5AcpiServicesDxe.c
  CcxZen5AcpiServicesDxe.h
  CcxZen5SmbiosDxe.c
  CcxZen5SmbiosDxe.h

[Guids]

[Protocols]
  gAmdCoreTopologyServicesV3ProtocolGuid  #CONSUMED
  gAmdFabricTopologyServices2ProtocolGuid #CONSUMED
  gAmdCcxDxeInitCompleteProtocolGuid      #PRODUCED
  gEfiMpServiceProtocolGuid               #CONSUMED
  gAmdCcxSmbiosServicesProtocolGuid       #PRODUCED
  gAmdAcpiCpuSsdtServicesProtocolGuid     #PRODUCED
  gAmdCcxAcpiCratServicesProtocolGuid     #PRODUCED
  gAmdCcxAcpiSratServicesProtocolGuid     #PRODUCED
  gAmdMpServicesPreReqProtocolGuid        #PRODUCED
  gAmdSocLogicalIdProtocolGuid            #CONSUMED
  gAmdFabricNumaServices2ProtocolGuid     #CONSUMED
  gAmdCcxAcpiRasServicesProtocolGuid      #PRODUCED
  gAmdNbioSmuInitCompleteProtocolGuid     #CONSUMED
  gAmdCcxOcCompleteProtocolGuid           #PRODUCED
  gAmdCcxBaseServicesProtocolGuid         #PRODUCED
  gAmdApcbDxeServiceProtocolGuid          #CONSUMED
  gAmdCcxAcpiPcctServicesProtocolGuid     #PRODUCED
  gAmdCcxAcpiCppcServicesProtocolGuid     #PRODUCED
  gAmdFabricResourceManagerServicesProtocolGuid #CONSUMED
  gAmdNbioCppcServicesProtocolGuid        #CONSUMED
  gAmdNbioCoreRankingTableServicesProtocolGuid #CONSUMED
  gAmdSocZen5ServicesProtocolGuid         #CONSUMED
  gEfiSmmControl2ProtocolGuid             #CONSUMED

[Pcd]
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdCStateMode
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdAgesaPstatePolicy
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdCStateIoBaseAddress
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdCcxCfgPFEHEnable
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdSnpMemCover
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdSnpMemSize
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdAmdCcxS3SaveSmi
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdApicMode
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdAcpiCpuCstC1Latency
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdAcpiCpuCstC2Latency
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdAcpiCpuCstC3Latency
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdAcpiCpuCstC4Latency
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdSplitRmpTable
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdAmdVmplEnable
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdAmdSvmLock
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdAmdSvmEnable
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdAmdStartupAllAPsSingleThread
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdAmdCoreRankingTableFeedbackInterruptFlags
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdAmdCoreRankingTableFeedbackInterrupt
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdCpuReqMinFreqEn
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdCpuReqMinFreq

[Depex]
  gAmdCcxZen5DepexProtocolGuid AND
  gAmdCoreTopologyServicesV3ProtocolGuid AND
  gAmdNbioSmuServicesProtocolGuid AND
  gAmdFabricTopologyServices2ProtocolGuid AND
  gAmdApcbDxeServiceProtocolGuid

