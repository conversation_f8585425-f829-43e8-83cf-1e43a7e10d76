#;*****************************************************************************
#;
#; Copyright (C) 2008-2024 Advanced Micro Devices, Inc. All rights reserved.
#;
#;******************************************************************************

[Defines]
  INF_VERSION                    = 0x00010005
  BASE_NAME                      = AmdFabricBrhPei
  FILE_GUID                      = 4489DD91-8FBA-4F0E-B218-A24497805E1B
  MODULE_TYPE                    = PEIM
  VERSION_STRING                 = 1.0
  ENTRY_POINT                    = AmdFabricBrhPeiInit

#
#  VALID_ARCHITECTURES           = IA32
#

[Packages]
  MdePkg/MdePkg.dec
  AgesaModulePkg/AgesaCommonModulePkg.dec
  AgesaModulePkg/AgesaModuleDfPkg.dec
  AgesaModulePkg/AgesaFamily1AModulePkg.dec
  AgesaPkg/AgesaPkg.dec

[LibraryClasses]
  BaseLib
  BaseMemoryLib
  PcdLib
  PeimEntryPoint
  AmdBaseLib
  IdsLib
  BaseFabricTopologyLib
  PeiFabricTopologyServices2Lib
  FabricRegisterAccLib
  AmdIdsHookLib
  FabricIdsHookBrhLibPei
  ApobCommonServiceLib
  PeiFabricResourceManagerServicesLib

[sources]
  AmdFabricBrhPei.c
  FabricPieRasInit.c
  FabricPieRasInit.h

[Guids]

[Protocols]

[Ppis]
  gAmdFabricTopologyServices2PpiGuid   #PRODUCED
  gAmdFabricPstateServicesPpiGuid      #PRODUCED
  gAmdSocLogicalIdPpiGuid              #CONSUMED
  gAmdFabricSocSpecificServicesPpiGuid #CONSUMED

[Pcd]
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdFabricWdtCfg
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdFabricWdtCntSel
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdAmdFabricNumOfScrubber
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdAmdFabricNumSeqScrubsCstateExit
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdFabricImmSyncFloodOnFatalErrCtrl
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdPerformanceTracing

[Depex]
  gAmdFabricBrhDepexPpiGuid

