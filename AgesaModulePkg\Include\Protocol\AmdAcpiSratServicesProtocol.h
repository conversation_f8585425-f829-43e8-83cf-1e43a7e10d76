/*****************************************************************************
 * Copyright (C) 2008-2024 Advanced Micro Devices, Inc. All rights reserved.
 *
*****************************************************************************
*/
/* $NoKeywords:$ */
/**
 * @file
 *
 * AMD SRAT Services Protocol prototype definition
 *
 *
 * @xrefitem bom "File Content Label" "Release Content"
 * @e project:      AGESA
 * @e sub-project:  Library
 * @e \$Revision: 313706 $   @e \$Date: 2015-02-25 21:00:43 -0600 (Wed, 25 Feb 2015) $
 */
#ifndef _AMD_ACPI_SRAT_SERVICES_PROTOCOL_H_
#define _AMD_ACPI_SRAT_SERVICES_PROTOCOL_H_

#pragma pack (push, 1)

#include "AmdAcpiSratServicesProtocolCommon.h"

/*----------------------------------------------------------------------------------------
 *                    T Y P E D E F S     A N D     S T R U C T U R E S
 *                            System Resource Affinity Table
 *----------------------------------------------------------------------------------------
 */

/*----------------------------------------------------------------------------------------
 *                 D E F I N I T I O N S     A N D     M A C R O S
 *----------------------------------------------------------------------------------------
 */

/*----------------------------------------------------------------------------------------
 *                M E M O R Y
 *----------------------------------------------------------------------------------------
 */

/// Forward declaration for the AMD_FABRIC_ACPI_SRAT_SERVICES_PROTOCOL.
typedef struct _AMD_FABRIC_ACPI_SRAT_SERVICES_PROTOCOL AMD_FABRIC_ACPI_SRAT_SERVICES_PROTOCOL;

/**
 * @brief Adds the system's SRAT memory entries to the SRAT table.
 *
 * @param[in]      This                Pointer to the Fabric ACPI SRAT services protocol instance. @see AMD_FABRIC_ACPI_SRAT_SERVICES_PROTOCOL
 * @param[in]      SratHeaderStructPtr Pointer to the SRAT table header structure. @see SRAT_HEADER
 * @param[in, out] TableEnd            Current SRAT table pointer to store the memory structures to.
 *
 * @return EFI_STATUS - status of the operation.
 */
typedef
EFI_STATUS
(EFIAPI *AMD_SRAT_SERVICES_CREATE_MEMORY) (
  IN     AMD_FABRIC_ACPI_SRAT_SERVICES_PROTOCOL *This,
  IN     SRAT_HEADER                            *SratHeaderStructPtr,
  IN OUT UINT8                                 **TableEnd
  );

/**
 * @brief Retrieves information about the domains declared via the SRAT.
 *
 * @param[in]  This            Pointer to the Fabric ACPI SRAT services protocol instance. @see AMD_FABRIC_ACPI_SRAT_SERVICES_PROTOCOL
 * @param[out] NumberOfDomains Number of memory entries in the SRAT.
 * @param[out] MemoryInfo      Information about the SRAT memory entries. @see MEMORY_INFO
 *
 * @return EFI_STATUS - status of the operation.
 */
typedef
EFI_STATUS
(EFIAPI *AMD_SRAT_SERVICES_GET_MEMORY_INFO) (
  IN     AMD_FABRIC_ACPI_SRAT_SERVICES_PROTOCOL *This,
     OUT UINT32                                 *NumberOfDomains,
     OUT MEMORY_INFO                           **MemoryInfo
  );

/// When installed, the SRAT Services Protocol produces a collection of services that return various information to generate SRAT.
struct _AMD_FABRIC_ACPI_SRAT_SERVICES_PROTOCOL {
  UINTN                             Revision;      ///< Revision Number.
  AMD_SRAT_SERVICES_CREATE_MEMORY   CreateMemory;  ///< @see AMD_SRAT_SERVICES_CREATE_MEMORY
  AMD_SRAT_SERVICES_GET_MEMORY_INFO GetMemoryInfo; ///< @see AMD_SRAT_SERVICES_GET_MEMORY_INFO
};

/// GUID for CCX ACPI SRAT services protocol.
extern EFI_GUID gAmdCcxAcpiSratServicesProtocolGuid;

/// GUID for Fabric ACPI SRAT services protocol.
extern EFI_GUID gAmdFabricAcpiSratServicesProtocolGuid;

#pragma pack (pop)

#endif // _AMD_ACPI_SRAT_SERVICES_PROTOCOL_H_

