#;*****************************************************************************
#;
#; Copyright (C) 2008-2024 Advanced Micro Devices, Inc. All rights reserved.
#;
#;******************************************************************************

[Defines]
  INF_VERSION                    = 0x00010006
  BASE_NAME                      = AmdEmulationFlagPeiLib
  FILE_GUID                      = 34CD4F29-590F-4C9D-849D-8FE7999360F5
  MODULE_TYPE                    = PEIM
  VERSION_STRING                 = 1.0
  LIBRARY_CLASS                  = AmdEmulationFlagLib|PEIM

[Sources.common]
  AmdEmulationFlagPeiLib.c

[Packages]
  MdePkg/MdePkg.dec
  AgesaModulePkg/AgesaCommonModulePkg.dec
  AgesaPkg/AgesaPkg.dec

[LibraryClasses]
  BaseLib
  AmdBaseLib
  HobLib
  PresiliconControlLib

[Guids]
  gAmdEmulationFlagHobGuid

[Protocols]

[Ppis]

[Pcd]

[Depex]





