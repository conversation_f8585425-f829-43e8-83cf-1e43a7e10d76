/*****************************************************************************
 * Copyright (C) 2008-2024 Advanced Micro Devices, Inc. All rights reserved.
 *
*****************************************************************************
*/
/* $NoKeywords:$ */
/**
 * @file
 *
 * Config Fch LPC controller
 *
 * Init LPC Controller features.
 *
 * @xrefitem bom "File Content Label" "Release Content"
 * @e project:     AGESA
 * @e sub-project: FCH
 * @e \$Revision: 309083 $   @e \$Date: 2014-12-09 09:28:24 -0800 (Tue, 09 Dec 2014) $
 *
 */
#include "FchPlatform.h"
#include "Filecode.h"

#define FILECODE FCH_KUNLUN_FCHKUNLUNCORE_KUNLUN_KLLPCSPI_KLLPCRESETSERVICE_FILECODE
#define SPI_BASE 0xFEC10000ul

/**
 * FchInitKunlunResetLpcPciTable - Lpc (Spi) device registers
 * initial during the power on stage.
 *
 *
 *
 *
 */
REG8_MASK FchInitKunlunResetLpcPciTable[] =
{
  //
  // LPC Device (Bus 0, Dev 20, Func 3)
  //
  {0x00, LPC_BUS_DEV_FUN, 0},

  {FCH_LPC_REG48, BIT2, BIT0 + BIT1},                              //PLAT-33566

  // Force EC_PortActive to 1 to fix possible IR non function issue when NO_EC_SUPPORT is defined
  {FCH_LPC_REGA4, 0xFE, BIT0},
  {0xFF, 0xFF, 0xFF},
};

/**
 * FchInitResetLpcProgram - Config Lpc controller during Power-On
 *
 *
 *
 * @param[in] FchDataPtr Fch configuration structure pointer.
 *
 */
VOID
FchInitResetLpcProgram (
  IN       VOID     *FchDataPtr
  )
{
  FCH_RESET_DATA_BLOCK      *LocalCfgPtr;
  AMD_CONFIG_PARAMS         *StdHeader;

  LocalCfgPtr = (FCH_RESET_DATA_BLOCK *) FchDataPtr;
  StdHeader = LocalCfgPtr->StdHeader;

  RwPci ((LPC_BUS_DEV_FUN << 16) + FCH_LPC_REG6C, AccessWidth32, 0xFFFFFF00, 0, StdHeader);

  if ( LocalCfgPtr->Spi.LpcEnable ) {
    ProgramPciByteTable ( (REG8_MASK*) (&FchInitKunlunResetLpcPciTable[0]), sizeof (FchInitKunlunResetLpcPciTable) / sizeof (REG8_MASK), StdHeader);

    if ( LocalCfgPtr->Spi.LpcClk0 ) {
      RwPci ((LPC_BUS_DEV_FUN << 16) + FCH_LPC_REGD0 + 1, AccessWidth8, 0xDF, 0x20, StdHeader);
    } else {
      RwPci ((LPC_BUS_DEV_FUN << 16) + FCH_LPC_REGD0 + 1, AccessWidth8, 0xDF, 0, StdHeader);
    }
    if ( LocalCfgPtr->Spi.LpcClk1 ) {
      RwPci ((LPC_BUS_DEV_FUN << 16) + FCH_LPC_REGD0 + 1, AccessWidth8, 0xBF, 0x40, StdHeader);
    } else {
      RwPci ((LPC_BUS_DEV_FUN << 16) + FCH_LPC_REGD0 + 1, AccessWidth8, 0xBF, 0, StdHeader);
    }
    if ( LocalCfgPtr->LegacyFree ) {
      RwPci (((LPC_BUS_DEV_FUN << 16) + FCH_LPC_REG44), AccessWidth32, 00, 0x0003C000, StdHeader);
    } else {
      RwPci (((LPC_BUS_DEV_FUN << 16) + FCH_LPC_REG44), AccessWidth32, 00, 0xFF03FFD5, StdHeader);
    }
  } else {
    // Disable LPC decode when LPC disabled
    RwPci ((LPC_BUS_DEV_FUN << 16) + FCH_LPC_REG44, AccessWidth32, 00, 0, StdHeader);
    RwPci ((LPC_BUS_DEV_FUN << 16) + FCH_LPC_REG48, AccessWidth32, 0xFF00, 0, StdHeader);
    // Disable LPC clock when LPC disabled
    //RwPci ((LPC_BUS_DEV_FUN << 16) + FCH_LPC_REGD0 + 1, AccessWidth8, 0x9F, 0, StdHeader);
  }
}

/**
 * FchInitResetSpi - Config Spi controller during Power-On
 *
 *
 *
 * @param[in] FchDataPtr Fch configuration structure pointer.
 *
 */
VOID
FchInitResetSpi (
  IN       VOID     *FchDataPtr
  )
{
  FCH_RESET_DATA_BLOCK      *LocalCfgPtr;
  AMD_CONFIG_PARAMS         *StdHeader;

  LocalCfgPtr = (FCH_RESET_DATA_BLOCK *) FchDataPtr;
  StdHeader = LocalCfgPtr->StdHeader;

  AGESA_TESTPOINT (TpFchInitResetSpi, NULL);
  //
  // Set Spi ROM Base Address
  //
  RwPci ((LPC_BUS_DEV_FUN << 16) + FCH_LPC_REGA0, AccessWidth32, 0x001F, SPI_BASE, StdHeader);

  RwMem (SPI_BASE + FCH_SPI_MMIO_REG00, AccessWidth32, 0xFFFFFFFF, (BIT19 + BIT24 + BIT25 + BIT26));
  RwMem (SPI_BASE + FCH_SPI_MMIO_REG0C, AccessWidth32, 0xFFC0FFFF, 0 );

  //Set SPI100 Enable
  RwMem (SPI_BASE + FCH_SPI_MMIO_REG20, AccessWidth8, 0xFE, (UINT8) ((LocalCfgPtr->SPI100_Enable) << 0));

  //
  //  Spi Pad Initial
  //UINT32                SPI100_RX_Timing_Config_Register_38;                 ///< SPI100_RX_Timing_Config_Register_38
  //UINT16                SPI100_RX_Timing_Config_Register_3C;                 ///< SPI100_RX_Timing_Config_Register_3C
  //UINT8                 SpiProtectEn0_1d_34;                                 ///

  //RwMem (SPI_BASE + FCH_SPI_MMIO_REG38, AccessWidth32, 0, LocalCfgPtr->SPI100_RX_Timing_Config_Register_38);
  //RwMem (SPI_BASE + FCH_SPI_MMIO_REG3C, AccessWidth16, 0, LocalCfgPtr->SPI100_RX_Timing_Config_Register_3C);
  //RwMem (SPI_BASE + FCH_SPI_MMIO_REG1D, AccessWidth8, 0xE7, (UINT8) ((LocalCfgPtr->SpiProtectEn0_1d_34) << 3));

  //
  //  Spi Mode Initial
  //
  if (LocalCfgPtr->SpiSpeed) {
    RwMem (SPI_BASE + FCH_SPI_MMIO_REG22, AccessWidth16, ~((UINT32) (0xF << 12)), ((LocalCfgPtr->SpiSpeed - 1 ) << 12));
  }

  if (LocalCfgPtr->WriteSpeed) {
    RwMem (SPI_BASE + FCH_SPI_MMIO_REG20, AccessWidth32, ~((UINT32) (0xF << 20)), ((LocalCfgPtr->WriteSpeed - 1 ) << 20));
  }

  if (LocalCfgPtr->FastSpeed) {
    RwMem (SPI_BASE + FCH_SPI_MMIO_REG20, AccessWidth32, ~((UINT32) (0xF << 24)), ((LocalCfgPtr->FastSpeed - 1 ) << 24));
  }

  if (LocalCfgPtr->SpiTpmSpeed) {
    RwMem (SPI_BASE + FCH_SPI_MMIO_REG20, AccessWidth32, ~((UINT32) (0xF << 16)), ((LocalCfgPtr->SpiTpmSpeed - 1) << 16));
  }

  //Enable drv_spiclk_earlier to fix TPM CS error issue
  RwMem (SPI_BASE + FCH_SPI_MMIO_REG00, AccessWidth32, ~(UINT32)(BIT13), BIT13);

  RwMem (SPI_BASE + FCH_SPI_MMIO_REG1C, AccessWidth32, ~(UINT32) (BIT10), ((LocalCfgPtr->BurstWrite) << 10));

  RwMem (SPI_BASE + FCH_SPI_MMIO_REG2C, AccessWidth32, ~(UINT32) (BIT14), (1 << 14)); //ENH433556:Enabling SPI Performance enhancement

  RwMem (SPI_BASE + FCH_SPI_MMIO_REG2C, AccessWidth32, ~(UINT32) (BIT15), 0); //ENH457313:  SPI Mem 0x2C[15] needs to be cleared in Carrizo

#if 1 // PLAT-121599
  // Set FCH::LPCPCICFG::PCI_MISCCNTRL::en_tpm_flush_rbuf to 1
  RwPci ((LPC_BUS_DEV_FUN << 16) + FCH_LPC_REGDC, AccessWidth32, ~(UINT32)BIT0, BIT0, StdHeader);
#else
  // Enabling SPI ROM Prefetch
  // Set LPC cfg 0xBA bit 8
  RwPci ((LPC_BUS_DEV_FUN << 16) + FCH_LPC_REGBA, AccessWidth16, 0xFFFF, BIT8, StdHeader);
#endif

  // Enable SPI Prefetch for USB, set LPC cfg 0xBA bit 7 to 1.
  RwPci ((LPC_BUS_DEV_FUN << 16) + FCH_LPC_REGBA, AccessWidth16, 0xFFFF, BIT7, StdHeader);
}


