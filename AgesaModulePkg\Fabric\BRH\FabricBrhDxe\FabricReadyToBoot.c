/*****************************************************************************
 *
 * Copyright (C) 2019-2024 Advanced Micro Devices, Inc. All rights reserved.
 *
 *****************************************************************************
 */

/*----------------------------------------------------------------------------------------
 *                             M O D U L E S    U S E D
 *----------------------------------------------------------------------------------------
 */
#include <AMD.h>
#include <Filecode.h>
#include <Library/IdsLib.h>
#include <Library/FabricRegisterAccLib.h>
#include <Library/BaseFabricTopologyLib.h>
#include <FabricRegistersBrh.h>
#include "FabricReadyToBoot.h"

/*----------------------------------------------------------------------------------------
 *                   D E F I N I T I O N S    A N D    M A C R O S
 *----------------------------------------------------------------------------------------
 */
#define FILECODE FABRIC_BRH_FABRICBRHDXE_FABRICREADYTOBOOT_FILECODE

/*----------------------------------------------------------------------------------------
 *           P R O T O T Y P E S     O F     L O C A L     F U N C T I O N S
 *----------------------------------------------------------------------------------------
 */

/*----------------------------------------------------------------------------------------
 *                           G L O B A L   V A R I A B L E S
 *----------------------------------------------------------------------------------------
 */

/*----------------------------------------------------------------------------------------
 *                  T Y P E D E F S     A N D     S T R U C T U R E S
 *----------------------------------------------------------------------------------------
 */

/*----------------------------------------------------------------------------------------
 *                          E X P O R T E D    F U N C T I O N S
 *----------------------------------------------------------------------------------------
 */

/*----------------------------------------------------------------------------------------*/
/**
 * @brief This function disables PCI extended configuration register access.
 *
 * @param[in] Event
 * @param[in] Context
 */
VOID
EFIAPI
FabricReadyToBoot (
  IN EFI_EVENT  Event,
  IN VOID       *Context
  )
{
  CORE_MASTER_ACCESS_CTRL_REGISTER CoreMasterAccCtrl;

  AGESA_TESTPOINT (TpDfReadyToBootEntry, NULL);

  IDS_HDT_CONSOLE (CPU_TRACE, "FabricReadyToBoot: Disable extended configuration access\n");

  for (UINTN i = 0; i < FabricTopologyGetNumberOfProcessorsPresent (); i++) {
    for (UINTN j = 0; j < FabricTopologyGetNumberOfDiesOnSocket (i); j++) {
      CoreMasterAccCtrl.Value = FabricRegisterAccRead (i,
                                                       j,
                                                       COREMASTERACCESSCTRL_FUNC,
                                                       COREMASTERACCESSCTRL_REG,
                                                       FABRIC_REG_ACC_BC);

      CoreMasterAccCtrl.Field.EnableCf8ExtCfg = 0;

      FabricRegisterAccWrite (i,
                              j,
                              COREMASTERACCESSCTRL_FUNC,
                              COREMASTERACCESSCTRL_REG,
                              FABRIC_REG_ACC_BC,
                              CoreMasterAccCtrl.Value,
                              TRUE);
    }
  }

  AGESA_TESTPOINT (TpDfReadyToBootExit, NULL);
}

