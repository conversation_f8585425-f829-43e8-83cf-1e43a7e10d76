/*****************************************************************************
 * Copyright (C) 2008-2024 Advanced Micro Devices, Inc. All rights reserved.
 *
*****************************************************************************
*/
/* $NoKeywords:$ */
/**
 * @file
 *
 * FCH PEIM
 *
 *
 * @xrefitem bom "File Content Label" "Release Content"
 * @e project:      AGESA
 * @e sub-project   FCH PEIM
 * @e \$Revision: 309090 $   @e \$Date: 2014-12-09 10:28:05 -0800 (Tue, 09 Dec 2014) $
 *
 */


#include "FchReset.h"
#define FILECODE FCH_KUNLUN_FCHKUNLUNPEI_FCHRESET_FILECODE

// Consumed PPIs


/**
 * @brief Reset the system using the chipset cf9 port
 *
 * @param[in] PeiServices Pointer to the PEI service table pointer
 *
 * @returns EFI_STATUS
 * @retval EFI_SUCCESS   Procedure successfully
 * @retval EFI_ERROR     Procedure failed (see error for more details)
 */
EFI_STATUS
FchPeiReset (
  IN    CONST   EFI_PEI_SERVICES  **PeiServices
  )
{
  EFI_PEI_CPU_IO_PPI           *CpuIo;

  // Issue the reset
  CpuIo = (*PeiServices)->CpuIo;
  CpuIo->IoWrite8 (
            PeiServices,
            CpuIo,
            FCH_RESET_PORT,
            (COLD_RESET + RESET_CPU)
            );

  // Reset unsuccessful
  CpuDeadLoop ();
  return (EFI_DEVICE_ERROR);
}



