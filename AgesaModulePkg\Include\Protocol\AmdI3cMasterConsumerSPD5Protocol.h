/*****************************************************************************
 *
 * Copyright (C) 2015-2024 Advanced Micro Devices, Inc. All rights reserved.
 *
 *******************************************************************************
 */

#ifndef _AMD_I3C_MASTER_CONSUMER_SPD5_PROTOCOL_H_
#define _AMD_I3C_MASTER_CONSUMER_SPD5_PROTOCOL_H_

typedef struct _EFI_DXE_SPDI3C_PROTOCOL EFI_DXE_SPDI3C_PROTOCOL;

typedef
EFI_STATUS
(EFIAPI *SPDI3C_SET_PROTOCOL) (
  IN CONST EFI_DXE_SPDI3C_PROTOCOL  *This,
  IN       UINTN             BusSelect,
  IN       UINTN             SlaveAddress,
  IN       UINT16            Command,
  IN       UINT8             Data
  );

typedef
EFI_STATUS
(EFIAPI *SPDI3C_GET_PROTOCOL) (
  IN CONST EFI_DXE_SPDI3C_PROTOCOL  *This,
  IN       UINTN             BusSelect,
  IN       UINTN             SlaveAddress,
  IN       UINT16            Command,
  OUT      UINT8             *Data,
  IN       UINT8             DataLength
  );

///
/// This PROTOCOL provide interface to set SPD5 via I3C.
///
struct _EFI_DXE_SPDI3C_PROTOCOL {
  UINTN    Revision;                  ///< Revision Number
  SPDI3C_SET_PROTOCOL Set;                ///< Write Register
  SPDI3C_GET_PROTOCOL Get;                ///< Read Register
};

// Current PROTOCOL revision
#define SPDI3C_PROTOCOL_REVISION   (0x02)

extern EFI_GUID gSpd5I3cProtoclGuid;


#endif // _SPD_I3C_DXE_H_



