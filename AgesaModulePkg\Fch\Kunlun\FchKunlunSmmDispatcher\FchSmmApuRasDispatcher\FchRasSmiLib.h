/******************************************************************************
*
 * Copyright (C) 2008-2024 Advanced Micro Devices, Inc. All rights reserved.
 *
*******************************************************************************
**/
/* $NoKeywords:$ */
/**
 * @file
 *
 * AMD Fch APU RAS SMI Dispatcher Driver
 *
 *
 * @xrefitem bom "File Content Label" "Release Content"
 * @e project:      AGESA
 * @e sub-project:  UEFI
 * @e \$Revision: 309090 $   @e \$Date: 2014-12-09 10:28:05 -0800 (Tue, 09 Dec 2014) $
 *
 */
#ifndef _FCH_SMM_APU_RAS_LIB_H_
#define _FCH_SMM_APU_RAS_LIB_H_

#include <Protocol/FchSmmApuRasDispatch.h>
#include <Protocol/FabricTopologyServices2.h>

VOID
FchSmmApuRasClearSmiSts (
  VOID
);

VOID
FchSmmApuRasSmiEnable (
  IN       FCH_SMM_APURAS_REGISTER_CONTEXT          *ApuRasRegisterContext
);

VOID
FchSmmApuRasSmiDisable (
  IN       FCH_SMM_APURAS_REGISTER_CONTEXT          *ApuRasRegisterContext
);

#endif



