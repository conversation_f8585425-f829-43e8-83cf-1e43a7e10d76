/*
 ******************************************************************************
 *
 * Copyright (C) 2008-2024 Advanced Micro Devices, Inc. All rights reserved.
 *
 *******************************************************************************
 */


/*----------------------------------------------------------------------------------------
 *                             M O D U L E S    U S E D
 *----------------------------------------------------------------------------------------
 */
#include <AGESA.h>
#include <cpuRegisters.h>
#include <SocLogicalId.h>
#include "BaseSocLogicalIdXlatZen5DieLib.h"
#include <Filecode.h>


/*----------------------------------------------------------------------------------------
 *                   D E F I N I T I O N S    A N D    M A C R O S
 *----------------------------------------------------------------------------------------
 */
#define FILECODE  LIBRARY_BASESOCLOGICALIDXLATZEN5DIELIB_BASESOCLOGICALIDXLATZEN5DIELIB_FILECODE

/*----------------------------------------------------------------------------------------
 *                  T Y P E D E F S     A N D     S T R U C T U R E S
 *----------------------------------------------------------------------------------------
 */

/*----------------------------------------------------------------------------------------
 *           P R O T O T Y P E S     O F     L O C A L     F U N C T I O N S
 *----------------------------------------------------------------------------------------
 */
BOOLEAN
FindApplicableLogicalTableEntries (
  IN       UINT32                       RawId,
     OUT   SOC_LOGICAL_FAMILY_XLAT    **Family,
     OUT   SOC_LOGICAL_REVISION_XLAT  **Revision
  );

STATIC CONST SOC_LOGICAL_REVISION_XLAT ROMDATA SocBrhLogicalRevisionTable[] =
{
  // NOTE: the highest supported stepping should be the first one
  {0x8, 0x0, AMD_REV_F1A_SHP_C0, AMD_CORE_F1A_ZEN5_C0},
  {0x2, 0x1, AMD_REV_F1A_BRH_C1, AMD_CORE_F1A_ZEN5_C1},
  {0x2, 0x0, AMD_REV_F1A_BRH_C0, AMD_CORE_F1A_ZEN5_C0},
  {0x1, 0x1, AMD_REV_F1A_BRH_B1, AMD_CORE_F1A_ZEN5_B0},
  {0x1, 0x0, AMD_REV_F1A_BRH_B0, AMD_CORE_F1A_ZEN5_A0},
  {0x0, 0x0, AMD_REV_F1A_BRH_A0, AMD_CORE_F1A_ZEN5_A0},
  SOC_LOGICAL_REVISION_XLAT_TERMINATOR
};

STATIC CONST SOC_LOGICAL_REVISION_XLAT ROMDATA SocBrhdLogicalRevisionTable[] =
{
  // NOTE: the highest supported stepping should be the first one
  {0x1, 0x1, AMD_REV_F1A_BRHD_B1, AMD_CORE_F1A_ZEN5_B0},
  {0x1, 0x0, AMD_REV_F1A_BRHD_B0, AMD_CORE_F1A_ZEN5_B0},
  {0x0, 0x0, AMD_REV_F1A_BRHD_A0, AMD_CORE_F1A_ZEN5_A0},
  SOC_LOGICAL_REVISION_XLAT_TERMINATOR
};

STATIC CONST SOC_LOGICAL_REVISION_XLAT ROMDATA SocStx1LogicalRevisionTable[] =
{
  // NOTE: the highest supported stepping should be the first one
  {0x4, 0x0, AMD_REV_F1A_STX1_B0, AMD_CORE_F1A_ZEN5_B0},
  {0x0, 0x0, AMD_REV_F1A_STX1_A0, AMD_CORE_F1A_ZEN5_A0},
  SOC_LOGICAL_REVISION_XLAT_TERMINATOR
};

STATIC CONST SOC_LOGICAL_REVISION_XLAT ROMDATA SocStxhLogicalRevisionTable[] =
{
  // NOTE: the highest supported stepping should be the first one
  {0x0, 0x0, AMD_REV_F1A_STXH_A0, AMD_CORE_F1A_ZEN5_A0},
  SOC_LOGICAL_REVISION_XLAT_TERMINATOR
};

STATIC CONST SOC_LOGICAL_REVISION_XLAT ROMDATA SocKrkLogicalRevisionTable[] =
{
  // NOTE: the highest supported stepping should be the first one
  {0x8, 0x0, AMD_REV_F1A_KRK2_A0, AMD_CORE_F1A_ZEN5_A0},
  {0x0, 0x0, AMD_REV_F1A_KRK1_A0, AMD_CORE_F1A_ZEN5_A0},
  SOC_LOGICAL_REVISION_XLAT_TERMINATOR
};

STATIC CONST SOC_LOGICAL_REVISION_XLAT ROMDATA SocGnrLogicalRevisionTable[] =
{
  // NOTE: the highest supported stepping should be the first one
  {0x4, 0x0, AMD_REV_F1A_GNR_B0, AMD_CORE_F1A_ZEN5_B0},
  {0x0, 0x0, AMD_REV_F1A_GNR_A0, AMD_CORE_F1A_ZEN5_A0},
  SOC_LOGICAL_REVISION_XLAT_TERMINATOR
};

STATIC CONST SOC_LOGICAL_FAMILY_XLAT ROMDATA SocZen5LogicalFamilyTable[] =
{
  {0x1A, 0x0, AMD_FAMILY_1A_BRH,  AMD_CORE_FAMILY_1A_ZEN5, SocBrhLogicalRevisionTable},    // Family 1Ah, Models 00h - 0Fh
  {0x1A, 0x1, AMD_FAMILY_1A_BRHD, AMD_CORE_FAMILY_1A_ZEN5, SocBrhdLogicalRevisionTable},   // Family 1Ah, Models 10h - 1Fh
  {0x1A, 0x2, AMD_FAMILY_1A_STX,  AMD_CORE_FAMILY_1A_ZEN5, SocStx1LogicalRevisionTable},   // Family 1Ah, Models 20h - 2Fh
  {0x1A, 0x4, AMD_FAMILY_1A_GNR,  AMD_CORE_FAMILY_1A_ZEN5, SocGnrLogicalRevisionTable},    // Family 1Ah, Models 40h - 4Fh
  {0x1A, 0x6, AMD_FAMILY_1A_KRK,  AMD_CORE_FAMILY_1A_ZEN5, SocKrkLogicalRevisionTable},    // Family 1Ah, Models 60h - 6Fh
  {0x1A, 0x7, AMD_FAMILY_1A_STXH, AMD_CORE_FAMILY_1A_ZEN5, SocStxhLogicalRevisionTable},   // Family 1Ah, Models 70h - 7Fh
  SOC_LOGICAL_FAMILY_XLAT_TERMINATOR
};

/*----------------------------------------------------------------------------------------
 *           P R O T O T Y P E S     O F     L O C A L     F U N C T I O N S
 *----------------------------------------------------------------------------------------
 */

/*----------------------------------------------------------------------------------------
 *                          E X P O R T E D    F U N C T I O N S
 *----------------------------------------------------------------------------------------
 */

/*---------------------------------------------------------------------------------------*/
/**
 * Translates the given raw ID into the appropriate logical family / logical revision
 *
 * @param[in]     RawId       Raw CPU ID to convert
 * @param[out]    LogicalId   Logical family and logical revision for the given raw ID
 *
 * @retval        TRUE        Logical ID was successfully found
 * @retval        FALSE       Family is unknown
 */
BOOLEAN
BaseSocConvertRawToLogical (
  IN       UINT32          RawId,
     OUT   SOC_LOGICAL_ID  *LogicalId
  )
{
  SOC_LOGICAL_FAMILY_XLAT    *LogicalFamilyTable;
  SOC_LOGICAL_REVISION_XLAT  *LogicalRevisionTable;
  BOOLEAN                     LogicalIdValid;

  LogicalIdValid = FindApplicableLogicalTableEntries (RawId, &LogicalFamilyTable, &LogicalRevisionTable);
  if (LogicalIdValid) {
    LogicalId->Family = LogicalFamilyTable->LogicalFamily;
    LogicalId->Revision = LogicalRevisionTable->LogicalRevision;
  }

  return LogicalIdValid;
}

/*---------------------------------------------------------------------------------------*/
/**
 * Translates the given raw ID into the appropriate logical family / logical revision
 *
 * @param[in]     RawId       Raw CPU ID to convert
 * @param[out]    LogicalId   Logical family and logical revision for the given raw ID
 *
 * @retval        TRUE        Logical ID was successfully found
 * @retval        FALSE       Family is unknown
 */
BOOLEAN
BaseCoreConvertRawToLogical (
  IN       UINT32           RawId,
     OUT   CORE_LOGICAL_ID  *LogicalId
  )
{
  SOC_LOGICAL_FAMILY_XLAT    *LogicalFamilyTable;
  SOC_LOGICAL_REVISION_XLAT  *LogicalRevisionTable;
  BOOLEAN                     LogicalIdValid;

  LogicalIdValid = FindApplicableLogicalTableEntries (RawId, &LogicalFamilyTable, &LogicalRevisionTable);
  if (LogicalIdValid) {
    LogicalId->CoreFamily = LogicalFamilyTable->LogicalCoreFamily;
    LogicalId->CoreRevision = LogicalRevisionTable->LogicalCoreRev;
  }

  return LogicalIdValid;
}

/*----------------------------------------------------------------------------------------
 *                          L O C A L    F U N C T I O N S
 *----------------------------------------------------------------------------------------
 */

/*---------------------------------------------------------------------------------------*/
/**
 * Finds the appropriate logical ID information based on SoC family / model / stepping
 *
 * @param[in]     RawId       Raw family / model / stepping
 * @param[out]    Family      Pointer to valid family table entry if retval is TRUE
 * @param[out]    Revision    Pointer to valid revision table entry if retval is TRUE
 *
 * @retval        TRUE        Logical table entries were successfully found
 * @retval        FALSE       Family is unknown
 */
BOOLEAN
FindApplicableLogicalTableEntries (
  IN       UINT32                       RawId,
     OUT   SOC_LOGICAL_FAMILY_XLAT    **Family,
     OUT   SOC_LOGICAL_REVISION_XLAT  **Revision
  )
{
  SOC_LOGICAL_FAMILY_XLAT    *LogicalFamilyTable;
  SOC_LOGICAL_REVISION_XLAT  *LogicalRevisionTable;
  UINT16                      RawFamily;
  UINT8                       RawStepping;
  UINT8                       RawBaseModel;
  UINT8                       RawExtModel;
  BOOLEAN                     EntriesFound;

  EntriesFound = FALSE;

  // get Raw CPUID
  RawFamily    = (UINT16) (((RawId & CPUID_BASE_FAMILY_MASK) >> CPUID_BASE_FAMILY_OFFSET) +
                           ((RawId & CPUID_EXT_FAMILY_MASK)  >> CPUID_EXT_FAMILY_OFFSET));
  RawBaseModel = (UINT8)   ((RawId & CPUID_BASE_MODEL_MASK)  >> CPUID_BASE_MODEL_OFFSET);
  RawExtModel  = (UINT8)   ((RawId & CPUID_EXT_MODEL_MASK)   >> CPUID_EXT_MODEL_OFFSET);
  RawStepping  = (UINT8)    (RawId & CPUID_STEPPING_MASK);

  // get logical CPUID
  LogicalFamilyTable = (SOC_LOGICAL_FAMILY_XLAT *) SocZen5LogicalFamilyTable;
  while (LogicalFamilyTable->LogicalRevisionTable != NULL) {
    if ((RawFamily == LogicalFamilyTable->RawFamily) &&
        (RawExtModel == LogicalFamilyTable->RawExtModel)) {
      LogicalRevisionTable = (SOC_LOGICAL_REVISION_XLAT *) LogicalFamilyTable->LogicalRevisionTable;  // get logical revision table
      EntriesFound = TRUE;
      *Family = LogicalFamilyTable;
      *Revision = LogicalRevisionTable;     // initialize SOC_LOGICAL_ID.Revision with the first one (should be
                                            // the highest supported one)
      while (LogicalRevisionTable->LogicalRevision != AMD_REVISION_UNKNOWN) {
        if ((RawBaseModel == LogicalRevisionTable->RawBaseModel) &&
            (RawStepping == LogicalRevisionTable->RawStepping)) {
          *Revision = LogicalRevisionTable;
          break;
        }
        LogicalRevisionTable++;
      }
      break;
    }
    LogicalFamilyTable++;
  }

  return EntriesFound;
}

