/*****************************************************************************
 * Copyright (C) 2008-2024 Advanced Micro Devices, Inc. All rights reserved.
 *
*****************************************************************************
*/
/* $NoKeywords:$ */
/**
 * @file
 *
 * FCH SMM Driver
 *
 *
 * @xrefitem bom "File Content Label" "Release Content"
 * @e project:      AGESA
 * @e sub-project   FCH SMM Driver
 * @e \$Revision: 309090 $   @e \$Date: 2014-12-09 10:28:05 -0800 (Tue, 09 Dec 2014) $
 *
 */
#include "FchSmm.h"
#define FILECODE FCH_KUNLUN_FCHKUNLUNSMM_PERIODICTIMERSMI_FILECODE

extern FCH_DATA_BLOCK                      gFchDataInSmm;

UINT32 mShortTimerCounter;
UINT32 mLongTimerCounter;


/**
 * @brief Handler for LongTimer SMI.
 *
 * @param[in] DispatchHandle       The handle of this callback, obtained when registering
 * @param[in] RegisterContext      Pointer to the FCH_SMM_PERIODICAL_REGISTER_CONTEXT
 * @param[in] PeriodicTimerContext Pointer to the EFI_SMM_PERIODIC_TIMER_CONTEXT context
 * @param[in] SizeOfContext        Pointer to the size of context
 *
 * @returns EFI_STATUS
 * @retval EFI_SUCCESS   Module initialized successfully
 * @retval EFI_ERROR     Initialization failed (see error for more details)
 */
EFI_STATUS
EFIAPI
AmdSmiLongTimerCallback (
  IN       EFI_HANDLE                                DispatchHandle,
  IN       CONST FCH_SMM_PERIODICAL_REGISTER_CONTEXT *RegisterContext,
  IN OUT   EFI_SMM_PERIODIC_TIMER_CONTEXT            *PeriodicTimerContext,
  IN OUT   UINTN                                     *SizeOfContext
  )
{
  //LibFchSmmIoWrite (&gSmst->SmmIo, SMM_IO_UINT16, 0x80, &mLongTimerCounter);
  LibFchSmmIoWrite (&gSmst->SmmIo, SMM_IO_UINT8, 0x80, &mLongTimerCounter);
  mLongTimerCounter++;
  return EFI_SUCCESS;
}


/**
 * @brief Handler for ShortTimer SMI.
 *
 * @param[in] DispatchHandle       The handle of this callback, obtained when registering
 * @param[in] RegisterContext      Pointer to the FCH_SMM_PERIODICAL_REGISTER_CONTEXT
 * @param[in] PeriodicTimerContext Pointer to the EFI_SMM_PERIODIC_TIMER_CONTEXT context
 * @param[in] SizeOfContext        Pointer to the size of context
 *
 * @returns EFI_STATUS
 * @retval EFI_SUCCESS   Module initialized successfully
 * @retval EFI_ERROR     Initialization failed (see error for more details)
 */
EFI_STATUS
EFIAPI
AmdSmiShortTimerCallback (
  IN       EFI_HANDLE                                DispatchHandle,
  IN       CONST FCH_SMM_PERIODICAL_REGISTER_CONTEXT *RegisterContext,
  IN OUT   EFI_SMM_PERIODIC_TIMER_CONTEXT            *PeriodicTimerContext,
  IN OUT   UINTN                                     *SizeOfContext
  )
{
  //LibFchSmmIoWrite (&gSmst->SmmIo, SMM_IO_UINT16, 0x80, &mShortTimerCounter);
  LibFchSmmIoWrite (&gSmst->SmmIo, SMM_IO_UINT8, 0x80, &mShortTimerCounter);
  mShortTimerCounter++;
  return EFI_SUCCESS;
}


EFI_STATUS
FchSmmRegisterPeriodicTimerSmi (
  VOID
  )
{
  EFI_STATUS                              Status;
  FCH_SMM_PERIODICAL_DISPATCH2_PROTOCOL   *AmdPeriodicalDispatch;
  FCH_SMM_PERIODICAL_REGISTER_CONTEXT     PeriodicalRegisterContext;
  EFI_HANDLE                              PtHandle;
  FCH_MISC                                *FchMisc;

  FchMisc = &gFchDataInSmm.Misc;

  //
  // Periodic Timer SMI Registration
  //
  Status = gSmst->SmmLocateProtocol (
                  &gFchSmmPeriodicalDispatch2ProtocolGuid,
                  NULL,
                  (VOID **)&AmdPeriodicalDispatch
                  );
  ASSERT_EFI_ERROR (Status);

  //
  // Due to the post code limitation, we choose to turn on LongTimer and ShortTimer SMIs exclusively.
  // If both are set to enable in setup, we only register the LongTimer SMI.
  //
  if (FchMisc->LongTimer.Enable) {
    PeriodicalRegisterContext.SmiTickInterval  = LONG_TIMER_SMI_INTERVAL;
    PeriodicalRegisterContext.Period           = FchMisc->LongTimer.CycleDuration * LONG_TIMER_SMI_INTERVAL;
    PeriodicalRegisterContext.StartNow         = FchMisc->LongTimer.StartNow;
    mLongTimerCounter = 0;
    Status = AmdPeriodicalDispatch->Register (
                                      AmdPeriodicalDispatch,
                                      AmdSmiLongTimerCallback,
                                      &PeriodicalRegisterContext,
                                      &PtHandle
                                      );
  } else if (FchMisc->ShortTimer.Enable) {
    PeriodicalRegisterContext.SmiTickInterval  = SHORT_TIMER_SMI_INTERVAL;
    PeriodicalRegisterContext.Period           = (FchMisc->ShortTimer.CycleDuration >> 1) * SHORT_TIMER_SMI_INTERVAL;
    PeriodicalRegisterContext.StartNow         = FchMisc->ShortTimer.StartNow;
    mShortTimerCounter = 0;
    Status = AmdPeriodicalDispatch->Register (
                                      AmdPeriodicalDispatch,
                                      AmdSmiShortTimerCallback,
                                      &PeriodicalRegisterContext,
                                      &PtHandle
                                      );
  }
  return Status;
}





