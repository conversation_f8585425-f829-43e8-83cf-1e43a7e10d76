/*****************************************************************************
 *
 * Copyright (C) 2008-2024 Advanced Micro Devices, Inc. All rights reserved.
 *
 *******************************************************************************/

 /* $NoKeywords:$ */
 /**
  * @file
  *
  * FCH PEIM
  *
  *
  * @xrefitem bom "File Content Label" "Release Content"
  * @e project:      AGESA
  * @e sub-project   FCH PEIM
  * @e \$Revision: 309090 $   @e \$Date: 2014-12-09 10:28:05 -0800 (Tue, 09 Dec 2014) $
  *
  */

#include "FchPei.h"
#include "FchReset.h"
#include "FchReset2.h"
#include "FchStall.h"
#include <cpuRegisters.h>
#include <Library/FchPeiLib.h>
#define FILECODE FCH_KUNLUN_FCHKUNLUNPEI_FCHPEI_FILECODE

#include <Ppi/CpuIo.h>
#include <Ppi/Reset.h>
#include <Ppi/Reset2.h>
#include <Ppi/Stall.h>

#include <Library/BaseMemoryLib.h>
#include <Library/HobLib.h>

extern EFI_GUID gFchResetDataHobGuid;
extern FCH_RESET_DATA_BLOCK   InitResetCfgDefault;

//
// Module globals
//
STATIC EFI_PEI_RESET_PPI mResetPpi = {
  FchPeiReset
};

STATIC EFI_PEI_RESET2_PPI mResetPpi2 = {
  FchPeiReset2
};

STATIC EFI_PEI_STALL_PPI mStallPpi = {
  FCH_STALL_RESOLUTION_USEC,
  FchPeiStall
};

STATIC EFI_PEI_PPI_DESCRIPTOR mPpiListReset = {
  (EFI_PEI_PPI_DESCRIPTOR_PPI | EFI_PEI_PPI_DESCRIPTOR_TERMINATE_LIST),
  &gEfiPeiResetPpiGuid,
  &mResetPpi
};

STATIC EFI_PEI_PPI_DESCRIPTOR mPpiListReset2 = {
  (EFI_PEI_PPI_DESCRIPTOR_PPI | EFI_PEI_PPI_DESCRIPTOR_TERMINATE_LIST),
  &gEfiPeiReset2PpiGuid,
  &mResetPpi2
};

STATIC EFI_PEI_PPI_DESCRIPTOR mPpiListStall = {
  (EFI_PEI_PPI_DESCRIPTOR_PPI | EFI_PEI_PPI_DESCRIPTOR_TERMINATE_LIST),
  &gEfiPeiStallPpiGuid,
  &mStallPpi
};

EFI_PEI_NOTIFY_DESCRIPTOR   mMemoryDicoverPpiCallback = {
  (EFI_PEI_PPI_DESCRIPTOR_NOTIFY_CALLBACK | EFI_PEI_PPI_DESCRIPTOR_TERMINATE_LIST),
  &gEfiPeiMemoryDiscoveredPpiGuid,
  FchMemoryDiscoveredPpiCallback
};

//Data init routine to setup default value
EFI_STATUS
EFIAPI
FchInitResetDataDefault (
  IN       FCH_RESET_DATA_BLOCK  *FchParams
  );

EFI_STATUS
EFIAPI
FchInitPcdResetData (
  IN       FCH_RESET_DATA_BLOCK  *FchParams
  );

//main routine for Fch PEI init work
EFI_STATUS
EFIAPI
FchInitPei (
  IN       FCH_RESET_DATA_BLOCK  *FchParams
  );

AGESA_STATUS
FchPeiReportConfigValues (
  VOID
  );

EFI_STATUS
FchCheckAfterG3 (
  VOID
  );

EFI_STATUS
FchCreateHobROM3 (
  IN       EFI_PEI_SERVICES    **PeiServices
  );

/**
 * @brief Entry point of the Kunlun FCH PEIM.
 *        Initialize FCH and publish the PPIs
 *
 * @details Update boot mode
 *          Install Reset PPI
 *          Install SMBUS PPI
 *          Install Stall PPI
 *
 * @param[in] FileHandle Pointer to the firmware file system header
 * @param[in] PeiServices Pointer to the PEI service table
 *
 * @returns EFI_STATUS
 * @retval EFI_SUCCESS   Module initialized successfully
 * @retval EFI_ERROR     Initialization failed (see error for more details)
 */
EFI_STATUS
EFIAPI
FchPeiInit (
  IN       EFI_PEI_FILE_HANDLE  FileHandle,
  IN CONST EFI_PEI_SERVICES     **PeiServices
  )
{
  EFI_STATUS                Status;
  EFI_PEI_RESET_PPI         *PeiReset;
  EFI_PEI_RESET2_PPI        *PeiReset2;
  EFI_PEI_STALL_PPI         *PeiStall;
  EFI_HOB_GUID_TYPE         *FchHob;

  FCH_PEI_PRIVATE           *FchPrivate;
  EFI_PEI_PPI_DESCRIPTOR    *PpiListFchInit;
  FCH_RESET_DATA_BLOCK      *FchParams;

  AGESA_TESTPOINT (TpFchPeiEntry, NULL);
  //
  // Check SOC ID
  //


  //
  // Check Fch HW ID
  //


  // Create Fch GUID HOB to save FCH_RESET_DATA_BLOCK
  Status = (*PeiServices)->CreateHob (
                             PeiServices,
                             EFI_HOB_TYPE_GUID_EXTENSION,
                             sizeof (EFI_HOB_GUID_TYPE) + sizeof (FCH_RESET_DATA_BLOCK),
                             (VOID **)&FchHob
                             );

  ASSERT_EFI_ERROR (Status);

  CopyMem (&FchHob->Name, &gFchResetDataHobGuid, sizeof (EFI_GUID));
  FchHob++;
  FchParams = (FCH_RESET_DATA_BLOCK *)FchHob;
  //load default to Fch data structure
  Status = FchInitResetDataDefault (FchParams);

  //Init FCH_PEI_PRIVATE
  Status = (*PeiServices)->AllocatePool (
                             PeiServices,
                             sizeof (FCH_PEI_PRIVATE),
                             (VOID **)&FchPrivate
                             );
  ASSERT_EFI_ERROR ( Status);

  FchPrivate->Signature                    = FCH_PEI_PRIVATE_DATA_SIGNATURE;
  FchPrivate->StdHdr.AltImageBasePtr       = (UINT32) PeiServices;
  FchPrivate->FchInitPpi.Revision          = AMD_FCH_INIT_PPI_REV;
  FchPrivate->FchInitPpi.FchResetData      = (VOID*) FchParams;
  FchPrivate->FchInitPpi.PtResetData       = (VOID*) &(FchParams->Promontory);
  FchPrivate->FchInitPpi.FpFchXhciRecovery = FchInitXhciOnRecovery;
  FchPrivate->FchInitPpi.FpFchEhciRecovery = FchInitEhciOnRecovery;
  FchPrivate->FchInitPpi.FpFchSataRecovery = FchInitSataOnRecovery;
  FchPrivate->FchInitPpi.FpFchGppRecovery  = FchInitGppOnRecovery;
  FchPrivate->FchInitPpi.FpFchEmmcRecovery = FchInitEmmcOnRecovery;

  //platform/OEM update 6.5
  //AgesaFchOemCallout (FchParams);
  //Do the real init tasks
  DEBUG ((DEBUG_INFO, "[FchInitPei] Fch Pei Init ...Start.\n"));
  Status = FchInitPei (FchParams);
  DEBUG ((DEBUG_INFO, "[FchInitPei] Fch Pei Init ...Complete.\n"));
  //
  // Update the boot mode
  //
  Status = FchUpdateBootMode ((EFI_PEI_SERVICES **)PeiServices);
  ASSERT_EFI_ERROR (Status);

  //set PCd for After G3
  FchCheckAfterG3();

  FchCreateHobROM3((EFI_PEI_SERVICES **)PeiServices);
  //
  // publish other PPIs
  //
  // Reset PPI
  // check to see if an instance is already installed
  Status = (*PeiServices)->LocatePpi (
                             PeiServices,
                             &gEfiPeiResetPpiGuid,
                             0,
                             NULL,
                             (VOID **)&PeiReset
                             );

  if (EFI_NOT_FOUND == Status) {
    // No instance currently installed, install our own
    Status = (*PeiServices)->InstallPpi (
                               PeiServices,
                               &mPpiListReset
                               );

    ASSERT_EFI_ERROR ( Status);
  }

  // Reset2 PPI
  Status = (*PeiServices)->LocatePpi (
                             PeiServices,
                             &gEfiPeiReset2PpiGuid,
                             0,
                             NULL,
                             (VOID **)&PeiReset2
                             );

  if (EFI_NOT_FOUND == Status) {
    // No instance currently installed, install our own
    Status = (*PeiServices)->InstallPpi (
                               PeiServices,
                               &mPpiListReset2
                               );

    ASSERT_EFI_ERROR ( Status);
  }

  // Stall PPI
  // check to see if an instance is already installed
  Status = (*PeiServices)->LocatePpi (
                             PeiServices,
                             &gEfiPeiStallPpiGuid,
                             0,
                             NULL,
                             (VOID **)&PeiStall
                             );

  if (EFI_NOT_FOUND == Status) {
    // There is no instance currently installed, install our own
    Status = (*PeiServices)->InstallPpi (
                               PeiServices,
                               &mPpiListStall
                               );

    ASSERT_EFI_ERROR ( Status);
  }

  // Allocate memory for the PPI descriptor
  Status = (*PeiServices)->AllocatePool (
                             PeiServices,
                             sizeof (EFI_PEI_PPI_DESCRIPTOR),
                             (VOID **)&PpiListFchInit
                             );
  ASSERT_EFI_ERROR ( Status);

  PpiListFchInit->Flags = (EFI_PEI_PPI_DESCRIPTOR_PPI | EFI_PEI_PPI_DESCRIPTOR_TERMINATE_LIST);
  PpiListFchInit->Guid  = &gAmdFchInitPpiGuid;
  PpiListFchInit->Ppi   = &FchPrivate->FchInitPpi;

  Status = (*PeiServices)->InstallPpi (
                             PeiServices,
                             PpiListFchInit
                             );
  ASSERT_EFI_ERROR ( Status);

  Status = (*PeiServices)->NotifyPpi (PeiServices, &mMemoryDicoverPpiCallback);
  ASSERT_EFI_ERROR ( Status);

  AGESA_TESTPOINT (TpFchPeiExit, NULL);
  return Status;
}


/**
 * @brief load Default value of FCH_RESET_DATA_BLOCK
 *
 * @param[in] FchParams Pointer to FCH_RESET_DATA_BLOCK
 *
 * @returns EFI_STATUS
 * @retval EFI_SUCCESS   Procedure successfully
 * @retval EFI_ERROR     Procedure failed (see error for more details)
 */
EFI_STATUS
EFIAPI
FchInitResetDataDefault (
  IN       FCH_RESET_DATA_BLOCK  *FchParams
  )
{
  EFI_STATUS                Status;

  CopyMem (FchParams, &InitResetCfgDefault, sizeof (FCH_RESET_DATA_BLOCK));

  //Platform call out
  Status = FchPlatformPTPeiInit ((VOID *)&FchParams->Promontory);

  ASSERT_EFI_ERROR (Status);

  Status = FchPlatformOemPeiInit ((VOID *)FchParams);

  ASSERT_EFI_ERROR (Status);

  IDS_HOOK (IDS_HOOK_FCH_INIT_RESET, NULL, (VOID *)FchParams);

  FchInitPcdResetData (FchParams);

  return EFI_SUCCESS;
}


/**
 * @brief load Default value of FCH_RESET_DATA_BLOCK from PCDs
 *
 * @param[in] FchParams Pointer to FCH_RESET_DATA_BLOCK
 *
 * @returns EFI_STATUS
 * @retval EFI_SUCCESS   Procedure successfully
 * @retval EFI_ERROR     Procedure failed (see error for more details)
 */
EFI_STATUS
EFIAPI
FchInitPcdResetData (
  IN       FCH_RESET_DATA_BLOCK  *FchParams
  )
{
  FchParams->FchBldCfg.CfgSmbus0BaseAddress = PcdGet16 (PcdAmdFchCfgSmbus0BaseAddress);
  FchParams->FchBldCfg.CfgSioPmeBaseAddress = PcdGet16 (PcdAmdFchCfgSioPmeBaseAddress);
  FchParams->FchBldCfg.CfgAcpiPm1EvtBlkAddr = PcdGet16 (PcdAmdFchCfgAcpiPm1EvtBlkAddr);
  FchParams->FchBldCfg.CfgAcpiPm1CntBlkAddr = PcdGet16 (PcdAmdFchCfgAcpiPm1CntBlkAddr);
  FchParams->FchBldCfg.CfgAcpiPmTmrBlkAddr  = PcdGet16 (PcdAmdFchCfgAcpiPmTmrBlkAddr);
  FchParams->FchBldCfg.CfgCpuControlBlkAddr = PcdGet16 (PcdAmdFchCfgCpuControlBlkAddr);
  FchParams->FchBldCfg.CfgAcpiGpe0BlkAddr   = PcdGet16 (PcdAmdFchCfgAcpiGpe0BlkAddr);
  FchParams->FchBldCfg.CfgSmiCmdPortAddr    = PcdGet16 (PcdAmdFchCfgSmiCmdPortAddr);
  //FchParams->FchBldCfg.CfgCpuControlBlkAddr = PcdGet16 (PcdAmdFchCfgCpuControlBlkAddr);

  //Dynamic PCDs
  FchParams->LegacyFree                    = PcdGetBool (PcdLegacyFree);
  //FchParams->EcKbd                         = PcdGetBool (PcdEcKbd);
  FchParams->FchOscout1ClkContinous        = PcdGetBool (PcdFchOscout1ClkContinous);
  FchParams->FchReset.SataEnable           = PcdGetBool (PcdSataEnable);
  if (FchParams->FchReset.SataEnable == FALSE) {
    FCH_PCDSET8 (PcdSataEnable2, 0x00);        //Disable all Sata
  }
  FchParams->SataSetMaxGen2                = PcdGetBool (PcdSataSetMaxGen2);
  FchParams->SataClkMode                   = PcdGet8 (PcdSataClkMode);
  FchParams->SataActLWkaEnable             = PcdGetBool (PcdSataActLWkaEnable);
  FchParams->FchReset.Xhci0Enable          = PcdGetBool (PcdXhci0Enable);
  FchParams->FchReset.Xhci1Enable          = PcdGetBool (PcdXhci1Enable);
  FchParams->LpcClockDriveStrength         = PcdGet8 (PcdLpcClockDriveStrength);
  FchParams->LpcClockDriveStrengthRiseTime = PcdGet8 (PcdLpcClockDriveStrengthRiseTime);
  FchParams->LpcClockDriveStrengthFallTime = PcdGet8 (PcdLpcClockDriveStrengthFallTime);
  //FchParams->Mode                          = PcdGet8 (PcdResetMode);   it has been replaced by  EFS to configure the setting.
  FchParams->SpiSpeed                      = PcdGet8 (PcdResetSpiSpeed);
  //FchParams->FastSpeed                     = PcdGet8 (PcdResetFastSpeed);  it has been replaced by  EFS to configure the setting.
  FchParams->WriteSpeed                    = PcdGet8 (PcdResetWriteSpeed);
  FchParams->SpiTpmSpeed                   = PcdGet8 (PcdResetSpiTpmSpeed);
  FchParams->Spi.LpcEnable                 = PcdGetBool (PcdLpcEnable);
  FchParams->Spi.LpcClk0                   = PcdGetBool (PcdLpcClk0);
  FchParams->Spi.LpcClk1                   = PcdGetBool (PcdLpcClk1);
  FchParams->Gpp.SerialDebugBusEnable      = PcdGetBool (PcdSerialDebugBusEnable);
  FchParams->WdtEnable                     = PcdGetBool (PcdFchWdtEnable);
  FchParams->BootTimerEnable               = PcdGetBool (PcdBootTimerEnable);
  FchParams->BootTimerResetType            = PcdGet8 (PcdBootTimerResetType);
  FchParams->Xhci0DevRemovable             = PcdGet32 (PcdXhci0DevRemovable);
  FchParams->DisableXhciPortLate           = PcdGetBool (PcdDisableXhciPortLate);
  FchParams->XhciUsb3PortDisable           = PcdGet32 (PcdXhciUsb3PortDisable);
  FchParams->XhciUsb2PortDisable           = PcdGet32 (PcdXhciUsb2PortDisable);
  FchParams->XhciOCpinSelect[0].Usb20OcPin = (UINT32)PcdGet64 (PcdXhciUsb20OcPinSelect);
  FchParams->XhciOCpinSelect[0].Usb31OcPin = (UINT16)PcdGet32 (PcdXhciUsb31OcPinSelect);
  FchParams->XhciOCpinSelect[1].Usb20OcPin = (UINT32)(RShiftU64 (PcdGet64 (PcdXhciUsb20OcPinSelect), 8));
  FchParams->XhciOCpinSelect[1].Usb31OcPin = (UINT16)(PcdGet32 (PcdXhciUsb31OcPinSelect) >> 8);
  FchParams->XhciOcPolarityCfgLow          = PcdGetBool (PcdXhciOcPolarityCfgLow);
  FchParams->Usb3PortForceGen1             = PcdGet8 (PcdXhciForceGen1);
  FchParams->OemUsbConfigurationTablePtr   = PcdGetPtr (PcdUsbKLOemConfigurationTable);
  FchParams->SerialIrqEnable               = PcdGetBool (PcdSerialIrqEnable);
  FchParams->Emmc.EmmcEnable               = PcdGet8 (PcdEmmcEnable);
  FchParams->UsbSparseModeEnable           = PcdGetBool (PcdUsbSparseModeEnable);
  FchParams->ToggleAllPwrGoodOnCf9         = PcdGetBool (PcdToggleAllPwrGoodOnCf9);
  FchParams->UsbDbgSCPipeSwitchEnable      = PcdGetBool (PcdUsbDbgSCPipeSwitchEnable);
  // I2C
  FchParams->I2CEnable                     = PcdGet32 (FchRTDeviceEnableMap);
  FchParams->I2CSdaHold[0]                 = PcdGet32 (PcdAmdFchI2c0SdaHold);
  FchParams->I2CSdaHold[1]                 = PcdGet32 (PcdAmdFchI2c1SdaHold);
  FchParams->I2CSdaHold[2]                 = PcdGet32 (PcdAmdFchI2c2SdaHold);
  FchParams->I2CSdaHold[3]                 = PcdGet32 (PcdAmdFchI2c3SdaHold);
  FchParams->I2CSdaHold[4]                 = PcdGet32 (PcdAmdFchI2c4SdaHold);
  FchParams->I2CSdaHold[5]                 = PcdGet32 (PcdAmdFchI2c5SdaHold);

  //Sata controller
  {
    UINT8   SataController;
    UINT8   SataEnable2;
    UINT32  SataRxPolarity;

    SataEnable2 = PcdGet8 (PcdSataEnable2);
    SataRxPolarity = (UINT32) PcdGet64 (PcdSataMultiDiePortRxPolarity);

    for (SataController = 0; SataController < KUNLUN_SATA_CONTROLLER_NUM; SataController++) {
      if (SataEnable2 & (1 << SataController)) {
        FchParams->SataEnable[SataController] = TRUE;
        FchParams->SataRxPolarity[SataController] = (UINT8) (SataRxPolarity >> (8 * SataController));
      } else {
        FchParams->SataEnable[SataController] = FALSE;
      }
    }

    FchParams->SataStaggeredSpinupEnable = PcdGetBool (PcdSataStaggeredSpinup);
  }

  FchParams->FchIoApicId                   = PcdGet8 (PcdCfgFchIoapicId);

  FchParams->Promontory.PromontoryUSB.PTXhciGen1  = PcdGet8 (PcdPTXhciGen1);
  FchParams->Promontory.PromontoryUSB.PTXhciGen2  = PcdGet8 (PcdPTXhciGen2);
  FchParams->Promontory.PromontoryUSB.PTAOAC      = PcdGet8 (PcdPTAOAC);
  FchParams->Promontory.PromontoryUSB.PTHW_LPM    = PcdGet8 (PcdPTHW_LPM);
  FchParams->Promontory.PromontoryUSB.PTDbC       = PcdGet8 (PcdPTDbC);
  FchParams->Promontory.PromontoryUSB.PTXHC_PME   = PcdGet8 (PcdPTXHC_PME);

  FchParams->Promontory.PromontorySATA.PTSataMode               = PcdGet8 (PcdPTSataMode);
  FchParams->Promontory.PromontorySATA.PTSataAggresiveDevSlpP0  = PcdGet8 (PcdPTSataAggresiveDevSlpP0);
  FchParams->Promontory.PromontorySATA.PTSataAggresiveDevSlpP1  = PcdGet8 (PcdPTSataAggresiveDevSlpP1);
  FchParams->Promontory.PromontorySATA.PTSataAggrLinkPmCap      = PcdGet8 (PcdPTSataAggrLinkPmCap);
  FchParams->Promontory.PromontorySATA.PTSataPscCap             = PcdGet8 (PcdPTSataPscCap);
  FchParams->Promontory.PromontorySATA.PTSataSscCap             = PcdGet8 (PcdPTSataSscCap);
  FchParams->Promontory.PromontorySATA.PTSataMsiCapability      = PcdGet8 (PcdPTSataMsiCapability);
  FchParams->Promontory.PromontorySATA.PTSataPortMdPort0        = PcdGet8 (PcdPTSataPortMdPort0);
  FchParams->Promontory.PromontorySATA.PTSataPortMdPort1        = PcdGet8 (PcdPTSataPortMdPort1);
  FchParams->Promontory.PromontorySATA.PTSataHotPlug            = PcdGet8 (PcdPTSataHotPlug);
  FchParams->Promontory.PromontoryUSB.Equalization4             = PcdGet8 (PcdPTUsbEqualization4);
  FchParams->Promontory.PromontoryUSB.Redriver                  = PcdGet8 (PcdPTUsbRedriver);

  FchParams->Promontory.PromontoryUSBPort.PTUsb31P0             = PcdGet8 (PcdPTUsb31P0);
  FchParams->Promontory.PromontoryUSBPort.PTUsb31P1             = PcdGet8 (PcdPTUsb31P1);
  FchParams->Promontory.PromontoryUSBPort.PTUsb30P0             = PcdGet8 (PcdPTUsb30P0);
  FchParams->Promontory.PromontoryUSBPort.PTUsb30P1             = PcdGet8 (PcdPTUsb30P1);
  FchParams->Promontory.PromontoryUSBPort.PTUsb30P2             = PcdGet8 (PcdPTUsb30P2);
  FchParams->Promontory.PromontoryUSBPort.PTUsb30P3             = PcdGet8 (PcdPTUsb30P3);
  FchParams->Promontory.PromontoryUSBPort.PTUsb30P4             = PcdGet8 (PcdPTUsb30P4);
  FchParams->Promontory.PromontoryUSBPort.PTUsb30P5             = PcdGet8 (PcdPTUsb30P5);
  FchParams->Promontory.PromontoryUSBPort.PTUsb20P0             = PcdGet8 (PcdPTUsb20P0);
  FchParams->Promontory.PromontoryUSBPort.PTUsb20P1             = PcdGet8 (PcdPTUsb20P1);
  FchParams->Promontory.PromontoryUSBPort.PTUsb20P2             = PcdGet8 (PcdPTUsb20P2);
  FchParams->Promontory.PromontoryUSBPort.PTUsb20P3             = PcdGet8 (PcdPTUsb20P3);
  FchParams->Promontory.PromontoryUSBPort.PTUsb20P4             = PcdGet8 (PcdPTUsb20P4);
  FchParams->Promontory.PromontoryUSBPort.PTUsb20P5             = PcdGet8 (PcdPTUsb20P5);

  FchParams->Promontory.PTUSBPortPROM2.PTUsb31P0                = PcdGet8 (PcdPTProm2Usb31P0);
  FchParams->Promontory.PTUSBPortPROM2.PTUsb31P1                = PcdGet8 (PcdPTProm2Usb31P1);
  FchParams->Promontory.PTUSBPortPROM2.PTUsb30P0                = PcdGet8 (PcdPTProm2Usb30P0);
  FchParams->Promontory.PTUSBPortPROM2.PTUsb30P1                = PcdGet8 (PcdPTProm2Usb30P1);
  FchParams->Promontory.PTUSBPortPROM2.PTUsb20P0                = PcdGet8 (PcdPTProm2Usb20P0);
  FchParams->Promontory.PTUSBPortPROM2.PTUsb20P1                = PcdGet8 (PcdPTProm2Usb20P1);
  FchParams->Promontory.PTUSBPortPROM2.PTUsb20P2                = PcdGet8 (PcdPTProm2Usb20P2);
  FchParams->Promontory.PTUSBPortPROM2.PTUsb20P3                = PcdGet8 (PcdPTProm2Usb20P3);
  FchParams->Promontory.PTUSBPortPROM2.PTUsb20P4                = PcdGet8 (PcdPTProm2Usb20P4);
  FchParams->Promontory.PTUSBPortPROM2.PTUsb20P5                = PcdGet8 (PcdPTProm2Usb20P5);

  FchParams->Promontory.PTUSBPortPROM1.PTUsb31P0                = PcdGet8 (PcdPTProm1Usb31P0);
  FchParams->Promontory.PTUSBPortPROM1.PTUsb31P1                = PcdGet8 (PcdPTProm1Usb31P1);
  FchParams->Promontory.PTUSBPortPROM1.PTUsb30P0                = PcdGet8 (PcdPTProm1Usb30P0);
  FchParams->Promontory.PTUSBPortPROM1.PTUsb20P0                = PcdGet8 (PcdPTProm1Usb20P0);
  FchParams->Promontory.PTUSBPortPROM1.PTUsb20P1                = PcdGet8 (PcdPTProm1Usb20P1);
  FchParams->Promontory.PTUSBPortPROM1.PTUsb20P2                = PcdGet8 (PcdPTProm1Usb20P2);
  FchParams->Promontory.PTUSBPortPROM1.PTUsb20P3                = PcdGet8 (PcdPTProm1Usb20P3);
  FchParams->Promontory.PTUSBPortPROM1.PTUsb20P4                = PcdGet8 (PcdPTProm1Usb20P4);
  FchParams->Promontory.PTUSBPortPROM1.PTUsb20P5                = PcdGet8 (PcdPTProm1Usb20P5);

  FchParams->Promontory.PromontorySATAPort.PTSataPort0Enable    = PcdGet8 (PcdPTSataPort0Enable);
  FchParams->Promontory.PromontorySATAPort.PTSataPort1Enable    = PcdGet8 (PcdPTSataPort1Enable);
  FchParams->Promontory.PromontorySATAPort.PTSataPort2Enable    = PcdGet8 (PcdPTSataPort2Enable);
  FchParams->Promontory.PromontorySATAPort.PTSataPort3Enable    = PcdGet8 (PcdPTSataPort3Enable);
  FchParams->Promontory.PromontorySATAPort.PTSataPort4Enable    = PcdGet8 (PcdPTSataPort4Enable);
  FchParams->Promontory.PromontorySATAPort.PTSataPort5Enable    = PcdGet8 (PcdPTSataPort5Enable);
  FchParams->Promontory.PromontorySATAPort.PTSataPort6Enable    = PcdGet8 (PcdPTSataPort6Enable);
  FchParams->Promontory.PromontorySATAPort.PTSataPort7Enable    = PcdGet8 (PcdPTSataPort7Enable);

  FchParams->Promontory.PCIEPorts[0]                            = (BOOLEAN)PcdGet8 (PcdPTPciePort0Enable);
  FchParams->Promontory.PCIEPorts[1]                            = (BOOLEAN)PcdGet8 (PcdPTPciePort1Enable);
  FchParams->Promontory.PCIEPorts[2]                            = (BOOLEAN)PcdGet8 (PcdPTPciePort2Enable);
  FchParams->Promontory.PCIEPorts[3]                            = (BOOLEAN)PcdGet8 (PcdPTPciePort3Enable);
  FchParams->Promontory.PCIEPorts[4]                            = (BOOLEAN)PcdGet8 (PcdPTPciePort4Enable);
  FchParams->Promontory.PCIEPorts[5]                            = (BOOLEAN)PcdGet8 (PcdPTPciePort5Enable);
  FchParams->Promontory.PCIEPorts[6]                            = (BOOLEAN)PcdGet8 (PcdPTPciePort6Enable);
  FchParams->Promontory.PCIEPorts[7]                            = (BOOLEAN)PcdGet8 (PcdPTPciePort7Enable);

  FchParams->Promontory.LPPTGPPClkForceOn[0]                    = (BOOLEAN)PcdGet8 (PcdPTGppClk0ForceOn);
  FchParams->Promontory.LPPTGPPClkForceOn[1]                    = (BOOLEAN)PcdGet8 (PcdPTGppClk1ForceOn);
  FchParams->Promontory.LPPTGPPClkForceOn[2]                    = (BOOLEAN)PcdGet8 (PcdPTGppClk2ForceOn);
  FchParams->Promontory.LPPTGPPClkForceOn[3]                    = (BOOLEAN)PcdGet8 (PcdPTGppClk3ForceOn);
  FchParams->Promontory.LPPTGPPClkForceOn[4]                    = (BOOLEAN)PcdGet8 (PcdPTGppClk4ForceOn);
  FchParams->Promontory.LPPTGPPClkForceOn[5]                    = (BOOLEAN)PcdGet8 (PcdPTGppClk5ForceOn);
  FchParams->Promontory.LPPTGPPClkForceOn[6]                    = (BOOLEAN)PcdGet8 (PcdPTGppClk6ForceOn);
  FchParams->Promontory.LPPTGPPClkForceOn[7]                    = (BOOLEAN)PcdGet8 (PcdPTGppClk7ForceOn);

  FchParams->Promontory.PTUSBPortPRO460.PTUsb31P0               = (BOOLEAN)PcdGet8 (PcdPTPro460Usb31P0);
  FchParams->Promontory.PTUSBPortPRO460.PTUsb31P1               = (BOOLEAN)PcdGet8 (PcdPTPro460Usb31P1);
  FchParams->Promontory.PTUSBPortPRO460.PTUsb30P0               = (BOOLEAN)PcdGet8 (PcdPTPro460Usb30P0);
  FchParams->Promontory.PTUSBPortPRO460.PTUsb30P1               = (BOOLEAN)PcdGet8 (PcdPTPro460Usb30P1);
  FchParams->Promontory.PTUSBPortPRO460.PTUsb30P2               = (BOOLEAN)PcdGet8 (PcdPTPro460Usb30P2);
  FchParams->Promontory.PTUSBPortPRO460.PTUsb30P3               = (BOOLEAN)PcdGet8 (PcdPTPro460Usb30P3);
  FchParams->Promontory.PTUSBPortPRO460.PTUsb20P0               = (BOOLEAN)PcdGet8 (PcdPTPro460Usb20P0);
  FchParams->Promontory.PTUSBPortPRO460.PTUsb20P1               = (BOOLEAN)PcdGet8 (PcdPTPro460Usb20P1);
  FchParams->Promontory.PTUSBPortPRO460.PTUsb20P2               = (BOOLEAN)PcdGet8 (PcdPTPro460Usb20P2);
  FchParams->Promontory.PTUSBPortPRO460.PTUsb20P3               = (BOOLEAN)PcdGet8 (PcdPTPro460Usb20P3);
  FchParams->Promontory.PTUSBPortPRO460.PTUsb20P4               = (BOOLEAN)PcdGet8 (PcdPTPro460Usb20P4);
  FchParams->Promontory.PTUSBPortPRO460.PTUsb20P5               = (BOOLEAN)PcdGet8 (PcdPTPro460Usb20P5);
  FchParams->Promontory.PromontoryUSB.XhciLockEnable            = PcdGet8 (PcdPTLock);
  FchParams->FchAsfCfg.DisableSlave                             = PcdGetBool (PcdAmdFchDisableAsfSlave);

  return EFI_SUCCESS;
}


/**
 * @brief Initialization for FCH controller
 *
 * @param[in] FchParams Pointer to FCH_RESET_DATA_BLOCK
 *
 * @returns EFI_STATUS
 * @retval EFI_SUCCESS   Procedure successfully
 * @retval EFI_ERROR     Procedure failed (see error for more details)
 */
EFI_STATUS
EFIAPI
FchInitPei (
  IN       FCH_RESET_DATA_BLOCK  *FchParams
  )
{
  FchInitReset (FchParams);

  return EFI_SUCCESS;
}


/**
 * @brief This function determines whether the platform is resuming from an S state
 *   using the FCH ACPI registers
 *
 * @param[in] PeiServices Pointer to the PEI service table pointer
 *
 * @returns EFI_BOOT_MODE Boot mode.
 */
EFI_BOOT_MODE
FchGetBootMode (
  IN       EFI_PEI_SERVICES    **PeiServices
  )
{
  EFI_PEI_CPU_IO_PPI  *CpuIo;
  UINT16              FchBootMode;
  UINTN               AcpiPm1Ctl;

  //
  // find the CpuIo protocol
  //
  CpuIo = (*PeiServices)->CpuIo;

  //
  // Check the FCH WAK_STS bit in the ACPI_PM1_CTL register
  //
  // get the address PM1_CTL register address
  AcpiPm1Ctl  = LibFchPmIoRead16V2 ((CONST EFI_PEI_SERVICES **)PeiServices, FCH_PMIOA_REG62);
  AcpiPm1Ctl  &= ~BIT0;

  // get the boot mode as seen by the south bridge
  FchBootMode = (CpuIo->IoRead16 ((CONST EFI_PEI_SERVICES **)PeiServices, CpuIo, AcpiPm1Ctl) & ACPI_BM_MASK);

  // convert the boot mode to the EFI version
  if (ACPI_S3 == FchBootMode) {
    return (BOOT_ON_S3_RESUME);
  }
  if (ACPI_S4 == FchBootMode) {
    return (BOOT_ON_S4_RESUME);
  }
  if (ACPI_S5 == FchBootMode) {
    return (BOOT_ON_S5_RESUME);
  }
  // S0 or unsupported Sx mode
  return (BOOT_WITH_FULL_CONFIGURATION);
}


/**
 * @brief Check system power on after G3
 *
 * @param[in] VOID
 *
 * @details This function will set PcdFchIsAfterG3 as TRUE if system is resume from G3
 *
 * @returns EFI_STATUS
 */
EFI_STATUS
FchCheckAfterG3 (
 VOID
  )
{
  UINT8            RTCShadowValue;
  //PM::RTCSHADOW
  ReadMem (ACPI_MMIO_BASE + PMIO_BASE + FCH_PMIOA_REG5B , AccessWidth8, &RTCShadowValue);

  DEBUG ((DEBUG_INFO, "RTCShadowValue=0x%x\n",  RTCShadowValue));
  if (  (RTCShadowValue & 0x7) == 0){
    //BIT3:0 is zerro, resume from G3
    FCH_PCDSETBOOL(PcdFchIsAfterG3, TRUE);
    DEBUG ((DEBUG_INFO, "Is After G3\n"));
  }
  return (EFI_SUCCESS);
}


/**
 * @brief Update the platform boot mode
 *
 * @param[in] PeiServices Pointer to the PEI service table pointer
 *
 * @details This function update the platform boot mode based on the information
 *   gathered from the south bridge.
 *   Note that we do not publish the BOOT_MODE PPI since the platform
 *   is responsible for deciding what the actual boot mode is.
 *
 * @returns EFI_STATUS
 * @retval EFI_SUCCESS   Procedure successfully
 * @retval EFI_ERROR     Procedure failed (see error for more details)
 */
EFI_STATUS
FchUpdateBootMode (
  IN       EFI_PEI_SERVICES    **PeiServices
  )
{
  EFI_BOOT_MODE  BootMode;
  EFI_BOOT_MODE  FchBootMode;

  // Get FCH Boot mode
  FchBootMode = FchGetBootMode (PeiServices);

  // Get the platform boot mode
  (*PeiServices)->GetBootMode (
                    (CONST EFI_PEI_SERVICES **)PeiServices,
                    &BootMode
                    );

  // Update boot mode if we are more important than the platform
  if ((BOOT_IN_RECOVERY_MODE != BootMode) && (BOOT_ON_FLASH_UPDATE != BootMode) && (BOOT_WITH_FULL_CONFIGURATION != FchBootMode)) {
    // Set Sx boot mode
    (*PeiServices)->SetBootMode (
                      (CONST EFI_PEI_SERVICES **)PeiServices,
                      FchBootMode
                      );
  }
  return (EFI_SUCCESS);
}


EFI_STATUS
EFIAPI
FchMemoryDiscoveredPpiCallback (
  IN  EFI_PEI_SERVICES                **PeiServices,
  IN  EFI_PEI_NOTIFY_DESCRIPTOR       *NotifyDesc,
  IN  VOID                            *InvokePpi
  )
{
  EFI_HOB_GUID_TYPE         *FchHob;
  FCH_RESET_DATA_BLOCK      *FchResetParams;
  AMD_FCH_INIT_PPI          *FchInitPpi;
  EFI_STATUS                Status;

  Status = EFI_SUCCESS;

  FchHob = GetFirstGuidHob (&gFchResetDataHobGuid);
  if (FchHob == NULL) {
    DEBUG ((DEBUG_INFO, "FCH HOB Not located, Exiting.\n"));
    return EFI_UNSUPPORTED;
  }
  FchHob++;
  FchResetParams = (FCH_RESET_DATA_BLOCK *)FchHob;

  Status = (*PeiServices)->LocatePpi (
                             (CONST EFI_PEI_SERVICES **)PeiServices,
                             &gAmdFchInitPpiGuid,
                             0,
                             NULL,
                             (VOID **)&FchInitPpi
                             );
  ASSERT_EFI_ERROR ( Status);

  FchInitPpi->FchResetData      = (VOID*) FchResetParams;
  FchInitPpi->PtResetData       = (VOID*) &(FchResetParams->Promontory);

  return Status;
}


EFI_STATUS
EFIAPI
FchInitXhciOnRecovery (
  IN       AMD_FCH_INIT_PPI   *This,
  IN       UINT32             XhciRomAddress
  )
{
  return EFI_SUCCESS;
}


EFI_STATUS
EFIAPI
FchInitEhciOnRecovery (
  IN       AMD_FCH_INIT_PPI   *This,
  IN       UINT32             EhciTemporaryBarAddress
  )
{
  return EFI_SUCCESS;
}


EFI_STATUS
EFIAPI
FchInitSataOnRecovery (
  IN       AMD_FCH_INIT_PPI   *This,
  IN       UINT32             SataBar0,
  IN       UINT32             SataBar5
  )
{
  return EFI_SUCCESS;
}


EFI_STATUS
EFIAPI
FchInitGppOnRecovery (
  IN       AMD_FCH_INIT_PPI   *This,
  IN       FCH_GPP_R          *FchGpp
  )
{
  return EFI_SUCCESS;
}


EFI_STATUS
EFIAPI
FchInitEmmcOnRecovery (
  IN       AMD_FCH_INIT_PPI   *This
  )
{
  return EFI_SUCCESS;
}


/**
 * @brief Create memory reserved type for ROM3
 *
 * @param[in] PeiServices Pointer to the PEI service table pointer
 *
 * @details ROM3 MMIO should be reported to memory reserved  type to avoid OS to use it.
 *   If ROM3 <= TOM2 , don't need to do this since DF will handle this.
 *
 * @returns EFI_STATUS
 * @retval EFI_SUCCESS   Procedure successfully
 * @retval EFI_ERROR     Procedure failed (see error for more details)
 */
EFI_STATUS
FchCreateHobROM3 (
  IN       EFI_PEI_SERVICES    **PeiServices
  )
{
  UINT32         Mmio64Hi = 0, Mmio64Low = 0;
  UINT64         ROM3MmioBase = 0, TOM2 = 0;

  // Get ROM3 , TOM2 address
  ReadMem   (FCH_SPI_BASE_ADDRESS + FCH_PMIOA_REG60, AccessWidth32, &Mmio64Low);  //  LPCHOSTSPIREG::BAR_64MB_ROM3_LOW
  Mmio64Low &= 0xFC000000;   //Bits[25:0] reserved
  ReadMem   (FCH_SPI_BASE_ADDRESS + FCH_PMIOA_REG64, AccessWidth32, &Mmio64Hi);  // LPCHOSTSPIREG::BAR_64MB_ROM3_HIGH
  ROM3MmioBase = (UINT64)(Mmio64Low | LShiftU64 (Mmio64Hi, 32) );
  DEBUG ((DEBUG_INFO, "ROM3=0x%lX\n", ROM3MmioBase));
  TOM2 = AsmReadMsr64 (MSR_TOM2);
  DEBUG ((DEBUG_INFO, "TOM2=0x%lX\n", TOM2));
  // If ROM3 <= TOM2 , don't need to do this since DF will handle this.
  // reported to memory reserved  type to avoid OS to use it.
  if ( ROM3MmioBase > TOM2 ){
    DEBUG ((DEBUG_INFO, "report ROM3 as memory reserved\n"));
    BuildResourceDescriptorHob (
      EFI_RESOURCE_MEMORY_RESERVED,
      (
       EFI_RESOURCE_ATTRIBUTE_WRITE_PROTECTED |
       EFI_RESOURCE_ATTRIBUTE_WRITE_PROTECTABLE
      ),
      ROM3MmioBase,
      0x4000000 //64MB
    );
  }

  return (EFI_SUCCESS);
}
