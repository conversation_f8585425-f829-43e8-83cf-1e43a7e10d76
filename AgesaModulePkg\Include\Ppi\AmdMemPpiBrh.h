/*****************************************************************************
 *
 * Copyright (C) 2008-2024 Advanced Micro Devices, Inc. All rights reserved.
 *
 *******************************************************************************
 **/

/**
 * @file
 *
 * AMD MEM PPI prototype definition
 *
 */

#ifndef _AMD_MEM_PPI_BRH_H_
#define _AMD_MEM_PPI_BRH_H_

#include <Base.h>
#include "AGESA.h"
#include "PlatformMemoryConfiguration.h"
#include "Library/AmdCalloutLib.h"

//----------------------------------------------------
//
//  Amd Memory Service Functions
//
//-------------------------------------------------------

/**
 * @brief Reads Service for the Memory Above 4GB
 * @retval EFI_SUCCESS Return value is valid
 * @retval EFI_UNSUPPORTED This function is not supported by this version of the driver
 *
 */
typedef EFI_STATUS (EFIAPI * PEI_AMD_SOC_GET_MEMORY_ABOVE_4GB_INTERFACE  ) (
  IN      EFI_PEI_SERVICES  **PeiServices,  ///< A pointer to the PEI services
     OUT  UINT32            *AmountOfMemory  ///< Return data pointer for the amount of memory
  );

/**
 * @brief Read Service for the Memory Below 4GB
 * @retval EFI_SUCCESS Return value is valid
 * @retval EFI_UNSUPPORTED This function is not supported by this version of the driver
 */
typedef EFI_STATUS (EFIAPI * PEI_AMD_SOC_GET_MEMORY_BELOW_4GB_INTERFACE  ) (
  IN      EFI_PEI_SERVICES  **PeiServices,  ///< A pointer to the PEI services
     OUT  UINT32            *AmountOfMemory  ///< Return data pointer for the amount of memory
  );

/**
 * @brief Read Service for the memory Below 1TB
 * @retval EFI_SUCCESS Return value is valid
 * @retval EFI_UNSUPPORTED This function is not supported by this version of the driver
 */
typedef EFI_STATUS (EFIAPI * PEI_AMD_SOC_GET_MEMORY_BELOW_1TB_INTERFACE  ) (
  IN      EFI_PEI_SERVICES  **PeiServices,  ///< A pointer to the PEI services
     OUT  UINT32            *AmountOfMemory  ///< Return data pointer for the amount of memory
  );

/**
 * @brief Read Service for the total amount of memory in the system
 * @retval EFI_SUCCESS Return value is valid
 * @retval EFI_UNSUPPORTED This function is not supported by this version of the driver
 */
typedef EFI_STATUS (EFIAPI * PEI_AMD_SOC_GET_TOTAL_MEMORY_SIZE_INTERFACE) (
  IN      EFI_PEI_SERVICES  **PeiServices,  ///< A pointer to the PEI services
     OUT  UINT32            *AmountOfMemory  ///< Return data pointer for the amount of memory
  );

/**
 * @brief Read Service for the total amount of memory in the system
 * @retval EFI_SUCCESS Return value is valid
 * @retval EFI_UNSUPPORTED This function is not supported by this version of the driver
 */
typedef EFI_STATUS (EFIAPI * PEI_AMD_SOC_GET_MEMORY_BOTTOM_IO_INTERFACE) (
  IN      EFI_PEI_SERVICES  **PeiServices,  ///< A pointer to the PEI services
     OUT  UINT32            *AmountOfMemory  ///< Return data pointer for the amount of memory
  );

/**
 * @brief Read Service for the Memory Frequency of a channel
 * @retval EFI_SUCCESS Return value is valid
 * @retval EFI_UNSUPPORTED This function is not supported by this version of the driver
 */
typedef EFI_STATUS (EFIAPI * PEI_AMD_SOC_GET_MEMORY_FREQUENCY_INTERFACE) (
  IN      EFI_PEI_SERVICES  **PeiServices,  ///< A pointer to the PEI services
     OUT  UINT32            Channel,  ///<  A pointer to the channel to read
     OUT  UINT32            *Frequency  ///< Return data pointer for a channel frequency
  );

/**
 * @brief Read Service for the Memory Frequency of a channel
 * @retval EFI_SUCCESS Return value is valid
 * @retval EFI_UNSUPPORTED This function is not supported by this version of the driver
 */
typedef EFI_STATUS (EFIAPI * PEI_AMD_SOC_GET_UMA_INFO_INTERFACE) (
  IN      EFI_PEI_SERVICES  **PeiServices, ///< A pointer to the PEI services
     OUT  UINT32            *UmaInfo ///< Return data pointer for the UmaInfo
  );

/**
 * @brief Get the Memory Map Interface
 * @param[in] PeiServices - A pointer to the PEI services
 * @param[in] NumberOfHoles - The number of holes in the system memory map
 * @param[in] TopOfSystemMemory - Top of system memory
 * @param[in] MemHoleDescPtr - Pointer to an open ended array of MEMORY_HOLE_DESCRIPTORs.
 * @return EFI_SUCCESS           - Return value is valid
 * @return EFI_UNSUPPORTED       - This function is not supported by this version of the driver
 */
typedef EFI_STATUS (EFIAPI * PEI_GET_SYSTEM_MEMORY_MAP) (
  IN CONST EFI_PEI_SERVICES     **PeiServices,
  IN UINT32                     *NumberOfHoles,
  IN UINT64                     *TopOfSystemMemory,
  IN VOID                       **MemHoleDescPtr
 );

//----------------------------------------------------
//
//  Amd Memory Init Complete Ppi
//
//-------------------------------------------------------
extern EFI_GUID gAmdMemoryInitCompletePpiGuid; ///< Amd Memory Init Complete Ppi
extern EFI_GUID gAmdMemTerminationDataPpiGuid; ///< Amd Memory Termination Data PPI

/**
 * @brief Amd Memory UMA Info
 */
typedef struct _AMD_MEMORY_UMA_INFO {
  UMA_MODE UmaMode; ///<  Uma Mode
                       ///<  0 = None
                       ///<  1 = Specified
                       ///<  2 = Auto
  UINT32 UmaSize; ///<  The size of shared graphics dram (16-bits)
                     ///<  NV_UMA_Size[31:0]=Addr[47:16]
                     ///<
  UINT32 UmaBase; ///< The allocated Uma base address (32-bits)
} AMD_MEMORY_UMA_INFO;

//-----------------------------------------------------------------------------

#ifndef _MEM_STATUS_CODE_GENERAL_INFO_
/// @cond !BRH
#define _MEM_STATUS_CODE_GENERAL_INFO_
/// @endcond

/**
 * @brief Macro to get DIMM presence
 *
 */
#define GET_DIMM_PRESENCE_OF(This, DimmPresentMap, Socket, Die, Channel, Dimm) \
       (UINT8) ((DimmPresentMap[Socket * ((AMD_MEMORY_INIT_COMPLETE_PPI  *)This)->MaxDiePerSocket +\
                      Die] >> (Channel *  ((AMD_MEMORY_INIT_COMPLETE_PPI  *)This)->MaxDimmPerChannel + Dimm)) & 1)
#endif

/**
 * @brief Status union for boolean or value status
 */
typedef union {
  BOOLEAN Enabled;    ///< Status.Enabled - TRUE: Enabled.
  UINT16 Value;       ///< Status.Value - Configured value.
} STATUS;

/**
 * @brief Memory Config Info
 */
typedef struct _MEM_CFG_INFO {
   STATUS Status;        ///< Status: Enable/disable or configured status value
   UINT16 StatusCode;    ///< Status Code: MEM_STATUS_CODE_GENERAL_INFO
} MEM_CFG_INFO;

/**
 * @brief Amd Memory Init Complete Ppi
 */
typedef struct _AMD_MEMORY_INIT_COMPLETE_PPI {
  UINT32                 Revision;                                              ///< revision
  UINT16                 AmdBottomIo;                                           ///< Bottom IO
  UINT32                 AmdMemoryBelow4gb;                                     ///< Memory below 4G
  UINT32                 AmdMemoryAbove4gb;                                     ///< Memory above 4G
  UINT32                 AmdMemoryBelow1Tb;                                     ///< Memory below 1T
  UINT32                 AmdTotalMemorySize;                                    ///< Total Memory Size
  UINT32                 AmdMemoryFrequency;                                    ///< Memory Frequency
  DIMM_VOLTAGE           AmdMemoryVddIo;                                        ///< Memory Vddio
  VDDP_VDDR_VOLTAGE      AmdMemoryVddpVddr;                                     ///< Memory Vddp Vddr
  AMD_MEMORY_UMA_INFO    AmdGetUmaInfo;                                         ///< AMD UMA Info
  UINT32                 DdrMaxRate;                                            ///< DDR Max Rate
  UINT16                 MemPORMaxSpeed;                                        ///< DDR POR speed in Mhz on current DIMM configuration
  UINT16                 MaxDimmSpdCommonSpeed;                                 ///< Maximum DDR SPD common speed
  PEI_GET_SYSTEM_MEMORY_MAP        GetSystemMemoryMap;                          ///< System Memory Map @see PEI_GET_SYSTEM_MEMORY_MAP

  LIST_ENTRY SpdDataListHead; ///< SPD Data List

  //
  // Use below info. to be aware of max. number of Socket/Die/Channel/Dimm supported.
  // Those are filled in by specific memory PEIMs.
  //
  UINT8  MaxSocketSupported;          ///< Indicates max. socket supported
  UINT8  MaxDiePerSocket;             ///< Indicates max. die per socket
  UINT8  MaxChannelPerDie;            ///< Indicates max. channel per die
  UINT8  MaxDimmPerChannel;           ///< Indicates max. dimm per channel

  // Dynamic data
  // Status reporting stuff
  UINT32 *DimmPresentMap;             ///< DimmPresentMap[MaxSocketSupported * MaxDiePerSocket]
                                      ///< Bit[1:0] - Dimm[1:0] of Channel0, .. , Bit[15:14] - Dimm[1:0] of Channel7
  MEM_CFG_INFO *ChipselIntlv;         ///< Chipselect Interleaving Config Info: ChipselIntlv[MaxSocketSupported * MaxDiePerSocket * MaxChannelPerDie]
  MEM_CFG_INFO *DramEcc;              ///< DRAM ECC Config Info: DramEcc[MaxSocketSupported * MaxDiePerSocket]
  MEM_CFG_INFO *DramParity;           ///< DRAM Parity Info: DramParity[MaxSocketSupported * MaxDiePerSocket]
  MEM_CFG_INFO *AutoRefFineGranMode;  ///< Auto Refresh Fine Granularity Mode Config Info: AutoRefFineGranMode[MaxSocketSupported * MaxDiePerSocket]
  // Platform Tuning stuff

  /// Fixed data
  // Status reporting stuff
  MEM_CFG_INFO MbistTestEnable;       ///< Mbist Test Enable Config Info
  MEM_CFG_INFO MbistAggressorEnable;  ///< Mbist Aggressor Enable Config Info
  MEM_CFG_INFO MbistPerBitSlaveDieReport;   ///< Obsolete
  MEM_CFG_INFO DramTempControlledRefreshEn; ///< DramTempControlledRefreshEn Config Info
  MEM_CFG_INFO UserTimingMode;        ///< User Timing Mode Config Info
  MEM_CFG_INFO UserTimingValue;       ///< User Timing Value Config Info
  MEM_CFG_INFO MemBusFreqLimit;       ///< Memory Bus Frequency Limit Config Info
  MEM_CFG_INFO EnablePowerDown;       ///< Enable Power Down Config Info
  MEM_CFG_INFO DramDoubleRefreshRate; ///< DramDoubleRefreshRate Config Info
  MEM_CFG_INFO PmuTrainMode;          ///< PMU Training Mode Config Info
  MEM_CFG_INFO EccSymbolSize;         ///< ECC Symbol Size Config Info
  MEM_CFG_INFO UEccRetry;             ///< UECC Retry Config Info
  MEM_CFG_INFO IgnoreSpdChecksum;     ///< Ignore SPD Checksum Config Info
  MEM_CFG_INFO EnableBankGroupSwapAlt;///< EnableBankGroupSwapAlt Config Info
  MEM_CFG_INFO EnableBankGroupSwap;   ///< EnableBankGroupSwap Config Info
  MEM_CFG_INFO DdrRouteBalancedTee;   ///< DdrRouteBalancedTee Config Info
  MEM_CFG_INFO NvdimmPowerSource;     ///< Obsolete
  MEM_CFG_INFO OdtsCmdThrotEn;        ///< OdtsCmdThrotEn Config Info
  MEM_CFG_INFO OdtsCmdThrotCyc;       ///< OdtsCmdThrotCyc Config Info
} AMD_MEMORY_INIT_COMPLETE_PPI;

#define AMD_MEMORY_INIT_COMPLETE_REVISION   0x0001  ///< F17 Model00
#define AMD_MEMORY_INIT_COMPLETE_REV_0002   0x0002  ///< F17 Model10&20
#define AMD_MEMORY_INIT_COMPLETE_REV_0003   0x0003  ///< F17 Model00 w/ PPR support
#define AMD_MEMORY_INIT_COMPLETE_REV_0400   0x0400  ///< F17 Model30 and back compatible w/ F17 Model00

/**
 * @brief Amd Memory Channel Translation Table Ppi
 */
typedef struct _AMD_MEMORY_CHANNEL_XLAT_PPI {
  UINT32                          Revision;                                              ///< revision
  VOID                            *XLatTab;                                              ///< Translation Table
} AMD_MEMORY_CHANNEL_XLAT_PPI;

#define AMD_MEMORY_CHANNEL_XLAT_REVISION   0x01  ///< Current PPI revision

/**
 * @brief Amd Memory Platform Configuration Ppi
 *
 */
typedef struct _AMD_AGESA_MEM_PLATFORM_CONFIGURATION_PPI {
  UINT32                          Revision;                                              ///< revision
  PSO_ENTRY                       *PlatformMemoryConfiguration;                          ///< Platform Memory Configurattion
} AMD_AGESA_MEM_PLATFORM_CONFIGURATION_PPI;

/**
 * @brief PEI_AMD_PLATFORM_DIMM_SPD_PPI data structure definition.
 *
 */
typedef struct _PEI_AMD_PLATFORM_DIMM_SPD_PPI PEI_AMD_PLATFORM_DIMM_SPD_PPI;

/**
 * @brief Defines function prototype for PlatformDimmSpdRead
 *
 */
typedef
EFI_STATUS
(EFIAPI *PEI_AMD_DIMM_SPD_READ) (
  IN      EFI_PEI_SERVICES                    **PeiServices,  ///< Pointer to PeiServices
  IN struct _PEI_AMD_PLATFORM_DIMM_SPD_PPI    *This,          ///< Pointer to the PPI structure
  IN OUT  AGESA_READ_SPD_PARAMS               *SpdData        ///< SPD Data Buffer
  );

/**
 * @brief PEI_AMD_PLATFORM_DIMM_SPD_PPI data structure definition.
 *
 */
struct _PEI_AMD_PLATFORM_DIMM_SPD_PPI {          // See the Forward Declaration above
  UINT8                                       TotalNumberOfSocket;    ///< Total Number of Physical Socket.
  UINT8                                       TotalNumberOfDimms;     ///< Total Number of DIMMS
  PEI_AMD_DIMM_SPD_READ                       PlatformDimmSpdRead;    ///< Function to be called
};

extern EFI_GUID gAmdMemoryBeforeDramInitPpiGuid;  ///< Memory Before DRAM Init PPI

/**
 * @brief Memory Before DRAM Init Interface
 * @param PeiServices A pointer to the PEI services
 * @retval EFI_SUCCESS Return value is valid
 * @retval EFI_UNSUPPORTED This function is not supported by this version of the driver
 */
typedef EFI_STATUS (EFIAPI * PEI_AMD_MEMORY_BEFORE_DRAM_INIT_INTERFACE  ) (
  IN      EFI_PEI_SERVICES  **PeiServices
  );


/**
 * @brief Amd Memory Before Dram Init Ppi
 *
 */
typedef struct _PEI_AMD_MEMORY_BEFORE_DRAM_INIT_PPI {
  UINT32                          Revision;                                              ///< revision
} PEI_BEFORE_DRAM_INIT_COMPLETE_PPI;

#define AMD_MEMORY_BEFORE_DRAM_INIT_REVISION   0x00  ///< Current PPI revision

extern EFI_GUID gAmdMemoryAfterDramInitPpiGuid;  ///< Amd Memory After Dram Init Ppi

/**
 * @brief Amd Memory After Dram Init Ppi
 *
 */
typedef struct _PEI_AMD_MEMORY_AFTER_DRAM_INIT_PPI {
  UINT32                          Revision;                                              ///< revision
} PEI_AFTER_DRAM_INIT_COMPLETE_PPI;

#define AMD_MEMORY_AFTER_DRAM_INIT_REVISION   0x00  ///< Current PPI revision

extern EFI_GUID gAmdMemoryInitializeAgesaMemoryPpiGuid;  ///< Amd Memory Init Ppi

/**
 * @brief Amd Memory Init Ppi
 *
 */
typedef struct _PEI_INITIALIZE_AGESA_MEMORY_PPI {
  UINT32                          Revision;                                              ///< revision
} PEI_INITIALIZE_AGESA_MEMORY_PPI;

#define AMD_MEMORY_INITIALIZE_AGESA_MEMORY_REVISION   0x00  ///< Current PPI revision

extern EFI_GUID gAmdMemoryFamilyServicesPpiGuid;  ///< Amd Memory Family Services PPI

/**
 * @brief Amd Memory Family Services PPI
 *
 */
typedef struct _PEI_MEMORY_FAMILY_SERVICE_PPI {
  UINT32                          Revision;                                              ///< revision
  PEI_AMD_SOC_GET_MEMORY_ABOVE_4GB_INTERFACE   AmdGetMemoryAbove4gbFamilyService;        ///< Service to get memory above 4G
  PEI_AMD_SOC_GET_MEMORY_BELOW_4GB_INTERFACE   AmdGetMemoryBelow4gbFamilyService;        ///< Service to get memory below 4G
  PEI_AMD_SOC_GET_MEMORY_BELOW_1TB_INTERFACE    AmdGetMemoryBelow1TbFamilyService;       ///< Service to get memory below 1T
  PEI_AMD_SOC_GET_MEMORY_BOTTOM_IO_INTERFACE    AmdGetBottomIo1TbFamilyService;          ///< Service to get BottomIo
  PEI_AMD_SOC_GET_TOTAL_MEMORY_SIZE_INTERFACE   AmdGetTotalMemorySize4gbFamilyService;   ///< Service to get total memory
} PEI_MEMORY_FAMILY_SERVICE_PPI;

#define AMD_MEMORY_FAMILY_SERVICE_REVISION   0x00  ///< Current PPI revision

//
// GUID definition
//

extern EFI_GUID gAmdMemoryPoolPointerTablePpiGuid;  ///< Pool Pointer Table PPI

/**
 * @brief Definition for AGESA Buffer Handles
 *
 */
typedef enum {
  AMD_INIT_HANDLE = 0,                        ///< First Handle pointer
  AMD_CRAT_STRUCTURE_POOL_PTR,                ///< Pointer to CRAT Table structure
  AMD_DATA_EYES_POOL_PTR,                     ///< Pointer to Data eyes table
  AMD_DATA_EYE_WORK_AREA_POOL_PTR,            ///< Pointer to Data eye work area
  AMD_DMI_DEV_INFO_STRUCTURE_POOL_PTR,        ///< Pointer to Dmi Dev Info Struncture
  AMD_DMI_DEV_INFO_DDR4_STRUCTURE_POOL_PTR,   ///< Pointer to Dmi Dev Info Struncture for DDR4
  AMD_NB_STRUCTURE_POOL_PTR,                  ///< Pointer to NB Structure
  AMD_2D_RD_WR_RIM_HANDLES_POOL_PTR,          ///< Pointer to 2D RD WR RIM Handle
  AMD_2D_RD_WR_POOL_PTR,                      ///< Pointer to 2D RD WR structure
  AMD_DEVICE_BLOCK_HEADER_POOL_PTR,           ///< Pointer to Device Block Header
  AMD_SPECIAL_CASE_REG_HEADER_POOL_PTR,       ///< Pointer to Special Case Register Heater
  AMD_MEM_DATA_STRUCT_POOL_PTR,               ///< Pointer to Mem Data Struct
  AMD_NB_BLOCK_POOL_PTR,                      ///< Pointer to NB Block
  AMD_S3_MEM_NB_BLOCK_POOL_PTR,               ///< Pointer to S3 Mem NB Block
  AMD_MEM_DIE_STRUCT_POOL_PTR,                ///< Pointer to Mem die struct
  AMD_S3_DATA_HANDLE_STRUCT_POOL_PTR,         ///< Pointer to S3 Data handle struct
  AMD_S3_SAVE_HANDLE_STRUCT_POOL_PTR,         ///< Pointer to S3 Save handle struct
  AMD_SKIP_MEM_S3_SAVE_STRUCT_POOL_PTR,       ///< Pointer to S3 Save struct
  AMD_SKIP_MEM_S3_NB_HANDLE_POOL_PTR,         ///< Pointer to S3 NB Handle
  AMD_SPD_DAT_STRUCTURE_POOL_PTR,             ///< Pointer to SPD Dat structure
  AMD_UMA_INFO_HANDLE_POOL_PTR,               ///< Pointer to UMA Info Handle
  AMD_MEM_TRAIN_BUFFER_POOL_PTR,              ///< Pointer to Mem train buffer
  AMD_NB_REG_TABLE_POOL_PTR,                  ///< Pointer to NB reg table
  AMD_UMA_INFO_POOL_PTR,                      ///< Pointer to UMC Info
  AMD_GNB_ACP_ENGINE_POOL_PTR,                ///< Pointer to GNB ACP Engine
  AMD_MEM_PMU_SRAM_MSG_BLOCK_POOL_PTR,        ///< Pointer to PMU SRAM MSG Block
  AMD_TRN_DATA_POOL_PTR,                      ///< Pointer to TRN Data
  AMD_MEM_2D_RDDQS_RIM_POOL_PTR,              ///< Pointer to  2D RDDQS RIM
  AMD_MEM_AUTO_HANDLER_POOL_PTR,              ///< Pointer to Mem Auto Handler
  AMD_FINAL_POOL_PTR                          ///< Last Handle Pointer
} AGESA_BUFFER_HANDLE;

/**
 * @brief Pool Pointer Table PPI
 *
 */
typedef struct _AMD_MEM_POOL_PTR_TABLE_PPI {
  UINTN               Revision;               ///< Revision Number
  UINT32              *PtrTable[AMD_FINAL_POOL_PTR];             ///< The Point of Pointer Table List
} AMD_MEM_POOL_PTR_TABLE_PPI;

#define AMD_MEM_POOL_PTR_TABLE_REVISION   0x00  ///< Current PPI revision

extern EFI_GUID gAmdMemoryTechServicesPpiGuid;  ///< AMD memory tech Services PPI

/**
 * @brief AMD memory tech Services PPI
 *
 */
typedef EFI_STATUS (EFIAPI * PEI_AMD_MEM_TECH_SERVICES_INTERFACE  ) (
  );

/**
 * @brief AMD memory tech Services PPI
 *
 */
typedef struct _AMD_MEM_TECH_SERVICES_PPI {
  UINTN               Revision;                                   ///< Revision Number
  PEI_AMD_MEM_TECH_SERVICES_INTERFACE   AmdDimmPresenceService;   ///< Service to detect DIMM presence
} AMD_MEM_TECH_SERVICES_PPI;

#define AMD_MEM_TECH_SERVICES_REVISION   0x00  ///< Current PPI revision

extern EFI_GUID gAmdMemoryTecnologyPpiGuid;  ///< AMD memory tech PPI

/**
 * @brief AMD memory tech PPI
 *
 */
typedef struct _PEI_MEMORY_TECHNOLOGY_PPI {
  UINTN               Revision;               ///< Revision Number
} PEI_MEMORY_TECHNOLOGY_PPI;

#define AMD_MEMORY_TECHNOLOGY_REVISION   0x00  ///< Current PPI revision

extern EFI_GUID gAmdMemoryAgesaReadSpdPpiGuid;  ///< AMD memory Read SPD PPI

typedef EFI_STATUS (EFIAPI * PEI_AMD_MEMORY_AGESA_READ_SPD_INTERFACE  ) (
  );

/**
 * @brief AMD memory Read SPD PPI
 *
 */
typedef struct _PEI_MEMORY_AGESA_READ_SPD_PPI {
  UINTN               Revision;                             ///< Revision Number
  PEI_AMD_MEMORY_AGESA_READ_SPD_INTERFACE   AgesaReadSpd;   ///< Service to read SPD
} PEI_MEMORY_AGESA_READ_SPD_PPI;

#define AMD_MEMORY_AGESA_READ_SPD_REVISION   0x00  ///< Current PPI revision

/**
 * @brief Defines function prototype to install Memory feature block
 *
 */
typedef
EFI_STATUS (*AMD_MEM_FEAT_ISNTALL_INTERFACE) (
  IN OUT     VOID*      MemFeatBlock                      ///< Memory feature block
  );

/**
 * @brief AMD memory Install Feature PPI
 *
 */
typedef struct _AMD_AGESA_MEM_FEAT_INSTALL_PPI {
  UINT32                                Revision;                   ///< revision
  AMD_MEM_FEAT_ISNTALL_INTERFACE        Install;                    ///< Service to install memory feature block
} AMD_AGESA_MEM_FEAT_INSTALL_PPI;

#define AMD_MEMORY_FEAT_INSTALL_REVISION   0x00  ///< Current PPI revision

#endif

