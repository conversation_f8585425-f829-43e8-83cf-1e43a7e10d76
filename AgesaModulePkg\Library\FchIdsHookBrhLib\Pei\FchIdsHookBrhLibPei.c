/*****************************************************************************
 *
 * Copyright (C) 2008-2024 Advanced Micro Devices, Inc. All rights reserved.
 *
 ******************************************************************************
 */
#include <Library/AmdBaseLib.h>
#include <Library/IdsLib.h>
#include <Library/AmdIdsHookLib.h>
#include <IdsHookId.h>
#include <IdsNvIdBRH.h>
#include <IdsNvDefBRH.h>
#include <Filecode.h>
#include <FchPlatform.h>

#define FILECODE UNASSIGNED_FILE_FILECODE

/*----------------------------------------------------------------------------------------
 *                          L O C A L    F U N C T I O N S
 *----------------------------------------------------------------------------------------
 */
IDS_HOOK_STATUS
CmnHookFuncFchBrhPeiInitReset (
  HOOK_ID   HookId,
  VOID     *Handle,
  VOID     *Data
  )
{
  UINT8      PcdData8;
  UINT32     PcdData32;
  UINT32     PcdData32Ex;
  UINT64     IdsNvValue;

  IDS_HDT_CONSOLE (FCH_TRACE, "CmnHookFuncFchBrhPeiInitReset FCH Reset Options Update\n");

  // SATA
  // gEfiAmdAgesaPkgTokenSpaceGuid.PcdSataEnable2|0xFF|UINT8|0x000FC031
  // Bits 0-3: Socket 0, SATA controllers 0-3.
  // Bits 4-7: Socket 1, SATA controllers 0-3.
  //
  PcdData8 = PcdGet8 (PcdSataEnable2);

  IDS_NV_READ_SKIP (IDSNVID_DBG_FCH_SATA0_ENABLE, &IdsNvValue) {
    switch (IdsNvValue) {
    case IDSOPT_DBG_FCH_SATA0_ENABLE_DISABLED:
      PcdData8 &= ~(UINT8)BIT0;
      break;
    case IDSOPT_DBG_FCH_SATA0_ENABLE_ENABLED:
      PcdData8 |= BIT0;
      break;
    case IDSOPT_DBG_FCH_SATA0_ENABLE_AUTO:
      break;
    default:
      ASSERT (FALSE);
      break;
    }
  }

  IDS_NV_READ_SKIP (IDSNVID_DBG_FCH_SATA1_ENABLE, &IdsNvValue) {
    switch (IdsNvValue) {
    case IDSOPT_DBG_FCH_SATA1_ENABLE_DISABLED:
      PcdData8 &= ~(UINT8)BIT1;
      break;
    case IDSOPT_DBG_FCH_SATA1_ENABLE_ENABLED:
      PcdData8 |= BIT1;
      break;
    case IDSOPT_DBG_FCH_SATA1_ENABLE_AUTO:
      break;
    default:
      ASSERT (FALSE);
      break;
    }
  }

  IDS_NV_READ_SKIP (IDSNVID_DBG_FCH_SATA2_ENABLE, &IdsNvValue) {
    switch (IdsNvValue) {
    case IDSOPT_DBG_FCH_SATA2_ENABLE_DISABLED:
      PcdData8 &= ~(UINT8)BIT2;
      break;
    case IDSOPT_DBG_FCH_SATA2_ENABLE_ENABLED:
      PcdData8 |= BIT2;
      break;
    case IDSOPT_DBG_FCH_SATA2_ENABLE_AUTO:
      break;
    default:
      ASSERT (FALSE);
      break;
    }
  }

  IDS_NV_READ_SKIP (IDSNVID_DBG_FCH_SATA3_ENABLE, &IdsNvValue) {
    switch (IdsNvValue) {
    case IDSOPT_DBG_FCH_SATA3_ENABLE_DISABLED:
      PcdData8 &= ~(UINT8)BIT3;
      break;
    case IDSOPT_DBG_FCH_SATA3_ENABLE_ENABLED:
      PcdData8 |= BIT3;
      break;
    case IDSOPT_DBG_FCH_SATA3_ENABLE_AUTO:
      break;
    default:
      ASSERT (FALSE);
      break;
    }
  }

  IDS_NV_READ_SKIP (IDSNVID_DBG_FCH_SATA4_ENABLE, &IdsNvValue) {
    switch (IdsNvValue) {
    case IDSOPT_DBG_FCH_SATA4_ENABLE_DISABLED:
      PcdData8 &= ~(UINT8)BIT4;
      break;
    case IDSOPT_DBG_FCH_SATA4_ENABLE_ENABLED:
      PcdData8 |= BIT4;
      break;
    case IDSOPT_DBG_FCH_SATA4_ENABLE_AUTO:
      break;
    default:
      ASSERT (FALSE);
      break;
    }
  }

  IDS_NV_READ_SKIP (IDSNVID_DBG_FCH_SATA5_ENABLE, &IdsNvValue) {
    switch (IdsNvValue) {
    case IDSOPT_DBG_FCH_SATA5_ENABLE_DISABLED:
      PcdData8 &= ~(UINT8)BIT5;
      break;
    case IDSOPT_DBG_FCH_SATA5_ENABLE_ENABLED:
      PcdData8 |= BIT5;
      break;
    case IDSOPT_DBG_FCH_SATA5_ENABLE_AUTO:
      break;
    default:
      ASSERT (FALSE);
      break;
    }
  }

  IDS_NV_READ_SKIP (IDSNVID_DBG_FCH_SATA6_ENABLE, &IdsNvValue) {
    switch (IdsNvValue) {
    case IDSOPT_DBG_FCH_SATA6_ENABLE_DISABLED:
      PcdData8 &= ~(UINT8)BIT6;
      break;
    case IDSOPT_DBG_FCH_SATA6_ENABLE_ENABLED:
      PcdData8 |= BIT6;
      break;
    case IDSOPT_DBG_FCH_SATA6_ENABLE_AUTO:
      break;
    default:
      ASSERT (FALSE);
      break;
    }
  }

  IDS_NV_READ_SKIP (IDSNVID_DBG_FCH_SATA7_ENABLE, &IdsNvValue) {
    switch (IdsNvValue) {
    case IDSOPT_DBG_FCH_SATA7_ENABLE_DISABLED:
      PcdData8 &= ~(UINT8)BIT7;
      break;
    case IDSOPT_DBG_FCH_SATA7_ENABLE_ENABLED:
      PcdData8 |= BIT7;
      break;
    case IDSOPT_DBG_FCH_SATA7_ENABLE_AUTO:
      break;
    default:
      ASSERT (FALSE);
      break;
    }
  }

  IDS_NV_READ_SKIP (IDSNVID_CMN_FCH_SATA_ENABLE, &IdsNvValue) {
    if (IdsNvValue != IDSOPT_CMN_FCH_SATA_ENABLE_AUTO) {
      if (IdsNvValue == IDSOPT_CMN_FCH_SATA_ENABLE_DISABLED) {
        FCH_PCDSETBOOL (PcdSataEnable, FALSE);
        PcdData8 = 0;
      } else {
        FCH_PCDSETBOOL (PcdSataEnable, TRUE);
      }
    }
  }

  FCH_PCDSET8 (PcdSataEnable2, PcdData8);

  // SATA SGPIO

  PcdData8 = PcdGet8 (PcdSataSgpioMultiDieEnable);

  IDS_NV_READ_SKIP (IDSNVID_DBG_FCH_SATA_SGPIO0, &IdsNvValue) {
    switch (IdsNvValue) {
    case IDSOPT_DBG_FCH_SATA_SGPIO0_DISABLED:
      PcdData8 &= ~(UINT8)BIT0;
      break;
    case IDSOPT_DBG_FCH_SATA_SGPIO0_ENABLED:
      PcdData8 |= BIT0;
      break;
    case IDSOPT_DBG_FCH_SATA_SGPIO0_AUTO:
      break;
    default:
      ASSERT (FALSE);
      break;
    }
  }

  IDS_NV_READ_SKIP (IDSNVID_DBG_FCH_SATA_MCM_DIE1_SGPIO0, &IdsNvValue) {
    switch (IdsNvValue) {
    case IDSOPT_DBG_FCH_SATA_MCM_DIE1_SGPIO0_DISABLED:
      PcdData8 &= ~(UINT8)BIT1;
      break;
    case IDSOPT_DBG_FCH_SATA_MCM_DIE1_SGPIO0_ENABLED:
      PcdData8 |= BIT1;
      break;
    case IDSOPT_DBG_FCH_SATA_MCM_DIE1_SGPIO0_AUTO:
      break;
    default:
      ASSERT (FALSE);
      break;
    }
  }

  IDS_NV_READ_SKIP (IDSNVID_DBG_FCH_SATA_MCM_DIE2_SGPIO0, &IdsNvValue) {
    switch (IdsNvValue) {
    case IDSOPT_DBG_FCH_SATA_MCM_DIE2_SGPIO0_DISABLED:
      PcdData8 &= ~(UINT8)BIT2;
      break;
    case IDSOPT_DBG_FCH_SATA_MCM_DIE2_SGPIO0_ENABLED:
      PcdData8 |= BIT2;
      break;
    case IDSOPT_DBG_FCH_SATA_MCM_DIE2_SGPIO0_AUTO:
      break;
    default:
      ASSERT (FALSE);
      break;
    }
  }

  IDS_NV_READ_SKIP (IDSNVID_DBG_FCH_SATA_MCM_DIE3_SGPIO0, &IdsNvValue) {
    switch (IdsNvValue) {
    case IDSOPT_DBG_FCH_SATA_MCM_DIE3_SGPIO0_DISABLED:
      PcdData8 &= ~(UINT8)BIT3;
      break;
    case IDSOPT_DBG_FCH_SATA_MCM_DIE3_SGPIO0_ENABLED:
      PcdData8 |= BIT3;
      break;
    case IDSOPT_DBG_FCH_SATA_MCM_DIE3_SGPIO0_AUTO:
      break;
    default:
      ASSERT (FALSE);
      break;
    }
  }

  IDS_NV_READ_SKIP (IDSNVID_DBG_FCH_SATA_MCM_DIE4_SGPIO0, &IdsNvValue) {
    switch (IdsNvValue) {
    case IDSOPT_DBG_FCH_SATA_MCM_DIE4_SGPIO0_DISABLED:
      PcdData8 &= ~(UINT8)BIT4;
      break;
    case IDSOPT_DBG_FCH_SATA_MCM_DIE4_SGPIO0_ENABLED:
      PcdData8 |= BIT4;
      break;
    case IDSOPT_DBG_FCH_SATA_MCM_DIE4_SGPIO0_AUTO:
      break;
    default:
      ASSERT (FALSE);
      break;
    }
  }

  IDS_NV_READ_SKIP (IDSNVID_DBG_FCH_SATA_MCM_DIE5_SGPIO0, &IdsNvValue) {
    switch (IdsNvValue) {
    case IDSOPT_DBG_FCH_SATA_MCM_DIE5_SGPIO0_DISABLED:
      PcdData8 &= ~(UINT8)BIT5;
      break;
    case IDSOPT_DBG_FCH_SATA_MCM_DIE5_SGPIO0_ENABLED:
      PcdData8 |= BIT5;
      break;
    case IDSOPT_DBG_FCH_SATA_MCM_DIE5_SGPIO0_AUTO:
      break;
    default:
      ASSERT (FALSE);
      break;
    }
  }

  IDS_NV_READ_SKIP (IDSNVID_DBG_FCH_SATA_MCM_DIE6_SGPIO0, &IdsNvValue) {
    switch (IdsNvValue) {
    case IDSOPT_DBG_FCH_SATA_MCM_DIE6_SGPIO0_DISABLED:
      PcdData8 &= ~(UINT8)BIT6;
      break;
    case IDSOPT_DBG_FCH_SATA_MCM_DIE6_SGPIO0_ENABLED:
      PcdData8 |= BIT6;
      break;
    case IDSOPT_DBG_FCH_SATA_MCM_DIE6_SGPIO0_AUTO:
      break;
    default:
      ASSERT (FALSE);
      break;
    }
  }

  IDS_NV_READ_SKIP (IDSNVID_DBG_FCH_SATA_MCM_DIE7_SGPIO0, &IdsNvValue) {
    switch (IdsNvValue) {
    case IDSOPT_DBG_FCH_SATA_MCM_DIE7_SGPIO0_DISABLED:
      PcdData8 &= ~(UINT8)BIT7;
      break;
    case IDSOPT_DBG_FCH_SATA_MCM_DIE7_SGPIO0_ENABLED:
      PcdData8 |= BIT7;
      break;
    case IDSOPT_DBG_FCH_SATA_MCM_DIE7_SGPIO0_AUTO:
      break;
    default:
      ASSERT (FALSE);
      break;
    }
  }

  FCH_PCDSET8 (PcdSataSgpioMultiDieEnable, PcdData8);

  // I2C/I3C

  PcdData32   = PcdGet32 (FchRTDeviceEnableMap);
  PcdData32Ex = PcdGet32 (FchRTDeviceEnableMapEx);

  IDS_NV_READ_SKIP (IDSNVID_CMN_FCH_I3_C0_CONFIG, &IdsNvValue) {
    switch (IdsNvValue) {
    case IDSOPT_CMN_FCH_I3_C0_CONFIG_I3CENABLED:                 // Select I3C controller
      PcdData32 |= BIT21;
      PcdData32 &= ~BIT5;
      PcdData32Ex |= BIT0;
      // IDS_NV_READ_SKIP (IDSNVID_CMN_FCH_I3_C0_MODE, &IdsNvValue)
      // switch (IdsNvValue) {
      //   case IDSOPT_CMN_FCH_I3_C0_MODE_I3C:
      //     PcdData32Ex |= BIT0;
      //     break;
      //   default:
      //     PcdData32Ex &= ~BIT0;
      //     break;
      // }
      break;
    case IDSOPT_CMN_FCH_I3_C0_CONFIG_I2CENABLED:                 // Select I2C controller
    case IDSOPT_CMN_FCH_I3_C0_CONFIG_AUTO:                       // Select Auto
      PcdData32 &= ~BIT21;
      PcdData32 |= BIT5;
      break;
    case IDSOPT_CMN_FCH_I3_C0_CONFIG_BOTHDISABLED:               // Select Disable All
    default:
      PcdData32 &= ~(BIT21 + BIT5);
      break;
    }
  }

  IDS_NV_READ_SKIP (IDSNVID_CMN_FCH_I3_C1_CONFIG, &IdsNvValue) {
    switch (IdsNvValue) {
    case IDSOPT_CMN_FCH_I3_C1_CONFIG_I3CENABLED:                 // Select I3C controller
      PcdData32 |= BIT13;
      PcdData32 &= ~BIT6;
      PcdData32Ex |= BIT1;
      // IDS_NV_READ_SKIP (IDSNVID_CMN_FCH_I3_C1_MODE, &IdsNvValue)
      // switch (IdsNvValue) {
      //   case IDSOPT_CMN_FCH_I3_C1_MODE_I3C:
      //     PcdData32Ex |= BIT1;
      //     break;
      //   default:
      //     PcdData32Ex &= ~BIT1;
      //     break;
      // }
      break;
    case IDSOPT_CMN_FCH_I3_C1_CONFIG_I2CENABLED:                 // Select I2C controller
    case IDSOPT_CMN_FCH_I3_C1_CONFIG_AUTO:                       // Select Autos
      PcdData32 &= ~BIT13;
      PcdData32 |= BIT6;
      break;
    case IDSOPT_CMN_FCH_I3_C1_CONFIG_BOTHDISABLED:               // Select Disable All
    default:
      PcdData32 &= ~(BIT13 + BIT6);
      break;
    }
  }

  IDS_NV_READ_SKIP (IDSNVID_CMN_FCH_I3_C2_CONFIG, &IdsNvValue) {
    switch (IdsNvValue) {
    case IDSOPT_CMN_FCH_I3_C2_CONFIG_I3CENABLED:                 // Select I3C controller
      PcdData32 |= BIT14;
      PcdData32 &= ~BIT7;
      PcdData32Ex |= BIT2;
      // IDS_NV_READ_SKIP (IDSNVID_CMN_FCH_I3_C2_MODE, &IdsNvValue)
      // switch (IdsNvValue) {
      //   case IDSOPT_CMN_FCH_I3_C2_MODE_I3C:
      //     PcdData32Ex |= BIT2;
      //     break;
      //   default:
      //     PcdData32Ex &= ~BIT2;
      //     break;
      // }
      break;
    case IDSOPT_CMN_FCH_I3_C2_CONFIG_I2CENABLED:                 // Select I2C controller
    case IDSOPT_CMN_FCH_I3_C2_CONFIG_AUTO:                       // Select Auto
      PcdData32 &= ~BIT14;
      PcdData32 |= BIT7;
      break;
    case IDSOPT_CMN_FCH_I3_C2_CONFIG_BOTHDISABLED:               // Select Disable All
    default:
      PcdData32 &= ~(BIT14 + BIT7);
      break;
    }
  }

  IDS_NV_READ_SKIP (IDSNVID_CMN_FCH_I3_C3_CONFIG, &IdsNvValue) {
    switch (IdsNvValue) {
    case IDSOPT_CMN_FCH_I3_C3_CONFIG_I3CENABLED:                 // Select I3C controller
      PcdData32 |= BIT15;
      PcdData32 &= ~BIT8;
      PcdData32Ex |= BIT3;
      // IDS_NV_READ_SKIP (IDSNVID_CMN_FCH_I3_C3_MODE, &IdsNvValue)
      // switch (IdsNvValue) {
      //   case IDSOPT_CMN_FCH_I3_C3_MODE_I3C:
      //     PcdData32Ex |= BIT3;
      //     break;
      //   default:
      //     PcdData32Ex &= ~BIT3;
      //     break;
      // }
      break;
    case IDSOPT_CMN_FCH_I3_C3_CONFIG_I2CENABLED:                 // Select I2C controller
    case IDSOPT_CMN_FCH_I3_C3_CONFIG_AUTO:                       // Select Auto
      PcdData32 &= ~BIT15;
      PcdData32 |= BIT8;
      break;
    case IDSOPT_CMN_FCH_I3_C3_CONFIG_BOTHDISABLED:               // Select Disable All
    default:
      PcdData32 &= ~(BIT15 + BIT8);
      break;
    }
  }

  IDS_NV_READ_SKIP (IDSNVID_CMN_FCH_I2_C4_CONFIG, &IdsNvValue) {
    if (IdsNvValue != IDSOPT_CMN_FCH_I2_C4_CONFIG_AUTO) { // Auto
      if (IdsNvValue == IDSOPT_CMN_FCH_I2_C4_CONFIG_ENABLED) {
        PcdData32 |= BIT9;
      } else {
        PcdData32 &= ~ BIT9;
      }
    }
  }

  IDS_NV_READ_SKIP (IDSNVID_CMN_FCH_I2_C5_CONFIG, &IdsNvValue) {
    if (IdsNvValue != IDSOPT_CMN_FCH_I2_C5_CONFIG_AUTO) { // Auto
      if (IdsNvValue == IDSOPT_CMN_FCH_I2_C5_CONFIG_ENABLED) {
        PcdData32 |= BIT10;
      } else {
        PcdData32 &= ~ BIT10;
      }
    }
  }

  FCH_PCDSET32 (FchRTDeviceEnableMap, PcdData32);
  FCH_PCDSET32 (FchRTDeviceEnableMapEx, PcdData32Ex);

  return IDS_HOOK_SUCCESS;
}

#ifndef IDS_HOOK_INTERNAL_SUPPORT
  #define FCH_BRH_IDS_HOOKS_INT_PEI
#else
  #include "Internal/FchIdsHookBrhLibIntPei.h"
#endif

IDS_HOOK_ELEMENT FchBrhIdsHooksPei[] = {
  {
    IDS_HOOK_FCH_INIT_RESET,
    &CmnHookFuncFchBrhPeiInitReset
  },
  FCH_BRH_IDS_HOOKS_INT_PEI
  IDS_HOOKS_END
};

IDS_HOOK_TABLE FchBrhIdsHookTablePei = {
  IDS_HOOK_TABLE_HEADER_REV1_DATA,
  FchBrhIdsHooksPei
};


AGESA_STATUS
GetIdsHookTable (
  IDS_HOOK_TABLE **IdsHookTable
  )
{
  *IdsHookTable = &FchBrhIdsHookTablePei;
  return AGESA_SUCCESS;
}

