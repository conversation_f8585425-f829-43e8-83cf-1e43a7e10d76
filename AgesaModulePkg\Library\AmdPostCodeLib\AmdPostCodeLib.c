/*****************************************************************************
 * Copyright (C) 2008-2024 Advanced Micro Devices, Inc. All rights reserved.
 *
*****************************************************************************
*/

 /*----------------------------------------------------------------------------------------
 *                             M O D U L E S    U S E D
 *----------------------------------------------------------------------------------------
 */
#include <Uefi.h>
#include <Library/AmdBaseLib.h>
#include <Library/IoLib.h>
#include <Library/PcdLib.h>

/**
  Light version IO write with less depx

  @param[in]  Address, IO address
  @param[in]  Value, Value to be writen to the address

**/
STATIC
VOID
IoWriteWidth (
  IN        ACCESS_WIDTH AccessWidth,
  UINT32    Address,
  IN        VOID *Value
  )
{
  switch (AccessWidth) {
  case AccessWidth8:
  case AccessS3SaveWidth8:
    IoWrite8 (Address, *(UINT8 *)Value);
    break;
  case AccessWidth16:
  case AccessS3SaveWidth16:
    IoWrite16 (Address, *(UINT16 *)Value);
    break;
  case AccessWidth32:
  case AccessS3SaveWidth32:
    IoWrite32 (Address, *(UINT32 *)Value);
    break;
  default:
    IoWrite32 (Address, *(UINT32 *)Value);
  }

}


/**
 * @brief Output Postcode to IO Port PcdIdsDebugPort specified, output to NBCFG_SCRATCH_0 in emulation case
 *
 * @param[in] AccessWidth   Access width
 * @param[in] Value         Pointer to data
 * @return VOID
 */
VOID
LibAmdPostCode (
  IN       ACCESS_WIDTH AccessWidth,
  IN       VOID *Value
  )
{
  IoWriteWidth (AccessWidth, PcdGet16 (PcdIdsDebugPort), Value);
}

