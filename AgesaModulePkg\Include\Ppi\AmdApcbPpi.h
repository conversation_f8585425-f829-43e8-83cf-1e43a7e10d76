/*****************************************************************************
 * Copyright (C) 2024 Advanced Micro Devices, Inc. All rights reserved.
 *
*****************************************************************************
*/

#ifndef _AMD_APCB_PPI_H_
#define _AMD_APCB_PPI_H_

/*----------------------------------------------------------------------------------------
 *                 D E F I N I T I O N S     A N D     M A C R O S
 *----------------------------------------------------------------------------------------
 */

#define APCB_PPI_VERSION_3_0    3

typedef struct _AMD_APCB_SERVICE_PPI AMD_APCB_SERVICE_PPI;

/**
 * @brief Function used to get the value of an APCB BOOL token
 *
 * @param[in]     This                      Point to AMD_APCB_SERVICE_PPI itself
 * @param[out]    Purpose                   Point to purpose
 * @param[in]     TokenId                   Token id
 * @param[out]    bValue                    Point to boolean value
 *
 * @retval        EFI_SUCCESS               Get APCB value successfully
 * @retval        EFI_NOT_FOUND             Cannot find the APCB token
 * @retval        EFI_ABORTED               Reject Access due to SMM locked
 */
typedef
EFI_STATUS
(EFIAPI *FP_GET_TOKEN_BOOL) (
  IN       AMD_APCB_SERVICE_PPI *This,
  IN OUT   UINT8                *Purpose,
  IN       UINT32               TokenId,
  IN OUT   BOOLEAN              *bValue
  );

/**
 * @brief Function used to get the value of an APCB UINT8 token
 *
 * @param[in]     This                      Point to AMD_APCB_SERVICE_PPI itself
 * @param[out]    Purpose                   Point to purpose
 * @param[in]     TokenId                   Token id
 * @param[out]    Value8                    Point to value
 *
 * @retval        EFI_SUCCESS               Get APCB value successfully
 * @retval        EFI_NOT_FOUND             Cannot find the APCB token
 * @retval        EFI_ABORTED               Reject Access due to SMM locked
 */
typedef
EFI_STATUS
(EFIAPI *FP_GET_TOKEN_8) (
  IN       AMD_APCB_SERVICE_PPI *This,
  IN OUT   UINT8                *Purpose,
  IN       UINT32               TokenId,
  IN OUT   UINT8                *Value8
  );

/**
 * @brief Function used to get the value of an APCB UINT16 toke
 *
 * @param[in]     This                      Point to AMD_APCB_SERVICE_PPI itself
 * @param[out]    Purpose                   Point to purpose
 * @param[in]     TokenId                   Token id
 * @param[out]    Value8                    Point to value
 *
 * @retval        EFI_SUCCESS               Get APCB value successfully
 * @retval        EFI_NOT_FOUND             Cannot find the APCB token
 * @retval        EFI_ABORTED               Reject Access due to SMM locked
 */
typedef
EFI_STATUS
(EFIAPI *FP_GET_TOKEN_16) (
  IN       AMD_APCB_SERVICE_PPI *This,
  IN OUT   UINT8                *Purpose,
  IN       UINT32               TokenId,
  IN OUT   UINT16               *Value16
  );

/**
 * @brief Function used to get the value of an APCB UINT32 token
 *
 * @param[in]     This                      Point to AMD_APCB_SERVICE_PPI itself
 * @param[out]    Purpose                   Point to purpose
 * @param[in]     TokenId                   Token id
 * @param[out]    Value8                    Point to value
 *
 * @retval        EFI_SUCCESS               Get APCB value successfully
 * @retval        EFI_NOT_FOUND             Cannot find the APCB token
 * @retval        EFI_ABORTED               Reject Access due to SMM locked
 */
typedef
EFI_STATUS
(EFIAPI *FP_GET_TOKEN_32) (
  IN       AMD_APCB_SERVICE_PPI *This,
  IN OUT   UINT8                *Purpose,
  IN       UINT32               TokenId,
  IN OUT   UINT32               *Value32
  );

/**
 * @brief Function used to retrive the data of a specified type
 *
 * @param[in]     This                      Point to AMD_APCB_SERVICE_PPI itself
 * @param[out]    Purpose                   Point to purpose
 * @param[in]     GroupId                   Group id
 * @param[in]     TypeId                    Type id
 * @param[in]     InstanceId                Instance id
 * @param[out]    DataBuf                   Point to the data buffer
 * @param[out]    DataSize                  Point to the data size
 *
 * @retval        EFI_SUCCESS               The type data is retrieved successfully
 * @retval        EFI_NOT_FOUND             The type data cannot be retrieved
 * @retval        EFI_ABORTED               Reject Access due to SMM locked
 */
typedef
EFI_STATUS
(EFIAPI *FP_GET_TYPE) (
  IN       AMD_APCB_SERVICE_PPI *This,
  IN  OUT  UINT8                *Purpose,
  IN       UINT16               GroupId,
  IN       UINT16               TypeId,
  IN       UINT16               InstanceId,
  IN  OUT  UINT8                **DataBuf,
  IN  OUT  UINT32               *DataSize
  );

///
/// Ppi prototype
///
/// Defines AMD_APCB_SERVICE_PPI, which publish the APCB service across all programs
///
struct _AMD_APCB_SERVICE_PPI {
  UINT32              Version;            ///< Version number of the Ppi
  //
  // APCB 3.0 services
  //
  FP_GET_TOKEN_BOOL   ApcbGetTokenBool;   ///< Get an APCB BOOL token
  FP_GET_TOKEN_8      ApcbGetToken8;      ///< Get an APCB UINT8 token
  FP_GET_TOKEN_16     ApcbGetToken16;     ///< Get an APCB UINT16 token
  FP_GET_TOKEN_32     ApcbGetToken32;     ///< Get an APCB UINT32 token
  FP_GET_TYPE         ApcbGetType;        ///< Retrieve the data of a specified type
};

extern EFI_GUID gAmdApcbPeiServicePpiGuid;

#endif //_AMD_APCB_PPI_H_


