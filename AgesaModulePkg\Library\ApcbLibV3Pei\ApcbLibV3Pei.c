/*****************************************************************************
 * Copyright (C) 2024 Advanced Micro Devices, Inc. All rights reserved.
 *
*****************************************************************************
*/

/*----------------------------------------------------------------------------------------
 *                             M O D U L E S    U S E D
 *----------------------------------------------------------------------------------------
 */

#include <Uefi.h>
#include <PiPei.h>
#include <Base.h>
#include <Library/PcdLib.h>
#include <Library/BaseMemoryLib.h>
#include <Library/PeiServicesLib.h>
#include <Library/AmdBaseLib.h>
#include <Library/AmdSocBaseLib.h>
#include <Library/AmdPspBaseLibV2.h>
#include <Library/AmdPspApobLib.h>
#include <Library/AmdPspFwImageHeaderLib.h>
#include <Library/ApobCommonServiceLib.h>
#include <Addendum/Apcb/Inc/CommonV3/ApcbV3Arch.h>
#include <Addendum/Apcb/Inc/CommonV3/ApcbV3Priority.h>
#include <Library/ApcbLibV3Pei.h>

#include <Filecode.h>

/*----------------------------------------------------------------------------------------
 *                   D E F I N I T I O N S    A N D    M A C R O S
 *----------------------------------------------------------------------------------------
 */
#define FILECODE        LIBRARY_APCBLIBV3PEI_APCBLIBV3PEI_FILECODE

#define APCB_SIGNATURE  0x42435041ul

/*----------------------------------------------------------------------------------------
 *                  T Y P E D E F S     A N D     S T R U C T U R E S
 *----------------------------------------------------------------------------------------
 */


/*----------------------------------------------------------------------------------------
 *           P R O T O T Y P E S     O F     L O C A L     F U N C T I O N S
 *----------------------------------------------------------------------------------------
 */


/*----------------------------------------------------------------------------------------
 *                          E X P O R T E D    F U N C T I O N S
 *----------------------------------------------------------------------------------------
 */


/*----------------------------------------------------------------------------------------
 *                          G L O B A L        V A L U E S
 *----------------------------------------------------------------------------------------
 */
STATIC APCB_PURPOSE_TO_PRIORITY_LEVEL
PurposeToPriorityMappingTable [PURPOSE_TO_PRIORITY_MAPPING_TABLE_SIZE] = {
  {APCB_TYPE_PURPOSE_HARD_FORCE,      APCB_PRIORITY_LEVEL_HARD_FORCE   },
  {APCB_TYPE_PURPOSE_ADMIN,           APCB_PRIORITY_LEVEL_HIGH         },
  {APCB_TYPE_PURPOSE_DEBUG,           APCB_PRIORITY_LEVEL_MEDIUM       },
  {APCB_TYPE_PURPOSE_EVENT_LOGGING,   APCB_PRIORITY_LEVEL_EVENT_LOGGING},
  {APCB_TYPE_PURPOSE_NORMAL,          APCB_PRIORITY_LEVEL_LOW          },
  {APCB_TYPE_PURPOSE_DEFAULT,         APCB_PRIORITY_LEVEL_DEFAULT      }
};

STATIC PRIORITY_INSTANCE_MAPPING PriorityInstanceMappingTable [PRIORITY_INSTANCE_MAPPING_TABLE_SIZE] = {
  {APCB_PRIORITY_LEVEL_HARD_FORCE,    BIOS_APCB_INFO_BACKUP,  APCB_BINARY_INSTANCE_HARD_FORCE    },
  {APCB_PRIORITY_LEVEL_HIGH,          BIOS_APCB_INFO,         APCB_BINARY_INSTANCE_BIOS_CONFIG   },
  {APCB_PRIORITY_LEVEL_MEDIUM,        BIOS_APCB_INFO,         APCB_BINARY_INSTANCE_BIOS_CONFIG   },
  {APCB_PRIORITY_LEVEL_EVENT_LOGGING, BIOS_APCB_INFO,         APCB_BINARY_INSTANCE_EVENT_LOGGING },
  {APCB_PRIORITY_LEVEL_LOW,           BIOS_APCB_INFO,         APCB_BINARY_INSTANCE_BIOS_CONFIG   },
  {APCB_PRIORITY_LEVEL_DEFAULT,       BIOS_APCB_INFO_BACKUP,  APCB_BINARY_INSTANCE_DEFAULT       }
};

STATIC APCB_BINARY_INSTANCE_STORAGE
ApcbBinaryInstanceStorageTable [APCB_BINARY_INSTANCE_STORAGE_TABLE_SIZE] = {
  {
    BIOS_APCB_INFO_BACKUP,
    APCB_BINARY_INSTANCE_DEFAULT,
    {
      NULL,
      0
    },
    {
      NULL,
      0
    },
    {
      0
    },
    APCB_BACKUP_BINARY_REQUIRED_NONE
  },
  {
    BIOS_APCB_INFO,
    APCB_BINARY_INSTANCE_BIOS_CONFIG,
    {
      NULL,
      0
    },
    {
      NULL,
      0
    },
    {
      0
    },
    APCB_BACKUP_BINARY_REQUIRED_MANDATORY
  },
  {
    BIOS_APCB_INFO,
    APCB_BINARY_INSTANCE_EVENT_LOGGING,
    {
      NULL,
      0
    },
    {
      NULL,
      0
    },
    {
      0
    },
    APCB_BACKUP_BINARY_REQUIRED_NONE
  }
};

/**
 * @brief Use Apcb Backup Entry instead of Apcb Active Entry or not
 *
 * @param[in] *Entry    The Pointer of APCB_BINARY_INSTANCE_STORAGE
 *
 * @return TRUE         Use Apcb Backup Entry Info
 * @return FALSE        Use Apcb Active Entry Info
 */
BOOLEAN
UseApcbBackUpEntryInfo (
  IN APCB_BINARY_INSTANCE_STORAGE *Entry
  )
{
  if ((PcdGet8 (PcdApcbRecoveryStrategy) == 0x1) &&
      (Entry->BiosDirEntry == BIOS_APCB_INFO) &&
      (Entry->ApcbBackUpEntryInfo.Address != NULL) &&
      (Entry->ApcbBackUpEntryInfo.Size != 0) &&
      (Entry->ApcbBackUpBinaryRequired == APCB_BACKUP_BINARY_REQUIRED_MANDATORY)) {
    return TRUE;
  }

  return FALSE;
}

/**
 * @brief Update ApcbBackUpEntryInfo into ApcbBinaryInstanceStorage struct
 *
 * @param[in]  ApcbEntryExist   Workable Apcb Entry is exist or not
 * @param[in] *Entry            The Pointer of APCB_BINARY_INSTANCE_STORAGE
 *
 * @return TRUE       ApcbBackUpEntry is exist
 * @return FALSE      ApcbBackUpEntry is not exist
 */
BOOLEAN
UpdateApcbBackUpEntryInfo (
  IN BOOLEAN                      ApcbEntryExist,
  IN APCB_BINARY_INSTANCE_STORAGE *Entry
  )
{
  UINT32      SubProgram;
  TYPE_ATTRIB TypeAttrib;
  UINT64      EntryDest;
  BOOLEAN     ApcbBackUpEntryExist;
  UINTN       *pApcbEntryInfoAddress;
  UINT32      PspFwImageHeaderLen;

  SubProgram                         = 0;
  ApcbBackUpEntryExist               = FALSE;

  if ((PcdGet8 (PcdApcbRecoveryStrategy) == 0x1) &&
      (Entry->BiosDirEntry == BIOS_APCB_INFO) &&
      (Entry->ApcbBackUpBinaryRequired == APCB_BACKUP_BINARY_REQUIRED_MANDATORY)) {

    ApobGetSubProgram (&SubProgram);
    TypeAttrib.SubProgram = SubProgram & 0x7;

    ApcbBackUpEntryExist = BIOSEntryInfoByAttributes (
                             (Entry->BiosDirEntry   + 8),
                             (Entry->BinaryInstance + 8),
                             (UINT8) SubProgram & 0x7,
                             &TypeAttrib,
                             (VOID *)   &Entry->ApcbBackUpEntryInfo.Address,
                             (UINT32 *) &Entry->ApcbBackUpEntryInfo.Size,
                             &EntryDest
                             );

    if (ApcbBackUpEntryExist) {
      if (ApcbFwImageHeaderCheck (
            (UINT64)(UINTN) Entry->ApcbBackUpEntryInfo.Address,
            &PspFwImageHeaderLen)) {

        pApcbEntryInfoAddress   = (UINTN *) &Entry->ApcbBackUpEntryInfo.Address;
        *pApcbEntryInfoAddress += PspFwImageHeaderLen;

        if ((Entry->ApcbBackUpEntryInfo.Size != 0) &&
            (Entry->ApcbBackUpEntryInfo.Size > PspFwImageHeaderLen) &&
            (PspFwImageHeaderLen != 0)) {
          Entry->ApcbBackUpEntryInfo.Size -= PspFwImageHeaderLen;
        }

        // It will use Apcb Backup data instead of Apcb workable data,
        // Set ApcbEntryInfo.Size = ApcbBackUpEntryInfo.Size first,
        // because will use ApcbBackUpEntryInfo.Size to create shadow buffer.
        if (ApcbEntryExist) {
          Entry->ApcbEntryInfo.Size = Entry->ApcbBackUpEntryInfo.Size;
        }
      }
    }
  }

  return ApcbBackUpEntryExist;
}

/**
 * @brief Initialize Apcb Pei Variable Struct Data
 *
 * @return Apcb Pei Variable Struct Base Address
 */
APCB_PEI_VARIABLE_STRUCT *
GetApcbPeiVariableStructAddress (
  VOID
  )
{
  EFI_STATUS                Status;
  BOOLEAN                   ApcbRecoveryFlag;
  APCB_PEI_VARIABLE_STRUCT  *Address;

  Status           = EFI_SUCCESS;
  ApcbRecoveryFlag = FALSE;
  Address          = NULL;

  //
  // Exit service, if recovery flag set
  //
  Status = ApobGetApcbRecoveryFlag (&ApcbRecoveryFlag);
  if ((ApcbRecoveryFlag == TRUE) &&
      (PcdGet8 (PcdApcbRecoveryStrategy) == 0x0)) {
    IDS_HDT_CONSOLE_PSP_TRACE ("[APCB Lib V3 Pei] APCB.RecoveryFlag Set, exit service\n");
    return Address;
  }

  //
  // Exit service, if recovery flag set
  //
  if (CheckPspRecoveryFlagV2 () == TRUE) {
    IDS_HDT_CONSOLE_PSP_TRACE ("[APCB Lib V3 Pei] Recovery flag set, exit service\n");
    return Address;
  }

  if (PcdGet32 (PcdApcbPeiVariableStructAddress) == 0) {
    Status = PeiServicesAllocatePool (
               sizeof (APCB_PEI_VARIABLE_STRUCT),
               (VOID **) &Address
               );

    if ((!EFI_ERROR(Status)) && (Address != NULL)) {
      ZeroMem ((VOID *) Address, sizeof (APCB_PEI_VARIABLE_STRUCT));
      PcdSet32S (PcdApcbPeiVariableStructAddress, (UINT32) (UINTN) Address);

      Address->CurrentPriorityLevel   = INVALID_PRIORITY_LEVEL;
      Address->CurrentBiosDirEntry    = INVALID_BIOS_DIR_ENTRY;
      Address->CurrentBinaryInstance  = INVALID_BINARY_INSTANCE;
      Address->CurrentBoardMask       = 0;

      CopyMem ((VOID *) &Address->PurposeToPriorityMapping [0],
               (VOID *) &PurposeToPriorityMappingTable [0],
               (PURPOSE_TO_PRIORITY_MAPPING_TABLE_SIZE * sizeof (APCB_PURPOSE_TO_PRIORITY_LEVEL))
               );

      CopyMem ((VOID *) &Address->ApcbBinaryInstanceStorage [0],
               (VOID *) &ApcbBinaryInstanceStorageTable [0],
               (APCB_BINARY_INSTANCE_STORAGE_TABLE_SIZE * sizeof (APCB_BINARY_INSTANCE_STORAGE))
               );
    } else {
      IDS_HDT_CONSOLE_PSP_TRACE ("[APCB Lib V3 Pei][%a] AllocatePool Fail\n", __FUNCTION__);
    }
  }

  Address = (APCB_PEI_VARIABLE_STRUCT *) (UINTN) PcdGet32 (PcdApcbPeiVariableStructAddress);

  return Address;
}

/**
 * @brief Get Apcb Binary Instance Storage Table Base Address and
 *        Number of Tables
 *
 * @param[in,out] SourceTableAddress  Apcb Binary Instance Storage Table Base Address
 * @param[in,out] SourceTableNum      Number of tables
 *
 * @return Status EFI_SUCCESS         Success
 *                Non-EFI_SUCCESS     Function Error
 */
EFI_STATUS
GetApcbBinaryInstanceStorageAddress (
  APCB_BINARY_INSTANCE_STORAGE  **SourceTableAddress,
  UINT8                          *SourceTableNum
  )
{
  EFI_STATUS                Status;
  APCB_PEI_VARIABLE_STRUCT  *pApcbPeiVariableStruct;

  Status                 = EFI_SUCCESS;
  pApcbPeiVariableStruct = NULL;

  if ((SourceTableAddress == NULL) || (SourceTableNum == NULL)) {
    return EFI_UNSUPPORTED;
  }

  pApcbPeiVariableStruct = GetApcbPeiVariableStructAddress ();
  if (pApcbPeiVariableStruct == NULL) {
    return EFI_UNSUPPORTED;
  }

  *SourceTableAddress = &pApcbPeiVariableStruct->ApcbBinaryInstanceStorage [0];
  *SourceTableNum     =  APCB_BINARY_INSTANCE_STORAGE_TABLE_SIZE;

  return Status;
}

/**
 * @brief Get Purpose To Priority Mapping Table Base Address and
 *        Number of tables
 *
 * @param[in,out] SourceTableAddress  Purpose To Priority Mapping Table Base Address
 * @param[in,out] SourceTableNum      Number of tables
 *
 * @return Status EFI_SUCCESS         Success
 *                Non-EFI_SUCCESS     Function Error
 */
EFI_STATUS
GetPurposeToPriorityMappingAddress (
  APCB_PURPOSE_TO_PRIORITY_LEVEL **SourceTableAddress,
  UINT8                           *SourceTableNum
  )
{
  EFI_STATUS                Status;
  APCB_PEI_VARIABLE_STRUCT  *pApcbPeiVariableStruct;

  Status                 = EFI_SUCCESS;
  pApcbPeiVariableStruct = NULL;

  if ((SourceTableAddress == NULL) || (SourceTableNum == NULL)) {
    return EFI_UNSUPPORTED;
  }

  pApcbPeiVariableStruct = GetApcbPeiVariableStructAddress ();
  if (pApcbPeiVariableStruct == NULL) {
    return EFI_UNSUPPORTED;
  }

  *SourceTableAddress = &pApcbPeiVariableStruct->PurposeToPriorityMapping [0];
  *SourceTableNum     = PURPOSE_TO_PRIORITY_MAPPING_TABLE_SIZE;

  return Status;
}

/**
 * @brief This function set the target priority level for the subsequent APCB operations
 *
 * @param[in] PriorityLevel   APCB Priority Level
 */
VOID
ApcbSetPriorityLevel (
  IN  UINT8  PriorityLevel
  )
{
  UINT8   i;
  APCB_PEI_VARIABLE_STRUCT  *pApcbPeiVariableStruct;

  pApcbPeiVariableStruct = GetApcbPeiVariableStructAddress ();
  if (pApcbPeiVariableStruct == NULL) {
   IDS_HDT_CONSOLE_PSP_TRACE ("[APCB Lib V3 Pei] GetApcbPeiVariableStructAddress Fail\n");
    return;
  }

  for (i = 0; i < sizeof (PriorityInstanceMappingTable) / sizeof (PRIORITY_INSTANCE_MAPPING); i ++) {
    if (PriorityLevel == PriorityInstanceMappingTable[i].PriorityLevel) {
      pApcbPeiVariableStruct->CurrentPriorityLevel = PriorityLevel;
      pApcbPeiVariableStruct->CurrentBiosDirEntry   = PriorityInstanceMappingTable[i].BiosDirEntry;
      pApcbPeiVariableStruct->CurrentBinaryInstance = PriorityInstanceMappingTable[i].BinaryInstance;
      IDS_HDT_CONSOLE_PSP_TRACE ("[APCB Lib V3 Pei] APCB Priority Level set to %d\n",
        pApcbPeiVariableStruct->CurrentPriorityLevel);
      IDS_HDT_CONSOLE_PSP_TRACE ("[APCB Lib V3 Pei]   |- CurrentBiosDirEntry   = 0x%x\n",
        pApcbPeiVariableStruct->CurrentBiosDirEntry);
      IDS_HDT_CONSOLE_PSP_TRACE ("[APCB Lib V3 Pei]   |- CurrentBinaryInstance = 0x%x\n",
        pApcbPeiVariableStruct->CurrentBinaryInstance);
      return;
    }
  }

  ASSERT (FALSE);
}

/**
 * @brief This function set the purpose for the subsequent APCB operations
 *
 * @retval UINT8 Purpose
 */
UINT8
ApcbGetPurpose (
  VOID
  )
{
  EFI_STATUS Status;
  UINT8   i;
  APCB_PEI_VARIABLE_STRUCT        *pApcbPeiVariableStruct;
  APCB_PURPOSE_TO_PRIORITY_LEVEL  *PurposeToPriorityMapping;
  UINT8                           PurposeToPriorityMappingSize;

  Status = EFI_SUCCESS;

  pApcbPeiVariableStruct = GetApcbPeiVariableStructAddress ();
  if (pApcbPeiVariableStruct == NULL) {
    IDS_HDT_CONSOLE_PSP_TRACE ("[APCB Lib V3 Pei] GetApcbPeiVariableStructAddress Fail\n");
    return APCB_TYPE_PURPOSE_NONE;
  }

  Status = GetPurposeToPriorityMappingAddress (&PurposeToPriorityMapping, &PurposeToPriorityMappingSize);
  if (EFI_ERROR(Status)) {
    IDS_HDT_CONSOLE_PSP_TRACE ("[APCB Lib V3 Pei] GetPurposeToPriorityMappingAddress Fail, Status: %r\n", Status);
    return APCB_TYPE_PURPOSE_NONE;
  }

  for (i = 0; i < PurposeToPriorityMappingSize; i ++) {
    if (pApcbPeiVariableStruct->CurrentPriorityLevel == PurposeToPriorityMapping[i].PriorityLevel) {
      return PurposeToPriorityMapping[i].Purpose;
    }
  }

  ASSERT (FALSE);

  return APCB_TYPE_PURPOSE_NONE;
}

/**
 * @brief Function to get ApcbShadowCopy Address
 *
 * @param[in,out] Size Apcb Data Size
 *
 * @return ApcbShadowCopy Address
 * @return NULL, APCB Shadow copy not found
 */
VOID *
GetApcbShadowCopy (
  IN OUT  UINT32  *Size
  )
{
  EFI_STATUS                    Status;
  APCB_V3_HEADER                *ApcbData;
  UINT8                         i;
  UINT8                         j;
  APCB_V3_HEADER                ApcbHeader;
  APCB_PEI_VARIABLE_STRUCT      *pApcbPeiVariableStruct;
  APCB_BINARY_INSTANCE_STORAGE  *ApcbBinaryInstanceStorage;
  UINT8                         ApcbBinaryInstanceStorageSize;

  ASSERT (Size != NULL);

  pApcbPeiVariableStruct = GetApcbPeiVariableStructAddress ();
  if (pApcbPeiVariableStruct == NULL) {
    IDS_HDT_CONSOLE_PSP_TRACE ("[APCB Lib V3 Pei] GetApcbPeiVariableStructAddress Fail\n");
    return NULL;
  }

  Status = GetApcbBinaryInstanceStorageAddress (&ApcbBinaryInstanceStorage, &ApcbBinaryInstanceStorageSize);
  if (EFI_ERROR(Status)) {
    IDS_HDT_CONSOLE_PSP_TRACE ("[APCB Lib V3 Pei] GetApcbBinaryInstanceStorageAddress Fail, Status: %r\n", Status);
    return NULL;
  }

  ApcbData = NULL;
  for (i = 0; i < sizeof (PriorityInstanceMappingTable) / sizeof (PRIORITY_INSTANCE_MAPPING); i ++) {
    if (pApcbPeiVariableStruct->CurrentPriorityLevel == PriorityInstanceMappingTable[i].PriorityLevel) {
      for (j = 0; j < ApcbBinaryInstanceStorageSize; j ++) {
        if (PriorityInstanceMappingTable[i].BiosDirEntry == ApcbBinaryInstanceStorage[j].BiosDirEntry &&
            PriorityInstanceMappingTable[i].BinaryInstance == ApcbBinaryInstanceStorage[j].BinaryInstance) {

          ApcbData = (APCB_V3_HEADER *) (UINTN) ApcbBinaryInstanceStorage[j].ApcbShadow.GetAddress;
          *Size = ApcbBinaryInstanceStorage[j].ApcbEntryInfo.Size;

          if ((0 != *Size) && (NULL != ApcbData) && (APCB_SIGNATURE == ApcbData->Signature)) {
            IDS_HDT_CONSOLE_PSP_TRACE ("[APCB Lib V3 Pei] GetApcbShadowCopy for Entry 0x%x, Binary Instance %d, 0x%x:0x%x\n",
                                        ApcbBinaryInstanceStorage[j].BiosDirEntry,
                                        ApcbBinaryInstanceStorage[j].BinaryInstance,
                                        ApcbData,
                                        *Size);

            // In Pei Phase, only provide to read APCB data services, so it always get data from Apcb backup data.
            if (UseApcbBackUpEntryInfo (&ApcbBinaryInstanceStorage[j]) == TRUE) {
              IDS_HDT_CONSOLE_PSP_TRACE ("[APCB Lib V3 Pei] GetApcbShadowCopy: Return Backup Shadow Copy Data ...\n");
              return ApcbData;
            }

            MapSpiDataToBuffer (
              (UINT32)(UINTN) ApcbBinaryInstanceStorage[j].ApcbEntryInfo.Address,
              (VOID *) &ApcbHeader,
              sizeof (APCB_V3_HEADER)
              );
            if (ApcbHeader.UniqueApcbInstance != ApcbData->UniqueApcbInstance) {
              MapSpiDataToBuffer (
                (UINT32)(UINTN) ApcbBinaryInstanceStorage[j].ApcbEntryInfo.Address,
                (VOID *) ApcbData,
                ApcbHeader.SizeOfApcb
                );
            }
            return ApcbData;
          } else {
            IDS_HDT_CONSOLE_PSP_TRACE ("[APCB Lib V3 Pei] APCB Shadow copy not found\n");
            return NULL;
          }
        }
      }
    }
  }

  IDS_HDT_CONSOLE_PSP_TRACE ("[APCB Lib V3 Pei] APCB Shadow copy not found\n");

  return NULL;
}

/**
 * @brief ApcbLibV3PeiConstructor, initial the shadow copy of APCB data in Pei Phase
 *  and save the address to PCD
 *
 * @param[in] ImageHandle   Image Handle
 * @param[in] SystemTable   The Pointer of EFI System Table
 *
 * @retval EFI_STATUS  0: Success, NonZero Error
 */
EFI_STATUS
EFIAPI
ApcbLibV3PeiConstructor (
  IN EFI_PEI_FILE_HANDLE     FileHandle,
  IN CONST EFI_PEI_SERVICES  **PeiServices
  )
{
  EFI_STATUS                      Status;
  APCB_PEI_VARIABLE_STRUCT        *pApcbPeiVariableStruct;
  UINT32                          SubProgram;
  APCB_PURPOSE_TO_PRIORITY_LEVEL  *PurposeToPriorityMapping;
  UINT8                           PurposeToPriorityMappingSize;
  UINT8                           i;
  TYPE_ATTRIB                     TypeAttrib;
  APCB_BINARY_INSTANCE_STORAGE    *ApcbBinaryInstanceStorage;
  UINT8                           ApcbBinaryInstanceStorageSize;
  APCB_V3_HEADER                  *ApcbShadowCopy;
  BOOLEAN                         ApcbEntryExist;
  UINT64                          ApcbEntryInfoAddress;
  UINT64                          EntryDest;
  UINTN                           *pApcbEntryInfoAddress;
  UINT32                          PspFwImageHeaderLen;
  BOOLEAN                         ApcbBackUpEntryExist;
  BOOLEAN                         ApcbRecoveryFlag;

  IDS_HDT_CONSOLE_PSP_TRACE ("\n[APCB Lib V3 Pei] ApcbLibV3PeiConstructor Entry\n");
  AGESA_TESTPOINT (TpApcbLibPeiEntry, NULL);

  Status                   = EFI_SUCCESS;
  pApcbPeiVariableStruct   = NULL;
  SubProgram               = 0;
  PurposeToPriorityMapping = NULL;
  ApcbShadowCopy           = NULL;
  ApcbBackUpEntryExist     = FALSE;

  //
  // Exit if BR program
  //
  if (SocFamilyIdentificationCheck (F15_BR_RAW_ID)) {
    return EFI_SUCCESS;
  }

  pApcbPeiVariableStruct = GetApcbPeiVariableStructAddress ();
  if (pApcbPeiVariableStruct == NULL) {
    IDS_HDT_CONSOLE_PSP_TRACE ("[APCB Lib V3 Pei] pApcbPeiVariableStruct is NULL\n");
    return EFI_SUCCESS;
  }

  ApcbRecoveryFlag = FALSE;
  Status = ApobGetApcbRecoveryFlag (&ApcbRecoveryFlag);

  ApobGetBoardMask (0, &pApcbPeiVariableStruct->CurrentBoardMask);
  IDS_HDT_CONSOLE_PSP_TRACE ("[APCB Lib V3 Pei] Current Board Mask = 0x%x\n", pApcbPeiVariableStruct->CurrentBoardMask);
  ApobGetSubProgram (&SubProgram);
  IDS_HDT_CONSOLE_PSP_TRACE ("[APCB Lib V3 Pei] Current SubProgram = 0x%x\n", SubProgram);

  //
  // Initialize the APCB purpose to priority mapping
  //
  Status = GetPurposeToPriorityMappingAddress (&PurposeToPriorityMapping, &PurposeToPriorityMappingSize);
  if (EFI_ERROR(Status)) {
    IDS_HDT_CONSOLE_PSP_TRACE ("[APCB Lib V3 Pei] GetPurposeToPriorityMappingAddress Fail, Status: %r\n", Status);
    return EFI_SUCCESS;
  }
  for (i = 0; i < PurposeToPriorityMappingSize; i ++) {
    switch (PurposeToPriorityMapping[i].Purpose) {
    case APCB_TYPE_PURPOSE_ADMIN:
      PurposeToPriorityMapping[i].PriorityLevel = PcdGet8 (PcdAmdApcbPriorityLevelAdmin);
      break;
    case APCB_TYPE_PURPOSE_DEBUG:
      PurposeToPriorityMapping[i].PriorityLevel = PcdGet8 (PcdAmdApcbPriorityLevelDebug);
      break;
    case APCB_TYPE_PURPOSE_NORMAL:
      PurposeToPriorityMapping[i].PriorityLevel = PcdGet8 (PcdAmdApcbPriorityLevelNormal);
      break;
    default:
      break;
    }
  }

  TypeAttrib.SubProgram = SubProgram & 0x7;
  Status = GetApcbBinaryInstanceStorageAddress (&ApcbBinaryInstanceStorage, &ApcbBinaryInstanceStorageSize);
  if (EFI_ERROR(Status)) {
    IDS_HDT_CONSOLE_PSP_TRACE ("[APCB Lib V3 Pei] GetApcbBinaryInstanceStorageAddress Fail, Status: %r\n", Status);
    return EFI_SUCCESS;
  }

  for (i = 0; i < ApcbBinaryInstanceStorageSize; i ++) {
    IDS_HDT_CONSOLE_PSP_TRACE ("[APCB Lib V3 Pei] Locating APCB binary Instance 0x%x in Entry 0x%x ...\n",
                                ApcbBinaryInstanceStorage[i].BinaryInstance,
                                ApcbBinaryInstanceStorage[i].BiosDirEntry
                              );

    ApcbEntryExist = BIOSEntryInfoByAttributes (
                       ApcbBinaryInstanceStorage[i].BiosDirEntry,
                       ApcbBinaryInstanceStorage[i].BinaryInstance,
                       (UINT8) SubProgram & 0x7,
                       &TypeAttrib,
                       (VOID *)   &ApcbEntryInfoAddress,
                       (UINT32 *) &ApcbBinaryInstanceStorage[i].ApcbEntryInfo.Size,
                       &EntryDest
                       );

    ApcbBinaryInstanceStorage[i].ApcbEntryInfo.Address = (APCB_V3_HEADER *) (UINTN) ApcbEntryInfoAddress;

    // Check Apcb Data has PSP Fw Image Header or not
    if (ApcbEntryExist) {
      if (ApcbFwImageHeaderCheck (
            (UINT64)(UINTN)ApcbBinaryInstanceStorage[i].ApcbEntryInfo.Address,
            &PspFwImageHeaderLen)) {

        pApcbEntryInfoAddress   = (UINTN *) &ApcbBinaryInstanceStorage[i].ApcbEntryInfo.Address;
        *pApcbEntryInfoAddress += PspFwImageHeaderLen;

        if ((ApcbBinaryInstanceStorage[i].ApcbEntryInfo.Size != 0) &&
            (ApcbBinaryInstanceStorage[i].ApcbEntryInfo.Size > PspFwImageHeaderLen) &&
            (PspFwImageHeaderLen != 0)) {
          ApcbBinaryInstanceStorage[i].ApcbEntryInfo.Size -= PspFwImageHeaderLen;
        }
      }
    }

    ApcbBackUpEntryExist = FALSE;
    ApcbBinaryInstanceStorage[i].ApcbBackUpEntryInfo.Address = NULL;
    ApcbBinaryInstanceStorage[i].ApcbBackUpEntryInfo.Size    = 0;
    if (ApcbRecoveryFlag == TRUE) {
      // Save Apcb Backup Entry Address and Size in ApcbBackUpEntryInfo
      ApcbBackUpEntryExist = UpdateApcbBackUpEntryInfo (ApcbEntryExist, &ApcbBinaryInstanceStorage[i]);
    }

    ApcbShadowCopy = (APCB_V3_HEADER *) (UINTN) ApcbBinaryInstanceStorage[i].ApcbShadow.GetAddress;

    if (ApcbShadowCopy == NULL) {
      if (!ApcbEntryExist) {
        IDS_HDT_CONSOLE_PSP_TRACE ("[APCB Lib V3 Pei] APCB Instance 0x%x does not exist in Entry 0x%x\n",
                                    ApcbBinaryInstanceStorage[i].BinaryInstance,
                                    ApcbBinaryInstanceStorage[i].BiosDirEntry
                                  );
      } else {

        IDS_HDT_CONSOLE_PSP_TRACE ("[APCB Lib V3 Pei] Allocate Pei APCB Shadow Copy for Binary Entry 0x%x Instance 0x%x with 0x%x bytes\n",
                                    ApcbBinaryInstanceStorage[i].BiosDirEntry,
                                    ApcbBinaryInstanceStorage[i].BinaryInstance,
                                    ApcbBinaryInstanceStorage[i].ApcbEntryInfo.Size
                                  );

        if (ApcbBinaryInstanceStorage[i].ApcbEntryInfo.Size == 0) {
          continue;
        }

        Status = PeiServicesAllocatePool (
                   ApcbBinaryInstanceStorage[i].ApcbEntryInfo.Size,
                   (VOID **) &ApcbShadowCopy
                   );

        if (EFI_ERROR(Status)) {
          IDS_HDT_CONSOLE_PSP_TRACE("[APCB Lib V3 Pei] AllocatePool Fail, Status: %r\n", Status);
          return EFI_SUCCESS;
        }

        if (ApcbShadowCopy == NULL) {
          IDS_HDT_CONSOLE_PSP_TRACE ("[APCB Lib V3 Pei] Fail to Allocate APCB Shadow Copy\n");
          ASSERT (ApcbShadowCopy != NULL);
          return EFI_SUCCESS;
        }

        IDS_HDT_CONSOLE_PSP_TRACE ("[APCB Lib V3 Pei] ALLOCATED [0x%x:0x%x]\n",
                                    ApcbShadowCopy,
                                    ApcbBinaryInstanceStorage[i].ApcbEntryInfo.Size
                                  );

        if ((ApcbShadowCopy != NULL) && (ApcbBinaryInstanceStorage[i].ApcbEntryInfo.Size != 0)) {
          ZeroMem (ApcbShadowCopy, ApcbBinaryInstanceStorage[i].ApcbEntryInfo.Size);
        }

        //
        // Copy the Apcb from SPI to Shadow Copy
        //
        if ((UseApcbBackUpEntryInfo (&ApcbBinaryInstanceStorage[i]) == TRUE) &&
            (ApcbBackUpEntryExist == TRUE)) {

          IDS_HDT_CONSOLE_PSP_TRACE ("[APCB Lib V3 Pei] Map Spi Data from Apcb Backup Entry [Address: 0x%x; Size: 0x%x]\n",
                                      ApcbBinaryInstanceStorage[i].ApcbBackUpEntryInfo.Address,
                                      ApcbBinaryInstanceStorage[i].ApcbBackUpEntryInfo.Size
                                    );
          MapSpiDataToBuffer(
             (UINT32)(UINTN) ApcbBinaryInstanceStorage[i].ApcbBackUpEntryInfo.Address,
             ApcbShadowCopy,
             ApcbBinaryInstanceStorage[i].ApcbBackUpEntryInfo.Size
             );
        } else {
          MapSpiDataToBuffer(
            (UINT32)(UINTN) ApcbBinaryInstanceStorage[i].ApcbEntryInfo.Address,
            ApcbShadowCopy,
            ApcbBinaryInstanceStorage[i].ApcbEntryInfo.Size
            );
        }

        ApcbBinaryInstanceStorage[i].ApcbShadow.SetAddress = (UINT32) (UINTN) ApcbShadowCopy;

        if (ApcbShadowCopy->Signature != APCB_SIGNATURE) {
          IDS_HDT_CONSOLE_PSP_TRACE ("[APCB Lib V3 Pei] Warning: APCB Signature is not correct.\n");
        }
      }
    }
  }

  IDS_HDT_CONSOLE_PSP_TRACE ("[APCB Lib V3 Pei] ApcbLibV3PeiConstructor Exit\n");
  AGESA_TESTPOINT (TpApcbLibPeiExit, NULL);

  return EFI_SUCCESS;
}


