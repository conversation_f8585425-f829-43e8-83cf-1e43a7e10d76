/*****************************************************************************
 * Copyright (C) 2008-2024 Advanced Micro Devices, Inc. All rights reserved.
 *
*****************************************************************************
*/
/* $NoKeywords:$ */
/**
 * @file
 *
 * Config Fch HwAcpi controller
 *
 * Init HwAcpi Controller features.
 *
 * @xrefitem bom "File Content Label" "Release Content"
 * @e project:     AGESA
 * @e sub-project: FCH
 * @e \$Revision: 309083 $   @e \$Date: 2014-12-09 09:28:24 -0800 (Tue, 09 Dec 2014) $
 *
 */
#include "FchPlatform.h"
#include "Filecode.h"
#include <Library/AmdPspMboxLibV2.h>
#include <Library/PcdLib.h>

#define FILECODE FCH_KUNLUN_FCHKUNLUNCORE_KUNLUN_KLHWACPI_KLHWACPILATESERVICE_FILECODE

/**
 * GcpuRelatedSetting - Program Gcpu C related function
 *
 *
 *
 * @param[in] FchDataPtr   Fch configuration structure pointer.
 *
 */
VOID
GcpuRelatedSetting (
  IN  VOID     *FchDataPtr
  )
{
  UINT8        FchAcDcMsg;
  UINT8        FchTimerTickTrack;
  UINT8        FchClockInterruptTag;
  UINT8        FchOhciTrafficHanding;
  UINT8        FchEhciTrafficHanding;
  UINT8        FchGcpuMsgCMultiCore;
  UINT8        FchGcpuMsgCStage;
  UINT32       Value;
  FCH_DATA_BLOCK         *LocalCfgPtr;

  LocalCfgPtr = (FCH_DATA_BLOCK *) FchDataPtr;

  FchAcDcMsg = (UINT8) LocalCfgPtr->Gcpu.AcDcMsg;
  FchTimerTickTrack = (UINT8) LocalCfgPtr->Gcpu.TimerTickTrack;
  FchClockInterruptTag = (UINT8) LocalCfgPtr->Gcpu.ClockInterruptTag;
  FchOhciTrafficHanding = (UINT8) LocalCfgPtr->Gcpu.OhciTrafficHanding;
  FchEhciTrafficHanding = (UINT8) LocalCfgPtr->Gcpu.EhciTrafficHanding;
  FchGcpuMsgCMultiCore = (UINT8) LocalCfgPtr->Gcpu.GcpuMsgCMultiCore;
  FchGcpuMsgCStage = (UINT8) LocalCfgPtr->Gcpu.GcpuMsgCStage;

  ReadMem (ACPI_MMIO_BASE + PMIO_BASE + FCH_PMIOA_REGA0, AccessWidth32, &Value);
  Value = Value & 0xC07F00A0;

  if ( FchAcDcMsg ) {
    Value = Value | BIT0;
  }

  if ( FchTimerTickTrack ) {
    Value = Value | BIT1;
  }

  if ( FchClockInterruptTag ) {
    Value = Value | BIT10;
  }

  if ( FchOhciTrafficHanding ) {
    Value = Value | BIT13;
  }

  if ( FchEhciTrafficHanding ) {
    Value = Value | BIT15;
  }

  if ( FchGcpuMsgCMultiCore ) {
    Value = Value | BIT23;
  }

  if ( FchGcpuMsgCStage ) {
    Value = (Value | (BIT6 + BIT4 + BIT3 + BIT2));
  }

  WriteMem (ACPI_MMIO_BASE + PMIO_BASE + FCH_PMIOA_REGA0, AccessWidth32, &Value);
}

/**
 * MtC1eEnable - Program Mt C1E Enable Function
 *
 *
 *
 * @param[in] FchDataPtr
 *
 */
VOID
MtC1eEnable (
  IN  VOID     *FchDataPtr
  )
{
  FCH_DATA_BLOCK         *LocalCfgPtr;

  LocalCfgPtr = (FCH_DATA_BLOCK *) FchDataPtr;

  if ( LocalCfgPtr->HwAcpi.MtC1eEnable ) {
    //Kunlun remove HW register
    //RwMem (ACPI_MMIO_BASE + PMIO_BASE + FCH_PMIOA_REG7A, AccessWidth16, ~(UINT32) BIT15, BIT15);
    //RwMem (ACPI_MMIO_BASE + PMIO_BASE + FCH_PMIOA_REG7A, AccessWidth16, ~(UINT32) (BIT3 + BIT2 + BIT1 + BIT0), 0x01);
    //RwMem (ACPI_MMIO_BASE + PMIO_BASE + FCH_PMIOA_REG80, AccessWidth16, ~(UINT32) (BIT13 + BIT7), BIT13 + BIT7);
  }
}

/**
 * StressResetModeLate - Stress Reset Mode
 *
 *
 *
 * @param[in] FchDataPtr
 *
 */
VOID
StressResetModeLate (
  IN  VOID     *FchDataPtr
  )
{
  UINT8                  ResetValue;
  FCH_DATA_BLOCK         *LocalCfgPtr;
  AMD_CONFIG_PARAMS      *StdHeader;

  LocalCfgPtr = (FCH_DATA_BLOCK *) FchDataPtr;
  StdHeader = LocalCfgPtr->StdHeader;

  switch ( LocalCfgPtr->HwAcpi.StressResetMode ) {
  case 0:
    return;
  case 1:
    ResetValue = FCH_KBC_RESET_COMMAND;
    LibAmdIoWrite (AccessWidth8, FCH_KBDRST_BASE_IO, &ResetValue, StdHeader);
    break;
  case 2:
    ResetValue = FCH_PCI_RESET_COMMAND06;
    LibAmdIoWrite (AccessWidth8, FCH_PCIRST_BASE_IO, &ResetValue, StdHeader);
    break;
  case 3:
    ResetValue = FCH_PCI_RESET_COMMAND0E;
    LibAmdIoWrite (AccessWidth8, FCH_PCIRST_BASE_IO, &ResetValue, StdHeader);
    break;
  case 4:
    LocalCfgPtr->HwAcpi.StressResetMode = 3;
    return;
  default:
    //ASSERT (FALSE);
    return;
  }
  while (LocalCfgPtr->HwAcpi.StressResetMode) {
  }
}

/**
 * FchEventInitUsbGpe - Init Gevent that mapped to Usb Pme
 *
 *
 *
 * @param[in] FchDataPtr
 *
 */
VOID
FchEventInitUsbGpe (
  IN  VOID     *FchDataPtr
  )
{
  UINT8                  UsbGpe0Num = 0, UsbGpe1Num = 0;
  UINT32                 SciLevl32;
//  FCH_DATA_BLOCK         *LocalCfgPtr;

//  LocalCfgPtr = (FCH_DATA_BLOCK *) FchDataPtr;

  UsbGpe0Num = ACPIMMIO8 (ACPI_MMIO_BASE + SMI_BASE + FCH_SMI_KL_Xhc0Wake);
  UsbGpe1Num = ACPIMMIO8 (ACPI_MMIO_BASE + SMI_BASE + FCH_SMI_KL_Xhc1Wake);

  SciLevl32 = ACPIMMIO32 (ACPI_MMIO_BASE + SMI_BASE + FCH_SMI_REG0C);

  if (UsbGpe0Num != 0) {
    UsbGpe0Num &= 0x1F;
    SciLevl32 |= (UINT32) (1 << UsbGpe0Num);
  }

  if (UsbGpe1Num != 0) {
    UsbGpe1Num &= 0x1F;
    SciLevl32 |= (UINT32) (1 << UsbGpe1Num);
  }

  ACPIMMIO32 (ACPI_MMIO_BASE + SMI_BASE + FCH_SMI_REG0C) = SciLevl32;
}

/**
 * FchEventInitLate - Program the latest unused event to all of unused SciMap
 *
 *
 *
 * @param[in] FchDataPtr
 *
*/
VOID
FchEventInitLate (
  IN  VOID     *FchDataPtr
  )
{
  UINT8                  SciMap, Event, UnusedEvent;
  UINT32                 UnusedEventBitFlag;
  UINT64                 UnusedSciMapBitFlag;

  // Set unused event bit flag to UnusedEventBitFlag
  // Set unused SciMap bit flag to UnusedSciMapBitFlag
  UnusedEventBitFlag = 0xFFFFFFFF;
  UnusedSciMapBitFlag = 0;
  for (SciMap = 0; SciMap <= 63; SciMap++) {
    Event = ACPIMMIO8 (ACPI_MMIO_BASE + SMI_BASE + FCH_SMI_Gevent0 + SciMap) & 0x1F;
    if (Event != 0) {
      // Clear unused event bit flag when Event is not equal to 0
      UnusedEventBitFlag &= ~((UINT32) 1 << Event);
    } else if (SciMap != 0) {
      // Set unused SciMap bit flag when Event is equal to 0
      UnusedSciMapBitFlag |= (UINT64) 1 << SciMap;
    } else {
      // Clear unused event bit 0 flag when both of SciMap and Event are equal to 0
      UnusedEventBitFlag &= ~(UINT32) BIT0;
    }
  }
  IDS_HDT_CONSOLE (FCH_TRACE, "  FchEventInitLate UnusedEventBitFlag  = 0x%08X\n", UnusedEventBitFlag);
  IDS_HDT_CONSOLE (FCH_TRACE, "  FchEventInitLate UnusedSciMapBitFlag = 0x%016LX\n", UnusedSciMapBitFlag);

  // Find the latest unused event with BitScanReverse
  for (UnusedEvent = 32; UnusedEvent > 0; UnusedEvent--) {
    if (UnusedEventBitFlag & ((UINT32) 1 << (UnusedEvent - 1))) {
      break;
    }
  }
  if (UnusedEvent != 0) {
    UnusedEvent--;
  }
  IDS_HDT_CONSOLE (FCH_TRACE, "  FchEventInitLate UnusedEvent         = 0x%02X\n", UnusedEvent);

  // Program the latest unused event to all of unused SciMap,
  // when the latest unused event is not equal to 0
  if (UnusedEvent != 0) {
    for (SciMap = 0; SciMap <= 63; SciMap++) {
      if (UnusedSciMapBitFlag & ((UINT64) 1 << SciMap)) {
        ACPIMMIO8 (ACPI_MMIO_BASE + SMI_BASE + FCH_SMI_Gevent0 + SciMap) = UnusedEvent;
      }
    }
  }
}

/**
 * FchI2cUartInitLate - Config Fch AMBA I2C Uart init Late
 *
 *
 *
 * @param[in] FchDataPtr Fch configuration structure pointer.
 *
 */
VOID
FchI2cUartInitLate (
  IN  VOID     *FchDataPtr
  )
{
  FCH_DATA_BLOCK         *LocalCfgPtr;

  LocalCfgPtr = (FCH_DATA_BLOCK *) FchDataPtr;

  // UART Legacy IO Enable Support
  if ( LocalCfgPtr->FchRunTime.Al2AhbLegacyUartIoEnable ) {
    ACPIMMIO16 (FCH_AL2AHBx20_LEGACY_UART_IO_ENABLE) = LocalCfgPtr->FchRunTime.Al2AhbLegacyUartIoEnable;
  } else {
    ACPIMMIO16 (FCH_AL2AHBx20_LEGACY_UART_IO_ENABLE) = 0;
  }
}

/**
 * FchCppcSciInit - Init SCI for CPPC feature
 *
 *
 *
 * @param[in] FchDataPtr
 *
 */
VOID
FchCppcSciInit (
  IN  VOID     *FchDataPtr
  )
{
  UINT32                 SciTrig;
  UINT32                 SciLevl;
  UINT32                 SwSciEn;
  UINT32                 EventEn;
  FCH_DATA_BLOCK         *LocalCfgPtr;

  LocalCfgPtr = (FCH_DATA_BLOCK *) FchDataPtr;

  if (LocalCfgPtr->Misc.Cppc.CppcSupport) {
    if(LocalCfgPtr->Misc.Cppc.SciBit){
        SciLevl = ACPIMMIO32 (ACPI_MMIO_BASE + SMI_BASE + FCH_SMI_REG0C);
        SciLevl |= LocalCfgPtr->Misc.Cppc.SciBit;   //Level trigger
        ACPIMMIO32 (ACPI_MMIO_BASE + SMI_BASE + FCH_SMI_REG0C) = SciLevl;

        SciTrig = ACPIMMIO32 (ACPI_MMIO_BASE + SMI_BASE + FCH_SMI_REG08);
        SciTrig |= LocalCfgPtr->Misc.Cppc.SciBit;   //Active high
        ACPIMMIO32 (ACPI_MMIO_BASE + SMI_BASE + FCH_SMI_REG08) = SciTrig;

        SwSciEn = ACPIMMIO32 (ACPI_MMIO_BASE + SMI_BASE + FCH_SMI_REG18);
        SwSciEn |= LocalCfgPtr->Misc.Cppc.SciBit;   //Enable SCI
        ACPIMMIO32 (ACPI_MMIO_BASE + SMI_BASE + FCH_SMI_REG18) = SwSciEn;

        EventEn = ACPIMMIO32 (ACPI_MMIO_BASE + SMI_BASE + FCH_SMI_REG04);
        EventEn |= LocalCfgPtr->Misc.Cppc.SciBit;   //Event Enable
        ACPIMMIO32 (ACPI_MMIO_BASE + SMI_BASE + FCH_SMI_REG04) = EventEn;
    }
  }
}

/**
 * FchI2cReleaseControlLate - Release SPD_HOST_CTRL_L
 *
 *
 *
 * @param[in] FchDataPtr
 *
 */
VOID
FchI2cReleaseControlLate (
  IN  VOID     *FchDataPtr
  )
{
  UINT32          I2cAddress;
  UINT32          Value32;
  UINT32          SmuStatus;
  FCH_DATA_BLOCK  *LocalCfgPtr;

  PCI_ADDR        NbioPciAddress;
  UINT32          SmuArg[6];
  EFI_STATUS      Status;
  UINT32          DimmsPerChannel = FixedPcdGet8 (PcdAmdMemMaxDimmPerChannelV2);
  UINT32          FenceStartAddress = 0x00000000;
  UINT32          FenceEndAddress = 0x00000000;
  BOOLEAN         IsFencingEnabled = TRUE;

  SmuStatus   = 0;
  LocalCfgPtr = (FCH_DATA_BLOCK *) FchDataPtr;
  I2cAddress  = ACPI_MMIO_BASE + GPIO_BANK0_BASE + FCH_BP_AGPIO3_SPD_HOST_CTRL_L;

  IDS_HDT_CONSOLE (FCH_TRACE, "[%a] start.\n", __FUNCTION__);

  Value32 = ACPIMMIO32 (I2cAddress);
  IDS_HDT_CONSOLE (FCH_TRACE, "SPD_HOST_CTRL_L value = 0x%x.\n", Value32);

  if ( LocalCfgPtr->HwAcpi.SpdHostCtrlRelease ) {
    IDS_HDT_CONSOLE (FCH_TRACE, "Release SPD host controller to BMC.\n");
    Value32 |= BIT23;   // Output enable
    Value32 |= BIT22;   // Output high
    ACPIMMIO32 (I2cAddress) = Value32;

    // Wait for 10uSec before continuing to the next step, to allow time for the SPD mux to switch
    // MicroSecondDelay(10);

    // Value32 = ACPIMMIO32 (I2cAddress);
    //  DEBUG((EFI_D_ERROR, "set SPD_HOST_CTRL_L back to high = 0x%x.\n", Value32));
  } else {
    IDS_HDT_CONSOLE (FCH_TRACE, "Do not release SPD host controller to BMC.\n");
    if ( LocalCfgPtr->HwAcpi.DimmTelemetry ) {
      IDS_HDT_CONSOLE (
        FCH_TRACE,
        "PcdRTDeviceEnableMap=0x%x, PcdRTDeviceEnableMapEx=0x%x\n",
        LocalCfgPtr->FchRunTime.FchDeviceEnableMap,
        LocalCfgPtr->FchRunTime.FchDeviceEnableMapEx
        );

       // Initialize argument for sending message to PMFW
      NbioSmuServiceCommonInitArguments (SmuArg);
      NbioPciAddress.AddressValue = MAKE_SBDFO (0, 0, 0, 0, 0);

      if ( LocalCfgPtr->FchRunTime.FchDeviceEnableMap & BIT21 ) {
        SmuArg[0] = 1;  // Use I3C controller
        FenceStartAddress = FCH_I3C_DIMM_TELEMETRY_START_ADDRESS;

        if ( DimmsPerChannel == 1 ) { //1DPC
          FenceEndAddress = FCH_I3C_DIMM_TELEMETRY_END_ADDRESS_1DPC;
        } else if ( DimmsPerChannel == 2 ) {  //2DPC
          FenceEndAddress = FCH_I3C_DIMM_TELEMETRY_END_ADDRESS_2DPC;
        }
      } else if ( LocalCfgPtr->FchRunTime.FchDeviceEnableMap & BIT5 ) {
        SmuArg[0] = 0;  // Use I2C controller
        FenceStartAddress = FCH_I2C_DIMM_TELEMETRY_START_ADDRESS;

        if ( DimmsPerChannel == 1 ) { //1DPC
          FenceEndAddress = FCH_I2C_DIMM_TELEMETRY_END_ADDRESS_1DPC;
        } else if ( DimmsPerChannel == 2 ) {  //2DPC
          FenceEndAddress = FCH_I2C_DIMM_TELEMETRY_END_ADDRESS_2DPC;
        }
      }

      IsFencingEnabled = PcdGetBool (PcdAmdFchIxcTelemetryPortsFenceControl);

      if (IsFencingEnabled) {
        IDS_HDT_CONSOLE (FCH_TRACE, "Send mailbox command to PSP for fencing DIMM Telemetry Ixc ports.\n");
        Status = PspMboxBiosFenceI2cI3cPort (FenceStartAddress, FenceEndAddress);
        if ( Status != EFI_SUCCESS ) {
          IDS_HDT_CONSOLE (FCH_TRACE, "[Error] PspMboxBiosFenceI2cI3cPort failed\n");
        }
      } else {
        IDS_HDT_CONSOLE (FCH_TRACE, "IxC Fencing control is disabled.\n");
      }

      IDS_HDT_CONSOLE (FCH_TRACE, "Send message to SMU for PMFW poll DIMM telemetry.\n");
      SmuStatus = NbioSmuServiceRequest (
                    NbioPciAddress,
                    BIOSSMC_MSG_StartDimmTelemetryReading,
                    SmuArg,
                    0
                    );
      if ( BIOSSMC_Result_OK == SmuStatus ) {
        IDS_HDT_CONSOLE (FCH_TRACE, "Send message to SMU for PMFW poll DIMM telemetry successfully.\n");
      } else {
        IDS_HDT_CONSOLE (FCH_TRACE, "Send message to SMU for PMFW poll DIMM telemetry failed.\n");
      }
    }
  }

  return;
}

