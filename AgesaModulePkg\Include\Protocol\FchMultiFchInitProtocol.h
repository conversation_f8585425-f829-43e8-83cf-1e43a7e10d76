/*****************************************************************************
 * Copyright (C) 2008-2024 Advanced Micro Devices, Inc. All rights reserved.
 *
*****************************************************************************
*/
/* $NoKeywords:$ */
/**
 * @file
 *
 * FCH DXE
 *
 *
 * @xrefitem bom "File Content Label" "Release Content"
 * @e project:      AGESA
 * @e sub-project   FCH DXE
 * @e \$Revision: 309090 $   @e \$Date: 2014-12-09 10:28:05 -0800 (Tue, 09 Dec 2014) $
 *
 */

#ifndef _FCH_MULTI_FCH_INIT_PROTOCOL_H_
#define _FCH_MULTI_FCH_INIT_PROTOCOL_H_

/**
 * @brief FCH Multi-socket Initialization protocol GUID
 */
extern EFI_GUID gFchMultiFchInitProtocolGuid;

/**
 * @brief Max number of dies per socket
 */
#define MULTI_FCH_MAX_NUMBER_OF_DIE_PER_SOCKET 8

/**
 * @brief Max number of sockets
 */
#define MULTI_FCH_MAX_NUMBER_OF_SOCKETS        8

/**
 * @brief Max number of MMIOs
 */
#define MULTI_FCH_MAX_NUMBER_OF_MMIO           \
  ((MULTI_FCH_MAX_NUMBER_OF_DIE_PER_SOCKET)*(MULTI_FCH_MAX_NUMBER_OF_SOCKETS))

/**
 * @brief Forward declaration for the FCH_MULTI_FCH_INIT_PROTOCOL
 */
typedef struct _FCH_MULTI_FCH_INIT_PROTOCOL FCH_MULTI_FCH_INIT_PROTOCOL;

/**
 * @brief Get MMIO base address for multiple FCHs
 *
 * @param This            Pointer to data structure FCH_MULTI_FCH_INIT_PROTOCOL
 * @param SocketNum       Socket Number
 * @param DieNum          Die Number
 * @param MmioBaseAddress Pointer to MMIO base address
 */
typedef EFI_STATUS (*FCH_MULITI_FCH_GET_MMIO_BASE_ADDR_DXE) (
  FCH_MULTI_FCH_INIT_PROTOCOL *This,
  UINTN                       SocketNum,
  UINTN                       DieNum,
  UINT64                      *MmioBaseAddress
  );

/**
 * @brief FCH Data Block structure
 */
typedef struct _FCH_MULITI_FCH_DATA_BLOCK {
  UINT64                FchAcpiMmioBase[MULTI_FCH_MAX_NUMBER_OF_MMIO];  ///< MMIO base of multi-FCH
} FCH_MULITI_FCH_DATA_BLOCK;


/**
 * @brief MULTI FCH INIT Protocol
 */
typedef struct _FCH_MULTI_FCH_INIT_PROTOCOL {
  UINTN                                  Revision;                  ///< Protocol Revision
  FCH_MULITI_FCH_DATA_BLOCK              FchMfData;                 ///< Data Block of FCH
  UINTN                                  TotalNumberOfSockets;      ///< Total number of sockets in system
  UINTN                                  TotalNumberOfDies;         ///< Total number of dies in system
  UINTN                                  TotalNumberOfRootBridges;  ///< Total root bridge of dies in system
  UINTN                                  DataBlockSize;             ///< Size of data block
  FCH_MULITI_FCH_GET_MMIO_BASE_ADDR_DXE  FchMfGetMmioBaseAddr;      ///< Pointer of procedure which gets MMIO base
} FCH_MULTI_FCH_INIT_PROTOCOL;

/**
 * @brief current Protocol revision
 */
#define FCH_MULTI_FCH_INIT_REV  0x02

#endif // _FCH_MULTI_FCH_INIT_PROTOCOL_H_



