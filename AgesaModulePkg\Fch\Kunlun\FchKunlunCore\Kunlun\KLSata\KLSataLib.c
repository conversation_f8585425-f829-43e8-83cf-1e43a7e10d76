/*****************************************************************************
 * Copyright (C) 2008-2024 Advanced Micro Devices, Inc. All rights reserved.
 *
*****************************************************************************
*/

/* $NoKeywords:$ */
/**
 * @file
 *
 * Fch SATA controller Library
 *
 * SATA Library
 *
 * @xrefitem bom "File Content Label" "Release Content"
 * @e project:     AGESA
 * @e sub-project: FCH
 * @e \$Revision: 309083 $   @e \$Date: 2014-12-09 09:28:24 -0800 (Tue, 09 Dec 2014) $
 *
 */

#include "FchPlatform.h"
#include "Filecode.h"
#define FILECODE FCH_KUNLUN_FCHKUNLUNCORE_KUNLUN_KLSATA_KLSATALIB_FILECODE

#define MAX_RETRY_NUM   200

VOID
SataEnableWriteAccessKL (
  IN  UINT32              DieBusNum,
  IN  UINT32              Controller
  )
{
  FchSmnRW (DieBusNum, FCH_KL_SMN_SATA_CONTROL_SLOR + 0x00 + Controller * FCH_KL_SMN_SATA_STEP, 0xFFFFFFFE, BIT0, NULL);
}

VOID
SataDisableWriteAccessKL (
  IN  UINT32              DieBusNum,
  IN  UINT32              Controller
  )
{
  FchSmnRW (DieBusNum, FCH_KL_SMN_SATA_CONTROL_SLOR + 0x00 + Controller * FCH_KL_SMN_SATA_STEP, 0xFFFFFFFE, 0, NULL);
}

/**
 * FchKLSataEnableSataMac - Enable Sata Controller
 *
 *
 * @param[in] DieBusNum  IOCH bus number on current Die.
 * @param[in] Controller Sata controller number.
 * @param[in] FchDataPtr Fch configuration structure pointer.
 *
 */
VOID
FchKLSataEnableSataMac (
  IN  UINT32   DieBusNum,
  IN  UINT32   Controller,
  IN  VOID     *FchDataPtr
  )
{
  UINT8                       Port;
  UINT8                       RxPolarity;
  FCH_RESET_DATA_BLOCK        *LocalCfgPtr;
  LocalCfgPtr = (FCH_RESET_DATA_BLOCK *)FchDataPtr;
  RxPolarity  = LocalCfgPtr->SataRxPolarity[Controller];

  FchSmnRW (DieBusNum, FCH_KL_SATA_MISC_CONTROL + Controller * FCH_KL_SMN_SATA_STEP, 0xFFFFFFFE, 0, NULL);

  // RxPolarity
  for (Port = 0; Port < KUNLUN_SATA_PORT_NUM; Port++) {
    if (RxPolarity & (1 << Port)) {
      FchSmnRW (DieBusNum, FCH_KL_SMN_SATA_CONTROL_SLOR + Controller * FCH_KL_SMN_SATA_STEP + Port * 0x20 + 0x80, 0xFFFFFFDF, BIT5, NULL);
    }
  }
}

/**
 * FchKLSataEnableSataStaggeredSpinup - Enable Sata Staggered Spin-up feature
 *
 *
 * @param[in] DieBusNum  IOCH bus number on current Die.
 * @param[in] Controller Sata controller number.
 * @param[in] FchDataPtr Fch configuration structure pointer.
 *
 */
VOID
FchKLSataEnableSataStaggeredSpinup (
  IN  UINT32   DieBusNum,
  IN  UINT32   Controller,
  IN  VOID     *FchDataPtr
  )
{
  UINT32                PortNum;
  FCH_RESET_DATA_BLOCK  *LocalCfgPtr;

  PortNum     = 0;
  LocalCfgPtr = (FCH_RESET_DATA_BLOCK *)FchDataPtr;

  if ( LocalCfgPtr->SataStaggeredSpinupEnable ) {
    // 1. Program cfg_cap_sss bit (Sata rsmu0x00[7]) to 1b
    FchSmnRW (
      DieBusNum,
      FCH_KL_SATA_MISC_CONTROL + (Controller * FCH_KL_SMN_SATA_STEP),
      (UINT32)(~(BIT7)),
      (UINT32)BIT7,
      NULL
      );

    // 2. Write 0b to PxCmd.SUD for each port
    for (PortNum = 0; PortNum < KUNLUN_SATA_PORT_NUM; PortNum++) {
      FchSmnRW (
        DieBusNum,
        FCH_KL_SMN_SATA_CONTROL_BAR5 + Controller * FCH_KL_SMN_SATA_STEP + FCH_SATA_BAR5_REG118 + PortNum * 0x80,
        (UINT32)(~(BIT1)),
        0,
        NULL
        );
    }
  }
}

/**
 * FchKLSataInitEnableSata - Enable Sata Controller
 *
 *
 * @param[in] DieBusNum  IOCH bus number on current Die.
 * @param[in] Controller Sata controller number.
 * @param[in] FchDataPtr Fch configuration structure pointer.
 *
 */
VOID
FchKLSataInitEnableSata (
  IN  UINT32   DieBusNum,
  IN  UINT32   Controller,
  IN  VOID     *FchDataPtr
  )
{
//  FCH_RESET_DATA_BLOCK        *LocalCfgPtr;
//  LocalCfgPtr = (FCH_RESET_DATA_BLOCK *)FchDataPtr;

  FchKLSataEnableSataMac (DieBusNum, Controller, FchDataPtr);

  FchKLSataEnableSataStaggeredSpinup (DieBusNum, Controller, FchDataPtr);
}

/**
 * FchKLSataInitDisableSata - Disable Sata Controller
 *
 *
 * @param[in] DieBusNum  IOCH bus number on current Die.
 * @param[in] Controller Sata controller number.
 * @param[in] FchDataPtr Fch configuration structure pointer.
 *
 */
VOID
FchKLSataInitDisableSata (
  IN  UINT32   DieBusNum,
  IN  UINT32   Controller,
  IN  VOID     *FchDataPtr
  )
{
  IDS_HDT_CONSOLE (
    FCH_TRACE,
    "[FCH][SATA] %a Disable SATA IP (DieBusNum:0x%x, Controller:0x%x)\n",
    __FUNCTION__,
    DieBusNum,
    Controller
    );

  FchSmnRW (DieBusNum, FCH_KL_SATA_MISC_CONTROL + Controller * FCH_KL_SMN_SATA_STEP, 0xfffffffe, BIT0, NULL);
  FchSmnRW (DieBusNum, FCH_KL_SATA_MISC_CONTROL + Controller * FCH_KL_SMN_SATA_STEP, ~(UINT32) BIT11, BIT11, NULL);

  /*
  Because of security consideration, x86 is forbidden to access nBIF straps.
  Move code to ABL.

  //Hide Sata Pci header
  FchKLSataInitHideSataPci (DieBusNum, Controller, FchDataPtr);
  */
}


/*
Because of security consideration, x86 is forbidden to access nBIF straps.
Move code to ABL.
*/
#if 0
/**
 * FchKLSataInitHideSataDummyF0 - Disable Sata Controller PCI
 * configuration space and set it to Dummy funciton0
 *
 *
 * @param[in] DieBusNum  IOCH bus number on current Die.
 * @param[in] Controller Sata controller number.
 * @param[in] FchDataPtr Fch configuration structure pointer.
 *
 */
VOID
FchKLSataInitHideSataDummyF0 (
  IN  UINT32   DieBusNum,
  IN  UINT32   Controller,
  IN  VOID     *FchDataPtr
  )
{
  UINT32                SataBridgeAddress;
//  FCH_RESET_DATA_BLOCK  *LocalCfgPtr;

  SataBridgeAddress = 0;
//  LocalCfgPtr       = (FCH_RESET_DATA_BLOCK *)FchDataPtr;

  IDS_HDT_CONSOLE (FCH_TRACE, "[FCH]FchKLSataInitHideSataDummyF0 Start!\n");

  if ((Controller != 0) && (Controller != 2)) {
    // only Sata0 and Sata2 are on Function 0
    IDS_HDT_CONSOLE (FCH_TRACE, "[FCH]FchKLSataInitHideSataDummyF0 Sata%x is not on Function0!\n", Controller);
    return;
  }

  SataBridgeAddress = FCH_KL_SATA0_RCC_DEV1_PORT_STRAP0 + FCH_KL_SMN_SATA_STEP * Controller;
  FchSmnRW (DieBusNum, SataBridgeAddress, ~(UINT32) BIT31, BIT31, NULL); //dummy Function 0
  SataBridgeAddress = FCH_KL_SMN_SATA0_NBIF_STRAP0 + FCH_KL_SMN_SATA_STEP * Controller;
  FchSmnRW (DieBusNum, SataBridgeAddress, 0xFFFF0000, 0x1452, NULL); //RCC_STRAP:RCC_DEV1_EPF0_STRAP0
  SataBridgeAddress = FCH_KL_SMN_SATA0_NBIF_STRAP13 + FCH_KL_SMN_SATA_STEP * Controller;
  FchSmnRW (DieBusNum, SataBridgeAddress, 0xFF000000, 0x00130000, NULL); //RCC_STRAP:RCC_DEV1_EPF0_STRAP13
  SataBridgeAddress = FCH_KL_SMN_SATA0_NBIF_STRAP4 + FCH_KL_SMN_SATA_STEP * Controller;
  FchSmnRW (DieBusNum, SataBridgeAddress, 0x807FFFFF, 0, NULL); //RCC_STRAP:RCC_DEV1_EPF0_STRAP4
  SataBridgeAddress = FCH_KL_SMN_SATA0_NBIF_STRAP3 + FCH_KL_SMN_SATA_STEP * Controller;
  FchSmnRW (DieBusNum, SataBridgeAddress, ~(UINT32) (BIT18 + BIT20), 0, NULL); //RCC_STRAP:RCC_DEV1_EPF0_STRAP3
  SataBridgeAddress = FCH_KL_SMN_SATA0_NBIF_STRAP2 + FCH_KL_SMN_SATA_STEP * Controller;
  FchSmnRW (DieBusNum, SataBridgeAddress, ~(UINT32) BIT8, 0, NULL); //RCC_STRAP:RCC_DEV1_EPF0_STRAP2
  SataBridgeAddress = FCH_KL_SATA0_VWR_VCHG_DIS_CTRL + FCH_KL_SMN_SATA_STEP * Controller;
  FchSmnRW (DieBusNum, SataBridgeAddress, ~(UINT32) (BIT7 + BIT8 + BIT9 +BIT10), (BIT7 + BIT8 + BIT9 +BIT10), NULL);
  IDS_HDT_CONSOLE (FCH_TRACE, "[FCH]FchKLSataInitHideSataDummyF0 Complete!\n");
}


/**
 * FchKLSataInitHideSataPci - Disable Sata Controller PCI
 * configuration space.
 *
 *
 * @param[in] DieBusNum  IOCH bus number on current Die.
 * @param[in] Controller Sata controller number.
 * @param[in] FchDataPtr Fch configuration structure pointer.
 *
 */
VOID
FchKLSataInitHideSataPci (
  IN  UINT32   DieBusNum,
  IN  UINT32   Controller,
  IN  VOID     *FchDataPtr
  )
{
  UINT32                SataBridgeAddress;
//  FCH_RESET_DATA_BLOCK  *LocalCfgPtr;

  SataBridgeAddress = 0;
//  LocalCfgPtr       = (FCH_RESET_DATA_BLOCK *)FchDataPtr;

  if ((Controller == 0) || (Controller == 2)) {
    // Remove it since ABL take care of it.
    // FchKLSataInitHideSataDummyF0 (DieBusNum, Controller, FchDataPtr);
  } else {
    SataBridgeAddress = FCH_KL_SMN_SATA1_NBIF_STRAP0 + FCH_KL_SMN_SATA_STEP * ( Controller - 1 );
    IDS_HDT_CONSOLE (
      FCH_TRACE,
      "[FCH][SATA] %a Hide SATA PCI agent. DieBusNum 0x%x, nBIF SATA %d, SMN 0x%x set BIT28\n",
      __FUNCTION__,
      DieBusNum,
      Controller,
      SataBridgeAddress
      );
    FchSmnRW (DieBusNum, SataBridgeAddress, ~(UINT32) BIT28, 0, NULL);
  }
}
#endif

/**
 * FchKLSataInitHideUnconnectedSataPci - Disable Unconnected
 * Sata Controller PCI configuration space.
 *
 *
 * @param[in] DieBusNum  IOCH bus number on current Die.
 * @param[in] FchDataPtr Fch configuration structure pointer.
 *
 */
VOID
FchKLSataInitHideUnconnectedSataPci (
  IN  UINT32   DieBusNum,
  IN  VOID     *FchDataPtr
  )
{
//  FCH_RESET_DATA_BLOCK        *LocalCfgPtr;

//  LocalCfgPtr = (FCH_RESET_DATA_BLOCK *)FchDataPtr;

}

/**
 * FchKLSataInitHideNbifDev1Pci - Hide nBIF0 Dev1
 * PCI configuration space.
 *
 *
 * @param[in] DieBusNum  IOCH bus number on current Die.
 * @param[in] NbioSata   Nbio/Iohc number of Sata controller.
 * @param[in] FchDataPtr Fch configuration structure pointer.
 *
 */
VOID
FchKLSataInitHideNbifDev1Pci (
  IN  UINT32   DieBusNum,
  IN  UINT32   NbioSata,
  IN  VOID     *FchDataPtr
  )
{
  UINT32       SataBridgeAddress;
  SataBridgeAddress = FCH_KL_NBIF1DEVINDCFG1_IOHC_Bridge_CNTL;
  SataBridgeAddress += FCH_KL_NBIF1DEVINDCFG1_IOHC_Bridge_CNTL_NBIO_STEP * NbioSata;
  IDS_HDT_CONSOLE (
    FCH_TRACE,
    "[FCH][SATA] %a Hide Nbif Dev1 Pci Bridge. DieBusNum 0x%x, nBIF Sata %d, SMN address 0x%x set 0x7\n",
    __FUNCTION__,
    DieBusNum,
    NbioSata,
    SataBridgeAddress
    );
  FchSmnRW (DieBusNum, SataBridgeAddress, ~(UINT32) (0x7), 0x7, NULL);
}

/**
 * FchKLSataInitEnableNbifDev1Pci - Enable nBIF0 Dev1
 * PCI configuration space.
 *
 *
 * @param[in] DieBusNum  IOCH bus number on current Die.
 * @param[in] NbioSata   NbioSata controller number.
 * @param[in] FchDataPtr Fch configuration structure pointer.
 *
 */
VOID
FchKLSataInitEnableNbifDev1Pci (
  IN  UINT32   DieBusNum,
  IN  UINT32   NbioSata,
  IN  VOID     *FchDataPtr
  )
{
  UINT32       SataBridgeAddress;
  SataBridgeAddress = FCH_KL_NBIF1DEVINDCFG1_IOHC_Bridge_CNTL;
  SataBridgeAddress += FCH_KL_NBIF1DEVINDCFG1_IOHC_Bridge_CNTL_NBIO_STEP * NbioSata;
  FchSmnRW (DieBusNum, SataBridgeAddress, ~(UINT32) (0x7), 0, NULL);
}

/**
 * FchKLSataInitCheckNbifDev1Pci - Check nBIF0 Dev1
 * PCI configuration space.
 *
 *
 * @param[in] DieBusNum  IOCH bus number on current Die.
 * @param[in] NbioSata   NbioSata controller number.
 * @param[in] FchDataPtr Fch configuration structure pointer.
 *
 */
BOOLEAN
FchKLSataInitCheckNbifDev1Pci (
  IN  UINT32   DieBusNum,
  IN  UINT32   NbioSata,
  IN  VOID     *FchDataPtr
  )
{
  UINT32       SataBridgeAddress;
  UINT32       SataBridgeRegVal;
  SataBridgeAddress = FCH_KL_NBIF1DEVINDCFG1_IOHC_Bridge_CNTL;
  SataBridgeAddress += FCH_KL_NBIF1DEVINDCFG1_IOHC_Bridge_CNTL_NBIO_STEP * NbioSata;
  FchSmnRead (DieBusNum, SataBridgeAddress, &SataBridgeRegVal, NULL);
  if ((SataBridgeRegVal & 0x7) == 0x0) {
    return TRUE;
  }
  return FALSE;
}

/**
 * FchKLSataInitPortOffline - Put Sata Port in Offline mode
 *
 *
 * @param[in] DieBusNum  IOCH bus number on current Die.
 * @param[in] Controller Sata controller number.
 * @param[in] PortNum    Sata Port to put in offline.
 * @param[in] FchDataPtr Fch configuration structure pointer.
 *
 */
VOID
FchKLSataInitPortOffline (
  IN  UINT32   DieBusNum,
  IN  UINT32   Controller,
  IN  UINT32   PortNum,
  IN  VOID     *FchDataPtr
  )
{
  UINT32                      Retry;
  UINT32                      PortStatus;
  UINT32                      SataRegAddr;
  FCH_RESET_DATA_BLOCK        *LocalCfgPtr;
  LocalCfgPtr = (FCH_RESET_DATA_BLOCK *)FchDataPtr;

  SataRegAddr = FCH_KL_SMN_SATA_CONTROL_BAR5 + Controller * FCH_KL_SMN_SATA_STEP + FCH_SATA_BAR5_REG12C + PortNum * 0x80;
  FchSmnRW (DieBusNum, SataRegAddr, 0xFFFFFFF0, 0x4, LocalCfgPtr->StdHeader);

  for (Retry = 0; Retry < MAX_RETRY_NUM; Retry++) {
    SataRegAddr = FCH_KL_SMN_SATA_CONTROL_BAR5 + Controller * FCH_KL_SMN_SATA_STEP + FCH_SATA_BAR5_REG128 + PortNum * 0x80;
    FchSmnRead (DieBusNum, SataRegAddr, &PortStatus, LocalCfgPtr->StdHeader);
    if ((PortStatus & 0x0F) == 0x4) {
      break;
    }
    FchStall (10, LocalCfgPtr->StdHeader);
  }
}

/**
 * FchKLSataInitPortActive - Put Sata Port back to active from
 * Offline mode
 *
 *
 * @param[in] DieBusNum  IOCH bus number on current Die.
 * @param[in] Controller Sata controller number.
 * @param[in] PortNum    Sata Port to put in offline.
 * @param[in] FchDataPtr Fch configuration structure pointer.
 *
 */
VOID
FchKLSataInitPortActive (
  IN  UINT32   DieBusNum,
  IN  UINT32   Controller,
  IN  UINT32   PortNum,
  IN  VOID     *FchDataPtr
  )
{
  UINT32                      SataRegAddr;
  FCH_RESET_DATA_BLOCK        *LocalCfgPtr;
  LocalCfgPtr = (FCH_RESET_DATA_BLOCK *)FchDataPtr;

  SataRegAddr = FCH_KL_SMN_SATA_CONTROL_SLOR + 0x00 + Controller * FCH_KL_SMN_SATA_STEP;
  FchSmnRW (DieBusNum, SataRegAddr, ~(UINT32) (BIT16 << PortNum), 0, LocalCfgPtr->StdHeader);
  SataRegAddr = FCH_KL_SMN_SATA_CONTROL_BAR5 + Controller * FCH_KL_SMN_SATA_STEP + FCH_SATA_BAR5_REG12C + PortNum * 0x80;
  FchSmnRW (DieBusNum, SataRegAddr, 0xFFFFFFF0, 0x1, LocalCfgPtr->StdHeader);
  FchStall (1000, LocalCfgPtr->StdHeader);
  SataRegAddr = FCH_KL_SMN_SATA_CONTROL_BAR5 + Controller * FCH_KL_SMN_SATA_STEP + FCH_SATA_BAR5_REG12C + PortNum * 0x80;
  FchSmnRW (DieBusNum, SataRegAddr, 0xFFFFFFF0, 0x0, LocalCfgPtr->StdHeader);
  FchStall (1000, LocalCfgPtr->StdHeader);
}

/**
 * FchKLSataInitPortClearFre - Clear PxCMD.FRE
 *
 *
 * @param[in] DieBusNum  IOCH bus number on current Die.
 * @param[in] Controller Sata controller number.
 * @param[in] PortNum    Sata Port to put in offline.
 * @param[in] FchDataPtr Fch configuration structure pointer.
 *
 */
VOID
FchKLSataInitPortClearFre (
  IN  UINT32   DieBusNum,
  IN  UINT32   Controller,
  IN  UINT32   PortNum,
  IN  VOID     *FchDataPtr
  )
{
  UINT32                      Retry;
  UINT32                      PortCmd;
  UINT32                      SataRegAddr;
  FCH_RESET_DATA_BLOCK        *LocalCfgPtr;
  LocalCfgPtr = (FCH_RESET_DATA_BLOCK *)FchDataPtr;

  SataRegAddr = FCH_KL_SMN_SATA_CONTROL_BAR5 + Controller * FCH_KL_SMN_SATA_STEP + FCH_SATA_BAR5_REG118 + PortNum * 0x80;
  FchSmnRead (DieBusNum, SataRegAddr, &PortCmd, LocalCfgPtr->StdHeader);
  if (PortCmd & BIT4) {                                                                // PxCMD.FRE == 1
    FchSmnRW (DieBusNum, SataRegAddr, 0xFFFFFFEF, 0x0, LocalCfgPtr->StdHeader);        // PxCMD.FRE == 0

    for (Retry = 0; Retry < MAX_RETRY_NUM; Retry++) {
      FchSmnRead (DieBusNum, SataRegAddr, &PortCmd, LocalCfgPtr->StdHeader);
      if ((PortCmd & BIT14) == 0x0) {
        break;
      }
      FchStall (1, LocalCfgPtr->StdHeader);
    }
  }
}

/**
 * FchKLSataInitEnableErr - Enable Error reporting
 *
 *
 * @param[in] DieBusNum  Bus Number of current Die.
 * @param[in] Controller Sata controller number.
 * @param[in] FchDataPtr Fch configuration structure pointer.
 *
 */
VOID
FchKLSataInitEnableErr (
  IN  UINT32   DieBusNum,
  IN  UINT32   Controller,
  IN VOID      *FchDataPtr
  )
{
  UINT32                 PortNum;
  UINT32                 SataRegAddr;
  BOOLEAN                FchD2HFifoParityErr;
  BOOLEAN                FchH2DFifoParityErr;
  BOOLEAN                FchContextMemoryParityErr;
  BOOLEAN                FchSataRasSupport;
  FCH_DATA_BLOCK         *LocalCfgPtr;
  AMD_CONFIG_PARAMS      *StdHeader;

  LocalCfgPtr = (FCH_DATA_BLOCK *) FchDataPtr;
  StdHeader = LocalCfgPtr->StdHeader;

  FchSataRasSupport = LocalCfgPtr->Sata[Controller].SataRasSupport;
  FchD2HFifoParityErr = TRUE;          //Optional
  FchH2DFifoParityErr = TRUE;          //Optional
  FchContextMemoryParityErr = TRUE;    //Optional

  if ( FchSataRasSupport ) {
    //
    // (Optional) Enable Report D2H FIFO Parity Error to System for each port.
    //
    if ( FchD2HFifoParityErr ) {
      for ( PortNum = 0; PortNum < KUNLUN_SATA_PORT_NUM; PortNum++ ) {
        SataRegAddr = FCH_KL_SMN_SATA_CONTROL_SLOR + Controller * FCH_KL_SMN_SATA_STEP + PortNum * 0x20 + 0x88;
        FchSmnRW (DieBusNum, SataRegAddr, ~(UINT32) BIT4, BIT4, StdHeader);
      }
    }

    //
    // (Optional) Enable Report H2D FIFO Parity Error to System for each port
    //
    if ( FchH2DFifoParityErr ) {
      for ( PortNum = 0; PortNum < KUNLUN_SATA_PORT_NUM; PortNum++ ) {
        SataRegAddr = FCH_KL_SMN_SATA_CONTROL_SLOR + Controller * FCH_KL_SMN_SATA_STEP + PortNum * 0x20 + 0x88;
        FchSmnRW (DieBusNum, SataRegAddr, ~(UINT32) BIT5, BIT5, StdHeader);
      }
    }

    //
    // (Optional) Enable Report Context Memory Parity Error to System for each port
    //
    if ( FchContextMemoryParityErr ) {
      for ( PortNum = 0; PortNum < KUNLUN_SATA_PORT_NUM; PortNum++ ) {
        SataRegAddr = FCH_KL_SMN_SATA_CONTROL_SLOR + Controller * FCH_KL_SMN_SATA_STEP + PortNum * 0x20 + 0x88;
        FchSmnRW (DieBusNum, SataRegAddr, ~(UINT32) BIT6, BIT6, StdHeader);
      }
    }
  }

  //
  //Clear error status
  //
  for ( PortNum = 0; PortNum < KUNLUN_SATA_PORT_NUM; PortNum++ ) {
    SataRegAddr = FCH_KL_SMN_SATA_CONTROL_BAR5 + Controller * FCH_KL_SMN_SATA_STEP + FCH_SATA_BAR5_REG130 + PortNum * 0x80;
    FchSmnRW (DieBusNum, SataRegAddr, 0xFFFFFFFF, 0xFFFFFFFF, StdHeader);
    SataRegAddr = FCH_KL_SMN_SATA_CONTROL_BAR5 + Controller * FCH_KL_SMN_SATA_STEP + FCH_SATA_BAR5_REG110 + PortNum * 0x80;
    FchSmnRW (DieBusNum, SataRegAddr, 0xFFFFFFFF, 0x00, StdHeader);
  }
}

/**
 * FchKLSataInitEsata - Enable eSATA port
 *
 *
 * @param[in] DieBusNum  Bus Number of current Die.
 * @param[in] Controller Sata controller number.
 * @param[in] FchDataPtr Fch configuration structure pointer.
 *
 */
VOID
FchKLSataInitEsata (
  IN  UINT32   DieBusNum,
  IN  UINT32   Controller,
  IN VOID      *FchDataPtr
  )
{
  UINT32                 SataRegAddr;
  FCH_DATA_BLOCK         *LocalCfgPtr;
  AMD_CONFIG_PARAMS      *StdHeader;

  LocalCfgPtr = (FCH_DATA_BLOCK *) FchDataPtr;
  StdHeader = LocalCfgPtr->StdHeader;

  //
  // SATA ESP port setting
  // These config bits are set for SATA driver to identify which ports are external SATA ports and need to
  // support hotplug. If a port is set as an external SATA port and need to support hotplug, then driver will
  // not enable power management (HIPM & DIPM) for these ports.
  //
  if ( LocalCfgPtr->Sata[Controller].SataEspPort != 0 ) {
    SataRegAddr = FCH_KL_SMN_SATA_CONTROL_BAR5 + Controller * FCH_KL_SMN_SATA_STEP + FCH_SATA_BAR5_REGF8;
    FchSmnRW (DieBusNum, SataRegAddr, ~(LocalCfgPtr->Sata[Controller].SataEspPort), 0, StdHeader);
    FchSmnRW (DieBusNum, SataRegAddr, 0xFF00FFFF, (LocalCfgPtr->Sata[Controller].SataEspPort << 16), StdHeader);
    //
    // External SATA Port Indication Registers
    // If any of the ports was programmed as an external port, HCAP.SXS should also be set
    //
    SataRegAddr = FCH_KL_SMN_SATA_CONTROL_BAR5 + Controller * FCH_KL_SMN_SATA_STEP + FCH_SATA_BAR5_REGFC;
    FchSmnRW (DieBusNum, SataRegAddr, ~(UINT32) (BIT20), BIT20, StdHeader);
    //
    // Enable  eSATA PHY power-saving when no device presented.
    //
    SataRegAddr = FCH_KL_SMN_SATA_CONTROL_SLOR + Controller * FCH_KL_SMN_SATA_STEP + 0x10;
    FchSmnRW (DieBusNum, SataRegAddr, ~(UINT32) (BIT29), BIT29, StdHeader);
  } else {
    //
    // External SATA Port Indication Registers
    // If any of the ports was programmed as an external port, HCAP.SXS should also be set (Clear for no ESP port)
    //
    SataRegAddr = FCH_KL_SMN_SATA_CONTROL_BAR5 + Controller * FCH_KL_SMN_SATA_STEP + FCH_SATA_BAR5_REGF8;
    FchSmnRW (DieBusNum, SataRegAddr, 0xFF00FF00, 0x00, StdHeader);
    SataRegAddr = FCH_KL_SMN_SATA_CONTROL_BAR5 + Controller * FCH_KL_SMN_SATA_STEP + FCH_SATA_BAR5_REGFC;
    FchSmnRW (DieBusNum, SataRegAddr, ~(UINT32) (BIT20), 0x00, StdHeader);
    SataRegAddr = FCH_KL_SMN_SATA_CONTROL_SLOR + Controller * FCH_KL_SMN_SATA_STEP + 0x10;
    FchSmnRW (DieBusNum, SataRegAddr, ~(UINT32) (BIT29), 0, StdHeader);
  }
}

/**
 * FchKLSataInitDevSlp - init DevSlp configuration
 *
 *
 * @param[in] DieBusNum  Bus Number of current Die.
 * @param[in] Controller Sata Controller Number.
 * @param[in] FchDataPtr Fch configuration structure pointer.
 *
 */
VOID
FchKLSataInitDevSlp (
  IN  UINT32   DieBusNum,
  IN  UINT32   Controller,
  IN VOID      *FchDataPtr
  )
{
  UINT8                  PortNumByte;
  FCH_DATA_BLOCK         *LocalCfgPtr;
  AMD_CONFIG_PARAMS      *StdHeader;

  LocalCfgPtr = (FCH_DATA_BLOCK *) FchDataPtr;
  StdHeader   = LocalCfgPtr->StdHeader;

  IDS_HDT_CONSOLE (FCH_TRACE, "%a Start\n", __FUNCTION__);

  IDS_HDT_CONSOLE (FCH_TRACE, "BusNum = 0x%x Controller = 0x%x MMIO = 0x%x\n", DieBusNum, Controller, LocalCfgPtr->HwAcpi.FchAcpiMmioBase);

  //
  // 1, Enable the DEVLSP related GPIO pad and put the it into open-drain mode
  // 2, Enable the capability bit for DevSlp support
  // 3, Program CFG_PxCMD2.DSP bit for the port which will support DEVSLP.
  // 4, Program PortX_DEVSLP_MAP to route any two ports to external PAD.
  //

  if ( LocalCfgPtr->Sata[Controller].SataDevSlpPort0 ) {
    PortNumByte = LocalCfgPtr->Sata[Controller].SataDevSlpPort0Num;

    // Pin: AGPIO5_DEVSLP0_SATA_ZP0_L, Func(0x01): DEVSLP0
    RwMem (LocalCfgPtr->HwAcpi.FchAcpiMmioBase + IOMUX_BASE + FCH_REG05, AccessWidth8, 0x00, 0x1);
    // Pin: BP_AGPIO5_DEVSLP0_SATA_ZP0_L, BIT23: Outputenable, BIT22: Outputvalue, BIT21: Pulldownenable, BIT20: Pulldowndisable
    RwMem (LocalCfgPtr->HwAcpi.FchAcpiMmioBase + GPIO_BANK0_BASE + FCH_GPIO_014_BP_AGPIO5, AccessWidth32, ~(UINT32) (BIT20 + BIT21 + BIT22 + BIT23), BIT23 | BIT20);

    // Set corresponding bit on FCH_SATA_BAR5_REGF4
    FchSmnRW (DieBusNum, (FCH_KL_SMN_SATA_CONTROL_BAR5 + Controller * FCH_KL_SMN_SATA_STEP) + FCH_SATA_BAR5_REGF4, 0xFFFFFFFF, BIT4 | (BIT8 << PortNumByte), StdHeader);
    // Set DevSlp0 port #
    FchSmnRW (DieBusNum, (FCH_KL_SMN_SATA_CONTROL_SLOR + Controller * FCH_KL_SMN_SATA_STEP) + FCH_SATA_BAR5_REG8C, 0xFFFF8FFF, (UINT32) (PortNumByte << 12), StdHeader);
  }

  if ( LocalCfgPtr->Sata[Controller].SataDevSlpPort1 ) {
    if (LocalCfgPtr->Sata[Controller].SataDevSlpPort0
      && LocalCfgPtr->Sata[Controller].SataDevSlpPort0Num == LocalCfgPtr->Sata[Controller].SataDevSlpPort1Num)
    {
      // Port1 is pointing to the same port as port 0. We should skip Port1 configuration.
      IDS_HDT_CONSOLE (FCH_TRACE, "WARNING: DevSlp 1 is pointing to the same controller/port as DevSlp 0's. We should skip DevSlp 1 configuration.\n");
      IDS_HDT_CONSOLE (FCH_TRACE, "%a End\n", __FUNCTION__);
      return;
    }

    PortNumByte = LocalCfgPtr->Sata[Controller].SataDevSlpPort1Num;

    // Pin: AGPIO6_DEVSLP1_SATA_ZP1_L, Func(0x01): DEVSLP1
    RwMem (LocalCfgPtr->HwAcpi.FchAcpiMmioBase + IOMUX_BASE + FCH_REG06, AccessWidth8, 0x00, 0x1);
    // Pin: BP_AGPIO6_DEVSLP1_SATA_ZP1_L, BIT23: Outputenable, BIT22: Outputvalue, BIT21: Pulldownenable, BIT20: Pulldowndisable
    RwMem (LocalCfgPtr->HwAcpi.FchAcpiMmioBase + GPIO_BANK0_BASE + FCH_GPIO_018_BP_AGPIO6, AccessWidth32, ~(UINT32) (BIT20 + BIT21 + BIT22 + BIT23), BIT23 | BIT20);

    // Set corresponding bit on FCH_SATA_BAR5_REGF4
    FchSmnRW (DieBusNum, (FCH_KL_SMN_SATA_CONTROL_BAR5 + Controller * FCH_KL_SMN_SATA_STEP) + FCH_SATA_BAR5_REGF4, 0xFFFFFFFF, BIT4 | (BIT8 << PortNumByte), StdHeader);
    // Set DevSlp1 port #
    FchSmnRW (DieBusNum, (FCH_KL_SMN_SATA_CONTROL_SLOR + Controller * FCH_KL_SMN_SATA_STEP) + FCH_SATA_BAR5_REGAC, 0xFFFF8FFF, (UINT32) (PortNumByte << 12), StdHeader);
  }

  IDS_HDT_CONSOLE (FCH_TRACE, "%a End\n", __FUNCTION__);
}

/**
 * FchKLSataInitMpssMap - Program Mechanical Presence Switch
 * State Mapping.
 *
 *
 * @param[in] DieBusNum  Bus Number of current Die
 * @param[in] FchDataPtr Fch configuration structure pointer.
 *
 */
VOID
FchKLSataInitMpssMap (
  IN  UINT32   DieBusNum,
  IN VOID      *FchDataPtr
  )
{
  UINT32                 PortNum;
  FCH_DATA_BLOCK         *LocalCfgPtr;
  AMD_CONFIG_PARAMS      *StdHeader;

  // only support MPSS on SATA0
  LocalCfgPtr = (FCH_DATA_BLOCK *) FchDataPtr;
  StdHeader = LocalCfgPtr->StdHeader;
  for (PortNum = 0; PortNum < KUNLUN_SATA_PORT_NUM; PortNum++) {
    FchSmnRW (DieBusNum, FCH_KL_SMN_SATA_CONTROL_SLOR + PortNum * 0x20 + 0x8C, 0xFFFFF8FF, (UINT32) (PortNum << 8), StdHeader);
  }
}

/**
 * FchKLSataInitRsmuCtrl - Sata Rsmu Control register setting
 *
 *
 * @param[in] DieBusNum  Bus Number of current Die.
 * @param[in] Controller Sata controller number.
 * @param[in] FchDataPtr Fch configuration structure pointer.
 *
 */
VOID
FchKLSataInitRsmuCtrl (
  IN  UINT32   DieBusNum,
  IN  UINT32   Controller,
  IN VOID      *FchDataPtr
  )
{
  FCH_DATA_BLOCK         *LocalCfgPtr;
  AMD_CONFIG_PARAMS      *StdHeader;

  LocalCfgPtr = (FCH_DATA_BLOCK *) FchDataPtr;
  StdHeader = LocalCfgPtr->StdHeader;

  //
  // SATA controller operates in maximum Gen2
  //
  if ( LocalCfgPtr->Sata[Controller].SataSetMaxGen2 ) {
    FchSmnRW (DieBusNum, FCH_KL_SATA_MISC_CONTROL + Controller * FCH_KL_SMN_SATA_STEP, ~(UINT32) BIT2, BIT2, StdHeader);
  } else {
    FchSmnRW (DieBusNum, FCH_KL_SATA_MISC_CONTROL + Controller * FCH_KL_SMN_SATA_STEP, ~(UINT32) BIT2, 0x00, StdHeader);
  }

  //
  // Sata Target Support 8 devices function
  //
  if ( LocalCfgPtr->Sata[Controller].SataTargetSupport8Device ) {
    FchSmnRW (DieBusNum, FCH_KL_SATA_MISC_CONTROL + Controller * FCH_KL_SMN_SATA_STEP, ~(UINT32) BIT3, BIT3, StdHeader);
  } else {
    FchSmnRW (DieBusNum, FCH_KL_SATA_MISC_CONTROL + Controller * FCH_KL_SMN_SATA_STEP, ~(UINT32) BIT3, 0x00, StdHeader);
  }

  //
  // Sata Generic Mode setting
  //
  if ( LocalCfgPtr->Sata[Controller].SataDisableGenericMode ) {
    FchSmnRW (DieBusNum, FCH_KL_SATA_MISC_CONTROL + Controller * FCH_KL_SMN_SATA_STEP, ~(UINT32) BIT1, BIT1, StdHeader);
  } else {
    FchSmnRW (DieBusNum, FCH_KL_SATA_MISC_CONTROL + Controller * FCH_KL_SMN_SATA_STEP, ~(UINT32) BIT1, 0x00, StdHeader);
  }

  //
  // OOB Detection Enhancement
  //
  if ( LocalCfgPtr->Sata[Controller].SataOobDetectionEnh ) {
    FchSmnRW (DieBusNum, FCH_KL_SATA_MISC_CONTROL + Controller * FCH_KL_SMN_SATA_STEP, ~(UINT32) BIT24, BIT24, StdHeader);
  } else {
    FchSmnRW (DieBusNum, FCH_KL_SATA_MISC_CONTROL + Controller * FCH_KL_SMN_SATA_STEP, ~(UINT32) BIT24, 0x00, StdHeader);
  }
}

/**
 * FchKLSataInitCtrlReg - Sata Rsmu Control register setting
 *
 *
 * @param[in] DieBusNum  Bus Number of current Die.
 * @param[in] Controller Sata controller number.
 * @param[in] FchDataPtr Fch configuration structure pointer.
 *
 */
VOID
FchKLSataInitCtrlReg (
  IN  UINT32   DieBusNum,
  IN  UINT32   Controller,
  IN VOID      *FchDataPtr
  )
{
  UINT32       AndMaskDword;
  UINT32       OrMaskDword;
  UINT8        FchSataAggrLinkPmCap;
  UINT8        FchSataPortMultCap;
  UINT8        FchSataPscCap;
  UINT8        FchSataSscCap;
  UINT8        FchSataFisBasedSwitching;
  UINT8        FchSataCccSupport;
  UINT8        FchSataAhciEnclosureManagement;
//  BOOLEAN      FchSataMsiEnable;
  FCH_DATA_BLOCK         *LocalCfgPtr;
  AMD_CONFIG_PARAMS      *StdHeader;

  LocalCfgPtr = (FCH_DATA_BLOCK *) FchDataPtr;
  StdHeader = LocalCfgPtr->StdHeader;

  FchSataAggrLinkPmCap = (UINT8) LocalCfgPtr->Sata[Controller].SataAggrLinkPmCap;
  FchSataPortMultCap = (UINT8) LocalCfgPtr->Sata[Controller].SataPortMultCap;
  FchSataPscCap = (UINT8) LocalCfgPtr->Sata[Controller].SataPscCap;
  FchSataSscCap = (UINT8) LocalCfgPtr->Sata[Controller].SataSscCap;
  FchSataFisBasedSwitching = (UINT8) LocalCfgPtr->Sata[Controller].SataFisBasedSwitching;
  FchSataCccSupport = (UINT8) LocalCfgPtr->Sata[Controller].SataCccSupport;
  FchSataAhciEnclosureManagement = (UINT8) LocalCfgPtr->Sata[Controller].SataAhciEnclosureManagement;
//  FchSataMsiEnable = LocalCfgPtr->Sata[Controller].SataMsiEnable;

  AndMaskDword = 0;
  OrMaskDword = 0;

  if ( !FchSataPortMultCap ) {
    AndMaskDword |= BIT12;
  }

  if ( FchSataFisBasedSwitching ) {
    OrMaskDword |= BIT10;
  } else {
    AndMaskDword |= BIT10;
  }

  if ( FchSataAggrLinkPmCap ) {
    OrMaskDword |= BIT11;
  } else {
    AndMaskDword |= BIT11;
  }

  if ( FchSataPscCap ) {
    OrMaskDword |= BIT1;
  } else {
    AndMaskDword |= BIT1;
  }

  if ( FchSataSscCap ) {
    OrMaskDword |= BIT26;
  } else {
    AndMaskDword |= BIT26;
  }

  //
  // Disabling CCC (Command Completion Coalescing) support.
  //
  if ( FchSataCccSupport ) {
    OrMaskDword |= BIT19;
  } else {
    AndMaskDword |= BIT19;
  }

  if ( FchSataAhciEnclosureManagement ) {
    OrMaskDword |= BIT27;
  } else {
    AndMaskDword |= BIT27;
  }

  FchSmnRW (DieBusNum, FCH_KL_SMN_SATA_CONTROL_BAR5 + FCH_SATA_BAR5_REGFC + Controller * FCH_KL_SMN_SATA_STEP, ~AndMaskDword, OrMaskDword, StdHeader);

  if ( FchSataFisBasedSwitching ) {
    FchSmnRW (DieBusNum, FCH_KL_SMN_SATA_CONTROL_BAR5 + FCH_SATA_BAR5_REGF8 + Controller * FCH_KL_SMN_SATA_STEP, 0x00FFFFFF, 0xFF000000, StdHeader);
  } else {
    FchSmnRW (DieBusNum, FCH_KL_SMN_SATA_CONTROL_BAR5 + FCH_SATA_BAR5_REGF8 + Controller * FCH_KL_SMN_SATA_STEP, 0x00FFFFFF, 0x00, StdHeader);
  }

  if ( LocalCfgPtr->Sata[Controller].BiosOsHandOff == 1 ) {
    FchSmnRW (DieBusNum, FCH_KL_SMN_SATA_CONTROL_BAR5 + FCH_SATA_BAR5_REG24 + Controller * FCH_KL_SMN_SATA_STEP, ~(UINT32) BIT0, BIT0, StdHeader);
  } else {
    FchSmnRW (DieBusNum, FCH_KL_SMN_SATA_CONTROL_BAR5 + FCH_SATA_BAR5_REG24 + Controller * FCH_KL_SMN_SATA_STEP, ~(UINT32) BIT0, 0x00, StdHeader);
  }

  // BIT2 : Enable dynamic blink clock gating
  // BIT14: Enable BlcClkGated gating
  // BIT15: Enable PciClkGated gating
  // BIT8 & BIT9 Disable performance enhancement
  // BIT25
  FchSmnRW (DieBusNum, FCH_KL_SMN_SATA_CONTROL_SLOR + Controller * FCH_KL_SMN_SATA_STEP + 0x10, ~(UINT32) (BIT2 + BIT8 + BIT9 + BIT14 + BIT15 + BIT25), BIT2 + BIT14 + BIT15 + BIT25, StdHeader);

  // Program sata_slor :: 0x14[8:5] = 0xF
  // Program sata_slor :: 0x14[30] = 1
  FchSmnRW (DieBusNum, FCH_KL_SMN_SATA_CONTROL_SLOR + Controller * FCH_KL_SMN_SATA_STEP + 0x14, ~(UINT32) (BIT5 + BIT6 + BIT7 + BIT8 + BIT30), BIT5 + BIT6 + BIT7 + BIT8 + BIT30, StdHeader);
  //
  // Disable Prefetch In Ahci Mode
  //
  if ( LocalCfgPtr->Sata[Controller].SataAhciDisPrefetchFunction ) {
    FchSmnRW (DieBusNum, FCH_KL_SMN_SATA_CONTROL_SLOR + Controller * FCH_KL_SMN_SATA_STEP + 0x00, ~(UINT32) BIT13, BIT13, StdHeader);
  } else {
    FchSmnRW (DieBusNum, FCH_KL_SMN_SATA_CONTROL_SLOR + Controller * FCH_KL_SMN_SATA_STEP + 0x00, ~(UINT32) BIT13, 0x00, StdHeader);
  }

  /*
  Because of security consideration, x86 is forbidden to access nBIF straps.
  Move code to ABL.
  */
  //FchKLSataInitMsi (DieBusNum, Controller, FchDataPtr);

  //
  // Shutdown ports
  //
  FchSmnRW (DieBusNum, FCH_KL_SMN_SATA_CONTROL_SLOR + Controller * FCH_KL_SMN_SATA_STEP + 0x00, 0xFF00FFFF, (LocalCfgPtr->Sata[Controller].SataPortPower << 16), StdHeader);

  //UBTS610116
  // Program sata_slor :: 0x04[22] = 0x1>
  FchSmnRW (DieBusNum, FCH_KL_SMN_SATA_CONTROL_SLOR + Controller * FCH_KL_SMN_SATA_STEP + 0x04, ~(UINT32) (BIT22), BIT22, StdHeader);
}


/*
Because of security consideration, x86 is forbidden to access nBIF straps.
Move code to ABL.
*/
#if 0
VOID
FchKLSataInitMsi (
  IN  UINT32   DieBusNum,
  IN  UINT32   Controller,
  IN VOID      *FchDataPtr
  )
{
  BOOLEAN            FchSataMsiEnable;
  FCH_DATA_BLOCK     *LocalCfgPtr;
  AMD_CONFIG_PARAMS  *StdHeader;

  LocalCfgPtr      = (FCH_DATA_BLOCK *) FchDataPtr;
  StdHeader        = LocalCfgPtr->StdHeader;
  FchSataMsiEnable = LocalCfgPtr->Sata[Controller].SataMsiEnable;

  //
  // MSI Capability could be disabled by setting register nBIF1:RCC_DEV0_EPF2_STRAP3.STRAP_MSI_EN_DEV0_F2 to 0.
  //
  if ( FchSataMsiEnable ) {
    switch ( Controller ) {
    case 0:    //Sata0
      FchSmnRW (DieBusNum, FCH_KL_SMN_SATA0_NBIF_STRAP3, ~(UINT32) BIT18, BIT18, StdHeader);
      break;
    case 1:    //Sata1
      FchSmnRW (DieBusNum, FCH_KL_SMN_SATA1_NBIF_STRAP3, ~(UINT32) BIT18, BIT18, StdHeader);
      break;
    case 2:    //Sata2
      FchSmnRW (DieBusNum, FCH_KL_SMN_SATA2_NBIF_STRAP3, ~(UINT32) BIT18, BIT18, StdHeader);
      break;
    case 3:    //Sata3
      FchSmnRW (DieBusNum, FCH_KL_SMN_SATA3_NBIF_STRAP3, ~(UINT32) BIT18, BIT18, StdHeader);
      break;
    default:
      break;
    }
  } else {
    switch ( Controller ) {
    case 0:    //Sata0
      FchSmnRW (DieBusNum, FCH_KL_SMN_SATA0_NBIF_STRAP3, ~(UINT32) BIT18, 0x00, StdHeader);
      break;
    case 1:    //Sata1
      FchSmnRW (DieBusNum, FCH_KL_SMN_SATA1_NBIF_STRAP3, ~(UINT32) BIT18, 0x00, StdHeader);
      break;
    case 2:    //Sata2
      FchSmnRW (DieBusNum, FCH_KL_SMN_SATA2_NBIF_STRAP3, ~(UINT32) BIT18, 0x00, StdHeader);
      break;
    case 3:    //Sata3
      FchSmnRW (DieBusNum, FCH_KL_SMN_SATA3_NBIF_STRAP3, ~(UINT32) BIT18, 0x00, StdHeader);
      break;
    default:
      break;
    }
  }
}
#endif


/**
 * FchKLSataControllerSetPortGenMode - Set Sata port mode (each)
 * for Gen1/Gen2/Gen3
 *
 *
 * @param[in] DieBusNum  Bus Number of current Die.
 * @param[in] Controller Sata controller number.
 * @param[in] PortMode   Sata Port Mode (Gen1/2/3) per port.
 * @param[in] FchDataPtr Fch configuration structure pointer.
 *
 */
VOID
FchKLSataControllerSetPortGenMode (
  IN  UINT32   DieBusNum,
  IN  UINT32   Controller,
  IN  UINT16   PortMode,
  IN  VOID     *FchDataPtr
  )
{
  UINT8        PortNumByte;
  UINT8        PortModeByte;
  UINT16       SataPortMode;
  FCH_DATA_BLOCK         *LocalCfgPtr;
  AMD_CONFIG_PARAMS      *StdHeader;

  LocalCfgPtr = (FCH_DATA_BLOCK *) FchDataPtr;
  StdHeader = LocalCfgPtr->StdHeader;

  SataPortMode = PortMode;
  PortNumByte = 0;

  while ( PortNumByte < KUNLUN_SATA_PORT_NUM ) {
    PortModeByte = (UINT8) (SataPortMode & 3);
    if ( (PortModeByte == BIT0) || (PortModeByte == BIT1) ) {
      if ( PortModeByte == BIT0 ) {
        //
        // set GEN 1
        //
        FchSmnRW (DieBusNum, FCH_KL_SMN_SATA_CONTROL_BAR5 + Controller * FCH_KL_SMN_SATA_STEP + FCH_SATA_BAR5_REG12C + PortNumByte * 0x80, 0xFFFFFF0F, 0x10, StdHeader);
      }

      if ( PortModeByte == BIT1 ) {
        //
        // set GEN2 (default is GEN3)
        //
        FchSmnRW (DieBusNum, FCH_KL_SMN_SATA_CONTROL_BAR5 + Controller * FCH_KL_SMN_SATA_STEP + FCH_SATA_BAR5_REG12C + PortNumByte * 0x80, 0xFFFFFF0F, 0x20, StdHeader);
      }

      FchSmnRW (DieBusNum, FCH_KL_SMN_SATA_CONTROL_BAR5 + Controller * FCH_KL_SMN_SATA_STEP + FCH_SATA_BAR5_REG12C + PortNumByte * 0x80, 0xFFFFFFFF, 0x01, StdHeader);
    }

    SataPortMode >>= 2;
    PortNumByte ++;
  }

  FchStall (1000, StdHeader);
  SataPortMode = PortMode;
  PortNumByte = 0;

  while ( PortNumByte < KUNLUN_SATA_PORT_NUM ) {
    PortModeByte = (UINT8) (SataPortMode & 3);

    if ( (PortModeByte == BIT0) || (PortModeByte == BIT1) ) {
      FchSmnRW (DieBusNum, FCH_KL_SMN_SATA_CONTROL_BAR5 + Controller * FCH_KL_SMN_SATA_STEP + FCH_SATA_BAR5_REG12C + PortNumByte * 0x80, 0xFFFFFFFE, 0x00, StdHeader);
    }

    PortNumByte ++;
    SataPortMode >>= 2;
  }
}

/**
 * FchKLSataInitMMC - Sata MSI MMC setting
 *
 *
 * @param[in] DieBusNum  Bus Number of current Die.
 * @param[in] Controller Sata controller number.
 * @param[in] FchDataPtr Fch configuration structure pointer.
 *
 */
VOID
FchKLSataInitMMC (
  IN  UINT32   DieBusNum,
  IN  UINT32   Controller,
  IN VOID      *FchDataPtr
  )
{
  UINT32                 SataPciMmc;
  FCH_DATA_BLOCK         *LocalCfgPtr;
  AMD_CONFIG_PARAMS      *StdHeader;

  LocalCfgPtr = (FCH_DATA_BLOCK *) FchDataPtr;
  StdHeader = LocalCfgPtr->StdHeader;
  SataPciMmc = 0;

  //
  // Sata MMC programming
  //
  SataPciMmc = 0x04000000;                        //Stones MMC set to 0x4
  FchSmnRW (DieBusNum, FCH_KL_SATA_EVENT_SELECT + Controller * FCH_KL_SMN_SATA_STEP, 0xF8FFFFFF, SataPciMmc, StdHeader);
}


/**
 * FchKLSataSetPortGenMode - Set Sata port mode (each) for
 * Gen1/Gen2/Gen3
 *
 *
 * @param[in] DieBusNum  Bus Number of current Die.
 * @param[in] Controller Sata controller number.
 * @param[in] FchDataPtr Fch configuration structure pointer.
 *
 */
VOID
FchKLSataSetPortGenMode (
  IN  UINT32   DieBusNum,
  IN  UINT32   Controller,
  IN  VOID     *FchDataPtr
  )
{
  UINT16       SataPortMode;
  FCH_DATA_BLOCK         *LocalCfgPtr;
//  AMD_CONFIG_PARAMS      *StdHeader;

  LocalCfgPtr = (FCH_DATA_BLOCK *) FchDataPtr;
//  StdHeader = LocalCfgPtr->StdHeader;
  SataPortMode = (UINT16) LocalCfgPtr->Sata[Controller].SataPortMd;
  FchKLSataControllerSetPortGenMode (DieBusNum, Controller, SataPortMode, FchDataPtr);
}

/**
 * FchKLSataSetBISTLComplianceMode - Set Sata port BIST-L Compliance mode
 *
 *
 * @param[in] DieBusNum  Bus Number of current Die.
 * @param[in] Controller Sata controller number.
 * @param[in] FchDataPtr Fch configuration structure pointer.
 *
 */
VOID
FchKLSataSetBISTLComplianceMode (
  IN  UINT32   DieBusNum,
  IN  UINT32   Controller,
  IN  VOID     *FchDataPtr
  )
{
  FCH_DATA_BLOCK          *LocalCfgPtr;
  AMD_CONFIG_PARAMS       *StdHeader;
  UINT32                  PortNumByte;
  UINT32                  BISTLPattern;

  LocalCfgPtr   = (FCH_DATA_BLOCK *) FchDataPtr;
  StdHeader     = LocalCfgPtr->StdHeader;
  PortNumByte   = 0;
  BISTLPattern  = (UINT32) LocalCfgPtr->Sata[Controller].SataBISTLComplianceMode;

  if ( BISTLPattern != 0 ) {
    for (PortNumByte=0; PortNumByte<KUNLUN_SATA_PORT_NUM; PortNumByte++) {
      // 1. Set SYSHUB::SATA::SLOR::SATA_PORT[X]_LBIST_CTRL_STS::Px_LBIST_Disconnect_En to 1
      FchSmnRW (
        DieBusNum,
        FCH_KL_SMN_SATA_CONTROL_SLOR + Controller * FCH_KL_SMN_SATA_STEP + PortNumByte * 0x20 + FCH_SATA_REG94,
        (UINT32)(~BIT11),
        BIT11,
        StdHeader
        );

      // 2. Set SYSHUB::SATA::SLOR::SATA_PORT[X]_LBIST_CTRL_STS::Px_LBIST_Sped to GEN1/GEN2/GEN3
      FchSmnRW (
        DieBusNum,
        FCH_KL_SMN_SATA_CONTROL_SLOR + Controller * FCH_KL_SMN_SATA_STEP + PortNumByte * 0x20 + FCH_SATA_REG94,
        (UINT32)(~(3<<12)),
        (BISTLPattern & 3) << 12,
        StdHeader
        );

      // 3. Set SYSHUB::SATA::SLOR::SATA_PORT[X]_LBIST_CTRL_STS::Px_LBIST_PTRN to 0x18
      FchSmnRW (
        DieBusNum,
        FCH_KL_SMN_SATA_CONTROL_SLOR + Controller * FCH_KL_SMN_SATA_STEP + PortNumByte * 0x20 + FCH_SATA_REG94,
        (UINT32)(~0x3C),
        0x18,
        StdHeader
        );

      // 4. Set SYSHUB::SATA::SLOR::SATA_PORT[X]_LBIST_CTRL_STS::Px_LBIST_En to 1
      FchSmnRW (
        DieBusNum,
        FCH_KL_SMN_SATA_CONTROL_SLOR + Controller * FCH_KL_SMN_SATA_STEP + PortNumByte * 0x20 + FCH_SATA_REG94,
        (UINT32)(~BIT0),
        BIT0,
        StdHeader
        );
    }
  }
}

/**
 * FchKLSataShutdownUnconnectedSataPortClock - Shutdown
 * unconnected Sata port clock
 *
 * @param[in] DieBusNum  Bus Number of current Die.
 * @param[in] Controller Sata controller number.
 * @param[in] FchDataPtr Fch configuration structure pointer.
 *
 */
VOID
FchKLSataShutdownUnconnectedSataPortClock (
  IN  UINT32     DieBusNum,
  IN  UINT32     Controller,
  IN  VOID       *FchDataPtr
  )
{
  UINT8                  PortNumByte;
  UINT8                  PortSataStatusByte;
  UINT8                  NumOfPorts;
  UINT8                  FchSataClkAutoOff;
  UINT32                 PortStatusDword;
  FCH_DATA_BLOCK         *LocalCfgPtr;
  AMD_CONFIG_PARAMS      *StdHeader;

  LocalCfgPtr = (FCH_DATA_BLOCK *) FchDataPtr;
  StdHeader = LocalCfgPtr->StdHeader;
  FchSataClkAutoOff = (UINT8) LocalCfgPtr->Sata[Controller].SataClkAutoOff;

  NumOfPorts = 0;
  //
  // Enable SATA auto clock control by default
  //
  if ( FchSataClkAutoOff ) {

      for ( PortNumByte = 0; PortNumByte < KUNLUN_SATA_PORT_NUM; PortNumByte++ ) {
        FchSmnRead (DieBusNum, FCH_KL_SMN_SATA_CONTROL_BAR5 + Controller * FCH_KL_SMN_SATA_STEP + FCH_SATA_BAR5_REG128 + PortNumByte * 0x80, &PortStatusDword, StdHeader);
        PortSataStatusByte = (UINT8) (PortStatusDword & 0xFF);
        //
        // Shutdown the clock for the port and do the necessary port reporting changes.
        // SYSHUB::SATA::PORT::SATA_AHCI_P_SSTS:P_SSTS_DET
        // 1: Device presence detected but PHY communication not established
        // 3: Device presence detected and PHY communication established
        //
        if ( ((PortSataStatusByte & 0x0F) != 0x03) && (! ((LocalCfgPtr->Sata[Controller].SataEspPort) & (1 << PortNumByte))) ) {
          FchKLSataInitPortClearFre (DieBusNum, Controller, PortNumByte, LocalCfgPtr);
          FchKLSataInitPortOffline (DieBusNum, Controller, PortNumByte, LocalCfgPtr);
          FchSmnRW (DieBusNum, FCH_KL_SMN_SATA_CONTROL_SLOR + Controller * FCH_KL_SMN_SATA_STEP + 0x00, ~(UINT32) (BIT16 << PortNumByte), (BIT16 << PortNumByte), StdHeader);
          FchSmnRW (DieBusNum, FCH_KL_SMN_SATA_CONTROL_BAR5 + Controller * FCH_KL_SMN_SATA_STEP + FCH_SATA_BAR5_REG0C, ~(UINT32) (1 << PortNumByte), 00, StdHeader);
        }
      }                                            ///end of for loop
  }

  FchSmnRead (DieBusNum, FCH_KL_SMN_SATA_CONTROL_BAR5 + Controller * FCH_KL_SMN_SATA_STEP + FCH_SATA_BAR5_REG0C, &PortStatusDword, StdHeader);
  PortSataStatusByte = (UINT8) (PortStatusDword & 0xFF);

  //
  //if all ports are in disabled state, report at least one port
  //
  if ( (PortSataStatusByte & 0xFF) == 0) {
    FchSmnRW (DieBusNum, FCH_KL_SMN_SATA_CONTROL_BAR5 + Controller * FCH_KL_SMN_SATA_STEP + FCH_SATA_BAR5_REG0C, 0xFFFFFF00, 0x01, StdHeader);
  }

  FchSmnRead (DieBusNum, FCH_KL_SMN_SATA_CONTROL_BAR5 + Controller * FCH_KL_SMN_SATA_STEP + FCH_SATA_BAR5_REG0C, &PortStatusDword, StdHeader);
  PortSataStatusByte = (UINT8) (PortStatusDword & 0xFF);

  for (PortNumByte = 0; PortNumByte < KUNLUN_SATA_PORT_NUM; PortNumByte ++) {
    if (PortSataStatusByte & (1 << PortNumByte)) {
      NumOfPorts++;
    }
  }

  if ( NumOfPorts == 0) {
    NumOfPorts = 0x01;
  }

  FchSmnRW (DieBusNum, FCH_KL_SMN_SATA_CONTROL_BAR5 + Controller * FCH_KL_SMN_SATA_STEP + FCH_SATA_BAR5_REG00, 0xFFFFFFE0, (UINT32) (NumOfPorts - 1), StdHeader);
}

// /**
//  * FchKLSataAutoShutdownController - Shutdown
//  * Controller without ports connected
//  *
//  * @param[in] DieBusNum  Bus Number of current Die.
//  * @param[in] Controller Sata controller number.
//  * @param[in] FchDataPtr Fch configuration structure pointer.
//  *
//  */
// VOID
// FchKLSataAutoShutdownController (
//   IN  UINT32   DieBusNum,
//   IN  UINT32   Controller,
//   IN  VOID     *FchDataPtr
//   )
// {
//   UINT8                       PortNumByte;
//   UINT32                      PortStatusDword;
//   FCH_DATA_BLOCK              *LocalCfgPtr;

//   IDS_HDT_CONSOLE (
//     FCH_TRACE,
//     "[FCH][SATA] %a (BusNum: 0x%0x) - Start\n",
//     __FUNCTION__,
//     DieBusNum
//     );

//   LocalCfgPtr = (FCH_DATA_BLOCK *)FchDataPtr;
//   if (ReadFchSleepType (LocalCfgPtr->StdHeader) != ACPI_SLPTYP_S3) {
//     if ((LocalCfgPtr->Sata[Controller].SataControllerAutoShutdown) && (LocalCfgPtr->Sata[Controller].SataEspPort == 0)) {
//       IDS_HDT_CONSOLE (FCH_TRACE, "[FCH][SATA] Sata controller %d\n", Controller);
//       for ( PortNumByte = 0; PortNumByte < KUNLUN_SATA_PORT_NUM; PortNumByte++ ) {
//         FchSmnRead (DieBusNum, FCH_KL_SMN_SATA_CONTROL_BAR5 + Controller * FCH_KL_SMN_SATA_STEP + FCH_SATA_BAR5_REG128 + PortNumByte * 0x80, &PortStatusDword, LocalCfgPtr->StdHeader);
//         IDS_HDT_CONSOLE (
//           FCH_TRACE,
//           "[FCH][SATA] Sata Port %d Status SMN 0x%x = 0x%x\n",
//           PortNumByte,
//           FCH_KL_SMN_SATA_CONTROL_BAR5 + Controller * FCH_KL_SMN_SATA_STEP + FCH_SATA_BAR5_REG128 + PortNumByte * 0x80,
//           PortStatusDword
//           );
//         //
//         // If SYSHUB::SATA::PORT::SATA_AHCI_P_SSTS:P_SSTS_DET is below value, skip controller disable
//         // 1: Device presence detected but PHY communication not established
//         // 3: Device presence detected and PHY communication established
//         //
//         if ( (PortStatusDword & 0xF) == 1 || (PortStatusDword & 0xF) == 3 ) {
//           IDS_HDT_CONSOLE (FCH_TRACE, "[FCH][SATA] Device Present. Quit.\n");
//           return;                         // do not hide/shut Sata if detect device
//         }
//       }
//       FchKLSataInitDisableSata (DieBusNum, Controller, FchDataPtr);
//       LocalCfgPtr->Sata[Controller].SataEnable = 0;
//     }
//   }

//   IDS_HDT_CONSOLE (
//     FCH_TRACE,
//     "[FCH][SATA] %a (BusNum: 0x%0x) - End\n",
//     __FUNCTION__,
//     DieBusNum
//     );
// }

/**
 * sataSetIrqIntResource - Config SATA IRQ/INT# resource
 *
 *
 *
 * @param[in] FchDataPtr Fch configuration structure pointer.
 * @param[in] StdHeader
 *
 */
VOID
SataSetIrqIntResource (
  IN  VOID                 *FchDataPtr,
  IN  AMD_CONFIG_PARAMS    *StdHeader
  )
{
  UINT8                  ValueByte;
//  FCH_DATA_BLOCK         *LocalCfgPtr;

//  LocalCfgPtr = (FCH_DATA_BLOCK *) FchDataPtr;
  //
  // IRQ14/IRQ15 come from IDE or SATA
  //
  ValueByte = 0x08;
  LibAmdIoWrite (AccessWidth8, FCH_IOMAP_REGC00, &ValueByte, StdHeader);
  LibAmdIoRead (AccessWidth8, FCH_IOMAP_REGC01, &ValueByte, StdHeader);
  ValueByte = ValueByte & 0x0F;
  ValueByte = ValueByte | 0xF0;
  LibAmdIoWrite (AccessWidth8, FCH_IOMAP_REGC01, &ValueByte, StdHeader);
}

/**
 * FchKLSataGpioSetPad - Set SGPIO PAD
 *
 *   - Private function
 *
 * @param[in] DieBusNum  Bus Number of current Die.
 * @param[in] Controller Sata controller number.
 * @param[in] FchDataPtr Fch configuration structure pointer.
 *
 */
VOID
FchKLSataGpioSetPad (
  IN  UINT32     DieBusNum,
  IN  UINT32     Controller,
  IN  VOID       *FchDataPtr
  )
{
  //set it via MMIO when access it on Master FCH.
  if ( DieBusNum == 0 ){
    IDS_HDT_CONSOLE (FCH_TRACE, "%a set SGPIO PAD on primary FCH\n", __FUNCTION__);
    // Set to SGPIO Pin
    RwMem ( ACPI_MMIO_BASE+REMOTE_GPIO_BASE + FCH_REGC0, AccessWidth8, 0x03, 0x01);  // Set IOMUXx100 to SGPIO0_CLK     FCH::RMTGPIO::IOMUX0_GPIO256
    RwMem ( ACPI_MMIO_BASE+REMOTE_GPIO_BASE + FCH_REGC1, AccessWidth8, 0x03, 0x01);  // Set IOMUXx101 to SGPIO1_CLK     FCH::RMTGPIO::IOMUX1_GPIO257
    RwMem ( ACPI_MMIO_BASE+REMOTE_GPIO_BASE + FCH_REGC2, AccessWidth8, 0x03, 0x01);  // Set IOMUXx102 to SGPIO2_CLK     FCH::RMTGPIO::IOMUX1_GPIO258
    RwMem ( ACPI_MMIO_BASE+REMOTE_GPIO_BASE + FCH_REGC3, AccessWidth8, 0x03, 0x01);  // Set IOMUXx103 to SGPIO3_CLK     FCH::RMTGPIO::IOMUX1_GPIO259
    RwMem ( ACPI_MMIO_BASE+REMOTE_GPIO_BASE + FCH_REGC4, AccessWidth8, 0x03, 0x00);  // Set IOMUXx104 to SGPIO_DATAOUT  FCH::RMTGPIO::IOMUX1_GPIO260
    RwMem ( ACPI_MMIO_BASE+REMOTE_GPIO_BASE + FCH_REGC5, AccessWidth8, 0x03, 0x00);  // Set IOMUXx105 to SGPIO_LOAD     FCH::RMTGPIO::IOMUX1_GPIO261
    //Make SPIO pins pull up enable
    RwMem ( ACPI_MMIO_BASE+REMOTE_GPIO_BASE + FCH_REG00, AccessWidth32, ~(UINT32) BIT21, BIT20);  // Set SGPIO0_CLK       FCH::RMTGPIO::AGPIO256_SGPIO0_CLK_UBM_BP_TYPE
    RwMem ( ACPI_MMIO_BASE+REMOTE_GPIO_BASE + FCH_REG04, AccessWidth32, ~(UINT32) BIT21, BIT20);  // Set SGPIO1_CLK       FCH::RMTGPIO::AGPIO257_SGPIO1_CLK_CLK_REQ01_L_UBM_CPRSNT_CHANGE_DET_L
    RwMem ( ACPI_MMIO_BASE+REMOTE_GPIO_BASE + FCH_REG08, AccessWidth32, ~(UINT32) BIT21, BIT20);  // Set SGPIO2_CLK       FCH::RMTGPIO::AGPIO258_SGPIO2_CLK_CLK_REQ02_L_UBM_2WIRE_RESET_L
    RwMem ( ACPI_MMIO_BASE+REMOTE_GPIO_BASE + FCH_REG0C, AccessWidth32, ~(UINT32) BIT21, BIT20);  // Set SGPIO3_CLK       FCH::RMTGPIO::AGPIO259_SGPIO3_CLK
    RwMem ( ACPI_MMIO_BASE+REMOTE_GPIO_BASE + FCH_REG10, AccessWidth32, ~(UINT32) BIT21, BIT20);  // Set SGPIO_DATAOUT    FCH::RMTGPIO::SGPIO_DATAOUT_AGPIO260
    RwMem ( ACPI_MMIO_BASE+REMOTE_GPIO_BASE + FCH_REG14, AccessWidth32, ~(UINT32) BIT21, BIT20);  // Set SGPIO_LOA
  } else {
    // Set to SGPIO Pin
    IDS_HDT_CONSOLE (FCH_TRACE, "%a set SGPIO PAD on secondary FCH\n", __FUNCTION__);
    FchSmnRW8 (DieBusNum, FCH_KL_REMOTE_GPIO_SMN_BASE + FCH_REGC0, 0x03, 0x01, NULL);  // Set IOMUXx100 to SGPIO0_CLK     FCH::RMTGPIO::IOMUX0_GPIO256
    FchSmnRW8 (DieBusNum, FCH_KL_REMOTE_GPIO_SMN_BASE + FCH_REGC1, 0x03, 0x01, NULL);  // Set IOMUXx101 to SGPIO1_CLK     FCH::RMTGPIO::IOMUX1_GPIO257
    FchSmnRW8 (DieBusNum, FCH_KL_REMOTE_GPIO_SMN_BASE + FCH_REGC2, 0x03, 0x01, NULL);  // Set IOMUXx102 to SGPIO2_CLK     FCH::RMTGPIO::IOMUX1_GPIO258
    FchSmnRW8 (DieBusNum, FCH_KL_REMOTE_GPIO_SMN_BASE + FCH_REGC3, 0x03, 0x01, NULL);  // Set IOMUXx103 to SGPIO3_CLK     FCH::RMTGPIO::IOMUX1_GPIO259
    FchSmnRW8 (DieBusNum, FCH_KL_REMOTE_GPIO_SMN_BASE + FCH_REGC4, 0x03, 0x00, NULL);  // Set IOMUXx104 to SGPIO_DATAOUT  FCH::RMTGPIO::IOMUX1_GPIO260
    FchSmnRW8 (DieBusNum, FCH_KL_REMOTE_GPIO_SMN_BASE + FCH_REGC5, 0x03, 0x00, NULL);  // Set IOMUXx105 to SGPIO_LOAD     FCH::RMTGPIO::IOMUX1_GPIO261

    //Make SPIO pins pull up enable
    FchSmnRW (DieBusNum, FCH_KL_REMOTE_GPIO_SMN_BASE + FCH_REG00, ~(UINT32) BIT21, BIT20, NULL);  // Set SGPIO0_CLK       FCH::RMTGPIO::AGPIO256_SGPIO0_CLK_UBM_BP_TYPE
    FchSmnRW (DieBusNum, FCH_KL_REMOTE_GPIO_SMN_BASE + FCH_REG04, ~(UINT32) BIT21, BIT20, NULL);  // Set SGPIO1_CLK       FCH::RMTGPIO::AGPIO257_SGPIO1_CLK_CLK_REQ01_L_UBM_CPRKLT_CHANGE_DET_L
    FchSmnRW (DieBusNum, FCH_KL_REMOTE_GPIO_SMN_BASE + FCH_REG08, ~(UINT32) BIT21, BIT20, NULL);  // Set SGPIO2_CLK       FCH::RMTGPIO::AGPIO258_SGPIO2_CLK_CLK_REQ02_L_UBM_2WIRE_RESET_L
    FchSmnRW (DieBusNum, FCH_KL_REMOTE_GPIO_SMN_BASE + FCH_REG0C, ~(UINT32) BIT21, BIT20, NULL);  // Set SGPIO3_CLK       FCH::RMTGPIO::AGPIO259_SGPIO3_CLK
    FchSmnRW (DieBusNum, FCH_KL_REMOTE_GPIO_SMN_BASE + FCH_REG10, ~(UINT32) BIT21, BIT20, NULL);  // Set SGPIO_DATAOUT    FCH::RMTGPIO::SGPIO_DATAOUT_AGPIO260
    FchSmnRW (DieBusNum, FCH_KL_REMOTE_GPIO_SMN_BASE + FCH_REG14, ~(UINT32) BIT21, BIT20, NULL);  // Set SGPIO_LOAD       FCH::RMTGPIO::SGPIO_LOAD_AGPIO261
  }
}

/**
 * FchKLSataGpioInitial - Sata GPIO function Procedure
 *
 *   - Private function
 *
 * @param[in] DieBusNum  Bus Number of current Die.
 * @param[in] Controller Sata controller number.
 * @param[in] FchDataPtr Fch configuration structure pointer.
 *
 */
VOID
FchKLSataGpioInitial (
  IN  UINT32     DieBusNum,
  IN  UINT32     Controller,
  IN  VOID       *FchDataPtr
  )
{
  UINT32                 FchSataBarRegDword;

  //1.Set SGPIO PAD
  FchKLSataGpioSetPad (DieBusNum, Controller, FchDataPtr);

  //2.Support 8 device on 1 SGPIO stream
  //set SPIO_TgtSpt8dev = 0x1, support 8 device on 1 SGPIO stream
  FchSmnRW (DieBusNum, FCH_KL_SATA_MISC_CONTROL + Controller * FCH_KL_SMN_SATA_STEP, ~(UINT32) BIT3, BIT3, NULL);

  //3.Enable SGPIO in Enclosure Management set
  //Program sata_ahci :: FCh[27] =1
  FchSmnRW (DieBusNum, FCH_KL_SMN_SATA_CONTROL_BAR5 + Controller * FCH_KL_SMN_SATA_STEP + FCH_SATA_BAR5_REGFC, ~(UINT32) BIT27, BIT27, NULL);

  //4.Set SGPIO enable
  // 4.2.3 SGPIO Initialization for Initiator0
  //a. Program GPIO_AMD_0.ISEL=0
  // Programe sata_ahci::506h = 0xC0
  FchSmnRW8 (DieBusNum, FCH_KL_SMN_SATA_CONTROL_BAR5 + Controller * FCH_KL_SMN_SATA_STEP + 0x506, 0x00, 0xC0, NULL);
  // Programe sata_ahci::507h = 0x00
  FchSmnRW8 (DieBusNum, FCH_KL_SMN_SATA_CONTROL_BAR5 + Controller * FCH_KL_SMN_SATA_STEP + 0x507, 0x00, 0x00, NULL);
  // Programe sata_ahci::508h = 0x01
  FchSmnRW8 (DieBusNum, FCH_KL_SMN_SATA_CONTROL_BAR5 + Controller * FCH_KL_SMN_SATA_STEP + 0x508, 0x00, 0x01, NULL);
  // Programe ISEL=0, sata_ahci::50Ch = 0x00000020 (ISEL=0, GN_AT=1(default), BSY_BYPEN= 0)
  FchSmnRW (DieBusNum, FCH_KL_SMN_SATA_CONTROL_BAR5 + Controller * FCH_KL_SMN_SATA_STEP + 0x50C, 0x00, 0x00000020, NULL);
  // Programe Sata_ahci::20h[8] = 1
  FchSmnRW (DieBusNum, FCH_KL_SMN_SATA_CONTROL_BAR5 + Controller * FCH_KL_SMN_SATA_STEP + FCH_SATA_BAR5_REG20, ~(UINT32) (BIT8), BIT8, NULL);
  do {
    FchSmnRead (DieBusNum, FCH_KL_SMN_SATA_CONTROL_BAR5 + Controller * FCH_KL_SMN_SATA_STEP + FCH_SATA_BAR5_REG20, &FchSataBarRegDword, NULL);
  } while ( FchSataBarRegDword & BIT8);
  FchStall (5000, NULL);

  //b. Configure GPIO_TX[0]
  // Programe sata_ahci::506h = 0x03
  FchSmnRW8 (DieBusNum, FCH_KL_SMN_SATA_CONTROL_BAR5 + Controller * FCH_KL_SMN_SATA_STEP + 0x506, 0x00, 0x03, NULL);
  // Programe sata_ahci::507h = 0x00
  FchSmnRW8 (DieBusNum, FCH_KL_SMN_SATA_CONTROL_BAR5 + Controller * FCH_KL_SMN_SATA_STEP + 0x507, 0x00, 0x00, NULL);
  // Programe sata_ahci::508h = 0x01
  FchSmnRW8 (DieBusNum, FCH_KL_SMN_SATA_CONTROL_BAR5 + Controller * FCH_KL_SMN_SATA_STEP + 0x508, 0x00, 0x01, NULL);
  // Programe sata_ahci::50Ch = 0xA0A0A0A0 (ACT=0x5 SOF, LOC=0x0, ERR=0x0)
  FchSmnRW (DieBusNum, FCH_KL_SMN_SATA_CONTROL_BAR5 + Controller * FCH_KL_SMN_SATA_STEP + 0x50C, 0x00, 0xA0A0A0A0, NULL);
  // Programe Sata_ahci::20h[8] = 1
  FchSmnRW (DieBusNum, FCH_KL_SMN_SATA_CONTROL_BAR5 + Controller * FCH_KL_SMN_SATA_STEP + FCH_SATA_BAR5_REG20, ~(UINT32) (BIT8), BIT8, NULL);
  do {
    FchSmnRead (DieBusNum, FCH_KL_SMN_SATA_CONTROL_BAR5 + Controller * FCH_KL_SMN_SATA_STEP + FCH_SATA_BAR5_REG20, &FchSataBarRegDword, NULL);
  } while ( FchSataBarRegDword & BIT8);
  FchStall (5000, NULL);

  //c. Configure GPIO_CFG[1:0] (enable SGPIO initiator0 and activity stretch settings)
  // Programe sata_ahci::506h = 0x00
  FchSmnRW8 (DieBusNum, FCH_KL_SMN_SATA_CONTROL_BAR5 + Controller * FCH_KL_SMN_SATA_STEP + 0x506, 0x00, 0x00, NULL);
  // Programe sata_ahci::507h = 0x00
  FchSmnRW8 (DieBusNum, FCH_KL_SMN_SATA_CONTROL_BAR5 + Controller * FCH_KL_SMN_SATA_STEP + 0x507, 0x00, 0x00, NULL);
  // Programe sata_ahci::508h = 0x02
  FchSmnRW8 (DieBusNum, FCH_KL_SMN_SATA_CONTROL_BAR5 + Controller * FCH_KL_SMN_SATA_STEP + 0x508, 0x00, 0x02, NULL);
  // Programe sata_ahci::50Ch = 0x00800000
  FchSmnRW (DieBusNum, FCH_KL_SMN_SATA_CONTROL_BAR5 + Controller * FCH_KL_SMN_SATA_STEP + 0x50C, 0x00000000, BIT23, NULL);
  // Programe sata_ahci::510h = 0x0F0F3700
  FchSmnRW (DieBusNum, FCH_KL_SMN_SATA_CONTROL_BAR5 + Controller * FCH_KL_SMN_SATA_STEP + 0x510, 0x00000000, 0x0F0F3700, NULL);
  // Programe Sata_ahci::20h[8] = 1
  FchSmnRW (DieBusNum, FCH_KL_SMN_SATA_CONTROL_BAR5 + Controller * FCH_KL_SMN_SATA_STEP + FCH_SATA_BAR5_REG20, ~(UINT32) (BIT8), BIT8, NULL);
  do {
    FchSmnRead (DieBusNum, FCH_KL_SMN_SATA_CONTROL_BAR5 + Controller * FCH_KL_SMN_SATA_STEP + FCH_SATA_BAR5_REG20, &FchSataBarRegDword, NULL);
  } while ( FchSataBarRegDword & BIT8);
  FchStall (5000, NULL);

  // 4.2.4 SGPIO Initialization for Initiator1
  //a. Program GPIO_AMD_0.ISEL=1
  // Programe sata_ahci::506h = 0xC0
  FchSmnRW8 (DieBusNum, FCH_KL_SMN_SATA_CONTROL_BAR5 + Controller * FCH_KL_SMN_SATA_STEP + 0x506, 0x00, 0xC0, NULL);
  // Programe sata_ahci::507h = 0x00
  FchSmnRW8 (DieBusNum, FCH_KL_SMN_SATA_CONTROL_BAR5 + Controller * FCH_KL_SMN_SATA_STEP + 0x507, 0x00, 0x00, NULL);
  // Programe sata_ahci::508h = 0x01
  FchSmnRW8 (DieBusNum, FCH_KL_SMN_SATA_CONTROL_BAR5 + Controller * FCH_KL_SMN_SATA_STEP + 0x508, 0x00, 0x01, NULL);
  // Programe ISEL=0, sata_ahci::50Ch = 0x00000021 (ISEL=1, GN_AT=1(default), BSY_BYPEN= 0)
  FchSmnRW (DieBusNum, FCH_KL_SMN_SATA_CONTROL_BAR5 + Controller * FCH_KL_SMN_SATA_STEP + 0x50C, 0x00, 0x00000021, NULL);
  // Programe Sata_ahci::20h[8] = 1
  FchSmnRW (DieBusNum, FCH_KL_SMN_SATA_CONTROL_BAR5 + Controller * FCH_KL_SMN_SATA_STEP + FCH_SATA_BAR5_REG20, ~(UINT32) (BIT8), BIT8, NULL);
  do {
    FchSmnRead (DieBusNum, FCH_KL_SMN_SATA_CONTROL_BAR5 + Controller * FCH_KL_SMN_SATA_STEP + FCH_SATA_BAR5_REG20, &FchSataBarRegDword, NULL);
  } while ( FchSataBarRegDword & BIT8);
  FchStall (5000, NULL);

  //b. Configure GPIO_TX[0]
  // Programe sata_ahci::506h = 0x03
  FchSmnRW8 (DieBusNum, FCH_KL_SMN_SATA_CONTROL_BAR5 + Controller * FCH_KL_SMN_SATA_STEP + 0x506, 0x00, 0x03, NULL);
  // Programe sata_ahci::507h = 0x00
  FchSmnRW8 (DieBusNum, FCH_KL_SMN_SATA_CONTROL_BAR5 + Controller * FCH_KL_SMN_SATA_STEP + 0x507, 0x00, 0x00, NULL);
  // Programe sata_ahci::508h = 0x01
  FchSmnRW8 (DieBusNum, FCH_KL_SMN_SATA_CONTROL_BAR5 + Controller * FCH_KL_SMN_SATA_STEP + 0x508, 0x00, 0x01, NULL);
  // Programe sata_ahci::50Ch = 0xA0A0A0A0 (ACT=0x5 SOF, LOC=0x0, ERR=0x0)
  FchSmnRW (DieBusNum, FCH_KL_SMN_SATA_CONTROL_BAR5 + Controller * FCH_KL_SMN_SATA_STEP + 0x50C, 0x00, 0xA0A0A0A0, NULL);
  // Programe Sata_ahci::20h[8] = 1
  FchSmnRW (DieBusNum, FCH_KL_SMN_SATA_CONTROL_BAR5 + Controller * FCH_KL_SMN_SATA_STEP + FCH_SATA_BAR5_REG20, ~(UINT32) (BIT8), BIT8, NULL);
  do {
    FchSmnRead (DieBusNum, FCH_KL_SMN_SATA_CONTROL_BAR5 + Controller * FCH_KL_SMN_SATA_STEP + FCH_SATA_BAR5_REG20, &FchSataBarRegDword, NULL);
  } while ( FchSataBarRegDword & BIT8);
  FchStall (5000, NULL);

  //c. Configure GPIO_CFG[1:0] (enable SGPIO initiator1 and activity stretch settings)
  // Programe sata_ahci::506h = 0x00
  FchSmnRW8 (DieBusNum, FCH_KL_SMN_SATA_CONTROL_BAR5 + Controller * FCH_KL_SMN_SATA_STEP + 0x506, 0x00, 0x00, NULL);
  // Programe sata_ahci::507h = 0x00
  FchSmnRW8 (DieBusNum, FCH_KL_SMN_SATA_CONTROL_BAR5 + Controller * FCH_KL_SMN_SATA_STEP + 0x507, 0x00, 0x00, NULL);
  // Programe sata_ahci::508h = 0x02
  FchSmnRW8 (DieBusNum, FCH_KL_SMN_SATA_CONTROL_BAR5 + Controller * FCH_KL_SMN_SATA_STEP + 0x508, 0x00, 0x02, NULL);
  // Programe sata_ahci::50Ch = 0x00800000
  FchSmnRW (DieBusNum, FCH_KL_SMN_SATA_CONTROL_BAR5 + Controller * FCH_KL_SMN_SATA_STEP + 0x50C, 0x00000000, BIT23, NULL);
  // Programe sata_ahci::510h = 0x0F0F3700
  FchSmnRW (DieBusNum, FCH_KL_SMN_SATA_CONTROL_BAR5 + Controller * FCH_KL_SMN_SATA_STEP + 0x510, 0x00000000, 0x0F0F3700, NULL);
  // Programe Sata_ahci::20h[8] = 1
  FchSmnRW (DieBusNum, FCH_KL_SMN_SATA_CONTROL_BAR5 + Controller * FCH_KL_SMN_SATA_STEP + FCH_SATA_BAR5_REG20, ~(UINT32) (BIT8), BIT8, NULL);
  do {
    FchSmnRead (DieBusNum, FCH_KL_SMN_SATA_CONTROL_BAR5 + Controller * FCH_KL_SMN_SATA_STEP + FCH_SATA_BAR5_REG20, &FchSataBarRegDword, NULL);
  } while ( FchSataBarRegDword & BIT8);
  FchStall (5000, NULL);
}



