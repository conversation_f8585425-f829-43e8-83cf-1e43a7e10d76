/*****************************************************************************
 *
 * Copyright (C) 2008-2024 Advanced Micro Devices, Inc. All rights reserved.
 *
 *******************************************************************************
 */

/*----------------------------------------------------------------------------------------
 *                             M O D U L E S    U S E D
 *----------------------------------------------------------------------------------------
 */

#include <DwI2cRegs.h>
#include <Library/DebugLib.h>
#include <Library/FchBaseLib.h>
#include <Library/TimerLib.h>
#include <Library/AmdBaseLib.h>
#include <Library/FchI2cLib.h>


/*----------------------------------------------------------------------------------------
 *                   D E F I N I T I O N S    A N D    M A C R O S
 *----------------------------------------------------------------------------------------
 */
#define FILECODE LIBRARY_FCHI2CLIB_FCHI2CLIB_FILECODE

/*----------------------------------------------------------------------------------------
 *                  T Y P E D E F S     A N D     S T R U C T U R E S
 *----------------------------------------------------------------------------------------
 */


/*----------------------------------------------------------------------------------------
 *           P R O T O T Y P E S     O F     L O C A L     F U N C T I O N S
 *----------------------------------------------------------------------------------------
 */


/*----------------------------------------------------------------------------------------
 *                          E X P O R T E D    F U N C T I O N S
 *----------------------------------------------------------------------------------------
 */

/**
  This function checks for errors during SMBUS access

  @param Base - Base address of the I2cController

  @retval UINT32 Status
*/

UINT32
I2cDwCheckErrors (
  IN  UINT32              Base,
  IN  I2cRegisterRead32   I2cRegRead32,
  IN  I2cRegisterWrite32  I2cRegWrite32
  )
{
  UINT32  I2cStatusCnt;
  UINT32  TxAbrt;
  UINT32  Status;
  UINT32  I2cPollTime = 0;

  I2cStatusCnt = DW_STATUS_WAIT_RETRY;
  TxAbrt = 0;
  Status = I2cRegRead32 (Base + DW_IC_RAW_INTR_STAT);
  // PEI and DXE polling time is same.
  I2cPollTime = (100 * 1000000) / configI2C_MAX_SPEED;

  if (Status & DW_IC_INTR_RX_UNDER) {
    I2cRegRead32 (Base + DW_IC_CLR_RX_UNDER);
  }
  if (Status & DW_IC_INTR_RX_OVER) {
    I2cRegRead32 (Base + DW_IC_CLR_RX_OVER);
  }
  if (Status & DW_IC_INTR_TX_ABRT) {
    Status = I2cRegRead32 (Base + DW_IC_TX_ABRT_SOURCE);
    I2cRegRead32 (Base + DW_IC_CLR_TX_ABRT);
    DEBUG ((DEBUG_ERROR, "TX_ABORT Error\n"));
  }

  if (Status & DW_IC_ERR_CONDITION) {
    if (Status || TxAbrt) {
      DEBUG ((DEBUG_ERROR, "Errors on I2cBus %08x %08x: \n", Status, TxAbrt));
    }
    I2cRegWrite32 (Base + DW_IC_ENABLE, 0); // Disable the adapter
    I2cDwWaitI2cEnable (Base, CHECK_IC_EN_HIGH, I2cRegRead32);  //Wait controller status change
    do {
      MicroSecondDelay (I2cPollTime);
      if (I2cStatusCnt == 0) {
        DEBUG ((DEBUG_ERROR, "Timeout during disable \n"));
      }
    } while (((I2cRegRead32 (Base + DW_IC_ENABLE_STATUS) & 0x01)) && I2cStatusCnt--);
    I2cRegWrite32 (Base + DW_IC_TAR, 0); // Clear Target address
  }

  return (Status & DW_IC_ERR_CONDITION);
}


/**
  This function waits for the TX FIFO to be available

  @param Base - Base address of the I2cController

  @retval EFI_STATUS
          - EFI_TIMEOUT: when Tx Fifo full empty
          - EFI_SUCCESS
*/

EFI_STATUS
I2cDwWaitTxData (
  IN  UINT32              Base,
  IN  I2cRegisterRead32   I2cRegRead32
  )
{
  INTN    Timeout;
  UINT32  I2cPollTime = 0;

  Timeout = DW_TX_DATA_RETRY;
  // PEI and DXE polling time is same.
  I2cPollTime = (100 * 1000000) / configI2C_MAX_SPEED;

  while (0 == (I2cRegRead32 (Base + DW_IC_STATUS) & DW_I2C_STATUS_TFE)) {
    if (Timeout <= 0) {
      DEBUG ((DEBUG_ERROR, "I2cTimeout waiting for Tx Fifo full empty \n"));
      return EFI_TIMEOUT;
    }
    Timeout--;
    MicroSecondDelay (I2cPollTime);
  }
  DEBUG ((DEBUG_VERBOSE, "I2C_STATUS : %x, Tx timeout remaining : %d of %d\n", I2cRegRead32 (Base + DW_IC_STATUS), Timeout, DW_TX_DATA_RETRY));
  return EFI_SUCCESS;
}

/**
  This function waits for the RX FIFO to be available

  @param Base - Base address of the I2cController

  @retval EFI_STATUS
          - EFI_TIMEOUT: when Rx data is not ready
          - EFI_SUCCESS
*/

EFI_STATUS
I2cDwWaitRxData (
  IN  UINT32              Base,
  IN  UINT32              RxFifoDepth,
  IN  I2cRegisterRead32   I2cRegRead32
  )
{
  INTN    Timeout;
  UINT32  I2cPollTime = 0;

  Timeout = DW_TX_DATA_RETRY;
  // PEI and DXE polling time is same.
  I2cPollTime = (100 * 1000000) / configI2C_MAX_SPEED;

  while (I2cRegRead32 (Base + DW_IC_RXFLR) < RxFifoDepth) {
    if (Timeout <= 0) {
      DEBUG ((DEBUG_ERROR, "I2cTimeout waiting for Rx data ready\n"));
      return EFI_TIMEOUT;
    }
    Timeout--;
    MicroSecondDelay (I2cPollTime);
  }
  DEBUG ((DEBUG_VERBOSE, "I2C_STATUS : %x, Rx timeout remaining : %d of %d\n", I2cRegRead32 (Base + DW_IC_STATUS), Timeout, DW_TX_DATA_RETRY));
  return EFI_SUCCESS;
}

/**
  This function waits for the controller status change

  @param Base - Base address of I2cController
  @param I2cEnBit: 0 (CHECK_IC_EN_LOW) or 1 (CHECK_IC_EN_HIGH)
          - I2cEnBit = 1, this will check if the controller is DISABLED or not.
            if IC_EN bit=0, then the I2C controller is disabled.
              - in this case, the function returns EFI_SUCCESS.
            if IC_EN bit=1, then the I2C controller is not still disabled. 
              - in this case, when retries timed out, the function returns EFI_TIMEOUT.
          - I2cEnBit = 0, this will check if the controller is ENABLED or not.
            if IC_EN bit=0, then the I2C controller is not enabled.
              - in this case, when retries timed out, the function returns EFI_TIMEOUT.
            if IC_EN bit=1, then the I2C controller is enabled.
              - in this case, the function returns EFI_SUCCESS.

  @retval EFI_STATUS: EFI_TIMEOUT or EFI_SUCCESS
*/

EFI_STATUS
I2cDwWaitI2cEnable (
  IN  UINT32              Base,
  IN  UINT32              I2cEnBit,
  IN  I2cRegisterRead32   I2cRegRead32
  )
{
  INTN    Timeout;
  UINT32  I2cPollTime = 0;

  Timeout = DW_STATUS_WAIT_RETRY;
  // PEI and DXE polling time is same.
  I2cPollTime = (100 * 1000000) / configI2C_MAX_SPEED;

  // When I2cEnBit=1, if IC_EN bit=0, then the I2C controller is now Disabled.
  // When I2cEnBit=0, if IC_EN bit=1, then the I2C controller is now Enabled.
  // From PPR,
  //   when read as 1 from bit 0 (IC_EN) of DW_IC_ENABLE_STATUS reg, DW_apb_i2c is deemed to be in an enabled state.
  //   when read as 0 from bit 0 (IC_EN) of DW_IC_ENABLE_STATUS reg, DW_apb_i2c is deemed completely inactive.
  while (I2cEnBit == (I2cRegRead32 (Base + DW_IC_ENABLE_STATUS) & DW_I2C_EN_STATUS_IC_EN)) {
    if (Timeout <= 0) {
      DEBUG ((DEBUG_ERROR, "[Debug] Timeout waiting for I2cEnable : %x\n", (I2cRegRead32 (Base + DW_IC_ENABLE_STATUS))));
      return EFI_TIMEOUT;
    }
    Timeout--;
    MicroSecondDelay (I2cPollTime);
  }
  return EFI_SUCCESS;
}

/**
  This function gets RX/TX FIFO Depth

  @param Base - Base address of I2cController
  @param RxTx - 0 for RX, 1 for TX

  @retval Depth - depth of FIFO for RX or TX
*/

UINT32
I2cGetRxTxFifoDepth (
  IN  UINT32              Base,
  IN  UINT32              RxTx,
  IN  I2cRegisterRead32   I2cRegRead32
  )
{
  UINT32  Depth = 0;

  if (RxTx == 0) {
    Depth = ((I2cRegRead32 (Base + DW_IC_COMP_PARAM_1) & DW_I2C_RX_BUFFER_DEPTH_MASK) >> DW_I2C_RX_BUFFER_DEPTH_SHIFT) + 1;
  } else {
    Depth = ((I2cRegRead32 (Base + DW_IC_COMP_PARAM_1) & DW_I2C_TX_BUFFER_DEPTH_MASK) >> DW_I2C_TX_BUFFER_DEPTH_SHIFT) + 1;
  }

  return Depth;
}

/**
  This function sets the target address for device on I2cBus

  @param Chip - Address of I2cDevice
  @param Base - Base address of the I2cController

  @retval EFI_STATUS
          - EFI_NOT_READY: when Disabling the controller failed.
          - EFI_DEVICE_ERROR:  when Enabling the controller failed.
          - EFI_SUCCESS
*/

EFI_STATUS
I2cSetTarget (
  IN  UINT32              Chip,
  IN  UINT32              Base,
  IN  I2cRegisterRead32   I2cRegRead32,
  IN  I2cRegisterWrite32  I2cRegWrite32
  )
{
  EFI_STATUS Status = EFI_SUCCESS;

  if (I2cRegRead32 (Base + DW_IC_TAR) != Chip) {
    I2cRegWrite32 (Base + DW_IC_ENABLE, 0); // Disable the Controller
    if (I2cDwWaitI2cEnable (Base, CHECK_IC_EN_HIGH, I2cRegRead32)) {
      return EFI_NOT_READY;
    }

    I2cRegWrite32 (Base + DW_IC_TAR, Chip); // Set Target Address
    DEBUG ((DEBUG_VERBOSE, "I2cTarget Set - Chip Address:%x\n", Chip));
    I2cRegWrite32 (Base + DW_IC_ENABLE, 1); // Enable the Controller
    if (I2cDwWaitI2cEnable (Base, CHECK_IC_EN_LOW, I2cRegRead32)) { //Check IC_EN bit
      return EFI_DEVICE_ERROR;
    }

    if (I2cRegRead32 (Base + DW_IC_TAR) != Chip) {
      DEBUG ((DEBUG_ERROR, "Cannot set the target on I2cBus to %x\n", Chip));
      Status = EFI_DEVICE_ERROR;
    }
  }
  return Status;
}

/**
  This function waits for bus to not be busy

  @param Base - Base address of the I2cController

  @retval TRUE - Timeout while waiting for not busy
  @retval FALSE - Bus is not busy
*/

BOOLEAN
I2cDwWaitBusNotBusy (
  IN  UINT32              Base,
  IN  I2cRegisterRead32   I2cRegRead32
  )
{
  INTN    Timeout;

  Timeout = DW_BUS_WAIT_TIMEOUT;

  while (I2cRegRead32 (Base + DW_IC_STATUS) & 0x20) {
    if (Timeout <= 0) {
      DEBUG ((DEBUG_ERROR, "I2cTimeout waiting for bus ready\n"));
      return TRUE;
    }
    Timeout--;
    MicroSecondDelay (1000);
  }
  return FALSE;
}

/**
  This function writes data

  @param Base - Base address of I2cController
  @param Data - Data to write
  @param DataLenInBytes - Data length
  @param TxFifoBufDepth - Tx Fifo Depth
  @param OperationCount - 1 for single step, and 2 for write-and-read step

  @retval EFI_STATUS
*/

EFI_STATUS
I2cPrivateWrite (
  IN  UINT32              Base,
  IN  UINT8               *Data,
  IN  UINT32              DataLenInBytes,
  IN  UINT32              TxFifoBufDepth,
  IN  UINTN               OperationCount,
  IN  I2cRegisterRead32   I2cRegRead32,
  IN  I2cRegisterWrite32  I2cRegWrite32
  )
{
  EFI_STATUS Status = EFI_SUCCESS;
  UINT32  Index;
  UINT32  TxFifoCount;

  DEBUG ((DEBUG_VERBOSE, "[Debug] Start Write Transaction: %d count.\n", DataLenInBytes));

  // Load up the TX FIFO
  while (0 < DataLenInBytes) {
    TxFifoCount = (TxFifoBufDepth < DataLenInBytes) ? TxFifoBufDepth : DataLenInBytes;
    DEBUG ((DEBUG_VERBOSE, "[Debug] Load up the TX FIFO, TxFifoBufDepth: %d\n", TxFifoCount));
    for (Index = 0; Index < TxFifoCount ; Index++) {
      if ((DataLenInBytes == 1) && (OperationCount == 1)) {
        DEBUG ((DEBUG_VERBOSE, "[Debug] Write Data: 0x%x\n", *(Data)));
        I2cRegWrite32 (Base + DW_IC_DATA_CMD, (*(Data++) & DW_I2C_DATA_MASK) | DW_I2C_DATA_STOP);
      } else {
        DEBUG ((DEBUG_VERBOSE, "[Debug] Write Data: 0x%x\n", *(Data)));
        I2cRegWrite32 (Base + DW_IC_DATA_CMD, *(Data++) & DW_I2C_DATA_MASK);
      }
      DataLenInBytes--;
    }
    DEBUG ((DEBUG_VERBOSE, "[Debug] Wait for TxFifo empty\n"));
    //Wait for TxFifo empty
    Status = I2cDwWaitTxData (Base, I2cRegRead32);
#if !defined(SIMNOW_BUILD)
    if (Status) {
      //Transcation failed, send STOP command to free the bus
      I2cRegWrite32 (Base + DW_IC_DATA_CMD, DW_I2C_DATA_STOP);
      return Status;
    }
#endif
  }
  DEBUG ((DEBUG_VERBOSE, "[Debug] Write Transfer Count : %d (should be 0)\n", DataLenInBytes));

  // Write complete
  DEBUG ((DEBUG_VERBOSE, "[Debug] Write complete\n"));

  return EFI_SUCCESS;
}

/**
  This function reads data

  @param Base - Base address of I2cController
  @param Data - Data to Read
  @param DataLenInBytes - Data length
  @param RxFifoBufDepth - Rx Fifo Depth

  @retval EFI_STATUS
*/

EFI_STATUS
I2cPrivateRead (
  IN  UINT32              Base,
  IN  UINT8               *Data,
  IN  UINT32              DataLenInBytes,
  IN  UINT32              RxFifoBufDepth,
  IN  I2cRegisterRead32   I2cRegRead32,
  IN  I2cRegisterWrite32  I2cRegWrite32
  )
{
  EFI_STATUS Status = EFI_SUCCESS;
  UINT32  Index;
  UINT32  RxFifoCount;

  DEBUG ((DEBUG_VERBOSE, "[Debug] Start Read Transaction: %d count.\n", DataLenInBytes));

  while (0 < DataLenInBytes) {
    RxFifoCount = (RxFifoBufDepth < DataLenInBytes) ? RxFifoBufDepth : DataLenInBytes;
    DEBUG ((DEBUG_VERBOSE, "[Debug] fille up Rx Fifo, RxFifoBufDepth : %d\n", RxFifoCount));
    // Fill up Rx Fifo
    for (Index = 0; Index < RxFifoCount ; Index++) {
      if (DataLenInBytes == 1) {
        I2cRegWrite32 (Base + DW_IC_DATA_CMD, DW_I2C_DATA_CMD | DW_I2C_DATA_STOP);
      } else {
        I2cRegWrite32 (Base + DW_IC_DATA_CMD, DW_I2C_DATA_CMD);
      }
      DataLenInBytes--;
    }

    DEBUG ((DEBUG_VERBOSE, "[Debug] Wait Rx data ready \n"));
    // Waiting for Rx data ready
    Status = I2cDwWaitRxData (Base, RxFifoCount, I2cRegRead32);
    if (Status) {
      //Transcation failed, send STOP command to free the bus
      I2cRegWrite32 (Base + DW_IC_DATA_CMD, DW_I2C_DATA_STOP);
      return Status;
    }
    DEBUG ((DEBUG_VERBOSE, "[Debug] Read data from Rxfifo\n"));
    for (Index = 0; Index < RxFifoCount; Index++) {
      if (I2cDwCheckErrors (Base, I2cRegRead32, I2cRegWrite32)) {
        I2cRegWrite32 (Base + DW_IC_DATA_CMD, DW_I2C_DATA_STOP);
        return EFI_DEVICE_ERROR;
      }
      *(Data++) = (UINT8) (I2cRegRead32 (Base + DW_IC_DATA_CMD) & DW_I2C_DATA_MASK); // Receive data unit from RxFifo
    }
    DEBUG ((DEBUG_VERBOSE, "[Debug] Read Transfer Count : %d\n", DataLenInBytes));
  }

  DEBUG ((DEBUG_VERBOSE, "[Debug] Read Transfer Count : %d (should be 0)\n", DataLenInBytes));

  // Read complete
  DEBUG ((DEBUG_VERBOSE, "[Debug] Read complete\n"));

  return EFI_SUCCESS;
}

/**
  This function initializes the controller

  @param Base - Base address of I2cController
  @param BusFrequency - Speed
  @param SdaHoldTime - SDA Hold Time

  @retval EFI_STATUS
*/

EFI_STATUS
EFIAPI
I2cInit (
  IN  UINT32              Base,
  IN  UINT32              BusFrequency,
  IN  UINT32              SdaHoldTime,
  IN  I2cRegisterRead32   I2cRegRead32,
  IN  I2cRegisterWrite32  I2cRegWrite32,
  IN  I2C_BUS_CLEAR_DATA  *I2cBusClearData  OPTIONAL
  )
{
  UINTN   BusClockHertz;
  UINT32  settings;
  UINT32  hcnt;
  UINT32  lcnt;

  // Disable the interface
  I2cRegWrite32 (Base + DW_IC_ENABLE, 0);
  if (I2cDwWaitI2cEnable (Base, CHECK_IC_EN_HIGH, I2cRegRead32)) {
    return EFI_NOT_READY;
  }

  //Mask Interrupt and Clear interrupt status
  I2cRegWrite32 (Base + DW_IC_INTR_MASK, 0);
  (VOID)I2cRegRead32 (Base + DW_IC_CLR_INTR);
  (VOID)I2cRegRead32 (Base + DW_IC_CLR_TX_ABRT);

  //This->RxFifoDepth = ((I2cRegRead32 (Base + DW_IC_COMP_PARAM_1) & DW_I2C_RX_BUFFER_DEPTH_MASK) >> DW_I2C_RX_BUFFER_DEPTH_SHIFT) + 1;
  //This->TxFifoDepth = ((I2cRegRead32 (Base + DW_IC_COMP_PARAM_1) & DW_I2C_TX_BUFFER_DEPTH_MASK) >> DW_I2C_TX_BUFFER_DEPTH_SHIFT) + 1;

  // Set hold time based on platform setting using new PCDs
  I2cRegWrite32 (Base + DW_IC_SDA_HOLD, SdaHoldTime);
  //Set default to Standard Speed
  switch (BusFrequency){
    case 0:
      BusClockHertz = SS_SPEED;
      break;
    case 1:
      BusClockHertz = FS_SPEED;
      break;
    case 2:
      BusClockHertz = HS_SPEED;
      break;
    default:
      BusClockHertz = SS_SPEED; //Set default to Standard Speed
      // ASSERT (FALSE);
      break;
  }
  //
  // SetBusFrequency
  //Status = SetBusFrequency (This, &BusClockHertz);
  //
  settings = 0;
  // Disable the interface
  I2cRegWrite32 (Base + DW_IC_ENABLE, 0);
  if (I2cDwWaitI2cEnable (Base, CHECK_IC_EN_HIGH, I2cRegRead32)) {
    return EFI_NOT_READY;
  }

  settings |= DW_I2C_CON_MASTER_MODE | DW_I2C_CON_IC_SLAVE_DISABLE;

  if (HS_SPEED <= BusClockHertz) {
    settings |= DW_I2C_CON_SPEED_HS;
    //*BusClockHertz = FS_SPEED;        //Return actually clock setting
    BusClockHertz = HS_SPEED;
  }
  else if (FS_SPEED <= BusClockHertz) {
    settings |= DW_I2C_CON_SPEED_FS;
    BusClockHertz = FS_SPEED;        //Return actually clock setting
  }
  else {
    settings |= DW_I2C_CON_SPEED_SS;
    BusClockHertz = SS_SPEED;        //Return actually clock setting
  }

  settings |= DW_I2C_CON_IC_RESTART_EN;

  I2cRegWrite32 (Base + DW_IC_CON, settings);

  // Setup spike suppression for SS and FS at 50ns
  I2cRegWrite32 (Base + DW_IC_FS_SPKLEN, configI2C_FS_GLITCH_FILTER);

  // Setup spike suppression for HS at 10ns
  I2cRegWrite32 (Base + DW_IC_FS_SPKLEN, configI2C_FS_GLITCH_FILTER);

  // Standard-mode 100Khz
  hcnt = AMD_SS_SCL_HCNT;
  lcnt = AMD_SS_SCL_LCNT;
  I2cRegWrite32 (Base + DW_IC_SS_SCL_HCNT, hcnt); // std speed high, 4us
  I2cRegWrite32 (Base + DW_IC_SS_SCL_LCNT, lcnt); // std speed low, 4.7us

  DEBUG ((DEBUG_INFO, "[Debug] Standard-mode HCNT:LCNT = %d:%d\n", hcnt, lcnt));

  // Fast-mode 400Khz
  hcnt = AMD_FS_SCL_HCNT;
  lcnt = AMD_FS_SCL_LCNT;
  I2cRegWrite32 (Base + DW_IC_FS_SCL_HCNT, hcnt);
  I2cRegWrite32 (Base + DW_IC_FS_SCL_LCNT, lcnt);
  I2cRegWrite32 (Base + DW_IC_HS_SCL_HCNT, hcnt);
  I2cRegWrite32 (Base + DW_IC_HS_SCL_LCNT, lcnt);

  DEBUG ((DEBUG_INFO, "[Debug] Fast-mode HCNT:LCNT = %d:%d\n", hcnt, lcnt));

  I2cSetStuckLowTimer(Base, I2cRegRead32, I2cRegWrite32, I2cBusClearData);

  return EFI_SUCCESS;
}


/**
*  This function enables SCL/SDA stuck low timer (Bus clear feature) for all the
*  ports which supports DDR telemetry
  @param Base - Base address of I2cController
  @param I2cRegisterRead32 -  Read32 function
  @param I2cRegisterWrite32 - Write32 funcition

  @retval EFI_STATUS
*/

EFI_STATUS
EFIAPI
I2cSetStuckLowTimer (
  IN  UINT32              Base,
  IN  I2cRegisterRead32   I2cRegRead32,
  IN  I2cRegisterWrite32  I2cRegWrite32,
  IN  I2C_BUS_CLEAR_DATA  *I2cBusClearData  OPTIONAL
  )
{
   UINT32             SCLStuckLowValue = 0;
   UINT32             SDAStuckLowValue = 0;
   UINT32             settings;

   if ( I2cBusClearData == NULL ) return EFI_UNSUPPORTED;

   SCLStuckLowValue = I2cBusClearData->SCLStuckLowTime;
   SDAStuckLowValue = I2cBusClearData->SDAStuckLowTime;

   DEBUG ((DEBUG_INFO, "BusClsFeature en, Set StuckLow value: SCL= 0x%x  SDA=0x%x\n", SCLStuckLowValue, SDAStuckLowValue));
   I2cRegWrite32 ( Base + DW_IC_SCL_STUCK_AT_LOW_TIMEOUT, SCLStuckLowValue);
   I2cRegWrite32 ( Base + DW_IC_SDA_STUCK_AT_LOW_TIMEOUT, SDAStuckLowValue);
   //enalbe Buc clear feature
   settings = I2cRegRead32 ( Base + DW_IC_CON);
   settings |= DW_I2C_CON_BUS_CLEAR_FEATURE_CTRL;
   I2cRegWrite32 ( Base + DW_IC_CON, settings);

   return EFI_SUCCESS;
}
