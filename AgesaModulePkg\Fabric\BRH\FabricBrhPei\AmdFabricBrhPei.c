/*****************************************************************************
 * Copyright (C) 2008-2024 Advanced Micro Devices, Inc. All rights reserved.
 *
 *****************************************************************************/

/**
 * @file
 *
 * AMD Fabric - API, and related functions.
 *
 * Contains code that initializes the data fabric
 *
 * @xrefitem bom "File Content Label" "Release Content"
 * @e project:      AGESA
 * @e sub-project:  Fabric
 * @e \$Revision$   @e \$Date$
 *
 */

#include <PiPei.h>
#include "AGESA.h"
#include <Filecode.h>
#include <Library/PeiFabricTopologyServices2Lib.h>
#include <Library/PeiFabricResourceManagerServicesLib.h>
#include <Ppi/AmdFabricSocSpecificServicesPpi.h>
#include "FabricPieRasInit.h"
#include <Library/AmdIdsHookLib.h>
#include "Library/IdsLib.h"
#include <Ppi/SocLogicalIdPpi.h>

/*----------------------------------------------------------------------------------------
 *                   D E F I N I T I O N S    A N D    M A C R O S
 *----------------------------------------------------------------------------------------
 */

#define FILECODE FABRIC_BRH_FABRICBRHPEI_AMDFABRICBRHPEI_FILECODE

/*----------------------------------------------------------------------------------------
 *           P R O T O T Y P E S     O F     L O C A L     F U N C T I O N S
 *----------------------------------------------------------------------------------------
 */

/*----------------------------------------------------------------------------------------
 *                           G L O B A L   V A R I A B L E S
 *----------------------------------------------------------------------------------------
 */

/*----------------------------------------------------------------------------------------
 *                  T Y P E D E F S     A N D     S T R U C T U R E S
 *----------------------------------------------------------------------------------------
 */

/*----------------------------------------------------------------------------------------
 *                          E X P O R T E D    F U N C T I O N S
 *----------------------------------------------------------------------------------------
 */

/*----------------------------------------------------------------------------------------*/
/**
 * @brief Fabric - IOD Driver Entry. Initialize the data fabric.
 *
 * @param[in] FileHandle
 * @param[in] PeiServices Pointer to the PEI services object.
 *
 * @return EFI_STATUS
 */
EFI_STATUS
EFIAPI
AmdFabricBrhPeiInit (
  IN     EFI_PEI_FILE_HANDLE         FileHandle,
  IN     CONST EFI_PEI_SERVICES    **PeiServices
  )
{
  EFI_STATUS                               Status;
  EFI_STATUS                               CalledStatus;
  AMD_PEI_SOC_LOGICAL_ID_PPI               *SocLogicalIdPpi;
  PEI_AMD_FABRIC_SOC_SPECIFIC_SERVICES_PPI *SocSpecificServicesPpi;

  AGESA_TESTPOINT (TpDfPeiEntry, NULL);

  Status = EFI_SUCCESS;

  IDS_HDT_CONSOLE (CPU_TRACE, "  AmdFabricBrhPeiInit Entry\n");

  CalledStatus = (*PeiServices)->LocatePpi (PeiServices,
                                            &gAmdSocLogicalIdPpiGuid,
                                            0,
                                            NULL,
                                            (VOID **)&SocLogicalIdPpi);

  ASSERT (CalledStatus == EFI_SUCCESS);

  CalledStatus = (*PeiServices)->LocatePpi (PeiServices,
                                            &gAmdFabricSocSpecificServicesPpiGuid,
                                            0,
                                            NULL,
                                            (VOID **)&SocSpecificServicesPpi);

  ASSERT (CalledStatus == EFI_SUCCESS);

  // Publish PEI topology services
  CalledStatus = FabricTopologyService2PpiInstall (PeiServices);

  Status = (CalledStatus > Status) ? CalledStatus : Status;

  // Publish PEI resource manager services
  CalledStatus = FabricResourceManagerServicePpiInstall (PeiServices);

  Status = (CalledStatus > Status) ? CalledStatus : Status;

  IDS_HDT_CONSOLE (CPU_TRACE, "    PIE RAS Init\n");
  FabricPieRasInit (PeiServices, SocLogicalIdPpi, SocSpecificServicesPpi);

  IDS_HOOK (IDS_HOOK_FABRIC_PEI_INIT_END, NULL, NULL);

  IDS_HDT_CONSOLE (CPU_TRACE, "  AmdFabricBrhPeiInit End\n");

  AGESA_TESTPOINT (TpDfPeiExit, NULL);

  return Status;
}

