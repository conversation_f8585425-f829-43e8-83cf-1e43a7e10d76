/*****************************************************************************
 *
 * Copyright (C) 2008-2024 Advanced Micro Devices, Inc. All rights reserved.
 *
 *******************************************************************************
 */

#include "FchSmm.h"
#include <Protocol/PspMboxSmmBufferAddressProtocol.h>
#include <Library/AmdPspMboxLibV2.h>

#include "FchPlatform.h"

#define FILECODE FCH_KUNLUN_FCHKUNLUNSMM_SWSMI_FILECODE

extern FCH_DATA_BLOCK                   gFchDataInSmm;
extern  UINT8                           *mFchPciIrqRoutingTable;
BOOLEAN                                 mPcdFchLockFCHReg = FALSE;
extern UINT8                            *mPspMboxSmmBuffer;
extern BOOLEAN                          *mPspMboxSmmFlagAddr;
BOOLEAN                                 mPcdFchOemGpioFencing = FALSE;
PSP_MBOX_SMMBUFFER_ADDRESS_PROTOCOL     *PspMboxSmmBufferAddressProtocol;
extern UINT32                            DimmsPerChannel;
extern BOOLEAN                           IsFencingEnabled;

UINT16
FchGetPmBase (
  VOID
  )
{
  UINT16  Value16;

  Value16 = 0;

  LibFchSmmPmioRead16V2 (&gSmst->SmmIo, FCH_PMIOA_REG60, &Value16);
  return Value16;
}

EFI_STATUS
EFIAPI
AmdFchOemSecureSmi (
  )
{
  EFI_STATUS          Status = EFI_SUCCESS;

  if (mPspMboxSmmFlagAddr == NULL) {
      Status = gSmst->SmmLocateProtocol (&gPspMboxSmmBufferAddressProtocolGuid, NULL, (VOID **)&PspMboxSmmBufferAddressProtocol);
      if (Status == EFI_SUCCESS) {
        mPspMboxSmmBuffer = PspMboxSmmBufferAddressProtocol->PspMboxSmmBuffer;
        mPspMboxSmmFlagAddr = PspMboxSmmBufferAddressProtocol->PspMboxSmmFlagAddr;
        IDS_HDT_CONSOLE_PSP_TRACE ("\tmPspMboxSmmBuffer 0x%x\n", mPspMboxSmmBuffer);
        IDS_HDT_CONSOLE_PSP_TRACE ("\tmPspMboxSmmFlagAddr 0x%x\n", mPspMboxSmmFlagAddr);
      } else {
        CpuDeadLoop ();
        return Status;
      }
  }
  if (mPcdFchLockFCHReg == TRUE) {
    *mPspMboxSmmFlagAddr = TRUE;
    Status = PspMboxBiosLockFCHReg (NULL);
    *mPspMboxSmmFlagAddr = FALSE;
    if (Status != EFI_SUCCESS) {
      return Status;
    }
  }
  if (mPcdFchOemGpioFencing == TRUE) {
    *mPspMboxSmmFlagAddr = TRUE;
    Status = PspMboxBiosLockGpio (NULL);
    *mPspMboxSmmFlagAddr = FALSE;
    if (Status != EFI_SUCCESS) {
      return Status;
    }
  }

  return Status;
}

EFI_STATUS
EFIAPI
AmdFchOemSecureSmiCallback (
  IN       EFI_HANDLE                        DispatchHandle,
  IN       CONST FCH_SMM_SW_REGISTER_CONTEXT *DispatchContext,
  IN OUT   FCH_SMM_SW_CONTEXT                *SwContext,
  IN OUT   UINTN                             *SizeOfSwContext
  )
{
  return AmdFchOemSecureSmi ();
}


/**
 * @brief Handler for before PCI S3 restore.
 *
 * @param[in] DispatchHandle   The handle of this callback, obtained when registering
 * @param[in] DispatchContext  Pointer to the FCH_SMM_SW_REGISTER_CONTEXT
 * @param[in] SwContext        Pointer to the FCH_SMM_SW_CONTEXT context buffer
 * @param[in] SizeOfSwContext  Pointer to the context size
 *
 * @returns EFI_STATUS
 * @retval EFI_SUCCESS   Module initialized successfully
 * @retval EFI_ERROR     Initialization failed (see error for more details)
 */
EFI_STATUS
EFIAPI
AmdSmiBeforePciS3RestoreCallback (
  IN       EFI_HANDLE                        DispatchHandle,
  IN       CONST FCH_SMM_SW_REGISTER_CONTEXT *DispatchContext,
  IN OUT   FCH_SMM_SW_CONTEXT                *SwContext,
  IN OUT   UINTN                             *SizeOfSwContext
  )
{
  FCH_DATA_BLOCK      *pFchPolicy;
  EFI_STATUS          Status;
  UINT8               Index;
  UINT8               *pData;

  Status = EFI_SUCCESS;

  // Restore entire FCH PCI IRQ routing space (C00/C01)
  pData = mFchPciIrqRoutingTable;
  Index = 0xFF;
  do {
    Index++;
    LibFchSmmIoWrite (&gSmst->SmmIo, SMM_IO_UINT8, FCH_IOMAP_REGC00, &Index);
    LibFchSmmIoWrite (&gSmst->SmmIo, SMM_IO_UINT8, FCH_IOMAP_REGC01, pData++);
  } while (Index != 0xFF);

  pFchPolicy = &gFchDataInSmm;
  FchInitS3EarlyRestore (pFchPolicy);
  return Status;
}


/**
 * @brief Handler for after PCI S3 restore.
 *
 * @param[in] DispatchHandle   The handle of this callback, obtained when registering
 * @param[in] DispatchContext  Pointer to the FCH_SMM_SW_REGISTER_CONTEXT
 * @param[in] SwContext        Pointer to the FCH_SMM_SW_CONTEXT context buffer
 * @param[in] SizeOfSwContext  Pointer to the context size
 *
 * @returns EFI_STATUS
 * @retval EFI_SUCCESS   Module initialized successfully
 * @retval EFI_ERROR     Initialization failed (see error for more details)
 */
EFI_STATUS
EFIAPI
AmdSmiAfterPciS3RestoreCallback (
  IN       EFI_HANDLE                        DispatchHandle,
  IN       CONST FCH_SMM_SW_REGISTER_CONTEXT *DispatchContext,
  IN OUT   FCH_SMM_SW_CONTEXT                *SwContext,
  IN OUT   UINTN                             *SizeOfSwContext
  )
{
  FCH_DATA_BLOCK      *pFchPolicy;
  EFI_STATUS          Status;

  Status = EFI_SUCCESS;
  pFchPolicy = &gFchDataInSmm;
  FchInitS3LateRestore (pFchPolicy);
  AmdFchOemSecureSmi ();
  return Status;
}


/**
 * @brief Handler for ACPI on.
 *
 * @param[in] DispatchHandle   The handle of this callback, obtained when registering
 * @param[in] DispatchContext  Pointer to the FCH_SMM_SW_REGISTER_CONTEXT
 * @param[in] SwContext        Pointer to the FCH_SMM_SW_CONTEXT context buffer
 * @param[in] SizeOfSwContext  Pointer to the context size
 *
 * @returns EFI_STATUS
 * @retval EFI_SUCCESS   Module initialized successfully
 * @retval EFI_ERROR     Initialization failed (see error for more details)
 */
EFI_STATUS
EFIAPI
AmdSmiAcpiOnCallback (
  IN       EFI_HANDLE                        DispatchHandle,
  IN       CONST FCH_SMM_SW_REGISTER_CONTEXT *DispatchContext,
  IN OUT   FCH_SMM_SW_CONTEXT                *SwContext,
  IN OUT   UINTN                             *SizeOfSwContext
  )
{
  FCH_DATA_BLOCK      *pFchPolicy;
  UINT16              AcpiPmbase;
  UINT8               dbIndex;
  UINT8               dbIrq;
  UINT32              ddValue;

  AcpiPmbase = FchGetPmBase ();

  pFchPolicy = &gFchDataInSmm;
  FchSmmAcpiOn (pFchPolicy);

  // Disable all GPE events and clear all GPE status
  ddValue = 0;
  LibFchSmmIoWrite (&gSmst->SmmIo, SMM_IO_UINT32, AcpiPmbase + R_FCH_ACPI_EVENT_ENABLE, &ddValue);
  LibFchSmmIoWrite (&gSmst->SmmIo, SMM_IO_UINT32, AcpiPmbase + R_FCH_ACPI_EVENT_STATUS, &ddValue);

  // Set ACPI IRQ to IRQ9 for non-APIC OSes
  dbIndex = 0x10;                                          // PIC - SCI
  dbIrq = 9;
  LibFchSmmIoWrite (&gSmst->SmmIo, SMM_IO_UINT8, FCH_IOMAP_REGC00, &dbIndex);
  LibFchSmmIoWrite (&gSmst->SmmIo, SMM_IO_UINT8, FCH_IOMAP_REGC01, &dbIrq);

  dbIndex |= BIT7;                                         // IOAPIC - SCI
  LibFchSmmIoWrite (&gSmst->SmmIo, SMM_IO_UINT8, FCH_IOMAP_REGC00, &dbIndex);
  LibFchSmmIoWrite (&gSmst->SmmIo, SMM_IO_UINT8, FCH_IOMAP_REGC01, &dbIrq);

  // Finally enable SCI
  LibFchSmmIoRead (&gSmst->SmmIo, SMM_IO_UINT32, AcpiPmbase + R_FCH_ACPI_PM_CONTROL, &ddValue);
  ddValue |= BIT0;
  LibFchSmmIoWrite (&gSmst->SmmIo, SMM_IO_UINT32, AcpiPmbase + R_FCH_ACPI_PM_CONTROL, &ddValue);

  return EFI_SUCCESS;
}


/**
 * @brief Handler for ACPI off.
 *
 * @param[in] DispatchHandle   The handle of this callback, obtained when registering
 * @param[in] DispatchContext  Pointer to the FCH_SMM_SW_REGISTER_CONTEXT
 * @param[in] SwContext        Pointer to the FCH_SMM_SW_CONTEXT context buffer
 * @param[in] SizeOfSwContext  Pointer to the context size
 *
 * @returns EFI_STATUS
 * @retval EFI_SUCCESS   Module initialized successfully
 * @retval EFI_ERROR     Initialization failed (see error for more details)
 */
EFI_STATUS
EFIAPI
AmdSmiAcpiOffCallback (
  IN       EFI_HANDLE                        DispatchHandle,
  IN       CONST FCH_SMM_SW_REGISTER_CONTEXT *DispatchContext,
  IN OUT   FCH_SMM_SW_CONTEXT                *SwContext,
  IN OUT   UINTN                             *SizeOfSwContext
  )
{
  UINT16      AcpiPmbase;
  UINT32      ddValue;

  AcpiPmbase = FchGetPmBase ();

  // Turn off SCI
  LibFchSmmIoRead (&gSmst->SmmIo, SMM_IO_UINT32, AcpiPmbase + R_FCH_ACPI_PM_CONTROL, &ddValue);
  ddValue &= ~BIT0;
  LibFchSmmIoWrite (&gSmst->SmmIo, SMM_IO_UINT32, AcpiPmbase + R_FCH_ACPI_PM_CONTROL, &ddValue);

  return EFI_SUCCESS;
}

#ifdef FCH_SPI_PROTECT_SMI
EFI_STATUS
EFIAPI
AmdSmiSpiUnlockCallback (
  IN       EFI_HANDLE                        DispatchHandle,
  IN       CONST FCH_SMM_SW_REGISTER_CONTEXT *DispatchContext,
  IN OUT   FCH_SMM_SW_CONTEXT                *SwContext,
  IN OUT   UINTN                             *SizeOfSwContext
  )
{
  FCH_DATA_BLOCK      *pFchPolicy;
  EFI_STATUS          Status;

  Status = EFI_SUCCESS;
  pFchPolicy = &gFchDataInSmm;
  FchSpiUnlock (pFchPolicy);
  return Status;
}

EFI_STATUS
EFIAPI
AmdSmiSpiLockCallback (
  IN       EFI_HANDLE                        DispatchHandle,
  IN       CONST FCH_SMM_SW_REGISTER_CONTEXT *DispatchContext,
  IN OUT   FCH_SMM_SW_CONTEXT                *SwContext,
  IN OUT   UINTN                             *SizeOfSwContext
  )
{
  FCH_DATA_BLOCK      *pFchPolicy;
  EFI_STATUS          Status;

  Status = EFI_SUCCESS;
  pFchPolicy = &gFchDataInSmm;
  FchSpiLock (pFchPolicy);
  return Status;
}
#endif

STATIC UINT8          SavePort80 = 0x00;


/**
 * @brief Handler for start timer.
 *
 * @param[in] DispatchHandle   The handle of this callback, obtained when registering
 * @param[in] DispatchContext  Pointer to the FCH_SMM_SW_REGISTER_CONTEXT
 * @param[in] SwContext        Pointer to the FCH_SMM_SW_CONTEXT context buffer
 * @param[in] SizeOfSwContext  Pointer to the context size
 *
 * @returns EFI_STATUS
 * @retval EFI_SUCCESS   Module initialized successfully
 */
EFI_STATUS
EFIAPI
AmdStartTimerSmiCallback (
  IN       EFI_HANDLE                        DispatchHandle,
  IN       CONST FCH_SMM_SW_REGISTER_CONTEXT *DispatchContext,
  IN OUT   FCH_SMM_SW_CONTEXT                *SwContext,
  IN OUT   UINTN                             *SizeOfSwContext
  )
{
  SavePort80 = ACPIMMIO8 (ACPI_MMIO_BASE + MISC_BASE + FCH_MISC_REG78);

  ACPIMMIO16 (ACPI_MMIO_BASE + SMI_BASE + FCH_SMI_REG96) |= SMI_TIMER_ENABLE;
  return EFI_SUCCESS;
}


/**
 * @brief Handler for stop timer.
 *
 * @param[in] DispatchHandle   The handle of this callback, obtained when registering
 * @param[in] DispatchContext  Pointer to the FCH_SMM_SW_REGISTER_CONTEXT
 * @param[in] SwContext        Pointer to the FCH_SMM_SW_CONTEXT context buffer
 * @param[in] SizeOfSwContext  Pointer to the context size
 *
 * @returns EFI_STATUS
 * @retval EFI_SUCCESS   Module initialized successfully
 */
EFI_STATUS
EFIAPI
AmdStopTimerSmiCallback (
  IN       EFI_HANDLE                        DispatchHandle,
  IN       CONST FCH_SMM_SW_REGISTER_CONTEXT *DispatchContext,
  IN OUT   FCH_SMM_SW_CONTEXT                *SwContext,
  IN OUT   UINTN                             *SizeOfSwContext
  )
{
  ACPIMMIO16 (ACPI_MMIO_BASE + SMI_BASE + FCH_SMI_REG96) &= ~SMI_TIMER_ENABLE;
  LibFchSmmIoWrite (&gSmst->SmmIo, SMM_IO_UINT8, 0x80, &SavePort80);
  return EFI_SUCCESS;
}


#if FCH_CAPTURE_RELEASE_SPD_BUS
EFI_STATUS
EFIAPI
AmdFchOemCaptureSPDBusSmiCallback (
  IN       EFI_HANDLE                        DispatchHandle,
  IN       CONST FCH_SMM_SW_REGISTER_CONTEXT *DispatchContext,
  IN OUT   FCH_SMM_SW_CONTEXT                *SwContext,
  IN OUT   UINTN                             *SizeOfSwContext
  )
{
  PCI_ADDR        NbioPciAddress;
  UINT32          SmuArg[6];
  UINT32          SmuStatus;
  UINT32          I2cAddress;
  UINT32          Value32;
  EFI_STATUS      Status;
  UINT32          FenceStartAddress = 0x00000000;
  UINT32          FenceEndAddress = 0x00000000;

  SmuStatus   = 0;
  NbioPciAddress.AddressValue = 0;
  I2cAddress  = 0;
  Value32     = 0;

  IDS_HDT_CONSOLE (FCH_TRACE, "%a - Entry\n", __FUNCTION__);

  // 1. Assert the SPD_MUX_CTRL_L pin
  if ( gFchDataInSmm.HwAcpi.SpdHostCtrlRelease ) {
    IDS_HDT_CONSOLE (FCH_TRACE, "Assert the SPD_MUX_CTRL_L pin\n");
    I2cAddress  = ACPI_MMIO_BASE + GPIO_BANK0_BASE + FCH_BP_AGPIO3_SPD_HOST_CTRL_L;
    Value32 = ACPIMMIO32 (I2cAddress);
    Value32 &= (UINT32)(~BIT23);
    Value32 &= (UINT32)(~BIT22);
    ACPIMMIO32 (I2cAddress) = Value32;
  } else {
    // 2. Stop PMFW Telemetry
    if ( gFchDataInSmm.HwAcpi.DimmTelemetry ) {
      IDS_HDT_CONSOLE (FCH_TRACE, "Send message to SMU for PMFW stop polling DIMM telemetry.\n");
      NbioSmuServiceCommonInitArguments (SmuArg);
      NbioPciAddress.AddressValue = MAKE_SBDFO (0, 0, 0, 0, 0);
      if ( gFchDataInSmm.FchRunTime.FchDeviceEnableMap & BIT21 ) {
        SmuArg[0] = 1;  // Use I3C controller
      } else if ( gFchDataInSmm.FchRunTime.FchDeviceEnableMap & BIT5 ) {
        SmuArg[0] = 0;  // Use I2C controller
      }

      if (IsFencingEnabled) {
        IDS_HDT_CONSOLE (FCH_TRACE, "Send mailbox command to PSP for unfencing I2C/I3C ports related to DIMM Telemetry.\n");
        if (mPspMboxSmmFlagAddr == NULL) {
          Status = gSmst->SmmLocateProtocol (&gPspMboxSmmBufferAddressProtocolGuid, NULL, (VOID **)&PspMboxSmmBufferAddressProtocol);
          if (Status == EFI_SUCCESS) {
            mPspMboxSmmBuffer = PspMboxSmmBufferAddressProtocol->PspMboxSmmBuffer;
            mPspMboxSmmFlagAddr = PspMboxSmmBufferAddressProtocol->PspMboxSmmFlagAddr;
            IDS_HDT_CONSOLE_PSP_TRACE ("\tmPspMboxSmmBuffer 0x%x\n", mPspMboxSmmBuffer);
            IDS_HDT_CONSOLE_PSP_TRACE ("\tmPspMboxSmmFlagAddr 0x%x\n", mPspMboxSmmFlagAddr);
          } else {
            IDS_HDT_CONSOLE (FCH_TRACE, "[Error] PspMboxSmmBufferAddressProtocol not found\n");
            return Status;
          }
        }

        *mPspMboxSmmFlagAddr = TRUE;
        Status = PspMboxBiosFenceI2cI3cPort (FenceStartAddress, FenceEndAddress);
        *mPspMboxSmmFlagAddr = FALSE;
        if (Status != EFI_SUCCESS) {
          IDS_HDT_CONSOLE (FCH_TRACE, "[Error] PspMboxBiosFenceI2cI3cPort failed\n");
        }
      } else {
        IDS_HDT_CONSOLE (FCH_TRACE, "IxC Fencing control is disabled.\n");
      }

      SmuStatus = NbioSmuServiceRequest (
                    NbioPciAddress,
                    BIOSSMC_MSG_StopDimmTelemetryReading,
                    SmuArg,
                    0
                    );
      if ( BIOSSMC_Result_OK == SmuStatus ) {
        IDS_HDT_CONSOLE (FCH_TRACE, "Send message to SMU successfully.\n");
      } else {
        IDS_HDT_CONSOLE (FCH_TRACE, "Send message to SMU failed.\n");
      }
    }
  }
  IDS_HDT_CONSOLE (FCH_TRACE, "%a - Exit\n", __FUNCTION__);
  return EFI_SUCCESS;
}


EFI_STATUS
EFIAPI
AmdFchOemReleaseSPDBusSmiCallback (
  IN       EFI_HANDLE                        DispatchHandle,
  IN       CONST FCH_SMM_SW_REGISTER_CONTEXT *DispatchContext,
  IN OUT   FCH_SMM_SW_CONTEXT                *SwContext,
  IN OUT   UINTN                             *SizeOfSwContext
  )
{
  PCI_ADDR        NbioPciAddress;
  UINT32          SmuArg[6];
  UINT32          SmuStatus;
  UINT32          I2cAddress;
  UINT32          Value32;
  EFI_STATUS      Status;
  UINT32          FenceStartAddress = 0x00000000;
  UINT32          FenceEndAddress = 0x00000000;

  SmuStatus   = 0;
  NbioPciAddress.AddressValue = 0;
  I2cAddress  = 0;
  Value32     = 0;

  IDS_HDT_CONSOLE (FCH_TRACE, "%a - Entry\n", __FUNCTION__);

  // 1. Deassert the SPD_MUX_CTRL_L pin
  if ( gFchDataInSmm.HwAcpi.SpdHostCtrlRelease ) {
    IDS_HDT_CONSOLE (FCH_TRACE, "Deassert the SPD_MUX_CTRL_L pin\n");
    I2cAddress  = ACPI_MMIO_BASE + GPIO_BANK0_BASE + FCH_BP_AGPIO3_SPD_HOST_CTRL_L;
    Value32 = ACPIMMIO32 (I2cAddress);
    Value32 |= BIT23;
    Value32 |= BIT22;
    ACPIMMIO32 (I2cAddress) = Value32;
  } else {
    // 2. Resume PMFW Telemetry
    if ( gFchDataInSmm.HwAcpi.DimmTelemetry ) {
      IDS_HDT_CONSOLE (FCH_TRACE, "Send message to SMU for PMFW resume polling DIMM telemetry.\n");

      NbioSmuServiceCommonInitArguments (SmuArg);
      NbioPciAddress.AddressValue = MAKE_SBDFO (0, 0, 0, 0, 0);
      if ( gFchDataInSmm.FchRunTime.FchDeviceEnableMap & BIT21 ) {
        SmuArg[0] = 1;  // Use I3C controller
        FenceStartAddress = FCH_I3C_DIMM_TELEMETRY_START_ADDRESS;

        if (DimmsPerChannel == 1) { //1DPC
          FenceEndAddress = FCH_I3C_DIMM_TELEMETRY_END_ADDRESS_1DPC;
        } else if (DimmsPerChannel == 2) {  //2DPC
          FenceEndAddress = FCH_I3C_DIMM_TELEMETRY_END_ADDRESS_2DPC;
        }
      } else if ( gFchDataInSmm.FchRunTime.FchDeviceEnableMap & BIT5 ) {
        SmuArg[0] = 0;  // Use I2C controller
        FenceStartAddress = FCH_I2C_DIMM_TELEMETRY_START_ADDRESS;

        if (DimmsPerChannel == 1) { //1DPC
          FenceEndAddress = FCH_I2C_DIMM_TELEMETRY_END_ADDRESS_1DPC;
        } else if (DimmsPerChannel == 2) {  //2DPC
          FenceEndAddress = FCH_I2C_DIMM_TELEMETRY_END_ADDRESS_2DPC;
        }
      }

      if (IsFencingEnabled) {
        IDS_HDT_CONSOLE (FCH_TRACE, "Send mailbox command to PSP for fencing DIMM Telemetry Ixc ports.\n");

        if (mPspMboxSmmFlagAddr == NULL) {
          Status = gSmst->SmmLocateProtocol (&gPspMboxSmmBufferAddressProtocolGuid, NULL, (VOID **)&PspMboxSmmBufferAddressProtocol);
          if (Status == EFI_SUCCESS) {
            mPspMboxSmmBuffer = PspMboxSmmBufferAddressProtocol->PspMboxSmmBuffer;
            mPspMboxSmmFlagAddr = PspMboxSmmBufferAddressProtocol->PspMboxSmmFlagAddr;
            IDS_HDT_CONSOLE_PSP_TRACE ("\tmPspMboxSmmBuffer 0x%x\n", mPspMboxSmmBuffer);
            IDS_HDT_CONSOLE_PSP_TRACE ("\tmPspMboxSmmFlagAddr 0x%x\n", mPspMboxSmmFlagAddr);
          } else {
            IDS_HDT_CONSOLE (FCH_TRACE, "[Error] PspMboxSmmBufferAddressProtocol not found\n");
            return Status;
          }
        }

        *mPspMboxSmmFlagAddr = TRUE;
        Status = PspMboxBiosFenceI2cI3cPort (FenceStartAddress, FenceEndAddress);
        *mPspMboxSmmFlagAddr = FALSE;
        if (Status != EFI_SUCCESS) {
          IDS_HDT_CONSOLE (FCH_TRACE, "[Error] PspMboxBiosFenceI2cI3cPort failed\n");
        }
      } else {
        IDS_HDT_CONSOLE (FCH_TRACE, "IxC Fencing control is disabled.\n");
      }

      SmuStatus = NbioSmuServiceRequest (
                    NbioPciAddress,
                    BIOSSMC_MSG_StartDimmTelemetryReading,
                    SmuArg,
                    0
                    );
      if ( BIOSSMC_Result_OK == SmuStatus ) {
        IDS_HDT_CONSOLE (FCH_TRACE, "Send message to SMU successfully.\n");
      } else {
        IDS_HDT_CONSOLE (FCH_TRACE, "Send message to SMU failed.\n");
      }
    }
  }

  IDS_HDT_CONSOLE (FCH_TRACE, "%a - Exit\n", __FUNCTION__);
  return EFI_SUCCESS;
}
#endif


/**
 * @brief Register Software SMI.
 *
 * @returns EFI_STATUS
 * @retval EFI_SUCCESS   Module initialized successfully
 * @retval EFI_ERROR     Initialization failed (see error for more details)
 */
EFI_STATUS
FchSmmRegisterSwSmi (
  VOID
  )
{
  EFI_STATUS                               Status;
  FCH_SMM_SW_DISPATCH2_PROTOCOL            *AmdSwDispatch;
  FCH_SMM_SW_REGISTER_CONTEXT              SwRegisterContext;
  EFI_HANDLE                               SwHandle;
  FCH_MISC                                 *FchMisc;

  FchMisc = &gFchDataInSmm.Misc;

  //
  //  Locate SMM SW dispatch protocol
  //
  Status = gSmst->SmmLocateProtocol (
                  &gFchSmmSwDispatch2ProtocolGuid,
                  NULL,
                  (VOID **)&AmdSwDispatch
                  );
  ASSERT_EFI_ERROR (Status);

  SwRegisterContext.AmdSwValue  = PcdGet8 (PcdFchOemBeforePciRestoreSwSmi); // use of PCD in place of FCHOEM_BEFORE_PCI_RESTORE_SWSMI    0xD3
  if (SwRegisterContext.AmdSwValue != 0) {
    SwRegisterContext.Order       = 0x80;
    Status = AmdSwDispatch->Register (
                              AmdSwDispatch,
                              AmdSmiBeforePciS3RestoreCallback,
                              &SwRegisterContext,
                              &SwHandle
                              );
    if (EFI_ERROR (Status)) {
      return Status;
    }
  }


  SwRegisterContext.AmdSwValue  = PcdGet8 (PcdFchOemAfterPciRestoreSwSmi); // use of PCD in place of FCHOEM_AFTER_PCI_RESTORE_SWSMI    0xD4
  if (SwRegisterContext.AmdSwValue != 0) {
    SwRegisterContext.Order       = 0x80;
    Status = AmdSwDispatch->Register (
                              AmdSwDispatch,
                              AmdSmiAfterPciS3RestoreCallback,
                              &SwRegisterContext,
                              &SwHandle
                              );
    if (EFI_ERROR (Status)) {
      return Status;
    }
  }


  SwRegisterContext.AmdSwValue  = PcdGet8 (PcdFchOemEnableAcpiSwSmi); // use of PCD in place of FCHOEM_ENABLE_ACPI_SWSMI           0xA0
  SwRegisterContext.Order       = 0x80;
  Status = AmdSwDispatch->Register (
                            AmdSwDispatch,
                            AmdSmiAcpiOnCallback,
                            &SwRegisterContext,
                            &SwHandle
                            );
  if (EFI_ERROR (Status)) {
    return Status;
  }


  SwRegisterContext.AmdSwValue  = PcdGet8 (PcdFchOemDisableAcpiSwSmi); // use of PCD in place of FCHOEM_DISABLE_ACPI_SWSMI          0xA1
  SwRegisterContext.Order       = 0x80;
  Status = AmdSwDispatch->Register (
                            AmdSwDispatch,
                            AmdSmiAcpiOffCallback,
                            &SwRegisterContext,
                            &SwHandle
                            );
  if (EFI_ERROR (Status)) {
    return Status;
  }

#ifdef FCH_SPI_PROTECT_SMI
  SwRegisterContext.AmdSwValue  = PcdGet8 (PcdFchOemSpiUnlockSwSmi); // use of PCD in place of FCHOEM_SPI_UNLOCK_SWSMI            0xAA
  SwRegisterContext.Order       = 0x80;
  Status = AmdSwDispatch->Register (
                            AmdSwDispatch,
                            AmdSmiSpiUnlockCallback,
                            &SwRegisterContext,
                            &SwHandle
                            );
  if (EFI_ERROR (Status)) {
    return Status;
  }

  SwRegisterContext.AmdSwValue  = PcdGet8 (PcdFchOemSpiLockSwSmi); // use of PCD in place of FCHOEM_SPI_LOCK_SWSMI              0xAB
  SwRegisterContext.Order       = 0x80;
  Status = AmdSwDispatch->Register (
                            AmdSwDispatch,
                            AmdSmiSpiLockCallback,
                            &SwRegisterContext,
                            &SwHandle
                            );
  if (EFI_ERROR (Status)) {
    return Status;
  }
#endif

  if (FchMisc->LongTimer.Enable || FchMisc->ShortTimer.Enable) {
    SwRegisterContext.AmdSwValue  = PcdGet8 (PcdFchOemStartTimerSmi); // use of PCD in place of FCHOEM_START_TIMER_SMI             0xBC
    SwRegisterContext.Order       = 0x80;
    Status = AmdSwDispatch->Register (
                              AmdSwDispatch,
                              AmdStartTimerSmiCallback,
                              &SwRegisterContext,
                              &SwHandle
                              );
    if (EFI_ERROR (Status)) {
      return Status;
    }

    SwRegisterContext.AmdSwValue  = PcdGet8 (PcdFchOemStopTimerSmi); // use of PCD in place of FCHOEM_STOP_TIMER_SMI              0xBD
    SwRegisterContext.Order       = 0x80;
    Status = AmdSwDispatch->Register (
                              AmdSwDispatch,
                              AmdStopTimerSmiCallback,
                              &SwRegisterContext,
                              &SwHandle
                              );
    if (EFI_ERROR (Status)) {
      return Status;
    }
  }

  if (PcdGetBool (PcdFchOemSecureEnable) == TRUE) {
    mPcdFchLockFCHReg = TRUE;
  }
  if (PcdGetBool (PcdFchOemGpioFencing) == TRUE) {
    mPcdFchOemGpioFencing = TRUE;
  }
  SwRegisterContext.AmdSwValue  = PcdGet8 (PcdFchOemSecureSwSmi); // use of PCD in place of FCHOEM_SECURE_SMI              0xD2
  SwRegisterContext.Order       = 0x80;
  Status = AmdSwDispatch->Register (
                            AmdSwDispatch,
                            AmdFchOemSecureSmiCallback,
                            &SwRegisterContext,
                            &SwHandle
                            );
  if (EFI_ERROR (Status)) {
    return Status;
  }

#if FCH_CAPTURE_RELEASE_SPD_BUS
  SwRegisterContext.AmdSwValue  = PcdGet8 (PcdFchOemCaptureSPDBusSmi);
  SwRegisterContext.Order       = 0x80;
  Status = AmdSwDispatch->Register (
                            AmdSwDispatch,
                            AmdFchOemCaptureSPDBusSmiCallback,
                            &SwRegisterContext,
                            &SwHandle
                            );
  if (EFI_ERROR (Status)) {
    return Status;
  }
  SwRegisterContext.AmdSwValue  = PcdGet8 (PcdFchOemReleaseSPDBusSmi);
  SwRegisterContext.Order       = 0x80;
  Status = AmdSwDispatch->Register (
                            AmdSwDispatch,
                            AmdFchOemReleaseSPDBusSmiCallback,
                            &SwRegisterContext,
                            &SwHandle
                            );
  if (EFI_ERROR (Status)) {
    return Status;
  }
#endif

  return EFI_SUCCESS;
}


