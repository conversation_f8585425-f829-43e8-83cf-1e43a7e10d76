#;*****************************************************************************
#;
#; Copyright (C) 2008-2024 Advanced Micro Devices, Inc. All rights reserved.
#;
#;******************************************************************************
#For EDKII use Only
[Defines]
  INF_VERSION                    = 0x00010006
  BASE_NAME                      = CcxSmbiosZen5Lib
  FILE_GUID                      = 76F9C213-D762-4A0C-BBEE-A50A35B06623
  MODULE_TYPE                    = BASE
  VERSION_STRING                 = 1.0
  LIBRARY_CLASS                  = CcxSmbiosLib

[Sources.common]
  CcxSmbiosZen5Lib.c
  CcxSmbiosZen5CommonLib.c
  CcxSmbiosZen5CommonLib.h

[Packages]
  MdePkg/MdePkg.dec
  AgesaModulePkg/AgesaCommonModulePkg.dec
  AgesaModulePkg/AgesaModuleCcxPkg.dec
  AgesaModulePkg/AgesaModuleDfPkg.dec
  AgesaPkg/AgesaPkg.dec

[LibraryClasses]
  AmdBaseLib
  IdsLib
  CcxMpServicesLib
  BaseFabricTopologyLib
  UefiBootServicesTableLib

[Guids]

[Protocols]
  gAmdFabricTopologyServices2ProtocolGuid #CONSUMED

[Ppis]

[Pcd]

[Depex]
  gAmdFabricTopologyServices2ProtocolGuid


