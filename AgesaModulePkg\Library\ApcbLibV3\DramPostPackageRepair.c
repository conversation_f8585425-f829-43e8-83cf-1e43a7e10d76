/*****************************************************************************
 * Copyright (C) 2008-2024 Advanced Micro Devices, Inc. All rights reserved.
 *
*****************************************************************************
*/
/* $NoKeywords:$ */
/**
 * @file
 *
 * AMD Library
 *
 * Contains interface to the AMD AGESA library
 *
 * @xrefitem bom "File Content Label" "Release Content"
 * @e project:      AGESA
 * @e sub-project:  Lib
 * @e \$Revision: 309090 $   @e \$Date: 2014-12-10 02:28:05 +0800 (Wed, 10 Dec 2014) $
 *
 */

/*****************************************************************************
 *
 * This software package can be used to enable the Overclocking of certain
 * AMD processors and its use is subject to the terms and conditions of the
 * AMD Overclocking Waiver. Enabling overclocking through use of the low-level
 * routines included in this package and operating an AMD processor outside of
 * the applicable AMD product specifications will void any AMD warranty and can
 * result in damage to the processor or the system into which the processor has
 * been integrated. The user of this software assumes, and AMD disclaims, all
 * risk, liability, costs and damages relating to or arising from the overclocking
 * of AMD processors.
 *
 ******************************************************************************
 */

/*----------------------------------------------------------------------------------------
 *                             M O D U L E S    U S E D
 *----------------------------------------------------------------------------------------
 */
#include "Uefi.h"
#include <Library/AmdBaseLib.h>
#include <Library/ApcbLibV3.h>
#include <Library/AmdPspBaseLibV2.h>
#include <Library/MemoryAllocationLib.h>
#include <Library/BaseMemoryLib.h>
#include <Porting.h>
#include <Addendum/Apcb/Inc/CommonV3/ApcbDataGroups.h>
#include <Addendum/Apcb/Inc/CommonV3/ApcbMemGroup.h>
#include <Library/ApobCommonServiceLib.h>
#include <CommonV3/ApcbV3Arch.h>
#include <Filecode.h>

/*----------------------------------------------------------------------------------------
 *                   D E F I N I T I O N S    A N D    M A C R O S
 *----------------------------------------------------------------------------------------
 */
#define FILECODE        LIBRARY_APCBLIBV3_DRAMPOSTPACKAGEREPAIR_FILECODE

#define MAX_APCB_SIZE 0x100000
#define MAX_DDR_PPR_SIZE (MAX_APCB_SIZE - sizeof (APCB_V3_HEADER))

/*----------------------------------------------------------------------------------------
 *                  T Y P E D E F S     A N D     S T R U C T U R E S
 *----------------------------------------------------------------------------------------
 */


/*----------------------------------------------------------------------------------------
 *           P R O T O T Y P E S     O F     L O C A L     F U N C T I O N S
 *----------------------------------------------------------------------------------------
 */


/*----------------------------------------------------------------------------------------
 *                          E X P O R T E D    F U N C T I O N S
 *----------------------------------------------------------------------------------------
 */


/*----------------------------------------------------------------------------------------
 *                          G L O B A L        V A L U E S
 *----------------------------------------------------------------------------------------
 */

extern BOOLEAN                  mAtRuntime;

/*---------------------------------------------------------------------------------------*/
/**
 * @brief This function retrieves the DRAM POST Package repair entries
 *
 * @param[in]         pApcbDppRepairEntries         - Buffer of the DRAM POST Package repair entries
 * @param[in]         SizeInByte                    - The size of the array of the DRAM POST Package repair entries
 *
 * @retval            EFI_SUCCESS            - DRAM POST Package repair entries are successfully retrieved
 *                    EFI_UNSUPPORTED        - DRAM POST Package repair entries cannot be retrieved at this stage
 *                    EFI_NOT_FOUND          - DRAM POST Package repair entries cannot be found
 */
EFI_STATUS
ApcbGetDramPostPkgRepairEntries (
  IN OUT   APCB_DPPRCL_REPAIR_ENTRY_V2 **EntryArray,
  IN OUT   UINT32                   *SizeInByte
  )
{
  BOOLEAN                   ApcbRecoveryFlag;


  //Exit service, if recovery flag set
  if (mAtRuntime == FALSE) {
    ApobGetApcbRecoveryFlag (&ApcbRecoveryFlag);
    if ((ApcbRecoveryFlag == TRUE) &&
        (PcdGet8 (PcdApcbRecoveryStrategy) == 0x0)) {
      IDS_HDT_CONSOLE_PSP_TRACE ("[APCB Lib V3] APCB.RecoveryFlag Set, exit service\n");
      return EFI_UNSUPPORTED;
    }
  }

  //Exit service, if recovery flag set
  if (CheckPspRecoveryFlagV2 () == TRUE) {
    IDS_HDT_CONSOLE_PSP_TRACE ("[APCB Lib V3] Recovery flag set, exit service\n");
    // ASSERT (FALSE); // Assertion in the debug build
    return EFI_UNSUPPORTED;
  }

  if (!ApcbGetType (APCB_GROUP_MEMORY, APCB_MEM_TYPE_DDR_POST_PACKAGE_REPAIR, 0, (UINT8 **) EntryArray, SizeInByte)) {
    IDS_HDT_CONSOLE_PSP_TRACE ("[APCB Lib V3] Failed to locate DDR5 Post Package Repair Entries\n");
    return EFI_NOT_FOUND;
  }

  return EFI_SUCCESS;
}

/*---------------------------------------------------------------------------------------*/
/**
 * @brief This function clears the DRAM POST Package repair entries
 *
 * @retval            EFI_SUCCESS            - DRAM POST Package repair entries are successfully cleared
 *                    EFI_UNSUPPORTED        - DRAM POST Package repair entries cannot be cleared at this stage
 *                    EFI_NOT_FOUND          - DRAM POST Package repair entries cannot be found
 */
EFI_STATUS
ApcbClearDramPostPkgRepairEntry (
  VOID
  )
{
  BOOLEAN                   ApcbRecoveryFlag;


  //Exit service, if recovery flag set
  if (mAtRuntime == FALSE) {
    ApobGetApcbRecoveryFlag (&ApcbRecoveryFlag);
    if ((ApcbRecoveryFlag == TRUE) &&
        (PcdGet8 (PcdApcbRecoveryStrategy) == 0x0)) {
      IDS_HDT_CONSOLE_PSP_TRACE ("[APCB Lib V3] APCB.RecoveryFlag Set, exit service\n");
      return EFI_UNSUPPORTED;
    }
  }

  //Exit service, if recovery flag set
  if (CheckPspRecoveryFlagV2 () == TRUE) {
    IDS_HDT_CONSOLE_PSP_TRACE ("[APCB Lib V3] Recovery flag set, exit service\n");
    // ASSERT (FALSE); // Assertion in the debug build
    return EFI_UNSUPPORTED;
  }

  if (ApcbSetType (APCB_GROUP_MEMORY, APCB_MEM_TYPE_DDR_POST_PACKAGE_REPAIR, 0, NULL, 0)) {
    return EFI_SUCCESS;
  }

  IDS_HDT_CONSOLE_PSP_TRACE ("[APCB Lib V3] Failed to locate DDR5 Post Package Repair Entries\n");

  return EFI_NOT_FOUND;
}

/*---------------------------------------------------------------------------------------*/
/*
 * @brief This function copies a DRAM POST Package repair entry, and
 * the entry data struct is APCB_DPPRCL_REPAIR_ENTRY_V2
 *
 * @paran[in]   *DestEntry             - The pointer of destination buffer
 * @paran[in]   *SrcEntry              - The pointer of source buffer
 */
VOID
CopyDramPostPkgRepairEntry (
  IN       APCB_DPPRCL_REPAIR_ENTRY_V2 *DestEntry,
  IN       APCB_DPPRCL_REPAIR_ENTRY_V2 *SrcEntry
  )
{
  ASSERT (DestEntry != NULL);
  ASSERT (SrcEntry != NULL);

  DestEntry->ddr.Valid          = SrcEntry->ddr.Valid;
  DestEntry->ddr.Bank           = SrcEntry->ddr.Bank;
  DestEntry->ddr.RankMultiplier = SrcEntry->ddr.RankMultiplier;
  DestEntry->ddr.Device         = SrcEntry->ddr.Device;
  DestEntry->ddr.ChipSelect     = SrcEntry->ddr.ChipSelect;
  DestEntry->ddr.Column         = SrcEntry->ddr.Column;
  DestEntry->ddr.RepairType     = SrcEntry->ddr.RepairType;
  DestEntry->ddr.Row            = SrcEntry->ddr.Row;
  DestEntry->ddr.Socket         = SrcEntry->ddr.Socket;
  DestEntry->ddr.Channel        = SrcEntry->ddr.Channel;
  DestEntry->ddr.TargetDevice   = SrcEntry->ddr.TargetDevice;

  DestEntry->ddr.SubChannel         = SrcEntry->ddr.SubChannel;
  DestEntry->ddr.HardPPRDone        = SrcEntry->ddr.HardPPRDone;
  DestEntry->ddr.PPRUndo            = SrcEntry->ddr.PPRUndo;
  DestEntry->ddr.PPRLock            = SrcEntry->ddr.PPRLock;
  DestEntry->ddr.DeviceTypeToRepair = SrcEntry->ddr.DeviceTypeToRepair;
  DestEntry->ddr.SerialNumber       = SrcEntry->ddr.SerialNumber;
  DestEntry->ddr.ErrorCause         = SrcEntry->ddr.ErrorCause;

}

/*---------------------------------------------------------------------------------------*/
/*
 * @brief This function copies a DRAM POST Package repair entry, and
 * the entry data struct is APCB_DPPRCL_REPAIR_ENTRY_V3
 *
 * @paran[in]   *DestEntry             - The pointer of destination buffer
 * @paran[in]   *SrcEntry              - The pointer of source buffer
 */
VOID
CopyDramPostPkgRepairEntryV3 (
  IN  APCB_DPPRCL_REPAIR_ENTRY_V3 *DestEntry,
  IN  APCB_DPPRCL_REPAIR_ENTRY_V3 *SrcEntry
  )
{

  ASSERT (DestEntry != NULL);
  ASSERT (SrcEntry != NULL);

  DestEntry->ddr.DeviceTypeToRepair = SrcEntry->ddr.DeviceTypeToRepair;
  DestEntry->ddr.Bank               = SrcEntry->ddr.Bank;
  DestEntry->ddr.Device             = SrcEntry->ddr.Device;
  DestEntry->ddr.ChipSelect         = SrcEntry->ddr.ChipSelect;
  DestEntry->ddr.Column             = SrcEntry->ddr.Column;
  DestEntry->ddr.TargetDevice       = SrcEntry->ddr.TargetDevice;
  DestEntry->ddr.Valid              = SrcEntry->ddr.Valid;

  DestEntry->ddr.Row                = SrcEntry->ddr.Row;
  DestEntry->ddr.RankMultiplier     = SrcEntry->ddr.RankMultiplier;
  DestEntry->ddr.Channel            = SrcEntry->ddr.Channel;
  DestEntry->ddr.SubChannel         = SrcEntry->ddr.SubChannel;
  DestEntry->ddr.HardPPRDone        = SrcEntry->ddr.HardPPRDone;
  DestEntry->ddr.PPRUndo            = SrcEntry->ddr.PPRUndo;
  DestEntry->ddr.PPRLock            = SrcEntry->ddr.PPRLock;
  DestEntry->ddr.Socket             = SrcEntry->ddr.Socket;

  DestEntry->ddr.RepairType         = SrcEntry->ddr.RepairType;
  DestEntry->ddr.ErrorCause         = SrcEntry->ddr.ErrorCause;
  DestEntry->ddr.RepairResult       = SrcEntry->ddr.RepairResult;

  DestEntry->ddr.extfield.bt.SerialNumberLoWord  = SrcEntry->ddr.extfield.bt.SerialNumberLoWord;
  DestEntry->ddr.extfield.bt.SerialNumberHiWord  = SrcEntry->ddr.extfield.bt.SerialNumberHiWord;

}

/*---------------------------------------------------------------------------------------*/
/**
 *      This function compares two DRAM POST Package repair entry
 *
 * @retval            TRUE            - Two DRAM POST Package repair entries are matched
 *                    FALSE           - Two DRAM POST Package repair entries are not matched
 *
 */
BOOLEAN
CompareDramPostPkgRepairEntry (
  IN       APCB_DPPRCL_REPAIR_ENTRY_V2 *DestEntry,
  IN       APCB_DPPRCL_REPAIR_ENTRY_V2 *SrcEntry
  )
{
  ASSERT (DestEntry != NULL);
  ASSERT (SrcEntry != NULL);

  if (DestEntry->ddr.Valid          == SrcEntry->ddr.Valid &&
      (DestEntry->ddr.Bank & 0x1C)  == (SrcEntry->ddr.Bank & 0x1C) &&
      DestEntry->ddr.RankMultiplier == SrcEntry->ddr.RankMultiplier &&
      DestEntry->ddr.Device         == SrcEntry->ddr.Device &&
      DestEntry->ddr.ChipSelect     == SrcEntry->ddr.ChipSelect &&
      DestEntry->ddr.Column         == SrcEntry->ddr.Column &&
      DestEntry->ddr.RepairType     == SrcEntry->ddr.RepairType &&
      DestEntry->ddr.Row            == SrcEntry->ddr.Row &&
      DestEntry->ddr.Socket         == SrcEntry->ddr.Socket &&
      DestEntry->ddr.Channel        == SrcEntry->ddr.Channel &&
      DestEntry->ddr.TargetDevice   == SrcEntry->ddr.TargetDevice &&

      DestEntry->ddr.SubChannel     == SrcEntry->ddr.SubChannel &&
      DestEntry->ddr.HardPPRDone    == SrcEntry->ddr.HardPPRDone &&
      DestEntry->ddr.PPRUndo        == SrcEntry->ddr.PPRUndo &&
      DestEntry->ddr.PPRLock        == SrcEntry->ddr.PPRLock &&
      DestEntry->ddr.SerialNumber   == SrcEntry->ddr.SerialNumber &&
      DestEntry->ddr.DeviceTypeToRepair == SrcEntry->ddr.DeviceTypeToRepair &&
      DestEntry->ddr.ErrorCause     == SrcEntry->ddr.ErrorCause
  ) {
    return TRUE;
  } else {
    return FALSE;
  }
}

/*---------------------------------------------------------------------------------------*/
/**
 * @brief This function compares two DRAM POST Package repair entry
 *
 * @paran[in]         *DestEntry             - The pointer of destination buffer
 * @paran[in]         *SrcEntry              - The pointer of source buffer
 *
 * @retval            TRUE            - Two DRAM POST Package repair entries are matched
 *                    FALSE           - Two DRAM POST Package repair entries are not matched
 *
 */
BOOLEAN
CompareDramPostPkgRepairEntryV3 (
  IN  APCB_DPPRCL_REPAIR_ENTRY_V3   *DestEntry,
  IN  APCB_DPPRCL_REPAIR_ENTRY_V3   *SrcEntry
  )
{

  ASSERT (DestEntry != NULL);
  ASSERT (SrcEntry != NULL);

  if (DestEntry->ddr.DeviceTypeToRepair == SrcEntry->ddr.DeviceTypeToRepair &&
      (DestEntry->ddr.Bank & 0x1C)      == (SrcEntry->ddr.Bank & 0x1C) &&
      DestEntry->ddr.Device             == SrcEntry->ddr.Device &&
      DestEntry->ddr.ChipSelect         == SrcEntry->ddr.ChipSelect &&
      DestEntry->ddr.Column             == SrcEntry->ddr.Column &&
      DestEntry->ddr.TargetDevice       == SrcEntry->ddr.TargetDevice &&
      DestEntry->ddr.Valid              == SrcEntry->ddr.Valid &&

      DestEntry->ddr.Row                == SrcEntry->ddr.Row &&
      DestEntry->ddr.RankMultiplier     == SrcEntry->ddr.RankMultiplier &&
      DestEntry->ddr.Channel            == SrcEntry->ddr.Channel &&
      DestEntry->ddr.SubChannel         == SrcEntry->ddr.SubChannel &&
      DestEntry->ddr.HardPPRDone        == SrcEntry->ddr.HardPPRDone &&
      DestEntry->ddr.PPRUndo            == SrcEntry->ddr.PPRUndo &&
      DestEntry->ddr.PPRLock            == SrcEntry->ddr.PPRLock &&
      DestEntry->ddr.Socket             == SrcEntry->ddr.Socket &&

      DestEntry->ddr.RepairType         == SrcEntry->ddr.RepairType &&
      DestEntry->ddr.ErrorCause         == SrcEntry->ddr.ErrorCause &&
      DestEntry->ddr.RepairResult       == SrcEntry->ddr.RepairResult &&

      DestEntry->ddr.extfield.bt.SerialNumberLoWord  == SrcEntry->ddr.extfield.bt.SerialNumberLoWord &&
      DestEntry->ddr.extfield.bt.SerialNumberHiWord  == SrcEntry->ddr.extfield.bt.SerialNumberHiWord) {
    return TRUE;
  } else {
    return FALSE;
  }

}

/*---------------------------------------------------------------------------------------*/
/**
 * @brief This function adds a DRAM POST Package repair entry
 *
 * @param[in]         Entry                  - The DRAM POST Package repair entry to be added
 *
 * @retval            EFI_SUCCESS            - DRAM POST Package repair entries are successfully cleared
 *                    EFI_UNSUPPORTED        - DRAM POST Package repair entries cannot be cleared at this stage
 *                    EFI_NOT_FOUND          - The type of DRAM POST Package repair entries cannot be found
 */
EFI_STATUS
ApcbAddDramPostPkgRepairEntry (
  IN       APCB_DPPRCL_REPAIR_ENTRY_V2 *Entry
  )
{
  EFI_STATUS                Status;
  BOOLEAN                   ApcbRecoveryFlag;
  APCB_DPPRCL_REPAIR_ENTRY_V2  *EntryArray;
  APCB_DPPRCL_REPAIR_ENTRY_V2  *NewEntryArray;
  UINT32                    SizeInByte;
  UINT16                    i;
  BOOLEAN                   EmptyEntryFound;
  UINT16                    NewEntryId;

  Status                  = EFI_SUCCESS;
  EmptyEntryFound         = FALSE;
  NewEntryArray           = NULL;

  //Exit service, if recovery flag set
  if (mAtRuntime == FALSE) {
    ApobGetApcbRecoveryFlag (&ApcbRecoveryFlag);
    if ((ApcbRecoveryFlag == TRUE) &&
        (PcdGet8 (PcdApcbRecoveryStrategy) == 0x0)) {
      IDS_HDT_CONSOLE_PSP_TRACE ("[APCB Lib V3] APCB.RecoveryFlag Set, exit service\n");
      return EFI_UNSUPPORTED;
    }
  }

  //Exit service, if recovery flag set
  if (CheckPspRecoveryFlagV2 () == TRUE) {
    IDS_HDT_CONSOLE_PSP_TRACE ("[APCB Lib V3] Recovery flag set, exit service\n");
    // ASSERT (FALSE); // Assertion in the debug build
    return EFI_UNSUPPORTED;
  }

  if (!ApcbGetType (APCB_GROUP_MEMORY, APCB_MEM_TYPE_DDR_POST_PACKAGE_REPAIR, 0, (UINT8 **) &EntryArray, &SizeInByte)) {
    IDS_HDT_CONSOLE_PSP_TRACE ("[APCB Lib V3] Failed to locate DDR5 Post Package Repair Entries\n");
    return EFI_NOT_FOUND;
  }

  if (((SizeInByte % sizeof (APCB_DPPRCL_REPAIR_ENTRY_V2)) != 0) || (SizeInByte > MAX_DDR_PPR_SIZE)) {
    IDS_HDT_CONSOLE_PSP_TRACE ("[APCB Lib V3] DDR Post Package Repair Entry bad size 0x%x\n", SizeInByte);
    return EFI_NOT_FOUND;
  }

  // Check if the target entry already exists
  for (i = 0; i < SizeInByte / sizeof (APCB_DPPRCL_REPAIR_ENTRY_V2); i ++) {
    if (CompareDramPostPkgRepairEntry (&EntryArray[i], Entry)) {
      IDS_HDT_CONSOLE_PSP_TRACE ("[APCB Lib V3] Duplicate DDR5 Post Package Repair Entry found\n");
      return EFI_SUCCESS;
    }
  }

  // Try adding the entry to the existing space first
  for (i = 0; i < SizeInByte / sizeof (APCB_DPPRCL_REPAIR_ENTRY_V2); i ++) {
    if (0 == EntryArray[i].ddr.Valid) {
      EmptyEntryFound = TRUE;
      break;
    }
  }

  if (!EmptyEntryFound && ((SizeInByte + sizeof (APCB_DPPRCL_REPAIR_ENTRY_V2)) > MAX_DDR_PPR_SIZE)) {
    IDS_HDT_CONSOLE_PSP_TRACE ("[APCB Lib V3] DDR Post Package Repair Entry no space\n");
    return EFI_BUFFER_TOO_SMALL;
  }

  NewEntryArray = AllocateZeroPool (SizeInByte + (EmptyEntryFound ? 0 : sizeof (APCB_DPPRCL_REPAIR_ENTRY_V2)));
  ASSERT (NewEntryArray != NULL);
  if (NewEntryArray == NULL) {
    IDS_HDT_CONSOLE_PSP_TRACE ("[APCB Lib V3] Failed to allocate buffer for a new DDR Post Package Repair Entry\n");
    return EFI_OUT_OF_RESOURCES;
  }
  CopyMem (NewEntryArray, EntryArray, SizeInByte);

  if (EmptyEntryFound) {
    CopyDramPostPkgRepairEntry (&NewEntryArray[i], Entry);
  } else {
    // Not enough space. Try increasing the size of the type data
    NewEntryId = (UINT16) SizeInByte / sizeof (APCB_DPPRCL_REPAIR_ENTRY_V2);
    if (NewEntryId >= PcdGet32 (PcdAmdMemCfgMaxPostPackageRepairEntries)) {
      IDS_HDT_CONSOLE_PSP_TRACE ("[APCB Lib V3] Too many Post Package Repair Entries requested\n");
      FreePool (NewEntryArray);
      return EFI_OUT_OF_RESOURCES;
    }
    CopyDramPostPkgRepairEntry (&NewEntryArray[NewEntryId], Entry);
  }

  if (!ApcbSetType (
        APCB_GROUP_MEMORY,
        APCB_MEM_TYPE_DDR_POST_PACKAGE_REPAIR,
        0,
        (UINT8 *)NewEntryArray,
        SizeInByte + (EmptyEntryFound ? 0 : sizeof (APCB_DPPRCL_REPAIR_ENTRY_V2)))) {
    IDS_HDT_CONSOLE_PSP_TRACE ("[APCB Lib V3] Failed to find the type or not enough APCB space for Post Package Repair Entries\n");
    Status = EFI_NOT_FOUND;
  }
  FreePool (NewEntryArray);

  return Status;
}

/*---------------------------------------------------------------------------------------*/
/**
 * @brief This function adds a DRAM POST Package repair entry, and
 *  the entry struct type is APCB_DPPRCL_REPAIR_ENTRY_V3
 *
 * @param[in]         Entry                  - The DRAM POST Package repair entry to be added
 *
 * @retval            EFI_SUCCESS            - DRAM POST Package repair entries are successfully cleared
 *                    EFI_UNSUPPORTED        - DRAM POST Package repair entries cannot be cleared at this stage
 *                    EFI_NOT_FOUND          - The type of DRAM POST Package repair entries cannot be found
 */
EFI_STATUS
ApcbAddDramPostPkgRepairEntryV3 (
  IN       APCB_DPPRCL_REPAIR_ENTRY_V3 *Entry
  )
{
  EFI_STATUS                    Status;
  BOOLEAN                       ApcbRecoveryFlag;
  UINT32                        SizeInByte;
  UINT16                        i;
  BOOLEAN                       EmptyEntryFound;
  UINT16                        EntryIndex;
  UINT32                        RepairEntrySize;
  APCB_DPPRCL_REPAIR_ENTRY_V3   *EntryArray;
  APCB_DPPRCL_REPAIR_ENTRY_V3   *NewEntryArray;

  Status          = EFI_SUCCESS;
  EmptyEntryFound = FALSE;
  RepairEntrySize = sizeof (APCB_DPPRCL_REPAIR_ENTRY_V3);
  EntryArray      = NULL;
  NewEntryArray   = NULL;

  //Exit service, if recovery flag set
  if (mAtRuntime == FALSE) {
    ApobGetApcbRecoveryFlag (&ApcbRecoveryFlag);
    if ((ApcbRecoveryFlag == TRUE) &&
        (PcdGet8 (PcdApcbRecoveryStrategy) == 0x0)) {
      IDS_HDT_CONSOLE_PSP_TRACE ("[APCB Lib V3] APCB.RecoveryFlag Set, exit service\n");
      return EFI_UNSUPPORTED;
    }
  }

  //Exit service, if recovery flag set
  if (CheckPspRecoveryFlagV2 () == TRUE) {
    IDS_HDT_CONSOLE_PSP_TRACE ("[APCB Lib V3] Recovery flag set, exit service\n");
    // ASSERT (FALSE); // Assertion in the debug build
    return EFI_UNSUPPORTED;
  }

  if (!ApcbGetType (APCB_GROUP_MEMORY, APCB_MEM_TYPE_DDR_POST_PACKAGE_REPAIR, 0, (UINT8 **) &EntryArray, &SizeInByte)) {
    IDS_HDT_CONSOLE_PSP_TRACE ("[APCB Lib V3] Failed to locate DDR Post Package Repair Entries\n");
    return EFI_NOT_FOUND;
  }

  if (EntryArray == NULL) {
    return EFI_UNSUPPORTED;
  }

  if (((SizeInByte % RepairEntrySize) != 0) || (SizeInByte > MAX_DDR_PPR_SIZE)) {
    IDS_HDT_CONSOLE_PSP_TRACE ("[APCB Lib V3] DDR Post Package Repair Entry bad size 0x%x\n", SizeInByte);
    return EFI_NOT_FOUND;
  }

  // Check if the target entry already exists
  for (i = 0; i < (SizeInByte / RepairEntrySize); i++) {
    if (CompareDramPostPkgRepairEntryV3 (&EntryArray[i], Entry)) {
      IDS_HDT_CONSOLE_PSP_TRACE ("[APCB Lib V3] Duplicate DDR Post Package Repair Entry found\n");
      return EFI_SUCCESS;
    }
  }

  // Try adding the entry to the existing space first
  for (i = 0; i < (SizeInByte / RepairEntrySize); i++) {
    if (0 == EntryArray[i].ddr.Valid) {
      EmptyEntryFound = TRUE;
      break;
    }
  }

  if (!EmptyEntryFound && ((SizeInByte + RepairEntrySize) > MAX_DDR_PPR_SIZE)) {
    IDS_HDT_CONSOLE_PSP_TRACE ("[APCB Lib V3] DDR Post Package Repair Entry no space\n");
    return EFI_BUFFER_TOO_SMALL;
  }

  NewEntryArray = AllocateZeroPool (SizeInByte + (EmptyEntryFound ? 0 : RepairEntrySize));
  ASSERT (NewEntryArray != NULL);
  if (NewEntryArray == NULL) {
    IDS_HDT_CONSOLE_PSP_TRACE ("[APCB Lib V3] Failed to allocate buffer for a new DDR Post Package Repair Entry\n");
    return EFI_OUT_OF_RESOURCES;
  }
  CopyMem (NewEntryArray, EntryArray, SizeInByte);

  if (EmptyEntryFound) {
    EntryIndex = i;
  } else {
    EntryIndex = (UINT16) (SizeInByte / RepairEntrySize);

    if (EntryIndex >= PcdGet32 (PcdAmdMemCfgMaxPostPackageRepairEntries)) {
      IDS_HDT_CONSOLE_PSP_TRACE ("[APCB Lib V3] Too many Post Package Repair Entries requested\n");
      FreePool (NewEntryArray);
      NewEntryArray = NULL;
      return EFI_OUT_OF_RESOURCES;
    }
  }

  CopyDramPostPkgRepairEntryV3 (&NewEntryArray[EntryIndex], Entry);

  if (!ApcbSetType (
        APCB_GROUP_MEMORY,
        APCB_MEM_TYPE_DDR_POST_PACKAGE_REPAIR,
        0,
        (UINT8 *) NewEntryArray,
        SizeInByte + (EmptyEntryFound ? 0 : RepairEntrySize))) {
    IDS_HDT_CONSOLE_PSP_TRACE ("[APCB Lib V3] Failed to find the type or not enough APCB space for Post Package Repair Entries\n");
    Status = EFI_NOT_FOUND;
  }

  if (NewEntryArray != NULL) {
    FreePool (NewEntryArray);
    NewEntryArray = NULL;
  }

  return Status;
}

/*---------------------------------------------------------------------------------------*/
/**
 * @brief This function removes a DRAM POST Package repair entry
 *
 * @param[in]         Entry                  - The DRAM POST Package repair entry to be added
 *
 * @retval            EFI_SUCCESS            - DRAM POST Package repair entry is successfully removed
 *                    EFI_UNSUPPORTED        - DRAM POST Package repair entry cannot be removed at this stage
 *                    EFI_NOT_FOUND          - DRAM POST Package repair entry cannot be found
 */
EFI_STATUS
ApcbRemoveDramPostPkgRepairEntry (
  IN       APCB_DPPRCL_REPAIR_ENTRY_V2 *Entry
  )
{
  EFI_STATUS                Status;
  BOOLEAN                   ApcbRecoveryFlag;
  APCB_DPPRCL_REPAIR_ENTRY_V2  *EntryArray;
  APCB_DPPRCL_REPAIR_ENTRY_V2  *NewEntryArray;
  UINT32                    SizeInByte;
  UINT16                    i;
  BOOLEAN                   TargetEntryFound;

  Status                  = EFI_SUCCESS;
  NewEntryArray           = NULL;

  //Exit service, if recovery flag set
  if (mAtRuntime == FALSE) {
    ApobGetApcbRecoveryFlag (&ApcbRecoveryFlag);
    if ((ApcbRecoveryFlag == TRUE) &&
        (PcdGet8 (PcdApcbRecoveryStrategy) == 0x0)) {
      IDS_HDT_CONSOLE_PSP_TRACE ("[APCB Lib V3] APCB.RecoveryFlag Set, exit service\n");
      return EFI_UNSUPPORTED;
    }
  }

  //Exit service, if recovery flag set
  if (CheckPspRecoveryFlagV2 () == TRUE) {
    IDS_HDT_CONSOLE_PSP_TRACE ("[APCB Lib V3] Recovery flag set, exit service\n");
    // ASSERT (FALSE); // Assertion in the debug build
    return EFI_UNSUPPORTED;
  }

  if (!ApcbGetType (APCB_GROUP_MEMORY, APCB_MEM_TYPE_DDR_POST_PACKAGE_REPAIR, 0, (UINT8 **)&EntryArray, &SizeInByte)) {
    IDS_HDT_CONSOLE_PSP_TRACE ("[APCB Lib V3] Failed to locate DDR5 Post Package Repair Entries\n");
    return EFI_NOT_FOUND;
  }

  if (SizeInByte < sizeof (APCB_DPPRCL_REPAIR_ENTRY_V2) || SizeInByte > MAX_DDR_PPR_SIZE) {
    IDS_HDT_CONSOLE_PSP_TRACE ("[APCB Lib V3] DDR Post Package Repair Entry bad size 0x%x\n", SizeInByte);
    return EFI_NOT_FOUND;
  }

  NewEntryArray = AllocateZeroPool (SizeInByte);
  ASSERT (NewEntryArray != NULL);
  if (NewEntryArray == NULL) {
    IDS_HDT_CONSOLE_PSP_TRACE ("[APCB Lib V3] Failed to allocate buffer for a new DDR5 Post Package Repair Entry\n");
    return EFI_OUT_OF_RESOURCES;
  }
  CopyMem (NewEntryArray, EntryArray, SizeInByte);

  TargetEntryFound = FALSE;
  for (i = 0; i < SizeInByte / sizeof (APCB_DPPRCL_REPAIR_ENTRY_V2); i ++) {
    if (CompareDramPostPkgRepairEntry (&NewEntryArray[i], Entry)) {
      TargetEntryFound                        = TRUE;
      NewEntryArray[i].ddr.Valid              = 0;
    }
  }

  if (TargetEntryFound) {
    if (!ApcbSetType (APCB_GROUP_MEMORY, APCB_MEM_TYPE_DDR_POST_PACKAGE_REPAIR, 0, (UINT8 *)NewEntryArray, SizeInByte)) {
      IDS_HDT_CONSOLE_PSP_TRACE ("[APCB Lib V3] Failed to find the type for Post Package Repair Entries\n");
      Status = EFI_NOT_FOUND;
    }
  } else {
    IDS_HDT_CONSOLE_PSP_TRACE ("[APCB Lib V3] Failed to find the DDR5 Post Package Repair Entry\n");
    Status = EFI_NOT_FOUND;
  }
  FreePool (NewEntryArray);

  return Status;
}

/*---------------------------------------------------------------------------------------*/
/**
 * @brief This function removes a DRAM POST Package repair entry, and
 * the entry struct type is APCB_DPPRCL_REPAIR_ENTRY_V3
 *
 * @param[in]         Entry                  - The DRAM POST Package repair entry to be added
 *
 * @retval            EFI_SUCCESS            - DRAM POST Package repair entry is successfully removed
 *                    EFI_UNSUPPORTED        - DRAM POST Package repair entry cannot be removed at this stage
 *                    EFI_NOT_FOUND          - DRAM POST Package repair entry cannot be found
 */
EFI_STATUS
ApcbRemoveDramPostPkgRepairEntryV3 (
  IN       APCB_DPPRCL_REPAIR_ENTRY_V3 *Entry
  )
{
  EFI_STATUS                    Status;
  BOOLEAN                       ApcbRecoveryFlag;
  UINT32                        SizeInByte;
  UINT16                        i;
  BOOLEAN                       TargetEntryFound;
  UINT32                        RepairEntrySize;
  APCB_DPPRCL_REPAIR_ENTRY_V3   *EntryArray;
  APCB_DPPRCL_REPAIR_ENTRY_V3   *NewEntryArray;

  Status            = EFI_SUCCESS;
  TargetEntryFound  = FALSE;
  RepairEntrySize   = sizeof (APCB_DPPRCL_REPAIR_ENTRY_V3);
  EntryArray        = NULL;
  NewEntryArray     = NULL;

  //Exit service, if recovery flag set
  if (mAtRuntime == FALSE) {
    ApobGetApcbRecoveryFlag (&ApcbRecoveryFlag);
    if ((ApcbRecoveryFlag == TRUE) &&
        (PcdGet8 (PcdApcbRecoveryStrategy) == 0x0)) {
      IDS_HDT_CONSOLE_PSP_TRACE ("[APCB Lib V3] APCB.RecoveryFlag Set, exit service\n");
      return EFI_UNSUPPORTED;
    }
  }

  //Exit service, if recovery flag set
  if (CheckPspRecoveryFlagV2 () == TRUE) {
    IDS_HDT_CONSOLE_PSP_TRACE ("[APCB Lib V3] Recovery flag set, exit service\n");
    // ASSERT (FALSE); // Assertion in the debug build
    return EFI_UNSUPPORTED;
  }

  if (!ApcbGetType (APCB_GROUP_MEMORY, APCB_MEM_TYPE_DDR_POST_PACKAGE_REPAIR, 0, (UINT8 **)&EntryArray, &SizeInByte)) {
    IDS_HDT_CONSOLE_PSP_TRACE ("[APCB Lib V3] Failed to locate DDR Post Package Repair Entries\n");
    return EFI_NOT_FOUND;
  }

  if (EntryArray == NULL) {
    return EFI_UNSUPPORTED;
  }

  if (SizeInByte < RepairEntrySize || SizeInByte > MAX_DDR_PPR_SIZE) {
    IDS_HDT_CONSOLE_PSP_TRACE ("[APCB Lib V3] DDR Post Package Repair Entry bad size 0x%x\n", SizeInByte);
    return EFI_NOT_FOUND;
  }

  NewEntryArray = AllocateZeroPool (SizeInByte);
  ASSERT (NewEntryArray != NULL);
  if (NewEntryArray == NULL) {
    IDS_HDT_CONSOLE_PSP_TRACE ("[APCB Lib V3] Failed to allocate buffer for a new DDR5 Post Package Repair Entry\n");
    return EFI_OUT_OF_RESOURCES;
  }
  CopyMem (NewEntryArray, EntryArray, SizeInByte);

  TargetEntryFound = FALSE;
  for (i = 0; i < SizeInByte / RepairEntrySize; i ++) {
    if (CompareDramPostPkgRepairEntryV3 (&NewEntryArray[i], Entry)) {
      TargetEntryFound          = TRUE;
      NewEntryArray[i].ddr.Valid = 0;
    }
  }

  if (TargetEntryFound) {
    if (!ApcbSetType (APCB_GROUP_MEMORY, APCB_MEM_TYPE_DDR_POST_PACKAGE_REPAIR, 0, (UINT8 *) NewEntryArray, SizeInByte)) {
      IDS_HDT_CONSOLE_PSP_TRACE ("[APCB Lib V3] Failed to find the type for Post Package Repair Entries\n");
      Status = EFI_NOT_FOUND;
    }
  } else {
    IDS_HDT_CONSOLE_PSP_TRACE ("[APCB Lib V3] Failed to find the DDR Post Package Repair Entry\n");
    Status = EFI_NOT_FOUND;
  }

  if (NewEntryArray != NULL) {
    FreePool (NewEntryArray);
    NewEntryArray = NULL;
  }

  return Status;
}

