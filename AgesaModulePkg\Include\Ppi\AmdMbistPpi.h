/*****************************************************************************
 *
 * Copyright (C) 2017-2024 Advanced Micro Devices, Inc. All rights reserved.
 *
 *******************************************************************************
 **/
#ifndef _AMD_MBIST_PPI_H_
#define _AMD_MBIST_PPI_H_


//----------------------------------------------------
//
//  AMD MBIST PEI Initialization Complete PPI
//
//-------------------------------------------------------

#ifdef <PERSON>_<PERSON>_PER_DIE
  #undef MAX_CHANNELS_PER_D<PERSON>
  #define MAX_CHANNELS_PER_DIE    FixedPcdGet8(PcdAmdMemMaxChannelPerDieV2)
#endif

/**
 * @brief MBIST Test Status
 */
typedef struct _MBIST_TEST_STATUS {
  UINT8                  ResultValid[MAX_MBIST_SUBTEST][MAX_CHANNELS_PER_DIE]; ///< Error status valid per chip select
  UINT8                  ErrorStatus[MAX_MBIST_SUBTEST][MAX_CHANNELS_PER_DIE]; ///< Error status per chip select
} MBIST_TEST_STATUS;

/**
 * @brief MBIST Margin
 */
typedef struct _MBIST_MARGIN {
  UINT8  PositiveEdge; ///< Positive edge margin
  UINT8  NegativeEdge; ///< Negative edge margin
} MBIST_MARGIN;

/**
 * @brief MBIST Data Eye Margin
 */
typedef struct _MBIST_DATA_EYE_MARGIN {
  MBIST_MARGIN  ReadDqDelay;    ///< Smallest Positive and Negative Read Dq Delay margin
  MBIST_MARGIN  ReadVref;       ///< Smallest Positive and Negative Read Vref delay
  MBIST_MARGIN  WriteDqDelay;   ///< Smallest Positive and Negative Write Dq Delay margin
  MBIST_MARGIN  WriteVref;      ///< Smallest Positive and Negative Write Vref delay
} MBIST_DATA_EYE_MARGIN;

/**
 * @brief MBIST Data Eye Margin Record
 */
typedef struct _MBIST_DATA_EYE_MARGIN_RECORD {
  BOOLEAN                IsDataEyeValid;    ///< Is Data Eye Record Valid
  MBIST_DATA_EYE_MARGIN  DataEyeMargin;     ///< DataEyeRecord
} MBIST_DATA_EYE_MARGIN_RECORD;

/**
 * @brief MBIST Data Eye
 */
typedef struct _MBIST_DATA_EYE {
  MBIST_DATA_EYE_MARGIN_RECORD  MbistDataEyeRecord[MAX_CHANNELS_PER_DIE][MAX_CHIPSELECT_PER_CHANNEL]; ///< Data eye margins for all chipselects per channel per die per socket
} MBIST_DATA_EYE;

//
// PPI function Prototype
//
/**
 * @brief Extract DataEye  Margin results for given MBIST_DATA_EYE for all the supported
 * socket and Die. Based off the field which determines, if the data is valid
 * it can be utilized accordingly.
 * @param[in] PeiServices       - PeiServices
 * @param[in] BufferSize        - Total Buffer Size of the data
 * @param[in] MbistTestResults  - Pointer to MBIST_TEST_STATUS structure
 * @return EFI_STATUS - Status Code
*/
typedef EFI_STATUS (EFIAPI * PEI_GET_MBIST_TEST_RESULTS) (
  IN CONST EFI_PEI_SERVICES  **PeiServices,
  IN UINT32 *BufferSize,
  IN OUT MBIST_TEST_STATUS  *MbistTestResults
);

/**
 * @brief Extract DataEye Margin results for given MBIST_DATA_EYE for all the supported
 * socket and Die. Based off the field which determines, if the data is valid
 * it can be utilized accordingly.
 * @param[in] PeiServices   - PeiServices
 * @param[in] BufferSize    - Total Buffer Size of the data
 * @param[in] MbistDataEye  - Pointer to MBIST_DATA_EYE structure
 * @return EFI_STATUS - Status Code
*/
typedef EFI_STATUS (EFIAPI * PEI_GET_MBIST_DATA_EYE) (
  IN CONST EFI_PEI_SERVICES  **PeiServices,
  IN UINT32 *BufferSize,
  IN OUT MBIST_DATA_EYE  *MbistDataEye
);

/**
 * @brief AMD Memory MBIST Test Results PPI
 */
typedef struct _AMD_MEMORY_MBIST_TEST_RESULTS_PPI {
  UINT32                        Revision; ///< PPI Revision
  MBIST_TEST_STATUS             MbistTestStatus [MAX_SOCKETS][MAX_DIES_PER_SOCKET]; ///< Test results for all channels per subtest per die per socket
  MBIST_DATA_EYE                MbistDataEyeMargin[ MAX_SOCKETS][MAX_DIES_PER_SOCKET]; ///< Data eye margins for all chipselects per channel per die per socket
  PEI_GET_MBIST_TEST_RESULTS    GetMbistTestResults; ///< Function for getting MBIST Test Results @see PEI_GET_MBIST_TEST_RESULTS
  PEI_GET_MBIST_DATA_EYE        GetMbistDataEyeMargin; ///< Function for getting MBIST Data Eye Margin @see PEI_GET_MBIST_DATA_EYE
} AMD_MEMORY_MBIST_TEST_RESULTS_PPI;

// Current PPI revision
#define AMD_MBIST_PPI_REVISION   0x03

extern EFI_GUID gAmdMbistPeiPpiGuid;

#endif  //_AMD_MBIST_PPI_H_



