#;*****************************************************************************
#;
#; Copyright (C) 2024 Advanced Micro Devices, Inc. All rights reserved
#;
#;******************************************************************************
[Defines]
  INF_VERSION                    = 0x00010006
  BASE_NAME                      = AmdEmulationAutoDetect
  FILE_GUID                      = 397D157A-5F65-4242-AA33-36AA2EB37A3A
  MODULE_TYPE                    = BASE
  VERSION_STRING                 = 1.0
  LIBRARY_CLASS                  = AmdEmulationAutoDetectLib|SEC PEIM PEI_CORE

[Sources.common]
  AmdEmulationAutoDetectPeiLib.c

[Sources.IA32]

[Sources.X64]

[Packages]
  MdePkg/MdePkg.dec
  AgesaModulePkg/AgesaCommonModulePkg.dec
  AgesaModulePkg/AgesaModuleFchPkg.dec
  AgesaPkg/AgesaPkg.dec


[LibraryClasses]
  BaseLib
  AmdBaseLib
  PciCf8Lib
  PcdLib

[Guids]

[Protocols]

[Ppis]

[FeaturePcd]

[Pcd]
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdIdsDebugPrintEmulationAutoDetect
[Depex]
  TRUE




