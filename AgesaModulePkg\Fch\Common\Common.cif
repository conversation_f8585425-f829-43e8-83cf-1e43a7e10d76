<component>
    name = "Common"
    category = ModulePart
    LocalRoot = "AgesaModulePkg\Fch\Common\"
    RefName = "AgesaModulePkg.Fch.Common"
[parts]
"AgesaModulePkg.Fch.Common.Asl"
"AgesaModulePkg.Fch.Common.FchEspiCmdDxe"
"AgesaModulePkg.Fch.Common.FchEspiCmdPei"
"AgesaModulePkg.Fch.Common.FchEspiCmdSmm"
"AgesaModulePkg.Fch.Common.I2cDxe"
"AgesaModulePkg.Fch.Common.I2cPei"
"AgesaModulePkg.Fch.Common.I2cSmm"
"AgesaModulePkg.Fch.Common.I3cDxe"
"AgesaModulePkg.Fch.Common.I3cPei"
"AgesaModulePkg.Fch.Common.Include"
<endComponent>
