#;*****************************************************************************
#;
#; Copyright (C) 2008-2024 Advanced Micro Devices, Inc. All rights reserved.
#;
#;******************************************************************************

[Defines]
  INF_VERSION                    = 0x00010005
  BASE_NAME                      = AmdFabricBrhDxe
  FILE_GUID                      = B040C9F7-5F6A-4B67-A7E5-4EAD9412F920
  MODULE_TYPE                    = DXE_DRIVER
  VERSION_STRING                 = 1.0
  ENTRY_POINT                    = AmdFabricBrhDxeInit

[Packages]
  MdePkg/MdePkg.dec
  AgesaModulePkg/AgesaCommonModulePkg.dec
  AgesaModulePkg/AgesaModuleDfPkg.dec
  AgesaModulePkg/AgesaFamily1AModulePkg.dec
  AgesaModulePkg/AgesaModuleCcxPkg.dec
  AgesaModulePkg/AgesaModuleNbioPkg.dec
  AgesaModulePkg/AgesaModuleMemPkg.dec
  AgesaModulePkg/AgesaModulePspPkg.dec
  AgesaPkg/AgesaPkg.dec

[LibraryClasses.common.DXE_DRIVER]
  BaseLib
  UefiLib

[LibraryClasses]
  UefiBootServicesTableLib
  UefiDriverEntryPoint
  BaseFabricTopologyLib
  DxeFabricTopologyServices2Lib
  FabricRegisterAccLib
  CxlCdatLib
  AmdIdsHookLib
  FabricIdsHookBrhLibDxe
  FabricResourceReportToGcdLib
  AmdPspApobLib
  AmdHeapLib
  DxeFabricResourceManagerServicesLib
  AmdPspBaseLibV2
  PcdLib
  ApobCommonServiceLib
  CoreTopologyV3Lib
  AmdSocBaseLib
  ApcbLibV3

[sources]
  AmdFabricBrhDxe.c
  FabricAcpiCdit.c
  FabricAcpiCrat.c
  FabricAcpiDistanceInfo.c
  FabricAcpiDomainInfo.c
  FabricAcpiDomainInfo.h
  FabricAcpiSlit.c
  FabricAcpiSrat.c
  FabricAcpiMsct.c
  FabricAcpiHmat.c
  FabricAcpiTable.h
  FabricReadyToBoot.c
  FabricReadyToBoot.h

[Guids]
  gEfiEventReadyToBootGuid

[Protocols]
  gAmdFabricResourceManagerServicesProtocolGuid # PRODUCED
  gAmdFabricAcpiSlitServicesProtocolGuid        # PRODUCED
  gAmdFabricNumaServices2ProtocolGuid           # PRODUCED
  gAmdFabricAcpiSratServicesProtocolGuid        # PRODUCED
  gAmdFabricAcpiCditServicesProtocolGuid        # PRODUCED
  gAmdFabricAcpiCratServicesProtocolGuid        # PRODUCED
  gAmdFabricAcpiMsctServicesProtocolGuid        # PRODUCED
  gAmdSocLogicalIdProtocolGuid                  # CONSUMED
  gAmdNbioSmuServicesProtocolGuid               # CONSUMED
  gAmdCoreTopologyServicesV3ProtocolGuid        # CONSUMED
  gEfiVariableWriteArchProtocolGuid             # CONSUMED
  gAmdApcbDxeServiceProtocolGuid                # CONSUMED
  gEfiPciEnumerationCompleteProtocolGuid        # CONSUMED
  gAmdAcpiHmatServicesProtocolGuid              # CONSUMED
  gAmdNbioCxlServicesProtocolGuid               # CONSUMED
  gApobCommonServiceProtocolGuid                # CONSUMED
  gAmdMemSmbiosServicesProtocolGuid             # CONSUMED
  gAmdApcbDxeServiceProtocolGuid                # CONSUMED
  gAmdMemChanXLatProtocolGuid                   # CONSUMED

[Pcd]
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdCStateMode
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdNumberOfPhysicalSocket
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdFabricCcxAsNumaDomain
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdFabricRoundRobinNumaDomainForCcx
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdFabricSlitDistancePcdCtrl
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdFabricSlitVirtualDistance
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdFabricSlitLocalDistance
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdFabricSlitRemoteDistance
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdFabricSlitCxlLocalDistance
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdFabricSlitCxlRemoteDistance
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdFabricSlitAutoRemoteFar
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdFabricSratSlitInstallOverride
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdAcpiHmat
  gEfiMdePkgTokenSpaceGuid.PcdPciExpressBaseAddress
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdFabric1TbRemap
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdCxlSpm

[Depex]
  gAmdFabricBrhDepexProtocolGuid AND
  gAmdApcbDxeServiceProtocolGuid

