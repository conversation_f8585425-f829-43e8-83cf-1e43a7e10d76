/*****************************************************************************
 * Copyright (C) 2008-2024 Advanced Micro Devices, Inc. All rights reserved.
 *
*****************************************************************************
*/
/* $NoKeywords:$ */
/**
 * @file
 *
 * AMD CPU SMBIOS functions.
 *
 * Contains code for collecting SMBIOS information
 *
 * @xrefitem bom "File Content Label" "Release Content"
 * @e project:      AGESA
 * @e sub-project:  Ccx
 * @e \$Revision$   @e \$Date$
 *
 */

/*----------------------------------------------------------------------------------------
 *                             M O D U L E S    U S E D
 *----------------------------------------------------------------------------------------
 */
#include <Porting.h>
#include <AMD.h>
#include <Filecode.h>
#include <cpuRegisters.h>
#include <Library/BaseLib.h>
#include <Library/AmdBaseLib.h>
#include <Library/CcxBaseX86Lib.h>
#include <Library/CcxSmbiosLib.h>
#include <Library/CcxPstatesLib.h>
#include <Library/UefiBootServicesTableLib.h>
#include <Protocol/SocZen5ServicesProtocol.h>

#define FILECODE CCX_ZEN5_DXE_CCXZEN5SMBIOSDXE_FILECODE

/*----------------------------------------------------------------------------------------
 *                   D E F I N I T I O N S    A N D    M A C R O S
 *----------------------------------------------------------------------------------------
 */

#define MAX_CCDS_PER_SKT 16

/*----------------------------------------------------------------------------------------
 *           P R O T O T Y P E S     O F     L O C A L     F U N C T I O N S
 *----------------------------------------------------------------------------------------
 */
EFI_STATUS
EFIAPI
Zen5GetCoreDmiInfo (
  IN       AMD_CCX_SMBIOS_SERVICES_PROTOCOL       *This,
  IN       UINTN                                   Socket,
     OUT   AMD_CCX_CORE_DMI_INFO                  *CoreDmiInfo
  );

EFI_STATUS
EFIAPI
Zen5GetCacheDmiInfo (
  IN       AMD_CCX_SMBIOS_SERVICES_PROTOCOL       *This,
  IN       UINTN                                   Socket,
     OUT   AMD_CACHE_DMI_INFO                     *CacheDmiInfo
  );

/*----------------------------------------------------------------------------------------
 *                           G L O B A L   V A R I A B L E S
 *----------------------------------------------------------------------------------------
 */
STATIC AMD_CCX_SMBIOS_SERVICES_PROTOCOL   mZen5DmiServicesProtocol = {
  SMBIOS_3_2_0,      // Support SMBIOS 3.2.0
  Zen5GetCoreDmiInfo,
  Zen5GetCacheDmiInfo
};

CHAR8 ROMDATA str_ProcManufacturer[] = "Advanced Micro Devices, Inc.";

UINT16 ROMDATA CacheConfigTable[CpuLmaxCache] = {
  CACHE_CFG_L1,
  CACHE_CFG_L2,
  CACHE_CFG_L3
};


/*----------------------------------------------------------------------------------------
 *                  T Y P E D E F S     A N D     S T R U C T U R E S
 *----------------------------------------------------------------------------------------
 */


/*----------------------------------------------------------------------------------------
 *                          E X P O R T E D    F U N C T I O N S
 *----------------------------------------------------------------------------------------
 */

EFI_STATUS
EFIAPI
CcxZen5SmbiosServicesProtocolInstall (
  IN       EFI_HANDLE        ImageHandle,
  IN       EFI_SYSTEM_TABLE  *SystemTable
  )
{
  // Install SMBIOS services protocol
  return gBS->InstallProtocolInterface (
                &ImageHandle,
                &gAmdCcxSmbiosServicesProtocolGuid,
                EFI_NATIVE_INTERFACE,
                &mZen5DmiServicesProtocol
                );
}

/*----------------------------------------------------------------------------------------
 *                          L O C A L    F U N C T I O N S
 *----------------------------------------------------------------------------------------
 */
/**
 *  This service retrieves DMI information about the core.
 *
 * @param[in]  This                                 A pointer to the
 *                                                  AMD_CCX_SMBIOS_SERVICES_PROTOCOL instance.
 * @param[in]  Socket                               Zero-based socket number to check.
 * @param[out] CoreDmiInfo                          Contains core DMI information
 *
 * @retval EFI_SUCCESS                              The core DMI information was successfully retrieved.
 * @retval EFI_INVALID_PARAMETER                    CoreDmiInfo is NULL.
 *
 **/
EFI_STATUS
EFIAPI
Zen5GetCoreDmiInfo (
  IN       AMD_CCX_SMBIOS_SERVICES_PROTOCOL       *This,
  IN       UINTN                                   Socket,
     OUT   AMD_CCX_CORE_DMI_INFO                  *CoreDmiInfo
  )
{
  UINT64                                  MsrData;
  UINTN                                   Frequency;
  UINTN                                   VoltageInuV;
  UINTN                                   PowerInmW;
  EFI_STATUS                              Status;
  AMD_SOC_ZEN5_SERVICES_PROTOCOL         *SocZen5Services;
  UINT32                                  CcdCountFuse;
  UINT32                                  CcdPresentFuse;
  UINT32                                  CcdDownFuse;
  UINT32                                  CoreCountByFuse;
  UINT32                                  CoreDisFuse[MAX_CCDS_PER_SKT];
  UINT32                                  CoreDisFuseSize;
  BOOLEAN                                 SmtEnabledByFuse[MAX_CCDS_PER_SKT];
  UINT32                                  SmtEnabledByFuseSize;
  UINT32                                  Fmax;
  CPUID_DATA                              CpuId;
  AMD_CONFIG_PARAMS                       StdHeader;
  UINT32                                  Index;
  UINT8                                   ByteIndexInUint64;
  BOOLEAN                                 SmtEnByFuse;

  SocZen5Services = NULL;
  Status = gBS->LocateProtocol (
                  &gAmdSocZen5ServicesProtocolGuid,
                  NULL,
                  (VOID **)&SocZen5Services
                  );
  ASSERT (!EFI_ERROR (Status));

  // Type 4 Offset 0x05, Processor Type
  CoreDmiInfo->ProcessorType = CENTRAL_PROCESSOR;

  // Type 4 Offset 0x06, Processor Family
  CoreDmiInfo->ProcessorFamily = CcxGetProcessorFamilyForSmbios (0, &StdHeader);

  // Type4 Offset 0x08, Processor ID
  CpuId.EAX_Reg = 0;
  CpuId.EDX_Reg = 0;
  AsmCpuid (
      AMD_CPUID_APICID_LPC_BID,
      &(CpuId.EAX_Reg),
      NULL,
      NULL,
      &(CpuId.EDX_Reg)
      );
  CoreDmiInfo->ProcessorID.EaxFamilyId = CpuId.EAX_Reg;
  CoreDmiInfo->ProcessorID.EdxFeatureId = CpuId.EDX_Reg;

  // Type4 Offset 0x11/0x16, Voltage/Current Speed
  CcxGetPstateInfo (0, SwPstate0, &Frequency, &VoltageInuV, &PowerInmW, &StdHeader);
  CoreDmiInfo->Voltage = (UINT8) ((VoltageInuV + 50000) / 100000 + 0x80); // Voltage = 0x80 + Voltage * 10
  CoreDmiInfo->CurrentSpeed = (UINT16) Frequency;

  // Type4 Offset 0x14, Max Speed
  CoreDmiInfo->MaxSpeed = (SocZen5Services->GetOpnFmax (SocZen5Services, &Fmax) == EFI_SUCCESS) ? (UINT16) Fmax : CoreDmiInfo->CurrentSpeed;

  // Type4 Offset 0x12, External Clock
  CoreDmiInfo->ExternalClock = EXTERNAL_CLOCK_100MHZ;

  // Type4 Offset 0x18, Status
  CoreDmiInfo->Status = SOCKET_POPULATED | CPU_STATUS_ENABLED;

  // Type4 Offset 0x19, Processor Upgrade
  CoreDmiInfo->ProcessorUpgrade = CcxGetProcessorUpgradeForSmbios (0, &StdHeader);

  // Type4 Offset 0x23/0x25, Core Count/Thread Count
  CpuId.ECX_Reg = 0;
  AsmCpuid (
      AMD_CPUID_ASIZE_PCCOUNT,
      NULL,
      NULL,
      &(CpuId.ECX_Reg),
      NULL
      );

  CoreDisFuseSize = sizeof (CoreDisFuse);
  SmtEnabledByFuseSize = sizeof (SmtEnabledByFuse);
  if (SocZen5Services->GetOpnCorePresenceEx (
                         SocZen5Services,
                         0,
                         &CcdPresentFuse,
                         &CcdDownFuse,
                         &CoreDisFuse[0],
                         &CoreDisFuseSize,
                         &CoreCountByFuse,
                         &SmtEnabledByFuse[0],
                         &SmtEnabledByFuseSize
                         ) == EFI_SUCCESS) {
    CoreDmiInfo->CoreCount = 0;
    CoreDmiInfo->ThreadCount = 0;

    for (Index = 0; Index < MAX_CCDS_PER_SKT; Index++) {
      if (((1 << Index) & CcdPresentFuse) && (((1 << Index) & CcdDownFuse) == 0)) {
        CoreDmiInfo->CoreCount += (UINT16) (CoreCountByFuse);
        CoreDmiInfo->ThreadCount += (UINT16) (CoreCountByFuse * 2);

        while (CoreDisFuse[Index] != 0) {
          if ((CoreDisFuse[Index] & 1) == 1) {
            CoreDmiInfo->CoreCount--;
            CoreDmiInfo->ThreadCount -= 2;
          } else if (SmtEnabledByFuse[Index] == FALSE) {
            CoreDmiInfo->ThreadCount--;
          }
          CoreDisFuse[Index] >>= 1;
        }
      }
    }
  } else if (SocZen5Services->GetOpnCorePresence (
                                SocZen5Services,
                                &CcdPresentFuse,
                                &CoreDisFuse[0],
                                &CoreCountByFuse,
                                &SmtEnByFuse
                                ) == EFI_SUCCESS) {
    // Get Core Count/Thread Count from FUSE
    while (CoreDisFuse[0] != 0) {
      if ((CoreDisFuse[0] & 1) == 1) {
        CoreCountByFuse--;
      }
      CoreDisFuse[0] >>= 1;
    }

    CcdCountFuse = 0;
    while (CcdPresentFuse != 0) {
      if ((CcdPresentFuse & 1) == 1) {
        CcdCountFuse++;
      }
      CcdPresentFuse >>= 1;
    }

    CoreDmiInfo->CoreCount = (UINT16) (CoreCountByFuse * CcdCountFuse);
    CoreDmiInfo->ThreadCount = (UINT16) CoreDmiInfo->CoreCount << (SmtEnByFuse ? 1 : 0);
  } else {
    // Get Core Count/Thread Count from registers
    CoreDmiInfo->ThreadCount = (UINT16) ((CpuId.ECX_Reg & 0xFFF) + 1); // bit 11:0
    CoreDmiInfo->CoreCount = CoreDmiInfo->ThreadCount / CcxGetThreadsPerCore ();
  }

  // Type4 Offset 0x24 Core Enabled
  CoreDmiInfo->CoreEnabled = (UINT16) (((CpuId.ECX_Reg & 0xFFF) + 1) / CcxGetThreadsPerCore ());

  // Type4 Offset 0x26, Processor Characteristics
  CoreDmiInfo->ProcessorCharacteristics = 0xFC;

  // Type4 ProcessorVersion
  for (Index = 0; Index <= 5; Index++) {
    MsrData = AsmReadMsr64 ((MSR_CPUID_NAME_STRING0 + Index));
    for (ByteIndexInUint64 = 0; ByteIndexInUint64 <= 7; ByteIndexInUint64++) {
      CoreDmiInfo->ProcessorVersion[Index * 8 + ByteIndexInUint64] = (UINT8) RShiftU64 (MsrData, (8 * ByteIndexInUint64));
    }
  }

  // Type4 ProcessorManufacturer
  ASSERT (PROC_MANU_LENGTH >= sizeof (str_ProcManufacturer));
  LibAmdMemCopy (CoreDmiInfo->ProcessorManufacturer, str_ProcManufacturer, sizeof (str_ProcManufacturer), &StdHeader);

  return EFI_SUCCESS;
}


/**
 * This service retrieves information about the cache.
 *
 * @param[in]  This                                 A pointer to the
 *                                                  AMD_CCX_SMBIOS_SERVICES_PROTOCOL instance.
 * @param[in]  Socket                               Zero-based socket number to check.
 * @param[out] CacheDmiInfo                         Contains cache DMI information
 *
 * @retval EFI_SUCCESS                              The cache DMI information was successfully retrieved.
 * @retval EFI_INVALID_PARAMETER                    CacheDmiInfo is NULL.
 *
 **/
EFI_STATUS
EFIAPI
Zen5GetCacheDmiInfo (
  IN       AMD_CCX_SMBIOS_SERVICES_PROTOCOL       *This,
  IN       UINTN                                   Socket,
     OUT   AMD_CACHE_DMI_INFO                     *CacheDmiInfo
  )
{
  GET_CACHE_INFO     CacheInfo;
  AMD_CACHE_LEVEL    CacheLevel;
  AMD_CONFIG_PARAMS  StdHeader;

  CcxGetCacheInfo (0, &CacheInfo, &StdHeader);

  for (CacheLevel = CpuL1Cache; CacheLevel < CpuLmaxCache; CacheLevel++) {
    // Type7 Offset 0x05, Cache Configuration
    CacheDmiInfo->CacheEachLevelInfo[CacheLevel].CacheCfg = CacheConfigTable[CacheLevel];

    // Type7 Offset 0x07 and 09, Maximum Cache Size and Installed Size
    // Maximum size
    if (CacheInfo.CacheEachLevelInfo[CacheLevel].CacheSize > 0xFFFF) {
      CacheDmiInfo->CacheEachLevelInfo[CacheLevel].MaxCacheSize  = 0xFFFF;
      CacheDmiInfo->CacheEachLevelInfo[CacheLevel].MaxCacheSize2 = CacheInfo.CacheEachLevelInfo[CacheLevel].CacheSize;
    } else {
      CacheDmiInfo->CacheEachLevelInfo[CacheLevel].MaxCacheSize  = (UINT16) CacheInfo.CacheEachLevelInfo[CacheLevel].CacheSize;
      CacheDmiInfo->CacheEachLevelInfo[CacheLevel].MaxCacheSize2 = ((CacheInfo.CacheEachLevelInfo[CacheLevel].CacheSize & 0x7FFF) |
                                                                   ((CacheInfo.CacheEachLevelInfo[CacheLevel].CacheSize & 0x8000) << 16));
    }

    // Installed size
    CacheDmiInfo->CacheEachLevelInfo[CacheLevel].InstallSize  = CacheDmiInfo->CacheEachLevelInfo[CacheLevel].MaxCacheSize;
    CacheDmiInfo->CacheEachLevelInfo[CacheLevel].InstallSize2 = CacheDmiInfo->CacheEachLevelInfo[CacheLevel].MaxCacheSize2;

    // Type7 Offset 0x0B and 0D, Supported SRAM Type and Current SRAM Type
    CacheDmiInfo->CacheEachLevelInfo[CacheLevel].SupportedSramType = SRAM_TYPE_PIPELINE_BURST;
    CacheDmiInfo->CacheEachLevelInfo[CacheLevel].CurrentSramType   = SRAM_TYPE_PIPELINE_BURST;

    // Type7 Offset 0x0F, Cache Speed
    CacheDmiInfo->CacheEachLevelInfo[CacheLevel].CacheSpeed = 1;

    // Type7 Offset 0x10, Error Correction Type
    CacheDmiInfo->CacheEachLevelInfo[CacheLevel].ErrorCorrectionType = ERR_CORRECT_TYPE_MULTI_BIT_ECC;

    // Type7 Offset 0x11, System Cache Type
    CacheDmiInfo->CacheEachLevelInfo[CacheLevel].SystemCacheType = CACHE_TYPE_UNIFIED;

    // Type7 Offset 0x12, Associativity
    CacheDmiInfo->CacheEachLevelInfo[CacheLevel].Associativity = CacheInfo.CacheEachLevelInfo[CacheLevel].Associativity;
  }

  return EFI_SUCCESS;
}

