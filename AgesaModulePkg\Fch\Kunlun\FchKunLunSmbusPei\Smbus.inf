#;*****************************************************************************
#;
#; Copyright (C) 2008-2024 Advanced Micro Devices, Inc. All rights reserved.
#;
#;******************************************************************************

[defines]

  INF_VERSION                   = 0x00010005
  BASE_NAME                     = FchSmbusPei
  FILE_GUID                     = 5c2cd12e-e2f2-4276-a0f2-4a17832f774d
  MODULE_TYPE                   = PEIM
  VERSION_STRING                = 1.0
  ENTRY_POINT                   = AmdSmbusPeiInit


[sources.common]
  Smbus.c
  Smbus.h

[sources.ia32]

[sources.x64]

[LibraryClasses.IA32]

[LibraryClasses]
  PeimEntryPoint
  BaseLib
  DebugLib
  IoLib
  PeiServicesLib
  HobLib
  AmdBaseLib

[Guids]

[Protocols]

[Ppis]
  gEfiPeiSmbus2PpiGuid          #PRODUCED

[Packages]
  MdePkg/MdePkg.dec
  AgesaModulePkg/AgesaModuleFchPkg.dec
  AgesaModulePkg/Fch/Kunlun/FchKunlun.dec

[Depex]
  gEfiPeiCpuIoPpiInstalledGuid  AND
  gAmdFchInitPpiGuid AND
  gAmdFchKLSmbusDepexPpiGuid
  # gEfiPeiSmbus2PpiGuid needs to be available before Agesa AmdInitReset has
  # been run even though AmdInitReset should be the function that initializes
  # the southbridge.  Don't put a dependency on gAmdPeiInitResetPpiGuid




