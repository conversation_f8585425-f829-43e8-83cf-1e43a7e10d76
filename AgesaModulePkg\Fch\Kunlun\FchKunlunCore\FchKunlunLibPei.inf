#;*****************************************************************************
#;
#; Copyright (C) 2008-2024 Advanced Micro Devices, Inc. All rights reserved.
#;
#;******************************************************************************

[Defines]
  LIBRARY_CLASS         = FchKunlunPeiLib | PEIM
  BASE_NAME             = FchKunlunPeiLib
  INF_VERSION           = 0x00010006
  FILE_GUID             = f6e76cd5-5a43-4f8b-a2f4-85cfb2af4c42
  MODULE_TYPE           = BASE
  VERSION_STRING        = 1.0

[Sources]
  Fch.h
  FchPage.h
  FchPlatform.h
  Common/AcpiLib.c
  Common/AcpiLib.h
  Common/FchAoacLib.c
  Common/FchCommon.c
  Common/FchCommonCfg.h
  Common/FchCommonSmm.c
  Common/FchDef.h
  Common/FchInterface.h
  Common/FchLib.c
  Common/FchPeLib.c
  Common/MemLib.c
  Common/PciLib.c
  Kunlun/ResetDefKL.c
  Kunlun/KLHwAcpi/KLHwAcpiReset.c
  Kunlun/KLHwAcpi/KLSSService.c
  Kunlun/KLInterface/KLFchInitReset.c
  Kunlun/KLInterface/KLFchTaskLauncher.c
  Kunlun/KLInterface/KLFchTaskLauncher.h
  Kunlun/KLPcie/KLAbReset.c
  Kunlun/KLPcie/KLAbResetService.c
  Kunlun/KLSata/KLSataReset.c
  Kunlun/KLSata/KLSataResetService.c
  Kunlun/KLSata/KLSataLib.c
  Kunlun/KLSd/KLSdResetService.c
  Kunlun/KLLpcSpi/KLLpcReset.c
  Kunlun/KLLpcSpi/KLLpcResetService.c
  Kunlun/KLLpcSpi/KLLpcEnvService.c
  Kunlun/KLLpcSpi/KLLpcEnv.c
  Kunlun/KLLpcSpi/KLSpiReset.c
  Kunlun/KLLpcSpi/KLSpiEnv.c
  Kunlun/KLEspi/KLEspiReset.c
  Kunlun/KLEspi/KLEspiLib.c
  Kunlun/KLUsb/KLUsbReset.c
  Kunlun/KLUsb/KLXhciReset.c
  Kunlun/KLUsb/KLXhciResetService.c
  Kunlun/KLUsb/KLXhciService.c

[Packages]
  MdePkg/MdePkg.dec
  AgesaPkg/AgesaPkg.dec
  AgesaModulePkg/AgesaCommonModulePkg.dec
  AgesaModulePkg/AgesaModuleFchPkg.dec
  AgesaModulePkg/Fch/Kunlun/FchKunlun.dec

[LibraryClasses]
  BaseMemoryLib
  AmdBaseLib
  AmdSocBaseLib
  IoLib
  AmdCapsuleLib
  NbioHandleLib
  NbioSmuBrhLib
  FchBaseLib
  AmdPspMboxLibV2

[LibraryClasses.common.PEIM]

[Guids]

[Protocols]

[Ppis]

[Pcd]
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdAmdEnvironmentFlag
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdAmdPreSilCtrl0

[BuildOptions]





