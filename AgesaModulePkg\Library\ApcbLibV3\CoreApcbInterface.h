/*****************************************************************************
 * Copyright (C) 2008-2024 Advanced Micro Devices, Inc. All rights reserved.
 *
*****************************************************************************
*/
/* $NoKeywords:$ */
/**
 * @file
 *
 * APCB.h
 *
 * AGESA PSP Customization Block
 *
 * @xrefitem bom "File Content Label" "Release Content"
 * @e project: AGESA
 * @e sub-project: (Mem)
 * @e \$Revision: 192403 $ @e \$Date: 2012-12-12 15:46:25 -0600 (Wed, 12 Dec 2012) $
 *
 **/

#ifndef _CORE_APCB_INTERFACE_H_
#define _CORE_APCB_INTERFACE_H_

#include <Library/ApcbCoreLib.h>

///
/// core APCB lib function prototypes
///
CORE_APCB_RET_CODE
coreApcbGetVersion (
  IN         UINT8        *apcbBuf,
      OUT    UINT16       *StructVersion,
      OUT    UINT16       *DataVersion
  );

CORE_APCB_RET_CODE
coreChecksumApcb (
  IN  OUT    UINT8        *apcbBuf
  );

CORE_APCB_RET_CODE
coreApcbSetBool (
  IN  OUT     UINT8       *apcbBuf,
  IN          UINT32      sizeApcbBuf,
  IN          UINT8       priorityMask,
  IN          UINT16      boardMask,
  IN          UINT8       typeInstance,
  IN          UINT32      apcbToken,
  IN          BOOLEAN     bValue
  );

CORE_APCB_RET_CODE
coreApcbSet8 (
  IN  OUT    UINT8        *apcbBuf,
  IN         UINT32       sizeApcbBuf,
  IN         UINT8        priorityMask,
  IN         UINT16       boardMask,
  IN         UINT8        typeInstance,
  IN         UINT32       apcbToken,
  IN         UINT8        value8
  );

CORE_APCB_RET_CODE
coreApcbSet16 (
  IN  OUT    UINT8        *apcbBuf,
  IN         UINT32       sizeApcbBuf,
  IN         UINT8        priorityMask,
  IN         UINT16       boardMask,
  IN         UINT8        typeInstance,
  IN         UINT32       apcbToken,
  IN         UINT16       value16
  );


CORE_APCB_RET_CODE
coreApcbSet32 (
  IN  OUT     UINT8       *apcbBuf,
  IN          UINT32      sizeApcbBuf,
  IN          UINT8       priorityMask,
  IN          UINT16      boardMask,
  IN          UINT8       typeInstance,
  IN          UINT32      apcbToken,
  IN          UINT32      value32
  );;

VOID
coreApcbFreeTypeChain (
  IN          CORE_APCB_TYPE_ENTRY  *coreApcbType
  );

VOID
coreApcbFreeTokenChain (
  IN          CORE_APCB_TOKEN_ENTRY  *coreApcbToken
  );

CORE_APCB_RET_CODE
coreApcbGetFirstType (
  IN        UINT8       *apcbBuf,
      OUT   UINT16      *groupId,
      OUT   UINT16      *typeId,
      OUT   UINT8       *priorityMask,
      OUT   UINT16      *boardMask,
      OUT   UINT16      *instance,
      OUT   UINT8       **dataBuf,
      OUT   UINT32      *dataSize,
      OUT   UINT32      *typeHandle
  );

CORE_APCB_RET_CODE
coreApcbGetNextType (
  IN  OUT     UINT32      *typeHandle,
      OUT     UINT16      *groupId,
      OUT     UINT16      *typeId,
      OUT     UINT8       *priorityMask,
      OUT     UINT16      *boardMask,
      OUT     UINT16      *instance,
      OUT     UINT8       **dataBuf,
      OUT     UINT32      *dataSize
  );

CORE_APCB_RET_CODE
coreApcbGetType (
  IN           UINT8       *apcbBuf,
  IN           UINT16      groupId,
  IN           UINT16      typeId,
  IN           UINT8       priorityMask,
  IN           UINT16      boardMask,
  IN           UINT16      instance,
      OUT      UINT8       **dataBuf,
      OUT      UINT32      *dataSize
  );

CORE_APCB_RET_CODE
coreApcbSetType (
  IN           UINT8       *apcbBuf,
  IN           UINT32      sizeApcbBuf,
  IN           UINT16      groupId,
  IN           UINT16      typeId,
  IN           UINT8       priorityMask,
  IN           UINT16      boardMask,
  IN           UINT16      instance,
  IN           UINT8       *dataBuf,
  IN           UINT32      dataSize
  );

CORE_APCB_RET_CODE
coreApcbGetFirstToken (
  IN          UINT32      typeHandle,
      OUT     UINT32      *token,
      OUT     VOID        *value,
      OUT     UINT8       *attribute,
      OUT     UINT32      *tokenHandle
  );

CORE_APCB_RET_CODE
coreApcbGetNextToken (
  IN  OUT     UINT32      *tokenHandle,
      OUT     UINT32      *token,
      OUT     VOID        *value,
      OUT     UINT8       *attribute
  );


#endif



