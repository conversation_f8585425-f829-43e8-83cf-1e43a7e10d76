/*
 ******************************************************************************
 *
 * Copyright (C) 2008-2024 Advanced Micro Devices, Inc. All rights reserved.
 *
 *******************************************************************************
 */


/*----------------------------------------------------------------------------------------
 *                             M O D U L E S    U S E D
 *----------------------------------------------------------------------------------------
 */
#include <Uefi.h>
#include <PiPei.h>
#include "AGESA.h"
#include "Filecode.h"
#include <Library/AmdBaseLib.h>
#include <Library/HobLib.h>
#include <Library/AmdEmulationFlagLib.h>

#define FILECODE LIBRARY_AMDEMULATIONFLAGLIB_AMDEMULATIONFLAGPEILIB_FILECODE

 /*----------------------------------------------------------------------------------------
  *                   D E F I N I T I O N S    A N D    M A C R O S
  *----------------------------------------------------------------------------------------
  */

 /*----------------------------------------------------------------------------------------
  *                  T Y P E D E F S     A N D     S T R U C T U R E S
  *----------------------------------------------------------------------------------------
  */
typedef struct {
  UINT8        CtrlFlag;
  CHAR16       *Name;
} PRE_SIL_CTRL_FLAG_STR_MAP;

#define MAKE_PRE_SIL_CTRL_FLAG_NODE(CtrlFlag) {CtrlFlag, L ## #CtrlFlag}

static PRE_SIL_CTRL_FLAG_STR_MAP PresiliconControlStringMap[] = {
  MAKE_PRE_SIL_CTRL_FLAG_NODE(NO_FLAGS),
  MAKE_PRE_SIL_CTRL_FLAG_NODE(PRESIL_SKIP_ALL_USBCONTROLLER_ACCESS),
  MAKE_PRE_SIL_CTRL_FLAG_NODE(PRESIL_SKIP_ALL_USBPHY_ACCESS),
  MAKE_PRE_SIL_CTRL_FLAG_NODE(PRESIL_SKIP_DXIO_INITIALIZATION),
  MAKE_PRE_SIL_CTRL_FLAG_NODE(PRESIL_SKIP_USBCODE_INSBIOS),
  MAKE_PRE_SIL_CTRL_FLAG_NODE(PRESIL_FASTSIM_GIO),
  MAKE_PRE_SIL_CTRL_FLAG_NODE(PRESIL_FASTSIM_DXIO),
  MAKE_PRE_SIL_CTRL_FLAG_NODE(PRESIL_FASTSIM_DFLT_TBL),
  MAKE_PRE_SIL_CTRL_FLAG_NODE(PRESIL_FASTSIM_SMU_MSGS),
  MAKE_PRE_SIL_CTRL_FLAG_NODE(PRESIL_DISABLE_EC),
  MAKE_PRE_SIL_CTRL_FLAG_NODE(PRESIL_FASTSIM_PEI_LOG),
  MAKE_PRE_SIL_CTRL_FLAG_NODE(PRESIL_FASTSIM_DXE_LOG),
  MAKE_PRE_SIL_CTRL_FLAG_NODE(PRESIL_SKIP_IOMMU_ENABLEMENT),
  MAKE_PRE_SIL_CTRL_FLAG_NODE(PRESIL_PORT80_REDIRECT),
};

 /*----------------------------------------------------------------------------------------
  *           P R O T O T Y P E S     O F     L O C A L     F U N C T I O N S
  *----------------------------------------------------------------------------------------
  */

 /**
  *      Get Emulation switch flag
  *
  *
  *  @param[in, out]      RawPreSilCtrl0    Printer to the value of PreSilCtrl0 register(C2PMSG_97)
  *  @param[in, out]      RawPreSilCtrl1    Printer to the value of PreSilCtrl1 register(C2PMSG_98)
  *  @return BOOLEAN    Returns TRUE if in emulatoin run. False if run in HW
  *
  **/
BOOLEAN
GetEmulationFlag (
  IN OUT UINT32                      *RawPreSilCtrl0,
  IN OUT UINT32                      *RawPreSilCtrl1
)
{
  UINT8                       *EnvFlagesHobBuffer = NULL;
  VOID                        *GuidHob;
  PRESILICON_CONTROL_STRUCT   *TempPresilCtrlPtr = NULL;

  GuidHob = GetFirstGuidHob (&gAmdEmulationFlagHobGuid);
  if (GuidHob == NULL) {
    return 0;
  }
  EnvFlagesHobBuffer = (UINT8 *) GET_GUID_HOB_DATA(GuidHob);

  TempPresilCtrlPtr = (PRESILICON_CONTROL_STRUCT *)EnvFlagesHobBuffer;
  *RawPreSilCtrl0 = TempPresilCtrlPtr->RawPreSilCtrl0;
  *RawPreSilCtrl1 = TempPresilCtrlPtr->RawPreSilCtrl1;

  return (TempPresilCtrlPtr->EnvType == ENV_HW ? 0: 1);
}


/**
 *
 *  presil_CheckEnv
 *
 *  Check the external environment to see if it matches the
 *  value passed in. Also, check the enabled control flags to
 *  see if the flag that is passed in is true. If these
 *  conditions are met, this functions will return true,
 *  enabling the workaround.
 *
 *  @param[in]        Env       Which environment need to be check.
 *  @param[in]        CtrlFlag  Which control flag need to be check.
 *  @return BOOLEAN   Returns TRUE if environment/control flag are matchs with request. Retruns False if mismatch.
 * */
BOOLEAN
EmulationFlagCheck (
  UINT8 Env,
  UINT8 CtrlFlag
  )
{
  UINT8                     *EnvFlagesHobBuffer = NULL;
  VOID                      *GuidHob;
  PRESILICON_CONTROL_STRUCT *PresilCtrlPtr = NULL;

  GuidHob = GetFirstGuidHob (&gAmdEmulationFlagHobGuid);
  if (GuidHob == NULL) {
    return 0;
  }
  EnvFlagesHobBuffer = (UINT8 *) GET_GUID_HOB_DATA(GuidHob);

  PresilCtrlPtr = (PRESILICON_CONTROL_STRUCT*)EnvFlagesHobBuffer;

  if (CtrlFlag >= NUM_CONTROL_FLAGS) {
    ASSERT (CtrlFlag < NUM_CONTROL_FLAGS);
    return FALSE;
  }

  if (((Env == ENV_ALL) || PresilCtrlPtr->EnvType == (PRESILICON_ENV_TYPE)Env) && (PresilCtrlPtr->ControlFlags[CtrlFlag] == TRUE)) {
    return TRUE;
  }

  return FALSE;
}

STATIC
CHAR16*
FindCtrlFlagName (
  UINT8         CtrlFlag
  )
{
  UINT32 i;
  UINT32 Length;
  Length = sizeof(PresiliconControlStringMap) / sizeof (PRE_SIL_CTRL_FLAG_STR_MAP);
  for (i = 0; i < Length; i++) {
    if (PresiliconControlStringMap[i].CtrlFlag == CtrlFlag) {
      return PresiliconControlStringMap[i].Name;
    }
  }

  return L"Undefined CtrlFlag";
}

VOID
DisplayEmulationFlag (
  VOID
)
{
  UINT8                       *EnvFlagesHobBuffer = NULL;
  VOID                       *GuidHob;
  PRESILICON_CONTROL_STRUCT   *PresilCtrlPtr = NULL;
  UINT8                       CtrlFlag;

  GuidHob = GetFirstGuidHob (&gAmdEmulationFlagHobGuid);
  if (GuidHob == NULL) {
    return;
  }
  EnvFlagesHobBuffer = (UINT8 *) GET_GUID_HOB_DATA(GuidHob);

  PresilCtrlPtr = (PRESILICON_CONTROL_STRUCT *)EnvFlagesHobBuffer;

  IDS_HDT_CONSOLE (MAIN_FLOW, "Environment Type: %s\n", (PresilCtrlPtr->EnvType == ENV_EMULATION? L"Emulation" : PresilCtrlPtr->EnvType == ENV_HW ? L"HW" : L"None"));

  IDS_HDT_CONSOLE (MAIN_FLOW, "  Control Flags\n");
  for(CtrlFlag = NO_FLAGS+1; CtrlFlag < NUM_CONTROL_FLAGS; CtrlFlag++) {
    IDS_HDT_CONSOLE (MAIN_FLOW, "  %s(%d) = %s\n", FindCtrlFlagName(CtrlFlag), CtrlFlag, PresilCtrlPtr->ControlFlags[CtrlFlag] ? L"TRUE" : L"FALSE");
  }
  IDS_HDT_CONSOLE (MAIN_FLOW, "\n");

  return;
}

