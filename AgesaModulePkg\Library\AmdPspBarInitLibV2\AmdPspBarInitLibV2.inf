#;*****************************************************************************
#;
#; Copyright (C) 2008-2024 Advanced Micro Devices, Inc. All rights reserved.
#;
#;******************************************************************************

[Defines]
  INF_VERSION                    = 0x00010006
  BASE_NAME                      = AmdPspBarInitLibV2
  FILE_GUID                      = DDE8B5D5-6DCF-4551-91D0-641F135E5B46
  MODULE_TYPE                    = PEIM
  VERSION_STRING                 = 1.0
  LIBRARY_CLASS                  = AmdPspBarInitLibV2

[Sources]
  AmdPspBarInitLibV2.c

[Packages]
  MdePkg/MdePkg.dec
  AgesaPkg/AgesaPkg.dec
  AgesaModulePkg/AgesaCommonModulePkg.dec
  AgesaModulePkg/AgesaModuleCcxPkg.dec
  AgesaModulePkg/AgesaModulePspPkg.dec
  AgesaModulePkg/AgesaModuleFchPkg.dec
  AgesaPkg/AgesaPkg.dec

[LibraryClasses]
  BaseLib
  BaseMemoryLib
  AmdBaseLib
  PciLib
  FabricResourceManagerLib
  CcxRolesLib
  AmdPspBaseLibV2
  AmdDirectoryBaseLib
  AmdPspRegBaseLib
  IdsLib

[LibraryClasses]
  PeiServicesTablePointerLib

[Guids]

[Ppis]
  gAmdFabricResourceManagerServicesPpiGuid

[Pcd]

[Depex]


