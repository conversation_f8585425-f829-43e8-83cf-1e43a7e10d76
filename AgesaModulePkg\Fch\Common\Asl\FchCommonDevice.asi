/*
  Supported device in FchCommonDevice.asi

  #Define   :   Device
   _HFP1_   :   HFP1
   _HID0_   :   HID0
   _GPIO_   :   GPIO
   _PPKG_   :   PPKG
   _UARx_   :   FURx,UARx(URAT)
   _I2Cx_   :   I2Cx
*/
Scope(\_SB) {

  #ifdef _HFP1_
  Device(HFP1) {  // HFP controller
    Name(_HID, "AMDI0060")
//    Name(_CID, "AMDI0060")
    Name(_UID, 0)

    Method (_STA, 0, NotSerialized) {
      if (HFPE) {
        Return (0x0f)
      } else {
        Return (0x00)
      }
    } // end of Method _STA

    Method (_CRS, 0x0, NotSerialized) {
      Name (RBUF, ResourceTemplate () {
        Memory32Fixed(ReadWrite, 0xFEC11000, 0x100)
      }) // End of Name (RBUF
      Return (RBUF)
    } // End of Method _CRS

  } // End Device HFP
  #endif

  #ifdef _HID0_
  Device(HID0) {  // HID 0 controller
    Name(_HID, "AMDI0063")
    Name(_CID, "PNP0C51")
    Name(_UID, 0)

    Method (_STA, 0, NotSerialized) {
      if (HD0E) {
        Return (0x0f)
      } else {
        Return (0x00)
      }
    } // end of Method _STA

    Method (_CRS, 0x0, NotSerialized) {
      Name (RBUF, ResourceTemplate () {
        Memory32Fixed(ReadWrite, 0xFEC13000, 0x200)
        GpioInt (Edge, ActiveHigh, SharedAndWake, PullNone, 0x0000, "\\_SB.GPIO", 0x00, ResourceConsumer, , ) {171}    //
      }) // End of Name (RBUF
      Return (RBUF)
    } // End of Method _CRS

  } // End Device HID0
  #endif

  #ifdef _HID2_
  Device(HID2) {  // HID 2 controller
    Name(_HID, "AMDI0063")
    Name(_CID, "PNP0C51")
    Name(_UID, 2)

    Method (_STA, 0, NotSerialized) {
      if (HD2E) {
        Return (0x0f)
      } else {
        Return (0x00)
      }
    } // end of Method _STA

    Method (_CRS, 0x0, NotSerialized) {
      Name (RBUF, ResourceTemplate () {
        Memory32Fixed(ReadWrite, 0xFEC12000, 0x200)
        GpioInt (Edge, ActiveHigh, SharedAndWake, PullNone, 0x0000, "\\_SB.GPIO", 0x00, ResourceConsumer, , ) {171}    //
      }) // End of Name (RBUF
      Return (RBUF)
    } // End of Method _CRS

  } // End Device HID2
  #endif

  #ifdef _GPIO_
  Device(GPIO) {
    Name (_HID, "AMDI0030")
    Name (_CID, "AMDI0030")
    Name(_UID, 0)

    Method (_CRS, 0x0, NotSerialized) {
      Name (RBUF, ResourceTemplate () {
        //
        // Interrupt resource. In this example, banks 0 & 1 share the same
        // interrupt to the parent controller and similarly banks 2 & 3.
        //
        // N.B. The definition below is chosen for an arbitrary
        //      test platform. It needs to be changed to reflect the hardware
        //      configuration of the actual platform
        //
        Interrupt(ResourceConsumer, Level, ActiveLow, Shared, , , ) {7}

        //
        // Memory resource. The definition below is chosen for an arbitrary
        // test platform. It needs to be changed to reflect the hardware
        // configuration of the actual platform.
        //
        Memory32Fixed(ReadWrite, 0xFED81500, 0x400)
      })

      Return (RBUF)
    }

    Method(_STA, 0, NotSerialized) {
        If (LGreaterEqual(TSOS, 0x70)) {
          Return (0x0F)
        } Else {
          Return (0x00)
        }
    }
  } // End Device GPIO
  #endif

  #ifdef _PPKG_
  Device(PPKG) {
    Name(_HID,"AMDI0052")
    //Name(_UID,0x0)
    Method(_STA, 0, NotSerialized) {
            Return (0x0F)
    }
  }  // End PKKG
  #endif

  // Uart Required interface:
  /*
    UT0O, UT0E, UT0I, UT0D;
    IUA0;
    FUIO;
    TSOS;
  */
  #ifdef _UAR0_
  Device(FUR0) {
  //    Name(_HID,"AMDI0020")            // UART Hardware Device ID
    Method(_HID, 0, Serialized) {
      if (Lequal(UT0O, 1)) {
        Return ("AMDI0029") //return dummy HID
      }
      Return("AMDI0020")  //
    }
    Name(_UID,0x0)
    Method(_CRS, 0, Serialized) {
      Name(BUF0, ResourceTemplate(){
        Interrupt(ResourceConsumer, Edge, ActiveHigh, Exclusive) {3}
        Memory32Fixed(ReadWrite, 0xFEDC9000, 0x1000)
        Memory32Fixed(ReadWrite, 0xFEDC7000, 0x1000)
      })
      // Create pointers to the specific byte
      CreateWordField (BUF0, 0x05, IRQW)
      //Modify the IRQ
      Store (IUA0, IRQW)
      Return(BUF0) // return the result
    }// end _CRS method

    Method (_STA, 0, NotSerialized) {
      Store (0x0, Local0)
      Store (FUIO (0), Local1)
      If (LGreaterEqual (TSOS, 0x70)) {
        If (LEqual (UT0E, One)) {
          If (LEqual (Local1, 0xF)) {
            Store (0xF, Local0)
          }
        }
      }
      If (LEqual (UT0I, One)) {
        Store (0x0, Local0)
      }
      Return (Local0)
    }

    Method(_S0W, 0) {
      if (LAnd(UT0D, UT0E)) {
        Return(4)
      } else {
        Return(0)
      }
    } // End of Method(_S0W

    Method(_PS0, 0) {
      if (LAnd(UT0D, UT0E)) {
        DSAD (11, 0)
      }
    } // End of Method(_PS0

    Method(_PS3, 0) {
      if (LAnd(UT0D, UT0E)) {
        DSAD (11, 3)
      }
    }
  } // End Device FUR0
  #endif

  #ifdef _UAR1_
  Device(FUR1) {
//    Name(_HID,"AMDI0020")            // UART Hardware Device ID
    Method(_HID, 0, Serialized) {
      if (Lequal(UT1O, 1)) {
        Return ("AMDI0029") //return dummy HID
      }
      Return("AMDI0020")  //
    }
    Name(_UID,0x1)
    Method(_CRS, 0, Serialized) {
      Name(BUF0, ResourceTemplate(){
        Interrupt(ResourceConsumer, Edge, ActiveHigh, Exclusive) {17}
        Memory32Fixed(ReadWrite, 0xFEDCA000, 0x1000)
        Memory32Fixed(ReadWrite, 0xFEDC8000, 0x1000)
      })
      // Create pointers to the specific byte
      CreateWordField (BUF0, 0x05, IRQW)
      //Modify the IRQ
      Store (IUA1, IRQW)
      //ShiftLeft (One, And (IUA1, 0x0F), IRQW)
      Return(BUF0) // return the result
    }// end _CRS method

    Method (_STA, 0, NotSerialized) {
      Store (0x0, Local0)
      Store (FUIO (1), Local1)
      If (LGreaterEqual (TSOS, 0x70)) {
        If (LEqual (UT1E, One)) {
          If (LEqual (Local1, 0xF)) {
            Store (0xF, Local0)
          }
        }
      }
      If (LEqual (UT1I, One)) {
        Store (0x0, Local0)
      }
      Return (Local0)
    }

    Method(_S0W, 0) {
      if (LAnd(UT1D, UT1E)) {
        Return(4)
      } else {
        Return(0)
      }
    } // End of Method(_S0W

    Method(_PS0, 0) {
      if (LAnd(UT1D, UT1E)) {
        DSAD (12, 0)
      }
    } // End of Method(_PS0

    Method(_PS3, 0) {
      if (LAnd(UT1D, UT1E)) {
        DSAD (12, 3)
      }
    }
  } // End Device FUR1
  #endif

  #ifdef _UAR2_
  Device(FUR2) {
//    Name(_HID,"AMDI0020")            // UART Hardware Device ID
    Method(_HID, 0, Serialized) {
      if (Lequal(UT2O, 1)) {
        Return ("AMDI0029") //return dummy HID
      }
      Return("AMDI0020")  //
    }
    Name(_UID,0x2)
    Method(_CRS, 0, Serialized) {
      Name(BUF0, ResourceTemplate(){
        Interrupt(ResourceConsumer, Edge, ActiveHigh, Exclusive) {5}
        Memory32Fixed(ReadWrite, 0xFEDCE000, 0x1000)
        Memory32Fixed(ReadWrite, 0xFEDCC000, 0x1000)
      })
      // Create pointers to the specific byte
      CreateWordField (BUF0, 0x05, IRQW)
      //Modify the IRQ
      Store (IUA2, IRQW)
      Return(BUF0) // return the result
    }// end _CRS method

    Method (_STA, 0, NotSerialized) {
      Store (0x0, Local0)
      Store (FUIO (2), Local1)
      If (LGreaterEqual (TSOS, 0x70)) {
        If (LEqual (UT2E, One)) {
          If (LEqual (Local1, 0xF)) {
            Store (0xF, Local0)
          }
        }
      }
      If (LEqual (UT2I, One)) {
        Store (0x0, Local0)
      }
      Return (Local0)
    }

    Method(_S0W, 0) {
      if (LAnd(UT2D, UT2E)) {
        Return(4)
      } else {
        Return(0)
      }
    } // End of Method(_S0W

    Method(_PS0, 0) {
      if (LAnd(UT2D, UT2E)) {
        DSAD (16, 0)
      }
    } // End of Method(_PS0

    Method(_PS3, 0) {
      if (LAnd(UT2D, UT2E)) {
        DSAD (16, 3)
      }
    }
  } // End Device FUR2
  #endif

  #ifdef _UAR3_
  Device(FUR3) {
//    Name(_HID,"AMDI0020")            // UART Hardware Device ID
    Method(_HID, 0, Serialized) {
      if (Lequal(UT3O, 1)) {
        Return ("AMDI0029") //return dummy HID
      }
      Return("AMDI0020")  //
    }
    Name(_UID,0x3)
    Method(_CRS, 0, Serialized) {
      Name(BUF0, ResourceTemplate(){
        Interrupt(ResourceConsumer, Edge, ActiveHigh, Exclusive) {18}
        Memory32Fixed(ReadWrite, 0xFEDCF000, 0x1000)
        Memory32Fixed(ReadWrite, 0xFEDCD000, 0x1000)
      })
      // Create pointers to the specific byte
      CreateWordField (BUF0, 0x05, IRQW)
      //Modify the IRQ
      Store (IUA3, IRQW)
      // ShiftLeft (One, And (IUA3, 0x0F), IRQW)
      Return(BUF0) // return the result
    }// end _CRS method


    Method (_STA, 0, NotSerialized) {
      Store (0x0, Local0)
      Store (FUIO (3), Local1)
      If (LGreaterEqual (TSOS, 0x70)) {
        If (LEqual (UT3E, One)) {
          If (LEqual (Local1, 0xF)) {
            Store (0xF, Local0)
          }
        }
      }
      If (LEqual (UT3I, One)) {
        Store (0x0, Local0)
      }
      Return (Local0)
    }

    Method(_S0W, 0) {
      if (LAnd(UT3D, UT3E)) {
        Return(4)
      } else {
        Return(0)
      }
    } // End of Method(_S0W

    Method(_PS0, 0) {
      if (LAnd(UT3D, UT3E)) {
        DSAD (26, 0)
      }
    } // End of Method(_PS0

    Method(_PS3, 0) {
      if (LAnd(UT3D, UT3E)) {
        DSAD (26, 3)
      }
    }
  } // End Device FUR3

  #endif

  #ifdef _UAR4_
  Device(FUR4) {
    Name(_HID,"AMDI0020")            // UART Hardware Device ID
    Name(_UID,0x4)
    Method(_CRS, 0, Serialized) {
      Name(BUF0, ResourceTemplate(){
        Interrupt(ResourceConsumer, Edge, ActiveHigh, Exclusive) {16}
        Memory32Fixed(ReadWrite, 0xFEDD1000, 0x1000)
        Memory32Fixed(ReadWrite, 0xFEDD0000, 0x1000)
      })
      // Create pointers to the specific byte
      CreateByteField (BUF0, 0x05, IRQB)
      //Modify the IRQ
      //ShiftLeft (One, And (IUA4, 0x0F), IRQW)
      Store (IUA4, IRQB)
      Return(BUF0) // return the result
    }// end _CRS method

    Method (_STA, 0, NotSerialized) {
      Store (0x0, Local0)
      If (LGreaterEqual (TSOS, 0x70)) {
        If (LEqual (UT4E, One)) {
          Store (0xF, Local0)
        }
      }
      If (LEqual (UT4I, One)) {
        Store (0x0, Local0)
      }
      Return (Local0)
    }

    Method(_S0W, 0) {
      if (LAnd(UT4D, UT4E)) {
        Return(4)
      } else {
        Return(0)
      }
    } // End of Method(_S0W

    Method(_PS0, 0) {
      if (LAnd(UT4D, UT4E)) {
        DSAD (20, 0)
      }
    } // End of Method(_PS0

    Method(_PS3, 0) {
      if (LAnd(UT4D, UT4E)) {
        DSAD (20, 3)
      }
    }
  } // End Device FUR4
  #endif

  #ifdef _I2CA_
  Device(I2CA) {
    Name(_HID,"AMDI0010")            // Hardware Device ID
    Name(_UID,0x0)
    Method(_CRS, 0, Serialized) {
      Name(BUF0, ResourceTemplate(){
        Interrupt(ResourceConsumer, Edge, ActiveHigh, Exclusive) {10}
        Memory32Fixed(ReadWrite, 0xFEDC2000, 0x1000)
      })
      // Create pointers to the specific byte
      CreateWordField (BUF0, 0x05, IRQW)
      //Modify the IRQ
      Store (IC0I, IRQW)
      Return(BUF0) // return the result
    }// end _CRS method

    Method(_STA, 0, NotSerialized) {
        If (LGreaterEqual(TSOS, 0x70)) {
          if (LEqual(IC0E, one)) { Return (0x0F)}
          Return (0x00)
        } Else {
          Return (0x00)
        }
    }

    Method(_DSM, 0x4, NotSerialized)
    {
      If(LEqual(Arg0, ToUUID("d93e4d1c-58bb-493c-a06a-605a717f9e2e")))
      {
        switch(ToInteger(Arg2))
        {
          case(0)
          {
            Return(Buffer(One){0x03})
          }
          case(1) // Get FS_SCL parameters
          {
            // Low word: FAST_SCL_LCNT - 0x00E5
            // High word: FAST_SCL_HCNT - 0x006A
            Return(Buffer(0x4){0xE5, 0x00, 0x6A, 0x00})
          }
        }
      }
      Else
      {
        Return(Buffer(One){0x00})
      }
    }

    Method(RSET,0) { SRAD (5, 200)}

    Method(_S0W, 0) {
      if (LAnd(IC0D, IC0E)) {
        Return(4)
      } else {
        Return(0)
      }
    }

    Method(_PS0, 0) {
      if (LAnd(IC0D, IC0E)) {
        DSAD (5, 0)
      }
    }

    Method(_PS3, 0) {
      if (LAnd(IC0D, IC0E)) {
        DSAD (5, 3)
      }
    }
  } // End Device I2CA
  #endif

  #ifdef _I2CB_
  Device(I2CB){
    Name(_HID,"AMDI0010")            // Hardware Device ID
    Name(_UID,0x1)
    Method(_CRS, 0, Serialized) {
      Name(BUF0, ResourceTemplate(){
        Interrupt(ResourceConsumer, Edge, ActiveHigh, Exclusive) {11}
        Memory32Fixed(ReadWrite, 0xFEDC3000, 0x1000)
      })
      // Create pointers to the specific byte
      CreateWordField (BUF0, 0x05, IRQW)
      //Modify the IRQ
      Store (IC1I, IRQW)
      Return(BUF0) // return the result
    }// end _CRS method

    Method(_STA, 0, NotSerialized) {
        If (LGreaterEqual(TSOS, 0x70)) {
          if (LEqual(IC1E, one)) { Return (0x0F)}
          Return (0x00)
        } Else {
          Return (0x00)
        }
    }

    Method(_DSM, 0x4, NotSerialized)
    {
      If(LEqual(Arg0, ToUUID("d93e4d1c-58bb-493c-a06a-605a717f9e2e")))
      {
        switch(ToInteger(Arg2))
        {
          case(0)
          {
            Return(Buffer(One){0x03})
          }
          case(1) // Get FS_SCL parameters
          {
            // Low word: FAST_SCL_LCNT - 0x00E5
            // High word: FAST_SCL_HCNT - 0x006A
            Return(Buffer(0x4){0xE5, 0x00, 0x6A, 0x00})
          }
        }
      }
      Else
      {
        Return(Buffer(One){0x00})
      }
    }

    Method(RSET,0) { SRAD (6, 200)}

    Method(_S0W, 0) {
      if (LAnd(IC1D, IC1E)) {
        Return(4)
      } else {
        Return(0)
      }
    }

    Method(_PS0, 0) {
      if (LAnd(IC1D, IC1E)) {
        DSAD (6, 0)
      }
    }

    Method(_PS3, 0) {
      if (LAnd(IC1D, IC1E)) {
        DSAD (6, 3)
      }
    }
  } // End Device I2CB
  #endif

  #ifdef _I2CC_
  Device(I2CC) {
    Name(_HID,"AMDI0010")            // Hardware Device ID
    Name(_UID,0x2)
    Method(_CRS, 0, Serialized) {
      Name(BUF0, ResourceTemplate(){
        Interrupt(ResourceConsumer, Edge, ActiveHigh, Exclusive) {4}
        Memory32Fixed(ReadWrite, 0xFEDC4000, 0x1000)
      })
      // Create pointers to the specific byte
      CreateWordField (BUF0, 0x05, IRQW)
      //Modify the IRQ
      Store (IC2I, IRQW)
      Return(BUF0) // return the result
    }// end _CRS method

    Method(_STA, 0, NotSerialized) {
        If (LGreaterEqual(TSOS, 0x70)) {
          if (LEqual(IC2E, one)) { Return (0x0F)}
          Return (0x00)
        } Else {
          Return (0x00)
        }
    }

    Method(_DSM, 0x4, NotSerialized)
    {
      If(LEqual(Arg0, ToUUID("d93e4d1c-58bb-493c-a06a-605a717f9e2e")))
      {
        switch(ToInteger(Arg2))
        {
          case(0)
          {
            Return(Buffer(One){0x03})
          }
          case(1) // Get FS_SCL parameters
          {
            // Low word: FAST_SCL_LCNT - 0x00E5
            // High word: FAST_SCL_HCNT - 0x006A
            Return(Buffer(0x4){0xE5, 0x00, 0x6A, 0x00})
          }
        }
      }
      Else
      {
        Return(Buffer(One){0x00})
      }
    }

    Method(RSET,0) { SRAD (7, 200)}

    Method(_S0W, 0) {
      if (LAnd(IC2D, IC2E)) {
        Return(4)
      } else {
        Return(0)
      }
    }

    Method(_PS0, 0) {
      if (LAnd(IC2D, IC2E)) {
        DSAD (7, 0)
      }
    }

    Method(_PS3, 0) {
      if (LAnd(IC2D, IC2E)) {
        DSAD (7, 3)
      }
    }
  } // End Device I2CC
  #endif

  #ifdef _I2CD_
  Device(I2CD) {
    Name(_HID,"AMDI0010")            // Hardware Device ID
    Name(_UID,0x3)
    Method(_CRS, 0, Serialized) {
      Name(BUF0, ResourceTemplate(){
        Interrupt(ResourceConsumer, Edge, ActiveHigh, Exclusive) {6}
        Memory32Fixed(ReadWrite, 0xFEDC5000, 0x1000)
      })
      // Create pointers to the specific byte
      CreateWordField (BUF0, 0x05, IRQW)
      //Modify the IRQ
      Store (IC3I, IRQW)
      Return(BUF0) // return the result
    }// end _CRS method

    Method(_STA, 0, NotSerialized) {
        If (LGreaterEqual(TSOS, 0x70)) {
          if (LEqual(IC3E, one)) { Return (0x0F)}
          Return (0x00)
        } Else {
          Return (0x00)
        }
    }

    Method(_DSM, 0x4, NotSerialized)
    {
      If(LEqual(Arg0, ToUUID("d93e4d1c-58bb-493c-a06a-605a717f9e2e")))
      {
        switch(ToInteger(Arg2))
        {
          case(0)
          {
            Return(Buffer(One){0x03})
          }
          case(1) // Get FS_SCL parameters
          {
            // Low word: FAST_SCL_LCNT - 0x00E5
            // High word: FAST_SCL_HCNT - 0x006A
            Return(Buffer(0x4){0xE5, 0x00, 0x6A, 0x00})
          }
        }
      }
      Else
      {
        Return(Buffer(One){0x00})
      }
    }

    Method(RSET,0) { SRAD (8, 200)}

    Method(_S0W, 0) {
      if (LAnd(IC3D, IC3E)) {
        Return(4)
      } else {
        Return(0)
      }
    }

    Method(_PS0, 0) {
      if (LAnd(IC3D, IC3E)) {
        DSAD (8, 0)
      }
    }

    Method(_PS3, 0) {
      if (LAnd(IC3D, IC3E)) {
        DSAD (8, 3)
      }
    }
  } // End Device I2CD
  #endif
  Name(I3ID,"AMDI0015")             // AMD I3C Driver ID
  Name(I2ID,"AMDI0016")
  Name(HCID,"AMDI5017")


  #ifdef _I2CA_
  Device(I3CA) {
    Method(_HID, 0, Serialized) {
      if (Lequal(I30M, 0)) {
        if (CondRefOf(HCIB)){
          Return (HCID)
        }else{
          Return (I3ID)
        }
      } else {
          Return (I2ID)
      }
    }
    Name(_UID,0x0)
    Method(_CRS, 0, Serialized) {
      Name(BUF0, ResourceTemplate(){
        Interrupt(ResourceConsumer, Edge, ActiveHigh, Exclusive) {10}
      Memory32Fixed(ReadWrite, 0xFEDD2000, 0x1000)
      })
      // Create pointers to the specific byte
      CreateWordField (BUF0, 0x05, IRQW)
      //Modify the IRQ
      Store (IC0I, IRQW)
      Return(BUF0) // return the result
    }// end _CRS method

    Method(_STA, 0, NotSerialized) {
        If (LGreaterEqual(TSOS, 0x70)) {
          if (LEqual(I30E, one)) { Return (0x0F)}
          Return (0x00)
        } Else {
          Return (0x00)
        }
    }

    Method(_DSD, 0, Serialized){
      if (CondRefOf(HCIB)){
        Return(Package() {
          ToUUID("daffd814-6eba-4d8c-8a91-bc9bbf4aa301"),
          Package () {
          "mipi-i3c-sw-interface-revision", 0x10000, // 1.0
          },
          ToUUID("dbb8e3e6-5886-4ba6-8795-1319f52a966b"), // Hierarchical Extension
          Package () {
          "mipi-i3c-ctrlr-0-subproperties", CTR0,
          }
        })
      } // End _DSD
    }

    Method(CTR0, 0, Serialized){
      if (CondRefOf(HCIB)){
        Return(Package() {
          ToUUID("daffd814-6eba-4d8c-8a91-bc9bbf4aa301"),
          Package () {
          "mipi-i3c-sw-interface-revision", 0x10000, // 1.0
          }
        })
      } // End CTRLR0
    }

    Method(RSET,0) { SRAD (21, 200)}

    Method(_S0W, 0) {
      if (LAnd(I30D, I30E)) {
        Return(4)
      } else {
        Return(0)
      }
    }

    Method(_PS0, 0) {
      if (LAnd(I30D, I30E)) {
        DSAD (21, 0)
      }
    }

    Method(_PS3, 0) {
      if (LAnd(I30D, I30E)) {
        DSAD (21, 3)
      }
    }
  } // End Device I3CA
  #endif

  #ifdef _I2CB_
  Device(I3CB) {

    Method(_HID, 0, Serialized) {
      if (Lequal(I31M, 0)) {
        if (CondRefOf(HCIB)){
          Return (HCID)
        }else{
          Return (I3ID)
        }
      } else {
          Return (I2ID)
      }
    }
    Name(_UID,0x1)
    Method(_CRS, 0, Serialized) {
      Name(BUF0, ResourceTemplate(){
        Interrupt(ResourceConsumer, Edge, ActiveHigh, Exclusive) {11}
      Memory32Fixed(ReadWrite, 0xFEDD3000, 0x1000)
      })
      // Create pointers to the specific byte
      CreateWordField (BUF0, 0x05, IRQW)
      //Modify the IRQ
      Store (IC1I, IRQW)
      Return(BUF0) // return the result
    }// end _CRS method

    Method(_STA, 0, NotSerialized) {
        If (LGreaterEqual(TSOS, 0x70)) {
          if (LEqual(I31E, one)) { Return (0x0F)}
          Return (0x00)
        } Else {
          Return (0x00)
        }
    }

    Method(_DSD, 0, Serialized){
      if (CondRefOf(HCIB)){
        Return(Package() {
          ToUUID("daffd814-6eba-4d8c-8a91-bc9bbf4aa301"),
          Package () {
          "mipi-i3c-sw-interface-revision", 0x10000, // 1.0
          },
          ToUUID("dbb8e3e6-5886-4ba6-8795-1319f52a966b"), // Hierarchical Extension
          Package () {
          "mipi-i3c-ctrlr-0-subproperties", CTR0,
          }
        })
      } // End _DSD
    }

    Method(CTR0, 0, Serialized){
      if (CondRefOf(HCIB)){
        Return(Package() {
          ToUUID("daffd814-6eba-4d8c-8a91-bc9bbf4aa301"),
          Package () {
          "mipi-i3c-sw-interface-revision", 0x10000, // 1.0
          }
        })
      } // End CTRLR0
    }

    Method(RSET,0) { SRAD (13, 200)}

    Method(_S0W, 0) {
      if (LAnd(I31D, I31E)) {
        Return(4)
      } else {
        Return(0)
      }
    }

    Method(_PS0, 0) {
      if (LAnd(I31D, I31E)) {
        DSAD (13, 0)
      }
    }

    Method(_PS3, 0) {
      if (LAnd(I31D, I31E)) {
        DSAD (13, 3)
      }
    }
  } // End Device I3CB
  #endif

  #ifdef _I2CC_
  Device(I3CC) {
    Method(_HID, 0, Serialized) {
      if (Lequal(I32M, 0)) {
        if (CondRefOf(HCIB)){
          Return (HCID)
        }else{
          Return (I3ID)
        }
      } else {
          Return (I2ID)
      }
    }
    Name(_UID,0x2)
    Method(_CRS, 0, Serialized) {
      Name(BUF0, ResourceTemplate(){
        Interrupt(ResourceConsumer, Edge, ActiveHigh, Exclusive) {4}
      Memory32Fixed(ReadWrite, 0xFEDD4000, 0x1000)
      })
      // Create pointers to the specific byte
      CreateWordField (BUF0, 0x05, IRQW)
      //Modify the IRQ
      Store (IC2I, IRQW)
      Return(BUF0) // return the result
    }// end _CRS method

    Method(_STA, 0, NotSerialized) {
        If (LGreaterEqual(TSOS, 0x70)) {
          if (LEqual(I32E, one)) { Return (0x0F)}
          Return (0x00)
        } Else {
          Return (0x00)
        }
    }

    Method(_DSD, 0, Serialized){
      if (CondRefOf(HCIB)){
        Return(Package() {
          ToUUID("daffd814-6eba-4d8c-8a91-bc9bbf4aa301"),
          Package () {
          "mipi-i3c-sw-interface-revision", 0x10000, // 1.0
          },
          ToUUID("dbb8e3e6-5886-4ba6-8795-1319f52a966b"), // Hierarchical Extension
          Package () {
          "mipi-i3c-ctrlr-0-subproperties", CTR0,
          }
        })
      } // End _DSD
    }

    Method(CTR0, 0, Serialized){
      if (CondRefOf(HCIB)){
        Return(Package() {
          ToUUID("daffd814-6eba-4d8c-8a91-bc9bbf4aa301"),
          Package () {
          "mipi-i3c-sw-interface-revision", 0x10000, // 1.0
          }
        })
      } // End CTRLR0
    }

    Method(RSET,0) { SRAD (14, 200)}

    Method(_S0W, 0) {
      if (LAnd(I32D, I32E)) {
        Return(4)
      } else {
        Return(0)
      }
    }

    Method(_PS0, 0) {
      if (LAnd(I32D, I32E)) {
        DSAD (14, 0)
      }
    }

    Method(_PS3, 0) {
      if (LAnd(I32D, I32E)) {
        DSAD (14, 3)
      }
    }
  } // End Device I3CC
  #endif

  #ifdef _I2CD_
  Device(I3CD) {
    Method(_HID, 0, Serialized) {
      if (Lequal(I33M, 0)) {
        if (CondRefOf(HCIB)){
          Return (HCID)
        }else{
          Return (I3ID)
        }
      } else {
          Return (I2ID)
      }
    }
    Name(_UID,0x3)
    Method(_CRS, 0, Serialized) {
      Name(BUF0, ResourceTemplate(){
        Interrupt(ResourceConsumer, Edge, ActiveHigh, Exclusive) {6}
      Memory32Fixed(ReadWrite, 0xFEDD6000, 0x1000)
      })
      // Create pointers to the specific byte
      CreateWordField (BUF0, 0x05, IRQW)
      //Modify the IRQ
      Store (IC3I, IRQW)
      Return(BUF0) // return the result
    }// end _CRS method

    Method(_STA, 0, NotSerialized) {
        If (LGreaterEqual(TSOS, 0x70)) {
          if (LEqual(I33E, one)) { Return (0x0F)}
          Return (0x00)
        } Else {
          Return (0x00)
        }
    }

    Method(_DSD, 0, Serialized){
      if (CondRefOf(HCIB)){
        Return(Package() {
          ToUUID("daffd814-6eba-4d8c-8a91-bc9bbf4aa301"),
          Package () {
          "mipi-i3c-sw-interface-revision", 0x10000, // 1.0
          },
          ToUUID("dbb8e3e6-5886-4ba6-8795-1319f52a966b"), // Hierarchical Extension
          Package () {
          "mipi-i3c-ctrlr-0-subproperties", CTR0,
          }
        })
      } // End _DSD
    }

    Method(CTR0, 0, Serialized){
      if (CondRefOf(HCIB)){
        Return(Package() {
          ToUUID("daffd814-6eba-4d8c-8a91-bc9bbf4aa301"),
          Package () {
          "mipi-i3c-sw-interface-revision", 0x10000, // 1.0
          }
        })
      } // End CTRLR0
    }

    Method(RSET,0) { SRAD (15, 200)}

    Method(_S0W, 0) {
      if (LAnd(I33D, I33E)) {
        Return(4)
      } else {
        Return(0)
      }
    }

    Method(_PS0, 0) {
      if (LAnd(I33D, I33E)) {
        DSAD (15, 0)
      }
    }

    Method(_PS3, 0) {
      if (LAnd(I33D, I33E)) {
        DSAD (15, 3)
      }
    }
  } // End Device I3CD
  #endif
  Method (SHAR, 1, NotSerialized) {
    if (LEqual (Arg0, 0)) {
      #ifdef _UAR1_
      return (\_SB.FUR1._STA)
      #else
      return (0)
      #endif
    } elseif (LEqual (Arg0, 1)) {
      #ifdef _UAR0_
      return (\_SB.FUR0._STA)
      #else
      return (0)
      #endif
    } elseif (LEqual (Arg0, 2)) {
      #ifdef _UAR3_
      return (\_SB.FUR3._STA)
      #else
      return (0)
      #endif
    } elseif (LEqual (Arg0, 3)) {
      #ifdef _UAR2_
      return (\_SB.FUR2._STA)
      #else
      return (0)
      #endif
    } else {
      // Return 3 should never be run, it avoids ASL compiler warning.
      return (3)
    }
  } // End of Method (FRUI

} // End of Scope(\_SB)

Scope(\_SB.PCI0) {
  #ifdef _UAR0_
  Device(UAR1) {  // COM Port
    Name(_HID, EISAID("PNP0500"))
    Name(_UID, 0x1)
  //  Name(_CID, EISAID("PNP0500"))
  //  Name(_HID, EISAID("PNP0501"))
  //  Name(_CID, EISAID("PNP0500"))
    Name(_DDN, "COM1")
    //*****************************************************
    // Method _STA:  Return Status
    //*****************************************************
    Method (_STA, 0, NotSerialized) { // Return Status of the UART
      Store (0x0, Local0)
      Store (FUIO (0), Local1)
      If (LNotEqual (Local1, 0xF)) {
        Store (0xF, Local0)
      }
      If (LEqual (UL0I, One)) {
        Store (0x0, Local0)
      }
      Return (Local0)
    } // end of Method _STA
    //*****************************************************
    //  Method _CRS:  Return Current Resource Settings
    //*****************************************************
    Method (_CRS, 0, NotSerialized) {
      Name (BUF0, ResourceTemplate() {
        IO (Decode16, 0x2E8, 0x2E8, 0x01, 0x08)
        IRQNoFlags() {3}
      })
      //
      // Create some ByteFields in the Buffer in order to
      // permit saving values into the data portions of
      // each of the descriptors above.
      //
      CreateByteField (BUF0, 0x02, IOLO) // IO Port Low
      CreateByteField (BUF0, 0x03, IOHI) // IO Port Hi
      CreateByteField (BUF0, 0x04, IORL) // IO Port Low
      CreateByteField (BUF0, 0x05, IORH) // IO Port High
      CreateWordField (BUF0, 0x09, IRQL) // IRQ
      //
      // Get the IO setting from the chip, and copy it
      // to both the min & max for the IO descriptor.
      //
      Store (FUIO(0), Local0)
      Switch (ToInteger(Local0))
      {
        Case (0)  { Store (0xE8, IOLO) Store (0x02, IOHI) Store (0xE8, IORL) Store (0x02, IORH) }
        Case (1)  { Store (0xF8, IOLO) Store (0x02, IOHI) Store (0xF8, IORL) Store (0x02, IORH) }
        Case (2)  { Store (0xE8, IOLO) Store (0x03, IOHI) Store (0xE8, IORL) Store (0x03, IORH) }
        Case (3)  { Store (0xF8, IOLO) Store (0x03, IOHI) Store (0xF8, IORL) Store (0x03, IORH) }
      }
      //
      // Get the IRQ setting from the chip, and shift
      // it into the IRQ descriptor word (bitwise).
      //
      ShiftLeft (One, And (FRUI (0), 0x0F), IRQL)
      Return(BUF0) // return the result
    } // end _CRS Method
  } // end of Device UART1
  #endif

  #ifdef _UAR1_
  Device(UAR2) {  // COM Port
    Name(_HID, EISAID("PNP0500"))
    Name(_UID, 0x2)
  //  Name(_CID, EISAID("PNP0500"))
  //  Name(_HID, EISAID("PNP0501"))
  //  Name(_CID, EISAID("PNP0500"))
    Name(_DDN, "COM2")
    //*****************************************************
    // Method _STA:  Return Status
    //*****************************************************
    Method (_STA, 0, NotSerialized) { // Return Status of the UART
      Store (0x0, Local0)
      Store (FUIO (1), Local1)
      If (LNotEqual (Local1, 0xF)) {
        Store (0xF, Local0)
      }
      If (LEqual (UL1I, One)) {
        Store (0x0, Local0)
      }
      Return (Local0)
    } // end of Method _STA
    //*****************************************************
    //  Method _CRS:  Return Current Resource Settings
    //*****************************************************
    Method (_CRS, 0, NotSerialized) {
      Name (BUF0, ResourceTemplate() {
        IO (Decode16, 0x2F8, 0x2F8, 0x01, 0x08)
        IRQNoFlags() {4}
      })
      //
      // Create some ByteFields in the Buffer in order to
      // permit saving values into the data portions of
      // each of the descriptors above.
      //
      CreateByteField (BUF0, 0x02, IOLO) // IO Port Low
      CreateByteField (BUF0, 0x03, IOHI) // IO Port Hi
      CreateByteField (BUF0, 0x04, IORL) // IO Port Low
      CreateByteField (BUF0, 0x05, IORH) // IO Port High
      CreateWordField (BUF0, 0x09, IRQL) // IRQ
      //
      // Get the IO setting from the chip, and copy it
      // to both the min & max for the IO descriptor.
      //
      Store (FUIO(1), Local0)
      Switch (ToInteger(Local0))
      {
        Case (0)  { Store (0xE8, IOLO) Store (0x02, IOHI) Store (0xE8, IORL) Store (0x02, IORH) }
        Case (1)  { Store (0xF8, IOLO) Store (0x02, IOHI) Store (0xF8, IORL) Store (0x02, IORH) }
        Case (2)  { Store (0xE8, IOLO) Store (0x03, IOHI) Store (0xE8, IORL) Store (0x03, IORH) }
        Case (3)  { Store (0xF8, IOLO) Store (0x03, IOHI) Store (0xF8, IORL) Store (0x03, IORH) }
      }
      //
      // Get the IRQ setting from the chip, and shift
      // it into the IRQ descriptor word (bitwise).
      //
      ShiftLeft (One, And (FRUI (1), 0x0F), IRQL)
      Return(BUF0) // return the result
    } // end _CRS Method
  } // end of Device UART2
  #endif

  #ifdef _UAR2_
  Device(UAR3) {  // COM Port
    Name(_HID, EISAID("PNP0500"))
    Name(_UID, 0x3)
  //  Name(_CID, EISAID("PNP0500"))
  //  Name(_HID, EISAID("PNP0501"))
  //  Name(_CID, EISAID("PNP0500"))
    Name(_DDN, "COM3")
    //*****************************************************
    // Method _STA:  Return Status
    //*****************************************************
    Method (_STA, 0, NotSerialized) { // Return Status of the UART
      Store (0x0, Local0)
      Store (FUIO (2), Local1)
      If (LNotEqual (Local1, 0xF)) {
        Store (0xF, Local0)
      }
      If (LEqual (UL2I, One)) {
        Store (0x0, Local0)
      }
      Return (Local0)
    } // end of Method _STA
    //*****************************************************
    //  Method _CRS:  Return Current Resource Settings
    //*****************************************************
    Method (_CRS, 0, NotSerialized) {
      Name (BUF0, ResourceTemplate() {
        IO (Decode16, 0x3E8, 0x3E8, 0x01, 0x08)
        IRQNoFlags() {3}
      })
      //
      // Create some ByteFields in the Buffer in order to
      // permit saving values into the data portions of
      // each of the descriptors above.
      //
      CreateByteField (BUF0, 0x02, IOLO) // IO Port Low
      CreateByteField (BUF0, 0x03, IOHI) // IO Port Hi
      CreateByteField (BUF0, 0x04, IORL) // IO Port Low
      CreateByteField (BUF0, 0x05, IORH) // IO Port High
      CreateWordField (BUF0, 0x09, IRQL) // IRQ
      //
      // Get the IO setting from the chip, and copy it
      // to both the min & max for the IO descriptor.
      //
      Store (FUIO(2), Local0)
      Switch (ToInteger(Local0))
      {
        Case (0)  { Store (0xE8, IOLO) Store (0x02, IOHI) Store (0xE8, IORL) Store (0x02, IORH) }
        Case (1)  { Store (0xF8, IOLO) Store (0x02, IOHI) Store (0xF8, IORL) Store (0x02, IORH) }
        Case (2)  { Store (0xE8, IOLO) Store (0x03, IOHI) Store (0xE8, IORL) Store (0x03, IORH) }
        Case (3)  { Store (0xF8, IOLO) Store (0x03, IOHI) Store (0xF8, IORL) Store (0x03, IORH) }
      }
      //
      // Get the IRQ setting from the chip, and shift
      // it into the IRQ descriptor word (bitwise).
      //
      ShiftLeft (One, And (FRUI (2), 0x0F), IRQL)
      Return(BUF0) // return the result
    } // end _CRS Method
  } // end of Device UART3
  #endif

  #ifdef _UAR3_
  Device(UAR4) {  // COM Port
    Name(_HID, EISAID("PNP0500"))
    Name(_UID, 0x4)
  //  Name(_CID, EISAID("PNP0500"))
  //  Name(_HID, EISAID("PNP0501"))
  //  Name(_CID, EISAID("PNP0500"))
    Name(_DDN, "COM4")
    //*****************************************************
    // Method _STA:  Return Status
    //*****************************************************
    Method (_STA, 0, NotSerialized) { // Return Status of the UART
      Store (0x0, Local0)
      Store (FUIO (3), Local1)
      If (LNotEqual (Local1, 0xF)) {
        Store (0xF, Local0)
      }
      If (LEqual (UL3I, One)) {
        Store (0x0, Local0)
      }
      Return (Local0)
    } // end of Method _STA
    //*****************************************************
    //  Method _CRS:  Return Current Resource Settings
    //*****************************************************
    Method (_CRS, 0, NotSerialized) {
      Name (BUF0, ResourceTemplate() {
        IO (Decode16, 0x3F8, 0x3F8, 0x01, 0x08)
        IRQNoFlags() {4}
      })
      //
      // Create some ByteFields in the Buffer in order to
      // permit saving values into the data portions of
      // each of the descriptors above.
      //
      CreateByteField (BUF0, 0x02, IOLO) // IO Port Low
      CreateByteField (BUF0, 0x03, IOHI) // IO Port Hi
      CreateByteField (BUF0, 0x04, IORL) // IO Port Low
      CreateByteField (BUF0, 0x05, IORH) // IO Port High
      CreateWordField (BUF0, 0x09, IRQL) // IRQ
      //
      // Get the IO setting from the chip, and copy it
      // to both the min & max for the IO descriptor.
      //
      Store (FUIO(3), Local0)
      Switch (ToInteger(Local0))
      {
        Case (0)  { Store (0xE8, IOLO) Store (0x02, IOHI) Store (0xE8, IORL) Store (0x02, IORH) }
        Case (1)  { Store (0xF8, IOLO) Store (0x02, IOHI) Store (0xF8, IORL) Store (0x02, IORH) }
        Case (2)  { Store (0xE8, IOLO) Store (0x03, IOHI) Store (0xE8, IORL) Store (0x03, IORH) }
        Case (3)  { Store (0xF8, IOLO) Store (0x03, IOHI) Store (0xF8, IORL) Store (0x03, IORH) }
      }
      //
      // Get the IRQ setting from the chip, and shift
      // it into the IRQ descriptor word (bitwise).
      //
      ShiftLeft (One, And (FRUI (3), 0x0F), IRQL)
      Return(BUF0) // return the result
    } // end _CRS Method
  } // end of Device UART4
  #endif
} // end of Scope(\_SB.PCI0)


