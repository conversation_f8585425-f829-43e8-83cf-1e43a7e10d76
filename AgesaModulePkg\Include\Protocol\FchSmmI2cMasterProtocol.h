/*****************************************************************************
 * Copyright (C) 2008-2024 Advanced Micro Devices, Inc. All rights reserved.
 *
*****************************************************************************
*/
/* $NoKeywords:$ */
/**
 * @file
 *
 * FCH DXE
 *
 *
 * @xrefitem bom "File Content Label" "Release Content"
 * @e project:      AGESA
 * @e sub-project   FCH SMM
 * @e \$Revision: 309090 $   @e \$Date: 2014-12-09 10:28:05 -0800 (Tue, 09 Dec 2014) $
 *
 */

#ifndef __FCH_SMM_I2C_MASTER_PROTOCOL_H__
#define __FCH_SMM_I2C_MASTER_PROTOCOL_H__

#include <Pi/PiI2c.h>

#pragma pack (push, 1)


extern EFI_GUID  gAmdFchSmmI2cMasterProtocolGuid;
typedef struct _FCH_SMM_I2C_MASTER_PROTOCOL FCH_SMM_I2C_MASTER_PROTOCOL;


#define  FCH_SMM_I2C_MASTER_PROTOCOL_REV    0x0001

/**
  Set the frequency for the I2C clock line.


  The software and controller do a best case effort of using the specified
  frequency for the I2C bus.  If the frequency does not match exactly then
  the I2C master protocol selects the next lower frequency to avoid
  exceeding the operating conditions for any of the I2C devices on the bus.
  For example if 400 KHz was specified and the controller's divide network
  only supports 402 KHz or 398 KHz then the I2C master protocol selects 398
  KHz.  If there are not lower frequencies available, then return
  EFI_UNSUPPORTED.

  @param[in] This           Pointer to an EFI_I2C_MASTER_PROTOCOL structure
  @param[in] BusClockHertz  Pointer to the requested I2C bus clock frequency
                            in Hertz.  Upon return this value contains the
                            actual frequency in use by the I2C controller.

  @retval EFI_SUCCESS           The bus frequency was set successfully.
  @retval EFI_ALREADY_STARTED   The controller is busy with another transaction.
  @retval EFI_INVALID_PARAMETER BusClockHertz is NULL
  @retval EFI_UNSUPPORTED       The controller does not support this frequency.

**/
typedef
EFI_STATUS
(EFIAPI *FCH_SMM_I2C_MASTER_PROTOCOL_SET_BUS_FREQUENCY)(
  IN CONST FCH_SMM_I2C_MASTER_PROTOCOL   *This,
  IN OUT UINTN                       *BusClockHertz
  );

/**
  Reset the I2C controller and configure it for use

  This routine must be called at or below TPL_NOTIFY.

  The I2C controller is reset.  The caller must call SetBusFrequench() after
  calling Reset().

  @param[in]     This       Pointer to an EFI_I2C_MASTER_PROTOCOL structure.

  @retval EFI_SUCCESS         The reset completed successfully.
  @retval EFI_ALREADY_STARTED The controller is busy with another transaction.
  @retval EFI_DEVICE_ERROR    The reset operation failed.

**/
typedef
EFI_STATUS
(EFIAPI *EFI_I2C_MASTER_PROTOCOL_RESET)(
  IN CONST FCH_SMM_I2C_MASTER_PROTOCOL *This
  );

/**
  Start an I2C transaction on the host controller.

  @param[in] This           Pointer to an FCH_SMM_I2C_MASTER_PROTOCOL structure.
  @param[in] SlaveAddress   Address of the device on the I2C bus.  Set the
                            I2C_ADDRESSING_10_BIT when using 10-bit addresses,
                            clear this bit for 7-bit addressing.  Bits 0-6
                            are used for 7-bit I2C slave addresses and bits
                            0-9 are used for 10-bit I2C slave addresses.
  @param[in] RequestPacket  Pointer to an EFI_I2C_REQUEST_PACKET
                            structure describing the I2C transaction.

  @retval EFI_SUCCESS           The asynchronous transaction was successfully
                                started when Event is not NULL.
  @retval EFI_SUCCESS           The transaction completed successfully when
                                Event is NULL.
  @retval EFI_ALREADY_STARTED   The controller is busy with another transaction.
  @retval EFI_BAD_BUFFER_SIZE   The RequestPacket->LengthInBytes value is too
                                large.
  @retval EFI_DEVICE_ERROR      There was an I2C error (NACK) during the
                                transaction.
  @retval EFI_INVALID_PARAMETER RequestPacket is NULL
  @retval EFI_NOT_FOUND         Reserved bit set in the SlaveAddress parameter
  @retval EFI_NO_RESPONSE       The I2C device is not responding to the slave
                                address.  EFI_DEVICE_ERROR will be returned if
                                the controller cannot distinguish when the NACK
                                occurred.
  @retval EFI_OUT_OF_RESOURCES  Insufficient memory for I2C transaction
  @retval EFI_UNSUPPORTED       The controller does not support the requested
                                transaction.

**/
typedef
EFI_STATUS
(EFIAPI *FCH_SMM_I2C_MASTER_PROTOCOL_START_REQUEST)(
  IN CONST FCH_SMM_I2C_MASTER_PROTOCOL *This,
  IN UINTN                             SlaveAddress,
  IN EFI_I2C_REQUEST_PACKET           *RequestPacket
  );

///
/// FCH SMM I2C master mode protocol
///
/// This protocol manipulates the I2C host controller to perform transactions as a
/// master on the I2C bus using the current state of any switches or multiplexers
/// in the I2C bus.
///
struct _FCH_SMM_I2C_MASTER_PROTOCOL {
  UINT16                                           Revision;
  ///
  /// Set the clock frequency for the I2C bus.
  ///
  FCH_SMM_I2C_MASTER_PROTOCOL_SET_BUS_FREQUENCY    SetBusFrequency;
  ///
  /// Start an I2C transaction in master mode on the host controller.
  ///
  FCH_SMM_I2C_MASTER_PROTOCOL_START_REQUEST        StartRequest;
  // I2C controller index (0'based)
  UINT32                                           ControllerNum;
};

#pragma pack (pop)

#endif //  __I2C_MASTER_H__

