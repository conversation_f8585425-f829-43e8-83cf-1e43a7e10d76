#;*****************************************************************************
#;
#; Copyright (C) 2024 Advanced Micro Devices, Inc. All rights reserved.
#;
#;******************************************************************************

[Defines]
  INF_VERSION                    = 0x00010006
  BASE_NAME                      = ApcbLibV3Pei
  FILE_GUID                      = EEA4E007-E408-4daa-82BD-4C52E7058753
  MODULE_TYPE                    = PEIM
  VERSION_STRING                 = 1.0
  LIBRARY_CLASS                  = ApcbLibV3Pei|PEIM
  CONSTRUCTOR                    = ApcbLibV3PeiConstructor

[Sources.common]
  ApcbLibV3Pei.c
  ApcbLibV3PeiServices.c

[Packages]
  MdePkg/MdePkg.dec
  MdeModulePkg/MdeModulePkg.dec
  AgesaPkg/AgesaPkg.dec
  AgesaModulePkg/AgesaCommonModulePkg.dec
  AgesaModulePkg/AgesaModuleMemPkg.dec
  AgesaModulePkg/AgesaModulePspPkg.dec

[LibraryClasses]
  PcdLib
  BaseMemoryLib
  PeiServicesLib
  AmdBaseLib
  AmdSocBaseLib
  AmdPspBaseLibV2
  AmdDirectoryBaseLib
  AmdPspApobLib
  ApobCommonServiceLib
  AmdPspFwImageHeaderLib
  ApcbCoreLib
  IdsLib

[Guids]


[Ppis]


[Pcd]
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdApcbPeiVariableStructAddress
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdApcbPriorityLevelAdmin
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdApcbPriorityLevelDebug
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdApcbPriorityLevelNormal
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdApcbRecoveryStrategy

