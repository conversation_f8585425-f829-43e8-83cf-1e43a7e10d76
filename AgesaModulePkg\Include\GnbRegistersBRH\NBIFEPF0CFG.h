/*****************************************************************************
 *
 * Copyright (C) 2008-2024 Advanced Micro Devices, Inc. All rights reserved.
 *
 *******************************************************************************
 **/

#ifndef _NBIFEPF0CFG_H_
#define _NBIFEPF0CFG_H_


/***********************************************************
* Register Name : ADAPTER_ID
************************************************************/

#define ADAPTER_ID_SUBSYSTEM_VENDOR_ID_OFFSET                  0
#define ADAPTER_ID_SUBSYSTEM_VENDOR_ID_MASK                    0xffff

#define ADAPTER_ID_SUBSYSTEM_ID_OFFSET                         16
#define ADAPTER_ID_SUBSYSTEM_ID_MASK                           0xffff0000

typedef union {
  struct {
    UINT32                                 SUBSYSTEM_VENDOR_ID:16;
    UINT32                                        SUBSYSTEM_ID:16;
  } Field;
  UINT32 Value;
} ADAPTER_ID_STRUCT;

#define PCICFG_NBIFEPF0CFG_ADAPTER_ID_OFFSET                        0x2c
#define SMN_DEV0_FUNC0_NBIF0NBIO0_ADAPTER_ID_ADDRESS                  0x1014002cUL
#define SMN_DEV0_FUNC0_NBIF0NBIO1_ADAPTER_ID_ADDRESS                  0x1034002cUL
#define SMN_DEV0_FUNC0_NBIF1NBIO0_ADAPTER_ID_ADDRESS                  0x1024002cUL
#define SMN_DEV0_FUNC0_NBIF1NBIO1_ADAPTER_ID_ADDRESS                  0x1044002cUL
#define SMN_DEV0_FUNC0_NBIF2NBIO0_ADAPTER_ID_ADDRESS                  0x1054002cUL
#define SMN_DEV0_FUNC0_NBIF2NBIO1_ADAPTER_ID_ADDRESS                  0x1074002cUL
#define SMN_DEV1_FUNC0_NBIF0NBIO0_ADAPTER_ID_ADDRESS                  0x1014802cUL
#define SMN_DEV1_FUNC0_NBIF0NBIO1_ADAPTER_ID_ADDRESS                  0x1034802cUL
#define SMN_DEV1_FUNC0_NBIF1NBIO0_ADAPTER_ID_ADDRESS                  0x1024802cUL
#define SMN_DEV1_FUNC0_NBIF1NBIO1_ADAPTER_ID_ADDRESS                  0x1044802cUL


/***********************************************************
* Register Name : ADAPTER_ID_W
************************************************************/

#define ADAPTER_ID_W_SUBSYSTEM_VENDOR_ID_OFFSET                0
#define ADAPTER_ID_W_SUBSYSTEM_VENDOR_ID_MASK                  0xffff

#define ADAPTER_ID_W_SUBSYSTEM_ID_OFFSET                       16
#define ADAPTER_ID_W_SUBSYSTEM_ID_MASK                         0xffff0000

typedef union {
  struct {
    UINT32                                 SUBSYSTEM_VENDOR_ID:16;
    UINT32                                        SUBSYSTEM_ID:16;
  } Field;
  UINT32 Value;
} ADAPTER_ID_W_STRUCT;

#define PCICFG_NBIFEPF0CFG_ADAPTER_ID_W_OFFSET                      0x4c
#define SMN_DEV0_FUNC0_NBIF0NBIO0_ADAPTER_ID_W_ADDRESS                0x1014004cUL
#define SMN_DEV0_FUNC0_NBIF0NBIO1_ADAPTER_ID_W_ADDRESS                0x1034004cUL
#define SMN_DEV0_FUNC0_NBIF1NBIO0_ADAPTER_ID_W_ADDRESS                0x1024004cUL
#define SMN_DEV0_FUNC0_NBIF1NBIO1_ADAPTER_ID_W_ADDRESS                0x1044004cUL
#define SMN_DEV0_FUNC0_NBIF2NBIO0_ADAPTER_ID_W_ADDRESS                0x1054004cUL
#define SMN_DEV0_FUNC0_NBIF2NBIO1_ADAPTER_ID_W_ADDRESS                0x1074004cUL
#define SMN_DEV1_FUNC0_NBIF0NBIO0_ADAPTER_ID_W_ADDRESS                0x1014804cUL
#define SMN_DEV1_FUNC0_NBIF0NBIO1_ADAPTER_ID_W_ADDRESS                0x1034804cUL
#define SMN_DEV1_FUNC0_NBIF1NBIO0_ADAPTER_ID_W_ADDRESS                0x1024804cUL
#define SMN_DEV1_FUNC0_NBIF1NBIO1_ADAPTER_ID_W_ADDRESS                0x1044804cUL


/***********************************************************
* Register Name : AP_CAP
************************************************************/

#define AP_CAP_AP_COUNT_OFFSET                                 0
#define AP_CAP_AP_COUNT_MASK                                   0xff

#define AP_CAP_AP_SELECTIVE_ENABLE_SUPPORTED_OFFSET            8
#define AP_CAP_AP_SELECTIVE_ENABLE_SUPPORTED_MASK              0x100

#define AP_CAP_Reserved_31_9_OFFSET                            9
#define AP_CAP_Reserved_31_9_MASK                              0xfffffe00

typedef union {
  struct {
    UINT32                                            AP_COUNT:8;
    UINT32                       AP_SELECTIVE_ENABLE_SUPPORTED:1;
    UINT32                                       Reserved_31_9:23;
  } Field;
  UINT32 Value;
} AP_CAP_STRUCT;

#define PCICFG_NBIFEPF0CFG_AP_CAP_OFFSET                            0x534
#define SMN_DEV0_FUNC0_NBIF0NBIO0_AP_CAP_ADDRESS                      0x10140534UL
#define SMN_DEV0_FUNC0_NBIF0NBIO1_AP_CAP_ADDRESS                      0x10340534UL
#define SMN_DEV0_FUNC0_NBIF1NBIO0_AP_CAP_ADDRESS                      0x10240534UL
#define SMN_DEV0_FUNC0_NBIF1NBIO1_AP_CAP_ADDRESS                      0x10440534UL
#define SMN_DEV0_FUNC0_NBIF2NBIO0_AP_CAP_ADDRESS                      0x10540534UL
#define SMN_DEV0_FUNC0_NBIF2NBIO1_AP_CAP_ADDRESS                      0x10740534UL
#define SMN_DEV1_FUNC0_NBIF0NBIO0_AP_CAP_ADDRESS                      0x10148534UL
#define SMN_DEV1_FUNC0_NBIF0NBIO1_AP_CAP_ADDRESS                      0x10348534UL
#define SMN_DEV1_FUNC0_NBIF1NBIO0_AP_CAP_ADDRESS                      0x10248534UL
#define SMN_DEV1_FUNC0_NBIF1NBIO1_AP_CAP_ADDRESS                      0x10448534UL


/***********************************************************
* Register Name : AP_CNTL
************************************************************/

#define AP_CNTL_AP_INDEX_SELECT_OFFSET                         0
#define AP_CNTL_AP_INDEX_SELECT_MASK                           0xff

#define AP_CNTL_AP_NEGOTIATION_GLOBAL_EN_OFFSET                8
#define AP_CNTL_AP_NEGOTIATION_GLOBAL_EN_MASK                  0x100

#define AP_CNTL_Reserved_31_9_OFFSET                           9
#define AP_CNTL_Reserved_31_9_MASK                             0xfffffe00

typedef union {
  struct {
    UINT32                                     AP_INDEX_SELECT:8;
    UINT32                            AP_NEGOTIATION_GLOBAL_EN:1;
    UINT32                                       Reserved_31_9:23;
  } Field;
  UINT32 Value;
} AP_CNTL_STRUCT;

#define PCICFG_NBIFEPF0CFG_AP_CNTL_OFFSET                           0x538
#define SMN_DEV0_FUNC0_NBIF0NBIO0_AP_CNTL_ADDRESS                     0x10140538UL
#define SMN_DEV0_FUNC0_NBIF0NBIO1_AP_CNTL_ADDRESS                     0x10340538UL
#define SMN_DEV0_FUNC0_NBIF1NBIO0_AP_CNTL_ADDRESS                     0x10240538UL
#define SMN_DEV0_FUNC0_NBIF1NBIO1_AP_CNTL_ADDRESS                     0x10440538UL
#define SMN_DEV0_FUNC0_NBIF2NBIO0_AP_CNTL_ADDRESS                     0x10540538UL
#define SMN_DEV0_FUNC0_NBIF2NBIO1_AP_CNTL_ADDRESS                     0x10740538UL
#define SMN_DEV1_FUNC0_NBIF0NBIO0_AP_CNTL_ADDRESS                     0x10148538UL
#define SMN_DEV1_FUNC0_NBIF0NBIO1_AP_CNTL_ADDRESS                     0x10348538UL
#define SMN_DEV1_FUNC0_NBIF1NBIO0_AP_CNTL_ADDRESS                     0x10248538UL
#define SMN_DEV1_FUNC0_NBIF1NBIO1_AP_CNTL_ADDRESS                     0x10448538UL


/***********************************************************
* Register Name : AP_DATA1
************************************************************/

#define AP_DATA1_AP_USAGE_INFORMATION_OFFSET                   0
#define AP_DATA1_AP_USAGE_INFORMATION_MASK                     0x7

#define AP_DATA1_Reserved_4_3_OFFSET                           3
#define AP_DATA1_Reserved_4_3_MASK                             0x18

#define AP_DATA1_AP_DETAILS_OFFSET                             5
#define AP_DATA1_AP_DETAILS_MASK                               0xffe0

#define AP_DATA1_AP_VENDOR_ID_OFFSET                           16
#define AP_DATA1_AP_VENDOR_ID_MASK                             0xffff0000

typedef union {
  struct {
    UINT32                                AP_USAGE_INFORMATION:3;
    UINT32                                        Reserved_4_3:2;
    UINT32                                          AP_DETAILS:11;
    UINT32                                        AP_VENDOR_ID:16;
  } Field;
  UINT32 Value;
} AP_DATA1_STRUCT;

#define PCICFG_NBIFEPF0CFG_AP_DATA1_OFFSET                          0x53c
#define SMN_DEV0_FUNC0_NBIF0NBIO0_AP_DATA1_ADDRESS                    0x1014053cUL
#define SMN_DEV0_FUNC0_NBIF0NBIO1_AP_DATA1_ADDRESS                    0x1034053cUL
#define SMN_DEV0_FUNC0_NBIF1NBIO0_AP_DATA1_ADDRESS                    0x1024053cUL
#define SMN_DEV0_FUNC0_NBIF1NBIO1_AP_DATA1_ADDRESS                    0x1044053cUL
#define SMN_DEV0_FUNC0_NBIF2NBIO0_AP_DATA1_ADDRESS                    0x1054053cUL
#define SMN_DEV0_FUNC0_NBIF2NBIO1_AP_DATA1_ADDRESS                    0x1074053cUL
#define SMN_DEV1_FUNC0_NBIF0NBIO0_AP_DATA1_ADDRESS                    0x1014853cUL
#define SMN_DEV1_FUNC0_NBIF0NBIO1_AP_DATA1_ADDRESS                    0x1034853cUL
#define SMN_DEV1_FUNC0_NBIF1NBIO0_AP_DATA1_ADDRESS                    0x1024853cUL
#define SMN_DEV1_FUNC0_NBIF1NBIO1_AP_DATA1_ADDRESS                    0x1044853cUL


/***********************************************************
* Register Name : AP_DATA2
************************************************************/

#define AP_DATA2_MODIFIED_TS_INFORMATION_2_OFFSET              0
#define AP_DATA2_MODIFIED_TS_INFORMATION_2_MASK                0xffffff

#define AP_DATA2_Reserved_31_24_OFFSET                         24
#define AP_DATA2_Reserved_31_24_MASK                           0xff000000

typedef union {
  struct {
    UINT32                           MODIFIED_TS_INFORMATION_2:24;
    UINT32                                      Reserved_31_24:8;
  } Field;
  UINT32 Value;
} AP_DATA2_STRUCT;

#define PCICFG_NBIFEPF0CFG_AP_DATA2_OFFSET                          0x540
#define SMN_DEV0_FUNC0_NBIF0NBIO0_AP_DATA2_ADDRESS                    0x10140540UL
#define SMN_DEV0_FUNC0_NBIF0NBIO1_AP_DATA2_ADDRESS                    0x10340540UL
#define SMN_DEV0_FUNC0_NBIF1NBIO0_AP_DATA2_ADDRESS                    0x10240540UL
#define SMN_DEV0_FUNC0_NBIF1NBIO1_AP_DATA2_ADDRESS                    0x10440540UL
#define SMN_DEV0_FUNC0_NBIF2NBIO0_AP_DATA2_ADDRESS                    0x10540540UL
#define SMN_DEV0_FUNC0_NBIF2NBIO1_AP_DATA2_ADDRESS                    0x10740540UL
#define SMN_DEV1_FUNC0_NBIF0NBIO0_AP_DATA2_ADDRESS                    0x10148540UL
#define SMN_DEV1_FUNC0_NBIF0NBIO1_AP_DATA2_ADDRESS                    0x10348540UL
#define SMN_DEV1_FUNC0_NBIF1NBIO0_AP_DATA2_ADDRESS                    0x10248540UL
#define SMN_DEV1_FUNC0_NBIF1NBIO1_AP_DATA2_ADDRESS                    0x10448540UL


/***********************************************************
* Register Name : AP_SELECTIVE_ENABLE_MASK
************************************************************/

#define AP_SELECTIVE_ENABLE_MASK_AP_SELECTIVE_ENABLE_MASK_0_OFFSET 0
#define AP_SELECTIVE_ENABLE_MASK_AP_SELECTIVE_ENABLE_MASK_0_MASK 0x1

#define AP_SELECTIVE_ENABLE_MASK_AP_SELECTIVE_ENABLE_MASK_31_1_OFFSET 1
#define AP_SELECTIVE_ENABLE_MASK_AP_SELECTIVE_ENABLE_MASK_31_1_MASK 0xfffffffe

typedef union {
  struct {
    UINT32                          AP_SELECTIVE_ENABLE_MASK_0:1;
    UINT32                       AP_SELECTIVE_ENABLE_MASK_31_1:31;
  } Field;
  UINT32 Value;
} AP_SELECTIVE_ENABLE_MASK_STRUCT;

#define PCICFG_NBIFEPF0CFG_AP_SELECTIVE_ENABLE_MASK_OFFSET          0x544
#define SMN_DEV0_FUNC0_NBIF0NBIO0_AP_SELECTIVE_ENABLE_MASK_ADDRESS    0x10140544UL
#define SMN_DEV0_FUNC0_NBIF0NBIO1_AP_SELECTIVE_ENABLE_MASK_ADDRESS    0x10340544UL
#define SMN_DEV0_FUNC0_NBIF1NBIO0_AP_SELECTIVE_ENABLE_MASK_ADDRESS    0x10240544UL
#define SMN_DEV0_FUNC0_NBIF1NBIO1_AP_SELECTIVE_ENABLE_MASK_ADDRESS    0x10440544UL
#define SMN_DEV0_FUNC0_NBIF2NBIO0_AP_SELECTIVE_ENABLE_MASK_ADDRESS    0x10540544UL
#define SMN_DEV0_FUNC0_NBIF2NBIO1_AP_SELECTIVE_ENABLE_MASK_ADDRESS    0x10740544UL
#define SMN_DEV1_FUNC0_NBIF0NBIO0_AP_SELECTIVE_ENABLE_MASK_ADDRESS    0x10148544UL
#define SMN_DEV1_FUNC0_NBIF0NBIO1_AP_SELECTIVE_ENABLE_MASK_ADDRESS    0x10348544UL
#define SMN_DEV1_FUNC0_NBIF1NBIO0_AP_SELECTIVE_ENABLE_MASK_ADDRESS    0x10248544UL
#define SMN_DEV1_FUNC0_NBIF1NBIO1_AP_SELECTIVE_ENABLE_MASK_ADDRESS    0x10448544UL


/***********************************************************
* Register Name : BASE_ADDR_1
************************************************************/

#define BASE_ADDR_1_BASE_ADDR_OFFSET                           0
#define BASE_ADDR_1_BASE_ADDR_MASK                             0xffffffff

typedef union {
  struct {
    UINT32                                           BASE_ADDR:32;
  } Field;
  UINT32 Value;
} BASE_ADDR_1_STRUCT;

#define PCICFG_NBIFEPF0CFG_BASE_ADDR_1_OFFSET                       0x10
#define SMN_DEV0_FUNC0_NBIF0NBIO0_BASE_ADDR_1_ADDRESS                 0x10140010UL
#define SMN_DEV0_FUNC0_NBIF0NBIO1_BASE_ADDR_1_ADDRESS                 0x10340010UL
#define SMN_DEV0_FUNC0_NBIF1NBIO0_BASE_ADDR_1_ADDRESS                 0x10240010UL
#define SMN_DEV0_FUNC0_NBIF1NBIO1_BASE_ADDR_1_ADDRESS                 0x10440010UL
#define SMN_DEV0_FUNC0_NBIF2NBIO0_BASE_ADDR_1_ADDRESS                 0x10540010UL
#define SMN_DEV0_FUNC0_NBIF2NBIO1_BASE_ADDR_1_ADDRESS                 0x10740010UL
#define SMN_DEV1_FUNC0_NBIF0NBIO0_BASE_ADDR_1_ADDRESS                 0x10148010UL
#define SMN_DEV1_FUNC0_NBIF0NBIO1_BASE_ADDR_1_ADDRESS                 0x10348010UL
#define SMN_DEV1_FUNC0_NBIF1NBIO0_BASE_ADDR_1_ADDRESS                 0x10248010UL
#define SMN_DEV1_FUNC0_NBIF1NBIO1_BASE_ADDR_1_ADDRESS                 0x10448010UL


/***********************************************************
* Register Name : BASE_ADDR_2
************************************************************/

#define BASE_ADDR_2_BASE_ADDR_OFFSET                           0
#define BASE_ADDR_2_BASE_ADDR_MASK                             0xffffffff

typedef union {
  struct {
    UINT32                                           BASE_ADDR:32;
  } Field;
  UINT32 Value;
} BASE_ADDR_2_STRUCT;

#define PCICFG_NBIFEPF0CFG_BASE_ADDR_2_OFFSET                       0x14
#define SMN_DEV0_FUNC0_NBIF0NBIO0_BASE_ADDR_2_ADDRESS                 0x10140014UL
#define SMN_DEV0_FUNC0_NBIF0NBIO1_BASE_ADDR_2_ADDRESS                 0x10340014UL
#define SMN_DEV0_FUNC0_NBIF1NBIO0_BASE_ADDR_2_ADDRESS                 0x10240014UL
#define SMN_DEV0_FUNC0_NBIF1NBIO1_BASE_ADDR_2_ADDRESS                 0x10440014UL
#define SMN_DEV0_FUNC0_NBIF2NBIO0_BASE_ADDR_2_ADDRESS                 0x10540014UL
#define SMN_DEV0_FUNC0_NBIF2NBIO1_BASE_ADDR_2_ADDRESS                 0x10740014UL
#define SMN_DEV1_FUNC0_NBIF0NBIO0_BASE_ADDR_2_ADDRESS                 0x10148014UL
#define SMN_DEV1_FUNC0_NBIF0NBIO1_BASE_ADDR_2_ADDRESS                 0x10348014UL
#define SMN_DEV1_FUNC0_NBIF1NBIO0_BASE_ADDR_2_ADDRESS                 0x10248014UL
#define SMN_DEV1_FUNC0_NBIF1NBIO1_BASE_ADDR_2_ADDRESS                 0x10448014UL


/***********************************************************
* Register Name : BASE_ADDR_3
************************************************************/

#define BASE_ADDR_3_BASE_ADDR_OFFSET                           0
#define BASE_ADDR_3_BASE_ADDR_MASK                             0xffffffff

typedef union {
  struct {
    UINT32                                           BASE_ADDR:32;
  } Field;
  UINT32 Value;
} BASE_ADDR_3_STRUCT;

#define PCICFG_NBIFEPF0CFG_BASE_ADDR_3_OFFSET                       0x18
#define SMN_DEV0_FUNC0_NBIF0NBIO0_BASE_ADDR_3_ADDRESS                 0x10140018UL
#define SMN_DEV0_FUNC0_NBIF0NBIO1_BASE_ADDR_3_ADDRESS                 0x10340018UL
#define SMN_DEV0_FUNC0_NBIF1NBIO0_BASE_ADDR_3_ADDRESS                 0x10240018UL
#define SMN_DEV0_FUNC0_NBIF1NBIO1_BASE_ADDR_3_ADDRESS                 0x10440018UL
#define SMN_DEV0_FUNC0_NBIF2NBIO0_BASE_ADDR_3_ADDRESS                 0x10540018UL
#define SMN_DEV0_FUNC0_NBIF2NBIO1_BASE_ADDR_3_ADDRESS                 0x10740018UL
#define SMN_DEV1_FUNC0_NBIF0NBIO0_BASE_ADDR_3_ADDRESS                 0x10148018UL
#define SMN_DEV1_FUNC0_NBIF0NBIO1_BASE_ADDR_3_ADDRESS                 0x10348018UL
#define SMN_DEV1_FUNC0_NBIF1NBIO0_BASE_ADDR_3_ADDRESS                 0x10248018UL
#define SMN_DEV1_FUNC0_NBIF1NBIO1_BASE_ADDR_3_ADDRESS                 0x10448018UL


/***********************************************************
* Register Name : BASE_ADDR_4
************************************************************/

#define BASE_ADDR_4_BASE_ADDR_OFFSET                           0
#define BASE_ADDR_4_BASE_ADDR_MASK                             0xffffffff

typedef union {
  struct {
    UINT32                                           BASE_ADDR:32;
  } Field;
  UINT32 Value;
} BASE_ADDR_4_STRUCT;

#define PCICFG_NBIFEPF0CFG_BASE_ADDR_4_OFFSET                       0x1c
#define SMN_DEV0_FUNC0_NBIF0NBIO0_BASE_ADDR_4_ADDRESS                 0x1014001cUL
#define SMN_DEV0_FUNC0_NBIF0NBIO1_BASE_ADDR_4_ADDRESS                 0x1034001cUL
#define SMN_DEV0_FUNC0_NBIF1NBIO0_BASE_ADDR_4_ADDRESS                 0x1024001cUL
#define SMN_DEV0_FUNC0_NBIF1NBIO1_BASE_ADDR_4_ADDRESS                 0x1044001cUL
#define SMN_DEV0_FUNC0_NBIF2NBIO0_BASE_ADDR_4_ADDRESS                 0x1054001cUL
#define SMN_DEV0_FUNC0_NBIF2NBIO1_BASE_ADDR_4_ADDRESS                 0x1074001cUL
#define SMN_DEV1_FUNC0_NBIF0NBIO0_BASE_ADDR_4_ADDRESS                 0x1014801cUL
#define SMN_DEV1_FUNC0_NBIF0NBIO1_BASE_ADDR_4_ADDRESS                 0x1034801cUL
#define SMN_DEV1_FUNC0_NBIF1NBIO0_BASE_ADDR_4_ADDRESS                 0x1024801cUL
#define SMN_DEV1_FUNC0_NBIF1NBIO1_BASE_ADDR_4_ADDRESS                 0x1044801cUL


/***********************************************************
* Register Name : BASE_ADDR_5
************************************************************/

#define BASE_ADDR_5_BASE_ADDR_OFFSET                           0
#define BASE_ADDR_5_BASE_ADDR_MASK                             0xffffffff

typedef union {
  struct {
    UINT32                                           BASE_ADDR:32;
  } Field;
  UINT32 Value;
} BASE_ADDR_5_STRUCT;

#define PCICFG_NBIFEPF0CFG_BASE_ADDR_5_OFFSET                       0x20
#define SMN_DEV0_FUNC0_NBIF0NBIO0_BASE_ADDR_5_ADDRESS                 0x10140020UL
#define SMN_DEV0_FUNC0_NBIF0NBIO1_BASE_ADDR_5_ADDRESS                 0x10340020UL
#define SMN_DEV0_FUNC0_NBIF1NBIO0_BASE_ADDR_5_ADDRESS                 0x10240020UL
#define SMN_DEV0_FUNC0_NBIF1NBIO1_BASE_ADDR_5_ADDRESS                 0x10440020UL
#define SMN_DEV0_FUNC0_NBIF2NBIO0_BASE_ADDR_5_ADDRESS                 0x10540020UL
#define SMN_DEV0_FUNC0_NBIF2NBIO1_BASE_ADDR_5_ADDRESS                 0x10740020UL
#define SMN_DEV1_FUNC0_NBIF0NBIO0_BASE_ADDR_5_ADDRESS                 0x10148020UL
#define SMN_DEV1_FUNC0_NBIF0NBIO1_BASE_ADDR_5_ADDRESS                 0x10348020UL
#define SMN_DEV1_FUNC0_NBIF1NBIO0_BASE_ADDR_5_ADDRESS                 0x10248020UL
#define SMN_DEV1_FUNC0_NBIF1NBIO1_BASE_ADDR_5_ADDRESS                 0x10448020UL


/***********************************************************
* Register Name : BASE_ADDR_6
************************************************************/

#define BASE_ADDR_6_BASE_ADDR_OFFSET                           0
#define BASE_ADDR_6_BASE_ADDR_MASK                             0xffffffff

typedef union {
  struct {
    UINT32                                           BASE_ADDR:32;
  } Field;
  UINT32 Value;
} BASE_ADDR_6_STRUCT;

#define PCICFG_NBIFEPF0CFG_BASE_ADDR_6_OFFSET                       0x24
#define SMN_DEV0_FUNC0_NBIF0NBIO0_BASE_ADDR_6_ADDRESS                 0x10140024UL
#define SMN_DEV0_FUNC0_NBIF0NBIO1_BASE_ADDR_6_ADDRESS                 0x10340024UL
#define SMN_DEV0_FUNC0_NBIF1NBIO0_BASE_ADDR_6_ADDRESS                 0x10240024UL
#define SMN_DEV0_FUNC0_NBIF1NBIO1_BASE_ADDR_6_ADDRESS                 0x10440024UL
#define SMN_DEV0_FUNC0_NBIF2NBIO0_BASE_ADDR_6_ADDRESS                 0x10540024UL
#define SMN_DEV0_FUNC0_NBIF2NBIO1_BASE_ADDR_6_ADDRESS                 0x10740024UL
#define SMN_DEV1_FUNC0_NBIF0NBIO0_BASE_ADDR_6_ADDRESS                 0x10148024UL
#define SMN_DEV1_FUNC0_NBIF0NBIO1_BASE_ADDR_6_ADDRESS                 0x10348024UL
#define SMN_DEV1_FUNC0_NBIF1NBIO0_BASE_ADDR_6_ADDRESS                 0x10248024UL
#define SMN_DEV1_FUNC0_NBIF1NBIO1_BASE_ADDR_6_ADDRESS                 0x10448024UL


/***********************************************************
* Register Name : BASE_CLASS
************************************************************/

#define BASE_CLASS_BASE_CLASS_OFFSET                           0
#define BASE_CLASS_BASE_CLASS_MASK                             0xff

typedef union {
  struct {
    UINT8                                          BASE_CLASS:8;
  } Field;
  UINT8 Value;
} BASE_CLASS_STRUCT;

#define PCICFG_NBIFEPF0CFG_BASE_CLASS_OFFSET                        0xb
#define SMN_DEV0_FUNC0_NBIF0NBIO0_BASE_CLASS_ADDRESS                  0x1014000bUL
#define SMN_DEV0_FUNC0_NBIF0NBIO1_BASE_CLASS_ADDRESS                  0x1034000bUL
#define SMN_DEV0_FUNC0_NBIF1NBIO0_BASE_CLASS_ADDRESS                  0x1024000bUL
#define SMN_DEV0_FUNC0_NBIF1NBIO1_BASE_CLASS_ADDRESS                  0x1044000bUL
#define SMN_DEV0_FUNC0_NBIF2NBIO0_BASE_CLASS_ADDRESS                  0x1054000bUL
#define SMN_DEV0_FUNC0_NBIF2NBIO1_BASE_CLASS_ADDRESS                  0x1074000bUL
#define SMN_DEV1_FUNC0_NBIF0NBIO0_BASE_CLASS_ADDRESS                  0x1014800bUL
#define SMN_DEV1_FUNC0_NBIF0NBIO1_BASE_CLASS_ADDRESS                  0x1034800bUL
#define SMN_DEV1_FUNC0_NBIF1NBIO0_BASE_CLASS_ADDRESS                  0x1024800bUL
#define SMN_DEV1_FUNC0_NBIF1NBIO1_BASE_CLASS_ADDRESS                  0x1044800bUL


/***********************************************************
* Register Name : BIST
************************************************************/

#define BIST_BIST_COMP_OFFSET                                  0
#define BIST_BIST_COMP_MASK                                    0xf

#define BIST_Reserved_5_4_OFFSET                               4
#define BIST_Reserved_5_4_MASK                                 0x30

#define BIST_BIST_STRT_OFFSET                                  6
#define BIST_BIST_STRT_MASK                                    0x40

#define BIST_BIST_CAP_OFFSET                                   7
#define BIST_BIST_CAP_MASK                                     0x80

typedef union {
  struct {
    UINT8                                           BIST_COMP:4;
    UINT8                                        Reserved_5_4:2;
    UINT8                                           BIST_STRT:1;
    UINT8                                            BIST_CAP:1;
  } Field;
  UINT8 Value;
} BIST_STRUCT;

#define PCICFG_NBIFEPF0CFG_BIST_OFFSET                              0xf
#define SMN_DEV0_FUNC0_NBIF0NBIO0_BIST_ADDRESS                        0x1014000fUL
#define SMN_DEV0_FUNC0_NBIF0NBIO1_BIST_ADDRESS                        0x1034000fUL
#define SMN_DEV0_FUNC0_NBIF1NBIO0_BIST_ADDRESS                        0x1024000fUL
#define SMN_DEV0_FUNC0_NBIF1NBIO1_BIST_ADDRESS                        0x1044000fUL
#define SMN_DEV0_FUNC0_NBIF2NBIO0_BIST_ADDRESS                        0x1054000fUL
#define SMN_DEV0_FUNC0_NBIF2NBIO1_BIST_ADDRESS                        0x1074000fUL
#define SMN_DEV1_FUNC0_NBIF0NBIO0_BIST_ADDRESS                        0x1014800fUL
#define SMN_DEV1_FUNC0_NBIF0NBIO1_BIST_ADDRESS                        0x1034800fUL
#define SMN_DEV1_FUNC0_NBIF1NBIO0_BIST_ADDRESS                        0x1024800fUL
#define SMN_DEV1_FUNC0_NBIF1NBIO1_BIST_ADDRESS                        0x1044800fUL


/***********************************************************
* Register Name : CACHE_LINE
************************************************************/

#define CACHE_LINE_CACHE_LINE_SIZE_OFFSET                      0
#define CACHE_LINE_CACHE_LINE_SIZE_MASK                        0xff

typedef union {
  struct {
    UINT8                                     CACHE_LINE_SIZE:8;
  } Field;
  UINT8 Value;
} CACHE_LINE_STRUCT;

#define PCICFG_NBIFEPF0CFG_CACHE_LINE_OFFSET                        0xc
#define SMN_DEV0_FUNC0_NBIF0NBIO0_CACHE_LINE_ADDRESS                  0x1014000cUL
#define SMN_DEV0_FUNC0_NBIF0NBIO1_CACHE_LINE_ADDRESS                  0x1034000cUL
#define SMN_DEV0_FUNC0_NBIF1NBIO0_CACHE_LINE_ADDRESS                  0x1024000cUL
#define SMN_DEV0_FUNC0_NBIF1NBIO1_CACHE_LINE_ADDRESS                  0x1044000cUL
#define SMN_DEV0_FUNC0_NBIF2NBIO0_CACHE_LINE_ADDRESS                  0x1054000cUL
#define SMN_DEV0_FUNC0_NBIF2NBIO1_CACHE_LINE_ADDRESS                  0x1074000cUL
#define SMN_DEV1_FUNC0_NBIF0NBIO0_CACHE_LINE_ADDRESS                  0x1014800cUL
#define SMN_DEV1_FUNC0_NBIF0NBIO1_CACHE_LINE_ADDRESS                  0x1034800cUL
#define SMN_DEV1_FUNC0_NBIF1NBIO0_CACHE_LINE_ADDRESS                  0x1024800cUL
#define SMN_DEV1_FUNC0_NBIF1NBIO1_CACHE_LINE_ADDRESS                  0x1044800cUL


/***********************************************************
* Register Name : CAP_PTR
************************************************************/

#define CAP_PTR_CAP_PTR_OFFSET                                 0
#define CAP_PTR_CAP_PTR_MASK                                   0xff

typedef union {
  struct {
    UINT8                                             CAP_PTR:8;
  } Field;
  UINT8 Value;
} CAP_PTR_STRUCT;

#define PCICFG_NBIFEPF0CFG_CAP_PTR_OFFSET                           0x34
#define SMN_DEV0_FUNC0_NBIF0NBIO0_CAP_PTR_ADDRESS                     0x10140034UL
#define SMN_DEV0_FUNC0_NBIF0NBIO1_CAP_PTR_ADDRESS                     0x10340034UL
#define SMN_DEV0_FUNC0_NBIF1NBIO0_CAP_PTR_ADDRESS                     0x10240034UL
#define SMN_DEV0_FUNC0_NBIF1NBIO1_CAP_PTR_ADDRESS                     0x10440034UL
#define SMN_DEV0_FUNC0_NBIF2NBIO0_CAP_PTR_ADDRESS                     0x10540034UL
#define SMN_DEV0_FUNC0_NBIF2NBIO1_CAP_PTR_ADDRESS                     0x10740034UL
#define SMN_DEV1_FUNC0_NBIF0NBIO0_CAP_PTR_ADDRESS                     0x10148034UL
#define SMN_DEV1_FUNC0_NBIF0NBIO1_CAP_PTR_ADDRESS                     0x10348034UL
#define SMN_DEV1_FUNC0_NBIF1NBIO0_CAP_PTR_ADDRESS                     0x10248034UL
#define SMN_DEV1_FUNC0_NBIF1NBIO1_CAP_PTR_ADDRESS                     0x10448034UL


/***********************************************************
* Register Name : CARDBUS_CIS_PTR
************************************************************/

#define CARDBUS_CIS_PTR_CARDBUS_CIS_PTR_OFFSET                 0
#define CARDBUS_CIS_PTR_CARDBUS_CIS_PTR_MASK                   0xffffffff

typedef union {
  struct {
    UINT32                                     CARDBUS_CIS_PTR:32;
  } Field;
  UINT32 Value;
} CARDBUS_CIS_PTR_STRUCT;

#define PCICFG_NBIFEPF0CFG_CARDBUS_CIS_PTR_OFFSET                   0x28
#define SMN_DEV0_FUNC0_NBIF0NBIO0_CARDBUS_CIS_PTR_ADDRESS             0x10140028UL
#define SMN_DEV0_FUNC0_NBIF0NBIO1_CARDBUS_CIS_PTR_ADDRESS             0x10340028UL
#define SMN_DEV0_FUNC0_NBIF1NBIO0_CARDBUS_CIS_PTR_ADDRESS             0x10240028UL
#define SMN_DEV0_FUNC0_NBIF1NBIO1_CARDBUS_CIS_PTR_ADDRESS             0x10440028UL
#define SMN_DEV0_FUNC0_NBIF2NBIO0_CARDBUS_CIS_PTR_ADDRESS             0x10540028UL
#define SMN_DEV0_FUNC0_NBIF2NBIO1_CARDBUS_CIS_PTR_ADDRESS             0x10740028UL
#define SMN_DEV1_FUNC0_NBIF0NBIO0_CARDBUS_CIS_PTR_ADDRESS             0x10148028UL
#define SMN_DEV1_FUNC0_NBIF0NBIO1_CARDBUS_CIS_PTR_ADDRESS             0x10348028UL
#define SMN_DEV1_FUNC0_NBIF1NBIO0_CARDBUS_CIS_PTR_ADDRESS             0x10248028UL
#define SMN_DEV1_FUNC0_NBIF1NBIO1_CARDBUS_CIS_PTR_ADDRESS             0x10448028UL


/***********************************************************
* Register Name : COMMAND
************************************************************/

#define COMMAND_IO_ACCESS_EN_OFFSET                            0
#define COMMAND_IO_ACCESS_EN_MASK                              0x1

#define COMMAND_MEM_ACCESS_EN_OFFSET                           1
#define COMMAND_MEM_ACCESS_EN_MASK                             0x2

#define COMMAND_BUS_MASTER_EN_OFFSET                           2
#define COMMAND_BUS_MASTER_EN_MASK                             0x4

#define COMMAND_SPECIAL_CYCLE_EN_OFFSET                        3
#define COMMAND_SPECIAL_CYCLE_EN_MASK                          0x8

#define COMMAND_MEM_WRITE_INVALIDATE_EN_OFFSET                 4
#define COMMAND_MEM_WRITE_INVALIDATE_EN_MASK                   0x10

#define COMMAND_PAL_SNOOP_EN_OFFSET                            5
#define COMMAND_PAL_SNOOP_EN_MASK                              0x20

#define COMMAND_PARITY_ERROR_RESPONSE_OFFSET                   6
#define COMMAND_PARITY_ERROR_RESPONSE_MASK                     0x40

#define COMMAND_AD_STEPPING_OFFSET                             7
#define COMMAND_AD_STEPPING_MASK                               0x80

#define COMMAND_SERR_EN_OFFSET                                 8
#define COMMAND_SERR_EN_MASK                                   0x100

#define COMMAND_FAST_B2B_EN_OFFSET                             9
#define COMMAND_FAST_B2B_EN_MASK                               0x200

#define COMMAND_INT_DIS_OFFSET                                 10
#define COMMAND_INT_DIS_MASK                                   0x400

#define COMMAND_Reserved_15_11_OFFSET                          11
#define COMMAND_Reserved_15_11_MASK                            0xf800

typedef union {
  struct {
    UINT16                                        IO_ACCESS_EN:1;
    UINT16                                       MEM_ACCESS_EN:1;
    UINT16                                       BUS_MASTER_EN:1;
    UINT16                                    SPECIAL_CYCLE_EN:1;
    UINT16                             MEM_WRITE_INVALIDATE_EN:1;
    UINT16                                        PAL_SNOOP_EN:1;
    UINT16                               PARITY_ERROR_RESPONSE:1;
    UINT16                                         AD_STEPPING:1;
    UINT16                                             SERR_EN:1;
    UINT16                                         FAST_B2B_EN:1;
    UINT16                                             INT_DIS:1;
    UINT16                                      Reserved_15_11:5;
  } Field;
  UINT16 Value;
} COMMAND_NBIFEPF0CFG_STRUCT;

#define PCICFG_NBIFEPF0CFG_COMMAND_OFFSET                           0x4
#define SMN_DEV0_FUNC0_NBIF0NBIO0_COMMAND_ADDRESS                     0x10140004UL
#define SMN_DEV0_FUNC0_NBIF0NBIO1_COMMAND_ADDRESS                     0x10340004UL
#define SMN_DEV0_FUNC0_NBIF1NBIO0_COMMAND_ADDRESS                     0x10240004UL
#define SMN_DEV0_FUNC0_NBIF1NBIO1_COMMAND_ADDRESS                     0x10440004UL
#define SMN_DEV0_FUNC0_NBIF2NBIO0_COMMAND_ADDRESS                     0x10540004UL
#define SMN_DEV0_FUNC0_NBIF2NBIO1_COMMAND_ADDRESS                     0x10740004UL
#define SMN_DEV1_FUNC0_NBIF0NBIO0_COMMAND_ADDRESS                     0x10148004UL
#define SMN_DEV1_FUNC0_NBIF0NBIO1_COMMAND_ADDRESS                     0x10348004UL
#define SMN_DEV1_FUNC0_NBIF1NBIO0_COMMAND_ADDRESS                     0x10248004UL
#define SMN_DEV1_FUNC0_NBIF1NBIO1_COMMAND_ADDRESS                     0x10448004UL


/***********************************************************
* Register Name : DATA_LINK_FEATURE_CAP
************************************************************/

#define DATA_LINK_FEATURE_CAP_LOCAL_DLF_SUPPORTED_OFFSET       0
#define DATA_LINK_FEATURE_CAP_LOCAL_DLF_SUPPORTED_MASK         0x7fffff

#define DATA_LINK_FEATURE_CAP_Reserved_30_23_OFFSET            23
#define DATA_LINK_FEATURE_CAP_Reserved_30_23_MASK              0x7f800000

#define DATA_LINK_FEATURE_CAP_DLF_EXCHANGE_ENABLE_OFFSET       31
#define DATA_LINK_FEATURE_CAP_DLF_EXCHANGE_ENABLE_MASK         0x80000000

typedef union {
  struct {
    UINT32                                 LOCAL_DLF_SUPPORTED:23;
    UINT32                                      Reserved_30_23:8;
    UINT32                                 DLF_EXCHANGE_ENABLE:1;
  } Field;
  UINT32 Value;
} DATA_LINK_FEATURE_CAP_STRUCT;

#define PCICFG_NBIFEPF0CFG_DATA_LINK_FEATURE_CAP_OFFSET             0x404
#define SMN_DEV0_FUNC0_NBIF0NBIO0_DATA_LINK_FEATURE_CAP_ADDRESS       0x10140404UL
#define SMN_DEV0_FUNC0_NBIF0NBIO1_DATA_LINK_FEATURE_CAP_ADDRESS       0x10340404UL
#define SMN_DEV0_FUNC0_NBIF1NBIO0_DATA_LINK_FEATURE_CAP_ADDRESS       0x10240404UL
#define SMN_DEV0_FUNC0_NBIF1NBIO1_DATA_LINK_FEATURE_CAP_ADDRESS       0x10440404UL
#define SMN_DEV0_FUNC0_NBIF2NBIO0_DATA_LINK_FEATURE_CAP_ADDRESS       0x10540404UL
#define SMN_DEV0_FUNC0_NBIF2NBIO1_DATA_LINK_FEATURE_CAP_ADDRESS       0x10740404UL
#define SMN_DEV1_FUNC0_NBIF0NBIO0_DATA_LINK_FEATURE_CAP_ADDRESS       0x10148404UL
#define SMN_DEV1_FUNC0_NBIF0NBIO1_DATA_LINK_FEATURE_CAP_ADDRESS       0x10348404UL
#define SMN_DEV1_FUNC0_NBIF1NBIO0_DATA_LINK_FEATURE_CAP_ADDRESS       0x10248404UL
#define SMN_DEV1_FUNC0_NBIF1NBIO1_DATA_LINK_FEATURE_CAP_ADDRESS       0x10448404UL


/***********************************************************
* Register Name : DATA_LINK_FEATURE_STATUS
************************************************************/

#define DATA_LINK_FEATURE_STATUS_REMOTE_DLF_SUPPORTED_OFFSET   0
#define DATA_LINK_FEATURE_STATUS_REMOTE_DLF_SUPPORTED_MASK     0x7fffff

#define DATA_LINK_FEATURE_STATUS_Reserved_30_23_OFFSET         23
#define DATA_LINK_FEATURE_STATUS_Reserved_30_23_MASK           0x7f800000

#define DATA_LINK_FEATURE_STATUS_REMOTE_DLF_SUPPORTED_VALID_OFFSET 31
#define DATA_LINK_FEATURE_STATUS_REMOTE_DLF_SUPPORTED_VALID_MASK 0x80000000

typedef union {
  struct {
    UINT32                                REMOTE_DLF_SUPPORTED:23;
    UINT32                                      Reserved_30_23:8;
    UINT32                          REMOTE_DLF_SUPPORTED_VALID:1;
  } Field;
  UINT32 Value;
} DATA_LINK_FEATURE_STATUS_STRUCT;

#define PCICFG_NBIFEPF0CFG_DATA_LINK_FEATURE_STATUS_OFFSET          0x408
#define SMN_DEV0_FUNC0_NBIF0NBIO0_DATA_LINK_FEATURE_STATUS_ADDRESS    0x10140408UL
#define SMN_DEV0_FUNC0_NBIF0NBIO1_DATA_LINK_FEATURE_STATUS_ADDRESS    0x10340408UL
#define SMN_DEV0_FUNC0_NBIF1NBIO0_DATA_LINK_FEATURE_STATUS_ADDRESS    0x10240408UL
#define SMN_DEV0_FUNC0_NBIF1NBIO1_DATA_LINK_FEATURE_STATUS_ADDRESS    0x10440408UL
#define SMN_DEV0_FUNC0_NBIF2NBIO0_DATA_LINK_FEATURE_STATUS_ADDRESS    0x10540408UL
#define SMN_DEV0_FUNC0_NBIF2NBIO1_DATA_LINK_FEATURE_STATUS_ADDRESS    0x10740408UL
#define SMN_DEV1_FUNC0_NBIF0NBIO0_DATA_LINK_FEATURE_STATUS_ADDRESS    0x10148408UL
#define SMN_DEV1_FUNC0_NBIF0NBIO1_DATA_LINK_FEATURE_STATUS_ADDRESS    0x10348408UL
#define SMN_DEV1_FUNC0_NBIF1NBIO0_DATA_LINK_FEATURE_STATUS_ADDRESS    0x10248408UL
#define SMN_DEV1_FUNC0_NBIF1NBIO1_DATA_LINK_FEATURE_STATUS_ADDRESS    0x10448408UL


/***********************************************************
* Register Name : DBESL_DBESLD
************************************************************/

#define DBESL_DBESLD_DBESL_OFFSET                              0
#define DBESL_DBESLD_DBESL_MASK                                0xf

#define DBESL_DBESLD_DBESLD_OFFSET                             4
#define DBESL_DBESLD_DBESLD_MASK                               0xf0

typedef union {
  struct {
    UINT8                                               DBESL:4;
    UINT8                                              DBESLD:4;
  } Field;
  UINT8 Value;
} DBESL_DBESLD_STRUCT;

#define PCICFG_NBIFEPF0CFG_DBESL_DBESLD_OFFSET                      0x62
#define SMN_DEV1_FUNC0_NBIF0NBIO0_DBESL_DBESLD_ADDRESS                0x10148062UL
#define SMN_DEV1_FUNC0_NBIF0NBIO1_DBESL_DBESLD_ADDRESS                0x10348062UL
#define SMN_DEV1_FUNC0_NBIF1NBIO0_DBESL_DBESLD_ADDRESS                0x10248062UL
#define SMN_DEV1_FUNC0_NBIF1NBIO1_DBESL_DBESLD_ADDRESS                0x10448062UL


/***********************************************************
* Register Name : DEVICE_CAP
************************************************************/

#define DEVICE_CAP_MAX_PAYLOAD_SUPPORT_OFFSET                  0
#define DEVICE_CAP_MAX_PAYLOAD_SUPPORT_MASK                    0x7

#define DEVICE_CAP_PHANTOM_FUNC_OFFSET                         3
#define DEVICE_CAP_PHANTOM_FUNC_MASK                           0x18

#define DEVICE_CAP_EXTENDED_TAG_OFFSET                         5
#define DEVICE_CAP_EXTENDED_TAG_MASK                           0x20

#define DEVICE_CAP_L0S_ACCEPTABLE_LATENCY_OFFSET               6
#define DEVICE_CAP_L0S_ACCEPTABLE_LATENCY_MASK                 0x1c0

#define DEVICE_CAP_L1_ACCEPTABLE_LATENCY_OFFSET                9
#define DEVICE_CAP_L1_ACCEPTABLE_LATENCY_MASK                  0xe00

#define DEVICE_CAP_Reserved_14_12_OFFSET                       12
#define DEVICE_CAP_Reserved_14_12_MASK                         0x7000

#define DEVICE_CAP_ROLE_BASED_ERR_REPORTING_OFFSET             15
#define DEVICE_CAP_ROLE_BASED_ERR_REPORTING_MASK               0x8000

#define DEVICE_CAP_ERR_COR_SUBCLASS_CAPABLE_OFFSET             16
#define DEVICE_CAP_ERR_COR_SUBCLASS_CAPABLE_MASK               0x10000

#define DEVICE_CAP_Reserved_17_17_OFFSET                       17
#define DEVICE_CAP_Reserved_17_17_MASK                         0x20000

#define DEVICE_CAP_CAPTURED_SLOT_POWER_LIMIT_OFFSET            18
#define DEVICE_CAP_CAPTURED_SLOT_POWER_LIMIT_MASK              0x3fc0000

#define DEVICE_CAP_CAPTURED_SLOT_POWER_SCALE_OFFSET            26
#define DEVICE_CAP_CAPTURED_SLOT_POWER_SCALE_MASK              0xc000000

#define DEVICE_CAP_FLR_CAPABLE_OFFSET                          28
#define DEVICE_CAP_FLR_CAPABLE_MASK                            0x10000000

#define DEVICE_CAP_Reserved_29_29_OFFSET                       29
#define DEVICE_CAP_Reserved_29_29_MASK                         0x20000000

#define DEVICE_CAP_TEE_IO_SUPPORT_OFFSET                       30
#define DEVICE_CAP_TEE_IO_SUPPORT_MASK                         0x40000000

#define DEVICE_CAP_Reserved_31_31_OFFSET                       31
#define DEVICE_CAP_Reserved_31_31_MASK                         0x80000000

typedef union {
  struct {
    UINT32                                 MAX_PAYLOAD_SUPPORT:3;
    UINT32                                        PHANTOM_FUNC:2;
    UINT32                                        EXTENDED_TAG:1;
    UINT32                              L0S_ACCEPTABLE_LATENCY:3;
    UINT32                               L1_ACCEPTABLE_LATENCY:3;
    UINT32                                      Reserved_14_12:3;
    UINT32                            ROLE_BASED_ERR_REPORTING:1;
    UINT32                            ERR_COR_SUBCLASS_CAPABLE:1;
    UINT32                                      Reserved_17_17:1;
    UINT32                           CAPTURED_SLOT_POWER_LIMIT:8;
    UINT32                           CAPTURED_SLOT_POWER_SCALE:2;
    UINT32                                         FLR_CAPABLE:1;
    UINT32                                      Reserved_29_29:1;
    UINT32                                      TEE_IO_SUPPORT:1;
    UINT32                                      Reserved_31_31:1;
  } Field;
  UINT32 Value;
} DEVICE_CAP_STRUCT;

#define PCICFG_NBIFEPF0CFG_DEVICE_CAP_OFFSET                        0x68
#define SMN_DEV0_FUNC0_NBIF0NBIO0_DEVICE_CAP_ADDRESS                  0x10140068UL
#define SMN_DEV0_FUNC0_NBIF0NBIO1_DEVICE_CAP_ADDRESS                  0x10340068UL
#define SMN_DEV0_FUNC0_NBIF1NBIO0_DEVICE_CAP_ADDRESS                  0x10240068UL
#define SMN_DEV0_FUNC0_NBIF1NBIO1_DEVICE_CAP_ADDRESS                  0x10440068UL
#define SMN_DEV0_FUNC0_NBIF2NBIO0_DEVICE_CAP_ADDRESS                  0x10540068UL
#define SMN_DEV0_FUNC0_NBIF2NBIO1_DEVICE_CAP_ADDRESS                  0x10740068UL
#define SMN_DEV1_FUNC0_NBIF0NBIO0_DEVICE_CAP_ADDRESS                  0x10148068UL
#define SMN_DEV1_FUNC0_NBIF0NBIO1_DEVICE_CAP_ADDRESS                  0x10348068UL
#define SMN_DEV1_FUNC0_NBIF1NBIO0_DEVICE_CAP_ADDRESS                  0x10248068UL
#define SMN_DEV1_FUNC0_NBIF1NBIO1_DEVICE_CAP_ADDRESS                  0x10448068UL


/***********************************************************
* Register Name : DEVICE_CAP2
************************************************************/

#define DEVICE_CAP2_CPL_TIMEOUT_RANGE_SUPPORTED_OFFSET         0
#define DEVICE_CAP2_CPL_TIMEOUT_RANGE_SUPPORTED_MASK           0xf

#define DEVICE_CAP2_CPL_TIMEOUT_DIS_SUPPORTED_OFFSET           4
#define DEVICE_CAP2_CPL_TIMEOUT_DIS_SUPPORTED_MASK             0x10

#define DEVICE_CAP2_ARI_FORWARDING_SUPPORTED_OFFSET            5
#define DEVICE_CAP2_ARI_FORWARDING_SUPPORTED_MASK              0x20

#define DEVICE_CAP2_ATOMICOP_ROUTING_SUPPORTED_OFFSET          6
#define DEVICE_CAP2_ATOMICOP_ROUTING_SUPPORTED_MASK            0x40

#define DEVICE_CAP2_ATOMICOP_32CMPLT_SUPPORTED_OFFSET          7
#define DEVICE_CAP2_ATOMICOP_32CMPLT_SUPPORTED_MASK            0x80

#define DEVICE_CAP2_ATOMICOP_64CMPLT_SUPPORTED_OFFSET          8
#define DEVICE_CAP2_ATOMICOP_64CMPLT_SUPPORTED_MASK            0x100

#define DEVICE_CAP2_CAS128_CMPLT_SUPPORTED_OFFSET              9
#define DEVICE_CAP2_CAS128_CMPLT_SUPPORTED_MASK                0x200

#define DEVICE_CAP2_NO_RO_ENABLED_P2P_PASSING_OFFSET           10
#define DEVICE_CAP2_NO_RO_ENABLED_P2P_PASSING_MASK             0x400

#define DEVICE_CAP2_LTR_SUPPORTED_OFFSET                       11
#define DEVICE_CAP2_LTR_SUPPORTED_MASK                         0x800

#define DEVICE_CAP2_TPH_CPLR_SUPPORTED_OFFSET                  12
#define DEVICE_CAP2_TPH_CPLR_SUPPORTED_MASK                    0x3000

#define DEVICE_CAP2_LN_SYSTEM_CLS_OFFSET                       14
#define DEVICE_CAP2_LN_SYSTEM_CLS_MASK                         0xc000

#define DEVICE_CAP2_TEN_BIT_TAG_COMPLETER_SUPPORTED_OFFSET     16
#define DEVICE_CAP2_TEN_BIT_TAG_COMPLETER_SUPPORTED_MASK       0x10000

#define DEVICE_CAP2_TEN_BIT_TAG_REQUESTER_SUPPORTED_OFFSET     17
#define DEVICE_CAP2_TEN_BIT_TAG_REQUESTER_SUPPORTED_MASK       0x20000

#define DEVICE_CAP2_OBFF_SUPPORTED_OFFSET                      18
#define DEVICE_CAP2_OBFF_SUPPORTED_MASK                        0xc0000

#define DEVICE_CAP2_EXTENDED_FMT_FIELD_SUPPORTED_OFFSET        20
#define DEVICE_CAP2_EXTENDED_FMT_FIELD_SUPPORTED_MASK          0x100000

#define DEVICE_CAP2_END_END_TLP_PREFIX_SUPPORTED_OFFSET        21
#define DEVICE_CAP2_END_END_TLP_PREFIX_SUPPORTED_MASK          0x200000

#define DEVICE_CAP2_MAX_END_END_TLP_PREFIXES_OFFSET            22
#define DEVICE_CAP2_MAX_END_END_TLP_PREFIXES_MASK              0xc00000

#define DEVICE_CAP2_EMER_POWER_REDUCTION_SUPPORTED_OFFSET      24
#define DEVICE_CAP2_EMER_POWER_REDUCTION_SUPPORTED_MASK        0x3000000

#define DEVICE_CAP2_EMER_POWER_REDUCTION_INIT_REQ_OFFSET       26
#define DEVICE_CAP2_EMER_POWER_REDUCTION_INIT_REQ_MASK         0x4000000

#define DEVICE_CAP2_Reserved_30_27_OFFSET                      27
#define DEVICE_CAP2_Reserved_30_27_MASK                        0x78000000

#define DEVICE_CAP2_FRS_SUPPORTED_OFFSET                       31
#define DEVICE_CAP2_FRS_SUPPORTED_MASK                         0x80000000

typedef union {
  struct {
    UINT32                         CPL_TIMEOUT_RANGE_SUPPORTED:4;
    UINT32                           CPL_TIMEOUT_DIS_SUPPORTED:1;
    UINT32                            ARI_FORWARDING_SUPPORTED:1;
    UINT32                          ATOMICOP_ROUTING_SUPPORTED:1;
    UINT32                          ATOMICOP_32CMPLT_SUPPORTED:1;
    UINT32                          ATOMICOP_64CMPLT_SUPPORTED:1;
    UINT32                              CAS128_CMPLT_SUPPORTED:1;
    UINT32                           NO_RO_ENABLED_P2P_PASSING:1;
    UINT32                                       LTR_SUPPORTED:1;
    UINT32                                  TPH_CPLR_SUPPORTED:2;
    UINT32                                       LN_SYSTEM_CLS:2;
    UINT32                     TEN_BIT_TAG_COMPLETER_SUPPORTED:1;
    UINT32                     TEN_BIT_TAG_REQUESTER_SUPPORTED:1;
    UINT32                                      OBFF_SUPPORTED:2;
    UINT32                        EXTENDED_FMT_FIELD_SUPPORTED:1;
    UINT32                        END_END_TLP_PREFIX_SUPPORTED:1;
    UINT32                            MAX_END_END_TLP_PREFIXES:2;
    UINT32                      EMER_POWER_REDUCTION_SUPPORTED:2;
    UINT32                       EMER_POWER_REDUCTION_INIT_REQ:1;
    UINT32                                      Reserved_30_27:4;
    UINT32                                       FRS_SUPPORTED:1;
  } Field;
  UINT32 Value;
} DEVICE_CAP2_STRUCT;

#define PCICFG_NBIFEPF0CFG_DEVICE_CAP2_OFFSET                       0x88
#define SMN_DEV0_FUNC0_NBIF0NBIO0_DEVICE_CAP2_ADDRESS                 0x10140088UL
#define SMN_DEV0_FUNC0_NBIF0NBIO1_DEVICE_CAP2_ADDRESS                 0x10340088UL
#define SMN_DEV0_FUNC0_NBIF1NBIO0_DEVICE_CAP2_ADDRESS                 0x10240088UL
#define SMN_DEV0_FUNC0_NBIF1NBIO1_DEVICE_CAP2_ADDRESS                 0x10440088UL
#define SMN_DEV0_FUNC0_NBIF2NBIO0_DEVICE_CAP2_ADDRESS                 0x10540088UL
#define SMN_DEV0_FUNC0_NBIF2NBIO1_DEVICE_CAP2_ADDRESS                 0x10740088UL
#define SMN_DEV1_FUNC0_NBIF0NBIO0_DEVICE_CAP2_ADDRESS                 0x10148088UL
#define SMN_DEV1_FUNC0_NBIF0NBIO1_DEVICE_CAP2_ADDRESS                 0x10348088UL
#define SMN_DEV1_FUNC0_NBIF1NBIO0_DEVICE_CAP2_ADDRESS                 0x10248088UL
#define SMN_DEV1_FUNC0_NBIF1NBIO1_DEVICE_CAP2_ADDRESS                 0x10448088UL


/***********************************************************
* Register Name : DEVICE_CNTL
************************************************************/

#define DEVICE_CNTL_CORR_ERR_EN_OFFSET                         0
#define DEVICE_CNTL_CORR_ERR_EN_MASK                           0x1

#define DEVICE_CNTL_NON_FATAL_ERR_EN_OFFSET                    1
#define DEVICE_CNTL_NON_FATAL_ERR_EN_MASK                      0x2

#define DEVICE_CNTL_FATAL_ERR_EN_OFFSET                        2
#define DEVICE_CNTL_FATAL_ERR_EN_MASK                          0x4

#define DEVICE_CNTL_USR_REPORT_EN_OFFSET                       3
#define DEVICE_CNTL_USR_REPORT_EN_MASK                         0x8

#define DEVICE_CNTL_RELAXED_ORD_EN_OFFSET                      4
#define DEVICE_CNTL_RELAXED_ORD_EN_MASK                        0x10

#define DEVICE_CNTL_MAX_PAYLOAD_SIZE_OFFSET                    5
#define DEVICE_CNTL_MAX_PAYLOAD_SIZE_MASK                      0xe0

#define DEVICE_CNTL_EXTENDED_TAG_EN_OFFSET                     8
#define DEVICE_CNTL_EXTENDED_TAG_EN_MASK                       0x100

#define DEVICE_CNTL_PHANTOM_FUNC_EN_OFFSET                     9
#define DEVICE_CNTL_PHANTOM_FUNC_EN_MASK                       0x200

#define DEVICE_CNTL_AUX_POWER_PM_EN_OFFSET                     10
#define DEVICE_CNTL_AUX_POWER_PM_EN_MASK                       0x400

#define DEVICE_CNTL_NO_SNOOP_EN_OFFSET                         11
#define DEVICE_CNTL_NO_SNOOP_EN_MASK                           0x800

#define DEVICE_CNTL_MAX_READ_REQUEST_SIZE_OFFSET               12
#define DEVICE_CNTL_MAX_READ_REQUEST_SIZE_MASK                 0x7000

#define DEVICE_CNTL_INITIATE_FLR_OFFSET                        15
#define DEVICE_CNTL_INITIATE_FLR_MASK                          0x8000

typedef union {
  struct {
    UINT16                                         CORR_ERR_EN:1;
    UINT16                                    NON_FATAL_ERR_EN:1;
    UINT16                                        FATAL_ERR_EN:1;
    UINT16                                       USR_REPORT_EN:1;
    UINT16                                      RELAXED_ORD_EN:1;
    UINT16                                    MAX_PAYLOAD_SIZE:3;
    UINT16                                     EXTENDED_TAG_EN:1;
    UINT16                                     PHANTOM_FUNC_EN:1;
    UINT16                                     AUX_POWER_PM_EN:1;
    UINT16                                         NO_SNOOP_EN:1;
    UINT16                               MAX_READ_REQUEST_SIZE:3;
    UINT16                                        INITIATE_FLR:1;
  } Field;
  UINT16 Value;
} DEVICE_CNTL_STRUCT;

#define PCICFG_NBIFEPF0CFG_DEVICE_CNTL_OFFSET                       0x6c
#define SMN_DEV0_FUNC0_NBIF0NBIO0_DEVICE_CNTL_ADDRESS                 0x1014006cUL
#define SMN_DEV0_FUNC0_NBIF0NBIO1_DEVICE_CNTL_ADDRESS                 0x1034006cUL
#define SMN_DEV0_FUNC0_NBIF1NBIO0_DEVICE_CNTL_ADDRESS                 0x1024006cUL
#define SMN_DEV0_FUNC0_NBIF1NBIO1_DEVICE_CNTL_ADDRESS                 0x1044006cUL
#define SMN_DEV0_FUNC0_NBIF2NBIO0_DEVICE_CNTL_ADDRESS                 0x1054006cUL
#define SMN_DEV0_FUNC0_NBIF2NBIO1_DEVICE_CNTL_ADDRESS                 0x1074006cUL
#define SMN_DEV1_FUNC0_NBIF0NBIO0_DEVICE_CNTL_ADDRESS                 0x1014806cUL
#define SMN_DEV1_FUNC0_NBIF0NBIO1_DEVICE_CNTL_ADDRESS                 0x1034806cUL
#define SMN_DEV1_FUNC0_NBIF1NBIO0_DEVICE_CNTL_ADDRESS                 0x1024806cUL
#define SMN_DEV1_FUNC0_NBIF1NBIO1_DEVICE_CNTL_ADDRESS                 0x1044806cUL


/***********************************************************
* Register Name : DEVICE_CNTL2
************************************************************/

#define DEVICE_CNTL2_CPL_TIMEOUT_VALUE_OFFSET                  0
#define DEVICE_CNTL2_CPL_TIMEOUT_VALUE_MASK                    0xf

#define DEVICE_CNTL2_CPL_TIMEOUT_DIS_OFFSET                    4
#define DEVICE_CNTL2_CPL_TIMEOUT_DIS_MASK                      0x10

#define DEVICE_CNTL2_ARI_FORWARDING_EN_OFFSET                  5
#define DEVICE_CNTL2_ARI_FORWARDING_EN_MASK                    0x20

#define DEVICE_CNTL2_ATOMICOP_REQUEST_EN_OFFSET                6
#define DEVICE_CNTL2_ATOMICOP_REQUEST_EN_MASK                  0x40

#define DEVICE_CNTL2_ATOMICOP_EGRESS_BLOCKING_OFFSET           7
#define DEVICE_CNTL2_ATOMICOP_EGRESS_BLOCKING_MASK             0x80

#define DEVICE_CNTL2_IDO_REQUEST_ENABLE_OFFSET                 8
#define DEVICE_CNTL2_IDO_REQUEST_ENABLE_MASK                   0x100

#define DEVICE_CNTL2_IDO_COMPLETION_ENABLE_OFFSET              9
#define DEVICE_CNTL2_IDO_COMPLETION_ENABLE_MASK                0x200

#define DEVICE_CNTL2_LTR_EN_OFFSET                             10
#define DEVICE_CNTL2_LTR_EN_MASK                               0x400

#define DEVICE_CNTL2_EMER_POWER_REDUCTION_REQUEST_OFFSET       11
#define DEVICE_CNTL2_EMER_POWER_REDUCTION_REQUEST_MASK         0x800

#define DEVICE_CNTL2_TEN_BIT_TAG_REQUESTER_ENABLE_OFFSET       12
#define DEVICE_CNTL2_TEN_BIT_TAG_REQUESTER_ENABLE_MASK         0x1000

#define DEVICE_CNTL2_OBFF_EN_OFFSET                            13
#define DEVICE_CNTL2_OBFF_EN_MASK                              0x6000

#define DEVICE_CNTL2_END_END_TLP_PREFIX_BLOCKING_OFFSET        15
#define DEVICE_CNTL2_END_END_TLP_PREFIX_BLOCKING_MASK          0x8000

typedef union {
  struct {
    UINT16                                   CPL_TIMEOUT_VALUE:4;
    UINT16                                     CPL_TIMEOUT_DIS:1;
    UINT16                                   ARI_FORWARDING_EN:1;
    UINT16                                 ATOMICOP_REQUEST_EN:1;
    UINT16                            ATOMICOP_EGRESS_BLOCKING:1;
    UINT16                                  IDO_REQUEST_ENABLE:1;
    UINT16                               IDO_COMPLETION_ENABLE:1;
    UINT16                                              LTR_EN:1;
    UINT16                        EMER_POWER_REDUCTION_REQUEST:1;
    UINT16                        TEN_BIT_TAG_REQUESTER_ENABLE:1;
    UINT16                                             OBFF_EN:2;
    UINT16                         END_END_TLP_PREFIX_BLOCKING:1;
  } Field;
  UINT16 Value;
} DEVICE_CNTL2_NBIFEPF0CFG_STRUCT;

#define PCICFG_NBIFEPF0CFG_DEVICE_CNTL2_OFFSET                      0x8c
#define SMN_DEV0_FUNC0_NBIF0NBIO0_DEVICE_CNTL2_ADDRESS                0x1014008cUL
#define SMN_DEV0_FUNC0_NBIF0NBIO1_DEVICE_CNTL2_ADDRESS                0x1034008cUL
#define SMN_DEV0_FUNC0_NBIF1NBIO0_DEVICE_CNTL2_ADDRESS                0x1024008cUL
#define SMN_DEV0_FUNC0_NBIF1NBIO1_DEVICE_CNTL2_ADDRESS                0x1044008cUL
#define SMN_DEV0_FUNC0_NBIF2NBIO0_DEVICE_CNTL2_ADDRESS                0x1054008cUL
#define SMN_DEV0_FUNC0_NBIF2NBIO1_DEVICE_CNTL2_ADDRESS                0x1074008cUL
#define SMN_DEV1_FUNC0_NBIF0NBIO0_DEVICE_CNTL2_ADDRESS                0x1014808cUL
#define SMN_DEV1_FUNC0_NBIF0NBIO1_DEVICE_CNTL2_ADDRESS                0x1034808cUL
#define SMN_DEV1_FUNC0_NBIF1NBIO0_DEVICE_CNTL2_ADDRESS                0x1024808cUL
#define SMN_DEV1_FUNC0_NBIF1NBIO1_DEVICE_CNTL2_ADDRESS                0x1044808cUL


/***********************************************************
* Register Name : DEVICE_ID
************************************************************/

#define DEVICE_ID_DEVICE_ID_OFFSET                             0
#define DEVICE_ID_DEVICE_ID_MASK                               0xffff

typedef union {
  struct {
    UINT16                                           DEVICE_ID:16;
  } Field;
  UINT16 Value;
} DEVICE_ID_STRUCT;

#define PCICFG_NBIFEPF0CFG_DEVICE_ID_OFFSET                         0x2
#define SMN_DEV0_FUNC0_NBIF0NBIO0_DEVICE_ID_ADDRESS                   0x10140002UL
#define SMN_DEV0_FUNC0_NBIF0NBIO1_DEVICE_ID_ADDRESS                   0x10340002UL
#define SMN_DEV0_FUNC0_NBIF1NBIO0_DEVICE_ID_ADDRESS                   0x10240002UL
#define SMN_DEV0_FUNC0_NBIF1NBIO1_DEVICE_ID_ADDRESS                   0x10440002UL
#define SMN_DEV0_FUNC0_NBIF2NBIO0_DEVICE_ID_ADDRESS                   0x10540002UL
#define SMN_DEV0_FUNC0_NBIF2NBIO1_DEVICE_ID_ADDRESS                   0x10740002UL
#define SMN_DEV1_FUNC0_NBIF0NBIO0_DEVICE_ID_ADDRESS                   0x10148002UL
#define SMN_DEV1_FUNC0_NBIF0NBIO1_DEVICE_ID_ADDRESS                   0x10348002UL
#define SMN_DEV1_FUNC0_NBIF1NBIO0_DEVICE_ID_ADDRESS                   0x10248002UL
#define SMN_DEV1_FUNC0_NBIF1NBIO1_DEVICE_ID_ADDRESS                   0x10448002UL


/***********************************************************
* Register Name : DEVICE_STATUS
************************************************************/

#define DEVICE_STATUS_CORR_ERR_OFFSET                          0
#define DEVICE_STATUS_CORR_ERR_MASK                            0x1

#define DEVICE_STATUS_NON_FATAL_ERR_OFFSET                     1
#define DEVICE_STATUS_NON_FATAL_ERR_MASK                       0x2

#define DEVICE_STATUS_FATAL_ERR_OFFSET                         2
#define DEVICE_STATUS_FATAL_ERR_MASK                           0x4

#define DEVICE_STATUS_USR_DETECTED_OFFSET                      3
#define DEVICE_STATUS_USR_DETECTED_MASK                        0x8

#define DEVICE_STATUS_AUX_PWR_OFFSET                           4
#define DEVICE_STATUS_AUX_PWR_MASK                             0x10

#define DEVICE_STATUS_TRANSACTIONS_PEND_OFFSET                 5
#define DEVICE_STATUS_TRANSACTIONS_PEND_MASK                   0x20

#define DEVICE_STATUS_EMER_POWER_REDUCTION_DETECTED_OFFSET     6
#define DEVICE_STATUS_EMER_POWER_REDUCTION_DETECTED_MASK       0x40

#define DEVICE_STATUS_Reserved_15_7_OFFSET                     7
#define DEVICE_STATUS_Reserved_15_7_MASK                       0xff80

typedef union {
  struct {
    UINT16                                            CORR_ERR:1;
    UINT16                                       NON_FATAL_ERR:1;
    UINT16                                           FATAL_ERR:1;
    UINT16                                        USR_DETECTED:1;
    UINT16                                             AUX_PWR:1;
    UINT16                                   TRANSACTIONS_PEND:1;
    UINT16                       EMER_POWER_REDUCTION_DETECTED:1;
    UINT16                                       Reserved_15_7:9;
  } Field;
  UINT16 Value;
} DEVICE_STATUS_STRUCT;

#define PCICFG_NBIFEPF0CFG_DEVICE_STATUS_OFFSET                     0x6e
#define SMN_DEV0_FUNC0_NBIF0NBIO0_DEVICE_STATUS_ADDRESS               0x1014006eUL
#define SMN_DEV0_FUNC0_NBIF0NBIO1_DEVICE_STATUS_ADDRESS               0x1034006eUL
#define SMN_DEV0_FUNC0_NBIF1NBIO0_DEVICE_STATUS_ADDRESS               0x1024006eUL
#define SMN_DEV0_FUNC0_NBIF1NBIO1_DEVICE_STATUS_ADDRESS               0x1044006eUL
#define SMN_DEV0_FUNC0_NBIF2NBIO0_DEVICE_STATUS_ADDRESS               0x1054006eUL
#define SMN_DEV0_FUNC0_NBIF2NBIO1_DEVICE_STATUS_ADDRESS               0x1074006eUL
#define SMN_DEV1_FUNC0_NBIF0NBIO0_DEVICE_STATUS_ADDRESS               0x1014806eUL
#define SMN_DEV1_FUNC0_NBIF0NBIO1_DEVICE_STATUS_ADDRESS               0x1034806eUL
#define SMN_DEV1_FUNC0_NBIF1NBIO0_DEVICE_STATUS_ADDRESS               0x1024806eUL
#define SMN_DEV1_FUNC0_NBIF1NBIO1_DEVICE_STATUS_ADDRESS               0x1044806eUL


/***********************************************************
* Register Name : DEVICE_STATUS2
************************************************************/

#define DEVICE_STATUS2_Reserved_15_0_OFFSET                    0
#define DEVICE_STATUS2_Reserved_15_0_MASK                      0xffff

typedef union {
  struct {
    UINT16                                       Reserved_15_0:16;
  } Field;
  UINT16 Value;
} DEVICE_STATUS2_STRUCT;

#define PCICFG_NBIFEPF0CFG_DEVICE_STATUS2_OFFSET                    0x8e
#define SMN_DEV0_FUNC0_NBIF0NBIO0_DEVICE_STATUS2_ADDRESS              0x1014008eUL
#define SMN_DEV0_FUNC0_NBIF0NBIO1_DEVICE_STATUS2_ADDRESS              0x1034008eUL
#define SMN_DEV0_FUNC0_NBIF1NBIO0_DEVICE_STATUS2_ADDRESS              0x1024008eUL
#define SMN_DEV0_FUNC0_NBIF1NBIO1_DEVICE_STATUS2_ADDRESS              0x1044008eUL
#define SMN_DEV0_FUNC0_NBIF2NBIO0_DEVICE_STATUS2_ADDRESS              0x1054008eUL
#define SMN_DEV0_FUNC0_NBIF2NBIO1_DEVICE_STATUS2_ADDRESS              0x1074008eUL
#define SMN_DEV1_FUNC0_NBIF0NBIO0_DEVICE_STATUS2_ADDRESS              0x1014808eUL
#define SMN_DEV1_FUNC0_NBIF0NBIO1_DEVICE_STATUS2_ADDRESS              0x1034808eUL
#define SMN_DEV1_FUNC0_NBIF1NBIO0_DEVICE_STATUS2_ADDRESS              0x1024808eUL
#define SMN_DEV1_FUNC0_NBIF1NBIO1_DEVICE_STATUS2_ADDRESS              0x1044808eUL


/***********************************************************
* Register Name : FLADJ
************************************************************/

#define FLADJ_FLADJ_OFFSET                                     0
#define FLADJ_FLADJ_MASK                                       0x3f

#define FLADJ_NFC_OFFSET                                       6
#define FLADJ_NFC_MASK                                         0x40

#define FLADJ_Reserved_7_7_OFFSET                              7
#define FLADJ_Reserved_7_7_MASK                                0x80

typedef union {
  struct {
    UINT8                                               FLADJ:6;
    UINT8                                                 NFC:1;
    UINT8                                        Reserved_7_7:1;
  } Field;
  UINT8 Value;
} FLADJ_STRUCT;

#define PCICFG_NBIFEPF0CFG_FLADJ_OFFSET                             0x61
#define SMN_DEV1_FUNC0_NBIF0NBIO0_FLADJ_ADDRESS                       0x10148061UL
#define SMN_DEV1_FUNC0_NBIF0NBIO1_FLADJ_ADDRESS                       0x10348061UL
#define SMN_DEV1_FUNC0_NBIF1NBIO0_FLADJ_ADDRESS                       0x10248061UL
#define SMN_DEV1_FUNC0_NBIF1NBIO1_FLADJ_ADDRESS                       0x10448061UL


/***********************************************************
* Register Name : HEADER
************************************************************/

#define HEADER_HEADER_TYPE_OFFSET                              0
#define HEADER_HEADER_TYPE_MASK                                0x7f

#define HEADER_DEVICE_TYPE_OFFSET                              7
#define HEADER_DEVICE_TYPE_MASK                                0x80

typedef union {
  struct {
    UINT8                                         HEADER_TYPE:7;
    UINT8                                         DEVICE_TYPE:1;
  } Field;
  UINT8 Value;
} HEADER_STRUCT;

#define PCICFG_NBIFEPF0CFG_HEADER_OFFSET                            0xe
#define SMN_DEV0_FUNC0_NBIF0NBIO0_HEADER_ADDRESS                      0x1014000eUL
#define SMN_DEV0_FUNC0_NBIF0NBIO1_HEADER_ADDRESS                      0x1034000eUL
#define SMN_DEV0_FUNC0_NBIF1NBIO0_HEADER_ADDRESS                      0x1024000eUL
#define SMN_DEV0_FUNC0_NBIF1NBIO1_HEADER_ADDRESS                      0x1044000eUL
#define SMN_DEV0_FUNC0_NBIF2NBIO0_HEADER_ADDRESS                      0x1054000eUL
#define SMN_DEV0_FUNC0_NBIF2NBIO1_HEADER_ADDRESS                      0x1074000eUL
#define SMN_DEV1_FUNC0_NBIF0NBIO0_HEADER_ADDRESS                      0x1014800eUL
#define SMN_DEV1_FUNC0_NBIF0NBIO1_HEADER_ADDRESS                      0x1034800eUL
#define SMN_DEV1_FUNC0_NBIF1NBIO0_HEADER_ADDRESS                      0x1024800eUL
#define SMN_DEV1_FUNC0_NBIF1NBIO1_HEADER_ADDRESS                      0x1044800eUL


/***********************************************************
* Register Name : INTERRUPT_LINE
************************************************************/

#define INTERRUPT_LINE_INTERRUPT_LINE_OFFSET                   0
#define INTERRUPT_LINE_INTERRUPT_LINE_MASK                     0xff

typedef union {
  struct {
    UINT8                                      INTERRUPT_LINE:8;
  } Field;
  UINT8 Value;
} INTERRUPT_LINE_STRUCT;

#define PCICFG_NBIFEPF0CFG_INTERRUPT_LINE_OFFSET                    0x3c
#define SMN_DEV0_FUNC0_NBIF0NBIO0_INTERRUPT_LINE_ADDRESS              0x1014003cUL
#define SMN_DEV0_FUNC0_NBIF0NBIO1_INTERRUPT_LINE_ADDRESS              0x1034003cUL
#define SMN_DEV0_FUNC0_NBIF1NBIO0_INTERRUPT_LINE_ADDRESS              0x1024003cUL
#define SMN_DEV0_FUNC0_NBIF1NBIO1_INTERRUPT_LINE_ADDRESS              0x1044003cUL
#define SMN_DEV0_FUNC0_NBIF2NBIO0_INTERRUPT_LINE_ADDRESS              0x1054003cUL
#define SMN_DEV0_FUNC0_NBIF2NBIO1_INTERRUPT_LINE_ADDRESS              0x1074003cUL
#define SMN_DEV1_FUNC0_NBIF0NBIO0_INTERRUPT_LINE_ADDRESS              0x1014803cUL
#define SMN_DEV1_FUNC0_NBIF0NBIO1_INTERRUPT_LINE_ADDRESS              0x1034803cUL
#define SMN_DEV1_FUNC0_NBIF1NBIO0_INTERRUPT_LINE_ADDRESS              0x1024803cUL
#define SMN_DEV1_FUNC0_NBIF1NBIO1_INTERRUPT_LINE_ADDRESS              0x1044803cUL


/***********************************************************
* Register Name : INTERRUPT_PIN
************************************************************/

#define INTERRUPT_PIN_INTERRUPT_PIN_OFFSET                     0
#define INTERRUPT_PIN_INTERRUPT_PIN_MASK                       0xff

typedef union {
  struct {
    UINT8                                       INTERRUPT_PIN:8;
  } Field;
  UINT8 Value;
} INTERRUPT_PIN_STRUCT;

#define PCICFG_NBIFEPF0CFG_INTERRUPT_PIN_OFFSET                     0x3d
#define SMN_DEV0_FUNC0_NBIF0NBIO0_INTERRUPT_PIN_ADDRESS               0x1014003dUL
#define SMN_DEV0_FUNC0_NBIF0NBIO1_INTERRUPT_PIN_ADDRESS               0x1034003dUL
#define SMN_DEV0_FUNC0_NBIF1NBIO0_INTERRUPT_PIN_ADDRESS               0x1024003dUL
#define SMN_DEV0_FUNC0_NBIF1NBIO1_INTERRUPT_PIN_ADDRESS               0x1044003dUL
#define SMN_DEV0_FUNC0_NBIF2NBIO0_INTERRUPT_PIN_ADDRESS               0x1054003dUL
#define SMN_DEV0_FUNC0_NBIF2NBIO1_INTERRUPT_PIN_ADDRESS               0x1074003dUL
#define SMN_DEV1_FUNC0_NBIF0NBIO0_INTERRUPT_PIN_ADDRESS               0x1014803dUL
#define SMN_DEV1_FUNC0_NBIF0NBIO1_INTERRUPT_PIN_ADDRESS               0x1034803dUL
#define SMN_DEV1_FUNC0_NBIF1NBIO0_INTERRUPT_PIN_ADDRESS               0x1024803dUL
#define SMN_DEV1_FUNC0_NBIF1NBIO1_INTERRUPT_PIN_ADDRESS               0x1044803dUL


/***********************************************************
* Register Name : LANE_EQUALIZATION_CNTL_16GT
************************************************************/

#define LANE_EQUALIZATION_CNTL_16GT_LANE_DSP_16GT_TX_PRESET_OFFSET 0
#define LANE_EQUALIZATION_CNTL_16GT_LANE_DSP_16GT_TX_PRESET_MASK 0xf

#define LANE_EQUALIZATION_CNTL_16GT_LANE_USP_16GT_TX_PRESET_OFFSET 4
#define LANE_EQUALIZATION_CNTL_16GT_LANE_USP_16GT_TX_PRESET_MASK 0xf0

typedef union {
  struct {
    UINT8                             LANE_DSP_16GT_TX_PRESET:4;
    UINT8                             LANE_USP_16GT_TX_PRESET:4;
  } Field;
  UINT8 Value;
} LANE_EQUALIZATION_CNTL_16GT_STRUCT;

#define PCICFG_NBIFEPF0CFG_LANE_EQUALIZATION_CNTL_16GT_OFFSET       0x430
#define SMN_DEV0_FUNC0_NBIF0_N0NBIO0_LANE_EQUALIZATION_CNTL_16GT_ADDRESS 0x10140430UL
#define SMN_DEV0_FUNC0_NBIF0_N0NBIO1_LANE_EQUALIZATION_CNTL_16GT_ADDRESS 0x10340430UL
#define SMN_DEV0_FUNC0_NBIF0_N10NBIO0_LANE_EQUALIZATION_CNTL_16GT_ADDRESS 0x1014043aUL
#define SMN_DEV0_FUNC0_NBIF0_N10NBIO1_LANE_EQUALIZATION_CNTL_16GT_ADDRESS 0x1034043aUL
#define SMN_DEV0_FUNC0_NBIF0_N11NBIO0_LANE_EQUALIZATION_CNTL_16GT_ADDRESS 0x1014043bUL
#define SMN_DEV0_FUNC0_NBIF0_N11NBIO1_LANE_EQUALIZATION_CNTL_16GT_ADDRESS 0x1034043bUL
#define SMN_DEV0_FUNC0_NBIF0_N12NBIO0_LANE_EQUALIZATION_CNTL_16GT_ADDRESS 0x1014043cUL
#define SMN_DEV0_FUNC0_NBIF0_N12NBIO1_LANE_EQUALIZATION_CNTL_16GT_ADDRESS 0x1034043cUL
#define SMN_DEV0_FUNC0_NBIF0_N13NBIO0_LANE_EQUALIZATION_CNTL_16GT_ADDRESS 0x1014043dUL
#define SMN_DEV0_FUNC0_NBIF0_N13NBIO1_LANE_EQUALIZATION_CNTL_16GT_ADDRESS 0x1034043dUL
#define SMN_DEV0_FUNC0_NBIF0_N14NBIO0_LANE_EQUALIZATION_CNTL_16GT_ADDRESS 0x1014043eUL
#define SMN_DEV0_FUNC0_NBIF0_N14NBIO1_LANE_EQUALIZATION_CNTL_16GT_ADDRESS 0x1034043eUL
#define SMN_DEV0_FUNC0_NBIF0_N15NBIO0_LANE_EQUALIZATION_CNTL_16GT_ADDRESS 0x1014043fUL
#define SMN_DEV0_FUNC0_NBIF0_N15NBIO1_LANE_EQUALIZATION_CNTL_16GT_ADDRESS 0x1034043fUL
#define SMN_DEV0_FUNC0_NBIF0_N1NBIO0_LANE_EQUALIZATION_CNTL_16GT_ADDRESS 0x10140431UL
#define SMN_DEV0_FUNC0_NBIF0_N1NBIO1_LANE_EQUALIZATION_CNTL_16GT_ADDRESS 0x10340431UL
#define SMN_DEV0_FUNC0_NBIF0_N2NBIO0_LANE_EQUALIZATION_CNTL_16GT_ADDRESS 0x10140432UL
#define SMN_DEV0_FUNC0_NBIF0_N2NBIO1_LANE_EQUALIZATION_CNTL_16GT_ADDRESS 0x10340432UL
#define SMN_DEV0_FUNC0_NBIF0_N3NBIO0_LANE_EQUALIZATION_CNTL_16GT_ADDRESS 0x10140433UL
#define SMN_DEV0_FUNC0_NBIF0_N3NBIO1_LANE_EQUALIZATION_CNTL_16GT_ADDRESS 0x10340433UL
#define SMN_DEV0_FUNC0_NBIF0_N4NBIO0_LANE_EQUALIZATION_CNTL_16GT_ADDRESS 0x10140434UL
#define SMN_DEV0_FUNC0_NBIF0_N4NBIO1_LANE_EQUALIZATION_CNTL_16GT_ADDRESS 0x10340434UL
#define SMN_DEV0_FUNC0_NBIF0_N5NBIO0_LANE_EQUALIZATION_CNTL_16GT_ADDRESS 0x10140435UL
#define SMN_DEV0_FUNC0_NBIF0_N5NBIO1_LANE_EQUALIZATION_CNTL_16GT_ADDRESS 0x10340435UL
#define SMN_DEV0_FUNC0_NBIF0_N6NBIO0_LANE_EQUALIZATION_CNTL_16GT_ADDRESS 0x10140436UL
#define SMN_DEV0_FUNC0_NBIF0_N6NBIO1_LANE_EQUALIZATION_CNTL_16GT_ADDRESS 0x10340436UL
#define SMN_DEV0_FUNC0_NBIF0_N7NBIO0_LANE_EQUALIZATION_CNTL_16GT_ADDRESS 0x10140437UL
#define SMN_DEV0_FUNC0_NBIF0_N7NBIO1_LANE_EQUALIZATION_CNTL_16GT_ADDRESS 0x10340437UL
#define SMN_DEV0_FUNC0_NBIF0_N8NBIO0_LANE_EQUALIZATION_CNTL_16GT_ADDRESS 0x10140438UL
#define SMN_DEV0_FUNC0_NBIF0_N8NBIO1_LANE_EQUALIZATION_CNTL_16GT_ADDRESS 0x10340438UL
#define SMN_DEV0_FUNC0_NBIF0_N9NBIO0_LANE_EQUALIZATION_CNTL_16GT_ADDRESS 0x10140439UL
#define SMN_DEV0_FUNC0_NBIF0_N9NBIO1_LANE_EQUALIZATION_CNTL_16GT_ADDRESS 0x10340439UL
#define SMN_DEV0_FUNC0_NBIF1_N0NBIO0_LANE_EQUALIZATION_CNTL_16GT_ADDRESS 0x10240430UL
#define SMN_DEV0_FUNC0_NBIF1_N0NBIO1_LANE_EQUALIZATION_CNTL_16GT_ADDRESS 0x10440430UL
#define SMN_DEV0_FUNC0_NBIF1_N10NBIO0_LANE_EQUALIZATION_CNTL_16GT_ADDRESS 0x1024043aUL
#define SMN_DEV0_FUNC0_NBIF1_N10NBIO1_LANE_EQUALIZATION_CNTL_16GT_ADDRESS 0x1044043aUL
#define SMN_DEV0_FUNC0_NBIF1_N11NBIO0_LANE_EQUALIZATION_CNTL_16GT_ADDRESS 0x1024043bUL
#define SMN_DEV0_FUNC0_NBIF1_N11NBIO1_LANE_EQUALIZATION_CNTL_16GT_ADDRESS 0x1044043bUL
#define SMN_DEV0_FUNC0_NBIF1_N12NBIO0_LANE_EQUALIZATION_CNTL_16GT_ADDRESS 0x1024043cUL
#define SMN_DEV0_FUNC0_NBIF1_N12NBIO1_LANE_EQUALIZATION_CNTL_16GT_ADDRESS 0x1044043cUL
#define SMN_DEV0_FUNC0_NBIF1_N13NBIO0_LANE_EQUALIZATION_CNTL_16GT_ADDRESS 0x1024043dUL
#define SMN_DEV0_FUNC0_NBIF1_N13NBIO1_LANE_EQUALIZATION_CNTL_16GT_ADDRESS 0x1044043dUL
#define SMN_DEV0_FUNC0_NBIF1_N14NBIO0_LANE_EQUALIZATION_CNTL_16GT_ADDRESS 0x1024043eUL
#define SMN_DEV0_FUNC0_NBIF1_N14NBIO1_LANE_EQUALIZATION_CNTL_16GT_ADDRESS 0x1044043eUL
#define SMN_DEV0_FUNC0_NBIF1_N15NBIO0_LANE_EQUALIZATION_CNTL_16GT_ADDRESS 0x1024043fUL
#define SMN_DEV0_FUNC0_NBIF1_N15NBIO1_LANE_EQUALIZATION_CNTL_16GT_ADDRESS 0x1044043fUL
#define SMN_DEV0_FUNC0_NBIF1_N1NBIO0_LANE_EQUALIZATION_CNTL_16GT_ADDRESS 0x10240431UL
#define SMN_DEV0_FUNC0_NBIF1_N1NBIO1_LANE_EQUALIZATION_CNTL_16GT_ADDRESS 0x10440431UL
#define SMN_DEV0_FUNC0_NBIF1_N2NBIO0_LANE_EQUALIZATION_CNTL_16GT_ADDRESS 0x10240432UL
#define SMN_DEV0_FUNC0_NBIF1_N2NBIO1_LANE_EQUALIZATION_CNTL_16GT_ADDRESS 0x10440432UL
#define SMN_DEV0_FUNC0_NBIF1_N3NBIO0_LANE_EQUALIZATION_CNTL_16GT_ADDRESS 0x10240433UL
#define SMN_DEV0_FUNC0_NBIF1_N3NBIO1_LANE_EQUALIZATION_CNTL_16GT_ADDRESS 0x10440433UL
#define SMN_DEV0_FUNC0_NBIF1_N4NBIO0_LANE_EQUALIZATION_CNTL_16GT_ADDRESS 0x10240434UL
#define SMN_DEV0_FUNC0_NBIF1_N4NBIO1_LANE_EQUALIZATION_CNTL_16GT_ADDRESS 0x10440434UL
#define SMN_DEV0_FUNC0_NBIF1_N5NBIO0_LANE_EQUALIZATION_CNTL_16GT_ADDRESS 0x10240435UL
#define SMN_DEV0_FUNC0_NBIF1_N5NBIO1_LANE_EQUALIZATION_CNTL_16GT_ADDRESS 0x10440435UL
#define SMN_DEV0_FUNC0_NBIF1_N6NBIO0_LANE_EQUALIZATION_CNTL_16GT_ADDRESS 0x10240436UL
#define SMN_DEV0_FUNC0_NBIF1_N6NBIO1_LANE_EQUALIZATION_CNTL_16GT_ADDRESS 0x10440436UL
#define SMN_DEV0_FUNC0_NBIF1_N7NBIO0_LANE_EQUALIZATION_CNTL_16GT_ADDRESS 0x10240437UL
#define SMN_DEV0_FUNC0_NBIF1_N7NBIO1_LANE_EQUALIZATION_CNTL_16GT_ADDRESS 0x10440437UL
#define SMN_DEV0_FUNC0_NBIF1_N8NBIO0_LANE_EQUALIZATION_CNTL_16GT_ADDRESS 0x10240438UL
#define SMN_DEV0_FUNC0_NBIF1_N8NBIO1_LANE_EQUALIZATION_CNTL_16GT_ADDRESS 0x10440438UL
#define SMN_DEV0_FUNC0_NBIF1_N9NBIO0_LANE_EQUALIZATION_CNTL_16GT_ADDRESS 0x10240439UL
#define SMN_DEV0_FUNC0_NBIF1_N9NBIO1_LANE_EQUALIZATION_CNTL_16GT_ADDRESS 0x10440439UL
#define SMN_DEV0_FUNC0_NBIF2_N0NBIO0_LANE_EQUALIZATION_CNTL_16GT_ADDRESS 0x10540430UL
#define SMN_DEV0_FUNC0_NBIF2_N0NBIO1_LANE_EQUALIZATION_CNTL_16GT_ADDRESS 0x10740430UL
#define SMN_DEV0_FUNC0_NBIF2_N10NBIO0_LANE_EQUALIZATION_CNTL_16GT_ADDRESS 0x1054043aUL
#define SMN_DEV0_FUNC0_NBIF2_N10NBIO1_LANE_EQUALIZATION_CNTL_16GT_ADDRESS 0x1074043aUL
#define SMN_DEV0_FUNC0_NBIF2_N11NBIO0_LANE_EQUALIZATION_CNTL_16GT_ADDRESS 0x1054043bUL
#define SMN_DEV0_FUNC0_NBIF2_N11NBIO1_LANE_EQUALIZATION_CNTL_16GT_ADDRESS 0x1074043bUL
#define SMN_DEV0_FUNC0_NBIF2_N12NBIO0_LANE_EQUALIZATION_CNTL_16GT_ADDRESS 0x1054043cUL
#define SMN_DEV0_FUNC0_NBIF2_N12NBIO1_LANE_EQUALIZATION_CNTL_16GT_ADDRESS 0x1074043cUL
#define SMN_DEV0_FUNC0_NBIF2_N13NBIO0_LANE_EQUALIZATION_CNTL_16GT_ADDRESS 0x1054043dUL
#define SMN_DEV0_FUNC0_NBIF2_N13NBIO1_LANE_EQUALIZATION_CNTL_16GT_ADDRESS 0x1074043dUL
#define SMN_DEV0_FUNC0_NBIF2_N14NBIO0_LANE_EQUALIZATION_CNTL_16GT_ADDRESS 0x1054043eUL
#define SMN_DEV0_FUNC0_NBIF2_N14NBIO1_LANE_EQUALIZATION_CNTL_16GT_ADDRESS 0x1074043eUL
#define SMN_DEV0_FUNC0_NBIF2_N15NBIO0_LANE_EQUALIZATION_CNTL_16GT_ADDRESS 0x1054043fUL
#define SMN_DEV0_FUNC0_NBIF2_N15NBIO1_LANE_EQUALIZATION_CNTL_16GT_ADDRESS 0x1074043fUL
#define SMN_DEV0_FUNC0_NBIF2_N1NBIO0_LANE_EQUALIZATION_CNTL_16GT_ADDRESS 0x10540431UL
#define SMN_DEV0_FUNC0_NBIF2_N1NBIO1_LANE_EQUALIZATION_CNTL_16GT_ADDRESS 0x10740431UL
#define SMN_DEV0_FUNC0_NBIF2_N2NBIO0_LANE_EQUALIZATION_CNTL_16GT_ADDRESS 0x10540432UL
#define SMN_DEV0_FUNC0_NBIF2_N2NBIO1_LANE_EQUALIZATION_CNTL_16GT_ADDRESS 0x10740432UL
#define SMN_DEV0_FUNC0_NBIF2_N3NBIO0_LANE_EQUALIZATION_CNTL_16GT_ADDRESS 0x10540433UL
#define SMN_DEV0_FUNC0_NBIF2_N3NBIO1_LANE_EQUALIZATION_CNTL_16GT_ADDRESS 0x10740433UL
#define SMN_DEV0_FUNC0_NBIF2_N4NBIO0_LANE_EQUALIZATION_CNTL_16GT_ADDRESS 0x10540434UL
#define SMN_DEV0_FUNC0_NBIF2_N4NBIO1_LANE_EQUALIZATION_CNTL_16GT_ADDRESS 0x10740434UL
#define SMN_DEV0_FUNC0_NBIF2_N5NBIO0_LANE_EQUALIZATION_CNTL_16GT_ADDRESS 0x10540435UL
#define SMN_DEV0_FUNC0_NBIF2_N5NBIO1_LANE_EQUALIZATION_CNTL_16GT_ADDRESS 0x10740435UL
#define SMN_DEV0_FUNC0_NBIF2_N6NBIO0_LANE_EQUALIZATION_CNTL_16GT_ADDRESS 0x10540436UL
#define SMN_DEV0_FUNC0_NBIF2_N6NBIO1_LANE_EQUALIZATION_CNTL_16GT_ADDRESS 0x10740436UL
#define SMN_DEV0_FUNC0_NBIF2_N7NBIO0_LANE_EQUALIZATION_CNTL_16GT_ADDRESS 0x10540437UL
#define SMN_DEV0_FUNC0_NBIF2_N7NBIO1_LANE_EQUALIZATION_CNTL_16GT_ADDRESS 0x10740437UL
#define SMN_DEV0_FUNC0_NBIF2_N8NBIO0_LANE_EQUALIZATION_CNTL_16GT_ADDRESS 0x10540438UL
#define SMN_DEV0_FUNC0_NBIF2_N8NBIO1_LANE_EQUALIZATION_CNTL_16GT_ADDRESS 0x10740438UL
#define SMN_DEV0_FUNC0_NBIF2_N9NBIO0_LANE_EQUALIZATION_CNTL_16GT_ADDRESS 0x10540439UL
#define SMN_DEV0_FUNC0_NBIF2_N9NBIO1_LANE_EQUALIZATION_CNTL_16GT_ADDRESS 0x10740439UL
#define SMN_DEV1_FUNC0_NBIF0_N0NBIO0_LANE_EQUALIZATION_CNTL_16GT_ADDRESS 0x10148430UL
#define SMN_DEV1_FUNC0_NBIF0_N0NBIO1_LANE_EQUALIZATION_CNTL_16GT_ADDRESS 0x10348430UL
#define SMN_DEV1_FUNC0_NBIF0_N10NBIO0_LANE_EQUALIZATION_CNTL_16GT_ADDRESS 0x1014843aUL
#define SMN_DEV1_FUNC0_NBIF0_N10NBIO1_LANE_EQUALIZATION_CNTL_16GT_ADDRESS 0x1034843aUL
#define SMN_DEV1_FUNC0_NBIF0_N11NBIO0_LANE_EQUALIZATION_CNTL_16GT_ADDRESS 0x1014843bUL
#define SMN_DEV1_FUNC0_NBIF0_N11NBIO1_LANE_EQUALIZATION_CNTL_16GT_ADDRESS 0x1034843bUL
#define SMN_DEV1_FUNC0_NBIF0_N12NBIO0_LANE_EQUALIZATION_CNTL_16GT_ADDRESS 0x1014843cUL
#define SMN_DEV1_FUNC0_NBIF0_N12NBIO1_LANE_EQUALIZATION_CNTL_16GT_ADDRESS 0x1034843cUL
#define SMN_DEV1_FUNC0_NBIF0_N13NBIO0_LANE_EQUALIZATION_CNTL_16GT_ADDRESS 0x1014843dUL
#define SMN_DEV1_FUNC0_NBIF0_N13NBIO1_LANE_EQUALIZATION_CNTL_16GT_ADDRESS 0x1034843dUL
#define SMN_DEV1_FUNC0_NBIF0_N14NBIO0_LANE_EQUALIZATION_CNTL_16GT_ADDRESS 0x1014843eUL
#define SMN_DEV1_FUNC0_NBIF0_N14NBIO1_LANE_EQUALIZATION_CNTL_16GT_ADDRESS 0x1034843eUL
#define SMN_DEV1_FUNC0_NBIF0_N15NBIO0_LANE_EQUALIZATION_CNTL_16GT_ADDRESS 0x1014843fUL
#define SMN_DEV1_FUNC0_NBIF0_N15NBIO1_LANE_EQUALIZATION_CNTL_16GT_ADDRESS 0x1034843fUL
#define SMN_DEV1_FUNC0_NBIF0_N1NBIO0_LANE_EQUALIZATION_CNTL_16GT_ADDRESS 0x10148431UL
#define SMN_DEV1_FUNC0_NBIF0_N1NBIO1_LANE_EQUALIZATION_CNTL_16GT_ADDRESS 0x10348431UL
#define SMN_DEV1_FUNC0_NBIF0_N2NBIO0_LANE_EQUALIZATION_CNTL_16GT_ADDRESS 0x10148432UL
#define SMN_DEV1_FUNC0_NBIF0_N2NBIO1_LANE_EQUALIZATION_CNTL_16GT_ADDRESS 0x10348432UL
#define SMN_DEV1_FUNC0_NBIF0_N3NBIO0_LANE_EQUALIZATION_CNTL_16GT_ADDRESS 0x10148433UL
#define SMN_DEV1_FUNC0_NBIF0_N3NBIO1_LANE_EQUALIZATION_CNTL_16GT_ADDRESS 0x10348433UL
#define SMN_DEV1_FUNC0_NBIF0_N4NBIO0_LANE_EQUALIZATION_CNTL_16GT_ADDRESS 0x10148434UL
#define SMN_DEV1_FUNC0_NBIF0_N4NBIO1_LANE_EQUALIZATION_CNTL_16GT_ADDRESS 0x10348434UL
#define SMN_DEV1_FUNC0_NBIF0_N5NBIO0_LANE_EQUALIZATION_CNTL_16GT_ADDRESS 0x10148435UL
#define SMN_DEV1_FUNC0_NBIF0_N5NBIO1_LANE_EQUALIZATION_CNTL_16GT_ADDRESS 0x10348435UL
#define SMN_DEV1_FUNC0_NBIF0_N6NBIO0_LANE_EQUALIZATION_CNTL_16GT_ADDRESS 0x10148436UL
#define SMN_DEV1_FUNC0_NBIF0_N6NBIO1_LANE_EQUALIZATION_CNTL_16GT_ADDRESS 0x10348436UL
#define SMN_DEV1_FUNC0_NBIF0_N7NBIO0_LANE_EQUALIZATION_CNTL_16GT_ADDRESS 0x10148437UL
#define SMN_DEV1_FUNC0_NBIF0_N7NBIO1_LANE_EQUALIZATION_CNTL_16GT_ADDRESS 0x10348437UL
#define SMN_DEV1_FUNC0_NBIF0_N8NBIO0_LANE_EQUALIZATION_CNTL_16GT_ADDRESS 0x10148438UL
#define SMN_DEV1_FUNC0_NBIF0_N8NBIO1_LANE_EQUALIZATION_CNTL_16GT_ADDRESS 0x10348438UL
#define SMN_DEV1_FUNC0_NBIF0_N9NBIO0_LANE_EQUALIZATION_CNTL_16GT_ADDRESS 0x10148439UL
#define SMN_DEV1_FUNC0_NBIF0_N9NBIO1_LANE_EQUALIZATION_CNTL_16GT_ADDRESS 0x10348439UL
#define SMN_DEV1_FUNC0_NBIF1_N0NBIO0_LANE_EQUALIZATION_CNTL_16GT_ADDRESS 0x10248430UL
#define SMN_DEV1_FUNC0_NBIF1_N0NBIO1_LANE_EQUALIZATION_CNTL_16GT_ADDRESS 0x10448430UL
#define SMN_DEV1_FUNC0_NBIF1_N10NBIO0_LANE_EQUALIZATION_CNTL_16GT_ADDRESS 0x1024843aUL
#define SMN_DEV1_FUNC0_NBIF1_N10NBIO1_LANE_EQUALIZATION_CNTL_16GT_ADDRESS 0x1044843aUL
#define SMN_DEV1_FUNC0_NBIF1_N11NBIO0_LANE_EQUALIZATION_CNTL_16GT_ADDRESS 0x1024843bUL
#define SMN_DEV1_FUNC0_NBIF1_N11NBIO1_LANE_EQUALIZATION_CNTL_16GT_ADDRESS 0x1044843bUL
#define SMN_DEV1_FUNC0_NBIF1_N12NBIO0_LANE_EQUALIZATION_CNTL_16GT_ADDRESS 0x1024843cUL
#define SMN_DEV1_FUNC0_NBIF1_N12NBIO1_LANE_EQUALIZATION_CNTL_16GT_ADDRESS 0x1044843cUL
#define SMN_DEV1_FUNC0_NBIF1_N13NBIO0_LANE_EQUALIZATION_CNTL_16GT_ADDRESS 0x1024843dUL
#define SMN_DEV1_FUNC0_NBIF1_N13NBIO1_LANE_EQUALIZATION_CNTL_16GT_ADDRESS 0x1044843dUL
#define SMN_DEV1_FUNC0_NBIF1_N14NBIO0_LANE_EQUALIZATION_CNTL_16GT_ADDRESS 0x1024843eUL
#define SMN_DEV1_FUNC0_NBIF1_N14NBIO1_LANE_EQUALIZATION_CNTL_16GT_ADDRESS 0x1044843eUL
#define SMN_DEV1_FUNC0_NBIF1_N15NBIO0_LANE_EQUALIZATION_CNTL_16GT_ADDRESS 0x1024843fUL
#define SMN_DEV1_FUNC0_NBIF1_N15NBIO1_LANE_EQUALIZATION_CNTL_16GT_ADDRESS 0x1044843fUL
#define SMN_DEV1_FUNC0_NBIF1_N1NBIO0_LANE_EQUALIZATION_CNTL_16GT_ADDRESS 0x10248431UL
#define SMN_DEV1_FUNC0_NBIF1_N1NBIO1_LANE_EQUALIZATION_CNTL_16GT_ADDRESS 0x10448431UL
#define SMN_DEV1_FUNC0_NBIF1_N2NBIO0_LANE_EQUALIZATION_CNTL_16GT_ADDRESS 0x10248432UL
#define SMN_DEV1_FUNC0_NBIF1_N2NBIO1_LANE_EQUALIZATION_CNTL_16GT_ADDRESS 0x10448432UL
#define SMN_DEV1_FUNC0_NBIF1_N3NBIO0_LANE_EQUALIZATION_CNTL_16GT_ADDRESS 0x10248433UL
#define SMN_DEV1_FUNC0_NBIF1_N3NBIO1_LANE_EQUALIZATION_CNTL_16GT_ADDRESS 0x10448433UL
#define SMN_DEV1_FUNC0_NBIF1_N4NBIO0_LANE_EQUALIZATION_CNTL_16GT_ADDRESS 0x10248434UL
#define SMN_DEV1_FUNC0_NBIF1_N4NBIO1_LANE_EQUALIZATION_CNTL_16GT_ADDRESS 0x10448434UL
#define SMN_DEV1_FUNC0_NBIF1_N5NBIO0_LANE_EQUALIZATION_CNTL_16GT_ADDRESS 0x10248435UL
#define SMN_DEV1_FUNC0_NBIF1_N5NBIO1_LANE_EQUALIZATION_CNTL_16GT_ADDRESS 0x10448435UL
#define SMN_DEV1_FUNC0_NBIF1_N6NBIO0_LANE_EQUALIZATION_CNTL_16GT_ADDRESS 0x10248436UL
#define SMN_DEV1_FUNC0_NBIF1_N6NBIO1_LANE_EQUALIZATION_CNTL_16GT_ADDRESS 0x10448436UL
#define SMN_DEV1_FUNC0_NBIF1_N7NBIO0_LANE_EQUALIZATION_CNTL_16GT_ADDRESS 0x10248437UL
#define SMN_DEV1_FUNC0_NBIF1_N7NBIO1_LANE_EQUALIZATION_CNTL_16GT_ADDRESS 0x10448437UL
#define SMN_DEV1_FUNC0_NBIF1_N8NBIO0_LANE_EQUALIZATION_CNTL_16GT_ADDRESS 0x10248438UL
#define SMN_DEV1_FUNC0_NBIF1_N8NBIO1_LANE_EQUALIZATION_CNTL_16GT_ADDRESS 0x10448438UL
#define SMN_DEV1_FUNC0_NBIF1_N9NBIO0_LANE_EQUALIZATION_CNTL_16GT_ADDRESS 0x10248439UL
#define SMN_DEV1_FUNC0_NBIF1_N9NBIO1_LANE_EQUALIZATION_CNTL_16GT_ADDRESS 0x10448439UL


/***********************************************************
* Register Name : LANE_EQUALIZATION_CNTL_32GT
************************************************************/

#define LANE_EQUALIZATION_CNTL_32GT_LANE_DSP_32GT_TX_PRESET_OFFSET 0
#define LANE_EQUALIZATION_CNTL_32GT_LANE_DSP_32GT_TX_PRESET_MASK 0xf

#define LANE_EQUALIZATION_CNTL_32GT_LANE_USP_32GT_TX_PRESET_OFFSET 4
#define LANE_EQUALIZATION_CNTL_32GT_LANE_USP_32GT_TX_PRESET_MASK 0xf0

typedef union {
  struct {
    UINT8                             LANE_DSP_32GT_TX_PRESET:4;
    UINT8                             LANE_USP_32GT_TX_PRESET:4;
  } Field;
  UINT8 Value;
} LANE_EQUALIZATION_CNTL_32GT_STRUCT;

#define PCICFG_NBIFEPF0CFG_LANE_EQUALIZATION_CNTL_32GT_OFFSET       0x520
#define SMN_DEV0_FUNC0_NBIF0_N0NBIO0_LANE_EQUALIZATION_CNTL_32GT_ADDRESS 0x10140520UL
#define SMN_DEV0_FUNC0_NBIF0_N0NBIO1_LANE_EQUALIZATION_CNTL_32GT_ADDRESS 0x10340520UL
#define SMN_DEV0_FUNC0_NBIF0_N10NBIO0_LANE_EQUALIZATION_CNTL_32GT_ADDRESS 0x1014052aUL
#define SMN_DEV0_FUNC0_NBIF0_N10NBIO1_LANE_EQUALIZATION_CNTL_32GT_ADDRESS 0x1034052aUL
#define SMN_DEV0_FUNC0_NBIF0_N11NBIO0_LANE_EQUALIZATION_CNTL_32GT_ADDRESS 0x1014052bUL
#define SMN_DEV0_FUNC0_NBIF0_N11NBIO1_LANE_EQUALIZATION_CNTL_32GT_ADDRESS 0x1034052bUL
#define SMN_DEV0_FUNC0_NBIF0_N12NBIO0_LANE_EQUALIZATION_CNTL_32GT_ADDRESS 0x1014052cUL
#define SMN_DEV0_FUNC0_NBIF0_N12NBIO1_LANE_EQUALIZATION_CNTL_32GT_ADDRESS 0x1034052cUL
#define SMN_DEV0_FUNC0_NBIF0_N13NBIO0_LANE_EQUALIZATION_CNTL_32GT_ADDRESS 0x1014052dUL
#define SMN_DEV0_FUNC0_NBIF0_N13NBIO1_LANE_EQUALIZATION_CNTL_32GT_ADDRESS 0x1034052dUL
#define SMN_DEV0_FUNC0_NBIF0_N14NBIO0_LANE_EQUALIZATION_CNTL_32GT_ADDRESS 0x1014052eUL
#define SMN_DEV0_FUNC0_NBIF0_N14NBIO1_LANE_EQUALIZATION_CNTL_32GT_ADDRESS 0x1034052eUL
#define SMN_DEV0_FUNC0_NBIF0_N15NBIO0_LANE_EQUALIZATION_CNTL_32GT_ADDRESS 0x1014052fUL
#define SMN_DEV0_FUNC0_NBIF0_N15NBIO1_LANE_EQUALIZATION_CNTL_32GT_ADDRESS 0x1034052fUL
#define SMN_DEV0_FUNC0_NBIF0_N1NBIO0_LANE_EQUALIZATION_CNTL_32GT_ADDRESS 0x10140521UL
#define SMN_DEV0_FUNC0_NBIF0_N1NBIO1_LANE_EQUALIZATION_CNTL_32GT_ADDRESS 0x10340521UL
#define SMN_DEV0_FUNC0_NBIF0_N2NBIO0_LANE_EQUALIZATION_CNTL_32GT_ADDRESS 0x10140522UL
#define SMN_DEV0_FUNC0_NBIF0_N2NBIO1_LANE_EQUALIZATION_CNTL_32GT_ADDRESS 0x10340522UL
#define SMN_DEV0_FUNC0_NBIF0_N3NBIO0_LANE_EQUALIZATION_CNTL_32GT_ADDRESS 0x10140523UL
#define SMN_DEV0_FUNC0_NBIF0_N3NBIO1_LANE_EQUALIZATION_CNTL_32GT_ADDRESS 0x10340523UL
#define SMN_DEV0_FUNC0_NBIF0_N4NBIO0_LANE_EQUALIZATION_CNTL_32GT_ADDRESS 0x10140524UL
#define SMN_DEV0_FUNC0_NBIF0_N4NBIO1_LANE_EQUALIZATION_CNTL_32GT_ADDRESS 0x10340524UL
#define SMN_DEV0_FUNC0_NBIF0_N5NBIO0_LANE_EQUALIZATION_CNTL_32GT_ADDRESS 0x10140525UL
#define SMN_DEV0_FUNC0_NBIF0_N5NBIO1_LANE_EQUALIZATION_CNTL_32GT_ADDRESS 0x10340525UL
#define SMN_DEV0_FUNC0_NBIF0_N6NBIO0_LANE_EQUALIZATION_CNTL_32GT_ADDRESS 0x10140526UL
#define SMN_DEV0_FUNC0_NBIF0_N6NBIO1_LANE_EQUALIZATION_CNTL_32GT_ADDRESS 0x10340526UL
#define SMN_DEV0_FUNC0_NBIF0_N7NBIO0_LANE_EQUALIZATION_CNTL_32GT_ADDRESS 0x10140527UL
#define SMN_DEV0_FUNC0_NBIF0_N7NBIO1_LANE_EQUALIZATION_CNTL_32GT_ADDRESS 0x10340527UL
#define SMN_DEV0_FUNC0_NBIF0_N8NBIO0_LANE_EQUALIZATION_CNTL_32GT_ADDRESS 0x10140528UL
#define SMN_DEV0_FUNC0_NBIF0_N8NBIO1_LANE_EQUALIZATION_CNTL_32GT_ADDRESS 0x10340528UL
#define SMN_DEV0_FUNC0_NBIF0_N9NBIO0_LANE_EQUALIZATION_CNTL_32GT_ADDRESS 0x10140529UL
#define SMN_DEV0_FUNC0_NBIF0_N9NBIO1_LANE_EQUALIZATION_CNTL_32GT_ADDRESS 0x10340529UL
#define SMN_DEV0_FUNC0_NBIF1_N0NBIO0_LANE_EQUALIZATION_CNTL_32GT_ADDRESS 0x10240520UL
#define SMN_DEV0_FUNC0_NBIF1_N0NBIO1_LANE_EQUALIZATION_CNTL_32GT_ADDRESS 0x10440520UL
#define SMN_DEV0_FUNC0_NBIF1_N10NBIO0_LANE_EQUALIZATION_CNTL_32GT_ADDRESS 0x1024052aUL
#define SMN_DEV0_FUNC0_NBIF1_N10NBIO1_LANE_EQUALIZATION_CNTL_32GT_ADDRESS 0x1044052aUL
#define SMN_DEV0_FUNC0_NBIF1_N11NBIO0_LANE_EQUALIZATION_CNTL_32GT_ADDRESS 0x1024052bUL
#define SMN_DEV0_FUNC0_NBIF1_N11NBIO1_LANE_EQUALIZATION_CNTL_32GT_ADDRESS 0x1044052bUL
#define SMN_DEV0_FUNC0_NBIF1_N12NBIO0_LANE_EQUALIZATION_CNTL_32GT_ADDRESS 0x1024052cUL
#define SMN_DEV0_FUNC0_NBIF1_N12NBIO1_LANE_EQUALIZATION_CNTL_32GT_ADDRESS 0x1044052cUL
#define SMN_DEV0_FUNC0_NBIF1_N13NBIO0_LANE_EQUALIZATION_CNTL_32GT_ADDRESS 0x1024052dUL
#define SMN_DEV0_FUNC0_NBIF1_N13NBIO1_LANE_EQUALIZATION_CNTL_32GT_ADDRESS 0x1044052dUL
#define SMN_DEV0_FUNC0_NBIF1_N14NBIO0_LANE_EQUALIZATION_CNTL_32GT_ADDRESS 0x1024052eUL
#define SMN_DEV0_FUNC0_NBIF1_N14NBIO1_LANE_EQUALIZATION_CNTL_32GT_ADDRESS 0x1044052eUL
#define SMN_DEV0_FUNC0_NBIF1_N15NBIO0_LANE_EQUALIZATION_CNTL_32GT_ADDRESS 0x1024052fUL
#define SMN_DEV0_FUNC0_NBIF1_N15NBIO1_LANE_EQUALIZATION_CNTL_32GT_ADDRESS 0x1044052fUL
#define SMN_DEV0_FUNC0_NBIF1_N1NBIO0_LANE_EQUALIZATION_CNTL_32GT_ADDRESS 0x10240521UL
#define SMN_DEV0_FUNC0_NBIF1_N1NBIO1_LANE_EQUALIZATION_CNTL_32GT_ADDRESS 0x10440521UL
#define SMN_DEV0_FUNC0_NBIF1_N2NBIO0_LANE_EQUALIZATION_CNTL_32GT_ADDRESS 0x10240522UL
#define SMN_DEV0_FUNC0_NBIF1_N2NBIO1_LANE_EQUALIZATION_CNTL_32GT_ADDRESS 0x10440522UL
#define SMN_DEV0_FUNC0_NBIF1_N3NBIO0_LANE_EQUALIZATION_CNTL_32GT_ADDRESS 0x10240523UL
#define SMN_DEV0_FUNC0_NBIF1_N3NBIO1_LANE_EQUALIZATION_CNTL_32GT_ADDRESS 0x10440523UL
#define SMN_DEV0_FUNC0_NBIF1_N4NBIO0_LANE_EQUALIZATION_CNTL_32GT_ADDRESS 0x10240524UL
#define SMN_DEV0_FUNC0_NBIF1_N4NBIO1_LANE_EQUALIZATION_CNTL_32GT_ADDRESS 0x10440524UL
#define SMN_DEV0_FUNC0_NBIF1_N5NBIO0_LANE_EQUALIZATION_CNTL_32GT_ADDRESS 0x10240525UL
#define SMN_DEV0_FUNC0_NBIF1_N5NBIO1_LANE_EQUALIZATION_CNTL_32GT_ADDRESS 0x10440525UL
#define SMN_DEV0_FUNC0_NBIF1_N6NBIO0_LANE_EQUALIZATION_CNTL_32GT_ADDRESS 0x10240526UL
#define SMN_DEV0_FUNC0_NBIF1_N6NBIO1_LANE_EQUALIZATION_CNTL_32GT_ADDRESS 0x10440526UL
#define SMN_DEV0_FUNC0_NBIF1_N7NBIO0_LANE_EQUALIZATION_CNTL_32GT_ADDRESS 0x10240527UL
#define SMN_DEV0_FUNC0_NBIF1_N7NBIO1_LANE_EQUALIZATION_CNTL_32GT_ADDRESS 0x10440527UL
#define SMN_DEV0_FUNC0_NBIF1_N8NBIO0_LANE_EQUALIZATION_CNTL_32GT_ADDRESS 0x10240528UL
#define SMN_DEV0_FUNC0_NBIF1_N8NBIO1_LANE_EQUALIZATION_CNTL_32GT_ADDRESS 0x10440528UL
#define SMN_DEV0_FUNC0_NBIF1_N9NBIO0_LANE_EQUALIZATION_CNTL_32GT_ADDRESS 0x10240529UL
#define SMN_DEV0_FUNC0_NBIF1_N9NBIO1_LANE_EQUALIZATION_CNTL_32GT_ADDRESS 0x10440529UL
#define SMN_DEV0_FUNC0_NBIF2_N0NBIO0_LANE_EQUALIZATION_CNTL_32GT_ADDRESS 0x10540520UL
#define SMN_DEV0_FUNC0_NBIF2_N0NBIO1_LANE_EQUALIZATION_CNTL_32GT_ADDRESS 0x10740520UL
#define SMN_DEV0_FUNC0_NBIF2_N10NBIO0_LANE_EQUALIZATION_CNTL_32GT_ADDRESS 0x1054052aUL
#define SMN_DEV0_FUNC0_NBIF2_N10NBIO1_LANE_EQUALIZATION_CNTL_32GT_ADDRESS 0x1074052aUL
#define SMN_DEV0_FUNC0_NBIF2_N11NBIO0_LANE_EQUALIZATION_CNTL_32GT_ADDRESS 0x1054052bUL
#define SMN_DEV0_FUNC0_NBIF2_N11NBIO1_LANE_EQUALIZATION_CNTL_32GT_ADDRESS 0x1074052bUL
#define SMN_DEV0_FUNC0_NBIF2_N12NBIO0_LANE_EQUALIZATION_CNTL_32GT_ADDRESS 0x1054052cUL
#define SMN_DEV0_FUNC0_NBIF2_N12NBIO1_LANE_EQUALIZATION_CNTL_32GT_ADDRESS 0x1074052cUL
#define SMN_DEV0_FUNC0_NBIF2_N13NBIO0_LANE_EQUALIZATION_CNTL_32GT_ADDRESS 0x1054052dUL
#define SMN_DEV0_FUNC0_NBIF2_N13NBIO1_LANE_EQUALIZATION_CNTL_32GT_ADDRESS 0x1074052dUL
#define SMN_DEV0_FUNC0_NBIF2_N14NBIO0_LANE_EQUALIZATION_CNTL_32GT_ADDRESS 0x1054052eUL
#define SMN_DEV0_FUNC0_NBIF2_N14NBIO1_LANE_EQUALIZATION_CNTL_32GT_ADDRESS 0x1074052eUL
#define SMN_DEV0_FUNC0_NBIF2_N15NBIO0_LANE_EQUALIZATION_CNTL_32GT_ADDRESS 0x1054052fUL
#define SMN_DEV0_FUNC0_NBIF2_N15NBIO1_LANE_EQUALIZATION_CNTL_32GT_ADDRESS 0x1074052fUL
#define SMN_DEV0_FUNC0_NBIF2_N1NBIO0_LANE_EQUALIZATION_CNTL_32GT_ADDRESS 0x10540521UL
#define SMN_DEV0_FUNC0_NBIF2_N1NBIO1_LANE_EQUALIZATION_CNTL_32GT_ADDRESS 0x10740521UL
#define SMN_DEV0_FUNC0_NBIF2_N2NBIO0_LANE_EQUALIZATION_CNTL_32GT_ADDRESS 0x10540522UL
#define SMN_DEV0_FUNC0_NBIF2_N2NBIO1_LANE_EQUALIZATION_CNTL_32GT_ADDRESS 0x10740522UL
#define SMN_DEV0_FUNC0_NBIF2_N3NBIO0_LANE_EQUALIZATION_CNTL_32GT_ADDRESS 0x10540523UL
#define SMN_DEV0_FUNC0_NBIF2_N3NBIO1_LANE_EQUALIZATION_CNTL_32GT_ADDRESS 0x10740523UL
#define SMN_DEV0_FUNC0_NBIF2_N4NBIO0_LANE_EQUALIZATION_CNTL_32GT_ADDRESS 0x10540524UL
#define SMN_DEV0_FUNC0_NBIF2_N4NBIO1_LANE_EQUALIZATION_CNTL_32GT_ADDRESS 0x10740524UL
#define SMN_DEV0_FUNC0_NBIF2_N5NBIO0_LANE_EQUALIZATION_CNTL_32GT_ADDRESS 0x10540525UL
#define SMN_DEV0_FUNC0_NBIF2_N5NBIO1_LANE_EQUALIZATION_CNTL_32GT_ADDRESS 0x10740525UL
#define SMN_DEV0_FUNC0_NBIF2_N6NBIO0_LANE_EQUALIZATION_CNTL_32GT_ADDRESS 0x10540526UL
#define SMN_DEV0_FUNC0_NBIF2_N6NBIO1_LANE_EQUALIZATION_CNTL_32GT_ADDRESS 0x10740526UL
#define SMN_DEV0_FUNC0_NBIF2_N7NBIO0_LANE_EQUALIZATION_CNTL_32GT_ADDRESS 0x10540527UL
#define SMN_DEV0_FUNC0_NBIF2_N7NBIO1_LANE_EQUALIZATION_CNTL_32GT_ADDRESS 0x10740527UL
#define SMN_DEV0_FUNC0_NBIF2_N8NBIO0_LANE_EQUALIZATION_CNTL_32GT_ADDRESS 0x10540528UL
#define SMN_DEV0_FUNC0_NBIF2_N8NBIO1_LANE_EQUALIZATION_CNTL_32GT_ADDRESS 0x10740528UL
#define SMN_DEV0_FUNC0_NBIF2_N9NBIO0_LANE_EQUALIZATION_CNTL_32GT_ADDRESS 0x10540529UL
#define SMN_DEV0_FUNC0_NBIF2_N9NBIO1_LANE_EQUALIZATION_CNTL_32GT_ADDRESS 0x10740529UL
#define SMN_DEV1_FUNC0_NBIF0_N0NBIO0_LANE_EQUALIZATION_CNTL_32GT_ADDRESS 0x10148520UL
#define SMN_DEV1_FUNC0_NBIF0_N0NBIO1_LANE_EQUALIZATION_CNTL_32GT_ADDRESS 0x10348520UL
#define SMN_DEV1_FUNC0_NBIF0_N10NBIO0_LANE_EQUALIZATION_CNTL_32GT_ADDRESS 0x1014852aUL
#define SMN_DEV1_FUNC0_NBIF0_N10NBIO1_LANE_EQUALIZATION_CNTL_32GT_ADDRESS 0x1034852aUL
#define SMN_DEV1_FUNC0_NBIF0_N11NBIO0_LANE_EQUALIZATION_CNTL_32GT_ADDRESS 0x1014852bUL
#define SMN_DEV1_FUNC0_NBIF0_N11NBIO1_LANE_EQUALIZATION_CNTL_32GT_ADDRESS 0x1034852bUL
#define SMN_DEV1_FUNC0_NBIF0_N12NBIO0_LANE_EQUALIZATION_CNTL_32GT_ADDRESS 0x1014852cUL
#define SMN_DEV1_FUNC0_NBIF0_N12NBIO1_LANE_EQUALIZATION_CNTL_32GT_ADDRESS 0x1034852cUL
#define SMN_DEV1_FUNC0_NBIF0_N13NBIO0_LANE_EQUALIZATION_CNTL_32GT_ADDRESS 0x1014852dUL
#define SMN_DEV1_FUNC0_NBIF0_N13NBIO1_LANE_EQUALIZATION_CNTL_32GT_ADDRESS 0x1034852dUL
#define SMN_DEV1_FUNC0_NBIF0_N14NBIO0_LANE_EQUALIZATION_CNTL_32GT_ADDRESS 0x1014852eUL
#define SMN_DEV1_FUNC0_NBIF0_N14NBIO1_LANE_EQUALIZATION_CNTL_32GT_ADDRESS 0x1034852eUL
#define SMN_DEV1_FUNC0_NBIF0_N15NBIO0_LANE_EQUALIZATION_CNTL_32GT_ADDRESS 0x1014852fUL
#define SMN_DEV1_FUNC0_NBIF0_N15NBIO1_LANE_EQUALIZATION_CNTL_32GT_ADDRESS 0x1034852fUL
#define SMN_DEV1_FUNC0_NBIF0_N1NBIO0_LANE_EQUALIZATION_CNTL_32GT_ADDRESS 0x10148521UL
#define SMN_DEV1_FUNC0_NBIF0_N1NBIO1_LANE_EQUALIZATION_CNTL_32GT_ADDRESS 0x10348521UL
#define SMN_DEV1_FUNC0_NBIF0_N2NBIO0_LANE_EQUALIZATION_CNTL_32GT_ADDRESS 0x10148522UL
#define SMN_DEV1_FUNC0_NBIF0_N2NBIO1_LANE_EQUALIZATION_CNTL_32GT_ADDRESS 0x10348522UL
#define SMN_DEV1_FUNC0_NBIF0_N3NBIO0_LANE_EQUALIZATION_CNTL_32GT_ADDRESS 0x10148523UL
#define SMN_DEV1_FUNC0_NBIF0_N3NBIO1_LANE_EQUALIZATION_CNTL_32GT_ADDRESS 0x10348523UL
#define SMN_DEV1_FUNC0_NBIF0_N4NBIO0_LANE_EQUALIZATION_CNTL_32GT_ADDRESS 0x10148524UL
#define SMN_DEV1_FUNC0_NBIF0_N4NBIO1_LANE_EQUALIZATION_CNTL_32GT_ADDRESS 0x10348524UL
#define SMN_DEV1_FUNC0_NBIF0_N5NBIO0_LANE_EQUALIZATION_CNTL_32GT_ADDRESS 0x10148525UL
#define SMN_DEV1_FUNC0_NBIF0_N5NBIO1_LANE_EQUALIZATION_CNTL_32GT_ADDRESS 0x10348525UL
#define SMN_DEV1_FUNC0_NBIF0_N6NBIO0_LANE_EQUALIZATION_CNTL_32GT_ADDRESS 0x10148526UL
#define SMN_DEV1_FUNC0_NBIF0_N6NBIO1_LANE_EQUALIZATION_CNTL_32GT_ADDRESS 0x10348526UL
#define SMN_DEV1_FUNC0_NBIF0_N7NBIO0_LANE_EQUALIZATION_CNTL_32GT_ADDRESS 0x10148527UL
#define SMN_DEV1_FUNC0_NBIF0_N7NBIO1_LANE_EQUALIZATION_CNTL_32GT_ADDRESS 0x10348527UL
#define SMN_DEV1_FUNC0_NBIF0_N8NBIO0_LANE_EQUALIZATION_CNTL_32GT_ADDRESS 0x10148528UL
#define SMN_DEV1_FUNC0_NBIF0_N8NBIO1_LANE_EQUALIZATION_CNTL_32GT_ADDRESS 0x10348528UL
#define SMN_DEV1_FUNC0_NBIF0_N9NBIO0_LANE_EQUALIZATION_CNTL_32GT_ADDRESS 0x10148529UL
#define SMN_DEV1_FUNC0_NBIF0_N9NBIO1_LANE_EQUALIZATION_CNTL_32GT_ADDRESS 0x10348529UL
#define SMN_DEV1_FUNC0_NBIF1_N0NBIO0_LANE_EQUALIZATION_CNTL_32GT_ADDRESS 0x10248520UL
#define SMN_DEV1_FUNC0_NBIF1_N0NBIO1_LANE_EQUALIZATION_CNTL_32GT_ADDRESS 0x10448520UL
#define SMN_DEV1_FUNC0_NBIF1_N10NBIO0_LANE_EQUALIZATION_CNTL_32GT_ADDRESS 0x1024852aUL
#define SMN_DEV1_FUNC0_NBIF1_N10NBIO1_LANE_EQUALIZATION_CNTL_32GT_ADDRESS 0x1044852aUL
#define SMN_DEV1_FUNC0_NBIF1_N11NBIO0_LANE_EQUALIZATION_CNTL_32GT_ADDRESS 0x1024852bUL
#define SMN_DEV1_FUNC0_NBIF1_N11NBIO1_LANE_EQUALIZATION_CNTL_32GT_ADDRESS 0x1044852bUL
#define SMN_DEV1_FUNC0_NBIF1_N12NBIO0_LANE_EQUALIZATION_CNTL_32GT_ADDRESS 0x1024852cUL
#define SMN_DEV1_FUNC0_NBIF1_N12NBIO1_LANE_EQUALIZATION_CNTL_32GT_ADDRESS 0x1044852cUL
#define SMN_DEV1_FUNC0_NBIF1_N13NBIO0_LANE_EQUALIZATION_CNTL_32GT_ADDRESS 0x1024852dUL
#define SMN_DEV1_FUNC0_NBIF1_N13NBIO1_LANE_EQUALIZATION_CNTL_32GT_ADDRESS 0x1044852dUL
#define SMN_DEV1_FUNC0_NBIF1_N14NBIO0_LANE_EQUALIZATION_CNTL_32GT_ADDRESS 0x1024852eUL
#define SMN_DEV1_FUNC0_NBIF1_N14NBIO1_LANE_EQUALIZATION_CNTL_32GT_ADDRESS 0x1044852eUL
#define SMN_DEV1_FUNC0_NBIF1_N15NBIO0_LANE_EQUALIZATION_CNTL_32GT_ADDRESS 0x1024852fUL
#define SMN_DEV1_FUNC0_NBIF1_N15NBIO1_LANE_EQUALIZATION_CNTL_32GT_ADDRESS 0x1044852fUL
#define SMN_DEV1_FUNC0_NBIF1_N1NBIO0_LANE_EQUALIZATION_CNTL_32GT_ADDRESS 0x10248521UL
#define SMN_DEV1_FUNC0_NBIF1_N1NBIO1_LANE_EQUALIZATION_CNTL_32GT_ADDRESS 0x10448521UL
#define SMN_DEV1_FUNC0_NBIF1_N2NBIO0_LANE_EQUALIZATION_CNTL_32GT_ADDRESS 0x10248522UL
#define SMN_DEV1_FUNC0_NBIF1_N2NBIO1_LANE_EQUALIZATION_CNTL_32GT_ADDRESS 0x10448522UL
#define SMN_DEV1_FUNC0_NBIF1_N3NBIO0_LANE_EQUALIZATION_CNTL_32GT_ADDRESS 0x10248523UL
#define SMN_DEV1_FUNC0_NBIF1_N3NBIO1_LANE_EQUALIZATION_CNTL_32GT_ADDRESS 0x10448523UL
#define SMN_DEV1_FUNC0_NBIF1_N4NBIO0_LANE_EQUALIZATION_CNTL_32GT_ADDRESS 0x10248524UL
#define SMN_DEV1_FUNC0_NBIF1_N4NBIO1_LANE_EQUALIZATION_CNTL_32GT_ADDRESS 0x10448524UL
#define SMN_DEV1_FUNC0_NBIF1_N5NBIO0_LANE_EQUALIZATION_CNTL_32GT_ADDRESS 0x10248525UL
#define SMN_DEV1_FUNC0_NBIF1_N5NBIO1_LANE_EQUALIZATION_CNTL_32GT_ADDRESS 0x10448525UL
#define SMN_DEV1_FUNC0_NBIF1_N6NBIO0_LANE_EQUALIZATION_CNTL_32GT_ADDRESS 0x10248526UL
#define SMN_DEV1_FUNC0_NBIF1_N6NBIO1_LANE_EQUALIZATION_CNTL_32GT_ADDRESS 0x10448526UL
#define SMN_DEV1_FUNC0_NBIF1_N7NBIO0_LANE_EQUALIZATION_CNTL_32GT_ADDRESS 0x10248527UL
#define SMN_DEV1_FUNC0_NBIF1_N7NBIO1_LANE_EQUALIZATION_CNTL_32GT_ADDRESS 0x10448527UL
#define SMN_DEV1_FUNC0_NBIF1_N8NBIO0_LANE_EQUALIZATION_CNTL_32GT_ADDRESS 0x10248528UL
#define SMN_DEV1_FUNC0_NBIF1_N8NBIO1_LANE_EQUALIZATION_CNTL_32GT_ADDRESS 0x10448528UL
#define SMN_DEV1_FUNC0_NBIF1_N9NBIO0_LANE_EQUALIZATION_CNTL_32GT_ADDRESS 0x10248529UL
#define SMN_DEV1_FUNC0_NBIF1_N9NBIO1_LANE_EQUALIZATION_CNTL_32GT_ADDRESS 0x10448529UL


/***********************************************************
* Register Name : LATENCY
************************************************************/

#define LATENCY_LATENCY_TIMER_OFFSET                           0
#define LATENCY_LATENCY_TIMER_MASK                             0xff

typedef union {
  struct {
    UINT8                                       LATENCY_TIMER:8;
  } Field;
  UINT8 Value;
} LATENCY_STRUCT;

#define PCICFG_NBIFEPF0CFG_LATENCY_OFFSET                           0xd
#define SMN_DEV0_FUNC0_NBIF0NBIO0_LATENCY_ADDRESS                     0x1014000dUL
#define SMN_DEV0_FUNC0_NBIF0NBIO1_LATENCY_ADDRESS                     0x1034000dUL
#define SMN_DEV0_FUNC0_NBIF1NBIO0_LATENCY_ADDRESS                     0x1024000dUL
#define SMN_DEV0_FUNC0_NBIF1NBIO1_LATENCY_ADDRESS                     0x1044000dUL
#define SMN_DEV0_FUNC0_NBIF2NBIO0_LATENCY_ADDRESS                     0x1054000dUL
#define SMN_DEV0_FUNC0_NBIF2NBIO1_LATENCY_ADDRESS                     0x1074000dUL
#define SMN_DEV1_FUNC0_NBIF0NBIO0_LATENCY_ADDRESS                     0x1014800dUL
#define SMN_DEV1_FUNC0_NBIF0NBIO1_LATENCY_ADDRESS                     0x1034800dUL
#define SMN_DEV1_FUNC0_NBIF1NBIO0_LATENCY_ADDRESS                     0x1024800dUL
#define SMN_DEV1_FUNC0_NBIF1NBIO1_LATENCY_ADDRESS                     0x1044800dUL


/***********************************************************
* Register Name : LINK_CAP
************************************************************/

#define LINK_CAP_LINK_SPEED_OFFSET                             0
#define LINK_CAP_LINK_SPEED_MASK                               0xf

#define LINK_CAP_LINK_WIDTH_OFFSET                             4
#define LINK_CAP_LINK_WIDTH_MASK                               0x3f0

#define LINK_CAP_PM_SUPPORT_OFFSET                             10
#define LINK_CAP_PM_SUPPORT_MASK                               0xc00

#define LINK_CAP_L0S_EXIT_LATENCY_OFFSET                       12
#define LINK_CAP_L0S_EXIT_LATENCY_MASK                         0x7000

#define LINK_CAP_L1_EXIT_LATENCY_OFFSET                        15
#define LINK_CAP_L1_EXIT_LATENCY_MASK                          0x38000

#define LINK_CAP_CLOCK_POWER_MANAGEMENT_OFFSET                 18
#define LINK_CAP_CLOCK_POWER_MANAGEMENT_MASK                   0x40000

#define LINK_CAP_SURPRISE_DOWN_ERR_REPORTING_OFFSET            19
#define LINK_CAP_SURPRISE_DOWN_ERR_REPORTING_MASK              0x80000

#define LINK_CAP_DL_ACTIVE_REPORTING_CAPABLE_OFFSET            20
#define LINK_CAP_DL_ACTIVE_REPORTING_CAPABLE_MASK              0x100000

#define LINK_CAP_LINK_BW_NOTIFICATION_CAP_OFFSET               21
#define LINK_CAP_LINK_BW_NOTIFICATION_CAP_MASK                 0x200000

#define LINK_CAP_ASPM_OPTIONALITY_COMPLIANCE_OFFSET            22
#define LINK_CAP_ASPM_OPTIONALITY_COMPLIANCE_MASK              0x400000

#define LINK_CAP_Reserved_23_23_OFFSET                         23
#define LINK_CAP_Reserved_23_23_MASK                           0x800000

#define LINK_CAP_PORT_NUMBER_OFFSET                            24
#define LINK_CAP_PORT_NUMBER_MASK                              0xff000000

typedef union {
  struct {
    UINT32                                          LINK_SPEED:4;
    UINT32                                          LINK_WIDTH:6;
    UINT32                                          PM_SUPPORT:2;
    UINT32                                    L0S_EXIT_LATENCY:3;
    UINT32                                     L1_EXIT_LATENCY:3;
    UINT32                              CLOCK_POWER_MANAGEMENT:1;
    UINT32                         SURPRISE_DOWN_ERR_REPORTING:1;
    UINT32                         DL_ACTIVE_REPORTING_CAPABLE:1;
    UINT32                            LINK_BW_NOTIFICATION_CAP:1;
    UINT32                         ASPM_OPTIONALITY_COMPLIANCE:1;
    UINT32                                      Reserved_23_23:1;
    UINT32                                         PORT_NUMBER:8;
  } Field;
  UINT32 Value;
} LINK_CAP_STRUCT;

#define PCICFG_NBIFEPF0CFG_LINK_CAP_OFFSET                          0x70
#define SMN_DEV0_FUNC0_NBIF0NBIO0_LINK_CAP_ADDRESS                    0x10140070UL
#define SMN_DEV0_FUNC0_NBIF0NBIO1_LINK_CAP_ADDRESS                    0x10340070UL
#define SMN_DEV0_FUNC0_NBIF1NBIO0_LINK_CAP_ADDRESS                    0x10240070UL
#define SMN_DEV0_FUNC0_NBIF1NBIO1_LINK_CAP_ADDRESS                    0x10440070UL
#define SMN_DEV0_FUNC0_NBIF2NBIO0_LINK_CAP_ADDRESS                    0x10540070UL
#define SMN_DEV0_FUNC0_NBIF2NBIO1_LINK_CAP_ADDRESS                    0x10740070UL
#define SMN_DEV1_FUNC0_NBIF0NBIO0_LINK_CAP_ADDRESS                    0x10148070UL
#define SMN_DEV1_FUNC0_NBIF0NBIO1_LINK_CAP_ADDRESS                    0x10348070UL
#define SMN_DEV1_FUNC0_NBIF1NBIO0_LINK_CAP_ADDRESS                    0x10248070UL
#define SMN_DEV1_FUNC0_NBIF1NBIO1_LINK_CAP_ADDRESS                    0x10448070UL


/***********************************************************
* Register Name : LINK_CAP2
************************************************************/

#define LINK_CAP2_Reserved_0_0_OFFSET                          0
#define LINK_CAP2_Reserved_0_0_MASK                            0x1

#define LINK_CAP2_SUPPORTED_LINK_SPEED_OFFSET                  1
#define LINK_CAP2_SUPPORTED_LINK_SPEED_MASK                    0xfe

#define LINK_CAP2_CROSSLINK_SUPPORTED_OFFSET                   8
#define LINK_CAP2_CROSSLINK_SUPPORTED_MASK                     0x100

#define LINK_CAP2_LOWER_SKP_OS_GEN_SUPPORT_OFFSET              9
#define LINK_CAP2_LOWER_SKP_OS_GEN_SUPPORT_MASK                0xfe00

#define LINK_CAP2_LOWER_SKP_OS_RCV_SUPPORT_OFFSET              16
#define LINK_CAP2_LOWER_SKP_OS_RCV_SUPPORT_MASK                0x7f0000

#define LINK_CAP2_RTM1_PRESENCE_DET_SUPPORT_OFFSET             23
#define LINK_CAP2_RTM1_PRESENCE_DET_SUPPORT_MASK               0x800000

#define LINK_CAP2_RTM2_PRESENCE_DET_SUPPORT_OFFSET             24
#define LINK_CAP2_RTM2_PRESENCE_DET_SUPPORT_MASK               0x1000000

#define LINK_CAP2_Reserved_30_25_OFFSET                        25
#define LINK_CAP2_Reserved_30_25_MASK                          0x7e000000

#define LINK_CAP2_DRS_SUPPORTED_OFFSET                         31
#define LINK_CAP2_DRS_SUPPORTED_MASK                           0x80000000

typedef union {
  struct {
    UINT32                                        Reserved_0_0:1;
    UINT32                                SUPPORTED_LINK_SPEED:7;
    UINT32                                 CROSSLINK_SUPPORTED:1;
    UINT32                            LOWER_SKP_OS_GEN_SUPPORT:7;
    UINT32                            LOWER_SKP_OS_RCV_SUPPORT:7;
    UINT32                           RTM1_PRESENCE_DET_SUPPORT:1;
    UINT32                           RTM2_PRESENCE_DET_SUPPORT:1;
    UINT32                                      Reserved_30_25:6;
    UINT32                                       DRS_SUPPORTED:1;
  } Field;
  UINT32 Value;
} LINK_CAP2_STRUCT;

#define PCICFG_NBIFEPF0CFG_LINK_CAP2_OFFSET                         0x90
#define SMN_DEV0_FUNC0_NBIF0NBIO0_LINK_CAP2_ADDRESS                   0x10140090UL
#define SMN_DEV0_FUNC0_NBIF0NBIO1_LINK_CAP2_ADDRESS                   0x10340090UL
#define SMN_DEV0_FUNC0_NBIF1NBIO0_LINK_CAP2_ADDRESS                   0x10240090UL
#define SMN_DEV0_FUNC0_NBIF1NBIO1_LINK_CAP2_ADDRESS                   0x10440090UL
#define SMN_DEV0_FUNC0_NBIF2NBIO0_LINK_CAP2_ADDRESS                   0x10540090UL
#define SMN_DEV0_FUNC0_NBIF2NBIO1_LINK_CAP2_ADDRESS                   0x10740090UL
#define SMN_DEV1_FUNC0_NBIF0NBIO0_LINK_CAP2_ADDRESS                   0x10148090UL
#define SMN_DEV1_FUNC0_NBIF0NBIO1_LINK_CAP2_ADDRESS                   0x10348090UL
#define SMN_DEV1_FUNC0_NBIF1NBIO0_LINK_CAP2_ADDRESS                   0x10248090UL
#define SMN_DEV1_FUNC0_NBIF1NBIO1_LINK_CAP2_ADDRESS                   0x10448090UL


/***********************************************************
* Register Name : LINK_CAP_16GT
************************************************************/

#define LINK_CAP_16GT_Reserved_31_0_OFFSET                     0
#define LINK_CAP_16GT_Reserved_31_0_MASK                       0xffffffff

typedef union {
  struct {
    UINT32                                       Reserved_31_0:32;
  } Field;
  UINT32 Value;
} LINK_CAP_16GT_STRUCT;

#define PCICFG_NBIFEPF0CFG_LINK_CAP_16GT_OFFSET                     0x414
#define SMN_DEV0_FUNC0_NBIF0NBIO0_LINK_CAP_16GT_ADDRESS               0x10140414UL
#define SMN_DEV0_FUNC0_NBIF0NBIO1_LINK_CAP_16GT_ADDRESS               0x10340414UL
#define SMN_DEV0_FUNC0_NBIF1NBIO0_LINK_CAP_16GT_ADDRESS               0x10240414UL
#define SMN_DEV0_FUNC0_NBIF1NBIO1_LINK_CAP_16GT_ADDRESS               0x10440414UL
#define SMN_DEV0_FUNC0_NBIF2NBIO0_LINK_CAP_16GT_ADDRESS               0x10540414UL
#define SMN_DEV0_FUNC0_NBIF2NBIO1_LINK_CAP_16GT_ADDRESS               0x10740414UL
#define SMN_DEV1_FUNC0_NBIF0NBIO0_LINK_CAP_16GT_ADDRESS               0x10148414UL
#define SMN_DEV1_FUNC0_NBIF0NBIO1_LINK_CAP_16GT_ADDRESS               0x10348414UL
#define SMN_DEV1_FUNC0_NBIF1NBIO0_LINK_CAP_16GT_ADDRESS               0x10248414UL
#define SMN_DEV1_FUNC0_NBIF1NBIO1_LINK_CAP_16GT_ADDRESS               0x10448414UL


/***********************************************************
* Register Name : LINK_CAP_32GT
************************************************************/

#define LINK_CAP_32GT_EQ_BYPASS_TO_HIGHEST_RATE_SUPPORTED_OFFSET 0
#define LINK_CAP_32GT_EQ_BYPASS_TO_HIGHEST_RATE_SUPPORTED_MASK 0x1

#define LINK_CAP_32GT_NO_EQ_NEEDED_SUPPORTED_OFFSET            1
#define LINK_CAP_32GT_NO_EQ_NEEDED_SUPPORTED_MASK              0x2

#define LINK_CAP_32GT_Reserved_7_2_OFFSET                      2
#define LINK_CAP_32GT_Reserved_7_2_MASK                        0xfc

#define LINK_CAP_32GT_MODIFIED_TS_USAGE_MODE0_SUPPORTED_OFFSET 8
#define LINK_CAP_32GT_MODIFIED_TS_USAGE_MODE0_SUPPORTED_MASK   0x100

#define LINK_CAP_32GT_MODIFIED_TS_USAGE_MODE1_SUPPORTED_OFFSET 9
#define LINK_CAP_32GT_MODIFIED_TS_USAGE_MODE1_SUPPORTED_MASK   0x200

#define LINK_CAP_32GT_MODIFIED_TS_USAGE_MODE2_SUPPORTED_OFFSET 10
#define LINK_CAP_32GT_MODIFIED_TS_USAGE_MODE2_SUPPORTED_MASK   0x400

#define LINK_CAP_32GT_MODIFIED_TS_RESERVED_USAGE_MODES_OFFSET  11
#define LINK_CAP_32GT_MODIFIED_TS_RESERVED_USAGE_MODES_MASK    0xf800

#define LINK_CAP_32GT_Reserved_31_16_OFFSET                    16
#define LINK_CAP_32GT_Reserved_31_16_MASK                      0xffff0000

typedef union {
  struct {
    UINT32                 EQ_BYPASS_TO_HIGHEST_RATE_SUPPORTED:1;
    UINT32                              NO_EQ_NEEDED_SUPPORTED:1;
    UINT32                                        Reserved_7_2:6;
    UINT32                   MODIFIED_TS_USAGE_MODE0_SUPPORTED:1;
    UINT32                   MODIFIED_TS_USAGE_MODE1_SUPPORTED:1;
    UINT32                   MODIFIED_TS_USAGE_MODE2_SUPPORTED:1;
    UINT32                    MODIFIED_TS_RESERVED_USAGE_MODES:5;
    UINT32                                      Reserved_31_16:16;
  } Field;
  UINT32 Value;
} LINK_CAP_32GT_STRUCT;

#define PCICFG_NBIFEPF0CFG_LINK_CAP_32GT_OFFSET                     0x504
#define SMN_DEV0_FUNC0_NBIF0NBIO0_LINK_CAP_32GT_ADDRESS               0x10140504UL
#define SMN_DEV0_FUNC0_NBIF0NBIO1_LINK_CAP_32GT_ADDRESS               0x10340504UL
#define SMN_DEV0_FUNC0_NBIF1NBIO0_LINK_CAP_32GT_ADDRESS               0x10240504UL
#define SMN_DEV0_FUNC0_NBIF1NBIO1_LINK_CAP_32GT_ADDRESS               0x10440504UL
#define SMN_DEV0_FUNC0_NBIF2NBIO0_LINK_CAP_32GT_ADDRESS               0x10540504UL
#define SMN_DEV0_FUNC0_NBIF2NBIO1_LINK_CAP_32GT_ADDRESS               0x10740504UL
#define SMN_DEV1_FUNC0_NBIF0NBIO0_LINK_CAP_32GT_ADDRESS               0x10148504UL
#define SMN_DEV1_FUNC0_NBIF0NBIO1_LINK_CAP_32GT_ADDRESS               0x10348504UL
#define SMN_DEV1_FUNC0_NBIF1NBIO0_LINK_CAP_32GT_ADDRESS               0x10248504UL
#define SMN_DEV1_FUNC0_NBIF1NBIO1_LINK_CAP_32GT_ADDRESS               0x10448504UL


/***********************************************************
* Register Name : LINK_CNTL
************************************************************/

#define LINK_CNTL_PM_CONTROL_OFFSET                            0
#define LINK_CNTL_PM_CONTROL_MASK                              0x3

#define LINK_CNTL_PTM_PROP_DELAY_ADAPT_INTER_B_OFFSET          2
#define LINK_CNTL_PTM_PROP_DELAY_ADAPT_INTER_B_MASK            0x4

#define LINK_CNTL_READ_CPL_BOUNDARY_OFFSET                     3
#define LINK_CNTL_READ_CPL_BOUNDARY_MASK                       0x8

#define LINK_CNTL_LINK_DIS_OFFSET                              4
#define LINK_CNTL_LINK_DIS_MASK                                0x10

#define LINK_CNTL_RETRAIN_LINK_OFFSET                          5
#define LINK_CNTL_RETRAIN_LINK_MASK                            0x20

#define LINK_CNTL_COMMON_CLOCK_CFG_OFFSET                      6
#define LINK_CNTL_COMMON_CLOCK_CFG_MASK                        0x40

#define LINK_CNTL_EXTENDED_SYNC_OFFSET                         7
#define LINK_CNTL_EXTENDED_SYNC_MASK                           0x80

#define LINK_CNTL_CLOCK_POWER_MANAGEMENT_EN_OFFSET             8
#define LINK_CNTL_CLOCK_POWER_MANAGEMENT_EN_MASK               0x100

#define LINK_CNTL_HW_AUTONOMOUS_WIDTH_DISABLE_OFFSET           9
#define LINK_CNTL_HW_AUTONOMOUS_WIDTH_DISABLE_MASK             0x200

#define LINK_CNTL_LINK_BW_MANAGEMENT_INT_EN_OFFSET             10
#define LINK_CNTL_LINK_BW_MANAGEMENT_INT_EN_MASK               0x400

#define LINK_CNTL_LINK_AUTONOMOUS_BW_INT_EN_OFFSET             11
#define LINK_CNTL_LINK_AUTONOMOUS_BW_INT_EN_MASK               0x800

#define LINK_CNTL_Reserved_13_12_OFFSET                        12
#define LINK_CNTL_Reserved_13_12_MASK                          0x3000

#define LINK_CNTL_DRS_SIGNALING_CONTROL_OFFSET                 14
#define LINK_CNTL_DRS_SIGNALING_CONTROL_MASK                   0xc000

typedef union {
  struct {
    UINT16                                          PM_CONTROL:2;
    UINT16                        PTM_PROP_DELAY_ADAPT_INTER_B:1;
    UINT16                                   READ_CPL_BOUNDARY:1;
    UINT16                                            LINK_DIS:1;
    UINT16                                        RETRAIN_LINK:1;
    UINT16                                    COMMON_CLOCK_CFG:1;
    UINT16                                       EXTENDED_SYNC:1;
    UINT16                           CLOCK_POWER_MANAGEMENT_EN:1;
    UINT16                         HW_AUTONOMOUS_WIDTH_DISABLE:1;
    UINT16                           LINK_BW_MANAGEMENT_INT_EN:1;
    UINT16                           LINK_AUTONOMOUS_BW_INT_EN:1;
    UINT16                                      Reserved_13_12:2;
    UINT16                               DRS_SIGNALING_CONTROL:2;
  } Field;
  UINT16 Value;
} LINK_CNTL_STRUCT;

#define PCICFG_NBIFEPF0CFG_LINK_CNTL_OFFSET                         0x74
#define SMN_DEV0_FUNC0_NBIF0NBIO0_LINK_CNTL_ADDRESS                   0x10140074UL
#define SMN_DEV0_FUNC0_NBIF0NBIO1_LINK_CNTL_ADDRESS                   0x10340074UL
#define SMN_DEV0_FUNC0_NBIF1NBIO0_LINK_CNTL_ADDRESS                   0x10240074UL
#define SMN_DEV0_FUNC0_NBIF1NBIO1_LINK_CNTL_ADDRESS                   0x10440074UL
#define SMN_DEV0_FUNC0_NBIF2NBIO0_LINK_CNTL_ADDRESS                   0x10540074UL
#define SMN_DEV0_FUNC0_NBIF2NBIO1_LINK_CNTL_ADDRESS                   0x10740074UL
#define SMN_DEV1_FUNC0_NBIF0NBIO0_LINK_CNTL_ADDRESS                   0x10148074UL
#define SMN_DEV1_FUNC0_NBIF0NBIO1_LINK_CNTL_ADDRESS                   0x10348074UL
#define SMN_DEV1_FUNC0_NBIF1NBIO0_LINK_CNTL_ADDRESS                   0x10248074UL
#define SMN_DEV1_FUNC0_NBIF1NBIO1_LINK_CNTL_ADDRESS                   0x10448074UL


/***********************************************************
* Register Name : LINK_CNTL2
************************************************************/

#define LINK_CNTL2_TARGET_LINK_SPEED_OFFSET                    0
#define LINK_CNTL2_TARGET_LINK_SPEED_MASK                      0xf

#define LINK_CNTL2_ENTER_COMPLIANCE_OFFSET                     4
#define LINK_CNTL2_ENTER_COMPLIANCE_MASK                       0x10

#define LINK_CNTL2_HW_AUTONOMOUS_SPEED_DISABLE_OFFSET          5
#define LINK_CNTL2_HW_AUTONOMOUS_SPEED_DISABLE_MASK            0x20

#define LINK_CNTL2_SELECTABLE_DEEMPHASIS_OFFSET                6
#define LINK_CNTL2_SELECTABLE_DEEMPHASIS_MASK                  0x40

#define LINK_CNTL2_XMIT_MARGIN_OFFSET                          7
#define LINK_CNTL2_XMIT_MARGIN_MASK                            0x380

#define LINK_CNTL2_ENTER_MOD_COMPLIANCE_OFFSET                 10
#define LINK_CNTL2_ENTER_MOD_COMPLIANCE_MASK                   0x400

#define LINK_CNTL2_COMPLIANCE_SOS_OFFSET                       11
#define LINK_CNTL2_COMPLIANCE_SOS_MASK                         0x800

#define LINK_CNTL2_COMPLIANCE_DEEMPHASIS_OFFSET                12
#define LINK_CNTL2_COMPLIANCE_DEEMPHASIS_MASK                  0xf000

typedef union {
  struct {
    UINT16                                   TARGET_LINK_SPEED:4;
    UINT16                                    ENTER_COMPLIANCE:1;
    UINT16                         HW_AUTONOMOUS_SPEED_DISABLE:1;
    UINT16                               SELECTABLE_DEEMPHASIS:1;
    UINT16                                         XMIT_MARGIN:3;
    UINT16                                ENTER_MOD_COMPLIANCE:1;
    UINT16                                      COMPLIANCE_SOS:1;
    UINT16                               COMPLIANCE_DEEMPHASIS:4;
  } Field;
  UINT16 Value;
} LINK_CNTL2_STRUCT;

#define PCICFG_NBIFEPF0CFG_LINK_CNTL2_OFFSET                        0x94
#define SMN_DEV0_FUNC0_NBIF0NBIO0_LINK_CNTL2_ADDRESS                  0x10140094UL
#define SMN_DEV0_FUNC0_NBIF0NBIO1_LINK_CNTL2_ADDRESS                  0x10340094UL
#define SMN_DEV0_FUNC0_NBIF1NBIO0_LINK_CNTL2_ADDRESS                  0x10240094UL
#define SMN_DEV0_FUNC0_NBIF1NBIO1_LINK_CNTL2_ADDRESS                  0x10440094UL
#define SMN_DEV0_FUNC0_NBIF2NBIO0_LINK_CNTL2_ADDRESS                  0x10540094UL
#define SMN_DEV0_FUNC0_NBIF2NBIO1_LINK_CNTL2_ADDRESS                  0x10740094UL
#define SMN_DEV1_FUNC0_NBIF0NBIO0_LINK_CNTL2_ADDRESS                  0x10148094UL
#define SMN_DEV1_FUNC0_NBIF0NBIO1_LINK_CNTL2_ADDRESS                  0x10348094UL
#define SMN_DEV1_FUNC0_NBIF1NBIO0_LINK_CNTL2_ADDRESS                  0x10248094UL
#define SMN_DEV1_FUNC0_NBIF1NBIO1_LINK_CNTL2_ADDRESS                  0x10448094UL


/***********************************************************
* Register Name : LINK_CNTL_16GT
************************************************************/

#define LINK_CNTL_16GT_Reserved_31_0_OFFSET                    0
#define LINK_CNTL_16GT_Reserved_31_0_MASK                      0xffffffff

typedef union {
  struct {
    UINT32                                       Reserved_31_0:32;
  } Field;
  UINT32 Value;
} LINK_CNTL_16GT_STRUCT;

#define PCICFG_NBIFEPF0CFG_LINK_CNTL_16GT_OFFSET                    0x418
#define SMN_DEV0_FUNC0_NBIF0NBIO0_LINK_CNTL_16GT_ADDRESS              0x10140418UL
#define SMN_DEV0_FUNC0_NBIF0NBIO1_LINK_CNTL_16GT_ADDRESS              0x10340418UL
#define SMN_DEV0_FUNC0_NBIF1NBIO0_LINK_CNTL_16GT_ADDRESS              0x10240418UL
#define SMN_DEV0_FUNC0_NBIF1NBIO1_LINK_CNTL_16GT_ADDRESS              0x10440418UL
#define SMN_DEV0_FUNC0_NBIF2NBIO0_LINK_CNTL_16GT_ADDRESS              0x10540418UL
#define SMN_DEV0_FUNC0_NBIF2NBIO1_LINK_CNTL_16GT_ADDRESS              0x10740418UL
#define SMN_DEV1_FUNC0_NBIF0NBIO0_LINK_CNTL_16GT_ADDRESS              0x10148418UL
#define SMN_DEV1_FUNC0_NBIF0NBIO1_LINK_CNTL_16GT_ADDRESS              0x10348418UL
#define SMN_DEV1_FUNC0_NBIF1NBIO0_LINK_CNTL_16GT_ADDRESS              0x10248418UL
#define SMN_DEV1_FUNC0_NBIF1NBIO1_LINK_CNTL_16GT_ADDRESS              0x10448418UL


/***********************************************************
* Register Name : LINK_CNTL_32GT
************************************************************/

#define LINK_CNTL_32GT_EQ_BYPASS_TO_HIGHEST_RATE_DIS_OFFSET    0
#define LINK_CNTL_32GT_EQ_BYPASS_TO_HIGHEST_RATE_DIS_MASK      0x1

#define LINK_CNTL_32GT_NO_EQ_NEEDED_DIS_OFFSET                 1
#define LINK_CNTL_32GT_NO_EQ_NEEDED_DIS_MASK                   0x2

#define LINK_CNTL_32GT_Reserved_7_2_OFFSET                     2
#define LINK_CNTL_32GT_Reserved_7_2_MASK                       0xfc

#define LINK_CNTL_32GT_MODIFIED_TS_USAGE_MODE_SEL_OFFSET       8
#define LINK_CNTL_32GT_MODIFIED_TS_USAGE_MODE_SEL_MASK         0x700

#define LINK_CNTL_32GT_Reserved_31_11_OFFSET                   11
#define LINK_CNTL_32GT_Reserved_31_11_MASK                     0xfffff800

typedef union {
  struct {
    UINT32                       EQ_BYPASS_TO_HIGHEST_RATE_DIS:1;
    UINT32                                    NO_EQ_NEEDED_DIS:1;
    UINT32                                        Reserved_7_2:6;
    UINT32                          MODIFIED_TS_USAGE_MODE_SEL:3;
    UINT32                                      Reserved_31_11:21;
  } Field;
  UINT32 Value;
} LINK_CNTL_32GT_STRUCT;

#define PCICFG_NBIFEPF0CFG_LINK_CNTL_32GT_OFFSET                    0x508
#define SMN_DEV0_FUNC0_NBIF0NBIO0_LINK_CNTL_32GT_ADDRESS              0x10140508UL
#define SMN_DEV0_FUNC0_NBIF0NBIO1_LINK_CNTL_32GT_ADDRESS              0x10340508UL
#define SMN_DEV0_FUNC0_NBIF1NBIO0_LINK_CNTL_32GT_ADDRESS              0x10240508UL
#define SMN_DEV0_FUNC0_NBIF1NBIO1_LINK_CNTL_32GT_ADDRESS              0x10440508UL
#define SMN_DEV0_FUNC0_NBIF2NBIO0_LINK_CNTL_32GT_ADDRESS              0x10540508UL
#define SMN_DEV0_FUNC0_NBIF2NBIO1_LINK_CNTL_32GT_ADDRESS              0x10740508UL
#define SMN_DEV1_FUNC0_NBIF0NBIO0_LINK_CNTL_32GT_ADDRESS              0x10148508UL
#define SMN_DEV1_FUNC0_NBIF0NBIO1_LINK_CNTL_32GT_ADDRESS              0x10348508UL
#define SMN_DEV1_FUNC0_NBIF1NBIO0_LINK_CNTL_32GT_ADDRESS              0x10248508UL
#define SMN_DEV1_FUNC0_NBIF1NBIO1_LINK_CNTL_32GT_ADDRESS              0x10448508UL


/***********************************************************
* Register Name : LINK_STATUS
************************************************************/

#define LINK_STATUS_CURRENT_LINK_SPEED_OFFSET                  0
#define LINK_STATUS_CURRENT_LINK_SPEED_MASK                    0xf

#define LINK_STATUS_NEGOTIATED_LINK_WIDTH_OFFSET               4
#define LINK_STATUS_NEGOTIATED_LINK_WIDTH_MASK                 0x3f0

#define LINK_STATUS_Reserved_10_10_OFFSET                      10
#define LINK_STATUS_Reserved_10_10_MASK                        0x400

#define LINK_STATUS_LINK_TRAINING_OFFSET                       11
#define LINK_STATUS_LINK_TRAINING_MASK                         0x800

#define LINK_STATUS_SLOT_CLOCK_CFG_OFFSET                      12
#define LINK_STATUS_SLOT_CLOCK_CFG_MASK                        0x1000

#define LINK_STATUS_DL_ACTIVE_OFFSET                           13
#define LINK_STATUS_DL_ACTIVE_MASK                             0x2000

#define LINK_STATUS_LINK_BW_MANAGEMENT_STATUS_OFFSET           14
#define LINK_STATUS_LINK_BW_MANAGEMENT_STATUS_MASK             0x4000

#define LINK_STATUS_LINK_AUTONOMOUS_BW_STATUS_OFFSET           15
#define LINK_STATUS_LINK_AUTONOMOUS_BW_STATUS_MASK             0x8000

typedef union {
  struct {
    UINT16                                  CURRENT_LINK_SPEED:4;
    UINT16                               NEGOTIATED_LINK_WIDTH:6;
    UINT16                                      Reserved_10_10:1;
    UINT16                                       LINK_TRAINING:1;
    UINT16                                      SLOT_CLOCK_CFG:1;
    UINT16                                           DL_ACTIVE:1;
    UINT16                           LINK_BW_MANAGEMENT_STATUS:1;
    UINT16                           LINK_AUTONOMOUS_BW_STATUS:1;
  } Field;
  UINT16 Value;
} LINK_STATUS_STRUCT;

#define PCICFG_NBIFEPF0CFG_LINK_STATUS_OFFSET                       0x76
#define SMN_DEV0_FUNC0_NBIF0NBIO0_LINK_STATUS_ADDRESS                 0x10140076UL
#define SMN_DEV0_FUNC0_NBIF0NBIO1_LINK_STATUS_ADDRESS                 0x10340076UL
#define SMN_DEV0_FUNC0_NBIF1NBIO0_LINK_STATUS_ADDRESS                 0x10240076UL
#define SMN_DEV0_FUNC0_NBIF1NBIO1_LINK_STATUS_ADDRESS                 0x10440076UL
#define SMN_DEV0_FUNC0_NBIF2NBIO0_LINK_STATUS_ADDRESS                 0x10540076UL
#define SMN_DEV0_FUNC0_NBIF2NBIO1_LINK_STATUS_ADDRESS                 0x10740076UL
#define SMN_DEV1_FUNC0_NBIF0NBIO0_LINK_STATUS_ADDRESS                 0x10148076UL
#define SMN_DEV1_FUNC0_NBIF0NBIO1_LINK_STATUS_ADDRESS                 0x10348076UL
#define SMN_DEV1_FUNC0_NBIF1NBIO0_LINK_STATUS_ADDRESS                 0x10248076UL
#define SMN_DEV1_FUNC0_NBIF1NBIO1_LINK_STATUS_ADDRESS                 0x10448076UL


/***********************************************************
* Register Name : LINK_STATUS2
************************************************************/

#define LINK_STATUS2_CUR_DEEMPHASIS_LEVEL_OFFSET               0
#define LINK_STATUS2_CUR_DEEMPHASIS_LEVEL_MASK                 0x1

#define LINK_STATUS2_EQUALIZATION_COMPLETE_8GT_OFFSET          1
#define LINK_STATUS2_EQUALIZATION_COMPLETE_8GT_MASK            0x2

#define LINK_STATUS2_EQUALIZATION_PHASE1_SUCCESS_8GT_OFFSET    2
#define LINK_STATUS2_EQUALIZATION_PHASE1_SUCCESS_8GT_MASK      0x4

#define LINK_STATUS2_EQUALIZATION_PHASE2_SUCCESS_8GT_OFFSET    3
#define LINK_STATUS2_EQUALIZATION_PHASE2_SUCCESS_8GT_MASK      0x8

#define LINK_STATUS2_EQUALIZATION_PHASE3_SUCCESS_8GT_OFFSET    4
#define LINK_STATUS2_EQUALIZATION_PHASE3_SUCCESS_8GT_MASK      0x10

#define LINK_STATUS2_LINK_EQUALIZATION_REQUEST_8GT_OFFSET      5
#define LINK_STATUS2_LINK_EQUALIZATION_REQUEST_8GT_MASK        0x20

#define LINK_STATUS2_RTM1_PRESENCE_DET_OFFSET                  6
#define LINK_STATUS2_RTM1_PRESENCE_DET_MASK                    0x40

#define LINK_STATUS2_RTM2_PRESENCE_DET_OFFSET                  7
#define LINK_STATUS2_RTM2_PRESENCE_DET_MASK                    0x80

#define LINK_STATUS2_CROSSLINK_RESOLUTION_OFFSET               8
#define LINK_STATUS2_CROSSLINK_RESOLUTION_MASK                 0x300

#define LINK_STATUS2_Reserved_11_10_OFFSET                     10
#define LINK_STATUS2_Reserved_11_10_MASK                       0xc00

#define LINK_STATUS2_DOWNSTREAM_COMPONENT_PRESENCE_OFFSET      12
#define LINK_STATUS2_DOWNSTREAM_COMPONENT_PRESENCE_MASK        0x7000

#define LINK_STATUS2_DRS_MESSAGE_RECEIVED_OFFSET               15
#define LINK_STATUS2_DRS_MESSAGE_RECEIVED_MASK                 0x8000

typedef union {
  struct {
    UINT16                                CUR_DEEMPHASIS_LEVEL:1;
    UINT16                           EQUALIZATION_COMPLETE_8GT:1;
    UINT16                     EQUALIZATION_PHASE1_SUCCESS_8GT:1;
    UINT16                     EQUALIZATION_PHASE2_SUCCESS_8GT:1;
    UINT16                     EQUALIZATION_PHASE3_SUCCESS_8GT:1;
    UINT16                       LINK_EQUALIZATION_REQUEST_8GT:1;
    UINT16                                   RTM1_PRESENCE_DET:1;
    UINT16                                   RTM2_PRESENCE_DET:1;
    UINT16                                CROSSLINK_RESOLUTION:2;
    UINT16                                      Reserved_11_10:2;
    UINT16                       DOWNSTREAM_COMPONENT_PRESENCE:3;
    UINT16                                DRS_MESSAGE_RECEIVED:1;
  } Field;
  UINT16 Value;
} LINK_STATUS2_STRUCT;

#define PCICFG_NBIFEPF0CFG_LINK_STATUS2_OFFSET                      0x96
#define SMN_DEV0_FUNC0_NBIF0NBIO0_LINK_STATUS2_ADDRESS                0x10140096UL
#define SMN_DEV0_FUNC0_NBIF0NBIO1_LINK_STATUS2_ADDRESS                0x10340096UL
#define SMN_DEV0_FUNC0_NBIF1NBIO0_LINK_STATUS2_ADDRESS                0x10240096UL
#define SMN_DEV0_FUNC0_NBIF1NBIO1_LINK_STATUS2_ADDRESS                0x10440096UL
#define SMN_DEV0_FUNC0_NBIF2NBIO0_LINK_STATUS2_ADDRESS                0x10540096UL
#define SMN_DEV0_FUNC0_NBIF2NBIO1_LINK_STATUS2_ADDRESS                0x10740096UL
#define SMN_DEV1_FUNC0_NBIF0NBIO0_LINK_STATUS2_ADDRESS                0x10148096UL
#define SMN_DEV1_FUNC0_NBIF0NBIO1_LINK_STATUS2_ADDRESS                0x10348096UL
#define SMN_DEV1_FUNC0_NBIF1NBIO0_LINK_STATUS2_ADDRESS                0x10248096UL
#define SMN_DEV1_FUNC0_NBIF1NBIO1_LINK_STATUS2_ADDRESS                0x10448096UL


/***********************************************************
* Register Name : LINK_STATUS_16GT
************************************************************/

#define LINK_STATUS_16GT_EQUALIZATION_COMPLETE_16GT_OFFSET     0
#define LINK_STATUS_16GT_EQUALIZATION_COMPLETE_16GT_MASK       0x1

#define LINK_STATUS_16GT_EQUALIZATION_PHASE1_SUCCESS_16GT_OFFSET 1
#define LINK_STATUS_16GT_EQUALIZATION_PHASE1_SUCCESS_16GT_MASK 0x2

#define LINK_STATUS_16GT_EQUALIZATION_PHASE2_SUCCESS_16GT_OFFSET 2
#define LINK_STATUS_16GT_EQUALIZATION_PHASE2_SUCCESS_16GT_MASK 0x4

#define LINK_STATUS_16GT_EQUALIZATION_PHASE3_SUCCESS_16GT_OFFSET 3
#define LINK_STATUS_16GT_EQUALIZATION_PHASE3_SUCCESS_16GT_MASK 0x8

#define LINK_STATUS_16GT_LINK_EQUALIZATION_REQUEST_16GT_OFFSET 4
#define LINK_STATUS_16GT_LINK_EQUALIZATION_REQUEST_16GT_MASK   0x10

#define LINK_STATUS_16GT_Reserved_31_5_OFFSET                  5
#define LINK_STATUS_16GT_Reserved_31_5_MASK                    0xffffffe0

typedef union {
  struct {
    UINT32                          EQUALIZATION_COMPLETE_16GT:1;
    UINT32                    EQUALIZATION_PHASE1_SUCCESS_16GT:1;
    UINT32                    EQUALIZATION_PHASE2_SUCCESS_16GT:1;
    UINT32                    EQUALIZATION_PHASE3_SUCCESS_16GT:1;
    UINT32                      LINK_EQUALIZATION_REQUEST_16GT:1;
    UINT32                                       Reserved_31_5:27;
  } Field;
  UINT32 Value;
} LINK_STATUS_16GT_STRUCT;

#define PCICFG_NBIFEPF0CFG_LINK_STATUS_16GT_OFFSET                  0x41c
#define SMN_DEV0_FUNC0_NBIF0NBIO0_LINK_STATUS_16GT_ADDRESS            0x1014041cUL
#define SMN_DEV0_FUNC0_NBIF0NBIO1_LINK_STATUS_16GT_ADDRESS            0x1034041cUL
#define SMN_DEV0_FUNC0_NBIF1NBIO0_LINK_STATUS_16GT_ADDRESS            0x1024041cUL
#define SMN_DEV0_FUNC0_NBIF1NBIO1_LINK_STATUS_16GT_ADDRESS            0x1044041cUL
#define SMN_DEV0_FUNC0_NBIF2NBIO0_LINK_STATUS_16GT_ADDRESS            0x1054041cUL
#define SMN_DEV0_FUNC0_NBIF2NBIO1_LINK_STATUS_16GT_ADDRESS            0x1074041cUL
#define SMN_DEV1_FUNC0_NBIF0NBIO0_LINK_STATUS_16GT_ADDRESS            0x1014841cUL
#define SMN_DEV1_FUNC0_NBIF0NBIO1_LINK_STATUS_16GT_ADDRESS            0x1034841cUL
#define SMN_DEV1_FUNC0_NBIF1NBIO0_LINK_STATUS_16GT_ADDRESS            0x1024841cUL
#define SMN_DEV1_FUNC0_NBIF1NBIO1_LINK_STATUS_16GT_ADDRESS            0x1044841cUL


/***********************************************************
* Register Name : LINK_STATUS_32GT
************************************************************/

#define LINK_STATUS_32GT_EQUALIZATION_COMPLETE_32GT_OFFSET     0
#define LINK_STATUS_32GT_EQUALIZATION_COMPLETE_32GT_MASK       0x1

#define LINK_STATUS_32GT_EQUALIZATION_PHASE1_SUCCESS_32GT_OFFSET 1
#define LINK_STATUS_32GT_EQUALIZATION_PHASE1_SUCCESS_32GT_MASK 0x2

#define LINK_STATUS_32GT_EQUALIZATION_PHASE2_SUCCESS_32GT_OFFSET 2
#define LINK_STATUS_32GT_EQUALIZATION_PHASE2_SUCCESS_32GT_MASK 0x4

#define LINK_STATUS_32GT_EQUALIZATION_PHASE3_SUCCESS_32GT_OFFSET 3
#define LINK_STATUS_32GT_EQUALIZATION_PHASE3_SUCCESS_32GT_MASK 0x8

#define LINK_STATUS_32GT_LINK_EQUALIZATION_REQUEST_32GT_OFFSET 4
#define LINK_STATUS_32GT_LINK_EQUALIZATION_REQUEST_32GT_MASK   0x10

#define LINK_STATUS_32GT_MODIFIED_TS_RECEIVED_OFFSET           5
#define LINK_STATUS_32GT_MODIFIED_TS_RECEIVED_MASK             0x20

#define LINK_STATUS_32GT_RECEIVED_ENHANCED_LINK_BEHAVIOR_CNTL_OFFSET 6
#define LINK_STATUS_32GT_RECEIVED_ENHANCED_LINK_BEHAVIOR_CNTL_MASK 0xc0

#define LINK_STATUS_32GT_TRANSMITTER_PRECODING_ON_OFFSET       8
#define LINK_STATUS_32GT_TRANSMITTER_PRECODING_ON_MASK         0x100

#define LINK_STATUS_32GT_TRANSMITTER_PRECODE_REQUEST_OFFSET    9
#define LINK_STATUS_32GT_TRANSMITTER_PRECODE_REQUEST_MASK      0x200

#define LINK_STATUS_32GT_NO_EQ_NEEDED_RECEIVED_OFFSET          10
#define LINK_STATUS_32GT_NO_EQ_NEEDED_RECEIVED_MASK            0x400

#define LINK_STATUS_32GT_Reserved_31_11_OFFSET                 11
#define LINK_STATUS_32GT_Reserved_31_11_MASK                   0xfffff800

typedef union {
  struct {
    UINT32                          EQUALIZATION_COMPLETE_32GT:1;
    UINT32                    EQUALIZATION_PHASE1_SUCCESS_32GT:1;
    UINT32                    EQUALIZATION_PHASE2_SUCCESS_32GT:1;
    UINT32                    EQUALIZATION_PHASE3_SUCCESS_32GT:1;
    UINT32                      LINK_EQUALIZATION_REQUEST_32GT:1;
    UINT32                                MODIFIED_TS_RECEIVED:1;
    UINT32                RECEIVED_ENHANCED_LINK_BEHAVIOR_CNTL:2;
    UINT32                            TRANSMITTER_PRECODING_ON:1;
    UINT32                         TRANSMITTER_PRECODE_REQUEST:1;
    UINT32                               NO_EQ_NEEDED_RECEIVED:1;
    UINT32                                      Reserved_31_11:21;
  } Field;
  UINT32 Value;
} LINK_STATUS_32GT_STRUCT;

#define PCICFG_NBIFEPF0CFG_LINK_STATUS_32GT_OFFSET                  0x50c
#define SMN_DEV0_FUNC0_NBIF0NBIO0_LINK_STATUS_32GT_ADDRESS            0x1014050cUL
#define SMN_DEV0_FUNC0_NBIF0NBIO1_LINK_STATUS_32GT_ADDRESS            0x1034050cUL
#define SMN_DEV0_FUNC0_NBIF1NBIO0_LINK_STATUS_32GT_ADDRESS            0x1024050cUL
#define SMN_DEV0_FUNC0_NBIF1NBIO1_LINK_STATUS_32GT_ADDRESS            0x1044050cUL
#define SMN_DEV0_FUNC0_NBIF2NBIO0_LINK_STATUS_32GT_ADDRESS            0x1054050cUL
#define SMN_DEV0_FUNC0_NBIF2NBIO1_LINK_STATUS_32GT_ADDRESS            0x1074050cUL
#define SMN_DEV1_FUNC0_NBIF0NBIO0_LINK_STATUS_32GT_ADDRESS            0x1014850cUL
#define SMN_DEV1_FUNC0_NBIF0NBIO1_LINK_STATUS_32GT_ADDRESS            0x1034850cUL
#define SMN_DEV1_FUNC0_NBIF1NBIO0_LINK_STATUS_32GT_ADDRESS            0x1024850cUL
#define SMN_DEV1_FUNC0_NBIF1NBIO1_LINK_STATUS_32GT_ADDRESS            0x1044850cUL


/***********************************************************
* Register Name : LOCAL_PARITY_MISMATCH_STATUS_16GT
************************************************************/

#define LOCAL_PARITY_MISMATCH_STATUS_16GT_LOCAL_PARITY_MISMATCH_STATUS_BITS_OFFSET 0
#define LOCAL_PARITY_MISMATCH_STATUS_16GT_LOCAL_PARITY_MISMATCH_STATUS_BITS_MASK 0xffff

#define LOCAL_PARITY_MISMATCH_STATUS_16GT_Reserved_31_16_OFFSET 16
#define LOCAL_PARITY_MISMATCH_STATUS_16GT_Reserved_31_16_MASK  0xffff0000

typedef union {
  struct {
    UINT32                   LOCAL_PARITY_MISMATCH_STATUS_BITS:16;
    UINT32                                      Reserved_31_16:16;
  } Field;
  UINT32 Value;
} LOCAL_PARITY_MISMATCH_STATUS_16GT_STRUCT;

#define PCICFG_NBIFEPF0CFG_LOCAL_PARITY_MISMATCH_STATUS_16GT_OFFSET 0x420
#define SMN_DEV0_FUNC0_NBIF0NBIO0_LOCAL_PARITY_MISMATCH_STATUS_16GT_ADDRESS 0x10140420UL
#define SMN_DEV0_FUNC0_NBIF0NBIO1_LOCAL_PARITY_MISMATCH_STATUS_16GT_ADDRESS 0x10340420UL
#define SMN_DEV0_FUNC0_NBIF1NBIO0_LOCAL_PARITY_MISMATCH_STATUS_16GT_ADDRESS 0x10240420UL
#define SMN_DEV0_FUNC0_NBIF1NBIO1_LOCAL_PARITY_MISMATCH_STATUS_16GT_ADDRESS 0x10440420UL
#define SMN_DEV0_FUNC0_NBIF2NBIO0_LOCAL_PARITY_MISMATCH_STATUS_16GT_ADDRESS 0x10540420UL
#define SMN_DEV0_FUNC0_NBIF2NBIO1_LOCAL_PARITY_MISMATCH_STATUS_16GT_ADDRESS 0x10740420UL
#define SMN_DEV1_FUNC0_NBIF0NBIO0_LOCAL_PARITY_MISMATCH_STATUS_16GT_ADDRESS 0x10148420UL
#define SMN_DEV1_FUNC0_NBIF0NBIO1_LOCAL_PARITY_MISMATCH_STATUS_16GT_ADDRESS 0x10348420UL
#define SMN_DEV1_FUNC0_NBIF1NBIO0_LOCAL_PARITY_MISMATCH_STATUS_16GT_ADDRESS 0x10248420UL
#define SMN_DEV1_FUNC0_NBIF1NBIO1_LOCAL_PARITY_MISMATCH_STATUS_16GT_ADDRESS 0x10448420UL


/***********************************************************
* Register Name : MARGINING_LANE_CNTL
************************************************************/

#define MARGINING_LANE_CNTL_RECEIVER_NUMBER_OFFSET             0
#define MARGINING_LANE_CNTL_RECEIVER_NUMBER_MASK               0x7

#define MARGINING_LANE_CNTL_MARGIN_TYPE_OFFSET                 3
#define MARGINING_LANE_CNTL_MARGIN_TYPE_MASK                   0x38

#define MARGINING_LANE_CNTL_USAGE_MODEL_OFFSET                 6
#define MARGINING_LANE_CNTL_USAGE_MODEL_MASK                   0x40

#define MARGINING_LANE_CNTL_Reserved_7_7_OFFSET                7
#define MARGINING_LANE_CNTL_Reserved_7_7_MASK                  0x80

#define MARGINING_LANE_CNTL_MARGIN_PAYLOAD_OFFSET              8
#define MARGINING_LANE_CNTL_MARGIN_PAYLOAD_MASK                0xff00

typedef union {
  struct {
    UINT16                                     RECEIVER_NUMBER:3;
    UINT16                                         MARGIN_TYPE:3;
    UINT16                                         USAGE_MODEL:1;
    UINT16                                        Reserved_7_7:1;
    UINT16                                      MARGIN_PAYLOAD:8;
  } Field;
  UINT16 Value;
} MARGINING_LANE_CNTL_STRUCT;

#define PCICFG_NBIFEPF0CFG_MARGINING_LANE_CNTL_OFFSET               0x458
#define SMN_DEV0_FUNC0_NBIF0_N0NBIO0_MARGINING_LANE_CNTL_ADDRESS      0x10140458UL
#define SMN_DEV0_FUNC0_NBIF0_N0NBIO1_MARGINING_LANE_CNTL_ADDRESS      0x10340458UL
#define SMN_DEV0_FUNC0_NBIF0_N10NBIO0_MARGINING_LANE_CNTL_ADDRESS     0x10140480UL
#define SMN_DEV0_FUNC0_NBIF0_N10NBIO1_MARGINING_LANE_CNTL_ADDRESS     0x10340480UL
#define SMN_DEV0_FUNC0_NBIF0_N11NBIO0_MARGINING_LANE_CNTL_ADDRESS     0x10140484UL
#define SMN_DEV0_FUNC0_NBIF0_N11NBIO1_MARGINING_LANE_CNTL_ADDRESS     0x10340484UL
#define SMN_DEV0_FUNC0_NBIF0_N12NBIO0_MARGINING_LANE_CNTL_ADDRESS     0x10140488UL
#define SMN_DEV0_FUNC0_NBIF0_N12NBIO1_MARGINING_LANE_CNTL_ADDRESS     0x10340488UL
#define SMN_DEV0_FUNC0_NBIF0_N13NBIO0_MARGINING_LANE_CNTL_ADDRESS     0x1014048cUL
#define SMN_DEV0_FUNC0_NBIF0_N13NBIO1_MARGINING_LANE_CNTL_ADDRESS     0x1034048cUL
#define SMN_DEV0_FUNC0_NBIF0_N14NBIO0_MARGINING_LANE_CNTL_ADDRESS     0x10140490UL
#define SMN_DEV0_FUNC0_NBIF0_N14NBIO1_MARGINING_LANE_CNTL_ADDRESS     0x10340490UL
#define SMN_DEV0_FUNC0_NBIF0_N15NBIO0_MARGINING_LANE_CNTL_ADDRESS     0x10140494UL
#define SMN_DEV0_FUNC0_NBIF0_N15NBIO1_MARGINING_LANE_CNTL_ADDRESS     0x10340494UL
#define SMN_DEV0_FUNC0_NBIF0_N1NBIO0_MARGINING_LANE_CNTL_ADDRESS      0x1014045cUL
#define SMN_DEV0_FUNC0_NBIF0_N1NBIO1_MARGINING_LANE_CNTL_ADDRESS      0x1034045cUL
#define SMN_DEV0_FUNC0_NBIF0_N2NBIO0_MARGINING_LANE_CNTL_ADDRESS      0x10140460UL
#define SMN_DEV0_FUNC0_NBIF0_N2NBIO1_MARGINING_LANE_CNTL_ADDRESS      0x10340460UL
#define SMN_DEV0_FUNC0_NBIF0_N3NBIO0_MARGINING_LANE_CNTL_ADDRESS      0x10140464UL
#define SMN_DEV0_FUNC0_NBIF0_N3NBIO1_MARGINING_LANE_CNTL_ADDRESS      0x10340464UL
#define SMN_DEV0_FUNC0_NBIF0_N4NBIO0_MARGINING_LANE_CNTL_ADDRESS      0x10140468UL
#define SMN_DEV0_FUNC0_NBIF0_N4NBIO1_MARGINING_LANE_CNTL_ADDRESS      0x10340468UL
#define SMN_DEV0_FUNC0_NBIF0_N5NBIO0_MARGINING_LANE_CNTL_ADDRESS      0x1014046cUL
#define SMN_DEV0_FUNC0_NBIF0_N5NBIO1_MARGINING_LANE_CNTL_ADDRESS      0x1034046cUL
#define SMN_DEV0_FUNC0_NBIF0_N6NBIO0_MARGINING_LANE_CNTL_ADDRESS      0x10140470UL
#define SMN_DEV0_FUNC0_NBIF0_N6NBIO1_MARGINING_LANE_CNTL_ADDRESS      0x10340470UL
#define SMN_DEV0_FUNC0_NBIF0_N7NBIO0_MARGINING_LANE_CNTL_ADDRESS      0x10140474UL
#define SMN_DEV0_FUNC0_NBIF0_N7NBIO1_MARGINING_LANE_CNTL_ADDRESS      0x10340474UL
#define SMN_DEV0_FUNC0_NBIF0_N8NBIO0_MARGINING_LANE_CNTL_ADDRESS      0x10140478UL
#define SMN_DEV0_FUNC0_NBIF0_N8NBIO1_MARGINING_LANE_CNTL_ADDRESS      0x10340478UL
#define SMN_DEV0_FUNC0_NBIF0_N9NBIO0_MARGINING_LANE_CNTL_ADDRESS      0x1014047cUL
#define SMN_DEV0_FUNC0_NBIF0_N9NBIO1_MARGINING_LANE_CNTL_ADDRESS      0x1034047cUL
#define SMN_DEV0_FUNC0_NBIF1_N0NBIO0_MARGINING_LANE_CNTL_ADDRESS      0x10240458UL
#define SMN_DEV0_FUNC0_NBIF1_N0NBIO1_MARGINING_LANE_CNTL_ADDRESS      0x10440458UL
#define SMN_DEV0_FUNC0_NBIF1_N10NBIO0_MARGINING_LANE_CNTL_ADDRESS     0x10240480UL
#define SMN_DEV0_FUNC0_NBIF1_N10NBIO1_MARGINING_LANE_CNTL_ADDRESS     0x10440480UL
#define SMN_DEV0_FUNC0_NBIF1_N11NBIO0_MARGINING_LANE_CNTL_ADDRESS     0x10240484UL
#define SMN_DEV0_FUNC0_NBIF1_N11NBIO1_MARGINING_LANE_CNTL_ADDRESS     0x10440484UL
#define SMN_DEV0_FUNC0_NBIF1_N12NBIO0_MARGINING_LANE_CNTL_ADDRESS     0x10240488UL
#define SMN_DEV0_FUNC0_NBIF1_N12NBIO1_MARGINING_LANE_CNTL_ADDRESS     0x10440488UL
#define SMN_DEV0_FUNC0_NBIF1_N13NBIO0_MARGINING_LANE_CNTL_ADDRESS     0x1024048cUL
#define SMN_DEV0_FUNC0_NBIF1_N13NBIO1_MARGINING_LANE_CNTL_ADDRESS     0x1044048cUL
#define SMN_DEV0_FUNC0_NBIF1_N14NBIO0_MARGINING_LANE_CNTL_ADDRESS     0x10240490UL
#define SMN_DEV0_FUNC0_NBIF1_N14NBIO1_MARGINING_LANE_CNTL_ADDRESS     0x10440490UL
#define SMN_DEV0_FUNC0_NBIF1_N15NBIO0_MARGINING_LANE_CNTL_ADDRESS     0x10240494UL
#define SMN_DEV0_FUNC0_NBIF1_N15NBIO1_MARGINING_LANE_CNTL_ADDRESS     0x10440494UL
#define SMN_DEV0_FUNC0_NBIF1_N1NBIO0_MARGINING_LANE_CNTL_ADDRESS      0x1024045cUL
#define SMN_DEV0_FUNC0_NBIF1_N1NBIO1_MARGINING_LANE_CNTL_ADDRESS      0x1044045cUL
#define SMN_DEV0_FUNC0_NBIF1_N2NBIO0_MARGINING_LANE_CNTL_ADDRESS      0x10240460UL
#define SMN_DEV0_FUNC0_NBIF1_N2NBIO1_MARGINING_LANE_CNTL_ADDRESS      0x10440460UL
#define SMN_DEV0_FUNC0_NBIF1_N3NBIO0_MARGINING_LANE_CNTL_ADDRESS      0x10240464UL
#define SMN_DEV0_FUNC0_NBIF1_N3NBIO1_MARGINING_LANE_CNTL_ADDRESS      0x10440464UL
#define SMN_DEV0_FUNC0_NBIF1_N4NBIO0_MARGINING_LANE_CNTL_ADDRESS      0x10240468UL
#define SMN_DEV0_FUNC0_NBIF1_N4NBIO1_MARGINING_LANE_CNTL_ADDRESS      0x10440468UL
#define SMN_DEV0_FUNC0_NBIF1_N5NBIO0_MARGINING_LANE_CNTL_ADDRESS      0x1024046cUL
#define SMN_DEV0_FUNC0_NBIF1_N5NBIO1_MARGINING_LANE_CNTL_ADDRESS      0x1044046cUL
#define SMN_DEV0_FUNC0_NBIF1_N6NBIO0_MARGINING_LANE_CNTL_ADDRESS      0x10240470UL
#define SMN_DEV0_FUNC0_NBIF1_N6NBIO1_MARGINING_LANE_CNTL_ADDRESS      0x10440470UL
#define SMN_DEV0_FUNC0_NBIF1_N7NBIO0_MARGINING_LANE_CNTL_ADDRESS      0x10240474UL
#define SMN_DEV0_FUNC0_NBIF1_N7NBIO1_MARGINING_LANE_CNTL_ADDRESS      0x10440474UL
#define SMN_DEV0_FUNC0_NBIF1_N8NBIO0_MARGINING_LANE_CNTL_ADDRESS      0x10240478UL
#define SMN_DEV0_FUNC0_NBIF1_N8NBIO1_MARGINING_LANE_CNTL_ADDRESS      0x10440478UL
#define SMN_DEV0_FUNC0_NBIF1_N9NBIO0_MARGINING_LANE_CNTL_ADDRESS      0x1024047cUL
#define SMN_DEV0_FUNC0_NBIF1_N9NBIO1_MARGINING_LANE_CNTL_ADDRESS      0x1044047cUL
#define SMN_DEV0_FUNC0_NBIF2_N0NBIO0_MARGINING_LANE_CNTL_ADDRESS      0x10540458UL
#define SMN_DEV0_FUNC0_NBIF2_N0NBIO1_MARGINING_LANE_CNTL_ADDRESS      0x10740458UL
#define SMN_DEV0_FUNC0_NBIF2_N10NBIO0_MARGINING_LANE_CNTL_ADDRESS     0x10540480UL
#define SMN_DEV0_FUNC0_NBIF2_N10NBIO1_MARGINING_LANE_CNTL_ADDRESS     0x10740480UL
#define SMN_DEV0_FUNC0_NBIF2_N11NBIO0_MARGINING_LANE_CNTL_ADDRESS     0x10540484UL
#define SMN_DEV0_FUNC0_NBIF2_N11NBIO1_MARGINING_LANE_CNTL_ADDRESS     0x10740484UL
#define SMN_DEV0_FUNC0_NBIF2_N12NBIO0_MARGINING_LANE_CNTL_ADDRESS     0x10540488UL
#define SMN_DEV0_FUNC0_NBIF2_N12NBIO1_MARGINING_LANE_CNTL_ADDRESS     0x10740488UL
#define SMN_DEV0_FUNC0_NBIF2_N13NBIO0_MARGINING_LANE_CNTL_ADDRESS     0x1054048cUL
#define SMN_DEV0_FUNC0_NBIF2_N13NBIO1_MARGINING_LANE_CNTL_ADDRESS     0x1074048cUL
#define SMN_DEV0_FUNC0_NBIF2_N14NBIO0_MARGINING_LANE_CNTL_ADDRESS     0x10540490UL
#define SMN_DEV0_FUNC0_NBIF2_N14NBIO1_MARGINING_LANE_CNTL_ADDRESS     0x10740490UL
#define SMN_DEV0_FUNC0_NBIF2_N15NBIO0_MARGINING_LANE_CNTL_ADDRESS     0x10540494UL
#define SMN_DEV0_FUNC0_NBIF2_N15NBIO1_MARGINING_LANE_CNTL_ADDRESS     0x10740494UL
#define SMN_DEV0_FUNC0_NBIF2_N1NBIO0_MARGINING_LANE_CNTL_ADDRESS      0x1054045cUL
#define SMN_DEV0_FUNC0_NBIF2_N1NBIO1_MARGINING_LANE_CNTL_ADDRESS      0x1074045cUL
#define SMN_DEV0_FUNC0_NBIF2_N2NBIO0_MARGINING_LANE_CNTL_ADDRESS      0x10540460UL
#define SMN_DEV0_FUNC0_NBIF2_N2NBIO1_MARGINING_LANE_CNTL_ADDRESS      0x10740460UL
#define SMN_DEV0_FUNC0_NBIF2_N3NBIO0_MARGINING_LANE_CNTL_ADDRESS      0x10540464UL
#define SMN_DEV0_FUNC0_NBIF2_N3NBIO1_MARGINING_LANE_CNTL_ADDRESS      0x10740464UL
#define SMN_DEV0_FUNC0_NBIF2_N4NBIO0_MARGINING_LANE_CNTL_ADDRESS      0x10540468UL
#define SMN_DEV0_FUNC0_NBIF2_N4NBIO1_MARGINING_LANE_CNTL_ADDRESS      0x10740468UL
#define SMN_DEV0_FUNC0_NBIF2_N5NBIO0_MARGINING_LANE_CNTL_ADDRESS      0x1054046cUL
#define SMN_DEV0_FUNC0_NBIF2_N5NBIO1_MARGINING_LANE_CNTL_ADDRESS      0x1074046cUL
#define SMN_DEV0_FUNC0_NBIF2_N6NBIO0_MARGINING_LANE_CNTL_ADDRESS      0x10540470UL
#define SMN_DEV0_FUNC0_NBIF2_N6NBIO1_MARGINING_LANE_CNTL_ADDRESS      0x10740470UL
#define SMN_DEV0_FUNC0_NBIF2_N7NBIO0_MARGINING_LANE_CNTL_ADDRESS      0x10540474UL
#define SMN_DEV0_FUNC0_NBIF2_N7NBIO1_MARGINING_LANE_CNTL_ADDRESS      0x10740474UL
#define SMN_DEV0_FUNC0_NBIF2_N8NBIO0_MARGINING_LANE_CNTL_ADDRESS      0x10540478UL
#define SMN_DEV0_FUNC0_NBIF2_N8NBIO1_MARGINING_LANE_CNTL_ADDRESS      0x10740478UL
#define SMN_DEV0_FUNC0_NBIF2_N9NBIO0_MARGINING_LANE_CNTL_ADDRESS      0x1054047cUL
#define SMN_DEV0_FUNC0_NBIF2_N9NBIO1_MARGINING_LANE_CNTL_ADDRESS      0x1074047cUL
#define SMN_DEV1_FUNC0_NBIF0_N0NBIO0_MARGINING_LANE_CNTL_ADDRESS      0x10148458UL
#define SMN_DEV1_FUNC0_NBIF0_N0NBIO1_MARGINING_LANE_CNTL_ADDRESS      0x10348458UL
#define SMN_DEV1_FUNC0_NBIF0_N10NBIO0_MARGINING_LANE_CNTL_ADDRESS     0x10148480UL
#define SMN_DEV1_FUNC0_NBIF0_N10NBIO1_MARGINING_LANE_CNTL_ADDRESS     0x10348480UL
#define SMN_DEV1_FUNC0_NBIF0_N11NBIO0_MARGINING_LANE_CNTL_ADDRESS     0x10148484UL
#define SMN_DEV1_FUNC0_NBIF0_N11NBIO1_MARGINING_LANE_CNTL_ADDRESS     0x10348484UL
#define SMN_DEV1_FUNC0_NBIF0_N12NBIO0_MARGINING_LANE_CNTL_ADDRESS     0x10148488UL
#define SMN_DEV1_FUNC0_NBIF0_N12NBIO1_MARGINING_LANE_CNTL_ADDRESS     0x10348488UL
#define SMN_DEV1_FUNC0_NBIF0_N13NBIO0_MARGINING_LANE_CNTL_ADDRESS     0x1014848cUL
#define SMN_DEV1_FUNC0_NBIF0_N13NBIO1_MARGINING_LANE_CNTL_ADDRESS     0x1034848cUL
#define SMN_DEV1_FUNC0_NBIF0_N14NBIO0_MARGINING_LANE_CNTL_ADDRESS     0x10148490UL
#define SMN_DEV1_FUNC0_NBIF0_N14NBIO1_MARGINING_LANE_CNTL_ADDRESS     0x10348490UL
#define SMN_DEV1_FUNC0_NBIF0_N15NBIO0_MARGINING_LANE_CNTL_ADDRESS     0x10148494UL
#define SMN_DEV1_FUNC0_NBIF0_N15NBIO1_MARGINING_LANE_CNTL_ADDRESS     0x10348494UL
#define SMN_DEV1_FUNC0_NBIF0_N1NBIO0_MARGINING_LANE_CNTL_ADDRESS      0x1014845cUL
#define SMN_DEV1_FUNC0_NBIF0_N1NBIO1_MARGINING_LANE_CNTL_ADDRESS      0x1034845cUL
#define SMN_DEV1_FUNC0_NBIF0_N2NBIO0_MARGINING_LANE_CNTL_ADDRESS      0x10148460UL
#define SMN_DEV1_FUNC0_NBIF0_N2NBIO1_MARGINING_LANE_CNTL_ADDRESS      0x10348460UL
#define SMN_DEV1_FUNC0_NBIF0_N3NBIO0_MARGINING_LANE_CNTL_ADDRESS      0x10148464UL
#define SMN_DEV1_FUNC0_NBIF0_N3NBIO1_MARGINING_LANE_CNTL_ADDRESS      0x10348464UL
#define SMN_DEV1_FUNC0_NBIF0_N4NBIO0_MARGINING_LANE_CNTL_ADDRESS      0x10148468UL
#define SMN_DEV1_FUNC0_NBIF0_N4NBIO1_MARGINING_LANE_CNTL_ADDRESS      0x10348468UL
#define SMN_DEV1_FUNC0_NBIF0_N5NBIO0_MARGINING_LANE_CNTL_ADDRESS      0x1014846cUL
#define SMN_DEV1_FUNC0_NBIF0_N5NBIO1_MARGINING_LANE_CNTL_ADDRESS      0x1034846cUL
#define SMN_DEV1_FUNC0_NBIF0_N6NBIO0_MARGINING_LANE_CNTL_ADDRESS      0x10148470UL
#define SMN_DEV1_FUNC0_NBIF0_N6NBIO1_MARGINING_LANE_CNTL_ADDRESS      0x10348470UL
#define SMN_DEV1_FUNC0_NBIF0_N7NBIO0_MARGINING_LANE_CNTL_ADDRESS      0x10148474UL
#define SMN_DEV1_FUNC0_NBIF0_N7NBIO1_MARGINING_LANE_CNTL_ADDRESS      0x10348474UL
#define SMN_DEV1_FUNC0_NBIF0_N8NBIO0_MARGINING_LANE_CNTL_ADDRESS      0x10148478UL
#define SMN_DEV1_FUNC0_NBIF0_N8NBIO1_MARGINING_LANE_CNTL_ADDRESS      0x10348478UL
#define SMN_DEV1_FUNC0_NBIF0_N9NBIO0_MARGINING_LANE_CNTL_ADDRESS      0x1014847cUL
#define SMN_DEV1_FUNC0_NBIF0_N9NBIO1_MARGINING_LANE_CNTL_ADDRESS      0x1034847cUL
#define SMN_DEV1_FUNC0_NBIF1_N0NBIO0_MARGINING_LANE_CNTL_ADDRESS      0x10248458UL
#define SMN_DEV1_FUNC0_NBIF1_N0NBIO1_MARGINING_LANE_CNTL_ADDRESS      0x10448458UL
#define SMN_DEV1_FUNC0_NBIF1_N10NBIO0_MARGINING_LANE_CNTL_ADDRESS     0x10248480UL
#define SMN_DEV1_FUNC0_NBIF1_N10NBIO1_MARGINING_LANE_CNTL_ADDRESS     0x10448480UL
#define SMN_DEV1_FUNC0_NBIF1_N11NBIO0_MARGINING_LANE_CNTL_ADDRESS     0x10248484UL
#define SMN_DEV1_FUNC0_NBIF1_N11NBIO1_MARGINING_LANE_CNTL_ADDRESS     0x10448484UL
#define SMN_DEV1_FUNC0_NBIF1_N12NBIO0_MARGINING_LANE_CNTL_ADDRESS     0x10248488UL
#define SMN_DEV1_FUNC0_NBIF1_N12NBIO1_MARGINING_LANE_CNTL_ADDRESS     0x10448488UL
#define SMN_DEV1_FUNC0_NBIF1_N13NBIO0_MARGINING_LANE_CNTL_ADDRESS     0x1024848cUL
#define SMN_DEV1_FUNC0_NBIF1_N13NBIO1_MARGINING_LANE_CNTL_ADDRESS     0x1044848cUL
#define SMN_DEV1_FUNC0_NBIF1_N14NBIO0_MARGINING_LANE_CNTL_ADDRESS     0x10248490UL
#define SMN_DEV1_FUNC0_NBIF1_N14NBIO1_MARGINING_LANE_CNTL_ADDRESS     0x10448490UL
#define SMN_DEV1_FUNC0_NBIF1_N15NBIO0_MARGINING_LANE_CNTL_ADDRESS     0x10248494UL
#define SMN_DEV1_FUNC0_NBIF1_N15NBIO1_MARGINING_LANE_CNTL_ADDRESS     0x10448494UL
#define SMN_DEV1_FUNC0_NBIF1_N1NBIO0_MARGINING_LANE_CNTL_ADDRESS      0x1024845cUL
#define SMN_DEV1_FUNC0_NBIF1_N1NBIO1_MARGINING_LANE_CNTL_ADDRESS      0x1044845cUL
#define SMN_DEV1_FUNC0_NBIF1_N2NBIO0_MARGINING_LANE_CNTL_ADDRESS      0x10248460UL
#define SMN_DEV1_FUNC0_NBIF1_N2NBIO1_MARGINING_LANE_CNTL_ADDRESS      0x10448460UL
#define SMN_DEV1_FUNC0_NBIF1_N3NBIO0_MARGINING_LANE_CNTL_ADDRESS      0x10248464UL
#define SMN_DEV1_FUNC0_NBIF1_N3NBIO1_MARGINING_LANE_CNTL_ADDRESS      0x10448464UL
#define SMN_DEV1_FUNC0_NBIF1_N4NBIO0_MARGINING_LANE_CNTL_ADDRESS      0x10248468UL
#define SMN_DEV1_FUNC0_NBIF1_N4NBIO1_MARGINING_LANE_CNTL_ADDRESS      0x10448468UL
#define SMN_DEV1_FUNC0_NBIF1_N5NBIO0_MARGINING_LANE_CNTL_ADDRESS      0x1024846cUL
#define SMN_DEV1_FUNC0_NBIF1_N5NBIO1_MARGINING_LANE_CNTL_ADDRESS      0x1044846cUL
#define SMN_DEV1_FUNC0_NBIF1_N6NBIO0_MARGINING_LANE_CNTL_ADDRESS      0x10248470UL
#define SMN_DEV1_FUNC0_NBIF1_N6NBIO1_MARGINING_LANE_CNTL_ADDRESS      0x10448470UL
#define SMN_DEV1_FUNC0_NBIF1_N7NBIO0_MARGINING_LANE_CNTL_ADDRESS      0x10248474UL
#define SMN_DEV1_FUNC0_NBIF1_N7NBIO1_MARGINING_LANE_CNTL_ADDRESS      0x10448474UL
#define SMN_DEV1_FUNC0_NBIF1_N8NBIO0_MARGINING_LANE_CNTL_ADDRESS      0x10248478UL
#define SMN_DEV1_FUNC0_NBIF1_N8NBIO1_MARGINING_LANE_CNTL_ADDRESS      0x10448478UL
#define SMN_DEV1_FUNC0_NBIF1_N9NBIO0_MARGINING_LANE_CNTL_ADDRESS      0x1024847cUL
#define SMN_DEV1_FUNC0_NBIF1_N9NBIO1_MARGINING_LANE_CNTL_ADDRESS      0x1044847cUL


/***********************************************************
* Register Name : MARGINING_LANE_STATUS
************************************************************/

#define MARGINING_LANE_STATUS_RECEIVER_NUMBER_STATUS_OFFSET    0
#define MARGINING_LANE_STATUS_RECEIVER_NUMBER_STATUS_MASK      0x7

#define MARGINING_LANE_STATUS_MARGIN_TYPE_STATUS_OFFSET        3
#define MARGINING_LANE_STATUS_MARGIN_TYPE_STATUS_MASK          0x38

#define MARGINING_LANE_STATUS_USAGE_MODEL_STATUS_OFFSET        6
#define MARGINING_LANE_STATUS_USAGE_MODEL_STATUS_MASK          0x40

#define MARGINING_LANE_STATUS_Reserved_7_7_OFFSET              7
#define MARGINING_LANE_STATUS_Reserved_7_7_MASK                0x80

#define MARGINING_LANE_STATUS_MARGIN_PAYLOAD_STATUS_OFFSET     8
#define MARGINING_LANE_STATUS_MARGIN_PAYLOAD_STATUS_MASK       0xff00

typedef union {
  struct {
    UINT16                              RECEIVER_NUMBER_STATUS:3;
    UINT16                                  MARGIN_TYPE_STATUS:3;
    UINT16                                  USAGE_MODEL_STATUS:1;
    UINT16                                        Reserved_7_7:1;
    UINT16                               MARGIN_PAYLOAD_STATUS:8;
  } Field;
  UINT16 Value;
} MARGINING_LANE_STATUS_STRUCT;

#define PCICFG_NBIFEPF0CFG_MARGINING_LANE_STATUS_OFFSET             0x45a
#define SMN_DEV0_FUNC0_NBIF0_N0NBIO0_MARGINING_LANE_STATUS_ADDRESS    0x1014045aUL
#define SMN_DEV0_FUNC0_NBIF0_N0NBIO1_MARGINING_LANE_STATUS_ADDRESS    0x1034045aUL
#define SMN_DEV0_FUNC0_NBIF0_N10NBIO0_MARGINING_LANE_STATUS_ADDRESS   0x10140482UL
#define SMN_DEV0_FUNC0_NBIF0_N10NBIO1_MARGINING_LANE_STATUS_ADDRESS   0x10340482UL
#define SMN_DEV0_FUNC0_NBIF0_N11NBIO0_MARGINING_LANE_STATUS_ADDRESS   0x10140486UL
#define SMN_DEV0_FUNC0_NBIF0_N11NBIO1_MARGINING_LANE_STATUS_ADDRESS   0x10340486UL
#define SMN_DEV0_FUNC0_NBIF0_N12NBIO0_MARGINING_LANE_STATUS_ADDRESS   0x1014048aUL
#define SMN_DEV0_FUNC0_NBIF0_N12NBIO1_MARGINING_LANE_STATUS_ADDRESS   0x1034048aUL
#define SMN_DEV0_FUNC0_NBIF0_N13NBIO0_MARGINING_LANE_STATUS_ADDRESS   0x1014048eUL
#define SMN_DEV0_FUNC0_NBIF0_N13NBIO1_MARGINING_LANE_STATUS_ADDRESS   0x1034048eUL
#define SMN_DEV0_FUNC0_NBIF0_N14NBIO0_MARGINING_LANE_STATUS_ADDRESS   0x10140492UL
#define SMN_DEV0_FUNC0_NBIF0_N14NBIO1_MARGINING_LANE_STATUS_ADDRESS   0x10340492UL
#define SMN_DEV0_FUNC0_NBIF0_N15NBIO0_MARGINING_LANE_STATUS_ADDRESS   0x10140496UL
#define SMN_DEV0_FUNC0_NBIF0_N15NBIO1_MARGINING_LANE_STATUS_ADDRESS   0x10340496UL
#define SMN_DEV0_FUNC0_NBIF0_N1NBIO0_MARGINING_LANE_STATUS_ADDRESS    0x1014045eUL
#define SMN_DEV0_FUNC0_NBIF0_N1NBIO1_MARGINING_LANE_STATUS_ADDRESS    0x1034045eUL
#define SMN_DEV0_FUNC0_NBIF0_N2NBIO0_MARGINING_LANE_STATUS_ADDRESS    0x10140462UL
#define SMN_DEV0_FUNC0_NBIF0_N2NBIO1_MARGINING_LANE_STATUS_ADDRESS    0x10340462UL
#define SMN_DEV0_FUNC0_NBIF0_N3NBIO0_MARGINING_LANE_STATUS_ADDRESS    0x10140466UL
#define SMN_DEV0_FUNC0_NBIF0_N3NBIO1_MARGINING_LANE_STATUS_ADDRESS    0x10340466UL
#define SMN_DEV0_FUNC0_NBIF0_N4NBIO0_MARGINING_LANE_STATUS_ADDRESS    0x1014046aUL
#define SMN_DEV0_FUNC0_NBIF0_N4NBIO1_MARGINING_LANE_STATUS_ADDRESS    0x1034046aUL
#define SMN_DEV0_FUNC0_NBIF0_N5NBIO0_MARGINING_LANE_STATUS_ADDRESS    0x1014046eUL
#define SMN_DEV0_FUNC0_NBIF0_N5NBIO1_MARGINING_LANE_STATUS_ADDRESS    0x1034046eUL
#define SMN_DEV0_FUNC0_NBIF0_N6NBIO0_MARGINING_LANE_STATUS_ADDRESS    0x10140472UL
#define SMN_DEV0_FUNC0_NBIF0_N6NBIO1_MARGINING_LANE_STATUS_ADDRESS    0x10340472UL
#define SMN_DEV0_FUNC0_NBIF0_N7NBIO0_MARGINING_LANE_STATUS_ADDRESS    0x10140476UL
#define SMN_DEV0_FUNC0_NBIF0_N7NBIO1_MARGINING_LANE_STATUS_ADDRESS    0x10340476UL
#define SMN_DEV0_FUNC0_NBIF0_N8NBIO0_MARGINING_LANE_STATUS_ADDRESS    0x1014047aUL
#define SMN_DEV0_FUNC0_NBIF0_N8NBIO1_MARGINING_LANE_STATUS_ADDRESS    0x1034047aUL
#define SMN_DEV0_FUNC0_NBIF0_N9NBIO0_MARGINING_LANE_STATUS_ADDRESS    0x1014047eUL
#define SMN_DEV0_FUNC0_NBIF0_N9NBIO1_MARGINING_LANE_STATUS_ADDRESS    0x1034047eUL
#define SMN_DEV0_FUNC0_NBIF1_N0NBIO0_MARGINING_LANE_STATUS_ADDRESS    0x1024045aUL
#define SMN_DEV0_FUNC0_NBIF1_N0NBIO1_MARGINING_LANE_STATUS_ADDRESS    0x1044045aUL
#define SMN_DEV0_FUNC0_NBIF1_N10NBIO0_MARGINING_LANE_STATUS_ADDRESS   0x10240482UL
#define SMN_DEV0_FUNC0_NBIF1_N10NBIO1_MARGINING_LANE_STATUS_ADDRESS   0x10440482UL
#define SMN_DEV0_FUNC0_NBIF1_N11NBIO0_MARGINING_LANE_STATUS_ADDRESS   0x10240486UL
#define SMN_DEV0_FUNC0_NBIF1_N11NBIO1_MARGINING_LANE_STATUS_ADDRESS   0x10440486UL
#define SMN_DEV0_FUNC0_NBIF1_N12NBIO0_MARGINING_LANE_STATUS_ADDRESS   0x1024048aUL
#define SMN_DEV0_FUNC0_NBIF1_N12NBIO1_MARGINING_LANE_STATUS_ADDRESS   0x1044048aUL
#define SMN_DEV0_FUNC0_NBIF1_N13NBIO0_MARGINING_LANE_STATUS_ADDRESS   0x1024048eUL
#define SMN_DEV0_FUNC0_NBIF1_N13NBIO1_MARGINING_LANE_STATUS_ADDRESS   0x1044048eUL
#define SMN_DEV0_FUNC0_NBIF1_N14NBIO0_MARGINING_LANE_STATUS_ADDRESS   0x10240492UL
#define SMN_DEV0_FUNC0_NBIF1_N14NBIO1_MARGINING_LANE_STATUS_ADDRESS   0x10440492UL
#define SMN_DEV0_FUNC0_NBIF1_N15NBIO0_MARGINING_LANE_STATUS_ADDRESS   0x10240496UL
#define SMN_DEV0_FUNC0_NBIF1_N15NBIO1_MARGINING_LANE_STATUS_ADDRESS   0x10440496UL
#define SMN_DEV0_FUNC0_NBIF1_N1NBIO0_MARGINING_LANE_STATUS_ADDRESS    0x1024045eUL
#define SMN_DEV0_FUNC0_NBIF1_N1NBIO1_MARGINING_LANE_STATUS_ADDRESS    0x1044045eUL
#define SMN_DEV0_FUNC0_NBIF1_N2NBIO0_MARGINING_LANE_STATUS_ADDRESS    0x10240462UL
#define SMN_DEV0_FUNC0_NBIF1_N2NBIO1_MARGINING_LANE_STATUS_ADDRESS    0x10440462UL
#define SMN_DEV0_FUNC0_NBIF1_N3NBIO0_MARGINING_LANE_STATUS_ADDRESS    0x10240466UL
#define SMN_DEV0_FUNC0_NBIF1_N3NBIO1_MARGINING_LANE_STATUS_ADDRESS    0x10440466UL
#define SMN_DEV0_FUNC0_NBIF1_N4NBIO0_MARGINING_LANE_STATUS_ADDRESS    0x1024046aUL
#define SMN_DEV0_FUNC0_NBIF1_N4NBIO1_MARGINING_LANE_STATUS_ADDRESS    0x1044046aUL
#define SMN_DEV0_FUNC0_NBIF1_N5NBIO0_MARGINING_LANE_STATUS_ADDRESS    0x1024046eUL
#define SMN_DEV0_FUNC0_NBIF1_N5NBIO1_MARGINING_LANE_STATUS_ADDRESS    0x1044046eUL
#define SMN_DEV0_FUNC0_NBIF1_N6NBIO0_MARGINING_LANE_STATUS_ADDRESS    0x10240472UL
#define SMN_DEV0_FUNC0_NBIF1_N6NBIO1_MARGINING_LANE_STATUS_ADDRESS    0x10440472UL
#define SMN_DEV0_FUNC0_NBIF1_N7NBIO0_MARGINING_LANE_STATUS_ADDRESS    0x10240476UL
#define SMN_DEV0_FUNC0_NBIF1_N7NBIO1_MARGINING_LANE_STATUS_ADDRESS    0x10440476UL
#define SMN_DEV0_FUNC0_NBIF1_N8NBIO0_MARGINING_LANE_STATUS_ADDRESS    0x1024047aUL
#define SMN_DEV0_FUNC0_NBIF1_N8NBIO1_MARGINING_LANE_STATUS_ADDRESS    0x1044047aUL
#define SMN_DEV0_FUNC0_NBIF1_N9NBIO0_MARGINING_LANE_STATUS_ADDRESS    0x1024047eUL
#define SMN_DEV0_FUNC0_NBIF1_N9NBIO1_MARGINING_LANE_STATUS_ADDRESS    0x1044047eUL
#define SMN_DEV0_FUNC0_NBIF2_N0NBIO0_MARGINING_LANE_STATUS_ADDRESS    0x1054045aUL
#define SMN_DEV0_FUNC0_NBIF2_N0NBIO1_MARGINING_LANE_STATUS_ADDRESS    0x1074045aUL
#define SMN_DEV0_FUNC0_NBIF2_N10NBIO0_MARGINING_LANE_STATUS_ADDRESS   0x10540482UL
#define SMN_DEV0_FUNC0_NBIF2_N10NBIO1_MARGINING_LANE_STATUS_ADDRESS   0x10740482UL
#define SMN_DEV0_FUNC0_NBIF2_N11NBIO0_MARGINING_LANE_STATUS_ADDRESS   0x10540486UL
#define SMN_DEV0_FUNC0_NBIF2_N11NBIO1_MARGINING_LANE_STATUS_ADDRESS   0x10740486UL
#define SMN_DEV0_FUNC0_NBIF2_N12NBIO0_MARGINING_LANE_STATUS_ADDRESS   0x1054048aUL
#define SMN_DEV0_FUNC0_NBIF2_N12NBIO1_MARGINING_LANE_STATUS_ADDRESS   0x1074048aUL
#define SMN_DEV0_FUNC0_NBIF2_N13NBIO0_MARGINING_LANE_STATUS_ADDRESS   0x1054048eUL
#define SMN_DEV0_FUNC0_NBIF2_N13NBIO1_MARGINING_LANE_STATUS_ADDRESS   0x1074048eUL
#define SMN_DEV0_FUNC0_NBIF2_N14NBIO0_MARGINING_LANE_STATUS_ADDRESS   0x10540492UL
#define SMN_DEV0_FUNC0_NBIF2_N14NBIO1_MARGINING_LANE_STATUS_ADDRESS   0x10740492UL
#define SMN_DEV0_FUNC0_NBIF2_N15NBIO0_MARGINING_LANE_STATUS_ADDRESS   0x10540496UL
#define SMN_DEV0_FUNC0_NBIF2_N15NBIO1_MARGINING_LANE_STATUS_ADDRESS   0x10740496UL
#define SMN_DEV0_FUNC0_NBIF2_N1NBIO0_MARGINING_LANE_STATUS_ADDRESS    0x1054045eUL
#define SMN_DEV0_FUNC0_NBIF2_N1NBIO1_MARGINING_LANE_STATUS_ADDRESS    0x1074045eUL
#define SMN_DEV0_FUNC0_NBIF2_N2NBIO0_MARGINING_LANE_STATUS_ADDRESS    0x10540462UL
#define SMN_DEV0_FUNC0_NBIF2_N2NBIO1_MARGINING_LANE_STATUS_ADDRESS    0x10740462UL
#define SMN_DEV0_FUNC0_NBIF2_N3NBIO0_MARGINING_LANE_STATUS_ADDRESS    0x10540466UL
#define SMN_DEV0_FUNC0_NBIF2_N3NBIO1_MARGINING_LANE_STATUS_ADDRESS    0x10740466UL
#define SMN_DEV0_FUNC0_NBIF2_N4NBIO0_MARGINING_LANE_STATUS_ADDRESS    0x1054046aUL
#define SMN_DEV0_FUNC0_NBIF2_N4NBIO1_MARGINING_LANE_STATUS_ADDRESS    0x1074046aUL
#define SMN_DEV0_FUNC0_NBIF2_N5NBIO0_MARGINING_LANE_STATUS_ADDRESS    0x1054046eUL
#define SMN_DEV0_FUNC0_NBIF2_N5NBIO1_MARGINING_LANE_STATUS_ADDRESS    0x1074046eUL
#define SMN_DEV0_FUNC0_NBIF2_N6NBIO0_MARGINING_LANE_STATUS_ADDRESS    0x10540472UL
#define SMN_DEV0_FUNC0_NBIF2_N6NBIO1_MARGINING_LANE_STATUS_ADDRESS    0x10740472UL
#define SMN_DEV0_FUNC0_NBIF2_N7NBIO0_MARGINING_LANE_STATUS_ADDRESS    0x10540476UL
#define SMN_DEV0_FUNC0_NBIF2_N7NBIO1_MARGINING_LANE_STATUS_ADDRESS    0x10740476UL
#define SMN_DEV0_FUNC0_NBIF2_N8NBIO0_MARGINING_LANE_STATUS_ADDRESS    0x1054047aUL
#define SMN_DEV0_FUNC0_NBIF2_N8NBIO1_MARGINING_LANE_STATUS_ADDRESS    0x1074047aUL
#define SMN_DEV0_FUNC0_NBIF2_N9NBIO0_MARGINING_LANE_STATUS_ADDRESS    0x1054047eUL
#define SMN_DEV0_FUNC0_NBIF2_N9NBIO1_MARGINING_LANE_STATUS_ADDRESS    0x1074047eUL
#define SMN_DEV1_FUNC0_NBIF0_N0NBIO0_MARGINING_LANE_STATUS_ADDRESS    0x1014845aUL
#define SMN_DEV1_FUNC0_NBIF0_N0NBIO1_MARGINING_LANE_STATUS_ADDRESS    0x1034845aUL
#define SMN_DEV1_FUNC0_NBIF0_N10NBIO0_MARGINING_LANE_STATUS_ADDRESS   0x10148482UL
#define SMN_DEV1_FUNC0_NBIF0_N10NBIO1_MARGINING_LANE_STATUS_ADDRESS   0x10348482UL
#define SMN_DEV1_FUNC0_NBIF0_N11NBIO0_MARGINING_LANE_STATUS_ADDRESS   0x10148486UL
#define SMN_DEV1_FUNC0_NBIF0_N11NBIO1_MARGINING_LANE_STATUS_ADDRESS   0x10348486UL
#define SMN_DEV1_FUNC0_NBIF0_N12NBIO0_MARGINING_LANE_STATUS_ADDRESS   0x1014848aUL
#define SMN_DEV1_FUNC0_NBIF0_N12NBIO1_MARGINING_LANE_STATUS_ADDRESS   0x1034848aUL
#define SMN_DEV1_FUNC0_NBIF0_N13NBIO0_MARGINING_LANE_STATUS_ADDRESS   0x1014848eUL
#define SMN_DEV1_FUNC0_NBIF0_N13NBIO1_MARGINING_LANE_STATUS_ADDRESS   0x1034848eUL
#define SMN_DEV1_FUNC0_NBIF0_N14NBIO0_MARGINING_LANE_STATUS_ADDRESS   0x10148492UL
#define SMN_DEV1_FUNC0_NBIF0_N14NBIO1_MARGINING_LANE_STATUS_ADDRESS   0x10348492UL
#define SMN_DEV1_FUNC0_NBIF0_N15NBIO0_MARGINING_LANE_STATUS_ADDRESS   0x10148496UL
#define SMN_DEV1_FUNC0_NBIF0_N15NBIO1_MARGINING_LANE_STATUS_ADDRESS   0x10348496UL
#define SMN_DEV1_FUNC0_NBIF0_N1NBIO0_MARGINING_LANE_STATUS_ADDRESS    0x1014845eUL
#define SMN_DEV1_FUNC0_NBIF0_N1NBIO1_MARGINING_LANE_STATUS_ADDRESS    0x1034845eUL
#define SMN_DEV1_FUNC0_NBIF0_N2NBIO0_MARGINING_LANE_STATUS_ADDRESS    0x10148462UL
#define SMN_DEV1_FUNC0_NBIF0_N2NBIO1_MARGINING_LANE_STATUS_ADDRESS    0x10348462UL
#define SMN_DEV1_FUNC0_NBIF0_N3NBIO0_MARGINING_LANE_STATUS_ADDRESS    0x10148466UL
#define SMN_DEV1_FUNC0_NBIF0_N3NBIO1_MARGINING_LANE_STATUS_ADDRESS    0x10348466UL
#define SMN_DEV1_FUNC0_NBIF0_N4NBIO0_MARGINING_LANE_STATUS_ADDRESS    0x1014846aUL
#define SMN_DEV1_FUNC0_NBIF0_N4NBIO1_MARGINING_LANE_STATUS_ADDRESS    0x1034846aUL
#define SMN_DEV1_FUNC0_NBIF0_N5NBIO0_MARGINING_LANE_STATUS_ADDRESS    0x1014846eUL
#define SMN_DEV1_FUNC0_NBIF0_N5NBIO1_MARGINING_LANE_STATUS_ADDRESS    0x1034846eUL
#define SMN_DEV1_FUNC0_NBIF0_N6NBIO0_MARGINING_LANE_STATUS_ADDRESS    0x10148472UL
#define SMN_DEV1_FUNC0_NBIF0_N6NBIO1_MARGINING_LANE_STATUS_ADDRESS    0x10348472UL
#define SMN_DEV1_FUNC0_NBIF0_N7NBIO0_MARGINING_LANE_STATUS_ADDRESS    0x10148476UL
#define SMN_DEV1_FUNC0_NBIF0_N7NBIO1_MARGINING_LANE_STATUS_ADDRESS    0x10348476UL
#define SMN_DEV1_FUNC0_NBIF0_N8NBIO0_MARGINING_LANE_STATUS_ADDRESS    0x1014847aUL
#define SMN_DEV1_FUNC0_NBIF0_N8NBIO1_MARGINING_LANE_STATUS_ADDRESS    0x1034847aUL
#define SMN_DEV1_FUNC0_NBIF0_N9NBIO0_MARGINING_LANE_STATUS_ADDRESS    0x1014847eUL
#define SMN_DEV1_FUNC0_NBIF0_N9NBIO1_MARGINING_LANE_STATUS_ADDRESS    0x1034847eUL
#define SMN_DEV1_FUNC0_NBIF1_N0NBIO0_MARGINING_LANE_STATUS_ADDRESS    0x1024845aUL
#define SMN_DEV1_FUNC0_NBIF1_N0NBIO1_MARGINING_LANE_STATUS_ADDRESS    0x1044845aUL
#define SMN_DEV1_FUNC0_NBIF1_N10NBIO0_MARGINING_LANE_STATUS_ADDRESS   0x10248482UL
#define SMN_DEV1_FUNC0_NBIF1_N10NBIO1_MARGINING_LANE_STATUS_ADDRESS   0x10448482UL
#define SMN_DEV1_FUNC0_NBIF1_N11NBIO0_MARGINING_LANE_STATUS_ADDRESS   0x10248486UL
#define SMN_DEV1_FUNC0_NBIF1_N11NBIO1_MARGINING_LANE_STATUS_ADDRESS   0x10448486UL
#define SMN_DEV1_FUNC0_NBIF1_N12NBIO0_MARGINING_LANE_STATUS_ADDRESS   0x1024848aUL
#define SMN_DEV1_FUNC0_NBIF1_N12NBIO1_MARGINING_LANE_STATUS_ADDRESS   0x1044848aUL
#define SMN_DEV1_FUNC0_NBIF1_N13NBIO0_MARGINING_LANE_STATUS_ADDRESS   0x1024848eUL
#define SMN_DEV1_FUNC0_NBIF1_N13NBIO1_MARGINING_LANE_STATUS_ADDRESS   0x1044848eUL
#define SMN_DEV1_FUNC0_NBIF1_N14NBIO0_MARGINING_LANE_STATUS_ADDRESS   0x10248492UL
#define SMN_DEV1_FUNC0_NBIF1_N14NBIO1_MARGINING_LANE_STATUS_ADDRESS   0x10448492UL
#define SMN_DEV1_FUNC0_NBIF1_N15NBIO0_MARGINING_LANE_STATUS_ADDRESS   0x10248496UL
#define SMN_DEV1_FUNC0_NBIF1_N15NBIO1_MARGINING_LANE_STATUS_ADDRESS   0x10448496UL
#define SMN_DEV1_FUNC0_NBIF1_N1NBIO0_MARGINING_LANE_STATUS_ADDRESS    0x1024845eUL
#define SMN_DEV1_FUNC0_NBIF1_N1NBIO1_MARGINING_LANE_STATUS_ADDRESS    0x1044845eUL
#define SMN_DEV1_FUNC0_NBIF1_N2NBIO0_MARGINING_LANE_STATUS_ADDRESS    0x10248462UL
#define SMN_DEV1_FUNC0_NBIF1_N2NBIO1_MARGINING_LANE_STATUS_ADDRESS    0x10448462UL
#define SMN_DEV1_FUNC0_NBIF1_N3NBIO0_MARGINING_LANE_STATUS_ADDRESS    0x10248466UL
#define SMN_DEV1_FUNC0_NBIF1_N3NBIO1_MARGINING_LANE_STATUS_ADDRESS    0x10448466UL
#define SMN_DEV1_FUNC0_NBIF1_N4NBIO0_MARGINING_LANE_STATUS_ADDRESS    0x1024846aUL
#define SMN_DEV1_FUNC0_NBIF1_N4NBIO1_MARGINING_LANE_STATUS_ADDRESS    0x1044846aUL
#define SMN_DEV1_FUNC0_NBIF1_N5NBIO0_MARGINING_LANE_STATUS_ADDRESS    0x1024846eUL
#define SMN_DEV1_FUNC0_NBIF1_N5NBIO1_MARGINING_LANE_STATUS_ADDRESS    0x1044846eUL
#define SMN_DEV1_FUNC0_NBIF1_N6NBIO0_MARGINING_LANE_STATUS_ADDRESS    0x10248472UL
#define SMN_DEV1_FUNC0_NBIF1_N6NBIO1_MARGINING_LANE_STATUS_ADDRESS    0x10448472UL
#define SMN_DEV1_FUNC0_NBIF1_N7NBIO0_MARGINING_LANE_STATUS_ADDRESS    0x10248476UL
#define SMN_DEV1_FUNC0_NBIF1_N7NBIO1_MARGINING_LANE_STATUS_ADDRESS    0x10448476UL
#define SMN_DEV1_FUNC0_NBIF1_N8NBIO0_MARGINING_LANE_STATUS_ADDRESS    0x1024847aUL
#define SMN_DEV1_FUNC0_NBIF1_N8NBIO1_MARGINING_LANE_STATUS_ADDRESS    0x1044847aUL
#define SMN_DEV1_FUNC0_NBIF1_N9NBIO0_MARGINING_LANE_STATUS_ADDRESS    0x1024847eUL
#define SMN_DEV1_FUNC0_NBIF1_N9NBIO1_MARGINING_LANE_STATUS_ADDRESS    0x1044847eUL


/***********************************************************
* Register Name : MARGINING_PORT_CAP
************************************************************/

#define MARGINING_PORT_CAP_MARGINING_USES_SOFTWARE_OFFSET      0
#define MARGINING_PORT_CAP_MARGINING_USES_SOFTWARE_MASK        0x1

#define MARGINING_PORT_CAP_Reserved_15_1_OFFSET                1
#define MARGINING_PORT_CAP_Reserved_15_1_MASK                  0xfffe

typedef union {
  struct {
    UINT16                             MARGINING_USES_SOFTWARE:1;
    UINT16                                       Reserved_15_1:15;
  } Field;
  UINT16 Value;
} MARGINING_PORT_CAP_STRUCT;

#define PCICFG_NBIFEPF0CFG_MARGINING_PORT_CAP_OFFSET                0x454
#define SMN_DEV0_FUNC0_NBIF0NBIO0_MARGINING_PORT_CAP_ADDRESS          0x10140454UL
#define SMN_DEV0_FUNC0_NBIF0NBIO1_MARGINING_PORT_CAP_ADDRESS          0x10340454UL
#define SMN_DEV0_FUNC0_NBIF1NBIO0_MARGINING_PORT_CAP_ADDRESS          0x10240454UL
#define SMN_DEV0_FUNC0_NBIF1NBIO1_MARGINING_PORT_CAP_ADDRESS          0x10440454UL
#define SMN_DEV0_FUNC0_NBIF2NBIO0_MARGINING_PORT_CAP_ADDRESS          0x10540454UL
#define SMN_DEV0_FUNC0_NBIF2NBIO1_MARGINING_PORT_CAP_ADDRESS          0x10740454UL
#define SMN_DEV1_FUNC0_NBIF0NBIO0_MARGINING_PORT_CAP_ADDRESS          0x10148454UL
#define SMN_DEV1_FUNC0_NBIF0NBIO1_MARGINING_PORT_CAP_ADDRESS          0x10348454UL
#define SMN_DEV1_FUNC0_NBIF1NBIO0_MARGINING_PORT_CAP_ADDRESS          0x10248454UL
#define SMN_DEV1_FUNC0_NBIF1NBIO1_MARGINING_PORT_CAP_ADDRESS          0x10448454UL


/***********************************************************
* Register Name : MARGINING_PORT_STATUS
************************************************************/

#define MARGINING_PORT_STATUS_MARGINING_READY_OFFSET           0
#define MARGINING_PORT_STATUS_MARGINING_READY_MASK             0x1

#define MARGINING_PORT_STATUS_MARGINING_SOFTWARE_READY_OFFSET  1
#define MARGINING_PORT_STATUS_MARGINING_SOFTWARE_READY_MASK    0x2

#define MARGINING_PORT_STATUS_Reserved_15_2_OFFSET             2
#define MARGINING_PORT_STATUS_Reserved_15_2_MASK               0xfffc

typedef union {
  struct {
    UINT16                                     MARGINING_READY:1;
    UINT16                            MARGINING_SOFTWARE_READY:1;
    UINT16                                       Reserved_15_2:14;
  } Field;
  UINT16 Value;
} MARGINING_PORT_STATUS_STRUCT;

#define PCICFG_NBIFEPF0CFG_MARGINING_PORT_STATUS_OFFSET             0x456
#define SMN_DEV0_FUNC0_NBIF0NBIO0_MARGINING_PORT_STATUS_ADDRESS       0x10140456UL
#define SMN_DEV0_FUNC0_NBIF0NBIO1_MARGINING_PORT_STATUS_ADDRESS       0x10340456UL
#define SMN_DEV0_FUNC0_NBIF1NBIO0_MARGINING_PORT_STATUS_ADDRESS       0x10240456UL
#define SMN_DEV0_FUNC0_NBIF1NBIO1_MARGINING_PORT_STATUS_ADDRESS       0x10440456UL
#define SMN_DEV0_FUNC0_NBIF2NBIO0_MARGINING_PORT_STATUS_ADDRESS       0x10540456UL
#define SMN_DEV0_FUNC0_NBIF2NBIO1_MARGINING_PORT_STATUS_ADDRESS       0x10740456UL
#define SMN_DEV1_FUNC0_NBIF0NBIO0_MARGINING_PORT_STATUS_ADDRESS       0x10148456UL
#define SMN_DEV1_FUNC0_NBIF0NBIO1_MARGINING_PORT_STATUS_ADDRESS       0x10348456UL
#define SMN_DEV1_FUNC0_NBIF1NBIO0_MARGINING_PORT_STATUS_ADDRESS       0x10248456UL
#define SMN_DEV1_FUNC0_NBIF1NBIO1_MARGINING_PORT_STATUS_ADDRESS       0x10448456UL


/***********************************************************
* Register Name : MAX_LATENCY
************************************************************/

#define MAX_LATENCY_MAX_LAT_OFFSET                             0
#define MAX_LATENCY_MAX_LAT_MASK                               0xff

typedef union {
  struct {
    UINT8                                             MAX_LAT:8;
  } Field;
  UINT8 Value;
} MAX_LATENCY_STRUCT;

#define PCICFG_NBIFEPF0CFG_MAX_LATENCY_OFFSET                       0x3f
#define SMN_DEV0_FUNC0_NBIF0NBIO0_MAX_LATENCY_ADDRESS                 0x1014003fUL
#define SMN_DEV0_FUNC0_NBIF0NBIO1_MAX_LATENCY_ADDRESS                 0x1034003fUL
#define SMN_DEV0_FUNC0_NBIF1NBIO0_MAX_LATENCY_ADDRESS                 0x1024003fUL
#define SMN_DEV0_FUNC0_NBIF1NBIO1_MAX_LATENCY_ADDRESS                 0x1044003fUL
#define SMN_DEV0_FUNC0_NBIF2NBIO0_MAX_LATENCY_ADDRESS                 0x1054003fUL
#define SMN_DEV0_FUNC0_NBIF2NBIO1_MAX_LATENCY_ADDRESS                 0x1074003fUL
#define SMN_DEV1_FUNC0_NBIF0NBIO0_MAX_LATENCY_ADDRESS                 0x1014803fUL
#define SMN_DEV1_FUNC0_NBIF0NBIO1_MAX_LATENCY_ADDRESS                 0x1034803fUL
#define SMN_DEV1_FUNC0_NBIF1NBIO0_MAX_LATENCY_ADDRESS                 0x1024803fUL
#define SMN_DEV1_FUNC0_NBIF1NBIO1_MAX_LATENCY_ADDRESS                 0x1044803fUL


/***********************************************************
* Register Name : MIN_GRANT
************************************************************/

#define MIN_GRANT_MIN_GNT_OFFSET                               0
#define MIN_GRANT_MIN_GNT_MASK                                 0xff

typedef union {
  struct {
    UINT8                                             MIN_GNT:8;
  } Field;
  UINT8 Value;
} MIN_GRANT_STRUCT;

#define PCICFG_NBIFEPF0CFG_MIN_GRANT_OFFSET                         0x3e
#define SMN_DEV0_FUNC0_NBIF0NBIO0_MIN_GRANT_ADDRESS                   0x1014003eUL
#define SMN_DEV0_FUNC0_NBIF0NBIO1_MIN_GRANT_ADDRESS                   0x1034003eUL
#define SMN_DEV0_FUNC0_NBIF1NBIO0_MIN_GRANT_ADDRESS                   0x1024003eUL
#define SMN_DEV0_FUNC0_NBIF1NBIO1_MIN_GRANT_ADDRESS                   0x1044003eUL
#define SMN_DEV0_FUNC0_NBIF2NBIO0_MIN_GRANT_ADDRESS                   0x1054003eUL
#define SMN_DEV0_FUNC0_NBIF2NBIO1_MIN_GRANT_ADDRESS                   0x1074003eUL
#define SMN_DEV1_FUNC0_NBIF0NBIO0_MIN_GRANT_ADDRESS                   0x1014803eUL
#define SMN_DEV1_FUNC0_NBIF0NBIO1_MIN_GRANT_ADDRESS                   0x1034803eUL
#define SMN_DEV1_FUNC0_NBIF1NBIO0_MIN_GRANT_ADDRESS                   0x1024803eUL
#define SMN_DEV1_FUNC0_NBIF1NBIO1_MIN_GRANT_ADDRESS                   0x1044803eUL


/***********************************************************
* Register Name : MSIX_CAP_LIST
************************************************************/

#define MSIX_CAP_LIST_CAP_ID_OFFSET                            0
#define MSIX_CAP_LIST_CAP_ID_MASK                              0xff

#define MSIX_CAP_LIST_NEXT_PTR_OFFSET                          8
#define MSIX_CAP_LIST_NEXT_PTR_MASK                            0xff00

typedef union {
  struct {
    UINT16                                              CAP_ID:8;
    UINT16                                            NEXT_PTR:8;
  } Field;
  UINT16 Value;
} MSIX_CAP_LIST_STRUCT;

#define PCICFG_NBIFEPF0CFG_MSIX_CAP_LIST_OFFSET                     0xc0
#define SMN_DEV0_FUNC0_NBIF0NBIO0_MSIX_CAP_LIST_ADDRESS               0x101400c0UL
#define SMN_DEV0_FUNC0_NBIF0NBIO1_MSIX_CAP_LIST_ADDRESS               0x103400c0UL
#define SMN_DEV0_FUNC0_NBIF1NBIO0_MSIX_CAP_LIST_ADDRESS               0x102400c0UL
#define SMN_DEV0_FUNC0_NBIF1NBIO1_MSIX_CAP_LIST_ADDRESS               0x104400c0UL
#define SMN_DEV0_FUNC0_NBIF2NBIO0_MSIX_CAP_LIST_ADDRESS               0x105400c0UL
#define SMN_DEV0_FUNC0_NBIF2NBIO1_MSIX_CAP_LIST_ADDRESS               0x107400c0UL
#define SMN_DEV1_FUNC0_NBIF0NBIO0_MSIX_CAP_LIST_ADDRESS               0x101480c0UL
#define SMN_DEV1_FUNC0_NBIF0NBIO1_MSIX_CAP_LIST_ADDRESS               0x103480c0UL
#define SMN_DEV1_FUNC0_NBIF1NBIO0_MSIX_CAP_LIST_ADDRESS               0x102480c0UL
#define SMN_DEV1_FUNC0_NBIF1NBIO1_MSIX_CAP_LIST_ADDRESS               0x104480c0UL


/***********************************************************
* Register Name : MSIX_MSG_CNTL
************************************************************/

#define MSIX_MSG_CNTL_MSIX_TABLE_SIZE_OFFSET                   0
#define MSIX_MSG_CNTL_MSIX_TABLE_SIZE_MASK                     0x7ff

#define MSIX_MSG_CNTL_Reserved_13_11_OFFSET                    11
#define MSIX_MSG_CNTL_Reserved_13_11_MASK                      0x3800

#define MSIX_MSG_CNTL_MSIX_FUNC_MASK_OFFSET                    14
#define MSIX_MSG_CNTL_MSIX_FUNC_MASK_MASK                      0x4000

#define MSIX_MSG_CNTL_MSIX_EN_OFFSET                           15
#define MSIX_MSG_CNTL_MSIX_EN_MASK                             0x8000

typedef union {
  struct {
    UINT16                                     MSIX_TABLE_SIZE:11;
    UINT16                                      Reserved_13_11:3;
    UINT16                                      MSIX_FUNC_MASK:1;
    UINT16                                             MSIX_EN:1;
  } Field;
  UINT16 Value;
} MSIX_MSG_CNTL_STRUCT;

#define PCICFG_NBIFEPF0CFG_MSIX_MSG_CNTL_OFFSET                     0xc2
#define SMN_DEV0_FUNC0_NBIF0NBIO0_MSIX_MSG_CNTL_ADDRESS               0x101400c2UL
#define SMN_DEV0_FUNC0_NBIF0NBIO1_MSIX_MSG_CNTL_ADDRESS               0x103400c2UL
#define SMN_DEV0_FUNC0_NBIF1NBIO0_MSIX_MSG_CNTL_ADDRESS               0x102400c2UL
#define SMN_DEV0_FUNC0_NBIF1NBIO1_MSIX_MSG_CNTL_ADDRESS               0x104400c2UL
#define SMN_DEV0_FUNC0_NBIF2NBIO0_MSIX_MSG_CNTL_ADDRESS               0x105400c2UL
#define SMN_DEV0_FUNC0_NBIF2NBIO1_MSIX_MSG_CNTL_ADDRESS               0x107400c2UL
#define SMN_DEV1_FUNC0_NBIF0NBIO0_MSIX_MSG_CNTL_ADDRESS               0x101480c2UL
#define SMN_DEV1_FUNC0_NBIF0NBIO1_MSIX_MSG_CNTL_ADDRESS               0x103480c2UL
#define SMN_DEV1_FUNC0_NBIF1NBIO0_MSIX_MSG_CNTL_ADDRESS               0x102480c2UL
#define SMN_DEV1_FUNC0_NBIF1NBIO1_MSIX_MSG_CNTL_ADDRESS               0x104480c2UL


/***********************************************************
* Register Name : MSIX_PBA
************************************************************/

#define MSIX_PBA_MSIX_PBA_BIR_OFFSET                           0
#define MSIX_PBA_MSIX_PBA_BIR_MASK                             0x7

#define MSIX_PBA_MSIX_PBA_OFFSET_OFFSET                        3
#define MSIX_PBA_MSIX_PBA_OFFSET_MASK                          0xfffffff8

typedef union {
  struct {
    UINT32                                        MSIX_PBA_BIR:3;
    UINT32                                     MSIX_PBA_OFFSET:29;
  } Field;
  UINT32 Value;
} MSIX_PBA_STRUCT;

#define PCICFG_NBIFEPF0CFG_MSIX_PBA_OFFSET                          0xc8
#define SMN_DEV0_FUNC0_NBIF0NBIO0_MSIX_PBA_ADDRESS                    0x101400c8UL
#define SMN_DEV0_FUNC0_NBIF0NBIO1_MSIX_PBA_ADDRESS                    0x103400c8UL
#define SMN_DEV0_FUNC0_NBIF1NBIO0_MSIX_PBA_ADDRESS                    0x102400c8UL
#define SMN_DEV0_FUNC0_NBIF1NBIO1_MSIX_PBA_ADDRESS                    0x104400c8UL
#define SMN_DEV0_FUNC0_NBIF2NBIO0_MSIX_PBA_ADDRESS                    0x105400c8UL
#define SMN_DEV0_FUNC0_NBIF2NBIO1_MSIX_PBA_ADDRESS                    0x107400c8UL
#define SMN_DEV1_FUNC0_NBIF0NBIO0_MSIX_PBA_ADDRESS                    0x101480c8UL
#define SMN_DEV1_FUNC0_NBIF0NBIO1_MSIX_PBA_ADDRESS                    0x103480c8UL
#define SMN_DEV1_FUNC0_NBIF1NBIO0_MSIX_PBA_ADDRESS                    0x102480c8UL
#define SMN_DEV1_FUNC0_NBIF1NBIO1_MSIX_PBA_ADDRESS                    0x104480c8UL


/***********************************************************
* Register Name : MSIX_TABLE
************************************************************/

#define MSIX_TABLE_MSIX_TABLE_BIR_OFFSET                       0
#define MSIX_TABLE_MSIX_TABLE_BIR_MASK                         0x7

#define MSIX_TABLE_MSIX_TABLE_OFFSET_OFFSET                    3
#define MSIX_TABLE_MSIX_TABLE_OFFSET_MASK                      0xfffffff8

typedef union {
  struct {
    UINT32                                      MSIX_TABLE_BIR:3;
    UINT32                                   MSIX_TABLE_OFFSET:29;
  } Field;
  UINT32 Value;
} MSIX_TABLE_STRUCT;

#define PCICFG_NBIFEPF0CFG_MSIX_TABLE_OFFSET                        0xc4
#define SMN_DEV0_FUNC0_NBIF0NBIO0_MSIX_TABLE_ADDRESS                  0x101400c4UL
#define SMN_DEV0_FUNC0_NBIF0NBIO1_MSIX_TABLE_ADDRESS                  0x103400c4UL
#define SMN_DEV0_FUNC0_NBIF1NBIO0_MSIX_TABLE_ADDRESS                  0x102400c4UL
#define SMN_DEV0_FUNC0_NBIF1NBIO1_MSIX_TABLE_ADDRESS                  0x104400c4UL
#define SMN_DEV0_FUNC0_NBIF2NBIO0_MSIX_TABLE_ADDRESS                  0x105400c4UL
#define SMN_DEV0_FUNC0_NBIF2NBIO1_MSIX_TABLE_ADDRESS                  0x107400c4UL
#define SMN_DEV1_FUNC0_NBIF0NBIO0_MSIX_TABLE_ADDRESS                  0x101480c4UL
#define SMN_DEV1_FUNC0_NBIF0NBIO1_MSIX_TABLE_ADDRESS                  0x103480c4UL
#define SMN_DEV1_FUNC0_NBIF1NBIO0_MSIX_TABLE_ADDRESS                  0x102480c4UL
#define SMN_DEV1_FUNC0_NBIF1NBIO1_MSIX_TABLE_ADDRESS                  0x104480c4UL


/***********************************************************
* Register Name : MSI_CAP_LIST
************************************************************/

#define MSI_CAP_LIST_CAP_ID_OFFSET                             0
#define MSI_CAP_LIST_CAP_ID_MASK                               0xff

#define MSI_CAP_LIST_NEXT_PTR_OFFSET                           8
#define MSI_CAP_LIST_NEXT_PTR_MASK                             0xff00

typedef union {
  struct {
    UINT16                                              CAP_ID:8;
    UINT16                                            NEXT_PTR:8;
  } Field;
  UINT16 Value;
} MSI_CAP_LIST_STRUCT;

#define PCICFG_NBIFEPF0CFG_MSI_CAP_LIST_OFFSET                      0xa0
#define SMN_DEV0_FUNC0_NBIF0NBIO0_MSI_CAP_LIST_ADDRESS                0x101400a0UL
#define SMN_DEV0_FUNC0_NBIF0NBIO1_MSI_CAP_LIST_ADDRESS                0x103400a0UL
#define SMN_DEV0_FUNC0_NBIF1NBIO0_MSI_CAP_LIST_ADDRESS                0x102400a0UL
#define SMN_DEV0_FUNC0_NBIF1NBIO1_MSI_CAP_LIST_ADDRESS                0x104400a0UL
#define SMN_DEV0_FUNC0_NBIF2NBIO0_MSI_CAP_LIST_ADDRESS                0x105400a0UL
#define SMN_DEV0_FUNC0_NBIF2NBIO1_MSI_CAP_LIST_ADDRESS                0x107400a0UL
#define SMN_DEV1_FUNC0_NBIF0NBIO0_MSI_CAP_LIST_ADDRESS                0x101480a0UL
#define SMN_DEV1_FUNC0_NBIF0NBIO1_MSI_CAP_LIST_ADDRESS                0x103480a0UL
#define SMN_DEV1_FUNC0_NBIF1NBIO0_MSI_CAP_LIST_ADDRESS                0x102480a0UL
#define SMN_DEV1_FUNC0_NBIF1NBIO1_MSI_CAP_LIST_ADDRESS                0x104480a0UL


/***********************************************************
* Register Name : MSI_EXT_MSG_DATA
************************************************************/

#define MSI_EXT_MSG_DATA_MSI_EXT_DATA_OFFSET                   0
#define MSI_EXT_MSG_DATA_MSI_EXT_DATA_MASK                     0xffff

typedef union {
  struct {
    UINT16                                        MSI_EXT_DATA:16;
  } Field;
  UINT16 Value;
} MSI_EXT_MSG_DATA_STRUCT;

#define PCICFG_NBIFEPF0CFG_MSI_EXT_MSG_DATA_OFFSET                  0x0
#define SMN_DEV0_FUNC0_NBIF0NBIO0_MSI_EXT_MSG_DATA_ADDRESS            0x10140000UL
#define SMN_DEV0_FUNC0_NBIF0NBIO1_MSI_EXT_MSG_DATA_ADDRESS            0x10340000UL
#define SMN_DEV0_FUNC0_NBIF1NBIO0_MSI_EXT_MSG_DATA_ADDRESS            0x10240000UL
#define SMN_DEV0_FUNC0_NBIF1NBIO1_MSI_EXT_MSG_DATA_ADDRESS            0x10440000UL
#define SMN_DEV0_FUNC0_NBIF2NBIO0_MSI_EXT_MSG_DATA_ADDRESS            0x10540000UL
#define SMN_DEV0_FUNC0_NBIF2NBIO1_MSI_EXT_MSG_DATA_ADDRESS            0x10740000UL
#define SMN_DEV1_FUNC0_NBIF0NBIO0_MSI_EXT_MSG_DATA_ADDRESS            0x10148000UL
#define SMN_DEV1_FUNC0_NBIF0NBIO1_MSI_EXT_MSG_DATA_ADDRESS            0x10348000UL
#define SMN_DEV1_FUNC0_NBIF1NBIO0_MSI_EXT_MSG_DATA_ADDRESS            0x10248000UL
#define SMN_DEV1_FUNC0_NBIF1NBIO1_MSI_EXT_MSG_DATA_ADDRESS            0x10448000UL


/***********************************************************
* Register Name : MSI_EXT_MSG_DATA_64
************************************************************/

#define MSI_EXT_MSG_DATA_64_MSI_EXT_DATA_64_OFFSET             0
#define MSI_EXT_MSG_DATA_64_MSI_EXT_DATA_64_MASK               0xffff

typedef union {
  struct {
    UINT16                                     MSI_EXT_DATA_64:16;
  } Field;
  UINT16 Value;
} MSI_EXT_MSG_DATA_64_STRUCT;

#define PCICFG_NBIFEPF0CFG_MSI_EXT_MSG_DATA_64_OFFSET               0x1
#define SMN_DEV0_FUNC0_NBIF0NBIO0_MSI_EXT_MSG_DATA_64_ADDRESS         0x10140001UL
#define SMN_DEV0_FUNC0_NBIF0NBIO1_MSI_EXT_MSG_DATA_64_ADDRESS         0x10340001UL
#define SMN_DEV0_FUNC0_NBIF1NBIO0_MSI_EXT_MSG_DATA_64_ADDRESS         0x10240001UL
#define SMN_DEV0_FUNC0_NBIF1NBIO1_MSI_EXT_MSG_DATA_64_ADDRESS         0x10440001UL
#define SMN_DEV0_FUNC0_NBIF2NBIO0_MSI_EXT_MSG_DATA_64_ADDRESS         0x10540001UL
#define SMN_DEV0_FUNC0_NBIF2NBIO1_MSI_EXT_MSG_DATA_64_ADDRESS         0x10740001UL
#define SMN_DEV1_FUNC0_NBIF0NBIO0_MSI_EXT_MSG_DATA_64_ADDRESS         0x10148001UL
#define SMN_DEV1_FUNC0_NBIF0NBIO1_MSI_EXT_MSG_DATA_64_ADDRESS         0x10348001UL
#define SMN_DEV1_FUNC0_NBIF1NBIO0_MSI_EXT_MSG_DATA_64_ADDRESS         0x10248001UL
#define SMN_DEV1_FUNC0_NBIF1NBIO1_MSI_EXT_MSG_DATA_64_ADDRESS         0x10448001UL


/***********************************************************
* Register Name : MSI_MASK
************************************************************/

#define MSI_MASK_MSI_MASK_OFFSET                               0
#define MSI_MASK_MSI_MASK_MASK                                 0xffffffff

typedef union {
  struct {
    UINT32                                            MSI_MASK:32;
  } Field;
  UINT32 Value;
} MSI_MASK_STRUCT;

#define PCICFG_NBIFEPF0CFG_MSI_MASK_OFFSET                          0x0
#define SMN_DEV0_FUNC0_NBIF0NBIO0_MSI_MASK_ADDRESS                    0x10140000UL
#define SMN_DEV0_FUNC0_NBIF0NBIO1_MSI_MASK_ADDRESS                    0x10340000UL
#define SMN_DEV0_FUNC0_NBIF1NBIO0_MSI_MASK_ADDRESS                    0x10240000UL
#define SMN_DEV0_FUNC0_NBIF1NBIO1_MSI_MASK_ADDRESS                    0x10440000UL
#define SMN_DEV0_FUNC0_NBIF2NBIO0_MSI_MASK_ADDRESS                    0x10540000UL
#define SMN_DEV0_FUNC0_NBIF2NBIO1_MSI_MASK_ADDRESS                    0x10740000UL
#define SMN_DEV1_FUNC0_NBIF0NBIO0_MSI_MASK_ADDRESS                    0x10148000UL
#define SMN_DEV1_FUNC0_NBIF0NBIO1_MSI_MASK_ADDRESS                    0x10348000UL
#define SMN_DEV1_FUNC0_NBIF1NBIO0_MSI_MASK_ADDRESS                    0x10248000UL
#define SMN_DEV1_FUNC0_NBIF1NBIO1_MSI_MASK_ADDRESS                    0x10448000UL


/***********************************************************
* Register Name : MSI_MASK_64
************************************************************/

#define MSI_MASK_64_MSI_MASK_64_OFFSET                         0
#define MSI_MASK_64_MSI_MASK_64_MASK                           0xffffffff

typedef union {
  struct {
    UINT32                                         MSI_MASK_64:32;
  } Field;
  UINT32 Value;
} MSI_MASK_64_STRUCT;

#define PCICFG_NBIFEPF0CFG_MSI_MASK_64_OFFSET                       0x1
#define SMN_DEV0_FUNC0_NBIF0NBIO0_MSI_MASK_64_ADDRESS                 0x10140001UL
#define SMN_DEV0_FUNC0_NBIF0NBIO1_MSI_MASK_64_ADDRESS                 0x10340001UL
#define SMN_DEV0_FUNC0_NBIF1NBIO0_MSI_MASK_64_ADDRESS                 0x10240001UL
#define SMN_DEV0_FUNC0_NBIF1NBIO1_MSI_MASK_64_ADDRESS                 0x10440001UL
#define SMN_DEV0_FUNC0_NBIF2NBIO0_MSI_MASK_64_ADDRESS                 0x10540001UL
#define SMN_DEV0_FUNC0_NBIF2NBIO1_MSI_MASK_64_ADDRESS                 0x10740001UL
#define SMN_DEV1_FUNC0_NBIF0NBIO0_MSI_MASK_64_ADDRESS                 0x10148001UL
#define SMN_DEV1_FUNC0_NBIF0NBIO1_MSI_MASK_64_ADDRESS                 0x10348001UL
#define SMN_DEV1_FUNC0_NBIF1NBIO0_MSI_MASK_64_ADDRESS                 0x10248001UL
#define SMN_DEV1_FUNC0_NBIF1NBIO1_MSI_MASK_64_ADDRESS                 0x10448001UL


/***********************************************************
* Register Name : MSI_MSG_ADDR_HI
************************************************************/

#define MSI_MSG_ADDR_HI_MSI_MSG_ADDR_HI_OFFSET                 0
#define MSI_MSG_ADDR_HI_MSI_MSG_ADDR_HI_MASK                   0xffffffff

typedef union {
  struct {
    UINT32                                     MSI_MSG_ADDR_HI:32;
  } Field;
  UINT32 Value;
} MSI_MSG_ADDR_HI_STRUCT;

#define PCICFG_NBIFEPF0CFG_MSI_MSG_ADDR_HI_OFFSET                   0x1
#define SMN_DEV0_FUNC0_NBIF0NBIO0_MSI_MSG_ADDR_HI_ADDRESS             0x10140001UL
#define SMN_DEV0_FUNC0_NBIF0NBIO1_MSI_MSG_ADDR_HI_ADDRESS             0x10340001UL
#define SMN_DEV0_FUNC0_NBIF1NBIO0_MSI_MSG_ADDR_HI_ADDRESS             0x10240001UL
#define SMN_DEV0_FUNC0_NBIF1NBIO1_MSI_MSG_ADDR_HI_ADDRESS             0x10440001UL
#define SMN_DEV0_FUNC0_NBIF2NBIO0_MSI_MSG_ADDR_HI_ADDRESS             0x10540001UL
#define SMN_DEV0_FUNC0_NBIF2NBIO1_MSI_MSG_ADDR_HI_ADDRESS             0x10740001UL
#define SMN_DEV1_FUNC0_NBIF0NBIO0_MSI_MSG_ADDR_HI_ADDRESS             0x10148001UL
#define SMN_DEV1_FUNC0_NBIF0NBIO1_MSI_MSG_ADDR_HI_ADDRESS             0x10348001UL
#define SMN_DEV1_FUNC0_NBIF1NBIO0_MSI_MSG_ADDR_HI_ADDRESS             0x10248001UL
#define SMN_DEV1_FUNC0_NBIF1NBIO1_MSI_MSG_ADDR_HI_ADDRESS             0x10448001UL


/***********************************************************
* Register Name : MSI_MSG_ADDR_LO
************************************************************/

#define MSI_MSG_ADDR_LO_Reserved_1_0_OFFSET                    0
#define MSI_MSG_ADDR_LO_Reserved_1_0_MASK                      0x3

#define MSI_MSG_ADDR_LO_MSI_MSG_ADDR_LO_OFFSET                 2
#define MSI_MSG_ADDR_LO_MSI_MSG_ADDR_LO_MASK                   0xfffffffc

typedef union {
  struct {
    UINT32                                        Reserved_1_0:2;
    UINT32                                     MSI_MSG_ADDR_LO:30;
  } Field;
  UINT32 Value;
} MSI_MSG_ADDR_LO_STRUCT;

#define PCICFG_NBIFEPF0CFG_MSI_MSG_ADDR_LO_OFFSET                   0xa4
#define SMN_DEV0_FUNC0_NBIF0NBIO0_MSI_MSG_ADDR_LO_ADDRESS             0x101400a4UL
#define SMN_DEV0_FUNC0_NBIF0NBIO1_MSI_MSG_ADDR_LO_ADDRESS             0x103400a4UL
#define SMN_DEV0_FUNC0_NBIF1NBIO0_MSI_MSG_ADDR_LO_ADDRESS             0x102400a4UL
#define SMN_DEV0_FUNC0_NBIF1NBIO1_MSI_MSG_ADDR_LO_ADDRESS             0x104400a4UL
#define SMN_DEV0_FUNC0_NBIF2NBIO0_MSI_MSG_ADDR_LO_ADDRESS             0x105400a4UL
#define SMN_DEV0_FUNC0_NBIF2NBIO1_MSI_MSG_ADDR_LO_ADDRESS             0x107400a4UL
#define SMN_DEV1_FUNC0_NBIF0NBIO0_MSI_MSG_ADDR_LO_ADDRESS             0x101480a4UL
#define SMN_DEV1_FUNC0_NBIF0NBIO1_MSI_MSG_ADDR_LO_ADDRESS             0x103480a4UL
#define SMN_DEV1_FUNC0_NBIF1NBIO0_MSI_MSG_ADDR_LO_ADDRESS             0x102480a4UL
#define SMN_DEV1_FUNC0_NBIF1NBIO1_MSI_MSG_ADDR_LO_ADDRESS             0x104480a4UL


/***********************************************************
* Register Name : MSI_MSG_CNTL
************************************************************/

#define MSI_MSG_CNTL_MSI_EN_OFFSET                             0
#define MSI_MSG_CNTL_MSI_EN_MASK                               0x1

#define MSI_MSG_CNTL_MSI_MULTI_CAP_OFFSET                      1
#define MSI_MSG_CNTL_MSI_MULTI_CAP_MASK                        0xe

#define MSI_MSG_CNTL_MSI_MULTI_EN_OFFSET                       4
#define MSI_MSG_CNTL_MSI_MULTI_EN_MASK                         0x70

#define MSI_MSG_CNTL_MSI_64BIT_OFFSET                          7
#define MSI_MSG_CNTL_MSI_64BIT_MASK                            0x80

#define MSI_MSG_CNTL_MSI_PERVECTOR_MASKING_CAP_OFFSET          8
#define MSI_MSG_CNTL_MSI_PERVECTOR_MASKING_CAP_MASK            0x100

#define MSI_MSG_CNTL_MSI_EXT_MSG_DATA_CAP_OFFSET               9
#define MSI_MSG_CNTL_MSI_EXT_MSG_DATA_CAP_MASK                 0x200

#define MSI_MSG_CNTL_MSI_EXT_MSG_DATA_EN_OFFSET                10
#define MSI_MSG_CNTL_MSI_EXT_MSG_DATA_EN_MASK                  0x400

#define MSI_MSG_CNTL_Reserved_15_11_OFFSET                     11
#define MSI_MSG_CNTL_Reserved_15_11_MASK                       0xf800

typedef union {
  struct {
    UINT16                                              MSI_EN:1;
    UINT16                                       MSI_MULTI_CAP:3;
    UINT16                                        MSI_MULTI_EN:3;
    UINT16                                           MSI_64BIT:1;
    UINT16                           MSI_PERVECTOR_MASKING_CAP:1;
    UINT16                                MSI_EXT_MSG_DATA_CAP:1;
    UINT16                                 MSI_EXT_MSG_DATA_EN:1;
    UINT16                                      Reserved_15_11:5;
  } Field;
  UINT16 Value;
} MSI_MSG_CNTL_STRUCT;

#define PCICFG_NBIFEPF0CFG_MSI_MSG_CNTL_OFFSET                      0xa2
#define SMN_DEV0_FUNC0_NBIF0NBIO0_MSI_MSG_CNTL_ADDRESS                0x101400a2UL
#define SMN_DEV0_FUNC0_NBIF0NBIO1_MSI_MSG_CNTL_ADDRESS                0x103400a2UL
#define SMN_DEV0_FUNC0_NBIF1NBIO0_MSI_MSG_CNTL_ADDRESS                0x102400a2UL
#define SMN_DEV0_FUNC0_NBIF1NBIO1_MSI_MSG_CNTL_ADDRESS                0x104400a2UL
#define SMN_DEV0_FUNC0_NBIF2NBIO0_MSI_MSG_CNTL_ADDRESS                0x105400a2UL
#define SMN_DEV0_FUNC0_NBIF2NBIO1_MSI_MSG_CNTL_ADDRESS                0x107400a2UL
#define SMN_DEV1_FUNC0_NBIF0NBIO0_MSI_MSG_CNTL_ADDRESS                0x101480a2UL
#define SMN_DEV1_FUNC0_NBIF0NBIO1_MSI_MSG_CNTL_ADDRESS                0x103480a2UL
#define SMN_DEV1_FUNC0_NBIF1NBIO0_MSI_MSG_CNTL_ADDRESS                0x102480a2UL
#define SMN_DEV1_FUNC0_NBIF1NBIO1_MSI_MSG_CNTL_ADDRESS                0x104480a2UL


/***********************************************************
* Register Name : MSI_MSG_DATA
************************************************************/

#define MSI_MSG_DATA_MSI_DATA_OFFSET                           0
#define MSI_MSG_DATA_MSI_DATA_MASK                             0xffff

typedef union {
  struct {
    UINT16                                            MSI_DATA:16;
  } Field;
  UINT16 Value;
} MSI_MSG_DATA_STRUCT;

#define PCICFG_NBIFEPF0CFG_MSI_MSG_DATA_OFFSET                      0x0
#define SMN_DEV0_FUNC0_NBIF0NBIO0_MSI_MSG_DATA_ADDRESS                0x10140000UL
#define SMN_DEV0_FUNC0_NBIF0NBIO1_MSI_MSG_DATA_ADDRESS                0x10340000UL
#define SMN_DEV0_FUNC0_NBIF1NBIO0_MSI_MSG_DATA_ADDRESS                0x10240000UL
#define SMN_DEV0_FUNC0_NBIF1NBIO1_MSI_MSG_DATA_ADDRESS                0x10440000UL
#define SMN_DEV0_FUNC0_NBIF2NBIO0_MSI_MSG_DATA_ADDRESS                0x10540000UL
#define SMN_DEV0_FUNC0_NBIF2NBIO1_MSI_MSG_DATA_ADDRESS                0x10740000UL
#define SMN_DEV1_FUNC0_NBIF0NBIO0_MSI_MSG_DATA_ADDRESS                0x10148000UL
#define SMN_DEV1_FUNC0_NBIF0NBIO1_MSI_MSG_DATA_ADDRESS                0x10348000UL
#define SMN_DEV1_FUNC0_NBIF1NBIO0_MSI_MSG_DATA_ADDRESS                0x10248000UL
#define SMN_DEV1_FUNC0_NBIF1NBIO1_MSI_MSG_DATA_ADDRESS                0x10448000UL


/***********************************************************
* Register Name : MSI_MSG_DATA_64
************************************************************/

#define MSI_MSG_DATA_64_MSI_DATA_64_OFFSET                     0
#define MSI_MSG_DATA_64_MSI_DATA_64_MASK                       0xffff

typedef union {
  struct {
    UINT16                                         MSI_DATA_64:16;
  } Field;
  UINT16 Value;
} MSI_MSG_DATA_64_STRUCT;

#define PCICFG_NBIFEPF0CFG_MSI_MSG_DATA_64_OFFSET                   0x1
#define SMN_DEV0_FUNC0_NBIF0NBIO0_MSI_MSG_DATA_64_ADDRESS             0x10140001UL
#define SMN_DEV0_FUNC0_NBIF0NBIO1_MSI_MSG_DATA_64_ADDRESS             0x10340001UL
#define SMN_DEV0_FUNC0_NBIF1NBIO0_MSI_MSG_DATA_64_ADDRESS             0x10240001UL
#define SMN_DEV0_FUNC0_NBIF1NBIO1_MSI_MSG_DATA_64_ADDRESS             0x10440001UL
#define SMN_DEV0_FUNC0_NBIF2NBIO0_MSI_MSG_DATA_64_ADDRESS             0x10540001UL
#define SMN_DEV0_FUNC0_NBIF2NBIO1_MSI_MSG_DATA_64_ADDRESS             0x10740001UL
#define SMN_DEV1_FUNC0_NBIF0NBIO0_MSI_MSG_DATA_64_ADDRESS             0x10148001UL
#define SMN_DEV1_FUNC0_NBIF0NBIO1_MSI_MSG_DATA_64_ADDRESS             0x10348001UL
#define SMN_DEV1_FUNC0_NBIF1NBIO0_MSI_MSG_DATA_64_ADDRESS             0x10248001UL
#define SMN_DEV1_FUNC0_NBIF1NBIO1_MSI_MSG_DATA_64_ADDRESS             0x10448001UL


/***********************************************************
* Register Name : MSI_PENDING
************************************************************/

#define MSI_PENDING_MSI_PENDING_OFFSET                         0
#define MSI_PENDING_MSI_PENDING_MASK                           0xffffffff

typedef union {
  struct {
    UINT32                                         MSI_PENDING:32;
  } Field;
  UINT32 Value;
} MSI_PENDING_STRUCT;

#define PCICFG_NBIFEPF0CFG_MSI_PENDING_OFFSET                       0x0
#define SMN_DEV0_FUNC0_NBIF0NBIO0_MSI_PENDING_ADDRESS                 0x10140000UL
#define SMN_DEV0_FUNC0_NBIF0NBIO1_MSI_PENDING_ADDRESS                 0x10340000UL
#define SMN_DEV0_FUNC0_NBIF1NBIO0_MSI_PENDING_ADDRESS                 0x10240000UL
#define SMN_DEV0_FUNC0_NBIF1NBIO1_MSI_PENDING_ADDRESS                 0x10440000UL
#define SMN_DEV0_FUNC0_NBIF2NBIO0_MSI_PENDING_ADDRESS                 0x10540000UL
#define SMN_DEV0_FUNC0_NBIF2NBIO1_MSI_PENDING_ADDRESS                 0x10740000UL
#define SMN_DEV1_FUNC0_NBIF0NBIO0_MSI_PENDING_ADDRESS                 0x10148000UL
#define SMN_DEV1_FUNC0_NBIF0NBIO1_MSI_PENDING_ADDRESS                 0x10348000UL
#define SMN_DEV1_FUNC0_NBIF1NBIO0_MSI_PENDING_ADDRESS                 0x10248000UL
#define SMN_DEV1_FUNC0_NBIF1NBIO1_MSI_PENDING_ADDRESS                 0x10448000UL


/***********************************************************
* Register Name : MSI_PENDING_64
************************************************************/

#define MSI_PENDING_64_MSI_PENDING_64_OFFSET                   0
#define MSI_PENDING_64_MSI_PENDING_64_MASK                     0xffffffff

typedef union {
  struct {
    UINT32                                      MSI_PENDING_64:32;
  } Field;
  UINT32 Value;
} MSI_PENDING_64_STRUCT;

#define PCICFG_NBIFEPF0CFG_MSI_PENDING_64_OFFSET                    0xb4
#define SMN_DEV0_FUNC0_NBIF0NBIO0_MSI_PENDING_64_ADDRESS              0x101400b4UL
#define SMN_DEV0_FUNC0_NBIF0NBIO1_MSI_PENDING_64_ADDRESS              0x103400b4UL
#define SMN_DEV0_FUNC0_NBIF1NBIO0_MSI_PENDING_64_ADDRESS              0x102400b4UL
#define SMN_DEV0_FUNC0_NBIF1NBIO1_MSI_PENDING_64_ADDRESS              0x104400b4UL
#define SMN_DEV0_FUNC0_NBIF2NBIO0_MSI_PENDING_64_ADDRESS              0x105400b4UL
#define SMN_DEV0_FUNC0_NBIF2NBIO1_MSI_PENDING_64_ADDRESS              0x107400b4UL
#define SMN_DEV1_FUNC0_NBIF0NBIO0_MSI_PENDING_64_ADDRESS              0x101480b4UL
#define SMN_DEV1_FUNC0_NBIF0NBIO1_MSI_PENDING_64_ADDRESS              0x103480b4UL
#define SMN_DEV1_FUNC0_NBIF1NBIO0_MSI_PENDING_64_ADDRESS              0x102480b4UL
#define SMN_DEV1_FUNC0_NBIF1NBIO1_MSI_PENDING_64_ADDRESS              0x104480b4UL


/***********************************************************
* Register Name : PCIE_ACS_CAP
************************************************************/

#define NBIF_ACS_CAP_SOURCE_VALIDATION_OFFSET                  0
#define NBIF_ACS_CAP_SOURCE_VALIDATION_MASK                    0x1

#define NBIF_ACS_CAP_TRANSLATION_BLOCKING_OFFSET               1
#define NBIF_ACS_CAP_TRANSLATION_BLOCKING_MASK                 0x2

#define NBIF_ACS_CAP_P2P_REQUEST_REDIRECT_OFFSET               2
#define NBIF_ACS_CAP_P2P_REQUEST_REDIRECT_MASK                 0x4

#define NBIF_ACS_CAP_P2P_COMPLETION_REDIRECT_OFFSET            3
#define NBIF_ACS_CAP_P2P_COMPLETION_REDIRECT_MASK              0x8

#define NBIF_ACS_CAP_UPSTREAM_FORWARDING_OFFSET                4
#define NBIF_ACS_CAP_UPSTREAM_FORWARDING_MASK                  0x10

#define NBIF_ACS_CAP_P2P_EGRESS_CONTROL_OFFSET                 5
#define NBIF_ACS_CAP_P2P_EGRESS_CONTROL_MASK                   0x20

#define NBIF_ACS_CAP_DIRECT_TRANSLATED_P2P_OFFSET              6
#define NBIF_ACS_CAP_DIRECT_TRANSLATED_P2P_MASK                0x40

#define NBIF_ACS_CAP_ENHANCED_CAPABILITY_OFFSET                7
#define NBIF_ACS_CAP_ENHANCED_CAPABILITY_MASK                  0x80

#define NBIF_ACS_CAP_EGRESS_CONTROL_VECTOR_SIZE_OFFSET         8
#define NBIF_ACS_CAP_EGRESS_CONTROL_VECTOR_SIZE_MASK           0xff00

typedef union {
  struct {
    UINT16                                   SOURCE_VALIDATION:1;
    UINT16                                TRANSLATION_BLOCKING:1;
    UINT16                                P2P_REQUEST_REDIRECT:1;
    UINT16                             P2P_COMPLETION_REDIRECT:1;
    UINT16                                 UPSTREAM_FORWARDING:1;
    UINT16                                  P2P_EGRESS_CONTROL:1;
    UINT16                               DIRECT_TRANSLATED_P2P:1;
    UINT16                                 ENHANCED_CAPABILITY:1;
    UINT16                          EGRESS_CONTROL_VECTOR_SIZE:8;
  } Field;
  UINT16 Value;
} PCIE_ACS_CAP_STRUCT;

#define PCICFG_NBIFEPF0CFG_PCIE_ACS_CAP_OFFSET                      0x2a4
#define SMN_DEV0_FUNC0_NBIF0NBIO0_PCIE_ACS_CAP_ADDRESS                0x101402a4UL
#define SMN_DEV0_FUNC0_NBIF0NBIO1_PCIE_ACS_CAP_ADDRESS                0x103402a4UL
#define SMN_DEV0_FUNC0_NBIF1NBIO0_PCIE_ACS_CAP_ADDRESS                0x102402a4UL
#define SMN_DEV0_FUNC0_NBIF1NBIO1_PCIE_ACS_CAP_ADDRESS                0x104402a4UL
#define SMN_DEV0_FUNC0_NBIF2NBIO0_PCIE_ACS_CAP_ADDRESS                0x105402a4UL
#define SMN_DEV0_FUNC0_NBIF2NBIO1_PCIE_ACS_CAP_ADDRESS                0x107402a4UL
#define SMN_DEV1_FUNC0_NBIF0NBIO0_PCIE_ACS_CAP_ADDRESS                0x101482a4UL
#define SMN_DEV1_FUNC0_NBIF0NBIO1_PCIE_ACS_CAP_ADDRESS                0x103482a4UL
#define SMN_DEV1_FUNC0_NBIF1NBIO0_PCIE_ACS_CAP_ADDRESS                0x102482a4UL
#define SMN_DEV1_FUNC0_NBIF1NBIO1_PCIE_ACS_CAP_ADDRESS                0x104482a4UL


/***********************************************************
* Register Name : PCIE_ACS_CNTL
************************************************************/

#define NBIF_ACS_CNTL_SOURCE_VALIDATION_EN_OFFSET              0
#define NBIF_ACS_CNTL_SOURCE_VALIDATION_EN_MASK                0x1

#define NBIF_ACS_CNTL_TRANSLATION_BLOCKING_EN_OFFSET           1
#define NBIF_ACS_CNTL_TRANSLATION_BLOCKING_EN_MASK             0x2

#define NBIF_ACS_CNTL_P2P_REQUEST_REDIRECT_EN_OFFSET           2
#define NBIF_ACS_CNTL_P2P_REQUEST_REDIRECT_EN_MASK             0x4

#define NBIF_ACS_CNTL_P2P_COMPLETION_REDIRECT_EN_OFFSET        3
#define NBIF_ACS_CNTL_P2P_COMPLETION_REDIRECT_EN_MASK          0x8

#define NBIF_ACS_CNTL_UPSTREAM_FORWARDING_EN_OFFSET            4
#define NBIF_ACS_CNTL_UPSTREAM_FORWARDING_EN_MASK              0x10

#define NBIF_ACS_CNTL_P2P_EGRESS_CONTROL_EN_OFFSET             5
#define NBIF_ACS_CNTL_P2P_EGRESS_CONTROL_EN_MASK               0x20

#define NBIF_ACS_CNTL_DIRECT_TRANSLATED_P2P_EN_OFFSET          6
#define NBIF_ACS_CNTL_DIRECT_TRANSLATED_P2P_EN_MASK            0x40

#define NBIF_ACS_CNTL_IO_REQUEST_BLOCKING_EN_OFFSET            7
#define NBIF_ACS_CNTL_IO_REQUEST_BLOCKING_EN_MASK              0x80

#define NBIF_ACS_CNTL_DSP_MEMORY_TARGET_ACCESS_CNTL_OFFSET     8
#define NBIF_ACS_CNTL_DSP_MEMORY_TARGET_ACCESS_CNTL_MASK       0x300

#define NBIF_ACS_CNTL_USP_MEMORY_TARGET_ACCESS_CNTL_OFFSET     10
#define NBIF_ACS_CNTL_USP_MEMORY_TARGET_ACCESS_CNTL_MASK       0xc00

#define NBIF_ACS_CNTL_UNCLAIMED_REQUEST_REDIRECT_CNTL_OFFSET   12
#define NBIF_ACS_CNTL_UNCLAIMED_REQUEST_REDIRECT_CNTL_MASK     0x1000

#define NBIF_ACS_CNTL_Reserved_15_13_OFFSET                    13
#define NBIF_ACS_CNTL_Reserved_15_13_MASK                      0xe000

typedef union {
  struct {
    UINT16                                SOURCE_VALIDATION_EN:1;
    UINT16                             TRANSLATION_BLOCKING_EN:1;
    UINT16                             P2P_REQUEST_REDIRECT_EN:1;
    UINT16                          P2P_COMPLETION_REDIRECT_EN:1;
    UINT16                              UPSTREAM_FORWARDING_EN:1;
    UINT16                               P2P_EGRESS_CONTROL_EN:1;
    UINT16                            DIRECT_TRANSLATED_P2P_EN:1;
    UINT16                              IO_REQUEST_BLOCKING_EN:1;
    UINT16                       DSP_MEMORY_TARGET_ACCESS_CNTL:2;
    UINT16                       USP_MEMORY_TARGET_ACCESS_CNTL:2;
    UINT16                     UNCLAIMED_REQUEST_REDIRECT_CNTL:1;
    UINT16                                      Reserved_15_13:3;
  } Field;
  UINT16 Value;
} PCIE_ACS_CNTL_NBIFEPF0CFG_STRUCT;

#define PCICFG_NBIFEPF0CFG_PCIE_ACS_CNTL_OFFSET                     0x2a6
#define SMN_DEV0_FUNC0_NBIF0NBIO0_PCIE_ACS_CNTL_ADDRESS               0x101402a6UL
#define SMN_DEV0_FUNC0_NBIF0NBIO1_PCIE_ACS_CNTL_ADDRESS               0x103402a6UL
#define SMN_DEV0_FUNC0_NBIF1NBIO0_PCIE_ACS_CNTL_ADDRESS               0x102402a6UL
#define SMN_DEV0_FUNC0_NBIF1NBIO1_PCIE_ACS_CNTL_ADDRESS               0x104402a6UL
#define SMN_DEV0_FUNC0_NBIF2NBIO0_PCIE_ACS_CNTL_ADDRESS               0x105402a6UL
#define SMN_DEV0_FUNC0_NBIF2NBIO1_PCIE_ACS_CNTL_ADDRESS               0x107402a6UL
#define SMN_DEV1_FUNC0_NBIF0NBIO0_PCIE_ACS_CNTL_ADDRESS               0x101482a6UL
#define SMN_DEV1_FUNC0_NBIF0NBIO1_PCIE_ACS_CNTL_ADDRESS               0x103482a6UL
#define SMN_DEV1_FUNC0_NBIF1NBIO0_PCIE_ACS_CNTL_ADDRESS               0x102482a6UL
#define SMN_DEV1_FUNC0_NBIF1NBIO1_PCIE_ACS_CNTL_ADDRESS               0x104482a6UL


/***********************************************************
* Register Name : PCIE_ACS_ENH_CAP_LIST
************************************************************/

#define NBIF_ACS_ENH_CAP_LIST_CAP_ID_OFFSET                    0
#define NBIF_ACS_ENH_CAP_LIST_CAP_ID_MASK                      0xffff

#define NBIF_ACS_ENH_CAP_LIST_CAP_VER_OFFSET                   16
#define NBIF_ACS_ENH_CAP_LIST_CAP_VER_MASK                     0xf0000

#define NBIF_ACS_ENH_CAP_LIST_NEXT_PTR_OFFSET                  20
#define NBIF_ACS_ENH_CAP_LIST_NEXT_PTR_MASK                    0xfff00000

typedef union {
  struct {
    UINT32                                              CAP_ID:16;
    UINT32                                             CAP_VER:4;
    UINT32                                            NEXT_PTR:12;
  } Field;
  UINT32 Value;
} PCIE_ACS_ENH_CAP_LIST_STRUCT;

#define PCICFG_NBIFEPF0CFG_PCIE_ACS_ENH_CAP_LIST_OFFSET             0x2a0
#define SMN_DEV0_FUNC0_NBIF0NBIO0_PCIE_ACS_ENH_CAP_LIST_ADDRESS       0x101402a0UL
#define SMN_DEV0_FUNC0_NBIF0NBIO1_PCIE_ACS_ENH_CAP_LIST_ADDRESS       0x103402a0UL
#define SMN_DEV0_FUNC0_NBIF1NBIO0_PCIE_ACS_ENH_CAP_LIST_ADDRESS       0x102402a0UL
#define SMN_DEV0_FUNC0_NBIF1NBIO1_PCIE_ACS_ENH_CAP_LIST_ADDRESS       0x104402a0UL
#define SMN_DEV0_FUNC0_NBIF2NBIO0_PCIE_ACS_ENH_CAP_LIST_ADDRESS       0x105402a0UL
#define SMN_DEV0_FUNC0_NBIF2NBIO1_PCIE_ACS_ENH_CAP_LIST_ADDRESS       0x107402a0UL
#define SMN_DEV1_FUNC0_NBIF0NBIO0_PCIE_ACS_ENH_CAP_LIST_ADDRESS       0x101482a0UL
#define SMN_DEV1_FUNC0_NBIF0NBIO1_PCIE_ACS_ENH_CAP_LIST_ADDRESS       0x103482a0UL
#define SMN_DEV1_FUNC0_NBIF1NBIO0_PCIE_ACS_ENH_CAP_LIST_ADDRESS       0x102482a0UL
#define SMN_DEV1_FUNC0_NBIF1NBIO1_PCIE_ACS_ENH_CAP_LIST_ADDRESS       0x104482a0UL


/***********************************************************
* Register Name : PCIE_ADV_ERR_CAP_CNTL
************************************************************/

#define NBIF_ADV_ERR_CAP_CNTL_FIRST_ERR_PTR_OFFSET             0
#define NBIF_ADV_ERR_CAP_CNTL_FIRST_ERR_PTR_MASK               0x1f

#define NBIF_ADV_ERR_CAP_CNTL_ECRC_GEN_CAP_OFFSET              5
#define NBIF_ADV_ERR_CAP_CNTL_ECRC_GEN_CAP_MASK                0x20

#define NBIF_ADV_ERR_CAP_CNTL_ECRC_GEN_EN_OFFSET               6
#define NBIF_ADV_ERR_CAP_CNTL_ECRC_GEN_EN_MASK                 0x40

#define NBIF_ADV_ERR_CAP_CNTL_ECRC_CHECK_CAP_OFFSET            7
#define NBIF_ADV_ERR_CAP_CNTL_ECRC_CHECK_CAP_MASK              0x80

#define NBIF_ADV_ERR_CAP_CNTL_ECRC_CHECK_EN_OFFSET             8
#define NBIF_ADV_ERR_CAP_CNTL_ECRC_CHECK_EN_MASK               0x100

#define NBIF_ADV_ERR_CAP_CNTL_MULTI_HDR_RECD_CAP_OFFSET        9
#define NBIF_ADV_ERR_CAP_CNTL_MULTI_HDR_RECD_CAP_MASK          0x200

#define NBIF_ADV_ERR_CAP_CNTL_MULTI_HDR_RECD_EN_OFFSET         10
#define NBIF_ADV_ERR_CAP_CNTL_MULTI_HDR_RECD_EN_MASK           0x400

#define NBIF_ADV_ERR_CAP_CNTL_TLP_PREFIX_LOG_PRESENT_OFFSET    11
#define NBIF_ADV_ERR_CAP_CNTL_TLP_PREFIX_LOG_PRESENT_MASK      0x800

#define NBIF_ADV_ERR_CAP_CNTL_COMPLETION_TIMEOUT_LOG_CAPABLE_OFFSET 12
#define NBIF_ADV_ERR_CAP_CNTL_COMPLETION_TIMEOUT_LOG_CAPABLE_MASK 0x1000

#define NBIF_ADV_ERR_CAP_CNTL_Reserved_31_13_OFFSET            13
#define NBIF_ADV_ERR_CAP_CNTL_Reserved_31_13_MASK              0xffffe000

typedef union {
  struct {
    UINT32                                       FIRST_ERR_PTR:5;
    UINT32                                        ECRC_GEN_CAP:1;
    UINT32                                         ECRC_GEN_EN:1;
    UINT32                                      ECRC_CHECK_CAP:1;
    UINT32                                       ECRC_CHECK_EN:1;
    UINT32                                  MULTI_HDR_RECD_CAP:1;
    UINT32                                   MULTI_HDR_RECD_EN:1;
    UINT32                              TLP_PREFIX_LOG_PRESENT:1;
    UINT32                      COMPLETION_TIMEOUT_LOG_CAPABLE:1;
    UINT32                                      Reserved_31_13:19;
  } Field;
  UINT32 Value;
} PCIE_ADV_ERR_CAP_CNTL_STRUCT;

#define PCICFG_NBIFEPF0CFG_PCIE_ADV_ERR_CAP_CNTL_OFFSET             0x168
#define SMN_DEV0_FUNC0_NBIF0NBIO0_PCIE_ADV_ERR_CAP_CNTL_ADDRESS       0x10140168UL
#define SMN_DEV0_FUNC0_NBIF0NBIO1_PCIE_ADV_ERR_CAP_CNTL_ADDRESS       0x10340168UL
#define SMN_DEV0_FUNC0_NBIF1NBIO0_PCIE_ADV_ERR_CAP_CNTL_ADDRESS       0x10240168UL
#define SMN_DEV0_FUNC0_NBIF1NBIO1_PCIE_ADV_ERR_CAP_CNTL_ADDRESS       0x10440168UL
#define SMN_DEV0_FUNC0_NBIF2NBIO0_PCIE_ADV_ERR_CAP_CNTL_ADDRESS       0x10540168UL
#define SMN_DEV0_FUNC0_NBIF2NBIO1_PCIE_ADV_ERR_CAP_CNTL_ADDRESS       0x10740168UL
#define SMN_DEV1_FUNC0_NBIF0NBIO0_PCIE_ADV_ERR_CAP_CNTL_ADDRESS       0x10148168UL
#define SMN_DEV1_FUNC0_NBIF0NBIO1_PCIE_ADV_ERR_CAP_CNTL_ADDRESS       0x10348168UL
#define SMN_DEV1_FUNC0_NBIF1NBIO0_PCIE_ADV_ERR_CAP_CNTL_ADDRESS       0x10248168UL
#define SMN_DEV1_FUNC0_NBIF1NBIO1_PCIE_ADV_ERR_CAP_CNTL_ADDRESS       0x10448168UL


/***********************************************************
* Register Name : PCIE_ADV_ERR_RPT_ENH_CAP_LIST
************************************************************/

#define NBIF_ADV_ERR_RPT_ENH_CAP_LIST_CAP_ID_OFFSET            0
#define NBIF_ADV_ERR_RPT_ENH_CAP_LIST_CAP_ID_MASK              0xffff

#define NBIF_ADV_ERR_RPT_ENH_CAP_LIST_CAP_VER_OFFSET           16
#define NBIF_ADV_ERR_RPT_ENH_CAP_LIST_CAP_VER_MASK             0xf0000

#define NBIF_ADV_ERR_RPT_ENH_CAP_LIST_NEXT_PTR_OFFSET          20
#define NBIF_ADV_ERR_RPT_ENH_CAP_LIST_NEXT_PTR_MASK            0xfff00000

typedef union {
  struct {
    UINT32                                              CAP_ID:16;
    UINT32                                             CAP_VER:4;
    UINT32                                            NEXT_PTR:12;
  } Field;
  UINT32 Value;
} PCIE_ADV_ERR_RPT_ENH_CAP_LIST_STRUCT;

#define PCICFG_NBIFEPF0CFG_PCIE_ADV_ERR_RPT_ENH_CAP_LIST_OFFSET     0x150
#define SMN_DEV0_FUNC0_NBIF0NBIO0_PCIE_ADV_ERR_RPT_ENH_CAP_LIST_ADDRESS 0x10140150UL
#define SMN_DEV0_FUNC0_NBIF0NBIO1_PCIE_ADV_ERR_RPT_ENH_CAP_LIST_ADDRESS 0x10340150UL
#define SMN_DEV0_FUNC0_NBIF1NBIO0_PCIE_ADV_ERR_RPT_ENH_CAP_LIST_ADDRESS 0x10240150UL
#define SMN_DEV0_FUNC0_NBIF1NBIO1_PCIE_ADV_ERR_RPT_ENH_CAP_LIST_ADDRESS 0x10440150UL
#define SMN_DEV0_FUNC0_NBIF2NBIO0_PCIE_ADV_ERR_RPT_ENH_CAP_LIST_ADDRESS 0x10540150UL
#define SMN_DEV0_FUNC0_NBIF2NBIO1_PCIE_ADV_ERR_RPT_ENH_CAP_LIST_ADDRESS 0x10740150UL
#define SMN_DEV1_FUNC0_NBIF0NBIO0_PCIE_ADV_ERR_RPT_ENH_CAP_LIST_ADDRESS 0x10148150UL
#define SMN_DEV1_FUNC0_NBIF0NBIO1_PCIE_ADV_ERR_RPT_ENH_CAP_LIST_ADDRESS 0x10348150UL
#define SMN_DEV1_FUNC0_NBIF1NBIO0_PCIE_ADV_ERR_RPT_ENH_CAP_LIST_ADDRESS 0x10248150UL
#define SMN_DEV1_FUNC0_NBIF1NBIO1_PCIE_ADV_ERR_RPT_ENH_CAP_LIST_ADDRESS 0x10448150UL


/***********************************************************
* Register Name : PCIE_AP_ENH_CAP_LIST
************************************************************/

#define NBIF_AP_ENH_CAP_LIST_CAP_ID_OFFSET                     0
#define NBIF_AP_ENH_CAP_LIST_CAP_ID_MASK                       0xffff

#define NBIF_AP_ENH_CAP_LIST_CAP_VER_OFFSET                    16
#define NBIF_AP_ENH_CAP_LIST_CAP_VER_MASK                      0xf0000

#define NBIF_AP_ENH_CAP_LIST_NEXT_PTR_OFFSET                   20
#define NBIF_AP_ENH_CAP_LIST_NEXT_PTR_MASK                     0xfff00000

typedef union {
  struct {
    UINT32                                              CAP_ID:16;
    UINT32                                             CAP_VER:4;
    UINT32                                            NEXT_PTR:12;
  } Field;
  UINT32 Value;
} PCIE_AP_ENH_CAP_LIST_STRUCT;

#define PCICFG_NBIFEPF0CFG_PCIE_AP_ENH_CAP_LIST_OFFSET              0x530
#define SMN_DEV0_FUNC0_NBIF0NBIO0_PCIE_AP_ENH_CAP_LIST_ADDRESS        0x10140530UL
#define SMN_DEV0_FUNC0_NBIF0NBIO1_PCIE_AP_ENH_CAP_LIST_ADDRESS        0x10340530UL
#define SMN_DEV0_FUNC0_NBIF1NBIO0_PCIE_AP_ENH_CAP_LIST_ADDRESS        0x10240530UL
#define SMN_DEV0_FUNC0_NBIF1NBIO1_PCIE_AP_ENH_CAP_LIST_ADDRESS        0x10440530UL
#define SMN_DEV0_FUNC0_NBIF2NBIO0_PCIE_AP_ENH_CAP_LIST_ADDRESS        0x10540530UL
#define SMN_DEV0_FUNC0_NBIF2NBIO1_PCIE_AP_ENH_CAP_LIST_ADDRESS        0x10740530UL
#define SMN_DEV1_FUNC0_NBIF0NBIO0_PCIE_AP_ENH_CAP_LIST_ADDRESS        0x10148530UL
#define SMN_DEV1_FUNC0_NBIF0NBIO1_PCIE_AP_ENH_CAP_LIST_ADDRESS        0x10348530UL
#define SMN_DEV1_FUNC0_NBIF1NBIO0_PCIE_AP_ENH_CAP_LIST_ADDRESS        0x10248530UL
#define SMN_DEV1_FUNC0_NBIF1NBIO1_PCIE_AP_ENH_CAP_LIST_ADDRESS        0x10448530UL


/***********************************************************
* Register Name : PCIE_ARI_CAP
************************************************************/

#define NBIF_ARI_CAP_ARI_MFVC_FUNC_GROUPS_CAP_OFFSET           0
#define NBIF_ARI_CAP_ARI_MFVC_FUNC_GROUPS_CAP_MASK             0x1

#define NBIF_ARI_CAP_ARI_ACS_FUNC_GROUPS_CAP_OFFSET            1
#define NBIF_ARI_CAP_ARI_ACS_FUNC_GROUPS_CAP_MASK              0x2

#define NBIF_ARI_CAP_Reserved_7_2_OFFSET                       2
#define NBIF_ARI_CAP_Reserved_7_2_MASK                         0xfc

#define NBIF_ARI_CAP_ARI_NEXT_FUNC_NUM_OFFSET                  8
#define NBIF_ARI_CAP_ARI_NEXT_FUNC_NUM_MASK                    0xff00

typedef union {
  struct {
    UINT16                            ARI_MFVC_FUNC_GROUPS_CAP:1;
    UINT16                             ARI_ACS_FUNC_GROUPS_CAP:1;
    UINT16                                        Reserved_7_2:6;
    UINT16                                   ARI_NEXT_FUNC_NUM:8;
  } Field;
  UINT16 Value;
} PCIE_ARI_CAP_STRUCT;

#define PCICFG_NBIFEPF0CFG_PCIE_ARI_CAP_OFFSET                      0x32c
#define SMN_DEV0_FUNC0_NBIF0NBIO0_PCIE_ARI_CAP_ADDRESS                0x1014032cUL
#define SMN_DEV0_FUNC0_NBIF0NBIO1_PCIE_ARI_CAP_ADDRESS                0x1034032cUL
#define SMN_DEV0_FUNC0_NBIF1NBIO0_PCIE_ARI_CAP_ADDRESS                0x1024032cUL
#define SMN_DEV0_FUNC0_NBIF1NBIO1_PCIE_ARI_CAP_ADDRESS                0x1044032cUL
#define SMN_DEV0_FUNC0_NBIF2NBIO0_PCIE_ARI_CAP_ADDRESS                0x1054032cUL
#define SMN_DEV0_FUNC0_NBIF2NBIO1_PCIE_ARI_CAP_ADDRESS                0x1074032cUL
#define SMN_DEV1_FUNC0_NBIF0NBIO0_PCIE_ARI_CAP_ADDRESS                0x1014832cUL
#define SMN_DEV1_FUNC0_NBIF0NBIO1_PCIE_ARI_CAP_ADDRESS                0x1034832cUL
#define SMN_DEV1_FUNC0_NBIF1NBIO0_PCIE_ARI_CAP_ADDRESS                0x1024832cUL
#define SMN_DEV1_FUNC0_NBIF1NBIO1_PCIE_ARI_CAP_ADDRESS                0x1044832cUL


/***********************************************************
* Register Name : PCIE_ARI_CNTL
************************************************************/

#define NBIF_ARI_CNTL_ARI_MFVC_FUNC_GROUPS_EN_OFFSET           0
#define NBIF_ARI_CNTL_ARI_MFVC_FUNC_GROUPS_EN_MASK             0x1

#define NBIF_ARI_CNTL_ARI_ACS_FUNC_GROUPS_EN_OFFSET            1
#define NBIF_ARI_CNTL_ARI_ACS_FUNC_GROUPS_EN_MASK              0x2

#define NBIF_ARI_CNTL_Reserved_3_2_OFFSET                      2
#define NBIF_ARI_CNTL_Reserved_3_2_MASK                        0xc

#define NBIF_ARI_CNTL_ARI_FUNCTION_GROUP_OFFSET                4
#define NBIF_ARI_CNTL_ARI_FUNCTION_GROUP_MASK                  0x70

#define NBIF_ARI_CNTL_Reserved_15_7_OFFSET                     7
#define NBIF_ARI_CNTL_Reserved_15_7_MASK                       0xff80

typedef union {
  struct {
    UINT16                             ARI_MFVC_FUNC_GROUPS_EN:1;
    UINT16                              ARI_ACS_FUNC_GROUPS_EN:1;
    UINT16                                        Reserved_3_2:2;
    UINT16                                  ARI_FUNCTION_GROUP:3;
    UINT16                                       Reserved_15_7:9;
  } Field;
  UINT16 Value;
} PCIE_ARI_CNTL_STRUCT;

#define PCICFG_NBIFEPF0CFG_PCIE_ARI_CNTL_OFFSET                     0x32e
#define SMN_DEV0_FUNC0_NBIF0NBIO0_PCIE_ARI_CNTL_ADDRESS               0x1014032eUL
#define SMN_DEV0_FUNC0_NBIF0NBIO1_PCIE_ARI_CNTL_ADDRESS               0x1034032eUL
#define SMN_DEV0_FUNC0_NBIF1NBIO0_PCIE_ARI_CNTL_ADDRESS               0x1024032eUL
#define SMN_DEV0_FUNC0_NBIF1NBIO1_PCIE_ARI_CNTL_ADDRESS               0x1044032eUL
#define SMN_DEV0_FUNC0_NBIF2NBIO0_PCIE_ARI_CNTL_ADDRESS               0x1054032eUL
#define SMN_DEV0_FUNC0_NBIF2NBIO1_PCIE_ARI_CNTL_ADDRESS               0x1074032eUL
#define SMN_DEV1_FUNC0_NBIF0NBIO0_PCIE_ARI_CNTL_ADDRESS               0x1014832eUL
#define SMN_DEV1_FUNC0_NBIF0NBIO1_PCIE_ARI_CNTL_ADDRESS               0x1034832eUL
#define SMN_DEV1_FUNC0_NBIF1NBIO0_PCIE_ARI_CNTL_ADDRESS               0x1024832eUL
#define SMN_DEV1_FUNC0_NBIF1NBIO1_PCIE_ARI_CNTL_ADDRESS               0x1044832eUL


/***********************************************************
* Register Name : PCIE_ARI_ENH_CAP_LIST
************************************************************/

#define NBIF_ARI_ENH_CAP_LIST_CAP_ID_OFFSET                    0
#define NBIF_ARI_ENH_CAP_LIST_CAP_ID_MASK                      0xffff

#define NBIF_ARI_ENH_CAP_LIST_CAP_VER_OFFSET                   16
#define NBIF_ARI_ENH_CAP_LIST_CAP_VER_MASK                     0xf0000

#define NBIF_ARI_ENH_CAP_LIST_NEXT_PTR_OFFSET                  20
#define NBIF_ARI_ENH_CAP_LIST_NEXT_PTR_MASK                    0xfff00000

typedef union {
  struct {
    UINT32                                              CAP_ID:16;
    UINT32                                             CAP_VER:4;
    UINT32                                            NEXT_PTR:12;
  } Field;
  UINT32 Value;
} PCIE_ARI_ENH_CAP_LIST_STRUCT;

#define PCICFG_NBIFEPF0CFG_PCIE_ARI_ENH_CAP_LIST_OFFSET             0x328
#define SMN_DEV0_FUNC0_NBIF0NBIO0_PCIE_ARI_ENH_CAP_LIST_ADDRESS       0x10140328UL
#define SMN_DEV0_FUNC0_NBIF0NBIO1_PCIE_ARI_ENH_CAP_LIST_ADDRESS       0x10340328UL
#define SMN_DEV0_FUNC0_NBIF1NBIO0_PCIE_ARI_ENH_CAP_LIST_ADDRESS       0x10240328UL
#define SMN_DEV0_FUNC0_NBIF1NBIO1_PCIE_ARI_ENH_CAP_LIST_ADDRESS       0x10440328UL
#define SMN_DEV0_FUNC0_NBIF2NBIO0_PCIE_ARI_ENH_CAP_LIST_ADDRESS       0x10540328UL
#define SMN_DEV0_FUNC0_NBIF2NBIO1_PCIE_ARI_ENH_CAP_LIST_ADDRESS       0x10740328UL
#define SMN_DEV1_FUNC0_NBIF0NBIO0_PCIE_ARI_ENH_CAP_LIST_ADDRESS       0x10148328UL
#define SMN_DEV1_FUNC0_NBIF0NBIO1_PCIE_ARI_ENH_CAP_LIST_ADDRESS       0x10348328UL
#define SMN_DEV1_FUNC0_NBIF1NBIO0_PCIE_ARI_ENH_CAP_LIST_ADDRESS       0x10248328UL
#define SMN_DEV1_FUNC0_NBIF1NBIO1_PCIE_ARI_ENH_CAP_LIST_ADDRESS       0x10448328UL


/***********************************************************
* Register Name : PCIE_BAR1_CAP
************************************************************/

#define NBIF_BAR1_CAP_Reserved_3_0_OFFSET                      0
#define NBIF_BAR1_CAP_Reserved_3_0_MASK                        0xf

#define NBIF_BAR1_CAP_BAR_SIZE_SUPPORTED_OFFSET                4
#define NBIF_BAR1_CAP_BAR_SIZE_SUPPORTED_MASK                  0xfffffff0

typedef union {
  struct {
    UINT32                                        Reserved_3_0:4;
    UINT32                                  BAR_SIZE_SUPPORTED:28;
  } Field;
  UINT32 Value;
} PCIE_BAR1_CAP_STRUCT;

#define PCICFG_NBIFEPF0CFG_PCIE_BAR1_CAP_OFFSET                     0x204
#define SMN_DEV0_FUNC0_NBIF0NBIO0_PCIE_BAR1_CAP_ADDRESS               0x10140204UL
#define SMN_DEV0_FUNC0_NBIF0NBIO1_PCIE_BAR1_CAP_ADDRESS               0x10340204UL
#define SMN_DEV0_FUNC0_NBIF1NBIO0_PCIE_BAR1_CAP_ADDRESS               0x10240204UL
#define SMN_DEV0_FUNC0_NBIF1NBIO1_PCIE_BAR1_CAP_ADDRESS               0x10440204UL
#define SMN_DEV0_FUNC0_NBIF2NBIO0_PCIE_BAR1_CAP_ADDRESS               0x10540204UL
#define SMN_DEV0_FUNC0_NBIF2NBIO1_PCIE_BAR1_CAP_ADDRESS               0x10740204UL
#define SMN_DEV1_FUNC0_NBIF0NBIO0_PCIE_BAR1_CAP_ADDRESS               0x10148204UL
#define SMN_DEV1_FUNC0_NBIF0NBIO1_PCIE_BAR1_CAP_ADDRESS               0x10348204UL
#define SMN_DEV1_FUNC0_NBIF1NBIO0_PCIE_BAR1_CAP_ADDRESS               0x10248204UL
#define SMN_DEV1_FUNC0_NBIF1NBIO1_PCIE_BAR1_CAP_ADDRESS               0x10448204UL


/***********************************************************
* Register Name : PCIE_BAR1_CNTL
************************************************************/

#define NBIF_BAR1_CNTL_BAR_INDEX_OFFSET                        0
#define NBIF_BAR1_CNTL_BAR_INDEX_MASK                          0x7

#define NBIF_BAR1_CNTL_Reserved_4_3_OFFSET                     3
#define NBIF_BAR1_CNTL_Reserved_4_3_MASK                       0x18

#define NBIF_BAR1_CNTL_BAR_TOTAL_NUM_OFFSET                    5
#define NBIF_BAR1_CNTL_BAR_TOTAL_NUM_MASK                      0xe0

#define NBIF_BAR1_CNTL_BAR_SIZE_OFFSET                         8
#define NBIF_BAR1_CNTL_BAR_SIZE_MASK                           0x3f00

#define NBIF_BAR1_CNTL_Reserved_15_14_OFFSET                   14
#define NBIF_BAR1_CNTL_Reserved_15_14_MASK                     0xc000

#define NBIF_BAR1_CNTL_BAR_SIZE_SUPPORTED_UPPER_OFFSET         16
#define NBIF_BAR1_CNTL_BAR_SIZE_SUPPORTED_UPPER_MASK           0xffff0000

typedef union {
  struct {
    UINT32                                           BAR_INDEX:3;
    UINT32                                        Reserved_4_3:2;
    UINT32                                       BAR_TOTAL_NUM:3;
    UINT32                                            BAR_SIZE:6;
    UINT32                                      Reserved_15_14:2;
    UINT32                            BAR_SIZE_SUPPORTED_UPPER:16;
  } Field;
  UINT32 Value;
} PCIE_BAR1_CNTL_STRUCT;

#define PCICFG_NBIFEPF0CFG_PCIE_BAR1_CNTL_OFFSET                    0x208
#define SMN_DEV0_FUNC0_NBIF0NBIO0_PCIE_BAR1_CNTL_ADDRESS              0x10140208UL
#define SMN_DEV0_FUNC0_NBIF0NBIO1_PCIE_BAR1_CNTL_ADDRESS              0x10340208UL
#define SMN_DEV0_FUNC0_NBIF1NBIO0_PCIE_BAR1_CNTL_ADDRESS              0x10240208UL
#define SMN_DEV0_FUNC0_NBIF1NBIO1_PCIE_BAR1_CNTL_ADDRESS              0x10440208UL
#define SMN_DEV0_FUNC0_NBIF2NBIO0_PCIE_BAR1_CNTL_ADDRESS              0x10540208UL
#define SMN_DEV0_FUNC0_NBIF2NBIO1_PCIE_BAR1_CNTL_ADDRESS              0x10740208UL
#define SMN_DEV1_FUNC0_NBIF0NBIO0_PCIE_BAR1_CNTL_ADDRESS              0x10148208UL
#define SMN_DEV1_FUNC0_NBIF0NBIO1_PCIE_BAR1_CNTL_ADDRESS              0x10348208UL
#define SMN_DEV1_FUNC0_NBIF1NBIO0_PCIE_BAR1_CNTL_ADDRESS              0x10248208UL
#define SMN_DEV1_FUNC0_NBIF1NBIO1_PCIE_BAR1_CNTL_ADDRESS              0x10448208UL


/***********************************************************
* Register Name : PCIE_BAR2_CAP
************************************************************/

#define NBIF_BAR2_CAP_Reserved_3_0_OFFSET                      0
#define NBIF_BAR2_CAP_Reserved_3_0_MASK                        0xf

#define NBIF_BAR2_CAP_BAR_SIZE_SUPPORTED_OFFSET                4
#define NBIF_BAR2_CAP_BAR_SIZE_SUPPORTED_MASK                  0xfffffff0

typedef union {
  struct {
    UINT32                                        Reserved_3_0:4;
    UINT32                                  BAR_SIZE_SUPPORTED:28;
  } Field;
  UINT32 Value;
} PCIE_BAR2_CAP_STRUCT;

#define PCICFG_NBIFEPF0CFG_PCIE_BAR2_CAP_OFFSET                     0x20c
#define SMN_DEV0_FUNC0_NBIF0NBIO0_PCIE_BAR2_CAP_ADDRESS               0x1014020cUL
#define SMN_DEV0_FUNC0_NBIF0NBIO1_PCIE_BAR2_CAP_ADDRESS               0x1034020cUL
#define SMN_DEV0_FUNC0_NBIF1NBIO0_PCIE_BAR2_CAP_ADDRESS               0x1024020cUL
#define SMN_DEV0_FUNC0_NBIF1NBIO1_PCIE_BAR2_CAP_ADDRESS               0x1044020cUL
#define SMN_DEV0_FUNC0_NBIF2NBIO0_PCIE_BAR2_CAP_ADDRESS               0x1054020cUL
#define SMN_DEV0_FUNC0_NBIF2NBIO1_PCIE_BAR2_CAP_ADDRESS               0x1074020cUL
#define SMN_DEV1_FUNC0_NBIF0NBIO0_PCIE_BAR2_CAP_ADDRESS               0x1014820cUL
#define SMN_DEV1_FUNC0_NBIF0NBIO1_PCIE_BAR2_CAP_ADDRESS               0x1034820cUL
#define SMN_DEV1_FUNC0_NBIF1NBIO0_PCIE_BAR2_CAP_ADDRESS               0x1024820cUL
#define SMN_DEV1_FUNC0_NBIF1NBIO1_PCIE_BAR2_CAP_ADDRESS               0x1044820cUL


/***********************************************************
* Register Name : PCIE_BAR2_CNTL
************************************************************/

#define NBIF_BAR2_CNTL_BAR_INDEX_OFFSET                        0
#define NBIF_BAR2_CNTL_BAR_INDEX_MASK                          0x7

#define NBIF_BAR2_CNTL_Reserved_4_3_OFFSET                     3
#define NBIF_BAR2_CNTL_Reserved_4_3_MASK                       0x18

#define NBIF_BAR2_CNTL_BAR_TOTAL_NUM_OFFSET                    5
#define NBIF_BAR2_CNTL_BAR_TOTAL_NUM_MASK                      0xe0

#define NBIF_BAR2_CNTL_BAR_SIZE_OFFSET                         8
#define NBIF_BAR2_CNTL_BAR_SIZE_MASK                           0x3f00

#define NBIF_BAR2_CNTL_Reserved_15_14_OFFSET                   14
#define NBIF_BAR2_CNTL_Reserved_15_14_MASK                     0xc000

#define NBIF_BAR2_CNTL_BAR_SIZE_SUPPORTED_UPPER_OFFSET         16
#define NBIF_BAR2_CNTL_BAR_SIZE_SUPPORTED_UPPER_MASK           0xffff0000

typedef union {
  struct {
    UINT32                                           BAR_INDEX:3;
    UINT32                                        Reserved_4_3:2;
    UINT32                                       BAR_TOTAL_NUM:3;
    UINT32                                            BAR_SIZE:6;
    UINT32                                      Reserved_15_14:2;
    UINT32                            BAR_SIZE_SUPPORTED_UPPER:16;
  } Field;
  UINT32 Value;
} PCIE_BAR2_CNTL_STRUCT;

#define PCICFG_NBIFEPF0CFG_PCIE_BAR2_CNTL_OFFSET                    0x210
#define SMN_DEV0_FUNC0_NBIF0NBIO0_PCIE_BAR2_CNTL_ADDRESS              0x10140210UL
#define SMN_DEV0_FUNC0_NBIF0NBIO1_PCIE_BAR2_CNTL_ADDRESS              0x10340210UL
#define SMN_DEV0_FUNC0_NBIF1NBIO0_PCIE_BAR2_CNTL_ADDRESS              0x10240210UL
#define SMN_DEV0_FUNC0_NBIF1NBIO1_PCIE_BAR2_CNTL_ADDRESS              0x10440210UL
#define SMN_DEV0_FUNC0_NBIF2NBIO0_PCIE_BAR2_CNTL_ADDRESS              0x10540210UL
#define SMN_DEV0_FUNC0_NBIF2NBIO1_PCIE_BAR2_CNTL_ADDRESS              0x10740210UL
#define SMN_DEV1_FUNC0_NBIF0NBIO0_PCIE_BAR2_CNTL_ADDRESS              0x10148210UL
#define SMN_DEV1_FUNC0_NBIF0NBIO1_PCIE_BAR2_CNTL_ADDRESS              0x10348210UL
#define SMN_DEV1_FUNC0_NBIF1NBIO0_PCIE_BAR2_CNTL_ADDRESS              0x10248210UL
#define SMN_DEV1_FUNC0_NBIF1NBIO1_PCIE_BAR2_CNTL_ADDRESS              0x10448210UL


/***********************************************************
* Register Name : PCIE_BAR3_CAP
************************************************************/

#define NBIF_BAR3_CAP_Reserved_3_0_OFFSET                      0
#define NBIF_BAR3_CAP_Reserved_3_0_MASK                        0xf

#define NBIF_BAR3_CAP_BAR_SIZE_SUPPORTED_OFFSET                4
#define NBIF_BAR3_CAP_BAR_SIZE_SUPPORTED_MASK                  0xfffffff0

typedef union {
  struct {
    UINT32                                        Reserved_3_0:4;
    UINT32                                  BAR_SIZE_SUPPORTED:28;
  } Field;
  UINT32 Value;
} PCIE_BAR3_CAP_STRUCT;

#define PCICFG_NBIFEPF0CFG_PCIE_BAR3_CAP_OFFSET                     0x214
#define SMN_DEV0_FUNC0_NBIF0NBIO0_PCIE_BAR3_CAP_ADDRESS               0x10140214UL
#define SMN_DEV0_FUNC0_NBIF0NBIO1_PCIE_BAR3_CAP_ADDRESS               0x10340214UL
#define SMN_DEV0_FUNC0_NBIF1NBIO0_PCIE_BAR3_CAP_ADDRESS               0x10240214UL
#define SMN_DEV0_FUNC0_NBIF1NBIO1_PCIE_BAR3_CAP_ADDRESS               0x10440214UL
#define SMN_DEV0_FUNC0_NBIF2NBIO0_PCIE_BAR3_CAP_ADDRESS               0x10540214UL
#define SMN_DEV0_FUNC0_NBIF2NBIO1_PCIE_BAR3_CAP_ADDRESS               0x10740214UL
#define SMN_DEV1_FUNC0_NBIF0NBIO0_PCIE_BAR3_CAP_ADDRESS               0x10148214UL
#define SMN_DEV1_FUNC0_NBIF0NBIO1_PCIE_BAR3_CAP_ADDRESS               0x10348214UL
#define SMN_DEV1_FUNC0_NBIF1NBIO0_PCIE_BAR3_CAP_ADDRESS               0x10248214UL
#define SMN_DEV1_FUNC0_NBIF1NBIO1_PCIE_BAR3_CAP_ADDRESS               0x10448214UL


/***********************************************************
* Register Name : PCIE_BAR3_CNTL
************************************************************/

#define NBIF_BAR3_CNTL_BAR_INDEX_OFFSET                        0
#define NBIF_BAR3_CNTL_BAR_INDEX_MASK                          0x7

#define NBIF_BAR3_CNTL_Reserved_4_3_OFFSET                     3
#define NBIF_BAR3_CNTL_Reserved_4_3_MASK                       0x18

#define NBIF_BAR3_CNTL_BAR_TOTAL_NUM_OFFSET                    5
#define NBIF_BAR3_CNTL_BAR_TOTAL_NUM_MASK                      0xe0

#define NBIF_BAR3_CNTL_BAR_SIZE_OFFSET                         8
#define NBIF_BAR3_CNTL_BAR_SIZE_MASK                           0x3f00

#define NBIF_BAR3_CNTL_Reserved_15_14_OFFSET                   14
#define NBIF_BAR3_CNTL_Reserved_15_14_MASK                     0xc000

#define NBIF_BAR3_CNTL_BAR_SIZE_SUPPORTED_UPPER_OFFSET         16
#define NBIF_BAR3_CNTL_BAR_SIZE_SUPPORTED_UPPER_MASK           0xffff0000

typedef union {
  struct {
    UINT32                                           BAR_INDEX:3;
    UINT32                                        Reserved_4_3:2;
    UINT32                                       BAR_TOTAL_NUM:3;
    UINT32                                            BAR_SIZE:6;
    UINT32                                      Reserved_15_14:2;
    UINT32                            BAR_SIZE_SUPPORTED_UPPER:16;
  } Field;
  UINT32 Value;
} PCIE_BAR3_CNTL_STRUCT;

#define PCICFG_NBIFEPF0CFG_PCIE_BAR3_CNTL_OFFSET                    0x218
#define SMN_DEV0_FUNC0_NBIF0NBIO0_PCIE_BAR3_CNTL_ADDRESS              0x10140218UL
#define SMN_DEV0_FUNC0_NBIF0NBIO1_PCIE_BAR3_CNTL_ADDRESS              0x10340218UL
#define SMN_DEV0_FUNC0_NBIF1NBIO0_PCIE_BAR3_CNTL_ADDRESS              0x10240218UL
#define SMN_DEV0_FUNC0_NBIF1NBIO1_PCIE_BAR3_CNTL_ADDRESS              0x10440218UL
#define SMN_DEV0_FUNC0_NBIF2NBIO0_PCIE_BAR3_CNTL_ADDRESS              0x10540218UL
#define SMN_DEV0_FUNC0_NBIF2NBIO1_PCIE_BAR3_CNTL_ADDRESS              0x10740218UL
#define SMN_DEV1_FUNC0_NBIF0NBIO0_PCIE_BAR3_CNTL_ADDRESS              0x10148218UL
#define SMN_DEV1_FUNC0_NBIF0NBIO1_PCIE_BAR3_CNTL_ADDRESS              0x10348218UL
#define SMN_DEV1_FUNC0_NBIF1NBIO0_PCIE_BAR3_CNTL_ADDRESS              0x10248218UL
#define SMN_DEV1_FUNC0_NBIF1NBIO1_PCIE_BAR3_CNTL_ADDRESS              0x10448218UL


/***********************************************************
* Register Name : PCIE_BAR4_CAP
************************************************************/

#define NBIF_BAR4_CAP_Reserved_3_0_OFFSET                      0
#define NBIF_BAR4_CAP_Reserved_3_0_MASK                        0xf

#define NBIF_BAR4_CAP_BAR_SIZE_SUPPORTED_OFFSET                4
#define NBIF_BAR4_CAP_BAR_SIZE_SUPPORTED_MASK                  0xfffffff0

typedef union {
  struct {
    UINT32                                        Reserved_3_0:4;
    UINT32                                  BAR_SIZE_SUPPORTED:28;
  } Field;
  UINT32 Value;
} PCIE_BAR4_CAP_STRUCT;

#define PCICFG_NBIFEPF0CFG_PCIE_BAR4_CAP_OFFSET                     0x21c
#define SMN_DEV0_FUNC0_NBIF0NBIO0_PCIE_BAR4_CAP_ADDRESS               0x1014021cUL
#define SMN_DEV0_FUNC0_NBIF0NBIO1_PCIE_BAR4_CAP_ADDRESS               0x1034021cUL
#define SMN_DEV0_FUNC0_NBIF1NBIO0_PCIE_BAR4_CAP_ADDRESS               0x1024021cUL
#define SMN_DEV0_FUNC0_NBIF1NBIO1_PCIE_BAR4_CAP_ADDRESS               0x1044021cUL
#define SMN_DEV0_FUNC0_NBIF2NBIO0_PCIE_BAR4_CAP_ADDRESS               0x1054021cUL
#define SMN_DEV0_FUNC0_NBIF2NBIO1_PCIE_BAR4_CAP_ADDRESS               0x1074021cUL
#define SMN_DEV1_FUNC0_NBIF0NBIO0_PCIE_BAR4_CAP_ADDRESS               0x1014821cUL
#define SMN_DEV1_FUNC0_NBIF0NBIO1_PCIE_BAR4_CAP_ADDRESS               0x1034821cUL
#define SMN_DEV1_FUNC0_NBIF1NBIO0_PCIE_BAR4_CAP_ADDRESS               0x1024821cUL
#define SMN_DEV1_FUNC0_NBIF1NBIO1_PCIE_BAR4_CAP_ADDRESS               0x1044821cUL


/***********************************************************
* Register Name : PCIE_BAR4_CNTL
************************************************************/

#define NBIF_BAR4_CNTL_BAR_INDEX_OFFSET                        0
#define NBIF_BAR4_CNTL_BAR_INDEX_MASK                          0x7

#define NBIF_BAR4_CNTL_Reserved_4_3_OFFSET                     3
#define NBIF_BAR4_CNTL_Reserved_4_3_MASK                       0x18

#define NBIF_BAR4_CNTL_BAR_TOTAL_NUM_OFFSET                    5
#define NBIF_BAR4_CNTL_BAR_TOTAL_NUM_MASK                      0xe0

#define NBIF_BAR4_CNTL_BAR_SIZE_OFFSET                         8
#define NBIF_BAR4_CNTL_BAR_SIZE_MASK                           0x3f00

#define NBIF_BAR4_CNTL_Reserved_15_14_OFFSET                   14
#define NBIF_BAR4_CNTL_Reserved_15_14_MASK                     0xc000

#define NBIF_BAR4_CNTL_BAR_SIZE_SUPPORTED_UPPER_OFFSET         16
#define NBIF_BAR4_CNTL_BAR_SIZE_SUPPORTED_UPPER_MASK           0xffff0000

typedef union {
  struct {
    UINT32                                           BAR_INDEX:3;
    UINT32                                        Reserved_4_3:2;
    UINT32                                       BAR_TOTAL_NUM:3;
    UINT32                                            BAR_SIZE:6;
    UINT32                                      Reserved_15_14:2;
    UINT32                            BAR_SIZE_SUPPORTED_UPPER:16;
  } Field;
  UINT32 Value;
} PCIE_BAR4_CNTL_STRUCT;

#define PCICFG_NBIFEPF0CFG_PCIE_BAR4_CNTL_OFFSET                    0x220
#define SMN_DEV0_FUNC0_NBIF0NBIO0_PCIE_BAR4_CNTL_ADDRESS              0x10140220UL
#define SMN_DEV0_FUNC0_NBIF0NBIO1_PCIE_BAR4_CNTL_ADDRESS              0x10340220UL
#define SMN_DEV0_FUNC0_NBIF1NBIO0_PCIE_BAR4_CNTL_ADDRESS              0x10240220UL
#define SMN_DEV0_FUNC0_NBIF1NBIO1_PCIE_BAR4_CNTL_ADDRESS              0x10440220UL
#define SMN_DEV0_FUNC0_NBIF2NBIO0_PCIE_BAR4_CNTL_ADDRESS              0x10540220UL
#define SMN_DEV0_FUNC0_NBIF2NBIO1_PCIE_BAR4_CNTL_ADDRESS              0x10740220UL
#define SMN_DEV1_FUNC0_NBIF0NBIO0_PCIE_BAR4_CNTL_ADDRESS              0x10148220UL
#define SMN_DEV1_FUNC0_NBIF0NBIO1_PCIE_BAR4_CNTL_ADDRESS              0x10348220UL
#define SMN_DEV1_FUNC0_NBIF1NBIO0_PCIE_BAR4_CNTL_ADDRESS              0x10248220UL
#define SMN_DEV1_FUNC0_NBIF1NBIO1_PCIE_BAR4_CNTL_ADDRESS              0x10448220UL


/***********************************************************
* Register Name : PCIE_BAR5_CAP
************************************************************/

#define NBIF_BAR5_CAP_Reserved_3_0_OFFSET                      0
#define NBIF_BAR5_CAP_Reserved_3_0_MASK                        0xf

#define NBIF_BAR5_CAP_BAR_SIZE_SUPPORTED_OFFSET                4
#define NBIF_BAR5_CAP_BAR_SIZE_SUPPORTED_MASK                  0xfffffff0

typedef union {
  struct {
    UINT32                                        Reserved_3_0:4;
    UINT32                                  BAR_SIZE_SUPPORTED:28;
  } Field;
  UINT32 Value;
} PCIE_BAR5_CAP_STRUCT;

#define PCICFG_NBIFEPF0CFG_PCIE_BAR5_CAP_OFFSET                     0x224
#define SMN_DEV0_FUNC0_NBIF0NBIO0_PCIE_BAR5_CAP_ADDRESS               0x10140224UL
#define SMN_DEV0_FUNC0_NBIF0NBIO1_PCIE_BAR5_CAP_ADDRESS               0x10340224UL
#define SMN_DEV0_FUNC0_NBIF1NBIO0_PCIE_BAR5_CAP_ADDRESS               0x10240224UL
#define SMN_DEV0_FUNC0_NBIF1NBIO1_PCIE_BAR5_CAP_ADDRESS               0x10440224UL
#define SMN_DEV0_FUNC0_NBIF2NBIO0_PCIE_BAR5_CAP_ADDRESS               0x10540224UL
#define SMN_DEV0_FUNC0_NBIF2NBIO1_PCIE_BAR5_CAP_ADDRESS               0x10740224UL
#define SMN_DEV1_FUNC0_NBIF0NBIO0_PCIE_BAR5_CAP_ADDRESS               0x10148224UL
#define SMN_DEV1_FUNC0_NBIF0NBIO1_PCIE_BAR5_CAP_ADDRESS               0x10348224UL
#define SMN_DEV1_FUNC0_NBIF1NBIO0_PCIE_BAR5_CAP_ADDRESS               0x10248224UL
#define SMN_DEV1_FUNC0_NBIF1NBIO1_PCIE_BAR5_CAP_ADDRESS               0x10448224UL


/***********************************************************
* Register Name : PCIE_BAR5_CNTL
************************************************************/

#define NBIF_BAR5_CNTL_BAR_INDEX_OFFSET                        0
#define NBIF_BAR5_CNTL_BAR_INDEX_MASK                          0x7

#define NBIF_BAR5_CNTL_Reserved_4_3_OFFSET                     3
#define NBIF_BAR5_CNTL_Reserved_4_3_MASK                       0x18

#define NBIF_BAR5_CNTL_BAR_TOTAL_NUM_OFFSET                    5
#define NBIF_BAR5_CNTL_BAR_TOTAL_NUM_MASK                      0xe0

#define NBIF_BAR5_CNTL_BAR_SIZE_OFFSET                         8
#define NBIF_BAR5_CNTL_BAR_SIZE_MASK                           0x3f00

#define NBIF_BAR5_CNTL_Reserved_15_14_OFFSET                   14
#define NBIF_BAR5_CNTL_Reserved_15_14_MASK                     0xc000

#define NBIF_BAR5_CNTL_BAR_SIZE_SUPPORTED_UPPER_OFFSET         16
#define NBIF_BAR5_CNTL_BAR_SIZE_SUPPORTED_UPPER_MASK           0xffff0000

typedef union {
  struct {
    UINT32                                           BAR_INDEX:3;
    UINT32                                        Reserved_4_3:2;
    UINT32                                       BAR_TOTAL_NUM:3;
    UINT32                                            BAR_SIZE:6;
    UINT32                                      Reserved_15_14:2;
    UINT32                            BAR_SIZE_SUPPORTED_UPPER:16;
  } Field;
  UINT32 Value;
} PCIE_BAR5_CNTL_STRUCT;

#define PCICFG_NBIFEPF0CFG_PCIE_BAR5_CNTL_OFFSET                    0x228
#define SMN_DEV0_FUNC0_NBIF0NBIO0_PCIE_BAR5_CNTL_ADDRESS              0x10140228UL
#define SMN_DEV0_FUNC0_NBIF0NBIO1_PCIE_BAR5_CNTL_ADDRESS              0x10340228UL
#define SMN_DEV0_FUNC0_NBIF1NBIO0_PCIE_BAR5_CNTL_ADDRESS              0x10240228UL
#define SMN_DEV0_FUNC0_NBIF1NBIO1_PCIE_BAR5_CNTL_ADDRESS              0x10440228UL
#define SMN_DEV0_FUNC0_NBIF2NBIO0_PCIE_BAR5_CNTL_ADDRESS              0x10540228UL
#define SMN_DEV0_FUNC0_NBIF2NBIO1_PCIE_BAR5_CNTL_ADDRESS              0x10740228UL
#define SMN_DEV1_FUNC0_NBIF0NBIO0_PCIE_BAR5_CNTL_ADDRESS              0x10148228UL
#define SMN_DEV1_FUNC0_NBIF0NBIO1_PCIE_BAR5_CNTL_ADDRESS              0x10348228UL
#define SMN_DEV1_FUNC0_NBIF1NBIO0_PCIE_BAR5_CNTL_ADDRESS              0x10248228UL
#define SMN_DEV1_FUNC0_NBIF1NBIO1_PCIE_BAR5_CNTL_ADDRESS              0x10448228UL


/***********************************************************
* Register Name : PCIE_BAR6_CAP
************************************************************/

#define NBIF_BAR6_CAP_Reserved_3_0_OFFSET                      0
#define NBIF_BAR6_CAP_Reserved_3_0_MASK                        0xf

#define NBIF_BAR6_CAP_BAR_SIZE_SUPPORTED_OFFSET                4
#define NBIF_BAR6_CAP_BAR_SIZE_SUPPORTED_MASK                  0xfffffff0

typedef union {
  struct {
    UINT32                                        Reserved_3_0:4;
    UINT32                                  BAR_SIZE_SUPPORTED:28;
  } Field;
  UINT32 Value;
} PCIE_BAR6_CAP_STRUCT;

#define PCICFG_NBIFEPF0CFG_PCIE_BAR6_CAP_OFFSET                     0x22c
#define SMN_DEV0_FUNC0_NBIF0NBIO0_PCIE_BAR6_CAP_ADDRESS               0x1014022cUL
#define SMN_DEV0_FUNC0_NBIF0NBIO1_PCIE_BAR6_CAP_ADDRESS               0x1034022cUL
#define SMN_DEV0_FUNC0_NBIF1NBIO0_PCIE_BAR6_CAP_ADDRESS               0x1024022cUL
#define SMN_DEV0_FUNC0_NBIF1NBIO1_PCIE_BAR6_CAP_ADDRESS               0x1044022cUL
#define SMN_DEV0_FUNC0_NBIF2NBIO0_PCIE_BAR6_CAP_ADDRESS               0x1054022cUL
#define SMN_DEV0_FUNC0_NBIF2NBIO1_PCIE_BAR6_CAP_ADDRESS               0x1074022cUL
#define SMN_DEV1_FUNC0_NBIF0NBIO0_PCIE_BAR6_CAP_ADDRESS               0x1014822cUL
#define SMN_DEV1_FUNC0_NBIF0NBIO1_PCIE_BAR6_CAP_ADDRESS               0x1034822cUL
#define SMN_DEV1_FUNC0_NBIF1NBIO0_PCIE_BAR6_CAP_ADDRESS               0x1024822cUL
#define SMN_DEV1_FUNC0_NBIF1NBIO1_PCIE_BAR6_CAP_ADDRESS               0x1044822cUL


/***********************************************************
* Register Name : PCIE_BAR6_CNTL
************************************************************/

#define NBIF_BAR6_CNTL_BAR_INDEX_OFFSET                        0
#define NBIF_BAR6_CNTL_BAR_INDEX_MASK                          0x7

#define NBIF_BAR6_CNTL_Reserved_4_3_OFFSET                     3
#define NBIF_BAR6_CNTL_Reserved_4_3_MASK                       0x18

#define NBIF_BAR6_CNTL_BAR_TOTAL_NUM_OFFSET                    5
#define NBIF_BAR6_CNTL_BAR_TOTAL_NUM_MASK                      0xe0

#define NBIF_BAR6_CNTL_BAR_SIZE_OFFSET                         8
#define NBIF_BAR6_CNTL_BAR_SIZE_MASK                           0x3f00

#define NBIF_BAR6_CNTL_Reserved_15_14_OFFSET                   14
#define NBIF_BAR6_CNTL_Reserved_15_14_MASK                     0xc000

#define NBIF_BAR6_CNTL_BAR_SIZE_SUPPORTED_UPPER_OFFSET         16
#define NBIF_BAR6_CNTL_BAR_SIZE_SUPPORTED_UPPER_MASK           0xffff0000

typedef union {
  struct {
    UINT32                                           BAR_INDEX:3;
    UINT32                                        Reserved_4_3:2;
    UINT32                                       BAR_TOTAL_NUM:3;
    UINT32                                            BAR_SIZE:6;
    UINT32                                      Reserved_15_14:2;
    UINT32                            BAR_SIZE_SUPPORTED_UPPER:16;
  } Field;
  UINT32 Value;
} PCIE_BAR6_CNTL_STRUCT;

#define PCICFG_NBIFEPF0CFG_PCIE_BAR6_CNTL_OFFSET                    0x230
#define SMN_DEV0_FUNC0_NBIF0NBIO0_PCIE_BAR6_CNTL_ADDRESS              0x10140230UL
#define SMN_DEV0_FUNC0_NBIF0NBIO1_PCIE_BAR6_CNTL_ADDRESS              0x10340230UL
#define SMN_DEV0_FUNC0_NBIF1NBIO0_PCIE_BAR6_CNTL_ADDRESS              0x10240230UL
#define SMN_DEV0_FUNC0_NBIF1NBIO1_PCIE_BAR6_CNTL_ADDRESS              0x10440230UL
#define SMN_DEV0_FUNC0_NBIF2NBIO0_PCIE_BAR6_CNTL_ADDRESS              0x10540230UL
#define SMN_DEV0_FUNC0_NBIF2NBIO1_PCIE_BAR6_CNTL_ADDRESS              0x10740230UL
#define SMN_DEV1_FUNC0_NBIF0NBIO0_PCIE_BAR6_CNTL_ADDRESS              0x10148230UL
#define SMN_DEV1_FUNC0_NBIF0NBIO1_PCIE_BAR6_CNTL_ADDRESS              0x10348230UL
#define SMN_DEV1_FUNC0_NBIF1NBIO0_PCIE_BAR6_CNTL_ADDRESS              0x10248230UL
#define SMN_DEV1_FUNC0_NBIF1NBIO1_PCIE_BAR6_CNTL_ADDRESS              0x10448230UL


/***********************************************************
* Register Name : PCIE_BAR_ENH_CAP_LIST
************************************************************/

#define NBIF_BAR_ENH_CAP_LIST_CAP_ID_OFFSET                    0
#define NBIF_BAR_ENH_CAP_LIST_CAP_ID_MASK                      0xffff

#define NBIF_BAR_ENH_CAP_LIST_CAP_VER_OFFSET                   16
#define NBIF_BAR_ENH_CAP_LIST_CAP_VER_MASK                     0xf0000

#define NBIF_BAR_ENH_CAP_LIST_NEXT_PTR_OFFSET                  20
#define NBIF_BAR_ENH_CAP_LIST_NEXT_PTR_MASK                    0xfff00000

typedef union {
  struct {
    UINT32                                              CAP_ID:16;
    UINT32                                             CAP_VER:4;
    UINT32                                            NEXT_PTR:12;
  } Field;
  UINT32 Value;
} PCIE_BAR_ENH_CAP_LIST_STRUCT;

#define PCICFG_NBIFEPF0CFG_PCIE_BAR_ENH_CAP_LIST_OFFSET             0x200
#define SMN_DEV0_FUNC0_NBIF0NBIO0_PCIE_BAR_ENH_CAP_LIST_ADDRESS       0x10140200UL
#define SMN_DEV0_FUNC0_NBIF0NBIO1_PCIE_BAR_ENH_CAP_LIST_ADDRESS       0x10340200UL
#define SMN_DEV0_FUNC0_NBIF1NBIO0_PCIE_BAR_ENH_CAP_LIST_ADDRESS       0x10240200UL
#define SMN_DEV0_FUNC0_NBIF1NBIO1_PCIE_BAR_ENH_CAP_LIST_ADDRESS       0x10440200UL
#define SMN_DEV0_FUNC0_NBIF2NBIO0_PCIE_BAR_ENH_CAP_LIST_ADDRESS       0x10540200UL
#define SMN_DEV0_FUNC0_NBIF2NBIO1_PCIE_BAR_ENH_CAP_LIST_ADDRESS       0x10740200UL
#define SMN_DEV1_FUNC0_NBIF0NBIO0_PCIE_BAR_ENH_CAP_LIST_ADDRESS       0x10148200UL
#define SMN_DEV1_FUNC0_NBIF0NBIO1_PCIE_BAR_ENH_CAP_LIST_ADDRESS       0x10348200UL
#define SMN_DEV1_FUNC0_NBIF1NBIO0_PCIE_BAR_ENH_CAP_LIST_ADDRESS       0x10248200UL
#define SMN_DEV1_FUNC0_NBIF1NBIO1_PCIE_BAR_ENH_CAP_LIST_ADDRESS       0x10448200UL


/***********************************************************
* Register Name : PCIE_CAP
************************************************************/

#define NBIF_CAP_VERSION_OFFSET                                0
#define NBIF_CAP_VERSION_MASK                                  0xf

#define NBIF_CAP_DEVICE_TYPE_OFFSET                            4
#define NBIF_CAP_DEVICE_TYPE_MASK                              0xf0

#define NBIF_CAP_SLOT_IMPLEMENTED_OFFSET                       8
#define NBIF_CAP_SLOT_IMPLEMENTED_MASK                         0x100

#define NBIF_CAP_INT_MESSAGE_NUM_OFFSET                        9
#define NBIF_CAP_INT_MESSAGE_NUM_MASK                          0x3e00

#define NBIF_CAP_Reserved_15_14_OFFSET                         14
#define NBIF_CAP_Reserved_15_14_MASK                           0xc000

typedef union {
  struct {
    UINT16                                             VERSION:4;
    UINT16                                         DEVICE_TYPE:4;
    UINT16                                    SLOT_IMPLEMENTED:1;
    UINT16                                     INT_MESSAGE_NUM:5;
    UINT16                                      Reserved_15_14:2;
  } Field;
  UINT16 Value;
} PCIE_CAP_STRUCT;

#define PCICFG_NBIFEPF0CFG_PCIE_CAP_OFFSET                          0x66
#define SMN_DEV0_FUNC0_NBIF0NBIO0_PCIE_CAP_ADDRESS                    0x10140066UL
#define SMN_DEV0_FUNC0_NBIF0NBIO1_PCIE_CAP_ADDRESS                    0x10340066UL
#define SMN_DEV0_FUNC0_NBIF1NBIO0_PCIE_CAP_ADDRESS                    0x10240066UL
#define SMN_DEV0_FUNC0_NBIF1NBIO1_PCIE_CAP_ADDRESS                    0x10440066UL
#define SMN_DEV0_FUNC0_NBIF2NBIO0_PCIE_CAP_ADDRESS                    0x10540066UL
#define SMN_DEV0_FUNC0_NBIF2NBIO1_PCIE_CAP_ADDRESS                    0x10740066UL
#define SMN_DEV1_FUNC0_NBIF0NBIO0_PCIE_CAP_ADDRESS                    0x10148066UL
#define SMN_DEV1_FUNC0_NBIF0NBIO1_PCIE_CAP_ADDRESS                    0x10348066UL
#define SMN_DEV1_FUNC0_NBIF1NBIO0_PCIE_CAP_ADDRESS                    0x10248066UL
#define SMN_DEV1_FUNC0_NBIF1NBIO1_PCIE_CAP_ADDRESS                    0x10448066UL


/***********************************************************
* Register Name : PCIE_CAP_LIST
************************************************************/

#define NBIF_CAP_LIST_CAP_ID_OFFSET                            0
#define NBIF_CAP_LIST_CAP_ID_MASK                              0xff

#define NBIF_CAP_LIST_NEXT_PTR_OFFSET                          8
#define NBIF_CAP_LIST_NEXT_PTR_MASK                            0xff00

typedef union {
  struct {
    UINT16                                              CAP_ID:8;
    UINT16                                            NEXT_PTR:8;
  } Field;
  UINT16 Value;
} PCIE_CAP_LIST_STRUCT;

#define PCICFG_NBIFEPF0CFG_PCIE_CAP_LIST_OFFSET                     0x64
#define SMN_DEV0_FUNC0_NBIF0NBIO0_PCIE_CAP_LIST_ADDRESS               0x10140064UL
#define SMN_DEV0_FUNC0_NBIF0NBIO1_PCIE_CAP_LIST_ADDRESS               0x10340064UL
#define SMN_DEV0_FUNC0_NBIF1NBIO0_PCIE_CAP_LIST_ADDRESS               0x10240064UL
#define SMN_DEV0_FUNC0_NBIF1NBIO1_PCIE_CAP_LIST_ADDRESS               0x10440064UL
#define SMN_DEV0_FUNC0_NBIF2NBIO0_PCIE_CAP_LIST_ADDRESS               0x10540064UL
#define SMN_DEV0_FUNC0_NBIF2NBIO1_PCIE_CAP_LIST_ADDRESS               0x10740064UL
#define SMN_DEV1_FUNC0_NBIF0NBIO0_PCIE_CAP_LIST_ADDRESS               0x10148064UL
#define SMN_DEV1_FUNC0_NBIF0NBIO1_PCIE_CAP_LIST_ADDRESS               0x10348064UL
#define SMN_DEV1_FUNC0_NBIF1NBIO0_PCIE_CAP_LIST_ADDRESS               0x10248064UL
#define SMN_DEV1_FUNC0_NBIF1NBIO1_PCIE_CAP_LIST_ADDRESS               0x10448064UL


/***********************************************************
* Register Name : PCIE_CORR_ERR_MASK
************************************************************/

#define NBIF_CORR_ERR_MASK_RCV_ERR_MASK_OFFSET                 0
#define NBIF_CORR_ERR_MASK_RCV_ERR_MASK_MASK                   0x1

#define NBIF_CORR_ERR_MASK_Reserved_5_1_OFFSET                 1
#define NBIF_CORR_ERR_MASK_Reserved_5_1_MASK                   0x3e

#define NBIF_CORR_ERR_MASK_BAD_TLP_MASK_OFFSET                 6
#define NBIF_CORR_ERR_MASK_BAD_TLP_MASK_MASK                   0x40

#define NBIF_CORR_ERR_MASK_BAD_DLLP_MASK_OFFSET                7
#define NBIF_CORR_ERR_MASK_BAD_DLLP_MASK_MASK                  0x80

#define NBIF_CORR_ERR_MASK_REPLAY_NUM_ROLLOVER_MASK_OFFSET     8
#define NBIF_CORR_ERR_MASK_REPLAY_NUM_ROLLOVER_MASK_MASK       0x100

#define NBIF_CORR_ERR_MASK_Reserved_11_9_OFFSET                9
#define NBIF_CORR_ERR_MASK_Reserved_11_9_MASK                  0xe00

#define NBIF_CORR_ERR_MASK_REPLAY_TIMER_TIMEOUT_MASK_OFFSET    12
#define NBIF_CORR_ERR_MASK_REPLAY_TIMER_TIMEOUT_MASK_MASK      0x1000

#define NBIF_CORR_ERR_MASK_ADVISORY_NONFATAL_ERR_MASK_OFFSET   13
#define NBIF_CORR_ERR_MASK_ADVISORY_NONFATAL_ERR_MASK_MASK     0x2000

#define NBIF_CORR_ERR_MASK_CORR_INT_ERR_MASK_OFFSET            14
#define NBIF_CORR_ERR_MASK_CORR_INT_ERR_MASK_MASK              0x4000

#define NBIF_CORR_ERR_MASK_HDR_LOG_OVFL_MASK_OFFSET            15
#define NBIF_CORR_ERR_MASK_HDR_LOG_OVFL_MASK_MASK              0x8000

#define NBIF_CORR_ERR_MASK_Reserved_31_16_OFFSET               16
#define NBIF_CORR_ERR_MASK_Reserved_31_16_MASK                 0xffff0000

typedef union {
  struct {
    UINT32                                        RCV_ERR_MASK:1;
    UINT32                                        Reserved_5_1:5;
    UINT32                                        BAD_TLP_MASK:1;
    UINT32                                       BAD_DLLP_MASK:1;
    UINT32                            REPLAY_NUM_ROLLOVER_MASK:1;
    UINT32                                       Reserved_11_9:3;
    UINT32                           REPLAY_TIMER_TIMEOUT_MASK:1;
    UINT32                          ADVISORY_NONFATAL_ERR_MASK:1;
    UINT32                                   CORR_INT_ERR_MASK:1;
    UINT32                                   HDR_LOG_OVFL_MASK:1;
    UINT32                                      Reserved_31_16:16;
  } Field;
  UINT32 Value;
} PCIE_CORR_ERR_MASK_STRUCT;

#define PCICFG_NBIFEPF0CFG_PCIE_CORR_ERR_MASK_OFFSET                0x164
#define SMN_DEV0_FUNC0_NBIF0NBIO0_PCIE_CORR_ERR_MASK_ADDRESS          0x10140164UL
#define SMN_DEV0_FUNC0_NBIF0NBIO1_PCIE_CORR_ERR_MASK_ADDRESS          0x10340164UL
#define SMN_DEV0_FUNC0_NBIF1NBIO0_PCIE_CORR_ERR_MASK_ADDRESS          0x10240164UL
#define SMN_DEV0_FUNC0_NBIF1NBIO1_PCIE_CORR_ERR_MASK_ADDRESS          0x10440164UL
#define SMN_DEV0_FUNC0_NBIF2NBIO0_PCIE_CORR_ERR_MASK_ADDRESS          0x10540164UL
#define SMN_DEV0_FUNC0_NBIF2NBIO1_PCIE_CORR_ERR_MASK_ADDRESS          0x10740164UL
#define SMN_DEV1_FUNC0_NBIF0NBIO0_PCIE_CORR_ERR_MASK_ADDRESS          0x10148164UL
#define SMN_DEV1_FUNC0_NBIF0NBIO1_PCIE_CORR_ERR_MASK_ADDRESS          0x10348164UL
#define SMN_DEV1_FUNC0_NBIF1NBIO0_PCIE_CORR_ERR_MASK_ADDRESS          0x10248164UL
#define SMN_DEV1_FUNC0_NBIF1NBIO1_PCIE_CORR_ERR_MASK_ADDRESS          0x10448164UL


/***********************************************************
* Register Name : PCIE_CORR_ERR_STATUS
************************************************************/

#define NBIF_CORR_ERR_STATUS_RCV_ERR_STATUS_OFFSET             0
#define NBIF_CORR_ERR_STATUS_RCV_ERR_STATUS_MASK               0x1

#define NBIF_CORR_ERR_STATUS_Reserved_5_1_OFFSET               1
#define NBIF_CORR_ERR_STATUS_Reserved_5_1_MASK                 0x3e

#define NBIF_CORR_ERR_STATUS_BAD_TLP_STATUS_OFFSET             6
#define NBIF_CORR_ERR_STATUS_BAD_TLP_STATUS_MASK               0x40

#define NBIF_CORR_ERR_STATUS_BAD_DLLP_STATUS_OFFSET            7
#define NBIF_CORR_ERR_STATUS_BAD_DLLP_STATUS_MASK              0x80

#define NBIF_CORR_ERR_STATUS_REPLAY_NUM_ROLLOVER_STATUS_OFFSET 8
#define NBIF_CORR_ERR_STATUS_REPLAY_NUM_ROLLOVER_STATUS_MASK   0x100

#define NBIF_CORR_ERR_STATUS_Reserved_11_9_OFFSET              9
#define NBIF_CORR_ERR_STATUS_Reserved_11_9_MASK                0xe00

#define NBIF_CORR_ERR_STATUS_REPLAY_TIMER_TIMEOUT_STATUS_OFFSET 12
#define NBIF_CORR_ERR_STATUS_REPLAY_TIMER_TIMEOUT_STATUS_MASK  0x1000

#define NBIF_CORR_ERR_STATUS_ADVISORY_NONFATAL_ERR_STATUS_OFFSET 13
#define NBIF_CORR_ERR_STATUS_ADVISORY_NONFATAL_ERR_STATUS_MASK 0x2000

#define NBIF_CORR_ERR_STATUS_CORR_INT_ERR_STATUS_OFFSET        14
#define NBIF_CORR_ERR_STATUS_CORR_INT_ERR_STATUS_MASK          0x4000

#define NBIF_CORR_ERR_STATUS_HDR_LOG_OVFL_STATUS_OFFSET        15
#define NBIF_CORR_ERR_STATUS_HDR_LOG_OVFL_STATUS_MASK          0x8000

#define NBIF_CORR_ERR_STATUS_Reserved_31_16_OFFSET             16
#define NBIF_CORR_ERR_STATUS_Reserved_31_16_MASK               0xffff0000

typedef union {
  struct {
    UINT32                                      RCV_ERR_STATUS:1;
    UINT32                                        Reserved_5_1:5;
    UINT32                                      BAD_TLP_STATUS:1;
    UINT32                                     BAD_DLLP_STATUS:1;
    UINT32                          REPLAY_NUM_ROLLOVER_STATUS:1;
    UINT32                                       Reserved_11_9:3;
    UINT32                         REPLAY_TIMER_TIMEOUT_STATUS:1;
    UINT32                        ADVISORY_NONFATAL_ERR_STATUS:1;
    UINT32                                 CORR_INT_ERR_STATUS:1;
    UINT32                                 HDR_LOG_OVFL_STATUS:1;
    UINT32                                      Reserved_31_16:16;
  } Field;
  UINT32 Value;
} PCIE_CORR_ERR_STATUS_STRUCT;

#define PCICFG_NBIFEPF0CFG_PCIE_CORR_ERR_STATUS_OFFSET              0x160
#define SMN_DEV0_FUNC0_NBIF0NBIO0_PCIE_CORR_ERR_STATUS_ADDRESS        0x10140160UL
#define SMN_DEV0_FUNC0_NBIF0NBIO1_PCIE_CORR_ERR_STATUS_ADDRESS        0x10340160UL
#define SMN_DEV0_FUNC0_NBIF1NBIO0_PCIE_CORR_ERR_STATUS_ADDRESS        0x10240160UL
#define SMN_DEV0_FUNC0_NBIF1NBIO1_PCIE_CORR_ERR_STATUS_ADDRESS        0x10440160UL
#define SMN_DEV0_FUNC0_NBIF2NBIO0_PCIE_CORR_ERR_STATUS_ADDRESS        0x10540160UL
#define SMN_DEV0_FUNC0_NBIF2NBIO1_PCIE_CORR_ERR_STATUS_ADDRESS        0x10740160UL
#define SMN_DEV1_FUNC0_NBIF0NBIO0_PCIE_CORR_ERR_STATUS_ADDRESS        0x10148160UL
#define SMN_DEV1_FUNC0_NBIF0NBIO1_PCIE_CORR_ERR_STATUS_ADDRESS        0x10348160UL
#define SMN_DEV1_FUNC0_NBIF1NBIO0_PCIE_CORR_ERR_STATUS_ADDRESS        0x10248160UL
#define SMN_DEV1_FUNC0_NBIF1NBIO1_PCIE_CORR_ERR_STATUS_ADDRESS        0x10448160UL


/***********************************************************
* Register Name : PCIE_DLF_ENH_CAP_LIST
************************************************************/

#define NBIF_DLF_ENH_CAP_LIST_CAP_ID_OFFSET                    0
#define NBIF_DLF_ENH_CAP_LIST_CAP_ID_MASK                      0xffff

#define NBIF_DLF_ENH_CAP_LIST_CAP_VER_OFFSET                   16
#define NBIF_DLF_ENH_CAP_LIST_CAP_VER_MASK                     0xf0000

#define NBIF_DLF_ENH_CAP_LIST_NEXT_PTR_OFFSET                  20
#define NBIF_DLF_ENH_CAP_LIST_NEXT_PTR_MASK                    0xfff00000

typedef union {
  struct {
    UINT32                                              CAP_ID:16;
    UINT32                                             CAP_VER:4;
    UINT32                                            NEXT_PTR:12;
  } Field;
  UINT32 Value;
} PCIE_DLF_ENH_CAP_LIST_STRUCT;

#define PCICFG_NBIFEPF0CFG_PCIE_DLF_ENH_CAP_LIST_OFFSET             0x400
#define SMN_DEV0_FUNC0_NBIF0NBIO0_PCIE_DLF_ENH_CAP_LIST_ADDRESS       0x10140400UL
#define SMN_DEV0_FUNC0_NBIF0NBIO1_PCIE_DLF_ENH_CAP_LIST_ADDRESS       0x10340400UL
#define SMN_DEV0_FUNC0_NBIF1NBIO0_PCIE_DLF_ENH_CAP_LIST_ADDRESS       0x10240400UL
#define SMN_DEV0_FUNC0_NBIF1NBIO1_PCIE_DLF_ENH_CAP_LIST_ADDRESS       0x10440400UL
#define SMN_DEV0_FUNC0_NBIF2NBIO0_PCIE_DLF_ENH_CAP_LIST_ADDRESS       0x10540400UL
#define SMN_DEV0_FUNC0_NBIF2NBIO1_PCIE_DLF_ENH_CAP_LIST_ADDRESS       0x10740400UL
#define SMN_DEV1_FUNC0_NBIF0NBIO0_PCIE_DLF_ENH_CAP_LIST_ADDRESS       0x10148400UL
#define SMN_DEV1_FUNC0_NBIF0NBIO1_PCIE_DLF_ENH_CAP_LIST_ADDRESS       0x10348400UL
#define SMN_DEV1_FUNC0_NBIF1NBIO0_PCIE_DLF_ENH_CAP_LIST_ADDRESS       0x10248400UL
#define SMN_DEV1_FUNC0_NBIF1NBIO1_PCIE_DLF_ENH_CAP_LIST_ADDRESS       0x10448400UL


/***********************************************************
* Register Name : PCIE_DPA_CAP
************************************************************/

#define NBIF_DPA_CAP_SUBSTATE_MAX_OFFSET                       0
#define NBIF_DPA_CAP_SUBSTATE_MAX_MASK                         0x1f

#define NBIF_DPA_CAP_Reserved_7_5_OFFSET                       5
#define NBIF_DPA_CAP_Reserved_7_5_MASK                         0xe0

#define NBIF_DPA_CAP_TRANS_LAT_UNIT_OFFSET                     8
#define NBIF_DPA_CAP_TRANS_LAT_UNIT_MASK                       0x300

#define NBIF_DPA_CAP_Reserved_11_10_OFFSET                     10
#define NBIF_DPA_CAP_Reserved_11_10_MASK                       0xc00

#define NBIF_DPA_CAP_PWR_ALLOC_SCALE_OFFSET                    12
#define NBIF_DPA_CAP_PWR_ALLOC_SCALE_MASK                      0x3000

#define NBIF_DPA_CAP_Reserved_15_14_OFFSET                     14
#define NBIF_DPA_CAP_Reserved_15_14_MASK                       0xc000

#define NBIF_DPA_CAP_TRANS_LAT_VAL_0_OFFSET                    16
#define NBIF_DPA_CAP_TRANS_LAT_VAL_0_MASK                      0xff0000

#define NBIF_DPA_CAP_TRANS_LAT_VAL_1_OFFSET                    24
#define NBIF_DPA_CAP_TRANS_LAT_VAL_1_MASK                      0xff000000

typedef union {
  struct {
    UINT32                                        SUBSTATE_MAX:5;
    UINT32                                        Reserved_7_5:3;
    UINT32                                      TRANS_LAT_UNIT:2;
    UINT32                                      Reserved_11_10:2;
    UINT32                                     PWR_ALLOC_SCALE:2;
    UINT32                                      Reserved_15_14:2;
    UINT32                                     TRANS_LAT_VAL_0:8;
    UINT32                                     TRANS_LAT_VAL_1:8;
  } Field;
  UINT32 Value;
} PCIE_DPA_CAP_STRUCT;

#define PCICFG_NBIFEPF0CFG_PCIE_DPA_CAP_OFFSET                      0x254
#define SMN_DEV0_FUNC0_NBIF0NBIO0_PCIE_DPA_CAP_ADDRESS                0x10140254UL
#define SMN_DEV0_FUNC0_NBIF0NBIO1_PCIE_DPA_CAP_ADDRESS                0x10340254UL
#define SMN_DEV0_FUNC0_NBIF1NBIO0_PCIE_DPA_CAP_ADDRESS                0x10240254UL
#define SMN_DEV0_FUNC0_NBIF1NBIO1_PCIE_DPA_CAP_ADDRESS                0x10440254UL
#define SMN_DEV0_FUNC0_NBIF2NBIO0_PCIE_DPA_CAP_ADDRESS                0x10540254UL
#define SMN_DEV0_FUNC0_NBIF2NBIO1_PCIE_DPA_CAP_ADDRESS                0x10740254UL
#define SMN_DEV1_FUNC0_NBIF0NBIO0_PCIE_DPA_CAP_ADDRESS                0x10148254UL
#define SMN_DEV1_FUNC0_NBIF0NBIO1_PCIE_DPA_CAP_ADDRESS                0x10348254UL
#define SMN_DEV1_FUNC0_NBIF1NBIO0_PCIE_DPA_CAP_ADDRESS                0x10248254UL
#define SMN_DEV1_FUNC0_NBIF1NBIO1_PCIE_DPA_CAP_ADDRESS                0x10448254UL


/***********************************************************
* Register Name : PCIE_DPA_CNTL
************************************************************/

#define NBIF_DPA_CNTL_SUBSTATE_CNTL_OFFSET                     0
#define NBIF_DPA_CNTL_SUBSTATE_CNTL_MASK                       0x1f

#define NBIF_DPA_CNTL_Reserved_15_5_OFFSET                     5
#define NBIF_DPA_CNTL_Reserved_15_5_MASK                       0xffe0

typedef union {
  struct {
    UINT16                                       SUBSTATE_CNTL:5;
    UINT16                                       Reserved_15_5:11;
  } Field;
  UINT16 Value;
} PCIE_DPA_CNTL_STRUCT;

#define PCICFG_NBIFEPF0CFG_PCIE_DPA_CNTL_OFFSET                     0x25e
#define SMN_DEV0_FUNC0_NBIF0NBIO0_PCIE_DPA_CNTL_ADDRESS               0x1014025eUL
#define SMN_DEV0_FUNC0_NBIF0NBIO1_PCIE_DPA_CNTL_ADDRESS               0x1034025eUL
#define SMN_DEV0_FUNC0_NBIF1NBIO0_PCIE_DPA_CNTL_ADDRESS               0x1024025eUL
#define SMN_DEV0_FUNC0_NBIF1NBIO1_PCIE_DPA_CNTL_ADDRESS               0x1044025eUL
#define SMN_DEV0_FUNC0_NBIF2NBIO0_PCIE_DPA_CNTL_ADDRESS               0x1054025eUL
#define SMN_DEV0_FUNC0_NBIF2NBIO1_PCIE_DPA_CNTL_ADDRESS               0x1074025eUL
#define SMN_DEV1_FUNC0_NBIF0NBIO0_PCIE_DPA_CNTL_ADDRESS               0x1014825eUL
#define SMN_DEV1_FUNC0_NBIF0NBIO1_PCIE_DPA_CNTL_ADDRESS               0x1034825eUL
#define SMN_DEV1_FUNC0_NBIF1NBIO0_PCIE_DPA_CNTL_ADDRESS               0x1024825eUL
#define SMN_DEV1_FUNC0_NBIF1NBIO1_PCIE_DPA_CNTL_ADDRESS               0x1044825eUL


/***********************************************************
* Register Name : PCIE_DPA_ENH_CAP_LIST
************************************************************/

#define NBIF_DPA_ENH_CAP_LIST_CAP_ID_OFFSET                    0
#define NBIF_DPA_ENH_CAP_LIST_CAP_ID_MASK                      0xffff

#define NBIF_DPA_ENH_CAP_LIST_CAP_VER_OFFSET                   16
#define NBIF_DPA_ENH_CAP_LIST_CAP_VER_MASK                     0xf0000

#define NBIF_DPA_ENH_CAP_LIST_NEXT_PTR_OFFSET                  20
#define NBIF_DPA_ENH_CAP_LIST_NEXT_PTR_MASK                    0xfff00000

typedef union {
  struct {
    UINT32                                              CAP_ID:16;
    UINT32                                             CAP_VER:4;
    UINT32                                            NEXT_PTR:12;
  } Field;
  UINT32 Value;
} PCIE_DPA_ENH_CAP_LIST_STRUCT;

#define PCICFG_NBIFEPF0CFG_PCIE_DPA_ENH_CAP_LIST_OFFSET             0x250
#define SMN_DEV0_FUNC0_NBIF0NBIO0_PCIE_DPA_ENH_CAP_LIST_ADDRESS       0x10140250UL
#define SMN_DEV0_FUNC0_NBIF0NBIO1_PCIE_DPA_ENH_CAP_LIST_ADDRESS       0x10340250UL
#define SMN_DEV0_FUNC0_NBIF1NBIO0_PCIE_DPA_ENH_CAP_LIST_ADDRESS       0x10240250UL
#define SMN_DEV0_FUNC0_NBIF1NBIO1_PCIE_DPA_ENH_CAP_LIST_ADDRESS       0x10440250UL
#define SMN_DEV0_FUNC0_NBIF2NBIO0_PCIE_DPA_ENH_CAP_LIST_ADDRESS       0x10540250UL
#define SMN_DEV0_FUNC0_NBIF2NBIO1_PCIE_DPA_ENH_CAP_LIST_ADDRESS       0x10740250UL
#define SMN_DEV1_FUNC0_NBIF0NBIO0_PCIE_DPA_ENH_CAP_LIST_ADDRESS       0x10148250UL
#define SMN_DEV1_FUNC0_NBIF0NBIO1_PCIE_DPA_ENH_CAP_LIST_ADDRESS       0x10348250UL
#define SMN_DEV1_FUNC0_NBIF1NBIO0_PCIE_DPA_ENH_CAP_LIST_ADDRESS       0x10248250UL
#define SMN_DEV1_FUNC0_NBIF1NBIO1_PCIE_DPA_ENH_CAP_LIST_ADDRESS       0x10448250UL


/***********************************************************
* Register Name : PCIE_DPA_LATENCY_INDICATOR
************************************************************/

#define NBIF_DPA_LATENCY_INDICATOR_TRANS_LAT_INDICATOR_BITS_OFFSET 0
#define NBIF_DPA_LATENCY_INDICATOR_TRANS_LAT_INDICATOR_BITS_MASK 0xff

#define NBIF_DPA_LATENCY_INDICATOR_Reserved_31_8_OFFSET        8
#define NBIF_DPA_LATENCY_INDICATOR_Reserved_31_8_MASK          0xffffff00

typedef union {
  struct {
    UINT32                            TRANS_LAT_INDICATOR_BITS:8;
    UINT32                                       Reserved_31_8:24;
  } Field;
  UINT32 Value;
} PCIE_DPA_LATENCY_INDICATOR_STRUCT;

#define PCICFG_NBIFEPF0CFG_PCIE_DPA_LATENCY_INDICATOR_OFFSET        0x258
#define SMN_DEV0_FUNC0_NBIF0NBIO0_PCIE_DPA_LATENCY_INDICATOR_ADDRESS  0x10140258UL
#define SMN_DEV0_FUNC0_NBIF0NBIO1_PCIE_DPA_LATENCY_INDICATOR_ADDRESS  0x10340258UL
#define SMN_DEV0_FUNC0_NBIF1NBIO0_PCIE_DPA_LATENCY_INDICATOR_ADDRESS  0x10240258UL
#define SMN_DEV0_FUNC0_NBIF1NBIO1_PCIE_DPA_LATENCY_INDICATOR_ADDRESS  0x10440258UL
#define SMN_DEV0_FUNC0_NBIF2NBIO0_PCIE_DPA_LATENCY_INDICATOR_ADDRESS  0x10540258UL
#define SMN_DEV0_FUNC0_NBIF2NBIO1_PCIE_DPA_LATENCY_INDICATOR_ADDRESS  0x10740258UL
#define SMN_DEV1_FUNC0_NBIF0NBIO0_PCIE_DPA_LATENCY_INDICATOR_ADDRESS  0x10148258UL
#define SMN_DEV1_FUNC0_NBIF0NBIO1_PCIE_DPA_LATENCY_INDICATOR_ADDRESS  0x10348258UL
#define SMN_DEV1_FUNC0_NBIF1NBIO0_PCIE_DPA_LATENCY_INDICATOR_ADDRESS  0x10248258UL
#define SMN_DEV1_FUNC0_NBIF1NBIO1_PCIE_DPA_LATENCY_INDICATOR_ADDRESS  0x10448258UL


/***********************************************************
* Register Name : PCIE_DPA_STATUS
************************************************************/

#define NBIF_DPA_STATUS_SUBSTATE_STATUS_OFFSET                 0
#define NBIF_DPA_STATUS_SUBSTATE_STATUS_MASK                   0x1f

#define NBIF_DPA_STATUS_Reserved_7_5_OFFSET                    5
#define NBIF_DPA_STATUS_Reserved_7_5_MASK                      0xe0

#define NBIF_DPA_STATUS_SUBSTATE_CNTL_ENABLED_OFFSET           8
#define NBIF_DPA_STATUS_SUBSTATE_CNTL_ENABLED_MASK             0x100

#define NBIF_DPA_STATUS_Reserved_15_9_OFFSET                   9
#define NBIF_DPA_STATUS_Reserved_15_9_MASK                     0xfe00

typedef union {
  struct {
    UINT16                                     SUBSTATE_STATUS:5;
    UINT16                                        Reserved_7_5:3;
    UINT16                               SUBSTATE_CNTL_ENABLED:1;
    UINT16                                       Reserved_15_9:7;
  } Field;
  UINT16 Value;
} PCIE_DPA_STATUS_STRUCT;

#define PCICFG_NBIFEPF0CFG_PCIE_DPA_STATUS_OFFSET                   0x25c
#define SMN_DEV0_FUNC0_NBIF0NBIO0_PCIE_DPA_STATUS_ADDRESS             0x1014025cUL
#define SMN_DEV0_FUNC0_NBIF0NBIO1_PCIE_DPA_STATUS_ADDRESS             0x1034025cUL
#define SMN_DEV0_FUNC0_NBIF1NBIO0_PCIE_DPA_STATUS_ADDRESS             0x1024025cUL
#define SMN_DEV0_FUNC0_NBIF1NBIO1_PCIE_DPA_STATUS_ADDRESS             0x1044025cUL
#define SMN_DEV0_FUNC0_NBIF2NBIO0_PCIE_DPA_STATUS_ADDRESS             0x1054025cUL
#define SMN_DEV0_FUNC0_NBIF2NBIO1_PCIE_DPA_STATUS_ADDRESS             0x1074025cUL
#define SMN_DEV1_FUNC0_NBIF0NBIO0_PCIE_DPA_STATUS_ADDRESS             0x1014825cUL
#define SMN_DEV1_FUNC0_NBIF0NBIO1_PCIE_DPA_STATUS_ADDRESS             0x1034825cUL
#define SMN_DEV1_FUNC0_NBIF1NBIO0_PCIE_DPA_STATUS_ADDRESS             0x1024825cUL
#define SMN_DEV1_FUNC0_NBIF1NBIO1_PCIE_DPA_STATUS_ADDRESS             0x1044825cUL


/***********************************************************
* Register Name : PCIE_DPA_SUBSTATE_PWR_ALLOC_0
************************************************************/

#define NBIF_DPA_SUBSTATE_PWR_ALLOC_0_SUBSTATE_PWR_ALLOC_OFFSET 0
#define NBIF_DPA_SUBSTATE_PWR_ALLOC_0_SUBSTATE_PWR_ALLOC_MASK  0xff

typedef union {
  struct {
    UINT8                                  SUBSTATE_PWR_ALLOC:8;
  } Field;
  UINT8 Value;
} PCIE_DPA_SUBSTATE_PWR_ALLOC_0_STRUCT;

#define PCICFG_NBIFEPF0CFG_PCIE_DPA_SUBSTATE_PWR_ALLOC_0_OFFSET     0x260
#define SMN_DEV0_FUNC0_NBIF0NBIO0_PCIE_DPA_SUBSTATE_PWR_ALLOC_0_ADDRESS 0x10140260UL
#define SMN_DEV0_FUNC0_NBIF0NBIO1_PCIE_DPA_SUBSTATE_PWR_ALLOC_0_ADDRESS 0x10340260UL
#define SMN_DEV0_FUNC0_NBIF1NBIO0_PCIE_DPA_SUBSTATE_PWR_ALLOC_0_ADDRESS 0x10240260UL
#define SMN_DEV0_FUNC0_NBIF1NBIO1_PCIE_DPA_SUBSTATE_PWR_ALLOC_0_ADDRESS 0x10440260UL
#define SMN_DEV0_FUNC0_NBIF2NBIO0_PCIE_DPA_SUBSTATE_PWR_ALLOC_0_ADDRESS 0x10540260UL
#define SMN_DEV0_FUNC0_NBIF2NBIO1_PCIE_DPA_SUBSTATE_PWR_ALLOC_0_ADDRESS 0x10740260UL
#define SMN_DEV1_FUNC0_NBIF0NBIO0_PCIE_DPA_SUBSTATE_PWR_ALLOC_0_ADDRESS 0x10148260UL
#define SMN_DEV1_FUNC0_NBIF0NBIO1_PCIE_DPA_SUBSTATE_PWR_ALLOC_0_ADDRESS 0x10348260UL
#define SMN_DEV1_FUNC0_NBIF1NBIO0_PCIE_DPA_SUBSTATE_PWR_ALLOC_0_ADDRESS 0x10248260UL
#define SMN_DEV1_FUNC0_NBIF1NBIO1_PCIE_DPA_SUBSTATE_PWR_ALLOC_0_ADDRESS 0x10448260UL


/***********************************************************
* Register Name : PCIE_DPA_SUBSTATE_PWR_ALLOC_1
************************************************************/

#define NBIF_DPA_SUBSTATE_PWR_ALLOC_1_SUBSTATE_PWR_ALLOC_OFFSET 0
#define NBIF_DPA_SUBSTATE_PWR_ALLOC_1_SUBSTATE_PWR_ALLOC_MASK  0xff

typedef union {
  struct {
    UINT8                                  SUBSTATE_PWR_ALLOC:8;
  } Field;
  UINT8 Value;
} PCIE_DPA_SUBSTATE_PWR_ALLOC_1_STRUCT;

#define PCICFG_NBIFEPF0CFG_PCIE_DPA_SUBSTATE_PWR_ALLOC_1_OFFSET     0x261
#define SMN_DEV0_FUNC0_NBIF0NBIO0_PCIE_DPA_SUBSTATE_PWR_ALLOC_1_ADDRESS 0x10140261UL
#define SMN_DEV0_FUNC0_NBIF0NBIO1_PCIE_DPA_SUBSTATE_PWR_ALLOC_1_ADDRESS 0x10340261UL
#define SMN_DEV0_FUNC0_NBIF1NBIO0_PCIE_DPA_SUBSTATE_PWR_ALLOC_1_ADDRESS 0x10240261UL
#define SMN_DEV0_FUNC0_NBIF1NBIO1_PCIE_DPA_SUBSTATE_PWR_ALLOC_1_ADDRESS 0x10440261UL
#define SMN_DEV0_FUNC0_NBIF2NBIO0_PCIE_DPA_SUBSTATE_PWR_ALLOC_1_ADDRESS 0x10540261UL
#define SMN_DEV0_FUNC0_NBIF2NBIO1_PCIE_DPA_SUBSTATE_PWR_ALLOC_1_ADDRESS 0x10740261UL
#define SMN_DEV1_FUNC0_NBIF0NBIO0_PCIE_DPA_SUBSTATE_PWR_ALLOC_1_ADDRESS 0x10148261UL
#define SMN_DEV1_FUNC0_NBIF0NBIO1_PCIE_DPA_SUBSTATE_PWR_ALLOC_1_ADDRESS 0x10348261UL
#define SMN_DEV1_FUNC0_NBIF1NBIO0_PCIE_DPA_SUBSTATE_PWR_ALLOC_1_ADDRESS 0x10248261UL
#define SMN_DEV1_FUNC0_NBIF1NBIO1_PCIE_DPA_SUBSTATE_PWR_ALLOC_1_ADDRESS 0x10448261UL


/***********************************************************
* Register Name : PCIE_DPA_SUBSTATE_PWR_ALLOC_2
************************************************************/

#define NBIF_DPA_SUBSTATE_PWR_ALLOC_2_SUBSTATE_PWR_ALLOC_OFFSET 0
#define NBIF_DPA_SUBSTATE_PWR_ALLOC_2_SUBSTATE_PWR_ALLOC_MASK  0xff

typedef union {
  struct {
    UINT8                                  SUBSTATE_PWR_ALLOC:8;
  } Field;
  UINT8 Value;
} PCIE_DPA_SUBSTATE_PWR_ALLOC_2_STRUCT;

#define PCICFG_NBIFEPF0CFG_PCIE_DPA_SUBSTATE_PWR_ALLOC_2_OFFSET     0x262
#define SMN_DEV0_FUNC0_NBIF0NBIO0_PCIE_DPA_SUBSTATE_PWR_ALLOC_2_ADDRESS 0x10140262UL
#define SMN_DEV0_FUNC0_NBIF0NBIO1_PCIE_DPA_SUBSTATE_PWR_ALLOC_2_ADDRESS 0x10340262UL
#define SMN_DEV0_FUNC0_NBIF1NBIO0_PCIE_DPA_SUBSTATE_PWR_ALLOC_2_ADDRESS 0x10240262UL
#define SMN_DEV0_FUNC0_NBIF1NBIO1_PCIE_DPA_SUBSTATE_PWR_ALLOC_2_ADDRESS 0x10440262UL
#define SMN_DEV0_FUNC0_NBIF2NBIO0_PCIE_DPA_SUBSTATE_PWR_ALLOC_2_ADDRESS 0x10540262UL
#define SMN_DEV0_FUNC0_NBIF2NBIO1_PCIE_DPA_SUBSTATE_PWR_ALLOC_2_ADDRESS 0x10740262UL
#define SMN_DEV1_FUNC0_NBIF0NBIO0_PCIE_DPA_SUBSTATE_PWR_ALLOC_2_ADDRESS 0x10148262UL
#define SMN_DEV1_FUNC0_NBIF0NBIO1_PCIE_DPA_SUBSTATE_PWR_ALLOC_2_ADDRESS 0x10348262UL
#define SMN_DEV1_FUNC0_NBIF1NBIO0_PCIE_DPA_SUBSTATE_PWR_ALLOC_2_ADDRESS 0x10248262UL
#define SMN_DEV1_FUNC0_NBIF1NBIO1_PCIE_DPA_SUBSTATE_PWR_ALLOC_2_ADDRESS 0x10448262UL


/***********************************************************
* Register Name : PCIE_DPA_SUBSTATE_PWR_ALLOC_3
************************************************************/

#define NBIF_DPA_SUBSTATE_PWR_ALLOC_3_SUBSTATE_PWR_ALLOC_OFFSET 0
#define NBIF_DPA_SUBSTATE_PWR_ALLOC_3_SUBSTATE_PWR_ALLOC_MASK  0xff

typedef union {
  struct {
    UINT8                                  SUBSTATE_PWR_ALLOC:8;
  } Field;
  UINT8 Value;
} PCIE_DPA_SUBSTATE_PWR_ALLOC_3_STRUCT;

#define PCICFG_NBIFEPF0CFG_PCIE_DPA_SUBSTATE_PWR_ALLOC_3_OFFSET     0x263
#define SMN_DEV0_FUNC0_NBIF0NBIO0_PCIE_DPA_SUBSTATE_PWR_ALLOC_3_ADDRESS 0x10140263UL
#define SMN_DEV0_FUNC0_NBIF0NBIO1_PCIE_DPA_SUBSTATE_PWR_ALLOC_3_ADDRESS 0x10340263UL
#define SMN_DEV0_FUNC0_NBIF1NBIO0_PCIE_DPA_SUBSTATE_PWR_ALLOC_3_ADDRESS 0x10240263UL
#define SMN_DEV0_FUNC0_NBIF1NBIO1_PCIE_DPA_SUBSTATE_PWR_ALLOC_3_ADDRESS 0x10440263UL
#define SMN_DEV0_FUNC0_NBIF2NBIO0_PCIE_DPA_SUBSTATE_PWR_ALLOC_3_ADDRESS 0x10540263UL
#define SMN_DEV0_FUNC0_NBIF2NBIO1_PCIE_DPA_SUBSTATE_PWR_ALLOC_3_ADDRESS 0x10740263UL
#define SMN_DEV1_FUNC0_NBIF0NBIO0_PCIE_DPA_SUBSTATE_PWR_ALLOC_3_ADDRESS 0x10148263UL
#define SMN_DEV1_FUNC0_NBIF0NBIO1_PCIE_DPA_SUBSTATE_PWR_ALLOC_3_ADDRESS 0x10348263UL
#define SMN_DEV1_FUNC0_NBIF1NBIO0_PCIE_DPA_SUBSTATE_PWR_ALLOC_3_ADDRESS 0x10248263UL
#define SMN_DEV1_FUNC0_NBIF1NBIO1_PCIE_DPA_SUBSTATE_PWR_ALLOC_3_ADDRESS 0x10448263UL


/***********************************************************
* Register Name : PCIE_DPA_SUBSTATE_PWR_ALLOC_4
************************************************************/

#define NBIF_DPA_SUBSTATE_PWR_ALLOC_4_SUBSTATE_PWR_ALLOC_OFFSET 0
#define NBIF_DPA_SUBSTATE_PWR_ALLOC_4_SUBSTATE_PWR_ALLOC_MASK  0xff

typedef union {
  struct {
    UINT8                                  SUBSTATE_PWR_ALLOC:8;
  } Field;
  UINT8 Value;
} PCIE_DPA_SUBSTATE_PWR_ALLOC_4_STRUCT;

#define PCICFG_NBIFEPF0CFG_PCIE_DPA_SUBSTATE_PWR_ALLOC_4_OFFSET     0x264
#define SMN_DEV0_FUNC0_NBIF0NBIO0_PCIE_DPA_SUBSTATE_PWR_ALLOC_4_ADDRESS 0x10140264UL
#define SMN_DEV0_FUNC0_NBIF0NBIO1_PCIE_DPA_SUBSTATE_PWR_ALLOC_4_ADDRESS 0x10340264UL
#define SMN_DEV0_FUNC0_NBIF1NBIO0_PCIE_DPA_SUBSTATE_PWR_ALLOC_4_ADDRESS 0x10240264UL
#define SMN_DEV0_FUNC0_NBIF1NBIO1_PCIE_DPA_SUBSTATE_PWR_ALLOC_4_ADDRESS 0x10440264UL
#define SMN_DEV0_FUNC0_NBIF2NBIO0_PCIE_DPA_SUBSTATE_PWR_ALLOC_4_ADDRESS 0x10540264UL
#define SMN_DEV0_FUNC0_NBIF2NBIO1_PCIE_DPA_SUBSTATE_PWR_ALLOC_4_ADDRESS 0x10740264UL
#define SMN_DEV1_FUNC0_NBIF0NBIO0_PCIE_DPA_SUBSTATE_PWR_ALLOC_4_ADDRESS 0x10148264UL
#define SMN_DEV1_FUNC0_NBIF0NBIO1_PCIE_DPA_SUBSTATE_PWR_ALLOC_4_ADDRESS 0x10348264UL
#define SMN_DEV1_FUNC0_NBIF1NBIO0_PCIE_DPA_SUBSTATE_PWR_ALLOC_4_ADDRESS 0x10248264UL
#define SMN_DEV1_FUNC0_NBIF1NBIO1_PCIE_DPA_SUBSTATE_PWR_ALLOC_4_ADDRESS 0x10448264UL


/***********************************************************
* Register Name : PCIE_DPA_SUBSTATE_PWR_ALLOC_5
************************************************************/

#define NBIF_DPA_SUBSTATE_PWR_ALLOC_5_SUBSTATE_PWR_ALLOC_OFFSET 0
#define NBIF_DPA_SUBSTATE_PWR_ALLOC_5_SUBSTATE_PWR_ALLOC_MASK  0xff

typedef union {
  struct {
    UINT8                                  SUBSTATE_PWR_ALLOC:8;
  } Field;
  UINT8 Value;
} PCIE_DPA_SUBSTATE_PWR_ALLOC_5_STRUCT;

#define PCICFG_NBIFEPF0CFG_PCIE_DPA_SUBSTATE_PWR_ALLOC_5_OFFSET     0x265
#define SMN_DEV0_FUNC0_NBIF0NBIO0_PCIE_DPA_SUBSTATE_PWR_ALLOC_5_ADDRESS 0x10140265UL
#define SMN_DEV0_FUNC0_NBIF0NBIO1_PCIE_DPA_SUBSTATE_PWR_ALLOC_5_ADDRESS 0x10340265UL
#define SMN_DEV0_FUNC0_NBIF1NBIO0_PCIE_DPA_SUBSTATE_PWR_ALLOC_5_ADDRESS 0x10240265UL
#define SMN_DEV0_FUNC0_NBIF1NBIO1_PCIE_DPA_SUBSTATE_PWR_ALLOC_5_ADDRESS 0x10440265UL
#define SMN_DEV0_FUNC0_NBIF2NBIO0_PCIE_DPA_SUBSTATE_PWR_ALLOC_5_ADDRESS 0x10540265UL
#define SMN_DEV0_FUNC0_NBIF2NBIO1_PCIE_DPA_SUBSTATE_PWR_ALLOC_5_ADDRESS 0x10740265UL
#define SMN_DEV1_FUNC0_NBIF0NBIO0_PCIE_DPA_SUBSTATE_PWR_ALLOC_5_ADDRESS 0x10148265UL
#define SMN_DEV1_FUNC0_NBIF0NBIO1_PCIE_DPA_SUBSTATE_PWR_ALLOC_5_ADDRESS 0x10348265UL
#define SMN_DEV1_FUNC0_NBIF1NBIO0_PCIE_DPA_SUBSTATE_PWR_ALLOC_5_ADDRESS 0x10248265UL
#define SMN_DEV1_FUNC0_NBIF1NBIO1_PCIE_DPA_SUBSTATE_PWR_ALLOC_5_ADDRESS 0x10448265UL


/***********************************************************
* Register Name : PCIE_DPA_SUBSTATE_PWR_ALLOC_6
************************************************************/

#define NBIF_DPA_SUBSTATE_PWR_ALLOC_6_SUBSTATE_PWR_ALLOC_OFFSET 0
#define NBIF_DPA_SUBSTATE_PWR_ALLOC_6_SUBSTATE_PWR_ALLOC_MASK  0xff

typedef union {
  struct {
    UINT8                                  SUBSTATE_PWR_ALLOC:8;
  } Field;
  UINT8 Value;
} PCIE_DPA_SUBSTATE_PWR_ALLOC_6_STRUCT;

#define PCICFG_NBIFEPF0CFG_PCIE_DPA_SUBSTATE_PWR_ALLOC_6_OFFSET     0x266
#define SMN_DEV0_FUNC0_NBIF0NBIO0_PCIE_DPA_SUBSTATE_PWR_ALLOC_6_ADDRESS 0x10140266UL
#define SMN_DEV0_FUNC0_NBIF0NBIO1_PCIE_DPA_SUBSTATE_PWR_ALLOC_6_ADDRESS 0x10340266UL
#define SMN_DEV0_FUNC0_NBIF1NBIO0_PCIE_DPA_SUBSTATE_PWR_ALLOC_6_ADDRESS 0x10240266UL
#define SMN_DEV0_FUNC0_NBIF1NBIO1_PCIE_DPA_SUBSTATE_PWR_ALLOC_6_ADDRESS 0x10440266UL
#define SMN_DEV0_FUNC0_NBIF2NBIO0_PCIE_DPA_SUBSTATE_PWR_ALLOC_6_ADDRESS 0x10540266UL
#define SMN_DEV0_FUNC0_NBIF2NBIO1_PCIE_DPA_SUBSTATE_PWR_ALLOC_6_ADDRESS 0x10740266UL
#define SMN_DEV1_FUNC0_NBIF0NBIO0_PCIE_DPA_SUBSTATE_PWR_ALLOC_6_ADDRESS 0x10148266UL
#define SMN_DEV1_FUNC0_NBIF0NBIO1_PCIE_DPA_SUBSTATE_PWR_ALLOC_6_ADDRESS 0x10348266UL
#define SMN_DEV1_FUNC0_NBIF1NBIO0_PCIE_DPA_SUBSTATE_PWR_ALLOC_6_ADDRESS 0x10248266UL
#define SMN_DEV1_FUNC0_NBIF1NBIO1_PCIE_DPA_SUBSTATE_PWR_ALLOC_6_ADDRESS 0x10448266UL


/***********************************************************
* Register Name : PCIE_DPA_SUBSTATE_PWR_ALLOC_7
************************************************************/

#define NBIF_DPA_SUBSTATE_PWR_ALLOC_7_SUBSTATE_PWR_ALLOC_OFFSET 0
#define NBIF_DPA_SUBSTATE_PWR_ALLOC_7_SUBSTATE_PWR_ALLOC_MASK  0xff

typedef union {
  struct {
    UINT8                                  SUBSTATE_PWR_ALLOC:8;
  } Field;
  UINT8 Value;
} PCIE_DPA_SUBSTATE_PWR_ALLOC_7_STRUCT;

#define PCICFG_NBIFEPF0CFG_PCIE_DPA_SUBSTATE_PWR_ALLOC_7_OFFSET     0x267
#define SMN_DEV0_FUNC0_NBIF0NBIO0_PCIE_DPA_SUBSTATE_PWR_ALLOC_7_ADDRESS 0x10140267UL
#define SMN_DEV0_FUNC0_NBIF0NBIO1_PCIE_DPA_SUBSTATE_PWR_ALLOC_7_ADDRESS 0x10340267UL
#define SMN_DEV0_FUNC0_NBIF1NBIO0_PCIE_DPA_SUBSTATE_PWR_ALLOC_7_ADDRESS 0x10240267UL
#define SMN_DEV0_FUNC0_NBIF1NBIO1_PCIE_DPA_SUBSTATE_PWR_ALLOC_7_ADDRESS 0x10440267UL
#define SMN_DEV0_FUNC0_NBIF2NBIO0_PCIE_DPA_SUBSTATE_PWR_ALLOC_7_ADDRESS 0x10540267UL
#define SMN_DEV0_FUNC0_NBIF2NBIO1_PCIE_DPA_SUBSTATE_PWR_ALLOC_7_ADDRESS 0x10740267UL
#define SMN_DEV1_FUNC0_NBIF0NBIO0_PCIE_DPA_SUBSTATE_PWR_ALLOC_7_ADDRESS 0x10148267UL
#define SMN_DEV1_FUNC0_NBIF0NBIO1_PCIE_DPA_SUBSTATE_PWR_ALLOC_7_ADDRESS 0x10348267UL
#define SMN_DEV1_FUNC0_NBIF1NBIO0_PCIE_DPA_SUBSTATE_PWR_ALLOC_7_ADDRESS 0x10248267UL
#define SMN_DEV1_FUNC0_NBIF1NBIO1_PCIE_DPA_SUBSTATE_PWR_ALLOC_7_ADDRESS 0x10448267UL


/***********************************************************
* Register Name : PCIE_HDR_LOG0
************************************************************/

#define NBIF_HDR_LOG0_TLP_HDR_OFFSET                           0
#define NBIF_HDR_LOG0_TLP_HDR_MASK                             0xffffffff

typedef union {
  struct {
    UINT32                                             TLP_HDR:32;
  } Field;
  UINT32 Value;
} PCIE_HDR_LOG0_STRUCT;

#define PCICFG_NBIFEPF0CFG_PCIE_HDR_LOG0_OFFSET                     0x16c
#define SMN_DEV0_FUNC0_NBIF0NBIO0_PCIE_HDR_LOG0_ADDRESS               0x1014016cUL
#define SMN_DEV0_FUNC0_NBIF0NBIO1_PCIE_HDR_LOG0_ADDRESS               0x1034016cUL
#define SMN_DEV0_FUNC0_NBIF1NBIO0_PCIE_HDR_LOG0_ADDRESS               0x1024016cUL
#define SMN_DEV0_FUNC0_NBIF1NBIO1_PCIE_HDR_LOG0_ADDRESS               0x1044016cUL
#define SMN_DEV0_FUNC0_NBIF2NBIO0_PCIE_HDR_LOG0_ADDRESS               0x1054016cUL
#define SMN_DEV0_FUNC0_NBIF2NBIO1_PCIE_HDR_LOG0_ADDRESS               0x1074016cUL
#define SMN_DEV1_FUNC0_NBIF0NBIO0_PCIE_HDR_LOG0_ADDRESS               0x1014816cUL
#define SMN_DEV1_FUNC0_NBIF0NBIO1_PCIE_HDR_LOG0_ADDRESS               0x1034816cUL
#define SMN_DEV1_FUNC0_NBIF1NBIO0_PCIE_HDR_LOG0_ADDRESS               0x1024816cUL
#define SMN_DEV1_FUNC0_NBIF1NBIO1_PCIE_HDR_LOG0_ADDRESS               0x1044816cUL


/***********************************************************
* Register Name : PCIE_HDR_LOG1
************************************************************/

#define NBIF_HDR_LOG1_TLP_HDR_OFFSET                           0
#define NBIF_HDR_LOG1_TLP_HDR_MASK                             0xffffffff

typedef union {
  struct {
    UINT32                                             TLP_HDR:32;
  } Field;
  UINT32 Value;
} PCIE_HDR_LOG1_STRUCT;

#define PCICFG_NBIFEPF0CFG_PCIE_HDR_LOG1_OFFSET                     0x170
#define SMN_DEV0_FUNC0_NBIF0NBIO0_PCIE_HDR_LOG1_ADDRESS               0x10140170UL
#define SMN_DEV0_FUNC0_NBIF0NBIO1_PCIE_HDR_LOG1_ADDRESS               0x10340170UL
#define SMN_DEV0_FUNC0_NBIF1NBIO0_PCIE_HDR_LOG1_ADDRESS               0x10240170UL
#define SMN_DEV0_FUNC0_NBIF1NBIO1_PCIE_HDR_LOG1_ADDRESS               0x10440170UL
#define SMN_DEV0_FUNC0_NBIF2NBIO0_PCIE_HDR_LOG1_ADDRESS               0x10540170UL
#define SMN_DEV0_FUNC0_NBIF2NBIO1_PCIE_HDR_LOG1_ADDRESS               0x10740170UL
#define SMN_DEV1_FUNC0_NBIF0NBIO0_PCIE_HDR_LOG1_ADDRESS               0x10148170UL
#define SMN_DEV1_FUNC0_NBIF0NBIO1_PCIE_HDR_LOG1_ADDRESS               0x10348170UL
#define SMN_DEV1_FUNC0_NBIF1NBIO0_PCIE_HDR_LOG1_ADDRESS               0x10248170UL
#define SMN_DEV1_FUNC0_NBIF1NBIO1_PCIE_HDR_LOG1_ADDRESS               0x10448170UL


/***********************************************************
* Register Name : PCIE_HDR_LOG2
************************************************************/

#define NBIF_HDR_LOG2_TLP_HDR_OFFSET                           0
#define NBIF_HDR_LOG2_TLP_HDR_MASK                             0xffffffff

typedef union {
  struct {
    UINT32                                             TLP_HDR:32;
  } Field;
  UINT32 Value;
} PCIE_HDR_LOG2_STRUCT;

#define PCICFG_NBIFEPF0CFG_PCIE_HDR_LOG2_OFFSET                     0x174
#define SMN_DEV0_FUNC0_NBIF0NBIO0_PCIE_HDR_LOG2_ADDRESS               0x10140174UL
#define SMN_DEV0_FUNC0_NBIF0NBIO1_PCIE_HDR_LOG2_ADDRESS               0x10340174UL
#define SMN_DEV0_FUNC0_NBIF1NBIO0_PCIE_HDR_LOG2_ADDRESS               0x10240174UL
#define SMN_DEV0_FUNC0_NBIF1NBIO1_PCIE_HDR_LOG2_ADDRESS               0x10440174UL
#define SMN_DEV0_FUNC0_NBIF2NBIO0_PCIE_HDR_LOG2_ADDRESS               0x10540174UL
#define SMN_DEV0_FUNC0_NBIF2NBIO1_PCIE_HDR_LOG2_ADDRESS               0x10740174UL
#define SMN_DEV1_FUNC0_NBIF0NBIO0_PCIE_HDR_LOG2_ADDRESS               0x10148174UL
#define SMN_DEV1_FUNC0_NBIF0NBIO1_PCIE_HDR_LOG2_ADDRESS               0x10348174UL
#define SMN_DEV1_FUNC0_NBIF1NBIO0_PCIE_HDR_LOG2_ADDRESS               0x10248174UL
#define SMN_DEV1_FUNC0_NBIF1NBIO1_PCIE_HDR_LOG2_ADDRESS               0x10448174UL


/***********************************************************
* Register Name : PCIE_HDR_LOG3
************************************************************/

#define NBIF_HDR_LOG3_TLP_HDR_OFFSET                           0
#define NBIF_HDR_LOG3_TLP_HDR_MASK                             0xffffffff

typedef union {
  struct {
    UINT32                                             TLP_HDR:32;
  } Field;
  UINT32 Value;
} PCIE_HDR_LOG3_STRUCT;

#define PCICFG_NBIFEPF0CFG_PCIE_HDR_LOG3_OFFSET                     0x178
#define SMN_DEV0_FUNC0_NBIF0NBIO0_PCIE_HDR_LOG3_ADDRESS               0x10140178UL
#define SMN_DEV0_FUNC0_NBIF0NBIO1_PCIE_HDR_LOG3_ADDRESS               0x10340178UL
#define SMN_DEV0_FUNC0_NBIF1NBIO0_PCIE_HDR_LOG3_ADDRESS               0x10240178UL
#define SMN_DEV0_FUNC0_NBIF1NBIO1_PCIE_HDR_LOG3_ADDRESS               0x10440178UL
#define SMN_DEV0_FUNC0_NBIF2NBIO0_PCIE_HDR_LOG3_ADDRESS               0x10540178UL
#define SMN_DEV0_FUNC0_NBIF2NBIO1_PCIE_HDR_LOG3_ADDRESS               0x10740178UL
#define SMN_DEV1_FUNC0_NBIF0NBIO0_PCIE_HDR_LOG3_ADDRESS               0x10148178UL
#define SMN_DEV1_FUNC0_NBIF0NBIO1_PCIE_HDR_LOG3_ADDRESS               0x10348178UL
#define SMN_DEV1_FUNC0_NBIF1NBIO0_PCIE_HDR_LOG3_ADDRESS               0x10248178UL
#define SMN_DEV1_FUNC0_NBIF1NBIO1_PCIE_HDR_LOG3_ADDRESS               0x10448178UL


/***********************************************************
* Register Name : PCIE_LANE_EQUALIZATION_CNTL
************************************************************/

#define NBIF_LANE_EQUALIZATION_CNTL_DOWNSTREAM_PORT_8GT_TX_PRESET_OFFSET 0
#define NBIF_LANE_EQUALIZATION_CNTL_DOWNSTREAM_PORT_8GT_TX_PRESET_MASK 0xf

#define NBIF_LANE_EQUALIZATION_CNTL_DOWNSTREAM_PORT_8GT_RX_PRESET_HINT_OFFSET 4
#define NBIF_LANE_EQUALIZATION_CNTL_DOWNSTREAM_PORT_8GT_RX_PRESET_HINT_MASK 0x70

#define NBIF_LANE_EQUALIZATION_CNTL_Reserved_7_7_OFFSET        7
#define NBIF_LANE_EQUALIZATION_CNTL_Reserved_7_7_MASK          0x80

#define NBIF_LANE_EQUALIZATION_CNTL_UPSTREAM_PORT_8GT_TX_PRESET_OFFSET 8
#define NBIF_LANE_EQUALIZATION_CNTL_UPSTREAM_PORT_8GT_TX_PRESET_MASK 0xf00

#define NBIF_LANE_EQUALIZATION_CNTL_UPSTREAM_PORT_8GT_RX_PRESET_HINT_OFFSET 12
#define NBIF_LANE_EQUALIZATION_CNTL_UPSTREAM_PORT_8GT_RX_PRESET_HINT_MASK 0x7000

#define NBIF_LANE_EQUALIZATION_CNTL_Reserved_15_15_OFFSET      15
#define NBIF_LANE_EQUALIZATION_CNTL_Reserved_15_15_MASK        0x8000

typedef union {
  struct {
    UINT16                       DOWNSTREAM_PORT_8GT_TX_PRESET:4;
    UINT16                  DOWNSTREAM_PORT_8GT_RX_PRESET_HINT:3;
    UINT16                                        Reserved_7_7:1;
    UINT16                         UPSTREAM_PORT_8GT_TX_PRESET:4;
    UINT16                    UPSTREAM_PORT_8GT_RX_PRESET_HINT:3;
    UINT16                                      Reserved_15_15:1;
  } Field;
  UINT16 Value;
} PCIE_LANE_EQUALIZATION_CNTL_STRUCT;

#define PCICFG_NBIFEPF0CFG_PCIE_LANE_EQUALIZATION_CNTL_OFFSET       0x27c
#define SMN_DEV0_FUNC0_NBIF0_N0NBIO0_PCIE_LANE_EQUALIZATION_CNTL_ADDRESS 0x1014027cUL
#define SMN_DEV0_FUNC0_NBIF0_N0NBIO1_PCIE_LANE_EQUALIZATION_CNTL_ADDRESS 0x1034027cUL
#define SMN_DEV0_FUNC0_NBIF0_N10NBIO0_PCIE_LANE_EQUALIZATION_CNTL_ADDRESS 0x10140290UL
#define SMN_DEV0_FUNC0_NBIF0_N10NBIO1_PCIE_LANE_EQUALIZATION_CNTL_ADDRESS 0x10340290UL
#define SMN_DEV0_FUNC0_NBIF0_N11NBIO0_PCIE_LANE_EQUALIZATION_CNTL_ADDRESS 0x10140292UL
#define SMN_DEV0_FUNC0_NBIF0_N11NBIO1_PCIE_LANE_EQUALIZATION_CNTL_ADDRESS 0x10340292UL
#define SMN_DEV0_FUNC0_NBIF0_N12NBIO0_PCIE_LANE_EQUALIZATION_CNTL_ADDRESS 0x10140294UL
#define SMN_DEV0_FUNC0_NBIF0_N12NBIO1_PCIE_LANE_EQUALIZATION_CNTL_ADDRESS 0x10340294UL
#define SMN_DEV0_FUNC0_NBIF0_N13NBIO0_PCIE_LANE_EQUALIZATION_CNTL_ADDRESS 0x10140296UL
#define SMN_DEV0_FUNC0_NBIF0_N13NBIO1_PCIE_LANE_EQUALIZATION_CNTL_ADDRESS 0x10340296UL
#define SMN_DEV0_FUNC0_NBIF0_N14NBIO0_PCIE_LANE_EQUALIZATION_CNTL_ADDRESS 0x10140298UL
#define SMN_DEV0_FUNC0_NBIF0_N14NBIO1_PCIE_LANE_EQUALIZATION_CNTL_ADDRESS 0x10340298UL
#define SMN_DEV0_FUNC0_NBIF0_N15NBIO0_PCIE_LANE_EQUALIZATION_CNTL_ADDRESS 0x1014029aUL
#define SMN_DEV0_FUNC0_NBIF0_N15NBIO1_PCIE_LANE_EQUALIZATION_CNTL_ADDRESS 0x1034029aUL
#define SMN_DEV0_FUNC0_NBIF0_N1NBIO0_PCIE_LANE_EQUALIZATION_CNTL_ADDRESS 0x1014027eUL
#define SMN_DEV0_FUNC0_NBIF0_N1NBIO1_PCIE_LANE_EQUALIZATION_CNTL_ADDRESS 0x1034027eUL
#define SMN_DEV0_FUNC0_NBIF0_N2NBIO0_PCIE_LANE_EQUALIZATION_CNTL_ADDRESS 0x10140280UL
#define SMN_DEV0_FUNC0_NBIF0_N2NBIO1_PCIE_LANE_EQUALIZATION_CNTL_ADDRESS 0x10340280UL
#define SMN_DEV0_FUNC0_NBIF0_N3NBIO0_PCIE_LANE_EQUALIZATION_CNTL_ADDRESS 0x10140282UL
#define SMN_DEV0_FUNC0_NBIF0_N3NBIO1_PCIE_LANE_EQUALIZATION_CNTL_ADDRESS 0x10340282UL
#define SMN_DEV0_FUNC0_NBIF0_N4NBIO0_PCIE_LANE_EQUALIZATION_CNTL_ADDRESS 0x10140284UL
#define SMN_DEV0_FUNC0_NBIF0_N4NBIO1_PCIE_LANE_EQUALIZATION_CNTL_ADDRESS 0x10340284UL
#define SMN_DEV0_FUNC0_NBIF0_N5NBIO0_PCIE_LANE_EQUALIZATION_CNTL_ADDRESS 0x10140286UL
#define SMN_DEV0_FUNC0_NBIF0_N5NBIO1_PCIE_LANE_EQUALIZATION_CNTL_ADDRESS 0x10340286UL
#define SMN_DEV0_FUNC0_NBIF0_N6NBIO0_PCIE_LANE_EQUALIZATION_CNTL_ADDRESS 0x10140288UL
#define SMN_DEV0_FUNC0_NBIF0_N6NBIO1_PCIE_LANE_EQUALIZATION_CNTL_ADDRESS 0x10340288UL
#define SMN_DEV0_FUNC0_NBIF0_N7NBIO0_PCIE_LANE_EQUALIZATION_CNTL_ADDRESS 0x1014028aUL
#define SMN_DEV0_FUNC0_NBIF0_N7NBIO1_PCIE_LANE_EQUALIZATION_CNTL_ADDRESS 0x1034028aUL
#define SMN_DEV0_FUNC0_NBIF0_N8NBIO0_PCIE_LANE_EQUALIZATION_CNTL_ADDRESS 0x1014028cUL
#define SMN_DEV0_FUNC0_NBIF0_N8NBIO1_PCIE_LANE_EQUALIZATION_CNTL_ADDRESS 0x1034028cUL
#define SMN_DEV0_FUNC0_NBIF0_N9NBIO0_PCIE_LANE_EQUALIZATION_CNTL_ADDRESS 0x1014028eUL
#define SMN_DEV0_FUNC0_NBIF0_N9NBIO1_PCIE_LANE_EQUALIZATION_CNTL_ADDRESS 0x1034028eUL
#define SMN_DEV0_FUNC0_NBIF1_N0NBIO0_PCIE_LANE_EQUALIZATION_CNTL_ADDRESS 0x1024027cUL
#define SMN_DEV0_FUNC0_NBIF1_N0NBIO1_PCIE_LANE_EQUALIZATION_CNTL_ADDRESS 0x1044027cUL
#define SMN_DEV0_FUNC0_NBIF1_N10NBIO0_PCIE_LANE_EQUALIZATION_CNTL_ADDRESS 0x10240290UL
#define SMN_DEV0_FUNC0_NBIF1_N10NBIO1_PCIE_LANE_EQUALIZATION_CNTL_ADDRESS 0x10440290UL
#define SMN_DEV0_FUNC0_NBIF1_N11NBIO0_PCIE_LANE_EQUALIZATION_CNTL_ADDRESS 0x10240292UL
#define SMN_DEV0_FUNC0_NBIF1_N11NBIO1_PCIE_LANE_EQUALIZATION_CNTL_ADDRESS 0x10440292UL
#define SMN_DEV0_FUNC0_NBIF1_N12NBIO0_PCIE_LANE_EQUALIZATION_CNTL_ADDRESS 0x10240294UL
#define SMN_DEV0_FUNC0_NBIF1_N12NBIO1_PCIE_LANE_EQUALIZATION_CNTL_ADDRESS 0x10440294UL
#define SMN_DEV0_FUNC0_NBIF1_N13NBIO0_PCIE_LANE_EQUALIZATION_CNTL_ADDRESS 0x10240296UL
#define SMN_DEV0_FUNC0_NBIF1_N13NBIO1_PCIE_LANE_EQUALIZATION_CNTL_ADDRESS 0x10440296UL
#define SMN_DEV0_FUNC0_NBIF1_N14NBIO0_PCIE_LANE_EQUALIZATION_CNTL_ADDRESS 0x10240298UL
#define SMN_DEV0_FUNC0_NBIF1_N14NBIO1_PCIE_LANE_EQUALIZATION_CNTL_ADDRESS 0x10440298UL
#define SMN_DEV0_FUNC0_NBIF1_N15NBIO0_PCIE_LANE_EQUALIZATION_CNTL_ADDRESS 0x1024029aUL
#define SMN_DEV0_FUNC0_NBIF1_N15NBIO1_PCIE_LANE_EQUALIZATION_CNTL_ADDRESS 0x1044029aUL
#define SMN_DEV0_FUNC0_NBIF1_N1NBIO0_PCIE_LANE_EQUALIZATION_CNTL_ADDRESS 0x1024027eUL
#define SMN_DEV0_FUNC0_NBIF1_N1NBIO1_PCIE_LANE_EQUALIZATION_CNTL_ADDRESS 0x1044027eUL
#define SMN_DEV0_FUNC0_NBIF1_N2NBIO0_PCIE_LANE_EQUALIZATION_CNTL_ADDRESS 0x10240280UL
#define SMN_DEV0_FUNC0_NBIF1_N2NBIO1_PCIE_LANE_EQUALIZATION_CNTL_ADDRESS 0x10440280UL
#define SMN_DEV0_FUNC0_NBIF1_N3NBIO0_PCIE_LANE_EQUALIZATION_CNTL_ADDRESS 0x10240282UL
#define SMN_DEV0_FUNC0_NBIF1_N3NBIO1_PCIE_LANE_EQUALIZATION_CNTL_ADDRESS 0x10440282UL
#define SMN_DEV0_FUNC0_NBIF1_N4NBIO0_PCIE_LANE_EQUALIZATION_CNTL_ADDRESS 0x10240284UL
#define SMN_DEV0_FUNC0_NBIF1_N4NBIO1_PCIE_LANE_EQUALIZATION_CNTL_ADDRESS 0x10440284UL
#define SMN_DEV0_FUNC0_NBIF1_N5NBIO0_PCIE_LANE_EQUALIZATION_CNTL_ADDRESS 0x10240286UL
#define SMN_DEV0_FUNC0_NBIF1_N5NBIO1_PCIE_LANE_EQUALIZATION_CNTL_ADDRESS 0x10440286UL
#define SMN_DEV0_FUNC0_NBIF1_N6NBIO0_PCIE_LANE_EQUALIZATION_CNTL_ADDRESS 0x10240288UL
#define SMN_DEV0_FUNC0_NBIF1_N6NBIO1_PCIE_LANE_EQUALIZATION_CNTL_ADDRESS 0x10440288UL
#define SMN_DEV0_FUNC0_NBIF1_N7NBIO0_PCIE_LANE_EQUALIZATION_CNTL_ADDRESS 0x1024028aUL
#define SMN_DEV0_FUNC0_NBIF1_N7NBIO1_PCIE_LANE_EQUALIZATION_CNTL_ADDRESS 0x1044028aUL
#define SMN_DEV0_FUNC0_NBIF1_N8NBIO0_PCIE_LANE_EQUALIZATION_CNTL_ADDRESS 0x1024028cUL
#define SMN_DEV0_FUNC0_NBIF1_N8NBIO1_PCIE_LANE_EQUALIZATION_CNTL_ADDRESS 0x1044028cUL
#define SMN_DEV0_FUNC0_NBIF1_N9NBIO0_PCIE_LANE_EQUALIZATION_CNTL_ADDRESS 0x1024028eUL
#define SMN_DEV0_FUNC0_NBIF1_N9NBIO1_PCIE_LANE_EQUALIZATION_CNTL_ADDRESS 0x1044028eUL
#define SMN_DEV0_FUNC0_NBIF2_N0NBIO0_PCIE_LANE_EQUALIZATION_CNTL_ADDRESS 0x1054027cUL
#define SMN_DEV0_FUNC0_NBIF2_N0NBIO1_PCIE_LANE_EQUALIZATION_CNTL_ADDRESS 0x1074027cUL
#define SMN_DEV0_FUNC0_NBIF2_N10NBIO0_PCIE_LANE_EQUALIZATION_CNTL_ADDRESS 0x10540290UL
#define SMN_DEV0_FUNC0_NBIF2_N10NBIO1_PCIE_LANE_EQUALIZATION_CNTL_ADDRESS 0x10740290UL
#define SMN_DEV0_FUNC0_NBIF2_N11NBIO0_PCIE_LANE_EQUALIZATION_CNTL_ADDRESS 0x10540292UL
#define SMN_DEV0_FUNC0_NBIF2_N11NBIO1_PCIE_LANE_EQUALIZATION_CNTL_ADDRESS 0x10740292UL
#define SMN_DEV0_FUNC0_NBIF2_N12NBIO0_PCIE_LANE_EQUALIZATION_CNTL_ADDRESS 0x10540294UL
#define SMN_DEV0_FUNC0_NBIF2_N12NBIO1_PCIE_LANE_EQUALIZATION_CNTL_ADDRESS 0x10740294UL
#define SMN_DEV0_FUNC0_NBIF2_N13NBIO0_PCIE_LANE_EQUALIZATION_CNTL_ADDRESS 0x10540296UL
#define SMN_DEV0_FUNC0_NBIF2_N13NBIO1_PCIE_LANE_EQUALIZATION_CNTL_ADDRESS 0x10740296UL
#define SMN_DEV0_FUNC0_NBIF2_N14NBIO0_PCIE_LANE_EQUALIZATION_CNTL_ADDRESS 0x10540298UL
#define SMN_DEV0_FUNC0_NBIF2_N14NBIO1_PCIE_LANE_EQUALIZATION_CNTL_ADDRESS 0x10740298UL
#define SMN_DEV0_FUNC0_NBIF2_N15NBIO0_PCIE_LANE_EQUALIZATION_CNTL_ADDRESS 0x1054029aUL
#define SMN_DEV0_FUNC0_NBIF2_N15NBIO1_PCIE_LANE_EQUALIZATION_CNTL_ADDRESS 0x1074029aUL
#define SMN_DEV0_FUNC0_NBIF2_N1NBIO0_PCIE_LANE_EQUALIZATION_CNTL_ADDRESS 0x1054027eUL
#define SMN_DEV0_FUNC0_NBIF2_N1NBIO1_PCIE_LANE_EQUALIZATION_CNTL_ADDRESS 0x1074027eUL
#define SMN_DEV0_FUNC0_NBIF2_N2NBIO0_PCIE_LANE_EQUALIZATION_CNTL_ADDRESS 0x10540280UL
#define SMN_DEV0_FUNC0_NBIF2_N2NBIO1_PCIE_LANE_EQUALIZATION_CNTL_ADDRESS 0x10740280UL
#define SMN_DEV0_FUNC0_NBIF2_N3NBIO0_PCIE_LANE_EQUALIZATION_CNTL_ADDRESS 0x10540282UL
#define SMN_DEV0_FUNC0_NBIF2_N3NBIO1_PCIE_LANE_EQUALIZATION_CNTL_ADDRESS 0x10740282UL
#define SMN_DEV0_FUNC0_NBIF2_N4NBIO0_PCIE_LANE_EQUALIZATION_CNTL_ADDRESS 0x10540284UL
#define SMN_DEV0_FUNC0_NBIF2_N4NBIO1_PCIE_LANE_EQUALIZATION_CNTL_ADDRESS 0x10740284UL
#define SMN_DEV0_FUNC0_NBIF2_N5NBIO0_PCIE_LANE_EQUALIZATION_CNTL_ADDRESS 0x10540286UL
#define SMN_DEV0_FUNC0_NBIF2_N5NBIO1_PCIE_LANE_EQUALIZATION_CNTL_ADDRESS 0x10740286UL
#define SMN_DEV0_FUNC0_NBIF2_N6NBIO0_PCIE_LANE_EQUALIZATION_CNTL_ADDRESS 0x10540288UL
#define SMN_DEV0_FUNC0_NBIF2_N6NBIO1_PCIE_LANE_EQUALIZATION_CNTL_ADDRESS 0x10740288UL
#define SMN_DEV0_FUNC0_NBIF2_N7NBIO0_PCIE_LANE_EQUALIZATION_CNTL_ADDRESS 0x1054028aUL
#define SMN_DEV0_FUNC0_NBIF2_N7NBIO1_PCIE_LANE_EQUALIZATION_CNTL_ADDRESS 0x1074028aUL
#define SMN_DEV0_FUNC0_NBIF2_N8NBIO0_PCIE_LANE_EQUALIZATION_CNTL_ADDRESS 0x1054028cUL
#define SMN_DEV0_FUNC0_NBIF2_N8NBIO1_PCIE_LANE_EQUALIZATION_CNTL_ADDRESS 0x1074028cUL
#define SMN_DEV0_FUNC0_NBIF2_N9NBIO0_PCIE_LANE_EQUALIZATION_CNTL_ADDRESS 0x1054028eUL
#define SMN_DEV0_FUNC0_NBIF2_N9NBIO1_PCIE_LANE_EQUALIZATION_CNTL_ADDRESS 0x1074028eUL
#define SMN_DEV1_FUNC0_NBIF0_N0NBIO0_PCIE_LANE_EQUALIZATION_CNTL_ADDRESS 0x1014827cUL
#define SMN_DEV1_FUNC0_NBIF0_N0NBIO1_PCIE_LANE_EQUALIZATION_CNTL_ADDRESS 0x1034827cUL
#define SMN_DEV1_FUNC0_NBIF0_N10NBIO0_PCIE_LANE_EQUALIZATION_CNTL_ADDRESS 0x10148290UL
#define SMN_DEV1_FUNC0_NBIF0_N10NBIO1_PCIE_LANE_EQUALIZATION_CNTL_ADDRESS 0x10348290UL
#define SMN_DEV1_FUNC0_NBIF0_N11NBIO0_PCIE_LANE_EQUALIZATION_CNTL_ADDRESS 0x10148292UL
#define SMN_DEV1_FUNC0_NBIF0_N11NBIO1_PCIE_LANE_EQUALIZATION_CNTL_ADDRESS 0x10348292UL
#define SMN_DEV1_FUNC0_NBIF0_N12NBIO0_PCIE_LANE_EQUALIZATION_CNTL_ADDRESS 0x10148294UL
#define SMN_DEV1_FUNC0_NBIF0_N12NBIO1_PCIE_LANE_EQUALIZATION_CNTL_ADDRESS 0x10348294UL
#define SMN_DEV1_FUNC0_NBIF0_N13NBIO0_PCIE_LANE_EQUALIZATION_CNTL_ADDRESS 0x10148296UL
#define SMN_DEV1_FUNC0_NBIF0_N13NBIO1_PCIE_LANE_EQUALIZATION_CNTL_ADDRESS 0x10348296UL
#define SMN_DEV1_FUNC0_NBIF0_N14NBIO0_PCIE_LANE_EQUALIZATION_CNTL_ADDRESS 0x10148298UL
#define SMN_DEV1_FUNC0_NBIF0_N14NBIO1_PCIE_LANE_EQUALIZATION_CNTL_ADDRESS 0x10348298UL
#define SMN_DEV1_FUNC0_NBIF0_N15NBIO0_PCIE_LANE_EQUALIZATION_CNTL_ADDRESS 0x1014829aUL
#define SMN_DEV1_FUNC0_NBIF0_N15NBIO1_PCIE_LANE_EQUALIZATION_CNTL_ADDRESS 0x1034829aUL
#define SMN_DEV1_FUNC0_NBIF0_N1NBIO0_PCIE_LANE_EQUALIZATION_CNTL_ADDRESS 0x1014827eUL
#define SMN_DEV1_FUNC0_NBIF0_N1NBIO1_PCIE_LANE_EQUALIZATION_CNTL_ADDRESS 0x1034827eUL
#define SMN_DEV1_FUNC0_NBIF0_N2NBIO0_PCIE_LANE_EQUALIZATION_CNTL_ADDRESS 0x10148280UL
#define SMN_DEV1_FUNC0_NBIF0_N2NBIO1_PCIE_LANE_EQUALIZATION_CNTL_ADDRESS 0x10348280UL
#define SMN_DEV1_FUNC0_NBIF0_N3NBIO0_PCIE_LANE_EQUALIZATION_CNTL_ADDRESS 0x10148282UL
#define SMN_DEV1_FUNC0_NBIF0_N3NBIO1_PCIE_LANE_EQUALIZATION_CNTL_ADDRESS 0x10348282UL
#define SMN_DEV1_FUNC0_NBIF0_N4NBIO0_PCIE_LANE_EQUALIZATION_CNTL_ADDRESS 0x10148284UL
#define SMN_DEV1_FUNC0_NBIF0_N4NBIO1_PCIE_LANE_EQUALIZATION_CNTL_ADDRESS 0x10348284UL
#define SMN_DEV1_FUNC0_NBIF0_N5NBIO0_PCIE_LANE_EQUALIZATION_CNTL_ADDRESS 0x10148286UL
#define SMN_DEV1_FUNC0_NBIF0_N5NBIO1_PCIE_LANE_EQUALIZATION_CNTL_ADDRESS 0x10348286UL
#define SMN_DEV1_FUNC0_NBIF0_N6NBIO0_PCIE_LANE_EQUALIZATION_CNTL_ADDRESS 0x10148288UL
#define SMN_DEV1_FUNC0_NBIF0_N6NBIO1_PCIE_LANE_EQUALIZATION_CNTL_ADDRESS 0x10348288UL
#define SMN_DEV1_FUNC0_NBIF0_N7NBIO0_PCIE_LANE_EQUALIZATION_CNTL_ADDRESS 0x1014828aUL
#define SMN_DEV1_FUNC0_NBIF0_N7NBIO1_PCIE_LANE_EQUALIZATION_CNTL_ADDRESS 0x1034828aUL
#define SMN_DEV1_FUNC0_NBIF0_N8NBIO0_PCIE_LANE_EQUALIZATION_CNTL_ADDRESS 0x1014828cUL
#define SMN_DEV1_FUNC0_NBIF0_N8NBIO1_PCIE_LANE_EQUALIZATION_CNTL_ADDRESS 0x1034828cUL
#define SMN_DEV1_FUNC0_NBIF0_N9NBIO0_PCIE_LANE_EQUALIZATION_CNTL_ADDRESS 0x1014828eUL
#define SMN_DEV1_FUNC0_NBIF0_N9NBIO1_PCIE_LANE_EQUALIZATION_CNTL_ADDRESS 0x1034828eUL
#define SMN_DEV1_FUNC0_NBIF1_N0NBIO0_PCIE_LANE_EQUALIZATION_CNTL_ADDRESS 0x1024827cUL
#define SMN_DEV1_FUNC0_NBIF1_N0NBIO1_PCIE_LANE_EQUALIZATION_CNTL_ADDRESS 0x1044827cUL
#define SMN_DEV1_FUNC0_NBIF1_N10NBIO0_PCIE_LANE_EQUALIZATION_CNTL_ADDRESS 0x10248290UL
#define SMN_DEV1_FUNC0_NBIF1_N10NBIO1_PCIE_LANE_EQUALIZATION_CNTL_ADDRESS 0x10448290UL
#define SMN_DEV1_FUNC0_NBIF1_N11NBIO0_PCIE_LANE_EQUALIZATION_CNTL_ADDRESS 0x10248292UL
#define SMN_DEV1_FUNC0_NBIF1_N11NBIO1_PCIE_LANE_EQUALIZATION_CNTL_ADDRESS 0x10448292UL
#define SMN_DEV1_FUNC0_NBIF1_N12NBIO0_PCIE_LANE_EQUALIZATION_CNTL_ADDRESS 0x10248294UL
#define SMN_DEV1_FUNC0_NBIF1_N12NBIO1_PCIE_LANE_EQUALIZATION_CNTL_ADDRESS 0x10448294UL
#define SMN_DEV1_FUNC0_NBIF1_N13NBIO0_PCIE_LANE_EQUALIZATION_CNTL_ADDRESS 0x10248296UL
#define SMN_DEV1_FUNC0_NBIF1_N13NBIO1_PCIE_LANE_EQUALIZATION_CNTL_ADDRESS 0x10448296UL
#define SMN_DEV1_FUNC0_NBIF1_N14NBIO0_PCIE_LANE_EQUALIZATION_CNTL_ADDRESS 0x10248298UL
#define SMN_DEV1_FUNC0_NBIF1_N14NBIO1_PCIE_LANE_EQUALIZATION_CNTL_ADDRESS 0x10448298UL
#define SMN_DEV1_FUNC0_NBIF1_N15NBIO0_PCIE_LANE_EQUALIZATION_CNTL_ADDRESS 0x1024829aUL
#define SMN_DEV1_FUNC0_NBIF1_N15NBIO1_PCIE_LANE_EQUALIZATION_CNTL_ADDRESS 0x1044829aUL
#define SMN_DEV1_FUNC0_NBIF1_N1NBIO0_PCIE_LANE_EQUALIZATION_CNTL_ADDRESS 0x1024827eUL
#define SMN_DEV1_FUNC0_NBIF1_N1NBIO1_PCIE_LANE_EQUALIZATION_CNTL_ADDRESS 0x1044827eUL
#define SMN_DEV1_FUNC0_NBIF1_N2NBIO0_PCIE_LANE_EQUALIZATION_CNTL_ADDRESS 0x10248280UL
#define SMN_DEV1_FUNC0_NBIF1_N2NBIO1_PCIE_LANE_EQUALIZATION_CNTL_ADDRESS 0x10448280UL
#define SMN_DEV1_FUNC0_NBIF1_N3NBIO0_PCIE_LANE_EQUALIZATION_CNTL_ADDRESS 0x10248282UL
#define SMN_DEV1_FUNC0_NBIF1_N3NBIO1_PCIE_LANE_EQUALIZATION_CNTL_ADDRESS 0x10448282UL
#define SMN_DEV1_FUNC0_NBIF1_N4NBIO0_PCIE_LANE_EQUALIZATION_CNTL_ADDRESS 0x10248284UL
#define SMN_DEV1_FUNC0_NBIF1_N4NBIO1_PCIE_LANE_EQUALIZATION_CNTL_ADDRESS 0x10448284UL
#define SMN_DEV1_FUNC0_NBIF1_N5NBIO0_PCIE_LANE_EQUALIZATION_CNTL_ADDRESS 0x10248286UL
#define SMN_DEV1_FUNC0_NBIF1_N5NBIO1_PCIE_LANE_EQUALIZATION_CNTL_ADDRESS 0x10448286UL
#define SMN_DEV1_FUNC0_NBIF1_N6NBIO0_PCIE_LANE_EQUALIZATION_CNTL_ADDRESS 0x10248288UL
#define SMN_DEV1_FUNC0_NBIF1_N6NBIO1_PCIE_LANE_EQUALIZATION_CNTL_ADDRESS 0x10448288UL
#define SMN_DEV1_FUNC0_NBIF1_N7NBIO0_PCIE_LANE_EQUALIZATION_CNTL_ADDRESS 0x1024828aUL
#define SMN_DEV1_FUNC0_NBIF1_N7NBIO1_PCIE_LANE_EQUALIZATION_CNTL_ADDRESS 0x1044828aUL
#define SMN_DEV1_FUNC0_NBIF1_N8NBIO0_PCIE_LANE_EQUALIZATION_CNTL_ADDRESS 0x1024828cUL
#define SMN_DEV1_FUNC0_NBIF1_N8NBIO1_PCIE_LANE_EQUALIZATION_CNTL_ADDRESS 0x1044828cUL
#define SMN_DEV1_FUNC0_NBIF1_N9NBIO0_PCIE_LANE_EQUALIZATION_CNTL_ADDRESS 0x1024828eUL
#define SMN_DEV1_FUNC0_NBIF1_N9NBIO1_PCIE_LANE_EQUALIZATION_CNTL_ADDRESS 0x1044828eUL


/***********************************************************
* Register Name : PCIE_LANE_ERROR_STATUS
************************************************************/

#define NBIF_LANE_ERROR_STATUS_LANE_ERROR_STATUS_BITS_OFFSET   0
#define NBIF_LANE_ERROR_STATUS_LANE_ERROR_STATUS_BITS_MASK     0xffff

#define NBIF_LANE_ERROR_STATUS_Reserved_31_16_OFFSET           16
#define NBIF_LANE_ERROR_STATUS_Reserved_31_16_MASK             0xffff0000

typedef union {
  struct {
    UINT32                              LANE_ERROR_STATUS_BITS:16;
    UINT32                                      Reserved_31_16:16;
  } Field;
  UINT32 Value;
} PCIE_LANE_ERROR_STATUS_STRUCT;

#define PCICFG_NBIFEPF0CFG_PCIE_LANE_ERROR_STATUS_OFFSET            0x278
#define SMN_DEV0_FUNC0_NBIF0NBIO0_PCIE_LANE_ERROR_STATUS_ADDRESS      0x10140278UL
#define SMN_DEV0_FUNC0_NBIF0NBIO1_PCIE_LANE_ERROR_STATUS_ADDRESS      0x10340278UL
#define SMN_DEV0_FUNC0_NBIF1NBIO0_PCIE_LANE_ERROR_STATUS_ADDRESS      0x10240278UL
#define SMN_DEV0_FUNC0_NBIF1NBIO1_PCIE_LANE_ERROR_STATUS_ADDRESS      0x10440278UL
#define SMN_DEV0_FUNC0_NBIF2NBIO0_PCIE_LANE_ERROR_STATUS_ADDRESS      0x10540278UL
#define SMN_DEV0_FUNC0_NBIF2NBIO1_PCIE_LANE_ERROR_STATUS_ADDRESS      0x10740278UL
#define SMN_DEV1_FUNC0_NBIF0NBIO0_PCIE_LANE_ERROR_STATUS_ADDRESS      0x10148278UL
#define SMN_DEV1_FUNC0_NBIF0NBIO1_PCIE_LANE_ERROR_STATUS_ADDRESS      0x10348278UL
#define SMN_DEV1_FUNC0_NBIF1NBIO0_PCIE_LANE_ERROR_STATUS_ADDRESS      0x10248278UL
#define SMN_DEV1_FUNC0_NBIF1NBIO1_PCIE_LANE_ERROR_STATUS_ADDRESS      0x10448278UL


/***********************************************************
* Register Name : PCIE_LINK_CNTL3
************************************************************/

#define NBIF_LINK_CNTL3_PERFORM_EQUALIZATION_OFFSET            0
#define NBIF_LINK_CNTL3_PERFORM_EQUALIZATION_MASK              0x1

#define NBIF_LINK_CNTL3_LINK_EQUALIZATION_REQ_INT_EN_OFFSET    1
#define NBIF_LINK_CNTL3_LINK_EQUALIZATION_REQ_INT_EN_MASK      0x2

#define NBIF_LINK_CNTL3_Reserved_8_2_OFFSET                    2
#define NBIF_LINK_CNTL3_Reserved_8_2_MASK                      0x1fc

#define NBIF_LINK_CNTL3_ENABLE_LOWER_SKP_OS_GEN_OFFSET         9
#define NBIF_LINK_CNTL3_ENABLE_LOWER_SKP_OS_GEN_MASK           0xfe00

#define NBIF_LINK_CNTL3_Reserved_31_16_OFFSET                  16
#define NBIF_LINK_CNTL3_Reserved_31_16_MASK                    0xffff0000

typedef union {
  struct {
    UINT32                                PERFORM_EQUALIZATION:1;
    UINT32                        LINK_EQUALIZATION_REQ_INT_EN:1;
    UINT32                                        Reserved_8_2:7;
    UINT32                             ENABLE_LOWER_SKP_OS_GEN:7;
    UINT32                                      Reserved_31_16:16;
  } Field;
  UINT32 Value;
} PCIE_LINK_CNTL3_STRUCT;

#define PCICFG_NBIFEPF0CFG_PCIE_LINK_CNTL3_OFFSET                   0x274
#define SMN_DEV0_FUNC0_NBIF0NBIO0_PCIE_LINK_CNTL3_ADDRESS             0x10140274UL
#define SMN_DEV0_FUNC0_NBIF0NBIO1_PCIE_LINK_CNTL3_ADDRESS             0x10340274UL
#define SMN_DEV0_FUNC0_NBIF1NBIO0_PCIE_LINK_CNTL3_ADDRESS             0x10240274UL
#define SMN_DEV0_FUNC0_NBIF1NBIO1_PCIE_LINK_CNTL3_ADDRESS             0x10440274UL
#define SMN_DEV0_FUNC0_NBIF2NBIO0_PCIE_LINK_CNTL3_ADDRESS             0x10540274UL
#define SMN_DEV0_FUNC0_NBIF2NBIO1_PCIE_LINK_CNTL3_ADDRESS             0x10740274UL
#define SMN_DEV1_FUNC0_NBIF0NBIO0_PCIE_LINK_CNTL3_ADDRESS             0x10148274UL
#define SMN_DEV1_FUNC0_NBIF0NBIO1_PCIE_LINK_CNTL3_ADDRESS             0x10348274UL
#define SMN_DEV1_FUNC0_NBIF1NBIO0_PCIE_LINK_CNTL3_ADDRESS             0x10248274UL
#define SMN_DEV1_FUNC0_NBIF1NBIO1_PCIE_LINK_CNTL3_ADDRESS             0x10448274UL


/***********************************************************
* Register Name : PCIE_LTR_CAP
************************************************************/

#define NBIF_LTR_CAP_LTR_MAX_S_LATENCY_VALUE_OFFSET            0
#define NBIF_LTR_CAP_LTR_MAX_S_LATENCY_VALUE_MASK              0x3ff

#define NBIF_LTR_CAP_LTR_MAX_S_LATENCY_SCALE_OFFSET            10
#define NBIF_LTR_CAP_LTR_MAX_S_LATENCY_SCALE_MASK              0x1c00

#define NBIF_LTR_CAP_Reserved_15_13_OFFSET                     13
#define NBIF_LTR_CAP_Reserved_15_13_MASK                       0xe000

#define NBIF_LTR_CAP_LTR_MAX_NS_LATENCY_VALUE_OFFSET           16
#define NBIF_LTR_CAP_LTR_MAX_NS_LATENCY_VALUE_MASK             0x3ff0000

#define NBIF_LTR_CAP_LTR_MAX_NS_LATENCY_SCALE_OFFSET           26
#define NBIF_LTR_CAP_LTR_MAX_NS_LATENCY_SCALE_MASK             0x1c000000

#define NBIF_LTR_CAP_Reserved_31_29_OFFSET                     29
#define NBIF_LTR_CAP_Reserved_31_29_MASK                       0xe0000000

typedef union {
  struct {
    UINT32                             LTR_MAX_S_LATENCY_VALUE:10;
    UINT32                             LTR_MAX_S_LATENCY_SCALE:3;
    UINT32                                      Reserved_15_13:3;
    UINT32                            LTR_MAX_NS_LATENCY_VALUE:10;
    UINT32                            LTR_MAX_NS_LATENCY_SCALE:3;
    UINT32                                      Reserved_31_29:3;
  } Field;
  UINT32 Value;
} PCIE_LTR_CAP_STRUCT;

#define PCICFG_NBIFEPF0CFG_PCIE_LTR_CAP_OFFSET                      0x324
#define SMN_DEV0_FUNC0_NBIF0NBIO0_PCIE_LTR_CAP_ADDRESS                0x10140324UL
#define SMN_DEV0_FUNC0_NBIF0NBIO1_PCIE_LTR_CAP_ADDRESS                0x10340324UL
#define SMN_DEV0_FUNC0_NBIF1NBIO0_PCIE_LTR_CAP_ADDRESS                0x10240324UL
#define SMN_DEV0_FUNC0_NBIF1NBIO1_PCIE_LTR_CAP_ADDRESS                0x10440324UL
#define SMN_DEV0_FUNC0_NBIF2NBIO0_PCIE_LTR_CAP_ADDRESS                0x10540324UL
#define SMN_DEV0_FUNC0_NBIF2NBIO1_PCIE_LTR_CAP_ADDRESS                0x10740324UL
#define SMN_DEV1_FUNC0_NBIF0NBIO0_PCIE_LTR_CAP_ADDRESS                0x10148324UL
#define SMN_DEV1_FUNC0_NBIF0NBIO1_PCIE_LTR_CAP_ADDRESS                0x10348324UL
#define SMN_DEV1_FUNC0_NBIF1NBIO0_PCIE_LTR_CAP_ADDRESS                0x10248324UL
#define SMN_DEV1_FUNC0_NBIF1NBIO1_PCIE_LTR_CAP_ADDRESS                0x10448324UL


/***********************************************************
* Register Name : PCIE_LTR_ENH_CAP_LIST
************************************************************/

#define NBIF_LTR_ENH_CAP_LIST_CAP_ID_OFFSET                    0
#define NBIF_LTR_ENH_CAP_LIST_CAP_ID_MASK                      0xffff

#define NBIF_LTR_ENH_CAP_LIST_CAP_VER_OFFSET                   16
#define NBIF_LTR_ENH_CAP_LIST_CAP_VER_MASK                     0xf0000

#define NBIF_LTR_ENH_CAP_LIST_NEXT_PTR_OFFSET                  20
#define NBIF_LTR_ENH_CAP_LIST_NEXT_PTR_MASK                    0xfff00000

typedef union {
  struct {
    UINT32                                              CAP_ID:16;
    UINT32                                             CAP_VER:4;
    UINT32                                            NEXT_PTR:12;
  } Field;
  UINT32 Value;
} PCIE_LTR_ENH_CAP_LIST_STRUCT;

#define PCICFG_NBIFEPF0CFG_PCIE_LTR_ENH_CAP_LIST_OFFSET             0x320
#define SMN_DEV0_FUNC0_NBIF0NBIO0_PCIE_LTR_ENH_CAP_LIST_ADDRESS       0x10140320UL
#define SMN_DEV0_FUNC0_NBIF0NBIO1_PCIE_LTR_ENH_CAP_LIST_ADDRESS       0x10340320UL
#define SMN_DEV0_FUNC0_NBIF1NBIO0_PCIE_LTR_ENH_CAP_LIST_ADDRESS       0x10240320UL
#define SMN_DEV0_FUNC0_NBIF1NBIO1_PCIE_LTR_ENH_CAP_LIST_ADDRESS       0x10440320UL
#define SMN_DEV0_FUNC0_NBIF2NBIO0_PCIE_LTR_ENH_CAP_LIST_ADDRESS       0x10540320UL
#define SMN_DEV0_FUNC0_NBIF2NBIO1_PCIE_LTR_ENH_CAP_LIST_ADDRESS       0x10740320UL
#define SMN_DEV1_FUNC0_NBIF0NBIO0_PCIE_LTR_ENH_CAP_LIST_ADDRESS       0x10148320UL
#define SMN_DEV1_FUNC0_NBIF0NBIO1_PCIE_LTR_ENH_CAP_LIST_ADDRESS       0x10348320UL
#define SMN_DEV1_FUNC0_NBIF1NBIO0_PCIE_LTR_ENH_CAP_LIST_ADDRESS       0x10248320UL
#define SMN_DEV1_FUNC0_NBIF1NBIO1_PCIE_LTR_ENH_CAP_LIST_ADDRESS       0x10448320UL


/***********************************************************
* Register Name : PCIE_MARGINING_ENH_CAP_LIST
************************************************************/

#define NBIF_MARGINING_ENH_CAP_LIST_CAP_ID_OFFSET              0
#define NBIF_MARGINING_ENH_CAP_LIST_CAP_ID_MASK                0xffff

#define NBIF_MARGINING_ENH_CAP_LIST_CAP_VER_OFFSET             16
#define NBIF_MARGINING_ENH_CAP_LIST_CAP_VER_MASK               0xf0000

#define NBIF_MARGINING_ENH_CAP_LIST_NEXT_PTR_OFFSET            20
#define NBIF_MARGINING_ENH_CAP_LIST_NEXT_PTR_MASK              0xfff00000

typedef union {
  struct {
    UINT32                                              CAP_ID:16;
    UINT32                                             CAP_VER:4;
    UINT32                                            NEXT_PTR:12;
  } Field;
  UINT32 Value;
} PCIE_MARGINING_ENH_CAP_LIST_STRUCT;

#define PCICFG_NBIFEPF0CFG_PCIE_MARGINING_ENH_CAP_LIST_OFFSET       0x450
#define SMN_DEV0_FUNC0_NBIF0NBIO0_PCIE_MARGINING_ENH_CAP_LIST_ADDRESS 0x10140450UL
#define SMN_DEV0_FUNC0_NBIF0NBIO1_PCIE_MARGINING_ENH_CAP_LIST_ADDRESS 0x10340450UL
#define SMN_DEV0_FUNC0_NBIF1NBIO0_PCIE_MARGINING_ENH_CAP_LIST_ADDRESS 0x10240450UL
#define SMN_DEV0_FUNC0_NBIF1NBIO1_PCIE_MARGINING_ENH_CAP_LIST_ADDRESS 0x10440450UL
#define SMN_DEV0_FUNC0_NBIF2NBIO0_PCIE_MARGINING_ENH_CAP_LIST_ADDRESS 0x10540450UL
#define SMN_DEV0_FUNC0_NBIF2NBIO1_PCIE_MARGINING_ENH_CAP_LIST_ADDRESS 0x10740450UL
#define SMN_DEV1_FUNC0_NBIF0NBIO0_PCIE_MARGINING_ENH_CAP_LIST_ADDRESS 0x10148450UL
#define SMN_DEV1_FUNC0_NBIF0NBIO1_PCIE_MARGINING_ENH_CAP_LIST_ADDRESS 0x10348450UL
#define SMN_DEV1_FUNC0_NBIF1NBIO0_PCIE_MARGINING_ENH_CAP_LIST_ADDRESS 0x10248450UL
#define SMN_DEV1_FUNC0_NBIF1NBIO1_PCIE_MARGINING_ENH_CAP_LIST_ADDRESS 0x10448450UL


/***********************************************************
* Register Name : PCIE_PASID_CAP
************************************************************/

#define NBIF_PASID_CAP_Reserved_0_0_OFFSET                     0
#define NBIF_PASID_CAP_Reserved_0_0_MASK                       0x1

#define NBIF_PASID_CAP_PASID_EXE_PERMISSION_SUPPORTED_OFFSET   1
#define NBIF_PASID_CAP_PASID_EXE_PERMISSION_SUPPORTED_MASK     0x2

#define NBIF_PASID_CAP_PASID_PRIV_MODE_SUPPORTED_OFFSET        2
#define NBIF_PASID_CAP_PASID_PRIV_MODE_SUPPORTED_MASK          0x4

#define NBIF_PASID_CAP_Reserved_7_3_OFFSET                     3
#define NBIF_PASID_CAP_Reserved_7_3_MASK                       0xf8

#define NBIF_PASID_CAP_MAX_PASID_WIDTH_OFFSET                  8
#define NBIF_PASID_CAP_MAX_PASID_WIDTH_MASK                    0x1f00

#define NBIF_PASID_CAP_Reserved_15_13_OFFSET                   13
#define NBIF_PASID_CAP_Reserved_15_13_MASK                     0xe000

typedef union {
  struct {
    UINT16                                        Reserved_0_0:1;
    UINT16                      PASID_EXE_PERMISSION_SUPPORTED:1;
    UINT16                           PASID_PRIV_MODE_SUPPORTED:1;
    UINT16                                        Reserved_7_3:5;
    UINT16                                     MAX_PASID_WIDTH:5;
    UINT16                                      Reserved_15_13:3;
  } Field;
  UINT16 Value;
} PCIE_PASID_CAP_STRUCT;

#define PCICFG_NBIFEPF0CFG_PCIE_PASID_CAP_OFFSET                    0x2d4
#define SMN_DEV0_FUNC0_NBIF0NBIO0_PCIE_PASID_CAP_ADDRESS              0x101402d4UL
#define SMN_DEV0_FUNC0_NBIF0NBIO1_PCIE_PASID_CAP_ADDRESS              0x103402d4UL
#define SMN_DEV0_FUNC0_NBIF1NBIO0_PCIE_PASID_CAP_ADDRESS              0x102402d4UL
#define SMN_DEV0_FUNC0_NBIF1NBIO1_PCIE_PASID_CAP_ADDRESS              0x104402d4UL
#define SMN_DEV0_FUNC0_NBIF2NBIO0_PCIE_PASID_CAP_ADDRESS              0x105402d4UL
#define SMN_DEV0_FUNC0_NBIF2NBIO1_PCIE_PASID_CAP_ADDRESS              0x107402d4UL
#define SMN_DEV1_FUNC0_NBIF0NBIO0_PCIE_PASID_CAP_ADDRESS              0x101482d4UL
#define SMN_DEV1_FUNC0_NBIF0NBIO1_PCIE_PASID_CAP_ADDRESS              0x103482d4UL
#define SMN_DEV1_FUNC0_NBIF1NBIO0_PCIE_PASID_CAP_ADDRESS              0x102482d4UL
#define SMN_DEV1_FUNC0_NBIF1NBIO1_PCIE_PASID_CAP_ADDRESS              0x104482d4UL


/***********************************************************
* Register Name : PCIE_PASID_CNTL
************************************************************/

#define NBIF_PASID_CNTL_PASID_ENABLE_OFFSET                    0
#define NBIF_PASID_CNTL_PASID_ENABLE_MASK                      0x1

#define NBIF_PASID_CNTL_PASID_EXE_PERMISSION_ENABLE_OFFSET     1
#define NBIF_PASID_CNTL_PASID_EXE_PERMISSION_ENABLE_MASK       0x2

#define NBIF_PASID_CNTL_PASID_PRIV_MODE_SUPPORTED_ENABLE_OFFSET 2
#define NBIF_PASID_CNTL_PASID_PRIV_MODE_SUPPORTED_ENABLE_MASK  0x4

#define NBIF_PASID_CNTL_Reserved_15_3_OFFSET                   3
#define NBIF_PASID_CNTL_Reserved_15_3_MASK                     0xfff8

typedef union {
  struct {
    UINT16                                        PASID_ENABLE:1;
    UINT16                         PASID_EXE_PERMISSION_ENABLE:1;
    UINT16                    PASID_PRIV_MODE_SUPPORTED_ENABLE:1;
    UINT16                                       Reserved_15_3:13;
  } Field;
  UINT16 Value;
} PCIE_PASID_CNTL_STRUCT;

#define PCICFG_NBIFEPF0CFG_PCIE_PASID_CNTL_OFFSET                   0x2d6
#define SMN_DEV0_FUNC0_NBIF0NBIO0_PCIE_PASID_CNTL_ADDRESS             0x101402d6UL
#define SMN_DEV0_FUNC0_NBIF0NBIO1_PCIE_PASID_CNTL_ADDRESS             0x103402d6UL
#define SMN_DEV0_FUNC0_NBIF1NBIO0_PCIE_PASID_CNTL_ADDRESS             0x102402d6UL
#define SMN_DEV0_FUNC0_NBIF1NBIO1_PCIE_PASID_CNTL_ADDRESS             0x104402d6UL
#define SMN_DEV0_FUNC0_NBIF2NBIO0_PCIE_PASID_CNTL_ADDRESS             0x105402d6UL
#define SMN_DEV0_FUNC0_NBIF2NBIO1_PCIE_PASID_CNTL_ADDRESS             0x107402d6UL
#define SMN_DEV1_FUNC0_NBIF0NBIO0_PCIE_PASID_CNTL_ADDRESS             0x101482d6UL
#define SMN_DEV1_FUNC0_NBIF0NBIO1_PCIE_PASID_CNTL_ADDRESS             0x103482d6UL
#define SMN_DEV1_FUNC0_NBIF1NBIO0_PCIE_PASID_CNTL_ADDRESS             0x102482d6UL
#define SMN_DEV1_FUNC0_NBIF1NBIO1_PCIE_PASID_CNTL_ADDRESS             0x104482d6UL


/***********************************************************
* Register Name : PCIE_PASID_ENH_CAP_LIST
************************************************************/

#define NBIF_PASID_ENH_CAP_LIST_CAP_ID_OFFSET                  0
#define NBIF_PASID_ENH_CAP_LIST_CAP_ID_MASK                    0xffff

#define NBIF_PASID_ENH_CAP_LIST_CAP_VER_OFFSET                 16
#define NBIF_PASID_ENH_CAP_LIST_CAP_VER_MASK                   0xf0000

#define NBIF_PASID_ENH_CAP_LIST_NEXT_PTR_OFFSET                20
#define NBIF_PASID_ENH_CAP_LIST_NEXT_PTR_MASK                  0xfff00000

typedef union {
  struct {
    UINT32                                              CAP_ID:16;
    UINT32                                             CAP_VER:4;
    UINT32                                            NEXT_PTR:12;
  } Field;
  UINT32 Value;
} PCIE_PASID_ENH_CAP_LIST_STRUCT;

#define PCICFG_NBIFEPF0CFG_PCIE_PASID_ENH_CAP_LIST_OFFSET           0x2d0
#define SMN_DEV0_FUNC0_NBIF0NBIO0_PCIE_PASID_ENH_CAP_LIST_ADDRESS     0x101402d0UL
#define SMN_DEV0_FUNC0_NBIF0NBIO1_PCIE_PASID_ENH_CAP_LIST_ADDRESS     0x103402d0UL
#define SMN_DEV0_FUNC0_NBIF1NBIO0_PCIE_PASID_ENH_CAP_LIST_ADDRESS     0x102402d0UL
#define SMN_DEV0_FUNC0_NBIF1NBIO1_PCIE_PASID_ENH_CAP_LIST_ADDRESS     0x104402d0UL
#define SMN_DEV0_FUNC0_NBIF2NBIO0_PCIE_PASID_ENH_CAP_LIST_ADDRESS     0x105402d0UL
#define SMN_DEV0_FUNC0_NBIF2NBIO1_PCIE_PASID_ENH_CAP_LIST_ADDRESS     0x107402d0UL
#define SMN_DEV1_FUNC0_NBIF0NBIO0_PCIE_PASID_ENH_CAP_LIST_ADDRESS     0x101482d0UL
#define SMN_DEV1_FUNC0_NBIF0NBIO1_PCIE_PASID_ENH_CAP_LIST_ADDRESS     0x103482d0UL
#define SMN_DEV1_FUNC0_NBIF1NBIO0_PCIE_PASID_ENH_CAP_LIST_ADDRESS     0x102482d0UL
#define SMN_DEV1_FUNC0_NBIF1NBIO1_PCIE_PASID_ENH_CAP_LIST_ADDRESS     0x104482d0UL


/***********************************************************
* Register Name : PCIE_PHY_16GT_ENH_CAP_LIST
************************************************************/

#define NBIF_PHY_16GT_ENH_CAP_LIST_CAP_ID_OFFSET               0
#define NBIF_PHY_16GT_ENH_CAP_LIST_CAP_ID_MASK                 0xffff

#define NBIF_PHY_16GT_ENH_CAP_LIST_CAP_VER_OFFSET              16
#define NBIF_PHY_16GT_ENH_CAP_LIST_CAP_VER_MASK                0xf0000

#define NBIF_PHY_16GT_ENH_CAP_LIST_NEXT_PTR_OFFSET             20
#define NBIF_PHY_16GT_ENH_CAP_LIST_NEXT_PTR_MASK               0xfff00000

typedef union {
  struct {
    UINT32                                              CAP_ID:16;
    UINT32                                             CAP_VER:4;
    UINT32                                            NEXT_PTR:12;
  } Field;
  UINT32 Value;
} PCIE_PHY_16GT_ENH_CAP_LIST_STRUCT;

#define PCICFG_NBIFEPF0CFG_PCIE_PHY_16GT_ENH_CAP_LIST_OFFSET        0x410
#define SMN_DEV0_FUNC0_NBIF0NBIO0_PCIE_PHY_16GT_ENH_CAP_LIST_ADDRESS  0x10140410UL
#define SMN_DEV0_FUNC0_NBIF0NBIO1_PCIE_PHY_16GT_ENH_CAP_LIST_ADDRESS  0x10340410UL
#define SMN_DEV0_FUNC0_NBIF1NBIO0_PCIE_PHY_16GT_ENH_CAP_LIST_ADDRESS  0x10240410UL
#define SMN_DEV0_FUNC0_NBIF1NBIO1_PCIE_PHY_16GT_ENH_CAP_LIST_ADDRESS  0x10440410UL
#define SMN_DEV0_FUNC0_NBIF2NBIO0_PCIE_PHY_16GT_ENH_CAP_LIST_ADDRESS  0x10540410UL
#define SMN_DEV0_FUNC0_NBIF2NBIO1_PCIE_PHY_16GT_ENH_CAP_LIST_ADDRESS  0x10740410UL
#define SMN_DEV1_FUNC0_NBIF0NBIO0_PCIE_PHY_16GT_ENH_CAP_LIST_ADDRESS  0x10148410UL
#define SMN_DEV1_FUNC0_NBIF0NBIO1_PCIE_PHY_16GT_ENH_CAP_LIST_ADDRESS  0x10348410UL
#define SMN_DEV1_FUNC0_NBIF1NBIO0_PCIE_PHY_16GT_ENH_CAP_LIST_ADDRESS  0x10248410UL
#define SMN_DEV1_FUNC0_NBIF1NBIO1_PCIE_PHY_16GT_ENH_CAP_LIST_ADDRESS  0x10448410UL


/***********************************************************
* Register Name : PCIE_PHY_32GT_ENH_CAP_LIST
************************************************************/

#define NBIF_PHY_32GT_ENH_CAP_LIST_CAP_ID_OFFSET               0
#define NBIF_PHY_32GT_ENH_CAP_LIST_CAP_ID_MASK                 0xffff

#define NBIF_PHY_32GT_ENH_CAP_LIST_CAP_VER_OFFSET              16
#define NBIF_PHY_32GT_ENH_CAP_LIST_CAP_VER_MASK                0xf0000

#define NBIF_PHY_32GT_ENH_CAP_LIST_NEXT_PTR_OFFSET             20
#define NBIF_PHY_32GT_ENH_CAP_LIST_NEXT_PTR_MASK               0xfff00000

typedef union {
  struct {
    UINT32                                              CAP_ID:16;
    UINT32                                             CAP_VER:4;
    UINT32                                            NEXT_PTR:12;
  } Field;
  UINT32 Value;
} PCIE_PHY_32GT_ENH_CAP_LIST_STRUCT;

#define PCICFG_NBIFEPF0CFG_PCIE_PHY_32GT_ENH_CAP_LIST_OFFSET        0x500
#define SMN_DEV0_FUNC0_NBIF0NBIO0_PCIE_PHY_32GT_ENH_CAP_LIST_ADDRESS  0x10140500UL
#define SMN_DEV0_FUNC0_NBIF0NBIO1_PCIE_PHY_32GT_ENH_CAP_LIST_ADDRESS  0x10340500UL
#define SMN_DEV0_FUNC0_NBIF1NBIO0_PCIE_PHY_32GT_ENH_CAP_LIST_ADDRESS  0x10240500UL
#define SMN_DEV0_FUNC0_NBIF1NBIO1_PCIE_PHY_32GT_ENH_CAP_LIST_ADDRESS  0x10440500UL
#define SMN_DEV0_FUNC0_NBIF2NBIO0_PCIE_PHY_32GT_ENH_CAP_LIST_ADDRESS  0x10540500UL
#define SMN_DEV0_FUNC0_NBIF2NBIO1_PCIE_PHY_32GT_ENH_CAP_LIST_ADDRESS  0x10740500UL
#define SMN_DEV1_FUNC0_NBIF0NBIO0_PCIE_PHY_32GT_ENH_CAP_LIST_ADDRESS  0x10148500UL
#define SMN_DEV1_FUNC0_NBIF0NBIO1_PCIE_PHY_32GT_ENH_CAP_LIST_ADDRESS  0x10348500UL
#define SMN_DEV1_FUNC0_NBIF1NBIO0_PCIE_PHY_32GT_ENH_CAP_LIST_ADDRESS  0x10248500UL
#define SMN_DEV1_FUNC0_NBIF1NBIO1_PCIE_PHY_32GT_ENH_CAP_LIST_ADDRESS  0x10448500UL


/***********************************************************
* Register Name : PCIE_PORT_VC_CAP_REG1
************************************************************/

#define NBIF_PORT_VC_CAP_REG1_EXT_VC_COUNT_OFFSET              0
#define NBIF_PORT_VC_CAP_REG1_EXT_VC_COUNT_MASK                0x7

#define NBIF_PORT_VC_CAP_REG1_Reserved_3_3_OFFSET              3
#define NBIF_PORT_VC_CAP_REG1_Reserved_3_3_MASK                0x8

#define NBIF_PORT_VC_CAP_REG1_LOW_PRIORITY_EXT_VC_COUNT_OFFSET 4
#define NBIF_PORT_VC_CAP_REG1_LOW_PRIORITY_EXT_VC_COUNT_MASK   0x70

#define NBIF_PORT_VC_CAP_REG1_Reserved_7_7_OFFSET              7
#define NBIF_PORT_VC_CAP_REG1_Reserved_7_7_MASK                0x80

#define NBIF_PORT_VC_CAP_REG1_REF_CLK_OFFSET                   8
#define NBIF_PORT_VC_CAP_REG1_REF_CLK_MASK                     0x300

#define NBIF_PORT_VC_CAP_REG1_PORT_ARB_TABLE_ENTRY_SIZE_OFFSET 10
#define NBIF_PORT_VC_CAP_REG1_PORT_ARB_TABLE_ENTRY_SIZE_MASK   0xc00

#define NBIF_PORT_VC_CAP_REG1_Reserved_31_12_OFFSET            12
#define NBIF_PORT_VC_CAP_REG1_Reserved_31_12_MASK              0xfffff000

typedef union {
  struct {
    UINT32                                        EXT_VC_COUNT:3;
    UINT32                                        Reserved_3_3:1;
    UINT32                           LOW_PRIORITY_EXT_VC_COUNT:3;
    UINT32                                        Reserved_7_7:1;
    UINT32                                             REF_CLK:2;
    UINT32                           PORT_ARB_TABLE_ENTRY_SIZE:2;
    UINT32                                      Reserved_31_12:20;
  } Field;
  UINT32 Value;
} PCIE_PORT_VC_CAP_REG1_STRUCT;

#define PCICFG_NBIFEPF0CFG_PCIE_PORT_VC_CAP_REG1_OFFSET             0x114
#define SMN_DEV0_FUNC0_NBIF0NBIO0_PCIE_PORT_VC_CAP_REG1_ADDRESS       0x10140114UL
#define SMN_DEV0_FUNC0_NBIF0NBIO1_PCIE_PORT_VC_CAP_REG1_ADDRESS       0x10340114UL
#define SMN_DEV0_FUNC0_NBIF1NBIO0_PCIE_PORT_VC_CAP_REG1_ADDRESS       0x10240114UL
#define SMN_DEV0_FUNC0_NBIF1NBIO1_PCIE_PORT_VC_CAP_REG1_ADDRESS       0x10440114UL
#define SMN_DEV0_FUNC0_NBIF2NBIO0_PCIE_PORT_VC_CAP_REG1_ADDRESS       0x10540114UL
#define SMN_DEV0_FUNC0_NBIF2NBIO1_PCIE_PORT_VC_CAP_REG1_ADDRESS       0x10740114UL
#define SMN_DEV1_FUNC0_NBIF0NBIO0_PCIE_PORT_VC_CAP_REG1_ADDRESS       0x10148114UL
#define SMN_DEV1_FUNC0_NBIF0NBIO1_PCIE_PORT_VC_CAP_REG1_ADDRESS       0x10348114UL
#define SMN_DEV1_FUNC0_NBIF1NBIO0_PCIE_PORT_VC_CAP_REG1_ADDRESS       0x10248114UL
#define SMN_DEV1_FUNC0_NBIF1NBIO1_PCIE_PORT_VC_CAP_REG1_ADDRESS       0x10448114UL


/***********************************************************
* Register Name : PCIE_PORT_VC_CAP_REG2
************************************************************/

#define NBIF_PORT_VC_CAP_REG2_VC_ARB_CAP_OFFSET                0
#define NBIF_PORT_VC_CAP_REG2_VC_ARB_CAP_MASK                  0xff

#define NBIF_PORT_VC_CAP_REG2_Reserved_23_8_OFFSET             8
#define NBIF_PORT_VC_CAP_REG2_Reserved_23_8_MASK               0xffff00

#define NBIF_PORT_VC_CAP_REG2_VC_ARB_TABLE_OFFSET_OFFSET       24
#define NBIF_PORT_VC_CAP_REG2_VC_ARB_TABLE_OFFSET_MASK         0xff000000

typedef union {
  struct {
    UINT32                                          VC_ARB_CAP:8;
    UINT32                                       Reserved_23_8:16;
    UINT32                                 VC_ARB_TABLE_OFFSET:8;
  } Field;
  UINT32 Value;
} PCIE_PORT_VC_CAP_REG2_STRUCT;

#define PCICFG_NBIFEPF0CFG_PCIE_PORT_VC_CAP_REG2_OFFSET             0x118
#define SMN_DEV0_FUNC0_NBIF0NBIO0_PCIE_PORT_VC_CAP_REG2_ADDRESS       0x10140118UL
#define SMN_DEV0_FUNC0_NBIF0NBIO1_PCIE_PORT_VC_CAP_REG2_ADDRESS       0x10340118UL
#define SMN_DEV0_FUNC0_NBIF1NBIO0_PCIE_PORT_VC_CAP_REG2_ADDRESS       0x10240118UL
#define SMN_DEV0_FUNC0_NBIF1NBIO1_PCIE_PORT_VC_CAP_REG2_ADDRESS       0x10440118UL
#define SMN_DEV0_FUNC0_NBIF2NBIO0_PCIE_PORT_VC_CAP_REG2_ADDRESS       0x10540118UL
#define SMN_DEV0_FUNC0_NBIF2NBIO1_PCIE_PORT_VC_CAP_REG2_ADDRESS       0x10740118UL
#define SMN_DEV1_FUNC0_NBIF0NBIO0_PCIE_PORT_VC_CAP_REG2_ADDRESS       0x10148118UL
#define SMN_DEV1_FUNC0_NBIF0NBIO1_PCIE_PORT_VC_CAP_REG2_ADDRESS       0x10348118UL
#define SMN_DEV1_FUNC0_NBIF1NBIO0_PCIE_PORT_VC_CAP_REG2_ADDRESS       0x10248118UL
#define SMN_DEV1_FUNC0_NBIF1NBIO1_PCIE_PORT_VC_CAP_REG2_ADDRESS       0x10448118UL


/***********************************************************
* Register Name : PCIE_PORT_VC_CNTL
************************************************************/

#define NBIF_PORT_VC_CNTL_LOAD_VC_ARB_TABLE_OFFSET             0
#define NBIF_PORT_VC_CNTL_LOAD_VC_ARB_TABLE_MASK               0x1

#define NBIF_PORT_VC_CNTL_VC_ARB_SELECT_OFFSET                 1
#define NBIF_PORT_VC_CNTL_VC_ARB_SELECT_MASK                   0xe

#define NBIF_PORT_VC_CNTL_Reserved_15_4_OFFSET                 4
#define NBIF_PORT_VC_CNTL_Reserved_15_4_MASK                   0xfff0

typedef union {
  struct {
    UINT16                                   LOAD_VC_ARB_TABLE:1;
    UINT16                                       VC_ARB_SELECT:3;
    UINT16                                       Reserved_15_4:12;
  } Field;
  UINT16 Value;
} PCIE_PORT_VC_CNTL_STRUCT;

#define PCICFG_NBIFEPF0CFG_PCIE_PORT_VC_CNTL_OFFSET                 0x11c
#define SMN_DEV0_FUNC0_NBIF0NBIO0_PCIE_PORT_VC_CNTL_ADDRESS           0x1014011cUL
#define SMN_DEV0_FUNC0_NBIF0NBIO1_PCIE_PORT_VC_CNTL_ADDRESS           0x1034011cUL
#define SMN_DEV0_FUNC0_NBIF1NBIO0_PCIE_PORT_VC_CNTL_ADDRESS           0x1024011cUL
#define SMN_DEV0_FUNC0_NBIF1NBIO1_PCIE_PORT_VC_CNTL_ADDRESS           0x1044011cUL
#define SMN_DEV0_FUNC0_NBIF2NBIO0_PCIE_PORT_VC_CNTL_ADDRESS           0x1054011cUL
#define SMN_DEV0_FUNC0_NBIF2NBIO1_PCIE_PORT_VC_CNTL_ADDRESS           0x1074011cUL
#define SMN_DEV1_FUNC0_NBIF0NBIO0_PCIE_PORT_VC_CNTL_ADDRESS           0x1014811cUL
#define SMN_DEV1_FUNC0_NBIF0NBIO1_PCIE_PORT_VC_CNTL_ADDRESS           0x1034811cUL
#define SMN_DEV1_FUNC0_NBIF1NBIO0_PCIE_PORT_VC_CNTL_ADDRESS           0x1024811cUL
#define SMN_DEV1_FUNC0_NBIF1NBIO1_PCIE_PORT_VC_CNTL_ADDRESS           0x1044811cUL


/***********************************************************
* Register Name : PCIE_PORT_VC_STATUS
************************************************************/

#define NBIF_PORT_VC_STATUS_VC_ARB_TABLE_STATUS_OFFSET         0
#define NBIF_PORT_VC_STATUS_VC_ARB_TABLE_STATUS_MASK           0x1

#define NBIF_PORT_VC_STATUS_Reserved_15_1_OFFSET               1
#define NBIF_PORT_VC_STATUS_Reserved_15_1_MASK                 0xfffe

typedef union {
  struct {
    UINT16                                 VC_ARB_TABLE_STATUS:1;
    UINT16                                       Reserved_15_1:15;
  } Field;
  UINT16 Value;
} PCIE_PORT_VC_STATUS_STRUCT;

#define PCICFG_NBIFEPF0CFG_PCIE_PORT_VC_STATUS_OFFSET               0x11e
#define SMN_DEV0_FUNC0_NBIF0NBIO0_PCIE_PORT_VC_STATUS_ADDRESS         0x1014011eUL
#define SMN_DEV0_FUNC0_NBIF0NBIO1_PCIE_PORT_VC_STATUS_ADDRESS         0x1034011eUL
#define SMN_DEV0_FUNC0_NBIF1NBIO0_PCIE_PORT_VC_STATUS_ADDRESS         0x1024011eUL
#define SMN_DEV0_FUNC0_NBIF1NBIO1_PCIE_PORT_VC_STATUS_ADDRESS         0x1044011eUL
#define SMN_DEV0_FUNC0_NBIF2NBIO0_PCIE_PORT_VC_STATUS_ADDRESS         0x1054011eUL
#define SMN_DEV0_FUNC0_NBIF2NBIO1_PCIE_PORT_VC_STATUS_ADDRESS         0x1074011eUL
#define SMN_DEV1_FUNC0_NBIF0NBIO0_PCIE_PORT_VC_STATUS_ADDRESS         0x1014811eUL
#define SMN_DEV1_FUNC0_NBIF0NBIO1_PCIE_PORT_VC_STATUS_ADDRESS         0x1034811eUL
#define SMN_DEV1_FUNC0_NBIF1NBIO0_PCIE_PORT_VC_STATUS_ADDRESS         0x1024811eUL
#define SMN_DEV1_FUNC0_NBIF1NBIO1_PCIE_PORT_VC_STATUS_ADDRESS         0x1044811eUL


/***********************************************************
* Register Name : PCIE_PWR_BUDGET_CAP
************************************************************/

#define NBIF_PWR_BUDGET_CAP_SYSTEM_ALLOCATED_OFFSET            0
#define NBIF_PWR_BUDGET_CAP_SYSTEM_ALLOCATED_MASK              0x1

#define NBIF_PWR_BUDGET_CAP_Reserved_7_1_OFFSET                1
#define NBIF_PWR_BUDGET_CAP_Reserved_7_1_MASK                  0xfe

typedef union {
  struct {
    UINT8                                    SYSTEM_ALLOCATED:1;
    UINT8                                        Reserved_7_1:7;
  } Field;
  UINT8 Value;
} PCIE_PWR_BUDGET_CAP_STRUCT;

#define PCICFG_NBIFEPF0CFG_PCIE_PWR_BUDGET_CAP_OFFSET               0x24c
#define SMN_DEV0_FUNC0_NBIF0NBIO0_PCIE_PWR_BUDGET_CAP_ADDRESS         0x1014024cUL
#define SMN_DEV0_FUNC0_NBIF0NBIO1_PCIE_PWR_BUDGET_CAP_ADDRESS         0x1034024cUL
#define SMN_DEV0_FUNC0_NBIF1NBIO0_PCIE_PWR_BUDGET_CAP_ADDRESS         0x1024024cUL
#define SMN_DEV0_FUNC0_NBIF1NBIO1_PCIE_PWR_BUDGET_CAP_ADDRESS         0x1044024cUL
#define SMN_DEV0_FUNC0_NBIF2NBIO0_PCIE_PWR_BUDGET_CAP_ADDRESS         0x1054024cUL
#define SMN_DEV0_FUNC0_NBIF2NBIO1_PCIE_PWR_BUDGET_CAP_ADDRESS         0x1074024cUL
#define SMN_DEV1_FUNC0_NBIF0NBIO0_PCIE_PWR_BUDGET_CAP_ADDRESS         0x1014824cUL
#define SMN_DEV1_FUNC0_NBIF0NBIO1_PCIE_PWR_BUDGET_CAP_ADDRESS         0x1034824cUL
#define SMN_DEV1_FUNC0_NBIF1NBIO0_PCIE_PWR_BUDGET_CAP_ADDRESS         0x1024824cUL
#define SMN_DEV1_FUNC0_NBIF1NBIO1_PCIE_PWR_BUDGET_CAP_ADDRESS         0x1044824cUL


/***********************************************************
* Register Name : PCIE_PWR_BUDGET_DATA
************************************************************/

#define NBIF_PWR_BUDGET_DATA_BASE_POWER_OFFSET                 0
#define NBIF_PWR_BUDGET_DATA_BASE_POWER_MASK                   0xff

#define NBIF_PWR_BUDGET_DATA_DATA_SCALE_OFFSET                 8
#define NBIF_PWR_BUDGET_DATA_DATA_SCALE_MASK                   0x300

#define NBIF_PWR_BUDGET_DATA_PM_SUB_STATE_OFFSET               10
#define NBIF_PWR_BUDGET_DATA_PM_SUB_STATE_MASK                 0x1c00

#define NBIF_PWR_BUDGET_DATA_PM_STATE_OFFSET                   13
#define NBIF_PWR_BUDGET_DATA_PM_STATE_MASK                     0x6000

#define NBIF_PWR_BUDGET_DATA_TYPE_OFFSET                       15
#define NBIF_PWR_BUDGET_DATA_TYPE_MASK                         0x38000

#define NBIF_PWR_BUDGET_DATA_POWER_RAIL_OFFSET                 18
#define NBIF_PWR_BUDGET_DATA_POWER_RAIL_MASK                   0x1c0000

#define NBIF_PWR_BUDGET_DATA_Reserved_31_21_OFFSET             21
#define NBIF_PWR_BUDGET_DATA_Reserved_31_21_MASK               0xffe00000

typedef union {
  struct {
    UINT32                                          BASE_POWER:8;
    UINT32                                          DATA_SCALE:2;
    UINT32                                        PM_SUB_STATE:3;
    UINT32                                            PM_STATE:2;
    UINT32                                                TYPE:3;
    UINT32                                          POWER_RAIL:3;
    UINT32                                      Reserved_31_21:11;
  } Field;
  UINT32 Value;
} PCIE_PWR_BUDGET_DATA_STRUCT;

#define PCICFG_NBIFEPF0CFG_PCIE_PWR_BUDGET_DATA_OFFSET              0x248
#define SMN_DEV0_FUNC0_NBIF0NBIO0_PCIE_PWR_BUDGET_DATA_ADDRESS        0x10140248UL
#define SMN_DEV0_FUNC0_NBIF0NBIO1_PCIE_PWR_BUDGET_DATA_ADDRESS        0x10340248UL
#define SMN_DEV0_FUNC0_NBIF1NBIO0_PCIE_PWR_BUDGET_DATA_ADDRESS        0x10240248UL
#define SMN_DEV0_FUNC0_NBIF1NBIO1_PCIE_PWR_BUDGET_DATA_ADDRESS        0x10440248UL
#define SMN_DEV0_FUNC0_NBIF2NBIO0_PCIE_PWR_BUDGET_DATA_ADDRESS        0x10540248UL
#define SMN_DEV0_FUNC0_NBIF2NBIO1_PCIE_PWR_BUDGET_DATA_ADDRESS        0x10740248UL
#define SMN_DEV1_FUNC0_NBIF0NBIO0_PCIE_PWR_BUDGET_DATA_ADDRESS        0x10148248UL
#define SMN_DEV1_FUNC0_NBIF0NBIO1_PCIE_PWR_BUDGET_DATA_ADDRESS        0x10348248UL
#define SMN_DEV1_FUNC0_NBIF1NBIO0_PCIE_PWR_BUDGET_DATA_ADDRESS        0x10248248UL
#define SMN_DEV1_FUNC0_NBIF1NBIO1_PCIE_PWR_BUDGET_DATA_ADDRESS        0x10448248UL


/***********************************************************
* Register Name : PCIE_PWR_BUDGET_DATA_SELECT
************************************************************/

#define NBIF_PWR_BUDGET_DATA_SELECT_DATA_SELECT_OFFSET         0
#define NBIF_PWR_BUDGET_DATA_SELECT_DATA_SELECT_MASK           0xff

typedef union {
  struct {
    UINT8                                         DATA_SELECT:8;
  } Field;
  UINT8 Value;
} PCIE_PWR_BUDGET_DATA_SELECT_STRUCT;

#define PCICFG_NBIFEPF0CFG_PCIE_PWR_BUDGET_DATA_SELECT_OFFSET       0x244
#define SMN_DEV0_FUNC0_NBIF0NBIO0_PCIE_PWR_BUDGET_DATA_SELECT_ADDRESS 0x10140244UL
#define SMN_DEV0_FUNC0_NBIF0NBIO1_PCIE_PWR_BUDGET_DATA_SELECT_ADDRESS 0x10340244UL
#define SMN_DEV0_FUNC0_NBIF1NBIO0_PCIE_PWR_BUDGET_DATA_SELECT_ADDRESS 0x10240244UL
#define SMN_DEV0_FUNC0_NBIF1NBIO1_PCIE_PWR_BUDGET_DATA_SELECT_ADDRESS 0x10440244UL
#define SMN_DEV0_FUNC0_NBIF2NBIO0_PCIE_PWR_BUDGET_DATA_SELECT_ADDRESS 0x10540244UL
#define SMN_DEV0_FUNC0_NBIF2NBIO1_PCIE_PWR_BUDGET_DATA_SELECT_ADDRESS 0x10740244UL
#define SMN_DEV1_FUNC0_NBIF0NBIO0_PCIE_PWR_BUDGET_DATA_SELECT_ADDRESS 0x10148244UL
#define SMN_DEV1_FUNC0_NBIF0NBIO1_PCIE_PWR_BUDGET_DATA_SELECT_ADDRESS 0x10348244UL
#define SMN_DEV1_FUNC0_NBIF1NBIO0_PCIE_PWR_BUDGET_DATA_SELECT_ADDRESS 0x10248244UL
#define SMN_DEV1_FUNC0_NBIF1NBIO1_PCIE_PWR_BUDGET_DATA_SELECT_ADDRESS 0x10448244UL


/***********************************************************
* Register Name : PCIE_PWR_BUDGET_ENH_CAP_LIST
************************************************************/

#define NBIF_PWR_BUDGET_ENH_CAP_LIST_CAP_ID_OFFSET             0
#define NBIF_PWR_BUDGET_ENH_CAP_LIST_CAP_ID_MASK               0xffff

#define NBIF_PWR_BUDGET_ENH_CAP_LIST_CAP_VER_OFFSET            16
#define NBIF_PWR_BUDGET_ENH_CAP_LIST_CAP_VER_MASK              0xf0000

#define NBIF_PWR_BUDGET_ENH_CAP_LIST_NEXT_PTR_OFFSET           20
#define NBIF_PWR_BUDGET_ENH_CAP_LIST_NEXT_PTR_MASK             0xfff00000

typedef union {
  struct {
    UINT32                                              CAP_ID:16;
    UINT32                                             CAP_VER:4;
    UINT32                                            NEXT_PTR:12;
  } Field;
  UINT32 Value;
} PCIE_PWR_BUDGET_ENH_CAP_LIST_STRUCT;

#define PCICFG_NBIFEPF0CFG_PCIE_PWR_BUDGET_ENH_CAP_LIST_OFFSET      0x240
#define SMN_DEV0_FUNC0_NBIF0NBIO0_PCIE_PWR_BUDGET_ENH_CAP_LIST_ADDRESS 0x10140240UL
#define SMN_DEV0_FUNC0_NBIF0NBIO1_PCIE_PWR_BUDGET_ENH_CAP_LIST_ADDRESS 0x10340240UL
#define SMN_DEV0_FUNC0_NBIF1NBIO0_PCIE_PWR_BUDGET_ENH_CAP_LIST_ADDRESS 0x10240240UL
#define SMN_DEV0_FUNC0_NBIF1NBIO1_PCIE_PWR_BUDGET_ENH_CAP_LIST_ADDRESS 0x10440240UL
#define SMN_DEV0_FUNC0_NBIF2NBIO0_PCIE_PWR_BUDGET_ENH_CAP_LIST_ADDRESS 0x10540240UL
#define SMN_DEV0_FUNC0_NBIF2NBIO1_PCIE_PWR_BUDGET_ENH_CAP_LIST_ADDRESS 0x10740240UL
#define SMN_DEV1_FUNC0_NBIF0NBIO0_PCIE_PWR_BUDGET_ENH_CAP_LIST_ADDRESS 0x10148240UL
#define SMN_DEV1_FUNC0_NBIF0NBIO1_PCIE_PWR_BUDGET_ENH_CAP_LIST_ADDRESS 0x10348240UL
#define SMN_DEV1_FUNC0_NBIF1NBIO0_PCIE_PWR_BUDGET_ENH_CAP_LIST_ADDRESS 0x10248240UL
#define SMN_DEV1_FUNC0_NBIF1NBIO1_PCIE_PWR_BUDGET_ENH_CAP_LIST_ADDRESS 0x10448240UL


/***********************************************************
* Register Name : PCIE_RTR_ENH_CAP_LIST
************************************************************/

#define NBIF_RTR_ENH_CAP_LIST_CAP_ID_OFFSET                    0
#define NBIF_RTR_ENH_CAP_LIST_CAP_ID_MASK                      0xffff

#define NBIF_RTR_ENH_CAP_LIST_CAP_VER_OFFSET                   16
#define NBIF_RTR_ENH_CAP_LIST_CAP_VER_MASK                     0xf0000

#define NBIF_RTR_ENH_CAP_LIST_NEXT_PTR_OFFSET                  20
#define NBIF_RTR_ENH_CAP_LIST_NEXT_PTR_MASK                    0xfff00000

typedef union {
  struct {
    UINT32                                              CAP_ID:16;
    UINT32                                             CAP_VER:4;
    UINT32                                            NEXT_PTR:12;
  } Field;
  UINT32 Value;
} PCIE_RTR_ENH_CAP_LIST_STRUCT;

#define PCICFG_NBIFEPF0CFG_PCIE_RTR_ENH_CAP_LIST_OFFSET             0x570
#define SMN_DEV0_FUNC0_NBIF0NBIO0_PCIE_RTR_ENH_CAP_LIST_ADDRESS       0x10140570UL
#define SMN_DEV0_FUNC0_NBIF0NBIO1_PCIE_RTR_ENH_CAP_LIST_ADDRESS       0x10340570UL
#define SMN_DEV0_FUNC0_NBIF1NBIO0_PCIE_RTR_ENH_CAP_LIST_ADDRESS       0x10240570UL
#define SMN_DEV0_FUNC0_NBIF1NBIO1_PCIE_RTR_ENH_CAP_LIST_ADDRESS       0x10440570UL
#define SMN_DEV0_FUNC0_NBIF2NBIO0_PCIE_RTR_ENH_CAP_LIST_ADDRESS       0x10540570UL
#define SMN_DEV0_FUNC0_NBIF2NBIO1_PCIE_RTR_ENH_CAP_LIST_ADDRESS       0x10740570UL
#define SMN_DEV1_FUNC0_NBIF0NBIO0_PCIE_RTR_ENH_CAP_LIST_ADDRESS       0x10148570UL
#define SMN_DEV1_FUNC0_NBIF0NBIO1_PCIE_RTR_ENH_CAP_LIST_ADDRESS       0x10348570UL
#define SMN_DEV1_FUNC0_NBIF1NBIO0_PCIE_RTR_ENH_CAP_LIST_ADDRESS       0x10248570UL
#define SMN_DEV1_FUNC0_NBIF1NBIO1_PCIE_RTR_ENH_CAP_LIST_ADDRESS       0x10448570UL


/***********************************************************
* Register Name : PCIE_SECONDARY_ENH_CAP_LIST
************************************************************/

#define NBIF_SECONDARY_ENH_CAP_LIST_CAP_ID_OFFSET              0
#define NBIF_SECONDARY_ENH_CAP_LIST_CAP_ID_MASK                0xffff

#define NBIF_SECONDARY_ENH_CAP_LIST_CAP_VER_OFFSET             16
#define NBIF_SECONDARY_ENH_CAP_LIST_CAP_VER_MASK               0xf0000

#define NBIF_SECONDARY_ENH_CAP_LIST_NEXT_PTR_OFFSET            20
#define NBIF_SECONDARY_ENH_CAP_LIST_NEXT_PTR_MASK              0xfff00000

typedef union {
  struct {
    UINT32                                              CAP_ID:16;
    UINT32                                             CAP_VER:4;
    UINT32                                            NEXT_PTR:12;
  } Field;
  UINT32 Value;
} PCIE_SECONDARY_ENH_CAP_LIST_STRUCT;

#define PCICFG_NBIFEPF0CFG_PCIE_SECONDARY_ENH_CAP_LIST_OFFSET       0x270
#define SMN_DEV0_FUNC0_NBIF0NBIO0_PCIE_SECONDARY_ENH_CAP_LIST_ADDRESS 0x10140270UL
#define SMN_DEV0_FUNC0_NBIF0NBIO1_PCIE_SECONDARY_ENH_CAP_LIST_ADDRESS 0x10340270UL
#define SMN_DEV0_FUNC0_NBIF1NBIO0_PCIE_SECONDARY_ENH_CAP_LIST_ADDRESS 0x10240270UL
#define SMN_DEV0_FUNC0_NBIF1NBIO1_PCIE_SECONDARY_ENH_CAP_LIST_ADDRESS 0x10440270UL
#define SMN_DEV0_FUNC0_NBIF2NBIO0_PCIE_SECONDARY_ENH_CAP_LIST_ADDRESS 0x10540270UL
#define SMN_DEV0_FUNC0_NBIF2NBIO1_PCIE_SECONDARY_ENH_CAP_LIST_ADDRESS 0x10740270UL
#define SMN_DEV1_FUNC0_NBIF0NBIO0_PCIE_SECONDARY_ENH_CAP_LIST_ADDRESS 0x10148270UL
#define SMN_DEV1_FUNC0_NBIF0NBIO1_PCIE_SECONDARY_ENH_CAP_LIST_ADDRESS 0x10348270UL
#define SMN_DEV1_FUNC0_NBIF1NBIO0_PCIE_SECONDARY_ENH_CAP_LIST_ADDRESS 0x10248270UL
#define SMN_DEV1_FUNC0_NBIF1NBIO1_PCIE_SECONDARY_ENH_CAP_LIST_ADDRESS 0x10448270UL


/***********************************************************
* Register Name : PCIE_TLP_PREFIX_LOG0
************************************************************/

#define NBIF_TLP_PREFIX_LOG0_TLP_PREFIX_OFFSET                 0
#define NBIF_TLP_PREFIX_LOG0_TLP_PREFIX_MASK                   0xffffffff

typedef union {
  struct {
    UINT32                                          TLP_PREFIX:32;
  } Field;
  UINT32 Value;
} PCIE_TLP_PREFIX_LOG0_STRUCT;

#define PCICFG_NBIFEPF0CFG_PCIE_TLP_PREFIX_LOG0_OFFSET              0x188
#define SMN_DEV0_FUNC0_NBIF0NBIO0_PCIE_TLP_PREFIX_LOG0_ADDRESS        0x10140188UL
#define SMN_DEV0_FUNC0_NBIF0NBIO1_PCIE_TLP_PREFIX_LOG0_ADDRESS        0x10340188UL
#define SMN_DEV0_FUNC0_NBIF1NBIO0_PCIE_TLP_PREFIX_LOG0_ADDRESS        0x10240188UL
#define SMN_DEV0_FUNC0_NBIF1NBIO1_PCIE_TLP_PREFIX_LOG0_ADDRESS        0x10440188UL
#define SMN_DEV0_FUNC0_NBIF2NBIO0_PCIE_TLP_PREFIX_LOG0_ADDRESS        0x10540188UL
#define SMN_DEV0_FUNC0_NBIF2NBIO1_PCIE_TLP_PREFIX_LOG0_ADDRESS        0x10740188UL
#define SMN_DEV1_FUNC0_NBIF0NBIO0_PCIE_TLP_PREFIX_LOG0_ADDRESS        0x10148188UL
#define SMN_DEV1_FUNC0_NBIF0NBIO1_PCIE_TLP_PREFIX_LOG0_ADDRESS        0x10348188UL
#define SMN_DEV1_FUNC0_NBIF1NBIO0_PCIE_TLP_PREFIX_LOG0_ADDRESS        0x10248188UL
#define SMN_DEV1_FUNC0_NBIF1NBIO1_PCIE_TLP_PREFIX_LOG0_ADDRESS        0x10448188UL


/***********************************************************
* Register Name : PCIE_TLP_PREFIX_LOG1
************************************************************/

#define NBIF_TLP_PREFIX_LOG1_TLP_PREFIX_OFFSET                 0
#define NBIF_TLP_PREFIX_LOG1_TLP_PREFIX_MASK                   0xffffffff

typedef union {
  struct {
    UINT32                                          TLP_PREFIX:32;
  } Field;
  UINT32 Value;
} PCIE_TLP_PREFIX_LOG1_STRUCT;

#define PCICFG_NBIFEPF0CFG_PCIE_TLP_PREFIX_LOG1_OFFSET              0x18c
#define SMN_DEV0_FUNC0_NBIF0NBIO0_PCIE_TLP_PREFIX_LOG1_ADDRESS        0x1014018cUL
#define SMN_DEV0_FUNC0_NBIF0NBIO1_PCIE_TLP_PREFIX_LOG1_ADDRESS        0x1034018cUL
#define SMN_DEV0_FUNC0_NBIF1NBIO0_PCIE_TLP_PREFIX_LOG1_ADDRESS        0x1024018cUL
#define SMN_DEV0_FUNC0_NBIF1NBIO1_PCIE_TLP_PREFIX_LOG1_ADDRESS        0x1044018cUL
#define SMN_DEV0_FUNC0_NBIF2NBIO0_PCIE_TLP_PREFIX_LOG1_ADDRESS        0x1054018cUL
#define SMN_DEV0_FUNC0_NBIF2NBIO1_PCIE_TLP_PREFIX_LOG1_ADDRESS        0x1074018cUL
#define SMN_DEV1_FUNC0_NBIF0NBIO0_PCIE_TLP_PREFIX_LOG1_ADDRESS        0x1014818cUL
#define SMN_DEV1_FUNC0_NBIF0NBIO1_PCIE_TLP_PREFIX_LOG1_ADDRESS        0x1034818cUL
#define SMN_DEV1_FUNC0_NBIF1NBIO0_PCIE_TLP_PREFIX_LOG1_ADDRESS        0x1024818cUL
#define SMN_DEV1_FUNC0_NBIF1NBIO1_PCIE_TLP_PREFIX_LOG1_ADDRESS        0x1044818cUL


/***********************************************************
* Register Name : PCIE_TLP_PREFIX_LOG2
************************************************************/

#define NBIF_TLP_PREFIX_LOG2_TLP_PREFIX_OFFSET                 0
#define NBIF_TLP_PREFIX_LOG2_TLP_PREFIX_MASK                   0xffffffff

typedef union {
  struct {
    UINT32                                          TLP_PREFIX:32;
  } Field;
  UINT32 Value;
} PCIE_TLP_PREFIX_LOG2_STRUCT;

#define PCICFG_NBIFEPF0CFG_PCIE_TLP_PREFIX_LOG2_OFFSET              0x190
#define SMN_DEV0_FUNC0_NBIF0NBIO0_PCIE_TLP_PREFIX_LOG2_ADDRESS        0x10140190UL
#define SMN_DEV0_FUNC0_NBIF0NBIO1_PCIE_TLP_PREFIX_LOG2_ADDRESS        0x10340190UL
#define SMN_DEV0_FUNC0_NBIF1NBIO0_PCIE_TLP_PREFIX_LOG2_ADDRESS        0x10240190UL
#define SMN_DEV0_FUNC0_NBIF1NBIO1_PCIE_TLP_PREFIX_LOG2_ADDRESS        0x10440190UL
#define SMN_DEV0_FUNC0_NBIF2NBIO0_PCIE_TLP_PREFIX_LOG2_ADDRESS        0x10540190UL
#define SMN_DEV0_FUNC0_NBIF2NBIO1_PCIE_TLP_PREFIX_LOG2_ADDRESS        0x10740190UL
#define SMN_DEV1_FUNC0_NBIF0NBIO0_PCIE_TLP_PREFIX_LOG2_ADDRESS        0x10148190UL
#define SMN_DEV1_FUNC0_NBIF0NBIO1_PCIE_TLP_PREFIX_LOG2_ADDRESS        0x10348190UL
#define SMN_DEV1_FUNC0_NBIF1NBIO0_PCIE_TLP_PREFIX_LOG2_ADDRESS        0x10248190UL
#define SMN_DEV1_FUNC0_NBIF1NBIO1_PCIE_TLP_PREFIX_LOG2_ADDRESS        0x10448190UL


/***********************************************************
* Register Name : PCIE_TLP_PREFIX_LOG3
************************************************************/

#define NBIF_TLP_PREFIX_LOG3_TLP_PREFIX_OFFSET                 0
#define NBIF_TLP_PREFIX_LOG3_TLP_PREFIX_MASK                   0xffffffff

typedef union {
  struct {
    UINT32                                          TLP_PREFIX:32;
  } Field;
  UINT32 Value;
} PCIE_TLP_PREFIX_LOG3_STRUCT;

#define PCICFG_NBIFEPF0CFG_PCIE_TLP_PREFIX_LOG3_OFFSET              0x194
#define SMN_DEV0_FUNC0_NBIF0NBIO0_PCIE_TLP_PREFIX_LOG3_ADDRESS        0x10140194UL
#define SMN_DEV0_FUNC0_NBIF0NBIO1_PCIE_TLP_PREFIX_LOG3_ADDRESS        0x10340194UL
#define SMN_DEV0_FUNC0_NBIF1NBIO0_PCIE_TLP_PREFIX_LOG3_ADDRESS        0x10240194UL
#define SMN_DEV0_FUNC0_NBIF1NBIO1_PCIE_TLP_PREFIX_LOG3_ADDRESS        0x10440194UL
#define SMN_DEV0_FUNC0_NBIF2NBIO0_PCIE_TLP_PREFIX_LOG3_ADDRESS        0x10540194UL
#define SMN_DEV0_FUNC0_NBIF2NBIO1_PCIE_TLP_PREFIX_LOG3_ADDRESS        0x10740194UL
#define SMN_DEV1_FUNC0_NBIF0NBIO0_PCIE_TLP_PREFIX_LOG3_ADDRESS        0x10148194UL
#define SMN_DEV1_FUNC0_NBIF0NBIO1_PCIE_TLP_PREFIX_LOG3_ADDRESS        0x10348194UL
#define SMN_DEV1_FUNC0_NBIF1NBIO0_PCIE_TLP_PREFIX_LOG3_ADDRESS        0x10248194UL
#define SMN_DEV1_FUNC0_NBIF1NBIO1_PCIE_TLP_PREFIX_LOG3_ADDRESS        0x10448194UL


/***********************************************************
* Register Name : PCIE_UNCORR_ERR_MASK
************************************************************/

#define NBIF_UNCORR_ERR_MASK_Reserved_3_0_OFFSET               0
#define NBIF_UNCORR_ERR_MASK_Reserved_3_0_MASK                 0xf

#define NBIF_UNCORR_ERR_MASK_DLP_ERR_MASK_OFFSET               4
#define NBIF_UNCORR_ERR_MASK_DLP_ERR_MASK_MASK                 0x10

#define NBIF_UNCORR_ERR_MASK_SURPDN_ERR_MASK_OFFSET            5
#define NBIF_UNCORR_ERR_MASK_SURPDN_ERR_MASK_MASK              0x20

#define NBIF_UNCORR_ERR_MASK_Reserved_11_6_OFFSET              6
#define NBIF_UNCORR_ERR_MASK_Reserved_11_6_MASK                0xfc0

#define NBIF_UNCORR_ERR_MASK_PSN_ERR_MASK_OFFSET               12
#define NBIF_UNCORR_ERR_MASK_PSN_ERR_MASK_MASK                 0x1000

#define NBIF_UNCORR_ERR_MASK_FC_ERR_MASK_OFFSET                13
#define NBIF_UNCORR_ERR_MASK_FC_ERR_MASK_MASK                  0x2000

#define NBIF_UNCORR_ERR_MASK_CPL_TIMEOUT_MASK_OFFSET           14
#define NBIF_UNCORR_ERR_MASK_CPL_TIMEOUT_MASK_MASK             0x4000

#define NBIF_UNCORR_ERR_MASK_CPL_ABORT_ERR_MASK_OFFSET         15
#define NBIF_UNCORR_ERR_MASK_CPL_ABORT_ERR_MASK_MASK           0x8000

#define NBIF_UNCORR_ERR_MASK_UNEXP_CPL_MASK_OFFSET             16
#define NBIF_UNCORR_ERR_MASK_UNEXP_CPL_MASK_MASK               0x10000

#define NBIF_UNCORR_ERR_MASK_RCV_OVFL_MASK_OFFSET              17
#define NBIF_UNCORR_ERR_MASK_RCV_OVFL_MASK_MASK                0x20000

#define NBIF_UNCORR_ERR_MASK_MAL_TLP_MASK_OFFSET               18
#define NBIF_UNCORR_ERR_MASK_MAL_TLP_MASK_MASK                 0x40000

#define NBIF_UNCORR_ERR_MASK_ECRC_ERR_MASK_OFFSET              19
#define NBIF_UNCORR_ERR_MASK_ECRC_ERR_MASK_MASK                0x80000

#define NBIF_UNCORR_ERR_MASK_UNSUPP_REQ_ERR_MASK_OFFSET        20
#define NBIF_UNCORR_ERR_MASK_UNSUPP_REQ_ERR_MASK_MASK          0x100000

#define NBIF_UNCORR_ERR_MASK_ACS_VIOLATION_MASK_OFFSET         21
#define NBIF_UNCORR_ERR_MASK_ACS_VIOLATION_MASK_MASK           0x200000

#define NBIF_UNCORR_ERR_MASK_UNCORR_INT_ERR_MASK_OFFSET        22
#define NBIF_UNCORR_ERR_MASK_UNCORR_INT_ERR_MASK_MASK          0x400000

#define NBIF_UNCORR_ERR_MASK_MC_BLOCKED_TLP_MASK_OFFSET        23
#define NBIF_UNCORR_ERR_MASK_MC_BLOCKED_TLP_MASK_MASK          0x800000

#define NBIF_UNCORR_ERR_MASK_ATOMICOP_EGRESS_BLOCKED_MASK_OFFSET 24
#define NBIF_UNCORR_ERR_MASK_ATOMICOP_EGRESS_BLOCKED_MASK_MASK 0x1000000

#define NBIF_UNCORR_ERR_MASK_TLP_PREFIX_BLOCKED_ERR_MASK_OFFSET 25
#define NBIF_UNCORR_ERR_MASK_TLP_PREFIX_BLOCKED_ERR_MASK_MASK  0x2000000

#define NBIF_UNCORR_ERR_MASK_POISONED_TLP_EGRESS_BLOCKED_MASK_OFFSET 26
#define NBIF_UNCORR_ERR_MASK_POISONED_TLP_EGRESS_BLOCKED_MASK_MASK 0x4000000

#define NBIF_UNCORR_ERR_MASK_Reserved_31_27_OFFSET             27
#define NBIF_UNCORR_ERR_MASK_Reserved_31_27_MASK               0xf8000000

typedef union {
  struct {
    UINT32                                        Reserved_3_0:4;
    UINT32                                        DLP_ERR_MASK:1;
    UINT32                                     SURPDN_ERR_MASK:1;
    UINT32                                       Reserved_11_6:6;
    UINT32                                        PSN_ERR_MASK:1;
    UINT32                                         FC_ERR_MASK:1;
    UINT32                                    CPL_TIMEOUT_MASK:1;
    UINT32                                  CPL_ABORT_ERR_MASK:1;
    UINT32                                      UNEXP_CPL_MASK:1;
    UINT32                                       RCV_OVFL_MASK:1;
    UINT32                                        MAL_TLP_MASK:1;
    UINT32                                       ECRC_ERR_MASK:1;
    UINT32                                 UNSUPP_REQ_ERR_MASK:1;
    UINT32                                  ACS_VIOLATION_MASK:1;
    UINT32                                 UNCORR_INT_ERR_MASK:1;
    UINT32                                 MC_BLOCKED_TLP_MASK:1;
    UINT32                        ATOMICOP_EGRESS_BLOCKED_MASK:1;
    UINT32                         TLP_PREFIX_BLOCKED_ERR_MASK:1;
    UINT32                    POISONED_TLP_EGRESS_BLOCKED_MASK:1;
    UINT32                                      Reserved_31_27:5;
  } Field;
  UINT32 Value;
} PCIE_UNCORR_ERR_MASK_STRUCT;

#define PCICFG_NBIFEPF0CFG_PCIE_UNCORR_ERR_MASK_OFFSET              0x158
#define SMN_DEV0_FUNC0_NBIF0NBIO0_PCIE_UNCORR_ERR_MASK_ADDRESS        0x10140158UL
#define SMN_DEV0_FUNC0_NBIF0NBIO1_PCIE_UNCORR_ERR_MASK_ADDRESS        0x10340158UL
#define SMN_DEV0_FUNC0_NBIF1NBIO0_PCIE_UNCORR_ERR_MASK_ADDRESS        0x10240158UL
#define SMN_DEV0_FUNC0_NBIF1NBIO1_PCIE_UNCORR_ERR_MASK_ADDRESS        0x10440158UL
#define SMN_DEV0_FUNC0_NBIF2NBIO0_PCIE_UNCORR_ERR_MASK_ADDRESS        0x10540158UL
#define SMN_DEV0_FUNC0_NBIF2NBIO1_PCIE_UNCORR_ERR_MASK_ADDRESS        0x10740158UL
#define SMN_DEV1_FUNC0_NBIF0NBIO0_PCIE_UNCORR_ERR_MASK_ADDRESS        0x10148158UL
#define SMN_DEV1_FUNC0_NBIF0NBIO1_PCIE_UNCORR_ERR_MASK_ADDRESS        0x10348158UL
#define SMN_DEV1_FUNC0_NBIF1NBIO0_PCIE_UNCORR_ERR_MASK_ADDRESS        0x10248158UL
#define SMN_DEV1_FUNC0_NBIF1NBIO1_PCIE_UNCORR_ERR_MASK_ADDRESS        0x10448158UL


/***********************************************************
* Register Name : PCIE_UNCORR_ERR_SEVERITY
************************************************************/

#define NBIF_UNCORR_ERR_SEVERITY_Reserved_3_0_OFFSET           0
#define NBIF_UNCORR_ERR_SEVERITY_Reserved_3_0_MASK             0xf

#define NBIF_UNCORR_ERR_SEVERITY_DLP_ERR_SEVERITY_OFFSET       4
#define NBIF_UNCORR_ERR_SEVERITY_DLP_ERR_SEVERITY_MASK         0x10

#define NBIF_UNCORR_ERR_SEVERITY_SURPDN_ERR_SEVERITY_OFFSET    5
#define NBIF_UNCORR_ERR_SEVERITY_SURPDN_ERR_SEVERITY_MASK      0x20

#define NBIF_UNCORR_ERR_SEVERITY_Reserved_11_6_OFFSET          6
#define NBIF_UNCORR_ERR_SEVERITY_Reserved_11_6_MASK            0xfc0

#define NBIF_UNCORR_ERR_SEVERITY_PSN_ERR_SEVERITY_OFFSET       12
#define NBIF_UNCORR_ERR_SEVERITY_PSN_ERR_SEVERITY_MASK         0x1000

#define NBIF_UNCORR_ERR_SEVERITY_FC_ERR_SEVERITY_OFFSET        13
#define NBIF_UNCORR_ERR_SEVERITY_FC_ERR_SEVERITY_MASK          0x2000

#define NBIF_UNCORR_ERR_SEVERITY_CPL_TIMEOUT_SEVERITY_OFFSET   14
#define NBIF_UNCORR_ERR_SEVERITY_CPL_TIMEOUT_SEVERITY_MASK     0x4000

#define NBIF_UNCORR_ERR_SEVERITY_CPL_ABORT_ERR_SEVERITY_OFFSET 15
#define NBIF_UNCORR_ERR_SEVERITY_CPL_ABORT_ERR_SEVERITY_MASK   0x8000

#define NBIF_UNCORR_ERR_SEVERITY_UNEXP_CPL_SEVERITY_OFFSET     16
#define NBIF_UNCORR_ERR_SEVERITY_UNEXP_CPL_SEVERITY_MASK       0x10000

#define NBIF_UNCORR_ERR_SEVERITY_RCV_OVFL_SEVERITY_OFFSET      17
#define NBIF_UNCORR_ERR_SEVERITY_RCV_OVFL_SEVERITY_MASK        0x20000

#define NBIF_UNCORR_ERR_SEVERITY_MAL_TLP_SEVERITY_OFFSET       18
#define NBIF_UNCORR_ERR_SEVERITY_MAL_TLP_SEVERITY_MASK         0x40000

#define NBIF_UNCORR_ERR_SEVERITY_ECRC_ERR_SEVERITY_OFFSET      19
#define NBIF_UNCORR_ERR_SEVERITY_ECRC_ERR_SEVERITY_MASK        0x80000

#define NBIF_UNCORR_ERR_SEVERITY_UNSUPP_REQ_ERR_SEVERITY_OFFSET 20
#define NBIF_UNCORR_ERR_SEVERITY_UNSUPP_REQ_ERR_SEVERITY_MASK  0x100000

#define NBIF_UNCORR_ERR_SEVERITY_ACS_VIOLATION_SEVERITY_OFFSET 21
#define NBIF_UNCORR_ERR_SEVERITY_ACS_VIOLATION_SEVERITY_MASK   0x200000

#define NBIF_UNCORR_ERR_SEVERITY_UNCORR_INT_ERR_SEVERITY_OFFSET 22
#define NBIF_UNCORR_ERR_SEVERITY_UNCORR_INT_ERR_SEVERITY_MASK  0x400000

#define NBIF_UNCORR_ERR_SEVERITY_MC_BLOCKED_TLP_SEVERITY_OFFSET 23
#define NBIF_UNCORR_ERR_SEVERITY_MC_BLOCKED_TLP_SEVERITY_MASK  0x800000

#define NBIF_UNCORR_ERR_SEVERITY_ATOMICOP_EGRESS_BLOCKED_SEVERITY_OFFSET 24
#define NBIF_UNCORR_ERR_SEVERITY_ATOMICOP_EGRESS_BLOCKED_SEVERITY_MASK 0x1000000

#define NBIF_UNCORR_ERR_SEVERITY_TLP_PREFIX_BLOCKED_ERR_SEVERITY_OFFSET 25
#define NBIF_UNCORR_ERR_SEVERITY_TLP_PREFIX_BLOCKED_ERR_SEVERITY_MASK 0x2000000

#define NBIF_UNCORR_ERR_SEVERITY_POISONED_TLP_EGRESS_BLOCKED_SEVERITY_OFFSET 26
#define NBIF_UNCORR_ERR_SEVERITY_POISONED_TLP_EGRESS_BLOCKED_SEVERITY_MASK 0x4000000

#define NBIF_UNCORR_ERR_SEVERITY_Reserved_31_27_OFFSET         27
#define NBIF_UNCORR_ERR_SEVERITY_Reserved_31_27_MASK           0xf8000000

typedef union {
  struct {
    UINT32                                        Reserved_3_0:4;
    UINT32                                    DLP_ERR_SEVERITY:1;
    UINT32                                 SURPDN_ERR_SEVERITY:1;
    UINT32                                       Reserved_11_6:6;
    UINT32                                    PSN_ERR_SEVERITY:1;
    UINT32                                     FC_ERR_SEVERITY:1;
    UINT32                                CPL_TIMEOUT_SEVERITY:1;
    UINT32                              CPL_ABORT_ERR_SEVERITY:1;
    UINT32                                  UNEXP_CPL_SEVERITY:1;
    UINT32                                   RCV_OVFL_SEVERITY:1;
    UINT32                                    MAL_TLP_SEVERITY:1;
    UINT32                                   ECRC_ERR_SEVERITY:1;
    UINT32                             UNSUPP_REQ_ERR_SEVERITY:1;
    UINT32                              ACS_VIOLATION_SEVERITY:1;
    UINT32                             UNCORR_INT_ERR_SEVERITY:1;
    UINT32                             MC_BLOCKED_TLP_SEVERITY:1;
    UINT32                    ATOMICOP_EGRESS_BLOCKED_SEVERITY:1;
    UINT32                     TLP_PREFIX_BLOCKED_ERR_SEVERITY:1;
    UINT32                POISONED_TLP_EGRESS_BLOCKED_SEVERITY:1;
    UINT32                                      Reserved_31_27:5;
  } Field;
  UINT32 Value;
} PCIE_UNCORR_ERR_SEVERITY_STRUCT;

#define PCICFG_NBIFEPF0CFG_PCIE_UNCORR_ERR_SEVERITY_OFFSET          0x15c
#define SMN_DEV0_FUNC0_NBIF0NBIO0_PCIE_UNCORR_ERR_SEVERITY_ADDRESS    0x1014015cUL
#define SMN_DEV0_FUNC0_NBIF0NBIO1_PCIE_UNCORR_ERR_SEVERITY_ADDRESS    0x1034015cUL
#define SMN_DEV0_FUNC0_NBIF1NBIO0_PCIE_UNCORR_ERR_SEVERITY_ADDRESS    0x1024015cUL
#define SMN_DEV0_FUNC0_NBIF1NBIO1_PCIE_UNCORR_ERR_SEVERITY_ADDRESS    0x1044015cUL
#define SMN_DEV0_FUNC0_NBIF2NBIO0_PCIE_UNCORR_ERR_SEVERITY_ADDRESS    0x1054015cUL
#define SMN_DEV0_FUNC0_NBIF2NBIO1_PCIE_UNCORR_ERR_SEVERITY_ADDRESS    0x1074015cUL
#define SMN_DEV1_FUNC0_NBIF0NBIO0_PCIE_UNCORR_ERR_SEVERITY_ADDRESS    0x1014815cUL
#define SMN_DEV1_FUNC0_NBIF0NBIO1_PCIE_UNCORR_ERR_SEVERITY_ADDRESS    0x1034815cUL
#define SMN_DEV1_FUNC0_NBIF1NBIO0_PCIE_UNCORR_ERR_SEVERITY_ADDRESS    0x1024815cUL
#define SMN_DEV1_FUNC0_NBIF1NBIO1_PCIE_UNCORR_ERR_SEVERITY_ADDRESS    0x1044815cUL


/***********************************************************
* Register Name : PCIE_UNCORR_ERR_STATUS
************************************************************/

#define NBIF_UNCORR_ERR_STATUS_Reserved_3_0_OFFSET             0
#define NBIF_UNCORR_ERR_STATUS_Reserved_3_0_MASK               0xf

#define NBIF_UNCORR_ERR_STATUS_DLP_ERR_STATUS_OFFSET           4
#define NBIF_UNCORR_ERR_STATUS_DLP_ERR_STATUS_MASK             0x10

#define NBIF_UNCORR_ERR_STATUS_SURPDN_ERR_STATUS_OFFSET        5
#define NBIF_UNCORR_ERR_STATUS_SURPDN_ERR_STATUS_MASK          0x20

#define NBIF_UNCORR_ERR_STATUS_Reserved_11_6_OFFSET            6
#define NBIF_UNCORR_ERR_STATUS_Reserved_11_6_MASK              0xfc0

#define NBIF_UNCORR_ERR_STATUS_PSN_ERR_STATUS_OFFSET           12
#define NBIF_UNCORR_ERR_STATUS_PSN_ERR_STATUS_MASK             0x1000

#define NBIF_UNCORR_ERR_STATUS_FC_ERR_STATUS_OFFSET            13
#define NBIF_UNCORR_ERR_STATUS_FC_ERR_STATUS_MASK              0x2000

#define NBIF_UNCORR_ERR_STATUS_CPL_TIMEOUT_STATUS_OFFSET       14
#define NBIF_UNCORR_ERR_STATUS_CPL_TIMEOUT_STATUS_MASK         0x4000

#define NBIF_UNCORR_ERR_STATUS_CPL_ABORT_ERR_STATUS_OFFSET     15
#define NBIF_UNCORR_ERR_STATUS_CPL_ABORT_ERR_STATUS_MASK       0x8000

#define NBIF_UNCORR_ERR_STATUS_UNEXP_CPL_STATUS_OFFSET         16
#define NBIF_UNCORR_ERR_STATUS_UNEXP_CPL_STATUS_MASK           0x10000

#define NBIF_UNCORR_ERR_STATUS_RCV_OVFL_STATUS_OFFSET          17
#define NBIF_UNCORR_ERR_STATUS_RCV_OVFL_STATUS_MASK            0x20000

#define NBIF_UNCORR_ERR_STATUS_MAL_TLP_STATUS_OFFSET           18
#define NBIF_UNCORR_ERR_STATUS_MAL_TLP_STATUS_MASK             0x40000

#define NBIF_UNCORR_ERR_STATUS_ECRC_ERR_STATUS_OFFSET          19
#define NBIF_UNCORR_ERR_STATUS_ECRC_ERR_STATUS_MASK            0x80000

#define NBIF_UNCORR_ERR_STATUS_UNSUPP_REQ_ERR_STATUS_OFFSET    20
#define NBIF_UNCORR_ERR_STATUS_UNSUPP_REQ_ERR_STATUS_MASK      0x100000

#define NBIF_UNCORR_ERR_STATUS_ACS_VIOLATION_STATUS_OFFSET     21
#define NBIF_UNCORR_ERR_STATUS_ACS_VIOLATION_STATUS_MASK       0x200000

#define NBIF_UNCORR_ERR_STATUS_UNCORR_INT_ERR_STATUS_OFFSET    22
#define NBIF_UNCORR_ERR_STATUS_UNCORR_INT_ERR_STATUS_MASK      0x400000

#define NBIF_UNCORR_ERR_STATUS_MC_BLOCKED_TLP_STATUS_OFFSET    23
#define NBIF_UNCORR_ERR_STATUS_MC_BLOCKED_TLP_STATUS_MASK      0x800000

#define NBIF_UNCORR_ERR_STATUS_ATOMICOP_EGRESS_BLOCKED_STATUS_OFFSET 24
#define NBIF_UNCORR_ERR_STATUS_ATOMICOP_EGRESS_BLOCKED_STATUS_MASK 0x1000000

#define NBIF_UNCORR_ERR_STATUS_TLP_PREFIX_BLOCKED_ERR_STATUS_OFFSET 25
#define NBIF_UNCORR_ERR_STATUS_TLP_PREFIX_BLOCKED_ERR_STATUS_MASK 0x2000000

#define NBIF_UNCORR_ERR_STATUS_POISONED_TLP_EGRESS_BLOCKED_STATUS_OFFSET 26
#define NBIF_UNCORR_ERR_STATUS_POISONED_TLP_EGRESS_BLOCKED_STATUS_MASK 0x4000000

#define NBIF_UNCORR_ERR_STATUS_Reserved_31_27_OFFSET           27
#define NBIF_UNCORR_ERR_STATUS_Reserved_31_27_MASK             0xf8000000

typedef union {
  struct {
    UINT32                                        Reserved_3_0:4;
    UINT32                                      DLP_ERR_STATUS:1;
    UINT32                                   SURPDN_ERR_STATUS:1;
    UINT32                                       Reserved_11_6:6;
    UINT32                                      PSN_ERR_STATUS:1;
    UINT32                                       FC_ERR_STATUS:1;
    UINT32                                  CPL_TIMEOUT_STATUS:1;
    UINT32                                CPL_ABORT_ERR_STATUS:1;
    UINT32                                    UNEXP_CPL_STATUS:1;
    UINT32                                     RCV_OVFL_STATUS:1;
    UINT32                                      MAL_TLP_STATUS:1;
    UINT32                                     ECRC_ERR_STATUS:1;
    UINT32                               UNSUPP_REQ_ERR_STATUS:1;
    UINT32                                ACS_VIOLATION_STATUS:1;
    UINT32                               UNCORR_INT_ERR_STATUS:1;
    UINT32                               MC_BLOCKED_TLP_STATUS:1;
    UINT32                      ATOMICOP_EGRESS_BLOCKED_STATUS:1;
    UINT32                       TLP_PREFIX_BLOCKED_ERR_STATUS:1;
    UINT32                  POISONED_TLP_EGRESS_BLOCKED_STATUS:1;
    UINT32                                      Reserved_31_27:5;
  } Field;
  UINT32 Value;
} PCIE_UNCORR_ERR_STATUS_STRUCT;

#define PCICFG_NBIFEPF0CFG_PCIE_UNCORR_ERR_STATUS_OFFSET            0x154
#define SMN_DEV0_FUNC0_NBIF0NBIO0_PCIE_UNCORR_ERR_STATUS_ADDRESS      0x10140154UL
#define SMN_DEV0_FUNC0_NBIF0NBIO1_PCIE_UNCORR_ERR_STATUS_ADDRESS      0x10340154UL
#define SMN_DEV0_FUNC0_NBIF1NBIO0_PCIE_UNCORR_ERR_STATUS_ADDRESS      0x10240154UL
#define SMN_DEV0_FUNC0_NBIF1NBIO1_PCIE_UNCORR_ERR_STATUS_ADDRESS      0x10440154UL
#define SMN_DEV0_FUNC0_NBIF2NBIO0_PCIE_UNCORR_ERR_STATUS_ADDRESS      0x10540154UL
#define SMN_DEV0_FUNC0_NBIF2NBIO1_PCIE_UNCORR_ERR_STATUS_ADDRESS      0x10740154UL
#define SMN_DEV1_FUNC0_NBIF0NBIO0_PCIE_UNCORR_ERR_STATUS_ADDRESS      0x10148154UL
#define SMN_DEV1_FUNC0_NBIF0NBIO1_PCIE_UNCORR_ERR_STATUS_ADDRESS      0x10348154UL
#define SMN_DEV1_FUNC0_NBIF1NBIO0_PCIE_UNCORR_ERR_STATUS_ADDRESS      0x10248154UL
#define SMN_DEV1_FUNC0_NBIF1NBIO1_PCIE_UNCORR_ERR_STATUS_ADDRESS      0x10448154UL


/***********************************************************
* Register Name : PCIE_VC0_RESOURCE_CAP
************************************************************/

#define NBIF_VC0_RESOURCE_CAP_PORT_ARB_CAP_OFFSET              0
#define NBIF_VC0_RESOURCE_CAP_PORT_ARB_CAP_MASK                0xff

#define NBIF_VC0_RESOURCE_CAP_Reserved_14_8_OFFSET             8
#define NBIF_VC0_RESOURCE_CAP_Reserved_14_8_MASK               0x7f00

#define NBIF_VC0_RESOURCE_CAP_REJECT_SNOOP_TRANS_OFFSET        15
#define NBIF_VC0_RESOURCE_CAP_REJECT_SNOOP_TRANS_MASK          0x8000

#define NBIF_VC0_RESOURCE_CAP_MAX_TIME_SLOTS_OFFSET            16
#define NBIF_VC0_RESOURCE_CAP_MAX_TIME_SLOTS_MASK              0x7f0000

#define NBIF_VC0_RESOURCE_CAP_Reserved_23_23_OFFSET            23
#define NBIF_VC0_RESOURCE_CAP_Reserved_23_23_MASK              0x800000

#define NBIF_VC0_RESOURCE_CAP_PORT_ARB_TABLE_OFFSET_OFFSET     24
#define NBIF_VC0_RESOURCE_CAP_PORT_ARB_TABLE_OFFSET_MASK       0xff000000

typedef union {
  struct {
    UINT32                                        PORT_ARB_CAP:8;
    UINT32                                       Reserved_14_8:7;
    UINT32                                  REJECT_SNOOP_TRANS:1;
    UINT32                                      MAX_TIME_SLOTS:7;
    UINT32                                      Reserved_23_23:1;
    UINT32                               PORT_ARB_TABLE_OFFSET:8;
  } Field;
  UINT32 Value;
} PCIE_VC0_RESOURCE_CAP_STRUCT;

#define PCICFG_NBIFEPF0CFG_PCIE_VC0_RESOURCE_CAP_OFFSET             0x120
#define SMN_DEV0_FUNC0_NBIF0NBIO0_PCIE_VC0_RESOURCE_CAP_ADDRESS       0x10140120UL
#define SMN_DEV0_FUNC0_NBIF0NBIO1_PCIE_VC0_RESOURCE_CAP_ADDRESS       0x10340120UL
#define SMN_DEV0_FUNC0_NBIF1NBIO0_PCIE_VC0_RESOURCE_CAP_ADDRESS       0x10240120UL
#define SMN_DEV0_FUNC0_NBIF1NBIO1_PCIE_VC0_RESOURCE_CAP_ADDRESS       0x10440120UL
#define SMN_DEV0_FUNC0_NBIF2NBIO0_PCIE_VC0_RESOURCE_CAP_ADDRESS       0x10540120UL
#define SMN_DEV0_FUNC0_NBIF2NBIO1_PCIE_VC0_RESOURCE_CAP_ADDRESS       0x10740120UL
#define SMN_DEV1_FUNC0_NBIF0NBIO0_PCIE_VC0_RESOURCE_CAP_ADDRESS       0x10148120UL
#define SMN_DEV1_FUNC0_NBIF0NBIO1_PCIE_VC0_RESOURCE_CAP_ADDRESS       0x10348120UL
#define SMN_DEV1_FUNC0_NBIF1NBIO0_PCIE_VC0_RESOURCE_CAP_ADDRESS       0x10248120UL
#define SMN_DEV1_FUNC0_NBIF1NBIO1_PCIE_VC0_RESOURCE_CAP_ADDRESS       0x10448120UL


/***********************************************************
* Register Name : PCIE_VC0_RESOURCE_CNTL
************************************************************/

#define NBIF_VC0_RESOURCE_CNTL_TC_VC_MAP_TC0_OFFSET            0
#define NBIF_VC0_RESOURCE_CNTL_TC_VC_MAP_TC0_MASK              0x1

#define NBIF_VC0_RESOURCE_CNTL_TC_VC_MAP_TC1_7_OFFSET          1
#define NBIF_VC0_RESOURCE_CNTL_TC_VC_MAP_TC1_7_MASK            0xfe

#define NBIF_VC0_RESOURCE_CNTL_Reserved_15_8_OFFSET            8
#define NBIF_VC0_RESOURCE_CNTL_Reserved_15_8_MASK              0xff00

#define NBIF_VC0_RESOURCE_CNTL_LOAD_PORT_ARB_TABLE_OFFSET      16
#define NBIF_VC0_RESOURCE_CNTL_LOAD_PORT_ARB_TABLE_MASK        0x10000

#define NBIF_VC0_RESOURCE_CNTL_PORT_ARB_SELECT_OFFSET          17
#define NBIF_VC0_RESOURCE_CNTL_PORT_ARB_SELECT_MASK            0xe0000

#define NBIF_VC0_RESOURCE_CNTL_Reserved_23_20_OFFSET           20
#define NBIF_VC0_RESOURCE_CNTL_Reserved_23_20_MASK             0xf00000

#define NBIF_VC0_RESOURCE_CNTL_VC_ID_OFFSET                    24
#define NBIF_VC0_RESOURCE_CNTL_VC_ID_MASK                      0x7000000

#define NBIF_VC0_RESOURCE_CNTL_Reserved_30_27_OFFSET           27
#define NBIF_VC0_RESOURCE_CNTL_Reserved_30_27_MASK             0x78000000

#define NBIF_VC0_RESOURCE_CNTL_VC_ENABLE_OFFSET                31
#define NBIF_VC0_RESOURCE_CNTL_VC_ENABLE_MASK                  0x80000000

typedef union {
  struct {
    UINT32                                       TC_VC_MAP_TC0:1;
    UINT32                                     TC_VC_MAP_TC1_7:7;
    UINT32                                       Reserved_15_8:8;
    UINT32                                 LOAD_PORT_ARB_TABLE:1;
    UINT32                                     PORT_ARB_SELECT:3;
    UINT32                                      Reserved_23_20:4;
    UINT32                                               VC_ID:3;
    UINT32                                      Reserved_30_27:4;
    UINT32                                           VC_ENABLE:1;
  } Field;
  UINT32 Value;
} PCIE_VC0_RESOURCE_CNTL_STRUCT;

#define PCICFG_NBIFEPF0CFG_PCIE_VC0_RESOURCE_CNTL_OFFSET            0x124
#define SMN_DEV0_FUNC0_NBIF0NBIO0_PCIE_VC0_RESOURCE_CNTL_ADDRESS      0x10140124UL
#define SMN_DEV0_FUNC0_NBIF0NBIO1_PCIE_VC0_RESOURCE_CNTL_ADDRESS      0x10340124UL
#define SMN_DEV0_FUNC0_NBIF1NBIO0_PCIE_VC0_RESOURCE_CNTL_ADDRESS      0x10240124UL
#define SMN_DEV0_FUNC0_NBIF1NBIO1_PCIE_VC0_RESOURCE_CNTL_ADDRESS      0x10440124UL
#define SMN_DEV0_FUNC0_NBIF2NBIO0_PCIE_VC0_RESOURCE_CNTL_ADDRESS      0x10540124UL
#define SMN_DEV0_FUNC0_NBIF2NBIO1_PCIE_VC0_RESOURCE_CNTL_ADDRESS      0x10740124UL
#define SMN_DEV1_FUNC0_NBIF0NBIO0_PCIE_VC0_RESOURCE_CNTL_ADDRESS      0x10148124UL
#define SMN_DEV1_FUNC0_NBIF0NBIO1_PCIE_VC0_RESOURCE_CNTL_ADDRESS      0x10348124UL
#define SMN_DEV1_FUNC0_NBIF1NBIO0_PCIE_VC0_RESOURCE_CNTL_ADDRESS      0x10248124UL
#define SMN_DEV1_FUNC0_NBIF1NBIO1_PCIE_VC0_RESOURCE_CNTL_ADDRESS      0x10448124UL


/***********************************************************
* Register Name : PCIE_VC0_RESOURCE_STATUS
************************************************************/

#define NBIF_VC0_RESOURCE_STATUS_PORT_ARB_TABLE_STATUS_OFFSET  0
#define NBIF_VC0_RESOURCE_STATUS_PORT_ARB_TABLE_STATUS_MASK    0x1

#define NBIF_VC0_RESOURCE_STATUS_VC_NEGOTIATION_PENDING_OFFSET 1
#define NBIF_VC0_RESOURCE_STATUS_VC_NEGOTIATION_PENDING_MASK   0x2

#define NBIF_VC0_RESOURCE_STATUS_Reserved_15_2_OFFSET          2
#define NBIF_VC0_RESOURCE_STATUS_Reserved_15_2_MASK            0xfffc

typedef union {
  struct {
    UINT16                               PORT_ARB_TABLE_STATUS:1;
    UINT16                              VC_NEGOTIATION_PENDING:1;
    UINT16                                       Reserved_15_2:14;
  } Field;
  UINT16 Value;
} PCIE_VC0_RESOURCE_STATUS_STRUCT;

#define PCICFG_NBIFEPF0CFG_PCIE_VC0_RESOURCE_STATUS_OFFSET          0x12a
#define SMN_DEV0_FUNC0_NBIF0NBIO0_PCIE_VC0_RESOURCE_STATUS_ADDRESS    0x1014012aUL
#define SMN_DEV0_FUNC0_NBIF0NBIO1_PCIE_VC0_RESOURCE_STATUS_ADDRESS    0x1034012aUL
#define SMN_DEV0_FUNC0_NBIF1NBIO0_PCIE_VC0_RESOURCE_STATUS_ADDRESS    0x1024012aUL
#define SMN_DEV0_FUNC0_NBIF1NBIO1_PCIE_VC0_RESOURCE_STATUS_ADDRESS    0x1044012aUL
#define SMN_DEV0_FUNC0_NBIF2NBIO0_PCIE_VC0_RESOURCE_STATUS_ADDRESS    0x1054012aUL
#define SMN_DEV0_FUNC0_NBIF2NBIO1_PCIE_VC0_RESOURCE_STATUS_ADDRESS    0x1074012aUL
#define SMN_DEV1_FUNC0_NBIF0NBIO0_PCIE_VC0_RESOURCE_STATUS_ADDRESS    0x1014812aUL
#define SMN_DEV1_FUNC0_NBIF0NBIO1_PCIE_VC0_RESOURCE_STATUS_ADDRESS    0x1034812aUL
#define SMN_DEV1_FUNC0_NBIF1NBIO0_PCIE_VC0_RESOURCE_STATUS_ADDRESS    0x1024812aUL
#define SMN_DEV1_FUNC0_NBIF1NBIO1_PCIE_VC0_RESOURCE_STATUS_ADDRESS    0x1044812aUL


/***********************************************************
* Register Name : PCIE_VC1_RESOURCE_CAP
************************************************************/

#define NBIF_VC1_RESOURCE_CAP_PORT_ARB_CAP_OFFSET              0
#define NBIF_VC1_RESOURCE_CAP_PORT_ARB_CAP_MASK                0xff

#define NBIF_VC1_RESOURCE_CAP_Reserved_14_8_OFFSET             8
#define NBIF_VC1_RESOURCE_CAP_Reserved_14_8_MASK               0x7f00

#define NBIF_VC1_RESOURCE_CAP_REJECT_SNOOP_TRANS_OFFSET        15
#define NBIF_VC1_RESOURCE_CAP_REJECT_SNOOP_TRANS_MASK          0x8000

#define NBIF_VC1_RESOURCE_CAP_MAX_TIME_SLOTS_OFFSET            16
#define NBIF_VC1_RESOURCE_CAP_MAX_TIME_SLOTS_MASK              0x3f0000

#define NBIF_VC1_RESOURCE_CAP_Reserved_23_22_OFFSET            22
#define NBIF_VC1_RESOURCE_CAP_Reserved_23_22_MASK              0xc00000

#define NBIF_VC1_RESOURCE_CAP_PORT_ARB_TABLE_OFFSET_OFFSET     24
#define NBIF_VC1_RESOURCE_CAP_PORT_ARB_TABLE_OFFSET_MASK       0xff000000

typedef union {
  struct {
    UINT32                                        PORT_ARB_CAP:8;
    UINT32                                       Reserved_14_8:7;
    UINT32                                  REJECT_SNOOP_TRANS:1;
    UINT32                                      MAX_TIME_SLOTS:6;
    UINT32                                      Reserved_23_22:2;
    UINT32                               PORT_ARB_TABLE_OFFSET:8;
  } Field;
  UINT32 Value;
} PCIE_VC1_RESOURCE_CAP_STRUCT;

#define PCICFG_NBIFEPF0CFG_PCIE_VC1_RESOURCE_CAP_OFFSET             0x12c
#define SMN_DEV0_FUNC0_NBIF0NBIO0_PCIE_VC1_RESOURCE_CAP_ADDRESS       0x1014012cUL
#define SMN_DEV0_FUNC0_NBIF0NBIO1_PCIE_VC1_RESOURCE_CAP_ADDRESS       0x1034012cUL
#define SMN_DEV0_FUNC0_NBIF1NBIO0_PCIE_VC1_RESOURCE_CAP_ADDRESS       0x1024012cUL
#define SMN_DEV0_FUNC0_NBIF1NBIO1_PCIE_VC1_RESOURCE_CAP_ADDRESS       0x1044012cUL
#define SMN_DEV0_FUNC0_NBIF2NBIO0_PCIE_VC1_RESOURCE_CAP_ADDRESS       0x1054012cUL
#define SMN_DEV0_FUNC0_NBIF2NBIO1_PCIE_VC1_RESOURCE_CAP_ADDRESS       0x1074012cUL
#define SMN_DEV1_FUNC0_NBIF0NBIO0_PCIE_VC1_RESOURCE_CAP_ADDRESS       0x1014812cUL
#define SMN_DEV1_FUNC0_NBIF0NBIO1_PCIE_VC1_RESOURCE_CAP_ADDRESS       0x1034812cUL
#define SMN_DEV1_FUNC0_NBIF1NBIO0_PCIE_VC1_RESOURCE_CAP_ADDRESS       0x1024812cUL
#define SMN_DEV1_FUNC0_NBIF1NBIO1_PCIE_VC1_RESOURCE_CAP_ADDRESS       0x1044812cUL


/***********************************************************
* Register Name : PCIE_VC1_RESOURCE_CNTL
************************************************************/

#define NBIF_VC1_RESOURCE_CNTL_TC_VC_MAP_TC0_OFFSET            0
#define NBIF_VC1_RESOURCE_CNTL_TC_VC_MAP_TC0_MASK              0x1

#define NBIF_VC1_RESOURCE_CNTL_TC_VC_MAP_TC1_7_OFFSET          1
#define NBIF_VC1_RESOURCE_CNTL_TC_VC_MAP_TC1_7_MASK            0xfe

#define NBIF_VC1_RESOURCE_CNTL_Reserved_15_8_OFFSET            8
#define NBIF_VC1_RESOURCE_CNTL_Reserved_15_8_MASK              0xff00

#define NBIF_VC1_RESOURCE_CNTL_LOAD_PORT_ARB_TABLE_OFFSET      16
#define NBIF_VC1_RESOURCE_CNTL_LOAD_PORT_ARB_TABLE_MASK        0x10000

#define NBIF_VC1_RESOURCE_CNTL_PORT_ARB_SELECT_OFFSET          17
#define NBIF_VC1_RESOURCE_CNTL_PORT_ARB_SELECT_MASK            0xe0000

#define NBIF_VC1_RESOURCE_CNTL_Reserved_23_20_OFFSET           20
#define NBIF_VC1_RESOURCE_CNTL_Reserved_23_20_MASK             0xf00000

#define NBIF_VC1_RESOURCE_CNTL_VC_ID_OFFSET                    24
#define NBIF_VC1_RESOURCE_CNTL_VC_ID_MASK                      0x7000000

#define NBIF_VC1_RESOURCE_CNTL_Reserved_30_27_OFFSET           27
#define NBIF_VC1_RESOURCE_CNTL_Reserved_30_27_MASK             0x78000000

#define NBIF_VC1_RESOURCE_CNTL_VC_ENABLE_OFFSET                31
#define NBIF_VC1_RESOURCE_CNTL_VC_ENABLE_MASK                  0x80000000

typedef union {
  struct {
    UINT32                                       TC_VC_MAP_TC0:1;
    UINT32                                     TC_VC_MAP_TC1_7:7;
    UINT32                                       Reserved_15_8:8;
    UINT32                                 LOAD_PORT_ARB_TABLE:1;
    UINT32                                     PORT_ARB_SELECT:3;
    UINT32                                      Reserved_23_20:4;
    UINT32                                               VC_ID:3;
    UINT32                                      Reserved_30_27:4;
    UINT32                                           VC_ENABLE:1;
  } Field;
  UINT32 Value;
} PCIE_VC1_RESOURCE_CNTL_STRUCT;

#define PCICFG_NBIFEPF0CFG_PCIE_VC1_RESOURCE_CNTL_OFFSET            0x130
#define SMN_DEV0_FUNC0_NBIF0NBIO0_PCIE_VC1_RESOURCE_CNTL_ADDRESS      0x10140130UL
#define SMN_DEV0_FUNC0_NBIF0NBIO1_PCIE_VC1_RESOURCE_CNTL_ADDRESS      0x10340130UL
#define SMN_DEV0_FUNC0_NBIF1NBIO0_PCIE_VC1_RESOURCE_CNTL_ADDRESS      0x10240130UL
#define SMN_DEV0_FUNC0_NBIF1NBIO1_PCIE_VC1_RESOURCE_CNTL_ADDRESS      0x10440130UL
#define SMN_DEV0_FUNC0_NBIF2NBIO0_PCIE_VC1_RESOURCE_CNTL_ADDRESS      0x10540130UL
#define SMN_DEV0_FUNC0_NBIF2NBIO1_PCIE_VC1_RESOURCE_CNTL_ADDRESS      0x10740130UL
#define SMN_DEV1_FUNC0_NBIF0NBIO0_PCIE_VC1_RESOURCE_CNTL_ADDRESS      0x10148130UL
#define SMN_DEV1_FUNC0_NBIF0NBIO1_PCIE_VC1_RESOURCE_CNTL_ADDRESS      0x10348130UL
#define SMN_DEV1_FUNC0_NBIF1NBIO0_PCIE_VC1_RESOURCE_CNTL_ADDRESS      0x10248130UL
#define SMN_DEV1_FUNC0_NBIF1NBIO1_PCIE_VC1_RESOURCE_CNTL_ADDRESS      0x10448130UL


/***********************************************************
* Register Name : PCIE_VC1_RESOURCE_STATUS
************************************************************/

#define NBIF_VC1_RESOURCE_STATUS_PORT_ARB_TABLE_STATUS_OFFSET  0
#define NBIF_VC1_RESOURCE_STATUS_PORT_ARB_TABLE_STATUS_MASK    0x1

#define NBIF_VC1_RESOURCE_STATUS_VC_NEGOTIATION_PENDING_OFFSET 1
#define NBIF_VC1_RESOURCE_STATUS_VC_NEGOTIATION_PENDING_MASK   0x2

#define NBIF_VC1_RESOURCE_STATUS_Reserved_15_2_OFFSET          2
#define NBIF_VC1_RESOURCE_STATUS_Reserved_15_2_MASK            0xfffc

typedef union {
  struct {
    UINT16                               PORT_ARB_TABLE_STATUS:1;
    UINT16                              VC_NEGOTIATION_PENDING:1;
    UINT16                                       Reserved_15_2:14;
  } Field;
  UINT16 Value;
} PCIE_VC1_RESOURCE_STATUS_STRUCT;

#define PCICFG_NBIFEPF0CFG_PCIE_VC1_RESOURCE_STATUS_OFFSET          0x136
#define SMN_DEV0_FUNC0_NBIF0NBIO0_PCIE_VC1_RESOURCE_STATUS_ADDRESS    0x10140136UL
#define SMN_DEV0_FUNC0_NBIF0NBIO1_PCIE_VC1_RESOURCE_STATUS_ADDRESS    0x10340136UL
#define SMN_DEV0_FUNC0_NBIF1NBIO0_PCIE_VC1_RESOURCE_STATUS_ADDRESS    0x10240136UL
#define SMN_DEV0_FUNC0_NBIF1NBIO1_PCIE_VC1_RESOURCE_STATUS_ADDRESS    0x10440136UL
#define SMN_DEV0_FUNC0_NBIF2NBIO0_PCIE_VC1_RESOURCE_STATUS_ADDRESS    0x10540136UL
#define SMN_DEV0_FUNC0_NBIF2NBIO1_PCIE_VC1_RESOURCE_STATUS_ADDRESS    0x10740136UL
#define SMN_DEV1_FUNC0_NBIF0NBIO0_PCIE_VC1_RESOURCE_STATUS_ADDRESS    0x10148136UL
#define SMN_DEV1_FUNC0_NBIF0NBIO1_PCIE_VC1_RESOURCE_STATUS_ADDRESS    0x10348136UL
#define SMN_DEV1_FUNC0_NBIF1NBIO0_PCIE_VC1_RESOURCE_STATUS_ADDRESS    0x10248136UL
#define SMN_DEV1_FUNC0_NBIF1NBIO1_PCIE_VC1_RESOURCE_STATUS_ADDRESS    0x10448136UL


/***********************************************************
* Register Name : PCIE_VC_ENH_CAP_LIST
************************************************************/

#define NBIF_VC_ENH_CAP_LIST_CAP_ID_OFFSET                     0
#define NBIF_VC_ENH_CAP_LIST_CAP_ID_MASK                       0xffff

#define NBIF_VC_ENH_CAP_LIST_CAP_VER_OFFSET                    16
#define NBIF_VC_ENH_CAP_LIST_CAP_VER_MASK                      0xf0000

#define NBIF_VC_ENH_CAP_LIST_NEXT_PTR_OFFSET                   20
#define NBIF_VC_ENH_CAP_LIST_NEXT_PTR_MASK                     0xfff00000

typedef union {
  struct {
    UINT32                                              CAP_ID:16;
    UINT32                                             CAP_VER:4;
    UINT32                                            NEXT_PTR:12;
  } Field;
  UINT32 Value;
} PCIE_VC_ENH_CAP_LIST_STRUCT;

#define PCICFG_NBIFEPF0CFG_PCIE_VC_ENH_CAP_LIST_OFFSET              0x110
#define SMN_DEV0_FUNC0_NBIF0NBIO0_PCIE_VC_ENH_CAP_LIST_ADDRESS        0x10140110UL
#define SMN_DEV0_FUNC0_NBIF0NBIO1_PCIE_VC_ENH_CAP_LIST_ADDRESS        0x10340110UL
#define SMN_DEV0_FUNC0_NBIF1NBIO0_PCIE_VC_ENH_CAP_LIST_ADDRESS        0x10240110UL
#define SMN_DEV0_FUNC0_NBIF1NBIO1_PCIE_VC_ENH_CAP_LIST_ADDRESS        0x10440110UL
#define SMN_DEV0_FUNC0_NBIF2NBIO0_PCIE_VC_ENH_CAP_LIST_ADDRESS        0x10540110UL
#define SMN_DEV0_FUNC0_NBIF2NBIO1_PCIE_VC_ENH_CAP_LIST_ADDRESS        0x10740110UL
#define SMN_DEV1_FUNC0_NBIF0NBIO0_PCIE_VC_ENH_CAP_LIST_ADDRESS        0x10148110UL
#define SMN_DEV1_FUNC0_NBIF0NBIO1_PCIE_VC_ENH_CAP_LIST_ADDRESS        0x10348110UL
#define SMN_DEV1_FUNC0_NBIF1NBIO0_PCIE_VC_ENH_CAP_LIST_ADDRESS        0x10248110UL
#define SMN_DEV1_FUNC0_NBIF1NBIO1_PCIE_VC_ENH_CAP_LIST_ADDRESS        0x10448110UL


/***********************************************************
* Register Name : PCIE_VENDOR_SPECIFIC1
************************************************************/

#define NBIF_VENDOR_SPECIFIC1_SCRATCH_OFFSET                   0
#define NBIF_VENDOR_SPECIFIC1_SCRATCH_MASK                     0xffffffff

typedef union {
  struct {
    UINT32                                             SCRATCH:32;
  } Field;
  UINT32 Value;
} PCIE_VENDOR_SPECIFIC1_STRUCT;

#define PCICFG_NBIFEPF0CFG_PCIE_VENDOR_SPECIFIC1_OFFSET             0x108
#define SMN_DEV0_FUNC0_NBIF0NBIO0_PCIE_VENDOR_SPECIFIC1_ADDRESS       0x10140108UL
#define SMN_DEV0_FUNC0_NBIF0NBIO1_PCIE_VENDOR_SPECIFIC1_ADDRESS       0x10340108UL
#define SMN_DEV0_FUNC0_NBIF1NBIO0_PCIE_VENDOR_SPECIFIC1_ADDRESS       0x10240108UL
#define SMN_DEV0_FUNC0_NBIF1NBIO1_PCIE_VENDOR_SPECIFIC1_ADDRESS       0x10440108UL
#define SMN_DEV0_FUNC0_NBIF2NBIO0_PCIE_VENDOR_SPECIFIC1_ADDRESS       0x10540108UL
#define SMN_DEV0_FUNC0_NBIF2NBIO1_PCIE_VENDOR_SPECIFIC1_ADDRESS       0x10740108UL
#define SMN_DEV1_FUNC0_NBIF0NBIO0_PCIE_VENDOR_SPECIFIC1_ADDRESS       0x10148108UL
#define SMN_DEV1_FUNC0_NBIF0NBIO1_PCIE_VENDOR_SPECIFIC1_ADDRESS       0x10348108UL
#define SMN_DEV1_FUNC0_NBIF1NBIO0_PCIE_VENDOR_SPECIFIC1_ADDRESS       0x10248108UL
#define SMN_DEV1_FUNC0_NBIF1NBIO1_PCIE_VENDOR_SPECIFIC1_ADDRESS       0x10448108UL


/***********************************************************
* Register Name : PCIE_VENDOR_SPECIFIC2
************************************************************/

#define NBIF_VENDOR_SPECIFIC2_SCRATCH_OFFSET                   0
#define NBIF_VENDOR_SPECIFIC2_SCRATCH_MASK                     0xffffffff

typedef union {
  struct {
    UINT32                                             SCRATCH:32;
  } Field;
  UINT32 Value;
} PCIE_VENDOR_SPECIFIC2_STRUCT;

#define PCICFG_NBIFEPF0CFG_PCIE_VENDOR_SPECIFIC2_OFFSET             0x10c
#define SMN_DEV0_FUNC0_NBIF0NBIO0_PCIE_VENDOR_SPECIFIC2_ADDRESS       0x1014010cUL
#define SMN_DEV0_FUNC0_NBIF0NBIO1_PCIE_VENDOR_SPECIFIC2_ADDRESS       0x1034010cUL
#define SMN_DEV0_FUNC0_NBIF1NBIO0_PCIE_VENDOR_SPECIFIC2_ADDRESS       0x1024010cUL
#define SMN_DEV0_FUNC0_NBIF1NBIO1_PCIE_VENDOR_SPECIFIC2_ADDRESS       0x1044010cUL
#define SMN_DEV0_FUNC0_NBIF2NBIO0_PCIE_VENDOR_SPECIFIC2_ADDRESS       0x1054010cUL
#define SMN_DEV0_FUNC0_NBIF2NBIO1_PCIE_VENDOR_SPECIFIC2_ADDRESS       0x1074010cUL
#define SMN_DEV1_FUNC0_NBIF0NBIO0_PCIE_VENDOR_SPECIFIC2_ADDRESS       0x1014810cUL
#define SMN_DEV1_FUNC0_NBIF0NBIO1_PCIE_VENDOR_SPECIFIC2_ADDRESS       0x1034810cUL
#define SMN_DEV1_FUNC0_NBIF1NBIO0_PCIE_VENDOR_SPECIFIC2_ADDRESS       0x1024810cUL
#define SMN_DEV1_FUNC0_NBIF1NBIO1_PCIE_VENDOR_SPECIFIC2_ADDRESS       0x1044810cUL


/***********************************************************
* Register Name : PCIE_VENDOR_SPECIFIC_ENH_CAP_LIST
************************************************************/

#define NBIF_VENDOR_SPECIFIC_ENH_CAP_LIST_CAP_ID_OFFSET        0
#define NBIF_VENDOR_SPECIFIC_ENH_CAP_LIST_CAP_ID_MASK          0xffff

#define NBIF_VENDOR_SPECIFIC_ENH_CAP_LIST_CAP_VER_OFFSET       16
#define NBIF_VENDOR_SPECIFIC_ENH_CAP_LIST_CAP_VER_MASK         0xf0000

#define NBIF_VENDOR_SPECIFIC_ENH_CAP_LIST_NEXT_PTR_OFFSET      20
#define NBIF_VENDOR_SPECIFIC_ENH_CAP_LIST_NEXT_PTR_MASK        0xfff00000

typedef union {
  struct {
    UINT32                                              CAP_ID:16;
    UINT32                                             CAP_VER:4;
    UINT32                                            NEXT_PTR:12;
  } Field;
  UINT32 Value;
} PCIE_VENDOR_SPECIFIC_ENH_CAP_LIST_STRUCT;

#define PCICFG_NBIFEPF0CFG_PCIE_VENDOR_SPECIFIC_ENH_CAP_LIST_OFFSET 0x100
#define SMN_DEV0_FUNC0_NBIF0NBIO0_PCIE_VENDOR_SPECIFIC_ENH_CAP_LIST_ADDRESS 0x10140100UL
#define SMN_DEV0_FUNC0_NBIF0NBIO1_PCIE_VENDOR_SPECIFIC_ENH_CAP_LIST_ADDRESS 0x10340100UL
#define SMN_DEV0_FUNC0_NBIF1NBIO0_PCIE_VENDOR_SPECIFIC_ENH_CAP_LIST_ADDRESS 0x10240100UL
#define SMN_DEV0_FUNC0_NBIF1NBIO1_PCIE_VENDOR_SPECIFIC_ENH_CAP_LIST_ADDRESS 0x10440100UL
#define SMN_DEV0_FUNC0_NBIF2NBIO0_PCIE_VENDOR_SPECIFIC_ENH_CAP_LIST_ADDRESS 0x10540100UL
#define SMN_DEV0_FUNC0_NBIF2NBIO1_PCIE_VENDOR_SPECIFIC_ENH_CAP_LIST_ADDRESS 0x10740100UL
#define SMN_DEV1_FUNC0_NBIF0NBIO0_PCIE_VENDOR_SPECIFIC_ENH_CAP_LIST_ADDRESS 0x10148100UL
#define SMN_DEV1_FUNC0_NBIF0NBIO1_PCIE_VENDOR_SPECIFIC_ENH_CAP_LIST_ADDRESS 0x10348100UL
#define SMN_DEV1_FUNC0_NBIF1NBIO0_PCIE_VENDOR_SPECIFIC_ENH_CAP_LIST_ADDRESS 0x10248100UL
#define SMN_DEV1_FUNC0_NBIF1NBIO1_PCIE_VENDOR_SPECIFIC_ENH_CAP_LIST_ADDRESS 0x10448100UL


/***********************************************************
* Register Name : PCIE_VENDOR_SPECIFIC_HDR
************************************************************/

#define NBIF_VENDOR_SPECIFIC_HDR_VSEC_ID_OFFSET                0
#define NBIF_VENDOR_SPECIFIC_HDR_VSEC_ID_MASK                  0xffff

#define NBIF_VENDOR_SPECIFIC_HDR_VSEC_REV_OFFSET               16
#define NBIF_VENDOR_SPECIFIC_HDR_VSEC_REV_MASK                 0xf0000

#define NBIF_VENDOR_SPECIFIC_HDR_VSEC_LENGTH_OFFSET            20
#define NBIF_VENDOR_SPECIFIC_HDR_VSEC_LENGTH_MASK              0xfff00000

typedef union {
  struct {
    UINT32                                             VSEC_ID:16;
    UINT32                                            VSEC_REV:4;
    UINT32                                         VSEC_LENGTH:12;
  } Field;
  UINT32 Value;
} PCIE_VENDOR_SPECIFIC_HDR_STRUCT;

#define PCICFG_NBIFEPF0CFG_PCIE_VENDOR_SPECIFIC_HDR_OFFSET          0x104
#define SMN_DEV0_FUNC0_NBIF0NBIO0_PCIE_VENDOR_SPECIFIC_HDR_ADDRESS    0x10140104UL
#define SMN_DEV0_FUNC0_NBIF0NBIO1_PCIE_VENDOR_SPECIFIC_HDR_ADDRESS    0x10340104UL
#define SMN_DEV0_FUNC0_NBIF1NBIO0_PCIE_VENDOR_SPECIFIC_HDR_ADDRESS    0x10240104UL
#define SMN_DEV0_FUNC0_NBIF1NBIO1_PCIE_VENDOR_SPECIFIC_HDR_ADDRESS    0x10440104UL
#define SMN_DEV0_FUNC0_NBIF2NBIO0_PCIE_VENDOR_SPECIFIC_HDR_ADDRESS    0x10540104UL
#define SMN_DEV0_FUNC0_NBIF2NBIO1_PCIE_VENDOR_SPECIFIC_HDR_ADDRESS    0x10740104UL
#define SMN_DEV1_FUNC0_NBIF0NBIO0_PCIE_VENDOR_SPECIFIC_HDR_ADDRESS    0x10148104UL
#define SMN_DEV1_FUNC0_NBIF0NBIO1_PCIE_VENDOR_SPECIFIC_HDR_ADDRESS    0x10348104UL
#define SMN_DEV1_FUNC0_NBIF1NBIO0_PCIE_VENDOR_SPECIFIC_HDR_ADDRESS    0x10248104UL
#define SMN_DEV1_FUNC0_NBIF1NBIO1_PCIE_VENDOR_SPECIFIC_HDR_ADDRESS    0x10448104UL


/***********************************************************
* Register Name : PMI_CAP
************************************************************/

#define PMI_CAP_VERSION_OFFSET                                 0
#define PMI_CAP_VERSION_MASK                                   0x7

#define PMI_CAP_PME_CLOCK_OFFSET                               3
#define PMI_CAP_PME_CLOCK_MASK                                 0x8

#define PMI_CAP_IMMEDIATE_READINESS_ON_RETURN_TO_D0_OFFSET     4
#define PMI_CAP_IMMEDIATE_READINESS_ON_RETURN_TO_D0_MASK       0x10

#define PMI_CAP_DEV_SPECIFIC_INIT_OFFSET                       5
#define PMI_CAP_DEV_SPECIFIC_INIT_MASK                         0x20

#define PMI_CAP_AUX_CURRENT_OFFSET                             6
#define PMI_CAP_AUX_CURRENT_MASK                               0x1c0

#define PMI_CAP_D1_SUPPORT_OFFSET                              9
#define PMI_CAP_D1_SUPPORT_MASK                                0x200

#define PMI_CAP_D2_SUPPORT_OFFSET                              10
#define PMI_CAP_D2_SUPPORT_MASK                                0x400

#define PMI_CAP_PME_SUPPORT_OFFSET                             11
#define PMI_CAP_PME_SUPPORT_MASK                               0xf800

typedef union {
  struct {
    UINT16                                             VERSION:3;
    UINT16                                           PME_CLOCK:1;
    UINT16                 IMMEDIATE_READINESS_ON_RETURN_TO_D0:1;
    UINT16                                   DEV_SPECIFIC_INIT:1;
    UINT16                                         AUX_CURRENT:3;
    UINT16                                          D1_SUPPORT:1;
    UINT16                                          D2_SUPPORT:1;
    UINT16                                         PME_SUPPORT:5;
  } Field;
  UINT16 Value;
} PMI_CAP_STRUCT;

#define PCICFG_NBIFEPF0CFG_PMI_CAP_OFFSET                           0x52
#define SMN_DEV0_FUNC0_NBIF0NBIO0_PMI_CAP_ADDRESS                     0x10140052UL
#define SMN_DEV0_FUNC0_NBIF0NBIO1_PMI_CAP_ADDRESS                     0x10340052UL
#define SMN_DEV0_FUNC0_NBIF1NBIO0_PMI_CAP_ADDRESS                     0x10240052UL
#define SMN_DEV0_FUNC0_NBIF1NBIO1_PMI_CAP_ADDRESS                     0x10440052UL
#define SMN_DEV0_FUNC0_NBIF2NBIO0_PMI_CAP_ADDRESS                     0x10540052UL
#define SMN_DEV0_FUNC0_NBIF2NBIO1_PMI_CAP_ADDRESS                     0x10740052UL
#define SMN_DEV1_FUNC0_NBIF0NBIO0_PMI_CAP_ADDRESS                     0x10148052UL
#define SMN_DEV1_FUNC0_NBIF0NBIO1_PMI_CAP_ADDRESS                     0x10348052UL
#define SMN_DEV1_FUNC0_NBIF1NBIO0_PMI_CAP_ADDRESS                     0x10248052UL
#define SMN_DEV1_FUNC0_NBIF1NBIO1_PMI_CAP_ADDRESS                     0x10448052UL


/***********************************************************
* Register Name : PMI_CAP_LIST
************************************************************/

#define PMI_CAP_LIST_CAP_ID_OFFSET                             0
#define PMI_CAP_LIST_CAP_ID_MASK                               0xff

#define PMI_CAP_LIST_NEXT_PTR_OFFSET                           8
#define PMI_CAP_LIST_NEXT_PTR_MASK                             0xff00

typedef union {
  struct {
    UINT16                                              CAP_ID:8;
    UINT16                                            NEXT_PTR:8;
  } Field;
  UINT16 Value;
} PMI_CAP_LIST_STRUCT;

#define PCICFG_NBIFEPF0CFG_PMI_CAP_LIST_OFFSET                      0x50
#define SMN_DEV0_FUNC0_NBIF0NBIO0_PMI_CAP_LIST_ADDRESS                0x10140050UL
#define SMN_DEV0_FUNC0_NBIF0NBIO1_PMI_CAP_LIST_ADDRESS                0x10340050UL
#define SMN_DEV0_FUNC0_NBIF1NBIO0_PMI_CAP_LIST_ADDRESS                0x10240050UL
#define SMN_DEV0_FUNC0_NBIF1NBIO1_PMI_CAP_LIST_ADDRESS                0x10440050UL
#define SMN_DEV0_FUNC0_NBIF2NBIO0_PMI_CAP_LIST_ADDRESS                0x10540050UL
#define SMN_DEV0_FUNC0_NBIF2NBIO1_PMI_CAP_LIST_ADDRESS                0x10740050UL
#define SMN_DEV1_FUNC0_NBIF0NBIO0_PMI_CAP_LIST_ADDRESS                0x10148050UL
#define SMN_DEV1_FUNC0_NBIF0NBIO1_PMI_CAP_LIST_ADDRESS                0x10348050UL
#define SMN_DEV1_FUNC0_NBIF1NBIO0_PMI_CAP_LIST_ADDRESS                0x10248050UL
#define SMN_DEV1_FUNC0_NBIF1NBIO1_PMI_CAP_LIST_ADDRESS                0x10448050UL


/***********************************************************
* Register Name : PMI_STATUS_CNTL
************************************************************/

#define PMI_STATUS_CNTL_POWER_STATE_OFFSET                     0
#define PMI_STATUS_CNTL_POWER_STATE_MASK                       0x3

#define PMI_STATUS_CNTL_Reserved_2_2_OFFSET                    2
#define PMI_STATUS_CNTL_Reserved_2_2_MASK                      0x4

#define PMI_STATUS_CNTL_NO_SOFT_RESET_OFFSET                   3
#define PMI_STATUS_CNTL_NO_SOFT_RESET_MASK                     0x8

#define PMI_STATUS_CNTL_Reserved_7_4_OFFSET                    4
#define PMI_STATUS_CNTL_Reserved_7_4_MASK                      0xf0

#define PMI_STATUS_CNTL_PME_EN_OFFSET                          8
#define PMI_STATUS_CNTL_PME_EN_MASK                            0x100

#define PMI_STATUS_CNTL_DATA_SELECT_OFFSET                     9
#define PMI_STATUS_CNTL_DATA_SELECT_MASK                       0x1e00

#define PMI_STATUS_CNTL_DATA_SCALE_OFFSET                      13
#define PMI_STATUS_CNTL_DATA_SCALE_MASK                        0x6000

#define PMI_STATUS_CNTL_PME_STATUS_OFFSET                      15
#define PMI_STATUS_CNTL_PME_STATUS_MASK                        0x8000

#define PMI_STATUS_CNTL_Reserved_21_16_OFFSET                  16
#define PMI_STATUS_CNTL_Reserved_21_16_MASK                    0x3f0000

#define PMI_STATUS_CNTL_B2_B3_SUPPORT_OFFSET                   22
#define PMI_STATUS_CNTL_B2_B3_SUPPORT_MASK                     0x400000

#define PMI_STATUS_CNTL_BUS_PWR_EN_OFFSET                      23
#define PMI_STATUS_CNTL_BUS_PWR_EN_MASK                        0x800000

#define PMI_STATUS_CNTL_PMI_DATA_OFFSET                        24
#define PMI_STATUS_CNTL_PMI_DATA_MASK                          0xff000000

typedef union {
  struct {
    UINT32                                         POWER_STATE:2;
    UINT32                                        Reserved_2_2:1;
    UINT32                                       NO_SOFT_RESET:1;
    UINT32                                        Reserved_7_4:4;
    UINT32                                              PME_EN:1;
    UINT32                                         DATA_SELECT:4;
    UINT32                                          DATA_SCALE:2;
    UINT32                                          PME_STATUS:1;
    UINT32                                      Reserved_21_16:6;
    UINT32                                       B2_B3_SUPPORT:1;
    UINT32                                          BUS_PWR_EN:1;
    UINT32                                            PMI_DATA:8;
  } Field;
  UINT32 Value;
} PMI_STATUS_CNTL_NBIFEPF0CFG_STRUCT;

#define PCICFG_NBIFEPF0CFG_PMI_STATUS_CNTL_OFFSET                   0x54
#define SMN_DEV0_FUNC0_NBIF0NBIO0_PMI_STATUS_CNTL_ADDRESS             0x10140054UL
#define SMN_DEV0_FUNC0_NBIF0NBIO1_PMI_STATUS_CNTL_ADDRESS             0x10340054UL
#define SMN_DEV0_FUNC0_NBIF1NBIO0_PMI_STATUS_CNTL_ADDRESS             0x10240054UL
#define SMN_DEV0_FUNC0_NBIF1NBIO1_PMI_STATUS_CNTL_ADDRESS             0x10440054UL
#define SMN_DEV0_FUNC0_NBIF2NBIO0_PMI_STATUS_CNTL_ADDRESS             0x10540054UL
#define SMN_DEV0_FUNC0_NBIF2NBIO1_PMI_STATUS_CNTL_ADDRESS             0x10740054UL
#define SMN_DEV1_FUNC0_NBIF0NBIO0_PMI_STATUS_CNTL_ADDRESS             0x10148054UL
#define SMN_DEV1_FUNC0_NBIF0NBIO1_PMI_STATUS_CNTL_ADDRESS             0x10348054UL
#define SMN_DEV1_FUNC0_NBIF1NBIO0_PMI_STATUS_CNTL_ADDRESS             0x10248054UL
#define SMN_DEV1_FUNC0_NBIF1NBIO1_PMI_STATUS_CNTL_ADDRESS             0x10448054UL


/***********************************************************
* Register Name : PROG_INTERFACE
************************************************************/

#define PROG_INTERFACE_PROG_INTERFACE_OFFSET                   0
#define PROG_INTERFACE_PROG_INTERFACE_MASK                     0xff

typedef union {
  struct {
    UINT8                                      PROG_INTERFACE:8;
  } Field;
  UINT8 Value;
} PROG_INTERFACE_STRUCT;

#define PCICFG_NBIFEPF0CFG_PROG_INTERFACE_OFFSET                    0x9
#define SMN_DEV0_FUNC0_NBIF0NBIO0_PROG_INTERFACE_ADDRESS              0x10140009UL
#define SMN_DEV0_FUNC0_NBIF0NBIO1_PROG_INTERFACE_ADDRESS              0x10340009UL
#define SMN_DEV0_FUNC0_NBIF1NBIO0_PROG_INTERFACE_ADDRESS              0x10240009UL
#define SMN_DEV0_FUNC0_NBIF1NBIO1_PROG_INTERFACE_ADDRESS              0x10440009UL
#define SMN_DEV0_FUNC0_NBIF2NBIO0_PROG_INTERFACE_ADDRESS              0x10540009UL
#define SMN_DEV0_FUNC0_NBIF2NBIO1_PROG_INTERFACE_ADDRESS              0x10740009UL
#define SMN_DEV1_FUNC0_NBIF0NBIO0_PROG_INTERFACE_ADDRESS              0x10148009UL
#define SMN_DEV1_FUNC0_NBIF0NBIO1_PROG_INTERFACE_ADDRESS              0x10348009UL
#define SMN_DEV1_FUNC0_NBIF1NBIO0_PROG_INTERFACE_ADDRESS              0x10248009UL
#define SMN_DEV1_FUNC0_NBIF1NBIO1_PROG_INTERFACE_ADDRESS              0x10448009UL


/***********************************************************
* Register Name : RECEIVED_MODIFIED_TS_DATA1
************************************************************/

#define RECEIVED_MODIFIED_TS_DATA1_RECEIVED_MODIFIED_TS_USAGE_MODE_OFFSET 0
#define RECEIVED_MODIFIED_TS_DATA1_RECEIVED_MODIFIED_TS_USAGE_MODE_MASK 0x7

#define RECEIVED_MODIFIED_TS_DATA1_RECEIVED_MODIFIED_TS_INFORMATION_1_OFFSET 3
#define RECEIVED_MODIFIED_TS_DATA1_RECEIVED_MODIFIED_TS_INFORMATION_1_MASK 0xfff8

#define RECEIVED_MODIFIED_TS_DATA1_RECEIVED_MODIFIED_TS_VENDOR_ID_OFFSET 16
#define RECEIVED_MODIFIED_TS_DATA1_RECEIVED_MODIFIED_TS_VENDOR_ID_MASK 0xffff0000

typedef union {
  struct {
    UINT32                     RECEIVED_MODIFIED_TS_USAGE_MODE:3;
    UINT32                  RECEIVED_MODIFIED_TS_INFORMATION_1:13;
    UINT32                      RECEIVED_MODIFIED_TS_VENDOR_ID:16;
  } Field;
  UINT32 Value;
} RECEIVED_MODIFIED_TS_DATA1_STRUCT;

#define PCICFG_NBIFEPF0CFG_RECEIVED_MODIFIED_TS_DATA1_OFFSET        0x510
#define SMN_DEV0_FUNC0_NBIF0NBIO0_RECEIVED_MODIFIED_TS_DATA1_ADDRESS  0x10140510UL
#define SMN_DEV0_FUNC0_NBIF0NBIO1_RECEIVED_MODIFIED_TS_DATA1_ADDRESS  0x10340510UL
#define SMN_DEV0_FUNC0_NBIF1NBIO0_RECEIVED_MODIFIED_TS_DATA1_ADDRESS  0x10240510UL
#define SMN_DEV0_FUNC0_NBIF1NBIO1_RECEIVED_MODIFIED_TS_DATA1_ADDRESS  0x10440510UL
#define SMN_DEV0_FUNC0_NBIF2NBIO0_RECEIVED_MODIFIED_TS_DATA1_ADDRESS  0x10540510UL
#define SMN_DEV0_FUNC0_NBIF2NBIO1_RECEIVED_MODIFIED_TS_DATA1_ADDRESS  0x10740510UL
#define SMN_DEV1_FUNC0_NBIF0NBIO0_RECEIVED_MODIFIED_TS_DATA1_ADDRESS  0x10148510UL
#define SMN_DEV1_FUNC0_NBIF0NBIO1_RECEIVED_MODIFIED_TS_DATA1_ADDRESS  0x10348510UL
#define SMN_DEV1_FUNC0_NBIF1NBIO0_RECEIVED_MODIFIED_TS_DATA1_ADDRESS  0x10248510UL
#define SMN_DEV1_FUNC0_NBIF1NBIO1_RECEIVED_MODIFIED_TS_DATA1_ADDRESS  0x10448510UL


/***********************************************************
* Register Name : RECEIVED_MODIFIED_TS_DATA2
************************************************************/

#define RECEIVED_MODIFIED_TS_DATA2_RECEIVED_MODIFIED_TS_INFORMATION_2_OFFSET 0
#define RECEIVED_MODIFIED_TS_DATA2_RECEIVED_MODIFIED_TS_INFORMATION_2_MASK 0xffffff

#define RECEIVED_MODIFIED_TS_DATA2_RECEIVED_ALTERNATE_PROTOCOL_NEGOTIATION_STATUS_OFFSET 24
#define RECEIVED_MODIFIED_TS_DATA2_RECEIVED_ALTERNATE_PROTOCOL_NEGOTIATION_STATUS_MASK 0x3000000

#define RECEIVED_MODIFIED_TS_DATA2_Reserved_31_26_OFFSET       26
#define RECEIVED_MODIFIED_TS_DATA2_Reserved_31_26_MASK         0xfc000000

typedef union {
  struct {
    UINT32                  RECEIVED_MODIFIED_TS_INFORMATION_2:24;
    UINT32      RECEIVED_ALTERNATE_PROTOCOL_NEGOTIATION_STATUS:2;
    UINT32                                      Reserved_31_26:6;
  } Field;
  UINT32 Value;
} RECEIVED_MODIFIED_TS_DATA2_STRUCT;

#define PCICFG_NBIFEPF0CFG_RECEIVED_MODIFIED_TS_DATA2_OFFSET        0x514
#define SMN_DEV0_FUNC0_NBIF0NBIO0_RECEIVED_MODIFIED_TS_DATA2_ADDRESS  0x10140514UL
#define SMN_DEV0_FUNC0_NBIF0NBIO1_RECEIVED_MODIFIED_TS_DATA2_ADDRESS  0x10340514UL
#define SMN_DEV0_FUNC0_NBIF1NBIO0_RECEIVED_MODIFIED_TS_DATA2_ADDRESS  0x10240514UL
#define SMN_DEV0_FUNC0_NBIF1NBIO1_RECEIVED_MODIFIED_TS_DATA2_ADDRESS  0x10440514UL
#define SMN_DEV0_FUNC0_NBIF2NBIO0_RECEIVED_MODIFIED_TS_DATA2_ADDRESS  0x10540514UL
#define SMN_DEV0_FUNC0_NBIF2NBIO1_RECEIVED_MODIFIED_TS_DATA2_ADDRESS  0x10740514UL
#define SMN_DEV1_FUNC0_NBIF0NBIO0_RECEIVED_MODIFIED_TS_DATA2_ADDRESS  0x10148514UL
#define SMN_DEV1_FUNC0_NBIF0NBIO1_RECEIVED_MODIFIED_TS_DATA2_ADDRESS  0x10348514UL
#define SMN_DEV1_FUNC0_NBIF1NBIO0_RECEIVED_MODIFIED_TS_DATA2_ADDRESS  0x10248514UL
#define SMN_DEV1_FUNC0_NBIF1NBIO1_RECEIVED_MODIFIED_TS_DATA2_ADDRESS  0x10448514UL


/***********************************************************
* Register Name : REVISION_ID
************************************************************/

#define REVISION_ID_MINOR_REV_ID_OFFSET                        0
#define REVISION_ID_MINOR_REV_ID_MASK                          0xf

#define REVISION_ID_MAJOR_REV_ID_OFFSET                        4
#define REVISION_ID_MAJOR_REV_ID_MASK                          0xf0

typedef union {
  struct {
    UINT8                                        MINOR_REV_ID:4;
    UINT8                                        MAJOR_REV_ID:4;
  } Field;
  UINT8 Value;
} REVISION_ID_STRUCT;

#define PCICFG_NBIFEPF0CFG_REVISION_ID_OFFSET                       0x8
#define SMN_DEV0_FUNC0_NBIF0NBIO0_REVISION_ID_ADDRESS                 0x10140008UL
#define SMN_DEV0_FUNC0_NBIF0NBIO1_REVISION_ID_ADDRESS                 0x10340008UL
#define SMN_DEV0_FUNC0_NBIF1NBIO0_REVISION_ID_ADDRESS                 0x10240008UL
#define SMN_DEV0_FUNC0_NBIF1NBIO1_REVISION_ID_ADDRESS                 0x10440008UL
#define SMN_DEV0_FUNC0_NBIF2NBIO0_REVISION_ID_ADDRESS                 0x10540008UL
#define SMN_DEV0_FUNC0_NBIF2NBIO1_REVISION_ID_ADDRESS                 0x10740008UL
#define SMN_DEV1_FUNC0_NBIF0NBIO0_REVISION_ID_ADDRESS                 0x10148008UL
#define SMN_DEV1_FUNC0_NBIF0NBIO1_REVISION_ID_ADDRESS                 0x10348008UL
#define SMN_DEV1_FUNC0_NBIF1NBIO0_REVISION_ID_ADDRESS                 0x10248008UL
#define SMN_DEV1_FUNC0_NBIF1NBIO1_REVISION_ID_ADDRESS                 0x10448008UL


/***********************************************************
* Register Name : ROM_BASE_ADDR
************************************************************/


/***********************************************************
* Register Name : RTM1_PARITY_MISMATCH_STATUS_16GT
************************************************************/

#define RTM1_PARITY_MISMATCH_STATUS_16GT_RTM1_PARITY_MISMATCH_STATUS_BITS_OFFSET 0
#define RTM1_PARITY_MISMATCH_STATUS_16GT_RTM1_PARITY_MISMATCH_STATUS_BITS_MASK 0xffff

#define RTM1_PARITY_MISMATCH_STATUS_16GT_Reserved_31_16_OFFSET 16
#define RTM1_PARITY_MISMATCH_STATUS_16GT_Reserved_31_16_MASK   0xffff0000

typedef union {
  struct {
    UINT32                    RTM1_PARITY_MISMATCH_STATUS_BITS:16;
    UINT32                                      Reserved_31_16:16;
  } Field;
  UINT32 Value;
} RTM1_PARITY_MISMATCH_STATUS_16GT_STRUCT;

#define PCICFG_NBIFEPF0CFG_RTM1_PARITY_MISMATCH_STATUS_16GT_OFFSET  0x424
#define SMN_DEV0_FUNC0_NBIF0NBIO0_RTM1_PARITY_MISMATCH_STATUS_16GT_ADDRESS 0x10140424UL
#define SMN_DEV0_FUNC0_NBIF0NBIO1_RTM1_PARITY_MISMATCH_STATUS_16GT_ADDRESS 0x10340424UL
#define SMN_DEV0_FUNC0_NBIF1NBIO0_RTM1_PARITY_MISMATCH_STATUS_16GT_ADDRESS 0x10240424UL
#define SMN_DEV0_FUNC0_NBIF1NBIO1_RTM1_PARITY_MISMATCH_STATUS_16GT_ADDRESS 0x10440424UL
#define SMN_DEV0_FUNC0_NBIF2NBIO0_RTM1_PARITY_MISMATCH_STATUS_16GT_ADDRESS 0x10540424UL
#define SMN_DEV0_FUNC0_NBIF2NBIO1_RTM1_PARITY_MISMATCH_STATUS_16GT_ADDRESS 0x10740424UL
#define SMN_DEV1_FUNC0_NBIF0NBIO0_RTM1_PARITY_MISMATCH_STATUS_16GT_ADDRESS 0x10148424UL
#define SMN_DEV1_FUNC0_NBIF0NBIO1_RTM1_PARITY_MISMATCH_STATUS_16GT_ADDRESS 0x10348424UL
#define SMN_DEV1_FUNC0_NBIF1NBIO0_RTM1_PARITY_MISMATCH_STATUS_16GT_ADDRESS 0x10248424UL
#define SMN_DEV1_FUNC0_NBIF1NBIO1_RTM1_PARITY_MISMATCH_STATUS_16GT_ADDRESS 0x10448424UL


/***********************************************************
* Register Name : RTM2_PARITY_MISMATCH_STATUS_16GT
************************************************************/

#define RTM2_PARITY_MISMATCH_STATUS_16GT_RTM2_PARITY_MISMATCH_STATUS_BITS_OFFSET 0
#define RTM2_PARITY_MISMATCH_STATUS_16GT_RTM2_PARITY_MISMATCH_STATUS_BITS_MASK 0xffff

#define RTM2_PARITY_MISMATCH_STATUS_16GT_Reserved_31_16_OFFSET 16
#define RTM2_PARITY_MISMATCH_STATUS_16GT_Reserved_31_16_MASK   0xffff0000

typedef union {
  struct {
    UINT32                    RTM2_PARITY_MISMATCH_STATUS_BITS:16;
    UINT32                                      Reserved_31_16:16;
  } Field;
  UINT32 Value;
} RTM2_PARITY_MISMATCH_STATUS_16GT_STRUCT;

#define PCICFG_NBIFEPF0CFG_RTM2_PARITY_MISMATCH_STATUS_16GT_OFFSET  0x428
#define SMN_DEV0_FUNC0_NBIF0NBIO0_RTM2_PARITY_MISMATCH_STATUS_16GT_ADDRESS 0x10140428UL
#define SMN_DEV0_FUNC0_NBIF0NBIO1_RTM2_PARITY_MISMATCH_STATUS_16GT_ADDRESS 0x10340428UL
#define SMN_DEV0_FUNC0_NBIF1NBIO0_RTM2_PARITY_MISMATCH_STATUS_16GT_ADDRESS 0x10240428UL
#define SMN_DEV0_FUNC0_NBIF1NBIO1_RTM2_PARITY_MISMATCH_STATUS_16GT_ADDRESS 0x10440428UL
#define SMN_DEV0_FUNC0_NBIF2NBIO0_RTM2_PARITY_MISMATCH_STATUS_16GT_ADDRESS 0x10540428UL
#define SMN_DEV0_FUNC0_NBIF2NBIO1_RTM2_PARITY_MISMATCH_STATUS_16GT_ADDRESS 0x10740428UL
#define SMN_DEV1_FUNC0_NBIF0NBIO0_RTM2_PARITY_MISMATCH_STATUS_16GT_ADDRESS 0x10148428UL
#define SMN_DEV1_FUNC0_NBIF0NBIO1_RTM2_PARITY_MISMATCH_STATUS_16GT_ADDRESS 0x10348428UL
#define SMN_DEV1_FUNC0_NBIF1NBIO0_RTM2_PARITY_MISMATCH_STATUS_16GT_ADDRESS 0x10248428UL
#define SMN_DEV1_FUNC0_NBIF1NBIO1_RTM2_PARITY_MISMATCH_STATUS_16GT_ADDRESS 0x10448428UL


/***********************************************************
* Register Name : RTR_DATA1
************************************************************/

#define RTR_DATA1_RESET_TIME_OFFSET                            0
#define RTR_DATA1_RESET_TIME_MASK                              0xfff

#define RTR_DATA1_DLUP_TIME_OFFSET                             12
#define RTR_DATA1_DLUP_TIME_MASK                               0xfff000

#define RTR_DATA1_Reserved_30_24_OFFSET                        24
#define RTR_DATA1_Reserved_30_24_MASK                          0x7f000000

#define RTR_DATA1_VALID_OFFSET                                 31
#define RTR_DATA1_VALID_MASK                                   0x80000000

typedef union {
  struct {
    UINT32                                          RESET_TIME:12;
    UINT32                                           DLUP_TIME:12;
    UINT32                                      Reserved_30_24:7;
    UINT32                                               VALID:1;
  } Field;
  UINT32 Value;
} RTR_DATA1_STRUCT;

#define PCICFG_NBIFEPF0CFG_RTR_DATA1_OFFSET                         0x574
#define SMN_DEV0_FUNC0_NBIF0NBIO0_RTR_DATA1_ADDRESS                   0x10140574UL
#define SMN_DEV0_FUNC0_NBIF0NBIO1_RTR_DATA1_ADDRESS                   0x10340574UL
#define SMN_DEV0_FUNC0_NBIF1NBIO0_RTR_DATA1_ADDRESS                   0x10240574UL
#define SMN_DEV0_FUNC0_NBIF1NBIO1_RTR_DATA1_ADDRESS                   0x10440574UL
#define SMN_DEV0_FUNC0_NBIF2NBIO0_RTR_DATA1_ADDRESS                   0x10540574UL
#define SMN_DEV0_FUNC0_NBIF2NBIO1_RTR_DATA1_ADDRESS                   0x10740574UL
#define SMN_DEV1_FUNC0_NBIF0NBIO0_RTR_DATA1_ADDRESS                   0x10148574UL
#define SMN_DEV1_FUNC0_NBIF0NBIO1_RTR_DATA1_ADDRESS                   0x10348574UL
#define SMN_DEV1_FUNC0_NBIF1NBIO0_RTR_DATA1_ADDRESS                   0x10248574UL
#define SMN_DEV1_FUNC0_NBIF1NBIO1_RTR_DATA1_ADDRESS                   0x10448574UL


/***********************************************************
* Register Name : RTR_DATA2
************************************************************/

#define RTR_DATA2_FLR_TIME_OFFSET                              0
#define RTR_DATA2_FLR_TIME_MASK                                0xfff

#define RTR_DATA2_D3HOTD0_TIME_OFFSET                          12
#define RTR_DATA2_D3HOTD0_TIME_MASK                            0xfff000

#define RTR_DATA2_Reserved_31_24_OFFSET                        24
#define RTR_DATA2_Reserved_31_24_MASK                          0xff000000

typedef union {
  struct {
    UINT32                                            FLR_TIME:12;
    UINT32                                        D3HOTD0_TIME:12;
    UINT32                                      Reserved_31_24:8;
  } Field;
  UINT32 Value;
} RTR_DATA2_STRUCT;

#define PCICFG_NBIFEPF0CFG_RTR_DATA2_OFFSET                         0x578
#define SMN_DEV0_FUNC0_NBIF0NBIO0_RTR_DATA2_ADDRESS                   0x10140578UL
#define SMN_DEV0_FUNC0_NBIF0NBIO1_RTR_DATA2_ADDRESS                   0x10340578UL
#define SMN_DEV0_FUNC0_NBIF1NBIO0_RTR_DATA2_ADDRESS                   0x10240578UL
#define SMN_DEV0_FUNC0_NBIF1NBIO1_RTR_DATA2_ADDRESS                   0x10440578UL
#define SMN_DEV0_FUNC0_NBIF2NBIO0_RTR_DATA2_ADDRESS                   0x10540578UL
#define SMN_DEV0_FUNC0_NBIF2NBIO1_RTR_DATA2_ADDRESS                   0x10740578UL
#define SMN_DEV1_FUNC0_NBIF0NBIO0_RTR_DATA2_ADDRESS                   0x10148578UL
#define SMN_DEV1_FUNC0_NBIF0NBIO1_RTR_DATA2_ADDRESS                   0x10348578UL
#define SMN_DEV1_FUNC0_NBIF1NBIO0_RTR_DATA2_ADDRESS                   0x10248578UL
#define SMN_DEV1_FUNC0_NBIF1NBIO1_RTR_DATA2_ADDRESS                   0x10448578UL


/***********************************************************
* Register Name : SATA_CAP_0
************************************************************/

#define SATA_CAP_0_CAP_ID_OFFSET                               0
#define SATA_CAP_0_CAP_ID_MASK                                 0xff

#define SATA_CAP_0_NEXT_PTR_OFFSET                             8
#define SATA_CAP_0_NEXT_PTR_MASK                               0xff00

#define SATA_CAP_0_SATA_CAP_MINOR_REV_OFFSET                   16
#define SATA_CAP_0_SATA_CAP_MINOR_REV_MASK                     0xf0000

#define SATA_CAP_0_SATA_CAP_MAJOR_REV_OFFSET                   20
#define SATA_CAP_0_SATA_CAP_MAJOR_REV_MASK                     0xf00000

#define SATA_CAP_0_SATA_CAP_RESERVED1_OFFSET                   24
#define SATA_CAP_0_SATA_CAP_RESERVED1_MASK                     0xff000000

typedef union {
  struct {
    UINT32                                              CAP_ID:8;
    UINT32                                            NEXT_PTR:8;
    UINT32                                  SATA_CAP_MINOR_REV:4;
    UINT32                                  SATA_CAP_MAJOR_REV:4;
    UINT32                                  SATA_CAP_RESERVED1:8;
  } Field;
  UINT32 Value;
} SATA_CAP_0_STRUCT;

#define PCICFG_NBIFEPF0CFG_SATA_CAP_0_OFFSET                        0xd0
#define SMN_DEV0_FUNC0_NBIF0NBIO0_SATA_CAP_0_ADDRESS                  0x101400d0UL
#define SMN_DEV0_FUNC0_NBIF0NBIO1_SATA_CAP_0_ADDRESS                  0x103400d0UL
#define SMN_DEV0_FUNC0_NBIF1NBIO0_SATA_CAP_0_ADDRESS                  0x102400d0UL
#define SMN_DEV0_FUNC0_NBIF1NBIO1_SATA_CAP_0_ADDRESS                  0x104400d0UL
#define SMN_DEV0_FUNC0_NBIF2NBIO0_SATA_CAP_0_ADDRESS                  0x105400d0UL
#define SMN_DEV0_FUNC0_NBIF2NBIO1_SATA_CAP_0_ADDRESS                  0x107400d0UL
#define SMN_DEV1_FUNC0_NBIF0NBIO0_SATA_CAP_0_ADDRESS                  0x101480d0UL
#define SMN_DEV1_FUNC0_NBIF0NBIO1_SATA_CAP_0_ADDRESS                  0x103480d0UL
#define SMN_DEV1_FUNC0_NBIF1NBIO0_SATA_CAP_0_ADDRESS                  0x102480d0UL
#define SMN_DEV1_FUNC0_NBIF1NBIO1_SATA_CAP_0_ADDRESS                  0x104480d0UL


/***********************************************************
* Register Name : SATA_CAP_1
************************************************************/

#define SATA_CAP_1_SATA_CAP_BAR_LOC_OFFSET                     0
#define SATA_CAP_1_SATA_CAP_BAR_LOC_MASK                       0xf

#define SATA_CAP_1_SATA_CAP_BAR_OFFSET_OFFSET                  4
#define SATA_CAP_1_SATA_CAP_BAR_OFFSET_MASK                    0xfffff0

#define SATA_CAP_1_SATA_CAP_RESERVED2_OFFSET                   24
#define SATA_CAP_1_SATA_CAP_RESERVED2_MASK                     0xff000000

typedef union {
  struct {
    UINT32                                    SATA_CAP_BAR_LOC:4;
    UINT32                                 SATA_CAP_BAR_OFFSET:20;
    UINT32                                  SATA_CAP_RESERVED2:8;
  } Field;
  UINT32 Value;
} SATA_CAP_1_STRUCT;

#define PCICFG_NBIFEPF0CFG_SATA_CAP_1_OFFSET                        0xd4
#define SMN_DEV0_FUNC0_NBIF0NBIO0_SATA_CAP_1_ADDRESS                  0x101400d4UL
#define SMN_DEV0_FUNC0_NBIF0NBIO1_SATA_CAP_1_ADDRESS                  0x103400d4UL
#define SMN_DEV0_FUNC0_NBIF1NBIO0_SATA_CAP_1_ADDRESS                  0x102400d4UL
#define SMN_DEV0_FUNC0_NBIF1NBIO1_SATA_CAP_1_ADDRESS                  0x104400d4UL
#define SMN_DEV0_FUNC0_NBIF2NBIO0_SATA_CAP_1_ADDRESS                  0x105400d4UL
#define SMN_DEV0_FUNC0_NBIF2NBIO1_SATA_CAP_1_ADDRESS                  0x107400d4UL
#define SMN_DEV1_FUNC0_NBIF0NBIO0_SATA_CAP_1_ADDRESS                  0x101480d4UL
#define SMN_DEV1_FUNC0_NBIF0NBIO1_SATA_CAP_1_ADDRESS                  0x103480d4UL
#define SMN_DEV1_FUNC0_NBIF1NBIO0_SATA_CAP_1_ADDRESS                  0x102480d4UL
#define SMN_DEV1_FUNC0_NBIF1NBIO1_SATA_CAP_1_ADDRESS                  0x104480d4UL


/***********************************************************
* Register Name : SATA_IDP_DATA
************************************************************/

#define SATA_IDP_DATA_IDP_DATA_OFFSET                          0
#define SATA_IDP_DATA_IDP_DATA_MASK                            0xffffffff

typedef union {
  struct {
    UINT32                                            IDP_DATA:32;
  } Field;
  UINT32 Value;
} SATA_IDP_DATA_STRUCT;

#define PCICFG_NBIFEPF0CFG_SATA_IDP_DATA_OFFSET                     0xdc
#define SMN_DEV0_FUNC0_NBIF0NBIO0_SATA_IDP_DATA_ADDRESS               0x101400dcUL
#define SMN_DEV0_FUNC0_NBIF0NBIO1_SATA_IDP_DATA_ADDRESS               0x103400dcUL
#define SMN_DEV0_FUNC0_NBIF1NBIO0_SATA_IDP_DATA_ADDRESS               0x102400dcUL
#define SMN_DEV0_FUNC0_NBIF1NBIO1_SATA_IDP_DATA_ADDRESS               0x104400dcUL
#define SMN_DEV0_FUNC0_NBIF2NBIO0_SATA_IDP_DATA_ADDRESS               0x105400dcUL
#define SMN_DEV0_FUNC0_NBIF2NBIO1_SATA_IDP_DATA_ADDRESS               0x107400dcUL
#define SMN_DEV1_FUNC0_NBIF0NBIO0_SATA_IDP_DATA_ADDRESS               0x101480dcUL
#define SMN_DEV1_FUNC0_NBIF0NBIO1_SATA_IDP_DATA_ADDRESS               0x103480dcUL
#define SMN_DEV1_FUNC0_NBIF1NBIO0_SATA_IDP_DATA_ADDRESS               0x102480dcUL
#define SMN_DEV1_FUNC0_NBIF1NBIO1_SATA_IDP_DATA_ADDRESS               0x104480dcUL


/***********************************************************
* Register Name : SATA_IDP_INDEX
************************************************************/

#define SATA_IDP_INDEX_IDP_RESERVED1_OFFSET                    0
#define SATA_IDP_INDEX_IDP_RESERVED1_MASK                      0x3

#define SATA_IDP_INDEX_IDP_INDEX_OFFSET                        2
#define SATA_IDP_INDEX_IDP_INDEX_MASK                          0xffc

#define SATA_IDP_INDEX_IDP_RESERVED2_OFFSET                    12
#define SATA_IDP_INDEX_IDP_RESERVED2_MASK                      0xfffff000

typedef union {
  struct {
    UINT32                                       IDP_RESERVED1:2;
    UINT32                                           IDP_INDEX:10;
    UINT32                                       IDP_RESERVED2:20;
  } Field;
  UINT32 Value;
} SATA_IDP_INDEX_STRUCT;

#define PCICFG_NBIFEPF0CFG_SATA_IDP_INDEX_OFFSET                    0xd8
#define SMN_DEV0_FUNC0_NBIF0NBIO0_SATA_IDP_INDEX_ADDRESS              0x101400d8UL
#define SMN_DEV0_FUNC0_NBIF0NBIO1_SATA_IDP_INDEX_ADDRESS              0x103400d8UL
#define SMN_DEV0_FUNC0_NBIF1NBIO0_SATA_IDP_INDEX_ADDRESS              0x102400d8UL
#define SMN_DEV0_FUNC0_NBIF1NBIO1_SATA_IDP_INDEX_ADDRESS              0x104400d8UL
#define SMN_DEV0_FUNC0_NBIF2NBIO0_SATA_IDP_INDEX_ADDRESS              0x105400d8UL
#define SMN_DEV0_FUNC0_NBIF2NBIO1_SATA_IDP_INDEX_ADDRESS              0x107400d8UL
#define SMN_DEV1_FUNC0_NBIF0NBIO0_SATA_IDP_INDEX_ADDRESS              0x101480d8UL
#define SMN_DEV1_FUNC0_NBIF0NBIO1_SATA_IDP_INDEX_ADDRESS              0x103480d8UL
#define SMN_DEV1_FUNC0_NBIF1NBIO0_SATA_IDP_INDEX_ADDRESS              0x102480d8UL
#define SMN_DEV1_FUNC0_NBIF1NBIO1_SATA_IDP_INDEX_ADDRESS              0x104480d8UL


/***********************************************************
* Register Name : SBRN
************************************************************/

#define SBRN_SBRN_OFFSET                                       0
#define SBRN_SBRN_MASK                                         0xff

typedef union {
  struct {
    UINT8                                                SBRN:8;
  } Field;
  UINT8 Value;
} SBRN_STRUCT;

#define PCICFG_NBIFEPF0CFG_SBRN_OFFSET                              0x60
#define SMN_DEV1_FUNC0_NBIF0NBIO0_SBRN_ADDRESS                        0x10148060UL
#define SMN_DEV1_FUNC0_NBIF0NBIO1_SBRN_ADDRESS                        0x10348060UL
#define SMN_DEV1_FUNC0_NBIF1NBIO0_SBRN_ADDRESS                        0x10248060UL
#define SMN_DEV1_FUNC0_NBIF1NBIO1_SBRN_ADDRESS                        0x10448060UL


/***********************************************************
* Register Name : STATUS
************************************************************/

#define STATUS_IMMEDIATE_READINESS_OFFSET                      0
#define STATUS_IMMEDIATE_READINESS_MASK                        0x1

#define STATUS_Reserved_2_1_OFFSET                             1
#define STATUS_Reserved_2_1_MASK                               0x6

#define STATUS_INT_STATUS_OFFSET                               3
#define STATUS_INT_STATUS_MASK                                 0x8

#define STATUS_CAP_LIST_OFFSET                                 4
#define STATUS_CAP_LIST_MASK                                   0x10

#define STATUS_PCI_66_CAP_OFFSET                               5
#define STATUS_PCI_66_CAP_MASK                                 0x20

#define STATUS_Reserved_6_6_OFFSET                             6
#define STATUS_Reserved_6_6_MASK                               0x40

#define STATUS_FAST_BACK_CAPABLE_OFFSET                        7
#define STATUS_FAST_BACK_CAPABLE_MASK                          0x80

#define STATUS_MASTER_DATA_PARITY_ERROR_OFFSET                 8
#define STATUS_MASTER_DATA_PARITY_ERROR_MASK                   0x100

#define STATUS_DEVSEL_TIMING_OFFSET                            9
#define STATUS_DEVSEL_TIMING_MASK                              0x600

#define STATUS_SIGNAL_TARGET_ABORT_OFFSET                      11
#define STATUS_SIGNAL_TARGET_ABORT_MASK                        0x800

#define STATUS_RECEIVED_TARGET_ABORT_OFFSET                    12
#define STATUS_RECEIVED_TARGET_ABORT_MASK                      0x1000

#define STATUS_RECEIVED_MASTER_ABORT_OFFSET                    13
#define STATUS_RECEIVED_MASTER_ABORT_MASK                      0x2000

#define STATUS_SIGNALED_SYSTEM_ERROR_OFFSET                    14
#define STATUS_SIGNALED_SYSTEM_ERROR_MASK                      0x4000

#define STATUS_PARITY_ERROR_DETECTED_OFFSET                    15
#define STATUS_PARITY_ERROR_DETECTED_MASK                      0x8000

typedef union {
  struct {
    UINT16                                 IMMEDIATE_READINESS:1;
    UINT16                                        Reserved_2_1:2;
    UINT16                                          INT_STATUS:1;
    UINT16                                            CAP_LIST:1;
    UINT16                                          PCI_66_CAP:1;
    UINT16                                        Reserved_6_6:1;
    UINT16                                   FAST_BACK_CAPABLE:1;
    UINT16                            MASTER_DATA_PARITY_ERROR:1;
    UINT16                                       DEVSEL_TIMING:2;
    UINT16                                 SIGNAL_TARGET_ABORT:1;
    UINT16                               RECEIVED_TARGET_ABORT:1;
    UINT16                               RECEIVED_MASTER_ABORT:1;
    UINT16                               SIGNALED_SYSTEM_ERROR:1;
    UINT16                               PARITY_ERROR_DETECTED:1;
  } Field;
  UINT16 Value;
} STATUS_STRUCT;

#define PCICFG_NBIFEPF0CFG_STATUS_OFFSET                            0x6
#define SMN_DEV0_FUNC0_NBIF0NBIO0_STATUS_ADDRESS                      0x10140006UL
#define SMN_DEV0_FUNC0_NBIF0NBIO1_STATUS_ADDRESS                      0x10340006UL
#define SMN_DEV0_FUNC0_NBIF1NBIO0_STATUS_ADDRESS                      0x10240006UL
#define SMN_DEV0_FUNC0_NBIF1NBIO1_STATUS_ADDRESS                      0x10440006UL
#define SMN_DEV0_FUNC0_NBIF2NBIO0_STATUS_ADDRESS                      0x10540006UL
#define SMN_DEV0_FUNC0_NBIF2NBIO1_STATUS_ADDRESS                      0x10740006UL
#define SMN_DEV1_FUNC0_NBIF0NBIO0_STATUS_ADDRESS                      0x10148006UL
#define SMN_DEV1_FUNC0_NBIF0NBIO1_STATUS_ADDRESS                      0x10348006UL
#define SMN_DEV1_FUNC0_NBIF1NBIO0_STATUS_ADDRESS                      0x10248006UL
#define SMN_DEV1_FUNC0_NBIF1NBIO1_STATUS_ADDRESS                      0x10448006UL


/***********************************************************
* Register Name : SUB_CLASS
************************************************************/

#define SUB_CLASS_SUB_CLASS_OFFSET                             0
#define SUB_CLASS_SUB_CLASS_MASK                               0xff

typedef union {
  struct {
    UINT8                                           SUB_CLASS:8;
  } Field;
  UINT8 Value;
} SUB_CLASS_STRUCT;

#define PCICFG_NBIFEPF0CFG_SUB_CLASS_OFFSET                         0xa
#define SMN_DEV0_FUNC0_NBIF0NBIO0_SUB_CLASS_ADDRESS                   0x1014000aUL
#define SMN_DEV0_FUNC0_NBIF0NBIO1_SUB_CLASS_ADDRESS                   0x1034000aUL
#define SMN_DEV0_FUNC0_NBIF1NBIO0_SUB_CLASS_ADDRESS                   0x1024000aUL
#define SMN_DEV0_FUNC0_NBIF1NBIO1_SUB_CLASS_ADDRESS                   0x1044000aUL
#define SMN_DEV0_FUNC0_NBIF2NBIO0_SUB_CLASS_ADDRESS                   0x1054000aUL
#define SMN_DEV0_FUNC0_NBIF2NBIO1_SUB_CLASS_ADDRESS                   0x1074000aUL
#define SMN_DEV1_FUNC0_NBIF0NBIO0_SUB_CLASS_ADDRESS                   0x1014800aUL
#define SMN_DEV1_FUNC0_NBIF0NBIO1_SUB_CLASS_ADDRESS                   0x1034800aUL
#define SMN_DEV1_FUNC0_NBIF1NBIO0_SUB_CLASS_ADDRESS                   0x1024800aUL
#define SMN_DEV1_FUNC0_NBIF1NBIO1_SUB_CLASS_ADDRESS                   0x1044800aUL


/***********************************************************
* Register Name : TRANSMITTED_MODIFIED_TS_DATA1
************************************************************/

#define TRANSMITTED_MODIFIED_TS_DATA1_TRANSMITTED_MODIFIED_TS_USAGE_MODE_OFFSET 0
#define TRANSMITTED_MODIFIED_TS_DATA1_TRANSMITTED_MODIFIED_TS_USAGE_MODE_MASK 0x7

#define TRANSMITTED_MODIFIED_TS_DATA1_TRANSMITTED_MODIFIED_TS_INFORMATION_1_OFFSET 3
#define TRANSMITTED_MODIFIED_TS_DATA1_TRANSMITTED_MODIFIED_TS_INFORMATION_1_MASK 0xfff8

#define TRANSMITTED_MODIFIED_TS_DATA1_TRANSMITTED_MODIFIED_TS_VENDOR_ID_OFFSET 16
#define TRANSMITTED_MODIFIED_TS_DATA1_TRANSMITTED_MODIFIED_TS_VENDOR_ID_MASK 0xffff0000

typedef union {
  struct {
    UINT32                  TRANSMITTED_MODIFIED_TS_USAGE_MODE:3;
    UINT32               TRANSMITTED_MODIFIED_TS_INFORMATION_1:13;
    UINT32                   TRANSMITTED_MODIFIED_TS_VENDOR_ID:16;
  } Field;
  UINT32 Value;
} TRANSMITTED_MODIFIED_TS_DATA1_STRUCT;

#define PCICFG_NBIFEPF0CFG_TRANSMITTED_MODIFIED_TS_DATA1_OFFSET     0x518
#define SMN_DEV0_FUNC0_NBIF0NBIO0_TRANSMITTED_MODIFIED_TS_DATA1_ADDRESS 0x10140518UL
#define SMN_DEV0_FUNC0_NBIF0NBIO1_TRANSMITTED_MODIFIED_TS_DATA1_ADDRESS 0x10340518UL
#define SMN_DEV0_FUNC0_NBIF1NBIO0_TRANSMITTED_MODIFIED_TS_DATA1_ADDRESS 0x10240518UL
#define SMN_DEV0_FUNC0_NBIF1NBIO1_TRANSMITTED_MODIFIED_TS_DATA1_ADDRESS 0x10440518UL
#define SMN_DEV0_FUNC0_NBIF2NBIO0_TRANSMITTED_MODIFIED_TS_DATA1_ADDRESS 0x10540518UL
#define SMN_DEV0_FUNC0_NBIF2NBIO1_TRANSMITTED_MODIFIED_TS_DATA1_ADDRESS 0x10740518UL
#define SMN_DEV1_FUNC0_NBIF0NBIO0_TRANSMITTED_MODIFIED_TS_DATA1_ADDRESS 0x10148518UL
#define SMN_DEV1_FUNC0_NBIF0NBIO1_TRANSMITTED_MODIFIED_TS_DATA1_ADDRESS 0x10348518UL
#define SMN_DEV1_FUNC0_NBIF1NBIO0_TRANSMITTED_MODIFIED_TS_DATA1_ADDRESS 0x10248518UL
#define SMN_DEV1_FUNC0_NBIF1NBIO1_TRANSMITTED_MODIFIED_TS_DATA1_ADDRESS 0x10448518UL


/***********************************************************
* Register Name : TRANSMITTED_MODIFIED_TS_DATA2
************************************************************/

#define TRANSMITTED_MODIFIED_TS_DATA2_TRANSMITTED_MODIFIED_TS_INFORMATION_2_OFFSET 0
#define TRANSMITTED_MODIFIED_TS_DATA2_TRANSMITTED_MODIFIED_TS_INFORMATION_2_MASK 0xffffff

#define TRANSMITTED_MODIFIED_TS_DATA2_TRANSMITTED_ALTERNATE_PROTOCOL_NEGOTIATION_STATUS_OFFSET 24
#define TRANSMITTED_MODIFIED_TS_DATA2_TRANSMITTED_ALTERNATE_PROTOCOL_NEGOTIATION_STATUS_MASK 0x3000000

#define TRANSMITTED_MODIFIED_TS_DATA2_Reserved_31_26_OFFSET    26
#define TRANSMITTED_MODIFIED_TS_DATA2_Reserved_31_26_MASK      0xfc000000

typedef union {
  struct {
    UINT32               TRANSMITTED_MODIFIED_TS_INFORMATION_2:24;
    UINT32   TRANSMITTED_ALTERNATE_PROTOCOL_NEGOTIATION_STATUS:2;
    UINT32                                      Reserved_31_26:6;
  } Field;
  UINT32 Value;
} TRANSMITTED_MODIFIED_TS_DATA2_STRUCT;

#define PCICFG_NBIFEPF0CFG_TRANSMITTED_MODIFIED_TS_DATA2_OFFSET     0x51c
#define SMN_DEV0_FUNC0_NBIF0NBIO0_TRANSMITTED_MODIFIED_TS_DATA2_ADDRESS 0x1014051cUL
#define SMN_DEV0_FUNC0_NBIF0NBIO1_TRANSMITTED_MODIFIED_TS_DATA2_ADDRESS 0x1034051cUL
#define SMN_DEV0_FUNC0_NBIF1NBIO0_TRANSMITTED_MODIFIED_TS_DATA2_ADDRESS 0x1024051cUL
#define SMN_DEV0_FUNC0_NBIF1NBIO1_TRANSMITTED_MODIFIED_TS_DATA2_ADDRESS 0x1044051cUL
#define SMN_DEV0_FUNC0_NBIF2NBIO0_TRANSMITTED_MODIFIED_TS_DATA2_ADDRESS 0x1054051cUL
#define SMN_DEV0_FUNC0_NBIF2NBIO1_TRANSMITTED_MODIFIED_TS_DATA2_ADDRESS 0x1074051cUL
#define SMN_DEV1_FUNC0_NBIF0NBIO0_TRANSMITTED_MODIFIED_TS_DATA2_ADDRESS 0x1014851cUL
#define SMN_DEV1_FUNC0_NBIF0NBIO1_TRANSMITTED_MODIFIED_TS_DATA2_ADDRESS 0x1034851cUL
#define SMN_DEV1_FUNC0_NBIF1NBIO0_TRANSMITTED_MODIFIED_TS_DATA2_ADDRESS 0x1024851cUL
#define SMN_DEV1_FUNC0_NBIF1NBIO1_TRANSMITTED_MODIFIED_TS_DATA2_ADDRESS 0x1044851cUL


/***********************************************************
* Register Name : VENDOR_CAP_LIST
************************************************************/

#define VENDOR_CAP_LIST_CAP_ID_OFFSET                          0
#define VENDOR_CAP_LIST_CAP_ID_MASK                            0xff

#define VENDOR_CAP_LIST_NEXT_PTR_OFFSET                        8
#define VENDOR_CAP_LIST_NEXT_PTR_MASK                          0xff00

#define VENDOR_CAP_LIST_LENGTH_OFFSET                          16
#define VENDOR_CAP_LIST_LENGTH_MASK                            0xff0000

#define VENDOR_CAP_LIST_Reserved_31_24_OFFSET                  24
#define VENDOR_CAP_LIST_Reserved_31_24_MASK                    0xff000000

typedef union {
  struct {
    UINT32                                              CAP_ID:8;
    UINT32                                            NEXT_PTR:8;
    UINT32                                              LENGTH:8;
    UINT32                                      Reserved_31_24:8;
  } Field;
  UINT32 Value;
} VENDOR_CAP_LIST_STRUCT;

#define PCICFG_NBIFEPF0CFG_VENDOR_CAP_LIST_OFFSET                   0x48
#define SMN_DEV0_FUNC0_NBIF0NBIO0_VENDOR_CAP_LIST_ADDRESS             0x10140048UL
#define SMN_DEV0_FUNC0_NBIF0NBIO1_VENDOR_CAP_LIST_ADDRESS             0x10340048UL
#define SMN_DEV0_FUNC0_NBIF1NBIO0_VENDOR_CAP_LIST_ADDRESS             0x10240048UL
#define SMN_DEV0_FUNC0_NBIF1NBIO1_VENDOR_CAP_LIST_ADDRESS             0x10440048UL
#define SMN_DEV0_FUNC0_NBIF2NBIO0_VENDOR_CAP_LIST_ADDRESS             0x10540048UL
#define SMN_DEV0_FUNC0_NBIF2NBIO1_VENDOR_CAP_LIST_ADDRESS             0x10740048UL
#define SMN_DEV1_FUNC0_NBIF0NBIO0_VENDOR_CAP_LIST_ADDRESS             0x10148048UL
#define SMN_DEV1_FUNC0_NBIF0NBIO1_VENDOR_CAP_LIST_ADDRESS             0x10348048UL
#define SMN_DEV1_FUNC0_NBIF1NBIO0_VENDOR_CAP_LIST_ADDRESS             0x10248048UL
#define SMN_DEV1_FUNC0_NBIF1NBIO1_VENDOR_CAP_LIST_ADDRESS             0x10448048UL


/***********************************************************
* Register Name : VENDOR_ID
************************************************************/

#define VENDOR_ID_VENDOR_ID_OFFSET                             0
#define VENDOR_ID_VENDOR_ID_MASK                               0xffff

typedef union {
  struct {
    UINT16                                           VENDOR_ID:16;
  } Field;
  UINT16 Value;
} VENDOR_ID_STRUCT;

#define PCICFG_NBIFEPF0CFG_VENDOR_ID_OFFSET                         0x0
#define SMN_DEV0_FUNC0_NBIF0NBIO0_VENDOR_ID_ADDRESS                   0x10140000UL
#define SMN_DEV0_FUNC0_NBIF0NBIO1_VENDOR_ID_ADDRESS                   0x10340000UL
#define SMN_DEV0_FUNC0_NBIF1NBIO0_VENDOR_ID_ADDRESS                   0x10240000UL
#define SMN_DEV0_FUNC0_NBIF1NBIO1_VENDOR_ID_ADDRESS                   0x10440000UL
#define SMN_DEV0_FUNC0_NBIF2NBIO0_VENDOR_ID_ADDRESS                   0x10540000UL
#define SMN_DEV0_FUNC0_NBIF2NBIO1_VENDOR_ID_ADDRESS                   0x10740000UL
#define SMN_DEV1_FUNC0_NBIF0NBIO0_VENDOR_ID_ADDRESS                   0x10148000UL
#define SMN_DEV1_FUNC0_NBIF0NBIO1_VENDOR_ID_ADDRESS                   0x10348000UL
#define SMN_DEV1_FUNC0_NBIF1NBIO0_VENDOR_ID_ADDRESS                   0x10248000UL
#define SMN_DEV1_FUNC0_NBIF1NBIO1_VENDOR_ID_ADDRESS                   0x10448000UL

#endif /* _NBIFEPF0CFG_H_ */

