#;*****************************************************************************
#;
#; Copyright (C) 2008-2024 Advanced Micro Devices, Inc. All rights reserved.
#;
#;******************************************************************************

[defines]

  INF_VERSION                   = 0x00010005
  BASE_NAME                     = FchI3cPei
  FILE_GUID                     = 95a7df51-966f-499c-bcab-9c2422c0d3d2
  MODULE_TYPE                   = PEIM
  VERSION_STRING                = 1.0
  ENTRY_POINT                   = AmdI3cMasterPeiInit


[sources.common]
  I3cMasterPei.c
  I3cMasterPei.h

[LibraryClasses]
  PeimEntryPoint
  FchI3cLib
  FchSocLib
  BaseLib
  DebugLib
  TimerLib
  IoLib
  FchBaseLib
  FabricRegisterAccLib
  BaseFabricTopologyLib

[Guids]

[Protocols]

[Ppis]
  gAmdFchSNI3cPpiGuid                #PRODUCED
  gAmdFabricTopologyServices2PpiGuid #CONSUMED
[Packages]
  MdePkg/MdePkg.dec
  AgesaPkg/AgesaPkg.dec
  AgesaModulePkg/AgesaModuleFchPkg.dec
  AgesaModulePkg/AgesaCommonModulePkg.dec
  AgesaModulePkg/AgesaModuleDfPkg.dec

[Pcd]
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdAmdEnvironmentFlag
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdSkt1FchBus
[Depex]
  gAmdPcdInitReadyPpiGuid AND
  gAmdFabricTopologyServices2PpiGuid
