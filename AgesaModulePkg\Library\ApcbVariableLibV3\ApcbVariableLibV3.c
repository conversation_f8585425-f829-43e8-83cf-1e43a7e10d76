/*****************************************************************************
 * Copyright (C) 2008-2024 Advanced Micro Devices, Inc. All rights reserved.
 *
*****************************************************************************
*/

/*****************************************************************************
 *
 * This software package can be used to enable the Overclocking of certain
 * AMD processors and its use is subject to the terms and conditions of the
 * AMD Overclocking Waiver. Enabling overclocking through use of the low-level
 * routines included in this package and operating an AMD processor outside of
 * the applicable AMD product specifications will void any AMD warranty and can
 * result in damage to the processor or the system into which the processor has
 * been integrated. The user of this software assumes, and AMD disclaims, all
 * risk, liability, costs and damages relating to or arising from the overclocking
 * of AMD processors.
 *
 ******************************************************************************
 */

/*----------------------------------------------------------------------------------------
 *                             M O D U L E S    U S E D
 *----------------------------------------------------------------------------------------
 */
#include "Porting.h"
#include <Library/PcdLib.h>
#include <Library/AmdBaseLib.h>
#include <Library/BaseMemoryLib.h>
#include <Library/ApcbVariableLibV3.h>
#include <Library/AmdDirectoryBaseLib.h>
#include <Library/UefiBootServicesTableLib.h>

#include <Filecode.h>

/*----------------------------------------------------------------------------------------
 *                   D E F I N I T I O N S    A N D    M A C R O S
 *----------------------------------------------------------------------------------------
 */
#define FILECODE        LIBRARY_APCBVARIABLELIBV3_APCBVARIABLELIBV3_FILECODE

/*----------------------------------------------------------------------------------------
 *                  T Y P E D E F S     A N D     S T R U C T U R E S
 *----------------------------------------------------------------------------------------
 */

/*----------------------------------------------------------------------------------------
 *           P R O T O T Y P E S     O F     L O C A L     F U N C T I O N S
 *----------------------------------------------------------------------------------------
 */

/*----------------------------------------------------------------------------------------
 *                          E X P O R T E D    F U N C T I O N S
 *----------------------------------------------------------------------------------------
 */

/*----------------------------------------------------------------------------------------
 *                          G L O B A L        V A L U E S
 *----------------------------------------------------------------------------------------
 */
STATIC APCB_VARIABLE_STRUCT *mApcbVariableStructAddress = NULL;
STATIC BOOLEAN               mIsDiagBLEnabled           = FALSE;

/**
 * @brief Check Soft Fuse: Diag BootLoader is enabled or not
 *
 * @param   VOID
 *
 * @return BOOLEAN TRUE  - Diag BootLoader is enabled
 * @return BOOLEAN FALSE - Diag BootLoader is disabled
 */
BOOLEAN
IsDiagBLEnabled (
  VOID
  )
{
  return mIsDiagBLEnabled;
}

/**
 * @brief Get Apcb Variable Struct Address
 *
 * @param   VOID
 *
 * @return  APCB_VARIABLE_STRUCT* Pointer
 */
APCB_VARIABLE_STRUCT*
GetApcbVariableStruct  (
  VOID
  )
{

  if (mApcbVariableStructAddress == NULL) {
    IDS_HDT_CONSOLE_PSP_TRACE ("[%a] mApcbVariableStructAddress is NULL\n", \
      __FUNCTION__);
  }

  return mApcbVariableStructAddress;
}


/**
 * @brief This function is used to copy Apcb Tokens info into Whitelist buffer.
 *
 * @param[in]    APCB_TOKENS_REG_INFO    *mApcbTokens     The pointer of whitelist buffer
 * @param[in]    UINT32                  mApcbTokensNum   The size of whitelist buffer
 * @param[in]    APCB_TOKENS_REG_INFO    ApcbTokens       The pointer of ApcbTokensRegInfo
 * @param[in]    UINT32                  ApcbTokensNum    The size of ApcbTokensRegInfo
 *
 * @retval       EFI_SUCCESS              Copy Apcb Tokens info into whitelist buffer successfully
 *               EFI_INVALID_PARAMETER    The Parameter is invalid
 *               EFI_BUFFER_TOO_SMALL     Whitelist buffer is full
 */
EFI_STATUS
EFIAPI
CopyApcbTokensToWhitelistBuffer (
  APCB_TOKENS_REG_INFO  *mApcbTokens,
  UINT32                mApcbTokensNum,
  APCB_TOKENS_REG_INFO  *ApcbTokens,
  UINT32                ApcbTokensNum
  )
{
  EFI_STATUS  Status;
  UINT32      Num;
  UINT32      Index;
  UINT32      ApcbTokensCnt;

  Status = EFI_SUCCESS;

  if ((ApcbTokens == NULL) || (ApcbTokensNum == 0)) {
    Status = EFI_INVALID_PARAMETER;
    IDS_HDT_CONSOLE_PSP_TRACE ("  |- The Passed Parameters are Invalid, return Status: %r\n", Status);
    IDS_HDT_CONSOLE_PSP_TRACE ("    |- Addr: 0x%x | Num: %d\n", ApcbTokens, ApcbTokensNum);
    return Status;
  }

  if ((mApcbTokens == NULL) || (mApcbTokensNum == 0)) {
    Status = EFI_INVALID_PARAMETER;
    IDS_HDT_CONSOLE_PSP_TRACE ("  |- The Whitelist Buffer is Illegal.\n");
    IDS_HDT_CONSOLE_PSP_TRACE ("    |- Addr: 0x%x | Num: %d\n", mApcbTokens, mApcbTokensNum);
    return Status;
  }

  ApcbTokensCnt = 0;
  for (Index = 0; Index < mApcbTokensNum; Index++) {
    if (mApcbTokens [Index].TokenId != 0) {
      if ((mApcbTokens [Index].Attribute >= TOKEN_ID_ATTRIBUTE_GET) &&
          (mApcbTokens [Index].Attribute <= TOKEN_ID_ATTRIBUTE_GET_AND_SET)) {
        ApcbTokensCnt++;
      } else {
        IDS_HDT_CONSOLE_PSP_TRACE ("  |- The Apcb Token Id's Attributes in Whitelist Buffer is Illegal, "
          "clear it in the buffer.\n");
        IDS_HDT_CONSOLE_PSP_TRACE ("    |- [%d] Uid: 0x%x | Attribute: %d\n", \
          Index, mApcbTokens [Index].TokenId, mApcbTokens [Index].Attribute);
        ZeroMem (&mApcbTokens [Index], sizeof (APCB_TOKENS_REG_INFO));
      }
    }
  }

  if (mApcbTokensNum <= ApcbTokensCnt) {
    Status = EFI_BUFFER_TOO_SMALL;
    IDS_HDT_CONSOLE_PSP_TRACE ("  |- Whitelist Buffer is full !!!\n");
    return Status;
  }

  if ((mApcbTokensNum - ApcbTokensCnt) < ApcbTokensNum) {
    IDS_HDT_CONSOLE_PSP_TRACE ("  |- The Whitelist remaining empty buffer Num is less then ApcbTokensRegInfoNum\n");
    IDS_HDT_CONSOLE_PSP_TRACE ("    |- The remaining buffer Num: %d | ApcbTokensRegInfoNum: %d\n", \
        (mApcbTokensNum - ApcbTokensCnt), ApcbTokensNum);
    IDS_HDT_CONSOLE_PSP_TRACE ("    |- It will still register tokens with the remaining empty buffer until the buffer is full.\n");
  }

  for (Num = 0; Num < ApcbTokensNum; Num++) {
    for (Index = 0; Index < mApcbTokensNum; Index++) {

      if ((ApcbTokens [Num].TokenId != 0) &&
          ((ApcbTokens [Num].Attribute >= TOKEN_ID_ATTRIBUTE_GET) &&
           (ApcbTokens [Num].Attribute <= TOKEN_ID_ATTRIBUTE_GET_AND_SET))) {

        // Allow Token Id to overrid it's attribute
        if (mApcbTokens [Index].TokenId == ApcbTokens [Num].TokenId) {
          mApcbTokens [Index].Attribute = ApcbTokens [Num].Attribute;
          break;
        }

        // Add Token Id into Whitelist Buffer
        if ((mApcbTokens [Index].TokenId == 0) &&
            (mApcbTokens [Index].Attribute == 0)) {

          CopyMem (&mApcbTokens [Index], &ApcbTokens [Num], \
            sizeof(APCB_TOKENS_REG_INFO));
          ApcbTokensCnt++;
          break;
        }

        if ((ApcbTokensCnt == mApcbTokensNum) &&
            (Index == (mApcbTokensNum - 1))) {
          IDS_HDT_CONSOLE_PSP_TRACE ("  |- Whitelist Buffer is full !!!\n");
          IDS_HDT_CONSOLE_PSP_TRACE ("    |- Uid: 0x%x | Attribute: %d, can not be set into the Buffer.\n", \
            ApcbTokens [Num].TokenId, ApcbTokens [Num].Attribute);
          Status = EFI_BUFFER_TOO_SMALL;
          break;
        }
      }
    }
  }

  return Status;

}


/**
 * @brief ApcbVariableLibV3Constructor
 *
 * @param[in] EFI_HANDLE        ImageHandle
 * @param[in] EFI_SYSTEM_TABLE  *SystemTable

 * @return  EFI_STATUS     EFI_SUCCESS       Success
 *                         Other Status      Fail
 */
EFI_STATUS
EFIAPI
ApcbVariableLibV3Constructor (
  IN EFI_HANDLE        ImageHandle,
  IN EFI_SYSTEM_TABLE  *SystemTable
  )
{
  EFI_STATUS            Status;
  APCB_VARIABLE_STRUCT  *pApcbVariableStruct;
  UINT32                Ignored;
  UINT64                EntryValue;

  Status      = EFI_SUCCESS;
  EntryValue  = 0;

  if (PSPEntryInfoV2 (AMD_SOFT_FUSE_CHAIN_01, &EntryValue, &Ignored) == TRUE) {
    if (EntryValue & BIT5) {
      mIsDiagBLEnabled = TRUE;
    }
  }

  // After this point mApcbVariableStructAddress should already setup,
  // it can be used to store the real buffer address.
  // Check if address exist, if none allocate buffer, set buffer address.
  if (mApcbVariableStructAddress == NULL) {
    // In order to make all mApcbVariableStructAddress store the same run-time address,
    // use PCD to avoid allocating different run-time buffers.
    if (PcdGet64 (PcdApcbVariableStructAddress) == 0) {
      pApcbVariableStruct = NULL;
      Status = gBS->AllocatePool (
                      EfiRuntimeServicesData,
                      sizeof(APCB_VARIABLE_STRUCT),
                      (VOID **) &pApcbVariableStruct
                      );
      if (EFI_ERROR(Status)) {
        IDS_HDT_CONSOLE_PSP_TRACE ("[%a] AllocatePool Fail, Status: %r\n", \
          __FUNCTION__, Status);
        ASSERT (FALSE);
        return EFI_SUCCESS;
      }

      if (pApcbVariableStruct == NULL) {
        IDS_HDT_CONSOLE_PSP_TRACE ("[%a] AllocatePool Address is NULL\n", \
          __FUNCTION__);
        ASSERT (FALSE);
        return EFI_SUCCESS;
      }

      IDS_HDT_CONSOLE_PSP_TRACE ("[%a] AllocatePool Address:       0x%x\n", \
        __FUNCTION__, pApcbVariableStruct);

      ZeroMem (pApcbVariableStruct, sizeof(APCB_VARIABLE_STRUCT));
      PcdSet64S (PcdApcbVariableStructAddress, (UINT64) (UINTN) pApcbVariableStruct);
    }

    mApcbVariableStructAddress = (APCB_VARIABLE_STRUCT *) (UINTN) PcdGet64 (PcdApcbVariableStructAddress);

    IDS_HDT_CONSOLE_PSP_TRACE ("[%a] mApcbVariableStructAddress: 0x%x\n", \
      __FUNCTION__, mApcbVariableStructAddress);
  }

  return EFI_SUCCESS;
}

