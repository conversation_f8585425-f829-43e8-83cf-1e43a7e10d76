/*****************************************************************************
 * Copyright (C) 2008-2024 Advanced Micro Devices, Inc. All rights reserved.
 *
*****************************************************************************
*/
/* $NoKeywords:$ */
/**
 * @file
 *
 * FCH DXE Driver
 *
 *
 * @xrefitem bom "File Content Label" "Release Content"
 * @e project:      AGESA
 * @e sub-project   FCH DXE Driver
 * @e \$Revision: 309090 $   @e \$Date: 2014-12-09 10:28:05 -0800 (Tue, 09 Dec 2014) $
 *
 */

#ifndef _FCH_DXE_H_
#define _FCH_DXE_H_

#include <Library/BaseLib.h>
#include <Library/DebugLib.h>
#include <Library/UefiLib.h>
#include <Library/HobLib.h>
#include <Library/UefiDriverEntryPoint.h>
#include <Library/UefiBootServicesTableLib.h>

#include "FchPlatform.h"
#include <Library/FchInitHookLib.h>
#include <Library/FchDxeLib.h>

#include <IdsHookId.h>
#include <Library/AmdIdsHookLib.h>

#include <Protocol/FchInitProtocol.h>
#include <Protocol/FchInitDonePolicyProtocol.h>

#include <Library/FabricRegisterAccLib.h>
#include <Protocol/FabricTopologyServices2.h>

#include <Library/AgesaConfigLib.h>
#include <ActOptions.h>

//
// Module data structure
//
/// Private data and access defines
typedef struct _FCH_DXE_PRIVATE {
  UINTN                            Signature;           ///< Signature
  FCH_INIT_PROTOCOL                FchInit;              ///< Protocol data
  EFI_EVENT                        EventAfterPciInit;   ///< Event related data
  EFI_EVENT                        EventReadyToBoot;    ///< Event related data
} FCH_DXE_PRIVATE;

#define FCH_DXE_PRIVATE_DATA_SIGNATURE   SIGNATURE_32 ('S', 'B', 'i', 'D')

#define FCH_DXE_PRIVATE_INSTANCE_FROM_PPI_THIS(a) \
  CR (a, FCH_PEI_PRIVATE, FchInit, FCH_DXE_PRIVATE_DATA_SIGNATURE)

#define FEATURE_CPPC_MASK                    (1 << 22)
//
// Functions Prototypes
//
EFI_STATUS
EFIAPI
FchDxeInit (
  IN       EFI_HANDLE         ImageHandle,
  IN       EFI_SYSTEM_TABLE   *SystemTable
  );

#endif // _FCH_DXE_H_



