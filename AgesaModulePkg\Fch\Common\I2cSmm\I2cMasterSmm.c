/*****************************************************************************
 *
 * Copyright (C) 2008-2024 Advanced Micro Devices, Inc. All rights reserved.
 *
 *******************************************************************************
 */


#include "Uefi.h"
#include "I2cMasterSmm.h"
#include <DwI2cRegs.h>
#include <Protocol/FchSmmI2cMasterProtocol.h>
#include <Protocol/FabricTopologyServices2.h>
#include <Library/SmmServicesTableLib.h>
#include <Library/FchSocLib.h>
#include <Library/FchBaseLib.h>
#include <Library/FchI2cLib.h>
#include <Library/DebugLib.h>
#include <Library/IdsLib.h>

#define FILECODE FCH_COMMON_I2CSMM_I2CMASTERSMM_FILECODE


#define I2CSMM_DEBUG_ON 0
#if   I2CSMM_DEBUG_ON == 1
#define  I2CSMM_DEBUG   FCH_TRACE
#else
#define  I2CSMM_DEBUG   FCH_TRACE_RSV2
#endif

UINTN    mSkt1FchBus = 0;
/**
   *  @brief This function writes a value to SMNIO
   *  @param Addr - address to write
   *  @param Value
   *  @retval VOIDs
   */
VOID
EFIAPI
SmnioWrite32 (
  IN  UINTN   Addr,
  IN  UINT32  Value
  )
{
  UINT32 Val = Value;

  FchSmnWrite ((UINT32)mSkt1FchBus,  (UINT32)Addr, &Val, NULL);
}

/**
   *  @brief This function reads a value from SMNIO
   *  @param addr - address to read
   *  @retval UINT32 : value of the address
   */
UINT32
EFIAPI
SmnioRead32 (
  IN      UINTN Addr
  )
{
  UINT32 Value;

  FchSmnRead ((UINT32)mSkt1FchBus, (UINT32)Addr, &Value, NULL);
  return Value;
}


/**
   *  @brief This function writes a value to MMIO
   *  @param Addr - address to write
   *  @param Value
   *  @retval VOIDs
   */
VOID
EFIAPI
I2cMmioWrite32 (
  IN  UINTN   Addr,
  IN  UINT32  Value
  )
{
  MmioWrite32(Addr, Value);
}

/**
 * @brief Get  and the number of installed processor and socket 1's FCH Bus Number.
 *
 * @returns UINTN        2nd socket bus number
 */
EFI_STATUS
GetNumProc_Sokcet1Bus(
  OUT  UINTN   *NumOfCPU,
  OUT  UINTN   *Skt1FchBus
  )
{
  EFI_STATUS                              Status;
  UINTN                                   BusNumberBase;
  AMD_FABRIC_TOPOLOGY_SERVICES2_PROTOCOL  *FabricTopology;
  BOOLEAN                                 HasFchDevice;
  UINTN                                   i;
  UINTN                                   NumberOfInstalledProcessors = 0;
  UINTN                                   NumberOfRootBridges;

  Status          = EFI_SUCCESS;
  FabricTopology  = NULL;
  BusNumberBase   = 0;
  *Skt1FchBus = 0;
  *NumOfCPU = 1;

  Status = gSmst->SmmLocateProtocol (
                    &gAmdFabricTopologyServices2SmmProtocolGuid,
                    NULL,
                    (VOID **) &FabricTopology
                    );

  Status = FabricTopology->GetSystemInfo (
                           FabricTopology,
                           &NumberOfInstalledProcessors,
                           NULL,
                           NULL,
                           NULL,
                           NULL
                           );
  if ( EFI_ERROR(Status)) {
    ASSERT(Status);
    *Skt1FchBus = 0;
    *NumOfCPU = 1;
    return Status;
  }

  *NumOfCPU = NumberOfInstalledProcessors;
  IDS_HDT_CONSOLE(FCH_TRACE, "NumOfCPU installed = 0x%x\n", NumOfCPU);
  if ( NumberOfInstalledProcessors == 1) {
    IDS_HDT_CONSOLE(FCH_TRACE, " no Socket1 FCH ret\n");
    *Skt1FchBus = 0;
    return EFI_SUCCESS;
  }
  if (Status == EFI_SUCCESS &&  NumberOfInstalledProcessors > 1 ) {
    Status = FabricTopology->GetDieInfo (
                               FabricTopology,
                               1,//socket 1
                               0, //Die 0
                               &NumberOfRootBridges,
                               NULL,
                               NULL
                               );
    if (Status == EFI_SUCCESS) {
      for (i = 0; i < NumberOfRootBridges; i++) {
        Status = FabricTopology->GetRootBridgeInfo (
                                   FabricTopology,
                                   1, //socket 1
                                   0, //die 0
                                   i,
                                   NULL,
                                   &BusNumberBase,
                                   NULL,
                                   NULL,
                                   &HasFchDevice,
                                   NULL
                                   );
        if (!EFI_ERROR (Status) && HasFchDevice) {
          IDS_HDT_CONSOLE(FCH_TRACE, "Socket1 Fch Bus:0x%x\n", BusNumberBase);
          *Skt1FchBus =  BusNumberBase;
        }
      }
    }
  }
  return EFI_SUCCESS;
}


/**
   *  @brief Set Bus Frequency
   *  @param[in] *This - EFI_I2C_MASTER_PROTOCOL table
   *  @param[in] *BusClockHertz - pointer to the BUS Clock Hertz
   *  @returns EFI_STATUS
   *  @retval EFI_SUCCESS : Set Bus Frequency successfully
   *          EFI_ERROR   : Failed (see error for more details)
   */
EFI_STATUS
EFIAPI
SetBusFrequency (
  IN  CONST FCH_SMM_I2C_MASTER_PROTOCOL   *This,
  IN  OUT UINTN                           *BusClockHertz
  )
{
  EFI_STATUS               Status = EFI_SUCCESS;
  I2C_MASTER_SMM_PRIVATE   *I2cMasterSmmPrivate;
  UINT32                   settings;
  UINT32                   Base;
  UINT8                    IsOnMainSocket;
  UINT32                   hcnt;
  UINT32                   lcnt;

  I2cRegisterRead32    I2cRegRead32;
  I2cRegisterWrite32   I2cRegWrite32;

  settings = 0;
  I2cMasterSmmPrivate = I2C_MASTER_SMM_PRIVATE_INSTANCE_FROM_THIS(This);

  // Get the base address for the controller
  Base = I2cMasterSmmPrivate->I2cBaseAddress;

  // Get this controller is in Socket 0 or 1
  IsOnMainSocket = ((Base & 0xFFFF0000) == 0xFEDC0000) ? 0x01 : 0x00;

  if (IsOnMainSocket == 0x01) {
    I2cRegRead32 = (I2cRegisterRead32) MmioRead32;
    I2cRegWrite32 = I2cMmioWrite32;
  } else {
    I2cRegRead32 = (I2cRegisterRead32) SmnioRead32;
    I2cRegWrite32 = (I2cRegisterWrite32) SmnioWrite32;
  }

  // Disable the interface
  I2cRegWrite32 (Base + DW_IC_ENABLE, 0);
  if (I2cDwWaitI2cEnable (Base, CHECK_IC_EN_HIGH, I2cRegRead32)) {
    return EFI_NOT_READY;
  }

  settings |= DW_I2C_CON_MASTER_MODE | DW_I2C_CON_IC_SLAVE_DISABLE;

  if (HS_SPEED <= *BusClockHertz) {
    settings |= DW_I2C_CON_SPEED_HS;
    //*BusClockHertz = FS_SPEED;        //Return actually clock setting
    *BusClockHertz = HS_SPEED;
  }
  else if (FS_SPEED <= *BusClockHertz) {
    settings |= DW_I2C_CON_SPEED_FS;
    *BusClockHertz = FS_SPEED;        //Return actually clock setting
  }
  else {
    settings |= DW_I2C_CON_SPEED_SS;
    *BusClockHertz = SS_SPEED;        //Return actually clock setting
  }

  settings |= DW_I2C_CON_IC_RESTART_EN;

  I2cRegWrite32 (Base + DW_IC_CON, settings);

  // Setup spike suppression for SS and FS at 50ns
  I2cRegWrite32 (Base + DW_IC_FS_SPKLEN, configI2C_FS_GLITCH_FILTER);

  // Setup spike suppression for HS at 10ns
  I2cRegWrite32 (Base + DW_IC_FS_SPKLEN, configI2C_FS_GLITCH_FILTER);

  // Standard-mode 100Khz
  hcnt = AMD_SS_SCL_HCNT;
  lcnt = AMD_SS_SCL_LCNT;
  I2cRegWrite32 (Base + DW_IC_SS_SCL_HCNT, hcnt); // std speed high, 4us
  I2cRegWrite32 (Base + DW_IC_SS_SCL_LCNT, lcnt); // std speed low, 4.7us

  IDS_HDT_CONSOLE(FCH_TRACE, "Standard-mode HCNT:LCNT = %d:%d\n", hcnt, lcnt);

  // Fast-mode 400Khz
  hcnt = AMD_FS_SCL_HCNT;
  lcnt = AMD_FS_SCL_LCNT;
  I2cRegWrite32 (Base + DW_IC_FS_SCL_HCNT, hcnt);
  I2cRegWrite32 (Base + DW_IC_FS_SCL_LCNT, lcnt);
  I2cRegWrite32 (Base + DW_IC_HS_SCL_HCNT, hcnt);
  I2cRegWrite32 (Base + DW_IC_HS_SCL_LCNT, lcnt);

  IDS_HDT_CONSOLE(FCH_TRACE, "Fast-mode HCNT:LCNT = %d:%d\n", hcnt, lcnt);

  return Status;
}



/**
   *  @brief Start request
   *  @param[in] *This - EFI_I2C_MASTER_PROTOCOL table
   *  @param[in] SlaveAddress -
   *  @param[in] *RequestPacket - EFI_I2C_REQUEST_PACKET table
   *  @param[in] Event -
   *  @param[out] *I2cStatus -
   *  @returns EFI_STATUS
   *  @retval EFI_SUCCESS :
   */
EFI_STATUS
EFIAPI
StartRequest (
  IN  CONST FCH_SMM_I2C_MASTER_PROTOCOL  *This,
  IN  UINTN                               SlaveAddress,
  IN  EFI_I2C_REQUEST_PACKET              *RequestPacket
  )
{
  EFI_STATUS               Status;
  I2C_MASTER_SMM_PRIVATE   *I2cMasterSmmPrivate;
  EFI_I2C_OPERATION        *Operation;
  UINT32                   Base;
  UINT8                    IsOnMainSocket;
  UINT32                   Index;
  UINTN                    OperationCount;

  I2cRegisterRead32 I2cRegRead32;
  I2cRegisterWrite32 I2cRegWrite32;
  I2cMasterSmmPrivate = I2C_MASTER_SMM_PRIVATE_INSTANCE_FROM_THIS(This);



  // Get the base address of the controller
  Base = I2cMasterSmmPrivate->I2cBaseAddress;
  // Check if the controller base address is main Socket
  IsOnMainSocket = ((Base & 0xFFFF0000) == 0xFEDC0000) ? 0x01 : 0x00;

  if (IsOnMainSocket == 0x01) {
    I2cRegRead32 = (I2cRegisterRead32) MmioRead32;
    I2cRegWrite32 = I2cMmioWrite32;
  } else {
    I2cRegRead32 = (I2cRegisterRead32) SmnioRead32;
    I2cRegWrite32 = (I2cRegisterWrite32) SmnioWrite32;
  }

  //
  // I2cAccess for Read/Write data
  //
  Operation = RequestPacket->Operation;
  OperationCount = RequestPacket->OperationCount;

  if (OperationCount == 0x00) {
    Status = EFI_UNSUPPORTED;
    goto Exit;
  }

  for (Index=0; Index < OperationCount; Index++) {
    if (Operation[Index].LengthInBytes == 0x00) {
      // We do not support quick read/write
      Status = EFI_UNSUPPORTED;
      goto Exit;
    } else if (Operation[Index].Flags & (I2C_FLAG_SMBUS_PEC | I2C_FLAG_SMBUS_PROCESS_CALL)) {
      // No PEC, ProcessCall and BlkProcessCall either
      Status = EFI_UNSUPPORTED;
      goto Exit;
    }
  }

  //Set target device slave address
  if (I2cSetTarget ((UINT32)SlaveAddress, Base, I2cRegRead32, I2cRegWrite32) != EFI_SUCCESS) {
    Status = EFI_DEVICE_ERROR;
    goto Exit;
  }

  if (I2cDwWaitBusNotBusy (Base, I2cRegRead32)) {
    Status = EFI_NOT_READY;
    goto Exit;
  }

  I2cRegWrite32 (Base + DW_IC_INTR_MASK, 0);
  (VOID)I2cRegRead32 (Base + DW_IC_CLR_INTR);

  IDS_HDT_CONSOLE(I2CSMM_DEBUG, "Enable I2cInterface\n");
  // Enable the interface
  I2cRegWrite32 (Base + DW_IC_ENABLE, DW_I2C_ENABLE);
  if (I2cDwWaitI2cEnable (Base, CHECK_IC_EN_LOW, I2cRegRead32)) {
    Status = EFI_NOT_READY;
    goto Exit;
  }

  for (Index=0; Index < OperationCount; Index++) {
    if (Operation[Index].Flags == 0x00) {
      // Write operation
      //  - if OperationCount == 2, it is 2 step write-and-read. We do not STOP on write operation in this case.
      IDS_HDT_CONSOLE(I2CSMM_DEBUG, "I2cMasterSmm.c StartRequest Operation (Write) Index: %u.\n", Index);
      Status = I2cPrivateWrite (Base, Operation[Index].Buffer, Operation[Index].LengthInBytes, I2cMasterSmmPrivate->TxFifoDepth, OperationCount, I2cRegRead32, I2cRegWrite32);
    } else {
      // Read operation
      IDS_HDT_CONSOLE(I2CSMM_DEBUG, "I2cMasterSmm.c StartRequest Operation (Read) Index: %u.\n", Index);
      Status = I2cPrivateRead (Base, Operation[Index].Buffer, Operation[Index].LengthInBytes, I2cMasterSmmPrivate->RxFifoDepth, I2cRegRead32, I2cRegWrite32);
    }

    if (Status != EFI_SUCCESS) {
      goto Exit;
    }
  }

  // Disable the interface
  IDS_HDT_CONSOLE(I2CSMM_DEBUG, "[Debug] Disable I2cInterface\n");
  I2cRegWrite32 (Base + DW_IC_ENABLE, 0);
  I2cDwWaitI2cEnable (Base, CHECK_IC_EN_HIGH, I2cRegRead32);  //Wait controller status change

Exit:

  return Status;
}

/**
 * @brief Entry point of the I2c master SMM Driver.
 *
 * @details
 *
 * @param[in] ImageHandle EFI Image Handle for the DXE driver
 * @param[in] SystemTable Pointer to the EFI system table
 *
 * @returns EFI_STATUS
 * @retval EFI_SUCCESS   Module initialized successfully
 * @retval EFI_ERROR     Initialization failed (see error for more details)
 */
EFI_STATUS
EFIAPI
I2cMasterSmmInit (
  IN       EFI_HANDLE         ImageHandle,
  IN       EFI_SYSTEM_TABLE   *SystemTable
  )

{

  EFI_STATUS                              Status = EFI_SUCCESS;
  UINT32                                  Index;
  UINT8                                   I2cControllerCount;
  UINT8                                   NumOfSupportedSockets;
  UINTN                                   NumOfSockets;
  I2C_MASTER_SMM_PRIVATE                 *FchSmmI2CMasterController = NULL;
  EFI_HANDLE                             *FchSmmI2CMasterHandle = NULL;
  UINT8                                   IsOnMainSocket;

  IDS_HDT_CONSOLE(FCH_TRACE, "%a entry\n", __FUNCTION__ );
  //get Socket1 FCH bus number
  GetNumProc_Sokcet1Bus(&NumOfSockets, &mSkt1FchBus);

  I2cControllerCount = FchSocI2cGetControllerCount ();
  IDS_HDT_CONSOLE(FCH_TRACE, "NumberOfI2cControllerPerSocket: %u.\n", I2cControllerCount);

  NumOfSupportedSockets = FchSocI2cGetNumOfSupportedSockets ();
  IDS_HDT_CONSOLE(FCH_TRACE, "NumOfSockets: %u, MaxNumOfSupportedSockets: %u.\n", NumOfSockets, NumOfSupportedSockets);

  if (NumOfSockets > NumOfSupportedSockets)
  {
    IDS_HDT_CONSOLE(FCH_TRACE, "NumOfSockets (%u) is greater than number of supported sockets (%u).\n", NumOfSockets, NumOfSupportedSockets);
    NumOfSockets = NumOfSupportedSockets;
  }
  //create smm protocol
  Status = gSmst->SmmAllocatePool (
                    EfiRuntimeServicesData,
                    NumOfSockets * I2cControllerCount * sizeof (I2C_MASTER_SMM_PRIVATE),
                    (VOID **) &FchSmmI2CMasterController
                    );

  Status = gSmst->SmmAllocatePool (
                    EfiRuntimeServicesData,
                    NumOfSockets * I2cControllerCount * sizeof(EFI_HANDLE),
                    (VOID **) &FchSmmI2CMasterHandle
                    );

  // Set SDA HOLD TIME & Bus Frequency
  for (Index = 0; Index < I2cControllerCount; Index++) {
    FchSmmI2CMasterController[Index].I2cSdaHoldTime = FchSocI2cSetSdaHoldTime (Index);
    if (FchSmmI2CMasterController[Index].I2cSdaHoldTime == 0xFFFFFFFF)
    {
      IDS_HDT_CONSOLE(FCH_TRACE, "Setting SDA HOLD TIME failed - Wrong parameter\n");
    }

    FchSmmI2CMasterController[Index].I2cBusFrequency = FchSocI2cSetBusFrequency (Index);
    if (FchSmmI2CMasterController[Index].I2cBusFrequency == 0xFFFFFFFF)
    {
      IDS_HDT_CONSOLE(FCH_TRACE, "Setting BUS FREQUENCY failed - Wrong parameter\n");
    }
  }
  if (NumOfSockets > 1) {
    for (Index = 0; Index < I2cControllerCount; Index++) {
      // Copy SDA Hold Time, Bus Frequency info to the second Socket
      FchSmmI2CMasterController[Index+I2cControllerCount].I2cSdaHoldTime = FchSocI2cSetSdaHoldTime (Index);
      FchSmmI2CMasterController[Index+I2cControllerCount].I2cBusFrequency = FchSocI2cSetBusFrequency (Index);
    }
  }

  for (Index = 0; Index < (UINT8)(NumOfSockets* I2cControllerCount); Index++) {
    FchSmmI2CMasterController[Index].Signature                                 = I2C_MASTER_SMM_PRIVATE_DATA_SIGNATURE;
    FchSmmI2CMasterController[Index].I2cSmmController.Revision                 = FCH_SMM_I2C_MASTER_PROTOCOL_REV;
    FchSmmI2CMasterController[Index].I2cSmmController.SetBusFrequency          = SetBusFrequency;
    FchSmmI2CMasterController[Index].I2cSmmController.StartRequest             = StartRequest;
    FchSmmI2CMasterController[Index].I2cSmmController.ControllerNum            = Index;
    FchSmmI2CMasterController[Index].I2cBaseAddress                            = FchSocI2cGetBaseAddress (Index);

    IsOnMainSocket = ((FchSmmI2CMasterController[Index].I2cBaseAddress & 0xFFFF0000) == 0xFEDC0000) ? 0x01 : 0x00;
    // Check if it is Socket 0 or 1
    if (IsOnMainSocket == 0x01) {
      //socket 0
      // get RxFifoDepth and TxFifoDepth
      FchSmmI2CMasterController[Index].RxFifoDepth = I2cGetRxTxFifoDepth (FchSmmI2CMasterController[Index].I2cBaseAddress, 0, MmioRead32);
      FchSmmI2CMasterController[Index].TxFifoDepth = I2cGetRxTxFifoDepth (FchSmmI2CMasterController[Index].I2cBaseAddress, 1, MmioRead32);
      // do not I2C init since DXe driver already init it
      // Status = I2cInit (
      //   FchSmmI2CMasterController[Index].I2cBaseAddress,
      //   FchSmmI2CMasterController[Index].I2cBusFrequency,
      //   FchSmmI2CMasterController[Index].I2cSdaHoldTime,
      //   MmioRead32,
      //   I2cMmioWrite32
      //   );
    } else {
      //socket 0
      // get RxFifoDepth and TxFifoDepth
      FchSmmI2CMasterController[Index].RxFifoDepth = I2cGetRxTxFifoDepth (FchSmmI2CMasterController[Index].I2cBaseAddress, 0, SmnioRead32);
      FchSmmI2CMasterController[Index].TxFifoDepth = I2cGetRxTxFifoDepth (FchSmmI2CMasterController[Index].I2cBaseAddress, 1, SmnioRead32);
      //  do not I2C init since DXe driver already init it
      // Status = I2cInit (FchSmmI2CMasterController[Index].I2cBaseAddress, FchSmmI2CMasterController[Index].I2cBusFrequency,
      //                    FchSmmI2CMasterController[Index].I2cSdaHoldTime, SmnioRead32, SmnioWrite32);
    }
    //install smm protocol
    FchSmmI2CMasterHandle[Index] = NULL;
    Status = gSmst->SmmInstallProtocolInterface (
                    &FchSmmI2CMasterHandle[Index],
                    &gAmdFchSmmI2cMasterProtocolGuid,
                    EFI_NATIVE_INTERFACE,
                    &FchSmmI2CMasterController[Index].I2cSmmController
                    );
    IDS_HDT_CONSOLE(FCH_TRACE, "Install I2C SMM master protcol[%d] ,Status =%r\n", Index, Status);
  }


  return Status;
}





