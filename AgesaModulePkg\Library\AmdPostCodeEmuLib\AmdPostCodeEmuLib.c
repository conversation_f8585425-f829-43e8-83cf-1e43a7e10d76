/*****************************************************************************
 * Copyright (C) 2008-2024 Advanced Micro Devices, Inc. All rights reserved.
 *
*****************************************************************************
*/

 /*----------------------------------------------------------------------------------------
 *                             M O D U L E S    U S E D
 *----------------------------------------------------------------------------------------
 */
#include <Uefi.h>
#include <Library/AmdBaseLib.h>
#include <Library/PciLib.h>
#include <Library/IoLib.h>
#include <Library/IdsLib.h>
#include <Library/PcdLib.h>
#include <Filecode.h>

#define FILECODE  LIBRARY_IDSMISCLIB_IDSMISCLIB_FILECODE
#define NBCFG_SCRATCH_0_SMN_ADDR 0x13B00068
#define MP0_C2PMSG_97               0x03810A84ul
#define NB_SMN_INDEX_2_PCI_ADDR (PCI_LIB_ADDRESS (0, 0, 0, 0xA0))  ///< PCI Addr of NB_SMN_INDEX_1
#define NB_SMN_DATA_2_PCI_ADDR (PCI_LIB_ADDRESS (0, 0, 0, 0xA4))  ///< PCI Addr of NB_SMN_DATA_1


/**
  Light version SMN read with less depx, SmnAccessLib still have too many depex

  @param[in]  Address, SMN address

**/
STATIC
UINT32
SmnRegRead (
  UINT32 Address
  )
{
  UINTN                  PciAddress;

  PciAddress = NB_SMN_INDEX_2_PCI_ADDR;
  PciWrite32 (PciAddress, Address);
  PciAddress = NB_SMN_DATA_2_PCI_ADDR;
  return PciRead32 (PciAddress);
}

/**
  Light version SMN write with less depx

  @param[in]  Address, SMN address
  @param[in]  Value, Value to be writen to the address

**/
STATIC
VOID
SmnRegWriteWidth (
  IN        ACCESS_WIDTH AccessWidth,
  UINT32    Address,
  IN        VOID *Value
  )
{
  UINTN                  PciAddress;
  UINT32                 Value32;
  Value32 = 0;
  switch (AccessWidth) {
  case AccessWidth8:
  case AccessS3SaveWidth8:
    Value32 |= *(UINT8 *)Value;
    break;
  case AccessWidth16:
  case AccessS3SaveWidth16:
    Value32 |= *(UINT16 *)Value;
    break;
  case AccessWidth32:
  case AccessS3SaveWidth32:
    Value32 |= *(UINT32 *)Value;
    break;
  default:
    Value32 |= *(UINT32 *)Value;
  }
  PciAddress = NB_SMN_INDEX_2_PCI_ADDR;
  PciWrite32 (PciAddress, Address);
  PciAddress = NB_SMN_DATA_2_PCI_ADDR;
  PciWrite32 (PciAddress, Value32);
}


/**
  Light version IO write with less depx

  @param[in]  Address, IO address
  @param[in]  Value, Value to be writen to the address

**/
STATIC
VOID
IoWriteWidth (
  IN        ACCESS_WIDTH AccessWidth,
  UINT32    Address,
  IN        VOID *Value
  )
{
  switch (AccessWidth) {
  case AccessWidth8:
  case AccessS3SaveWidth8:
    IoWrite8 (Address, *(UINT8 *)Value);
    break;
  case AccessWidth16:
  case AccessS3SaveWidth16:
    IoWrite16 (Address, *(UINT16 *)Value);
    break;
  case AccessWidth32:
  case AccessS3SaveWidth32:
    IoWrite32 (Address, *(UINT32 *)Value);
    break;
  default:
    IoWrite32 (Address, *(UINT32 *)Value);
  }

}

/**
 * @brief Output Postcode to IO Port PcdIdsDebugPort specified, output to NBCFG_SCRATCH_0 in emulation case
 *
 * @param[in] AccessWidth   Access width
 * @param[in] Value         Pointer to data
 * @return VOID
 */
VOID
LibAmdPostCode (
  IN       ACCESS_WIDTH AccessWidth,
  IN       VOID *Value
  )
{
  BOOLEAN                UseNbScratchReg;

  UseNbScratchReg = FALSE;
  //This library will be called even before HOB service established, use direct C2P register read instead of "EmulationFlagCheck"
  // UINT32 Port80Redirect:1;               ///< BIT15 1= Redirect the port80 writes to NBCFG_SCRATCH_0
  UseNbScratchReg = ((SmnRegRead (MP0_C2PMSG_97) & BIT15) ? 1 : 0);

  if (UseNbScratchReg) {
    SmnRegWriteWidth (AccessWidth, NBCFG_SCRATCH_0_SMN_ADDR, Value);
  } else {
    IoWriteWidth (AccessWidth, PcdGet16 (PcdIdsDebugPort), Value);
  }
}

