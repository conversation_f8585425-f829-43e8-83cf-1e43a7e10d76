#;*****************************************************************************
#;
#; Copyright (C) 2008-2024 Advanced Micro Devices, Inc. All rights reserved.
#;
#;******************************************************************************

[Defines]
  INF_VERSION                    = 0x00010006
  BASE_NAME                      = FchI3cLib
  FILE_GUID                      = 71862c2b-fff3-435c-8563-a503d1609b0b
  MODULE_TYPE                    = BASE
  VERSION_STRING                 = 1.0
  LIBRARY_CLASS                  = FchI3cLib

[Sources]
  FchI3cLib.c

[Packages]
  MdePkg/MdePkg.dec
  AgesaModulePkg/AgesaModuleFchPkg.dec
  AgesaModulePkg/AgesaCommonModulePkg.dec
  AgesaPkg/AgesaPkg.dec

[LibraryClasses]
  BaseLib
  DebugLib
  BaseMemoryLib
[Pcd]
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdFchI3CDxeDebugOff
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdFchI3cTimeoutUs
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdFchI3cTimeoutRetry

