#;*****************************************************************************
#;
#; Copyright (C) 2008-2024 Advanced Micro Devices, Inc. All rights reserved.
#;
#;******************************************************************************
#For EDKII use Only
[Defines]
  INF_VERSION                    = 0x00010005
  BASE_NAME                      = FabricResourceManager3Lib
  FILE_GUID                      = DDF8DB92-981A-4A27-A832-DFCD9E7D2823
  MODULE_TYPE                    = BASE
  VERSION_STRING                 = 1.0
  LIBRARY_CLASS                  = FabricResourceManagerLib

[Sources]
  FabricResourceManager3.c
  FabricResourceInitBasedOnNv3.c
  FabricResourceManager3.h

[Packages]
  MdePkg/MdePkg.dec
  AgesaModulePkg/AgesaCommonModulePkg.dec
  AgesaModulePkg/AgesaModuleDfPkg.dec
  AgesaPkg/AgesaPkg.dec

[LibraryClasses]
  BaseLib
  AmdBaseLib
  AmdS3SaveLib
  AmdHeapLib
  BaseFabricTopologyLib
  BaseCoreLogicalIdLib
  FabricRegisterAccLib
  IdsLib
  FabricResourceSizeForEachRbLib
  FabricResourceReportToGcdLib

[Guids]
  gFabricRootBridgeOrderInfoHobGuid

[Protocols]

[Ppis]

[Pcd]
  gEfiMdePkgTokenSpaceGuid.PcdPciExpressBaseAddress
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdBottomMmioReservedForPrimaryRb
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdMmioAbove4GLimit
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdMmioSizePerRbForNonPciDevice
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdAbove4GMmioSizePerRbForNonPciDevice
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdSmee
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdFabric1TbRemap

[Depex]

