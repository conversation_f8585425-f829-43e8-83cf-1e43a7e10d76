/*****************************************************************************
 * Copyright (C) 2008-2024 Advanced Micro Devices, Inc. All rights reserved.
 *
*****************************************************************************
*/
/* $NoKeywords:$ */
/**
 * @file
 *
 * Config Fch SATA controller
 *
 * Init SATA features.
 *
 * @xrefitem bom "File Content Label" "Release Content"
 * @e project:     AGESA
 * @e sub-project: FCH
 * @e \$Revision: 309083 $   @e \$Date: 2014-12-09 09:28:24 -0800 (Tue, 09 Dec 2014) $
 *
 */
#include "FchPlatform.h"
#include "Filecode.h"
#define FILECODE FCH_KUNLUN_FCHKUNLUNCORE_KUNLUN_KLSATA_KLSATAENV_FILECODE

/**
 * FchInitEnvSata - Config SATA controller before PCI emulation
 *
 *
 *
 * @param[in] FchDataPtr Fch configuration structure pointer.
 *
 */
VOID
FchInitEnvSata (
  IN  VOID     *FchDataPtr
  )
{
  UINT32                 SataController;
  FCH_DATA_BLOCK         *LocalCfgPtr;
  AMD_CONFIG_PARAMS      *StdHeader;

  LocalCfgPtr = (FCH_DATA_BLOCK *) FchDataPtr;
  StdHeader = LocalCfgPtr->StdHeader;

  AGESA_TESTPOINT (TpFchInitEnvSata, NULL);

  for (SataController = 0; SataController < KUNLUN_SATA_CONTROLLER_NUM; SataController++) {
    if (LocalCfgPtr->Sata[SataController].SataEnable) {
      SataEnableWriteAccessKL (0, SataController);
      FchKLInitEnvProgramSata (0, SataController, FchDataPtr);

      /*
      Because of security consideration, x86 is forbidden to access nBIF straps.
      Move code to ABL.

      //
      // Call Sub-function for each Sata mode
      //
      if (( LocalCfgPtr->Sata[SataController].SataClass == SataAhci7804) || (LocalCfgPtr->Sata[SataController].SataClass == SataAhci )) {
        FchInitEnvSataAhciKL ( 0, SataController, FchDataPtr );
      }

      if ( LocalCfgPtr->Sata[SataController].SataClass == SataRaid) {
        FchInitEnvSataRaidKL ( 0, SataController, FchDataPtr );
      }
      */

      SataDisableWriteAccessKL (0, SataController);
      //FchKLSataAutoShutdownController (0, SataController, FchDataPtr);
    } else {
      continue;                                                //return if SATA controller is disabled.
    }
  }

  // check if Sata0 and Sata1 are both disabled
  if ((!LocalCfgPtr->Sata[0].SataEnable) && (!LocalCfgPtr->Sata[1].SataEnable)) {
    FchKLSataInitHideNbifDev1Pci (0, 0, FchDataPtr);
  }
  // check if Sata2 and Sata3 are both disabled
  if ((!LocalCfgPtr->Sata[2].SataEnable) && (!LocalCfgPtr->Sata[3].SataEnable)) {
    FchKLSataInitHideNbifDev1Pci (0, 3, FchDataPtr);
  }

  //
  // SATA IRQ Resource
  //
  SataSetIrqIntResource (LocalCfgPtr, StdHeader);
}




