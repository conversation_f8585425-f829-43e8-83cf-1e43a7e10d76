/*****************************************************************************
 *
 * Copyright (C) 2024 Advanced Micro Devices, Inc. All rights reserved.
 *
 *******************************************************************************
 */

#ifndef SOC_CORE_INFO2_ACCESS_LIB_H_
#define SOC_CORE_INFO2_ACCESS_LIB_H_

#include <Uefi.h>
#include <Guid/SocCoreInfo2.h>

#define SOC_CORE_INFO2_ACCESS_INVALID_INDEX (0xFFFFFFFF)

/**
  Return specific bit from a bit stream.

  @param BitStream              Point to a bit stream
  @param BitStreamSize          Size of bit stream in byte
  @param Index                  Specify bit index to read

  @retval 0                     This bit is 0
  @retval 1                     This bit is 1

**/
UINT8
SocCoreInfo2AccessGetBitFromStream (
  IN  UINT8 *BitStream,
  IN  UINT32 BitStreamSize,
  IN  UINT32 Index
);

/**
  Return SocCoreInfo2 CCD Enable bit for specific CCD

  @param SocCoreInfo2           Point to SOC_CORE_INFO2 Hob
  @param Ccd                    Physical CCD number

  @retval 0                     This CCD is fused
  @retval 1                     This CCD is enabled

**/
UINT8
SocCoreInfo2AccessGetCcdEnableBit (
    IN  SOC_CORE_INFO2 *SocCoreInfo2,
    IN  UINTN Ccd
);

/**
  Return stream like data index inside SocCoreInfo2 data

  @param SocCoreInfo2           Point to SOC_CORE_INFO2 Hob
  @param Ccd                    Physical CCD number
  @param Ccx                    Physical CCX number
  @param Core                   Physical Core number

  @retval SOC_CORE_INFO2_ACCESS_INVALID_INDEX
                                SocCoreInfo2 is invalid or CCD,CCX,Core number is invalid
  @retval                       Index number for access SocCoreInfo2 data

**/
UINT32
SocCoreInfo2AccessGetCoreDataIndex (
  IN  SOC_CORE_INFO2 *SocCoreInfo2,
  IN  UINTN Ccd,
  IN  UINTN Ccx,
  IN  UINTN Core
);

/**
  Return CoreDisBit of SocCoreInfo2 for specific core

  @param SocCoreInfo2           Point to SOC_CORE_INFO2 Hob
  @param Ccd                    Physical CCD number
  @param Ccx                    Physical CCX number
  @param Core                   Physical Core number

  @retval 0                     This core is fused
  @retval 1                     This core is enabled

**/
UINT8
SocCoreInfo2AccessGetCoreDisBit (
    IN  SOC_CORE_INFO2 *SocCoreInfo2,
    IN  UINTN Ccd,
    IN  UINTN Ccx,
    IN  UINTN Core
);

/**
  Return CORE_STATUS2 pointer of SocCoreInfo2 for specific core

  @param SocCoreInfo2           Point to SOC_CORE_INFO2 Hob
  @param Ccd                    Physical CCD number
  @param Ccx                    Physical CCX number
  @param Core                   Physical Core number

  @retval NULL                  SocCoreInfo2 is invalid or CCD,CCX,Core number is invalid
  @retval                       Pointer to specific core

**/
CORE_STATUS2
*SocCoreInfo2AccessGetCoreStatusForCore (
    IN  SOC_CORE_INFO2 *SocCoreInfo2,
    IN  UINTN Ccd,
    IN  UINTN Ccx,
    IN  UINTN Core
);

#endif

