/*****************************************************************************
 *
 * Copyright (C) 2008-2024 Advanced Micro Devices, Inc. All rights reserved.
 *
 *******************************************************************************
 */

/*----------------------------------------------------------------------------------------
 *                             M O D U L E S    U S E D
 *----------------------------------------------------------------------------------------
 */
#include <Filecode.h>
#include <DwI2cRegs.h>
#include <Library/BaseFabricTopologyLib.h>
#include <Addendum/Apcb/Inc/BRH/ApcbV3TokenUid.h>
#include <Library/IdsLib.h>
#include <Library/FchBaseLib.h>
#include "FchRegistersCommon.h"

/*----------------------------------------------------------------------------------------
 *                   D E F I N I T I O N S    A N D    M A C R O S
 *----------------------------------------------------------------------------------------
 */
#define FILECODE LIBRARY_FCHSOCLIB_BREITHORN_FCHBREITHORNLIB_FILECODE

#define I3C_SUPPORTED_SOCKETS_COUNT       (2u)
#define I3C_CONTROLLER_COUNT_PER_SOCKET   (4u)
#define I3C_CONTROLLER_COUNT_ALL_SOCKETS  (8u)
#define I3C_SDA_TX_HOLD_DEFAULT           (2u)

#define I2C_SUPPORTED_SOCKETS_COUNT       (2u)
#define I2C_CONTROLLER_COUNT_PER_SOCKET   (6u)
#define I2C_CONTROLLER_COUNT_ALL_SOCKETS  (12u)


// I2C SCL/SDA stuck low Timer
#define I2C_SCL_STUCK_AT_LOW_TIMEOUT            (0x20F60) // 900us
#define I2C_SDA_STUCK_AT_LOW_TIMEOUT            (0x20F60) // 900us
/*----------------------------------------------------------------------------------------
 *                  T Y P E D E F S     A N D     S T R U C T U R E S
 *----------------------------------------------------------------------------------------
 */


/*----------------------------------------------------------------------------------------
 *           P R O T O T Y P E S     O F     L O C A L     F U N C T I O N S
 *----------------------------------------------------------------------------------------
 */


/*----------------------------------------------------------------------------------------
 *                          E X P O R T E D    F U N C T I O N S
 *----------------------------------------------------------------------------------------
 */

UINT32 I3cBaseAddress[I3C_CONTROLLER_COUNT_ALL_SOCKETS] = {
  0xFEDD2000,
  0xFEDD3000,
  0xFEDD4000,
  0xFEDD6000,
  0x02DE2000,
  0x02DE3000,
  0x02DE4000,
  0x02DE5000
};

#define IsMmioAddress(address) ((address & 0xFED00000) == 0xFED00000)

typedef struct {
  UINT32          I2cBase;
  UINT32          I2cSCLAddress;
  UINT32          I2cSCLValue;
} I2C_INFO;

static I2C_INFO I2cBaseAddress[I2C_CONTROLLER_COUNT_ALL_SOCKETS] = {
  // 0 - Socket[0].i2c[0]
  {
    0xFEDC2000,
    ACPI_MMIO_BASE + IOMUX_BASE + FCH_IOMUX_REG91,   // IOMUX_GPIO145_IxC0_SCL
    1
  },

  // 1 - Socket[0].IxC[1]
  {
    0xFEDC3000,
    ACPI_MMIO_BASE + IOMUX_BASE + FCH_IOMUX_REG93,   // IOMUX_GPIO147_IxC1_SCL
    1
  },

  // 2 - Socket[0].IxC[2]
  {
    0xFEDC4000,
    ACPI_MMIO_BASE + IOMUX_BASE + FCH_IOMUX_REG95,   // IOMUX_GPIO149_IxC2_SCL
    1
  },

  // 3 - Socket[0].IxC[3]
  {
    0xFEDC5000,
    ACPI_MMIO_BASE + IOMUX_BASE + FCH_IOMUX_REG97,   // IOMUX_GPIO151_IxC3_SCL
    1
  },

  // 4 - Socket[0].IxC[4]
  {
    0xFEDC6000,
    ACPI_MMIO_BASE + IOMUX_BASE + FCH_IOMUX_REG0D,   // IOMUX_GPIO13_I2C4_SCL
    0
  },

  // 5 - Socket[0].IxC[5]
  {
    0xFEDCB000,
    ACPI_MMIO_BASE + IOMUX_BASE + FCH_IOMUX_REG13,   // IOMUX_GPIO19_I2C5_SCL
    0
  },

  // 6 - Socket[1].IxC[0]
  {
    0x02DC2000,
    ACPI_SMN_BASE + IOMUX_BASE + FCH_IOMUX_REG91,    // IOMUX_GPIO145_IxC0_SCL
    1
  },

  // 7 - Socket[1].IxC[1]
  {
    0x02DC3000,
    ACPI_SMN_BASE + IOMUX_BASE + FCH_IOMUX_REG93,    // IOMUX_GPIO147_IxC1_SCL
    1
  },

  // 8 - Socket[1].IxC[2]
  {
    0x02DD4000,
    ACPI_SMN_BASE + IOMUX_BASE + FCH_IOMUX_REG95,    // IOMUX_GPIO149_IxC2_SCL
    1
  },

  // 9 - Socket[1].IxC[3]
  {
    0x02DD5000,
    ACPI_SMN_BASE + IOMUX_BASE + FCH_IOMUX_REG97,    // IOMUX_GPIO151_IxC3_SCL
    1
  },

  // 10 - Socket[1].IxC[4]
  {
    0x02DD6000,
    ACPI_SMN_BASE + IOMUX_BASE + FCH_IOMUX_REG0D,    // IOMUX_GPIO13_I2C4_SCL
    0
  },

  // 11 - Socket[1].IxC[5]
  {
    0x02DDB000,
    ACPI_SMN_BASE + IOMUX_BASE + FCH_IOMUX_REG13,    // IOMUX_GPIO19_I2C5_SCL
    0
  }
};


UINT8
FchSocI3cGetNumOfSupportedSockets (
  IN   UINT8   NumOfSockets
  )
{
  return ( NumOfSockets > I3C_SUPPORTED_SOCKETS_COUNT ) ? I3C_SUPPORTED_SOCKETS_COUNT : NumOfSockets;
}

UINT32
FchSocI3cGetControllerCount (
  VOID
  )
{
  return I3C_CONTROLLER_COUNT_PER_SOCKET;
}

UINT32
FchSocI3cGetBaseAddress (
  IN       UINT32 SocketNumber,
  IN       UINT32 ControllerNumber
  )
{
  ASSERT (ControllerNumber < I3C_CONTROLLER_COUNT_PER_SOCKET);
  return I3cBaseAddress[SocketNumber*I3C_CONTROLLER_COUNT_PER_SOCKET + ControllerNumber];
}

UINT8
FchSocI3cIsControllerEnabled (
  IN       UINT32 SocketNumber,
  IN       UINT32 ControllerNumber
  )
{
  ASSERT (ControllerNumber < I3C_CONTROLLER_COUNT_PER_SOCKET);
  UINT8 Mode = I2C_ENABLE;
  switch (ControllerNumber) {
  case 0:
    Mode = PcdGet8 (PcdAmdFchI2cI3c0);
    break;
  case 1:
    Mode = PcdGet8 (PcdAmdFchI2cI3c1);
    break;
  case 2:
    Mode = PcdGet8 (PcdAmdFchI2cI3c2);
    break;
  case 3:
    Mode = PcdGet8 (PcdAmdFchI2cI3c3);
    break;
  default:
    ASSERT (FALSE);
  }
  return ( Mode == I3C_ENABLE ) ? 0x01 : 0x00;
}

UINT8
FchSocI3cGetI2cCompatibility (
  IN       UINT32 SocketNumber,
  IN       UINT32 ControllerNumber
  )
{
  ASSERT (ControllerNumber < I3C_CONTROLLER_COUNT_PER_SOCKET);
  //UINT8 Mode = I3C_MODE;

  return (UINT8)I3C_MODE;
}

UINT8
FchSocI3cGetSpeed (
  IN       UINT32 SocketNumber,
  IN       UINT32 ControllerNumber
  )
{
  ASSERT (ControllerNumber < I3C_CONTROLLER_COUNT_PER_SOCKET);
  UINT8 Speed = PcdGet8 (PcdFchI3cSpeed);

  if (Speed != 0x2) {
    Speed = 0x2;
  }

  return Speed;
}

UINT8
FchSocI3cGetPushPullHighCount (
  IN       UINT32 SocketNumber,
  IN       UINT32 ControllerNumber
  )
{
  return PcdGet8 (PcdFchI3cPPHcnt);
}

UINT8
FchSocI3cGetSdaHoldTime (
  IN       UINT32 SocketNumber,
  IN       UINT32 ControllerNumber
  )
{
  UINT8 I3cSdaHoldTime         = I3C_SDA_TX_HOLD_DEFAULT;
  UINT8 I3cSdaHoldTimeOverride = 0x00;

  ASSERT (ControllerNumber < I3C_CONTROLLER_COUNT_PER_SOCKET);

  I3cSdaHoldTimeOverride = PcdGet8 (PcdAmdFchI3cSdaHoldOverride);

  if (I3cSdaHoldTimeOverride == 0x00 ) {
     return I3cSdaHoldTime;
  }

  switch (ControllerNumber) {
  case 0:
    I3cSdaHoldTime = PcdGet8 (PcdAmdFchI3c0SdaHold);
    break;
  case 1:
    I3cSdaHoldTime = PcdGet8 (PcdAmdFchI3c1SdaHold);
    break;
  case 2:
    I3cSdaHoldTime = PcdGet8 (PcdAmdFchI3c2SdaHold);
    break;
  case 3:
    I3cSdaHoldTime = PcdGet8 (PcdAmdFchI3c3SdaHold);
    break;
  default:
    // We should not get here!
    ASSERT (FALSE);
    break;
  }
  return I3cSdaHoldTime;
}

UINT8
FchSocI2cGetControllerCount (
  VOID
  )
{
  return I2C_CONTROLLER_COUNT_PER_SOCKET;
}

UINT8
FchSocI2cGetNumOfSupportedSockets (
  VOID
  )
{
  return I2C_SUPPORTED_SOCKETS_COUNT;
}

/**
  This function returns I2C mode enable or disabled.

  @param Index - controller number

  @retval UINT8 - I2C Mode
*/
UINT8
FchSocI2cIsControllerEnabled (
  IN  UINT32    Index
  )
{
  UINT32                Socket1BusNum;
  UINT8                 Value8;
  volatile UINT32       *pAddr;
  ROOT_BRIDGE_LOCATION  FchRb;

  ASSERT (Index < I2C_CONTROLLER_COUNT_ALL_SOCKETS);

  Value8               = 0xFF;
  pAddr                 = (volatile UINT32*)(UINTN)I2cBaseAddress[Index].I2cSCLAddress;
  Socket1BusNum         = 0;

  if (IsMmioAddress (I2cBaseAddress[Index].I2cSCLAddress)) {
    Value8 = (UINT8)(*pAddr);
  } else {
    FabricTopologyGetSystemComponentRootBridgeLocation (PrimaryFch, &FchRb);
    Socket1BusNum = (UINT32) FabricTopologyGetHostBridgeBusBase (1, FchRb.Die, FchRb.Index);
    FchSmnRead8 (Socket1BusNum, I2cBaseAddress[Index].I2cSCLAddress, &Value8, NULL);
  }

  IDS_HDT_CONSOLE (
    FCH_TRACE,
    "%a i2c ch %d bus 0x%x SCL address 0x%x SCL exp value 0x%x read value 0x%x\n",
    __FUNCTION__,
    Index,
    Socket1BusNum,
    I2cBaseAddress[Index].I2cSCLAddress,
    I2cBaseAddress[Index].I2cSCLValue,
    Value8
    );

  if (Value8 == 0xFF) {
    return FALSE;
  } else {
    return ( (Value8 & 0x3) == (UINT8)I2cBaseAddress[Index].I2cSCLValue ) ? TRUE : FALSE;
  }
}

/**
  This function returns SDA HOLD TIME per channel

  @param Index - index for the channel

  @retval UINT32 - SDA HOLD TIME value
*/
UINT32
FchSocI2cSetSdaHoldTime (
  IN  UINT32    Index
  )
{
  UINT32  I2cSdaHoldTime;

  switch (Index) {
  case 0:
    I2cSdaHoldTime = PcdGet32 (PcdAmdFchI2c0SdaHold);
    if (PcdGet8(PcdAmdFchI2cSdaHoldOverride)){
      I2cSdaHoldTime = (UINT32)( PcdGet16(PcdAmdFchI2c0SdaTxHold) | ( I2C_SDA_RX_HOLD_SHIFT << PcdGet8(PcdAmdFchI2c0SdaRxHold) ) );
    }
    break;
  case 1:
    I2cSdaHoldTime = PcdGet32 (PcdAmdFchI2c1SdaHold);
    if (PcdGet8(PcdAmdFchI2cSdaHoldOverride)){
      I2cSdaHoldTime = (UINT32)( PcdGet16(PcdAmdFchI2c1SdaTxHold) | ( I2C_SDA_RX_HOLD_SHIFT << PcdGet8(PcdAmdFchI2c1SdaRxHold) ) );
    }
    break;
  case 2:
    I2cSdaHoldTime = PcdGet32 (PcdAmdFchI2c2SdaHold);
    if (PcdGet8(PcdAmdFchI2cSdaHoldOverride)){
      I2cSdaHoldTime = (UINT32)( PcdGet16(PcdAmdFchI2c2SdaTxHold) | ( I2C_SDA_RX_HOLD_SHIFT << PcdGet8(PcdAmdFchI2c2SdaRxHold) ) );
    }
    break;
  case 3:
    I2cSdaHoldTime = PcdGet32 (PcdAmdFchI2c3SdaHold);
    if (PcdGet8(PcdAmdFchI2cSdaHoldOverride)){
      I2cSdaHoldTime = (UINT32)( PcdGet16(PcdAmdFchI2c3SdaTxHold) | ( I2C_SDA_RX_HOLD_SHIFT << PcdGet8(PcdAmdFchI2c3SdaRxHold) ) );
    }
    break;
  case 4:
    I2cSdaHoldTime = PcdGet32 (PcdAmdFchI2c4SdaHold);
    if (PcdGet8(PcdAmdFchI2cSdaHoldOverride)){
      I2cSdaHoldTime = (UINT32)( PcdGet16(PcdAmdFchI2c4SdaTxHold) | ( I2C_SDA_RX_HOLD_SHIFT << PcdGet8(PcdAmdFchI2c4SdaRxHold) ) );
    }
    break;
  case 5:
    I2cSdaHoldTime = PcdGet32 (PcdAmdFchI2c5SdaHold);
    if (PcdGet8(PcdAmdFchI2cSdaHoldOverride)){
      I2cSdaHoldTime = (UINT32)( PcdGet16(PcdAmdFchI2c5SdaTxHold) | ( I2C_SDA_RX_HOLD_SHIFT << PcdGet8(PcdAmdFchI2c5SdaRxHold) ) );
    }
    break;
  default:
    // it should not be here!
    I2cSdaHoldTime = 0xFFFFFFFF;
    break;
  }
  return I2cSdaHoldTime;
}

/**
  This function returns Bus Frequency per channel

  @param Index - index for the channel

  @retval UINT32 - Bus Frequency value
*/
UINT32
FchSocI2cSetBusFrequency (
  IN  UINT32    Index
  )
{
  UINT32  I2cBusFrequency;

  switch (Index) {
  case 0:
    I2cBusFrequency = PcdGet32 (PcdAmdFchI2c0BusFrequency);
    break;
  case 1:
    I2cBusFrequency = PcdGet32 (PcdAmdFchI2c1BusFrequency);
    break;
  case 2:
    I2cBusFrequency = PcdGet32 (PcdAmdFchI2c2BusFrequency);
    break;
  case 3:
    I2cBusFrequency = PcdGet32 (PcdAmdFchI2c3BusFrequency);
    break;
  case 4:
    I2cBusFrequency = PcdGet32 (PcdAmdFchI2c4BusFrequency);
    break;
  case 5:
    I2cBusFrequency = PcdGet32 (PcdAmdFchI2c5BusFrequency);
    break;
  default:
    // it should not be here!
    I2cBusFrequency = 0xFFFFFFFF;
    break;
  }
  return I2cBusFrequency;
}

/**
  This function returns Base address per channel

  @param Index - index for the channel

  @retval UINT32 - Base address
*/
UINT32
FchSocI2cGetBaseAddress (
  IN  UINT32     Index
  )
{
  return I2cBaseAddress[Index].I2cBase;
}



/**
  This function returns true if this I2C Controller Support Bus clear feature

  @param Index - The index of I2C Controller per socket.

  @retval TRUE - This I2C Controller Support Bus clear feature
*/
BOOLEAN
FchSocI2cIsSupportBusClearFeature (
  IN  UINT32              Index
  )
{

 #if FixedPcdGet8(PcdAmdMemMaxDimmPerChannelV2) == 1
  //1DPC
  if (Index == 0 || Index == 1 ) {// I2C 0/1
    return TRUE;
  }
 #else
  //2DPC
   if ( Index <= 3 ) {// I2C 0/1/2/4
    return TRUE;
  }
  #endif

  return FALSE;
}

/**
  This function returns SCL stuck Low time  per channel

  @param Index - index for the channel

  @retval UINT32 - SCL stuck Low time
*/
UINT32
FchSocI2cSetSCLStuckTime (
  IN  UINT32    Index
  )
{
  //current only use one setting for all channel
  return I2C_SCL_STUCK_AT_LOW_TIMEOUT;
}

/**
  This function returns SDA stuck Low time  per channel

  @param Index - index for the channel

  @retval UINT32 - SCL stuck Low time
*/
UINT32
FchSocI2cSetSDAStuckTime (
  IN  UINT32    Index
  )
{
  //current only use one setting for all channel
  return I2C_SDA_STUCK_AT_LOW_TIMEOUT;
}
