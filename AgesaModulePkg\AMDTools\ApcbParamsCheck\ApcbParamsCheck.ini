[project]
rome = _rome
raven = _raven
summit = _summit
castlepeak = _castlepeak

[path_common]
path = ..\..\..\AgesaPkg\Addendum\Apcb\

[path_rome]
path_1 = RomeSp3Rdimm\ApcbDataDefaultRecovery\RomeCommon
path_2 = CastlePeakSp3r3Udimm\ApcbDataDefaultRecovery\CRB_WhiteHaven
path_3 = CastlePeakSp3r3UdimmAlt1\ApcbDataDefaultRecovery\CRB_Sharkstooth
path_4 = CastlePeakSp3Rdimm\ApcbDataDefaultRecovery\CRB_Cloudripper

[file_v2_rome]
#file_1 = ApcbData_SSP_GID_0x1701_Type_ExtParams.c
#file_2 = ApcbData_SSP_GID_0x1702_Type_ExtParams.c
#file_3 = ApcbData_SSP_GID_0x1703_Type_ExtParams.c
#file_4 = ApcbData_SSP_GID_0x1704_Type_ExtParams.c
#file_5 = ApcbData_SSP_GID_0x1705_Type_ExtParams.c
#file_6 = ApcbData_SSP_GID_0x1706_Type_ExtParams.c
#file_7 = ApcbData_SSP_GID_0x1707_Type_CbsDefParams.c
#file_8 = ApcbData_SSP_GID_0x1707_Type_CbsParams.c
#file_9 = ApcbData_SSP_GID_0x1707_Type_IntDefParams.c
#file_10 = ApcbData_SSP_GID_0x1707_Type_IntParams.c

[file_v3_rome]
file_1 = ApcbData_SSP_GID_0x3000_Type_Token1Byte.c
#file_2 = ApcbData_SSP_GID_0x3000_Type_Token1ByteDebug.c
file_3 = ApcbData_SSP_GID_0x3000_Type_Token2Bytes.c
#file_4 = ApcbData_SSP_GID_0x3000_Type_Token2BytesDebug.c
file_5 = ApcbData_SSP_GID_0x3000_Type_Token4Bytes.c
#file_6 = ApcbData_SSP_GID_0x3000_Type_Token4BytesDebug.c
file_7 = ApcbData_SSP_GID_0x3000_Type_TokenBoolean.c
#file_8 = ApcbData_SSP_GID_0x3000_Type_TokenBooleanDebug.c

[path_raven]
path_1 = RavenAm4Sodimm\ApcbData\
path_2 = RavenAm4UDimm\ApcbData\
path_3 = RavenFp5Dramdown\ApcbData\
path_4 = RavenFp5Sodimm\ApcbData\
path_5 = RavenFp5Udimm\ApcbData\

[file_v2_raven]
file_1 = ApcbData_RV_GID_0x1703_Type_ExtDefParams.c
file_2 = ApcbData_RV_GID_0x1703_Type_ExtParams.c
file_3 = ApcbData_RV_GID_0x1704_Type_ExtDefParams.c
file_4 = ApcbData_RV_GID_0x1704_Type_ExtParams.c
file_5 = ApcbData_RV_GID_0x1706_Type_ExtDefParams.c
file_6 = ApcbData_RV_GID_0x1706_Type_ExtParams.c
file_7 = ApcbData_RV_GID_0x1707_Type_IntDefParams.c
file_8 = ApcbData_RV_GID_0x1707_Type_IntParams.c
file_9 = ..\Internal\ApcbData\ApcbData_RV_GID_0x1703_Type_ExtDefParams.c
file_10 = ..\Internal\ApcbData\ApcbData_RV_GID_0x1703_Type_ExtParams.c
file_11 = ..\Internal\ApcbData\ApcbData_RV_GID_0x1704_Type_ExtDefParams.c
file_12 = ..\Internal\ApcbData\ApcbData_RV_GID_0x1704_Type_ExtParams.c
file_13 = ..\Internal\ApcbData\ApcbData_RV_GID_0x1706_Type_ExtDefParams.c
file_14 = ..\Internal\ApcbData\ApcbData_RV_GID_0x1706_Type_ExtParams.c
file_15 = ..\Internal\ApcbData\ApcbData_RV_GID_0x1707_Type_IntDefParams.c
file_16 = ..\Internal\ApcbData\ApcbData_RV_GID_0x1707_Type_IntParams.c

[file_v3_raven]

[path_summit]
path_1 = SummitAm4Sodimm\ApcbData\
path_2 = SummitAm4Udimm\ApcbData\

[file_v2_summit]
file_1 = ApcbData_ZP_GID_0x1702_Type_ExtDefParams.c
file_2 = ApcbData_ZP_GID_0x1702_Type_ExtParams.c
file_3 = ApcbData_ZP_GID_0x1703_Type_ExtDefParams.c
file_4 = ApcbData_ZP_GID_0x1703_Type_ExtParams.c
file_5 = ApcbData_ZP_GID_0x1704_Type_ExtDefParams.c
file_6 = ApcbData_ZP_GID_0x1704_Type_ExtParams.c
file_7 = ApcbData_ZP_GID_0x1706_Type_ExtDefParams.c
file_8 = ApcbData_ZP_GID_0x1706_Type_ExtParams.c
file_9 = ApcbData_ZP_GID_0x1707_Type_IntDefParams.c
file_10 = ApcbData_ZP_GID_0x1707_Type_IntParams.c
file_11 = ..\Internal\ApcbData\ApcbData_ZP_GID_0x1702_Type_ExtDefParams.c
file_12 = ..\Internal\ApcbData\ApcbData_ZP_GID_0x1702_Type_ExtParams.c
file_13 = ..\Internal\ApcbData\ApcbData_ZP_GID_0x1703_Type_ExtDefParams.c
file_14 = ..\Internal\ApcbData\ApcbData_ZP_GID_0x1703_Type_ExtParams.c
file_15 = ..\Internal\ApcbData\ApcbData_ZP_GID_0x1704_Type_ExtDefParams.c
file_16 = ..\Internal\ApcbData\ApcbData_ZP_GID_0x1704_Type_ExtParams.c
file_17 = ..\Internal\ApcbData\ApcbData_ZP_GID_0x1706_Type_ExtDefParams.c
file_18 = ..\Internal\ApcbData\ApcbData_ZP_GID_0x1706_Type_ExtParams.c
file_19 = ..\Internal\ApcbData\ApcbData_ZP_GID_0x1707_Type_IntDefParams.c
file_20 = ..\Internal\ApcbData\ApcbData_ZP_GID_0x1707_Type_IntParams.c

[file_v3_summit]

[path_castlepeak]
path_1 = CastlePeakSp3r3Udimm\ApcbDataDefaultRecovery\CRB_WhiteHaven
path_2 = CastlePeakSp3r3UdimmAlt1\ApcbDataDefaultRecovery\CRB_Sharkstooth
path_3 = CastlePeakSp3Rdimm\ApcbDataDefaultRecovery\CRB_Cloudripper
path_4 = RomeSp3Rdimm\ApcbDataDefaultRecovery\RomeCommon

[file_v2_castlepeak]
#file_1 = ApcbData_SSP_GID_0x1701_Type_ExtParams.c
#file_2 = ApcbData_SSP_GID_0x1702_Type_ExtParams.c
#file_3 = ApcbData_SSP_GID_0x1703_Type_ExtParams.c
#file_4 = ApcbData_SSP_GID_0x1704_Type_ExtParams.c
#file_5 = ApcbData_SSP_GID_0x1705_Type_ExtParams.c
#file_6 = ApcbData_SSP_GID_0x1706_Type_ExtParams.c
#file_7 = ApcbData_SSP_GID_0x1707_Type_CbsDefParams.c
#file_8 = ApcbData_SSP_GID_0x1707_Type_CbsParams.c
#file_9 = ApcbData_SSP_GID_0x1707_Type_IntDefParams.c
#file_10 = ApcbData_SSP_GID_0x1707_Type_IntParams.c

[file_v3_castlepeak]
file_1 = ApcbData_SSP_GID_0x3000_Type_Token1Byte.c
#file_2 = ApcbData_SSP_GID_0x3000_Type_Token1ByteDebug.c
file_3 = ApcbData_SSP_GID_0x3000_Type_Token2Bytes.c
#file_4 = ApcbData_SSP_GID_0x3000_Type_Token2BytesDebug.c
file_5 = ApcbData_SSP_GID_0x3000_Type_Token4Bytes.c
#file_6 = ApcbData_SSP_GID_0x3000_Type_Token4BytesDebug.c
file_7 = ApcbData_SSP_GID_0x3000_Type_TokenBoolean.c
#file_8 = ApcbData_SSP_GID_0x3000_Type_TokenBooleanDebug.c