/*****************************************************************************
 * Copyright (C) 2008-2024 Advanced Micro Devices, Inc. All rights reserved.
 *
*****************************************************************************
*/
/* $NoKeywords:$ */
/**
 * @file
 *
 * Fabric watchdog timer library
 *
 * @xrefitem bom "File Content Label" "Release Content"
 * @e project:      AGESA
 * @e sub-project:  Fabric
 * @e \$Revision$   @e \$Date$
 *
 */


/*----------------------------------------------------------------------------------------
 *                             M O D U L E S    U S E D
 *----------------------------------------------------------------------------------------
 */
#include <AMD.h>
#include <FabricRegistersDf4.h>
#include <Library/IdsLib.h>
#include <Library/FabricWdtLib.h>
#include <Library/FabricRegisterAccLib.h>
#include <Library/BaseLib.h>
#include <Filecode.h>

/*----------------------------------------------------------------------------------------
 *                   D E F I N I T I O N S    A N D    M A C R O S
 *----------------------------------------------------------------------------------------
 */
#define FILECODE LIBRARY_FABRICWDTDF4LIB_FABRICWDTDF4LIB_FILECODE

/*----------------------------------------------------------------------------------------
 *                           G L O B A L   V A R I A B L E S
 *----------------------------------------------------------------------------------------
 */
UINT32 Df4WdtCntSelDecode [] =
{
  31,
  127,
  255,
  511,
  1023,
  2047,
  4095,
  16383
};
#define NumberOfDfWdtCntSel (sizeof (Df4WdtCntSelDecode) / sizeof (Df4WdtCntSelDecode[0]))

UINT64 Df4WdtBaseSelDecode [] =
{
  1310000,
  1280,
  10,
  0
};
#define NumberOfDfWdtBaseSel (sizeof (Df4WdtBaseSelDecode) / sizeof (Df4WdtBaseSelDecode[0]))

/*----------------------------------------------------------------------------------------
 *                  T Y P E D E F S     A N D     S T R U C T U R E S
 *----------------------------------------------------------------------------------------
 */

/*----------------------------------------------------------------------------------------
 *                          E X P O R T E D    F U N C T I O N S
 *----------------------------------------------------------------------------------------
 */

/* -----------------------------------------------------------------------------*/
/**
 *
 *  FabricGetCcmWdtInfo
 *
 *  @param[in, out] DfCcmTimeout        DF CCM WDT time in ns
 *
 *  @retval TRUE  WDT is enabled
 *  @retval FALSE WDT is disabled
 *
 */
BOOLEAN
FabricGetCcmWdtInfo (
  IN       UINT64 *DfCcmTimeout
  )
{
  BOOLEAN                  DfWdtEn;
  DF_GLOBAL_CTRL_REGISTER  DfGlblCtrl;

  DfGlblCtrl.Value = FabricRegisterAccRead (0, 0, DFGLOBALCTRL_FUNC, DFGLOBALCTRL_REG, FABRIC_REG_ACC_BC);
  ASSERT (DfGlblCtrl.Field.WDTBaseSel < NumberOfDfWdtBaseSel);
  ASSERT (DfGlblCtrl.Field.CCMWDTCntSel < NumberOfDfWdtCntSel);
  if (DfGlblCtrl.Field.WDTBaseSel < 3) {
    DfWdtEn = TRUE;
    *DfCcmTimeout = (UINT64) MultU64x32(Df4WdtBaseSelDecode[DfGlblCtrl.Field.WDTBaseSel], Df4WdtCntSelDecode[DfGlblCtrl.Field.CCMWDTCntSel]);
  } else {
    DfWdtEn = FALSE;
    *DfCcmTimeout = 0;
  }
  return DfWdtEn;
}



