/*
 ******************************************************************************
 *
 * Copyright (C) 2008-2024 Advanced Micro Devices, Inc. All rights reserved.
 *
 *******************************************************************************
 */
/**
 * @file
 *
 * AMD CCX Zen5 API, and related functions.
 *
 *
 * @xrefitem bom "File Content Label" "Release Content"
 * @e project:      AGESA
 * @e sub-project:  Ccx
 * @e \$Revision$   @e \$Date$
 *
 */

#pragma once

#include <Library/CcxHaltLib.h>
#include <Library/CcxResetTablesLibV2.h>
#include <Ppi/NbioSmuServicesPpi.h>

#pragma pack (push, 1)

/*---------------------------------------------------------------------------------------
 *          M I X E D   (Definitions And Macros / Typedefs, Structures, Enums)
 *---------------------------------------------------------------------------------------
 */


/*---------------------------------------------------------------------------------------
 *                 D E F I N I T I O N S     A N D     M A C R O S
 *---------------------------------------------------------------------------------------
 */

// layout of ApStartupVector
// +---------------------------+
// | AllowToLaunchNextThreadLocation
// |   4 bytes (UINT32)        |
// +---------------------------+ +-> ApStartupVector + A
// | BSP GDTR                  |
// |   6 bytes (UINT16 + UINTN)|
// +-------------------+-------+ +-> ApStartupVector + 4
// | Near Jump to ApStartupCode|
// |   4 bytes                 |
// +---------------------------+ +-> ApStartupVector (got from BIOS directory)
// | Page Table for 64-bit     +
// +---------------------------+ - > ApStartupVector - 8
// |                           |
// |  GDT                      |
// +---------------------------+
// |                           |
// |  ApMtrrSyncList[]         |
// |                           |
// |                           |
// +---------------------------+
// |                           |
// |  Code for ApStartupCode   |
// |                           |
// +---------------------------+

#define CR3_SAVE_SIZE 8

#define STARTUP_VECTOR_SIZE 16  // Starts at ApStartup Vector
typedef struct {
  UINTN  TopOfApRegion;
  UINTN  PageTable;            // Should always be at ApStartup Vector - 16
  UINTN  GdtTable;
  UINTN  ApMttrSyncList;
  UINTN  ApStartupCodeBase;
} AP_STARTUP_MEMORY_MAP;

VOID ApStartupFixups (AP_STARTUP_MEMORY_MAP *ApStartupMemoryMap, VOID *ApLaunchGlobalData);


// If below size is changed, please update the same definition in ApAsm.nasm
#define  AP_STACK_SIZE          0x400

#define  ZEN5_CAC_WEIGHT_NUM  23

/*---------------------------------------------------------------------------------------
 *               T Y P E D E F S,   S T R U C T U R E S,    E N U M S
 *---------------------------------------------------------------------------------------
 */

typedef struct {
  UINT16    Limit;
  UINT32    Base;   //Before switching to 64-bit mode, 32-bit base is used by lgdt.
} GDT32_DESCRIPTOR;


/// AP MSR sync up
typedef struct {
  IN  UINT32 MsrAddr;     ///< MSR address
  IN  UINT64 MsrData;     ///< MSR Settings
  IN  UINT64 MsrMask;     ///< MSR mask
} AP_MSR_SYNC;

/// Core Watchdog
typedef struct {
BOOLEAN              CpuWdtEn;
UINT16               CpuWdtTimeOut;
UINT8                CpuWdTmrCfgSeverity;
UINT8                PcdAmdRedirectForReturnDis;
} CORE_WATCHDOG;

typedef struct {
  UINT32                     BspMsrLocation;                   ///< Do NOT change the offset of this variable
  VOLATILE UINT32            ApSyncCount;                      ///< Do NOT change the offset of this variable
  UINT32                     AllowToLaunchNextThreadLocation;  ///< Do NOT change the offset of this variable
  UINT32                     ApStackBasePtr;                   ///< Do NOT change the offset of this variable
  IA32_DESCRIPTOR            ApGdtDescriptor;                  ///< Do NOT change the offset of this variable
  UINT8                      SleepType;
  UINT32                     SizeOfApMtrr;
  VOLATILE AP_MTRR_SETTINGS  *ApMtrrSyncList;
  VOLATILE AP_MSR_SYNC       *ApMsrSyncList;
  UINT64                     BspPatchLevel;
  UINT64                     UcodePatchAddr;
  ENTRY_CRITERIA             ResetTableCriteria;
  CORE_WATCHDOG              CpuWdt;
  BOOLEAN                    SetCacWeightsEnable;
  UINT64                     CacWeights[ZEN5_CAC_WEIGHT_NUM];
  BOOLEAN                    TransparentErrorLoggingEnable;
} AMD_CCX_AP_LAUNCH_GLOBAL_DATA;

///  The possible PAUSE instruction mode settings.
typedef enum  {
  CPU_PAUSECNTSEL_1_0_16CYCLES,
  CPU_PAUSECNTSEL_1_0_32CYCLES,
  CPU_PAUSECNTSEL_1_0_64CYCLES,
  CPU_PAUSECNTSEL_1_0_128CYCLES,
  CPU_PAUSECNTSEL_1_0_AUTO = 0xFF             ///< Use the recommended setting for the processor.
} CPU_PAUSECNTSEL_1_0;

/*---------------------------------------------------------------------------------------
 *                        F U N C T I O N    P R O T O T Y P E
 *---------------------------------------------------------------------------------------
 */
VOID ApAsmCode (
  VOID
 );

VOID
RegSettingBeforeLaunchingNextThread (
  );

VOID
ApEntryPointInC (
  IN       VOLATILE AMD_CCX_AP_LAUNCH_GLOBAL_DATA *ApLaunchGlobalData
  );

VOID
SetupApStartupRegion (
  IN       VOLATILE AMD_CCX_AP_LAUNCH_GLOBAL_DATA *ApLaunchGlobalData,
  OUT      VOID    **ApStartupBuffer,
  OUT      VOID    **MemoryContentCopy,
  OUT      UINTN   *MemoryContentCopySize
  );

VOID
RestoreResetVector (
  IN       VOLATILE AMD_CCX_AP_LAUNCH_GLOBAL_DATA *ApLaunchGlobalData,
  IN       UINT32   TotalCoresLaunched,
  IN       VOID     *ApStartupBuffer,
  IN       VOID     *MemoryContentCopy,
  IN       UINTN    MemoryContentCopySize
  );

VOID
CcxZen5MicrocodeInit (
  IN OUT   UINT64             *UcodePatchAddr,
  IN       AMD_CONFIG_PARAMS  *StdHeader
  );

VOID
CcxZen5EnableWdt (
  IN       CORE_WATCHDOG  *CpuWdt
  );

VOID
CcxZen5EnableSmeeHmkee (
  );

VOID
CcxZen5Prefetcher (
  );

VOID
CcxZen5SetMiscMsrs (
  );

VOID
CcxZen5SyncMiscMsrs (
  IN       VOLATILE AMD_CCX_AP_LAUNCH_GLOBAL_DATA *ApLaunchGlobalData
  );

EFI_STATUS
CcxZen5GetCacWeights (
  IN       PEI_AMD_NBIO_SMU_SERVICES_PPI       *NbioSmuServices,
  IN       UINT64                              *CacWeights
  );

VOID
CcxZen5SetCacWeights (
  IN       UINT64                              *CacWeights
  );

AGESA_STATUS
CcxZen5InitializeC6 (
  IN       AMD_CONFIG_PARAMS         *StdHeader
  );

AGESA_STATUS
CcxZen5InitializeCpb (
  IN       AMD_CONFIG_PARAMS         *StdHeader
  );

///  The possible software prefetch mode settings.
typedef enum  {
  SOFTWARE_PREFETCHES_AUTO,                     ///< Use the recommended setting for the processor. In most cases, the recommended setting is enabled.
  DISABLE_SOFTWARE_PREFETCHES,                  ///< Disable software prefetches (convert software prefetch instructions to NOP).
  MAX_SOFTWARE_PREFETCH_MODE                    ///< Not a software prefetch mode, use for limit checking.
} SOFTWARE_PREFETCH_MODE;

/// Advanced performance tunings, prefetchers.
/// These settings provide for performance tuning to optimize for specific workloads.
typedef struct {
  SOFTWARE_PREFETCH_MODE  SoftwarePrefetchMode; ///< This value provides for advanced performance tuning by controlling the software prefetch instructions.
} CCX_PREFETCH_MODE;

AGESA_STATUS
CcxZen5InitializePrefetchMode (
  IN       AMD_CONFIG_PARAMS                *StdHeader
  );

VOID
CcxZen5SyncMiscMsrs (
  IN       VOLATILE AMD_CCX_AP_LAUNCH_GLOBAL_DATA *ApLaunchGlobalData
  );

EFI_STATUS
EFIAPI
CcxInitWithMpServices (
  IN EFI_PEI_SERVICES           **PeiServices,
  IN EFI_PEI_NOTIFY_DESCRIPTOR  *NotifyDescriptor,
  IN VOID                       *Ppi
  );

#pragma pack (pop)

