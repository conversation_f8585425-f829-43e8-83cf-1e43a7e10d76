/*****************************************************************************
 * Copyright (C) 2022-2024 Advanced Micro Devices, Inc. All rights reserved.
 *
******************************************************************************
*/
/*----------------------------------------------------------------------------------------
 *                             M O D U L E S    U S E D
 *----------------------------------------------------------------------------------------
 */

#include <Library/DebugLib.h>
#include <Library/DashAsfBaseLib.h>
#include "Filecode.h"

#define FILECODE LIBRARY_DASHASFBASELIB_DASHASFBASELIB_FILECODE

/**
 * @brief Dash function : DashAsfMasterEnable
 *
 * @details Set Asf Smbus Master or Slave Mode
 *
 * @param DashAsfSmMasterEn - 1: Enable/ 0: disable
 *
 * @return Value The value after transferring
 */

VOID
DashAsfMasterEnable(
IN BOOLEAN DashAsfSmMasterEn)
{
  UINT8 Value;

  Value = 0;

  DEBUG ((DEBUG_INFO, "%a, Enter\n", __FUNCTION__));

  if (DashAsfSmMasterEn) {
    Value = *(volatile UINT8*)(UINTN)(ACPI_MMIO_BASE + PMIO_BASE + FCH_PMIOA_REG00 + 0x02);
    Value |= 0x01;
    *(volatile UINT8*)(UINTN)(ACPI_MMIO_BASE + PMIO_BASE + FCH_PMIOA_REG00 + 0x02) =  Value;
  } else {
    Value = *(volatile UINT8*)(UINTN)(ACPI_MMIO_BASE + PMIO_BASE + FCH_PMIOA_REG00 + 0x02);
    Value &= 0xFE;
    *(volatile UINT8*)(UINTN)(ACPI_MMIO_BASE + PMIO_BASE + FCH_PMIOA_REG00 + 0x02) = Value;
  }
}


