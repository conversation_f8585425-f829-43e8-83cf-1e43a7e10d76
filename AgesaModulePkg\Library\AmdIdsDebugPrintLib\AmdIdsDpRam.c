/*****************************************************************************
 * Copyright (C) 2008-2024 Advanced Micro Devices, Inc. All rights reserved.
 *
*****************************************************************************
*/
/**
 * @file
 *
 * AMD Integrated Debug Debug_library Routines
 *
 * Contains all functions related to HDTOUT
 *
 * @xrefitem bom "File Content Label" "Release Content"
 * @e project:      AGESA
 * @e sub-project:  IDS
 * @e \$Revision: 309090 $   @e \$Date: 2014-12-10 02:28:05 +0800 (Wed, 10 Dec 2014) $
 */

 /*----------------------------------------------------------------------------------------
 *                             M O D U L E S    U S E D
 *----------------------------------------------------------------------------------------
 */
#include "AGESA.h"
#include "Library/IdsLib.h"
#include "AMD.h"
#include "IdsDpRam.h"
#include "Filecode.h"
#include <Library/AmdBaseLib.h>
#include <AmdIdsDebugPrintLocal.h>
#include <Library/AmdEmulationAutoDetect.h>
#include <Library/BaseLib.h>

#define FILECODE LIBRARY_AMDIDSDEBUGPRINTLIB_AMDIDSDPRAM_FILECODE

/**
 * @brief Determine whether IDS console is enabled.
 *
 *  @return BOOLEAN
 * 
 *  @retval TRUE - Alway return true
 *
 **/
BOOLEAN
AmdIdsDpRamSupport (
  VOID
  )
{
  if (AmdIdsEmulationAutoDetect()) {
    // Return FALSE if the system is emulation.
    return FALSE;
  }

  if (PcdGetBool (PcdAmdIdsDebugPrintRamDebugEnable)) {
    return TRUE;
  }

  return FALSE;
}


/**
 * @brief Read Debug Print Memory
 *
 * @param[in] AccessWidth   Access width
 * @param[in] MemAddress    Memory address
 * @param[out] Value         Pointer to data
 *
 */
VOID
STATIC
AmdIdsDpRamRead (
  IN       ACCESS_WIDTH AccessWidth,
  IN       UINT64 MemAddress,
     OUT   VOID *Value
  )
{
  LibAmdMemRead (AccessWidth, MemAddress, Value, NULL);
}

/**
 * @brief Write Debug Print Memory
 *
 * @param[in] AccessWidth   Access width
 * @param[in] MemAddress    Memory address
 * @param[in] Value         Pointer to data
 *
 */
VOID
STATIC
AmdIdsDpRamWrite (
  IN       ACCESS_WIDTH AccessWidth,
  IN       UINT64 MemAddress,
  IN       VOID *Value
  )
{
  LibAmdMemWrite (AccessWidth, MemAddress, Value, NULL);
}

/**
 *  @brief get debug print latest Index
 *
 *  @param[in,out] LatestIndex  - LatestIndex to be filled
 *
 *  @return AGESA_STATUS
 *  @retval AGESA_ERROR - Debug Ram region is read only
 *  @retval AGESA_SUCCESS - Successfully get the Latest Index
 *
**/
AGESA_STATUS
STATIC
AmdIdsDpRamGetLatestIndex (
  IN OUT   UINT32 *LatestIndex
  )
{

  ASSERT (LatestIndex != NULL);
  AmdIdsDpRamRead (AccessWidth32, DEBUG_PRINT_RAM_LATESTIDX_ADDR, LatestIndex);
  return AGESA_SUCCESS;
}

/**
 *  @brief Get DpRam customize Filter
 *
 *  @param[in,out] Filter    Filter do be filled
 *
 *  @return BOOLEAN
 *  @retval FALSE - Alway return FALSE
 *
 **/
BOOLEAN
AmdIdsDpRamGetFilter (
  IN OUT   UINT64 *Filter
  )
{
  return FALSE;
}

/**
 *  @brief Check is BSP or not
 *
 *  @return BOOLEAN
 *  @retval FALSE - If not BSP return False
 *  @retval TRUE - If BSP return True
 *
 **/
BOOLEAN
AmdIdsRamDbgIsBsp (
  VOID
  )
{
  return (BOOLEAN) ((AsmReadMsr64 (MSR_APIC_BAR) & BIT8) != 0);
}

/**
 *  @brief Check RAM Debug signature valid or not
 *
 *  When do warm reboot, we find that memory content will not be clear sometimes
 *  which may result in LastestIndex overflow. so we add more signatures check and 
 *  olny let BSP operates this memory region.
 * 
 *  @return BOOLEAN
 *  @retval FALSE - If not BSP or signature is invalid, return false
 *  @retval TRUE - If all signatures are valid, return True
 *
 **/
BOOLEAN
AmdIdsRamDbgValid (
  VOID
  )
{
  UINT32 DpRamSig     = 0;
  UINT32 DpRamSig1    = 0;
  UINT32 DpRamSig2    = 0;
  UINT32 _LatestIndex = 0;

  //Check is BSP or not
  if (!AmdIdsRamDbgIsBsp ()) {
    return FALSE;
  }

  //Check SIG
  AmdIdsDpRamRead (AccessWidth32, DEBUG_PRINT_RAM_SIG_ADDR, &DpRamSig);
  if (DpRamSig != DEBUG_PRINT_RAM_SIG) {
    return FALSE;
  }
  //Check SIG1
  AmdIdsDpRamRead (AccessWidth32, DEBUG_PRINT_RAM_SIG1_ADDR, &DpRamSig1);
  if (DpRamSig1 != DEBUG_PRINT_RAM_SIG1) {
    return FALSE;
  }
  //Check SIG2
  AmdIdsDpRamRead (AccessWidth32, DEBUG_PRINT_RAM_SIG2_ADDR, &DpRamSig2);
  if (DpRamSig2 != DEBUG_PRINT_RAM_SIG2) {
    return FALSE;
  }
  // Check if LatestIndex overflow
  AmdIdsDpRamRead (AccessWidth32, DEBUG_PRINT_RAM_LATESTIDX_ADDR, &_LatestIndex);
  if (_LatestIndex > PcdGet32 (PcdAmdIdsDebugPrintRamDebugSize)) {
    return FALSE;
  }

  return TRUE;
}

/**
 *  @brief Init local private data
 *
**/
VOID
AmdIdsDpRamConstructor (
  VOID
  )
{
  UINT32 i;
  UINT32 _LatestIndex;
  UINT32 DpRamSig;
  UINT32 DpRamSig1;
  UINT32 DpRamSig2;
  UINT8  DpBufferDftValue;
  
  //Check BSP, if not, return
  if (!AmdIdsRamDbgIsBsp()) return;

  ASSERT (DEBUG_PRINT_RAM_SIG_ADDR != 0);

  //Check Signature, if invalid, need reinit this memory region
 if (!AmdIdsRamDbgValid()){
    //Init Debug Print RAM Header
    DpRamSig = DEBUG_PRINT_RAM_SIG;
    AmdIdsDpRamWrite (AccessWidth32, DEBUG_PRINT_RAM_SIG_ADDR, &DpRamSig);
    //Check if read only memory
    DpRamSig = 0;
    AmdIdsDpRamRead (AccessWidth32, DEBUG_PRINT_RAM_SIG_ADDR, &DpRamSig);
    if (DpRamSig != DEBUG_PRINT_RAM_SIG) {
      return ;
    }
    DpRamSig1 = DEBUG_PRINT_RAM_SIG1;
    DpRamSig2 = DEBUG_PRINT_RAM_SIG2;
    AmdIdsDpRamWrite (AccessWidth32, DEBUG_PRINT_RAM_SIG1_ADDR, &DpRamSig1);
    AmdIdsDpRamWrite (AccessWidth32, DEBUG_PRINT_RAM_SIG2_ADDR, &DpRamSig2);

    //Init Latest Index with zero
    _LatestIndex = 0;
    AmdIdsDpRamWrite (AccessWidth32, DEBUG_PRINT_RAM_LATESTIDX_ADDR, &_LatestIndex);
    //Init Debug Print Buffer with defalut value
    ASSERT (DEBUG_PRINT_BUFFER_SIZE != 0);
    DpBufferDftValue = DEBUG_PRINT_BUFFER_DFT_VALUE;
    for (i = 0; i < DEBUG_PRINT_BUFFER_SIZE; i++) {
      AmdIdsDpRamWrite (AccessWidth8, DEBUG_PRINT_BUFFER_START + i, &DpBufferDftValue);
    }
  }
}

/**
 *  @brief Clean up the Debug Print buffer, remove oldest record, do the relocation
 *
 *  @param[in] NewRecordLength  - The Length of new record
 *  @param[in,out] LatestIndex  - LatestIndex to be Updated
 *
**/
VOID
STATIC
AmdIdsDpRamCleanUp (
  IN       UINTN NewRecordLength,
  IN OUT   UINT32 *LatestIndex
  )
{
  UINT32 RecordOffset;
  UINT8  Value8;
  UINT32 i;

  //Always move forward 4M byte data for convenience
  RecordOffset = (UINT32)PcdGet32 (PcdAmdIdsDebugPrintRamDebugSize)/4;
  //Move forward (RecordOffset = 4MB) byte data
  for (i = 0; i < (*LatestIndex - RecordOffset); i++) {
    AmdIdsDpRamRead (AccessWidth8, DEBUG_PRINT_BUFFER_START + RecordOffset + i, &Value8);
    AmdIdsDpRamWrite (AccessWidth8, DEBUG_PRINT_BUFFER_START + i, &Value8);
  }
  *LatestIndex -= RecordOffset;
  //Fill LatestIndex ~ End with default value
  for (i = 0; i < (DEBUG_PRINT_BUFFER_SIZE - *LatestIndex); i++) {
    Value8 = DEBUG_PRINT_BUFFER_DFT_VALUE;
    AmdIdsDpRamWrite (AccessWidth8, DEBUG_PRINT_BUFFER_START + *LatestIndex + i, &Value8);
  }
}

/**
 *  @brief Print formated string
 *
 *  @param[in] Buffer  - Point to input buffer
 *  @param[in] BufferSize  - Buffer size
 *  @param[in] debugPrintPrivate  - Option
 *
**/
VOID
AmdIdsDpRamPrint (
  IN      CHAR8   *Buffer,
  IN      UINTN BufferSize
  )
{
  UINT32 LatestIndex = 0;
  UINT32 Counter;
  UINT64 DebugPrintBufferAddr;
  UINT32 _BufferSize;

  // Check RAM debug valid or not
  if(!AmdIdsRamDbgValid()) return;

  ASSERT (BufferSize <= DEBUG_PRINT_BUFFER_SIZE);
  //Get Latest Index
  if (AmdIdsDpRamGetLatestIndex (&LatestIndex) == AGESA_SUCCESS) {
    _BufferSize = (UINT32) BufferSize;
    //Check if exceed the limit, if so shift the oldest data, and do the relocation
    if (((LatestIndex + _BufferSize) > DEBUG_PRINT_BUFFER_SIZE)) {
      AmdIdsDpRamCleanUp (_BufferSize, &LatestIndex);
    }
    if ((LatestIndex + _BufferSize) <= DEBUG_PRINT_BUFFER_SIZE) {
      //Save the data to RAM, Update the Latest Index
      Counter = _BufferSize;
      DebugPrintBufferAddr = (UINT64) (DEBUG_PRINT_BUFFER_START + LatestIndex);
      while (Counter--) {
        AmdIdsDpRamWrite (AccessWidth8, DebugPrintBufferAddr++, Buffer++);
      }
      LatestIndex += _BufferSize;
      AmdIdsDpRamWrite (AccessWidth32, DEBUG_PRINT_RAM_LATESTIDX_ADDR, &LatestIndex);
    }
  }
}


CONST AMD_IDS_DEBUG_PRINT_INSTANCE IdsDebugPrintRamInstance =
{
  AmdIdsDpRamSupport,
  AmdIdsDpRamConstructor,
  AmdIdsDpRamGetFilter,
  AmdIdsDpRamPrint
};






