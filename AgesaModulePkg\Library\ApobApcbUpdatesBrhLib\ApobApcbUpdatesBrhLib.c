/*****************************************************************************
 *
 * Copyright (C) 2008-2024 Advanced Micro Devices, Inc. All rights reserved.
 *
 *******************************************************************************
 **/

#include <Uefi.h>
#include <Pi/PiMultiPhase.h>
#include <Library/DebugLib.h>
#include <Library/PcdLib.h>
#include <Library/ApobApcbLib.h>

#include <AGESA.h>
#include <BRH/ApcbV3TokenUid.h>
#include <BRH/APOB.h>
#include <Library/AmdPspApobLib.h>
#include <Library/IdsLib.h>
#include <Library/HobLib.h>
#include <Library/BaseFabricTopologyLib.h>

typedef struct {
  UINT32    Apcb;
  UINTN     Pcd;
} APCB_TO_PCD;

CONST APCB_TO_PCD  UpdateByteList[] = {
  { APCB_TOKEN_UID_APIC_MODE, PcdToken (PcdAmdApicMode) },
  { APCB_TOKEN_UID_DF_REMAP_AT_1TB, PcdToken (PcdAmdFabric1TbRemap) },
};

CONST APCB_TO_PCD  UpdateBooleanList[] = {
  { APCB_TOKEN_UID_IOMMU,     PcdToken (PcdCfgIommuSupport) },
  { APCB_TOKEN_UID_SCAN_DUMP_DEBUG_ENABLE, PcdToken(PcdResetCpuOnSyncFlood) },
};

/**
 * @brief Run through list of 1 Byte APCB tokens and set PCDs
 *
 * @param[in] ApcbUpdates   Pointer to APOB's ApcbUpdates struct
 */
STATIC
VOID
UpdateApcbBytes (
  IN APOB_APCB_UPDATES_STRUCT  *ApcbUpdates
  )
{
  EFI_STATUS  Status;
  UINT32      Idx1;
  UINT32      Idx2;

  for (Idx1 = 0; Idx1 < ApcbUpdates->NumItems; Idx1++) {
    for (Idx2 = 0; Idx2 < ARRAY_SIZE (UpdateByteList); Idx2++) {
      if (UpdateByteList[Idx2].Apcb == ApcbUpdates->Item[Idx1].Token) {
        IDS_HDT_CONSOLE (MAIN_FLOW, "%a: Set APCB %x / PCD %x  to %x\n", __func__, UpdateByteList[Idx2].Apcb, UpdateByteList[Idx2].Pcd, (UINT8)ApcbUpdates->Item[Idx1].Value);
        Status = LibPcdSet8S (UpdateByteList[Idx2].Pcd, (UINT8)ApcbUpdates->Item[Idx1].Value);
        ASSERT_EFI_ERROR (Status);
        break;
      }
    }
  }
}

/**
 * @brief Run through list of boolean APCB tokens and set PCDs
 *
 * @param[in] ApcbUpdates   Pointer to APOB's ApcbUpdates struct
 */
STATIC
VOID
UpdateApcbBooleans (
  IN APOB_APCB_UPDATES_STRUCT  *ApcbUpdates
  )
{
  EFI_STATUS  Status;
  UINT32      Idx1;
  UINT32      Idx2;

  for (Idx1 = 0; Idx1 < ApcbUpdates->NumItems; Idx1++) {
    for (Idx2 = 0; Idx2 < ARRAY_SIZE (UpdateBooleanList); Idx2++) {
      if (UpdateBooleanList[Idx2].Apcb == ApcbUpdates->Item[Idx1].Token) {
        IDS_HDT_CONSOLE (
          MAIN_FLOW,
          "%a: Set APCB %x / PCD %x  to %a\n", __func__,
          UpdateBooleanList[Idx2].Apcb,
          UpdateBooleanList[Idx2].Pcd,
          ((ApcbUpdates->Item[Idx1].Value != 0) ? "TRUE" : "FALSE")
          );
        Status = LibPcdSetBoolS (UpdateBooleanList[Idx2].Pcd, (BOOLEAN) (ApcbUpdates->Item[Idx1].Value != 0));
        ASSERT_EFI_ERROR (Status);
        break;
      }
    }
  }
}

/**
 * @brief Scan APOB for APCB overrides, set PCDs appropriately
 *
 */
VOID
EFIAPI
ApobUpdatePcds (
  VOID
  )
{
  EFI_STATUS                Status;
  APOB_APCB_UPDATES_STRUCT  *ApobEntry;

  IDS_HDT_CONSOLE (MAIN_FLOW, "%a\n", __func__);

  ApobEntry = NULL;

  Status = AmdPspGetApobEntryInstance (APOB_APCB, APOB_APCB_UPDATES_STRUCT_TYPE, 0, FALSE, (APOB_TYPE_HEADER **)&ApobEntry);
  if (EFI_ERROR (Status)) {
    IDS_HDT_CONSOLE (MAIN_FLOW, "Fail to get ApobApcbUpdates\n");
    return;
  }

  if (ApobEntry->NumItems == 0) {
    return;
  }

  UpdateApcbBytes (ApobEntry);
  UpdateApcbBooleans (ApobEntry);
}

/**
 * @brief Prepare the hob data for APOB APCB
 *
 */
VOID
EFIAPI
ApobApcbInit (
  VOID
  )
{
  EFI_STATUS                Status;
  APOB_APCB_UPDATES_STRUCT  *ApobEntry;
  APOB_APCB_TOKEN_HOB       *ApobApcbTokenHob;
  UINT32                    Index;
  UINT32                    InstanceId;
  UINT32                    SocketCount;
  UINT32                    DieCount;
  UINT32                    SocketLoop;
  UINT32                    DieLoop;

  IDS_HDT_CONSOLE (MAIN_FLOW, "%a\n", __FUNCTION__);

  ApobEntry = NULL;

  SocketCount = FabricTopologyGetNumberOfProcessorsPresent ();
  for (SocketLoop = 0; SocketLoop < SocketCount; SocketLoop++) {
    DieCount = FabricTopologyGetNumberOfDiesOnSocket (SocketLoop);
    for (DieLoop = 0; DieLoop < DieCount; DieLoop++) {
      InstanceId = ((DieLoop & 0xFF) | ((SocketLoop & 0xFF) << 8));

      Status = AmdPspGetApobEntryInstance (APOB_APCB, APOB_APCB_UPDATES_STRUCT_TYPE, InstanceId, FALSE, (APOB_TYPE_HEADER **)&ApobEntry);
      if (EFI_ERROR (Status)) {
        IDS_HDT_CONSOLE (MAIN_FLOW, "Fail to get ApobApcbUpdates\n");
        continue;
      }

      if (ApobEntry->NumItems == 0) {
        continue;
      }

      ApobApcbTokenHob = BuildGuidHob (&gAmdApobApcbHobGuid, sizeof (APOB_APCB_TOKEN_HOB) + sizeof (APOB_APCB_TOKEN_ITEM) * ApobEntry->NumItems);
      if (ApobApcbTokenHob == NULL) {
        ASSERT (FALSE);
        continue;
      }

      ApobApcbTokenHob->SocketId = SocketLoop;
      ApobApcbTokenHob->DieId = DieLoop;

      for (Index = 0; Index < ApobEntry->NumItems; Index++) {
        ApobApcbTokenHob->Items[Index].Token = ApobEntry->Item[Index].Token;
        ApobApcbTokenHob->Items[Index].Value = ApobEntry->Item[Index].Value;
      }

      ApobApcbTokenHob->NumberOfItems = ApobEntry->NumItems;
    }
  }
}

