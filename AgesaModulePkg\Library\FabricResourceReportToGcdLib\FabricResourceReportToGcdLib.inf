#;*****************************************************************************
#;
#; Copyright (C) 2008-2024 Advanced Micro Devices, Inc. All rights reserved.
#;
#;******************************************************************************
#For EDKII use Only
[Defines]
  INF_VERSION                    = 0x00010005
  BASE_NAME                      = FabricResourceReportToGcdLib
  FILE_GUID                      = 68468F1E-E0A2-4827-A5AB-E7920B30D850
  MODULE_TYPE                    = BASE
  VERSION_STRING                 = 1.0
  LIBRARY_CLASS                  = FabricResourceReportToGcdLib | DXE_DRIVER DXE_RUNTIME_DRIVER DXE_SAL_DRIVER DXE_SMM_DRIVER SMM_CORE UEFI_APPLICATION UEFI_DRIVER

[Sources]
  FabricResourceReportToGcdLib.c

[Packages]
  MdePkg/MdePkg.dec
  AgesaModulePkg/AgesaCommonModulePkg.dec
  AgesaModulePkg/AgesaModuleDfPkg.dec
  AgesaPkg/AgesaPkg.dec

[LibraryClasses]
  DxeServicesTableLib
  BaseLib
  AmdBaseLib
  AmdHeapLib
  IdsLib

[Guids]

[Protocols]

[Ppis]

[Pcd]
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdBottomMmioReservedForPrimaryRb

[Depex]



