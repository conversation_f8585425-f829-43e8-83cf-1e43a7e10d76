/*
*****************************************************************************
*
 * Copyright (C) 2008-2024 Advanced Micro Devices, Inc. All rights reserved.
 *
*******************************************************************************
*/
#include <Filecode.h>
#include <Library/IoLib.h>
#include <Library/IdsLib.h>
#include <Library/FchBaseLib.h>
#include <Library/FchEspiCmdLib.h>
#include <FchRegistersCommon.h>

#define FILECODE LIBRARY_FCHESPICMDLIB_FCHESPICMDLIB_FILECODE
#define ESPI_CMD_DEBUG_ON     0     // set to 1 if want to turn on debugging output in the library

/*----------------------------------------------------------------------------------------*/
/**
 * Wait4stClear - Wait for DNCMD status bit to be cleared
 *
 * @param[in]  EspiBase       Espi MMIO base
 *
 */
VOID
Wait4stClear (
  IN  UINT32     EspiBase
  )
{
  UINT32            Retry;
  ESPIx00_DN_TXHDR0 EspiReg00;

  for ( Retry = 0; Retry < MAX_ESPI_RETRY; Retry++ ) {
    EspiReg00.Value = MmioRead32 ((UINTN)(EspiBase + ESPI_DN_TXHDR_0));
    if ( EspiReg00.Field.CommandStatus == 0 ) {
      break;
    }
    FchStall (2, NULL);  //delay 2us
  }
}

/*----------------------------------------------------------------------------------------*/
/**
 * FchEspiCmd_InBandRst - eSPI In Band Reset command
 *      1. Check "command status" of "eSPI downstream transmit Header Register 0"
 *         (FCH::ITF::ESPI::DN_TXHDR_0th[DNCMD_STATUS]) to see if the registers are ready to accept the
 *         software programming.
 *         If this bit is set, stop here. If clear, continue to step2.
 *      2. Program Slave0 configuration register (FCH::ITF::ESPI::SLAVE0_CONFIG[CLK_FREQ_SEL]) to set
 *         eSPI Bus clock to 16.7MHz.
 *      3. Program "eSPI downstream transmit Header Register 0" (FCH::ITF::ESPI::DN_TXHDR_0th[DNCMD_TYPE])
 *         to select "In-Band Reset command".
 *      4. Program "command status" (FCH::ITF::ESPI::DN_TXHDR_0th[DNCMD_STATUS]) to 1 to start sending
 *         down "In-Band Reset".
 *      5. Wait for interrupt line set according to Register Command interrupt mapping setting
 *      6. Software check if "command status" bit (FCH::ITF::ESPI::DN_TXHDR_0th[DNCMD_STATUS]) is cleared.
 *      7. Software can issue new Command (GET_CONFIGURATION/SET_CONFIGURATION) for Slave0.
 *
 * @param[in]  EspiBase       Espi MMIO base
 *
 */
VOID
FchEspiCmd_InBandRst  (
  IN  UINT32     EspiBase
  )
{
  ESPIx00_DN_TXHDR0     EspiReg00;
  ESPIx68_SLAVE0_CONFIG EspiReg68;

  Wait4stClear (EspiBase);
  EspiReg00.Value = MmioRead32 ((UINTN)(EspiBase + ESPI_DN_TXHDR_0));
  EspiReg68.Value = MmioRead32 ((UINTN)(EspiBase + ESPI_SLAVE0_CONFIG));
  EspiReg68.Field.OperatingFreq = 0;                                     // [27:25] CLK_FREQ_SEL
  EspiReg00.Field.SWCommandType = IN_BAND_RESET;                         // [2:0] DNCMD_TYPE
  MmioWrite32 ((UINTN) (EspiBase + ESPI_SLAVE0_CONFIG), EspiReg68.Value);
  MmioWrite32 ((UINTN) (EspiBase + ESPI_DN_TXHDR_0), EspiReg00.Value);
  EspiReg00.Field.CommandStatus = 1;
  MmioWrite32 ((UINTN) (EspiBase + ESPI_DN_TXHDR_0), EspiReg00.Value);
  Wait4stClear (EspiBase);
}

/*----------------------------------------------------------------------------------------*/
/**
 * FchEspiCmd_GetConfiguration - eSPI Get Configuration command
 *      1. Check "command status" of "eSPI downstream transmit Header Register 0"
 *         (FCH::ITF::ESPI::DN_TXHDR_0th[DNCMD_STATUS]) to see if the registers are ready to accept the
 *         software programming.
 *         If this bit is set, stop here. If clear, continue to step2.
 *      2. Program "eSPI downstream transmit Header Register 0" (FCH::ITF::ESPI::DN_TXHDR_0th[DNCMD_TYPE])
 *         to select "GET_CONFIGURATION".
 *      3. Program register0 address field (FCH::ITF::ESPI::DN_TXHDR_0th [23:8]) for "GET_CONFIGURATION".
 *      4. Program "command status" (FCH::ITF::ESPI::DN_TXHDR_0th[DNCMD_STATUS]) to 1 to start sending down
 *         "GET_CONFIGURATION".
 *      5. Wait for interrupt line set according to Register Command interrupt mapping setting.
 *      6. Software check if "command status" bit (FCH::ITF::ESPI::DN_TXHDR_0th[DNCMD_STATUS]) is cleared.
 *      7. Software Read data from "eSPI downstream Header Register 1" Data register field
 *         (FCH::ITF::ESPI::DN_TXHDR_1 [31:0]) for "GET_CONFIGURATION" command.
 *
 * @param[in]  EspiBase       Espi MMIO base
 * @param[in]  RegAddr        Slave register address
 *
 * @retval    Register Value
 */
UINT32
FchEspiCmd_GetConfiguration  (
  IN  UINT32     EspiBase,
  IN  UINT32     RegAddr
  )
{
  ESPIx00_DN_TXHDR0     EspiReg00;
  ESPIx04_DN_TXHDR1     EspiReg04;

  Wait4stClear (EspiBase);
  EspiReg00.Value = MmioRead32 ((UINTN)(EspiBase + ESPI_DN_TXHDR_0));
  EspiReg00.Field.SWCommandType = GET_CONFIGURATION;                     // [2:0] DNCMD_TYPE
  EspiReg00.Field.DnCmdHdata0   = (RegAddr & 0xFF00) >> 8;
  EspiReg00.Field.DnCmdHdata1   = RegAddr & 0xFF;
  EspiReg04.Value = 0;
  MmioWrite32 ((UINTN) (EspiBase + ESPI_DN_TXHDR_0), EspiReg00.Value);
  MmioWrite32 ((UINTN) (EspiBase + ESPI_DN_TXHDR_1), EspiReg04.Value);
  EspiReg00.Field.CommandStatus = 1;
  MmioWrite32 ((UINTN) (EspiBase + ESPI_DN_TXHDR_0), EspiReg00.Value);
  Wait4stClear (EspiBase);

  EspiReg04.Value = MmioRead32 ((UINTN)(EspiBase + ESPI_DN_TXHDR_1));
  return EspiReg04.Value;
}

/*----------------------------------------------------------------------------------------*/
/**
 * FchEspiCmd_SetConfiguration - eSPI Set Configuration command
 *      1. Check "command status" of "eSPI downstream transmit Header Register 0"
 *         (FCH::ITF::ESPI::DN_TXHDR_0th[DNCMD_STATUS]) to see if the registers are ready to accept the
 *         software programming.
 *         If this bit is set, stop here. If clear, continue to step2.
 *      2. Program register0 (FCH::ITF::ESPI::DN_TXHDR_0th[DNCMD_TYPE]) to select "SET_CONFIGURATION".
 *      3. Program register0 address field (FCH::ITF::ESPI::DN_TXHDR_0th [23:8]) for "SET_CONFIGURATION".
 *      4. Software write data to "eSPI downstream transmit Header Register 1" (FCH::ITF::ESPI::DN_TXHDR_1 [31:0])
 *         for "SET_CONFIGURATION" command.
 *      5. Program "command status" (FCH::ITF::ESPI::DN_TXHDR_0th[DNCMD_STATUS]) to 1 to start sending down
 *        "SET_CONFIGURATION".
 *      6. Wait for interrupt line set according to Register Command interrupt mapping setting.
 *      7. Software check "command status" bit (FCH::ITF::ESPI::DN_TXHDR_0th[DNCMD_STATUS]) is cleared.
 *      8. Software program Slave0 configuration register (FCH::ITF::ESPI::SLAVE0_CONFIG [31:0]) and set eSPI bus
 *         same as slave device configuration. If slave0 needs to switch clock, software needs to program Slave0
 *         configuration register to select new Bus clock
 *
 * @param[in]  EspiBase       Espi MMIO base
 * @param[in]  RegAddr        Slave register address
 * @param[in]  Value          Slave register value
 *
 */
VOID
FchEspiCmd_SetConfiguration  (
  IN  UINT32     EspiBase,
  IN  UINT32     RegAddr,
  IN  UINT32     Value
  )
{
  ESPIx00_DN_TXHDR0     EspiReg00;
  ESPIx04_DN_TXHDR1     EspiReg04;

  Wait4stClear (EspiBase);
  EspiReg00.Value = MmioRead32 ((UINTN)(EspiBase + ESPI_DN_TXHDR_0));
  EspiReg00.Field.SWCommandType = SET_CONFIGURATION;                     // [2:0] DNCMD_TYPE
  EspiReg00.Field.DnCmdHdata0   = (RegAddr & 0xFF00) >> 8;
  EspiReg00.Field.DnCmdHdata1   = RegAddr & 0xFF;
  EspiReg04.Value = Value;
  MmioWrite32 ((UINTN) (EspiBase + ESPI_DN_TXHDR_0), EspiReg00.Value);
  MmioWrite32 ((UINTN) (EspiBase + ESPI_DN_TXHDR_1), EspiReg04.Value);
  EspiReg00.Field.CommandStatus = 1;
  MmioWrite32 ((UINTN) (EspiBase + ESPI_DN_TXHDR_0), EspiReg00.Value);
  Wait4stClear (EspiBase);
}

/*----------------------------------------------------------------------------------------*/
/**
 * Wait4PutFlashTranActiveClear - Wait for DNCMD PUT_FLASH_NP_TRAN_ACTIVE bit to be cleared
 *
 * @param[in]  EspiBase       Espi MMIO base
 *
 */
VOID
Wait4PutFlashTranActiveClear (
  IN  UINT32     EspiBase
  )
{
  UINT32            Retry;
  ESPIx00_DN_TXHDR0 EspiReg00;

  for ( Retry = 0; Retry < MAX_ESPI_RETRY; Retry++ ) {
    EspiReg00.Value = MmioRead32 ((UINTN)(EspiBase + ESPI_DN_TXHDR_0));
    if ( EspiReg00.Field.PutFlashNpTranActive == 0 ) {
      break;
    }
    FchStall (2, NULL);  //delay 2us
  }
}

/*----------------------------------------------------------------------------------------*/
/**
 * Wait4UpcmdStatusSetAndClear - Wait for UPCMD status bit to be set then clear
 *
 * @param[in]  EspiBase       Espi MMIO base
 *
 */
VOID
Wait4UpcmdStatusSetAndClear (
  IN  UINT32     EspiBase
  )
{
  UINT32            Retry = 0;
  ESPIx10_UP_RXHDR0 EspiReg10;

  for ( Retry = 0; Retry < MAX_ESPI_RETRY; Retry++ ) {
    EspiReg10.Value = MmioRead32 ((UINTN)(EspiBase + ESPI_UP_RXHDR_0));
    if ( EspiReg10.Field.UpCommandStatus == 1 ) {
      // write 1 back to clear
      MmioWrite32 ((UINTN) (EspiBase + ESPI_UP_RXHDR_0), EspiReg10.Value);
      return;
    }
    FchStall (2, NULL);  //delay 2us
  }
}

/*----------------------------------------------------------------------------------------*/
/**
 * FchEspiCmd_SafsFlashRead - eSPI SAFS FLASH Read command
 *      1. Check FCH::ITF::ESPI::DN_TXHDR_0th[DNCMD_STATUS] (see Note below) to see if the registers are ready
 *         to accept the SW programming. If this bit is set, stop here; If clear, continue to step 2 below.
 *      2. Program FCH::ITF::ESPI::DN_TXHDR_0th[DNCMD_TYPE] to select "Command Type" for "Flash down
 *         stream request".
 *      3. Program FCH::ITF::ESPI::DN_TXHDR_0th [31:8] to select "Cycle Type", "Tag" and Length" (which is
 *         recommended to be programmed per the value of Maximum Read Request Size Supported).
 *      4. Program FCH::ITF::ESPI::DN_TXHDR_1 [31:0] to set "Address".
 *      5. Program FCH::ITF::ESPI::DN_TXHDR_0th[DNCMD_STATUS] = 1 to start sending down the
 *         PUT_FLASH_NP packet.
 *      6. Wait for interrupt line set (FCH::ITF::ESPI::SLAVE0_INT_STS[DNCMD_INT]) according to Register
 *         Command interrupt mapping setting.
 *      7. Software checks if FCH::ITF::ESPI::DN_TXHDR_0th[DNCMD_STATUS] is cleared (See Note below).
 *      8. When eSPI Slave sets FLASH_C_AVAIL in slave status register, eSPI controller hardware will automatically send
 *         GET_FLASH_C packet to slave and get back the upstream Flash-completion data and store the data in
 *         "Received_OOB/Flash_Register_FIFO_0x18".
 *         1. Note: When FCH::ITF::ESPI::SLAVE0_CONFIG[PUT_FLASH_NP_HEADER_DATA_EN] is 1 and no
 *            DEFER occur, eSPI controller could get back the upstream Flash-completion data within this cycle and
 *            will not send GET_FLASH_C.
 *      9. When one flash completion data packet has been received in FIFO, controller will generate Command Interrupt
 *         according to Register Command interrupt mapping setting.
 *          1. There may be 3 cases to receive flash completion packet:
 *              1. PUT_FLASH_NP with immediately response
 *              2. GET_FLASH_C
 *              3. GET_STATUS with modifier.
 *          2. Each case the "RX_REQ_TYPE" will be different.
 *      10. Software reads FCH::ITF::ESPI::SLAVE0_INT_STS[FLASH_REQ_INT] and knows Flash completion Packet is
 *          received.
 *      11. Software checks whether FCH::ITF::ESPI::UP_RXHDR_0[UPCMD_STATUS] is set and whether
 *          FCH::ITF::ESPI::UP_RXHDR_0[UPCMD_TYPE] is matching with the interrupt.
 *          1. When get back data by GET_FLASH_C, it is 3'b010
 *          2. When PUT_FLASH_NP with immediately response, it is 3'b011
 *          3. When GET_STATUS with modifier, it is 3'b100
 *      12. Then software reads FCH::ITF::ESPI::UP_RXHDR_0 [31:0] to get the Flash completion header information.
 *      13. If the cycle type is CplD, then software needs to read FCH::ITF::ESPI::UP_RXDATA_PORT [31:0] by DW
 *          access. For example, if the Flash Read Request eSPI header length is 0x9 (9 bytes), after software reads
 *          header, software need read 0x18 (Data Port) three times, the first read is for Byte3 ~ Byte0, the second
 *          read is for Byte7 ~ Byte4, the third read is {"Do not care", "Do not Care", Byte9, Byte8}.
 *      14. Software writes 1'b1 to FCH::ITF::ESPI::UP_RXHDR_0[UPCMD_STATUS] and eSPI controller register RX
 *          FIFO is ready to accept new upstream Flah/OOB request.
 *      15. Software writes 1'b1 to FCH::ITF::ESPI::SLAVE0_INT_STS[FLASH_REQ_INT] to clear Flash request interrupt.
 *    Note: Since Put_Flash_NP is non-posted and might be deferred by device, if FCH::ITF::ESPI::DN_TXHDR_0th [4:3]
 *    = 2'b10, it means this SAFS request is deferred and pending to device for a final completion. It's a must to
 *    check FCH::ITF::ESPI::DN_TXHDR_0th[PUT_FLASH_NP_TRAN_ACTIVE] is cleared before issuing any new SAFS request
 *    per register interface. Of course, it is fine to issue other request per register interface at that time.
 *
 * @param[in]  EspiBase       Espi MMIO base
 * @param[in]  Address        Address to read
 * @param[in]  Length         Length in byte to read
 * @param[in]  Buffer         Pointer to the data read to
 *
 */
EFI_STATUS
FchEspiCmd_SafsFlashRead  (
  IN  UINT32     EspiBase,
  IN  UINT32     Address,
  IN  UINT32     Length,
  OUT UINT8      *Buffer
  )
{
  ESPIx00_DN_TXHDR0           EspiReg00;
  ESPIx04_DN_TXHDR1           EspiReg04;
  ESPIx10_UP_RXHDR0           EspiReg10;
  ESPIx70_SLAVE0_INT_STS      EspiReg70;
  ESPI_SL40_SLAVE_FA_CAPCFG   FaCapCfg;
  UINT32                      MaxReadReqSize;
  UINT32                      Retry;
  UINT32                      RxData, RxLen;
  UINT32                      i;

#if ESPI_CMD_DEBUG_ON
  IDS_HDT_CONSOLE (MAIN_FLOW, "FCH ESPI SAFS: FchEspiCmd_SafsFlashRead Start!\n");
#endif

  Wait4stClear (EspiBase);

  // Get Maximum Read Request Size (0x40[14:12])
  FaCapCfg.Value = FchEspiCmd_GetConfiguration (EspiBase, SLAVE_FA_CAPCFG);

  // Flash Access Channel Maximum Read Request Size 0x40[14:12] bits definitions:
  // 000b: Reserved.
  // 001b: 64 bytes max read request size.
  // 010b: 128 bytes max read request size.
  // 011b: 256 bytes max read request size.
  // 100b: 512 bytes max read request size.
  // 101b: 1024 bytes max read request size.
  // 110b: 2048 bytes max read request size.
  // 111b: 4096 bytes max read request size.
  if (FaCapCfg.Field.ChMaxReadReqSize != 0) {
      MaxReadReqSize = 64 << (FaCapCfg.Field.ChMaxReadReqSize - 1);
  }
  else {
    MaxReadReqSize = 64;  // Set 64 bytes as default
  }

  // According to eSPI spec, If the read request size exceeds the Maximum Payload Size of the respective channel,
  // the completion must be returned in multiple split completions, with each completion contains up to the
  // Maximum Payload Size.
  // According to PPR, the length is recommended to be programmed per the value of Maximum Read Request Size
  // Supported.
  if (Length > MaxReadReqSize) {
  #if ESPI_CMD_DEBUG_ON
    IDS_HDT_CONSOLE (MAIN_FLOW, "FCH ESPI SAFS: Length is too large.\n");
  #endif
    return EFI_INVALID_PARAMETER;
  }

  // Set Command Type, Cycle Type, Tag, Length.
  EspiReg00.Value = MmioRead32 ((UINTN)(EspiBase + ESPI_DN_TXHDR_0));
  EspiReg00.Field.SWCommandType = FA_DOWN_STREAM;                     // [2:0] DNCMD_TYPE
  EspiReg00.Field.DnCmdHdata0   = CYCLE_TYPE_FLASH_READ;
  EspiReg00.Field.DnCmdHdata1   = (Length >> 8) & 0x0F;               // Tag = 0, Length[11:8]
  EspiReg00.Field.DnCmdHdata2   = Length & 0xFF;                      // Length[7:0]
  MmioWrite32 ((UINTN) (EspiBase + ESPI_DN_TXHDR_0), EspiReg00.Value);

  // Set Address
  EspiReg04.Value = Address;
  MmioWrite32 ((UINTN) (EspiBase + ESPI_DN_TXHDR_1), EspiReg04.Value);

  // Send down the PUT_FLASH_NP packet
  EspiReg00.Field.CommandStatus = 1;
  MmioWrite32 ((UINTN) (EspiBase + ESPI_DN_TXHDR_0), EspiReg00.Value);

  Wait4stClear (EspiBase);
  Wait4PutFlashTranActiveClear (EspiBase);

  // Check FLASH_REG_INT
  for ( Retry = 0; Retry < MAX_ESPI_RETRY; Retry++ ) {
    EspiReg70.Value = MmioRead32 ((UINTN)(EspiBase + ESPI_SLAVE0_INT_STS));
    if ( EspiReg70.Field.FlashReqInt == 1 ) {
      // Flash Completion Packet is received
      break;
    }
    FchStall (2, NULL);  //delay 2us
  }

  if (Retry >= MAX_ESPI_RETRY) {
    // Wait for completion packet timeout
#if ESPI_CMD_DEBUG_ON
      IDS_HDT_CONSOLE (MAIN_FLOW, "FCH ESPI SAFS: Wait timeout.\n");
#endif
    return EFI_TIMEOUT;
  }

  // Check UPCMD_STATUS
  for ( Retry = 0; Retry < MAX_ESPI_RETRY; Retry++ ) {
    EspiReg10.Value = MmioRead32 ((UINTN)(EspiBase + ESPI_UP_RXHDR_0));
    if ( EspiReg10.Field.UpCommandStatus == 1 ) {
      break;
    }
    FchStall (2, NULL);  //delay 2us
  }

  if (Retry >= MAX_ESPI_RETRY) {
#if ESPI_CMD_DEBUG_ON
    IDS_HDT_CONSOLE (MAIN_FLOW, "Wait UPCMD_STATUS timeout.\n");
#endif
  }

  // Read flash completion header information
  EspiReg10.Value = MmioRead32 ((UINTN)(EspiBase + ESPI_UP_RXHDR_0));

  // Received data length. It should match requested data length.
  RxLen = (UINT32)(((EspiReg10.Field.UpCmdHdata1 & 0x0F) << 8) + EspiReg10.Field.UpCmdHdata2);

  if (RxLen > Length) {
    return EFI_BAD_BUFFER_SIZE;
  }

  // Read data from RX FIFO
  for (i = 0; i < RxLen; i += 4) {
    RxData = MmioRead32 ((UINTN)(EspiBase + ESPI_UP_RXDATA_PORT));

    // Check boundary to avoid buffer overflow
    if (i < Length) {
      Buffer[i] = (UINT8)(RxData & 0xFF);
        if ((i + 1) < Length) {
        Buffer[i + 1] = (UINT8)((RxData >> 8) & 0xFF);
        if ((i + 2) < Length) {
          Buffer[i + 2] = (UINT8)((RxData >> 16) & 0xFF);
          if ((i + 3) < Length) {
            Buffer[i + 3] = (UINT8)((RxData >> 24) & 0xFF);
          }
        }
      }
    }
  }

  // Clear UPCMD_STATUS (Write-1-to-clear). The rest bits of the register are Read-only.
  EspiReg10.Field.UpCommandStatus = 1;
  MmioWrite32 ((UINTN) (EspiBase + ESPI_UP_RXHDR_0), EspiReg10.Value);

  // Clear FLASH_REQ_INT. The bits of the register are Write-1-to-clear.
  EspiReg70.Value = 0;
  EspiReg70.Field.FlashReqInt = 1;
  MmioWrite32 ((UINTN) (EspiBase + ESPI_SLAVE0_INT_STS), EspiReg70.Value);

  return EFI_SUCCESS;
}

/*----------------------------------------------------------------------------------------*/
/**
 * FchEspiCmd_SafsFlashWrite - eSPI SAFS FLASH Write command
 *      1. Check FCH::ITF::ESPI::DN_TXHDR_0th[DNCMD_STATUS] to see if the registers are ready to accept the SW
 *         programming. If this bit is set, stop here. If clear, continue to step 2 below.
 *      2. Program FCH::ITF::ESPI::DN_TXDATA_PORT [31:0] and write data into Data FIFO.
 *      3. Program FCH::ITF::ESPI::DN_TXHDR_0th[DNCMD_TYPE] to select "Command Type" for "Flash down stream
 *         request".
 *      4. Program FCH::ITF::ESPI::DN_TXHDR_0th [31:8] to set "Cycle Type", "Tag" and "Length" (by reference to
 *         Flash Access Channel Maximum Payload Size).
 *      5. Program FCH::ITF::ESPI::DN_TXHDR_1 [31:0] to set "Address".
 *      6. Program FCH::ITF::ESPI::DN_TXHDR_0th[DNCMD_STATUS] to 1 to start sending down the PUT_FLASH_NP packet.
 *      7. Wait for interrupt line set (bit[28]) according to Register Command interrupt mapping setting.
 *      8. Software checks if FCH::ITF::ESPI::DN_TXHDR_0th[DNCMD_STATUS] is cleared (See Note below).
 *      9. When eSPI Sslave sets FLASH_C_AVAIL in lave status register, eSPI controller hardware will
 *         automatically send GET_FLASH_C packet to slave and update the completion information into "Upstream
 *         Received Flash/OOB Header Register0", then controller will generate Command Interrupt according to
 *         Register Command interrupt mapping setting.
 *      10. Software reads FCH::ITF::ESPI::SLAVE0_INT_STS[FLASH_REQ_INT] and knows the completion is received.
 *      11. Software checks whether FCH::ITF::ESPI::UP_RXHDR_0[UPCMD_STATUS] is set and whether
 *          FCH::ITF::ESPI::UP_RXHDR_0[UPCMD_TYPE] is matching with the interrupt.
 *           1. When get back data by GET_FLASH_C, it is 3'b010
 *           2. When PUT_FLASH_NP with immediately response, it is 3'b011
 *           3. When GET_STATUS with modifier, it is 3'b100
 *      12. Then software can read FCH::ITF::ESPI::UP_RXHDR_0 [31:0] to confirm the flash completion details.
 *          At this time point, software can take new actions.
 *      13. Software write 1'b1 to FCH::ITF::ESPI::UP_RXHDR_0[UPCMD_STATUS] and eSPI controller register RX FIFO
 *          is ready to accept new upstream Flash/OOBN request.
 *      14. Software writes 1'b1 to FCH::ITF::ESPI::SLAVE0_INT_STS[FLASH_REQ_INT] to clear Flash request interrupt.
 *    Note: Since Put_Flash_NP is non-posted and might be deferred by device, if FCH::ITF::ESPI::DN_TXHDR_0th [4:3]
 *    = 2'b10, it means this SAFS request is deferred and pending to device for a final completion. It's a must to
 *    check FCH::ITF::ESPI::DN_TXHDR_0th[PUT_FLASH_NP_TRAN_ACTIVE] to be cleared before issuing any new SAFS request
 *    per register interface. Of course, it is fine to issue other request per register interface at that time.
 *
 * @param[in]  EspiBase       Espi MMIO base
 * @param[in]  Address        Address to write
 * @param[in]  Length         Length in byte to write
 * @param[in]  Value          Pointer to the data to write
 *
 */
EFI_STATUS
FchEspiCmd_SafsFlashWrite  (
  IN  UINT32     EspiBase,
  IN  UINT32     Address,
  IN  UINT32     Length,
  IN  UINT8      *Value
  )
{
  ESPIx00_DN_TXHDR0           EspiReg00;
  ESPIx04_DN_TXHDR1           EspiReg04;
  ESPIx0C_DN_TXDATA_PORT      EspiReg0C;
  ESPIx30_GLOBAL_CTRL0        EspiReg30;
  ESPIx68_SLAVE0_CONFIG       EspiReg68;
  ESPIx70_SLAVE0_INT_STS      EspiReg70;
  UINT32                      MaxPayloadSize;
  UINT32                      Reg30Save;
  UINT32                      i;

#if ESPI_CMD_DEBUG_ON
  IDS_HDT_CONSOLE (MAIN_FLOW, "FCH ESPI SAFS: FchEspiCmd_SafsFlashWrite Start!\n");
#endif

  // Disable watchdog counter
  EspiReg30.Value = MmioRead32 ((UINTN)(EspiBase + ESPI_GLOBAL_CTRL0));
  Reg30Save = EspiReg30.Value;
  EspiReg30.Field.WdgEn = 0;
  MmioWrite32 ((UINTN) (EspiBase + ESPI_GLOBAL_CTRL0), EspiReg30.Value);

  Wait4stClear (EspiBase);

  // Get Flash Access Channel Maximum Payload Size Selected (Assume that it is set by eSPI init code for both 0x40[10:8] and SLAVE0_CONFIG[FLASH_MPS].)
  EspiReg68.Value = MmioRead32 ((UINTN)(EspiBase + ESPI_SLAVE0_CONFIG));

  // Flash Access Channel Maximum Payload Size Selected/Supported bits definitions
  // 000b: Reserved
  // 001b: 64 bytes max payload size
  // 010b: 128 bytes max payload size
  // 011b: 256 bytes max payload size
  // 100b-111b: Reserved
  if (EspiReg68.Field.FlashMaxPayloadSize == 0x01) {
    MaxPayloadSize = 64;
  }
  else if (EspiReg68.Field.FlashMaxPayloadSize == 0x02) {
    MaxPayloadSize = 128;
  }
  else if (EspiReg68.Field.FlashMaxPayloadSize == 0x03) {
    MaxPayloadSize = 256;
  }
  else {
    MaxPayloadSize = 64;  // Set 64 bytes as default
  }

  // If input length is too long, return error.
  if (Length > MaxPayloadSize) {
#if ESPI_CMD_DEBUG_ON
      IDS_HDT_CONSOLE (MAIN_FLOW, "FCH ESPI SAFS: Length is too large.\n");
#endif
    return EFI_INVALID_PARAMETER;
  }

  // Write data into Data FIFO
  for (i = 0; i < Length; i += 4) {
    // Clear data first at every cycle
    EspiReg0C.Value = 0;

    // Check boundary to avoid buffer overflow
    if (i < Length) {
      EspiReg0C.Field.DnTxData0 = Value[i];
      if ((i + 1) < Length) {
        EspiReg0C.Field.DnTxData1 = Value[i + 1];
        if ((i + 2) < Length) {
          EspiReg0C.Field.DnTxData2 = Value[i + 2];
          if ((i + 3) < Length) {
            EspiReg0C.Field.DnTxData3 = Value[i + 3];
          }
        }
      }
    }

    MmioWrite32 ((UINTN) (EspiBase + ESPI_DN_TXDATA_PORT), EspiReg0C.Value);
  }

  // Set Command Type, Cycle Type, Tag, Length.
  EspiReg00.Value = MmioRead32 ((UINTN)(EspiBase + ESPI_DN_TXHDR_0));
  EspiReg00.Field.SWCommandType = FA_DOWN_STREAM;                     // [2:0] DNCMD_TYPE
  EspiReg00.Field.DnCmdHdata0   = CYCLE_TYPE_FLASH_WRITE;
  EspiReg00.Field.DnCmdHdata1   = (Length >> 8) & 0x0F;               // Tag = 0, Length[11:8]
  EspiReg00.Field.DnCmdHdata2   = Length & 0xFF;                      // Length[7:0]
  MmioWrite32 ((UINTN) (EspiBase + ESPI_DN_TXHDR_0), EspiReg00.Value);

  // Set Address
  EspiReg04.Value = Address;
  MmioWrite32 ((UINTN) (EspiBase + ESPI_DN_TXHDR_1), EspiReg04.Value);

  // Send down the PUT_FLASH_NP packet
  EspiReg00.Field.CommandStatus = 1;
  MmioWrite32 ((UINTN) (EspiBase + ESPI_DN_TXHDR_0), EspiReg00.Value);

  Wait4stClear (EspiBase);
  Wait4UpcmdStatusSetAndClear(EspiBase);
  Wait4PutFlashTranActiveClear (EspiBase);

  // Clear FLASH_REQ_INT. The bits of the register are Write-1-to-clear.
  EspiReg70.Value = 0;
  EspiReg70.Field.FlashReqInt = 1;
  MmioWrite32 ((UINTN) (EspiBase + ESPI_SLAVE0_INT_STS), EspiReg70.Value);

  // Restore Global Control 0
  MmioWrite32 ((UINTN) (EspiBase + ESPI_GLOBAL_CTRL0), Reg30Save);

  return EFI_SUCCESS;
}

/*----------------------------------------------------------------------------------------*/
/**
 * FchEspiCmd_SafsFlashErase - eSPI SAFS FLASH Erase command
 *      1. Check FCH::ITF::ESPI::DN_TXHDR_0th[DNCMD_STATUS] to see if the registers are ready to accept the SW
 *         programming (See Note below). If this bit is set, stop here. If clear, continue to step 2 below.
 *      2. Program FCH::ITF::ESPI::DN_TXHDR_0th[DNCMD_TYPE] to select "Command Type" for "Flash down stream
 *         request".
 *      3. Program FCH::ITF::ESPI::DN_TXHDR_0th [31:8] to set "Cycle Type", "Tag" and "Length".
 *          1. Note: "Length[1:0]" value means erase block size.
 *      4. Program FCH::ITF::ESPI::DN_TXHDR_1 [31:0] to set "Address".
 *      5. Program FCH::ITF::ESPI::DN_TXHDR_0th[DNCMD_STATUS] to 1 to start sending down the PUT_FLASH_NP packet.
 *      6. Wait for interrupt line set (bit[28]) according to Register Command interrupt mapping setting.
 *      7. Software checks if FCH::ITF::ESPI::DN_TXHDR_0th[DNCMD_STATUS] is cleared (see Note below).
 *      8. When eSPI Slave sets FLASH_C_AVAIL in slave status register, eSPI controller hardware will automatically
 *         send GET_FLASH_C packet to slave and update the completion information into "Upstream Received Flash/OOB
 *         Header Register0", then controller will generate Command Interrupt according to Register Command interrupt
 *         mapping setting.
 *      9. Software reads FCH::ITF::ESPI::SLAVE0_INT_STS[FLASH_REQ_INT] and knows the completion is received.
 *      10. Software checks whether FCH::ITF::ESPI::UP_RXHDR_0[UPCMD_STATUS] is set and
 *          FCH::ITF::ESPI::UP_RXHDR_0[UPCMD_TYPE] is matching with the interrupt.
 *           1. When get back by GET_FLASH_C, it is 3'b010
 *           2. When PUT_FLASH_NP with immediately response, it is 3'b011
 *           3. When GET_STATUS with modifier, it is 3'b100
 *      11. Then software reads FCH::ITF::ESPI::UP_RXHDR_0 [31:0] to confirm the flash completion details.
 *          At this time point, software can take new actions.
 *      12. Software writes 1'b1 to FCH::ITF::ESPI::UP_RXHDR_0[UPCMD_STATUS] and eSPI controller register RX
 *          FIFO is ready to accept new upstream Flash/OOB request.
 *      13. Software writes 1'b1 to FCH::ITF::ESPI::SLAVE0_INT_STS[FLASH_REQ_INT] to clear Flash request interrupt.
 *    Note: Since Put_Flash_NP is non-posted and might be deferred by device, if FCH::ITF::ESPI::DN_TXHDR_0th [4:3]
 *    = 2'b10, it means this SAFS request is deferred and pending to device for a final completion. It's a must to
 *    check FCH::ITF::ESPI::DN_TXHDR_0th[PUT_FLASH_NP_TRAN_ACTIVE] to be cleared before issuing any new SAFS request
 *    per register interface. Of course, it is fine to issue other request per register interface at that time.
 *
 * SAFS Erase Block Size:  Length     Block Size
 *                           0           4KB
 *                           1          32KB
 *                           2          64KB
 *                           3         128KB
 *
 * @param[in]  EspiBase       Espi MMIO base
 * @param[in]  Address        Address to erase
 * @param[in]  Length         Block Size to erase
 *
 *
 */
EFI_STATUS
FchEspiCmd_SafsFlashErase  (
  IN  UINT32     EspiBase,
  IN  UINT32     Address,
  IN  UINT32     Length
  )
{
  ESPIx00_DN_TXHDR0          EspiReg00;
  ESPIx04_DN_TXHDR1          EspiReg04;
  ESPIx30_GLOBAL_CTRL0       EspiReg30;
  ESPIx70_SLAVE0_INT_STS     EspiReg70;
  ESPI_SL44_SLAVE_FA_CAPCFG2 FaCapCfg2;
  UINT32                     Reg30Save;

#if ESPI_CMD_DEBUG_ON
    IDS_HDT_CONSOLE (MAIN_FLOW, "FCH ESPI SAFS: FchEspiCmd_SafsFlahErase Start!\n");
#endif

  // Disable watchdog counter
  EspiReg30.Value = MmioRead32 ((UINTN)(EspiBase + ESPI_GLOBAL_CTRL0));
  Reg30Save = EspiReg30.Value;
  EspiReg30.Field.WdgEn = 0;
  MmioWrite32 ((UINTN) (EspiBase + ESPI_GLOBAL_CTRL0), EspiReg30.Value);

  //
  // First to check if device support Erase Block Size
  //
#if ESPI_CMD_DEBUG_ON
    IDS_HDT_CONSOLE (MAIN_FLOW, "FCH ESPI SAFS: Check if device support Erase Block Size!\n");
#endif

  FaCapCfg2.Value = FchEspiCmd_GetConfiguration (EspiBase, SLAVE_FA_CAPCFG2);

#if ESPI_CMD_DEBUG_ON
  IDS_HDT_CONSOLE (MAIN_FLOW, "FCH ESPI SAFS: SLAVE_FA_CAPCFG2 (offset 0x44) = 0x%X!\n", FaCapCfg2.Value);
#endif

  /***********************************************************
   *
   * SAFS Erase Block Size support bitmap (bit15:8):
   *   Bit 0: Reserved
   *   Bit 1: Reserved
   *   Bit 2: 4 Kbytes EBS supported
   *   Bit 3: Reserved
   *   Bit 4: Reserved
   *   Bit 5: 32 Kbytes EBS supported
   *   Bit 6: 64 Kbytes EBS supported
   *   Bit 7: 128 Kbytes EBS supported
   *
  ************************************************************/
  if ((Length == 0) && ((FaCapCfg2.Field.RO_TargetFlashEraseBlockSize & BIT2) == 0)) {
#if ESPI_CMD_DEBUG_ON
      IDS_HDT_CONSOLE (MAIN_FLOW, "FCH ESPI SAFS: Device does NOT support Erase Block Size 4KB!\n");
#endif
    return EFI_UNSUPPORTED;
  } else if ((Length == 1) && ((FaCapCfg2.Field.RO_TargetFlashEraseBlockSize & BIT5) == 0)) {
#if ESPI_CMD_DEBUG_ON
      IDS_HDT_CONSOLE (MAIN_FLOW, "FCH ESPI SAFS: Device does NOT support Erase Block Size 32KB!\n");
#endif
    return EFI_UNSUPPORTED;
  } else if ((Length == 2) && ((FaCapCfg2.Field.RO_TargetFlashEraseBlockSize & BIT6) == 0)) {
#if ESPI_CMD_DEBUG_ON
      IDS_HDT_CONSOLE (MAIN_FLOW, "FCH ESPI SAFS: Device does NOT support Erase Block Size 64KB!\n");
#endif
    return EFI_UNSUPPORTED;
  } else if ((Length == 3) && ((FaCapCfg2.Field.RO_TargetFlashEraseBlockSize & BIT7) == 0)) {
#if ESPI_CMD_DEBUG_ON
      IDS_HDT_CONSOLE (MAIN_FLOW, "FCH ESPI SAFS: Device does NOT support Erase Block Size 128KB!\n");
#endif
    return EFI_UNSUPPORTED;
  } else if (Length > 3) {
#if ESPI_CMD_DEBUG_ON
      IDS_HDT_CONSOLE (MAIN_FLOW, "FCH ESPI SAFS: illegal Erase Block Size input (>3)!\n");
#endif
    return EFI_INVALID_PARAMETER;
  }

  Wait4stClear (EspiBase);
  EspiReg00.Value = MmioRead32 ((UINTN)(EspiBase + ESPI_DN_TXHDR_0));
  EspiReg00.Field.SWCommandType = FA_DOWN_STREAM;                     // [2:0] DNCMD_TYPE
  // ESPIx00[31:24] set to Length[7:0]
  // ESPIx00[23:16] set to 0: Tag = 0, Length[11:8] = 0
  // ESPIx00[15:8] set to Cycle Type
  EspiReg00.Field.DnCmdHdata0   = CYCLE_TYPE_FLASH_ERASE;
  EspiReg00.Field.DnCmdHdata1   = 0;
  EspiReg00.Field.DnCmdHdata2   = Length & 0xFF;
  EspiReg04.Value = Address;

  MmioWrite32 ((UINTN) (EspiBase + ESPI_DN_TXHDR_0), EspiReg00.Value);
  MmioWrite32 ((UINTN) (EspiBase + ESPI_DN_TXHDR_1), EspiReg04.Value);
  EspiReg00.Field.CommandStatus = 1;
  MmioWrite32 ((UINTN) (EspiBase + ESPI_DN_TXHDR_0), EspiReg00.Value);
  Wait4stClear (EspiBase);
  Wait4UpcmdStatusSetAndClear(EspiBase);
  Wait4PutFlashTranActiveClear (EspiBase);

  // Clear FLASH_REQ_INT. The bits of the register are Write-1-to-clear.
  EspiReg70.Value = 0;
  EspiReg70.Field.FlashReqInt = 1;
  MmioWrite32 ((UINTN) (EspiBase + ESPI_SLAVE0_INT_STS), EspiReg70.Value);

  // Restore Global Control 0
  MmioWrite32 ((UINTN) (EspiBase + ESPI_GLOBAL_CTRL0), Reg30Save);

  return EFI_SUCCESS;
}

/*----------------------------------------------------------------------------------------*/
/**
 * FchEspiCmd_SafsRpmcOp1 - eSPI SAFS RPMC OP1 command
 *
 * SAFS-RPMC OP1 with R1R0 = 0x0 (Cycle Type = 0x03)
 *      1. Check FCH::ITF::ESPI::DN_TXHDR_0th[DNCMD_STATUS] to see if the registers are ready to accept the SW
 *         programming (see Note below). If this bit is set, stop here. If clear, continue to step 2 below.
 *      2. Program FCH::ITF::ESPI::DN_TXDATA_PORT [31:0] and write data into Data FIFO.
 *      3. Program FCH::ITF::ESPI::DN_TXHDR_0th[DNCMD_TYPE] to select "Command Type" for "Flash down
 *         stream request". This operation must be done by byte-write operation.
 *      4. Program 0x03(Cycle Type of RPMC OP1 with R1R0 = 0x0) to
 *         FCH::ITF::ESPI::DN_TXHDR_0th[DNCMD_HDATA0]. This operation must be done by byte-write operation.
 *      5. Program FCH::ITF::ESPI::DN_TXHDR_0th [31:16] to set "Tag" and "Length" (Length[11:0] could be set to a
 *         value within 40h (the usual value could be for a length of 63 bytes, 39 bytes, 47 bytes or 1 byte)).
 *      6. Program FCH::ITF::ESPI::DN_TXHDR_0th[DNCMD_STATUS] to 1 to start sending down the
 *         PUT_FLASH_NP packet.
 *      7. Wait for interrupt line set (bit[28]) according to Register Command interrupt mapping setting.
 *      8. Software checks if FCH::ITF::ESPI::DN_TXHDR_0th[DNCMD_STATUS] is cleared (see Note below).
 *      9. When eSPI Slave sets FLASH_C_AVAIL in slave status register, eSPI controller hardware will automatically send
 *         GET_FLASH_C packet to slave and update the completion information into "Upstream Received Flash/OOB
 *         Header Register0", then controller will generate Command Interrupt according to Register Command interrupt
 *         mapping setting.
 *          1. Note: When PUT_FLASH_NP_HEADER_EN is 1 and no DEFER occur, eSPI controller could get back
 *             the upstream Flash completion header within this cycle and will not send GET_FLASH_C.
 *      10. Software reads FCH::ITF::ESPI::SLAVE0_INT_STS[FLASH_REQ_INT] and knows the completion is received.
 *          1. There may be 3 cases to receive flash completion packet:
 *              1. PUT_FLASH_NP with immediately response
 *              2. GET_FLASH_C
 *              3. GET_STATUS with modifier
 *          2. Each case, the "RX_REQ_TYPE" will be different.
 *      11. Software checks whether FCH::ITF::ESPI::UP_RXHDR_0[UPCMD_STATUS] is set and whether
 *          FCH::ITF::ESPI::UP_RXHDR_0[UPCMD_TYPE] is matching with the interrupt.
 *          1. When get back data by GET_FLASH_C, it is 3'b010
 *          2. When PUT_FLASH_NP with immediately response, it is 3'b011
 *          3. When GET_STATUS with modifier, it is 3'b100
 *      12. Then software can read FCH::ITF::ESPI::UP_RXHDR_0 [31:0] to confirm the flash completion details. At this
 *          time point, software can take new actions.
 *      13. Software writes 1'b1 to FCH::ITF::ESPI::UP_RXHDR_0[UPCMD_STATUS] and eSPI controller register RX
 *          FIFO is ready to accept new upstream Flash/OOB request.
 *      14. Software writes 1'b1 to FCH::ITF::ESPI::SLAVE0_INT_STS[FLASH_REQ_INT] to clear Flash request interrupt.
 *
 *    Note: Since Put_Flash_NP is non-posted and might be deferred by device, if FCH::ITF::ESPI::DN_TXHDR_0th [4:3]
 *    = 2'b10, it means this SAFS request is deferred and pending to device for a final completion. It's a must to
 *    check FCH::ITF::ESPI::DN_TXHDR_0th[PUT_FLASH_NP_TRAN_ACTIVE] to be cleared before issuing any new SAFS request
 *    per register interface. Of course, it is fine to issue other request per register interface at that time.
 *
 * SAFS-RPMC OP1 with R1R0 = 0x1/0x2/0x3 (Cycle Type = 0x23/0x43/0x63)
 *      1. Check FCH::ITF::ESPI::DN_TXHDR_0th[DNCMD_STATUS] to see if the registers are ready to accept the SW
 *         programming (see Note below). If this bit is set, stop here. If clear, continue to step 2 below.
 *      2. Program data byte 0 to FCH::ITF::ESPI::DN_TXHDR_1 [31:24].
 *      3. Program data byte 1 to FCH::ITF::ESPI::DN_TXHDR_1 [23:16].
 *      4. Program data byte 2 to FCH::ITF::ESPI::DN_TXHDR_1 [15:8].
 *      5. Program data byte 3 to FCH::ITF::ESPI::DN_TXHDR_1 [7:0].
 *      6. Program data byte 4 and the rest of data to FCH::ITF::ESPI::DN_TXDATA_PORT[31:0] and write data into Data
 *         FIFO.
 *      7. Program FCH::ITF::ESPI::DN_TXHDR_0th[DNCMD_TYPE] to select "Command Type" for "Flash down
 *         stream request". This operation must be done by byte-write operation.
 *      8. Program 0x23 or 0x43 or 0x63(Cycle Type of RPMC OP1 with R1R0 = 0x1/0x2/0x3) to
 *         FCH::ITF::ESPI::DN_TXHDR_0th[DNCMD_HDATA0]. This operation must be done by byte-write operation.
 *      9. Program FCH::ITF::ESPI::DN_TXHDR_0th [31:16] to set "Tag" and "Length" (Length[11:0] could be set to a
 *         value within 40h (the usual value could be for a length of 63 bytes, 39 bytes, 47 bytes or 1 byte)).
 *      10. Program FCH::ITF::ESPI::DN_TXHDR_0th[DNCMD_STATUS] to 1 to start sending down the
 *          PUT_FLASH_NP packet.
 *      11. Wait for interrupt line set (bit[28]) according to Register Command interrupt mapping setting.
 *      12. Software checks if FCH::ITF::ESPI::DN_TXHDR_0th[DNCMD_STATUS] is cleared (see Note below).
 *      13. When eSPI Slave sets FLASH_C_AVAIL in slave status register, eSPI controller hardware will automatically
 *          send GET_FLASH_C packet to slave and update the completion information into "Upstream Received Flash/OOB
 *          Header Register0", then controller will generate Command Interrupt according to Register Command interrupt
 *          mapping setting.
 *          1. Note: When PUT_FLASH_NP_HEADER_EN is 1 and no DEFER occur, eSPI controller could get back
 *          the upstream Flash completion header within this cycle and will not send GET_FLASH_C.
 *      14. Software reads FCH::ITF::ESPI::SLAVE0_INT_STS[FLASH_REQ_INT] and knows the completion is received.
 *          1. There may be 3 cases to receive flash completion packet:
 *              1. PUT_FLASH_NP with immediately response
 *              2. GET_FLASH_C
 *              3. GET_STATUS with modifier
 *          2. Each case, the "RX_REQ_TYPE" will be different.
 *      15. Software checks whether FCH::ITF::ESPI::UP_RXHDR_0[UPCMD_STATUS] is set and whether
 *          FCH::ITF::ESPI::UP_RXHDR_0[UPCMD_TYPE] is matching with the interrupt.
 *          1. When get back data by GET_FLASH_C, it is 3'b010
 *          2. When PUT_FLASH_NP with immediately response, it is 3'b011
 *          3. When GET_STATUS with modifier, it is 3'b100
 *      16. Then software can read FCH::ITF::ESPI::UP_RXHDR_0 [31:0] to confirm the flash completion details. At this
 *          time point, software can take new actions.
 *      17. Software writes 1'b1 to FCH::ITF::ESPI::UP_RXHDR_0[UPCMD_STATUS] and eSPI controller register RX
 *          FIFO is ready to accept new upstream Flash/OOB request.
 *      18. Software writes 1'b1 to FCH::ITF::ESPI::SLAVE0_INT_STS[FLASH_REQ_INT] to clear Flash request interrupt.
 *    Note: Since Put_Flash_NP is non-posted and might be deferred by device, if FCH::ITF::ESPI::DN_TXHDR_0th [4:3]
 *    = 2'b10, it means this SAFS request is deferred and pending to device for a final completion. It's a must to
 *    check FCH::ITF::ESPI::DN_TXHDR_0th[PUT_FLASH_NP_TRAN_ACTIVE] to be cleared before issuing any new SAFS request
 *    per register interface. Of course, it is fine to issue other request per register interface at that time.
 *
 * @param[in]  EspiBase       Espi MMIO base
 * @param[in]  RpmcFlashDev   Which RPMC flash device is targeted to
 * @param[in]  Length         Length in byte
 * @param[in]  Data           Pointer to data to send
 *
 */
EFI_STATUS
FchEspiCmd_SafsRpmcOp1  (
  IN  UINT32     EspiBase,
  IN  UINT8      RpmcFlashDev,
  IN  UINT32     Length,
  OUT UINT8      *Data
  )
{
  ESPIx00_DN_TXHDR0           EspiReg00;
  ESPIx04_DN_TXHDR1           EspiReg04;
  ESPIx0C_DN_TXDATA_PORT      EspiReg0C;
  ESPIx30_GLOBAL_CTRL0        EspiReg30;
  ESPIx70_SLAVE0_INT_STS      EspiReg70;
  UINT32                      Reg30Save;
  UINT32                      i;

#if ESPI_CMD_DEBUG_ON
  IDS_HDT_CONSOLE (MAIN_FLOW, "FCH ESPI SAFS: FchEspiCmd_SafsRpmcOp1 Start!\n");
#endif

  // Disable watchdog counter
  EspiReg30.Value = MmioRead32 ((UINTN)(EspiBase + ESPI_GLOBAL_CTRL0));
  Reg30Save = EspiReg30.Value;
  EspiReg30.Field.WdgEn = 0;
  MmioWrite32 ((UINTN) (EspiBase + ESPI_GLOBAL_CTRL0), EspiReg30.Value);

  Wait4stClear (EspiBase);

  // Length should be within 40h.
  if (Length > RPMC_CMD_PAYLOAD_MAX_LEN) {
    return EFI_INVALID_PARAMETER;
  }

  // Check R1R0 encoding
  // 00 - Indicates the RPMC cycle is targeting to the 1st RPMC flash device.
  // 01 - Indicates the RPMC cycle is targeting to the 2nd RPMC flash device.
  // 10 - Indicates the RPMC cycle is targeting to the 3rd RPMC flash device.
  // 11 - Indicates the RPMC cycle is targeting to the 4th RPMC flash device.

  // SAFS-RPMC OP1 with R1R0 = 0x0 (Cycle Type = 0x03)
  if (RpmcFlashDev == 0) {
    // Write data into Data FIFO
    for (i = 0; i < Length; i += 4) {
      // Clear data first at every cycle
      EspiReg0C.Value = 0;

      // Check boundary to avoid buffer overflow
      if (i < Length) {
        EspiReg0C.Field.DnTxData0 = Data[i];
        if ((i + 1) < Length) {
          EspiReg0C.Field.DnTxData1 = Data[i + 1];
          if ((i + 2) < Length) {
            EspiReg0C.Field.DnTxData2 = Data[i + 2];
            if ((i + 3) < Length) {
              EspiReg0C.Field.DnTxData3 = Data[i + 3];
            }
          }
        }
      }
      MmioWrite32 ((UINTN) (EspiBase + ESPI_DN_TXDATA_PORT), EspiReg0C.Value);
    }

    // Set Command Type
    EspiReg00.Value = MmioRead32 ((UINTN)(EspiBase + ESPI_DN_TXHDR_0));
    EspiReg00.Field.SWCommandType = FA_DOWN_STREAM;                     // [2:0] DNCMD_TYPE
    // Must be byte-write operation for DNCMD_TYPE
    MmioWrite8 ((UINTN) (EspiBase + ESPI_DN_TXHDR_0), (UINT8)(EspiReg00.Value & 0x07));

    // Set Cycle Type, Tag, Length
    // ESPIx00[31:24] set to Length[7:0]
    // ESPIx00[23:16] set to 0: Tag = 0, Length[11:8] = 0
    // ESPIx00[15:8] set to Cycle Type
    EspiReg00.Field.DnCmdHdata0   = CYCLE_TYPE_RPMC_OP1;
    EspiReg00.Field.DnCmdHdata1   = (Length >> 8) & 0x0F;               // Tag = 0, Length[11:8]
    EspiReg00.Field.DnCmdHdata2   = Length & 0xFF;                      // Length[7:0]
    // Must be byte-write operation for DNCMD_HDATA0
    MmioWrite8 ((UINTN) (EspiBase + ESPI_DN_TXHDR_0 + 1), (UINT8)(EspiReg00.Field.DnCmdHdata0));

    MmioWrite8 ((UINTN) (EspiBase + ESPI_DN_TXHDR_0 + 2), (UINT8)(EspiReg00.Field.DnCmdHdata1));
    MmioWrite8 ((UINTN) (EspiBase + ESPI_DN_TXHDR_0 + 3), (UINT8)(EspiReg00.Field.DnCmdHdata2));
  }
  // SAFS-RPMC OP1 with R1R0 = 0x1/0x2/0x3 (Cycle Type = 0x23/0x43/0x63)
  else if (RpmcFlashDev <= 3) {
    // Write first 4 bytes to DN_TXHDR_1
    EspiReg04.Field.DnCmdHdata3 = Data[0];
    EspiReg04.Field.DnCmdHdata4 = Data[1];
    EspiReg04.Field.DnCmdHdata5 = Data[2];
    EspiReg04.Field.DnCmdHdata6 = Data[3];
    MmioWrite32 ((UINTN) (EspiBase + ESPI_DN_TXHDR_1), EspiReg04.Value);

    // Write rest of data into Data FIFO
    for (i = 4; i < Length; i += 4) {
      // Clear data first at every cycle
      EspiReg0C.Value = 0;

      // Check boundary to avoid buffer overflow
      if (i < Length) {
        EspiReg0C.Field.DnTxData0 = Data[i];
        if ((i + 1) < Length) {
          EspiReg0C.Field.DnTxData1 = Data[i + 1];
          if ((i + 2) < Length) {
            EspiReg0C.Field.DnTxData2 = Data[i + 2];
            if ((i + 3) < Length) {
              EspiReg0C.Field.DnTxData3 = Data[i + 3];
            }
          }
        }
      }
      MmioWrite32 ((UINTN) (EspiBase + ESPI_DN_TXDATA_PORT), EspiReg0C.Value);
    }

    // Set Command Type
    EspiReg00.Value = MmioRead32 ((UINTN)(EspiBase + ESPI_DN_TXHDR_0));
    EspiReg00.Field.SWCommandType = FA_DOWN_STREAM;                     // [2:0] DNCMD_TYPE
    // Must be byte-write operation for DNCMD_TYPE
    MmioWrite8 ((UINTN) (EspiBase + ESPI_DN_TXHDR_0), (UINT8)(EspiReg00.Value & 0x07));

    // Set Cycle Type, Tag, Length
    // ESPIx00[31:24] set to Length[7:0]
    // ESPIx00[23:16] set to 0: Tag = 0, Length[11:8] = 0
    // ESPIx00[15:8] set to Cycle Type
    EspiReg00.Field.DnCmdHdata0   = (RpmcFlashDev << 5) | CYCLE_TYPE_RPMC_OP1;  // Cycle Type = 0x23/0x43/0x63
    EspiReg00.Field.DnCmdHdata1   = (Length >> 8) & 0x0F;               // Tag = 0, Length[11:8]
    EspiReg00.Field.DnCmdHdata2   = Length & 0xFF;                      // Length[7:0]
    // Must be byte-write operation for DNCMD_HDATA0
    MmioWrite8 ((UINTN) (EspiBase + ESPI_DN_TXHDR_0 + 1), (UINT8)(EspiReg00.Field.DnCmdHdata0));

    MmioWrite8 ((UINTN) (EspiBase + ESPI_DN_TXHDR_0 + 2), (UINT8)(EspiReg00.Field.DnCmdHdata1));
    MmioWrite8 ((UINTN) (EspiBase + ESPI_DN_TXHDR_0 + 3), (UINT8)(EspiReg00.Field.DnCmdHdata2));
  }
  else {
    return EFI_INVALID_PARAMETER;
  }

  // Send down the PUT_FLASH_NP packet
  EspiReg00.Field.CommandStatus = 1;
  MmioWrite8 ((UINTN) (EspiBase + ESPI_DN_TXHDR_0), (UINT8)(EspiReg00.Value & 0x0F));

  Wait4stClear (EspiBase);
  Wait4UpcmdStatusSetAndClear(EspiBase);
  Wait4PutFlashTranActiveClear (EspiBase);

  // Clear FLASH_REQ_INT. The bits of the register are Write-1-to-clear.
  EspiReg70.Value = 0;
  EspiReg70.Field.FlashReqInt = 1;
  MmioWrite32 ((UINTN) (EspiBase + ESPI_SLAVE0_INT_STS), EspiReg70.Value);

  // Restore Global Control 0
  MmioWrite32 ((UINTN) (EspiBase + ESPI_GLOBAL_CTRL0), Reg30Save);

  return EFI_SUCCESS;
};

/*----------------------------------------------------------------------------------------*/
/**
 * FchEspiCmd_SafsRpmcOp2 - eSPI SAFS RPMC OP2 command
 *      1. Check FCH::ITF::ESPI::DN_TXHDR_0th[DNCMD_STATUS] to see if the registers are ready to accept the SW
 *         programming (see Note below). If this bit is set, stop here. If clear, continue to step 2 below.
 *      2. Program FCH::ITF::ESPI::DN_TXHDR_0th[DNCMD_TYPE] to select "Command Type" for "Flash down
 *         stream request". This operation must be done by byte-write operation.
 *      3. Program FCH::ITF::ESPI::DN_TXHDR_0th [15:8] to set "Cycle Type", this operation must be done by bytewrite
 *         operation.
 *      4. Program FCH::ITF::ESPI::DN_TXHDR_0th [31:16] to set "Tag" and "Length" (which is recommended to be
 *         programmed per the value of Maximum Read Request Size Supported).
 *      5. Programming FCH::ITF::ESPI::DN_TXHDR_0th[DNCMD_STATUS] to 1 to start sending down the
 *         PUT_FLASH_NP packet.
 *      6. Wait for interrupt line set (bit[28]) according to Register Command interrupt mapping setting.
 *      7. Software checks if FCH::ITF::ESPI::DN_TXHDR_0th[DNCMD_STATUS] is cleared (see Note below).
 *      8. When eSPI Slave sets FLASH_C_AVAIL in salve status register, eSPI controller hardware will automatically
 *         send GET_FLASH_C packet to slave and get back the upstream Flash completion data and store the data in
 *         "Received OOB/Flash Register FIFO 0x18".
 *         1. Note: When FCH::ITF::ESPI::SLAVE0_CONFIG[PUT_FLASH_NP_HEADER_DATA_EN] is 1 and no
 *         DEFER occur, eSPI controller could get back the upstream Flash completion data within this cycle and
 *         will not send GET_FLASH_C).
 *      9. When one flash completion data packet has been received in FIFO, controller will generate Command Interrupt
 *         according to Register Command interrupt mapping setting.
 *         1. There may be 3 cases to receive flash completion packet:
 *            1. PUT_FLASH_NP with immediately response
 *            2. GET_FLASH_C
 *            3. GET_STATUS with modifier.
 *         2. Each case, the "RX_REQ_TYPE" will be different.
 *      10. Software reads FCH::ITF::ESPI::SLAVE0_INT_STS[FLASH_REQ_INT] and knows Flash completion Packet is
 *          received.
 *      11. Software checks whether FCH::ITF::ESPI::UP_RXHDR_0[UPCMD_STATUS] is set and whether
 *          FCH::ITF::ESPI::UP_RXHDR_0[UPCMD_TYPE] is matching with the interrupt.
 *          1. When get back data by GET_FLASH_C, it is 3'b010
 *          2. When PUT_FLASH_NP with immediately response, it is 3'b011
 *          3. When GET_STATUS with modifier, it is 3'b100
 *      12. Then- software reads FCH::ITF::ESPI::UP_RXHDR_0 [31:0] to get the Flash completion header information.
 *      13. If the cycle type is CplD, then software needs to read FCH::ITF::ESPI::UP_RXDATA_PORT [31:0] by DW
 *          access. For example, if the RPMC OP2 request eSPI header length is 0x9 (9 bytes), after software reads
 *          header, software need read 0x18 (Data Port) three times. The first read is for Byte3 ~ Byte0, the second
 *          read is for Byte7 ~ Byte4, the third read is {"Do not care", "Do not care", byte9, byte8}.
 *      14. Software writes 1'b1 to FCH::ITF::ESPI::UP_RXHDR_0[UPCMD_STATUS] and eSPI controller register RX
 *          FIFO is ready to accept new upstream Flash/OOB request.
 *      15. Software writes 1'b1 to FCH::ITF::ESPI::SLAVE0_INT_STS[FLASH_REQ_INT] to clear Flash request interrupt.
 *    Note: Since Put_Flash_NP is non-posted and might be deferred by device, if FCH::ITF::ESPI::DN_TXHDR_0th [4:3]
 *    = 2'b10, it means this SAFS request is deferred and pending to device for a final completion. It is a must to
 *    check FCH::ITF::ESPI::DN_TXHDR_0th[PUT_FLASH_NP_TRAN_ACTIVE] to be cleared before issuing any new SAFS request
 *    per register interface. Of course, it is fine to issue other request per register interface at that time.
 *
 * @param[in]  EspiBase       Espi MMIO base
 * @param[in]  RpmcFlashDev   Which RPMC flash device is targeted to
 * @param[in]  Length         Length in byte
 * @param[in]  Buffer         Pointer to buffer to receive
 *
 */
EFI_STATUS
FchEspiCmd_SafsRpmcOp2  (
  IN  UINT32     EspiBase,
  IN  UINT8      RpmcFlashDev,
  IN  UINT32     Length,
  IN  UINT8      *Buffer
  )
{
  ESPIx00_DN_TXHDR0           EspiReg00;
  ESPIx10_UP_RXHDR0           EspiReg10;
  ESPIx70_SLAVE0_INT_STS      EspiReg70;
  ESPI_SL40_SLAVE_FA_CAPCFG   FaCapCfg;
  UINT32                      MaxReadReqSize;
  UINT32                      Retry;
  UINT32                      RxData, RxLen;
  UINT32                      i;

#if ESPI_CMD_DEBUG_ON
  IDS_HDT_CONSOLE (MAIN_FLOW, "FCH ESPI SAFS: FchEspiCmd_SafsRpmcOp2 Start!\n");
#endif

  Wait4stClear (EspiBase);

  // Get Maximum Read Request Size (0x40[14:12])
  FaCapCfg.Value = FchEspiCmd_GetConfiguration (EspiBase, SLAVE_FA_CAPCFG);

  // Flash Access Channel Maximum Read Request Size 0x40[14:12] bits definitions:
  // 000b: Reserved.
  // 001b: 64 bytes max read request size.
  // 010b: 128 bytes max read request size.
  // 011b: 256 bytes max read request size.
  // 100b: 512 bytes max read request size.
  // 101b: 1024 bytes max read request size.
  // 110b: 2048 bytes max read request size.
  // 111b: 4096 bytes max read request size.
  if (FaCapCfg.Field.ChMaxReadReqSize != 0) {
      MaxReadReqSize = 64 << (FaCapCfg.Field.ChMaxReadReqSize - 1);
  }
  else {
    MaxReadReqSize = 64;  // Set 64 bytes as default
  }

  // According to eSPI spec, If the read request size exceeds the Maximum Payload Size of the respective channel,
  // the completion must be returned in multiple split completions, with each completion contains up to the
  // Maximum Payload Size.
  // According to PPR, the length is recommended to be programmed per the value of Maximum Read Request Size
  // Supported.
  if (Length > MaxReadReqSize) {
    return EFI_INVALID_PARAMETER;
  }

  // Check R1R0 encoding
  // 00 - Indicates the RPMC cycle is targeting to the 1st RPMC flash device.
  // 01 - Indicates the RPMC cycle is targeting to the 2nd RPMC flash device.
  // 10 - Indicates the RPMC cycle is targeting to the 3rd RPMC flash device.
  // 11 - Indicates the RPMC cycle is targeting to the 4th RPMC flash device.
  if (RpmcFlashDev > 3) {
    return EFI_INVALID_PARAMETER;
  }

  // Set Command Type
  EspiReg00.Value = MmioRead32 ((UINTN)(EspiBase + ESPI_DN_TXHDR_0));
  EspiReg00.Field.SWCommandType = FA_DOWN_STREAM;                     // [2:0] DNCMD_TYPE
  // Must be byte-write operation for DNCMD_TYPE
  MmioWrite8 ((UINTN) (EspiBase + ESPI_DN_TXHDR_0), (UINT8)(EspiReg00.Value & 0x07));

  // Set Cycle Type, Tag, Length
  // ESPIx00[31:24] set to Length[7:0]
  // ESPIx00[23:16] set to 0: Tag = 0, Length[11:8] = 0
  // ESPIx00[15:8] set to Cycle Type
  EspiReg00.Field.DnCmdHdata0   = (RpmcFlashDev << 5) | CYCLE_TYPE_RPMC_OP2; // Cycle Type = [0 R1 R0 0 0 1 0 0]
  EspiReg00.Field.DnCmdHdata1   = (Length >> 8) & 0x0F;               // Tag = 0, Length[11:8]
  EspiReg00.Field.DnCmdHdata2   = Length & 0xFF;                      // Length[7:0]
  // Must be byte-write operation for DNCMD_HDATA0
  MmioWrite8 ((UINTN) (EspiBase + ESPI_DN_TXHDR_0 + 1), (UINT8)(EspiReg00.Field.DnCmdHdata0));

  MmioWrite8 ((UINTN) (EspiBase + ESPI_DN_TXHDR_0 + 2), (UINT8)(EspiReg00.Field.DnCmdHdata1));
  MmioWrite8 ((UINTN) (EspiBase + ESPI_DN_TXHDR_0 + 3), (UINT8)(EspiReg00.Field.DnCmdHdata2));

  // Send down the PUT_FLASH_NP packet
  EspiReg00.Field.CommandStatus = 1;
  MmioWrite8 ((UINTN) (EspiBase + ESPI_DN_TXHDR_0), (UINT8)(EspiReg00.Value & 0x0F));

  Wait4stClear (EspiBase);
  Wait4PutFlashTranActiveClear (EspiBase);

  // Check FLASH_REG_INT
  for ( Retry = 0; Retry < MAX_ESPI_RETRY; Retry++ ) {
    EspiReg70.Value = MmioRead32 ((UINTN)(EspiBase + ESPI_SLAVE0_INT_STS));
    if ( EspiReg70.Field.FlashReqInt == 1 ) {
      // Flash Completion Packet is received
      break;
    }
    FchStall (2, NULL);  //delay 2us
  }

  if (Retry >= MAX_ESPI_RETRY) {
    // Wait for completion packet timeout
    return EFI_TIMEOUT;
  }

  // Check UPCMD_STATUS
  for ( Retry = 0; Retry < MAX_ESPI_RETRY; Retry++ ) {
    EspiReg10.Value = MmioRead32 ((UINTN)(EspiBase + ESPI_UP_RXHDR_0));
    if ( EspiReg10.Field.UpCommandStatus == 1 ) {
      break;
    }
    FchStall (2, NULL);  //delay 2us
  }

  if (Retry >= MAX_ESPI_RETRY) {
#if ESPI_CMD_DEBUG_ON
    IDS_HDT_CONSOLE (MAIN_FLOW, "Wait UPCMD_STATUS timeout.\n");
#endif
  }

  // Read flash completion header information
  EspiReg10.Value = MmioRead32 ((UINTN)(EspiBase + ESPI_UP_RXHDR_0));

  // Received data length. It should match requested data length.
  RxLen = (UINT32)(((EspiReg10.Field.UpCmdHdata1 & 0x0F) << 8) + EspiReg10.Field.UpCmdHdata2);

  if (RxLen > Length) {
    return EFI_BAD_BUFFER_SIZE;
  }

  // Read data from RX FIFO
  for (i = 0; i < RxLen; i += 4) {
    RxData = MmioRead32 ((UINTN)(EspiBase + ESPI_UP_RXDATA_PORT));

    // Check boundary to avoid buffer overflow
    if (i < Length) {
      Buffer[i] = (UINT8)(RxData & 0xFF);
        if ((i + 1) < Length) {
        Buffer[i + 1] = (UINT8)((RxData >> 8) & 0xFF);
        if ((i + 2) < Length) {
          Buffer[i + 2] = (UINT8)((RxData >> 16) & 0xFF);
          if ((i + 3) < Length) {
            Buffer[i + 3] = (UINT8)((RxData >> 24) & 0xFF);
          }
        }
      }
    }
  }

  // Clear UPCMD_STATUS (Write-1-to-clear). The rest bits of the register are Read-only.
  EspiReg10.Field.UpCommandStatus = 1;
  MmioWrite32 ((UINTN) (EspiBase + ESPI_UP_RXHDR_0), EspiReg10.Value);

  // Clear FLASH_REQ_INT. The bits of the register are Write-1-to-clear.
  EspiReg70.Value = 0;
  EspiReg70.Field.FlashReqInt = 1;
  MmioWrite32 ((UINTN) (EspiBase + ESPI_SLAVE0_INT_STS), EspiReg70.Value);

  return EFI_SUCCESS;
};

