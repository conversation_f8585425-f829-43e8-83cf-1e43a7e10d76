/*
 ******************************************************************************
 *
 * Copyright (C) 2019-2024 Advanced Micro Devices, Inc. All rights reserved.
 *
 *******************************************************************************
 */

/*----------------------------------------------------------------------------------------
 *                             M O D U L E S    U S E D
 *----------------------------------------------------------------------------------------
 */
#include <Uefi.h>
#include <AMD.h>
#include <Filecode.h>
#include <cpuRegisters.h>
#include <Library/BaseLib.h>
#include <Library/AmdBaseLib.h>
#include <Library/CcxBaseX86Lib.h>

/*----------------------------------------------------------------------------------------
 *                   D E F I N I T I O N S    A N D    M A C R O S
 *----------------------------------------------------------------------------------------
 */
#define FILECODE LIBRARY_CCXAPICZEN5LIB_CCXAPICZEN5LIB_FILECODE

#define CORE_MASK_WIDTH_MASK 0x1F
#define LEVEL_TYPE_MASK      0xFF00
#define LEVEL_TYPE_OFFSET    0x8

// CPUID_Fn0000000B ECX LevelType
typedef enum {
  LEVEL_TYPE_INVALID = 0,
  LEVEL_TYPE_THREAD,
  LEVEL_TYPE_PROCESSOR
} EXTENDED_TOPOLOGY_ENUMERATION_LEVEL_TYPE;

// CPUID_Fn80000026 ECX LevelType
typedef enum {
  LEVEL_TYPE_RESERVED = 0,
  LEVEL_TYPE_CORE,
  LEVEL_TYPE_COMPLEX,
  LEVEL_TYPE_CCD,
  LEVEL_TYPE_SOCKET,
} EXTENDED_CPU_TOPOLOGY_LEVEL_TYPE;

/*----------------------------------------------------------------------------------------
 *           P R O T O T Y P E S     O F     L O C A L     F U  N C T I O N S
 *----------------------------------------------------------------------------------------
 */

 /*----------------------------------------------------------------------------------------
  *                          E X P O R T E D    F U N C T I O N S
  *----------------------------------------------------------------------------------------
 */

 /*---------------------------------------------------------------------------------------*/
 /**
  *
  * Returns the APIC Id based on the provided inputs
  *
  * @param[in]      Socket              Socket # of APIC Id to calculate
  * @param[in]      Die                 Die # of APIC Id to calculate
  * @param[in]      Ccd                 CCD # of APIC Id to calculate
  * @param[in]      Complex             Complex # of APIC Id to calculate
  * @param[in]      Core                Core # of APIC Id to calculate
  * @param[in]      Thread              Thread # of APIC Id to calculate
  *
  * @return         UINT32              Calculate APIC Id
  */
UINT32
CcxCalcLocalApic (
  IN       UINTN    Socket,
  IN       UINTN    Die,
  IN       UINTN    Ccd,
  IN       UINTN    Complex,
  IN       UINTN    Core,
  IN       UINTN    Thread
  )
{
  CPUID_DATA CpuId = {0};
  UINT8      Subindex        = 0;
  BOOLEAN    SmtEnable       = FALSE;
  UINT8      CoreBitShift    = 0;
  UINT8      ComplexBitShift = 0;
  UINT8      CcdBitShift     = 0;
  UINT8      SocketBitShift  = 0;
  UINT32     ApicId          = 0;

  ASSERT (Socket < 2);
  ASSERT (Die == 0);
  ASSERT (Ccd < 16);
  ASSERT (Complex < 2);
  ASSERT (Core < 16);
  ASSERT (Thread < 2);

  // CPUID_Fn0000000B [Extended Topology Enumeration]
  AsmCpuidEx (AMD_CPUID_EXTENDED_TOPOLOGY_ENUMERATION, 0, &(CpuId.EAX_Reg), &(CpuId.EBX_Reg), &(CpuId.ECX_Reg), &(CpuId.EDX_Reg));
  if ((CpuId.EAX_Reg & CORE_MASK_WIDTH_MASK) == 0x1) {
    SmtEnable = TRUE;
  } else {
    SmtEnable = FALSE;
  }

  // CPUID_Fn80000026 [Extended CPU Topology] subindex 0~3
  for (Subindex = 0; Subindex <= 3; Subindex++) {
    AsmCpuidEx (AMD_CPUID_EXTENDED_CPU_TOPOLOGY, Subindex, &(CpuId.EAX_Reg), &(CpuId.EBX_Reg), &(CpuId.ECX_Reg), &(CpuId.EDX_Reg));
    if (((CpuId.ECX_Reg & LEVEL_TYPE_MASK) >> LEVEL_TYPE_OFFSET) == LEVEL_TYPE_CORE) {
      // Core
      CoreBitShift = CpuId.EAX_Reg & CORE_MASK_WIDTH_MASK;
    }

    if (((CpuId.ECX_Reg & LEVEL_TYPE_MASK) >> LEVEL_TYPE_OFFSET) == LEVEL_TYPE_COMPLEX) {
      // Complex
      ComplexBitShift = CpuId.EAX_Reg & CORE_MASK_WIDTH_MASK;
    }

    if (((CpuId.ECX_Reg & LEVEL_TYPE_MASK) >> LEVEL_TYPE_OFFSET) == LEVEL_TYPE_CCD) {
      // CCD
      CcdBitShift = CpuId.EAX_Reg & CORE_MASK_WIDTH_MASK;
    }

    if (((CpuId.ECX_Reg & LEVEL_TYPE_MASK) >> LEVEL_TYPE_OFFSET) == LEVEL_TYPE_SOCKET) {
      // Socket
      SocketBitShift = CpuId.EAX_Reg & CORE_MASK_WIDTH_MASK;
    }
  }

  ApicId = (UINT32)((Core << CoreBitShift) + \
                    (Complex << ComplexBitShift) + \
                    (Ccd << CcdBitShift) + \
                    (Socket << SocketBitShift));
  if (SmtEnable) {
    ApicId += (UINT32)Thread;
  }

  return ApicId;
}
