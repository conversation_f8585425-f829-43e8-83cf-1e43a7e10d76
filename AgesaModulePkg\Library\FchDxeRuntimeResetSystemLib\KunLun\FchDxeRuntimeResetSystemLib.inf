#;*****************************************************************************
#;
#; Copyright (C) 2022-2024 Advanced Micro Devices, Inc. All rights reserved.
#;
#;******************************************************************************

[Defines]
  INF_VERSION                    = 0x00010006
  BASE_NAME                      = ResetSystemLib
  FILE_GUID                      = 81797A01-C500-478F-B462-FC1F7ADE1665
  MODULE_TYPE                    = DXE_RUNTIME_DRIVER
  VERSION_STRING                 = 1.0
  LIBRARY_CLASS                  = ResetSystemLib
  CONSTRUCTOR                    = FchDxeRuntimeResetSystemLibConstructor

#
#  VALID_ARCHITECTURES           = IA32 X64
#

[Sources]
  FchDxeRuntimeResetSystemLib.c

[Packages]
  MdePkg/MdePkg.dec
  AgesaPkg/AgesaPkg.dec
  MdeModulePkg/MdeModulePkg.dec
  AgesaModulePkg/AgesaModuleFchPkg.dec

[LibraryClasses]
  BaseLib
  PrintLib
  IoLib
  FchBaseLib

[Protocols]

[Guids]
  gEfiAmdAgesaSpecificWarmResetGuid

[Pcd]
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdFchFullHardReset

