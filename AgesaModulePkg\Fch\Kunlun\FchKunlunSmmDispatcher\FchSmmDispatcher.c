/******************************************************************************
*
 * Copyright (C) 2008-2024 Advanced Micro Devices, Inc. All rights reserved.
 *
*******************************************************************************
**/

#include "FchSmmDispatcher.h"

#include <Protocol/SmmCpuIo2.h>
#include <Protocol/FabricTopologyServices2.h>
#include "FchSmmSxDispatcher.h"
#include "FchSmmSwDispatcher.h"
#include "FchSmmPwrBtnDispatcher.h"
#include "FchSmmIoTrapDispatcher.h"
#include "FchSmmPeriodicalDispatcher.h"
#include "FchSmmGpiDispatcher.h"
#include "FchSmmUsbDispatcher.h"
#include "FchSmmMiscDispatcher.h"
#include "FchSmmApuRasDispatcher.h"
#include <Library/SmmMemLib.h>
#include "FchPlatform.h"

#define FILECODE FCH_KUNLUN_FCHKUNLUNSMMDISPATCHER_FCHSMMDISPATCHER_FILECODE

extern FCH_SMM_DISPATCHER_TABLE FchSmmDispatcherTable[];
extern UINT8 NumOfDispatcherTableEntry;
extern SAVE_B2B_IO B2bIoList[];
extern UINT8 NumOfB2bIoListEntry;
extern SAVE_PCI SavePciList[];
extern UINT8 NumOfSavePciListEntry;
extern FCH_USB_SMI_SYSINFO  FchUsbSmiSysInfo;
extern UINT16 Cpl3BlockIoList[];
extern UINT8 NumOfCpl3BlockIoListEntry;

UINT8  mEspiSmiOutB = 0;
UINTN  gSmmNumberOfSocketEnabled = 0;

EFI_SMM_CPU_PROTOCOL         *mSmmCpuProtocol;

FCH_SMM_SW_NODE              *HeadFchSmmSwNodePtr;
FCH_SMM_SX_NODE              *HeadFchSmmSxNodePtr;
FCH_SMM_PWRBTN_NODE          *HeadFchSmmPwrBtnNodePtr;
FCH_SMM_PERIODICAL_NODE      *HeadFchSmmPeriodicalNodePtr;
FCH_SMM_GPI_NODE             *HeadFchSmmGpiNodePtr;
FCH_SMM_USB_NODE             *HeadFchSmmUsbNodePtr;
FCH_SMM_MISC_NODE            *HeadFchSmmMiscNodePtr;
FCH_SMM_APURAS_NODE          *HeadFchSmmApuRasNodePtr;
FCH_SMM_COMMUNICATION_BUFFER *CommunicationBufferPtr;
FCH_SMM_SW_CONTEXT           *EfiSmmSwContext;

EFI_SMM_PERIODIC_TIMER_CONTEXT EfiSmmPeriodicTimerContext;

typedef struct {
  EFI_GUID  *Guid;
  VOID      *Interface;
} FCH_PROTOCOL_LIST;

FCH_PROTOCOL_LIST FchProtocolList[] = {
                    {&gFchSmmSwDispatch2ProtocolGuid,            &gFchSmmSwDispatch2Protocol},
                    {&gEfiSmmSwDispatch2ProtocolGuid,            &gEfiSmmSwDispatch2Protocol},
                    {&gFchSmmSxDispatch2ProtocolGuid,            &gFchSmmSxDispatch2Protocol},
                    {&gEfiSmmSxDispatch2ProtocolGuid,            &gEfiSmmSxDispatch2Protocol},
                    {&gFchSmmPwrBtnDispatch2ProtocolGuid,        &gFchSmmPwrBtnDispatch2Protocol},
                    {&gEfiSmmPowerButtonDispatch2ProtocolGuid,   &gEfiSmmPwrBtnDispatch2Protocol},
                    {&gFchSmmPeriodicalDispatch2ProtocolGuid,    &gFchSmmPeriodicalDispatch2Protocol},
                    {&gEfiSmmPeriodicTimerDispatch2ProtocolGuid, &gEfiSmmPeriodicalDispatch2Protocol},
                    {&gFchSmmUsbDispatch2ProtocolGuid,           &gFchSmmUsbDispatch2Protocol},
                    {&gEfiSmmUsbDispatch2ProtocolGuid,           &gEfiSmmUsbDispatch2Protocol},
                    {&gFchSmmGpiDispatch2ProtocolGuid,           &gFchSmmGpiDispatch2Protocol},
                    {&gEfiSmmGpiDispatch2ProtocolGuid,           &gEfiSmmGpiDispatch2Protocol},
                    {&gFchSmmIoTrapDispatch2ProtocolGuid,        &gFchSmmIoTrapDispatch2Protocol},
                    {&gEfiSmmIoTrapDispatch2ProtocolGuid,        &gEfiSmmIoTrapDispatch2Protocol},
                    {&gFchSmmMiscDispatchProtocolGuid,           &gFchSmmMiscDispatchProtocol},
                    };

FCH_PROTOCOL_LIST FchProtocolListRas[] = {
                    {&gFchSmmApuRasDispatchProtocolGuid,         &gFchSmmApuRasDispatchProtocol},
                    };

EFI_STATUS
EFIAPI
FchSmmDispatchHandler (
  IN       EFI_HANDLE                   SmmImageHandle,
  IN       CONST VOID                   *SmmEntryContext,
  IN OUT   VOID                         *CommunicationBuffer OPTIONAL,
  IN OUT   UINTN                        *SourceSize OPTIONAL
  );

VOID
FchSmmInitUsbSmiSysInfo (
  VOID
  );

VOID
ClearSmiStatus (
  VOID
  );

/*----------------------------------------------------------------------------------------*/
/**
 * Entry point of the AMD FCH SMM dispatcher driver
 * Example of dispatcher driver that handled IO TRAP requests only
 *
 * @param[in]     ImageHandle    Pointer to the firmware file system header
 * @param[in]     SystemTable    Pointer to System table
 *
 * @retval        EFI_SUCCESS    Module initialized successfully
 * @retval        EFI_ERROR      Initialization failed (see error for more details)
 */
/*----------------------------------------------------------------------------------------*/
EFI_STATUS
EFIAPI
FchSmmDispatcherEntry (
  IN       EFI_HANDLE         ImageHandle,
  IN       EFI_SYSTEM_TABLE   *SystemTable
  )
{
  EFI_STATUS                    Status;
  EFI_HANDLE                    DispatchHandle;
  EFI_HANDLE                    FchSmmDispatcherHandle;
  UINTN                         i;

  AGESA_TESTPOINT (TpFchSmmDispatcherEntry, NULL);

  // Init Golbal data
  gSmmNumberOfSocketEnabled = FabricTopologyGetNumberOfProcessorsPresent ();

  mEspiSmiOutB = PcdGet8 (PcdEspiSmiOutBVwEnable);

  Status = gSmst->SmmLocateProtocol (
                    &gEfiSmmCpuProtocolGuid,
                    NULL,
                    (VOID **)&mSmmCpuProtocol
                    );
  ASSERT_EFI_ERROR (Status);

  Status = gSmst->SmmAllocatePool (
                    EfiRuntimeServicesData,
                    sizeof (FCH_SMM_SW_NODE),
                    (VOID **)&HeadFchSmmSwNodePtr
                    );
  if (EFI_ERROR (Status)) {
    return Status;
  }
  ZeroMem (HeadFchSmmSwNodePtr, sizeof (FCH_SMM_SW_NODE));

  Status = gSmst->SmmAllocatePool (
                          EfiRuntimeServicesData,
                          sizeof (FCH_SMM_SX_NODE),
                          (VOID **)&HeadFchSmmSxNodePtr
                          );

  if (EFI_ERROR (Status)) {
    return Status;
  }
  ZeroMem (HeadFchSmmSxNodePtr, sizeof (FCH_SMM_SX_NODE));

  Status = gSmst->SmmAllocatePool (
                    EfiRuntimeServicesData,
                    sizeof (FCH_SMM_PWRBTN_NODE),
                    (VOID **)&HeadFchSmmPwrBtnNodePtr
                    );
  if (EFI_ERROR (Status)) {
    return Status;
  }
  ZeroMem (HeadFchSmmPwrBtnNodePtr, sizeof (FCH_SMM_PWRBTN_NODE));

  Status = gSmst->SmmAllocatePool (
                    EfiRuntimeServicesData,
                    sizeof (FCH_SMM_PERIODICAL_NODE),
                    (VOID **)&HeadFchSmmPeriodicalNodePtr
                    );
  if (EFI_ERROR (Status)) {
    return Status;
  }
  ZeroMem (HeadFchSmmPeriodicalNodePtr, sizeof (FCH_SMM_PERIODICAL_NODE));

  Status = gSmst->SmmAllocatePool (
                    EfiRuntimeServicesData,
                    sizeof (FCH_SMM_GPI_NODE),
                    (VOID **)&HeadFchSmmGpiNodePtr
                    );
  if (EFI_ERROR (Status)) {
    return Status;
  }
  ZeroMem (HeadFchSmmGpiNodePtr, sizeof (FCH_SMM_GPI_NODE));
  HeadFchSmmGpiNodePtr->Context.GpiNum = 0xffff;

  Status = gSmst->SmmAllocatePool (
                    EfiRuntimeServicesData,
                    sizeof (FCH_SMM_USB_NODE),
                    (VOID **)&HeadFchSmmUsbNodePtr
                    );
  if (EFI_ERROR (Status)) {
    return Status;
  }
  ZeroMem (HeadFchSmmUsbNodePtr, sizeof (FCH_SMM_USB_NODE));
  HeadFchSmmUsbNodePtr->Context.Order = 0xFF;

  Status = gSmst->SmmAllocatePool (
                    EfiRuntimeServicesData,
                    sizeof (FCH_SMM_MISC_NODE),
                    (VOID **)&HeadFchSmmMiscNodePtr
                    );
  if (EFI_ERROR (Status)) {
    return Status;
  }
  ZeroMem (HeadFchSmmMiscNodePtr, sizeof (FCH_SMM_MISC_NODE));

  Status = gSmst->SmmAllocatePool (
                    EfiRuntimeServicesData,
                    sizeof (FCH_SMM_SW_CONTEXT),
                    (VOID **)&EfiSmmSwContext
                    );
  if (EFI_ERROR (Status)) {
    return Status;
  }
  ZeroMem (EfiSmmSwContext, sizeof (FCH_SMM_SW_CONTEXT));

  Status = gSmst->SmmAllocatePool (
                    EfiRuntimeServicesData,
                    sizeof (FCH_SMM_COMMUNICATION_BUFFER),
                    (VOID **)&CommunicationBufferPtr
                    );
  if (EFI_ERROR (Status)) {
    return Status;
  }

  for (i = 0 ; i < sizeof (FchProtocolList) / sizeof (FCH_PROTOCOL_LIST); i++ ) {
    FchSmmDispatcherHandle =  NULL;
    Status = gSmst->SmmInstallProtocolInterface (
               &FchSmmDispatcherHandle,
               FchProtocolList[i].Guid,
               EFI_NATIVE_INTERFACE,
               FchProtocolList[i].Interface);
    if (EFI_ERROR (Status)) {
      return Status;
    }
  }

  if ( PcdGetBool (PcdAmdFchApuRasSmiSupport) ) {
    Status = gSmst->SmmAllocatePool (
                      EfiRuntimeServicesData,
                      sizeof (FCH_SMM_APURAS_NODE),
                      (VOID **)&HeadFchSmmApuRasNodePtr
                      );
    if (EFI_ERROR (Status)) {
      return Status;
    }
    ZeroMem (HeadFchSmmApuRasNodePtr, sizeof (FCH_SMM_APURAS_NODE));

    FchSmmDispatcherHandle =  NULL;
    Status = gSmst->SmmInstallProtocolInterface (
               &FchSmmDispatcherHandle,
               FchProtocolListRas[0].Guid,
               EFI_NATIVE_INTERFACE,
               FchProtocolListRas[0].Interface);
    if (EFI_ERROR (Status)) {
      return Status;
    }

    // Find handler for APU Hw Assertion bit
    for (i = 0; i < NumOfDispatcherTableEntry; i++ ) {
      if ((FchSmmDispatcherTable[i].StatusReg == FCH_SMI_REG84) && (FchSmmDispatcherTable[i].SmiStatusBit == ApuRasSmi)) {
        FchSmmDispatcherTable[i].SmiDispatcher = FchSmmApuRasDispatchHandler;
      }
    }
  }

  Status = gSmst->SmiHandlerRegister (
                    FchSmmDispatchHandler,
                    NULL,
                    &DispatchHandle
                    );

  if (EFI_ERROR (Status)) {
    return Status;
  }

  {
    UINT32  SmmDispatcherData32;
    //
    // Clear all SMI status bit
    //
    ClearSmiStatus ();

    //
    // Clear SmiEnB
    //
    SmmDispatcherData32 = ACPIMMIO32 (ACPI_MMIO_BASE + SMI_BASE + FCH_SMI_REG98);
    SmmDispatcherData32 &= ~(BIT31);
    //FWDEV-17123 SmmDispatcherData32 |= BIT28;
    ACPIMMIO32 (ACPI_MMIO_BASE + SMI_BASE + FCH_SMI_REG98) = SmmDispatcherData32;
  }

  FchSmmInitUsbSmiSysInfo ();

  AGESA_TESTPOINT (TpFchSmmDispatcherExit, NULL);
  return Status;
}


BOOLEAN
IsCpl3BlockIo(
  UINT16    Io
  )
{
  UINT8  Index;

  for (Index = 0; Index < NumOfCpl3BlockIoListEntry; Index++){
    if(Io == Cpl3BlockIoList[Index]){
      return TRUE;
    }
  }
  return FALSE;
}


VOID
SaveB2BRegisters (
  VOID
  )
{
  EFI_SMM_CPU_IO2_PROTOCOL   *SmmCpuIo;
  UINT8                      i;
  UINT32                     PciAddress;
  UINT16                     CodeSegment;

  SmmCpuIo = &gSmst->SmmIo;

  CodeSegment = AsmReadCs();

  for (i = 0; i < NumOfB2bIoListEntry; i++) {
    if (CodeSegment & (BIT0|BIT1)){
      if (TRUE == IsCpl3BlockIo(B2bIoList[i].ioPort)){
        continue;
      }
    }
    SmmCpuIo->Io.Read (SmmCpuIo, B2bIoList[i].ioWidth, B2bIoList[i].ioPort, 1, &B2bIoList[i].ioValue);
  }
  for (i = 0; i < NumOfSavePciListEntry; i++) {
    PciAddress = MAKE_SBDFO (0, SavePciList[i].Bus, SavePciList[i].Dev, SavePciList[i].Func, SavePciList[i].Offset);
    switch (SavePciList[i].DataWidth){
      case SMM_IO_UINT32:
        SavePciList[i].DataValue = PciRead32 (PciAddress);
        break;
      case SMM_IO_UINT16:
        SavePciList[i].DataValue = PciRead16 (PciAddress);
        break;
      case SMM_IO_UINT8:
        SavePciList[i].DataValue = PciRead8 (PciAddress);
        break;
      default:
        break;
    }
  }
}


VOID
RestoreB2BRegisters ( VOID )
{
  EFI_SMM_CPU_IO2_PROTOCOL   *SmmCpuIo;
  UINT8                      i;
  UINT32                     PciAddress;
  UINT16                     CodeSegment;

  SmmCpuIo = &gSmst->SmmIo;

  CodeSegment = AsmReadCs ();

  for (i = 0; i < NumOfSavePciListEntry; i++) {
    PciAddress = MAKE_SBDFO (0, SavePciList[i].Bus, SavePciList[i].Dev, SavePciList[i].Func, SavePciList[i].Offset);
    switch (SavePciList[i].DataWidth){
      case SMM_IO_UINT32:
        PciWrite32 (PciAddress, (UINT32)(SavePciList[i].DataValue));
        break;
      case SMM_IO_UINT16:
        PciWrite16 (PciAddress, (UINT16)(SavePciList[i].DataValue));
        break;
      case SMM_IO_UINT8:
        PciWrite8 (PciAddress, (UINT8)(SavePciList[i].DataValue));
        break;
      default:
        break;
    }
  }

  for (i = 0; i < NumOfB2bIoListEntry; i++) {
    if (CodeSegment & (BIT0|BIT1)){
      if (TRUE == IsCpl3BlockIo(B2bIoList[i].ioPort)){
        continue;
      }
    }
    SmmCpuIo->Io.Write (SmmCpuIo, B2bIoList[i].ioWidth, B2bIoList[i].ioPort, 1, &B2bIoList[i].ioValue);
  }
}


VOID
SendEspiSmiOutB_Assert ( VOID )
{
  if (mEspiSmiOutB & BIT0) {
    // eSPI0
    FchEspiSendSmiOutbVW (FCH_ESPI0_BASE_ADDRESS, 0x20);
    DEBUG ((DEBUG_INFO, "[FchSmmDispatcher] Sent SMIOUT Assertion VW on eSPI0!\n"));
  }

  if (mEspiSmiOutB & BIT1) {
    // eSPI1
    FchEspiSendSmiOutbVW (FCH_ESPI1_BASE_ADDRESS, 0x20);
    DEBUG ((DEBUG_INFO, "[FchSmmDispatcher] Sent SMIOUT Assertion VW on eSPI1!\n"));
  }
}

VOID
SendEspiSmiOutB_DeAssert ( VOID )
{
  if (mEspiSmiOutB & BIT0) {
    // eSPI0
    FchEspiSendSmiOutbVW (FCH_ESPI0_BASE_ADDRESS, 0x22);
    DEBUG ((DEBUG_INFO, "[FchSmmDispatcher] Sent SMIOUT De-Assertion VW on eSPI0!\n"));
  }

  if (mEspiSmiOutB & BIT1) {
    // eSPI1
    FchEspiSendSmiOutbVW (FCH_ESPI1_BASE_ADDRESS, 0x22);
    DEBUG ((DEBUG_INFO, "[FchSmmDispatcher] Sent SMIOUT De-Assertion VW on eSPI1!\n"));
  }
}

VOID
ClearSmiStatus (
  VOID
  )
{
  UINT32  SmiStatusReg;
  UINT32  SmiStatusData;

  //
  // Clear SmiStatus0 - SmiStatus4
  //
  for (SmiStatusReg = FCH_SMI_REG80; SmiStatusReg <= FCH_SMI_REG90; SmiStatusReg = SmiStatusReg + 4 ) {
    SmiStatusData = ACPIMMIO32 (ACPI_MMIO_BASE + SMI_BASE + SmiStatusReg);
    ACPIMMIO32 (ACPI_MMIO_BASE + SMI_BASE + SmiStatusReg) = SmiStatusData;
  }

  //
  // Clear SmiSciStatus
  //
  SmiStatusData = ACPIMMIO32 (ACPI_MMIO_BASE + SMI_BASE + FCH_SMI_REG10);
  ACPIMMIO32 (ACPI_MMIO_BASE + SMI_BASE + FCH_SMI_REG10) = SmiStatusData;
}

/**
 * @brief Get Socket 1 Bus Number.
 *
 * @returns UINTN        2nd socket bus number
 */
UINTN
FchSmmGetSocket1Bus (
  VOID
  )
{
  EFI_STATUS                              Status;
  UINTN                                   BusNumberBase;
  AMD_FABRIC_TOPOLOGY_SERVICES2_PROTOCOL  *FabricTopology;
  BOOLEAN                                 HasFchDevice;
  UINTN                                   i;
  UINTN                                   NumberOfRootBridges;

  Status          = EFI_SUCCESS;
  FabricTopology  = NULL;
  BusNumberBase   = 0;

  Status = gSmst->SmmLocateProtocol (
                    &gAmdFabricTopologyServices2SmmProtocolGuid,
                    NULL,
                    (VOID **) &FabricTopology
                    );
  if (Status == EFI_SUCCESS) {
    Status = FabricTopology->GetDieInfo (
                               FabricTopology,
                               FCH_SECOND_SOCKET,
                               FCH_SECOND_SOCKET_DIE,
                               &NumberOfRootBridges,
                               NULL,
                               NULL
                               );
    if (Status == EFI_SUCCESS) {
      for (i = 0; i < NumberOfRootBridges; i++) {
        Status = FabricTopology->GetRootBridgeInfo (
                                   FabricTopology,
                                   FCH_SECOND_SOCKET,
                                   FCH_SECOND_SOCKET_DIE,
                                   i,
                                   NULL,
                                   &BusNumberBase,
                                   NULL,
                                   NULL,
                                   &HasFchDevice,
                                   NULL
                                   );
        if (!EFI_ERROR (Status) && HasFchDevice) {
          DEBUG ((DEBUG_INFO, "[%a] Multi FCH SMM Get 2nd Socket Bus Number = 0x%x\n", __FUNCTION__, BusNumberBase));
          return BusNumberBase;
        }
      }
    }
  }
  ASSERT (FALSE);
  return 0;
}

/*----------------------------------------------------------------------------------------*/
/**
 * FCH SMM dispatcher handler
 *
 *
 * @param[in]       SmmImageHandle        Image Handle
 * @param[in]       SmmEntryContext         (see PI 1.2 for more details)
 * @param[in, out]   OPTIONAL CommunicationBuffer   Communication Buffer (see PI 1.1 for more details)
 * @param[in, out]   OPTIONAL SourceSize            Buffer size (see PI 1.1 for more details)

 * @retval          EFI_SUCCESS           SMI handled by dispatcher
 * @retval          EFI_UNSUPPORTED       SMI not supported by dispcther
 */
/*----------------------------------------------------------------------------------------*/
EFI_STATUS
EFIAPI
FchSmmDispatchHandler (
  IN       EFI_HANDLE                   SmmImageHandle,
  IN       CONST VOID                   *SmmEntryContext,
  IN OUT   VOID                         *CommunicationBuffer OPTIONAL,
  IN OUT   UINTN                        *SourceSize OPTIONAL
  )
{
  UINT8        SmmDispatcherIndex;
  UINT32       SmiStatusData;
//  UINT32       SmiReg88StatusData;
  //UINT32       UsbSmiEnableRegister;
  //UINT32       UsbSmiEnableStatus;
  UINT32       EosStatus;
  EFI_STATUS   Status;

  if ( CommunicationBuffer != NULL
    && SourceSize != NULL
    && !SmmIsBufferOutsideSmmValid ((UINTN)CommunicationBuffer, (UINT64)(*SourceSize)) )
  {
    DEBUG ((EFI_D_ERROR, "[%a]SMM communication data buffer in SMRAM or overflow!\n", __FUNCTION__));
    return (EFI_INVALID_PARAMETER);
  }

  Status = EFI_WARN_INTERRUPT_SOURCE_PENDING; //Updated to be compliant with UDK2010.SR1.UP1
  SaveB2BRegisters ();
  SendEspiSmiOutB_Assert ();
//  SmiReg88StatusData = ACPIMMIO32 (ACPI_MMIO_BASE + SMI_BASE + FCH_SMI_REG88) & SmiCmdPort;
  //FchSmnRead (0, 0x16D80118, &UsbSmiEnableRegister, NULL);
  //UsbSmiEnableStatus = UsbSmiEnableRegister & BIT8;
  do {
    /*
    if (UsbSmiEnableStatus) {
      ACPIMMIO8 (ACPI_MMIO_BASE + PMIO_BASE + FCH_PMIOA_REGED) &= ~(BIT4);
      FchSmnRW (0, 0x16D80118, 0xfffffeff, 0x000, NULL);
    }
    */
    for (SmmDispatcherIndex = 0; SmmDispatcherIndex < NumOfDispatcherTableEntry; SmmDispatcherIndex++ ) {
      SmiStatusData = ACPIMMIO32 (ACPI_MMIO_BASE + SMI_BASE + FchSmmDispatcherTable[SmmDispatcherIndex].StatusReg);
      if (( SmiStatusData &= FchSmmDispatcherTable[SmmDispatcherIndex].SmiStatusBit) != 0) {
        CommunicationBufferPtr->SmiStatusReg = FchSmmDispatcherTable[SmmDispatcherIndex].StatusReg;
        CommunicationBufferPtr->SmiStatusBit = SmiStatusData;
        CommunicationBuffer = (VOID *) CommunicationBufferPtr;
        Status = FchSmmDispatcherTable[SmmDispatcherIndex].SmiDispatcher (SmmImageHandle, CommunicationBuffer, SourceSize);
        if (Status != EFI_SUCCESS) {
          Status = EFI_WARN_INTERRUPT_SOURCE_PENDING;
        }
        SmmDispatcherIndex = 0;
      }
    }
    /*
    if (UsbSmiEnableStatus) {
      ACPIMMIO8 (ACPI_MMIO_BASE + PMIO_BASE + FCH_PMIOA_REGED) |= (BIT4);
      FchSmnRW (0, 0x16D80118, 0xfffffeff, 0x100, NULL);
    }
    */

    //
    // Clear all SMI status bit to cover un-registered events
    //
    ClearSmiStatus ();
    ACPIMMIO32 (ACPI_MMIO_BASE + SMI_BASE + FCH_SMI_REG98) |= Eos;
    EosStatus = ACPIMMIO32 (ACPI_MMIO_BASE + SMI_BASE + FCH_SMI_REG98) & Eos;
  //} while ((EosStatus != Eos) || (ACPIMMIO32 (ACPI_MMIO_BASE + SMI_BASE + FCH_SMI_REG84) & UsbSmi));
  } while ((EosStatus != Eos));
  SendEspiSmiOutB_DeAssert ();
  RestoreB2BRegisters ();
  return  Status;
}

//temp fix..
CONST VOID* IdsDebugPrint[] =
{
  NULL
};

VOID
FchSmmInitUsbSmiSysInfo (
  VOID
  )
{
  if (PcdGetBool (PcdXhci0Enable)) {
    // XHCI0 under Socket[0].NBIO[1].IOHC[0]
    FchUsbSmiSysInfo.Socket0NbioSmiEn |= BIT3;
  }

  if (PcdGetBool (PcdXhci1Enable)) {
    // XHCI1 under Socket[0].NBIO[0].IOHC[0]
    FchUsbSmiSysInfo.Socket0NbioSmiEn |= BIT2;
  }

  // If there is 2 sockets installed and enabled
  if ( gSmmNumberOfSocketEnabled == FCH_MAX_SOCKET ) {
    if (PcdGetBool (PcdXhci2Enable)) {
      //XHCI2 on Socket1 NBIO[1]
      FchUsbSmiSysInfo.Socket1En = TRUE;
      FchUsbSmiSysInfo.Socket1NbioSmiEn |= BIT3;
    }

    if (PcdGetBool (PcdXhci3Enable)) {
      //XHCI3 on Socket1 NBIO[0]
      FchUsbSmiSysInfo.Socket1En = TRUE;
      FchUsbSmiSysInfo.Socket1NbioSmiEn |= BIT2;
    }

    if (FchUsbSmiSysInfo.Socket1En) {
      FchUsbSmiSysInfo.Socket1Bus = (UINT32) FchSmmGetSocket1Bus ();
      DEBUG ((DEBUG_INFO, "[%a] 2nd socket bus number 0x%x.\n", __FUNCTION__, FchUsbSmiSysInfo.Socket1Bus));
      ASSERT (FchUsbSmiSysInfo.Socket1Bus != 0);
    }
  } else {
    FchUsbSmiSysInfo.Socket1En = FALSE;
    FchUsbSmiSysInfo.Socket1NbioSmiEn = 0;
  }

  DEBUG ((
    DEBUG_INFO,
    "[%a] Update USB SMI SysInfo:\n"
    " Socket0NbioSmiEn = 0x%x\n"
    " Socket1NbioSmiEn = 0x%x\n"
    " Socket1En        = 0x%x\n"
    " Socket1Bus       = 0x%x\n",
    __FUNCTION__,
    FchUsbSmiSysInfo.Socket0NbioSmiEn,
    FchUsbSmiSysInfo.Socket1NbioSmiEn,
    FchUsbSmiSysInfo.Socket1En,
    FchUsbSmiSysInfo.Socket1Bus
    ));
}


