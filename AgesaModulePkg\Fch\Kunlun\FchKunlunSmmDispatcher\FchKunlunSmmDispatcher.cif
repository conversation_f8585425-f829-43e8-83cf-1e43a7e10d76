<component>
    name = "FchKunlunSmmDispatcher"
    category = ModulePart
    LocalRoot = "AgesaModulePkg\Fch\Kunlun\FchKunlunSmmDispatcher\"
    RefName = "AgesaModulePkg.Fch.Kunlun.FchKunlunSmmDispatcher"
[INF]
"FchSmmDiagDispatcher.inf"
"FchSmmDispatcher.inf"
[files]
"FchSmmData.c"
"FchSmmDiagDispatcher.c"
"FchSmmDiagDispatcher.h"
"FchSmmDispatcher.c"
"FchSmmDispatcher.h"
[parts]
"AgesaModulePkg.Fch.Kunlun.FchKunlunSmmDispatcher.FchSmmApuRasDispatcher"
"AgesaModulePkg.Fch.Kunlun.FchKunlunSmmDispatcher.FchSmmGpiDispatcher"
"AgesaModulePkg.Fch.Kunlun.FchKunlunSmmDispatcher.FchSmmIoTrapDispatcher"
"AgesaModulePkg.Fch.Kunlun.FchKunlunSmmDispatcher.FchSmmMiscDispatcher"
"AgesaModulePkg.Fch.Kunlun.FchKunlunSmmDispatcher.FchSmmPeriodicalDispatcher"
"AgesaModulePkg.Fch.Kunlun.FchKunlunSmmDispatcher.FchSmmPwrBtnDispatcher"
"AgesaModulePkg.Fch.Kunlun.FchKunlunSmmDispatcher.FchSmmSwDispatcher"
"AgesaModulePkg.Fch.Kunlun.FchKunlunSmmDispatcher.FchSmmSxDispatcher"
"AgesaModulePkg.Fch.Kunlun.FchKunlunSmmDispatcher.FchSmmUsbDispatcher"
<endComponent>
