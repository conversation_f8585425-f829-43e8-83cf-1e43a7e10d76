#;*****************************************************************************
#;
#; Copyright (C) 2008-2024 Advanced Micro Devices, Inc. All rights reserved.
#;
#;******************************************************************************
[Defines]
  INF_VERSION                    = 0x00010006
  BASE_NAME                      = AmdPspApobLib
  FILE_GUID                      = 714A2ED3-36AC-48F9-AB14-B1E358A92D1F
  MODULE_TYPE                    = BASE
  VERSION_STRING                 = 1.0
  LIBRARY_CLASS                  = AmdPspApobLib

[Sources.common]
  AmdPspApobLib.c

[Packages]
  MdePkg/MdePkg.dec
  AgesaPkg/AgesaPkg.dec
  AgesaModulePkg/AgesaCommonModulePkg.dec
  AgesaModulePkg/AgesaModulePspPkg.dec

[LibraryClasses]
  BaseMemoryLib
  HobLib
  PcdLib
  MemoryAllocationLib
  AmdDirectoryBaseLib
  AmdSocBaseLib

[Guids]
  gAmdPspApobHobGuid         #Consume

[Protocols]


[Ppis]

[Pcd]
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdPspApobUseSpiMmioAddress


