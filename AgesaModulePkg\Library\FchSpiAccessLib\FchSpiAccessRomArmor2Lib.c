/*
*****************************************************************************
*
 * Copyright (C) 2008-2024 Advanced Micro Devices, Inc. All rights reserved.
 *
*******************************************************************************
*/

/*----------------------------------------------------------------------------------------
 *                             M O D U L E S    U S E D
 *----------------------------------------------------------------------------------------
 */
#include <Filecode.h>
#include <Library/IoLib.h>
#include <Library/BaseMemoryLib.h>
#include <Library/FchSpiAccessLib.h>
#include <Library/DebugLib.h>
#include <Library/AmdPspFlashAccLib.h>
#include "FchRegistersCommon.h"

/*----------------------------------------------------------------------------------------
 *                   D E F I N I T I O N S    A N D    M A C R O S
 *----------------------------------------------------------------------------------------
 */
#define FILECODE LIBRARY_FCHSPIACCESSLIB_FCHSPIACCESSROMARMOR2LIB_FILECODE

#define IS_SPI_ROM2_OFFSET(a) (((a) < 0x1000000) ? TRUE : FALSE)
#define IS_IN_SPI_ROM2_WINDOW(a) ((((a) & ~(0xFFFFFF)) == 0xFF000000) ? TRUE : FALSE)
#define IS_SPI_ROM3_OFFSET(a) (((a) < 0x4000000) ? TRUE : FALSE)
#define IS_IN_SPI_ROM3_WINDOW(a) ((((a) & ~(0x3FFFFFF)) == 0xFD00000000) ? TRUE : FALSE)
/*----------------------------------------------------------------------------------------
 *                  T Y P E D E F S     A N D     S T R U C T U R E S
 *----------------------------------------------------------------------------------------
 */

/*----------------------------------------------------------------------------------------
 *           P R O T O T Y P E S     O F     L O C A L     F U N C T I O N S
 *----------------------------------------------------------------------------------------
 */

/**
* CheckSpiConfigSpaceValid - Check SPI configure space is valid
*
*
* @param VOID
*
* @retval BOOLEAN - SPI configuration space is valid or not
*/
BOOLEAN
CheckSpiConfigSpaceValid(
)
{
  UINT32    Value32;

  Value32 = MmioRead32 ((UINTN)(FCH_SPI_BASE_ADDRESS + FCH_SPI_MMIO_REG4C_SPISTATUS));
  if (Value32 == 0xFFFFFFFF){
    return FALSE;
  } else {
    return TRUE;
  }
}

/**
* DetectRom2Page - detect the ROM page decoded into ROM2 decode range (by default ROM2 16MB window (0xFF000000))
*
*
* @param VOID
*
* @retval UINT32 - the ROM page address (bit 24/25)
*/
UINT32
DetectRom2Page(
)
{
  UINT32    Value32;
  UINT8     SpiRomPageXor;
  UINT32    RomPage;

  RomPage = FCH_ROM_START_ADDRESS_2 >> 24;

  Value32 = MmioRead32 ((UINTN)(FCH_SPI_BASE_ADDRESS + FCH_SPI_MMIO_REG30));
  if (Value32 & FCH_SPI_R2MSK24){
    RomPage &= ~FCH_SPI_R2VAL24;
    RomPage |= Value32 & FCH_SPI_R2VAL24;
  }
  if (Value32 & FCH_SPI_R2MSK25){
    RomPage &= ~FCH_SPI_R2VAL25;
    RomPage |= Value32 & FCH_SPI_R2VAL25;
  }

  Value32 = MmioRead32 ((UINTN)(FCH_SPI_BASE_ADDRESS + FCH_SPI_MMIO_REG5C_Addr32_Ctrl3));

  SpiRomPageXor = (UINT8)(Value32 & FCH_SPI_SPIROM_PAGE_MASK);
  RomPage ^= SpiRomPageXor;

  return (RomPage << 24);
}

/*----------------------------------------------------------------------------------------
 *                          E X P O R T E D    F U N C T I O N S
 *----------------------------------------------------------------------------------------
 */

/**
 * IsRom2Decoded - check Offset
 *
 *
 * @param[in] Offset - The offset of ROM
 *
 * @retval BOOLEAN Is in SPI ROM2 decode window or not
 */
BOOLEAN
IsRom2Decoded (
  IN       UINT64        Offset
  )
{
  UINT32                    Bank;
  BOOLEAN                   ConfigSpaceValid;

  ConfigSpaceValid = CheckSpiConfigSpaceValid();

  if (ConfigSpaceValid == FALSE) {
    return FALSE;
  }

  Bank = DetectRom2Page();

#ifdef FCH_SPI_ROM2_ACCESS_64MB_SUPPORT
  if ((Bank & (BIT24 | BIT25)) != (Offset & (BIT24 | BIT25))){
#else
  if ((Bank & BIT24) != (Offset & BIT24)){
#endif
    return FALSE;
  }else {
    return TRUE;
  }
}

/**
 * FchSpiRomRead - Read the SPI ROM
 *
 *
 * @param[in] Offset - The offset of ROM or ROM2 address
 * @param[out]*Data - Buffer address to save the data
 * @param[in] Length - The number of byte to read
 *
 * @retval BOOLEAN success read or not
 */
BOOLEAN
FchSpiRomRead (
    IN      UINT32            Offset,
    OUT     UINT8             *Data,
    IN      UINT32            Length
  )
{
  return FchSpiRomReadEx(Offset, Data, Length);
}

/**
 * FchSpiRomReadEx - Extended function to read the SPI ROM
 *
 *
 * @param[in] Location - Host address in ROM2/ROM3 window to SPI ROM
 *                     - Offset of host SPI ROM3 address window when ROM Armor 2 disabled
 *                     - Offset of physical ROM when ROM Armor 2 enabled
 * @param[out]*Data - Buffer address to save the data
 * @param[in] Length - The number of byte to read
 *
 * @retval BOOLEAN success read or not
 */
BOOLEAN
FchSpiRomReadEx (
    IN      UINTN             Location,
    OUT     UINT8             *Data,
    IN      UINT32            Length
  )
{
  EFI_STATUS        Status;
  UINTN             NumByte;
  UINTN             FlasAddress;

  if (sizeof(UINTN) != sizeof(UINT64)){ //not in 64 bit mode
    return FALSE;
  }

  if ((Data == NULL) || (Length == 0)){
   return FALSE;
  }

  if( IS_SPI_ROM3_OFFSET (Location)) {  // Offset of ROM
    if((Location >= FCH_ROM_SIZE_64M) || (Length > FCH_ROM_SIZE_64M) || ((Location + Length) > FCH_ROM_SIZE_64M)){
    return FALSE;
    }
    FlasAddress = FCH_ROM_START_ADDRESS_3 + Location;
  } else if(IS_IN_SPI_ROM2_WINDOW(Location) || IS_IN_SPI_ROM3_WINDOW(Location)){ // ROM2/ROM3 address
    FlasAddress = Location;
  } else{
    return FALSE;
  }

  NumByte = Length;
  Status = PspReadFlash (Location, &NumByte, Data);
  if (Status == EFI_NOT_FOUND) {
    CopyMem (Data, (VOID *)FlasAddress, NumByte);
  }

  return TRUE;
}

