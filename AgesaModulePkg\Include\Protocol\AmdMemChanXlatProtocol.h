/*****************************************************************************
 * Copyright (C) 2024 Advanced Micro Devices, Inc. All rights reserved.
 *
*****************************************************************************
*/
/* $NoKeywords:$ */
/**
 * @file
 *
 * AmdMemChanXlatProtocol.h
 *
 * AMD Memory Translate Services Protocol prototype definition
 *
 * @xrefitem bom "File Content Label" "Release Content"
 * @e project:      AGESA
 * @e sub-project:  Protocol
 * @e \$Revision:  $   @e \$Date:  $
 *
 */

#ifndef _AMD_MEMCHANXLAT_SERVICES_PROTOCOL_H_
#define _AMD_MEMCHANXLAT_SERVICES_PROTOCOL_H_

#pragma pack (push, 1)

/*----------------------------------------------------------------------------------------
 *                    T Y P E D E F S     A N D     S T R U C T U R E S
 *----------------------------------------------------------------------------------------
 */

/// Amd Memory Channel Translation Table.
typedef struct _CHANNEL_TO_UMC_XLAT {
  UINT8   RequestedChannelId;   ///< Requested Channel ID
  UINT8   TranslatedUmcId;      ///< Translated Umc ID
} CHANNEL_TO_UMC_XLAT;

/*----------------------------------------------------------------------------------------
 *                 D E F I N I T I O N S     A N D     M A C R O S
 *----------------------------------------------------------------------------------------
 */

#define AMD_MEMCHANXLAT_SERVICES_PROTOCOL_REVISION   0x01

/// Forward declaration for AMD_MEMCHANXLAT_SERVICES_PROTOCOL.
typedef struct _AMD_MEMCHANXLAT_SERVICES_PROTOCOL AMD_MEMCHANXLAT_SERVICES_PROTOCOL;

/**
 * @brief Translate the UMC Id depending upon the UMC translation table.
 *
 * @details Translate the UMC Id depending upon the UMC translation table.
 *
 * @param[in]        This                 - A pointer to the MEM_XLAT_SERVICES_PROTOCOL instance.
 * @param[in]        RequestedChannelId   - The requested channel Id - This is channel number on
 *                                          the platform board. i.e.
 *                                          Channel A  - Channel 0 on board
 *                                          Channel B  - Channel 1 on board.
 * @param[out]       *TranslatedUmcId     - Pointer to the translated Id. This is UMC number from SOC.
 * @retval           EFI_STATUS           - 0: Success, NonZero: EFI_NO_MAPPING.
 */
typedef
EFI_STATUS
(EFIAPI *AMD_MEMXLAT_SERVICES_GET_UMC_INFO) (
  IN     AMD_MEMCHANXLAT_SERVICES_PROTOCOL *This,
  IN     UINT8                             RequestedChannelId,
     OUT UINT8                             *TranslatedUmcId
  );

struct _AMD_MEMCHANXLAT_SERVICES_PROTOCOL {
  UINTN                             Revision;                  ///< Revision Number.
  AMD_MEMXLAT_SERVICES_GET_UMC_INFO GetMemUmcInfo;             ///< Get Memory UMC information
};

/// GUID for Memory Translate Services Protocol.
extern EFI_GUID gAmdMemChanXLatProtocolGuid;

#pragma pack (pop)

#endif // _AMD_MEMCHANXLAT_SERVICES_PROTOCOL_H_
