/*****************************************************************************
 * Copyright (C) 2008-2024 Advanced Micro Devices, Inc. All rights reserved.
 *
*****************************************************************************
*/
/* $NoKeywords:$ */
/**
 * @file
 *
 * Config Fch ESPI controller
 *
 * Init ESPI features.
 *
 * @xrefitem bom "File Content Label" "Release Content"
 * @e project:     AGESA
 * @e sub-project: FCH
 * @e \$Revision: 312667 $   @e \$Date: 2015-02-09 17:39:22 -0800 (Mon, 09 Feb 2015) $
 *
 */
#include "FchPlatform.h"
#include "Filecode.h"
#define FILECODE FCH_KUNLUN_FCHKUNLUNCORE_KUNLUN_KLESPI_KLESPIENV_FILECODE

/**
 * FchInitEnvEspi - Config ESPI controller before PCI emulation
 *
 *
 *
 * @param[in] FchDataPtr Fch configuration structure pointer.
 *
 */
VOID
FchInitEnvEspi (
  IN  VOID     *FchDataPtr
  )
{
}




