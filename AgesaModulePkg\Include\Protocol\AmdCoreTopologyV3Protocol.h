/*****************************************************************************
 * Copyright (C) 2008-2024 Advanced Micro Devices, Inc. All rights reserved.
 *
*****************************************************************************
*/
#ifndef _AMD_CORE_TOPOLOGY_SERVICES_V3_PROTOCOL_H_
#define _AMD_CORE_TOPOLOGY_SERVICES_V3_PROTOCOL_H_


///
/// Forward declaration for the AMD_CORE_TOPOLOGY_SERVICES_V3_PROTOCOL.
///
typedef struct _AMD_CORE_TOPOLOGY_SERVICES_V3_PROTOCOL AMD_CORE_TOPOLOGY_SERVICES_V3_PROTOCOL;

/**
 * This service retrieves information about the layout of the cores on the given die.
 * It can be used as compatible function of CoreTopologyServicesV2 GetCoreTopology().
 * But it will return maximum number of Ccd/Complex/Core/Thread.
 *
 * @param[in]  This                           A pointer to the
 *                                            AMD_CORE_TOPOLOGY_SERVICES_V3_PROTOCOL instance.
 * @param[in]  Socket                         Zero-based socket number to check.
 * @param[in]  Die                            The target die's identifier within Socket.
 * @param[out] NumberOfCcds                   Pointer to the maxinum number of enabled CCDs on
 *                                            the given socket / die.
 * @param[out] NumberOfComplexes              Pointer to the maxinum number of enabled complexes on
 *                                            the given socket / die.
 * @param[out] NumberOfCores                  Pointer to the maxinum number of enabled cores per
 *                                            complex on the given socket / die.
 * @param[out] NumberOfThreads                Pointer to the maxinum number of enabled threads per
 *                                            core on the given socket / die.
 *
 * @retval EFI_SUCCESS                        The core topology information was successfully retrieved.
 * @retval EFI_INVALID_PARAMETER              Socket is non-existent.
 * @retval EFI_INVALID_PARAMETER              Die is non-existent.
 * @retval EFI_INVALID_PARAMETER              All output parameter pointers are NULL.
 *
 **/
typedef
EFI_STATUS
(EFIAPI *AMD_CORE_TOPOLOGY_SERVICES_GET_CORE_TOPOLOGY_MAX_ON_DIE_V3) (
  IN       AMD_CORE_TOPOLOGY_SERVICES_V3_PROTOCOL  *This,
  IN       UINTN                                   Socket,
  IN       UINTN                                   Die,
     OUT   UINTN                                   *MaxNumberOfCcds,
     OUT   UINTN                                   *MaxNumberOfComplexes,
     OUT   UINTN                                   *MaxNumberOfCores,
     OUT   UINTN                                   *MaxNumberOfThreads
  );

/**
 * Get enabled thread count of current system.
 *
 * @param[in]  This                           A pointer to the
 *                                            AMD_CORE_TOPOLOGY_SERVICES_V3_PROTOCOL instance.
 * @param[out] NumOfEnabledThread             Pointer to the number of threads.
 *
 * @retval EFI_SUCCESS                       The core topology information was successfully retrieved.
 * @retval EFI_INVALID_PARAMETER             NumOfSocket pointers to NULL.
 *
 **/
typedef
EFI_STATUS
(EFIAPI *AMD_CORE_TOPOLOGY_SERVICES_GET_ENABLED_THREAD_ON_SYSTEM_V3) (
  IN       AMD_CORE_TOPOLOGY_SERVICES_V3_PROTOCOL  *This,
     OUT   UINTN                                   *NumOfEnabledThread
  );

/**
 * Get socket count of current system.
 *
 * @param[in]  This                           A pointer to the
 *                                            AMD_CORE_TOPOLOGY_SERVICES_V3_PROTOCOL instance.
 * @param[out] NumOfSocket                    Pointer to the number of sockets.
 *
 * @retval EFI_SUCCESS                       The core topology information was successfully retrieved.
 * @retval EFI_INVALID_PARAMETER             NumOfSocket pointers to NULL.
 *
 **/
typedef
EFI_STATUS
(EFIAPI *AMD_CORE_TOPOLOGY_SERVICES_GET_SOCKET_COUNT_ON_SYSTEM_V3) (
  IN       AMD_CORE_TOPOLOGY_SERVICES_V3_PROTOCOL  *This,
     OUT   UINTN                                   *NumOfSocket
  );

/**
 * Get die count for given socket.
 *
 * @param[in]  This                           A pointer to the
 *                                            AMD_CORE_TOPOLOGY_SERVICES_V3_PROTOCOL instance.
 * @param[in]  Socket                         Zero-based socket number to check.
 * @param[out] NumOfDie                       Pointer to the number of enabled Dies on
 *                                            the given socket.
 *
 * @retval EFI_SUCCESS                       The core topology information was successfully retrieved.
 * @retval EFI_INVALID_PARAMETER             Socket is non-existent.
 * @retval EFI_INVALID_PARAMETER             NumOfSocket pointers to NULL.
 *
 **/
typedef
EFI_STATUS
(EFIAPI *AMD_CORE_TOPOLOGY_SERVICES_GET_DIE_COUNT_ON_SOCKET_V3) (
  IN       AMD_CORE_TOPOLOGY_SERVICES_V3_PROTOCOL  *This,
  IN       UINTN                                   Socket,
     OUT   UINTN                                   *NumOfDie
  );

/**
 * Get ccd count for given die.
 *
 * @param[in]  This                           A pointer to the
 *                                            AMD_CORE_TOPOLOGY_SERVICES_V3_PROTOCOL instance.
 * @param[in]  Socket                         Zero-based socket number to check.
 * @param[in]  Die                            The target die's identifier within Socket.
 * @param[out] NumberOfCcds                   Pointer to the number of enabled CCDs on
 *                                            the given socket / die.
 *
 * @retval EFI_SUCCESS                       The core topology information was successfully retrieved.
 * @retval EFI_INVALID_PARAMETER             Socket or Die is non-existent.
 * @retval EFI_INVALID_PARAMETER             NumberOfCcds pointers to NULL.
 *
 **/
typedef
EFI_STATUS
(EFIAPI *AMD_CORE_TOPOLOGY_SERVICES_GET_CCD_COUNT_ON_DIE_V3) (
  IN       AMD_CORE_TOPOLOGY_SERVICES_V3_PROTOCOL  *This,
  IN       UINTN                                   Socket,
  IN       UINTN                                   Die,
     OUT   UINTN                                   *NumberOfCcds
  );

/**
 * Get complex count for given ccd.
 *
 * @param[in]  This                           A pointer to the
 *                                            AMD_CORE_TOPOLOGY_SERVICES_V3_PROTOCOL instance.
 * @param[in]  Socket                         Zero-based socket number to check.
 * @param[in]  Die                            The target die's identifier within Socket.
 * @param[in]  Ccd                            The target CCD's identifier within die
 * @param[out] NumberOfComplexes              Pointer to the number of enabled complexes on
 *                                            the given socket / die.
 *
 * @retval EFI_SUCCESS                       The core topology information was successfully retrieved.
 * @retval EFI_INVALID_PARAMETER             Socket, Die or Ccd is non-existent.
 * @retval EFI_INVALID_PARAMETER             NumberOfComplexes pointers to NULL.
 *
 **/
typedef
EFI_STATUS
(EFIAPI *AMD_CORE_TOPOLOGY_SERVICES_GET_COMPLEX_COUNT_ON_CCD_V3) (
  IN       AMD_CORE_TOPOLOGY_SERVICES_V3_PROTOCOL  *This,
  IN       UINTN                                   Socket,
  IN       UINTN                                   Die,
  IN       UINTN                                   Ccd,
     OUT   UINTN                                   *NumberOfComplexes
  );

/**
 * Get core count for given complex.
 *
 * @param[in]  This                           A pointer to the
 *                                            AMD_CORE_TOPOLOGY_SERVICES_V3_PROTOCOL instance.
 * @param[in]  Socket                         Zero-based socket number to check.
 * @param[in]  Die                            The target die's identifier within Socket.
 * @param[in]  Ccd                            The target CCD's identifier within die
 * @param[in]  Complex                        The target Complex's identifier within ccd
 * @param[out] NumberOfCcds                   Pointer to the number of enabled CCDs on
 *                                            the given socket / die.
 *
 * @retval EFI_SUCCESS                       The core topology information was successfully retrieved.
 * @retval EFI_INVALID_PARAMETER             Socket, Die, CCD or Complex is non-existent.
 * @retval EFI_INVALID_PARAMETER             NumberOfCores pointers to NULL.
 *
 **/
typedef
EFI_STATUS
(EFIAPI *AMD_CORE_TOPOLOGY_SERVICES_GET_CORE_COUNT_ON_COMPLEX_V3) (
  IN       AMD_CORE_TOPOLOGY_SERVICES_V3_PROTOCOL  *This,
  IN       UINTN                                   Socket,
  IN       UINTN                                   Die,
  IN       UINTN                                   Ccd,
  IN       UINTN                                   Complex,
     OUT   UINTN                                   *NumberOfCores
  );

/**
 * Get thread count for given core.
 *
 * @param[in]  This                           A pointer to the
 *                                            AMD_CORE_TOPOLOGY_SERVICES_V3_PROTOCOL instance.
 * @param[in]  Socket                         Zero-based socket number to check.
 * @param[in]  Die                            The target die's identifier within socket.
 * @param[in]  Ccd                            The target CCD's identifier within die
 * @param[in]  Complex                        The target Complex's identifier within ccd
 * @param[in]  Core                           The target Core's identifier within core
 * @param[out] NumberOfThreads                Pointer to the number of enabled threads per
 *                                            core on the given socket / die.
 *
 * @retval EFI_SUCCESS                       The core topology information was successfully retrieved.
 * @retval EFI_INVALID_PARAMETER             Socket,Die,Ccd, Complex or Core is non-existent.
 * @retval EFI_INVALID_PARAMETER             NumberOfThreads pointers to NULL.
 *
 **/
typedef
EFI_STATUS
(EFIAPI *AMD_CORE_TOPOLOGY_SERVICES_GET_THREAD_COUNT_ON_CORE_V3) (
  IN       AMD_CORE_TOPOLOGY_SERVICES_V3_PROTOCOL  *This,
  IN       UINTN                                   Socket,
  IN       UINTN                                   Die,
  IN       UINTN                                   Ccd,
  IN       UINTN                                   Complex,
  IN       UINTN                                   Core,
     OUT   UINTN                                   *NumberOfThreads
  );

/**
 * Convert logical Socket/Die/CCD/Complex/Core number to physical number.
 *
 * @param[in]  This                           A pointer to the
 *                                            AMD_CORE_TOPOLOGY_SERVICES_V3_PROTOCOL instance.
 * @param[in,out]  Socket                     On input, caller provides logical socket number,
 *                                            On output, it is physical socket number.
 * @param[in,out]  Die                        On input, caller provides logical die number,
 *                                            On output, it is physical die number.
 * @param[in,out]  Ccd                        On input, caller provides logical CCD number,
 *                                            On output, it is physical CCD number.
 * @param[in,out]  Complex                    On input, caller provides logical complex number,
 *                                            On output, it is physical complex number.
 * @param[in,out]  Core                       On input, caller provides logical core number,
 *                                            On output, it is physical core number.
 *
 * @retval EFI_SUCCESS                        The conversion is successfully completed.
 * @retval EFI_UNSUPPORTED                    Do not support this function.
 * @retval EFI_INVALID_PARAMETER              Input Socket, Die, Ccd, Complex or Core is not non-existent.
 * @retval EFI_INVALID_PARAMETER              No required higher level parameter provided.
  *
 **/
typedef
EFI_STATUS
(EFIAPI *AMD_CORE_TOPOLOGY_SERVICES_LOGICAL_TO_PHYSICAL_LOCATION_V3) (
  IN       AMD_CORE_TOPOLOGY_SERVICES_V3_PROTOCOL  *This,
  IN OUT   UINTN                                   *Socket,
  IN OUT   UINTN                                   *Die,
  IN OUT   UINTN                                   *Ccd,
  IN OUT   UINTN                                   *Complex,
  IN OUT   UINTN                                   *Core
  );

/**
 * This service will start a core to fetch its first instructions from the reset
 * vector.  This service may only be called from the BSP.
 *
 * @param[in]  This                           A pointer to the
 *                                            AMD_CORE_TOPOLOGY_SERVICES_V3_PROTOCOL instance.
 * @param[in]  Socket                         Zero-based socket number of the target thread.
 * @param[in]  Die                            Zero-based die number within Socket of the target thread.
 * @param[in]  LogicalCcd                     Zero-based logical core complex die of the target thread.
 * @param[in]  LogicalComplex                 Zero-based logical complex number of the target thread.
 * @param[in]  LogicalCore                    Zero-based logical core number of the target thread.
 * @param[in]  LogicalThread                  Zero-based logical thread number of the target thread.
 *
 * @retval EFI_SUCCESS                       The thread was successfully launched.
 * @retval EFI_DEVICE_ERROR                  The thread has already been launched.
 * @retval EFI_INVALID_PARAMETER             Socket is non-existent.
 * @retval EFI_INVALID_PARAMETER             Die is non-existent.
 * @retval EFI_INVALID_PARAMETER             LogicalComplex is non-existent.
 * @retval EFI_INVALID_PARAMETER             LogicalThread is non-existent.
 *
 **/
typedef
EFI_STATUS
(EFIAPI *AMD_CORE_TOPOLOGY_SERVICES_LAUNCH_THREAD_V3) (
  IN       AMD_CORE_TOPOLOGY_SERVICES_V3_PROTOCOL  *This,
  IN       UINTN                                   Socket,
  IN       UINTN                                   Die,
  IN       UINTN                                   LogicalCcd,
  IN       UINTN                                   LogicalComplex,
  IN       UINTN                                   LogicalCore,
  IN       UINTN                                   LogicalThread
  );

///
/// When installed, the AMD Core Topology Services PROTOCOL produces a
/// collection of services that provide information on Core Topology.
///
struct _AMD_CORE_TOPOLOGY_SERVICES_V3_PROTOCOL {
  AMD_CORE_TOPOLOGY_SERVICES_GET_CORE_TOPOLOGY_MAX_ON_DIE_V3  GetCoreTopologyOnDieMax;     ///< Function to get the layout of the maximum cores on the given die.
  AMD_CORE_TOPOLOGY_SERVICES_GET_ENABLED_THREAD_ON_SYSTEM_V3  GetEnabledThreadOnSystem;    ///< Function to get enabled thread count on this system
  AMD_CORE_TOPOLOGY_SERVICES_GET_SOCKET_COUNT_ON_SYSTEM_V3    GetSocketCountOnSystem;      ///< Function to get socket count on this system
  AMD_CORE_TOPOLOGY_SERVICES_GET_DIE_COUNT_ON_SOCKET_V3       GetDieCountOnSocket;         ///< Function to get die count on specified socket
  AMD_CORE_TOPOLOGY_SERVICES_GET_CCD_COUNT_ON_DIE_V3          GetCcdCountOnDie;            ///< Function to get CCD count on specified die
  AMD_CORE_TOPOLOGY_SERVICES_GET_COMPLEX_COUNT_ON_CCD_V3      GetComplexCountOnCcd;        ///< Function to get complex count on specified CCD
  AMD_CORE_TOPOLOGY_SERVICES_GET_CORE_COUNT_ON_COMPLEX_V3     GetCoreCountOnComplex;       ///< Function to get core count on specified complex
  AMD_CORE_TOPOLOGY_SERVICES_GET_THREAD_COUNT_ON_CORE_V3      GetThreadCountOnCore;        ///< Function to get thread count on specified core
  AMD_CORE_TOPOLOGY_SERVICES_LOGICAL_TO_PHYSICAL_LOCATION_V3  LogicalToPhysicalLocation;   ///< Function to convert logical location to physical location
  AMD_CORE_TOPOLOGY_SERVICES_LAUNCH_THREAD_V3                 LaunchThread;                ///< Function to launch a thread.
};

///
/// Guid declaration for the AMD_CORE_TOPOLOGY_SERVICES_V3_PROTOCOL.
///
extern EFI_GUID gAmdCoreTopologyServicesV3ProtocolGuid;

#endif


