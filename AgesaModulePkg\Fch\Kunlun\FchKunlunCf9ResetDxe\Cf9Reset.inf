#;*****************************************************************************
#;
#; Copyright (C) 2008-2024 Advanced Micro Devices, Inc. All rights reserved.
#;
#;******************************************************************************

[defines]
  INF_VERSION                    = 0x00010005
  BASE_NAME                      = Cf9Reset
  FILE_GUID                      = a4e221cb-c1e7-4292-98b7-4b2a41fc254f
  MODULE_TYPE                    = DXE_RUNTIME_DRIVER
  VERSION_STRING                 = 1.0
  ENTRY_POINT                    = InitializeCf9Reset

[sources.common]
  Cf9Reset.c
  Cf9Reset.h

[LibraryClasses.common.DXE_DRIVER]
  BaseLib
  UefiLib
  PrintLib
  IoLib

[LibraryClasses]
  BaseMemoryLib
  UefiDriverEntryPoint
  UefiBootServicesTableLib
  DebugLib
  AmdBaseLib

[Guids]
  gEfiAmdAgesaSpecificWarmResetGuid

[Protocols]
  gEfiResetArchProtocolGuid      #PRODUCED


[Packages]
  MdePkg/MdePkg.dec
  AgesaPkg/AgesaPkg.dec
  AgesaModulePkg/AgesaModuleFchPkg.dec
  AgesaModulePkg/Fch/Kunlun/FchKunlun.dec

[Pcd]
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdFchFullHardReset

[Depex]
  gEfiCpuIo2ProtocolGuid  AND
  gAmdFchKunlunDepexProtocolGuid


