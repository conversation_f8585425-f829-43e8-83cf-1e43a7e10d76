<component>
    name = "FchSpiAccessLib"
    category = ModulePart
    LocalRoot = "AgesaModulePkg\Library\FchSpiAccessLib\"
    RefName = "AgesaModulePkg.Library.FchSpiAccessLib"
[INF]
"FchSpiAccessRom2Lib.inf"
"FchSpiAccessRom2V2Lib.inf"
"FchSpiAccessRom3Lib.inf"
"FchSpiAccessRom3V2Lib.inf"
"FchSpiAccessRomArmor2Lib.inf"
"FchSpiAccessRomArmor2V2Lib.inf"
"FchSpiAccessRomNull.inf"
"FchSpiAccessRomV2Null.inf"
"FchSpiAccessSmnLib.inf"
"FchSpiAccessSmnV2Lib.inf"
[files]
"FchSpiAccessCommon.c"
"FchSpiAccessRom2Lib.c"
"FchSpiAccessRom2V2Lib.c"
"FchSpiAccessRom3Lib.c"
"FchSpiAccessRom3V2Lib.c"
"FchSpiAccessRomArmor2Lib.c"
"FchSpiAccessRomArmor2V2Lib.c"
"FchSpiAccessRomNull.c"
"FchSpiAccessRomV2Null.c"
"FchSpiAccessSmnLib.c"
"FchSpiAccessSmnV2Lib.c"
<endComponent>
