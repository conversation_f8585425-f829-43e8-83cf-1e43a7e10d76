/*****************************************************************************
 *
 * Copyright (C) 2008-2024 Advanced Micro Devices, Inc. All rights reserved.
 *
 *******************************************************************************
 */

/*----------------------------------------------------------------------------------------
 *                             M O D U L E S    U S E D
 *----------------------------------------------------------------------------------------
 */

#include <DwI3cRegs.h>
#include <Pi/PiPeiCis.h>
#include <Ppi/FabricTopologyServices2Ppi.h>
#include <Library/DebugLib.h>
#include <Library/FchI3cLib.h>
#include <Library/FchPeiLib.h>
#include <Library/FchBaseLib.h>
#include <Library/FchSocLib.h>
#include <Library/BaseMemoryLib.h>
#include <Library/BaseFabricTopologyLib.h>
#include "I3cMasterPei.h"

/*----------------------------------------------------------------------------------------
 *                   D E F I N I T I O N S    A N D    M A C R O S
 *----------------------------------------------------------------------------------------
 */
#define FILECODE FCH_COMMON_I3CPEI_I3CMASTERPEI_FILECODE

/*----------------------------------------------------------------------------------------
 *                  T Y P E D E F S     A N D     S T R U C T U R E S
 *----------------------------------------------------------------------------------------
 */


/*----------------------------------------------------------------------------------------
 *           P R O T O T Y P E S     O F     L O C A L     F U N C T I O N S
 *----------------------------------------------------------------------------------------
 */


/*----------------------------------------------------------------------------------------
 *                          E X P O R T E D    F U N C T I O N S
 *----------------------------------------------------------------------------------------
 */

extern EFI_GUID gAmdFchSNI3cPpiGuid;

extern UINT32 I3cBaseAddress[];
extern UINT32 I3cModeApcbTokens[];
extern UINT32 I3cOptionsApcbTokens[];

EFI_GUID AmdI3cMasterID = { 0xbeec265d, 0x3379, 0x491c, {0x9f, 0x40, 0x50, 0x3e, 0xdb, 0xde, 0xfd, 0x06} };

VOID
EFIAPI
SmnioWrite32 (
  IN       UINTN  Addr,
  IN       UINT32 Value
  )
{
  UINT32 Val = Value;
  UINT32 Skt1FchBus;

  Skt1FchBus = PcdGet8(PcdSkt1FchBus);
  FchSmnWrite (Skt1FchBus, (UINT32)(UINTN)Addr, &Val, NULL);
}

UINT32
EFIAPI
SmnioRead32 (
  IN       UINTN Addr
  )
{
  UINT32 Value;
  UINT32 Skt1FchBus;

  Skt1FchBus = PcdGet8(PcdSkt1FchBus);
  FchSmnRead (Skt1FchBus, (UINT32)(UINTN)Addr, &Value, NULL);
  return Value;
}

VOID
EFIAPI
I3cMmioWrite32 (
  IN  UINTN   Addr,
  IN  UINT32  Value
  )
{
  MmioWrite32(Addr, Value);
}

EFI_STATUS
AmdI3cMasterPeiInit (
  IN       EFI_PEI_FILE_HANDLE FileHandle,
  IN CONST EFI_PEI_SERVICES    **PeiServices
  )
{
  UINT32                                 Index;
  EFI_STATUS                             Status;
  UINT32                                 SocketNum;
  UINT8                                  IsOnMainSocket;
  UINT32                                 ControllerNum;
  UINT32                                 I3cControllerCount;
  UINT32                                 NumOfSupportedSockets;
  UINTN                                  NumberOfInstalledProcessors;
  UINTN                                  TotalNumberOfDie;
  UINTN                                  TotalNumberOfRootBridges;
  ROOT_BRIDGE_LOCATION                   FchRb;
  EFI_PEI_PPI_DESCRIPTOR                 *I3cPpis;
  EFI_PEI_I3C_MASTER_PPI                 *I3cController;
  AMD_PEI_FABRIC_TOPOLOGY_SERVICES2_PPI  *FabricTopologyServices;
  EFI_I3C_CONTROLLER_CAPABILITIES        *I3cControllerCapabilities;
  UINTN                                  Skt1FchBus = 0;

  DEBUG ((EFI_D_INFO, "[Debug] AmdI3cMasterPeiInit Entry point.\n"));

  //
  // Locat Fabric Topology Service PPI
  Status = (*PeiServices)->LocatePpi (
                             PeiServices,
                             &gAmdFabricTopologyServices2PpiGuid,
                             0,
                             NULL,
                             (VOID **)&FabricTopologyServices
                             );

  ASSERT_EFI_ERROR (Status);
  if (EFI_ERROR (Status)) {
    DEBUG ((EFI_D_ERROR, "[Warning] LocatePpi failed (Status: 0x%x).\n", Status));
    return Status;
  }

  Status = FabricTopologyServices->GetSystemInfo (
             &NumberOfInstalledProcessors,
             &TotalNumberOfDie,
             &TotalNumberOfRootBridges,
            &FchRb,
             NULL
             );
  ASSERT_EFI_ERROR (Status);
  if (EFI_ERROR (Status)) {
    DEBUG ((EFI_D_ERROR, "[Warning] GetSystemInfo failed (Status: 0x%x).\n", Status));
    return Status;
  }
  if (NumberOfInstalledProcessors >= 2){
     Status = FabricTopologyServices->GetRootBridgeInfo (
                             1,//socket
                             FchRb.Die,
                             FchRb.Index,
                             NULL,
                             &Skt1FchBus,
                             NULL,
                             NULL,
                             NULL,
                             NULL
                             );
     ASSERT_EFI_ERROR (Status);
     if (EFI_ERROR (Status)) {
       DEBUG ((EFI_D_ERROR, "[Warning] GetRootBridgeInfo failed (Status: 0x%x).\n", Status));
       return Status;
     }
  }

  DEBUG ((EFI_D_INFO, "Primary FCH index =0x%x Skt1FchBus=0x%x\n",  FchRb.Index, Skt1FchBus));
  PcdSet8S(PcdSkt1FchBus, (UINT8)Skt1FchBus);
  NumOfSupportedSockets = FchSocI3cGetNumOfSupportedSockets ((UINT8)NumberOfInstalledProcessors);

  I3cControllerCount = FchSocI3cGetControllerCount ();
  DEBUG ((EFI_D_INFO, "[Info] I3cMasterPei.c NumOfSupportedSockets: %d, NumberOfI3cControllerPerSocket: %d.\n", NumOfSupportedSockets, I3cControllerCount));
  ASSERT (NumOfSupportedSockets<=2);

  // Create the Protocols
  Status = (*PeiServices)->AllocatePool (PeiServices, NumOfSupportedSockets * I3cControllerCount * sizeof(EFI_PEI_I3C_MASTER_PPI), (VOID **)&I3cController);
  ASSERT_EFI_ERROR (Status);
  if (EFI_ERROR(Status)) {
      DEBUG ((EFI_D_ERROR, "[Warning] AllocatePool() failed for I3cController (Status: 0x%x).\n", Status));
      return Status;
  }

  Status = (*PeiServices)->AllocatePool (PeiServices, NumOfSupportedSockets * I3cControllerCount * sizeof(EFI_I3C_CONTROLLER_CAPABILITIES), (VOID **)&I3cControllerCapabilities);
  ASSERT_EFI_ERROR (Status);
  if (EFI_ERROR(Status)) {
      DEBUG ((EFI_D_ERROR, "[Warning] AllocatePool() failed for I3cControllerCapabilities (Status: 0x%x).\n", Status));
      return Status;
  }

  Status = (*PeiServices)->AllocatePool (PeiServices, NumOfSupportedSockets * I3cControllerCount * sizeof(EFI_PEI_PPI_DESCRIPTOR), (VOID **)&I3cPpis);
  ASSERT_EFI_ERROR (Status);
  if (EFI_ERROR(Status)) {
      DEBUG ((EFI_D_ERROR, "[Warning] AllocatePool() failed for I3cPpis (Status: 0x%x).\n", Status));
      return Status;
  }

  (*PeiServices)->SetMem (I3cControllerCapabilities, NumOfSupportedSockets * I3cControllerCount * sizeof(EFI_I3C_CONTROLLER_CAPABILITIES), 0);
  (*PeiServices)->SetMem (I3cController, NumOfSupportedSockets * I3cControllerCount * sizeof(EFI_PEI_I3C_MASTER_PPI), 0);

  for ( SocketNum=0; SocketNum<NumOfSupportedSockets; SocketNum++ ) {
    for ( ControllerNum=0; ControllerNum<I3cControllerCount; ControllerNum++) {

      Index = SocketNum * I3cControllerCount + ControllerNum;

      I3cControllerCapabilities[Index].StructureSizeInBytes = sizeof (EFI_I3C_CONTROLLER_CAPABILITIES);
      I3cControllerCapabilities[Index].MaximumReceiveBytes  = 0xFFFFFFFF;
      I3cControllerCapabilities[Index].MaximumTotalBytes    = 0xFFFFFFFF;
      I3cControllerCapabilities[Index].MaximumTransmitBytes = 0xFFFFFFFF;

      I3cController[Index].SetBusFrequency                  = SetBusFrequencyI3c;
      I3cController[Index].Reset                            = Reset;
      I3cController[Index].StartRequest                     = StartRequest;
      I3cController[Index].I3cControllerCapabilities        = &I3cControllerCapabilities[Index];

      I3cController[Index].ControllerNum                    = Index;
      I3cController[Index].BaseAddress                      = FchSocI3cGetBaseAddress (SocketNum, ControllerNum);
      I3cController[Index].IsEnabled                        = FchSocI3cIsControllerEnabled (SocketNum, ControllerNum);
      I3cController[Index].I2cCompatibility                 = FchSocI3cGetI2cCompatibility (SocketNum, ControllerNum);
      I3cController[Index].SdaHoldTime                      = FchSocI3cGetSdaHoldTime (SocketNum, ControllerNum);
      I3cController[Index].Speed                            = FchSocI3cGetSpeed (SocketNum, ControllerNum);
      I3cController[Index].PushPullHighCount                = FchSocI3cGetPushPullHighCount (SocketNum, ControllerNum);

      CopyGuid (&I3cController[Index].Identifier, &AmdI3cMasterID);

      if ( I3cController[Index].IsEnabled ) {
        IsOnMainSocket = ( ( I3cController[Index].BaseAddress & 0xFFFF0000 ) == 0xFEDD0000 ) ? 0x01 : 0x00;
        if ( IsOnMainSocket == 0x01 ) {
          Status = I3cInit (I3cController[Index].BaseAddress,
            I3cController[Index].I2cCompatibility,
            I3cController[Index].Speed,
            I3cController[Index].SdaHoldTime,
            I3cController[Index].PushPullHighCount,
            MmioRead32,
            I3cMmioWrite32
          );
        } else {
          Status = I3cInit (I3cController[Index].BaseAddress,
            I3cController[Index].I2cCompatibility,
            I3cController[Index].Speed,
            I3cController[Index].SdaHoldTime,
            I3cController[Index].PushPullHighCount,
            SmnioRead32,
            SmnioWrite32
          );
        }

        if ( Status == EFI_SUCCESS ) {
          DEBUG ((EFI_D_INFO, "[Info] I3C controller (SocketNum: %u, ControllerNum: %u) initialized (Status: 0x%x).\n", SocketNum, ControllerNum, Status));
        } else {
          DEBUG ((EFI_D_ERROR, "[Error] I3C controller (SocketNum: %u, ControllerNum: %u) initialization failed (Status: 0x%x).\n", SocketNum, ControllerNum, Status));
        }
      } else {
        DEBUG ((EFI_D_INFO, "[Info] I3C controller (SocketNum: %u, ControllerNum: %u) initialization skipped (IsEnabled: 0x%x).\n", SocketNum, ControllerNum, I3cController[Index].IsEnabled));
      }

      I3cPpis[Index].Guid  = &gAmdFchSNI3cPpiGuid;
      I3cPpis[Index].Ppi   = &I3cController[Index];
      I3cPpis[Index].Flags = ( Index != (UINT8)( NumOfSupportedSockets * I3cControllerCount - 1 ) ) ?
        ( EFI_PEI_PPI_DESCRIPTOR_PPI ) :
        ( EFI_PEI_PPI_DESCRIPTOR_PPI | EFI_PEI_PPI_DESCRIPTOR_TERMINATE_LIST );
    }
  }

  Status = (*PeiServices)->InstallPpi (PeiServices, I3cPpis);

  if ( Status != EFI_SUCCESS ) {
    DEBUG ((EFI_D_ERROR, "[Warning] I3C controller InstallPpi failed (Status: 0x%x).\n", Status));
  }

  return EFI_SUCCESS;
}

EFI_STATUS
EFIAPI
StartRequest (
  IN CONST EFI_PEI_I3C_MASTER_PPI *This,
  IN       UINTN                  SlaveAddress,
  IN       EFI_I3C_REQUEST_PACKET *RequestPacket,
  IN       BOOLEAN                 ReStartEnable
  )
{
  UINT32 Base;
  UINT8  Speed;
  UINT32 Index;
  EFI_STATUS Status;
  UINTN OperationCount;
  UINT8 IsOnMainSocket;
  EFI_I3C_OPERATION *Operation;
  UINT8 TerminationOnCompletion;
  I3cRegisterRead32  I3cRegRead32;
  I3cRegisterWrite32 I3cRegWrite32;

  if ( This->IsEnabled == 0x00 ) {
    DEBUG ((EFI_D_ERROR, "[Error] I3C controller (index %u) is not configured as I3C (IsEnabled: 0x%x).\n", This->ControllerNum, This->IsEnabled));
    return EFI_DEVICE_ERROR;
  }

  Base  = This->BaseAddress;
  Speed = This->Speed;
  IsOnMainSocket = ( ( Base & 0xFFFF0000 ) == 0xFEDD0000 ) ? 0x01 : 0x00;
  Operation = RequestPacket->Operation;
  OperationCount = RequestPacket->OperationCount;

  if ( OperationCount == 0x00 ) {
    return EFI_UNSUPPORTED;
  }

  for ( Index=0; Index < OperationCount; Index++ ) {
    if ( Operation[Index].LengthInBytes == 0x00 ) {
      // We do not support quick read/write
      return EFI_UNSUPPORTED;
    } else if ( Operation[Index].Flags & (I3C_FLAG_SMBUS_PEC | I3C_FLAG_SMBUS_PROCESS_CALL) ) {
      // No PEC, ProcessCall and BlkProcessCall either
      return Status = EFI_UNSUPPORTED;
    }
  }

  if ( IsOnMainSocket == 0x01 ) {
    I3cRegRead32  = (I3cRegisterRead32)MmioRead32;
    I3cRegWrite32 = I3cMmioWrite32;
  } else {
    I3cRegRead32  = (I3cRegisterRead32)SmnioRead32;
    I3cRegWrite32 = (I3cRegisterWrite32)SmnioWrite32;
  }
  //check I3C controller is master and idle
  Status = I3cCheckPresentState(Base, I3cRegRead32, I3cRegWrite32);
  if (EFI_ERROR(Status)){
   //reset
     DEBUG ((EFI_D_ERROR, "reset controller \n"));
     Reset(This);
     //check status
     Status = I3cCheckPresentState(Base, I3cRegRead32, I3cRegWrite32);
     if (EFI_ERROR(Status)){
      DEBUG ((EFI_D_ERROR, "I3C idle faile:%r ret\n", Status));
      return Status;
     }
  }

  if (  ReStartEnable == TRUE && OperationCount == 2 && Operation[0].Flags == 0x00 &&  Operation[1].Flags == I3C_FLAG_READ  ){
    DEBUG ((EFI_D_INFO, "AmdI3cMasterPei.c write then Read operation with ReStart: \n"));
    Status = I3cPrivateWriteRead (Base, Operation[0].Buffer, Operation[0].LengthInBytes, Operation[1].Buffer, Operation[1].LengthInBytes, SlaveAddress, Speed, I3cRegRead32, I3cRegWrite32 );
  }
  else {
    for ( Index=0; Index < OperationCount; Index++ ) {
      // Note: DDR5 SPD does not like 'restart' bit
      // TerminationOnCompletion = ( ( Index + 1 ) == OperationCount ) ? 0x01 : 0x00;
      TerminationOnCompletion = 0x01;

      if ( Operation[Index].Flags == 0x00 ) {
        // Write operation
        DEBUG ((EFI_D_ERROR, "[Debug] AmdI3cMasterPei.c AmdI3cMasterHandleRequest Operation (Write) Index: %u.\n", Index));
        if ( IsOnMainSocket == 0x01 ) {
          Status = I3cPrivateWrite (Base, Operation[Index].Buffer, Operation[Index].LengthInBytes, SlaveAddress, TerminationOnCompletion, Speed, MmioRead32, I3cMmioWrite32);
        } else {
          Status = I3cPrivateWrite (Base, Operation[Index].Buffer, Operation[Index].LengthInBytes, SlaveAddress, TerminationOnCompletion, Speed, SmnioRead32, SmnioWrite32);
        }
      } else {
        // Read operation
        if ( IsOnMainSocket == 0x01 ) {
          Status = I3cPrivateRead (Base, Operation[Index].Buffer, Operation[Index].LengthInBytes, SlaveAddress, TerminationOnCompletion, Speed, MmioRead32, I3cMmioWrite32);
        } else {
          Status = I3cPrivateRead (Base, Operation[Index].Buffer, Operation[Index].LengthInBytes, SlaveAddress, TerminationOnCompletion, Speed, SmnioRead32, SmnioWrite32);
        }
        DEBUG ((EFI_D_ERROR, "[Debug] AmdI3cMasterPei.c AmdI3cMasterHandleRequest Operation (Read) Index: %u.\n", Index));
      }

      if ( Status != EFI_SUCCESS ) {
        return Status;
      }
    }
  }

  return Status;
}

EFI_STATUS
EFIAPI
SetBusFrequencyI3c (
  IN       EFI_PEI_I3C_MASTER_PPI *This,
  IN       UINTN                  *BusClockHertz
  )
{
  // TODO: Fix this
  return EFI_UNSUPPORTED;
}

EFI_STATUS
EFIAPI
Reset (
  IN CONST EFI_PEI_I3C_MASTER_PPI *This
  )
{
  EFI_STATUS Status;
  UINT8 IsOnMainSocket;

  if ( This->IsEnabled == 0x00 ) {
    DEBUG ((EFI_D_ERROR, "[Error] I3C controller (index %u) is not configured as I3C (IsEnabled: 0x%x).\n", This->ControllerNum, This->IsEnabled));
    return EFI_DEVICE_ERROR;
  }

  IsOnMainSocket = ( ( This->BaseAddress & 0xFFFF0000 ) == 0xFEDD0000 ) ? 0x01 : 0x00;
  if ( IsOnMainSocket == 0x01 ) {
    Status = I3cSoftReset (This->BaseAddress, MmioRead32, I3cMmioWrite32);
  } else {
    Status = I3cSoftReset (This->BaseAddress, SmnioRead32, SmnioWrite32);
  }

  if ( Status != EFI_SUCCESS ) {
    DEBUG ((EFI_D_ERROR, "[Error] I3C controller (index %u) reset failed (Status: 0x%x).\n", This->ControllerNum, Status));
    return EFI_DEVICE_ERROR;
  }

  if ( IsOnMainSocket == 0x01 ) {
    Status = I3cInit (
      This->BaseAddress,
      This->I2cCompatibility,
      This->Speed,
      This->SdaHoldTime,
      This->PushPullHighCount,
      MmioRead32,
      I3cMmioWrite32
    );
  } else {
    Status = I3cInit (
      This->BaseAddress,
      This->I2cCompatibility,
      This->Speed,
      This->SdaHoldTime,
      This->PushPullHighCount,
      SmnioRead32,
      SmnioWrite32
    );
  }

  if ( Status == EFI_SUCCESS ) {
    DEBUG ((EFI_D_INFO, "[Debug] I3C controller (index %u) initialized (Status: 0x%x).\n", This->ControllerNum, Status));
  } else {
    DEBUG ((EFI_D_ERROR, "[Error] I3C controller (index %u) initialization failed (Status: 0x%x).\n", This->ControllerNum, Status));
  }

  return Status;
}


