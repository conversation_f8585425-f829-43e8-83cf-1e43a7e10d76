#;*****************************************************************************
#;
#; Copyright (C) 2008-2024 Advanced Micro Devices, Inc. All rights reserved.
#;
#;******************************************************************************

[defines]
  INF_VERSION                    = 0x00010005
  BASE_NAME                      = FchKunlunDxe
  FILE_GUID                      = bc6102dd-3cc7-4b86-9a2c-4e125cabdf3a
  MODULE_TYPE                    = DXE_DRIVER
  VERSION_STRING                 = 1.0
  ENTRY_POINT                    = FchDxeInit


[sources.common]
  FchDxe.c
  FchDxe.h
  ReportConfigValues.c

[LibraryClasses.common.DXE_DRIVER]
  BaseLib
  UefiLib
  HobLib
  PrintLib

[LibraryClasses]
  UefiDriverEntryPoint
  UefiBootServicesTableLib
  DebugLib

  NbioHandleLib
  NbioSmuBrhLib
  FchKunlunDxeLib
  FchDxeLibV9
  FchInitHookLibDxe
  AmdIdsHookLib
  FchIdsHookLib
  FabricRegisterAccLib
  AgesaConfigLib

[Guids]
  gFchResetDataHobGuid

[Protocols]
  gEfiPciIoProtocolGuid                   #COMSUMED
  gFchInitProtocolGuid                    #PRODUCED
  gFchInitDonePolicyProtocolGuid          #PRODUCED
  gAmdFabricTopologyServices2ProtocolGuid #COMSUMED
  gAmdApcbDxeServiceProtocolGuid          #COMSUMED

[Pcd]
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdSataClass
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdSataAggrLinkPmCap
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdHpetEnable
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdHpetMsiDis
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdSpreadSpectrum
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdSmbusSsid
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdSataAhciSsid
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdSataRaidSsid
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdSataRaid5Ssid
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdSataIdeSsid
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdXhciSsid
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdIsaBridgeSsid
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdSdSsid
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdSataRasSupport
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdSataAhciDisPrefetchFunction
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdSdConfig
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdSdClockMultiplier
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdSdDbgConfig
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdPwrFailShadow
  gEfiAmdAgesaModulePkgTokenSpaceGuid.FchRTDeviceEnableMap
  gEfiAmdAgesaModulePkgTokenSpaceGuid.FchRTDeviceEnableMapEx
  gEfiAmdAgesaModulePkgTokenSpaceGuid.FchAl2AhbLegacyUartIoEnable
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdSataDevSlpPort0
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdSataDevSlpPort1
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdSataDevSlpPort0Num
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdSataDevSlpPort1Num
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdSataPortMultCap
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdSataPscCap
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdSataSscCap
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdSataClkAutoOff
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdSataFisBasedSwitching
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdSataCccSupport
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdSataMsiEnable
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdSataTargetSupport8Device
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdSataDisableGenericMode
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdSataAhciEnclosureManagement
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdSataHotRemovalEnh
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdSataPhyPllShutDown
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdSataSgpio0
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdSataMultiDiePortESP
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdSataMultiDiePortShutDown
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdSataIoDie0PortMode
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdSataSgpioMultiDieEnable
#  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdSataControllerAutoShutdown   unsupport it
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdTimerTickTrack
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdClockInterruptTag
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdSerialDebugBusEnable
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdNoClearThermalTripSts
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdStressResetMode
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdALinkClkGateOff
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdBLinkClkGateOff
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdAbClockGating
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdSbgMemoryPowerSaving
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdSbgClockGating
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdXdmaDmaWrite16ByteMode
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdXdmaMemoryPowerSaving
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdXdmaPendingNprThreshold
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdXdmaDncplOrderDis
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdGppClockRequest0
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdGppClockRequest1
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdGppClockRequest2
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdGppClockRequest3
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdSltGfxClockRequest0
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdSltGfxClockRequest1
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdSdphostBypassDataPack
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdSdphostDisNpmwrProtect
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdResetCpuOnSyncFlood
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdDelayResetCpuOnSyncFlood
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdEmmcEnable
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdEmmcType
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdEmmcDriverType
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdEmmcBoot
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdEmmcAdma2Support
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdEmmcAdmaSupport
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdEmmcSdmaSupport
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdEmmcA64bSupport
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdEmmcD3Support
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdOemProgrammingTablePtr
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdNoneSioKbcSupport
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdNativePcieSupport
  gEfiAmdAgesaPkgTokenSpaceGuid.FchUart0LegacyEnable
  gEfiAmdAgesaPkgTokenSpaceGuid.FchUart1LegacyEnable
  gEfiAmdAgesaPkgTokenSpaceGuid.FchUart2LegacyEnable
  gEfiAmdAgesaPkgTokenSpaceGuid.FchUart3LegacyEnable
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdFchAlinkRasSupport
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdSmuFeatureControlDefines
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdFchiLa1MTraceMemoryEn
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdFchiLa1MTraceMemoryBase
  gEfiMdePkgTokenSpaceGuid.PcdPciExpressBaseAddress
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdFchI2c0SdaHold
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdFchI2c1SdaHold
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdFchI2c2SdaHold
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdFchI2c3SdaHold
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdFchI2c4SdaHold
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdFchI2c5SdaHold
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdSyncFloodToApml
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdFchUart0Irq
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdFchUart1Irq
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdFchUart2Irq
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdFchUart3Irq
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdFchI2c0Irq
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdFchI2c1Irq
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdFchI2c2Irq
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdFchI2c3Irq
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdFchI2c4Irq
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdFchI2c5Irq
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdCppcSciBitMap
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdFchOemSecureSwSmi
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdSataBISTLComplianceMode
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdSataMultiDieDevSlp
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdFchSpdHostCtrlRelease
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdFchDimmTelemetry
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdFchAoacInitEnable
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdFchShortTimerEnableSmi
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdFchShortTimerStartNowSmi
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdFchShortTimerCycleDurationSmi
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdFchLongTimerEnableSmi
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdFchLongTimerStartNowSmi
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdFchLongTimerCycleDurationSmi
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdIdsDebugPrintSerialPortSelect
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdFchSxEntryXhciPmeEnWA
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdSataUBMDiagMode
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdXhci0Enable
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdXhci1Enable
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdXhci2Enable
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdXhci3Enable
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdFchI2cSdaHoldOverride
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdSataStaggeredSpinup
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdBootTimerEnable
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdFchAcpiDeviceInvisibeMapEx
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdAmdMemMaxDimmPerChannelV2
[Packages]
  MdePkg/MdePkg.dec
  AgesaPkg/AgesaPkg.dec
  AgesaModulePkg/AgesaModuleFchPkg.dec
  AgesaModulePkg/Fch/Kunlun/FchKunlun.dec
  AgesaModulePkg/AgesaModuleNbioPkg.dec
  AgesaModulePkg/AgesaModuleDfPkg.dec
  AgesaModulePkg/AgesaCommonModulePkg.dec

[Depex]
  gEfiPciRootBridgeIoProtocolGuid  AND
  gAmdFchKunlunDepexProtocolGuid   AND
  gAmdFabricTopologyServices2ProtocolGuid



