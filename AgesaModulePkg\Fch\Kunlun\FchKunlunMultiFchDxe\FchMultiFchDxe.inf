#;*****************************************************************************
#;
#; Copyright (C) 2008-2024 Advanced Micro Devices, Inc. All rights reserved.
#;
#;******************************************************************************

[defines]
  INF_VERSION                    = 0x00010005
  BASE_NAME                      = FchKunlunMultiFchDxe
  FILE_GUID                      = 750f8e5a-59be-485b-a717-fd850fc8772b
  MODULE_TYPE                    = DXE_DRIVER
  VERSION_STRING                 = 1.0
  ENTRY_POINT                    = MultiFchDxeInit


[sources.common]
  FchMultiFchDxe.c
  FchMultiFchDxe.h

[LibraryClasses.common.DXE_DRIVER]
  BaseLib
  UefiLib
  HobLib
  PrintLib

[LibraryClasses]
  FchDxeLibV9
  FchKunlunDxeLib
  FabricRegisterAccLib

  UefiDriverEntryPoint
  UefiBootServicesTableLib
  DebugLib
  NbioSmuBrhLib

[Guids]
  gFchMultiFchResetDataHobGuid

[Protocols]
  gFchInitProtocolGuid              #CONSUMED
  gFchMultiFchInitProtocolGuid      #PRODUCED
  gAmdApcbDxeServiceProtocolGuid    #CONSUMED

[Packages]
  MdePkg/MdePkg.dec
  AgesaPkg/AgesaPkg.dec
  AgesaModulePkg/AgesaModuleFchPkg.dec
  AgesaModulePkg/Fch/Kunlun/FchKunlun.dec
  AgesaModulePkg/AgesaCommonModulePkg.dec
  AgesaModulePkg/AgesaModuleDfPkg.dec

[Pcd]
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdSataEnable2
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdSataMultiDiePortShutDown
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdSataMultiDiePortESP
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdSataMultiDieDevSlp
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdSataSgpioMultiDieEnable
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdSataIoDie1PortMode
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdSataUBMDiagMode
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdResetCpuOnSyncFlood
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdDelayResetCpuOnSyncFlood

[Depex]
  gFchInitProtocolGuid                    AND
  gAmdFchKLMultiFchDepexProtocolGuid      AND
  gAmdFabricTopologyServices2ProtocolGuid AND
  gAmdApcbDxeServiceProtocolGuid



