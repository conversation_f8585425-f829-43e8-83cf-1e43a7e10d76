/*****************************************************************************
 *
 * Copyright (C) 2008-2024 Advanced Micro Devices, Inc. All rights reserved.
 *
 *******************************************************************************
 **/

#ifndef _SDPMUX_H_
#define _SDPMUX_H_


/***********************************************************
* Register Name : NTBPCIE_LOCATION
************************************************************/

#define NTBPCIE_LOCATION_NTBPCIE_Port_OFFSET                   0
#define NTBPCIE_LOCATION_NTBPCIE_Port_MASK                     0xffff

#define NTBPCIE_LOCATION_NTBPCIE_Core_OFFSET                   16
#define NTBPCIE_LOCATION_NTBPCIE_Core_MASK                     0xffff0000

typedef union {
  struct {
    UINT32                                        NTBPCIE_Port:16;
    UINT32                                        NTBPCIE_Core:16;
  } Field;
  UINT32 Value;
} NTBPCIE_LOCATION_STRUCT;

#define SMN_NTBPCIE_LOCATION_ADDRESS                                  0x440000cUL
#define SMN_IOHUB0NBIO0_NTBPCIE_LOCATION_ADDRESS                      0x440000cUL
#define SMN_IOHUB0NBIO1_NTBPCIE_LOCATION_ADDRESS                      0x4e0000cUL


/***********************************************************
* Register Name : NTBPCIE_UNITID
************************************************************/

#define NTBPCIE_UNITID_NTBPCIE_UnitID_OFFSET                   0
#define NTBPCIE_UNITID_NTBPCIE_UnitID_MASK                     0x7ff

#define NTBPCIE_UNITID_Reserved_31_11_OFFSET                   11
#define NTBPCIE_UNITID_Reserved_31_11_MASK                     0xfffff800

typedef union {
  struct {
    UINT32                                      NTBPCIE_UnitID:11;
    UINT32                                      Reserved_31_11:21;
  } Field;
  UINT32 Value;
} NTBPCIE_UNITID_STRUCT;

#define SMN_NTBPCIE_UNITID_ADDRESS                                    0x4400010UL
#define SMN_IOHUB0NBIO0_NTBPCIE_UNITID_ADDRESS                        0x4400010UL
#define SMN_IOHUB0NBIO1_NTBPCIE_UNITID_ADDRESS                        0x4e00010UL


/***********************************************************
* Register Name : SDPMUX_DMA_COMP_EARLY_WAKE_UP_EN
************************************************************/

#define SDPMUX_DMA_COMP_EARLY_WAKE_UP_EN_DMA_EnableEarlyCompClkReqIngress_OFFSET 0
#define SDPMUX_DMA_COMP_EARLY_WAKE_UP_EN_DMA_EnableEarlyCompClkReqIngress_MASK 0xffff

#define SDPMUX_DMA_COMP_EARLY_WAKE_UP_EN_DMA_EnableEarlyCompClkReqEgress_OFFSET 16
#define SDPMUX_DMA_COMP_EARLY_WAKE_UP_EN_DMA_EnableEarlyCompClkReqEgress_MASK 0xffff0000

typedef union {
  struct {
    UINT32                    DMA_EnableEarlyCompClkReqIngress:16;
    UINT32                     DMA_EnableEarlyCompClkReqEgress:16;
  } Field;
  UINT32 Value;
} SDPMUX_DMA_COMP_EARLY_WAKE_UP_EN_STRUCT;

#define SMN_SDPMUX_DMA_COMP_EARLY_WAKE_UP_EN_ADDRESS                  0x4400024UL
#define SMN_IOHUB0NBIO0_SDPMUX_DMA_COMP_EARLY_WAKE_UP_EN_ADDRESS      0x4400024UL
#define SMN_IOHUB0NBIO1_SDPMUX_DMA_COMP_EARLY_WAKE_UP_EN_ADDRESS      0x4e00024UL


/***********************************************************
* Register Name : SDPMUX_DMA_ORIG_EARLY_WAKE_UP_EN
************************************************************/

#define SDPMUX_DMA_ORIG_EARLY_WAKE_UP_EN_DMA_EnableEarlyOrigClkReqIngress_OFFSET 0
#define SDPMUX_DMA_ORIG_EARLY_WAKE_UP_EN_DMA_EnableEarlyOrigClkReqIngress_MASK 0xffff
#define SDPMUX_DMA_ORIG_EARLY_WAKE_UP_EN_DMA_EnableEarlyOrigClkReqIngress_DEFAULT     0x2

#define SDPMUX_DMA_ORIG_EARLY_WAKE_UP_EN_DMA_EnableEarlyOrigClkReqEgress_OFFSET 16
#define SDPMUX_DMA_ORIG_EARLY_WAKE_UP_EN_DMA_EnableEarlyOrigClkReqEgress_MASK 0xffff0000
#define SDPMUX_DMA_ORIG_EARLY_WAKE_UP_EN_DMA_EnableEarlyOrigClkReqEgress_DEFAULT     0x1

typedef union {
  struct {
    UINT32                    DMA_EnableEarlyOrigClkReqIngress:16;
    UINT32                     DMA_EnableEarlyOrigClkReqEgress:16;
  } Field;
  UINT32 Value;
} SDPMUX_DMA_ORIG_EARLY_WAKE_UP_EN_STRUCT;

#define SMN_SDPMUX_DMA_ORIG_EARLY_WAKE_UP_EN_ADDRESS                  0x4400018UL
#define SMN_IOHUB0NBIO0_SDPMUX_DMA_ORIG_EARLY_WAKE_UP_EN_ADDRESS      0x4400018UL
#define SMN_IOHUB0NBIO1_SDPMUX_DMA_ORIG_EARLY_WAKE_UP_EN_ADDRESS      0x4e00018UL


/***********************************************************
* Register Name : SDPMUX_GLUE_CG_LCLK_CTRL_0
************************************************************/

#define SDPMUX_GLUE_CG_LCLK_CTRL_0_Reserved_3_0_OFFSET         0
#define SDPMUX_GLUE_CG_LCLK_CTRL_0_Reserved_3_0_MASK           0xf

#define SDPMUX_GLUE_CG_LCLK_CTRL_0_CG_OFF_HYSTERESIS_OFFSET    4
#define SDPMUX_GLUE_CG_LCLK_CTRL_0_CG_OFF_HYSTERESIS_MASK      0xff0

#define SDPMUX_GLUE_CG_LCLK_CTRL_0_Reserved_21_12_OFFSET       12
#define SDPMUX_GLUE_CG_LCLK_CTRL_0_Reserved_21_12_MASK         0x3ff000

#define SDPMUX_GLUE_CG_LCLK_CTRL_0_SOFT_OVERRIDE_CLK9_OFFSET   22
#define SDPMUX_GLUE_CG_LCLK_CTRL_0_SOFT_OVERRIDE_CLK9_MASK     0x400000
#define SDPMUX_GLUE_CG_LCLK_CTRL_0_SOFT_OVERRIDE_CLK9_DEFAULT     0x0

#define SDPMUX_GLUE_CG_LCLK_CTRL_0_SOFT_OVERRIDE_CLK8_OFFSET   23
#define SDPMUX_GLUE_CG_LCLK_CTRL_0_SOFT_OVERRIDE_CLK8_MASK     0x800000
#define SDPMUX_GLUE_CG_LCLK_CTRL_0_SOFT_OVERRIDE_CLK8_DEFAULT     0x0

#define SDPMUX_GLUE_CG_LCLK_CTRL_0_SOFT_OVERRIDE_CLK7_OFFSET   24
#define SDPMUX_GLUE_CG_LCLK_CTRL_0_SOFT_OVERRIDE_CLK7_MASK     0x1000000
#define SDPMUX_GLUE_CG_LCLK_CTRL_0_SOFT_OVERRIDE_CLK7_DEFAULT     0x0

#define SDPMUX_GLUE_CG_LCLK_CTRL_0_SOFT_OVERRIDE_CLK6_OFFSET   25
#define SDPMUX_GLUE_CG_LCLK_CTRL_0_SOFT_OVERRIDE_CLK6_MASK     0x2000000
#define SDPMUX_GLUE_CG_LCLK_CTRL_0_SOFT_OVERRIDE_CLK6_DEFAULT     0x0

#define SDPMUX_GLUE_CG_LCLK_CTRL_0_SOFT_OVERRIDE_CLK5_OFFSET   26
#define SDPMUX_GLUE_CG_LCLK_CTRL_0_SOFT_OVERRIDE_CLK5_MASK     0x4000000
#define SDPMUX_GLUE_CG_LCLK_CTRL_0_SOFT_OVERRIDE_CLK5_DEFAULT     0x0

#define SDPMUX_GLUE_CG_LCLK_CTRL_0_SOFT_OVERRIDE_CLK4_OFFSET   27
#define SDPMUX_GLUE_CG_LCLK_CTRL_0_SOFT_OVERRIDE_CLK4_MASK     0x8000000
#define SDPMUX_GLUE_CG_LCLK_CTRL_0_SOFT_OVERRIDE_CLK4_DEFAULT     0x0

#define SDPMUX_GLUE_CG_LCLK_CTRL_0_SOFT_OVERRIDE_CLK3_OFFSET   28
#define SDPMUX_GLUE_CG_LCLK_CTRL_0_SOFT_OVERRIDE_CLK3_MASK     0x10000000
#define SDPMUX_GLUE_CG_LCLK_CTRL_0_SOFT_OVERRIDE_CLK3_DEFAULT     0x0

#define SDPMUX_GLUE_CG_LCLK_CTRL_0_SOFT_OVERRIDE_CLK2_OFFSET   29
#define SDPMUX_GLUE_CG_LCLK_CTRL_0_SOFT_OVERRIDE_CLK2_MASK     0x20000000
#define SDPMUX_GLUE_CG_LCLK_CTRL_0_SOFT_OVERRIDE_CLK2_DEFAULT     0x0

#define SDPMUX_GLUE_CG_LCLK_CTRL_0_SOFT_OVERRIDE_CLK1_OFFSET   30
#define SDPMUX_GLUE_CG_LCLK_CTRL_0_SOFT_OVERRIDE_CLK1_MASK     0x40000000
#define SDPMUX_GLUE_CG_LCLK_CTRL_0_SOFT_OVERRIDE_CLK1_DEFAULT     0x0

#define SDPMUX_GLUE_CG_LCLK_CTRL_0_SOFT_OVERRIDE_CLK0_OFFSET   31
#define SDPMUX_GLUE_CG_LCLK_CTRL_0_SOFT_OVERRIDE_CLK0_MASK     0x80000000
#define SDPMUX_GLUE_CG_LCLK_CTRL_0_SOFT_OVERRIDE_CLK0_DEFAULT     0x0

typedef union {
  struct {
    UINT32                                        Reserved_3_0:4;
    UINT32                                   CG_OFF_HYSTERESIS:8;
    UINT32                                      Reserved_21_12:10;
    UINT32                                  SOFT_OVERRIDE_CLK9:1;
    UINT32                                  SOFT_OVERRIDE_CLK8:1;
    UINT32                                  SOFT_OVERRIDE_CLK7:1;
    UINT32                                  SOFT_OVERRIDE_CLK6:1;
    UINT32                                  SOFT_OVERRIDE_CLK5:1;
    UINT32                                  SOFT_OVERRIDE_CLK4:1;
    UINT32                                  SOFT_OVERRIDE_CLK3:1;
    UINT32                                  SOFT_OVERRIDE_CLK2:1;
    UINT32                                  SOFT_OVERRIDE_CLK1:1;
    UINT32                                  SOFT_OVERRIDE_CLK0:1;
  } Field;
  UINT32 Value;
} SDPMUX_GLUE_CG_LCLK_CTRL_0_STRUCT;

#define SMN_SDPMUX_GLUE_CG_LCLK_CTRL_0_ADDRESS                        0x4400000UL
#define SMN_IOHUB0NBIO0_SDPMUX_GLUE_CG_LCLK_CTRL_0_ADDRESS            0x4400000UL
#define SMN_IOHUB0NBIO1_SDPMUX_GLUE_CG_LCLK_CTRL_0_ADDRESS            0x4e00000UL


/***********************************************************
* Register Name : SDPMUX_GLUE_CG_LCLK_CTRL_1
************************************************************/

#define SDPMUX_GLUE_CG_LCLK_CTRL_1_Reserved_21_0_OFFSET        0
#define SDPMUX_GLUE_CG_LCLK_CTRL_1_Reserved_21_0_MASK          0x3fffff

#define SDPMUX_GLUE_CG_LCLK_CTRL_1_SOFT_OVERRIDE_CLK9_OFFSET   22
#define SDPMUX_GLUE_CG_LCLK_CTRL_1_SOFT_OVERRIDE_CLK9_MASK     0x400000
#define SDPMUX_GLUE_CG_LCLK_CTRL_1_SOFT_OVERRIDE_CLK9_DEFAULT     0x0

#define SDPMUX_GLUE_CG_LCLK_CTRL_1_SOFT_OVERRIDE_CLK8_OFFSET   23
#define SDPMUX_GLUE_CG_LCLK_CTRL_1_SOFT_OVERRIDE_CLK8_MASK     0x800000
#define SDPMUX_GLUE_CG_LCLK_CTRL_1_SOFT_OVERRIDE_CLK8_DEFAULT     0x0

#define SDPMUX_GLUE_CG_LCLK_CTRL_1_SOFT_OVERRIDE_CLK7_OFFSET   24
#define SDPMUX_GLUE_CG_LCLK_CTRL_1_SOFT_OVERRIDE_CLK7_MASK     0x1000000
#define SDPMUX_GLUE_CG_LCLK_CTRL_1_SOFT_OVERRIDE_CLK7_DEFAULT     0x0

#define SDPMUX_GLUE_CG_LCLK_CTRL_1_SOFT_OVERRIDE_CLK6_OFFSET   25
#define SDPMUX_GLUE_CG_LCLK_CTRL_1_SOFT_OVERRIDE_CLK6_MASK     0x2000000
#define SDPMUX_GLUE_CG_LCLK_CTRL_1_SOFT_OVERRIDE_CLK6_DEFAULT     0x0

#define SDPMUX_GLUE_CG_LCLK_CTRL_1_SOFT_OVERRIDE_CLK5_OFFSET   26
#define SDPMUX_GLUE_CG_LCLK_CTRL_1_SOFT_OVERRIDE_CLK5_MASK     0x4000000
#define SDPMUX_GLUE_CG_LCLK_CTRL_1_SOFT_OVERRIDE_CLK5_DEFAULT     0x0

#define SDPMUX_GLUE_CG_LCLK_CTRL_1_SOFT_OVERRIDE_CLK4_OFFSET   27
#define SDPMUX_GLUE_CG_LCLK_CTRL_1_SOFT_OVERRIDE_CLK4_MASK     0x8000000
#define SDPMUX_GLUE_CG_LCLK_CTRL_1_SOFT_OVERRIDE_CLK4_DEFAULT     0x0

#define SDPMUX_GLUE_CG_LCLK_CTRL_1_SOFT_OVERRIDE_CLK3_OFFSET   28
#define SDPMUX_GLUE_CG_LCLK_CTRL_1_SOFT_OVERRIDE_CLK3_MASK     0x10000000
#define SDPMUX_GLUE_CG_LCLK_CTRL_1_SOFT_OVERRIDE_CLK3_DEFAULT     0x0

#define SDPMUX_GLUE_CG_LCLK_CTRL_1_SOFT_OVERRIDE_CLK2_OFFSET   29
#define SDPMUX_GLUE_CG_LCLK_CTRL_1_SOFT_OVERRIDE_CLK2_MASK     0x20000000
#define SDPMUX_GLUE_CG_LCLK_CTRL_1_SOFT_OVERRIDE_CLK2_DEFAULT     0x0

#define SDPMUX_GLUE_CG_LCLK_CTRL_1_SOFT_OVERRIDE_CLK1_OFFSET   30
#define SDPMUX_GLUE_CG_LCLK_CTRL_1_SOFT_OVERRIDE_CLK1_MASK     0x40000000
#define SDPMUX_GLUE_CG_LCLK_CTRL_1_SOFT_OVERRIDE_CLK1_DEFAULT     0x0

#define SDPMUX_GLUE_CG_LCLK_CTRL_1_SOFT_OVERRIDE_CLK0_OFFSET   31
#define SDPMUX_GLUE_CG_LCLK_CTRL_1_SOFT_OVERRIDE_CLK0_MASK     0x80000000
#define SDPMUX_GLUE_CG_LCLK_CTRL_1_SOFT_OVERRIDE_CLK0_DEFAULT     0x0

typedef union {
  struct {
    UINT32                                       Reserved_21_0:22;
    UINT32                                  SOFT_OVERRIDE_CLK9:1;
    UINT32                                  SOFT_OVERRIDE_CLK8:1;
    UINT32                                  SOFT_OVERRIDE_CLK7:1;
    UINT32                                  SOFT_OVERRIDE_CLK6:1;
    UINT32                                  SOFT_OVERRIDE_CLK5:1;
    UINT32                                  SOFT_OVERRIDE_CLK4:1;
    UINT32                                  SOFT_OVERRIDE_CLK3:1;
    UINT32                                  SOFT_OVERRIDE_CLK2:1;
    UINT32                                  SOFT_OVERRIDE_CLK1:1;
    UINT32                                  SOFT_OVERRIDE_CLK0:1;
  } Field;
  UINT32 Value;
} SDPMUX_GLUE_CG_LCLK_CTRL_1_STRUCT;

#define SMN_SDPMUX_GLUE_CG_LCLK_CTRL_1_ADDRESS                        0x4400004UL
#define SMN_IOHUB0NBIO0_SDPMUX_GLUE_CG_LCLK_CTRL_1_ADDRESS            0x4400004UL
#define SMN_IOHUB0NBIO1_SDPMUX_GLUE_CG_LCLK_CTRL_1_ADDRESS            0x4e00004UL


/***********************************************************
* Register Name : SDPMUX_HST_COMP_EARLY_WAKE_UP_EN
************************************************************/

#define SDPMUX_HST_COMP_EARLY_WAKE_UP_EN_HST_EnableEarlyCompClkReqIngress_OFFSET 0
#define SDPMUX_HST_COMP_EARLY_WAKE_UP_EN_HST_EnableEarlyCompClkReqIngress_MASK 0xffff

#define SDPMUX_HST_COMP_EARLY_WAKE_UP_EN_HST_EnableEarlyCompClkReqEgress_OFFSET 16
#define SDPMUX_HST_COMP_EARLY_WAKE_UP_EN_HST_EnableEarlyCompClkReqEgress_MASK 0xffff0000

typedef union {
  struct {
    UINT32                    HST_EnableEarlyCompClkReqIngress:16;
    UINT32                     HST_EnableEarlyCompClkReqEgress:16;
  } Field;
  UINT32 Value;
} SDPMUX_HST_COMP_EARLY_WAKE_UP_EN_STRUCT;

#define SMN_SDPMUX_HST_COMP_EARLY_WAKE_UP_EN_ADDRESS                  0x4400020UL
#define SMN_IOHUB0NBIO0_SDPMUX_HST_COMP_EARLY_WAKE_UP_EN_ADDRESS      0x4400020UL
#define SMN_IOHUB0NBIO1_SDPMUX_HST_COMP_EARLY_WAKE_UP_EN_ADDRESS      0x4e00020UL


/***********************************************************
* Register Name : SDPMUX_HST_ORIG_EARLY_WAKE_UP_EN
************************************************************/

#define SDPMUX_HST_ORIG_EARLY_WAKE_UP_EN_HST_EnableEarlyOrigClkReqIngress_OFFSET 0
#define SDPMUX_HST_ORIG_EARLY_WAKE_UP_EN_HST_EnableEarlyOrigClkReqIngress_MASK 0xffff
#define SDPMUX_HST_ORIG_EARLY_WAKE_UP_EN_HST_EnableEarlyOrigClkReqIngress_DEFAULT     0x1

#define SDPMUX_HST_ORIG_EARLY_WAKE_UP_EN_HST_EnableEarlyOrigClkReqEgress_OFFSET 16
#define SDPMUX_HST_ORIG_EARLY_WAKE_UP_EN_HST_EnableEarlyOrigClkReqEgress_MASK 0xffff0000
#define SDPMUX_HST_ORIG_EARLY_WAKE_UP_EN_HST_EnableEarlyOrigClkReqEgress_DEFAULT     0x2

typedef union {
  struct {
    UINT32                    HST_EnableEarlyOrigClkReqIngress:16;
    UINT32                     HST_EnableEarlyOrigClkReqEgress:16;
  } Field;
  UINT32 Value;
} SDPMUX_HST_ORIG_EARLY_WAKE_UP_EN_STRUCT;

#define SMN_SDPMUX_HST_ORIG_EARLY_WAKE_UP_EN_ADDRESS                  0x4400014UL
#define SMN_IOHUB0NBIO0_SDPMUX_HST_ORIG_EARLY_WAKE_UP_EN_ADDRESS      0x4400014UL
#define SMN_IOHUB0NBIO1_SDPMUX_HST_ORIG_EARLY_WAKE_UP_EN_ADDRESS      0x4e00014UL


/***********************************************************
* Register Name : SDPMUX_NTB_COMP_EARLY_WAKE_UP_EN
************************************************************/

#define SDPMUX_NTB_COMP_EARLY_WAKE_UP_EN_NTB_EnableEarlyCompClkReqIngress_OFFSET 0
#define SDPMUX_NTB_COMP_EARLY_WAKE_UP_EN_NTB_EnableEarlyCompClkReqIngress_MASK 0xffff

#define SDPMUX_NTB_COMP_EARLY_WAKE_UP_EN_NTB_EnableEarlyCompClkReqEgress_OFFSET 16
#define SDPMUX_NTB_COMP_EARLY_WAKE_UP_EN_NTB_EnableEarlyCompClkReqEgress_MASK 0xffff0000

typedef union {
  struct {
    UINT32                    NTB_EnableEarlyCompClkReqIngress:16;
    UINT32                     NTB_EnableEarlyCompClkReqEgress:16;
  } Field;
  UINT32 Value;
} SDPMUX_NTB_COMP_EARLY_WAKE_UP_EN_STRUCT;

#define SMN_SDPMUX_NTB_COMP_EARLY_WAKE_UP_EN_ADDRESS                  0x4400028UL
#define SMN_IOHUB0NBIO0_SDPMUX_NTB_COMP_EARLY_WAKE_UP_EN_ADDRESS      0x4400028UL
#define SMN_IOHUB0NBIO1_SDPMUX_NTB_COMP_EARLY_WAKE_UP_EN_ADDRESS      0x4e00028UL


/***********************************************************
* Register Name : SDPMUX_NTB_ORIG_EARLY_WAKE_UP_EN
************************************************************/

#define SDPMUX_NTB_ORIG_EARLY_WAKE_UP_EN_NTB_EnableEarlyOrigClkReqIngress_OFFSET 0
#define SDPMUX_NTB_ORIG_EARLY_WAKE_UP_EN_NTB_EnableEarlyOrigClkReqIngress_MASK 0xffff
#define SDPMUX_NTB_ORIG_EARLY_WAKE_UP_EN_NTB_EnableEarlyOrigClkReqIngress_DEFAULT     0x4

#define SDPMUX_NTB_ORIG_EARLY_WAKE_UP_EN_NTB_EnableEarlyOrigClkReqEgress_OFFSET 16
#define SDPMUX_NTB_ORIG_EARLY_WAKE_UP_EN_NTB_EnableEarlyOrigClkReqEgress_MASK 0xffff0000
#define SDPMUX_NTB_ORIG_EARLY_WAKE_UP_EN_NTB_EnableEarlyOrigClkReqEgress_DEFAULT     0x2

typedef union {
  struct {
    UINT32                    NTB_EnableEarlyOrigClkReqIngress:16;
    UINT32                     NTB_EnableEarlyOrigClkReqEgress:16;
  } Field;
  UINT32 Value;
} SDPMUX_NTB_ORIG_EARLY_WAKE_UP_EN_STRUCT;

#define SMN_SDPMUX_NTB_ORIG_EARLY_WAKE_UP_EN_ADDRESS                  0x440001cUL
#define SMN_IOHUB0NBIO0_SDPMUX_NTB_ORIG_EARLY_WAKE_UP_EN_ADDRESS      0x440001cUL
#define SMN_IOHUB0NBIO1_SDPMUX_NTB_ORIG_EARLY_WAKE_UP_EN_ADDRESS      0x4e0001cUL


/***********************************************************
* Register Name : SDPMUX_ORIG_TO_COMP_EN
************************************************************/

#define SDPMUX_ORIG_TO_COMP_EN_EnableOrigtoCompClkReq_OFFSET   0
#define SDPMUX_ORIG_TO_COMP_EN_EnableOrigtoCompClkReq_MASK     0xffffffff

typedef union {
  struct {
    UINT32                              EnableOrigtoCompClkReq:32;
  } Field;
  UINT32 Value;
} SDPMUX_ORIG_TO_COMP_EN_STRUCT;

#define SMN_SDPMUX_ORIG_TO_COMP_EN_ADDRESS                            0x440002cUL
#define SMN_IOHUB0NBIO0_SDPMUX_ORIG_TO_COMP_EN_ADDRESS                0x440002cUL
#define SMN_IOHUB0NBIO1_SDPMUX_ORIG_TO_COMP_EN_ADDRESS                0x4e0002cUL


/***********************************************************
* Register Name : SDPMUX_PASSTHROUGH_CTRL
************************************************************/

#define SDPMUX_PASSTHROUGH_CTRL_SDPMUX_Passthrough_Dis_OFFSET  0
#define SDPMUX_PASSTHROUGH_CTRL_SDPMUX_Passthrough_Dis_MASK    0x1

#define SDPMUX_PASSTHROUGH_CTRL_Reserved_31_1_OFFSET           1
#define SDPMUX_PASSTHROUGH_CTRL_Reserved_31_1_MASK             0xfffffffe

typedef union {
  struct {
    UINT32                              SDPMUX_Passthrough_Dis:1;
    UINT32                                       Reserved_31_1:31;
  } Field;
  UINT32 Value;
} SDPMUX_PASSTHROUGH_CTRL_STRUCT;

#define SMN_SDPMUX_PASSTHROUGH_CTRL_ADDRESS                           0x4400030UL
#define SMN_IOHUB0NBIO0_SDPMUX_PASSTHROUGH_CTRL_ADDRESS               0x4400030UL
#define SMN_IOHUB0NBIO1_SDPMUX_PASSTHROUGH_CTRL_ADDRESS               0x4e00030UL


/***********************************************************
* Register Name : SDPMUX_PERF_CNTL
************************************************************/

#define SDPMUX_PERF_CNTL_EVENT0_SEL_OFFSET                     0
#define SDPMUX_PERF_CNTL_EVENT0_SEL_MASK                       0xff

#define SDPMUX_PERF_CNTL_EVENT1_SEL_OFFSET                     8
#define SDPMUX_PERF_CNTL_EVENT1_SEL_MASK                       0xff00

#define SDPMUX_PERF_CNTL_EVENT2_SEL_OFFSET                     16
#define SDPMUX_PERF_CNTL_EVENT2_SEL_MASK                       0xff0000

#define SDPMUX_PERF_CNTL_EVENT3_SEL_OFFSET                     24
#define SDPMUX_PERF_CNTL_EVENT3_SEL_MASK                       0xff000000

typedef union {
  struct {
    UINT32                                          EVENT0_SEL:8;
    UINT32                                          EVENT1_SEL:8;
    UINT32                                          EVENT2_SEL:8;
    UINT32                                          EVENT3_SEL:8;
  } Field;
  UINT32 Value;
} SDPMUX_PERF_CNTL_STRUCT;

#define SMN_SDPMUX_PERF_CNTL_ADDRESS                                  0x4400040UL
#define SMN_IOHUB0NBIO0_SDPMUX_PERF_CNTL_ADDRESS                      0x4400040UL
#define SMN_IOHUB0NBIO1_SDPMUX_PERF_CNTL_ADDRESS                      0x4e00040UL


/***********************************************************
* Register Name : SDPMUX_PERF_COUNT0
************************************************************/

#define SDPMUX_PERF_COUNT0_COUNTER0_OFFSET                     0
#define SDPMUX_PERF_COUNT0_COUNTER0_MASK                       0xffffffff

typedef union {
  struct {
    UINT32                                            COUNTER0:32;
  } Field;
  UINT32 Value;
} SDPMUX_PERF_COUNT0_STRUCT;

#define SMN_SDPMUX_PERF_COUNT0_ADDRESS                                0x4400044UL
#define SMN_IOHUB0NBIO0_SDPMUX_PERF_COUNT0_ADDRESS                    0x4400044UL
#define SMN_IOHUB0NBIO1_SDPMUX_PERF_COUNT0_ADDRESS                    0x4e00044UL


/***********************************************************
* Register Name : SDPMUX_PERF_COUNT0_UPPER
************************************************************/

#define SDPMUX_PERF_COUNT0_UPPER_COUNTER0_UPPER_OFFSET         0
#define SDPMUX_PERF_COUNT0_UPPER_COUNTER0_UPPER_MASK           0xffffff

#define SDPMUX_PERF_COUNT0_UPPER_Reserved_31_24_OFFSET         24
#define SDPMUX_PERF_COUNT0_UPPER_Reserved_31_24_MASK           0xff000000

typedef union {
  struct {
    UINT32                                      COUNTER0_UPPER:24;
    UINT32                                      Reserved_31_24:8;
  } Field;
  UINT32 Value;
} SDPMUX_PERF_COUNT0_UPPER_STRUCT;

#define SMN_SDPMUX_PERF_COUNT0_UPPER_ADDRESS                          0x4400048UL
#define SMN_IOHUB0NBIO0_SDPMUX_PERF_COUNT0_UPPER_ADDRESS              0x4400048UL
#define SMN_IOHUB0NBIO1_SDPMUX_PERF_COUNT0_UPPER_ADDRESS              0x4e00048UL


/***********************************************************
* Register Name : SDPMUX_PERF_COUNT1
************************************************************/

#define SDPMUX_PERF_COUNT1_COUNTER1_OFFSET                     0
#define SDPMUX_PERF_COUNT1_COUNTER1_MASK                       0xffffffff

typedef union {
  struct {
    UINT32                                            COUNTER1:32;
  } Field;
  UINT32 Value;
} SDPMUX_PERF_COUNT1_STRUCT;

#define SMN_SDPMUX_PERF_COUNT1_ADDRESS                                0x440004cUL
#define SMN_IOHUB0NBIO0_SDPMUX_PERF_COUNT1_ADDRESS                    0x440004cUL
#define SMN_IOHUB0NBIO1_SDPMUX_PERF_COUNT1_ADDRESS                    0x4e0004cUL


/***********************************************************
* Register Name : SDPMUX_PERF_COUNT1_UPPER
************************************************************/

#define SDPMUX_PERF_COUNT1_UPPER_COUNTER1_UPPER_OFFSET         0
#define SDPMUX_PERF_COUNT1_UPPER_COUNTER1_UPPER_MASK           0xffffff

#define SDPMUX_PERF_COUNT1_UPPER_Reserved_31_24_OFFSET         24
#define SDPMUX_PERF_COUNT1_UPPER_Reserved_31_24_MASK           0xff000000

typedef union {
  struct {
    UINT32                                      COUNTER1_UPPER:24;
    UINT32                                      Reserved_31_24:8;
  } Field;
  UINT32 Value;
} SDPMUX_PERF_COUNT1_UPPER_STRUCT;

#define SMN_SDPMUX_PERF_COUNT1_UPPER_ADDRESS                          0x4400050UL
#define SMN_IOHUB0NBIO0_SDPMUX_PERF_COUNT1_UPPER_ADDRESS              0x4400050UL
#define SMN_IOHUB0NBIO1_SDPMUX_PERF_COUNT1_UPPER_ADDRESS              0x4e00050UL


/***********************************************************
* Register Name : SDPMUX_PERF_COUNT2
************************************************************/

#define SDPMUX_PERF_COUNT2_COUNTER2_OFFSET                     0
#define SDPMUX_PERF_COUNT2_COUNTER2_MASK                       0xffffffff

typedef union {
  struct {
    UINT32                                            COUNTER2:32;
  } Field;
  UINT32 Value;
} SDPMUX_PERF_COUNT2_STRUCT;

#define SMN_SDPMUX_PERF_COUNT2_ADDRESS                                0x4400054UL
#define SMN_IOHUB0NBIO0_SDPMUX_PERF_COUNT2_ADDRESS                    0x4400054UL
#define SMN_IOHUB0NBIO1_SDPMUX_PERF_COUNT2_ADDRESS                    0x4e00054UL


/***********************************************************
* Register Name : SDPMUX_PERF_COUNT2_UPPER
************************************************************/

#define SDPMUX_PERF_COUNT2_UPPER_COUNTER2_UPPER_OFFSET         0
#define SDPMUX_PERF_COUNT2_UPPER_COUNTER2_UPPER_MASK           0xffffff

#define SDPMUX_PERF_COUNT2_UPPER_Reserved_31_24_OFFSET         24
#define SDPMUX_PERF_COUNT2_UPPER_Reserved_31_24_MASK           0xff000000

typedef union {
  struct {
    UINT32                                      COUNTER2_UPPER:24;
    UINT32                                      Reserved_31_24:8;
  } Field;
  UINT32 Value;
} SDPMUX_PERF_COUNT2_UPPER_STRUCT;

#define SMN_SDPMUX_PERF_COUNT2_UPPER_ADDRESS                          0x4400058UL
#define SMN_IOHUB0NBIO0_SDPMUX_PERF_COUNT2_UPPER_ADDRESS              0x4400058UL
#define SMN_IOHUB0NBIO1_SDPMUX_PERF_COUNT2_UPPER_ADDRESS              0x4e00058UL


/***********************************************************
* Register Name : SDPMUX_PERF_COUNT3
************************************************************/

#define SDPMUX_PERF_COUNT3_COUNTER3_OFFSET                     0
#define SDPMUX_PERF_COUNT3_COUNTER3_MASK                       0xffffffff

typedef union {
  struct {
    UINT32                                            COUNTER3:32;
  } Field;
  UINT32 Value;
} SDPMUX_PERF_COUNT3_STRUCT;

#define SMN_SDPMUX_PERF_COUNT3_ADDRESS                                0x440005cUL
#define SMN_IOHUB0NBIO0_SDPMUX_PERF_COUNT3_ADDRESS                    0x440005cUL
#define SMN_IOHUB0NBIO1_SDPMUX_PERF_COUNT3_ADDRESS                    0x4e0005cUL


/***********************************************************
* Register Name : SDPMUX_PERF_COUNT3_UPPER
************************************************************/

#define SDPMUX_PERF_COUNT3_UPPER_COUNTER3_UPPER_OFFSET         0
#define SDPMUX_PERF_COUNT3_UPPER_COUNTER3_UPPER_MASK           0xffffff

#define SDPMUX_PERF_COUNT3_UPPER_Reserved_31_24_OFFSET         24
#define SDPMUX_PERF_COUNT3_UPPER_Reserved_31_24_MASK           0xff000000

typedef union {
  struct {
    UINT32                                      COUNTER3_UPPER:24;
    UINT32                                      Reserved_31_24:8;
  } Field;
  UINT32 Value;
} SDPMUX_PERF_COUNT3_UPPER_STRUCT;

#define SMN_SDPMUX_PERF_COUNT3_UPPER_ADDRESS                          0x4400060UL
#define SMN_IOHUB0NBIO0_SDPMUX_PERF_COUNT3_UPPER_ADDRESS              0x4400060UL
#define SMN_IOHUB0NBIO1_SDPMUX_PERF_COUNT3_UPPER_ADDRESS              0x4e00060UL


/***********************************************************
* Register Name : SDPMUX_PGMST_CNTL
************************************************************/

#define SDPMUX_PGMST_CNTL_CFG_PG_HYSTERESIS_OFFSET             0
#define SDPMUX_PGMST_CNTL_CFG_PG_HYSTERESIS_MASK               0xff

#define SDPMUX_PGMST_CNTL_CFG_PG_EN_OFFSET                     8
#define SDPMUX_PGMST_CNTL_CFG_PG_EN_MASK                       0x100

#define SDPMUX_PGMST_CNTL_Reserved_9_9_OFFSET                  9
#define SDPMUX_PGMST_CNTL_Reserved_9_9_MASK                    0x200

#define SDPMUX_PGMST_CNTL_CFG_IDLENESS_COUNT_EN_OFFSET         10
#define SDPMUX_PGMST_CNTL_CFG_IDLENESS_COUNT_EN_MASK           0x3c00

#define SDPMUX_PGMST_CNTL_CFG_FW_PG_EXIT_EN_OFFSET             14
#define SDPMUX_PGMST_CNTL_CFG_FW_PG_EXIT_EN_MASK               0xc000

#define SDPMUX_PGMST_CNTL_Reserved_31_16_OFFSET                16
#define SDPMUX_PGMST_CNTL_Reserved_31_16_MASK                  0xffff0000

typedef union {
  struct {
    UINT32                                   CFG_PG_HYSTERESIS:8;
    UINT32                                           CFG_PG_EN:1;
    UINT32                                        Reserved_9_9:1;
    UINT32                               CFG_IDLENESS_COUNT_EN:4;
    UINT32                                   CFG_FW_PG_EXIT_EN:2;
    UINT32                                      Reserved_31_16:16;
  } Field;
  UINT32 Value;
} SDPMUX_PGMST_CNTL_STRUCT;

#define SMN_SDPMUX_PGMST_CNTL_ADDRESS                                 0x4400080UL
#define SMN_IOHUB0NBIO0_SDPMUX_PGMST_CNTL_ADDRESS                     0x4400080UL
#define SMN_IOHUB0NBIO1_SDPMUX_PGMST_CNTL_ADDRESS                     0x4e00080UL


/***********************************************************
* Register Name : SDPMUX_PGSLV_CNTL
************************************************************/

#define SDPMUX_PGSLV_CNTL_CFG_IDLE_HYSTERESIS_OFFSET           0
#define SDPMUX_PGSLV_CNTL_CFG_IDLE_HYSTERESIS_MASK             0x1f

#define SDPMUX_PGSLV_CNTL_Reserved_31_5_OFFSET                 5
#define SDPMUX_PGSLV_CNTL_Reserved_31_5_MASK                   0xffffffe0

typedef union {
  struct {
    UINT32                                 CFG_IDLE_HYSTERESIS:5;
    UINT32                                       Reserved_31_5:27;
  } Field;
  UINT32 Value;
} SDPMUX_PGSLV_CNTL_STRUCT;

#define SMN_SDPMUX_PGSLV_CNTL_ADDRESS                                 0x4400084UL
#define SMN_IOHUB0NBIO0_SDPMUX_PGSLV_CNTL_ADDRESS                     0x4400084UL
#define SMN_IOHUB0NBIO1_SDPMUX_PGSLV_CNTL_ADDRESS                     0x4e00084UL


/***********************************************************
* Register Name : SDPMUX_RSMU_HCID
************************************************************/

#define SDPMUX_RSMU_HCID_RSMU_HCID_HwRev_OFFSET                0
#define SDPMUX_RSMU_HCID_RSMU_HCID_HwRev_MASK                  0x3f

#define SDPMUX_RSMU_HCID_Reserved_7_6_OFFSET                   6
#define SDPMUX_RSMU_HCID_Reserved_7_6_MASK                     0xc0

#define SDPMUX_RSMU_HCID_RSMU_HCID_HwMinVer_OFFSET             8
#define SDPMUX_RSMU_HCID_RSMU_HCID_HwMinVer_MASK               0x7f00

#define SDPMUX_RSMU_HCID_Reserved_15_15_OFFSET                 15
#define SDPMUX_RSMU_HCID_Reserved_15_15_MASK                   0x8000

#define SDPMUX_RSMU_HCID_RSMU_HCID_HwMajVer_OFFSET             16
#define SDPMUX_RSMU_HCID_RSMU_HCID_HwMajVer_MASK               0x7f0000

#define SDPMUX_RSMU_HCID_Reserved_31_23_OFFSET                 23
#define SDPMUX_RSMU_HCID_Reserved_31_23_MASK                   0xff800000

typedef union {
  struct {
    UINT32                                     RSMU_HCID_HwRev:6;
    UINT32                                        Reserved_7_6:2;
    UINT32                                  RSMU_HCID_HwMinVer:7;
    UINT32                                      Reserved_15_15:1;
    UINT32                                  RSMU_HCID_HwMajVer:7;
    UINT32                                      Reserved_31_23:9;
  } Field;
  UINT32 Value;
} SDPMUX_RSMU_HCID_STRUCT;

#define SMN_SDPMUX_RSMU_HCID_ADDRESS                                  0x4400064UL
#define SMN_IOHUB0NBIO0_SDPMUX_RSMU_HCID_ADDRESS                      0x4400064UL
#define SMN_IOHUB0NBIO1_SDPMUX_RSMU_HCID_ADDRESS                      0x4e00064UL


/***********************************************************
* Register Name : SDPMUX_RSMU_SIID
************************************************************/

#define SDPMUX_RSMU_SIID_RSMU_SIID_SwIfRev_OFFSET              0
#define SDPMUX_RSMU_SIID_RSMU_SIID_SwIfRev_MASK                0x3f

#define SDPMUX_RSMU_SIID_Reserved_7_6_OFFSET                   6
#define SDPMUX_RSMU_SIID_Reserved_7_6_MASK                     0xc0

#define SDPMUX_RSMU_SIID_RSMU_SIID_SwIfMinVer_OFFSET           8
#define SDPMUX_RSMU_SIID_RSMU_SIID_SwIfMinVer_MASK             0x7f00

#define SDPMUX_RSMU_SIID_Reserved_15_15_OFFSET                 15
#define SDPMUX_RSMU_SIID_Reserved_15_15_MASK                   0x8000

#define SDPMUX_RSMU_SIID_RSMU_SIID_SwIfMajVer_OFFSET           16
#define SDPMUX_RSMU_SIID_RSMU_SIID_SwIfMajVer_MASK             0x7f0000

#define SDPMUX_RSMU_SIID_Reserved_31_23_OFFSET                 23
#define SDPMUX_RSMU_SIID_Reserved_31_23_MASK                   0xff800000

typedef union {
  struct {
    UINT32                                   RSMU_SIID_SwIfRev:6;
    UINT32                                        Reserved_7_6:2;
    UINT32                                RSMU_SIID_SwIfMinVer:7;
    UINT32                                      Reserved_15_15:1;
    UINT32                                RSMU_SIID_SwIfMajVer:7;
    UINT32                                      Reserved_31_23:9;
  } Field;
  UINT32 Value;
} SDPMUX_RSMU_SIID_STRUCT;

#define SMN_SDPMUX_RSMU_SIID_ADDRESS                                  0x4400068UL
#define SMN_IOHUB0NBIO0_SDPMUX_RSMU_SIID_ADDRESS                      0x4400068UL
#define SMN_IOHUB0NBIO1_SDPMUX_RSMU_SIID_ADDRESS                      0x4e00068UL


/***********************************************************
* Register Name : SDPMUX_SDP_PORT_CONTROL
************************************************************/

#define SDPMUX_SDP_PORT_CONTROL_Port_Disconnect_Hysteresis_OFFSET 0
#define SDPMUX_SDP_PORT_CONTROL_Port_Disconnect_Hysteresis_MASK 0xfff

#define SDPMUX_SDP_PORT_CONTROL_Reserved_31_12_OFFSET          12
#define SDPMUX_SDP_PORT_CONTROL_Reserved_31_12_MASK            0xfffff000

typedef union {
  struct {
    UINT32                          Port_Disconnect_Hysteresis:12;
    UINT32                                      Reserved_31_12:20;
  } Field;
  UINT32 Value;
} SDPMUX_SDP_PORT_CONTROL_STRUCT;

#define SMN_SDPMUX_SDP_PORT_CONTROL_ADDRESS                           0x4400008UL
#define SMN_IOHUB0NBIO0_SDPMUX_SDP_PORT_CONTROL_ADDRESS               0x4400008UL
#define SMN_IOHUB0NBIO1_SDPMUX_SDP_PORT_CONTROL_ADDRESS               0x4e00008UL


/***********************************************************
* Register Name : SDPMUX_SION_Client_DataPoolCredit_Alloc_0
************************************************************/

#define SDPMUX_SION_Client_DataPoolCredit_Alloc_0_SDPMUX_SION_Client_DataPoolCredit_Alloc_0_OFFSET 0
#define SDPMUX_SION_Client_DataPoolCredit_Alloc_0_SDPMUX_SION_Client_DataPoolCredit_Alloc_0_MASK 0xffffffff

typedef union {
  struct {
    UINT32           SDPMUX_SION_Client_DataPoolCredit_Alloc_0:32;
  } Field;
  UINT32 Value;
} SDPMUX_SION_Client_DataPoolCredit_Alloc_0_STRUCT;

#define SMN_SDPMUX_SION_Client_DataPoolCredit_Alloc_0_ADDRESS         0x4400470UL
#define SMN_IOHUB0_N0NBIO0_SDPMUX_SION_Client_DataPoolCredit_Alloc_0_ADDRESS 0x4400470UL
#define SMN_IOHUB0_N0NBIO1_SDPMUX_SION_Client_DataPoolCredit_Alloc_0_ADDRESS 0x4e00470UL
#define SMN_IOHUB0_N1NBIO0_SDPMUX_SION_Client_DataPoolCredit_Alloc_0_ADDRESS 0x4400870UL
#define SMN_IOHUB0_N1NBIO1_SDPMUX_SION_Client_DataPoolCredit_Alloc_0_ADDRESS 0x4e00870UL
#define SMN_IOHUB0_N2NBIO0_SDPMUX_SION_Client_DataPoolCredit_Alloc_0_ADDRESS 0x4400c70UL
#define SMN_IOHUB0_N2NBIO1_SDPMUX_SION_Client_DataPoolCredit_Alloc_0_ADDRESS 0x4e00c70UL


/***********************************************************
* Register Name : SDPMUX_SION_Client_DataPoolCredit_Alloc_1
************************************************************/

#define SDPMUX_SION_Client_DataPoolCredit_Alloc_1_SDPMUX_SION_Client_DataPoolCredit_Alloc_1_OFFSET 0
#define SDPMUX_SION_Client_DataPoolCredit_Alloc_1_SDPMUX_SION_Client_DataPoolCredit_Alloc_1_MASK 0xffffffff

typedef union {
  struct {
    UINT32           SDPMUX_SION_Client_DataPoolCredit_Alloc_1:32;
  } Field;
  UINT32 Value;
} SDPMUX_SION_Client_DataPoolCredit_Alloc_1_STRUCT;

#define SMN_SDPMUX_SION_Client_DataPoolCredit_Alloc_1_ADDRESS         0x4400474UL
#define SMN_IOHUB0_N0NBIO0_SDPMUX_SION_Client_DataPoolCredit_Alloc_1_ADDRESS 0x4400474UL
#define SMN_IOHUB0_N0NBIO1_SDPMUX_SION_Client_DataPoolCredit_Alloc_1_ADDRESS 0x4e00474UL
#define SMN_IOHUB0_N1NBIO0_SDPMUX_SION_Client_DataPoolCredit_Alloc_1_ADDRESS 0x4400874UL
#define SMN_IOHUB0_N1NBIO1_SDPMUX_SION_Client_DataPoolCredit_Alloc_1_ADDRESS 0x4e00874UL
#define SMN_IOHUB0_N2NBIO0_SDPMUX_SION_Client_DataPoolCredit_Alloc_1_ADDRESS 0x4400c74UL
#define SMN_IOHUB0_N2NBIO1_SDPMUX_SION_Client_DataPoolCredit_Alloc_1_ADDRESS 0x4e00c74UL


/***********************************************************
* Register Name : SDPMUX_SION_Client_DataPoolCredit_Alloc_2
************************************************************/

#define SDPMUX_SION_Client_DataPoolCredit_Alloc_2_SDPMUX_SION_Client_DataPoolCredit_Alloc_2_OFFSET 0
#define SDPMUX_SION_Client_DataPoolCredit_Alloc_2_SDPMUX_SION_Client_DataPoolCredit_Alloc_2_MASK 0xffffffff

typedef union {
  struct {
    UINT32           SDPMUX_SION_Client_DataPoolCredit_Alloc_2:32;
  } Field;
  UINT32 Value;
} SDPMUX_SION_Client_DataPoolCredit_Alloc_2_STRUCT;

#define SMN_SDPMUX_SION_Client_DataPoolCredit_Alloc_2_ADDRESS         0x4400478UL
#define SMN_IOHUB0_N0NBIO0_SDPMUX_SION_Client_DataPoolCredit_Alloc_2_ADDRESS 0x4400478UL
#define SMN_IOHUB0_N0NBIO1_SDPMUX_SION_Client_DataPoolCredit_Alloc_2_ADDRESS 0x4e00478UL
#define SMN_IOHUB0_N1NBIO0_SDPMUX_SION_Client_DataPoolCredit_Alloc_2_ADDRESS 0x4400878UL
#define SMN_IOHUB0_N1NBIO1_SDPMUX_SION_Client_DataPoolCredit_Alloc_2_ADDRESS 0x4e00878UL
#define SMN_IOHUB0_N2NBIO0_SDPMUX_SION_Client_DataPoolCredit_Alloc_2_ADDRESS 0x4400c78UL
#define SMN_IOHUB0_N2NBIO1_SDPMUX_SION_Client_DataPoolCredit_Alloc_2_ADDRESS 0x4e00c78UL


/***********************************************************
* Register Name : SDPMUX_SION_Client_DataPoolCredit_Alloc_3
************************************************************/

#define SDPMUX_SION_Client_DataPoolCredit_Alloc_3_SDPMUX_SION_Client_DataPoolCredit_Alloc_3_OFFSET 0
#define SDPMUX_SION_Client_DataPoolCredit_Alloc_3_SDPMUX_SION_Client_DataPoolCredit_Alloc_3_MASK 0xffffffff

typedef union {
  struct {
    UINT32           SDPMUX_SION_Client_DataPoolCredit_Alloc_3:32;
  } Field;
  UINT32 Value;
} SDPMUX_SION_Client_DataPoolCredit_Alloc_3_STRUCT;

#define SMN_SDPMUX_SION_Client_DataPoolCredit_Alloc_3_ADDRESS         0x440047cUL
#define SMN_IOHUB0_N0NBIO0_SDPMUX_SION_Client_DataPoolCredit_Alloc_3_ADDRESS 0x440047cUL
#define SMN_IOHUB0_N0NBIO1_SDPMUX_SION_Client_DataPoolCredit_Alloc_3_ADDRESS 0x4e0047cUL
#define SMN_IOHUB0_N1NBIO0_SDPMUX_SION_Client_DataPoolCredit_Alloc_3_ADDRESS 0x440087cUL
#define SMN_IOHUB0_N1NBIO1_SDPMUX_SION_Client_DataPoolCredit_Alloc_3_ADDRESS 0x4e0087cUL
#define SMN_IOHUB0_N2NBIO0_SDPMUX_SION_Client_DataPoolCredit_Alloc_3_ADDRESS 0x4400c7cUL
#define SMN_IOHUB0_N2NBIO1_SDPMUX_SION_Client_DataPoolCredit_Alloc_3_ADDRESS 0x4e00c7cUL


/***********************************************************
* Register Name : SDPMUX_SION_Client_RdRspPoolCredit_Alloc_0
************************************************************/

#define SDPMUX_SION_Client_RdRspPoolCredit_Alloc_0_SDPMUX_SION_Client_RdRspPoolCredit_Alloc_0_OFFSET 0
#define SDPMUX_SION_Client_RdRspPoolCredit_Alloc_0_SDPMUX_SION_Client_RdRspPoolCredit_Alloc_0_MASK 0xffffffff

typedef union {
  struct {
    UINT32          SDPMUX_SION_Client_RdRspPoolCredit_Alloc_0:32;
  } Field;
  UINT32 Value;
} SDPMUX_SION_Client_RdRspPoolCredit_Alloc_0_STRUCT;

#define SMN_SDPMUX_SION_Client_RdRspPoolCredit_Alloc_0_ADDRESS        0x4400480UL
#define SMN_IOHUB0_N0NBIO0_SDPMUX_SION_Client_RdRspPoolCredit_Alloc_0_ADDRESS 0x4400480UL
#define SMN_IOHUB0_N0NBIO1_SDPMUX_SION_Client_RdRspPoolCredit_Alloc_0_ADDRESS 0x4e00480UL
#define SMN_IOHUB0_N1NBIO0_SDPMUX_SION_Client_RdRspPoolCredit_Alloc_0_ADDRESS 0x4400880UL
#define SMN_IOHUB0_N1NBIO1_SDPMUX_SION_Client_RdRspPoolCredit_Alloc_0_ADDRESS 0x4e00880UL
#define SMN_IOHUB0_N2NBIO0_SDPMUX_SION_Client_RdRspPoolCredit_Alloc_0_ADDRESS 0x4400c80UL
#define SMN_IOHUB0_N2NBIO1_SDPMUX_SION_Client_RdRspPoolCredit_Alloc_0_ADDRESS 0x4e00c80UL


/***********************************************************
* Register Name : SDPMUX_SION_Client_RdRspPoolCredit_Alloc_1
************************************************************/

#define SDPMUX_SION_Client_RdRspPoolCredit_Alloc_1_SDPMUX_SION_Client_RdRspPoolCredit_Alloc_1_OFFSET 0
#define SDPMUX_SION_Client_RdRspPoolCredit_Alloc_1_SDPMUX_SION_Client_RdRspPoolCredit_Alloc_1_MASK 0xffffffff

typedef union {
  struct {
    UINT32          SDPMUX_SION_Client_RdRspPoolCredit_Alloc_1:32;
  } Field;
  UINT32 Value;
} SDPMUX_SION_Client_RdRspPoolCredit_Alloc_1_STRUCT;

#define SMN_SDPMUX_SION_Client_RdRspPoolCredit_Alloc_1_ADDRESS        0x4400484UL
#define SMN_IOHUB0_N0NBIO0_SDPMUX_SION_Client_RdRspPoolCredit_Alloc_1_ADDRESS 0x4400484UL
#define SMN_IOHUB0_N0NBIO1_SDPMUX_SION_Client_RdRspPoolCredit_Alloc_1_ADDRESS 0x4e00484UL
#define SMN_IOHUB0_N1NBIO0_SDPMUX_SION_Client_RdRspPoolCredit_Alloc_1_ADDRESS 0x4400884UL
#define SMN_IOHUB0_N1NBIO1_SDPMUX_SION_Client_RdRspPoolCredit_Alloc_1_ADDRESS 0x4e00884UL
#define SMN_IOHUB0_N2NBIO0_SDPMUX_SION_Client_RdRspPoolCredit_Alloc_1_ADDRESS 0x4400c84UL
#define SMN_IOHUB0_N2NBIO1_SDPMUX_SION_Client_RdRspPoolCredit_Alloc_1_ADDRESS 0x4e00c84UL


/***********************************************************
* Register Name : SDPMUX_SION_Client_RdRspPoolCredit_Alloc_2
************************************************************/

#define SDPMUX_SION_Client_RdRspPoolCredit_Alloc_2_SDPMUX_SION_Client_RdRspPoolCredit_Alloc_2_OFFSET 0
#define SDPMUX_SION_Client_RdRspPoolCredit_Alloc_2_SDPMUX_SION_Client_RdRspPoolCredit_Alloc_2_MASK 0xffffffff

typedef union {
  struct {
    UINT32          SDPMUX_SION_Client_RdRspPoolCredit_Alloc_2:32;
  } Field;
  UINT32 Value;
} SDPMUX_SION_Client_RdRspPoolCredit_Alloc_2_STRUCT;

#define SMN_SDPMUX_SION_Client_RdRspPoolCredit_Alloc_2_ADDRESS        0x4400488UL
#define SMN_IOHUB0_N0NBIO0_SDPMUX_SION_Client_RdRspPoolCredit_Alloc_2_ADDRESS 0x4400488UL
#define SMN_IOHUB0_N0NBIO1_SDPMUX_SION_Client_RdRspPoolCredit_Alloc_2_ADDRESS 0x4e00488UL
#define SMN_IOHUB0_N1NBIO0_SDPMUX_SION_Client_RdRspPoolCredit_Alloc_2_ADDRESS 0x4400888UL
#define SMN_IOHUB0_N1NBIO1_SDPMUX_SION_Client_RdRspPoolCredit_Alloc_2_ADDRESS 0x4e00888UL
#define SMN_IOHUB0_N2NBIO0_SDPMUX_SION_Client_RdRspPoolCredit_Alloc_2_ADDRESS 0x4400c88UL
#define SMN_IOHUB0_N2NBIO1_SDPMUX_SION_Client_RdRspPoolCredit_Alloc_2_ADDRESS 0x4e00c88UL


/***********************************************************
* Register Name : SDPMUX_SION_Client_RdRspPoolCredit_Alloc_3
************************************************************/

#define SDPMUX_SION_Client_RdRspPoolCredit_Alloc_3_SDPMUX_SION_Client_RdRspPoolCredit_Alloc_3_OFFSET 0
#define SDPMUX_SION_Client_RdRspPoolCredit_Alloc_3_SDPMUX_SION_Client_RdRspPoolCredit_Alloc_3_MASK 0xffffffff

typedef union {
  struct {
    UINT32          SDPMUX_SION_Client_RdRspPoolCredit_Alloc_3:32;
  } Field;
  UINT32 Value;
} SDPMUX_SION_Client_RdRspPoolCredit_Alloc_3_STRUCT;

#define SMN_SDPMUX_SION_Client_RdRspPoolCredit_Alloc_3_ADDRESS        0x440048cUL
#define SMN_IOHUB0_N0NBIO0_SDPMUX_SION_Client_RdRspPoolCredit_Alloc_3_ADDRESS 0x440048cUL
#define SMN_IOHUB0_N0NBIO1_SDPMUX_SION_Client_RdRspPoolCredit_Alloc_3_ADDRESS 0x4e0048cUL
#define SMN_IOHUB0_N1NBIO0_SDPMUX_SION_Client_RdRspPoolCredit_Alloc_3_ADDRESS 0x440088cUL
#define SMN_IOHUB0_N1NBIO1_SDPMUX_SION_Client_RdRspPoolCredit_Alloc_3_ADDRESS 0x4e0088cUL
#define SMN_IOHUB0_N2NBIO0_SDPMUX_SION_Client_RdRspPoolCredit_Alloc_3_ADDRESS 0x4400c8cUL
#define SMN_IOHUB0_N2NBIO1_SDPMUX_SION_Client_RdRspPoolCredit_Alloc_3_ADDRESS 0x4e00c8cUL


/***********************************************************
* Register Name : SDPMUX_SION_Client_ReqPoolCredit_Alloc_0
************************************************************/

#define SDPMUX_SION_Client_ReqPoolCredit_Alloc_0_SDPMUX_SION_Client_ReqPoolCredit_Alloc_0_OFFSET 0
#define SDPMUX_SION_Client_ReqPoolCredit_Alloc_0_SDPMUX_SION_Client_ReqPoolCredit_Alloc_0_MASK 0xffffffff

typedef union {
  struct {
    UINT32            SDPMUX_SION_Client_ReqPoolCredit_Alloc_0:32;
  } Field;
  UINT32 Value;
} SDPMUX_SION_Client_ReqPoolCredit_Alloc_0_STRUCT;

#define SMN_SDPMUX_SION_Client_ReqPoolCredit_Alloc_0_ADDRESS          0x4400460UL
#define SMN_IOHUB0_N0NBIO0_SDPMUX_SION_Client_ReqPoolCredit_Alloc_0_ADDRESS 0x4400460UL
#define SMN_IOHUB0_N0NBIO1_SDPMUX_SION_Client_ReqPoolCredit_Alloc_0_ADDRESS 0x4e00460UL
#define SMN_IOHUB0_N1NBIO0_SDPMUX_SION_Client_ReqPoolCredit_Alloc_0_ADDRESS 0x4400860UL
#define SMN_IOHUB0_N1NBIO1_SDPMUX_SION_Client_ReqPoolCredit_Alloc_0_ADDRESS 0x4e00860UL
#define SMN_IOHUB0_N2NBIO0_SDPMUX_SION_Client_ReqPoolCredit_Alloc_0_ADDRESS 0x4400c60UL
#define SMN_IOHUB0_N2NBIO1_SDPMUX_SION_Client_ReqPoolCredit_Alloc_0_ADDRESS 0x4e00c60UL


/***********************************************************
* Register Name : SDPMUX_SION_Client_ReqPoolCredit_Alloc_1
************************************************************/

#define SDPMUX_SION_Client_ReqPoolCredit_Alloc_1_SDPMUX_SION_Client_ReqPoolCredit_Alloc_1_OFFSET 0
#define SDPMUX_SION_Client_ReqPoolCredit_Alloc_1_SDPMUX_SION_Client_ReqPoolCredit_Alloc_1_MASK 0xffffffff

typedef union {
  struct {
    UINT32            SDPMUX_SION_Client_ReqPoolCredit_Alloc_1:32;
  } Field;
  UINT32 Value;
} SDPMUX_SION_Client_ReqPoolCredit_Alloc_1_STRUCT;

#define SMN_SDPMUX_SION_Client_ReqPoolCredit_Alloc_1_ADDRESS          0x4400464UL
#define SMN_IOHUB0_N0NBIO0_SDPMUX_SION_Client_ReqPoolCredit_Alloc_1_ADDRESS 0x4400464UL
#define SMN_IOHUB0_N0NBIO1_SDPMUX_SION_Client_ReqPoolCredit_Alloc_1_ADDRESS 0x4e00464UL
#define SMN_IOHUB0_N1NBIO0_SDPMUX_SION_Client_ReqPoolCredit_Alloc_1_ADDRESS 0x4400864UL
#define SMN_IOHUB0_N1NBIO1_SDPMUX_SION_Client_ReqPoolCredit_Alloc_1_ADDRESS 0x4e00864UL
#define SMN_IOHUB0_N2NBIO0_SDPMUX_SION_Client_ReqPoolCredit_Alloc_1_ADDRESS 0x4400c64UL
#define SMN_IOHUB0_N2NBIO1_SDPMUX_SION_Client_ReqPoolCredit_Alloc_1_ADDRESS 0x4e00c64UL


/***********************************************************
* Register Name : SDPMUX_SION_Client_ReqPoolCredit_Alloc_2
************************************************************/

#define SDPMUX_SION_Client_ReqPoolCredit_Alloc_2_SDPMUX_SION_Client_ReqPoolCredit_Alloc_2_OFFSET 0
#define SDPMUX_SION_Client_ReqPoolCredit_Alloc_2_SDPMUX_SION_Client_ReqPoolCredit_Alloc_2_MASK 0xffffffff

typedef union {
  struct {
    UINT32            SDPMUX_SION_Client_ReqPoolCredit_Alloc_2:32;
  } Field;
  UINT32 Value;
} SDPMUX_SION_Client_ReqPoolCredit_Alloc_2_STRUCT;

#define SMN_SDPMUX_SION_Client_ReqPoolCredit_Alloc_2_ADDRESS          0x4400468UL
#define SMN_IOHUB0_N0NBIO0_SDPMUX_SION_Client_ReqPoolCredit_Alloc_2_ADDRESS 0x4400468UL
#define SMN_IOHUB0_N0NBIO1_SDPMUX_SION_Client_ReqPoolCredit_Alloc_2_ADDRESS 0x4e00468UL
#define SMN_IOHUB0_N1NBIO0_SDPMUX_SION_Client_ReqPoolCredit_Alloc_2_ADDRESS 0x4400868UL
#define SMN_IOHUB0_N1NBIO1_SDPMUX_SION_Client_ReqPoolCredit_Alloc_2_ADDRESS 0x4e00868UL
#define SMN_IOHUB0_N2NBIO0_SDPMUX_SION_Client_ReqPoolCredit_Alloc_2_ADDRESS 0x4400c68UL
#define SMN_IOHUB0_N2NBIO1_SDPMUX_SION_Client_ReqPoolCredit_Alloc_2_ADDRESS 0x4e00c68UL


/***********************************************************
* Register Name : SDPMUX_SION_Client_ReqPoolCredit_Alloc_3
************************************************************/

#define SDPMUX_SION_Client_ReqPoolCredit_Alloc_3_SDPMUX_SION_Client_ReqPoolCredit_Alloc_3_OFFSET 0
#define SDPMUX_SION_Client_ReqPoolCredit_Alloc_3_SDPMUX_SION_Client_ReqPoolCredit_Alloc_3_MASK 0xffffffff

typedef union {
  struct {
    UINT32            SDPMUX_SION_Client_ReqPoolCredit_Alloc_3:32;
  } Field;
  UINT32 Value;
} SDPMUX_SION_Client_ReqPoolCredit_Alloc_3_STRUCT;

#define SMN_SDPMUX_SION_Client_ReqPoolCredit_Alloc_3_ADDRESS          0x440046cUL
#define SMN_IOHUB0_N0NBIO0_SDPMUX_SION_Client_ReqPoolCredit_Alloc_3_ADDRESS 0x440046cUL
#define SMN_IOHUB0_N0NBIO1_SDPMUX_SION_Client_ReqPoolCredit_Alloc_3_ADDRESS 0x4e0046cUL
#define SMN_IOHUB0_N1NBIO0_SDPMUX_SION_Client_ReqPoolCredit_Alloc_3_ADDRESS 0x440086cUL
#define SMN_IOHUB0_N1NBIO1_SDPMUX_SION_Client_ReqPoolCredit_Alloc_3_ADDRESS 0x4e0086cUL
#define SMN_IOHUB0_N2NBIO0_SDPMUX_SION_Client_ReqPoolCredit_Alloc_3_ADDRESS 0x4400c6cUL
#define SMN_IOHUB0_N2NBIO1_SDPMUX_SION_Client_ReqPoolCredit_Alloc_3_ADDRESS 0x4e00c6cUL


/***********************************************************
* Register Name : SDPMUX_SION_Client_WrRspPoolCredit_Alloc_0
************************************************************/

#define SDPMUX_SION_Client_WrRspPoolCredit_Alloc_0_SDPMUX_SION_Client_WrRspPoolCredit_Alloc_0_OFFSET 0
#define SDPMUX_SION_Client_WrRspPoolCredit_Alloc_0_SDPMUX_SION_Client_WrRspPoolCredit_Alloc_0_MASK 0xffffffff

typedef union {
  struct {
    UINT32          SDPMUX_SION_Client_WrRspPoolCredit_Alloc_0:32;
  } Field;
  UINT32 Value;
} SDPMUX_SION_Client_WrRspPoolCredit_Alloc_0_STRUCT;

#define SMN_SDPMUX_SION_Client_WrRspPoolCredit_Alloc_0_ADDRESS        0x4400490UL
#define SMN_IOHUB0_N0NBIO0_SDPMUX_SION_Client_WrRspPoolCredit_Alloc_0_ADDRESS 0x4400490UL
#define SMN_IOHUB0_N0NBIO1_SDPMUX_SION_Client_WrRspPoolCredit_Alloc_0_ADDRESS 0x4e00490UL
#define SMN_IOHUB0_N1NBIO0_SDPMUX_SION_Client_WrRspPoolCredit_Alloc_0_ADDRESS 0x4400890UL
#define SMN_IOHUB0_N1NBIO1_SDPMUX_SION_Client_WrRspPoolCredit_Alloc_0_ADDRESS 0x4e00890UL
#define SMN_IOHUB0_N2NBIO0_SDPMUX_SION_Client_WrRspPoolCredit_Alloc_0_ADDRESS 0x4400c90UL
#define SMN_IOHUB0_N2NBIO1_SDPMUX_SION_Client_WrRspPoolCredit_Alloc_0_ADDRESS 0x4e00c90UL


/***********************************************************
* Register Name : SDPMUX_SION_Client_WrRspPoolCredit_Alloc_1
************************************************************/

#define SDPMUX_SION_Client_WrRspPoolCredit_Alloc_1_SDPMUX_SION_Client_WrRspPoolCredit_Alloc_1_OFFSET 0
#define SDPMUX_SION_Client_WrRspPoolCredit_Alloc_1_SDPMUX_SION_Client_WrRspPoolCredit_Alloc_1_MASK 0xffffffff

typedef union {
  struct {
    UINT32          SDPMUX_SION_Client_WrRspPoolCredit_Alloc_1:32;
  } Field;
  UINT32 Value;
} SDPMUX_SION_Client_WrRspPoolCredit_Alloc_1_STRUCT;

#define SMN_SDPMUX_SION_Client_WrRspPoolCredit_Alloc_1_ADDRESS        0x4400494UL
#define SMN_IOHUB0_N0NBIO0_SDPMUX_SION_Client_WrRspPoolCredit_Alloc_1_ADDRESS 0x4400494UL
#define SMN_IOHUB0_N0NBIO1_SDPMUX_SION_Client_WrRspPoolCredit_Alloc_1_ADDRESS 0x4e00494UL
#define SMN_IOHUB0_N1NBIO0_SDPMUX_SION_Client_WrRspPoolCredit_Alloc_1_ADDRESS 0x4400894UL
#define SMN_IOHUB0_N1NBIO1_SDPMUX_SION_Client_WrRspPoolCredit_Alloc_1_ADDRESS 0x4e00894UL
#define SMN_IOHUB0_N2NBIO0_SDPMUX_SION_Client_WrRspPoolCredit_Alloc_1_ADDRESS 0x4400c94UL
#define SMN_IOHUB0_N2NBIO1_SDPMUX_SION_Client_WrRspPoolCredit_Alloc_1_ADDRESS 0x4e00c94UL


/***********************************************************
* Register Name : SDPMUX_SION_Client_WrRspPoolCredit_Alloc_2
************************************************************/

#define SDPMUX_SION_Client_WrRspPoolCredit_Alloc_2_SDPMUX_SION_Client_WrRspPoolCredit_Alloc_2_OFFSET 0
#define SDPMUX_SION_Client_WrRspPoolCredit_Alloc_2_SDPMUX_SION_Client_WrRspPoolCredit_Alloc_2_MASK 0xffffffff

typedef union {
  struct {
    UINT32          SDPMUX_SION_Client_WrRspPoolCredit_Alloc_2:32;
  } Field;
  UINT32 Value;
} SDPMUX_SION_Client_WrRspPoolCredit_Alloc_2_STRUCT;

#define SMN_SDPMUX_SION_Client_WrRspPoolCredit_Alloc_2_ADDRESS        0x4400498UL
#define SMN_IOHUB0_N0NBIO0_SDPMUX_SION_Client_WrRspPoolCredit_Alloc_2_ADDRESS 0x4400498UL
#define SMN_IOHUB0_N0NBIO1_SDPMUX_SION_Client_WrRspPoolCredit_Alloc_2_ADDRESS 0x4e00498UL
#define SMN_IOHUB0_N1NBIO0_SDPMUX_SION_Client_WrRspPoolCredit_Alloc_2_ADDRESS 0x4400898UL
#define SMN_IOHUB0_N1NBIO1_SDPMUX_SION_Client_WrRspPoolCredit_Alloc_2_ADDRESS 0x4e00898UL
#define SMN_IOHUB0_N2NBIO0_SDPMUX_SION_Client_WrRspPoolCredit_Alloc_2_ADDRESS 0x4400c98UL
#define SMN_IOHUB0_N2NBIO1_SDPMUX_SION_Client_WrRspPoolCredit_Alloc_2_ADDRESS 0x4e00c98UL


/***********************************************************
* Register Name : SDPMUX_SION_Client_WrRspPoolCredit_Alloc_3
************************************************************/

#define SDPMUX_SION_Client_WrRspPoolCredit_Alloc_3_SDPMUX_SION_Client_WrRspPoolCredit_Alloc_3_OFFSET 0
#define SDPMUX_SION_Client_WrRspPoolCredit_Alloc_3_SDPMUX_SION_Client_WrRspPoolCredit_Alloc_3_MASK 0xffffffff

typedef union {
  struct {
    UINT32          SDPMUX_SION_Client_WrRspPoolCredit_Alloc_3:32;
  } Field;
  UINT32 Value;
} SDPMUX_SION_Client_WrRspPoolCredit_Alloc_3_STRUCT;

#define SMN_SDPMUX_SION_Client_WrRspPoolCredit_Alloc_3_ADDRESS        0x440049cUL
#define SMN_IOHUB0_N0NBIO0_SDPMUX_SION_Client_WrRspPoolCredit_Alloc_3_ADDRESS 0x440049cUL
#define SMN_IOHUB0_N0NBIO1_SDPMUX_SION_Client_WrRspPoolCredit_Alloc_3_ADDRESS 0x4e0049cUL
#define SMN_IOHUB0_N1NBIO0_SDPMUX_SION_Client_WrRspPoolCredit_Alloc_3_ADDRESS 0x440089cUL
#define SMN_IOHUB0_N1NBIO1_SDPMUX_SION_Client_WrRspPoolCredit_Alloc_3_ADDRESS 0x4e0089cUL
#define SMN_IOHUB0_N2NBIO0_SDPMUX_SION_Client_WrRspPoolCredit_Alloc_3_ADDRESS 0x4400c9cUL
#define SMN_IOHUB0_N2NBIO1_SDPMUX_SION_Client_WrRspPoolCredit_Alloc_3_ADDRESS 0x4e00c9cUL


/***********************************************************
* Register Name : SDPMUX_SION_LiveLock_WatchDog_Threshold
************************************************************/

#define SDPMUX_SION_LiveLock_WatchDog_Threshold_SDPMUX_SION_LiveLock_WatchDog_Threshold_OFFSET 0
#define SDPMUX_SION_LiveLock_WatchDog_Threshold_SDPMUX_SION_LiveLock_WatchDog_Threshold_MASK 0xff

#define SDPMUX_SION_LiveLock_WatchDog_Threshold_Reserved_31_8_OFFSET 8
#define SDPMUX_SION_LiveLock_WatchDog_Threshold_Reserved_31_8_MASK 0xffffff00

typedef union {
  struct {
    UINT32             SDPMUX_SION_LiveLock_WatchDog_Threshold:8;
    UINT32                                       Reserved_31_8:24;
  } Field;
  UINT32 Value;
} SDPMUX_SION_LiveLock_WatchDog_Threshold_STRUCT;

#define SMN_SDPMUX_SION_LiveLock_WatchDog_Threshold_ADDRESS           0x4400ca0UL
#define SMN_IOHUB0NBIO0_SDPMUX_SION_LiveLock_WatchDog_Threshold_ADDRESS 0x4400ca0UL
#define SMN_IOHUB0NBIO1_SDPMUX_SION_LiveLock_WatchDog_Threshold_ADDRESS 0x4e00ca0UL


/***********************************************************
* Register Name : SDPMUX_SION_PERF_CNT_CNTL0
************************************************************/

#define SDPMUX_SION_PERF_CNT_CNTL0_SDPMUX_SION_CNT_EN_OFFSET   0
#define SDPMUX_SION_PERF_CNT_CNTL0_SDPMUX_SION_CNT_EN_MASK     0x1

#define SDPMUX_SION_PERF_CNT_CNTL0_SDPMUX_SION_SHADOW_WR_OFFSET 1
#define SDPMUX_SION_PERF_CNT_CNTL0_SDPMUX_SION_SHADOW_WR_MASK  0x2

#define SDPMUX_SION_PERF_CNT_CNTL0_SDPMUX_SION_PERF_RESET_OFFSET 2
#define SDPMUX_SION_PERF_CNT_CNTL0_SDPMUX_SION_PERF_RESET_MASK 0x4

#define SDPMUX_SION_PERF_CNT_CNTL0_Reserved_7_3_OFFSET         3
#define SDPMUX_SION_PERF_CNT_CNTL0_Reserved_7_3_MASK           0xf8

#define SDPMUX_SION_PERF_CNT_CNTL0_SDPMUX_SION_SHADOW_DELAY_OFFSET 8
#define SDPMUX_SION_PERF_CNT_CNTL0_SDPMUX_SION_SHADOW_DELAY_MASK 0xf00

#define SDPMUX_SION_PERF_CNT_CNTL0_Reserved_14_12_OFFSET       12
#define SDPMUX_SION_PERF_CNT_CNTL0_Reserved_14_12_MASK         0x7000

#define SDPMUX_SION_PERF_CNT_CNTL0_SDPMUX_SION_SHADOW_DELAY_EN_OFFSET 15
#define SDPMUX_SION_PERF_CNT_CNTL0_SDPMUX_SION_SHADOW_DELAY_EN_MASK 0x8000

#define SDPMUX_SION_PERF_CNT_CNTL0_SDPMUX_SION_PERF_RESET_DELAY_OFFSET 16
#define SDPMUX_SION_PERF_CNT_CNTL0_SDPMUX_SION_PERF_RESET_DELAY_MASK 0xf0000

#define SDPMUX_SION_PERF_CNT_CNTL0_Reserved_22_20_OFFSET       20
#define SDPMUX_SION_PERF_CNT_CNTL0_Reserved_22_20_MASK         0x700000

#define SDPMUX_SION_PERF_CNT_CNTL0_SDPMUX_SION_PERF_RESET_DELAY_EN_OFFSET 23
#define SDPMUX_SION_PERF_CNT_CNTL0_SDPMUX_SION_PERF_RESET_DELAY_EN_MASK 0x800000

#define SDPMUX_SION_PERF_CNT_CNTL0_Reserved_31_24_OFFSET       24
#define SDPMUX_SION_PERF_CNT_CNTL0_Reserved_31_24_MASK         0xff000000

typedef union {
  struct {
    UINT32                                  SDPMUX_SION_CNT_EN:1;
    UINT32                               SDPMUX_SION_SHADOW_WR:1;
    UINT32                              SDPMUX_SION_PERF_RESET:1;
    UINT32                                        Reserved_7_3:5;
    UINT32                            SDPMUX_SION_SHADOW_DELAY:4;
    UINT32                                      Reserved_14_12:3;
    UINT32                         SDPMUX_SION_SHADOW_DELAY_EN:1;
    UINT32                        SDPMUX_SION_PERF_RESET_DELAY:4;
    UINT32                                      Reserved_22_20:3;
    UINT32                     SDPMUX_SION_PERF_RESET_DELAY_EN:1;
    UINT32                                      Reserved_31_24:8;
  } Field;
  UINT32 Value;
} SDPMUX_SION_PERF_CNT_CNTL0_STRUCT;

#define SMN_SDPMUX_SION_PERF_CNT_CNTL0_ADDRESS                        0x4400ca4UL
#define SMN_IOHUB0NBIO0_SDPMUX_SION_PERF_CNT_CNTL0_ADDRESS            0x4400ca4UL
#define SMN_IOHUB0NBIO1_SDPMUX_SION_PERF_CNT_CNTL0_ADDRESS            0x4e00ca4UL


/***********************************************************
* Register Name : SDPMUX_SION_PERF_CNT_CNTL1
************************************************************/

#define SDPMUX_SION_PERF_CNT_CNTL1_SDPMUX_SION_EVENT0_SEL_OFFSET 0
#define SDPMUX_SION_PERF_CNT_CNTL1_SDPMUX_SION_EVENT0_SEL_MASK 0xff

#define SDPMUX_SION_PERF_CNT_CNTL1_SDPMUX_SION_EVENT1_SEL_OFFSET 8
#define SDPMUX_SION_PERF_CNT_CNTL1_SDPMUX_SION_EVENT1_SEL_MASK 0xff00

#define SDPMUX_SION_PERF_CNT_CNTL1_SDPMUX_SION_EVENT2_SEL_OFFSET 16
#define SDPMUX_SION_PERF_CNT_CNTL1_SDPMUX_SION_EVENT2_SEL_MASK 0xff0000

#define SDPMUX_SION_PERF_CNT_CNTL1_SDPMUX_SION_EVENT3_SEL_OFFSET 24
#define SDPMUX_SION_PERF_CNT_CNTL1_SDPMUX_SION_EVENT3_SEL_MASK 0xff000000

typedef union {
  struct {
    UINT32                              SDPMUX_SION_EVENT0_SEL:8;
    UINT32                              SDPMUX_SION_EVENT1_SEL:8;
    UINT32                              SDPMUX_SION_EVENT2_SEL:8;
    UINT32                              SDPMUX_SION_EVENT3_SEL:8;
  } Field;
  UINT32 Value;
} SDPMUX_SION_PERF_CNT_CNTL1_STRUCT;

#define SMN_SDPMUX_SION_PERF_CNT_CNTL1_ADDRESS                        0x4400ca8UL
#define SMN_IOHUB0NBIO0_SDPMUX_SION_PERF_CNT_CNTL1_ADDRESS            0x4400ca8UL
#define SMN_IOHUB0NBIO1_SDPMUX_SION_PERF_CNT_CNTL1_ADDRESS            0x4e00ca8UL


/***********************************************************
* Register Name : SDPMUX_SION_PERF_COUNT0
************************************************************/

#define SDPMUX_SION_PERF_COUNT0_SDPMUX_SION_COUNTER0_OFFSET    0
#define SDPMUX_SION_PERF_COUNT0_SDPMUX_SION_COUNTER0_MASK      0xffffffff

typedef union {
  struct {
    UINT32                                SDPMUX_SION_COUNTER0:32;
  } Field;
  UINT32 Value;
} SDPMUX_SION_PERF_COUNT0_STRUCT;

#define SMN_SDPMUX_SION_PERF_COUNT0_ADDRESS                           0x4400cacUL
#define SMN_IOHUB0NBIO0_SDPMUX_SION_PERF_COUNT0_ADDRESS               0x4400cacUL
#define SMN_IOHUB0NBIO1_SDPMUX_SION_PERF_COUNT0_ADDRESS               0x4e00cacUL


/***********************************************************
* Register Name : SDPMUX_SION_PERF_COUNT0_UPPER
************************************************************/

#define SDPMUX_SION_PERF_COUNT0_UPPER_SDPMUX_SION_COUNTER0_UPPER_OFFSET 0
#define SDPMUX_SION_PERF_COUNT0_UPPER_SDPMUX_SION_COUNTER0_UPPER_MASK 0xffffff

#define SDPMUX_SION_PERF_COUNT0_UPPER_Reserved_31_24_OFFSET    24
#define SDPMUX_SION_PERF_COUNT0_UPPER_Reserved_31_24_MASK      0xff000000

typedef union {
  struct {
    UINT32                          SDPMUX_SION_COUNTER0_UPPER:24;
    UINT32                                      Reserved_31_24:8;
  } Field;
  UINT32 Value;
} SDPMUX_SION_PERF_COUNT0_UPPER_STRUCT;

#define SMN_SDPMUX_SION_PERF_COUNT0_UPPER_ADDRESS                     0x4400cb0UL
#define SMN_IOHUB0NBIO0_SDPMUX_SION_PERF_COUNT0_UPPER_ADDRESS         0x4400cb0UL
#define SMN_IOHUB0NBIO1_SDPMUX_SION_PERF_COUNT0_UPPER_ADDRESS         0x4e00cb0UL


/***********************************************************
* Register Name : SDPMUX_SION_PERF_COUNT1
************************************************************/

#define SDPMUX_SION_PERF_COUNT1_SDPMUX_SION_COUNTER1_OFFSET    0
#define SDPMUX_SION_PERF_COUNT1_SDPMUX_SION_COUNTER1_MASK      0xffffffff

typedef union {
  struct {
    UINT32                                SDPMUX_SION_COUNTER1:32;
  } Field;
  UINT32 Value;
} SDPMUX_SION_PERF_COUNT1_STRUCT;

#define SMN_SDPMUX_SION_PERF_COUNT1_ADDRESS                           0x4400cb4UL
#define SMN_IOHUB0NBIO0_SDPMUX_SION_PERF_COUNT1_ADDRESS               0x4400cb4UL
#define SMN_IOHUB0NBIO1_SDPMUX_SION_PERF_COUNT1_ADDRESS               0x4e00cb4UL


/***********************************************************
* Register Name : SDPMUX_SION_PERF_COUNT1_UPPER
************************************************************/

#define SDPMUX_SION_PERF_COUNT1_UPPER_SDPMUX_SION_COUNTER1_UPPER_OFFSET 0
#define SDPMUX_SION_PERF_COUNT1_UPPER_SDPMUX_SION_COUNTER1_UPPER_MASK 0xffffff

#define SDPMUX_SION_PERF_COUNT1_UPPER_Reserved_31_24_OFFSET    24
#define SDPMUX_SION_PERF_COUNT1_UPPER_Reserved_31_24_MASK      0xff000000

typedef union {
  struct {
    UINT32                          SDPMUX_SION_COUNTER1_UPPER:24;
    UINT32                                      Reserved_31_24:8;
  } Field;
  UINT32 Value;
} SDPMUX_SION_PERF_COUNT1_UPPER_STRUCT;

#define SMN_SDPMUX_SION_PERF_COUNT1_UPPER_ADDRESS                     0x4400cb8UL
#define SMN_IOHUB0NBIO0_SDPMUX_SION_PERF_COUNT1_UPPER_ADDRESS         0x4400cb8UL
#define SMN_IOHUB0NBIO1_SDPMUX_SION_PERF_COUNT1_UPPER_ADDRESS         0x4e00cb8UL


/***********************************************************
* Register Name : SDPMUX_SION_PERF_COUNT2
************************************************************/

#define SDPMUX_SION_PERF_COUNT2_SDPMUX_SION_COUNTER2_OFFSET    0
#define SDPMUX_SION_PERF_COUNT2_SDPMUX_SION_COUNTER2_MASK      0xffffffff

typedef union {
  struct {
    UINT32                                SDPMUX_SION_COUNTER2:32;
  } Field;
  UINT32 Value;
} SDPMUX_SION_PERF_COUNT2_STRUCT;

#define SMN_SDPMUX_SION_PERF_COUNT2_ADDRESS                           0x4400cbcUL
#define SMN_IOHUB0NBIO0_SDPMUX_SION_PERF_COUNT2_ADDRESS               0x4400cbcUL
#define SMN_IOHUB0NBIO1_SDPMUX_SION_PERF_COUNT2_ADDRESS               0x4e00cbcUL


/***********************************************************
* Register Name : SDPMUX_SION_PERF_COUNT2_UPPER
************************************************************/

#define SDPMUX_SION_PERF_COUNT2_UPPER_SDPMUX_SION_COUNTER2_UPPER_OFFSET 0
#define SDPMUX_SION_PERF_COUNT2_UPPER_SDPMUX_SION_COUNTER2_UPPER_MASK 0xffffff

#define SDPMUX_SION_PERF_COUNT2_UPPER_Reserved_31_24_OFFSET    24
#define SDPMUX_SION_PERF_COUNT2_UPPER_Reserved_31_24_MASK      0xff000000

typedef union {
  struct {
    UINT32                          SDPMUX_SION_COUNTER2_UPPER:24;
    UINT32                                      Reserved_31_24:8;
  } Field;
  UINT32 Value;
} SDPMUX_SION_PERF_COUNT2_UPPER_STRUCT;

#define SMN_SDPMUX_SION_PERF_COUNT2_UPPER_ADDRESS                     0x4400cc0UL
#define SMN_IOHUB0NBIO0_SDPMUX_SION_PERF_COUNT2_UPPER_ADDRESS         0x4400cc0UL
#define SMN_IOHUB0NBIO1_SDPMUX_SION_PERF_COUNT2_UPPER_ADDRESS         0x4e00cc0UL


/***********************************************************
* Register Name : SDPMUX_SION_PERF_COUNT3
************************************************************/

#define SDPMUX_SION_PERF_COUNT3_SDPMUX_SION_COUNTER3_OFFSET    0
#define SDPMUX_SION_PERF_COUNT3_SDPMUX_SION_COUNTER3_MASK      0xffffffff

typedef union {
  struct {
    UINT32                                SDPMUX_SION_COUNTER3:32;
  } Field;
  UINT32 Value;
} SDPMUX_SION_PERF_COUNT3_STRUCT;

#define SMN_SDPMUX_SION_PERF_COUNT3_ADDRESS                           0x4400cc4UL
#define SMN_IOHUB0NBIO0_SDPMUX_SION_PERF_COUNT3_ADDRESS               0x4400cc4UL
#define SMN_IOHUB0NBIO1_SDPMUX_SION_PERF_COUNT3_ADDRESS               0x4e00cc4UL


/***********************************************************
* Register Name : SDPMUX_SION_PERF_COUNT3_UPPER
************************************************************/

#define SDPMUX_SION_PERF_COUNT3_UPPER_SDPMUX_SION_COUNTER3_UPPER_OFFSET 0
#define SDPMUX_SION_PERF_COUNT3_UPPER_SDPMUX_SION_COUNTER3_UPPER_MASK 0xffffff

#define SDPMUX_SION_PERF_COUNT3_UPPER_Reserved_31_24_OFFSET    24
#define SDPMUX_SION_PERF_COUNT3_UPPER_Reserved_31_24_MASK      0xff000000

typedef union {
  struct {
    UINT32                          SDPMUX_SION_COUNTER3_UPPER:24;
    UINT32                                      Reserved_31_24:8;
  } Field;
  UINT32 Value;
} SDPMUX_SION_PERF_COUNT3_UPPER_STRUCT;

#define SMN_SDPMUX_SION_PERF_COUNT3_UPPER_ADDRESS                     0x4400cc8UL
#define SMN_IOHUB0NBIO0_SDPMUX_SION_PERF_COUNT3_UPPER_ADDRESS         0x4400cc8UL
#define SMN_IOHUB0NBIO1_SDPMUX_SION_PERF_COUNT3_UPPER_ADDRESS         0x4e00cc8UL


/***********************************************************
* Register Name : SDPMUX_SION_S0_Client_RdRsp_BurstTarget_Lower
************************************************************/

#define SDPMUX_SION_S0_Client_RdRsp_BurstTarget_Lower_RdRsp_BurstTarget_Lower_OFFSET 0
#define SDPMUX_SION_S0_Client_RdRsp_BurstTarget_Lower_RdRsp_BurstTarget_Lower_MASK 0xffffffff

typedef union {
  struct {
    UINT32                             RdRsp_BurstTarget_Lower:32;
  } Field;
  UINT32 Value;
} SDPMUX_SION_S0_Client_RdRsp_BurstTarget_Lower_STRUCT;

#define SMN_SDPMUX_SION_S0_Client_RdRsp_BurstTarget_Lower_ADDRESS     0x4400410UL
#define SMN_IOHUB0_N0NBIO0_SDPMUX_SION_S0_Client_RdRsp_BurstTarget_Lower_ADDRESS 0x4400410UL
#define SMN_IOHUB0_N0NBIO1_SDPMUX_SION_S0_Client_RdRsp_BurstTarget_Lower_ADDRESS 0x4e00410UL
#define SMN_IOHUB0_N1NBIO0_SDPMUX_SION_S0_Client_RdRsp_BurstTarget_Lower_ADDRESS 0x4400810UL
#define SMN_IOHUB0_N1NBIO1_SDPMUX_SION_S0_Client_RdRsp_BurstTarget_Lower_ADDRESS 0x4e00810UL
#define SMN_IOHUB0_N2NBIO0_SDPMUX_SION_S0_Client_RdRsp_BurstTarget_Lower_ADDRESS 0x4400c10UL
#define SMN_IOHUB0_N2NBIO1_SDPMUX_SION_S0_Client_RdRsp_BurstTarget_Lower_ADDRESS 0x4e00c10UL


/***********************************************************
* Register Name : SDPMUX_SION_S0_Client_RdRsp_BurstTarget_Upper
************************************************************/

#define SDPMUX_SION_S0_Client_RdRsp_BurstTarget_Upper_RdRsp_BurstTarget_Upper_OFFSET 0
#define SDPMUX_SION_S0_Client_RdRsp_BurstTarget_Upper_RdRsp_BurstTarget_Upper_MASK 0xffffffff

typedef union {
  struct {
    UINT32                             RdRsp_BurstTarget_Upper:32;
  } Field;
  UINT32 Value;
} SDPMUX_SION_S0_Client_RdRsp_BurstTarget_Upper_STRUCT;

#define SMN_SDPMUX_SION_S0_Client_RdRsp_BurstTarget_Upper_ADDRESS     0x4400414UL
#define SMN_IOHUB0_N0NBIO0_SDPMUX_SION_S0_Client_RdRsp_BurstTarget_Upper_ADDRESS 0x4400414UL
#define SMN_IOHUB0_N0NBIO1_SDPMUX_SION_S0_Client_RdRsp_BurstTarget_Upper_ADDRESS 0x4e00414UL
#define SMN_IOHUB0_N1NBIO0_SDPMUX_SION_S0_Client_RdRsp_BurstTarget_Upper_ADDRESS 0x4400814UL
#define SMN_IOHUB0_N1NBIO1_SDPMUX_SION_S0_Client_RdRsp_BurstTarget_Upper_ADDRESS 0x4e00814UL
#define SMN_IOHUB0_N2NBIO0_SDPMUX_SION_S0_Client_RdRsp_BurstTarget_Upper_ADDRESS 0x4400c14UL
#define SMN_IOHUB0_N2NBIO1_SDPMUX_SION_S0_Client_RdRsp_BurstTarget_Upper_ADDRESS 0x4e00c14UL


/***********************************************************
* Register Name : SDPMUX_SION_S0_Client_RdRsp_TimeSlot_Lower
************************************************************/

#define SDPMUX_SION_S0_Client_RdRsp_TimeSlot_Lower_RdRsp_TimeSlot_Lower_OFFSET 0
#define SDPMUX_SION_S0_Client_RdRsp_TimeSlot_Lower_RdRsp_TimeSlot_Lower_MASK 0xffffffff

typedef union {
  struct {
    UINT32                                RdRsp_TimeSlot_Lower:32;
  } Field;
  UINT32 Value;
} SDPMUX_SION_S0_Client_RdRsp_TimeSlot_Lower_STRUCT;

#define SMN_SDPMUX_SION_S0_Client_RdRsp_TimeSlot_Lower_ADDRESS        0x4400418UL
#define SMN_IOHUB0_N0NBIO0_SDPMUX_SION_S0_Client_RdRsp_TimeSlot_Lower_ADDRESS 0x4400418UL
#define SMN_IOHUB0_N0NBIO1_SDPMUX_SION_S0_Client_RdRsp_TimeSlot_Lower_ADDRESS 0x4e00418UL
#define SMN_IOHUB0_N1NBIO0_SDPMUX_SION_S0_Client_RdRsp_TimeSlot_Lower_ADDRESS 0x4400818UL
#define SMN_IOHUB0_N1NBIO1_SDPMUX_SION_S0_Client_RdRsp_TimeSlot_Lower_ADDRESS 0x4e00818UL
#define SMN_IOHUB0_N2NBIO0_SDPMUX_SION_S0_Client_RdRsp_TimeSlot_Lower_ADDRESS 0x4400c18UL
#define SMN_IOHUB0_N2NBIO1_SDPMUX_SION_S0_Client_RdRsp_TimeSlot_Lower_ADDRESS 0x4e00c18UL


/***********************************************************
* Register Name : SDPMUX_SION_S0_Client_RdRsp_TimeSlot_Upper
************************************************************/

#define SDPMUX_SION_S0_Client_RdRsp_TimeSlot_Upper_RdRsp_TimeSlot_Upper_OFFSET 0
#define SDPMUX_SION_S0_Client_RdRsp_TimeSlot_Upper_RdRsp_TimeSlot_Upper_MASK 0xffffffff

typedef union {
  struct {
    UINT32                                RdRsp_TimeSlot_Upper:32;
  } Field;
  UINT32 Value;
} SDPMUX_SION_S0_Client_RdRsp_TimeSlot_Upper_STRUCT;

#define SMN_SDPMUX_SION_S0_Client_RdRsp_TimeSlot_Upper_ADDRESS        0x440041cUL
#define SMN_IOHUB0_N0NBIO0_SDPMUX_SION_S0_Client_RdRsp_TimeSlot_Upper_ADDRESS 0x440041cUL
#define SMN_IOHUB0_N0NBIO1_SDPMUX_SION_S0_Client_RdRsp_TimeSlot_Upper_ADDRESS 0x4e0041cUL
#define SMN_IOHUB0_N1NBIO0_SDPMUX_SION_S0_Client_RdRsp_TimeSlot_Upper_ADDRESS 0x440081cUL
#define SMN_IOHUB0_N1NBIO1_SDPMUX_SION_S0_Client_RdRsp_TimeSlot_Upper_ADDRESS 0x4e0081cUL
#define SMN_IOHUB0_N2NBIO0_SDPMUX_SION_S0_Client_RdRsp_TimeSlot_Upper_ADDRESS 0x4400c1cUL
#define SMN_IOHUB0_N2NBIO1_SDPMUX_SION_S0_Client_RdRsp_TimeSlot_Upper_ADDRESS 0x4e00c1cUL


/***********************************************************
* Register Name : SDPMUX_SION_S0_Client_Req_BurstTarget_Lower
************************************************************/

#define SDPMUX_SION_S0_Client_Req_BurstTarget_Lower_Req_BurstTarget_Lower_OFFSET 0
#define SDPMUX_SION_S0_Client_Req_BurstTarget_Lower_Req_BurstTarget_Lower_MASK 0xffffffff
#define SDPMUX_SION_S0_Client_Req_BurstTarget_Lower_Req_BurstTarget_Lower_DEFAULT     0x4040404

typedef union {
  struct {
    UINT32                               Req_BurstTarget_Lower:32;
  } Field;
  UINT32 Value;
} SDPMUX_SION_S0_Client_Req_BurstTarget_Lower_STRUCT;

#define SMN_SDPMUX_SION_S0_Client_Req_BurstTarget_Lower_ADDRESS       0x4400400UL
#define SMN_IOHUB0_N0NBIO0_SDPMUX_SION_S0_Client_Req_BurstTarget_Lower_ADDRESS 0x4400400UL
#define SMN_IOHUB0_N0NBIO1_SDPMUX_SION_S0_Client_Req_BurstTarget_Lower_ADDRESS 0x4e00400UL
#define SMN_IOHUB0_N1NBIO0_SDPMUX_SION_S0_Client_Req_BurstTarget_Lower_ADDRESS 0x4400800UL
#define SMN_IOHUB0_N1NBIO1_SDPMUX_SION_S0_Client_Req_BurstTarget_Lower_ADDRESS 0x4e00800UL
#define SMN_IOHUB0_N2NBIO0_SDPMUX_SION_S0_Client_Req_BurstTarget_Lower_ADDRESS 0x4400c00UL
#define SMN_IOHUB0_N2NBIO1_SDPMUX_SION_S0_Client_Req_BurstTarget_Lower_ADDRESS 0x4e00c00UL


/***********************************************************
* Register Name : SDPMUX_SION_S0_Client_Req_BurstTarget_Upper
************************************************************/

#define SDPMUX_SION_S0_Client_Req_BurstTarget_Upper_Req_BurstTarget_Upper_OFFSET 0
#define SDPMUX_SION_S0_Client_Req_BurstTarget_Upper_Req_BurstTarget_Upper_MASK 0xffffffff
#define SDPMUX_SION_S0_Client_Req_BurstTarget_Upper_Req_BurstTarget_Upper_DEFAULT     0x4040404

typedef union {
  struct {
    UINT32                               Req_BurstTarget_Upper:32;
  } Field;
  UINT32 Value;
} SDPMUX_SION_S0_Client_Req_BurstTarget_Upper_STRUCT;

#define SMN_SDPMUX_SION_S0_Client_Req_BurstTarget_Upper_ADDRESS       0x4400404UL
#define SMN_IOHUB0_N0NBIO0_SDPMUX_SION_S0_Client_Req_BurstTarget_Upper_ADDRESS 0x4400404UL
#define SMN_IOHUB0_N0NBIO1_SDPMUX_SION_S0_Client_Req_BurstTarget_Upper_ADDRESS 0x4e00404UL
#define SMN_IOHUB0_N1NBIO0_SDPMUX_SION_S0_Client_Req_BurstTarget_Upper_ADDRESS 0x4400804UL
#define SMN_IOHUB0_N1NBIO1_SDPMUX_SION_S0_Client_Req_BurstTarget_Upper_ADDRESS 0x4e00804UL
#define SMN_IOHUB0_N2NBIO0_SDPMUX_SION_S0_Client_Req_BurstTarget_Upper_ADDRESS 0x4400c04UL
#define SMN_IOHUB0_N2NBIO1_SDPMUX_SION_S0_Client_Req_BurstTarget_Upper_ADDRESS 0x4e00c04UL


/***********************************************************
* Register Name : SDPMUX_SION_S0_Client_Req_TimeSlot_Lower
************************************************************/

#define SDPMUX_SION_S0_Client_Req_TimeSlot_Lower_Req_TimeSlot_Lower_OFFSET 0
#define SDPMUX_SION_S0_Client_Req_TimeSlot_Lower_Req_TimeSlot_Lower_MASK 0xffffffff

typedef union {
  struct {
    UINT32                                  Req_TimeSlot_Lower:32;
  } Field;
  UINT32 Value;
} SDPMUX_SION_S0_Client_Req_TimeSlot_Lower_STRUCT;

#define SMN_SDPMUX_SION_S0_Client_Req_TimeSlot_Lower_ADDRESS          0x4400408UL
#define SMN_IOHUB0_N0NBIO0_SDPMUX_SION_S0_Client_Req_TimeSlot_Lower_ADDRESS 0x4400408UL
#define SMN_IOHUB0_N0NBIO1_SDPMUX_SION_S0_Client_Req_TimeSlot_Lower_ADDRESS 0x4e00408UL
#define SMN_IOHUB0_N1NBIO0_SDPMUX_SION_S0_Client_Req_TimeSlot_Lower_ADDRESS 0x4400808UL
#define SMN_IOHUB0_N1NBIO1_SDPMUX_SION_S0_Client_Req_TimeSlot_Lower_ADDRESS 0x4e00808UL
#define SMN_IOHUB0_N2NBIO0_SDPMUX_SION_S0_Client_Req_TimeSlot_Lower_ADDRESS 0x4400c08UL
#define SMN_IOHUB0_N2NBIO1_SDPMUX_SION_S0_Client_Req_TimeSlot_Lower_ADDRESS 0x4e00c08UL


/***********************************************************
* Register Name : SDPMUX_SION_S0_Client_Req_TimeSlot_Upper
************************************************************/

#define SDPMUX_SION_S0_Client_Req_TimeSlot_Upper_Req_TimeSlot_Upper_OFFSET 0
#define SDPMUX_SION_S0_Client_Req_TimeSlot_Upper_Req_TimeSlot_Upper_MASK 0xffffffff

typedef union {
  struct {
    UINT32                                  Req_TimeSlot_Upper:32;
  } Field;
  UINT32 Value;
} SDPMUX_SION_S0_Client_Req_TimeSlot_Upper_STRUCT;

#define SMN_SDPMUX_SION_S0_Client_Req_TimeSlot_Upper_ADDRESS          0x440040cUL
#define SMN_IOHUB0_N0NBIO0_SDPMUX_SION_S0_Client_Req_TimeSlot_Upper_ADDRESS 0x440040cUL
#define SMN_IOHUB0_N0NBIO1_SDPMUX_SION_S0_Client_Req_TimeSlot_Upper_ADDRESS 0x4e0040cUL
#define SMN_IOHUB0_N1NBIO0_SDPMUX_SION_S0_Client_Req_TimeSlot_Upper_ADDRESS 0x440080cUL
#define SMN_IOHUB0_N1NBIO1_SDPMUX_SION_S0_Client_Req_TimeSlot_Upper_ADDRESS 0x4e0080cUL
#define SMN_IOHUB0_N2NBIO0_SDPMUX_SION_S0_Client_Req_TimeSlot_Upper_ADDRESS 0x4400c0cUL
#define SMN_IOHUB0_N2NBIO1_SDPMUX_SION_S0_Client_Req_TimeSlot_Upper_ADDRESS 0x4e00c0cUL


/***********************************************************
* Register Name : SDPMUX_SION_S0_Client_WrRsp_BurstTarget_Lower
************************************************************/

#define SDPMUX_SION_S0_Client_WrRsp_BurstTarget_Lower_WrRsp_BurstTarget_Lower_OFFSET 0
#define SDPMUX_SION_S0_Client_WrRsp_BurstTarget_Lower_WrRsp_BurstTarget_Lower_MASK 0xffffffff

typedef union {
  struct {
    UINT32                             WrRsp_BurstTarget_Lower:32;
  } Field;
  UINT32 Value;
} SDPMUX_SION_S0_Client_WrRsp_BurstTarget_Lower_STRUCT;

#define SMN_SDPMUX_SION_S0_Client_WrRsp_BurstTarget_Lower_ADDRESS     0x4400420UL
#define SMN_IOHUB0_N0NBIO0_SDPMUX_SION_S0_Client_WrRsp_BurstTarget_Lower_ADDRESS 0x4400420UL
#define SMN_IOHUB0_N0NBIO1_SDPMUX_SION_S0_Client_WrRsp_BurstTarget_Lower_ADDRESS 0x4e00420UL
#define SMN_IOHUB0_N1NBIO0_SDPMUX_SION_S0_Client_WrRsp_BurstTarget_Lower_ADDRESS 0x4400820UL
#define SMN_IOHUB0_N1NBIO1_SDPMUX_SION_S0_Client_WrRsp_BurstTarget_Lower_ADDRESS 0x4e00820UL
#define SMN_IOHUB0_N2NBIO0_SDPMUX_SION_S0_Client_WrRsp_BurstTarget_Lower_ADDRESS 0x4400c20UL
#define SMN_IOHUB0_N2NBIO1_SDPMUX_SION_S0_Client_WrRsp_BurstTarget_Lower_ADDRESS 0x4e00c20UL


/***********************************************************
* Register Name : SDPMUX_SION_S0_Client_WrRsp_BurstTarget_Upper
************************************************************/

#define SDPMUX_SION_S0_Client_WrRsp_BurstTarget_Upper_WrRsp_BurstTarget_Upper_OFFSET 0
#define SDPMUX_SION_S0_Client_WrRsp_BurstTarget_Upper_WrRsp_BurstTarget_Upper_MASK 0xffffffff

typedef union {
  struct {
    UINT32                             WrRsp_BurstTarget_Upper:32;
  } Field;
  UINT32 Value;
} SDPMUX_SION_S0_Client_WrRsp_BurstTarget_Upper_STRUCT;

#define SMN_SDPMUX_SION_S0_Client_WrRsp_BurstTarget_Upper_ADDRESS     0x4400424UL
#define SMN_IOHUB0_N0NBIO0_SDPMUX_SION_S0_Client_WrRsp_BurstTarget_Upper_ADDRESS 0x4400424UL
#define SMN_IOHUB0_N0NBIO1_SDPMUX_SION_S0_Client_WrRsp_BurstTarget_Upper_ADDRESS 0x4e00424UL
#define SMN_IOHUB0_N1NBIO0_SDPMUX_SION_S0_Client_WrRsp_BurstTarget_Upper_ADDRESS 0x4400824UL
#define SMN_IOHUB0_N1NBIO1_SDPMUX_SION_S0_Client_WrRsp_BurstTarget_Upper_ADDRESS 0x4e00824UL
#define SMN_IOHUB0_N2NBIO0_SDPMUX_SION_S0_Client_WrRsp_BurstTarget_Upper_ADDRESS 0x4400c24UL
#define SMN_IOHUB0_N2NBIO1_SDPMUX_SION_S0_Client_WrRsp_BurstTarget_Upper_ADDRESS 0x4e00c24UL


/***********************************************************
* Register Name : SDPMUX_SION_S0_Client_WrRsp_TimeSlot_Lower
************************************************************/

#define SDPMUX_SION_S0_Client_WrRsp_TimeSlot_Lower_WrRsp_TimeSlot_Lower_OFFSET 0
#define SDPMUX_SION_S0_Client_WrRsp_TimeSlot_Lower_WrRsp_TimeSlot_Lower_MASK 0xffffffff

typedef union {
  struct {
    UINT32                                WrRsp_TimeSlot_Lower:32;
  } Field;
  UINT32 Value;
} SDPMUX_SION_S0_Client_WrRsp_TimeSlot_Lower_STRUCT;

#define SMN_SDPMUX_SION_S0_Client_WrRsp_TimeSlot_Lower_ADDRESS        0x4400428UL
#define SMN_IOHUB0_N0NBIO0_SDPMUX_SION_S0_Client_WrRsp_TimeSlot_Lower_ADDRESS 0x4400428UL
#define SMN_IOHUB0_N0NBIO1_SDPMUX_SION_S0_Client_WrRsp_TimeSlot_Lower_ADDRESS 0x4e00428UL
#define SMN_IOHUB0_N1NBIO0_SDPMUX_SION_S0_Client_WrRsp_TimeSlot_Lower_ADDRESS 0x4400828UL
#define SMN_IOHUB0_N1NBIO1_SDPMUX_SION_S0_Client_WrRsp_TimeSlot_Lower_ADDRESS 0x4e00828UL
#define SMN_IOHUB0_N2NBIO0_SDPMUX_SION_S0_Client_WrRsp_TimeSlot_Lower_ADDRESS 0x4400c28UL
#define SMN_IOHUB0_N2NBIO1_SDPMUX_SION_S0_Client_WrRsp_TimeSlot_Lower_ADDRESS 0x4e00c28UL


/***********************************************************
* Register Name : SDPMUX_SION_S0_Client_WrRsp_TimeSlot_Upper
************************************************************/

#define SDPMUX_SION_S0_Client_WrRsp_TimeSlot_Upper_WrRsp_TimeSlot_Upper_OFFSET 0
#define SDPMUX_SION_S0_Client_WrRsp_TimeSlot_Upper_WrRsp_TimeSlot_Upper_MASK 0xffffffff

typedef union {
  struct {
    UINT32                                WrRsp_TimeSlot_Upper:32;
  } Field;
  UINT32 Value;
} SDPMUX_SION_S0_Client_WrRsp_TimeSlot_Upper_STRUCT;

#define SMN_SDPMUX_SION_S0_Client_WrRsp_TimeSlot_Upper_ADDRESS        0x440042cUL
#define SMN_IOHUB0_N0NBIO0_SDPMUX_SION_S0_Client_WrRsp_TimeSlot_Upper_ADDRESS 0x440042cUL
#define SMN_IOHUB0_N0NBIO1_SDPMUX_SION_S0_Client_WrRsp_TimeSlot_Upper_ADDRESS 0x4e0042cUL
#define SMN_IOHUB0_N1NBIO0_SDPMUX_SION_S0_Client_WrRsp_TimeSlot_Upper_ADDRESS 0x440082cUL
#define SMN_IOHUB0_N1NBIO1_SDPMUX_SION_S0_Client_WrRsp_TimeSlot_Upper_ADDRESS 0x4e0082cUL
#define SMN_IOHUB0_N2NBIO0_SDPMUX_SION_S0_Client_WrRsp_TimeSlot_Upper_ADDRESS 0x4400c2cUL
#define SMN_IOHUB0_N2NBIO1_SDPMUX_SION_S0_Client_WrRsp_TimeSlot_Upper_ADDRESS 0x4e00c2cUL


/***********************************************************
* Register Name : SDPMUX_SION_S1_Client_RdRsp_BurstTarget_Lower
************************************************************/

#define SDPMUX_SION_S1_Client_RdRsp_BurstTarget_Lower_RdRsp_BurstTarget_Lower_OFFSET 0
#define SDPMUX_SION_S1_Client_RdRsp_BurstTarget_Lower_RdRsp_BurstTarget_Lower_MASK 0xffffffff

typedef union {
  struct {
    UINT32                             RdRsp_BurstTarget_Lower:32;
  } Field;
  UINT32 Value;
} SDPMUX_SION_S1_Client_RdRsp_BurstTarget_Lower_STRUCT;

#define SMN_SDPMUX_SION_S1_Client_RdRsp_BurstTarget_Lower_ADDRESS     0x4400440UL
#define SMN_IOHUB0_N0NBIO0_SDPMUX_SION_S1_Client_RdRsp_BurstTarget_Lower_ADDRESS 0x4400440UL
#define SMN_IOHUB0_N0NBIO1_SDPMUX_SION_S1_Client_RdRsp_BurstTarget_Lower_ADDRESS 0x4e00440UL
#define SMN_IOHUB0_N1NBIO0_SDPMUX_SION_S1_Client_RdRsp_BurstTarget_Lower_ADDRESS 0x4400840UL
#define SMN_IOHUB0_N1NBIO1_SDPMUX_SION_S1_Client_RdRsp_BurstTarget_Lower_ADDRESS 0x4e00840UL
#define SMN_IOHUB0_N2NBIO0_SDPMUX_SION_S1_Client_RdRsp_BurstTarget_Lower_ADDRESS 0x4400c40UL
#define SMN_IOHUB0_N2NBIO1_SDPMUX_SION_S1_Client_RdRsp_BurstTarget_Lower_ADDRESS 0x4e00c40UL


/***********************************************************
* Register Name : SDPMUX_SION_S1_Client_RdRsp_BurstTarget_Upper
************************************************************/

#define SDPMUX_SION_S1_Client_RdRsp_BurstTarget_Upper_RdRsp_BurstTarget_Upper_OFFSET 0
#define SDPMUX_SION_S1_Client_RdRsp_BurstTarget_Upper_RdRsp_BurstTarget_Upper_MASK 0xffffffff

typedef union {
  struct {
    UINT32                             RdRsp_BurstTarget_Upper:32;
  } Field;
  UINT32 Value;
} SDPMUX_SION_S1_Client_RdRsp_BurstTarget_Upper_STRUCT;

#define SMN_SDPMUX_SION_S1_Client_RdRsp_BurstTarget_Upper_ADDRESS     0x4400444UL
#define SMN_IOHUB0_N0NBIO0_SDPMUX_SION_S1_Client_RdRsp_BurstTarget_Upper_ADDRESS 0x4400444UL
#define SMN_IOHUB0_N0NBIO1_SDPMUX_SION_S1_Client_RdRsp_BurstTarget_Upper_ADDRESS 0x4e00444UL
#define SMN_IOHUB0_N1NBIO0_SDPMUX_SION_S1_Client_RdRsp_BurstTarget_Upper_ADDRESS 0x4400844UL
#define SMN_IOHUB0_N1NBIO1_SDPMUX_SION_S1_Client_RdRsp_BurstTarget_Upper_ADDRESS 0x4e00844UL
#define SMN_IOHUB0_N2NBIO0_SDPMUX_SION_S1_Client_RdRsp_BurstTarget_Upper_ADDRESS 0x4400c44UL
#define SMN_IOHUB0_N2NBIO1_SDPMUX_SION_S1_Client_RdRsp_BurstTarget_Upper_ADDRESS 0x4e00c44UL


/***********************************************************
* Register Name : SDPMUX_SION_S1_Client_RdRsp_TimeSlot_Lower
************************************************************/

#define SDPMUX_SION_S1_Client_RdRsp_TimeSlot_Lower_RdRsp_TimeSlot_Lower_OFFSET 0
#define SDPMUX_SION_S1_Client_RdRsp_TimeSlot_Lower_RdRsp_TimeSlot_Lower_MASK 0xffffffff

typedef union {
  struct {
    UINT32                                RdRsp_TimeSlot_Lower:32;
  } Field;
  UINT32 Value;
} SDPMUX_SION_S1_Client_RdRsp_TimeSlot_Lower_STRUCT;

#define SMN_SDPMUX_SION_S1_Client_RdRsp_TimeSlot_Lower_ADDRESS        0x4400448UL
#define SMN_IOHUB0_N0NBIO0_SDPMUX_SION_S1_Client_RdRsp_TimeSlot_Lower_ADDRESS 0x4400448UL
#define SMN_IOHUB0_N0NBIO1_SDPMUX_SION_S1_Client_RdRsp_TimeSlot_Lower_ADDRESS 0x4e00448UL
#define SMN_IOHUB0_N1NBIO0_SDPMUX_SION_S1_Client_RdRsp_TimeSlot_Lower_ADDRESS 0x4400848UL
#define SMN_IOHUB0_N1NBIO1_SDPMUX_SION_S1_Client_RdRsp_TimeSlot_Lower_ADDRESS 0x4e00848UL
#define SMN_IOHUB0_N2NBIO0_SDPMUX_SION_S1_Client_RdRsp_TimeSlot_Lower_ADDRESS 0x4400c48UL
#define SMN_IOHUB0_N2NBIO1_SDPMUX_SION_S1_Client_RdRsp_TimeSlot_Lower_ADDRESS 0x4e00c48UL


/***********************************************************
* Register Name : SDPMUX_SION_S1_Client_RdRsp_TimeSlot_Upper
************************************************************/

#define SDPMUX_SION_S1_Client_RdRsp_TimeSlot_Upper_RdRsp_TimeSlot_Upper_OFFSET 0
#define SDPMUX_SION_S1_Client_RdRsp_TimeSlot_Upper_RdRsp_TimeSlot_Upper_MASK 0xffffffff

typedef union {
  struct {
    UINT32                                RdRsp_TimeSlot_Upper:32;
  } Field;
  UINT32 Value;
} SDPMUX_SION_S1_Client_RdRsp_TimeSlot_Upper_STRUCT;

#define SMN_SDPMUX_SION_S1_Client_RdRsp_TimeSlot_Upper_ADDRESS        0x440044cUL
#define SMN_IOHUB0_N0NBIO0_SDPMUX_SION_S1_Client_RdRsp_TimeSlot_Upper_ADDRESS 0x440044cUL
#define SMN_IOHUB0_N0NBIO1_SDPMUX_SION_S1_Client_RdRsp_TimeSlot_Upper_ADDRESS 0x4e0044cUL
#define SMN_IOHUB0_N1NBIO0_SDPMUX_SION_S1_Client_RdRsp_TimeSlot_Upper_ADDRESS 0x440084cUL
#define SMN_IOHUB0_N1NBIO1_SDPMUX_SION_S1_Client_RdRsp_TimeSlot_Upper_ADDRESS 0x4e0084cUL
#define SMN_IOHUB0_N2NBIO0_SDPMUX_SION_S1_Client_RdRsp_TimeSlot_Upper_ADDRESS 0x4400c4cUL
#define SMN_IOHUB0_N2NBIO1_SDPMUX_SION_S1_Client_RdRsp_TimeSlot_Upper_ADDRESS 0x4e00c4cUL


/***********************************************************
* Register Name : SDPMUX_SION_S1_Client_Req_BurstTarget_Lower
************************************************************/

#define SDPMUX_SION_S1_Client_Req_BurstTarget_Lower_Req_BurstTarget_Lower_OFFSET 0
#define SDPMUX_SION_S1_Client_Req_BurstTarget_Lower_Req_BurstTarget_Lower_MASK 0xffffffff
#define SDPMUX_SION_S1_Client_Req_BurstTarget_Lower_Req_BurstTarget_Lower_DEFAULT     0x4040404

typedef union {
  struct {
    UINT32                               Req_BurstTarget_Lower:32;
  } Field;
  UINT32 Value;
} SDPMUX_SION_S1_Client_Req_BurstTarget_Lower_STRUCT;

#define SMN_SDPMUX_SION_S1_Client_Req_BurstTarget_Lower_ADDRESS       0x4400430UL
#define SMN_IOHUB0_N0NBIO0_SDPMUX_SION_S1_Client_Req_BurstTarget_Lower_ADDRESS 0x4400430UL
#define SMN_IOHUB0_N0NBIO1_SDPMUX_SION_S1_Client_Req_BurstTarget_Lower_ADDRESS 0x4e00430UL
#define SMN_IOHUB0_N1NBIO0_SDPMUX_SION_S1_Client_Req_BurstTarget_Lower_ADDRESS 0x4400830UL
#define SMN_IOHUB0_N1NBIO1_SDPMUX_SION_S1_Client_Req_BurstTarget_Lower_ADDRESS 0x4e00830UL
#define SMN_IOHUB0_N2NBIO0_SDPMUX_SION_S1_Client_Req_BurstTarget_Lower_ADDRESS 0x4400c30UL
#define SMN_IOHUB0_N2NBIO1_SDPMUX_SION_S1_Client_Req_BurstTarget_Lower_ADDRESS 0x4e00c30UL


/***********************************************************
* Register Name : SDPMUX_SION_S1_Client_Req_BurstTarget_Upper
************************************************************/

#define SDPMUX_SION_S1_Client_Req_BurstTarget_Upper_Req_BurstTarget_Upper_OFFSET 0
#define SDPMUX_SION_S1_Client_Req_BurstTarget_Upper_Req_BurstTarget_Upper_MASK 0xffffffff
#define SDPMUX_SION_S1_Client_Req_BurstTarget_Upper_Req_BurstTarget_Upper_DEFAULT     0x4040404

typedef union {
  struct {
    UINT32                               Req_BurstTarget_Upper:32;
  } Field;
  UINT32 Value;
} SDPMUX_SION_S1_Client_Req_BurstTarget_Upper_STRUCT;

#define SMN_SDPMUX_SION_S1_Client_Req_BurstTarget_Upper_ADDRESS       0x4400434UL
#define SMN_IOHUB0_N0NBIO0_SDPMUX_SION_S1_Client_Req_BurstTarget_Upper_ADDRESS 0x4400434UL
#define SMN_IOHUB0_N0NBIO1_SDPMUX_SION_S1_Client_Req_BurstTarget_Upper_ADDRESS 0x4e00434UL
#define SMN_IOHUB0_N1NBIO0_SDPMUX_SION_S1_Client_Req_BurstTarget_Upper_ADDRESS 0x4400834UL
#define SMN_IOHUB0_N1NBIO1_SDPMUX_SION_S1_Client_Req_BurstTarget_Upper_ADDRESS 0x4e00834UL
#define SMN_IOHUB0_N2NBIO0_SDPMUX_SION_S1_Client_Req_BurstTarget_Upper_ADDRESS 0x4400c34UL
#define SMN_IOHUB0_N2NBIO1_SDPMUX_SION_S1_Client_Req_BurstTarget_Upper_ADDRESS 0x4e00c34UL


/***********************************************************
* Register Name : SDPMUX_SION_S1_Client_Req_TimeSlot_Lower
************************************************************/

#define SDPMUX_SION_S1_Client_Req_TimeSlot_Lower_Req_TimeSlot_Lower_OFFSET 0
#define SDPMUX_SION_S1_Client_Req_TimeSlot_Lower_Req_TimeSlot_Lower_MASK 0xffffffff

typedef union {
  struct {
    UINT32                                  Req_TimeSlot_Lower:32;
  } Field;
  UINT32 Value;
} SDPMUX_SION_S1_Client_Req_TimeSlot_Lower_STRUCT;

#define SMN_SDPMUX_SION_S1_Client_Req_TimeSlot_Lower_ADDRESS          0x4400438UL
#define SMN_IOHUB0_N0NBIO0_SDPMUX_SION_S1_Client_Req_TimeSlot_Lower_ADDRESS 0x4400438UL
#define SMN_IOHUB0_N0NBIO1_SDPMUX_SION_S1_Client_Req_TimeSlot_Lower_ADDRESS 0x4e00438UL
#define SMN_IOHUB0_N1NBIO0_SDPMUX_SION_S1_Client_Req_TimeSlot_Lower_ADDRESS 0x4400838UL
#define SMN_IOHUB0_N1NBIO1_SDPMUX_SION_S1_Client_Req_TimeSlot_Lower_ADDRESS 0x4e00838UL
#define SMN_IOHUB0_N2NBIO0_SDPMUX_SION_S1_Client_Req_TimeSlot_Lower_ADDRESS 0x4400c38UL
#define SMN_IOHUB0_N2NBIO1_SDPMUX_SION_S1_Client_Req_TimeSlot_Lower_ADDRESS 0x4e00c38UL


/***********************************************************
* Register Name : SDPMUX_SION_S1_Client_Req_TimeSlot_Upper
************************************************************/

#define SDPMUX_SION_S1_Client_Req_TimeSlot_Upper_Req_TimeSlot_Upper_OFFSET 0
#define SDPMUX_SION_S1_Client_Req_TimeSlot_Upper_Req_TimeSlot_Upper_MASK 0xffffffff

typedef union {
  struct {
    UINT32                                  Req_TimeSlot_Upper:32;
  } Field;
  UINT32 Value;
} SDPMUX_SION_S1_Client_Req_TimeSlot_Upper_STRUCT;

#define SMN_SDPMUX_SION_S1_Client_Req_TimeSlot_Upper_ADDRESS          0x440043cUL
#define SMN_IOHUB0_N0NBIO0_SDPMUX_SION_S1_Client_Req_TimeSlot_Upper_ADDRESS 0x440043cUL
#define SMN_IOHUB0_N0NBIO1_SDPMUX_SION_S1_Client_Req_TimeSlot_Upper_ADDRESS 0x4e0043cUL
#define SMN_IOHUB0_N1NBIO0_SDPMUX_SION_S1_Client_Req_TimeSlot_Upper_ADDRESS 0x440083cUL
#define SMN_IOHUB0_N1NBIO1_SDPMUX_SION_S1_Client_Req_TimeSlot_Upper_ADDRESS 0x4e0083cUL
#define SMN_IOHUB0_N2NBIO0_SDPMUX_SION_S1_Client_Req_TimeSlot_Upper_ADDRESS 0x4400c3cUL
#define SMN_IOHUB0_N2NBIO1_SDPMUX_SION_S1_Client_Req_TimeSlot_Upper_ADDRESS 0x4e00c3cUL


/***********************************************************
* Register Name : SDPMUX_SION_S1_Client_WrRsp_BurstTarget_Lower
************************************************************/

#define SDPMUX_SION_S1_Client_WrRsp_BurstTarget_Lower_WrRsp_BurstTarget_Lower_OFFSET 0
#define SDPMUX_SION_S1_Client_WrRsp_BurstTarget_Lower_WrRsp_BurstTarget_Lower_MASK 0xffffffff

typedef union {
  struct {
    UINT32                             WrRsp_BurstTarget_Lower:32;
  } Field;
  UINT32 Value;
} SDPMUX_SION_S1_Client_WrRsp_BurstTarget_Lower_STRUCT;

#define SMN_SDPMUX_SION_S1_Client_WrRsp_BurstTarget_Lower_ADDRESS     0x4400450UL
#define SMN_IOHUB0_N0NBIO0_SDPMUX_SION_S1_Client_WrRsp_BurstTarget_Lower_ADDRESS 0x4400450UL
#define SMN_IOHUB0_N0NBIO1_SDPMUX_SION_S1_Client_WrRsp_BurstTarget_Lower_ADDRESS 0x4e00450UL
#define SMN_IOHUB0_N1NBIO0_SDPMUX_SION_S1_Client_WrRsp_BurstTarget_Lower_ADDRESS 0x4400850UL
#define SMN_IOHUB0_N1NBIO1_SDPMUX_SION_S1_Client_WrRsp_BurstTarget_Lower_ADDRESS 0x4e00850UL
#define SMN_IOHUB0_N2NBIO0_SDPMUX_SION_S1_Client_WrRsp_BurstTarget_Lower_ADDRESS 0x4400c50UL
#define SMN_IOHUB0_N2NBIO1_SDPMUX_SION_S1_Client_WrRsp_BurstTarget_Lower_ADDRESS 0x4e00c50UL


/***********************************************************
* Register Name : SDPMUX_SION_S1_Client_WrRsp_BurstTarget_Upper
************************************************************/

#define SDPMUX_SION_S1_Client_WrRsp_BurstTarget_Upper_WrRsp_BurstTarget_Upper_OFFSET 0
#define SDPMUX_SION_S1_Client_WrRsp_BurstTarget_Upper_WrRsp_BurstTarget_Upper_MASK 0xffffffff

typedef union {
  struct {
    UINT32                             WrRsp_BurstTarget_Upper:32;
  } Field;
  UINT32 Value;
} SDPMUX_SION_S1_Client_WrRsp_BurstTarget_Upper_STRUCT;

#define SMN_SDPMUX_SION_S1_Client_WrRsp_BurstTarget_Upper_ADDRESS     0x4400454UL
#define SMN_IOHUB0_N0NBIO0_SDPMUX_SION_S1_Client_WrRsp_BurstTarget_Upper_ADDRESS 0x4400454UL
#define SMN_IOHUB0_N0NBIO1_SDPMUX_SION_S1_Client_WrRsp_BurstTarget_Upper_ADDRESS 0x4e00454UL
#define SMN_IOHUB0_N1NBIO0_SDPMUX_SION_S1_Client_WrRsp_BurstTarget_Upper_ADDRESS 0x4400854UL
#define SMN_IOHUB0_N1NBIO1_SDPMUX_SION_S1_Client_WrRsp_BurstTarget_Upper_ADDRESS 0x4e00854UL
#define SMN_IOHUB0_N2NBIO0_SDPMUX_SION_S1_Client_WrRsp_BurstTarget_Upper_ADDRESS 0x4400c54UL
#define SMN_IOHUB0_N2NBIO1_SDPMUX_SION_S1_Client_WrRsp_BurstTarget_Upper_ADDRESS 0x4e00c54UL


/***********************************************************
* Register Name : SDPMUX_SION_S1_Client_WrRsp_TimeSlot_Lower
************************************************************/

#define SDPMUX_SION_S1_Client_WrRsp_TimeSlot_Lower_WrRsp_TimeSlot_Lower_OFFSET 0
#define SDPMUX_SION_S1_Client_WrRsp_TimeSlot_Lower_WrRsp_TimeSlot_Lower_MASK 0xffffffff

typedef union {
  struct {
    UINT32                                WrRsp_TimeSlot_Lower:32;
  } Field;
  UINT32 Value;
} SDPMUX_SION_S1_Client_WrRsp_TimeSlot_Lower_STRUCT;

#define SMN_SDPMUX_SION_S1_Client_WrRsp_TimeSlot_Lower_ADDRESS        0x4400458UL
#define SMN_IOHUB0_N0NBIO0_SDPMUX_SION_S1_Client_WrRsp_TimeSlot_Lower_ADDRESS 0x4400458UL
#define SMN_IOHUB0_N0NBIO1_SDPMUX_SION_S1_Client_WrRsp_TimeSlot_Lower_ADDRESS 0x4e00458UL
#define SMN_IOHUB0_N1NBIO0_SDPMUX_SION_S1_Client_WrRsp_TimeSlot_Lower_ADDRESS 0x4400858UL
#define SMN_IOHUB0_N1NBIO1_SDPMUX_SION_S1_Client_WrRsp_TimeSlot_Lower_ADDRESS 0x4e00858UL
#define SMN_IOHUB0_N2NBIO0_SDPMUX_SION_S1_Client_WrRsp_TimeSlot_Lower_ADDRESS 0x4400c58UL
#define SMN_IOHUB0_N2NBIO1_SDPMUX_SION_S1_Client_WrRsp_TimeSlot_Lower_ADDRESS 0x4e00c58UL


/***********************************************************
* Register Name : SDPMUX_SION_S1_Client_WrRsp_TimeSlot_Upper
************************************************************/

#define SDPMUX_SION_S1_Client_WrRsp_TimeSlot_Upper_WrRsp_TimeSlot_Upper_OFFSET 0
#define SDPMUX_SION_S1_Client_WrRsp_TimeSlot_Upper_WrRsp_TimeSlot_Upper_MASK 0xffffffff

typedef union {
  struct {
    UINT32                                WrRsp_TimeSlot_Upper:32;
  } Field;
  UINT32 Value;
} SDPMUX_SION_S1_Client_WrRsp_TimeSlot_Upper_STRUCT;

#define SMN_SDPMUX_SION_S1_Client_WrRsp_TimeSlot_Upper_ADDRESS        0x440045cUL
#define SMN_IOHUB0_N0NBIO0_SDPMUX_SION_S1_Client_WrRsp_TimeSlot_Upper_ADDRESS 0x440045cUL
#define SMN_IOHUB0_N0NBIO1_SDPMUX_SION_S1_Client_WrRsp_TimeSlot_Upper_ADDRESS 0x4e0045cUL
#define SMN_IOHUB0_N1NBIO0_SDPMUX_SION_S1_Client_WrRsp_TimeSlot_Upper_ADDRESS 0x440085cUL
#define SMN_IOHUB0_N1NBIO1_SDPMUX_SION_S1_Client_WrRsp_TimeSlot_Upper_ADDRESS 0x4e0085cUL
#define SMN_IOHUB0_N2NBIO0_SDPMUX_SION_S1_Client_WrRsp_TimeSlot_Upper_ADDRESS 0x4400c5cUL
#define SMN_IOHUB0_N2NBIO1_SDPMUX_SION_S1_Client_WrRsp_TimeSlot_Upper_ADDRESS 0x4e00c5cUL

#endif /* _SDPMUX_H_ */

