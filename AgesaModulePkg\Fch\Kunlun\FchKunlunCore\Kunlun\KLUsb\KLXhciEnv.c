/*****************************************************************************
 * Copyright (C) 2008-2024 Advanced Micro Devices, Inc. All rights reserved.
 *
*****************************************************************************
*/
/* $NoKeywords:$ */
/**
 * @file
 *
 * Config Fch USB3 controller
 *
 * Init USB3 features.
 *
 * @xrefitem bom "File Content Label" "Release Content"
 * @e project:     AGESA
 * @e sub-project: FCH
 * @e \$Revision: 309083 $   @e \$Date: 2014-12-09 09:28:24 -0800 (Tue, 09 Dec 2014) $
 *
 */
#include "FchPlatform.h"
#include "Filecode.h"
#define FILECODE FCH_KUNLUN_FCHKUNLUNCORE_KUNLUN_KLUSB_KLXHCIENV_FILECODE


/**
 * FchInitEnvUsbXhci - Config XHCI controller before PCI
 * emulation
 *
 *
 *
 * @param[in] FchDataPtr Fch configuration structure pointer.
 *
 */
VOID
FchInitEnvUsbXhci (
  IN  VOID     *FchDataPtr
  )
{
//  FCH_DATA_BLOCK         *LocalCfgPtr;
//  AMD_CONFIG_PARAMS      *StdHeader;

//  LocalCfgPtr = (FCH_DATA_BLOCK *) FchDataPtr;
//  StdHeader = LocalCfgPtr->StdHeader;

  /*
  Because of security consideration, x86 is forbidden to access nBIF straps.
  Move code to ABL.

  if (LocalCfgPtr->Usb.XhciSsid != 0) {
    FchKLXhciInitSsid (0, LocalCfgPtr->Usb.XhciSsid);
  }
  */

  FchKLXhciIohcPmeDisable (0, TRUE);
}



