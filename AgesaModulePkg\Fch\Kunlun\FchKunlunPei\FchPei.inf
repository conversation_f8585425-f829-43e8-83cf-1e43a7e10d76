#;*****************************************************************************
#;
#; Copyright (C) 2008-2024 Advanced Micro Devices, Inc. All rights reserved.
#;
#;******************************************************************************

[defines]

  INF_VERSION                   = 0x00010005
  BASE_NAME                     = FchKunlunPei
  FILE_GUID                     = 27eeec71-d849-4b64-a04b-5643cceac876
  MODULE_TYPE                   = PEIM
  VERSION_STRING                = 1.0
  ENTRY_POINT                   = FchPeiInit

[sources.common]
  FchPei.c
  FchPei.h
  FchReset.c
  FchReset.h
  FchReset2.c
  FchReset2.h
  FchStall.c
  FchStall.h


[sources.IA32]

[sources.X64]

[LibraryClasses.IA32]

[LibraryClasses]
  PeimEntryPoint
  BaseLib
  DebugLib
  IoLib
  PeiServicesLib
  HobLib
  BaseMemoryLib

  NbioHandleLib
  NbioSmuBrhLib
  FchKunlunPeiLib
  FchPeiLibV9
  FchInitHookLibPei
  AmdIdsHookLib
  FchIdsHookLib
  AmdBaseLib
  AgesaConfigLib
  TimerLib
[Pcd]
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdFchCfgSmbus0BaseAddress
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdFchCfgSioPmeBaseAddress
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdFchCfgAcpiPm1EvtBlkAddr
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdFchCfgAcpiPm1CntBlkAddr
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdFchCfgAcpiPmTmrBlkAddr
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdFchCfgCpuControlBlkAddr
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdFchCfgAcpiGpe0BlkAddr
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdFchCfgSmiCmdPortAddr
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdFchFullHardReset

  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdLegacyFree
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdEcKbd
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdFchOscout1ClkContinous
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdSataEnable
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdSataSetMaxGen2
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdSataClkMode
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdSataEnable2
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdSataMultiDiePortRxPolarity

  gEfiAmdAgesaPkgTokenSpaceGuid.PcdXhci0Enable
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdXhci1Enable
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdLpcClockDriveStrength
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdLpcClockDriveStrengthRiseTime
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdLpcClockDriveStrengthFallTime
  #gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdResetMode
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdResetSpiSpeed
  #gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdResetFastSpeed
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdResetWriteSpeed
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdResetSpiTpmSpeed
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdLpcEnable
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdLpcClk0
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdLpcClk1
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdFchWdtEnable
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdSerialDebugBusEnable
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdXhci0DevRemovable
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdDisableXhciPortLate
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdXhciUsb3PortDisable
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdXhciUsb2PortDisable
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdXhciUsb20OcPinSelect
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdXhciUsb31OcPinSelect
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdXhciOcPolarityCfgLow
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdXhciForceGen1
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdUsbKLOemConfigurationTable
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdSerialIrqEnable
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdEmmcEnable
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdSataActLWkaEnable
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdBootTimerEnable
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdBootTimerResetType
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdUsbSparseModeEnable
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdFchI2c0SdaHold
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdFchI2c1SdaHold
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdFchI2c2SdaHold
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdFchI2c3SdaHold
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdFchI2c4SdaHold
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdFchI2c5SdaHold
  gEfiAmdAgesaModulePkgTokenSpaceGuid.FchRTDeviceEnableMap
  gEfiAmdAgesaModulePkgTokenSpaceGuid.FchRTDeviceEnableMapEx
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdSataStaggeredSpinup
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgFchIoapicId
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdToggleAllPwrGoodOnCf9
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdUsbDbgSCPipeSwitchEnable
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdFchIsAfterG3
 #
 # Promontory
 #
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdPTXhciGen1
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdPTXhciGen2
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdPTAOAC
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdPTHW_LPM
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdPTDbC
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdPTXHC_PME
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdPTSataMode
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdPTSataAggresiveDevSlpP0
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdPTSataAggresiveDevSlpP1
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdPTSataAggrLinkPmCap
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdPTSataPscCap
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdPTSataSscCap
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdPTSataMsiCapability
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdPTSataPortMdPort0
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdPTSataPortMdPort1
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdPTSataHotPlug
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdPTUsbEqualization4
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdPTUsbRedriver
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdPTUsb31P0
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdPTUsb31P1
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdPTUsb30P0
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdPTUsb30P1
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdPTUsb30P2
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdPTUsb30P3
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdPTUsb30P4
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdPTUsb30P5
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdPTUsb20P0
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdPTUsb20P1
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdPTUsb20P2
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdPTUsb20P3
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdPTUsb20P4
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdPTUsb20P5
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdPTProm2Usb31P0
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdPTProm2Usb31P1
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdPTProm2Usb30P0
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdPTProm2Usb30P1
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdPTProm2Usb20P0
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdPTProm2Usb20P1
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdPTProm2Usb20P2
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdPTProm2Usb20P3
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdPTProm2Usb20P4
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdPTProm2Usb20P5
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdPTProm1Usb31P0
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdPTProm1Usb31P1
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdPTProm1Usb30P0
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdPTProm1Usb20P0
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdPTProm1Usb20P1
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdPTProm1Usb20P2
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdPTProm1Usb20P3
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdPTProm1Usb20P4
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdPTProm1Usb20P5
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdPTSataPort0Enable
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdPTSataPort1Enable
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdPTSataPort2Enable
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdPTSataPort3Enable
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdPTSataPort4Enable
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdPTSataPort5Enable
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdPTSataPort6Enable
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdPTSataPort7Enable
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdPTPciePort0Enable
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdPTPciePort1Enable
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdPTPciePort2Enable
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdPTPciePort3Enable
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdPTPciePort4Enable
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdPTPciePort5Enable
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdPTPciePort6Enable
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdPTPciePort7Enable
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdPTGppClk0ForceOn
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdPTGppClk1ForceOn
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdPTGppClk2ForceOn
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdPTGppClk3ForceOn
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdPTGppClk4ForceOn
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdPTGppClk5ForceOn
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdPTGppClk6ForceOn
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdPTGppClk7ForceOn
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdPTPro460Usb31P0
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdPTPro460Usb31P1
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdPTPro460Usb30P0
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdPTPro460Usb30P1
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdPTPro460Usb30P2
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdPTPro460Usb30P3
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdPTPro460Usb20P0
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdPTPro460Usb20P1
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdPTPro460Usb20P2
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdPTPro460Usb20P3
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdPTPro460Usb20P4
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdPTPro460Usb20P5
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdPTLock
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdAmdFchDisableAsfSlave
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdSataClass
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdSataRasSupport
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdSataAhciDisPrefetchFunction
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdSataDevSlpPort0
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdSataDevSlpPort1
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdXhci2Enable
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdXhci3Enable
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdPwrFailShadow
  gEfiAmdAgesaPkgTokenSpaceGuid.FchUart0LegacyEnable
  gEfiAmdAgesaPkgTokenSpaceGuid.FchUart1LegacyEnable
  gEfiAmdAgesaPkgTokenSpaceGuid.FchUart2LegacyEnable
  gEfiAmdAgesaPkgTokenSpaceGuid.FchUart3LegacyEnable
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdFchAlinkRasSupport
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdResetCpuOnSyncFlood

[Guids]
  gFchResetDataHobGuid
  gEfiAmdAgesaSpecificWarmResetGuid

[Protocols]

[Ppis]
  gEfiPeiStallPpiGuid           #PRODUCED
  gEfiPeiResetPpiGuid           #PRODUCED
  gEfiPeiReset2PpiGuid          #PRODUCED
  gAmdFchInitPpiGuid            #PRODUCED
  gEfiPeiMemoryDiscoveredPpiGuid

[Packages]
  MdePkg/MdePkg.dec
  AgesaPkg/AgesaPkg.dec
  AgesaModulePkg/AgesaModuleFchPkg.dec
  AgesaModulePkg/Fch/Kunlun/FchKunlun.dec
  AgesaModulePkg/AgesaCommonModulePkg.dec
  AgesaModulePkg/AgesaModuleNbioPkg.dec


[Depex]
  gEfiPeiCpuIoPpiInstalledGuid AND
  gAmdFchKunlunDepexPpiGuid

[BuildOptions]





