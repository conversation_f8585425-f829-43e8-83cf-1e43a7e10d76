/*
*****************************************************************************
*
 * Copyright (C) 2008-2024 Advanced Micro Devices, Inc. All rights reserved.
 *
*******************************************************************************
*/
/* $NoKeywords:$ */
/**
 * @file
 *
 * PCIe AER definitions.
 *
 *
 *
 * @xrefitem bom "File Content Label" "Release Content"
 * @e project:     AGESA
 * @e sub-project: NBIO
 * @e \$Revision: 313706 $   @e \$Date: 2015-02-25 21:00:43 -0600 (Wed, 25 Feb 2015) $
 *
 */

#ifndef _GNB_PCIE_AER_H_
#define _GNB_PCIE_AER_H_

#pragma pack (push, 1)

// Need to remove these below 4 structures for AGESA V9+ in favor of the ones in GnbPciLib.h based off of the actual PCIe spec
// These defines all have multiple duplicates. which is is confusing as well as do not list all bits available now.

/// To be deprecated
/// PCIe Correctable Error Mask
typedef union {
  struct {                                                            ///<
    UINT32                  Reserved_5_0 :6;                          ///<
    UINT32                  BadTLPMask :1;                            ///< [6] BadTlpMask
    UINT32                  BadDLLPMask :1;                           ///< [7] BadDllpMask
    UINT32                  ReplayNumberRolloverMask :1;              ///< [8] ReplayNumRolloverMask
    UINT32                  Reserved_11_9 :3;                         ///<
    UINT32                  ReplayTimerTimeoutMask :1;                ///< [12] ReplayTimerTimeoutMask
    UINT32                  AdvisoryNonFatalErrorMask :1;             ///< [13] AdvisoryNonFatalErrorMask
    UINT32                  CorrectedInternalErrorMask:1;             ///< [14] CorrectedInternalErrorMask
    UINT32                  HeaderLogOverflowMask:1;                  ///< [15] HeaderLogOverflowMask
    UINT32                  Reserved_31_16:16;                        ///< Reserved
  } Field;                                                            ///<
  UINT32 Value;                                                       ///<
} NBIO_PCIe_AER_CORRECTABLE_MASK;

/// To be deprecated
/// PCIe Unorrectable Error Mask
typedef union {
  struct {
    UINT32                  Undefined1:1;
    UINT32                  Reserved1:3;                     ///< Reserved
    UINT32                  DataLinkProtocolErrorMask:1;     ///< Controls if function can report Data Link Protocol Error.
    UINT32                  SurpriseDownErrorMask:1;         ///< Controls if function can report Surprise down error.
    UINT32                  Reserved2:6;                     ///< Reserved
    UINT32                  PoisonedTLPMask:1;               ///< Controls if function can report Poisoned TLP error.
    UINT32                  FlowControlProtocolErrorMask:1;  ///< Controls if function can report Flow control error.
    UINT32                  CompletionTimeoutMask:1;         ///< Controls if function can report Completion Timeout error.
    UINT32                  CompleterAbortMask:1;            ///< Controls if function can report Completion abort error
    UINT32                  UnexpectedCompletionMask:1;      ///< Controls if function can report Unexpected Completion error.
    UINT32                  ReceiverOverflowMask:1;          ///< Controls if function can report Reveiver Overflow error.
    UINT32                  MalTlpMask:1;                    ///< Controls if function can report Malformed TLP error.
    UINT32                  ECRCErrorMask:1;                 ///< Controls if function can report ECRC error.
    UINT32                  UnsupportedRequestErrorMask:1;   ///< Controls if function can report unsupported request error.
    UINT32                  AcsViolationMask:1;              ///< Controls if function can report ACS violation error.
    UINT32                  UncorrectableInternalErrorMask:1;///< Controls if function can report Internal error.
    UINT32                  McBlockedTlpMask:1;              ///< Controls if function can report Mc Blocked Tlp error.
    UINT32                  AtomicopEgressBlockedMask:1;     ///< Controls if function can report Atomic Op egress blocked.
    UINT32                  TlpPrefixBlockedErrorMask:1;     ///< Controls if function can report TLP prefix block error.
    UINT32                  PoisonedTlpEgressBlockedMask:1;  ///< Controls if function can report TLP egress error.
    UINT32                  Reserved3:1;                     ///< Reserved
    UINT32                  IdeCheckFailedMask:1;            ///< Controls if the function can report a IDE Check Failed Error
    UINT32                  MisroutedIdeTlpMask:1;           ///< Controls if the function can report a Misrouted IDE TLP Error.
    UINT32                  PcrcCheckFailedMask:1;           ///< Controls if the function can report a PCRC Check Failed Error
    UINT32                  Reserved4:1;                     ///< Reserved
  } Field;                                                   ///<
  UINT32 Value;                                              ///<
} NBIO_PCIe_AER_UNCORRECTABLE_MASK;

/// To be deprecated
/// PCIe Unorrectable Error Severity
typedef union {
  struct {
    UINT32                  Undefined1:1;
    UINT32                  Reserved1:3;                           ///< Reserved
    UINT32                  DataLinkProtocolErrorSeverity:1;       ///< Controls if function can report Data Link Protocol Error.
    UINT32                  SurpriseDownErrorSeverity:1;           ///< Controls if function can report Surprise down error.
    UINT32                  Reserved2:6;                           ///< Reserved
    UINT32                  PoisonedTLPSeverity:1;                 ///< Controls if function can report Poisoned TLP error.
    UINT32                  FlowControlProtocolErrorSeverity:1;    ///< Controls if function can report Flow control error.
    UINT32                  CompletionTimeoutSeverity :1;          ///< Controls if function can report Completion Timeout error.
    UINT32                  CompleterAbortSeverity :1;             ///< Controls if function can report Completion abort error
    UINT32                  UnexpectedCompletionSeverity:1;        ///< Controls if function can report Unexpected Completion error.
    UINT32                  ReceiverOverflowErrorSeverity:1;       ///< Controls if function can report Reveiver Overflow error.
    UINT32                  MalTlpSeverity:1;                      ///< Controls if function can report Malformed TLP error.
    UINT32                  ECRCErrorSeverity:1;                   ///< Controls if function can report ECRC error.
    UINT32                  UnsupportedRequestErrorSeverity:1;     ///< Controls if function can report unsupported request error.
    UINT32                  AcsViolationSeverity:1;                ///< Controls if function can report ACS violation error.
    UINT32                  UncorrectableInternalErrorSeverity:1;  ///< Controls if function can report Internal error.
    UINT32                  McBlockedTlpSeverity:1;                ///< Controls if function can report Mc Blocked Tlp error.
    UINT32                  AtomicOpEgressBlockedSeverity:1;       ///< Controls if function can report Atomic Op egress blocked.
    UINT32                  TlpPrefixBlockedErrorSeverity:1;       ///< Controls if function can report TLP prefix block error.
    UINT32                  PoisonedTlpEgressBlockedESeverity:1;   ///< Controls if function can report TLP egress error.
    UINT32                  Reserved3:1;                           ///< Reserved
    UINT32                  IdeCheckFailedSeverity:1;              ///< Controls how the function reports a IDE Check Failed Error.
    UINT32                  MisroutedIdeTlpSeverity:1;             ///< Controls how the function reports a Misrouted IDE TLP Error.
    UINT32                  PcrcCheckFailedSeverity:1;             ///< Controls how the function reports a PCRC Check Failed Blocked Error.
    UINT32                  Reserved4:1;                           ///< Reserved
  } Field;                                                            ///<
  UINT32 Value;                                                       ///<
} NBIO_PCIe_AER_UNCORRECTABLE_SEVERITY;
#pragma pack (pop)

/// To be deprecated
/// PCIe AER Port Configuration
typedef struct {
  UINT8                                PortAerEnable;                 ///< General per-port enable
  UINT8                                PciDev;                        ///< PCI Device Number
  UINT8                                PciFunc;                       ///< PCI Function Number
  NBIO_PCIe_AER_CORRECTABLE_MASK       CorrectableMask;               ///< Per-port mask for correctable errors
  NBIO_PCIe_AER_UNCORRECTABLE_MASK     UncorrectableMask;             ///< Per-port mask for uncorrectable errors
  NBIO_PCIe_AER_UNCORRECTABLE_SEVERITY UncorrectableSeverity;         ///< Per-port severity configuration for uncorrectable errors
} NBIO_PCIe_PORT_AER_CONFIG;

/// PCIe AER Configuration
typedef struct {
  UINT8                      NumberOfPorts;                           ///< The amount of ports of GNB
  NBIO_PCIe_PORT_AER_CONFIG  PortAerConfig[];                         ///< Per-port AER configuration
} AMD_NBIO_PCIe_AER_CONFIG;

#endif

