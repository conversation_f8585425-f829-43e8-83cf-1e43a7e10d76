/*
*****************************************************************************
*
 * Copyright (C) 2008-2024 Advanced Micro Devices, Inc. All rights reserved.
 *
*******************************************************************************
*/
/* $NoKeywords:$ */
/**
 * @file
 *
 * Config Fch Xhci controller
 *
 * Init Xhci Controller features (PEI phase).
 *
 * @xrefitem bom "File Content Label" "Release Content"
 * @e project:     AGESA
 * @e sub-project: FCH
 * @e \$Revision: 309083 $   @e \$Date: 2014-12-09 09:28:24 -0800 (Tue, 09 Dec 2014) $
 *
 */
#include "FchPlatform.h"
#include "Filecode.h"
#include <Library/AmdPspMboxLibV2.h>
#include <Library/BaseMemoryLib.h>

#define FILECODE FCH_KUNLUN_FCHKUNLUNCORE_KUNLUN_KLUSB_KLXHCISERVICE_FILECODE

#define USB_CONFIG_REGISTER_MOD_OPCODE       (1<<24)

// Group 1: USB Container Registers, USB PD I2C Slave Registers, and TCA Registers
//  # Programmed before reset is de-asserted to PHY and Host Controller.
//  # Programmed per cold-boot, warm-reset, and S4/S5 exit.
#define USB_CONFIG_REGISTER_GROUP_1   1

// Group 2: USB PHY parameter and Internal Registers, post PHY Firmware start
//  # Programmed after reset is de-asserted to PHY, but before reset is de-asserted to Host Controller.
//  # Programmed per cold-boot, warm-reset, S4/S5 exit, and link-reset.
#define USB_CONFIG_REGISTER_GROUP_2   2

// Group 3: USB IP Core Registers
//  # Programmed after reset is de-asserted to PHY and Host Controller.
//  # Programmed per cold-boot, warm-reset, S4/S5 exit, link-reset, S3 exit, and D3 exit.
#define USB_CONFIG_REGISTER_GROUP_3   3

// Group 4: PHY Internal Registers, pew PHY FW start.
#define USB_CONFIG_REGISTER_GROUP_4   4

/*
Because of security consideration, x86 is forbidden to access nBIF straps.
Move code to ABL.

VOID
FchInitXhciEnableKL (
  IN  VOID     *FchDataPtr
  )
{
  FCH_RESET_DATA_BLOCK        *LocalCfgPtr;
  LocalCfgPtr = (FCH_RESET_DATA_BLOCK *)FchDataPtr;

  if (LocalCfgPtr->FchReset.Xhci0Enable == FALSE) {
    FchSmnRW (0, FCH_KL_USB0_NBIF_STRAP0, ~(UINT32) BIT28, 0, NULL);
  }
#if FCH_USB3_CONTROLLER_NUM > 1
  if (LocalCfgPtr->FchReset.Xhci1Enable == FALSE) {
    FchSmnRW (0, FCH_KL_USB1_NBIF_STRAP0, ~(UINT32) BIT28, 0, NULL);
  }
#endif
}
*/


VOID
FchKLXhciIohcPmeDisable (
  IN  UINT32    DieBusNum,
  IN  BOOLEAN   PMEDis
  )
{
  if (PMEDis) {
    FchSmnRW (DieBusNum, FCH_KL_NBIO0_IOHC_NB_PCI_CTRL, ~(UINT32) BIT4, BIT4, NULL);
#if FCH_USB3_CONTROLLER_NUM > 1
    FchSmnRW (DieBusNum, FCH_KL_NBIO1_IOHC_NB_PCI_CTRL, ~(UINT32) BIT4, BIT4, NULL);
#endif
  } else {
    FchSmnRW (DieBusNum, FCH_KL_NBIO0_IOHC_NB_PCI_CTRL, ~(UINT32) BIT4, 0, NULL);
#if FCH_USB3_CONTROLLER_NUM > 1
    FchSmnRW (DieBusNum, FCH_KL_NBIO1_IOHC_NB_PCI_CTRL, ~(UINT32) BIT4, 0, NULL);
#endif
  }
}


/**
 * @brief Program PME enable bit for XHCI controllers
 *
 * @returns VOID
 */
VOID
FchProgramXhciPmeEn (
  IN  UINT32    DieBusNum,
  IN  BOOLEAN   Xhci0Enable,
  IN  BOOLEAN   Xhci1Enable
  )
{
  UINT32  Value32;

  Value32 = 0;

  IDS_HDT_CONSOLE (FCH_TRACE, "%a Start\n");

  //
  // Put Usb3 to S0 power rail
  //
  FchSmnRW (DieBusNum, ACPI_SMN_BASE + PMIO_BASE + FCH_PMIOA_REGEE, (UINT32)~(BIT0 + BIT1), (BIT1 + BIT0), NULL);

#if FCH_USB3_CONTROLLER_NUM > 1
  FchSmnRW (DieBusNum, ACPI_SMN_BASE + PMIO_BASE + FCH_PMIOA_REGEE, (UINT32)~(BIT4 + BIT5), (BIT4 + BIT5), NULL);
#endif

  //
  // Enable XHCI PME
  //
  FchSmnRead (DieBusNum, ACPI_SMN_BASE + SMI_BASE + FCH_SMI_SCIMAP56, &Value32, NULL);  // FCH::SMI::SCIMAP14

  if ( Xhci0Enable ){
    FchSmnRW (
      DieBusNum,
      ACPI_SMN_BASE + SMI_BASE + FCH_SMI_REG04,
      ~(1 << ( Value32 & 0x1F )),             // Bit[4:0] Mapping of XHC-0 PME event to one of the 32 Event_Status bits
      1 << ( Value32 & 0x1F ),
      NULL
      );
  }

#if FCH_USB3_CONTROLLER_NUM > 1
  if ( Xhci1Enable ){
    FchSmnRW (
      DieBusNum,
      ACPI_SMN_BASE + SMI_BASE + FCH_SMI_REG04,
      ~(1 << ( ( Value32 >> 8 ) & 0x1F )),    // Bit[12:8] Mapping of XHC-1 PME event to one of the 32 Event_Status bits
      1 << ( ( Value32 >> 8 ) & 0x1F ),
      NULL
      );
  }
#endif

  // Clear SMI event status
  FchSmnRW (
    DieBusNum,
    ACPI_SMN_BASE + SMI_BASE + FCH_SMI_REG00, // FCH::SMI::EVENT_STATUS
    0xFFFFFFFF,
    0,
    NULL
    );

  IDS_HDT_CONSOLE (FCH_TRACE, "%a Exit\n");
}


/**
 * FchKLXhciSmuService Request -  Xhci1 SMU Service Request
 *
 *
 * @param[in] RequestId           Request ID.
 *
 */
BOOLEAN
FchKLXhciSmuService (
  IN  UINT32    DieBusNum,
  IN  UINT32    RequestId
  )
{
  BOOLEAN status;
  PCI_ADDR        NbioPciAddress;
  UINT32          SmuArg[6];

  status = FALSE;
  NbioSmuServiceCommonInitArguments (SmuArg);
  NbioPciAddress.AddressValue = MAKE_SBDFO (DF_GET_SEGMENT(DieBusNum), DF_GET_BUS(DieBusNum), 0, 0, 0);
  if (NbioSmuServiceRequest (NbioPciAddress, RequestId, SmuArg,0)) {
    status = TRUE;
  }

  return status;
}

/**
 * FchKLXhciSmuServiceUsbInit  -  Xhci SMU Service USB Init
 * Request
 *
 *
 *
 */
BOOLEAN
FchKLXhciSmuServiceUsbInit (
  IN  UINT32    DieBusNum,
  IN  FCH_RESET_DATA_BLOCK     *FchDataPtr
  )
{
  BOOLEAN         status;
  PCI_ADDR        NbioPciAddress;
  UINT32          SmuArg[6];
  FCH_RESET_DATA_BLOCK        *LocalCfgPtr;

  LocalCfgPtr = (FCH_RESET_DATA_BLOCK *)FchDataPtr;

  status = FALSE;
  NbioSmuServiceCommonInitArguments (SmuArg);
  NbioPciAddress.AddressValue = MAKE_SBDFO (DF_GET_SEGMENT(DieBusNum), DF_GET_BUS(DieBusNum), 0, 0, 0);
  SmuArg[0] = 0;
  SmuArg[1] = 0;
  if (LocalCfgPtr->FchReset.Xhci0Enable) {
    SmuArg[0] |= BIT0;
  }
#if FCH_USB3_CONTROLLER_NUM > 1
  if (LocalCfgPtr->FchReset.Xhci1Enable) {
    SmuArg[0] |= BIT1;
  }
#endif
  if (NbioSmuServiceRequest (NbioPciAddress, BIOSSMC_MSG_UsbInit, SmuArg,0)) {
    status = TRUE;
  }

  return status;
}

/**
 * FchKLXhciSmuUsbConfigUpdate  -  Xhci Smu Usb Config Update
 *
 *
 *
 */
BOOLEAN
FchKLXhciSmuUsbConfigUpdate (
  IN  UINT32    DieBusNum,
  IN  UINT32    smn_register,
  IN  UINT32    smn_mask,
  IN  UINT32    smn_data,
  IN  UINT32    smn_group
  )
{
  BOOLEAN status;
  PCI_ADDR        NbioPciAddress;
  UINT32          SmuArg[6];
  status = FALSE;
  NbioSmuServiceCommonInitArguments (SmuArg);
  SmuArg[0] = smn_register;
  SmuArg[1] = smn_mask;
  SmuArg[2] = smn_data;
  SmuArg[3] = smn_group;
  NbioPciAddress.AddressValue = MAKE_SBDFO (DF_GET_SEGMENT(DieBusNum), DF_GET_BUS(DieBusNum), 0, 0, 0);
  if (NbioSmuServiceRequest (NbioPciAddress, BIOSSMC_MSG_UsbConfigUpdate, SmuArg,0)) {
    status = TRUE;
  }

  return status;
}


/**
 * FchKLXhciSmuUsbConfigUpdateRegister  -  Xhci Smu Usb Config Update
 *
 *
 *
 */
BOOLEAN
FchKLXhciSmuUsbConfigUpdateReg (
  IN  UINT32    DieBusNum,
  IN  UINT32    smn_register,
  IN  UINT32    smn_mask,
  IN  UINT32    smn_data,
  IN  UINT32    smn_usb_host,
  IN  UINT32    smn_usb_group
  )
{
  UINT32  smn_group;

  smn_group = USB_CONFIG_REGISTER_MOD_OPCODE + ((smn_usb_host & 0xF) << 20) + smn_usb_group;

  IDS_HDT_CONSOLE (
    FCH_TRACE,
    "[FCH] UsbConfigUpdate Message to Bus 0x%x with %x, %x, %x, %x\n",
    DieBusNum,
    smn_register,
    smn_mask,
    smn_data,
    smn_group
    );

  return FchKLXhciSmuUsbConfigUpdate (
           DieBusNum,
           smn_register,
           smn_mask,
           smn_data,
           smn_group
           );
}

/**
 * FchKLXhciSubSequenceEnable  -  Xhci SubSequence Enable
 *
 *
 *
 */
VOID
FchKLXhciSubSequenceEnable (
  IN  UINT32   DieBusNum,
  IN  VOID     *FchDataPtr
  )
{
  FCH_RESET_DATA_BLOCK        *LocalCfgPtr;
  LocalCfgPtr = (FCH_RESET_DATA_BLOCK *)FchDataPtr;

  IDS_HDT_CONSOLE (FCH_TRACE, "[FCH]FchKLXhciSubSequenceEnable is started!\n");
  if (LocalCfgPtr->FchReset.Xhci0Enable) {
    // Type-A PHY
    FchKLXhciSmuUsbConfigUpdate (DieBusNum, 0x00000000, 0x00000000, 0x00000000, 0xC0000002);
  }

#if FCH_USB3_CONTROLLER_NUM > 1
  if (LocalCfgPtr->FchReset.Xhci1Enable) {
    // Type-A PHY
    FchKLXhciSmuUsbConfigUpdate (DieBusNum, 0x00000000, 0x00000000, 0x00000000, 0xC0100002);
  }
#endif
  IDS_HDT_CONSOLE (FCH_TRACE, "[FCH]FchKLXhciSubSequenceEnable is completed!\n");
}


/**
 * FchKLXhciDbgSC  -  Xhci enable/disable  DbgSC feature
 *
 *
 */
VOID
FchKLXhciDbgSC(
  IN  UINT32   DieBusNum,
  IN  VOID     *FchDataPtr
  )
{
  FCH_RESET_DATA_BLOCK        *LocalCfgPtr;

  LocalCfgPtr = (FCH_RESET_DATA_BLOCK *)FchDataPtr;

  IDS_HDT_CONSOLE (FCH_TRACE, "UsbDbgSC PipeSwitch=0x%x\n",LocalCfgPtr->UsbDbgSCPipeSwitchEnable);

  if ( LocalCfgPtr->UsbDbgSCPipeSwitchEnable ) {
    IDS_HDT_CONSOLE (FCH_TRACE, "UsbDbgSC PipeSwitch Enable \n");
    FchKLXhciSmuUsbConfigUpdate (DieBusNum, 0, 0, 0, 0xF5000101);
  } else {
    IDS_HDT_CONSOLE (FCH_TRACE, "UsbDbgSC PipeSwitch Dis \n");
    FchKLXhciSmuUsbConfigUpdate (DieBusNum, 0, 0, 0, 0xF5000000);
  }
}


/**
 * @brief Disable USB Port by PSP mailbox
 *        This PSP mailbox can only be run in SMM.
 *
 * @param SocketId          Processor number of P1 or P2
 * @param XhciEnable        Bitmap of XHCI controller enablement
 *                           Bit[0] - Controller[0]
 *                           Bit[1] - Controller[1]
 * @param Usb2PortDisable   Bitmap of USB2 port disable.
 *                           Bit[0] - Controller[0].Port[0]
 *                           Bit[1] - Controller[0].Port[1]
 *                           Bit[2] - Controller[1].Port[0]
 *                           Bit[3] - Controller[1].Port[1]
 * @param Usb3PortDisable   Bitmap of USB3 port disable
 *                           Bit[0] - Controller[0].Port[0]
 *                           Bit[1] - Controller[0].Port[1]
 *                           Bit[2] - Controller[1].Port[0]
 *                           Bit[3] - Controller[1].Port[1]
 *
 * @returns VOID
 */
VOID
FchKLXhciDisablePortByPspMbox (
  IN      UINT32    SocketId,
  IN      UINT8     XhciEnable,
  IN      UINT32    Usb2PortDisable,
  IN      UINT32    Usb3PortDisable,
  IN      UINT8     *SmmBuffer,
  IN      BOOLEAN   *SmmFlag
  )
{
  EFI_STATUS          Status;
  UINT32              UsbController;
  USB_CONFIG_DATA     UsbConfigData;

  Status              = EFI_SUCCESS;
  UsbController       = 0;

  IDS_HDT_CONSOLE (FCH_TRACE, "[FCH] %a Started (Socket: %d)\n", __FUNCTION__, SocketId);

  IDS_HDT_CONSOLE (
    FCH_TRACE,
    " XhciEnable = 0x%x Usb2PortDisable = 0x%x Usb3PortDisable = 0x%x\n",
    XhciEnable,
    Usb2PortDisable,
    Usb3PortDisable
    );

  for (UsbController = 0; UsbController < FCH_USB3_CONTROLLER_NUM; UsbController ++) {

    ZeroMem (&UsbConfigData, sizeof (USB_CONFIG_DATA));

    if ( XhciEnable & ( 1 << UsbController ) ) {
      // Set USB port disable value.
      //  USB31::USBCONTAINERREGCNTR0::PORT_DISABLE_WRITE_ONCE_CNTR0
      //   Bit[15:0]  - USB2_PORT_DISABLE_WO
      //   Bit[31:16] - USB3_PORT_DISABLE_WO
      UsbConfigData.UsbConfigureData0  = (Usb2PortDisable>>(UsbController*FCH_USB3_PORT_MNU_PER_CONTROLLER))&0x3;         // USB2 port disable
      UsbConfigData.UsbConfigureData0 |= ((Usb3PortDisable>>(UsbController*FCH_USB3_PORT_MNU_PER_CONTROLLER))&0x3)<<16;   // USB3 port disable
    } else {
      UsbConfigData.UsbConfigureData0 = 0;
    }
    IDS_HDT_CONSOLE (FCH_TRACE, " Controller %d UsbConfigData.UsbConfigureData0 = 0x%x\n", UsbController, UsbConfigData.UsbConfigureData0);

    // Send PSP mailbox command only port disable is needed.
    if ( UsbConfigData.UsbConfigureData0 ) {
      UsbConfigData.SocketId          = SocketId;
      UsbConfigData.UsbRegisterID     = (UsbController << 24) | ID_PORT_DISABLE_WRITE_ONCE_CNTR;
      UsbConfigData.UsbConfigureOp    = MBOX_USB_CONFIG_OP_WR;
      UsbConfigData.UsbConfigureData1 = UsbConfigData.UsbConfigureData0;
      Status = PspMboxBiosCmdUsbConfig (
                 &UsbConfigData,
                 SmmBuffer,
                 SmmFlag
                 );
      IDS_HDT_CONSOLE (
        FCH_TRACE,
        " PSP Mbox USB Config status %r (SocketId: 0x%x, UsbRegisterID: 0x%x, UsbConfigureOp: 0x%x, UsbConfigureData0: 0x%x)\n",
        Status,
        UsbConfigData.SocketId,
        UsbConfigData.UsbRegisterID,
        UsbConfigData.UsbConfigureOp,
        UsbConfigData.UsbConfigureData0
        );
      if ( !EFI_ERROR (Status) ) {
        // Read back
        UsbConfigData.SocketId          = SocketId;
        UsbConfigData.UsbRegisterID     = (UsbController << 24) | ID_PORT_DISABLE_WRITE_ONCE_CNTR;
        UsbConfigData.UsbConfigureOp    = MBOX_USB_CONFIG_OP_RD;
        UsbConfigData.UsbConfigureData0 = 0;
        Status = PspMboxBiosCmdUsbConfig (
                   &UsbConfigData,
                   SmmBuffer,
                   SmmFlag
                   );
        IDS_HDT_CONSOLE (
          FCH_TRACE,
          " Read back PSP Mbox USB Config status %r (SocketId: 0x%x, UsbRegisterID: 0x%x, UsbConfigureOp: 0x%x, UsbConfigureData0: 0x%x)\n",
          Status,
          UsbConfigData.SocketId,
          UsbConfigData.UsbRegisterID,
          UsbConfigData.UsbConfigureOp,
          UsbConfigData.UsbConfigureData0
          );
      }
    }
  }

  IDS_HDT_CONSOLE (FCH_TRACE, "[FCH] %a Exit\n", __FUNCTION__);
}


/**
 * FchKLXhciOverCurrent  -  Xhci OC Pin Mapping
 *
 *
 *
 */
VOID
FchKLXhciOverCurrent (
  IN  UINT32   DieBusNum,
  IN  VOID     *FchDataPtr
  )
{
  UINT32                      OverCurrentMap;
  FCH_RESET_DATA_BLOCK        *LocalCfgPtr;
  LocalCfgPtr = (FCH_RESET_DATA_BLOCK *)FchDataPtr;

  // Port0-1 : Controller0
  OverCurrentMap = (LocalCfgPtr->XhciOCpinSelect[0].Usb20OcPin & 0xFF);
  OverCurrentMap |= ((LocalCfgPtr->XhciOCpinSelect[0].Usb31OcPin & 0xFF) << 8);
  FchKLXhciSmuUsbConfigUpdate (DieBusNum, FCH_KL_USB_PORT_OCMAPPING0, 0x0000FFFF, OverCurrentMap, 0x01000001);
  IDS_HDT_CONSOLE (
    FCH_TRACE,
    "[FCH]FchKLXhciOverCurrent through UsbConfigUpdate Message with 0x16D80120, 0x0000FFFF, %x, 0x01000001\n",
    OverCurrentMap
    );

#if FCH_USB3_CONTROLLER_NUM > 1
  // Port2-3 : Controller1
  OverCurrentMap = (LocalCfgPtr->XhciOCpinSelect[1].Usb20OcPin & 0xFF);
  OverCurrentMap |= ((LocalCfgPtr->XhciOCpinSelect[1].Usb31OcPin & 0xFF) << 8);
  FchKLXhciSmuUsbConfigUpdate (DieBusNum, FCH_KL_USB_PORT_OCMAPPING0, 0x0000FFFF, OverCurrentMap, 0x01100001);
  IDS_HDT_CONSOLE (
    FCH_TRACE,
    "[FCH]FchKLXhciOverCurrent through UsbConfigUpdate Message with 0x16F80120, 0x0000FFFF, %x, 0x01000001\n",
    OverCurrentMap
    );
#endif
}

VOID
FchKLXhciSparseMode(
  IN  UINT32   DieBusNum,
  IN  VOID     *FchDataPtr
  )
{
  FCH_RESET_DATA_BLOCK        *LocalCfgPtr;
  LocalCfgPtr = (FCH_RESET_DATA_BLOCK *)FchDataPtr;

  if (LocalCfgPtr->UsbSparseModeEnable) {
    FchKLXhciSmuUsbConfigUpdate (DieBusNum, FCH_KL_USB_GUCTL, BIT17, BIT17, 0x01000003);
#if FCH_USB3_CONTROLLER_NUM > 1
    FchKLXhciSmuUsbConfigUpdate (DieBusNum, FCH_KL_USB_GUCTL, BIT17, BIT17, 0x01100003);
#endif
    IDS_HDT_CONSOLE (FCH_TRACE, "[FCH]FchKLXhciSparseMode to Enabled\n");
  } else {
    FchKLXhciSmuUsbConfigUpdate (DieBusNum, FCH_KL_USB_GUCTL, BIT17, 0, 0x01000003);
#if FCH_USB3_CONTROLLER_NUM > 1
    FchKLXhciSmuUsbConfigUpdate (DieBusNum, FCH_KL_USB_GUCTL, BIT17, 0, 0x01100003);
#endif
    IDS_HDT_CONSOLE (FCH_TRACE, "[FCH]FchKLXhciSparseMode to Disabled\n");
  }
}

VOID
FchKLXhciOCPolarity (
  IN  UINT32   DieBusNum,
  IN  VOID     *FchDataPtr
  )
{
  FCH_RESET_DATA_BLOCK        *LocalCfgPtr;
  LocalCfgPtr = (FCH_RESET_DATA_BLOCK *)FchDataPtr;

  if (LocalCfgPtr->XhciOcPolarityCfgLow) {
    FchKLXhciSmuUsbConfigUpdate (DieBusNum, FCH_KL_USB_PORT_CONTROL, BIT8, BIT8, 0x01000001);
#if FCH_USB3_CONTROLLER_NUM > 1
    FchKLXhciSmuUsbConfigUpdate (DieBusNum, FCH_KL_USB_PORT_CONTROL, BIT8, BIT8, 0x01100001);
#endif
    IDS_HDT_CONSOLE (FCH_TRACE, "[FCH]FchKLXhciOCPolarity set Polarity to Low\n");
  } else {
    FchKLXhciSmuUsbConfigUpdate (DieBusNum, FCH_KL_USB_PORT_CONTROL, BIT8, 0, 0x01000001);
#if FCH_USB3_CONTROLLER_NUM > 1
    FchKLXhciSmuUsbConfigUpdate (DieBusNum, FCH_KL_USB_PORT_CONTROL, BIT8, 0, 0x01100001);
#endif
    IDS_HDT_CONSOLE (FCH_TRACE, "[FCH]FchKLXhciOCPolarity set Polarity to High\n");
  }
}

/**
 * FchKLXhciDeviceRemovable  -  Xhci Device Removable Control
 *
 *
 *
 */
VOID
FchKLXhciDeviceRemovable (
  IN  UINT32   DieBusNum,
  IN  VOID     *FchDataPtr
  )
{
  UINT32                      DeviceRemovable;
  FCH_RESET_DATA_BLOCK        *LocalCfgPtr;
  LocalCfgPtr = (FCH_RESET_DATA_BLOCK *)FchDataPtr;

  // Port0-1 : Controller0
  DeviceRemovable = LocalCfgPtr->Xhci0DevRemovable;
  DeviceRemovable = DeviceRemovable & 0x00030003;
  FchKLXhciSmuUsbConfigUpdate (DieBusNum, FCH_KL_USB_DEVICE_REMOVABLE, 0x00030003, DeviceRemovable, 0x01000001);
  IDS_HDT_CONSOLE (
    FCH_TRACE,
    "[FCH]FchKLXhciDeviceRemovable for USB0 by UsbConfigUpdate Message with 0x16D80114, 0x00030003, %x, 0x01000001\n",
    DeviceRemovable
    );

#if FCH_USB3_CONTROLLER_NUM > 1
  // Port2-3 : Controller1
  DeviceRemovable = LocalCfgPtr->Xhci0DevRemovable;
  DeviceRemovable = DeviceRemovable >> 2;
  DeviceRemovable = DeviceRemovable & 0x00030003;
  FchKLXhciSmuUsbConfigUpdate (DieBusNum, FCH_KL_USB_DEVICE_REMOVABLE, 0x00030003, DeviceRemovable, 0x01100001);
  IDS_HDT_CONSOLE (
    FCH_TRACE,
    "[FCH]FchKLXhciDeviceRemovable for USB1 by UsbConfigUpdate Message with 0x16F80114, 0x00030003, %x, 0x01000001\n",
    DeviceRemovable
    );
#endif
}

/**
 * FchKLXhciRasFeature  -  Xhci RAS Control
 *
 *
 *
 */
VOID
FchKLXhciRasFeature (
  IN  UINT32   DieBusNum,
  IN  VOID     *FchDataPtr
  )
{
  // FCH_RESET_DATA_BLOCK        *LocalCfgPtr;
  // LocalCfgPtr = (FCH_RESET_DATA_BLOCK *)FchDataPtr;

//   if (LocalCfgPtr->XhciECCDedErrRptEn) {
//     // USB31::USBCONTAINERREGCNTR0::INTERRUPT_CONTROL_CNTR0:ECC_DedErrRptEn
//     FchKLXhciSmuUsbConfigUpdateReg (
//       DieBusNum,
//       FCH_KL_USB_INTERRUPT_CONTROL,
//       BIT12,
//       BIT12,
//       0,                              // Controller 0
//       USB_CONFIG_REGISTER_GROUP_1
//       );
// #if FCH_USB3_CONTROLLER_NUM > 1
//     FchKLXhciSmuUsbConfigUpdateReg (
//       DieBusNum,
//       FCH_KL_USB_INTERRUPT_CONTROL,
//       BIT12,
//       BIT12,
//       1,                              // Controller 1
//       USB_CONFIG_REGISTER_GROUP_1
//       );
// #endif
//     IDS_HDT_CONSOLE (FCH_TRACE, "[FCH]FchKLXhciRasFeature Enable ECC_DedErrRptEn\n");
//   } else {
//     FchKLXhciSmuUsbConfigUpdateReg (
//       DieBusNum,
//       FCH_KL_USB_INTERRUPT_CONTROL,
//       BIT12,
//       0,
//       0,                              // Controller 0
//       USB_CONFIG_REGISTER_GROUP_1
//       );
// #if FCH_USB3_CONTROLLER_NUM > 1
//     FchKLXhciSmuUsbConfigUpdateReg (
//       DieBusNum,
//       FCH_KL_USB_INTERRUPT_CONTROL,
//       BIT12,
//       0,
//       1,                              // Controller 1
//       USB_CONFIG_REGISTER_GROUP_1
//       );
// #endif
//     IDS_HDT_CONSOLE (FCH_TRACE, "[FCH]FchKLXhciRasFeature disable ECC_DedErrRptEn\n");
//   }
}

/**
 * FchKLUsbPortForceGen1  -  Port Force Gen1
 *
 *
 *
 */

VOID
FchKLUsbPortForceGen1 (
    IN  UINT32   DieBusNum,
    IN  VOID     *FchDataPtr
  )
{
  UINT8    UsbPortForceGen1;
  UINT32   DW0_Index;
  UINT32   DW1_Mask;
  UINT32   DW2_Data;
  UINT32   DW3_Op_Group;
  FCH_RESET_DATA_BLOCK        *LocalCfgPtr;

  LocalCfgPtr = (FCH_RESET_DATA_BLOCK *)FchDataPtr;
  UsbPortForceGen1  = LocalCfgPtr->Usb3PortForceGen1;
  IDS_HDT_CONSOLE (FCH_TRACE, "[FCH]FchKLUsbPortForceGen1 Started!\n");
  IDS_HDT_CONSOLE (FCH_TRACE, "[FCH]UsbPortForceGen1 Parameter =%x\n", UsbPortForceGen1);
  UsbPortForceGen1 &= 0x3;

  //Controller0 Port Control
  DW0_Index    = FCH_KL_USB_PORT_CONTROL;
  DW1_Mask     = 0x3 << 16;
  DW2_Data     = (UINT32) (UsbPortForceGen1 << 16);
  DW3_Op_Group = 0x01000001;
  FchKLXhciSmuUsbConfigUpdate (DieBusNum, DW0_Index, DW1_Mask, DW2_Data, DW3_Op_Group);
  IDS_HDT_CONSOLE (
    FCH_TRACE,
    "[FCH]UsbPortForceGen1 through UsbConfigUpdate Message with %x, %x, %x, %x\n",
    DW0_Index,
    DW1_Mask,
    DW2_Data,
    DW3_Op_Group
    );

#if FCH_USB3_CONTROLLER_NUM > 1
  //
  // Controller1
  //
  UsbPortForceGen1  = (LocalCfgPtr->Usb3PortForceGen1) >> 2;
  UsbPortForceGen1 &= 0x3;

  //Controller0 Port Control
  DW0_Index    = FCH_KL_USB_PORT_CONTROL;
  DW1_Mask     = 0x3 << 16;
  DW2_Data     = (UINT32) (UsbPortForceGen1 << 16);
  DW3_Op_Group = 0x01100001;
  FchKLXhciSmuUsbConfigUpdate (DieBusNum, DW0_Index, DW1_Mask, DW2_Data, DW3_Op_Group);
  IDS_HDT_CONSOLE (
    FCH_TRACE,
    "[FCH]UsbPortForceGen1 through UsbConfigUpdate with Message %x, %x, %x, %x\n",
    DW0_Index,
    DW1_Mask,
    DW2_Data,
    DW3_Op_Group
    );
#endif
}

/**
 * FchKLUsbOemUsb20PhyConfigure  -  USB 2.0 PHY Platform
 * Configurationb
 *
 *
 *
 */
VOID
FchKLUsbOemUsb20PhyConfigure (
  IN  UINT32   DieBusNum,
  IN  VOID     *PlatformConfigureTable
  )
{
  UINT32  Controller;
  UINT32  Port;
  UINT32  Op_Group;
  FCH_KL_USB_OEM_PLATFORM_TABLE *PtrTbl;

  UINT8 COMPDSTUNE;
  UINT8 SQRXTUNE;
  UINT8 TXFSLSTUNE;
  UINT8 TXPREEMPAMPTUNE;
  UINT8 TXPREEMPPULSETUNE;
  UINT8 TXRISETUNE;
  UINT8 TXVREFTUNE;
  UINT8 TXHSXVTUNE;
  UINT8 TXRESTUNE;

  Op_Group  = USB_CONFIG_REGISTER_GROUP_1;
  PtrTbl    = (FCH_KL_USB_OEM_PLATFORM_TABLE *)PlatformConfigureTable;

  IDS_HDT_CONSOLE (FCH_TRACE, "[FCH] %a Started\n", __FUNCTION__);

  if ( PtrTbl->Usb20PhyEnable == 0x00 ) {
    IDS_HDT_CONSOLE (FCH_TRACE, "[FCH] Usb2.0 PHY override is not enabled\n");
    IDS_HDT_CONSOLE (FCH_TRACE, "[FCH] %a Exit\n", __FUNCTION__);
    return;
  }

  for (Controller = 0; Controller < FCH_USB3_CONTROLLER_NUM; Controller++) {
    for (Port = 0; Port < FCH_USB3_PORT_MNU_PER_CONTROLLER; Port++) {

      IDS_HDT_CONSOLE (
        FCH_TRACE,
        "[FCH] Controller %d Port %d Array Idx %d\n",
        Controller,
        Port,
        Controller * FCH_USB3_CONTROLLER_NUM + Port
        );

      // Override USB31::USB20LANEPARAMCTLREGCNTR0PORT::USB_20LANEPARACTL0_CNTR0
      IDS_HDT_CONSOLE (FCH_TRACE, "[FCH] Override USB31::USB20LANEPARAMCTLREGCNTR0PORT::USB_20LANEPARACTL0_CNTR0\n");

      COMPDSTUNE        = PtrTbl->Usb20PhyPort[Controller * FCH_USB3_CONTROLLER_NUM + Port].COMPDSTUNE;
      SQRXTUNE          = PtrTbl->Usb20PhyPort[Controller * FCH_USB3_CONTROLLER_NUM + Port].SQRXTUNE;
      TXFSLSTUNE        = PtrTbl->Usb20PhyPort[Controller * FCH_USB3_CONTROLLER_NUM + Port].TXFSLSTUNE;
      TXPREEMPAMPTUNE   = PtrTbl->Usb20PhyPort[Controller * FCH_USB3_CONTROLLER_NUM + Port].TXPREEMPAMPTUNE;
      TXPREEMPPULSETUNE = PtrTbl->Usb20PhyPort[Controller * FCH_USB3_CONTROLLER_NUM + Port].TXPREEMPPULSETUNE;
      TXRISETUNE        = PtrTbl->Usb20PhyPort[Controller * FCH_USB3_CONTROLLER_NUM + Port].TXRISETUNE;
      TXVREFTUNE        = PtrTbl->Usb20PhyPort[Controller * FCH_USB3_CONTROLLER_NUM + Port].TXVREFTUNE;
      TXHSXVTUNE        = PtrTbl->Usb20PhyPort[Controller * FCH_USB3_CONTROLLER_NUM + Port].TXHSXVTUNE;
      TXRESTUNE         = PtrTbl->Usb20PhyPort[Controller * FCH_USB3_CONTROLLER_NUM + Port].TXRESTUNE;

      FchKLXhciSmuUsbConfigUpdateReg (
        DieBusNum,
        FCH_KL_USB_20LANEPARACTL0_CNTR0 + 0x400 * Port,
          ((COMPDSTUNE        == 0xFF) ? 0x0 : (0x07 <<  0)) \
        | ((SQRXTUNE          == 0xFF) ? 0x0 : (0x07 << 12)) \
        | ((TXFSLSTUNE        == 0xFF) ? 0x0 : (0x0F << 16)) \
        | ((TXPREEMPAMPTUNE   == 0xFF) ? 0x0 : (0x03 << 20)) \
        | ((TXPREEMPPULSETUNE == 0xFF) ? 0x0 : (0x01 << 23)) \
        | ((TXRISETUNE        == 0xFF) ? 0x0 : (0x03 << 24)) \
        | ((TXVREFTUNE        == 0xFF) ? 0x0 : (0x0F << 28)),
          ((COMPDSTUNE        == 0xFF) ? 0x0 : ((COMPDSTUNE        & 0x07) <<  0)) \
        | ((SQRXTUNE          == 0xFF) ? 0x0 : ((SQRXTUNE          & 0x07) << 12)) \
        | ((TXFSLSTUNE        == 0xFF) ? 0x0 : ((TXFSLSTUNE        & 0x0F) << 16)) \
        | ((TXPREEMPAMPTUNE   == 0xFF) ? 0x0 : ((TXPREEMPAMPTUNE   & 0x03) << 20)) \
        | ((TXPREEMPPULSETUNE == 0xFF) ? 0x0 : ((TXPREEMPPULSETUNE & 0x01) << 23)) \
        | ((TXRISETUNE        == 0xFF) ? 0x0 : ((TXRISETUNE        & 0x03) << 24)) \
        | ((TXVREFTUNE        == 0xFF) ? 0x0 : ((TXVREFTUNE        & 0x0F) << 28)),
        Controller,
        Op_Group
        );

      // Override USB31::USB20LANEPARAMCTLREGCNTR0PORT::USB_20LANEPARACTL1_CNTR0
      IDS_HDT_CONSOLE (FCH_TRACE, "[FCH] Override USB31::USB20LANEPARAMCTLREGCNTR0PORT::USB_20LANEPARACTL1_CNTR0\n");
      FchKLXhciSmuUsbConfigUpdateReg (
        DieBusNum,
        FCH_KL_USB_20LANEPARACTL0_CNTR0 + 0x400 * Port + 0x04,
          ((TXHSXVTUNE == 0xFF) ? 0x0 : (0x03 << 0))  \
        | ((TXRESTUNE  == 0xFF) ? 0x0 : (0x03 << 2)),
          ((TXHSXVTUNE == 0xFF) ? 0x0 : ((TXHSXVTUNE & 0x03) << 0))  \
        | ((TXRESTUNE  == 0xFF) ? 0x0 : ((TXRESTUNE  & 0x03) << 2)),
        Controller,
        Op_Group
        );
    }
  }

  IDS_HDT_CONSOLE (FCH_TRACE, "[FCH] %a Exit\n", __FUNCTION__);
}

/**
 * FchKLUsbOemUsb31PhyConfigure  -  USB 3.1 PHY Platform
 * Configuration
 *
 *
 *
 */
VOID
FchKLUsbOemUsb31PhyConfigure (
  IN  UINT32   DieBusNum,
  IN  VOID     *PlatformConfigureTable
  )
{
  UINT32  Controller;
  UINT32  Port;
  UINT32  Op_Group;
  FCH_KL_USB_OEM_PLATFORM_TABLE *PtrTbl;

  UINT8 RX_ANA_IQ_PHASE_ADJUST;
  UINT8 RX_EQ_DELTA_IQ_OVRD_EN;
  UINT8 RX_EQ_DELTA_IQ_OVRD_VAL;
  UINT8 RX_IQ_PHASE_ADJUST;
  UINT8 TX_VBOOST_LVL_EN;
  UINT8 TX_VBOOST_LVL;
  UINT8 RX_VREF_CTRL_EN;
  UINT8 RX_VREF_CTRL;
  // UINT8 TX_VBOOST_LVL_EN_X;
  // UINT8 TX_VBOOST_LVL_X;
  // UINT8 RX_VREF_CTRL_EN_X;
  // UINT8 RX_VREF_CTRL_X;

  Op_Group  = USB_CONFIG_REGISTER_GROUP_2;
  PtrTbl    = (FCH_KL_USB_OEM_PLATFORM_TABLE *)PlatformConfigureTable;

  IDS_HDT_CONSOLE (FCH_TRACE, "[FCH] %a Started\n", __FUNCTION__);

  if ( PtrTbl->Usb31PhyEnable == 0x00 ) {
    IDS_HDT_CONSOLE (FCH_TRACE, "[FCH] Usb3.1 PHY override is not enabled\n");
    IDS_HDT_CONSOLE (FCH_TRACE, "[FCH] %a Exit\n", __FUNCTION__);
    return;
  }

  for (Controller = 0; Controller < FCH_USB3_CONTROLLER_NUM; Controller++){
    for (Port = 0; Port < FCH_USB3_PORT_MNU_PER_CONTROLLER; Port++) {

      // Switch Port (PHY) first
      IDS_HDT_CONSOLE (
        FCH_TRACE,
        "[FCH] Switch to controller %d Port %d Array Idx %d\n",
        Controller,
        Port,
        Controller * FCH_USB3_CONTROLLER_NUM + Port
        );
      FchKLXhciSmuUsbConfigUpdateReg (
        DieBusNum,
        FCH_KL_USB_PORT_CONTROL,
        0x0000F000,
        Port << USB31_PORT_SEL_OFFSET,
        Controller,
        USB_CONFIG_REGISTER_GROUP_1
        );

      RX_ANA_IQ_PHASE_ADJUST  = PtrTbl->Usb31PhyPort[Controller * FCH_USB3_CONTROLLER_NUM + Port].RX_ANA_IQ_PHASE_ADJUST;
      RX_EQ_DELTA_IQ_OVRD_EN  = PtrTbl->Usb31PhyPort[Controller * FCH_USB3_CONTROLLER_NUM + Port].RX_EQ_DELTA_IQ_OVRD_EN;
      RX_EQ_DELTA_IQ_OVRD_VAL = PtrTbl->Usb31PhyPort[Controller * FCH_USB3_CONTROLLER_NUM + Port].RX_EQ_DELTA_IQ_OVRD_VAL;
      RX_IQ_PHASE_ADJUST      = PtrTbl->Usb31PhyPort[Controller * FCH_USB3_CONTROLLER_NUM + Port].RX_IQ_PHASE_ADJUST;
      TX_VBOOST_LVL_EN        = PtrTbl->Usb31PhyPort[Controller * FCH_USB3_CONTROLLER_NUM + Port].TX_VBOOST_LVL_EN;
      TX_VBOOST_LVL           = PtrTbl->Usb31PhyPort[Controller * FCH_USB3_CONTROLLER_NUM + Port].TX_VBOOST_LVL;
      RX_VREF_CTRL_EN         = PtrTbl->Usb31PhyPort[Controller * FCH_USB3_CONTROLLER_NUM + Port].RX_VREF_CTRL_EN;
      RX_VREF_CTRL            = PtrTbl->Usb31PhyPort[Controller * FCH_USB3_CONTROLLER_NUM + Port].RX_VREF_CTRL;
      // TX_VBOOST_LVL_EN_X      = PtrTbl->Usb31PhyPort[Controller * FCH_USB3_CONTROLLER_NUM + Port].TX_VBOOST_LVL_EN_X;
      // TX_VBOOST_LVL_X         = PtrTbl->Usb31PhyPort[Controller * FCH_USB3_CONTROLLER_NUM + Port].TX_VBOOST_LVL_X;
      // RX_VREF_CTRL_EN_X       = PtrTbl->Usb31PhyPort[Controller * FCH_USB3_CONTROLLER_NUM + Port].RX_VREF_CTRL_EN_X;
      // RX_VREF_CTRL_X          = PtrTbl->Usb31PhyPort[Controller * FCH_USB3_CONTROLLER_NUM + Port].RX_VREF_CTRL_X;

      // Override DWCUSB31SSPPHYPHYX1NS::LANE0_DIG_ANA_RX_ANA_IQ_PHASE_ADJUST:RX_ANA_IQ_PHASE_ADJUST (Bit[6:0])
      IDS_HDT_CONSOLE (FCH_TRACE, "[FCH] Override DWCUSB31SSPPHYPHYX1NS::LANE0_DIG_ANA_RX_ANA_IQ_PHASE_ADJUST:RX_ANA_IQ_PHASE_ADJUST\n");
      FchKLXhciSmuUsbConfigUpdateReg (
        DieBusNum,
        FCH_KL_USB0_LANE0_DIG_ANA_RX_ANA_IQ_PHASE_ADJUST,
        (RX_ANA_IQ_PHASE_ADJUST == 0xFF) ? 0x0 : 0x7F,                                      // 0x0000007F
        (RX_ANA_IQ_PHASE_ADJUST == 0xFF) ? 0x0 : RX_ANA_IQ_PHASE_ADJUST & 0x7F,
        Controller,
        Op_Group
        );

      // Override DWCUSB31SSPPHYPHYX1NS::RAWLANE0_DIG_PCS_XF_RX_EQ_DELTA_IQ_OVRD_IN
      IDS_HDT_CONSOLE (FCH_TRACE, "[FCH] Override DWCUSB31SSPPHYPHYX1NS::RAWLANE0_DIG_PCS_XF_RX_EQ_DELTA_IQ_OVRD_IN\n");
      FchKLXhciSmuUsbConfigUpdateReg (
        DieBusNum,
        FCH_KL_USB0_RAWLANE0_DIG_PCS_XF_RX_EQ_DELTA_IQ_OVRD_IN,
          ((RX_EQ_DELTA_IQ_OVRD_VAL == 0xFF) ? 0x0 : 0x0F)                                     \
        | ((RX_EQ_DELTA_IQ_OVRD_EN  == 0xFF) ? 0x0 : (0x01 << RX_EQ_DELTA_IQ_OVRD_EN_OFFSET)),
          ((RX_EQ_DELTA_IQ_OVRD_VAL == 0xFF) ? 0x0 : (RX_EQ_DELTA_IQ_OVRD_VAL & 0xF))                                  \
        | ((RX_EQ_DELTA_IQ_OVRD_EN  == 0xFF) ? 0x0 : ((RX_EQ_DELTA_IQ_OVRD_EN & 1) << RX_EQ_DELTA_IQ_OVRD_EN_OFFSET)),
        Controller,
        Op_Group
        );

      // Override DWCUSB31SSPPHYPHYX1NS::RAWAONLANE0_DIG_RX_IQ_PHASE_ADJUST
      IDS_HDT_CONSOLE (FCH_TRACE, "[FCH] Override DWCUSB31SSPPHYPHYX1NS::RAWAONLANE0_DIG_RX_IQ_PHASE_ADJUST\n");
      FchKLXhciSmuUsbConfigUpdateReg (
        DieBusNum,
        FCH_KL_USB0_RAWLANE0_DIG_AON_RX_IQ_PHASE_ADJUST,
        (RX_IQ_PHASE_ADJUST == 0xFF) ? 0x00 : 0x7F,                                         // 0x0000007F
        (RX_IQ_PHASE_ADJUST == 0xFF) ? 0x00 : RX_IQ_PHASE_ADJUST & 0x7F,
        Controller,
        Op_Group
        );

      // Override DWCUSB31SSPPHYPHYX1NS::SUP_DIG_LVL_OVRD_IN
      IDS_HDT_CONSOLE (FCH_TRACE, "[FCH] Override DWCUSB31SSPPHYPHYX1NS::SUP_DIG_LVL_OVRD_IN\n");
      FchKLXhciSmuUsbConfigUpdateReg (
        DieBusNum,
        FCH_KL_USB0_SUP_DIG_LVL_OVRD_IN,
          ((RX_VREF_CTRL     == 0xFF) ? 0x00 : (RX_VREF_CTRL_MASK     << RX_VREF_CTRL_OFFSET))       \
        | ((RX_VREF_CTRL_EN  == 0xFF) ? 0x00 : (RX_VREF_CTRL_EN_MASK  << RX_VREF_CTRL_EN_OFFSET))    \
        | ((TX_VBOOST_LVL    == 0xFF) ? 0x00 : (TX_VBOOST_LVL_MASK    << TX_VBOOST_LVL_OFFSET))      \
        | ((TX_VBOOST_LVL_EN == 0xFF) ? 0x00 : (TX_VBOOST_LVL_EN_MASK << TX_VBOOST_LVL_EN_OFFSET)),
          ((RX_VREF_CTRL     == 0xFF) ? 0x00 : ((RX_VREF_CTRL     & RX_VREF_CTRL_MASK)     << RX_VREF_CTRL_OFFSET))       \
        | ((RX_VREF_CTRL_EN  == 0xFF) ? 0x00 : ((RX_VREF_CTRL_EN  & RX_VREF_CTRL_EN_MASK)  << RX_VREF_CTRL_EN_OFFSET))    \
        | ((TX_VBOOST_LVL    == 0xFF) ? 0x00 : ((TX_VBOOST_LVL    & TX_VBOOST_LVL_MASK)    << TX_VBOOST_LVL_OFFSET))      \
        | ((TX_VBOOST_LVL_EN == 0xFF) ? 0x00 : ((TX_VBOOST_LVL_EN & TX_VBOOST_LVL_EN_MASK) << TX_VBOOST_LVL_EN_OFFSET)),
        Controller,
        Op_Group
        );

      /*
      SUPX_DIG_LVL_OVRD_IN seems to share register banck with SUP_DIG_LVL_OVRD_IN.
      Comment it out for short term solution.

      // Override DWCUSB31SSPPHYPHYX1NS::SUPX_DIG_LVL_OVRD_IN
      IDS_HDT_CONSOLE (FCH_TRACE, "[FCH] Override DWCUSB31SSPPHYPHYX1NS::SUPX_DIG_LVL_OVRD_IN\n");
      FchKLXhciSmuUsbConfigUpdateReg (
        DieBusNum,
        FCH_KL_USB0_SUPX_DIG_LVL_OVRD_IN,
          ((RX_VREF_CTRL_X     == 0xFF) ? 0x00 : (RX_VREF_CTRL_MASK     << RX_VREF_CTRL_OFFSET))       \
        | ((RX_VREF_CTRL_EN_X  == 0xFF) ? 0x00 : (RX_VREF_CTRL_EN_MASK  << RX_VREF_CTRL_EN_OFFSET))    \
        | ((TX_VBOOST_LVL_X    == 0xFF) ? 0x00 : (TX_VBOOST_LVL_MASK    << TX_VBOOST_LVL_OFFSET))      \
        | ((TX_VBOOST_LVL_EN_X == 0xFF) ? 0x00 : (TX_VBOOST_LVL_EN_MASK << TX_VBOOST_LVL_EN_OFFSET)),
          ((RX_VREF_CTRL_X     == 0xFF) ? 0x00 : ((RX_VREF_CTRL_X     & RX_VREF_CTRL_MASK)     << RX_VREF_CTRL_OFFSET))       \
        | ((RX_VREF_CTRL_EN_X  == 0xFF) ? 0x00 : ((RX_VREF_CTRL_EN_X  & RX_VREF_CTRL_EN_MASK)  << RX_VREF_CTRL_EN_OFFSET))    \
        | ((TX_VBOOST_LVL_X    == 0xFF) ? 0x00 : ((TX_VBOOST_LVL_X    & TX_VBOOST_LVL_MASK)    << TX_VBOOST_LVL_OFFSET))      \
        | ((TX_VBOOST_LVL_EN_X == 0xFF) ? 0x00 : ((TX_VBOOST_LVL_EN_X & TX_VBOOST_LVL_EN_MASK) << TX_VBOOST_LVL_EN_OFFSET)),
        Controller,
        Op_Group
        );
      */
    }
  }

  IDS_HDT_CONSOLE (FCH_TRACE, "[FCH] %a Exit\n", __FUNCTION__);
}


/**
 * FchKLXhciOemConfigure  -  Xhci Platform Configurationb
 *
 *
 *
 */
VOID
FchKLXhciOemConfigure (
  IN  UINT32   DieBusNum,
  IN  VOID     *PlatformConfigureTable
  )
{
  FchKLUsbOemUsb20PhyConfigure (DieBusNum, PlatformConfigureTable);
  FchKLUsbOemUsb31PhyConfigure (DieBusNum, PlatformConfigureTable);
}

/**
 * FchKLXhciPassParameter  -  Xhci Pass Parameters
 *
 *
 *
 */
VOID
FchKLXhciPassParameter (
  IN  UINT32   DieBusNum,
  IN  VOID     *FchDataPtr
  )
{
  FCH_RESET_DATA_BLOCK          *LocalCfgPtr;
  FCH_KL_USB_OEM_PLATFORM_TABLE *PlatformUsbConfigureTable;

  LocalCfgPtr = (FCH_RESET_DATA_BLOCK *)FchDataPtr;
  PlatformUsbConfigureTable = (FCH_KL_USB_OEM_PLATFORM_TABLE *)(LocalCfgPtr->OemUsbConfigurationTablePtr);

  IDS_HDT_CONSOLE (FCH_TRACE, "[FCH]FchKLXhciPassParameter - Entry\n");

  IDS_HDT_CONSOLE (FCH_TRACE, "[FCH]FchSNXhciPassParameter - FchSNXhciDbgSC\n");
  FchKLXhciDbgSC (DieBusNum, LocalCfgPtr);
  IDS_HDT_CONSOLE (FCH_TRACE, "[FCH]FchKLXhciPassParameter - FchKLXhciOverCurrent\n");
  FchKLXhciOverCurrent (DieBusNum, LocalCfgPtr);
  IDS_HDT_CONSOLE (FCH_TRACE, "[FCH]FchKLXhciPassParameter - FchKLXhciDeviceRemovable\n");
  FchKLXhciDeviceRemovable (DieBusNum, LocalCfgPtr);
  IDS_HDT_CONSOLE (FCH_TRACE, "[FCH]FchKLXhciPassParameter - FchKLXhciRasFeature\n");
  FchKLXhciRasFeature (DieBusNum, LocalCfgPtr);
  IDS_HDT_CONSOLE (FCH_TRACE, "[FCH]FchKLXhciPassParameter - FchKLXhciOCPolarity\n");
  FchKLXhciOCPolarity (DieBusNum, LocalCfgPtr);
  IDS_HDT_CONSOLE (FCH_TRACE, "[FCH]FchKLXhciPassParameter - FchKLXhciSparseMode\n");
  FchKLXhciSparseMode (DieBusNum, LocalCfgPtr);

  if ( PlatformUsbConfigureTable != NULL
    && PlatformUsbConfigureTable->Version_Major == FCH_USB_MAJOR_VERSION
    && PlatformUsbConfigureTable->Version_Minor == FCH_USB_MINOR_VERSION
    && PlatformUsbConfigureTable->TableLength   == sizeof (FCH_KL_USB_OEM_PLATFORM_TABLE) )  //Breithorn (Kunlun) USB D.13
  {
    IDS_HDT_CONSOLE (
      FCH_TRACE,
      "[FCH] Find %a (%X.%X) USB oem table\n",
      FCH_NAME,
      FCH_USB_MAJOR_VERSION,
      FCH_USB_MINOR_VERSION
      );
    FchKLXhciOemConfigure (DieBusNum, PlatformUsbConfigureTable);
  }

  IDS_HDT_CONSOLE (FCH_TRACE, "[FCH]FchKLXhciPassParameter - Force Gen1\n");

  FchKLUsbPortForceGen1 (DieBusNum, LocalCfgPtr);

  IDS_HDT_CONSOLE (FCH_TRACE, "[FCH]FchKLXhciPassParameter - Exit\n");
}


BOOLEAN
FchKLXhciCheckUsbPhySkip (
  IN  UINT32   DieBusNum,
  IN  VOID     *FchDataPtr
  )
{
  UINT32 Presil_Ctrl0;

  ///< C2PMSG_97  (SMN address 0x3810A84)
  ///< SkipAllUSBControllerAccess:1;         ///< When set FW will skip all USB Control init communication from the FW
  ///< SkipAllUSBPhyAccess:1;                ///< When set FW will skip all USB phy communication from the FW
  //FchSmnRead (DieBusNum, 0x3810A84, &Presil_Ctrl0, NULL);
  Presil_Ctrl0 = PcdGet32 (PcdAmdPreSilCtrl0);
  IDS_HDT_CONSOLE (FCH_TRACE, "[FCH] %a C2PMSG_97 = 0x%x\n", __FUNCTION__, Presil_Ctrl0);
  if (Presil_Ctrl0 & BIT10) {
    return TRUE;
  } else {
    return FALSE;
  }
}

BOOLEAN
FchKLXhciCheckUsbSkipFlag (
  IN  UINT32   DieBusNum,
  IN  VOID     *FchDataPtr
  )
{
  UINT32 Presil_Ctrl0;

  ///< C2PMSG_97  (SMN address 0x3810A84)
  ///< SkipAllUSBControllerAccess:1;          ///< When set FW will skip all USB Control init communication from the FW
  ///< SkipAllUSBPhyAccess:1;                 ///< When set FW will skip all USB phy communication from the FW
  //FchSmnRead (DieBusNum, 0x3810A84, &Presil_Ctrl0, NULL);
  Presil_Ctrl0 = PcdGet32 (PcdAmdPreSilCtrl0);
  IDS_HDT_CONSOLE (FCH_TRACE, "[FCH] %a C2PMSG_97 = 0x%x\n", __FUNCTION__, Presil_Ctrl0);
  if (Presil_Ctrl0 & BIT9) {
    return TRUE;
  } else {
    return FALSE;
  }
}


/**
 * FchKLXhciInitBootProgram - Config Xhci controller during
 * Power-On
 *
 *
 * @param[in] DieBusNum  IOCH bus number on current Die.
 * @param[in] FchDataPtr Fch configuration structure pointer.
 *
 */
VOID
FchKLXhciInitBootProgram (
  IN  UINT32   DieBusNum,
  IN  VOID     *FchDataPtr
  )
{
  IDS_HDT_CONSOLE (FCH_TRACE, "[FCH] %a on Bus %x - Entry\n", __FUNCTION__, DieBusNum);
  if (FchKLXhciCheckUsbSkipFlag (DieBusNum, FchDataPtr)) {
    IDS_HDT_CONSOLE (FCH_TRACE, "[FCH] %a SkipAllUSBControllerAccess...\n", __FUNCTION__);
  } else {
    FchKLXhciPassParameter (DieBusNum, FchDataPtr);
    if (FchKLXhciCheckUsbPhySkip (DieBusNum, FchDataPtr)) {
      IDS_HDT_CONSOLE (FCH_TRACE, "[FCH] %a SkipAllUSBPhyAccess...\n", __FUNCTION__);
    } else {
      FchKLXhciSubSequenceEnable (DieBusNum, FchDataPtr);
    }
    IDS_HDT_CONSOLE (FCH_TRACE, "[FCH] %a on Bus %x - call SMU service\n", __FUNCTION__, DieBusNum);
    FchKLXhciSmuServiceUsbInit (DieBusNum, FchDataPtr);
  }

  IDS_HDT_CONSOLE (FCH_TRACE, "[FCH] %a on Bus %x - Exit\n", __FUNCTION__, DieBusNum);
}

/**
 * FchKLXhciInitS3ExitProgram - Config Xhci controller during
 * S3 Exit
 *
 *
 * @param[in] DieBusNum  IOCH bus number on current Die.
 * @param[in] FchDataPtr Fch configuration structure pointer.
 *
 */
VOID
FchKLXhciInitS3ExitProgram (
  IN  UINT32   DieBusNum,
  IN  VOID     *FchDataPtr
  )
{
  IDS_HDT_CONSOLE (FCH_TRACE, "[FCH]FchKLXhciInitS3ExitProgram on Bus %x - Entry\n", DieBusNum);
  FchKLXhciPassParameter (DieBusNum, FchDataPtr);
  FchKLXhciSmuService (DieBusNum, BIOSSMC_MSG_UsbS3Exit);
  IDS_HDT_CONSOLE (FCH_TRACE, "[FCH]FchKLXhciInitS3ExitProgram on Bus %x - Exit\n", DieBusNum);
}

/**
 * FchKLXhciInitS3EntryProgram - Config Xhci controller before
 * entering S3
 *
 *
 * @param[in] DieBusNum  IOCH bus number on current Die.
 * @param[in] FchDataPtr Fch configuration structure pointer.
 *
 */
VOID
FchKLXhciInitS3EntryProgram (
  IN  UINT32   DieBusNum,
  IN  VOID     *FchDataPtr
  )
{
  IDS_HDT_CONSOLE (FCH_TRACE, "[FCH]FchKLXhciInitS3EntryProgram on Bus %x - Entry\n", DieBusNum);
  FchKLXhciSmuService (DieBusNum, BIOSSMC_MSG_UsbS3Entry);
  IDS_HDT_CONSOLE (FCH_TRACE, "[FCH]FchKLXhciInitS3EntryProgram on Bus %x - Exit\n", DieBusNum);
}


/*
Because of security consideration, x86 is forbidden to access nBIF straps.
Move code to ABL.
*/
#if 0
/**
 * FchKLXhciInitSsid - Update Xhci SSID
 *
 *
 * @param[in] DieBusNum  IOCH bus number on current Die.
 * @param[in] Ssid       The SSID value to be updated
 *
 */
VOID
FchKLXhciInitSsid (
  IN  UINT32   DieBusNum,
  IN  UINT32   Ssid
  )
{
  FchSmnRW (DieBusNum, FCH_KL_USB0_NBIF_STRAP3, 0x00, Ssid, NULL);

#if FCH_USB3_CONTROLLER_NUM > 1
  FchSmnRW (DieBusNum, FCH_KL_USB1_NBIF_STRAP3, 0x00, Ssid, NULL);
#endif
}
#endif


