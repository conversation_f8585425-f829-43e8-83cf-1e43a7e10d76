/*
****************************************************************************
*
 * Copyright (C) 2008-2024 Advanced Micro Devices, Inc. All rights reserved.
 *
*******************************************************************************
*/


#include <Filecode.h>
#include <Library/HobLib.h>
#include <Library/DebugLib.h>
#include <Library/AmdErrorLogLib.h>
#include <Protocol/AmdErrorLogProtocol.h>
#include <Protocol/AmdErrorLogServiceProtocol.h>
#include "AmdErrorLogDisplayBrhDxe.h"
#include <AmdRas.h>
#include <Library/IdsLib.h>
#include <Library/BaseMemoryLib.h>
#include <Library/UefiLib.h>
#include <Library/AmdSocBaseLib.h>
#include "Porting.h"
#include <Addendum/Apcb/Inc/BRH/APOB.h>

#define FILECODE ERRORLOG_AMDERRORLOGDISPLAYBRHDXE_AMDERRORLOGDISPLAYBRHDXE_FILECODE

extern  EFI_BOOT_SERVICES       *gBS;

//
// Driver Global Data
//
STATIC AMD_ERROR_BUFFER *ErrorLogBuffer;

typedef struct {
  UINT32    errorCode;
  CHAR16    *string;
} PMU_ERRORCODE_STRING;

// PMU string for Turin rev Ax/Bx
STATIC PMU_ERRORCODE_STRING PmuString [] = {
  // String file of Turin_PMU_904F_24_3_8
  // Following message from ddr5_rdimm_pmu_train.strings
  {0x000f0000, L"PMU: Error: start address of ACSM MPR read sequence must be aligned on even acsm addr position\n"},
  {0x00220001, L"PMU: Error: CS%d failed to find a DFIMRL setting that worked for all bytes during MaxRdLat training\n"},
  {0x00240001, L"PMU: Error: CS%d failed to find a DFIMRL setting that worked for channel A bytes during MaxRdLat training\n"},
  {0x00260001, L"PMU: Error: CS%d failed to find a DFIMRL setting that worked for channel B bytes during MaxRdLat training\n"},
  {0x00280000, L"PMU: Error: No passing DFIMRL value found for any chip select during MaxRdLat training\n"},
  {0x00290000, L"PMU: Error: No passing DFIMRL value found for any chip select for channel A during MaxRdLat training\n"},
  {0x002a0000, L"PMU: Error: No passing DFIMRL value found for any chip select for channel B during MaxRdLat training\n"},
  {0x00320003, L"PMU: Error: Dbyte %d lane %d txDqDly passing region is too small (width = %d)\n"},
  {0x003a0001, L"PMU: Error: Dbyte %d txDqDly DM training did not start inside the eye\n"},
  {0x003f0003, L"PMU: Error: Dbyte %d lane %d txDqDly DM passing region is too small (width = %d)\n"},
  {0x00420002, L"PMU: Error: Dbyte %d nibble %d found multiple working coarse delay setting for MRD/MWD\n"},
  {0x00460003, L"PMU: Error: Dbyte %d nibble %d MRD/MWD passing region is too small (width = %d)\n"},
  {0x004b0001, L"PMU: Error: MRD/MWD training is not converging on rank %d after trying all possible RCD CmdDly\n"},
  {0x00540003, L"PMU: Error: Dbyte %d nibble %d rxClkDly passing region is too small (width = %d)\n"},
  {0x005b0003, L"PMU: Error: D5 rd2D no passing region for rank %d, db %d, lane %d\n"},
  {0x005c0002, L"PMU: Error: Wrong PBDly seed 0x%04x results in too small RxClkDly 0x%04x\n"},
  {0x00660002, L"PMU: Error: tg %d nib %d RxClkDly had no passing region\n"},
  {0x006d0002, L"PMU: Error: db %d lane %d vrefDAC had no passing region\n"},
  {0x00810002, L"PMU: Error: dbyte %d lane %d TxDqDly had no passing region\n"},
  {0x008a0001, L"PMU: Error: nib %d vrefDQ had no passing region\n"},
  {0x00a80003, L"PMU: Error: Dbyte %d nibble %d MRD passing region is too small (width = %d)\n"},
  {0x00a90003, L"PMU: Error: Dbyte %d nibble %d MWD passing region is too small (width = %d)\n"},
  {0x00b40002, L"PMU: Error: dbyte %d lane %d's per-lane vrefDAC's had no passing region\n"},
  {0x00be0002, L"PMU: Error: dbyte %d lane %d failed read deskew\n"},
  {0x00ca0001, L"PMU: Error: Wrong PMU image loaded. message Block DramType = 0x%02x, but image built for D4U Type\n"},
  {0x00cb0001, L"PMU: Error: Wrong PMU image loaded. message Block DramType = 0x%02x, but image built for D4R Type\n"},
  {0x00cc0001, L"PMU: Error: Wrong PMU image loaded. message Block DramType = 0x%02x, but image built for D4LR Type\n"},
  {0x00cd0000, L"PMU: Error: Both 2t timing mode and ddr4 geardown mode specifed in the messageblock's PhyCfg and MR3 fields. Only one can be enabled\n"},
  {0x00d70000, L"PMU: Error: start address of ACSM RxEn sequence must be aligned on even acsm addr position\n"},
  {0x00db0001, L"PMU: Error: Dbyte %d couldn't find the rising edge of DQS during RxEn Training\n"},
  {0x00e20001, L"PMU: Error: Failed MRE for nib %d\n"},
  {0x00ec0002, L"PMU: Error: Failed MREP for nib %d with %d one\n"},
  {0x00f30000, L"PMU: ERROR: WARNING: CS train did not find a rising edge for all devices.\n"},
  {0x00fd0003, L"PMU: Error: CSn %d Channel %d CS train passing region is too small (width = %d)\n"},
  {0x01060004, L"PMU: Error: CSn %d Channel %d Signal A%d CA train passing region is too small (width = %d)\n"},
  {0x010d0003, L"PMU: Error: CSn %d Channel %d VrefCS train passing region is too small (width = %d)\n"},
  {0x01130004, L"PMU: Error: CSn %d Channel %d Signal CA%d VrefCA train passing region is too small (width = %d)\n"},
  {0x01160003, L"PMU: Error: RCD CA DFE training failed for CS 0x%x Channel %d CA%d (no open eye was found)\n"},
  {0x01180003, L"PMU: Error: RCD CA DFE training could not calculate trained VrefCA center for CS 0x%x Channel %d CA%d\n"},
  {0x01280003, L"PMU5: Error: CSn %d Channel %d RCD CS train passing region is too small (width = %d), assume 0 as center\n"},
  {0x012c0003, L"PMU: Error: CSn %d Channel %d RCD CS train passing region is too small (width = %d)\n"},
  {0x01300000, L"PMU: Error: QCS delays of subchannels are too far apart to converge\n"},
  {0x01330000, L"PMU: Error: start address of ACSM RCD_CA sequence must be aligned on even acsm addr position\n"},
  {0x01390002, L"PMU5: Error: CSn %d Channel %d RCD CA final delay too large, assume 0\n"},
  {0x01430002, L"PMU: Error: specified bank (BG:%d BA:%d) is not available for PPR\n"},
  {0x01470000, L"PMU: Error: Mismatched internal revision between DCCM and ICCM images\n"},
  {0x01730000, L"PMU: Error: EnabledDQsChA must be > 0\n"},
  {0x01740000, L"PMU: Error: EnabledDQsChB must be > 0\n"},
  {0x01820000, L"PMU: Error: No dbiDisable without d4\n"},
  {0x01920000, L"PMU: Error: getMaxRxen() failed to find largest rxen nibble delay\n"},
  {0x01e70000, L"PMU: Error: start address of ACSM WR/RD activate sequence must be aligned on even acsm addr position\n"},
  {0x01e90000, L"PMU: Error: start address of ACSM WR/RD program sequence must be aligned on even acsm addr position\n"},
  {0x01eb0000, L"PMU: Error: start address of ACSM DM sequence must be aligned on even acsm addr position\n"},
  {0x01ec0003, L"PMU: Error: Firmware was not able to detect swizzle setting for TG%d Dbyte%d DQ%d\n"},
  {0x01ed0005, L"PMU: Error: Wrong DqLnSelTg setting for TG%d Dbyte%d DQ%d: expected %d found %d\n"},
  {0x01ee0005, L"PMU: Error: Wrong DqLnSelTg setting for TG%d Dbyte%d: DQ%d and DQ%d have the same value (%d)\n"},
  {0x01ef0003, L"PMU: Error: Firmware was not able to detect swizzle setting for TG%d Dbyte%d DQ%d\n"},
  {0x01f90000, L"PMU: Error: internal error in d5_detect_dq_swizzle() cannot find unused mapping\n"},
  {0x02040000, L"PMU: Error: start address of ACSM WL sequence must be aligned on even acsm addr position\n"},
  {0x020e0001, L"PMU: Error: Failed DWL for nib %d\n"},
  {0x02110002, L"PMU: Error: nib %d external WL %d underflow\n"},
  {0x02130000, L"PMU: Error: internal DWL error ACSM sequences overlap\n"},
  {0x02170000, L"PMU: Error: Some nibble didn't converge during internal WL\n"},
  {0x02190002, L"PMU: Error: nib %d internal WL %d overflow\n"},
  {0x021d0002, L"PMU: Error: nib %d external WL %d underflow\n"},
  {0x02230000, L"PMU: Error: Some nibble didn't converge during internal WL\n"},
  {0x02260002, L"PMU: Error: nib %d internal WL %d overflow\n"},
  {0x022b0002, L"PMU: Error: nib %d external WL %d overflow\n"},
  {0x022c0002, L"PMU: Error: nib %d external WL %d underflow\n"},
  {0x022e0002, L"PMU: Error: nib %d external WL %d overflow\n"},
  {0x022f0002, L"PMU: Error: nib %d external WL %d underflow\n"},
  {0x02450000, L"PMU: Error: Failed write leveling coarse\n"},
  {0x024a0001, L"PMU: Error: All margin after write leveling coarse are smaller than minMargin %d\n"},
  {0x02520002, L"PMU: Error: Failed DWL for nib %d with %d one\n"},
  {0x04060001, L"PMU: Error: acsm_set_cmd to non existant instruction adddress %d\n"},
  {0x04070001, L"PMU: Error: acsm_set_cmd with unknown ddr cmd 0x%x\n"},
  {0x040b0000, L"PMU: Error: Polling on ACSM done failed to complete in acsm_poll_done()...\n"},
  {0x04100004, L"PMU: Error: setAcsmCLCWL: cl and cwl must be each >= %d, and %d, resp. CL=%d CWL=%d\n"},
  {0x04110002, L"PMU: Error: setAcsmCLCWL: cl and cwl must be each >= 5. CL=%d CWL=%d\n"},
  {0x04130001, L"PMU: Error: Reserved value of register F0RC0F found in message block: 0x%04x\n"},

  // Following message from ddr5_rdimm_pmu_amdposttrain.strings
  // Some message may duplicate to above but different error code encoding.
  {0x00020000, L"PMU: Error: start address of ACSM MPR read sequence must be aligned on even acsm addr position\n"},
  {0x00150001, L"PMU: Error: CS%d failed to find a DFIMRL setting that worked for all bytes during MaxRdLat training\n"},
  {0x00170001, L"PMU: Error: CS%d failed to find a DFIMRL setting that worked for channel A bytes during MaxRdLat training\n"},
  {0x00190001, L"PMU: Error: CS%d failed to find a DFIMRL setting that worked for channel B bytes during MaxRdLat training\n"},
  {0x001b0000, L"PMU: Error: No passing DFIMRL value found for any chip select during MaxRdLat training\n"},
  {0x001c0000, L"PMU: Error: No passing DFIMRL value found for any chip select for channel A during MaxRdLat training\n"},
  {0x001d0000, L"PMU: Error: No passing DFIMRL value found for any chip select for channel B during MaxRdLat training\n"},
  {0x00250003, L"PMU: Error: Dbyte %d lane %d txDqDly passing region is too small (width = %d)\n"},
  {0x002d0001, L"PMU: Error: Dbyte %d txDqDly DM training did not start inside the eye\n"},
  {0x00320003, L"PMU: Error: Dbyte %d lane %d txDqDly DM passing region is too small (width = %d)\n"},
  {0x00350002, L"PMU: Error: Dbyte %d nibble %d found multiple working coarse delay setting for MRD/MWD\n"},
  {0x00390003, L"PMU: Error: Dbyte %d nibble %d MRD/MWD passing region is too small (width = %d)\n"},
  {0x003e0001, L"PMU: Error: MRD/MWD training is not converging on rank %d after trying all possible RCD CmdDly\n"},
  {0x00470003, L"PMU: Error: Dbyte %d nibble %d rxClkDly passing region is too small (width = %d)\n"},
  {0x004e0003, L"PMU: Error: D5 rd2D no passing region for rank %d, db %d, lane %d\n"},
  {0x004f0002, L"PMU: Error: Wrong PBDly seed 0x%04x results in too small RxClkDly 0x%04x\n"},
  {0x00590002, L"PMU: Error: tg %d nib %d RxClkDly had no passing region\n"},
  {0x00600002, L"PMU: Error: db %d lane %d vrefDAC had no passing region\n"},
  {0x00740002, L"PMU: Error: dbyte %d lane %d TxDqDly had no passing region\n"},
  {0x007d0001, L"PMU: Error: nib %d vrefDQ had no passing region\n"},
  {0x009b0003, L"PMU: Error: Dbyte %d nibble %d MRD passing region is too small (width = %d)\n"},
  {0x009c0003, L"PMU: Error: Dbyte %d nibble %d MWD passing region is too small (width = %d)\n"},
  {0x00a70002, L"PMU: Error: dbyte %d lane %d's per-lane vrefDAC's had no passing region\n"},
  {0x00b10002, L"PMU: Error: dbyte %d lane %d failed read deskew\n"},
  {0x00c10000, L"PMU: Error: EnabledDQsChA must be > 0\n"},
  {0x00c20000, L"PMU: Error: EnabledDQsChB must be > 0\n"},
  {0x00d00000, L"PMU: Error: No dbiDisable without d4\n"},
  {0x00e00000, L"PMU: Error: getMaxRxen() failed to find largest rxen nibble delay\n"},
  {0x01350000, L"PMU: Error: start address of ACSM WR/RD activate sequence must be aligned on even acsm addr position\n"},
  {0x01370000, L"PMU: Error: start address of ACSM WR/RD program sequence must be aligned on even acsm addr position\n"},
  {0x01390000, L"PMU: Error: start address of ACSM DM sequence must be aligned on even acsm addr position\n"},
  {0x013a0003, L"PMU: Error: Firmware was not able to detect swizzle setting for TG%d Dbyte%d DQ%d\n"},
  {0x013b0005, L"PMU: Error: Wrong DqLnSelTg setting for TG%d Dbyte%d DQ%d: expected %d found %d\n"},
  {0x013c0005, L"PMU: Error: Wrong DqLnSelTg setting for TG%d Dbyte%d: DQ%d and DQ%d have the same value (%d)\n"},
  {0x013d0003, L"PMU: Error: Firmware was not able to detect swizzle setting for TG%d Dbyte%d DQ%d\n"},
  {0x01470000, L"PMU: Error: internal error in d5_detect_dq_swizzle() cannot find unused mapping\n"},
  {0x01520000, L"PMU: Error: start address of ACSM WL sequence must be aligned on even acsm addr position\n"},
  {0x015c0001, L"PMU: Error: Failed DWL for nib %d\n"},
  {0x015f0002, L"PMU: Error: nib %d external WL %d underflow\n"},
  {0x01610000, L"PMU: Error: internal DWL error ACSM sequences overlap\n"},
  {0x01650000, L"PMU: Error: Some nibble didn't converge during internal WL\n"},
  {0x01670002, L"PMU: Error: nib %d internal WL %d overflow\n"},
  {0x016b0002, L"PMU: Error: nib %d external WL %d underflow\n"},
  {0x01710000, L"PMU: Error: Some nibble didn't converge during internal WL\n"},
  {0x01740002, L"PMU: Error: nib %d internal WL %d overflow\n"},
  {0x01790002, L"PMU: Error: nib %d external WL %d overflow\n"},
  {0x017a0002, L"PMU: Error: nib %d external WL %d underflow\n"},
  {0x017c0002, L"PMU: Error: nib %d external WL %d overflow\n"},
  {0x017d0002, L"PMU: Error: nib %d external WL %d underflow\n"},
  {0x01930000, L"PMU: Error: Failed write leveling coarse\n"},
  {0x01980001, L"PMU: Error: All margin after write leveling coarse are smaller than minMargin %d\n"},
  {0x01a00002, L"PMU: Error: Failed DWL for nib %d with %d one\n"},
  {0x01bb0002, L"PMU: Error: db %d lane %d vrefDAC had no passing region\n"},
  {0x04060001, L"PMU: Error: acsm_set_cmd to non existant instruction adddress %d\n"},
  {0x04070001, L"PMU: Error: acsm_set_cmd with unknown ddr cmd 0x%x\n"},
  {0x040b0000, L"PMU: Error: Polling on ACSM done failed to complete in acsm_poll_done()...\n"},
  {0x04100004, L"PMU: Error: setAcsmCLCWL: cl and cwl must be each >= %d, and %d, resp. CL=%d CWL=%d\n"},
  {0x04110002, L"PMU: Error: setAcsmCLCWL: cl and cwl must be each >= 5. CL=%d CWL=%d\n"},
  {0x04130001, L"PMU: Error: Reserved value of register F0RC0F found in message block: 0x%04x\n"}
};

// PMU string for Turin rev C0 or newer
STATIC PMU_ERRORCODE_STRING PmuString_RevCx [] = {
  // String file of Turin_PMU_9C12_24_10_25
  // Following message from ddr5_rdimm_pmu_train.strings
  {0x000f0000, L"PMU: Error: start address of ACSM MPR read sequence must be aligned on even acsm addr position\n"},
  {0x00220001, L"PMU: Error: CS%d failed to find a DFIMRL setting that worked for all bytes during MaxRdLat training\n"},
  {0x00240001, L"PMU: Error: CS%d failed to find a DFIMRL setting that worked for channel A bytes during MaxRdLat training\n"},
  {0x00260001, L"PMU: Error: CS%d failed to find a DFIMRL setting that worked for channel B bytes during MaxRdLat training\n"},
  {0x00280000, L"PMU: Error: No passing DFIMRL value found for any chip select during MaxRdLat training\n"},
  {0x00290000, L"PMU: Error: No passing DFIMRL value found for any chip select for channel A during MaxRdLat training\n"},
  {0x002a0000, L"PMU: Error: No passing DFIMRL value found for any chip select for channel B during MaxRdLat training\n"},
  {0x00320003, L"PMU: Error: Dbyte %d lane %d txDqDly passing region is too small (width = %d)\n"},
  {0x00370001, L"PMU: Error: Dbyte %d txDqDly DM training did not start inside the eye\n"},
  {0x003c0003, L"PMU: Error: Dbyte %d lane %d txDqDly DM passing region is too small (width = %d)\n"},
  {0x003f0002, L"PMU: Error: Dbyte %d nibble %d found multiple working coarse delay setting for MRD/MWD\n"},
  {0x00430003, L"PMU: Error: Dbyte %d nibble %d MRD/MWD passing region is too small (width = %d)\n"},
  {0x00480001, L"PMU: Error: MRD/MWD training is not converging on rank %d after trying all possible RCD CmdDly\n"},
  {0x00510003, L"PMU: Error: Dbyte %d nibble %d rxClkDly passing region is too small (width = %d)\n"},
  {0x005a0003, L"PMU: Error: D5 rd2D no passing region for rank %d, db %d, lane %d\n"},
  {0x005b0002, L"PMU: Error: Wrong PBDly seed 0x%04x results in too small RxClkDly 0x%04x\n"},
  {0x00650002, L"PMU: Error: tg %d nib %d RxClkDly had no passing region\n"},
  {0x006c0002, L"PMU: Error: db %d lane %d vrefDAC had no passing region\n"},
  {0x00800002, L"PMU: Error: dbyte %d lane %d TxDqDly had no passing region\n"},
  {0x00880001, L"PMU: Error: nib %d vrefDQ had no passing region\n"},
  {0x00a60003, L"PMU: Error: Dbyte %d nibble %d MRD passing region is too small (width = %d)\n"},
  {0x00a70003, L"PMU: Error: Dbyte %d nibble %d MWD passing region is too small (width = %d)\n"},
  {0x00b20002, L"PMU: Error: dbyte %d lane %d's per-lane vrefDAC's had no passing region\n"},
  {0x00bc0002, L"PMU: Error: dbyte %d lane %d failed read deskew\n"},
  {0x00c80001, L"PMU: Error: Wrong PMU image loaded. message Block DramType = 0x%02x, but image built for D4U Type\n"},
  {0x00c90001, L"PMU: Error: Wrong PMU image loaded. message Block DramType = 0x%02x, but image built for D4R Type\n"},
  {0x00ca0001, L"PMU: Error: Wrong PMU image loaded. message Block DramType = 0x%02x, but image built for D4LR Type\n"},
  {0x00cb0000, L"PMU: Error: Both 2t timing mode and ddr4 geardown mode specifed in the messageblock's PhyCfg and MR3 fields. Only one can be enabled\n"},
  {0x00d50000, L"PMU: Error: start address of ACSM RxEn sequence must be aligned on even acsm addr position\n"},
  {0x00d90001, L"PMU: Error: Dbyte %d couldn't find the rising edge of DQS during RxEn Training\n"},
  {0x00e00001, L"PMU: Error: Failed MRE for nib %d\n"},
  {0x00ea0002, L"PMU: Error: Failed MREP for nib %d with %d one\n"},
  {0x00f10000, L"PMU: ERROR: WARNING: CS train did not find a rising edge for all devices.\n"},
  {0x00fb0003, L"PMU: Error: CSn %d Channel %d CS train passing region is too small (width = %d)\n"},
  {0x01040004, L"PMU: Error: CSn %d Channel %d Signal A%d CA train passing region is too small (width = %d)\n"},
  {0x010b0003, L"PMU: Error: CSn %d Channel %d VrefCS train passing region is too small (width = %d)\n"},
  {0x01110004, L"PMU: Error: CSn %d Channel %d Signal CA%d VrefCA train passing region is too small (width = %d)\n"},
  {0x01140003, L"PMU: Error: RCD CA DFE training failed for CS 0x%x Channel %d CA%d (no open eye was found)\n"},
  {0x01160003, L"PMU: Error: RCD CA DFE training could not calculate trained VrefCA center for CS 0x%x Channel %d CA%d\n"},
  {0x01230003, L"PMU5: Error: CSn %d Channel %d RCD CS train passing region is too small (width = %d), assume 0 as center\n"},
  {0x01270003, L"PMU: Error: CSn %d Channel %d RCD CS train passing region is too small (width = %d)\n"},
  {0x01280000, L"PMU: Error: neither CS0 nor CS2 present in RCD CS training\n"},
  {0x012c0003, L"PMU: Error: eye_center[%d][%d]=%d is not within rcw_0x12 +/-24\n"},
  {0x012f0000, L"PMU: Error: eye center average should never be less than -12\n"},
  {0x01300000, L"PMU: Error: eye center average should never be greater than 63\n"},
  {0x01320000, L"PMU: Error: start address of ACSM RCD_CA sequence must be aligned on even acsm addr position\n"},
  {0x01350002, L"PMU5: Error: CSn %d Channel %d RCD CA final delay too large, assume 0\n"},
  {0x013f0002, L"PMU: Error: specified bank (BG:%d BA:%d) is not available for PPR\n"},
  {0x01430000, L"PMU: Error: Mismatched internal revision between DCCM and ICCM images\n"},
  {0x01720000, L"PMU: Error: EnabledDQsChA must be > 0\n"},
  {0x01730000, L"PMU: Error: EnabledDQsChB must be > 0\n"},
  {0x01810000, L"PMU: Error: No dbiDisable without d4\n"},
  {0x01910000, L"PMU: Error: getMaxRxen() failed to find largest rxen nibble delay\n"},
  {0x01e60000, L"PMU: Error: start address of ACSM WR/RD activate sequence must be aligned on even acsm addr position\n"},
  {0x01e80000, L"PMU: Error: start address of ACSM WR/RD program sequence must be aligned on even acsm addr position\n"},
  {0x01ea0000, L"PMU: Error: start address of ACSM DM sequence must be aligned on even acsm addr position\n"},
  {0x01eb0003, L"PMU: Error: Firmware was not able to detect swizzle setting for TG%d Dbyte%d DQ%d\n"},
  {0x01ec0005, L"PMU: Error: Wrong DqLnSelTg setting for TG%d Dbyte%d DQ%d: expected %d found %d\n"},
  {0x01ed0005, L"PMU: Error: Wrong DqLnSelTg setting for TG%d Dbyte%d: DQ%d and DQ%d have the same value (%d)\n"},
  {0x01ee0003, L"PMU: Error: Firmware was not able to detect swizzle setting for TG%d Dbyte%d DQ%d\n"},
  {0x01f80000, L"PMU: Error: internal error in d5_detect_dq_swizzle() cannot find unused mapping\n"},
  {0x02000006, L"PMU: Error: Mismatch found csn %d, db %d, dbLane %d, errorCount %d, mr10Offset %d, wrBubble(memclk) %d.\n"},
  {0x02010004, L"PMU: Error: Mismatch found at end of training after MRL csn %d, db %d, dbLane %d, iter %d.\n"},
  {0x02050000, L"PMU: Error: start address of ACSM WL sequence must be aligned on even acsm addr position\n"},
  {0x02100001, L"PMU: Error: Failed DWL for nib %d\n"},
  {0x02130002, L"PMU: Error: nib %d external WL %d underflow\n"},
  {0x02150000, L"PMU: Error: internal DWL error ACSM sequences overlap\n"},
  {0x02190000, L"PMU: Error: Some nibble didn't converge during internal WL\n"},
  {0x021b0002, L"PMU: Error: nib %d internal WL %d overflow\n"},
  {0x021f0002, L"PMU: Error: nib %d external WL %d underflow\n"},
  {0x02250000, L"PMU: Error: Some nibble didn't converge during internal WL\n"},
  {0x02280002, L"PMU: Error: nib %d internal WL %d overflow\n"},
  {0x022d0002, L"PMU: Error: nib %d external WL %d overflow\n"},
  {0x022e0002, L"PMU: Error: nib %d external WL %d underflow\n"},
  {0x02300002, L"PMU: Error: nib %d external WL %d overflow\n"},
  {0x02310002, L"PMU: Error: nib %d external WL %d underflow\n"},
  {0x02470000, L"PMU: Error: Failed write leveling coarse\n"},
  {0x024c0001, L"PMU: Error: All margin after write leveling coarse are smaller than minMargin %d\n"},
  {0x02540002, L"PMU: Error: Failed DWL for nib %d with %d one\n"},
  {0x04060001, L"PMU: Error: acsm_set_cmd to non existant instruction adddress %d\n"},
  {0x04070001, L"PMU: Error: acsm_set_cmd with unknown ddr cmd 0x%x\n"},
  {0x040b0000, L"PMU: Error: Polling on ACSM done failed to complete in acsm_poll_done()...\n"},
  {0x04100004, L"PMU: Error: setAcsmCLCWL: cl and cwl must be each >= %d, and %d, resp. CL=%d CWL=%d\n"},
  {0x04110002, L"PMU: Error: setAcsmCLCWL: cl and cwl must be each >= 5. CL=%d CWL=%d\n"},
  {0x04130001, L"PMU: Error: Reserved value of register F0RC0F found in message block: 0x%04x\n"},

  // Following message from ddr5_rdimm_pmu_amdposttrain.strings
  // Some message may duplicate to above but different error code encoding.
  {0x00020000, L"PMU: Error: start address of ACSM MPR read sequence must be aligned on even acsm addr position\n"},
  {0x00150001, L"PMU: Error: CS%d failed to find a DFIMRL setting that worked for all bytes during MaxRdLat training\n"},
  {0x00170001, L"PMU: Error: CS%d failed to find a DFIMRL setting that worked for channel A bytes during MaxRdLat training\n"},
  {0x00190001, L"PMU: Error: CS%d failed to find a DFIMRL setting that worked for channel B bytes during MaxRdLat training\n"},
  {0x001b0000, L"PMU: Error: No passing DFIMRL value found for any chip select during MaxRdLat training\n"},
  {0x001c0000, L"PMU: Error: No passing DFIMRL value found for any chip select for channel A during MaxRdLat training\n"},
  {0x001d0000, L"PMU: Error: No passing DFIMRL value found for any chip select for channel B during MaxRdLat training\n"},
  {0x00250003, L"PMU: Error: Dbyte %d lane %d txDqDly passing region is too small (width = %d)\n"},
  {0x002a0001, L"PMU: Error: Dbyte %d txDqDly DM training did not start inside the eye\n"},
  {0x002f0003, L"PMU: Error: Dbyte %d lane %d txDqDly DM passing region is too small (width = %d)\n"},
  {0x00320002, L"PMU: Error: Dbyte %d nibble %d found multiple working coarse delay setting for MRD/MWD\n"},
  {0x00360003, L"PMU: Error: Dbyte %d nibble %d MRD/MWD passing region is too small (width = %d)\n"},
  {0x003b0001, L"PMU: Error: MRD/MWD training is not converging on rank %d after trying all possible RCD CmdDly\n"},
  {0x00440003, L"PMU: Error: Dbyte %d nibble %d rxClkDly passing region is too small (width = %d)\n"},
  {0x004d0003, L"PMU: Error: D5 rd2D no passing region for rank %d, db %d, lane %d\n"},
  {0x004e0002, L"PMU: Error: Wrong PBDly seed 0x%04x results in too small RxClkDly 0x%04x\n"},
  {0x00580002, L"PMU: Error: tg %d nib %d RxClkDly had no passing region\n"},
  {0x005f0002, L"PMU: Error: db %d lane %d vrefDAC had no passing region\n"},
  {0x00730002, L"PMU: Error: dbyte %d lane %d TxDqDly had no passing region\n"},
  {0x007b0001, L"PMU: Error: nib %d vrefDQ had no passing region\n"},
  {0x00990003, L"PMU: Error: Dbyte %d nibble %d MRD passing region is too small (width = %d)\n"},
  {0x009a0003, L"PMU: Error: Dbyte %d nibble %d MWD passing region is too small (width = %d)\n"},
  {0x00a50002, L"PMU: Error: dbyte %d lane %d's per-lane vrefDAC's had no passing region\n"},
  {0x00af0002, L"PMU: Error: dbyte %d lane %d failed read deskew\n"},
  {0x00c10000, L"PMU: Error: EnabledDQsChA must be > 0\n"},
  {0x00c20000, L"PMU: Error: EnabledDQsChB must be > 0\n"},
  {0x00d00000, L"PMU: Error: No dbiDisable without d4\n"},
  {0x00e00000, L"PMU: Error: getMaxRxen() failed to find largest rxen nibble delay\n"},
  {0x01350000, L"PMU: Error: start address of ACSM WR/RD activate sequence must be aligned on even acsm addr position\n"},
  {0x01370000, L"PMU: Error: start address of ACSM WR/RD program sequence must be aligned on even acsm addr position\n"},
  {0x01390000, L"PMU: Error: start address of ACSM DM sequence must be aligned on even acsm addr position\n"},
  {0x013a0003, L"PMU: Error: Firmware was not able to detect swizzle setting for TG%d Dbyte%d DQ%d\n"},
  {0x013b0005, L"PMU: Error: Wrong DqLnSelTg setting for TG%d Dbyte%d DQ%d: expected %d found %d\n"},
  {0x013c0005, L"PMU: Error: Wrong DqLnSelTg setting for TG%d Dbyte%d: DQ%d and DQ%d have the same value (%d)\n"},
  {0x013d0003, L"PMU: Error: Firmware was not able to detect swizzle setting for TG%d Dbyte%d DQ%d\n"},
  {0x01470000, L"PMU: Error: internal error in d5_detect_dq_swizzle() cannot find unused mapping\n"},
  {0x014f0006, L"PMU: Error: Mismatch found csn %d, db %d, dbLane %d, errorCount %d, mr10Offset %d, wrBubble(memclk) %d.\n"},
  {0x01500004, L"PMU: Error: Mismatch found at end of training after MRL csn %d, db %d, dbLane %d, iter %d.\n"},
  {0x01540000, L"PMU: Error: start address of ACSM WL sequence must be aligned on even acsm addr position\n"},
  {0x015f0001, L"PMU: Error: Failed DWL for nib %d\n"},
  {0x01620002, L"PMU: Error: nib %d external WL %d underflow\n"},
  {0x01640000, L"PMU: Error: internal DWL error ACSM sequences overlap\n"},
  {0x01680000, L"PMU: Error: Some nibble didn't converge during internal WL\n"},
  {0x016a0002, L"PMU: Error: nib %d internal WL %d overflow\n"},
  {0x016e0002, L"PMU: Error: nib %d external WL %d underflow\n"},
  {0x01740000, L"PMU: Error: Some nibble didn't converge during internal WL\n"},
  {0x01770002, L"PMU: Error: nib %d internal WL %d overflow\n"},
  {0x017c0002, L"PMU: Error: nib %d external WL %d overflow\n"},
  {0x017d0002, L"PMU: Error: nib %d external WL %d underflow\n"},
  {0x017f0002, L"PMU: Error: nib %d external WL %d overflow\n"},
  {0x01800002, L"PMU: Error: nib %d external WL %d underflow\n"},
  {0x01960000, L"PMU: Error: Failed write leveling coarse\n"},
  {0x019b0001, L"PMU: Error: All margin after write leveling coarse are smaller than minMargin %d\n"},
  {0x01a30002, L"PMU: Error: Failed DWL for nib %d with %d one\n"},
  {0x01c10002, L"PMU: Error: db %d lane %d vrefDAC had no passing region\n"},
  {0x04060001, L"PMU: Error: acsm_set_cmd to non existant instruction adddress %d\n"},
  {0x04070001, L"PMU: Error: acsm_set_cmd with unknown ddr cmd 0x%x\n"},
  {0x040b0000, L"PMU: Error: Polling on ACSM done failed to complete in acsm_poll_done()...\n"},
  {0x04100004, L"PMU: Error: setAcsmCLCWL: cl and cwl must be each >= %d, and %d, resp. CL=%d CWL=%d\n"},
  {0x04110002, L"PMU: Error: setAcsmCLCWL: cl and cwl must be each >= 5. CL=%d CWL=%d\n"},
  {0x04130001, L"PMU: Error: Reserved value of register F0RC0F found in message block: 0x%04x\n"}
};

/*---------------------------------------------------------------------------------------*/
/**
 *
 * This function logs AGESA Errors into the Error log.
 *
 * It will put the information in a circular buffer consisting of 128 such log
 * entries. If the buffer gets full, then the next Error log entry will be written
 * over the oldest Error log entry.
 *
 * @param[in]   PeiServices, ClearBuffer
 * @param[out]  ErrorLogDataPtr
 *
 */
EFI_STATUS
EFIAPI
AmdAquireErrorLogWithFlagDxe (
  IN       DXE_AMD_ERROR_LOG_SERVICES_PROTOCOL   *This,
  OUT      ERROR_LOG_DATA_STRUCT *ErrorLogDataPtr,
  IN       BOOLEAN ClearBuffer
  )
{
  EFI_STATUS    Status = EFI_SUCCESS;

  AquireErrorLog (ErrorLogBuffer, ErrorLogDataPtr, ClearBuffer);
  //Reset Error Log buffer
  if (ClearBuffer) {
    ErrorLogBufferInit(ErrorLogBuffer);
  }

  return Status;
}

//                                   SoC channel: 0, 1, 2, 3, 4, 5, 6,  7,  8, 9, 10, 11
STATIC CONST UINT8  Channel2BoardChannelXlat[] = {2, 4, 5, 0, 1, 3, 8, 10, 11, 6,  7,  9};

VOID
DisplayMemPmuTrainingFailure (
  )
{
  EFI_HOB_GUID_TYPE   *HobAddr;
  PMU_TRAINING_FAILURE_INFO_ENTRY   *PmuFailure;
  UINT16    stringIndex;
  UINT16    PmuStringLength;
  UINT8     BoardChannelId;
  UINT16    *errorCodeNotFound[] = {L"  --> ErrorCode not found in the message string table\n\n"};
  UINT16    i;
  PMU_ERRORCODE_STRING *TargetString;

  HobAddr = GetFirstGuidHob (&gAmdMemPmuTrainingFailureHobGuid);

  PmuFailure = (PMU_TRAINING_FAILURE_INFO_ENTRY *) GET_GUID_HOB_DATA (HobAddr);
  IDS_HDT_CONSOLE (MAIN_FLOW, "\nPMU Training Failure HOB address = %x\n", PmuFailure);
  for (i = 0; i < 80; i++) {  // PMU_TRAINING_FAILURE_INFO_ENTRY  FailureEntry[40], 40 entries per socket, 80 entries for two sockets
    if (0 == PmuFailure->ErrorCode) {  // ErrorCode == 0 indicating end of failure entry
      break;
    }

    BoardChannelId = Channel2BoardChannelXlat[PmuFailure->UmcId];
    Print (L"\n");
    Print (L"Socket %d, Board Channel %d (SoC Channel %d), ", PmuFailure->SocketId, BoardChannelId, PmuFailure->UmcId);
    Print (L"ErrorCode = %x, %x, %x, %x, %x\n", PmuFailure->ErrorCode, PmuFailure->Data[0], PmuFailure->Data[1], PmuFailure->Data[2], PmuFailure->Data[3]);

    if (IS_SOC_BRH_Cx || IS_SOC_BRHD_Bx) {
      PmuStringLength = sizeof (PmuString_RevCx) / sizeof (PmuString_RevCx[0]);
      TargetString    = &PmuString_RevCx[0];
    } else {
      PmuStringLength = sizeof (PmuString) / sizeof (PmuString[0]);
      TargetString    = &PmuString[0];
    }
    for (stringIndex = 0; stringIndex < PmuStringLength; stringIndex++) {
      if (PmuFailure->ErrorCode == TargetString[stringIndex].errorCode) {
        break;
      }
    }
    if (stringIndex == PmuStringLength) {  // ErrorCode not found
      Print (errorCodeNotFound[0]);
    } else {
      Print (L"  --> ");
      Print (TargetString[stringIndex].string, PmuFailure->Data[0], PmuFailure->Data[1], PmuFailure->Data[2], PmuFailure->Data[3]);
      Print (L"\n");
    }

    PmuFailure++;
  }
}

/*---------------------------------------------------------------------------------------*/
/**
 *
 * This function aquires gAmdErrorLogServiceProtocolGuid and display any error of
 * ABL_MEM_PMU_TRAIN_ERROR & ABL_MEM_AGESA_MEMORY_TEST_ERROR to serial console & video
 * screen
 *
 */
VOID
DisplayAmdErrorLogCallBackBrh (
  IN EFI_EVENT  Event,
  IN VOID       *Context
  )
{
  DXE_AMD_ERROR_LOG_SERVICES_PROTOCOL   *ErrorLogServices;
  ERROR_LOG_DATA_STRUCT   *ErrorLogDataPtr;
  UINT32                  i;
  UINT32                  Ccd;
  EFI_STATUS              Status;
  BOOLEAN                 anyError;
  UINT8                   SocketId;
  UINT8                   SocChannelId;
  UINT8                   BoardChannelId;
  EFI_HOB_GUID_TYPE       *HobAddr;
  PMU_TRAINING_FAILURE_INFO_ENTRY   *PmuFailure;
  UINT16                  stageCompletionCode;
  UINT16                  RrwErrorAtChannel;
  BOOLEAN                 HaltOnErr;
  SELF_HEALING_BIST_ERROR_LOG_STRUCT  SelfHealingBistErrorLog;

  Status = gBS->LocateProtocol(&gAmdErrorLogServiceProtocolGuid, NULL, (VOID **)&ErrorLogServices);
  if (EFI_ERROR (Status)) {
    IDS_HDT_CONSOLE_PSP_TRACE ("Locate gAmdErrorLogServiceProtocolGuid fail at DisplayAmdErrorLogCallBackBrh\n");
    return;
  }
  if (!FeaturePcdGet (PcdAmdIdsDebugPrintEnable) && !FeaturePcdGet (PcdAgesaPrintEventLogToConsole)) {
    return;
  }

  Status = gBS->AllocatePool (EfiBootServicesData, sizeof (ERROR_LOG_DATA_STRUCT), (VOID **)&ErrorLogDataPtr);
  if (EFI_ERROR (Status)) {
    return;
  }
  ZeroMem (ErrorLogDataPtr, sizeof (ERROR_LOG_DATA_STRUCT));

  ErrorLogServices->AmdAquireErrorLogWithFlagDxe (ErrorLogServices, ErrorLogDataPtr, FALSE);

  DisplayMemPmuTrainingFailure ();  // For Milan only
  //DisplayMemMbistDataEyeMargin ();

  // ABL_MEM_PMU_TRAIN_ERROR
  anyError = FALSE;
  for (i = 0; i < ErrorLogDataPtr->Count; i++) {
    if (ABL_MEM_PMU_TRAIN_ERROR == ErrorLogDataPtr->ErrorLog_Param[i].ErrorInfo) {
      anyError = TRUE;
      break;
    }
  }

  if (anyError) {
    Print (L"\n********************************************************************************\n");
    for (i = 0; i < ErrorLogDataPtr->Count; i++) {
      if (ABL_MEM_PMU_TRAIN_ERROR == ErrorLogDataPtr->ErrorLog_Param[i].ErrorInfo) {
        SocketId       = ErrorLogDataPtr->ErrorLog_Param[i].DataParam1 & 3;  // Isolate socket #
        SocChannelId   = (ErrorLogDataPtr->ErrorLog_Param[i].DataParam1 >> 8) & 0xf;
        BoardChannelId = Channel2BoardChannelXlat[SocChannelId];
        Print (L"* Memory PMU Training error at DIMM(s) in Socket %d, Board Channel %d (SoC Channel %d)\n", SocketId, BoardChannelId, SocChannelId);
      }
    }

    // Display Stage Completion info
    HobAddr = GetFirstGuidHob (&gAmdMemPmuTrainingFailureHobGuid);
    PmuFailure = (PMU_TRAINING_FAILURE_INFO_ENTRY *) GET_GUID_HOB_DATA (HobAddr);
    for (i = 0; i < 80; i++) {  // PMU_TRAINING_FAILURE_INFO_ENTRY  FailureEntry[40], 40 entries per socket, 80 entries for two sockets
      if (0 == PmuFailure->ErrorCode) {  // ErrorCode == 0 indicating end of failure entry
        break;
      }

      BoardChannelId = Channel2BoardChannelXlat[PmuFailure->UmcId];
      Print (L"\n");
      Print (L"Socket %d, Board Channel %d (SoC Channel %d), %dD training, following training stage completed\n",
             PmuFailure->SocketId, BoardChannelId, PmuFailure->UmcId, (PmuFailure->Train1d2d + 1));

      // Loop through all PmuFailure->stageCompletion, print its text message
      // Case 11 & 15 are useless, insert MJ_MSG_QCSQCA_TRAIN & MJ_MSG_MPR_RD_TRAIN in, must match to ABL Memory\Phy\Syn\DDR54\MemPmu.c MemPmuRunUntil
      for (stageCompletionCode = 0; stageCompletionCode < 16; stageCompletionCode ++) {
        if (((PmuFailure->stageCompletion >> stageCompletionCode) & 1) == 1) {
          switch (stageCompletionCode) {
            case 0:
              Print (L"\tEnd of initialization\n");
              break;
            case 1:
              Print (L"\tEnd of fine write leveling\n");
              break;
            case 2:
              Print (L"\tEnd of read enable training\n");
              break;
            case 3:
              Print (L"\tEnd of read delay center optimization\n");
              break;
            case 4:
              Print (L"\tEnd of write delay center optimization\n");
              break;
            case 5:
              Print (L"\tEnd of 2D read delay/voltage center optimization\n");
              break;
            case 6:
              Print (L"\tEnd of 2D write delay /voltage center optimization\n");
              break;
            case 7:
              Print (L"\tTraining has run successfully\n");
              break;
            case 9:
              Print (L"\tEnd of max read latency training\n");
              break;
            case 10:
              Print (L"\tEnd of read dq deskew training\n");
              break;
            case 11:  // Insert MJ_MSG_QCSQCA_TRAIN 0x1D here
              Print (L"\tEnd of RCD QCS/QCA training\n");
              break;
            case 12:
              Print (L"\tEnd of all DB triaining\n");
              break;
            case 13:
              Print (L"\tEnd of CA training\n");
              break;
            case 14:
              Print (L"\tEnd of Write leveling coarse delay\n");
              break;
            case 15:  // Insert MJ_MSG_MPR_RD_TRAIN 0xFD here
              Print (L"\tEnd of MPR read delay center optimization\n");
              break;
            default:
              Print (L"\tUnknown PMU Message\n");
              break;
          }
        }
      }

      PmuFailure++;
    }

    Print (L"********************************************************************************\n");
  }

  // ABL_MEM_RRW_ERROR
  anyError = FALSE;
  for (i = 0; i < ErrorLogDataPtr->Count; i++) {
    if (ABL_MEM_RRW_ERROR == ErrorLogDataPtr->ErrorLog_Param[i].ErrorInfo) {
      anyError = TRUE;
      break;
    }
  }

  if (anyError) {
    Print (L"\n******************************************************************************\n");
    for (i = 0; i < ErrorLogDataPtr->Count; i++) {
      if (ABL_MEM_RRW_ERROR == ErrorLogDataPtr->ErrorLog_Param[i].ErrorInfo) {
        SocketId          = ErrorLogDataPtr->ErrorLog_Param[i].DataParam1 & 3;  // Isolate socket #
        RrwErrorAtChannel = (ErrorLogDataPtr->ErrorLog_Param[i].DataParam1 >> 8) & 0xfff;  // Isolate channel, total 12 bits
        for (i = 0; i < 12; i++) {
          if (0 != ((RrwErrorAtChannel >> i) & 1)) {
            BoardChannelId = Channel2BoardChannelXlat[i];
            Print (L"* Memory RRW Test error at Socket %d, Board Channel %d (SoC Channel %d)\n", SocketId, BoardChannelId, i);
          }
        }
      }
    }
    Print (L"******************************************************************************\n");
  }

  // ABL_MEM_AGESA_MEMORY_TEST_ERROR
  anyError = FALSE;
  for (i = 0; i < ErrorLogDataPtr->Count; i++) {
    if (ABL_MEM_AGESA_MEMORY_TEST_ERROR == ErrorLogDataPtr->ErrorLog_Param[i].ErrorInfo) {
      anyError = TRUE;
      break;
    }
  }

  if (anyError) {
    Print (L"\n******************************************************************************\n");
    for (i = 0; i < ErrorLogDataPtr->Count; i++) {
      if (ABL_MEM_AGESA_MEMORY_TEST_ERROR == ErrorLogDataPtr->ErrorLog_Param[i].ErrorInfo) {
        SocketId       = ErrorLogDataPtr->ErrorLog_Param[i].DataParam1 & 3;  // Isolate socket #
        SocChannelId   = (ErrorLogDataPtr->ErrorLog_Param[i].DataParam1 >> 8) & 0xf;
        BoardChannelId = Channel2BoardChannelXlat[SocChannelId];
        Print (L"* Agesa Memory Test error at Socket %d, Board Channel %d (SoC Channel %d), ", SocketId, BoardChannelId, SocChannelId);

        switch ((ErrorLogDataPtr->ErrorLog_Param[i].DataParam1 >> 16) & 3) {    // Isolate DIMM #
        case 1:
          Print (L"DIMM 0\n");
          break;
        case 2:
          Print (L"DIMM 1\n");
          break;
        case 3:
          Print (L"DIMM 0 & DIMM 1\n");
        }
      }
    }
    Print (L"******************************************************************************\n");
  }

  // ABL_MEM_ERROR_MIXED_ECC_AND_NON_ECC_DIMM_IN_SYSTEM
  anyError = FALSE;
  for (i = 0; i < ErrorLogDataPtr->Count; i++) {
    if (ABL_MEM_ERROR_MIXED_ECC_AND_NON_ECC_DIMM_IN_SYSTEM  == ErrorLogDataPtr->ErrorLog_Param[i].ErrorInfo) {
      anyError = TRUE;
      break;
    }
  }

  if (anyError) {
    Print (L"\n***********************************************************************************\n");
    for (i = 0; i < ErrorLogDataPtr->Count; i++) {
      if (ABL_MEM_ERROR_MIXED_ECC_AND_NON_ECC_DIMM_IN_SYSTEM == ErrorLogDataPtr->ErrorLog_Param[i].ErrorInfo) {
        SocketId       = ErrorLogDataPtr->ErrorLog_Param[i].DataParam1 & 3;  // Isolate socket #
        SocChannelId   = (ErrorLogDataPtr->ErrorLog_Param[i].DataParam1 >> 8) & 0xf;
        BoardChannelId = Channel2BoardChannelXlat[SocChannelId];
        Print (L"* Mixed ECC and non-ECC DIMM in system at Socket %d, Board Channel %d (SoC Channel %d)\n", SocketId, BoardChannelId, SocChannelId);
      }
    }
    Print (L"***********************************************************************************\n");
  }

  // ABL_MEM_ERROR_LRDIMM_MIXMFG
  anyError = FALSE;
  for (i = 0; i < ErrorLogDataPtr->Count; i++) {
    if (ABL_MEM_ERROR_LRDIMM_MIXMFG  == ErrorLogDataPtr->ErrorLog_Param[i].ErrorInfo) {
      anyError = TRUE;
      break;
    }
  }

  if (anyError) {
    Print (L"\n**********************************************************************************************************************\n");
    for (i = 0; i < ErrorLogDataPtr->Count; i++) {
      if (ABL_MEM_ERROR_LRDIMM_MIXMFG == ErrorLogDataPtr->ErrorLog_Param[i].ErrorInfo) {
        SocketId       = ErrorLogDataPtr->ErrorLog_Param[i].DataParam1 & 3;  // Isolate socket #
        SocChannelId   = (ErrorLogDataPtr->ErrorLog_Param[i].DataParam1 >> 8) & 0xf;
        BoardChannelId = Channel2BoardChannelXlat[SocChannelId];
        Print (L"* Mix certain vendor LRDIMM with other vendor LRDIMM in the same channel, at Socket %d, Board Channel %d (SoC Channel %d)\n",
               SocketId, BoardChannelId, SocChannelId);
      }
    }
    Print (L"**********************************************************************************************************************\n");
  }

  // ABL_CCD_BIST_FAILURE
  anyError = FALSE;
  for (i = 0; i < ErrorLogDataPtr->Count; i++) {
    if (ABL_CCD_BIST_FAILURE == ErrorLogDataPtr->ErrorLog_Param[i].ErrorInfo) {
      anyError = TRUE;
      break;
    }
  }

  if (anyError) {
    Print (L"\n**********************************************************\n");
    for (i = 0; i < ErrorLogDataPtr->Count; i++) {
      if (ABL_CCD_BIST_FAILURE == ErrorLogDataPtr->ErrorLog_Param[i].ErrorInfo) {
        for(Ccd = 0; Ccd < MAX_CCDS_PER_IOD; Ccd++){
          if(ErrorLogDataPtr->ErrorLog_Param[i].DataParam1 & (1 << Ccd)){
            Print (L"* CCD BIST error at Socket %d Die %d CCD %d\n", (ErrorLogDataPtr->ErrorLog_Param[i].DataParam1 >> 24) & 0xFF, (ErrorLogDataPtr->ErrorLog_Param[i].DataParam1 >> 16) & 0xFF, Ccd);
          }
        }
      }
    }
    Print (L"**********************************************************\n");
  }

  // ABL_MEM_MEMORY_HEALING_BIST_ERROR
  anyError = FALSE;
  for (i = 0; i < ErrorLogDataPtr->Count; i++) {
    if (ABL_MEM_MEMORY_HEALING_BIST_ERROR == ErrorLogDataPtr->ErrorLog_Param[i].ErrorInfo) {
      anyError = TRUE;
      break;
    }
  }

  if (anyError) {
    Print (L"\n******************************************************************************\n");
    for (i = 0; i < ErrorLogDataPtr->Count; i++) {
      if (ABL_MEM_MEMORY_HEALING_BIST_ERROR == ErrorLogDataPtr->ErrorLog_Param[i].ErrorInfo) {
        SocketId       = ErrorLogDataPtr->ErrorLog_Param[i].DataParam1 & 3;  // Isolate socket #
        SocChannelId   = (ErrorLogDataPtr->ErrorLog_Param[i].DataParam1 >> 8) & 0xf;
        BoardChannelId = Channel2BoardChannelXlat[SocChannelId];
        Print (L"* Memory Healing BIST error at Socket %d, Board Channel %d (SoC Channel %d), ", SocketId, BoardChannelId, SocChannelId);

        switch ((ErrorLogDataPtr->ErrorLog_Param[i].DataParam1 >> 16) & 3) {    // Isolate DIMM #
        case 1:
          Print (L"DIMM 0\n");
          break;
        case 2:
          Print (L"DIMM 1\n");
          break;
        case 3:
          Print (L"DIMM 0 & DIMM 1\n");
        }
      }
    }
    Print (L"******************************************************************************\n");
  }

  // ABL_MEM_CHANNEL_POPULATION_ORDER
  anyError = FALSE;
  HaltOnErr = FALSE;
  for (i = 0; i < ErrorLogDataPtr->Count; i++) {
    if (ABL_MEM_CHANNEL_POPULATION_ORDER  == ErrorLogDataPtr->ErrorLog_Param[i].ErrorInfo) {
      anyError = TRUE;
      break;
    }
  }

  if (anyError) {
    Print (L"\n***********************************************************************************\n");
    for (i = 0; i < ErrorLogDataPtr->Count; i++) {
      if (ABL_MEM_CHANNEL_POPULATION_ORDER == ErrorLogDataPtr->ErrorLog_Param[i].ErrorInfo) {
        SocketId       = ErrorLogDataPtr->ErrorLog_Param[i].DataParam1 & 3;  // Isolate socket #
        HaltOnErr      = (ErrorLogDataPtr->ErrorLog_Param[i].DataParam1 & BIT16) ? TRUE : FALSE;
        Print (L"* Memory channel configuration does NOT follow Memory Population Guidelines at Socket %d !!\n", SocketId);
      }
    }
    Print (L"***********************************************************************************\n");
    if (HaltOnErr) {
      Print (L"\n* System Halted!!\n");
      CpuDeadLoop ();
    }
  }

  // ABL_MEM_SELF_HEALING_BIST_ERROR
  anyError = FALSE;
  HaltOnErr = FALSE;
  for (i = 0; i < ErrorLogDataPtr->Count; i++) {
    if (ABL_MEM_SELF_HEALING_BIST_ERROR  == ErrorLogDataPtr->ErrorLog_Param[i].ErrorInfo) {
      anyError = TRUE;
      break;
    }
  }

  if (anyError) {
    Print (L"\n***********************************************************************************\n");
    for (i = 0; i < ErrorLogDataPtr->Count; i++) {
      if (ABL_MEM_SELF_HEALING_BIST_ERROR == ErrorLogDataPtr->ErrorLog_Param[i].ErrorInfo) {
        SelfHealingBistErrorLog.Value = ErrorLogDataPtr->ErrorLog_Param[i].DataParam1;
        SocChannelId   = (UINT8)SelfHealingBistErrorLog.Field.Channel;
        BoardChannelId = Channel2BoardChannelXlat[SocChannelId];
        Print (L"* Self-Healing Mem BIST error at Socket %d Board Channel %d (SoC Channel %d) ChipSelect %d ChipId %d SubChannel %d Package %d\n",
            SelfHealingBistErrorLog.Field.Socket, BoardChannelId, SocChannelId, SelfHealingBistErrorLog.Field.ChipSelect,
            SelfHealingBistErrorLog.Field.RankMultiplier, SelfHealingBistErrorLog.Field.SubChannel, SelfHealingBistErrorLog.Field.TargetDevice);
      }
    }
    Print (L"***********************************************************************************\n");
  }

  //Make sure the hook ONLY called one time.
  if (Event != NULL) {
    gBS->CloseEvent (Event);
  }
  Status = gBS->FreePool (ErrorLogDataPtr);
}

/*********************************************************************************
 * Name: AmdErrorLogDisplayBrhDxeInit
 *
 * Description
 *   Entry point of the AMD Error Log DXE driver
 *   Perform the configuration init, resource reservation, early post init
 *   and install all the supported protocol
 *
 * Input
 *   ImageHandle : EFI Image Handle for the DXE driver
 *   SystemTable : pointer to the EFI system table
 *
 * Output
 *   EFI_SUCCESS : Module initialized successfully
 *   EFI_ERROR   : Initialization failed (see error for more details)
 *
 *********************************************************************************/
EFI_STATUS
EFIAPI
AmdErrorLogDisplayBrhDxeInit (
  IN       EFI_HANDLE         ImageHandle,
  IN       EFI_SYSTEM_TABLE   *SystemTable
  )
{
  EFI_STATUS          Status = EFI_SUCCESS;
  //EFI_EVENT           DisplayAmdErrorLogEvent;
  //VOID                *Registration;

  DEBUG ((EFI_D_INFO, "*****************************DXE Error Log Display Driver Entry*********************\n"));

  DisplayAmdErrorLogCallBackBrh(NULL, NULL);
/*
  Status = gBS->CreateEventEx(
      EVT_NOTIFY_SIGNAL,
      TPL_NOTIFY,
      DisplayAmdErrorLogCallBackBrh,
      NULL,
      NULL,
      &DisplayAmdErrorLogEvent
      );

  Status = gBS->RegisterProtocolNotify (
      &gAmdNbioIommuProtocolGuid,
      DisplayAmdErrorLogEvent,
      &Registration
      );
*/
  DEBUG ((EFI_D_INFO, "*****************************DXE Error Log Display Driver Exit*********************\n"));
  return (Status);
}


