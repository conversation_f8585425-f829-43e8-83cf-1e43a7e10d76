/*****************************************************************************
 *
 * Copyright (C) 2018-2024 Advanced Micro Devices, Inc. All rights reserved.
 *
 *******************************************************************************
 */

/*----------------------------------------------------------------------------------------
 *                             M O D U L E S    U S E D
 *----------------------------------------------------------------------------------------
 */
#include <Uefi.h>
#include <Library/PciLib.h>
#include <Library/AmdPspMmioLib.h>
#include <Library/AmdPspRegMuxLibV2.h>
#include <Library/AmdPspRegBaseLib.h>
#include <Filecode.h>
/*----------------------------------------------------------------------------------------
 *                   D E F I N I T I O N S    A N D    M A C R O S
 *----------------------------------------------------------------------------------------
 */
#define FILECODE LIBRARY_AMDPSPREGMUXLIBV2DXE_AMDPSPREGMUXLIBV2_FILECODE

#define NB_SMN_INDEX_2_PCI_ADDR             (MAKE_SBDFO (0, 0, 0, 0, 0xB8))  ///< PCI Addr of NB_SMN_INDEX_2
#define NB_SMN_DATA_2_PCI_ADDR              (MAKE_SBDFO (0, 0, 0, 0, 0xBC))  ///< PCI Addr of NB_SMN_DATA_2
#define PSP_BAR_SIZE                        0x100000ul                       ///< Size of PSP BAR

BOOLEAN                     mRomArmor2Or3Enabled;
UINT32                      mPspC2pMsgRegBaseOffset = MP0_C2PMSG_BASE_OFFSET;
/*----------------------------------------------------------------------------------------
 *                  T Y P E D E F S     A N D     S T R U C T U  R E S
 *----------------------------------------------------------------------------------------
 */

/*----------------------------------------------------------------------------------------
 *           P R O T O T Y P E S     O F     L O C A L     F U N C T I O N S
 *----------------------------------------------------------------------------------------
 */

/**
  Return the PspMMIO MMIO location

  @param[in] PspMmioBase Pointer to Psp MMIO address

  @retval BOOLEAN  0: Error, 1 Success
**/
BOOLEAN
GetPspMmioBaseAddress (
  IN OUT   UINT32 *PspMmioBase
  )
{
  UINT32    Value32;
  UINTN     PciAddress;
  UINT32    SmnBase;

  *PspMmioBase = 0;

  GetPspIOHCxNbMiscSmnBase (&SmnBase, NULL);

  PciAddress = NB_SMN_INDEX_2_PCI_ADDR;
  Value32 = SmnBase + NBMSIC_PSP_BASE_ADDR_LO_OFFSET;
  PciWrite32 (PciAddress, Value32);
  PciAddress = NB_SMN_DATA_2_PCI_ADDR;
  Value32 = PciRead32 (PciAddress);
  //Mask out the lower bits
  Value32 &= 0xFFF00000;

  if (Value32 == 0) {
    return (FALSE);
  }

  *PspMmioBase = Value32;
  return (TRUE);
}

/**
  Library constructor for the AMD PSP Register Mutex Library V2 instance.

  @param ImageHandle The ImageHandle of the driver consuming the AmdPspRegMuxLibV2
  @param SystemTable Pointer to the EFI System Table

  @return EFI_SUCCESS The library constructor completed execution
**/
EFI_STATUS EFIAPI AmdPspRegMuxLibV2Constructor (
){
  UINT8 RomArmorSelection = PcdGet8 (PcdAmdPspRomArmorSelection);
  mRomArmor2Or3Enabled = (RomArmorSelection == 2 || RomArmorSelection == 3);
  mPspC2pMsgRegBaseOffset = GetPspC2pMsgRegBaseOffset ();
  return EFI_SUCCESS;
}

/*----------------------------------------------------------------------------------------
 *                          E X P O R T E D    F U N C T I O N S
 *----------------------------------------------------------------------------------------
 */

/**
 * Acquire the Mutex for access PSP,X86 co-accessed register
 * Call this routine before access certain registers, especially for SMI registers
 *
 */
VOID
AcquirePspAccRegMutex ()
{
  UINT32 PspMmioBase;

  //When RA2 enabled, PSP FW does not expect BIOS touch any PSP hardware mutex register
  if (mRomArmor2Or3Enabled) {
    return;
  }

  PspMmioBase = 0;
  if (GetPspMmioBaseAddress (&PspMmioBase)) {
    PspMmioWrite (mPspC2pMsgRegBaseOffset + BIOS_MUTEX_OFFSET, 1);
    PspMmioWrite (mPspC2pMsgRegBaseOffset + MUTEX_TURN_OFFSET, 1);
    //Wait till PSP FW release the mutex
    while ((PspMmioRead (mPspC2pMsgRegBaseOffset + PSP_MUTEX_OFFSET) == 1) &&
           (PspMmioRead (mPspC2pMsgRegBaseOffset + MUTEX_TURN_OFFSET) == 1)) {
      ;
    }
  }
}
/**
 * Release the Mutex for access PSP,X86 co-accessed register
 * Call this routine after access certain registers, especially for SMI registers
 *
 */
VOID
ReleasePspAccRegMutex ()
{
  UINT32 PspMmioBase;

  //When RA2 enabled, PSP FW does not expect BIOS touch any PSP hardware mutex register
  if (mRomArmor2Or3Enabled) {
    return;
  }

  PspMmioBase = 0;
  if (GetPspMmioBaseAddress (&PspMmioBase)) {
    PspMmioWrite (mPspC2pMsgRegBaseOffset + BIOS_MUTEX_OFFSET, 0);
  }
}

