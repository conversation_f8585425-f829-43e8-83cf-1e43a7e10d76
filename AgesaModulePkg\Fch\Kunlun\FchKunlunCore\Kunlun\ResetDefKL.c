/****************************************************************************
*
 * Copyright (C) 2008-2024 Advanced Micro Devices, Inc. All rights reserved.
 *
*******************************************************************************
*
*/

/*----------------------------------------------------------------------------------------
 *                             M O D U L E S    U S E D
 *----------------------------------------------------------------------------------------
 */
#include  "FchPlatform.h"
#include  "Filecode.h"
/*----------------------------------------------------------------------------------------
 *                   D E F I N I T I O N S    A N D    M A C R O S
 *----------------------------------------------------------------------------------------
 */



/*----------------------------------------------------------------------------------------
 * Default FCH interface settings at InitReset phase.
 *----------------------------------------------------------------------------------------
 */
CONST FCH_RESET_INTERFACE ROMDATA FchResetInterfaceDefault = {
  TRUE,                // UmiGen2
  TRUE,                // SataEnable
  TRUE,                // IdeEnable
  TRUE,                // GppEnable
  TRUE,                // Xhci0Enable
  TRUE                 // Xhci1Enable
};


/*----------------------------------------------------------------
 *  InitReset Phase Data Block Default (Failsafe)
 *----------------------------------------------------------------
 */
FCH_RESET_DATA_BLOCK   InitResetCfgDefault = {
  NULL,                  // StdHeader
  { TRUE,
    TRUE,
    FALSE,
    FALSE,
    TRUE,
    TRUE
    },                   // FchReset

  0,                     // FastSpeed
  0,                     // WriteSpeed
  0,                     // SpiTpmSpeed
  0,                     // Mode
  0,                     // AutoMode
  0,                     // BurstWrite
  FALSE,                 // SataIdeCombMdPriSecOpt
  0,                     // Cg2Pll
  FALSE,                 // EcKbd
  FALSE,                 // LegacyFree
  FALSE,                 // SataSetMaxGen2
  1,                     // SataClkMode
  0,                     // SataModeReg
  FALSE,                 // SataInternal100Spread
  FALSE,                 // SataActLWkaEnable
  2,                     // SpiSpeed
//  0xFCFCFCFC,                     // 38
//  0x88FC,                     // 3c
//  0,                     // 1d_34
  1,                     // 20_0
  FALSE,                 // EcChannel0

  {                      // FCH_GPP
    {                    // Array of FCH_GPP_PORT_CONFIG       PortCfg[4]
      {
        FALSE,           // PortPresent
        FALSE,           // PortDetected
        FALSE,           // PortIsGen2
        FALSE,           // PortHotPlug
        0,               // PortMisc
      },
      {
        FALSE,           // PortPresent
        FALSE,           // PortDetected
        FALSE,           // PortIsGen2
        FALSE,           // PortHotPlug
        0,               // PortMisc
      },
      {
        FALSE,           // PortPresent
        FALSE,           // PortDetected
        FALSE,           // PortIsGen2
        FALSE,           // PortHotPlug
        0,               // PortMisc
      },
      {
        FALSE,           // PortPresent
        FALSE,           // PortDetected
        FALSE,           // PortIsGen2
        FALSE,           // PortHotPlug
        0,               // PortMisc
      },
    },
    PortA1B1C1D1,        // GppLinkConfig
    FALSE,               // GppFunctionEnable
    FALSE,               // GppToggleReset
    0,                   // GppHotPlugGeventNum
    0,                   // GppFoundGfxDev
    FALSE,               // GppGen2
    0,                   // GppGen2Strap
    FALSE,               // GppMemWrImprove
    FALSE,               // GppUnhidePorts
    0,                   // GppPortAspm
    FALSE,               // GppLaneReversal
    FALSE,               // GppPhyPllPowerDown
    FALSE,               // GppDynamicPowerSaving
    FALSE,               // PcieAer
    FALSE,               // PcieRas
    FALSE,               // PcieCompliance
    FALSE,               // PcieSoftwareDownGrade
    FALSE,               // UmiPhyPllPowerDown
    FALSE,               // SerialDebugBusEnable
    0,                   // GppHardwareDownGrade
    0,                   // GppL1ImmediateAck
    FALSE,               // NewGppAlgorithm
    0,                   // HotPlugPortsStatus
    0,                   // FailPortsStatus
    40,                  // GppPortMinPollingTime
    FALSE,               // IsCapsuleMode
  },
  {                      // FCH_SPI
    TRUE,                // LpcEnable
    FALSE,               // LpcMsiEnable
    0x00000000,          // LpcSsid
    0,                   // RomBaseAddress
    0,                   // Speed
    0,                   // FastSpeed
    0,                   // WriteSpeed
    0,                   // Mode
    0,                   // AutoMode
    0,                   // BurstWrite
    TRUE,                // LpcClk0
    TRUE,                // LpcClk1
    0,                   // SPI100_Enable
    {0}                  // SpiDeviceProfile
  },
  FALSE,                 // QeEnabled
  FALSE,                 // FCH OSCOUT1_CLK Continous
  0,                     // LpcClockDriveStrength
  8,                     // LpcClockDriveStrengthRiseTime
  8,                     // LpcClockDriveStrengthFallTime
  0,                     // USB3 ECC SMI control
  0xFF,                  // USB3 Controller0 Port Num
  0xFF,                  // USB3 Controller1 Port Num
  0x01B3C953,            // USB3 LANEPARACTL0
  FALSE,                 // EspiEnable
  FALSE,                 // EspiIo80Enable
  FALSE,                 // EspiKbc6064Enable
  FALSE,                 // EspiEc0Enable
  FALSE,                 // Espi1Enable
  FALSE,                 // Espi1Io80Enable
  FALSE,                 // Espi1Kbc6064Enable
  FALSE,                 // Espi1Slave0Enable
  TRUE,                  // WdtEnable
//  NULL,                  // OemResetProgrammingTablePtr
  {
    {0,0,0,0,0,0},         // XHCI0 P0 Phy Parameters
    {0,0,0,0,0,0},         // XHCI0 P1 Phy Parameters
    {0,0,0,0,0,0},         // XHCI0 P2 Phy Parameters
    {0,0,0,0,0,0},         // XHCI0 P3 Phy Parameters
  },
  0,                     //  Xhci0DevRemovable
  0xFED80000,            //  FchAcpiMmioBase
  TRUE,                 //  XhciOcPolarityCfgLow
  FALSE,                 //  DisableXhciPortLate
  0x00,                  //  XhciUsb3PortDisable
  0x00,                  //  XhciUsb2PortDisable
  {                         //  XhciOCpinSelect
      {0xFFFFFFFF, 0xFFFF}, //  XhciOCpinSelect of XHCI controller 0
      {0xFFFFFFFF, 0xFFFF}, //  XhciOCpinSelect of XHCI controller 1
  },
  0x00,                  //  Usb3PortForceGen1
  {TRUE, TRUE, TRUE, TRUE},  //Sata controller enable.
  {                      // FCH_PT
    0x5F50545F,             // Signature of FCH_PT "_PT_"
    {1, 1, 0, 1, 1, 1, 0, 0, 0, 1},// FCH_PT_USB
    {0, 0, 0, 1, 1, 1, 1, 0, 0, 0},// FCH_PT_SATA
    {1, 1, 1, 1, 1, 1, 1, 1},// FCH_PT_SATA port
    {1, 1},// FCH_PT_PCIE
    {0xFF, 0x00, 0x00, 0x00,0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0x0000000000000000},// FCH_PT_ADDR
    {1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1},// FCH_PT_USBPort for PROM 3/4/5/6/7
    {1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1},      // FCH_PT_USBPort for PRO 460
    {1, 1, 1, 1, 1, 1, 1, 1, 1, 1},            // FCH_PT_USBPort for PROM 2/8/9
    {1, 1, 1, 1, 1, 1, 1, 1, 1},               // FCH_PT_USBPort for PROM 1
    {                      // PT USB TX
      {                     // Array of USB31[2] Tx setting
        {0x0F,0x0F,0x01,0x01,0x04,0x03,0x00,0x01,0x04,0x01},         // USB31PCS_B1 Tx setting
        {0x0F,0x0F,0x01,0x01,0x04,0x04,0x00,0x01,0x04,0x01},         // USB31PCS_B2 Tx setting
      },
      {                     // Array of USB30[3] Tx setting
        {0x0F,0x01,0x04},                                            // USB30PCS_P0 Tx setting
        {0x0F,0x01,0x08},                                            // USB30PCS_P1 Tx setting
        {0x0F,0x01,0x04},                                            // USB30PCS_P2 Tx setting
        {0x0F,0x01,0x08},                                            // USB30PCS_P3 Tx setting
        {0x0F,0x01,0x04},                                            // USB30PCS_P4 Tx setting
        {0x0F,0x01,0x08},                                            // USB30PCS_P5 Tx setting
      },
      5,                                                 // PT USB2.0 TX driving current, 7: largest By USB_HSDP/N[0]
      5,                                                 // PT USB2.0 TX driving current, 7: largest By USB_HSDP/N[5]
      5,                                                 // PT USB2.0 TX driving current, 7: largest By USB_HSDP/N[13][11]
      5,                                                 // PT USB2.0 TX driving current, 7: largest By USB_HSDP/N[12][10]
      5,                                                 // PT USB2.0 TX driving current, 7: largest By USB_HSDP/N[2][6]
      5,                                                 // PT USB2.0 TX driving current, 7: largest By USB_HSDP/N[3][7]
      5,                                                 // PT USB2.0 TX driving current, 7: largest By USB_HSDP/N[4][8]
      5,                                                 // PT USB2.0 TX driving current, 7: largest By USB_HSDP/N[1][9]
    },
    {                      // LP-PT USB TX
      {                     // Array of USB31[2] Tx setting
        {0x0F,0x0F,0x01,0x01,0x04,0x03,0x00,0x01,0x04,0x01},         // LP-PT USB31PCS_B1 Tx setting
        {0x0F,0x0F,0x01,0x01,0x04,0x03,0x00,0x01,0x04,0x01},         // LP-PT USB31PCS_B2 Tx setting
      },
      {                     // Array of USB30[3] Tx setting
        {0x0F,0x01,0x04},                                            // LP-PT USB30PCS_P0 Tx setting
        {0x0F,0x01,0x0C},                                            // LP-PT USB30PCS_P1 Tx setting
        {0x0F,0x01,0x04},                                            // LP-PT USB30PCS_P2 Tx setting
        {0x0F,0x01,0x0C},                                            // LP-PT USB30PCS_P3 Tx setting
        {0x0F,0x01,0x04},                                            // LP-PT USB30PCS_P4 Tx setting
        {0x0F,0x01,0x0C},                                            // LP-PT USB30PCS_P5 Tx setting
      },
      { // USB20Tx[8]
        // SlewRateDelay, DrivingCurrent, and DisconnectWindow
        {0x01, 0x07, 0x01}, // SlewRateDelay Port[0],      DrivingCurrent Port[0]      and DisconnectWindow Port[1]
        {0x01, 0x07, 0x01}, // SlewRateDelay Port[10][12], DrivingCurrent Port[10][12] and DisconnectWindow Port[2][8]
        {0x01, 0x07, 0x01}, // SlewRateDelay Port[2][6],   DrivingCurrent Port[2][6]   and DisconnectWindow Port[4][10]
        {0x01, 0x07, 0x01}, // SlewRateDelay Port[4][8],   DrivingCurrent Port[4][8]   and DisconnectWindow Port[6][12]
        {0x01, 0x07, 0x01}, // SlewRateDelay Port[5],      DrivingCurrent Port[5]      and DisconnectWindow Port[0]
        {0x01, 0x07, 0x01}, // SlewRateDelay Port[11][13], DrivingCurrent Port[11][13] and DisconnectWindow Port[3][9]
        {0x01, 0x07, 0x01}, // SlewRateDelay Port[3][7],   DrivingCurrent Port[3][7]   and DisconnectWindow Port[5][11]
        {0x01, 0x07, 0x01}, // SlewRateDelay Port[1][9],   DrivingCurrent Port[1][9]   and DisconnectWindow Port[7][13]
      },
    },
    {                      // PT SATA TX
      {0x00,0x04,0x08,0x00,0x01,0x01,0x00,0x02,0x03},             // SATA Port0 TX SETTINGS
      {0x00,0x04,0x08,0x00,0x01,0x01,0x00,0x02,0x03},             // SATA Port1 TX SETTINGS
      {0x00,0x04,0x08,0x00,0x01,0x01,0x00,0x02,0x03},             // SATA Port2 TX SETTINGS
      {0x00,0x04,0x08,0x00,0x01,0x01,0x00,0x02,0x03},             // SATA Port3 TX SETTINGS
      {0x00,0x04,0x08,0x00,0x01,0x01,0x00,0x02,0x03},             // SATA Port4 TX SETTINGS
      {0x00,0x04,0x08,0x00,0x01,0x01,0x00,0x02,0x03},             // SATA Port5 TX SETTINGS
      {0x00,0x04,0x08,0x00,0x01,0x01,0x00,0x02,0x03},             // SATA Port6 TX SETTINGS
      {0x00,0x04,0x08,0x00,0x01,0x01,0x00,0x02,0x03},             // SATA Port7 TX SETTINGS
    },
    {6, 6, 6, 6},// Fine Tune Up stream port lane de-emphasis
    {1, 1, 1, 1, 1, 1, 1, 1},// Enable/Disable PCIE Switch Downstream Ports
    {0, 0, 0, 0, 0, 0, 0, 0},// Enable/Disable LP PT GPP Clock Force On
    {0, 0x00, 0x00000000},   // data Structure to get PT root port
    {                        // PTSvidSsid
      {0x1B21,0x1142},                                            // XHCI_SVID XHCI_SSID
      {0x1B21,0x1062},                                            // AHCI_SVID AHCI_SSID
      {0x1B21,0x0201},                                            // PCIE_SVID PCIE_SSID
      {0x1B21,0x3306},                                            // PCIE_DSP0_SVID PCIE_DSP0_SSID
      {0x1B21,0x3306},                                            // PCIE_DSP1_SVID PCIE_DSP1_SSID
      {0x1B21,0x3306},                                            // PCIE_DSP2_SVID PCIE_DSP2_SSID
      {0x1B21,0x3306},                                            // PCIE_DSP3_SVID PCIE_DSP3_SSID
      {0x1B21,0x3306},                                            // PCIE_DSP4_SVID PCIE_DSP4_SSID
    },
  },
//  NULL,                  // EarlyOemGpioTable
  {0},                   // FchBldCfg
  NULL,                  // OemUsbConfigurationTablePtr
  TRUE,                  // SerialIrqEnable
  {0},                   // EMMC structure
  {0},                   // FchAsfCfg
  0,                     // BootTimerEnable
  0,                     // UsbSparseModeEnable
  0,                      // BootTimerResetType
  0x00000000,            //UINT32  I2CEnable;    ///I2C controller enable.
  {
    0x00000001,            //  UINT32   I2CSdaHold[6] ///I2C sda hold time
    0x00000001,            //  UINT32   I2CSdaHold[6] ///I2C sda hold time
    0x00000001,            //  UINT32   I2CSdaHold[6] ///I2C sda hold time
    0x00000001,            //  UINT32   I2CSdaHold[6] ///I2C sda hold time
    0x00000001,            //  UINT32   I2CSdaHold[6] ///I2C sda hold time
    0x00000001             //  UINT32   I2CSdaHold[6] ///I2C sda hold time
  },
  FALSE,                 // SataStaggeredSpinupEnable
  0x00,                  // FchIoApicId
  {0, 0, 0, 0},          // SataRxPolarity
  FALSE,                 // ToggleAllPwrGoodOnCf9
  FALSE,                 // UsbDbgSCPipeSwitchEnable
};





