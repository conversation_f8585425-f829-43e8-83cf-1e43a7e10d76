/*****************************************************************************
 *
 * Copyright (C) 2008-2024 Advanced Micro Devices, Inc. All rights reserved.
 *
 *******************************************************************************
 */
#include <Library/BaseLib.h>
#include <Library/PcdLib.h>
#include <Library/CcxBaseX86Lib.h>
#include <AGESA.h>
#include <Filecode.h>
#include <cpuRegisters.h>
#include <CcxRegistersZen5.h>
#include <CcxZen5AcpiServicesDxe.h>
#include <Library/AmdBaseLib.h>
#include <Library/AmdHeapLib.h>
#include <Library/CcxPstatesLib.h>
#include <Library/UefiBootServicesTableLib.h>
#include <Library/FabricRegisterAccLib.h>
#include <Library/CcxApicLib.h>
#include <Library/CcxCppcLib.h>
#include <Library/CcxCcdReorderLib.h>
#include <Library/AmlGenerationLib.h>
#include <Library/AcpiTableHelperLib.h>
#include <Protocol/MpService.h>
#include <Protocol/AmdAcpiCpuSsdtServicesProtocol.h>
#include <Protocol/AmdAcpiCratServicesProtocol.h>
#include <Protocol/AmdAcpiSratServicesProtocol.h>
#include <Protocol/AmdCoreTopologyV3Protocol.h>
#include <Protocol/FabricNumaServices2.h>
#include <Protocol/FabricTopologyServices2.h>
#include <Protocol/AmdNbioSmuServicesProtocol.h>
#include <Protocol/AmdNbioCppcServicesProtocol.h>
#include <Protocol/AmdNbioCoreRankingTableServicesProtocol.h>
#include <Protocol/AmdAcpiPcctServicesProtocol.h>
#include <Protocol/AmdAcpiCppcServicesProtocol.h>
#include <Protocol/FabricResourceManagerServicesProtocol.h>
#include <Protocol/AcpiSystemDescriptionTable.h>

#define FILECODE CCX_ZEN5_DXE_CCXZEN5ACPISERVICESDXE_FILECODE
STATIC GEN_COMM_SUBSPACE_STRUCT  ROMDATA GenCommSubspaceStruct =
{
  GEN_COMM_TYPE,        // Type
  GEN_COMM_LENGTH,      // Length
  {0},                  // Reserved
  0,                    // Base Address
  0,                    // Length
  {0},                  // Doorbell Register
  0,                    // Doorbell Preserve
  0,                    // Doorbell Write
  4,                    // Nominal Latency
  0,                    // Maximum Periodic Access Rate
  0                     // Minimum Request Turnaround Time
};

STATIC EXTEND_PCCT_SUBSPACES_STRUCT  ROMDATA ExtendPcctCommSubspaceTemplateStruct =
{
  SLAVE_SUBSPACE_TYPE,        // Type
  sizeof(EXTEND_PCCT_SUBSPACES_STRUCT), // Length
  0xFFFF,                     // PlatformInterrupt
  0x0,                        // PlatformInterruptFlags;
  {0},                        // Reserved
  0,                          // BaseAddress (To be filled by code)
  0,                          // MemoryLength
  {0},                        // DoorbellRegister (Unused)
  0,                          // DoorbellPreserve
  0,                          // DoorbellWrite
  4,                          // NominalLatency
  0,                          // MaxPeriodicAccessRate
  0,                          // MinReqTurnaroundTime
  {0} ,                       // PlatformInterruptAckRegister (Unused)
  0,                          // PlatformInterruptAckPreserve
  0,                          // PlatformInterruptAckSet
  {0},                        // Reserved
  {0},                        // CommandCompleteCheckRegisterAddress (Unused)
  0,                          // CommandCompleteCheckMask
  {0},                        // CommandCompleteUpdateRegisterAddress
  0,                          // CommandCompleteUpdatePreserveMask
  0,                          // CommandCompleteUpdateSetMask
  {0},                        // ErrorStatusRegister (Unused)
  0                           // ErrorStatusMask
};

/// TLB type
typedef enum {
  TLB_2M = 0,       ///< 0 - TLB 2M4M associativity
  TLB_4K,           ///< 1 - TLB 4K associativity
  TLB_1G,           ///< 2 - TLB 1G associativity
  TLB_TYPE_MAX,     ///< MaxValue
} TLB_TYPE;

#define SHARED_MAX_SIZE             ((ZEN5_MAX_NUMBER_OF_APS + 1) * 256)
#define  BITS_PER_BYTE     8
#define  KB_SHIFT         10
#define MAX_CCDS_PER_SKT  16

STATIC CPPC_ENTRIES_COMMON_TABLE  ROMDATA Zen5CppcRegisterDescriptorsMsr[] =
{
  //Highest Performance
  {CPC_BUFFER, {GENERIC_REG_SPACE_ID_FFH, 8, 24, GENERIC_REG_ADDR_SIZE_QWORD, 0xC00102B0}, NULL, SUPPORTED_BY_V2},
  //Nominal Performance
  {CPC_BUFFER, {GENERIC_REG_SPACE_ID_FFH, 8, 16, GENERIC_REG_ADDR_SIZE_QWORD, 0xC00102B0}, NULL, SUPPORTED_BY_V2},
  //Lowest Non-Linear Performance
  {CPC_BUFFER, {GENERIC_REG_SPACE_ID_FFH, 8,  8, GENERIC_REG_ADDR_SIZE_QWORD, 0xC00102B0}, NULL, SUPPORTED_BY_V2},
  //Lowest Performance
  {CPC_BUFFER, {GENERIC_REG_SPACE_ID_FFH, 8,  0, GENERIC_REG_ADDR_SIZE_QWORD, 0xC00102B0}, NULL, SUPPORTED_BY_V2},
  //Guaranteed Performance
  {CPC_BUFFER, {GENERIC_REG_SPACE_ID_FFH, 8,  8, GENERIC_REG_ADDR_SIZE_QWORD, 0xC00102B2}, NULL, SUPPORTED_BY_V2}, // Not implemented, the register filed pointed are all 0s.
  //Desired Performance
  {CPC_BUFFER, {GENERIC_REG_SPACE_ID_FFH, 8, 16, GENERIC_REG_ADDR_SIZE_QWORD, 0xC00102B3}, NULL, SUPPORTED_BY_V2},
  //Minimum Performance
  {CPC_BUFFER, {GENERIC_REG_SPACE_ID_FFH, 8, 0x08, GENERIC_REG_ADDR_SIZE_QWORD, 0xC00102B3}, NULL, SUPPORTED_BY_V2},
  //Maximum Performance
  {CPC_BUFFER, {GENERIC_REG_SPACE_ID_FFH, 8, 0x00, GENERIC_REG_ADDR_SIZE_QWORD, 0xC00102B3}, NULL, SUPPORTED_BY_V2},
  //Performance Reduction Tolerance
  {CPC_BUFFER, {GENERIC_REG_SPACE_ID_MEM, 0, 0, GENERIC_REG_ADDR_SIZE_UNDEFINED, 0}, NULL, SUPPORTED_BY_V2}, // Unsupported
  //Time Window
  {CPC_BUFFER, {GENERIC_REG_SPACE_ID_MEM, 0, 0, GENERIC_REG_ADDR_SIZE_UNDEFINED, 0}, NULL, SUPPORTED_BY_V2}, // Unsupported
  //Counter Wraparound Time
  {CPC_BUFFER, {GENERIC_REG_SPACE_ID_MEM, 0, 0, GENERIC_REG_ADDR_SIZE_UNDEFINED, 0}, NULL, SUPPORTED_BY_V2}, // Unsupported
  //Reference Performance Counter
  {CPC_BUFFER, {GENERIC_REG_SPACE_ID_FFH, 64, 0, GENERIC_REG_ADDR_SIZE_QWORD, 0x00000000000000E7}, NULL, SUPPORTED_BY_V2}, // MPERF
  //Delivered Performance Counter
  {CPC_BUFFER, {GENERIC_REG_SPACE_ID_FFH, 64, 0, GENERIC_REG_ADDR_SIZE_QWORD, 0x00000000000000E8}, NULL, SUPPORTED_BY_V2}, // APERF
  //Performance Limited
  {CPC_BUFFER, {GENERIC_REG_SPACE_ID_FFH, 2,  0, GENERIC_REG_ADDR_SIZE_QWORD, 0xC00102B4}, NULL, SUPPORTED_BY_V2},
  //CPPC Enable
  {CPC_BUFFER, {GENERIC_REG_SPACE_ID_FFH, 1,  0, GENERIC_REG_ADDR_SIZE_QWORD, 0xC00102B1}, NULL, SUPPORTED_BY_V2},
  //Autonomous Selection Enable
  {CPC_INTEGER_DWORD, {0xFF, 0xFF, 0xFF, 0xFF, 0xFFFFFFFFFFFFFFFF}, AmdCppcAutonomousSelectionEnable, SUPPORTED_BY_V2}, // fixed value, set to 1
  //Autonomous Activity Window
  {CPC_BUFFER, {GENERIC_REG_SPACE_ID_MEM, 0, 0, GENERIC_REG_ADDR_SIZE_UNDEFINED, 0}, NULL, SUPPORTED_BY_V2},
  //Energy Performance Preference
  {CPC_BUFFER, {GENERIC_REG_SPACE_ID_FFH, 8, 24, GENERIC_REG_ADDR_SIZE_QWORD, 0xC00102B3}, NULL, SUPPORTED_BY_V2},
  //Reference Performance
  {CPC_BUFFER, {GENERIC_REG_SPACE_ID_MEM, 0, 0, GENERIC_REG_ADDR_SIZE_UNDEFINED, 0}, NULL, SUPPORTED_BY_V2},
  //Lowest Frequency
  {CPC_INTEGER_DWORD, {0xFF, 0xFF, 0xFF, 0xFF, 0xFFFFFFFFFFFFFFFF}, AmdCppcGetLowestSpeed, SUPPORTED_BY_V3},
  //Nominal Frequency
  {CPC_INTEGER_DWORD, {0xFF, 0xFF, 0xFF, 0xFF, 0xFFFFFFFFFFFFFFFF}, AmdCppcGetNominalSpeed, SUPPORTED_BY_V3},
  //Terminator
  {CPC_END_OF_TABLE, {0xFF, 0xFF, 0xFF, 0xFF, 0xFFFFFFFFFFFFFFFF}, 0, 0},
};

/*----------------------------------------------------------------------------------------
 *           P R O T O T Y P E S     O F     L O C A L     F U N C T I O N S
 *----------------------------------------------------------------------------------------
 */
EFI_STATUS
EFIAPI
CcxZen5PStateGatherData (
  IN       AMD_ACPI_CPU_SSDT_SERVICES_PROTOCOL    *This,
     OUT   AMD_PSTATE_SYS_INFO                   **PstateSysInfoPtr
  );

AGESA_STATUS
CcxZen5GetPstateTransLatency (
     OUT   UINT32                                 *TransitionLatency,
  IN       AMD_CONFIG_PARAMS                      *StdHeader
  );

EFI_STATUS
EFIAPI
CcxZen5GetCStateInfo (
  IN       AMD_ACPI_CPU_SSDT_SERVICES_PROTOCOL    *This,
     OUT   AMD_CSTATE_INFO                       **CstateInfo
  );

UINT32
EFIAPI
CcxZen5GetPsdDomain (
  IN       AMD_ACPI_CPU_SSDT_SERVICES_PROTOCOL    *This,
  IN       UINT32                                  LocalApicId
  );

EFI_STATUS
EFIAPI
CcxZen5GetPStatePower (
  IN       AMD_ACPI_CPU_SSDT_SERVICES_PROTOCOL    *This,
  IN       CCX_PSTATE                             Pstate,
     OUT   UINTN                                  *PowerInmW
  );

EFI_STATUS
EFIAPI
CcxZen5GetCratHsaProcEntry (
  IN       AMD_CCX_ACPI_CRAT_SERVICES_PROTOCOL    *This,
  IN       CRAT_HEADER                            *CratHeaderStructPtr,
  IN OUT   UINT8                                 **TableEnd
  );

EFI_STATUS
EFIAPI
CcxZen5GetCratCacheEntry (
  IN       AMD_CCX_ACPI_CRAT_SERVICES_PROTOCOL    *This,
  IN       CRAT_HEADER                            *CratHeaderStructPtr,
  IN OUT   UINT8                                 **TableEnd
  );

EFI_STATUS
EFIAPI
CcxZen5GetCratTlbEntry (
  IN       AMD_CCX_ACPI_CRAT_SERVICES_PROTOCOL    *This,
  IN       CRAT_HEADER                            *CratHeaderStructPtr,
  IN OUT   UINT8                                 **TableEnd
  );

UINT8 *
AddOneCratEntry (
  IN       CRAT_ENTRY_TYPE    CratEntryType,
  IN       CRAT_HEADER       *CratHeaderStructPtr,
  IN OUT   UINT8            **TableEnd
  );

UINT8
GetCacheAssoc (
  IN       UINT16   RawAssoc
  );

UINT8
GetTlbSize (
  IN       TLB_TYPE   TLB_TYPE,
  IN       CRAT_TLB  *CratTlbEntry,
  IN       UINT16     RawAssocSize
  );

EFI_STATUS
EFIAPI
CcxZen5CreateSratApicEntry (
  IN       AMD_CCX_ACPI_SRAT_SERVICES_PROTOCOL    *This,
  IN       SRAT_HEADER                            *SratHeaderStructPtr,
  IN OUT   UINT8                                 **TableEnd
  );

EFI_STATUS
EFIAPI
CcxZen5AddGenCommSubspaceStruct (
  IN       AMD_CCX_ACPI_PCCT_SERVICES_PROTOCOL    *This,
  IN       PCCT_HEADER                            *PcctHeaderStructPtr,
  IN       AMD_CPPC_INFO                          *AmdCppcInfo,
  IN OUT   UINT8                                  **EndOfTable
  );

EFI_STATUS
EFIAPI
CcxZen5AddCoreRankingTableSlaveSubspaceStruct (
  IN       AMD_CCX_ACPI_PCCT_SERVICES_PROTOCOL    *This,
  IN       PCCT_HEADER                            *PcctHeaderStructPtr,
  IN       AMD_CPPC_INFO                          *AmdCppcInfo,
  IN OUT   UINT8                                  **EndOfTable
  );

VOID
EFIAPI
SetSharedMemoryToUC (
  IN       VOID  *Buffer
  );

EFI_STATUS
EFIAPI
CcxZen5GetRegisterDescriptorsMsr (
  IN       AMD_CCX_ACPI_CPPC_SERVICES_PROTOCOL    *This,
     OUT   CPPC_ENTRIES_COMMON_TABLE              **CppcRegDescMsr
  );

VOID
UpdateHeteroSsdt (
  IN     HETERO_DSM_ACPI_VARIABLE_STRUCT *HeteroDsmAcpiVariable
  );

/*----------------------------------------------------------------------------------------
 *                           G L O B A L   V A R I A B L E S
 *----------------------------------------------------------------------------------------
 */
STATIC AMD_ACPI_CPU_SSDT_SERVICES_PROTOCOL   mZen5SsdtServicesProtocol = {
  1,
  CcxZen5PStateGatherData,
  CcxZen5GetCStateInfo,
  CcxZen5GetPsdDomain,
  CcxZen5GetPStatePower
};

STATIC AMD_CCX_ACPI_CRAT_SERVICES_PROTOCOL   mZen5CratServicesProtocol = {
  0,
  CcxZen5GetCratHsaProcEntry,
  CcxZen5GetCratCacheEntry,
  CcxZen5GetCratTlbEntry
};

STATIC AMD_CCX_ACPI_SRAT_SERVICES_PROTOCOL   mZen5SratServicesProtocol = {
  2,
  CcxZen5CreateSratApicEntry
};

STATIC AMD_CCX_ACPI_PCCT_SERVICES_PROTOCOL  mZen5PcctServicesProtocol = {
  2,
  CcxZen5AddGenCommSubspaceStruct,
  CcxZen5AddCoreRankingTableSlaveSubspaceStruct
};

STATIC AMD_CCX_ACPI_CPPC_SERVICES_PROTOCOL  mZen5CppcServicesProtocol = {
  1,
  CcxZen5GetRegisterDescriptorsMsr
};

/*----------------------------------------------------------------------------------------
 *                          E X P O R T E D    F U N C T I O N S
 *----------------------------------------------------------------------------------------
 */

EFI_STATUS
EFIAPI
CcxZen5AcpiCpuSsdtServicesProtocolInstall (
  IN       EFI_HANDLE        ImageHandle,
  IN       EFI_SYSTEM_TABLE  *SystemTable
  )
{
  // Install ACPI CPU SSDT services protocol
  return gBS->InstallProtocolInterface (
                &ImageHandle,
                &gAmdAcpiCpuSsdtServicesProtocolGuid,
                EFI_NATIVE_INTERFACE,
                &mZen5SsdtServicesProtocol
                );
}

EFI_STATUS
EFIAPI
CcxZen5CratServicesProtocolInstall (
  IN       EFI_HANDLE        ImageHandle,
  IN       EFI_SYSTEM_TABLE  *SystemTable
  )
{
  // Install ACPI CPU CRAT services protocol
  return gBS->InstallProtocolInterface (
                &ImageHandle,
                &gAmdCcxAcpiCratServicesProtocolGuid,
                EFI_NATIVE_INTERFACE,
                &mZen5CratServicesProtocol
                );
}

EFI_STATUS
EFIAPI
CcxZen5SratServicesProtocolInstall (
  IN       EFI_HANDLE        ImageHandle,
  IN       EFI_SYSTEM_TABLE  *SystemTable
  )
{
  // Install ACPI CPU SRAT services protocol
  return gBS->InstallProtocolInterface (
                &ImageHandle,
                &gAmdCcxAcpiSratServicesProtocolGuid,
                EFI_NATIVE_INTERFACE,
                &mZen5SratServicesProtocol
                );
}

EFI_STATUS
EFIAPI
CcxZen5PcctServicesProtocolInstall (
  IN       EFI_HANDLE        ImageHandle,
  IN       EFI_SYSTEM_TABLE  *SystemTable
  )
{
  return gBS->InstallProtocolInterface (
                &ImageHandle,
                &gAmdCcxAcpiPcctServicesProtocolGuid,
                EFI_NATIVE_INTERFACE,
                &mZen5PcctServicesProtocol
                );
}

EFI_STATUS
EFIAPI
CcxZen5CppcServicesProtocolInstall (
  IN       EFI_HANDLE        ImageHandle,
  IN       EFI_SYSTEM_TABLE  *SystemTable
  )
{
  return gBS->InstallProtocolInterface (
                &ImageHandle,
                &gAmdCcxAcpiCppcServicesProtocolGuid,
                EFI_NATIVE_INTERFACE,
                &mZen5CppcServicesProtocol
                );
}
/*----------------------------------------------------------------------------------------
 *                          L O C A L    F U N C T I O N S
 *----------------------------------------------------------------------------------------
 */

/*----------------------------------------------------------------------------------------
 *                          AMD_ACPI_CPU_SSDT_SERVICES_PROTOCOL
 *----------------------------------------------------------------------------------------
 */

/**
 *---------------------------------------------------------------------------------------
 *
 *  CcxZen5PStateGatherData
 *
 *  Description:
 *    This function will gather PState information from the MSRs and fill up the
 *    pStateBuf. This buffer will be used by the PState table generation code later.
 *
 *  Parameters:
 *    @param[in]  This                                 A pointer to the AMD_ACPI_CPU_SSDT_SERVICES_PROTOCOL instance.
 *    @param[out] PstateSysInfoPtr                     Contains Pstate information for whole system
 *
 *    @returns    EFI_STATUS
 *
 *---------------------------------------------------------------------------------------
 **/
EFI_STATUS
EFIAPI
CcxZen5PStateGatherData (
  IN       AMD_ACPI_CPU_SSDT_SERVICES_PROTOCOL    *This,
     OUT   AMD_PSTATE_SYS_INFO                   **PstateSysInfoPtr
  )
{
  UINT32                                    LApicIdLoop;
  UINT32                                    TableSize;
  UINT32                                    PstateLoop;
  UINT32                                    TotalEnabledPStates;
  UINTN                                     NumberOfCcds;
  UINTN                                     NumberOfComplexes;
  UINTN                                     NumberOfCores;
  UINTN                                     NumberOfThreads;
  UINTN                                     CcdLoop;
  UINTN                                     ComplexLoop;
  UINTN                                     CoreLoop;
  UINTN                                     ThreadLoop;
  UINTN                                     MaxSwState;
  UINTN                                     NumberOfBoostPstate;
  UINTN                                     Frequency;
  UINTN                                     VoltageInuV;
  UINTN                                     PowerInmW;
  UINTN                                     NumberOfSockets;
  UINTN                                     NumberOfDies;
  UINTN                                     SocketLoop;
  UINTN                                     DieLoop;
  AMD_PSTATE_SOCKET_INFO                   *PstateSocketInfo;
  S_PSTATE_VALUES                          *PstateStructure;
  ALLOCATE_HEAP_PARAMS                      AllocHeapParams;
  EFI_STATUS                                Status;
  AGESA_STATUS                              AgesaStatus;
  AMD_CONFIG_PARAMS                         StdHeader;
  AMD_FABRIC_TOPOLOGY_SERVICES2_PROTOCOL   *FabricTopology;
  AMD_CORE_TOPOLOGY_SERVICES_V3_PROTOCOL   *CoreTopology;

  CcxGetPstateNumber (0, &NumberOfBoostPstate, &MaxSwState, &StdHeader);
  MaxSwState = MaxSwState - NumberOfBoostPstate;

  Status = gBS->LocateProtocol (&gAmdFabricTopologyServices2ProtocolGuid, NULL, (VOID **)&FabricTopology);
  ASSERT (!EFI_ERROR (Status));
  if (EFI_ERROR (Status)) {
    return EFI_DEVICE_ERROR;
  }

  Status = gBS->LocateProtocol (&gAmdCoreTopologyServicesV3ProtocolGuid, NULL, (VOID **)&CoreTopology);
  ASSERT (!EFI_ERROR (Status));
  if (EFI_ERROR (Status)) {
    return EFI_DEVICE_ERROR;
  }

  if (FabricTopology->GetSystemInfo (FabricTopology, &NumberOfSockets, NULL, NULL, NULL, NULL) != EFI_SUCCESS) {
    return EFI_DEVICE_ERROR;
  }

  // Create Heap and store p-state data for ACPI table
  TableSize = (UINT32) (sizeof (AMD_PSTATE_SYS_INFO) + MultU64x64 ((MultU64x64 (MaxSwState, sizeof (S_PSTATE_VALUES)) + sizeof (AMD_PSTATE_SOCKET_INFO)), NumberOfSockets));

  AllocHeapParams.RequestedBufferSize = TableSize;
  AllocHeapParams.BufferHandle        = AMD_PSTATE_DATA_BUFFER_HANDLE;
  AllocHeapParams.Persist             = HEAP_SYSTEM_MEM;
  AgesaStatus = HeapAllocateBuffer (&AllocHeapParams, &StdHeader);
  ASSERT (AgesaStatus == AGESA_SUCCESS);
  if (AgesaStatus == AGESA_SUCCESS) {
    *PstateSysInfoPtr = (AMD_PSTATE_SYS_INFO *) AllocHeapParams.BufferPtr;
  } else {
    return EFI_OUT_OF_RESOURCES;
  }

  (*PstateSysInfoPtr)->TotalSocketInSystem = (UINT8) NumberOfSockets;
  (*PstateSysInfoPtr)->SizeOfBytes    = TableSize;
  PstateSocketInfo = (*PstateSysInfoPtr)->PStateSocketStruct;

  for (SocketLoop = 0; SocketLoop < NumberOfSockets; SocketLoop++) {
    // Calculate number of logical cores
    LApicIdLoop = 0;
    if (FabricTopology->GetProcessorInfo (FabricTopology, SocketLoop, &NumberOfDies, NULL) == EFI_SUCCESS) {
      for (DieLoop = 0; DieLoop < NumberOfDies; DieLoop++) {
        if (CoreTopology->GetCcdCountOnDie (CoreTopology, SocketLoop, DieLoop, &NumberOfCcds) == EFI_SUCCESS) {
          for (CcdLoop = 0; CcdLoop < NumberOfCcds; CcdLoop++) {
            Status = CoreTopology->GetComplexCountOnCcd (CoreTopology, SocketLoop, DieLoop, CcdLoop, &NumberOfComplexes);
            ASSERT (!EFI_ERROR (Status));
            if (EFI_ERROR (Status)) {
              NumberOfComplexes = 0;
            }
            for (ComplexLoop = 0; ComplexLoop < NumberOfComplexes; ComplexLoop++) {
              Status = CoreTopology->GetCoreCountOnComplex (CoreTopology, SocketLoop, DieLoop, CcdLoop, ComplexLoop, &NumberOfCores);
              ASSERT (!EFI_ERROR (Status));
              if (EFI_ERROR (Status)) {
                NumberOfCores = 0;
              }
              for (CoreLoop = 0; CoreLoop < NumberOfCores; CoreLoop++) {
                Status = CoreTopology->GetThreadCountOnCore (CoreTopology, SocketLoop, DieLoop, CcdLoop, ComplexLoop, CoreLoop, &NumberOfThreads);
                ASSERT (!EFI_ERROR (Status));
                if (EFI_ERROR (Status)) {
                  NumberOfThreads = 0;
                }
                for (ThreadLoop = 0; ThreadLoop < NumberOfThreads; ThreadLoop++) {
                  if (LApicIdLoop < ARRAY_SIZE (PstateSocketInfo->LocalApicId)) {
                    PstateSocketInfo->LocalApicId[LApicIdLoop] = CcxCalcLocalApic (SocketLoop, DieLoop, CcdLoop, ComplexLoop, CoreLoop, ThreadLoop);
                  } else {
                    ASSERT (FALSE);
                  }
                  LApicIdLoop ++;
                }
              }
            }
          }
        }
      }
    }

    PstateSocketInfo->SocketNumber        = (UINT8) SocketLoop;
    PstateSocketInfo->TotalLogicalCores   = (UINT16) LApicIdLoop;
    PstateSocketInfo->CreateAcpiTables    = TRUE;
    PstateSocketInfo->PStateMaxValue      = (UINT8) MaxSwState;

    // Get transition latency
    CcxZen5GetPstateTransLatency (&(PstateSocketInfo->TransitionLatency), &StdHeader);

    // Get IsPsdDependent
    // Family 19h defaults to dependent PSD
    switch (PcdGet8 (PcdAmdAgesaPstatePolicy)) {
    case 0:
      PstateSocketInfo->IsPsdDependent = FALSE;
      break;
    case 1:
      PstateSocketInfo->IsPsdDependent = TRUE;
      break;
    case 2:
      PstateSocketInfo->IsPsdDependent = FALSE;
      break;
    default:
      ASSERT (FALSE);
      break;
    }

    PstateStructure = PstateSocketInfo->PStateStruct;
    TotalEnabledPStates = 0;

    for (PstateLoop = 0; PstateLoop <= MaxSwState; PstateLoop++) {

      LibAmdMemFill (PstateStructure, 0, sizeof (S_PSTATE_VALUES), &StdHeader);

      if (CcxGetPstateInfo (0, (SwPstate0 + PstateLoop), &Frequency, &VoltageInuV, &PowerInmW, &StdHeader)) {
        PstateStructure->CoreFreq       = (UINT32) Frequency;
        PstateStructure->Power          = (UINT32) PowerInmW;
        PstateStructure->SwPstateNumber = PstateLoop;
        PstateStructure->PStateEnable   = 1;
        PstateStructure++;
        TotalEnabledPStates++;
      }
    } // for (PstateLoop = 0; PstateLoop < MaxState; PstateLoop++)

    // Do not create ACPI Tables if there is one or less than one PState is enabled
    if (TotalEnabledPStates <= 1) {
      PstateSocketInfo->CreateAcpiTables = FALSE;
    }

    PstateSocketInfo = (AMD_PSTATE_SOCKET_INFO *) ((UINT8 *) PstateSocketInfo + sizeof (AMD_PSTATE_SOCKET_INFO) + sizeof (S_PSTATE_VALUES) * MaxSwState);
  }

  return EFI_SUCCESS;
}

/*---------------------------------------------------------------------------------------*/
/**
 *  Family specific call to get Pstate Transition Latency.
 *
 *  Calculate TransitionLatency by Gaters On/Off Time value and pll value.
 *
 *  @param[out]    TransitionLatency                 The transition latency.
 *  @param[in]     StdHeader                         Header for library and services
 *
 *  @retval        AGESA_SUCCESS Always succeeds.
 */
AGESA_STATUS
CcxZen5GetPstateTransLatency (
     OUT   UINT32                                 *TransitionLatency,
  IN       AMD_CONFIG_PARAMS                      *StdHeader
  )
{
  *TransitionLatency = 0;

  return (AGESA_SUCCESS);
}

/**
 *---------------------------------------------------------------------------------------
 *
 *  CcxZen5GetCStateInfo
 *
 *  Description:
 *    This function will gather CState information
 *
 *  Parameters:
 *    @param[in]  This                                 A pointer to the AMD_ACPI_CPU_SSDT_SERVICES_PROTOCOL instance.
 *    @param[out] CstateInfo                           Contains Cstate information
 *
 *    @returns    EFI_STATUS
 *
 *---------------------------------------------------------------------------------------
 **/
EFI_STATUS
EFIAPI
CcxZen5GetCStateInfo (
  IN       AMD_ACPI_CPU_SSDT_SERVICES_PROTOCOL    *This,
     OUT   AMD_CSTATE_INFO                       **CstateInfo
  )
{
  AGESA_STATUS          AgesaStatus;
  ALLOCATE_HEAP_PARAMS  AllocHeapParams;

  AllocHeapParams.RequestedBufferSize = sizeof (AMD_CSTATE_INFO);
  AllocHeapParams.BufferHandle        = AMD_CSTATE_DATA_BUFFER_HANDLE;
  AllocHeapParams.Persist             = HEAP_SYSTEM_MEM;
  AgesaStatus = HeapAllocateBuffer (&AllocHeapParams, NULL);
  ASSERT (AgesaStatus == AGESA_SUCCESS);
  if (AgesaStatus == AGESA_SUCCESS) {
    *CstateInfo = (AMD_CSTATE_INFO *) AllocHeapParams.BufferPtr;
  } else {
    return EFI_OUT_OF_RESOURCES;
  }

  // Is Cstate enabled
  if ((PcdGet8 (PcdAmdCStateMode) != 1) ||
      (PcdGet16 (PcdAmdCStateIoBaseAddress) == 0)) {
    (*CstateInfo)->IsCstateEnabled = FALSE;
  } else {
    (*CstateInfo)->IsCstateEnabled = TRUE;
  }

  // Io Cstate address
  (*CstateInfo)->IoCstateAddr = (UINT32) PcdGet16 (PcdAmdCStateIoBaseAddress);

  (*CstateInfo)->IsCsdGenerated = (BOOLEAN) (CcxGetThreadsPerCore () > 1);

  (*CstateInfo)->IsMonitorMwaitSupported = TRUE;

  (*CstateInfo)->C1Latency = (UINT16) PcdGet16 (PcdAmdAcpiCpuCstC1Latency);

  (*CstateInfo)->C2Latency = (UINT16) PcdGet16 (PcdAmdAcpiCpuCstC2Latency);

  (*CstateInfo)->C3Latency = (UINT16) PcdGet16 (PcdAmdAcpiCpuCstC3Latency);

  (*CstateInfo)->C4Latency = (UINT16) PcdGet16 (PcdAmdAcpiCpuCstC4Latency);

  return EFI_SUCCESS;
}

/**
 *---------------------------------------------------------------------------------------
 *
 *  CcxZen5GetPsdDomain
 *
 *  Description:
 *    This function will return PSD domain for independency
 *
 *  Parameters:
 *    @param[in]  This                                 A pointer to the AMD_ACPI_CPU_SSDT_SERVICES_PROTOCOL instance.
 *    @param[in]  LocalApicId                          Local APIC ID
 *
 *    @returns    _PSD domain for the given core
 *
 *---------------------------------------------------------------------------------------
 **/
UINT32
EFIAPI
CcxZen5GetPsdDomain (
  IN       AMD_ACPI_CPU_SSDT_SERVICES_PROTOCOL    *This,
  IN       UINT32                                  LocalApicId
  )
{
  UINT8  ThreadsPerCore;

  ThreadsPerCore = CcxGetThreadsPerCore ();
  ASSERT (ThreadsPerCore != 0);

  return ((UINT32) (LocalApicId / ThreadsPerCore));
}

/**
 *---------------------------------------------------------------------------------------
 *
 *  CcxZen5GetPStatePower
 *
 *  Description:
 *    This function will return the Power in mW for specified PState
 *
 *  Parameters:
 *    @param[in]  This                    Pointer to the AMD_ACPI_CPU_SSDT_SERVICES_PROTOCOL instance.
 *    @param[in]  Pstate                  PState to read
 *    @param[out] PowerInmW               Power in mW of the specified PState
 *
 *    @retval     EFI_STATUS
 *
 *---------------------------------------------------------------------------------------
 **/
EFI_STATUS
EFIAPI
CcxZen5GetPStatePower (
  IN       AMD_ACPI_CPU_SSDT_SERVICES_PROTOCOL    *This,
  IN       CCX_PSTATE                             Pstate,
     OUT   UINTN                                  *PowerInmW
  )
{
  UINTN             Frequency;
  UINTN             VoltageInuV;
  AMD_CONFIG_PARAMS StdHeader;

  CcxGetPstateInfo (0, Pstate, &Frequency, &VoltageInuV, PowerInmW, &StdHeader);

  return EFI_SUCCESS;
}

/*----------------------------------------------------------------------------------------
 *                          AMD_CCX_ACPI_CRAT_SERVICES_PROTOCOL
 *----------------------------------------------------------------------------------------
 */

/**
 * This service retrieves CRAT information about the HSA.
 *
 * @param[in]      This                             A pointer to the
 *                                                  AMD_CCX_ACPI_CRAT_SERVICES_PROTOCOL instance.
 * @param[in]      CratHeaderStructPtr              CRAT table structure pointer
 * @param[in, out] TableEnd                         Point to the end of this table
 *
 * @retval EFI_SUCCESS                              The HSA processor information was successfully retrieved.
 * @retval EFI_INVALID_PARAMETER                    CratHsaProcInfo is NULL.
 *
 **/
EFI_STATUS
EFIAPI
CcxZen5GetCratHsaProcEntry (
  IN       AMD_CCX_ACPI_CRAT_SERVICES_PROTOCOL    *This,
  IN       CRAT_HEADER                            *CratHeaderStructPtr,
  IN OUT   UINT8                                 **TableEnd
  )
{
  UINTN                                    SocketLoop;
  UINTN                                    DieLoop;
  UINTN                                    NumberOfSockets;
  UINTN                                    NumberOfDies;
  UINTN                                    NumberOfCcds;
  UINTN                                    NumberOfComplexes;
  UINTN                                    NumberOfCores;
  UINTN                                    NumberOfThreads;
  UINTN                                    CcdLoop;
  UINTN                                    ComplexLoop;
  UINTN                                    CoreLoop;
  UINT32                                   Domain;
  UINT32                                   PreDomain;
  EFI_STATUS                               CalledStatus;
  CRAT_HSA_PROCESSING_UNIT                *CratHsaEntry;
  AMD_FABRIC_TOPOLOGY_SERVICES2_PROTOCOL  *FabricTopology;
  AMD_CORE_TOPOLOGY_SERVICES_V3_PROTOCOL  *CoreTopologyServices;
  FABRIC_NUMA_SERVICES2_PROTOCOL          *FabricNuma;

  // Locate Fabric CRAT Services Protocol
  if (gBS->LocateProtocol (&gAmdFabricNumaServices2ProtocolGuid, NULL, (VOID **) &FabricNuma) != EFI_SUCCESS) {
    return EFI_ABORTED;
  }

  // Locate FabricTopologyServices2Protocol
  CalledStatus = gBS->LocateProtocol (&gAmdFabricTopologyServices2ProtocolGuid, NULL, (VOID **) &FabricTopology);
  ASSERT (!EFI_ERROR (CalledStatus));
  if (EFI_ERROR (CalledStatus)) {
    return EFI_ABORTED;
  }

  CalledStatus = gBS->LocateProtocol (&gAmdCoreTopologyServicesV3ProtocolGuid, NULL, (VOID **)&CoreTopologyServices);
  ASSERT (!EFI_ERROR (CalledStatus));
  if (EFI_ERROR (CalledStatus)) {
    return EFI_ABORTED;
  }

  PreDomain = 0;
  CratHsaEntry = NULL;
  if (FabricTopology->GetSystemInfo (FabricTopology, &NumberOfSockets, NULL, NULL, NULL, NULL) == EFI_SUCCESS) {
    for (SocketLoop = 0; SocketLoop < NumberOfSockets; SocketLoop++) {
      if (FabricTopology->GetProcessorInfo (FabricTopology, SocketLoop, &NumberOfDies, NULL) == EFI_SUCCESS) {
        for (DieLoop = 0; DieLoop < NumberOfDies; DieLoop++) {
          if (FabricNuma->DomainXlat (FabricNuma, SocketLoop, DieLoop, 0, 0, &Domain) ==  EFI_SUCCESS) {
            if ((Domain != PreDomain) || (Domain == 0)) {
              CratHsaEntry = (CRAT_HSA_PROCESSING_UNIT *) AddOneCratEntry (CRAT_HSA_PROC_UNIT_TYPE, CratHeaderStructPtr, TableEnd);
              CratHsaEntry->Flags.Enabled    = 1;
              CratHsaEntry->Flags.CpuPresent = 1;
              CratHsaEntry->ProximityNode    = Domain;
              CratHsaEntry->ProcessorIdLow   = CcxCalcLocalApic (SocketLoop, DieLoop, 0, 0, 0, 0);
              CratHsaEntry->NumCPUCores      = 0;
              CratHsaEntry->WaveFrontSize    = 4;
              PreDomain = Domain;
            }
            //
            // Get thread count by CCD/Complex/Core loop
            //
            CalledStatus = CoreTopologyServices->GetCcdCountOnDie (CoreTopologyServices, SocketLoop, DieLoop, &NumberOfCcds);
            ASSERT (!EFI_ERROR (CalledStatus));
            if (EFI_ERROR (CalledStatus)) {
              NumberOfCcds = 0;
            }
            for (CcdLoop = 0; CcdLoop < NumberOfCcds; CcdLoop++) {
              CalledStatus = CoreTopologyServices->GetComplexCountOnCcd (CoreTopologyServices, SocketLoop, DieLoop, CcdLoop, &NumberOfComplexes);
              ASSERT (!EFI_ERROR (CalledStatus));
              if (EFI_ERROR (CalledStatus)) {
                NumberOfComplexes = 0;
              }
              for (ComplexLoop = 0; ComplexLoop < NumberOfComplexes; ComplexLoop++) {
                CalledStatus = CoreTopologyServices->GetCoreCountOnComplex (CoreTopologyServices, SocketLoop, DieLoop, CcdLoop, ComplexLoop, &NumberOfCores);
                ASSERT (!EFI_ERROR (CalledStatus));
                if (EFI_ERROR (CalledStatus)) {
                  NumberOfCores = 0;
                }
                for (CoreLoop = 0; CoreLoop < NumberOfCores; CoreLoop++) {
                  CalledStatus = CoreTopologyServices->GetThreadCountOnCore (CoreTopologyServices, SocketLoop, DieLoop, CcdLoop, ComplexLoop, CoreLoop, &NumberOfThreads);
                  ASSERT (!EFI_ERROR (CalledStatus));
                  if (EFI_ERROR (CalledStatus)) {
                    NumberOfThreads = 0;
                  }
                  CratHsaEntry->NumCPUCores += (UINT16) (NumberOfThreads);
                }
              }
            }
          }
        }
      }
    }
  }

  return EFI_SUCCESS;
}

/**
 * This service retrieves information about the cache.
 *
 * @param[in]      This                             A pointer to the
 *                                                  AMD_CCX_ACPI_CRAT_SERVICES_PROTOCOL instance.
 * @param[in]      CratHeaderStructPtr              CRAT table structure pointer
 * @param[in, out] TableEnd                         Point to the end of this table
 *
 * @retval EFI_SUCCESS                              The cache information was successfully retrieved.
 * @retval EFI_INVALID_PARAMETER                    CratCacheInfo is NULL.
 *
 **/
EFI_STATUS
EFIAPI
CcxZen5GetCratCacheEntry (
  IN       AMD_CCX_ACPI_CRAT_SERVICES_PROTOCOL    *This,
  IN       CRAT_HEADER                            *CratHeaderStructPtr,
  IN OUT   UINT8                                 **TableEnd
  )
{
  UINT8                                    i;
  UINT32                                   ApicId;
  UINT8                                    SiblingMapMask;
  UINT32                                   NumOfThreadsSharing;
  UINT32                                   TotalThreads;
  UINTN                                    SocketLoop;
  UINTN                                    DieLoop;
  UINTN                                    CcdLoop;
  UINTN                                    ComplexLoop;
  UINTN                                    CoreLoop;
  UINTN                                    ThreadsLoop;
  UINTN                                    NumberOfSockets;
  UINTN                                    NumberOfDies;
  UINTN                                    NumberOfCcds;
  UINTN                                    NumberOfComplexes;
  UINTN                                    NumberOfCores;
  UINTN                                    NumberOfThreads;
  UINT32                                   CacheProp;
  EFI_STATUS                               CalledStatus;
  AMD_FABRIC_TOPOLOGY_SERVICES2_PROTOCOL  *FabricTopology;
  AMD_CORE_TOPOLOGY_SERVICES_V3_PROTOCOL  *CoreTopologyServices;
  CRAT_CACHE                              *CratCacheEntry;
  CPUID_CACHE_PROP                         CacheProperties;
  CPUID_EXT_FEAT_MSR                       ExtFeat;
  BOOLEAN                                  DisTopoExt;

  // Locate FabricTopologyServices2Protocol
  CalledStatus = gBS->LocateProtocol (&gAmdFabricTopologyServices2ProtocolGuid, NULL, (VOID **) &FabricTopology);
  ASSERT (!EFI_ERROR (CalledStatus));
  if (EFI_ERROR (CalledStatus)) {
    return EFI_DEVICE_ERROR;
  }

  CalledStatus = gBS->LocateProtocol (&gAmdCoreTopologyServicesV3ProtocolGuid, NULL, (VOID **)&CoreTopologyServices);
  ASSERT (!EFI_ERROR (CalledStatus));
  if (EFI_ERROR (CalledStatus)) {
    return EFI_DEVICE_ERROR;
  }

  ExtFeat.Value = AsmReadMsr64 (MSR_CPUID_EXT_FEAT);
  if (ExtFeat.Field.TopologyExtensions == 0) {
    DisTopoExt = TRUE;
    ExtFeat.Field.TopologyExtensions = 1;
    AsmWriteMsr64 (MSR_CPUID_EXT_FEAT, ExtFeat.Value);
  } else {
    DisTopoExt = FALSE;
  }

  TotalThreads = 0;
  if (FabricTopology->GetSystemInfo (FabricTopology, &NumberOfSockets, NULL, NULL, NULL, NULL) == EFI_SUCCESS) {
    for (SocketLoop = 0; SocketLoop < NumberOfSockets; SocketLoop++) {
      if (FabricTopology->GetProcessorInfo (FabricTopology, SocketLoop, &NumberOfDies, NULL) == EFI_SUCCESS) {
        for (DieLoop = 0; DieLoop < NumberOfDies; DieLoop++) {
          if (CoreTopologyServices->GetCcdCountOnDie (CoreTopologyServices, SocketLoop, DieLoop, &NumberOfCcds) == EFI_SUCCESS) {
            for (CcdLoop = 0; CcdLoop < NumberOfCcds; CcdLoop++) {
              CalledStatus = CoreTopologyServices->GetComplexCountOnCcd (CoreTopologyServices, SocketLoop, DieLoop, CcdLoop, &NumberOfComplexes);
              ASSERT (!EFI_ERROR (CalledStatus));
              if (EFI_ERROR (CalledStatus)) {
                NumberOfComplexes = 0;
              }
              for (ComplexLoop = 0; ComplexLoop < NumberOfComplexes; ComplexLoop++) {
                CalledStatus = CoreTopologyServices->GetCoreCountOnComplex (CoreTopologyServices, SocketLoop, DieLoop, CcdLoop, ComplexLoop, &NumberOfCores);
                ASSERT (!EFI_ERROR (CalledStatus));
                if (EFI_ERROR (CalledStatus)) {
                  NumberOfCores = 0;
                }
                for (CoreLoop = 0; CoreLoop < NumberOfCores; CoreLoop++) {
                  CalledStatus = CoreTopologyServices->GetThreadCountOnCore (CoreTopologyServices, SocketLoop, DieLoop, CcdLoop, ComplexLoop, CoreLoop, &NumberOfThreads);
                  ASSERT (!EFI_ERROR (CalledStatus));
                  if (EFI_ERROR (CalledStatus)) {
                    NumberOfThreads = 0;
                  }
                  for (ThreadsLoop = 0; ThreadsLoop < NumberOfThreads; ThreadsLoop++) {
                    ApicId = CcxCalcLocalApic (SocketLoop, DieLoop, CcdLoop, ComplexLoop, CoreLoop, ThreadsLoop);
                    CacheProp = 0;
                    do {
                      CacheProperties.EAX.Value = 0;
                      CacheProperties.EBX.Value = 0;
                      CacheProperties.ECX.Value = 0;
                      CacheProperties.EDX.Value = 0;
                      AsmCpuidEx (CPUID_CACHE_PROPERTIES,
                                  CacheProp++,
                                  &(CacheProperties.EAX.Value),
                                  &(CacheProperties.EBX.Value),
                                  &(CacheProperties.ECX.Value),
                                  &(CacheProperties.EDX.Value));
                      if (CacheProperties.EAX.Field.CacheType != 0) {
                        NumOfThreadsSharing = CacheProperties.EAX.Field.NumSharingCache + 1;
                        if ((TotalThreads % NumOfThreadsSharing) == 0) {
                          CratCacheEntry = (CRAT_CACHE *) AddOneCratEntry (CRAT_CACHE_TYPE, CratHeaderStructPtr, TableEnd);
                          CratCacheEntry->Flags.Enabled   = 1;
                          CratCacheEntry->Flags.CpuCache  = 1;
                          CratCacheEntry->Flags.DataCache = (CacheProperties.EAX.Field.CacheType != 2) ? 1 : 0;
                          CratCacheEntry->Flags.InstructionCache = (CacheProperties.EAX.Field.CacheType != 1) ? 1 : 0;
                          CratCacheEntry->ProcessorIdLow  = ApicId;
                          SiblingMapMask = 1;
                          for (i = 1; i < NumOfThreadsSharing; i++) {
                            SiblingMapMask = (SiblingMapMask << 1) + 1;
                          }
                          i = (UINT8) (TotalThreads / BITS_PER_BYTE);
                          CratCacheEntry->SiblingMap[i]   = SiblingMapMask << (TotalThreads % BITS_PER_BYTE);
                          CratCacheEntry->CacheProperties = (CacheProperties.EDX.Field.CacheInclusive == 0) ? 0 : 2;
                          CratCacheEntry->CacheSize       = (((CacheProperties.ECX.Field.CacheNumSets + 1) * (CacheProperties.EBX.Field.CacheNumWays + 1) * \
                                                            (CacheProperties.EBX.Field.CacheLineSize + 1)) >> KB_SHIFT);
                          CratCacheEntry->CacheLevel      = (UINT8) CacheProperties.EAX.Field.CacheLevel;
                          CratCacheEntry->CacheLineSize   = (UINT16) (CacheProperties.EBX.Field.CacheLineSize + 1);
                          CratCacheEntry->Associativity   = (UINT8) ((CacheProperties.EAX.Field.FullyAssociative == 1) ? 0xFF : (CacheProperties.EBX.Field.CacheNumWays + 1));
                          CratCacheEntry->CacheLatency    = 1;
                          CratCacheEntry->LinesPerTag     = 1;
                        }
                      }
                    } while (CacheProperties.EAX.Field.CacheType != 0);
                    TotalThreads++;
                  }
                }
              }
            }
          }
        }
      }
    }
  }

  if (DisTopoExt) {
    ExtFeat.Field.TopologyExtensions = 0;
    AsmWriteMsr64 (MSR_CPUID_EXT_FEAT, ExtFeat.Value);
  }

  return EFI_SUCCESS;
}

/**
 * This service retrieves information about the TLB.
 *
 * @param[in]      This                             A pointer to the
 *                                                  AMD_CCX_ACPI_CRAT_SERVICES_PROTOCOL instance.
 * @param[in]      CratHeaderStructPtr              CRAT table structure pointer
 * @param[in, out] TableEnd                         Point to the end of this table
 *
 * @retval EFI_SUCCESS                              The TLB information was successfully retrieved.
 * @retval EFI_INVALID_PARAMETER                    CratTlbInfo is NULL.
 *
 **/
EFI_STATUS
EFIAPI
CcxZen5GetCratTlbEntry (
  IN       AMD_CCX_ACPI_CRAT_SERVICES_PROTOCOL    *This,
  IN       CRAT_HEADER                            *CratHeaderStructPtr,
  IN OUT   UINT8                                 **TableEnd
  )
{
  UINT8                                    i;
  UINT32                                   ApicId;
  UINT8                                    SiblingMapMask;
  UINT32                                   NumOfThreadsSharing;
  UINT32                                   TotalThreads;
  UINTN                                    SocketLoop;
  UINTN                                    DieLoop;
  UINTN                                    CcdLoop;
  UINTN                                    ComplexLoop;
  UINTN                                    CoreLoop;
  UINTN                                    ThreadsLoop;
  UINTN                                    NumberOfSockets;
  UINTN                                    NumberOfDies;
  UINTN                                    NumberOfCcds;
  UINTN                                    NumberOfComplexes;
  UINTN                                    NumberOfCores;
  UINTN                                    NumberOfThreads;
  CPUID_DATA                               CpuId;
  EFI_STATUS                               CalledStatus;
  AMD_FABRIC_TOPOLOGY_SERVICES2_PROTOCOL  *FabricTopology;
  AMD_CORE_TOPOLOGY_SERVICES_V3_PROTOCOL  *CoreTopologyServices;
  CRAT_TLB                                *CratTlbEntry;

  // Locate FabricTopologyServices2Protocol
  CalledStatus = gBS->LocateProtocol (&gAmdFabricTopologyServices2ProtocolGuid, NULL, (VOID **) &FabricTopology);
  ASSERT (!EFI_ERROR (CalledStatus));
  if (EFI_ERROR (CalledStatus)) {
    return EFI_DEVICE_ERROR;
  }

  CalledStatus = gBS->LocateProtocol (&gAmdCoreTopologyServicesV3ProtocolGuid, NULL, (VOID **)&CoreTopologyServices);
  ASSERT (!EFI_ERROR (CalledStatus));
  if (EFI_ERROR (CalledStatus)) {
    return EFI_DEVICE_ERROR;
  }

  TotalThreads = 0;
  if (FabricTopology->GetSystemInfo (FabricTopology, &NumberOfSockets, NULL, NULL, NULL, NULL) == EFI_SUCCESS) {
    for (SocketLoop = 0; SocketLoop < NumberOfSockets; SocketLoop++) {
      if (FabricTopology->GetProcessorInfo (FabricTopology, SocketLoop, &NumberOfDies, NULL) == EFI_SUCCESS) {
        for (DieLoop = 0; DieLoop < NumberOfDies; DieLoop++) {
          if (CoreTopologyServices->GetCcdCountOnDie (CoreTopologyServices, SocketLoop, DieLoop, &NumberOfCcds) == EFI_SUCCESS) {
            for (CcdLoop = 0; CcdLoop < NumberOfCcds; CcdLoop++) {
              CalledStatus = CoreTopologyServices->GetComplexCountOnCcd (CoreTopologyServices, SocketLoop, DieLoop, CcdLoop, &NumberOfComplexes);
              ASSERT (!EFI_ERROR (CalledStatus));
              if (EFI_ERROR (CalledStatus)) {
                NumberOfComplexes = 0;
              }
              for (ComplexLoop = 0; ComplexLoop < NumberOfComplexes; ComplexLoop++) {
                CalledStatus = CoreTopologyServices->GetCoreCountOnComplex (CoreTopologyServices, SocketLoop, DieLoop, CcdLoop, ComplexLoop, &NumberOfCores);
                ASSERT (!EFI_ERROR (CalledStatus));
                if (EFI_ERROR (CalledStatus)) {
                  NumberOfCores = 0;
                }
                for (CoreLoop = 0; CoreLoop < NumberOfCores; CoreLoop++) {
                  CalledStatus = CoreTopologyServices->GetThreadCountOnCore (CoreTopologyServices, SocketLoop, DieLoop, CcdLoop, ComplexLoop, CoreLoop, &NumberOfThreads);
                  ASSERT (!EFI_ERROR (CalledStatus));
                  if (EFI_ERROR (CalledStatus)) {
                    NumberOfThreads = 0;
                  }
                  for (ThreadsLoop = 0; ThreadsLoop < NumberOfThreads; ThreadsLoop++) {
                    ApicId = CcxCalcLocalApic (SocketLoop, DieLoop, CcdLoop, ComplexLoop, CoreLoop, ThreadsLoop);

                    // L1 Data TLB
                    CpuId.EAX_Reg = 0;
                    AsmCpuidEx (0x8000001D, 0, &(CpuId.EAX_Reg), NULL, NULL, NULL);
                    NumOfThreadsSharing = (((CpuId.EAX_Reg >> 14) & 0xFFF) + 1);
                    if ((TotalThreads % NumOfThreadsSharing) == 0) {
                      CratTlbEntry = (CRAT_TLB *) AddOneCratEntry (CRAT_TLB_TYPE, CratHeaderStructPtr, TableEnd);
                      CratTlbEntry->Flags.Enabled     = 1;
                      CratTlbEntry->Flags.DataTLB     = 1;
                      CratTlbEntry->Flags.CpuTLB      = 1;
                      CratTlbEntry->ProcessorIdLow    = ApicId;
                      SiblingMapMask = 1;
                      for (i = 1; i < NumOfThreadsSharing; i++) {
                        SiblingMapMask = (SiblingMapMask << 1) + 1;
                      }
                      i = (UINT8) (TotalThreads / 8);
                      CratTlbEntry->SiblingMap[i]     = SiblingMapMask << (TotalThreads % 8);
                      CratTlbEntry->TlbLevel          = L1_CACHE;
                      CpuId.EAX_Reg = 0;
                      CpuId.EBX_Reg = 0;
                      AsmCpuid (AMD_CPUID_TLB_L1Cache, &(CpuId.EAX_Reg), &(CpuId.EBX_Reg), NULL, NULL);
                      CratTlbEntry->DataTlbAssociativity2MB = CpuId.EAX_Reg >> 24;
                      CratTlbEntry->DataTlbSize2MB    = GetTlbSize (TLB_2M, CratTlbEntry, ((CpuId.EAX_Reg >> 16) & 0xFF));
                      CratTlbEntry->DTLB4KAssoc       = CpuId.EBX_Reg >> 24;
                      CratTlbEntry->DTLB4KSize        = GetTlbSize (TLB_4K, CratTlbEntry, ((CpuId.EBX_Reg >> 16) & 0xFF));
                      CpuId.EAX_Reg = 0;
                      AsmCpuid (AMD_CPUID_L1L2Tlb1GIdentifiers, &(CpuId.EAX_Reg), NULL, NULL, NULL);
                      CratTlbEntry->DTLB1GAssoc       = CpuId.EAX_Reg >> 28;
                      CratTlbEntry->DTLB1GSize        = GetTlbSize (TLB_1G, CratTlbEntry, ((CpuId.EAX_Reg >> 16) & 0xFFF));
                    }

                    // L1 Instruction TLB
                    CpuId.EAX_Reg = 0;
                    AsmCpuidEx (0x8000001D, 1, &(CpuId.EAX_Reg), NULL, NULL, NULL);
                    NumOfThreadsSharing = (((CpuId.EAX_Reg >> 14) & 0xFFF) + 1);
                    if ((TotalThreads % NumOfThreadsSharing) == 0) {
                      CratTlbEntry = (CRAT_TLB *) AddOneCratEntry (CRAT_TLB_TYPE, CratHeaderStructPtr, TableEnd);
                      CratTlbEntry->Flags.Enabled     = 1;
                      CratTlbEntry->Flags.InstructionTLB = 1;
                      CratTlbEntry->Flags.CpuTLB      = 1;
                      CratTlbEntry->ProcessorIdLow    = ApicId;
                      SiblingMapMask = 1;
                      for (i = 1; i < NumOfThreadsSharing; i++) {
                        SiblingMapMask = (SiblingMapMask << 1) + 1;
                      }
                      i = (UINT8) (TotalThreads / 8);
                      CratTlbEntry->SiblingMap[i]     = SiblingMapMask << (TotalThreads % 8);
                      CratTlbEntry->TlbLevel          = L1_CACHE;
                      CpuId.EAX_Reg = 0;
                      CpuId.EBX_Reg = 0;
                      AsmCpuid (AMD_CPUID_TLB_L1Cache, &(CpuId.EAX_Reg), &(CpuId.EBX_Reg), NULL, NULL);
                      CratTlbEntry->InstructionTlbAssociativity2MB = (CpuId.EAX_Reg >> 8) & 0xFF;
                      CratTlbEntry->InstructionTlbSize2MB = GetTlbSize (TLB_2M, CratTlbEntry, (CpuId.EAX_Reg & 0xFF));
                      CratTlbEntry->ITLB4KAssoc       = (CpuId.EBX_Reg >> 8) & 0xFF;
                      CratTlbEntry->ITLB4KSize        = GetTlbSize (TLB_4K, CratTlbEntry, (CpuId.EBX_Reg & 0xFF));
                      CpuId.EAX_Reg = 0;
                      AsmCpuid (AMD_CPUID_L1L2Tlb1GIdentifiers, &(CpuId.EAX_Reg), NULL, NULL, NULL);
                      CratTlbEntry->ITLB1GAssoc       = (CpuId.EAX_Reg >> 12) & 0xF;
                      CratTlbEntry->ITLB1GSize        = GetTlbSize (TLB_1G, CratTlbEntry, (CpuId.EAX_Reg & 0xFFF));
                    }

                    // L2 Data TLB
                    CpuId.EAX_Reg = 0;
                    AsmCpuidEx (0x8000001D, 2, &(CpuId.EAX_Reg), NULL, NULL, NULL);
                    NumOfThreadsSharing = (((CpuId.EAX_Reg >> 14) & 0xFFF) + 1);
                    if ((TotalThreads % NumOfThreadsSharing) == 0) {
                      CratTlbEntry = (CRAT_TLB *) AddOneCratEntry (CRAT_TLB_TYPE, CratHeaderStructPtr, TableEnd);
                      CratTlbEntry->Flags.Enabled     = 1;
                      CratTlbEntry->Flags.DataTLB     = 1;
                      CratTlbEntry->Flags.CpuTLB      = 1;
                      CratTlbEntry->ProcessorIdLow    = ApicId;
                      SiblingMapMask = 1;
                      for (i = 1; i < NumOfThreadsSharing; i++) {
                        SiblingMapMask = (SiblingMapMask << 1) + 1;
                      }
                      i = (UINT8) (TotalThreads / 8);
                      CratTlbEntry->SiblingMap[i]     = SiblingMapMask << (TotalThreads % 8);
                      CratTlbEntry->TlbLevel          = L2_CACHE;
                      CpuId.EAX_Reg = 0;
                      CpuId.EBX_Reg = 0;
                      AsmCpuid (AMD_CPUID_L2L3Cache_L2TLB, &(CpuId.EAX_Reg), &(CpuId.EBX_Reg), NULL, NULL);
                      CratTlbEntry->DataTlbAssociativity2MB = CpuId.EAX_Reg >> 28;
                      CratTlbEntry->DataTlbSize2MB    = GetTlbSize (TLB_2M, CratTlbEntry, ((CpuId.EAX_Reg >> 16) & 0xFFF));
                      CratTlbEntry->DTLB4KAssoc       = GetCacheAssoc (CpuId.EBX_Reg >> 28);
                      CratTlbEntry->DTLB4KSize        = GetTlbSize (TLB_4K, CratTlbEntry, ((CpuId.EBX_Reg >> 16) & 0xFFF));
                      CpuId.EAX_Reg = 0;
                      CpuId.EBX_Reg = 0;
                      AsmCpuid (AMD_CPUID_L1L2Tlb1GIdentifiers, &(CpuId.EAX_Reg), &(CpuId.EBX_Reg), NULL, NULL);
                      CratTlbEntry->DTLB1GAssoc       = GetCacheAssoc (CpuId.EBX_Reg >> 28);
                      CratTlbEntry->DTLB1GSize        = GetTlbSize (TLB_1G, CratTlbEntry, ((CpuId.EAX_Reg >> 16) & 0xFFF));
                    }

                    // L2 Instruction TLB
                    CpuId.EAX_Reg = 0;
                    AsmCpuidEx (0x8000001D, 2, &(CpuId.EAX_Reg), NULL, NULL, NULL);
                    NumOfThreadsSharing = (((CpuId.EAX_Reg >> 14) & 0xFFF) + 1);
                    if ((TotalThreads % NumOfThreadsSharing) == 0) {
                      CratTlbEntry = (CRAT_TLB *) AddOneCratEntry (CRAT_TLB_TYPE, CratHeaderStructPtr, TableEnd);
                      CratTlbEntry->Flags.Enabled     = 1;
                      CratTlbEntry->Flags.InstructionTLB = 1;
                      CratTlbEntry->Flags.CpuTLB      = 1;
                      CratTlbEntry->ProcessorIdLow    = ApicId;
                      SiblingMapMask = 1;
                      for (i = 1; i < NumOfThreadsSharing; i++) {
                        SiblingMapMask = (SiblingMapMask << 1) + 1;
                      }
                      i = (UINT8) (TotalThreads / 8);
                      CratTlbEntry->SiblingMap[i]     = SiblingMapMask << (TotalThreads % 8);
                      CratTlbEntry->TlbLevel          = L2_CACHE;
                      CpuId.EAX_Reg = 0;
                      CpuId.EBX_Reg = 0;
                      AsmCpuid (AMD_CPUID_L2L3Cache_L2TLB, &(CpuId.EAX_Reg), &(CpuId.EBX_Reg), &(CpuId.ECX_Reg), &(CpuId.EDX_Reg));
                      CratTlbEntry->InstructionTlbAssociativity2MB = GetCacheAssoc ((CpuId.EAX_Reg >> 12) & 0xF);
                      CratTlbEntry->InstructionTlbSize2MB = GetTlbSize (TLB_2M, CratTlbEntry, (CpuId.EAX_Reg & 0xFFF));
                      CratTlbEntry->ITLB4KAssoc       = GetCacheAssoc ((CpuId.EBX_Reg >> 12) & 0xF);
                      CratTlbEntry->ITLB4KSize        = GetTlbSize (TLB_4K, CratTlbEntry, (CpuId.EBX_Reg & 0xFFF));
                      CpuId.EBX_Reg = 0;
                      AsmCpuid (AMD_CPUID_L1L2Tlb1GIdentifiers, NULL, &(CpuId.EBX_Reg), NULL, NULL);
                      CratTlbEntry->ITLB1GAssoc       = GetCacheAssoc ((CpuId.EBX_Reg >> 12) & 0xF);
                      CratTlbEntry->ITLB1GSize        = GetTlbSize (TLB_1G, CratTlbEntry, (CpuId.EBX_Reg & 0xFFF));
                    }

                    // No L3 TLB
                    TotalThreads++;
                  }
                }
              }
            }
          }
        }
      }
    }
  }

  return EFI_SUCCESS;
}

/*----------------------------------------------------------------------------------------
 *                          AMD_CCX_ACPI_SRAT_SERVICES_PROTOCOL
 *----------------------------------------------------------------------------------------
 */

/**
 * This service create SRAT Local APIC structure
 *
 * @param[in]      This                             A pointer to the
 *                                                  AMD_CCX_ACPI_SRAT_SERVICES_PROTOCOL instance.
 * @param[in]      SratHeaderStructPtr              SRAT table structure pointer
 * @param[in, out] TableEnd                         Point to the end of this table
 *
 * @retval EFI_SUCCESS                              The LAPIC was successfully created.
 *
 **/
EFI_STATUS
EFIAPI
CcxZen5CreateSratApicEntry (
  IN       AMD_CCX_ACPI_SRAT_SERVICES_PROTOCOL    *This,
  IN       SRAT_HEADER                            *SratHeaderStructPtr,
  IN OUT   UINT8                                 **TableEnd
  )
{
  UINT32                                   ApicId;
  UINTN                                    SocketLoop;
  UINTN                                    DieLoop;
  UINTN                                    CcdLoop;
  UINTN                                    ComplexLoop;
  UINTN                                    CoreLoop;
  UINTN                                    ThreadsLoop;
  UINTN                                    NumberOfSockets;
  UINTN                                    NumberOfDies;
  UINTN                                    NumberOfCcds;
  UINTN                                    NumberOfComplexes;
  UINTN                                    NumberOfCores;
  UINTN                                    NumberOfThreads;
  UINT32                                   Domain;
  SRAT_APIC                               *ApicEntry;
  SRAT_x2APIC                             *X2ApicEntry;
  EFI_STATUS                               CalledStatus;
  AMD_FABRIC_TOPOLOGY_SERVICES2_PROTOCOL  *FabricTopology;
  AMD_CORE_TOPOLOGY_SERVICES_V3_PROTOCOL  *CoreTopologyServices;
  FABRIC_NUMA_SERVICES2_PROTOCOL          *FabricNuma;
  UINT8                                    ApicMode;
  UINTN                                    OrderedLogicalCcd[MAX_CCDS_PER_SKT];
  UINTN                                    CcdNum;

  // Locate Fabric SRAT Services Protocol
  if (gBS->LocateProtocol (&gAmdFabricNumaServices2ProtocolGuid, NULL, (VOID **) &FabricNuma) != EFI_SUCCESS) {
    return EFI_ABORTED;
  }

  // Locate FabricTopologyServices2Protocol
  CalledStatus = gBS->LocateProtocol (&gAmdFabricTopologyServices2ProtocolGuid, NULL, (VOID **) &FabricTopology);
  ASSERT (!EFI_ERROR (CalledStatus));
  if (EFI_ERROR (CalledStatus)) {
    return EFI_ABORTED;
  }

  CalledStatus = gBS->LocateProtocol (&gAmdCoreTopologyServicesV3ProtocolGuid, NULL, (VOID **)&CoreTopologyServices);
  ASSERT (!EFI_ERROR (CalledStatus));
  if (EFI_ERROR (CalledStatus)) {
    return EFI_ABORTED;
  }

  // get Apic Mode
  ApicMode = PcdGet8 (PcdAmdApicMode);

  if (FabricTopology->GetSystemInfo (FabricTopology, &NumberOfSockets, NULL, NULL, NULL, NULL) == EFI_SUCCESS) {
    for (SocketLoop = 0; SocketLoop < NumberOfSockets; SocketLoop++) {
      if (FabricTopology->GetProcessorInfo (FabricTopology, SocketLoop, &NumberOfDies, NULL) == EFI_SUCCESS) {
        for (DieLoop = 0; DieLoop < NumberOfDies; DieLoop++) {
          if (CoreTopologyServices->GetCcdCountOnDie (CoreTopologyServices, SocketLoop, DieLoop, &NumberOfCcds) == EFI_SUCCESS) {
            ASSERT (NumberOfCcds <= MAX_CCDS_PER_SKT);
            CalledStatus = ReOrderLogicalCcdWithNumaDomainOrder (CoreTopologyServices, SocketLoop, DieLoop, NumberOfCcds, OrderedLogicalCcd);
            ASSERT (CalledStatus == EFI_SUCCESS);
            if (EFI_ERROR (CalledStatus)) {
              continue;
            }
            for (CcdLoop = 0; CcdLoop < NumberOfCcds; CcdLoop++) {
              CcdNum = OrderedLogicalCcd [CcdLoop];
              CalledStatus = CoreTopologyServices->GetComplexCountOnCcd (CoreTopologyServices, SocketLoop, DieLoop, CcdNum, &NumberOfComplexes);
              ASSERT (!EFI_ERROR (CalledStatus));
              if (EFI_ERROR (CalledStatus)) {
                NumberOfComplexes = 0;
              }
              for (ComplexLoop = 0; ComplexLoop < NumberOfComplexes; ComplexLoop++) {
                if (FabricNuma->DomainXlat (FabricNuma, SocketLoop, DieLoop, CcdNum, ComplexLoop, &Domain) ==  EFI_SUCCESS) {
                  CalledStatus = CoreTopologyServices->GetCoreCountOnComplex (CoreTopologyServices, SocketLoop, DieLoop, CcdNum, ComplexLoop, &NumberOfCores);
                  ASSERT (!EFI_ERROR (CalledStatus));
                  if (EFI_ERROR (CalledStatus)) {
                    NumberOfCores = 0;
                  }
                  for (CoreLoop = 0; CoreLoop < NumberOfCores; CoreLoop++) {
                    CalledStatus = CoreTopologyServices->GetThreadCountOnCore (CoreTopologyServices, SocketLoop, DieLoop, CcdNum, ComplexLoop, CoreLoop, &NumberOfThreads);
                    ASSERT (!EFI_ERROR (CalledStatus));
                    if (EFI_ERROR (CalledStatus)) {
                      NumberOfThreads = 0;
                    }
                    for (ThreadsLoop = 0; ThreadsLoop < NumberOfThreads; ThreadsLoop++) {
                      ApicId = CcxCalcLocalApic (SocketLoop, DieLoop, CcdNum, ComplexLoop, CoreLoop, ThreadsLoop);
                      if (ApicMode == x2ApicMode) {
                        X2ApicEntry = (SRAT_x2APIC *) *TableEnd;
                        *TableEnd += sizeof (SRAT_x2APIC);

                        X2ApicEntry->Type = SRAT_LOCAL_X2_APIC_TYPE;
                        X2ApicEntry->Length = sizeof (SRAT_x2APIC);
                        X2ApicEntry->ProximityDomain = Domain;
                        X2ApicEntry->x2ApicId = ApicId;
                        X2ApicEntry->Flags.Enabled = 1;
                        X2ApicEntry->ClockDomain = 0;
                      } else if (ApicId < XAPIC_ID_MAX) {
                        ApicEntry = (SRAT_APIC *) *TableEnd;
                        *TableEnd += sizeof (SRAT_APIC);

                        ApicEntry->Type = SRAT_LOCAL_APIC_TYPE;
                        ApicEntry->Length = sizeof (SRAT_APIC);
                        ApicEntry->ProximityDomain_7_0 = (UINT8) Domain & 0xFF;
                        ApicEntry->ProximityDomain_31_8[0] = (UINT8) ((Domain >> 8) & 0xFF);
                        ApicEntry->ProximityDomain_31_8[1] = (UINT8) ((Domain >> 16) & 0xFF);
                        ApicEntry->ProximityDomain_31_8[2] = (UINT8) ((Domain >> 24) & 0xFF);
                        ApicEntry->ApicId = (UINT8) ApicId;
                        ApicEntry->Flags.Enabled = 1;
                        ApicEntry->LocalSapicEid = 0;
                        ApicEntry->ClockDomain = 0;
                      }
                    }
                  }
                }
              }
            }
          }
        }
      }
    }
  }

  return EFI_SUCCESS;
}

/**
 * This service creates a PCC Subspace structure
 *
 * @param[in]      This                             Pointer to AMD_CCX_ACPI_PCCT_SERVICES_PROTCOL instance
 * @param[in]      PcctHeaderStructPtr              Pointer to the PCCT header struct
 * @param[in]      EndOfTable                       Pointer to the end of the PCCT table
 *
 * @retval EFI_SUCCESS                              The structure is successfully created
 *
 **/
EFI_STATUS
EFIAPI
CcxZen5AddGenCommSubspaceStruct (
  IN       AMD_CCX_ACPI_PCCT_SERVICES_PROTOCOL    *This,
  IN       PCCT_HEADER                            *PcctHeaderStructPtr,
  IN       AMD_CPPC_INFO                          *AmdCppcInfo,
  IN OUT   UINT8                                  **EndOfTable
  )
{
  UINT32                               *Pcc;
  UINT32                               MtrrIndex;
  UINT64                               MmioBase;
  UINT64                               MmioLength;
  MTRR_INFO                            MtrrInfo;
  FABRIC_TARGET                        MmioTarget;
  FABRIC_MMIO_ATTRIBUTE                Attrib;
  EFI_STATUS                           Status;
  EFI_PHYSICAL_ADDRESS                 SharedRegion;
  EFI_MP_SERVICES_PROTOCOL             *MpServices;
  DXE_AMD_NBIO_SMU_SERVICES_PROTOCOL   *NbioSmuServices;
  FABRIC_RESOURCE_MANAGER_PROTOCOL     *FabricResourceMgr;
  GEN_COMM_SUBSPACE_STRUCT             *GenCommSubspacePtr;
  DXE_AMD_NBIO_CPPC_SERVICES_PROTOCOL  *NbioCppcServices;
  ALLOCATE_HEAP_PARAMS                 AllocParams;
  UINT32                               RegEax;
  UINT8                                PhysicalAddressBits;
  UINT64                               VariableMtrrMask;

  IDS_HDT_CONSOLE (MAIN_FLOW, "  CcxZen5AddGenCommSubspaceStruct Entry\n");
  Status = gBS->LocateProtocol (&gAmdNbioSmuServicesProtocolGuid, NULL, (VOID **) &NbioSmuServices);
  ASSERT (!EFI_ERROR (Status));
  if (EFI_ERROR (Status)) {
    IDS_HDT_CONSOLE (MAIN_FLOW, "  Returning error, NbioSmuServicesProtocol not found\n");
    return Status;
  }

  Status = gBS->LocateProtocol (&gEfiMpServiceProtocolGuid, NULL, (VOID **)&MpServices);
  ASSERT (!EFI_ERROR (Status));
  if (EFI_ERROR (Status)) {
    IDS_HDT_CONSOLE (MAIN_FLOW, "  Returning error, MpServiceProtocol not found\n");
    return Status;
  }

  Status = gBS->LocateProtocol (&gAmdFabricResourceManagerServicesProtocolGuid, NULL, (VOID **)&FabricResourceMgr);
  ASSERT (!EFI_ERROR (Status));
  if (EFI_ERROR (Status)) {
    IDS_HDT_CONSOLE (MAIN_FLOW, "  Returning error, FabricResourceManagerServicesProtocol not found\n");
    return Status;
  }

  Status = gBS->LocateProtocol (&gAmdNbioCppcServicesProtocolGuid, NULL, (VOID **)&NbioCppcServices);
  ASSERT (!EFI_ERROR (Status));
  if (EFI_ERROR (Status)) {
    IDS_HDT_CONSOLE (MAIN_FLOW, "  Returning error, NbioCppcServicesProtocol not found\n");
    return Status;
  }
  GenCommSubspacePtr = (GEN_COMM_SUBSPACE_STRUCT *) *EndOfTable;
  LibAmdMemCopy ((VOID *) GenCommSubspacePtr, (VOID *) &GenCommSubspaceStruct, (UINTN) (sizeof (GEN_COMM_SUBSPACE_STRUCT)), NULL);

  // Allocate block of physical memory to store CPPC shared memory
  if (gBS->AllocatePages (AllocateAnyPages, EfiACPIMemoryNVS, EFI_SIZE_TO_PAGES (SHARED_MAX_SIZE * 2), &SharedRegion) != EFI_SUCCESS) {
    IDS_HDT_CONSOLE (MAIN_FLOW, "  Returning error, allocate page failed\n");
    return EFI_UNSUPPORTED;
  }

  SharedRegion = ((SharedRegion + (SHARED_MAX_SIZE - 1)) & ~((SHARED_MAX_SIZE) - 1));
  AllocParams.RequestedBufferSize = sizeof (UINT64);
  AllocParams.BufferHandle = AMD_PCCT_SHARED_REGION_ADDRESS_HANDLE;
  AllocParams.Persist = HEAP_SYSTEM_MEM;
  if (HeapAllocateBuffer (&AllocParams, NULL) == AGESA_SUCCESS) {
    *((UINT64 *) AllocParams.BufferPtr) = SharedRegion;
  }

  // Mark shared memory region as UC
  for (MtrrIndex = 0; MtrrIndex < 8; MtrrIndex++) {
    if (((AsmReadMsr64 (AMD_MTRR_VARIABLE_MASK0 + (MtrrIndex << 1))) & BIT11) == 0) {
      break;
    }
  }
  if (MtrrIndex >= 8) {
    IDS_HDT_CONSOLE (MAIN_FLOW, "  Returning unsupported, no var mtrr found\n");
    return EFI_UNSUPPORTED;
  }

  RegEax = 0;
  // Since some programs support 52bits physical address, need to calculate the MtrrMask
  AsmCpuid (CPUID_LONG_MODE_ADDR, &RegEax, NULL, NULL, NULL);
  PhysicalAddressBits = (UINT8)RegEax;
  VariableMtrrMask = LShiftU64 (1, PhysicalAddressBits) - 1;

  MtrrInfo.MtrrIndex = MtrrIndex;
  MtrrInfo.MtrrBase = SharedRegion & VariableMtrrMask;
  MtrrInfo.MtrrMask = (VariableMtrrMask & 0xFFFFFFFFFFFF0000ull) | BIT11;
  MpServices->StartupAllAPs (
                MpServices,
                SetSharedMemoryToUC,
                PcdGetBool (PcdAmdStartupAllAPsSingleThread),
                NULL,
                0,
                (VOID *) &MtrrInfo,
                NULL
                );
  SetSharedMemoryToUC (&MtrrInfo);

  // Set up doorbell for mailbox as MMIO
  MmioTarget.PciBusNum = 0;
  MmioTarget.SocketNum = 0;
  MmioTarget.RbNum = 0;
  MmioTarget.TgtType = 1;
  MmioTarget.PciSegNum = 0;

  Attrib.ReadEnable = 1;
  Attrib.WriteEnable = 1;
  Attrib.NonPosted = 0;
  Attrib.CpuDis = 0;
  Attrib.MmioType = NON_PCI_DEVICE_BELOW_4G;

  MmioLength = ALIGN_1M + 1;

  Status = FabricResourceMgr->FabricAllocateMmio (FabricResourceMgr, &MmioBase, &MmioLength, ALIGN_1M, MmioTarget, &Attrib);
  if (EFI_ERROR (Status)) {
    IDS_HDT_CONSOLE (MAIN_FLOW, "  Returning error, allocate mmio\n");
    return EFI_UNSUPPORTED;
  }

  // Populate signature of Shared Region with 'PCCNull'
  Pcc = (UINT32 *) SharedRegion;
  LibAmdMemFill ((VOID *) Pcc, 0x00, SHARED_MAX_SIZE, NULL);
  *Pcc = 0x50434300;

  // Pass shared region address to SMU FW
  Status = NbioCppcServices->SetTable (SharedRegion);
  if (EFI_ERROR (Status)) {
    return EFI_DEVICE_ERROR;
  }
  ASSERT (AmdCppcInfo != NULL);

  // Populate the entries in the GAS
  GenCommSubspacePtr->RangeBaseAddress = SharedRegion;
  GenCommSubspacePtr->RangeLength = SHARED_MAX_SIZE;
  GenCommSubspacePtr->DoorbellRegister.AddrSpaceId = GAS_SPACE_ID_MEM;
  GenCommSubspacePtr->DoorbellRegister.RegisterBitWidth = 64;
  GenCommSubspacePtr->DoorbellRegister.RegisterBitOffset = 0;
  GenCommSubspacePtr->DoorbellRegister.AccessSize = GAS_ADDR_SIZE_QWORD;
  GenCommSubspacePtr->DoorbellRegister.RegisterAddress = AmdCppcInfo->DoorbellRegister;
  GenCommSubspacePtr->DoorbellPreserve = 0xFFFFFFFF00000000;
  GenCommSubspacePtr->DoorbellWrite = 0x1;
  GenCommSubspacePtr->NominalLatency = AmdCppcInfo->NominalLatency;
  GenCommSubspacePtr->MaxPeriodicAccessRate = AmdCppcInfo->MaxPeriodicAccessRate;
  GenCommSubspacePtr->MinReqTurnaroundTime = (UINT16) AmdCppcInfo->MinReqTurnaroundTime;
  *EndOfTable += sizeof (GEN_COMM_SUBSPACE_STRUCT);

  return EFI_SUCCESS;
}

/**
 * This service creates a PCC Subspace type 4 structure for CoreRankingTable
 *
 * @param[in]      This                             Pointer to AMD_CCX_ACPI_PCCT_SERVICES_PROTCOL instance
 * @param[in]      PcctHeaderStructPtr              Pointer to the PCCT header struct
 * @param[in]      EndOfTable                       Pointer to the end of the PCCT table
 *
 * @retval EFI_SUCCESS                              The structure is successfully created
 *
 **/
EFI_STATUS
EFIAPI
CcxZen5AddCoreRankingTableSlaveSubspaceStruct (
    IN       AMD_CCX_ACPI_PCCT_SERVICES_PROTOCOL    *This,
    IN       PCCT_HEADER                            *PcctHeaderStructPtr,
    IN       AMD_CPPC_INFO                          *AmdCppcInfo,
    IN OUT   UINT8                                  **EndOfTable
  )
{
  UINT32                               i;
  EFI_STATUS                           Status;
  EFI_PHYSICAL_ADDRESS                 SharedRegion;
  EXTEND_PCCT_SUBSPACES_STRUCT         *ExtendPcctCommSubspacePtr;
  DXE_AMD_NBIO_CORE_RANKING_TABLE_SERVICES_PROTOCOL  *NbioCoreRankingServices;
  FEATURE_EXT2_EAX                     Ext2Eax = {0};
  EX_CPU_TOPOLOGY_EAX                  ExCpuTopologyEax = {0};
  HETERO_WL_CLASSIFICATION_EAX         HeteroWorkloadClassificationEax = {0};
  HETERO_DYN_RNK_TBL_EBX               HeteroDynamicRankingTableEbx = {0};
  AMD_CORE_RANKING_TABLE_INFO          CoreRankingTableInfo;
  UINT32                               SharedRegionSize;
  UINT32                               CoreRankingTableSize;
  UINT32                               NumberOfClasses;
  HETERO_DSM_ACPI_VARIABLE_STRUCT      HeteroDsmAcpiVariable;
  PCCT_SUBSPACES_TYPE4_REGISTERS_STRUCT                     *PcctSubspaceType4Registers;
  EXTENDED_PCC_SUBSPACES_SHARED_MEMORY_REGION_HEADER_STRUCT *ExtPccSubspaceSharedMemoryRegion;
  MEMORY_MAPPED_RANKING_TABLE_HEADER_STRUCT                 *MemoryMappedRankingTableHeader;

  IDS_HDT_CONSOLE (MAIN_FLOW, "  CcxZen5AddCoreRankingTableSlaveSubspaceStruct Entry\n");

  AsmCpuid (
      CPUID_EXTENDED_FEATURE_2,
      &Ext2Eax.Value,
      NULL,
      NULL,
      NULL
      );
  if (Ext2Eax.Field.WL_CLASS_SUPPORT == 0) {
    IDS_HDT_CONSOLE (MAIN_FLOW, "  WL_CLASS_SUPPORT is disabled. Skip to generate PCCT for CoreRankingTable\n");
    return EFI_UNSUPPORTED;
  }
  for (i = 0; i < 4; i ++) {
    // Loop each level to check HeterogeneousCoreTopology existence.
    AsmCpuidEx (
        CPUID_EXTENDED_CPU_TOPOLOGY,
        i,
        &ExCpuTopologyEax.Value,
        NULL,
        NULL,
        NULL
        );
    if (ExCpuTopologyEax.Field.HeterogeneousCoreTopology != 0) {
      break;
    }
  }
  if (ExCpuTopologyEax.Field.HeterogeneousCoreTopology == 0) {
    IDS_HDT_CONSOLE (MAIN_FLOW, "  There are no Heterogeneous cores on this system. Skip to generate PCCT for CoreRankingTable\n");
    return EFI_UNSUPPORTED;
  }

  Status = gBS->LocateProtocol (&gAmdNbioCoreRankingTableServicesProtocolGuid, NULL, (VOID **)&NbioCoreRankingServices);
  ASSERT (!EFI_ERROR (Status));
  if (EFI_ERROR (Status)) {
    IDS_HDT_CONSOLE (MAIN_FLOW, "  Returning error, NbioCoreRankingServicesProtocol not found\n");
    return Status;
  }
  Status = NbioCoreRankingServices->CoreRankingTableGetInfo (NbioCoreRankingServices, &CoreRankingTableInfo);
  if (EFI_ERROR (Status)) {
    IDS_HDT_CONSOLE (MAIN_FLOW, "  Returning error, CoreRankingTableGetInfo returns error\n");
    return Status;
  }
  if (!CoreRankingTableInfo.IsEnabled) {
    IDS_HDT_CONSOLE (MAIN_FLOW, "  Returning error, NbioCoreRankingServices does not support CoreRankingTable\n");
    return EFI_UNSUPPORTED;
  }

  ExtendPcctCommSubspacePtr = (EXTEND_PCCT_SUBSPACES_STRUCT *) *EndOfTable;
  LibAmdMemCopy ((VOID *) ExtendPcctCommSubspacePtr, (VOID *) &ExtendPcctCommSubspaceTemplateStruct, (UINTN) (sizeof (EXTEND_PCCT_SUBSPACES_STRUCT)), NULL);

  // Allocate block of physical memory to store CoreRankingTable shared memory
  if (gBS->AllocatePages (AllocateAnyPages, EfiACPIMemoryNVS, EFI_SIZE_TO_PAGES (SHARED_MAX_SIZE), &SharedRegion) != EFI_SUCCESS) {
    IDS_HDT_CONSOLE (MAIN_FLOW, "  Returning error, allocate page failed\n");
    return EFI_UNSUPPORTED;
  }
  LibAmdMemFill ((VOID *)(UINTN)SharedRegion, 0x00, SHARED_MAX_SIZE, NULL);
  IDS_HDT_CONSOLE (MAIN_FLOW, "  Core ranking table is at 0x%x_%08x\n", (UINT32)(SharedRegion >> 32), (UINT32)(SharedRegion & 0xFFFFFFFF));

  // The shared memory address and size send to SMU that needs to minus the Initiator Responder Communications Channel Shared Memory Region header
  SharedRegionSize = SHARED_MAX_SIZE - sizeof (EXTENDED_PCC_SUBSPACES_SHARED_MEMORY_REGION_HEADER_STRUCT);
  // put the PCCT subspace type4 registers group at the end of the shared memory region
  PcctSubspaceType4Registers = (PCCT_SUBSPACES_TYPE4_REGISTERS_STRUCT *)(SharedRegion + SHARED_MAX_SIZE - sizeof (PCCT_SUBSPACES_TYPE4_REGISTERS_STRUCT));
  // Pass shared region address (without the header) to SMU FW
  Status = NbioCoreRankingServices->SetTable (SharedRegion + sizeof (EXTENDED_PCC_SUBSPACES_SHARED_MEMORY_REGION_HEADER_STRUCT), SharedRegionSize, PcctSubspaceType4Registers);
  if (EFI_ERROR (Status)) {
    IDS_HDT_CONSOLE (MAIN_FLOW, "  Set ranking table %r \n", Status);
    return EFI_DEVICE_ERROR;
  }
  ASSERT (AmdCppcInfo != NULL);

  // Update the Initiator Responder Communications Channel Shared Memory Region header
  MemoryMappedRankingTableHeader = (MEMORY_MAPPED_RANKING_TABLE_HEADER_STRUCT *)(SharedRegion + sizeof (EXTENDED_PCC_SUBSPACES_SHARED_MEMORY_REGION_HEADER_STRUCT));
  NumberOfClasses = MemoryMappedRankingTableHeader->NumberOfClasses;
  if (NumberOfClasses == 0) {
    AsmCpuid (
        CPUID_HETERO_WORKLOAD_CLASSIFICATION,
        &HeteroWorkloadClassificationEax.Value,
        &HeteroDynamicRankingTableEbx.Value,
        NULL,
        NULL
        );
    NumberOfClasses = (UINT32)HeteroWorkloadClassificationEax.Field.NUM_WORK_CLASS;
  }
  CoreRankingTableSize = sizeof (MEMORY_MAPPED_RANKING_TABLE_HEADER_STRUCT) + \
                         (sizeof (UINT32) * (MemoryMappedRankingTableHeader->NumberOf32bitBitmaps)) + \
                         (sizeof (UINT32) * NumberOfClasses * (MemoryMappedRankingTableHeader->NumberOfRankingDimensions) * MemoryMappedRankingTableHeader->NumberOfLogicalProcessors) + \
                         (sizeof (UINT8) * NumberOfClasses * (MemoryMappedRankingTableHeader->NumberOfRankingDimensions));
  ExtPccSubspaceSharedMemoryRegion = (EXTENDED_PCC_SUBSPACES_SHARED_MEMORY_REGION_HEADER_STRUCT *)SharedRegion;
  ExtPccSubspaceSharedMemoryRegion->Signature = 0x50434300;
  ExtPccSubspaceSharedMemoryRegion->Length = CoreRankingTableSize + sizeof (ExtPccSubspaceSharedMemoryRegion->Command);

  // Populate dynamic entries
  ExtendPcctCommSubspacePtr->BaseAddress = SharedRegion;
  ExtendPcctCommSubspacePtr->MemoryLength = CoreRankingTableSize + sizeof (EXTENDED_PCC_SUBSPACES_SHARED_MEMORY_REGION_HEADER_STRUCT);
  ExtendPcctCommSubspacePtr->PlatformInterrupt = PcdGet32 (PcdAmdCoreRankingTableFeedbackInterrupt);
  ExtendPcctCommSubspacePtr->PlatformInterruptFlags = PcdGet8 (PcdAmdCoreRankingTableFeedbackInterruptFlags);

  // The PreserveMask and SetMask case:
  //   DoorbellRegister, PlatformInterruptAckRegister and CommandCompleteUpdateRegister
  // How OS check the mask:
  //   val &= reg->preserve_mask;
  //   val |= reg->set_mask;

  // The StatusMask case:
  //   CommandCompleteCheckRegister and ErrorStatusRegister
  // How OS check the mask:
  //   val &= pchan->cmd_complete.status_mask;
  //   if (!val) return IRQ_NONE;

  // The OSPM driver reads at least 4 Byte once, so we need to set the RegisterBitWidth to 32 or 64.
  // The OSPM driver can access the whole region that we provide, so we put all the GAS registers at the end of shared memory.
  ExtendPcctCommSubspacePtr->PlatformInterruptAckRegister.AddrSpaceId = GAS_SPACE_ID_MEM;
  ExtendPcctCommSubspacePtr->PlatformInterruptAckRegister.RegisterBitWidth = 64;
  ExtendPcctCommSubspacePtr->PlatformInterruptAckRegister.RegisterBitOffset = 0;
  ExtendPcctCommSubspacePtr->PlatformInterruptAckRegister.AccessSize = GAS_ADDR_SIZE_QWORD;
  ExtendPcctCommSubspacePtr->PlatformInterruptAckRegister.RegisterAddress = (UINT64)&(PcctSubspaceType4Registers->PlatformInterruptAckRegister);
  ExtendPcctCommSubspacePtr->PlatformInterruptAckPreserve = 0xFFFFFFFFFFFFFFFE;
  ExtendPcctCommSubspacePtr->PlatformInterruptAckSet = BIT0;

  // We use the same address for the CommandCompleteCheckRegister and CommandCompleteUpdateRegister, if so, the SMU did not need to sync them.
  ExtendPcctCommSubspacePtr->CommandCompleteCheckRegisterAddress.AddrSpaceId = GAS_SPACE_ID_MEM;
  ExtendPcctCommSubspacePtr->CommandCompleteCheckRegisterAddress.RegisterBitWidth = 64;
  ExtendPcctCommSubspacePtr->CommandCompleteCheckRegisterAddress.RegisterBitOffset = 0;
  ExtendPcctCommSubspacePtr->CommandCompleteCheckRegisterAddress.AccessSize = GAS_ADDR_SIZE_QWORD;
  ExtendPcctCommSubspacePtr->CommandCompleteCheckRegisterAddress.RegisterAddress = (UINT64)&(PcctSubspaceType4Registers->CommandCompleteCheckRegister);
  ExtendPcctCommSubspacePtr->CommandCompleteCheckMask = BIT0;
  ExtendPcctCommSubspacePtr->CommandCompleteUpdateRegisterAddress.AddrSpaceId = GAS_SPACE_ID_MEM;
  ExtendPcctCommSubspacePtr->CommandCompleteUpdateRegisterAddress.RegisterBitWidth = 64;
  ExtendPcctCommSubspacePtr->CommandCompleteUpdateRegisterAddress.RegisterBitOffset = 0;
  ExtendPcctCommSubspacePtr->CommandCompleteUpdateRegisterAddress.AccessSize = GAS_ADDR_SIZE_QWORD;
  ExtendPcctCommSubspacePtr->CommandCompleteUpdateRegisterAddress.RegisterAddress = (UINT64)&(PcctSubspaceType4Registers->CommandCompleteCheckRegister);
  ExtendPcctCommSubspacePtr->CommandCompleteUpdatePreserveMask = 0xFFFFFFFFFFFFFFFE;
  ExtendPcctCommSubspacePtr->CommandCompleteUpdateSetMask = BIT0;

  // The OS did not actually use the DoorbellRegister and ErrorStatusRegister.
  ExtendPcctCommSubspacePtr->DoorbellRegister.AddrSpaceId = GAS_SPACE_ID_MEM;
  ExtendPcctCommSubspacePtr->DoorbellRegister.RegisterBitWidth = 64; // It's better to 32 or 64 bits for OSPM driver
  ExtendPcctCommSubspacePtr->DoorbellRegister.RegisterBitOffset = 0;
  ExtendPcctCommSubspacePtr->DoorbellRegister.AccessSize = GAS_ADDR_SIZE_QWORD;
  ExtendPcctCommSubspacePtr->DoorbellRegister.RegisterAddress = (UINT64)&(PcctSubspaceType4Registers->DoorbellRegister);
  ExtendPcctCommSubspacePtr->DoorbellPreserve = 0xFFFFFFFFFFFFFFFE;
  ExtendPcctCommSubspacePtr->DoorbellWrite = BIT0;
  ExtendPcctCommSubspacePtr->NominalLatency = AmdCppcInfo->NominalLatency;
  ExtendPcctCommSubspacePtr->MaxPeriodicAccessRate = AmdCppcInfo->MaxPeriodicAccessRate;
  ExtendPcctCommSubspacePtr->MinReqTurnaroundTime = (UINT16) AmdCppcInfo->MinReqTurnaroundTime;

  ExtendPcctCommSubspacePtr->ErrorStatusRegister.AddrSpaceId = GAS_SPACE_ID_MEM;
  ExtendPcctCommSubspacePtr->ErrorStatusRegister.RegisterBitWidth = 64;
  ExtendPcctCommSubspacePtr->ErrorStatusRegister.RegisterBitOffset = 0;
  ExtendPcctCommSubspacePtr->ErrorStatusRegister.AccessSize = GAS_ADDR_SIZE_QWORD;
  ExtendPcctCommSubspacePtr->ErrorStatusRegister.RegisterAddress = (UINT64)&(PcctSubspaceType4Registers->ErrorStatusRegister);
  ExtendPcctCommSubspacePtr->ErrorStatusMask = BIT0;

  // Enable interrupt if it is capble
  if (ExtendPcctCommSubspacePtr->PlatformInterrupt != 0) {
    PcctHeaderStructPtr->Flags |= 0x01;
  }

  *EndOfTable += sizeof (EXTEND_PCCT_SUBSPACES_STRUCT);

  HeteroDsmAcpiVariable.DynamicRankingTableSupport = (UINT8)HeteroDynamicRankingTableEbx.Field.DYN_RNK_TBL;
  HeteroDsmAcpiVariable.SharedMemoryRegionSubspaceId = (ExtPccSubspaceSharedMemoryRegion->Signature & 0xFF);
  HeteroDsmAcpiVariable.SharedMemoryRegionLength = ExtPccSubspaceSharedMemoryRegion->Length;

  UpdateHeteroSsdt (&HeteroDsmAcpiVariable);

  return EFI_SUCCESS;
}

/*---------------------------------------------------------------------------------------*/
/**
* This function will add one CRAT entry.
*
*    @param[in]      CratEntryType        CRAT entry type
*    @param[in]      CratHeaderStructPtr  CRAT header pointer
*    @param[in, out] TableEnd             The end of CRAT
*
*    @returns        Pointer to the added entry
*/
UINT8 *
AddOneCratEntry (
  IN       CRAT_ENTRY_TYPE    CratEntryType,
  IN       CRAT_HEADER       *CratHeaderStructPtr,
  IN OUT   UINT8            **TableEnd
  )
{
  UINT8 *CurrentEntry;

  ASSERT (CratEntryType < CRAT_MAX_TYPE);

  CurrentEntry = *TableEnd;
  CratHeaderStructPtr->TotalEntries++;
  switch (CratEntryType) {
  case CRAT_HSA_PROC_UNIT_TYPE:
    *TableEnd += sizeof (CRAT_HSA_PROCESSING_UNIT);
    ((CRAT_HSA_PROCESSING_UNIT *) CurrentEntry)->Type = (UINT8) CratEntryType;
    ((CRAT_HSA_PROCESSING_UNIT *) CurrentEntry)->Length = sizeof (CRAT_HSA_PROCESSING_UNIT);
    CratHeaderStructPtr->NumNodes++;
    break;
  case CRAT_CACHE_TYPE:
    *TableEnd += sizeof (CRAT_CACHE);
    ((CRAT_CACHE *) CurrentEntry)->Type = (UINT8) CratEntryType;
    ((CRAT_CACHE *) CurrentEntry)->Length = sizeof (CRAT_CACHE);
    break;
  case CRAT_TLB_TYPE:
    *TableEnd += sizeof (CRAT_TLB);
    ((CRAT_TLB *) CurrentEntry)->Type = (UINT8) CratEntryType;
    ((CRAT_TLB *) CurrentEntry)->Length = sizeof (CRAT_TLB);
    break;
  case CRAT_FPU_TYPE:
    *TableEnd += sizeof (CRAT_FPU);
    ((CRAT_FPU *) CurrentEntry)->Type = (UINT8) CratEntryType;
    ((CRAT_FPU *) CurrentEntry)->Length = sizeof (CRAT_FPU);
    break;
  default:
    ASSERT (FALSE);
    break;
  }
  return CurrentEntry;
}

/*---------------------------------------------------------------------------------------*/
/**
* Return associativity
*
*    @param[in]      RawAssoc          Data which is got from CPUID
*
*    @returns        The actual associativity based on the encoded input
*
*/
UINT8
GetCacheAssoc (
  IN       UINT16   RawAssoc
  )
{
  UINT8 Associativity;

  Associativity = 0;

  switch (RawAssoc) {
  case 0:
  case 1:
  case 2:
  case 3:
  case 4:
    Associativity = (UINT8) RawAssoc;
    break;
  case 5:
    Associativity = 6;
    break;
  case 6:
    Associativity = 8;
    break;
  case 8:
    Associativity = 16;
    break;
  case 0xA:
    Associativity = 32;
    break;
  case 0xB:
    Associativity = 48;
    break;
  case 0xC:
    Associativity = 64;
    break;
  case 0xD:
    Associativity = 96;
    break;
  case 0xE:
    Associativity = 128;
    break;
  case 0xF:
    Associativity = 0xFF;
    break;
  default:
    ASSERT (FALSE);
    break;
  }

  return Associativity;
}

/*---------------------------------------------------------------------------------------*/
/**
* Return associativity
*
*    @param[in]      TLB_TYPE          2M4M, 4K or 1G
*    @param[in]      CratTlbEntry      Crat TLB entry
*    @param[in]      RawAssocSize      Value which is got from CPUID
*
*    @returns        TLB size
*
*/
UINT8
GetTlbSize (
  IN       TLB_TYPE   TLB_TYPE,
  IN       CRAT_TLB  *CratTlbEntry,
  IN       UINT16     RawAssocSize
  )
{
  UINT8 TlbSize;

  if (RawAssocSize >= 256) {
    TlbSize = (UINT8) (RawAssocSize / 256);
    if (TLB_TYPE == TLB_2M) {
      CratTlbEntry->Flags.TLB2MBase256 = 1;
    }

    if (TLB_TYPE == TLB_4K) {
      CratTlbEntry->Flags.TLB4KBase256 = 1;
    }

    if (TLB_TYPE == TLB_1G) {
      CratTlbEntry->Flags.TLB1GBase256 = 1;
    }
  } else {
    TlbSize = (UINT8) (RawAssocSize);
  }

  return TlbSize;
}

VOID
EFIAPI
SetSharedMemoryToUC (
  IN       VOID  *Buffer
  )
{
  MTRR_INFO  *MtrrInfo;

  MtrrInfo = (MTRR_INFO *)Buffer;
  AsmWriteMsr64 (((MtrrInfo->MtrrIndex << 1) + AMD_MTRR_VARIABLE_BASE0), MtrrInfo->MtrrBase);
  AsmWriteMsr64 (((MtrrInfo->MtrrIndex << 1) + AMD_MTRR_VARIABLE_MASK0), MtrrInfo->MtrrMask);
  AsmWbinvd ();
}

/**
 * This service returns information on the CPPC Register Descriptors MSR
 *
 * @param[in]      This                 Pointer to AMD_CCX_ACPI_CPPC_SERVICES_PROTCOL instance
 * @param[out]     CppcRegDescMsr       CPPC Register Descriptors MSR
 *
 * @retval EFI_SUCCESS                  Always succeeds
 *
 **/
EFI_STATUS
EFIAPI
CcxZen5GetRegisterDescriptorsMsr (
  IN       AMD_CCX_ACPI_CPPC_SERVICES_PROTOCOL    *This,
     OUT   CPPC_ENTRIES_COMMON_TABLE              **CppcRegDescMsr
  )
{
  *CppcRegDescMsr = (CPPC_ENTRIES_COMMON_TABLE *) &Zen5CppcRegisterDescriptorsMsr;

  return EFI_SUCCESS;
}

/**
  Attach the Hetero information under the scope \_SB into the Hetero SSDT,
  it's for the OSPM driver to check the information through the _DSM method.

  @param[in]        HeteroDsmAcpiVariable - All the Hetero information _DSM method needed
**/
VOID
UpdateHeteroSsdt (
  IN     HETERO_DSM_ACPI_VARIABLE_STRUCT *HeteroDsmAcpiVariable
  )
{
  EFI_STATUS                  Status;
  LIST_ENTRY                  *ListHead;
  LIST_ENTRY                  AmlListHead;
  AML_OBJECT_INSTANCE         *MainObject;
  LIST_ENTRY                  *Node;
  EFI_ACPI_SDT_HEADER         *Table;
  EFI_ACPI_TABLE_VERSION      Version;
  UINTN                       TableKey;
  CHAR8                       *HeaderOemTableId;
  UINT64                      OemTableId;

  IDS_HDT_CONSOLE (MAIN_FLOW, "%a: Entry\n", __FUNCTION__);
  IDS_HDT_CONSOLE (MAIN_FLOW, "%a: SharedMemoryRegionSubspaceId = %x.\n", __FUNCTION__, HeteroDsmAcpiVariable->SharedMemoryRegionSubspaceId);
  IDS_HDT_CONSOLE (MAIN_FLOW, "%a: SharedMemoryRegionLength     = %x.\n", __FUNCTION__, HeteroDsmAcpiVariable->SharedMemoryRegionLength);
  IDS_HDT_CONSOLE (MAIN_FLOW, "%a: DynamicRankingTableSupport   = %x.\n", __FUNCTION__, HeteroDsmAcpiVariable->DynamicRankingTableSupport);
  IDS_HDT_CONSOLE (MAIN_FLOW, "%a: pHeteroDsmAcpiVariable = %x.\n", __FUNCTION__, HeteroDsmAcpiVariable);

  OemTableId = 0;
  HeaderOemTableId = "Hetero";
  LibAmdMemCopy ((VOID *)&OemTableId, (VOID *) HeaderOemTableId, AsciiStrnLenS ((CHAR8 *)HeaderOemTableId, 8), NULL);

  Status = GetExistingAcpiTable (
             EFI_ACPI_6_3_SECONDARY_SYSTEM_DESCRIPTION_TABLE_SIGNATURE,
             OemTableId,
             &Table, &Version, &TableKey
             );
  if (EFI_ERROR (Status)) {
    IDS_HDT_CONSOLE (MAIN_FLOW, "%a: ERROR: Unable to locate Hetero SSDT, Status=%r.\n", __FUNCTION__, Status);
    ASSERT (FALSE);
    return;
  }

  ListHead = &AmlListHead;
  InitializeListHead (ListHead);

  Status = AmlScope(AmlStart, "\\_SB", ListHead); // START: Scope (\_SB.MP1)
  Status |= AmlName (AmlStart, "HDYT", ListHead); // START: Name (HDYT, Object)
  Status |= AmlOPDataInteger (HeteroDsmAcpiVariable->DynamicRankingTableSupport, ListHead); // Object
  Status |= AmlName (AmlClose, "HDYT", ListHead); // CLOSE: Name (HDYT, Object)
  Status |= AmlName (AmlStart, "HSID", ListHead); // START: Name (HSID, Object)
  Status |= AmlOPDataInteger (HeteroDsmAcpiVariable->SharedMemoryRegionSubspaceId, ListHead); // Object
  Status |= AmlName (AmlClose, "HSID", ListHead); // CLOSE: Name (HSID, Object)
  Status |= AmlName (AmlStart, "HSLN", ListHead); // START: Name (HSLN, Object)
  Status |= AmlOPDataInteger (HeteroDsmAcpiVariable->SharedMemoryRegionLength, ListHead); // Object
  Status |= AmlName (AmlClose, "HSLN", ListHead); // CLOSE: Name (HSLN, Object)
  Status |= AmlScope(AmlClose, "\\_SB", ListHead); // CLOSE: Scope (\_SB.MP1)

  if (EFI_ERROR (Status)) {
    IDS_HDT_CONSOLE (MAIN_FLOW, "%a: ERROR Return Status failure\n", __FUNCTION__);
    ASSERT (FALSE);
    AmlFreeObjectList (ListHead);
    return;
  }

  if (!EFI_ERROR (Status)) {
    AmlDebugPrintLinkedObjects (ListHead);
  }

  // Get Main Object from List
  Node = GetFirstNode(ListHead);
  MainObject = AML_OBJECT_INSTANCE_FROM_LINK(Node);

  Status = PrependExistingAcpiTable (
             Table,
             TableKey,
             MainObject->Data,
             MainObject->DataSize
             );
  if (EFI_ERROR (Status)) {
    IDS_HDT_CONSOLE (MAIN_FLOW, "%a: ERROR Append to Hetero SSDT Status=%r\n", __FUNCTION__, Status);
  }

  AmlFreeObjectList (ListHead);
}

