/*****************************************************************************
 *
 * Copyright (C) 2008-2024 Advanced Micro Devices, Inc. All rights reserved.
 *
 *******************************************************************************
 */

#if FixedPcdGetBool(PcdFchI3CDxeDebugOff) == 1 && defined(EFIx64)
 #if !defined(MDEPKG_NDEBUG)
 #define MDEPKG_NDEBUG
 #endif
#endif

/*----------------------------------------------------------------------------------------
 *                             M O D U L E S    U S E D
 *----------------------------------------------------------------------------------------
 */

#include <DwI3cRegs.h>
#include <Library/DebugLib.h>
#include <Library/FchBaseLib.h>
#include <Library/TimerLib.h>
#include <Library/AmdBaseLib.h>
#include <Library/FchI3cLib.h>
#include <Library/BaseMemoryLib.h>
/*----------------------------------------------------------------------------------------
 *                   D E F I N I T I O N S    A N D    M A C R O S
 *----------------------------------------------------------------------------------------
 */
#define FILECODE LIBRARY_FCHI3CLIB_FCHI3CLIB_FILECODE

#define I3C_TRANSFER_CMD_PRESENT        (0x01)
#define I3C_TRANSFER_CMD_TYPE_READ      (0x01)
#define I3C_TRANSFER_CMD_TYPE_WRITE     (0x00)
#define I3C_TRANSFER_CMD_NOT_PRESENT    (0x00)
#define I3C_TRANSFER_CMD_FIFO_SDAP      (0x00)
#define I3C_TRANSFER_CMD_QUEUE_SDAP     (0x01)
#define DEVICE_INDEX_NOT_FOUND          (0xFF)
#define TERMINATE_ON_COMPLETION         (0x01)

#define I3C_TRANSFER_TIMEOUT_US         FixedPcdGet16(PcdFchI3cTimeoutUs)
#define I3C_TRANSFER_TIMEOUT_RETRIES    FixedPcdGet16(PcdFchI3cTimeoutRetry)

// Target frequency(f) = 1/(5ns *(HCNT + LCNT))
// LCNT= roundup((1/f - HCNT*5)/5ns) ==> LCNT= roundup (( T(ns) - HCNT*5)/5ns) ==> LCNT = (roundup(T(ns) / 5ns) - HCNT)
// For SDR2/f=6MHZ, T(ns) = 1000/6. Hence, LCNT = 34 - HCNT
#define CONSTANT_LCNT_CALCULATION_SDR2  34

/*----------------------------------------------------------------------------------------
 *                  T Y P E D E F S     A N D     S T R U C T U R E S
 *----------------------------------------------------------------------------------------
 */


/*----------------------------------------------------------------------------------------
 *           P R O T O T Y P E S     O F     L O C A L     F U N C T I O N S
 *----------------------------------------------------------------------------------------
 */


/*----------------------------------------------------------------------------------------
 *                          E X P O R T E D    F U N C T I O N S
 *----------------------------------------------------------------------------------------
 */

STATIC UINT8 gTransactionId = 0x00;

STATIC
BOOLEAN
HasOddParity (
  IN       UINTN Num
  )
{
  BOOLEAN Parity = 0;
  while (Num) {
    Parity = !Parity;
    Num = Num & (Num - 1);
  }
  return Parity;
}

#if 0
STATIC
VOID
I3cPrintDebugInfo (
  IN       UINT32             Base,
  IN       I3cRegisterRead32  I3cRegRead32,
  IN       I3cRegisterWrite32 I3cRegWrite32
  )
{
  UINT32 Value;

  Value = I3cRegRead32 (Base + DW_DEVICE_CTRL);
  DEBUG ((EFI_D_ERROR, "[Debug] DW_DEVICE_CTRL: 0x%x.\n", Value));

  Value = I3cRegRead32 (Base + DW_INTR_STATUS);
  DEBUG ((EFI_D_ERROR, "[Debug] DW_INTR_STATUS: 0x%x.\n", Value));

  Value = I3cRegRead32 (Base + DW_PRESENT_STATE );
  DEBUG ((EFI_D_ERROR, "[Debug] DW_PRESENT_STATE: 0x%x.\n", Value));

  Value = I3cRegRead32 (Base + DW_DATA_BUFFER_STATUS_LEVEL);
  DEBUG ((EFI_D_ERROR, "[Debug] DW_DATA_BUFFER_STATUS_LEVEL: 0x%x.\n", Value));

  Value = I3cRegRead32 (Base + DW_QUEUE_STATUS_LEVEL );
  DEBUG ((EFI_D_ERROR, "[Debug] DW_QUEUE_STATUS_LEVEL: 0x%x.\n", Value));
}
#endif

STATIC
VOID
I3cEmptyResponseQueue (
  IN       UINT32             Base,
  IN       I3cRegisterRead32  I3cRegRead32,
  IN       I3cRegisterWrite32 I3cRegWrite32
  )
{
  UINT32 Resp;
  UINT32 Value;
  UINT32 RespCnt;

  // Read all the available responses in response queue
  RespCnt = ( I3cRegRead32 (Base + DW_QUEUE_STATUS_LEVEL) & 0x0000FF00 ) >> 8;
  while ( RespCnt > 0 ) {
    Resp = I3cRegRead32 (Base + DW_RESPONSE_QUEUE_PORT);
    Value = ( Resp & ERR_STS_MASK ) >> 28;
    if ( Value != 0x00 ) {
      DEBUG ((EFI_D_ERROR, "[Error] I3cLib.c Response has error (DW_RESPONSE_QUEUE_PORT: 0x%x).\n", Resp));
    }
    RespCnt = ( I3cRegRead32 (Base + DW_QUEUE_STATUS_LEVEL) & 0x0000FF00 ) >> 8;
  }
}

STATIC
EFI_STATUS
I3cClearHaltState (
  IN       UINT32             Base,
  IN       I3cRegisterRead32  I3cRegRead32,
  IN       I3cRegisterWrite32 I3cRegWrite32
  )
{
  UINT32 Value;
  INT32  Count = I3C_TRANSFER_TIMEOUT_RETRIES;

  Value = I3cRegRead32 (Base + DW_PRESENT_STATE);
  if ( ( Value & CM_TFR_STS_HALT_STATE ) == CM_TFR_STS_HALT_STATE ) {
    DEBUG ((EFI_D_ERROR, "[Error] I3cLib.c Device is in halt state (DW_PRESENT_STATE: 0x%x).\n", Value));
    // Un-halt the controller
    Value = I3cRegRead32 (Base + DW_DEVICE_CTRL);
    Value |= DWC_RESUME;
    I3cRegWrite32 (Base + DW_DEVICE_CTRL, Value);
    // Wait till the controller is ready
    while ( Count >= 0 ) {
      Value = I3cRegRead32 (Base + DW_PRESENT_STATE);
      if ( ( Value & CM_TFR_STS_HALT_STATE ) == 0x00 ) {
        // We are good
        break;
      }
      MicroSecondDelay (I3C_TRANSFER_TIMEOUT_US);
      Count--;
    }
    if ( Count < 0 ) {
      DEBUG ((EFI_D_ERROR, "[Error] I3cLib.c Device is still in halt state (DW_PRESENT_STATE: 0x%x).\n", Value));
      return EFI_ABORTED;
    }
  }

  return EFI_SUCCESS;
}

STATIC
VOID
I3cBufferInit (
  IN       UINT32             Base,
  IN       I3cRegisterRead32  I3cRegRead32,
  IN       I3cRegisterWrite32 I3cRegWrite32
  )
{
  UINT32 Value;

  Value = I3cRegRead32 (Base + DW_QUEUE_SIZE_CAPABILITY);
  I3cRegWrite32 (Base + DW_QUEUE_SIZE_CAPABILITY, ( Value & 0xFFFFFFF0 ) | ( 0x05 << 0  ));
  I3cRegWrite32 (Base + DW_QUEUE_SIZE_CAPABILITY, ( Value & 0xFFFFFF0F ) | ( 0x05 << 4  ));
  I3cRegWrite32 (Base + DW_QUEUE_SIZE_CAPABILITY, ( Value & 0xFFFFF0FF ) | ( 0x03 << 8  ));
  I3cRegWrite32 (Base + DW_QUEUE_SIZE_CAPABILITY, ( Value & 0xFFFF0FFF ) | ( 0x02 << 12 ));
  I3cRegWrite32 (Base + DW_QUEUE_SIZE_CAPABILITY, ( Value & 0xFFF0FFFF ) | ( 0x03 << 16 ));

  Value = I3cRegRead32 (Base + DW_QUEUE_THLD_CTRL);
  I3cRegWrite32 (Base + DW_QUEUE_THLD_CTRL, Value & 0x00FF0000);

  Value = I3cRegRead32 (Base + DW_DATA_BUFFER_THLD_CTRL);
  I3cRegWrite32 (Base + DW_DATA_BUFFER_THLD_CTRL, ( Value & 0xF8F8F8F8 ));

  Value = I3cRegRead32 (Base + DW_QUEUE_SIZE_CAPABILITY);
  // DEBUG ((EFI_D_ERROR, "[Debug] FchI3cLib.c TX_BUF_SIZE: %u.\n",   (Value & 0x0000000F) >> 0));
  // DEBUG ((EFI_D_ERROR, "[Debug] FchI3cLib.c RX_BUF_SIZE: %u.\n",   (Value & 0x000000F0) >> 4));
  // DEBUG ((EFI_D_ERROR, "[Debug] FchI3cLib.c CMD_BUF_SIZE: %u.\n",  (Value & 0x00000F00) >> 8));
  // DEBUG ((EFI_D_ERROR, "[Debug] FchI3cLib.c RESP_BUF_SIZE: %u.\n", (Value & 0x0000F000) >> 12));
  // DEBUG ((EFI_D_ERROR, "[Debug] FchI3cLib.c IBI_BUF_SIZE: %u.\n",  (Value & 0x000F0000) >> 16));

  Value = I3cRegRead32 (Base + DW_QUEUE_THLD_CTRL);
  // DEBUG ((EFI_D_ERROR, "[Debug] FchI3cLib.c CMD_EMPTY_BUF_THLD: %u.\n", (Value & 0x000000FF) >> 0));
  // DEBUG ((EFI_D_ERROR, "[Debug] FchI3cLib.c RESP_BUF_THLD: %u.\n",      (Value & 0x0000FF00) >> 8));
  // DEBUG ((EFI_D_ERROR, "[Debug] FchI3cLib.c IBI_STATUS_THLD: %u.\n",    (Value & 0xFF000000) >> 24));

  Value = I3cRegRead32 (Base + DW_DATA_BUFFER_THLD_CTRL);
  // DEBUG ((EFI_D_ERROR, "[Debug] FchI3cLib.c TX_EMPTY_BUF_THLD: %u.\n", (Value & 0x00000007) >> 0));
  // DEBUG ((EFI_D_ERROR, "[Debug] FchI3cLib.c RX_BUF_THLD: %u.\n",       (Value & 0x00000700) >> 8));
  // DEBUG ((EFI_D_ERROR, "[Debug] FchI3cLib.c TX_START_THLD: %u.\n",     (Value & 0x00070000) >> 16));
  // DEBUG ((EFI_D_ERROR, "[Debug] FchI3cLib.c RX_START_THLD: %u.\n",     (Value & 0x07000000) >> 24));
}

STATIC
UINT32
I3cWaitDeviceDisabled (
  IN       UINT32            Base,
  IN       I3cRegisterRead32 I3cRegRead32
  )
{
  UINT32 Value;
  INT32  Count = I3C_TRANSFER_TIMEOUT_RETRIES;
  while ( Count >= 0 ) {
    Value = I3cRegRead32 (Base + DW_DEVICE_CTRL);
    if ( ( Value & DWC_MIPI_I3C_CONTROLLER_ENABLE ) == 0x00 ) {
      return 0x00;
    }
    MicroSecondDelay (I3C_TRANSFER_TIMEOUT_US);
    Count--;
  }
  return 0x01;
}

STATIC
UINT32
I3cWaitDeviceResetCompleted (
  IN       UINT32            Base,
  IN       I3cRegisterRead32 I3cRegRead32
  )
{
  UINT32 Value;
  INT32  Count = I3C_TRANSFER_TIMEOUT_RETRIES;
  while ( Count >= 0 ) {
    Value = I3cRegRead32(Base + DW_RESET_CTRL);
    if ( ( Value & SOFT_RST ) == 0x00 ) {
      return 0x00;
    }
    MicroSecondDelay (I3C_TRANSFER_TIMEOUT_US);
    Count--;
  }
  return 0x01;
}

STATIC
UINT32
I3cWaitRecvBufAvailable (
  IN       UINT32            Base,
  IN       I3cRegisterRead32 I3cRegRead32
  )
{
  UINT32 Status = 0;
  INT32  Count = I3C_TRANSFER_TIMEOUT_RETRIES;
  while ( Count >= 0 ) {
    Status = I3cRegRead32 (Base + DW_INTR_STATUS);
    if ( ( Status & RX_THLD_STS ) == RX_THLD_STS ) {
      return 0x00;
    }
    MicroSecondDelay (I3C_TRANSFER_TIMEOUT_US);
    Count--;
  }
  return 0x01;
}

STATIC
UINT32
I3cWaitRespAvailable (
  IN       UINT32            Base,
  IN       I3cRegisterRead32 I3cRegRead32
  )
{
  UINT32 Value = 0x00;
  INT32  Count = I3C_TRANSFER_TIMEOUT_RETRIES;
  while ( Count >= 0 ) {
    // RESP_READY_STS is set when number of entries in
    // response queue is greater or equal to threshold value
    // specified by RESP_BUF_THLD.
    // RESP_BUF_THLD is set to 0x00
    Value = I3cRegRead32 (Base + DW_INTR_STATUS);
    if ( ( Value & RESP_READY_STS ) == RESP_READY_STS ) {
      return 0x00;
    }
    MicroSecondDelay (I3C_TRANSFER_TIMEOUT_US);
    Count--;
  }
  return 0x01;
}

#if 0
STATIC
UINT32
I3cWaitTxBufEmpty (
  IN       UINT32            Base,
  IN       I3cRegisterRead32 I3cRegRead32
  )
{
  UINT32 Value;
  INT32  Count = I3C_TRANSFER_TIMEOUT_RETRIES;
  while ( Count >= 0 ) {
    Value = I3cRegRead32 (Base + DW_INTR_STATUS);
    if ( Value & TX_THLD_STS ) {
      // We are good
      return 0x00;
    }
    MicroSecondDelay (I3C_TRANSFER_TIMEOUT_US);
    Count--;
  }
  return 0x01;
}

STATIC
UINT32
I3cWaitRxBufEmpty (
  IN       UINT32            Base,
  IN       I3cRegisterRead32 I3cRegRead32
  )
{
  UINT32 Value;
  INT32  Count = I3C_TRANSFER_TIMEOUT_RETRIES;
  while ( Count >= 0 ) {
    Value = I3cRegRead32 (Base + DW_INTR_STATUS);
    if ( Value & RX_THLD_STS ) {
      // We are good
      return 0x00;
    }
    MicroSecondDelay (I3C_TRANSFER_TIMEOUT_US);
    Count--;
  }
  return 0x01;
}
#endif

STATIC
EFI_STATUS
CommonRegistersInit (
  IN       UINT32             Base,
  IN       I3cRegisterRead32  I3cRegRead32,
  IN       I3cRegisterWrite32 I3cRegWrite32
  )
{
  EFI_STATUS Status = EFI_SUCCESS;
  UINT32 Value = 0;

  // If I2C devices are present on the bus,
  // we need to set the slave present bit
  Value = I3cRegRead32 (Base + DW_DEVICE_CTRL);

  // TODO: Check this
  // Turn off IBA_INCLUDE
  // Value &= (~IBA_INCLUDE);

  I3cRegWrite32 (Base + DW_DEVICE_CTRL, Value);

  // Set device to Master mode
  Value = I3cRegRead32 (Base + DW_DEVICE_CTRL_EXTENDED);
  I3cRegWrite32 (Base + DW_DEVICE_CTRL_EXTENDED, Value & 0xFFFFFFFC);

  Value = I3cRegRead32 (Base + DW_BUS_FREE_AVAIL_TIMING);
  I3cRegWrite32 (Base + DW_BUS_FREE_AVAIL_TIMING, ( Value & 0xFFFF0000 ) + BUS_FREE_TIME);

  // Set SCL_LOW_MST_TIMEOUT ~ 50ms
  Value = SCL_LOW_MST_TIMEOUT_COUNT;
  I3cRegWrite32 (Base + DW_I3C_SCL_LOW_MST_EXT_TIMEOUT, Value);

  // Enable all Interrupts in DW_INTR_STATUS_EN register
  Value = BUS_RESET_DONE_STS_EN |
    TRANSFER_ERR_STS_EN         |
    TRANSFER_ABORT_STS_EN       |
    RESP_READY_STS_EN           |
    CMD_QUEUE_READY_STS_EN      |
    IBI_THLD_STS_EN             |
    RX_THLD_STS_EN              |
    TX_THLD_STS_EN;
  I3cRegWrite32 (Base + DW_INTR_STATUS_EN, Value);

  // Enable all Interrupts in DW_INTR_SIGNAL_EN register
  Value = BUS_RESET_DONE_SIGNAL_EN |
    TRANSFER_ERR_SIGNAL_EN         |
    TRANSFER_ABORT_SIGNAL_EN       |
    RESP_READY_SIGNAL_EN           |
    CMD_QUEUE_READY_SIGNAL_EN      |
    IBI_THLD_SIGNAL_EN             |
    RX_THLD_SIGNAL_EN              |
    TX_THLD_SIGNAL_EN;
  I3cRegWrite32 (Base + DW_INTR_SIGNAL_EN, Value);

  return Status;
}

STATIC
EFI_STATUS
MasterRegistersInit (
  IN       UINT32             Base,
  IN       UINT8              Speed,
  IN       UINT8              I3cPpHcnt,
  IN       I3cRegisterRead32  I3cRegRead32,
  IN       I3cRegisterWrite32 I3cRegWrite32
  )
{
  UINT32 Value      = 0x00;
  UINT32 pp_cnt     = 0x00;
  UINT32 I3cExtLcnt = 0x00;
  UINT8  I3cPpLcnt  = AMD_I3C_PP_LCNT;
  UINT32 od_cnt     = (AMD_I3C_OD_LCNT & I3C_OD_LCNT_MASK) + (AMD_I3C_OD_HCNT & I3C_OD_HCNT_MASK);

  // I3C Push Pull Low Count and Ext Lcnt Timing Calculation
  // Target frequency(f) = 1/(5ns *(HCNT + LCNT))
  // Hence, LCNT= roundup((1/f*5ns - HCNT)) ==> LCNT= roundup (( T(ns)/5ns - HCNT))
  // LCNT = (roundup(T(ns)/5ns) - HCNT) where T(ns) = 1000/Freq_in_Mhz , ex: for SDR2 => T(ns) = 1000/6

  I3cExtLcnt = I3cRegRead32 (Base + DW_SCL_EXT_LCNT_TIMING);
  if ( Speed == I3C_MODE_SDR0_SPEED ) {
    //For SDR0,I3C PP HCNT value is limited to be equal to default HCNT value since the duty cycle range(24%-40%) is not applicable for SDR0.
    I3cPpHcnt = AMD_I3C_PP_HCNT;
    I3cPpLcnt = AMD_I3C_PP_LCNT; // Calculated using the Lcnt formula
  } else if ( Speed == I3C_MODE_SDR2_SPEED ) {
    //For SDR2,I3C PP HCNT value is defined such that the duty cycle range remains between 24% to 40%.
    if (I3cPpHcnt < AMD_I3C_PP_HCNT) {
      I3cPpHcnt = AMD_I3C_PP_HCNT;
    } else if (I3cPpHcnt > AMD_I3C_PP_MAX_HCNT_SDR2) {
      I3cPpHcnt = AMD_I3C_PP_MAX_HCNT_SDR2;
    } // Fall through: I3cPpHcnt value taken from the PCD
    I3cPpLcnt = (CONSTANT_LCNT_CALCULATION_SDR2 - I3cPpHcnt); // after simplifying the LCNT equation for 6Mhz
    I3cExtLcnt &= ~(I3C_EXT_LCNT_2_MASK); //clearing I3C_EXT_LCNT2
    I3cExtLcnt |= (((UINT32) I3cPpLcnt << I3C_EXT_LCNT_2_BIT_SHIFT) & I3C_EXT_LCNT_2_MASK); // SDR2 Uses I3C_EXT_LCNT_2 field for the data transfer.
  } else {
    I3cPpLcnt = AMD_I3C_PP_LCNT;
  }

  DEBUG ((EFI_D_ERROR, "[Debug] I3cPpLcnt= %d, I3cPpHcnt= %d ,I3cExtLcnt= %d.\n",I3cPpLcnt, I3cPpHcnt,I3cExtLcnt));

  // Set SCL Extended Low Count Timing Register for I3C transfers.
  I3cRegWrite32 (Base + DW_SCL_EXT_LCNT_TIMING, I3cExtLcnt);

  // Set SCL clock high period and low period count for I3C Push Pull transfers.
  pp_cnt = (I3cPpLcnt & I3C_PP_LCNT_MASK) + ((I3cPpHcnt << AMD_I3C_PP_HCNT_BIT_SHIFT) & I3C_PP_HCNT_MASK);
  I3cRegWrite32 (Base + DW_SCL_I3C_PP_TIMING, pp_cnt);

  // Set SCL clock high period and low period count for I3C Open Drain transfers.
  I3cRegWrite32 (Base + DW_SCL_I3C_OD_TIMING, od_cnt);

  Value = I3cRegRead32 (Base + DW_DEVICE_ADDR);
  I3cRegWrite32 (Base + DW_DEVICE_ADDR, Value | 0x80000000);

  return EFI_SUCCESS;
}

STATIC
EFI_STATUS
I3cGetTransferCmd (
  IN       UINT32                 Cmd,
  IN       UINT32                 Cp,
  IN       UINT32                 DevIndx,
  IN       UINT32                 Speed,
  IN       UINT32                 Sdap,
  IN       UINT32                 RnW,
  IN       UINT32                 Toc,
  IN       UINT32                 Tid,
  IN  OUT  I3C_CMD_DATA_STRUCTURE *TransferCmd
  )
{
  TransferCmd->Value = 0;
  TransferCmd->TransferCmdData.Cp      = Cp;
  TransferCmd->TransferCmdData.Cmd     = Cmd;
  TransferCmd->TransferCmdData.RnW     = RnW;
  TransferCmd->TransferCmdData.Tid     = Tid;
  TransferCmd->TransferCmdData.Toc     = Toc;
  TransferCmd->TransferCmdData.Roc     = 0x01;
  TransferCmd->TransferCmdData.Sdap    = Sdap;
  TransferCmd->TransferCmdData.Speed   = Speed;
  TransferCmd->TransferCmdData.DevIndx = DevIndx;
  TransferCmd->TransferCmdData.CmdAttr = TRANSFER_CMD;
  //DEBUG ((EFI_D_INFO, "I3cLib.c Ic3GetTransferCmd (RnW: 0x%X, Cp: 0x%x, Cmd: 0x%x, DevIndx: 0x%x, Sdap: 0x%x, Toc: 0x%x).\n", RnW, Cp, Cmd, DevIndx, Sdap, Toc));
  return EFI_SUCCESS;
}

STATIC
EFI_STATUS
I3cGetTransferArgCmd (
  IN       UINT32                 DataLengthInBytes,
  IN  OUT  I3C_CMD_DATA_STRUCTURE *TransferCmd
  )
{
  TransferCmd->Value                   = 0x00;
  TransferCmd->TransferArgData.Dl      = DataLengthInBytes;
  TransferCmd->TransferArgData.CmdAttr = TRANSFER_ARG;
  return EFI_SUCCESS;
}

STATIC
EFI_STATUS
I3cGetShortDataCmd (
  IN       UINT8                  *Data,
  IN       UINT32                 DataLengthInBytes,
  IN  OUT  I3C_CMD_DATA_STRUCTURE *TransferCmd
  )
{
  TransferCmd->Value                  = 0;
  TransferCmd->ShortDataArg.CmdAttr   = SHORT_DATA_ARG;
  TransferCmd->ShortDataArg.ByteStrb  = ( 1 << DataLengthInBytes ) - 1;

  if ( DataLengthInBytes == 0x01 ) {
    TransferCmd->ShortDataArg.DataByte0 = Data[0];
  } else if ( DataLengthInBytes == 0x02 ) {
    TransferCmd->ShortDataArg.DataByte0 = Data[0];
    TransferCmd->ShortDataArg.DataByte1 = Data[1];
  } else {
    TransferCmd->ShortDataArg.DataByte0 = Data[0];
    TransferCmd->ShortDataArg.DataByte1 = Data[1];
    TransferCmd->ShortDataArg.DataByte2 = Data[2];
  }

  return EFI_SUCCESS;
}

STATIC
EFI_STATUS
I3cTxBufferWrite (
  IN       UINT32             Base,
  IN       UINT8              *Data,
  IN       UINT32             DataLengthInBytes,
  IN       I3cRegisterRead32  I3cRegRead32,
  IN       I3cRegisterWrite32 I3cRegWrite32
  )
{
  UINT32 Res;
  UINT32 Value;
  UINT32 Index;

  Index = 0x00;
  Res = DataLengthInBytes/4;
  while ( Index < Res ) {
    Value = *((UINT32*)(&Data[Index*4]));
    //DEBUG ((EFI_D_INFO, "I3cLib.c WriteTransferArgCmd Index: %u, Tx: 0x%x.\n", Index, Value));
    I3cRegWrite32 (Base + DW_TX_DATA_PORT, Value);
    Index++;
  }

  if ( DataLengthInBytes & 0x3 ) {
    Value = 0x00;
    LibAmdMemCopy (&Value, &Data[Index*4], DataLengthInBytes & 0x3, (AMD_CONFIG_PARAMS*) NULL);
    //DEBUG ((EFI_D_INFO, "I3cLib.c WriteTransferArgCmd -Index-: %u, Tx: 0x%x.\n", Index, Value));
    I3cRegWrite32 (Base + DW_TX_DATA_PORT, Value);
  }

  return EFI_SUCCESS;
}

STATIC
EFI_STATUS
I3cInitiateTransfer (
  IN       UINT32                 Base,
  IN       I3C_CMD_DATA_STRUCTURE *CmdH,
  IN       I3C_CMD_DATA_STRUCTURE *CmdL,
  IN       I3cRegisterRead32      I3cRegRead32,
  IN       I3cRegisterWrite32     I3cRegWrite32
  )
{
  UINT32 Value;
  EFI_STATUS Status;

  Status = EFI_SUCCESS;

 // DEBUG ((EFI_D_INFO, "[Info] I3cInitiateTransfer start.\n"));
  // I3cPrintDebugInfo (Base, I3cRegRead32, I3cRegWrite32);

  // Issue Private Transfer Write Command
  I3cRegWrite32 (Base + DW_COMMAND_QUEUE_PORT, CmdH->Value);
  I3cRegWrite32 (Base + DW_COMMAND_QUEUE_PORT, CmdL->Value);

  if ( CmdL->TransferCmdData.Toc == 0x00 ) {
    // Not the last command
    return EFI_SUCCESS;
  }

  // Wait till the response queue is updated
  if ( I3cWaitRespAvailable(Base, I3cRegRead32) != 0x00 ) {
    DEBUG ((EFI_D_ERROR, "[Error] I3cInitiateTransfer WaitRespAvailable timeout!\n"));
    // I3cPrintDebugInfo (Base, I3cRegRead32, I3cRegWrite32);
    return EFI_TIMEOUT;
  }

  Value = I3cRegRead32(Base + DW_INTR_STATUS);
  DEBUG ((EFI_D_INFO, "I3cInitiateTransfer DW_INTR_STATUS is: 0x%x.\n", Value));
  if ( ( Value & TRANSFER_ABORT_STS ) == TRANSFER_ABORT_STS ) {
    DEBUG ((EFI_D_ERROR, "[Error] I3cInitiateTransfer DW_INTR_STATUS transfer abort flag is set.\n"));
    Status = EFI_ABORTED;
  } else if ( ( Value & TRANSFER_ERR_STS ) == TRANSFER_ERR_STS ) {
    DEBUG ((EFI_D_ERROR, "[Error] I3cInitiateTransfer DW_INTR_STATUS transfer error flag is set.\n"));
    Status = EFI_ABORTED;
  }

  // Empty response queue
  I3cEmptyResponseQueue (Base, I3cRegRead32, I3cRegWrite32);

  // Clear the error/abort interrupts if there is any
  Value = I3cRegRead32 (Base + DW_INTR_STATUS);
  I3cRegWrite32 (Base + DW_INTR_STATUS, Value | TRANSFER_ABORT_STS | TRANSFER_ERR_STS);

  // Clear halt state if there is any
  I3cClearHaltState (Base, I3cRegRead32, I3cRegWrite32);

  // I3cPrintDebugInfo (Base, I3cRegRead32, I3cRegWrite32);
  return Status;
}

STATIC
EFI_STATUS
I3cGetTableAddrIndex (
  IN       UINT32             Base,
  IN       UINTN              SlaveAddress,
  OUT      UINT32             *DevIndx,
  IN       I3cRegisterRead32  I3cRegRead32,
  IN       I3cRegisterWrite32 I3cRegWrite32
  )
{
  UINT32 Index;
  UINT32 Value;
  UINT32 StaticAddr;
  UINT32 DynamicAddr;
  UINT32 AddrTableNumOfRows;
  UINT32 AddrTableStartAddress;

  if ( (SlaveAddress & 0x80) == 0x80 ) {
    return EFI_INVALID_PARAMETER;
  }

  Value = I3cRegRead32(Base + DW_DEVICE_ADDR_TABLE_POINTER);
  AddrTableNumOfRows = (Value & 0xFFFF0000) >> 16;
  AddrTableStartAddress = Value & 0xFFFF;

  // Look for the first empty spot
  for ( Index=0; Index < AddrTableNumOfRows; Index++ ) {
    Value = I3cRegRead32 (Base + AddrTableStartAddress + 4 * Index);
    StaticAddr = Value & 0x7F;
    DynamicAddr = ( Value >> 16 ) & 0x7F;
    if ( ( DynamicAddr == SlaveAddress ) && ( StaticAddr == SlaveAddress ) ) {
      // This device is already enumerated, we just need to return
      // the device index
      *DevIndx = Index;
      return EFI_SUCCESS;
    }
  }

  *DevIndx = DEVICE_INDEX_NOT_FOUND;

  return EFI_SUCCESS;
}

STATIC
EFI_STATUS
I3cSendSETAASACmd (
  IN       UINT32             Base,
  IN       UINTN              SlaveAddress,
  OUT      UINT32             *DevIndx,
  IN       I3cRegisterRead32  I3cRegRead32,
  IN       I3cRegisterWrite32 I3cRegWrite32
  )
{
  UINT32 Index;
  UINT32 Value;
  UINT32 TableAddr;
  EFI_STATUS Status;
  UINT32 StaticAddr;
  UINT32 DynamicAddr;
  UINT32 AddrTableNumOfRows;
  UINT32 AddrTableStartAddress;
  I3C_CMD_DATA_STRUCTURE TransferCmd;

  Status = EFI_SUCCESS;

  DEBUG ((EFI_D_INFO, "I3cLib.c I3cSendSETAASACmd started.\n"));
  // I3cPrintDebugInfo (Base, I3cRegRead32, I3cRegWrite32);

  Value = I3cRegRead32 (Base + DW_DEVICE_ADDR_TABLE_POINTER);
  AddrTableNumOfRows = (Value & 0xFFFF0000) >> 16;
  AddrTableStartAddress = Value & 0xFFFF;

  for ( Index=0; Index < AddrTableNumOfRows; Index++ ) {
    Value = I3cRegRead32 (Base + AddrTableStartAddress + 4 * Index);
    StaticAddr = Value & 0x7F;
    DynamicAddr = ( Value >> 16 ) & 0x7F;
    // DEBUG ((EFI_D_ERROR, "[Debug] I3cLib.c Index: %u, TableAddr: 0x%x, StaticAddr: 0x%x, DynamicAddr: 0x%x.\n", Index, Value, StaticAddr, DynamicAddr));
    if ( ( StaticAddr == 0x7F ) && ( DynamicAddr == 0x7F ) ) {
      // We have found an empty spot
      break;
    }
  }

  if ( Index == AddrTableNumOfRows ) {
    DEBUG ((EFI_D_INFO, "I3cLib.c I3cSendSETAASA: No empty spot left, use the last\n"));
    Index--;
  }

  TableAddr = (UINT32)(SlaveAddress | ( SlaveAddress << 16 ));
  if ( HasOddParity (SlaveAddress) == FALSE ) {
    TableAddr |= 0x00800000;
  }
  I3cRegWrite32 (Base + AddrTableStartAddress + (UINTN)4 * Index, TableAddr);
  // DEBUG ((EFI_D_ERROR, "[Debug] I3cLib.c I3cSendSETAASA: Index: %u, TableAddr: 0x%x.\n", Index, I3cRegRead32(Base + AddrTableStartAddress + 4 * Index)));

  TransferCmd.TransferCmdData.CmdAttr  = TRANSFER_CMD;
  TransferCmd.TransferCmdData.Cmd      = SETAASA_CCC;
  TransferCmd.TransferCmdData.Cp       = 0x01;
  TransferCmd.TransferCmdData.DevIndx  = Index;
  TransferCmd.TransferCmdData.Speed    = I2C_MODE_I2C_FM_SPEED;
  TransferCmd.TransferCmdData.Roc      = 0x01;
  TransferCmd.TransferCmdData.Toc      = 0x01;
  TransferCmd.TransferCmdData.Sdap     = 0x01;
  TransferCmd.TransferCmdData.RnW      = 0x00;

  I3cRegWrite32 (Base + DW_COMMAND_QUEUE_PORT, TransferCmd.Value);

  // Wait till the response queue is updated
  if ( I3cWaitRespAvailable (Base, I3cRegRead32) != 0x00 ) {
    DEBUG ((EFI_D_ERROR, "[Error] I3cSendSETAASACmd WaitRespAvailable timeout!\n"));
    // I3cPrintDebugInfo (Base, I3cRegRead32, I3cRegWrite32);
    I3cRegWrite32(Base + AddrTableStartAddress + (UINTN)4 * Index, 0xFFFFFFFF);
    return EFI_TIMEOUT;
  }

  Value = I3cRegRead32(Base + DW_INTR_STATUS);
  DEBUG ((EFI_D_INFO, "I3cSendSETAASAcmd DW_INTR_STATUS is: 0x%x.\n", Value));
  if ( ( Value & TRANSFER_ABORT_STS ) == TRANSFER_ABORT_STS ) {
    DEBUG ((EFI_D_ERROR, "[Error] I3cSendSETAASAcmd DW_INTR_STATUS transfer abort flag is set.\n"));
    Status = EFI_ABORTED;
  } else if ( ( Value & TRANSFER_ERR_STS ) == TRANSFER_ERR_STS ) {
    DEBUG ((EFI_D_ERROR, "[Error] I3cSendSETAASAcmd DW_INTR_STATUS transfer abort flag is set.\n"));
    Status = EFI_ABORTED;
  }

  // Empty response queue
  I3cEmptyResponseQueue (Base, I3cRegRead32, I3cRegWrite32);

  // Clear the error/abort interrupts if there is any
  Value = I3cRegRead32 (Base + DW_INTR_STATUS);
  I3cRegWrite32 (Base + DW_INTR_STATUS, Value | TRANSFER_ABORT_STS | TRANSFER_ERR_STS);

  // Clear halt state if there is any
  I3cClearHaltState (Base, I3cRegRead32, I3cRegWrite32);

  // Update the device index and leave
  if ( Status == EFI_SUCCESS ) {
    *DevIndx = Index;
  } else {
    I3cRegWrite32 (Base + AddrTableStartAddress + (UINTN)4 * Index, 0xFFFFFFFF);
  }

  DEBUG ((EFI_D_INFO, "I3cLib.c I3cSendSETAASA ended.\n"));
  return Status;
}


EFI_STATUS
EFIAPI
I3cBroadcastCCCWrite (
  IN       UINT32             Base,
  IN       UINT32             Cmd,
  IN       UINT8              Speed,
  IN       I3cRegisterRead32  I3cRegRead32,
  IN       I3cRegisterWrite32 I3cRegWrite32
  )
{
  I3C_CMD_DATA_STRUCTURE CmdH;
  I3C_CMD_DATA_STRUCTURE CmdL;

  I3cGetTransferArgCmd (0x00, &CmdH);
  I3cGetTransferCmd (
    Cmd,
    I3C_TRANSFER_CMD_PRESENT,
    0x00, // DevIndex
    Speed,
    I3C_TRANSFER_CMD_FIFO_SDAP,
    I3C_TRANSFER_CMD_TYPE_WRITE,
    TERMINATE_ON_COMPLETION,
    ( gTransactionId++ & 0xF ),
    &CmdL
  );

  DEBUG ((EFI_D_INFO, "I3cLib.c I3cBroadcastCCCWrite CmdH: 0x%x, CmdL: 0x%x.\n", CmdH.Value, CmdL.Value));
  return I3cInitiateTransfer (Base, &CmdH, &CmdL, I3cRegRead32, I3cRegWrite32);
}

EFI_STATUS
EFIAPI
I3cPrivateCCCRead (
  IN       UINT32             Base,
  IN       UINT32             Cmd,
  OUT      UINT8              *Data,
  IN       UINT32             DataLenInBytes,
  IN       UINTN              SlaveAddress,
  IN       UINT8              Speed,
  IN       I3cRegisterRead32  I3cRegRead32,
  IN       I3cRegisterWrite32 I3cRegWrite32
  )
{

  UINT32 Res;
  UINT32 Value;
  UINT32 Index;
  UINT32 DevIndx;
  UINT32 RecvCnt;
  UINT32 RecvData;
  UINT32 RxBufSize;
  EFI_STATUS Status;

  I3C_CMD_DATA_STRUCTURE CmdH;
  I3C_CMD_DATA_STRUCTURE CmdL;

  Status = I3cGetTableAddrIndex (Base, SlaveAddress, &DevIndx, I3cRegRead32, I3cRegWrite32);
  if ( Status != EFI_SUCCESS ) {
    DEBUG ((EFI_D_ERROR, "[Error] I3cLib.c I3cGetTableAddrIndex failed.\n"));
    return Status;
  }

  if ( DevIndx == DEVICE_INDEX_NOT_FOUND ) {
    Status = I3cSendSETAASACmd(Base, SlaveAddress, &DevIndx, I3cRegRead32, I3cRegWrite32);
    if ( Status != EFI_SUCCESS ) {
       DEBUG ((EFI_D_ERROR, "[Error] I3cLib.c I3cSendSETAASACmd failed.\n"));
       return Status;
    }
    DEBUG ((EFI_D_INFO, "I3cLib.c Device (Address: 0x%x) was added to the AddressTable (Row: %u).\n", SlaveAddress, DevIndx));
  }

  Value = (I3cRegRead32 (Base + DW_QUEUE_SIZE_CAPABILITY) & 0x000000F0) >> 4;
  RxBufSize = ( 2 << Value ) * sizeof(UINT32);

  if ( DataLenInBytes > RxBufSize ) {
    DEBUG ((EFI_D_ERROR, "[Error] I3cLib.c I3cPrivateRead Response length (%u) exceeds I3c RX buffer size (%u).\n", DataLenInBytes, RxBufSize));
    return EFI_BUFFER_TOO_SMALL;
  }

  I3cGetTransferArgCmd (DataLenInBytes, &CmdH);
  I3cGetTransferCmd (
    Cmd,
    I3C_TRANSFER_CMD_PRESENT,
    DevIndx,
    Speed,
    I3C_TRANSFER_CMD_FIFO_SDAP,
    I3C_TRANSFER_CMD_TYPE_READ,
    0x01, // TOC
    ( gTransactionId++ & 0xF ),
    &CmdL
  );

  DEBUG ((EFI_D_INFO, "I3cLib.c I3cDirectCCCRead CmdH: 0x%x, CmdL: 0x%x.\n", CmdH.Value, CmdL.Value));
  Status = I3cInitiateTransfer (Base, &CmdH, &CmdL, I3cRegRead32, I3cRegWrite32);

  if ( Status != EFI_SUCCESS ) {
    return Status;
  }

  Status = I3cWaitRecvBufAvailable (Base, I3cRegRead32);
  if ( Status != 0x00 ) {
    DEBUG ((EFI_D_ERROR, "[Error] I3cLib.c I3cWaitRecvBufAvailable timeout (DW_INTR_STATUS: 0x%x).\n", I3cRegRead32 (Base + DW_INTR_STATUS)));
    return EFI_TIMEOUT;
  }

  // Read all the data in receive buffer
  Value = ( I3cRegRead32 (Base + DW_DATA_BUFFER_STATUS_LEVEL) & 0x00FF0000 ) >> 16;
  RecvCnt = Value * sizeof(UINT32);

  if ( RecvCnt < DataLenInBytes ) {
    DEBUG ((EFI_D_ERROR, "[Error] I3cLib.c I3cWaitRecvBufAvailable not enough data (RecvCnt: 0x%x, RespLenInBytes: 0x%x).\n", RecvCnt, DataLenInBytes));
    return EFI_TIMEOUT;
  }

  Index = 0x00;
  Res = DataLenInBytes/4;
  while ( Index < Res ) {
    RecvData = I3cRegRead32 (Base + DW_RX_DATA_PORT);
    // DEBUG ((EFI_D_ERROR, "[Debug] I3cLib.c I3cSendReadCmd RecvData (Index: %u, Val: 0x%x).\n", Index, RecvData));
    LibAmdMemCopy (&Data[Index*4], &RecvData, 4, (AMD_CONFIG_PARAMS*) NULL);
    Index++;
  }

  if ( DataLenInBytes & 0x3 ) {
    RecvData = I3cRegRead32 (Base + DW_RX_DATA_PORT);
    // DEBUG ((EFI_D_ERROR, "[Debug] I3cLib.c I3cSendReadCmd RecvData (Index: %u, Val: 0x%x).\n", Index, RecvData));
    LibAmdMemCopy (&Data[Index*4], &RecvData, DataLenInBytes & 0x3, (AMD_CONFIG_PARAMS*) NULL);
  }

  return EFI_SUCCESS;
}


EFI_STATUS
I3cPrivateWriteRead (
  IN       UINT32             Base,
  IN       UINT8              *WriteData,
  IN       UINT32             WriteDataLenInBytes,
  IN       UINT8              *ReadData,
  IN       UINT32             ReadDataLenInBytes,
  IN       UINTN              SlaveAddress,
  IN       UINT8              Speed,
  IN       I3cRegisterRead32  I3cRegRead32,
  IN       I3cRegisterWrite32 I3cRegWrite32
  )
{

  UINT32                  DevIndx;
  EFI_STATUS              Status;
  I3C_CMD_DATA_STRUCTURE  CmdH;
  I3C_CMD_DATA_STRUCTURE  CmdL;
  UINT32                  Value;
  UINT32                  RecvCnt;
  UINT32                  RecvData;
  UINT32                  RxBufSize;
  UINT32                  Index,Res;
  UINT8                   TerminationOnCompletion;


  Status = I3cGetTableAddrIndex (Base, SlaveAddress, &DevIndx, I3cRegRead32, I3cRegWrite32);
  if ( Status != EFI_SUCCESS ) {
    DEBUG ((EFI_D_ERROR, "[Error] I3cLib.c I3cGetTableAddrIndex failed.\n"));
    return Status;
  }

  if ( DevIndx == DEVICE_INDEX_NOT_FOUND ) {
    Status = I3cSendSETAASACmd (Base, SlaveAddress, &DevIndx, I3cRegRead32, I3cRegWrite32);
    if ( Status != EFI_SUCCESS ) {
       DEBUG ((EFI_D_ERROR, "[Error] I3cLib.c I3cSendSETAASACmd failed.\n"));
       return Status;
    }
    DEBUG ((EFI_D_INFO, "I3cLib.c Device (Address: 0x%x, index: 0x%x) was added to the address table.\n", SlaveAddress, DevIndx));
  }

  if ( WriteDataLenInBytes == 0x00 ) {
    return EFI_INVALID_PARAMETER;
  } else if ( WriteDataLenInBytes > ( TX_BUF_SIZE * sizeof(UINT32) ) ) {
    return EFI_INVALID_PARAMETER;
  }
  //Write operation
  ZeroMem (&CmdH, sizeof (I3C_CMD_DATA_STRUCTURE));
  ZeroMem (&CmdL, sizeof (I3C_CMD_DATA_STRUCTURE));
  TerminationOnCompletion = 0;//the next commnad with restart.
  I3cGetTransferArgCmd (WriteDataLenInBytes, &CmdH);
  I3cGetTransferCmd (
    0x00,
    I3C_TRANSFER_CMD_NOT_PRESENT,
    DevIndx,
    Speed,
    I3C_TRANSFER_CMD_FIFO_SDAP,
    I3C_TRANSFER_CMD_TYPE_WRITE,
    TerminationOnCompletion,
    ( gTransactionId++ & 0xF ),
    &CmdL
  );

  Status = I3cInitiateTransfer (Base, &CmdH, &CmdL, I3cRegRead32, I3cRegWrite32);

  //Read Operation
  Value = I3cRegRead32 (Base + DW_QUEUE_SIZE_CAPABILITY);
  RxBufSize = (UINT32)( (Value & 0x000000F0) >> 4 );
  RxBufSize = (UINT32)( ( 2 << RxBufSize ) * 4 );

  if ( ReadDataLenInBytes > RxBufSize  ) {
    DEBUG ((EFI_D_ERROR, "[Error] I3cLib.c I3cPrivateRead Response length (%u) exceeds I3c RX buffer size (%u).\n", ReadDataLenInBytes, RxBufSize ));
    return EFI_BUFFER_TOO_SMALL;
  }
  ZeroMem (&CmdH, sizeof (I3C_CMD_DATA_STRUCTURE));
  ZeroMem (&CmdL, sizeof (I3C_CMD_DATA_STRUCTURE));

  TerminationOnCompletion = 1;//last command
  I3cGetTransferArgCmd ( ReadDataLenInBytes, &CmdH);
  I3cGetTransferCmd (
    0x00,
    I3C_TRANSFER_CMD_NOT_PRESENT,
    DevIndx,
    Speed,
    I3C_TRANSFER_CMD_FIFO_SDAP,
    I3C_TRANSFER_CMD_TYPE_READ,
    TerminationOnCompletion,
    ( gTransactionId++ & 0xF ),
    &CmdL
  );
  I3cRegWrite32 (Base + DW_COMMAND_QUEUE_PORT, CmdH.Value);
  I3cRegWrite32 (Base + DW_COMMAND_QUEUE_PORT, CmdL.Value);
  //Write Data in DW_TX_DATA_PORT
  I3cTxBufferWrite (Base, WriteData, WriteDataLenInBytes, I3cRegRead32, I3cRegWrite32);
   // Wait till the response queue is updated
  if ( I3cWaitRespAvailable(Base, I3cRegRead32) != 0x00 ) {
    DEBUG ((EFI_D_ERROR, "[Error] I3cInitiateTransfer WaitRespAvailable timeout!\n"));
    // I3cPrintDebugInfo (Base, I3cRegRead32, I3cRegWrite32);
    return EFI_TIMEOUT;
  }

  Value = I3cRegRead32(Base + DW_INTR_STATUS);
  DEBUG ((EFI_D_INFO, "I3cInitiateTransfer DW_INTR_STATUS is: 0x%x.\n", Value));
  if ( ( Value & TRANSFER_ABORT_STS ) == TRANSFER_ABORT_STS ) {
    DEBUG ((EFI_D_ERROR, "[Error] I3cInitiateTransfer DW_INTR_STATUS transfer abort flag is set.\n"));
    Status = EFI_ABORTED;
  } else if ( ( Value & TRANSFER_ERR_STS ) == TRANSFER_ERR_STS ) {
    DEBUG ((EFI_D_ERROR, "[Error] I3cInitiateTransfer DW_INTR_STATUS transfer error flag is set.\n"));
    Status = EFI_ABORTED;
  }
  // Empty response queue
  I3cEmptyResponseQueue (Base, I3cRegRead32, I3cRegWrite32);

  // Clear the error/abort interrupts if there is any
  Value = I3cRegRead32 (Base + DW_INTR_STATUS);
  I3cRegWrite32 (Base + DW_INTR_STATUS, Value | TRANSFER_ABORT_STS | TRANSFER_ERR_STS);

  // Clear halt state if there is any
  I3cClearHaltState (Base, I3cRegRead32, I3cRegWrite32);
  if (EFI_ERROR(Status)){
    return Status;
  }
  Status = I3cWaitRecvBufAvailable (Base, I3cRegRead32);
  if ( Status != 0x00 ) {
    DEBUG ((EFI_D_ERROR, "[Error] I3cLib.c I3cWaitRecvBufAvailable timeout (DW_INTR_STATUS: 0x%x).\n", I3cRegRead32 (Base + DW_INTR_STATUS)));
    return EFI_TIMEOUT;
  }

  // Read all the data in receive buffer
  RecvCnt = ( I3cRegRead32(Base + DW_DATA_BUFFER_STATUS_LEVEL) & 0x00FF0000 ) >> 16;
  // DEBUG ((EFI_D_ERROR, "[Debug] I3cLib.c I3cSendReadCmd: RecvCnt: %u.\n", RecvCnt));

  if ( (RecvCnt * 4) < ReadDataLenInBytes ) {
    DEBUG ((EFI_D_ERROR, "[Error] I3cLib.c I3cWaitRecvBufAvailable not enough data (RecvCnt: 0x%x, RespLenInBytes: 0x%x).\n", RecvCnt, ReadDataLenInBytes));
    return EFI_TIMEOUT;
  }

  Index = 0x00;
  Res = ReadDataLenInBytes/4;
  while ( Index < Res ) {
    RecvData = I3cRegRead32 (Base + DW_RX_DATA_PORT);
    // DEBUG ((EFI_D_ERROR, "[Debug] I3cLib.c I3cSendReadCmd RecvData (Index: %u, Val: 0x%x).\n", Index, RecvData));
    LibAmdMemCopy (&ReadData[Index*4], &RecvData, 4, (AMD_CONFIG_PARAMS*) NULL);
    Index++;
  }

  if ( ReadDataLenInBytes & 0x3 ) {
    RecvData = I3cRegRead32 (Base + DW_RX_DATA_PORT);
    // DEBUG ((EFI_D_ERROR, "[Debug] I3cLib.c I3cSendReadCmd RecvData (Index: %u, Val: 0x%x).\n", Index, RecvData));
    LibAmdMemCopy (&ReadData[Index*4], &RecvData, ReadDataLenInBytes & 0x3, (AMD_CONFIG_PARAMS*) NULL);
  }

  return Status;



}

EFI_STATUS
I3cPrivateWrite (
  IN       UINT32             Base,
  IN       UINT8              *Data,
  IN       UINT32             DataLenInBytes,
  IN       UINTN              SlaveAddress,
  IN       UINT8              TerminationOnCompletion,
  IN       UINT8              Speed,
  IN       I3cRegisterRead32  I3cRegRead32,
  IN       I3cRegisterWrite32 I3cRegWrite32
  )
{
  UINT32 Value;
  UINT32 DevIndx;
  UINT32 TxBufSize;
  EFI_STATUS Status;

  Status = I3cGetTableAddrIndex (Base, SlaveAddress, &DevIndx, I3cRegRead32, I3cRegWrite32);
  if ( Status != EFI_SUCCESS ) {
    DEBUG ((EFI_D_ERROR, "[Error] I3cLib.c I3cGetTableAddrIndex failed.\n"));
    return Status;
  }

  if ( DevIndx == DEVICE_INDEX_NOT_FOUND ) {
    Status = I3cSendSETAASACmd (Base, SlaveAddress, &DevIndx, I3cRegRead32, I3cRegWrite32);
    if ( Status != EFI_SUCCESS ) {
       DEBUG ((EFI_D_ERROR, "[Error] I3cLib.c I3cSendSETAASACmd failed.\n"));
       return Status;
    }
    DEBUG ((EFI_D_INFO, "I3cLib.c Device (Address: 0x%x, index: 0x%x) was added to the address table.\n", SlaveAddress, DevIndx));
  }

  Value = I3cRegRead32 (Base + DW_QUEUE_SIZE_CAPABILITY) & 0x0000000F;
  TxBufSize = ( 2 << Value ) * sizeof(UINT32);

  if ( DataLenInBytes == 0x00 ) {
    return EFI_INVALID_PARAMETER;
  } else if ( DataLenInBytes > TxBufSize ) {
    DEBUG ((EFI_D_ERROR, "[Error] I3cLib.c I3cPrivateWrite Transmit data length (%u) exceeds I3c TX buffer size (%u).\n", DataLenInBytes, TxBufSize));
    return EFI_BUFFER_TOO_SMALL;
  } else if ( DataLenInBytes < 0x04 ) {
    I3C_CMD_DATA_STRUCTURE CmdH;
    I3C_CMD_DATA_STRUCTURE CmdL;

    I3cGetShortDataCmd (Data, DataLenInBytes, &CmdH);
    I3cGetTransferCmd (
      0x00,
      I3C_TRANSFER_CMD_NOT_PRESENT,
      DevIndx,
      Speed,
      I3C_TRANSFER_CMD_QUEUE_SDAP,
      I3C_TRANSFER_CMD_TYPE_WRITE,
      TerminationOnCompletion,
      ( gTransactionId++ & 0xF ),
      &CmdL
    );

    DEBUG ((EFI_D_INFO, "I3cLib.c I3cPrivateWrite CmdH: 0x%x, CmdL: 0x%x.\n", CmdH.Value, CmdL.Value));
    Status = I3cInitiateTransfer (Base, &CmdH, &CmdL, I3cRegRead32, I3cRegWrite32);
  } else {
    I3C_CMD_DATA_STRUCTURE CmdH;
    I3C_CMD_DATA_STRUCTURE CmdL;

    I3cTxBufferWrite (Base, Data, DataLenInBytes, I3cRegRead32, I3cRegWrite32);
    I3cGetTransferArgCmd (DataLenInBytes, &CmdH);
    I3cGetTransferCmd (
      0x00,
      I3C_TRANSFER_CMD_NOT_PRESENT,
      DevIndx,
      Speed,
      I3C_TRANSFER_CMD_FIFO_SDAP,
      I3C_TRANSFER_CMD_TYPE_WRITE,
      TerminationOnCompletion,
      ( gTransactionId++ & 0xF ),
      &CmdL
    );

    //DEBUG ((EFI_D_INFO, "I3cLib.c I3cPrivateWrite CmdH: 0x%x, CmdL: 0x%x.\n", CmdH.Value, CmdL.Value));
    Status = I3cInitiateTransfer (Base, &CmdH, &CmdL, I3cRegRead32, I3cRegWrite32);
  }

  return Status;
}

EFI_STATUS
I3cPrivateRead (
  IN       UINT32             Base,
  OUT      UINT8              *Data,
  IN       UINT32             DataLenInBytes,
  IN       UINTN              SlaveAddress,
  IN       UINT8              TerminationOnCompletion,
  IN       UINT8              Speed,
  IN       I3cRegisterRead32  I3cRegRead32,
  IN       I3cRegisterWrite32 I3cRegWrite32
  )
{
  UINT32 Res;
  UINT32 Value;
  UINT32 Index;
  UINT32 DevIndx;
  UINT32 RecvCnt;
  UINT32 RecvData;
  UINT32 RxBufSize;
  EFI_STATUS Status;

  Status = I3cGetTableAddrIndex (Base, SlaveAddress, &DevIndx, I3cRegRead32, I3cRegWrite32);
  if ( Status != EFI_SUCCESS ) {
    DEBUG ((EFI_D_ERROR, "[Error] I3cLib.c I3cGetTableAddrIndex failed.\n"));
    return Status;
  }

  if ( DevIndx == DEVICE_INDEX_NOT_FOUND ) {
    Status = I3cSendSETAASACmd (Base, SlaveAddress, &DevIndx, I3cRegRead32, I3cRegWrite32);
    if ( Status != EFI_SUCCESS ) {
       DEBUG ((EFI_D_ERROR, "[Error] I3cLib.c I3cSendSETAASACmd failed.\n"));
       return Status;
    }
    DEBUG ((EFI_D_INFO, "I3cLib.c Device (Address: 0x%x) was added to the AddressTable (Row: %u).\n", SlaveAddress, DevIndx));
  }

  Value = (I3cRegRead32 (Base + DW_QUEUE_SIZE_CAPABILITY) & 0x000000F0) >> 4;
  RxBufSize = ( 2 << Value ) * sizeof(UINT32);

  if ( DataLenInBytes > RxBufSize  ) {
    DEBUG ((EFI_D_ERROR, "[Error] I3cLib.c I3cPrivateRead Response length (%u) exceeds I3c RX buffer size (%u).\n", DataLenInBytes, RxBufSize));
    return EFI_BUFFER_TOO_SMALL;
  }

  I3C_CMD_DATA_STRUCTURE CmdH;
  I3C_CMD_DATA_STRUCTURE CmdL;

  I3cGetTransferArgCmd (DataLenInBytes, &CmdH);
  I3cGetTransferCmd (
    0x00,
    I3C_TRANSFER_CMD_NOT_PRESENT,
    DevIndx,
    Speed,
    I3C_TRANSFER_CMD_FIFO_SDAP,
    I3C_TRANSFER_CMD_TYPE_READ,
    TerminationOnCompletion,
    ( gTransactionId++ & 0xF ),
    &CmdL
  );

  //DEBUG ((EFI_D_INFO, "I3cLib.c I3cPrivateRead CmdH: 0x%x, CmdL: 0x%x.\n", CmdH.Value, CmdL.Value));
  Status = I3cInitiateTransfer(Base, &CmdH, &CmdL, I3cRegRead32, I3cRegWrite32);

  if ( Status != EFI_SUCCESS ) {
    return Status;
  }

  if ( TerminationOnCompletion == 0x00 ) {
    // Not the last command
    DEBUG ((EFI_D_INFO, "I3cLib.c ToC is set. Skipping I3cWaitRecvBufAvailable...\n"));
    return EFI_SUCCESS;
  }

  Status = I3cWaitRecvBufAvailable (Base, I3cRegRead32);
  if ( Status != 0x00 ) {
    DEBUG ((EFI_D_ERROR, "[Error] I3cLib.c I3cWaitRecvBufAvailable timeout (DW_INTR_STATUS: 0x%x).\n", I3cRegRead32 (Base + DW_INTR_STATUS)));
    return EFI_TIMEOUT;
  }

  // Read all the data in receive buffer
  Value = ( I3cRegRead32(Base + DW_DATA_BUFFER_STATUS_LEVEL) & 0x00FF0000 ) >> 16;
  RecvCnt = Value * sizeof(UINT32);

  if ( RecvCnt < DataLenInBytes ) {
    DEBUG ((EFI_D_ERROR, "[Error] I3cLib.c I3cWaitRecvBufAvailable not enough data (RecvCnt: 0x%x, RespLenInBytes: 0x%x).\n", RecvCnt, DataLenInBytes));
    return EFI_TIMEOUT;
  }

  Index = 0x00;
  Res = DataLenInBytes/4;
  while ( Index < Res ) {
    RecvData = I3cRegRead32 (Base + DW_RX_DATA_PORT);
    // DEBUG ((EFI_D_ERROR, "[Debug] I3cLib.c I3cSendReadCmd RecvData (Index: %u, Val: 0x%x).\n", Index, RecvData));
    LibAmdMemCopy (&Data[Index*4], &RecvData, 4, (AMD_CONFIG_PARAMS*) NULL);
    Index++;
  }

  if ( DataLenInBytes & 0x3 ) {
    RecvData = I3cRegRead32 (Base + DW_RX_DATA_PORT);
    // DEBUG ((EFI_D_ERROR, "[Debug] I3cLib.c I3cSendReadCmd RecvData (Index: %u, Val: 0x%x).\n", Index, RecvData));
    LibAmdMemCopy (&Data[Index*4], &RecvData, DataLenInBytes & 0x3, (AMD_CONFIG_PARAMS*) NULL);
  }

  return EFI_SUCCESS;
}

EFI_STATUS
I3cInit (
  IN       UINT32             Base,
  IN       UINT8              I2cSlavePresent,
  IN       UINT8              Speed,
  IN       UINT8              SdaHoldTime,
  IN       UINT8              PushPullHighCount,
  IN       I3cRegisterRead32  I3cRegRead32,
  IN       I3cRegisterWrite32 I3cRegWrite32
  )
{
  UINT32 Value;

  DEBUG ((EFI_D_INFO, "I3cLib.c I3cInit start.\n"));
  // I3cPrintDebugInfo (Base, I3cRegRead32, I3cRegWrite32);

  // Disable controller
  Value = I3cRegRead32 (Base + DW_DEVICE_CTRL);
  Value &= ~DWC_MIPI_I3C_CONTROLLER_ENABLE;
  I3cRegWrite32 (Base + DW_DEVICE_CTRL, Value);

  // Wait till controller is disabled
  I3cWaitDeviceDisabled (Base, I3cRegRead32);

  // Reset TX/RX/CMD/RESP buffers
  Value = I3cRegRead32 (Base + DW_RESET_CTRL);
  I3cRegWrite32 (Base + DW_RESET_CTRL, Value | SOFT_RST);

  // Wait till the reset is complete
  I3cWaitDeviceResetCompleted (Base, I3cRegRead32);

  // If I2C mode is selected.
  Value = I3cRegRead32 (Base + DW_DEVICE_CTRL);
  if ( I2cSlavePresent == 0x01 ) {
    Value |= I2C_SLAVE_PRESENT;
  } else {
    Value &= (~I2C_SLAVE_PRESENT);
    // enalbe IBA_INCLUDE when I3C mode
    Value |= IBA_INCLUDE;
  }
  I3cRegWrite32 (Base + DW_DEVICE_CTRL, Value);

  // Intialize Common Registers
  CommonRegistersInit (Base, I3cRegRead32, I3cRegWrite32);

  // Master registers initialization
  MasterRegistersInit (Base, Speed, PushPullHighCount, I3cRegRead32, I3cRegWrite32);

  // Set the hold time based on the provided pcd option
  I3cRegWrite32 (Base + DW_SDA_HOLD_DLY_TIMING, SdaHoldTime << 16);

  // Buffer initialization
  I3cBufferInit (Base, I3cRegRead32, I3cRegWrite32);

  // Clear all interrupts
  Value = TRANSFER_ABORT_STS |
    CCC_UPDATED_STS          |
    DYN_ADDR_ASSGN_STS       |
    TRANSFER_ERR_STS         |
    DEFSLV_STS               |
    READ_REQ_RECV_STS        |
    BUSOWNER_UPDATED_STS;
  I3cRegWrite32 (Base + DW_INTR_STATUS, Value);

  // Clear halt state if there is any
  I3cClearHaltState (Base, I3cRegRead32, I3cRegWrite32);

  // Enable I3C controller
  Value = I3cRegRead32 (Base + DW_DEVICE_CTRL);
  Value |= DWC_MIPI_I3C_CONTROLLER_ENABLE;
  I3cRegWrite32 (Base + DW_DEVICE_CTRL, Value);

  // Lets give it 5ms delay
  MicroSecondDelay (I3C_TRANSFER_TIMEOUT_US);

  // I3cPrintDebugInfo (Base, I3cRegRead32, I3cRegWrite32);

  DEBUG ((EFI_D_INFO, "I3cLib.c I3cInit end.\n"));

  return EFI_SUCCESS;
}

EFI_STATUS
I3cSoftReset (
  IN       UINT32             Base,
  IN       I3cRegisterRead32  I3cRegRead32,
  IN       I3cRegisterWrite32 I3cRegWrite32
  )
{
  UINT32 Value;
  INT32  Count = I3C_TRANSFER_TIMEOUT_RETRIES;

  Value = I3cRegRead32 (Base + DW_RESET_CTRL);
  I3cRegWrite32 (Base + DW_RESET_CTRL, Value | SOFT_RST);

  while ( Count >= 0 ) {
    Value = I3cRegRead32(Base + DW_RESET_CTRL);
    if ( ( Value & SOFT_RST ) == 0x00 ) {
      break;
    }
    MicroSecondDelay (I3C_TRANSFER_TIMEOUT_US);
    Count--;
  }

  if ( Count < 0 ) {
     DEBUG ((EFI_D_ERROR, "[Error] I3cLib.c Reset timeout (DW_RESET_CTRL: 0x%x).\n", Value));
     return EFI_TIMEOUT;
  }

  return EFI_SUCCESS;
}

EFI_STATUS
I3cCheckPresentState (
  IN       UINT32             Base,
  IN       I3cRegisterRead32  I3cRegRead32,
  IN       I3cRegisterWrite32 I3cRegWrite32
  )
{
  UINT32       Value;
  INT32        Count = I3C_TRANSFER_TIMEOUT_RETRIES;
  EFI_STATUS   Status = EFI_SUCCESS;

  //check is is halt state or not
  Value = I3cRegRead32 (Base + DW_PRESENT_STATE);
  DEBUG ((EFI_D_INFO, "Check Halt state (DW_PRESENT_STATE: 0x%x).\n", Value));
  if ( ( Value & CM_TFR_STS_HALT_STATE ) == CM_TFR_STS_HALT_STATE ) {
    Status = I3cClearHaltState (Base, I3cRegRead32, I3cRegWrite32 );
  }
  if (EFI_ERROR(Status)){
    DEBUG ((EFI_D_ERROR, "Still in halt state\n"));
    return EFI_ABORTED;
  }
  // Clear the error/abort interrupts if there is any
  Value = I3cRegRead32(Base + DW_INTR_STATUS);
  DEBUG ((EFI_D_INFO, "I3cInitiateTransfer DW_INTR_STATUS is: 0x%x.\n", Value));
  if ( Value & (TRANSFER_ABORT_STS | TRANSFER_ERR_STS) ){
     Value |= (TRANSFER_ABORT_STS | TRANSFER_ERR_STS);
    I3cRegWrite32 (Base + DW_INTR_STATUS, Value);
    Value = I3cRegRead32(Base + DW_INTR_STATUS);
    DEBUG ((EFI_D_ERROR, "interrupts status erorr cls (DW_INTR_STATUS: 0x%x).\n", Value));
    if ( Value & (TRANSFER_ABORT_STS | TRANSFER_ERR_STS) ){
        DEBUG ((EFI_D_ERROR, "interrupts status clear error (DW_INTR_STATUS: 0x%x).\n", Value));
    }
  }

  Value = I3cRegRead32 (Base + DW_PRESENT_STATE);
  DEBUG ((EFI_D_INFO, "MasterIdle (DW_PRESENT_STATE: 0x%x).\n", Value));
  //Check it is in IDLE master
  while ( Count >= 0 ) {
    Value = I3cRegRead32(Base + DW_PRESENT_STATE);
    if ( (SCL_LINE_SIGNAL_LEVEL | SDA_LINE_SIGNAL_LEVEL | CURRENT_MASTER | MASTER_IDLE)
         ==  (Value & (SCL_LINE_SIGNAL_LEVEL | SDA_LINE_SIGNAL_LEVEL | CURRENT_MASTER | MASTER_IDLE)) ) {
      break;
    }
    MicroSecondDelay (I3C_TRANSFER_TIMEOUT_US);
    Count--;
  }

  if ( Count < 0 ) {
     DEBUG ((EFI_D_ERROR, "[Error] I3cLib.c MasterIdle timeout (DW_PRESENT_STATE: 0x%x).\n", Value));
     return EFI_TIMEOUT;
  }

  return EFI_SUCCESS;
}



