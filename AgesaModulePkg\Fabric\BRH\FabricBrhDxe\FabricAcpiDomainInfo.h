/*****************************************************************************
 *
 * Copyright (C) 2019-2024 Advanced Micro Devices, Inc. All rights reserved.
 *
 *****************************************************************************
 */

#ifndef _FABRIC_ACPI_DOMAIN_INFO_H_
#define _FABRIC_ACPI_DOMAIN_INFO_H_

#include <Protocol/AmdAcpiSratServicesProtocol.h>
#include <FabricRegistersBrh.h>
#include <FabricInfoBrh.h>

#pragma pack (push, 1)
#define MAX_NPS            4
#define MAX_CCX_PER_CCD    1
#define MAX_CCD_PER_SOCKET BRH_MAX_CCD_PER_SOCKET

#define NORMALIZED_SOCKET_SHIFT (1 << 4)

#define MAX_REPORTED_DOMAINS ((MAX_CCX_PER_CCD * MAX_CCD_PER_SOCKET * BRH_MAX_SOCKETS) + \
                              (BRH_NUM_CS_CMP_BLOCKS * BRH_MAX_SOCKETS))
#define MAX_PHYSICAL_DOMAINS ((BRH_MAX_SOCKETS * MAX_NPS) + (BRH_NUM_CS_CMP_BLOCKS * BRH_MAX_SOCKETS))

typedef
VOID
F_PUBLISH_ACPI_NUMA_MEM_ENTRY (
  IN OUT UINT8   **TablePointer,
  IN     UINT32  Domain,
  IN     UINT32  RegionBaseLow,
  IN     UINT32  RegionBaseHigh,
  IN     UINT32  RegionSizeLow,
  IN     UINT32  RegionSizeHigh
  );

typedef F_PUBLISH_ACPI_NUMA_MEM_ENTRY *PF_PUBLISH_ACPI_NUMA_MEM_ENTRY;

EFI_STATUS
FabricCreateSystemAcpiDomainData (
  IN OUT UINT8                           **TableEnd,
  IN     PF_PUBLISH_ACPI_NUMA_MEM_ENTRY  PublishMemEntry,
     OUT UINT32                          *NumberOfEntriesPublished
  );

EFI_STATUS
FabricGetMemoryInfo (
  OUT UINT32       *NumberOfDomains,
  OUT MEMORY_INFO  **MemoryInfo
  );

UINT8
FabricGetMaxDomains (
  VOID
  );

#pragma pack (pop)
#endif // _FABRIC_ACPI_DOMAIN_INFO_H_

