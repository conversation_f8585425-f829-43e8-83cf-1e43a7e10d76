#;*****************************************************************************
#;
#; Copyright (C) 2008-2024 Advanced Micro Devices, Inc. All rights reserved.
#;
#;******************************************************************************
[Defines]
  INF_VERSION                    = 0x00010005
  BASE_NAME                      = FchSpiAccessRomArmor2Lib
  FILE_GUID                      = 259A3ECF-D1E6-47F4-8F25-0B190A40454D
  MODULE_TYPE                    = DXE_DRIVER
  VERSION_STRING                 = 1.0
  LIBRARY_CLASS                  = FchSpiAccessLib|DXE_DRIVER DXE_CORE DXE_RUNTIME_DRIVER UEFI_DRIVER SMM_CORE DXE_SMM_DRIVER UEFI_APPLICATION

#
# The following information is for reference only and not required by the build tools.
#
#  VALID_ARCHITECTURES           = IA32 X64
#
#
#  This instance for 32MB and smaller size SPI ROM READ on DXE phase, do not support runtime yet.

[Sources]
  FchSpiAccessCommon.c
  FchSpiAccessRomArmor2Lib.c

[Packages]
  AgesaModulePkg/AgesaModuleFchPkg.dec
  MdePkg/MdePkg.dec
  AgesaPkg/AgesaPkg.dec

[LibraryClasses]
  IoLib
  PciLib
  FchBaseLib
  BaseMemoryLib
  AmdPspFlashAccLib

