#;*****************************************************************************
#;
#; Copyright (C) 2008-2024 Advanced Micro Devices, Inc. All rights reserved.
#;
#;******************************************************************************

[defines]
  INF_VERSION                    = 0x00010005
  BASE_NAME                      = AmdErrorLogDisplayBrhDxe
  FILE_GUID                      = BA0C203A-166B-4B93-9636-8327AB2BF71F
  MODULE_TYPE                    = DXE_DRIVER
  VERSION_STRING                 = 1.0
  ENTRY_POINT                    = AmdErrorLogDisplayBrhDxeInit

[sources]
  AmdErrorLogDisplayBrhDxe.c
  AmdErrorLogDisplayBrhDxe.h

[LibraryClasses]
  UefiDriverEntryPoint
  DebugLib
  AmdErrorLogLib
  HobLib
  PcdLib
  AmdSocBaseLib

[Guids]
  gErrorLogHobGuid
  gAmdMemPmuTrainingFailureHobGuid

[Protocols]
  gAmdErrorLogReadyProtocolGuid          #CONSUMED
  gAmdErrorLogProtocolGuid               #PRODUCED
  gAmdErrorLogServiceProtocolGuid        #PRODUCED
  gAmdErrorLogFullProtocolGuid           #PRODUCED
  gAmdErrorLogAvailableProtocolGuid      #PRODUCED
  gAmdErrorLogDepexProtocolGuid          #CONSUMED
  gAmdNbioIommuProtocolGuid              #CONSUMED

[Packages]
  MdePkg/MdePkg.dec
  AgesaModulePkg/AgesaCommonModulePkg.dec
  AgesaPkg/AgesaPkg.dec
  AgesaModulePkg/AgesaFamily1AModulePkg.dec

[Pcd]
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAgesaPrintEventLogToConsole
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdIdsDebugPrintEnable

[Depex]
  gAmdErrorLogDepexProtocolGuid AND
  gAmdErrorLogReadyProtocolGuid AND
  gEfiPciEnumerationCompleteProtocolGuid AND
  gAmdMemSp5DxeBrhDepexProtocolGuid

[BuildOptions]
MSFT:*_*_*_CC_FLAGS = /D DISABLE_NEW_DEPRECATED_INTERFACES /W4

