#;*****************************************************************************
#;
#; Copyright (C) 2008-2024 Advanced Micro Devices, Inc. All rights reserved.
#;
#;******************************************************************************
[Defines]
  INF_VERSION                    = 0x00010006
  BASE_NAME                      = ApcbLibV3
  FILE_GUID                      = ********-DF16-4C7A-A32A-3E6F50213E68
  MODULE_TYPE                    = DXE_DRIVER
  VERSION_STRING                 = 1.0
  LIBRARY_CLASS                  = ApcbLibV3 | DXE_DRIVER DXE_SMM_DRIVER DXE_RUNTIME_DRIVER DXE_CORE SMM_CORE UEFI_DRIVER 
  CONSTRUCTOR                    = ApcbLibConstructor

[Sources.common]
  ApcbLibV3.c
  ApcbLibV3Services.c
  DramPostPackageRepair.c
  CoreApcbInterface.c
  CoreApcbInterface.h
  CalloutLib.c
  CalloutLib.h
  ApcbLibV2Compatibility.c
  UpdateShadowDimmConfig.c

[Packages]
  MdePkg/MdePkg.dec
  MdeModulePkg/MdeModulePkg.dec
  AgesaPkg/AgesaPkg.dec
  AgesaModulePkg/AgesaCommonModulePkg.dec
  AgesaModulePkg/AgesaModuleMemPkg.dec
  AgesaModulePkg/AgesaModulePspPkg.dec

[LibraryClasses]
  PcdLib
  AmdPspBaseLibV2
  BaseMemoryLib
  UefiBootServicesTableLib
  AmdPspFlashUpdateLib
  AmdPspApobLib
  ApobCommonServiceLib
  AmdSocBaseLib
  MemRestoreLib
  ApcbChecksumLibV3
  ApcbVariableLibV3
  AmdPspFwImageHeaderLib
  ApcbCoreLib

[Guids]


[Protocols]
  gEfiSmmBase2ProtocolGuid
  gEfiSmmReadyToLockProtocolGuid
  gPspFlashAccSmmCommReadyProtocolGuid

[Ppis]


[Pcd]
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdAmdMemCfgMaxPostPackageRepairEntries
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdApcbPriorityLevelAdmin
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdApcbPriorityLevelDebug
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdApcbPriorityLevelNormal
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdApcbDimmSpdShadow
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdLockApcbDxeAfterSmmLock
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdApcbUseHmacChecksum
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdApcbRecoveryStrategy
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdApcbRecoveryDiscardMemContext

[Depex]




