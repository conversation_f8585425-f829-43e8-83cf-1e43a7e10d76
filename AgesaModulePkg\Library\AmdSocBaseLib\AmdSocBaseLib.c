/*****************************************************************************
 * Copyright (C) 2015-2024 Advanced Micro Devices, Inc. All rights reserved.
 *
*****************************************************************************
*/
/* $NoKeywords:$ */
/**
 * @file
 *
 * AmdSocBaseLib.c
 *
 * Feature Soc common functions
 *
 * @xrefitem bom "File Content Label" "Release Content"
 * @e project: AGESA
 * @e sub-project: SOC
 * @e \$Revision: 313790 $ @e \$Date: 2015-02-26 23:31:28 -0600 (Thu, 26 Feb 2015) $
 *
 **/

#include <Library/BaseLib.h>
#include <Include/Filecode.h>
#include <Library/PcdLib.h>
#include <Library/AmdBaseLib.h>
#include <Library/AmdSocBaseLib.h>
#include <Library/PciLib.h>

#define FILECODE LIBRARY_AMDSOCBASELIB_AMDSOCBASELIB_FILECODE

/*----------------------------------------------------------------------------
 *                          DEFINITIONS AND MACROS
 *
 *----------------------------------------------------------------------------
 */

/*----------------------------------------------------------------------------
 *                           TYPEDEFS AND STRUCTURES
 *
 *----------------------------------------------------------------------------
 */

/*----------------------------------------------------------------------------
 *                        PROTOTYPES OF LOCAL FUNCTIONS
 *
 *----------------------------------------------------------------------------
 */


/* -----------------------------------------------------------------------------*/
/**
 *
 *
 *      This function check SOC hardware identification ID
 *
 *     @param[in]   SocId  - the SOC_ID_STRUCT
 *
 *     @return          TRUE -  Hardware identification ID check pass.
 *     @return          FALSE - Hardware identification ID check fail.
 */
BOOLEAN
SocHardwareIdentificationCheck (
  IN       SOC_ID_STRUCT *SocId
  )
{
  UINT32 EAX_Reg = 0;
  UINT32 EBX_Reg = 0;

  AsmCpuid (
      AMD_CPUID_FMF,
      &EAX_Reg,
      &EBX_Reg,
      NULL,
      NULL
      );

  // Check Hardware Identification
  if (SocId->SocFamilyID != (EAX_Reg & RAW_FAMILY_ID_MASK)) {
    return FALSE;
  }

  if (SocId->PackageType != ((EBX_Reg >> 28) & 0xF)) {
    return FALSE;
  }

  return TRUE;
}

/**
 *
 *
 *      This function check SOC hardware identification ID with base model
 *
 *     @param[in]   SocId  - the SOC_ID_STRUCT
 *
 *     @return          TRUE -  Hardware identification ID check pass.
 *     @return          FALSE - Hardware identification ID check fail.
 */
BOOLEAN
SocHardwareIdentificationCheckV2 (
  IN       SOC_ID_STRUCT *SocId
  )
{
  UINT32 EAX_Reg = 0;
  UINT32 EBX_Reg = 0;

  AsmCpuid (
      AMD_CPUID_FMF,
      &EAX_Reg,
      &EBX_Reg,
      NULL,
      NULL
      );

  // Check Hardware Identification
  if (SocId->SocFamilyID != (EAX_Reg & (RAW_FAMILY_ID_MASK | CPUID_BASE_MODEL_MASK))) {
    return FALSE;
  }

  if (SocId->PackageType != ((EBX_Reg >> 28) & 0xF)) {
    return FALSE;
  }

  return TRUE;
}

/**
 *
 *
 *      This function check SOC family identification ID
 *
 *     @param[in]   SocFamilyID  - FamilyId
 *
 *     @return          TRUE -  Hardware identification ID check pass.
 *     @return          FALSE - Hardware identification ID check fail.
 */
BOOLEAN
SocFamilyIdentificationCheck (
  IN       UINT32 SocFamilyID
  )
{
  UINT32 EAX_Reg = 0;

  AsmCpuid (
      AMD_CPUID_FMF,
      &EAX_Reg,
      NULL,
      NULL,
      NULL
      );

  // Check Hardware Identification
  if (SocFamilyID != (EAX_Reg & RAW_FAMILY_ID_MASK)) {
    return FALSE;
  }

  return TRUE;
}

/**
 *
 *
 *      This function check SOC family identification ID with base model
 *
 *     @param[in]   SocFamilyID  - FamilyId
 *
 *     @return          TRUE -  Hardware identification ID check pass.
 *     @return          FALSE - Hardware identification ID check fail.
 */
BOOLEAN
SocFamilyIdentificationCheckV2 (
  IN       UINT32 SocFamilyID
  )
{
  UINT32 EAX_Reg = 0;

  AsmCpuid (
      AMD_CPUID_FMF,
      &EAX_Reg,
      NULL,
      NULL,
      NULL
      );

  // Check Hardware Identification
  if (SocFamilyID != (EAX_Reg & (RAW_FAMILY_ID_MASK | CPUID_BASE_MODEL_MASK))) {
    return FALSE;
  }

  return TRUE;
}

BOOLEAN
AmdPcdInit(
  IN       UINT32               NumberOfPcdEntries,
  IN       AMD_PCD_LIST         *AmdPcdList
  )
{
  AMD_PCD_LIST *PlatformPcdList;
  AMD_PCD_PTR *Pcd_Ptr_Buff;
  UINT32 i;

  PlatformPcdList = AmdPcdList;
  for (i = 0 ; i < NumberOfPcdEntries ; i++) {
    switch ((PlatformPcdList[i].AmdPcdDataType & ~FLAG_NOT_PCD_EX)) {
    case PCD_BOOL:
      if ((PlatformPcdList[i].AmdPcdDataType & FLAG_NOT_PCD_EX) == 0) {
        LibPcdSetExBoolS (PlatformPcdList[i].AmdConfigurationParameterPcdGuid, PlatformPcdList[i].AmdConfigurationParameterPcdTokenNumber, (BOOLEAN)PlatformPcdList[i].Value);
      } else {
        LibPcdSetBoolS (PlatformPcdList[i].AmdConfigurationParameterPcdTokenNumber, (BOOLEAN)PlatformPcdList[i].Value);
      }
      break;
    case PCD_UINT8:
      if ((PlatformPcdList[i].AmdPcdDataType & FLAG_NOT_PCD_EX) == 0) {
        LibPcdSetEx8S (PlatformPcdList[i].AmdConfigurationParameterPcdGuid, PlatformPcdList[i].AmdConfigurationParameterPcdTokenNumber, (UINT8)PlatformPcdList[i].Value);
      } else {
        LibPcdSet8S (PlatformPcdList[i].AmdConfigurationParameterPcdTokenNumber, (UINT8)PlatformPcdList[i].Value);
      }
      break;

    case PCD_UINT16:
     if ((PlatformPcdList[i].AmdPcdDataType & FLAG_NOT_PCD_EX) == 0) {
        LibPcdSetEx16S (PlatformPcdList[i].AmdConfigurationParameterPcdGuid, PlatformPcdList[i].AmdConfigurationParameterPcdTokenNumber, (UINT16)PlatformPcdList[i].Value);
      } else {
        LibPcdSet16S (PlatformPcdList[i].AmdConfigurationParameterPcdTokenNumber, (UINT16)PlatformPcdList[i].Value);
      }
      break;

    case PCD_UINT32:
      if ((PlatformPcdList[i].AmdPcdDataType & FLAG_NOT_PCD_EX) == 0) {
        LibPcdSetEx32S (PlatformPcdList[i].AmdConfigurationParameterPcdGuid, PlatformPcdList[i].AmdConfigurationParameterPcdTokenNumber, (UINT16)PlatformPcdList[i].Value);
      } else {
        LibPcdSet32S (PlatformPcdList[i].AmdConfigurationParameterPcdTokenNumber, (UINT16)PlatformPcdList[i].Value);
      }
      break;

    case PCD_UINT64:
      if ((PlatformPcdList[i].AmdPcdDataType & FLAG_NOT_PCD_EX) == 0) {
        LibPcdSetEx64S (PlatformPcdList[i].AmdConfigurationParameterPcdGuid, PlatformPcdList[i].AmdConfigurationParameterPcdTokenNumber, (UINT16)PlatformPcdList[i].Value);
      } else {
        LibPcdSet64S (PlatformPcdList[i].AmdConfigurationParameterPcdTokenNumber, (UINT16)PlatformPcdList[i].Value);
      }
      break;

    case PCD_PTR:
      Pcd_Ptr_Buff = (AMD_PCD_PTR *)(UINTN)PlatformPcdList[i].Value;
      if ((PlatformPcdList[i].AmdPcdDataType & FLAG_NOT_PCD_EX) == 0) {
        LibPcdSetExPtrS (PlatformPcdList[i].AmdConfigurationParameterPcdGuid, PlatformPcdList[i].AmdConfigurationParameterPcdTokenNumber, &(Pcd_Ptr_Buff->BufferSize), Pcd_Ptr_Buff->Buffer);
      } else {
        LibPcdSetPtrS (PlatformPcdList[i].AmdConfigurationParameterPcdTokenNumber, &(Pcd_Ptr_Buff->BufferSize), Pcd_Ptr_Buff->Buffer);
      }
      break;
    }
  }
  return TRUE;
}

BOOLEAN
SocSpecialFuseCheck (
  IN       UINT32 Addr,
  IN       UINT32 BitLsb,
  IN       UINT32 BitMsb,
  IN       UINT32 ExpectedValue
  )
{
  UINT32   Value;
  UINTN    PciAddr;

  PciAddr = MAKE_SBDFO (0, 0, 0, 0, 0xB8);
  PciWrite32 (PciAddr, Addr);

  PciAddr = MAKE_SBDFO (0, 0, 0, 0, 0xBC);
  Value = PciRead32 (PciAddr);

  Value >>= BitLsb;
  Value &= (1 << (BitMsb - BitLsb + 1)) - 1;

  return (Value == ExpectedValue);
}

/**
 *
 *      This function check if the SOC is X3D or not
 *
 *     @param[in]   SocFamilyID  - FamilyId
 *
 *     @return          TRUE -  X3D.
 *     @return          FALSE - non-X3D.
 */
BOOLEAN
SocX3DCheck (
  IN       UINT32 SocFamilyID
  )
{
  UINT32 SmnData32;
  UINT8  CcdEn;
  UINT8  Index;

  switch (SocFamilyID) {
  case F19_VMR_RAW_ID: //Vermeer
    LibAmdSmnRegisterRead (0, 0x5D3C0, &SmnData32);
    if (SmnData32 & BIT16) {
      return TRUE;
    }
    break;
  case F19_RPL_RAW_ID: //Raphael
    LibAmdSmnRegisterRead (0, 0x5D3BC, &SmnData32);
    CcdEn = (SmnData32 >> 22) & 0x3;
    for (Index = 0; Index < 2; Index++ ) {
      if ((1<<Index) & CcdEn) {
        LibAmdSmnRegisterRead (0, (0x30081CE0|(Index << 25)), &SmnData32);
        if (SmnData32 & (BIT23|BIT24|BIT25|BIT26)) {
          return TRUE;
        }
      }
    }
    break;
  case F1A_GNR_RAW_ID: //Granite Ridge
    LibAmdSmnRegisterRead (0, 0x5D3BC, &SmnData32);
    CcdEn = (SmnData32 >> 22) & 0x3;
    for (Index = 0; Index < 2; Index++ ) {
      if ((1<<Index) & CcdEn) {
        LibAmdSmnRegisterRead (0, (0x304A0064|(Index << 25)), &SmnData32);
        if (SmnData32 & (BIT26|BIT27|BIT28)) {
          return TRUE;
        }
      }
    }
    break;
  default:
    break;
  }

  return FALSE;
}

/**
 *
 *  This function check SOC Family & Ext Model & Base Model & Stepping & Package Type identification ID
 *
 *     @param[in]   SocFamilyID  - FamilyID
 *     @param[in]   Attribute    - Attribute
 *                                 If need to check the extended Model, please set FIXED_EXT_MODEL to Attribute;
 *                                 If do not check the extended Model, please set ANY_EXT_MODEL to Attribute.
 *                                 If need to check the base Model, please set FIXED_BASE_MODEL to Attribute;
 *                                 If do not check the base Model, please set ANY_BASE_MODEL to Attribute.
 *                                 If do not check the stepping, please set ANY_STEPPING to Attribute.
 *                                 If do not check the package type, please set ANY_PKG_TYPE to Attribute;
 *                                 If need to check the package type, please set package type to Attribute bit 0~3.
 *
 *     @return      TRUE         - check pass.
 *     @return      FALSE        - check fail.
 */
BOOLEAN
SocIdentificationCheck (
  IN       UINT32 SocFamilyID,
  IN       UINT32 Attributes
  )
{
  UINT32 EAX_Reg = 0;
  UINT32 EBX_Reg = 0;

  AsmCpuid (
      AMD_CPUID_FMF,
      &EAX_Reg,
      &EBX_Reg,
      NULL,
      NULL
      );

  // Check Family
  if ((SocFamilyID & (CPUID_EXT_FAMILY_MASK | CPUID_BASE_FAMILY_MASK)) != (EAX_Reg & (CPUID_EXT_FAMILY_MASK | CPUID_BASE_FAMILY_MASK))) {
    return FALSE;
  }

  // Check Ext Model
  if ((Attributes & ANY_EXT_MODEL) == FIXED_EXT_MODEL) {
    if ((SocFamilyID & CPUID_EXT_MODEL_MASK) != (EAX_Reg & CPUID_EXT_MODEL_MASK)) {
      return FALSE;
    }
  }

  // Check Base Model
  if ((Attributes & ANY_BASE_MODEL) == FIXED_BASE_MODEL) {
    if ((SocFamilyID & CPUID_BASE_MODEL_MASK) != (EAX_Reg & CPUID_BASE_MODEL_MASK)) {
      return FALSE;
    }
  }

  // Check Stepping
  if ((Attributes & ANY_STEPPING) != ANY_STEPPING) {
    if ((SocFamilyID & CPUID_STEPPING_MASK) != (EAX_Reg & CPUID_STEPPING_MASK)) {
      return FALSE;
    }
  }

  // Check Package Type
  if ((Attributes & ANY_PKG_TYPE) != ANY_PKG_TYPE) {
    if ((Attributes & ANY_PKG_TYPE) != ((EBX_Reg >> 28) & 0xF)) {
      return FALSE;
    }
  }

  return TRUE;
}

