#;*****************************************************************************
#;
#; Copyright (C) 2008-2024 Advanced Micro Devices, Inc. All rights reserved.
#;
#;******************************************************************************
[Defines]
  INF_VERSION                    = 0x00010006
  BASE_NAME                      = CcxZen5BrhIdsHookLibSmm
  FILE_GUID                      = 926D4DB9-7C51-4F23-B97A-F08213200CF7
  MODULE_TYPE                    = BASE
  VERSION_STRING                 = 1.0
  LIBRARY_CLASS                  = CcxZen5IdsHookLibSmm

[Sources.common]
  CcxZen5BrhIdsHookLibSmm.c
  CcxZen5BrhIdsSyncMsrSmm.c
  CcxZen5BrhIdsSyncMsrSmm.h

[Packages]
  MdePkg/MdePkg.dec
  AgesaModulePkg/AgesaCommonModulePkg.dec
  AgesaModulePkg/AgesaModuleCcxPkg.dec
  AgesaModulePkg/AgesaModuleFchPkg.dec
  AgesaPkg/AgesaPkg.dec

[LibraryClasses]
  BaseLib
  BaseMemoryLib
  AmdBaseLib
  SmmServicesTableLib
  AmdIdsDebugPrintLib
  IdsLib
  CcxRolesLib

[Guids]

[Protocols]
  gFchSmmSwDispatch2ProtocolGuid

[Ppis]

[FeaturePcd]

[Pcd]

[Depex]
  gFchSmmSwDispatch2ProtocolGuid

