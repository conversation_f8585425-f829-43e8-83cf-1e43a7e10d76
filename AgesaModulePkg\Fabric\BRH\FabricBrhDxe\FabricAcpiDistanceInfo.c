/*****************************************************************************
 *
 * Copyright (C) 2019-2024 Advanced Micro Devices, Inc. All rights reserved.
 *
 *****************************************************************************
 */

/*----------------------------------------------------------------------------------------
 *                             M O D U L E S    U S E D
 *----------------------------------------------------------------------------------------
 */
#include "Porting.h"
#include "AMD.h"
#include <Library/AmdBaseLib.h>
#include <Library/BaseFabricTopologyLib.h>
#include <Library/AmdIdsHookLib.h>
#include <Protocol/FabricNumaServices2.h>
#include <Filecode.h>

/*----------------------------------------------------------------------------------------
 *                   D E F I N I T I O N S    A N D    M A C R O S
 *----------------------------------------------------------------------------------------
 */
#define FILECODE FABRIC_BRH_FABRICBRHDXE_FABRICACPIDISTANCEINFO_FILECODE

/*----------------------------------------------------------------------------------------
 *           P R O T O T Y P E S     O F     L O C A L     F U N C T I O N S
 *----------------------------------------------------------------------------------------
 */

/*----------------------------------------------------------------------------------------
 *                           G L O B A L   V A R I A B L E S
 *----------------------------------------------------------------------------------------
 */

/*----------------------------------------------------------------------------------------
 *                  T Y P E D E F S     A N D     S T R U C T U R E S
 *----------------------------------------------------------------------------------------
 */
#define DISTANCE_TO_SELF 10

/*----------------------------------------------------------------------------------------
 *                          E X P O R T E D    F U N C T I O N S
 *----------------------------------------------------------------------------------------
 */

/*----------------------------------------------------------------------------------------*/
/**
 * @brief This function publishes the SLIT and CDIT distance information.
 *
 * @param[in, out] Distance    Current ACPI table pointer to store the distance matrix.
 * @param[in]      DomainCount Number of unique NUMA domains.
 * @param[in]      DomainInfo  Information about the NUMA domains.
 *
 * @return EFI_STATUS
 *
 * @retval EFI_SUCCESS - Distance matrix stored.
 */
EFI_STATUS
EFIAPI
FabricGetDistanceInfo (
  IN OUT UINT8         *Distance,
  IN     UINT32        DomainCount,
  IN     DOMAIN_INFO2  *DomainInfo
  )
{
  UINT32  i;
  UINT32  j;
  UINT8   D2DLocal;
  UINT8   D2DRemote;
  UINT8   D2CLocal;
  UINT8   D2CRemote;
  UINT8   V2VLocal;
  BOOLEAN UsePcds;

  // Check the master switch on whether or not the PCDs should be honored
  UsePcds = (BOOLEAN) (PcdGet8 (PcdAmdFabricSlitDistancePcdCtrl) == 0);

  // Distance to report for different physical domains on the same socket
  D2DLocal = UsePcds ? PcdGet8 (PcdAmdFabricSlitLocalDistance) : 12;
  if (D2DLocal < 10) {
    D2DLocal = 11;
  }

  // Distance to report for domains on the other socket
  D2DRemote = UsePcds ? PcdGet8 (PcdAmdFabricSlitRemoteDistance) : PcdGetBool (PcdAmdFabricSlitAutoRemoteFar) ? 32 : 28;
  if (D2DRemote < 10) {
    D2DRemote = 11;
  }

  // Distance to report for different virtual domains in the same physical domain
  V2VLocal = UsePcds ? PcdGet8 (PcdAmdFabricSlitVirtualDistance) : 11;
  if (V2VLocal < 10) {
    V2VLocal = 11;
  }

  // Distance to report for CXL connected memory attached to the same socket
  D2CLocal = UsePcds ? PcdGet8 (PcdAmdFabricSlitCxlLocalDistance) : 18;
  if (D2CLocal < 10) {
    D2CLocal = 11;
  }

  // Distance to report for CXL connected memory attached to the other socket
  D2CRemote = UsePcds ? PcdGet8 (PcdAmdFabricSlitCxlRemoteDistance) : 28;
  if (D2CRemote < 10) {
    D2CRemote = 11;
  }

  for (i = 0; i < DomainCount; i++) {
    ASSERT (DomainInfo[i].Type < MaxNumaDomainType2);
    for (j = 0; j < DomainCount; j++) {
      if (i == j) {
        // Case 1: distance to self
        *Distance = DISTANCE_TO_SELF;
      } else if (DomainInfo[i].PhysicalDomain == DomainInfo[j].PhysicalDomain) {
        // Case 2: two virtual domains within the same physical domain
        *Distance = V2VLocal;
      } else {
        ASSERT (DomainInfo[j].Type < MaxNumaDomainType2);
        if ((DomainInfo[i].Type == NumaDram) && (DomainInfo[j].Type == NumaDram)) {
          // Case 3: the two domains contain cores and possibly DRAM
          *Distance = ((DomainInfo[i].SocketMap & DomainInfo[j].SocketMap) != 0) ? D2DLocal : D2DRemote;
        } else if ((DomainInfo[i].Type == NumaDram) && (DomainInfo[j].Type == NumaCxl)) {
          // Case 4: from Dram to CXL.mem
          *Distance = ((DomainInfo[i].SocketMap & DomainInfo[j].SocketMap) != 0) ? D2CLocal : D2CRemote;
        } else if (DomainInfo[i].Type == NumaCxl) {
          // Case 5: from CXL.mem to others
          *Distance = 0xFF;
        } else {
          // Unsupported combination.  Mark as unreachable.
          ASSERT (FALSE);
          *Distance = 0xFF;
        }
      }
      Distance++;
    }
  }

  return EFI_SUCCESS;
}

