/*
 ******************************************************************************
 *
 * Copyright (C) 2008-2024 Advanced Micro Devices, Inc. All rights reserved.
 *
 *******************************************************************************
 */

/*----------------------------------------------------------------------------------------
 *                             M O D U L E S    U S E D
 *----------------------------------------------------------------------------------------
 */
#include <Uefi.h>
#include <AGESA.h>
#include <Library/AmdBaseLib.h>
#include <Library/AmdSocBaseLib.h>
#include <Library/AmdPspRegBaseLib.h>
#include <Filecode.h>

#define FILECODE LIBRARY_AMDPSPREGBASELIB_AMDPSPREGBASELIB_FILECODE

/*----------------------------------------------------------------------------------------
 *                   D E F I N I T I O N S    A N D    M A C R O S
 *----------------------------------------------------------------------------------------
 */
#pragma pack (push, 1)

typedef struct _AMD_PSP_REG_INFO_STRUCT {
  UINT32  SocFamilyID;
  UINT32  IohcxNbMiscSmnBase;
  UINT8   RbNumber;
  UINT8   PspC2pMsgRegType;
  UINT32  PspC2pMsgRegBaseOffset;
  UINT32  PspP2cMsgRegBaseOffset;
} AMD_PSP_REG_INFO_STRUCT;

#pragma pack (pop)

/*----------------------------------------------------------------------------------------
 *                  T Y P E D E F S     A N D     S T R U C T U R E S
 *----------------------------------------------------------------------------------------
 */
AMD_PSP_REG_INFO_STRUCT gAmdPspRegInfoTbl [] = {
  {F17_MTS_RAW_ID,    IOHC3NBMSIC_SMN_BASE, 0x00, MP0_C2PMSG,   MP0_C2PMSG_BASE_OFFSET,   MP0_P2CMSG_BASE_OFFSET},
  {F17_SSP_RAW_ID,    IOHC3NBMSIC_SMN_BASE, 0x03, MP0_C2PMSG,   MP0_C2PMSG_BASE_OFFSET,   MP0_P2CMSG_BASE_OFFSET},
  {F19_VMR_RAW_ID,    IOHC3NBMSIC_SMN_BASE, 0x00, MP0_C2PMSG,   MP0_C2PMSG_BASE_OFFSET,   MP0_P2CMSG_BASE_OFFSET},
  {F1A_BRH_RAW_ID,    IOHC0NBMSIC_SMN_BASE, 0x00, MPASP_C2PMSG, MPASP_C2PMSG_BASE_OFFSET, MPASP_P2CMSG_BASE_OFFSET},
  {F1A_BRHD_RAW_ID,   IOHC0NBMSIC_SMN_BASE, 0x00, MPASP_C2PMSG, MPASP_C2PMSG_BASE_OFFSET, MPASP_P2CMSG_BASE_OFFSET},
  {F1A_STX1_RAW_ID,   IOHC0NBMSIC_SMN_BASE, 0x00, MPASP_C2PMSG, MPASP_C2PMSG_BASE_OFFSET, MPASP_P2CMSG_BASE_OFFSET},
  {F1A_STXH_RAW_ID,   IOHC0NBMSIC_SMN_BASE, 0x00, MPASP_C2PMSG, MPASP_C2PMSG_BASE_OFFSET, MPASP_P2CMSG_BASE_OFFSET},
  {F1A_KRK1_RAW_ID,   IOHC0NBMSIC_SMN_BASE, 0x00, MPASP_C2PMSG, MPASP_C2PMSG_BASE_OFFSET, MPASP_P2CMSG_BASE_OFFSET},
};

/*----------------------------------------------------------------------------------------
 *           P R O T O T Y P E S     O F     L O C A L     F U N C T I O N S
 *----------------------------------------------------------------------------------------
 */

/**
 * @brief Get PSP Register Info Entry
 *
 * @param[in,out] AMD_PSP_REG_INFO_STRUCT **Entry
 *
 * @retval         BOOLEAN TRUE:  Found
 *                         FALSE: Not found
 */
BOOLEAN
GetAmdPspRegInfoEntry (
  IN OUT AMD_PSP_REG_INFO_STRUCT **Entry
  )
{
  UINT32  Index;
  UINT32  Size;
  BOOLEAN IsFound;

  *Entry  = NULL;
  IsFound = FALSE;

  Size = sizeof (gAmdPspRegInfoTbl)/sizeof (AMD_PSP_REG_INFO_STRUCT);
  for (Index = 0; Index < Size; Index++) {
    if (SocFamilyIdentificationCheck (gAmdPspRegInfoTbl [Index].SocFamilyID)) {
      IsFound = TRUE;
      *Entry  = &gAmdPspRegInfoTbl [Index];
      break;
    }
  }

  return IsFound;
}


/**
 * @brief Get Psp IOHCxNbMisc SmmBase and RbNumber
 *
 * @param [in,out] UINT32 *SmnBase
 * @param [in,out] UINT8  *RbNumber
 *
 * @retval         VOID
 */
VOID
EFIAPI
GetPspIOHCxNbMiscSmnBase (
  IN OUT UINT32 *SmnBase,
  IN OUT UINT8  *RbNumber
  )
{
  AMD_PSP_REG_INFO_STRUCT *Entry;

  Entry    = NULL;
  *SmnBase = IOHC0NBMSIC_SMN_BASE;
  if (RbNumber != NULL) {
    *RbNumber = 0;
  }

  if (GetAmdPspRegInfoEntry (&Entry) == TRUE) {
    *SmnBase  = Entry->IohcxNbMiscSmnBase;
    if (RbNumber != NULL) {
      *RbNumber = Entry->RbNumber;
    }
  }
}

/**
 * @brief Get Psp C2P MSG Register Type
 *
 * @param [in] VOID
 *
 * @retval     UINT8   Type
 */
UINT8
EFIAPI
GetPspC2pMsgRegType (
  VOID
  )
{
  AMD_PSP_REG_INFO_STRUCT *Entry;
  UINT8                   Type;

  Entry = NULL;
  Type  = MP0_C2PMSG;

  if (GetAmdPspRegInfoEntry (&Entry) == TRUE) {
    Type = Entry->PspC2pMsgRegType;
  }

  return Type;
}

/**
 * @brief Get Psp C2P MSG Register Base Offset
 *
 * @param   VOID
 *
 * @retval  UINT32  BaseOffset
 */
UINT32
EFIAPI
GetPspC2pMsgRegBaseOffset (
  VOID
  )
{
  AMD_PSP_REG_INFO_STRUCT *Entry;
  UINT32                  BaseOffset;

  Entry      = NULL;
  BaseOffset = MP0_C2PMSG_BASE_OFFSET;

  if (GetAmdPspRegInfoEntry (&Entry) == TRUE) {
    BaseOffset = Entry->PspC2pMsgRegBaseOffset;
  }

  return BaseOffset;
}


/**
 * @brief Get Psp C2P MSG Register Offset
 *
 * @param [in] UINT32 PspC2pMsgNumber (Range 0 ~ 127)
 *
 * @retval     UINT32 RegOffset
 */
UINT32
EFIAPI
GetPspC2pMsgRegOffset (
  UINT32 PspC2pMsgNumber
  )
{
  UINT32 BaseOffset;
  UINT32 AlignOffset;
  UINT32 RegOffset;

  RegOffset = 0xFFFFFFFF;

  if (PspC2pMsgNumber > PSP_C2PMSG_127) {
    return RegOffset;
  }

  //
  //RMB/PHX/MDN:
  //  MP0_C2PMSG_0   (Offset: 0x1_0500) ~ MP0_C2PMSG_31   (Offset: 0x1_057C)
  //BRH/STX:
  //  MPASP_C2PMSG_0 (Offset: 0x1_0900) ~ MPASP_C2PMSG_31 (Offset: 0x1_097C)
  //
  //RMB/PHX/MDN:
  //  MP0_C2PMSG_32   (Offset: 0x1_0980) ~ MP0_C2PMSG_127   (Offset: 0x1_097C)
  //BRH/STX:
  //  MPASP_C2PMSG_32 (Offset: 0x1_0980) ~ MPASP_C2PMSG_127 (Offset: 0x1_097C)
  //
  BaseOffset = GetPspC2pMsgRegBaseOffset ();

  AlignOffset = 0;
  if ((PspC2pMsgNumber >= PSP_C2PMSG_32) &&
      (BaseOffset == MP0_C2PMSG_BASE_OFFSET)) {
    AlignOffset = PSP_ALIGN_OFFSET;
  }

  RegOffset = BaseOffset + AlignOffset + (PspC2pMsgNumber * 4);

  return RegOffset;
}


/**
 * @brief Get Psp P2C MSG Register Base Offset
 *
 * @param   VOID
 *
 * @retval  UINT32  BaseOffset
**/
UINT32
EFIAPI
GetPspP2cMsgRegBaseOffset (
  VOID
  )
{
  AMD_PSP_REG_INFO_STRUCT *Entry;
  UINT32                  BaseOffset;

  Entry      = NULL;
  BaseOffset = MP0_P2CMSG_BASE_OFFSET;

  if (GetAmdPspRegInfoEntry (&Entry) == TRUE) {
    BaseOffset = Entry->PspP2cMsgRegBaseOffset;
  }

  return BaseOffset;
}


/**
 * @brief Get Psp P2C MSG Register Offset
 *
 * @param [in] UINT32 PspP2cMsgNumber (Range 0 ~ 5)
 *
 * @retval     UINT32 RegOffset
 */
UINT32
EFIAPI
GetPspP2cMsgRegOffset (
  UINT32 PspP2cMsgNumber
  )
{
  UINT32 BaseOffset;
  UINT32 RegOffset;

  RegOffset = 0xFFFFFFFF;

  if (PspP2cMsgNumber > PSP_P2CMSG_INTSTS) {
    return RegOffset;
  }

  BaseOffset = GetPspP2cMsgRegBaseOffset ();

  RegOffset = BaseOffset + (PspP2cMsgNumber * 4);

  return RegOffset;
}

