/*****************************************************************************
 * Copyright (C) 2008-2024 Advanced Micro Devices, Inc. All rights reserved.
 *
*****************************************************************************
*/
/* $NoKeywords:$ */
/**
 * @file
 *
 * FCH common
 *
 *
 *
 * @xrefitem bom "File Content Label" "Release Content"
 * @e project:     AGESA
 * @e sub-project: FCH
 * @e \$Revision: 309090 $   @e \$Date: 2014-12-09 10:28:05 -0800 (Tue, 09 Dec 2014) $
 *
 */
#include "FchPlatform.h"
#define FILECODE FCH_KUNLUN_FCHKUNLUNCORE_COMMON_FCHCOMMON_FILECODE

/**
 * ReadFchChipsetRevision - Get FCH chipset revision
 *
 *
 * @param[in] StdHeader - Amd Configuration Parameters.
 *
 */
UINT8
ReadFchChipsetRevision (
  IN AMD_CONFIG_PARAMS *StdHeader
  )
{
  UINT8    FchChipSetRevision;
  UINT8    FchMiscRegister;
  ReadPmio (FCH_PMIOA_REGC8 + 1, AccessWidth8, &FchMiscRegister, StdHeader);
  RwMem (ACPI_MMIO_BASE + PMIO_BASE + FCH_PMIOA_REGC8 + 1, AccessWidth8, ~(UINT32) BIT7, 0);
  ReadPci ((SMBUS_BUS_DEV_FUN << 16) + FCH_CFG_REG08, AccessWidth8, &FchChipSetRevision, StdHeader);
  WritePmio (FCH_PMIOA_REGC8 + 1, AccessWidth8, &FchMiscRegister, StdHeader);
  return FchChipSetRevision;
}

/**
 * ReadSocType - Get Soc ID
 *
 *
 *
 */
UINT32
ReadSocType (
  VOID
  )
{
  UINT32    SocId;

  ReadPci ((AMD_D0F0 << 16) + FCH_CFG_REG00, AccessWidth32, &SocId, NULL);

  return SocId;
}

/**
 * CheckZP - Check if ZP Soc
 *
 *
 *
 */
BOOLEAN
CheckZP (
  VOID
  )
{
  if (ReadSocType () == SOC_ZPID) {
    return TRUE;
  } else {
    return FALSE;
  }
}

/**
 * ReadSocDieBusNum - Get Bus Number of given Die
 *
 *
 *
 */
UINT32
ReadSocDieBusNum (
  UINT8  Die
  )
{
  UINT16    VendorID;
  UINT32    DieBusNum;

  ReadPci (((((Die + 24) << 3) + 0) << 16) + FCH_CFG_REG00, AccessWidth16, &VendorID, NULL);
  if (VendorID != 0x1022) {
    return 0xffffffff;
  }

  ReadPci (((((Die + 24) << 3) + 0) << 16) + 0x84, AccessWidth32, &DieBusNum, NULL);
  DieBusNum &= 0xff;
  return DieBusNum;
}



