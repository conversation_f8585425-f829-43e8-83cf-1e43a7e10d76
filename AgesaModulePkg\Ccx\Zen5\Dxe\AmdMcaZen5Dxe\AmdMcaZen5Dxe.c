/*****************************************************************************
 *
 * Copyright (C) 2008-2024 Advanced Micro Devices, Inc. All rights reserved.
 *
 *******************************************************************************
 */

#include <Library/BaseLib.h>
#include <Library/AmdBaseLib.h>
#include <Library/AmdPspApobLib.h>
#include <Library/AmdSocBaseLib.h>
#include <Library/UefiBootServicesTableLib.h>
#include <Library/BaseMemoryLib.h>
#include <Library/CoreTopologyV3Lib.h>
#include <Protocol/AmdCoreTopologyV3Protocol.h>
#include <Protocol/MpService.h>
#include <Addendum/Apcb/Inc/APOBCMN.h>
#include <AmdRas.h>
#include <Filecode.h>

#define FILECODE CCX_ZEN5_DXE_AMDMCAZEN5DXE_AMDMCAZEN5DXE_FILECODE

#define LS_THR0_SMNADDR_BYTE1           0xAE
#define IF_THR0_SMNADDR_BYTE1           0xAA
#define L2_THR0_SMNADDR_BYTE1           0x8A
#define DE_THR0_SMNADDR_BYTE1           0x96
#define EX_THR0_SMNADDR_BYTE1           0x9A
#define FP_THR0_SMNADDR_BYTE1           0x86

/**
 * @brief The total number of MCA registers in an MCAX Bank.
 *
 * @details MCAX MSRs are present at MSRC000_2[3FF:000]. This MSR address range contains space for 64 banks
 * of 16 registers each.
 */
#define SMCA_REG_PER_BANK               (1<<4)
#define MCA_IPID_OFFSET                 (0x05)

/**
 * @brief The MSR address of the first MCAX register
 *
 * @details Machine Check Architecture Extensions (MCAX) is AMD's x86-64 extension to the Machine Check Architecture.
 *          MCAX supports up to 64 MCA banks per logical core. MSRC000_2000 for the beginning of MCA Bank 0
 *          MCAX MSRs are present at MSRC000_2[3FF:000]. This MSR address range contains space for 64 banks of 16
 *          registers each. MSRC000_2[FFF:400] are Reserved for future use. The MCAX MSR address range allows access
 *          to both legacy MCA registers and MCAX registers in each MCA bank.
 */
#define MCA_EXTENSION_BASE              (0xC0002000UL)
// MCA Hardware ID
/**
 * @brief The MCA Hardware ID and MCA TYPE of CPU CORE Block
 *
 * @details To determine which type of block is mapped to an MCA bank, software can query the MCA_IPID register
 *          within that bank.
 *          MCA_IPID[HardwareID] provides the block type for the block that contains this MCA bank.
 *          For blocks that contain multiple MCA bank types (e.g., CPU cores), MCA_IPID[McaType] provides
 *          an identifier for the type of MCA bank.
 */
#define MCA_CPU_CORE_ID                 (0x0B0)
#define LS_MCA_TYPE                     (0x0000)  ///< Load-Store Unit
#define IF_MCA_TYPE                     (0x0001)  ///< Instruction Fetch Unit
#define L2_MCA_TYPE                     (0x0002)  ///< L2 Cache Unit
#define DE_MCA_TYPE                     (0x0003)  ///< Decode Unit
#define EX_MCA_TYPE                     (0x0005)  ///< Execution Unit
#define FP_MCA_TYPE                     (0x0006)  ///< Floating-Point Unit
#define L3_MCA_TYPE                     (0x0007)  ///< L3 Cache Unit

#define RAS_MAX_CORES (CCX_MAX_SOCKETS * CCX_MAX_DIES_PER_SOCKET * MAX_CCDS_PER_IOD * MAX_CCX_PER_CCD * CCX_MAX_CORES_PER_COMPLEX * CCX_MAX_THREADS_PER_CORE)

#define CCX_MAX_SOCKETS                 2
#define CCX_MAX_DIES_PER_SOCKET         1   // Program dependent
#define MAX_CCX_PER_CCD                 2   // Program dependent
#define CCX_MAX_CORES_PER_COMPLEX       8   // Zen is 4, Cerberus is 8
#define CCX_MAX_THREADS_PER_CORE        2   // Fixed for Zen / Cerberus
#define CCX_NOT_PRESENT (0xFF)

#define MAX_CCDS_PER_IOD                8  // Program dependent

typedef struct {
  UINT8                 PhysCoreNumber;
  BOOLEAN               IsThreadEnabled[CCX_MAX_THREADS_PER_CORE];
} LOGICAL_CORE_INFO;

typedef struct {
  UINT8                 PhysComplexNumber;
  LOGICAL_CORE_INFO     CoreInfo[CCX_MAX_CORES_PER_COMPLEX];
} LOGICAL_COMPLEX_INFO;

typedef struct {
  UINT8                 PhysCcdNumber;
  LOGICAL_COMPLEX_INFO  ComplexMap[MAX_CCX_PER_CCD];
} LOGICAL_CCD_INFO;

/// AMD APOB_CCD_LOGICAL_TO_PHYSICAL_MAP_TYPE Header
typedef struct {
  APOB_TYPE_HEADER      ApobTypeHeader;    ///< APOB Type Header
  LOGICAL_CCD_INFO      CcdMap[MAX_CCDS_PER_IOD];
} APOB_CCD_LOGICAL_TO_PHYSICAL_MAP_TYPE_STRUCT;

typedef struct {
  EFI_MP_SERVICES_PROTOCOL      *MpServices;
  CPU_INFO                      *RasCpuMap;
} MCA_MP_FUNCTION_PARAM;

/*----------------------------------------------------------------------------------------
 *                               D E F I N I T I O N S
 *----------------------------------------------------------------------------------------
 */
VOID
EFIAPI
McaZen5InitWithMpServices (
  IN EFI_EVENT              Event,
  IN VOID                   *Context
  );

VOID
EFIAPI
ProgramCoreMcaIpIdInstanceId (
  IN VOID                   *Buffer
  );

EFI_STATUS
CollectCpuMap (
  OUT MCA_MP_FUNCTION_PARAM *McaMpFuncParam
  );

/*----------------------------------------------------------------------------------------
 *                           G L O B A L   V A R I A B L E S
 *----------------------------------------------------------------------------------------
 */

/*----------------------------------------------------------------------------------------
 *           P R O T O T Y P E S     O F     L O C A L     F U N C T I O N S
 *----------------------------------------------------------------------------------------
 */

/* -----------------------------------------------------------------------------*/
/**
 *
 *  AmdCcxZen5DxeInit
 *
 *  @param[in]  ImageHandle     EFI Image Handle for the DXE driver
 *  @param[in]  SystemTable     Pointer to the EFI system table
 *
 *  Description:
 *    Zen5 Driver Entry.  Initialize the core complex.
 *
 *  @retval EFI_STATUS
 *
 */
EFI_STATUS
EFIAPI
AmdMcaZen5DxeInit (
  IN EFI_HANDLE             ImageHandle,
  IN EFI_SYSTEM_TABLE       *SystemTable
  )
{
  EFI_STATUS                Status;
  EFI_STATUS                CalledStatus;
  EFI_MP_SERVICES_PROTOCOL  *MpServices;
  EFI_EVENT                 McaZen5InitWithMpServicesEvent;
  VOID                      *RegistrationForMcaZen5InitWithMpServicesEvent;

  IDS_HDT_CONSOLE (MAIN_FLOW, "  AmdMcaZen5DxeInit Entry\n");

  //
  // Set up call back after MP services are available.
  //
  Status = gBS->LocateProtocol (&gEfiMpServiceProtocolGuid, NULL, (VOID **)&MpServices);
  if (!EFI_ERROR (Status)) {
    McaZen5InitWithMpServices (NULL, NULL);
  } else {
    CalledStatus = gBS->CreateEventEx (
                             EVT_NOTIFY_SIGNAL,
                             TPL_NOTIFY,
                             McaZen5InitWithMpServices,
                             NULL,
                             NULL,
                             &McaZen5InitWithMpServicesEvent
                             );
    Status = (CalledStatus > Status) ? CalledStatus : Status;

    CalledStatus = gBS->RegisterProtocolNotify (
                            &gEfiMpServiceProtocolGuid,
                            McaZen5InitWithMpServicesEvent,
                            &(RegistrationForMcaZen5InitWithMpServicesEvent)
                            );
    Status = (CalledStatus > Status) ? CalledStatus : Status;
  }

  IDS_HDT_CONSOLE (MAIN_FLOW, "  AmdMcaZen5DxeInit End\n");

  return Status;
}

VOID
EFIAPI
McaZen5InitWithMpServices (
  IN EFI_EVENT              Event,
  IN VOID                   *Context
  )
{
  EFI_STATUS                Status;
  EFI_MP_SERVICES_PROTOCOL  *MpServices;
  MCA_MP_FUNCTION_PARAM     McaMpFuncParam;

  IDS_HDT_CONSOLE (MAIN_FLOW, "  McaZen5InitWithMpServices Entry\n");

  Status = gBS->LocateProtocol (&gEfiMpServiceProtocolGuid, NULL, (VOID **)&MpServices);
  if (EFI_ERROR(Status)) {
    return;
  }

  McaMpFuncParam.MpServices = MpServices;
  McaMpFuncParam.RasCpuMap  = NULL;
  Status = CollectCpuMap (&McaMpFuncParam);
  if (EFI_ERROR(Status)) {
    IDS_HDT_CONSOLE (MAIN_FLOW, "  CollectCpuMap failed\n");
    return;
  }
  // Execute on running APs
  MpServices->StartupAllAPs (
                MpServices,
                ProgramCoreMcaIpIdInstanceId,
                FALSE,
                NULL,
                0,
                (VOID *)&McaMpFuncParam,
                NULL
                );

  // For BSP
  ProgramCoreMcaIpIdInstanceId ((VOID *)&McaMpFuncParam);

  IDS_HDT_CONSOLE (MAIN_FLOW, "  McaZen5InitWithMpServices Exit\n");
}

VOID
EFIAPI
ProgramCoreMcaIpIdInstanceId (
  IN VOID                   *Buffer
  )
{
  EFI_STATUS                Status;
  UINT32                    CoreMcaBankIndex;
  MCA_IPID_MSR              McaIpidMsr;
  UINTN                     ProcNum;
  MCA_MP_FUNCTION_PARAM     *McaMpFuncParam;
  EFI_MP_SERVICES_PROTOCOL  *MpServices;
  CPU_INFO                  *RasCpuMap;
  UINT8                     CcdId;
  UINT8                     CcxId;
  UINT8                     CoreId;
  UINT8                     ThreadId;
  UINT8                     CoreMcaSmnAddrByte1;
  UINT8                     SocketId;

  McaMpFuncParam = Buffer;
  MpServices = McaMpFuncParam->MpServices;
  RasCpuMap = McaMpFuncParam->RasCpuMap;

  Status = MpServices->WhoAmI (MpServices, &ProcNum);
  if (EFI_ERROR (Status)) {
    return;
  }

  CcdId     = RasCpuMap[ProcNum].DieId;
  CcxId     = RasCpuMap[ProcNum].CcxId;
  CoreId    = RasCpuMap[ProcNum].CoreId;
  ThreadId  = RasCpuMap[ProcNum].ThreadID;
  SocketId  = RasCpuMap[ProcNum].SocketId;

  for (CoreMcaBankIndex = 0; CoreMcaBankIndex < MAX_CORE_MCA_BANK_COUNT; CoreMcaBankIndex++) {
    if (CoreMcaBankIndex == 4) { // RAZ
      continue;
    }

    McaIpidMsr.Value = AsmReadMsr64 ((MCA_EXTENSION_BASE + ((CoreMcaBankIndex * SMCA_REG_PER_BANK) | MCA_IPID_OFFSET))); // MCA_IPID

    if (McaIpidMsr.Field.HardwareID != MCA_CPU_CORE_ID) {
      continue; // Should not be here
    }

    switch (McaIpidMsr.Field.McaType) {
      case LS_MCA_TYPE:
        CoreMcaSmnAddrByte1 = LS_THR0_SMNADDR_BYTE1;
        break;
      case IF_MCA_TYPE:
        CoreMcaSmnAddrByte1 = IF_THR0_SMNADDR_BYTE1;
        break;
      case L2_MCA_TYPE:
        CoreMcaSmnAddrByte1 = L2_THR0_SMNADDR_BYTE1;
        break;
      case DE_MCA_TYPE:
        CoreMcaSmnAddrByte1 = DE_THR0_SMNADDR_BYTE1;
        break;
      case EX_MCA_TYPE:
        CoreMcaSmnAddrByte1 = EX_THR0_SMNADDR_BYTE1;
        break;
      case FP_MCA_TYPE:
        CoreMcaSmnAddrByte1 = FP_THR0_SMNADDR_BYTE1;
        break;
      default: // Should not be here
        CoreMcaSmnAddrByte1 = 0;
        break;
    }

    McaIpidMsr.Field.InstanceId = (((0x2000 | ((CcdId * 8) << 4) | ((CcxId * 4) << 4) | (CoreId * 2)) << 16) | ((CoreMcaSmnAddrByte1 + ThreadId) << 8));
    McaIpidMsr.Field.InstanceIdHi = SocketId;

    if (SocFamilyIdentificationCheck (F1A_STX1_RAW_ID) || SocFamilyIdentificationCheck (F1A_KRK1_RAW_ID)) {
      McaIpidMsr.Field.InstanceId |= 0x00800000; // STX base is 0x20800000
    }

    AsmWriteMsr64 ((MCA_EXTENSION_BASE + ((CoreMcaBankIndex * SMCA_REG_PER_BANK) | MCA_IPID_OFFSET)), McaIpidMsr.Value);
    IDS_HDT_CONSOLE (MAIN_FLOW, "  MSR %x set %x\n", (MCA_EXTENSION_BASE + ((CoreMcaBankIndex * SMCA_REG_PER_BANK) | MCA_IPID_OFFSET)), McaIpidMsr.Value);
  }

  return;
}

EFI_STATUS
CollectCpuMap (
  OUT MCA_MP_FUNCTION_PARAM *McaMpFuncParam
  )
{
  AMD_CORE_TOPOLOGY_SERVICES_V3_PROTOCOL  *CoreTopology;
  CPU_INFO                                *RasCpuMap;
  UINT32                                  Index;
  UINTN                                   SocketLoop;
  UINTN                                   DieLoop;
  UINTN                                   CcdLoop;
  UINTN                                   ComplexLoop;
  UINTN                                   CoreLoop;
  UINTN                                   ThreadLoop;
  UINTN                                   PhySocket;
  UINTN                                   PhyDie;
  UINTN                                   PhyCcd;
  UINTN                                   PhyComplex;
  UINTN                                   PhyCore;
  EFI_STATUS                              Status = EFI_SUCCESS;
  CORE_TOPOLOGY_ITERATION_RESULT          IterationResult;

  McaMpFuncParam->RasCpuMap = NULL;
  CoreTopology = NULL;

  Status = gBS->LocateProtocol (&gAmdCoreTopologyServicesV3ProtocolGuid, NULL, (VOID **)&CoreTopology);
  if (EFI_ERROR (Status)) {
    return Status;
  }

  //
  //  Allocate memory and Initialize for Data block
  //
  Status = gBS->AllocatePool (
                  EfiReservedMemoryType,
                  sizeof (CPU_INFO) * RAS_MAX_CORES,
                  (VOID **)&RasCpuMap
                  );
  if (EFI_ERROR (Status)) {
    return Status;
  }
  ZeroMem (RasCpuMap, sizeof (CPU_INFO) * RAS_MAX_CORES);

  Index = 0;
  CORE_TOPOLOGY_V3_FOR_EACH_THREAD (CoreTopology, IterationResult, SocketLoop, DieLoop, CcdLoop, ComplexLoop, CoreLoop, ThreadLoop) {
    if (Index == RAS_MAX_CORES) {
      ASSERT (FALSE);
      break;
    }
    PhySocket = SocketLoop;
    PhyDie = DieLoop;
    PhyCcd = CcdLoop;
    PhyComplex = ComplexLoop;
    PhyCore = CoreLoop;
    if (CoreTopology->LogicalToPhysicalLocation (CoreTopology, &PhySocket, &PhyDie, &PhyCcd, &PhyComplex, &PhyCore) == EFI_SUCCESS) {
      RasCpuMap[Index].ProcessorNumber = Index;    //CPU Logic Number
      RasCpuMap[Index].SocketId = (UINT8)(PhySocket & 0xFF);
      RasCpuMap[Index].DieId = (UINT8)(PhyCcd & 0xFF);
      RasCpuMap[Index].CcxId = (UINT8)(PhyComplex & 0xFF);
      RasCpuMap[Index].CoreId = (UINT8)(PhyCore & 0xFF);
      RasCpuMap[Index].ThreadID = (UINT8)ThreadLoop;
      Index++;
    }
  }

  // Update Ras CPU map pointer to AMD RAS Policy buffer.
  McaMpFuncParam->RasCpuMap = RasCpuMap;

  return EFI_SUCCESS;
}

