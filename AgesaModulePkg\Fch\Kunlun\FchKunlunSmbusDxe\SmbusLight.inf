#;*****************************************************************************
#;
#; Copyright (C) 2008-2024 Advanced Micro Devices, Inc. All rights reserved.
#;
#;******************************************************************************

[defines]
  INF_VERSION                    = 0x00010005
  BASE_NAME                      = FchSmbusDxe
  FILE_GUID                      = 42aaa06f-b219-42a3-889e-413053ed10ae
  MODULE_TYPE                    = DXE_DRIVER
  VERSION_STRING                 = 1.0
  ENTRY_POINT                    = InstallAmdSmbusLightEntryPoint

[Sources]
  SmbusLight.c
  SmbusLight.h

[LibraryClasses.common.DXE_DRIVER]
  BaseLib
  UefiLib
  HobLib
  PrintLib

[LibraryClasses]
  UefiDriverEntryPoint
  UefiBootServicesTableLib
  DebugLib
  AmdBaseLib

[Guids]

[Protocols]
  gEfiSmbusHcProtocolGuid           #PRODUCED
  gEfiMetronomeArchProtocolGuid     #CONSUMED

[Packages]
  MdePkg/MdePkg.dec
  AgesaModulePkg/AgesaModuleFchPkg.dec
  AgesaModulePkg/Fch/Kunlun/FchKunlun.dec

[Depex]
  gEfiMetronomeArchProtocolGuid
  AND
  gEfiCpuIo2ProtocolGuid
  AND
  gAmdFchKLSmbusDepexProtocolGuid



