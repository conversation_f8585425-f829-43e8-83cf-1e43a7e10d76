/*****************************************************************************
*
 * Copyright (C) 2008-2024 Advanced Micro Devices, Inc. All rights reserved.
 *
*******************************************************************************
*
*/
#ifndef _SMU_BRH_MSG_DEF_H_
#define _SMU_BRH_MSG_DEF_H_
#define INVALID_SMU_MESSAGE                  0xFF

// SMU Response Codes:
#define BIOSSMC_Result_OK                    0x1
#define BIOSSMC_Result_Failed                0xFF
#define BIOSSMC_Result_UnknownCmd            0xFE
#define BIOSSMC_Result_CmdRejectedPrereq     0xFD
#define BIOSSMC_Result_CmdRejectedBusy       0xFC

// Message Definitions:
#define BIOSSMC_MSG_TestMessage                         0x1
#define BIOSSMC_MSG_GetSmuVersion                       0x2
#define BIOSSMC_MSG_EnableSmuFeatures                   0x3
#define BIOSSMC_MSG_DisableSmuFeatures                  0x4
#define BIOSSMC_MSG_SetBiosDramAddr                     0x5
#define BIOSSMC_MSG_SetToolsDramAddr                    0x6
#define BIOSSMC_MSG_SetDebugDramAddr                    0x7
#define BIOSSMC_MSG_CxlSpeedNotification                0x8
#define BIOSSMC_MSG_EnableAer                           0x9
#define BIOSSMC_MSG_UsbInit                             0xA
#define BIOSSMC_MSG_SleepEntry                          0xB
#define BIOSSMC_MSG_EnableDSMWorkaround                 0xC
#define BIOSSMC_MSG_GetNameString                       0xD
#define BIOSSMC_MSG_GetClusterOnDieMode                 0xE
#define BIOSSMC_MSG_GetPerSrcBistPF                     0xF
#define BIOSSMC_MSG_TransferBiosIfTableToSmu            0x10
#define BIOSSMC_MSG_spare2                              0x11
#define BIOSSMC_MSG_spare3                              0x12
#define BIOSSMC_MSG_UsbConfigUpdate                     0x13
#define BIOSSMC_MSG_UsbS3Entry                          0x14
#define BIOSSMC_MSG_UsbS3Exit                           0x15
#define BIOSSMC_MSG_spare4                              0x16
#define BIOSSMC_MSG_spare5                              0x17
#define BIOSSMC_MSG_spare6                              0x18
#define BIOSSMC_MSG_spare7                              0x19
#define BIOSSMC_MSG_spare8                              0x1A
#define BIOSSMC_MSG_spare9                              0x1B
#define BIOSSMC_MSG_spare10                             0x1C
#define BIOSSMC_MSG_spare11                             0x1D
#define BIOSSMC_MSG_spare12                             0x1E
#define BIOSSMC_MSG_spare13                             0x1F
#define BIOSSMC_MSG_spare14                             0x20
#define BIOSSMC_MSG_ColdResetEntry                      0x21
#define BIOSSMC_MSG_EnableSCM                           0x22
#define BIOSSMC_MSG_TDP_TJMAX                           0x23
#define BIOSSMC_MSG_EnableOverclocking                  0x24
#define BIOSSMC_MSG_DisableOverclocking                 0x25
#define BIOSSMC_MSG_SetOverclockFreqAllCores            0x26
#define BIOSSMC_MSG_SetOverclockFreqPerCore             0x27
#define BIOSSMC_MSG_SetOverclockVID                     0x28
#define BIOSSMC_MSG_SetBoostLimitFrequency              0x29
#define BIOSSMC_MSG_spare15                             0x2A
#define BIOSSMC_MSG_SetBoostLimitFrequencyAllCores      0x2B
#define BIOSSMC_MSG_GetOverclockCap                     0x2C
#define BIOSSMC_MSG_spare16                             0x2D
#define BIOSSMC_MSG_spare17                             0x2E
#define BIOSSMC_MSG_SetFITLimitScalar                   0x2F
#define BIOSSMC_MSG_spare18                             0x30
#define BIOSSMC_MSG_spare19                             0x31
#define BIOSSMC_MSG_spare20                             0x32
#define BIOSSMC_MSG_PBO_EN                              0x33
#define BIOSSMC_MSG_SetLclkDpmLevelRange                0x34
#define BIOSSMC_MSG_SetDldoPsmMargin                    0x35
#define BIOSSMC_MSG_SetAllDldoPsmMargin                 0x36
#define BIOSSMC_MSG_FllBootTimeCalibration              0x37
#define BIOSSMC_MSG_spare21                             0x38
#define BIOSSMC_MSG_GetCPPCSupportedRegisters           0x39
#define BIOSSMC_MSG_GetCPPCNominalFrequency             0x3A
#define BIOSSMC_MSG_SetResetCpuOnSyncFlood              0x3B
#define BIOSSMC_MSG_SetTDCLimit                         0x3C
#define BIOSSMC_MSG_SetEDCLimit                         0x3D
#define BIOSSMC_MSG_SetPPTLimit                         0x3E
#define BIOSSMC_MSG_SetTjMax                            0x3F
#define BIOSSMC_MSG_GetCPPCLowestFrequency              0x40
#define BIOSSMC_MSG_EnableHSMPInterrupts                0x41
#define BIOSSMC_MSG_SetSyncFloodToApml                  0x42
#define BIOSSMC_MSG_SetS0i3PmeTurnOffDelay              0x43
#define BIOSSMC_MSG_SetStbLogDramAddr                   0x44
#define BIOSSMC_MSG_spare22                             0x45
#define BIOSSMC_MSG_UsbControllerDisabled               0x46
#define BIOSSMC_MSG_StartDimmTelemetryReading           0x47
#define BIOSSMC_MSG_StopDimmTelemetryReading            0x48
#define BIOSSMC_MSG_PowerGateMmHub                      0x49
#define BIOSSMC_MSG_SetEdcExcursionReporting            0x4A
#define BIOSSMC_MSG_GetDimmTelementryStatus             0x4B
#define BIOSSMC_MSG_EnableNVMSelfRefresh                0x4C
#define BIOSSMC_MSG_SetDelayResetCpuOnSyncFlood         0x4D
#define BIOSSMC_MSG_EnableSPGUnusedPcie                 0x4E
#define BIOSSMC_MSG_PcdAmdMemEcsStatusInterrupt         0x4F
#define BIOSSMC_MSG_GetPowerManagementFuse              0x50
#define BIOSSMC_MSG_DebugLogValidity                    0x51
#define BIOSSMC_MSG_DebugLogDump                        0x52
#define BIOSSMC_MSG_EnableOOBPCIeConfigAccess           0x53
#define BIOSSMC_Message_Count                           0x54

#endif

