/*****************************************************************************
 * Copyright (C) 2008-2024 Advanced Micro Devices, Inc. All rights reserved.
 *
*****************************************************************************
*/
/* $NoKeywords:$ */
/**
 * @file
 *
 * AMD FCH SMM APU RAS SMI Dispatcher Driver
 *
 *
 * @xrefitem bom "File Content Label" "Release Content"
 * @e project:      AGESA
 * @e sub-project:  UEFI
 * @e \$Revision: 309090 $   @e \$Date: 2014-12-09 10:28:05 -0800 (Tue, 09 Dec 2014) $
 *
 */

#ifndef _FCH_SMM_APU_RAS_DISPATCHER_H_
#define _FCH_SMM_APU_RAS_DISPATCHER_H_


#include "FchSmmDispatcher.h"

#include <Protocol/FchSmmApuRasDispatch.h>

extern  FCH_SMM_APURAS_DISPATCH_PROTOCOL gFchSmmApuRasDispatchProtocol;


EFI_STATUS
EFIAPI
FchSmmApuRasDispatchHandler (
  IN      EFI_HANDLE     SmmImageHandle,
  IN OUT  VOID           *CommunicationBuffer OPTIONAL,
  IN OUT  UINTN          *SourceSize OPTIONAL
  );

///
/// RAS SMI Node
///
typedef struct _FCH_SMM_APURAS_NODE {
  EFI_HANDLE                                 DispatchHandle;         ///< Dispatch Handle
  FCH_SMM_APURAS_REGISTER_CONTEXT            Context;                ///< Register context
  FCH_SMM_APURAS_HANDLER_ENTRY_POINT         CallBackFunction;       ///< SMM handler entry point
  struct _FCH_SMM_APURAS_NODE                *FchApuRasNodePtr;      ///< pointer to next node
} FCH_SMM_APURAS_NODE;

extern  FCH_SMM_APURAS_NODE                  *HeadFchSmmApuRasNodePtr;
#endif



