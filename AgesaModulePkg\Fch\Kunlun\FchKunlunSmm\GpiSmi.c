/*****************************************************************************
 * Copyright (C) 2008-2024 Advanced Micro Devices, Inc. All rights reserved.
 *
*****************************************************************************
*/
/* $NoKeywords:$ */
/**
 * @file
 *
 * FCH SMM Driver
 *
 *
 * @xrefitem bom "File Content Label" "Release Content"
 * @e project:      AGESA
 * @e sub-project   FCH SMM Driver
 * @e \$Revision: 309090 $   @e \$Date: 2014-12-09 10:28:05 -0800 (Tue, 09 Dec 2014) $
 *
 */
#include "FchSmm.h"
#define FILECODE FCH_KUNLUN_FCHKUNLUNSMM_GPISMI_FILECODE

UINT32  mGpiSmiCounter;

EFI_STATUS
EFIAPI
AmdGpiSmiCallback (
  IN       EFI_HANDLE                         DispatchHandle,
  IN       CONST EFI_SMM_GPI_REGISTER_CONTEXT *GpiRegisterContext,
  IN OUT   EFI_SMM_GPI_REGISTER_CONTEXT       *CurrentContext,
  IN OUT   UINTN                              *SizeOfContext
  )
{
  LibFchSmmIoWrite (&gSmst->SmmIo, SMM_IO_UINT8, 0x80, &mGpiSmiCounter);
  mGpiSmiCounter++;
  return EFI_SUCCESS;
}


EFI_STATUS
FchSmmRegisterGpiSmi (
  VOID
  )
{
  EFI_STATUS                           Status;
  FCH_SMM_GPI_DISPATCH2_PROTOCOL       *AmdGpiDispatch;
//  EFI_SMM_GPI_REGISTER_CONTEXT         GpiRegisterContext;
//  EFI_HANDLE                           GpiHandle;

  //
  // Smi GPI test
  //
  Status = gSmst->SmmLocateProtocol (
                  &gFchSmmGpiDispatch2ProtocolGuid,
                  NULL,
                  (VOID **)&AmdGpiDispatch
                  );
  ASSERT_EFI_ERROR (Status);

//
// Sample code for Gpi SMI callback register.
//
/*
  mGpiSmiCounter = 0;
  GpiRegisterContext.GpiNum = 0;
  Status = AmdGpiDispatch->Register (
                             AmdGpiDispatch,
                             AmdGpiSmiCallback,
                             &GpiRegisterContext,
                             &GpiHandle
                             );
*/
  return Status;
}





