/*****************************************************************************
 * Copyright (C) 2008-2024 Advanced Micro Devices, Inc. All rights reserved.
 *
*****************************************************************************
*/
/* $NoKeywords:$ */
/**
 * @file
 *
 * Config Fch ESPI controller
 *
 * Init ESPI features.
 *
 * @xrefitem bom "File Content Label" "Release Content"
 * @e project:     AGESA
 * @e sub-project: FCH
 * @e \$Revision: 309083 $   @e \$Date: 2014-12-09 09:28:24 -0800 (Tue, 09 Dec 2014) $
 *
 */
#include "FchPlatform.h"
#include "Filecode.h"
#define FILECODE FCH_KUNLUN_FCHKUNLUNCORE_KUNLUN_KLESPI_KLESPILIB_FILECODE

#define MAX_ESPI_RETRY     100000          // 100ms

#define SET_CONFIGURATION  0
#define GET_CONFIGURATION  1
#define IN_BAND_RESET      2
#define PC_MSG_DOWN_STREAM 4
#define VM_DOWN_STREAM     5
#define OOB_DOWN_STREAM    6

#define SLAVE_REG_ID           0x04
#define SLAVE_GENERAL_CAPCFG   0x08
#define SLAVE_PC_CAPCFG        0x10
#define SLAVE_VW_CAPCFG        0x20
#define SLAVE_OOB_CAPCFG       0x30
#define SLAVE_FA_CAPCFG        0x40
#define SLAVE_MEC1701_SCRATCH_REG 0x80      // This is a scratch register of MEC1701 eSPI and does nothing.

//ESPIx00
#define DNCMD_STATUS       BIT3

//ESPIx2C Master Capability
#define MASTER_FA_SUPPORT                   BIT0
#define MASTER_OOB_SUPPORT                  BIT1
#define MASTER_VW_SUPPORT                   BIT2
#define MASTER_PERIPHERAL_SUPPORT           BIT3

//ESPIx68  Slave0 Configuration
#define SLAVE_FA_ENABLE                     BIT0
#define SLAVE_OOB_ENABLE                    BIT1
#define SLAVE_VW_ENABLE                     BIT2
#define SLAVE_PC_ENABLE             BIT3

//SLAVE offset 0x08   SLAVE_GENERAL_CAPCFG
#define SLAVE_FA_SUPPORT                    BIT3
#define SLAVE_OOB_SUPPORT                   BIT2
#define SLAVE_VW_SUPPORT                    BIT1
#define SLAVE_PERIPHERAL_SUPPORT            BIT0

/// eSPIx00 eSPI Software Specific Register 0
typedef union {
  struct {
    UINT32      SWCommandType:3;
    UINT32      CommandStatus:1;
    UINT32      SlaveNSelect:2;
    UINT32      Reserved_7_6:2;
    UINT32      AddrByte0PCycTypeVWCntOOBCycType:8;
    UINT32      AddrByte0VWIdxOOBPktByte1:8;
    UINT32      VWDataOOBPktByte2:8;
  } Field;
  UINT32 Value;
} ESPIx00_DN_DXHDR0;

/// eSPIx04 eSPI Software Specific Register 1
typedef union {
  struct {
    UINT32      VWIdxOOBPktbyte3:8;
    UINT32      VWDataOOBByteCnt:8;
    UINT32      VWIdxOOBPktByte5:8;
    UINT32      VWDataOOBPktByte6:8;
  } Field;
  UINT32 Value;
} ESPIx04_DN_DXHDR1;

/// eSPIx2C eSPI Master Capability
typedef union {
  struct {
    UINT32      FlashAccessChannelSupport:1;
    UINT32      OOBMessageChannelSupport:1;
    UINT32      VWChannelSupport:1;
    UINT32      PChannelSupport:1;
    UINT32      MasterVersion:3;
    UINT32      FlashAccessChannelMaxPayload:3;
    UINT32      OOBMessageChannelMaxPayload:3;
    UINT32      OperatingMaxVWCount:6;
    UINT32      PChannelMaxPayloadSize:3;
    UINT32      NumberOfSlave:3;
    UINT32      OperatingSupportFreq:3;
    UINT32      IOMode:2;
    UINT32      AlertMode:1;
    UINT32      CRCCheck:1;
  } Field;
  UINT32 Value;
} ESPIx2C_MASTER_CAP;

/// eSPIx68 eSPI Slave N Configuration
typedef union {
  struct {
    UINT32      FlashAccessChannelEnable:1;
    UINT32      OOBMessageChannelEnable:1;
    UINT32      VWChannelEnable:1;
    UINT32      PChannelEnable:1;
    UINT32      OOBValidBitEnable:1;
    UINT32      Reserved_24_5:20;
    UINT32      OperatingFreq:3;
    UINT32      IOModeSelect:2;
    UINT32      AlertMode:1;
    UINT32      CRCCheckingEnable:1;
  } Field;
  UINT32 Value;
} ESPIx68_SLAVE0_CONFIG;

///
/// Slave Registers
///
/// Offset 04h: Device Identification
typedef union {
  struct {
    UINT32      RO_VersionID:8;
    UINT32      Reserved_31_8:24;
  } Field;
  UINT32 Value;
} ESPI_SL04_DEVICE_ID;

/// Offset 08h: General Capabilities and Configurations
typedef union {
  struct {
    UINT32      RO_PCSupported:1;
    UINT32      RO_VWSupported:1;
    UINT32      RO_OOBMsgSupported:1;
    UINT32      RO_FASupported:1;
    UINT32      Reserved_7_3:4;
    UINT32      Reserved_11_8:4;
    UINT32      RO_MaxWaitStateAllowed:4;
    UINT32      RO_MaxFreqSupported:3;
    UINT32      RO_OpenDrainAlertSupported:1;
    UINT32      OperatingFreq:3;
    UINT32      OpenDrainAlertSelect:1;
    UINT32      RO_IOModeSupported:2;
    UINT32      IOModeSelect:2;
    UINT32      AlertMode:1;
    UINT32      Reserved_29:1;
    UINT32      ResponseModifierEn:1;
    UINT32      CRCCheckingEn:1;
  } Field;
  UINT32 Value;
} ESPI_SL08_SLAVE_GENERAL_CAPCFG;

/// Offset 10h: Channel 0 Capabilities and Configurations
typedef union {
  struct {
    UINT32      PCEn:1;
    UINT32      RO_PCReady:1;
    UINT32      BusMasterEn:1;
    UINT32      Reserved_3:1;
    UINT32      RO_PCMaxPayloadSizeSupported:3;
    UINT32      Reserved_7:1;
    UINT32      PCMaxPayloadSizeSelected:3;
    UINT32      Reserved_11:1;
    UINT32      PCMaxReadRequestSize:3;
    UINT32      Reserved_31_15:17;
  } Field;
  UINT32 Value;
} ESPI_SL10_SLAVE_PC_CAPCFG;

/// Offset 20h: Channel 1 Capabilities and Configurations
typedef union {
  struct {
    UINT32      VWEn:1;
    UINT32      RO_VWReady:1;
    UINT32      Reserved_7_2:6;
    UINT32      RO_MaxVWCntSupported:6;
    UINT32      Reserved_15_14:2;
    UINT32      OpMaxVWCnt:6;
    UINT32      Reserved_31_22:10;
  } Field;
  UINT32 Value;
} ESPI_SL20_SLAVE_VW_CAPCFG;

/// Offset 30h: Channel 2 Capabilities and Configurations
typedef union {
  struct {
    UINT32      OOBEn:1;
    UINT32      RO_OOBReady:1;
    UINT32      Reserved_3_2:2;
    UINT32      RO_MsgChMaxPayloadSizeSupported:3;
    UINT32      Reserved_7:1;
    UINT32      MsgChMaxPayloadSizeSelected:3;
    UINT32      Reserved_31_11:21;
  } Field;
  UINT32 Value;
} ESPI_SL30_SLAVE_OOB_CAPCFG;

/// Offset 40h: Channel 2 Capabilities and Configurations
typedef union {
  struct {
    UINT32      FAEn:1;
    UINT32      RO_FAReady:1;
    UINT32      FlashBlockEraseSize:3;
    UINT32      RO_ChMaxPayloadSizeSupported:3;
    UINT32      ChMaxPayloadSizeSelected:3;
    UINT32      RO_FlashSharingMode:1;
    UINT32      ChMaxReadReqSize:3;
    UINT32      Reserved_31_15:17;
  } Field;
  UINT32 Value;
} ESPI_SL40_SLAVE_FA_CAPCFG;


UINT32 getESPIBase ()
{
  UINT32 base = 0xFEC20000;
  //ReadPci ((LPC_BUS_DEV_FUN << 16) + 0xA0, AccessWidth32, &base, NULL);
  //base &= 0xFFFFFFC0;
  //base += 0x00010000;

  // eSPI Master Capability
  if ( ACPIMMIO32 (base + 0x2C) == 0 || ACPIMMIO32 (base + 0x2C) == 0xFFFFFFFF) {
    ASSERT (FALSE);
  }

  return base;
}

UINT32 getESPI1Base ()
{
  UINT32 base = 0xFEC30000;
  //ReadPci ((LPC_BUS_DEV_FUN << 16) + 0xA0, AccessWidth32, &base, NULL);
  //base &= 0xFFFFFFC0;
  //base += 0x00020000;

  // eSPI Master Capability
  if ( ACPIMMIO32 (base + 0x2C) == 0 || ACPIMMIO32 (base + 0x2C) == 0xFFFFFFFF) {
    ASSERT (FALSE);
  }

  return base;
}
/*
wait4stClear ()
{
    while(eSPIx00.Field.CommandStatus) {
        IoWrite16(0x80, 0xE291); // delay
        IoRead8(0x404);          // delay
    }
}
*/
VOID
wait4stClear (
  IN  UINT32     EspiBase
  )
{
  UINT32 Retry;

  for ( Retry = 0; Retry < MAX_ESPI_RETRY; Retry++ ) {
    if ( (ACPIMMIO32 (EspiBase + 0x00) & DNCMD_STATUS) == 0 ) {
      break;
    }
    //FchStall (2, NULL);
    IoWrite16(0x80, 0xE291); // delay
  }
}
/*
eSPI_InBandRst (UINT32 slave)
{
    eSPIx00.Field.CommandStatus = 0;
    wait4stClear();
    eSPIx00.Field.SlaveNSelect = slave;
    eSPIx68.Field.OperatingFreq = 0;
    eSPIx00.Field.SWCommandType = IN_BAND_RESET;
    eSPIx00.Field.CommandStatus = 1;
    wait4stClear();
}
*/
VOID
KLeSPI_InBandRst  (
  IN  UINT32     EspiBase
  )
{
  RwMem (EspiBase + 0x00, AccessWidth32, ~(UINT32)BIT3, 0x00);
  wait4stClear (EspiBase);
  RwMem (EspiBase + 0x00, AccessWidth32, ~(UINT32) (BIT4 + BIT5), 0x00);
  RwMem (EspiBase + 0x68, AccessWidth32, 0xF1FFFFFF, 0x00);              // [27:25] CLK_FREQ_SEL
  RwMem (EspiBase + 0x00, AccessWidth32, 0xFFFFFFF8, IN_BAND_RESET);     // [2:0] DNCMD_TYPE
  RwMem (EspiBase + 0x00, AccessWidth32, ~(UINT32)BIT3, BIT3);
  wait4stClear (EspiBase);
}

/*
UINT32 eSPI_GetConfiguration(UINT32 slave, UINT16 offset)
{
    wait4stClear();
    eSPIx00.Field.SlaveNSelect = slave;
    eSPIx00.Field.SWCommandType = GET_CONFIGURATION;
    eSPIx00.Field.AddrByte0PCycTypeVWCntOOBCycType = ((offset >> 8) & 0xFF);
    eSPIx00.Field.AddrByte0VWIdxOOBPktByte1 = (offset & 0xFF);
    eSPIx04.Value = 0;
    eSPIx00.Field.CommandStatus = 1;
    wait4stClear();
    return eSPIx04.Value;
}
*/
UINT32
KLeSPI_GetConfiguration  (
  IN  UINT32     EspiBase,
  IN  UINT32     RegAddr
  )
{
  wait4stClear (EspiBase);
  RwMem (EspiBase + 0x00, AccessWidth32, ~(UINT32) (BIT4 + BIT5), 0x00);
  RwMem (EspiBase + 0x00, AccessWidth32, 0xFFFFFFF8, GET_CONFIGURATION);     // [2:0] DNCMD_TYPE
  RwMem (EspiBase + 0x00, AccessWidth32, 0xFF00FFFF, (RegAddr & 0xFF) << 16);
  RwMem (EspiBase + 0x00, AccessWidth32, 0xFFFF00FF, RegAddr & 0xFF00);
  RwMem (EspiBase + 0x04, AccessWidth32, 0x00, 0x00);
  RwMem (EspiBase + 0x00, AccessWidth32, ~(UINT32)BIT3, BIT3);
  wait4stClear (EspiBase);
  return ACPIMMIO32 (EspiBase + 0x04);
}

/*
eSPI_SetConfiguration (UINT32 slave, UINT32 offset, UINT32 value)
{
    wait4stClear();
    eSPIx00.Field.SlaveNSelect = slave;
    eSPIx00.Field.SWCommandType = SET_CONFIGURATION;
    eSPIx00.Field.AddrByte0PCycTypeVWCntOOBCycType = ((offset >> 8) & 0xFF);
    eSPIx00.Field.AddrByte0VWIdxOOBPktByte1 = (offset & 0xFF);
    eSPIx04.Value = value;
    eSPIx00.Field.CommandStatus = 1;
    wait4stClear();
}
*/
VOID
KLeSPI_SetConfiguration  (
  IN  UINT32     EspiBase,
  IN  UINT32     RegAddr,
  IN  UINT32     Value
  )
{
  wait4stClear (EspiBase);
  RwMem (EspiBase + 0x00, AccessWidth32, ~(UINT32) (BIT4 + BIT5), 0x00);
  RwMem (EspiBase + 0x00, AccessWidth32, 0xFFFFFFF8, SET_CONFIGURATION);     // [2:0] DNCMD_TYPE
  RwMem (EspiBase + 0x00, AccessWidth32, 0xFF00FFFF, (RegAddr & 0xFF) << 16);
  RwMem (EspiBase + 0x00, AccessWidth32, 0xFFFF00FF, RegAddr & 0xFF00);
  RwMem (EspiBase + 0x04, AccessWidth32, 0x00, Value);
  RwMem (EspiBase + 0x00, AccessWidth32, ~(UINT32)BIT3, BIT3);
  wait4stClear (EspiBase);
}

/**
 * FchEspiSendSmiOutbVW - Send SmiOutB VW
 *
 *
 *
 * @param[in] EspiBase  eSPI Base MMIO address.
 * @param[in] Smib_Type SmiOutB assertion or de-assertion.
 */
VOID
FchEspiSendSmiOutbVW (
  IN  UINT32     EspiBase,
  IN  UINT32     Smib_Type
  )
{
  UINT32  Index;
  UINT32  Data;
  UINT32  RegData;

  wait4stClear (EspiBase);

  // program Header 0 to VW
  // set command to VW; bit 2:0// CommandType = 5;
  // bit 3// CommandStatus = 0;
  // byte 0, bits 7:4// B0b74 = 0;
  // byte 1; VW count// VWcount = 0x00;
  // Byte2 = 0x00;
  // Byte3 = 0x00;
  RwMem (EspiBase + 0x00, AccessWidth32, 0x00000000, VM_DOWN_STREAM);     // [2:0] DNCMD_TYPE

  // write ESPI HEADER1 and HEADER2
  // WR_SMN( DN_TXHDR_1, 0x00000000 ); //
  // WR_SMN( DN_TXHDR_2, 0x00000000 ); //
  RwMem (EspiBase + 0x04, AccessWidth32, 0x00000000, 0x00000000);
  RwMem (EspiBase + 0x08, AccessWidth32, 0x00000000, 0x00000000);

  // configure it index 7
  Index = 7; //0x7;
  switch ( Smib_Type ) {
    case 0x20: Data = 0x20; break;  // smib assert
    case 0x22: Data = 0x22; break;  // smib de-assert
    default  : Data = 0x00; break;
  }

  // data == 0x00 means there is something wrong, we should just skip the
  // rest and return
  RegData = ((Data & 0xFF) << 8) | (Index & 0xFF);
  if ( (Data & 0xFF) != 0x00 ) {
    //WR_SMN( DN_TXDATA_PORT, regdata ); //
    //CommandStatus = 0x1; // bit 3
    //Header0 = { Byte3, Byte2,
    //            VWcount, B0b74, CommandStatus, CommandType };
    RwMem (EspiBase + 0x0C, AccessWidth32, 0x00000000, RegData);
    RwMem (EspiBase + 0x00, AccessWidth32, ~(UINT32)BIT3, BIT3);
  }

  wait4stClear (EspiBase);
}


