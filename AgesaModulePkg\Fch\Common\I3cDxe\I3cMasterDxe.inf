#;*****************************************************************************
#;
#; Copyright (C) 2008-2024 Advanced Micro Devices, Inc. All rights reserved.
#;
#;******************************************************************************

[defines]

  INF_VERSION                   = 0x00010005
  BASE_NAME                     = FchI3cDxe
  FILE_GUID                     = d62e673f-a935-4751-9279-4c1e63ed0a4e
  MODULE_TYPE                   = DXE_DRIVER
  VERSION_STRING                = 1.0
  ENTRY_POINT                   = AmdI3cMasterDxeInit


[sources.common]
  I3cMasterDxe.c
  I3cMasterDxe.h

[LibraryClasses.common.DXE_DRIVER]
  BaseLib
  UefiLib
  HobLib
  PrintLib

[LibraryClasses]
  FchBaseLib
  FchI3cLib
  FchSocLib
  FabricRegisterAccLib
  BaseFabricTopologyLib
  UefiDriverEntryPoint
  UefiBootServicesTableLib
  DebugLib
  ApobCommonServiceLib
  AmdPspBaseLibV2

[Guids]

[Protocols]
  gAmdFchSNI3cProtocolGuid          #PRODUCED
  gAmdApcbDxeServiceProtocolGuid    #CONSUMED
  gAmdFabricTopologyServices2ProtocolGuid
[Ppis]

[Packages]
  MdePkg/MdePkg.dec
  AgesaPkg/AgesaPkg.dec
  AgesaModulePkg/AgesaModuleFchPkg.dec
  AgesaModulePkg/AgesaCommonModulePkg.dec
  AgesaModulePkg/AgesaModuleDfPkg.dec

[Pcd]
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdAmdEnvironmentFlag
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdFchI3CDxeDebugOff
[Depex]
  gAmdApcbDxeServiceProtocolGuid


