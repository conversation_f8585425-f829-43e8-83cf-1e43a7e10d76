/*****************************************************************************
 *
 * Copyright (C) 2008-2024 Advanced Micro Devices, Inc. All rights reserved.
 *
 *******************************************************************************
 */

#include "FchEspiCmdDxe.h"

#define FILECODE FCH_COMMON_FCHESPICMDDXE_FCHESPICMDDXE_FILECODE

extern EFI_BOOT_SERVICES *gBS;

/**
  * DxeInBandRst - eSPI In Band Reset
  *
  * @param This                   Pointer to an EFI_ESPI_CMD_PROTOCOL structure.
  * @param[in]  EspiBase          Espi MMIO base
  *
  */
VOID
EFIAPI
DxeInBandRst (
  IN CONST EFI_ESPI_CMD_PROTOCOL  *This,
  IN  UINT32                      EspiBase
  )
{
  FchEspiCmd_InBandRst(EspiBase);
}

/**
  *  DxeGetConfiguration - eSPI Get Configuration
  *
  * @param This                   Pointer to an EFI_ESPI_CMD_PROTOCOL structure.
  * @param[in]  EspiBase          Espi MMIO base
  * @param[in]  RegAddr           Slave register address
  *
  * @retval    Register Value
  *
  */
UINT32
EFIAPI
DxeGetConfiguration (
  IN CONST EFI_ESPI_CMD_PROTOCOL  *This,
  IN  UINT32                      EspiBase,
  IN  UINT32                      RegAddr
 )
{
  return (FchEspiCmd_GetConfiguration(EspiBase, RegAddr));
}

/**
  * DxeSetConfiguration - eSPI Set Configuration
  *
  * @param This                   Pointer to an EFI_ESPI_CMD_PROTOCOL structure.
  * @param[in]  EspiBase          Espi MMIO base
  * @param[in]  RegAddr           Slave register address
  * @param[in]  Value             Slave register value
  *
  */
VOID
EFIAPI
DxeSetConfiguration (
  IN CONST EFI_ESPI_CMD_PROTOCOL  *This,
  IN  UINT32                      EspiBase,
  IN  UINT32                      RegAddr,
  IN  UINT32                      Value
)
{
  FchEspiCmd_SetConfiguration(EspiBase, RegAddr, Value);
}

/**
  * DxeSafsFlashRead - eSPI SAFS FLASH Read
  *
  * @param This                   Pointer to an EFI_ESPI_CMD_PROTOCOL structure.
  * @param[in]  EspiBase          Espi MMIO base
  * @param[in]  Address           Address to read
  * @param[in]  Length            Length in byte to read
  * @param[in]  Buffer            Pointer to the data read to
  *
  * @retval EFI_STATUS
  *
  */
EFI_STATUS
EFIAPI
DxeSafsFlashRead (
  IN CONST EFI_ESPI_CMD_PROTOCOL  *This,
  IN  UINT32                      EspiBase,
  IN  UINT32                      Address,
  IN  UINT32                      Length,
  OUT UINT8                       *Buffer
  )
{
  return (FchEspiCmd_SafsFlashRead(EspiBase, Address, Length, Buffer));
}

/**
  * DxeSafsFlashWrite - eSPI SAFS FLASH Write
  *
  * @param  This                  Pointer to an EFI_ESPI_CMD_PROTOCOL structure.
  * @param[in]  EspiBase          Espi MMIO base
  * @param[in]  Address           Address to write
  * @param[in]  Length            Length in byte to write
  * @param[in]  Value             Pointer to the data to write
  *
  * @retval EFI_STATUS
  *
  */
EFI_STATUS
EFIAPI
DxeSafsFlashWrite (
  IN CONST EFI_ESPI_CMD_PROTOCOL  *This,
  IN  UINT32                      EspiBase,
  IN  UINT32                      Address,
  IN  UINT32                      Length,
  IN  UINT8                       *Value
  )
{
  return (FchEspiCmd_SafsFlashWrite(EspiBase, Address, Length, Value));
}

/**
  * DxeSafsFlashErase - eSPI SAFS FLASH Erase
  *
  * @param  This                  Pointer to an EFI_ESPI_CMD_PROTOCOL structure.
  * @param[in]  EspiBase          Espi MMIO base
  * @param[in]  Address           Address to erase
  * @param[in]  Length            Block Size to erase
  *
  *  @retval EFI_STATUS
  *
  */
EFI_STATUS
EFIAPI
DxeSafsFlashErase (
  IN CONST EFI_ESPI_CMD_PROTOCOL  *This,
  IN  UINT32                      EspiBase,
  IN  UINT32                      Address,
  IN  UINT32                      Length
  )
{
  return (FchEspiCmd_SafsFlashErase(EspiBase, Address, Length));
}

/**
  * DxeSafsRpmcOp1 - eSPI SAFS RPMC OP1
  *
  * @param  This                  Pointer to an EFI_ESPI_CMD_PROTOCOL structure.
  * @param[in]  EspiBase          Espi MMIO base
  * @param[in]  RpmcFlashDev      Which RPMC flash device is targeted to
  * @param[in]  Length            Length in byte
  * @param[in]  Data              Pointer to data to send
  *
  *  @retval EFI_STATUS
  *
  */
EFI_STATUS
EFIAPI
DxeSafsRpmcOp1 (
  IN CONST EFI_ESPI_CMD_PROTOCOL  *This,
  IN  UINT32                      EspiBase,
  IN  UINT8                       RpmcFlashDev,
  IN  UINT32                      Length,
  IN  UINT8                       *Data
  )
{
  return (FchEspiCmd_SafsRpmcOp1(EspiBase, RpmcFlashDev, Length, Data));
}

/**
  * DxeSafsRpmcOp2 - eSPI SAFS RPMC OP2
  *
  * @param  This                  Pointer to an EFI_ESPI_CMD_PROTOCOL structure.
  * @param[in]  EspiBase          Espi MMIO base
  * @param[in]  RpmcFlashDev      Which RPMC flash device is targeted to
  * @param[in]  Length            Length in byte
  * @param[in]  Buffer            Pointer to buffer to receive
  *
  *  @retval EFI_STATUS
  *
  */
EFI_STATUS
EFIAPI
DxeSafsRpmcOp2 (
  IN CONST EFI_ESPI_CMD_PROTOCOL  *This,
  IN  UINT32                      EspiBase,
  IN  UINT8                       RpmcFlashDev,
  IN  UINT32                      Length,
  IN  UINT8                       *Buffer
  )
{
  return (FchEspiCmd_SafsRpmcOp1(EspiBase, RpmcFlashDev, Length, Buffer));
}

EFI_ESPI_CMD_PROTOCOL DxeFchEspiCmdProt = {
  DxeInBandRst,
  DxeGetConfiguration,
  DxeSetConfiguration,
  DxeSafsFlashRead,
  DxeSafsFlashWrite,
  DxeSafsFlashErase,
  DxeSafsRpmcOp1,
  DxeSafsRpmcOp2
};

/**
  *  @brief Entry point of the AMD FCH eSPI Command DXE driver
  *  @details Install supported protocol
  *  @param[in] ImageHandle - EFI Image Handle for the DXE driver
  *  @param[in] SystemTable - pointer to the EFI system table
  *  @returns EFI_STATUS
  *  @retval EFI_SUCCESS : Module initialized successfully
  *          EFI_ERROR   : Initialization failed (see error for more details)
  */
EFI_STATUS
EFIAPI
AmdFchEspiCmdDxeInit (
  IN  EFI_HANDLE        ImageHandle,
  IN  EFI_SYSTEM_TABLE  *SystemTable
  )
{
  EFI_STATUS            Status;
  EFI_HANDLE            Handle;

  DEBUG ((EFI_D_INFO, "FchEspiCmdDxe entry point: AmdFchEspiCmdDxeInit.\n"));


  // Install Protocol
  Handle = ImageHandle;
  Status = gBS->InstallProtocolInterface (
                  &Handle,
                  &gAmdFchEspiCmdProtocolGuid,
                  EFI_NATIVE_INTERFACE,
                  &DxeFchEspiCmdProt
                  );

    if (Status != EFI_SUCCESS) {
      DEBUG ((DEBUG_ERROR, "[Warning] InstallProtocolInterface failed (Status: 0x%x).\n", Status));
    }
  return (Status);
}
