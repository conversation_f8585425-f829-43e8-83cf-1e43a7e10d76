#;*****************************************************************************
#;
#; Copyright (C) 2008-2024 Advanced Micro Devices, Inc. All rights reserved.
#;
#;******************************************************************************
[Defines]
  INF_VERSION                    = 0x00010005
  BASE_NAME                      = FchSpiAccessRom3Lib
  FILE_GUID                      = a9339373-6345-4815-a616-a16fcc61d7ca
  MODULE_TYPE                    = DXE_DRIVER
  VERSION_STRING                 = 1.0
  LIBRARY_CLASS                  = FchSpiAccessLib|DXE_DRIVER DXE_CORE DXE_RUNTIME_DRIVER UEFI_DRIVER SMM_CORE DXE_SMM_DRIVER UEFI_APPLICATION

#
# The following information is for reference only and not required by the build tools.
#
#  VALID_ARCHITECTURES           = IA32 X64
#
#
#  This instance for 32MB and smaller size SPI ROM READ on DXE phase, do not support runtime yet.

[Sources]
  FchSpiAccessCommon.c
  FchSpiAccessRom3Lib.c

[Packages]
  AgesaModulePkg/AgesaModuleFchPkg.dec
  MdePkg/MdePkg.dec
  AgesaPkg/AgesaPkg.dec

[LibraryClasses]
  IoLib
  PciLib
  FchBaseLib
  BaseMemoryLib
  AmdBaseLib

