/*****************************************************************************
 * Copyright (C) 2008-2024 Advanced Micro Devices, Inc. All rights reserved.
 *
*****************************************************************************
*/
/* $NoKeywords:$ */
/**
 * @file
 *
 * APCB service PPI prototype definition
 *
 *
 * @xrefitem bom "File Content Label" "Release Content"
 * @e project:      AGESA
 * @e sub-project:  APCB
 * @e \$Revision: 309090 $   @e \$Date: 2014-12-10 02:28:05 +0800 (Wed, 10 Dec 2014) $
 */


#ifndef _AMD_APCB_SERVICE_PROTOCOL_H_
#define _AMD_APCB_SERVICE_PROTOCOL_H_

#include "ApcbCommon.h"
#include "Library/ApcbVariableLibV3.h"
/*----------------------------------------------------------------------------------------
 *                    T Y P E D E F S     A N D     S T R U C T U R E S
 *                            COMPONENT Locality Distance Information Table
 *----------------------------------------------------------------------------------------
 */

/*----------------------------------------------------------------------------------------
 *                 D E F I N I T I O N S     A N D     M A C R O S
 *----------------------------------------------------------------------------------------
 */

#define       APCB_PROTOCOL_VERSION_2_0          2 ///< APCB protocol version
#define       APCB_PROTOCOL_VERSION_3_0          3 ///< APCB protocol version

///
/// Forward declaration for the AMD_APCB_SERVICE_PROTOCOL.
///
typedef struct _AMD_APCB_SERVICE_PROTOCOL AMD_APCB_SERVICE_PROTOCOL;

/**
 * @brief Function used to set active APCB instance
 *
 * @param[in]         This                    Point to AMD_APCB_SERVICE_PROTOCOL itself
 * @param[in]         Instance                The index of instance to be set active
 *
 */
typedef
VOID
(EFIAPI *FP_SET_ACTIVE_APCB_INSTANCE) (
  IN       AMD_APCB_SERVICE_PROTOCOL    *This,
  IN       UINT8             Instance
  );

/**
 * @brief Function used to flush APCB data
 *
 * @param[in]         This                    Point to AMD_APCB_SERVICE_PROTOCOL itself
 *
 * @retval            EFI_SUCCESS             Flush APCB data successfully
 * @retval            EFI_UNSUPPORTED         Error
 */
typedef
EFI_STATUS
(EFIAPI *FP_FLUSH_APCB_DATA) (
  IN       AMD_APCB_SERVICE_PROTOCOL    *This
  );

/**
 * @brief Function used to update CBS APCB data
 *
 * @param[in]         This                    Point to AMD_APCB_SERVICE_PROTOCOL itself
 * @param[in]         ApcbTypeData            Point to apcb type data
 * @param[in]         SizeInByte              Size of apcb type data
 * @param[in]         Internal                Is internal
 *
 * @retval            EFI_SUCCESS             Update CBS APCB data successfully
 * @retval            Not-EFI_SUCCESS         Error
 */
typedef
EFI_STATUS
(*FP_UPDATE_CBS_APCB_DATA) (
  IN       AMD_APCB_SERVICE_PROTOCOL    *This,
  IN       UINT8             *ApcbTypeData,
  IN       UINT32            SizeInByte,
  IN       BOOLEAN           Internal
  );

/**
 * @brief Function used to get the value of an APCB config token
 *
 * @param[in]         This                    Point to AMD_APCB_SERVICE_PROTOCOL itself
 * @param[in]         TokenId                 Token id
 * @param[in,out]     SizeInByte              Size of apcb token value
 * @param[in,out]     Value                   Point to the apcb token value
 *
 * @retval            EFI_SUCCESS             Get the value of an APCB config token successfully
 * @retval            Not-EFI_SUCCESS         Error
 */
typedef
EFI_STATUS
(EFIAPI *FP_GET_CONFIG_PARAM) (
  IN       AMD_APCB_SERVICE_PROTOCOL    *This,
  IN       UINT16            TokenId,
  IN OUT   UINT32            *SizeInByte,
  IN OUT   UINT64            *Value
  );

/**
 * @brief Function used to set the value of an APCB config token
 *
 * @param[in]         This                    Point to AMD_APCB_SERVICE_PROTOCOL itself
 * @param[in]         TokenId                 Token id
 * @param[in,out]     SizeInByte              Size of apcb token value
 * @param[in,out]     Value                   Point to the apcb token value
 *
 * @retval            EFI_SUCCESS             Set the value of an APCB config token successfully
 * @retval            Not-EFI_SUCCESS         Error
 */
typedef
EFI_STATUS
(EFIAPI *FP_SET_CONFIG_PARAM) (
  IN       AMD_APCB_SERVICE_PROTOCOL    *This,
  IN       UINT16            TokenId,
  IN OUT   UINT32            *SizeInByte,
  IN OUT   UINT64            *Value
  );

/**
 * @brief Function used to get the value of an APCB CBS token
 *
 * @param[in]         This                    Point to AMD_APCB_SERVICE_PROTOCOL itself
 * @param[in]         TokenId                 Token id
 * @param[in,out]     SizeInByte              Size of apcb token value
 * @param[in,out]     Value                   Point to the apcb token value
 *
 * @retval            EFI_SUCCESS             Get the value of an APCB CBS token successfully
 * @retval            Not-EFI_SUCCESS         Error
 */
typedef
EFI_STATUS
(EFIAPI *FP_GET_CBS_PARAM) (
  IN       AMD_APCB_SERVICE_PROTOCOL    *This,
  IN       UINT16            TokenId,
  IN OUT   UINT32            *SizeInByte,
  IN OUT   UINT64            *Value
  );

/**
 * @brief Function used to set the value of an APCB CBS token
 *
 * @param[in]         This                    Point to AMD_APCB_SERVICE_PROTOCOL itself
 * @param[in]         TokenId                 Token id
 * @param[in,out]     SizeInByte              Size of apcb token value
 * @param[in,out]     Value                   Point to the apcb token value
 *
 * @retval            EFI_SUCCESS             Set the value of an APCB CBS token successfully
 * @retval            Not-EFI_SUCCESS         Error
 */
typedef
EFI_STATUS
(EFIAPI *FP_SET_CBS_PARAM) (
  IN       AMD_APCB_SERVICE_PROTOCOL    *This,
  IN       UINT16            TokenId,
  IN OUT   UINT32            *SizeInByte,
  IN OUT   UINT64            *Value
  );

/**
 * @brief Function used to get the DRAM POST Package repair entries
 *
 * @param[in]         This                    Point to AMD_APCB_SERVICE_PROTOCOL itself
 * @param[in,out]     Entry                   Point to entry
 * @param[in,out]     NumOfEntries            Number of entry
 *
 * @retval            EFI_SUCCESS             DRAM POST Package repair entries are successfully retrieved
 * @retval            EFI_UNSUPPORTED         DRAM POST Package repair entries cannot be retrieved at this stage
 * @retval            EFI_NOT_FOUND           DRAM POST Package repair entries cannot be found
 * @retval            EFI_ABORTED             Reject Access due to SMM locked
 */
typedef
EFI_STATUS
(EFIAPI *FP_GET_DRAM_POSTPKG_REPAIR_ENTRY) (
  IN       AMD_APCB_SERVICE_PROTOCOL    *This,
  IN OUT   DPPR_REPAIR_ENTRY_V2 *Entry,
  IN OUT   UINT32            *NumOfEntries
  );

/**
 * @brief Function used to clear the DRAM POST Package repair entries
 *
 * @param[in]         This                    Point to AMD_APCB_SERVICE_PROTOCOL itself
 *
 * @retval            EFI_SUCCESS             DRAM POST Package repair entries are successfully cleared
 * @retval            EFI_UNSUPPORTED         DRAM POST Package repair entries cannot be cleared at this stage
 * @retval            EFI_NOT_FOUND           DRAM POST Package repair entries cannot be found
 * @retval            EFI_ABORTED             Reject Access due to SMM locked
 */
typedef
EFI_STATUS
(EFIAPI *FP_CLEAR_DRAM_POSTPKG_REPAIR_ENTRY) (
  IN       AMD_APCB_SERVICE_PROTOCOL    *This
  );

/**
 * @brief Function used to add a DRAM POST Package repair entry
 *
 * @param[in]         This                    Point to AMD_APCB_SERVICE_PROTOCOL itself
 * @param[in]         Entry                   Point to entry
 *
 * @retval            EFI_SUCCESS             DRAM POST Package repair entries are successfully added
 * @retval            EFI_UNSUPPORTED         DRAM POST Package repair entries cannot be added at this stage
 * @retval            EFI_NOT_FOUND           The type of DRAM POST Package repair entries cannot be found
 * @retval            EFI_ABORTED             Reject Access due to SMM locked
 */
typedef
EFI_STATUS
(EFIAPI *FP_ADD_DRAM_POSTPKG_REPAIR_ENTRY) (
  IN       AMD_APCB_SERVICE_PROTOCOL    *This,
  IN       DPPR_REPAIR_ENTRY_V2 *Entry
  );

/**
 * @brief Function used to remove a DRAM POST Package repair entry
 *
 * @param[in]         This                    Point to AMD_APCB_SERVICE_PROTOCOL itself
 * @param[in]         Entry                   Point to entry
 *
 * @retval            EFI_SUCCESS             DRAM POST Package repair entry is successfully removed
 * @retval            EFI_UNSUPPORTED         DRAM POST Package repair entry cannot be removed at this stage
 * @retval            EFI_NOT_FOUND           DRAM POST Package repair entry cannot be found
 * @retval            EFI_ABORTED             Reject Access due to SMM locked
 */
typedef
EFI_STATUS
(EFIAPI *FP_REMOVE_DRAM_POSTPKG_REPAIR_ENTRY) (
  IN       AMD_APCB_SERVICE_PROTOCOL    *This,
  IN       DPPR_REPAIR_ENTRY_V2 *Entry
  );

/**
 * @brief Function used to get the DRAM POST Package repair entries
 *
 * @param[in]         This                    Point to AMD_APCB_SERVICE_PROTOCOL itself
 * @param[in]         EntryStructVersion      Entry Struct Version
 *                                               -  0x2: Use DPPR_REPAIR_ENTRY_V2 data struct
 *                                               -  0x3: Use DPPR_REPAIR_ENTRY_V3 data struct
 * @param[in,out]     Entry                   Pointer to the DRAM POST Package repair entries to be retrieved
 * @param[in,out]     NumOfEntries            Number of DRAM POST Package repair entries to be returned
 *
 * @retval            EFI_SUCCESS             DRAM POST Package repair entries are successfully retrieved
 * @retval            EFI_UNSUPPORTED         DRAM POST Package repair entries cannot be retrieved at this stage
 * @retval            EFI_NOT_FOUND           DRAM POST Package repair entries cannot be found
 * @retval            EFI_ABORTED             Reject Access due to SMM locked
 */
typedef
EFI_STATUS
(EFIAPI *FP_GET_DRAM_POSTPKG_REPAIR_ENTRY_EX) (
  IN       AMD_APCB_SERVICE_PROTOCOL    *This,
  IN       UINT8                        EntryStructVersion,
  IN OUT   VOID                         *Entry,
  IN OUT   UINT32                       *NumOfEntries
  );

/**
 * @brief Function used to add a DRAM POST Package repair entry
 *
 * @param[in]         This                    Point to AMD_APCB_SERVICE_PROTOCOL itself
 * @param[in]         EntryStructVersion      Entry Struct Version
 *                                               -  0x2: Use DPPR_REPAIR_ENTRY_V2 data struct
 *                                               -  0x3: Use DPPR_REPAIR_ENTRY_V3 data struct
 * @param[in]         Entry                   Pointer to the DRAM POST Package repair entries
 *
 * @retval            EFI_SUCCESS             DRAM POST Package repair entries are successfully added
 * @retval            EFI_UNSUPPORTED         DRAM POST Package repair entries cannot be added at this stage
 * @retval            EFI_NOT_FOUND           The type of DRAM POST Package repair entries cannot be found
 * @retval            EFI_ABORTED             Reject Access due to SMM locked
 */
typedef
EFI_STATUS
(EFIAPI *FP_ADD_DRAM_POSTPKG_REPAIR_ENTRY_EX) (
  IN       AMD_APCB_SERVICE_PROTOCOL    *This,
  IN       UINT8                        EntryStructVersion,
  IN       VOID                         *Entry
  );

/**
 * @brief Function used to remove a DRAM POST Package repair entry
 *
 * @param[in]         This                    Point to AMD_APCB_SERVICE_PROTOCOL itself
 * @param[in]         EntryStructVersion      Entry Struct Version
 *                                               -  0x2: Use DPPR_REPAIR_ENTRY_V2 data struct
 *                                               -  0x3: Use DPPR_REPAIR_ENTRY_V3 data struct
 * @param[in]         Entry                   Pointer to the DRAM POST Package repair entries
 *
 * @retval            EFI_SUCCESS             DRAM POST Package repair entry is successfully removed
 * @retval            EFI_UNSUPPORTED         DRAM POST Package repair entry cannot be removed at this stage
 * @retval            EFI_NOT_FOUND           DRAM POST Package repair entry cannot be found
 * @retval            EFI_ABORTED             Reject Access due to SMM locked
 */
typedef
EFI_STATUS
(EFIAPI *FP_REMOVE_DRAM_POSTPKG_REPAIR_ENTRY_EX) (
  IN       AMD_APCB_SERVICE_PROTOCOL    *This,
  IN       UINT8                        EntryStructVersion,
  IN       VOID                         *Entry
  );

/**
 * @brief Function used to acquire the mutex for subsequent APCB operations
 *
 * @param[in]         This                    Point to AMD_APCB_SERVICE_PROTOCOL itself
 *
 * @retval            EFI_SUCCESS             Apcb Mutex Locked successfully
 * @retval            EFI_ACCESS_DENIED       APCB cannot be accessed
 * @retval            EFI_ABORTED             Reject Access due to SMM locked
 */
typedef
EFI_STATUS
(EFIAPI *FP_ACQUIRE_MUTEX) (
  IN       AMD_APCB_SERVICE_PROTOCOL    *This
  );

/**
 * @brief Function used to release the mutex for previous APCB operations
 *
 * @param[in]     This                      Point to AMD_APCB_SERVICE_PROTOCOL itself
 *
 * @retval        EFI_SUCCESS               Apcb Mutex Release successfully
 * @retval        EFI_ACCESS_DENIED         APCB cannot be accessed
 * @retval        EFI_ABORTED               Reject Access due to SMM locked
 */
typedef
EFI_STATUS
(EFIAPI *FP_RELEASE_MUTEX) (
  IN       AMD_APCB_SERVICE_PROTOCOL    *This
  );

/**
 * @brief Function used to get the value of an APCB BOOL token
 *
 * @param[in]     This                      Point to AMD_APCB_SERVICE_PROTOCOL itself
 * @param[out]    Purpose                   Point to purpose
 * @param[in]     TokenId                   Token id
 * @param[out]    bValue                    Point to boolean value
 *
 * @retval        EFI_SUCCESS               Get APCB value successfully
 * @retval        EFI_NOT_FOUND             Cannot find the APCB token
 * @retval        EFI_ABORTED               Reject Access due to SMM locked
 */
typedef
EFI_STATUS
(EFIAPI *FP_GET_TOKEN_BOOL) (
  IN       AMD_APCB_SERVICE_PROTOCOL    *This,
     OUT   UINT8                        *Purpose,
  IN       UINT32                       TokenId,
     OUT   BOOLEAN                      *bValue
  );

/**
 * @brief Function used to set the value of an APCB BOOL token
 *
 * @param[in]     This                      Point to AMD_APCB_SERVICE_PROTOCOL itself
 * @param[in]     Purpose                   Purpose
 * @param[in]     TokenId                   Token id
 * @param[in]     bValue                    Boolean value
 *
 * @retval        EFI_SUCCESS               Set APCB value successfully
 * @retval        EFI_OUT_OF_RESOURCES      Cannot set the APCB token
 * @retval        EFI_ABORTED               Reject Access due to SMM locked
 */
typedef
EFI_STATUS
(EFIAPI *FP_SET_TOKEN_BOOL) (
  IN       AMD_APCB_SERVICE_PROTOCOL    *This,
  IN       UINT8                        Purpose,
  IN       UINT32                       TokenId,
  IN       BOOLEAN                      bValue
  );

/**
 * @brief Function used to get the value of an APCB UINT8 token
 *
 * @param[in]     This                      Point to AMD_APCB_SERVICE_PROTOCOL itself
 * @param[out]    Purpose                   Point to purpose
 * @param[in]     TokenId                   Token id
 * @param[out]    Value8                    Point to value
 *
 * @retval        EFI_SUCCESS               Get APCB value successfully
 * @retval        EFI_NOT_FOUND             Cannot find the APCB token
 * @retval        EFI_ABORTED               Reject Access due to SMM locked
 */
typedef
EFI_STATUS
(EFIAPI *FP_GET_TOKEN_8) (
  IN       AMD_APCB_SERVICE_PROTOCOL    *This,
     OUT   UINT8                        *Purpose,
  IN       UINT32                       TokenId,
     OUT   UINT8                        *Value8
  );

/**
 * @brief Function used to set the value of an APCB UINT8 token
 *
 * @param[in]     This                      Point to AMD_APCB_SERVICE_PROTOCOL itself
 * @param[in]     Purpose                   Purpose
 * @param[in]     TokenId                   Token id
 * @param[in]     Value8                    Value to be set
 *
 * @retval        EFI_SUCCESS               Set APCB value successfully
 * @retval        EFI_OUT_OF_RESOURCES      Cannot set the APCB token
 * @retval        EFI_ABORTED               Reject Access due to SMM locked
 */
typedef
EFI_STATUS
(EFIAPI *FP_SET_TOKEN_8) (
  IN       AMD_APCB_SERVICE_PROTOCOL    *This,
  IN       UINT8                        Purpose,
  IN       UINT32                       TokenId,
  IN       UINT8                        Value8
  );

/**
 * @brief Function used to get the value of an APCB UINT16 toke
 *
 * @param[in]     This                      Point to AMD_APCB_SERVICE_PROTOCOL itself
 * @param[out]    Purpose                   Point to purpose
 * @param[in]     TokenId                   Token id
 * @param[out]    Value8                    Point to value
 *
 * @retval        EFI_SUCCESS               Get APCB value successfully
 * @retval        EFI_NOT_FOUND             Cannot find the APCB token
 * @retval        EFI_ABORTED               Reject Access due to SMM locked
 */
typedef
EFI_STATUS
(EFIAPI *FP_GET_TOKEN_16) (
  IN       AMD_APCB_SERVICE_PROTOCOL    *This,
     OUT   UINT8                        *Purpose,
  IN       UINT32                       TokenId,
     OUT   UINT16                       *Value16
  );

/**
 * @brief Function used to set the value of an APCB UINT16 token
 *
 * @param[in]     This                      Point to AMD_APCB_SERVICE_PROTOCOL itself
 * @param[in]     Purpose                   Purpose
 * @param[in]     TokenId                   Token id
 * @param[in]     Value8                    Value to be set
 *
 * @retval        EFI_SUCCESS               Set APCB value successfully
 * @retval        EFI_OUT_OF_RESOURCES      Cannot set the APCB token
 * @retval        EFI_ABORTED               Reject Access due to SMM locked
 */
typedef
EFI_STATUS
(EFIAPI *FP_SET_TOKEN_16) (
  IN       AMD_APCB_SERVICE_PROTOCOL    *This,
  IN       UINT8                        Purpose,
  IN       UINT32                       TokenId,
  IN       UINT16                       Value16
  );

/**
 * @brief Function used to get the value of an APCB UINT32 token
 *
 * @param[in]     This                      Point to AMD_APCB_SERVICE_PROTOCOL itself
 * @param[out]    Purpose                   Point to purpose
 * @param[in]     TokenId                   Token id
 * @param[out]    Value8                    Point to value
 *
 * @retval        EFI_SUCCESS               Get APCB value successfully
 * @retval        EFI_NOT_FOUND             Cannot find the APCB token
 * @retval        EFI_ABORTED               Reject Access due to SMM locked
 */
typedef
EFI_STATUS
(EFIAPI *FP_GET_TOKEN_32) (
  IN       AMD_APCB_SERVICE_PROTOCOL    *This,
     OUT   UINT8                        *Purpose,
  IN       UINT32                       TokenId,
     OUT   UINT32                       *Value32
  );

/**
 * @brief Function used to set the value of an APCB UINT32 token
 *
 * @param[in]     This                      Point to AMD_APCB_SERVICE_PROTOCOL itself
 * @param[in]     Purpose                   Purpose
 * @param[in]     TokenId                   Token id
 * @param[in]     Value8                    Value to be set
 *
 * @retval        EFI_SUCCESS               Set APCB value successfully
 * @retval        EFI_OUT_OF_RESOURCES      Cannot set the APCB token
 * @retval        EFI_ABORTED               Reject Access due to SMM locked
 */
typedef
EFI_STATUS
(EFIAPI *FP_SET_TOKEN_32) (
  IN       AMD_APCB_SERVICE_PROTOCOL    *This,
  IN       UINT8                        Purpose,
  IN       UINT32                       TokenId,
  IN       UINT32                       Value32
  );

/**
 * @brief Function used to retrive the data of a specified type
 *
 * @param[in]     This                      Point to AMD_APCB_SERVICE_PROTOCOL itself
 * @param[out]    Purpose                   Point to purpose
 * @param[in]     GroupId                   Group id
 * @param[in]     TypeId                    Type id
 * @param[in]     InstanceId                Instance id
 * @param[out]    DataBuf                   Point to the data buffer
 * @param[out]    DataSize                  Point to the data size
 *
 * @retval        EFI_SUCCESS               The type data is retrieved successfully
 * @retval        EFI_NOT_FOUND             The type data cannot be retrieved
 * @retval        EFI_ABORTED               Reject Access due to SMM locked
 */
typedef
EFI_STATUS
(EFIAPI *FP_GET_TYPE) (
  IN       AMD_APCB_SERVICE_PROTOCOL    *This,
      OUT  UINT8                        *Purpose,
  IN       UINT16                       GroupId,
  IN       UINT16                       TypeId,
  IN       UINT16                       InstanceId,
      OUT  UINT8                        **DataBuf,
      OUT  UINT32                       *DataSize
  );

/**
 * @brief Function used to set the data of a specified type
 *
 * @param[in]     This                      Point to AMD_APCB_SERVICE_PROTOCOL itself
 * @param[in]     Purpose                   Purpose
 * @param[in]     GroupId                   Group id
 * @param[in]     TypeId                    Type id
 * @param[in]     InstanceId                Instance id
 * @param[out]    DataBuf                   Point to the data buffer
 * @param[out]    DataSize                  Data size
 *
 * @retval        EFI_SUCCESS               The type data is set successfully
 * @retval        EFI_NOT_FOUND             The type data cannot be retrieved
 * @retval        EFI_ABORTED               Reject Access due to SMM locked
 */
typedef
EFI_STATUS
(EFIAPI *FP_SET_TYPE) (
  IN       AMD_APCB_SERVICE_PROTOCOL    *This,
  IN       UINT8                        Purpose,
  IN       UINT16                       GroupId,
  IN       UINT16                       TypeId,
  IN       UINT16                       InstanceId,
      OUT  UINT8                        *DataBuf,
      OUT  UINT32                       DataSize
  );

/**
 * @brief Function used to purge all APCB token/value pairs
 *
 * @param[in]     This                      Point to AMD_APCB_SERVICE_PROTOCOL itself
 * @param[in]     Purpose                   Purpose
 *
 * @retval        EFI_SUCCESS               All tokens are purged
 * @retval        EFI_NOT_FOUND             All tokens are not purged
 * @retval        EFI_ABORTED               Reject Access due to SMM locked
 */
typedef
EFI_STATUS
(EFIAPI *FP_PURGE_ALL_TOKENS) (
  IN       AMD_APCB_SERVICE_PROTOCOL    *This,
  IN       UINT8                        Purpose
  );

/**
 * @brief Function used to purge the data of all types
 *
 * @param[in]     This                     Point to AMD_APCB_SERVICE_PROTOCOL itself
 * @param[in]     TPurpose                  Purpose
 *
 * @retval        EFI_SUCCESS               All types are purged
 * @retval        EFI_OUT_OF_RESOURCES      All types are not purged
 * @retval        EFI_ABORTED               Reject Access due to SMM locked
 */
typedef
EFI_STATUS
(EFIAPI *FP_PURGE_ALL_TYPES) (
  IN       AMD_APCB_SERVICE_PROTOCOL    *This,
  IN       UINT8                        Purpose
  );

/**
 * @brief Function used to purge the data tokens
 *
 * @param[in]     This                      Point to AMD_APCB_SERVICE_PROTOCOL itself
 * @param[in]     Purpose                   Purpose
 * @param[in]     ApcbTokenList             Point to apcb token list
 * @param[in]     TokenCount                Number of tokens
 *
 * @retval        EFI_SUCCESS               Tokens are purged
 * @retval        EFI_NOT_FOUND             Tokens are not purged
 * @retval        EFI_ABORTED               Reject Access due to SMM locked
 */
typedef
EFI_STATUS
(EFIAPI *FP_PURGE_TOKENS) (
  IN       AMD_APCB_SERVICE_PROTOCOL    *This,
  IN       UINT8                        Purpose,
  IN       UINT32                       *ApcbTokenList,
  IN       UINT32                       TokenCount
  );

/**
 * @brief This function is used to register Apcb token id into whitelist buffer before smm lock.
 *
 * @details this function can be called multiple times to store Apcb token id into the buffer.
 * This function will not be allowed to execute to store the new token id after Smm Locked.
 * After SMM locked, Apcb Smm Protocol: access tokens functions may be called by some routines,
 * those functions use whitelist buffer to verify the Apcb token id is valid before
 * accessing the Apcb data
 *
 * @param[in]     This                      Point to AMD_APCB_SERVICE_PROTOCOL itself
 * @param[in]     ApcbTokensRegInfo         The pointer of ApcbTokensRegInfo
 * @param[in]     ApcbTokensRegInfoNum      The number of ApcbTokensRegInfo
 *
 * @retval        EFI_SUCCESS               Register Apcb Token Id into whitelist buffer successfully
 * @retval        EFI_UNSUPPORTED           The function did not support to register Apcb Token id
 * @retval        EFI_INVALID_PARAMETER     The Parameter is invalid
 * @retval        EFI_BUFFER_TOO_SMALL      Whitelist buffer is full
 */
typedef
EFI_STATUS
(EFIAPI *FP_TOKENS_REGISTER_PERMISSION) (
  IN       AMD_APCB_SERVICE_PROTOCOL    *This,
  IN       APCB_TOKENS_REG_INFO         *ApcbTokensRegInfo,
  IN       UINT32                        ApcbTokensRegInfoNum
  );

///
/// Protocol prototype
///
/// Defines AMD_APCB_SERVICE_PROTOCOL, which publish the APCB service across all programs
///
struct _AMD_APCB_SERVICE_PROTOCOL {
  //
  // Common APCB services
  //
  UINT32                                  Version;                            ///< Version number of the protocol
  FP_FLUSH_APCB_DATA                      ApcbFlushData;                      ///< Flush APCB data back to the SPI ROM
  FP_GET_DRAM_POSTPKG_REPAIR_ENTRY        ApcbGetDramPostPkgRepairEntries;    ///< Retrieve DRAM Post Package Repair Entries
  FP_CLEAR_DRAM_POSTPKG_REPAIR_ENTRY      ApcbClearDramPostPkgRepairEntry;    ///< Clear DRAM Post Package Repair Entries
  FP_ADD_DRAM_POSTPKG_REPAIR_ENTRY        ApcbAddDramPostPkgRepairEntry;      ///< Add a DRAM Post Package Repair Entry
  FP_REMOVE_DRAM_POSTPKG_REPAIR_ENTRY     ApcbRemoveDramPostPkgRepairEntry;   ///< Remove a DRAM Post Package Repair Entry

  FP_GET_DRAM_POSTPKG_REPAIR_ENTRY_EX     ApcbGetDramPostPkgRepairEntriesEx;  ///< Retrieve DRAM Post Package Repair Entries
  FP_ADD_DRAM_POSTPKG_REPAIR_ENTRY_EX     ApcbAddDramPostPkgRepairEntryEx;    ///< Add a DRAM Post Package Repair Entry
  FP_REMOVE_DRAM_POSTPKG_REPAIR_ENTRY_EX  ApcbRemoveDramPostPkgRepairEntryEx; ///< Remove a DRAM Post Package Repair Entry
  //
  // APCB 2.0 services
  //
  FP_SET_ACTIVE_APCB_INSTANCE           ApcbSetActiveInstance;            ///< Set the active instance of APCB
  FP_GET_CONFIG_PARAM                   ApcbGetConfigParameter;           ///< Get an APCB configuration parameter
  FP_SET_CONFIG_PARAM                   ApcbSetConfigParameter;           ///< Set an APCB configuration parameter
  FP_GET_CBS_PARAM                      ApcbGetCbsParameter;              ///< Get an APCB CBS parameter
  FP_SET_CBS_PARAM                      ApcbSetCbsParameter;              ///< Set an APCB CBS parameter
  FP_UPDATE_CBS_APCB_DATA               ApcbUpdateCbsData;                ///< Update CBS APCB data
  //
  // APCB 3.0 services
  //
  FP_ACQUIRE_MUTEX                      ApcbAcquireMutex;                 ///< Acquire Mutex
  FP_RELEASE_MUTEX                      ApcbReleaseMutex;                 ///< Release Mutex
  FP_GET_TOKEN_BOOL                     ApcbGetTokenBool;                 ///< Get an APCB BOOL token
  FP_SET_TOKEN_BOOL                     ApcbSetTokenBool;                 ///< Set an APCB BOOL token
  FP_GET_TOKEN_8                        ApcbGetToken8;                    ///< Get an APCB UINT8 token
  FP_SET_TOKEN_8                        ApcbSetToken8;                    ///< Set an APCB UINT8 token
  FP_GET_TOKEN_16                       ApcbGetToken16;                   ///< Get an APCB UINT16 token
  FP_SET_TOKEN_16                       ApcbSetToken16;                   ///< Set an APCB UINT16 token
  FP_GET_TOKEN_32                       ApcbGetToken32;                   ///< Get an APCB UINT32 token
  FP_SET_TOKEN_32                       ApcbSetToken32;                   ///< Set an APCB UINT32 token
  FP_GET_TYPE                           ApcbGetType;                      ///< Retrieve the data of a specified type
  FP_SET_TYPE                           ApcbSetType;                      ///< Set the data of a specified type
  FP_PURGE_ALL_TOKENS                   ApcbPurgeAllTokens;               ///< Purge all token/value pairs
  FP_PURGE_ALL_TYPES                    ApcbPurgeAllTypes;                ///< Purge the data of all types
  FP_PURGE_TOKENS                       ApcbPurgeTokens;                  ///< Purge token pairs
  FP_TOKENS_REGISTER_PERMISSION         ApcbTokensRegisterPermission;     ///< Register APCB Token ID Access Permission
};

extern EFI_GUID gAmdApcbDxeServiceProtocolGuid; ///< APCB Dxe service protocol Guid
extern EFI_GUID gAmdApcbSmmServiceProtocolGuid; ///< APCB Smm service portocol Guid

#endif //_AMD_APCB_SERVICE_PROTOCOL_H_



