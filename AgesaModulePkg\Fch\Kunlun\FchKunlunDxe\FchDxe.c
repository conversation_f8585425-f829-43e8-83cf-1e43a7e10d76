/*****************************************************************************
 *
 * Copyright (C) 2008-2024 Advanced Micro Devices, Inc. All rights reserved.
 *
 *******************************************************************************
 */

#include "FchDxe.h"

#define FILECODE FCH_KUNLUN_FCHKUNLUNDXE_FCHDXE_FILECODE

extern EFI_GUID gFchResetDataHobGuid;
extern EFI_GUID gEfiPciIoProtocolGuid;
extern FCH_DATA_BLOCK InitEnvCfgDefault;

VOID
FchReportConfigValues (
  IN      FCH_DATA_BLOCK     *LateParams
  );


//
// Private Function Declaration
//
VOID
EFIAPI
InvokeFchInitMid (
  IN       EFI_EVENT        Event,
  IN       VOID             *Context
  )
{
  FCH_DATA_BLOCK        *FchPolicy;

  IDS_HDT_CONSOLE (FCH_TRACE, "[FchInitMid] Fch Init - After PCI Scan ...Start\n");
  FchPolicy = (FCH_DATA_BLOCK*) Context;
  FchInitMid (FchPolicy);
  FchReportConfigValues (FchPolicy);
  IDS_HDT_CONSOLE (FCH_TRACE, "[FchInitMid] Fch Init - After PCI Scan ...Complete\n");

  gBS->CloseEvent (Event);
}


VOID
FchInitLateOemSecure (
  IN  VOID     *FchDataPtr
  )
{
  UINT16                  FchSmiData;
  UINT16                  SwSmiCmdAddress;
  FCH_DATA_BLOCK         *LocalCfgPtr;

  LocalCfgPtr = (FCH_DATA_BLOCK *) FchDataPtr;

  FchSmiData = PcdGet8 (PcdFchOemSecureSwSmi);
  SwSmiCmdAddress = ACPIMMIO16 (ACPI_MMIO_BASE + PMIO_BASE + FCH_PMIOA_REG6A);
  LibAmdIoWrite (AccessWidth16, SwSmiCmdAddress, &FchSmiData, LocalCfgPtr->StdHeader);
}


VOID
EFIAPI
InvokeFchInitLate (
  IN       EFI_EVENT        Event,
  IN       VOID             *Context
  )
{
  FCH_DATA_BLOCK        *FchPolicy;

  IDS_HDT_CONSOLE (FCH_TRACE, "[FchInitLate] Fch Init - Before Boot ...Start\n");
  FchPolicy = (FCH_DATA_BLOCK*) Context;
  FchInitLate (FchPolicy);
  FchInitLateOemSecure (FchPolicy);
  IDS_HDT_CONSOLE (FCH_TRACE, "[FchInitLate] Fch Init - Before Boot ...Complete\n");

  gBS->CloseEvent (Event);
}


/**
 * @brief load value of FCH_DATA_BLOCK
 *
 * @param[in] FchParams Pointer to FCH_DATA_BLOCK
 *
 * @returns FCH_DATA_BLOCK*
 */
FCH_DATA_BLOCK*
FchInitDataBlock (
  IN OUT   FCH_DATA_BLOCK  *FchParams
  )
{
  EFI_STATUS                Status;
  EFI_HOB_GUID_TYPE         *FchHob;
  FCH_RESET_DATA_BLOCK      *FchResetParams;
  UINT16                    IoEnable16;
  UINT16                    UartChannel;
  UINT8                     UartLegacy[4];
  PCI_ADDR                  NbioPciAddress;
  UINT32                    SmuArg[6];
  UINT8                     syncFloodResetDelay = 0;
  UINT8                     DevSlp0ControllerNum;
  UINT8                     DevSlp1ControllerNum;

  //load default
  gBS->CopyMem (
         FchParams,
         &InitEnvCfgDefault,
         sizeof (FCH_DATA_BLOCK)
         );

  //find HOB and update with reset data block
  FchHob = (EFI_HOB_GUID_TYPE *)GetFirstGuidHob (&gFchResetDataHobGuid);
  if (FchHob == NULL) {
    IDS_HDT_CONSOLE (FCH_TRACE, "FCH HOB Not located, Exiting.\n");
    Status = EFI_UNSUPPORTED;
    ASSERT_EFI_ERROR (Status);
    return FchParams;
  }
  FchHob++;
  FchResetParams = (FCH_RESET_DATA_BLOCK *) ((UINTN)FchHob - sizeof (UINT32));
  //FchParams->FchResetDataBlock = FchResetParams;
  gBS->CopyMem (
         &FchParams->FchResetDataBlock,
         FchResetParams,
         sizeof (FCH_RESET_DATA_BLOCK)
         );

  FchParams->Usb.Xhci0Enable              = FchResetParams->FchReset.Xhci0Enable;
  FchParams->Usb.Xhci1Enable              = FchResetParams->FchReset.Xhci1Enable;
  //FchParams->Spi.SpiFastSpeed             = FchResetParams->FastSpeed; //it has been replaced by  EFS to configure the setting.
  FchParams->Spi.WriteSpeed               = FchResetParams->WriteSpeed;
  //FchParams->Spi.SpiMode                  = FchResetParams->Mode; //it has been replaced by  EFS to configure the setting.
  FchParams->Spi.SpiSpeed                 = FchResetParams->SpiSpeed;
  FchParams->Spi.AutoMode                 = FchResetParams->AutoMode;
  FchParams->Spi.SpiBurstWrite            = FchResetParams->BurstWrite;
  FchParams->Spi.LpcEnable                = FchResetParams->Spi.LpcEnable;
  FchParams->Misc.Cg2Pll                  = FchResetParams->Cg2Pll;
  FchParams->Sata[0].SataEnable           = FchResetParams->SataEnable[0];
  FchParams->Sata[1].SataEnable           = FchResetParams->SataEnable[1];
  FchParams->Sata[2].SataEnable           = FchResetParams->SataEnable[2];
  FchParams->Sata[3].SataEnable           = FchResetParams->SataEnable[3];
  FchParams->Sata[0].SataSetMaxGen2       = FchResetParams->SataSetMaxGen2;

  //FchParams->Promontory                   = FchResetParams->Promontory;
  gBS->CopyMem (
         &FchParams->Promontory,
         &FchResetParams->Promontory,
         sizeof (FCH_PT)
         );

  //Platform call out
  Status = FchPlatformPTDxeInit ((VOID *)&FchParams->Promontory);

  ASSERT_EFI_ERROR (Status);

  Status = FchPlatformOemDxeInit ((VOID *)FchParams);

  ASSERT_EFI_ERROR (Status);

  IDS_HOOK (IDS_HOOK_FCH_INIT_ENV, NULL, (VOID *)FchParams);

  // SMI Timers PCDs
  FchParams->Misc.ShortTimer.Enable        = PcdGetBool (PcdFchShortTimerEnableSmi);
  FchParams->Misc.ShortTimer.StartNow      = PcdGetBool (PcdFchShortTimerStartNowSmi);
  FchParams->Misc.ShortTimer.CycleDuration = (UINT16) PcdGet32 (PcdFchShortTimerCycleDurationSmi);

  FchParams->Misc.LongTimer.Enable         = PcdGetBool (PcdFchLongTimerEnableSmi);
  FchParams->Misc.LongTimer.StartNow       = PcdGetBool (PcdFchLongTimerStartNowSmi);
  FchParams->Misc.LongTimer.CycleDuration  = (UINT16) PcdGet32 (PcdFchLongTimerCycleDurationSmi);

  //Dynamic PCDs
  FchParams->Ab.AbClockGating             = PcdGet8 (PcdAbClockGating);
  FchParams->Ab.ALinkClkGateOff           = PcdGet8 (PcdALinkClkGateOff);
  FchParams->Ab.BLinkClkGateOff           = PcdGet8 (PcdBLinkClkGateOff);
  FchParams->Ab.SbgMemoryPowerSaving      = PcdGetBool (PcdSbgMemoryPowerSaving);
  FchParams->Ab.SbgClockGating            = PcdGetBool (PcdSbgClockGating);
  FchParams->Ab.XdmaDmaWrite16ByteMode    = PcdGetBool (PcdXdmaDmaWrite16ByteMode);
  FchParams->Ab.XdmaMemoryPowerSaving     = PcdGetBool (PcdXdmaMemoryPowerSaving);
  FchParams->Ab.XdmaPendingNprThreshold   = PcdGet8 (PcdXdmaPendingNprThreshold);
  FchParams->Ab.XdmaDncplOrderDis         = PcdGetBool (PcdXdmaDncplOrderDis);
  //FchParams->Ab.GppClockRequest0          = PcdGet8 (PcdGppClockRequest0);
  //FchParams->Ab.GppClockRequest1          = PcdGet8 (PcdGppClockRequest1);
  //FchParams->Ab.GppClockRequest2          = PcdGet8 (PcdGppClockRequest2);
  //FchParams->Ab.GppClockRequest3          = PcdGet8 (PcdGppClockRequest3);
  //FchParams->Ab.SltGfxClockRequest0       = PcdGet8 (PcdSltGfxClockRequest0);
  //FchParams->Ab.SltGfxClockRequest1       = PcdGet8 (PcdSltGfxClockRequest1);
  FchParams->Ab.SdphostBypassDataPack     = PcdGetBool (PcdSdphostBypassDataPack);
  FchParams->Ab.SdphostDisNpmwrProtect    = PcdGetBool (PcdSdphostDisNpmwrProtect);
  if (PcdGetBool (PcdResetCpuOnSyncFlood) && (!PcdGetBool (PcdSyncFloodToApml))) {
    FchParams->Ab.ResetCpuOnSyncFlood     = TRUE;
  } else {
    FchParams->Ab.ResetCpuOnSyncFlood     = FALSE;
  }

  // Send SMU SyncFlood setting
  NbioSmuServiceCommonInitArguments (SmuArg);
  if (PcdGetBool (PcdResetCpuOnSyncFlood)) {
    SmuArg[0] = 1;
  }
  NbioPciAddress.AddressValue = MAKE_SBDFO (0, 0, 0, 0, 0);
  if (NbioSmuServiceRequest (NbioPciAddress, BIOSSMC_MSG_SetResetCpuOnSyncFlood, SmuArg, 0)) {
  }
  //send SMC delay sync flood when PcdResetCpuOnSyncFlood enabled
  if (PcdGetBool (PcdResetCpuOnSyncFlood)) {
    syncFloodResetDelay = PcdGet8 ( PcdDelayResetCpuOnSyncFlood);
    DEBUG ((DEBUG_INFO, "syncFloodResetDelay: %d min \n", syncFloodResetDelay));
    if (  syncFloodResetDelay > 0 && syncFloodResetDelay < 5){
    // Allow rang 5 to 255 minutes for enabled sync flood reset delay. Treat it as 0 if it is not within enabled range.
      DEBUG ((DEBUG_INFO, "disable syncFloodResetDelay\n"));
      syncFloodResetDelay = 0;
      FCH_PCDSET8 ( PcdDelayResetCpuOnSyncFlood, 0);
    }
    SmuArg[0] =  syncFloodResetDelay;
    NbioSmuServiceRequest (NbioPciAddress, BIOSSMC_MSG_SetDelayResetCpuOnSyncFlood, SmuArg, 0);
  }

  FchParams->Sata[0].SataClass               = PcdGet8 (PcdSataClass);
  FchParams->Sata[0].SataAggrLinkPmCap       = PcdGet8 (PcdSataAggrLinkPmCap);
  FchParams->Sata[0].SataPortMultCap         = PcdGet8 (PcdSataPortMultCap);
  FchParams->Sata[0].SataPscCap              = PcdGet8 (PcdSataPscCap);
  FchParams->Sata[0].SataSscCap              = PcdGet8 (PcdSataSscCap);
  FchParams->Sata[0].SataClkAutoOff          = PcdGet8 (PcdSataClkAutoOff);
  FchParams->Sata[0].SataFisBasedSwitching   = PcdGet8 (PcdSataFisBasedSwitching);
  FchParams->Sata[0].SataCccSupport          = PcdGet8 (PcdSataCccSupport);
  FchParams->Sata[0].SataDisableGenericMode  = PcdGet8 (PcdSataDisableGenericMode);
  FchParams->Sata[0].SataTargetSupport8Device          = PcdGet8 (PcdSataTargetSupport8Device);
  FchParams->Sata[0].SataAhciEnclosureManagement       = PcdGet8 (PcdSataAhciEnclosureManagement);
  FchParams->Sata[0].SataMsiEnable           = PcdGetBool (PcdSataMsiEnable);
  FchParams->Sata[0].SataRasSupport          = PcdGetBool (PcdSataRasSupport);
  FchParams->Sata[0].SataAhciDisPrefetchFunction       = PcdGetBool (PcdSataAhciDisPrefetchFunction);
  FchParams->Sata[0].SataAhciSsid            = PcdGet32 (PcdSataAhciSsid);
  FchParams->Sata[0].SataRaid5Ssid           = PcdGet32 (PcdSataRaid5Ssid);
  FchParams->Sata[0].SataRaidSsid            = PcdGet32 (PcdSataRaidSsid);

  IDS_HDT_CONSOLE (FCH_TRACE, "[FchInitDataBlock] PcdSataDevSlpPort0: %d.\n", PcdGetBool(PcdSataDevSlpPort0));
  if (PcdGetBool (PcdSataDevSlpPort0)) {
    DevSlp0ControllerNum = (PcdGet8 (PcdSataDevSlpPort0Num) & 0xF0) >> 4;
    FchParams->Sata[DevSlp0ControllerNum].SataDevSlpPort0  = TRUE;
    FchParams->Sata[DevSlp0ControllerNum].SataDevSlpPort0Num = (PcdGet8 (PcdSataDevSlpPort0Num)) & 0x0F;
    IDS_HDT_CONSOLE (FCH_TRACE, "[FchInitDataBlock] DevSlp0 ControllerNum: %d, PortNum: %d.\n", DevSlp0ControllerNum, FchParams->Sata[DevSlp0ControllerNum].SataDevSlpPort0Num);
  }

  IDS_HDT_CONSOLE (FCH_TRACE, "[FchInitDataBlock] PcdSataDevSlpPort1: %d.\n", PcdGetBool(PcdSataDevSlpPort1));
  if (PcdGetBool (PcdSataDevSlpPort1)) {
    DevSlp1ControllerNum = (PcdGet8 (PcdSataDevSlpPort1Num) & 0xF0) >> 4;
    FchParams->Sata[DevSlp1ControllerNum].SataDevSlpPort1  = TRUE;
    FchParams->Sata[DevSlp1ControllerNum].SataDevSlpPort1Num = (PcdGet8 (PcdSataDevSlpPort1Num)) & 0x0F;
    IDS_HDT_CONSOLE (FCH_TRACE, "[FchInitDataBlock] DevSlp1 ControllerNum: %d, PortNum: %d.\n", DevSlp1ControllerNum, FchParams->Sata[DevSlp1ControllerNum].SataDevSlpPort1Num);
  }

  //FchParams->Sata[0].SataControllerAutoShutdown        = PcdGetBool (PcdSataControllerAutoShutdown);

  FchParams->Sata[0].SataBISTLComplianceMode = PcdGet8 (PcdSataBISTLComplianceMode);

  //Sata common settings
  FchParams->Sata[1].SataClass               = FchParams->Sata[0].SataClass;
  FchParams->Sata[2].SataClass               = FchParams->Sata[0].SataClass;
  FchParams->Sata[3].SataClass               = FchParams->Sata[0].SataClass;

  FchParams->Sata[1].SataSetMaxGen2          = FchParams->Sata[0].SataSetMaxGen2;
  FchParams->Sata[2].SataSetMaxGen2          = FchParams->Sata[0].SataSetMaxGen2;
  FchParams->Sata[3].SataSetMaxGen2          = FchParams->Sata[0].SataSetMaxGen2;

  FchParams->Sata[1].SataAggrLinkPmCap       = FchParams->Sata[0].SataAggrLinkPmCap;
  FchParams->Sata[2].SataAggrLinkPmCap       = FchParams->Sata[0].SataAggrLinkPmCap;
  FchParams->Sata[3].SataAggrLinkPmCap       = FchParams->Sata[0].SataAggrLinkPmCap;

  FchParams->Sata[1].SataPortMultCap         = FchParams->Sata[0].SataPortMultCap;
  FchParams->Sata[1].SataPortMultCap         = FchParams->Sata[0].SataPortMultCap;
  FchParams->Sata[1].SataPortMultCap         = FchParams->Sata[0].SataPortMultCap;

  FchParams->Sata[1].SataPscCap              = FchParams->Sata[0].SataPscCap;
  FchParams->Sata[2].SataPscCap              = FchParams->Sata[0].SataPscCap;
  FchParams->Sata[3].SataPscCap              = FchParams->Sata[0].SataPscCap;

  FchParams->Sata[1].SataSscCap              = FchParams->Sata[0].SataSscCap;
  FchParams->Sata[2].SataSscCap              = FchParams->Sata[0].SataSscCap;
  FchParams->Sata[3].SataSscCap              = FchParams->Sata[0].SataSscCap;

  FchParams->Sata[1].SataClkAutoOff          = FchParams->Sata[0].SataClkAutoOff;
  FchParams->Sata[2].SataClkAutoOff          = FchParams->Sata[0].SataClkAutoOff;
  FchParams->Sata[3].SataClkAutoOff          = FchParams->Sata[0].SataClkAutoOff;

  FchParams->Sata[1].SataFisBasedSwitching   = FchParams->Sata[0].SataFisBasedSwitching;
  FchParams->Sata[2].SataFisBasedSwitching   = FchParams->Sata[0].SataFisBasedSwitching;
  FchParams->Sata[3].SataFisBasedSwitching   = FchParams->Sata[0].SataFisBasedSwitching;

  FchParams->Sata[1].SataCccSupport          = FchParams->Sata[0].SataCccSupport;
  FchParams->Sata[2].SataCccSupport          = FchParams->Sata[0].SataCccSupport;
  FchParams->Sata[3].SataCccSupport          = FchParams->Sata[0].SataCccSupport;

  FchParams->Sata[1].SataMsiEnable           = FchParams->Sata[0].SataMsiEnable;
  FchParams->Sata[2].SataMsiEnable           = FchParams->Sata[0].SataMsiEnable;
  FchParams->Sata[3].SataMsiEnable           = FchParams->Sata[0].SataMsiEnable;

  FchParams->Sata[1].SataTargetSupport8Device          = FchParams->Sata[0].SataTargetSupport8Device;
  FchParams->Sata[2].SataTargetSupport8Device          = FchParams->Sata[0].SataTargetSupport8Device;
  FchParams->Sata[3].SataTargetSupport8Device          = FchParams->Sata[0].SataTargetSupport8Device;

  FchParams->Sata[1].SataDisableGenericMode  = FchParams->Sata[0].SataDisableGenericMode;
  FchParams->Sata[2].SataDisableGenericMode  = FchParams->Sata[0].SataDisableGenericMode;
  FchParams->Sata[3].SataDisableGenericMode  = FchParams->Sata[0].SataDisableGenericMode;

  FchParams->Sata[1].SataAhciEnclosureManagement       = FchParams->Sata[0].SataAhciEnclosureManagement;
  FchParams->Sata[2].SataAhciEnclosureManagement       = FchParams->Sata[0].SataAhciEnclosureManagement;
  FchParams->Sata[3].SataAhciEnclosureManagement       = FchParams->Sata[0].SataAhciEnclosureManagement;

  FchParams->Sata[1].SataRasSupport          = FchParams->Sata[0].SataRasSupport;
  FchParams->Sata[2].SataRasSupport          = FchParams->Sata[0].SataRasSupport;
  FchParams->Sata[3].SataRasSupport          = FchParams->Sata[0].SataRasSupport;

  FchParams->Sata[1].SataAhciDisPrefetchFunction       = FchParams->Sata[0].SataAhciDisPrefetchFunction;
  FchParams->Sata[2].SataAhciDisPrefetchFunction       = FchParams->Sata[0].SataAhciDisPrefetchFunction;
  FchParams->Sata[3].SataAhciDisPrefetchFunction       = FchParams->Sata[0].SataAhciDisPrefetchFunction;

  FchParams->Sata[1].SataAhciSsid            = FchParams->Sata[0].SataAhciSsid;
  FchParams->Sata[2].SataAhciSsid            = FchParams->Sata[0].SataAhciSsid;
  FchParams->Sata[3].SataAhciSsid            = FchParams->Sata[0].SataAhciSsid;
  FchParams->Sata[1].SataRaid5Ssid           = FchParams->Sata[0].SataRaid5Ssid;
  FchParams->Sata[2].SataRaid5Ssid           = FchParams->Sata[0].SataRaid5Ssid;
  FchParams->Sata[3].SataRaid5Ssid           = FchParams->Sata[0].SataRaid5Ssid;
  FchParams->Sata[1].SataRaidSsid            = FchParams->Sata[0].SataRaidSsid;
  FchParams->Sata[2].SataRaidSsid            = FchParams->Sata[0].SataRaidSsid;
  FchParams->Sata[3].SataRaidSsid            = FchParams->Sata[0].SataRaidSsid;
  FchParams->Sata[1].SataControllerAutoShutdown         = FchParams->Sata[0].SataControllerAutoShutdown;
  //Update for Silicon
  FchParams->Sata[2].SataControllerAutoShutdown         = FchParams->Sata[0].SataControllerAutoShutdown;
  FchParams->Sata[3].SataControllerAutoShutdown         = FchParams->Sata[0].SataControllerAutoShutdown;

  FchParams->Sata[1].SataBISTLComplianceMode = FchParams->Sata[0].SataBISTLComplianceMode;
  FchParams->Sata[2].SataBISTLComplianceMode = FchParams->Sata[0].SataBISTLComplianceMode;
  FchParams->Sata[3].SataBISTLComplianceMode = FchParams->Sata[0].SataBISTLComplianceMode;

  //Sata controller
  {
    UINT8 SataController;
    UINT8 SataSgpioEnable;
    UINT64 SataEspEnable;
    UINT64 SataPortShutdown;
    UINT64 SataPortMode;

    SataEspEnable    = PcdGet64 (PcdSataMultiDiePortESP);
    SataPortShutdown = PcdGet64 (PcdSataMultiDiePortShutDown);
    SataPortMode     = PcdGet64 (PcdSataIoDie0PortMode);
    SataSgpioEnable  = PcdGet8 (PcdSataSgpioMultiDieEnable);

    for (SataController = 0; SataController < KUNLUN_SATA_CONTROLLER_NUM; SataController++) {
      FchParams->Sata[SataController].SataEspPort     = (UINT8)(SataEspEnable >> (8 * SataController));
      FchParams->Sata[SataController].SataPortPower   = (UINT8)(SataPortShutdown >> (8 * SataController));
      FchParams->Sata[SataController].SataPortMd      = (UINT16)(SataPortMode >> (16 * SataController));
      FchParams->Sata[SataController].SataSgpio0      = (UINT8)((SataSgpioEnable >> SataController) & BIT0);
      FchParams->Sata[SataController].SataUBMDiagMode = PcdGetBool (PcdSataUBMDiagMode);
    }
  }

  FchParams->Hpet.HpetEnable              = PcdGetBool (PcdHpetEnable);
  FchParams->Hpet.HpetMsiDis              = PcdGetBool (PcdHpetMsiDis);
  FchParams->Hpet.HpetBase                = 0xFED00000;

  FchParams->HwAcpi.SpreadSpectrum        = PcdGetBool (PcdSpreadSpectrum);
  FchParams->HwAcpi.WatchDogTimerBase     = 0xFEB00000;
  FchParams->Misc.NoneSioKbcSupport       = PcdGetBool (PcdNoneSioKbcSupport);
  FchParams->HwAcpi.PwrFailShadow         = PcdGet8 (PcdPwrFailShadow);
  FchParams->HwAcpi.StressResetMode       = PcdGet8 (PcdStressResetMode);
  FchParams->HwAcpi.NoClearThermalTripSts = PcdGetBool (PcdNoClearThermalTripSts);
  FchParams->HwAcpi.FchAlinkRasSupport    = PcdGetBool (PcdAmdFchAlinkRasSupport);
  FchParams->HwAcpi.OemProgrammingTablePtr     = PcdGetPtr (PcdOemProgrammingTablePtr);
  FchParams->HwAcpi.I2c0SdaHold           = PcdGet32 (PcdAmdFchI2c0SdaHold);
  FchParams->HwAcpi.I2c1SdaHold           = PcdGet32 (PcdAmdFchI2c1SdaHold);
  FchParams->HwAcpi.I2c2SdaHold           = PcdGet32 (PcdAmdFchI2c2SdaHold);
  FchParams->HwAcpi.I2c3SdaHold           = PcdGet32 (PcdAmdFchI2c3SdaHold);
  FchParams->HwAcpi.I2c4SdaHold           = PcdGet32 (PcdAmdFchI2c4SdaHold);
  FchParams->HwAcpi.I2c5SdaHold           = PcdGet32 (PcdAmdFchI2c5SdaHold);
  FchParams->HwAcpi.FchSxEntryXhciPmeEn   = PcdGetBool (PcdFchSxEntryXhciPmeEnWA);
  FchParams->HwAcpi.FchAoacProgramEnable  = PcdGetBool (PcdFchAoacInitEnable);
  FchParams->Gpp.SerialDebugBusEnable     = PcdGetBool (PcdSerialDebugBusEnable);
  FchParams->Gcpu.TimerTickTrack          = PcdGet8 (PcdTimerTickTrack);
  FchParams->Gcpu.ClockInterruptTag       = PcdGet8 (PcdClockInterruptTag);
  FchParams->Misc.NativePcieSupport       = PcdGetBool (PcdNativePcieSupport);
  FchParams->Misc.Cppc.CppcSupport        = ((PcdGet32 (PcdSmuFeatureControlDefines) & FEATURE_CPPC_MASK) ? TRUE : FALSE);
  FchParams->Misc.Cppc.SciBit             = PcdGet32 (PcdCppcSciBitMap);
  FchParams->Misc.FchiLa1MTraceMemoryEn   = PcdGetBool (PcdFchiLa1MTraceMemoryEn);

  FchParams->Sd.SdConfig                  = PcdGet8 (PcdSdConfig);
//  FchParams->Sd.SdClockMultiplier         = PcdGetBool (PcdSdClockMultiplier);
//  FchParams->Sd.SdDbgConfig               = PcdGet8 (PcdSdDbgConfig);

  FchParams->Emmc.EmmcEnable              = PcdGet8 (PcdEmmcEnable);
  FchParams->Emmc.EmmcType                = PcdGet8 (PcdEmmcType);
  FchParams->Emmc.EmmcDriverType          = PcdGet8 (PcdEmmcDriverType);
  FchParams->Emmc.EmmcBoot                = PcdGetBool (PcdEmmcBoot);
  FchParams->Emmc.EmmcAdma2Support        = PcdGetBool (PcdEmmcAdma2Support);
  FchParams->Emmc.EmmcAdmaSupport         = PcdGetBool (PcdEmmcAdmaSupport);
  FchParams->Emmc.EmmcSdmaSupport         = PcdGetBool (PcdEmmcSdmaSupport);
  FchParams->Emmc.EmmcA64bSupport         = PcdGetBool (PcdEmmcA64bSupport);
  FchParams->Emmc.EmmcD3Support           = PcdGetBool (PcdEmmcD3Support);
  FchParams->FchRunTime.FchDeviceEnableMap   = PcdGet32 (FchRTDeviceEnableMap);
  FchParams->FchRunTime.FchDeviceEnableMapEx = PcdGet32 (FchRTDeviceEnableMapEx);

  if (PcdGet16 (PcdAmdIdsDebugPrintSerialPortSelect) & BIT15) {
    FchParams->FchRunTime.FchAcpiDeviceInvisibleMap = PcdGet16 (PcdAmdIdsDebugPrintSerialPortSelect) & ~BIT15;
  }
  if ((FchParams->FchRunTime.FchAcpiDeviceInvisibleMap & BIT0) || (FchParams->FchRunTime.FchAcpiDeviceInvisibleMap & BIT8)) {
    FchParams->FchRunTime.FchDeviceEnableMap |= BIT11;
  }
  if ((FchParams->FchRunTime.FchAcpiDeviceInvisibleMap & BIT1) || (FchParams->FchRunTime.FchAcpiDeviceInvisibleMap & BIT9)) {
    FchParams->FchRunTime.FchDeviceEnableMap |= BIT12;
  }
  if ((FchParams->FchRunTime.FchAcpiDeviceInvisibleMap & BIT2) || (FchParams->FchRunTime.FchAcpiDeviceInvisibleMap & BIT10)) {
    FchParams->FchRunTime.FchDeviceEnableMap |= BIT16;
  }
  if ((FchParams->FchRunTime.FchAcpiDeviceInvisibleMap & BIT3) || (FchParams->FchRunTime.FchAcpiDeviceInvisibleMap & BIT11)) {
    FchParams->FchRunTime.FchDeviceEnableMap |= BIT26;
  }

  FchParams->FchRunTime.FchAcpiDeviceInvisibleMapEx = PcdGet16 (PcdFchAcpiDeviceInvisibeMapEx);
  FchParams->FchRunTime.PcieMmioBase       = PcdGet64 (PcdPciExpressBaseAddress);
  if (FchParams->FchRunTime.FchDeviceEnableMap & BIT11) {
    UartLegacy[0] = PcdGet8 (FchUart0LegacyEnable);
  } else {
    UartLegacy[0] = 0;
  }
  if (FchParams->FchRunTime.FchDeviceEnableMap & BIT12) {
    UartLegacy[1] = PcdGet8 (FchUart1LegacyEnable);
  } else {
    UartLegacy[1] = 0;
  }
  if (FchParams->FchRunTime.FchDeviceEnableMap & BIT16) {
    UartLegacy[2] = PcdGet8 (FchUart2LegacyEnable);
  } else {
    UartLegacy[2] = 0;
  }
  if (FchParams->FchRunTime.FchDeviceEnableMap & BIT26) {
    UartLegacy[3] = PcdGet8 (FchUart3LegacyEnable);
  } else {
    UartLegacy[3] = 0;
  }

  IoEnable16 = 0;
  for (UartChannel = 0; UartChannel < 4; UartChannel++ ) {
    if (UartLegacy[UartChannel]) {
      IoEnable16 |= (BIT0 << (UartLegacy[UartChannel] - 1)) + (UartChannel << (8 + ((UartLegacy[UartChannel] - 1) * 2)));
    }
  }
  FchParams->FchRunTime.Al2AhbLegacyUartIoEnable = IoEnable16;

  FchParams->Smbus.SmbusSsid              = PcdGet32 (PcdSmbusSsid);
  FchParams->Spi.LpcSsid                  = PcdGet32 (PcdIsaBridgeSsid);
  FchParams->Sd.SdSsid                    = PcdGet32 (PcdSdSsid);
  FchParams->Usb.XhciSsid                 = PcdGet32 (PcdXhciSsid);

  FchParams->FchRunTime.Uart0Irq = PcdGet8 (PcdFchUart0Irq);
  FchParams->FchRunTime.Uart1Irq = PcdGet8 (PcdFchUart1Irq);
  FchParams->FchRunTime.Uart2Irq = PcdGet8 (PcdFchUart2Irq);
  FchParams->FchRunTime.Uart3Irq = PcdGet8 (PcdFchUart3Irq);
  FchParams->FchRunTime.I2c0Irq = PcdGet8 (PcdFchI2c0Irq);
  FchParams->FchRunTime.I2c1Irq = PcdGet8 (PcdFchI2c1Irq);
  FchParams->FchRunTime.I2c2Irq = PcdGet8 (PcdFchI2c2Irq);
  FchParams->FchRunTime.I2c3Irq = PcdGet8 (PcdFchI2c3Irq);
  FchParams->FchRunTime.I2c4Irq = PcdGet8 (PcdFchI2c4Irq);
  FchParams->FchRunTime.I2c5Irq = PcdGet8 (PcdFchI2c5Irq);

  FchParams->FchRunTime.DimmsPerChannel = FixedPcdGet8 (PcdAmdMemMaxDimmPerChannelV2);
  FchParams->HwAcpi.SpdHostCtrlRelease = PcdGetBool (PcdAmdFchSpdHostCtrlRelease);
  FchParams->HwAcpi.DimmTelemetry      = PcdGetBool (PcdAmdFchDimmTelemetry);

  return FchParams;
}


/**
 * @brief Entry point of the Kunlun FCH DXE Driver.
 *
 * @details Perform the configuration init, resource reservation, early post init
 *   and install all the supported protocol
 *
 * @param[in] ImageHandle EFI Image Handle for the DXE driver
 * @param[in] SystemTable Pointer to the EFI system table
 *
 * @returns EFI_STATUS
 * @retval EFI_SUCCESS   Module initialized successfully
 * @retval EFI_ERROR     Initialization failed (see error for more details)
 */
EFI_STATUS
EFIAPI
FchDxeInit (
  IN       EFI_HANDLE         ImageHandle,
  IN       EFI_SYSTEM_TABLE   *SystemTable
  )
{
  FCH_DXE_PRIVATE     *FchPrivate;
  EFI_STATUS          Status;
  EFI_HANDLE          Handle;
  FCH_DATA_BLOCK      *FchDataBlock;
  EFI_EVENT           PciIoEvent;
  VOID                *Registration;

  AGESA_TESTPOINT (TpFchDxeEntry, NULL);
  //
  // Initialize EFI library
  //

  //
  // Initialize the configuration structure and private data area
  //
  // Allocate memory for the private data
  Status = gBS->AllocatePool (
                  EfiACPIMemoryNVS,
                  sizeof (FCH_DXE_PRIVATE),
                  (VOID **)&FchPrivate
                  );

  ASSERT_EFI_ERROR (Status);

  Status = gBS->AllocatePool (
                  EfiACPIMemoryNVS,
                  sizeof (FCH_DATA_BLOCK),
                  (VOID **)&FchDataBlock
                  );

  ASSERT_EFI_ERROR (Status);

  FchDataBlock = FchInitDataBlock (FchDataBlock);
  // Initialize the private data structure
  FchPrivate->Signature = FCH_DXE_PRIVATE_DATA_SIGNATURE;
  // Initialize the FCHInit protocol
  FchPrivate->FchInit.Revision             = FCH_INIT_REV;
  FchPrivate->FchInit.FchRev               = FCH_VERSION_KUNLUN;
  FchPrivate->FchInit.FchPolicy            = (VOID*) FchDataBlock;
  FchPrivate->FchInit.FchPtPolicy          = (VOID*) &(FchDataBlock->Promontory);
  FchPrivate->FchInit.FpUsbPortDisable2    = NULL;

  //
  // Publish the FCHInit protocol
  //
  Handle = ImageHandle;
  Status = gBS->InstallProtocolInterface (
                  &Handle,
                  &gFchInitProtocolGuid,
                  EFI_NATIVE_INTERFACE,
                  &FchPrivate->FchInit
                  );

  if (EFI_ERROR (Status)) {
    return (Status);
  }

  //
  // Enviroment Init Entry
  //
  IDS_HDT_CONSOLE (FCH_TRACE, "[FchInitEnv] Fch Init - Before PCI Scan ...Start\n");
  Status = FchInitEnv (FchDataBlock);
  IDS_HDT_CONSOLE (FCH_TRACE, "[FchInitEnv] Fch Init - Before PCI Scan ...complete\n");

  if (FchDataBlock->Misc.FchiLa1MTraceMemoryBase) {
    FCH_PCDSET32 (PcdFchiLa1MTraceMemoryBase,FchDataBlock->Misc.FchiLa1MTraceMemoryBase);
  }
  //
  // Register the event handling function for FchInitMid to be launched after
  // PciIo protocol
  //
  Status = gBS->CreateEventEx (
             EVT_NOTIFY_SIGNAL,
             TPL_NOTIFY,
             InvokeFchInitMid,
             FchPrivate->FchInit.FchPolicy,
             NULL,
             &PciIoEvent
             );

  Status = gBS->RegisterProtocolNotify (
              &gEfiPciIoProtocolGuid,
              PciIoEvent,
              &Registration
              );

  //
  // Register the event handling function for FchInitLate to be launched after
  // Ready to Boot
  //
  Status = EfiCreateEventReadyToBootEx (
             TPL_CALLBACK,
             InvokeFchInitLate,
             FchPrivate->FchInit.FchPolicy,
             &FchPrivate->EventReadyToBoot
             );

  Status = EfiCreateEventLegacyBootEx (
             TPL_CALLBACK,
             InvokeFchInitLate,
             FchPrivate->FchInit.FchPolicy,
             &FchPrivate->EventReadyToBoot
             );

  //
  // Install gFchInitDonePolicyProtocolGuid to signal Platform
  //
  Handle = ImageHandle;
  Status = gBS->InstallProtocolInterface (
                  &Handle,
                  &gFchInitDonePolicyProtocolGuid,
                  EFI_NATIVE_INTERFACE,
                  NULL
                  );

  AGESA_TESTPOINT (TpFchDxeExit, NULL);

  return (Status);
}



