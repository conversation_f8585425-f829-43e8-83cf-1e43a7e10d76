#;*****************************************************************************
#;
#; Copyright (C) 2008-2024 Advanced Micro Devices, Inc. All rights reserved.
#;
#;******************************************************************************
[Defines]
  INF_VERSION                    = 0x00010005
  BASE_NAME                      = FchSpiAccessRom2V2Lib
  FILE_GUID                      = a6dd135d-ddae-4e8c-bb7f-dfb467e6ef3c
  MODULE_TYPE                    = BASE
  VERSION_STRING                 = 1.0
  LIBRARY_CLASS                  = FchSpiAccessLib

#
# The following information is for reference only and not required by the build tools.
#
#  VALID_ARCHITECTURES           = IA32 X64
#
#
#  This instance for SPI ROM READ accordting to SPI ROM2 window mapping configuration, do not support runtime yet.

[Sources]
  FchSpiAccessCommon.c
  FchSpiAccessRom2V2Lib.c

[Packages]
  AgesaModulePkg/AgesaModuleFchPkg.dec
  MdePkg/MdePkg.dec
  AgesaPkg/AgesaPkg.dec

[LibraryClasses]
  IoLib
  PciLib
  DebugLib
  BaseLib
  BaseMemoryLib
  FchBaseLib


