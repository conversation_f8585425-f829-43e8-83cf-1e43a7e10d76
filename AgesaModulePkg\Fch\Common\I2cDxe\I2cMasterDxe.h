/*****************************************************************************
 *
 * Copyright (C) 2015-2024 Advanced Micro Devices, Inc. All rights reserved.
 *
 *******************************************************************************
 */

#ifndef _AMD_I2C_MASTER_DXE_H_
#define _AMD_I2C_MASTER_DXE_H_

#include <Library/FchI2cLib.h>
#include <Library/DebugLib.h>
#include <Library/PcdLib.h>
#include <Library/BaseMemoryLib.h>
#include <Library/FchBaseLib.h>
#include <Protocol/I2cMaster.h>

/// I2cMaster Private struct
typedef struct {
  CONTROLLER_DEVICE_PATH    ControllerDp;
  EFI_DEVICE_PATH_PROTOCOL  End;
} I2C_CONTROLLER_DEVICE_PATH;


typedef struct _FCH_EFI_DXE_I2C_MASTER_PROTOCOL {
  EFI_I2C_MASTER_PROTOCOL   I2cController;              // I2cController;
  UINT8                     I2cControllerInitialized;   // I2cControllerInitialized
  UINT32                    ControllerNum;              // ControllerNum;
  UINT32                    I2cBaseAddress;             // I2cBaseAddress
  UINT32                    I2cSdaHoldTime;             // I2cSdaHoldTime
  UINT32                    I2cBusFrequency;            // I2cBusFrequency
  UINT32                    RxFifoDepth;                // RxFifoDepth
  UINT32                    TxFifoDepth;                // TxFifoDepth
} FCH_EFI_DXE_I2C_MASTER_PROTOCOL;

EFI_STATUS
EFIAPI
SetBusFrequency (
  IN  CONST EFI_I2C_MASTER_PROTOCOL   *This,
  IN  OUT UINTN                       *BusClockHertz
  );

EFI_STATUS
EFIAPI
Reset (
  IN  CONST EFI_I2C_MASTER_PROTOCOL   *This
  );

EFI_STATUS
EFIAPI
StartRequest (
  IN  CONST EFI_I2C_MASTER_PROTOCOL   *This,
  IN  UINTN                           SlaveAddress,
  IN  EFI_I2C_REQUEST_PACKET          *RequestPacket,
  IN  EFI_EVENT                       Event      OPTIONAL,
  OUT EFI_STATUS                      *I2cStatus OPTIONAL
  );

EFI_STATUS
EFIAPI
AmdI2cMasterDxeInit (
  IN  EFI_HANDLE        ImageHandle,
  IN  EFI_SYSTEM_TABLE  *SystemTable
  );

#endif // _AMD_I2C_MASTER_DXE_H_

