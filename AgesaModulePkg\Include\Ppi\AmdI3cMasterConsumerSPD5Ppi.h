/*****************************************************************************
 *
 * Copyright (C) 2015-2024 Advanced Micro Devices, Inc. All rights reserved.
 *
 *******************************************************************************
 */

#ifndef _AMD_I3C_MASTER_CONSUMER_SPD5_H_
#define _AMD_I3C_MASTER_CONSUMER_SPD5_H_

typedef
EFI_STATUS
(EFIAPI *SPDI3C_SET_PPI) (
  IN CONST EFI_PEI_SERVICES  **PeiServices,
  IN       UINTN             BusSelect,
  IN       UINTN             SlaveAddress,
  IN       UINT16            Command,
  IN       UINT8             Data
  );

typedef
<PERSON><PERSON>_STATUS
(EFIAPI *SPDI3C_GET_PPI) (
  IN CONST EFI_PEI_SERVICES  **PeiServices,
  IN       UINTN             BusSelect,
  IN       UINTN             SlaveAddress,
  IN       UINT16            Command,
  OUT      UINT8             *Data
  );

///
/// This PPI provide interface to set SPD5 via I3C
///
typedef struct _EFI_PEI_SPDI3C_PPI {
  UINTN    Revision;                  ///< Revision Number
  SPDI3C_SET_PPI Set;                ///< Write Register
  SPDI3C_GET_PPI Get;                ///< Read Register
} EFI_PEI_SPDI3C_PPI;

// Current PPI revision
#define SPDI3C_PPI_REVISION   (0x01)

extern EFI_GUID gSpd5I3cPpiGuid;

EFI_STATUS
EFIAPI
SpdI3cSet (
  IN CONST EFI_PEI_SERVICES  **PeiServices,
  IN       UINTN             BusSelect,
  IN       UINTN             SlaveAddress,
  IN       UINT16            Command,
  IN       UINT8             Data
  );

EFI_STATUS
EFIAPI
SpdI3cGet (
  IN CONST EFI_PEI_SERVICES  **PeiServices,
  IN       UINTN             BusSelect,
  IN       UINTN             SlaveAddress,
  IN       UINT16            Command,
  OUT      UINT8             *Data
  );

#endif // _SPD_I3C_PEI_H_



