/*****************************************************************************
 *
 * Copyright (C) 2022-2024 Advanced Micro Devices, Inc. All rights reserved.
 *
 *******************************************************************************
 */

#include <PiPei.h>
#include <Uefi.h>
#include <string.h>
#include <stdlib.h>
#include <Library/UefiLib.h>
#include <Library/DebugLib.h>
#include <Library/UnitTestLib.h>
#include <Library/PrintLib.h>

#include "AGESA.h"
#include <FchCommonCfg.h>
#include "../KLXhciEnv.c"

#define UNIT_TEST_NAME     "FCH Kunlun USB Unit Test"
#define UNIT_TEST_VERSION  "0.1"

#define MDE_CPU_AARCH64

///
/// Global variables used in unit tests
///

/**
  @brief Unit test that verifies FchInitEnvUsb

  @param[in] Context     [Optional] An optional parameter that enables:
                         1) test-case reuse with varied parameters and
                         2) test-case re-entry for Target tests that need a
                         reboot.  This parameter is a VOID* and it is the
                         responsibility of the test author to ensure that the
                         contents are well understood by all test cases that may
                         consume it.

  @retval UNIT_TEST_PASSED              The Unit test has completed and the test
                                        case was successful.
  @retval UNIT_TEST_ERROR_TEST_FAILED   A test case assertion has failed.
**/
UNIT_TEST_STATUS
EFIAPI
FchInitEnvUsbTest1 (
  IN UNIT_TEST_CONTEXT  Context
  )
{
  FCH_DATA_BLOCK  *FchDataBlock;

  FchDataBlock = (FCH_DATA_BLOCK*) malloc(sizeof(FCH_DATA_BLOCK));
  memset(FchDataBlock, 0 , sizeof(FchDataBlock));

  FchInitEnvUsb ((VOID *) FchDataBlock);

  return UNIT_TEST_PASSED;
}

/**
  @brief Unit test that verifies FchInitLateUsb

  @param[in] Context     [Optional] An optional parameter that enables:
                         1) test-case reuse with varied parameters and
                         2) test-case re-entry for Target tests that need a
                         reboot.  This parameter is a VOID* and it is the
                         responsibility of the test author to ensure that the
                         contents are well understood by all test cases that may
                         consume it.

  @retval UNIT_TEST_PASSED              The Unit test has completed and the test
                                        case was successful.
  @retval UNIT_TEST_ERROR_TEST_FAILED   A test case assertion has failed.
**/
UNIT_TEST_STATUS
EFIAPI
FchInitLateUsbTest1 (
  IN UNIT_TEST_CONTEXT  Context
  )
{
  FCH_DATA_BLOCK  *FchDataBlock;

  FchDataBlock = (FCH_DATA_BLOCK*) malloc(sizeof(FCH_DATA_BLOCK));
  memset(FchDataBlock, 0 , sizeof(FchDataBlock));

  FchInitLateUsb ((VOID *) FchDataBlock);

  return UNIT_TEST_PASSED;
}

/**
  @brief Unit test that verifies FchInitMidUsb

  @param[in] Context     [Optional] An optional parameter that enables:
                         1) test-case reuse with varied parameters and
                         2) test-case re-entry for Target tests that need a
                         reboot.  This parameter is a VOID* and it is the
                         responsibility of the test author to ensure that the
                         contents are well understood by all test cases that may
                         consume it.

  @retval UNIT_TEST_PASSED              The Unit test has completed and the test
                                        case was successful.
  @retval UNIT_TEST_ERROR_TEST_FAILED   A test case assertion has failed.
**/
UNIT_TEST_STATUS
EFIAPI
FchInitMidUsbTest1 (
  IN UNIT_TEST_CONTEXT  Context
  )
{
  FCH_DATA_BLOCK  *FchDataBlock;

  FchDataBlock = (FCH_DATA_BLOCK*) malloc(sizeof(FCH_DATA_BLOCK));
  memset(FchDataBlock, 0 , sizeof(FchDataBlock));

  FchInitMidUsb ((VOID *) FchDataBlock);

  return UNIT_TEST_PASSED;
}

/**
  @brief Unit test that verifies FchInitResetUsb

  @param[in] Context     [Optional] An optional parameter that enables:
                         1) test-case reuse with varied parameters and
                         2) test-case re-entry for Target tests that need a
                         reboot.  This parameter is a VOID* and it is the
                         responsibility of the test author to ensure that the
                         contents are well understood by all test cases that may
                         consume it.

  @retval UNIT_TEST_PASSED              The Unit test has completed and the test
                                        case was successful.
  @retval UNIT_TEST_ERROR_TEST_FAILED   A test case assertion has failed.
**/
UNIT_TEST_STATUS
EFIAPI
FchInitResetUsbTest1 (
  IN UNIT_TEST_CONTEXT  Context
  )
{
  FCH_DATA_BLOCK  *FchDataBlock;

  FchDataBlock = (FCH_DATA_BLOCK*) malloc(sizeof(FCH_DATA_BLOCK));
  memset(FchDataBlock, 0 , sizeof(FchDataBlock));

  FchInitResetUsb ((VOID *) FchDataBlock);

  return UNIT_TEST_PASSED;
}

/**
  @brief Unit test that verifies FchInitEnvUsbXhci

  @param[in] Context     [Optional] An optional parameter that enables:
                         1) test-case reuse with varied parameters and
                         2) test-case re-entry for Target tests that need a
                         reboot.  This parameter is a VOID* and it is the
                         responsibility of the test author to ensure that the
                         contents are well understood by all test cases that may
                         consume it.

  @retval UNIT_TEST_PASSED              The Unit test has completed and the test
                                        case was successful.
  @retval UNIT_TEST_ERROR_TEST_FAILED   A test case assertion has failed.
**/
UNIT_TEST_STATUS
EFIAPI
FchInitEnvUsbXhciTest1 (
  IN UNIT_TEST_CONTEXT  Context
  )
{
  FCH_DATA_BLOCK  *FchDataBlock;

  FchDataBlock = (FCH_DATA_BLOCK*) malloc(sizeof(FCH_DATA_BLOCK));
  memset(FchDataBlock, 0 , sizeof(FchDataBlock));

  FchInitEnvUsbXhci ((VOID *) FchDataBlock);

  return UNIT_TEST_PASSED;
}

/**
  @brief Unit test that verifies FchInitLateUsbXhci

  @param[in] Context     [Optional] An optional parameter that enables:
                         1) test-case reuse with varied parameters and
                         2) test-case re-entry for Target tests that need a
                         reboot.  This parameter is a VOID* and it is the
                         responsibility of the test author to ensure that the
                         contents are well understood by all test cases that may
                         consume it.

  @retval UNIT_TEST_PASSED              The Unit test has completed and the test
                                        case was successful.
  @retval UNIT_TEST_ERROR_TEST_FAILED   A test case assertion has failed.
**/
UNIT_TEST_STATUS
EFIAPI
FchInitLateUsbXhciTest1 (
  IN UNIT_TEST_CONTEXT  Context
  )
{
  FCH_DATA_BLOCK  *FchDataBlock;

  FchDataBlock = (FCH_DATA_BLOCK*) malloc(sizeof(FCH_DATA_BLOCK));
  memset(FchDataBlock, 0 , sizeof(FchDataBlock));

  FchInitLateUsbXhci ((VOID *) FchDataBlock);

  return UNIT_TEST_PASSED;
}

/**
  @brief Unit test that verifies FchInitMidUsbXhci

  @param[in] Context     [Optional] An optional parameter that enables:
                         1) test-case reuse with varied parameters and
                         2) test-case re-entry for Target tests that need a
                         reboot.  This parameter is a VOID* and it is the
                         responsibility of the test author to ensure that the
                         contents are well understood by all test cases that may
                         consume it.

  @retval UNIT_TEST_PASSED              The Unit test has completed and the test
                                        case was successful.
  @retval UNIT_TEST_ERROR_TEST_FAILED   A test case assertion has failed.
**/
UNIT_TEST_STATUS
EFIAPI
FchInitMidUsbXhciTest1 (
  IN UNIT_TEST_CONTEXT  Context
  )
{
  FCH_DATA_BLOCK  *FchDataBlock;

  FchDataBlock = (FCH_DATA_BLOCK*) malloc(sizeof(FCH_DATA_BLOCK));
  memset(FchDataBlock, 0 , sizeof(FchDataBlock));

  FchInitMidUsbXhci ((VOID *) FchDataBlock);

  return UNIT_TEST_PASSED;
}

/**
  @brief Unit test that verifies FchInitResetXhci

  @param[in] Context     [Optional] An optional parameter that enables:
                         1) test-case reuse with varied parameters and
                         2) test-case re-entry for Target tests that need a
                         reboot.  This parameter is a VOID* and it is the
                         responsibility of the test author to ensure that the
                         contents are well understood by all test cases that may
                         consume it.

  @retval UNIT_TEST_PASSED              The Unit test has completed and the test
                                        case was successful.
  @retval UNIT_TEST_ERROR_TEST_FAILED   A test case assertion has failed.
**/
UNIT_TEST_STATUS
EFIAPI
FchInitResetXhciTest1 (
  IN UNIT_TEST_CONTEXT  Context
  )
{
  FCH_DATA_BLOCK  *FchDataBlock;

  FchDataBlock = (FCH_DATA_BLOCK*) malloc(sizeof(FCH_DATA_BLOCK));
  memset(FchDataBlock, 0 , sizeof(FchDataBlock));

  FchInitResetXhci ((VOID *) FchDataBlock);

  return UNIT_TEST_PASSED;
}

/**
  @brief Unit test that verifies FchKLXhciIohcPmeDisable

  @param[in] Context     [Optional] An optional parameter that enables:
                         1) test-case reuse with varied parameters and
                         2) test-case re-entry for Target tests that need a
                         reboot.  This parameter is a VOID* and it is the
                         responsibility of the test author to ensure that the
                         contents are well understood by all test cases that may
                         consume it.

  @retval UNIT_TEST_PASSED              The Unit test has completed and the test
                                        case was successful.
  @retval UNIT_TEST_ERROR_TEST_FAILED   A test case assertion has failed.
**/
UNIT_TEST_STATUS
EFIAPI
FchKLXhciIohcPmeDisableTest1 (
  IN UNIT_TEST_CONTEXT  Context
  )
{
  FchKLXhciIohcPmeDisable (0 , 0);

  return UNIT_TEST_PASSED;
}

/**
  @brief Initialize the unit test framework, suite, and unit tests for the
  sample unit tests and run the unit tests.

  @retval EFI_SUCCESS           All test cases were dispatched.
  @retval EFI_OUT_OF_RESOURCES  There are not enough resources available to
                                 initialize the unit tests.
**/
EFI_STATUS
EFIAPI
UefiTestMain (
  VOID
  )
{
  EFI_STATUS                  Status;
  UNIT_TEST_FRAMEWORK_HANDLE  Framework;
  UNIT_TEST_SUITE_HANDLE      FchTests;

  Framework = NULL;

  DEBUG ((DEBUG_INFO, "%a v%a\n", UNIT_TEST_NAME, UNIT_TEST_VERSION));

  //
  // Start setting up the test framework for running the tests.
  //
  Status = InitUnitTestFramework (&Framework, UNIT_TEST_NAME, gEfiCallerBaseName, UNIT_TEST_VERSION);
  if (EFI_ERROR (Status)) {
    goto EXIT;
  }

  //
  // Populate the Unit Test Suite.
  //
  Status = CreateUnitTestSuite (&FchTests, Framework, "FCH KLUsb Test", "FCH.KLUsb", NULL, NULL);
  if (EFI_ERROR (Status)) {
    Status = EFI_OUT_OF_RESOURCES;
    goto EXIT;
  }

  AddTestCase (FchTests, "KLUsbEnv.c", "FchInitEnvUsb", FchInitEnvUsbTest1, NULL, NULL, NULL);
  AddTestCase (FchTests, "KLUsbLate.c", "FchInitLateUsb", FchInitLateUsbTest1, NULL, NULL, NULL);
  AddTestCase (FchTests, "KLUsbMid.c", "FchInitMidUsb", FchInitMidUsbTest1, NULL, NULL, NULL);
  AddTestCase (FchTests, "KLUsbReset.c", "FchInitResetUsb", FchInitResetUsbTest1, NULL, NULL, NULL);
  AddTestCase (FchTests, "KLXhciEnv.c", "FchInitEnvUsbXhci", FchInitEnvUsbXhciTest1, NULL, NULL, NULL);
  AddTestCase (FchTests, "KLXhciLate.c", "FchInitLateUsbXhci", FchInitLateUsbXhciTest1, NULL, NULL, NULL);
  AddTestCase (FchTests, "KLXhciMid.c", "FchInitMidUsbXhci", FchInitMidUsbXhciTest1, NULL, NULL, NULL);
  AddTestCase (FchTests, "KLXhciService.c", "FchKLXhciIohcPmeDisable", FchKLXhciIohcPmeDisableTest1, NULL, NULL, NULL);

  //
  // Execute the tests.
  //
  Status = RunAllTestSuites (Framework);

EXIT:
  if (Framework) {
    FreeUnitTestFramework (Framework);
  }

  return Status;
}

/**
  Standard POSIX C entry point for host based unit test execution.
**/
int
main (
  int   argc,
  char  *argv[]
  )
{
  return UefiTestMain ();
}

