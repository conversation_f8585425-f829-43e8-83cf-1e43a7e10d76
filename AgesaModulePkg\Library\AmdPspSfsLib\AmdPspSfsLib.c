/*****************************************************************************
 * Copyright (C) 2023-2024 Advanced Micro Devices, Inc. All rights reserved.
 *
*****************************************************************************
*/
/* $NoKeywords:$ */
/**
 * @file
 *
 * PSP SFS related functions
 *
 *
 * @xrefitem bom "File Content Label" "Release Content"
 * @e project:      AGESA
 * @e sub-project:  PSP
 */
#include "Uefi.h"
#include "AGESA.h"
#include <Library/UefiBootServicesTableLib.h>
#include <Library/AmdBaseLib.h>
#include <Library/BaseMemoryLib.h>
#include <Library/AmdPspMboxLibV2.h>
#include <Protocol/AmdApcbProtocol.h>
#include <BRH/ApcbV3TokenUid.h>
#include <Filecode.h>

#define FILECODE LIBRARY_AMDPSPSFSLIB_AMDPSPSFSLIB_FILECODE

UINT8 TestKeyData[RSA2048_FILE_SIZE] = {
  // Offset 0x00000000 to 0x0000023F
  0x01, 0x00, 0x00, 0x00, 0xF7, 0x8F, 0xAA, 0xA0, 0x26, 0xD3, 0x2B, 0xC1,
  0x62, 0x81, 0xEC, 0xD7, 0x69, 0xFA, 0xDE, 0x78, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x08, 0x00, 0x00,
  0x00, 0x08, 0x00, 0x00, 0x01, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x25, 0x8C, 0xE5, 0x56,
  0x3F, 0x56, 0x56, 0x6E, 0xA1, 0x56, 0x0B, 0xB6, 0x6E, 0x19, 0x07, 0x77,
  0xBE, 0xC6, 0x12, 0xE6, 0xB0, 0x7A, 0xAE, 0xC4, 0x5B, 0xAB, 0xE2, 0x56,
  0x28, 0x4F, 0xD7, 0x7F, 0x49, 0x1F, 0xE3, 0x92, 0xED, 0xD8, 0x79, 0x1F,
  0x9F, 0xD3, 0x27, 0xA3, 0x63, 0x82, 0xB3, 0x6A, 0x9B, 0xB6, 0xDF, 0x83,
  0x65, 0xC0, 0x1E, 0x45, 0x1D, 0xF5, 0x0F, 0xDC, 0x55, 0xE0, 0x2E, 0x3A,
  0x4C, 0x28, 0x46, 0x9D, 0xB3, 0x4D, 0x9D, 0xCD, 0x0E, 0xF8, 0x8E, 0xE6,
  0x76, 0x27, 0x51, 0xB7, 0xE6, 0xBD, 0x72, 0x9F, 0x3D, 0x37, 0xC4, 0x3D,
  0xEA, 0xC7, 0x76, 0x20, 0xB9, 0x2F, 0xD5, 0xB5, 0xCA, 0x3A, 0x9A, 0x6D,
  0x21, 0x2B, 0x44, 0xE2, 0xE3, 0xE2, 0x8E, 0x0A, 0x80, 0xF3, 0x24, 0x3F,
  0x38, 0x9D, 0x0D, 0xFD, 0xC5, 0x73, 0x62, 0x80, 0x5B, 0x86, 0xD4, 0x82,
  0x35, 0x9C, 0x26, 0x3C, 0xBF, 0x5F, 0x2F, 0xCA, 0x4D, 0x99, 0xF0, 0xBC,
  0xE8, 0xCE, 0x7E, 0xD2, 0xC7, 0x36, 0xD9, 0x93, 0xBA, 0x45, 0xDB, 0xB0,
  0xF0, 0xED, 0xC5, 0xFE, 0x4E, 0x48, 0x73, 0x81, 0x5E, 0x53, 0x77, 0xF9,
  0xB2, 0xFE, 0xDD, 0x6F, 0x5D, 0x50, 0x0B, 0xED, 0xA4, 0x38, 0x4F, 0x89,
  0xC0, 0x48, 0x6F, 0x92, 0xB2, 0x9E, 0xD1, 0x95, 0xB7, 0xA1, 0x6B, 0xA3,
  0x15, 0xCB, 0xA2, 0x17, 0xF5, 0x16, 0xF3, 0xA8, 0x3E, 0xD3, 0x8F, 0x02,
  0x9F, 0x0C, 0x50, 0xE0, 0xD1, 0x69, 0xAC, 0xAE, 0x7A, 0x87, 0x91, 0xE0,
  0x3C, 0x98, 0x4F, 0x9D, 0x25, 0x08, 0xDB, 0xAE, 0xBD, 0x90, 0xE7, 0x76,
  0xF2, 0xA7, 0xB2, 0xE3, 0xAA, 0xCF, 0x38, 0xAE, 0x8F, 0x6D, 0x80, 0xDF,
  0x9B, 0x39, 0x3E, 0x87, 0x02, 0x5B, 0x83, 0x6F, 0x01, 0x79, 0x32, 0xC8,
  0x1E, 0x0B, 0xA1, 0x3B, 0xF1, 0xBB, 0xA6, 0x78, 0xC9, 0x29, 0x72, 0xDD
};

/**
 *  Handle the co-signing key if available
 *  and co-signing feature is enabled in BIOS setup
 *
 *  @param VOID
 *
 *  @retval       EFI_SUCCESS       Function succeed
 *  @retval       NON-ZERO          Error occurs
*/

EFI_STATUS
AmdPspProcessCoSignKey (
  VOID
  )
{
  EFI_STATUS                    Status;
  AMD_APCB_SERVICE_PROTOCOL     *ApcbDxeServiceProtocol;
  UINT8                         ApcbPurpose;
  BOOLEAN                       ApcbPspSfsEnableValue;
  BOOLEAN                       ApcbPspSfsCoSignRequiredValue;
  OEM_CO_SIGN_KEY               Key;
  UINT32                        PublicExponentSize;
  UINT32                        ModulusSize;

  IDS_HDT_CONSOLE_PSP_TRACE ("AmdPspProcessCoSignKey - Entry\n");

  ApcbDxeServiceProtocol  = NULL;
  ApcbPurpose             = 0u;
  ZeroMem (&Key, sizeof (Key));

  Status = gBS->LocateProtocol (&gAmdApcbDxeServiceProtocolGuid, NULL, (VOID **)&ApcbDxeServiceProtocol);
  if (EFI_ERROR (Status)) {
    IDS_HDT_CONSOLE_PSP_TRACE ("Unable to locate APCB Protocol\n");
    return Status;
  }

  Status = ApcbDxeServiceProtocol->ApcbGetTokenBool (ApcbDxeServiceProtocol, &ApcbPurpose, APCB_TOKEN_UID_PSP_SFS_ENABLE, &ApcbPspSfsEnableValue);
  if (EFI_ERROR (Status)) {
    IDS_HDT_CONSOLE_PSP_TRACE ("Unable to get APCB token APCB_TOKEN_UID_PSP_SFS_ENABLE, Status:%r\n", Status);
    return Status;
  }

  Status = ApcbDxeServiceProtocol->ApcbGetTokenBool (ApcbDxeServiceProtocol, &ApcbPurpose, APCB_TOKEN_UID_PSP_SFS_COSIGN_REQUIRED, &ApcbPspSfsCoSignRequiredValue);
  if (EFI_ERROR (Status)) {
    IDS_HDT_CONSOLE_PSP_TRACE ("Unable to get APCB token APCB_TOKEN_UID_PSP_SFS_COSIGN_REQUIRED, Status:%r\n", Status);
    return Status;
  }

  if (ApcbPspSfsCoSignRequiredValue && ApcbPspSfsEnableValue) {
    CopyMem (Key.OemCoSignKey, TestKeyData, sizeof (TestKeyData));

    PublicExponentSize = ((KEY_HEADER*)&TestKeyData[0])->PublicExponentSize;
    ModulusSize        = ((KEY_HEADER*)&TestKeyData[0])->ModulusSize;

    IDS_HDT_CONSOLE_PSP_TRACE ("Read Input Customer Co-Sign Key , PublicExponentSize=0x%x, ModulusSize=0x%x\n", PublicExponentSize, ModulusSize);

    if (PublicExponentSize != ModulusSize) {
      IDS_HDT_CONSOLE_PSP_TRACE ("Invalid Input Customer Co-Sign Key, Key Sizes Mismatch, PublicExponentSize=0x%x, ModulusSize=0x%x\n", PublicExponentSize, ModulusSize);
      Status = EFI_UNSUPPORTED;
      return Status;
    }

    if ((PublicExponentSize != (RSA4096_SIZE * 8u)) && (PublicExponentSize != (RSA2048_SIZE * 8u))) {
      IDS_HDT_CONSOLE_PSP_TRACE ("Invalid Input Customer Co-Sign Key, Key Sizes Not 2K OR 4K, PublicExponentSize=0x%x, ModulusSize=0x%x\n", PublicExponentSize, ModulusSize);
      Status = EFI_UNSUPPORTED;
      return Status;
    }

    Key.KeySize = PublicExponentSize;
    Status = PspMboxBiosCmdSendCoSignPublicKey(&Key);
    IDS_HDT_CONSOLE_PSP_TRACE ("Sent Co-Sign Key, KeySize=0x%x, Status:%r\n", Key.KeySize, Status);

  } else {
    IDS_HDT_CONSOLE_PSP_TRACE ("No Co-Sign Key sent, ApcbPspSfsCoSignRequiredValue: %d, ApcbPspSfsEnableValue: %d, Status:%r\n",
                                ApcbPspSfsCoSignRequiredValue, ApcbPspSfsEnableValue, Status);
    Status = EFI_UNSUPPORTED;
  }

  IDS_HDT_CONSOLE_PSP_TRACE ("AmdPspProcessCoSignKey - Exit, Status:%r\n", Status);
  return Status;
}

