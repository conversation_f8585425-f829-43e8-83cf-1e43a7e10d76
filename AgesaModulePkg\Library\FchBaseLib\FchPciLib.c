/*
*****************************************************************************
*
 * Copyright (C) 2008-2024 Advanced Micro Devices, Inc. All rights reserved.
 *
*******************************************************************************
*/

/*----------------------------------------------------------------------------------------
 *                             M O D U L E S    U S E D
 *----------------------------------------------------------------------------------------
 */
#include <Filecode.h>
#include <Library/AmdBaseLib.h>
#include <Library/FchBaseLib.h>
#include <Library/BaseFabricTopologyLib.h>
#include <Library/PciSegmentLib.h>

/*----------------------------------------------------------------------------------------
 *                   D E F I N I T I O N S    A N D    M A C R O S
 *----------------------------------------------------------------------------------------
 */
#define FILECODE LIBRARY_FCHBASELIB_FCHPCILIB_FILECODE


/*----------------------------------------------------------------------------------------
 *                  T Y P E D E F S     A N D     S T R U C T U R E S
 *----------------------------------------------------------------------------------------
 */


/*----------------------------------------------------------------------------------------
 *           P R O T O T Y P E S     O F     L O C A L     F U N C T I O N S
 *----------------------------------------------------------------------------------------
 */


/*----------------------------------------------------------------------------------------
 *                          E X P O R T E D    F U N C T I O N S
 *----------------------------------------------------------------------------------------
 */
 /*----------------------------------------------------------------------------------------*/

/*----------------------------------------------------------------------------------------*/
/**
 * @brief  FchSetPciByteTable - Program PCI register by table (8 bits data)
 *
 *
 *
 * @param[in] pPciByteTable    - Table data pointer
 * @param[in] dwTableSize      - Table length
 * @param[in] StdHeader
 *
 */
VOID
FchSetPciByteTable (
  IN       PCI_REG8_MASK       *pPciByteTable,
  IN       UINT16              dwTableSize,
  IN       AMD_CONFIG_PARAMS   *StdHeader
  )
{
  UINT8     i;
  UINT8     dbBusNo;
  UINT8     dbDevFnNo;
  UINTN     PciAddress;

  dbBusNo = pPciByteTable->RegIndex;
  dbDevFnNo = pPciByteTable->AndMask;
  pPciByteTable++;

  for ( i = 1; i < dwTableSize; i++ ) {
    if ( (pPciByteTable->RegIndex == 0xFF) && (pPciByteTable->AndMask == 0xFF) && (pPciByteTable->OrMask == 0xFF) ) {
      pPciByteTable++;
      dbBusNo = pPciByteTable->RegIndex;
      dbDevFnNo = pPciByteTable->AndMask;
      pPciByteTable++;
      i++;
    } else {
      PciAddress = (dbBusNo << 20) + (dbDevFnNo << 12) + pPciByteTable->RegIndex;
      PciAndThenOr8 (PciAddress, pPciByteTable->AndMask, pPciByteTable->OrMask);
      pPciByteTable++;
    }
  }
}


UINT32
FchPciCfgRead32 (
  IN       UINT32              IohcBus,
  IN       UINT32              Dev,
  IN       UINT32              Fn,
  IN       UINT32              Offset,
  IN       AMD_CONFIG_PARAMS   *StdHeader
  )
{
  UINT64    PciAddress;

  PciAddress = PCI_SEGMENT_LIB_ADDRESS (
                 IohcBus / MAX_PCI_BUS_NUMBER_PER_SEGMENT,
                 IohcBus % MAX_PCI_BUS_NUMBER_PER_SEGMENT,
                 Dev,
                 Fn,
                 Offset
                 );
  return PciSegmentRead32 (PciAddress);
}

VOID
FchPciCfgWrite32 (
  IN       UINT32              IohcBus,
  IN       UINT32              Dev,
  IN       UINT32              Fn,
  IN       UINT32              Offset,
  IN       UINT32              Value32,
  IN       AMD_CONFIG_PARAMS   *StdHeader
  )
{
  UINT64    PciAddress;

  PciAddress = PCI_SEGMENT_LIB_ADDRESS (
                 IohcBus / MAX_PCI_BUS_NUMBER_PER_SEGMENT,
                 IohcBus % MAX_PCI_BUS_NUMBER_PER_SEGMENT,
                 Dev,
                 Fn,
                 Offset
                 );
  PciSegmentWrite32 (PciAddress, Value32);
}


VOID
FchPciCfgRMW32 (
  IN       UINT32              IohcBus,
  IN       UINT32              Dev,
  IN       UINT32              Fn,
  IN       UINT32              Offset,
  IN       UINT32              AndMask,
  IN       UINT32              OrValue,
  IN       AMD_CONFIG_PARAMS   *StdHeader
  )
{
  UINT32    Value32;

  Value32 = FchPciCfgRead32 (IohcBus, Dev, Fn, Offset, StdHeader);
  Value32 &= AndMask;
  Value32 |= OrValue;
  FchPciCfgWrite32 (IohcBus, Dev, Fn, Offset, Value32, StdHeader);
}



