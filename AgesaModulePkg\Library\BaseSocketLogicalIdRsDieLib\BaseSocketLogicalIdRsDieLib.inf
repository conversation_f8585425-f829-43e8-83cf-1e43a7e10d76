#;*****************************************************************************
#;
#; Copyright (C) 2008-2024 Advanced Micro Devices, Inc. All rights reserved.
#;
#;******************************************************************************
#For EDKII use Only
[Defines]
  INF_VERSION                    = 0x00010005
  BASE_NAME                      = BaseSocketLogicalIdRsDieLib
  FILE_GUID                      = 26F1DB37-C146-439B-BA23-7AD19A8EAEEC
  MODULE_TYPE                    = BASE
  VERSION_STRING                 = 1.0
  LIBRARY_CLASS                  = BaseSocketLogicalIdLib

[Sources]
  BaseSocketLogicalIdRsDieLib.c

[Packages]
  MdePkg/MdePkg.dec
  AgesaModulePkg/AgesaCommonModulePkg.dec
  AgesaModulePkg/AgesaModuleDfPkg.dec

[LibraryClasses]
  BaseLib
  SmnAccessLib
  BaseFabricTopologyLib
  BaseSocLogicalIdXlatLib

[Guids]

[Protocols]

[Ppis]

[Pcd]

[Depex]



