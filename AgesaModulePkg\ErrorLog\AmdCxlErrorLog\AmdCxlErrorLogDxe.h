/** @file

Cxl Error Log DXE Driver.

**/
/******************************************************************************
 * Copyright (C) 2024 Advanced Micro Devices, Inc. All rights reserved.
 *
 ***************************************************************************/
#ifndef __AMD_CXL_ERROR_LOG_DXE_H__
#define __AMD_CXL_ERROR_LOG_DXE_H__

#include <Uefi.h>
#include <AMD.h>
#include <Porting.h>
#include <Filecode.h>
#include <Library/BaseLib.h>
#include <Library/BaseMemoryLib.h>
#include <Library/DxeServicesTableLib.h>
#include <Library/HobLib.h>
#include <Library/PcdLib.h>
#include <Library/UefiLib.h>
#include <Library/UefiBootServicesTableLib.h>
#include <Library/UefiRuntimeServicesTableLib.h>
#include <Library/UefiDriverEntryPoint.h>
#include <Library/AmdErrorLogLib.h>
#include <Protocol/AmdErrorLogProtocol.h>
#include <Protocol/AmdErrorLogServiceProtocol.h>
#include <Protocol/AmdCxlErrorLogProtocol.h>

VOID
EFIAPI
CxlDumpErrorLog (
  IN ERROR_LOG_DATA_STRUCT                 *ErrorLogDataPtr
);
/**
 * @brief Add CXL Error Log.
 *
 * @param[in]   This               Pointer to the AMD_CXL_ERROR_LOG_PROTOCOL instance.
 * @param[out]  CxlErrorLog        Pointer to the CXL Error Log entry to add in the log.
 *
 * @retval EFI_SUCCESS             Successful add CXL error log.
 * @retval                         EFI_NOT_FOUND and other errors.
 */

EFI_STATUS
EFIAPI
CxlAddErrorLog (
   IN  AMD_CXL_ERROR_LOG_PROTOCOL   *This,
   IN  ERROR_LOG_PARAMS             *CxlErrorLog
);
/**
 * @brief Acquire CXL Error Log.
 *
 * @param[in]   This               Pointer to the AMD_CXL_ERROR_LOG_PROTOCOL instance.
 * @param[out]  ErrorLogDataPtr    Pointer to CXL Error Log structure.
 *
 * @retval EFI_SUCCESS             Successful acquire CXL error log.
 * @retval                         EFI_NOT_FOUND and other errors.
 */

EFI_STATUS
EFIAPI
CxlAcquireErrorLog (
   IN  AMD_CXL_ERROR_LOG_PROTOCOL   *This,
   IN OUT ERROR_LOG_DATA_STRUCT     **CxlErrorLogDataPtr
);
/**
 * @brief Display CXL Error Log.
 *
 * @param[in]   This               Pointer to the AMD_CXL_ERROR_LOG_PROTOCOL instance.
 * @param[out]  None
 *
 * @retval EFI_SUCCESS             Successful display CXL error log.
 * @retval                         EFI_NOT_FOUND and other errors.
 */

EFI_STATUS
EFIAPI
CxlDisplayErrorLog (
  IN     AMD_CXL_ERROR_LOG_PROTOCOL   *This
  );
/**
 * @brief Display CXL Error Messages.
 *
 * @param[in]   This               Pointer to the AMD_CXL_ERROR_LOG_PROTOCOL instance.
 * @param[out]  None
 *
 * @retval EFI_SUCCESS             Successful display CXL error messages.
 * @retval                         EFI_NOT_FOUND and other errors.
 */

EFI_STATUS
EFIAPI
CxlDisplayErrorLogMessages (
  IN     AMD_CXL_ERROR_LOG_PROTOCOL   *This
  );
/*********************************************************************************
 * Name: CxlErrorLogDxeInit
 *
 * Description
 *   Entry point of the CXL Error Log DXE driver
 *   Perform the configuration init, resource reservation, early post init
 *   and install all the supported protocol
 *
 * Input
 *   ImageHandle : EFI Image Handle for the DXE driver
 *   SystemTable : pointer to the EFI system table
 *
 * Output
 *   None
 *
 *********************************************************************************/
EFI_STATUS
EFIAPI
CxlErrorLogDxeInit (
  IN       EFI_HANDLE         ImageHandle,
  IN       EFI_SYSTEM_TABLE   *SystemTable
  );

#endif

