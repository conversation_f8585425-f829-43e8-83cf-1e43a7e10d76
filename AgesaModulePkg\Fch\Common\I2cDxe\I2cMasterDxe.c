/*****************************************************************************
 *
 * Copyright (C) 2008-2024 Advanced Micro Devices, Inc. All rights reserved.
 *
 *******************************************************************************
 */

/*----------------------------------------------------------------------------------------
 *                             M O D U L E S    U S E D
 *----------------------------------------------------------------------------------------
 */

#include <DwI2cRegs.h>
#include <FabricInfoRs.h>
#include <Library/DebugLib.h>
#include <Library/IdsLib.h>

#include <Protocol/I2cMaster.h>
#include "I2cMasterDxe.h"
#include <Library/FchI2cLib.h>

#include <Library/FchSocLib.h>
#include <FabricRegistersRs.h>
#include <Library/BaseMemoryLib.h>
#include <Library/BaseFabricTopologyLib.h>
#include <Protocol/DevicePath.h>
#include <Protocol/FabricTopologyServices2.h>

/*----------------------------------------------------------------------------------------
 *                   D E F I N I T I O N S    A N D    M A C R O S
 *----------------------------------------------------------------------------------------
 */
#define FILECODE FCH_COMMON_I2CDXE_I2CMASTERDXE_FILECODE

/*----------------------------------------------------------------------------------------
 *                  T Y P E D E F S     A N D     S T R U C T U R E S
 *----------------------------------------------------------------------------------------
 */


/*----------------------------------------------------------------------------------------
 *           P R O T O T Y P E S     O F     L O C A L     F U N C T I O N S
 *----------------------------------------------------------------------------------------
 */


/*----------------------------------------------------------------------------------------
 *                          E X P O R T E D    F U N C T I O N S
 *----------------------------------------------------------------------------------------
 */
extern EFI_BOOT_SERVICES *gBS;

I2C_CONTROLLER_DEVICE_PATH DevicePathI2cDefault = {
  { { HARDWARE_DEVICE_PATH, HW_CONTROLLER_DP, {sizeof(CONTROLLER_DEVICE_PATH)}}, 0 },
  { END_DEVICE_PATH_TYPE, END_ENTIRE_DEVICE_PATH_SUBTYPE, {sizeof (EFI_DEVICE_PATH_PROTOCOL), 0}}
};

EFI_GUID AmdI2cMasterID = \
  {0x7BB75906, 0x2675, 0x4397, {0xA5, 0xAB, 0xFF, 0x53, 0x9D, 0x9C, 0x3B, 0xD5 }};
UINTN    mSkt1FchBus = 0;

/**
   *  @brief This function writes a value to SMNIO
   *  @param Addr - address to write
   *  @param Value
   *  @retval VOIDs
   */
VOID
EFIAPI
SmnioWrite32 (
  IN  UINTN   Addr,
  IN  UINT32  Value
  )
{
  UINT32 Val = Value;

  FchSmnWrite ((UINT32)mSkt1FchBus,  (UINT32)Addr, &Val, NULL);
}

/**
   *  @brief This function reads a value from SMNIO
   *  @param addr - address to read
   *  @retval UINT32 : value of the address
   */
UINT32
EFIAPI
SmnioRead32 (
  IN      UINTN Addr
  )
{
  UINT32 Value;

  FchSmnRead ((UINT32)mSkt1FchBus, (UINT32)Addr, &Value, NULL);
  return Value;
}

/**
   *  @brief This function writes a value to MMIO
   *  @param Addr - address to write
   *  @param Value
   *  @retval VOIDs
   */
VOID
EFIAPI
I2cMmioWrite32 (
  IN  UINTN   Addr,
  IN  UINT32  Value
  )
{
  MmioWrite32(Addr, Value);
}

/**
   *  @brief Entry point of the AMD I2cMaster DXE driver
   *  @details Perform the configuration init, resource reservation, early post init
   *  and install all the supported protocol
   *  @param[in] ImageHandle - EFI Image Handle for the DXE driver
   *  @param[in] SystemTable - pointer to the EFI system table
   *  @returns EFI_STATUS
   *  @retval EFI_SUCCESS : Module initialized successfully
   *          EFI_ERROR   : Initialization failed (see error for more details)
   */
EFI_STATUS
EFIAPI
AmdI2cMasterDxeInit (
  IN  EFI_HANDLE        ImageHandle,
  IN  EFI_SYSTEM_TABLE  *SystemTable
  )
{
  EFI_STATUS                              Status;
  UINT32                                  Index;
  UINT32                                  SocketNum;
  UINT8                                   IsOnMainSocket;
  UINT32                                  ControllerNum;
  EFI_HANDLE                              *I2cHandles;
  UINTN                                   NumOfSockets;
  UINT8                                   I2cControllerCount;
  UINT8                                   NumOfSupportedSockets;
  FCH_EFI_DXE_I2C_MASTER_PROTOCOL         *FchI2cController;
  EFI_I2C_CONTROLLER_CAPABILITIES         *I2cControllerCapabilities;
  I2C_CONTROLLER_DEVICE_PATH              *DevicePathI2c = NULL;
  AMD_FABRIC_TOPOLOGY_SERVICES2_PROTOCOL  *FabricTopology;
  ROOT_BRIDGE_LOCATION                    FchRb;
  I2C_BUS_CLEAR_DATA                      I2cBusClearData = {0};
  I2C_BUS_CLEAR_DATA                      *I2cBusClearDataPtr = NULL;

  IDS_HDT_CONSOLE (FCH_TRACE, "[Debug] AmdI2cMasterDxeInit Entry point.\n");
  Status = gBS->LocateProtocol (
                  &gAmdFabricTopologyServices2ProtocolGuid,
                  NULL,
                  (VOID**)&FabricTopology
                  );
  if (!EFI_ERROR(Status)){
    Status = FabricTopology->GetSystemInfo (
                               FabricTopology,
                               &NumOfSockets,
                               NULL,
                               NULL,
                               &FchRb,
                               NULL
                               );
    if ( Status != EFI_SUCCESS ) {
      IDS_HDT_CONSOLE (FCH_TRACE, "[Warning] GetSystemInfo failed (Status: 0x%x).\n", Status);
      ASSERT_EFI_ERROR(Status);
      return Status;
    }

    if (NumOfSockets >= 2) {
      Status = FabricTopology->GetRootBridgeInfo (
                                 FabricTopology,
                                 1,//socket
                                 FchRb.Die,
                                 FchRb.Index,
                                 NULL,
                                 &mSkt1FchBus,
                                 NULL,
                                 NULL,
                                 NULL,
                                 NULL
                                 );
      if ( Status != EFI_SUCCESS ) {
        IDS_HDT_CONSOLE (FCH_TRACE, "[Warning] GetRootBridgeInfo failed (Status: 0x%x).\n", Status);
        ASSERT_EFI_ERROR(Status);
        return Status;
      }
    }
  } else {
    //backward compatible if cannot get
    IDS_HDT_CONSOLE (FCH_TRACE, "faield locate Farbric,use libary\n");
    NumOfSockets = FabricTopologyGetNumberOfProcessorsPresent ();
    if (NumOfSockets >= 2) {
      FabricTopologyGetSystemComponentRootBridgeLocation(PrimaryFch, &FchRb);
      mSkt1FchBus = FabricTopologyGetHostBridgeBusBase(1,FchRb.Die, FchRb.Index );
    }
  }
  IDS_HDT_CONSOLE (FCH_TRACE, "Primary FCH index =0x%x Skt1FchBus=0x%x\n",  FchRb.Index, mSkt1FchBus);

  I2cControllerCount = FchSocI2cGetControllerCount ();
  IDS_HDT_CONSOLE (FCH_TRACE, "[Info] I2cMasterDxe.c NumberOfI2cControllerPerSocket: %u.\n", I2cControllerCount);

  NumOfSupportedSockets = FchSocI2cGetNumOfSupportedSockets ();
  IDS_HDT_CONSOLE (FCH_TRACE, "[Info] I2cMasterDxe.c NumOfSockets: %u, MaxNumOfSupportedSockets: %u.\n", NumOfSockets, NumOfSupportedSockets);

  if (NumOfSockets > NumOfSupportedSockets) {
    IDS_HDT_CONSOLE (FCH_TRACE, "[Warning] I2cMasterDxe.c NumOfSockets (%u) is greater than number of supported sockets (%u).\n", NumOfSockets, NumOfSupportedSockets);
    NumOfSockets = NumOfSupportedSockets;
  }

  //
  // Create the Protocols
  //
  Status = gBS->AllocatePool (
                  EfiBootServicesData,
                  NumOfSockets * I2cControllerCount * sizeof (FCH_EFI_DXE_I2C_MASTER_PROTOCOL),
                  (VOID **) &FchI2cController
                  );
  if (EFI_ERROR (Status)) {
    IDS_HDT_CONSOLE (FCH_TRACE, "[Warning] AllocatePool for FchI2cController failed (Status: 0x%x).\n", Status);
    ASSERT_EFI_ERROR (Status);
    return Status;
  } else {
    //clear instances content
    ZeroMem (FchI2cController, NumOfSockets * I2cControllerCount * sizeof (FCH_EFI_DXE_I2C_MASTER_PROTOCOL));
  }

  Status = gBS->AllocatePool (
                  EfiBootServicesData,
                  sizeof (EFI_I2C_CONTROLLER_CAPABILITIES),
                  (VOID **) &I2cControllerCapabilities
                  );
  if (EFI_ERROR (Status)) {
    IDS_HDT_CONSOLE (FCH_TRACE, "[Warning] AllocatePool for I2cControllerCapabilities failed (Status: 0x%x).\n", Status);
    // free up previous memory alloc
    gBS->FreePool(FchI2cController);
    ASSERT_EFI_ERROR (Status);
    return Status;
  } else {
    //clear instances content
    ZeroMem (I2cControllerCapabilities, sizeof (EFI_I2C_CONTROLLER_CAPABILITIES));
    I2cControllerCapabilities->StructureSizeInBytes = sizeof (EFI_I2C_CONTROLLER_CAPABILITIES);
    I2cControllerCapabilities->MaximumReceiveBytes  = 0xFFFFFFFF;
    I2cControllerCapabilities->MaximumTotalBytes    = 0xFFFFFFFF;
    I2cControllerCapabilities->MaximumTransmitBytes = 0xFFFFFFFF;
  }

  Status = gBS->AllocatePool (
                  EfiBootServicesData,
                  NumOfSockets * I2cControllerCount * sizeof (EFI_HANDLE),
                  (VOID **) &I2cHandles
                  );
  if (EFI_ERROR (Status)) {
    IDS_HDT_CONSOLE (FCH_TRACE, "[Warning] AllocatePool for I2cHandles failed (Status: 0x%x).\n", Status);
    // free up previous memory alloc
    gBS->FreePool(FchI2cController);
    gBS->FreePool(I2cControllerCapabilities);

    ASSERT_EFI_ERROR (Status);
    return Status;
  } else {
    ZeroMem (I2cHandles, NumOfSockets * I2cControllerCount * sizeof (EFI_HANDLE));
  }

  // Set SDA HOLD TIME & Bus Frequency
  for (Index = 0; Index < I2cControllerCount; Index++) {
    FchI2cController[Index].I2cSdaHoldTime = FchSocI2cSetSdaHoldTime (Index);
    if (FchI2cController[Index].I2cSdaHoldTime == 0xFFFFFFFF) {
      IDS_HDT_CONSOLE (FCH_TRACE, "[Error] I2cMasterPei.c Setting SDA HOLD TIME failed - Wrong parameter\n");
    }

    FchI2cController[Index].I2cBusFrequency = FchSocI2cSetBusFrequency (Index);
    if (FchI2cController[Index].I2cBusFrequency == 0xFFFFFFFF) {
      IDS_HDT_CONSOLE (FCH_TRACE, "[Error] I2cMasterPei.c Setting BUS FREQUENCY failed - Wrong parameter\n");
    }
  }

  if (NumOfSockets > 1) {
    for (Index = 0; Index < I2cControllerCount; Index++) {
      // Copy SDA Hold Time, Bus Frequency info to the second Socket
      FchI2cController[Index + I2cControllerCount].I2cSdaHoldTime = FchSocI2cSetSdaHoldTime (Index);
      FchI2cController[Index + I2cControllerCount].I2cBusFrequency = FchSocI2cSetBusFrequency (Index);
    }
  }

  for ( SocketNum = 0; SocketNum < NumOfSockets; SocketNum++ ) {
    for ( ControllerNum = 0; ControllerNum < I2cControllerCount; ControllerNum++) {

      Index = SocketNum * I2cControllerCount + ControllerNum;

      FchI2cController[Index].I2cController.SetBusFrequency           = SetBusFrequency;
      FchI2cController[Index].I2cController.Reset                     = Reset;
      FchI2cController[Index].I2cController.StartRequest              = StartRequest;
      FchI2cController[Index].I2cController.I2cControllerCapabilities = I2cControllerCapabilities;
      FchI2cController[Index].I2cBaseAddress                          = FchSocI2cGetBaseAddress (Index);
      FchI2cController[Index].I2cControllerInitialized = 0x00;
      FchI2cController[Index].ControllerNum = Index;

      // Check if the I2C controller is enabled by PCD
      if (FchSocI2cIsControllerEnabled (Index)) {
        IsOnMainSocket = ((FchI2cController[Index].I2cBaseAddress & 0xFFFF0000) == 0xFEDC0000) ? 0x01 : 0x00;
        //check BusClear Feature support
        //  Check it per socket.  Should have the status for both socket 0 and socket 1.
        if ( TRUE == FchSocI2cIsSupportBusClearFeature(Index % I2cControllerCount) ) {
          IDS_HDT_CONSOLE (FCH_TRACE, "I2c Index[%d] Bus cls support\n", Index);
          I2cBusClearData.SCLStuckLowTime = FchSocI2cSetSCLStuckTime (Index % I2cControllerCount);
          I2cBusClearData.SDAStuckLowTime = FchSocI2cSetSDAStuckTime (Index % I2cControllerCount);
          I2cBusClearDataPtr = &I2cBusClearData;
        } else {
          I2cBusClearDataPtr = NULL;
        }
        // Check if it is Socket 0 or 1
        if (IsOnMainSocket == 0x01) {
          // get RxFifoDepth and TxFifoDepth
          FchI2cController[Index].RxFifoDepth = I2cGetRxTxFifoDepth (FchI2cController[Index].I2cBaseAddress, 0, MmioRead32);
          FchI2cController[Index].TxFifoDepth = I2cGetRxTxFifoDepth (FchI2cController[Index].I2cBaseAddress, 1, MmioRead32);
          // I2C init
          Status = I2cInit (
                     FchI2cController[Index].I2cBaseAddress,
                     FchI2cController[Index].I2cBusFrequency,
                     FchI2cController[Index].I2cSdaHoldTime,
                     MmioRead32,
                     I2cMmioWrite32,
                     I2cBusClearDataPtr
                     );
        } else {
          // get RxFifoDepth and TxFifoDepth
          FchI2cController[Index].RxFifoDepth = I2cGetRxTxFifoDepth (FchI2cController[Index].I2cBaseAddress, 0, SmnioRead32);
          FchI2cController[Index].TxFifoDepth = I2cGetRxTxFifoDepth (FchI2cController[Index].I2cBaseAddress, 1, SmnioRead32);
          // I2C init
          Status = I2cInit (
                     FchI2cController[Index].I2cBaseAddress,
                     FchI2cController[Index].I2cBusFrequency,
                     FchI2cController[Index].I2cSdaHoldTime,
                     SmnioRead32,
                     SmnioWrite32,
                     I2cBusClearDataPtr
                     );
        }

        if (Status == EFI_SUCCESS) {
          IDS_HDT_CONSOLE (FCH_TRACE, "[Info] I2C controller (SocketNum: %u, ControllerNum: %u, base 0x%x) initialized (Status: 0x%x).\n", \
                               SocketNum, ControllerNum, FchI2cController[Index].I2cBaseAddress, Status);
          FchI2cController[Index].I2cControllerInitialized = 0x01;
        } else {
          IDS_HDT_CONSOLE (FCH_TRACE, "[Warning] I2C controller (SocketNum: %u, ControllerNum: %u, base 0x%x) initialization failed (Status: 0x%x).\n", \
                                SocketNum, ControllerNum, FchI2cController[Index].I2cBaseAddress, Status);
        }
      } else {
        IDS_HDT_CONSOLE (
          FCH_TRACE,
          "[Warning] I2C controller (SocketNum: %u, ControllerNum: %u, base 0x%x) initialization skip.\n",
          SocketNum,
          ControllerNum,
          FchI2cController[Index].I2cBaseAddress
          );
      }

      DevicePathI2c = NULL;
      Status = gBS->AllocatePool (EfiBootServicesData, sizeof (I2C_CONTROLLER_DEVICE_PATH), (VOID **) &DevicePathI2c);
      IDS_HDT_CONSOLE (FCH_TRACE, "[Info] DevicePathI2c = %p\n", DevicePathI2c);
      if (EFI_ERROR (Status)) {
        return Status;
      } else {
        gBS->CopyMem (DevicePathI2c, &DevicePathI2cDefault, sizeof (I2C_CONTROLLER_DEVICE_PATH));
        DevicePathI2c->ControllerDp.ControllerNumber = Index;
      }

      // Install Protocols
      I2cHandles[Index] = NULL;
      Status = gBS->InstallMultipleProtocolInterfaces (
                      &I2cHandles[Index],
                      &gEfiI2cMasterProtocolGuid,
                      &FchI2cController[Index],
                      &gEfiDevicePathProtocolGuid,
                      DevicePathI2c,
                      NULL
                      );

      if (Status != EFI_SUCCESS) {
        IDS_HDT_CONSOLE (FCH_TRACE, "[Warning] I2C controller (index %u) InstallMultipleProtocolInterface failed (Status: 0x%x).\n", Index, Status);
      }
    }
  }

  return (Status);
}

/**
   *  @brief Set Bus Frequency
   *  @param[in] *This - EFI_I2C_MASTER_PROTOCOL table
   *  @param[in] *BusClockHertz - pointer to the BUS Clock Hertz
   *  @returns EFI_STATUS
   *  @retval EFI_SUCCESS : Set Bus Frequency successfully
   *          EFI_ERROR   : Failed (see error for more details)
   */
EFI_STATUS
EFIAPI
SetBusFrequency (
  IN  CONST EFI_I2C_MASTER_PROTOCOL   *This,
  IN  OUT UINTN                       *BusClockHertz
  )
{
  EFI_STATUS    Status = EFI_SUCCESS;
  FCH_EFI_DXE_I2C_MASTER_PROTOCOL *I2cPrivate;
  UINT32  settings;
  UINT32  Base;
  UINT8   IsOnMainSocket;
  UINT32  hcnt;
  UINT32  lcnt;

  I2cRegisterRead32 I2cRegRead32;
  I2cRegisterWrite32 I2cRegWrite32;

  settings = 0;
  I2cPrivate = (FCH_EFI_DXE_I2C_MASTER_PROTOCOL*)This;

  // Check if the controller is initialied.
  if (I2cPrivate->I2cControllerInitialized == 0x00) {
    return EFI_DEVICE_ERROR;
  }

  // Get the base address for the controller
  Base = I2cPrivate->I2cBaseAddress;

  // Get this controller is in Socket 0 or 1
  IsOnMainSocket = ((Base & 0xFFFF0000) == 0xFEDC0000) ? 0x01 : 0x00;

  if (IsOnMainSocket == 0x01) {
    I2cRegRead32 = (I2cRegisterRead32) MmioRead32;
    I2cRegWrite32 = I2cMmioWrite32;
  } else {
    I2cRegRead32 = (I2cRegisterRead32) SmnioRead32;
    I2cRegWrite32 = (I2cRegisterWrite32) SmnioWrite32;
  }

  // Disable the interface
  I2cRegWrite32 (Base + DW_IC_ENABLE, 0);
  if (I2cDwWaitI2cEnable (Base, CHECK_IC_EN_HIGH, I2cRegRead32)) {
    return EFI_NOT_READY;
  }

  settings |= DW_I2C_CON_MASTER_MODE | DW_I2C_CON_IC_SLAVE_DISABLE;

  if (HS_SPEED <= *BusClockHertz) {
    settings |= DW_I2C_CON_SPEED_HS;
    //*BusClockHertz = FS_SPEED;        //Return actually clock setting
    *BusClockHertz = HS_SPEED;
  } else if (FS_SPEED <= *BusClockHertz) {
    settings |= DW_I2C_CON_SPEED_FS;
    *BusClockHertz = FS_SPEED;        //Return actually clock setting
  } else {
    settings |= DW_I2C_CON_SPEED_SS;
    *BusClockHertz = SS_SPEED;        //Return actually clock setting
  }

  settings |= DW_I2C_CON_IC_RESTART_EN;

  I2cRegWrite32 (Base + DW_IC_CON, settings);

  // Setup spike suppression for SS and FS at 50ns
  I2cRegWrite32 (Base + DW_IC_FS_SPKLEN, configI2C_FS_GLITCH_FILTER);

  // Setup spike suppression for HS at 10ns
  I2cRegWrite32 (Base + DW_IC_FS_SPKLEN, configI2C_FS_GLITCH_FILTER);

  // Standard-mode 100Khz
  hcnt = AMD_SS_SCL_HCNT;
  lcnt = AMD_SS_SCL_LCNT;
  I2cRegWrite32 (Base + DW_IC_SS_SCL_HCNT, hcnt); // std speed high, 4us
  I2cRegWrite32 (Base + DW_IC_SS_SCL_LCNT, lcnt); // std speed low, 4.7us

  IDS_HDT_CONSOLE (FCH_TRACE, "[Debug] Standard-mode HCNT:LCNT = %d:%d\n", hcnt, lcnt);

  // Fast-mode 400Khz
  hcnt = AMD_FS_SCL_HCNT;
  lcnt = AMD_FS_SCL_LCNT;
  I2cRegWrite32 (Base + DW_IC_FS_SCL_HCNT, hcnt);
  I2cRegWrite32 (Base + DW_IC_FS_SCL_LCNT, lcnt);
  I2cRegWrite32 (Base + DW_IC_HS_SCL_HCNT, hcnt);
  I2cRegWrite32 (Base + DW_IC_HS_SCL_LCNT, lcnt);

  IDS_HDT_CONSOLE (FCH_TRACE, "[Debug] Fast-mode HCNT:LCNT = %d:%d\n", hcnt, lcnt);

  return Status;
}

/**
   *  @brief Reset
   *  @param[in] *This - EFI_I2C_MASTER_PROTOCOL table
   *  @returns EFI_STATUS
   *  @retval EFI_SUCCESS :
   */
EFI_STATUS
EFIAPI
Reset (
  IN  CONST EFI_I2C_MASTER_PROTOCOL   *This
  )
{
  return EFI_SUCCESS; // to avoid blocking the binding process (Start) for I2C host protocol in I2cHost.c
}

/**
   *  @brief Start request
   *  @param[in] *This - EFI_I2C_MASTER_PROTOCOL table
   *  @param[in] SlaveAddress -
   *  @param[in] *RequestPacket - EFI_I2C_REQUEST_PACKET table
   *  @param[in] Event -
   *  @param[out] *I2cStatus -
   *  @returns EFI_STATUS
   *  @retval EFI_SUCCESS :
   */
EFI_STATUS
EFIAPI
StartRequest (
  IN  CONST EFI_I2C_MASTER_PROTOCOL   *This,
  IN  UINTN                           SlaveAddress,
  IN  EFI_I2C_REQUEST_PACKET          *RequestPacket,
  IN  EFI_EVENT                       Event      OPTIONAL,
  OUT EFI_STATUS                      *I2cStatus OPTIONAL
  )
{
  EFI_STATUS Status;
  FCH_EFI_DXE_I2C_MASTER_PROTOCOL *I2cPrivate;
  EFI_I2C_OPERATION *Operation;
  UINT32  Base;
  UINT8   IsOnMainSocket;
  UINT32  Index;
  UINTN   OperationCount;

  I2cRegisterRead32 I2cRegRead32;
  I2cRegisterWrite32 I2cRegWrite32;

  I2cPrivate = (FCH_EFI_DXE_I2C_MASTER_PROTOCOL*)This;

  // Check if the controller is initialied.
  if (I2cPrivate->I2cControllerInitialized == 0x00) {
    return EFI_DEVICE_ERROR;
  }

  // Get the base address of the controller
  Base = I2cPrivate->I2cBaseAddress;
  // Check if the controller base address is main Socket
  IsOnMainSocket = ((Base & 0xFFFF0000) == 0xFEDC0000) ? 0x01 : 0x00;

  if (IsOnMainSocket == 0x01) {
    I2cRegRead32 = (I2cRegisterRead32) MmioRead32;
    I2cRegWrite32 = I2cMmioWrite32;
  } else {
    I2cRegRead32 = (I2cRegisterRead32) SmnioRead32;
    I2cRegWrite32 = (I2cRegisterWrite32) SmnioWrite32;
  }

  //
  // I2cAccess for Read/Write data
  //
  Operation = RequestPacket->Operation;
  OperationCount = RequestPacket->OperationCount;

  if (OperationCount == 0x00) {
    Status = EFI_UNSUPPORTED;
    goto Exit;
  }

  for (Index=0; Index < OperationCount; Index++) {
    if (Operation[Index].LengthInBytes == 0x00) {
      // We do not support quick read/write
      Status = EFI_UNSUPPORTED;
      goto Exit;
    } else if (Operation[Index].Flags & (I2C_FLAG_SMBUS_PEC | I2C_FLAG_SMBUS_PROCESS_CALL)) {
      // No PEC, ProcessCall and BlkProcessCall either
      Status = EFI_UNSUPPORTED;
      goto Exit;
    }
  }

  //Set target device slave address
  if (I2cSetTarget ((UINT32)SlaveAddress, Base, I2cRegRead32, I2cRegWrite32) != EFI_SUCCESS) {
    Status = EFI_DEVICE_ERROR;
    goto Exit;
  }

  if (I2cDwWaitBusNotBusy (Base, I2cRegRead32)) {
    Status = EFI_NOT_READY;
    goto Exit;
  }

  I2cRegWrite32 (Base + DW_IC_INTR_MASK, 0);
  (VOID)I2cRegRead32 (Base + DW_IC_CLR_INTR);

  IDS_HDT_CONSOLE (FCH_TRACE, "[Debug] Enable I2cInterface\n");
  // Enable the interface
  I2cRegWrite32 (Base + DW_IC_ENABLE, DW_I2C_ENABLE);
  if (I2cDwWaitI2cEnable (Base, CHECK_IC_EN_LOW, I2cRegRead32)) {
    Status = EFI_NOT_READY;
    goto Exit;
  }

  for (Index=0; Index < OperationCount; Index++) {
    if (Operation[Index].Flags == 0x00) {
      // Write operation
      //  - if OperationCount == 2, it is 2 step write-and-read. We do not STOP on write operation in this case.
      IDS_HDT_CONSOLE (FCH_TRACE, "[Debug] I2cMasterDxe.c StartRequest Operation (Write) Index: %u.\n", Index);
      Status = I2cPrivateWrite (Base, Operation[Index].Buffer, Operation[Index].LengthInBytes, I2cPrivate->TxFifoDepth, OperationCount, I2cRegRead32, I2cRegWrite32);
    } else {
      // Read operation
      IDS_HDT_CONSOLE (FCH_TRACE, "[Debug] I2cMasterDxe.c StartRequest Operation (Read) Index: %u.\n", Index);
      Status = I2cPrivateRead (Base, Operation[Index].Buffer, Operation[Index].LengthInBytes, I2cPrivate->RxFifoDepth, I2cRegRead32, I2cRegWrite32);
    }

    if (Status != EFI_SUCCESS) {
      goto Exit;
    }
  }

  // Disable the interface
  IDS_HDT_CONSOLE (FCH_TRACE, "[Debug] Disable I2cInterface\n");
  I2cRegWrite32 (Base + DW_IC_ENABLE, 0);
  I2cDwWaitI2cEnable (Base, CHECK_IC_EN_HIGH, I2cRegRead32);  //Wait controller status change

Exit:
  // If I2cStatus parameter is configured, the need to return the current Status.
  if (I2cStatus != NULL) {
    *I2cStatus = Status;
  }
  if (Event != NULL) {
    gBS->SignalEvent(Event); // to avoid infinte loop condition when executing I2cHostQueueRequest() in I2cHost.c
  }

  return Status;
}

