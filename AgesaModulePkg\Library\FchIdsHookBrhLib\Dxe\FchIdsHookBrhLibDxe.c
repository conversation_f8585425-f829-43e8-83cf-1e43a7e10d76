/*****************************************************************************
 *
 * Copyright (C) 2008-2024 Advanced Micro Devices, Inc. All rights reserved.
 *
 ******************************************************************************
 */
#include <Library/AmdBaseLib.h>
#include <Library/IdsLib.h>
#include <Library/AmdIdsHookLib.h>
#include <IdsHookId.h>
#include <IdsNvIdBRH.h>
#include <IdsNvDefBRH.h>
#include <Filecode.h>
#include <FchPlatform.h>

#define FILECODE UNASSIGNED_FILE_FILECODE

typedef struct {
  CONST UINT16  NvId;
  CONST UINT32  iSata;
  CONST UINT32  eSata;
  CONST UINT32  Auto;
} FCH_ESATA_NV_DATA;

FCH_ESATA_NV_DATA  Sata_eSATA_NV_ID[64] = {
  {IDSNVID_DBG_FCH_SATAE_SATA_PORT0,          IDSOPT_DBG_FCH_SATAE_SATA_PORT0_ISATA,             IDSOPT_DBG_FCH_SATAE_SATA_PORT0_ESATA,            IDSOPT_DBG_FCH_SATAE_SATA_PORT0_AUTO         },
  {IDSNVID_DBG_FCH_SATAE_SATA_PORT1,          IDSOPT_DBG_FCH_SATAE_SATA_PORT1_ISATA,             IDSOPT_DBG_FCH_SATAE_SATA_PORT1_ESATA,            IDSOPT_DBG_FCH_SATAE_SATA_PORT1_AUTO         },
  {IDSNVID_DBG_FCH_SATAE_SATA_PORT2,          IDSOPT_DBG_FCH_SATAE_SATA_PORT2_ISATA,             IDSOPT_DBG_FCH_SATAE_SATA_PORT2_ESATA,            IDSOPT_DBG_FCH_SATAE_SATA_PORT2_AUTO         },
  {IDSNVID_DBG_FCH_SATAE_SATA_PORT3,          IDSOPT_DBG_FCH_SATAE_SATA_PORT3_ISATA,             IDSOPT_DBG_FCH_SATAE_SATA_PORT3_ESATA,            IDSOPT_DBG_FCH_SATAE_SATA_PORT3_AUTO         },
  {IDSNVID_DBG_FCH_SATAE_SATA_PORT4,          IDSOPT_DBG_FCH_SATAE_SATA_PORT4_ISATA,             IDSOPT_DBG_FCH_SATAE_SATA_PORT4_ESATA,            IDSOPT_DBG_FCH_SATAE_SATA_PORT4_AUTO         },
  {IDSNVID_DBG_FCH_SATAE_SATA_PORT5,          IDSOPT_DBG_FCH_SATAE_SATA_PORT5_ISATA,             IDSOPT_DBG_FCH_SATAE_SATA_PORT5_ESATA,            IDSOPT_DBG_FCH_SATAE_SATA_PORT5_AUTO         },
  {IDSNVID_DBG_FCH_SATAE_SATA_PORT6,          IDSOPT_DBG_FCH_SATAE_SATA_PORT6_ISATA,             IDSOPT_DBG_FCH_SATAE_SATA_PORT6_ESATA,            IDSOPT_DBG_FCH_SATAE_SATA_PORT6_AUTO         },
  {IDSNVID_DBG_FCH_SATAE_SATA_PORT7,          IDSOPT_DBG_FCH_SATAE_SATA_PORT7_ISATA,             IDSOPT_DBG_FCH_SATAE_SATA_PORT7_ESATA,            IDSOPT_DBG_FCH_SATAE_SATA_PORT7_AUTO         },

  {IDSNVID_DBG_FCH_SATA_MCM_DIE1_ESATA_PORT0, IDSOPT_DBG_FCH_SATA_MCM_DIE1_ESATA_PORT0_DISABLED, IDSOPT_DBG_FCH_SATA_MCM_DIE1_ESATA_PORT0_ENABLED, IDSOPT_DBG_FCH_SATA_MCM_DIE1_ESATA_PORT0_AUTO},
  {IDSNVID_DBG_FCH_SATA_MCM_DIE1_ESATA_PORT1, IDSOPT_DBG_FCH_SATA_MCM_DIE1_ESATA_PORT1_DISABLED, IDSOPT_DBG_FCH_SATA_MCM_DIE1_ESATA_PORT1_ENABLED, IDSOPT_DBG_FCH_SATA_MCM_DIE1_ESATA_PORT1_AUTO},
  {IDSNVID_DBG_FCH_SATA_MCM_DIE1_ESATA_PORT2, IDSOPT_DBG_FCH_SATA_MCM_DIE1_ESATA_PORT2_DISABLED, IDSOPT_DBG_FCH_SATA_MCM_DIE1_ESATA_PORT2_ENABLED, IDSOPT_DBG_FCH_SATA_MCM_DIE1_ESATA_PORT2_AUTO},
  {IDSNVID_DBG_FCH_SATA_MCM_DIE1_ESATA_PORT3, IDSOPT_DBG_FCH_SATA_MCM_DIE1_ESATA_PORT3_DISABLED, IDSOPT_DBG_FCH_SATA_MCM_DIE1_ESATA_PORT3_ENABLED, IDSOPT_DBG_FCH_SATA_MCM_DIE1_ESATA_PORT3_AUTO},
  {IDSNVID_DBG_FCH_SATA_MCM_DIE1_ESATA_PORT4, IDSOPT_DBG_FCH_SATA_MCM_DIE1_ESATA_PORT4_DISABLED, IDSOPT_DBG_FCH_SATA_MCM_DIE1_ESATA_PORT4_ENABLED, IDSOPT_DBG_FCH_SATA_MCM_DIE1_ESATA_PORT4_AUTO},
  {IDSNVID_DBG_FCH_SATA_MCM_DIE1_ESATA_PORT5, IDSOPT_DBG_FCH_SATA_MCM_DIE1_ESATA_PORT5_DISABLED, IDSOPT_DBG_FCH_SATA_MCM_DIE1_ESATA_PORT5_ENABLED, IDSOPT_DBG_FCH_SATA_MCM_DIE1_ESATA_PORT5_AUTO},
  {IDSNVID_DBG_FCH_SATA_MCM_DIE1_ESATA_PORT6, IDSOPT_DBG_FCH_SATA_MCM_DIE1_ESATA_PORT6_DISABLED, IDSOPT_DBG_FCH_SATA_MCM_DIE1_ESATA_PORT6_ENABLED, IDSOPT_DBG_FCH_SATA_MCM_DIE1_ESATA_PORT6_AUTO},
  {IDSNVID_DBG_FCH_SATA_MCM_DIE1_ESATA_PORT7, IDSOPT_DBG_FCH_SATA_MCM_DIE1_ESATA_PORT7_DISABLED, IDSOPT_DBG_FCH_SATA_MCM_DIE1_ESATA_PORT7_ENABLED, IDSOPT_DBG_FCH_SATA_MCM_DIE1_ESATA_PORT7_AUTO},

  {IDSNVID_DBG_FCH_SATA_MCM_DIE2_ESATA_PORT0, IDSOPT_DBG_FCH_SATA_MCM_DIE2_ESATA_PORT0_DISABLED, IDSOPT_DBG_FCH_SATA_MCM_DIE2_ESATA_PORT0_ENABLED, IDSOPT_DBG_FCH_SATA_MCM_DIE2_ESATA_PORT0_AUTO},
  {IDSNVID_DBG_FCH_SATA_MCM_DIE2_ESATA_PORT1, IDSOPT_DBG_FCH_SATA_MCM_DIE2_ESATA_PORT1_DISABLED, IDSOPT_DBG_FCH_SATA_MCM_DIE2_ESATA_PORT1_ENABLED, IDSOPT_DBG_FCH_SATA_MCM_DIE2_ESATA_PORT1_AUTO},
  {IDSNVID_DBG_FCH_SATA_MCM_DIE2_ESATA_PORT2, IDSOPT_DBG_FCH_SATA_MCM_DIE2_ESATA_PORT2_DISABLED, IDSOPT_DBG_FCH_SATA_MCM_DIE2_ESATA_PORT2_ENABLED, IDSOPT_DBG_FCH_SATA_MCM_DIE2_ESATA_PORT2_AUTO},
  {IDSNVID_DBG_FCH_SATA_MCM_DIE2_ESATA_PORT3, IDSOPT_DBG_FCH_SATA_MCM_DIE2_ESATA_PORT3_DISABLED, IDSOPT_DBG_FCH_SATA_MCM_DIE2_ESATA_PORT3_ENABLED, IDSOPT_DBG_FCH_SATA_MCM_DIE2_ESATA_PORT3_AUTO},
  {IDSNVID_DBG_FCH_SATA_MCM_DIE2_ESATA_PORT4, IDSOPT_DBG_FCH_SATA_MCM_DIE2_ESATA_PORT4_DISABLED, IDSOPT_DBG_FCH_SATA_MCM_DIE2_ESATA_PORT4_ENABLED, IDSOPT_DBG_FCH_SATA_MCM_DIE2_ESATA_PORT4_AUTO},
  {IDSNVID_DBG_FCH_SATA_MCM_DIE2_ESATA_PORT5, IDSOPT_DBG_FCH_SATA_MCM_DIE2_ESATA_PORT5_DISABLED, IDSOPT_DBG_FCH_SATA_MCM_DIE2_ESATA_PORT5_ENABLED, IDSOPT_DBG_FCH_SATA_MCM_DIE2_ESATA_PORT5_AUTO},
  {IDSNVID_DBG_FCH_SATA_MCM_DIE2_ESATA_PORT6, IDSOPT_DBG_FCH_SATA_MCM_DIE2_ESATA_PORT6_DISABLED, IDSOPT_DBG_FCH_SATA_MCM_DIE2_ESATA_PORT6_ENABLED, IDSOPT_DBG_FCH_SATA_MCM_DIE2_ESATA_PORT6_AUTO},
  {IDSNVID_DBG_FCH_SATA_MCM_DIE2_ESATA_PORT7, IDSOPT_DBG_FCH_SATA_MCM_DIE2_ESATA_PORT7_DISABLED, IDSOPT_DBG_FCH_SATA_MCM_DIE2_ESATA_PORT7_ENABLED, IDSOPT_DBG_FCH_SATA_MCM_DIE2_ESATA_PORT7_AUTO},

  {IDSNVID_DBG_FCH_SATA_MCM_DIE3_ESATA_PORT0, IDSOPT_DBG_FCH_SATA_MCM_DIE3_ESATA_PORT0_DISABLED, IDSOPT_DBG_FCH_SATA_MCM_DIE3_ESATA_PORT0_ENABLED, IDSOPT_DBG_FCH_SATA_MCM_DIE3_ESATA_PORT0_AUTO},
  {IDSNVID_DBG_FCH_SATA_MCM_DIE3_ESATA_PORT1, IDSOPT_DBG_FCH_SATA_MCM_DIE3_ESATA_PORT1_DISABLED, IDSOPT_DBG_FCH_SATA_MCM_DIE3_ESATA_PORT1_ENABLED, IDSOPT_DBG_FCH_SATA_MCM_DIE3_ESATA_PORT1_AUTO},
  {IDSNVID_DBG_FCH_SATA_MCM_DIE3_ESATA_PORT2, IDSOPT_DBG_FCH_SATA_MCM_DIE3_ESATA_PORT2_DISABLED, IDSOPT_DBG_FCH_SATA_MCM_DIE3_ESATA_PORT2_ENABLED, IDSOPT_DBG_FCH_SATA_MCM_DIE3_ESATA_PORT2_AUTO},
  {IDSNVID_DBG_FCH_SATA_MCM_DIE3_ESATA_PORT3, IDSOPT_DBG_FCH_SATA_MCM_DIE3_ESATA_PORT3_DISABLED, IDSOPT_DBG_FCH_SATA_MCM_DIE3_ESATA_PORT3_ENABLED, IDSOPT_DBG_FCH_SATA_MCM_DIE3_ESATA_PORT3_AUTO},
  {IDSNVID_DBG_FCH_SATA_MCM_DIE3_ESATA_PORT4, IDSOPT_DBG_FCH_SATA_MCM_DIE3_ESATA_PORT4_DISABLED, IDSOPT_DBG_FCH_SATA_MCM_DIE3_ESATA_PORT4_ENABLED, IDSOPT_DBG_FCH_SATA_MCM_DIE3_ESATA_PORT4_AUTO},
  {IDSNVID_DBG_FCH_SATA_MCM_DIE3_ESATA_PORT5, IDSOPT_DBG_FCH_SATA_MCM_DIE3_ESATA_PORT5_DISABLED, IDSOPT_DBG_FCH_SATA_MCM_DIE3_ESATA_PORT5_ENABLED, IDSOPT_DBG_FCH_SATA_MCM_DIE3_ESATA_PORT5_AUTO},
  {IDSNVID_DBG_FCH_SATA_MCM_DIE3_ESATA_PORT6, IDSOPT_DBG_FCH_SATA_MCM_DIE3_ESATA_PORT6_DISABLED, IDSOPT_DBG_FCH_SATA_MCM_DIE3_ESATA_PORT6_ENABLED, IDSOPT_DBG_FCH_SATA_MCM_DIE3_ESATA_PORT6_AUTO},
  {IDSNVID_DBG_FCH_SATA_MCM_DIE3_ESATA_PORT7, IDSOPT_DBG_FCH_SATA_MCM_DIE3_ESATA_PORT7_DISABLED, IDSOPT_DBG_FCH_SATA_MCM_DIE3_ESATA_PORT7_ENABLED, IDSOPT_DBG_FCH_SATA_MCM_DIE3_ESATA_PORT7_AUTO},

  {IDSNVID_DBG_FCH_SATA_MCM_DIE4_ESATA_PORT0, IDSOPT_DBG_FCH_SATA_MCM_DIE4_ESATA_PORT0_DISABLED, IDSOPT_DBG_FCH_SATA_MCM_DIE4_ESATA_PORT0_ENABLED, IDSOPT_DBG_FCH_SATA_MCM_DIE4_ESATA_PORT0_AUTO},
  {IDSNVID_DBG_FCH_SATA_MCM_DIE4_ESATA_PORT1, IDSOPT_DBG_FCH_SATA_MCM_DIE4_ESATA_PORT1_DISABLED, IDSOPT_DBG_FCH_SATA_MCM_DIE4_ESATA_PORT1_ENABLED, IDSOPT_DBG_FCH_SATA_MCM_DIE4_ESATA_PORT1_AUTO},
  {IDSNVID_DBG_FCH_SATA_MCM_DIE4_ESATA_PORT2, IDSOPT_DBG_FCH_SATA_MCM_DIE4_ESATA_PORT2_DISABLED, IDSOPT_DBG_FCH_SATA_MCM_DIE4_ESATA_PORT2_ENABLED, IDSOPT_DBG_FCH_SATA_MCM_DIE4_ESATA_PORT2_AUTO},
  {IDSNVID_DBG_FCH_SATA_MCM_DIE4_ESATA_PORT3, IDSOPT_DBG_FCH_SATA_MCM_DIE4_ESATA_PORT3_DISABLED, IDSOPT_DBG_FCH_SATA_MCM_DIE4_ESATA_PORT3_ENABLED, IDSOPT_DBG_FCH_SATA_MCM_DIE4_ESATA_PORT3_AUTO},
  {IDSNVID_DBG_FCH_SATA_MCM_DIE4_ESATA_PORT4, IDSOPT_DBG_FCH_SATA_MCM_DIE4_ESATA_PORT4_DISABLED, IDSOPT_DBG_FCH_SATA_MCM_DIE4_ESATA_PORT4_ENABLED, IDSOPT_DBG_FCH_SATA_MCM_DIE4_ESATA_PORT4_AUTO},
  {IDSNVID_DBG_FCH_SATA_MCM_DIE4_ESATA_PORT5, IDSOPT_DBG_FCH_SATA_MCM_DIE4_ESATA_PORT5_DISABLED, IDSOPT_DBG_FCH_SATA_MCM_DIE4_ESATA_PORT5_ENABLED, IDSOPT_DBG_FCH_SATA_MCM_DIE4_ESATA_PORT5_AUTO},
  {IDSNVID_DBG_FCH_SATA_MCM_DIE4_ESATA_PORT6, IDSOPT_DBG_FCH_SATA_MCM_DIE4_ESATA_PORT6_DISABLED, IDSOPT_DBG_FCH_SATA_MCM_DIE4_ESATA_PORT6_ENABLED, IDSOPT_DBG_FCH_SATA_MCM_DIE4_ESATA_PORT6_AUTO},
  {IDSNVID_DBG_FCH_SATA_MCM_DIE4_ESATA_PORT7, IDSOPT_DBG_FCH_SATA_MCM_DIE4_ESATA_PORT7_DISABLED, IDSOPT_DBG_FCH_SATA_MCM_DIE4_ESATA_PORT7_ENABLED, IDSOPT_DBG_FCH_SATA_MCM_DIE4_ESATA_PORT7_AUTO},

  {IDSNVID_DBG_FCH_SATA_MCM_DIE5_ESATA_PORT0, IDSOPT_DBG_FCH_SATA_MCM_DIE5_ESATA_PORT0_DISABLED, IDSOPT_DBG_FCH_SATA_MCM_DIE5_ESATA_PORT0_ENABLED, IDSOPT_DBG_FCH_SATA_MCM_DIE5_ESATA_PORT0_AUTO},
  {IDSNVID_DBG_FCH_SATA_MCM_DIE5_ESATA_PORT1, IDSOPT_DBG_FCH_SATA_MCM_DIE5_ESATA_PORT1_DISABLED, IDSOPT_DBG_FCH_SATA_MCM_DIE5_ESATA_PORT1_ENABLED, IDSOPT_DBG_FCH_SATA_MCM_DIE5_ESATA_PORT1_AUTO},
  {IDSNVID_DBG_FCH_SATA_MCM_DIE5_ESATA_PORT2, IDSOPT_DBG_FCH_SATA_MCM_DIE5_ESATA_PORT2_DISABLED, IDSOPT_DBG_FCH_SATA_MCM_DIE5_ESATA_PORT2_ENABLED, IDSOPT_DBG_FCH_SATA_MCM_DIE5_ESATA_PORT2_AUTO},
  {IDSNVID_DBG_FCH_SATA_MCM_DIE5_ESATA_PORT3, IDSOPT_DBG_FCH_SATA_MCM_DIE5_ESATA_PORT3_DISABLED, IDSOPT_DBG_FCH_SATA_MCM_DIE5_ESATA_PORT3_ENABLED, IDSOPT_DBG_FCH_SATA_MCM_DIE5_ESATA_PORT3_AUTO},
  {IDSNVID_DBG_FCH_SATA_MCM_DIE5_ESATA_PORT4, IDSOPT_DBG_FCH_SATA_MCM_DIE5_ESATA_PORT4_DISABLED, IDSOPT_DBG_FCH_SATA_MCM_DIE5_ESATA_PORT4_ENABLED, IDSOPT_DBG_FCH_SATA_MCM_DIE5_ESATA_PORT4_AUTO},
  {IDSNVID_DBG_FCH_SATA_MCM_DIE5_ESATA_PORT5, IDSOPT_DBG_FCH_SATA_MCM_DIE5_ESATA_PORT5_DISABLED, IDSOPT_DBG_FCH_SATA_MCM_DIE5_ESATA_PORT5_ENABLED, IDSOPT_DBG_FCH_SATA_MCM_DIE5_ESATA_PORT5_AUTO},
  {IDSNVID_DBG_FCH_SATA_MCM_DIE5_ESATA_PORT6, IDSOPT_DBG_FCH_SATA_MCM_DIE5_ESATA_PORT6_DISABLED, IDSOPT_DBG_FCH_SATA_MCM_DIE5_ESATA_PORT6_ENABLED, IDSOPT_DBG_FCH_SATA_MCM_DIE5_ESATA_PORT6_AUTO},
  {IDSNVID_DBG_FCH_SATA_MCM_DIE5_ESATA_PORT7, IDSOPT_DBG_FCH_SATA_MCM_DIE5_ESATA_PORT7_DISABLED, IDSOPT_DBG_FCH_SATA_MCM_DIE5_ESATA_PORT7_ENABLED, IDSOPT_DBG_FCH_SATA_MCM_DIE5_ESATA_PORT7_AUTO},

  {IDSNVID_DBG_FCH_SATA_MCM_DIE6_ESATA_PORT0, IDSOPT_DBG_FCH_SATA_MCM_DIE6_ESATA_PORT0_DISABLED, IDSOPT_DBG_FCH_SATA_MCM_DIE6_ESATA_PORT0_ENABLED, IDSOPT_DBG_FCH_SATA_MCM_DIE6_ESATA_PORT0_AUTO},
  {IDSNVID_DBG_FCH_SATA_MCM_DIE6_ESATA_PORT1, IDSOPT_DBG_FCH_SATA_MCM_DIE6_ESATA_PORT1_DISABLED, IDSOPT_DBG_FCH_SATA_MCM_DIE6_ESATA_PORT1_ENABLED, IDSOPT_DBG_FCH_SATA_MCM_DIE6_ESATA_PORT1_AUTO},
  {IDSNVID_DBG_FCH_SATA_MCM_DIE6_ESATA_PORT2, IDSOPT_DBG_FCH_SATA_MCM_DIE6_ESATA_PORT2_DISABLED, IDSOPT_DBG_FCH_SATA_MCM_DIE6_ESATA_PORT2_ENABLED, IDSOPT_DBG_FCH_SATA_MCM_DIE6_ESATA_PORT2_AUTO},
  {IDSNVID_DBG_FCH_SATA_MCM_DIE6_ESATA_PORT3, IDSOPT_DBG_FCH_SATA_MCM_DIE6_ESATA_PORT3_DISABLED, IDSOPT_DBG_FCH_SATA_MCM_DIE6_ESATA_PORT3_ENABLED, IDSOPT_DBG_FCH_SATA_MCM_DIE6_ESATA_PORT3_AUTO},
  {IDSNVID_DBG_FCH_SATA_MCM_DIE6_ESATA_PORT4, IDSOPT_DBG_FCH_SATA_MCM_DIE6_ESATA_PORT4_DISABLED, IDSOPT_DBG_FCH_SATA_MCM_DIE6_ESATA_PORT4_ENABLED, IDSOPT_DBG_FCH_SATA_MCM_DIE6_ESATA_PORT4_AUTO},
  {IDSNVID_DBG_FCH_SATA_MCM_DIE6_ESATA_PORT5, IDSOPT_DBG_FCH_SATA_MCM_DIE6_ESATA_PORT5_DISABLED, IDSOPT_DBG_FCH_SATA_MCM_DIE6_ESATA_PORT5_ENABLED, IDSOPT_DBG_FCH_SATA_MCM_DIE6_ESATA_PORT5_AUTO},
  {IDSNVID_DBG_FCH_SATA_MCM_DIE6_ESATA_PORT6, IDSOPT_DBG_FCH_SATA_MCM_DIE6_ESATA_PORT6_DISABLED, IDSOPT_DBG_FCH_SATA_MCM_DIE6_ESATA_PORT6_ENABLED, IDSOPT_DBG_FCH_SATA_MCM_DIE6_ESATA_PORT6_AUTO},
  {IDSNVID_DBG_FCH_SATA_MCM_DIE6_ESATA_PORT7, IDSOPT_DBG_FCH_SATA_MCM_DIE6_ESATA_PORT7_DISABLED, IDSOPT_DBG_FCH_SATA_MCM_DIE6_ESATA_PORT7_ENABLED, IDSOPT_DBG_FCH_SATA_MCM_DIE6_ESATA_PORT7_AUTO},

  {IDSNVID_DBG_FCH_SATA_MCM_DIE7_ESATA_PORT0, IDSOPT_DBG_FCH_SATA_MCM_DIE7_ESATA_PORT0_DISABLED, IDSOPT_DBG_FCH_SATA_MCM_DIE7_ESATA_PORT0_ENABLED, IDSOPT_DBG_FCH_SATA_MCM_DIE7_ESATA_PORT0_AUTO},
  {IDSNVID_DBG_FCH_SATA_MCM_DIE7_ESATA_PORT1, IDSOPT_DBG_FCH_SATA_MCM_DIE7_ESATA_PORT1_DISABLED, IDSOPT_DBG_FCH_SATA_MCM_DIE7_ESATA_PORT1_ENABLED, IDSOPT_DBG_FCH_SATA_MCM_DIE7_ESATA_PORT1_AUTO},
  {IDSNVID_DBG_FCH_SATA_MCM_DIE7_ESATA_PORT2, IDSOPT_DBG_FCH_SATA_MCM_DIE7_ESATA_PORT2_DISABLED, IDSOPT_DBG_FCH_SATA_MCM_DIE7_ESATA_PORT2_ENABLED, IDSOPT_DBG_FCH_SATA_MCM_DIE7_ESATA_PORT2_AUTO},
  {IDSNVID_DBG_FCH_SATA_MCM_DIE7_ESATA_PORT3, IDSOPT_DBG_FCH_SATA_MCM_DIE7_ESATA_PORT3_DISABLED, IDSOPT_DBG_FCH_SATA_MCM_DIE7_ESATA_PORT3_ENABLED, IDSOPT_DBG_FCH_SATA_MCM_DIE7_ESATA_PORT3_AUTO},
  {IDSNVID_DBG_FCH_SATA_MCM_DIE7_ESATA_PORT4, IDSOPT_DBG_FCH_SATA_MCM_DIE7_ESATA_PORT4_DISABLED, IDSOPT_DBG_FCH_SATA_MCM_DIE7_ESATA_PORT4_ENABLED, IDSOPT_DBG_FCH_SATA_MCM_DIE7_ESATA_PORT4_AUTO},
  {IDSNVID_DBG_FCH_SATA_MCM_DIE7_ESATA_PORT5, IDSOPT_DBG_FCH_SATA_MCM_DIE7_ESATA_PORT5_DISABLED, IDSOPT_DBG_FCH_SATA_MCM_DIE7_ESATA_PORT5_ENABLED, IDSOPT_DBG_FCH_SATA_MCM_DIE7_ESATA_PORT5_AUTO},
  {IDSNVID_DBG_FCH_SATA_MCM_DIE7_ESATA_PORT6, IDSOPT_DBG_FCH_SATA_MCM_DIE7_ESATA_PORT6_DISABLED, IDSOPT_DBG_FCH_SATA_MCM_DIE7_ESATA_PORT6_ENABLED, IDSOPT_DBG_FCH_SATA_MCM_DIE7_ESATA_PORT6_AUTO},
  {IDSNVID_DBG_FCH_SATA_MCM_DIE7_ESATA_PORT7, IDSOPT_DBG_FCH_SATA_MCM_DIE7_ESATA_PORT7_DISABLED, IDSOPT_DBG_FCH_SATA_MCM_DIE7_ESATA_PORT7_ENABLED, IDSOPT_DBG_FCH_SATA_MCM_DIE7_ESATA_PORT7_AUTO} 
};


IDS_HOOK_STATUS
CmnHookFuncFchBrhDxeInitEnv (
  HOOK_ID   HookId,
  VOID     *Handle,
  VOID     *Data
  )
{
  UINT8  PcdData8;
  UINT32 PcdData32;
  UINT64 PcdSata64;
  UINT64 IdsNvValue;
  UINT32 i;

  IDS_HDT_CONSOLE (FCH_TRACE, "CmnHookFuncFchBrhDxeInitEnv Options Update\n");

  // SATA Device Sleep Controller/Port 0
  PcdData8 = PcdGet8 (PcdSataDevSlpPort0Num);

  IDS_NV_READ_SKIP (IDSNVID_DBG_FCH_SATA_DEV_SLP_PORT0_NUM, &IdsNvValue) {
    ASSERT (IdsNvValue <= IDSOPT_DBG_FCH_SATA_DEV_SLP_PORT0_NUM_MAX);
    PcdData8 |= (UINT8)IdsNvValue;
  }


  IDS_NV_READ_SKIP (IDSNVID_DBG_FCH_SATA_DEV_SLP_CONTROLLER0_NUM, &IdsNvValue) {
    ASSERT (IdsNvValue <= IDSOPT_DBG_FCH_SATA_DEV_SLP_CONTROLLER0_NUM_MAX);
    PcdData8 |= ((UINT8)IdsNvValue) << 4;
  }

  FCH_PCDSET8 (PcdSataDevSlpPort0Num, PcdData8);

  // SATA Device Sleep Controller/Port 1

  PcdData8 = PcdGet8 (PcdSataDevSlpPort1Num);

  IDS_NV_READ_SKIP (IDSNVID_DBG_FCH_SATA_DEV_SLP_PORT1_NUM, &IdsNvValue) {
    ASSERT (IdsNvValue <= IDSOPT_DBG_FCH_SATA_DEV_SLP_PORT1_NUM_MAX);
    PcdData8 |= (UINT8)IdsNvValue;
  }

  IDS_NV_READ_SKIP (IDSNVID_DBG_FCH_SATA_DEV_SLP_CONTROLLER1_NUM, &IdsNvValue) {
    ASSERT (IdsNvValue <= IDSOPT_DBG_FCH_SATA_DEV_SLP_CONTROLLER1_NUM_MAX);
    PcdData8 |= ((UINT8)IdsNvValue) << 4;
  }

  FCH_PCDSET8 (PcdSataDevSlpPort1Num, PcdData8);

  // SATA Socket1 Device Sleep
  PcdSata64 = PcdGet64 (PcdSataMultiDieDevSlp);

  IDS_NV_READ_SKIP (IDSNVID_DBG_FCH_SATA_MCM_DIE4_DEV_SLP0, &IdsNvValue) {
    switch (IdsNvValue) {
    case IDSOPT_DBG_FCH_SATA_MCM_DIE4_DEV_SLP0_DISABLED:
      break;
    case IDSOPT_DBG_FCH_SATA_MCM_DIE4_DEV_SLP0_ENABLED:
      PcdSata64 |= 0x01;
      break;
    case IDSOPT_DBG_FCH_SATA_MCM_DIE4_DEV_SLP0_AUTO:
      break;
    default:
      ASSERT (FALSE);
      break;
    }
  }

  IDS_NV_READ_SKIP (IDSNVID_DBG_FCH_SATA_MCM_DIE4_DEV_SLP0_NUM, &IdsNvValue) {
    ASSERT (IdsNvValue <= IDSOPT_DBG_FCH_SATA_MCM_DIE4_DEV_SLP0_NUM_MAX);
    PcdSata64 |= IdsNvValue << 1;
  }

  IDS_NV_READ_SKIP (IDSNVID_DBG_FCH_SATA_MCM_DIE4_DEV_SLP_CONTROLLER0_NUM, &IdsNvValue) {
    ASSERT (IdsNvValue <= IDSOPT_DBG_FCH_SATA_MCM_DIE4_DEV_SLP_CONTROLLER0_NUM_MAX);
    ASSERT (IdsNvValue >= IDSOPT_DBG_FCH_SATA_MCM_DIE4_DEV_SLP_CONTROLLER0_NUM_MIN);
    PcdSata64 |= IdsNvValue << 4;
  }

  IDS_NV_READ_SKIP (IDSNVID_DBG_FCH_SATA_MCM_DIE4_DEV_SLP1, &IdsNvValue) {
    switch (IdsNvValue) {
    case IDSOPT_DBG_FCH_SATA_MCM_DIE4_DEV_SLP1_DISABLED:
      break;
    case IDSOPT_DBG_FCH_SATA_MCM_DIE4_DEV_SLP1_ENABLED:
      PcdSata64 |= 1 << 8;
      break;
    case IDSOPT_DBG_FCH_SATA_MCM_DIE4_DEV_SLP1_AUTO:
      break;
    default:
      ASSERT (FALSE);
      break;
    }
  }

  IDS_NV_READ_SKIP (IDSNVID_DBG_FCH_SATA_MCM_DIE4_DEV_SLP1_NUM, &IdsNvValue) {
    ASSERT (IdsNvValue <= IDSOPT_DBG_FCH_SATA_MCM_DIE4_DEV_SLP1_NUM_MAX);
    PcdSata64 |= IdsNvValue << 9;
  }

  IDS_NV_READ_SKIP (IDSNVID_DBG_FCH_SATA_MCM_DIE4_DEV_SLP_CONTROLLER1_NUM, &IdsNvValue) {
    ASSERT (IdsNvValue <= IDSOPT_DBG_FCH_SATA_MCM_DIE4_DEV_SLP_CONTROLLER1_NUM_MAX);
    ASSERT (IdsNvValue >= IDSOPT_DBG_FCH_SATA_MCM_DIE4_DEV_SLP_CONTROLLER1_NUM_MIN);
    PcdSata64 |= IdsNvValue << 12;
  }


  FCH_PCDSET64 (PcdSataMultiDieDevSlp, PcdSata64);

  // I2C, Uart, ESPI
  PcdData32 = PcdGet32 (FchRTDeviceEnableMap);

  IDS_NV_READ_SKIP (IDSNVID_CMN_FCH_UART0_CONFIG, &IdsNvValue) {
    if (IdsNvValue != IDSOPT_CMN_FCH_UART0_CONFIG_AUTO) { // Auto
      if (IdsNvValue == IDSOPT_CMN_FCH_UART0_CONFIG_ENABLED) {
        PcdData32 |= BIT11;
      } else {
        PcdData32 &= ~ BIT11;
      }
    }
  }

  IDS_NV_READ_SKIP (IDSNVID_CMN_FCH_UART1_CONFIG, &IdsNvValue) {
    if (IdsNvValue != IDSOPT_CMN_FCH_UART1_CONFIG_AUTO) { // Auto
      if (IdsNvValue == IDSOPT_CMN_FCH_UART1_CONFIG_ENABLED) {
        PcdData32 |= BIT12;
      } else {
        PcdData32 &= ~ BIT12;
      }
    }
  }

  IDS_NV_READ_SKIP (IDSNVID_CMN_FCH_UART2_CONFIG, &IdsNvValue) {
    if (IdsNvValue != IDSOPT_CMN_FCH_UART2_CONFIG_AUTO) { // Auto
      if (IdsNvValue == IDSOPT_CMN_FCH_UART2_CONFIG_ENABLED) {
        PcdData32 |= BIT16;
      } else {
        PcdData32 &= ~ BIT16;
      }
    }
  }

  // IDS_NV_READ_SKIP (IDSNVID_CMN_FCH_UART3_CONFIG, &IdsNvValue) {
  //   if (IdsNvValue != IDSOPT_CMN_FCH_UART3_CONFIG_AUTO) { // Auto
  //     if (IdsNvValue == IDSOPT_CMN_FCH_UART3_CONFIG_ENABLED) {
  //       PcdData32 |= BIT26;
  //     } else {
  //       PcdData32 &= ~ BIT26;
  //     }
  //   }
  // }


  FCH_PCDSET32 (FchRTDeviceEnableMap, PcdData32);

  // Init eSATA PCD ->
  PcdSata64 = PcdGet64 (PcdSataMultiDiePortESP);
  IDS_HDT_CONSOLE (FCH_TRACE, "%a PcdSataMultiDiePortESP 0x%lx\n", __FUNCTION__, PcdSata64);
  for (i = 0; i < 64; i++) {
    IDS_NV_READ_SKIP (Sata_eSATA_NV_ID[i].NvId , &IdsNvValue) {
      if ((UINT32) IdsNvValue == Sata_eSATA_NV_ID[i].eSata) {
        PcdSata64 |= ((UINT64) 1) << i;
      } else if ((UINT32) IdsNvValue == Sata_eSATA_NV_ID[i].iSata) {
        PcdSata64 &= ~(((UINT64) 1) << i);
      } else if ((UINT32) IdsNvValue == Sata_eSATA_NV_ID[i].Auto) {
        // Do nothing with Auto
      } else {
        ASSERT (FALSE);
      }
    }
  }
  IDS_HDT_CONSOLE (FCH_TRACE, "%a PcdSataMultiDiePortESP 0x%lx\n", __FUNCTION__, PcdSata64);
  FCH_PCDSET64 (PcdSataMultiDiePortESP, PcdSata64);
 // Init eSATA PCD <-

  return IDS_HOOK_SUCCESS;
}

#ifndef IDS_HOOK_INTERNAL_SUPPORT
  #define FCH_BRH_IDS_HOOKS_INT_DXE
#else
  #include "Internal/FchIdsHookBrhLibIntDxe.h"
#endif

IDS_HOOK_ELEMENT FchBrhIdsHooksDxe[] = {
  {
    IDS_HOOK_FCH_INIT_ENV,
    &CmnHookFuncFchBrhDxeInitEnv
  },
  FCH_BRH_IDS_HOOKS_INT_DXE
  IDS_HOOKS_END
};

IDS_HOOK_TABLE FchBrhIdsHookTableDxe = {
  IDS_HOOK_TABLE_HEADER_REV1_DATA,
  FchBrhIdsHooksDxe
};

AGESA_STATUS
GetIdsHookTable (
  IDS_HOOK_TABLE **IdsHookTable
  )
{
  *IdsHookTable = &FchBrhIdsHookTableDxe;
  return AGESA_SUCCESS;
}



