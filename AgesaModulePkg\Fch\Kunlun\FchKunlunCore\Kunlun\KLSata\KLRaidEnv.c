/*****************************************************************************
 * Copyright (C) 2008-2024 Advanced Micro Devices, Inc. All rights reserved.
 *
*****************************************************************************
*/
/* $NoKeywords:$ */
/**
 * @file
 *
 * Config Fch SATA controller (Raid mode)
 *
 * Init SATA Raid features.
 *
 * @xrefitem bom "File Content Label" "Release Content"
 * @e project:     AGESA
 * @e sub-project: FCH
 * @e \$Revision: 309083 $   @e \$Date: 2014-12-09 09:28:24 -0800 (Tue, 09 Dec 2014) $
 *
 */
#include "FchPlatform.h"
#include "Filecode.h"
#define FILECODE FCH_KUNLUN_FCHKUNLUNCORE_KUNLUN_KLSATA_KLRAIDENV_FILECODE

//
// Declaration of local functions
//


/*
Because of security consideration, x86 is forbidden to access nBIF straps.
Move code to ABL.
*/
#if 0
/**
 * FchInitEnvSataRaid - Config SATA Raid controller before PCI
 * emulation
 *
 *
 * @param[in] DieBusNum  IOCH bus number on current Die.
 * @param[in] Controller Sata controller number.
 * @param[in] FchDataPtr Fch configuration structure pointer.
 *
 */
VOID
FchInitEnvSataRaidKL  (
  IN  UINT32   DieBusNum,
  IN  UINT32   Controller,
  IN  VOID     *FchDataPtr
  )
{
  FCH_DATA_BLOCK         *LocalCfgPtr;
  AMD_CONFIG_PARAMS      *StdHeader;
  UINT32                 Strap0;
  UINT32                 Strap13;
  UINT32                 PcicfgBase;

  LocalCfgPtr = (FCH_DATA_BLOCK *) FchDataPtr;
  StdHeader = LocalCfgPtr->StdHeader;
  Strap0 = FCH_KL_SMN_SATA0_NBIF_STRAP0;
  Strap13 = FCH_KL_SMN_SATA0_NBIF_STRAP13;
  PcicfgBase = FCH_KL_SATA0_SMN_PCICFG;

  //
  // Get Strap/register address for different controller
  //
  switch (Controller) {
  case 0:
    break;
  case 1:
    Strap0 = FCH_KL_SMN_SATA1_NBIF_STRAP0;
    Strap13 = FCH_KL_SMN_SATA1_NBIF_STRAP13;
    PcicfgBase = FCH_KL_SATA1_SMN_PCICFG;
    break;
  case 2:
    Strap0 = FCH_KL_SMN_SATA2_NBIF_STRAP0;
    Strap13 = FCH_KL_SMN_SATA2_NBIF_STRAP13;
    PcicfgBase = FCH_KL_SATA2_SMN_PCICFG;
    break;
  case 3:
    Strap0 = FCH_KL_SMN_SATA3_NBIF_STRAP0;
    Strap13 = FCH_KL_SMN_SATA3_NBIF_STRAP13;
    PcicfgBase = FCH_KL_SATA3_SMN_PCICFG;
    break;
  default:
    break;
  }

  //
  // Class code
  //
  FchSmnRW (DieBusNum, Strap13, 0x00, 0x00010400, StdHeader);
  //
  // Device ID
  //
  FchSmnRW (DieBusNum, Strap0, 0xFFFF0000, KUNLUN_FCH_SATA_RAID_DID, StdHeader);

  //
  // SSID
  //
  if (LocalCfgPtr->Sata[Controller].SataRaidSsid != NULL ) {
    FchSmnRW (DieBusNum, PcicfgBase + 0x4C, 0x00, LocalCfgPtr->Sata[Controller].SataRaidSsid, StdHeader);
  }
}
#endif


