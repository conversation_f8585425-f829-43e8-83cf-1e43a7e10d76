/*****************************************************************************
 *
 * Copyright (C) 2019-2024 Advanced Micro Devices, Inc. All rights reserved.
 *
 *****************************************************************************
 */

/*----------------------------------------------------------------------------------------
 *                             M O D U L E S    U S E D
 *----------------------------------------------------------------------------------------
 */
#include "Porting.h"
#include "AMD.h"
#include "FabricAcpiTable.h"
#include "FabricAcpiDomainInfo.h"
#include "Library/AmdBaseLib.h"
#include <FabricRegistersBrh.h>
#include <FabricInfoBrh.h>
#include "Library/UefiBootServicesTableLib.h"
#include <Protocol/AmdAcpiSratServicesProtocol.h>
#include <Protocol/FabricNumaServices2.h>
#include "Filecode.h"

/*----------------------------------------------------------------------------------------
 *                   D E F I N I T I O N S    A N D    M A C R O S
 *----------------------------------------------------------------------------------------
 */
#define FILECODE FABRIC_BRH_FABRICBRHDXE_FABRICACPISRAT_FILECODE

/*----------------------------------------------------------------------------------------
 *                         E X T E R N   D E F I N I T I O N S
 *----------------------------------------------------------------------------------------
 */
extern UINT32 mNumberOfReportedDomains;
extern UINT32 mSystemCxlCount;

/*----------------------------------------------------------------------------------------
 *           P R O T O T Y P E S     O F     L O C A L     F U N C T I O N S
 *----------------------------------------------------------------------------------------
 */
EFI_STATUS
EFIAPI
FabricCreateSratMemoryInfo (
  IN     AMD_FABRIC_ACPI_SRAT_SERVICES_PROTOCOL  *This,
  IN     SRAT_HEADER                             *SratHeaderStructPtr,
  IN OUT UINT8                                   **TableEnd
  );

EFI_STATUS
EFIAPI
FabricGetSratMemoryInfo (
  IN     AMD_FABRIC_ACPI_SRAT_SERVICES_PROTOCOL  *This,
     OUT UINT32                                  *NumberOfDomains,
     OUT MEMORY_INFO                             **MemoryInfo
  );

VOID
CreateSratMemoryEntry (
  IN OUT UINT8   **TablePointer,
  IN     UINT32  Domain,
  IN     UINT32  RegionBaseLow,
  IN     UINT32  RegionBaseHigh,
  IN     UINT32  RegionSizeLow,
  IN     UINT32  RegionSizeHigh
  );

/*----------------------------------------------------------------------------------------
 *                           G L O B A L   V A R I A B L E S
 *----------------------------------------------------------------------------------------
 */
STATIC AMD_FABRIC_ACPI_SRAT_SERVICES_PROTOCOL mFabricAcpiSratServicesProtocol = {
  0x1,
  FabricCreateSratMemoryInfo,
  FabricGetSratMemoryInfo
};

/*----------------------------------------------------------------------------------------
 *                  T Y P E D E F S     A N D     S T R U C T U R E S
 *----------------------------------------------------------------------------------------
 */

/*----------------------------------------------------------------------------------------
 *                          E X P O R T E D    F U N C T I O N S
 *----------------------------------------------------------------------------------------
 */

/*----------------------------------------------------------------------------------------*/
/**
 * @brief This function will install the SRAT services protocol.
 *
 * @param[in] ImageHandle  Image handle.
 * @param[in] SystemTable  EFI system table.
 *
 * @return EFI_STATUS
 */
EFI_STATUS
EFIAPI
FabricBrhAcpiSratProtocolInstall (
  IN EFI_HANDLE        ImageHandle,
  IN EFI_SYSTEM_TABLE  *SystemTable
  )
{
  return gBS->InstallProtocolInterface (&ImageHandle,
                                        &gAmdFabricAcpiSratServicesProtocolGuid,
                                        EFI_NATIVE_INTERFACE,
                                        &mFabricAcpiSratServicesProtocol);
}

/**
 * @brief This function adds the system's SRAT memory entries to the SRAT table.
 *
 * @param[in]      This                SRAT services.
 * @param[in]      SratHeaderStructPtr SRAT table header pointer.
 * @param[in, out] TableEnd            Current SRAT table pointer to store the memory structures to.
 *
 * @return EFI_STATUS
 *
 * @retval EFI_SUCCESS - Memory structures published successfully.
 * @retval EFI_ABORTED - A problem was encountered.
 */
EFI_STATUS
EFIAPI
FabricCreateSratMemoryInfo (
  IN     AMD_FABRIC_ACPI_SRAT_SERVICES_PROTOCOL  *This,
  IN     SRAT_HEADER                             *SratHeaderStructPtr,
  IN OUT UINT8                                   **TableEnd
  )
{
  return FabricCreateSystemAcpiDomainData (TableEnd, &CreateSratMemoryEntry, NULL);
}

/**
 * @brief This function returns information about the domains declared via the SRAT.
 *
 * @param[in]  This            SRAT services.
 * @param[out] NumberOfDomains Number of memory entries in the SRAT.
 * @param[out] MemoryInfo      Information about the SRAT memory entries.
 *
 * @return EFI_STATUS
 *
 * @retval EFI_SUCCESS           - Requested info valid.
 * @retval EFI_INVALID_PARAMETER - Requested info not returned.
 */
EFI_STATUS
EFIAPI
FabricGetSratMemoryInfo (
  IN     AMD_FABRIC_ACPI_SRAT_SERVICES_PROTOCOL  *This,
     OUT UINT32                                  *NumberOfDomains,
     OUT MEMORY_INFO                             **MemoryInfo
  )
{
  if ((NumberOfDomains == NULL) && (MemoryInfo == NULL)) {
    return EFI_INVALID_PARAMETER;
  }

  return FabricGetMemoryInfo (NumberOfDomains, MemoryInfo);
}

/**
 * @brief This function writes one memory entry to the SRAT table and updates the pointer.
 *
 * @param[in, out] TablePointer   Current SRAT table pointer to store the memory structures to.
 * @param[in]      Domain         This region's domain.
 * @param[in]      RegionBaseLow  Lower 32 bits of the region's base address.
 * @param[in]      RegionBaseHigh Upper 32 bits of the region's base address.
 * @param[in]      RegionSizeLow  Lower 32 bits of the region's size.
 * @param[in]      RegionSizeHigh Upper 32 bits of the region's size.
 */
VOID
CreateSratMemoryEntry (
  IN OUT UINT8   **TablePointer,
  IN     UINT32  Domain,
  IN     UINT32  RegionBaseLow,
  IN     UINT32  RegionBaseHigh,
  IN     UINT32  RegionSizeLow,
  IN     UINT32  RegionSizeHigh
  )
{
  SRAT_MEMORY *MemoryEntry;

  MemoryEntry = (SRAT_MEMORY *) *TablePointer;
  *TablePointer += sizeof (SRAT_MEMORY);

  MemoryEntry->Type            = SRAT_MEMORY_TYPE;
  MemoryEntry->Length          = sizeof (SRAT_MEMORY);
  MemoryEntry->ProximityDomain = Domain;
  MemoryEntry->BaseAddressLow  = RegionBaseLow;
  MemoryEntry->BaseAddressHigh = RegionBaseHigh;
  MemoryEntry->LengthLow       = RegionSizeLow;
  MemoryEntry->LengthHigh      = RegionSizeHigh;
  MemoryEntry->Flags.Enabled   = 1;
  if ((mSystemCxlCount > 0) && (mNumberOfReportedDomains > mSystemCxlCount)) {
    if (Domain >= (mNumberOfReportedDomains - mSystemCxlCount)) {
      MemoryEntry->Flags.SPM = PcdGetBool (PcdAmdCxlSpm);
      MemoryEntry->Flags.HotPluggable = 1;
    }
  }
}

