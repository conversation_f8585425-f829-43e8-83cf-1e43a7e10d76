#;*****************************************************************************
#;
#; Copyright (C) 2008-2024 Advanced Micro Devices, Inc. All rights reserved.
#;
#;******************************************************************************

[Defines]
  INF_VERSION                    = 0x00010006
  BASE_NAME                      = AmdEmulationFlagDxeSmmLib
  FILE_GUID                      = ********-9BE0-47DF-92B3-65BBB7D05183
  MODULE_TYPE                    = DXE_DRIVER
  VERSION_STRING                 = 1.0
  LIBRARY_CLASS                  = AmdEmulationFlagLib | DXE_DRIVER DXE_SMM_DRIVER DXE_RUNTIME_DRIVER DXE_CORE SMM_CORE UEFI_DRIVER
  CONSTRUCTOR                    = AmdEmulationFlagLibConstructor

[Sources.common]
  AmdEmulationFlagDxeSmmLib.c

[Packages]
  MdePkg/MdePkg.dec
  AgesaModulePkg/AgesaCommonModulePkg.dec
  AgesaPkg/AgesaPkg.dec

[LibraryClasses]
  BaseLib
  AmdBaseLib
  HobLib
  PresiliconControlLib

[Guids]
  gAmdEmulationFlagHobGuid

[Protocols]

[Ppis]

[Pcd]

[Depex]





