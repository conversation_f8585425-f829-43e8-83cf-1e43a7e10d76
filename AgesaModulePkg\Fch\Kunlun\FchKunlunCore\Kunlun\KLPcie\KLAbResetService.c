/*****************************************************************************
 * Copyright (C) 2008-2024 Advanced Micro Devices, Inc. All rights reserved.
 *
*****************************************************************************
*/
/* $NoKeywords:$ */
/**
 * @file
 *
 * Config KUNLUN AB
 *
 * Init AB bridge.
 *
 * @xrefitem bom "File Content Label" "Release Content"
 * @e project:     AGESA
 * @e sub-project: FCH
 * @e \$Revision: 309083 $   @e \$Date: 2014-12-09 09:28:24 -0800 (Tue, 09 Dec 2014) $
 *
 */
#include "FchPlatform.h"
#include "Filecode.h"
#define FILECODE FCH_KUNLUN_FCHKUNLUNCORE_KUNLUN_KLPCIE_KLABRESETSERVICE_FILECODE


/**
 * FchProgramAbPowerOnReset - Config Ab Bridge during Power-On
 *
 *
 *
 * @param[in] FchDataPtr Fch configuration structure pointer.
 *
 */
VOID
FchProgramAbPowerOnReset (
  IN  VOID     *FchDataPtr
  )
{
  //
  // Set A-Link bridge access address.
  // This is an I/O address. The I/O address must be on 16-byte boundary.
  //
  RwMem (ACPI_MMIO_BASE + PMIO_BASE + FCH_PMIOA_REGE0, AccessWidth32, 00, ALINK_ACCESS_INDEX);
}




