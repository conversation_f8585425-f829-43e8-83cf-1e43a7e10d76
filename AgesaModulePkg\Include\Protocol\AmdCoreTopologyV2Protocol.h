/*****************************************************************************
 * Copyright (C) 2008-2024 Advanced Micro Devices, Inc. All rights reserved.
 *
*****************************************************************************
*/
/* $NoKeywords:$ */
/**
 * @file
 *
 * AMD Core Topology Services Protocol prototype definition
 *
 *
 * @xrefitem bom "File Content Label" "Release Content"
 * @e project:      AGESA
 * @e sub-project:  Fabric
 * @e \$Revision: 313706 $   @e \$Date: 2015-02-25 21:00:43 -0600 (Wed, 25 Feb 2015) $
 */

#ifndef _AMD_CORE_TOPOLOGY_SERVICES_V2_PROTOCOL_H_
#define _AMD_CORE_TOPOLOGY_SERVICES_V2_PROTOCOL_H_


///
/// Forward declaration for the AMD_CORE_TOPOLOGY_SERVICES_V2_PROTOCOL.
///
typedef struct _AMD_CORE_TOPOLOGY_SERVICES_V2_PROTOCOL AMD_CORE_TOPOLOGY_SERVICES_V2_PROTOCOL;

/**
 * This service retrieves information about the layout of the cores on the given die.
 *
 * @param[in]  This                           A pointer to the
 *                                            AMD_CORE_TOPOLOGY_SERVICES_V2_PROTOCOL instance.
 * @param[in]  Socket                         Zero-based socket number to check.
 * @param[in]  Die                            The target die's identifier within Socket.
 * @param[out] NumberOfCcds                   Pointer to the number of enabled CCDs on
 *                                            the given socket / die.
 * @param[out] NumberOfComplexes              Pointer to the number of enabled complexes on
 *                                            the given socket / die.
 * @param[out] NumberOfCores                  Pointer to the number of enabled cores per
 *                                            complex on the given socket / die.
 * @param[out] NumberOfThreads                Pointer to the number of enabled threads per
 *                                            core on the given socket / die.
 *
 * @retval EFI_SUCCESS                        The core topology information was successfully retrieved.
 * @retval EFI_INVALID_PARAMETER              Socket is non-existent.
 * @retval EFI_INVALID_PARAMETER              Die is non-existent.
 * @retval EFI_INVALID_PARAMETER              All output parameter pointers are NULL.
 *
 **/
typedef
EFI_STATUS
(EFIAPI *AMD_CORE_TOPOLOGY_SERVICES_GET_CORE_TOPOLOGY_ON_DIE_V2) (
  IN       AMD_CORE_TOPOLOGY_SERVICES_V2_PROTOCOL  *This,
  IN       UINTN                                   Socket,
  IN       UINTN                                   Die,
     OUT   UINTN                                   *NumberOfCcds,
     OUT   UINTN                                   *NumberOfComplexes,
     OUT   UINTN                                   *NumberOfCores,
     OUT   UINTN                                   *NumberOfThreads
  );

/**
 * This service will start a core to fetch its first instructions from the reset
 * vector.  This service may only be called from the BSP.
 *
 * @param[in]  This                           A pointer to the
 *                                            AMD_CORE_TOPOLOGY_SERVICES_V2_PROTOCOL instance.
 * @param[in]  Socket                         Zero-based socket number of the target thread.
 * @param[in]  Die                            Zero-based die number within Socket of the target thread.
 * @param[in]  LogicalCcd                     Zero-based logical core complex die of the target thread.
 * @param[in]  LogicalComplex                 Zero-based logical complex number of the target thread.
 * @param[in]  LogicalCore                    Zero-based logical core number of the target thread.
 * @param[in]  LogicalThread                  Zero-based logical thread number of the target thread.
 *
 * @retval EFI_SUCCESS                        The thread was successfully launched.
 * @retval EFI_DEVICE_ERROR                   The thread has already been launched.
 * @retval EFI_INVALID_PARAMETER              Socket is non-existent.
 * @retval EFI_INVALID_PARAMETER              Die is non-existent.
 * @retval EFI_INVALID_PARAMETER              LogicalComplex is non-existent.
 * @retval EFI_INVALID_PARAMETER              LogicalThread is non-existent.
 *
 **/
typedef
EFI_STATUS
(EFIAPI *AMD_CORE_TOPOLOGY_SERVICES_LAUNCH_THREAD_V2) (
  IN       AMD_CORE_TOPOLOGY_SERVICES_V2_PROTOCOL  *This,
  IN       UINTN                                   Socket,
  IN       UINTN                                   Die,
  IN       UINTN                                   LogicalCcd,
  IN       UINTN                                   LogicalComplex,
  IN       UINTN                                   LogicalCore,
  IN       UINTN                                   LogicalThread
  );

///
/// When installed, the AMD Core Topology Services PROTOCOL produces a
/// collection of services that provide information on Core Topology.
///
struct _AMD_CORE_TOPOLOGY_SERVICES_V2_PROTOCOL {
  AMD_CORE_TOPOLOGY_SERVICES_GET_CORE_TOPOLOGY_ON_DIE_V2  GetCoreTopologyOnDie; ///< Function to get the layout of the cores on the given die.
  AMD_CORE_TOPOLOGY_SERVICES_LAUNCH_THREAD_V2             LaunchThread;         ///< Function to start a thread.
};

///
/// Guid declaration for the AMD_CORE_TOPOLOGY_SERVICES_V2_PROTOCOL.
///
extern EFI_GUID gAmdCoreTopologyServicesV2ProtocolGuid;

#endif



