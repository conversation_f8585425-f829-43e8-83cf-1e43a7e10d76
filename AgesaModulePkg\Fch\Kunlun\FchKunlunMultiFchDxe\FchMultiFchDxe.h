/*****************************************************************************
 * Copyright (C) 2008-2024 Advanced Micro Devices, Inc. All rights reserved.
 *
*****************************************************************************
*/

#ifndef _FCH_DXE_H_
#define _FCH_DXE_H_

#include <Library/BaseLib.h>
#include <Library/BaseMemoryLib.h>
#include <Library/DebugLib.h>
#include <Library/UefiLib.h>
#include <Library/HobLib.h>
#include <Library/UefiDriverEntryPoint.h>
#include <Library/UefiBootServicesTableLib.h>

#include "FchPlatform.h"
#include <Library/FchDxeLib.h>
#include <Library/FabricRegisterAccLib.h>
#include <Library/BaseFabricTopologyLib.h>

#include <Protocol/FchInitProtocol.h>
#include <Protocol/FchMultiFchInitProtocol.h>
#include <Protocol/FabricTopologyServices2.h>

//
// Module data structure
//
/// Private data and access defines
typedef struct _FCH_MULTI_FCH_DXE_PRIVATE {
  UINTN                            Signature;           ///< Signature
  FCH_MULTI_FCH_INIT_PROTOCOL      FchMultiFchInit;     ///< Protocol data
  EFI_EVENT                        EventReadyToBoot;    ///< Event related data
} FCH_MULTI_FCH_DXE_PRIVATE;

#define FCH_MULTI_FCH_DXE_PRIVATE_DATA_SIGNATURE   SIGNATURE_32 ('F', 'M', 'F', 'D')

#define FCH_MULTI_FCH_DXE_PRIVATE_INSTANCE_FROM_THIS(a) \
  CR (a, FCH_MULTI_FCH_DXE_PRIVATE, FchMultiFchInit, FCH_MULTI_FCH_DXE_PRIVATE_DATA_SIGNATURE)


//
// Functions Prototypes
//
EFI_STATUS
EFIAPI
MultiFchDxeInit (
  IN       EFI_HANDLE         ImageHandle,
  IN       EFI_SYSTEM_TABLE   *SystemTable
  );

VOID
FchKLSecondaryFchInitSataDxe (
  IN  UINT8       Die,
  IN  UINT32      DieBusNum,
  IN  VOID        *FchDataPtr
);

VOID
EFIAPI
MultiFchInitRtb (
  IN       EFI_EVENT        Event,
  IN       VOID             *Context
);

VOID
FchKLSecondaryFchInitSataRtb (
  IN  UINT8       Die,
  IN  UINT32      DieBusNum,
  IN  VOID        *FchDataPtr
);

VOID
FchSecondarySendStartTelemetryMsg(
  IN  UINT32      DieBusNum,
  IN  VOID        *FchDataPtr
);

#endif // _FCH_DXE_H_



