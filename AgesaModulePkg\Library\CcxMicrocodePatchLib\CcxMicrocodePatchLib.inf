#;*****************************************************************************
#;
#; Copyright (C) 2008-2024 Advanced Micro Devices, Inc. All rights reserved.
#;
#;******************************************************************************
#For EDKII use Only
[Defines]
  INF_VERSION                    = 0x00010006
  BASE_NAME                      = CcxMicrocodePatchLib
  FILE_GUID                      = BFEBDC1C-7FA0-4fda-99C4-106C7C6D64DB
  MODULE_TYPE                    = BASE
  VERSION_STRING                 = 1.0
  LIBRARY_CLASS                  = CcxMicrocodePatchLib

[Sources.common]
  CcxMicrocodePatchLib.c

[Packages]
  MdePkg/MdePkg.dec
  AgesaPkg/AgesaPkg.dec
  AgesaModulePkg/AgesaCommonModulePkg.dec
  AgesaModulePkg/AgesaModuleCcxPkg.dec
  AgesaModulePkg/AgesaModulePspPkg.dec

[LibraryClasses]
  BaseLib
  AmdBaseLib
  CcxRolesLib
  IdsLib
  AmdPspBaseLibV2
  PciLib
  MemoryAllocationLib
  AmdPspMboxLibV2
  AmdDirectoryBaseLib
  IoLib

[Guids]

[Protocols]

[Ppis]

[Pcd]

[Depex]



