#;*****************************************************************************
#;
#; Copyright (C) 2008-2024 Advanced Micro Devices, Inc. All rights reserved.
#;
#;*****************************************************************************
[Defines]
  INF_VERSION                    = 0x00010006
  BASE_NAME                      = FchIdsHookBrhLibDxe
  FILE_GUID                      = 20392dc9-a61b-40b2-896b-b88c91b007c7
  MODULE_TYPE                    = DXE_DRIVER
  VERSION_STRING                 = 1.0
  LIBRARY_CLASS                  = FchIdsHookLib

[Sources.common]
  FchIdsHookBrhLibDxe.c

[Packages]
  MdePkg/MdePkg.dec
  AgesaPkg/AgesaPkg.dec
  AgesaModulePkg/AgesaCommonModulePkg.dec
  AgesaModulePkg/AgesaModuleFchPkg.dec
  AgesaModulePkg/Fch/Kunlun/FchKunlun.dec

[LibraryClasses]
  AmdBaseLib
  AmdIdsDebugPrintLib

[Guids]

[Protocols]

[Ppis]

[FeaturePcd]

[Pcd]
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdSataDevSlpPort0Num
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdSataDevSlpPort1Num
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdSataMultiDiePortESP
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdSataMultiDieDevSlp
  gEfiAmdAgesaModulePkgTokenSpaceGuid.FchRTDeviceEnableMap


[Depex]
  TRUE

[BuildOptions]

