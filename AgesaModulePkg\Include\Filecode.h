/*****************************************************************************
 *
 * Copyright (C) 2008-2024 Advanced Micro Devices, Inc. All rights reserved.
 *
 *******************************************************************************
 **/
/* $NoKeywords:$ */
/**
 * @file
 *
 * Collectively assign unique filecodes for assert and debug to each source file.
 *
 * Publish values for decorated filenames, which can be used for
 * ASSERT and debug support using a preprocessor define like:
 * @n <tt> _#define FILECODE MY_C_FILENAME_FILECODE </tt> @n
 * This file serves as a reference for debugging to associate the code and filename.
 *
 * @xrefitem bom "File Content Label" "Release Content"
 * @e project:      AGESA
 * @e sub-project:  Include
 * @e _$Revision: 312538 $   @e \$Date: 2015-02-09 16:53:54 +0800 (Mon, 09 Feb 2015) $
 */

#ifndef _FILECODE_H_
#define _FILECODE_H_

#define UNASSIGNED_FILE_FILECODE                                          (0xFFFF)

/// For debug use in any Platform's options C file.
/// Can be reused for platforms and image builds, since only one options file can be built.
#define PLATFORM_SPECIFIC_OPTIONS_FILECODE                                (0xBBBB)

#define LIBRARY_AMDBASELIB_AMDBASELIB_FILECODE                            (0x0001)
#define LIBRARY_AMDTABLELIB_AMDTABLELIB_FILECODE                          (0x0002)
#define LIBRARY_AMDTABLELIB_PEI_AMDTABLEHOOKPEILIB_FILECODE               (0x0003)
#define LIBRARY_AMDTABLELIB_DXE_AMDTABLEHOOKDXELIB_FILECODE               (0x0004)
#define LIBRARY_AMDTABLELIB_PEIXV_AMDTABLEHOOKPEIXVLIB_FILECODE           (0x0005)
#define LIBRARY_AMDTABLELIB_DXEXV_AMDTABLEHOOKDXEXVLIB_FILECODE           (0x0006)
#define LIBRARY_AMDHEAPPEILIB_AMDHEAPPEILIB_FILECODE                      (0x0007)
#define LIBRARY_AMDHEAPDXELIB_AMDHEAPDXELIB_FILECODE                      (0x0008)
#define LIBRARY_AMDS3SAVELIB_WOS3SAVE_AMDWOS3SAVELIB_FILECODE             (0x0009)
#define LIBRARY_AMDS3SAVELIB_S3SAVE_AMDS3SAVELIB_FILECODE                 (0x000A)
#define LIBRARY_CCXMPSERVICESDXELIB_CCXMPSERVICESDXELIB_FILECODE          (0x000B)
#define LIBRARY_CCXMPSERVICESSMMLIB_CCXMPSERVICESSMMLIB_FILECODE          (0x000C)
#define LIBRARY_DXECCXSMMACCESS2LIB_CCXSMMACCESS2LIB_FILECODE             (0x000D)
#define LIBRARY_DXECCXCPPCLIB_CCXCPPCLIB_FILECODE                         (0x000E)
#define LIBRARY_AMDHEAPLIBNULL_AMDHEAPLIBNULL_FILECODE                    (0x000F)

#define LIBRARY_AMDTABLELIBV2_AMDTABLELIBV2_FILECODE                      (0x0012)
#define LIBRARY_AMDTABLELIBV2_PEI_AMDTABLEHOOKPEILIBV2_FILECODE           (0x0013)
#define LIBRARY_AMDTABLELIBV2_DXE_AMDTABLEHOOKDXELIBV2_FILECODE           (0x0014)
#define LIBRARY_AMDCAPSULELIBPEI_AMDCAPSULELIBPEI_FILECODE                (0x0015)
#define LIBRARY_AMDCAPSULELIBDXE_AMDCAPSULELIBDXE_FILECODE                (0x0016)

#define LIBRARY_AGESACONFIGLIB_AGESACONFIGLIB_FILECODE                    (0x0017)
#define UNIVERSAL_ACTDXE_ACTDXE_FILECODE                                  (0x0018)
#define LIBRARY_AMDCFGPCDBUFLIBNULL_AMDCFGPCDBUFLIBNULL_FILECODE          (0x0019)
#define LIBRARY_AMDCFGPCDBUFLIBPEI_AMDCFGPCDBUFLIBPEI_FILECODE            (0x001A)
#define LIBRARY_AMDCFGPCDBUFLIBDXE_AMDCFGPCDBUFLIBDXE_FILECODE            (0x001B)
#define UNIVERSAL_AMDABLPERFORMANCEDXE_AMDABLPERFORMANCEDXE_FILECODE      (0x001C)

// Memory

#define MEM_AMDMEMCZPEI_AMDMEMCZPEI_FILECODE                              (0x1001)
#define MEM_AMDMEMFEATPEI_AMDMEMFEATPEI_FILECODE                          (0x1002)
#define MEM_AMDMEMS3CZDXE_AMDMEMS3CZDXE_FILECODE                          (0x1003)
#define MEM_AMDMEMSMBIOSDXE_AMDMEMSMBIOSDXE_FILECODE                      (0x1004)

#define MEM_AMDMEMAM4DXE_AMDMEMAM4DXE_FILECODE                            (0x1005)
#define MEM_AMDMEMAM4PEI_AMDMEMAM4PEI_FILECODE                            (0x1006)
#define MEM_AMDMEMDDR4PEI_AMDMEMDDR4PEI_FILECODE                          (0x1007)
#define MEM_AMDMEMFP4PEI_AMDMEMFP4PEI_FILECODE                            (0x1008)
#define MEM_AMDMEMPEI_AMDMEMPEI_FILECODE                                  (0x1009)
#define MEM_AMDMEMSMPEI_AMDMEMSMPEI_FILECODE                              (0x100A)
#define MEM_AMDMEMSMBIOSV2PEI_MEMSMBIOSV2PEI_FILECODE                     (0x100C)
#define MEM_AMDMEMCHANXLATZPPEI_MEMCHANXLATZPPEI_FILECODE                 (0x100D)
#define MEM_AMDMEMCHANXLATDUMMYPEI_MEMCHANXLATDUMMYPEI_FILECODE           (0x100E)
#define MEM_AMDMEMSMBIOSV2DXE_AMDMEMSMBIOSV2DXE_FILECODE                  (0x100F)

#define MEM_AMDMEMRESTOREPEI_MEMRESTOREPEI_FILECODE                       (0x1010)
#define MEM_AMDMEMRESTOREDXE_MEMRESTOREDXE_FILECODE                       (0x1011)
#define MEM_AMDMEMZPSP3PEI_AMDMEMZPSP3PEI_FILECODE                        (0x1012)
#define MEM_AMDMEMSSPSP3PEI_AMDMEMSSPSP3PEI_FILECODE                      (0x1013)
#define MEM_AMDMEMZPSP3DXE_AMDMEMZPSP3DXE_FILECODE                        (0x1014)
#define MEM_AMDMEMSSPSP3DXE_AMDMEMSSPSP3DXE_FILECODE                      (0x1015)
#define MEM_AMDMEMZPSP3R2PEI_AMDMEMZPSP3R2PEI_FILECODE                    (0x1016)
#define MEM_AMDMEMZPSP3R2DXE_AMDMEMZPSP3R2DXE_FILECODE                    (0x1017)

#define MEM_AMDMEMAM4RNPEI_AMDMEMAM4RNPEI_FILECODE                        (0x1018)
#define MEM_AMDMEMORYHOBINFOPEIMV3_AMDMEMORYHOBINFOPEIMV3_FILECODE        (0x1019)
#define MEM_AMDMEMBASP3PEI_AMDMEMBASP3PEI_FILECODE                        (0x101A)
#define MEM_AMDMEMORYHOBINFOPEIMRS_AMDMEMORYHOBINFOPEIMRS_FILECODE        (0x101B)
#define MEM_AMDMEMORYHOBINFOPEIMRMB_AMDMEMORYHOBINFOPEIM_FILECODE         (0x101C)

#define MEM_AMDMEMAM5DXE_AMDMEMAM5DXE_FILECODE                            (0x101D)
#define MEM_AMDMEMAM5PEI_AMDMEMAM5PEI_FILECODE                            (0x101E)
#define MEM_AMDMEMRSSP5PEI_AMDMEMRSSP5PEI_FILECODE                        (0x101F)
#define MEM_AMDMEMSMBIOSV2RMBPEI_MEMSMBIOSV2PEI_FILECODE                  (0x1020)
#define MEM_AMDMEMFP7DXE_AMDMEMFP7DXE_FILECODE                            (0x1021)
#define MEM_AMDMEMFP7PEI_AMDMEMFP7PEI_FILECODE                            (0x1022)
#define MEM_AMDMEMTESTEXPEI_AMDMEMTESTEXPEI_FILECODE                      (0x1023)
#define MEM_AMDMEMORYHOBINFOPEIMMI3_AMDMEMORYHOBINFOPEIM_FILECODE         (0x1024)

#define MEM_AMDMEMRESTOREPEIV2_MEMRESTOREPEI_FILECODE                     (0x1025)
#define MEM_AMDMEMRESTOREDXEV2_MEMRESTOREDXE_FILECODE                     (0x1026)

#define MEM_AMDMEMORYHOBINFOPEIMRPL_AMDMEMORYHOBINFOPEIM_FILECODE         (0x1027)
#define MEM_AMDMEMSMBIOSV2RPLPEI_MEMSMBIOSV2PEI_FILECODE                  (0x1028)
#define MEM_AMDMEMSTPSP6PEI_AMDMEMSTPSP6PEI_FILECODE                      (0x1029)

#define MEM_AMDMEMFL1DXE_AMDMEMFL1DXE_FILECODE                            (0x102A)
#define MEM_AMDMEMFL1PEI_AMDMEMFL1PEI_FILECODE                            (0x102B)

#define MEM_AMDMEMCHANXLATPEI_MEMCHANXLATPEI_FILECODE                     (0x102C)
#define MEM_AMDMEMCHANXLATDXE_MEMCHANXLATDXE_FILECODE                     (0x1045)
#define MEM_AMDMEMFT6PEI_AMDMEMFT6PEI_FILECODE                            (0x102D)
#define MEM_AMDMEMORYHOBINFOPEIMFT6_AMDMEMORYHOBINFOPEIM_FILECODE         (0x102E)
#define MEM_AMDMEMPPRSMMDRIVER_AMDMEMPPRSMMDRIVER_FILECODE                (0x102F)
#define MEM_AMDMEMSH5DXE_AMDMEMSH5DXE_FILECODE                            (0x1030)
#define MEM_AMDMEMSH5PEI_AMDMEMSH5PEI_FILECODE                            (0x1031)
#define MEM_AMDMEMSMBIOSV2MDNPEI_MEMSMBIOSV2PEI_FILECODE                  (0x1032)
#define MEM_AMDMBISTPEI_AMDMBISTPEI_FILECODE                              (0x1033)

#define MEM_AMDMEMBRHSP5PEI_AMDMEMBRHSP5PEI_FILECODE                      (0x1034)
#define MEM_AMDMEMORYHOBINFOPEIMBRH_AMDMEMORYHOBINFOPEIMBRH_FILECODE      (0x1035)
#define MEM_AMDMEMBRHSP5DXE_AMDMEMBRHSP5DXE_FILECODE                      (0x1036)
#define MEM_AMDMEMSMBIOSV2BRHPEI_MEMSMBIOSV2PEI_FILECODE                  (0x1037)
#define MEM_AMDMBISTBRHPEI_AMDMBISTBRHPEI_FILECODE                        (0x1038)
#define MEM_AMDMEMFP8STXPEI_AMDMEMFP8STXPEI_FILECODE                      (0x1039)
#define MEM_AMDMEMFP8STXDXE_AMDMEMFP8STXDXE_FILECODE                      (0x103A)
#define MEM_AMDMEMORYHOBINFOPEIMV2_AMDMEMORYHOBINFOPEIMV2_FILECODE        (0x103B)
#define MEM_AMDMEMFP11STXHPEI_AMDMEMFP11STXHPEI_FILECODE                (0x103C)
#define MEM_AMDMEMFP11STXHDXE_AMDMEMFP11STXHDXE_FILECODE                (0x103D)
#define MEM_AMDMEMFP8KRKPEI_AMDMEMFP8KRKPEI_FILECODE                      (0x103E)
#define MEM_AMDMEMFP8KRKDXE_AMDMEMFP8KRKDXE_FILECODE                      (0x103F)
#define MEM_AMDMEMSTPSP6DXE_AMDMEMSTPSP6DXE_FILECODE                      (0x1040)

#define MEM_AMDMEMFP8DXE_AMDMEMFP8DXE_FILECODE                            (0x1041)
#define MEM_AMDMEMFP8PEI_AMDMEMFP8PEI_FILECODE                            (0x1042)
#define MEM_AMDMEMORYHOBINFOPEIMPHX_AMDMEMORYHOBINFOPEIM_FILECODE         (0x1043)
#define MEM_AMDMEMSMBIOSV2PHXPEI_MEMSMBIOSV2PEI_FILECODE                  (0x1044)
#define MEM_AMDMEMORYHOBINFOPEIMSTX_AMDMEMORYHOBINFOPEIM_FILECODE         (0x1046)
#define MEM_AMDMEMSMBIOSV2STXPEI_MEMSMBIOSV2PEI_FILECODE                  (0x1047)
#define MEM_AMDMEMORYHOBINFOPEIMSTXH_AMDMEMORYHOBINFOPEIM_FILECODE        (0x1048)
#define MEM_AMDMEMSMBIOSV2STXHPEI_MEMSMBIOSV2PEI_FILECODE                 (0x1049)


// Psp
//0x2000 ~ 0x200F for PSP drivers
#define PSP_AMDPSPPEIV1_AMDPSPPEIV1_FILECODE                              (0x2000)
#define PSP_AMDPSPDXEV1_AMDPSPDXEV1_FILECODE                              (0x2001)
#define PSP_AMDPSPP2CMBOXV1_AMDPSPP2CMBOXV1_FILECODE                      (0x2002)
#define PSP_AMDPSPSMMV1_AMDPSPSMMV1_FILECODE                              (0x2003)
#define PSP_AMDPSPP2CMBOXV1_AMDPSPP2CMBOXCMDV1_FILECODE                   (0x2004)
#define PSP_AMDPSPSMMV1_AMDPSPRESUMEHANDLINGV1_FILECODE                   (0x2005)
#define PSP_AMDPSPSMMV1_AMDPSPRESUMESERVICESV1_FILECODE                   (0x2006)
#define LIBRARY_PSPIDSHOOKLIB_DXE_PSPIDSHOOKLIBDXE_FILECODE               (0x2007)
#define PSP_AMDPSPKVM_AMDPSPKVMKEYBOARD_FILECODE                          (0x2008)
#define PSP_AMDPSPKVM_AMDPSPKVMMOUSE_FILECODE                             (0x2009)
#define PSP_AMDPSPKVM_AMDPSPKVMSIMPLEMOUSE_FILECODE                       (0x200A)
#define PSP_APOBDRV_APOBDUMMYDXE_APOBDUMMYDXE_FILECODE                    (0x200B)
#define PSP_APOBDRV_APOBDUMMYPEI_APOBDUMMYPEI_FILECODE                    (0x200C)
#define PSP_AMDPSPINTRUSIONDETECTION_AMDPSPINTRUSIONDETECTIONPEI_FILECODE (0x200D)
#define PSP_AMDPSPINTRUSIONDETECTION_AMDPSPINTRUSIONDETECTIONDXE_FILECODE (0x200E)
#define PSP_AMDPSPINTRUSIONDETECTION_AMDPSPINTRUSIONDETECTIONSMM_FILECODE (0x200F)


//0x2010 ~ 0x202F for PSP Libs and drivers
#define LIBRARY_AMDPSPBASELIBV1_AMDPSPBASELIBV1_FILECODE                  (0x2010)
#define LIBRARY_AMDPSPMBOXLIBV1_AMDPSPMBOXLIBV1_FILECODE                  (0x2011)
#define PSP_AMDPSPROMARMOR3SMM_AMDPSPROMARMOR3SMM_FILECODE                (0x2012)
#define LIBRARY_AMDPSPDXESMMBUFLIB_AMDPSPDXESMMBUFLIB_FILECODE            (0x2013)
#define LIBRARY_AMDPSPREGBASELIB_AMDPSPREGBASELIB_FILECODE                (0x2014)
#define PSP_AMDPSPASPT_AMDPSPASPT_FILECODE                                (0x2015)

#define PSP_AMDPSPPEIV2_AMDPSPPEIV2_FILECODE                              (0x2020)
#define PSP_AMDPSPDXEV2_AMDPSPDXEV2_FILECODE                              (0x2021)
#define PSP_AMDPSPP2CMBOXV2_AMDPSPP2CMBOXV2_FILECODE                      (0x2022)
#define PSP_AMDPSPSMMV2_AMDPSPSMMV2_FILECODE                              (0x2023)
#define PSP_AMDPSPP2CMBOXV2_AMDPSPFLASHACCSMMHDLR_FILECODE                (0x2024)
#define PSP_AMDPSPDXEV2_AMDFLASHACCDXE_FILECODE                           (0x2025)
#define PSP_AMDPSPP2CMBOXV2_AMDPSPP2CMBOXCMDV2_FILECODE                   (0x2026)
#define PSP_AMDPSPSMMV2_AMDPSPRESUMEHANDLINGV2_FILECODE                   (0x2027)
#define PSP_AMDPSPSMMV2_AMDPSPRESUMESERVICESV2_FILECODE                   (0x2028)
#define PSP_AMDPSPKVM_AMDPSPKVM_FILECODE                                  (0x2029)
#define PSP_AMDPSPP2CMBOXV2_AMDPSPP2CMBOXV2SMMBUFFER_FILECODE             (0x202A)
#define PSP_AMDPSPROMARMORSMM_AMDPSPROMARMORSMM_FILECODE                  (0x202B)
#define PSP_AMDPSPPSBDISABLEPEI_AMDPSPPSBDISABLEPEI_FILECODE              (0x202C)
#define PSP_AMDPSPROMARMOR2SMM_AMDPSPROMARMOR2SMM_FILECODE                (0x202D)
#define PSP_AMDPSPKVM_AMDPSPKVMABSOLUTEMOUSE_FILECODE                     (0x202E)
#define PSP_AMDPSPKVM_AMDPSPKVMKBDTEXTIN_FILECODE                         (0x202F)

//0x2030 ~ 0x203F for PSP V2 Libs
#define LIBRARY_AMDPSPBASELIBV2_AMDPSPBASELIBV2_FILECODE                  (0x2030)
#define LIBRARY_AMDPSPMBOXLIBV2_AMDPSPMBOXLIBV2_FILECODE                  (0x2031)
#define LIBRARY_AMDPSPAPOBLIB_AMDPSPAPOBLIB_FILECODE                      (0x2032)
#define LIBRARY_APOBZPRVLIB_APOBZPRVLIB_FILECODE                          (0x2033)
#define LIBRARY_APOBZPMCMLIB_APOBZPMCMLIB_FILECODE                        (0x2034)
#define LIBRARY_APOBSSPLIB_APOBSSPLIB_FILECODE                            (0x2035)
#define LIBRARY_APOBRNLIB_APOBRNLIB_FILECODE                              (0x2036)
#define LIBRARY_APOBRSLIB_APOBAPCBUPDATES_FILECODE                        (0x2037)
#define LIBRARY_APOBCOMMONSERVICELIBPEI_APOBCOMMONSERVICELIBPEI_FILECODE  (0x2038)
#define LIBRARY_APOBCOMMONSERVICELIBDXE_APOBCOMMONSERVICELIBDXE_FILECODE  (0x2039)
#define LIBRARY_APOBFF3LIB_APOBFF3LIB_FILECODE                            (0x203A)
#define LIBRARY_APOBRMBLIB_APOBRMBLIB_FILECODE                            (0x203B)
#define LIBRARY_APOBRSLIB_APOBRSLIB_FILECODE                              (0x203C)
#define LIBRARY_APOBRPLLIB_APOBRPLLIB_FILECODE                            (0x203D)
#define LIBRARY_APOBPHXLIB_APOBPHXLIB_FILECODE                            (0x203E)
#define LIBRARY_APOBDUMMYLIB_APOBDUMMYLIB_FILECODE                        (0x203F)

//0x2040 ~ 0x204F for PSP common Libs
#define LIBRARY_AMDPSPCOMMONLIBPEI_AMDPSPCOMMONLIBPEI_FILECODE            (0x2040)
#define LIBRARY_AMDPSPCOMMONLIBDXE_AMDPSPCOMMONLIBDXE_FILECODE            (0x2041)
#define LIBRARY_AMDPSPFLASHACCLIBDXE_AMDPSPFLASHACCLIBDXE_FILECODE        (0x2043)
#define LIBRARY_AMDPSPFLASHUPDATELIB_AMDPSPFLASHUPDATELIB_FILECODE        (0x2044)
#define LIBRARY_AMDPSPFLASHACCLIBNULL_AMDPSPFLASHACCLIBNULL_FILECODE      (0x2045)
#define LIBRARY_AMDPSPROMARMORLIB_AMDPSPROMARMORLIB_FILECODE              (0x2046)
#define LIBRARY_AMDPSPROMARMORLIBNULL_AMDPSPROMARMORLIBNULL_FILECODE      (0x2047)
#define LIBRARY_AMDPSPPSBFUSINGLIB_AMDPSPPSBFUSINGLIB_FILECODE            (0x2048)
#define LIBRARY_AMDPSPHSTISTATELIB_AMDPSPHSTISTATELIB_FILECODE            (0x2049)
#define LIBRARY_AMDPSPMMIOLIB_AMDPSPMMIOLIB_FILECODE                      (0x204A)
#define LIBRARY_AMDPSPMMIOLIBSMMISO_AMDPSPMMIOLIB_FILECODE                (0x204B)
#define LIBRARY_AMDPSPREGMUXLIBV2DXE_AMDPSPREGMUXLIBV2_FILECODE           (0x204C)
#define LIBRARY_AMDPSPREGMUXLIBV2DXERT_AMDPSPREGMUXLIBV2_FILECODE         (0x204D)
#define LIBRARY_AMDPSPREGMUXLIBV2NULL_AMDPSPREGMUXLIBV2_FILECODE          (0x204E)
#define LIBRARY_AMDTPM2COMMANDLIB_AMDTPM2COMMANDLIB_FILECODE              (0x204F)


//0x2050 ~ 0x205F Ftpm
#define LIBRARY_AMDPSPFTPMLIB_AMDPSPFTPMLIB_FILECODE                      (0x2050)
#define PSP_AMDPSPFTPMPEI_AMDPSPFTPMPEI_FILECODE                          (0x2051)
#define PSP_AMDPSPFTPMDXE_AMDPSPFTPMDXE_FILECODE                          (0x2052)
#define LIBRARY_AMDHSPFTPMLIB_AMDHSPFTPMLIB_FILECODE                      (0x2053)
#define PSP_AMDHSPFTPMPEI_AMDHSPFTPMPEI_FILECODE                          (0x2054)
#define PSP_AMDHSPFTPMDXE_AMDHSPFTPMDXE_FILECODE                          (0x2055)
#define LIBRARY_HSPLIB_HSPLIB_FILECODE                                    (0x2056)
#define PSP_PLUTONSECURITYPROCESSOR_PLUTONSECURITYPROCESSOR_FILECODE      (0x2057)
#define PSP_AMDHSPCONFIGPEI_AMDHSPCONFIGPEI_FILECODE                      (0x2058)
#define PSP_PLUTONSECURITYPROCESSORV2_PLUTONSECURITYPROCESSORV2_FILECODE  (0x2059)

//0x2060 ~ 0x206F APOB
#define PSP_APOBDRV_APOBZPRVPEI_APOBZPRVPEI_FILECODE                      (0x2060)
#define PSP_APOBDRV_APOBZPRVDXE_APOBZPRVDXE_FILECODE                      (0x2061)
#define PSP_APOBDRV_APOBZPMCMPEI_APOBMCMPEI_FILECODE                      (0x2062)
#define PSP_APOBDRV_APOBZPMCMDXE_APOBMCMDXE_FILECODE                      (0x2063)
#define PSP_APOBDRV_APOBSSPPEI_APOBSSPPEI_FILECODE                        (0x2064)
#define PSP_APOBDRV_APOBSSPDXE_APOBSSPDXE_FILECODE                        (0x2065)
#define PSP_APOBDRV_APOBRNPEI_APOBRNPEI_FILECODE                          (0x2066)
#define PSP_APOBDRV_APOBRNDXE_APOBRNDXE_FILECODE                          (0x2067)
#define PSP_APOBDRV_APOBFF3PEI_APOBFF3PEI_FILECODE                        (0x2068)
#define PSP_APOBDRV_APOBFF3DXE_APOBFF3DXE_FILECODE                        (0x2069)
#define PSP_APOBDRV_APOBRMBPEI_APOBRMBPEI_FILECODE                        (0x206A)
#define PSP_APOBDRV_APOBRMBDXE_APOBRMBDXE_FILECODE                        (0x206B)
#define PSP_APOBDRV_APOBRSPEI_APOBRSPEI_FILECODE                          (0x206C)
#define PSP_APOBDRV_APOBRSDXE_APOBRSDXE_FILECODE                          (0x206D)
#define PSP_APOBDRV_APOBRPLPEI_APOBRPLPEI_FILECODE                        (0x206E)
#define PSP_APOBDRV_APOBRPLDXE_APOBRPLDXE_FILECODE                        (0x206F)


#define PSP_AMDPSPDTPMPEI_AMDPSPDTPMPEI_FILECODE                          (0x2070)
#define PSP_APOBDRV_APOBPHXPEI_APOBPHXPEI_FILECODE                        (0x2071)
#define PSP_APOBDRV_APOBPHXDXE_APOBPHXDXE_FILECODE                        (0x2072)
#define PSP_APOBDRV_APOBMDNPEI_APOBMDNPEI_FILECODE                        (0x2073)
#define PSP_APOBDRV_APOBMDNDXE_APOBMDNDXE_FILECODE                        (0x2074)
#define PSP_APOBDRV_APOBBRHPEI_APOBBRHPEI_FILECODE                        (0x2075)
#define PSP_APOBDRV_APOBBRHDXE_APOBBRHDXE_FILECODE                        (0x2076)
#define PSP_APOBDRV_APOBSTXKRKPEI_APOBSTXKRKPEI_FILECODE                  (0x2077)
#define PSP_APOBDRV_APOBSTXKRKDXE_APOBSTXKRKDXE_FILECODE                  (0x2078)
#define PSP_APOBDRV_APOBMI3PEI_APOBMI3PEI_FILECODE                        (0x2079)
#define PSP_APOBDRV_APOBMI3DXE_APOBMI3DXE_FILECODE                        (0x207A)
#define PSP_APOBDRV_APOBSTXHPEI_APOBSTXHPEI_FILECODE                      (0x207B)
#define PSP_APOBDRV_APOBSTXHDXE_APOBSTXHDXE_FILECODE                      (0x207C)

#define LIBRARY_APOBGNRLIB_APOBGNRLIB_FILECODE                            (0x207D)
#define PSP_APOBDRV_APOBGNRPEI_APOBGNRPEI_FILECODE                        (0x207E)
#define PSP_APOBDRV_APOBGNRDXE_APOBGNRDXE_FILECODE                        (0x207F)

//0x2080~0x20FF for IDS
#define LIBRARY_AMDIDSHOOKLIB_AMDIDSHOOKLIB_FILECODE                         (0x2080)
#define LIBRARY_AMDIDSDEBUGPRINTLIB_AMDIDSDEBUGPRINTLIB_FILECODE             (0x2081)
#define DEBUG_AMDIDSDEBUGPRINTDXE_AMDIDSDEBUGPRINTDXE_FILECODE               (0x2082)
#define DEBUG_AMDIDSDEBUGPRINTPEI_AMDIDSDEBUGPRINTPEI_FILECODE               (0x2083)
#define LIBRARY_AMDIDSDEBUGPRINTLIB_AMDIDSDPRAM_FILECODE                     (0x2085)
#define LIBRARY_AMDIDSDEBUGPRINTLIB_AMDIDSDPREDIRECTIO_FILECODE              (0x2086)
#define LIBRARY_AMDIDSDEBUGPRINTLIB_AMDIDSDPSERIAL_FILECODE                  (0x2087)
#define LIBRARY_DEBUGLIBIDSDP_DEBUGLIBIDSDP_FILECODE                         (0x2088)
#define LIBRARY_IDSDXELIB_IDSDXELIB_FILECODE                                 (0x2089)
#define LIBRARY_IDSLIBNULL_IDSLIBNULL_FILECODE                               (0x208A)
#define LIBRARY_IDSPEILIB_IDSPEILIB_FILECODE                                 (0x208B)
#define LIBRARY_AMDIDSHOOKLIBNULL_AMDIDSHOOKLIBNULL_FILECODE                 (0x208C)
#define LIBRARY_AMDIDSEXTLIBNULL_AMDIDSHOOKEXTLIBNULL_FILECODE               (0x208D)
#define LIBRARY_IDSMISCLIB_IDSMISCLIBCOMMON_FILECODE                         (0x208E)
#define LIBRARY_IDSHOOKLIBTABLENULL_IDSHOOKLIBTABLENULL_FILECODE             (0x208F)
#define LIBRARY_AMDIDSDEBUGPRINTLIB_AMDIDSDPUARTLIB_FILECODE                 (0x2090)
#define LIBRARY_AMDIDSHOOKLIBDXE_AMDIDSHOOKLIB_FILECODE                      (0x2091)
#define LIBRARY_AMDIDSHOOKLIBPEI_AMDIDSHOOKLIB_FILECODE                      (0x2092)
#define LIBRARY_SOCCMNIDSHOOKRSLIB_DXE_SOCCMNIDSHOOKRSLIBDXE_FILECODE        (0x2093)
#define LIBRARY_SOCCMNIDSHOOKMI3LIB_DXE_SOCCMNIDSHOOKMI3LIBDXE_FILECODE      (0x2094)
#define LIBRARY_CCXZEN5BRHIDSHOOKLIB_PEI_CCXZEN5BRHIDSHOOKLIBPEI_FILECODE    (0x2095)
#define LIBRARY_CCXZEN5BRHIDSHOOKLIB_DXE_CCXZEN5BRHIDSCUSTOMPSTATES_FILECODE (0x2096)
#define LIBRARY_CCXZEN5BRHIDSHOOKLIB_DXE_CCXZEN5BRHIDSHOOKLIBDXE_FILECODE    (0x2097)
#define LIBRARY_CCXZEN5BRHIDSHOOKLIB_SMM_CCXZEN5BRHIDSSYNCMSRSMM_FILECODE    (0x2098)
#define LIBRARY_SOCCMNIDSHOOKBRHLIB_DXE_SOCCMNIDSHOOKBRHLIBDXE_FILECODE      (0x2099)
#define LIBRARY_SOCCMNIDSHOOKBRHLIB_PEI_SOCCMNIDSHOOKBRHLIBINTPEI_FILECODE   (0x209A)
#define LIBRARY_FABRICIDSHOOKBRHLIB_PEI_FABRICIDSHOOKBRHLIBPEI_FILECODE      (0x209B)
#define LIBRARY_FABRICIDSHOOKBRHLIB_PEI_FABRICIDSHOOKBRHLIBINTPEI_FILECODE   (0x209C)
#define LIBRARY_CCXIDSHOOKLIB_CCXIDSHOOKLIB_FILECODE                         (0x209D)
#define LIBRARY_AMDEMULATIONAUTODETECTPEILIB_AMDEMULATIONAUTODETECTPEILIB_FILECODE                         (0x209E)
#define LIBRARY_AMDEMULATIONAUTODETECTDXELIB_AMDEMULATIONAUTODETECTDXELIB_FILECODE                         (0x209F)
#define LIBRARY_AMDEMULATIONAUTODETECTLIBNULL_AMDEMULATIONAUTODETECTLIBNULL_FILECODE                       (0x20A0)
#define LIBRARY_IDSMISCLIB_BASEIDSMISCLIB_FILECODE                           (0x20A1)
#define LIBRARY_IDSMISCLIB_SMMIDSMISCLIB_FILECODE                            (0x20A2)

//0x2100 - 0x21FF for MPM
#define MPM_MPMPEI_MPMPEI_FILECODE                                        (0x2100)
#define LIBRARY_MPMMBOXLIB_MPMMBOXLIB_FILECODE                            (0x2101)
#define LIBRARY_MPMLIB_MPMLIB_FILECODE                                    (0x2102)
#define LIBRARY_MPMFUNCLIB_MPMFUNCLIB_FILECODE                            (0x2103)
#define MPM_MPMDXE_MPMDXE_FILECODE                                        (0x2104)
#define MPM_MPMKVM_MPMKVM_FILECODE                                        (0x2105)
#define LIBRARY_MPMDEVLIB_MPMDEVINTLIB_FILECODE                           (0x2106)
#define LIBRARY_MPMDEVLIB_DEVICE_AQUANTIA_G_FILECODE                      (0x2107)
#define LIBRARY_MPMDEVLIB_DEVICE_BCM5762_FILECODE                         (0x2108)
#define LIBRARY_MPMDEVLIB_DEVICE_QCAHASTINGS_FILECODE                     (0x2109)
#define LIBRARY_MPMDEVLIB_DEVICE_RTK8168_FILECODE                         (0x210A)
#define MPM_MPMASF_MPMASFDXE_MPMASFDXE_FILECODE                           (0x210B)
#define MPM_MPMASF_MPMASFPEI_MPMASFPEI_FILECODE                           (0x210C)
#define MPM_MPMBATTERYINFO_MPMBATTERYINFO_FILECODE                        (0x210D)
#define MPM_MPMKVM_MPMKVMABSOLUTEMOUSE_FILECODE                           (0x210E)
#define MPM_MPMKVM_MPMKVMKBDTEXTIN_FILECODE                               (0x210F)
#define MPM_MPMKVM_MPMKVMKEYBOARD_FILECODE                                (0x2110)
#define MPM_MPMKVM_MPMKVMMOUSE_FILECODE                                   (0x2111)
#define MPM_MPMKVM_MPMKVMSIMPLEMOUSE_FILECODE                             (0x2112)
#define MPM_MPMPLDM_MPMPLDMBASE_MPMPLDMBASE_FILECODE                      (0x2113)
#define MPM_MPMPLDM_MPMPLDMBCC_BIOSATTRIBUTEPENDINGVALUETABLE_FILECODE    (0x2114)
#define MPM_MPMPLDM_MPMPLDMBCC_BIOSATTRIBUTETABLE_FILECODE                (0x2115)
#define MPM_MPMPLDM_MPMPLDMBCC_BIOSATTRIBUTEVALUETABLE_FILECODE           (0x2116)
#define MPM_MPMPLDM_MPMPLDMBCC_BIOSSTRINGTABLE_FILECODE                   (0x2117)
#define MPM_MPMPLDM_MPMPLDMBCC_MPMPLDMBCC_FILECODE                        (0x2118)
#define MPM_MPMPLDM_MPMPLDMBCC_SETBIOSTABLESTAGS_FILECODE                 (0x2119)
#define MPM_MPMPLDM_MPMPLDMMONITOR_MPMPLDMSENSOR_FILECODE                 (0x211A)
#define MPM_MPMPLDM_MPMPLDMSMBIOS_MPMPLDMSMBIOS_FILECODE                  (0x211B)
#define MPM_MPMSERIALIODXE_MPMSERIALIODXE_FILECODE                        (0x211C)
#define LIBRARY_MPMBIOSCMDLIB_MPMBIOSCMDLIB_FILECODE                      (0x211D)
#define LIBRARY_MPMDEVLIB_DEVICE_MEDIATEK_FILECODE                        (0x211E)
#define LIBRARY_DASHASFBASELIB_DASHASFBASELIB_FILECODE                    (0x2120)
#define LIBRARY_AMDMPMREGBASELIB_AMDMPMREGBASELIB_FILECODE                (0x2121)

//0x2200 ~ 0x22FF for PSP Libs
#define LIBRARY_APOBMDNLIB_APOBMDNLIB_FILECODE                              (0x2200)
#define LIBRARY_AMDDIRECTORYBASELIB_AMDDIRECTORYBASELIB_FILECODE            (0x2201)
#define LIBRARY_AMDSTBLIB_AMDSTBLIB_FILECODE                                (0x2202)
#define LIBRARY_AMDSTBLIBNULL_AMDSTBLIBNULL_FILECODE                        (0x2203)
#define LIBRARY_APOBBRHLIB_APOBBRHLIB_FILECODE                              (0x2204)
#define LIBRARY_APOBSTXKRKLIB_APOBSTXKRKLIB_FILECODE                        (0x2205)
#define LIBRARY_HSPPRETCGEVENTLOGLIBNULL_HSPPRETCGEVENTLOGLIBNULL_FILECODE  (0x2206)
#define LIBRARY_HSPPRETCGEVENTLOGLIBPEI_HSPPRETCGEVENTLOGLIBPEI_FILECODE    (0x2207)
#define LIBRARY_APOBSTXHLIB_APOBSTXHLIB_FILECODE                            (0x2208)
#define LIBRARY_AMDPSPBARINITLIBV2_AMDPSPBARINITLIBV2_FILECODE              (0x2209)
#define LIBRARY_AMDPSPSFSLIB_AMDPSPSFSLIB_FILECODE                          (0x220A)

// CCX
#define LIBRARY_CCXBISTLIB_CCXBISTLIB_FILECODE                           (0x3001)
#define LIBRARY_CCXMICROCODEPATCHLIB_CCXMICROCODEPATCHLIB_FILECODE       (0x3002)
#define LIBRARY_CCXHALTLIB_CCXHALTLIB_FILECODE                           (0x3003)
#define LIBRARY_CCXBASEX86LIB_CCXBASEX86LIB_FILECODE                     (0x3004)
#define LIBRARY_CCXROLESX86LIB_CCXROLESX86LIB_FILECODE                   (0x3005)
#define LIBRARY_CCXRESETTABLESZPLIB_CCXRESETTABLESZPLIB_FILECODE         (0x3006)
#define CCX_ZEN_CCXZENZPPEI_CCXZENZPCACHEINIT_FILECODE                   (0x3007)
#define CCX_ZEN_CCXZENZPDXE_AMDCCXZENZPDXE_FILECODE                      (0x3008)
#define CCX_ZEN_CCXZENZPPEI_AMDCCXZENZPPEI_FILECODE                      (0x3009)
#define CCX_ZEN_CCXZENZPDXE_CCXZENZPMICROCODEPATCH_FILECODE              (0x300A)
#define LIBRARY_CCXZENZPIDSHOOKLIB_DXE_CCXZENZPIDSHOOKLIBDXE_FILECODE    (0x300B)
#define LIBRARY_CCXZENZPIDSHOOKLIB_DXE_CCXZENZPIDSCUSTOMPSTATES_FILECODE (0x300C)
#define LIBRARY_CCXZENZPIDSHOOKLIB_PEI_CCXZENZPIDSHOOKLIBPEI_FILECODE    (0x300D)
#define LIBRARY_CCXPSTATESZENZPLIB_CCXPSTATESZENZPLIB_FILECODE           (0x300E)
#define CCX_ZEN_CCXZENZPDXE_CCXZENZPACPISERVICESDXE_FILECODE             (0x300F)
#define CCX_ZEN_CCXZENZPDXE_CCXZENZPC6_FILECODE                          (0x3010)
#define CCX_ZEN_CCXZENZPDXE_CCXZENZPCPB_FILECODE                         (0x3011)
#define CCX_ZEN_CCXZENZPDXE_CCXZENZPPREFETCH_FILECODE                    (0x3012)
#define CCX_ZEN_CCXZENZPDXE_CCXZENZPSMBIOSDXE_FILECODE                   (0x3013)
#define CCX_ZEN_CCXZENZPPEI_CCXZENZPDOWNCOREINIT_FILECODE                (0x3014)
#define LIBRARY_CCXSMBIOSZENZPLIB_CCXSMBIOSZENZPLIB_FILECODE             (0x3015)
#define LIBRARY_CCXSMBIOSZENZPLIB_CCXSMBIOSZENZPCOMMONLIB_FILECODE       (0x3016)
#define CCX_ZEN_CCXZENZPPEI_CCXZENZPBRANDSTRING_FILECODE                 (0x3017)
#define CCX_ZEN_CCXZENZPSMM_AMDCCXZENZPSMM_FILECODE                      (0x3018)
#define LIBRARY_CCXZENZPIDSHOOKLIB_SMM_CCXZENZPIDSHOOKLIBSMM_FILECODE    (0x3019)
#define LIBRARY_CCXPSPLIB_CCXPSPLIB_FILECODE                             (0x301A)
#define LIBRARY_CCXZENZPIDSHOOKLIB_SMM_CCXZENZPIDSSYNCMSRSMM_FILECODE    (0x301B)
#define LIBRARY_FABRICIDSHOOKPHXLIB_DXE_FABRICIDSHOOKPHXLIBDXE_FILECODE  (0x301C)
#define LIBRARY_BASECORELOGICALIDX86LIB_BASECORELOGICALIDX86LIB_FILECODE (0x301D)
#define LIBRARY_CCXZENRVIDSHOOKLIB_DXE_CCXZENRVIDSHOOKLIBDXE_FILECODE    (0x301E)
#define LIBRARY_CCXZENRVIDSHOOKLIB_DXE_CCXZENRVIDSCUSTOMPSTATES_FILECODE (0x301F)
#define LIBRARY_CCXZENRVIDSHOOKLIB_PEI_CCXZENRVIDSHOOKLIBPEI_FILECODE    (0x3020)
#define LIBRARY_CCXZENRVIDSHOOKLIB_SMM_CCXZENRVIDSSYNCMSRSMM_FILECODE    (0x3021)
#define LIBRARY_CCXZENRVIDSHOOKLIB_SMM_CCXZENRVIDSHOOKLIBSMM_FILECODE    (0x3022)

#define LIBRARY_CCXRESETTABLESRVLIB_CCXRESETTABLESRVLIB_FILECODE         (0x3023)
#define CCX_ZEN_CCXZENRVPEI_CCXZENRVCACHEINIT_FILECODE                   (0x3024)
#define CCX_ZEN_CCXZENRVDXE_AMDCCXZENRVDXE_FILECODE                      (0x3025)
#define CCX_ZEN_CCXZENRVPEI_AMDCCXZENRVPEI_FILECODE                      (0x3026)
#define CCX_ZEN_CCXZENRVDXE_CCXZENRVMICROCODEPATCH_FILECODE              (0x3027)
#define LIBRARY_CCXPSTATESZENRVLIB_CCXPSTATESZENRVLIB_FILECODE           (0x3028)
#define CCX_ZEN_CCXZENRVDXE_CCXZENRVACPISERVICESDXE_FILECODE             (0x3029)
#define CCX_ZEN_CCXZENRVDXE_CCXZENRVC6_FILECODE                          (0x302A)
#define CCX_ZEN_CCXZENRVDXE_CCXZENRVCPB_FILECODE                         (0x302B)
#define CCX_ZEN_CCXZENRVDXE_CCXZENRVPREFETCH_FILECODE                    (0x302C)
#define CCX_ZEN_CCXZENRVDXE_CCXZENRVSMBIOSDXE_FILECODE                   (0x302D)
#define CCX_ZEN_CCXZENRVPEI_CCXZENRVDOWNCOREINIT_FILECODE                (0x302E)
#define LIBRARY_CCXSMBIOSZENRVLIB_CCXSMBIOSZENRVLIB_FILECODE             (0x302F)
#define LIBRARY_CCXSMBIOSZENRVLIB_CCXSMBIOSZENRVCOMMONLIB_FILECODE       (0x3030)
#define CCX_ZEN_CCXZENRVPEI_CCXZENRVBRANDSTRING_FILECODE                 (0x3031)
#define CCX_ZEN_CCXZENRVSMM_AMDCCXZENRVSMM_FILECODE                      (0x3032)
#define LIBRARY_CCXSETMCAZPLIB_CCXSETMCAZPLIB_FILECODE                   (0x3033)
#define CCX_ZEN_CCXZENZPDXE_CCXZENZPACPIRAS_FILECODE                     (0x3034)
#define LIBRARY_CCXSETMCARVLIB_CCXSETMCARVLIB_FILECODE                   (0x3037)
#define LIBRARY_CCXSETMMIOCFGBASELIB_CCXSETMMIOCFGBASELIB_FILECODE       (0x3038)
#define LIBRARY_DXECCXBASEX86SERVICESLIB_CCXBASEX86SERVICESDXE_FILECODE  (0x3039)
#define CCX_VH_PEI_AMDCCXVHPEI_FILECODE                                  (0x303A)
#define CCX_VH_PEI_CCXVHBRANDSTRING_FILECODE                             (0x303B)
#define CCX_VH_PEI_CCXVHCACHEINIT_FILECODE                               (0x303C)
#define CCX_VH_PEI_CCXVHDOWNCOREINIT_FILECODE                            (0x303D)
#define CCX_VH_DXE_AMDCCXVHDXE_FILECODE                                  (0x303E)
#define CCX_VH_DXE_CCXVHACPIRAS_FILECODE                                 (0x303F)
#define CCX_VH_DXE_CCXVHACPISERVICESDXE_FILECODE                         (0x3040)
#define CCX_VH_DXE_CCXVHC6_FILECODE                                      (0x3041)
#define CCX_VH_DXE_CCXVHCPB_FILECODE                                     (0x3042)
#define CCX_VH_DXE_CCXVHMICROCODEPATCH_FILECODE                          (0x3043)
#define CCX_VH_DXE_CCXVHPREFETCH_FILECODE                                (0x3044)
#define CCX_VH_DXE_CCXVHSMBIOSDXE_FILECODE                               (0x3045)
#define CCX_VH_SMM_AMDCCXVHSMM_FILECODE                                  (0x3046)
#define LIBRARY_CCXPSTATESVHLIB_CCXPSTATESVHLIB_FILECODE                 (0x3047)
#define LIBRARY_CCXRESETTABLESVHLIB_CCXRESETTABLESVHLIB_FILECODE         (0x3048)
#define LIBRARY_CCXSETMCAVHLIB_CCXSETMCAVHLIB_FILECODE                   (0x3049)
#define LIBRARY_CCXSMBIOSVHLIB_CCXSMBIOSVHCOMMONLIB_FILECODE             (0x304A)
#define LIBRARY_CCXSMBIOSVHLIB_CCXSMBIOSVHLIB_FILECODE                   (0x304B)
#define LIBRARY_CCXVHIDSHOOKLIB_PEI_CCXVHIDSHOOKLIBPEI_FILECODE          (0x304C)
#define LIBRARY_CCXVHIDSHOOKLIB_DXE_CCXVHIDSHOOKLIBDXE_FILECODE          (0x304D)
#define LIBRARY_CCXVHIDSHOOKLIB_DXE_CCXVHIDSCUSTOMPSTATES_FILECODE       (0x304E)
#define LIBRARY_CCXVHIDSHOOKLIB_SMM_CCXVHIDSHOOKLIBSMM_FILECODE          (0x304F)
#define LIBRARY_CCXVHIDSHOOKLIB_SMM_CCXVHIDSSYNCMSRSMM_FILECODE          (0x3050)
#define LIBRARY_CCXAPICVHLIB_CCXAPICVHLIB_FILECODE                       (0x3051)
#define CCX_ZEN3_PEI_AMDCCXZEN3PEI_FILECODE                              (0x3052)
#define CCX_ZEN3_PEI_CCXZEN3BRANDSTRING_FILECODE                         (0x3053)
#define CCX_ZEN3_PEI_CCXZEN3CACHEINIT_FILECODE                           (0x3054)
#define CCX_ZEN3_PEI_CCXZEN3DOWNCOREINIT_FILECODE                        (0x3055)
#define CCX_ZEN3_DXE_AMDCCXZEN3DXE_FILECODE                              (0x3056)
#define CCX_ZEN3_DXE_CCXZEN3ACPIRAS_FILECODE                             (0x3057)
#define CCX_ZEN3_DXE_CCXZEN3ACPISERVICESDXE_FILECODE                     (0x3058)
#define CCX_ZEN3_DXE_CCXZEN3C6_FILECODE                                  (0x3059)
#define CCX_ZEN3_DXE_CCXZEN3CPB_FILECODE                                 (0x305A)
#define CCX_ZEN3_DXE_CCXZEN3MICROCODEPATCH_FILECODE                      (0x305B)
#define CCX_ZEN3_DXE_CCXZEN3PREFETCH_FILECODE                            (0x305C)
#define CCX_ZEN3_DXE_CCXZEN3SMBIOSDXE_FILECODE                           (0x305D)
#define CCX_ZEN3_SMM_AMDCCXZEN3SMM_FILECODE                              (0x305E)
#define LIBRARY_CCXPSTATESZEN3LIB_CCXPSTATESZEN3LIB_FILECODE             (0x305F)
#define LIBRARY_CCXRESETTABLESZEN3LIB_CCXRESETTABLESZEN3LIB_FILECODE     (0x3060)
#define LIBRARY_CCXSETMCAZEN3LIB_CCXSETMCAZEN3LIB_FILECODE               (0x3061)
#define LIBRARY_CCXSMBIOSZEN3LIB_CCXSMBIOSZEN3COMMONLIB_FILECODE         (0x3062)
#define LIBRARY_CCXSMBIOSZEN3LIB_CCXSMBIOSZEN3LIB_FILECODE               (0x3063)
#define LIBRARY_CCXZEN3IDSHOOKLIB_PEI_CCXZEN3IDSHOOKLIBPEI_FILECODE      (0x3064)
#define LIBRARY_CCXZEN3IDSHOOKLIB_DXE_CCXZEN3IDSHOOKLIBDXE_FILECODE      (0x3065)
#define LIBRARY_CCXAPICZEN3LIB_CCXAPICZEN3LIB_FILECODE                   (0x3066)
#define LIBRARY_CCXZEN3IDSHOOKLIB_DXE_CCXZEN3IDSCUSTOMPSTATES_FILECODE   (0x3067)
#define LIBRARY_CCXZEN3IDSHOOKLIB_SMM_CCXZEN3IDSHOOKLIBSMM_FILECODE      (0x3068)
#define LIBRARY_CCXZEN3IDSHOOKLIB_SMM_CCXZEN3IDSSYNCMSRSMM_FILECODE      (0x3069)
#define CCX_VH_PEI_CCXVHRNBRANDSTRING_FILECODE                           (0x306A)
#define CCX_VH_DXE_CCXVHSVM_FILECODE                                     (0x306B)
#define LIBRARY_CCXTSCTIMERLIB_BASETSCTIMERLIB_FILECODE                  (0x306C)
#define LIBRARY_CCXTSCTIMERLIB_DXETSCTIMERLIB_FILECODE                   (0x306D)
#define LIBRARY_CCXTSCTIMERLIB_PEITSCTIMERLIB_FILECODE                   (0x306E)
#define LIBRARY_CCXTSCTIMERLIB_TSCTIMERLIBSHARE_FILECODE                 (0x306F)
#define CCX_ZEN3_PEI_RMB_AMDCCXZEN3RMBPEI_FILECODE                       (0x3070)
#define CCX_ZEN3_DXE_RMB_AMDCCXZEN3RMBDXE_FILECODE                       (0x3071)
#define LIBRARY_CCXRESETTABLESZEN3V2LIB_CCXRESETTABLESZEN3V2LIB_FILECODE (0x3072)
#define CCX_ZEN4_PEI_AMDCCXZEN4PEI_FILECODE                              (0x3073)
#define CCX_ZEN4_PEI_CCXZEN4BRANDSTRING_FILECODE                         (0x3074)
#define CCX_ZEN4_PEI_CCXZEN4CACHEINIT_FILECODE                           (0x3075)
#define CCX_ZEN4_PEI_CCXZEN4DOWNCOREINIT_FILECODE                        (0x3076)
#define CCX_ZEN4_DXE_AMDCCXZEN4DXE_FILECODE                              (0x3077)
#define CCX_ZEN4_DXE_CCXZEN4ACPIRAS_FILECODE                             (0x3078)
#define CCX_ZEN4_DXE_CCXZEN4ACPISERVICESDXE_FILECODE                     (0x3079)
#define CCX_ZEN4_DXE_CCXZEN4C6_FILECODE                                  (0x307A)
#define CCX_ZEN4_DXE_CCXZEN4CPB_FILECODE                                 (0x307B)
#define CCX_ZEN4_DXE_CCXZEN4MICROCODEPATCH_FILECODE                      (0x307C)
#define CCX_ZEN4_DXE_CCXZEN4PREFETCH_FILECODE                            (0x307D)
#define CCX_ZEN4_DXE_CCXZEN4SMBIOSDXE_FILECODE                           (0x307E)
#define CCX_ZEN4_SMM_AMDCCXZEN4SMM_FILECODE                              (0x307F)
#define LIBRARY_CCXZEN4RSIDSHOOKLIB_PEI_CCXZEN4RSIDSHOOKLIBPEI_FILECODE  (0x3080)
#define LIBRARY_CCXZEN4RSIDSHOOKLIB_DXE_CCXZEN4RSIDSHOOKLIBDXE_FILECODE  (0x3081)
#define LIBRARY_CCXZEN4RSIDSHOOKLIB_DXE_CCXZEN4RSIDSCUSTOMPSTATES_FILECODE (0x3082)
#define LIBRARY_CCXZEN4RSIDSHOOKLIB_SMM_CCXZEN4RSIDSHOOKLIBSMM_FILECODE   (0x3083)
#define LIBRARY_CCXZEN4RSIDSHOOKLIB_SMM_CCXZEN4RSIDSSYNCMSRSMM_FILECODE  (0x3084)
#define LIBRARY_CCXPSTATESZEN4LIB_CCXPSTATESZEN4LIB_FILECODE             (0x3085)
#define LIBRARY_CCXRESETTABLESZEN4LIB_CCXRESETTABLESZEN4LIB_FILECODE     (0x3086)
#define LIBRARY_CCXSETMCAZEN4LIB_CCXSETMCAZEN4LIB_FILECODE               (0x3087)
#define LIBRARY_CCXSMBIOSZEN4LIB_CCXSMBIOSZEN4COMMONLIB_FILECODE         (0x3088)
#define LIBRARY_CCXSMBIOSZEN4LIB_CCXSMBIOSZEN4LIB_FILECODE               (0x3089)
#define LIBRARY_CCXAPICZEN4LIB_CCXAPICZEN4LIB_FILECODE                   (0x308A)
#define LIBRARY_CCXROLESZEN4LIB_CCXROLESZEN4LIB_FILECODE                 (0x308B)
#define LIBRARY_CCXZEN4MI3IDSHOOKLIB_PEI_CCXZEN4MI3IDSHOOKLIBPEI_FILECODE (0x308C)
#define LIBRARY_CCXZEN4MI3IDSHOOKLIB_DXE_CCXZEN4MI3IDSHOOKLIBDXE_FILECODE (0x308D)
#define LIBRARY_CCXZEN4MI3IDSHOOKLIB_DXE_CCXZEN4MI3IDSCUSTOMPSTATES_FILECODE (0x308E)
#define LIBRARY_CCXZEN4MI3IDSHOOKLIB_SMM_CCXZEN4MI3IDSHOOKLIBSMM_FILECODE (0x308F)
#define LIBRARY_CCXZEN4MI3IDSHOOKLIB_SMM_CCXZEN4MI3IDSSYNCMSRSMM_FILECODE (0x3090)
#define CCX_ZEN4_DXE_MI3_AMDCCXZEN4MI3DXE_FILECODE                       (0x3091)
#define CCX_ZEN4_PEI_MI3_AMDCCXZEN4MI3PEI_FILECODE                       (0x3092)

#define CCX_ZEN5_PEI_AMDCCXZEN5PEI_FILECODE                              (0x3093)
#define CCX_ZEN5_PEI_CCXZEN5BRANDSTRING_FILECODE                         (0x3094)
#define CCX_ZEN5_PEI_CCXZEN5CACHEINIT_FILECODE                           (0x3095)
#define CCX_ZEN5_PEI_CCXZEN5DOWNCOREINIT_FILECODE                        (0x3096)
#define CCX_ZEN5_DXE_AMDCCXZEN5DXE_FILECODE                              (0x3097)
#define CCX_ZEN5_DXE_CCXZEN5ACPIRAS_FILECODE                             (0x3098)
#define CCX_ZEN5_DXE_CCXZEN5ACPISERVICESDXE_FILECODE                     (0x3099)
#define CCX_ZEN5_DXE_CCXZEN5SMBIOSDXE_FILECODE                           (0x309A)
#define CCX_ZEN5_SMM_AMDCCXZEN5SMM_FILECODE                              (0x309B)
#define LIBRARY_CCXRESETTABLESZEN5LIB_CCXRESETTABLESZEN5LIB_FILECODE     (0x309C)
#define LIBRARY_CCXSETMCAZEN5LIB_CCXSETMCAZEN5LIB_FILECODE               (0x309D)
#define LIBRARY_CCXSMBIOSZEN5LIB_CCXSMBIOSZEN5COMMONLIB_FILECODE         (0x309E)
#define LIBRARY_CCXSMBIOSZEN5LIB_CCXSMBIOSZEN5LIB_FILECODE               (0x309F)
#define LIBRARY_CCXAPICZEN5LIB_CCXAPICZEN5LIB_FILECODE                   (0x30A0)
#define LIBRARY_CCXROLESZEN5LIB_CCXROLESZEN5LIB_FILECODE                 (0x30A1)
#define LIBRARY_CCXPSTATESZEN5LIB_CCXPSTATESZEN5LIB_FILECODE             (0x30A2)
#define LIBRARY_CCXZEN5STXKRKIDSHOOKLIB_DXE_CCXZEN5STXKRKIDSCUSTOMPSTATES_FILECODE   (0x30A3)
#define LIBRARY_CCXZEN5STXKRKIDSHOOKLIB_DXE_CCXZEN5STXKRKIDSHOOKLIBDXE_FILECODE      (0x30A4)
#define LIBRARY_CCXZEN5STXKRKIDSHOOKLIB_PEI_CCXZEN5STXKRKIDSHOOKLIBPEI_FILECODE      (0x30A5)
#define LIBRARY_CCXZEN5STXKRKIDSHOOKLIB_SMM_CCXZEN5STXKRKIDSHOOKLIBSMM_FILECODE      (0x30A6)
#define LIBRARY_CCXZEN5STXKRKIDSHOOKLIB_SMM_CCXZEN5STXKRKIDSSYNCMSRSMM_FILECODE      (0x30A7)
#define CCX_VH_DXE_FF3_AMDCCXVHFF3DXE_FILECODE                           (0x30A8)
#define CCX_VH_DXE_FF3_CCXVHACPISERVICESFF3DXE_FILECODE                  (0x30A9)
#define CCX_VH_DXE_MDN_AMDCCXVHMDNDXE_FILECODE                           (0x30AA)
#define CCX_VH_DXE_MDN_CCXVHACPISERVICESMDNDXE_FILECODE                  (0x30AB)
#define CCX_VH_DXE_RN_AMDCCXVHRNDXE_FILECODE                             (0x30AC)
#define CCX_VH_PEI_CCXVHC6_FILECODE                                      (0x30AD)
#define CCX_VH_PEI_CCXVHCPB_FILECODE                                     (0x30AE)
#define CCX_VH_PEI_CCXVHPREFETCH_FILECODE                                (0x30AF)
#define CCX_VH_PEI_FF3_AMDCCXVHFF3PEI_FILECODE                           (0x30B0)
#define CCX_VH_PEI_FF3_CCXVHFF3DOWNCOREINIT_FILECODE                     (0x30B1)
#define CCX_VH_PEI_MDN_AMDCCXVHMDNPEI_FILECODE                           (0x30B2)
#define CCX_VH_PEI_MDN_CCXVHMDNDOWNCOREINIT_FILECODE                     (0x30B3)
#define CCX_VH_PEI_RN_CCXVHRNBRANDSTRING_FILECODE                        (0x30B4)
#define LIBRARY_CCXRESETTABLESVHV2LIB_CCXRESETTABLESVHV2LIB_FILECODE     (0x30B5)
#define LIBRARY_CCXROLESVHLIB_CCXROLESVHLIB_FILECODE                     (0x30B6)
#define LIBRARY_CCXVHMDNIDSHOOKLIB_DXE_CCXVHMDNIDSCUSTOMPSTATES_FILECODE (0x30B7)
#define LIBRARY_CCXVHMDNIDSHOOKLIB_DXE_CCXVHMDNIDSHOOKLIBDXE_FILECODE    (0x30B8)
#define LIBRARY_CCXVHMDNIDSHOOKLIB_DXE_CCXVHMDNIDSSYNCMSR_FILECODE       (0x30B9)
#define LIBRARY_CCXVHMDNIDSHOOKLIB_PEI_CCXVHMDNIDSHOOKLIBPEI_FILECODE    (0x30BA)
#define LIBRARY_CCXVHMDNIDSHOOKLIB_SMM_CCXVHMDNIDSHOOKLIBSMM_FILECODE    (0x30BB)
#define LIBRARY_CCXVHMDNIDSHOOKLIB_SMM_CCXVHMDNIDSSYNCMSRSMM_FILECODE    (0x30BC)
#define LIBRARY_FABRICIDSHOOKMDNNULLLIB_PEI_FABRICIDSHOOKMDNNULLLIBPEI_FILECODE      (0x30BD)
#define LIBRARY_FABRICRESOURCEMANAGERMI3LIB_FABRICRESOURCEINIT3_FILECODE             (0x30BE)
#define LIBRARY_FABRICRESOURCEMANAGERMI3LIB_FABRICRESOURCEINITBASEDONNV3_FILECODE    (0x30BF)
#define LIBRARY_FABRICRESOURCEMANAGERMI3LIB_FABRICRESOURCEMANAGER3_FILECODE          (0x30C0)

#define LIBRARY_CCXIDSHOOKLIB_GNR_DXE_CCXZEN5GNRIDSCUSTOMPSTATES_FILECODE            (0x30C1)
#define LIBRARY_CCXIDSHOOKLIB_GNR_DXE_CCXZEN5GNRIDSHOOKLIBDXE_FILECODE               (0x30C2)
#define LIBRARY_CCXIDSHOOKLIB_GNR_DXE_CCXZEN5GNRIDSSYNCMSR_FILECODE                  (0x30C3)
#define LIBRARY_CCXIDSHOOKLIB_GNR_PEI_CCXZEN5GNRIDSHOOKLIBPEI_FILECODE               (0x30C4)
#define LIBRARY_CCXIDSHOOKLIB_GNR_SMM_CCXZEN5GNRIDSHOOKLIBSMM_FILECODE               (0x30C5)
#define LIBRARY_CCXIDSHOOKLIB_GNR_SMM_CCXZEN5GNRIDSSYNCMSRSMM_FILECODE               (0x30C6)

#define LIBRARY_DXECCXCORETOPOLOGYSERVICESV3ONV2LIB_CORETOPOLOGYSERVICESV3ONV2DXE_FILECODE    (0x30C7)
#define LIBRARY_PEICCXCORETOPOLOGYSERVICESV3ONV2LIB_CORETOPOLOGYSERVICESV3ONV2PEI_FILECODE    (0x30C8)
#define LIBRARY_DXECCXCORETOPOLOGYSERVICESV3STXKRKLIB_CORETOPOLOGYSERVICESV3STXKRKDXE_FILECODE   (0x30C9)
#define LIBRARY_PEICCXCORETOPOLOGYSERVICESV3STXKRKLIB_CORETOPOLOGYSERVICESV3STXKRKPEI_FILECODE   (0x30CA)
#define LIBRARY_DXECCXCORETOPOLOGYSERVICESV3BRHLIB_CORETOPOLOGYSERVICESV3BRHDXE_FILECODE      (0x30CB)
#define LIBRARY_PEICCXCORETOPOLOGYSERVICESV3BRHLIB_CORETOPOLOGYSERVICESV3BRHPEI_FILECODE      (0x30CC)
#define CCX_ZEN4_PEI_MI3_CCXZEN4MI3DOWNCOREINIT_FILECODE                                      (0x30CD)
#define LIBRARY_DXECCXCORETOPOLOGYSERVICESV3GNRLIB_CORETOPOLOGYSERVICESV3GNRDXE_FILECODE      (0x30CE)
#define LIBRARY_PEICCXCORETOPOLOGYSERVICESV3GNRLIB_CORETOPOLOGYSERVICESV3GNRPEI_FILECODE      (0x30CF)
#define LIBRARY_DXECCXCORETOPOLOGYSERVICESV3MI3LIB_CORETOPOLOGYSERVICESV3MI3DXE_FILECODE      (0x30D0)
#define LIBRARY_DXECCXCORETOPOLOGYSERVICESV3STXHLIB_CORETOPOLOGYSERVICESV3STXHDXE_FILECODE    (0x30D1)
#define LIBRARY_PEICCXCORETOPOLOGYSERVICESV3STXHLIB_CORETOPOLOGYSERVICESV3STXHPEI_FILECODE    (0x30D2)
#define LIBRARY_DXECCXCORETOPOLOGYSERVICESV3KRKLIB_CORETOPOLOGYSERVICESV3KRKDXE_FILECODE      (0x30D3)
#define LIBRARY_PEICCXCORETOPOLOGYSERVICESV3KRKLIB_CORETOPOLOGYSERVICESV3KRKPEI_FILECODE      (0x30D4)

#define LIBRARY_CCXZEN5STXHIDSHOOKLIB_DXE_CCXZEN5STXHIDSCUSTOMPSTATES_FILECODE                (0x30D5)
#define LIBRARY_CCXZEN5STXHIDSHOOKLIB_DXE_CCXZEN5STXHIDSHOOKLIBDXE_FILECODE                   (0x30D6)
#define LIBRARY_CCXZEN5STXHIDSHOOKLIB_PEI_CCXZEN5STXHIDSHOOKLIBPEI_FILECODE                   (0x30D7)
#define LIBRARY_CCXZEN5STXHIDSHOOKLIB_SMM_CCXZEN5STXHIDSHOOKLIBSMM_FILECODE                   (0x30D8)
#define LIBRARY_CCXZEN5STXHIDSHOOKLIB_SMM_CCXZEN5STXHIDSSYNCMSRSMM_FILECODE                   (0x30D9)
#define LIBRARY_CCXZEN5STXHIDSHOOKLIB_DXE_CCXZEN5STXHIDSSYNCMSR_FILECODE                      (0x30DA)

#define LIBRARY_CCXZEN5STXKRKIDSHOOKLIB_DXE_CCXZEN5STXKRKIDSSYNCMSR_FILECODE                  (0x30DF)
#define LIBRARY_CCXZEN5SHPIDSHOOKLIB_PEI_CCXZEN5SHPIDSHOOKLIBPEI_FILECODE                     (0x30DB)
#define LIBRARY_CCXZEN5SHPIDSHOOKLIB_DXE_CCXZEN5SHPIDSHOOKLIBDXE_FILECODE                     (0x30DC)
#define LIBRARY_CCXZEN5SHPIDSHOOKLIB_DXE_CCXZEN5SHPIDSCUSTOMPSTATES_FILECODE                  (0x30DD)
#define LIBRARY_CCXZEN5SHPIDSHOOKLIB_DXE_CCXZEN5SHPISDSYNCMSR_FILECODE                        (0x30DE)
#define LIBRARY_CCXZEN5SHPIDSHOOKLIB_SMM_CCXZEN5SHPIDSHOOKLIBSMM_FILECODE                     (0x3143)
#define LIBRARY_CCXZEN5SHPIDSHOOKLIB_SMM_CCXZEN5SHPIDSSYNCMSRSMM_FILECODE                     (0x3144)

#define LIBRARY_CCXZEN4STPIDSHOOKLIB_PEI_CCXZEN4STPIDSHOOKLIBPEI_FILECODE            (0x3100)
#define LIBRARY_CCXZEN4STPIDSHOOKLIB_DXE_CCXZEN4STPIDSHOOKLIBDXE_FILECODE            (0x3101)
#define LIBRARY_CCXZEN4STPIDSHOOKLIB_DXE_CCXZEN4STPIDSCUSTOMPSTATES_FILECODE         (0x3102)
#define LIBRARY_CCXZEN4STPIDSHOOKLIB_DXE_CCXZEN4STPIDSSYNCMSR_FILECODE               (0x3103)
#define LIBRARY_CCXZEN4STPIDSHOOKLIB_SMM_CCXZEN4STPIDSHOOKLIBSMM_FILECODE            (0x3104)
#define LIBRARY_CCXZEN4STPIDSHOOKLIB_SMM_CCXZEN4STPIDSSYNCMSRSMM_FILECODE            (0x3105)

#define LIBRARY_PEICORETOPOLOGYV3LIB_PEICORETOPOLOGYV3LIB_FILECODE (0x3106)
#define LIBRARY_DXECORETOPOLOGYV3LIB_DXECORETOPOLOGYV3LIB_FILECODE (0x3107)

#define LIBRARY_DXECCXCCDREORDERZEN5LIB_DXECCXCCDREORDERZEN5LIB_FILECODE (0x3108)

#define LIBRARY_CCXZEN5BRHDXELIB_CCXZEN5BRHDXELIB_FILECODE               (0x3109)
#define LIBRARY_CCXZEN5GNRDXELIB_CCXZEN5GNRDXELIB_FILECODE               (0x310A)
#define LIBRARY_CCXZEN5STXKRKDXELIB_CCXZEN5STXKRKDXELIB_FILECODE         (0x310B)
#define LIBRARY_CCXZEN5STXHDXELIB_CCXZEN5STXHDXELIB_FILECODE             (0x310C)
#define LIBRARY_CCXZEN4DXELIB_CCXZEN4DXELIB_FILECODE                     (0x310E)
#define LIBRARY_CCXZEN4RSDXELIB_CCXZEN4RSDXELIB_FILECODE                 (0x310F)
#define LIBRARY_CCXZEN5SEGRMPBRHDXELIB_CCXZEN5SEGRMPBRHDXELIB_FILECODE   (0x3110)
#define LIBRARY_CCXZEN5SEGRMPLIB_CCXZEN5SEGRMPDXELIB_FILECODE            (0x3111)
#define LIBRARY_DXECCXCORETOPOLOGYSERVICESV3RSLIB_CORETOPOLOGYSERVICESV3RSDXE_FILECODE     (0x3112)
#define LIBRARY_PEICCXCORETOPOLOGYSERVICESV3RSLIB_CORETOPOLOGYSERVICESV3RSPEI_FILECODE     (0x3113)
#define CCX_ZEN4_PEI_IA32_APSTARTUPFIXUPS_FILECODE                       (0x3114)
#define CCX_ZEN4_PEI_X64_APSTARTUPFIXUPS_FILECODE                        (0x3115)
#define CCX_ZEN5_PEI_IA32_APSTARTUPFIXUPS_FILECODE                       (0x3116)
#define CCX_ZEN5_PEI_X64_APSTARTUPFIXUPS_FILECODE                        (0x3117)
#define CCX_ZEN4_DXE_AMDMCAZEN4DXE_AMDMCAZEN4DXE_FILECODE                (0x3118)
#define CCX_ZEN5_DXE_AMDMCAZEN5DXE_AMDMCAZEN5DXE_FILECODE                (0x3119)

// 0x30E0 ~ 0x30FF for Excavator
#define LIBRARY_APCOMMUNICATIONXVLIB_CPUAPCOMM_FILECODE                  (0x30E0)
#define LIBRARY_APCOMMUNICATIONXVLIB_CPUAPCOMMAPIC_FILECODE              (0x30E1)
#define LIBRARY_CCXSMBIOSXVLIB_CCXSMBIOSXVLIB_FILECODE                   (0x30E2)
#define LIBRARY_CCXSMBIOSXVLIB_CCXSMBIOSXVCOMMONLIB_FILECODE             (0x30E3)
#define LIBRARY_CCXBTCLIB_CCXXVBTCLIB_FILECODE                           (0x30E4)
#define LIBRARY_CCXDOWNCOREXVLIB_CCXDOWNCOREXVLIB_FILECODE               (0x30E5)
#define LIBRARY_CCXPSTATESXVLIB_CCXPSTATESXVLIB_FILECODE                 (0x30E6)

#define CCX_XV_CCXXVPEI_AMDCCXXVPEI_FILECODE                             (0x30EA)
#define CCX_XV_CCXXVPEI_CCXXVBRANDSTRING_FILECODE                        (0x30EB)
#define CCX_XV_CCXXVPEI_CCXXVGENERALSERVICES_FILECODE                    (0x30EC)
#define CCX_XV_CCXXVPEI_CCXXVBRWORKAROUNDTABLE_FILECODE                  (0x30ED)
#define CCX_XV_CCXXVPEI_CCXXVHALT_FILECODE                               (0x30EE)
#define CCX_XV_CCXXVPEI_CCXXVLOCALAPIC_FILECODE                          (0x30EF)
#define CCX_XV_CCXXVPEI_CCXXVMICROCODEPATCH_FILECODE                     (0x30F0)
#define CCX_XV_CCXXVPEI_CCXXVPMFEATURES_FILECODE                         (0x30F1)
#define CCX_XV_CCXXVPEI_CCXXVPOWERMGMT_FILECODE                          (0x30F2)
#define CCX_XV_CCXXVPEI_CCXXVPSTATESERVICESPEI_FILECODE                  (0x30F3)
#define CCX_XV_CCXXVPEI_CCXXVSYNCMTRR_FILECODE                           (0x30F4)
#define CCX_XV_CCXXVPEI_CCXXVTABLES_FILECODE                             (0x30F5)

#define CCX_XV_CCXXVDXE_AMDCCXXVDXE_FILECODE                             (0x30F6)
#define CCX_XV_CCXXVDXE_CCXXVACPISERVICESDXE_FILECODE                    (0x30F8)
#define CCX_XV_CCXXVDXE_CCXXVGENERALSERVICESDXE_FILECODE                 (0x30F9)
#define CCX_XV_CCXXVDXE_CCXXVPMFEATURESDXE_FILECODE                      (0x30FA)
#define CCX_XV_CCXXVDXE_CCXXVSMBIOSDXE_FILECODE                          (0x30FB)
#define CCX_XV_CCXXVDXE_CCXXVPREFETCH_FILECODE                           (0x30FC)

#define CCX_XV_CCXXVSMM_AMDCCXXVSMM_FILECODE                             (0x30FD)

//Universal
#define UNIVERSAL_SMBIOS_AMDSMBIOSDXE_FILECODE                           (0x8000)
#define UNIVERSAL_SMBIOS_AMDSMBIOSTYPE4_FILECODE                         (0x8001)
#define UNIVERSAL_SMBIOS_AMDSMBIOSTYPE7_FILECODE                         (0x8002)
#define UNIVERSAL_SMBIOS_AMDSMBIOSTYPE11_FILECODE                        (0x8003)
#define UNIVERSAL_SMBIOS_AMDSMBIOSTYPE16_FILECODE                        (0x8004)
#define UNIVERSAL_SMBIOS_AMDSMBIOSTYPE17_FILECODE                        (0x8005)
#define UNIVERSAL_SMBIOS_AMDSMBIOSTYPE18_FILECODE                        (0x8006)
#define UNIVERSAL_SMBIOS_AMDSMBIOSTYPE19_FILECODE                        (0x8007)
#define UNIVERSAL_SMBIOS_AMDSMBIOSTYPE20_FILECODE                        (0x8008)
#define UNIVERSAL_SMBIOS_AMDSMBIOSTYPE40_FILECODE                        (0x8009)
#define UNIVERSAL_ACPI_AMDACPIDXE_FILECODE                               (0x8010)
#define UNIVERSAL_ACPI_AMDACPICPUSSDT_FILECODE                           (0x8011)
#define UNIVERSAL_ACPI_AMDACPICRAT_FILECODE                              (0x8012)
#define UNIVERSAL_ACPI_AMDACPICDIT_FILECODE                              (0x8013)
#define UNIVERSAL_ACPI_AMDACPISRAT_FILECODE                              (0x8014)
#define UNIVERSAL_ACPI_AMDACPISLIT_FILECODE                              (0x8015)
#define UNIVERSAL_ACPI_AMDACPIMSCT_FILECODE                              (0x8016)
#define UNIVERSAL_AMDMEMORYHOBINFOPEIM_AMDMEMORYHOBINFOPEIM_FILECODE     (0x8017)
#define UNIVERSAL_ACPI_AMDACPIPCCT_FILECODE                              (0x8019)
#define UNIVERSAL_ACPI_AMDACPINOTIFYCPU_FILECODE                         (0x801A)
#define UNIVERSAL_AMDIGPUVGACONTROLDXE_AMDIGPUVGACONTROLDXE_FILECODE     (0x801B)
#define UNIVERSAL_AMDSMMCOMMUNICATION_AMDSMMCOMMUNICATION_FILECODE       (0x801C)
#define UNIVERSAL_ACPI_AMDACPIHMAT_FILECODE                              (0x801D)
#define UNIVERSAL_ACPI_AMDACPIHMATSERVICE_FILECODE                       (0x801E)
#define UNIVERSAL_ACPI_AMDACPIBDAT_FILECODE                              (0x801F)
#define UNIVERSAL_ACPI_AMDACPIHETEROSSDT_FILECODE                        (0x8020)
#define UNIVERSAL_AMDAUTODYNAMICCOMMAND_AMDAUTODYNAMICCOMMAND_FILECODE   (0x8021)
#define UNIVERSAL_AMDAUTODYNAMICCOMMAND_AMDAUTOTOOL_FILECODE             (0x8022)
#define UNIVERSAL_AMDAUTODYNAMICCOMMAND_AMDAUTOTOOLAPP_FILECODE          (0x8023)
#define UNIVERSAL_AMDAUTODYNAMICCOMMAND_SHELLFUNCTION_FILECODE           (0x8024)

// Fabric
#define FABRIC_ZP_FABRICZPPEI_AMDFABRICZPPEI_FILECODE                    (0xDF00)
#define FABRIC_ZP_FABRICZPPEI_FABRICPSTATESERVICESPEI_FILECODE           (0xDF01)
#define FABRIC_ZP_FABRICZPDXE_AMDFABRICZPDXE_FILECODE                    (0xDF02)
#define LIBRARY_FABRICREGISTERACCSOC15LIB_FABRICREGISTERACCSOC15LIB_FILECODE  (0xDF03)
#define FABRIC_ZP_FABRICZPPEI_FABRICCCMINIT_FILECODE                     (0xDF04)
#define FABRIC_ZP_FABRICZPPEI_FABRICCSINIT_FILECODE                      (0xDF05)
#define FABRIC_ZP_FABRICZPPEI_FABRICDFCLKGATINGINIT_FILECODE             (0xDF06)
#define FABRIC_ZP_FABRICZPPEI_FABRICPIERASINIT_FILECODE                  (0xDF07)
#define FABRIC_ZP_FABRICZPDXE_FABRICPIEPWRMGMT_FILECODE                  (0xDF08)
#define LIBRARY_FABRICIDSHOOKZPLIB_PEI_FABRICIDSHOOKZPLIBPEI_FILECODE    (0xDF09)
#define LIBRARY_FABRICIDSHOOKZPLIB_DXE_FABRICIDSHOOKZPLIBDXE_FILECODE    (0xDF0A)
#define FABRIC_ZP_FABRICZPPEI_FABRICSCRUBINIT_FILECODE                   (0xDF0B)
#define FABRIC_ZP_FABRICZPPEI_FABRICIOMSINIT_FILECODE                    (0xDF0C)
#define LIBRARY_FABRICRESOURCEMANAGERDF1LIB_FABRICRESOURCEMANAGER1_FILECODE (0xDF0D)
#define FABRIC_ZP_FABRICZPDXE_FABRICACPISLIT_FILECODE                    (0xDF0E)
#define FABRIC_ZP_FABRICZPDXE_FABRICACPICDIT_FILECODE                    (0xDF0F)
#define FABRIC_ZP_FABRICZPDXE_FABRICACPIDISTANCEINFO_FILECODE            (0xDF10)
#define FABRIC_ZP_FABRICZPDXE_FABRICACPISRAT_FILECODE                    (0xDF11)
#define FABRIC_ZP_FABRICZPDXE_FABRICACPICRAT_FILECODE                    (0xDF12)
#define FABRIC_ZP_FABRICZPDXE_FABRICREADYTOBOOT_FILECODE                 (0xDF13)
#define LIBRARY_BASESOCKETLOGICALIDRVDIELIB_BASESOCKETLOGICALIDRVDIELIB_FILECODE (0xDF14)
#define LIBRARY_BASESOCKETLOGICALIDZPDIELIB_BASESOCKETLOGICALIDZPDIELIB_FILECODE (0xDF15)


#define FABRIC_RV_FABRICRVPEI_AMDFABRICRVPEI_FILECODE                    (0xDF16)
#define FABRIC_RV_FABRICRVPEI_FABRICPSTATESERVICESPEI_FILECODE           (0xDF17)
#define FABRIC_RV_FABRICRVDXE_AMDFABRICRVDXE_FILECODE                    (0xDF18)
#define FABRIC_RV_FABRICRVPEI_FABRICCCMINIT_FILECODE                     (0xDF19)
#define FABRIC_RV_FABRICRVPEI_FABRICCSINIT_FILECODE                      (0xDF1A)
#define FABRIC_RV_FABRICRVPEI_FABRICDFCLKGATINGINIT_FILECODE             (0xDF1B)
#define FABRIC_RV_FABRICRVPEI_FABRICPIERASINIT_FILECODE                  (0xDF1C)
#define FABRIC_RV_FABRICRVDXE_FABRICPIEPWRMGMT_FILECODE                  (0xDF1D)
#define LIBRARY_FABRICIDSHOOKRVLIB_PEI_FABRICIDSHOOKRVLIBPEI_FILECODE    (0xDF1E)
#define LIBRARY_FABRICIDSHOOKRVLIB_DXE_FABRICIDSHOOKRVLIBDXE_FILECODE    (0xDF1F)
#define FABRIC_RV_FABRICRVPEI_FABRICSCRUBINIT_FILECODE                   (0xDF20)
#define FABRIC_RV_FABRICRVPEI_FABRICIOMSINIT_FILECODE                    (0xDF21)
#define FABRIC_RV_FABRICRVDXE_FABRICACPISLIT_FILECODE                    (0xDF23)
#define FABRIC_RV_FABRICRVDXE_FABRICACPICDIT_FILECODE                    (0xDF24)
#define FABRIC_RV_FABRICRVDXE_FABRICACPIDISTANCEINFO_FILECODE            (0xDF25)
#define FABRIC_RV_FABRICRVDXE_FABRICACPISRAT_FILECODE                    (0xDF26)
#define FABRIC_RV_FABRICRVDXE_FABRICACPICRAT_FILECODE                    (0xDF27)
#define FABRIC_RV_FABRICRVDXE_FABRICREADYTOBOOT_FILECODE                 (0xDF28)
#define LIBRARY_PEIFABRICRESOURCESIZEFOREACHRBLIB_PEIFABRICRESOURCESIZEFOREACHRBLIB_FILECODE (0xDF29)
#define LIBRARY_DXEFABRICRESOURCESIZEFOREACHRBLIB_DXEFABRICRESOURCESIZEFOREACHRBLIB_FILECODE (0xDF2A)
#define LIBRARY_FABRICRESOURCEMANAGERDF1LIB_FABRICRESOURCEINITBASEDONNV1_FILECODE (0xDF2B)
#define LIBRARY_FABRICRESOURCEMANAGERDF1LIB_FABRICRESOURCEINIT1_FILECODE (0xDF2C)
#define FABRIC_ZP_FABRICZPSMM_AMDFABRICZPSMM_FILECODE                    (0xDF2D)
#define FABRIC_RV_FABRICRVSMM_AMDFABRICRVSMM_FILECODE                    (0xDF31)
#define FABRIC_ZP_FABRICZPDXE_FABRICACPIDOMAININFO_FILECODE              (0xDF32)
#define FABRIC_RV_FABRICRVDXE_FABRICACPIDOMAININFO_FILECODE              (0xDF33)
#define LIBRARY_FABRICRESOURCEMANAGERLIB_FABRICRESOURCEREPORTTOGCDLIB_FILECODE (0xDF34)
#define FABRIC_ZP_FABRICZPDXE_FABRICACPIMSCT_FILECODE                    (0xDF35)
#define LIBRARY_FABRICRESOURCEMANAGERDF3LIB_FABRICRESOURCEINITBASEDONNV3_FILECODE (0xDF36)
#define LIBRARY_FABRICRESOURCEMANAGERDF3LIB_FABRICRESOURCEINIT3_FILECODE   (0xDF37)
#define LIBRARY_FABRICRESOURCEMANAGERDF3LIB_FABRICRESOURCEMANAGER3_FILECODE (0xDF38)
#define LIBRARY_FABRICREGISTERACCDF3LIB_FABRICREGISTERACCDF3LIB_FILECODE (0xDF39)
#define LIBRARY_BASEFABRICTOPOLOGYSSPLIB_BASEFABRICTOPOLOGYSSPLIB_FILECODE (0xDF3A)
#define FABRIC_SSP_FABRICSSPPEI_AMDFABRICSSPPEI_FILECODE                 (0xDF3B)
#define FABRIC_SSP_FABRICSSPPEI_FABRICCCMINIT_FILECODE                   (0xDF3C)
#define FABRIC_SSP_FABRICSSPPEI_FABRICCSINIT_FILECODE                    (0xDF3D)
#define FABRIC_SSP_FABRICSSPPEI_FABRICDFCLKGATINGINIT_FILECODE           (0xDF3E)
#define FABRIC_SSP_FABRICSSPPEI_FABRICPIERASINIT_FILECODE                (0xDF3F)
#define FABRIC_SSP_FABRICSSPPEI_FABRICSCRUBINIT_FILECODE                 (0xDF40)
#define FABRIC_SSP_FABRICSSPDXE_AMDFABRICSSPDXE_FILECODE                 (0xDF41)
#define FABRIC_SSP_FABRICSSPDXE_FABRICACPICDIT_FILECODE                  (0xDF42)
#define FABRIC_SSP_FABRICSSPDXE_FABRICACPICRAT_FILECODE                  (0xDF43)
#define FABRIC_SSP_FABRICSSPDXE_FABRICACPIDISTANCEINFO_FILECODE          (0xDF44)
#define FABRIC_SSP_FABRICSSPDXE_FABRICACPIDOMAININFO_FILECODE            (0xDF45)
#define FABRIC_SSP_FABRICSSPDXE_FABRICACPIMSCT_FILECODE                  (0xDF46)
#define FABRIC_SSP_FABRICSSPDXE_FABRICACPISLIT_FILECODE                  (0xDF47)
#define FABRIC_SSP_FABRICSSPDXE_FABRICACPISRAT_FILECODE                  (0xDF48)
#define FABRIC_SSP_FABRICSSPDXE_FABRICREADYTOBOOT_FILECODE               (0xDF49)
#define FABRIC_SSP_FABRICSSPSMM_AMDFABRICSSPSMM_FILECODE                 (0xDF4A)
#define LIBRARY_FABRICIDSHOOKSSPLIB_PEI_FABRICIDSHOOKSSPLIBPEI_FILECODE  (0xDF4B)
#define LIBRARY_FABRICIDSHOOKSSPLIB_DXE_FABRICIDSHOOKSSPLIBDXE_FILECODE  (0xDF4C)
#define LIBRARY_BASESOCKETLOGICALIDSSPDIELIB_BASESOCKETLOGICALIDSSPDIELIB_FILECODE (0xDF4D)
#define LIBRARY_PEIFABRICTOPOLOGYSERVICES2LIB_FABRICTOPOLOGYSERVICES2PEI_FILECODE (0xDF4E)
#define LIBRARY_DXEFABRICTOPOLOGYSERVICES2LIB_FABRICTOPOLOGYSERVICES2DXE_FILECODE (0xDF4F)
#define LIBRARY_SMMFABRICTOPOLOGYSERVICES2LIB_FABRICTOPOLOGYSERVICES2SMM_FILECODE (0xDF50)
#define LIBRARY_DXEFABRICRESOURCEMANAGERSERVICESLIB_FABRICRESOURCEMANAGERSERVICESDXE_FILECODE (0xDF51)
#define LIBRARY_PEIFABRICRESOURCEMANAGERSERVICESLIB_FABRICRESOURCEMANAGERSERVICESPEI_FILECODE (0xDF52)
#define LIBRARY_FABRICRESOURCEREPORTTOGCDLIB_FABRICRESOURCEREPORTTOGCDLIB_FILECODE (0xDF53)
#define LIBRARY_FABRICRESOURCEREPORTTOGCDNULLLIB_FABRICRESOURCEREPORTTOGCDNULLLIB_FILECODE (0xDF54)
#define LIBRARY_PEIFABRICSOCSPECIFICSERVICESSSPLIB_PEIFABRICSOCSPECIFICSERVICESSSPLIB_FILECODE (0xDF55)
#define LIBRARY_PEIFABRICSOCSPECIFICSERVICESMTSLIB_PEIFABRICSOCSPECIFICSERVICESMTSLIB_FILECODE (0xDF56)
#define LIBRARY_BASEFABRICTOPOLOGYRNLIB_BASEFABRICTOPOLOGYRNLIB_FILECODE (0xDF57)
#define FABRIC_RN_FABRICRNPEI_AMDFABRICRNPEI_FILECODE                  (0xDF58)
#define FABRIC_RN_FABRICRNPEI_FABRICCCMINIT_FILECODE                   (0xDF59)
#define FABRIC_RN_FABRICRNPEI_FABRICCSINIT_FILECODE                    (0xDF5A)
#define FABRIC_RN_FABRICRNPEI_FABRICDFCLKGATINGINIT_FILECODE           (0xDF5B)
#define FABRIC_RN_FABRICRNPEI_FABRICPIERASINIT_FILECODE                (0xDF5C)
#define FABRIC_RN_FABRICRNPEI_FABRICSCRUBINIT_FILECODE                 (0xDF5D)
#define FABRIC_RN_FABRICRNDXE_AMDFABRICRNDXE_FILECODE                  (0xDF5E)
#define FABRIC_RN_FABRICRNDXE_FABRICACPICDIT_FILECODE                  (0xDF5F)
#define FABRIC_RN_FABRICRNDXE_FABRICACPICRAT_FILECODE                  (0xDF60)
#define FABRIC_RN_FABRICRNDXE_FABRICACPIDISTANCEINFO_FILECODE          (0xDF61)
#define FABRIC_RN_FABRICRNDXE_FABRICACPIDOMAININFO_FILECODE            (0xDF62)
#define FABRIC_RN_FABRICRNDXE_FABRICACPIMSCT_FILECODE                  (0xDF63)
#define FABRIC_RN_FABRICRNDXE_FABRICACPISLIT_FILECODE                  (0xDF64)
#define FABRIC_RN_FABRICRNDXE_FABRICACPISRAT_FILECODE                  (0xDF65)
#define FABRIC_RN_FABRICRNDXE_FABRICREADYTOBOOT_FILECODE               (0xDF66)
#define FABRIC_RN_FABRICRNSMM_AMDFABRICRNSMM_FILECODE                  (0xDF67)
#define LIBRARY_FABRICIDSHOOKRNLIB_PEI_FABRICIDSHOOKRNLIBPEI_FILECODE  (0xDF68)
#define LIBRARY_FABRICIDSHOOKRNLIB_DXE_FABRICIDSHOOKRNLIBDXE_FILECODE  (0xDF69)
#define LIBRARY_BASESOCKETLOGICALIDRNDIELIB_BASESOCKETLOGICALIDRNDIELIB_FILECODE (0xDF6A)
#define FABRIC_RN_FABRICRNDXE_FABRICPIEPWRMGMT_FILECODE                (0xDF6B)

#define FABRIC_BA_FABRICBAPEI_AMDFABRICBAPEI_FILECODE                  (0xDF6D)
#define FABRIC_BA_FABRICBAPEI_FABRICCCMINIT_FILECODE                   (0xDF6E)
#define FABRIC_BA_FABRICBAPEI_FABRICCSINIT_FILECODE                    (0xDF6F)
#define FABRIC_BA_FABRICBAPEI_FABRICDFCLKGATINGINIT_FILECODE           (0xDF70)
#define FABRIC_BA_FABRICBAPEI_FABRICPIERASINIT_FILECODE                (0xDF71)
#define FABRIC_BA_FABRICBAPEI_FABRICSCRUBINIT_FILECODE                 (0xDF72)
#define FABRIC_BA_FABRICBADXE_AMDFABRICBADXE_FILECODE                  (0xDF73)
#define FABRIC_BA_FABRICBADXE_FABRICACPICDIT_FILECODE                  (0xDF74)
#define FABRIC_BA_FABRICBADXE_FABRICACPICRAT_FILECODE                  (0xDF75)
#define FABRIC_BA_FABRICBADXE_FABRICACPIDISTANCEINFO_FILECODE          (0xDF76)
#define FABRIC_BA_FABRICBADXE_FABRICACPIDOMAININFO_FILECODE            (0xDF77)
#define FABRIC_BA_FABRICBADXE_FABRICACPIMSCT_FILECODE                  (0xDF78)
#define FABRIC_BA_FABRICBADXE_FABRICACPISLIT_FILECODE                  (0xDF79)
#define FABRIC_BA_FABRICBADXE_FABRICACPISRAT_FILECODE                  (0xDF7A)
#define FABRIC_BA_FABRICBADXE_FABRICREADYTOBOOT_FILECODE               (0xDF7B)
#define FABRIC_BA_FABRICBASMM_AMDFABRICBASMM_FILECODE                  (0xDF7C)
#define LIBRARY_FABRICIDSHOOKBALIB_PEI_FABRICIDSHOOKBALIBPEI_FILECODE  (0xDF7D)
#define LIBRARY_BASEFABRICTOPOLOGYBALIB_BASEFABRICTOPOLOGYBALIB_FILECODE (0xDF7E)
#define LIBRARY_FABRICREGISTERACCBALIB_FABRICREGISTERACCBALIB_FILECODE (0xDF7F)
#define LIBRARY_FABRICRESOURCEMANAGERBALIB_FABRICRESOURCEINIT3_FILECODE (0xDF80)
#define LIBRARY_FABRICRESOURCEMANAGERBALIB_FABRICRESOURCEINITBASEDONNV3_FILECODE (0xDF81)
#define LIBRARY_FABRICRESOURCEMANAGERBALIB_FABRICRESOURCEMANAGER3_FILECODE (0xDF82)
#define LIBRARY_FABRICWDTBALIB_FABRICWDTBALIB_FILECODE                 (0xDF83)
#define LIBRARY_PEIFABRICSOCSPECIFICSERVICESBALIB_PEIFABRICSOCSPECIFICSERVICESBALIB_FILECODE (0xDF84)
#define LIBRARY_SOCCMNIDSHOOKBALIB_PEI_SOCCMNIDSHOOKBALIBPEI_FILECODE  (0xDF85)
#define LIBRARY_SOCCMNBALIB_DXE_SOCCMNIDSHOOKBALIBDXE_FILECODE         (0xDF86)
#define LIBRARY_MI200FABRICLIB_MI200FABRICLIB_FILECODE                 (0xDF87)
#define LIBRARY_FABRICRESOURCEMANAGERDF3RMBLIB_FABRICRESOURCEINIT3_FILECODE   (0xDF88)
#define LIBRARY_FABRICRESOURCEMANAGERDF3RMBLIB_FABRICRESOURCEMANAGER3_FILECODE (0xDF89)
#define LIBRARY_FABRICRESOURCEMANAGERDF3RMBLIB_FABRICRESOURCEINITBASEDONNV3_FILECODE (0xDF8A)

#define FABRIC_RMB_FABRICRMBPEI_AMDFABRICRMBPEI_FILECODE                  (0xDF90)
#define FABRIC_RMB_FABRICRMBPEI_FABRICCCMINIT_FILECODE                    (0xDF91)
#define FABRIC_RMB_FABRICRMBPEI_FABRICCSINIT_FILECODE                     (0xDF92)
#define FABRIC_RMB_FABRICRMBPEI_FABRICDFCLKGATINGINIT_FILECODE            (0xDF93)
#define FABRIC_RMB_FABRICRMBPEI_FABRICPIERASINIT_FILECODE                 (0xDF94)
#define FABRIC_RMB_FABRICRMBPEI_FABRICSCRUBINIT_FILECODE                  (0xDF95)
#define FABRIC_RMB_FABRICRMBDXE_FABRICPIEPWRMGMT_FILECODE                 (0xDF96)
#define FABRIC_RMB_FABRICRMBDXE_AMDFABRICRMBDXE_FILECODE                  (0xDF97)
#define FABRIC_RMB_FABRICRMBDXE_FABRICACPICDIT_FILECODE                   (0xDF98)
#define FABRIC_RMB_FABRICRMBDXE_FABRICACPICRAT_FILECODE                   (0xDF99)
#define FABRIC_RMB_FABRICRMBDXE_FABRICACPIDISTANCEINFO_FILECODE           (0xDF9A)
#define FABRIC_RMB_FABRICRMBDXE_FABRICACPIDOMAININFO_FILECODE             (0xDF9B)
#define FABRIC_RMB_FABRICRMBDXE_FABRICACPIMSCT_FILECODE                   (0xDF9C)
#define FABRIC_RMB_FABRICRMBDXE_FABRICACPISLIT_FILECODE                   (0xDF9D)
#define FABRIC_RMB_FABRICRMBDXE_FABRICACPISRAT_FILECODE                   (0xDF9E)
#define FABRIC_RMB_FABRICRMBDXE_FABRICREADYTOBOOT_FILECODE                (0xDF9F)
#define FABRIC_RMB_FABRICRMBSMM_AMDFABRICRMBSMM_FILECODE                  (0xDFA0)
#define LIBRARY_FABRICIDSHOOKRMBLIB_PEI_FABRICIDSHOOKRMBLIBPEI_FILECODE   (0xDFA1)
#define LIBRARY_FABRICIDSHOOKRMBLIB_DXE_FABRICIDSHOOKRMBLIBDXE_FILECODE   (0xDFA2)
#define LIBRARY_BASESOCKETLOGICALIDRMBDIELIB_BASESOCKETLOGICALIDRMBDIELIB_FILECODE (0xDFA3)
#define LIBRARY_BASEFABRICTOPOLOGYRMBLIB_BASEFABRICTOPOLOGYRMBLIB_FILECODE (0xDFA4)

#define FABRIC_RS_FABRICRSPEI_AMDFABRICRSPEI_FILECODE                      (0xDFA5)
#define FABRIC_RS_FABRICRSPEI_FABRICCCMINIT_FILECODE                       (0xDFA6)
#define FABRIC_RS_FABRICRSPEI_FABRICCSINIT_FILECODE                        (0xDFA7)
#define FABRIC_RS_FABRICRSPEI_FABRICDFCLKGATINGINIT_FILECODE               (0xDFA8)
#define FABRIC_RS_FABRICRSPEI_FABRICPIERASINIT_FILECODE                    (0xDFA9)
#define FABRIC_RS_FABRICRSPEI_FABRICSCRUBINIT_FILECODE                     (0xDFAA)
#define FABRIC_RS_FABRICRSDXE_AMDFABRICRSDXE_FILECODE                      (0xDFAB)
#define FABRIC_RS_FABRICRSDXE_FABRICACPICDIT_FILECODE                      (0xDFAC)
#define FABRIC_RS_FABRICRSDXE_FABRICACPICRAT_FILECODE                      (0xDFAD)
#define FABRIC_RS_FABRICRSDXE_FABRICACPIDISTANCEINFO_FILECODE              (0xDFAE)
#define FABRIC_RS_FABRICRSDXE_FABRICACPIDOMAININFO_FILECODE                (0xDFAF)
#define FABRIC_RS_FABRICRSDXE_FABRICACPIMSCT_FILECODE                      (0xDFB0)
#define FABRIC_RS_FABRICRSDXE_FABRICACPISLIT_FILECODE                      (0xDFB1)
#define FABRIC_RS_FABRICRSDXE_FABRICACPISRAT_FILECODE                      (0xDFB2)
#define FABRIC_RS_FABRICRSDXE_FABRICREADYTOBOOT_FILECODE                   (0xDFB3)
#define FABRIC_RS_FABRICRSSMM_AMDFABRICRSSMM_FILECODE                      (0xDFB4)
#define LIBRARY_FABRICIDSHOOKRSLIB_PEI_FABRICIDSHOOKRSLIBPEI_FILECODE      (0xDFB5)
#define LIBRARY_FABRICIDSHOOKRSLIB_DXE_FABRICIDSHOOKRSLIBDXE_FILECODE      (0xDFB6)
#define LIBRARY_BASEFABRICTOPOLOGYRSLIB_BASEFABRICTOPOLOGYRSLIB_FILECODE   (0xDFB7)
#define LIBRARY_BASESOCKETLOGICALIDRSDIELIB_BASESOCKETLOGICALIDRSDIELIB_FILECODE (0xDFB8)
#define FABRIC_RS_FABRICRSDXE_FABRICACPIHMAT_FILECODE                      (0xDFB9)

#define FABRIC_PHX_FABRICPHXPEI_AMDFABRICPHXPEI_FILECODE                  (0xDFBB)
#define FABRIC_PHX_FABRICPHXPEI_FABRICCCMINIT_FILECODE                    (0xDFBC)
#define FABRIC_PHX_FABRICPHXPEI_FABRICCSINIT_FILECODE                     (0xDFBD)
#define FABRIC_PHX_FABRICPHXPEI_FABRICDFCLKGATINGINIT_FILECODE            (0xDFBE)
#define FABRIC_PHX_FABRICPHXPEI_FABRICPIERASINIT_FILECODE                 (0xDFBF)
#define FABRIC_PHX_FABRICPHXPEI_FABRICSCRUBINIT_FILECODE                  (0xDFC0)
#define FABRIC_PHX_FABRICPHXDXE_FABRICPIEPWRMGMT_FILECODE                 (0xDFC1)
#define FABRIC_PHX_FABRICPHXDXE_AMDFABRICPHXDXE_FILECODE                  (0xDFC2)
#define FABRIC_PHX_FABRICPHXDXE_FABRICACPICDIT_FILECODE                   (0xDFC3)
#define FABRIC_PHX_FABRICPHXDXE_FABRICACPICRAT_FILECODE                   (0xDFC4)
#define FABRIC_PHX_FABRICPHXDXE_FABRICACPIDISTANCEINFO_FILECODE           (0xDFC5)
#define FABRIC_PHX_FABRICPHXDXE_FABRICACPIDOMAININFO_FILECODE             (0xDFC6)
#define FABRIC_PHX_FABRICPHXDXE_FABRICACPIMSCT_FILECODE                   (0xDFC7)
#define FABRIC_PHX_FABRICPHXDXE_FABRICACPISLIT_FILECODE                   (0xDFC8)
#define FABRIC_PHX_FABRICPHXDXE_FABRICACPISRAT_FILECODE                   (0xDFC9)
#define FABRIC_PHX_FABRICPHXDXE_FABRICREADYTOBOOT_FILECODE                (0xDFCA)
#define FABRIC_PHX_FABRICPHXSMM_AMDFABRICPHXSMM_FILECODE                  (0xDFCB)
#define LIBRARY_BASEFABRICTOPOLOGYPHXLIB_BASEFABRICTOPOLOGYPHXLIB_FILECODE (0xDFCC)
#define LIBRARY_FABRICRESOURCEMANAGERPHXLIB_FABRICRESOURCEINIT3_FILECODE  (0xDFCD)
#define LIBRARY_FABRICRESOURCEMANAGERPHXLIB_FABRICRESOURCEINITBASEDONNV3_FILECODE (0xDFCE)
#define LIBRARY_FABRICRESOURCEMANAGERPHXLIB_FABRICRESOURCEMANAGER3_FILECODE (0xDFCF)
#define LIBRARY_FABRICREGISTERACCPHXLIB_FABRICREGISTERACCPHXLIB_FILECODE    (0xDFD0)

#define FABRIC_RPL_FABRICRPLPEI_AMDFABRICRPLPEI_FILECODE                   (0xDFD2)
#define FABRIC_RPL_FABRICRPLPEI_FABRICCSINIT_FILECODE                      (0xDFD4)
#define FABRIC_RPL_FABRICRPLPEI_FABRICDFCLKGATINGINIT_FILECODE             (0xDFD5)
#define FABRIC_RPL_FABRICRPLPEI_FABRICPIERASINIT_FILECODE                  (0xDFD6)
#define FABRIC_RPL_FABRICRPLDXE_FABRICPIEPWRMGMT_FILECODE                  (0xDFD7)
#define FABRIC_RPL_FABRICRPLDXE_AMDFABRICRPLDXE_FILECODE                   (0xDFD8)
#define FABRIC_RPL_FABRICRPLDXE_FABRICACPICDIT_FILECODE                    (0xDFD9)
#define FABRIC_RPL_FABRICRPLDXE_FABRICACPICRAT_FILECODE                    (0xDFDA)
#define FABRIC_RPL_FABRICRPLDXE_FABRICACPIDISTANCEINFO_FILECODE            (0xDFDB)
#define FABRIC_RPL_FABRICRPLDXE_FABRICACPIDOMAININFO_FILECODE              (0xDFDC)
#define FABRIC_RPL_FABRICRPLDXE_FABRICACPIMSCT_FILECODE                    (0xDFDD)
#define FABRIC_RPL_FABRICRPLDXE_FABRICACPISLIT_FILECODE                    (0xDFDE)
#define FABRIC_RPL_FABRICRPLDXE_FABRICACPISRAT_FILECODE                    (0xDFDF)
#define FABRIC_RPL_FABRICRPLDXE_FABRICREADYTOBOOT_FILECODE                 (0xDFF0)
#define FABRIC_RPL_FABRICRPLSMM_AMDFABRICRPLSMM_FILECODE                   (0xDFF1)
#define LIBRARY_BASEFABRICTOPOLOGYRPLLIB_BASEFABRICTOPOLOGYRPLLIB_FILECODE (0xDFF2)
#define LIBRARY_FABRICIDSHOOKRPLLIB_PEI_FABRICIDSHOOKRPLLIBPEI_FILECODE    (0xDFF3)
#define LIBRARY_FABRICIDSHOOKRPLLIB_DXE_FABRICIDSHOOKRPLLIBDXE_FILECODE    (0xDFF4)
#define LIBRARY_FABRICREGISTERACCDF4LIB_FABRICREGISTERACCDF4LIB_FILECODE   (0xDFF5)
#define LIBRARY_FABRICRESOURCEMANAGERRPLLIB_FABRICRESOURCEINIT3_FILECODE   (0xDFF6)
#define LIBRARY_FABRICIDSHOOKPHXLIB_PEI_FABRICIDSHOOKPHXLIBPEI_FILECODE    (0xDFF7)
#define LIBRARY_FABRICREGISTERACCDF4SMNLIB_FABRICREGISTERACCDF4SMNLIB_FILECODE (0xDFF8)
#define LIBRARY_FABRICRESOURCEMANAGERRPLLIB_FABRICRESOURCEMANAGER3_FILECODE (0xDFFA)
#define LIBRARY_FABRICRESOURCEMANAGERRPLLIB_FABRICRESOURCEINITBASEDONNV3_FILECODE (0xDFFB)

#define LIBRARY_FABRICIDSHOOKSTPLIB_PEI_FABRICIDSHOOKSTPLIBPEI_FILECODE    (0xDFFC)
#define LIBRARY_FABRICIDSHOOKSTPLIB_DXE_FABRICIDSHOOKSTPLIBDXE_FILECODE    (0xDFFD)

#define LIBRARY_DXEFABRICSOCSPECIFICSERVICESRSLIB_DXEFABRICSOCSPECIFICSERVICESRSLIB_FILECODE (0xDFFE)
#define LIBRARY_PEIFABRICSOCSPECIFICSERVICESRSLIB_PEIFABRICSOCSPECIFICSERVICESRSLIB_FILECODE (0xDFFF)

// 0xDFE0 ~ 0xDFED for Excavator
#define FABRIC_XV_UNBXVPEI_AMDUNBXVPEI_FILECODE                          (0xDFE0)
#define FABRIC_XV_UNBXVPEI_UNBXVPOWERMANAGER_FILECODE                    (0xDFE1)
#define FABRIC_XV_UNBXVPEI_UNBPSTATESERVICESPEI_FILECODE                 (0xDFE2)
#define FABRIC_XV_UNBXVDXE_AMDUNBXVDXE_FILECODE                          (0xDFE3)
#define FABRIC_XV_UNBXVDXE_UNBTOPOLOGYSERVICESDXE_FILECODE               (0xDFE4)
#define LIBRARY_FABRICPSTATESUNBLIB_FABRICPSTATESUNBLIB_FILECODE         (0xDFE5)
#define LIBRARY_FABRICRESOURCEMANAGERUNBLIB_FABRICRESOURCEMANAGERUNBLIB_FILECODE (0xDFE6)
#define LIBRARY_FABRICPSTATESZPLIB_FABRICPSTATESZPLIB_FILECODE           (0xDFE7)
#define LIBRARY_BASESOCKETLOGICALIDUNBLIB_BASESOCKETLOGICALIDUNBLIB_FILECODE (0xDFE8)
#define LIBRARY_BASEFABRICTOPOLOGYUNBLIB_BASEFABRICTOPOLOGYUNBLIB_FILECODE (0xDFE9)
#define FABRIC_XV_UNBXVDXE_UNBACPICDIT_FILECODE                          (0xDFEA)
#define LIBRARY_FABRICWDTZPLIB_FABRICWDTZPLIB_FILECODE                   (0xDFEB)
#define LIBRARY_FABRICWDTDF3LIB_FABRICWDTDF3LIB_FILECODE                 (0xDFEC)
#define LIBRARY_FABRICWDTDF4LIB_FABRICWDTDF4LIB_FILECODE                 (0xDFED)

#define FABRIC_FF3_FABRICFF3PEI_AMDFABRICFF3PEI_FILECODE                 (0xDE00)
#define FABRIC_FF3_FABRICFF3PEI_FABRICCCMINIT_FILECODE                   (0xDE01)
#define FABRIC_FF3_FABRICFF3PEI_FABRICCSINIT_FILECODE                    (0xDE02)
#define FABRIC_FF3_FABRICFF3PEI_FABRICDFCLKGATINGINIT_FILECODE           (0xDE03)
#define FABRIC_FF3_FABRICFF3PEI_FABRICPIERASINIT_FILECODE                (0xDE04)
#define FABRIC_FF3_FABRICFF3PEI_FABRICSCRUBINIT_FILECODE                 (0xDE05)
#define FABRIC_FF3_FABRICFF3DXE_AMDFABRICFF3DXE_FILECODE                 (0xDE06)
#define FABRIC_FF3_FABRICFF3DXE_FABRICACPICDIT_FILECODE                  (0xDE07)
#define FABRIC_FF3_FABRICFF3DXE_FABRICACPICRAT_FILECODE                  (0xDE08)
#define FABRIC_FF3_FABRICFF3DXE_FABRICACPIDISTANCEINFO_FILECODE          (0xDE09)
#define FABRIC_FF3_FABRICFF3DXE_FABRICACPIDOMAININFO_FILECODE            (0xDE0A)
#define FABRIC_FF3_FABRICFF3DXE_FABRICACPIMSCT_FILECODE                  (0xDE0B)
#define FABRIC_FF3_FABRICFF3DXE_FABRICACPISLIT_FILECODE                  (0xDE0C)
#define FABRIC_FF3_FABRICFF3DXE_FABRICACPISRAT_FILECODE                  (0xDE0D)
#define FABRIC_FF3_FABRICFF3DXE_FABRICREADYTOBOOT_FILECODE               (0xDE0E)
#define FABRIC_FF3_FABRICFF3SMM_AMDFABRICFF3SMM_FILECODE                 (0xDE0F)
#define FABRIC_FF3_FABRICFF3DXE_FABRICPIEPWRMGMT_FILECODE                (0xDE10)
#define LIBRARY_FABRICIDSHOOKFF3LIB_PEI_FABRICIDSHOOKFF3LIBPEI_FILECODE  (0xDE11)
#define LIBRARY_FABRICIDSHOOKFF3LIB_DXE_FABRICIDSHOOKFF3LIBDXE_FILECODE  (0xDE12)
#define LIBRARY_BASESOCKETLOGICALIDFF3DIELIB_BASESOCKETLOGICALIDFF3DIELIB_FILECODE   (0xDE13)
#define LIBRARY_BASEFABRICTOPOLOGYFF3LIB_BASEFABRICTOPOLOGYFF3LIB_FILECODE           (0xDE14)
#define LIBRARY_FABRICRESOURCEMANAGERDF3FF3LIB_FABRICRESOURCEINIT3_FILECODE          (0xDE15)
#define LIBRARY_FABRICRESOURCEMANAGERDF3FF3LIB_FABRICRESOURCEMANAGER3_FILECODE       (0xDE16)
#define LIBRARY_FABRICRESOURCEMANAGERDF3FF3LIB_FABRICRESOURCEINITBASEDONNV3_FILECODE (0xDE17)

#define LIBRARY_BASEFABRICTOPOLOGYZPLIB_BASEFABRICTOPOLOGYZPLIB_FILECODE  (0xDE18)
#define LIBRARY_DXEAMDPCIHOSTBRIDGELIB_PCIHOSTBRIDGELIB_FILECODE          (0xDE19)
#define LIBRARY_DXEFABRICSOCSPECIFICSERVICESSSPLIB_DXEFABRICSOCSPECIFICSERVICESSSPLIB_FILECODE (0xDE1A)
#define LIBRARY_FABRICIDSHOOKUNBLIB_DXE_FABRICIDSHOOKUNBLIBDXE_FILECODE   (0xDE1B)
#define LIBRARY_FABRICIDSHOOKUNBLIB_PEI_FABRICIDSHOOKUNBLIBPEI_FILECODE   (0xDE1C)
#define LIBRARY_PEISOCBISTLOGGING2LIB_SOCBISTLOGGING2LIB_FILECODE         (0xDE1D)
#define LIBRARY_PEISOCBISTZEN3CCDLIB_PEISOCBISTZEN3CCDLIB_FILECODE        (0xDE1E)

#define LIBRARY_CCXIDSCUSTOMPSTATENULLLIB_CCXIDSCUSTOMPSTATESNULLLIB_FILECODE (0xDE1F)
#define LIBRARY_CCXIDSCUSTOMPSTATEZENLIB_CCXIDSCUSTOMPSTATESZENLIB_FILECODE   (0xDE20)
#define LIBRARY_CCXMPSERVICEDEPEXLIB_CCXMPSERVICEPPIDEPEXLIB_FILECODE         (0xDE21)
#define LIBRARY_CCXNONSMMRESUMESECLIB_CCXNONSMMRESUMESEC_FILECODE         (0xDE22)
#define LIBRARY_CCXROLESZEN3LIB_CCXROLESZEN3LIB_FILECODE                  (0xDE23)
#define LIBRARY_CCXSTALLZENLIB_CCXSTALLZENLIB_FILECODE                    (0xDE24)
#define LIBRARY_CCXZEN3IDSHOOKLIB_DXE_CCXZEN3IDSSYNCMSR_FILECODE          (0xDE25)
#define LIBRARY_CCXZEN3RMBIDSHOOKLIB_DXE_CCXZEN3RMBIDSSYNCMSR_FILECODE    (0xDE26)
#define LIBRARY_CCXZENZPIDSHOOKLIB_DXE_CCXZENZPIDSSYNCMSR_FILECODE        (0xDE27)
#define LIBRARY_PEICCXSMMACCESSLIB_PEICCXSMMACCESSLIB_FILECODE            (0xDE28)

#define FABRIC_MDN_FABRICMDNPEI_AMDFABRICMDNPEI_FILECODE                  (0xDE29)
#define FABRIC_MDN_FABRICMDNPEI_FABRICCCMINIT_FILECODE                    (0xDE2A)
#define FABRIC_MDN_FABRICMDNPEI_FABRICCSINIT_FILECODE                     (0xDE2B)
#define FABRIC_MDN_FABRICMDNPEI_FABRICDFCLKGATINGINIT_FILECODE            (0xDE2C)
#define FABRIC_MDN_FABRICMDNPEI_FABRICPIERASINIT_FILECODE                 (0xDE2D)
#define FABRIC_MDN_FABRICMDNPEI_FABRICSCRUBINIT_FILECODE                  (0xDE2E)
#define FABRIC_MDN_FABRICMDNDXE_FABRICPIEPWRMGMT_FILECODE                 (0xDE2F)
#define FABRIC_MDN_FABRICMDNDXE_AMDFABRICMDNDXE_FILECODE                  (0xDE30)
#define FABRIC_MDN_FABRICMDNDXE_FABRICACPICDIT_FILECODE                   (0xDE31)
#define FABRIC_MDN_FABRICMDNDXE_FABRICACPICRAT_FILECODE                   (0xDE32)
#define FABRIC_MDN_FABRICMDNDXE_FABRICACPIDISTANCEINFO_FILECODE           (0xDE33)
#define FABRIC_MDN_FABRICMDNDXE_FABRICACPIDOMAININFO_FILECODE             (0xDE34)
#define FABRIC_MDN_FABRICMDNDXE_FABRICACPIMSCT_FILECODE                   (0xDE35)
#define FABRIC_MDN_FABRICMDNDXE_FABRICACPISLIT_FILECODE                   (0xDE36)
#define FABRIC_MDN_FABRICMDNDXE_FABRICACPISRAT_FILECODE                   (0xDE37)
#define FABRIC_MDN_FABRICMDNDXE_FABRICREADYTOBOOT_FILECODE                (0xDE38)
#define FABRIC_MDN_FABRICMDNSMM_AMDFABRICMDNSMM_FILECODE                  (0xDE39)
#define LIBRARY_FABRICIDSHOOKMDNLIB_PEI_FABRICIDSHOOKMDNLIBPEI_FILECODE   (0xDE3A)
#define LIBRARY_FABRICIDSHOOKMDNLIB_DXE_FABRICIDSHOOKMDNLIBDXE_FILECODE   (0xDE3B)
#define LIBRARY_BASESOCKETLOGICALIDMDNDIELIB_BASESOCKETLOGICALIDMDNDIELIB_FILECODE (0xDE3C)
#define LIBRARY_BASEFABRICTOPOLOGYMDNLIB_BASEFABRICTOPOLOGYMDNLIB_FILECODE (0xDE3D)
#define LIBRARY_FABRICRESOURCEMANAGERDF3MDNLIB_FABRICRESOURCEINIT3_FILECODE (0xDE3E)
#define LIBRARY_FABRICRESOURCEMANAGERDF3MDNLIB_FABRICRESOURCEINITBASEDONNV3_FILECODE (0xDE3F)
#define LIBRARY_FABRICRESOURCEMANAGERDF3MDNLIB_FABRICRESOURCEMANAGER3_FILECODE (0xDE40)

#define FABRIC_MI3_FABRICMI3PEI_AMDFABRICMI3PEI_FILECODE                  (0xDE41)
#define FABRIC_MI3_FABRICMI3PEI_FABRICDFCLKGATINGINIT_FILECODE            (0xDE42)
#define FABRIC_MI3_FABRICMI3PEI_FABRICPIERASINIT_FILECODE                 (0xDE43)
#define FABRIC_MI3_FABRICMI3DXE_FABRICREADYTOBOOT_FILECODE                (0xDE44)
#define FABRIC_MI3_FABRICMI3SMM_AMDFABRICMI3SMM_FILECODE                  (0xDE45)
#define FABRIC_MI3_FABRICMI3DXE_AMDFABRICMI3DXE_FILECODE                  (0xDE46)
#define FABRIC_MI3_FABRICMI3DXE_FABRICACPICDIT_FILECODE                   (0xDE47)
#define FABRIC_MI3_FABRICMI3DXE_FABRICACPICRAT_FILECODE                   (0xDE48)
#define FABRIC_MI3_FABRICMI3DXE_FABRICACPIDISTANCEINFO_FILECODE           (0xDE49)
#define FABRIC_MI3_FABRICMI3DXE_FABRICACPIDOMAININFO_FILECODE             (0xDE4A)
#define FABRIC_MI3_FABRICMI3DXE_FABRICACPIMSCT_FILECODE                   (0xDE4B)
#define FABRIC_MI3_FABRICMI3DXE_FABRICACPISLIT_FILECODE                   (0xDE4C)
#define FABRIC_MI3_FABRICMI3DXE_FABRICACPISRAT_FILECODE                   (0xDE4D)
#define LIBRARY_FABRICIDSHOOKMI3LIB_PEI_FABRICIDSHOOKMI3LIBPEI_FILECODE   (0xDE4E)
#define LIBRARY_FABRICIDSHOOKMI3LIB_DXE_FABRICIDSHOOKMI3LIBDXE_FILECODE   (0xDE4F)
#define LIBRARY_BASEFABRICTOPOLOGYMI3LIB_BASEFABRICTOPOLOGYMI3LIB_FILECODE (0xDE50)
#define LIBRARY_PEIFABRICTOPOLOGYSERVICES2MI3LIB_FABRICTOPOLOGYSERVICES2MI3PEI_FILECODE (0xDE51)

#define FABRIC_STXKRK_FABRICSTXKRKPEI_AMDFABRICSTXKRKPEI_FILECODE                  (0xDE52)
#define FABRIC_STXKRK_FABRICSTXKRKPEI_FABRICCSINIT_FILECODE                        (0xDE53)
#define FABRIC_STXKRK_FABRICSTXKRKPEI_FABRICDFCLKGATINGINIT_FILECODE               (0xDE54)
#define FABRIC_STXKRK_FABRICSTXKRKPEI_FABRICPIERASINIT_FILECODE                    (0xDE55)
#define FABRIC_STXKRK_FABRICSTXKRKDXE_FABRICPIEPWRMGMT_FILECODE                    (0xDE56)
#define FABRIC_STXKRK_FABRICSTXKRKDXE_AMDFABRICSTXKRKDXE_FILECODE                  (0xDE57)
#define FABRIC_STXKRK_FABRICSTXKRKDXE_FABRICACPICDIT_FILECODE                      (0xDE58)
#define FABRIC_STXKRK_FABRICSTXKRKDXE_FABRICACPICRAT_FILECODE                      (0xDE59)
#define FABRIC_STXKRK_FABRICSTXKRKDXE_FABRICACPIDISTANCEINFO_FILECODE              (0xDE5A)
#define FABRIC_STXKRK_FABRICSTXKRKDXE_FABRICACPIDOMAININFO_FILECODE                (0xDE5B)
#define FABRIC_STXKRK_FABRICSTXKRKDXE_FABRICACPIMSCT_FILECODE                      (0xDE5C)
#define FABRIC_STXKRK_FABRICSTXKRKDXE_FABRICACPISLIT_FILECODE                      (0xDE5D)
#define FABRIC_STXKRK_FABRICSTXKRKDXE_FABRICACPISRAT_FILECODE                      (0xDE5E)
#define FABRIC_STXKRK_FABRICSTXKRKDXE_FABRICREADYTOBOOT_FILECODE                   (0xDE5F)
#define FABRIC_STXKRK_FABRICSTXKRKSMM_AMDFABRICSTXKRKSMM_FILECODE                  (0xDE60)
#define LIBRARY_BASEFABRICTOPOLOGYSTXKRKLIB_BASEFABRICTOPOLOGYSTXKRKLIB_FILECODE   (0xDE61)
#define LIBRARY_FABRICRESOURCEMANAGERSTXKRKLIB_FABRICRESOURCEINIT3_FILECODE        (0xDE62)
#define LIBRARY_FABRICRESOURCEMANAGERSTXKRKLIB_FABRICRESOURCEINITBASEDONNV3_FILECODE  (0xDE63)
#define LIBRARY_FABRICRESOURCEMANAGERSTXKRKLIB_FABRICRESOURCEMANAGER3_FILECODE        (0xDE64)
#define LIBRARY_FABRICIDSHOOKSTXKRKLIB_PEI_FABRICIDSHOOKSTXKRKLIBPEI_FILECODE      (0xDE65)
#define LIBRARY_FABRICIDSHOOKSTXKRKLIB_DXE_FABRICIDSHOOKSTXKRKLIBDXE_FILECODE      (0xDE66)
#define FABRIC_MDN_COMMON_FABRICCOMMON_FILECODE                                    (0xDE67)

#define FABRIC_BRH_FABRICBRHPEI_AMDFABRICBRHPEI_FILECODE                           (0xDE68)
#define FABRIC_BRH_FABRICBRHPEI_FABRICCCMINIT_FILECODE                             (0xDE69)
#define FABRIC_BRH_FABRICBRHPEI_FABRICCSINIT_FILECODE                              (0xDE6A)
#define FABRIC_BRH_FABRICBRHPEI_FABRICDFCLKGATINGINIT_FILECODE                     (0xDE6B)
#define FABRIC_BRH_FABRICBRHPEI_FABRICPIERASINIT_FILECODE                          (0xDE6C)
#define FABRIC_BRH_FABRICBRHPEI_FABRICSCRUBINIT_FILECODE                           (0xDE6D)
#define FABRIC_BRH_FABRICBRHDXE_AMDFABRICBRHDXE_FILECODE                           (0xDE6E)
#define FABRIC_BRH_FABRICBRHDXE_FABRICACPICDIT_FILECODE                            (0xDE6F)
#define FABRIC_BRH_FABRICBRHDXE_FABRICACPICRAT_FILECODE                            (0xDE70)
#define FABRIC_BRH_FABRICBRHDXE_FABRICACPIDISTANCEINFO_FILECODE                    (0xDE71)
#define FABRIC_BRH_FABRICBRHDXE_FABRICACPIDOMAININFO_FILECODE                      (0xDE72)
#define FABRIC_BRH_FABRICBRHDXE_FABRICACPIHMAT_FILECODE                            (0xDE73)
#define FABRIC_BRH_FABRICBRHDXE_FABRICACPIMSCT_FILECODE                            (0xDE74)
#define FABRIC_BRH_FABRICBRHDXE_FABRICACPISLIT_FILECODE                            (0xDE75)
#define FABRIC_BRH_FABRICBRHDXE_FABRICACPISRAT_FILECODE                            (0xDE76)
#define FABRIC_BRH_FABRICBRHDXE_FABRICREADYTOBOOT_FILECODE                         (0xDE77)
#define FABRIC_BRH_FABRICBRHSMM_AMDFABRICBRHSMM_FILECODE                           (0xDE78)
#define FABRIC_BRH_FABRICBRHPEI_FABRICPDRTUNINGINIT_FILECODE                       (0xDE79)
#define LIBRARY_BASEFABRICTOPOLOGYBRHRSLIB_BASEFABRICTOPOLOGYBRHRSLIB_FILECODE     (0xDE7A)
#define LIBRARY_BASESOCKETLOGICALIDBRHDIELIB_BASESOCKETLOGICALIDBRHDIELIB_FILECODE (0xDE7B)
#define LIBRARY_FABRICREGISTERACCBRHLIB_FABRICREGISTERACCBRHLIB_FILECODE           (0xDE7C)
#define LIBRARY_SMMCPUFEATURESLIB_SMMCPUFEATURESLIB_FILECODE                       (0xDE7D)
#define FABRIC_MI3_FABRICMI3DXE_FABRICACPIBANDWIDTHLATENCY_FILECODE                (0xDE7E)

#define FABRIC_STXH_FABRICSTXHPEI_AMDFABRICSTXHPEI_FILECODE                        (0xDE7F)
#define FABRIC_STXH_FABRICSTXHPEI_FABRICCSINIT_FILECODE                            (0xDE80)
#define FABRIC_STXH_FABRICSTXHPEI_FABRICDFCLKGATINGINIT_FILECODE                   (0xDE81)
#define FABRIC_STXH_FABRICSTXHPEI_FABRICPIERASINIT_FILECODE                        (0xDE82)
#define FABRIC_STXH_FABRICSTXHDXE_FABRICPIEPWRMGMT_FILECODE                        (0xDE83)
#define FABRIC_STXH_FABRICSTXHDXE_AMDFABRICSTXHDXE_FILECODE                        (0xDE84)
#define FABRIC_STXH_FABRICSTXHDXE_FABRICACPICDIT_FILECODE                          (0xDE85)
#define FABRIC_STXH_FABRICSTXHDXE_FABRICACPICRAT_FILECODE                          (0xDE86)
#define FABRIC_STXH_FABRICSTXHDXE_FABRICACPIDISTANCEINFO_FILECODE                  (0xDE87)
#define FABRIC_STXH_FABRICSTXHDXE_FABRICACPIDOMAININFO_FILECODE                    (0xDE88)
#define FABRIC_STXH_FABRICSTXHDXE_FABRICACPIMSCT_FILECODE                          (0xDE89)
#define FABRIC_STXH_FABRICSTXHDXE_FABRICACPISLIT_FILECODE                          (0xDE8A)
#define FABRIC_STXH_FABRICSTXHDXE_FABRICACPISRAT_FILECODE                          (0xDE8B)
#define FABRIC_STXH_FABRICSTXHDXE_FABRICREADYTOBOOT_FILECODE                       (0xDE8C)
#define FABRIC_STXH_FABRICSTXHSMM_AMDFABRICSTXHSMM_FILECODE                        (0xDE8D)
#define LIBRARY_BASEFABRICTOPOLOGYSTXHLIB_BASEFABRICTOPOLOGYSTXHLIB_FILECODE       (0xDE8E)
#define LIBRARY_FABRICRESOURCEMANAGERSTXHLIB_FABRICRESOURCEINIT3_FILECODE          (0xDE8F)
#define LIBRARY_FABRICRESOURCEMANAGERSTXHLIB_FABRICRESOURCEINITBASEDONNV3_FILECODE (0xDE90)
#define LIBRARY_FABRICRESOURCEMANAGERSTXHLIB_FABRICRESOURCEMANAGER3_FILECODE       (0xDE91)
#define LIBRARY_FABRICIDSHOOKSTXHLIB_PEI_FABRICIDSHOOKSTXHLIBPEI_FILECODE          (0xDE92)
#define LIBRARY_FABRICIDSHOOKSTXHLIB_DXE_FABRICIDSHOOKSTXHLIBDXE_FILECODE          (0xDE93)
#define FABRIC_MI3_FABRICMI3DXE_FABRICUPDATEASLLIB_FILECODE                        (0xDE94)
#define LIBRARY_BASESOCKETLOGICALIDMI3DIELIB_BASESOCKETLOGICALIDMI3DIELIB_FILECODE (0xDE95)
#define LIBRARY_BASEFABRICTOPOLOGYSTPSHPLIB_BASEFABRICTOPOLOGYSTPSHPLIB_FILECODE   (0xDE96)
#define FABRIC_MI3_FABRICMI3SMM_AMDFABRICASLCALLBACK_FILECODE                      (0xDEB5)
#define LIBRARY_FABRICIDSHOOKSHPLIB_PEI_FABRICIDSHOOKSHPLIBPEI_FILECODE            (0xDEB6)
#define LIBRARY_FABRICIDSHOOKSHPLIB_DXE_FABRICIDSHOOKSHPLIBDXE_FILECODE            (0xDEB7)

#define LIBRARY_FABRICROOTBRIDGEORDERLIB_FABRICROOTBRIDGEORDERLIB_FILECODE         (0xDEB8)

// GNB
#define LIBRARY_GNBCZLIB_MODULES_GNBCOMMONLIB_GNBLIB_FILECODE                       (0xA001)
#define LIBRARY_GNBCZLIB_MODULES_GNBCOMMONLIB_GNBLIBCPUACC_FILECODE                 (0xA002)
#define LIBRARY_GNBCZLIB_MODULES_GNBCOMMONLIB_GNBLIBIOACC_FILECODE                  (0xA003)
#define LIBRARY_GNBCZLIB_MODULES_GNBCOMMONLIB_GNBLIBMEMACC_FILECODE                 (0xA004)
#define LIBRARY_GNBCZLIB_MODULES_GNBCOMMONLIB_GNBLIBPCI_FILECODE                    (0xA005)
#define LIBRARY_GNBCZLIB_MODULES_GNBCOMMONLIB_GNBLIBPCIACC_FILECODE                 (0xA006)
#define LIBRARY_GNBCZLIB_MODULES_GNBGFXINITLIB_GFXENUMCONNECTORS_FILECODE           (0xA007)
#define LIBRARY_GNBCZLIB_MODULES_GNBPCIECONFIG_PCIECONFIGDATA_FILECODE              (0xA008)
#define LIBRARY_GNBCZLIB_MODULES_GNBPCIECONFIG_PCIECONFIGLIB_FILECODE               (0xA009)
#define LIBRARY_GNBCZLIB_MODULES_GNBPCIECONFIG_PCIEINPUTPARSER_FILECODE             (0xA00A)
#define LIBRARY_GNBCZLIB_MODULES_GNBPCIECONFIG_PCIEMAPTOPOLOGY_FILECODE             (0xA00B)
#define LIBRARY_GNBCZLIB_MODULES_GNBPCIEMISC_PCIEASPMBLACKLIST_FILECODE             (0xA00C)
#define LIBRARY_GNBCZLIB_MODULES_PCIEMISCCOMMLIB_PCIEASPMEXITLATENCY_FILECODE       (0xA00D)
#define LIBRARY_GNBCZLIB_MODULES_GNBPCIEINITLIB_PCIEPORTREGACC_FILECODE             (0xA00E)
#define LIBRARY_GNBCZLIB_MODULES_GNBPCIEINITLIB_PCIEPORTSERVICES_FILECODE           (0xA00F)
#define LIBRARY_GNBCZLIB_MODULES_GNBPCIEINITLIB_PCIEUTILITYLIB_FILECODE             (0xA010)
#define LIBRARY_GNBCZLIB_MODULES_GNBPCIEINITLIB_PCIEWRAPPERREGACC_FILECODE          (0xA011)
#define LIBRARY_GNBCZLIB_MODULES_GNBPCIEMISC_PCIECOMMCLKCFG_FILECODE                (0xA012)
#define LIBRARY_GNBCZLIB_MODULES_GNBTABLE_GNBTABLE_FILECODE                         (0xA013)
#define LIBRARY_GNBCZLIB_MODULES_GNBGFXCONFIG_GFXCONFIGLIB_FILECODE                 (0xA014)
#define LIBRARY_GNBCZLIB_MODULES_GNBIOMMUIVRS_GNBIOMMUIVRS_FILECODE                 (0xA015)
#define LIBRARY_GNBCZLIB_MODULES_GNBIOMMUIVRS_GNBIVRSLIB_FILECODE                   (0xA016)
#define LIBRARY_GNBCZLIB_MODULES_GNBNBINITLIB_GNBNBINITLIB_FILECODE                 (0xA017)
#define LIBRARY_GNBCZLIB_MODULES_GNBFAMTRANSLATION_GNBPCIETRANSLATION_FILECODE      (0xA018)
#define LIBRARY_GNBCZLIB_MODULES_GNBSBLIB_GNBSBPCIE_FILECODE                        (0xA019)
#define LIBRARY_GNBCZLIB_MODULES_GNBSBLIB_GNBSBLIB_FILECODE                         (0xA01A)
#define LIBRARY_GNBCZLIB_MODULES_GNBIOMMUIVRS_GNBSBIOMMULIB_FILECODE                (0xA01B)
#define LIBRARY_GNBCZLIB_MODULES_GNBCOMMONLIB_GNBTIMERLIB_FILECODE                  (0xA01C)
#define LIBRARY_GNBCZLIB_MODULES_GNBSSOCKETLIB_GNBSSOCKETLIB_FILECODE               (0xA01D)
#define LIBRARY_GNBCZLIB_MODULES_GNBPCIECONFIG_GNBHANDLELIB_FILECODE                (0xA01E)
#define LIBRARY_GNBCZLIB_MODULES_GNBPCIEMISC_PCIEASPM_FILECODE                      (0xA01F)
#define LIBRARY_GNBCZLIB_MODULES_GNBPCIEINITLIB_PCIEASPMEXITLATENCY_FILECODE        (0xA020)
#define LIBRARY_GNBCZLIB_MODULES_GNBPCIEINITLIB_PCIESILICONSERVICES_FILECODE        (0xA021)
#define LIBRARY_GNBCZLIB_MODULES_GNBFAMTRANSLATION_GNBTRANSLATION_FILECODE          (0xA022)
#define LIBRARY_GNBCZLIB_MODULES_GNBIOAPIC_GNBIOAPIC_FILECODE                       (0xA023)
#define LIBRARY_GNBCZLIB_MODULES_PCIEMISCCOMMLIB_PCIEMAXPAYLOAD_FILECODE            (0xA024)
#define LIBRARY_GNBCZLIB_MODULES_GNBPCIEMISC_PCIECLKPM_FILECODE                     (0xA025)
#define LIBRARY_GNBCZLIB_MODULES_GNBSVIEW_GNBSVIEW_FILECODE                         (0xA026)
#define LIBRARY_GNBCZLIB_MODULES_GNBPCIETRAININGV2_PCIETRAININGV2_FILECODE          (0xA027)
#define LIBRARY_GNBCZLIB_MODULES_GNBPCIETRAININGV2_PCIEWORKAROUNDSV2_FILECODE       (0xA028)
#define LIBRARY_GNBCZLIB_MODULES_GNBINITCZ_ALIBCZM_FILECODE                         (0xA029)
#define LIBRARY_GNBCZLIB_MODULES_GNBINITCZ_ALIBCZD_FILECODE                         (0xA02A)
#define LIBRARY_GNBCZLIB_MODULES_GNBINITCZ_GFXENVINITCZ_FILECODE                    (0xA02B)
#define LIBRARY_GNBCZLIB_MODULES_GNBINITCZ_GFXGMCINITCZ_FILECODE                    (0xA02C)
#define LIBRARY_GNBCZLIB_MODULES_GNBINITCZ_GFXINTEGRATEDINFOTABLECZ_FILECODE        (0xA02D)
#define LIBRARY_GNBCZLIB_MODULES_GNBINITCZ_GFXLIBCZ_FILECODE                        (0xA02E)
#define LIBRARY_GNBCZLIB_MODULES_GNBINITCZ_GFXMIDINITCZ_FILECODE                    (0xA02F)
#define LIBRARY_GNBCZLIB_MODULES_GNBINITCZ_GFXPOSTINITCZ_FILECODE                   (0xA030)
#define LIBRARY_GNBCZLIB_MODULES_GNBINITCZ_GNBEARLYINITCZ_FILECODE                  (0xA031)
#define LIBRARY_GNBCZLIB_MODULES_GNBINITCZ_GNBENVINITCZ_FILECODE                    (0xA032)
#define LIBRARY_GNBCZLIB_MODULES_GNBINITCZ_GNBFUSETABLECZ_FILECODE                  (0xA033)
#define LIBRARY_GNBCZLIB_MODULES_GNBINITCZ_GNBIOMMUIVRSCZ_FILECODE                  (0xA034)
#define LIBRARY_GNBCZLIB_MODULES_GNBINITCZ_GNBMIDINITCZ_FILECODE                    (0xA035)
#define LIBRARY_GNBCZLIB_MODULES_GNBINITCZ_GNBPOSTINITCZ_FILECODE                   (0xA036)
#define LIBRARY_GNBCZLIB_MODULES_GNBINITCZ_GNBREGISTERACCCZ_FILECODE                (0xA037)
#define LIBRARY_GNBCZLIB_MODULES_GNBINITCZ_PCIECOMPLEXDATACZ_FILECODE               (0xA038)
#define LIBRARY_GNBCZLIB_MODULES_GNBINITCZ_PCIECONFIGCZ_FILECODE                    (0xA039)
#define LIBRARY_GNBCZLIB_MODULES_GNBINITCZ_PCIEEARLYINITCZ_FILECODE                 (0xA03A)
#define LIBRARY_GNBCZLIB_MODULES_GNBINITCZ_PCIEENVINITCZ_FILECODE                   (0xA03B)
#define LIBRARY_GNBCZLIB_MODULES_GNBINITCZ_PCIELIBCZ_FILECODE                       (0xA03C)
#define LIBRARY_GNBCZLIB_MODULES_GNBPCIEMISC_PCIEMAXPAYLOAD_FILECODE                (0xA03D)
#define LIBRARY_GNBCZLIB_MODULES_GNBINITCZ_PCIEPOSTINITCZ_FILECODE                  (0xA03E)
#define LIBRARY_GNBCZLIB_MODULES_GNBINITCZ_PCIEPOWERGATECZ_FILECODE                 (0xA03F)
#define LIBRARY_GNBCZLIB_MODULES_GNBSMULIBV8_GNBSMUINITLIBV8_FILECODE               (0xA040)
#define LIBRARY_GNBCZLIB_MODULES_GNBINITCZ_PCIEARIINITCZ_FILECODE                   (0xA041)
#define LIBRARY_GNBCZLIB_MODULES_GNBINITCZ_GNBBOOTTIMECALCZ_FILECODE                (0xA042)
#define LIBRARY_GNBCZLIB_MODULES_GNBINITCZ_GNBPSPCZ_FILECODE                        (0xA043)
#define LIBRARY_GNBCZLIB_MODULES_GNBINITCZ_GNBSMUINITCZ_FILECODE                    (0xA044)
#define LIBRARY_GNBCZLIB_MODULES_GNBIOMMUIVRS_GNBIOMMUSCRATCH_FILECODE              (0xA045)
#define LIBRARY_GNBCZLIB_MODULES_GNBPCIEALIBV2_PCIEALIBV2_FILECODE                  (0xA046)
#define LIBRARY_GNBCZLIB_MODULES_GNBGFXINTTABLEV5_GFXLIBV5_FILECODE                 (0xA047)
#define LIBRARY_GNBCZLIB_MODULES_GNBGFXINTTABLEV5_GFXINTEGRATEDINFOTABLEV5_FILECODE (0xA048)
#define LIBRARY_GNBCZLIB_MODULES_GNBGFXINTTABLEV5_GFXPWRPLAYTABLEV5_FILECODE        (0xA049)
#define LIBRARY_GNBCZLIB_MODULES_GNBAZALIALIB_GNBAZALIALIB_FILECODE                 (0xA04A)
#define LIBRARY_GNBCZLIB_MODULES_GNBINITCZ_PCIEMIDINITCZ_FILECODE                   (0xA04B)
#define LIBRARY_NBIOIDSHOOKCZLIB_NBIOIDSHOOKCZLIB_FILECODE                          (0xA04C)

// SHP
#define NBIO_SHP_PEI_BASEINIT_FILECODE                                     (0xA060)
#define NBIO_SHP_PEI_CXLINIT_FILECODE                                      (0xA061)
#define NBIO_SHP_PEI_DPCSTATUSREPORT_FILECODE                              (0xA062)
#define NBIO_SHP_PEI_DXIOCFGPOINTS_FILECODE                                (0xA063)
#define NBIO_SHP_PEI_DXIOINIT_FILECODE                                     (0xA064)
#define NBIO_SHP_PEI_PCIEEARLYTRAIN_FILECODE                               (0xA065)
#define NBIO_SHP_PEI_ENTRYPOINTS_FILECODE                                  (0xA066)
#define NBIO_SHP_PEI_HIDEPORTS_FILECODE                                    (0xA067)
#define NBIO_SHP_PEI_IOAPICINIT_FILECODE                                   (0xA068)
#define NBIO_SHP_PEI_IOMMUINIT_FILECODE                                    (0xA069)
#define NBIO_SHP_PEI_NBIOTABLES_FILECODE                                   (0xA06A)
#define NBIO_SHP_PEI_NBIOTABLESMP_FILECODE                                 (0xA06B)
#define NBIO_SHP_PEI_NBIOTOPMEM_FILECODE                                   (0xA06C)
#define NBIO_SHP_PEI_NTBFEATURE_FILECODE                                   (0xA06D)
#define NBIO_SHP_PEI_PCIECOMPLEXDATA_FILECODE                              (0xA06E)
#define NBIO_SHP_PEI_PCIEHOTPLUG_FILECODE                                  (0xA06F)
#define NBIO_SHP_PEI_PCIEREMAP_FILECODE                                    (0xA070)
#define NBIO_SHP_PEI_PCIESTRAPS_FILECODE                                   (0xA071)
#define NBIO_SHP_PEI_PKGTYPEFIXUPS_FILECODE                                (0xA072)
#define NBIO_SHP_PEI_SMUINIT_FILECODE                                      (0xA073)
#define NBIO_SHP_PEI_SMUSERVICESPPI_FILECODE                               (0xA074)
#define NBIO_SHP_PEI_TOPOLOGYWA_FILECODE                                   (0xA075)
#define NBIO_SHP_PEI_MPIOTUNNEL_FILECODE                                   (0xA076)
#define NBIO_SHP_DXE_CPPCSERVICESPROTOCOL_FILECODE                         (0xA077)
#define NBIO_SHP_DXE_CXLCONFIGPORTFEATURE_FILECODE                         (0xA078)
#define NBIO_SHP_DXE_ENTRYPOINTS_FILECODE                                  (0xA079)
#define NBIO_SHP_DXE_IOMMUINIT_FILECODE                                    (0xA07A)
#define NBIO_SHP_DXE_NBIOHWLOCK_FILECODE                                   (0xA07B)
#define NBIO_SHP_DXE_PCIEAER_FILECODE                                      (0xA07C)
#define NBIO_SHP_DXE_PCIEINIT_FILECODE                                     (0xA07D)
#define NBIO_SHP_DXE_PCIESRIS_FILECODE                                     (0xA07E)
#define NBIO_SHP_DXE_PCIETOPOLOGYPROTOCOL_FILECODE                         (0xA07F)
#define NBIO_SHP_DXE_PCIETYPEFIXUPS_FILECODE                               (0xA080)
#define NBIO_SHP_DXE_PMMTABLEINIT_FILECODE                                 (0xA081)
#define NBIO_SHP_DXE_RASCONTROL_FILECODE                                   (0xA082)
#define NBIO_SHP_DXE_SERVERHOTPLUGFEAT_FILECODE                            (0xA083)
#define NBIO_SHP_DXE_SMUDEBUG_FILECODE                                     (0xA084)
#define NBIO_SHP_DXE_SMUINIT_FILECODE                                      (0xA085)
#define NBIO_SHP_DXE_SMUSERVICESPROTOCOL_FILECODE                          (0xA086)
#define NBIO_SHP_DXE_SMUSUPPORTFUNCTIONS_FILECODE                          (0xA087)
#define NBIO_SHP_PEI_IOMMUTABLES_FILECODE                                  (0xA088)
#define NBIO_SHP_PEI_NBIOIOHCSHPTBL_FILECODE                               (0xA089)
#define NBIO_SHP_PEI_NBIONBIFINIT_FILECODE                                 (0xA08A)
#define LIBRARY_NBIOIDSHOOKSHPLIB_DXE_NBIOIDSHOOKSHPLIBDXE_FILECODE        (0xA08B)
#define LIBRARY_NBIOIDSHOOKSHPLIB_PEI_NBIOIDSHOOKSHPLIBPEI_FILECODE        (0xA08C)
#define LIBRARY_NBIOSMUSHPLIB_NBIOSMUSHPLIB_FILECODE                       (0xA08D)
#define NBIO_SHP_DXE_CXLHOTPLUGFEATURES_FILECODE                           (0xA08E)
#define NBIO_SHP_PEI_HARVESTFILE_FILECODE                                  (0xA08F)
#define NBIO_SHP_DXE_ALIBINIT_FILECODE                                     (0xA090)
#define NBIO_SHP_DXE_MPIOSERVICESPROTOCOL_FILECODE                         (0xA091)
#define NBIO_SHP_DXE_PCIECLKREQ_FILECODE                                   (0xA092)
#define NBIO_SHP_PEI_HDAUDIOINIT_FILECODE                                  (0xA093)

#define LIBRARY_GNBHEAPDXELIB_GNBHEAPDXELIB_FILECODE                 (0xA101)
#define LIBRARY_GNBHEAPPEILIB_GNBHEAPPEILIB_FILECODE                 (0xA102)
#define LIBRARY_GNBREGISTERACCZPLIB_GNBREGISTERACCZPLIB_FILECODE     (0xA103)
#define LIBRARY_NBIOREGISTERACCLIB_NBIOREGISTERACC_FILECODE          (0xA104)
#define LIBRARY_GNBLIB_GNBLIB_FILECODE                               (0xA105)
#define LIBRARY_PCIECONFIGLIB_PCIECONFIGLIB_FILECODE                 (0xA106)
#define LIBRARY_PCIECONFIGLIB_PCIEINPUTPARSERLIB_FILECODE            (0xA107)
#define LIBRARY_GNBCPUACCLIB_GNBCPUACCLIB_FILECODE                   (0xA108)
#define LIBRARY_GNBIOACCLIB_GNBIOACCLIB_FILECODE                     (0xA10B)
#define LIBRARY_GNBMEMACCLIB_GNBMEMACCLIB_FILECODE                   (0xA10C)
#define LIBRARY_GNBPCIACCLIB_GNBPCIACCLIB_FILECODE                   (0xA10D)
#define LIBRARY_GNBPCILIB_GNBPCILIB_FILECODE                         (0xA110)
#define LIBRARY_GNBSSOCKETLIB_GNBSSOCKETLIB_FILECODE                 (0xA111)
#define LIBRARY_NBIOHANDLELIB_NBIOHANDLELIB_FILECODE                 (0xA113)
#define LIBRARY_NBIOIOMMUIVRSLIB_GNBIOMMUIVRSLIB_FILECODE            (0xA114)
#define LIBRARY_NBIOIOMMUIVRSLIB_GNBIOMMUSCRATCHLIB_FILECODE         (0xA115)
#define LIBRARY_NBIOIOMMUIVRSLIB_GNBIVRSLIB_FILECODE                 (0xA116)
#define LIBRARY_NBIOIOMMUIVRSLIB_GNBSBIOMMULIB_FILECODE              (0xA117)
#define LIBRARY_NBIOREGISTERACCLIB_NBIOTABLE_FILECODE                (0xA118)
#define LIBRARY_NBIOSMUV10LIB_NBIOSMUV10LIB_FILECODE                 (0xA119)
#define LIBRARY_NBIOSMUV9LIB_NBIOSMUV9LIB_FILECODE                   (0xA11A)
#define LIBRARY_NBIOSMUV11LIB_NBIOSMUV11LIB_FILECODE                 (0xA11B)
#define LIBRARY_NBIOSMUV12LIB_NBIOSMUV12LIB_FILECODE                 (0xA11C)
#define LIBRARY_PCIEMISCCOMMLIB_PCIEASPMBLACKLISTLIB_FILECODE        (0xA11E)
#define LIBRARY_PCIEMISCCOMMLIB_PCIEASPMLIB_FILECODE                 (0xA11F)
#define LIBRARY_PCIEMISCCOMMLIB_PCIECLKPMLIB_FILECODE                (0xA120)
#define LIBRARY_PCIEMISCCOMMLIB_PCIECOMMCLKCFGLIB_FILECODE           (0xA121)
#define LIBRARY_PCIEMISCCOMMLIB_PCIEMAXPAYLOADLIB_FILECODE           (0xA122)
#define LIBRARY_PCIEMISCCOMMLIB_PCIEASPMEXITLATENCYLIB_FILECODE      (0xA123)
#define LIBRARY_PCIEMISCCOMMLIB_PCIEARILIB_FILECODE                  (0xA124)
#define LIBRARY_PCIEMISCCOMMLIB_PCIEASPML1SSLIB_FILECODE             (0xA125)
#define LIBRARY_PCIEMISCCOMMLIB_PCIESLOTPWRLIMIT_FILECODE            (0xA126)
#define LIBRARY_DXIOLIBV1_DXIOLIBV1_FILECODE                         (0xA127)
#define LIBRARY_NBIOIDSHOOKZPLIB_NBIOIDSHOOKZPLIB_FILECODE           (0xA128)
#define LIBRARY_NBIOIDSHOOKRVLIB_NBIOIDSHOOKRVLIB_FILECODE           (0xA129)
#define LIBRARY_PCIEMISCCOMMLIB_PCIEMAXREADREQLIB_FILECODE           (0xA12A)
#define LIBRARY_NBIOAZALIALIB_NBIOAZALIALIB_FILECODE                 (0xA12B)
#define LIBRARY_SMNACCESSLIB_SMNACCESSLIB_FILECODE                   (0xA12C)
#define LIBRARY_DXIOLIBV1_DXIOANCDATAV1_FILECODE                     (0xA12D)
#define LIBRARY_PCIEMISCCOMMLIB_PCIELTRLIB_FILECODE                  (0xA12E)
#define LIBRARY_NBIOTABLEBLASTLIB_NBIOTABLEBLASTLIB_FILECODE         (0xA12F)
#define LIBRARY_DXIOLIBV2_DXIOLIBV2_FILECODE                         (0xA130)
#define LIBRARY_DXIOLIBV2_DXIOANCDATAV2_FILECODE                     (0xA131)
#define LIBRARY_DXIOLIBV2_DXIODEBUGOUT_FILECODE                      (0xA132)
#define LIBRARY_DXIOLIBV2_DXIOMAPPINGRESULTS_FILECODE                (0xA133)
#define LIBRARY_DXIOLIBV2_DXIOPARSER_FILECODE                        (0xA134)
#define LIBRARY_DXIOLIBV2_DXIOPROCESSRESULTS_FILECODE                (0xA135)
#define LIBRARY_DXIOLIBV2_DXIOSUPPORTFUNCTIONS_FILECODE              (0xA136)
#define LIBRARY_DXIOLIBV2_DXIOTRAININGRESULTS_FILECODE               (0xA137)
#define LIBRARY_PCIEMISCCOMMLIB_PCIEMAXCPLPAYLOADLIB_FILECODE        (0xA138)
#define LIBRARY_NBIOUTILLIB_NBIOUTILLIB_FILECODE                     (0xA139)
#define LIBRARY_BXBNBIO_BXBSMUV11LIB_BXBSMUV11LIB_FILECODE           (0xA13A)
#define LIBRARY_BXBNBIO_BXBACCESSLIB_BXBACCESSLIB_FILECODE           (0xA13B)
#define LIBRARY_BXBNBIO_BXBNULLLIB_BXBNULLLIB_FILECODE               (0xA13C)
#define LIBRARY_PCIEMISCCOMMLIB_PCIETBTLIB_FILECODE                  (0xA13D)
#define LIBRARY_DXIOLIBV2_DXIOEARLYTRAIN_FILECODE                    (0xA13E)
#define LIBRARY_NBIOSMUV13LIB_NBIOSMUV13LIB_FILECODE                 (0xA13F)

#define LIBRARY_NBIOIDSHOOKSSPLIB_PEI_NBIOIDSHOOKSSPLIBPEI_FILECODE  (0xA140)
#define LIBRARY_NBIOIDSHOOKSSPLIB_DXE_NBIOIDSHOOKSSPLIBDXE_FILECODE  (0xA141)

#define LIBRARY_DXIOLIBV1_PTSECONDLYDETECTINGWORKAROUND_FILECODE     (0xA142)

#define LIBRARY_NBIOIDSHOOKRNLIB_PEI_NBIOIDSHOOKRNLIBPEI_FILECODE    (0xA143)
#define LIBRARY_NBIOIDSHOOKRNLIB_DXE_NBIOIDSHOOKRNLIBDXE_FILECODE    (0xA144)

#define NBIO_LIBRARY_IVRSLIBV2_NBIOIVRSTABLE_FILECODE                (0xA145)
#define NBIO_LIBRARY_IVRSLIBV2_IVRSDEBUGDUMP_FILECODE                (0xA146)
#define NBIO_LIBRARY_IVRSLIBV2_NBIOIVMDLIB_FILECODE                  (0xA147)
#define NBIO_LIBRARY_IVRSLIBV2_NBIOIVRSLIB_FILECODE                  (0xA148)

#define LIBRARY_NBIOIDSHOOKRMBLIB_PEI_NBIOIDSHOOKRMBLIBPEI_FILECODE  (0xA149)
#define LIBRARY_NBIOIDSHOOKRMBLIB_DXE_NBIOIDSHOOKRMBLIBDXE_FILECODE  (0xA14A)
#define LIBRARY_PCIEMISCCOMMLIB_PCIEOBFFLIB_FILECODE                 (0xA14B)
#define LIBRARY_NBIOSMURSLIB_NBIOSMURSLIB_FILECODE                   (0xA14C)

#define LIBRARY_PCIEMISCCOMMLIB_PCIEATOMICOPSLIB_FILECODE            (0xA14D)
#define LIBRARY_PCIEMISCCOMMLIB_PCIEECRCLIB_FILECODE                 (0xA14E)

#define NBIO_LIBRARY_MPIOINITLIB_MPIOUBMTOPOLOGY_FILECODE            (0xA14F)
#define NBIO_LIBRARY_MPIOLIB_MPIOLIB_FILECODE                        (0xA150)
#define NBIO_LIBRARY_MPIOINITLIB_MPIOINITFLOW_FILECODE               (0xA151)
#define NBIO_LIBRARY_MPIOINITLIB_MPIOANCDATAV1_FILECODE              (0xA152)
#define NBIO_LIBRARY_MPIOINITLIB_MPIODEBUGOUT_FILECODE               (0xA153)
#define NBIO_LIBRARY_MPIOINITLIB_MPIOMAPPINGRESULTS_FILECODE         (0xA154)
#define NBIO_LIBRARY_MPIOINITLIB_MPIOPARSER_FILECODE                 (0xA155)
#define NBIO_LIBRARY_MPIOINITLIB_MPIOPROCESSRESULTS_FILECODE         (0xA156)
#define NBIO_LIBRARY_MPIOINITLIB_MPIOSUPPORTFUNCTIONS_FILECODE       (0xA157)
#define NBIO_LIBRARY_MPIOINITLIB_MPIOTRAININGRESULTS_FILECODE        (0xA158)
#define NBIO_LIBRARY_MPIOINITLIB_MPIOEARLYTRAIN_FILECODE             (0xA159)

#define LIBRARY_NBIOIDSHOOKRPLLIB_PEI_NBIOIDSHOOKRPLLIBPEI_FILECODE  (0xA15A)
#define LIBRARY_NBIOIDSHOOKRPLLIB_DXE_NBIOIDSHOOKRPLLIBDXE_FILECODE  (0xA15B)
#define NBIO_NBIOALIB_RPL_DXE_AMDNBIOALIBCALLBACK_FILECODE           (0xA15C)
#define NBIO_NBIOALIB_RPL_DXE_AMDNBIOALIBRMBDXE_FILECODE             (0xA15D)

#define LIBRARY_GNBPCISEGMENTACCLIB_GNBPCISEGMENTACCLIB_FILECODE     (0xA15E)

#define LIBRARY_NBIOIDSHOOKMDNLIB_PEI_NBIOIDSHOOKMDNLIBPEI_FILECODE  (0xA160)
#define LIBRARY_NBIOIDSHOOKMDNLIB_DXE_NBIOIDSHOOKMDNLIBDXE_FILECODE  (0xA161)

#define LIBRARY_NBIOSMUV14LIB_NBIOSMUV14LIB_FILECODE                 (0xA162)

#define LIBRARY_NBIOIDSHOOKPHXLIB_DXE_NBIOIDSHOOKPHXLIBDXE_FILECODE  (0xA163)
#define LIBRARY_NBIOIDSHOOKPHXLIB_PEI_NBIOIDSHOOKPHXLIBPEI_FILECODE  (0xA164)
#define NBIO_NBIOALIB_PHX_DXE_AMDNBIOALIBCALLBACK_FILECODE           (0xA165)

#define LIBRARY_NBIOIDSHOOKSTXKRKLIB_PEI_NBIOIDSHOOKSTXKRKLIBPEI_FILECODE  (0xA166)
#define LIBRARY_NBIOIDSHOOKSTXKRKLIB_DXE_NBIOIDSHOOKSTXKRKLIBDXE_FILECODE  (0xA167)
#define LIBRARY_NBIOIDSHOOKSTPLIB_PEI_NBIOIDSHOOKSTPLIBPEI_FILECODE  (0xA168)
#define LIBRARY_NBIOIDSHOOKSTPLIB_DXE_NBIOIDSHOOKSTPLIBDXE_FILECODE  (0xA169)
#define LIBRARY_NBIOSMUMI3LIB_NBIOSMUMI3LIB_FILECODE                 (0xA16A)
#define NBIO_LIBRARY_MPIOLIBV2_MPIOLIBV2_FILECODE                    (0xA16B)
#define NBIO_LIBRARY_MPIOLIBV2_UNITTEST_MPIOLIBV2UNITTEST_FILECODE   (0xA16C)
#define LIBRARY_NBIOPMCSRTABLELIB_NBIOPMCSRTABLELIB_FILECODE         (0xA16D)
#define LIBRARY_NBIOIDSHOOKSTXHLIB_PEI_NBIOIDSHOOKSTXHLIBPEI_FILECODE  (0xA16E)
#define LIBRARY_NBIOIDSHOOKSTXHLIB_DXE_NBIOIDSHOOKSTXHLIBDXE_FILECODE  (0xA16F)

#define NBIO_NBIOBASE_AMDNBIOBASECZPEI_AMDNBIOBASECZPEI_FILECODE          (0xA1F0)
#define NBIO_NBIOBASE_AMDNBIOBASECZDXE_AMDNBIOBASECZDXE_FILECODE          (0xA1F1)
#define NBIO_SMU_AMDNBIOSMUV8PEI_AMDNBIOSMUV8PEI_FILECODE                 (0xA1F2)
#define NBIO_SMU_AMDNBIOSMUV8DXE_AMDNBIOSMUV8DXE_FILECODE                 (0xA1F3)

#define NBIO_NBIOBASE_AMDNBIOBASEZPDXE_AMDNBIOBASEZPDXE_FILECODE          (0xA200)
#define NBIO_SMU_AMDNBIOSMUV10DXE_SMU10BIOSINTERFACETABLE_FILECODE        (0xA201)
#define NBIO_SMU_AMDNBIOSMUV12DXE_SMU12BIOSINTERFACETABLE_FILECODE        (0xA202)
#define NBIO_SMU_AMDNBIOSMUV13DXE_SMU13BIOSINTERFACETABLE_FILECODE        (0xA203)

#define NBIO_NBIOBASE_AMDNBIOBASEZPPEI_AMDNBIOBASEZPPEI_FILECODE          (0xA210)
#define NBIO_NBIOBASE_AMDNBIOBASEZPPEI_GNBIOMMUTABLESZP_FILECODE          (0xA211)
#define NBIO_NBIOBASE_AMDNBIOBASEZPPEI_NBIOTABLESZP_FILECODE              (0xA212)
#define NBIO_NBIOBASE_AMDNBIOBASEZPPEI_NBIOTOPMEMZP_FILECODE              (0xA213)
#define NBIO_NBIOBASE_AMDNBIOBASEZPPEI_PCIECOMPLEXDATAZP_FILECODE         (0xA214)
#define NBIO_NBIOBASE_AMDNBIOBASEZPPEI_NBIORASCONTROLZP_FILECODE          (0xA215)
#define NBIO_NBIOBASE_AMDNBIOBASEZPPEI_NBIORASWORKAROUNDZP_FILECODE       (0xA216)

#define NBIO_PCIE_AMDNBIOPCIEZPPEI_AMDNBIOPCIEZPPEI_FILECODE              (0xA220)
#define NBIO_PCIE_AMDNBIOPCIEZPPEI_DXIOINITZP_FILECODE                    (0xA221)
#define NBIO_PCIE_AMDNBIOPCIEZPPEI_DXIOWORKAROUND_FILECODE                (0xA222)
#define NBIO_PCIE_AMDNBIOPCIEZPPEI_HIDEPORTSZP_FILECODE                   (0xA223)
#define NBIO_PCIE_AMDNBIOPCIEZPPEI_PCIEREMAPZP_FILECODE                   (0xA224)
#define NBIO_PCIE_AMDNBIOPCIEZPPEI_DEVICECONTROLZP_FILECODE               (0xA225)
#define NBIO_PCIE_AMDNBIOPCIEZPPEI_PCIEHOTPLUGZP_FILECODE                 (0xA226)

#define NBIO_PCIE_AMDNBIOPCIERVPEI_AMDNBIOPCIERVPEI_FILECODE              (0xA227)
#define NBIO_PCIE_AMDNBIOPCIERVPEI_DXIOINITRV_FILECODE                    (0xA228)
#define NBIO_PCIE_AMDNBIOPCIERVPEI_HIDEPORTSRV_FILECODE                   (0xA229)
#define NBIO_PCIE_AMDNBIOPCIERVPEI_PCIEREMAPRV_FILECODE                   (0xA22A)
#define NBIO_PCIE_AMDNBIOPCIERVPEI_DEVICECONTROLRV_FILECODE               (0xA22B)
#define NBIO_PCIE_AMDNBIOPCIERVPEI_PCIEHOTPLUGRV_FILECODE                 (0xA22C)
#define NBIO_PCIE_AMDNBIOPCIERVPEI_DXIOCFGPOINTSRV_FILECODE               (0xA22D)
#define NBIO_PCIE_AMDNBIOSPCIERVDXE_AMDPHYCLKCNTRLRV_FILECODE             (0xA22E)

#define NBIO_IOMMU_AMDNBIOIOMMUZPPEI_AMDNBIOIOMMUZPPEI_FILECODE           (0xA22F)

#define NBIO_PCIE_AMDNBIOPCIEZPDXE_AMDNBIOPCIEZPDXE_FILECODE              (0xA230)
#define NBIO_PCIE_AMDNBIOPCIEZPDXE_AMDNBIOPCIEAERZP_FILECODE              (0xA231)
#define NBIO_PCIE_AMDNBIOPCIEZPDXE_AMDNBIOPCIESRISZP_FILECODE             (0xA232)
#define NBIO_PCIE_AMDNBIOPCIEZPDXE_AMDNBIOPCIECLKREQZP_FILECODE           (0xA233)
#define NBIO_PCIE_AMDNBIOPCIEZPDXE_AMDNBIOEXITLATENCYZP_FILECODE          (0xA234)

#define NBIO_PCIE_AMDNBIOPCIERVDXE_AMDNBIOPCIERVDXE_FILECODE              (0xA235)
#define NBIO_PCIE_AMDNBIOPCIERVDXE_AMDNBIOPCIEAERRV_FILECODE              (0xA236)
#define NBIO_PCIE_AMDNBIOPCIERVDXE_AMDNBIOPCIESRISRV_FILECODE             (0xA237)
#define NBIO_PCIE_AMDNBIOPCIERVDXE_AMDNBIOPCIECLKREQRV_FILECODE           (0xA238)

#define NBIO_IOMMU_AMDNBIOIOMMURVDXE_AMDNBIOIOMMURVDXE_FILECODE           (0xA239)
#define NBIO_IOMMU_AMDNBIOIOMMUZPDXE_AMDNBIOIOMMUZPDXE_FILECODE           (0xA240)
#define NBIO_SMU_AMDNBIOSMUV9DXE_AMDNBIOSMUV9DXE_FILECODE                 (0xA241)
#define NBIO_IOMMU_AMDNBIOIOMMUZPDXE_MSIXWORKAROUND_FILECODE              (0xA242)
#define NBIO_IOMMU_AMDNBIOIOMMURVDXE_MSIXWORKAROUND_FILECODE              (0xA243)
#define NBIO_PCIE_AMDNBIOPCIERVDXE_AMDNBIOEXITLATENCYRV_FILECODE          (0xA244)
#define NBIO_PCIE_AMDNBIOPCIEZPPEI_DPCSTATUSREPORT_FILECODE               (0xA245)
#define NBIO_PCIE_AMDNBIOPCIERNDXE_AMDNBIOEXITLATENCYRV_FILECODE          (0xA246)

#define NBIO_SMU_AMDNBIOSMUV9PEI_AMDNBIOSMUV9PEI_FILECODE                 (0xA250)
#define NBIO_SMU_AMDNBIOSMUV9PEI_SMUEARLYINIT_FILECODE                    (0xA251)
#define NBIO_SMU_AMDNBIOSMUV9PEI_AMDSMUSERVICESV9PPI_FILECODE             (0xA252)
#define NBIO_SMU_AMDNBIOSMUV9PEI_AMDCORELAUNCHSERVICEPPI_FILECODE         (0xA253)

#define NBIO_NBIOBASE_AMDNBIOBASERVDXE_AMDNBIOBASERVDXE_FILECODE          (0xA280)

#define NBIO_NBIOBASE_AMDNBIOBASERVPEI_AMDNBIOBASERVPEI_FILECODE          (0xA290)
#define NBIO_NBIOBASE_AMDNBIOBASERVPEI_GNBIOMMUTABLESRV_FILECODE          (0xA291)
#define NBIO_NBIOBASE_AMDNBIOBASERVPEI_NBIOTABLESRV_FILECODE              (0xA292)
#define NBIO_NBIOBASE_AMDNBIOBASERVPEI_NBIOTOPMEMRV_FILECODE              (0xA293)
#define NBIO_NBIOBASE_AMDNBIOBASERVPEI_PCIECOMPLEXDATARV_FILECODE         (0xA294)
#define NBIO_NBIOBASE_AMDNBIOBASERVPEI_NBIORASCONTROLRV_FILECODE          (0xA295)

#define NBIO_EARLYPHASE_AMDNBIOEARLYPHASERVPEI_AMDNBIOEARLYPHASERVPEI_FILECODE   (0xA297)
#define NBIO_EARLYPHASE_AMDNBIOEARLYPHASERNPEI_AMDNBIOEARLYPHASERNPEI_FILECODE   (0xA298)
#define NBIO_EARLYPHASE_AMDNBIOEARLYPHASERMBPEI_AMDNBIOEARLYPHASERMBPEI_FILECODE (0xA299)
#define NBIO_EARLYPHASE_AMDNBIOEARLYPHASEMDNPEI_AMDNBIOEARLYPHASEMDNPEI_FILECODE (0xA29A)

#define NBIO_SMU_AMDNBIOSMUV10DXE_AMDNBIOSMUV10DXE_FILECODE          (0xA2A0)

#define NBIO_SMU_AMDNBIOSMUV10PEI_AMDNBIOSMUV10PEI_FILECODE          (0xA2B0)
#define NBIO_SMU_AMDNBIOSMUV10PEI_SMUEARLYINIT_FILECODE              (0xA2B1)
#define NBIO_SMU_AMDNBIOSMUV10PEI_AMDSMUSERVICESV10PPI_FILECODE      (0xA2B2)
#define NBIO_SMU_AMDNBIOSMUV10PEI_AMDCORELAUNCHSERVICEPPI_FILECODE   (0xA2B3)

#define NBIO_GFX_AMDNBIOGFXRVPEI_AMDNBIOGFXRVPEI_FILECODE            (0xA2C0)
#define NBIO_GFX_AMDNBIOGFXRVPEI_GFXTABLESRV_FILECODE                (0xA2C1)

#define NBIO_GFX_AMDNBIOGFXRVDXE_AMDNBIOGFXRVDXE_FILECODE            (0xA2D0)

#define NBIO_NBIOALIB_AMDNBIOALIBZPDXE_AMDNBIOALIBZPDXE_FILECODE          (0xA2E0)
#define NBIO_NBIOALIB_AMDNBIOALIBZPDXE_AMDNBIOALIBCALLBACK_FILECODE       (0xA2E1)
#define NBIO_NBIOALIB_AMDNBIOALIBRVDXE_AMDNBIOALIBRVDXE_FILECODE          (0xA2E2)
#define NBIO_NBIOALIB_AMDNBIOALIBRVDXE_AMDNBIOALIBCALLBACK_FILECODE       (0xA2E3)
#define NBIO_NBIOALIB_RN_DXE_AMDNBIOALIBRNDXE_FILECODE                    (0xA2E4)
#define NBIO_NBIOALIB_RN_DXE_AMDNBIOALIBCALLBACK_FILECODE                 (0xA2E5)
#define NBIO_NBIOALIB_RMB_DXE_AMDNBIOALIBRMBDXE_FILECODE                  (0xA2E6)
#define NBIO_NBIOALIB_RMB_DXE_AMDNBIOALIBCALLBACK_FILECODE                (0xA2E7)
#define NBIO_NBIOALIB_MDN_DXE_AMDNBIOALIBMDNDXE_FILECODE                  (0xA2E8)
#define NBIO_NBIOALIB_MDN_DXE_AMDNBIOALIBCALLBACK_FILECODE                (0xA2E9)

#define NBIO_SMU_SSP_PEI_SMUDRIVERENTRY_FILECODE                          (0xA300)
#define NBIO_SMU_SSP_PEI_CCXTOPOLOGYPPI_FILECODE                          (0xA301)
#define NBIO_SMU_SSP_PEI_EARLYINITFEATURE_FILECODE                        (0xA302)
#define NBIO_SMU_SSP_PEI_SMUSERVICESPPI_FILECODE                          (0xA303)

#define NBIO_SMU_SSP_DXE_DRIVERENTRY_FILECODE                             (0xA304)
#define NBIO_SMU_SSP_DXE_FINALINITCALLBACK_FILECODE                       (0xA305)
#define NBIO_SMU_SSP_DXE_AFTERCCXCALLBACK_FILECODE                        (0xA306)
#define NBIO_SMU_SSP_DXE_CCXTOPOLOGYPROTOCOL_FILECODE                     (0xA307)
#define NBIO_SMU_SSP_DXE_SERVERHOTPLUGFEAT_FILECODE                       (0xA308)
#define NBIO_SMU_SSP_DXE_SMUSERVICESPROTOCOL_FILECODE                     (0xA309)
#define NBIO_SMU_SSP_DXE_SMUSUPPORTFUNCTIONS_FILECODE                     (0xA30A)
#define NBIO_SMU_SSP_DXE_SMUINFOCOLLECTION_FILECODE                       (0xA30B)
#define NBIO_SMU_SSP_DXE_CPPCSERVICESPROTOCOL_FILECODE                    (0xA30C)

#define NBIO_IOAPIC_SSP_PEI_AMDNBIOIOAPIC_FILECODE                        (0xA310)
#define NBIO_IOMMU_SSP_DXE_AMDNBIOIOMMU_FILECODE                          (0xA311)
#define NBIO_IOMMU_SSP_DXE_MSIXWORKAROUND_FILECODE                        (0xA312)
#define NBIO_IOMMU_SSP_PEI_AMDNBIOIOMMU_FILECODE                          (0xA313)
#define NBIO_NBIOBASE_SSP_DXE_AMDNBIOBASE_FILECODE                        (0xA314)
#define NBIO_NBIOBASE_SSP_PEI_AMDNBIOBASE_FILECODE                        (0xA315)
#define NBIO_NBIOBASE_SSP_PEI_GNBIOMMUTABLES_FILECODE                     (0xA316)
#define NBIO_NBIOBASE_SSP_PEI_NBIORASWORKAROUND_FILECODE                  (0xA317)
#define NBIO_NBIOBASE_SSP_PEI_NBIOTABLES_FILECODE                         (0xA318)
#define NBIO_NBIOBASE_SSP_PEI_NBIOTOPMEM_FILECODE                         (0xA319)
#define NBIO_PCIE_SSP_DXE_AMDNBIOPCIEAER_FILECODE                         (0xA31A)
#define NBIO_PCIE_SSP_DXE_AMDNBIOPCIECLKREQ_FILECODE                      (0xA31B)
#define NBIO_PCIE_SSP_DXE_AMDNBIOPCIEDXE_FILECODE                         (0xA31C)
#define NBIO_PCIE_SSP_DXE_AMDNBIOPCIESRIS_FILECODE                        (0xA31D)
#define NBIO_PCIE_SSP_DXE_NBIORASCONTROL_FILECODE                         (0xA31E)
#define NBIO_PCIE_SSP_PEI_AMDNBIOPCIEPEI_FILECODE                         (0xA31F)
#define NBIO_PCIE_SSP_PEI_DEVICECONTROL_FILECODE                          (0xA320)
#define NBIO_PCIE_SSP_PEI_DPCSTATUSREPORT_FILECODE                        (0xA321)
#define NBIO_PCIE_SSP_PEI_DXIOCFGPOINTS_FILECODE                          (0xA322)
#define NBIO_PCIE_SSP_PEI_DXIOINIT_FILECODE                               (0xA323)
#define NBIO_PCIE_SSP_PEI_HIDEPORTS_FILECODE                              (0xA324)
#define NBIO_PCIE_SSP_PEI_NTBFEATURE_FILECODE                             (0xA325)
#define NBIO_PCIE_SSP_PEI_PCIEHOTPLUG_FILECODE                            (0xA326)
#define NBIO_PCIE_SSP_PEI_PCIEREMAP_FILECODE                              (0xA327)
#define NBIO_PCIE_SSP_PEI_DXIOTOPOLOGY_FILECODE                           (0xA328)
#define NBIO_PCIE_SSP_PEI_PKGTYPEFIXUPS_FILECODE                          (0xA329)
#define NBIO_NBIOBASE_SSP_PEI_NBIOTABLESROME_FILECODE                     (0xA32A)
#define NBIO_NBIOBASE_SSP_PEI_NBIOTABLESMTS_FILECODE                      (0xA32B)
#define NBIO_PCIE_SSP_DXE_NBIOESMCONTROL_FILECODE                         (0xA32C)
#define NBIO_LIBRARY_IOMMUDMARLIB_DXE_IOMMUPROTOCOL_FILECODE              (0xA32D)
#define NBIO_LIBRARY_IOMMUDMARLIB_DXE_AMDIOMMUMEMORYINIT_FILECODE         (0xA32E)
#define NBIO_LIBRARY_IOMMUDMARLIB_DXE_AMDIOMMUDMARLIB_FILECODE            (0xA32F)
#define NBIO_LIBRARY_IOMMUDMARLIB_PEI_IOMMUPPI_FILECODE                   (0xA330)
#define NBIO_LIBRARY_IOMMUDMARLIB_PEI_AMDIOMMUMEMORYINIT_FILECODE         (0xA331)
#define NBIO_LIBRARY_IOMMUDMARLIB_PEI_AMDIOMMUDMARLIB_FILECODE            (0xA332)
#define NBIO_NBIOBASE_SSP_PEI_PCIECOMPLEXDATA_FILECODE                    (0xA333)
#define NBIO_PCIE_SSP_PEI_EARLYLINKPPI_FILECODE                           (0xA334)
#define NBIO_LIBRARY_IVRSDEVICEDFLTLIB_IVMDINFOTABLE_FILECODE             (0xA335)
#define NBIO_LIBRARY_IVRSDEVICEDFLTLIB_IVRSDEVICEINFOLIB_FILECODE         (0xA336)
#define NBIO_LIBRARY_IVRSLIBV2_IVRSSBDEVICES_FILECODE                     (0xA337)
#define NBIO_LIBRARY_IVRSLIBV2_NBIOIOAPICINIT_FILECODE                    (0xA338)
#define NBIO_LIBRARY_PCIECOMPLEXDEFAULTSLIB_PCIECOMPLEXDEFAULTSLIB_FILECODE (0xA339)
#define LIBRARY_NBIOCLKREQCONTROLLIBNULL_NBIOCLKREQCONTROLLIBNULL_FILECODE  (0xA33A)
#define LIBRARY_NBIOIDSHOOKBXBLIB_DXE_NBIOIDSHOOKBXBLIBDXE_FILECODE       (0xA33B)
#define LIBRARY_NBIOIDSHOOKBXBLIB_PEI_NBIOIDSHOOKBXBLIBPEI_FILECODE       (0xA33C)
#define LIBRARY_NBIOIDSHOOKSSPLIB_DXE_NBIOIDSHOOKSSPFANTABLE_FILECODE     (0xA33D)
#define LIBRARY_NBIOIDSHOOKSSPLIB_DXE_NBIOIDSHOOKVMRFANTABLE_FILECODE     (0xA33E)
#define LIBRARY_PCIEMISCCOMMLIB_PCIEAERSETTING_FILECODE                   (0xA33F)
#define LIBRARY_PCIEMISCCOMMLIB_PCIEASPML1SSBLACKLISTLIB_FILECODE         (0xA340)
#define LIBRARY_PCIEMISCCOMMLIB_PCIEPREFERIOORDERLIB_FILECODE             (0xA341)
#define LIBRARY_BXBNBIO_BXBACCESSLIB_BXBTABLEBLAST_FILECODE               (0xA342)
#define LIBRARY_BXBNBIO_BXBINITLIBV1_BXBINITLIB_FILECODE                  (0xA343)
#define LIBRARY_BXBNBIO_BXBINITLIBV1_BXBUPLINKSPEED_FILECODE              (0xA344)
#define LIBRARY_BXBNBIO_BXBINITLIBV1_BXBCOMPLEXDATA_FILECODE              (0xA345)
#define LIBRARY_BXBNBIO_BXBINITLIBV1_BXBSOCLIBZP_BXBSOCLIB_FILECODE       (0xA346)
#define LIBRARY_BXBNBIO_BXBINITLIBV1_BXBSOCLIBSSP_BXBSOCLIB_FILECODE      (0xA347)
#define LIBRARY_BXBNBIO_BXBINITLIBV1_BXBSOCLIBRV_BXBSOCLIB_FILECODE       (0xA348)
#define LIBRARY_BXBNBIO_BXBINITLIBV1_BXBSOCLIBRN_BXBSOCLIB_FILECODE       (0xA349)
#define LIBRARY_BXBNBIO_BXBINITLIBV1_BXBSOCLIBRMB_BXBSOCLIB_FILECODE      (0xA34A)
#define LIBRARY_BXBNBIO_BXBINITLIBV1_BXBSOCLIBRPL_BXBSOCLIB_FILECODE      (0xA34B)
#define LIBRARY_BXBNBIO_BXBINITLIBV1_BXBSOCLIBPHX_BXBSOCLIB_FILECODE      (0xA34C)
#define NBIO_LIBRARY_COLLECTNBIFPORTINFOLIBNULL_COLLECTNBIFPORTINFOLIBNULL_FILECODE     (0xA34D)
#define LIBRARY_PCIEMISCCOMMLIB_PCIESAVESUBBUSLIB_FILECODE                (0xA34E)
#define NBIO_LIBRARY_IOMMUDMARLIB_DXE_IOMMUDMARPROTOCOL_FILECODE          (0xA34F)

//Renoir
#define NBIO_SMU_RN_PEI_SMUDRIVERENTRY_FILECODE                          (0xA400)
#define NBIO_SMU_RN_PEI_CCXTOPOLOGYPPI_FILECODE                          (0xA401)
#define NBIO_SMU_RN_PEI_EARLYINITFEATURE_FILECODE                        (0xA402)
#define NBIO_SMU_RN_PEI_SMUSERVICESPPI_FILECODE                          (0xA403)

#define NBIO_SMU_RN_DXE_DRIVERENTRY_FILECODE                             (0xA404)
#define NBIO_SMU_RN_DXE_FINALINITCALLBACK_FILECODE                       (0xA405)
#define NBIO_SMU_RN_DXE_AFTERCCXCALLBACK_FILECODE                        (0xA406)
#define NBIO_SMU_RN_DXE_CCXTOPOLOGYPROTOCOL_FILECODE                     (0xA407)
#define NBIO_SMU_RN_DXE_SERVERHOTPLUGFEAT_FILECODE                       (0xA408)
#define NBIO_SMU_RN_DXE_SMUSERVICESPROTOCOL_FILECODE                     (0xA409)
#define NBIO_SMU_RN_DXE_SMUSUPPORTFUNCTIONS_FILECODE                     (0xA40A)
#define NBIO_SMU_RN_DXE_SMUINFOCOLLECTION_FILECODE                       (0xA40B)
#define NBIO_SMU_RN_DXE_CPPCSERVICESPROTOCOL_FILECODE                    (0xA40C)

#define NBIO_IOAPIC_RN_PEI_AMDNBIOIOAPIC_FILECODE                        (0xA410)
#define NBIO_IOMMU_RN_DXE_AMDNBIOIOMMU_FILECODE                          (0xA411)
#define NBIO_IOMMU_RN_PEI_AMDNBIOIOMMU_FILECODE                          (0xA413)
#define NBIO_NBIOBASE_RN_DXE_AMDNBIOBASE_FILECODE                        (0xA414)
#define NBIO_NBIOBASE_RN_PEI_AMDNBIOBASE_FILECODE                        (0xA415)
#define NBIO_NBIOBASE_RN_PEI_GNBIOMMUTABLES_FILECODE                     (0xA416)
#define NBIO_NBIOBASE_RN_PEI_NBIORASWORKAROUND_FILECODE                  (0xA417)
#define NBIO_NBIOBASE_RN_PEI_NBIOTABLES_FILECODE                         (0xA418)
#define NBIO_NBIOBASE_RN_PEI_NBIOTOPMEM_FILECODE                         (0xA419)
#define NBIO_PCIE_RN_DXE_AMDNBIOPCIEAER_FILECODE                         (0xA41A)
#define NBIO_PCIE_RN_DXE_AMDNBIOPCIECLKREQ_FILECODE                      (0xA41B)
#define NBIO_PCIE_RN_DXE_AMDNBIOPCIEDXE_FILECODE                         (0xA41C)
#define NBIO_PCIE_RN_DXE_AMDNBIOPCIESRIS_FILECODE                        (0xA41D)
#define NBIO_PCIE_RN_DXE_NBIORASCONTROL_FILECODE                         (0xA41E)
#define NBIO_PCIE_RN_PEI_AMDNBIOPCIEPEI_FILECODE                         (0xA41F)
#define NBIO_PCIE_RN_PEI_DEVICECONTROL_FILECODE                          (0xA420)
#define NBIO_PCIE_RN_PEI_DPCSTATUSREPORT_FILECODE                        (0xA421)
#define NBIO_PCIE_RN_PEI_DXIOCFGPOINTS_FILECODE                          (0xA422)
#define NBIO_PCIE_RN_PEI_DXIOINIT_FILECODE                               (0xA423)
#define NBIO_PCIE_RN_PEI_HIDEPORTS_FILECODE                              (0xA424)
#define NBIO_PCIE_RN_PEI_PCIEREMAP_FILECODE                              (0xA427)
#define NBIO_PCIE_RN_PEI_DXIOTOPOLOGY_FILECODE                           (0xA428)
#define NBIO_PCIE_RN_PEI_PCIEHOTPLUG_FILECODE                            (0xA429)
#define NBIO_PCIE_RN_PEI_EARLYLINKPPI_FILECODE                           (0xA42A)
#define NBIO_PCIE_RN_PEI_PKGTYPEFIXUPS_FILECODE                          (0xA42B)
#define NBIO_NBIOBASE_RN_PEI_PCIECOMPLEXDATA_FILECODE                    (0xA42C)

//Rembrandt
#define NBIO_GFX_RMB_PEI_GFXINFOCOLLECTION_FILECODE                      (0xA43C)
#define NBIO_PCIE_RMB_COMMON_PCIESTRAPS_FILECODE                         (0xA43D)
#define NBIO_SMU_RMB_DXE_PKGTYPEFIXUPS_FILECODE                          (0xA43E)
#define NBIO_SMU_RMB_DXE_SMU13BIOSINTERFACETABLE_FILECODE                (0xA43F)
#define NBIO_SMU_RMB_PEI_SMUDRIVERENTRY_FILECODE                         (0xA440)
#define NBIO_SMU_RMB_PEI_CCXTOPOLOGYPPI_FILECODE                         (0xA441)
#define NBIO_SMU_RMB_PEI_EARLYINITFEATURE_FILECODE                       (0xA442)
#define NBIO_SMU_RMB_PEI_SMUSERVICESPPI_FILECODE                         (0xA443)

#define NBIO_SMU_RMB_DXE_SMUDRIVERENTRY_FILECODE                         (0xA444)
#define NBIO_SMU_RMB_DXE_FINALINITCALLBACK_FILECODE                      (0xA445)
#define NBIO_SMU_RMB_DXE_AFTERCCXCALLBACK_FILECODE                       (0xA446)
#define NBIO_SMU_RMB_DXE_CCXTOPOLOGYPROTOCOL_FILECODE                    (0xA447)
#define NBIO_SMU_RMB_DXE_SERVERHOTPLUGFEAT_FILECODE                      (0xA448)
#define NBIO_SMU_RMB_DXE_SMUSERVICESPROTOCOL_FILECODE                    (0xA449)
#define NBIO_SMU_RMB_DXE_SMUSUPPORTFUNCTIONS_FILECODE                    (0xA44A)
#define NBIO_SMU_RMB_DXE_INFOCOLLECTION_FILECODE                         (0xA44B)
#define NBIO_SMU_RMB_DXE_CPPCSERVICESPROTOCOL_FILECODE                   (0xA44C)
#define NBIO_GFX_RMB_DXE_DRIVERENTRY_FILECODE                            (0xA44D)
#define NBIO_GFX_RMB_PEI_GFXENUMCONNECTORS_FILECODE                      (0xA44F)
#define NBIO_GFX_RMB_PEI_GFXDRIVERENTRY_FILECODE                         (0xA450)
#define NBIO_IOAPIC_RMB_PEI_AMDNBIOIOAPIC_FILECODE                       (0xA451)
#define NBIO_IOMMU_RMB_DXE_AMDNBIOIOMMU_FILECODE                         (0xA452)
#define NBIO_IOMMU_RMB_PEI_IOMMUDRIVERENTRY_FILECODE                     (0xA453)
#define NBIO_NBIOBASE_RMB_DXE_AMDNBIOBASE_FILECODE                       (0xA454)
#define NBIO_NBIOBASE_RMB_PEI_AMDNBIOBASE_FILECODE                       (0xA455)
#define NBIO_NBIOBASE_RMB_PEI_GNBIOMMUTABLES_FILECODE                    (0xA456)
#define NBIO_NBIOBASE_RMB_PEI_NBIORASWORKAROUND_FILECODE                 (0xA457)
#define NBIO_NBIOBASE_RMB_PEI_NBIOTABLES_FILECODE                        (0xA458)
#define NBIO_NBIOBASE_RMB_PEI_NBIOTOPMEM_FILECODE                        (0xA459)
#define NBIO_PCIE_RMB_DXE_AMDNBIOPCIEAER_FILECODE                        (0xA45A)
#define NBIO_PCIE_RMB_DXE_AMDNBIOPCIECLKREQ_FILECODE                     (0xA45B)
#define NBIO_PCIE_RMB_DXE_AMDNBIOPCIEDXE_FILECODE                        (0xA45C)
#define NBIO_PCIE_RMB_DXE_AMDNBIOPCIESRIS_FILECODE                       (0xA45D)
#define NBIO_PCIE_RMB_PEI_DXIOUSB4_FILECODE                              (0xA45E)
#define NBIO_PCIE_RMB_PEI_AMDNBIOPCIEPEI_FILECODE                        (0xA45F)
#define NBIO_PCIE_RMB_PEI_DEVICECONTROL_FILECODE                         (0xA460)
#define NBIO_PCIE_RMB_PEI_DPCSTATUSREPORT_FILECODE                       (0xA461)
#define NBIO_PCIE_RMB_PEI_DXIOCFGPOINTS_FILECODE                         (0xA462)
#define NBIO_PCIE_RMB_PEI_DXIOINIT_FILECODE                              (0xA463)
#define NBIO_PCIE_RMB_PEI_HIDEPORTS_FILECODE                             (0xA464)
#define NBIO_PCIE_RMB_PEI_PCIEREMAP_FILECODE                             (0xA467)
#define NBIO_PCIE_RMB_PEI_DXIOTOPOLOGY_FILECODE                          (0xA468)
#define NBIO_PCIE_RMB_PEI_PCIEHOTPLUG_FILECODE                           (0xA469)
#define NBIO_PCIE_RMB_PEI_EARLYLINKPPI_FILECODE                          (0xA46A)
#define NBIO_PCIE_RMB_PEI_PKGTYPEFIXUPS_FILECODE                         (0xA46B)
#define NBIO_PCIE_RMB_DXE_AMDAPLUSA_FILECODE                             (0xA46C)
#define NBIO_PCIE_RMB_DXE_AMDNBIOEXITLATENCY_FILECODE                    (0xA46D)
#define NBIO_PCIE_RMB_DXE_AMDNBIOPCIEASPM_FILECODE                       (0xA46E)
#define NBIO_PCIE_RMB_DXE_AMDPSPPRMB_FILECODE                            (0xA46F)

//Raphael

#define NBIO_GFX_RPL_PEI_GFXENUMCONNECTORS_FILECODE                      (0xA430)
#define NBIO_GFX_RPL_PEI_GFXINFOCOLLECTION_FILECODE                      (0XA431)
#define NBIO_IOMMU_RPL_PEI_IOMMUDRIVERENTRY_FILECODE                     (0XA432)
#define NBIO_NBIOBASE_RPL_DXE_MPIOSERVICESPROTOCOL_FILECODE              (0XA433)
#define NBIO_PCIE_RPL_DXE_AMDPCIELTRRPL_FILECODE                         (0XA434)
#define NBIO_PCIE_RPL_DXE_PCIEDGPUONLYMODE_FILECODE                      (0XA435)
#define NBIO_SMU_RPL_PEI_SMUDRIVERENTRY_FILECODE                         (0xA470)
#define NBIO_SMU_RPL_PEI_CCXTOPOLOGYPPI_FILECODE                         (0xA471)
#define NBIO_SMU_RPL_PEI_EARLYINITFEATURE_FILECODE                       (0xA472)
#define NBIO_SMU_RPL_PEI_SMUSERVICESPPI_FILECODE                         (0xA473)

#define NBIO_SMU_RPL_DXE_DRIVERENTRY_FILECODE                            (0xA474)
#define NBIO_SMU_RPL_DXE_FINALINITCALLBACK_FILECODE                      (0xA475)
#define NBIO_SMU_RPL_DXE_AFTERCCXCALLBACK_FILECODE                       (0xA476)
#define NBIO_SMU_RPL_DXE_CCXTOPOLOGYPROTOCOL_FILECODE                    (0xA477)
#define NBIO_SMU_RPL_DXE_SERVERHOTPLUGFEAT_FILECODE                      (0xA478)
#define NBIO_SMU_RPL_DXE_SMUSERVICESPROTOCOL_FILECODE                    (0xA479)
#define NBIO_SMU_RPL_DXE_SMUSUPPORTFUNCTIONS_FILECODE                    (0xA47A)
#define NBIO_SMU_RPL_DXE_SMUINFOCOLLECTION_FILECODE                      (0xA47B)
#define NBIO_SMU_RPL_DXE_CPPCSERVICESPROTOCOL_FILECODE                   (0xA47C)
#define NBIO_SMU_RPL_DXE_PKGTYPEFIXUPS_FILECODE                          (0xA47D)
#define NBIO_SMU_RPL_DXE_MPIOSERVICEPROTOCOL_FILECODE                    (0xA47E)
#define NBIO_GFX_RPL_DXE_DRIVERENTRY_FILECODE                            (0XA47F)
#define NBIO_IOAPIC_RPL_PEI_AMDNBIOIOAPIC_FILECODE                       (0xA480)
#define NBIO_IOMMU_RPL_DXE_AMDNBIOIOMMU_FILECODE                         (0xA481)
#define NBIO_IOMMU_RPL_PEI_AMDNBIOIOMMU_FILECODE                         (0xA482)
#define NBIO_NBIOBASE_RPL_DXE_AMDNBIOBASE_FILECODE                       (0xA483)
#define NBIO_NBIOBASE_RPL_PEI_AMDNBIOBASE_FILECODE                       (0xA484)
#define NBIO_NBIOBASE_RPL_PEI_GNBIOMMUTABLES_FILECODE                    (0xA485)
#define NBIO_NBIOBASE_RPL_PEI_NBIORASWORKAROUND_FILECODE                 (0xA486)
#define NBIO_NBIOBASE_RPL_PEI_NBIOTABLES_FILECODE                        (0xA487)
#define NBIO_NBIOBASE_RPL_PEI_NBIOTOPMEM_FILECODE                        (0xA488)
#define NBIO_NBIOBASE_RPL_PEI_PCIECOMPLEXDATA_FILECODE                   (0xA489)
#define NBIO_PCIE_RPL_DXE_AMDNBIOPCIEAER_FILECODE                        (0xA48A)
#define NBIO_PCIE_RPL_DXE_AMDNBIOPCIECLKREQ_FILECODE                     (0xA48B)
#define NBIO_PCIE_RPL_DXE_AMDNBIOPCIEDXE_FILECODE                        (0xA48C)
#define NBIO_PCIE_RPL_DXE_AMDNBIOPCIESRIS_FILECODE                       (0xA48D)
#define NBIO_PCIE_RPL_DXE_NBIORASCONTROL_FILECODE                        (0xA48E)
#define NBIO_PCIE_RPL_PEI_AMDNBIOPCIEPEI_FILECODE                        (0xA48F)
#define NBIO_PCIE_RPL_PEI_DEVICECONTROL_FILECODE                         (0xA490)
#define NBIO_PCIE_RPL_PEI_DPCSTATUSREPORT_FILECODE                       (0xA491)
#define NBIO_PCIE_RPL_PEI_DXIOCFGPOINTS_FILECODE                         (0xA492)
#define NBIO_PCIE_RPL_PEI_DXIOINIT_FILECODE                              (0xA493)
#define NBIO_PCIE_RPL_PEI_HIDEPORTS_FILECODE                             (0xA494)
#define NBIO_PCIE_RPL_PEI_PCIECOMPLEXDATA_FILECODE                       (0xA495)
#define NBIO_PCIE_RPL_PEI_PCIEREMAP_FILECODE                             (0xA497)
#define NBIO_PCIE_RPL_PEI_DXIOTOPOLOGY_FILECODE                          (0xA498)
#define NBIO_PCIE_RPL_PEI_PCIEHOTPLUG_FILECODE                           (0xA499)
#define NBIO_PCIE_RPL_PEI_EARLYLINKPPI_FILECODE                          (0xA49A)
#define NBIO_PCIE_RPL_PEI_PKGTYPEFIXUPS_FILECODE                         (0xA49B)
#define NBIO_GFX_RPL_PEI_GFXDRIVERENTRY_FILECODE                         (0xA49C)

#define LIBRARY_NBIOSMURPLLIB_NBIOSMURPLLIB_FILECODE                     (0xA49D)
#define NBIO_PCIE_RPL_COMMON_PCIESTRAPS_FILECODE                         (0xA49E)
#define NBIO_EARLYPHASE_AMDNBIOEARLYPHASERPLPEI_AMDNBIOEARLYPHASERPLPEI_FILECODE                 (0xA49F)

// PHX
#define NBIO_PHX_PEI_SMUINIT_FILECODE                                    (0xA4A0)
#define NBIO_PHX_PEI_CCXTOPOLOGYPPI_FILECODE                             (0xA4A1)
#define NBIO_PHX_PEI_EARLYINITFEATURE_FILECODE                           (0xA4A2)
#define NBIO_PHX_PEI_SMUSERVICESPPI_FILECODE                             (0xA4A3)
#define NBIO_PHX_PEI_IOAPICINIT_FILECODE                                 (0xA4A4)
#define NBIO_PHX_PEI_IOMMUINIT_FILECODE                                  (0xA4A5)
#define NBIO_PHX_PEI_BASEINIT_FILECODE                                   (0xA4A6)
#define NBIO_PHX_PEI_NBIOTABLES_FILECODE                                 (0xA4A7)
#define NBIO_PHX_PEI_NBIOTOPMEM_FILECODE                                 (0xA4A8)
#define NBIO_PHX_PEI_PCIECOMPLEXDATA_FILECODE                            (0xA4A9)
#define NBIO_PHX_PEI_PCIEINIT_FILECODE                                   (0xA4AA)
#define NBIO_PHX_PEI_DEVICECONTROL_FILECODE                              (0xA4AB)
#define NBIO_PHX_PEI_DPCSTATUSREPORT_FILECODE                            (0xA4AC)
#define NBIO_PHX_PEI_DXIOCFGPOINTS_FILECODE                              (0xA4AD)
#define NBIO_PHX_PEI_DXIOINIT_FILECODE                                   (0xA4AE)
#define NBIO_PHX_PEI_HIDEPORTS_FILECODE                                  (0xA4AF)
#define NBIO_PHX_PEI_PCIEREMAP_FILECODE                                  (0xA4B0)
#define NBIO_PHX_PEI_DXIOTOPOLOGY_FILECODE                               (0xA4B1)
#define NBIO_PHX_PEI_PCIEHOTPLUG_FILECODE                                (0xA4B2)
#define NBIO_PHX_PEI_EARLYLINKPPI_FILECODE                               (0xA4B3)
#define NBIO_PHX_PEI_PKGTYPEFIXUPS_FILECODE                              (0xA4B4)
#define NBIO_PHX_PEI_GFXINIT_FILECODE                                    (0xA4B5)
#define NBIO_PHX_PEI_SMU13BIOSINTERFACETABLE_FILECODE                    (0xA4B6)
#define NBIO_PHX_PEI_SMUSUPPORTFUNCTIONS_FILECODE                        (0xA4B7)
#define NBIO_PHX_PEI_ENTRYPOINTS_FILECODE                                (0xA4B8)
#define NBIO_PHX_PEI_GFXINFOCOLLECTION_FILECODE                          (0xA4B9)
#define NBIO_PHX_PEI_INFOCOLLECTION_FILECODE                             (0xA4BA)
#define NBIO_PHX_PEI_SMUPKGTYPEFIXUPS_FILECODE                           (0xA4BB)
#define NBIO_PHX_PEI_CDMAINIT_FILECODE                                   (0xA4BC)
#define NBIO_PHX_PEI_IOMMUTABLES_FILECODE                                (0xA4BD)
#define NBIO_PHX_PEI_GFXENUMCONNECTORS_FILECODE                          (0xA4BE)
#define NBIO_PHX_COMMON_PCIESTRAPS_FILECODE                              (0xA4BF)

#define NBIO_PHX_DXE_ALIBINIT_FILECODE                                   (0xA4C0)
#define NBIO_PHX_DXE_AMDAPLUSA_FILECODE                                  (0xA4C1)
#define NBIO_PHX_DXE_AMDNBIOALIBCALLBACK_FILECODE                        (0xA4C2)
#define NBIO_PHX_DXE_AMDNBIOEXITLATENCY_FILECODE                         (0xA4C3)
#define NBIO_PHX_DXE_AMDNBIOPCIEAER_FILECODE                             (0xA4C4)
#define NBIO_PHX_DXE_AMDNBIOPCIEASPM_FILECODE                            (0xA4C5)
#define NBIO_PHX_DXE_AMDNBIOPCIECLKREQ_FILECODE                          (0xA4C6)
#define NBIO_PHX_DXE_AMDNBIOPCIESRIS_FILECODE                            (0xA4C7)
#define NBIO_PHX_DXE_AMDPSPPPHX_FILECODE                                 (0xA4C8)
#define NBIO_PHX_DXE_CCXTOPOLOGYPROTOCOL_FILECODE                        (0xA4C9)
#define NBIO_PHX_DXE_CPPCSERVICESPROTOCOL_FILECODE                       (0xA4CA)
#define NBIO_PHX_DXE_ENTRYPOINTS_FILECODE                                (0xA4CB)
#define NBIO_PHX_DXE_FINALINITCALLBACK_FILECODE                          (0xA4CC)
#define NBIO_PHX_DXE_GFXINIT_FILECODE                                    (0xA4CD)
#define NBIO_PHX_DXE_INFOCOLLECTION_FILECODE                             (0xA4CE)
#define NBIO_PHX_DXE_IOMMUINIT_FILECODE                                  (0xA4CF)
#define NBIO_PHX_DXE_PCIEINIT_FILECODE                                   (0xA4D0)
#define NBIO_PHX_DXE_SMUINIT_FILECODE                                    (0xA4D1)
#define NBIO_PHX_DXE_SMUSERVICESPROTOCOL_FILECODE                        (0xA4D2)
#define NBIO_PHX_DXE_SMUSUPPORTFUNCTIONS_FILECODE                        (0xA4D3)
#define NBIO_PHX_DXE_AFTERCCXCALLBACK_FILECODE                           (0xA4D4)
#define NBIO_PHX_DXE_MPIOSERVICESPROTOCOL_FILECODE                       (0xA4D5)
#define NBIO_PHX_DXE_PCIELTR_FILECODE                                    (0xA9D0)
#define NBIO_PHX_DXE_PHXPCIEASPML1SSLIB_FILECODE                         (0xA9D1)
#define NBIO_PHX_DXE_FSP_AMDNBIODXEPHXFSPPEI_FILECODE                    (0xA9D2)
#define NBIO_PHX_DXE_FSP_GFXINITDXEFSPPEI_FILECODE                       (0xA9D3)
#define NBIO_PHX_DXE_PCIEDGPUONLYMODE_FILECODE                           (0xA9D4)

// RPL
#define NBIO_NBIOALIB_RPL_DXE_AMDNBIOALIBRPLDXE_FILECODE                 (0xA4D6)
#define NBIO_PCIE_RPL_DXE_AMDAPLUSA_FILECODE                             (0xA4D7)
#define NBIO_PCIE_RPL_DXE_AMDNBIOEXITLATENCY_FILECODE                    (0xA4D8)
#define NBIO_PCIE_RPL_DXE_AMDNBIOPCIEASPM_FILECODE                       (0xA4D9)
#define NBIO_PCIE_RPL_DXE_AMDPSPPRPL_FILECODE                            (0xA4DA)
#define NBIO_SMU_RPL_DXE_INFOCOLLECTION_FILECODE                         (0xA4DB)
#define NBIO_SMU_RPL_DXE_SMUDRIVERENTRY_FILECODE                         (0xA4DC)
#define NBIO_PCIE_RPL_LIBRARY_COLLECTNBIFPORTINFOLIB_COLLECTNBIFPORTINFOLIB_FILECODE (0xA4DD)

// GN
#define NBIO_SMU_GN_PEI_SMUDRIVERENTRY_FILECODE                          (0xA500)
#define NBIO_SMU_GN_PEI_CCXTOPOLOGYPPI_FILECODE                          (0xA501)
#define NBIO_SMU_GN_PEI_EARLYINITFEATURE_FILECODE                        (0xA502)
#define NBIO_SMU_GN_PEI_SMUSERVICESPPI_FILECODE                          (0xA503)

#define NBIO_SMU_GN_DXE_DRIVERENTRY_FILECODE                             (0xA504)
#define NBIO_SMU_GN_DXE_FINALINITCALLBACK_FILECODE                       (0xA505)
#define NBIO_SMU_GN_DXE_AFTERCCXCALLBACK_FILECODE                        (0xA506)
#define NBIO_SMU_GN_DXE_CCXTOPOLOGYPROTOCOL_FILECODE                     (0xA507)
#define NBIO_SMU_GN_DXE_SERVERHOTPLUGFEAT_FILECODE                       (0xA508)
#define NBIO_SMU_GN_DXE_SMUSERVICESPROTOCOL_FILECODE                     (0xA509)
#define NBIO_SMU_GN_DXE_SMUSUPPORTFUNCTIONS_FILECODE                     (0xA50A)
#define NBIO_SMU_GN_DXE_SMUINFOCOLLECTION_FILECODE                       (0xA50B)
#define NBIO_SMU_GN_DXE_CPPCSERVICESPROTOCOL_FILECODE                    (0xA50C)

#define NBIO_IOMMU_GN_DXE_AMDNBIOIOMMU_FILECODE                          (0xA510)
#define NBIO_IOMMU_GN_PEI_AMDNBIOIOMMU_FILECODE                          (0xA511)
#define NBIO_NBIOBASE_GN_PEI_GNBIOMMUTABLES_FILECODE                     (0xA512)
#define NBIO_NBIOBASE_GN_DXE_AMDNBIOBASE_FILECODE                        (0xA513)
#define NBIO_NBIOBASE_GN_PEI_AMDNBIOBASE_FILECODE                        (0xA514)
#define NBIO_NBIOBASE_GN_PEI_NBIOTABLES_FILECODE                         (0xA515)
#define NBIO_NBIOBASE_GN_PEI_NBIOTABLESMILAN_FILECODE                    (0xA516)
#define NBIO_NBIOBASE_GN_PEI_NBIOTABLESVMR_FILECODE                      (0xA517)
#define NBIO_NBIOBASE_GN_PEI_NBIOTOPMEM_FILECODE                         (0xA518)
#define NBIO_PCIE_GN_DXE_AMDNBIOPCIEAER_FILECODE                         (0xA519)
#define NBIO_PCIE_GN_DXE_AMDNBIOPCIECLKREQ_FILECODE                      (0xA51A)
#define NBIO_PCIE_GN_DXE_AMDNBIOPCIEDXE_FILECODE                         (0xA51B)
#define NBIO_PCIE_GN_DXE_AMDNBIOPCIESRIS_FILECODE                        (0xA51C)
#define NBIO_PCIE_GN_DXE_CCIX_FILECODE                                   (0xA51D)
#define NBIO_PCIE_GN_DXE_NBIOESMCONTROL_FILECODE                         (0xA51E)
#define NBIO_PCIE_GN_DXE_NBIORASCONTROL_FILECODE                         (0xA51F)
#define NBIO_PCIE_GN_PEI_AMDNBIOPCIEPEI_FILECODE                         (0xA520)
#define NBIO_PCIE_GN_PEI_DEVICECONTROL_FILECODE                          (0xA521)
#define NBIO_PCIE_GN_PEI_DPCSTATUSREPORT_FILECODE                        (0xA522)
#define NBIO_PCIE_GN_PEI_DXIOCFGPOINTS_FILECODE                          (0xA523)
#define NBIO_PCIE_GN_PEI_DXIOINIT_FILECODE                               (0xA524)
#define NBIO_PCIE_GN_PEI_DXIOTOPOLOGY_FILECODE                           (0xA525)
#define NBIO_PCIE_GN_PEI_EARLYLINKPPI_FILECODE                           (0xA526)
#define NBIO_PCIE_GN_PEI_HIDEPORTS_FILECODE                              (0xA527)
#define NBIO_PCIE_GN_PEI_NTBFEATURE_FILECODE                             (0xA528)
#define NBIO_NBIOBASE_GN_PEI_PCIECOMPLEXDATA_FILECODE                    (0xA529)
#define NBIO_PCIE_GN_PEI_PCIEHOTPLUG_FILECODE                            (0xA52A)
#define NBIO_PCIE_GN_PEI_PCIEREMAP_FILECODE                              (0xA52B)
#define NBIO_PCIE_GN_PEI_PKGTYPEFIXUPS_FILECODE                          (0xA52C)
#define LIBRARY_NBIOIDSHOOKGNLIB_DXE_NBIOIDSHOOKGNLIBDXE_FILECODE        (0xA52D)
#define LIBRARY_NBIOIDSHOOKGNLIB_PEI_NBIOIDSHOOKGNLIBPEI_FILECODE        (0xA52E)
#define LIBRARY_NBIOSERVICESLIB_DXE_NBIOSERVICESLIBDXE_FILECODE          (0xA52F)

// BA
#define NBIO_SMU_BA_PEI_SMUDRIVERENTRY_FILECODE                          (0xA540)
#define NBIO_SMU_BA_PEI_CCXTOPOLOGYPPI_FILECODE                          (0xA541)
#define NBIO_SMU_BA_PEI_EARLYINITFEATURE_FILECODE                        (0xA542)
#define NBIO_SMU_BA_PEI_SMUSERVICESPPI_FILECODE                          (0xA543)

#define NBIO_SMU_BA_DXE_DRIVERENTRY_FILECODE                             (0xA544)
#define NBIO_SMU_BA_DXE_FINALINITCALLBACK_FILECODE                       (0xA545)
#define NBIO_SMU_BA_DXE_AFTERCCXCALLBACK_FILECODE                        (0xA546)
#define NBIO_SMU_BA_DXE_CCXTOPOLOGYPROTOCOL_FILECODE                     (0xA547)
#define NBIO_SMU_BA_DXE_SERVERHOTPLUGFEAT_FILECODE                       (0xA548)
#define NBIO_SMU_BA_DXE_SMUSERVICESPROTOCOL_FILECODE                     (0xA549)
#define NBIO_SMU_BA_DXE_SMUSUPPORTFUNCTIONS_FILECODE                     (0xA54A)
#define NBIO_SMU_BA_DXE_SMUINFOCOLLECTION_FILECODE                       (0xA54B)
#define NBIO_SMU_BA_DXE_CPPCSERVICESPROTOCOL_FILECODE                    (0xA54C)

#define NBIO_IOMMU_BA_DXE_AMDNBIOIOMMU_FILECODE                          (0xA550)
#define NBIO_IOMMU_BA_PEI_AMDNBIOIOMMU_FILECODE                          (0xA551)
#define NBIO_NBIOBASE_BA_PEI_GNBIOMMUTABLES_FILECODE                     (0xA552)
#define NBIO_NBIOBASE_BA_DXE_AMDNBIOBASE_FILECODE                        (0xA553)
#define NBIO_NBIOBASE_BA_PEI_AMDNBIOBASE_FILECODE                        (0xA554)
#define NBIO_NBIOBASE_BA_PEI_NBIOTABLES_FILECODE                         (0xA555)
#define NBIO_NBIOBASE_BA_PEI_NBIOTABLESMILAN_FILECODE                    (0xA556)
#define NBIO_NBIOBASE_BA_PEI_NBIOTABLESVMR_FILECODE                      (0xA557)
#define NBIO_NBIOBASE_BA_PEI_NBIOTOPMEM_FILECODE                         (0xA558)
#define NBIO_PCIE_BA_DXE_AMDNBIOPCIEAER_FILECODE                         (0xA559)
#define NBIO_PCIE_BA_DXE_AMDNBIOPCIECLKREQ_FILECODE                      (0xA55A)
#define NBIO_PCIE_BA_DXE_AMDNBIOPCIEDXE_FILECODE                         (0xA55B)
#define NBIO_PCIE_BA_DXE_AMDNBIOPCIESRIS_FILECODE                        (0xA55C)
#define NBIO_PCIE_BA_DXE_CCIX_FILECODE                                   (0xA55D)
#define NBIO_PCIE_BA_DXE_NBIOESMCONTROL_FILECODE                         (0xA55E)
#define NBIO_PCIE_BA_DXE_NBIORASCONTROL_FILECODE                         (0xA55F)
#define NBIO_PCIE_BA_PEI_AMDNBIOPCIEPEI_FILECODE                         (0xA560)
#define NBIO_PCIE_BA_PEI_DEVICECONTROL_FILECODE                          (0xA561)
#define NBIO_PCIE_BA_PEI_DPCSTATUSREPORT_FILECODE                        (0xA562)
#define NBIO_PCIE_BA_PEI_DXIOCFGPOINTS_FILECODE                          (0xA563)
#define NBIO_PCIE_BA_PEI_DXIOINIT_FILECODE                               (0xA564)
#define NBIO_PCIE_BA_PEI_DXIOTOPOLOGY_FILECODE                           (0xA565)
#define NBIO_PCIE_BA_PEI_EARLYLINKPPI_FILECODE                           (0xA566)
#define NBIO_PCIE_BA_PEI_HIDEPORTS_FILECODE                              (0xA567)
#define NBIO_PCIE_BA_PEI_NTBFEATURE_FILECODE                             (0xA568)
#define NBIO_NBIOBASE_BA_PEI_PCIECOMPLEXDATA_FILECODE                    (0xA569)
#define NBIO_PCIE_BA_PEI_PCIEHOTPLUG_FILECODE                            (0xA56A)
#define NBIO_PCIE_BA_PEI_PCIEREMAP_FILECODE                              (0xA56B)
#define NBIO_PCIE_BA_PEI_PKGTYPEFIXUPS_FILECODE                          (0xA56C)
#define LIBRARY_NBIOIDSHOOKBALIB_DXE_NBIOIDSHOOKBALIBDXE_FILECODE        (0xA56D)
#define LIBRARY_NBIOIDSHOOKBALIB_PEI_NBIOIDSHOOKBALIBPEI_FILECODE        (0xA56E)
//MI200
#define NBIO_PCIE_MI200_PEI_MI200HIDEPORTS_FILECODE                      (0xA580)
#define NBIO_PCIE_MI200_PEI_MI200LIB_FILECODE                            (0xA581)
#define NBIO_PCIE_MI200_PEI_MI200TOPMEM_FILECODE                         (0xA582)

// FF3
#define NBIO_EARLYPHASE_FF3_AMDNBIOEARLYPHASEFF3PEI_FILECODE             (0xA600)
#define NBIO_GFX_FF3_DXE_DRIVERENTRY_FILECODE                            (0xA601)
#define NBIO_GFX_FF3_PEI_GFXDRIVERENTRY_FILECODE                         (0xA602)
#define NBIO_GFX_FF3_DXE_SMUINFOCOLLECTION_FILECODE                      (0xA603)
#define NBIO_IOMMU_FF3_DXE_AMDNBIOIOMMU_FILECODE                         (0xA604)
#define NBIO_IOMMU_FF3_PEI_AMDNBIOIOMMU_FILECODE                         (0xA605)
#define NBIO_NBIOALIB_VN_DXE_AMDNBIOALIBCALLBACK_FILECODE                (0xA606)
#define NBIO_NBIOALIB_VN_DXE_AMDNBIOALIBVNDXE_FILECODE                   (0xA607)
#define NBIO_NBIOBASE_FF3_DXE_AMDNBIOBASE_FILECODE                       (0xA608)
#define NBIO_NBIOBASE_FF3_PEI_AMDNBIOBASE_FILECODE                       (0xA609)
#define NBIO_NBIOBASE_FF3_PEI_NBIOTABLES_FILECODE                        (0xA60A)
#define NBIO_NBIOBASE_FF3_PEI_NBIOTOPMEM_FILECODE                        (0xA60B)

#define NBIO_PCIE_FF3_DXE_AMDNBIOPCIECLKREQ_FILECODE                     (0xA610)
#define NBIO_PCIE_FF3_DXE_AMDNBIOPCIEDXE_FILECODE                        (0xA611)
#define NBIO_PCIE_FF3_DXE_AMDNBIOPCIESRIS_FILECODE                       (0xA612)
#define NBIO_PCIE_FF3_DXE_AMDPSPPFF3_FILECODE                            (0xA613)
#define NBIO_PCIE_FF3_PEI_AMDNBIOPCIEPEI_FILECODE                        (0xA614)
#define NBIO_PCIE_FF3_PEI_DEVICECONTROL_FILECODE                         (0xA615)
#define NBIO_PCIE_FF3_PEI_DPCSTATUSREPORT_FILECODE                       (0xA616)
#define NBIO_PCIE_FF3_PEI_DXIOCFGPOINTS_FILECODE                         (0xA617)
#define NBIO_PCIE_FF3_PEI_DXIOINIT_FILECODE                              (0xA618)
#define NBIO_PCIE_FF3_PEI_DXIOTOPOLOGY_FILECODE                          (0xA619)
#define NBIO_PCIE_FF3_PEI_EARLYLINKPPI_FILECODE                          (0xA61A)
#define NBIO_PCIE_FF3_PEI_HIDEPORTS_FILECODE                             (0xA61B)
#define NBIO_PCIE_FF3_PEI_PCIECOMPLEXDATA_FILECODE                       (0xA61C)
#define NBIO_PCIE_FF3_PEI_PCIEHOTPLUG_FILECODE                           (0xA61D)
#define NBIO_PCIE_FF3_PEI_PCIEREMAP_FILECODE                             (0xA61E)
#define NBIO_PCIE_FF3_PEI_PKGTYPEFIXUPS_FILECODE                         (0xA61F)

#define NBIO_SMU_FF3_DXE_AFTERCCXCALLBACK_FILECODE                       (0xA620)
#define NBIO_SMU_FF3_DXE_CCXTOPOLOGYPROTOCOL_FILECODE                    (0xA621)
#define NBIO_SMU_FF3_DXE_CPPCSERVICESPROTOCOL_FILECODE                   (0xA622)
#define NBIO_SMU_FF3_DXE_FINALINITCALLBACK_FILECODE                      (0xA623)
#define NBIO_SMU_FF3_DXE_SMUINFOCOLLECTION_FILECODE                      (0xA624)
#define NBIO_SMU_FF3_DXE_DRIVERENTRY_FILECODE                            (0xA625)
#define NBIO_SMU_FF3_DXE_SMUSERVICESPROTOCOL_FILECODE                    (0xA626)
#define NBIO_SMU_FF3_DXE_SMUSUPPORTFUNCTIONS_FILECODE                    (0xA627)
#define NBIO_SMU_FF3_PEI_CCXTOPOLOGYPPI_FILECODE                         (0xA628)
#define NBIO_SMU_FF3_PEI_EARLYINITFEATURE_FILECODE                       (0xA629)
#define NBIO_SMU_FF3_PEI_SMUDRIVERENTRY_FILECODE                         (0xA62A)
#define NBIO_SMU_FF3_PEI_SMUSERVICESPPI_FILECODE                         (0xA62B)

#define LIBRARY_NBIOIDSHOOKFF3LIB_PEI_NBIOIDSHOOKLIBPEI_FILECODE         (0xA62C)
#define LIBRARY_NBIOIDSHOOKFF3LIB_DXE_NBIOIDSHOOKLIBDXE_FILECODE         (0xA62D)

#define NBIO_LIBRARY_SMNTABLE_SMNTABLE_FILECODE                          (0xA630)

//Mendocino
#define NBIO_PCIE_MDN_COMMON_PCIESTRAPS_FILECODE                         (0xA631)
#define NBIO_SMU_MDNFSP_PEI_PKGTYPEFIXUPS_FILECODE                       (0xA632)
#define NBIO_SMU_MDN_DXE_SMU13BIOSINTERFACETABLE_FILECODE                (0xA633)
#define NBIO_SMU_MDN_PEI_SMUDRIVERENTRY_FILECODE                         (0xA634)
#define NBIO_SMU_MDN_PEI_CCXTOPOLOGYPPI_FILECODE                         (0xA635)
#define NBIO_SMU_MDN_PEI_EARLYINITFEATURE_FILECODE                       (0xA636)
#define NBIO_SMU_MDN_PEI_SMUSERVICESPPI_FILECODE                         (0xA637)
#define NBIO_SMU_MDN_DXE_SMUDRIVERENTRY_FILECODE                         (0xA638)
#define NBIO_SMU_MDN_DXE_FINALINITCALLBACK_FILECODE                      (0xA639)
#define NBIO_SMU_MDNFSP_PEI_PMINIT_FILECODE                              (0xA63A)
#define NBIO_SMU_MDN_DXE_CCXTOPOLOGYPROTOCOL_FILECODE                    (0xA63B)
#define NBIO_SMU_MDN_DXE_SERVERHOTPLUGFEAT_FILECODE                      (0xA63C)
#define NBIO_SMU_MDN_DXE_SMUSERVICESPROTOCOL_FILECODE                    (0xA63D)
#define NBIO_SMU_MDN_DXE_SMUSUPPORTFUNCTIONS_FILECODE                    (0xA63E)
#define NBIO_SMU_MDN_PEI_INFOCOLLECTION_FILECODE                         (0xA63F)
#define NBIO_SMU_MDN_DXE_CPPCSERVICESPROTOCOL_FILECODE                   (0xA640)
#define NBIO_GFX_MDN_DXE_DRIVERENTRY_FILECODE                            (0xA641)
#define NBIO_GFX_MDN_PEI_GFXENUMCONNECTORS_FILECODE                      (0xA642)
#define NBIO_GFX_MDN_PEI_GFXINFOCOLLECTION_FILECODE                      (0xA643)
#define NBIO_GFX_MDN_PEI_GFXDRIVERENTRY_FILECODE                         (0xA644)
#define NBIO_IOAPIC_MDN_PEI_AMDNBIOIOAPIC_FILECODE                       (0xA645)
#define NBIO_IOMMU_MDN_DXE_AMDNBIOIOMMU_FILECODE                         (0xA646)
#define NBIO_IOMMU_MDN_PEI_IOMMUDRIVERENTRY_FILECODE                     (0xA647)
#define NBIO_NBIOBASE_MDN_DXE_AMDNBIOBASE_FILECODE                       (0xA648)
#define NBIO_NBIOBASE_MDN_PEI_AMDNBIOBASE_FILECODE                       (0xA649)
#define NBIO_NBIOBASE_MDN_PEI_GNBIOMMUTABLES_FILECODE                    (0xA64A)
#define NBIO_NBIOBASE_MDN_PEI_NBIORASWORKAROUND_FILECODE                 (0xA64B)
#define NBIO_NBIOBASE_MDN_PEI_NBIOTABLES_FILECODE                        (0xA64C)
#define NBIO_NBIOBASE_MDN_PEI_NBIOTOPMEM_FILECODE                        (0xA64D)
#define NBIO_PCIE_MDN_COMMON_NBIOPCIEINIT_AMDNBIOPCIEAER_FILECODE        (0xA64E)
#define NBIO_PCIE_MDN_COMMON_NBIOPCIEINIT_AMDNBIOPCIECLKREQ_FILECODE     (0xA64F)
#define NBIO_PCIE_MDN_DXE_AMDNBIOPCIEDXE_FILECODE                        (0xA650)
#define NBIO_PCIE_MDN_COMMON_NBIOPCIEINIT_AMDNBIOPCIESRIS_FILECODE       (0xA651)

#define NBIO_PCIE_MDN_PEI_AMDNBIOPCIEPEI_FILECODE                        (0xA653)
#define NBIO_PCIE_MDN_PEI_DEVICECONTROL_FILECODE                         (0xA654)
#define NBIO_PCIE_MDN_PEI_DPCSTATUSREPORT_FILECODE                       (0xA655)
#define NBIO_PCIE_MDN_PEI_DXIOCFGPOINTS_FILECODE                         (0xA656)
#define NBIO_PCIE_MDN_PEI_DXIOINIT_FILECODE                              (0xA657)
#define NBIO_PCIE_MDN_PEI_HIDEPORTS_FILECODE                             (0xA658)
#define NBIO_PCIE_MDN_PEI_PCIEREMAP_FILECODE                             (0xA659)
#define NBIO_PCIE_MDN_PEI_DXIOTOPOLOGY_FILECODE                          (0xA65A)
#define NBIO_PCIE_MDN_PEI_PCIEHOTPLUG_FILECODE                           (0xA65B)
#define NBIO_PCIE_MDN_PEI_EARLYLINKPPI_FILECODE                          (0xA65C)
#define NBIO_PCIE_MDN_PEI_PKGTYPEFIXUPS_FILECODE                         (0xA65D)
#define NBIO_PCIE_MDN_COMMON_NBIOPCIEINIT_AMDAPLUSA_FILECODE             (0xA65E)
#define NBIO_PCIE_MDN_COMMON_NBIOPCIEINIT_AMDNBIOEXITLATENCY_FILECODE    (0xA65F)
#define NBIO_PCIE_MDN_COMMON_NBIOPCIEINIT_AMDNBIOPCIEASPM_FILECODE       (0xA660)
#define NBIO_PCIE_MDN_COMMON_NBIOPCIEINIT_AMDPSPPMDN_FILECODE            (0xA661)
#define NBIO_SMU_MDN_PEI_SMUSUPPORTFUNCTIONS_FILECODE                    (0xA662)
#define NBIO_SMU_MDNFSP_PEI_LATESMUINIT_FILECODE                         (0xA664)
#define NBIO_PCIE_MDN_COMMON_NBIOPCIEINIT_NBIOPCIEINIT_FILECODE          (0xA665)
#define NBIO_PCIE_MDN_FSP_LATEPCIEINIT_LATEPCIEINIT_FILECODE             (0xA666)
#define NBIO_PCIE_MDN_PEI_PCIECOMPLEXDATA_FILECODE                       (0xA667)
#define NBIO_SMU_MDN_DXE_AFTERCCXCALLBACK_FILECODE                       (0xA668)
#define NBIO_SMU_MDN_DXE_INFOCOLLECTION_FILECODE                         (0xA669)
#define NBIO_SMU_MDN_DXE_PKGTYPEFIXUPS_FILECODE                          (0xA66A)
#define NBIO_SMU_MDNFSP_PEI_CCXTOPOLOGYPPI_FILECODE                      (0xA66B)
#define NBIO_SMU_MDNFSP_PEI_EARLYINITFEATURE_FILECODE                    (0xA66C)
#define NBIO_SMU_MDNFSP_PEI_INFOCOLLECTION_FILECODE                      (0xA66D)
#define NBIO_SMU_MDNFSP_PEI_SMU13BIOSINTERFACETABLE_FILECODE             (0xA66E)
#define NBIO_SMU_MDNFSP_PEI_SMUDRIVERENTRY_FILECODE                      (0xA66F)
#define NBIO_SMU_MDNFSP_PEI_SMUSERVICESPPI_FILECODE                      (0xA670)
#define NBIO_SMU_MDNFSP_PEI_SMUSUPPORTFUNCTIONS_FILECODE                 (0xA671)

//NBIO COMMON LIB
#define NBIO_LIBRARY_COMMONPEI_PCIECOMPLEXINIT_FILECODE                   (0xA700)
#define NBIO_LIBRARY_COMMONPEI_PCIEPEISERVICES_FILECODE                   (0xA701)
#define NBIO_LIBRARY_COMMONPEI_DXIOTOPOLOGY_FILECODE                      (0xA702)
#define NBIO_LIBRARY_COMMONPEI_HIDEWRAPPER_FILECODE                       (0xA703)
#define NBIO_LIBRARY_COMMONPEI_PEILIBFUNCTIONS_FILECODE                   (0xA704)
#define NBIO_LIBRARY_COMMONDXE_DXELIBFUNCTIONS_FILECODE                   (0xA705)
#define NBIO_COMMON_CXLMANAGERDXE_CXLMANAGERDXE_FILECODE                  (0xA706)
#define NBIO_COMMON_DXE_GETPCIRESOURCES_FILECODE                          (0xA707)
#define NBIO_LIBRARY_GETPCIERESOURCESLIB_DXEGETPCIRESOURCES_FILECODE      (0xA708)
#define NBIO_LIBRARY_CXLCDATLIB_CXLCDAT_FILECODE                          (0XA709)
#define NBIO_LIBRARY_CXLCONFIGLIB_CXLCONFIG_FILECODE                      (0XA70A)
#define NBIO_LIBRARY_CXLCEDTLIB_CXLCEDT_FILECODE                          (0XA70B)
#define NBIO_LIBRARY_CXLMBOXLIB_CXLMBOX_FILECODE                          (0XA70C)

// RS
#define NBIO_RS_PEI_BASEINIT_FILECODE                                     (0xA800)
#define NBIO_RS_PEI_CCXTOPOLOGYPPI_FILECODE                               (0xA801)
#define NBIO_RS_PEI_CXLINIT_FILECODE                                      (0xA802)
#define NBIO_RS_PEI_DPCSTATUSREPORT_FILECODE                              (0xA803)
#define NBIO_RS_PEI_DXIOCFGPOINTS_FILECODE                                (0xA804)
#define NBIO_RS_PEI_DXIOINIT_FILECODE                                     (0xA805)
#define NBIO_RS_PEI_EARLYINITFEATURE_FILECODE                             (0xA806)
#define NBIO_RS_PEI_EARLYLINKPPI_FILECODE                                 (0xA807)
#define NBIO_RS_PEI_ENTRYPOINTS_FILECODE                                  (0xA808)
#define NBIO_RS_PEI_HIDEPORTS_FILECODE                                    (0xA809)
#define NBIO_RS_PEI_IOAPICINIT_FILECODE                                   (0xA80A)
#define NBIO_RS_PEI_IOMMUINIT_FILECODE                                    (0xA80B)
#define NBIO_RS_PEI_NBIOTABLES_FILECODE                                   (0xA80C)
#define NBIO_RS_PEI_NBIOTABLESMP_FILECODE                                 (0xA80D)
#define NBIO_RS_PEI_NBIOTOPMEM_FILECODE                                   (0xA80E)
#define NBIO_RS_PEI_NTBFEATURE_FILECODE                                   (0xA80F)
#define NBIO_RS_PEI_PCIECOMPLEXDATA_FILECODE                              (0xA810)
#define NBIO_RS_PEI_PCIEHOTPLUG_FILECODE                                  (0xA811)
#define NBIO_RS_PEI_PCIEREMAP_FILECODE                                    (0xA812)
#define NBIO_RS_PEI_PCIESTRAPS_FILECODE                                   (0xA813)
#define NBIO_RS_PEI_PKGTYPEFIXUPS_FILECODE                                (0xA814)
#define NBIO_RS_PEI_SMUINIT_FILECODE                                      (0xA815)
#define NBIO_RS_PEI_SMUSERVICESPPI_FILECODE                               (0xA816)
#define NBIO_RS_PEI_TOPOLOGYWA_FILECODE                                   (0xA817)
#define NBIO_RS_DXE_CCXTOPOLOGYPROTOCOL_FILECODE                          (0xA818)
#define NBIO_RS_DXE_CPPCSERVICESPROTOCOL_FILECODE                         (0xA819)
#define NBIO_RS_DXE_CXLCONFIG_FILECODE                                    (0xA81A)
#define NBIO_RS_DXE_ENTRYPOINTS_FILECODE                                  (0xA81B)
#define NBIO_RS_DXE_NBIOESMCONTROL_FILECODE                               (0xA81C)
#define NBIO_RS_DXE_IOMMUINIT_FILECODE                                    (0xA81D)
#define NBIO_RS_DXE_NBIOHWLOCK_FILECODE                                   (0xA81E)
#define NBIO_RS_DXE_PCIEAER_FILECODE                                      (0xA81F)
#define NBIO_RS_DXE_PCIECLKREQ_FILECODE                                   (0xA820)
#define NBIO_RS_DXE_PCIEINIT_FILECODE                                     (0xA821)
#define NBIO_RS_DXE_PCIESRIS_FILECODE                                     (0xA822)
#define NBIO_RS_DXE_PCIETOPOLOGYPROTOCOL_FILECODE                         (0xA823)
#define NBIO_RS_DXE_PCIETYPEFIXUPS_FILECODE                               (0xA824)
#define NBIO_RS_DXE_PMMTABLEINIT_FILECODE                                 (0xA825)
#define NBIO_RS_DXE_RASCONTROL_FILECODE                                   (0xA826)
#define NBIO_RS_DXE_SERVERHOTPLUGFEAT_FILECODE                            (0xA827)
#define NBIO_RS_DXE_SMUDEBUG_FILECODE                                     (0xA828)
#define NBIO_RS_DXE_SMUINIT_FILECODE                                      (0xA829)
#define NBIO_RS_DXE_SMUSERVICESPROTOCOL_FILECODE                          (0xA82A)
#define NBIO_RS_DXE_SMUSUPPORTFUNCTIONS_FILECODE                          (0xA82B)
#define PSP_AMDPSPDXEV2RS_AMDPSPDXEV2_FILECODE                            (0xA82C)
#define NBIO_RS_PEI_IOMMUTABLES_FILECODE                                  (0xA82D)
#define NBIO_RS_DXE_CXLCEDT_FILECODE                                      (0xA82E)

//MI300
#define NBIO_MI300_PEI_BASEINIT_FILECODE                                     (0xA840)
#define NBIO_MI300_PEI_CCXTOPOLOGYPPI_FILECODE                               (0xA841)
#define NBIO_MI300_PEI_CXLINIT_FILECODE                                      (0xA842)
#define NBIO_MI300_PEI_DPCSTATUSREPORT_FILECODE                              (0xA843)
#define NBIO_MI300_PEI_DXIOCFGPOINTS_FILECODE                                (0xA844)
#define NBIO_MI300_PEI_DXIOINIT_FILECODE                                     (0xA845)
#define NBIO_MI300_PEI_EARLYINITFEATURE_FILECODE                             (0xA846)
#define NBIO_MI300_PEI_EARLYLINKPPI_FILECODE                                 (0xA847)
#define NBIO_MI300_PEI_ENTRYPOINTS_FILECODE                                  (0xA848)
#define NBIO_MI300_PEI_HIDEPORTS_FILECODE                                    (0xA849)
#define NBIO_MI300_PEI_IOAPICINIT_FILECODE                                   (0xA84A)
#define NBIO_MI300_PEI_IOMMUINIT_FILECODE                                    (0xA84B)
#define NBIO_MI300_PEI_NBIOTABLES_FILECODE                                   (0xA84C)
#define NBIO_MI300_PEI_NBIOTABLESMP_FILECODE                                 (0xA84D)
#define NBIO_MI300_PEI_NBIOTOPMEM_FILECODE                                   (0xA84E)
#define NBIO_MI300_PEI_NTBFEATURE_FILECODE                                   (0xA84F)
#define NBIO_MI300_PEI_PCIECOMPLEXDATA_FILECODE                              (0xA850)
#define NBIO_MI300_PEI_PCIEHOTPLUG_FILECODE                                  (0xA851)
#define NBIO_MI300_PEI_PCIEREMAP_FILECODE                                    (0xA852)
#define NBIO_MI300_PEI_PCIESTRAPS_FILECODE                                   (0xA853)
#define NBIO_MI300_PEI_PKGTYPEFIXUPS_FILECODE                                (0xA854)
#define NBIO_MI300_PEI_SMUINIT_FILECODE                                      (0xA855)
#define NBIO_MI300_PEI_SMUSERVICESPPI_FILECODE                               (0xA856)
#define NBIO_MI300_PEI_TOPOLOGYWA_FILECODE                                   (0xA857)
#define NBIO_MI300_PEI_NBIONBIFINIT_FILECODE                                 (0xA858)
#define NBIO_MI300_DXE_CCXTOPOLOGYPROTOCOL_FILECODE                          (0xA859)
#define NBIO_MI300_DXE_CPPCSERVICESPROTOCOL_FILECODE                         (0xA85A)
#define NBIO_MI300_DXE_ENTRYPOINTS_FILECODE                                  (0xA85B)
#define NBIO_MI300_DXE_NBIOESMCONTROL_FILECODE                               (0xA85C)
#define NBIO_MI300_DXE_IOMMUINIT_FILECODE                                    (0xA85D)
#define NBIO_MI300_DXE_NBIOHWLOCK_FILECODE                                   (0xA85E)
#define NBIO_MI300_DXE_PCIEAER_FILECODE                                      (0xA85F)
#define NBIO_MI300_DXE_PCIECLKREQ_FILECODE                                   (0xA860)
#define NBIO_MI300_DXE_PCIEINIT_FILECODE                                     (0xA861)
#define NBIO_MI300_DXE_PCIESRIS_FILECODE                                     (0xA862)
#define NBIO_MI300_DXE_PCIETOPOLOGYPROTOCOL_FILECODE                         (0xA863)
#define NBIO_MI300_DXE_PCIETYPEFIXUPS_FILECODE                               (0xA864)
#define NBIO_MI300_DXE_PMMTABLEINIT_FILECODE                                 (0xA865)
#define NBIO_MI300_DXE_RASCONTROL_FILECODE                                   (0xA866)
#define NBIO_MI300_DXE_SERVERHOTPLUGFEAT_FILECODE                            (0xA867)
#define NBIO_MI300_DXE_SMUDEBUG_FILECODE                                     (0xA868)
#define NBIO_MI300_DXE_SMUINIT_FILECODE                                      (0xA869)
#define NBIO_MI300_DXE_SMUSERVICESPROTOCOL_FILECODE                          (0xA86A)
#define NBIO_MI300_DXE_SMUSUPPORTFUNCTIONS_FILECODE                          (0xA86B)
#define NBIO_MI300_DXE_CXLUPDATEAPCB_FILECODE                                (0xA86D)
#define NBIO_MI300_DXE_CXLCONFIGPORTFEATURE_FILECODE                         (0xA86E)

// STP
#define NBIO_STP_DXE_MPIOSERVICESPROTOCOL_FILECODE                        (0xA879)
#define NBIO_STP_DXE_ALIBINIT_FILECODE                                    (0xA880)
#define NBIO_STP_DXE_CCXTOPOLOGYPROTOCOL_FILECODE                         (0xA881)
#define NBIO_STP_DXE_CPPCSERVICESPROTOCOL_FILECODE                        (0xA882)
#define NBIO_STP_DXE_CXLCONFIGUREPORTFEATURES_FILECODE                    (0xA883)
#define NBIO_STP_DXE_ENTRYPOINTS_FILECODE                                 (0xA884)
#define NBIO_STP_DXE_ESMCONTROL_FILECODE                                  (0xA885)
#define NBIO_STP_DXE_IOMMUINIT_FILECODE                                   (0xA886)
#define NBIO_STP_DXE_NBIOHWLOCK_FILECODE                                  (0xA887)
#define NBIO_STP_DXE_PCIEAER_FILECODE                                     (0xA888)
#define NBIO_STP_DXE_PCIECLKREQ_FILECODE                                  (0xA889)
#define NBIO_STP_DXE_PCIEINIT_FILECODE                                    (0xA88A)
#define NBIO_STP_DXE_PCIESRIS_FILECODE                                    (0xA88B)
#define NBIO_STP_DXE_PCIETOPOLOGYPROTOCOL_FILECODE                        (0xA88C)
#define NBIO_STP_DXE_PCIETYPEFIXUPS_FILECODE                              (0xA88D)
#define NBIO_STP_DXE_PMMTABLEINIT_FILECODE                                (0xA88E)
#define NBIO_STP_DXE_RASCONTROL_FILECODE                                  (0xA88F)
#define NBIO_STP_DXE_SERVERHOTPLUGFEAT_FILECODE                           (0xA890)
#define NBIO_STP_DXE_SMUDEBUG_FILECODE                                    (0xA891)
#define NBIO_STP_DXE_SMUINIT_FILECODE                                     (0xA892)
#define NBIO_STP_DXE_SMUSERVICESPROTOCOL_FILECODE                         (0xA893)
#define NBIO_STP_DXE_SMUSUPPORTFUNCTIONS_FILECODE                         (0xA894)
#define NBIO_STP_PEI_BASEINIT_FILECODE                                    (0xA895)
#define NBIO_STP_PEI_PKGTYPEFIXUPS_FILECODE                               (0xA896)
#define NBIO_STP_PEI_CCXTOPOLOGYPPI_FILECODE                              (0xA897)
#define NBIO_STP_PEI_CDMAINIT_FILECODE                                    (0xA898)
#define NBIO_STP_PEI_CXLINIT_FILECODE                                     (0xA899)
#define NBIO_STP_PEI_DPCSTATUSREPORT_FILECODE                             (0xA89A)
#define NBIO_STP_PEI_DXIOCFGPOINTS_FILECODE                               (0xA89B)
#define NBIO_STP_PEI_DXIOINIT_FILECODE                                    (0xA89C)
#define NBIO_STP_PEI_EARLYINITFEATURE_FILECODE                            (0xA89D)
#define NBIO_STP_PEI_EARLYLINKPPI_FILECODE                                (0xA89E)
#define NBIO_STP_PEI_ENTRYPOINTS_FILECODE                                 (0xA89F)
#define NBIO_STP_PEI_HDAUDIOINIT_FILECODE                                 (0xA8A0)
#define NBIO_STP_PEI_HIDEPORTS_FILECODE                                   (0xA8A1)
#define NBIO_STP_PEI_IOAPICINIT_FILECODE                                  (0xA8A2)
#define NBIO_STP_PEI_IOMMUINIT_FILECODE                                   (0xA8A3)
#define NBIO_STP_PEI_NBIOTOPMEM_FILECODE                                  (0xA8A4)
#define NBIO_STP_PEI_NTBFEATURE_FILECODE                                  (0xA8A5)
#define NBIO_STP_PEI_PCIEHOTPLUG_FILECODE                                 (0xA8A6)
#define NBIO_STP_PEI_PCIEREMAP_FILECODE                                   (0xA8A7)
#define NBIO_STP_PEI_PCIESTRAPS_FILECODE                                  (0xA8A8)
#define NBIO_STP_PEI_SMUINIT_FILECODE                                     (0xA8A9)
#define NBIO_STP_PEI_SMUSERVICESPPI_FILECODE                              (0xA8AA)
#define NBIO_STP_PEI_TOPOLOGYWA_FILECODE                                  (0xA8AB)
#define NBIO_STP_DXE_PKGTYPEFIXUPS_FILECODE                               (0xA8AC)
#define NBIO_STP_PEI_IOMMUTABLES_FILECODE                                 (0xA8AD)
#define NBIO_STP_PEI_NBIOTABLES_FILECODE                                  (0xA8AE)
#define NBIO_STP_PEI_PCIECOMPLEXDATA_FILECODE                             (0xA8AF)
#define NBIO_STP_DXE_CXLHOTPLUGFEATURES_FILECODE                          (0xA8B0)
#define NBIO_STP_DXE_PCIELTR_FILECODE                                     (0xA8B1)


// STX
#define NBIO_STXKRK_PEI_SMUINIT_FILECODE                                  (0xA900)
#define NBIO_STXKRK_PEI_EARLYINITFEATURE_FILECODE                         (0xA902)
#define NBIO_STXKRK_PEI_SMUSERVICESPPI_FILECODE                           (0xA903)
#define NBIO_STXKRK_PEI_IOAPICINIT_FILECODE                               (0xA904)
#define NBIO_STXKRK_PEI_IOMMUINIT_FILECODE                                (0xA905)
#define NBIO_STXKRK_PEI_BASEINIT_FILECODE                                 (0xA906)
#define NBIO_STXKRK_PEI_NBIOTABLES_FILECODE                               (0xA907)
#define NBIO_STXKRK_PEI_NBIOTOPMEM_FILECODE                               (0xA908)
#define NBIO_STXKRK_PEI_PCIECOMPLEXDATA_FILECODE                          (0xA909)
#define NBIO_STXKRK_PEI_PCIEINIT_FILECODE                                 (0xA90A)
#define NBIO_STXKRK_PEI_DEVICECONTROL_FILECODE                            (0xA90B)
#define NBIO_STXKRK_PEI_DPCSTATUSREPORT_FILECODE                          (0xA90C)
#define NBIO_STXKRK_PEI_DXIOCFGPOINTS_FILECODE                            (0xA90D)
#define NBIO_STXKRK_PEI_DXIOINIT_FILECODE                                 (0xA90E)
#define NBIO_STXKRK_PEI_HIDEPORTS_FILECODE                                (0xA90F)
#define NBIO_STXKRK_PEI_PCIEREMAP_FILECODE                                (0xA910)
#define NBIO_STXKRK_PEI_DXIOTOPOLOGY_FILECODE                             (0xA911)
#define NBIO_STXKRK_PEI_PCIEHOTPLUG_FILECODE                              (0xA912)
#define NBIO_STXKRK_PEI_EARLYLINKPPI_FILECODE                             (0xA913)
#define NBIO_STXKRK_PEI_PKGTYPEFIXUPS_FILECODE                            (0xA914)
#define NBIO_STXKRK_PEI_GFXINIT_FILECODE                                  (0xA915)
#define NBIO_STXKRK_PEI_SMU14BIOSINTERFACETABLE_FILECODE                  (0xA916)
#define NBIO_STXKRK_PEI_SMUSUPPORTFUNCTIONS_FILECODE                      (0xA917)
#define NBIO_STXKRK_PEI_ENTRYPOINTS_FILECODE                              (0xA918)
#define NBIO_STXKRK_PEI_GFXINFOCOLLECTION_FILECODE                        (0xA919)
#define NBIO_STXKRK_PEI_INFOCOLLECTION_FILECODE                           (0xA91A)
#define NBIO_STXKRK_PEI_SMUPKGTYPEFIXUPS_FILECODE                         (0xA91B)
#define NBIO_STXKRK_PEI_CDMAINIT_FILECODE                                 (0xA91C)
#define NBIO_STXKRK_PEI_IOMMUTABLES_FILECODE                              (0xA91D)
#define NBIO_STXKRK_PEI_UNITTEST_DATAINIT_FILECODE                        (0xA91E)
#define NBIO_STXKRK_PEI_UNITTEST_NBIOPEIUNITTEST_FILECODE                 (0xA91F)

#define NBIO_STXKRK_DXE_ALIB_ALIBINIT_FILECODE                            (0xA920)
#define NBIO_STXKRK_DXE_AMDAPLUSA_FILECODE                                (0xA921)
#define NBIO_STXKRK_DXE_ALIB_AMDNBIOALIBCALLBACK_FILECODE                 (0xA922)
#define NBIO_STXKRK_DXE_AMDNBIOEXITLATENCY_FILECODE                       (0xA923)
#define NBIO_STXKRK_DXE_AMDNBIOPCIEAER_FILECODE                           (0xA924)
#define NBIO_STXKRK_DXE_AMDNBIOPCIEASPM_FILECODE                          (0xA925)
#define NBIO_STXKRK_DXE_AMDNBIOPCIECLKREQ_FILECODE                        (0xA926)
#define NBIO_STXKRK_DXE_AMDNBIOPCIESRIS_FILECODE                          (0xA927)
#define NBIO_STXKRK_DXE_AMDPSPPSTXKRK_FILECODE                            (0xA928)
#define NBIO_STXKRK_DXE_CPPCSERVICESPROTOCOL_FILECODE                     (0xA92A)
#define NBIO_STXKRK_DXE_ENTRYPOINTS_FILECODE                              (0xA92B)
#define NBIO_STXKRK_DXE_FINALINITCALLBACK_FILECODE                        (0xA92C)
#define NBIO_STXKRK_DXE_GFXINIT_FILECODE                                  (0xA92D)
#define NBIO_STXKRK_DXE_INFOCOLLECTION_FILECODE                           (0xA92E)
#define NBIO_STXKRK_DXE_IOMMUINIT_FILECODE                                (0xA92F)
#define NBIO_STXKRK_DXE_PCIEINIT_FILECODE                                 (0xA930)
#define NBIO_STXKRK_DXE_SMUINIT_FILECODE                                  (0xA931)
#define NBIO_STXKRK_DXE_SMUSERVICESPROTOCOL_FILECODE                      (0xA932)
#define NBIO_STXKRK_DXE_SMUSUPPORTFUNCTIONS_FILECODE                      (0xA933)
#define NBIO_STXKRK_DXE_AFTERCCXCALLBACK_FILECODE                         (0xA934)
#define NBIO_STXKRK_DXE_MPIOSERVICESPROTOCOL_FILECODE                     (0xA935)
#define NBIO_STXKRK_PEI_DXIOUSB4_FILECODE                                 (0xA936)
#define NBIO_STXKRK_PEI_GFXENUMCONNECTORS_FILECODE                        (0xA937)
#define NBIO_STXKRK_COMMON_PCIESTRAPS_FILECODE                            (0xA938)
#define NBIO_STXKRK_DXE_CORERANKINGTABLESERVICESPROTOCOL_FILECODE         (0xA939)
#define NBIO_STXKRK_DXE_PCIELTR_FILECODE                                  (0xA93A)
#define NBIO_STXKRK_PEI_IOMMUFEATUREPPI_FILECODE                          (0xA93B)
#define NBIO_STXKRK_DXE_PCIEEXTENDEDSYNC_FILECODE                         (0xA93C)
#define NBIO_STXKRK_SMM_NBIOSMMSTXKRK_FILECODE                            (0xA93D)


// BRH
#define NBIO_BRH_PEI_BASEINIT_FILECODE                                     (0xA940)
#define NBIO_BRH_PEI_CXLINIT_FILECODE                                      (0xA942)
#define NBIO_BRH_PEI_DPCSTATUSREPORT_FILECODE                              (0xA943)
#define NBIO_BRH_PEI_DXIOCFGPOINTS_FILECODE                                (0xA944)
#define NBIO_BRH_PEI_DXIOINIT_FILECODE                                     (0xA945)
#define NBIO_BRH_PEI_PCIEEARLYTRAIN_FILECODE                               (0xA946)
#define NBIO_BRH_PEI_ENTRYPOINTS_FILECODE                                  (0xA948)
#define NBIO_BRH_PEI_HIDEPORTS_FILECODE                                    (0xA949)
#define NBIO_BRH_PEI_IOAPICINIT_FILECODE                                   (0xA94A)
#define NBIO_BRH_PEI_IOMMUINIT_FILECODE                                    (0xA94B)
#define NBIO_BRH_PEI_NBIOTABLES_FILECODE                                   (0xA94C)
#define NBIO_BRH_PEI_NBIOTABLESMP_FILECODE                                 (0xA94D)
#define NBIO_BRH_PEI_NBIOTOPMEM_FILECODE                                   (0xA94E)
#define NBIO_BRH_PEI_NTBFEATURE_FILECODE                                   (0xA94F)
#define NBIO_BRH_PEI_PCIECOMPLEXDATA_FILECODE                              (0xA950)
#define NBIO_BRH_PEI_PCIEHOTPLUG_FILECODE                                  (0xA951)
#define NBIO_BRH_PEI_PCIEREMAP_FILECODE                                    (0xA952)
#define NBIO_BRH_PEI_PCIESTRAPS_FILECODE                                   (0xA953)
#define NBIO_BRH_PEI_PKGTYPEFIXUPS_FILECODE                                (0xA954)
#define NBIO_BRH_PEI_SMUINIT_FILECODE                                      (0xA955)
#define NBIO_BRH_PEI_SMUSERVICESPPI_FILECODE                               (0xA956)
#define NBIO_BRH_PEI_TOPOLOGYWA_FILECODE                                   (0xA957)
#define NBIO_BRH_PEI_MPIOTUNNEL_FILECODE                                   (0xA958)
#define NBIO_BRH_DXE_CPPCSERVICESPROTOCOL_FILECODE                         (0xA959)
#define NBIO_BRH_DXE_CXLCONFIGPORTFEATURE_FILECODE                         (0xA95A)
#define NBIO_BRH_DXE_ENTRYPOINTS_FILECODE                                  (0xA95B)
#define NBIO_BRH_DXE_IOMMUINIT_FILECODE                                    (0xA95D)
#define NBIO_BRH_DXE_NBIOHWLOCK_FILECODE                                   (0xA95E)
#define NBIO_BRH_DXE_PCIEAER_FILECODE                                      (0xA95F)
#define NBIO_BRH_DXE_PCIEINIT_FILECODE                                     (0xA961)
#define NBIO_BRH_DXE_PCIESRIS_FILECODE                                     (0xA962)
#define NBIO_BRH_DXE_PCIETOPOLOGYPROTOCOL_FILECODE                         (0xA963)
#define NBIO_BRH_DXE_PCIETYPEFIXUPS_FILECODE                               (0xA964)
#define NBIO_BRH_DXE_PMMTABLEINIT_FILECODE                                 (0xA965)
#define NBIO_BRH_DXE_RASCONTROL_FILECODE                                   (0xA966)
#define NBIO_BRH_DXE_SERVERHOTPLUGFEAT_FILECODE                            (0xA967)
#define NBIO_BRH_DXE_SMUDEBUG_FILECODE                                     (0xA968)
#define NBIO_BRH_DXE_SMUINIT_FILECODE                                      (0xA969)
#define NBIO_BRH_DXE_SMUSERVICESPROTOCOL_FILECODE                          (0xA96A)
#define NBIO_BRH_DXE_SMUSUPPORTFUNCTIONS_FILECODE                          (0xA96B)
#define NBIO_BRH_PEI_IOMMUTABLES_FILECODE                                  (0xA96C)
#define NBIO_BRH_PEI_NBIOIOHCBRHTBL_FILECODE                               (0xA96D)
#define NBIO_BRH_PEI_NBIONBIFINIT_FILECODE                                 (0xA96E)
#define LIBRARY_NBIOIDSHOOKBRHLIB_DXE_NBIOIDSHOOKBRHLIBDXE_FILECODE        (0xA96F)
#define LIBRARY_NBIOIDSHOOKBRHLIB_PEI_NBIOIDSHOOKBRHLIBPEI_FILECODE        (0xA970)
#define LIBRARY_NBIOSMUBRHLIB_NBIOSMUBRHLIB_FILECODE                       (0xA971)
#define NBIO_BRH_DXE_CXLHOTPLUGFEATURES_FILECODE                           (0xA978)
#define NBIO_BRH_PEI_HARVESTFILE_FILECODE                                  (0xA979)
#define NBIO_BRH_PEI_IOMMUFEATUREPPI_FILECODE                              (0xA97A)

//IVRSLibV3
#define NBIO_LIBRARY_IVRSLIBV3_NBIOIVRSTABLE_FILECODE                     (0xA972)
#define NBIO_LIBRARY_IVRSLIBV3_IVRSDEBUGDUMP_FILECODE                     (0xA973)
#define NBIO_LIBRARY_IVRSLIBV3_NBIOIVMDLIB_FILECODE                       (0xA974)
#define NBIO_LIBRARY_IVRSLIBV3_NBIOIVRSLIB_FILECODE                       (0xA975)
#define NBIO_LIBRARY_IVRSLIBV3_IVRSSBDEVICES_FILECODE                     (0xA976)
#define NBIO_LIBRARY_IVRSLIBV3_NBIOIOAPICINIT_FILECODE                    (0xA977)

// PHX
#define NBIO_PHX_PEI_DXIOUSB4_FILECODE                                     (0xA980)
#define NBIO_PHX_PEI_FAMILY_SEPARATE_FILECODE                              (0xA981)
#define NBIO_PHX_PEI_NBIOPHXFAMILYSEPARATEPEI_FILECODE                     (0xA982)
#define NBIO_PHX_LIBRARY_COLLECTNBIFPORTINFOLIB_COLLECTNBIFPORTINFOLIB_FILECODE (0xA983)

// STXH
#define NBIO_STXH_PEI_SMUINIT_FILECODE                                    (0xA990)
#define NBIO_STXH_PEI_CCXTOPOLOGYPPI_FILECODE                             (0xA991)
#define NBIO_STXH_PEI_EARLYINITFEATURE_FILECODE                           (0xA992)
#define NBIO_STXH_PEI_SMUSERVICESPPI_FILECODE                             (0xA993)
#define NBIO_STXH_PEI_IOIOAPIC_FILECODE                                   (0xA994)
#define NBIO_STXH_PEI_IOMMUINIT_FILECODE                                  (0xA995)
#define NBIO_STXH_PEI_BASEINIT_FILECODE                                   (0xA996)
#define NBIO_STXH_PEI_NBIOTABLES_FILECODE                                 (0xA997)
#define NBIO_STXH_PEI_NBIOTOPMEM_FILECODE                                 (0xA998)
#define NBIO_STXH_PEI_PCIECOMPLEXDATA_FILECODE                            (0xA999)
#define NBIO_STXH_PEI_PCIEINIT_FILECODE                                   (0xA99A)
#define NBIO_STXH_PEI_DEVICECONTROL_FILECODE                              (0xA99B)
#define NBIO_STXH_PEI_DPCSTATUSREPORT_FILECODE                            (0xA99C)
#define NBIO_STXH_PEI_DXIOCFGPOINTS_FILECODE                              (0xA99D)
#define NBIO_STXH_PEI_DXIOINIT_FILECODE                                   (0xA99E)
#define NBIO_STXH_PEI_HIDEPORTS_FILECODE                                  (0xA99F)
#define NBIO_STXH_PEI_PCIEREMAP_FILECODE                                  (0xA9A0)
#define NBIO_STXH_PEI_DXIOTOPOLOGY_FILECODE                               (0xA9A1)
#define NBIO_STXH_PEI_PCIEHOTPLUG_FILECODE                                (0xA9A2)
#define NBIO_STXH_PEI_EARLYLINKPPI_FILECODE                               (0xA9A3)
#define NBIO_STXH_PEI_PKGTYPEFIXUPS_FILECODE                              (0xA9A4)
#define NBIO_STXH_PEI_GFXINIT_FILECODE                                    (0xA9A5)
#define NBIO_STXH_PEI_SMU14BIOSINTERFACETABLE_FILECODE                    (0xA9A6)
#define NBIO_STXH_PEI_SMUSUPPORTFUNCTIONS_FILECODE                        (0xA9A7)
#define NBIO_STXH_PEI_ENTRYPOINTS_FILECODE                                (0xA9A8)
#define NBIO_STXH_PEI_GFXINFOCOLLECTION_FILECODE                          (0xA9A9)
#define NBIO_STXH_PEI_INFOCOLLECTION_FILECODE                             (0xA9AA)
#define NBIO_STXH_PEI_SMUPKGTYPEFIXUPS_FILECODE                           (0xA9AB)
#define NBIO_STXH_PEI_CDMAINIT_FILECODE                                   (0xA9AC)
#define NBIO_STXH_PEI_IOMMUTABLES_FILECODE                                (0xA9AD)
#define NBIO_STXH_PEI_FANPOLICYDATA_FILECODE                              (0xA9AE)
#define NBIO_STXH_PEI_IOAPICINIT_FILECODE                                 (0xA9AF)

#define NBIO_STXH_DXE_ALIB_ALIBINIT_FILECODE                              (0xA9B0)
#define NBIO_STXH_DXE_AMDAPLUSA_FILECODE                                  (0xA9B1)
#define NBIO_STXH_DXE_ALIB_AMDNBIOALIBCALLBACK_FILECODE                   (0xA9B2)
#define NBIO_STXH_DXE_AMDNBIOEXITLATENCY_FILECODE                         (0xA9B3)
#define NBIO_STXH_DXE_AMDNBIOPCIEAER_FILECODE                             (0xA9B4)
#define NBIO_STXH_DXE_AMDNBIOPCIEASPM_FILECODE                            (0xA9B5)
#define NBIO_STXH_DXE_AMDNBIOPCIECLKREQ_FILECODE                          (0xA9B6)
#define NBIO_STXH_DXE_AMDNBIOPCIESRIS_FILECODE                            (0xA9B7)
#define NBIO_STXH_DXE_AMDPSPPSTXH_FILECODE                                (0xA9B8)
#define NBIO_STXH_DXE_CCXTOPOLOGYPROTOCOL_FILECODE                        (0xA9B9)
#define NBIO_STXH_DXE_CPPCSERVICESPROTOCOL_FILECODE                       (0xA9BA)
#define NBIO_STXH_DXE_ENTRYPOINTS_FILECODE                                (0xA9BB)
#define NBIO_STXH_DXE_FINALINITCALLBACK_FILECODE                          (0xA9BC)
#define NBIO_STXH_DXE_GFXINIT_FILECODE                                    (0xA9BD)
#define NBIO_STXH_DXE_INFOCOLLECTION_FILECODE                             (0xA9BE)
#define NBIO_STXH_DXE_IOMMUINIT_FILECODE                                  (0xA9BF)
#define NBIO_STXH_DXE_PCIEINIT_FILECODE                                   (0xA9C0)
#define NBIO_STXH_DXE_SMUINIT_FILECODE                                    (0xA9C1)
#define NBIO_STXH_DXE_SMUSERVICESPROTOCOL_FILECODE                        (0xA9C2)
#define NBIO_STXH_DXE_SMUSUPPORTFUNCTIONS_FILECODE                        (0xA9C3)
#define NBIO_STXH_DXE_AFTERCCXCALLBACK_FILECODE                           (0xA9C4)
#define NBIO_STXH_DXE_MPIOSERVICESPROTOCOL_FILECODE                       (0xA9C5)
#define NBIO_STXH_PEI_DXIOUSB4_FILECODE                                   (0xA9C6)
#define NBIO_STXH_PEI_GFXENUMCONNECTORS_FILECODE                          (0xA9C7)
#define NBIO_STXH_COMMON_PCIESTRAPS_FILECODE                              (0xA9C8)
#define NBIO_STXH_DXE_CORERANKINGTABLESERVICESPROTOCOL_FILECODE           (0xA9C9)
#define NBIO_STXH_DXE_PCIELTR_FILECODE                                    (0xA9CA)
#define NBIO_STXH_PEI_IOMMUFEATUREPPI_FILECODE                            (0xA9CB)
#define NBIO_STXH_DXE_AMDNBIOPCIELCRECOVERY_FILECODE                      (0xA9CC)
#define NBIO_STXH_DXE_AMDPCIELCRECOVERYEXTSYS_FILECODE                    (0xA9CD)

// WH
#define NBIO_WH_PEI_DPCSTATUSREPORT_FILECODE                              (0xA9E0)
#define NBIO_WH_PEI_ENTRYPOINTS_FILECODE                                  (0xA9E1)
#define NBIO_WH_PEI_PCIECOMPLEX_FILECODE                                  (0xA9E2)
#define NBIO_WH_PEI_PKGTYPEFIXUPS_FILECODE                                (0xA9E3)

//FCH
#define LIBRARY_FCHDXELIB_FCHDXELIB_FILECODE                              (0xB000)
#define LIBRARY_FCHPEILIB_FCHPEILIB_FILECODE                              (0xB001)
#define LIBRARY_FCHSMMLIB_FCHSMMLIB_FILECODE                              (0xB002)
#define LIBRARY_FCHBASELIB_FCHACPILIB_FILECODE                            (0xB003)
#define LIBRARY_FCHBASELIB_FCHPMIOLIB_FILECODE                            (0xB004)
#define LIBRARY_FCHBASELIB_FCHSOCLIB_FILECODE                             (0xB005)
#define LIBRARY_FCHBASELIB_FCHCHIPSETLIB_FILECODE                         (0xB006)
#define LIBRARY_FCHBASELIB_FCHSTALLLIB_FILECODE                           (0xB007)
#define LIBRARY_FCHBASELIB_FCHPCILIB_FILECODE                             (0xB008)
#define LIBRARY_FCHBASELIB_FCHROMACCESSLIB_FILECODE                       (0xB009)
#define LIBRARY_FCHBASELIB_FCHSCILIB_FILECODE                             (0xB00A)
#define LIBRARY_FCHBASELIB_FCHCONFIGLIB_FILECODE                          (0xB00B)
#define LIBRARY_FCHBASELIB_FCHALINKLIB_FILECODE                           (0xB00C)
#define LIBRARY_FCHBASELIB_FCHPMIO2LIB_FILECODE                           (0xB00D)
#define LIBRARY_FCHBASELIB_FCHBIOSRAMLIB_FILECODE                         (0xB00E)
#define LIBRARY_FCHBASELIB_FCHSMILIB_FILECODE                             (0xB00F)
#define LIBRARY_FCHBASELIB_FCHSMNLIB_FILECODE                             (0xB010)
#define LIBRARY_FCHBASELIB_FCHGPIOLIB_FILECODE                            (0xB011)
#define LIBRARY_FCHBASERESETSYSTEMLIB_FCHBASERESETSYSTEMLIB_FILECODE      (0xB012)
#define LIBRARY_FCHIDSHOOKLIB_FCHIDSHOOKLIB_FILECODE                      (0xB013)
#define LIBRARY_FCHINITHOOKLIB_FCHINITHOOKLIBDXE_FILECODE                 (0xB014)
#define LIBRARY_FCHINITHOOKLIB_FCHINITHOOKLIBPEI_FILECODE                 (0xB015)
#define LIBRARY_AMDRTCLIB_AMDRTC_FILECODE                                 (0xB016)
#define LIBRARY_FCHDXELEGACYINTERRUPTLIB_FCHDXELEGACYINTERRUPTLIB_FILECODE (0xB017)
#define LIBRARY_FCHDXERUNTIMERESETSYSTEMLIB_SONGSHAN_FCHDXERUNTIMERESETSYSTEMLIB_FILECODE                    (0xB018)
#define LIBRARY_FCHBASERESETSYSTEMLIB_FCHFORCERESETSYSTEMLIB_FILECODE     (0xB019)
#define LIBRARY_FCHBASELIB_FCHIOLIB_FILECODE                              (0xB01A)

#define LIBRARY_FCHSPIACCESSLIB_FCHSPIACCESSSMNLIB_FILECODE               (0xB021)
#define LIBRARY_FCHSPIACCESSLIB_FCHSPIACCESSROM2LIB_FILECODE              (0xB022)
#define LIBRARY_FCHSPIACCESSLIB_FCHSPIACCESSROM3LIB_FILECODE              (0xB023)
#define LIBRARY_FCHSPIACCESSLIB_FCHSPIACCESSROMARMOR2LIB_FILECODE         (0xB024)
#define LIBRARY_FCHSPIACCESSLIB_FCHSPIACCESSSMNV2LIB_FILECODE             (0xB025)
#define LIBRARY_FCHSPIACCESSLIB_FCHSPIACCESSROM2V2LIB_FILECODE            (0xB026)
#define LIBRARY_FCHSPIACCESSLIB_FCHSPIACCESSROM3V2LIB_FILECODE            (0xB027)
#define LIBRARY_FCHSPIACCESSLIB_FCHSPIACCESSROMARMOR2V2LIB_FILECODE       (0xB028)
#define LIBRARY_FCHSPIACCESSLIB_FCHSPIACCESSROMNULL_FILECODE              (0xB029)
#define LIBRARY_FCHSPIACCESSLIB_FCHSPIACCESSROMV2NULL_FILECODE            (0xB02A)
#define LIBRARY_FCHSPIACCESSLIB_FCHSPIACCESSCOMMON_FILECODE               (0xB02B)
#define LIBRARY_FCHESPILIB_FCHESPIRSLIB_FILECODE                          (0xB02C)
#define LIBRARY_FCHSOCLIB_SONGSHAN_FCHSONGSHANLIB_FILECODE                (0xB02D)
#define LIBRARY_FCHSOCLIB_QIANTANG_FCHQIANTANGLIB_FILECODE                (0xB02E)
#define LIBRARY_FCHSOCLIB_HUANGSHAN_FCHHUANGSHANLIB_FILECODE              (0xB02F)
#define LIBRARY_FCHSOCLIB_TACOMA_FCHTACOMALIB_FILECODE                    (0xB030)
#define LIBRARY_FCHSOCLIB_YUNTAI_FCHYUNTAILIB_FILECODE                    (0xB031)
#define LIBRARY_FCHSOCLIB_ALISHAN_FCHALISHANLIB_FILECODE                  (0xB032)

#define LIBRARY_FCHI2CLIB_FCHI2CLIB_FILECODE                              (0xB033)
#define LIBRARY_FCHI3CLIB_FCHI3CLIB_FILECODE                              (0xB034)
#define LIBRARY_FCHI3CLIB_UNITTEST_FCHI3CLIBUNITTEST_FILECODE             (0xB035)
#define LIBRARY_FCHSOCLIB_BREITHORN_FCHBREITHORNLIB_FILECODE              (0xB036)
#define LIBRARY_FCHTESTLIB_FCHTESTLIB_FILECODE                            (0xB037)
#define LIBRARY_FCHI3CLIB_FCHI3CHCILIB_FILECODE                           (0xB038)


#define FCH_KERN_FCHKERNCORE_COMMON_ACPILIB_FILECODE                      (0xB100)
#define FCH_KERN_FCHKERNCORE_COMMON_FCHCOMMONSMM_FILECODE                 (0xB101)
#define FCH_KERN_FCHKERNCORE_COMMON_FCHCOMMON_FILECODE                    (0xB102)
#define FCH_KERN_FCHKERNCORE_COMMON_FCHLIB_FILECODE                       (0xB103)
#define FCH_KERN_FCHKERNCORE_COMMON_FCHPELIB_FILECODE                     (0xB104)
#define FCH_KERN_FCHKERNCORE_COMMON_MEMLIB_FILECODE                       (0xB105)
#define FCH_KERN_FCHKERNCORE_COMMON_PCILIB_FILECODE                       (0xB106)
#define FCH_KERN_FCHKERNCORE_KERN_KERNAZALIA_KERNAZALIAENV_FILECODE       (0xB110)
#define FCH_KERN_FCHKERNCORE_KERN_KERNAZALIA_KERNAZALIALATE_FILECODE      (0xB111)
#define FCH_KERN_FCHKERNCORE_KERN_KERNAZALIA_KERNAZALIAMID_FILECODE       (0xB112)
#define FCH_KERN_FCHKERNCORE_KERN_KERNHWACPI_KERNHWACPIENVSERVICE_FILECODE     (0xB113)
#define FCH_KERN_FCHKERNCORE_KERN_KERNHWACPI_KERNHWACPIENV_FILECODE       (0xB114)
#define FCH_KERN_FCHKERNCORE_KERN_KERNHWACPI_KERNHWACPILATESERVICE_FILECODE    (0xB115)
#define FCH_KERN_FCHKERNCORE_KERN_KERNHWACPI_KERNHWACPILATE_FILECODE      (0xB116)
#define FCH_KERN_FCHKERNCORE_KERN_KERNHWACPI_KERNHWACPIMID_FILECODE       (0xB117)
#define FCH_KERN_FCHKERNCORE_KERN_KERNHWACPI_KERNHWACPIRESET_FILECODE     (0xB118)
#define FCH_KERN_FCHKERNCORE_KERN_KERNHWACPI_KERNSSSERVICE_FILECODE       (0xB119)
#define FCH_KERN_FCHKERNCORE_KERN_KERNHWM_KERNHWMENVSERVICE_FILECODE      (0xB120)
#define FCH_KERN_FCHKERNCORE_KERN_KERNHWM_KERNHWMENV_FILECODE             (0xB121)
#define FCH_KERN_FCHKERNCORE_KERN_KERNHWM_KERNHWMLATESERVICE_FILECODE     (0xB122)
#define FCH_KERN_FCHKERNCORE_KERN_KERNHWM_KERNHWMLATE_FILECODE            (0xB123)
#define FCH_KERN_FCHKERNCORE_KERN_KERNHWM_KERNHWMMID_FILECODE             (0xB124)
#define FCH_KERN_FCHKERNCORE_KERN_KERNHWM_KERNHWMRESET_FILECODE           (0xB125)
#define FCH_KERN_FCHKERNCORE_KERN_KERNIMC_KERNFCHECENV_FILECODE           (0xB126)
#define FCH_KERN_FCHKERNCORE_KERN_KERNIMC_KERNFCHECLATE_FILECODE          (0xB127)
#define FCH_KERN_FCHKERNCORE_KERN_KERNIMC_KERNFCHECMID_FILECODE           (0xB128)
#define FCH_KERN_FCHKERNCORE_KERN_KERNIMC_KERNFCHECRESET_FILECODE         (0xB129)
#define FCH_KERN_FCHKERNCORE_KERN_KERNIMC_KERNIMCENV_FILECODE             (0xB12A)
#define FCH_KERN_FCHKERNCORE_KERN_KERNIMC_KERNIMCLATE_FILECODE            (0xB12B)
#define FCH_KERN_FCHKERNCORE_KERN_KERNIMC_KERNIMCLIB_FILECODE             (0xB12C)
#define FCH_KERN_FCHKERNCORE_KERN_KERNIMC_KERNIMCMID_FILECODE             (0xB12D)
#define FCH_KERN_FCHKERNCORE_KERN_KERNIMC_KERNIMCRESET_FILECODE           (0xB12E)
#define FCH_KERN_FCHKERNCORE_KERN_KERNIMC_KERNIMCSERVICE_FILECODE         (0xB12F)
#define FCH_KERN_FCHKERNCORE_KERN_KERNINTERFACE_KERNFCHINITENV_FILECODE   (0xB130)
#define FCH_KERN_FCHKERNCORE_KERN_KERNINTERFACE_KERNFCHINITLATE_FILECODE  (0xB131)
#define FCH_KERN_FCHKERNCORE_KERN_KERNINTERFACE_KERNFCHINITMID_FILECODE   (0xB132)
#define FCH_KERN_FCHKERNCORE_KERN_KERNINTERFACE_KERNFCHINITRESET_FILECODE (0xB133)
#define FCH_KERN_FCHKERNCORE_KERN_KERNINTERFACE_KERNFCHINITS3_FILECODE    (0xB134)
#define FCH_KERN_FCHKERNCORE_KERN_KERNINTERFACE_KERNFCHTASKLAUNCHER_FILECODE   (0xB135)
#define FCH_KERN_FCHKERNCORE_KERN_KERNIR_KERNIRENV_FILECODE               (0xB136)
#define FCH_KERN_FCHKERNCORE_KERN_KERNIR_KERNIRLATE_FILECODE              (0xB137)
#define FCH_KERN_FCHKERNCORE_KERN_KERNIR_KERNIRMID_FILECODE               (0xB138)
#define FCH_KERN_FCHKERNCORE_KERN_KERNPCIE_KERNABENVSERVICE_FILECODE      (0xB139)
#define FCH_KERN_FCHKERNCORE_KERN_KERNPCIE_KERNABENV_FILECODE             (0xB13A)
#define FCH_KERN_FCHKERNCORE_KERN_KERNPCIE_KERNABLATE_FILECODE            (0xB13B)
#define FCH_KERN_FCHKERNCORE_KERN_KERNPCIE_KERNABMID_FILECODE             (0xB13C)
#define FCH_KERN_FCHKERNCORE_KERN_KERNPCIE_KERNABRESETSERVICE_FILECODE    (0xB13D)
#define FCH_KERN_FCHKERNCORE_KERN_KERNPCIE_KERNABRESET_FILECODE           (0xB13E)
#define FCH_KERN_FCHKERNCORE_KERN_KERNPCIE_KERNABSERVICE_FILECODE         (0xB13F)
#define FCH_KERN_FCHKERNCORE_KERN_KERNSATA_KERNAHCIENV_FILECODE           (0xB140)
#define FCH_KERN_FCHKERNCORE_KERN_KERNSATA_KERNAHCILATE_FILECODE          (0xB141)
#define FCH_KERN_FCHKERNCORE_KERN_KERNSATA_KERNAHCILIB_FILECODE           (0xB142)
#define FCH_KERN_FCHKERNCORE_KERN_KERNSATA_KERNAHCIMID_FILECODE           (0xB143)
#define FCH_KERN_FCHKERNCORE_KERN_KERNSATA_KERNIDE2AHCIENV_FILECODE       (0xB144)
#define FCH_KERN_FCHKERNCORE_KERN_KERNSATA_KERNIDE2AHCILATE_FILECODE      (0xB145)
#define FCH_KERN_FCHKERNCORE_KERN_KERNSATA_KERNIDE2AHCILIB_FILECODE       (0xB146)
#define FCH_KERN_FCHKERNCORE_KERN_KERNSATA_KERNIDE2AHCIMID_FILECODE       (0xB147)
#define FCH_KERN_FCHKERNCORE_KERN_KERNSATA_KERNRAIDENV_FILECODE           (0xB148)
#define FCH_KERN_FCHKERNCORE_KERN_KERNSATA_KERNRAIDLATE_FILECODE          (0xB149)
#define FCH_KERN_FCHKERNCORE_KERN_KERNSATA_KERNRAIDLIB_FILECODE           (0xB14A)
#define FCH_KERN_FCHKERNCORE_KERN_KERNSATA_KERNRAIDMID_FILECODE           (0xB14B)
#define FCH_KERN_FCHKERNCORE_KERN_KERNSATA_KERNSATAENVLIB_FILECODE        (0xB14C)
#define FCH_KERN_FCHKERNCORE_KERN_KERNSATA_KERNSATAENVSERVICE_FILECODE    (0xB14D)
#define FCH_KERN_FCHKERNCORE_KERN_KERNSATA_KERNSATAENV_FILECODE           (0xB14E)
#define FCH_KERN_FCHKERNCORE_KERN_KERNSATA_KERNSATAIDELIB_FILECODE        (0xB150)
#define FCH_KERN_FCHKERNCORE_KERN_KERNSATA_KERNSATAIDEENV_FILECODE        (0xB151)
#define FCH_KERN_FCHKERNCORE_KERN_KERNSATA_KERNSATAIDELATE_FILECODE       (0xB152)
#define FCH_KERN_FCHKERNCORE_KERN_KERNSATA_KERNSATAIDEMID_FILECODE        (0xB153)
#define FCH_KERN_FCHKERNCORE_KERN_KERNSATA_KERNSATALATE_FILECODE          (0xB154)
#define FCH_KERN_FCHKERNCORE_KERN_KERNSATA_KERNSATALIB_FILECODE           (0xB155)
#define FCH_KERN_FCHKERNCORE_KERN_KERNSATA_KERNSATAMID_FILECODE           (0xB156)
#define FCH_KERN_FCHKERNCORE_KERN_KERNSATA_KERNSATARESETSERVICE_FILECODE  (0xB157)
#define FCH_KERN_FCHKERNCORE_KERN_KERNSATA_KERNSATARESET_FILECODE         (0xB158)
#define FCH_KERN_FCHKERNCORE_KERN_KERNSATA_KERNSATASERVICE_FILECODE       (0xB159)
#define FCH_KERN_FCHKERNCORE_KERN_KERNSD_KERNSDENVSERVICE_FILECODE        (0xB160)
#define FCH_KERN_FCHKERNCORE_KERN_KERNSD_KERNSDENV_FILECODE               (0xB161)
#define FCH_KERN_FCHKERNCORE_KERN_KERNSD_KERNSDLATE_FILECODE              (0xB162)
#define FCH_KERN_FCHKERNCORE_KERN_KERNSD_KERNSDMID_FILECODE               (0xB163)
#define FCH_KERN_FCHKERNCORE_KERN_KERNSPI_KERNLPCENVSERVICE_FILECODE      (0xB164)
#define FCH_KERN_FCHKERNCORE_KERN_KERNSPI_KERNLPCENV_FILECODE             (0xB165)
#define FCH_KERN_FCHKERNCORE_KERN_KERNSPI_KERNLPCLATE_FILECODE            (0xB166)
#define FCH_KERN_FCHKERNCORE_KERN_KERNSPI_KERNLPCMID_FILECODE             (0xB167)
#define FCH_KERN_FCHKERNCORE_KERN_KERNSPI_KERNLPCRESETSERVICE_FILECODE    (0xB168)
#define FCH_KERN_FCHKERNCORE_KERN_KERNSPI_KERNLPCRESET_FILECODE           (0xB169)
#define FCH_KERN_FCHKERNCORE_KERN_KERNSPI_KERNSPIENV_FILECODE             (0xB16A)
#define FCH_KERN_FCHKERNCORE_KERN_KERNSPI_KERNSPILATE_FILECODE            (0xB16B)
#define FCH_KERN_FCHKERNCORE_KERN_KERNSPI_KERNSPIMID_FILECODE             (0xB16C)
#define FCH_KERN_FCHKERNCORE_KERN_KERNUSB_KERNEHCIENV_FILECODE            (0xB170)
#define FCH_KERN_FCHKERNCORE_KERN_KERNUSB_KERNEHCILATE_FILECODE           (0xB171)
#define FCH_KERN_FCHKERNCORE_KERN_KERNUSB_KERNEHCIMIDSERVICE_FILECODE     (0xB172)
#define FCH_KERN_FCHKERNCORE_KERN_KERNUSB_KERNEHCIMID_FILECODE            (0xB173)
#define FCH_KERN_FCHKERNCORE_KERN_KERNUSB_KERNEHCIRESET_FILECODE          (0xB174)
#define FCH_KERN_FCHKERNCORE_KERN_KERNUSB_KERNUSBENV_FILECODE             (0xB175)
#define FCH_KERN_FCHKERNCORE_KERN_KERNUSB_KERNUSBLATE_FILECODE            (0xB176)
#define FCH_KERN_FCHKERNCORE_KERN_KERNUSB_KERNUSBMID_FILECODE             (0xB177)
#define FCH_KERN_FCHKERNCORE_KERN_KERNUSB_KERNUSBRESET_FILECODE           (0xB178)
#define FCH_KERN_FCHKERNCORE_KERN_KERNUSB_KERNXHCIENVSERVICE_FILECODE     (0xB179)
#define FCH_KERN_FCHKERNCORE_KERN_KERNUSB_KERNXHCIENV_FILECODE            (0xB17A)
#define FCH_KERN_FCHKERNCORE_KERN_KERNUSB_KERNXHCILATESERVICE_FILECODE    (0xB17B)
#define FCH_KERN_FCHKERNCORE_KERN_KERNUSB_KERNXHCILATE_FILECODE           (0xB17C)
#define FCH_KERN_FCHKERNCORE_KERN_KERNUSB_KERNXHCIMID_FILECODE            (0xB17D)
#define FCH_KERN_FCHKERNCORE_KERN_KERNUSB_KERNXHCIRESETSERVICE_FILECODE   (0xB17E)
#define FCH_KERN_FCHKERNCORE_KERN_KERNUSB_KERNXHCIRESET_FILECODE          (0xB17F)
#define FCH_KERN_FCHKERNCORE_KERN_KERNESPI_KERNESPIENV_FILECODE           (0xB191)
#define FCH_KERN_FCHKERNCORE_KERN_KERNESPI_KERNESPIMID_FILECODE           (0xB192)
#define FCH_KERN_FCHKERNCORE_KERN_KERNESPI_KERNESPILATE_FILECODE          (0xB193)
#define FCH_KERN_FCHKERNCORE_KERN_KERNESPI_KERNESPILIB_FILECODE           (0xB194)

#define FCH_KERN_FCHKERNCF9RESETDXE_CF9RESET_FILECODE                     (0xB200)
#define FCH_KERN_FCHKERNCOMPLEMENT_IMCCONTROL_IMCCONTROLLIB_FILECODE      (0xB201)
#define FCH_KERN_FCHKERNCOMPLEMENT_IMCCONTROL_IMCCONTROL_FILECODE         (0xB202)
#define FCH_KERN_FCHKERNDXE_FCHDXE_FILECODE                               (0xB203)
#define FCH_KERN_FCHKERNDXE_USBOC_FILECODE                                (0xB204)
#define FCH_KERN_FCHKERNLEGACYINTERRUPTDXE_LEGACYINTERRUPT_FILECODE       (0xB205)
#define FCH_KERN_FCHKERNPEI_FCHPEI_FILECODE                               (0xB206)
#define FCH_KERN_FCHKERNPEI_FCHRESET_FILECODE                             (0xB207)
#define FCH_KERN_FCHKERNPEI_FCHSTALL_FILECODE                             (0xB208)
#define FCH_KERN_FCHKERNSMBUSDXE_SMBUSLIGHT_FILECODE                      (0xB209)
#define FCH_KERN_FCHKERNSMBUSPEI_SMBUS_FILECODE                           (0xB20A)
#define FCH_KERN_FCHKERNSMMCONTROLDXE_SMMCONTROL_FILECODE                 (0xB20B)
#define FCH_KERN_FCHKERNSMMDISPATCHER_FCHSMMDIAGDISPATCHER_FILECODE                                  (0xB210)
#define FCH_KERN_FCHKERNSMMDISPATCHER_FCHSMMDISPATCHER_FILECODE                                      (0xB211)
#define FCH_KERN_FCHKERNSMMDISPATCHER_FCHSMMGPIDISPATCHER_FCHSMMGPIDISPATCHER_FILECODE               (0xB212)
#define FCH_KERN_FCHKERNSMMDISPATCHER_FCHSMMIOTRAPDISPATCHER_FCHSMMIOTRAPDISPATCHER_FILECODE         (0xB213)
#define FCH_KERN_FCHKERNSMMDISPATCHER_FCHSMMMISCDISPATCHER_FCHSMMMISCDISPATCHER_FILECODE             (0xB214)
#define FCH_KERN_FCHKERNSMMDISPATCHER_FCHSMMPERIODICALDISPATCHER_FCHSMMPERIODICALDISPATCHER_FILECODE (0xB215)
#define FCH_KERN_FCHKERNSMMDISPATCHER_FCHSMMPWRBTNDISPATCHER_FCHSMMPWRBTNDISPATCHER_FILECODE         (0xB216)
#define FCH_KERN_FCHKERNSMMDISPATCHER_FCHSMMSWDISPATCHER_FCHSMMSWDISPATCHER_FILECODE                 (0xB217)
#define FCH_KERN_FCHKERNSMMDISPATCHER_FCHSMMSXDISPATCHER_FCHSMMSXDISPATCHER_FILECODE                 (0xB218)
#define FCH_KERN_FCHKERNSMMDISPATCHER_FCHSMMUSBDISPATCHER_FCHSMMUSBDISPATCHER_FILECODE               (0xB219)
#define FCH_KERN_FCHKERNSMM_FCHSMM_FILECODE                               (0xB220)
#define FCH_KERN_FCHKERNSMM_GPISMI_FILECODE                               (0xB221)
#define FCH_KERN_FCHKERNSMM_IOTRAPSMI_FILECODE                            (0xB222)
#define FCH_KERN_FCHKERNSMM_MISCSMI_FILECODE                              (0xB223)
#define FCH_KERN_FCHKERNSMM_PERIODICTIMERSMI_FILECODE                     (0xB224)
#define FCH_KERN_FCHKERNSMM_POWERBUTTONSMI_FILECODE                       (0xB225)
#define FCH_KERN_FCHKERNSMM_SWSMI_FILECODE                                (0xB226)
#define FCH_KERN_FCHKERNSMM_SXSMI_FILECODE                                (0xB227)

#define FCH_TAISHAN_FCHTAISHANCORE_COMMON_ACPILIB_FILECODE                (0xB300)
#define FCH_TAISHAN_FCHTAISHANCORE_COMMON_FCHCOMMONSMM_FILECODE           (0xB301)
#define FCH_TAISHAN_FCHTAISHANCORE_COMMON_FCHCOMMON_FILECODE              (0xB302)
#define FCH_TAISHAN_FCHTAISHANCORE_COMMON_FCHLIB_FILECODE                 (0xB303)
#define FCH_TAISHAN_FCHTAISHANCORE_COMMON_FCHPELIB_FILECODE               (0xB304)
#define FCH_TAISHAN_FCHTAISHANCORE_COMMON_MEMLIB_FILECODE                 (0xB305)
#define FCH_TAISHAN_FCHTAISHANCORE_COMMON_PCILIB_FILECODE                 (0xB306)
#define FCH_TAISHAN_FCHTAISHANCORE_COMMON_FCHAOACLIB_FILECODE             (0xB307)
#define FCH_TAISHAN_FCHTAISHANCORE_COMMON_FCHSMNLIB_FILECODE              (0xB308)
#define FCH_TAISHAN_FCHTAISHANCORE_TAISHAN_TSEMMC_TSEMMCENV_FILECODE      (0xB310)
#define FCH_TAISHAN_FCHTAISHANCORE_TAISHAN_TSEMMC_TSEMMCLATE_FILECODE     (0xB311)
#define FCH_TAISHAN_FCHTAISHANCORE_TAISHAN_TSEMMC_TSEMMCMID_FILECODE      (0xB312)
#define FCH_TAISHAN_FCHTAISHANCORE_TAISHAN_TSEMMC_TSEMMCRESET_FILECODE    (0xB313)
#define FCH_TAISHAN_FCHTAISHANCORE_TAISHAN_TSGBE_TSGBEENV_FILECODE        (0xB314)
#define FCH_TAISHAN_FCHTAISHANCORE_TAISHAN_TSGBE_TSGBELATE_FILECODE       (0xB315)
#define FCH_TAISHAN_FCHTAISHANCORE_TAISHAN_TSGBE_TSGBEMID_FILECODE        (0xB316)
#define FCH_TAISHAN_FCHTAISHANCORE_TAISHAN_TSGBE_TSGBERESET_FILECODE      (0xB317)
#define FCH_TAISHAN_FCHTAISHANCORE_TAISHAN_TSHWACPI_TSHWACPIENVSERVICE_FILECODE     (0xB318)
#define FCH_TAISHAN_FCHTAISHANCORE_TAISHAN_TSHWACPI_TSHWACPIENV_FILECODE            (0xB319)
#define FCH_TAISHAN_FCHTAISHANCORE_TAISHAN_TSHWACPI_TSHWACPILATESERVICE_FILECODE    (0xB31A)
#define FCH_TAISHAN_FCHTAISHANCORE_TAISHAN_TSHWACPI_TSHWACPILATE_FILECODE           (0xB31B)
#define FCH_TAISHAN_FCHTAISHANCORE_TAISHAN_TSHWACPI_TSHWACPIMID_FILECODE            (0xB31C)
#define FCH_TAISHAN_FCHTAISHANCORE_TAISHAN_TSHWACPI_TSHWACPIRESET_FILECODE          (0xB31D)
#define FCH_TAISHAN_FCHTAISHANCORE_TAISHAN_TSHWACPI_TSSSSERVICE_FILECODE            (0xB31E)
#define FCH_TAISHAN_FCHTAISHANCORE_TAISHAN_TSHWM_TSHWMENVSERVICE_FILECODE           (0xB320)
#define FCH_TAISHAN_FCHTAISHANCORE_TAISHAN_TSHWM_TSHWMENV_FILECODE                  (0xB321)
#define FCH_TAISHAN_FCHTAISHANCORE_TAISHAN_TSHWM_TSHWMLATESERVICE_FILECODE          (0xB322)
#define FCH_TAISHAN_FCHTAISHANCORE_TAISHAN_TSHWM_TSHWMLATE_FILECODE                 (0xB323)
#define FCH_TAISHAN_FCHTAISHANCORE_TAISHAN_TSHWM_TSHWMMID_FILECODE                  (0xB324)
#define FCH_TAISHAN_FCHTAISHANCORE_TAISHAN_TSHWM_TSHWMRESET_FILECODE                (0xB325)
#define FCH_TAISHAN_FCHTAISHANCORE_TAISHAN_TSIMC_TSFCHECENV_FILECODE                (0xB326)
#define FCH_TAISHAN_FCHTAISHANCORE_TAISHAN_TSIMC_TSFCHECLATE_FILECODE               (0xB327)
#define FCH_TAISHAN_FCHTAISHANCORE_TAISHAN_TSIMC_TSFCHECMID_FILECODE                (0xB328)
#define FCH_TAISHAN_FCHTAISHANCORE_TAISHAN_TSIMC_TSFCHECRESET_FILECODE              (0xB329)
#define FCH_TAISHAN_FCHTAISHANCORE_TAISHAN_TSIMC_TSIMCENV_FILECODE                  (0xB32A)
#define FCH_TAISHAN_FCHTAISHANCORE_TAISHAN_TSIMC_TSIMCLATE_FILECODE                 (0xB32B)
#define FCH_TAISHAN_FCHTAISHANCORE_TAISHAN_TSIMC_TSIMCLIB_FILECODE                  (0xB32C)
#define FCH_TAISHAN_FCHTAISHANCORE_TAISHAN_TSIMC_TSIMCMID_FILECODE                  (0xB32D)
#define FCH_TAISHAN_FCHTAISHANCORE_TAISHAN_TSIMC_TSIMCRESET_FILECODE                (0xB32E)
#define FCH_TAISHAN_FCHTAISHANCORE_TAISHAN_TSIMC_TSIMCSERVICE_FILECODE              (0xB32F)
#define FCH_TAISHAN_FCHTAISHANCORE_TAISHAN_TSINTERFACE_TSFCHINITENV_FILECODE        (0xB330)
#define FCH_TAISHAN_FCHTAISHANCORE_TAISHAN_TSINTERFACE_TSFCHINITLATE_FILECODE       (0xB331)
#define FCH_TAISHAN_FCHTAISHANCORE_TAISHAN_TSINTERFACE_TSFCHINITMID_FILECODE        (0xB332)
#define FCH_TAISHAN_FCHTAISHANCORE_TAISHAN_TSINTERFACE_TSFCHINITRESET_FILECODE      (0xB333)
#define FCH_TAISHAN_FCHTAISHANCORE_TAISHAN_TSINTERFACE_TSFCHINITS3_FILECODE         (0xB334)
#define FCH_TAISHAN_FCHTAISHANCORE_TAISHAN_TSINTERFACE_TSFCHTASKLAUNCHER_FILECODE   (0xB335)
#define FCH_TAISHAN_FCHTAISHANCORE_TAISHAN_TSIR_TSIRENV_FILECODE                    (0xB336)
#define FCH_TAISHAN_FCHTAISHANCORE_TAISHAN_TSIR_TSIRLATE_FILECODE                   (0xB337)
#define FCH_TAISHAN_FCHTAISHANCORE_TAISHAN_TSIR_TSIRMID_FILECODE                    (0xB338)
#define FCH_TAISHAN_FCHTAISHANCORE_TAISHAN_TSPCIE_TSABENVSERVICE_FILECODE           (0xB339)
#define FCH_TAISHAN_FCHTAISHANCORE_TAISHAN_TSPCIE_TSABENV_FILECODE                  (0xB33A)
#define FCH_TAISHAN_FCHTAISHANCORE_TAISHAN_TSPCIE_TSABLATE_FILECODE                 (0xB33B)
#define FCH_TAISHAN_FCHTAISHANCORE_TAISHAN_TSPCIE_TSABMID_FILECODE                  (0xB33C)
#define FCH_TAISHAN_FCHTAISHANCORE_TAISHAN_TSPCIE_TSABRESETSERVICE_FILECODE         (0xB33D)
#define FCH_TAISHAN_FCHTAISHANCORE_TAISHAN_TSPCIE_TSABRESET_FILECODE                (0xB33E)
#define FCH_TAISHAN_FCHTAISHANCORE_TAISHAN_TSPCIE_TSABSERVICE_FILECODE              (0xB33F)
#define FCH_TAISHAN_FCHTAISHANCORE_TAISHAN_TSSATA_TSAHCIENV_FILECODE                (0xB340)
#define FCH_TAISHAN_FCHTAISHANCORE_TAISHAN_TSSATA_TSAHCILATE_FILECODE               (0xB341)
#define FCH_TAISHAN_FCHTAISHANCORE_TAISHAN_TSSATA_TSAHCILIB_FILECODE                (0xB342)
#define FCH_TAISHAN_FCHTAISHANCORE_TAISHAN_TSSATA_TSAHCIMID_FILECODE                (0xB343)
#define FCH_TAISHAN_FCHTAISHANCORE_TAISHAN_TSSATA_TSIDE2AHCIENV_FILECODE            (0xB344)
#define FCH_TAISHAN_FCHTAISHANCORE_TAISHAN_TSSATA_TSIDE2AHCILATE_FILECODE           (0xB345)
#define FCH_TAISHAN_FCHTAISHANCORE_TAISHAN_TSSATA_TSIDE2AHCILIB_FILECODE            (0xB346)
#define FCH_TAISHAN_FCHTAISHANCORE_TAISHAN_TSSATA_TSIDE2AHCIMID_FILECODE            (0xB347)
#define FCH_TAISHAN_FCHTAISHANCORE_TAISHAN_TSSATA_TSRAIDENV_FILECODE                (0xB348)
#define FCH_TAISHAN_FCHTAISHANCORE_TAISHAN_TSSATA_TSRAIDLATE_FILECODE               (0xB349)
#define FCH_TAISHAN_FCHTAISHANCORE_TAISHAN_TSSATA_TSRAIDLIB_FILECODE                (0xB34A)
#define FCH_TAISHAN_FCHTAISHANCORE_TAISHAN_TSSATA_TSRAIDMID_FILECODE                (0xB34B)
#define FCH_TAISHAN_FCHTAISHANCORE_TAISHAN_TSSATA_TSSATAENVLIB_FILECODE             (0xB34C)
#define FCH_TAISHAN_FCHTAISHANCORE_TAISHAN_TSSATA_TSSATAENVSERVICE_FILECODE         (0xB34D)
#define FCH_TAISHAN_FCHTAISHANCORE_TAISHAN_TSSATA_TSSATAENV_FILECODE                (0xB34E)
#define FCH_TAISHAN_FCHTAISHANCORE_TAISHAN_TSSATA_TSSATAIDELIB_FILECODE             (0xB350)
#define FCH_TAISHAN_FCHTAISHANCORE_TAISHAN_TSSATA_TSSATAIDEENV_FILECODE             (0xB351)
#define FCH_TAISHAN_FCHTAISHANCORE_TAISHAN_TSSATA_TSSATAIDELATE_FILECODE            (0xB352)
#define FCH_TAISHAN_FCHTAISHANCORE_TAISHAN_TSSATA_TSSATAIDEMID_FILECODE             (0xB353)
#define FCH_TAISHAN_FCHTAISHANCORE_TAISHAN_TSSATA_TSSATALATE_FILECODE               (0xB354)
#define FCH_TAISHAN_FCHTAISHANCORE_TAISHAN_TSSATA_TSSATALIB_FILECODE                (0xB355)
#define FCH_TAISHAN_FCHTAISHANCORE_TAISHAN_TSSATA_TSSATAMID_FILECODE                (0xB356)
#define FCH_TAISHAN_FCHTAISHANCORE_TAISHAN_TSSATA_TSSATARESETSERVICE_FILECODE       (0xB357)
#define FCH_TAISHAN_FCHTAISHANCORE_TAISHAN_TSSATA_TSSATARESET_FILECODE              (0xB358)
#define FCH_TAISHAN_FCHTAISHANCORE_TAISHAN_TSSATA_TSSATASERVICE_FILECODE            (0xB359)
#define FCH_TAISHAN_FCHTAISHANCORE_TAISHAN_TSSD_TSSDENVSERVICE_FILECODE             (0xB360)
#define FCH_TAISHAN_FCHTAISHANCORE_TAISHAN_TSSD_TSSDENV_FILECODE                    (0xB361)
#define FCH_TAISHAN_FCHTAISHANCORE_TAISHAN_TSSD_TSSDLATE_FILECODE                   (0xB362)
#define FCH_TAISHAN_FCHTAISHANCORE_TAISHAN_TSSD_TSSDMID_FILECODE                    (0xB363)
#define FCH_TAISHAN_FCHTAISHANCORE_TAISHAN_TSSPI_TSLPCENVSERVICE_FILECODE           (0xB364)
#define FCH_TAISHAN_FCHTAISHANCORE_TAISHAN_TSSPI_TSLPCENV_FILECODE                  (0xB365)
#define FCH_TAISHAN_FCHTAISHANCORE_TAISHAN_TSSPI_TSLPCLATE_FILECODE                 (0xB366)
#define FCH_TAISHAN_FCHTAISHANCORE_TAISHAN_TSSPI_TSLPCMID_FILECODE                  (0xB367)
#define FCH_TAISHAN_FCHTAISHANCORE_TAISHAN_TSSPI_TSLPCRESETSERVICE_FILECODE         (0xB368)
#define FCH_TAISHAN_FCHTAISHANCORE_TAISHAN_TSSPI_TSLPCRESET_FILECODE                (0xB369)
#define FCH_TAISHAN_FCHTAISHANCORE_TAISHAN_TSSPI_TSSPIENV_FILECODE                  (0xB36A)
#define FCH_TAISHAN_FCHTAISHANCORE_TAISHAN_TSSPI_TSSPILATE_FILECODE                 (0xB36B)
#define FCH_TAISHAN_FCHTAISHANCORE_TAISHAN_TSSPI_TSSPIMID_FILECODE                  (0xB36C)
#define FCH_TAISHAN_FCHTAISHANCORE_TAISHAN_TSUSB_TSUSBENV_FILECODE                  (0xB375)
#define FCH_TAISHAN_FCHTAISHANCORE_TAISHAN_TSUSB_TSUSBLATE_FILECODE                 (0xB376)
#define FCH_TAISHAN_FCHTAISHANCORE_TAISHAN_TSUSB_TSUSBMID_FILECODE                  (0xB377)
#define FCH_TAISHAN_FCHTAISHANCORE_TAISHAN_TSUSB_TSUSBRESET_FILECODE                (0xB378)
#define FCH_TAISHAN_FCHTAISHANCORE_TAISHAN_TSUSB_TSXHCIENVSERVICE_FILECODE          (0xB379)
#define FCH_TAISHAN_FCHTAISHANCORE_TAISHAN_TSUSB_TSXHCIENV_FILECODE                 (0xB37A)
#define FCH_TAISHAN_FCHTAISHANCORE_TAISHAN_TSUSB_TSXHCILATESERVICE_FILECODE         (0xB37B)
#define FCH_TAISHAN_FCHTAISHANCORE_TAISHAN_TSUSB_TSXHCILATE_FILECODE                (0xB37C)
#define FCH_TAISHAN_FCHTAISHANCORE_TAISHAN_TSUSB_TSXHCIMID_FILECODE                 (0xB37D)
#define FCH_TAISHAN_FCHTAISHANCORE_TAISHAN_TSUSB_TSXHCIRESETSERVICE_FILECODE        (0xB37E)
#define FCH_TAISHAN_FCHTAISHANCORE_TAISHAN_TSUSB_TSXHCIRESET_FILECODE               (0xB37F)
#define FCH_TAISHAN_FCHTAISHANCORE_TAISHAN_TSUSB_TSXHCISERVICE_FILECODE             (0xB380)
#define FCH_TAISHAN_FCHTAISHANCORE_TAISHAN_TSESPI_TSESPIRESET_FILECODE              (0xB390)
#define FCH_TAISHAN_FCHTAISHANCORE_TAISHAN_TSESPI_TSESPIENV_FILECODE                (0xB391)
#define FCH_TAISHAN_FCHTAISHANCORE_TAISHAN_TSESPI_TSESPIMID_FILECODE                (0xB392)
#define FCH_TAISHAN_FCHTAISHANCORE_TAISHAN_TSESPI_TSESPILATE_FILECODE               (0xB393)
#define FCH_TAISHAN_FCHTAISHANCORE_TAISHAN_TSESPI_TSESPILIB_FILECODE                (0xB394)
#define FCH_TAISHAN_FCHTAISHANCORE_TAISHAN_TSXGBE_TSXGBEENV_FILECODE                (0xB3A0)
#define FCH_TAISHAN_FCHTAISHANCORE_TAISHAN_TSXGBE_TSXGBELATE_FILECODE               (0xB3A1)
#define FCH_TAISHAN_FCHTAISHANCORE_TAISHAN_TSXGBE_TSXGBEMID_FILECODE                (0xB3A2)
#define FCH_TAISHAN_FCHTAISHANCORE_TAISHAN_TSXGBE_TSXGBERESET_FILECODE              (0xB3A3)
#define FCH_TAISHAN_FCHTAISHANCORE_TAISHAN_TSXGBE_TSXGBELIB_FILECODE                (0xB3A4)

#define FCH_TAISHAN_FCHTAISHANCF9RESETDXE_CF9RESET_FILECODE                         (0xB400)
#define FCH_TAISHAN_FCHTAISHANMULTIFCHDXE_FCHMULTIFCHDXE_FILECODE                   (0xB401)
#define FCH_TAISHAN_FCHTAISHANMULTIFCHPEI_FCHMULTIFCHPEI_FILECODE                   (0xB402)
#define FCH_TAISHAN_FCHTAISHANDXE_FCHDXE_FILECODE                                   (0xB403)
#define FCH_TAISHAN_FCHTAISHANLEGACYINTERRUPTDXE_LEGACYINTERRUPT_FILECODE           (0xB405)
#define FCH_TAISHAN_FCHTAISHANPEI_FCHPEI_FILECODE                                   (0xB406)
#define FCH_TAISHAN_FCHTAISHANPEI_FCHRESET_FILECODE                                 (0xB407)
#define FCH_TAISHAN_FCHTAISHANPEI_FCHSTALL_FILECODE                                 (0xB408)
#define FCH_TAISHAN_FCHTAISHANSMBUSDXE_SMBUSLIGHT_FILECODE                          (0xB409)
#define FCH_TAISHAN_FCHTAISHANSMBUSPEI_SMBUS_FILECODE                               (0xB40A)
#define FCH_TAISHAN_FCHTAISHANSMMCONTROLDXE_SMMCONTROL_FILECODE                     (0xB40B)
#define FCH_TAISHAN_FCHTAISHANSMMDISPATCHER_FCHSMMDIAGDISPATCHER_FILECODE                                  (0xB410)
#define FCH_TAISHAN_FCHTAISHANSMMDISPATCHER_FCHSMMDISPATCHER_FILECODE                                      (0xB411)
#define FCH_TAISHAN_FCHTAISHANSMMDISPATCHER_FCHSMMGPIDISPATCHER_FCHSMMGPIDISPATCHER_FILECODE               (0xB412)
#define FCH_TAISHAN_FCHTAISHANSMMDISPATCHER_FCHSMMIOTRAPDISPATCHER_FCHSMMIOTRAPDISPATCHER_FILECODE         (0xB413)
#define FCH_TAISHAN_FCHTAISHANSMMDISPATCHER_FCHSMMMISCDISPATCHER_FCHSMMMISCDISPATCHER_FILECODE             (0xB414)
#define FCH_TAISHAN_FCHTAISHANSMMDISPATCHER_FCHSMMPERIODICALDISPATCHER_FCHSMMPERIODICALDISPATCHER_FILECODE (0xB415)
#define FCH_TAISHAN_FCHTAISHANSMMDISPATCHER_FCHSMMPWRBTNDISPATCHER_FCHSMMPWRBTNDISPATCHER_FILECODE         (0xB416)
#define FCH_TAISHAN_FCHTAISHANSMMDISPATCHER_FCHSMMSWDISPATCHER_FCHSMMSWDISPATCHER_FILECODE                 (0xB417)
#define FCH_TAISHAN_FCHTAISHANSMMDISPATCHER_FCHSMMSXDISPATCHER_FCHSMMSXDISPATCHER_FILECODE                 (0xB418)
#define FCH_TAISHAN_FCHTAISHANSMMDISPATCHER_FCHSMMUSBDISPATCHER_FCHSMMUSBDISPATCHER_FILECODE               (0xB419)
#define FCH_TAISHAN_FCHTAISHANSMM_FCHSMM_FILECODE                         (0xB420)
#define FCH_TAISHAN_FCHTAISHANSMM_GPISMI_FILECODE                         (0xB421)
#define FCH_TAISHAN_FCHTAISHANSMM_IOTRAPSMI_FILECODE                      (0xB422)
#define FCH_TAISHAN_FCHTAISHANSMM_MISCSMI_FILECODE                        (0xB423)
#define FCH_TAISHAN_FCHTAISHANSMM_PERIODICTIMERSMI_FILECODE               (0xB424)
#define FCH_TAISHAN_FCHTAISHANSMM_POWERBUTTONSMI_FILECODE                 (0xB425)
#define FCH_TAISHAN_FCHTAISHANSMM_SWSMI_FILECODE                          (0xB426)
#define FCH_TAISHAN_FCHTAISHANSMM_SXSMI_FILECODE                          (0xB427)
#define FCH_TAISHAN_FCHTAISHANCF9RESETSMM_CF9RESETSMM_FILECODE            (0xB428)
#define FCH_TAISHAN_FCHTAISHANDXE_FCHCONTROLSERVICE_FILECODE              (0xB429)
#define FCH_TAISHAN_FCHTAISHANDXE_FCHTAISHANSSDT_FILECODE                 (0xB42A)
#define FCH_TAISHAN_FCHTAISHANMULTIFCHSMM_FCHMULTIFCHSMM_FILECODE         (0xB42B)
#define FCH_TAISHAN_FCHTAISHANPEI_FCHRESET2_FILECODE                      (0xB42C)
#define FCH_TAISHAN_FCHTAISHANSMMDISPATCHER_FCHSMMAPURASDISPATCHER_FCHSMMAPURASDISPATCHER_FILECODE (0xB42D)





#define FCH_SANDSTONE_FCHSANDSTONECORE_COMMON_ACPILIB_FILECODE                            (0xB500)
#define FCH_SANDSTONE_FCHSANDSTONECORE_COMMON_FCHCOMMONSMM_FILECODE                       (0xB501)
#define FCH_SANDSTONE_FCHSANDSTONECORE_COMMON_FCHCOMMON_FILECODE                          (0xB502)
#define FCH_SANDSTONE_FCHSANDSTONECORE_COMMON_FCHLIB_FILECODE                             (0xB503)
#define FCH_SANDSTONE_FCHSANDSTONECORE_COMMON_FCHPELIB_FILECODE                           (0xB504)
#define FCH_SANDSTONE_FCHSANDSTONECORE_COMMON_MEMLIB_FILECODE                             (0xB505)
#define FCH_SANDSTONE_FCHSANDSTONECORE_COMMON_PCILIB_FILECODE                             (0xB506)
#define FCH_SANDSTONE_FCHSANDSTONECORE_COMMON_FCHAOACLIB_FILECODE                         (0xB507)
#define FCH_SANDSTONE_FCHSANDSTONECORE_COMMON_FCHSMNLIB_FILECODE                          (0xB508)
#define FCH_SANDSTONE_FCHSANDSTONECORE_SANDSTONE_SSEMMC_SSEMMCENV_FILECODE                (0xB510)
#define FCH_SANDSTONE_FCHSANDSTONECORE_SANDSTONE_SSEMMC_SSEMMCLATE_FILECODE               (0xB511)
#define FCH_SANDSTONE_FCHSANDSTONECORE_SANDSTONE_SSEMMC_SSEMMCMID_FILECODE                (0xB512)
#define FCH_SANDSTONE_FCHSANDSTONECORE_SANDSTONE_SSEMMC_SSEMMCRESET_FILECODE              (0xB513)
#define FCH_SANDSTONE_FCHSANDSTONECORE_SANDSTONE_SSHWACPI_SSHWACPIENVSERVICE_FILECODE     (0xB518)
#define FCH_SANDSTONE_FCHSANDSTONECORE_SANDSTONE_SSHWACPI_SSHWACPIENV_FILECODE            (0xB519)
#define FCH_SANDSTONE_FCHSANDSTONECORE_SANDSTONE_SSHWACPI_SSHWACPILATESERVICE_FILECODE    (0xB51A)
#define FCH_SANDSTONE_FCHSANDSTONECORE_SANDSTONE_SSHWACPI_SSHWACPILATE_FILECODE           (0xB51B)
#define FCH_SANDSTONE_FCHSANDSTONECORE_SANDSTONE_SSHWACPI_SSHWACPIMID_FILECODE            (0xB51C)
#define FCH_SANDSTONE_FCHSANDSTONECORE_SANDSTONE_SSHWACPI_SSHWACPIRESET_FILECODE          (0xB51D)
#define FCH_SANDSTONE_FCHSANDSTONECORE_SANDSTONE_SSHWACPI_SSSSSERVICE_FILECODE            (0xB51E)
#define FCH_SANDSTONE_FCHSANDSTONECORE_SANDSTONE_SSINTERFACE_SSFCHINITENV_FILECODE        (0xB530)
#define FCH_SANDSTONE_FCHSANDSTONECORE_SANDSTONE_SSINTERFACE_SSFCHINITLATE_FILECODE       (0xB531)
#define FCH_SANDSTONE_FCHSANDSTONECORE_SANDSTONE_SSINTERFACE_SSFCHINITMID_FILECODE        (0xB532)
#define FCH_SANDSTONE_FCHSANDSTONECORE_SANDSTONE_SSINTERFACE_SSFCHINITRESET_FILECODE      (0xB533)
#define FCH_SANDSTONE_FCHSANDSTONECORE_SANDSTONE_SSINTERFACE_SSFCHINITS3_FILECODE         (0xB534)
#define FCH_SANDSTONE_FCHSANDSTONECORE_SANDSTONE_SSINTERFACE_SSFCHTASKLAUNCHER_FILECODE   (0xB535)
#define FCH_SANDSTONE_FCHSANDSTONECORE_SANDSTONE_SSPCIE_SSABENVSERVICE_FILECODE           (0xB539)
#define FCH_SANDSTONE_FCHSANDSTONECORE_SANDSTONE_SSPCIE_SSABENV_FILECODE                  (0xB53A)
#define FCH_SANDSTONE_FCHSANDSTONECORE_SANDSTONE_SSPCIE_SSABLATE_FILECODE                 (0xB53B)
#define FCH_SANDSTONE_FCHSANDSTONECORE_SANDSTONE_SSPCIE_SSABMID_FILECODE                  (0xB53C)
#define FCH_SANDSTONE_FCHSANDSTONECORE_SANDSTONE_SSPCIE_SSABRESETSERVICE_FILECODE         (0xB53D)
#define FCH_SANDSTONE_FCHSANDSTONECORE_SANDSTONE_SSPCIE_SSABRESET_FILECODE                (0xB53E)
#define FCH_SANDSTONE_FCHSANDSTONECORE_SANDSTONE_SSPCIE_SSABSERVICE_FILECODE              (0xB53F)
#define FCH_SANDSTONE_FCHSANDSTONECORE_SANDSTONE_SSSATA_SSAHCIENV_FILECODE                (0xB540)
#define FCH_SANDSTONE_FCHSANDSTONECORE_SANDSTONE_SSSATA_SSAHCILATE_FILECODE               (0xB541)
#define FCH_SANDSTONE_FCHSANDSTONECORE_SANDSTONE_SSSATA_SSAHCILIB_FILECODE                (0xB542)
#define FCH_SANDSTONE_FCHSANDSTONECORE_SANDSTONE_SSSATA_SSAHCIMID_FILECODE                (0xB543)
#define FCH_SANDSTONE_FCHSANDSTONECORE_SANDSTONE_SSSATA_SSRAIDENV_FILECODE                (0xB548)
#define FCH_SANDSTONE_FCHSANDSTONECORE_SANDSTONE_SSSATA_SSRAIDLATE_FILECODE               (0xB549)
#define FCH_SANDSTONE_FCHSANDSTONECORE_SANDSTONE_SSSATA_SSRAIDLIB_FILECODE                (0xB54A)
#define FCH_SANDSTONE_FCHSANDSTONECORE_SANDSTONE_SSSATA_SSRAIDMID_FILECODE                (0xB54B)
#define FCH_SANDSTONE_FCHSANDSTONECORE_SANDSTONE_SSSATA_SSSATAENVLIB_FILECODE             (0xB54C)
#define FCH_SANDSTONE_FCHSANDSTONECORE_SANDSTONE_SSSATA_SSSATAENVSERVICE_FILECODE         (0xB54D)
#define FCH_SANDSTONE_FCHSANDSTONECORE_SANDSTONE_SSSATA_SSSATAENV_FILECODE                (0xB54E)
#define FCH_SANDSTONE_FCHSANDSTONECORE_SANDSTONE_SSSATA_SSSATALATE_FILECODE               (0xB554)
#define FCH_SANDSTONE_FCHSANDSTONECORE_SANDSTONE_SSSATA_SSSATALIB_FILECODE                (0xB555)
#define FCH_SANDSTONE_FCHSANDSTONECORE_SANDSTONE_SSSATA_SSSATAMID_FILECODE                (0xB556)
#define FCH_SANDSTONE_FCHSANDSTONECORE_SANDSTONE_SSSATA_SSSATARESETSERVICE_FILECODE       (0xB557)
#define FCH_SANDSTONE_FCHSANDSTONECORE_SANDSTONE_SSSATA_SSSATARESET_FILECODE              (0xB558)
#define FCH_SANDSTONE_FCHSANDSTONECORE_SANDSTONE_SSSATA_SSSATASERVICE_FILECODE            (0xB559)
#define FCH_SANDSTONE_FCHSANDSTONECORE_SANDSTONE_SSSD_SSSDENVSERVICE_FILECODE             (0xB560)
#define FCH_SANDSTONE_FCHSANDSTONECORE_SANDSTONE_SSSD_SSSDENV_FILECODE                    (0xB561)
#define FCH_SANDSTONE_FCHSANDSTONECORE_SANDSTONE_SSSD_SSSDLATE_FILECODE                   (0xB562)
#define FCH_SANDSTONE_FCHSANDSTONECORE_SANDSTONE_SSSD_SSSDMID_FILECODE                    (0xB563)
#define FCH_SANDSTONE_FCHSANDSTONECORE_SANDSTONE_SSSPI_SSLPCENVSERVICE_FILECODE           (0xB564)
#define FCH_SANDSTONE_FCHSANDSTONECORE_SANDSTONE_SSSPI_SSLPCENV_FILECODE                  (0xB565)
#define FCH_SANDSTONE_FCHSANDSTONECORE_SANDSTONE_SSSPI_SSLPCLATE_FILECODE                 (0xB566)
#define FCH_SANDSTONE_FCHSANDSTONECORE_SANDSTONE_SSSPI_SSLPCMID_FILECODE                  (0xB567)
#define FCH_SANDSTONE_FCHSANDSTONECORE_SANDSTONE_SSSPI_SSLPCRESETSERVICE_FILECODE         (0xB568)
#define FCH_SANDSTONE_FCHSANDSTONECORE_SANDSTONE_SSSPI_SSLPCRESET_FILECODE                (0xB569)
#define FCH_SANDSTONE_FCHSANDSTONECORE_SANDSTONE_SSSPI_SSSPIENV_FILECODE                  (0xB56A)
#define FCH_SANDSTONE_FCHSANDSTONECORE_SANDSTONE_SSSPI_SSSPILATE_FILECODE                 (0xB56B)
#define FCH_SANDSTONE_FCHSANDSTONECORE_SANDSTONE_SSSPI_SSSPIMID_FILECODE                  (0xB56C)
#define FCH_SANDSTONE_FCHSANDSTONECORE_SANDSTONE_SSUSB_SSUSBENV_FILECODE                  (0xB575)
#define FCH_SANDSTONE_FCHSANDSTONECORE_SANDSTONE_SSUSB_SSUSBLATE_FILECODE                 (0xB576)
#define FCH_SANDSTONE_FCHSANDSTONECORE_SANDSTONE_SSUSB_SSUSBMID_FILECODE                  (0xB577)
#define FCH_SANDSTONE_FCHSANDSTONECORE_SANDSTONE_SSUSB_SSUSBRESET_FILECODE                (0xB578)
#define FCH_SANDSTONE_FCHSANDSTONECORE_SANDSTONE_SSUSB_SSXHCIENV_FILECODE                 (0xB57A)
#define FCH_SANDSTONE_FCHSANDSTONECORE_SANDSTONE_SSUSB_SSXHCILATE_FILECODE                (0xB57C)
#define FCH_SANDSTONE_FCHSANDSTONECORE_SANDSTONE_SSUSB_SSXHCIMID_FILECODE                 (0xB57D)
#define FCH_SANDSTONE_FCHSANDSTONECORE_SANDSTONE_SSUSB_SSXHCIRESETSERVICE_FILECODE        (0xB57E)
#define FCH_SANDSTONE_FCHSANDSTONECORE_SANDSTONE_SSUSB_SSXHCIRESET_FILECODE               (0xB57F)
#define FCH_SANDSTONE_FCHSANDSTONECORE_SANDSTONE_SSUSB_SSXHCISERVICE_FILECODE             (0xB580)
#define FCH_SANDSTONE_FCHSANDSTONECORE_SANDSTONE_SSESPI_SSESPIRESET_FILECODE              (0xB590)
#define FCH_SANDSTONE_FCHSANDSTONECORE_SANDSTONE_SSESPI_SSESPIENV_FILECODE                (0xB591)
#define FCH_SANDSTONE_FCHSANDSTONECORE_SANDSTONE_SSESPI_SSESPIMID_FILECODE                (0xB592)
#define FCH_SANDSTONE_FCHSANDSTONECORE_SANDSTONE_SSESPI_SSESPILATE_FILECODE               (0xB593)
#define FCH_SANDSTONE_FCHSANDSTONECORE_SANDSTONE_SSESPI_SSESPILIB_FILECODE                (0xB594)
#define FCH_SANDSTONE_FCHSANDSTONECORE_SANDSTONE_SSXGBE_SSXGBEENV_FILECODE                (0xB5A0)
#define FCH_SANDSTONE_FCHSANDSTONECORE_SANDSTONE_SSXGBE_SSXGBELATE_FILECODE               (0xB5A1)
#define FCH_SANDSTONE_FCHSANDSTONECORE_SANDSTONE_SSXGBE_SSXGBEMID_FILECODE                (0xB5A2)
#define FCH_SANDSTONE_FCHSANDSTONECORE_SANDSTONE_SSXGBE_SSXGBERESET_FILECODE              (0xB5A3)
#define FCH_SANDSTONE_FCHSANDSTONECORE_SANDSTONE_SSXGBE_SSXGBELIB_FILECODE                (0xB5A4)

#define FCH_SANDSTONE_FCHSANDSTONECORECF9RESETDXE_CF9RESET_FILECODE                         (0xB600)
#define FCH_SANDSTONE_FCHSANDSTONECOREMULTIFCHDXE_FCHMULTIFCHDXE_FILECODE                   (0xB601)
#define FCH_SANDSTONE_FCHSANDSTONECOREMULTIFCHPEI_FCHMULTIFCHPEI_FILECODE                   (0xB602)
#define FCH_SANDSTONE_FCHSANDSTONECOREDXE_FCHDXE_FILECODE                                   (0xB603)
#define FCH_SANDSTONE_FCHSANDSTONECORELEGACYINTERRUPTDXE_LEGACYINTERRUPT_FILECODE           (0xB605)
#define FCH_SANDSTONE_FCHSANDSTONECOREPEI_FCHPEI_FILECODE                                   (0xB606)
#define FCH_SANDSTONE_FCHSANDSTONECOREPEI_FCHRESET_FILECODE                                 (0xB607)
#define FCH_SANDSTONE_FCHSANDSTONECOREPEI_FCHSTALL_FILECODE                                 (0xB608)
#define FCH_SANDSTONE_FCHSANDSTONECORESMBUSDXE_SMBUSLIGHT_FILECODE                          (0xB609)
#define FCH_SANDSTONE_FCHSANDSTONECORESMBUSPEI_SMBUS_FILECODE                               (0xB60A)
#define FCH_SANDSTONE_FCHSANDSTONECORESMMCONTROLDXE_SMMCONTROL_FILECODE                     (0xB60B)
#define FCH_SANDSTONE_FCHSANDSTONECORESMMDISPATCHER_FCHSMMDIAGDISPATCHER_FILECODE                                  (0xB610)
#define FCH_SANDSTONE_FCHSANDSTONECORESMMDISPATCHER_FCHSMMDISPATCHER_FILECODE                                      (0xB611)
#define FCH_SANDSTONE_FCHSANDSTONECORESMMDISPATCHER_FCHSMMGPIDISPATCHER_FCHSMMGPIDISPATCHER_FILECODE               (0xB612)
#define FCH_SANDSTONE_FCHSANDSTONECORESMMDISPATCHER_FCHSMMIOTRAPDISPATCHER_FCHSMMIOTRAPDISPATCHER_FILECODE         (0xB613)
#define FCH_SANDSTONE_FCHSANDSTONECORESMMDISPATCHER_FCHSMMMISCDISPATCHER_FCHSMMMISCDISPATCHER_FILECODE             (0xB614)
#define FCH_SANDSTONE_FCHSANDSTONECORESMMDISPATCHER_FCHSMMPERIODICALDISPATCHER_FCHSMMPERIODICALDISPATCHER_FILECODE (0xB615)
#define FCH_SANDSTONE_FCHSANDSTONECORESMMDISPATCHER_FCHSMMPWRBTNDISPATCHER_FCHSMMPWRBTNDISPATCHER_FILECODE         (0xB616)
#define FCH_SANDSTONE_FCHSANDSTONECORESMMDISPATCHER_FCHSMMSWDISPATCHER_FCHSMMSWDISPATCHER_FILECODE                 (0xB617)
#define FCH_SANDSTONE_FCHSANDSTONECORESMMDISPATCHER_FCHSMMSXDISPATCHER_FCHSMMSXDISPATCHER_FILECODE                 (0xB618)
#define FCH_SANDSTONE_FCHSANDSTONECORESMMDISPATCHER_FCHSMMUSBDISPATCHER_FCHSMMUSBDISPATCHER_FILECODE               (0xB619)
#define FCH_SANDSTONE_FCHSANDSTONECORESMM_FCHSMM_FILECODE                         (0xB620)
#define FCH_SANDSTONE_FCHSANDSTONECORESMM_GPISMI_FILECODE                         (0xB621)
#define FCH_SANDSTONE_FCHSANDSTONECORESMM_IOTRAPSMI_FILECODE                      (0xB622)
#define FCH_SANDSTONE_FCHSANDSTONECORESMM_MISCSMI_FILECODE                        (0xB623)
#define FCH_SANDSTONE_FCHSANDSTONECORESMM_PERIODICTIMERSMI_FILECODE               (0xB624)
#define FCH_SANDSTONE_FCHSANDSTONECORESMM_POWERBUTTONSMI_FILECODE                 (0xB625)
#define FCH_SANDSTONE_FCHSANDSTONECORESMM_SWSMI_FILECODE                          (0xB626)
#define FCH_SANDSTONE_FCHSANDSTONECORESMM_SXSMI_FILECODE                          (0xB627)

#define FCH_SANDSTONE_FCHSANDSTONECF9RESETDXE_CF9RESET_FILECODE                   (0xB628)
#define FCH_SANDSTONE_FCHSANDSTONECF9RESETSMM_CF9RESETSMM_FILECODE                (0xB629)
#define FCH_SANDSTONE_FCHSANDSTONECOMPLEMENT_FCHSSSATAD3COLD_FCHSSSATAD3COLDSMM_FILECODE    (0xB62A)
#define FCH_SANDSTONE_FCHSANDSTONECORE_SANDSTONE_SSSATA_SSSATAD3LIB_FILECODE      (0xB62B)
#define FCH_SANDSTONE_FCHSANDSTONEDXE_FCHDXE_FILECODE                             (0xB62C)
#define FCH_SANDSTONE_FCHSANDSTONEI2CDXE_MP2I2CLIBDXE_FILECODE                    (0xB62D)
#define FCH_SANDSTONE_FCHSANDSTONEI2CPEI_MP2I2CLIBPEI_FILECODE                    (0xB62E)
#define FCH_SANDSTONE_FCHSANDSTONELEGACYINTERRUPTDXE_LEGACYINTERRUPT_FILECODE     (0xB62F)
#define FCH_SANDSTONE_FCHSANDSTONEPEI_FCHPEI_FILECODE                   (0xB630)
#define FCH_SANDSTONE_FCHSANDSTONEPEI_FCHRESET_FILECODE                 (0xB631)
#define FCH_SANDSTONE_FCHSANDSTONEPEI_FCHRESET2_FILECODE                (0xB632)
#define FCH_SANDSTONE_FCHSANDSTONEPEI_FCHSTALL_FILECODE                 (0xB633)
#define FCH_SANDSTONE_FCHSANDSTONESMBUSDXE_SMBUSLIGHT_FILECODE          (0xB634)
#define FCH_SANDSTONE_FCHSANDSTONESMBUSPEI_SMBUS_FILECODE               (0xB635)
#define FCH_SANDSTONE_FCHSANDSTONESMM_FCHSMM_FILECODE                   (0xB636)
#define FCH_SANDSTONE_FCHSANDSTONESMM_GPISMI_FILECODE                   (0xB637)
#define FCH_SANDSTONE_FCHSANDSTONESMM_IOTRAPSMI_FILECODE                (0xB638)
#define FCH_SANDSTONE_FCHSANDSTONESMM_MISCSMI_FILECODE                  (0xB639)
#define FCH_SANDSTONE_FCHSANDSTONESMM_PERIODICTIMERSMI_FILECODE         (0xB63A)
#define FCH_SANDSTONE_FCHSANDSTONESMM_POWERBUTTONSMI_FILECODE           (0xB63B)
#define FCH_SANDSTONE_FCHSANDSTONESMM_SWSMI_FILECODE                    (0xB63C)
#define FCH_SANDSTONE_FCHSANDSTONESMM_SXSMI_FILECODE                    (0xB63D)
#define FCH_SANDSTONE_FCHSANDSTONESMMCONTROLDXE_SMMCONTROL_FILECODE                             (0xB63E)
#define FCH_SANDSTONE_FCHSANDSTONESMMDISPATCHER_FCHSMMDIAGDISPATCHER_FILECODE                   (0xB63F)
#define FCH_SANDSTONE_FCHSANDSTONESMMDISPATCHER_FCHSMMDISPATCHER_FILECODE                       (0xB640)
#define FCH_SANDSTONE_FCHSANDSTONESMMDISPATCHER_FCHSMMGPIDISPATCHER_FCHSMMGPIDISPATCHER_FILECODE                  (0xB641)
#define FCH_SANDSTONE_FCHSANDSTONESMMDISPATCHER_FCHSMMIOTRAPDISPATCHER_FCHSMMIOTRAPDISPATCHER_FILECODE            (0xB642)
#define FCH_SANDSTONE_FCHSANDSTONESMMDISPATCHER_FCHSMMMISCDISPATCHER_FCHSMMMISCDISPATCHER_FILECODE                (0xB643)
#define FCH_SANDSTONE_FCHSANDSTONESMMDISPATCHER_FCHSMMPERIODICALDISPATCHER_FCHSMMPERIODICALDISPATCHER_FILECODE    (0xB644)
#define FCH_SANDSTONE_FCHSANDSTONESMMDISPATCHER_FCHSMMPWRBTNDISPATCHER_FCHSMMPWRBTNDISPATCHER_FILECODE            (0xB645)
#define FCH_SANDSTONE_FCHSANDSTONESMMDISPATCHER_FCHSMMSWDISPATCHER_FCHSMMSWDISPATCHER_FILECODE   (0xB646)
#define FCH_SANDSTONE_FCHSANDSTONESMMDISPATCHER_FCHSMMSXDISPATCHER_FCHSMMSXDISPATCHER_FILECODE   (0xB647)
#define FCH_SANDSTONE_FCHSANDSTONESMMDISPATCHER_FCHSMMUSBDISPATCHER_FCHSMMUSBDISPATCHER_FILECODE (0xB648)




#define FCH_HUANGSHAN_FCHHUANGSHANCF9RESETDXE_CF9RESET_FILECODE                                                   (0xB650)
#define FCH_HUANGSHAN_FCHHUANGSHANCORE_COMMON_ACPILIB_FILECODE                                                    (0xB651)
#define FCH_HUANGSHAN_FCHHUANGSHANCORE_COMMON_FCHAOACLIB_FILECODE                                                 (0xB652)
#define FCH_HUANGSHAN_FCHHUANGSHANCORE_COMMON_FCHCOMMON_FILECODE                                                  (0xB653)
#define FCH_HUANGSHAN_FCHHUANGSHANCORE_COMMON_FCHCOMMONSMM_FILECODE                                               (0xB654)
#define FCH_HUANGSHAN_FCHHUANGSHANCORE_COMMON_FCHLIB_FILECODE                                                     (0xB655)
#define FCH_HUANGSHAN_FCHHUANGSHANCORE_COMMON_FCHPELIB_FILECODE                                                   (0xB656)
#define FCH_HUANGSHAN_FCHHUANGSHANCORE_COMMON_FCHSMNLIB_FILECODE                                                  (0xB657)
#define FCH_HUANGSHAN_FCHHUANGSHANCORE_COMMON_MEMLIB_FILECODE                                                     (0xB658)
#define FCH_HUANGSHAN_FCHHUANGSHANCORE_COMMON_PCILIB_FILECODE                                                     (0xB659)
#define FCH_HUANGSHAN_FCHHUANGSHANCORE_HUANGSHAN_HNESPI_HNESPIENV_FILECODE                                           (0xB65A)
#define FCH_HUANGSHAN_FCHHUANGSHANCORE_HUANGSHAN_HNESPI_HNESPILATE_FILECODE                                          (0xB65B)
#define FCH_HUANGSHAN_FCHHUANGSHANCORE_HUANGSHAN_HNESPI_HNESPILIB_FILECODE                                           (0xB65C)
#define FCH_HUANGSHAN_FCHHUANGSHANCORE_HUANGSHAN_HNESPI_HNESPIMID_FILECODE                                           (0xB65D)
#define FCH_HUANGSHAN_FCHHUANGSHANCORE_HUANGSHAN_HNESPI_HNESPIRESET_FILECODE                                         (0xB65E)
#define FCH_HUANGSHAN_FCHHUANGSHANCORE_HUANGSHAN_HNHWACPI_HNHWACPIENV_FILECODE                                       (0xB65F)
#define FCH_HUANGSHAN_FCHHUANGSHANCORE_HUANGSHAN_HNHWACPI_HNHWACPIENVSERVICE_FILECODE                                (0xB660)
#define FCH_HUANGSHAN_FCHHUANGSHANCORE_HUANGSHAN_HNHWACPI_HNHWACPILATE_FILECODE                                      (0xB661)
#define FCH_HUANGSHAN_FCHHUANGSHANCORE_HUANGSHAN_HNHWACPI_HNHWACPILATESERVICE_FILECODE                               (0xB662)
#define FCH_HUANGSHAN_FCHHUANGSHANCORE_HUANGSHAN_HNHWACPI_HNHWACPIMID_FILECODE                                       (0xB663)
#define FCH_HUANGSHAN_FCHHUANGSHANCORE_HUANGSHAN_HNHWACPI_HNHWACPIMIDSERVICE_FILECODE                                (0xB664)
#define FCH_HUANGSHAN_FCHHUANGSHANCORE_HUANGSHAN_HNHWACPI_HNHWACPIRESET_FILECODE                                     (0xB665)
#define FCH_HUANGSHAN_FCHHUANGSHANCORE_HUANGSHAN_HNHWACPI_HNSSSERVICE_FILECODE                                       (0xB666)
#define FCH_HUANGSHAN_FCHHUANGSHANCORE_HUANGSHAN_HNINTERFACE_HNFCHINITENV_FILECODE                                   (0xB667)
#define FCH_HUANGSHAN_FCHHUANGSHANCORE_HUANGSHAN_HNINTERFACE_HNFCHINITLATE_FILECODE                                  (0xB668)
#define FCH_HUANGSHAN_FCHHUANGSHANCORE_HUANGSHAN_HNINTERFACE_HNFCHINITMID_FILECODE                                   (0xB669)
#define FCH_HUANGSHAN_FCHHUANGSHANCORE_HUANGSHAN_HNINTERFACE_HNFCHINITRESET_FILECODE                                 (0xB66A)
#define FCH_HUANGSHAN_FCHHUANGSHANCORE_HUANGSHAN_HNINTERFACE_HNFCHINITS3_FILECODE                                    (0xB66B)
#define FCH_HUANGSHAN_FCHHUANGSHANCORE_HUANGSHAN_HNINTERFACE_HNFCHTASKLAUNCHER_FILECODE                              (0xB66C)
#define FCH_HUANGSHAN_FCHHUANGSHANCORE_HUANGSHAN_HNLPCSPI_HNLPCENV_FILECODE                                          (0xB66D)
#define FCH_HUANGSHAN_FCHHUANGSHANCORE_HUANGSHAN_HNLPCSPI_HNLPCENVSERVICE_FILECODE                                   (0xB66E)
#define FCH_HUANGSHAN_FCHHUANGSHANCORE_HUANGSHAN_HNLPCSPI_HNLPCLATE_FILECODE                                         (0xB66F)
#define FCH_HUANGSHAN_FCHHUANGSHANCORE_HUANGSHAN_HNLPCSPI_HNLPCMID_FILECODE                                          (0xB670)
#define FCH_HUANGSHAN_FCHHUANGSHANCORE_HUANGSHAN_HNLPCSPI_HNLPCRESET_FILECODE                                        (0xB671)
#define FCH_HUANGSHAN_FCHHUANGSHANCORE_HUANGSHAN_HNLPCSPI_HNLPCRESETSERVICE_FILECODE                                 (0xB672)
#define FCH_HUANGSHAN_FCHHUANGSHANCORE_HUANGSHAN_HNLPCSPI_HNSPIENV_FILECODE                                          (0xB673)
#define FCH_HUANGSHAN_FCHHUANGSHANCORE_HUANGSHAN_HNLPCSPI_HNSPILATE_FILECODE                                         (0xB674)
#define FCH_HUANGSHAN_FCHHUANGSHANCORE_HUANGSHAN_HNLPCSPI_HNSPIMID_FILECODE                                          (0xB675)
#define FCH_HUANGSHAN_FCHHUANGSHANCORE_HUANGSHAN_HNLPCSPI_HNSPIRESET_FILECODE                                        (0xB676)
#define FCH_HUANGSHAN_FCHHUANGSHANCORE_HUANGSHAN_HNPCIE_HNABENV_FILECODE                                             (0xB677)
#define FCH_HUANGSHAN_FCHHUANGSHANCORE_HUANGSHAN_HNPCIE_HNABENVSERVICE_FILECODE                                      (0xB678)
#define FCH_HUANGSHAN_FCHHUANGSHANCORE_HUANGSHAN_HNPCIE_HNABLATE_FILECODE                                            (0xB679)
#define FCH_HUANGSHAN_FCHHUANGSHANCORE_HUANGSHAN_HNPCIE_HNABMID_FILECODE                                             (0xB67A)
#define FCH_HUANGSHAN_FCHHUANGSHANCORE_HUANGSHAN_HNPCIE_HNABRESET_FILECODE                                           (0xB67B)
#define FCH_HUANGSHAN_FCHHUANGSHANCORE_HUANGSHAN_HNPCIE_HNABRESETSERVICE_FILECODE                                    (0xB67C)
#define FCH_HUANGSHAN_FCHHUANGSHANCORE_HUANGSHAN_HNPCIE_HNABSERVICE_FILECODE                                         (0xB67D)
#define FCH_HUANGSHAN_FCHHUANGSHANCORE_HUANGSHAN_HNSD_HNSDENV_FILECODE                                               (0xB67E)
#define FCH_HUANGSHAN_FCHHUANGSHANCORE_HUANGSHAN_HNSD_HNSDENVSERVICE_FILECODE                                        (0xB67F)
#define FCH_HUANGSHAN_FCHHUANGSHANCORE_HUANGSHAN_HNSD_HNSDLATE_FILECODE                                              (0xB680)
#define FCH_HUANGSHAN_FCHHUANGSHANCORE_HUANGSHAN_HNSD_HNSDMID_FILECODE                                               (0xB681)
#define FCH_HUANGSHAN_FCHHUANGSHANCORE_HUANGSHAN_HNSD_HNSDRESETSERVICE_FILECODE                                      (0xB682)
#define FCH_HUANGSHAN_FCHHUANGSHANCORE_HUANGSHAN_HNSD_HNSDSERVICE_FILECODE                                           (0xB683)
#define FCH_HUANGSHAN_FCHHUANGSHANCORE_HUANGSHAN_HNUSB_HNCIOENV_FILECODE                                             (0xB684)
#define FCH_HUANGSHAN_FCHHUANGSHANCORE_HUANGSHAN_HNUSB_HNCIOLATE_FILECODE                                            (0xB685)
#define FCH_HUANGSHAN_FCHHUANGSHANCORE_HUANGSHAN_HNUSB_HNCIOMID_FILECODE                                             (0xB686)
#define FCH_HUANGSHAN_FCHHUANGSHANCORE_HUANGSHAN_HNUSB_HNCIORESET_FILECODE                                           (0xB687)
#define FCH_HUANGSHAN_FCHHUANGSHANCORE_HUANGSHAN_HNUSB_HNCIOSERVICE_FILECODE                                         (0xB688)
#define FCH_HUANGSHAN_FCHHUANGSHANCORE_HUANGSHAN_HNUSB_HNUSBENV_FILECODE                                             (0xB689)
#define FCH_HUANGSHAN_FCHHUANGSHANCORE_HUANGSHAN_HNUSB_HNUSBLATE_FILECODE                                            (0xB68A)
#define FCH_HUANGSHAN_FCHHUANGSHANCORE_HUANGSHAN_HNUSB_HNUSBMID_FILECODE                                             (0xB68B)
#define FCH_HUANGSHAN_FCHHUANGSHANCORE_HUANGSHAN_HNUSB_HNUSBRESET_FILECODE                                           (0xB68C)
#define FCH_HUANGSHAN_FCHHUANGSHANCORE_HUANGSHAN_HNUSB_HNXHCIENV_FILECODE                                            (0xB68D)
#define FCH_HUANGSHAN_FCHHUANGSHANCORE_HUANGSHAN_HNUSB_HNXHCILATE_FILECODE                                           (0xB68E)
#define FCH_HUANGSHAN_FCHHUANGSHANCORE_HUANGSHAN_HNUSB_HNXHCIMID_FILECODE                                            (0xB68F)
#define FCH_HUANGSHAN_FCHHUANGSHANCORE_HUANGSHAN_HNUSB_HNXHCIRECOVERY_FILECODE                                       (0xB690)
#define FCH_HUANGSHAN_FCHHUANGSHANCORE_HUANGSHAN_HNUSB_HNXHCIRESET_FILECODE                                          (0xB691)
#define FCH_HUANGSHAN_FCHHUANGSHANCORE_HUANGSHAN_HNUSB_HNXHCIRESETSERVICE_FILECODE                                   (0xB692)
#define FCH_HUANGSHAN_FCHHUANGSHANCORE_HUANGSHAN_HNUSB_HNXHCISERVICE_FILECODE                                        (0xB693)
#define FCH_HUANGSHAN_FCHHUANGSHANCORE_HUANGSHAN_HNWIFI_HNWIFIENV_FILECODE                                           (0xB694)
#define FCH_HUANGSHAN_FCHHUANGSHANCORE_HUANGSHAN_HNWIFI_HNWIFILATE_FILECODE                                          (0xB695)
#define FCH_HUANGSHAN_FCHHUANGSHANCORE_HUANGSHAN_HNWIFI_HNWIFIMID_FILECODE                                           (0xB696)
#define FCH_HUANGSHAN_FCHHUANGSHANCORE_HUANGSHAN_HNWIFI_HNWIFIRESET_FILECODE                                         (0xB697)
#define FCH_HUANGSHAN_FCHHUANGSHANDXE_FCHDXE_FILECODE                                                             (0xB698)
#define FCH_HUANGSHAN_FCHHUANGSHANDXE_FCHHUANGSHANDSDT_FILECODE                                                      (0xB699)
#define FCH_HUANGSHAN_FCHHUANGSHANDXE_FCHHUANGSHANSSDT_FILECODE                                                      (0xB69A)
#define FCH_HUANGSHAN_FCHHUANGSHANLEGACYINTERRUPTDXE_LEGACYINTERRUPT_FILECODE                                     (0xB69B)
#define FCH_HUANGSHAN_FCHHUANGSHANPEI_FCHPEI_FILECODE                                                             (0xB69C)
#define FCH_HUANGSHAN_FCHHUANGSHANPEI_FCHRESET_FILECODE                                                           (0xB69D)
#define FCH_HUANGSHAN_FCHHUANGSHANPEI_FCHRESET2_FILECODE                                                          (0xB69E)
#define FCH_HUANGSHAN_FCHHUANGSHANPEI_FCHSTALL_FILECODE                                                           (0xB69F)
#define FCH_HUANGSHAN_FCHHUANGSHANSMBUSDXE_SMBUSLIGHT_FILECODE                                                    (0xB6A0)
#define FCH_HUANGSHAN_FCHHUANGSHANSMBUSPEI_SMBUS_FILECODE                                                         (0xB6A1)
#define FCH_HUANGSHAN_FCHHUANGSHANSMM_FCHSMM_FILECODE                                                             (0xB6A2)
#define FCH_HUANGSHAN_FCHHUANGSHANSMM_GPISMI_FILECODE                                                             (0xB6A3)
#define FCH_HUANGSHAN_FCHHUANGSHANSMM_IOTRAPSMI_FILECODE                                                          (0xB6A4)
#define FCH_HUANGSHAN_FCHHUANGSHANSMM_MISCSMI_FILECODE                                                            (0xB6A5)
#define FCH_HUANGSHAN_FCHHUANGSHANSMM_PERIODICTIMERSMI_FILECODE                                                   (0xB6A6)
#define FCH_HUANGSHAN_FCHHUANGSHANSMM_POWERBUTTONSMI_FILECODE                                                     (0xB6A7)
#define FCH_HUANGSHAN_FCHHUANGSHANSMM_SWSMI_FILECODE                                                              (0xB6A8)
#define FCH_HUANGSHAN_FCHHUANGSHANSMM_SXSMI_FILECODE                                                              (0xB6A9)
#define FCH_HUANGSHAN_FCHHUANGSHANSMMCONTROLDXE_SMMCONTROL_FILECODE                                               (0xB6AA)
#define FCH_HUANGSHAN_FCHHUANGSHANSMMDISPATCHER_FCHSMMDIAGDISPATCHER_FILECODE                                     (0xB6AB)
#define FCH_HUANGSHAN_FCHHUANGSHANSMMDISPATCHER_FCHSMMDISPATCHER_FILECODE                                         (0xB6AC)
#define FCH_HUANGSHAN_FCHHUANGSHANSMMDISPATCHER_FCHSMMGPIDISPATCHER_FCHSMMGPIDISPATCHER_FILECODE                  (0xB6AD)
#define FCH_HUANGSHAN_FCHHUANGSHANSMMDISPATCHER_FCHSMMIOTRAPDISPATCHER_FCHSMMIOTRAPDISPATCHER_FILECODE            (0xB6AE)
#define FCH_HUANGSHAN_FCHHUANGSHANSMMDISPATCHER_FCHSMMMISCDISPATCHER_FCHSMMMISCDISPATCHER_FILECODE                (0xB6AF)
#define FCH_HUANGSHAN_FCHHUANGSHANSMMDISPATCHER_FCHSMMPERIODICALDISPATCHER_FCHSMMPERIODICALDISPATCHER_FILECODE    (0xB6B0)
#define FCH_HUANGSHAN_FCHHUANGSHANSMMDISPATCHER_FCHSMMPWRBTNDISPATCHER_FCHSMMPWRBTNDISPATCHER_FILECODE            (0xB6B1)
#define FCH_HUANGSHAN_FCHHUANGSHANSMMDISPATCHER_FCHSMMSWDISPATCHER_FCHSMMSWDISPATCHER_FILECODE                    (0xB6B2)
#define FCH_HUANGSHAN_FCHHUANGSHANSMMDISPATCHER_FCHSMMSXDISPATCHER_FCHSMMSXDISPATCHER_FILECODE                    (0xB6B3)
#define FCH_HUANGSHAN_FCHHUANGSHANSMMDISPATCHER_FCHSMMUSBDISPATCHER_FCHSMMUSBDISPATCHER_FILECODE                  (0xB6B4)
#define FCH_HUANGSHAN_FCHHUANGSHANCORE_HUANGSHAN_HNESPI_HNESPIUNITTEST_HNESPIUNITTEST_FILECODE                    (0xB6B5)
#define FCH_HUANGSHAN_FCHHUANGSHANCORE_HUANGSHAN_HNHWACPI_HNHWACPIUNITTEST_HNHWACPIUNITTEST_FILECODE              (0xB6B6)
#define FCH_HUANGSHAN_FCHHUANGSHANCORE_HUANGSHAN_HNINTERFACE_HNINTERFACEUNITTEST_HNINTERFACEUNITTEST_FILECODE     (0xB6B7)
#define FCH_HUANGSHAN_FCHHUANGSHANCORE_HUANGSHAN_HNLPCSPI_HNLPCSPIUNITTEST_HNLPCSPIUNITTEST_FILECODE              (0xB6B8)
#define FCH_HUANGSHAN_FCHHUANGSHANCORE_HUANGSHAN_HNPCIE_HNPCIEUNITTEST_HNPCIEUNITTEST_FILECODE                    (0xB6B9)
#define FCH_HUANGSHAN_FCHHUANGSHANCORE_HUANGSHAN_HNSD_HNSDUNITTEST_HNSDUNITTEST_FILECODE                          (0xB6BA)
#define FCH_HUANGSHAN_FCHHUANGSHANCORE_HUANGSHAN_HNUSB_HNUSBUNITTEST_HNUSBUNITTEST_FILECODE                       (0xB6BB)
#define FCH_HUANGSHAN_FCHHUANGSHANCORE_HUANGSHAN_HNWIFI_HNWIFIUNITTEST_HNWIFIUNITTEST_FILECODE                    (0xB6BC)
#define FCH_HUANGSHAN_FCHHUANGSHANDXE_HNDXEUNITTEST_HNDXEUNITTEST_FILECODE                                        (0xB6BD)

#define LIBRARY_FCHIDSHOOKSTXLIB_PEI_FCHIDSHOOKSTXLIBPEI_FILECODE                                           (0xB6E0)
#define LIBRARY_FCHIDSHOOKSTXLIB_DXE_FCHIDSHOOKSTXLIBDXE_FILECODE                                           (0xB6E1)


#define FCH_HUASHAN_FCHHUASHANCORE_COMMON_ACPILIB_FILECODE                                                 (0xB900)
#define FCH_HUASHAN_FCHHUASHANCORE_COMMON_FCHCOMMONSMM_FILECODE                                            (0xB901)
#define FCH_HUASHAN_FCHHUASHANCORE_COMMON_FCHCOMMON_FILECODE                                               (0xB902)
#define FCH_HUASHAN_FCHHUASHANCORE_COMMON_FCHLIB_FILECODE                                                  (0xB903)
#define FCH_HUASHAN_FCHHUASHANCORE_COMMON_FCHPELIB_FILECODE                                                (0xB904)
#define FCH_HUASHAN_FCHHUASHANCORE_COMMON_MEMLIB_FILECODE                                                  (0xB905)
#define FCH_HUASHAN_FCHHUASHANCORE_COMMON_PCILIB_FILECODE                                                  (0xB906)
#define FCH_HUASHAN_FCHHUASHANCORE_COMMON_FCHAOACLIB_FILECODE                                              (0xB907)
#define FCH_HUASHAN_FCHHUASHANCORE_COMMON_FCHSMNLIB_FILECODE                                               (0xB908)
#define FCH_HUASHAN_FCHHUASHANCORE_HUASHAN_HSEMMC_HSEMMCENV_FILECODE                                       (0xB910)
#define FCH_HUASHAN_FCHHUASHANCORE_HUASHAN_HSEMMC_HSEMMCLATE_FILECODE                                      (0xB911)
#define FCH_HUASHAN_FCHHUASHANCORE_HUASHAN_HSEMMC_HSEMMCMID_FILECODE                                       (0xB912)
#define FCH_HUASHAN_FCHHUASHANCORE_HUASHAN_HSEMMC_HSEMMCRESET_FILECODE                                     (0xB913)
#define FCH_HUASHAN_FCHHUASHANCORE_HUASHAN_HSGBE_HSGBEENV_FILECODE                                         (0xB914)
#define FCH_HUASHAN_FCHHUASHANCORE_HUASHAN_HSGBE_HSGBELATE_FILECODE                                        (0xB915)
#define FCH_HUASHAN_FCHHUASHANCORE_HUASHAN_HSGBE_HSGBEMID_FILECODE                                         (0xB916)
#define FCH_HUASHAN_FCHHUASHANCORE_HUASHAN_HSGBE_HSGBERESET_FILECODE                                       (0xB917)
#define FCH_HUASHAN_FCHHUASHANCORE_HUASHAN_HSHWACPI_HSHWACPIENVSERVICE_FILECODE                            (0xB918)
#define FCH_HUASHAN_FCHHUASHANCORE_HUASHAN_HSHWACPI_HSHWACPIENV_FILECODE                                   (0xB919)
#define FCH_HUASHAN_FCHHUASHANCORE_HUASHAN_HSHWACPI_HSHWACPILATESERVICE_FILECODE                           (0xB91A)
#define FCH_HUASHAN_FCHHUASHANCORE_HUASHAN_HSHWACPI_HSHWACPILATE_FILECODE                                  (0xB91B)
#define FCH_HUASHAN_FCHHUASHANCORE_HUASHAN_HSHWACPI_HSHWACPIMID_FILECODE                                   (0xB91C)
#define FCH_HUASHAN_FCHHUASHANCORE_HUASHAN_HSHWACPI_HSHWACPIRESET_FILECODE                                 (0xB91D)
#define FCH_HUASHAN_FCHHUASHANCORE_HUASHAN_HSHWACPI_HSSSSERVICE_FILECODE                                   (0xB91E)
#define FCH_HUASHAN_FCHHUASHANCORE_HUASHAN_HSHWM_HSHWMENVSERVICE_FILECODE                                  (0xB920)
#define FCH_HUASHAN_FCHHUASHANCORE_HUASHAN_HSHWM_HSHWMENV_FILECODE                                         (0xB921)
#define FCH_HUASHAN_FCHHUASHANCORE_HUASHAN_HSHWM_HSHWMLATESERVICE_FILECODE                                 (0xB922)
#define FCH_HUASHAN_FCHHUASHANCORE_HUASHAN_HSHWM_HSHWMLATE_FILECODE                                        (0xB923)
#define FCH_HUASHAN_FCHHUASHANCORE_HUASHAN_HSHWM_HSHWMMID_FILECODE                                         (0xB924)
#define FCH_HUASHAN_FCHHUASHANCORE_HUASHAN_HSHWM_HSHWMRESET_FILECODE                                       (0xB925)
#define FCH_HUASHAN_FCHHUASHANCORE_HUASHAN_HSIMC_HSFCHECENV_FILECODE                                       (0xB926)
#define FCH_HUASHAN_FCHHUASHANCORE_HUASHAN_HSIMC_HSFCHECLATE_FILECODE                                      (0xB927)
#define FCH_HUASHAN_FCHHUASHANCORE_HUASHAN_HSIMC_HSFCHECMID_FILECODE                                       (0xB928)
#define FCH_HUASHAN_FCHHUASHANCORE_HUASHAN_HSIMC_HSFCHECRESET_FILECODE                                     (0xB929)
#define FCH_HUASHAN_FCHHUASHANCORE_HUASHAN_HSIMC_HSIMCENV_FILECODE                                         (0xB92A)
#define FCH_HUASHAN_FCHHUASHANCORE_HUASHAN_HSIMC_HSIMCLATE_FILECODE                                        (0xB92B)
#define FCH_HUASHAN_FCHHUASHANCORE_HUASHAN_HSIMC_HSIMCLIB_FILECODE                                         (0xB92C)
#define FCH_HUASHAN_FCHHUASHANCORE_HUASHAN_HSIMC_HSIMCMID_FILECODE                                         (0xB92D)
#define FCH_HUASHAN_FCHHUASHANCORE_HUASHAN_HSIMC_HSIMCRESET_FILECODE                                       (0xB92E)
#define FCH_HUASHAN_FCHHUASHANCORE_HUASHAN_HSIMC_HSIMCSERVICE_FILECODE                                     (0xB92F)
#define FCH_HUASHAN_FCHHUASHANCORE_HUASHAN_HSINTERFACE_HSFCHINITENV_FILECODE                               (0xB930)
#define FCH_HUASHAN_FCHHUASHANCORE_HUASHAN_HSINTERFACE_HSFCHINITLATE_FILECODE                              (0xB931)
#define FCH_HUASHAN_FCHHUASHANCORE_HUASHAN_HSINTERFACE_HSFCHINITMID_FILECODE                               (0xB932)
#define FCH_HUASHAN_FCHHUASHANCORE_HUASHAN_HSINTERFACE_HSFCHINITRESET_FILECODE                             (0xB933)
#define FCH_HUASHAN_FCHHUASHANCORE_HUASHAN_HSINTERFACE_HSFCHINITS3_FILECODE                                (0xB934)
#define FCH_HUASHAN_FCHHUASHANCORE_HUASHAN_HSINTERFACE_HSFCHTASKLAUNCHER_FILECODE                          (0xB935)
#define FCH_HUASHAN_FCHHUASHANCORE_HUASHAN_HSIR_HSIRENV_FILECODE                                           (0xB936)
#define FCH_HUASHAN_FCHHUASHANCORE_HUASHAN_HSIR_HSIRLATE_FILECODE                                          (0xB937)
#define FCH_HUASHAN_FCHHUASHANCORE_HUASHAN_HSIR_HSIRMID_FILECODE                                           (0xB938)
#define FCH_HUASHAN_FCHHUASHANCORE_HUASHAN_HSPCIE_HSABENVSERVICE_FILECODE                                  (0xB939)
#define FCH_HUASHAN_FCHHUASHANCORE_HUASHAN_HSPCIE_HSABENV_FILECODE                                         (0xB93A)
#define FCH_HUASHAN_FCHHUASHANCORE_HUASHAN_HSPCIE_HSABLATE_FILECODE                                        (0xB93B)
#define FCH_HUASHAN_FCHHUASHANCORE_HUASHAN_HSPCIE_HSABMID_FILECODE                                         (0xB93C)
#define FCH_HUASHAN_FCHHUASHANCORE_HUASHAN_HSPCIE_HSABRESETSERVICE_FILECODE                                (0xB93D)
#define FCH_HUASHAN_FCHHUASHANCORE_HUASHAN_HSPCIE_HSABRESET_FILECODE                                       (0xB93E)
#define FCH_HUASHAN_FCHHUASHANCORE_HUASHAN_HSPCIE_HSABSERVICE_FILECODE                                     (0xB93F)
#define FCH_HUASHAN_FCHHUASHANCORE_HUASHAN_HSSATA_HSAHCIENV_FILECODE                                       (0xB940)
#define FCH_HUASHAN_FCHHUASHANCORE_HUASHAN_HSSATA_HSAHCILATE_FILECODE                                      (0xB941)
#define FCH_HUASHAN_FCHHUASHANCORE_HUASHAN_HSSATA_HSAHCILIB_FILECODE                                       (0xB942)
#define FCH_HUASHAN_FCHHUASHANCORE_HUASHAN_HSSATA_HSAHCIMID_FILECODE                                       (0xB943)
#define FCH_HUASHAN_FCHHUASHANCORE_HUASHAN_HSSATA_HSIDE2AHCIENV_FILECODE                                   (0xB944)
#define FCH_HUASHAN_FCHHUASHANCORE_HUASHAN_HSSATA_HSIDE2AHCILATE_FILECODE                                  (0xB945)
#define FCH_HUASHAN_FCHHUASHANCORE_HUASHAN_HSSATA_HSIDE2AHCILIB_FILECODE                                   (0xB946)
#define FCH_HUASHAN_FCHHUASHANCORE_HUASHAN_HSSATA_HSIDE2AHCIMID_FILECODE                                   (0xB947)
#define FCH_HUASHAN_FCHHUASHANCORE_HUASHAN_HSSATA_HSRAIDENV_FILECODE                                       (0xB948)
#define FCH_HUASHAN_FCHHUASHANCORE_HUASHAN_HSSATA_HSRAIDLATE_FILECODE                                      (0xB949)
#define FCH_HUASHAN_FCHHUASHANCORE_HUASHAN_HSSATA_HSRAIDLIB_FILECODE                                       (0xB94A)
#define FCH_HUASHAN_FCHHUASHANCORE_HUASHAN_HSSATA_HSRAIDMID_FILECODE                                       (0xB94B)
#define FCH_HUASHAN_FCHHUASHANCORE_HUASHAN_HSSATA_HSSATAENVLIB_FILECODE                                    (0xB94C)
#define FCH_HUASHAN_FCHHUASHANCORE_HUASHAN_HSSATA_HSSATAENVSERVICE_FILECODE                                (0xB94D)
#define FCH_HUASHAN_FCHHUASHANCORE_HUASHAN_HSSATA_HSSATAENV_FILECODE                                       (0xB94E)
#define FCH_HUASHAN_FCHHUASHANCORE_HUASHAN_HSSATA_HSSATAIDELIB_FILECODE                                    (0xB950)
#define FCH_HUASHAN_FCHHUASHANCORE_HUASHAN_HSSATA_HSSATAIDEENV_FILECODE                                    (0xB951)
#define FCH_HUASHAN_FCHHUASHANCORE_HUASHAN_HSSATA_HSSATAIDELATE_FILECODE                                   (0xB952)
#define FCH_HUASHAN_FCHHUASHANCORE_HUASHAN_HSSATA_HSSATAIDEMID_FILECODE                                    (0xB953)
#define FCH_HUASHAN_FCHHUASHANCORE_HUASHAN_HSSATA_HSSATALATE_FILECODE                                      (0xB954)
#define FCH_HUASHAN_FCHHUASHANCORE_HUASHAN_HSSATA_HSSATALIB_FILECODE                                       (0xB955)
#define FCH_HUASHAN_FCHHUASHANCORE_HUASHAN_HSSATA_HSSATAMID_FILECODE                                       (0xB956)
#define FCH_HUASHAN_FCHHUASHANCORE_HUASHAN_HSSATA_HSSATARESETSERVICE_FILECODE                              (0xB957)
#define FCH_HUASHAN_FCHHUASHANCORE_HUASHAN_HSSATA_HSSATARESET_FILECODE                                     (0xB958)
#define FCH_HUASHAN_FCHHUASHANCORE_HUASHAN_HSSATA_HSSATASERVICE_FILECODE                                   (0xB959)
#define FCH_HUASHAN_FCHHUASHANCORE_HUASHAN_HSSD_HSSDENVSERVICE_FILECODE                                    (0xB960)
#define FCH_HUASHAN_FCHHUASHANCORE_HUASHAN_HSSD_HSSDENV_FILECODE                                           (0xB961)
#define FCH_HUASHAN_FCHHUASHANCORE_HUASHAN_HSSD_HSSDLATE_FILECODE                                          (0xB962)
#define FCH_HUASHAN_FCHHUASHANCORE_HUASHAN_HSSD_HSSDMID_FILECODE                                           (0xB963)
#define FCH_HUASHAN_FCHHUASHANCORE_HUASHAN_HSSPI_HSLPCENVSERVICE_FILECODE                                  (0xB964)
#define FCH_HUASHAN_FCHHUASHANCORE_HUASHAN_HSSPI_HSLPCENV_FILECODE                                         (0xB965)
#define FCH_HUASHAN_FCHHUASHANCORE_HUASHAN_HSSPI_HSLPCLATE_FILECODE                                        (0xB966)
#define FCH_HUASHAN_FCHHUASHANCORE_HUASHAN_HSSPI_HSLPCMID_FILECODE                                         (0xB967)
#define FCH_HUASHAN_FCHHUASHANCORE_HUASHAN_HSSPI_HSLPCRESETSERVICE_FILECODE                                (0xB968)
#define FCH_HUASHAN_FCHHUASHANCORE_HUASHAN_HSSPI_HSLPCRESET_FILECODE                                       (0xB969)
#define FCH_HUASHAN_FCHHUASHANCORE_HUASHAN_HSSPI_HSSPIENV_FILECODE                                         (0xB96A)
#define FCH_HUASHAN_FCHHUASHANCORE_HUASHAN_HSSPI_HSSPILATE_FILECODE                                        (0xB96B)
#define FCH_HUASHAN_FCHHUASHANCORE_HUASHAN_HSSPI_HSSPIMID_FILECODE                                         (0xB96C)
#define FCH_HUASHAN_FCHHUASHANCORE_HUASHAN_HSUSB_HSUSBENV_FILECODE                                         (0xB975)
#define FCH_HUASHAN_FCHHUASHANCORE_HUASHAN_HSUSB_HSUSBLATE_FILECODE                                        (0xB976)
#define FCH_HUASHAN_FCHHUASHANCORE_HUASHAN_HSUSB_HSUSBMID_FILECODE                                         (0xB977)
#define FCH_HUASHAN_FCHHUASHANCORE_HUASHAN_HSUSB_HSUSBRESET_FILECODE                                       (0xB978)
#define FCH_HUASHAN_FCHHUASHANCORE_HUASHAN_HSUSB_HSXHCIENVSERVICE_FILECODE                                 (0xB979)
#define FCH_HUASHAN_FCHHUASHANCORE_HUASHAN_HSUSB_HSXHCIENV_FILECODE                                        (0xB97A)
#define FCH_HUASHAN_FCHHUASHANCORE_HUASHAN_HSUSB_HSXHCILATESERVICE_FILECODE                                (0xB97B)
#define FCH_HUASHAN_FCHHUASHANCORE_HUASHAN_HSUSB_HSXHCILATE_FILECODE                                       (0xB97C)
#define FCH_HUASHAN_FCHHUASHANCORE_HUASHAN_HSUSB_HSXHCIMID_FILECODE                                        (0xB97D)
#define FCH_HUASHAN_FCHHUASHANCORE_HUASHAN_HSUSB_HSXHCIRESETSERVICE_FILECODE                               (0xB97E)
#define FCH_HUASHAN_FCHHUASHANCORE_HUASHAN_HSUSB_HSXHCIRESET_FILECODE                                      (0xB97F)
#define FCH_HUASHAN_FCHHUASHANCORE_HUASHAN_HSUSB_HSXHCISERVICE_FILECODE                                    (0xB980)
#define FCH_HUASHAN_FCHHUASHANCORE_HUASHAN_HSESPI_HSESPIRESET_FILECODE                                     (0xB990)
#define FCH_HUASHAN_FCHHUASHANCORE_HUASHAN_HSESPI_HSESPIENV_FILECODE                                       (0xB991)
#define FCH_HUASHAN_FCHHUASHANCORE_HUASHAN_HSESPI_HSESPIMID_FILECODE                                       (0xB992)
#define FCH_HUASHAN_FCHHUASHANCORE_HUASHAN_HSESPI_HSESPILATE_FILECODE                                      (0xB993)
#define FCH_HUASHAN_FCHHUASHANCORE_HUASHAN_HSESPI_HSESPILIB_FILECODE                                       (0xB994)
#define FCH_HUASHAN_FCHHUASHANCORE_HUASHAN_HSXGBE_HSXGBEENV_FILECODE                                       (0xB9A0)
#define FCH_HUASHAN_FCHHUASHANCORE_HUASHAN_HSXGBE_HSXGBELATE_FILECODE                                      (0xB9A1)
#define FCH_HUASHAN_FCHHUASHANCORE_HUASHAN_HSXGBE_HSXGBEMID_FILECODE                                       (0xB9A2)
#define FCH_HUASHAN_FCHHUASHANCORE_HUASHAN_HSXGBE_HSXGBERESET_FILECODE                                     (0xB9A3)
#define FCH_HUASHAN_FCHHUASHANCORE_HUASHAN_HSXGBE_HSXGBELIB_FILECODE                                       (0xB9A4)
#define FCH_HUASHAN_FCHHUASHANCF9RESETDXE_CF9RESET_FILECODE                                                (0xB9A5)
#define FCH_HUASHAN_FCHHUASHANMULTIFCHDXE_FCHMULTIFCHDXE_FILECODE                                          (0xB9A6)
#define FCH_HUASHAN_FCHHUASHANMULTIFCHPEI_FCHMULTIFCHPEI_FILECODE                                          (0xB9A7)
#define FCH_HUASHAN_FCHHUASHANDXE_FCHDXE_FILECODE                                                          (0xB9A8)
#define FCH_HUASHAN_FCHHUASHANLEGACYINTERRUPTDXE_LEGACYINTERRUPT_FILECODE                                  (0xB9A9)
#define FCH_HUASHAN_FCHHUASHANPEI_FCHPEI_FILECODE                                                          (0xB9AA)
#define FCH_HUASHAN_FCHHUASHANPEI_FCHRESET_FILECODE                                                        (0xB9AB)
#define FCH_HUASHAN_FCHHUASHANPEI_FCHSTALL_FILECODE                                                        (0xB9AC)
#define FCH_HUASHAN_FCHHUASHANSMBUSDXE_SMBUSLIGHT_FILECODE                                                 (0xB9AD)
#define FCH_HUASHAN_FCHHUASHANSMBUSPEI_SMBUS_FILECODE                                                      (0xB9AE)
#define FCH_HUASHAN_FCHHUASHANSMMCONTROLDXE_SMMCONTROL_FILECODE                                            (0xB9AF)
#define FCH_HUASHAN_FCHHUASHANSMMDISPATCHER_FCHSMMDIAGDISPATCHER_FILECODE                                  (0xB9B0)
#define FCH_HUASHAN_FCHHUASHANSMMDISPATCHER_FCHSMMDISPATCHER_FILECODE                                      (0xB9B1)
#define FCH_HUASHAN_FCHHUASHANSMMDISPATCHER_FCHSMMGPIDISPATCHER_FCHSMMGPIDISPATCHER_FILECODE               (0xB9B2)
#define FCH_HUASHAN_FCHHUASHANSMMDISPATCHER_FCHSMMIOTRAPDISPATCHER_FCHSMMIOTRAPDISPATCHER_FILECODE         (0xB9B3)
#define FCH_HUASHAN_FCHHUASHANSMMDISPATCHER_FCHSMMMISCDISPATCHER_FCHSMMMISCDISPATCHER_FILECODE             (0xB9B4)
#define FCH_HUASHAN_FCHHUASHANSMMDISPATCHER_FCHSMMPERIODICALDISPATCHER_FCHSMMPERIODICALDISPATCHER_FILECODE (0xB9B5)
#define FCH_HUASHAN_FCHHUASHANSMMDISPATCHER_FCHSMMPWRBTNDISPATCHER_FCHSMMPWRBTNDISPATCHER_FILECODE         (0xB9B6)
#define FCH_HUASHAN_FCHHUASHANSMMDISPATCHER_FCHSMMSWDISPATCHER_FCHSMMSWDISPATCHER_FILECODE                 (0xB9B7)
#define FCH_HUASHAN_FCHHUASHANSMMDISPATCHER_FCHSMMSXDISPATCHER_FCHSMMSXDISPATCHER_FILECODE                 (0xB9B8)
#define FCH_HUASHAN_FCHHUASHANSMMDISPATCHER_FCHSMMUSBDISPATCHER_FCHSMMUSBDISPATCHER_FILECODE               (0xB9B9)
#define FCH_HUASHAN_FCHHUASHANSMMDISPATCHER_FCHSMMAPURASDISPATCHER_FCHSMMAPURASDISPATCHER_FILECODE         (0xB9BA)
#define FCH_HUASHAN_FCHHUASHANSMMDISPATCHER_FCHSMMAPURASDISPATCHER_FCHSSPRASSMILIB_FILECODE                (0xB9BB)
#define FCH_HUASHAN_FCHHUASHANSMM_FCHSMM_FILECODE                                                          (0xB9C0)
#define FCH_HUASHAN_FCHHUASHANSMM_GPISMI_FILECODE                                                          (0xB9C1)
#define FCH_HUASHAN_FCHHUASHANSMM_IOTRAPSMI_FILECODE                                                       (0xB9C2)
#define FCH_HUASHAN_FCHHUASHANSMM_MISCSMI_FILECODE                                                         (0xB9C3)
#define FCH_HUASHAN_FCHHUASHANSMM_PERIODICTIMERSMI_FILECODE                                                (0xB9C4)
#define FCH_HUASHAN_FCHHUASHANSMM_POWERBUTTONSMI_FILECODE                                                  (0xB9C5)
#define FCH_HUASHAN_FCHHUASHANSMM_SWSMI_FILECODE                                                           (0xB9C6)
#define FCH_HUASHAN_FCHHUASHANSMM_SXSMI_FILECODE                                                           (0xB9C7)

#define FCH_SONGSHAN_FCHSONGSHANCORE_COMMON_ACPILIB_FILECODE                                    (0xB440)
#define FCH_SONGSHAN_FCHSONGSHANCORE_COMMON_FCHCOMMONSMM_FILECODE                               (0xB441)
#define FCH_SONGSHAN_FCHSONGSHANCORE_COMMON_FCHCOMMON_FILECODE                                  (0xB442)
#define FCH_SONGSHAN_FCHSONGSHANCORE_COMMON_FCHLIB_FILECODE                                     (0xB443)
#define FCH_SONGSHAN_FCHSONGSHANCORE_COMMON_FCHPELIB_FILECODE                                   (0xB444)
#define FCH_SONGSHAN_FCHSONGSHANCORE_COMMON_MEMLIB_FILECODE                                     (0xB445)
#define FCH_SONGSHAN_FCHSONGSHANCORE_COMMON_PCILIB_FILECODE                                     (0xB446)
#define FCH_SONGSHAN_FCHSONGSHANCORE_COMMON_FCHAOACLIB_FILECODE                                 (0xB447)
#define FCH_SONGSHAN_FCHSONGSHANCORE_COMMON_FCHSMNLIB_FILECODE                                  (0xB448)
#define FCH_SONGSHAN_FCHSONGSHANCORE_SONGSHAN_SNEMMC_SNEMMCENV_FILECODE                         (0xB449)
#define FCH_SONGSHAN_FCHSONGSHANCORE_SONGSHAN_SNEMMC_SNEMMCLATE_FILECODE                        (0xB44A)
#define FCH_SONGSHAN_FCHSONGSHANCORE_SONGSHAN_SNEMMC_SNEMMCMID_FILECODE                         (0xB44B)
#define FCH_SONGSHAN_FCHSONGSHANCORE_SONGSHAN_SNEMMC_SNEMMCRESET_FILECODE                       (0xB44C)
#define FCH_SONGSHAN_FCHSONGSHANCORE_SONGSHAN_SNGBE_SNGBEENV_FILECODE                           (0xB44D)
#define FCH_SONGSHAN_FCHSONGSHANCORE_SONGSHAN_SNGBE_SNGBELATE_FILECODE                          (0xB44E)
#define FCH_SONGSHAN_FCHSONGSHANCORE_SONGSHAN_SNGBE_SNGBEMID_FILECODE                           (0xB44F)
#define FCH_SONGSHAN_FCHSONGSHANCORE_SONGSHAN_SNGBE_SNGBERESET_FILECODE                         (0xB450)
#define FCH_SONGSHAN_FCHSONGSHANCORE_SONGSHAN_SNHWACPI_SNHWACPIENVSERVICE_FILECODE     (0xB451)
#define FCH_SONGSHAN_FCHSONGSHANCORE_SONGSHAN_SNHWACPI_SNHWACPIENV_FILECODE            (0xB452)
#define FCH_SONGSHAN_FCHSONGSHANCORE_SONGSHAN_SNHWACPI_SNHWACPILATESERVICE_FILECODE    (0xB453)
#define FCH_SONGSHAN_FCHSONGSHANCORE_SONGSHAN_SNHWACPI_SNHWACPILATE_FILECODE           (0xB454)
#define FCH_SONGSHAN_FCHSONGSHANCORE_SONGSHAN_SNHWACPI_SNHWACPIMID_FILECODE            (0xB455)
#define FCH_SONGSHAN_FCHSONGSHANCORE_SONGSHAN_SNHWACPI_SNHWACPIRESET_FILECODE          (0xB456)
#define FCH_SONGSHAN_FCHSONGSHANCORE_SONGSHAN_SNHWACPI_SNSSSERVICE_FILECODE            (0xB457)
#define FCH_SONGSHAN_FCHSONGSHANCORE_SONGSHAN_SNHWM_SNHWMENVSERVICE_FILECODE           (0xB458)
#define FCH_SONGSHAN_FCHSONGSHANCORE_SONGSHAN_SNHWM_SNHWMENV_FILECODE                  (0xB459)
#define FCH_SONGSHAN_FCHSONGSHANCORE_SONGSHAN_SNHWM_SNHWMLATESERVICE_FILECODE          (0xB45A)
#define FCH_SONGSHAN_FCHSONGSHANCORE_SONGSHAN_SNHWM_SNHWMLATE_FILECODE                 (0xB45B)
#define FCH_SONGSHAN_FCHSONGSHANCORE_SONGSHAN_SNHWM_SNHWMMID_FILECODE                  (0xB45C)
#define FCH_SONGSHAN_FCHSONGSHANCORE_SONGSHAN_SNHWM_SNHWMRESET_FILECODE                (0xB45D)
#define FCH_SONGSHAN_FCHSONGSHANCORE_SONGSHAN_SNIMC_SNFCHECENV_FILECODE                (0xB45E)
#define FCH_SONGSHAN_FCHSONGSHANCORE_SONGSHAN_SNIMC_SNFCHECLATE_FILECODE               (0xB45F)
#define FCH_SONGSHAN_FCHSONGSHANCORE_SONGSHAN_SNIMC_SNFCHECMID_FILECODE                (0xB460)
#define FCH_SONGSHAN_FCHSONGSHANCORE_SONGSHAN_SNIMC_SNFCHECRESET_FILECODE              (0xB461)
#define FCH_SONGSHAN_FCHSONGSHANCORE_SONGSHAN_SNIMC_SNIMCENV_FILECODE                  (0xB462)
#define FCH_SONGSHAN_FCHSONGSHANCORE_SONGSHAN_SNIMC_SNIMCLATE_FILECODE                 (0xB463)
#define FCH_SONGSHAN_FCHSONGSHANCORE_SONGSHAN_SNIMC_SNIMCLIB_FILECODE                  (0xB464)
#define FCH_SONGSHAN_FCHSONGSHANCORE_SONGSHAN_SNIMC_SNIMCMID_FILECODE                  (0xB465)
#define FCH_SONGSHAN_FCHSONGSHANCORE_SONGSHAN_SNIMC_SNIMCRESET_FILECODE                (0xB466)
#define FCH_SONGSHAN_FCHSONGSHANCORE_SONGSHAN_SNIMC_SNIMCSERVICE_FILECODE              (0xB467)
#define FCH_SONGSHAN_FCHSONGSHANCORE_SONGSHAN_SNINTERFACE_SNFCHINITENV_FILECODE        (0xB468)
#define FCH_SONGSHAN_FCHSONGSHANCORE_SONGSHAN_SNINTERFACE_SNFCHINITLATE_FILECODE       (0xB469)
#define FCH_SONGSHAN_FCHSONGSHANCORE_SONGSHAN_SNINTERFACE_SNFCHINITMID_FILECODE        (0xB46A)
#define FCH_SONGSHAN_FCHSONGSHANCORE_SONGSHAN_SNINTERFACE_SNFCHINITRESET_FILECODE      (0xB46B)
#define FCH_SONGSHAN_FCHSONGSHANCORE_SONGSHAN_SNINTERFACE_SNFCHINITS3_FILECODE         (0xB46C)
#define FCH_SONGSHAN_FCHSONGSHANCORE_SONGSHAN_SNINTERFACE_SNFCHTASKLAUNCHER_FILECODE   (0xB46D)
#define FCH_SONGSHAN_FCHSONGSHANCORE_SONGSHAN_SNIR_SNIRENV_FILECODE                    (0xB46E)
#define FCH_SONGSHAN_FCHSONGSHANCORE_SONGSHAN_SNIR_SNIRLATE_FILECODE                   (0xB46F)
#define FCH_SONGSHAN_FCHSONGSHANCORE_SONGSHAN_SNIR_SNIRMID_FILECODE                    (0xB470)
#define FCH_SONGSHAN_FCHSONGSHANCORE_SONGSHAN_SNPCIE_SNABENVSERVICE_FILECODE           (0xB471)
#define FCH_SONGSHAN_FCHSONGSHANCORE_SONGSHAN_SNPCIE_SNABENV_FILECODE                  (0xB472)
#define FCH_SONGSHAN_FCHSONGSHANCORE_SONGSHAN_SNPCIE_SNABLATE_FILECODE                 (0xB473)
#define FCH_SONGSHAN_FCHSONGSHANCORE_SONGSHAN_SNPCIE_SNABMID_FILECODE                  (0xB474)
#define FCH_SONGSHAN_FCHSONGSHANCORE_SONGSHAN_SNPCIE_SNABRESETSERVICE_FILECODE         (0xB475)
#define FCH_SONGSHAN_FCHSONGSHANCORE_SONGSHAN_SNPCIE_SNABRESET_FILECODE                (0xB476)
#define FCH_SONGSHAN_FCHSONGSHANCORE_SONGSHAN_SNPCIE_SNABSERVICE_FILECODE              (0xB477)
#define FCH_SONGSHAN_FCHSONGSHANCORE_SONGSHAN_SNSATA_SNAHCIENV_FILECODE                (0xB478)
#define FCH_SONGSHAN_FCHSONGSHANCORE_SONGSHAN_SNSATA_SNAHCILATE_FILECODE               (0xB479)
#define FCH_SONGSHAN_FCHSONGSHANCORE_SONGSHAN_SNSATA_SNAHCILIB_FILECODE                (0xB47A)
#define FCH_SONGSHAN_FCHSONGSHANCORE_SONGSHAN_SNSATA_SNAHCIMID_FILECODE                (0xB47B)
#define FCH_SONGSHAN_FCHSONGSHANCORE_SONGSHAN_SNSATA_SNIDE2AHCIENV_FILECODE            (0xB47C)
#define FCH_SONGSHAN_FCHSONGSHANCORE_SONGSHAN_SNSATA_SNIDE2AHCILATE_FILECODE           (0xB47D)
#define FCH_SONGSHAN_FCHSONGSHANCORE_SONGSHAN_SNSATA_SNIDE2AHCILIB_FILECODE            (0xB47E)
#define FCH_SONGSHAN_FCHSONGSHANCORE_SONGSHAN_SNSATA_SNIDE2AHCIMID_FILECODE            (0xB47F)
#define FCH_SONGSHAN_FCHSONGSHANCORE_SONGSHAN_SNSATA_SNRAIDENV_FILECODE                (0xB480)
#define FCH_SONGSHAN_FCHSONGSHANCORE_SONGSHAN_SNSATA_SNRAIDLATE_FILECODE               (0xB481)
#define FCH_SONGSHAN_FCHSONGSHANCORE_SONGSHAN_SNSATA_SNRAIDLIB_FILECODE                (0xB482)
#define FCH_SONGSHAN_FCHSONGSHANCORE_SONGSHAN_SNSATA_SNRAIDMID_FILECODE                (0xB483)
#define FCH_SONGSHAN_FCHSONGSHANCORE_SONGSHAN_SNSATA_SNSATAENVLIB_FILECODE             (0xB484)
#define FCH_SONGSHAN_FCHSONGSHANCORE_SONGSHAN_SNSATA_SNSATAENVSERVICE_FILECODE         (0xB485)
#define FCH_SONGSHAN_FCHSONGSHANCORE_SONGSHAN_SNSATA_SNSATAENV_FILECODE                (0xB486)
#define FCH_SONGSHAN_FCHSONGSHANCORE_SONGSHAN_SNSATA_SNSATAIDELIB_FILECODE             (0xB487)
#define FCH_SONGSHAN_FCHSONGSHANCORE_SONGSHAN_SNSATA_SNSATAIDEENV_FILECODE             (0xB488)
#define FCH_SONGSHAN_FCHSONGSHANCORE_SONGSHAN_SNSATA_SNSATAIDELATE_FILECODE            (0xB489)
#define FCH_SONGSHAN_FCHSONGSHANCORE_SONGSHAN_SNSATA_SNSATAIDEMID_FILECODE             (0xB48A)
#define FCH_SONGSHAN_FCHSONGSHANCORE_SONGSHAN_SNSATA_SNSATALATE_FILECODE               (0xB48B)
#define FCH_SONGSHAN_FCHSONGSHANCORE_SONGSHAN_SNSATA_SNSATALIB_FILECODE                (0xB48C)
#define FCH_SONGSHAN_FCHSONGSHANCORE_SONGSHAN_SNSATA_SNSATAMID_FILECODE                (0xB48D)
#define FCH_SONGSHAN_FCHSONGSHANCORE_SONGSHAN_SNSATA_SNSATARESETSERVICE_FILECODE       (0xB48E)
#define FCH_SONGSHAN_FCHSONGSHANCORE_SONGSHAN_SNSATA_SNSATARESET_FILECODE              (0xB48F)
#define FCH_SONGSHAN_FCHSONGSHANCORE_SONGSHAN_SNSATA_SNSATASERVICE_FILECODE            (0xB490)
#define FCH_SONGSHAN_FCHSONGSHANCORE_SONGSHAN_SNSD_SNSDENVSERVICE_FILECODE             (0xB491)
#define FCH_SONGSHAN_FCHSONGSHANCORE_SONGSHAN_SNSD_SNSDENV_FILECODE                    (0xB492)
#define FCH_SONGSHAN_FCHSONGSHANCORE_SONGSHAN_SNSD_SNSDLATE_FILECODE                   (0xB493)
#define FCH_SONGSHAN_FCHSONGSHANCORE_SONGSHAN_SNSD_SNSDMID_FILECODE                    (0xB494)
#define FCH_SONGSHAN_FCHSONGSHANCORE_SONGSHAN_SNSPI_SNLPCENVSERVICE_FILECODE           (0xB495)
#define FCH_SONGSHAN_FCHSONGSHANCORE_SONGSHAN_SNSPI_SNLPCENV_FILECODE                  (0xB496)
#define FCH_SONGSHAN_FCHSONGSHANCORE_SONGSHAN_SNSPI_SNLPCLATE_FILECODE                 (0xB497)
#define FCH_SONGSHAN_FCHSONGSHANCORE_SONGSHAN_SNSPI_SNLPCMID_FILECODE                  (0xB498)
#define FCH_SONGSHAN_FCHSONGSHANCORE_SONGSHAN_SNSPI_SNLPCRESETSERVICE_FILECODE         (0xB499)
#define FCH_SONGSHAN_FCHSONGSHANCORE_SONGSHAN_SNSPI_SNLPCRESET_FILECODE                (0xB49A)
#define FCH_SONGSHAN_FCHSONGSHANCORE_SONGSHAN_SNSPI_SNSPIENV_FILECODE                  (0xB49B)
#define FCH_SONGSHAN_FCHSONGSHANCORE_SONGSHAN_SNSPI_SNSPILATE_FILECODE                 (0xB49C)
#define FCH_SONGSHAN_FCHSONGSHANCORE_SONGSHAN_SNSPI_SNSPIMID_FILECODE                  (0xB49D)
#define FCH_SONGSHAN_FCHSONGSHANCORE_SONGSHAN_SNUSB_SNUSBENV_FILECODE                  (0xB49E)
#define FCH_SONGSHAN_FCHSONGSHANCORE_SONGSHAN_SNUSB_SNUSBLATE_FILECODE                 (0xB49F)
#define FCH_SONGSHAN_FCHSONGSHANCORE_SONGSHAN_SNUSB_SNUSBMID_FILECODE                  (0xB4A0)
#define FCH_SONGSHAN_FCHSONGSHANCORE_SONGSHAN_SNUSB_SNUSBRESET_FILECODE                (0xB4A1)
#define FCH_SONGSHAN_FCHSONGSHANCORE_SONGSHAN_SNUSB_SNXHCIENVSERVICE_FILECODE          (0xB4A2)
#define FCH_SONGSHAN_FCHSONGSHANCORE_SONGSHAN_SNUSB_SNXHCIENV_FILECODE                 (0xB4A3)
#define FCH_SONGSHAN_FCHSONGSHANCORE_SONGSHAN_SNUSB_SNXHCILATESERVICE_FILECODE         (0xB4A4)
#define FCH_SONGSHAN_FCHSONGSHANCORE_SONGSHAN_SNUSB_SNXHCILATE_FILECODE                (0xB4A5)
#define FCH_SONGSHAN_FCHSONGSHANCORE_SONGSHAN_SNUSB_SNXHCIMID_FILECODE                 (0xB4A6)
#define FCH_SONGSHAN_FCHSONGSHANCORE_SONGSHAN_SNUSB_SNXHCIRESETSERVICE_FILECODE        (0xB4A7)
#define FCH_SONGSHAN_FCHSONGSHANCORE_SONGSHAN_SNUSB_SNXHCIRESET_FILECODE               (0xB4A8)
#define FCH_SONGSHAN_FCHSONGSHANCORE_SONGSHAN_SNUSB_SNXHCISERVICE_FILECODE             (0xB4A9)
#define FCH_SONGSHAN_FCHSONGSHANCORE_SONGSHAN_SNESPI_SNESPIRESET_FILECODE              (0xB4AA)
#define FCH_SONGSHAN_FCHSONGSHANCORE_SONGSHAN_SNESPI_SNESPIENV_FILECODE                (0xB4AB)
#define FCH_SONGSHAN_FCHSONGSHANCORE_SONGSHAN_SNESPI_SNESPIMID_FILECODE                (0xB4AC)
#define FCH_SONGSHAN_FCHSONGSHANCORE_SONGSHAN_SNESPI_SNESPILATE_FILECODE               (0xB4AD)
#define FCH_SONGSHAN_FCHSONGSHANCORE_SONGSHAN_SNESPI_SNESPILIB_FILECODE                (0xB4AE)
#define FCH_SONGSHAN_FCHSONGSHANCORE_SONGSHAN_SNXGBE_SNXGBEENV_FILECODE                (0xB4AF)
#define FCH_SONGSHAN_FCHSONGSHANCORE_SONGSHAN_SNXGBE_SNXGBELATE_FILECODE               (0xB4B0)
#define FCH_SONGSHAN_FCHSONGSHANCORE_SONGSHAN_SNXGBE_SNXGBEMID_FILECODE                (0xB4B1)
#define FCH_SONGSHAN_FCHSONGSHANCORE_SONGSHAN_SNXGBE_SNXGBERESET_FILECODE              (0xB4B2)
#define FCH_SONGSHAN_FCHSONGSHANCORE_SONGSHAN_SNXGBE_SNXGBELIB_FILECODE                (0xB4B3)
#define FCH_SONGSHAN_FCHSONGSHANCF9RESETDXE_CF9RESET_FILECODE                         (0xB4B4)
#define FCH_SONGSHAN_FCHSONGSHANMULTIFCHDXE_FCHMULTIFCHDXE_FILECODE                   (0xB4B5)
#define FCH_SONGSHAN_FCHSONGSHANMULTIFCHPEI_FCHMULTIFCHPEI_FILECODE                   (0xB4B6)
#define FCH_SONGSHAN_FCHSONGSHANDXE_FCHDXE_FILECODE                                   (0xB4B7)
#define FCH_SONGSHAN_FCHSONGSHANLEGACYINTERRUPTDXE_LEGACYINTERRUPT_FILECODE           (0xB4B8)
#define FCH_SONGSHAN_FCHSONGSHANPEI_FCHPEI_FILECODE                                   (0xB4B9)
#define FCH_SONGSHAN_FCHSONGSHANPEI_FCHRESET_FILECODE                                 (0xB4BA)
#define FCH_SONGSHAN_FCHSONGSHANPEI_FCHSTALL_FILECODE                                 (0xB4BB)
#define FCH_SONGSHAN_FCHSONGSHANSMBUSDXE_SMBUSLIGHT_FILECODE                          (0xB4BC)
#define FCH_SONGSHAN_FCHSONGSHANSMBUSPEI_SMBUS_FILECODE                               (0xB4BD)
#define FCH_SONGSHAN_FCHSONGSHANSMMCONTROLDXE_SMMCONTROL_FILECODE                     (0xB4BE)
#define FCH_SONGSHAN_FCHSONGSHANSMMDISPATCHER_FCHSMMDIAGDISPATCHER_FILECODE                                  (0xB4BF)
#define FCH_SONGSHAN_FCHSONGSHANSMMDISPATCHER_FCHSMMDISPATCHER_FILECODE                                      (0xB4C0)
#define FCH_SONGSHAN_FCHSONGSHANSMMDISPATCHER_FCHSMMGPIDISPATCHER_FCHSMMGPIDISPATCHER_FILECODE               (0xB4C1)
#define FCH_SONGSHAN_FCHSONGSHANSMMDISPATCHER_FCHSMMIOTRAPDISPATCHER_FCHSMMIOTRAPDISPATCHER_FILECODE         (0xB4C2)
#define FCH_SONGSHAN_FCHSONGSHANSMMDISPATCHER_FCHSMMMISCDISPATCHER_FCHSMMMISCDISPATCHER_FILECODE             (0xB4C3)
#define FCH_SONGSHAN_FCHSONGSHANSMMDISPATCHER_FCHSMMPERIODICALDISPATCHER_FCHSMMPERIODICALDISPATCHER_FILECODE (0xB4C4)
#define FCH_SONGSHAN_FCHSONGSHANSMMDISPATCHER_FCHSMMPWRBTNDISPATCHER_FCHSMMPWRBTNDISPATCHER_FILECODE         (0xB4C5)
#define FCH_SONGSHAN_FCHSONGSHANSMMDISPATCHER_FCHSMMSWDISPATCHER_FCHSMMSWDISPATCHER_FILECODE                 (0xB4C6)
#define FCH_SONGSHAN_FCHSONGSHANSMMDISPATCHER_FCHSMMSXDISPATCHER_FCHSMMSXDISPATCHER_FILECODE                 (0xB4C7)
#define FCH_SONGSHAN_FCHSONGSHANSMMDISPATCHER_FCHSMMUSBDISPATCHER_FCHSMMUSBDISPATCHER_FILECODE               (0xB4C8)
#define FCH_SONGSHAN_FCHSONGSHANSMMDISPATCHER_FCHSMMAPURASDISPATCHER_FCHSMMAPURASDISPATCHER_FILECODE         (0xB4C9)
#define FCH_SONGSHAN_FCHSONGSHANSMMDISPATCHER_FCHSMMAPURASDISPATCHER_FCHSSPRASSMILIB_FILECODE                (0xB4CA)
#define FCH_SONGSHAN_FCHSONGSHANSMMDISPATCHER_FCHSMMAPURASDISPATCHER_FCHRASSMILIB_FILECODE                   (0xB4CB)
#define FCH_SONGSHAN_FCHSONGSHANSMM_FCHSMM_FILECODE                         (0xB4CC)
#define FCH_SONGSHAN_FCHSONGSHANSMM_GPISMI_FILECODE                         (0xB4CD)
#define FCH_SONGSHAN_FCHSONGSHANSMM_IOTRAPSMI_FILECODE                      (0xB4CE)
#define FCH_SONGSHAN_FCHSONGSHANSMM_MISCSMI_FILECODE                        (0xB4CF)
#define FCH_SONGSHAN_FCHSONGSHANSMM_PERIODICTIMERSMI_FILECODE               (0xB4D0)
#define FCH_SONGSHAN_FCHSONGSHANSMM_POWERBUTTONSMI_FILECODE                 (0xB4D1)
#define FCH_SONGSHAN_FCHSONGSHANSMM_SWSMI_FILECODE                          (0xB4D2)
#define FCH_SONGSHAN_FCHSONGSHANSMM_SXSMI_FILECODE                          (0xB4D3)
#define FCH_SONGSHAN_FCHSONGSHANI3CPEI_I3C_FILECODE                         (0xB4D4)

#define FCH_SONGSHAN_FCHSONGSHANCORE_SONGSHAN_SNLPCSPI_SNLPCENV_FILECODE                          (0xB4D5)
#define FCH_SONGSHAN_FCHSONGSHANCORE_SONGSHAN_SNLPCSPI_SNLPCENVSERVICE_FILECODE                   (0xB4D6)
#define FCH_SONGSHAN_FCHSONGSHANCORE_SONGSHAN_SNLPCSPI_SNLPCLATE_FILECODE                         (0xB4D7)
#define FCH_SONGSHAN_FCHSONGSHANCORE_SONGSHAN_SNLPCSPI_SNLPCMID_FILECODE                          (0xB4D8)
#define FCH_SONGSHAN_FCHSONGSHANCORE_SONGSHAN_SNLPCSPI_SNLPCRESET_FILECODE                        (0xB4D9)
#define FCH_SONGSHAN_FCHSONGSHANCORE_SONGSHAN_SNLPCSPI_SNLPCRESETSERVICE_FILECODE                 (0xB4DA)
#define FCH_SONGSHAN_FCHSONGSHANCORE_SONGSHAN_SNLPCSPI_SNSPIENV_FILECODE                          (0xB4DB)
#define FCH_SONGSHAN_FCHSONGSHANCORE_SONGSHAN_SNLPCSPI_SNSPILATE_FILECODE                         (0xB4DC)
#define FCH_SONGSHAN_FCHSONGSHANCORE_SONGSHAN_SNLPCSPI_SNSPIMID_FILECODE                          (0xB4DE)

#define FCH_SONGSHAN_FCHSONGSHANDXE_FCHSONGSHANSSDT_FILECODE                          (0xB4DF)
#define FCH_SONGSHAN_FCHSONGSHANI3CDXE_AMDI3CMASTERDXE_FILECODE                       (0xB4E0)
#define FCH_SONGSHAN_FCHSONGSHANI3CDXE_I3CSMNDXE_FILECODE                             (0xB4E1)
#define FCH_SONGSHAN_FCHSONGSHANI3CPEI_AMDI3CMASTERPEI_FILECODE                       (0xB4E2)
#define FCH_SONGSHAN_FCHSONGSHANI3CPEI_I3CSMNPEI_FILECODE                             (0xB4E3)
#define FCH_SONGSHAN_FCHSONGSHANMULTIFCHSMM_FCHMULTIFCHSMM_FILECODE                   (0xB4E4)
#define FCH_SONGSHAN_FCHSONGSHANPEI_FCHRESET2_FILECODE                                (0xB4E5)


#define LIBRARY_FCHIDSHOOKSTPLIB_PEI_FCHIDSHOOKSTPLIBPEI_FILECODE                                            (0xB4F0)
#define LIBRARY_FCHIDSHOOKSTPLIB_DXE_FCHIDSHOOKSTPLIBDXE_FILECODE                                            (0xB4F1)

#define FCH_QIANTANG_FCHQIANTANGCORE_COMMON_ACPILIB_FILECODE                                                 (0xBA00)
#define FCH_QIANTANG_FCHQIANTANGCORE_COMMON_FCHCOMMONSMM_FILECODE                                            (0xBA01)
#define FCH_QIANTANG_FCHQIANTANGCORE_COMMON_FCHCOMMON_FILECODE                                               (0xBA02)
#define FCH_QIANTANG_FCHQIANTANGCORE_COMMON_FCHLIB_FILECODE                                                  (0xBA03)
#define FCH_QIANTANG_FCHQIANTANGCORE_COMMON_FCHPELIB_FILECODE                                                (0xBA04)
#define FCH_QIANTANG_FCHQIANTANGCORE_COMMON_MEMLIB_FILECODE                                                  (0xBA05)
#define FCH_QIANTANG_FCHQIANTANGCORE_COMMON_PCILIB_FILECODE                                                  (0xBA06)
#define FCH_QIANTANG_FCHQIANTANGCORE_COMMON_FCHAOACLIB_FILECODE                                              (0xBA07)
#define FCH_QIANTANG_FCHQIANTANGCORE_COMMON_FCHSMNLIB_FILECODE                                               (0xBA08)
#define FCH_QIANTANG_FCHQIANTANGCORE_QIANTANG_QTEMMC_QTEMMCENV_FILECODE                                      (0xBA10)
#define FCH_QIANTANG_FCHQIANTANGCORE_QIANTANG_QTEMMC_QTEMMCLATE_FILECODE                                     (0xBA11)
#define FCH_QIANTANG_FCHQIANTANGCORE_QIANTANG_QTEMMC_QTEMMCMID_FILECODE                                      (0xBA12)
#define FCH_QIANTANG_FCHQIANTANGCORE_QIANTANG_QTEMMC_QTEMMCRESET_FILECODE                                    (0xBA13)
#define FCH_QIANTANG_FCHQIANTANGCORE_QIANTANG_QTGBE_QTGBEENV_FILECODE                                        (0xBA14)
#define FCH_QIANTANG_FCHQIANTANGCORE_QIANTANG_QTGBE_QTGBELATE_FILECODE                                       (0xBA15)
#define FCH_QIANTANG_FCHQIANTANGCORE_QIANTANG_QTGBE_QTGBEMID_FILECODE                                        (0xBA16)
#define FCH_QIANTANG_FCHQIANTANGCORE_QIANTANG_QTGBE_QTGBERESET_FILECODE                                      (0xBA17)
#define FCH_QIANTANG_FCHQIANTANGCORE_QIANTANG_QTHWACPI_QTHWACPIENVSERVICE_FILECODE                           (0xBA18)
#define FCH_QIANTANG_FCHQIANTANGCORE_QIANTANG_QTHWACPI_QTHWACPIENV_FILECODE                                  (0xBA19)
#define FCH_QIANTANG_FCHQIANTANGCORE_QIANTANG_QTHWACPI_QTHWACPILATESERVICE_FILECODE                          (0xBA1A)
#define FCH_QIANTANG_FCHQIANTANGCORE_QIANTANG_QTHWACPI_QTHWACPILATE_FILECODE                                 (0xBA1B)
#define FCH_QIANTANG_FCHQIANTANGCORE_QIANTANG_QTHWACPI_QTHWACPIMID_FILECODE                                  (0xBA1C)
#define FCH_QIANTANG_FCHQIANTANGCORE_QIANTANG_QTHWACPI_QTHWACPIRESET_FILECODE                                (0xBA1D)
#define FCH_QIANTANG_FCHQIANTANGCORE_QIANTANG_QTHWACPI_QTSSSERVICE_FILECODE                                  (0xBA1E)
#define FCH_QIANTANG_FCHQIANTANGCORE_QIANTANG_QTHWM_QTHWMENVSERVICE_FILECODE                                 (0xBA20)
#define FCH_QIANTANG_FCHQIANTANGCORE_QIANTANG_QTHWM_QTHWMENV_FILECODE                                        (0xBA21)
#define FCH_QIANTANG_FCHQIANTANGCORE_QIANTANG_QTHWM_QTHWMLATESERVICE_FILECODE                                (0xBA22)
#define FCH_QIANTANG_FCHQIANTANGCORE_QIANTANG_QTHWM_QTHWMLATE_FILECODE                                       (0xBA23)
#define FCH_QIANTANG_FCHQIANTANGCORE_QIANTANG_QTHWM_QTHWMMID_FILECODE                                        (0xBA24)
#define FCH_QIANTANG_FCHQIANTANGCORE_QIANTANG_QTHWM_QTHWMRESET_FILECODE                                      (0xBA25)
#define FCH_QIANTANG_FCHQIANTANGCORE_QIANTANG_QTIMC_QTFCHECENV_FILECODE                                      (0xBA26)
#define FCH_QIANTANG_FCHQIANTANGCORE_QIANTANG_QTIMC_QTFCHECLATE_FILECODE                                     (0xBA27)
#define FCH_QIANTANG_FCHQIANTANGCORE_QIANTANG_QTIMC_QTFCHECMID_FILECODE                                      (0xBA28)
#define FCH_QIANTANG_FCHQIANTANGCORE_QIANTANG_QTIMC_QTFCHECRESET_FILECODE                                    (0xBA29)
#define FCH_QIANTANG_FCHQIANTANGCORE_QIANTANG_QTIMC_QTIMCENV_FILECODE                                        (0xBA2A)
#define FCH_QIANTANG_FCHQIANTANGCORE_QIANTANG_QTIMC_QTIMCLATE_FILECODE                                       (0xBA2B)
#define FCH_QIANTANG_FCHQIANTANGCORE_QIANTANG_QTIMC_QTIMCLIB_FILECODE                                        (0xBA2C)
#define FCH_QIANTANG_FCHQIANTANGCORE_QIANTANG_QTIMC_QTIMCMID_FILECODE                                        (0xBA2D)
#define FCH_QIANTANG_FCHQIANTANGCORE_QIANTANG_QTIMC_QTIMCRESET_FILECODE                                      (0xBA2E)
#define FCH_QIANTANG_FCHQIANTANGCORE_QIANTANG_QTIMC_QTIMCSERVICE_FILECODE                                    (0xBA2F)
#define FCH_QIANTANG_FCHQIANTANGCORE_QIANTANG_QTINTERFACE_QTFCHINITENV_FILECODE                              (0xBA30)
#define FCH_QIANTANG_FCHQIANTANGCORE_QIANTANG_QTINTERFACE_QTFCHINITLATE_FILECODE                             (0xBA31)
#define FCH_QIANTANG_FCHQIANTANGCORE_QIANTANG_QTINTERFACE_QTFCHINITMID_FILECODE                              (0xBA32)
#define FCH_QIANTANG_FCHQIANTANGCORE_QIANTANG_QTINTERFACE_QTFCHINITRESET_FILECODE                            (0xBA33)
#define FCH_QIANTANG_FCHQIANTANGCORE_QIANTANG_QTINTERFACE_QTFCHINITS3_FILECODE                               (0xBA34)
#define FCH_QIANTANG_FCHQIANTANGCORE_QIANTANG_QTINTERFACE_QTFCHTASKLAUNCHER_FILECODE                         (0xBA35)
#define FCH_QIANTANG_FCHQIANTANGCORE_QIANTANG_QTIR_QTIRENV_FILECODE                                          (0xBA36)
#define FCH_QIANTANG_FCHQIANTANGCORE_QIANTANG_QTIR_QTIRLATE_FILECODE                                         (0xBA37)
#define FCH_QIANTANG_FCHQIANTANGCORE_QIANTANG_QTIR_QTIRMID_FILECODE                                          (0xBA38)
#define FCH_QIANTANG_FCHQIANTANGCORE_QIANTANG_QTPCIE_QTABENVSERVICE_FILECODE                                 (0xBA39)
#define FCH_QIANTANG_FCHQIANTANGCORE_QIANTANG_QTPCIE_QTABENV_FILECODE                                        (0xBA3A)
#define FCH_QIANTANG_FCHQIANTANGCORE_QIANTANG_QTPCIE_QTABLATE_FILECODE                                       (0xBA3B)
#define FCH_QIANTANG_FCHQIANTANGCORE_QIANTANG_QTPCIE_QTABMID_FILECODE                                        (0xBA3C)
#define FCH_QIANTANG_FCHQIANTANGCORE_QIANTANG_QTPCIE_QTABRESETSERVICE_FILECODE                               (0xBA3D)
#define FCH_QIANTANG_FCHQIANTANGCORE_QIANTANG_QTPCIE_QTABRESET_FILECODE                                      (0xBA3E)
#define FCH_QIANTANG_FCHQIANTANGCORE_QIANTANG_QTPCIE_QTABSERVICE_FILECODE                                    (0xBA3F)
#define FCH_QIANTANG_FCHQIANTANGCORE_QIANTANG_QTSATA_QTAHCIENV_FILECODE                                      (0xBA40)
#define FCH_QIANTANG_FCHQIANTANGCORE_QIANTANG_QTSATA_QTAHCILATE_FILECODE                                     (0xBA41)
#define FCH_QIANTANG_FCHQIANTANGCORE_QIANTANG_QTSATA_QTAHCILIB_FILECODE                                      (0xBA42)
#define FCH_QIANTANG_FCHQIANTANGCORE_QIANTANG_QTSATA_QTAHCIMID_FILECODE                                      (0xBA43)
#define FCH_QIANTANG_FCHQIANTANGCORE_QIANTANG_QTSATA_QTIDE2AHCIENV_FILECODE                                  (0xBA44)
#define FCH_QIANTANG_FCHQIANTANGCORE_QIANTANG_QTSATA_QTIDE2AHCILATE_FILECODE                                 (0xBA45)
#define FCH_QIANTANG_FCHQIANTANGCORE_QIANTANG_QTSATA_QTIDE2AHCILIB_FILECODE                                  (0xBA46)
#define FCH_QIANTANG_FCHQIANTANGCORE_QIANTANG_QTSATA_QTIDE2AHCIMID_FILECODE                                  (0xBA47)
#define FCH_QIANTANG_FCHQIANTANGCORE_QIANTANG_QTSATA_QTRAIDENV_FILECODE                                      (0xBA48)
#define FCH_QIANTANG_FCHQIANTANGCORE_QIANTANG_QTSATA_QTRAIDLATE_FILECODE                                     (0xBA49)
#define FCH_QIANTANG_FCHQIANTANGCORE_QIANTANG_QTSATA_QTRAIDLIB_FILECODE                                      (0xBA4A)
#define FCH_QIANTANG_FCHQIANTANGCORE_QIANTANG_QTSATA_QTRAIDMID_FILECODE                                      (0xBA4B)
#define FCH_QIANTANG_FCHQIANTANGCORE_QIANTANG_QTSATA_QTSATAENVLIB_FILECODE                                   (0xBA4C)
#define FCH_QIANTANG_FCHQIANTANGCORE_QIANTANG_QTSATA_QTSATAENVSERVICE_FILECODE                               (0xBA4D)
#define FCH_QIANTANG_FCHQIANTANGCORE_QIANTANG_QTSATA_QTSATAENV_FILECODE                                      (0xBA4E)
#define FCH_QIANTANG_FCHQIANTANGCORE_QIANTANG_QTSATA_QTSATAIDELIB_FILECODE                                   (0xBA50)
#define FCH_QIANTANG_FCHQIANTANGCORE_QIANTANG_QTSATA_QTSATAIDEENV_FILECODE                                   (0xBA51)
#define FCH_QIANTANG_FCHQIANTANGCORE_QIANTANG_QTSATA_QTSATAIDELATE_FILECODE                                  (0xBA52)
#define FCH_QIANTANG_FCHQIANTANGCORE_QIANTANG_QTSATA_QTSATAIDEMID_FILECODE                                   (0xBA53)
#define FCH_QIANTANG_FCHQIANTANGCORE_QIANTANG_QTSATA_QTSATALATE_FILECODE                                     (0xBA54)
#define FCH_QIANTANG_FCHQIANTANGCORE_QIANTANG_QTSATA_QTSATALIB_FILECODE                                      (0xBA55)
#define FCH_QIANTANG_FCHQIANTANGCORE_QIANTANG_QTSATA_QTSATAMID_FILECODE                                      (0xBA56)
#define FCH_QIANTANG_FCHQIANTANGCORE_QIANTANG_QTSATA_QTSATARESETSERVICE_FILECODE                             (0xBA57)
#define FCH_QIANTANG_FCHQIANTANGCORE_QIANTANG_QTSATA_QTSATARESET_FILECODE                                    (0xBA58)
#define FCH_QIANTANG_FCHQIANTANGCORE_QIANTANG_QTSATA_QTSATASERVICE_FILECODE                                  (0xBA59)
#define FCH_QIANTANG_FCHQIANTANGCORE_QIANTANG_QTSD_QTSDENVSERVICE_FILECODE                                   (0xBA60)
#define FCH_QIANTANG_FCHQIANTANGCORE_QIANTANG_QTSD_QTSDENV_FILECODE                                          (0xBA61)
#define FCH_QIANTANG_FCHQIANTANGCORE_QIANTANG_QTSD_QTSDLATE_FILECODE                                         (0xBA62)
#define FCH_QIANTANG_FCHQIANTANGCORE_QIANTANG_QTSD_QTSDMID_FILECODE                                          (0xBA63)
#define FCH_QIANTANG_FCHQIANTANGCORE_QIANTANG_QTSPI_QTLPCENVSERVICE_FILECODE                                 (0xBA64)
#define FCH_QIANTANG_FCHQIANTANGCORE_QIANTANG_QTSPI_QTLPCENV_FILECODE                                        (0xBA65)
#define FCH_QIANTANG_FCHQIANTANGCORE_QIANTANG_QTSPI_QTLPCLATE_FILECODE                                       (0xBA66)
#define FCH_QIANTANG_FCHQIANTANGCORE_QIANTANG_QTSPI_QTLPCMID_FILECODE                                        (0xBA67)
#define FCH_QIANTANG_FCHQIANTANGCORE_QIANTANG_QTSPI_QTLPCRESETSERVICE_FILECODE                               (0xBA68)
#define FCH_QIANTANG_FCHQIANTANGCORE_QIANTANG_QTSPI_QTLPCRESET_FILECODE                                      (0xBA69)
#define FCH_QIANTANG_FCHQIANTANGCORE_QIANTANG_QTSPI_QTSPIENV_FILECODE                                        (0xBA6A)
#define FCH_QIANTANG_FCHQIANTANGCORE_QIANTANG_QTSPI_QTSPILATE_FILECODE                                       (0xBA6B)
#define FCH_QIANTANG_FCHQIANTANGCORE_QIANTANG_QTSPI_QTSPIMID_FILECODE                                        (0xBA6C)
#define FCH_QIANTANG_FCHQIANTANGCORE_QIANTANG_QTUSB_QTUSBENV_FILECODE                                        (0xBA75)
#define FCH_QIANTANG_FCHQIANTANGCORE_QIANTANG_QTUSB_QTUSBLATE_FILECODE                                       (0xBA76)
#define FCH_QIANTANG_FCHQIANTANGCORE_QIANTANG_QTUSB_QTUSBMID_FILECODE                                        (0xBA77)
#define FCH_QIANTANG_FCHQIANTANGCORE_QIANTANG_QTUSB_QTUSBRESET_FILECODE                                      (0xBA78)
#define FCH_QIANTANG_FCHQIANTANGCORE_QIANTANG_QTUSB_QTXHCIENVSERVICE_FILECODE                                (0xBA79)
#define FCH_QIANTANG_FCHQIANTANGCORE_QIANTANG_QTUSB_QTXHCIENV_FILECODE                                       (0xBA7A)
#define FCH_QIANTANG_FCHQIANTANGCORE_QIANTANG_QTUSB_QTXHCILATESERVICE_FILECODE                               (0xBA7B)
#define FCH_QIANTANG_FCHQIANTANGCORE_QIANTANG_QTUSB_QTXHCILATE_FILECODE                                      (0xBA7C)
#define FCH_QIANTANG_FCHQIANTANGCORE_QIANTANG_QTUSB_QTXHCIMID_FILECODE                                       (0xBA7D)
#define FCH_QIANTANG_FCHQIANTANGCORE_QIANTANG_QTUSB_QTXHCIRESETSERVICE_FILECODE                              (0xBA7E)
#define FCH_QIANTANG_FCHQIANTANGCORE_QIANTANG_QTUSB_QTXHCIRESET_FILECODE                                     (0xBA7F)
#define FCH_QIANTANG_FCHQIANTANGCORE_QIANTANG_QTUSB_QTXHCISERVICE_FILECODE                                   (0xBA80)
#define FCH_QIANTANG_FCHQIANTANGCORE_QIANTANG_QTESPI_QTESPIRESET_FILECODE                                    (0xBA90)
#define FCH_QIANTANG_FCHQIANTANGCORE_QIANTANG_QTESPI_QTESPIENV_FILECODE                                      (0xBA91)
#define FCH_QIANTANG_FCHQIANTANGCORE_QIANTANG_QTESPI_QTESPIMID_FILECODE                                      (0xBA92)
#define FCH_QIANTANG_FCHQIANTANGCORE_QIANTANG_QTESPI_QTESPILATE_FILECODE                                     (0xBA93)
#define FCH_QIANTANG_FCHQIANTANGCORE_QIANTANG_QTESPI_QTESPILIB_FILECODE                                      (0xBA94)
#define FCH_QIANTANG_FCHQIANTANGCORE_QIANTANG_QTXGBE_QTXGBEENV_FILECODE                                      (0xBAA0)
#define FCH_QIANTANG_FCHQIANTANGCORE_QIANTANG_QTXGBE_QTXGBELATE_FILECODE                                     (0xBAA1)
#define FCH_QIANTANG_FCHQIANTANGCORE_QIANTANG_QTXGBE_QTXGBEMID_FILECODE                                      (0xBAA2)
#define FCH_QIANTANG_FCHQIANTANGCORE_QIANTANG_QTXGBE_QTXGBERESET_FILECODE                                    (0xBAA3)
#define FCH_QIANTANG_FCHQIANTANGCORE_QIANTANG_QTXGBE_QTXGBELIB_FILECODE                                      (0xBAA4)
#define FCH_QIANTANG_FCHQIANTANGCF9RESETDXE_CF9RESET_FILECODE                                                (0xBAA5)
#define FCH_QIANTANG_FCHQIANTANGMULTIFCHDXE_FCHMULTIFCHDXE_FILECODE                                          (0xBAA6)
#define FCH_QIANTANG_FCHQIANTANGMULTIFCHPEI_FCHMULTIFCHPEI_FILECODE                                          (0xBAA7)
#define FCH_QIANTANG_FCHQIANTANGDXE_FCHDXE_FILECODE                                                          (0xBAA8)
#define FCH_QIANTANG_FCHQIANTANGLEGACYINTERRUPTDXE_LEGACYINTERRUPT_FILECODE                                  (0xBAA9)
#define FCH_QIANTANG_FCHQIANTANGPEI_FCHPEI_FILECODE                                                          (0xBAAA)
#define FCH_QIANTANG_FCHQIANTANGPEI_FCHRESET_FILECODE                                                        (0xBAAB)
#define FCH_QIANTANG_FCHQIANTANGPEI_FCHSTALL_FILECODE                                                        (0xBAAC)
#define FCH_QIANTANG_FCHQIANTANGSMBUSDXE_SMBUSLIGHT_FILECODE                                                 (0xBAAD)
#define FCH_QIANTANG_FCHQIANTANGSMBUSPEI_SMBUS_FILECODE                                                      (0xBAAE)
#define FCH_QIANTANG_FCHQIANTANGSMMCONTROLDXE_SMMCONTROL_FILECODE                                            (0xBAAF)
#define FCH_QIANTANG_FCHQIANTANGSMMDISPATCHER_FCHSMMDIAGDISPATCHER_FILECODE                                  (0xBAB0)
#define FCH_QIANTANG_FCHQIANTANGSMMDISPATCHER_FCHSMMDISPATCHER_FILECODE                                      (0xBAB1)
#define FCH_QIANTANG_FCHQIANTANGSMMDISPATCHER_FCHSMMGPIDISPATCHER_FCHSMMGPIDISPATCHER_FILECODE               (0xBAB2)
#define FCH_QIANTANG_FCHQIANTANGSMMDISPATCHER_FCHSMMIOTRAPDISPATCHER_FCHSMMIOTRAPDISPATCHER_FILECODE         (0xBAB3)
#define FCH_QIANTANG_FCHQIANTANGSMMDISPATCHER_FCHSMMMISCDISPATCHER_FCHSMMMISCDISPATCHER_FILECODE             (0xBAB4)
#define FCH_QIANTANG_FCHQIANTANGSMMDISPATCHER_FCHSMMPERIODICALDISPATCHER_FCHSMMPERIODICALDISPATCHER_FILECODE (0xBAB5)
#define FCH_QIANTANG_FCHQIANTANGSMMDISPATCHER_FCHSMMPWRBTNDISPATCHER_FCHSMMPWRBTNDISPATCHER_FILECODE         (0xBAB6)
#define FCH_QIANTANG_FCHQIANTANGSMMDISPATCHER_FCHSMMSWDISPATCHER_FCHSMMSWDISPATCHER_FILECODE                 (0xBAB7)
#define FCH_QIANTANG_FCHQIANTANGSMMDISPATCHER_FCHSMMSXDISPATCHER_FCHSMMSXDISPATCHER_FILECODE                 (0xBAB8)
#define FCH_QIANTANG_FCHQIANTANGSMMDISPATCHER_FCHSMMUSBDISPATCHER_FCHSMMUSBDISPATCHER_FILECODE               (0xBAB9)
#define FCH_QIANTANG_FCHQIANTANGSMMDISPATCHER_FCHSMMAPURASDISPATCHER_FCHSMMAPURASDISPATCHER_FILECODE         (0xBABA)
#define FCH_QIANTANG_FCHQIANTANGSMMDISPATCHER_FCHSMMAPURASDISPATCHER_FCHSSPRASSMILIB_FILECODE                (0xBABB)
#define FCH_QIANTANG_FCHQIANTANGSMM_FCHSMM_FILECODE                                                          (0xBAC0)
#define FCH_QIANTANG_FCHQIANTANGSMM_GPISMI_FILECODE                                                          (0xBAC1)
#define FCH_QIANTANG_FCHQIANTANGSMM_IOTRAPSMI_FILECODE                                                       (0xBAC2)
#define FCH_QIANTANG_FCHQIANTANGSMM_MISCSMI_FILECODE                                                         (0xBAC3)
#define FCH_QIANTANG_FCHQIANTANGSMM_PERIODICTIMERSMI_FILECODE                                                (0xBAC4)
#define FCH_QIANTANG_FCHQIANTANGSMM_POWERBUTTONSMI_FILECODE                                                  (0xBAC5)
#define FCH_QIANTANG_FCHQIANTANGSMM_SWSMI_FILECODE                                                           (0xBAC6)
#define FCH_QIANTANG_FCHQIANTANGSMM_SXSMI_FILECODE                                                           (0xBAC7)

#define FCH_SHASTA_FCHSHASTACORE_COMMON_ACPILIB_FILECODE                                                    (0xBB00)
#define FCH_SHASTA_FCHSHASTACORE_COMMON_FCHCOMMONSMM_FILECODE                                               (0xBB01)
#define FCH_SHASTA_FCHSHASTACORE_COMMON_FCHCOMMON_FILECODE                                                  (0xBB02)
#define FCH_SHASTA_FCHSHASTACORE_COMMON_FCHLIB_FILECODE                                                     (0xBB03)
#define FCH_SHASTA_FCHSHASTACORE_COMMON_FCHPELIB_FILECODE                                                   (0xBB04)
#define FCH_SHASTA_FCHSHASTACORE_COMMON_MEMLIB_FILECODE                                                     (0xBB05)
#define FCH_SHASTA_FCHSHASTACORE_COMMON_PCILIB_FILECODE                                                     (0xBB06)
#define FCH_SHASTA_FCHSHASTACORE_COMMON_FCHAOACLIB_FILECODE                                                 (0xBB07)
#define FCH_SHASTA_FCHSHASTACORE_COMMON_FCHSMNLIB_FILECODE                                                  (0xBB08)
#define FCH_SHASTA_FCHSHASTACORE_SHASTA_SHESPI_SHESPILIB_FILECODE                                           (0xBB10)

#define FCH_KEITH_FCHKEITHCORE_COMMON_ACPILIB_FILECODE                                                      (0xBB11)
#define FCH_KEITH_FCHKEITHCORE_COMMON_FCHCOMMONSMM_FILECODE                                                 (0xBB12)
#define FCH_KEITH_FCHKEITHCORE_COMMON_FCHCOMMON_FILECODE                                                    (0xBB13)
#define FCH_KEITH_FCHKEITHCORE_COMMON_FCHLIB_FILECODE                                                       (0xBB14)
#define FCH_KEITH_FCHKEITHCORE_COMMON_FCHPELIB_FILECODE                                                     (0xBB15)
#define FCH_KEITH_FCHKEITHCORE_COMMON_MEMLIB_FILECODE                                                       (0xBB16)
#define FCH_KEITH_FCHKEITHCORE_COMMON_PCILIB_FILECODE                                                       (0xBB17)
#define FCH_KEITH_FCHKEITHCORE_COMMON_FCHAOACLIB_FILECODE                                                   (0xBB18)
#define FCH_KEITH_FCHKEITHCORE_COMMON_FCHSMNLIB_FILECODE                                                    (0xBB19)
#define FCH_KEITH_FCHKEITHCORE_KEITH_KTESPI_KTESPILIB_FILECODE                                              (0xBB1A)
#define FCH_KEITH_FCHKEITHCF9RESETDXE_CF9RESET_FILECODE                                                     (0xBB1B)
#define FCH_KEITH_FCHKEITHCOMPLEMENT_FCHKTSATAD3COLD_FCHKTSATAD3COLDSMM_FILECODE                            (0xBB1C)
#define FCH_KEITH_FCHKEITHCORE_KEITH_KTESPI_KTESPIENV_FILECODE                                              (0xBB1D)
#define FCH_KEITH_FCHKEITHCORE_KEITH_KTESPI_KTESPILATE_FILECODE                                             (0xBB1E)
#define FCH_KEITH_FCHKEITHCORE_KEITH_KTESPI_KTESPIMID_FILECODE                                              (0xBB1F)
#define FCH_KEITH_FCHKEITHCORE_KEITH_KTESPI_KTESPIRESET_FILECODE                                            (0xBB20)
#define FCH_KEITH_FCHKEITHCORE_KEITH_KTHWACPI_KTHWACPIENV_FILECODE                                          (0xBB21)
#define FCH_KEITH_FCHKEITHCORE_KEITH_KTHWACPI_KTHWACPIENVSERVICE_FILECODE                                   (0xBB22)
#define FCH_KEITH_FCHKEITHCORE_KEITH_KTHWACPI_KTHWACPILATE_FILECODE                                         (0xBB23)
#define FCH_KEITH_FCHKEITHCORE_KEITH_KTHWACPI_KTHWACPILATESERVICE_FILECODE                                  (0xBB24)
#define FCH_KEITH_FCHKEITHCORE_KEITH_KTHWACPI_KTHWACPIMID_FILECODE                                          (0xBB25)
#define FCH_KEITH_FCHKEITHCORE_KEITH_KTHWACPI_KTHWACPIMIDSERVICE_FILECODE                                   (0xBB26)
#define FCH_KEITH_FCHKEITHCORE_KEITH_KTHWACPI_KTHWACPIRESET_FILECODE                                        (0xBB27)
#define FCH_KEITH_FCHKEITHCORE_KEITH_KTHWACPI_KTSSSERVICE_FILECODE                                          (0xBB28)
#define FCH_KEITH_FCHKEITHCORE_KEITH_KTINTERFACE_KTFCHINITENV_FILECODE                                      (0xBB29)
#define FCH_KEITH_FCHKEITHCORE_KEITH_KTINTERFACE_KTFCHINITLATE_FILECODE                                     (0xBB2A)
#define FCH_KEITH_FCHKEITHCORE_KEITH_KTINTERFACE_KTFCHINITMID_FILECODE                                      (0xBB2B)
#define FCH_KEITH_FCHKEITHCORE_KEITH_KTINTERFACE_KTFCHINITRESET_FILECODE                                    (0xBB2C)
#define FCH_KEITH_FCHKEITHCORE_KEITH_KTINTERFACE_KTFCHINITS3_FILECODE                                       (0xBB2D)
#define FCH_KEITH_FCHKEITHCORE_KEITH_KTINTERFACE_KTFCHTASKLAUNCHER_FILECODE                                 (0xBB2E)
#define FCH_KEITH_FCHKEITHCORE_KEITH_KTLPCSPI_KTLPCENV_FILECODE                                             (0xBB2F)
#define FCH_KEITH_FCHKEITHCORE_KEITH_KTLPCSPI_KTLPCENVSERVICE_FILECODE                                      (0xBB30)
#define FCH_KEITH_FCHKEITHCORE_KEITH_KTLPCSPI_KTLPCLATE_FILECODE                                            (0xBB31)
#define FCH_KEITH_FCHKEITHCORE_KEITH_KTLPCSPI_KTLPCMID_FILECODE                                             (0xBB32)
#define FCH_KEITH_FCHKEITHCORE_KEITH_KTLPCSPI_KTLPCRESET_FILECODE                                           (0xBB33)
#define FCH_KEITH_FCHKEITHCORE_KEITH_KTLPCSPI_KTLPCRESETSERVICE_FILECODE                                    (0xBB34)
#define FCH_KEITH_FCHKEITHCORE_KEITH_KTLPCSPI_KTSPIENV_FILECODE                                             (0xBB35)
#define FCH_KEITH_FCHKEITHCORE_KEITH_KTLPCSPI_KTSPILATE_FILECODE                                            (0xBB36)
#define FCH_KEITH_FCHKEITHCORE_KEITH_KTLPCSPI_KTSPIMID_FILECODE                                             (0xBB37)
#define FCH_KEITH_FCHKEITHCORE_KEITH_KTPCIE_KTABENV_FILECODE                                                (0xBB38)
#define FCH_KEITH_FCHKEITHCORE_KEITH_KTPCIE_KTABENVSERVICE_FILECODE                                         (0xBB39)
#define FCH_KEITH_FCHKEITHCORE_KEITH_KTPCIE_KTABLATE_FILECODE                                               (0xBB3A)
#define FCH_KEITH_FCHKEITHCORE_KEITH_KTPCIE_KTABMID_FILECODE                                                (0xBB3B)
#define FCH_KEITH_FCHKEITHCORE_KEITH_KTPCIE_KTABRESET_FILECODE                                              (0xBB3C)
#define FCH_KEITH_FCHKEITHCORE_KEITH_KTPCIE_KTABRESETSERVICE_FILECODE                                       (0xBB3D)
#define FCH_KEITH_FCHKEITHCORE_KEITH_KTPCIE_KTABSERVICE_FILECODE                                            (0xBB3E)
#define FCH_KEITH_FCHKEITHCORE_KEITH_KTSATA_KTAHCIENV_FILECODE                                              (0xBB3F)
#define FCH_KEITH_FCHKEITHCORE_KEITH_KTSATA_KTAHCILATE_FILECODE                                             (0xBB40)
#define FCH_KEITH_FCHKEITHCORE_KEITH_KTSATA_KTAHCILIB_FILECODE                                              (0xBB41)
#define FCH_KEITH_FCHKEITHCORE_KEITH_KTSATA_KTAHCIMID_FILECODE                                              (0xBB42)
#define FCH_KEITH_FCHKEITHCORE_KEITH_KTSATA_KTRAIDENV_FILECODE                                              (0xBB43)
#define FCH_KEITH_FCHKEITHCORE_KEITH_KTSATA_KTRAIDLATE_FILECODE                                             (0xBB44)
#define FCH_KEITH_FCHKEITHCORE_KEITH_KTSATA_KTRAIDLIB_FILECODE                                              (0xBB45)
#define FCH_KEITH_FCHKEITHCORE_KEITH_KTSATA_KTRAIDMID_FILECODE                                              (0xBB46)
#define FCH_KEITH_FCHKEITHCORE_KEITH_KTSATA_KTSATAD3LIB_FILECODE                                            (0xBB47)
#define FCH_KEITH_FCHKEITHCORE_KEITH_KTSATA_KTSATAENV_FILECODE                                              (0xBB48)
#define FCH_KEITH_FCHKEITHCORE_KEITH_KTSATA_KTSATAENVSERVICE_FILECODE                                       (0xBB49)
#define FCH_KEITH_FCHKEITHCORE_KEITH_KTSATA_KTSATALATE_FILECODE                                             (0xBB4A)
#define FCH_KEITH_FCHKEITHCORE_KEITH_KTSATA_KTSATALIB_FILECODE                                              (0xBB4B)
#define FCH_KEITH_FCHKEITHCORE_KEITH_KTSATA_KTSATAMID_FILECODE                                              (0xBB4C)
#define FCH_KEITH_FCHKEITHCORE_KEITH_KTSATA_KTSATARESET_FILECODE                                            (0xBB4D)
#define FCH_KEITH_FCHKEITHCORE_KEITH_KTSATA_KTSATARESETSERVICE_FILECODE                                     (0xBB4E)
#define FCH_KEITH_FCHKEITHCORE_KEITH_KTSATA_KTSATASERVICE_FILECODE                                          (0xBB4F)
#define FCH_KEITH_FCHKEITHCORE_KEITH_KTSD_KTSDENV_FILECODE                                                  (0xBB50)
#define FCH_KEITH_FCHKEITHCORE_KEITH_KTSD_KTSDENVSERVICE_FILECODE                                           (0xBB51)
#define FCH_KEITH_FCHKEITHCORE_KEITH_KTSD_KTSDLATE_FILECODE                                                 (0xBB52)
#define FCH_KEITH_FCHKEITHCORE_KEITH_KTSD_KTSDMID_FILECODE                                                  (0xBB53)
#define FCH_KEITH_FCHKEITHCORE_KEITH_KTSD_KTSDRESETSERVICE_FILECODE                                         (0xBB54)
#define FCH_KEITH_FCHKEITHCORE_KEITH_KTSD_KTSDSERVICE_FILECODE                                              (0xBB55)
#define FCH_KEITH_FCHKEITHCORE_KEITH_KTUSB_KTCIOENV_FILECODE                                                (0xBB56)
#define FCH_KEITH_FCHKEITHCORE_KEITH_KTUSB_KTCIOLATE_FILECODE                                               (0xBB57)
#define FCH_KEITH_FCHKEITHCORE_KEITH_KTUSB_KTCIOMID_FILECODE                                                (0xBB58)
#define FCH_KEITH_FCHKEITHCORE_KEITH_KTUSB_KTCIORESET_FILECODE                                              (0xBB59)
#define FCH_KEITH_FCHKEITHCORE_KEITH_KTUSB_KTCIOSERVICE_FILECODE                                            (0xBB5A)
#define FCH_KEITH_FCHKEITHCORE_KEITH_KTUSB_KTUSBENV_FILECODE                                                (0xBB5B)
#define FCH_KEITH_FCHKEITHCORE_KEITH_KTUSB_KTUSBLATE_FILECODE                                               (0xBB5C)
#define FCH_KEITH_FCHKEITHCORE_KEITH_KTUSB_KTUSBMID_FILECODE                                                (0xBB5D)
#define FCH_KEITH_FCHKEITHCORE_KEITH_KTUSB_KTUSBRESET_FILECODE                                              (0xBB5E)
#define FCH_KEITH_FCHKEITHCORE_KEITH_KTUSB_KTXHCIENV_FILECODE                                               (0xBB5F)
#define FCH_KEITH_FCHKEITHCORE_KEITH_KTUSB_KTXHCILATE_FILECODE                                              (0xBB60)
#define FCH_KEITH_FCHKEITHCORE_KEITH_KTUSB_KTXHCIMID_FILECODE                                               (0xBB61)
#define FCH_KEITH_FCHKEITHCORE_KEITH_KTUSB_KTXHCIRECOVERY_FILECODE                                          (0xBB62)
#define FCH_KEITH_FCHKEITHCORE_KEITH_KTUSB_KTXHCIRESET_FILECODE                                             (0xBB63)
#define FCH_KEITH_FCHKEITHCORE_KEITH_KTUSB_KTXHCIRESETSERVICE_FILECODE                                      (0xBB64)
#define FCH_KEITH_FCHKEITHCORE_KEITH_KTUSB_KTXHCISERVICE_FILECODE                                           (0xBB65)
#define FCH_KEITH_FCHKEITHCORE_KEITH_KTWIFI_KTWIFIENV_FILECODE                                              (0xBB66)
#define FCH_KEITH_FCHKEITHCORE_KEITH_KTWIFI_KTWIFILATE_FILECODE                                             (0xBB67)
#define FCH_KEITH_FCHKEITHCORE_KEITH_KTWIFI_KTWIFIMID_FILECODE                                              (0xBB68)
#define FCH_KEITH_FCHKEITHCORE_KEITH_KTWIFI_KTWIFIRESET_FILECODE                                            (0xBB69)
#define FCH_KEITH_FCHKEITHCORE_KEITH_KTXGBE_KTXGBEENV_FILECODE                                              (0xBB6A)
#define FCH_KEITH_FCHKEITHCORE_KEITH_KTXGBE_KTXGBELATE_FILECODE                                             (0xBB6B)
#define FCH_KEITH_FCHKEITHCORE_KEITH_KTXGBE_KTXGBELIB_FILECODE                                              (0xBB6C)
#define FCH_KEITH_FCHKEITHCORE_KEITH_KTXGBE_KTXGBEMID_FILECODE                                              (0xBB6D)
#define FCH_KEITH_FCHKEITHCORE_KEITH_KTXGBE_KTXGBERESET_FILECODE                                            (0xBB6E)
#define FCH_KEITH_FCHKEITHDXE_FCHDXE_FILECODE                                                               (0xBB6F)
#define FCH_KEITH_FCHKEITHDXE_FCHKEITHDSDT_FILECODE                                                         (0xBB70)
#define FCH_KEITH_FCHKEITHDXE_FCHKEITHSSDT_FILECODE                                                         (0xBB71)
#define FCH_KEITH_FCHKEITHLEGACYINTERRUPTDXE_LEGACYINTERRUPT_FILECODE                                       (0xBB72)
#define FCH_KEITH_FCHKEITHPEI_FCHPEI_FILECODE                                                               (0xBB73)
#define FCH_KEITH_FCHKEITHPEI_FCHRESET_FILECODE                                                             (0xBB74)
#define FCH_KEITH_FCHKEITHPEI_FCHRESET2_FILECODE                                                            (0xBB75)
#define FCH_KEITH_FCHKEITHPEI_FCHSTALL_FILECODE                                                             (0xBB76)
#define FCH_KEITH_FCHKEITHSMBUSDXE_SMBUSLIGHT_FILECODE                                                      (0xBB77)
#define FCH_KEITH_FCHKEITHSMBUSPEI_SMBUS_FILECODE                                                           (0xBB78)
#define FCH_KEITH_FCHKEITHSMM_FCHSMM_FILECODE                                                               (0xBB79)
#define FCH_KEITH_FCHKEITHSMM_GPISMI_FILECODE                                                               (0xBB7A)
#define FCH_KEITH_FCHKEITHSMM_IOTRAPSMI_FILECODE                                                            (0xBB7B)
#define FCH_KEITH_FCHKEITHSMM_MISCSMI_FILECODE                                                              (0xBB7C)
#define FCH_KEITH_FCHKEITHSMM_PERIODICTIMERSMI_FILECODE                                                     (0xBB7D)
#define FCH_KEITH_FCHKEITHSMM_POWERBUTTONSMI_FILECODE                                                       (0xBB7E)
#define FCH_KEITH_FCHKEITHSMM_SWSMI_FILECODE                                                                (0xBB7F)
#define FCH_KEITH_FCHKEITHSMM_SXSMI_FILECODE                                                                (0xBB80)
#define FCH_KEITH_FCHKEITHSMMCONTROLDXE_SMMCONTROL_FILECODE                                                 (0xBB81)
#define FCH_KEITH_FCHKEITHSMMDISPATCHER_FCHSMMDIAGDISPATCHER_FILECODE                                       (0xBB82)
#define FCH_KEITH_FCHKEITHSMMDISPATCHER_FCHSMMDISPATCHER_FILECODE                                           (0xBB83)
#define FCH_KEITH_FCHKEITHSMMDISPATCHER_FCHSMMGPIDISPATCHER_FCHSMMGPIDISPATCHER_FILECODE                    (0xBB84)
#define FCH_KEITH_FCHKEITHSMMDISPATCHER_FCHSMMIOTRAPDISPATCHER_FCHSMMIOTRAPDISPATCHER_FILECODE              (0xBB85)
#define FCH_KEITH_FCHKEITHSMMDISPATCHER_FCHSMMMISCDISPATCHER_FCHSMMMISCDISPATCHER_FILECODE                  (0xBB86)
#define FCH_KEITH_FCHKEITHSMMDISPATCHER_FCHSMMPERIODICALDISPATCHER_FCHSMMPERIODICALDISPATCHER_FILECODE      (0xBB87)
#define FCH_KEITH_FCHKEITHSMMDISPATCHER_FCHSMMPWRBTNDISPATCHER_FCHSMMPWRBTNDISPATCHER_FILECODE              (0xBB88)
#define FCH_KEITH_FCHKEITHSMMDISPATCHER_FCHSMMSWDISPATCHER_FCHSMMSWDISPATCHER_FILECODE                      (0xBB89)
#define FCH_KEITH_FCHKEITHSMMDISPATCHER_FCHSMMSXDISPATCHER_FCHSMMSXDISPATCHER_FILECODE                      (0xBB8A)
#define FCH_KEITH_FCHKEITHSMMDISPATCHER_FCHSMMUSBDISPATCHER_FCHSMMUSBDISPATCHER_FILECODE                    (0xBB8B)
#define FCH_KEITH_FCHKEITHI2CPEI_FCHI2CMASTERPEI_FILECODE                                                   (0xBB8C)
#define FCH_KEITH_FCHKEITHI2CDXE_FCHI2CMASTERDXE_FILECODE                                                   (0xBB8D)

#define LIBRARY_FCHIDSHOOKRMBLIB_PEI_FCHIDSHOOKRMBLIBPEI_FILECODE                                           (0xBB90)
#define LIBRARY_FCHIDSHOOKRMBLIB_DXE_FCHIDSHOOKRMBLIBDXE_FILECODE                                           (0xBB91)

#define FCH_YUNTAI_FCHYUNTAICORE_COMMON_ACPILIB_FILECODE                                                    (0xBD00)
#define FCH_YUNTAI_FCHYUNTAICORE_COMMON_FCHCOMMONSMM_FILECODE                                               (0xBD01)
#define FCH_YUNTAI_FCHYUNTAICORE_COMMON_FCHCOMMON_FILECODE                                                  (0xBD02)
#define FCH_YUNTAI_FCHYUNTAICORE_COMMON_FCHLIB_FILECODE                                                     (0xBD03)
#define FCH_YUNTAI_FCHYUNTAICORE_COMMON_FCHPELIB_FILECODE                                                   (0xBD04)
#define FCH_YUNTAI_FCHYUNTAICORE_COMMON_MEMLIB_FILECODE                                                     (0xBD05)
#define FCH_YUNTAI_FCHYUNTAICORE_COMMON_PCILIB_FILECODE                                                     (0xBD06)
#define FCH_YUNTAI_FCHYUNTAICORE_COMMON_FCHAOACLIB_FILECODE                                                 (0xBD07)
#define FCH_YUNTAI_FCHYUNTAICORE_COMMON_FCHSMNLIB_FILECODE                                                  (0xBD08)
#define FCH_YUNTAI_FCHYUNTAICORE_YUNTAI_YTESPI_YTESPIENV_FILECODE                                           (0xBD09)
#define FCH_YUNTAI_FCHYUNTAICORE_YUNTAI_YTESPI_YTESPILIB_FILECODE                                           (0xBD0A)
#define FCH_YUNTAI_FCHYUNTAICORE_YUNTAI_YTESPI_YTESPILATE_FILECODE                                          (0xBD0B)
#define FCH_YUNTAI_FCHYUNTAICORE_YUNTAI_YTESPI_YTESPIMID_FILECODE                                           (0xBD0C)
#define FCH_YUNTAI_FCHYUNTAICORE_YUNTAI_YTESPI_YTESPIRESET_FILECODE                                         (0xBD0D)
#define FCH_YUNTAI_FCHYUNTAICORE_YUNTAI_YTHWACPI_YTHWACPIENV_FILECODE                                       (0xBD0E)
#define FCH_YUNTAI_FCHYUNTAICORE_YUNTAI_YTHWACPI_YTHWACPIENVSERVICE_FILECODE                                (0xBD0F)
#define FCH_YUNTAI_FCHYUNTAICORE_YUNTAI_YTHWACPI_YTHWACPILATE_FILECODE                                      (0xBD10)
#define FCH_YUNTAI_FCHYUNTAICORE_YUNTAI_YTHWACPI_YTHWACPILATESERVICE_FILECODE                               (0xBD12)
#define FCH_YUNTAI_FCHYUNTAICORE_YUNTAI_YTHWACPI_YTHWACPIMID_FILECODE                                       (0xBD13)
#define FCH_YUNTAI_FCHYUNTAICORE_YUNTAI_YTHWACPI_YTHWACPIMIDSERVICE_FILECODE                                (0xBD14)
#define FCH_YUNTAI_FCHYUNTAICORE_YUNTAI_YTHWACPI_YTHWACPIRESET_FILECODE                                     (0xBD15)
#define FCH_YUNTAI_FCHYUNTAICORE_YUNTAI_YTHWACPI_YTSSSERVICE_FILECODE                                       (0xBD16)
#define FCH_YUNTAI_FCHYUNTAICORE_YUNTAI_YTINTERFACE_YTFCHINITENV_FILECODE                                   (0xBD17)
#define FCH_YUNTAI_FCHYUNTAICORE_YUNTAI_YTINTERFACE_YTFCHINITLATE_FILECODE                                  (0xBD18)
#define FCH_YUNTAI_FCHYUNTAICORE_YUNTAI_YTINTERFACE_YTFCHINITMID_FILECODE                                   (0xBD19)
#define FCH_YUNTAI_FCHYUNTAICORE_YUNTAI_YTINTERFACE_YTFCHINITRESET_FILECODE                                 (0xBD1A)
#define FCH_YUNTAI_FCHYUNTAICORE_YUNTAI_YTINTERFACE_YTFCHINITS3_FILECODE                                    (0xBD1B)
#define FCH_YUNTAI_FCHYUNTAICORE_YUNTAI_YTINTERFACE_YTFCHTASKLAUNCHER_FILECODE                              (0xBD1C)
#define FCH_YUNTAI_FCHYUNTAICORE_YUNTAI_YTLPCSPI_YTLPCENV_FILECODE                                          (0xBD1D)
#define FCH_YUNTAI_FCHYUNTAICORE_YUNTAI_YTLPCSPI_YTLPCENVSERVICE_FILECODE                                   (0xBD1E)
#define FCH_YUNTAI_FCHYUNTAICORE_YUNTAI_YTLPCSPI_YTLPCLATE_FILECODE                                         (0xBD1F)
#define FCH_YUNTAI_FCHYUNTAICORE_YUNTAI_YTLPCSPI_YTLPCMID_FILECODE                                          (0xBD20)
#define FCH_YUNTAI_FCHYUNTAICORE_YUNTAI_YTLPCSPI_YTLPCRESET_FILECODE                                        (0xBD21)
#define FCH_YUNTAI_FCHYUNTAICORE_YUNTAI_YTLPCSPI_YTLPCRESETSERVICE_FILECODE                                 (0xBD22)
#define FCH_YUNTAI_FCHYUNTAICORE_YUNTAI_YTLPCSPI_YTSPIENV_FILECODE                                          (0xBD23)
#define FCH_YUNTAI_FCHYUNTAICORE_YUNTAI_YTLPCSPI_YTSPILATE_FILECODE                                         (0xBD24)
#define FCH_YUNTAI_FCHYUNTAICORE_YUNTAI_YTLPCSPI_YTSPIMID_FILECODE                                          (0xBD25)
#define FCH_YUNTAI_FCHYUNTAICORE_YUNTAI_YTLPCSPI_YTSPIRESET_FILECODE                                        (0xBD26)
#define FCH_YUNTAI_FCHYUNTAICORE_YUNTAI_YTPCIE_YTABENV_FILECODE                                             (0xBD27)
#define FCH_YUNTAI_FCHYUNTAICORE_YUNTAI_YTPCIE_YTABENVSERVICE_FILECODE                                      (0xBD28)
#define FCH_YUNTAI_FCHYUNTAICORE_YUNTAI_YTPCIE_YTABLATE_FILECODE                                            (0xBD29)
#define FCH_YUNTAI_FCHYUNTAICORE_YUNTAI_YTPCIE_YTABMID_FILECODE                                             (0xBD2A)
#define FCH_YUNTAI_FCHYUNTAICORE_YUNTAI_YTPCIE_YTABRESET_FILECODE                                           (0xBD2B)
#define FCH_YUNTAI_FCHYUNTAICORE_YUNTAI_YTPCIE_YTABRESETSERVICE_FILECODE                                    (0xBD2C)
#define FCH_YUNTAI_FCHYUNTAICORE_YUNTAI_YTPCIE_YTABSERVICE_FILECODE                                         (0xBD2D)
#define FCH_YUNTAI_FCHYUNTAICORE_YUNTAI_YTSD_YTSDENV_FILECODE                                               (0xBD2E)
#define FCH_YUNTAI_FCHYUNTAICORE_YUNTAI_YTSD_YTSDENVSERVICE_FILECODE                                        (0xBD2F)
#define FCH_YUNTAI_FCHYUNTAICORE_YUNTAI_YTSD_YTSDLATE_FILECODE                                              (0xBD30)
#define FCH_YUNTAI_FCHYUNTAICORE_YUNTAI_YTSD_YTSDMID_FILECODE                                               (0xBD31)
#define FCH_YUNTAI_FCHYUNTAICORE_YUNTAI_YTSD_YTSDRESETSERVICE_FILECODE                                      (0xBD32)
#define FCH_YUNTAI_FCHYUNTAICORE_YUNTAI_YTSD_YTSDSERVICE_FILECODE                                           (0xBD33)
#define FCH_YUNTAI_FCHYUNTAICORE_YUNTAI_YTUSB_YTCIOENV_FILECODE                                             (0xBD34)
#define FCH_YUNTAI_FCHYUNTAICORE_YUNTAI_YTUSB_YTCIOLATE_FILECODE                                            (0xBD35)
#define FCH_YUNTAI_FCHYUNTAICORE_YUNTAI_YTUSB_YTCIOMID_FILECODE                                             (0xBD36)
#define FCH_YUNTAI_FCHYUNTAICORE_YUNTAI_YTUSB_YTCIORESET_FILECODE                                           (0xBD37)
#define FCH_YUNTAI_FCHYUNTAICORE_YUNTAI_YTUSB_YTCIOSERVICE_FILECODE                                         (0xBD38)
#define FCH_YUNTAI_FCHYUNTAICORE_YUNTAI_YTUSB_YTUSBENV_FILECODE                                             (0xBD39)
#define FCH_YUNTAI_FCHYUNTAICORE_YUNTAI_YTUSB_YTUSBLATE_FILECODE                                            (0xBD3A)
#define FCH_YUNTAI_FCHYUNTAICORE_YUNTAI_YTUSB_YTUSBMID_FILECODE                                             (0xBD3B)
#define FCH_YUNTAI_FCHYUNTAICORE_YUNTAI_YTUSB_YTUSBRESET_FILECODE                                           (0xBD3C)
#define FCH_YUNTAI_FCHYUNTAICORE_YUNTAI_YTUSB_YTXHCIENV_FILECODE                                            (0xBD3D)
#define FCH_YUNTAI_FCHYUNTAICORE_YUNTAI_YTUSB_YTXHCILATE_FILECODE                                           (0xBD3E)
#define FCH_YUNTAI_FCHYUNTAICORE_YUNTAI_YTUSB_YTXHCIMID_FILECODE                                            (0xBD3F)
#define FCH_YUNTAI_FCHYUNTAICORE_YUNTAI_YTUSB_YTXHCIRECOVERY_FILECODE                                       (0xBD40)
#define FCH_YUNTAI_FCHYUNTAICORE_YUNTAI_YTUSB_YTXHCIRESET_FILECODE                                          (0xBD41)
#define FCH_YUNTAI_FCHYUNTAICORE_YUNTAI_YTUSB_YTXHCIRESETSERVICE_FILECODE                                   (0xBD42)
#define FCH_YUNTAI_FCHYUNTAICORE_YUNTAI_YTUSB_YTXHCISERVICE_FILECODE                                        (0xBD43)
#define FCH_YUNTAI_FCHYUNTAICORE_YUNTAI_YTWIFI_YTWIFIENV_FILECODE                                           (0xBD44)
#define FCH_YUNTAI_FCHYUNTAICORE_YUNTAI_YTWIFI_YTWIFILATE_FILECODE                                          (0xBD45)
#define FCH_YUNTAI_FCHYUNTAICORE_YUNTAI_YTWIFI_YTWIFIMID_FILECODE                                           (0xBD46)
#define FCH_YUNTAI_FCHYUNTAICORE_YUNTAI_YTWIFI_YTWIFIRESET_FILECODE                                         (0xBD47)
#define FCH_YUNTAI_FCHYUNTAICF9RESETDXE_CF9RESET_FILECODE                                                   (0xBD48)
#define FCH_YUNTAI_FCHYUNTAIDXE_FCHDXE_FILECODE                                                             (0xBD49)
#define FCH_YUNTAI_FCHYUNTAIDXE_FCHYUNTAIDSDT_FILECODE                                                      (0xBD4A)
#define FCH_YUNTAI_FCHYUNTAIDXE_FCHYUNTAISSDT_FILECODE                                                      (0xBD4B)
#define FCH_YUNTAI_FCHYUNTAILEGACYINTERRUPTDXE_LEGACYINTERRUPT_FILECODE                                     (0xBD4C)
#define FCH_YUNTAI_FCHYUNTAIPEI_FCHPEI_FILECODE                                                             (0xBD4D)
#define FCH_YUNTAI_FCHYUNTAIPEI_FCHRESET_FILECODE                                                           (0xBD4E)
#define FCH_YUNTAI_FCHYUNTAIPEI_FCHRESET2_FILECODE                                                          (0xBD4F)
#define FCH_YUNTAI_FCHYUNTAIPEI_FCHSTALL_FILECODE                                                           (0xBD50)
#define FCH_YUNTAI_FCHYUNTAISMBUSDXE_SMBUSLIGHT_FILECODE                                                    (0xBD51)
#define FCH_YUNTAI_FCHYUNTAISMBUSPEI_SMBUS_FILECODE                                                         (0xBD52)
#define FCH_YUNTAI_FCHYUNTAISMM_FCHSMM_FILECODE                                                             (0xBD53)
#define FCH_YUNTAI_FCHYUNTAISMM_GPISMI_FILECODE                                                             (0xBD54)
#define FCH_YUNTAI_FCHYUNTAISMM_IOTRAPSMI_FILECODE                                                          (0xBD55)
#define FCH_YUNTAI_FCHYUNTAISMM_MISCSMI_FILECODE                                                            (0xBD56)
#define FCH_YUNTAI_FCHYUNTAISMM_PERIODICTIMERSMI_FILECODE                                                   (0xBD57)
#define FCH_YUNTAI_FCHYUNTAISMM_POWERBUTTONSMI_FILECODE                                                     (0xBD58)
#define FCH_YUNTAI_FCHYUNTAISMM_SWSMI_FILECODE                                                              (0xBD59)
#define FCH_YUNTAI_FCHYUNTAISMM_SXSMI_FILECODE                                                              (0xBD5A)
#define FCH_YUNTAI_FCHYUNTAISMMCONTROLDXE_SMMCONTROL_FILECODE                                               (0xBD5B)
#define FCH_YUNTAI_FCHYUNTAISMMDISPATCHER_FCHSMMDATA_FILECODE                                               (0xBD5C)
#define FCH_YUNTAI_FCHYUNTAISMMDISPATCHER_FCHSMMDIAGDISPATCHER_FILECODE                                     (0xBD5D)
#define FCH_YUNTAI_FCHYUNTAISMMDISPATCHER_FCHSMMDISPATCHER_FILECODE                                         (0xBD5E)
#define FCH_YUNTAI_FCHYUNTAISMMDISPATCHER_FCHSMMGPIDISPATCHER_FCHSMMGPIDISPATCHER_FILECODE                  (0xBD5F)
#define FCH_YUNTAI_FCHYUNTAISMMDISPATCHER_FCHSMMIOTRAPDISPATCHER_FCHSMMIOTRAPDISPATCHER_FILECODE            (0xBD60)
#define FCH_YUNTAI_FCHYUNTAISMMDISPATCHER_FCHSMMMISCDISPATCHER_FCHSMMMISCDISPATCHER_FILECODE                (0xBD61)
#define FCH_YUNTAI_FCHYUNTAISMMDISPATCHER_FCHSMMPERIODICALDISPATCHER_FCHSMMPERIODICALDISPATCHER_FILECODE    (0xBD62)
#define FCH_YUNTAI_FCHYUNTAISMMDISPATCHER_FCHSMMPWRBTNDISPATCHER_FCHSMMPWRBTNDISPATCHER_FILECODE            (0xBD63)
#define FCH_YUNTAI_FCHYUNTAISMMDISPATCHER_FCHSMMSWDISPATCHER_FCHSMMSWDISPATCHER_FILECODE                    (0xBD64)
#define FCH_YUNTAI_FCHYUNTAISMMDISPATCHER_FCHSMMSXDISPATCHER_FCHSMMSXDISPATCHER_FILECODE                    (0xBD65)
#define FCH_YUNTAI_FCHYUNTAISMMDISPATCHER_FCHSMMUSBDISPATCHER_FCHSMMUSBDISPATCHER_FILECODE                  (0xBD66)
#define LIBRARY_FCHIDSHOOKRPLLIB_PEI_FCHIDSHOOKRPLLIBPEI_FILECODE                                           (0xBD67)
#define LIBRARY_FCHIDSHOOKRPLLIB_DXE_FCHIDSHOOKRPLLIBDXE_FILECODE                                           (0xBD68)

#define FCH_TACOMA_FCHTACOMACF9RESETDXE_CF9RESET_FILECODE                                                   (0xB710)
#define FCH_TACOMA_FCHTACOMACORE_COMMON_ACPILIB_FILECODE                                                    (0xB711)
#define FCH_TACOMA_FCHTACOMACORE_COMMON_FCHAOACLIB_FILECODE                                                 (0xB712)
#define FCH_TACOMA_FCHTACOMACORE_COMMON_FCHCOMMON_FILECODE                                                  (0xB713)
#define FCH_TACOMA_FCHTACOMACORE_COMMON_FCHCOMMONSMM_FILECODE                                               (0xB714)
#define FCH_TACOMA_FCHTACOMACORE_COMMON_FCHLIB_FILECODE                                                     (0xB715)
#define FCH_TACOMA_FCHTACOMACORE_COMMON_FCHPELIB_FILECODE                                                   (0xB716)
#define FCH_TACOMA_FCHTACOMACORE_COMMON_FCHSMNLIB_FILECODE                                                  (0xB717)
#define FCH_TACOMA_FCHTACOMACORE_COMMON_MEMLIB_FILECODE                                                     (0xB718)
#define FCH_TACOMA_FCHTACOMACORE_COMMON_PCILIB_FILECODE                                                     (0xB719)
#define FCH_TACOMA_FCHTACOMACORE_TACOMA_TCESPI_TCESPIENV_FILECODE                                           (0xB71A)
#define FCH_TACOMA_FCHTACOMACORE_TACOMA_TCESPI_TCESPILATE_FILECODE                                          (0xB71B)
#define FCH_TACOMA_FCHTACOMACORE_TACOMA_TCESPI_TCESPILIB_FILECODE                                           (0xB71C)
#define FCH_TACOMA_FCHTACOMACORE_TACOMA_TCESPI_TCESPIMID_FILECODE                                           (0xB71D)
#define FCH_TACOMA_FCHTACOMACORE_TACOMA_TCESPI_TCESPIRESET_FILECODE                                         (0xB71E)
#define FCH_TACOMA_FCHTACOMACORE_TACOMA_TCHWACPI_TCHWACPIENV_FILECODE                                       (0xB71F)
#define FCH_TACOMA_FCHTACOMACORE_TACOMA_TCHWACPI_TCHWACPIENVSERVICE_FILECODE                                (0xB720)
#define FCH_TACOMA_FCHTACOMACORE_TACOMA_TCHWACPI_TCHWACPILATE_FILECODE                                      (0xB721)
#define FCH_TACOMA_FCHTACOMACORE_TACOMA_TCHWACPI_TCHWACPILATESERVICE_FILECODE                               (0xB722)
#define FCH_TACOMA_FCHTACOMACORE_TACOMA_TCHWACPI_TCHWACPIMID_FILECODE                                       (0xB723)
#define FCH_TACOMA_FCHTACOMACORE_TACOMA_TCHWACPI_TCHWACPIMIDSERVICE_FILECODE                                (0xB724)
#define FCH_TACOMA_FCHTACOMACORE_TACOMA_TCHWACPI_TCHWACPIRESET_FILECODE                                     (0xB725)
#define FCH_TACOMA_FCHTACOMACORE_TACOMA_TCHWACPI_TCSSSERVICE_FILECODE                                       (0xB726)
#define FCH_TACOMA_FCHTACOMACORE_TACOMA_TCINTERFACE_TCFCHINITENV_FILECODE                                   (0xB727)
#define FCH_TACOMA_FCHTACOMACORE_TACOMA_TCINTERFACE_TCFCHINITLATE_FILECODE                                  (0xB728)
#define FCH_TACOMA_FCHTACOMACORE_TACOMA_TCINTERFACE_TCFCHINITMID_FILECODE                                   (0xB729)
#define FCH_TACOMA_FCHTACOMACORE_TACOMA_TCINTERFACE_TCFCHINITRESET_FILECODE                                 (0xB72A)
#define FCH_TACOMA_FCHTACOMACORE_TACOMA_TCINTERFACE_TCFCHINITS3_FILECODE                                    (0xB72B)
#define FCH_TACOMA_FCHTACOMACORE_TACOMA_TCINTERFACE_TCFCHTASKLAUNCHER_FILECODE                              (0xB72C)
#define FCH_TACOMA_FCHTACOMACORE_TACOMA_TCLPCSPI_TCLPCENV_FILECODE                                          (0xB72D)
#define FCH_TACOMA_FCHTACOMACORE_TACOMA_TCLPCSPI_TCLPCENVSERVICE_FILECODE                                   (0xB72E)
#define FCH_TACOMA_FCHTACOMACORE_TACOMA_TCLPCSPI_TCLPCLATE_FILECODE                                         (0xB72F)
#define FCH_TACOMA_FCHTACOMACORE_TACOMA_TCLPCSPI_TCLPCMID_FILECODE                                          (0xB730)
#define FCH_TACOMA_FCHTACOMACORE_TACOMA_TCLPCSPI_TCLPCRESET_FILECODE                                        (0xB731)
#define FCH_TACOMA_FCHTACOMACORE_TACOMA_TCLPCSPI_TCLPCRESETSERVICE_FILECODE                                 (0xB732)
#define FCH_TACOMA_FCHTACOMACORE_TACOMA_TCLPCSPI_TCSPIENV_FILECODE                                          (0xB733)
#define FCH_TACOMA_FCHTACOMACORE_TACOMA_TCLPCSPI_TCSPILATE_FILECODE                                         (0xB734)
#define FCH_TACOMA_FCHTACOMACORE_TACOMA_TCLPCSPI_TCSPIMID_FILECODE                                          (0xB735)
#define FCH_TACOMA_FCHTACOMACORE_TACOMA_TCLPCSPI_TCSPIRESET_FILECODE                                        (0xB736)
#define FCH_TACOMA_FCHTACOMACORE_TACOMA_TCPCIE_TCABENV_FILECODE                                             (0xB737)
#define FCH_TACOMA_FCHTACOMACORE_TACOMA_TCPCIE_TCABENVSERVICE_FILECODE                                      (0xB738)
#define FCH_TACOMA_FCHTACOMACORE_TACOMA_TCPCIE_TCABLATE_FILECODE                                            (0xB739)
#define FCH_TACOMA_FCHTACOMACORE_TACOMA_TCPCIE_TCABMID_FILECODE                                             (0xB73A)
#define FCH_TACOMA_FCHTACOMACORE_TACOMA_TCPCIE_TCABRESET_FILECODE                                           (0xB73B)
#define FCH_TACOMA_FCHTACOMACORE_TACOMA_TCPCIE_TCABRESETSERVICE_FILECODE                                    (0xB73C)
#define FCH_TACOMA_FCHTACOMACORE_TACOMA_TCPCIE_TCABSERVICE_FILECODE                                         (0xB73D)
#define FCH_TACOMA_FCHTACOMACORE_TACOMA_TCSD_TCSDENV_FILECODE                                               (0xB74E)
#define FCH_TACOMA_FCHTACOMACORE_TACOMA_TCSD_TCSDENVSERVICE_FILECODE                                        (0xB74F)
#define FCH_TACOMA_FCHTACOMACORE_TACOMA_TCSD_TCSDLATE_FILECODE                                              (0xB750)
#define FCH_TACOMA_FCHTACOMACORE_TACOMA_TCSD_TCSDMID_FILECODE                                               (0xB751)
#define FCH_TACOMA_FCHTACOMACORE_TACOMA_TCSD_TCSDRESETSERVICE_FILECODE                                      (0xB752)
#define FCH_TACOMA_FCHTACOMACORE_TACOMA_TCSD_TCSDSERVICE_FILECODE                                           (0xB753)
#define FCH_TACOMA_FCHTACOMACORE_TACOMA_TCUSB_TCCIOENV_FILECODE                                             (0xB754)
#define FCH_TACOMA_FCHTACOMACORE_TACOMA_TCUSB_TCCIOLATE_FILECODE                                            (0xB755)
#define FCH_TACOMA_FCHTACOMACORE_TACOMA_TCUSB_TCCIOMID_FILECODE                                             (0xB756)
#define FCH_TACOMA_FCHTACOMACORE_TACOMA_TCUSB_TCCIORESET_FILECODE                                           (0xB757)
#define FCH_TACOMA_FCHTACOMACORE_TACOMA_TCUSB_TCCIOSERVICE_FILECODE                                         (0xB758)
#define FCH_TACOMA_FCHTACOMACORE_TACOMA_TCUSB_TCUSBENV_FILECODE                                             (0xB759)
#define FCH_TACOMA_FCHTACOMACORE_TACOMA_TCUSB_TCUSBLATE_FILECODE                                            (0xB75A)
#define FCH_TACOMA_FCHTACOMACORE_TACOMA_TCUSB_TCUSBMID_FILECODE                                             (0xB75B)
#define FCH_TACOMA_FCHTACOMACORE_TACOMA_TCUSB_TCUSBRESET_FILECODE                                           (0xB75C)
#define FCH_TACOMA_FCHTACOMACORE_TACOMA_TCUSB_TCXHCIENV_FILECODE                                            (0xB75D)
#define FCH_TACOMA_FCHTACOMACORE_TACOMA_TCUSB_TCXHCILATE_FILECODE                                           (0xB75E)
#define FCH_TACOMA_FCHTACOMACORE_TACOMA_TCUSB_TCXHCIMID_FILECODE                                            (0xB75F)
#define FCH_TACOMA_FCHTACOMACORE_TACOMA_TCUSB_TCXHCIRECOVERY_FILECODE                                       (0xB760)
#define FCH_TACOMA_FCHTACOMACORE_TACOMA_TCUSB_TCXHCIRESET_FILECODE                                          (0xB761)
#define FCH_TACOMA_FCHTACOMACORE_TACOMA_TCUSB_TCXHCIRESETSERVICE_FILECODE                                   (0xB762)
#define FCH_TACOMA_FCHTACOMACORE_TACOMA_TCUSB_TCXHCISERVICE_FILECODE                                        (0xB763)
#define FCH_TACOMA_FCHTACOMACORE_TACOMA_TCWIFI_TCWIFIENV_FILECODE                                           (0xB764)
#define FCH_TACOMA_FCHTACOMACORE_TACOMA_TCWIFI_TCWIFILATE_FILECODE                                          (0xB765)
#define FCH_TACOMA_FCHTACOMACORE_TACOMA_TCWIFI_TCWIFIMID_FILECODE                                           (0xB766)
#define FCH_TACOMA_FCHTACOMACORE_TACOMA_TCWIFI_TCWIFIRESET_FILECODE                                         (0xB767)
#define FCH_TACOMA_FCHTACOMADXE_FCHDXE_FILECODE                                                             (0xB768)
#define FCH_TACOMA_FCHTACOMADXE_FCHTACOMADSDT_FILECODE                                                      (0xB769)
#define FCH_TACOMA_FCHTACOMADXE_FCHTACOMASSDT_FILECODE                                                      (0xB76A)
#define FCH_TACOMA_FCHTACOMALEGACYINTERRUPTDXE_LEGACYINTERRUPT_FILECODE                                     (0xB76B)
#define FCH_TACOMA_FCHTACOMAPEI_FCHPEI_FILECODE                                                             (0xB76C)
#define FCH_TACOMA_FCHTACOMAPEI_FCHRESET_FILECODE                                                           (0xB76D)
#define FCH_TACOMA_FCHTACOMAPEI_FCHRESET2_FILECODE                                                          (0xB76E)
#define FCH_TACOMA_FCHTACOMAPEI_FCHSTALL_FILECODE                                                           (0xB76F)
#define FCH_TACOMA_FCHTACOMASMBUSDXE_SMBUSLIGHT_FILECODE                                                    (0xB770)
#define FCH_TACOMA_FCHTACOMASMBUSPEI_SMBUS_FILECODE                                                         (0xB771)
#define FCH_TACOMA_FCHTACOMASMM_FCHSMM_FILECODE                                                             (0xB772)
#define FCH_TACOMA_FCHTACOMASMM_GPISMI_FILECODE                                                             (0xB773)
#define FCH_TACOMA_FCHTACOMASMM_IOTRAPSMI_FILECODE                                                          (0xB774)
#define FCH_TACOMA_FCHTACOMASMM_MISCSMI_FILECODE                                                            (0xB775)
#define FCH_TACOMA_FCHTACOMASMM_PERIODICTIMERSMI_FILECODE                                                   (0xB776)
#define FCH_TACOMA_FCHTACOMASMM_POWERBUTTONSMI_FILECODE                                                     (0xB777)
#define FCH_TACOMA_FCHTACOMASMM_SWSMI_FILECODE                                                              (0xB778)
#define FCH_TACOMA_FCHTACOMASMM_SXSMI_FILECODE                                                              (0xB779)
#define FCH_TACOMA_FCHTACOMASMMCONTROLDXE_SMMCONTROL_FILECODE                                               (0xB77A)
#define FCH_TACOMA_FCHTACOMASMMDISPATCHER_FCHSMMDIAGDISPATCHER_FILECODE                                     (0xB77B)
#define FCH_TACOMA_FCHTACOMASMMDISPATCHER_FCHSMMDISPATCHER_FILECODE                                         (0xB77C)
#define FCH_TACOMA_FCHTACOMASMMDISPATCHER_FCHSMMGPIDISPATCHER_FCHSMMGPIDISPATCHER_FILECODE                  (0xB77D)
#define FCH_TACOMA_FCHTACOMASMMDISPATCHER_FCHSMMIOTRAPDISPATCHER_FCHSMMIOTRAPDISPATCHER_FILECODE            (0xB77E)
#define FCH_TACOMA_FCHTACOMASMMDISPATCHER_FCHSMMMISCDISPATCHER_FCHSMMMISCDISPATCHER_FILECODE                (0xB77F)
#define FCH_TACOMA_FCHTACOMASMMDISPATCHER_FCHSMMPERIODICALDISPATCHER_FCHSMMPERIODICALDISPATCHER_FILECODE    (0xB780)
#define FCH_TACOMA_FCHTACOMASMMDISPATCHER_FCHSMMPWRBTNDISPATCHER_FCHSMMPWRBTNDISPATCHER_FILECODE            (0xB782)
#define FCH_TACOMA_FCHTACOMASMMDISPATCHER_FCHSMMSWDISPATCHER_FCHSMMSWDISPATCHER_FILECODE                    (0xB783)
#define FCH_TACOMA_FCHTACOMASMMDISPATCHER_FCHSMMSXDISPATCHER_FCHSMMSXDISPATCHER_FILECODE                    (0xB784)
#define FCH_TACOMA_FCHTACOMASMMDISPATCHER_FCHSMMUSBDISPATCHER_FCHSMMUSBDISPATCHER_FILECODE                  (0xB785)

#define LIBRARY_FCHIDSHOOKPHXLIB_PEI_FCHIDSHOOKPHXLIBPEI_FILECODE                                           (0xB790)
#define LIBRARY_FCHIDSHOOKPHXLIB_DXE_FCHIDSHOOKPHXLIBDXE_FILECODE                                           (0xB791)

#define FCH_KEITHMDN_FCHKEITHMDNCORE_COMMON_ACPILIB_FILECODE                                                  (0xBE11)
#define FCH_KEITHMDN_FCHKEITHMDNCORE_COMMON_FCHCOMMONSMM_FILECODE                                             (0xBE12)
#define FCH_KEITHMDN_FCHKEITHMDNCORE_COMMON_FCHCOMMON_FILECODE                                                (0xBE13)
#define FCH_KEITHMDN_FCHKEITHMDNCORE_COMMON_FCHLIB_FILECODE                                                   (0xBE14)
#define FCH_KEITHMDN_FCHKEITHMDNCORE_COMMON_FCHPELIB_FILECODE                                                 (0xBE15)
#define FCH_KEITHMDN_FCHKEITHMDNCORE_COMMON_MEMLIB_FILECODE                                                   (0xBE16)
#define FCH_KEITHMDN_FCHKEITHMDNCORE_COMMON_PCILIB_FILECODE                                                   (0xBE17)
#define FCH_KEITHMDN_FCHKEITHMDNCORE_COMMON_FCHAOACLIB_FILECODE                                               (0xBE18)
#define FCH_KEITHMDN_FCHKEITHMDNCORE_COMMON_FCHSMNLIB_FILECODE                                                (0xBE19)
#define FCH_KEITHMDN_FCHKEITHMDNCORE_KEITHMDN_KMESPI_KMESPILIB_FILECODE                                       (0xBE1A)
#define FCH_KEITHMDN_FCHKEITHMDNCF9RESETDXE_CF9RESET_FILECODE                                                 (0xBE1B)
#define FCH_KEITHMDN_FCHKEITHMDNCOMPLEMENT_FCHKTSATAD3COLD_FCHKTSATAD3COLDSMM_FILECODE                        (0xBE1C)
#define FCH_KEITHMDN_FCHKEITHMDNCORE_KEITHMDN_KMESPI_KMESPIENV_FILECODE                                       (0xBE1D)
#define FCH_KEITHMDN_FCHKEITHMDNCORE_KEITHMDN_KMESPI_KMESPILATE_FILECODE                                      (0xBE1E)
#define FCH_KEITHMDN_FCHKEITHMDNCORE_KEITHMDN_KMESPI_KMESPIMID_FILECODE                                       (0xBE1F)
#define FCH_KEITHMDN_FCHKEITHMDNCORE_KEITHMDN_KMESPI_KMESPIRESET_FILECODE                                     (0xBE20)
#define FCH_KEITHMDN_FCHKEITHMDNCORE_KEITHMDN_KMHWACPI_KMHWACPIENV_FILECODE                                   (0xBE21)
#define FCH_KEITHMDN_FCHKEITHMDNCORE_KEITHMDN_KMHWACPI_KMHWACPIENVSERVICE_FILECODE                            (0xBE22)
#define FCH_KEITHMDN_FCHKEITHMDNCORE_KEITHMDN_KMHWACPI_KMHWACPILATE_FILECODE                                  (0xBE23)
#define FCH_KEITHMDN_FCHKEITHMDNCORE_KEITHMDN_KMHWACPI_KMHWACPILATESERVICE_FILECODE                           (0xBE24)
#define FCH_KEITHMDN_FCHKEITHMDNCORE_KEITHMDN_KMHWACPI_KMHWACPIMID_FILECODE                                   (0xBE25)
#define FCH_KEITHMDN_FCHKEITHMDNCORE_KEITHMDN_KMHWACPI_KMHWACPIMIDSERVICE_FILECODE                            (0xBE26)
#define FCH_KEITHMDN_FCHKEITHMDNCORE_KEITHMDN_KMHWACPI_KMHWACPIRESET_FILECODE                                 (0xBE27)
#define FCH_KEITHMDN_FCHKEITHMDNCORE_KEITHMDN_KMHWACPI_KMSSSERVICE_FILECODE                                   (0xBE28)
#define FCH_KEITHMDN_FCHKEITHMDNCORE_KEITHMDN_KMINTERFACE_KMFCHINITENV_FILECODE                               (0xBE29)
#define FCH_KEITHMDN_FCHKEITHMDNCORE_KEITHMDN_KMINTERFACE_KMFCHINITLATE_FILECODE                              (0xBE2A)
#define FCH_KEITHMDN_FCHKEITHMDNCORE_KEITHMDN_KMINTERFACE_KMFCHINITMID_FILECODE                               (0xBE2B)
#define FCH_KEITHMDN_FCHKEITHMDNCORE_KEITHMDN_KMINTERFACE_KMFCHINITRESET_FILECODE                             (0xBE2C)
#define FCH_KEITHMDN_FCHKEITHMDNCORE_KEITHMDN_KMINTERFACE_KMFCHINITS3_FILECODE                                (0xBE2D)
#define FCH_KEITHMDN_FCHKEITHMDNCORE_KEITHMDN_KMINTERFACE_KMFCHTASKLAUNCHER_FILECODE                          (0xBE2E)
#define FCH_KEITHMDN_FCHKEITHMDNCORE_KEITHMDN_KMLPCSPI_KMLPCENV_FILECODE                                      (0xBE2F)
#define FCH_KEITHMDN_FCHKEITHMDNCORE_KEITHMDN_KMLPCSPI_KMLPCENVSERVICE_FILECODE                               (0xBE30)
#define FCH_KEITHMDN_FCHKEITHMDNCORE_KEITHMDN_KMLPCSPI_KMLPCLATE_FILECODE                                     (0xBE31)
#define FCH_KEITHMDN_FCHKEITHMDNCORE_KEITHMDN_KMLPCSPI_KMLPCMID_FILECODE                                      (0xBE32)
#define FCH_KEITHMDN_FCHKEITHMDNCORE_KEITHMDN_KMLPCSPI_KMLPCRESET_FILECODE                                    (0xBE33)
#define FCH_KEITHMDN_FCHKEITHMDNCORE_KEITHMDN_KMLPCSPI_KMLPCRESETSERVICE_FILECODE                             (0xBE34)
#define FCH_KEITHMDN_FCHKEITHMDNCORE_KEITHMDN_KMLPCSPI_KMSPIENV_FILECODE                                      (0xBE35)
#define FCH_KEITHMDN_FCHKEITHMDNCORE_KEITHMDN_KMLPCSPI_KMSPILATE_FILECODE                                     (0xBE36)
#define FCH_KEITHMDN_FCHKEITHMDNCORE_KEITHMDN_KMLPCSPI_KMSPIMID_FILECODE                                      (0xBE37)
#define FCH_KEITHMDN_FCHKEITHMDNCORE_KEITHMDN_KMPCIE_KMABENV_FILECODE                                         (0xBE38)
#define FCH_KEITHMDN_FCHKEITHMDNCORE_KEITHMDN_KMPCIE_KMABENVSERVICE_FILECODE                                  (0xBE39)
#define FCH_KEITHMDN_FCHKEITHMDNCORE_KEITHMDN_KMPCIE_KMABLATE_FILECODE                                        (0xBE3A)
#define FCH_KEITHMDN_FCHKEITHMDNCORE_KEITHMDN_KMPCIE_KMABMID_FILECODE                                         (0xBE3B)
#define FCH_KEITHMDN_FCHKEITHMDNCORE_KEITHMDN_KMPCIE_KMABRESET_FILECODE                                       (0xBE3C)
#define FCH_KEITHMDN_FCHKEITHMDNCORE_KEITHMDN_KMPCIE_KMABRESETSERVICE_FILECODE                                (0xBE3D)
#define FCH_KEITHMDN_FCHKEITHMDNCORE_KEITHMDN_KMPCIE_KMABSERVICE_FILECODE                                     (0xBE3E)
#define FCH_KEITHMDN_FCHKEITHMDNCORE_KEITHMDN_KMSATA_KMAHCIENV_FILECODE                                       (0xBE3F)
#define FCH_KEITHMDN_FCHKEITHMDNCORE_KEITHMDN_KMSATA_KMAHCILATE_FILECODE                                      (0xBE40)
#define FCH_KEITHMDN_FCHKEITHMDNCORE_KEITHMDN_KMSATA_KMAHCILIB_FILECODE                                       (0xBE41)
#define FCH_KEITHMDN_FCHKEITHMDNCORE_KEITHMDN_KMSATA_KMAHCIMID_FILECODE                                       (0xBE42)
#define FCH_KEITHMDN_FCHKEITHMDNCORE_KEITHMDN_KMSATA_KMRAIDENV_FILECODE                                       (0xBE43)
#define FCH_KEITHMDN_FCHKEITHMDNCORE_KEITHMDN_KMSATA_KMRAIDLATE_FILECODE                                      (0xBE44)
#define FCH_KEITHMDN_FCHKEITHMDNCORE_KEITHMDN_KMSATA_KMRAIDLIB_FILECODE                                       (0xBE45)
#define FCH_KEITHMDN_FCHKEITHMDNCORE_KEITHMDN_KMSATA_KMRAIDMID_FILECODE                                       (0xBE46)
#define FCH_KEITHMDN_FCHKEITHMDNCORE_KEITHMDN_KMSATA_KMSATAD3LIB_FILECODE                                     (0xBE47)
#define FCH_KEITHMDN_FCHKEITHMDNCORE_KEITHMDN_KMSATA_KMSATAENV_FILECODE                                       (0xBE48)
#define FCH_KEITHMDN_FCHKEITHMDNCORE_KEITHMDN_KMSATA_KMSATAENVSERVICE_FILECODE                                (0xBE49)
#define FCH_KEITHMDN_FCHKEITHMDNCORE_KEITHMDN_KMSATA_KMSATALATE_FILECODE                                      (0xBE4A)
#define FCH_KEITHMDN_FCHKEITHMDNCORE_KEITHMDN_KMSATA_KMSATALIB_FILECODE                                       (0xBE4B)
#define FCH_KEITHMDN_FCHKEITHMDNCORE_KEITHMDN_KMSATA_KMSATAMID_FILECODE                                       (0xBE4C)
#define FCH_KEITHMDN_FCHKEITHMDNCORE_KEITHMDN_KMSATA_KMSATARESET_FILECODE                                     (0xBE4D)
#define FCH_KEITHMDN_FCHKEITHMDNCORE_KEITHMDN_KMSATA_KMSATARESETSERVICE_FILECODE                              (0xBE4E)
#define FCH_KEITHMDN_FCHKEITHMDNCORE_KEITHMDN_KMSATA_KMSATASERVICE_FILECODE                                   (0xBE4F)
#define FCH_KEITHMDN_FCHKEITHMDNCORE_KEITHMDN_KMSD_KMSDENV_FILECODE                                           (0xBE50)
#define FCH_KEITHMDN_FCHKEITHMDNCORE_KEITHMDN_KMSD_KMSDENVSERVICE_FILECODE                                    (0xBE51)
#define FCH_KEITHMDN_FCHKEITHMDNCORE_KEITHMDN_KMSD_KMSDLATE_FILECODE                                          (0xBE52)
#define FCH_KEITHMDN_FCHKEITHMDNCORE_KEITHMDN_KMSD_KMSDMID_FILECODE                                           (0xBE53)
#define FCH_KEITHMDN_FCHKEITHMDNCORE_KEITHMDN_KMSD_KMSDRESETSERVICE_FILECODE                                  (0xBE54)
#define FCH_KEITHMDN_FCHKEITHMDNCORE_KEITHMDN_KMSD_KMSDSERVICE_FILECODE                                       (0xBE55)
#define FCH_KEITHMDN_FCHKEITHMDNCORE_KEITHMDN_KMUSB_KMCIOENV_FILECODE                                         (0xBE56)
#define FCH_KEITHMDN_FCHKEITHMDNCORE_KEITHMDN_KMUSB_KMCIOLATE_FILECODE                                        (0xBE57)
#define FCH_KEITHMDN_FCHKEITHMDNCORE_KEITHMDN_KMUSB_KMCIOMID_FILECODE                                         (0xBE58)
#define FCH_KEITHMDN_FCHKEITHMDNCORE_KEITHMDN_KMUSB_KMCIORESET_FILECODE                                       (0xBE59)
#define FCH_KEITHMDN_FCHKEITHMDNCORE_KEITHMDN_KMUSB_KMCIOSERVICE_FILECODE                                     (0xBE5A)
#define FCH_KEITHMDN_FCHKEITHMDNCORE_KEITHMDN_KMUSB_KMUSBENV_FILECODE                                         (0xBE5B)
#define FCH_KEITHMDN_FCHKEITHMDNCORE_KEITHMDN_KMUSB_KMUSBLATE_FILECODE                                        (0xBE5C)
#define FCH_KEITHMDN_FCHKEITHMDNCORE_KEITHMDN_KMUSB_KMUSBMID_FILECODE                                         (0xBE5D)
#define FCH_KEITHMDN_FCHKEITHMDNCORE_KEITHMDN_KMUSB_KMUSBRESET_FILECODE                                       (0xBE5E)
#define FCH_KEITHMDN_FCHKEITHMDNCORE_KEITHMDN_KMUSB_KMXHCIENV_FILECODE                                        (0xBE5F)
#define FCH_KEITHMDN_FCHKEITHMDNCORE_KEITHMDN_KMUSB_KMXHCILATE_FILECODE                                       (0xBE60)
#define FCH_KEITHMDN_FCHKEITHMDNCORE_KEITHMDN_KMUSB_KMXHCIMID_FILECODE                                        (0xBE61)
#define FCH_KEITHMDN_FCHKEITHMDNCORE_KEITHMDN_KMUSB_KMXHCIRECOVERY_FILECODE                                   (0xBE62)
#define FCH_KEITHMDN_FCHKEITHMDNCORE_KEITHMDN_KMUSB_KMXHCIRESET_FILECODE                                      (0xBE63)
#define FCH_KEITHMDN_FCHKEITHMDNCORE_KEITHMDN_KMUSB_KMXHCIRESETSERVICE_FILECODE                               (0xBE64)
#define FCH_KEITHMDN_FCHKEITHMDNCORE_KEITHMDN_KMUSB_KMXHCISERVICE_FILECODE                                    (0xBE65)
#define FCH_KEITHMDN_FCHKEITHMDNCORE_KEITHMDN_KMWIFI_KMWIFIENV_FILECODE                                       (0xBE66)
#define FCH_KEITHMDN_FCHKEITHMDNCORE_KEITHMDN_KMWIFI_KMWIFILATE_FILECODE                                      (0xBE67)
#define FCH_KEITHMDN_FCHKEITHMDNCORE_KEITHMDN_KMWIFI_KMWIFIMID_FILECODE                                       (0xBE68)
#define FCH_KEITHMDN_FCHKEITHMDNCORE_KEITHMDN_KMWIFI_KMWIFIRESET_FILECODE                                     (0xBE69)
#define FCH_KEITHMDN_FCHKEITHMDNCORE_KEITHMDN_KMXGBE_KMXGBEENV_FILECODE                                       (0xBE6A)
#define FCH_KEITHMDN_FCHKEITHMDNCORE_KEITHMDN_KMXGBE_KMXGBELATE_FILECODE                                      (0xBE6B)
#define FCH_KEITHMDN_FCHKEITHMDNCORE_KEITHMDN_KMXGBE_KMXGBELIB_FILECODE                                       (0xBE6C)
#define FCH_KEITHMDN_FCHKEITHMDNCORE_KEITHMDN_KMXGBE_KMXGBEMID_FILECODE                                       (0xBE6D)
#define FCH_KEITHMDN_FCHKEITHMDNCORE_KEITHMDN_KMXGBE_KMXGBERESET_FILECODE                                     (0xBE6E)
#define FCH_KEITHMDN_FCHKEITHMDNDXE_FCHDXE_FILECODE                                                           (0xBE6F)
#define FCH_KEITHMDN_FCHKEITHMDNDXE_FCHKEITHMDNDSDT_FILECODE                                                  (0xBE70)
#define FCH_KEITHMDN_FCHKEITHMDNDXE_FCHKEITHMDNSSDT_FILECODE                                                  (0xBE71)
#define FCH_KEITHMDN_FCHKEITHMDNLEGACYINTERRUPTDXE_LEGACYINTERRUPT_FILECODE                                   (0xBE72)
#define FCH_KEITHMDN_FCHKEITHMDNPEI_FCHPEI_FILECODE                                                           (0xBE73)
#define FCH_KEITHMDN_FCHKEITHMDNPEI_FCHRESET_FILECODE                                                         (0xBE74)
#define FCH_KEITHMDN_FCHKEITHMDNPEI_FCHRESET2_FILECODE                                                        (0xBE75)
#define FCH_KEITHMDN_FCHKEITHMDNPEI_FCHSTALL_FILECODE                                                         (0xBE76)
#define FCH_KEITHMDN_FCHKEITHMDNSMBUSDXE_SMBUSLIGHT_FILECODE                                                  (0xBE77)
#define FCH_KEITHMDN_FCHKEITHMDNSMBUSPEI_SMBUS_FILECODE                                                       (0xBE78)
#define FCH_KEITHMDN_FCHKEITHMDNSMM_FCHSMM_FILECODE                                                           (0xBE79)
#define FCH_KEITHMDN_FCHKEITHMDNSMM_GPISMI_FILECODE                                                           (0xBE7A)
#define FCH_KEITHMDN_FCHKEITHMDNSMM_IOTRAPSMI_FILECODE                                                        (0xBE7B)
#define FCH_KEITHMDN_FCHKEITHMDNSMM_MISCSMI_FILECODE                                                          (0xBE7C)
#define FCH_KEITHMDN_FCHKEITHMDNSMM_PERIODICTIMERSMI_FILECODE                                                 (0xBE7D)
#define FCH_KEITHMDN_FCHKEITHMDNSMM_POWERBUTTONSMI_FILECODE                                                   (0xBE7E)
#define FCH_KEITHMDN_FCHKEITHMDNSMM_SWSMI_FILECODE                                                            (0xBE7F)
#define FCH_KEITHMDN_FCHKEITHMDNSMM_SXSMI_FILECODE                                                            (0xBE80)
#define FCH_KEITHMDN_FCHKEITHMDNSMMCONTROLDXE_SMMCONTROL_FILECODE                                             (0xBE81)
#define FCH_KEITHMDN_FCHKEITHMDNSMMDISPATCHER_FCHSMMDIAGDISPATCHER_FILECODE                                   (0xBE82)
#define FCH_KEITHMDN_FCHKEITHMDNSMMDISPATCHER_FCHSMMDISPATCHER_FILECODE                                       (0xBE83)
#define FCH_KEITHMDN_FCHKEITHMDNSMMDISPATCHER_FCHSMMGPIDISPATCHER_FCHSMMGPIDISPATCHER_FILECODE                (0xBE84)
#define FCH_KEITHMDN_FCHKEITHMDNSMMDISPATCHER_FCHSMMIOTRAPDISPATCHER_FCHSMMIOTRAPDISPATCHER_FILECODE          (0xBE85)
#define FCH_KEITHMDN_FCHKEITHMDNSMMDISPATCHER_FCHSMMMISCDISPATCHER_FCHSMMMISCDISPATCHER_FILECODE              (0xBE86)
#define FCH_KEITHMDN_FCHKEITHMDNSMMDISPATCHER_FCHSMMPERIODICALDISPATCHER_FCHSMMPERIODICALDISPATCHER_FILECODE  (0xBE87)
#define FCH_KEITHMDN_FCHKEITHMDNSMMDISPATCHER_FCHSMMPWRBTNDISPATCHER_FCHSMMPWRBTNDISPATCHER_FILECODE          (0xBE88)
#define FCH_KEITHMDN_FCHKEITHMDNSMMDISPATCHER_FCHSMMSWDISPATCHER_FCHSMMSWDISPATCHER_FILECODE                  (0xBE89)
#define FCH_KEITHMDN_FCHKEITHMDNSMMDISPATCHER_FCHSMMSXDISPATCHER_FCHSMMSXDISPATCHER_FILECODE                  (0xBE8A)
#define FCH_KEITHMDN_FCHKEITHMDNSMMDISPATCHER_FCHSMMUSBDISPATCHER_FCHSMMUSBDISPATCHER_FILECODE                (0xBE8B)

#define LIBRARY_FCHIDSHOOKMDNLIB_PEI_FCHIDSHOOKMDNLIBPEI_FILECODE                                             (0xBE90)
#define LIBRARY_FCHIDSHOOKMDNLIB_DXE_FCHIDSHOOKMDNLIBDXE_FILECODE                                             (0xBE91)
#define LIBRARY_FCHIDSHOOKMDNNULLLIB_PEI_FCHIDSHOOKMDNNULLLIBPEI_FILECODE                                     (0xBE92)

#define FCH_KUNLUN_FCHKUNLUNCORE_COMMON_ACPILIB_FILECODE                                    (0xBF40)
#define FCH_KUNLUN_FCHKUNLUNCORE_COMMON_FCHCOMMONSMM_FILECODE                               (0xBF41)
#define FCH_KUNLUN_FCHKUNLUNCORE_COMMON_FCHCOMMON_FILECODE                                  (0xBF42)
#define FCH_KUNLUN_FCHKUNLUNCORE_COMMON_FCHLIB_FILECODE                                     (0xBF43)
#define FCH_KUNLUN_FCHKUNLUNCORE_COMMON_FCHPELIB_FILECODE                                   (0xBF44)
#define FCH_KUNLUN_FCHKUNLUNCORE_COMMON_MEMLIB_FILECODE                                     (0xBF45)
#define FCH_KUNLUN_FCHKUNLUNCORE_COMMON_PCILIB_FILECODE                                     (0xBF46)
#define FCH_KUNLUN_FCHKUNLUNCORE_COMMON_FCHAOACLIB_FILECODE                                 (0xBF47)
#define FCH_KUNLUN_FCHKUNLUNCORE_COMMON_FCHSMNLIB_FILECODE                                  (0xBF48)
#define FCH_KUNLUN_FCHKUNLUNCORE_KUNLUN_KLEMMC_KLEMMCENV_FILECODE                         (0xBF49)
#define FCH_KUNLUN_FCHKUNLUNCORE_KUNLUN_KLEMMC_KLEMMCLATE_FILECODE                        (0xBF4A)
#define FCH_KUNLUN_FCHKUNLUNCORE_KUNLUN_KLEMMC_KLEMMCMID_FILECODE                         (0xBF4B)
#define FCH_KUNLUN_FCHKUNLUNCORE_KUNLUN_KLEMMC_KLEMMCRESET_FILECODE                       (0xBF4C)
#define FCH_KUNLUN_FCHKUNLUNCORE_KUNLUN_KLGBE_KLGBEENV_FILECODE                           (0xBF4D)
#define FCH_KUNLUN_FCHKUNLUNCORE_KUNLUN_KLGBE_KLGBELATE_FILECODE                          (0xBF4E)
#define FCH_KUNLUN_FCHKUNLUNCORE_KUNLUN_KLGBE_KLGBEMID_FILECODE                           (0xBF4F)
#define FCH_KUNLUN_FCHKUNLUNCORE_KUNLUN_KLGBE_KLGBERESET_FILECODE                         (0xBF50)
#define FCH_KUNLUN_FCHKUNLUNCORE_KUNLUN_KLHWACPI_KLHWACPIENVSERVICE_FILECODE     (0xBF51)
#define FCH_KUNLUN_FCHKUNLUNCORE_KUNLUN_KLHWACPI_KLHWACPIENV_FILECODE            (0xBF52)
#define FCH_KUNLUN_FCHKUNLUNCORE_KUNLUN_KLHWACPI_KLHWACPILATESERVICE_FILECODE    (0xBF53)
#define FCH_KUNLUN_FCHKUNLUNCORE_KUNLUN_KLHWACPI_KLHWACPILATE_FILECODE           (0xBF54)
#define FCH_KUNLUN_FCHKUNLUNCORE_KUNLUN_KLHWACPI_KLHWACPIMID_FILECODE            (0xBF55)
#define FCH_KUNLUN_FCHKUNLUNCORE_KUNLUN_KLHWACPI_KLHWACPIRESET_FILECODE          (0xBF56)
#define FCH_KUNLUN_FCHKUNLUNCORE_KUNLUN_KLHWACPI_KLSSSERVICE_FILECODE            (0xBF57)
#define FCH_KUNLUN_FCHKUNLUNCORE_KUNLUN_KLHWM_KLHWMENVSERVICE_FILECODE           (0xBF58)
#define FCH_KUNLUN_FCHKUNLUNCORE_KUNLUN_KLHWM_KLHWMENV_FILECODE                  (0xBF59)
#define FCH_KUNLUN_FCHKUNLUNCORE_KUNLUN_KLHWM_KLHWMLATESERVICE_FILECODE          (0xBF5A)
#define FCH_KUNLUN_FCHKUNLUNCORE_KUNLUN_KLHWM_KLHWMLATE_FILECODE                 (0xBF5B)
#define FCH_KUNLUN_FCHKUNLUNCORE_KUNLUN_KLHWM_KLHWMMID_FILECODE                  (0xBF5C)
#define FCH_KUNLUN_FCHKUNLUNCORE_KUNLUN_KLHWM_KLHWMRESET_FILECODE                (0xBF5D)
#define FCH_KUNLUN_FCHKUNLUNCORE_KUNLUN_KLIMC_KLFCHECENV_FILECODE                (0xBF5E)
#define FCH_KUNLUN_FCHKUNLUNCORE_KUNLUN_KLIMC_KLFCHECLATE_FILECODE               (0xBF5F)
#define FCH_KUNLUN_FCHKUNLUNCORE_KUNLUN_KLIMC_KLFCHECMID_FILECODE                (0xBF60)
#define FCH_KUNLUN_FCHKUNLUNCORE_KUNLUN_KLIMC_KLFCHECRESET_FILECODE              (0xBF61)
#define FCH_KUNLUN_FCHKUNLUNCORE_KUNLUN_KLIMC_KLIMCENV_FILECODE                  (0xBF62)
#define FCH_KUNLUN_FCHKUNLUNCORE_KUNLUN_KLIMC_KLIMCLATE_FILECODE                 (0xBF63)
#define FCH_KUNLUN_FCHKUNLUNCORE_KUNLUN_KLIMC_KLIMCLIB_FILECODE                  (0xBF64)
#define FCH_KUNLUN_FCHKUNLUNCORE_KUNLUN_KLIMC_KLIMCMID_FILECODE                  (0xBF65)
#define FCH_KUNLUN_FCHKUNLUNCORE_KUNLUN_KLIMC_KLIMCRESET_FILECODE                (0xBF66)
#define FCH_KUNLUN_FCHKUNLUNCORE_KUNLUN_KLIMC_KLIMCSERVICE_FILECODE              (0xBF67)
#define FCH_KUNLUN_FCHKUNLUNCORE_KUNLUN_KLINTERFACE_KLFCHINITENV_FILECODE        (0xBF68)
#define FCH_KUNLUN_FCHKUNLUNCORE_KUNLUN_KLINTERFACE_KLFCHINITLATE_FILECODE       (0xBF69)
#define FCH_KUNLUN_FCHKUNLUNCORE_KUNLUN_KLINTERFACE_KLFCHINITMID_FILECODE        (0xBF6A)
#define FCH_KUNLUN_FCHKUNLUNCORE_KUNLUN_KLINTERFACE_KLFCHINITRESET_FILECODE      (0xBF6B)
#define FCH_KUNLUN_FCHKUNLUNCORE_KUNLUN_KLINTERFACE_KLFCHINITS3_FILECODE         (0xBF6C)
#define FCH_KUNLUN_FCHKUNLUNCORE_KUNLUN_KLINTERFACE_KLFCHTASKLAUNCHER_FILECODE   (0xBF6D)
#define FCH_KUNLUN_FCHKUNLUNCORE_KUNLUN_KLIR_KLIRENV_FILECODE                    (0xBF6E)
#define FCH_KUNLUN_FCHKUNLUNCORE_KUNLUN_KLIR_KLIRLATE_FILECODE                   (0xBF6F)
#define FCH_KUNLUN_FCHKUNLUNCORE_KUNLUN_KLIR_KLIRMID_FILECODE                    (0xBF70)
#define FCH_KUNLUN_FCHKUNLUNCORE_KUNLUN_KLPCIE_KLABENVSERVICE_FILECODE           (0xBF71)
#define FCH_KUNLUN_FCHKUNLUNCORE_KUNLUN_KLPCIE_KLABENV_FILECODE                  (0xBF72)
#define FCH_KUNLUN_FCHKUNLUNCORE_KUNLUN_KLPCIE_KLABLATE_FILECODE                 (0xBF73)
#define FCH_KUNLUN_FCHKUNLUNCORE_KUNLUN_KLPCIE_KLABMID_FILECODE                  (0xBF74)
#define FCH_KUNLUN_FCHKUNLUNCORE_KUNLUN_KLPCIE_KLABRESETSERVICE_FILECODE         (0xBF75)
#define FCH_KUNLUN_FCHKUNLUNCORE_KUNLUN_KLPCIE_KLABRESET_FILECODE                (0xBF76)
#define FCH_KUNLUN_FCHKUNLUNCORE_KUNLUN_KLPCIE_KLABSERVICE_FILECODE              (0xBF77)
#define FCH_KUNLUN_FCHKUNLUNCORE_KUNLUN_KLSATA_KLAHCIENV_FILECODE                (0xBF78)
#define FCH_KUNLUN_FCHKUNLUNCORE_KUNLUN_KLSATA_KLAHCILATE_FILECODE               (0xBF79)
#define FCH_KUNLUN_FCHKUNLUNCORE_KUNLUN_KLSATA_KLAHCILIB_FILECODE                (0xBF7A)
#define FCH_KUNLUN_FCHKUNLUNCORE_KUNLUN_KLSATA_KLAHCIMID_FILECODE                (0xBF7B)
#define FCH_KUNLUN_FCHKUNLUNCORE_KUNLUN_KLSATA_KLIDE2AHCIENV_FILECODE            (0xBF7C)
#define FCH_KUNLUN_FCHKUNLUNCORE_KUNLUN_KLSATA_KLIDE2AHCILATE_FILECODE           (0xBF7D)
#define FCH_KUNLUN_FCHKUNLUNCORE_KUNLUN_KLSATA_KLIDE2AHCILIB_FILECODE            (0xBF7E)
#define FCH_KUNLUN_FCHKUNLUNCORE_KUNLUN_KLSATA_KLIDE2AHCIMID_FILECODE            (0xBF7F)
#define FCH_KUNLUN_FCHKUNLUNCORE_KUNLUN_KLSATA_KLRAIDENV_FILECODE                (0xBF80)
#define FCH_KUNLUN_FCHKUNLUNCORE_KUNLUN_KLSATA_KLRAIDLATE_FILECODE               (0xBF81)
#define FCH_KUNLUN_FCHKUNLUNCORE_KUNLUN_KLSATA_KLRAIDLIB_FILECODE                (0xBF82)
#define FCH_KUNLUN_FCHKUNLUNCORE_KUNLUN_KLSATA_KLRAIDMID_FILECODE                (0xBF83)
#define FCH_KUNLUN_FCHKUNLUNCORE_KUNLUN_KLSATA_KLSATAENVLIB_FILECODE             (0xBF84)
#define FCH_KUNLUN_FCHKUNLUNCORE_KUNLUN_KLSATA_KLSATAENVSERVICE_FILECODE         (0xBF85)
#define FCH_KUNLUN_FCHKUNLUNCORE_KUNLUN_KLSATA_KLSATAENV_FILECODE                (0xBF86)
#define FCH_KUNLUN_FCHKUNLUNCORE_KUNLUN_KLSATA_KLSATAIDELIB_FILECODE             (0xBF87)
#define FCH_KUNLUN_FCHKUNLUNCORE_KUNLUN_KLSATA_KLSATAIDEENV_FILECODE             (0xBF88)
#define FCH_KUNLUN_FCHKUNLUNCORE_KUNLUN_KLSATA_KLSATAIDELATE_FILECODE            (0xBF89)
#define FCH_KUNLUN_FCHKUNLUNCORE_KUNLUN_KLSATA_KLSATAIDEMID_FILECODE             (0xBF8A)
#define FCH_KUNLUN_FCHKUNLUNCORE_KUNLUN_KLSATA_KLSATALATE_FILECODE               (0xBF8B)
#define FCH_KUNLUN_FCHKUNLUNCORE_KUNLUN_KLSATA_KLSATALIB_FILECODE                (0xBF8C)
#define FCH_KUNLUN_FCHKUNLUNCORE_KUNLUN_KLSATA_KLSATAMID_FILECODE                (0xBF8D)
#define FCH_KUNLUN_FCHKUNLUNCORE_KUNLUN_KLSATA_KLSATARESETSERVICE_FILECODE       (0xBF8E)
#define FCH_KUNLUN_FCHKUNLUNCORE_KUNLUN_KLSATA_KLSATARESET_FILECODE              (0xBF8F)
#define FCH_KUNLUN_FCHKUNLUNCORE_KUNLUN_KLSATA_KLSATASERVICE_FILECODE            (0xBF90)
#define FCH_KUNLUN_FCHKUNLUNCORE_KUNLUN_KLSD_KLSDENVSERVICE_FILECODE             (0xBF91)
#define FCH_KUNLUN_FCHKUNLUNCORE_KUNLUN_KLSD_KLSDENV_FILECODE                    (0xBF92)
#define FCH_KUNLUN_FCHKUNLUNCORE_KUNLUN_KLSD_KLSDLATE_FILECODE                   (0xBF93)
#define FCH_KUNLUN_FCHKUNLUNCORE_KUNLUN_KLSD_KLSDMID_FILECODE                    (0xBF94)
#define FCH_KUNLUN_FCHKUNLUNCORE_KUNLUN_KLSPI_KLLPCENVSERVICE_FILECODE           (0xBF95)
#define FCH_KUNLUN_FCHKUNLUNCORE_KUNLUN_KLSPI_KLLPCENV_FILECODE                  (0xBF96)
#define FCH_KUNLUN_FCHKUNLUNCORE_KUNLUN_KLSPI_KLLPCLATE_FILECODE                 (0xBF97)
#define FCH_KUNLUN_FCHKUNLUNCORE_KUNLUN_KLSPI_KLLPCMID_FILECODE                  (0xBF98)
#define FCH_KUNLUN_FCHKUNLUNCORE_KUNLUN_KLSPI_KLLPCRESETSERVICE_FILECODE         (0xBF99)
#define FCH_KUNLUN_FCHKUNLUNCORE_KUNLUN_KLSPI_KLLPCRESET_FILECODE                (0xBF9A)
#define FCH_KUNLUN_FCHKUNLUNCORE_KUNLUN_KLSPI_KLSPIENV_FILECODE                  (0xBF9B)
#define FCH_KUNLUN_FCHKUNLUNCORE_KUNLUN_KLSPI_KLSPILATE_FILECODE                 (0xBF9C)
#define FCH_KUNLUN_FCHKUNLUNCORE_KUNLUN_KLSPI_KLSPIMID_FILECODE                  (0xBF9D)
#define FCH_KUNLUN_FCHKUNLUNCORE_KUNLUN_KLUSB_KLUSBENV_FILECODE                  (0xBF9E)
#define FCH_KUNLUN_FCHKUNLUNCORE_KUNLUN_KLUSB_KLUSBLATE_FILECODE                 (0xBF9F)
#define FCH_KUNLUN_FCHKUNLUNCORE_KUNLUN_KLUSB_KLUSBMID_FILECODE                  (0xBFA0)
#define FCH_KUNLUN_FCHKUNLUNCORE_KUNLUN_KLUSB_KLUSBRESET_FILECODE                (0xBFA1)
#define FCH_KUNLUN_FCHKUNLUNCORE_KUNLUN_KLUSB_KLXHCIENVSERVICE_FILECODE          (0xBFA2)
#define FCH_KUNLUN_FCHKUNLUNCORE_KUNLUN_KLUSB_KLXHCIENV_FILECODE                 (0xBFA3)
#define FCH_KUNLUN_FCHKUNLUNCORE_KUNLUN_KLUSB_KLXHCILATESERVICE_FILECODE         (0xBFA4)
#define FCH_KUNLUN_FCHKUNLUNCORE_KUNLUN_KLUSB_KLXHCILATE_FILECODE                (0xBFA5)
#define FCH_KUNLUN_FCHKUNLUNCORE_KUNLUN_KLUSB_KLXHCIMID_FILECODE                 (0xBFA6)
#define FCH_KUNLUN_FCHKUNLUNCORE_KUNLUN_KLUSB_KLXHCIRESETSERVICE_FILECODE        (0xBFA7)
#define FCH_KUNLUN_FCHKUNLUNCORE_KUNLUN_KLUSB_KLXHCIRESET_FILECODE               (0xBFA8)
#define FCH_KUNLUN_FCHKUNLUNCORE_KUNLUN_KLUSB_KLXHCISERVICE_FILECODE             (0xBFA9)
#define FCH_KUNLUN_FCHKUNLUNCORE_KUNLUN_KLESPI_KLESPIRESET_FILECODE              (0xBFAA)
#define FCH_KUNLUN_FCHKUNLUNCORE_KUNLUN_KLESPI_KLESPIENV_FILECODE                (0xBFAB)
#define FCH_KUNLUN_FCHKUNLUNCORE_KUNLUN_KLESPI_KLESPIMID_FILECODE                (0xBFAC)
#define FCH_KUNLUN_FCHKUNLUNCORE_KUNLUN_KLESPI_KLESPILATE_FILECODE               (0xBFAD)
#define FCH_KUNLUN_FCHKUNLUNCORE_KUNLUN_KLESPI_KLESPILIB_FILECODE                (0xBFAE)
#define FCH_KUNLUN_FCHKUNLUNCORE_KUNLUN_KLXGBE_KLXGBEENV_FILECODE                (0xBFAF)
#define FCH_KUNLUN_FCHKUNLUNCORE_KUNLUN_KLXGBE_KLXGBELATE_FILECODE               (0xBFB0)
#define FCH_KUNLUN_FCHKUNLUNCORE_KUNLUN_KLXGBE_KLXGBEMID_FILECODE                (0xBFB1)
#define FCH_KUNLUN_FCHKUNLUNCORE_KUNLUN_KLXGBE_KLXGBERESET_FILECODE              (0xBFB2)
#define FCH_KUNLUN_FCHKUNLUNCORE_KUNLUN_KLXGBE_KLXGBELIB_FILECODE                (0xBFB3)
#define FCH_KUNLUN_FCHKUNLUNCF9RESETDXE_CF9RESET_FILECODE                         (0xBFB4)
#define FCH_KUNLUN_FCHKUNLUNMULTIFCHDXE_FCHMULTIFCHDXE_FILECODE                   (0xBFB5)
#define FCH_KUNLUN_FCHKUNLUNMULTIFCHPEI_FCHMULTIFCHPEI_FILECODE                   (0xBFB6)
#define FCH_KUNLUN_FCHKUNLUNDXE_FCHDXE_FILECODE                                   (0xBFB7)
#define FCH_KUNLUN_FCHKUNLUNLEGACYINTERRUPTDXE_LEGACYINTERRUPT_FILECODE           (0xBFB8)
#define FCH_KUNLUN_FCHKUNLUNPEI_FCHPEI_FILECODE                                   (0xBFB9)
#define FCH_KUNLUN_FCHKUNLUNPEI_FCHRESET_FILECODE                                 (0xBFBA)
#define FCH_KUNLUN_FCHKUNLUNPEI_FCHSTALL_FILECODE                                 (0xBFBB)
#define FCH_KUNLUN_FCHKUNLUNSMBUSDXE_SMBUSLIGHT_FILECODE                          (0xBFBC)
#define FCH_KUNLUN_FCHKUNLUNSMBUSPEI_SMBUS_FILECODE                               (0xBFBD)
#define FCH_KUNLUN_FCHKUNLUNSMMCONTROLDXE_SMMCONTROL_FILECODE                     (0xBFBE)
#define FCH_KUNLUN_FCHKUNLUNSMMDISPATCHER_FCHSMMDIAGDISPATCHER_FILECODE                                  (0xBFBF)
#define FCH_KUNLUN_FCHKUNLUNSMMDISPATCHER_FCHSMMDISPATCHER_FILECODE                                      (0xBFC0)
#define FCH_KUNLUN_FCHKUNLUNSMMDISPATCHER_FCHSMMGPIDISPATCHER_FCHSMMGPIDISPATCHER_FILECODE               (0xBFC1)
#define FCH_KUNLUN_FCHKUNLUNSMMDISPATCHER_FCHSMMIOTRAPDISPATCHER_FCHSMMIOTRAPDISPATCHER_FILECODE         (0xBFC2)
#define FCH_KUNLUN_FCHKUNLUNSMMDISPATCHER_FCHSMMMISCDISPATCHER_FCHSMMMISCDISPATCHER_FILECODE             (0xBFC3)
#define FCH_KUNLUN_FCHKUNLUNSMMDISPATCHER_FCHSMMPERIODICALDISPATCHER_FCHSMMPERIODICALDISPATCHER_FILECODE (0xBFC4)
#define FCH_KUNLUN_FCHKUNLUNSMMDISPATCHER_FCHSMMPWRBTNDISPATCHER_FCHSMMPWRBTNDISPATCHER_FILECODE         (0xBFC5)
#define FCH_KUNLUN_FCHKUNLUNSMMDISPATCHER_FCHSMMSWDISPATCHER_FCHSMMSWDISPATCHER_FILECODE                 (0xBFC6)
#define FCH_KUNLUN_FCHKUNLUNSMMDISPATCHER_FCHSMMSXDISPATCHER_FCHSMMSXDISPATCHER_FILECODE                 (0xBFC7)
#define FCH_KUNLUN_FCHKUNLUNSMMDISPATCHER_FCHSMMUSBDISPATCHER_FCHSMMUSBDISPATCHER_FILECODE               (0xBFC8)
#define FCH_KUNLUN_FCHKUNLUNSMMDISPATCHER_FCHSMMAPURASDISPATCHER_FCHSMMAPURASDISPATCHER_FILECODE         (0xBFC9)
#define FCH_KUNLUN_FCHKUNLUNSMMDISPATCHER_FCHSMMAPURASDISPATCHER_FCHSSPRASSMILIB_FILECODE                (0xBFCA)
#define FCH_KUNLUN_FCHKUNLUNSMM_FCHSMM_FILECODE                         (0xBFCB)
#define FCH_KUNLUN_FCHKUNLUNSMM_GPISMI_FILECODE                         (0xBFCC)
#define FCH_KUNLUN_FCHKUNLUNSMM_IOTRAPSMI_FILECODE                      (0xBFCD)
#define FCH_KUNLUN_FCHKUNLUNSMM_MISCSMI_FILECODE                        (0xBFCE)
#define FCH_KUNLUN_FCHKUNLUNSMM_PERIODICTIMERSMI_FILECODE               (0xBFCF)
#define FCH_KUNLUN_FCHKUNLUNSMM_POWERBUTTONSMI_FILECODE                 (0xBFD0)
#define FCH_KUNLUN_FCHKUNLUNSMM_SWSMI_FILECODE                          (0xBFD1)
#define FCH_KUNLUN_FCHKUNLUNSMM_SXSMI_FILECODE                          (0xBFD2)
#define FCH_KUNLUN_FCHKUNLUNI3CPEI_I3C_FILECODE                         (0xBFD3)

#define FCH_KUNLUN_FCHKUNLUNCORE_KUNLUN_KLLPCSPI_KLLPCENV_FILECODE                                  (0xBFD4)
#define FCH_KUNLUN_FCHKUNLUNCORE_KUNLUN_KLLPCSPI_KLLPCENVSERVICE_FILECODE                           (0xBFD5)
#define FCH_KUNLUN_FCHKUNLUNCORE_KUNLUN_KLLPCSPI_KLLPCLATE_FILECODE                                 (0xBFD6)
#define FCH_KUNLUN_FCHKUNLUNCORE_KUNLUN_KLLPCSPI_KLLPCMID_FILECODE                                  (0xBFD7)
#define FCH_KUNLUN_FCHKUNLUNCORE_KUNLUN_KLLPCSPI_KLLPCRESET_FILECODE                                (0xBFD8)
#define FCH_KUNLUN_FCHKUNLUNCORE_KUNLUN_KLLPCSPI_KLLPCRESETSERVICE_FILECODE                         (0xBFD9)
#define FCH_KUNLUN_FCHKUNLUNCORE_KUNLUN_KLLPCSPI_KLSPIENV_FILECODE                                  (0xBFDA)
#define FCH_KUNLUN_FCHKUNLUNCORE_KUNLUN_KLLPCSPI_KLSPILATE_FILECODE                                 (0xBFDB)
#define FCH_KUNLUN_FCHKUNLUNCORE_KUNLUN_KLLPCSPI_KLSPIMID_FILECODE                                  (0xBFDC)
#define FCH_KUNLUN_FCHKUNLUNDXE_FCHKUNLUNDSDT_FILECODE                     (0xBFDD)
#define FCH_KUNLUN_FCHKUNLUNDXE_FCHKUNLUNSSDT_FILECODE                     (0xBFDE)
#define FCH_KUNLUN_FCHKUNLUNI3CDXE_AMDI3CMASTERDXE_FILECODE                (0xBFDF)
#define FCH_KUNLUN_FCHKUNLUNI3CPEI_AMDI3CMASTERPEI_FILECODE                (0xBFE0)
#define FCH_KUNLUN_FCHKUNLUNMULTIFCHSMM_FCHMULTIFCHSMM_FILECODE            (0xBFE1)
#define FCH_KUNLUN_FCHKUNLUNPEI_FCHRESET2_FILECODE                         (0xBFE2)
#define LIBRARY_FCHDXERUNTIMERESETSYSTEMLIB_KUNLUN_FCHDXERUNTIMERESETSYSTEMLIB_FILECODE  (0xBFE3)

#define FCH_COMMON_I2CPEI_I2CMASTERPEI_FILECODE                            (0xBFE4)
#define FCH_COMMON_I2CDXE_I2CMASTERDXE_FILECODE                            (0xBFE5)
#define FCH_COMMON_I3CDXE_AMDI3CMASTERCONSUMERDXE_FILECODE                 (0xBFE6)
#define FCH_COMMON_I3CPEI_I3CMASTERPEI_FILECODE                            (0xBFE7)
#define FCH_COMMON_I3CDXE_I3CMASTERDXE_FILECODE                            (0xBFE8)
#define LIBRARY_FCHESPILIB_FCHESPILIB_FILECODE                             (0xBFE9)
#define FCH_COMMON_I2CSMM_I2CMASTERSMM_FILECODE                            (0xBFEA)
#define LIBRARY_FCHESPICMDLIB_FCHESPICMDLIB_FILECODE                       (0xBFEB)
#define FCH_COMMON_FCHESPICMDPEI_FCHESPICMDPEI_FILECODE                    (0xBFEC)
#define FCH_COMMON_FCHESPICMDDXE_FCHESPICMDDXE_FILECODE                    (0xBFED)
#define FCH_COMMON_FCHESPICMDSMM_FCHESPICMDSMM_FILECODE                    (0xBFEE)
#define FCH_COMMON_I3CPEI_I3CHCIMASTERPEI_FILECODE                         (0xBFEF)
#define FCH_COMMON_I3CDXE_I3CHCIMASTERDXE_FILECODE                         (0xBFF0)

#define FCH_COMMON_FCHCF9RESETDXE_CF9RESET_FILECODE                                                (0xB7A0)
#define FCH_COMMON_FCHLEGACYINTERRUPTDXE_LEGACYINTERRUPT_FILECODE                                  (0xB7A1)
#define FCH_COMMON_FCHSMBUSDXE_SMBUSLIGHT_FILECODE                                                 (0xB7A2)
#define FCH_COMMON_FCHSMBUSPEI_SMBUS_FILECODE                                                      (0xB7A3)
#define FCH_COMMON_FCHSMMCONTROLDXE_SMMCONTROL_FILECODE                                            (0xB7A4)
#define FCH_COMMON_FCHPEI_FCHPEI_FILECODE                                                          (0xB7A5)
#define FCH_COMMON_FCHPEI_FCHRESET_FILECODE                                                        (0xB7A6)
#define FCH_COMMON_FCHPEI_FCHSTALL_FILECODE                                                        (0xB7A7)
#define FCH_COMMON_FCHPEI_FCHRESET2_FILECODE                                                       (0xB7A8)
#define FCH_COMMON_FCHDXE_FCHDXE_FILECODE                                                          (0xB7A9)
#define FCH_COMMON_FCHSMM_FCHSMM_FILECODE                                                          (0xB7AA)
#define FCH_COMMON_FCHSMM_GPISMI_FILECODE                                                          (0xB7AB)
#define FCH_COMMON_FCHSMM_IOTRAPSMI_FILECODE                                                       (0xB7AC)
#define FCH_COMMON_FCHSMM_MISCSMI_FILECODE                                                         (0xB7AD)
#define FCH_COMMON_FCHSMM_PERIODICTIMERSMI_FILECODE                                                (0xB7AE)
#define FCH_COMMON_FCHSMM_POWERBUTTONSMI_FILECODE                                                  (0xB7AF)
#define FCH_COMMON_FCHSMM_SWSMI_FILECODE                                                           (0xB7B0)
#define FCH_COMMON_FCHSMM_SXSMI_FILECODE                                                           (0xB7B2)
#define FCH_COMMON_FCHSMM_FCHSMMSUPP_FILECODE                                                      (0xB7B3)
#define FCH_COMMON_FCHSMM_XHCISERVICE_FILECODE                                                     (0xB7B4)
#define FCH_COMMON_FCHMULTIFCHDXE_FCHMULTIFCHDXE_FILECODE                                          (0xB7B5)
#define FCH_COMMON_FCHMULTIFCHSMM_FCHMULTIFCHSMM_FILECODE                                          (0xB7B6)

#define FCH_COMMON_FCHMULTIFCHPEI_FCHMULTIFCHPEI_FILECODE                                          (0xB7C3)

// PROMONTORY
#define FCH_PROMONTORY_PROMONTORYPEI_FCHPROMONTORYPEI_FILECODE                                  (0xB800)
#define FCH_PROMONTORY_PROMONTORYPEI_FCHPROMONTORYCBSPEI_FILECODE                               (0xB801)
#define FCH_PROMONTORY_PROMONTORYPEI_FCHPROMONTORYGPIOPEI_FILECODE                              (0xB802)
#define FCH_PROMONTORY_PROMONTORYLIBRARY_FCHPROMONTORYPEILIB_FCHPROMONTORYPEILIB_FILECODE       (0xB803)
#define FCH_PROMONTORY_FCHPROMONTORYDXE_PROMONTORYDXE_FILECODE                                  (0xB804)
#define FCH_PROMONTORY_PROMONTORYDXE_FCHPROMONTORYCBSDXE_FILECODE                               (0xB805)
#define FCH_PROMONTORY_PROMONTORYDXE_FCHPROMONTORYGPIODXE_FILECODE                              (0xB806)
#define FCH_PROMONTORY_PROMONTORYLIBRARY_FCHPROMONTORYDXELIB_FCHPROMONTORYDXELIB_FILECODE       (0xB807)
#define FCH_PROMONTORY_PROMONTORYDXE_FCHPROMONTORYSSDT_FILECODE                                 (0xB808)
#define FCH_PROMONTORY_PROMONTORYSMM_FCHPROMONTORYGPIOSMM_FILECODE                              (0xB809)
#define FCH_PROMONTORY_PROMONTORYSMM_FCHPROMONTORYSMM_FILECODE                                  (0xB80A)
#define FCH_PROMONTORY_PROMONTORYDXE_FCHPROMONTORYDXE_FILECODE                                  (0xB80B)

// BIXBY
#define FCH_BIXBY_FCHBIXBYCORE_COMMON_ACPILIB_FILECODE                                          (0xB810)
#define FCH_BIXBY_FCHBIXBYCORE_COMMON_FCHAOACLIB_FILECODE                                       (0xB811)
#define FCH_BIXBY_FCHBIXBYCORE_COMMON_FCHCOMMON_FILECODE                                        (0xB812)
#define FCH_BIXBY_FCHBIXBYCORE_COMMON_FCHCOMMONSMM_FILECODE                                     (0xB813)
#define FCH_BIXBY_FCHBIXBYCORE_COMMON_FCHLIB_FILECODE                                           (0xB814)
#define FCH_BIXBY_FCHBIXBYPEI_FCHPEI_FILECODE                                                   (0xB815)
#define FCH_BIXBY_FCHBIXBYCORE_COMMON_FCHPELIB_FILECODE                                         (0xB816)
#define FCH_BIXBY_FCHBIXBYPEI_FCHRESET_FILECODE                                                 (0xB817)
#define FCH_BIXBY_FCHBIXBYSMM_FCHSMM_FILECODE                                                   (0xB818)
#define FCH_BIXBY_FCHBIXBYCORE_COMMON_FCHSMNLIB_FILECODE                                        (0xB819)
#define FCH_BIXBY_FCHBIXBYPEI_FCHSTALL_FILECODE                                                 (0xB81A)
#define FCH_BIXBY_FCHBIXBYCORE_BIXBY_BIXBYSATA_BIXBYAHCILIB_FILECODE                            (0xB81B)
#define FCH_BIXBY_FCHBIXBYCORE_BIXBY_BIXBYSATA_BIXBYAHCIENV_FILECODE                            (0xB81C)
#define FCH_BIXBY_FCHBIXBYCORE_BIXBY_BIXBYSATA_BIXBYAHCIMID_FILECODE                            (0xB81D)
#define FCH_BIXBY_FCHBIXBYCORE_BIXBY_BIXBYSATA_BIXBYAHCILATE_FILECODE                           (0xB81E)
#define FCH_BIXBY_FCHBIXBYCORE_BIXBY_BIXBYINTERFACE_BIXBYFCHINITRESET_FILECODE                  (0xB81F)
#define FCH_BIXBY_FCHBIXBYCORE_BIXBY_BIXBYINTERFACE_BIXBYFCHINITENV_FILECODE                    (0xB820)
#define FCH_BIXBY_FCHBIXBYCORE_BIXBY_BIXBYINTERFACE_BIXBYFCHINITMID_FILECODE                    (0xB821)
#define FCH_BIXBY_FCHBIXBYCORE_BIXBY_BIXBYINTERFACE_BIXBYFCHINITLATE_FILECODE                   (0xB822)
#define FCH_BIXBY_FCHBIXBYCORE_BIXBY_BIXBYINTERFACE_BIXBYFCHINITS3_FILECODE                     (0xB823)
#define FCH_BIXBY_FCHBIXBYCORE_BIXBY_BIXBYINTERFACE_BIXBYFCHTASKLAUNCHER_FILECODE               (0xB824)
#define FCH_BIXBY_FCHBIXBYCORE_BIXBY_BIXBYSATA_BIXBYRAIDLIB_FILECODE                            (0xB825)
#define FCH_BIXBY_FCHBIXBYCORE_BIXBY_BIXBYSATA_BIXBYRAIDENV_FILECODE                            (0xB826)
#define FCH_BIXBY_FCHBIXBYCORE_BIXBY_BIXBYSATA_BIXBYRAIDMID_FILECODE                            (0xB827)
#define FCH_BIXBY_FCHBIXBYCORE_BIXBY_BIXBYSATA_BIXBYRAIDLATE_FILECODE                           (0xB828)
#define FCH_BIXBY_FCHBIXBYCORE_BIXBY_BIXBYSATA_BIXBYSATALIB_FILECODE                            (0xB829)
#define FCH_BIXBY_FCHBIXBYCORE_BIXBY_BIXBYSATA_BIXBYSATARESETSERVICE_FILECODE                   (0xB82A)
#define FCH_BIXBY_FCHBIXBYCORE_BIXBY_BIXBYSATA_BIXBYSATASERVICE_FILECODE                        (0xB82B)
#define FCH_BIXBY_FCHBIXBYCORE_BIXBY_BIXBYSATA_BIXBYSATAENVLIB_FILECODE                         (0xB82C)
#define FCH_BIXBY_FCHBIXBYCORE_BIXBY_BIXBYSATA_BIXBYSATAENVSERVICE_FILECODE                     (0xB82D)
#define FCH_BIXBY_FCHBIXBYCORE_BIXBY_BIXBYSATA_BIXBYSATARESET_FILECODE                          (0xB82E)
#define FCH_BIXBY_FCHBIXBYCORE_BIXBY_BIXBYSATA_BIXBYSATAENV_FILECODE                            (0xB82F)
#define FCH_BIXBY_FCHBIXBYCORE_BIXBY_BIXBYSATA_BIXBYSATAMID_FILECODE                            (0xB830)
#define FCH_BIXBY_FCHBIXBYCORE_BIXBY_BIXBYSATA_BIXBYSATALATE_FILECODE                           (0xB831)
#define FCH_BIXBY_FCHBIXBYCORE_BIXBY_BIXBYUSB_BIXBYUSBRESET_FILECODE                            (0xB832)
#define FCH_BIXBY_FCHBIXBYCORE_BIXBY_BIXBYUSB_BIXBYUSBENV_FILECODE                              (0xB833)
#define FCH_BIXBY_FCHBIXBYCORE_BIXBY_BIXBYUSB_BIXBYUSBMID_FILECODE                              (0xB834)
#define FCH_BIXBY_FCHBIXBYCORE_BIXBY_BIXBYUSB_BIXBYUSBLATE_FILECODE                             (0xB835)
#define FCH_BIXBY_FCHBIXBYCORE_BIXBY_BIXBYUSB_BIXBYXHCIENV_FILECODE                             (0xB836)
#define FCH_BIXBY_FCHBIXBYCORE_BIXBY_BIXBYUSB_BIXBYXHCIMID_FILECODE                             (0xB837)
#define FCH_BIXBY_FCHBIXBYCORE_BIXBY_BIXBYUSB_BIXBYXHCILATE_FILECODE                            (0xB838)
#define FCH_BIXBY_FCHBIXBYCORE_BIXBY_BIXBYUSB_BIXBYXHCIRECOVERY_FILECODE                        (0xB839)
#define FCH_BIXBY_FCHBIXBYCORE_BIXBY_BIXBYUSB_BIXBYXHCIRESET_FILECODE                           (0xB83A)
#define FCH_BIXBY_FCHBIXBYCORE_BIXBY_BIXBYUSB_BIXBYXHCIRESETSERVICE_FILECODE                    (0xB83B)
#define FCH_BIXBY_FCHBIXBYCORE_BIXBY_BIXBYUSB_BIXBYXHCISERVICE_FILECODE                         (0xB83C)
#define FCH_BIXBY_FCHBIXBYCORE_COMMON_MEMLIB_FILECODE                                           (0xB83D)
#define FCH_BIXBY_FCHBIXBYLEGACYINTERRUPTDXE_LEGACYINTERRUPT_FILECODE                           (0xB83E)
#define FCH_BIXBY_FCHBIXBYSMM_SWSMI_FILECODE                                                    (0xB83F)
#define FCH_BIXBY_FCHBIXBYSMM_SXSMI_FILECODE                                                    (0xB840)
#define FCH_BIXBY_FCHBIXBYPEI_FCHBIXBYGPIOPEI_FILECODE                                          (0xB841)
#define FCH_BIXBY_FCHBIXBYCORE_BIXBY_BIXBYHWACPI_BIXBYHWACPIENV_FILECODE                        (0xB842)
#define FCH_BIXBY_FCHBIXBYCORE_BIXBY_BIXBYHWACPI_BIXBYHWACPIENVSERVICE_FILECODE                 (0xB843)
#define FCH_BIXBY_FCHBIXBYDXE_FCHBIXBYSSDT_FILECODE                                             (0xB844)
#define FCH_BIXBY_FCHBIXBYPEI_FCHBIXBYEARLYLINK_FILECODE                                        (0xB845)
#define FCH_BIXBY_FCHBIXBYCORE_BIXBY_BIXBYHWACPI_BIXBYHWACPIRESET_FILECODE                      (0xB846)
#define FCH_BIXBY_FCHBIXBYCORE_BIXBY_BIXBYHWACPI_BIXBYSSSERVICE_FILECODE                        (0xB847)
#define FCH_BIXBY_FCHBIXBYCORE_COMMON_PCILIB_FILECODE                                           (0xB848)
#define FCH_BIXBY_FCHBIXBYDXE_FCHDXE_FILECODE                                                   (0xB849)
#define FCH_BIXBY_FCHBIXBYPEI_FCHRESET2_FILECODE                                                (0xB84A)
#define NBIO_BIXBY_PEI_DRIVERENTRY_FILECODE                                                     (0xB84B)

// PROMONTORY PLUS
#define FCH_PROMONTORYPLUS_PROMONTORYPLUSPEI_FCHPROMONTORYPEI_FILECODE                                (0xB850)
#define FCH_PROMONTORYPLUS_PROMONTORYPLUSPEI_FCHPROMONTORYCBSPEI_FILECODE                             (0xB851)
#define FCH_PROMONTORYPLUS_PROMONTORYPLUSPEI_FCHPROMONTORYGPIOPEI_FILECODE                            (0xB852)
#define FCH_PROMONTORYPLUS_PROMONTORYPLUSLIBRARY_FCHPROMONTORYPEILIB_FCHPROMONTORYPEILIB_FILECODE     (0xB853)
#define FCH_PROMONTORYPLUS_PROMONTORYPLUSDXE_FCHPROMONTORYDXE_FILECODE                                (0xB854)
//#define PROMONTORY_PLUS_FCHPROMONTORYCBSDXE_PROMONTORYCBSDXE_FILECODE                                 (0xB855)
#define FCH_PROMONTORYPLUS_PROMONTORYPLUSDXE_FCHPROMONTORYGPIODXE_FILECODE                            (0xB856)
#define FCH_PROMONTORYPLUS_PROMONTORYPLUSLIBRARY_FCHPROMONTORYDXELIB_FCHPROMONTORYDXELIB_FILECODE     (0xB857)
#define FCH_PROMONTORYPLUS_PROMONTORYPLUSDXE_FCHPROMONTORYSSDT_FILECODE                               (0xB858)
#define FCH_PROMONTORYPLUS_PROMONTORYPLUSSMM_FCHPROMONTORYGPIOSMM_FILECODE                            (0xB859)
#define FCH_PROMONTORYPLUS_PROMONTORYPLUSSMM_FCHPROMONTORYSMM_FILECODE                                (0xB85A)
#define FCH_PROMONTORYPLUS_PROMONTORYPLUSSMM_FCHPROMONTORYSMMBTSWPATCH_FILECODE                       (0xB85B)
#define FCH_PROMONTORYPLUS_PROMONTORYPLUSSMM_FCHPROMONTORYSMMCF9RESETSMI_FILECODE                     (0xB85C)
#define FCH_PROMONTORYPLUS_PROMONTORYPLUSSMM_FCHPROMONTORYSMMPOWERBUTTONSMI_FILECODE                  (0xB85D)
#define FCH_PROMONTORYPLUS_PROMONTORYPLUSSMM_FCHPROMONTORYSMMSWSMI_FILECODE                           (0xB85E)
#define FCH_PROMONTORYPLUS_PROMONTORYPLUSSMM_FCHPROMONTORYSMMSXSMI_FILECODE                           (0xB85F)

// USB4
#define LIBRARY_AMDUSB4LIB_ADAPTER_FILECODE                               (0xB880)
#define LIBRARY_AMDUSB4LIB_BANDWIDTH_FILECODE                             (0xB881)
#define LIBRARY_AMDUSB4LIB_COMMON_FILECODE                                (0xB882)
#define LIBRARY_AMDUSB4LIB_CORE_FILECODE                                  (0xB883)
#define LIBRARY_AMDUSB4LIB_DPTUNNELING14_FILECODE                         (0xB884)
#define LIBRARY_AMDUSB4LIB_DROM_FILECODE                                  (0xB885)
#define LIBRARY_AMDUSB4LIB_FLOWS_FILECODE                                 (0xB886)
#define LIBRARY_AMDUSB4LIB_HOSTINTERFACE_FILECODE                         (0xB887)
#define LIBRARY_AMDUSB4LIB_PATH_FILECODE                                  (0xB888)
#define LIBRARY_AMDUSB4LIB_PCIETUNNELING_FILECODE                         (0xB889)
#define LIBRARY_AMDUSB4LIB_PORT_FILECODE                                  (0xB88A)
#define LIBRARY_AMDUSB4LIB_ROUTER_FILECODE                                (0xB88B)
#define LIBRARY_AMDUSB4LIB_TBT3_FILECODE                                  (0xB88C)
#define LIBRARY_AMDUSB4LIB_TMU_FILECODE                                   (0xB88D)
#define LIBRARY_AMDUSB4LIB_USB3TUNNELING_FILECODE                         (0xB88E)
#define USB4_AMDUSB4DXE_AMDUSB4DXE_FILECODE                               (0xB88F)
#define USB4_AMDUSB4PEI_AMDUSB4PEI_FILECODE                               (0xB890)
#define USB4_AMDUSB4COMMON_AMDUSB4COMMON_FILECODE                         (0xB891)
#define USB4_AMDUSB4SOC_AMDUSB4SOC_FILECODE                               (0xB892)

// BIXBY
#define NBIO_BIXBY_BIXBYEARLYLINK_BIXBYEARLYLINK_FILECODE                 (0xB860)
#define NBIO_BIXBY_BIXBYEARLYLINK_BIXBYLOCATION_FILECODE                  (0xB861)
#define NBIO_BIXBY_COMMON_PCIESTRAPS_FILECODE                             (0xB862)
#define NBIO_BIXBY_DXE_AMDNBIOPCIEASPM_FILECODE                           (0xB863)
#define NBIO_BIXBY_DXE_AMDNBIOPCIECLKREQ_FILECODE                         (0xB864)
#define NBIO_BIXBY_DXE_DRIVERENTRY_FILECODE                               (0xB865)
#define NBIO_BIXBY_PEI_BXBSMUSERVICESPPI_FILECODE                         (0xB866)
#define NBIO_BIXBY_PEI_DXIOCFGPOINTS_FILECODE                             (0xB867)
#define NBIO_BIXBY_PEI_DXIOINIT_FILECODE                                  (0xB868)
#define NBIO_BIXBY_PEI_DXIOTOPOLOGY_FILECODE                              (0xB869)
#define NBIO_BIXBY_PEI_EARLYLINKPPI_FILECODE                              (0xB86A)
#define NBIO_BIXBY_PEI_HIDEPORTS_FILECODE                                 (0xB86B)
#define NBIO_BIXBY_PEI_MEMORYTOP_FILECODE                                 (0xB86C)
#define NBIO_BIXBY_PEI_PCIEHOTPLUG_FILECODE                               (0xB86D)
#define NBIO_BIXBY_PEI_PCIEREMAP_FILECODE                                 (0xB86E)

// PROM21
#define FCH_PROM21_PEI_FCHPROM21PEI_FILECODE                              (0xB8A0)
#define FCH_PROM21_PEI_FCHPROM21PEILOADFW_FILECODE                        (0xB8A1)
#define FCH_PROM21_PEI_FCHPROM21CBSPEI_FILECODE                           (0xB8A2)
#define FCH_PROM21_PEI_FCHPROM21CBSSATAPEI_FILECODE                       (0xB8A3)
#define FCH_PROM21_PEI_FCHPROM21CBSXHCIPEI_FILECODE                       (0xB8A4)
#define FCH_PROM21_PEI_FCHPROM21GPIOPEI_FILECODE                          (0xB8A5)
#define FCH_PROM21_LIBRARY_BASE_FCHPROM21BASELIB_FILECODE                 (0xB8A6)
#define FCH_PROM21_DXE_FCHPROM21DXE_FILECODE                              (0xB8A7)
#define FCH_PROM21_DXE_FCHPROM21GPIODXE_FILECODE                          (0xB8A8)
#define FCH_PROM21_DXE_FCHPROM21SSDTDXE_FILECODE                          (0xB8A9)
#define FCH_PROM21_SMM_FCHPROM21SMM_FILECODE                              (0xB8AA)
#define FCH_PROM21_SMM_FCHPROM21GPIOSMM_FILECODE                          (0xB8AB)
#define FCH_PROM21_SMM_FCHPROM21SMMBTSWPATCH_FILECODE                     (0xB8AC)
#define FCH_PROM21_SMM_FCHPROM21SMMCF9RESETSMI_FILECODE                   (0xB8AD)
#define FCH_PROM21_SMM_FCHPROM21SMMPOWERBUTTONSMI_FILECODE                (0xB8AE)
#define FCH_PROM21_SMM_FCHPROM21SMMSWSMI_FILECODE                         (0xB8AF)
#define FCH_PROM21_SMM_FCHPROM21SMMSXSMI_FILECODE                         (0xB8B0)
#define FCH_PROM21_PEI_FCHPROM21CBSSIPEI_FILECODE                         (0xB8B1)

#define FCH_TURNER_DXE_FCHTURNERDXE_FILECODE                              (0xB8D0)
#define FCH_TURNER_DXE_FCHTURNERSSDTDXE_FILECODE                          (0xB8D1)
#define FCH_TURNER_LIBRARY_BASE_FCHTURNERBASELIB_FILECODE                 (0xB8D2)
#define FCH_TURNER_PEI_FCHTURNERCBSPEI_FILECODE                           (0xB8D3)
#define FCH_TURNER_PEI_FCHTURNERCBSUSB4PEI_FILECODE                       (0xB8D4)
#define FCH_TURNER_PEI_FCHTURNERCBSXHCIPEI_FILECODE                       (0xB8D5)
#define FCH_TURNER_PEI_FCHTURNERPEI_FILECODE                              (0xB8D6)
#define FCH_TURNER_PEI_FCHTURNERPEILOADFW_FILECODE                        (0xB8D7)
#define FCH_TURNER_SMM_FCHTURNERSMM_FILECODE                              (0xB8D8)
#define FCH_TURNER_SMM_FCHTURNERSMMCF9RESETSMI_FILECODE                   (0xB8D9)
#define FCH_TURNER_SMM_FCHTURNERSMMPOWERBUTTONSMI_FILECODE                (0xB8DA)
#define FCH_TURNER_SMM_FCHTURNERSMMSWSMI_FILECODE                         (0xB8DB)
#define FCH_TURNER_SMM_FCHTURNERSMMSXSMI_FILECODE                         (0xB8DC)

#define JEDECNVDIMM_SMM_JEDECNVDIMM_FILECODE                              (0xBC00)
#define JEDECNVDIMM_SMM_JEDECNVDIMMACPI_FILECODE                          (0xBC01)
#define JEDECNVDIMM_SMM_JEDECNVDIMMDSM_FILECODE                           (0xBC02)
#define JEDECNVDIMM_SMM_JEDECNVDIMMSMBUS_FILECODE                         (0xBC03)
#define JEDECNVDIMM_DXE_JEDECNVDIMMDXE_FILECODE                           (0xBC04)
#define JEDECNVDIMM_SMM_JEDECNVDIMMARS_FILECODE                           (0xBC05)

#define FCH_ALISHAN_FCHALISHANCF9RESETDXE_CF9RESET_FILECODE                                                   (0xC650)
#define FCH_ALISHAN_FCHALISHANCORE_COMMON_ACPILIB_FILECODE                                                    (0xC651)
#define FCH_ALISHAN_FCHALISHANCORE_COMMON_FCHAOACLIB_FILECODE                                                 (0xC652)
#define FCH_ALISHAN_FCHALISHANCORE_COMMON_FCHCOMMON_FILECODE                                                  (0xC653)
#define FCH_ALISHAN_FCHALISHANCORE_COMMON_FCHCOMMONSMM_FILECODE                                               (0xC654)
#define FCH_ALISHAN_FCHALISHANCORE_COMMON_FCHLIB_FILECODE                                                     (0xC655)
#define FCH_ALISHAN_FCHALISHANCORE_COMMON_FCHPELIB_FILECODE                                                   (0xC656)
#define FCH_ALISHAN_FCHALISHANCORE_COMMON_FCHSMNLIB_FILECODE                                                  (0xC657)
#define FCH_ALISHAN_FCHALISHANCORE_COMMON_MEMLIB_FILECODE                                                     (0xC658)
#define FCH_ALISHAN_FCHALISHANCORE_COMMON_PCILIB_FILECODE                                                     (0xC659)
#define FCH_ALISHAN_FCHALISHANCORE_ALISHAN_ANESPI_ANESPIENV_FILECODE                                           (0xC65A)
#define FCH_ALISHAN_FCHALISHANCORE_ALISHAN_ANESPI_ANESPILATE_FILECODE                                          (0xC65B)
#define FCH_ALISHAN_FCHALISHANCORE_ALISHAN_ANESPI_ANESPILIB_FILECODE                                           (0xC65C)
#define FCH_ALISHAN_FCHALISHANCORE_ALISHAN_ANESPI_ANESPIMID_FILECODE                                           (0xC65D)
#define FCH_ALISHAN_FCHALISHANCORE_ALISHAN_ANESPI_ANESPIRESET_FILECODE                                         (0xC65E)
#define FCH_ALISHAN_FCHALISHANCORE_ALISHAN_ANHWACPI_ANHWACPIENV_FILECODE                                       (0xC65F)
#define FCH_ALISHAN_FCHALISHANCORE_ALISHAN_ANHWACPI_ANHWACPIENVSERVICE_FILECODE                                (0xC660)
#define FCH_ALISHAN_FCHALISHANCORE_ALISHAN_ANHWACPI_ANHWACPILATE_FILECODE                                      (0xC661)
#define FCH_ALISHAN_FCHALISHANCORE_ALISHAN_ANHWACPI_ANHWACPILATESERVICE_FILECODE                               (0xC662)
#define FCH_ALISHAN_FCHALISHANCORE_ALISHAN_ANHWACPI_ANHWACPIMID_FILECODE                                       (0xC663)
#define FCH_ALISHAN_FCHALISHANCORE_ALISHAN_ANHWACPI_ANHWACPIMIDSERVICE_FILECODE                                (0xC664)
#define FCH_ALISHAN_FCHALISHANCORE_ALISHAN_ANHWACPI_ANHWACPIRESET_FILECODE                                     (0xC665)
#define FCH_ALISHAN_FCHALISHANCORE_ALISHAN_ANHWACPI_ANSSSERVICE_FILECODE                                       (0xC666)
#define FCH_ALISHAN_FCHALISHANCORE_ALISHAN_ANINTERFACE_ANFCHINITENV_FILECODE                                   (0xC667)
#define FCH_ALISHAN_FCHALISHANCORE_ALISHAN_ANINTERFACE_ANFCHINITLATE_FILECODE                                  (0xC668)
#define FCH_ALISHAN_FCHALISHANCORE_ALISHAN_ANINTERFACE_ANFCHINITMID_FILECODE                                   (0xC669)
#define FCH_ALISHAN_FCHALISHANCORE_ALISHAN_ANINTERFACE_ANFCHINITRESET_FILECODE                                 (0xC66A)
#define FCH_ALISHAN_FCHALISHANCORE_ALISHAN_ANINTERFACE_ANFCHINITS3_FILECODE                                    (0xC66B)
#define FCH_ALISHAN_FCHALISHANCORE_ALISHAN_ANINTERFACE_ANFCHTASKLAUNCHER_FILECODE                              (0xC66C)
#define FCH_ALISHAN_FCHALISHANCORE_ALISHAN_ANLPCSPI_ANLPCENV_FILECODE                                          (0xC66D)
#define FCH_ALISHAN_FCHALISHANCORE_ALISHAN_ANLPCSPI_ANLPCENVSERVICE_FILECODE                                   (0xC66E)
#define FCH_ALISHAN_FCHALISHANCORE_ALISHAN_ANLPCSPI_ANLPCLATE_FILECODE                                         (0xC66F)
#define FCH_ALISHAN_FCHALISHANCORE_ALISHAN_ANLPCSPI_ANLPCMID_FILECODE                                          (0xC670)
#define FCH_ALISHAN_FCHALISHANCORE_ALISHAN_ANLPCSPI_ANLPCRESET_FILECODE                                        (0xC671)
#define FCH_ALISHAN_FCHALISHANCORE_ALISHAN_ANLPCSPI_ANLPCRESETSERVICE_FILECODE                                 (0xC672)
#define FCH_ALISHAN_FCHALISHANCORE_ALISHAN_ANLPCSPI_ANSPIENV_FILECODE                                          (0xC673)
#define FCH_ALISHAN_FCHALISHANCORE_ALISHAN_ANLPCSPI_ANSPILATE_FILECODE                                         (0xC674)
#define FCH_ALISHAN_FCHALISHANCORE_ALISHAN_ANLPCSPI_ANSPIMID_FILECODE                                          (0xC675)
#define FCH_ALISHAN_FCHALISHANCORE_ALISHAN_ANLPCSPI_ANSPIRESET_FILECODE                                        (0xC676)
#define FCH_ALISHAN_FCHALISHANCORE_ALISHAN_ANPCIE_ANABENV_FILECODE                                             (0xC677)
#define FCH_ALISHAN_FCHALISHANCORE_ALISHAN_ANPCIE_ANABENVSERVICE_FILECODE                                      (0xC678)
#define FCH_ALISHAN_FCHALISHANCORE_ALISHAN_ANPCIE_ANABLATE_FILECODE                                            (0xC679)
#define FCH_ALISHAN_FCHALISHANCORE_ALISHAN_ANPCIE_ANABMID_FILECODE                                             (0xC67A)
#define FCH_ALISHAN_FCHALISHANCORE_ALISHAN_ANPCIE_ANABRESET_FILECODE                                           (0xC67B)
#define FCH_ALISHAN_FCHALISHANCORE_ALISHAN_ANPCIE_ANABRESETSERVICE_FILECODE                                    (0xC67C)
#define FCH_ALISHAN_FCHALISHANCORE_ALISHAN_ANPCIE_ANABSERVICE_FILECODE                                         (0xC67D)
#define FCH_ALISHAN_FCHALISHANCORE_ALISHAN_ANSD_ANSDENV_FILECODE                                               (0xC67E)
#define FCH_ALISHAN_FCHALISHANCORE_ALISHAN_ANSD_ANSDENVSERVICE_FILECODE                                        (0xC67F)
#define FCH_ALISHAN_FCHALISHANCORE_ALISHAN_ANSD_ANSDLATE_FILECODE                                              (0xC680)
#define FCH_ALISHAN_FCHALISHANCORE_ALISHAN_ANSD_ANSDMID_FILECODE                                               (0xC681)
#define FCH_ALISHAN_FCHALISHANCORE_ALISHAN_ANSD_ANSDRESETSERVICE_FILECODE                                      (0xC682)
#define FCH_ALISHAN_FCHALISHANCORE_ALISHAN_ANSD_ANSDSERVICE_FILECODE                                           (0xC683)
#define FCH_ALISHAN_FCHALISHANCORE_ALISHAN_ANUSB_ANCIOENV_FILECODE                                             (0xC684)
#define FCH_ALISHAN_FCHALISHANCORE_ALISHAN_ANUSB_ANCIOLATE_FILECODE                                            (0xC685)
#define FCH_ALISHAN_FCHALISHANCORE_ALISHAN_ANUSB_ANCIOMID_FILECODE                                             (0xC686)
#define FCH_ALISHAN_FCHALISHANCORE_ALISHAN_ANUSB_ANCIORESET_FILECODE                                           (0xC687)
#define FCH_ALISHAN_FCHALISHANCORE_ALISHAN_ANUSB_ANCIOSERVICE_FILECODE                                         (0xC688)
#define FCH_ALISHAN_FCHALISHANCORE_ALISHAN_ANUSB_ANUSBENV_FILECODE                                             (0xC689)
#define FCH_ALISHAN_FCHALISHANCORE_ALISHAN_ANUSB_ANUSBLATE_FILECODE                                            (0xC68A)
#define FCH_ALISHAN_FCHALISHANCORE_ALISHAN_ANUSB_ANUSBMID_FILECODE                                             (0xC68B)
#define FCH_ALISHAN_FCHALISHANCORE_ALISHAN_ANUSB_ANUSBRESET_FILECODE                                           (0xC68C)
#define FCH_ALISHAN_FCHALISHANCORE_ALISHAN_ANUSB_ANXHCIENV_FILECODE                                            (0xC68D)
#define FCH_ALISHAN_FCHALISHANCORE_ALISHAN_ANUSB_ANXHCILATE_FILECODE                                           (0xC68E)
#define FCH_ALISHAN_FCHALISHANCORE_ALISHAN_ANUSB_ANXHCIMID_FILECODE                                            (0xC68F)
#define FCH_ALISHAN_FCHALISHANCORE_ALISHAN_ANUSB_ANXHCIRECOVERY_FILECODE                                       (0xC690)
#define FCH_ALISHAN_FCHALISHANCORE_ALISHAN_ANUSB_ANXHCIRESET_FILECODE                                          (0xC691)
#define FCH_ALISHAN_FCHALISHANCORE_ALISHAN_ANUSB_ANXHCIRESETSERVICE_FILECODE                                   (0xC692)
#define FCH_ALISHAN_FCHALISHANCORE_ALISHAN_ANUSB_ANXHCISERVICE_FILECODE                                        (0xC693)
#define FCH_ALISHAN_FCHALISHANCORE_ALISHAN_ANWIFI_ANWIFIENV_FILECODE                                           (0xC694)
#define FCH_ALISHAN_FCHALISHANCORE_ALISHAN_ANWIFI_ANWIFILATE_FILECODE                                          (0xC695)
#define FCH_ALISHAN_FCHALISHANCORE_ALISHAN_ANWIFI_ANWIFIMID_FILECODE                                           (0xC696)
#define FCH_ALISHAN_FCHALISHANCORE_ALISHAN_ANWIFI_ANWIFIRESET_FILECODE                                         (0xC697)
#define FCH_ALISHAN_FCHALISHANDXE_FCHDXE_FILECODE                                                             (0xC698)
#define FCH_ALISHAN_FCHALISHANDXE_FCHALISHANDSDT_FILECODE                                                      (0xC699)
#define FCH_ALISHAN_FCHALISHANDXE_FCHALISHANSSDT_FILECODE                                                      (0xC69A)
#define FCH_ALISHAN_FCHALISHANLEGACYINTERRUPTDXE_LEGACYINTERRUPT_FILECODE                                     (0xC69B)
#define FCH_ALISHAN_FCHALISHANPEI_FCHPEI_FILECODE                                                             (0xC69C)
#define FCH_ALISHAN_FCHALISHANPEI_FCHRESET_FILECODE                                                           (0xC69D)
#define FCH_ALISHAN_FCHALISHANPEI_FCHRESET2_FILECODE                                                          (0xC69E)
#define FCH_ALISHAN_FCHALISHANPEI_FCHSTALL_FILECODE                                                           (0xC69F)
#define FCH_ALISHAN_FCHALISHANSMBUSDXE_SMBUSLIGHT_FILECODE                                                    (0xC6A0)
#define FCH_ALISHAN_FCHALISHANSMBUSPEI_SMBUS_FILECODE                                                         (0xC6A1)
#define FCH_ALISHAN_FCHALISHANSMM_FCHSMM_FILECODE                                                             (0xC6A2)
#define FCH_ALISHAN_FCHALISHANSMM_GPISMI_FILECODE                                                             (0xC6A3)
#define FCH_ALISHAN_FCHALISHANSMM_IOTRAPSMI_FILECODE                                                          (0xC6A4)
#define FCH_ALISHAN_FCHALISHANSMM_MISCSMI_FILECODE                                                            (0xC6A5)
#define FCH_ALISHAN_FCHALISHANSMM_PERIODICTIMERSMI_FILECODE                                                   (0xC6A6)
#define FCH_ALISHAN_FCHALISHANSMM_POWERBUTTONSMI_FILECODE                                                     (0xC6A7)
#define FCH_ALISHAN_FCHALISHANSMM_SWSMI_FILECODE                                                              (0xC6A8)
#define FCH_ALISHAN_FCHALISHANSMM_SXSMI_FILECODE                                                              (0xC6A9)
#define FCH_ALISHAN_FCHALISHANSMMCONTROLDXE_SMMCONTROL_FILECODE                                               (0xC6AA)
#define FCH_ALISHAN_FCHALISHANSMMDISPATCHER_FCHSMMDIAGDISPATCHER_FILECODE                                     (0xC6AB)
#define FCH_ALISHAN_FCHALISHANSMMDISPATCHER_FCHSMMDISPATCHER_FILECODE                                         (0xC6AC)
#define FCH_ALISHAN_FCHALISHANSMMDISPATCHER_FCHSMMGPIDISPATCHER_FCHSMMGPIDISPATCHER_FILECODE                  (0xC6AD)
#define FCH_ALISHAN_FCHALISHANSMMDISPATCHER_FCHSMMIOTRAPDISPATCHER_FCHSMMIOTRAPDISPATCHER_FILECODE            (0xC6AE)
#define FCH_ALISHAN_FCHALISHANSMMDISPATCHER_FCHSMMMISCDISPATCHER_FCHSMMMISCDISPATCHER_FILECODE                (0xC6AF)
#define FCH_ALISHAN_FCHALISHANSMMDISPATCHER_FCHSMMPERIODICALDISPATCHER_FCHSMMPERIODICALDISPATCHER_FILECODE    (0xC6B0)
#define FCH_ALISHAN_FCHALISHANSMMDISPATCHER_FCHSMMPWRBTNDISPATCHER_FCHSMMPWRBTNDISPATCHER_FILECODE            (0xC6B1)
#define FCH_ALISHAN_FCHALISHANSMMDISPATCHER_FCHSMMSWDISPATCHER_FCHSMMSWDISPATCHER_FILECODE                    (0xC6B2)
#define FCH_ALISHAN_FCHALISHANSMMDISPATCHER_FCHSMMSXDISPATCHER_FCHSMMSXDISPATCHER_FILECODE                    (0xC6B3)
#define FCH_ALISHAN_FCHALISHANSMMDISPATCHER_FCHSMMUSBDISPATCHER_FCHSMMUSBDISPATCHER_FILECODE                  (0xC6B4)

#define LIBRARY_FCHIDSHOOKKRKLIB_PEI_FCHIDSHOOKKRKLIBPEI_FILECODE                                           (0xC6E0)
#define LIBRARY_FCHIDSHOOKKRKLIB_DXE_FCHIDSHOOKKRKLIBDXE_FILECODE                                           (0xC6E1)

#define LIBRARY_APCBLIB_APCBLIB_FILECODE                                  (0xF100)

// MEMORY
#define LIBRARY_MEM_FEATDMICOLLECTLIB_AMDMEMDMILIB_FILECODE               (0xF104)
#define LIBRARY_MEM_FEATDMICOLLECTLIB_AMDMEMDMID4LIB_FILECODE             (0xF105)
#define LIBRARY_MEM_FEATDMICOLLECTLIB_AMDMEMDMID34LIB_FILECODE            (0xF106)
#define LIBRARY_MEM_MAINLIB_MDEF_FILECODE                                 (0xF101)
#define LIBRARY_MEM_MAINLIB_MINIT_FILECODE                                (0xF102)
#define LIBRARY_MEM_MAINLIB_MM_FILECODE                                   (0xF103)
#define LIBRARY_MEM_MAINLIB_MMECC_FILECODE                                (0xF107)
#define LIBRARY_MEM_MAINLIB_MMEXCLUDEDIMM_FILECODE                        (0xF108)
#define LIBRARY_MEM_MAINLIB_MMNODEINTERLEAVE_FILECODE                     (0xF10B)
#define LIBRARY_MEM_MAINLIB_MMONLINESPARE_FILECODE                        (0xF10C)
#define LIBRARY_MEM_MAINLIB_S3_FILECODE                                   (0xF10E)
#define LIBRARY_MEM_MAINLIB_MUC_FILECODE                                  (0xF10F)
#define LIBRARY_MEM_MAINLIB_MMMEMCLR_FILECODE                             (0xF110)
#define LIBRARY_MEM_MAINLIB_MMFLOW_FILECODE                               (0xF112)
#define LIBRARY_MEM_MAINLIB_MERRHDL_FILECODE                              (0xF113)
#define LIBRARY_MEM_MAINLIB_MMLVDDR3_FILECODE                             (0xF115)
#define LIBRARY_MEM_MAINLIB_MMUMAALLOC_FILECODE                           (0xF116)
#define LIBRARY_MEM_MAINLIB_MMMEMRESTORE_FILECODE                         (0xF117)
#define LIBRARY_MEM_MAINLIB_MMCONDITIONALPSO_FILECODE                     (0xF118)
#define LIBRARY_MEM_MAINLIB_MMAGGRESSOR_FILECODE                          (0xF119)
#define LIBRARY_MEM_MAINLIB_MMLVDDR4_FILECODE                             (0xF11A)
#define LIBRARY_MEM_MAINCZLIB_MMFLOWD3CZ_FILECODE                         (0xF127)
#define LIBRARY_MEM_MAINCZLIB_MMFLOWD4CZ_FILECODE                         (0xF12A)
#define LIBRARY_MEM_MAINCZLIB_MMFLOWD34CZ_FILECODE                        (0xF12B)

#define LIBRARY_MEM_FEATCRATLIB_AMDMEMCRATLIB_FILECODE                    (0xF095)
#define LIBRARY_MEM_FEATS3LIB_MFS3_FILECODE                               (0xF092)
#define LIBRARY_MEM_FEATMEMCLRLIB_MFMEMCLR_FILECODE                       (0xF08B)
#define LIBRARY_MEM_FEATIDENTIFYDIMMLIB_MFIDENDIMM_FILECODE               (0xF088)
#define LIBRARY_MEM_FEATINTLVRGNLIB_MFINTLVRN_FILECODE                    (0xF089)
#define LIBRARY_MEM_FEATTABLELIB_MFTDS_FILECODE                           (0xF093)
#define LIBRARY_MEM_FEATUMALIB_AMDMEMFEATUMALIB_FILECODE                  (0xF094)
#define LIBRARY_MEM_FEATOLSPARELIB_MFSPR_FILECODE                         (0xF08E)
#define LIBRARY_MEM_FEATCSINTLVLIB_MFCSI_FILECODE                         (0xF082)
#define LIBRARY_MEM_FEATCHINTLVLIB_MFCHI_FILECODE                         (0xF081)
#define LIBRARY_MEM_FEATCHINTLVLIB_MFMCHI_FILECODE                        (0xF084)
#define LIBRARY_MEM_FEATECCLIB_MFECC_FILECODE                             (0xF083)
#define LIBRARY_MEM_FEATECCLIB_MFEMP_FILECODE                             (0xF085)
#define LIBRARY_MEM_FEATNODEINTLVLIB_MFNDI_FILECODE                       (0xF08C)
#define LIBRARY_MEM_FEATLVDDR3LIB_MFLVDDR3_FILECODE                       (0xF08A)
#define LIBRARY_MEM_FEATONDIMMTHERMALLIB_MFODTHERMAL_FILECODE             (0xF08D)
#define LIBRARY_MEM_FEATEXCLUDEDIMMLIB_MFDIMMEXCLUD_FILECODE              (0xF086)
#define LIBRARY_MEM_FEATAMPLIB_MFAMP_FILECODE                             (0xF096)

#define LIBRARY_MEM_NBCZLIB_MNCZ_FILECODE                                 (0xF289)
#define LIBRARY_MEM_NBCZLIB_MND3CZ_FILECODE                               (0xF28A)
#define LIBRARY_MEM_NBCZLIB_MND4CZ_FILECODE                               (0xF28B)
#define LIBRARY_MEM_NBCZLIB_MNDCTCZ_FILECODE                              (0xF28C)
#define LIBRARY_MEM_NBCZLIB_MNDCTD3CZ_FILECODE                            (0xF28D)
#define LIBRARY_MEM_NBCZLIB_MNDCTD4CZ_FILECODE                            (0xF28E)
#define LIBRARY_MEM_NBCZLIB_MNIDENDIMMCZ_FILECODE                         (0xF28F)
#define LIBRARY_MEM_NBCZLIB_MNMCTCZ_FILECODE                              (0xF290)
#define LIBRARY_MEM_NBCZLIB_MNPHYCZ_FILECODE                              (0xF291)
#define LIBRARY_MEM_NBCZLIB_MNPHYD3CZ_FILECODE                            (0xF292)
#define LIBRARY_MEM_NBCZLIB_MNPHYD4CZ_FILECODE                            (0xF293)
#define LIBRARY_MEM_NBCZLIB_MNPMUCZ_FILECODE                              (0xF294)
#define LIBRARY_MEM_NBCZLIB_MNPMUD3CZ_FILECODE                            (0xF295)
#define LIBRARY_MEM_NBCZLIB_MNPMUD4CZ_FILECODE                            (0xF296)
#define LIBRARY_MEM_NBCZLIB_MNPMUSRAMMSGBLOCKCZ_FILECODE                  (0xF297)
#define LIBRARY_MEM_NBCZLIB_MNPROTOCZ_FILECODE                            (0xF298)
#define LIBRARY_MEM_NBCZLIB_MNREGCZ_FILECODE                              (0xF299)
#define LIBRARY_MEM_NBCZLIB_MNS3CZ_FILECODE                               (0xF29A)
#define LIBRARY_MEM_NBCZLIB_MNPSPCZ_FILECODE                              (0xF29B)

#define LIBRARY_MEM_PSCZLIB_MPCZ3_FILECODE                                (0xF445)
#define LIBRARY_MEM_PSCZLIB_MPSCZ3_FILECODE                               (0xF446)
#define LIBRARY_MEM_PSCZLIB_MPUCZ3_FILECODE                               (0xF447)
#define LIBRARY_MEM_PSCZLIB_MPCZ4_FILECODE                                (0xF44C)
#define LIBRARY_MEM_PSCZLIB_MPSCZ4_FILECODE                               (0xF44D)
#define LIBRARY_MEM_PSCZFP4LIB_MPSCZFP4D3_FILECODE                        (0xF44A)
#define LIBRARY_MEM_PSCZFP4LIB_MPUCZFP4D3_FILECODE                        (0xF44B)
#define LIBRARY_MEM_PSCZFP4LIB_MPSCZFP4D4_FILECODE                        (0xF44F)
#define LIBRARY_MEM_PSCZFP4LIB_MPUCZFP4D4_FILECODE                        (0xF450)
#define LIBRARY_MEM_PSCZAM4LIB_MPUCZAM4D3_FILECODE                        (0xF458)
#define LIBRARY_MEM_PSCZAM4LIB_MPUCZAM4D4_FILECODE                        (0xF459)
#define LIBRARY_MEM_PSCZLIB_MPUCZ4_FILECODE                               (0xF45A)
#define LIBRARY_MEM_PSCZAM4LIB_MPSCZAM4D4_FILECODE                        (0xF45B)

#define LIBRARY_MEM_PSLIB_MP_FILECODE                                     (0xF401)
#define LIBRARY_MEM_PSLIB_MPRTT_FILECODE                                  (0xF422)
#define LIBRARY_MEM_PSLIB_MPMAXFREQ_FILECODE                              (0xF423)
#define LIBRARY_MEM_PSLIB_MPODTPAT_FILECODE                               (0xF424)
#define LIBRARY_MEM_PSLIB_MPSAO_FILECODE                                  (0xF425)
#define LIBRARY_MEM_PSLIB_MPMR0_FILECODE                                  (0xF426)
#define LIBRARY_MEM_PSLIB_MPRC2IBT_FILECODE                               (0xF427)
#define LIBRARY_MEM_PSLIB_MPRC10OPSPD_FILECODE                            (0xF428)
#define LIBRARY_MEM_PSLIB_MPLRIBT_FILECODE                                (0xF429)
#define LIBRARY_MEM_PSLIB_MPLRNPR_FILECODE                                (0xF42A)
#define LIBRARY_MEM_PSLIB_MPLRNLR_FILECODE                                (0xF42B)
#define LIBRARY_MEM_PSLIB_MPS2D_FILECODE                                  (0xF436)
#define LIBRARY_MEM_PSLIB_MPSEEDS_FILECODE                                (0xF437)
#define LIBRARY_MEM_PSLIB_MPCADCFG_FILECODE                               (0xF43C)
#define LIBRARY_MEM_PSLIB_MPDATACFGD3_FILECODE                            (0xF43D)
#define LIBRARY_MEM_PSLIB_MPDATACFGD4_FILECODE                            (0xF43E)

#define LIBRARY_MEM_S3INITLIB_S3INIT_FILECODE                             (0xF451)

#define LIBRARY_MEM_SERVICESCZLIB_AMDMEMSERVICESCZLIB_FILECODE            (0xF461)

#define LIBRARY_MEM_SERVICESLIB_AMDMEMSERVICESLIB_FILECODE                (0xF471)

#define LIBRARY_MEM_SERVICESSMLIB_AMDMEMSERVICESSMLIB_FILECODE            (0xF481)

#define LIBRARY_MEM_TECHLIB_MT_FILECODE                                   (0xF501)
#define LIBRARY_MEM_TECHLIB_MTHDI_FILECODE                                (0xF502)
#define LIBRARY_MEM_TECHLIB_MTTDIMBT_FILECODE                             (0xF504)
#define LIBRARY_MEM_TECHLIB_MTTECC_FILECODE                               (0xF505)
#define LIBRARY_MEM_TECHLIB_MTTHRC_FILECODE                               (0xF506)
#define LIBRARY_MEM_TECHLIB_MTTML_FILECODE                                (0xF507)
#define LIBRARY_MEM_TECHLIB_MTTOPTSRC_FILECODE                            (0xF509)
#define LIBRARY_MEM_TECHLIB_MTTSRC_FILECODE                               (0xF50B)
#define LIBRARY_MEM_TECHLIB_MTTEDGEDETECT_FILECODE                        (0xF50C)
#define LIBRARY_MEM_TECHLIB_MTTHRCSEEDTRAIN_FILECODE                      (0xF58A)
#define LIBRARY_MEM_TECHLIB_MTTRDDQS2DTRAINING_FILECODE                   (0xF58B)
#define LIBRARY_MEM_TECHLIB_MTTRDDQS2DEYERIMSEARCH_FILECODE               (0xF58C)

#define LIBRARY_MEM_TECHDDR3LIB_MT3_FILECODE                              (0xF581)
#define LIBRARY_MEM_TECHDDR3LIB_MTOT3_FILECODE                            (0xF583)
#define LIBRARY_MEM_TECHDDR3LIB_MTRCI3_FILECODE                           (0xF584)
#define LIBRARY_MEM_TECHDDR3LIB_MTSDI3_FILECODE                           (0xF585)
#define LIBRARY_MEM_TECHDDR3LIB_MTSPD3_FILECODE                           (0xF586)
#define LIBRARY_MEM_TECHDDR3LIB_MTTWL3_FILECODE                           (0xF587)
#define LIBRARY_MEM_TECHDDR3LIB_MTTECC3_FILECODE                          (0xF588)
#define LIBRARY_MEM_TECHDDR3LIB_MTLRDIMM3_FILECODE                        (0xF589)

#define LIBRARY_MEM_TECHDDR4LIB_MT4_FILECODE                              (0xF58D)
#define LIBRARY_MEM_TECHDDR4LIB_MTSPD4_FILECODE                           (0xF58E)

#define LIBRARY_MEM_ARDKLIB_MA_FILECODE                                   (0xF001)

#define LIBRARY_MEM_NBLIB_MN_FILECODE                                     (0xF27C)
#define LIBRARY_MEM_NBLIB_MNDCT_FILECODE                                  (0xF27D)
#define LIBRARY_MEM_NBLIB_MNPHY_FILECODE                                  (0xF27E)
#define LIBRARY_MEM_NBLIB_MNMCT_FILECODE                                  (0xF27F)
#define LIBRARY_MEM_NBLIB_MNS3_FILECODE                                   (0xF280)
#define LIBRARY_MEM_NBLIB_MNFLOW_FILECODE                                 (0xF281)
#define LIBRARY_MEM_NBLIB_MNFEAT_FILECODE                                 (0xF282)
#define LIBRARY_MEM_NBLIB_MNREG_FILECODE                                  (0xF285)
#define LIBRARY_MEM_NBLIB_MNPMU_FILECODE                                  (0xF286)
#define LIBRARY_MEM_NBLIB_MNMRSD3_FILECODE                                (0xF287)
#define LIBRARY_MEM_NBLIB_MNMRSD4_FILECODE                                (0xF288)

#define LIBRARY_MEM_ERRORLOGLIB_MEMERRORLOG_FILECODE                      (0xF300)
#define LIBRARY_HARDCODEDMEMCOMMONSERVICELIB_HARDCODEDMEMCOMMONSERVICELIB_FILECODE (0xF301)

#define LIBRARY_MEM_FEATDMICONSTRUCTLIB_MEMDMICONSTRUCTLIB_FILECODE       (0xF302)

#define LIBRARY_MEM_MEMIDSHOOKCZLIB_MEMIDSHOOKCZLIB_FILECODE              (0xF303)
#define LIBRARY_MEM_MEMIDSHOOKCZLIB_INTERNAL_MEMIDSHOOKCZLIBINT_FILECODE  (0xF304)
#define LIBRARY_AMDCALLOUTLIB_AGESAREADSPD_FILECODE                       (0xF305)
#define LIBRARY_AMDCALLOUTLIB_AGESAHOOKBEFOREEXITSELFREFRESH_FILECODE     (0xF306)
#define LIBRARY_AMDCALLOUTLIB_AGESAS3SAVE_FILECODE                        (0xF307)
#define LIBRARY_AMDCALLOUTLIB_AGESAHOOKBEFOREDRAMINIT_FILECODE            (0xF308)

#define LIBRARY_MEM_NULL_ARDKLIBNULL_MA_FILECODE                          (0xF600)
#define LIBRARY_MEM_NULL_ERRORLOGLIBNULL_MEMERRORLOG_FILECODE             (0xF601)
#define LIBRARY_MEM_NULL_FEATAMPLIBNULL_MFAMP_FILECODE                    (0xF602)
#define LIBRARY_MEM_NULL_FEATCHINTLVLIBNULL_MFCHI_FILECODE                (0xF603)
#define LIBRARY_MEM_NULL_FEATCHINTLVLIBNULL_MFMCHI_FILECODE               (0xF604)
#define LIBRARY_MEM_NULL_FEATCRATLIBNULL_AMDMEMCRATLIB_FILECODE           (0xF605)
#define LIBRARY_MEM_NULL_FEATCSINTLVLIBNULL_MFCSI_FILECODE                (0xF606)
#define LIBRARY_MEM_NULL_FEATDMICOLLECTLIBNULL_AMDMEMDMILIB_FILECODE      (0xF609)
#define LIBRARY_MEM_NULL_FEATECCLIBNULL_MFECC_FILECODE                    (0xF60A)
#define LIBRARY_MEM_NULL_FEATECCLIBNULL_MFEMP_FILECODE                    (0xF60B)
#define LIBRARY_MEM_NULL_FEATEXCLUDEDIMMLIBNULL_MFDIMMEXCLUD_FILECODE     (0xF60C)
#define LIBRARY_MEM_NULL_FEATIDENTIFYDIMMLIBNULL_MFIDENDIMM_FILECODE      (0xF60D)
#define LIBRARY_MEM_NULL_FEATINTLVRGNLIBNULL_MFINTLVRN_FILECODE           (0xF60E)
#define LIBRARY_MEM_NULL_FEATLVDDR3LIBNULL_MFLVDDR3_FILECODE              (0xF60F)
#define LIBRARY_MEM_NULL_FEATMEMCLRLIBNULL_MFMEMCLR_FILECODE              (0xF610)
#define LIBRARY_MEM_NULL_FEATNODEINTLVLIBNULL_MFNDI_FILECODE              (0xF611)
#define LIBRARY_MEM_NULL_FEATOLSPARELIBNULL_MFSPR_FILECODE                (0xF612)
#define LIBRARY_MEM_NULL_FEATONDIMMTHERMALLIBNULL_MFODTHERMAL_FILECODE    (0xF613)
#define LIBRARY_MEM_NULL_FEATS3LIBNULL_MFS3_FILECODE                      (0xF614)
#define LIBRARY_MEM_NULL_FEATTABLELIBNULL_MFTDS_FILECODE                  (0xF615)
#define LIBRARY_MEM_NULL_FEATUMALIBNULL_AMDMEMFEATUMALIB_FILECODE         (0xF616)
#define LIBRARY_MEM_NULL_MAINCZLIBNULL_MMFLOWD3CZ_FILECODE                (0xF617)
#define LIBRARY_MEM_NULL_MAINCZLIBNULL_MMFLOWD4CZ_FILECODE                (0xF618)
#define LIBRARY_MEM_NULL_MAINCZLIBNULL_MMFLOWD34CZ_FILECODE               (0xF619)
#define LIBRARY_MEM_NULL_MAINLIBNULL_AMDMEMMAINLIB_FILECODE               (0xF61A)
#define LIBRARY_MEM_NULL_MAINLIBNULL_MDEF_FILECODE                        (0xF61B)
#define LIBRARY_MEM_NULL_MAINLIBNULL_MERRHDL_FILECODE                     (0xF61C)
#define LIBRARY_MEM_NULL_MAINLIBNULL_MINIT_FILECODE                       (0xF61D)
#define LIBRARY_MEM_NULL_MAINLIBNULL_MM_FILECODE                          (0xF61E)
#define LIBRARY_MEM_NULL_MAINLIBNULL_MMAGGRESSOR_FILECODE                 (0xF61F)
#define LIBRARY_MEM_NULL_MAINLIBNULL_MMCONDITIONALPSO_FILECODE            (0xF620)
#define LIBRARY_MEM_NULL_MAINLIBNULL_MMECC_FILECODE                       (0xF621)
#define LIBRARY_MEM_NULL_MAINLIBNULL_MMEXCLUDEDIMM_FILECODE               (0xF622)
#define LIBRARY_MEM_NULL_MAINLIBNULL_MMFLOW_FILECODE                      (0xF623)
#define LIBRARY_MEM_NULL_MAINLIBNULL_MMLVDDR3_FILECODE                    (0xF624)
#define LIBRARY_MEM_NULL_MAINLIBNULL_MMLVDDR4_FILECODE                    (0xF625)
#define LIBRARY_MEM_NULL_MAINLIBNULL_MMMEMCLR_FILECODE                    (0xF626)
#define LIBRARY_MEM_NULL_MAINLIBNULL_MMMEMRESTORE_FILECODE                (0xF627)
#define LIBRARY_MEM_NULL_MAINLIBNULL_MMNODEINTERLEAVE_FILECODE            (0xF628)
#define LIBRARY_MEM_NULL_MAINLIBNULL_MMONLINESPARE_FILECODE               (0xF629)
#define LIBRARY_MEM_NULL_MAINLIBNULL_MMUMAALLOC_FILECODE                  (0xF62A)
#define LIBRARY_MEM_NULL_MAINLIBNULL_MUC_FILECODE                         (0xF62B)
#define LIBRARY_MEM_NULL_MAINLIBNULL_S3_FILECODE                          (0xF62C)
#define LIBRARY_MEM_NULL_NBCZLIBNULL_MNCZ_FILECODE                        (0xF62D)
#define LIBRARY_MEM_NULL_NBCZLIBNULL_MND3CZ_FILECODE                      (0xF62E)
#define LIBRARY_MEM_NULL_NBCZLIBNULL_MND4CZ_FILECODE                      (0xF62F)
#define LIBRARY_MEM_NULL_NBCZLIBNULL_MNDCTCZ_FILECODE                     (0xF630)
#define LIBRARY_MEM_NULL_NBCZLIBNULL_MNDCTD3CZ_FILECODE                   (0xF631)
#define LIBRARY_MEM_NULL_NBCZLIBNULL_MNDCTD4CZ_FILECODE                   (0xF632)
#define LIBRARY_MEM_NULL_NBCZLIBNULL_MNIDENDIMMCZ_FILECODE                (0xF633)
#define LIBRARY_MEM_NULL_NBCZLIBNULL_MNMCTCZ_FILECODE                     (0xF634)
#define LIBRARY_MEM_NULL_NBCZLIBNULL_MNPHYCZ_FILECODE                     (0xF635)
#define LIBRARY_MEM_NULL_NBCZLIBNULL_MNPHYD3CZ_FILECODE                   (0xF636)
#define LIBRARY_MEM_NULL_NBCZLIBNULL_MNPHYD4CZ_FILECODE                   (0xF637)
#define LIBRARY_MEM_NULL_NBCZLIBNULL_MNPMUCZ_FILECODE                     (0xF638)
#define LIBRARY_MEM_NULL_NBCZLIBNULL_MNPMUD3CZ_FILECODE                   (0xF639)
#define LIBRARY_MEM_NULL_NBCZLIBNULL_MNPMUD4CZ_FILECODE                   (0xF63A)
#define LIBRARY_MEM_NULL_NBCZLIBNULL_MNPMUSRAMMSGBLOCKCZ_FILECODE         (0xF63B)
#define LIBRARY_MEM_NULL_NBCZLIBNULL_MNPROTOCZ_FILECODE                   (0xF63C)
#define LIBRARY_MEM_NULL_NBCZLIBNULL_MNPSPCZ_FILECODE                     (0xF63D)
#define LIBRARY_MEM_NULL_NBCZLIBNULL_MNREGCZ_FILECODE                     (0xF63E)
#define LIBRARY_MEM_NULL_NBCZLIBNULL_MNS3CZ_FILECODE                      (0xF63F)
#define LIBRARY_MEM_NULL_NBLIBNULL_MN_FILECODE                            (0xF640)
#define LIBRARY_MEM_NULL_NBLIBNULL_MNDCT_FILECODE                         (0xF641)
#define LIBRARY_MEM_NULL_NBLIBNULL_MNFEAT_FILECODE                        (0xF642)
#define LIBRARY_MEM_NULL_NBLIBNULL_MNFLOW_FILECODE                        (0xF643)
#define LIBRARY_MEM_NULL_NBLIBNULL_MNMCT_FILECODE                         (0xF644)
#define LIBRARY_MEM_NULL_NBLIBNULL_MNMRSD3_FILECODE                       (0xF645)
#define LIBRARY_MEM_NULL_NBLIBNULL_MNMRSD4_FILECODE                       (0xF646)
#define LIBRARY_MEM_NULL_NBLIBNULL_MNPHY_FILECODE                         (0xF647)
#define LIBRARY_MEM_NULL_NBLIBNULL_MNPMU_FILECODE                         (0xF648)
#define LIBRARY_MEM_NULL_NBLIBNULL_MNREG_FILECODE                         (0xF649)
#define LIBRARY_MEM_NULL_NBLIBNULL_MNS3_FILECODE                          (0xF64A)
#define LIBRARY_MEM_NULL_NBLIBNULL_MNTRAIN3_FILECODE                      (0xF64B)
#define LIBRARY_MEM_NULL_PSCZAM4LIBNULL_MPUCZAM4D3_FILECODE               (0xF64C)
#define LIBRARY_MEM_NULL_PSCZAM4LIBNULL_MPUCZAM4D4_FILECODE               (0xF64D)
#define LIBRARY_MEM_NULL_PSCZFP4LIBNULL_MPSCZFP4D3_FILECODE               (0xF64E)
#define LIBRARY_MEM_NULL_PSCZFP4LIBNULL_MPSCZFP4D4_FILECODE               (0xF64F)
#define LIBRARY_MEM_NULL_PSCZFP4LIBNULL_MPUCZFP4D3_FILECODE               (0xF650)
#define LIBRARY_MEM_NULL_PSCZFP4LIBNULL_MPUCZFP4D4_FILECODE               (0xF651)
#define LIBRARY_MEM_NULL_PSCZLIBNULL_MPCZ3_FILECODE                       (0xF652)
#define LIBRARY_MEM_NULL_PSCZLIBNULL_MPCZ4_FILECODE                       (0xF653)
#define LIBRARY_MEM_NULL_PSCZLIBNULL_MPSCZ3_FILECODE                      (0xF654)
#define LIBRARY_MEM_NULL_PSCZLIBNULL_MPSCZ4_FILECODE                      (0xF655)
#define LIBRARY_MEM_NULL_PSCZLIBNULL_MPUCZ3_FILECODE                      (0xF656)
#define LIBRARY_MEM_NULL_PSLIBNULL_MP_FILECODE                            (0xF657)
#define LIBRARY_MEM_NULL_PSLIBNULL_MPCADCFG_FILECODE                      (0xF658)
#define LIBRARY_MEM_NULL_PSLIBNULL_MPDATACFGD3_FILECODE                   (0xF659)
#define LIBRARY_MEM_NULL_PSLIBNULL_MPDATACFGD4_FILECODE                   (0xF65A)
#define LIBRARY_MEM_NULL_PSLIBNULL_MPLRIBT_FILECODE                       (0xF65B)
#define LIBRARY_MEM_NULL_PSLIBNULL_MPLRNLR_FILECODE                       (0xF65C)
#define LIBRARY_MEM_NULL_PSLIBNULL_MPLRNPR_FILECODE                       (0xF65D)
#define LIBRARY_MEM_NULL_PSLIBNULL_MPMAXFREQ_FILECODE                     (0xF65E)
#define LIBRARY_MEM_NULL_PSLIBNULL_MPMR0_FILECODE                         (0xF65F)
#define LIBRARY_MEM_NULL_PSLIBNULL_MPODTPAT_FILECODE                      (0xF660)
#define LIBRARY_MEM_NULL_PSLIBNULL_MPRC2IBT_FILECODE                      (0xF661)
#define LIBRARY_MEM_NULL_PSLIBNULL_MPRC10OPSPD_FILECODE                   (0xF662)
#define LIBRARY_MEM_NULL_PSLIBNULL_MPRTT_FILECODE                         (0xF663)
#define LIBRARY_MEM_NULL_PSLIBNULL_MPS2D_FILECODE                         (0xF664)
#define LIBRARY_MEM_NULL_PSLIBNULL_MPSAO_FILECODE                         (0xF665)
#define LIBRARY_MEM_NULL_PSLIBNULL_MPSEEDS_FILECODE                       (0xF666)
#define LIBRARY_MEM_NULL_TECHDDR3LIBNULL_MT3_FILECODE                     (0xF667)
#define LIBRARY_MEM_NULL_TECHDDR3LIBNULL_MTLRDIMM3_FILECODE               (0xF668)
#define LIBRARY_MEM_NULL_TECHDDR3LIBNULL_MTOT3_FILECODE                   (0xF669)
#define LIBRARY_MEM_NULL_TECHDDR3LIBNULL_MTRCI3_FILECODE                  (0xF66A)
#define LIBRARY_MEM_NULL_TECHDDR3LIBNULL_MTSDI3_FILECODE                  (0xF66B)
#define LIBRARY_MEM_NULL_TECHDDR3LIBNULL_MTSPD3_FILECODE                  (0xF66C)
#define LIBRARY_MEM_NULL_TECHDDR3LIBNULL_MTTECC3_FILECODE                 (0xF66D)
#define LIBRARY_MEM_NULL_TECHDDR3LIBNULL_MTTWL3_FILECODE                  (0xF66E)
#define LIBRARY_MEM_NULL_TECHDDR4LIBNULL_MT4_FILECODE                     (0xF66F)
#define LIBRARY_MEM_NULL_TECHDDR4LIBNULL_MTSPD4_FILECODE                  (0xF670)
#define LIBRARY_MEM_NULL_TECHLIBNULL_MT_FILECODE                          (0xF671)
#define LIBRARY_MEM_NULL_TECHLIBNULL_MTHDI_FILECODE                       (0xF672)
#define LIBRARY_MEM_NULL_TECHLIBNULL_MTTDIMBT_FILECODE                    (0xF673)
#define LIBRARY_MEM_NULL_TECHLIBNULL_MTTECC_FILECODE                      (0xF674)
#define LIBRARY_MEM_NULL_TECHLIBNULL_MTTEDGEDETECT_FILECODE               (0xF675)
#define LIBRARY_MEM_NULL_TECHLIBNULL_MTTHRC_FILECODE                      (0xF676)
#define LIBRARY_MEM_NULL_TECHLIBNULL_MTTHRCSEEDTRAIN_FILECODE             (0xF677)
#define LIBRARY_MEM_NULL_TECHLIBNULL_MTTML_FILECODE                       (0xF678)
#define LIBRARY_MEM_NULL_TECHLIBNULL_MTTOPTSRC_FILECODE                   (0xF679)
#define LIBRARY_MEM_NULL_TECHLIBNULL_MTTRDDQS2DEYERIMSEARCH_FILECODE      (0xF67A)
#define LIBRARY_MEM_NULL_TECHLIBNULL_MTTRDDQS2DTRAINING_FILECODE          (0xF67B)
#define LIBRARY_MEM_NULL_TECHLIBNULL_MTTSRC_FILECODE                      (0xF67C)

#define LIBRARY_MEMSMBIOSV2LIB_MEMSMBIOSV2LIB_FILECODE                    (0xF67D)
#define LIBRARY_MEM_NULL_FEATDMICOLLECTLIBNULL_AMDMEMDMID34LIB_FILECODE   (0xF67E)

#define LIBRARY_MEM_NULL_FEATDMICOLLECTLIBNULL_AMDMEMDMID4LIB_FILECODE    (0xF680)
#define LIBRARY_MEMRESTORELIB_MEMRESTORELIB_FILECODE                      (0xF681)
#define LIBRARY_MEMSMBIOSV2ZPLIB_MEMSMBIOSV2LIB_FILECODE                  (0xF682)
#define LIBRARY_MEMSMBIOSV2ZPMCMLIB_MEMSMBIOSV2LIB_FILECODE               (0xF683)
#define LIBRARY_MEMSMBIOSV2SSPLIB_MEMSMBIOSV2LIB_FILECODE                 (0xF684)
#define LIBRARY_MEMSMBIOSV2RMBLPD5LIB_MEMSMBIOSV2LIB_FILECODE             (0xF685)
#define LIBRARY_MEMSMBIOSV2RMBD5LIB_MEMSMBIOSV2LIB_FILECODE               (0xF686)
#define LIBRARY_MEMSMBIOSV2PHXLPD5LIB_MEMSMBIOSV2LIB_FILECODE             (0xF687)
#define LIBRARY_MEMSMBIOSV2PHXD5LIB_MEMSMBIOSV2LIB_FILECODE               (0xF688)
#define LIBRARY_MEMSMBIOSV2RSD5LIB_MEMSMBIOSV2LIB_FILECODE                (0xF689)
#define LIBRARY_MEMSMBIOSV2MDNLPD5LIB_MEMSMBIOSV2LIB_FILECODE             (0xF68A)
#define LIBRARY_MEMSMBIOSV2STXLPD5LIB_MEMSMBIOSV2LIB_FILECODE             (0xF68B)
#define LIBRARY_MEMSMBIOSV2STXHLPD5LIB_MEMSMBIOSV2LIB_FILECODE           (0xF68C)
#define LIBRARY_MEMSMBIOSV2KRKLPD5LIB_MEMSMBIOSV2LIB_FILECODE             (0xF68D)
#define LIBRARY_MEMSMBIOSV2KRKD5LIB_MEMSMBIOSV2LIB_FILECODE               (0xF68E)
#define LIBRARY_MEMSMBIOSV2STPD5LIB_MEMSMBIOSV2LIB_FILECODE               (0xF68F)

#define LIBRARY_MEMSMBIOSV2LIB_MEMSMBIOS320V2LIB_FILECODE                 (0xF690)
#define LIBRARY_MEMSMBIOSV2ZPLIB_MEMSMBIOS320V2LIB_FILECODE               (0xF691)
#define LIBRARY_MEMSMBIOSV2SSPLIB_MEMSMBIOS320V2LIB_FILECODE              (0xF692)
#define LIBRARY_MEMSMBIOSV2RMBLPD5LIB_MEMSMBIOS320V2LIB_FILECODE          (0xF693)
#define LIBRARY_MEMSMBIOSV2RMBD5LIB_MEMSMBIOS320V2LIB_FILECODE            (0xF694)
#define LIBRARY_MEMSMBIOSV2PHXLPD5LIB_MEMSMBIOS320V2LIB_FILECODE          (0xF695)
#define LIBRARY_MEMSMBIOSV2PHXD5LIB_MEMSMBIOS320V2LIB_FILECODE            (0xF696)
#define LIBRARY_MEMSMBIOSV2RSD5LIB_MEMSMBIOS320V2LIB_FILECODE             (0xF697)
#define LIBRARY_MEMSMBIOSV2MI3HBMLIB_MEMSMBIOSV2LIB_FILECODE              (0xF698)
#define LIBRARY_MEMSMBIOSV2MI3HBMLIB_MEMSMBIOS320V2LIB_FILECODE           (0xF699)
#define LIBRARY_MEMSMBIOSV2RPLD5LIB_MEMSMBIOS320V2LIB_FILECODE            (0xF69A)
#define LIBRARY_MEMSMBIOSV2RPLD5LIB_MEMSMBIOSV2LIB_FILECODE               (0xF69B)
#define LIBRARY_MEMSMBIOSV2MDNLPD5LIB_MEMSMBIOS320V2LIB_FILECODE          (0xF69C)
#define LIBRARY_MEMSMBIOSV2STXLPD5LIB_MEMSMBIOS320V2LIB_FILECODE          (0xF69D)
#define LIBRARY_MEMSMBIOSV2STXHLPD5LIB_MEMSMBIOS320V2LIB_FILECODE        (0xF69E)
#define LIBRARY_MEMSMBIOSV2KRKLPD5LIB_MEMSMBIOS320V2LIB_FILECODE          (0xF69F)
#define LIBRARY_MEMSMBIOSV2KRKD5LIB_MEMSMBIOS320V2LIB_FILECODE            (0xF6A0)
#define LIBRARY_MEMSMBIOSV2STXD5LIB_MEMSMBIOS320V2LIB_FILECODE            (0xF6A1)
#define LIBRARY_MEMSMBIOSV2STXD5LIB_MEMSMBIOSV2LIB_FILECODE               (0xF6A2)
#define LIBRARY_MEMSMBIOSV2STPD5LIB_MEMSMBIOS320V2LIB_FILECODE            (0xF6A3)

#define LIBRARY_OPTIONMEMORYINSTALLCZLIB_OPTIONMEMORYINSTALLCZ_FILECODE   (0xF700)
#define LIBRARY_MEMSMBIOSV2BRHD5LIB_MEMSMBIOSV2LIB_FILECODE               (0xF701)
#define LIBRARY_MEMSMBIOSV2BRHD5LIB_MEMSMBIOS320V2LIB_FILECODE            (0xF702)

#define LIBRARY_PMMPDMAARSLIB_RS_PMMPDMARSARSLIB_FILECODE                 (0xF800)
#define LIBRARY_PMMPDMAARSLIB_BRH_PMMPDMABRHARSLIB_FILECODE               (0xF801)

#define LIBRARY_MEMUMCACCESSLIB_MEMUMCACCESSLIB_FILECODE                  (0xF900)

// ZP SOC Drivers
#define SOC_AMDSOCAM4BRPEI_AMDSOCAM4BRPEI_FILECODE                        (0x5202)
#define SOC_AMDSOCAM4BRDXE_AMDSOCAM4BRDXE_FILECODE                        (0x5203)
#define SOC_AMDSOCAM4SMPEI_AMDSOCAM4SMPEI_FILECODE                        (0x5204)
#define SOC_AMDSOCAM4SMDXE_AMDSOCAM4SMDXE_FILECODE                        (0x5205)
#define SOC_AMDSOCFP5RVPEI_AMDSOCFP5RVPEI_FILECODE                        (0x5206)
#define SOC_AMDSOCFP5RVDXE_AMDSOCFP5RVDXE_FILECODE                        (0x5207)
#define SOC_AMDSOCSP3ZPPEI_AMDSOCSP3ZPPEI_FILECODE                        (0x5208)
#define SOC_AMDSOCSP3ZPDXE_AMDSOCSP3ZPDXE_FILECODE                        (0x5209)
#define SOC_AMDSOCSP4ZPPEI_AMDSOCSP4ZPPEI_FILECODE                        (0x520A)
#define SOC_AMDSOCSP4ZPDXE_AMDSOCSP4ZPDXE_FILECODE                        (0x520B)
#define SOC_AMDSOCAM4RVPEI_AMDSOCAM4RVPEI_FILECODE                        (0x520C)
#define SOC_AMDSOCAM4RVDXE_AMDSOCAM4RVDXE_FILECODE                        (0x520D)
#define SOC_AMDSOCSP3R2TRPEI_AMDSOCSP3R2TRPEI_FILECODE                    (0x520E)
#define SOC_AMDSOCSP3R2TRDXE_AMDSOCSP3R2TRDXE_FILECODE                    (0x520F)
#define SOC_AMDSOCSP4SMPEI_AMDSOCSP4SMPEI_FILECODE                        (0x5210)
#define SOC_AMDSOCSP4SMDXE_AMDSOCSP4SMDXE_FILECODE                        (0x5211)
#define SOC_AMDSOCFT5RVPEI_AMDSOCFT5RVPEI_FILECODE                        (0x5212)
#define SOC_AMDSOCFT5RVDXE_AMDSOCFT5RVDXE_FILECODE                        (0x5213)

// SSP SOC Driver
#define SOC_AMDSOCAM4MTSPEI_AMDSOCAM4MTSPEI_FILECODE                      (0x5220)
#define SOC_AMDSOCAM4MTSDXE_AMDSOCAM4MTSDXE_FILECODE                      (0x5221)
#define SOC_AMDSOCSP3RMPEI_AMDSOCSP3RMPEI_FILECODE                        (0x5222)
#define SOC_AMDSOCSP3RMDXE_AMDSOCSP3RMDXE_FILECODE                        (0x5223)
#define SOC_AMDSOCSP3R3CPPEI_AMDSOCSP3R3CPPEI_FILECODE                    (0x5224)
#define SOC_AMDSOCSP3R3CPDXE_AMDSOCSP3R3CPDXE_FILECODE                    (0x5225)

// RN SOC Driver
#define SOC_AMDSOCFP6RNPEI_AMDSOCFP6RNPEI_FILECODE                        (0x5230)
#define SOC_AMDSOCFP6RNDXE_AMDSOCFP6RNDXE_FILECODE                        (0x5231)
#define SOC_AMDSOCAM4RNPEI_AMDSOCAM4RNPEI_FILECODE                        (0x5232)
#define SOC_AMDSOCAM4RNDXE_AMDSOCAM4RNDXE_FILECODE                        (0x5233)

// GN SOC Driver
#define SOC_AMDSOCAM4VMRPEI_AMDSOCAM4VMRPEI_FILECODE                      (0x5240)
#define SOC_AMDSOCAM4VMRDXE_AMDSOCAM4VMRDXE_FILECODE                      (0x5241)
#define SOC_AMDSOCSP3GNPEI_AMDSOCSP3GNPEI_FILECODE                        (0x5242)
#define SOC_AMDSOCSP3GNDXE_AMDSOCSP3GNDXE_FILECODE                        (0x5243)

// CZN SOC Driver
#define SOC_AMDSOCAM4CZNPEI_AMDSOCAM4CZNPEI_FILECODE                      (0x5244)
#define SOC_AMDSOCAM4CZNDXE_AMDSOCAM4CZNDXE_FILECODE                      (0x5245)
#define SOC_AMDSOCFP6CZNPEI_AMDSOCFP6CZNPEI_FILECODE                      (0x5246)
#define SOC_AMDSOCFP6CZNDXE_AMDSOCFP6CZNDXE_FILECODE                      (0x5247)

// BA SOC Driver
#define SOC_AMDSOCSP3R4BAPEI_AMDSOCSP3R4BAPEI_FILECODE                    (0x5248)
#define SOC_AMDSOCSP3R4BADXE_AMDSOCSP3R4BADXE_FILECODE                    (0x5249)

// FF3 SOC Driver
#define SOC_AMDSOCFF3MRPEI_AMDSOCFF3MRPEI_FILECODE                        (0x5250)
#define SOC_AMDSOCFF3MRDXE_AMDSOCFF3MRDXE_FILECODE                        (0x5251)
#define SOC_AMDSOCFF3VNPEI_AMDSOCFF3VNPEI_FILECODE                        (0x5252)
#define SOC_AMDSOCFF3VNDXE_AMDSOCFF3VNDXE_FILECODE                        (0x5253)

// RS SOC Driver
#define SOC_AMDSOCSP5RSPEI_AMDSOCSP5RSPEI_FILECODE                        (0x5254)
#define SOC_AMDSOCSP5RSDXE_AMDSOCSP5RSDXE_FILECODE                        (0x5255)

// RPL SOC Driver
#define SOC_AMDSOCAM5RPLPEI_AMDSOCAM5RPLPEI_FILECODE                      (0x5256)
#define SOC_AMDSOCAM5RPLDXE_AMDSOCAM5RPLDXE_FILECODE                      (0x5257)
#define SOC_AMDSOCFL1RPLPEI_AMDSOCFL1RPLPEI_FILECODE                      (0x526E)
#define SOC_AMDSOCFL1RPLDXE_AMDSOCFL1RPLDXE_FILECODE                      (0x526F)

// RMB SOC Driver
#define SOC_AMDSOCFP7RMBPEI_AMDSOCFP7RMBPEI_FILECODE                      (0x5258)
#define SOC_AMDSOCFP7RMBDXE_AMDSOCFP7RMBDXE_FILECODE                      (0x5259)
#define SOC_AMDSOCFP7R2RMBPEI_AMDSOCFP7R2RMBPEI_FILECODE                  (0x525A)
#define SOC_AMDSOCFP7R2RMBDXE_AMDSOCFP7R2RMBDXE_FILECODE                  (0x525B)
#define SOC_AMDSOCAM5RMBDXE_AMDSOCAM5RMBDXE_FILECODE                      (0x525C)
#define SOC_AMDSOCAM5RMBPEI_AMDSOCAM5RMBPEI_FILECODE                      (0x525D)

// PHX SOC Driver
#define SOC_AMDSOCFP8PHXPEI_AMDSOCFP8PHXPEI_FILECODE                      (0x525E)
#define SOC_AMDSOCFP8PHXDXE_AMDSOCFP8PHXDXE_FILECODE                      (0x525F)

// MDN SOC Driver
#define SOC_AMDSOCFT6MDNPEI_AMDSOCFT6MDNPEI_FILECODE                      (0x5260)
#define SOC_AMDSOCFT6MDNDXE_AMDSOCFT6MDNDXE_FILECODE                      (0x5261)

// MI300 SOC Driver
#define SOC_AMDSOCSH5MI3PEI_AMDSOCSH3MI3PEI_FILECODE                      (0x5262)
#define SOC_AMDSOCSH5MI3DXE_AMDSOCSH5MI3DXE_FILECODE                      (0x5263)

// STP SOC Driver
#define SOC_AMDSOCSP6STPPEI_AMDSOCSP6STPPEI_FILECODE                      (0x5264)
#define SOC_AMDSOCSP6STPDXE_AMDSOCSP6STPDXE_FILECODE                      (0x5265)
#define SOC_AMDSOCSP6STPPEI_SOCSERVICESTPPEI_FILECODE                     (0x5266)

// STXHFP11 SOC Driver
#define SOC_AMDSOCFP11STXHPEI_AMDSOCFP11STXHPEI_FILECODE                  (0x526A)
#define SOC_AMDSOCFP11STXHDXE_AMDSOCFP11STXHDXE_FILECODE                  (0x526B)

// BRH SOC Driver
#define SOC_AMDSOCSP5BRHPEI_AMDSOCSP5BRHPEI_FILECODE                      (0x526C)
#define SOC_AMDSOCSP5BRHDXE_AMDSOCSP5BRHDXE_FILECODE                      (0x526D)

// WH SOC Driver
#define SOC_AMDSOCSP7WHPEI_AMDSOCSP7WHPEI_FILECODE                        (0x5274)
#define SOC_AMDSOCSP7WHDXE_AMDSOCSP7WHDXE_FILECODE                        (0x5275)

// STX FP8
#define SOC_AMDSOCFP8STXKRKPEI_AMDSOCFP8STXKRKPEI_FILECODE                (0x5270)
#define SOC_AMDSOCFP8STXKRKDXE_AMDSOCFP8STXKRKDXE_FILECODE                (0x5271)

// SHP SOC Driver
#define SOC_AMDSOCSP6SHPPEI_AMDSOCSP6SHPPEI_FILECODE                      (0x5272)
#define SOC_AMDSOCSP6SHPDXE_AMDSOCSP6SHPDXE_FILECODE                      (0x5273)
#define SOC_AMDSOCSP6SHPPEI_SOCSERVICESHPPEI_FILECODE                     (0x5276)

// GNR SOC Driver
#define SOC_AMDSOCAM5GNRPEI_AMDSOCAM5GNRPEI_FILECODE                      (0x5280)
#define SOC_AMDSOCAM5GNRDXE_AMDSOCAM5GNRDXE_FILECODE                      (0x5281)
#define SOC_AMDSOCAM5GNRSMM_AMDSOCAM5GNRSMM_FILECODE                      (0x5282)
#define SOC_AMDSOCFL1GNRPEI_AMDSOCFL1GNRPEI_FILECODE                      (0x5283)
#define SOC_AMDSOCFL1GNRDXE_AMDSOCFL1GNRDXE_FILECODE                      (0x5284)
#define SOC_AMDSOCFL1GNRSMM_AMDSOCFL1GNRSMM_FILECODE                      (0x5285)

// SOC Service
#define SOC_AMDSOCSP3R3CPPEI_SOCSERVICECPPEI_FILECODE                     (0x5301)
#define SOC_AMDSOCSP3R3CGLPEI_SOCSERVICECGLPEI_FILECODE                   (0x5302)
#define SOC_AMDSOCFP7R2RMBPEI_SOCSERVICERMBPEI_FILECODE                   (0x5303)
#define SOC_AMDSOCFP7RMBPEI_SOCSERVICERMBPEI_FILECODE                     (0x5304)
#define SOC_AMDSOCAM5RMBPEI_SOCSERVICERMBPEI_FILECODE                     (0x5305)
#define SOC_AMDSOCAM5RPLPEI_SOCSERVICERPLPEI_FILECODE                     (0x5306)
#define SOC_AMDSOCFL1RPLPEI_SOCSERVICERPLPEI_FILECODE                     (0x5307)
#define SOC_AMDSOCFP8PHXPEI_SOCSERVICEPHXPEI_FILECODE                     (0x5308)
#define SOC_AMDSOCAM5GNRPEI_SOCSERVICEGNRPEI_FILECODE                     (0x5309)
#define SOC_AMDSOCAM5PHXPEI_SOCSERVICEPHXPEI_FILECODE                     (0x5312)
#define SOC_AMDSOCFL1GNRPEI_SOCSERVICEGNRPEI_FILECODE                     (0x5313)
#define SOC_AMDSOCFP8STXKRKPEI_SOCSERVICESTXKRKPEI_FILECODE               (0x5314)
#define SOC_AMDSOCFP11STXHPEI_SOCSERVICESTXHPEI_FILECODE                  (0x5315)

// PHX SOC Driver
#define SOC_AMDSOCFP7PHXDXE_AMDSOCFP7PHXDXE_FILECODE                      (0x530A)
#define SOC_AMDSOCFP7PHXPEI_AMDSOCFP7PHXPEI_FILECODE                      (0x530B)
#define SOC_AMDSOCFP7PHXPEI_SOCSERVICEPHXPEI_FILECODE                     (0x530C)
#define SOC_AMDSOCFP7R2PHXDXE_AMDSOCFP7R2PHXDXE_FILECODE                  (0x530D)
#define SOC_AMDSOCFP7R2PHXPEI_AMDSOCFP7R2PHXPEI_FILECODE                  (0x530E)
#define SOC_AMDSOCFP7R2PHXPEI_SOCSERVICEPHXPEI_FILECODE                   (0x530F)
#define SOC_AMDSOCAM5PHXDXE_AMDSOCAM5PHXDXE_FILECODE                      (0x5310)
#define SOC_AMDSOCAM5PHXPEI_AMDSOCAM5PHXPEI_FILECODE                      (0x5311)

// SOC Library
#define LIBRARY_AMDSOCBASELIB_AMDSOCBASELIB_FILECODE                                 (0x5001)
#define LIBRARY_DXESOCLOGICALIDSERVICESLIB_SOCLOGICALIDSERVICESDXE_FILECODE          (0x5010)
#define LIBRARY_PEISOCLOGICALIDSERVICESLIB_SOCLOGICALIDSERVICESPEI_FILECODE          (0x5011)
#define LIBRARY_BASESOCLOGICALIDXLATBRLIB_BASESOCLOGICALIDXLATBRLIB_FILECODE         (0x5012)
#define LIBRARY_BASESOCLOGICALIDXLATZPDIELIB_BASESOCLOGICALIDXLATZPDIELIB_FILECODE   (0x5013)
#define LIBRARY_BASESOCLOGICALIDXLATRVDIELIB_BASESOCLOGICALIDXLATRVDIELIB_FILECODE   (0x5014)
#define LIBRARY_SOCCMNIDSHOOKZPLIB_PEI_SOCCMNIDSHOOKZPLIBPEI_FILECODE                (0x5015)
#define LIBRARY_SOCCMNIDSHOOKZPLIB_DXE_SOCCMNIDSHOOKZPLIBDXE_FILECODE                (0x5016)
#define LIBRARY_SOCCMNIDSHOOKRVLIB_PEI_SOCCMNIDSHOOKRVLIBPEI_FILECODE                (0x5017)
#define LIBRARY_SOCCMNIDSHOOKRVLIB_DXE_SOCCMNIDSHOOKRVLIBDXE_FILECODE                (0x5018)
#define LIBRARY_PEISOCBISTLOGGINGLIB_SOCBISTLOGGINGLIB_FILECODE                      (0x5019)
#define LIBRARY_PEISOCBISTZPLIB_PEISOCBISTZPLIB_FILECODE                             (0x501A)
#define LIBRARY_PEISOCBISTRVLIB_PEISOCBISTRVLIB_FILECODE                             (0x501B)
#define LIBRARY_BASESOCLOGICALIDXLATSSPDIELIB_BASESOCLOGICALIDXLATSSPDIELIB_FILECODE (0x501C)
#define LIBRARY_SOCCMNIDSHOOKSSPLIB_PEI_SOCCMNIDSHOOKSSPLIBPEI_FILECODE              (0x501D)
#define LIBRARY_SOCCMNIDSHOOKSSPLIB_DXE_SOCCMNIDSHOOKSSPLIBDXE_FILECODE              (0x501E)
#define LIBRARY_PEISOCBISTVHCCDLIB_PEISOCBISTVHCCDLIB_FILECODE                       (0x501F)
#define LIBRARY_PEISOCBISTLOGGINGLIB_SOCBISTLOGGING2LIB_FILECODE                     (0x5020)
#define LIBRARY_PEISOCVALHALLASERVICESMTSLIB_SOCVALHALLASERVICESMTSPEI_FILECODE      (0x5021)
#define LIBRARY_PEISOCVALHALLASERVICESRNLIB_SOCVALHALLASERVICESRNPEI_FILECODE        (0x5022)
#define LIBRARY_PEISOCVALHALLASERVICESSSPLIB_SOCVALHALLASERVICESSSPPEI_FILECODE      (0x5023)
#define LIBRARY_DXESOCVALHALLASERVICESMTSLIB_SOCVALHALLASERVICESMTSDXE_FILECODE      (0x5024)
#define LIBRARY_DXESOCVALHALLASERVICESRNLIB_SOCVALHALLASERVICESRNDXE_FILECODE        (0x5025)
#define LIBRARY_DXESOCVALHALLASERVICESSSPLIB_SOCVALHALLASERVICESSSPDXE_FILECODE      (0x5026)
#define LIBRARY_PEISOCBISTRNLIB_PEISOCBISTRNLIB_FILECODE                             (0x5027)
#define LIBRARY_SOCCMNIDSHOOKRNLIB_PEI_SOCCMNIDSHOOKRNLIBPEI_FILECODE                (0x5028)
#define LIBRARY_SOCCMNIDSHOOKRNLIB_DXE_SOCCMNIDSHOOKRNLIBDXE_FILECODE                (0x5029)
#define LIBRARY_PEISOCZENSERVICESLIB_SOCZENSERVICESPEI_FILECODE                      (0x502A)
#define LIBRARY_DXESOCZENSERVICESLIB_SOCZENSERVICESDXE_FILECODE                      (0x502B)
#define LIBRARY_SOCCMNIDSHOOKCZNLIB_PEI_SOCCMNIDSHOOKCZNLIBPEI_FILECODE              (0x502C)
#define LIBRARY_SOCCMNIDSHOOKCZNLIB_DXE_SOCCMNIDSHOOKCZNLIBDXE_FILECODE              (0x502D)
#define LIBRARY_BASESOCLOGICALIDXLATZEN3DIELIB_BASESOCLOGICALIDXLATZEN3DIELIB_FILECODE (0x502F)
#define LIBRARY_BASESOCLOGICALIDXLATRNDIELIB_BASESOCLOGICALIDXLATRNDIELIB_FILECODE   (0x5031)
#define LIBRARY_DXESOCZEN3SERVICESVMRLIB_SOCZEN3SERVICESVMRDXE_FILECODE              (0x5032)
#define LIBRARY_DXESOCZEN3SERVICESCZNLIB_SOCZEN3SERVICESCZNDXE_FILECODE              (0x5033)
#define LIBRARY_DXESOCZEN3SERVICESGNLIB_SOCZEN3SERVICESGNDXE_FILECODE                (0x5034)
#define LIBRARY_PEISOCZEN3SERVICESVMRLIB_SOCZEN3SERVICESVMRPEI_FILECODE              (0x5035)
#define LIBRARY_PEISOCZEN3SERVICESCZNLIB_SOCZEN3SERVICESCZNPEI_FILECODE              (0x5036)
#define LIBRARY_PEISOCZEN3SERVICESGNLIB_SOCZEN3SERVICESGNPEI_FILECODE                (0x5037)
#define LIBRARY_SOCCMNIDSHOOKGNLIB_DXE_SOCCMNIDSHOOKGNLIBDXE_FILECODE                (0x5038)
#define LIBRARY_SOCCMNIDSHOOKGNLIB_PEI_SOCCMNIDSHOOKGNLIBPEI_FILECODE                (0x5039)
#define LIBRARY_FABRICIDSHOOKGNLIB_DXE_FABRICIDSHOOKGNLIBDXE_FILECODE                (0x503A)
#define LIBRARY_FABRICIDSHOOKGNLIB_PEI_FABRICIDSHOOKGNLIBPEI_FILECODE                (0x503B)
#define LIBRARY_CCXZEN3GNIDSHOOKLIB_DXE_CCXZEN3GNIDSCUSTOMPSTATES_FILECODE           (0x503C)
#define LIBRARY_CCXZEN3GNIDSHOOKLIB_DXE_CCXZEN3GNIDSHOOKLIBDXE_FILECODE              (0x503D)
#define LIBRARY_CCXZEN3GNIDSHOOKLIB_PEI_CCXZEN3GNIDSHOOKLIBPEI_FILECODE              (0x503E)
#define LIBRARY_CCXZEN3GNIDSHOOKLIB_SMM_CCXZEN3GNIDSHOOKLIBSMM_FILECODE              (0x503F)
#define LIBRARY_CCXZEN3GNIDSHOOKLIB_SMM_CCXZEN3GNIDSSYNCMSRSMM_FILECODE              (0x5040)
#define LIBRARY_CCXZEN3CZNIDSHOOKLIB_DXE_CCXZEN3CZNIDSCUSTOMPSTATES_FILECODE         (0x5041)
#define LIBRARY_CCXZEN3CZNIDSHOOKLIB_DXE_CCXZEN3CZNIDSHOOKLIBDXE_FILECODE            (0x5042)
#define LIBRARY_CCXZEN3CZNIDSHOOKLIB_PEI_CCXZEN3CZNIDSHOOKLIBPEI_FILECODE            (0x5043)
#define LIBRARY_CCXZEN3CZNIDSHOOKLIB_SMM_CCXZEN3CZNIDSHOOKLIBSMM_FILECODE            (0x5044)
#define LIBRARY_CCXZEN3CZNIDSHOOKLIB_SMM_CCXZEN3CZNIDSSYNCMSRSMM_FILECODE            (0x5045)
#define LIBRARY_DXESOCZEN3SERVICESRMBLIB_SOCZEN3SERVICESRMBDXE_FILECODE              (0x5046)
#define LIBRARY_PEISOCZEN3SERVICESRMBLIB_SOCZEN3SERVICESRMBPEI_FILECODE              (0x5047)
#define LIBRARY_BASESOCLOGICALIDXLATFF3DIELIB_BASESOCLOGICALIDXLATFF3DIELIB_FILECODE   (0x5048)
#define LIBRARY_DXESOCVALHALLASERVICESFF3LIB_SOCVALHALLASERVICESFF3DXE_FILECODE        (0x5049)
#define LIBRARY_PEISOCBISTFF3LIB_PEISOCBISTFF3LIB_FILECODE                             (0x504A)
#define LIBRARY_PEISOCVALHALLASERVICESFF3LIB_SOCVALHALLASERVICESFF3PEI_FILECODE        (0x504B)
#define LIBRARY_SOCCMNIDSHOOKFF3LIB_PEI_SOCCMNIDSHOOKFF3LIBPEI_FILECODE                (0x504C)
#define LIBRARY_SOCCMNIDSHOOKFF3LIB_DXE_SOCCMNIDSHOOKFF3LIBDXE_FILECODE                (0x504D)
#define LIBRARY_CCXZEN3BAIDSHOOKLIB_DXE_CCXZEN3BAIDSCUSTOMPSTATES_FILECODE           (0x504E)
#define LIBRARY_CCXZEN3BAIDSHOOKLIB_DXE_CCXZEN3BAIDSHOOKLIBDXE_FILECODE              (0x504F)
#define LIBRARY_CCXZEN3BAIDSHOOKLIB_PEI_CCXZEN3BAIDSHOOKLIBPEI_FILECODE              (0x5050)
#define LIBRARY_CCXZEN3BAIDSHOOKLIB_SMM_CCXZEN3BAIDSHOOKLIBSMM_FILECODE              (0x5051)
#define LIBRARY_CCXZEN3BAIDSHOOKLIB_SMM_CCXZEN3BAIDSSYNCMSRSMM_FILECODE              (0x5052)
#define LIBRARY_SOCCMNIDSHOOKRMBLIB_PEI_SOCCMNIDSHOOKRMBLIBPEI_FILECODE              (0x5053)
#define LIBRARY_SOCCMNIDSHOOKRMBLIB_DXE_SOCCMNIDSHOOKRMBLIBDXE_FILECODE              (0x5054)
#define LIBRARY_CCXZEN3RMBIDSHOOKLIB_DXE_CCXZEN3RMBIDSCUSTOMPSTATES_FILECODE         (0x5055)
#define LIBRARY_CCXZEN3RMBIDSHOOKLIB_DXE_CCXZEN3RMBIDSHOOKLIBDXE_FILECODE            (0x5056)
#define LIBRARY_CCXZEN3RMBIDSHOOKLIB_PEI_CCXZEN3RMBIDSHOOKLIBPEI_FILECODE            (0x5057)
#define LIBRARY_CCXZEN3RMBIDSHOOKLIB_SMM_CCXZEN3RMBIDSHOOKLIBSMM_FILECODE            (0x5058)
#define LIBRARY_CCXZEN3RMBIDSHOOKLIB_SMM_CCXZEN3RMBIDSSYNCMSRSMM_FILECODE            (0x5059)
#define LIBRARY_PEISOCZEN3SERVICESCGLLIB_SOCZEN3SERVICESCGLPEI_FILECODE              (0x505A)
#define LIBRARY_DXESOCZEN3SERVICESCGLLIB_SOCZEN3SERVICESCGLDXE_FILECODE              (0x505B)
#define LIBRARY_PEISOCZEN4SERVICESRSLIB_SOCZEN4SERVICESRSPEI_FILECODE                (0x505C)
#define LIBRARY_DXESOCZEN4SERVICESRSLIB_SOCZEN4SERVICESRSDXE_FILECODE                (0x505D)
#define LIBRARY_BASESOCLOGICALIDXLATZEN4DIELIB_BASESOCLOGICALIDXLATZEN4DIELIB_FILECODE (0x505E)
#define LIBRARY_PEISOCBISTZEN4CCDLIB_PEISOCBISTZEN4CCDLIB_FILECODE                   (0x505F)
#define LIBRARY_SOCCMNIDSHOOKPHXLIB_PEI_SOCCMNIDSHOOKPHXLIBPEI_FILECODE              (0x5060)
#define LIBRARY_SOCCMNIDSHOOKPHXLIB_DXE_SOCCMNIDSHOOKPHXLIBDXE_FILECODE              (0x5061)
#define LIBRARY_PEISOCZEN4SERVICESPHXLIB_SOCZEN4SERVICESPHXPEI_FILECODE              (0x5062)
#define LIBRARY_DXESOCZEN4SERVICESPHXLIB_SOCZEN4SERVICESPHXDXE_FILECODE              (0x5063)
#define LIBRARY_CCXZEN4PHXIDSHOOKLIB_DXE_CCXZEN4PHXIDSCUSTOMPSTATES_FILECODE         (0x5064)
#define LIBRARY_CCXZEN4PHXIDSHOOKLIB_DXE_CCXZEN4PHXIDSHOOKLIBDXE_FILECODE            (0x5065)
#define LIBRARY_CCXZEN4PHXIDSHOOKLIB_PEI_CCXZEN4PHXIDSHOOKLIBPEI_FILECODE            (0x5066)
#define LIBRARY_CCXZEN4PHXIDSHOOKLIB_SMM_CCXZEN4PHXIDSHOOKLIBSMM_FILECODE            (0x5067)
#define LIBRARY_CCXZEN4PHXIDSHOOKLIB_SMM_CCXZEN4PHXIDSSYNCMSRSMM_FILECODE            (0x5068)

#define LIBRARY_CCXZEN4RPLIDSHOOKLIB_DXE_CCXZEN4RPLIDSCUSTOMPSTATES_FILECODE         (0x5069)
#define LIBRARY_CCXZEN4RPLIDSHOOKLIB_DXE_CCXZEN4RPLIDSHOOKLIBDXE_FILECODE            (0x506A)
#define LIBRARY_CCXZEN4RPLIDSHOOKLIB_PEI_CCXZEN4RPLIDSHOOKLIBPEI_FILECODE            (0x506B)
#define LIBRARY_CCXZEN4RPLIDSHOOKLIB_SMM_CCXZEN4RPLIDSHOOKLIBSMM_FILECODE            (0x506C)
#define LIBRARY_CCXZEN4RPLIDSHOOKLIB_SMM_CCXZEN4RPLIDSSYNCMSRSMM_FILECODE            (0x506D)
#define LIBRARY_PEISOCZEN4SERVICESRPLLIB_SOCZEN4SERVICESRPLPEI_FILECODE              (0x506E)
#define LIBRARY_DXESOCZEN4SERVICESRPLLIB_SOCZEN4SERVICESRPLDXE_FILECODE              (0x506F)
#define LIBRARY_SOCCMNIDSHOOKMDNLIB_PEI_SOCCMNIDSHOOKMDNLIBPEI_FILECODE              (0x5070)
#define LIBRARY_SOCCMNIDSHOOKMDNLIB_DXE_SOCCMNIDSHOOKMDNLIBDXE_FILECODE              (0x5071)
#define LIBRARY_PEISOCVALHALLASERVICESMDNLIB_SOCVALHALLASERVICESMDNPEI_FILECODE      (0x5072)
#define LIBRARY_DXESOCVALHALLASERVICESMDNLIB_SOCVALHALLASERVICESMDNDXE_FILECODE      (0x5073)
#define LIBRARY_CCXZEN4RPLIDSHOOKLIB_DXE_CCXZEN4RPLIDSSYNCMSR_FILECODE               (0x5074)
#define LIBRARY_SOCCMNIDSHOOKRPLLIB_DXE_SOCCMNIDSHOOKRPLLIBDXE_FILECODE              (0x5075)
#define LIBRARY_SOCCMNIDSHOOKRPLLIB_PEI_SOCCMNIDSHOOKRPLLIBPEI_FILECODE              (0x5076)
#define LIBRARY_SOCCMNIDSHOOKSTXKRKLIB_PEI_SOCCMNIDSHOOKSTXKRKLIBPEI_FILECODE        (0x5077)
#define LIBRARY_SOCCMNIDSHOOKSTXKRKLIB_DXE_SOCCMNIDSHOOKSTXKRKLIBDXE_FILECODE        (0x5078)
#define LIBRARY_PEISOCBISTZEN5CCDLIB_PEISOCBISTZEN5CCDLIB_FILECODE                   (0x5079)
#define LIBRARY_PEISOCZEN5SERVICESSTXKRKLIB_SOCZEN5SERVICESSTXKRKPEI_FILECODE        (0x507A)
#define LIBRARY_DXESOCZEN5SERVICESSTXKRKLIB_SOCZEN5SERVICESSTXKRKDXE_FILECODE        (0x507B)
#define LIBRARY_BASESOCLOGICALIDXLATZEN5DIELIB_BASESOCLOGICALIDXLATZEN5DIELIB_FILECODE (0x507C)
#define LIBRARY_PEISOCZEN4SERVICESMI3LIB_SOCZEN4SERVICESMI3PEI_FILECODE              (0x507D)
#define LIBRARY_DXESOCZEN4SERVICESMI3LIB_SOCZEN4SERVICESMI3DXE_FILECODE              (0x507E)
#define LIBRARY_SOCCMNIDSHOOKSTPLIB_DXE_SOCCMNIDSHOOKSTPLIBDXE_FILECODE              (0x507F)
#define LIBRARY_SOCCMNIDSHOOKSTPLIB_PEI_SOCCMNIDSHOOKSTPLIBPEI_FILECODE              (0x5080)
#define LIBRARY_BASESOCLOGICALIDXLATZEN2DIELIB_BASESOCLOGICALIDXLATZEN2DIELIB_FILECODE (0x5081)

#define LIBRARY_DXESOCZEN5SERVICESBRHLIB_SOCZEN5SERVICESBRHDXE_FILECODE              (0x5082)
#define LIBRARY_PEISOCZEN5SERVICESBRHLIB_SOCZEN5SERVICESBRHPEI_FILECODE              (0x5083)
#define LIBRARY_SOCZEN5SERVICESGNRLIB_DXE_SOCZEN5SERVICESGNRDXE_FILECODE             (0x5084)
#define LIBRARY_SOCZEN5SERVICESGNRLIB_PEI_SOCZEN5SERVICESGNRPEI_FILECODE             (0x5085)
#define LIBRARY_CCXZEN4PHXIDSHOOKLIB_DXE_CCXZEN4PHXIDSSYNCMSR_FILECODE               (0x5086)
#define LIBRARY_FABRICRESOURCEMANAGERLIBNULL_FABRICRESOURCEMANAGERLIBNULL_FILECODE   (0x5087)

#define LIBRARY_SOCCMNIDSHOOKSTXHLIB_PEI_SOCCMNIDSHOOKSTXHLIBPEI_FILECODE            (0x5088)
#define LIBRARY_SOCCMNIDSHOOKSTXHLIB_DXE_SOCCMNIDSHOOKSTXHLIBDXE_FILECODE            (0x5089)

#define LIBRARY_PEISOCBISTLOGGING3LIB_SOCBISTLOGGING3LIB_FILECODE                    (0x508A)

#define LIBRARY_PEISOCZEN5SERVICESSTXHLIB_SOCZEN5SERVICESSTXHPEI_FILECODE            (0x508B)
#define LIBRARY_DXESOCZEN5SERVICESSTXHLIB_SOCZEN5SERVICESSTXHDXE_FILECODE            (0x508C)
#define LIBRARY_PEISOCZEN5SERVICESKRKLIB_SOCZEN5SERVICESKRKPEI_FILECODE              (0x508D)
#define LIBRARY_DXESOCZEN5SERVICESKRKLIB_SOCZEN5SERVICESKRKDXE_FILECODE              (0x508E)
#define LIBRARY_PEISOCBISTZEN5CCDSTXKRKLIB_PEISOCBISTZEN5CCDSTXKRKLIB_FILECODE       (0x508F)
#define LIBRARY_PEISOCBISTZEN5CCDBRHLIB_PEISOCBISTZEN5CCDBRHLIB_FILECODE             (0x5090)
#define LIBRARY_PEISOCBISTZEN5CCDGNRLIB_PEISOCBISTZEN5CCDGNRLIB_FILECODE             (0x5091)
#define LIBRARY_PEISOCBISTZEN5CCDSTXHLIB_PEISOCBISTZEN5CCDSTXHLIB_FILECODE           (0x5092)
#define LIBRARY_SOCCOREINFO2ACCESSLIB_SOCCOREINFO2ACCESSLIB_FILECODE                 (0x5093)

#define LIBRARY_SOCZEN5SERVICESSHPLIB_PEI_SOCZEN5SERVICESSHPPEI_FILECODE             (0x5094)
#define LIBRARY_SOCZEN5SERVICESSHPLIB_DXE_SOCZEN5SERVICESSHPDXE_FILECODE             (0x5095)
#define LIBRARY_SOCCMNIDSHOOKSHPLIB_PEI_SOCCMNIDSHOOKSHPLIBPEI_FILECODE              (0x5096)
#define LIBRARY_SOCCMNIDSHOOKSHPLIB_DXE_SOCCMNIDSHOOKSHPLIBDXE_FILECODE              (0x5097)

//Event Log Drivers
#define LIBRARY_AMDERRORLOGLIB_AMDERRORLOGLIB_FILECODE                    (0xE001)
#define ERRORLOG_AMDERRORLOGPEI_AMDERRORLOGPEI_FILECODE                   (0xE002)
#define ERRORLOG_AMDERRORLOGDXE_AMDERRORLOGDXE_FILECODE                   (0xE003)
#define ERRORLOG_AMDCXLERRORLOGDXE_AMDCXLERRORLOGDXE_FILECODE             (0xE00A)

// Version String Drivers
#define UNIVERSAL_VERSION_AMDVERSIONPEI_AMDVERSIONPEI_FILECODE              (0xE004)
#define UNIVERSAL_VERSION_AMDVERSIONDXE_AMDVERSIONDXE_FILECODE              (0xE005)
#define ERRORLOG_AMDERRORLOGDISPLAYRSDXE_AMDERRORLOGDISPLAYRSDXE_FILECODE   (0xE006)
#define ERRORLOG_AMDERRORLOGDISPLAYDXE_AMDERRORLOGDISPLAYDXE_FILECODE       (0xE007)
#define ERRORLOG_AMDERRORLOGDISPLAYGNDXE_AMDERRORLOGDISPLAYGNDXE_FILECODE   (0xE008)
#define ERRORLOG_AMDERRORLOGDISPLAYBRHDXE_AMDERRORLOGDISPLAYBRHDXE_FILECODE (0xE009)

//APCB
#define PSP_APCB_APCBZPDXE_APCBZPDXE_FILECODE                               (0xC200)
#define PSP_APCB_APCBZPSMM_APCBZPSMM_FILECODE                               (0xC201)
#define PSP_APCB_TOKENSZP_CBS_EXT_TOKENS_FILECODE                           (0xC202)
#define PSP_APCB_TOKENSZP_CONFIG_TOKENS_FILECODE                            (0xC203)

#define PSP_APCB_APCBRVDXE_APCBRVDXE_FILECODE                               (0xC204)
#define PSP_APCB_APCBRVSMM_APCBRVSMM_FILECODE                               (0xC205)
#define PSP_APCB_TOKENSRV_CBS_EXT_TOKENS_FILECODE                           (0xC206)
#define PSP_APCB_TOKENSRV_CONFIG_TOKENS_FILECODE                            (0xC207)

#define PSP_APCB_APCBSSPDXE_APCBSSPDXE_FILECODE                             (0xC208)
#define PSP_APCB_APCBSSPSMM_APCBSSPSMM_FILECODE                             (0xC209)
#define PSP_APCB_TOKENSSSP_CBS_EXT_TOKENS_FILECODE                          (0xC20A)
#define PSP_APCB_TOKENSSSP_CONFIG_TOKENS_FILECODE                           (0xC20B)

#define LIBRARY_APCBZPLIB_APCBLIB_FILECODE                                  (0xC210)
#define LIBRARY_APCBZPLIB_APCBREPLACETYPE_FILECODE                          (0xC211)
#define LIBRARY_APCBZPLIB_DRAMPOSTPACKAGEREPAIR_FILECODE                    (0xC212)
#define LIBRARY_APCBZPLIB_GETSETPARAMS_FILECODE                             (0xC213)

#define LIBRARY_APCBRVLIB_APCBLIB_FILECODE                                  (0xC214)
#define LIBRARY_APCBRVLIB_APCBREPLACETYPE_FILECODE                          (0xC215)
#define LIBRARY_APCBRVLIB_DRAMPOSTPACKAGEREPAIR_FILECODE                    (0xC216)
#define LIBRARY_APCBRVLIB_GETSETPARAMS_FILECODE                             (0xC217)

#define LIBRARY_APCBRVLIB_UPDATESHADOWDIMMCONFIG_FILECODE                   (0xC218)

#define LIBRARY_APCBSSPLIB_APCBLIB_FILECODE                                 (0xC219)
#define LIBRARY_APCBSSPLIB_APCBREPLACETYPE_FILECODE                         (0xC21A)
#define LIBRARY_APCBSSPLIB_DRAMPOSTPACKAGEREPAIR_FILECODE                   (0xC21B)
#define LIBRARY_APCBSSPLIB_GETSETPARAMS_FILECODE                            (0xC21C)

#define LIBRARY_APCBLIBV3_UPDATESHADOWDIMMCONFIG_FILECODE                   (0xC21D)

#define LIBRARY_APCBLIBV3_APCBLIBV3_FILECODE                                (0xC220)
#define LIBRARY_APCBLIBV3_APCBLIBV3SERVICES_FILECODE                        (0xC221)
#define LIBRARY_APCBLIBV3_DRAMPOSTPACKAGEREPAIR_FILECODE                    (0xC222)
#define LIBRARY_APCBLIBV3_APCBLIBV2COMPATIBILITY_FILECODE                   (0xC223)
#define LIBRARY_APCBCHECKSUMLIBV3_APCBCHECKSUMLIBV3_FILECODE                (0xC224)
#define LIBRARY_APCBHMACCHECKSUMLIBV3_APCBHMACCHECKSUMLIBV3_FILECODE        (0xC225)
#define LIBRARY_APCBLIBV3_CALLOUTLIB_FILECODE                               (0xC226)
#define LIBRARY_APCBLIBV3_COREAPCBINTERFACE_FILECODE                        (0xC227)
#define LIBRARY_APCBVARIABLELIBV3_APCBVARIABLELIBV3_FILECODE                (0xC228)
#define LIBRARY_AMDPSPFWIMAGEHEADERLIB_AMDPSPFWIMAGEHEADERLIB_FILECODE      (0xC229)

#define PSP_APCBDRV_APCBV3DXE_APCBV3DXE_FILECODE                            (0xC230)
#define PSP_APCBDRV_APCBV3SMM_APCBV3SMM_FILECODE                            (0xC231)
#define PSP_APCBDRV_APCBV3PEI_APCBV3PEI_FILECODE                            (0xC232)
#define LIBRARY_APCBCORELIB_APCBCORELIB_FILECODE                            (0xC233)
#define LIBRARY_APCBLIBV3PEI_APCBLIBV3PEI_FILECODE                          (0xC234)
#define LIBRARY_APCBLIBV3PEI_APCBLIBV3PEISERVICES_FILECODE                  (0xC235)
#define LIBRARY_APOBAPCBLIB_APOBAPCBLIB_FILECODE                            (0xC236)

//RAS
#define LIBRARY_RASZPSMMLIB_RASZPSMMLIB_FILECODE                          (0xC001)
#define LIBRARY_RASZPBASELIB_RASZPBASELIB_FILECODE                        (0xC002)

#define LIBRARY_RASBRBASELIB_RASBRBASELIB_FILECODE                        (0xC003)

#define LIBRARY_RASSSPSMMLIB_RASSSPSMMLIB_FILECODE                        (0xC004)
#define LIBRARY_RASSSPBASELIB_RASSSPBASELIB_FILECODE                      (0xC005)

#define LIBRARY_RASGNSMMLIB_RASGNSMMLIB_FILECODE                          (0xC006)
#define LIBRARY_RASGNBASELIB_RASGNBASELIB_FILECODE                        (0xC007)

#define LIBRARY_RASBASMMLIB_RASBASMMLIB_FILECODE                          (0xC008)
#define LIBRARY_RASBABASELIB_RASBABASELIB_FILECODE                        (0xC009)

#define LIBRARY_RAS_RS_RASRSSMMLIB_RASRSSMMLIB_FILECODE                          (0xC00A)
#define LIBRARY_RAS_RS_RASRSSOCLIB_RASRSSOCLIB_FILECODE                          (0xC00B)
#define LIBRARY_RAS_RS_DFADDRESSTRANSLATERSLIB_DFADDRESSTRANSLATERSLIB_FILECODE  (0xC00C)
#define LIBRARY_RAS_RASBASELIB_RASBASELIB_FILECODE                               (0xC00D)
#define LIBRARY_RAS_RASACPI63LIB_RASACPI63LIB_FILECODE                           (0xC00E)

#define LIBRARY_RAS_BRH_RASBRHSMMLIB_RASBRHSMMLIB_FILECODE                       (0xC00F)
#define LIBRARY_RAS_BRH_RASBRHSOCLIB_RASBRHSOCLIB_FILECODE                       (0xC010)

#define LIBRARY_RASRVSMMLIB_RASRVSMMLIB_FILECODE                          (0xC011)
#define LIBRARY_RASRVBASELIB_RASRVBASELIB_FILECODE                        (0xC012)

#define LIBRARY_RAS_PHX_DFADDRESSTRANSLATEPHXLIB_DFADDRESSTRANSLATEPHXLIB_FILECODE  (0xC013)

#define LIBRARY_RAS_MI3_RASMI3SMMLIB_RASMI3SMMLIB_FILECODE                          (0xC014)
#define LIBRARY_RAS_MI3_RASMI3SOCLIB_RASMI3SOCLIB_FILECODE                          (0xC015)
#define LIBRARY_RAS_MI3_DFADDRESSTRANSLATEMI3LIB_DFADDRESSTRANSLATEMI3LIB_FILECODE  (0xC016)
#define LIBRARY_RAS_RASIDSPEILIB_RASIDSPEILIB_FILECODE                              (0xC017)

#define LIBRARY_RAS_BRH_DFADDRESSTRANSLATERSLIB_DFADDRESSTRANSLATERSLIB_FILECODE    (0xC018)
#define LIBRARY_RAS_RPL_DFADDRESSTRANSLATERPLLIB_DFADDRESSTRANSLATERPLLIB_FILECODE  (0xC019)

#define RAS_AMDRASZPSP3APEIDXE_AMDRASZPSP3APEIDXE_FILECODE                (0xC101)
#define RAS_AMDRASZPSP3DXE_AMDRASZPSP3DXE_FILECODE                        (0xC102)
#define RAS_AMDRASZPSP3SMM_AMDRASZPSP3SMM_FILECODE                        (0xC103)

#define RAS_AMDRASBRAPEIDXE_AMDRASBRAPEIDXE_FILECODE                      (0xC104)
#define RAS_AMDRASBRDXE_AMDRASBRDXE_FILECODE                              (0xC105)

#define RAS_AMDRASSSPAPEIDXE_AMDRASSSPAPEIDXE_FILECODE                    (0xC106)
#define RAS_AMDRASSSPDXE_AMDRASSSPDXE_FILECODE                            (0xC107)
#define RAS_AMDRASSSPSMM_AMDRASSPSSMM_FILECODE                            (0xC108)

#define RAS_AMDRASRVAPEIDXE_AMDRASRVAPEIDXE_FILECODE                      (0xC111)
#define RAS_AMDRASRVDXE_AMDRASRVDXE_FILECODE                              (0xC112)
#define RAS_AMDRASRVSMM_AMDRASRVSMM_FILECODE                              (0xC113)

#define RAS_AMDRASGNSP3APEIDXE_AMDRASGNSP3APEIDXE_FILECODE                (0xC114)
#define RAS_AMDRASGNSP3DXE_AMDRASGNSP3DXE_FILECODE                        (0xC115)
#define RAS_AMDRASGNSP3SMM_AMDRASGNSP3SMM_FILECODE                        (0xC116)

#define RAS_AMDRASBASP3APEIDXE_AMDRASBASP3APEIDXE_FILECODE                (0xC117)
#define RAS_AMDRASBASP3DXE_AMDRASBASP3DXE_FILECODE                        (0xC118)
#define RAS_AMDRASBASP3SMM_AMDRASBASP3SMM_FILECODE                        (0xC119)

#define RAS_AMDRASRNDXE_AMDRASRNDXE_FILECODE                              (0xC11A)

#define RAS_AMDRASRSSERVICEDXE_AMDRASRSSERVICEDXE_FILECODE                (0xC11B)
#define RAS_AMDRASRSDXE_AMDRASRSDXE_FILECODE                              (0xC11C)
#define RAS_AMDRASRSSERVICESMM_AMDRASRSSERVICESMM_FILECODE                (0xC11D)

#define RAS_AMDRASBRHSERVICEDXE_AMDRASBRHSERVICEDXE_FILECODE              (0xC11E)
#define RAS_AMDRASBRHDXE_AMDRASBRHDXE_FILECODE                            (0xC11F)
#define RAS_AMDRASBRHSERVICESMM_AMDRASBRHSERVICESMM_FILECODE              (0xC120)

#define RAS_RPL_AMDMINIRASRPLSERVICEDXE_AMDMINIRASRPLSERVICEDXE_FILECODE  (0xC122)
#define RAS_PHX_AMDMINIRASSERVICEDXE_AMDMINIRASSERVICEDXE_FILECODE        (0xC123)

#define RAS_AMDRASMI3SERVICEDXE_AMDRASMI3SERVICEDXE_FILECODE              (0xC124)
#define RAS_AMDRASMI3DXE_AMDRASMI3DXE_FILECODE                            (0xC125)
#define RAS_AMDRASMI3SERVICESMM_AMDRASMI3SERVICESMM_FILECODE              (0xC126)

// Emulation Switch
#define LIBRARY_AMDEMULATIONFLAGLIB_AMDEMULATIONFLAGPEILIB_FILECODE           (0xC300)
#define LIBRARY_AMDEMULATIONFLAGLIB_AMDEMULATIONFLAGDXESMMLIB_FILECODE        (0xC301)
#define LIBRARY_PRESILICONCONTROLRMBLIB_PRESILICONCONTROLRMBLIB_FILECODE      (0xC310)
#define LIBRARY_PRESILICONCONTROLPHXLIB_PRESILICONCONTROLPHXLIB_FILECODE      (0xC311)
#define LIBRARY_PRESILICONCONTROLRPLLIB_PRESILICONCONTROLRPLLIB_FILECODE      (0xC312)
#define LIBRARY_PRESILICONCONTROLMDNLIB_PRESILICONCONTROLMDNLIB_FILECODE      (0xC313)
#define LIBRARY_PRESILICONCONTROLRSLIB_PRESILICONCONTROLRSLIB_FILECODE        (0xC314)
#define LIBRARY_PRESILICONCONTROLMI3LIB_PRESILICONCONTROLMI3LIB_FILECODE      (0xC315)
#define LIBRARY_PRESILICONCONTROLBRHLIB_PRESILICONCONTROLBRHLIB_FILECODE      (0xC316)
#define LIBRARY_PRESILICONCONTROLSTXLIB_PRESILICONCONTROLSTXLIB_FILECODE      (0xC317)
#define LIBRARY_PRESILICONCONTROLSTXHLIB_PRESILICONCONTROLSTXHLIB_FILECODE    (0xC318)
#define LIBRARY_PRESILICONCONTROLKRKLIB_PRESILICONCONTROLKRKLIB_FILECODE      (0xC319)
#define LIBRARY_PRESILICONCONTROLWHLIB_PRESILICONCONTROLWHLIB_FILECODE        (0xC31A)

// SecureBio
#define SECUREBIO_SECUREBIODXE_SECUREBIODXE_FILECODE                      (0xC400)

//ACPI
#define LIBRARY_AMDACPIAMLLIB_AMDACPIAMLLIB_FILECODE                      (0xC500)
#define LIBRARY_DXEAMLGENERATIONLIB_AMLARGOBJECTS_FILECODE                (0xC501)
#define LIBRARY_DXEAMLGENERATIONLIB_AMLASSISTFUNCTIONS_FILECODE           (0xC502)
#define LIBRARY_DXEAMLGENERATIONLIB_AMLDATAOBJECTS_FILECODE               (0xC503)
#define LIBRARY_DXEAMLGENERATIONLIB_AMLEXPRESSIONOPCODES_FILECODE         (0xC504)
#define LIBRARY_DXEAMLGENERATIONLIB_AMLLOCALOBJECTS_FILECODE              (0xC505)
#define LIBRARY_DXEAMLGENERATIONLIB_AMLNAMEDOBJECT_FILECODE               (0xC506)
#define LIBRARY_DXEAMLGENERATIONLIB_AMLNAMESPACEMODIFIEROBJECTS_FILECODE  (0xC507)
#define LIBRARY_DXEAMLGENERATIONLIB_AMLNAMESTRING_FILECODE                (0xC508)
#define LIBRARY_DXEAMLGENERATIONLIB_AMLOBJECTSDEBUG_FILECODE              (0xC509)
#define LIBRARY_DXEAMLGENERATIONLIB_AMLPKGLENGTH_FILECODE                 (0xC50A)
#define LIBRARY_DXEAMLGENERATIONLIB_AMLRESOURCEDESCRIPTOR_FILECODE        (0xC50B)
#define LIBRARY_DXEAMLGENERATIONLIB_AMLSTATEMENTOPCODES_FILECODE          (0xC50C)
#define LIBRARY_DXEAMLGENERATIONLIB_AMLTABLE_FILECODE                     (0xC50D)
#define LIBRARY_DXEAMLGENERATIONLIB_LOCALAMLOBJECTS_FILECODE              (0xC50E)
#define LIBRARY_ACPITABLEHELPERLIB_ACPITABLEHELPERLIB_FILECODE            (0xC50F)

//0xC600 - 0xC6FF for Dash
#define DASH_ASFMSG_ASFMSG_FILECODE                                       (0xC600)
#define DASH_ASFTABLE_ASFTABLEASL_FILECODE                                (0xC601)
#define DASH_DASHPLDM_DASHPLDMBASE_DASHPLDMBASE_FILECODE                  (0xC602)
#define DASH_DASHPLDM_DASHPLDMBCC_BIOSATTRIBUTEPENDINGVALUETABLE_FILECODE (0xC603)
#define DASH_DASHPLDM_DASHPLDMBCC_BIOSATTRIBUTETABLE_FILECODE             (0xC604)
#define DASH_DASHPLDM_DASHPLDMBCC_BIOSATTRIBUTEVALUETABLE_FILECODE        (0xC605)
#define DASH_DASHPLDM_DASHPLDMBCC_BIOSSTRINGTABLE_FILECODE                (0xC606)
#define DASH_DASHPLDM_DASHPLDMBCC_DASHPLDMBCC_FILECODE                    (0xC607)
#define DASH_DASHPLDM_DASHPLDMBCC_SETBIOSTABLESTAGS_FILECODE              (0xC608)
#define DASH_DASHPLDM_DASHPLDMSMBIOS_DASHPLDMSMBIOS_FILECODE              (0xC609)
#define DASH_MCTPSMBUS_DASHMCTPSMBUSDXE_FILECODE                          (0xC60A)
#define DASH_SETSYSTEMSTATE_SETSYSTEMSTATE_FILECODE                       (0xC60B)
#define DASH_SMBIOSTYPEADD_SMBIOSTYPEADD_FILECODE                         (0xC60C)
#define LIBRARY_DASHASFSMBUSLIB_DASHASFSMBUSLIB_FILECODE                  (0xC60D)
#define LIBRARY_AMDPERFLIB_AMDPERFLIB_FILECODE                            (0xC60E)

//0xC700 - C70F for PlatformSecLib
#define LIBRARY_PLATFORMSECLIB_PLATFORMSECLIB_FILECODE                    (0xC700)

#endif // _FILECODE_H_

