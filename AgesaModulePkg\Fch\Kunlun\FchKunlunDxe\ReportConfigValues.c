/*****************************************************************************
 *
 * Copyright (C) 2008-2024 Advanced Micro Devices, Inc. All rights reserved.
 *
 *******************************************************************************
 */

#include <AMD.h>

#include <Library/DebugLib.h>
#include <Library/PcdLib.h>
#include <Library/UefiBootServicesTableLib.h>

#include <Library/AgesaConfigLib.h>
#include <ActOptions.h>
#include <Protocol/AmdApcbProtocol.h>

#include "FchPlatform.h"

typedef enum {
  Pcd1 = 0,
  Pcd8,
  Pcd16,
  Pcd32,
  Pcd64,
  Apcb1,
  Ap<PERSON>b8,
  Ap<PERSON>b16,
  Ap<PERSON>b32,
  Ap<PERSON>b64,
} FCHACTCONFIGTYPE;

typedef struct {
  FCHACTCONFIGTYPE  Type;
  UINT32            ActConfigUid;
  UINT32            PcdApcbName;
} FCHCONFIGVALUE;


STATIC CONST FCHCONFIGVALUE FchConfigList[] = {

  // I3C/I2C Configuration Options
  {Apcb8, ACT_CFG_UID_CmnFchI3C0Config,                   APCB_TOKEN_UID_I2C_I3C_SMBUS_0                    },
  {Apcb8, ACT_CFG_UID_CmnFchI3C1Config,                   APCB_TOKEN_UID_I2C_I3C_SMBUS_1                    },
  {Apcb8, ACT_CFG_UID_CmnFchI3C2Config,                   APCB_TOKEN_UID_I2C_I3C_SMBUS_2                    },
  {Apcb8, ACT_CFG_UID_CmnFchI3C3Config,                   APCB_TOKEN_UID_I2C_I3C_SMBUS_3                    },
  {Apcb8, ACT_CFG_UID_CmnFchI2C4Config,                   APCB_TOKEN_UID_I2C_I3C_SMBUS_4                    },
  {Apcb8, ACT_CFG_UID_CmnFchI2C5Config,                   APCB_TOKEN_UID_I2C_I3C_SMBUS_5                    },
  // {Apcb8, ACT_CFG_UID_CmnFchI3C0Mode,                     APCB_TOKEN_UID_I2CI3C_CONTROLLER0                 },
  // {Apcb8, ACT_CFG_UID_CmnFchI3C1Mode,                     APCB_TOKEN_UID_I2CI3C_CONTROLLER1                 },
  // {Apcb8, ACT_CFG_UID_CmnFchI3C2Mode,                     APCB_TOKEN_UID_I2CI3C_CONTROLLER2                 },
  // {Apcb8, ACT_CFG_UID_CmnFchI3C3Mode,                     APCB_TOKEN_UID_I2CI3C_CONTROLLER3                 },
  {Pcd1,  ACT_CFG_UID_CmnFchReleaseSpdHostControl,        PcdToken (PcdAmdFchSpdHostCtrlRelease)            },
  {Pcd1,  ACT_CFG_UID_CmnFchPMFWDdr5Telemetry,            PcdToken (PcdAmdFchDimmTelemetry)                 },
  {Pcd8,  ACT_CFG_UID_CmnFchI2cSdaHoldOverride,           PcdToken (PcdAmdFchI2cSdaHoldOverride)            },
  {Apcb8, ACT_CFG_UID_CmnFchApmlSbtsiSlvMode,             APCB_TOKEN_UID_APML_SBTSI_SLAVE_MODE              },
  {Apcb8, ACT_CFG_UID_CmnFchI3cModeSpeed,                 APCB_TOKEN_UID_FCH_I3C_TRANSFER_SPEED             },
  {Apcb8, ACT_CFG_UID_CmnFchI3cSdaHoldOverride,           APCB_TOKEN_UID_FCH_I3C_SDA_HOLD_OVERRIDE          },

  // SATA Configuration Options
  {Apcb1, ACT_CFG_UID_CmnFchSataEnable,                   APCB_TOKEN_UID_FCH_SATA_ENABLE                    },
  {Pcd8,  ACT_CFG_UID_CmnFchSataClass,                    PcdToken (PcdSataClass)                           },
  {Pcd1,  ACT_CFG_UID_CmnFchSataRasSupport,               PcdToken (PcdSataRasSupport)                      },
  {Pcd1,  ACT_CFG_UID_CmnFchSataStaggeredSpinup,          PcdToken (PcdSataStaggeredSpinup)                 },
  {Pcd1,  ACT_CFG_UID_CmnFchSataAhciDisPrefetchFunction,  PcdToken (PcdSataAhciDisPrefetchFunction)         },
  {Pcd1,  ACT_CFG_UID_DbgFchSataAggresiveDevSlpP0,        PcdToken (PcdSataDevSlpPort0)                     },
  {Pcd1,  ACT_CFG_UID_DbgFchSataAggresiveDevSlpP1,        PcdToken (PcdSataDevSlpPort1)                     },
  {Apcb1, ACT_CFG_UID_DbgFchSata0Enable,                  APCB_TOKEN_UID_FCH_SATA_0_ENABLE                  },
  {Apcb1, ACT_CFG_UID_DbgFchSata1Enable,                  APCB_TOKEN_UID_FCH_SATA_1_ENABLE                  },
  {Apcb1, ACT_CFG_UID_DbgFchSata2Enable,                  APCB_TOKEN_UID_FCH_SATA_2_ENABLE                  },
  {Apcb1, ACT_CFG_UID_DbgFchSata3Enable,                  APCB_TOKEN_UID_FCH_SATA_3_ENABLE                  },
  {Apcb1, ACT_CFG_UID_DbgFchSata4Enable,                  APCB_TOKEN_UID_FCH_SATA_4_ENABLE                  },
  {Apcb1, ACT_CFG_UID_DbgFchSata5Enable,                  APCB_TOKEN_UID_FCH_SATA_5_ENABLE                  },
  {Apcb1, ACT_CFG_UID_DbgFchSata6Enable,                  APCB_TOKEN_UID_FCH_SATA_6_ENABLE                  },
  {Apcb1, ACT_CFG_UID_DbgFchSata7Enable,                  APCB_TOKEN_UID_FCH_SATA_7_ENABLE                  },

  // USB Configuration Options
  {Pcd1,  ACT_CFG_UID_CmnFchUsbXHCI0Enable,               PcdToken (PcdXhci0Enable)                         },
  {Pcd1,  ACT_CFG_UID_CmnFchUsbXHCI1Enable,               PcdToken (PcdXhci1Enable)                         },
  {Pcd1,  ACT_CFG_UID_CmnFchUsbXHCI2Enable,               PcdToken (PcdXhci2Enable)                         },
  {Pcd1,  ACT_CFG_UID_CmnFchUsbXHCI3Enable,               PcdToken (PcdXhci3Enable)                         },

  // Ac Power Loss Options
  {Pcd8,  ACT_CFG_UID_CmnFchSystemPwrFailShadow,          PcdToken (PcdPwrFailShadow)                       },

  // Uart Configuration Options
  {Pcd8,  ACT_CFG_UID_CmnFchUart0LegacyConfig,            PcdToken (FchUart0LegacyEnable)                   },
  {Pcd8,  ACT_CFG_UID_CmnFchUart1LegacyConfig,            PcdToken (FchUart1LegacyEnable)                   },
  {Pcd8,  ACT_CFG_UID_CmnFchUart2LegacyConfig,            PcdToken (FchUart2LegacyEnable)                   },
  {Pcd8,  ACT_CFG_UID_CmnFchUart3LegacyConfig,            PcdToken (FchUart3LegacyEnable)                   },

  // FCH RAS Options
  {Pcd1,  ACT_CFG_UID_CmnFchAlinkRasSupport,              PcdToken (PcdAmdFchAlinkRasSupport)               },
  {Pcd1,  ACT_CFG_UID_DbgFchSyncfloodEnable,              PcdToken (PcdResetCpuOnSyncFlood)                 },

  // Miscellaneous Options
  {Pcd1,  ACT_CFG_UID_DbgFchSystemSpreadSpectrum,         PcdToken (PcdSpreadSpectrum)                      },
  {Pcd1,  ACT_CFG_UID_CmnBootTimerEnable,                 PcdToken (PcdBootTimerEnable)                     },
};

STATIC UINT32  FchConfigListSize = sizeof (FchConfigList) / sizeof (FCHCONFIGVALUE);

AMD_APCB_SERVICE_PROTOCOL *mApcbDxeServiceProtocol = NULL;

STATIC
EFI_STATUS
GetApcbValue (
  IN      FCHACTCONFIGTYPE  Type,
  IN      UINT32            ApcbToken,
      OUT VOID              *ApcbValue
  )
{
  EFI_STATUS  Status;
  UINT8       ApcbPurpose;

  Status      = EFI_UNSUPPORTED;
  ApcbPurpose = 0;

  if ( mApcbDxeServiceProtocol == NULL ) {
    Status = gBS->LocateProtocol (&gAmdApcbDxeServiceProtocolGuid, NULL, (VOID **) &mApcbDxeServiceProtocol);
    IDS_HDT_CONSOLE (FCH_TRACE, "ERROR: Can not find APCB service Status = %r\n", Status);
    ASSERT (!EFI_ERROR (Status));
  }

  switch (Type) {
    case Apcb1:
      Status = mApcbDxeServiceProtocol->ApcbGetTokenBool (
                                          mApcbDxeServiceProtocol,
                                          &ApcbPurpose,
                                          ApcbToken,
                                          (BOOLEAN*) ApcbValue
                                          );
      break;
    case Apcb8:
      Status = mApcbDxeServiceProtocol->ApcbGetToken8 (
                                          mApcbDxeServiceProtocol,
                                          &ApcbPurpose,
                                          ApcbToken,
                                          (UINT8*) ApcbValue
                                          );
      break;
    case Apcb16:
      Status = mApcbDxeServiceProtocol->ApcbGetToken16 (
                                          mApcbDxeServiceProtocol,
                                          &ApcbPurpose,
                                          ApcbToken,
                                          (UINT16*) ApcbValue
                                          );
      break;
    case Apcb32:
      Status = mApcbDxeServiceProtocol->ApcbGetToken32 (
                                          mApcbDxeServiceProtocol,
                                          &ApcbPurpose,
                                          ApcbToken,
                                          (UINT32*) ApcbValue
                                          );
      break;
    default:
      break;
  }

  return Status;
}


STATIC
VOID
FchReportConfigValuesCommon (
  VOID
  )
{
  UINT32  i;
  BOOLEAN Value1;
  UINT8   Value8;
  UINT16  Value16;
  UINT32  Value32;

  i       = 0;
  Value1  = 0;
  Value8  = 0;
  Value16 = 0;
  Value32 = 0;

  IDS_HDT_CONSOLE (FCH_TRACE, "%a Start\n", __FUNCTION__);

  for (i = 0; i < FchConfigListSize; i++) {
    switch (FchConfigList[i].Type) {
      case Pcd1:
        SetAgesaCfg8 (FchConfigList[i].ActConfigUid, (UINT8) LibPcdGetBool (FchConfigList[i].PcdApcbName));
        break;
      case Pcd8:
        SetAgesaCfg8 (FchConfigList[i].ActConfigUid, LibPcdGet8 (FchConfigList[i].PcdApcbName));
        break;
      case Pcd16:
        SetAgesaCfg16 (FchConfigList[i].ActConfigUid, LibPcdGet16 (FchConfigList[i].PcdApcbName));
        break;
      case Pcd32:
        SetAgesaCfg32 (FchConfigList[i].ActConfigUid, LibPcdGet32 (FchConfigList[i].PcdApcbName));
        break;
      case Apcb1:
        Value1 = 0;
        if ( GetApcbValue (FchConfigList[i].Type, FchConfigList[i].PcdApcbName, (VOID*) &Value1) == EFI_SUCCESS ) {
          SetAgesaCfg8 (FchConfigList[i].ActConfigUid, (UINT8) Value1);
        } else {
          IDS_HDT_CONSOLE (FCH_TRACE, "[%a] Can not find APCB token 0x%x\n", __FUNCTION__, FchConfigList[i].PcdApcbName);
        }
        break;
      case Apcb8:
        Value8 = 0;
        if ( GetApcbValue (FchConfigList[i].Type, FchConfigList[i].PcdApcbName, (VOID*) &Value8) == EFI_SUCCESS ) {
          SetAgesaCfg8 (FchConfigList[i].ActConfigUid, Value8);
        } else {
          IDS_HDT_CONSOLE (FCH_TRACE, "[%a] Can not find APCB token 0x%x\n", __FUNCTION__, FchConfigList[i].PcdApcbName);
        }
        break;
      case Apcb16:
        Value16 = 0;
        if ( GetApcbValue (FchConfigList[i].Type, FchConfigList[i].PcdApcbName, (VOID*) &Value16) == EFI_SUCCESS ) {
          SetAgesaCfg16 (FchConfigList[i].ActConfigUid, Value16);
        } else {
          IDS_HDT_CONSOLE (FCH_TRACE, "[%a] Can not find APCB token 0x%x\n", __FUNCTION__, FchConfigList[i].PcdApcbName);
        }
        break;
      case Apcb32:
        Value32 = 0;
        if ( GetApcbValue (FchConfigList[i].Type, FchConfigList[i].PcdApcbName, (VOID*) &Value32) == EFI_SUCCESS ) {
          SetAgesaCfg32 (FchConfigList[i].ActConfigUid, Value32);
        } else {
          IDS_HDT_CONSOLE (FCH_TRACE, "[%a] Can not find APCB token 0x%x\n", __FUNCTION__, FchConfigList[i].PcdApcbName);
        }

        break;
      case Pcd64:
      case Apcb64:
      default:
        break;
    }
  }

  IDS_HDT_CONSOLE (FCH_TRACE, "%a End\n", __FUNCTION__);
}


STATIC
VOID
FchReportConfigValuesSpecial (
  VOID
  )
{
  IDS_HDT_CONSOLE (FCH_TRACE, "%a Start\n", __FUNCTION__);
  IDS_HDT_CONSOLE (FCH_TRACE, "%a End\n", __FUNCTION__);
}


/**
 * @brief Reports PCD values to AGESA configuration Database
 *
 * @details Reports PCD values to AGESA configuration Database
 *
 * @return AGESA_STATUS
 * @retval AGESA_SUCCESS   Procedure successfully
 * @retval AGESA_ERROR     Procedure failed (see error for more details)
 */
VOID
FchReportConfigValues (
  IN      FCH_DATA_BLOCK     *LateParams
  )
{
  IDS_HDT_CONSOLE (FCH_TRACE, "%a Start\n", __FUNCTION__);
  FchReportConfigValuesCommon ();
  FchReportConfigValuesSpecial ();
  IDS_HDT_CONSOLE (FCH_TRACE, "%a End\n", __FUNCTION__);
}



