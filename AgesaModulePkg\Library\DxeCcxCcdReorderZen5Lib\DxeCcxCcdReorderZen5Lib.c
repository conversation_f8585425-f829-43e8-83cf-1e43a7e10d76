/*****************************************************************************
 *
 * Copyright (C) 2019-2024 Advanced Micro Devices, Inc. All rights reserved.
 *
 *******************************************************************************
 */
#include <PiDxe.h>
#include <Protocol/AmdCoreTopologyV3Protocol.h>
#include <Library/IdsLib.h>
#include <Library/CcxCcdReorderLib.h>
#include <Filecode.h>

#define FILECODE LIBRARY_DXECCXCCDREORDERZEN5LIB_DXECCXCCDREORDERZEN5LIB_FILECODE

/*----------------------------------------------------------------------------------------
 *                           G L O B A L   V A R I A B L E S
 *----------------------------------------------------------------------------------------
 */

/**
 * Re-order the logical CCD number array with the CCD NUMA domain order for Zen5.
 *
 * @param[in]  CoreTopology                   For PEI phase, a pointer to the
 *                                            AMD_CORE_TOPOLOGY_SERVICES_V3_PPI instance.
 *                                            For DXE phase, a pointer to the
 *                                            AMD_CORE_TOPOLOGY_SERVICES_V3_PROTOCOL instance.
 * @param[in]  LogicalSocket                  Zero-based logical socket number.
 * @param[in]  LogicalDie                     Zero-based logical die number.
 * @param[in]  NumberOfCcds                   The number of logical CCDs.
 * @param[out] OrderedLogicalCcd              The ordered logical CCD array.
 *
 * @retval EFI_SUCCESS                       Operation successfully completed.
 * @retval EFI_INVALID_PARAMETER             CoreTopology services is NULL.
 * @retval EFI_INVALID_PARAMETER             Specified location is not available.
 * @retval EFI_NOT_FOUND                     No next item.
 *
 **/
EFI_STATUS
EFIAPI
ReOrderLogicalCcdWithNumaDomainOrder (
  IN  VOID                                    *CoreTopology,
  IN  UINTN                                   LogicalSocket,
  IN  UINTN                                   LogicalDie,
  IN  UINTN                                   NumberOfCcds,
  OUT UINTN                                   *OrderedLogicalCcd
  )
{
  // CcdNum guarantees SRAT entries are entered in increasing order of NUMA domain
  UINTN      PhyCcdOrder[] = {0, 4, 8, 12, 2, 6, 10, 14, 3, 7, 11, 15, 1, 5, 9, 13};
  UINTN      i;
  UINTN      j;
  UINTN      k;
  UINTN      PhySocketNum;
  UINTN      PhyDieNum;
  UINTN      PhyCcdNum;
  EFI_STATUS Status;
  struct {
    UINTN LogicalId;
    UINTN PhysicalId;
  } CcdMap[ARRAY_SIZE (PhyCcdOrder)];
  AMD_CORE_TOPOLOGY_SERVICES_V3_PROTOCOL *CoreTopologyServices;

  CoreTopologyServices = CoreTopology;

  if (CoreTopologyServices == NULL || OrderedLogicalCcd == NULL) {
    return EFI_INVALID_PARAMETER;
  }
  if (NumberOfCcds > ARRAY_SIZE (PhyCcdOrder)) {
    ASSERT (FALSE);
    return EFI_INVALID_PARAMETER;
  }

  IDS_HDT_CONSOLE (MAIN_FLOW, "Logical CCD    Physical CCD\n");
  for (i = 0; i < NumberOfCcds; i++) {
    PhySocketNum = LogicalSocket;
    PhyDieNum    = LogicalDie;
    PhyCcdNum    = i;
    Status = CoreTopologyServices->LogicalToPhysicalLocation (CoreTopologyServices, &PhySocketNum, &PhyDieNum, &PhyCcdNum, NULL, NULL);
    ASSERT (!EFI_ERROR (Status));
    CcdMap[i].LogicalId = i;
    CcdMap[i].PhysicalId = PhyCcdNum;
    IDS_HDT_CONSOLE (MAIN_FLOW, "        %02d             %02d\n", i, PhyCcdNum);
  }

  IDS_HDT_CONSOLE (MAIN_FLOW, "Ordered Logical CCD: ");
  k = 0;
  for (i = 0; i < ARRAY_SIZE (PhyCcdOrder); i++) {
    for (j = 0; j < NumberOfCcds; j++) {
      if (PhyCcdOrder[i] == CcdMap[j].PhysicalId) {
        OrderedLogicalCcd[k] = CcdMap[j].LogicalId;
        k++;
        IDS_HDT_CONSOLE (MAIN_FLOW, "%d ", CcdMap[j].LogicalId);
      }
    }
  }
  IDS_HDT_CONSOLE (MAIN_FLOW, "\n");
  ASSERT (k == NumberOfCcds);

  return EFI_SUCCESS;
}

