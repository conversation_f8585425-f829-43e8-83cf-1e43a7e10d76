#;*****************************************************************************
#;
#; Copyright (C) 2008-2024 Advanced Micro Devices, Inc. All rights reserved.
#;
#;******************************************************************************

[defines]
  INF_VERSION                    = 0x00010005
  BASE_NAME                      = FchKunlunMultiFchSmm
  FILE_GUID                      = 67d1c54d-b8f7-4d69-a8f3-ff9116c4c02a
  MODULE_TYPE                    = DXE_SMM_DRIVER
  PI_SPECIFICATION_VERSION       = 0x0001000A
  VERSION_STRING                 = 1.1
  ENTRY_POINT                    = MultiFchSmmInit


[sources.common]
  FchMultiFchSmm.c
  FchMultiFchSmm.h

[LibraryClasses]
  FchKunlunDxeLib
  FchSmmLibV9
  FabricRegisterAccLib
  NbioHandleLib
  NbioSmuBrhLib

  UefiDriverEntryPoint
  BaseMemoryLib
  BaseLib
  DebugLib
  HobLib
  UefiLib
  PcdLib
  SmmServicesTableLib
  UefiRuntimeServicesTableLib
  UefiBootServicesTableLib
  DevicePathLib
  MemoryAllocationLib

[Guids]

[Protocols]
  gFchSmmSxDispatch2ProtocolGuid                #CONSUMED
  gFchSmmSwDispatch2ProtocolGuid                #CONSUMED
  gFchSmmInitProtocolGuid                       #CONSUMED
  gFchMultiFchInitProtocolGuid                  #CONSUMED
  gFchInitProtocolGuid                          #CONSUMED
  gAmdFabricTopologyServices2SmmProtocolGuid    #CONSUMED
  gPspMboxSmmBufferAddressProtocolGuid          #CONSUMED
  gEdkiiSmmReadyToBootProtocolGuid              #CONSUMED

[Packages]
  MdePkg/MdePkg.dec
  MdeModulePkg/MdeModulePkg.dec
  AgesaPkg/AgesaPkg.dec
  AgesaModulePkg/AgesaModuleFchPkg.dec
  AgesaModulePkg/AgesaModulePspPkg.dec
  AgesaModulePkg/Fch/Kunlun/FchKunlun.dec
  AgesaModulePkg/AgesaModuleDfPkg.dec

[Pcd]
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdSataEnable2
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdSataMultiDiePortShutDown
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdSataMultiDiePortESP
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdSataMultiDieDevSlp
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdSataSgpioMultiDieEnable
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdSataIoDie1PortMode
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdFchOemBeforePciRestoreSwSmi
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdFchOemAfterPciRestoreSwSmi
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdFchOemCaptureSPDBusSmi
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdFchOemReleaseSPDBusSmi
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdXhci2Enable
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdXhci3Enable
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdXhciUsb2PortDisable
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdXhciUsb3PortDisable
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdSataUBMDiagMode

[Depex]
  gFchSmmInitProtocolGuid  AND
  gFchSmmSxDispatch2ProtocolGuid AND
  gFchSmmSwDispatch2ProtocolGuid  AND
  gAmdFchKLMultiFchDepexProtocolGuid AND
  gAmdFabricTopologyServices2SmmProtocolGuid


