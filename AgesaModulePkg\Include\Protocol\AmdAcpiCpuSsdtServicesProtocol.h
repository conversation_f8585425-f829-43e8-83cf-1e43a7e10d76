/*****************************************************************************
 *
 * Copyright (C) 2016-2024 Advanced Micro Devices, Inc. All rights reserved.
 *
 *******************************************************************************
 **/
/* $NoKeywords:$ */
/**
 * @file
 *
 * CCX base Services Protocol prototype definition
 *
 *
 * @xrefitem bom "File Content Label" "Release Content"
 * @e project:      AGESA
 * @e sub-project:  Library
 * @e \$Revision: 313706 $   @e \$Date: 2015-02-25 21:00:43 -0600 (Wed, 25 Feb 2015) $
 */
#ifndef _AMD_ACPI_CPU_SSDT_SERVICES_PROTOCOL_H_
#define _AMD_ACPI_CPU_SSDT_SERVICES_PROTOCOL_H_

#pragma pack (push, 1)

#define MAX_THREAD_NUMBER_PER_SOCKET 512

/// P-state structure for each state.
typedef struct {
  UINT32 PStateEnable;   ///< Pstate enable.
  UINT32 CoreFreq;       ///< Frequency in MHz.
  UINT32 Power;          ///< Power in milliWatts.
  UINT32 SwPstateNumber; ///< Software P-state number.
} S_PSTATE_VALUES;

/// P-state structure for socket.
typedef struct {
  UINT8           SocketNumber;                              ///< Physical socket number of this socket.
  UINT16          TotalLogicalCores;                         ///< Logical core number in this socket.
  BOOLEAN         CreateAcpiTables;                          ///< Create table flag.
  UINT32          LocalApicId[MAX_THREAD_NUMBER_PER_SOCKET]; ///< Local Apic Id.
  UINT8           PStateMaxValue;                            ///< Max p-state number in this core.
  UINT32          TransitionLatency;                         ///< Transition latency.
  BOOLEAN         IsPsdDependent;                            ///< Dependent or Independent PSD.
  S_PSTATE_VALUES PStateStruct[1];                           ///< P-state structure. @see S_PSTATE_VALUES
} AMD_PSTATE_SOCKET_INFO;

/// P-state structure for whole system
typedef struct {
  UINT8                  TotalSocketInSystem;   ///< Total node number in system.
  UINT32                 SizeOfBytes;           ///< Structure size.
  AMD_PSTATE_SOCKET_INFO PStateSocketStruct[1]; ///< P-state structure for socket. @see AMD_PSTATE_SOCKET_INFO
} AMD_PSTATE_SYS_INFO;

/// C-state structure
typedef struct {
  BOOLEAN IsCstateEnabled;         ///< Is Cstate enabled.
  UINT32  IoCstateAddr;            ///< Io Cstate address.
  BOOLEAN IsCsdGenerated;          ///< Is _CSD needed.
  BOOLEAN IsMonitorMwaitSupported; ///< Should C1 FFH be declared.
  UINT16  C1Latency;               ///< C1 Latency.
  UINT16  C2Latency;               ///< C2 Latency.
  UINT16  C3Latency;               ///< C3 Latency. A non-zero value results in C3 declaration.
  UINT16  C4Latency;               ///< C4 Latency. A non-zero value results in C4 declaration. Supported in revision 1.
} AMD_CSTATE_INFO;

/// Forward declaration for the AMD_ACPI_CPU_SSDT_SERVICES_PROTOCOL.
typedef struct _AMD_ACPI_CPU_SSDT_SERVICES_PROTOCOL AMD_ACPI_CPU_SSDT_SERVICES_PROTOCOL;

/**
 * @brief Gathers P-state information from the MSRs.
 *
 * @param[in]  This             Pointer to the SSDT services protocol instance. @see AMD_ACPI_CPU_SSDT_SERVICES_PROTOCOL
 * @param[out] PstateSysInfoPtr Contains P-state information for the whole system.
 *
 * @return EFI_STATUS - status of the operation.
 */
typedef
EFI_STATUS
(EFIAPI *AMD_SSDT_SERVICES_GET_PSTATE_INFO) (
  IN     AMD_ACPI_CPU_SSDT_SERVICES_PROTOCOL *This,
     OUT AMD_PSTATE_SYS_INFO                **PstateSysInfoPtr
  );

/**
 * @brief Gathers C-state information.
 *
 * @param[in]  This       Pointer to the SSDT services protocol instance. @see AMD_ACPI_CPU_SSDT_SERVICES_PROTOCOL
 * @param[out] CstateInfo Contains C-state information.
 *
 * @return EFI_STATUS - status of the operation.
 */
typedef
EFI_STATUS
(EFIAPI *AMD_SSDT_SERVICES_GET_CSTATE_INFO) (
  IN     AMD_ACPI_CPU_SSDT_SERVICES_PROTOCOL *This,
     OUT AMD_CSTATE_INFO                    **CstateInfo
  );

/**
 * @brief Returns PSD domain for independency.
 *
 * @param[in] This        Pointer to the SSDT services protocol instance. @see AMD_ACPI_CPU_SSDT_SERVICES_PROTOCOL
 * @param[in] LocalApicId Local APIC ID.
 *
 * @return PSD domain for the given core.
 */
typedef
UINT32
(EFIAPI *AMD_SSDT_SERVICES_GET_PSD_DOMAIN_FOR_INDEP) (
  IN     AMD_ACPI_CPU_SSDT_SERVICES_PROTOCOL *This,
  IN     UINT32                               LocalApicId
  );

/**
 * @brief Gets the power in mW for specified P-state
 *
 * @param[in]  This      Pointer to the SSDT services protocol instance. @see AMD_ACPI_CPU_SSDT_SERVICES_PROTOCOL
 * @param[in]  Pstate    P-state to read. @see CCX_PSTATE
 * @param[out] PowerInmW Pointer to store power in mW for the specified P-state.
 *
 * @return EFI_STATUS - status of the operation.
 */
typedef
EFI_STATUS
(EFIAPI *AMD_SSDT_SERVICES_GET_PSTATE_POWER) (
  IN     AMD_ACPI_CPU_SSDT_SERVICES_PROTOCOL *This,
  IN     CCX_PSTATE                           Pstate,
     OUT UINTN                               *PowerInmW
  );

/// ACPI Processor SSDT services protocol
struct _AMD_ACPI_CPU_SSDT_SERVICES_PROTOCOL {
  UINTN                                      Revision;       ///< Revision Number.
  AMD_SSDT_SERVICES_GET_PSTATE_INFO          GetPstateInfo;  ///< @see AMD_SSDT_SERVICES_GET_PSTATE_INFO
  AMD_SSDT_SERVICES_GET_CSTATE_INFO          GetCstateInfo;  ///< @see AMD_SSDT_SERVICES_GET_CSTATE_INFO
  AMD_SSDT_SERVICES_GET_PSD_DOMAIN_FOR_INDEP GetPsdDomain;   ///< @see AMD_SSDT_SERVICES_GET_PSD_DOMAIN_FOR_INDEP
  AMD_SSDT_SERVICES_GET_PSTATE_POWER         GetPstatePower; ///< @see AMD_SSDT_SERVICES_GET_PSTATE_POWER
};

/// GUID for SSDT services protocol.
extern EFI_GUID gAmdAcpiCpuSsdtServicesProtocolGuid;

#pragma pack (pop)

#endif // _AMD_ACPI_CPU_SSDT_SERVICES_PROTOCOL_H_

