/*****************************************************************************
 * Copyright (C) 2008-2024 Advanced Micro Devices, Inc. All rights reserved.
 *
*****************************************************************************
*/

#ifndef _FCH_MCM_PEI_H_
#define _FCH_MCM_PEI_H_

#include <Library/DebugLib.h>
#include <Library/BaseMemoryLib.h>
#include <Library/FchPeiLib.h>
#include "FchPlatform.h"
#include <Library/FchBaseLib.h>
#include <Library/FabricResourceManagerLib.h>
#include <Library/FabricRegisterAccLib.h>
#include <Library/AmdCapsuleLib.h>

#include <Ppi/AmdFchInitPpi.h>
#include <Ppi/AmdMultiFchInitPpi.h>
#include <Ppi/FabricTopologyServices2Ppi.h>
#include <Ppi/PciCfg2.h>

/// Module data structure
typedef struct _FCH_MULTI_FCH_PEI_PRIVATE {
  UINTN                            Signature;          ///< Signature
  AMD_FCH_MULTI_FCH_INIT_PPI       FchMultiFchInitPpi; ///< Multi FCH INIT PPI
  UINT64                           Reserved[8];        ///< Reserved
} FCH_MULTI_FCH_PEI_PRIVATE;

#define MULTI_FCH_PEI_PRIVATE_DATA_SIGNATURE   SIGNATURE_32 ('$', 'F', 'M', 'F')

#define FCH_MULTI_FCH_PEI_PRIVATE_FROM_THIS(a) \
  (FCH_MULTI_FCH_PEI_PRIVATE*) (CR ((a), FCH_MULTI_FCH_PEI_PRIVATE, FchMultiFchInitPpi, MULTI_FCH_PEI_PRIVATE_DATA_SIGNATURE))

EFI_STATUS
EFIAPI
MultiFchPeiInit (
  IN       EFI_PEI_FILE_HANDLE  FileHandle,
  IN CONST EFI_PEI_SERVICES     **PeiServices
);

#if 0
STATIC
VOID
FchSecondaryFchInitPei (
  IN  UINT32      DieBusNum,
  IN  VOID        *FchDataPtr
);
#endif

STATIC
VOID
FchSecondaryFchInitUsbPei (
  IN  UINT32      DieBusNum,
  IN  VOID        *FchDataPtr
);

STATIC
VOID
FchSecondaryFchInitSataPei (
  IN  UINT32      DieBusNum,
  IN  VOID        *FchDataPtr
);

STATIC
VOID
FchSecondaryFchMmioRelocate (
  IN CONST EFI_PEI_SERVICES     **PeiServices,
  IN  UINT32      DieBusNum,
  IN  VOID        *FchDataPtr
);

STATIC
VOID
FchSecondaryFchIORegInit (
  IN  UINT32      DieBusNum,
  IN  VOID        *FchDataPtr
);

STATIC
VOID
FchSecondaryFchInitABPei (
  IN  UINT32      DieBusNum,
  IN  VOID        *FchDataPtr
);

#if 0
STATIC
VOID
FchSecondaryFchInitPmioPei (
  IN  UINT32      DieBusNum,
  IN  VOID        *FchDataPtr
);

STATIC
VOID
FchSecondaryFchSpreadSpectrum (
  IN  UINT8       Die,
  IN  VOID        *FchDataPtr
);
#endif
STATIC
VOID
FchSecondaryFchDisableSD (
  IN  UINT32      DieBusNum,
  IN  VOID        *FchDataPtr
);

#endif // _FCH_MCM_PEI_H_


