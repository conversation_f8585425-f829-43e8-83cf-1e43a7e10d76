#;*****************************************************************************
#;
#; Copyright (C) 2008-2024 Advanced Micro Devices, Inc. All rights reserved.
#;
#;******************************************************************************

[Defines]

  INF_VERSION                    = 0x00010006
  BASE_NAME                      = FchDxeLibV9
  FILE_GUID                      = 5807E3C3-AA6E-46a4-A99D-3AB221F9E485
  MODULE_TYPE                    = DXE_DRIVER
  VERSION_STRING                 = 1.0
  LIBRARY_CLASS                  = FchDxeLibV9


#
# The following information is for reference only and not required by the build tools.
#
#  VALID_ARCHITECTURES           = IA32 X64
#

[Sources]
  FchDxeLib.c

[Packages]
  MdePkg/MdePkg.dec
  AgesaModulePkg/AgesaModuleFchPkg.dec

[LibraryClasses]
  IoLib
  PciLib
  BaseMemoryLib
  UefiBootServicesTableLib
  UefiRuntimeServicesTableLib

[Guids]

[Protocols]
  #gEfiPciRootBridgeIoProtocolGuid
[Ppis]

[Pcd]

[Depex]
  #gEfiPciRootBridgeIoProtocolGuid

[BuildOptions]





