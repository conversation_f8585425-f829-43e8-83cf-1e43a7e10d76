#;*****************************************************************************
#;
#; Copyright (C) 2008-2024 Advanced Micro Devices, Inc. All rights reserved.
#;
#;******************************************************************************

[defines]

  INF_VERSION                   = 0x00010005
  BASE_NAME                     = FchI2cMasterDxe
  FILE_GUID                     = 151664d4-6001-478f-8c97-c51420fc2b75
  MODULE_TYPE                   = DXE_DRIVER
  VERSION_STRING                = 1.0
  ENTRY_POINT                   = AmdI2cMasterDxeInit


[sources.common]
  I2cMasterDxe.c
  I2cMasterDxe.h

[LibraryClasses.common.DXE_DRIVER]
  BaseLib
  UefiLib
  HobLib
  PrintLib

[LibraryClasses]
  FchBaseLib
  FchI2cLib
  FchSocLib
  FabricRegisterAccLib
  BaseFabricTopologyLib
  UefiDriverEntryPoint
  UefiBootServicesTableLib
  DebugLib
  AmdBaseLib
  ApobCommonServiceLib

[Guids]

[Protocols]
  gEfiI2cMasterProtocolGuid         #PRODUCED
  gEfiDevicePathProtocolGuid        #CONSUMED
  gAmdApcbDxeServiceProtocolGuid    #CONSUMED
  gAmdFabricTopologyServices2ProtocolGuid
[Ppis]

[Packages]
  MdePkg/MdePkg.dec
  AgesaPkg/AgesaPkg.dec
  AgesaModulePkg/AgesaModuleFchPkg.dec
  AgesaModulePkg/AgesaCommonModulePkg.dec
  AgesaModulePkg/AgesaModuleDfPkg.dec

[Pcd]
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdAmdEnvironmentFlag

[Depex]
  gAmdApcbDxeServiceProtocolGuid


