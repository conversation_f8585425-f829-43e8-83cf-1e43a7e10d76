/*****************************************************************************
 *
 * Copyright (C) 2015-2024 Advanced Micro Devices, Inc. All rights reserved.
 *
 *******************************************************************************
 */

#ifndef _AMD_I2C_MASTER_PEI_H_
#define _AMD_I2C_MASTER_PEI_H_

#include <Library/FchI2cLib.h>
#include <Library/DebugLib.h>
#include <Library/PcdLib.h>
#include <Library/BaseMemoryLib.h>
#include <Library/FchBaseLib.h>
#include <Ppi/AmdFchInitPpi.h>

/// I2cMaster Private struct
typedef struct _FCH_EFI_PEI_I2C_MASTER_PPI {
  EFI_PEI_I2C_MASTER_PPI    I2cPpi;                     // I2cPpi
  UINT8                     I2cControllerInitialized;   // I2cControllerInitialized
  UINT32                    ControllerNum;              // ControllerNum;
  UINT32                    I2cBaseAddress;             // I2cBaseAddress
  UINT32                    I2cSdaHoldTime;             // I2cSdaHoldTime
  UINT32                    I2cBusFrequency;            // I2cBusFrequency
  UINT32                    RxFifoDepth;                // RxFifoDepth
  UINT32                    TxFifoDepth;                // TxFifoDepth
} FCH_EFI_PEI_I2C_MASTER_PPI;


EFI_STATUS
EFIAPI
SetBusFrequency (
  IN  EFI_PEI_I2C_MASTER_PPI    *This,
  IN  UINTN                     *BusClockHertz
  );

EFI_STATUS
EFIAPI
Reset (
  IN CONST EFI_PEI_I2C_MASTER_PPI   *This
  );

EFI_STATUS
EFIAPI
StartRequest (
  IN  CONST EFI_PEI_I2C_MASTER_PPI      *This,
  IN  UINTN                             SlaveAddress,
  IN  EFI_I2C_REQUEST_PACKET            *RequestPacket
  );

EFI_STATUS
AmdI2cMasterPeiInit (
  IN  EFI_PEI_FILE_HANDLE     FileHandle,
  IN  CONST EFI_PEI_SERVICES  **PeiServices
  );

EFI_STATUS
EFIAPI
FchI2CInitCallback (
  IN  EFI_PEI_SERVICES                **PeiServices,
  IN  EFI_PEI_NOTIFY_DESCRIPTOR       *NotifyDesc,
  IN  VOID                            *InvokePpi
  );

#endif // _AMD_I2C_MASTER_PEI_H_

