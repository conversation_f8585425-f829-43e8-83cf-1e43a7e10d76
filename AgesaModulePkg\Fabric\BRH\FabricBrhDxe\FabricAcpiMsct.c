/*****************************************************************************
 *
 * Copyright (C) 2019-2024 Advanced Micro Devices, Inc. All rights reserved.
 *
 *****************************************************************************
 */

/*----------------------------------------------------------------------------------------
 *                             M O D U L E S    U S E D
 *----------------------------------------------------------------------------------------
 */

#include "AGESA.h"
#include <Library/AmdBaseLib.h>
#include <Library/IdsLib.h>
#include <Library/UefiBootServicesTableLib.h>
#include <Library/CoreTopologyV3Lib.h>
#include <Protocol/AmdCoreTopologyV3Protocol.h>
#include <Protocol/FabricNumaServices2.h>
#include <Protocol/AmdAcpiSratServicesProtocol.h>
#include <Protocol/AmdAcpiMsctServicesProtocol.h>
#include "FabricAcpiDomainInfo.h"
#include "Filecode.h"

/*----------------------------------------------------------------------------------------
 *                   D E F I N I T I O N S    A N D    M A C R O S
 *----------------------------------------------------------------------------------------
 */
#define FILECODE FABRIC_BRH_FABRICBRHDXE_FABRICACPIMSCT_FILECODE

#define MAX_PROXIMITY_DOMAINS MAX_REPORTED_DOMAINS

/*----------------------------------------------------------------------------------------
 *           P R O T O T Y P E S     O F     L O C A L     F U N C T I O N S
 *----------------------------------------------------------------------------------------
 */
EFI_STATUS
EFIAPI
FabricGetMsctInfo (
  IN     AMD_FABRIC_ACPI_MSCT_SERVICES_PROTOCOL  *This,
     OUT UINT32                                  *NumPopProxDomains,
     OUT UINT32                                  *MaxNumProxDomains,
     OUT MSCT_PROX_DOMAIN_INFO                   **MsctInfo
  );

/*----------------------------------------------------------------------------------------
 *                           G L O B A L   V A R I A B L E S
 *----------------------------------------------------------------------------------------
 */
STATIC AMD_FABRIC_ACPI_MSCT_SERVICES_PROTOCOL mFabricAcpiMsctServicesProtocol = {
  0x1,
  FabricGetMsctInfo
};

MSCT_PROX_DOMAIN_INFO mMsctDomains[MAX_PROXIMITY_DOMAINS];

/*----------------------------------------------------------------------------------------
 *                  T Y P E D E F S     A N D     S T R U C T U R E S
 *----------------------------------------------------------------------------------------
 */

/*----------------------------------------------------------------------------------------
 *                          E X P O R T E D    F U N C T I O N S
 *----------------------------------------------------------------------------------------
 */

/*----------------------------------------------------------------------------------------*/
/**
 * @brief This function will install the MSCT services protocol.
 *
 * @param[in] ImageHandle Image handle.
 * @param[in] SystemTable EFI system table.
 *
 * @return EFI_STATUS
 *
 * @retval EFI_SUCCESS - Services protocol installed.
 */
EFI_STATUS
EFIAPI
FabricBrhAcpiMsctProtocolInstall (
  IN EFI_HANDLE        ImageHandle,
  IN EFI_SYSTEM_TABLE  *SystemTable
  )
{
  return gBS->InstallProtocolInterface (&ImageHandle,
                                        &gAmdFabricAcpiMsctServicesProtocolGuid,
                                        EFI_NATIVE_INTERFACE,
                                        &mFabricAcpiMsctServicesProtocol);
}

/**
 * @brief This function will return the information necessary to generate MSCT.
 *
 * @param[in]  This              Pointer to AMD_FABRIC_ACPI_MSCT_SERVICES_PROTOCOL instance.
 * @param[out] NumPopProxDomains Number of populated proximity domains.
 * @param[out] MaxNumProxDomains Maximum number of proximity domains on the system.
 * @param[out] MsctInfo          Structure that describes MSCT proximity domains.
 *
 * @return EFI_STATUS
 *
 * @retval EFI_ABORTED - Services protocol not available.
 * @retval EFI_SUCCESS - MSCT data successfully gathered.
 */
EFI_STATUS
EFIAPI
FabricGetMsctInfo (
  IN     AMD_FABRIC_ACPI_MSCT_SERVICES_PROTOCOL  *This,
     OUT UINT32                                  *NumPopProxDomains,
     OUT UINT32                                  *MaxNumProxDomains,
     OUT MSCT_PROX_DOMAIN_INFO                   **MsctInfo
  )
{
  UINT8                                  MaxProxDomains;
  UINTN                                  Socket;
  UINTN                                  Die;
  UINTN                                  Ccd;
  UINTN                                  Ccx;
  UINTN                                  Core;
  UINTN                                  NumberOfCores;
  UINTN                                  NumberOfThreads;
  UINT32                                 i;
  UINT32                                 j;
  UINT32                                 CurrDomain;
  UINT32                                 NumberOfDomainsInSystem;
  UINT32                                 MemoryInfoCtr;
  DOMAIN_TYPE2                           CurrDomainType;
  DOMAIN_INFO2                           *DomainInfo;
  MEMORY_INFO                            *MemoryInfo;
  FABRIC_NUMA_SERVICES2_PROTOCOL         *FabricNumaServices;
  AMD_FABRIC_ACPI_SRAT_SERVICES_PROTOCOL *FabricSratServices;
  AMD_CORE_TOPOLOGY_SERVICES_V3_PROTOCOL *CoreTopology;
  CORE_TOPOLOGY_ITERATION_RESULT         IterationResult;

  if (gBS->LocateProtocol (&gAmdFabricNumaServices2ProtocolGuid, NULL, (VOID **) &FabricNumaServices) != EFI_SUCCESS) {
    return EFI_ABORTED;
  }

  if (gBS->LocateProtocol (&gAmdFabricAcpiSratServicesProtocolGuid, NULL, (VOID **) &FabricSratServices) != EFI_SUCCESS) {
    return EFI_ABORTED;
  }

  if (gBS->LocateProtocol (&gAmdCoreTopologyServicesV3ProtocolGuid, NULL, (VOID **) &CoreTopology) != EFI_SUCCESS) {
    return EFI_ABORTED;
  }

  if (FabricNumaServices->GetDomainInfo (FabricNumaServices, &NumberOfDomainsInSystem, &DomainInfo, NULL) != EFI_SUCCESS) {
    return EFI_ABORTED;
  }

  MaxProxDomains = FabricGetMaxDomains ();
  ASSERT (MaxProxDomains <= MAX_PROXIMITY_DOMAINS);

  // Initialize mMsctDomains
  LibAmdMemFill ((VOID *) mMsctDomains, 0x00, sizeof (mMsctDomains), NULL);

  for (i = 0; i < NumberOfDomainsInSystem ; i++) {
    CurrDomainType = DomainInfo[i].Type;
    ASSERT (CurrDomainType < MaxNumaDomainType2);

    mMsctDomains[i].ProxDomain = i;
  }

  CORE_TOPOLOGY_V3_FOR_EACH_COMPLEX (CoreTopology, IterationResult, Socket, Die, Ccd, Ccx) {
    if (FabricNumaServices->DomainXlat (FabricNumaServices, Socket, Die, Ccd, Ccx, &CurrDomain) == EFI_SUCCESS) {
      if (CoreTopology->GetCoreCountOnComplex (CoreTopology, Socket, Die, Ccd, Ccx, &NumberOfCores) == EFI_SUCCESS) {
        for (Core = 0; Core < NumberOfCores; Core++) {
          if (CoreTopology->GetThreadCountOnCore (CoreTopology, Socket, Die, Ccx, Ccx, Core, &NumberOfThreads) == EFI_SUCCESS) {
            mMsctDomains[CurrDomain].MaxProcCap += (UINT32) NumberOfThreads;
          }
        }
      }
    }
  }

  // Obtain memory affinity information for each domain
  if (FabricSratServices->GetMemoryInfo (FabricSratServices, &MemoryInfoCtr, &MemoryInfo) == EFI_SUCCESS) {
    // Update MaxMemCap field
    for (i = 0; i < MemoryInfoCtr; i++) {
      CurrDomain = MemoryInfo[i].Domain;

      for (j = 0; j < NumberOfDomainsInSystem; j++) {
        if (CurrDomain == mMsctDomains[j].ProxDomain) {
          mMsctDomains[j].MaxMemCap += MemoryInfo[i].RegionSize;
          break;
        }
      }
    }
  }

  *MsctInfo = &mMsctDomains[0];
  *NumPopProxDomains = NumberOfDomainsInSystem;
  *MaxNumProxDomains = MaxProxDomains;

  return EFI_SUCCESS;
}




