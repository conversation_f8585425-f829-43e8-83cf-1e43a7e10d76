/*****************************************************************************
 * Copyright (C) 2008-2024 Advanced Micro Devices, Inc. All rights reserved.
 *
*****************************************************************************
*/
/* $NoKeywords:$ */
/**
 * @file
 *
 * Config Fch Sata controller
 *
 * Init Sata Controller features (PEI phase).
 *
 * @xrefitem bom "File Content Label" "Release Content"
 * @e project:     AGESA
 * @e sub-project: FCH
 * @e \$Revision: 309083 $   @e \$Date: 2014-12-09 09:28:24 -0800 (Tue, 09 Dec 2014) $
 *
 */
#include "FchPlatform.h"
#include "Filecode.h"
#define FILECODE FCH_KUNLUN_FCHKUNLUNCORE_KUNLUN_KLSATA_KLSATARESETSERVICE_FILECODE

/**
 * FchInitResetSataProgram - Config Sata controller during
 * Power-On
 *
 *
 * @param[in] DieBusNum  IOCH bus number on current Die.
 * @param[in] FchDataPtr Fch configuration structure pointer.
 *
 */
VOID
FchInitResetSataProgram (
  IN  UINT32   DieBusNum,
  IN  VOID     *FchDataPtr
  )
{
  UINT32                    SataController;
  FCH_RESET_DATA_BLOCK      *LocalCfgPtr;

  LocalCfgPtr = (FCH_RESET_DATA_BLOCK *) FchDataPtr;

  //FchKLSataInitHideUnconnectedSataPci (DieBusNum, FchDataPtr);

  //
  // Enable the SATA controller.
  //
  for (SataController = 0; SataController < KUNLUN_SATA_CONTROLLER_NUM; SataController++) {
    if (LocalCfgPtr->SataEnable[SataController]) {
      FchKLSataInitEnableSata (DieBusNum, SataController, FchDataPtr);
    }else{
      FchKLSataInitDisableSata (DieBusNum, SataController, FchDataPtr);
    }
  }

  // check if Sata0 and Sata1 are both disabled
  if ((!LocalCfgPtr->SataEnable[0]) && (!LocalCfgPtr->SataEnable[1])) {
    FchKLSataInitHideNbifDev1Pci (DieBusNum, 0, FchDataPtr);
  }
  // check if Sata2 and Sata3 are both disabled
  if ((!LocalCfgPtr->SataEnable[2]) && (!LocalCfgPtr->SataEnable[3])) {
    FchKLSataInitHideNbifDev1Pci (DieBusNum, 3, FchDataPtr);
  }
}





