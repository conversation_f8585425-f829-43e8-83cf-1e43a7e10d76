/*****************************************************************************
 *
 * Copyright (C) 2008-2024 Advanced Micro Devices, Inc. All rights reserved.
 *
 *******************************************************************************
 */

#ifndef _FCH_ESPI_CMD_DXE_H_
#define _FCH_ESPI_CMD_DXE_H_

#include <Library/DebugLib.h>
#include <Library/IoLib.h>
#include <Library/IdsLib.h>
#include <Library/PcdLib.h>
#include <Library/UefiLib.h>
#include <Library/UefiDriverEntryPoint.h>
#include <Library/FchBaseLib.h>
#include <Library/FchEspiCmdLib.h>
#include <Protocol/FchEspiCmdProtocol.h>

VOID
EFIAPI
DxeInBandRst (
  IN CONST EFI_ESPI_CMD_PROTOCOL  *This,
  IN  UINT32                      EspiBase
  );

UINT32
EFIAPI
DxeGetConfiguration (
  IN CONST EFI_ESPI_CMD_PROTOCOL  *This,
  IN  UINT32                      EspiBase,
  IN  UINT32                      RegAddr
 );

VOID
EFIAPI
DxeSetConfiguration (
  IN CONST EFI_ESPI_CMD_PROTOCOL  *This,
  IN  UINT32                      EspiBase,
  IN  UINT32                      RegAddr,
  IN  UINT32                      Value
);

EFI_STATUS
EFIAPI
DxeSafsFlashRead (
  IN CONST EFI_ESPI_CMD_PROTOCOL  *This,
  IN  UINT32                      EspiBase,
  IN  UINT32                      Address,
  IN  UINT32                      Length,
  OUT UINT8                       *Buffer
  );

EFI_STATUS
EFIAPI
DxeSafsFlashWrite (
  IN CONST EFI_ESPI_CMD_PROTOCOL  *This,
  IN  UINT32                      EspiBase,
  IN  UINT32                      Address,
  IN  UINT32                      Length,
  IN  UINT8                       *Value
  );

EFI_STATUS
EFIAPI
DxeSafsFlashErase (
  IN CONST EFI_ESPI_CMD_PROTOCOL  *This,
  IN  UINT32                      EspiBase,
  IN  UINT32                      Address,
  IN  UINT32                      Length
  );

EFI_STATUS
EFIAPI
DxeSafsRpmcOp1 (
  IN CONST EFI_ESPI_CMD_PROTOCOL  *This,
  IN  UINT32                      EspiBase,
  IN  UINT8                       RpmcFlashDev,
  IN  UINT32                      Length,
  IN  UINT8                       *Data
  );

EFI_STATUS
EFIAPI
DxeSafsRpmcOp2 (
  IN CONST EFI_ESPI_CMD_PROTOCOL  *This,
  IN  UINT32                      EspiBase,
  IN  UINT8                       RpmcFlashDev,
  IN  UINT32                      Length,
  IN  UINT8                       *Buffer
  );

EFI_STATUS
EFIAPI
AmdFchEspiCmdDxeInit (
  IN  EFI_HANDLE        ImageHandle,
  IN  EFI_SYSTEM_TABLE  *SystemTable
  );

#endif // _FCH_ESPI_CMD_DXE_H_

