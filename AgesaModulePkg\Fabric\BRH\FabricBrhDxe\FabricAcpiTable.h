/*****************************************************************************
 *
 * Copyright (C) 2019-2024 Advanced Micro Devices, Inc. All rights reserved.
 *
 *****************************************************************************
 */

#ifndef _FABRIC_ACPI_TABLE_H_
#define _FABRIC_ACPI_TABLE_H_

#include <Protocol/FabricNumaServices2.h>

#pragma pack (push, 1)

EFI_STATUS
EFIAPI
FabricBrhNumaServices2ProtocolInstall (
  IN EFI_HANDLE        ImageHandle,
  IN EFI_SYSTEM_TABLE  *SystemTable
  );

EFI_STATUS
EFIAPI
FabricBrhAcpiSlitProtocolInstall (
  IN EFI_HANDLE        ImageHandle,
  IN EFI_SYSTEM_TABLE  *SystemTable
  );

EFI_STATUS
EFIAPI
FabricBrhAcpiSratProtocolInstall (
  IN EFI_HANDLE        ImageHandle,
  IN EFI_SYSTEM_TABLE  *SystemTable
  );

EFI_STATUS
EFIAPI
FabricBrhAcpiCditProtocolInstall (
  IN EFI_HANDLE        ImageHandle,
  IN EFI_SYSTEM_TABLE  *SystemTable
  );

EFI_STATUS
EFIAPI
FabricBrhAcpiCratProtocolInstall (
  IN EFI_HANDLE        ImageHandle,
  IN EFI_SYSTEM_TABLE  *SystemTable
  );

EFI_STATUS
EFIAPI
FabricGetDistanceInfo (
  IN OUT UINT8         *Distance,
  IN     UINT32        DomainCount,
  IN     DOMAIN_INFO2  *DomainInfo
  );

EFI_STATUS
EFIAPI
FabricBrhAcpiMsctProtocolInstall (
  IN EFI_HANDLE        ImageHandle,
  IN EFI_SYSTEM_TABLE  *SystemTable
  );

VOID
EFIAPI
FabricBrhAcpiHmatInstall (
  IN       EFI_EVENT         Event,
  IN       VOID              *Context
  );

#pragma pack (pop)
#endif // _FABRIC_ACPI_TABLE_H_

