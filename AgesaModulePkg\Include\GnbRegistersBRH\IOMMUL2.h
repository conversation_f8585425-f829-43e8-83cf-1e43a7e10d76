/*****************************************************************************
 *
 * Copyright (C) 2008-2024 Advanced Micro Devices, Inc. All rights reserved.
 *
 *******************************************************************************
 **/

#ifndef _IOMMUL2_H_
#define _IOMMUL2_H_


/***********************************************************
* Register Name : IOMMU_ADAPTER_ID
************************************************************/

#define IOMMU_ADAPTER_ID_SUBSYSTEM_VENDOR_ID_OFFSET            0
#define IOMMU_ADAPTER_ID_SUBSYSTEM_VENDOR_ID_MASK              0xffff

#define IOMMU_ADAPTER_ID_SUBSYSTEM_ID_OFFSET                   16
#define IOMMU_ADAPTER_ID_SUBSYSTEM_ID_MASK                     0xffff0000

typedef union {
  struct {
    UINT32                                 SUBSYSTEM_VENDOR_ID:16;
    UINT32                                        SUBSYSTEM_ID:16;
  } Field;
  UINT32 Value;
} IOMMU_ADAPTER_ID_STRUCT;

#define PCICFG_IOMMUL2_IOMMU_ADAPTER_ID_OFFSET                      0x2c

#define SMN_IOMMU_ADAPTER_ID_ADDRESS                                  0x13f0002cUL
#define SMN_IOMMU0NBIO0_IOMMU_ADAPTER_ID_ADDRESS                      0x13f0002cUL
#define SMN_IOMMU0NBIO1_IOMMU_ADAPTER_ID_ADDRESS                      0x1410002cUL
#define SMN_IOMMU1NBIO0_IOMMU_ADAPTER_ID_ADDRESS                      0x1400002cUL
#define SMN_IOMMU1NBIO1_IOMMU_ADAPTER_ID_ADDRESS                      0x1420002cUL


/***********************************************************
* Register Name : IOMMU_ADAPTER_ID_W
************************************************************/

#define IOMMU_ADAPTER_ID_W_SUBSYSTEM_VENDOR_ID_W_OFFSET        0
#define IOMMU_ADAPTER_ID_W_SUBSYSTEM_VENDOR_ID_W_MASK          0xffff

#define IOMMU_ADAPTER_ID_W_SUBSYSTEM_ID_W_OFFSET               16
#define IOMMU_ADAPTER_ID_W_SUBSYSTEM_ID_W_MASK                 0xffff0000

typedef union {
  struct {
    UINT32                               SUBSYSTEM_VENDOR_ID_W:16;
    UINT32                                      SUBSYSTEM_ID_W:16;
  } Field;
  UINT32 Value;
} IOMMU_ADAPTER_ID_W_STRUCT;

#define PCICFG_IOMMUL2_IOMMU_ADAPTER_ID_W_OFFSET                    0x78

#define SMN_IOMMU_ADAPTER_ID_W_ADDRESS                                0x13f00078UL
#define SMN_IOMMU0NBIO0_IOMMU_ADAPTER_ID_W_ADDRESS                    0x13f00078UL
#define SMN_IOMMU0NBIO1_IOMMU_ADAPTER_ID_W_ADDRESS                    0x14100078UL
#define SMN_IOMMU1NBIO0_IOMMU_ADAPTER_ID_W_ADDRESS                    0x14000078UL
#define SMN_IOMMU1NBIO1_IOMMU_ADAPTER_ID_W_ADDRESS                    0x14200078UL


/***********************************************************
* Register Name : IOMMU_BASE_CODE
************************************************************/

#define IOMMU_BASE_CODE_BASE_CLASS_CODE_OFFSET                 0
#define IOMMU_BASE_CODE_BASE_CLASS_CODE_MASK                   0xff

typedef union {
  struct {
    UINT8                                     BASE_CLASS_CODE:8;
  } Field;
  UINT8 Value;
} IOMMU_BASE_CODE_STRUCT;

#define PCICFG_IOMMUL2_IOMMU_BASE_CODE_OFFSET                       0xb

#define SMN_IOMMU_BASE_CODE_ADDRESS                                   0x13f0000bUL
#define SMN_IOMMU0NBIO0_IOMMU_BASE_CODE_ADDRESS                       0x13f0000bUL
#define SMN_IOMMU0NBIO1_IOMMU_BASE_CODE_ADDRESS                       0x1410000bUL
#define SMN_IOMMU1NBIO0_IOMMU_BASE_CODE_ADDRESS                       0x1400000bUL
#define SMN_IOMMU1NBIO1_IOMMU_BASE_CODE_ADDRESS                       0x1420000bUL


/***********************************************************
* Register Name : IOMMU_CAPABILITIES_PTR
************************************************************/

#define IOMMU_CAPABILITIES_PTR_CAP_PTR_OFFSET                  0
#define IOMMU_CAPABILITIES_PTR_CAP_PTR_MASK                    0xff

#define IOMMU_CAPABILITIES_PTR_Reserved_31_8_OFFSET            8
#define IOMMU_CAPABILITIES_PTR_Reserved_31_8_MASK              0xffffff00

typedef union {
  struct {
    UINT32                                             CAP_PTR:8;
    UINT32                                       Reserved_31_8:24;
  } Field;
  UINT32 Value;
} IOMMU_CAPABILITIES_PTR_STRUCT;

#define PCICFG_IOMMUL2_IOMMU_CAPABILITIES_PTR_OFFSET                0x34

#define SMN_IOMMU_CAPABILITIES_PTR_ADDRESS                            0x13f00034UL
#define SMN_IOMMU0NBIO0_IOMMU_CAPABILITIES_PTR_ADDRESS                0x13f00034UL
#define SMN_IOMMU0NBIO1_IOMMU_CAPABILITIES_PTR_ADDRESS                0x14100034UL
#define SMN_IOMMU1NBIO0_IOMMU_CAPABILITIES_PTR_ADDRESS                0x14000034UL
#define SMN_IOMMU1NBIO1_IOMMU_CAPABILITIES_PTR_ADDRESS                0x14200034UL


/***********************************************************
* Register Name : IOMMU_CAP_BASE_HI
************************************************************/

#define IOMMU_CAP_BASE_HI_IOMMU_BASE_ADDR_HI_OFFSET            0
#define IOMMU_CAP_BASE_HI_IOMMU_BASE_ADDR_HI_MASK              0xffffffff

typedef union {
  struct {
    UINT32                                  IOMMU_BASE_ADDR_HI:32;
  } Field;
  UINT32 Value;
} IOMMU_CAP_BASE_HI_STRUCT;

#define PCICFG_IOMMUL2_IOMMU_CAP_BASE_HI_OFFSET                     0x48

#define SMN_IOMMU_CAP_BASE_HI_ADDRESS                                 0x13f00048UL
#define SMN_IOMMU0NBIO0_IOMMU_CAP_BASE_HI_ADDRESS                     0x13f00048UL
#define SMN_IOMMU0NBIO1_IOMMU_CAP_BASE_HI_ADDRESS                     0x14100048UL
#define SMN_IOMMU1NBIO0_IOMMU_CAP_BASE_HI_ADDRESS                     0x14000048UL
#define SMN_IOMMU1NBIO1_IOMMU_CAP_BASE_HI_ADDRESS                     0x14200048UL


/***********************************************************
* Register Name : IOMMU_CAP_BASE_LO
************************************************************/

#define IOMMU_CAP_BASE_LO_IOMMU_ENABLE_OFFSET                  0
#define IOMMU_CAP_BASE_LO_IOMMU_ENABLE_MASK                    0x1

#define IOMMU_CAP_BASE_LO_Reserved_18_1_OFFSET                 1
#define IOMMU_CAP_BASE_LO_Reserved_18_1_MASK                   0x7fffe

#define IOMMU_CAP_BASE_LO_IOMMU_BASE_ADDR_LO_OFFSET            19
#define IOMMU_CAP_BASE_LO_IOMMU_BASE_ADDR_LO_MASK              0xfff80000

typedef union {
  struct {
    UINT32                                        IOMMU_ENABLE:1;
    UINT32                                       Reserved_18_1:18;
    UINT32                                  IOMMU_BASE_ADDR_LO:13;
  } Field;
  UINT32 Value;
} IOMMU_CAP_BASE_LO_STRUCT;

#define PCICFG_IOMMUL2_IOMMU_CAP_BASE_LO_OFFSET                     0x44

#define SMN_IOMMU_CAP_BASE_LO_ADDRESS                                 0x13f00044UL
#define SMN_IOMMU0NBIO0_IOMMU_CAP_BASE_LO_ADDRESS                     0x13f00044UL
#define SMN_IOMMU0NBIO1_IOMMU_CAP_BASE_LO_ADDRESS                     0x14100044UL
#define SMN_IOMMU1NBIO0_IOMMU_CAP_BASE_LO_ADDRESS                     0x14000044UL
#define SMN_IOMMU1NBIO1_IOMMU_CAP_BASE_LO_ADDRESS                     0x14200044UL


/***********************************************************
* Register Name : IOMMU_CAP_HEADER
************************************************************/

#define IOMMU_CAP_HEADER_IOMMU_CAP_ID_OFFSET                   0
#define IOMMU_CAP_HEADER_IOMMU_CAP_ID_MASK                     0xff

#define IOMMU_CAP_HEADER_IOMMU_CAP_PTR_OFFSET                  8
#define IOMMU_CAP_HEADER_IOMMU_CAP_PTR_MASK                    0xff00

#define IOMMU_CAP_HEADER_IOMMU_CAP_TYPE_OFFSET                 16
#define IOMMU_CAP_HEADER_IOMMU_CAP_TYPE_MASK                   0x70000

#define IOMMU_CAP_HEADER_IOMMU_CAP_REV_OFFSET                  19
#define IOMMU_CAP_HEADER_IOMMU_CAP_REV_MASK                    0xf80000

#define IOMMU_CAP_HEADER_IOMMU_IO_TLBSUP_OFFSET                24
#define IOMMU_CAP_HEADER_IOMMU_IO_TLBSUP_MASK                  0x1000000

#define IOMMU_CAP_HEADER_IOMMU_HT_TUNNEL_SUP_OFFSET            25
#define IOMMU_CAP_HEADER_IOMMU_HT_TUNNEL_SUP_MASK              0x2000000

#define IOMMU_CAP_HEADER_IOMMU_NP_CACHE_OFFSET                 26
#define IOMMU_CAP_HEADER_IOMMU_NP_CACHE_MASK                   0x4000000

#define IOMMU_CAP_HEADER_IOMMU_EFR_SUP_OFFSET                  27
#define IOMMU_CAP_HEADER_IOMMU_EFR_SUP_MASK                    0x8000000

#define IOMMU_CAP_HEADER_IOMMU_CAP_EXT_OFFSET                  28
#define IOMMU_CAP_HEADER_IOMMU_CAP_EXT_MASK                    0x10000000

#define IOMMU_CAP_HEADER_Reserved_31_29_OFFSET                 29
#define IOMMU_CAP_HEADER_Reserved_31_29_MASK                   0xe0000000

typedef union {
  struct {
    UINT32                            IOMMU_CAP_ID_BITS:8;
    UINT32                                       IOMMU_CAP_PTR:8;
    UINT32                                      IOMMU_CAP_TYPE:3;
    UINT32                                       IOMMU_CAP_REV:5;
    UINT32                                     IOMMU_IO_TLBSUP:1;
    UINT32                                 IOMMU_HT_TUNNEL_SUP:1;
    UINT32                                      IOMMU_NP_CACHE:1;
    UINT32                                       IOMMU_EFR_SUP:1;
    UINT32                                       IOMMU_CAP_EXT:1;
    UINT32                                      Reserved_31_29:3;
  } Field;
  UINT32 Value;
} IOMMU_CAP_HEADER_STRUCT;

#define PCICFG_IOMMUL2_IOMMU_CAP_HEADER_OFFSET                      0x40

#define SMN_IOMMU_CAP_HEADER_ADDRESS                                  0x13f00040UL
#define SMN_IOMMU0NBIO0_IOMMU_CAP_HEADER_ADDRESS                      0x13f00040UL
#define SMN_IOMMU0NBIO1_IOMMU_CAP_HEADER_ADDRESS                      0x14100040UL
#define SMN_IOMMU1NBIO0_IOMMU_CAP_HEADER_ADDRESS                      0x14000040UL
#define SMN_IOMMU1NBIO1_IOMMU_CAP_HEADER_ADDRESS                      0x14200040UL


/***********************************************************
* Register Name : IOMMU_CAP_MISC
************************************************************/

#define IOMMU_CAP_MISC_IOMMU_MSI_NUM_OFFSET                    0
#define IOMMU_CAP_MISC_IOMMU_MSI_NUM_MASK                      0x1f

#define IOMMU_CAP_MISC_IOMMU_GVA_SIZE_OFFSET                   5
#define IOMMU_CAP_MISC_IOMMU_GVA_SIZE_MASK                     0xe0

#define IOMMU_CAP_MISC_IOMMU_PA_SIZE_OFFSET                    8
#define IOMMU_CAP_MISC_IOMMU_PA_SIZE_MASK                      0x7f00

#define IOMMU_CAP_MISC_IOMMU_VA_SIZE_OFFSET                    15
#define IOMMU_CAP_MISC_IOMMU_VA_SIZE_MASK                      0x3f8000

#define IOMMU_CAP_MISC_IOMMU_HT_ATS_RESV_OFFSET                22
#define IOMMU_CAP_MISC_IOMMU_HT_ATS_RESV_MASK                  0x400000

#define IOMMU_CAP_MISC_Reserved_26_23_OFFSET                   23
#define IOMMU_CAP_MISC_Reserved_26_23_MASK                     0x7800000

#define IOMMU_CAP_MISC_IOMMU_MSI_NUM_PPR_OFFSET                27
#define IOMMU_CAP_MISC_IOMMU_MSI_NUM_PPR_MASK                  0xf8000000

typedef union {
  struct {
    UINT32                                       IOMMU_MSI_NUM:5;
    UINT32                                      IOMMU_GVA_SIZE:3;
    UINT32                                       IOMMU_PA_SIZE:7;
    UINT32                                       IOMMU_VA_SIZE:7;
    UINT32                                   IOMMU_HT_ATS_RESV:1;
    UINT32                                      Reserved_26_23:4;
    UINT32                                   IOMMU_MSI_NUM_PPR:5;
  } Field;
  UINT32 Value;
} IOMMU_CAP_MISC_STRUCT;

#define PCICFG_IOMMUL2_IOMMU_CAP_MISC_OFFSET                        0x50

#define SMN_IOMMU_CAP_MISC_ADDRESS                                    0x13f00050UL
#define SMN_IOMMU0NBIO0_IOMMU_CAP_MISC_ADDRESS                        0x13f00050UL
#define SMN_IOMMU0NBIO1_IOMMU_CAP_MISC_ADDRESS                        0x14100050UL
#define SMN_IOMMU1NBIO0_IOMMU_CAP_MISC_ADDRESS                        0x14000050UL
#define SMN_IOMMU1NBIO1_IOMMU_CAP_MISC_ADDRESS                        0x14200050UL


/***********************************************************
* Register Name : IOMMU_CAP_MISC_1
************************************************************/

#define IOMMU_CAP_MISC_1_IOMMU_MSI_NUM_GA_OFFSET               0
#define IOMMU_CAP_MISC_1_IOMMU_MSI_NUM_GA_MASK                 0x1f

#define IOMMU_CAP_MISC_1_IOMMU_ARCH_MODE_OFFSET                5
#define IOMMU_CAP_MISC_1_IOMMU_ARCH_MODE_MASK                  0x20

#define IOMMU_CAP_MISC_1_DVM_MODE_OFFSET                       6
#define IOMMU_CAP_MISC_1_DVM_MODE_MASK                         0xc0

#define IOMMU_CAP_MISC_1_STU_SIZE_SEL_OFFSET                   8
#define IOMMU_CAP_MISC_1_STU_SIZE_SEL_MASK                     0x100

#define IOMMU_CAP_MISC_1_GST_PAGE_TABLE_FMT_SEL_OFFSET         9
#define IOMMU_CAP_MISC_1_GST_PAGE_TABLE_FMT_SEL_MASK           0x200

#define IOMMU_CAP_MISC_1_Reserved_14_10_OFFSET                 10
#define IOMMU_CAP_MISC_1_Reserved_14_10_MASK                   0x7c00

#define IOMMU_CAP_MISC_1_SMMUMMIO_EN_OFFSET                    15
#define IOMMU_CAP_MISC_1_SMMUMMIO_EN_MASK                      0x8000

#define IOMMU_CAP_MISC_1_Reserved_30_16_OFFSET                 16
#define IOMMU_CAP_MISC_1_Reserved_30_16_MASK                   0x7fff0000

#define IOMMU_CAP_MISC_1_SMMUMMIO_LOCK_OFFSET                  31
#define IOMMU_CAP_MISC_1_SMMUMMIO_LOCK_MASK                    0x80000000

typedef union {
  struct {
    UINT32                                    IOMMU_MSI_NUM_GA:5;
    UINT32                                     IOMMU_ARCH_MODE:1;
    UINT32                                            DVM_MODE:2;
    UINT32                                        STU_SIZE_SEL:1;
    UINT32                              GST_PAGE_TABLE_FMT_SEL:1;
    UINT32                                      Reserved_14_10:5;
    UINT32                                         SMMUMMIO_EN:1;
    UINT32                                      Reserved_30_16:15;
    UINT32                                       SMMUMMIO_LOCK:1;
  } Field;
  UINT32 Value;
} IOMMU_CAP_MISC_1_STRUCT;

#define PCICFG_IOMMUL2_IOMMU_CAP_MISC_1_OFFSET                      0x54

#define SMN_IOMMU_CAP_MISC_1_ADDRESS                                  0x13f00054UL
#define SMN_IOMMU0NBIO0_IOMMU_CAP_MISC_1_ADDRESS                      0x13f00054UL
#define SMN_IOMMU0NBIO1_IOMMU_CAP_MISC_1_ADDRESS                      0x14100054UL
#define SMN_IOMMU1NBIO0_IOMMU_CAP_MISC_1_ADDRESS                      0x14000054UL
#define SMN_IOMMU1NBIO1_IOMMU_CAP_MISC_1_ADDRESS                      0x14200054UL


/***********************************************************
* Register Name : IOMMU_CAP_RANGE
************************************************************/

#define IOMMU_CAP_RANGE_IOMMU_UNIT_ID_OFFSET                   0
#define IOMMU_CAP_RANGE_IOMMU_UNIT_ID_MASK                     0x1f

#define IOMMU_CAP_RANGE_Reserved_6_5_OFFSET                    5
#define IOMMU_CAP_RANGE_Reserved_6_5_MASK                      0x60

#define IOMMU_CAP_RANGE_IOMMU_RNG_VALID_OFFSET                 7
#define IOMMU_CAP_RANGE_IOMMU_RNG_VALID_MASK                   0x80

#define IOMMU_CAP_RANGE_IOMMU_BUS_NUMBER_OFFSET                8
#define IOMMU_CAP_RANGE_IOMMU_BUS_NUMBER_MASK                  0xff00

#define IOMMU_CAP_RANGE_IOMMU_FIRST_DEVICE_OFFSET              16
#define IOMMU_CAP_RANGE_IOMMU_FIRST_DEVICE_MASK                0xff0000

#define IOMMU_CAP_RANGE_IOMMU_LAST_DEVICE_OFFSET               24
#define IOMMU_CAP_RANGE_IOMMU_LAST_DEVICE_MASK                 0xff000000

typedef union {
  struct {
    UINT32                                       IOMMU_UNIT_ID:5;
    UINT32                                        Reserved_6_5:2;
    UINT32                                     IOMMU_RNG_VALID:1;
    UINT32                                    IOMMU_BUS_NUMBER:8;
    UINT32                                  IOMMU_FIRST_DEVICE:8;
    UINT32                                   IOMMU_LAST_DEVICE:8;
  } Field;
  UINT32 Value;
} IOMMU_CAP_RANGE_STRUCT;

#define PCICFG_IOMMUL2_IOMMU_CAP_RANGE_OFFSET                       0x4c

#define SMN_IOMMU_CAP_RANGE_ADDRESS                                   0x13f0004cUL
#define SMN_IOMMU0NBIO0_IOMMU_CAP_RANGE_ADDRESS                       0x13f0004cUL
#define SMN_IOMMU0NBIO1_IOMMU_CAP_RANGE_ADDRESS                       0x1410004cUL
#define SMN_IOMMU1NBIO0_IOMMU_CAP_RANGE_ADDRESS                       0x1400004cUL
#define SMN_IOMMU1NBIO1_IOMMU_CAP_RANGE_ADDRESS                       0x1420004cUL


/***********************************************************
* Register Name : IOMMU_COMMAND
************************************************************/

#define IOMMU_COMMAND_IO_ACCESS_EN_OFFSET                      0
#define IOMMU_COMMAND_IO_ACCESS_EN_MASK                        0x1

#define IOMMU_COMMAND_MEM_ACCESS_EN_OFFSET                     1
#define IOMMU_COMMAND_MEM_ACCESS_EN_MASK                       0x2

#define IOMMU_COMMAND_BUS_MASTER_EN_OFFSET                     2
#define IOMMU_COMMAND_BUS_MASTER_EN_MASK                       0x4

#define IOMMU_COMMAND_Reserved_5_3_OFFSET                      3
#define IOMMU_COMMAND_Reserved_5_3_MASK                        0x38

#define IOMMU_COMMAND_PARITY_ERROR_EN_OFFSET                   6
#define IOMMU_COMMAND_PARITY_ERROR_EN_MASK                     0x40

#define IOMMU_COMMAND_Reserved_7_7_OFFSET                      7
#define IOMMU_COMMAND_Reserved_7_7_MASK                        0x80

#define IOMMU_COMMAND_SERR_EN_OFFSET                           8
#define IOMMU_COMMAND_SERR_EN_MASK                             0x100

#define IOMMU_COMMAND_Reserved_9_9_OFFSET                      9
#define IOMMU_COMMAND_Reserved_9_9_MASK                        0x200

#define IOMMU_COMMAND_INTERRUPT_DIS_OFFSET                     10
#define IOMMU_COMMAND_INTERRUPT_DIS_MASK                       0x400

#define IOMMU_COMMAND_Reserved_15_11_OFFSET                    11
#define IOMMU_COMMAND_Reserved_15_11_MASK                      0xf800

typedef union {
  struct {
    UINT16                                        IO_ACCESS_EN:1;
    UINT16                                       MEM_ACCESS_EN:1;
    UINT16                                       BUS_MASTER_EN:1;
    UINT16                                        Reserved_5_3:3;
    UINT16                                     PARITY_ERROR_EN:1;
    UINT16                                        Reserved_7_7:1;
    UINT16                                             SERR_EN:1;
    UINT16                                        Reserved_9_9:1;
    UINT16                                       INTERRUPT_DIS:1;
    UINT16                                      Reserved_15_11:5;
  } Field;
  UINT16 Value;
} IOMMU_COMMAND_STRUCT;

#define PCICFG_IOMMUL2_IOMMU_COMMAND_OFFSET                         0x4

#define SMN_IOMMU_COMMAND_ADDRESS                                     0x13f00004UL
#define SMN_IOMMU0NBIO0_IOMMU_COMMAND_ADDRESS                         0x13f00004UL
#define SMN_IOMMU0NBIO1_IOMMU_COMMAND_ADDRESS                         0x14100004UL
#define SMN_IOMMU1NBIO0_IOMMU_COMMAND_ADDRESS                         0x14000004UL
#define SMN_IOMMU1NBIO1_IOMMU_COMMAND_ADDRESS                         0x14200004UL


/***********************************************************
* Register Name : IOMMU_CONTROL_W
************************************************************/

#define IOMMU_CONTROL_W_INTERRUPT_PIN_W_OFFSET                 0
#define IOMMU_CONTROL_W_INTERRUPT_PIN_W_MASK                   0x7

#define IOMMU_CONTROL_W_Reserved_3_3_OFFSET                    3
#define IOMMU_CONTROL_W_Reserved_3_3_MASK                      0x8

#define IOMMU_CONTROL_W_MINOR_REV_ID_W_OFFSET                  4
#define IOMMU_CONTROL_W_MINOR_REV_ID_W_MASK                    0xf0

#define IOMMU_CONTROL_W_IO_TLBSUP_W_OFFSET                     8
#define IOMMU_CONTROL_W_IO_TLBSUP_W_MASK                       0x100

#define IOMMU_CONTROL_W_EFR_SUP_W_OFFSET                       9
#define IOMMU_CONTROL_W_EFR_SUP_W_MASK                         0x200

#define IOMMU_CONTROL_W_MSI_MULT_MESS_CAP_W_OFFSET             10
#define IOMMU_CONTROL_W_MSI_MULT_MESS_CAP_W_MASK               0x1c00

#define IOMMU_CONTROL_W_IOMMU_CAP_EXT_W_OFFSET                 13
#define IOMMU_CONTROL_W_IOMMU_CAP_EXT_W_MASK                   0x2000

#define IOMMU_CONTROL_W_IOMMU_GVA_SIZE_W_OFFSET                14
#define IOMMU_CONTROL_W_IOMMU_GVA_SIZE_W_MASK                  0x1c000

#define IOMMU_CONTROL_W_IOMMU_PA_SIZE_W_OFFSET                 17
#define IOMMU_CONTROL_W_IOMMU_PA_SIZE_W_MASK                   0xfe0000

#define IOMMU_CONTROL_W_Reserved_31_24_OFFSET                  24
#define IOMMU_CONTROL_W_Reserved_31_24_MASK                    0xff000000

typedef union {
  struct {
    UINT32                                     INTERRUPT_PIN_W:3;
    UINT32                                        Reserved_3_3:1;
    UINT32                                      MINOR_REV_ID_W:4;
    UINT32                                         IO_TLBSUP_W:1;
    UINT32                                           EFR_SUP_W:1;
    UINT32                                 MSI_MULT_MESS_CAP_W:3;
    UINT32                                     IOMMU_CAP_EXT_W:1;
    UINT32                                    IOMMU_GVA_SIZE_W:3;
    UINT32                                     IOMMU_PA_SIZE_W:7;
    UINT32                                      Reserved_31_24:8;
  } Field;
  UINT32 Value;
} IOMMU_CONTROL_W_STRUCT;

#define PCICFG_IOMMUL2_IOMMU_CONTROL_W_OFFSET                       0x7c

#define SMN_IOMMU_CONTROL_W_ADDRESS                                   0x13f0007cUL
#define SMN_IOMMU0NBIO0_IOMMU_CONTROL_W_ADDRESS                       0x13f0007cUL
#define SMN_IOMMU0NBIO1_IOMMU_CONTROL_W_ADDRESS                       0x1410007cUL
#define SMN_IOMMU1NBIO0_IOMMU_CONTROL_W_ADDRESS                       0x1400007cUL
#define SMN_IOMMU1NBIO1_IOMMU_CONTROL_W_ADDRESS                       0x1420007cUL


/***********************************************************
* Register Name : IOMMU_DEVICE_ID
************************************************************/

#define IOMMU_DEVICE_ID_DEVICE_ID_OFFSET                       0
#define IOMMU_DEVICE_ID_DEVICE_ID_MASK                         0xffff

typedef union {
  struct {
    UINT16                                           DEVICE_ID:16;
  } Field;
  UINT16 Value;
} IOMMU_DEVICE_ID_STRUCT;

#define PCICFG_IOMMUL2_IOMMU_DEVICE_ID_OFFSET                       0x2

#define SMN_IOMMU_DEVICE_ID_ADDRESS                                   0x13f00002UL
#define SMN_IOMMU0NBIO0_IOMMU_DEVICE_ID_ADDRESS                       0x13f00002UL
#define SMN_IOMMU0NBIO1_IOMMU_DEVICE_ID_ADDRESS                       0x14100002UL
#define SMN_IOMMU1NBIO0_IOMMU_DEVICE_ID_ADDRESS                       0x14000002UL
#define SMN_IOMMU1NBIO1_IOMMU_DEVICE_ID_ADDRESS                       0x14200002UL


/***********************************************************
* Register Name : IOMMU_DSFX_CONTROL
************************************************************/

#define IOMMU_DSFX_CONTROL_DSFXSup_OFFSET                      0
#define IOMMU_DSFX_CONTROL_DSFXSup_MASK                        0xffffff

#define IOMMU_DSFX_CONTROL_REVISION_MINOR_OFFSET               24
#define IOMMU_DSFX_CONTROL_REVISION_MINOR_MASK                 0xf000000

#define IOMMU_DSFX_CONTROL_REVISION_MAJOR_OFFSET               28
#define IOMMU_DSFX_CONTROL_REVISION_MAJOR_MASK                 0xf0000000

typedef union {
  struct {
    UINT32                                             DSFXSup:24;
    UINT32                                      REVISION_MINOR:4;
    UINT32                                      REVISION_MAJOR:4;
  } Field;
  UINT32 Value;
} IOMMU_DSFX_CONTROL_STRUCT;

#define PCICFG_IOMMUL2_IOMMU_DSFX_CONTROL_OFFSET                    0x8c

#define SMN_IOMMU_DSFX_CONTROL_ADDRESS                                0x13f0008cUL
#define SMN_IOMMU0NBIO0_IOMMU_DSFX_CONTROL_ADDRESS                    0x13f0008cUL
#define SMN_IOMMU0NBIO1_IOMMU_DSFX_CONTROL_ADDRESS                    0x1410008cUL
#define SMN_IOMMU1NBIO0_IOMMU_DSFX_CONTROL_ADDRESS                    0x1400008cUL
#define SMN_IOMMU1NBIO1_IOMMU_DSFX_CONTROL_ADDRESS                    0x1420008cUL


/***********************************************************
* Register Name : IOMMU_GST_PREFETCH_CNTRL_0
************************************************************/

#define IOMMU_GST_PREFETCH_CNTRL_0_GST_PREFETCH_ACCESS_PRESET_OFFSET 0
#define IOMMU_GST_PREFETCH_CNTRL_0_GST_PREFETCH_ACCESS_PRESET_MASK 0x1

#define IOMMU_GST_PREFETCH_CNTRL_0_Reserved_31_1_OFFSET        1
#define IOMMU_GST_PREFETCH_CNTRL_0_Reserved_31_1_MASK          0xfffffffe

typedef union {
  struct {
    UINT32                          GST_PREFETCH_ACCESS_PRESET:1;
    UINT32                                       Reserved_31_1:31;
  } Field;
  UINT32 Value;
} IOMMU_GST_PREFETCH_CNTRL_0_STRUCT;

#define PCICFG_IOMMUL2_IOMMU_GST_PREFETCH_CNTRL_0_OFFSET            0xe0

#define SMN_IOMMU_GST_PREFETCH_CNTRL_0_ADDRESS                        0x13f000e0UL
#define SMN_IOMMU0NBIO0_IOMMU_GST_PREFETCH_CNTRL_0_ADDRESS            0x13f000e0UL
#define SMN_IOMMU0NBIO1_IOMMU_GST_PREFETCH_CNTRL_0_ADDRESS            0x141000e0UL
#define SMN_IOMMU1NBIO0_IOMMU_GST_PREFETCH_CNTRL_0_ADDRESS            0x140000e0UL
#define SMN_IOMMU1NBIO1_IOMMU_GST_PREFETCH_CNTRL_0_ADDRESS            0x142000e0UL


/***********************************************************
* Register Name : IOMMU_HEADER
************************************************************/

#define IOMMU_HEADER_HEADER_TYPE_OFFSET                        0
#define IOMMU_HEADER_HEADER_TYPE_MASK                          0xff

typedef union {
  struct {
    UINT8                                         HEADER_TYPE:8;
  } Field;
  UINT8 Value;
} IOMMU_HEADER_STRUCT;

#define PCICFG_IOMMUL2_IOMMU_HEADER_OFFSET                          0xe

#define SMN_IOMMU_HEADER_ADDRESS                                      0x13f0000eUL
#define SMN_IOMMU0NBIO0_IOMMU_HEADER_ADDRESS                          0x13f0000eUL
#define SMN_IOMMU0NBIO1_IOMMU_HEADER_ADDRESS                          0x1410000eUL
#define SMN_IOMMU1NBIO0_IOMMU_HEADER_ADDRESS                          0x1400000eUL
#define SMN_IOMMU1NBIO1_IOMMU_HEADER_ADDRESS                          0x1420000eUL


/***********************************************************
* Register Name : IOMMU_INTERRUPT_LINE
************************************************************/

#define IOMMU_INTERRUPT_LINE_INTERRUPT_LINE_OFFSET             0
#define IOMMU_INTERRUPT_LINE_INTERRUPT_LINE_MASK               0xff

typedef union {
  struct {
    UINT8                                      INTERRUPT_LINE:8;
  } Field;
  UINT8 Value;
} IOMMU_INTERRUPT_LINE_STRUCT;

#define PCICFG_IOMMUL2_IOMMU_INTERRUPT_LINE_OFFSET                  0x3c

#define SMN_IOMMU_INTERRUPT_LINE_ADDRESS                              0x13f0003cUL
#define SMN_IOMMU0NBIO0_IOMMU_INTERRUPT_LINE_ADDRESS                  0x13f0003cUL
#define SMN_IOMMU0NBIO1_IOMMU_INTERRUPT_LINE_ADDRESS                  0x1410003cUL
#define SMN_IOMMU1NBIO0_IOMMU_INTERRUPT_LINE_ADDRESS                  0x1400003cUL
#define SMN_IOMMU1NBIO1_IOMMU_INTERRUPT_LINE_ADDRESS                  0x1420003cUL


/***********************************************************
* Register Name : IOMMU_INTERRUPT_PIN
************************************************************/

#define IOMMU_INTERRUPT_PIN_INTERRUPT_PIN_OFFSET               0
#define IOMMU_INTERRUPT_PIN_INTERRUPT_PIN_MASK                 0xff

typedef union {
  struct {
    UINT8                                       INTERRUPT_PIN:8;
  } Field;
  UINT8 Value;
} IOMMU_INTERRUPT_PIN_STRUCT;

#define PCICFG_IOMMUL2_IOMMU_INTERRUPT_PIN_OFFSET                   0x3d

#define SMN_IOMMU_INTERRUPT_PIN_ADDRESS                               0x13f0003dUL
#define SMN_IOMMU0NBIO0_IOMMU_INTERRUPT_PIN_ADDRESS                   0x13f0003dUL
#define SMN_IOMMU0NBIO1_IOMMU_INTERRUPT_PIN_ADDRESS                   0x1410003dUL
#define SMN_IOMMU1NBIO0_IOMMU_INTERRUPT_PIN_ADDRESS                   0x1400003dUL
#define SMN_IOMMU1NBIO1_IOMMU_INTERRUPT_PIN_ADDRESS                   0x1420003dUL


/***********************************************************
* Register Name : IOMMU_MMIO_CONTROL0_W
************************************************************/

#define IOMMU_MMIO_CONTROL0_W_PREF_SUP_W_OFFSET                0
#define IOMMU_MMIO_CONTROL0_W_PREF_SUP_W_MASK                  0x1

#define IOMMU_MMIO_CONTROL0_W_PPR_SUP_W_OFFSET                 1
#define IOMMU_MMIO_CONTROL0_W_PPR_SUP_W_MASK                   0x2

#define IOMMU_MMIO_CONTROL0_W_XT_SUP_W_OFFSET                  2
#define IOMMU_MMIO_CONTROL0_W_XT_SUP_W_MASK                    0x4

#define IOMMU_MMIO_CONTROL0_W_NX_SUP_W_OFFSET                  3
#define IOMMU_MMIO_CONTROL0_W_NX_SUP_W_MASK                    0x8

#define IOMMU_MMIO_CONTROL0_W_GT_SUP_W_OFFSET                  4
#define IOMMU_MMIO_CONTROL0_W_GT_SUP_W_MASK                    0x10

#define IOMMU_MMIO_CONTROL0_W_sATS_SUP_W_OFFSET                5
#define IOMMU_MMIO_CONTROL0_W_sATS_SUP_W_MASK                  0x20

#define IOMMU_MMIO_CONTROL0_W_IA_SUP_W_OFFSET                  6
#define IOMMU_MMIO_CONTROL0_W_IA_SUP_W_MASK                    0x40

#define IOMMU_MMIO_CONTROL0_W_GA_SUP_W_OFFSET                  7
#define IOMMU_MMIO_CONTROL0_W_GA_SUP_W_MASK                    0x80

#define IOMMU_MMIO_CONTROL0_W_HE_SUP_W_OFFSET                  8
#define IOMMU_MMIO_CONTROL0_W_HE_SUP_W_MASK                    0x100

#define IOMMU_MMIO_CONTROL0_W_PC_SUP_W_OFFSET                  9
#define IOMMU_MMIO_CONTROL0_W_PC_SUP_W_MASK                    0x200

#define IOMMU_MMIO_CONTROL0_W_HATS_W_OFFSET                    10
#define IOMMU_MMIO_CONTROL0_W_HATS_W_MASK                      0xc00

#define IOMMU_MMIO_CONTROL0_W_US_SUP_W_OFFSET                  12
#define IOMMU_MMIO_CONTROL0_W_US_SUP_W_MASK                    0x1000

#define IOMMU_MMIO_CONTROL0_W_GATS_W_OFFSET                    13
#define IOMMU_MMIO_CONTROL0_W_GATS_W_MASK                      0x6000

#define IOMMU_MMIO_CONTROL0_W_PageMigration_SUP_W_OFFSET       15
#define IOMMU_MMIO_CONTROL0_W_PageMigration_SUP_W_MASK         0x8000

#define IOMMU_MMIO_CONTROL0_W_GCR3TRPModeSup_W_OFFSET          16
#define IOMMU_MMIO_CONTROL0_W_GCR3TRPModeSup_W_MASK            0x10000

#define IOMMU_MMIO_CONTROL0_W_GstBufferTRPModeSup_W_OFFSET     17
#define IOMMU_MMIO_CONTROL0_W_GstBufferTRPModeSup_W_MASK       0x20000

#define IOMMU_MMIO_CONTROL0_W_SNPAVICSup_W_OFFSET              18
#define IOMMU_MMIO_CONTROL0_W_SNPAVICSup_W_MASK                0x1c0000

#define IOMMU_MMIO_CONTROL0_W_GAM_SUP_W_OFFSET                 21
#define IOMMU_MMIO_CONTROL0_W_GAM_SUP_W_MASK                   0xe00000

#define IOMMU_MMIO_CONTROL0_W_PPRF_W_OFFSET                    24
#define IOMMU_MMIO_CONTROL0_W_PPRF_W_MASK                      0x3000000

#define IOMMU_MMIO_CONTROL0_W_CXLIO_SUP_W_OFFSET               26
#define IOMMU_MMIO_CONTROL0_W_CXLIO_SUP_W_MASK                 0x4000000

#define IOMMU_MMIO_CONTROL0_W_Reserved_27_27_OFFSET            27
#define IOMMU_MMIO_CONTROL0_W_Reserved_27_27_MASK              0x8000000

#define IOMMU_MMIO_CONTROL0_W_EVENTF_W_OFFSET                  28
#define IOMMU_MMIO_CONTROL0_W_EVENTF_W_MASK                    0x30000000

#define IOMMU_MMIO_CONTROL0_W_GLX_SUP_W_OFFSET                 30
#define IOMMU_MMIO_CONTROL0_W_GLX_SUP_W_MASK                   0xc0000000

typedef union {
  struct {
    UINT32                                          PREF_SUP_W:1;
    UINT32                                           PPR_SUP_W:1;
    UINT32                                            XT_SUP_W:1;
    UINT32                                            NX_SUP_W:1;
    UINT32                                            GT_SUP_W:1;
    UINT32                                          sATS_SUP_W:1;
    UINT32                                            IA_SUP_W:1;
    UINT32                                            GA_SUP_W:1;
    UINT32                                            HE_SUP_W:1;
    UINT32                                            PC_SUP_W:1;
    UINT32                                              HATS_W:2;
    UINT32                                            US_SUP_W:1;
    UINT32                                              GATS_W:2;
    UINT32                                 PageMigration_SUP_W:1;
    UINT32                                    GCR3TRPModeSup_W:1;
    UINT32                               GstBufferTRPModeSup_W:1;
    UINT32                                        SNPAVICSup_W:3;
    UINT32                                           GAM_SUP_W:3;
    UINT32                                              PPRF_W:2;
    UINT32                                         CXLIO_SUP_W:1;
    UINT32                                      Reserved_27_27:1;
    UINT32                                            EVENTF_W:2;
    UINT32                                           GLX_SUP_W:2;
  } Field;
  UINT32 Value;
} IOMMU_MMIO_CONTROL0_W_STRUCT;

#define PCICFG_IOMMUL2_IOMMU_MMIO_CONTROL0_W_OFFSET                 0x80

#define SMN_IOMMU_MMIO_CONTROL0_W_ADDRESS                             0x13f00080UL
#define SMN_IOMMU0NBIO0_IOMMU_MMIO_CONTROL0_W_ADDRESS                 0x13f00080UL
#define SMN_IOMMU0NBIO1_IOMMU_MMIO_CONTROL0_W_ADDRESS                 0x14100080UL
#define SMN_IOMMU1NBIO0_IOMMU_MMIO_CONTROL0_W_ADDRESS                 0x14000080UL
#define SMN_IOMMU1NBIO1_IOMMU_MMIO_CONTROL0_W_ADDRESS                 0x14200080UL


/***********************************************************
* Register Name : IOMMU_MMIO_CONTROL1_W
************************************************************/

#define IOMMU_MMIO_CONTROL1_W_PAS_MAX_W_OFFSET                 0
#define IOMMU_MMIO_CONTROL1_W_PAS_MAX_W_MASK                   0xf

#define IOMMU_MMIO_CONTROL1_W_IDECMD_SUP_W_OFFSET              4
#define IOMMU_MMIO_CONTROL1_W_IDECMD_SUP_W_MASK                0x10

#define IOMMU_MMIO_CONTROL1_W_SEVSNPIO_SUP_W_OFFSET            5
#define IOMMU_MMIO_CONTROL1_W_SEVSNPIO_SUP_W_MASK              0x20

#define IOMMU_MMIO_CONTROL1_W_DTE_seg_W_OFFSET                 6
#define IOMMU_MMIO_CONTROL1_W_DTE_seg_W_MASK                   0xc0

#define IOMMU_MMIO_CONTROL1_W_PPR_OVERFLOW_EARLY_SUP_W_OFFSET  8
#define IOMMU_MMIO_CONTROL1_W_PPR_OVERFLOW_EARLY_SUP_W_MASK    0x100

#define IOMMU_MMIO_CONTROL1_W_PPR_AUTORESP_SUP_W_OFFSET        9
#define IOMMU_MMIO_CONTROL1_W_PPR_AUTORESP_SUP_W_MASK          0x200

#define IOMMU_MMIO_CONTROL1_W_BLOCK_STOPMARK_SUP_W_OFFSET      10
#define IOMMU_MMIO_CONTROL1_W_BLOCK_STOPMARK_SUP_W_MASK        0x400

#define IOMMU_MMIO_CONTROL1_W_MARCnum_SUP_W_OFFSET             11
#define IOMMU_MMIO_CONTROL1_W_MARCnum_SUP_W_MASK               0x1800

#define IOMMU_MMIO_CONTROL1_W_SNOOP_ATTRS_SUP_W_OFFSET         13
#define IOMMU_MMIO_CONTROL1_W_SNOOP_ATTRS_SUP_W_MASK           0x2000

#define IOMMU_MMIO_CONTROL1_W_GIo_SUP_W_OFFSET                 14
#define IOMMU_MMIO_CONTROL1_W_GIo_SUP_W_MASK                   0x4000

#define IOMMU_MMIO_CONTROL1_W_HA_SUP_W_OFFSET                  15
#define IOMMU_MMIO_CONTROL1_W_HA_SUP_W_MASK                    0x8000

#define IOMMU_MMIO_CONTROL1_W_EPH_SUP_W_OFFSET                 16
#define IOMMU_MMIO_CONTROL1_W_EPH_SUP_W_MASK                   0x10000

#define IOMMU_MMIO_CONTROL1_W_ATTRFW_SUP_W_OFFSET              17
#define IOMMU_MMIO_CONTROL1_W_ATTRFW_SUP_W_MASK                0x20000

#define IOMMU_MMIO_CONTROL1_W_V2_HD_DIS_SUP_W_OFFSET           18
#define IOMMU_MMIO_CONTROL1_W_V2_HD_DIS_SUP_W_MASK             0x40000

#define IOMMU_MMIO_CONTROL1_W_InvIotlbTypeSup_W_OFFSET         19
#define IOMMU_MMIO_CONTROL1_W_InvIotlbTypeSup_W_MASK           0x80000

#define IOMMU_MMIO_CONTROL1_W_HD_SUP_W_OFFSET                  20
#define IOMMU_MMIO_CONTROL1_W_HD_SUP_W_MASK                    0x100000

#define IOMMU_MMIO_CONTROL1_W_VIOMMU_SUP_W_OFFSET              21
#define IOMMU_MMIO_CONTROL1_W_VIOMMU_SUP_W_MASK                0x200000

#define IOMMU_MMIO_CONTROL1_W_SVEIO_SUP_W_OFFSET               22
#define IOMMU_MMIO_CONTROL1_W_SVEIO_SUP_W_MASK                 0x400000

#define IOMMU_MMIO_CONTROL1_W_VMTABLESIZE_W_OFFSET             23
#define IOMMU_MMIO_CONTROL1_W_VMTABLESIZE_W_MASK               0x7800000

#define IOMMU_MMIO_CONTROL1_W_V2_HA_DIS_SUP_W_OFFSET           27
#define IOMMU_MMIO_CONTROL1_W_V2_HA_DIS_SUP_W_MASK             0x8000000

#define IOMMU_MMIO_CONTROL1_W_GAPPI_SUP_W_OFFSET               28
#define IOMMU_MMIO_CONTROL1_W_GAPPI_SUP_W_MASK                 0x10000000

#define IOMMU_MMIO_CONTROL1_W_GAPPIDis_SUP_W_OFFSET            29
#define IOMMU_MMIO_CONTROL1_W_GAPPIDis_SUP_W_MASK              0x20000000

#define IOMMU_MMIO_CONTROL1_W_ForcePhyDestSup_W_OFFSET         30
#define IOMMU_MMIO_CONTROL1_W_ForcePhyDestSup_W_MASK           0x40000000

#define IOMMU_MMIO_CONTROL1_W_SNP_SUP_W_OFFSET                 31
#define IOMMU_MMIO_CONTROL1_W_SNP_SUP_W_MASK                   0x80000000

typedef union {
  struct {
    UINT32                                           PAS_MAX_W:4;
    UINT32                                        IDECMD_SUP_W:1;
    UINT32                                      SEVSNPIO_SUP_W:1;
    UINT32                                           DTE_seg_W:2;
    UINT32                            PPR_OVERFLOW_EARLY_SUP_W:1;
    UINT32                                  PPR_AUTORESP_SUP_W:1;
    UINT32                                BLOCK_STOPMARK_SUP_W:1;
    UINT32                                       MARCnum_SUP_W:2;
    UINT32                                   SNOOP_ATTRS_SUP_W:1;
    UINT32                                           GIo_SUP_W:1;
    UINT32                                            HA_SUP_W:1;
    UINT32                                           EPH_SUP_W:1;
    UINT32                                        ATTRFW_SUP_W:1;
    UINT32                                     V2_HD_DIS_SUP_W:1;
    UINT32                                   InvIotlbTypeSup_W:1;
    UINT32                                            HD_SUP_W:1;
    UINT32                                        VIOMMU_SUP_W:1;
    UINT32                                         SVEIO_SUP_W:1;
    UINT32                                       VMTABLESIZE_W:4;
    UINT32                                     V2_HA_DIS_SUP_W:1;
    UINT32                                         GAPPI_SUP_W:1;
    UINT32                                      GAPPIDis_SUP_W:1;
    UINT32                                   ForcePhyDestSup_W:1;
    UINT32                                           SNP_SUP_W:1;
  } Field;
  UINT32 Value;
} IOMMU_MMIO_CONTROL1_W_STRUCT;

#define PCICFG_IOMMUL2_IOMMU_MMIO_CONTROL1_W_OFFSET                 0x84

#define SMN_IOMMU_MMIO_CONTROL1_W_ADDRESS                             0x13f00084UL
#define SMN_IOMMU0NBIO0_IOMMU_MMIO_CONTROL1_W_ADDRESS                 0x13f00084UL
#define SMN_IOMMU0NBIO1_IOMMU_MMIO_CONTROL1_W_ADDRESS                 0x14100084UL
#define SMN_IOMMU1NBIO0_IOMMU_MMIO_CONTROL1_W_ADDRESS                 0x14000084UL
#define SMN_IOMMU1NBIO1_IOMMU_MMIO_CONTROL1_W_ADDRESS                 0x14200084UL


/***********************************************************
* Register Name : IOMMU_MMIO_CONTROL2_W
************************************************************/

#define IOMMU_MMIO_CONTROL2_W_NUM_INT_SUP_W_OFFSET             0
#define IOMMU_MMIO_CONTROL2_W_NUM_INT_SUP_W_MASK               0x3

#define IOMMU_MMIO_CONTROL2_W_HTR_IGN_SUP_W_OFFSET             2
#define IOMMU_MMIO_CONTROL2_W_HTR_IGN_SUP_W_MASK               0x4

#define IOMMU_MMIO_CONTROL2_W_Reserved_31_3_OFFSET             3
#define IOMMU_MMIO_CONTROL2_W_Reserved_31_3_MASK               0xfffffff8

typedef union {
  struct {
    UINT32                                       NUM_INT_SUP_W:2;
    UINT32                                       HTR_IGN_SUP_W:1;
    UINT32                                       Reserved_31_3:29;
  } Field;
  UINT32 Value;
} IOMMU_MMIO_CONTROL2_W_STRUCT;

#define PCICFG_IOMMUL2_IOMMU_MMIO_CONTROL2_W_OFFSET                 0xe8

#define SMN_IOMMU_MMIO_CONTROL2_W_ADDRESS                             0x13f000e8UL
#define SMN_IOMMU0NBIO0_IOMMU_MMIO_CONTROL2_W_ADDRESS                 0x13f000e8UL
#define SMN_IOMMU0NBIO1_IOMMU_MMIO_CONTROL2_W_ADDRESS                 0x141000e8UL
#define SMN_IOMMU1NBIO0_IOMMU_MMIO_CONTROL2_W_ADDRESS                 0x140000e8UL
#define SMN_IOMMU1NBIO1_IOMMU_MMIO_CONTROL2_W_ADDRESS                 0x142000e8UL


/***********************************************************
* Register Name : IOMMU_MSI_ADDR_HI
************************************************************/

#define IOMMU_MSI_ADDR_HI_MSI_ADDR_HI_OFFSET                   0
#define IOMMU_MSI_ADDR_HI_MSI_ADDR_HI_MASK                     0xffffffff

typedef union {
  struct {
    UINT32                                         MSI_ADDR_HI:32;
  } Field;
  UINT32 Value;
} IOMMU_MSI_ADDR_HI_STRUCT;

#define PCICFG_IOMMUL2_IOMMU_MSI_ADDR_HI_OFFSET                     0x6c

#define SMN_IOMMU_MSI_ADDR_HI_ADDRESS                                 0x13f0006cUL
#define SMN_IOMMU0NBIO0_IOMMU_MSI_ADDR_HI_ADDRESS                     0x13f0006cUL
#define SMN_IOMMU0NBIO1_IOMMU_MSI_ADDR_HI_ADDRESS                     0x1410006cUL
#define SMN_IOMMU1NBIO0_IOMMU_MSI_ADDR_HI_ADDRESS                     0x1400006cUL
#define SMN_IOMMU1NBIO1_IOMMU_MSI_ADDR_HI_ADDRESS                     0x1420006cUL


/***********************************************************
* Register Name : IOMMU_MSI_ADDR_LO
************************************************************/

#define IOMMU_MSI_ADDR_LO_Reserved_1_0_OFFSET                  0
#define IOMMU_MSI_ADDR_LO_Reserved_1_0_MASK                    0x3

#define IOMMU_MSI_ADDR_LO_MSI_ADDR_LO_OFFSET                   2
#define IOMMU_MSI_ADDR_LO_MSI_ADDR_LO_MASK                     0xfffffffc

typedef union {
  struct {
    UINT32                                        Reserved_1_0:2;
    UINT32                                         MSI_ADDR_LO:30;
  } Field;
  UINT32 Value;
} IOMMU_MSI_ADDR_LO_STRUCT;

#define PCICFG_IOMMUL2_IOMMU_MSI_ADDR_LO_OFFSET                     0x68

#define SMN_IOMMU_MSI_ADDR_LO_ADDRESS                                 0x13f00068UL
#define SMN_IOMMU0NBIO0_IOMMU_MSI_ADDR_LO_ADDRESS                     0x13f00068UL
#define SMN_IOMMU0NBIO1_IOMMU_MSI_ADDR_LO_ADDRESS                     0x14100068UL
#define SMN_IOMMU1NBIO0_IOMMU_MSI_ADDR_LO_ADDRESS                     0x14000068UL
#define SMN_IOMMU1NBIO1_IOMMU_MSI_ADDR_LO_ADDRESS                     0x14200068UL


/***********************************************************
* Register Name : IOMMU_MSI_CAP
************************************************************/

#define IOMMU_MSI_CAP_MSI_CAP_ID_OFFSET                        0
#define IOMMU_MSI_CAP_MSI_CAP_ID_MASK                          0xff

#define IOMMU_MSI_CAP_MSI_CAP_PTR_OFFSET                       8
#define IOMMU_MSI_CAP_MSI_CAP_PTR_MASK                         0xff00

#define IOMMU_MSI_CAP_MSI_EN_OFFSET                            16
#define IOMMU_MSI_CAP_MSI_EN_MASK                              0x10000

#define IOMMU_MSI_CAP_MSI_MULT_MESS_CAP_OFFSET                 17
#define IOMMU_MSI_CAP_MSI_MULT_MESS_CAP_MASK                   0xe0000

#define IOMMU_MSI_CAP_MSI_MULT_MESS_EN_OFFSET                  20
#define IOMMU_MSI_CAP_MSI_MULT_MESS_EN_MASK                    0x700000

#define IOMMU_MSI_CAP_MSI_64_EN_OFFSET                         23
#define IOMMU_MSI_CAP_MSI_64_EN_MASK                           0x800000

#define IOMMU_MSI_CAP_Reserved_31_24_OFFSET                    24
#define IOMMU_MSI_CAP_Reserved_31_24_MASK                      0xff000000

typedef union {
  struct {
    UINT32                                          MSI_CAP_ID:8;
    UINT32                                         MSI_CAP_PTR:8;
    UINT32                                              MSI_EN:1;
    UINT32                                   MSI_MULT_MESS_CAP:3;
    UINT32                                    MSI_MULT_MESS_EN:3;
    UINT32                                           MSI_64_EN:1;
    UINT32                                      Reserved_31_24:8;
  } Field;
  UINT32 Value;
} IOMMU_MSI_CAP_STRUCT;

#define PCICFG_IOMMUL2_IOMMU_MSI_CAP_OFFSET                         0x64

#define SMN_IOMMU_MSI_CAP_ADDRESS                                     0x13f00064UL
#define SMN_IOMMU0NBIO0_IOMMU_MSI_CAP_ADDRESS                         0x13f00064UL
#define SMN_IOMMU0NBIO1_IOMMU_MSI_CAP_ADDRESS                         0x14100064UL
#define SMN_IOMMU1NBIO0_IOMMU_MSI_CAP_ADDRESS                         0x14000064UL
#define SMN_IOMMU1NBIO1_IOMMU_MSI_CAP_ADDRESS                         0x14200064UL


/***********************************************************
* Register Name : IOMMU_MSI_DATA
************************************************************/

#define IOMMU_MSI_DATA_MSI_DATA_OFFSET                         0
#define IOMMU_MSI_DATA_MSI_DATA_MASK                           0xffff

#define IOMMU_MSI_DATA_Reserved_31_16_OFFSET                   16
#define IOMMU_MSI_DATA_Reserved_31_16_MASK                     0xffff0000

typedef union {
  struct {
    UINT32                                            MSI_DATA:16;
    UINT32                                      Reserved_31_16:16;
  } Field;
  UINT32 Value;
} IOMMU_MSI_DATA_STRUCT;

#define PCICFG_IOMMUL2_IOMMU_MSI_DATA_OFFSET                        0x70

#define SMN_IOMMU_MSI_DATA_ADDRESS                                    0x13f00070UL
#define SMN_IOMMU0NBIO0_IOMMU_MSI_DATA_ADDRESS                        0x13f00070UL
#define SMN_IOMMU0NBIO1_IOMMU_MSI_DATA_ADDRESS                        0x14100070UL
#define SMN_IOMMU1NBIO0_IOMMU_MSI_DATA_ADDRESS                        0x14000070UL
#define SMN_IOMMU1NBIO1_IOMMU_MSI_DATA_ADDRESS                        0x14200070UL


/***********************************************************
* Register Name : IOMMU_MSI_MAPPING_CAP
************************************************************/

#define IOMMU_MSI_MAPPING_CAP_MSI_MAP_CAP_ID_OFFSET            0
#define IOMMU_MSI_MAPPING_CAP_MSI_MAP_CAP_ID_MASK              0xff

#define IOMMU_MSI_MAPPING_CAP_MSI_MAP_CAP_PTR_OFFSET           8
#define IOMMU_MSI_MAPPING_CAP_MSI_MAP_CAP_PTR_MASK             0xff00

#define IOMMU_MSI_MAPPING_CAP_MSI_MAP_EN_OFFSET                16
#define IOMMU_MSI_MAPPING_CAP_MSI_MAP_EN_MASK                  0x10000

#define IOMMU_MSI_MAPPING_CAP_MSI_MAP_FIXD_OFFSET              17
#define IOMMU_MSI_MAPPING_CAP_MSI_MAP_FIXD_MASK                0x20000

#define IOMMU_MSI_MAPPING_CAP_MSI_MAP_RSV_OFFSET               18
#define IOMMU_MSI_MAPPING_CAP_MSI_MAP_RSV_MASK                 0x7fc0000

#define IOMMU_MSI_MAPPING_CAP_MSI_MAP_CAP_TYPE_OFFSET          27
#define IOMMU_MSI_MAPPING_CAP_MSI_MAP_CAP_TYPE_MASK            0xf8000000

typedef union {
  struct {
    UINT32                                      MSI_MAP_CAP_ID:8;
    UINT32                                     MSI_MAP_CAP_PTR:8;
    UINT32                                          MSI_MAP_EN:1;
    UINT32                                        MSI_MAP_FIXD:1;
    UINT32                                         MSI_MAP_RSV:9;
    UINT32                                    MSI_MAP_CAP_TYPE:5;
  } Field;
  UINT32 Value;
} IOMMU_MSI_MAPPING_CAP_STRUCT;

#define PCICFG_IOMMUL2_IOMMU_MSI_MAPPING_CAP_OFFSET                 0x74

#define SMN_IOMMU_MSI_MAPPING_CAP_ADDRESS                             0x13f00074UL
#define SMN_IOMMU0NBIO0_IOMMU_MSI_MAPPING_CAP_ADDRESS                 0x13f00074UL
#define SMN_IOMMU0NBIO1_IOMMU_MSI_MAPPING_CAP_ADDRESS                 0x14100074UL
#define SMN_IOMMU1NBIO0_IOMMU_MSI_MAPPING_CAP_ADDRESS                 0x14000074UL
#define SMN_IOMMU1NBIO1_IOMMU_MSI_MAPPING_CAP_ADDRESS                 0x14200074UL


/***********************************************************
* Register Name : IOMMU_RANGE_W
************************************************************/

#define IOMMU_RANGE_W_Reserved_6_0_OFFSET                      0
#define IOMMU_RANGE_W_Reserved_6_0_MASK                        0x7f

#define IOMMU_RANGE_W_RNG_VALID_W_OFFSET                       7
#define IOMMU_RANGE_W_RNG_VALID_W_MASK                         0x80

#define IOMMU_RANGE_W_BUS_NUMBER_W_OFFSET                      8
#define IOMMU_RANGE_W_BUS_NUMBER_W_MASK                        0xff00

#define IOMMU_RANGE_W_FIRST_DEVICE_W_OFFSET                    16
#define IOMMU_RANGE_W_FIRST_DEVICE_W_MASK                      0xff0000

#define IOMMU_RANGE_W_LAST_DEVICE_W_OFFSET                     24
#define IOMMU_RANGE_W_LAST_DEVICE_W_MASK                       0xff000000

typedef union {
  struct {
    UINT32                                        Reserved_6_0:7;
    UINT32                                         RNG_VALID_W:1;
    UINT32                                        BUS_NUMBER_W:8;
    UINT32                                      FIRST_DEVICE_W:8;
    UINT32                                       LAST_DEVICE_W:8;
  } Field;
  UINT32 Value;
} IOMMU_RANGE_W_STRUCT;

#define PCICFG_IOMMUL2_IOMMU_RANGE_W_OFFSET                         0x88

#define SMN_IOMMU_RANGE_W_ADDRESS                                     0x13f00088UL
#define SMN_IOMMU0NBIO0_IOMMU_RANGE_W_ADDRESS                         0x13f00088UL
#define SMN_IOMMU0NBIO1_IOMMU_RANGE_W_ADDRESS                         0x14100088UL
#define SMN_IOMMU1NBIO0_IOMMU_RANGE_W_ADDRESS                         0x14000088UL
#define SMN_IOMMU1NBIO1_IOMMU_RANGE_W_ADDRESS                         0x14200088UL


/***********************************************************
* Register Name : IOMMU_REGPROG_INF
************************************************************/

#define IOMMU_REGPROG_INF_REG_LEVEL_PROG_INF_OFFSET            0
#define IOMMU_REGPROG_INF_REG_LEVEL_PROG_INF_MASK              0xff

typedef union {
  struct {
    UINT8                                  REG_LEVEL_PROG_INF:8;
  } Field;
  UINT8 Value;
} IOMMU_REGPROG_INF_STRUCT;

#define PCICFG_IOMMUL2_IOMMU_REGPROG_INF_OFFSET                     0x9

#define SMN_IOMMU_REGPROG_INF_ADDRESS                                 0x13f00009UL
#define SMN_IOMMU0NBIO0_IOMMU_REGPROG_INF_ADDRESS                     0x13f00009UL
#define SMN_IOMMU0NBIO1_IOMMU_REGPROG_INF_ADDRESS                     0x14100009UL
#define SMN_IOMMU1NBIO0_IOMMU_REGPROG_INF_ADDRESS                     0x14000009UL
#define SMN_IOMMU1NBIO1_IOMMU_REGPROG_INF_ADDRESS                     0x14200009UL


/***********************************************************
* Register Name : IOMMU_REVISION_ID
************************************************************/

#define IOMMU_REVISION_ID_MINOR_REV_ID_OFFSET                  0
#define IOMMU_REVISION_ID_MINOR_REV_ID_MASK                    0xf

#define IOMMU_REVISION_ID_MAJOR_REV_ID_OFFSET                  4
#define IOMMU_REVISION_ID_MAJOR_REV_ID_MASK                    0xf0

typedef union {
  struct {
    UINT8                                        MINOR_REV_ID:4;
    UINT8                                        MAJOR_REV_ID:4;
  } Field;
  UINT8 Value;
} IOMMU_REVISION_ID_STRUCT;

#define PCICFG_IOMMUL2_IOMMU_REVISION_ID_OFFSET                     0x8

#define SMN_IOMMU_REVISION_ID_ADDRESS                                 0x13f00008UL
#define SMN_IOMMU0NBIO0_IOMMU_REVISION_ID_ADDRESS                     0x13f00008UL
#define SMN_IOMMU0NBIO1_IOMMU_REVISION_ID_ADDRESS                     0x14100008UL
#define SMN_IOMMU1NBIO0_IOMMU_REVISION_ID_ADDRESS                     0x14000008UL
#define SMN_IOMMU1NBIO1_IOMMU_REVISION_ID_ADDRESS                     0x14200008UL


/***********************************************************
* Register Name : IOMMU_STATUS
************************************************************/

#define IOMMU_STATUS_Reserved_2_0_OFFSET                       0
#define IOMMU_STATUS_Reserved_2_0_MASK                         0x7

#define IOMMU_STATUS_INT_Status_OFFSET                         3
#define IOMMU_STATUS_INT_Status_MASK                           0x8

#define IOMMU_STATUS_CAP_LIST_OFFSET                           4
#define IOMMU_STATUS_CAP_LIST_MASK                             0x10

#define IOMMU_STATUS_Reserved_7_5_OFFSET                       5
#define IOMMU_STATUS_Reserved_7_5_MASK                         0xe0

#define IOMMU_STATUS_MASTER_DATA_ERROR_OFFSET                  8
#define IOMMU_STATUS_MASTER_DATA_ERROR_MASK                    0x100

#define IOMMU_STATUS_Reserved_10_9_OFFSET                      9
#define IOMMU_STATUS_Reserved_10_9_MASK                        0x600

#define IOMMU_STATUS_SIGNAL_TARGET_ABORT_OFFSET                11
#define IOMMU_STATUS_SIGNAL_TARGET_ABORT_MASK                  0x800

#define IOMMU_STATUS_RECEIVED_TARGET_ABORT_OFFSET              12
#define IOMMU_STATUS_RECEIVED_TARGET_ABORT_MASK                0x1000

#define IOMMU_STATUS_RECEIVED_MASTER_ABORT_OFFSET              13
#define IOMMU_STATUS_RECEIVED_MASTER_ABORT_MASK                0x2000

#define IOMMU_STATUS_SIGNALED_SYSTEM_ERROR_OFFSET              14
#define IOMMU_STATUS_SIGNALED_SYSTEM_ERROR_MASK                0x4000

#define IOMMU_STATUS_PARITY_ERROR_DETECTED_OFFSET              15
#define IOMMU_STATUS_PARITY_ERROR_DETECTED_MASK                0x8000

typedef union {
  struct {
    UINT16                                        Reserved_2_0:3;
    UINT16                                          INT_Status:1;
    UINT16                                            CAP_LIST:1;
    UINT16                                        Reserved_7_5:3;
    UINT16                                   MASTER_DATA_ERROR:1;
    UINT16                                       Reserved_10_9:2;
    UINT16                                 SIGNAL_TARGET_ABORT:1;
    UINT16                               RECEIVED_TARGET_ABORT:1;
    UINT16                               RECEIVED_MASTER_ABORT:1;
    UINT16                               SIGNALED_SYSTEM_ERROR:1;
    UINT16                               PARITY_ERROR_DETECTED:1;
  } Field;
  UINT16 Value;
} IOMMU_STATUS_STRUCT;

#define PCICFG_IOMMUL2_IOMMU_STATUS_OFFSET                          0x6

#define SMN_IOMMU_STATUS_ADDRESS                                      0x13f00006UL
#define SMN_IOMMU0NBIO0_IOMMU_STATUS_ADDRESS                          0x13f00006UL
#define SMN_IOMMU0NBIO1_IOMMU_STATUS_ADDRESS                          0x14100006UL
#define SMN_IOMMU1NBIO0_IOMMU_STATUS_ADDRESS                          0x14000006UL
#define SMN_IOMMU1NBIO1_IOMMU_STATUS_ADDRESS                          0x14200006UL


/***********************************************************
* Register Name : IOMMU_SUB_CLASS
************************************************************/

#define IOMMU_SUB_CLASS_SUB_CLASS_INF_OFFSET                   0
#define IOMMU_SUB_CLASS_SUB_CLASS_INF_MASK                     0xff

typedef union {
  struct {
    UINT8                                       SUB_CLASS_INF:8;
  } Field;
  UINT8 Value;
} IOMMU_SUB_CLASS_STRUCT;

#define PCICFG_IOMMUL2_IOMMU_SUB_CLASS_OFFSET                       0xa

#define SMN_IOMMU_SUB_CLASS_ADDRESS                                   0x13f0000aUL
#define SMN_IOMMU0NBIO0_IOMMU_SUB_CLASS_ADDRESS                       0x13f0000aUL
#define SMN_IOMMU0NBIO1_IOMMU_SUB_CLASS_ADDRESS                       0x1410000aUL
#define SMN_IOMMU1NBIO0_IOMMU_SUB_CLASS_ADDRESS                       0x1400000aUL
#define SMN_IOMMU1NBIO1_IOMMU_SUB_CLASS_ADDRESS                       0x1420000aUL


/***********************************************************
* Register Name : IOMMU_VENDOR_ID
************************************************************/

#define IOMMU_VENDOR_ID_VENDOR_ID_OFFSET                       0
#define IOMMU_VENDOR_ID_VENDOR_ID_MASK                         0xffff

typedef union {
  struct {
    UINT16                                           VENDOR_ID:16;
  } Field;
  UINT16 Value;
} IOMMU_VENDOR_ID_STRUCT;

#define PCICFG_IOMMUL2_IOMMU_VENDOR_ID_OFFSET                       0x0

#define SMN_IOMMU_VENDOR_ID_ADDRESS                                   0x13f00000UL
#define SMN_IOMMU0NBIO0_IOMMU_VENDOR_ID_ADDRESS                       0x13f00000UL
#define SMN_IOMMU0NBIO1_IOMMU_VENDOR_ID_ADDRESS                       0x14100000UL
#define SMN_IOMMU1NBIO0_IOMMU_VENDOR_ID_ADDRESS                       0x14000000UL
#define SMN_IOMMU1NBIO1_IOMMU_VENDOR_ID_ADDRESS                       0x14200000UL


/***********************************************************
* Register Name : IOMMU_VFCNTL_BASE_HI
************************************************************/

#define IOMMU_VFCNTL_BASE_HI_VFCNTL_BASE_ADDR_HI_OFFSET        0
#define IOMMU_VFCNTL_BASE_HI_VFCNTL_BASE_ADDR_HI_MASK          0xffffffff

typedef union {
  struct {
    UINT32                                 VFCNTL_BASE_ADDR_HI:32;
  } Field;
  UINT32 Value;
} IOMMU_VFCNTL_BASE_HI_STRUCT;

#define PCICFG_IOMMUL2_IOMMU_VFCNTL_BASE_HI_OFFSET                  0xdc

#define SMN_IOMMU_VFCNTL_BASE_HI_ADDRESS                              0x13f000dcUL
#define SMN_IOMMU0NBIO0_IOMMU_VFCNTL_BASE_HI_ADDRESS                  0x13f000dcUL
#define SMN_IOMMU0NBIO1_IOMMU_VFCNTL_BASE_HI_ADDRESS                  0x141000dcUL
#define SMN_IOMMU1NBIO0_IOMMU_VFCNTL_BASE_HI_ADDRESS                  0x140000dcUL
#define SMN_IOMMU1NBIO1_IOMMU_VFCNTL_BASE_HI_ADDRESS                  0x142000dcUL


/***********************************************************
* Register Name : IOMMU_VFCNTL_BASE_LO
************************************************************/

#define IOMMU_VFCNTL_BASE_LO_VFCNTL_ENABLE_OFFSET              0
#define IOMMU_VFCNTL_BASE_LO_VFCNTL_ENABLE_MASK                0x1

#define IOMMU_VFCNTL_BASE_LO_Reserved_21_1_OFFSET              1
#define IOMMU_VFCNTL_BASE_LO_Reserved_21_1_MASK                0x3ffffe

#define IOMMU_VFCNTL_BASE_LO_VFCNTL_BASE_ADDR_LO_OFFSET        22
#define IOMMU_VFCNTL_BASE_LO_VFCNTL_BASE_ADDR_LO_MASK          0xffc00000

typedef union {
  struct {
    UINT32                                       VFCNTL_ENABLE:1;
    UINT32                                       Reserved_21_1:21;
    UINT32                                 VFCNTL_BASE_ADDR_LO:10;
  } Field;
  UINT32 Value;
} IOMMU_VFCNTL_BASE_LO_STRUCT;

#define PCICFG_IOMMUL2_IOMMU_VFCNTL_BASE_LO_OFFSET                  0xd8

#define SMN_IOMMU_VFCNTL_BASE_LO_ADDRESS                              0x13f000d8UL
#define SMN_IOMMU0NBIO0_IOMMU_VFCNTL_BASE_LO_ADDRESS                  0x13f000d8UL
#define SMN_IOMMU0NBIO1_IOMMU_VFCNTL_BASE_LO_ADDRESS                  0x141000d8UL
#define SMN_IOMMU1NBIO0_IOMMU_VFCNTL_BASE_LO_ADDRESS                  0x140000d8UL
#define SMN_IOMMU1NBIO1_IOMMU_VFCNTL_BASE_LO_ADDRESS                  0x142000d8UL


/***********************************************************
* Register Name : IOMMU_VF_BASE_HI
************************************************************/

#define IOMMU_VF_BASE_HI_VF_BASE_ADDR_HI_OFFSET                0
#define IOMMU_VF_BASE_HI_VF_BASE_ADDR_HI_MASK                  0xffffffff

typedef union {
  struct {
    UINT32                                     VF_BASE_ADDR_HI:32;
  } Field;
  UINT32 Value;
} IOMMU_VF_BASE_HI_STRUCT;

#define PCICFG_IOMMUL2_IOMMU_VF_BASE_HI_OFFSET                      0xd4

#define SMN_IOMMU_VF_BASE_HI_ADDRESS                                  0x13f000d4UL
#define SMN_IOMMU0NBIO0_IOMMU_VF_BASE_HI_ADDRESS                      0x13f000d4UL
#define SMN_IOMMU0NBIO1_IOMMU_VF_BASE_HI_ADDRESS                      0x141000d4UL
#define SMN_IOMMU1NBIO0_IOMMU_VF_BASE_HI_ADDRESS                      0x140000d4UL
#define SMN_IOMMU1NBIO1_IOMMU_VF_BASE_HI_ADDRESS                      0x142000d4UL


/***********************************************************
* Register Name : IOMMU_VF_BASE_LO
************************************************************/

#define IOMMU_VF_BASE_LO_VF_ENABLE_OFFSET                      0
#define IOMMU_VF_BASE_LO_VF_ENABLE_MASK                        0x1

#define IOMMU_VF_BASE_LO_Reserved_27_1_OFFSET                  1
#define IOMMU_VF_BASE_LO_Reserved_27_1_MASK                    0xffffffe

#define IOMMU_VF_BASE_LO_VF_BASE_ADDR_LO_OFFSET                28
#define IOMMU_VF_BASE_LO_VF_BASE_ADDR_LO_MASK                  0xf0000000

typedef union {
  struct {
    UINT32                                           VF_ENABLE:1;
    UINT32                                       Reserved_27_1:27;
    UINT32                                     VF_BASE_ADDR_LO:4;
  } Field;
  UINT32 Value;
} IOMMU_VF_BASE_LO_STRUCT;

#define PCICFG_IOMMUL2_IOMMU_VF_BASE_LO_OFFSET                      0xd0

#define SMN_IOMMU_VF_BASE_LO_ADDRESS                                  0x13f000d0UL
#define SMN_IOMMU0NBIO0_IOMMU_VF_BASE_LO_ADDRESS                      0x13f000d0UL
#define SMN_IOMMU0NBIO1_IOMMU_VF_BASE_LO_ADDRESS                      0x141000d0UL
#define SMN_IOMMU1NBIO0_IOMMU_VF_BASE_LO_ADDRESS                      0x140000d0UL
#define SMN_IOMMU1NBIO1_IOMMU_VF_BASE_LO_ADDRESS                      0x142000d0UL


/***********************************************************
* Register Name : IOMMU_VIOMMU_CAP
************************************************************/

#define IOMMU_VIOMMU_CAP_VIOMMU_CAP_ID_OFFSET                  0
#define IOMMU_VIOMMU_CAP_VIOMMU_CAP_ID_MASK                    0xff

#define IOMMU_VIOMMU_CAP_VIOMMU_CAP_PTR_OFFSET                 8
#define IOMMU_VIOMMU_CAP_VIOMMU_CAP_PTR_MASK                   0xff00

#define IOMMU_VIOMMU_CAP_VIOMMU_CAP_LEN_OFFSET                 16
#define IOMMU_VIOMMU_CAP_VIOMMU_CAP_LEN_MASK                   0xff0000

#define IOMMU_VIOMMU_CAP_Reserved_26_24_OFFSET                 24
#define IOMMU_VIOMMU_CAP_Reserved_26_24_MASK                   0x7000000

#define IOMMU_VIOMMU_CAP_VIOMMU_CAP_TYPE_OFFSET                27
#define IOMMU_VIOMMU_CAP_VIOMMU_CAP_TYPE_MASK                  0xf8000000

typedef union {
  struct {
    UINT32                                       VIOMMU_CAP_ID:8;
    UINT32                                      VIOMMU_CAP_PTR:8;
    UINT32                                      VIOMMU_CAP_LEN:8;
    UINT32                                      Reserved_26_24:3;
    UINT32                                     VIOMMU_CAP_TYPE:5;
  } Field;
  UINT32 Value;
} IOMMU_VIOMMU_CAP_STRUCT;

#define PCICFG_IOMMUL2_IOMMU_VIOMMU_CAP_OFFSET                      0xc8

#define SMN_IOMMU_VIOMMU_CAP_ADDRESS                                  0x13f000c8UL
#define SMN_IOMMU0NBIO0_IOMMU_VIOMMU_CAP_ADDRESS                      0x13f000c8UL
#define SMN_IOMMU0NBIO1_IOMMU_VIOMMU_CAP_ADDRESS                      0x141000c8UL
#define SMN_IOMMU1NBIO0_IOMMU_VIOMMU_CAP_ADDRESS                      0x140000c8UL
#define SMN_IOMMU1NBIO1_IOMMU_VIOMMU_CAP_ADDRESS                      0x142000c8UL


/***********************************************************
* Register Name : IOMMU_VIOMMU_PM_STALL_RETRY_CNTRL
************************************************************/

#define IOMMU_VIOMMU_PM_STALL_RETRY_CNTRL_tw_resend_freq_OFFSET 0
#define IOMMU_VIOMMU_PM_STALL_RETRY_CNTRL_tw_resend_freq_MASK  0xff

#define IOMMU_VIOMMU_PM_STALL_RETRY_CNTRL_tw_resend_limit_OFFSET 8
#define IOMMU_VIOMMU_PM_STALL_RETRY_CNTRL_tw_resend_limit_MASK 0x3ff00

#define IOMMU_VIOMMU_PM_STALL_RETRY_CNTRL_Reserved_31_18_OFFSET 18
#define IOMMU_VIOMMU_PM_STALL_RETRY_CNTRL_Reserved_31_18_MASK  0xfffc0000

typedef union {
  struct {
    UINT32                                      tw_resend_freq:8;
    UINT32                                     tw_resend_limit:10;
    UINT32                                      Reserved_31_18:14;
  } Field;
  UINT32 Value;
} IOMMU_VIOMMU_PM_STALL_RETRY_CNTRL_STRUCT;

#define PCICFG_IOMMUL2_IOMMU_VIOMMU_PM_STALL_RETRY_CNTRL_OFFSET     0x300

#define SMN_IOMMU_VIOMMU_PM_STALL_RETRY_CNTRL_ADDRESS                 0x13f00300UL
#define SMN_IOMMU0NBIO0_IOMMU_VIOMMU_PM_STALL_RETRY_CNTRL_ADDRESS     0x13f00300UL
#define SMN_IOMMU0NBIO1_IOMMU_VIOMMU_PM_STALL_RETRY_CNTRL_ADDRESS     0x14100300UL
#define SMN_IOMMU1NBIO0_IOMMU_VIOMMU_PM_STALL_RETRY_CNTRL_ADDRESS     0x14000300UL
#define SMN_IOMMU1NBIO1_IOMMU_VIOMMU_PM_STALL_RETRY_CNTRL_ADDRESS     0x14200300UL


/***********************************************************
* Register Name : L2A_UPDATE_FILTER_CNTL
************************************************************/

#define L2A_UPDATE_FILTER_CNTL_L2a_Update_Filter_Bypass_OFFSET 0
#define L2A_UPDATE_FILTER_CNTL_L2a_Update_Filter_Bypass_MASK   0x1

#define L2A_UPDATE_FILTER_CNTL_L2a_Update_Filter_RdLatency_OFFSET 1
#define L2A_UPDATE_FILTER_CNTL_L2a_Update_Filter_RdLatency_MASK 0x1e

#define L2A_UPDATE_FILTER_CNTL_Reserved_31_5_OFFSET            5
#define L2A_UPDATE_FILTER_CNTL_Reserved_31_5_MASK              0xffffffe0

typedef union {
  struct {
    UINT32                            L2a_Update_Filter_Bypass:1;
    UINT32                         L2a_Update_Filter_RdLatency:4;
    UINT32                                       Reserved_31_5:27;
  } Field;
  UINT32 Value;
} L2A_UPDATE_FILTER_CNTL_STRUCT;

#define SMN_L2A_UPDATE_FILTER_CNTL_ADDRESS                            0x15700088UL
#define SMN_IOMMU0NBIO0_L2A_UPDATE_FILTER_CNTL_ADDRESS                0x15700088UL
#define SMN_IOMMU0NBIO1_L2A_UPDATE_FILTER_CNTL_ADDRESS                0x15900088UL
#define SMN_IOMMU1NBIO0_L2A_UPDATE_FILTER_CNTL_ADDRESS                0x15800088UL
#define SMN_IOMMU1NBIO1_L2A_UPDATE_FILTER_CNTL_ADDRESS                0x15a00088UL


/***********************************************************
* Register Name : L2B_IOMMU_DISABLE_CNTRL
************************************************************/

#define L2B_IOMMU_DISABLE_CNTRL_L2B_PDC_RST_IOMMUDis_OFFSET    0
#define L2B_IOMMU_DISABLE_CNTRL_L2B_PDC_RST_IOMMUDis_MASK      0x1

#define L2B_IOMMU_DISABLE_CNTRL_Reserved_31_1_OFFSET           1
#define L2B_IOMMU_DISABLE_CNTRL_Reserved_31_1_MASK             0xfffffffe

typedef union {
  struct {
    UINT32                                L2B_PDC_RST_IOMMUDis:1;
    UINT32                                       Reserved_31_1:31;
  } Field;
  UINT32 Value;
} L2B_IOMMU_DISABLE_CNTRL_STRUCT;

#define SMN_L2B_IOMMU_DISABLE_CNTRL_ADDRESS                           0x13f01368UL
#define SMN_IOMMU0NBIO0_L2B_IOMMU_DISABLE_CNTRL_ADDRESS               0x13f01368UL
#define SMN_IOMMU0NBIO1_L2B_IOMMU_DISABLE_CNTRL_ADDRESS               0x14101368UL
#define SMN_IOMMU1NBIO0_L2B_IOMMU_DISABLE_CNTRL_ADDRESS               0x14001368UL
#define SMN_IOMMU1NBIO1_L2B_IOMMU_DISABLE_CNTRL_ADDRESS               0x14201368UL


/***********************************************************
* Register Name : L2B_SDP_PARITY_ERROR_EN
************************************************************/

#define L2B_SDP_PARITY_ERROR_EN_DVM_PARITY_ERROR_EN_OFFSET     0
#define L2B_SDP_PARITY_ERROR_EN_DVM_PARITY_ERROR_EN_MASK       0x1

#define L2B_SDP_PARITY_ERROR_EN_CP_PARITY_ERROR_EN_OFFSET      1
#define L2B_SDP_PARITY_ERROR_EN_CP_PARITY_ERROR_EN_MASK        0x2

#define L2B_SDP_PARITY_ERROR_EN_TWW_PARITY_ERROR_EN_OFFSET     2
#define L2B_SDP_PARITY_ERROR_EN_TWW_PARITY_ERROR_EN_MASK       0x4

#define L2B_SDP_PARITY_ERROR_EN_VFMMIO_PARITY_ERROR_EN_OFFSET  3
#define L2B_SDP_PARITY_ERROR_EN_VFMMIO_PARITY_ERROR_EN_MASK    0x8

#define L2B_SDP_PARITY_ERROR_EN_MSG_PARITY_ERROR_EN_OFFSET     4
#define L2B_SDP_PARITY_ERROR_EN_MSG_PARITY_ERROR_EN_MASK       0x10

#define L2B_SDP_PARITY_ERROR_EN_Reserved_31_5_OFFSET           5
#define L2B_SDP_PARITY_ERROR_EN_Reserved_31_5_MASK             0xffffffe0

typedef union {
  struct {
    UINT32                                 DVM_PARITY_ERROR_EN:1;
    UINT32                                  CP_PARITY_ERROR_EN:1;
    UINT32                                 TWW_PARITY_ERROR_EN:1;
    UINT32                              VFMMIO_PARITY_ERROR_EN:1;
    UINT32                                 MSG_PARITY_ERROR_EN:1;
    UINT32                                       Reserved_31_5:27;
  } Field;
  UINT32 Value;
} L2B_SDP_PARITY_ERROR_EN_STRUCT;

#define SMN_L2B_SDP_PARITY_ERROR_EN_ADDRESS                           0x13f01288UL
#define SMN_IOMMU0NBIO0_L2B_SDP_PARITY_ERROR_EN_ADDRESS               0x13f01288UL
#define SMN_IOMMU0NBIO1_L2B_SDP_PARITY_ERROR_EN_ADDRESS               0x14101288UL
#define SMN_IOMMU1NBIO0_L2B_SDP_PARITY_ERROR_EN_ADDRESS               0x14001288UL
#define SMN_IOMMU1NBIO1_L2B_SDP_PARITY_ERROR_EN_ADDRESS               0x14201288UL


/***********************************************************
* Register Name : L2B_SDP_REQ_MAXCRED
************************************************************/

#define L2B_SDP_REQ_MAXCRED_L2B_RDRSP_MAXCRED_OFFSET           0
#define L2B_SDP_REQ_MAXCRED_L2B_RDRSP_MAXCRED_MASK             0xffff

#define L2B_SDP_REQ_MAXCRED_L2B_WRRSP_MAXCRED_OFFSET           16
#define L2B_SDP_REQ_MAXCRED_L2B_WRRSP_MAXCRED_MASK             0xffff0000

typedef union {
  struct {
    UINT32                                   L2B_RDRSP_MAXCRED:16;
    UINT32                                   L2B_WRRSP_MAXCRED:16;
  } Field;
  UINT32 Value;
} L2B_SDP_REQ_MAXCRED_STRUCT;

#define SMN_L2B_SDP_REQ_MAXCRED_ADDRESS                               0x13f01280UL
#define SMN_IOMMU0NBIO0_L2B_SDP_REQ_MAXCRED_ADDRESS                   0x13f01280UL
#define SMN_IOMMU0NBIO1_L2B_SDP_REQ_MAXCRED_ADDRESS                   0x14101280UL
#define SMN_IOMMU1NBIO0_L2B_SDP_REQ_MAXCRED_ADDRESS                   0x14001280UL
#define SMN_IOMMU1NBIO1_L2B_SDP_REQ_MAXCRED_ADDRESS                   0x14201280UL


/***********************************************************
* Register Name : L2B_SDP_RSP_MAXCRED
************************************************************/

#define L2B_SDP_RSP_MAXCRED_Reserved_23_0_OFFSET               0
#define L2B_SDP_RSP_MAXCRED_Reserved_23_0_MASK                 0xffffff

#define L2B_SDP_RSP_MAXCRED_L2B_REQ_MAXCRED_OFFSET             24
#define L2B_SDP_RSP_MAXCRED_L2B_REQ_MAXCRED_MASK               0xf000000

#define L2B_SDP_RSP_MAXCRED_L2B_DATA_MAXCRED_OFFSET            28
#define L2B_SDP_RSP_MAXCRED_L2B_DATA_MAXCRED_MASK              0xf0000000

typedef union {
  struct {
    UINT32                                       Reserved_23_0:24;
    UINT32                                     L2B_REQ_MAXCRED:4;
    UINT32                                    L2B_DATA_MAXCRED:4;
  } Field;
  UINT32 Value;
} L2B_SDP_RSP_MAXCRED_STRUCT;

#define SMN_L2B_SDP_RSP_MAXCRED_ADDRESS                               0x13f01284UL
#define SMN_IOMMU0NBIO0_L2B_SDP_RSP_MAXCRED_ADDRESS                   0x13f01284UL
#define SMN_IOMMU0NBIO1_L2B_SDP_RSP_MAXCRED_ADDRESS                   0x14101284UL
#define SMN_IOMMU1NBIO0_L2B_SDP_RSP_MAXCRED_ADDRESS                   0x14001284UL
#define SMN_IOMMU1NBIO1_L2B_SDP_RSP_MAXCRED_ADDRESS                   0x14201284UL


/***********************************************************
* Register Name : L2B_UPDATE_FILTER_CNTL
************************************************************/

#define L2B_UPDATE_FILTER_CNTL_L2b_Update_Filter_Bypass_OFFSET 0
#define L2B_UPDATE_FILTER_CNTL_L2b_Update_Filter_Bypass_MASK   0x1

#define L2B_UPDATE_FILTER_CNTL_L2b_Update_Filter_RdLatency_OFFSET 1
#define L2B_UPDATE_FILTER_CNTL_L2b_Update_Filter_RdLatency_MASK 0x1e

#define L2B_UPDATE_FILTER_CNTL_L2b_PDC_Update_Filter_TagPerfFix_OFFSET 5
#define L2B_UPDATE_FILTER_CNTL_L2b_PDC_Update_Filter_TagPerfFix_MASK 0x20

#define L2B_UPDATE_FILTER_CNTL_L2b_PTC_Update_Filter_TagPerfFix_OFFSET 6
#define L2B_UPDATE_FILTER_CNTL_L2b_PTC_Update_Filter_TagPerfFix_MASK 0x40

#define L2B_UPDATE_FILTER_CNTL_Reserved_31_7_OFFSET            7
#define L2B_UPDATE_FILTER_CNTL_Reserved_31_7_MASK              0xffffff80

typedef union {
  struct {
    UINT32                            L2b_Update_Filter_Bypass:1;
    UINT32                         L2b_Update_Filter_RdLatency:4;
    UINT32                    L2b_PDC_Update_Filter_TagPerfFix:1;
    UINT32                    L2b_PTC_Update_Filter_TagPerfFix:1;
    UINT32                                       Reserved_31_7:25;
  } Field;
  UINT32 Value;
} L2B_UPDATE_FILTER_CNTL_STRUCT;

#define SMN_L2B_UPDATE_FILTER_CNTL_ADDRESS                            0x13f0114cUL
#define SMN_IOMMU0NBIO0_L2B_UPDATE_FILTER_CNTL_ADDRESS                0x13f0114cUL
#define SMN_IOMMU0NBIO1_L2B_UPDATE_FILTER_CNTL_ADDRESS                0x1410114cUL
#define SMN_IOMMU1NBIO0_L2B_UPDATE_FILTER_CNTL_ADDRESS                0x1400114cUL
#define SMN_IOMMU1NBIO1_L2B_UPDATE_FILTER_CNTL_ADDRESS                0x1420114cUL


/***********************************************************
* Register Name : L2_CONTROL_0
************************************************************/

#define L2_CONTROL_0_Reserved_0_0_OFFSET                       0
#define L2_CONTROL_0_Reserved_0_0_MASK                         0x1

#define L2_CONTROL_0_AllowL1CacheVZero_OFFSET                  1
#define L2_CONTROL_0_AllowL1CacheVZero_MASK                    0x2

#define L2_CONTROL_0_AllowL1CacheATSRsp_OFFSET                 2
#define L2_CONTROL_0_AllowL1CacheATSRsp_MASK                   0x4

#define L2_CONTROL_0_DTCHitVZeroOrIVZero_OFFSET                3
#define L2_CONTROL_0_DTCHitVZeroOrIVZero_MASK                  0x8

#define L2_CONTROL_0_Reserved_9_4_OFFSET                       4
#define L2_CONTROL_0_Reserved_9_4_MASK                         0x3f0

#define L2_CONTROL_0_SIDEPTEOnUntransExcl_OFFSET               10
#define L2_CONTROL_0_SIDEPTEOnUntransExcl_MASK                 0x400

#define L2_CONTROL_0_SIDEPTEOnAddrTransExcl_OFFSET             11
#define L2_CONTROL_0_SIDEPTEOnAddrTransExcl_MASK               0x800

#define L2_CONTROL_0_Reserved_18_12_OFFSET                     12
#define L2_CONTROL_0_Reserved_18_12_MASK                       0x7f000

#define L2_CONTROL_0_AllowL1CacheLargePagemode0_OFFSET         19
#define L2_CONTROL_0_AllowL1CacheLargePagemode0_MASK           0x80000

#define L2_CONTROL_0_IFifoBurstLength_OFFSET                   20
#define L2_CONTROL_0_IFifoBurstLength_MASK                     0xf00000

#define L2_CONTROL_0_IFifoClientPriority_OFFSET                24
#define L2_CONTROL_0_IFifoClientPriority_MASK                  0xff000000

typedef union {
  struct {
    UINT32                                        Reserved_0_0:1;
    UINT32                                   AllowL1CacheVZero:1;
    UINT32                                  AllowL1CacheATSRsp:1;
    UINT32                                 DTCHitVZeroOrIVZero:1;
    UINT32                                        Reserved_9_4:6;
    UINT32                                SIDEPTEOnUntransExcl:1;
    UINT32                              SIDEPTEOnAddrTransExcl:1;
    UINT32                                      Reserved_18_12:7;
    UINT32                          AllowL1CacheLargePagemode0:1;
    UINT32                                    IFifoBurstLength:4;
    UINT32                                 IFifoClientPriority:8;
  } Field;
  UINT32 Value;
} L2_CONTROL_0_STRUCT;

#define SMN_L2_CONTROL_0_ADDRESS                                      0x15700030UL
#define SMN_IOMMU0NBIO0_L2_CONTROL_0_ADDRESS                          0x15700030UL
#define SMN_IOMMU0NBIO1_L2_CONTROL_0_ADDRESS                          0x15900030UL
#define SMN_IOMMU1NBIO0_L2_CONTROL_0_ADDRESS                          0x15800030UL
#define SMN_IOMMU1NBIO1_L2_CONTROL_0_ADDRESS                          0x15a00030UL


/***********************************************************
* Register Name : L2_CONTROL_1
************************************************************/

#define L2_CONTROL_1_SeqInvBurstLimitInv_OFFSET                0
#define L2_CONTROL_1_SeqInvBurstLimitInv_MASK                  0xff

#define L2_CONTROL_1_SeqInvBurstLimitL2Req_OFFSET              8
#define L2_CONTROL_1_SeqInvBurstLimitL2Req_MASK                0xff00

#define L2_CONTROL_1_SeqInvBurstLimitEn_OFFSET                 16
#define L2_CONTROL_1_SeqInvBurstLimitEn_MASK                   0x10000

#define L2_CONTROL_1_DBUSDis_OFFSET                            17
#define L2_CONTROL_1_DBUSDis_MASK                              0x20000

#define L2_CONTROL_1_Reserved_23_18_OFFSET                     18
#define L2_CONTROL_1_Reserved_23_18_MASK                       0xfc0000

#define L2_CONTROL_1_PerfThreshold_OFFSET                      24
#define L2_CONTROL_1_PerfThreshold_MASK                        0xff000000

typedef union {
  struct {
    UINT32                                 SeqInvBurstLimitInv:8;
    UINT32                               SeqInvBurstLimitL2Req:8;
    UINT32                                  SeqInvBurstLimitEn:1;
    UINT32                                             DBUSDis:1;
    UINT32                                      Reserved_23_18:6;
    UINT32                                       PerfThreshold:8;
  } Field;
  UINT32 Value;
} L2_CONTROL_1_STRUCT;

#define SMN_L2_CONTROL_1_ADDRESS                                      0x15700034UL
#define SMN_IOMMU0NBIO0_L2_CONTROL_1_ADDRESS                          0x15700034UL
#define SMN_IOMMU0NBIO1_L2_CONTROL_1_ADDRESS                          0x15900034UL
#define SMN_IOMMU1NBIO0_L2_CONTROL_1_ADDRESS                          0x15800034UL
#define SMN_IOMMU1NBIO1_L2_CONTROL_1_ADDRESS                          0x15a00034UL


/***********************************************************
* Register Name : L2_CONTROL_5
************************************************************/

#define L2_CONTROL_5_QueueArbFBPri_OFFSET                      0
#define L2_CONTROL_5_QueueArbFBPri_MASK                        0x1

#define L2_CONTROL_5_Reserved_1_1_OFFSET                       1
#define L2_CONTROL_5_Reserved_1_1_MASK                         0x2

#define L2_CONTROL_5_FC1Dis_OFFSET                             2
#define L2_CONTROL_5_FC1Dis_MASK                               0x4

#define L2_CONTROL_5_DTCUpdateVOneIVZero_OFFSET                3
#define L2_CONTROL_5_DTCUpdateVOneIVZero_MASK                  0x8

#define L2_CONTROL_5_DTCUpdateVZeroIVOne_OFFSET                4
#define L2_CONTROL_5_DTCUpdateVZeroIVOne_MASK                  0x10

#define L2_CONTROL_5_Reserved_5_5_OFFSET                       5
#define L2_CONTROL_5_Reserved_5_5_MASK                         0x20

#define L2_CONTROL_5_FC3Dis_OFFSET                             6
#define L2_CONTROL_5_FC3Dis_MASK                               0x40

#define L2_CONTROL_5_Reserved_10_7_OFFSET                      7
#define L2_CONTROL_5_Reserved_10_7_MASK                        0x780

#define L2_CONTROL_5_ForceTWonVCQoS_OFFSET                     11
#define L2_CONTROL_5_ForceTWonVCQoS_MASK                       0x800

#define L2_CONTROL_5_GST_partial_ptc_cntrl_OFFSET              12
#define L2_CONTROL_5_GST_partial_ptc_cntrl_MASK                0x7f000

#define L2_CONTROL_5_Reserved_19_19_OFFSET                     19
#define L2_CONTROL_5_Reserved_19_19_MASK                       0x80000

#define L2_CONTROL_5_STORE_PDPE_QOS_PTC_OFFSET                 20
#define L2_CONTROL_5_STORE_PDPE_QOS_PTC_MASK                   0x100000

#define L2_CONTROL_5_Reserved_24_21_OFFSET                     21
#define L2_CONTROL_5_Reserved_24_21_MASK                       0x1e00000

#define L2_CONTROL_5_DTCUpdatePri_OFFSET                       25
#define L2_CONTROL_5_DTCUpdatePri_MASK                         0x2000000

#define L2_CONTROL_5_L2B_L2A_v1_trans_credits_OFFSET           26
#define L2_CONTROL_5_L2B_L2A_v1_trans_credits_MASK             0xfc000000

typedef union {
  struct {
    UINT32                                       QueueArbFBPri:1;
    UINT32                                        Reserved_1_1:1;
    UINT32                                              FC1Dis:1;
    UINT32                                 DTCUpdateVOneIVZero:1;
    UINT32                                 DTCUpdateVZeroIVOne:1;
    UINT32                                        Reserved_5_5:1;
    UINT32                                              FC3Dis:1;
    UINT32                                       Reserved_10_7:4;
    UINT32                                      ForceTWonVCQoS:1;
    UINT32                               GST_partial_ptc_cntrl:7;
    UINT32                                      Reserved_19_19:1;
    UINT32                                  STORE_PDPE_QOS_PTC:1;
    UINT32                                      Reserved_24_21:4;
    UINT32                                        DTCUpdatePri:1;
    UINT32                            L2B_L2A_v1_trans_credits:6;
  } Field;
  UINT32 Value;
} L2_CONTROL_5_STRUCT;

#define SMN_L2_CONTROL_5_ADDRESS                                      0x13f01130UL
#define SMN_IOMMU0NBIO0_L2_CONTROL_5_ADDRESS                          0x13f01130UL
#define SMN_IOMMU0NBIO1_L2_CONTROL_5_ADDRESS                          0x14101130UL
#define SMN_IOMMU1NBIO0_L2_CONTROL_5_ADDRESS                          0x14001130UL
#define SMN_IOMMU1NBIO1_L2_CONTROL_5_ADDRESS                          0x14201130UL


/***********************************************************
* Register Name : L2_CONTROL_6
************************************************************/

#define L2_CONTROL_6_SeqInvBurstLimitInv_OFFSET                0
#define L2_CONTROL_6_SeqInvBurstLimitInv_MASK                  0xff

#define L2_CONTROL_6_SeqInvBurstLimitPDCReq_OFFSET             8
#define L2_CONTROL_6_SeqInvBurstLimitPDCReq_MASK               0xff00

#define L2_CONTROL_6_SeqInvBurstLimitEn_OFFSET                 16
#define L2_CONTROL_6_SeqInvBurstLimitEn_MASK                   0x10000

#define L2_CONTROL_6_Reserved_23_17_OFFSET                     17
#define L2_CONTROL_6_Reserved_23_17_MASK                       0xfe0000

#define L2_CONTROL_6_Perf2Threshold_OFFSET                     24
#define L2_CONTROL_6_Perf2Threshold_MASK                       0xff000000

typedef union {
  struct {
    UINT32                                 SeqInvBurstLimitInv:8;
    UINT32                              SeqInvBurstLimitPDCReq:8;
    UINT32                                  SeqInvBurstLimitEn:1;
    UINT32                                      Reserved_23_17:7;
    UINT32                                      Perf2Threshold:8;
  } Field;
  UINT32 Value;
} L2_CONTROL_6_STRUCT;

#define SMN_L2_CONTROL_6_ADDRESS                                      0x13f0113cUL
#define SMN_IOMMU0NBIO0_L2_CONTROL_6_ADDRESS                          0x13f0113cUL
#define SMN_IOMMU0NBIO1_L2_CONTROL_6_ADDRESS                          0x1410113cUL
#define SMN_IOMMU1NBIO0_L2_CONTROL_6_ADDRESS                          0x1400113cUL
#define SMN_IOMMU1NBIO1_L2_CONTROL_6_ADDRESS                          0x1420113cUL


/***********************************************************
* Register Name : L2_CONTROL_7
************************************************************/

#define L2_CONTROL_7_PCTRL_hysteresis_OFFSET                   0
#define L2_CONTROL_7_PCTRL_hysteresis_MASK                     0xfff

#define L2_CONTROL_7_Reserved_31_12_OFFSET                     12
#define L2_CONTROL_7_Reserved_31_12_MASK                       0xfffff000

typedef union {
  struct {
    UINT32                                    PCTRL_hysteresis:12;
    UINT32                                      Reserved_31_12:20;
  } Field;
  UINT32 Value;
} L2_CONTROL_7_STRUCT;

#define SMN_L2_CONTROL_7_ADDRESS                                      0x13f011a8UL
#define SMN_IOMMU0NBIO0_L2_CONTROL_7_ADDRESS                          0x13f011a8UL
#define SMN_IOMMU0NBIO1_L2_CONTROL_7_ADDRESS                          0x141011a8UL
#define SMN_IOMMU1NBIO0_L2_CONTROL_7_ADDRESS                          0x140011a8UL
#define SMN_IOMMU1NBIO1_L2_CONTROL_7_ADDRESS                          0x142011a8UL


/***********************************************************
* Register Name : L2_CP_CONTROL
************************************************************/

#define L2_CP_CONTROL_CPPrefetchDis_OFFSET                     0
#define L2_CP_CONTROL_CPPrefetchDis_MASK                       0x1

#define L2_CP_CONTROL_CPFlushOnWait_OFFSET                     1
#define L2_CP_CONTROL_CPFlushOnWait_MASK                       0x2

#define L2_CP_CONTROL_CPFlushOnInv_OFFSET                      2
#define L2_CP_CONTROL_CPFlushOnInv_MASK                        0x4

#define L2_CP_CONTROL_CPStallCmdErrForIOTLBCmpl_OFFSET         3
#define L2_CP_CONTROL_CPStallCmdErrForIOTLBCmpl_MASK           0x8

#define L2_CP_CONTROL_CPForceReqPassPW_OFFSET                  4
#define L2_CP_CONTROL_CPForceReqPassPW_MASK                    0x10

#define L2_CP_CONTROL_CPForceOneOutstandingCommand_OFFSET      5
#define L2_CP_CONTROL_CPForceOneOutstandingCommand_MASK        0x20

#define L2_CP_CONTROL_Reserved_15_6_OFFSET                     6
#define L2_CP_CONTROL_Reserved_15_6_MASK                       0xffc0

#define L2_CP_CONTROL_CPRdDelay_OFFSET                         16
#define L2_CP_CONTROL_CPRdDelay_MASK                           0xffff0000

typedef union {
  struct {
    UINT32                                       CPPrefetchDis:1;
    UINT32                                       CPFlushOnWait:1;
    UINT32                                        CPFlushOnInv:1;
    UINT32                           CPStallCmdErrForIOTLBCmpl:1;
    UINT32                                    CPForceReqPassPW:1;
    UINT32                        CPForceOneOutstandingCommand:1;
    UINT32                                       Reserved_15_6:10;
    UINT32                                           CPRdDelay:16;
  } Field;
  UINT32 Value;
} L2_CP_CONTROL_STRUCT;

#define SMN_L2_CP_CONTROL_ADDRESS                                     0x13f01158UL
#define SMN_IOMMU0NBIO0_L2_CP_CONTROL_ADDRESS                         0x13f01158UL
#define SMN_IOMMU0NBIO1_L2_CP_CONTROL_ADDRESS                         0x14101158UL
#define SMN_IOMMU1NBIO0_L2_CP_CONTROL_ADDRESS                         0x14001158UL
#define SMN_IOMMU1NBIO1_L2_CP_CONTROL_ADDRESS                         0x14201158UL


/***********************************************************
* Register Name : L2_CP_CONTROL_1
************************************************************/

#define L2_CP_CONTROL_1_CPL1Off_OFFSET                         0
#define L2_CP_CONTROL_1_CPL1Off_MASK                           0xffff

#define L2_CP_CONTROL_1_Reserved_31_16_OFFSET                  16
#define L2_CP_CONTROL_1_Reserved_31_16_MASK                    0xffff0000

typedef union {
  struct {
    UINT32                                             CPL1Off:16;
    UINT32                                      Reserved_31_16:16;
  } Field;
  UINT32 Value;
} L2_CP_CONTROL_1_STRUCT;

#define SMN_L2_CP_CONTROL_1_ADDRESS                                   0x13f0115cUL
#define SMN_IOMMU0NBIO0_L2_CP_CONTROL_1_ADDRESS                       0x13f0115cUL
#define SMN_IOMMU0NBIO1_L2_CP_CONTROL_1_ADDRESS                       0x1410115cUL
#define SMN_IOMMU1NBIO0_L2_CP_CONTROL_1_ADDRESS                       0x1400115cUL
#define SMN_IOMMU1NBIO1_L2_CP_CONTROL_1_ADDRESS                       0x1420115cUL


/***********************************************************
* Register Name : L2_CREDIT_CONTROL_0
************************************************************/

#define L2_CREDIT_CONTROL_0_FC1Credits_OFFSET                  0
#define L2_CREDIT_CONTROL_0_FC1Credits_MASK                    0x7f

#define L2_CREDIT_CONTROL_0_FC1Override_OFFSET                 7
#define L2_CREDIT_CONTROL_0_FC1Override_MASK                   0x80

#define L2_CREDIT_CONTROL_0_Reserved_14_8_OFFSET               8
#define L2_CREDIT_CONTROL_0_Reserved_14_8_MASK                 0x7f00

#define L2_CREDIT_CONTROL_0_FC3Credits_OFFSET                  15
#define L2_CREDIT_CONTROL_0_FC3Credits_MASK                    0x1f8000

#define L2_CREDIT_CONTROL_0_FC3Override_OFFSET                 21
#define L2_CREDIT_CONTROL_0_FC3Override_MASK                   0x200000

#define L2_CREDIT_CONTROL_0_Reserved_31_22_OFFSET              22
#define L2_CREDIT_CONTROL_0_Reserved_31_22_MASK                0xffc00000

typedef union {
  struct {
    UINT32                                          FC1Credits:7;
    UINT32                                         FC1Override:1;
    UINT32                                       Reserved_14_8:7;
    UINT32                                          FC3Credits:6;
    UINT32                                         FC3Override:1;
    UINT32                                      Reserved_31_22:10;
  } Field;
  UINT32 Value;
} L2_CREDIT_CONTROL_0_STRUCT;

#define SMN_L2_CREDIT_CONTROL_0_ADDRESS                               0x13f011c0UL
#define SMN_IOMMU0NBIO0_L2_CREDIT_CONTROL_0_ADDRESS                   0x13f011c0UL
#define SMN_IOMMU0NBIO1_L2_CREDIT_CONTROL_0_ADDRESS                   0x141011c0UL
#define SMN_IOMMU1NBIO0_L2_CREDIT_CONTROL_0_ADDRESS                   0x140011c0UL
#define SMN_IOMMU1NBIO1_L2_CREDIT_CONTROL_0_ADDRESS                   0x142011c0UL


/***********************************************************
* Register Name : L2_CREDIT_CONTROL_1
************************************************************/

#define L2_CREDIT_CONTROL_1_Reserved_15_0_OFFSET               0
#define L2_CREDIT_CONTROL_1_Reserved_15_0_MASK                 0xffff

#define L2_CREDIT_CONTROL_1_CP_PREFETCH_credits_OFFSET         16
#define L2_CREDIT_CONTROL_1_CP_PREFETCH_credits_MASK           0xf0000

#define L2_CREDIT_CONTROL_1_PPR_MCIF_credits_OFFSET            20
#define L2_CREDIT_CONTROL_1_PPR_MCIF_credits_MASK              0xf00000

#define L2_CREDIT_CONTROL_1_Reserved_31_24_OFFSET              24
#define L2_CREDIT_CONTROL_1_Reserved_31_24_MASK                0xff000000

typedef union {
  struct {
    UINT32                                       Reserved_15_0:16;
    UINT32                                 CP_PREFETCH_credits:4;
    UINT32                                    PPR_MCIF_credits:4;
    UINT32                                      Reserved_31_24:8;
  } Field;
  UINT32 Value;
} L2_CREDIT_CONTROL_1_STRUCT;

#define SMN_L2_CREDIT_CONTROL_1_ADDRESS                               0x13f011c4UL
#define SMN_IOMMU0NBIO0_L2_CREDIT_CONTROL_1_ADDRESS                   0x13f011c4UL
#define SMN_IOMMU0NBIO1_L2_CREDIT_CONTROL_1_ADDRESS                   0x141011c4UL
#define SMN_IOMMU1NBIO0_L2_CREDIT_CONTROL_1_ADDRESS                   0x140011c4UL
#define SMN_IOMMU1NBIO1_L2_CREDIT_CONTROL_1_ADDRESS                   0x142011c4UL


/***********************************************************
* Register Name : L2_DTC_CONTROL
************************************************************/

#define L2_DTC_CONTROL_Reserved_2_0_OFFSET                     0
#define L2_DTC_CONTROL_Reserved_2_0_MASK                       0x7

#define L2_DTC_CONTROL_DTCLRUUpdatePri_OFFSET                  3
#define L2_DTC_CONTROL_DTCLRUUpdatePri_MASK                    0x8

#define L2_DTC_CONTROL_DTCParityEn_OFFSET                      4
#define L2_DTC_CONTROL_DTCParityEn_MASK                        0x10

#define L2_DTC_CONTROL_Reserved_7_5_OFFSET                     5
#define L2_DTC_CONTROL_Reserved_7_5_MASK                       0xe0

#define L2_DTC_CONTROL_DTCInvalidationSel_OFFSET               8
#define L2_DTC_CONTROL_DTCInvalidationSel_MASK                 0x300

#define L2_DTC_CONTROL_DTCSoftInvalidate_OFFSET                10
#define L2_DTC_CONTROL_DTCSoftInvalidate_MASK                  0x400

#define L2_DTC_CONTROL_Reserved_12_11_OFFSET                   11
#define L2_DTC_CONTROL_Reserved_12_11_MASK                     0x1800

#define L2_DTC_CONTROL_DTCBypass_OFFSET                        13
#define L2_DTC_CONTROL_DTCBypass_MASK                          0x2000

#define L2_DTC_CONTROL_Reserved_14_14_OFFSET                   14
#define L2_DTC_CONTROL_Reserved_14_14_MASK                     0x4000

#define L2_DTC_CONTROL_DTCParitySupport_OFFSET                 15
#define L2_DTC_CONTROL_DTCParitySupport_MASK                   0x8000

#define L2_DTC_CONTROL_DTCWays_OFFSET                          16
#define L2_DTC_CONTROL_DTCWays_MASK                            0xff0000

#define L2_DTC_CONTROL_Reserved_27_24_OFFSET                   24
#define L2_DTC_CONTROL_Reserved_27_24_MASK                     0xf000000

#define L2_DTC_CONTROL_DTCEntries_OFFSET                       28
#define L2_DTC_CONTROL_DTCEntries_MASK                         0xf0000000

typedef union {
  struct {
    UINT32                                        Reserved_2_0:3;
    UINT32                                     DTCLRUUpdatePri:1;
    UINT32                                         DTCParityEn:1;
    UINT32                                        Reserved_7_5:3;
    UINT32                                  DTCInvalidationSel:2;
    UINT32                                   DTCSoftInvalidate:1;
    UINT32                                      Reserved_12_11:2;
    UINT32                                           DTCBypass:1;
    UINT32                                      Reserved_14_14:1;
    UINT32                                    DTCParitySupport:1;
    UINT32                                             DTCWays:8;
    UINT32                                      Reserved_27_24:4;
    UINT32                                          DTCEntries:4;
  } Field;
  UINT32 Value;
} L2_DTC_CONTROL_STRUCT;

#define SMN_L2_DTC_CONTROL_ADDRESS                                    0x15700040UL
#define SMN_IOMMU0NBIO0_L2_DTC_CONTROL_ADDRESS                        0x15700040UL
#define SMN_IOMMU0NBIO1_L2_DTC_CONTROL_ADDRESS                        0x15900040UL
#define SMN_IOMMU1NBIO0_L2_DTC_CONTROL_ADDRESS                        0x15800040UL
#define SMN_IOMMU1NBIO1_L2_DTC_CONTROL_ADDRESS                        0x15a00040UL


/***********************************************************
* Register Name : L2_DTC_HASH_CONTROL
************************************************************/

#define L2_DTC_HASH_CONTROL_Reserved_15_0_OFFSET               0
#define L2_DTC_HASH_CONTROL_Reserved_15_0_MASK                 0xffff

#define L2_DTC_HASH_CONTROL_DTCAddressMask_OFFSET              16
#define L2_DTC_HASH_CONTROL_DTCAddressMask_MASK                0xffff0000

typedef union {
  struct {
    UINT32                                       Reserved_15_0:16;
    UINT32                                      DTCAddressMask:16;
  } Field;
  UINT32 Value;
} L2_DTC_HASH_CONTROL_STRUCT;

#define SMN_L2_DTC_HASH_CONTROL_ADDRESS                               0x15700044UL
#define SMN_IOMMU0NBIO0_L2_DTC_HASH_CONTROL_ADDRESS                   0x15700044UL
#define SMN_IOMMU0NBIO1_L2_DTC_HASH_CONTROL_ADDRESS                   0x15900044UL
#define SMN_IOMMU1NBIO0_L2_DTC_HASH_CONTROL_ADDRESS                   0x15800044UL
#define SMN_IOMMU1NBIO1_L2_DTC_HASH_CONTROL_ADDRESS                   0x15a00044UL


/***********************************************************
* Register Name : L2_DTC_WAY_CONTROL
************************************************************/

#define L2_DTC_WAY_CONTROL_DTCWayDisable_OFFSET                0
#define L2_DTC_WAY_CONTROL_DTCWayDisable_MASK                  0xffff

#define L2_DTC_WAY_CONTROL_DTCWayAccessDisable_OFFSET          16
#define L2_DTC_WAY_CONTROL_DTCWayAccessDisable_MASK            0xffff0000

typedef union {
  struct {
    UINT32                                       DTCWayDisable:16;
    UINT32                                 DTCWayAccessDisable:16;
  } Field;
  UINT32 Value;
} L2_DTC_WAY_CONTROL_STRUCT;

#define SMN_L2_DTC_WAY_CONTROL_ADDRESS                                0x15700048UL
#define SMN_IOMMU0NBIO0_L2_DTC_WAY_CONTROL_ADDRESS                    0x15700048UL
#define SMN_IOMMU0NBIO1_L2_DTC_WAY_CONTROL_ADDRESS                    0x15900048UL
#define SMN_IOMMU1NBIO0_L2_DTC_WAY_CONTROL_ADDRESS                    0x15800048UL
#define SMN_IOMMU1NBIO1_L2_DTC_WAY_CONTROL_ADDRESS                    0x15a00048UL


/***********************************************************
* Register Name : L2_DVM_CNTRL
************************************************************/

#define L2_DVM_CNTRL_DVM_CompReqAddrOverride_OFFSET            0
#define L2_DVM_CNTRL_DVM_CompReqAddrOverride_MASK              0x1

#define L2_DVM_CNTRL_DVM_B2B_ASIDOnlyInvAll_OFFSET             1
#define L2_DVM_CNTRL_DVM_B2B_ASIDOnlyInvAll_MASK               0x2

#define L2_DVM_CNTRL_Reserved_7_2_OFFSET                       2
#define L2_DVM_CNTRL_Reserved_7_2_MASK                         0xfc

#define L2_DVM_CNTRL_DVM_CompReqAddr_OFFSET                    8
#define L2_DVM_CNTRL_DVM_CompReqAddr_MASK                      0xffff00

#define L2_DVM_CNTRL_DVM_SYNC_InvAll_OFFSET                    24
#define L2_DVM_CNTRL_DVM_SYNC_InvAll_MASK                      0x1000000

#define L2_DVM_CNTRL_DVM_DropSEV_ASIDOnly_OFFSET               25
#define L2_DVM_CNTRL_DVM_DropSEV_ASIDOnly_MASK                 0x2000000

#define L2_DVM_CNTRL_DVM_DropB2B_Sync_OFFSET                   26
#define L2_DVM_CNTRL_DVM_DropB2B_Sync_MASK                     0x4000000

#define L2_DVM_CNTRL_DVM_Drop_IOMMUDis_OFFSET                  27
#define L2_DVM_CNTRL_DVM_Drop_IOMMUDis_MASK                    0x8000000

#define L2_DVM_CNTRL_DVM_TWTrackingDis_OFFSET                  28
#define L2_DVM_CNTRL_DVM_TWTrackingDis_MASK                    0x10000000

#define L2_DVM_CNTRL_DVM_FilterL1Mode0_OFFSET                  29
#define L2_DVM_CNTRL_DVM_FilterL1Mode0_MASK                    0x20000000

#define L2_DVM_CNTRL_Reserved_31_30_OFFSET                     30
#define L2_DVM_CNTRL_Reserved_31_30_MASK                       0xc0000000

typedef union {
  struct {
    UINT32                             DVM_CompReqAddrOverride:1;
    UINT32                              DVM_B2B_ASIDOnlyInvAll:1;
    UINT32                                        Reserved_7_2:6;
    UINT32                                     DVM_CompReqAddr:16;
    UINT32                                     DVM_SYNC_InvAll:1;
    UINT32                                DVM_DropSEV_ASIDOnly:1;
    UINT32                                    DVM_DropB2B_Sync:1;
    UINT32                                   DVM_Drop_IOMMUDis:1;
    UINT32                                   DVM_TWTrackingDis:1;
    UINT32                                   DVM_FilterL1Mode0:1;
    UINT32                                      Reserved_31_30:2;
  } Field;
  UINT32 Value;
} L2_DVM_CNTRL_STRUCT;

#define SMN_L2_DVM_CNTRL_ADDRESS                                      0x13f01354UL
#define SMN_IOMMU0NBIO0_L2_DVM_CNTRL_ADDRESS                          0x13f01354UL
#define SMN_IOMMU0NBIO1_L2_DVM_CNTRL_ADDRESS                          0x14101354UL
#define SMN_IOMMU1NBIO0_L2_DVM_CNTRL_ADDRESS                          0x14001354UL
#define SMN_IOMMU1NBIO1_L2_DVM_CNTRL_ADDRESS                          0x14201354UL


/***********************************************************
* Register Name : L2_DVM_INV_CASES_CAM
************************************************************/

#define L2_DVM_INV_CASES_CAM_APV0_MODE_CAM_OFFSET              0
#define L2_DVM_INV_CASES_CAM_APV0_MODE_CAM_MASK                0x3

#define L2_DVM_INV_CASES_CAM_APV1_MODE_CAM_OFFSET              2
#define L2_DVM_INV_CASES_CAM_APV1_MODE_CAM_MASK                0xc

#define L2_DVM_INV_CASES_CAM_APV2_MODE_CAM_OFFSET              4
#define L2_DVM_INV_CASES_CAM_APV2_MODE_CAM_MASK                0x30

#define L2_DVM_INV_CASES_CAM_APV3_MODE_CAM_OFFSET              6
#define L2_DVM_INV_CASES_CAM_APV3_MODE_CAM_MASK                0xc0

#define L2_DVM_INV_CASES_CAM_APV4_MODE_CAM_OFFSET              8
#define L2_DVM_INV_CASES_CAM_APV4_MODE_CAM_MASK                0x300

#define L2_DVM_INV_CASES_CAM_APV5_MODE_CAM_OFFSET              10
#define L2_DVM_INV_CASES_CAM_APV5_MODE_CAM_MASK                0xc00

#define L2_DVM_INV_CASES_CAM_APV6_MODE_CAM_OFFSET              12
#define L2_DVM_INV_CASES_CAM_APV6_MODE_CAM_MASK                0x3000

#define L2_DVM_INV_CASES_CAM_APV7_MODE_CAM_OFFSET              14
#define L2_DVM_INV_CASES_CAM_APV7_MODE_CAM_MASK                0xc000

#define L2_DVM_INV_CASES_CAM_Reserved_31_16_OFFSET             16
#define L2_DVM_INV_CASES_CAM_Reserved_31_16_MASK               0xffff0000

typedef union {
  struct {
    UINT32                                       APV0_MODE_CAM:2;
    UINT32                                       APV1_MODE_CAM:2;
    UINT32                                       APV2_MODE_CAM:2;
    UINT32                                       APV3_MODE_CAM:2;
    UINT32                                       APV4_MODE_CAM:2;
    UINT32                                       APV5_MODE_CAM:2;
    UINT32                                       APV6_MODE_CAM:2;
    UINT32                                       APV7_MODE_CAM:2;
    UINT32                                      Reserved_31_16:16;
  } Field;
  UINT32 Value;
} L2_DVM_INV_CASES_CAM_STRUCT;

#define SMN_L2_DVM_INV_CASES_CAM_ADDRESS                              0x13f01364UL
#define SMN_IOMMU0NBIO0_L2_DVM_INV_CASES_CAM_ADDRESS                  0x13f01364UL
#define SMN_IOMMU0NBIO1_L2_DVM_INV_CASES_CAM_ADDRESS                  0x14101364UL
#define SMN_IOMMU1NBIO0_L2_DVM_INV_CASES_CAM_ADDRESS                  0x14001364UL
#define SMN_IOMMU1NBIO1_L2_DVM_INV_CASES_CAM_ADDRESS                  0x14201364UL


/***********************************************************
* Register Name : L2_DVM_INV_CASES_PDC
************************************************************/

#define L2_DVM_INV_CASES_PDC_APV0_MODE_PDC_OFFSET              0
#define L2_DVM_INV_CASES_PDC_APV0_MODE_PDC_MASK                0x3

#define L2_DVM_INV_CASES_PDC_APV1_MODE_PDC_OFFSET              2
#define L2_DVM_INV_CASES_PDC_APV1_MODE_PDC_MASK                0xc

#define L2_DVM_INV_CASES_PDC_APV2_MODE_PDC_OFFSET              4
#define L2_DVM_INV_CASES_PDC_APV2_MODE_PDC_MASK                0x30

#define L2_DVM_INV_CASES_PDC_APV3_MODE_PDC_OFFSET              6
#define L2_DVM_INV_CASES_PDC_APV3_MODE_PDC_MASK                0xc0

#define L2_DVM_INV_CASES_PDC_APV4_MODE_PDC_OFFSET              8
#define L2_DVM_INV_CASES_PDC_APV4_MODE_PDC_MASK                0x300

#define L2_DVM_INV_CASES_PDC_APV5_MODE_PDC_OFFSET              10
#define L2_DVM_INV_CASES_PDC_APV5_MODE_PDC_MASK                0xc00

#define L2_DVM_INV_CASES_PDC_APV6_MODE_PDC_OFFSET              12
#define L2_DVM_INV_CASES_PDC_APV6_MODE_PDC_MASK                0x3000

#define L2_DVM_INV_CASES_PDC_APV7_MODE_PDC_OFFSET              14
#define L2_DVM_INV_CASES_PDC_APV7_MODE_PDC_MASK                0xc000

#define L2_DVM_INV_CASES_PDC_Reserved_31_16_OFFSET             16
#define L2_DVM_INV_CASES_PDC_Reserved_31_16_MASK               0xffff0000

typedef union {
  struct {
    UINT32                                       APV0_MODE_PDC:2;
    UINT32                                       APV1_MODE_PDC:2;
    UINT32                                       APV2_MODE_PDC:2;
    UINT32                                       APV3_MODE_PDC:2;
    UINT32                                       APV4_MODE_PDC:2;
    UINT32                                       APV5_MODE_PDC:2;
    UINT32                                       APV6_MODE_PDC:2;
    UINT32                                       APV7_MODE_PDC:2;
    UINT32                                      Reserved_31_16:16;
  } Field;
  UINT32 Value;
} L2_DVM_INV_CASES_PDC_STRUCT;

#define SMN_L2_DVM_INV_CASES_PDC_ADDRESS                              0x13f0135cUL
#define SMN_IOMMU0NBIO0_L2_DVM_INV_CASES_PDC_ADDRESS                  0x13f0135cUL
#define SMN_IOMMU0NBIO1_L2_DVM_INV_CASES_PDC_ADDRESS                  0x1410135cUL
#define SMN_IOMMU1NBIO0_L2_DVM_INV_CASES_PDC_ADDRESS                  0x1400135cUL
#define SMN_IOMMU1NBIO1_L2_DVM_INV_CASES_PDC_ADDRESS                  0x1420135cUL


/***********************************************************
* Register Name : L2_DVM_INV_CASES_PTC
************************************************************/

#define L2_DVM_INV_CASES_PTC_APV0_MODE_PTC_OFFSET              0
#define L2_DVM_INV_CASES_PTC_APV0_MODE_PTC_MASK                0x3

#define L2_DVM_INV_CASES_PTC_APV1_MODE_PTC_OFFSET              2
#define L2_DVM_INV_CASES_PTC_APV1_MODE_PTC_MASK                0xc

#define L2_DVM_INV_CASES_PTC_APV2_MODE_PTC_OFFSET              4
#define L2_DVM_INV_CASES_PTC_APV2_MODE_PTC_MASK                0x30

#define L2_DVM_INV_CASES_PTC_APV3_MODE_PTC_OFFSET              6
#define L2_DVM_INV_CASES_PTC_APV3_MODE_PTC_MASK                0xc0

#define L2_DVM_INV_CASES_PTC_APV4_MODE_PTC_OFFSET              8
#define L2_DVM_INV_CASES_PTC_APV4_MODE_PTC_MASK                0x300

#define L2_DVM_INV_CASES_PTC_APV5_MODE_PTC_OFFSET              10
#define L2_DVM_INV_CASES_PTC_APV5_MODE_PTC_MASK                0xc00

#define L2_DVM_INV_CASES_PTC_APV6_MODE_PTC_OFFSET              12
#define L2_DVM_INV_CASES_PTC_APV6_MODE_PTC_MASK                0x3000

#define L2_DVM_INV_CASES_PTC_APV7_MODE_PTC_OFFSET              14
#define L2_DVM_INV_CASES_PTC_APV7_MODE_PTC_MASK                0xc000

#define L2_DVM_INV_CASES_PTC_Reserved_31_16_OFFSET             16
#define L2_DVM_INV_CASES_PTC_Reserved_31_16_MASK               0xffff0000

typedef union {
  struct {
    UINT32                                       APV0_MODE_PTC:2;
    UINT32                                       APV1_MODE_PTC:2;
    UINT32                                       APV2_MODE_PTC:2;
    UINT32                                       APV3_MODE_PTC:2;
    UINT32                                       APV4_MODE_PTC:2;
    UINT32                                       APV5_MODE_PTC:2;
    UINT32                                       APV6_MODE_PTC:2;
    UINT32                                       APV7_MODE_PTC:2;
    UINT32                                      Reserved_31_16:16;
  } Field;
  UINT32 Value;
} L2_DVM_INV_CASES_PTC_STRUCT;

#define SMN_L2_DVM_INV_CASES_PTC_ADDRESS                              0x13f01360UL
#define SMN_IOMMU0NBIO0_L2_DVM_INV_CASES_PTC_ADDRESS                  0x13f01360UL
#define SMN_IOMMU0NBIO1_L2_DVM_INV_CASES_PTC_ADDRESS                  0x14101360UL
#define SMN_IOMMU1NBIO0_L2_DVM_INV_CASES_PTC_ADDRESS                  0x14001360UL
#define SMN_IOMMU1NBIO1_L2_DVM_INV_CASES_PTC_ADDRESS                  0x14201360UL


/***********************************************************
* Register Name : L2_ERR_RULE_CONTROL_0
************************************************************/

#define L2_ERR_RULE_CONTROL_0_ERRRuleLock0_OFFSET              0
#define L2_ERR_RULE_CONTROL_0_ERRRuleLock0_MASK                0x1

#define L2_ERR_RULE_CONTROL_0_ERRRuleDisable0_OFFSET           1
#define L2_ERR_RULE_CONTROL_0_ERRRuleDisable0_MASK             0xfffffffe

typedef union {
  struct {
    UINT32                                        ERRRuleLock0:1;
    UINT32                                     ERRRuleDisable0:31;
  } Field;
  UINT32 Value;
} L2_ERR_RULE_CONTROL_0_STRUCT;

#define SMN_L2_ERR_RULE_CONTROL_0_ADDRESS                             0x13f01200UL
#define SMN_IOMMU0NBIO0_L2_ERR_RULE_CONTROL_0_ADDRESS                 0x13f01200UL
#define SMN_IOMMU0NBIO1_L2_ERR_RULE_CONTROL_0_ADDRESS                 0x14101200UL
#define SMN_IOMMU1NBIO0_L2_ERR_RULE_CONTROL_0_ADDRESS                 0x14001200UL
#define SMN_IOMMU1NBIO1_L2_ERR_RULE_CONTROL_0_ADDRESS                 0x14201200UL


/***********************************************************
* Register Name : L2_ERR_RULE_CONTROL_1
************************************************************/

#define L2_ERR_RULE_CONTROL_1_ERRRuleDisable1_OFFSET           0
#define L2_ERR_RULE_CONTROL_1_ERRRuleDisable1_MASK             0xffffffff

typedef union {
  struct {
    UINT32                                     ERRRuleDisable1:32;
  } Field;
  UINT32 Value;
} L2_ERR_RULE_CONTROL_1_STRUCT;

#define SMN_L2_ERR_RULE_CONTROL_1_ADDRESS                             0x13f01204UL
#define SMN_IOMMU0NBIO0_L2_ERR_RULE_CONTROL_1_ADDRESS                 0x13f01204UL
#define SMN_IOMMU0NBIO1_L2_ERR_RULE_CONTROL_1_ADDRESS                 0x14101204UL
#define SMN_IOMMU1NBIO0_L2_ERR_RULE_CONTROL_1_ADDRESS                 0x14001204UL
#define SMN_IOMMU1NBIO1_L2_ERR_RULE_CONTROL_1_ADDRESS                 0x14201204UL


/***********************************************************
* Register Name : L2_ERR_RULE_CONTROL_2
************************************************************/

#define L2_ERR_RULE_CONTROL_2_ERRRuleDisable2_OFFSET           0
#define L2_ERR_RULE_CONTROL_2_ERRRuleDisable2_MASK             0xffffffff

typedef union {
  struct {
    UINT32                                     ERRRuleDisable2:32;
  } Field;
  UINT32 Value;
} L2_ERR_RULE_CONTROL_2_STRUCT;

#define SMN_L2_ERR_RULE_CONTROL_2_ADDRESS                             0x13f01208UL
#define SMN_IOMMU0NBIO0_L2_ERR_RULE_CONTROL_2_ADDRESS                 0x13f01208UL
#define SMN_IOMMU0NBIO1_L2_ERR_RULE_CONTROL_2_ADDRESS                 0x14101208UL
#define SMN_IOMMU1NBIO0_L2_ERR_RULE_CONTROL_2_ADDRESS                 0x14001208UL
#define SMN_IOMMU1NBIO1_L2_ERR_RULE_CONTROL_2_ADDRESS                 0x14201208UL


/***********************************************************
* Register Name : L2_ERR_RULE_CONTROL_3
************************************************************/

#define L2_ERR_RULE_CONTROL_3_ERRRuleLock1_OFFSET              0
#define L2_ERR_RULE_CONTROL_3_ERRRuleLock1_MASK                0x1

#define L2_ERR_RULE_CONTROL_3_Reserved_3_1_OFFSET              1
#define L2_ERR_RULE_CONTROL_3_Reserved_3_1_MASK                0xe

#define L2_ERR_RULE_CONTROL_3_ERRRuleDisable3_OFFSET           4
#define L2_ERR_RULE_CONTROL_3_ERRRuleDisable3_MASK             0xfffffff0

typedef union {
  struct {
    UINT32                                        ERRRuleLock1:1;
    UINT32                                        Reserved_3_1:3;
    UINT32                                     ERRRuleDisable3:28;
  } Field;
  UINT32 Value;
} L2_ERR_RULE_CONTROL_3_STRUCT;

#define SMN_L2_ERR_RULE_CONTROL_3_ADDRESS                             0x157000c0UL
#define SMN_IOMMU0NBIO0_L2_ERR_RULE_CONTROL_3_ADDRESS                 0x157000c0UL
#define SMN_IOMMU0NBIO1_L2_ERR_RULE_CONTROL_3_ADDRESS                 0x159000c0UL
#define SMN_IOMMU1NBIO0_L2_ERR_RULE_CONTROL_3_ADDRESS                 0x158000c0UL
#define SMN_IOMMU1NBIO1_L2_ERR_RULE_CONTROL_3_ADDRESS                 0x15a000c0UL


/***********************************************************
* Register Name : L2_ERR_RULE_CONTROL_4
************************************************************/

#define L2_ERR_RULE_CONTROL_4_ERRRuleDisable4_OFFSET           0
#define L2_ERR_RULE_CONTROL_4_ERRRuleDisable4_MASK             0xffffffff

typedef union {
  struct {
    UINT32                                     ERRRuleDisable4:32;
  } Field;
  UINT32 Value;
} L2_ERR_RULE_CONTROL_4_STRUCT;

#define SMN_L2_ERR_RULE_CONTROL_4_ADDRESS                             0x157000c4UL
#define SMN_IOMMU0NBIO0_L2_ERR_RULE_CONTROL_4_ADDRESS                 0x157000c4UL
#define SMN_IOMMU0NBIO1_L2_ERR_RULE_CONTROL_4_ADDRESS                 0x159000c4UL
#define SMN_IOMMU1NBIO0_L2_ERR_RULE_CONTROL_4_ADDRESS                 0x158000c4UL
#define SMN_IOMMU1NBIO1_L2_ERR_RULE_CONTROL_4_ADDRESS                 0x15a000c4UL


/***********************************************************
* Register Name : L2_ERR_RULE_CONTROL_5
************************************************************/

#define L2_ERR_RULE_CONTROL_5_ERRRuleDisable5_OFFSET           0
#define L2_ERR_RULE_CONTROL_5_ERRRuleDisable5_MASK             0xffffffff

typedef union {
  struct {
    UINT32                                     ERRRuleDisable5:32;
  } Field;
  UINT32 Value;
} L2_ERR_RULE_CONTROL_5_STRUCT;

#define SMN_L2_ERR_RULE_CONTROL_5_ADDRESS                             0x157000c8UL
#define SMN_IOMMU0NBIO0_L2_ERR_RULE_CONTROL_5_ADDRESS                 0x157000c8UL
#define SMN_IOMMU0NBIO1_L2_ERR_RULE_CONTROL_5_ADDRESS                 0x159000c8UL
#define SMN_IOMMU1NBIO0_L2_ERR_RULE_CONTROL_5_ADDRESS                 0x158000c8UL
#define SMN_IOMMU1NBIO1_L2_ERR_RULE_CONTROL_5_ADDRESS                 0x15a000c8UL


/***********************************************************
* Register Name : L2_ERR_RULE_CONTROL_6
************************************************************/

#define L2_ERR_RULE_CONTROL_6_Reserved_0_0_OFFSET              0
#define L2_ERR_RULE_CONTROL_6_Reserved_0_0_MASK                0x1

#define L2_ERR_RULE_CONTROL_6_ERRRuleDisable6_OFFSET           1
#define L2_ERR_RULE_CONTROL_6_ERRRuleDisable6_MASK             0x7ffffffe

#define L2_ERR_RULE_CONTROL_6_Reserved_31_31_OFFSET            31
#define L2_ERR_RULE_CONTROL_6_Reserved_31_31_MASK              0x80000000

typedef union {
  struct {
    UINT32                                        Reserved_0_0:1;
    UINT32                                     ERRRuleDisable6:30;
    UINT32                                      Reserved_31_31:1;
  } Field;
  UINT32 Value;
} L2_ERR_RULE_CONTROL_6_STRUCT;

#define SMN_L2_ERR_RULE_CONTROL_6_ADDRESS                             0x15700118UL
#define SMN_IOMMU0NBIO0_L2_ERR_RULE_CONTROL_6_ADDRESS                 0x15700118UL
#define SMN_IOMMU0NBIO1_L2_ERR_RULE_CONTROL_6_ADDRESS                 0x15900118UL
#define SMN_IOMMU1NBIO0_L2_ERR_RULE_CONTROL_6_ADDRESS                 0x15800118UL
#define SMN_IOMMU1NBIO1_L2_ERR_RULE_CONTROL_6_ADDRESS                 0x15a00118UL


/***********************************************************
* Register Name : L2_ERR_RULE_CONTROL_7
************************************************************/

#define L2_ERR_RULE_CONTROL_7_ERRRuleDisable7_OFFSET           0
#define L2_ERR_RULE_CONTROL_7_ERRRuleDisable7_MASK             0xffffffff

typedef union {
  struct {
    UINT32                                     ERRRuleDisable7:32;
  } Field;
  UINT32 Value;
} L2_ERR_RULE_CONTROL_7_STRUCT;

#define SMN_L2_ERR_RULE_CONTROL_7_ADDRESS                             0x1570011cUL
#define SMN_IOMMU0NBIO0_L2_ERR_RULE_CONTROL_7_ADDRESS                 0x1570011cUL
#define SMN_IOMMU0NBIO1_L2_ERR_RULE_CONTROL_7_ADDRESS                 0x1590011cUL
#define SMN_IOMMU1NBIO0_L2_ERR_RULE_CONTROL_7_ADDRESS                 0x1580011cUL
#define SMN_IOMMU1NBIO1_L2_ERR_RULE_CONTROL_7_ADDRESS                 0x15a0011cUL


/***********************************************************
* Register Name : L2_ERR_RULE_CONTROL_8
************************************************************/

#define L2_ERR_RULE_CONTROL_8_ERRRuleDisable8_OFFSET           0
#define L2_ERR_RULE_CONTROL_8_ERRRuleDisable8_MASK             0xffffffff

typedef union {
  struct {
    UINT32                                     ERRRuleDisable8:32;
  } Field;
  UINT32 Value;
} L2_ERR_RULE_CONTROL_8_STRUCT;

#define SMN_L2_ERR_RULE_CONTROL_8_ADDRESS                             0x15700120UL
#define SMN_IOMMU0NBIO0_L2_ERR_RULE_CONTROL_8_ADDRESS                 0x15700120UL
#define SMN_IOMMU0NBIO1_L2_ERR_RULE_CONTROL_8_ADDRESS                 0x15900120UL
#define SMN_IOMMU1NBIO0_L2_ERR_RULE_CONTROL_8_ADDRESS                 0x15800120UL
#define SMN_IOMMU1NBIO1_L2_ERR_RULE_CONTROL_8_ADDRESS                 0x15a00120UL


/***********************************************************
* Register Name : L2_ITC_CONTROL
************************************************************/

#define L2_ITC_CONTROL_Reserved_2_0_OFFSET                     0
#define L2_ITC_CONTROL_Reserved_2_0_MASK                       0x7

#define L2_ITC_CONTROL_ITCLRUUpdatePri_OFFSET                  3
#define L2_ITC_CONTROL_ITCLRUUpdatePri_MASK                    0x8

#define L2_ITC_CONTROL_ITCParityEn_OFFSET                      4
#define L2_ITC_CONTROL_ITCParityEn_MASK                        0x10

#define L2_ITC_CONTROL_Reserved_7_5_OFFSET                     5
#define L2_ITC_CONTROL_Reserved_7_5_MASK                       0xe0

#define L2_ITC_CONTROL_ITCInvalidationSel_OFFSET               8
#define L2_ITC_CONTROL_ITCInvalidationSel_MASK                 0x300

#define L2_ITC_CONTROL_ITCSoftInvalidate_OFFSET                10
#define L2_ITC_CONTROL_ITCSoftInvalidate_MASK                  0x400

#define L2_ITC_CONTROL_Reserved_12_11_OFFSET                   11
#define L2_ITC_CONTROL_Reserved_12_11_MASK                     0x1800

#define L2_ITC_CONTROL_ITCBypass_OFFSET                        13
#define L2_ITC_CONTROL_ITCBypass_MASK                          0x2000

#define L2_ITC_CONTROL_Reserved_14_14_OFFSET                   14
#define L2_ITC_CONTROL_Reserved_14_14_MASK                     0x4000

#define L2_ITC_CONTROL_ITCParitySupport_OFFSET                 15
#define L2_ITC_CONTROL_ITCParitySupport_MASK                   0x8000

#define L2_ITC_CONTROL_ITCWays_OFFSET                          16
#define L2_ITC_CONTROL_ITCWays_MASK                            0xff0000

#define L2_ITC_CONTROL_Reserved_27_24_OFFSET                   24
#define L2_ITC_CONTROL_Reserved_27_24_MASK                     0xf000000

#define L2_ITC_CONTROL_ITCEntries_OFFSET                       28
#define L2_ITC_CONTROL_ITCEntries_MASK                         0xf0000000

typedef union {
  struct {
    UINT32                                        Reserved_2_0:3;
    UINT32                                     ITCLRUUpdatePri:1;
    UINT32                                         ITCParityEn:1;
    UINT32                                        Reserved_7_5:3;
    UINT32                                  ITCInvalidationSel:2;
    UINT32                                   ITCSoftInvalidate:1;
    UINT32                                      Reserved_12_11:2;
    UINT32                                           ITCBypass:1;
    UINT32                                      Reserved_14_14:1;
    UINT32                                    ITCParitySupport:1;
    UINT32                                             ITCWays:8;
    UINT32                                      Reserved_27_24:4;
    UINT32                                          ITCEntries:4;
  } Field;
  UINT32 Value;
} L2_ITC_CONTROL_STRUCT;

#define SMN_L2_ITC_CONTROL_ADDRESS                                    0x15700050UL
#define SMN_IOMMU0NBIO0_L2_ITC_CONTROL_ADDRESS                        0x15700050UL
#define SMN_IOMMU0NBIO1_L2_ITC_CONTROL_ADDRESS                        0x15900050UL
#define SMN_IOMMU1NBIO0_L2_ITC_CONTROL_ADDRESS                        0x15800050UL
#define SMN_IOMMU1NBIO1_L2_ITC_CONTROL_ADDRESS                        0x15a00050UL


/***********************************************************
* Register Name : L2_ITC_HASH_CONTROL
************************************************************/

#define L2_ITC_HASH_CONTROL_Reserved_15_0_OFFSET               0
#define L2_ITC_HASH_CONTROL_Reserved_15_0_MASK                 0xffff

#define L2_ITC_HASH_CONTROL_ITCAddressMask_OFFSET              16
#define L2_ITC_HASH_CONTROL_ITCAddressMask_MASK                0xffff0000

typedef union {
  struct {
    UINT32                                       Reserved_15_0:16;
    UINT32                                      ITCAddressMask:16;
  } Field;
  UINT32 Value;
} L2_ITC_HASH_CONTROL_STRUCT;

#define SMN_L2_ITC_HASH_CONTROL_ADDRESS                               0x15700054UL
#define SMN_IOMMU0NBIO0_L2_ITC_HASH_CONTROL_ADDRESS                   0x15700054UL
#define SMN_IOMMU0NBIO1_L2_ITC_HASH_CONTROL_ADDRESS                   0x15900054UL
#define SMN_IOMMU1NBIO0_L2_ITC_HASH_CONTROL_ADDRESS                   0x15800054UL
#define SMN_IOMMU1NBIO1_L2_ITC_HASH_CONTROL_ADDRESS                   0x15a00054UL


/***********************************************************
* Register Name : L2_ITC_WAY_CONTROL
************************************************************/

#define L2_ITC_WAY_CONTROL_ITCWayDisable_OFFSET                0
#define L2_ITC_WAY_CONTROL_ITCWayDisable_MASK                  0xffff

#define L2_ITC_WAY_CONTROL_ITCWayAccessDisable_OFFSET          16
#define L2_ITC_WAY_CONTROL_ITCWayAccessDisable_MASK            0xffff0000

typedef union {
  struct {
    UINT32                                       ITCWayDisable:16;
    UINT32                                 ITCWayAccessDisable:16;
  } Field;
  UINT32 Value;
} L2_ITC_WAY_CONTROL_STRUCT;

#define SMN_L2_ITC_WAY_CONTROL_ADDRESS                                0x15700058UL
#define SMN_IOMMU0NBIO0_L2_ITC_WAY_CONTROL_ADDRESS                    0x15700058UL
#define SMN_IOMMU0NBIO1_L2_ITC_WAY_CONTROL_ADDRESS                    0x15900058UL
#define SMN_IOMMU1NBIO0_L2_ITC_WAY_CONTROL_ADDRESS                    0x15800058UL
#define SMN_IOMMU1NBIO1_L2_ITC_WAY_CONTROL_ADDRESS                    0x15a00058UL


/***********************************************************
* Register Name : L2_L2A_CK_GATE_CONTROL
************************************************************/

#define L2_L2A_CK_GATE_CONTROL_CKGateL2ARegsDisable_OFFSET     0
#define L2_L2A_CK_GATE_CONTROL_CKGateL2ARegsDisable_MASK       0x1
#define L2_L2A_CK_GATE_CONTROL_CKGateL2ARegsDisable_DEFAULT     0x0

#define L2_L2A_CK_GATE_CONTROL_CKGateL2ADynamicDisable_OFFSET  1
#define L2_L2A_CK_GATE_CONTROL_CKGateL2ADynamicDisable_MASK    0x2
#define L2_L2A_CK_GATE_CONTROL_CKGateL2ADynamicDisable_DEFAULT     0x0

#define L2_L2A_CK_GATE_CONTROL_CKGateL2ACacheDisable_OFFSET    2
#define L2_L2A_CK_GATE_CONTROL_CKGateL2ACacheDisable_MASK      0x4
#define L2_L2A_CK_GATE_CONTROL_CKGateL2ACacheDisable_DEFAULT     0x0

#define L2_L2A_CK_GATE_CONTROL_CKGateL2APerfDisable_OFFSET     3
#define L2_L2A_CK_GATE_CONTROL_CKGateL2APerfDisable_MASK       0x8

#define L2_L2A_CK_GATE_CONTROL_Reserved_15_4_OFFSET            4
#define L2_L2A_CK_GATE_CONTROL_Reserved_15_4_MASK              0xfff0

#define L2_L2A_CK_GATE_CONTROL_CKGateL2ALength_OFFSET          16
#define L2_L2A_CK_GATE_CONTROL_CKGateL2ALength_MASK            0x30000

#define L2_L2A_CK_GATE_CONTROL_CKGateL2AStop_OFFSET            18
#define L2_L2A_CK_GATE_CONTROL_CKGateL2AStop_MASK              0xc0000

#define L2_L2A_CK_GATE_CONTROL_CKGateL2APortClkDis_OFFSET      20
#define L2_L2A_CK_GATE_CONTROL_CKGateL2APortClkDis_MASK        0x100000

#define L2_L2A_CK_GATE_CONTROL_Reserved_31_21_OFFSET           21
#define L2_L2A_CK_GATE_CONTROL_Reserved_31_21_MASK             0xffe00000

typedef union {
  struct {
    UINT32                                CKGateL2ARegsDisable:1;
    UINT32                             CKGateL2ADynamicDisable:1;
    UINT32                               CKGateL2ACacheDisable:1;
    UINT32                                CKGateL2APerfDisable:1;
    UINT32                                       Reserved_15_4:12;
    UINT32                                     CKGateL2ALength:2;
    UINT32                                       CKGateL2AStop:2;
    UINT32                                 CKGateL2APortClkDis:1;
    UINT32                                      Reserved_31_21:11;
  } Field;
  UINT32 Value;
} L2_L2A_CK_GATE_CONTROL_STRUCT;

#define SMN_L2_L2A_CK_GATE_CONTROL_ADDRESS                            0x157000ccUL
#define SMN_IOMMU0NBIO0_L2_L2A_CK_GATE_CONTROL_ADDRESS                0x157000ccUL
#define SMN_IOMMU0NBIO1_L2_L2A_CK_GATE_CONTROL_ADDRESS                0x159000ccUL
#define SMN_IOMMU1NBIO0_L2_L2A_CK_GATE_CONTROL_ADDRESS                0x158000ccUL
#define SMN_IOMMU1NBIO1_L2_L2A_CK_GATE_CONTROL_ADDRESS                0x15a000ccUL


/***********************************************************
* Register Name : L2_L2A_PGSIZE_CONTROL
************************************************************/

#define L2_L2A_PGSIZE_CONTROL_L2AREG_GST_PGSIZE_OFFSET         0
#define L2_L2A_PGSIZE_CONTROL_L2AREG_GST_PGSIZE_MASK           0x7f

#define L2_L2A_PGSIZE_CONTROL_Reserved_7_7_OFFSET              7
#define L2_L2A_PGSIZE_CONTROL_Reserved_7_7_MASK                0x80

#define L2_L2A_PGSIZE_CONTROL_L2AREG_HOST_PGSIZE_OFFSET        8
#define L2_L2A_PGSIZE_CONTROL_L2AREG_HOST_PGSIZE_MASK          0x7f00

#define L2_L2A_PGSIZE_CONTROL_Reserved_16_15_OFFSET            15
#define L2_L2A_PGSIZE_CONTROL_Reserved_16_15_MASK              0x18000

#define L2_L2A_PGSIZE_CONTROL_L2AREG_PTCSCAN_MODE_OFFSET       17
#define L2_L2A_PGSIZE_CONTROL_L2AREG_PTCSCAN_MODE_MASK         0xe0000

#define L2_L2A_PGSIZE_CONTROL_Reserved_31_20_OFFSET            20
#define L2_L2A_PGSIZE_CONTROL_Reserved_31_20_MASK              0xfff00000

typedef union {
  struct {
    UINT32                                   L2AREG_GST_PGSIZE:7;
    UINT32                                        Reserved_7_7:1;
    UINT32                                  L2AREG_HOST_PGSIZE:7;
    UINT32                                      Reserved_16_15:2;
    UINT32                                 L2AREG_PTCSCAN_MODE:3;
    UINT32                                      Reserved_31_20:12;
  } Field;
  UINT32 Value;
} L2_L2A_PGSIZE_CONTROL_STRUCT;

#define SMN_L2_L2A_PGSIZE_CONTROL_ADDRESS                             0x157000d0UL
#define SMN_IOMMU0NBIO0_L2_L2A_PGSIZE_CONTROL_ADDRESS                 0x157000d0UL
#define SMN_IOMMU0NBIO1_L2_L2A_PGSIZE_CONTROL_ADDRESS                 0x159000d0UL
#define SMN_IOMMU1NBIO0_L2_L2A_PGSIZE_CONTROL_ADDRESS                 0x158000d0UL
#define SMN_IOMMU1NBIO1_L2_L2A_PGSIZE_CONTROL_ADDRESS                 0x15a000d0UL


/***********************************************************
* Register Name : L2_L2B_CK_GATE_CONTROL
************************************************************/

#define L2_L2B_CK_GATE_CONTROL_CKGateL2BRegsDisable_OFFSET     0
#define L2_L2B_CK_GATE_CONTROL_CKGateL2BRegsDisable_MASK       0x1
#define L2_L2B_CK_GATE_CONTROL_CKGateL2BRegsDisable_DEFAULT     0x0

#define L2_L2B_CK_GATE_CONTROL_CKGateL2BDynamicDisable_OFFSET  1
#define L2_L2B_CK_GATE_CONTROL_CKGateL2BDynamicDisable_MASK    0x2
#define L2_L2B_CK_GATE_CONTROL_CKGateL2BDynamicDisable_DEFAULT     0x0

#define L2_L2B_CK_GATE_CONTROL_CKGateL2BMiscDisable_OFFSET     2
#define L2_L2B_CK_GATE_CONTROL_CKGateL2BMiscDisable_MASK       0x4
#define L2_L2B_CK_GATE_CONTROL_CKGateL2BMiscDisable_DEFAULT     0x0

#define L2_L2B_CK_GATE_CONTROL_CKGateL2BCacheDisable_OFFSET    3
#define L2_L2B_CK_GATE_CONTROL_CKGateL2BCacheDisable_MASK      0x8
#define L2_L2B_CK_GATE_CONTROL_CKGateL2BCacheDisable_DEFAULT     0x0

#define L2_L2B_CK_GATE_CONTROL_CKGateL2BAPCDisable_OFFSET      4
#define L2_L2B_CK_GATE_CONTROL_CKGateL2BAPCDisable_MASK        0x10

#define L2_L2B_CK_GATE_CONTROL_CKGateL2BPerfDisable_OFFSET     5
#define L2_L2B_CK_GATE_CONTROL_CKGateL2BPerfDisable_MASK       0x20

#define L2_L2B_CK_GATE_CONTROL_CKGateL2BPortClkDis_OFFSET      6
#define L2_L2B_CK_GATE_CONTROL_CKGateL2BPortClkDis_MASK        0x40

#define L2_L2B_CK_GATE_CONTROL_Reserved_15_7_OFFSET            7
#define L2_L2B_CK_GATE_CONTROL_Reserved_15_7_MASK              0xff80

#define L2_L2B_CK_GATE_CONTROL_CKGateL2BLength_OFFSET          16
#define L2_L2B_CK_GATE_CONTROL_CKGateL2BLength_MASK            0x30000

#define L2_L2B_CK_GATE_CONTROL_CKGateL2BStop_OFFSET            18
#define L2_L2B_CK_GATE_CONTROL_CKGateL2BStop_MASK              0xc0000

#define L2_L2B_CK_GATE_CONTROL_Reserved_31_20_OFFSET           20
#define L2_L2B_CK_GATE_CONTROL_Reserved_31_20_MASK             0xfff00000

typedef union {
  struct {
    UINT32                                CKGateL2BRegsDisable:1;
    UINT32                             CKGateL2BDynamicDisable:1;
    UINT32                                CKGateL2BMiscDisable:1;
    UINT32                               CKGateL2BCacheDisable:1;
    UINT32                                 CKGateL2BAPCDisable:1;
    UINT32                                CKGateL2BPerfDisable:1;
    UINT32                                 CKGateL2BPortClkDis:1;
    UINT32                                       Reserved_15_7:9;
    UINT32                                     CKGateL2BLength:2;
    UINT32                                       CKGateL2BStop:2;
    UINT32                                      Reserved_31_20:12;
  } Field;
  UINT32 Value;
} L2_L2B_CK_GATE_CONTROL_STRUCT;

#define SMN_L2_L2B_CK_GATE_CONTROL_ADDRESS                            0x13f01240UL
#define SMN_IOMMU0NBIO0_L2_L2B_CK_GATE_CONTROL_ADDRESS                0x13f01240UL
#define SMN_IOMMU0NBIO1_L2_L2B_CK_GATE_CONTROL_ADDRESS                0x14101240UL
#define SMN_IOMMU1NBIO0_L2_L2B_CK_GATE_CONTROL_ADDRESS                0x14001240UL
#define SMN_IOMMU1NBIO1_L2_L2B_CK_GATE_CONTROL_ADDRESS                0x14201240UL


/***********************************************************
* Register Name : L2_L2B_PGSIZE_CONTROL
************************************************************/

#define L2_L2B_PGSIZE_CONTROL_L2BREG_GST_PGSIZE_OFFSET         0
#define L2_L2B_PGSIZE_CONTROL_L2BREG_GST_PGSIZE_MASK           0x7f

#define L2_L2B_PGSIZE_CONTROL_Reserved_7_7_OFFSET              7
#define L2_L2B_PGSIZE_CONTROL_Reserved_7_7_MASK                0x80

#define L2_L2B_PGSIZE_CONTROL_L2BREG_HOST_PGSIZE_OFFSET        8
#define L2_L2B_PGSIZE_CONTROL_L2BREG_HOST_PGSIZE_MASK          0x7f00

#define L2_L2B_PGSIZE_CONTROL_Reserved_31_15_OFFSET            15
#define L2_L2B_PGSIZE_CONTROL_Reserved_31_15_MASK              0xffff8000

typedef union {
  struct {
    UINT32                                   L2BREG_GST_PGSIZE:7;
    UINT32                                        Reserved_7_7:1;
    UINT32                                  L2BREG_HOST_PGSIZE:7;
    UINT32                                      Reserved_31_15:17;
  } Field;
  UINT32 Value;
} L2_L2B_PGSIZE_CONTROL_STRUCT;

#define SMN_L2_L2B_PGSIZE_CONTROL_ADDRESS                             0x13f01250UL
#define SMN_IOMMU0NBIO0_L2_L2B_PGSIZE_CONTROL_ADDRESS                 0x13f01250UL
#define SMN_IOMMU0NBIO1_L2_L2B_PGSIZE_CONTROL_ADDRESS                 0x14101250UL
#define SMN_IOMMU1NBIO0_L2_L2B_PGSIZE_CONTROL_ADDRESS                 0x14001250UL
#define SMN_IOMMU1NBIO1_L2_L2B_PGSIZE_CONTROL_ADDRESS                 0x14201250UL


/***********************************************************
* Register Name : L2_MAILBOX_FLUSH_CNTRL
************************************************************/

#define L2_MAILBOX_FLUSH_CNTRL_MailboxFlushCoalesce_OFFSET     0
#define L2_MAILBOX_FLUSH_CNTRL_MailboxFlushCoalesce_MASK       0x1

#define L2_MAILBOX_FLUSH_CNTRL_MailboxFlushSend_IOMMUDis_OFFSET 1
#define L2_MAILBOX_FLUSH_CNTRL_MailboxFlushSend_IOMMUDis_MASK  0x2

#define L2_MAILBOX_FLUSH_CNTRL_MailboxFlushSend_InvallDis_OFFSET 2
#define L2_MAILBOX_FLUSH_CNTRL_MailboxFlushSend_InvallDis_MASK 0x4

#define L2_MAILBOX_FLUSH_CNTRL_Reserved_31_3_OFFSET            3
#define L2_MAILBOX_FLUSH_CNTRL_Reserved_31_3_MASK              0xfffffff8

typedef union {
  struct {
    UINT32                                MailboxFlushCoalesce:1;
    UINT32                           MailboxFlushSend_IOMMUDis:1;
    UINT32                          MailboxFlushSend_InvallDis:1;
    UINT32                                       Reserved_31_3:29;
  } Field;
  UINT32 Value;
} L2_MAILBOX_FLUSH_CNTRL_STRUCT;

#define SMN_L2_MAILBOX_FLUSH_CNTRL_ADDRESS                            0x13f01358UL
#define SMN_IOMMU0NBIO0_L2_MAILBOX_FLUSH_CNTRL_ADDRESS                0x13f01358UL
#define SMN_IOMMU0NBIO1_L2_MAILBOX_FLUSH_CNTRL_ADDRESS                0x14101358UL
#define SMN_IOMMU1NBIO0_L2_MAILBOX_FLUSH_CNTRL_ADDRESS                0x14001358UL
#define SMN_IOMMU1NBIO1_L2_MAILBOX_FLUSH_CNTRL_ADDRESS                0x14201358UL


/***********************************************************
* Register Name : L2_MISC_CNTRL_3
************************************************************/

#define L2_MISC_CNTRL_3_REG_ats_iw_OFFSET                      0
#define L2_MISC_CNTRL_3_REG_ats_iw_MASK                        0x1

#define L2_MISC_CNTRL_3_REG_mask_l2_nw_OFFSET                  1
#define L2_MISC_CNTRL_3_REG_mask_l2_nw_MASK                    0x2

#define L2_MISC_CNTRL_3_REG_atomic_filter_en_OFFSET            2
#define L2_MISC_CNTRL_3_REG_atomic_filter_en_MASK              0x4

#define L2_MISC_CNTRL_3_REG_l1wq_id_el_en_OFFSET               3
#define L2_MISC_CNTRL_3_REG_l1wq_id_el_en_MASK                 0x8

#define L2_MISC_CNTRL_3_REG_PPR_StrictOrder_En_OFFSET          4
#define L2_MISC_CNTRL_3_REG_PPR_StrictOrder_En_MASK            0x10

#define L2_MISC_CNTRL_3_REG_vIOMMU_IntFilter_En_OFFSET         5
#define L2_MISC_CNTRL_3_REG_vIOMMU_IntFilter_En_MASK           0x20

#define L2_MISC_CNTRL_3_REG_guesttw_dte_sd_en_OFFSET           6
#define L2_MISC_CNTRL_3_REG_guesttw_dte_sd_en_MASK             0x40

#define L2_MISC_CNTRL_3_REG_LogATS_iriw_zero_OFFSET            7
#define L2_MISC_CNTRL_3_REG_LogATS_iriw_zero_MASK              0x80

#define L2_MISC_CNTRL_3_REG_PTC_Update_AddrTransReq_OFFSET     8
#define L2_MISC_CNTRL_3_REG_PTC_Update_AddrTransReq_MASK       0x100

#define L2_MISC_CNTRL_3_REG_RstPtrs_on_BaseHiAcc_Dis_OFFSET    9
#define L2_MISC_CNTRL_3_REG_RstPtrs_on_BaseHiAcc_Dis_MASK      0x200

#define L2_MISC_CNTRL_3_REG_RstPtrs_on_BaseLoAcc_Dis_OFFSET    10
#define L2_MISC_CNTRL_3_REG_RstPtrs_on_BaseLoAcc_Dis_MASK      0x400

#define L2_MISC_CNTRL_3_REG_RstPtrs_on_LenAccs_En_OFFSET       11
#define L2_MISC_CNTRL_3_REG_RstPtrs_on_LenAccs_En_MASK         0x800

#define L2_MISC_CNTRL_3_REG_DTEResvBitChkDis_OFFSET            12
#define L2_MISC_CNTRL_3_REG_DTEResvBitChkDis_MASK              0x1000

#define L2_MISC_CNTRL_3_REG_RstGVAPtrs_on_BaseHiAcc_Dis_OFFSET 13
#define L2_MISC_CNTRL_3_REG_RstGVAPtrs_on_BaseHiAcc_Dis_MASK   0x2000

#define L2_MISC_CNTRL_3_REG_RstGVAPtrs_on_BaseLoAcc_Dis_OFFSET 14
#define L2_MISC_CNTRL_3_REG_RstGVAPtrs_on_BaseLoAcc_Dis_MASK   0x4000

#define L2_MISC_CNTRL_3_REG_RstGVAPtrs_on_LenAccs_En_OFFSET    15
#define L2_MISC_CNTRL_3_REG_RstGVAPtrs_on_LenAccs_En_MASK      0x8000

#define L2_MISC_CNTRL_3_REG_VFMMIO_StrictOrder_En_OFFSET       16
#define L2_MISC_CNTRL_3_REG_VFMMIO_StrictOrder_En_MASK         0x10000

#define L2_MISC_CNTRL_3_REG_WaitPtr_WrRsp_OFFSET               17
#define L2_MISC_CNTRL_3_REG_WaitPtr_WrRsp_MASK                 0x20000

#define L2_MISC_CNTRL_3_REG_AllowL1CacheLargePagemode0_OFFSET  18
#define L2_MISC_CNTRL_3_REG_AllowL1CacheLargePagemode0_MASK    0x40000

#define L2_MISC_CNTRL_3_REG_L2_NW_CTRL_OFFSET                  19
#define L2_MISC_CNTRL_3_REG_L2_NW_CTRL_MASK                    0x80000

#define L2_MISC_CNTRL_3_REG_GAPPI_MT_OFFSET                    20
#define L2_MISC_CNTRL_3_REG_GAPPI_MT_MASK                      0xf00000

#define L2_MISC_CNTRL_3_REG_GAPPI_DM_OFFSET                    24
#define L2_MISC_CNTRL_3_REG_GAPPI_DM_MASK                      0x1000000

#define L2_MISC_CNTRL_3_REG_GAPPI_TM_OFFSET                    25
#define L2_MISC_CNTRL_3_REG_GAPPI_TM_MASK                      0x2000000

#define L2_MISC_CNTRL_3_REG_forward_all_dm1_interrupts_num_int_en_OFFSET 26
#define L2_MISC_CNTRL_3_REG_forward_all_dm1_interrupts_num_int_en_MASK 0x4000000

#define L2_MISC_CNTRL_3_Reserved_30_27_OFFSET                  27
#define L2_MISC_CNTRL_3_Reserved_30_27_MASK                    0x78000000

#define L2_MISC_CNTRL_3_REG_gmc_iommu_dis_OFFSET               31
#define L2_MISC_CNTRL_3_REG_gmc_iommu_dis_MASK                 0x80000000

typedef union {
  struct {
    UINT32                                          REG_ats_iw:1;
    UINT32                                      REG_mask_l2_nw:1;
    UINT32                                REG_atomic_filter_en:1;
    UINT32                                   REG_l1wq_id_el_en:1;
    UINT32                              REG_PPR_StrictOrder_En:1;
    UINT32                             REG_vIOMMU_IntFilter_En:1;
    UINT32                               REG_guesttw_dte_sd_en:1;
    UINT32                                REG_LogATS_iriw_zero:1;
    UINT32                         REG_PTC_Update_AddrTransReq:1;
    UINT32                        REG_RstPtrs_on_BaseHiAcc_Dis:1;
    UINT32                        REG_RstPtrs_on_BaseLoAcc_Dis:1;
    UINT32                           REG_RstPtrs_on_LenAccs_En:1;
    UINT32                                REG_DTEResvBitChkDis:1;
    UINT32                     REG_RstGVAPtrs_on_BaseHiAcc_Dis:1;
    UINT32                     REG_RstGVAPtrs_on_BaseLoAcc_Dis:1;
    UINT32                        REG_RstGVAPtrs_on_LenAccs_En:1;
    UINT32                           REG_VFMMIO_StrictOrder_En:1;
    UINT32                                   REG_WaitPtr_WrRsp:1;
    UINT32                      REG_AllowL1CacheLargePagemode0:1;
    UINT32                                      REG_L2_NW_CTRL:1;
    UINT32                                        REG_GAPPI_MT:4;
    UINT32                                        REG_GAPPI_DM:1;
    UINT32                                        REG_GAPPI_TM:1;
    UINT32           REG_forward_all_dm1_interrupts_num_int_en:1;
    UINT32                                      Reserved_30_27:4;
    UINT32                                   REG_gmc_iommu_dis:1;
  } Field;
  UINT32 Value;
} L2_MISC_CNTRL_3_STRUCT;

#define SMN_L2_MISC_CNTRL_3_ADDRESS                                   0x13f0111cUL
#define SMN_IOMMU0NBIO0_L2_MISC_CNTRL_3_ADDRESS                       0x13f0111cUL
#define SMN_IOMMU0NBIO1_L2_MISC_CNTRL_3_ADDRESS                       0x1410111cUL
#define SMN_IOMMU1NBIO0_L2_MISC_CNTRL_3_ADDRESS                       0x1400111cUL
#define SMN_IOMMU1NBIO1_L2_MISC_CNTRL_3_ADDRESS                       0x1420111cUL


/***********************************************************
* Register Name : L2_MULTATS_PTE_GROUP_CNTRL
************************************************************/

#define L2_MULTATS_PTE_GROUP_CNTRL_L1_INTGFX_32K_PTE_GROUP_EN_OFFSET 0
#define L2_MULTATS_PTE_GROUP_CNTRL_L1_INTGFX_32K_PTE_GROUP_EN_MASK 0x1

#define L2_MULTATS_PTE_GROUP_CNTRL_L1_OTHER_32K_PTE_GROUP_EN_OFFSET 1
#define L2_MULTATS_PTE_GROUP_CNTRL_L1_OTHER_32K_PTE_GROUP_EN_MASK 0x2

#define L2_MULTATS_PTE_GROUP_CNTRL_PTE_GROUP_NONATS_EN_OFFSET  2
#define L2_MULTATS_PTE_GROUP_CNTRL_PTE_GROUP_NONATS_EN_MASK    0x4

#define L2_MULTATS_PTE_GROUP_CNTRL_L1_RSP_SPLIT_REQ_PTE_GROUP_OFFSET 3
#define L2_MULTATS_PTE_GROUP_CNTRL_L1_RSP_SPLIT_REQ_PTE_GROUP_MASK 0x8

#define L2_MULTATS_PTE_GROUP_CNTRL_L2_TW_SPLIT_REQ_PTE_GROUP_OFFSET 4
#define L2_MULTATS_PTE_GROUP_CNTRL_L2_TW_SPLIT_REQ_PTE_GROUP_MASK 0x10

#define L2_MULTATS_PTE_GROUP_CNTRL_Reserved_31_5_OFFSET        5
#define L2_MULTATS_PTE_GROUP_CNTRL_Reserved_31_5_MASK          0xffffffe0

typedef union {
  struct {
    UINT32                          L1_INTGFX_32K_PTE_GROUP_EN:1;
    UINT32                           L1_OTHER_32K_PTE_GROUP_EN:1;
    UINT32                                 PTE_GROUP_NONATS_EN:1;
    UINT32                          L1_RSP_SPLIT_REQ_PTE_GROUP:1;
    UINT32                           L2_TW_SPLIT_REQ_PTE_GROUP:1;
    UINT32                                       Reserved_31_5:27;
  } Field;
  UINT32 Value;
} L2_MULTATS_PTE_GROUP_CNTRL_STRUCT;

#define SMN_L2_MULTATS_PTE_GROUP_CNTRL_ADDRESS                        0x13f01310UL
#define SMN_IOMMU0NBIO0_L2_MULTATS_PTE_GROUP_CNTRL_ADDRESS            0x13f01310UL
#define SMN_IOMMU0NBIO1_L2_MULTATS_PTE_GROUP_CNTRL_ADDRESS            0x14101310UL
#define SMN_IOMMU1NBIO0_L2_MULTATS_PTE_GROUP_CNTRL_ADDRESS            0x14001310UL
#define SMN_IOMMU1NBIO1_L2_MULTATS_PTE_GROUP_CNTRL_ADDRESS            0x14201310UL


/***********************************************************
* Register Name : L2_PDC_CONTROL
************************************************************/

#define L2_PDC_CONTROL_Reserved_2_0_OFFSET                     0
#define L2_PDC_CONTROL_Reserved_2_0_MASK                       0x7

#define L2_PDC_CONTROL_PDCLRUUpdatePri_OFFSET                  3
#define L2_PDC_CONTROL_PDCLRUUpdatePri_MASK                    0x8

#define L2_PDC_CONTROL_PDCParityEn_OFFSET                      4
#define L2_PDC_CONTROL_PDCParityEn_MASK                        0x10

#define L2_PDC_CONTROL_Reserved_7_5_OFFSET                     5
#define L2_PDC_CONTROL_Reserved_7_5_MASK                       0xe0

#define L2_PDC_CONTROL_PDCInvalidationSel_OFFSET               8
#define L2_PDC_CONTROL_PDCInvalidationSel_MASK                 0x300

#define L2_PDC_CONTROL_PDCSoftInvalidate_OFFSET                10
#define L2_PDC_CONTROL_PDCSoftInvalidate_MASK                  0x400

#define L2_PDC_CONTROL_PDC_Inv_Overlapping_Pages_OFFSET        11
#define L2_PDC_CONTROL_PDC_Inv_Overlapping_Pages_MASK          0x800

#define L2_PDC_CONTROL_PDCSearchDirection_OFFSET               12
#define L2_PDC_CONTROL_PDCSearchDirection_MASK                 0x1000

#define L2_PDC_CONTROL_PDCBypass_OFFSET                        13
#define L2_PDC_CONTROL_PDCBypass_MASK                          0x2000

#define L2_PDC_CONTROL_PDCModeLookupFix_OFFSET                 14
#define L2_PDC_CONTROL_PDCModeLookupFix_MASK                   0x4000

#define L2_PDC_CONTROL_PDCParitySupport_OFFSET                 15
#define L2_PDC_CONTROL_PDCParitySupport_MASK                   0x8000

#define L2_PDC_CONTROL_PDCWays_OFFSET                          16
#define L2_PDC_CONTROL_PDCWays_MASK                            0xff0000

#define L2_PDC_CONTROL_Reserved_27_24_OFFSET                   24
#define L2_PDC_CONTROL_Reserved_27_24_MASK                     0xf000000

#define L2_PDC_CONTROL_PDCEntries_OFFSET                       28
#define L2_PDC_CONTROL_PDCEntries_MASK                         0xf0000000

typedef union {
  struct {
    UINT32                                        Reserved_2_0:3;
    UINT32                                     PDCLRUUpdatePri:1;
    UINT32                                         PDCParityEn:1;
    UINT32                                        Reserved_7_5:3;
    UINT32                                  PDCInvalidationSel:2;
    UINT32                                   PDCSoftInvalidate:1;
    UINT32                           PDC_Inv_Overlapping_Pages:1;
    UINT32                                  PDCSearchDirection:1;
    UINT32                                           PDCBypass:1;
    UINT32                                    PDCModeLookupFix:1;
    UINT32                                    PDCParitySupport:1;
    UINT32                                             PDCWays:8;
    UINT32                                      Reserved_27_24:4;
    UINT32                                          PDCEntries:4;
  } Field;
  UINT32 Value;
} L2_PDC_CONTROL_STRUCT;

#define SMN_L2_PDC_CONTROL_ADDRESS                                    0x13f01140UL
#define SMN_IOMMU0NBIO0_L2_PDC_CONTROL_ADDRESS                        0x13f01140UL
#define SMN_IOMMU0NBIO1_L2_PDC_CONTROL_ADDRESS                        0x14101140UL
#define SMN_IOMMU1NBIO0_L2_PDC_CONTROL_ADDRESS                        0x14001140UL
#define SMN_IOMMU1NBIO1_L2_PDC_CONTROL_ADDRESS                        0x14201140UL


/***********************************************************
* Register Name : L2_PDC_HASH_CONTROL
************************************************************/

#define L2_PDC_HASH_CONTROL_Reserved_15_0_OFFSET               0
#define L2_PDC_HASH_CONTROL_Reserved_15_0_MASK                 0xffff

#define L2_PDC_HASH_CONTROL_PDCAddressMask_OFFSET              16
#define L2_PDC_HASH_CONTROL_PDCAddressMask_MASK                0xffff0000

typedef union {
  struct {
    UINT32                                       Reserved_15_0:16;
    UINT32                                      PDCAddressMask:16;
  } Field;
  UINT32 Value;
} L2_PDC_HASH_CONTROL_STRUCT;

#define SMN_L2_PDC_HASH_CONTROL_ADDRESS                               0x13f01144UL
#define SMN_IOMMU0NBIO0_L2_PDC_HASH_CONTROL_ADDRESS                   0x13f01144UL
#define SMN_IOMMU0NBIO1_L2_PDC_HASH_CONTROL_ADDRESS                   0x14101144UL
#define SMN_IOMMU1NBIO0_L2_PDC_HASH_CONTROL_ADDRESS                   0x14001144UL
#define SMN_IOMMU1NBIO1_L2_PDC_HASH_CONTROL_ADDRESS                   0x14201144UL


/***********************************************************
* Register Name : L2_PDC_LPT_CONTROL_0
************************************************************/

#define L2_PDC_LPT_CONTROL_0_PDC_Lvl6_Bank_Wr_Sel_OFFSET       0
#define L2_PDC_LPT_CONTROL_0_PDC_Lvl6_Bank_Wr_Sel_MASK         0xff

#define L2_PDC_LPT_CONTROL_0_Reserved_15_8_OFFSET              8
#define L2_PDC_LPT_CONTROL_0_Reserved_15_8_MASK                0xff00

#define L2_PDC_LPT_CONTROL_0_PDC_Lvl5_Bank_Wr_Sel_OFFSET       16
#define L2_PDC_LPT_CONTROL_0_PDC_Lvl5_Bank_Wr_Sel_MASK         0xff0000

#define L2_PDC_LPT_CONTROL_0_Reserved_31_24_OFFSET             24
#define L2_PDC_LPT_CONTROL_0_Reserved_31_24_MASK               0xff000000

typedef union {
  struct {
    UINT32                                PDC_Lvl6_Bank_Wr_Sel:8;
    UINT32                                       Reserved_15_8:8;
    UINT32                                PDC_Lvl5_Bank_Wr_Sel:8;
    UINT32                                      Reserved_31_24:8;
  } Field;
  UINT32 Value;
} L2_PDC_LPT_CONTROL_0_STRUCT;

#define SMN_L2_PDC_LPT_CONTROL_0_ADDRESS                              0x13f0136cUL
#define SMN_IOMMU0NBIO0_L2_PDC_LPT_CONTROL_0_ADDRESS                  0x13f0136cUL
#define SMN_IOMMU0NBIO1_L2_PDC_LPT_CONTROL_0_ADDRESS                  0x1410136cUL
#define SMN_IOMMU1NBIO0_L2_PDC_LPT_CONTROL_0_ADDRESS                  0x1400136cUL
#define SMN_IOMMU1NBIO1_L2_PDC_LPT_CONTROL_0_ADDRESS                  0x1420136cUL


/***********************************************************
* Register Name : L2_PDC_LPT_CONTROL_1
************************************************************/

#define L2_PDC_LPT_CONTROL_1_PDC_Lvl4_Bank_Wr_Sel_OFFSET       0
#define L2_PDC_LPT_CONTROL_1_PDC_Lvl4_Bank_Wr_Sel_MASK         0xff

#define L2_PDC_LPT_CONTROL_1_Reserved_15_8_OFFSET              8
#define L2_PDC_LPT_CONTROL_1_Reserved_15_8_MASK                0xff00

#define L2_PDC_LPT_CONTROL_1_PDC_Lvl3_Bank_Wr_Sel_OFFSET       16
#define L2_PDC_LPT_CONTROL_1_PDC_Lvl3_Bank_Wr_Sel_MASK         0xff0000

#define L2_PDC_LPT_CONTROL_1_Reserved_31_24_OFFSET             24
#define L2_PDC_LPT_CONTROL_1_Reserved_31_24_MASK               0xff000000

typedef union {
  struct {
    UINT32                                PDC_Lvl4_Bank_Wr_Sel:8;
    UINT32                                       Reserved_15_8:8;
    UINT32                                PDC_Lvl3_Bank_Wr_Sel:8;
    UINT32                                      Reserved_31_24:8;
  } Field;
  UINT32 Value;
} L2_PDC_LPT_CONTROL_1_STRUCT;

#define SMN_L2_PDC_LPT_CONTROL_1_ADDRESS                              0x13f01370UL
#define SMN_IOMMU0NBIO0_L2_PDC_LPT_CONTROL_1_ADDRESS                  0x13f01370UL
#define SMN_IOMMU0NBIO1_L2_PDC_LPT_CONTROL_1_ADDRESS                  0x14101370UL
#define SMN_IOMMU1NBIO0_L2_PDC_LPT_CONTROL_1_ADDRESS                  0x14001370UL
#define SMN_IOMMU1NBIO1_L2_PDC_LPT_CONTROL_1_ADDRESS                  0x14201370UL


/***********************************************************
* Register Name : L2_PDC_LPT_CONTROL_10
************************************************************/

#define L2_PDC_LPT_CONTROL_10_PDC_Phase1_Bank7_Page_Size_Sel_OFFSET 0
#define L2_PDC_LPT_CONTROL_10_PDC_Phase1_Bank7_Page_Size_Sel_MASK 0x1f

#define L2_PDC_LPT_CONTROL_10_PDC_Phase2_Bank7_Page_Size_Sel_OFFSET 5
#define L2_PDC_LPT_CONTROL_10_PDC_Phase2_Bank7_Page_Size_Sel_MASK 0x3e0

#define L2_PDC_LPT_CONTROL_10_PDC_Phase3_Bank7_Page_Size_Sel_OFFSET 10
#define L2_PDC_LPT_CONTROL_10_PDC_Phase3_Bank7_Page_Size_Sel_MASK 0x7c00

#define L2_PDC_LPT_CONTROL_10_PDC_Phase4_Bank7_Page_Size_Sel_OFFSET 15
#define L2_PDC_LPT_CONTROL_10_PDC_Phase4_Bank7_Page_Size_Sel_MASK 0xf8000

#define L2_PDC_LPT_CONTROL_10_PDC_Phase5_Bank7_Page_Size_Sel_OFFSET 20
#define L2_PDC_LPT_CONTROL_10_PDC_Phase5_Bank7_Page_Size_Sel_MASK 0x1f00000

#define L2_PDC_LPT_CONTROL_10_Reserved_31_25_OFFSET            25
#define L2_PDC_LPT_CONTROL_10_Reserved_31_25_MASK              0xfe000000

typedef union {
  struct {
    UINT32                      PDC_Phase1_Bank7_Page_Size_Sel:5;
    UINT32                      PDC_Phase2_Bank7_Page_Size_Sel:5;
    UINT32                      PDC_Phase3_Bank7_Page_Size_Sel:5;
    UINT32                      PDC_Phase4_Bank7_Page_Size_Sel:5;
    UINT32                      PDC_Phase5_Bank7_Page_Size_Sel:5;
    UINT32                                      Reserved_31_25:7;
  } Field;
  UINT32 Value;
} L2_PDC_LPT_CONTROL_10_STRUCT;

#define SMN_L2_PDC_LPT_CONTROL_10_ADDRESS                             0x13f01394UL
#define SMN_IOMMU0NBIO0_L2_PDC_LPT_CONTROL_10_ADDRESS                 0x13f01394UL
#define SMN_IOMMU0NBIO1_L2_PDC_LPT_CONTROL_10_ADDRESS                 0x14101394UL
#define SMN_IOMMU1NBIO0_L2_PDC_LPT_CONTROL_10_ADDRESS                 0x14001394UL
#define SMN_IOMMU1NBIO1_L2_PDC_LPT_CONTROL_10_ADDRESS                 0x14201394UL


/***********************************************************
* Register Name : L2_PDC_LPT_CONTROL_2
************************************************************/

#define L2_PDC_LPT_CONTROL_2_PDC_Lvl2_Bank_Wr_Sel_OFFSET       0
#define L2_PDC_LPT_CONTROL_2_PDC_Lvl2_Bank_Wr_Sel_MASK         0xff

#define L2_PDC_LPT_CONTROL_2_Reserved_15_8_OFFSET              8
#define L2_PDC_LPT_CONTROL_2_Reserved_15_8_MASK                0xff00

#define L2_PDC_LPT_CONTROL_2_PDC_Page_Mode_6_Total_Lookup_Phases_OFFSET 16
#define L2_PDC_LPT_CONTROL_2_PDC_Page_Mode_6_Total_Lookup_Phases_MASK 0x70000

#define L2_PDC_LPT_CONTROL_2_PDC_Page_Mode_5_Total_Lookup_Phases_OFFSET 19
#define L2_PDC_LPT_CONTROL_2_PDC_Page_Mode_5_Total_Lookup_Phases_MASK 0x380000

#define L2_PDC_LPT_CONTROL_2_PDC_Page_Mode_4_Total_Lookup_Phases_OFFSET 22
#define L2_PDC_LPT_CONTROL_2_PDC_Page_Mode_4_Total_Lookup_Phases_MASK 0x1c00000

#define L2_PDC_LPT_CONTROL_2_PDC_Page_Mode_3_Total_Lookup_Phases_OFFSET 25
#define L2_PDC_LPT_CONTROL_2_PDC_Page_Mode_3_Total_Lookup_Phases_MASK 0xe000000

#define L2_PDC_LPT_CONTROL_2_PDC_Page_Mode_2_Total_Lookup_Phases_OFFSET 28
#define L2_PDC_LPT_CONTROL_2_PDC_Page_Mode_2_Total_Lookup_Phases_MASK 0x70000000

#define L2_PDC_LPT_CONTROL_2_Reserved_31_31_OFFSET             31
#define L2_PDC_LPT_CONTROL_2_Reserved_31_31_MASK               0x80000000

typedef union {
  struct {
    UINT32                                PDC_Lvl2_Bank_Wr_Sel:8;
    UINT32                                       Reserved_15_8:8;
    UINT32                 PDC_Page_Mode_6_Total_Lookup_Phases:3;
    UINT32                 PDC_Page_Mode_5_Total_Lookup_Phases:3;
    UINT32                 PDC_Page_Mode_4_Total_Lookup_Phases:3;
    UINT32                 PDC_Page_Mode_3_Total_Lookup_Phases:3;
    UINT32                 PDC_Page_Mode_2_Total_Lookup_Phases:3;
    UINT32                                      Reserved_31_31:1;
  } Field;
  UINT32 Value;
} L2_PDC_LPT_CONTROL_2_STRUCT;

#define SMN_L2_PDC_LPT_CONTROL_2_ADDRESS                              0x13f01374UL
#define SMN_IOMMU0NBIO0_L2_PDC_LPT_CONTROL_2_ADDRESS                  0x13f01374UL
#define SMN_IOMMU0NBIO1_L2_PDC_LPT_CONTROL_2_ADDRESS                  0x14101374UL
#define SMN_IOMMU1NBIO0_L2_PDC_LPT_CONTROL_2_ADDRESS                  0x14001374UL
#define SMN_IOMMU1NBIO1_L2_PDC_LPT_CONTROL_2_ADDRESS                  0x14201374UL


/***********************************************************
* Register Name : L2_PDC_LPT_CONTROL_3
************************************************************/

#define L2_PDC_LPT_CONTROL_3_PDC_Phase1_Bank0_Page_Size_Sel_OFFSET 0
#define L2_PDC_LPT_CONTROL_3_PDC_Phase1_Bank0_Page_Size_Sel_MASK 0x1f

#define L2_PDC_LPT_CONTROL_3_PDC_Phase2_Bank0_Page_Size_Sel_OFFSET 5
#define L2_PDC_LPT_CONTROL_3_PDC_Phase2_Bank0_Page_Size_Sel_MASK 0x3e0

#define L2_PDC_LPT_CONTROL_3_PDC_Phase3_Bank0_Page_Size_Sel_OFFSET 10
#define L2_PDC_LPT_CONTROL_3_PDC_Phase3_Bank0_Page_Size_Sel_MASK 0x7c00

#define L2_PDC_LPT_CONTROL_3_PDC_Phase4_Bank0_Page_Size_Sel_OFFSET 15
#define L2_PDC_LPT_CONTROL_3_PDC_Phase4_Bank0_Page_Size_Sel_MASK 0xf8000

#define L2_PDC_LPT_CONTROL_3_PDC_Phase5_Bank0_Page_Size_Sel_OFFSET 20
#define L2_PDC_LPT_CONTROL_3_PDC_Phase5_Bank0_Page_Size_Sel_MASK 0x1f00000

#define L2_PDC_LPT_CONTROL_3_Reserved_31_25_OFFSET             25
#define L2_PDC_LPT_CONTROL_3_Reserved_31_25_MASK               0xfe000000

typedef union {
  struct {
    UINT32                      PDC_Phase1_Bank0_Page_Size_Sel:5;
    UINT32                      PDC_Phase2_Bank0_Page_Size_Sel:5;
    UINT32                      PDC_Phase3_Bank0_Page_Size_Sel:5;
    UINT32                      PDC_Phase4_Bank0_Page_Size_Sel:5;
    UINT32                      PDC_Phase5_Bank0_Page_Size_Sel:5;
    UINT32                                      Reserved_31_25:7;
  } Field;
  UINT32 Value;
} L2_PDC_LPT_CONTROL_3_STRUCT;

#define SMN_L2_PDC_LPT_CONTROL_3_ADDRESS                              0x13f01378UL
#define SMN_IOMMU0NBIO0_L2_PDC_LPT_CONTROL_3_ADDRESS                  0x13f01378UL
#define SMN_IOMMU0NBIO1_L2_PDC_LPT_CONTROL_3_ADDRESS                  0x14101378UL
#define SMN_IOMMU1NBIO0_L2_PDC_LPT_CONTROL_3_ADDRESS                  0x14001378UL
#define SMN_IOMMU1NBIO1_L2_PDC_LPT_CONTROL_3_ADDRESS                  0x14201378UL


/***********************************************************
* Register Name : L2_PDC_LPT_CONTROL_4
************************************************************/

#define L2_PDC_LPT_CONTROL_4_PDC_Phase1_Bank1_Page_Size_Sel_OFFSET 0
#define L2_PDC_LPT_CONTROL_4_PDC_Phase1_Bank1_Page_Size_Sel_MASK 0x1f

#define L2_PDC_LPT_CONTROL_4_PDC_Phase2_Bank1_Page_Size_Sel_OFFSET 5
#define L2_PDC_LPT_CONTROL_4_PDC_Phase2_Bank1_Page_Size_Sel_MASK 0x3e0

#define L2_PDC_LPT_CONTROL_4_PDC_Phase3_Bank1_Page_Size_Sel_OFFSET 10
#define L2_PDC_LPT_CONTROL_4_PDC_Phase3_Bank1_Page_Size_Sel_MASK 0x7c00

#define L2_PDC_LPT_CONTROL_4_PDC_Phase4_Bank1_Page_Size_Sel_OFFSET 15
#define L2_PDC_LPT_CONTROL_4_PDC_Phase4_Bank1_Page_Size_Sel_MASK 0xf8000

#define L2_PDC_LPT_CONTROL_4_PDC_Phase5_Bank1_Page_Size_Sel_OFFSET 20
#define L2_PDC_LPT_CONTROL_4_PDC_Phase5_Bank1_Page_Size_Sel_MASK 0x1f00000

#define L2_PDC_LPT_CONTROL_4_Reserved_31_25_OFFSET             25
#define L2_PDC_LPT_CONTROL_4_Reserved_31_25_MASK               0xfe000000

typedef union {
  struct {
    UINT32                      PDC_Phase1_Bank1_Page_Size_Sel:5;
    UINT32                      PDC_Phase2_Bank1_Page_Size_Sel:5;
    UINT32                      PDC_Phase3_Bank1_Page_Size_Sel:5;
    UINT32                      PDC_Phase4_Bank1_Page_Size_Sel:5;
    UINT32                      PDC_Phase5_Bank1_Page_Size_Sel:5;
    UINT32                                      Reserved_31_25:7;
  } Field;
  UINT32 Value;
} L2_PDC_LPT_CONTROL_4_STRUCT;

#define SMN_L2_PDC_LPT_CONTROL_4_ADDRESS                              0x13f0137cUL
#define SMN_IOMMU0NBIO0_L2_PDC_LPT_CONTROL_4_ADDRESS                  0x13f0137cUL
#define SMN_IOMMU0NBIO1_L2_PDC_LPT_CONTROL_4_ADDRESS                  0x1410137cUL
#define SMN_IOMMU1NBIO0_L2_PDC_LPT_CONTROL_4_ADDRESS                  0x1400137cUL
#define SMN_IOMMU1NBIO1_L2_PDC_LPT_CONTROL_4_ADDRESS                  0x1420137cUL


/***********************************************************
* Register Name : L2_PDC_LPT_CONTROL_5
************************************************************/

#define L2_PDC_LPT_CONTROL_5_PDC_Phase1_Bank2_Page_Size_Sel_OFFSET 0
#define L2_PDC_LPT_CONTROL_5_PDC_Phase1_Bank2_Page_Size_Sel_MASK 0x1f

#define L2_PDC_LPT_CONTROL_5_PDC_Phase2_Bank2_Page_Size_Sel_OFFSET 5
#define L2_PDC_LPT_CONTROL_5_PDC_Phase2_Bank2_Page_Size_Sel_MASK 0x3e0

#define L2_PDC_LPT_CONTROL_5_PDC_Phase3_Bank2_Page_Size_Sel_OFFSET 10
#define L2_PDC_LPT_CONTROL_5_PDC_Phase3_Bank2_Page_Size_Sel_MASK 0x7c00

#define L2_PDC_LPT_CONTROL_5_PDC_Phase4_Bank2_Page_Size_Sel_OFFSET 15
#define L2_PDC_LPT_CONTROL_5_PDC_Phase4_Bank2_Page_Size_Sel_MASK 0xf8000

#define L2_PDC_LPT_CONTROL_5_PDC_Phase5_Bank2_Page_Size_Sel_OFFSET 20
#define L2_PDC_LPT_CONTROL_5_PDC_Phase5_Bank2_Page_Size_Sel_MASK 0x1f00000

#define L2_PDC_LPT_CONTROL_5_Reserved_31_25_OFFSET             25
#define L2_PDC_LPT_CONTROL_5_Reserved_31_25_MASK               0xfe000000

typedef union {
  struct {
    UINT32                      PDC_Phase1_Bank2_Page_Size_Sel:5;
    UINT32                      PDC_Phase2_Bank2_Page_Size_Sel:5;
    UINT32                      PDC_Phase3_Bank2_Page_Size_Sel:5;
    UINT32                      PDC_Phase4_Bank2_Page_Size_Sel:5;
    UINT32                      PDC_Phase5_Bank2_Page_Size_Sel:5;
    UINT32                                      Reserved_31_25:7;
  } Field;
  UINT32 Value;
} L2_PDC_LPT_CONTROL_5_STRUCT;

#define SMN_L2_PDC_LPT_CONTROL_5_ADDRESS                              0x13f01380UL
#define SMN_IOMMU0NBIO0_L2_PDC_LPT_CONTROL_5_ADDRESS                  0x13f01380UL
#define SMN_IOMMU0NBIO1_L2_PDC_LPT_CONTROL_5_ADDRESS                  0x14101380UL
#define SMN_IOMMU1NBIO0_L2_PDC_LPT_CONTROL_5_ADDRESS                  0x14001380UL
#define SMN_IOMMU1NBIO1_L2_PDC_LPT_CONTROL_5_ADDRESS                  0x14201380UL


/***********************************************************
* Register Name : L2_PDC_LPT_CONTROL_6
************************************************************/

#define L2_PDC_LPT_CONTROL_6_PDC_Phase1_Bank3_Page_Size_Sel_OFFSET 0
#define L2_PDC_LPT_CONTROL_6_PDC_Phase1_Bank3_Page_Size_Sel_MASK 0x1f

#define L2_PDC_LPT_CONTROL_6_PDC_Phase2_Bank3_Page_Size_Sel_OFFSET 5
#define L2_PDC_LPT_CONTROL_6_PDC_Phase2_Bank3_Page_Size_Sel_MASK 0x3e0

#define L2_PDC_LPT_CONTROL_6_PDC_Phase3_Bank3_Page_Size_Sel_OFFSET 10
#define L2_PDC_LPT_CONTROL_6_PDC_Phase3_Bank3_Page_Size_Sel_MASK 0x7c00

#define L2_PDC_LPT_CONTROL_6_PDC_Phase4_Bank3_Page_Size_Sel_OFFSET 15
#define L2_PDC_LPT_CONTROL_6_PDC_Phase4_Bank3_Page_Size_Sel_MASK 0xf8000

#define L2_PDC_LPT_CONTROL_6_PDC_Phase5_Bank3_Page_Size_Sel_OFFSET 20
#define L2_PDC_LPT_CONTROL_6_PDC_Phase5_Bank3_Page_Size_Sel_MASK 0x1f00000

#define L2_PDC_LPT_CONTROL_6_Reserved_31_25_OFFSET             25
#define L2_PDC_LPT_CONTROL_6_Reserved_31_25_MASK               0xfe000000

typedef union {
  struct {
    UINT32                      PDC_Phase1_Bank3_Page_Size_Sel:5;
    UINT32                      PDC_Phase2_Bank3_Page_Size_Sel:5;
    UINT32                      PDC_Phase3_Bank3_Page_Size_Sel:5;
    UINT32                      PDC_Phase4_Bank3_Page_Size_Sel:5;
    UINT32                      PDC_Phase5_Bank3_Page_Size_Sel:5;
    UINT32                                      Reserved_31_25:7;
  } Field;
  UINT32 Value;
} L2_PDC_LPT_CONTROL_6_STRUCT;

#define SMN_L2_PDC_LPT_CONTROL_6_ADDRESS                              0x13f01384UL
#define SMN_IOMMU0NBIO0_L2_PDC_LPT_CONTROL_6_ADDRESS                  0x13f01384UL
#define SMN_IOMMU0NBIO1_L2_PDC_LPT_CONTROL_6_ADDRESS                  0x14101384UL
#define SMN_IOMMU1NBIO0_L2_PDC_LPT_CONTROL_6_ADDRESS                  0x14001384UL
#define SMN_IOMMU1NBIO1_L2_PDC_LPT_CONTROL_6_ADDRESS                  0x14201384UL


/***********************************************************
* Register Name : L2_PDC_LPT_CONTROL_7
************************************************************/

#define L2_PDC_LPT_CONTROL_7_PDC_Phase1_Bank4_Page_Size_Sel_OFFSET 0
#define L2_PDC_LPT_CONTROL_7_PDC_Phase1_Bank4_Page_Size_Sel_MASK 0x1f

#define L2_PDC_LPT_CONTROL_7_PDC_Phase2_Bank4_Page_Size_Sel_OFFSET 5
#define L2_PDC_LPT_CONTROL_7_PDC_Phase2_Bank4_Page_Size_Sel_MASK 0x3e0

#define L2_PDC_LPT_CONTROL_7_PDC_Phase3_Bank4_Page_Size_Sel_OFFSET 10
#define L2_PDC_LPT_CONTROL_7_PDC_Phase3_Bank4_Page_Size_Sel_MASK 0x7c00

#define L2_PDC_LPT_CONTROL_7_PDC_Phase4_Bank4_Page_Size_Sel_OFFSET 15
#define L2_PDC_LPT_CONTROL_7_PDC_Phase4_Bank4_Page_Size_Sel_MASK 0xf8000

#define L2_PDC_LPT_CONTROL_7_PDC_Phase5_Bank4_Page_Size_Sel_OFFSET 20
#define L2_PDC_LPT_CONTROL_7_PDC_Phase5_Bank4_Page_Size_Sel_MASK 0x1f00000

#define L2_PDC_LPT_CONTROL_7_Reserved_31_25_OFFSET             25
#define L2_PDC_LPT_CONTROL_7_Reserved_31_25_MASK               0xfe000000

typedef union {
  struct {
    UINT32                      PDC_Phase1_Bank4_Page_Size_Sel:5;
    UINT32                      PDC_Phase2_Bank4_Page_Size_Sel:5;
    UINT32                      PDC_Phase3_Bank4_Page_Size_Sel:5;
    UINT32                      PDC_Phase4_Bank4_Page_Size_Sel:5;
    UINT32                      PDC_Phase5_Bank4_Page_Size_Sel:5;
    UINT32                                      Reserved_31_25:7;
  } Field;
  UINT32 Value;
} L2_PDC_LPT_CONTROL_7_STRUCT;

#define SMN_L2_PDC_LPT_CONTROL_7_ADDRESS                              0x13f01388UL
#define SMN_IOMMU0NBIO0_L2_PDC_LPT_CONTROL_7_ADDRESS                  0x13f01388UL
#define SMN_IOMMU0NBIO1_L2_PDC_LPT_CONTROL_7_ADDRESS                  0x14101388UL
#define SMN_IOMMU1NBIO0_L2_PDC_LPT_CONTROL_7_ADDRESS                  0x14001388UL
#define SMN_IOMMU1NBIO1_L2_PDC_LPT_CONTROL_7_ADDRESS                  0x14201388UL


/***********************************************************
* Register Name : L2_PDC_LPT_CONTROL_8
************************************************************/

#define L2_PDC_LPT_CONTROL_8_PDC_Phase1_Bank5_Page_Size_Sel_OFFSET 0
#define L2_PDC_LPT_CONTROL_8_PDC_Phase1_Bank5_Page_Size_Sel_MASK 0x1f

#define L2_PDC_LPT_CONTROL_8_PDC_Phase2_Bank5_Page_Size_Sel_OFFSET 5
#define L2_PDC_LPT_CONTROL_8_PDC_Phase2_Bank5_Page_Size_Sel_MASK 0x3e0

#define L2_PDC_LPT_CONTROL_8_PDC_Phase3_Bank5_Page_Size_Sel_OFFSET 10
#define L2_PDC_LPT_CONTROL_8_PDC_Phase3_Bank5_Page_Size_Sel_MASK 0x7c00

#define L2_PDC_LPT_CONTROL_8_PDC_Phase4_Bank5_Page_Size_Sel_OFFSET 15
#define L2_PDC_LPT_CONTROL_8_PDC_Phase4_Bank5_Page_Size_Sel_MASK 0xf8000

#define L2_PDC_LPT_CONTROL_8_PDC_Phase5_Bank5_Page_Size_Sel_OFFSET 20
#define L2_PDC_LPT_CONTROL_8_PDC_Phase5_Bank5_Page_Size_Sel_MASK 0x1f00000

#define L2_PDC_LPT_CONTROL_8_Reserved_31_25_OFFSET             25
#define L2_PDC_LPT_CONTROL_8_Reserved_31_25_MASK               0xfe000000

typedef union {
  struct {
    UINT32                      PDC_Phase1_Bank5_Page_Size_Sel:5;
    UINT32                      PDC_Phase2_Bank5_Page_Size_Sel:5;
    UINT32                      PDC_Phase3_Bank5_Page_Size_Sel:5;
    UINT32                      PDC_Phase4_Bank5_Page_Size_Sel:5;
    UINT32                      PDC_Phase5_Bank5_Page_Size_Sel:5;
    UINT32                                      Reserved_31_25:7;
  } Field;
  UINT32 Value;
} L2_PDC_LPT_CONTROL_8_STRUCT;

#define SMN_L2_PDC_LPT_CONTROL_8_ADDRESS                              0x13f0138cUL
#define SMN_IOMMU0NBIO0_L2_PDC_LPT_CONTROL_8_ADDRESS                  0x13f0138cUL
#define SMN_IOMMU0NBIO1_L2_PDC_LPT_CONTROL_8_ADDRESS                  0x1410138cUL
#define SMN_IOMMU1NBIO0_L2_PDC_LPT_CONTROL_8_ADDRESS                  0x1400138cUL
#define SMN_IOMMU1NBIO1_L2_PDC_LPT_CONTROL_8_ADDRESS                  0x1420138cUL


/***********************************************************
* Register Name : L2_PDC_LPT_CONTROL_9
************************************************************/

#define L2_PDC_LPT_CONTROL_9_PDC_Phase1_Bank6_Page_Size_Sel_OFFSET 0
#define L2_PDC_LPT_CONTROL_9_PDC_Phase1_Bank6_Page_Size_Sel_MASK 0x1f

#define L2_PDC_LPT_CONTROL_9_PDC_Phase2_Bank6_Page_Size_Sel_OFFSET 5
#define L2_PDC_LPT_CONTROL_9_PDC_Phase2_Bank6_Page_Size_Sel_MASK 0x3e0

#define L2_PDC_LPT_CONTROL_9_PDC_Phase3_Bank6_Page_Size_Sel_OFFSET 10
#define L2_PDC_LPT_CONTROL_9_PDC_Phase3_Bank6_Page_Size_Sel_MASK 0x7c00

#define L2_PDC_LPT_CONTROL_9_PDC_Phase4_Bank6_Page_Size_Sel_OFFSET 15
#define L2_PDC_LPT_CONTROL_9_PDC_Phase4_Bank6_Page_Size_Sel_MASK 0xf8000

#define L2_PDC_LPT_CONTROL_9_PDC_Phase5_Bank6_Page_Size_Sel_OFFSET 20
#define L2_PDC_LPT_CONTROL_9_PDC_Phase5_Bank6_Page_Size_Sel_MASK 0x1f00000

#define L2_PDC_LPT_CONTROL_9_Reserved_31_25_OFFSET             25
#define L2_PDC_LPT_CONTROL_9_Reserved_31_25_MASK               0xfe000000

typedef union {
  struct {
    UINT32                      PDC_Phase1_Bank6_Page_Size_Sel:5;
    UINT32                      PDC_Phase2_Bank6_Page_Size_Sel:5;
    UINT32                      PDC_Phase3_Bank6_Page_Size_Sel:5;
    UINT32                      PDC_Phase4_Bank6_Page_Size_Sel:5;
    UINT32                      PDC_Phase5_Bank6_Page_Size_Sel:5;
    UINT32                                      Reserved_31_25:7;
  } Field;
  UINT32 Value;
} L2_PDC_LPT_CONTROL_9_STRUCT;

#define SMN_L2_PDC_LPT_CONTROL_9_ADDRESS                              0x13f01390UL
#define SMN_IOMMU0NBIO0_L2_PDC_LPT_CONTROL_9_ADDRESS                  0x13f01390UL
#define SMN_IOMMU0NBIO1_L2_PDC_LPT_CONTROL_9_ADDRESS                  0x14101390UL
#define SMN_IOMMU1NBIO0_L2_PDC_LPT_CONTROL_9_ADDRESS                  0x14001390UL
#define SMN_IOMMU1NBIO1_L2_PDC_LPT_CONTROL_9_ADDRESS                  0x14201390UL


/***********************************************************
* Register Name : L2_PDC_WAY_CONTROL
************************************************************/

#define L2_PDC_WAY_CONTROL_PDCWayDisable_OFFSET                0
#define L2_PDC_WAY_CONTROL_PDCWayDisable_MASK                  0xffff

#define L2_PDC_WAY_CONTROL_PDCWayAccessDisable_OFFSET          16
#define L2_PDC_WAY_CONTROL_PDCWayAccessDisable_MASK            0xffff0000

typedef union {
  struct {
    UINT32                                       PDCWayDisable:16;
    UINT32                                 PDCWayAccessDisable:16;
  } Field;
  UINT32 Value;
} L2_PDC_WAY_CONTROL_STRUCT;

#define SMN_L2_PDC_WAY_CONTROL_ADDRESS                                0x13f01148UL
#define SMN_IOMMU0NBIO0_L2_PDC_WAY_CONTROL_ADDRESS                    0x13f01148UL
#define SMN_IOMMU0NBIO1_L2_PDC_WAY_CONTROL_ADDRESS                    0x14101148UL
#define SMN_IOMMU1NBIO0_L2_PDC_WAY_CONTROL_ADDRESS                    0x14001148UL
#define SMN_IOMMU1NBIO1_L2_PDC_WAY_CONTROL_ADDRESS                    0x14201148UL


/***********************************************************
* Register Name : L2_PM_MPDMA_MAILBOX_HI_0
************************************************************/

#define L2_PM_MPDMA_MAILBOX_HI_0_MAILBOX0_MCM_ADDR_OFFSET      0
#define L2_PM_MPDMA_MAILBOX_HI_0_MAILBOX0_MCM_ADDR_MASK        0xff

#define L2_PM_MPDMA_MAILBOX_HI_0_MAILBOX0_TLB_FLUSH_OFFSET     8
#define L2_PM_MPDMA_MAILBOX_HI_0_MAILBOX0_TLB_FLUSH_MASK       0x100

#define L2_PM_MPDMA_MAILBOX_HI_0_Reserved_23_9_OFFSET          9
#define L2_PM_MPDMA_MAILBOX_HI_0_Reserved_23_9_MASK            0xfffe00

#define L2_PM_MPDMA_MAILBOX_HI_0_MAILBOX0_VC_MASK_OFFSET       24
#define L2_PM_MPDMA_MAILBOX_HI_0_MAILBOX0_VC_MASK_MASK         0xff000000

typedef union {
  struct {
    UINT32                                   MAILBOX0_MCM_ADDR:8;
    UINT32                                  MAILBOX0_TLB_FLUSH:1;
    UINT32                                       Reserved_23_9:15;
    UINT32                                    MAILBOX0_VC_MASK:8;
  } Field;
  UINT32 Value;
} L2_PM_MPDMA_MAILBOX_HI_0_STRUCT;

#define SMN_L2_PM_MPDMA_MAILBOX_HI_0_ADDRESS                          0x13f01318UL
#define SMN_IOMMU0NBIO0_L2_PM_MPDMA_MAILBOX_HI_0_ADDRESS              0x13f01318UL
#define SMN_IOMMU0NBIO1_L2_PM_MPDMA_MAILBOX_HI_0_ADDRESS              0x14101318UL
#define SMN_IOMMU1NBIO0_L2_PM_MPDMA_MAILBOX_HI_0_ADDRESS              0x14001318UL
#define SMN_IOMMU1NBIO1_L2_PM_MPDMA_MAILBOX_HI_0_ADDRESS              0x14201318UL


/***********************************************************
* Register Name : L2_PM_MPDMA_MAILBOX_HI_1
************************************************************/

#define L2_PM_MPDMA_MAILBOX_HI_1_MAILBOX1_MCM_ADDR_OFFSET      0
#define L2_PM_MPDMA_MAILBOX_HI_1_MAILBOX1_MCM_ADDR_MASK        0xff

#define L2_PM_MPDMA_MAILBOX_HI_1_MAILBOX1_TLB_FLUSH_OFFSET     8
#define L2_PM_MPDMA_MAILBOX_HI_1_MAILBOX1_TLB_FLUSH_MASK       0x100

#define L2_PM_MPDMA_MAILBOX_HI_1_Reserved_23_9_OFFSET          9
#define L2_PM_MPDMA_MAILBOX_HI_1_Reserved_23_9_MASK            0xfffe00

#define L2_PM_MPDMA_MAILBOX_HI_1_MAILBOX1_VC_MASK_OFFSET       24
#define L2_PM_MPDMA_MAILBOX_HI_1_MAILBOX1_VC_MASK_MASK         0xff000000

typedef union {
  struct {
    UINT32                                   MAILBOX1_MCM_ADDR:8;
    UINT32                                  MAILBOX1_TLB_FLUSH:1;
    UINT32                                       Reserved_23_9:15;
    UINT32                                    MAILBOX1_VC_MASK:8;
  } Field;
  UINT32 Value;
} L2_PM_MPDMA_MAILBOX_HI_1_STRUCT;

#define SMN_L2_PM_MPDMA_MAILBOX_HI_1_ADDRESS                          0x13f01320UL
#define SMN_IOMMU0NBIO0_L2_PM_MPDMA_MAILBOX_HI_1_ADDRESS              0x13f01320UL
#define SMN_IOMMU0NBIO1_L2_PM_MPDMA_MAILBOX_HI_1_ADDRESS              0x14101320UL
#define SMN_IOMMU1NBIO0_L2_PM_MPDMA_MAILBOX_HI_1_ADDRESS              0x14001320UL
#define SMN_IOMMU1NBIO1_L2_PM_MPDMA_MAILBOX_HI_1_ADDRESS              0x14201320UL


/***********************************************************
* Register Name : L2_PM_MPDMA_MAILBOX_HI_2
************************************************************/

#define L2_PM_MPDMA_MAILBOX_HI_2_MAILBOX2_MCM_ADDR_OFFSET      0
#define L2_PM_MPDMA_MAILBOX_HI_2_MAILBOX2_MCM_ADDR_MASK        0xff

#define L2_PM_MPDMA_MAILBOX_HI_2_MAILBOX2_TLB_FLUSH_OFFSET     8
#define L2_PM_MPDMA_MAILBOX_HI_2_MAILBOX2_TLB_FLUSH_MASK       0x100

#define L2_PM_MPDMA_MAILBOX_HI_2_Reserved_23_9_OFFSET          9
#define L2_PM_MPDMA_MAILBOX_HI_2_Reserved_23_9_MASK            0xfffe00

#define L2_PM_MPDMA_MAILBOX_HI_2_MAILBOX2_VC_MASK_OFFSET       24
#define L2_PM_MPDMA_MAILBOX_HI_2_MAILBOX2_VC_MASK_MASK         0xff000000

typedef union {
  struct {
    UINT32                                   MAILBOX2_MCM_ADDR:8;
    UINT32                                  MAILBOX2_TLB_FLUSH:1;
    UINT32                                       Reserved_23_9:15;
    UINT32                                    MAILBOX2_VC_MASK:8;
  } Field;
  UINT32 Value;
} L2_PM_MPDMA_MAILBOX_HI_2_STRUCT;

#define SMN_L2_PM_MPDMA_MAILBOX_HI_2_ADDRESS                          0x13f01328UL
#define SMN_IOMMU0NBIO0_L2_PM_MPDMA_MAILBOX_HI_2_ADDRESS              0x13f01328UL
#define SMN_IOMMU0NBIO1_L2_PM_MPDMA_MAILBOX_HI_2_ADDRESS              0x14101328UL
#define SMN_IOMMU1NBIO0_L2_PM_MPDMA_MAILBOX_HI_2_ADDRESS              0x14001328UL
#define SMN_IOMMU1NBIO1_L2_PM_MPDMA_MAILBOX_HI_2_ADDRESS              0x14201328UL


/***********************************************************
* Register Name : L2_PM_MPDMA_MAILBOX_HI_3
************************************************************/

#define L2_PM_MPDMA_MAILBOX_HI_3_MAILBOX3_MCM_ADDR_OFFSET      0
#define L2_PM_MPDMA_MAILBOX_HI_3_MAILBOX3_MCM_ADDR_MASK        0xff

#define L2_PM_MPDMA_MAILBOX_HI_3_MAILBOX3_TLB_FLUSH_OFFSET     8
#define L2_PM_MPDMA_MAILBOX_HI_3_MAILBOX3_TLB_FLUSH_MASK       0x100

#define L2_PM_MPDMA_MAILBOX_HI_3_Reserved_23_9_OFFSET          9
#define L2_PM_MPDMA_MAILBOX_HI_3_Reserved_23_9_MASK            0xfffe00

#define L2_PM_MPDMA_MAILBOX_HI_3_MAILBOX3_VC_MASK_OFFSET       24
#define L2_PM_MPDMA_MAILBOX_HI_3_MAILBOX3_VC_MASK_MASK         0xff000000

typedef union {
  struct {
    UINT32                                   MAILBOX3_MCM_ADDR:8;
    UINT32                                  MAILBOX3_TLB_FLUSH:1;
    UINT32                                       Reserved_23_9:15;
    UINT32                                    MAILBOX3_VC_MASK:8;
  } Field;
  UINT32 Value;
} L2_PM_MPDMA_MAILBOX_HI_3_STRUCT;

#define SMN_L2_PM_MPDMA_MAILBOX_HI_3_ADDRESS                          0x13f01330UL
#define SMN_IOMMU0NBIO0_L2_PM_MPDMA_MAILBOX_HI_3_ADDRESS              0x13f01330UL
#define SMN_IOMMU0NBIO1_L2_PM_MPDMA_MAILBOX_HI_3_ADDRESS              0x14101330UL
#define SMN_IOMMU1NBIO0_L2_PM_MPDMA_MAILBOX_HI_3_ADDRESS              0x14001330UL
#define SMN_IOMMU1NBIO1_L2_PM_MPDMA_MAILBOX_HI_3_ADDRESS              0x14201330UL


/***********************************************************
* Register Name : L2_PM_MPDMA_MAILBOX_HI_4
************************************************************/

#define L2_PM_MPDMA_MAILBOX_HI_4_MAILBOX4_MCM_ADDR_OFFSET      0
#define L2_PM_MPDMA_MAILBOX_HI_4_MAILBOX4_MCM_ADDR_MASK        0xff

#define L2_PM_MPDMA_MAILBOX_HI_4_MAILBOX4_TLB_FLUSH_OFFSET     8
#define L2_PM_MPDMA_MAILBOX_HI_4_MAILBOX4_TLB_FLUSH_MASK       0x100

#define L2_PM_MPDMA_MAILBOX_HI_4_Reserved_23_9_OFFSET          9
#define L2_PM_MPDMA_MAILBOX_HI_4_Reserved_23_9_MASK            0xfffe00

#define L2_PM_MPDMA_MAILBOX_HI_4_MAILBOX4_VC_MASK_OFFSET       24
#define L2_PM_MPDMA_MAILBOX_HI_4_MAILBOX4_VC_MASK_MASK         0xff000000

typedef union {
  struct {
    UINT32                                   MAILBOX4_MCM_ADDR:8;
    UINT32                                  MAILBOX4_TLB_FLUSH:1;
    UINT32                                       Reserved_23_9:15;
    UINT32                                    MAILBOX4_VC_MASK:8;
  } Field;
  UINT32 Value;
} L2_PM_MPDMA_MAILBOX_HI_4_STRUCT;

#define SMN_L2_PM_MPDMA_MAILBOX_HI_4_ADDRESS                          0x13f01338UL
#define SMN_IOMMU0NBIO0_L2_PM_MPDMA_MAILBOX_HI_4_ADDRESS              0x13f01338UL
#define SMN_IOMMU0NBIO1_L2_PM_MPDMA_MAILBOX_HI_4_ADDRESS              0x14101338UL
#define SMN_IOMMU1NBIO0_L2_PM_MPDMA_MAILBOX_HI_4_ADDRESS              0x14001338UL
#define SMN_IOMMU1NBIO1_L2_PM_MPDMA_MAILBOX_HI_4_ADDRESS              0x14201338UL


/***********************************************************
* Register Name : L2_PM_MPDMA_MAILBOX_HI_5
************************************************************/

#define L2_PM_MPDMA_MAILBOX_HI_5_MAILBOX5_MCM_ADDR_OFFSET      0
#define L2_PM_MPDMA_MAILBOX_HI_5_MAILBOX5_MCM_ADDR_MASK        0xff

#define L2_PM_MPDMA_MAILBOX_HI_5_MAILBOX5_TLB_FLUSH_OFFSET     8
#define L2_PM_MPDMA_MAILBOX_HI_5_MAILBOX5_TLB_FLUSH_MASK       0x100

#define L2_PM_MPDMA_MAILBOX_HI_5_Reserved_23_9_OFFSET          9
#define L2_PM_MPDMA_MAILBOX_HI_5_Reserved_23_9_MASK            0xfffe00

#define L2_PM_MPDMA_MAILBOX_HI_5_MAILBOX5_VC_MASK_OFFSET       24
#define L2_PM_MPDMA_MAILBOX_HI_5_MAILBOX5_VC_MASK_MASK         0xff000000

typedef union {
  struct {
    UINT32                                   MAILBOX5_MCM_ADDR:8;
    UINT32                                  MAILBOX5_TLB_FLUSH:1;
    UINT32                                       Reserved_23_9:15;
    UINT32                                    MAILBOX5_VC_MASK:8;
  } Field;
  UINT32 Value;
} L2_PM_MPDMA_MAILBOX_HI_5_STRUCT;

#define SMN_L2_PM_MPDMA_MAILBOX_HI_5_ADDRESS                          0x13f01340UL
#define SMN_IOMMU0NBIO0_L2_PM_MPDMA_MAILBOX_HI_5_ADDRESS              0x13f01340UL
#define SMN_IOMMU0NBIO1_L2_PM_MPDMA_MAILBOX_HI_5_ADDRESS              0x14101340UL
#define SMN_IOMMU1NBIO0_L2_PM_MPDMA_MAILBOX_HI_5_ADDRESS              0x14001340UL
#define SMN_IOMMU1NBIO1_L2_PM_MPDMA_MAILBOX_HI_5_ADDRESS              0x14201340UL


/***********************************************************
* Register Name : L2_PM_MPDMA_MAILBOX_HI_6
************************************************************/

#define L2_PM_MPDMA_MAILBOX_HI_6_MAILBOX6_MCM_ADDR_OFFSET      0
#define L2_PM_MPDMA_MAILBOX_HI_6_MAILBOX6_MCM_ADDR_MASK        0xff

#define L2_PM_MPDMA_MAILBOX_HI_6_MAILBOX6_TLB_FLUSH_OFFSET     8
#define L2_PM_MPDMA_MAILBOX_HI_6_MAILBOX6_TLB_FLUSH_MASK       0x100

#define L2_PM_MPDMA_MAILBOX_HI_6_Reserved_23_9_OFFSET          9
#define L2_PM_MPDMA_MAILBOX_HI_6_Reserved_23_9_MASK            0xfffe00

#define L2_PM_MPDMA_MAILBOX_HI_6_MAILBOX6_VC_MASK_OFFSET       24
#define L2_PM_MPDMA_MAILBOX_HI_6_MAILBOX6_VC_MASK_MASK         0xff000000

typedef union {
  struct {
    UINT32                                   MAILBOX6_MCM_ADDR:8;
    UINT32                                  MAILBOX6_TLB_FLUSH:1;
    UINT32                                       Reserved_23_9:15;
    UINT32                                    MAILBOX6_VC_MASK:8;
  } Field;
  UINT32 Value;
} L2_PM_MPDMA_MAILBOX_HI_6_STRUCT;

#define SMN_L2_PM_MPDMA_MAILBOX_HI_6_ADDRESS                          0x13f01348UL
#define SMN_IOMMU0NBIO0_L2_PM_MPDMA_MAILBOX_HI_6_ADDRESS              0x13f01348UL
#define SMN_IOMMU0NBIO1_L2_PM_MPDMA_MAILBOX_HI_6_ADDRESS              0x14101348UL
#define SMN_IOMMU1NBIO0_L2_PM_MPDMA_MAILBOX_HI_6_ADDRESS              0x14001348UL
#define SMN_IOMMU1NBIO1_L2_PM_MPDMA_MAILBOX_HI_6_ADDRESS              0x14201348UL


/***********************************************************
* Register Name : L2_PM_MPDMA_MAILBOX_HI_7
************************************************************/

#define L2_PM_MPDMA_MAILBOX_HI_7_MAILBOX7_MCM_ADDR_OFFSET      0
#define L2_PM_MPDMA_MAILBOX_HI_7_MAILBOX7_MCM_ADDR_MASK        0xff

#define L2_PM_MPDMA_MAILBOX_HI_7_MAILBOX7_TLB_FLUSH_OFFSET     8
#define L2_PM_MPDMA_MAILBOX_HI_7_MAILBOX7_TLB_FLUSH_MASK       0x100

#define L2_PM_MPDMA_MAILBOX_HI_7_Reserved_23_9_OFFSET          9
#define L2_PM_MPDMA_MAILBOX_HI_7_Reserved_23_9_MASK            0xfffe00

#define L2_PM_MPDMA_MAILBOX_HI_7_MAILBOX7_VC_MASK_OFFSET       24
#define L2_PM_MPDMA_MAILBOX_HI_7_MAILBOX7_VC_MASK_MASK         0xff000000

typedef union {
  struct {
    UINT32                                   MAILBOX7_MCM_ADDR:8;
    UINT32                                  MAILBOX7_TLB_FLUSH:1;
    UINT32                                       Reserved_23_9:15;
    UINT32                                    MAILBOX7_VC_MASK:8;
  } Field;
  UINT32 Value;
} L2_PM_MPDMA_MAILBOX_HI_7_STRUCT;

#define SMN_L2_PM_MPDMA_MAILBOX_HI_7_ADDRESS                          0x13f01350UL
#define SMN_IOMMU0NBIO0_L2_PM_MPDMA_MAILBOX_HI_7_ADDRESS              0x13f01350UL
#define SMN_IOMMU0NBIO1_L2_PM_MPDMA_MAILBOX_HI_7_ADDRESS              0x14101350UL
#define SMN_IOMMU1NBIO0_L2_PM_MPDMA_MAILBOX_HI_7_ADDRESS              0x14001350UL
#define SMN_IOMMU1NBIO1_L2_PM_MPDMA_MAILBOX_HI_7_ADDRESS              0x14201350UL


/***********************************************************
* Register Name : L2_PM_MPDMA_MAILBOX_LO_0
************************************************************/

#define L2_PM_MPDMA_MAILBOX_LO_0_Reserved_1_0_OFFSET           0
#define L2_PM_MPDMA_MAILBOX_LO_0_Reserved_1_0_MASK             0x3

#define L2_PM_MPDMA_MAILBOX_LO_0_MAILBOX0_REGISTER_OFFSET_OFFSET 2
#define L2_PM_MPDMA_MAILBOX_LO_0_MAILBOX0_REGISTER_OFFSET_MASK 0xffffc

#define L2_PM_MPDMA_MAILBOX_LO_0_MAILBOX0_APERTURE_ID_OFFSET   20
#define L2_PM_MPDMA_MAILBOX_LO_0_MAILBOX0_APERTURE_ID_MASK     0xfff00000

typedef union {
  struct {
    UINT32                                        Reserved_1_0:2;
    UINT32                            MAILBOX0_REGISTER_OFFSET:18;
    UINT32                                MAILBOX0_APERTURE_ID:12;
  } Field;
  UINT32 Value;
} L2_PM_MPDMA_MAILBOX_LO_0_STRUCT;

#define SMN_L2_PM_MPDMA_MAILBOX_LO_0_ADDRESS                          0x13f01314UL
#define SMN_IOMMU0NBIO0_L2_PM_MPDMA_MAILBOX_LO_0_ADDRESS              0x13f01314UL
#define SMN_IOMMU0NBIO1_L2_PM_MPDMA_MAILBOX_LO_0_ADDRESS              0x14101314UL
#define SMN_IOMMU1NBIO0_L2_PM_MPDMA_MAILBOX_LO_0_ADDRESS              0x14001314UL
#define SMN_IOMMU1NBIO1_L2_PM_MPDMA_MAILBOX_LO_0_ADDRESS              0x14201314UL


/***********************************************************
* Register Name : L2_PM_MPDMA_MAILBOX_LO_1
************************************************************/

#define L2_PM_MPDMA_MAILBOX_LO_1_Reserved_1_0_OFFSET           0
#define L2_PM_MPDMA_MAILBOX_LO_1_Reserved_1_0_MASK             0x3

#define L2_PM_MPDMA_MAILBOX_LO_1_MAILBOX1_REGISTER_OFFSET_OFFSET 2
#define L2_PM_MPDMA_MAILBOX_LO_1_MAILBOX1_REGISTER_OFFSET_MASK 0xffffc

#define L2_PM_MPDMA_MAILBOX_LO_1_MAILBOX1_APERTURE_ID_OFFSET   20
#define L2_PM_MPDMA_MAILBOX_LO_1_MAILBOX1_APERTURE_ID_MASK     0xfff00000

typedef union {
  struct {
    UINT32                                        Reserved_1_0:2;
    UINT32                            MAILBOX1_REGISTER_OFFSET:18;
    UINT32                                MAILBOX1_APERTURE_ID:12;
  } Field;
  UINT32 Value;
} L2_PM_MPDMA_MAILBOX_LO_1_STRUCT;

#define SMN_L2_PM_MPDMA_MAILBOX_LO_1_ADDRESS                          0x13f0131cUL
#define SMN_IOMMU0NBIO0_L2_PM_MPDMA_MAILBOX_LO_1_ADDRESS              0x13f0131cUL
#define SMN_IOMMU0NBIO1_L2_PM_MPDMA_MAILBOX_LO_1_ADDRESS              0x1410131cUL
#define SMN_IOMMU1NBIO0_L2_PM_MPDMA_MAILBOX_LO_1_ADDRESS              0x1400131cUL
#define SMN_IOMMU1NBIO1_L2_PM_MPDMA_MAILBOX_LO_1_ADDRESS              0x1420131cUL


/***********************************************************
* Register Name : L2_PM_MPDMA_MAILBOX_LO_2
************************************************************/

#define L2_PM_MPDMA_MAILBOX_LO_2_Reserved_1_0_OFFSET           0
#define L2_PM_MPDMA_MAILBOX_LO_2_Reserved_1_0_MASK             0x3

#define L2_PM_MPDMA_MAILBOX_LO_2_MAILBOX2_REGISTER_OFFSET_OFFSET 2
#define L2_PM_MPDMA_MAILBOX_LO_2_MAILBOX2_REGISTER_OFFSET_MASK 0xffffc

#define L2_PM_MPDMA_MAILBOX_LO_2_MAILBOX2_APERTURE_ID_OFFSET   20
#define L2_PM_MPDMA_MAILBOX_LO_2_MAILBOX2_APERTURE_ID_MASK     0xfff00000

typedef union {
  struct {
    UINT32                                        Reserved_1_0:2;
    UINT32                            MAILBOX2_REGISTER_OFFSET:18;
    UINT32                                MAILBOX2_APERTURE_ID:12;
  } Field;
  UINT32 Value;
} L2_PM_MPDMA_MAILBOX_LO_2_STRUCT;

#define SMN_L2_PM_MPDMA_MAILBOX_LO_2_ADDRESS                          0x13f01324UL
#define SMN_IOMMU0NBIO0_L2_PM_MPDMA_MAILBOX_LO_2_ADDRESS              0x13f01324UL
#define SMN_IOMMU0NBIO1_L2_PM_MPDMA_MAILBOX_LO_2_ADDRESS              0x14101324UL
#define SMN_IOMMU1NBIO0_L2_PM_MPDMA_MAILBOX_LO_2_ADDRESS              0x14001324UL
#define SMN_IOMMU1NBIO1_L2_PM_MPDMA_MAILBOX_LO_2_ADDRESS              0x14201324UL


/***********************************************************
* Register Name : L2_PM_MPDMA_MAILBOX_LO_3
************************************************************/

#define L2_PM_MPDMA_MAILBOX_LO_3_Reserved_1_0_OFFSET           0
#define L2_PM_MPDMA_MAILBOX_LO_3_Reserved_1_0_MASK             0x3

#define L2_PM_MPDMA_MAILBOX_LO_3_MAILBOX3_REGISTER_OFFSET_OFFSET 2
#define L2_PM_MPDMA_MAILBOX_LO_3_MAILBOX3_REGISTER_OFFSET_MASK 0xffffc

#define L2_PM_MPDMA_MAILBOX_LO_3_MAILBOX3_APERTURE_ID_OFFSET   20
#define L2_PM_MPDMA_MAILBOX_LO_3_MAILBOX3_APERTURE_ID_MASK     0xfff00000

typedef union {
  struct {
    UINT32                                        Reserved_1_0:2;
    UINT32                            MAILBOX3_REGISTER_OFFSET:18;
    UINT32                                MAILBOX3_APERTURE_ID:12;
  } Field;
  UINT32 Value;
} L2_PM_MPDMA_MAILBOX_LO_3_STRUCT;

#define SMN_L2_PM_MPDMA_MAILBOX_LO_3_ADDRESS                          0x13f0132cUL
#define SMN_IOMMU0NBIO0_L2_PM_MPDMA_MAILBOX_LO_3_ADDRESS              0x13f0132cUL
#define SMN_IOMMU0NBIO1_L2_PM_MPDMA_MAILBOX_LO_3_ADDRESS              0x1410132cUL
#define SMN_IOMMU1NBIO0_L2_PM_MPDMA_MAILBOX_LO_3_ADDRESS              0x1400132cUL
#define SMN_IOMMU1NBIO1_L2_PM_MPDMA_MAILBOX_LO_3_ADDRESS              0x1420132cUL


/***********************************************************
* Register Name : L2_PM_MPDMA_MAILBOX_LO_4
************************************************************/

#define L2_PM_MPDMA_MAILBOX_LO_4_Reserved_1_0_OFFSET           0
#define L2_PM_MPDMA_MAILBOX_LO_4_Reserved_1_0_MASK             0x3

#define L2_PM_MPDMA_MAILBOX_LO_4_MAILBOX4_REGISTER_OFFSET_OFFSET 2
#define L2_PM_MPDMA_MAILBOX_LO_4_MAILBOX4_REGISTER_OFFSET_MASK 0xffffc

#define L2_PM_MPDMA_MAILBOX_LO_4_MAILBOX4_APERTURE_ID_OFFSET   20
#define L2_PM_MPDMA_MAILBOX_LO_4_MAILBOX4_APERTURE_ID_MASK     0xfff00000

typedef union {
  struct {
    UINT32                                        Reserved_1_0:2;
    UINT32                            MAILBOX4_REGISTER_OFFSET:18;
    UINT32                                MAILBOX4_APERTURE_ID:12;
  } Field;
  UINT32 Value;
} L2_PM_MPDMA_MAILBOX_LO_4_STRUCT;

#define SMN_L2_PM_MPDMA_MAILBOX_LO_4_ADDRESS                          0x13f01334UL
#define SMN_IOMMU0NBIO0_L2_PM_MPDMA_MAILBOX_LO_4_ADDRESS              0x13f01334UL
#define SMN_IOMMU0NBIO1_L2_PM_MPDMA_MAILBOX_LO_4_ADDRESS              0x14101334UL
#define SMN_IOMMU1NBIO0_L2_PM_MPDMA_MAILBOX_LO_4_ADDRESS              0x14001334UL
#define SMN_IOMMU1NBIO1_L2_PM_MPDMA_MAILBOX_LO_4_ADDRESS              0x14201334UL


/***********************************************************
* Register Name : L2_PM_MPDMA_MAILBOX_LO_5
************************************************************/

#define L2_PM_MPDMA_MAILBOX_LO_5_Reserved_1_0_OFFSET           0
#define L2_PM_MPDMA_MAILBOX_LO_5_Reserved_1_0_MASK             0x3

#define L2_PM_MPDMA_MAILBOX_LO_5_MAILBOX5_REGISTER_OFFSET_OFFSET 2
#define L2_PM_MPDMA_MAILBOX_LO_5_MAILBOX5_REGISTER_OFFSET_MASK 0xffffc

#define L2_PM_MPDMA_MAILBOX_LO_5_MAILBOX5_APERTURE_ID_OFFSET   20
#define L2_PM_MPDMA_MAILBOX_LO_5_MAILBOX5_APERTURE_ID_MASK     0xfff00000

typedef union {
  struct {
    UINT32                                        Reserved_1_0:2;
    UINT32                            MAILBOX5_REGISTER_OFFSET:18;
    UINT32                                MAILBOX5_APERTURE_ID:12;
  } Field;
  UINT32 Value;
} L2_PM_MPDMA_MAILBOX_LO_5_STRUCT;

#define SMN_L2_PM_MPDMA_MAILBOX_LO_5_ADDRESS                          0x13f0133cUL
#define SMN_IOMMU0NBIO0_L2_PM_MPDMA_MAILBOX_LO_5_ADDRESS              0x13f0133cUL
#define SMN_IOMMU0NBIO1_L2_PM_MPDMA_MAILBOX_LO_5_ADDRESS              0x1410133cUL
#define SMN_IOMMU1NBIO0_L2_PM_MPDMA_MAILBOX_LO_5_ADDRESS              0x1400133cUL
#define SMN_IOMMU1NBIO1_L2_PM_MPDMA_MAILBOX_LO_5_ADDRESS              0x1420133cUL


/***********************************************************
* Register Name : L2_PM_MPDMA_MAILBOX_LO_6
************************************************************/

#define L2_PM_MPDMA_MAILBOX_LO_6_Reserved_1_0_OFFSET           0
#define L2_PM_MPDMA_MAILBOX_LO_6_Reserved_1_0_MASK             0x3

#define L2_PM_MPDMA_MAILBOX_LO_6_MAILBOX6_REGISTER_OFFSET_OFFSET 2
#define L2_PM_MPDMA_MAILBOX_LO_6_MAILBOX6_REGISTER_OFFSET_MASK 0xffffc

#define L2_PM_MPDMA_MAILBOX_LO_6_MAILBOX6_APERTURE_ID_OFFSET   20
#define L2_PM_MPDMA_MAILBOX_LO_6_MAILBOX6_APERTURE_ID_MASK     0xfff00000

typedef union {
  struct {
    UINT32                                        Reserved_1_0:2;
    UINT32                            MAILBOX6_REGISTER_OFFSET:18;
    UINT32                                MAILBOX6_APERTURE_ID:12;
  } Field;
  UINT32 Value;
} L2_PM_MPDMA_MAILBOX_LO_6_STRUCT;

#define SMN_L2_PM_MPDMA_MAILBOX_LO_6_ADDRESS                          0x13f01344UL
#define SMN_IOMMU0NBIO0_L2_PM_MPDMA_MAILBOX_LO_6_ADDRESS              0x13f01344UL
#define SMN_IOMMU0NBIO1_L2_PM_MPDMA_MAILBOX_LO_6_ADDRESS              0x14101344UL
#define SMN_IOMMU1NBIO0_L2_PM_MPDMA_MAILBOX_LO_6_ADDRESS              0x14001344UL
#define SMN_IOMMU1NBIO1_L2_PM_MPDMA_MAILBOX_LO_6_ADDRESS              0x14201344UL


/***********************************************************
* Register Name : L2_PM_MPDMA_MAILBOX_LO_7
************************************************************/

#define L2_PM_MPDMA_MAILBOX_LO_7_Reserved_1_0_OFFSET           0
#define L2_PM_MPDMA_MAILBOX_LO_7_Reserved_1_0_MASK             0x3

#define L2_PM_MPDMA_MAILBOX_LO_7_MAILBOX7_REGISTER_OFFSET_OFFSET 2
#define L2_PM_MPDMA_MAILBOX_LO_7_MAILBOX7_REGISTER_OFFSET_MASK 0xffffc

#define L2_PM_MPDMA_MAILBOX_LO_7_MAILBOX7_APERTURE_ID_OFFSET   20
#define L2_PM_MPDMA_MAILBOX_LO_7_MAILBOX7_APERTURE_ID_MASK     0xfff00000

typedef union {
  struct {
    UINT32                                        Reserved_1_0:2;
    UINT32                            MAILBOX7_REGISTER_OFFSET:18;
    UINT32                                MAILBOX7_APERTURE_ID:12;
  } Field;
  UINT32 Value;
} L2_PM_MPDMA_MAILBOX_LO_7_STRUCT;

#define SMN_L2_PM_MPDMA_MAILBOX_LO_7_ADDRESS                          0x13f0134cUL
#define SMN_IOMMU0NBIO0_L2_PM_MPDMA_MAILBOX_LO_7_ADDRESS              0x13f0134cUL
#define SMN_IOMMU0NBIO1_L2_PM_MPDMA_MAILBOX_LO_7_ADDRESS              0x1410134cUL
#define SMN_IOMMU1NBIO0_L2_PM_MPDMA_MAILBOX_LO_7_ADDRESS              0x1400134cUL
#define SMN_IOMMU1NBIO1_L2_PM_MPDMA_MAILBOX_LO_7_ADDRESS              0x1420134cUL


/***********************************************************
* Register Name : L2_PTC_A_CONTROL
************************************************************/

#define L2_PTC_A_CONTROL_Reserved_0_0_OFFSET                   0
#define L2_PTC_A_CONTROL_Reserved_0_0_MASK                     0x1

#define L2_PTC_A_CONTROL_PTCAStoreFinalATSeperate_OFFSET       1
#define L2_PTC_A_CONTROL_PTCAStoreFinalATSeperate_MASK         0x2

#define L2_PTC_A_CONTROL_PTCAStorePartialATSeperate_OFFSET     2
#define L2_PTC_A_CONTROL_PTCAStorePartialATSeperate_MASK       0x4

#define L2_PTC_A_CONTROL_PTCALRUUpdatePri_OFFSET               3
#define L2_PTC_A_CONTROL_PTCALRUUpdatePri_MASK                 0x8

#define L2_PTC_A_CONTROL_PTCAParityEn_OFFSET                   4
#define L2_PTC_A_CONTROL_PTCAParityEn_MASK                     0x10

#define L2_PTC_A_CONTROL_Reserved_7_5_OFFSET                   5
#define L2_PTC_A_CONTROL_Reserved_7_5_MASK                     0xe0

#define L2_PTC_A_CONTROL_PTCAInvalidationSel_OFFSET            8
#define L2_PTC_A_CONTROL_PTCAInvalidationSel_MASK              0x300

#define L2_PTC_A_CONTROL_PTCASoftInvalidate_OFFSET             10
#define L2_PTC_A_CONTROL_PTCASoftInvalidate_MASK               0x400

#define L2_PTC_A_CONTROL_PTCA2MMode_OFFSET                     11
#define L2_PTC_A_CONTROL_PTCA2MMode_MASK                       0x800

#define L2_PTC_A_CONTROL_PCTA_Inv_Overlapping_Pages_OFFSET     12
#define L2_PTC_A_CONTROL_PCTA_Inv_Overlapping_Pages_MASK       0x1000

#define L2_PTC_A_CONTROL_PTCABypass_OFFSET                     13
#define L2_PTC_A_CONTROL_PTCABypass_MASK                       0x2000

#define L2_PTC_A_CONTROL_PTCAFastInvalidateGuest_OFFSET        14
#define L2_PTC_A_CONTROL_PTCAFastInvalidateGuest_MASK          0x4000

#define L2_PTC_A_CONTROL_PTCAParitySupport_OFFSET              15
#define L2_PTC_A_CONTROL_PTCAParitySupport_MASK                0x8000

#define L2_PTC_A_CONTROL_PTCAWays_OFFSET                       16
#define L2_PTC_A_CONTROL_PTCAWays_MASK                         0xff0000

#define L2_PTC_A_CONTROL_Reserved_27_24_OFFSET                 24
#define L2_PTC_A_CONTROL_Reserved_27_24_MASK                   0xf000000

#define L2_PTC_A_CONTROL_PTCAEntries_OFFSET                    28
#define L2_PTC_A_CONTROL_PTCAEntries_MASK                      0xf0000000

typedef union {
  struct {
    UINT32                                        Reserved_0_0:1;
    UINT32                            PTCAStoreFinalATSeperate:1;
    UINT32                          PTCAStorePartialATSeperate:1;
    UINT32                                    PTCALRUUpdatePri:1;
    UINT32                                        PTCAParityEn:1;
    UINT32                                        Reserved_7_5:3;
    UINT32                                 PTCAInvalidationSel:2;
    UINT32                                  PTCASoftInvalidate:1;
    UINT32                                          PTCA2MMode:1;
    UINT32                          PCTA_Inv_Overlapping_Pages:1;
    UINT32                                          PTCABypass:1;
    UINT32                             PTCAFastInvalidateGuest:1;
    UINT32                                   PTCAParitySupport:1;
    UINT32                                            PTCAWays:8;
    UINT32                                      Reserved_27_24:4;
    UINT32                                         PTCAEntries:4;
  } Field;
  UINT32 Value;
} L2_PTC_A_CONTROL_STRUCT;

#define SMN_L2_PTC_A_CONTROL_ADDRESS                                  0x15700060UL
#define SMN_IOMMU0NBIO0_L2_PTC_A_CONTROL_ADDRESS                      0x15700060UL
#define SMN_IOMMU0NBIO1_L2_PTC_A_CONTROL_ADDRESS                      0x15900060UL
#define SMN_IOMMU1NBIO0_L2_PTC_A_CONTROL_ADDRESS                      0x15800060UL
#define SMN_IOMMU1NBIO1_L2_PTC_A_CONTROL_ADDRESS                      0x15a00060UL


/***********************************************************
* Register Name : L2_PTC_A_HASH_CONTROL
************************************************************/

#define L2_PTC_A_HASH_CONTROL_Reserved_15_0_OFFSET             0
#define L2_PTC_A_HASH_CONTROL_Reserved_15_0_MASK               0xffff

#define L2_PTC_A_HASH_CONTROL_PTCAAddressMask_OFFSET           16
#define L2_PTC_A_HASH_CONTROL_PTCAAddressMask_MASK             0xffff0000

typedef union {
  struct {
    UINT32                                       Reserved_15_0:16;
    UINT32                                     PTCAAddressMask:16;
  } Field;
  UINT32 Value;
} L2_PTC_A_HASH_CONTROL_STRUCT;

#define SMN_L2_PTC_A_HASH_CONTROL_ADDRESS                             0x15700064UL
#define SMN_IOMMU0NBIO0_L2_PTC_A_HASH_CONTROL_ADDRESS                 0x15700064UL
#define SMN_IOMMU0NBIO1_L2_PTC_A_HASH_CONTROL_ADDRESS                 0x15900064UL
#define SMN_IOMMU1NBIO0_L2_PTC_A_HASH_CONTROL_ADDRESS                 0x15800064UL
#define SMN_IOMMU1NBIO1_L2_PTC_A_HASH_CONTROL_ADDRESS                 0x15a00064UL


/***********************************************************
* Register Name : L2_PTC_A_WAY_CONTROL
************************************************************/

#define L2_PTC_A_WAY_CONTROL_PTCAWayDisable_OFFSET             0
#define L2_PTC_A_WAY_CONTROL_PTCAWayDisable_MASK               0xffff

#define L2_PTC_A_WAY_CONTROL_PTCAWayAccessDisable_OFFSET       16
#define L2_PTC_A_WAY_CONTROL_PTCAWayAccessDisable_MASK         0xffff0000

typedef union {
  struct {
    UINT32                                      PTCAWayDisable:16;
    UINT32                                PTCAWayAccessDisable:16;
  } Field;
  UINT32 Value;
} L2_PTC_A_WAY_CONTROL_STRUCT;

#define SMN_L2_PTC_A_WAY_CONTROL_ADDRESS                              0x15700068UL
#define SMN_IOMMU0NBIO0_L2_PTC_A_WAY_CONTROL_ADDRESS                  0x15700068UL
#define SMN_IOMMU0NBIO1_L2_PTC_A_WAY_CONTROL_ADDRESS                  0x15900068UL
#define SMN_IOMMU1NBIO0_L2_PTC_A_WAY_CONTROL_ADDRESS                  0x15800068UL
#define SMN_IOMMU1NBIO1_L2_PTC_A_WAY_CONTROL_ADDRESS                  0x15a00068UL


/***********************************************************
* Register Name : L2_PTC_LPT_CONTROL_0
************************************************************/

#define L2_PTC_LPT_CONTROL_0_PTC_4K_Bank_Wr_Sel_OFFSET         0
#define L2_PTC_LPT_CONTROL_0_PTC_4K_Bank_Wr_Sel_MASK           0xff

#define L2_PTC_LPT_CONTROL_0_Reserved_15_8_OFFSET              8
#define L2_PTC_LPT_CONTROL_0_Reserved_15_8_MASK                0xff00

#define L2_PTC_LPT_CONTROL_0_PTC_2M_Bank_Wr_Sel_OFFSET         16
#define L2_PTC_LPT_CONTROL_0_PTC_2M_Bank_Wr_Sel_MASK           0xff0000

#define L2_PTC_LPT_CONTROL_0_Reserved_31_24_OFFSET             24
#define L2_PTC_LPT_CONTROL_0_Reserved_31_24_MASK               0xff000000

typedef union {
  struct {
    UINT32                                  PTC_4K_Bank_Wr_Sel:8;
    UINT32                                       Reserved_15_8:8;
    UINT32                                  PTC_2M_Bank_Wr_Sel:8;
    UINT32                                      Reserved_31_24:8;
  } Field;
  UINT32 Value;
} L2_PTC_LPT_CONTROL_0_STRUCT;

#define SMN_L2_PTC_LPT_CONTROL_0_ADDRESS                              0x15700124UL
#define SMN_IOMMU0NBIO0_L2_PTC_LPT_CONTROL_0_ADDRESS                  0x15700124UL
#define SMN_IOMMU0NBIO1_L2_PTC_LPT_CONTROL_0_ADDRESS                  0x15900124UL
#define SMN_IOMMU1NBIO0_L2_PTC_LPT_CONTROL_0_ADDRESS                  0x15800124UL
#define SMN_IOMMU1NBIO1_L2_PTC_LPT_CONTROL_0_ADDRESS                  0x15a00124UL


/***********************************************************
* Register Name : L2_PTC_LPT_CONTROL_1
************************************************************/

#define L2_PTC_LPT_CONTROL_1_PTC_1G_Bank_Wr_Sel_OFFSET         0
#define L2_PTC_LPT_CONTROL_1_PTC_1G_Bank_Wr_Sel_MASK           0xff

#define L2_PTC_LPT_CONTROL_1_Reserved_15_8_OFFSET              8
#define L2_PTC_LPT_CONTROL_1_Reserved_15_8_MASK                0xff00

#define L2_PTC_LPT_CONTROL_1_PTC_Total_Lookup_Phases_OFFSET    16
#define L2_PTC_LPT_CONTROL_1_PTC_Total_Lookup_Phases_MASK      0x70000

#define L2_PTC_LPT_CONTROL_1_Reserved_31_19_OFFSET             19
#define L2_PTC_LPT_CONTROL_1_Reserved_31_19_MASK               0xfff80000

typedef union {
  struct {
    UINT32                                  PTC_1G_Bank_Wr_Sel:8;
    UINT32                                       Reserved_15_8:8;
    UINT32                             PTC_Total_Lookup_Phases:3;
    UINT32                                      Reserved_31_19:13;
  } Field;
  UINT32 Value;
} L2_PTC_LPT_CONTROL_1_STRUCT;

#define SMN_L2_PTC_LPT_CONTROL_1_ADDRESS                              0x15700128UL
#define SMN_IOMMU0NBIO0_L2_PTC_LPT_CONTROL_1_ADDRESS                  0x15700128UL
#define SMN_IOMMU0NBIO1_L2_PTC_LPT_CONTROL_1_ADDRESS                  0x15900128UL
#define SMN_IOMMU1NBIO0_L2_PTC_LPT_CONTROL_1_ADDRESS                  0x15800128UL
#define SMN_IOMMU1NBIO1_L2_PTC_LPT_CONTROL_1_ADDRESS                  0x15a00128UL


/***********************************************************
* Register Name : L2_PTC_LPT_CONTROL_2
************************************************************/

#define L2_PTC_LPT_CONTROL_2_PTC_Phase1_Bank0_Page_Size_Sel_OFFSET 0
#define L2_PTC_LPT_CONTROL_2_PTC_Phase1_Bank0_Page_Size_Sel_MASK 0x1f

#define L2_PTC_LPT_CONTROL_2_PTC_Phase2_Bank0_Page_Size_Sel_OFFSET 5
#define L2_PTC_LPT_CONTROL_2_PTC_Phase2_Bank0_Page_Size_Sel_MASK 0x3e0

#define L2_PTC_LPT_CONTROL_2_PTC_Phase3_Bank0_Page_Size_Sel_OFFSET 10
#define L2_PTC_LPT_CONTROL_2_PTC_Phase3_Bank0_Page_Size_Sel_MASK 0x7c00

#define L2_PTC_LPT_CONTROL_2_Reserved_31_15_OFFSET             15
#define L2_PTC_LPT_CONTROL_2_Reserved_31_15_MASK               0xffff8000

typedef union {
  struct {
    UINT32                      PTC_Phase1_Bank0_Page_Size_Sel:5;
    UINT32                      PTC_Phase2_Bank0_Page_Size_Sel:5;
    UINT32                      PTC_Phase3_Bank0_Page_Size_Sel:5;
    UINT32                                      Reserved_31_15:17;
  } Field;
  UINT32 Value;
} L2_PTC_LPT_CONTROL_2_STRUCT;

#define SMN_L2_PTC_LPT_CONTROL_2_ADDRESS                              0x1570012cUL
#define SMN_IOMMU0NBIO0_L2_PTC_LPT_CONTROL_2_ADDRESS                  0x1570012cUL
#define SMN_IOMMU0NBIO1_L2_PTC_LPT_CONTROL_2_ADDRESS                  0x1590012cUL
#define SMN_IOMMU1NBIO0_L2_PTC_LPT_CONTROL_2_ADDRESS                  0x1580012cUL
#define SMN_IOMMU1NBIO1_L2_PTC_LPT_CONTROL_2_ADDRESS                  0x15a0012cUL


/***********************************************************
* Register Name : L2_PTC_LPT_CONTROL_3
************************************************************/

#define L2_PTC_LPT_CONTROL_3_PTC_Phase1_Bank1_Page_Size_Sel_OFFSET 0
#define L2_PTC_LPT_CONTROL_3_PTC_Phase1_Bank1_Page_Size_Sel_MASK 0x1f

#define L2_PTC_LPT_CONTROL_3_PTC_Phase2_Bank1_Page_Size_Sel_OFFSET 5
#define L2_PTC_LPT_CONTROL_3_PTC_Phase2_Bank1_Page_Size_Sel_MASK 0x3e0

#define L2_PTC_LPT_CONTROL_3_PTC_Phase3_Bank1_Page_Size_Sel_OFFSET 10
#define L2_PTC_LPT_CONTROL_3_PTC_Phase3_Bank1_Page_Size_Sel_MASK 0x7c00

#define L2_PTC_LPT_CONTROL_3_Reserved_31_15_OFFSET             15
#define L2_PTC_LPT_CONTROL_3_Reserved_31_15_MASK               0xffff8000

typedef union {
  struct {
    UINT32                      PTC_Phase1_Bank1_Page_Size_Sel:5;
    UINT32                      PTC_Phase2_Bank1_Page_Size_Sel:5;
    UINT32                      PTC_Phase3_Bank1_Page_Size_Sel:5;
    UINT32                                      Reserved_31_15:17;
  } Field;
  UINT32 Value;
} L2_PTC_LPT_CONTROL_3_STRUCT;

#define SMN_L2_PTC_LPT_CONTROL_3_ADDRESS                              0x15700130UL
#define SMN_IOMMU0NBIO0_L2_PTC_LPT_CONTROL_3_ADDRESS                  0x15700130UL
#define SMN_IOMMU0NBIO1_L2_PTC_LPT_CONTROL_3_ADDRESS                  0x15900130UL
#define SMN_IOMMU1NBIO0_L2_PTC_LPT_CONTROL_3_ADDRESS                  0x15800130UL
#define SMN_IOMMU1NBIO1_L2_PTC_LPT_CONTROL_3_ADDRESS                  0x15a00130UL


/***********************************************************
* Register Name : L2_PTC_LPT_CONTROL_4
************************************************************/

#define L2_PTC_LPT_CONTROL_4_PTC_Phase1_Bank2_Page_Size_Sel_OFFSET 0
#define L2_PTC_LPT_CONTROL_4_PTC_Phase1_Bank2_Page_Size_Sel_MASK 0x1f

#define L2_PTC_LPT_CONTROL_4_PTC_Phase2_Bank2_Page_Size_Sel_OFFSET 5
#define L2_PTC_LPT_CONTROL_4_PTC_Phase2_Bank2_Page_Size_Sel_MASK 0x3e0

#define L2_PTC_LPT_CONTROL_4_PTC_Phase3_Bank2_Page_Size_Sel_OFFSET 10
#define L2_PTC_LPT_CONTROL_4_PTC_Phase3_Bank2_Page_Size_Sel_MASK 0x7c00

#define L2_PTC_LPT_CONTROL_4_Reserved_31_15_OFFSET             15
#define L2_PTC_LPT_CONTROL_4_Reserved_31_15_MASK               0xffff8000

typedef union {
  struct {
    UINT32                      PTC_Phase1_Bank2_Page_Size_Sel:5;
    UINT32                      PTC_Phase2_Bank2_Page_Size_Sel:5;
    UINT32                      PTC_Phase3_Bank2_Page_Size_Sel:5;
    UINT32                                      Reserved_31_15:17;
  } Field;
  UINT32 Value;
} L2_PTC_LPT_CONTROL_4_STRUCT;

#define SMN_L2_PTC_LPT_CONTROL_4_ADDRESS                              0x15700134UL
#define SMN_IOMMU0NBIO0_L2_PTC_LPT_CONTROL_4_ADDRESS                  0x15700134UL
#define SMN_IOMMU0NBIO1_L2_PTC_LPT_CONTROL_4_ADDRESS                  0x15900134UL
#define SMN_IOMMU1NBIO0_L2_PTC_LPT_CONTROL_4_ADDRESS                  0x15800134UL
#define SMN_IOMMU1NBIO1_L2_PTC_LPT_CONTROL_4_ADDRESS                  0x15a00134UL


/***********************************************************
* Register Name : L2_PTC_LPT_CONTROL_5
************************************************************/

#define L2_PTC_LPT_CONTROL_5_PTC_Phase1_Bank3_Page_Size_Sel_OFFSET 0
#define L2_PTC_LPT_CONTROL_5_PTC_Phase1_Bank3_Page_Size_Sel_MASK 0x1f

#define L2_PTC_LPT_CONTROL_5_PTC_Phase2_Bank3_Page_Size_Sel_OFFSET 5
#define L2_PTC_LPT_CONTROL_5_PTC_Phase2_Bank3_Page_Size_Sel_MASK 0x3e0

#define L2_PTC_LPT_CONTROL_5_PTC_Phase3_Bank3_Page_Size_Sel_OFFSET 10
#define L2_PTC_LPT_CONTROL_5_PTC_Phase3_Bank3_Page_Size_Sel_MASK 0x7c00

#define L2_PTC_LPT_CONTROL_5_Reserved_31_15_OFFSET             15
#define L2_PTC_LPT_CONTROL_5_Reserved_31_15_MASK               0xffff8000

typedef union {
  struct {
    UINT32                      PTC_Phase1_Bank3_Page_Size_Sel:5;
    UINT32                      PTC_Phase2_Bank3_Page_Size_Sel:5;
    UINT32                      PTC_Phase3_Bank3_Page_Size_Sel:5;
    UINT32                                      Reserved_31_15:17;
  } Field;
  UINT32 Value;
} L2_PTC_LPT_CONTROL_5_STRUCT;

#define SMN_L2_PTC_LPT_CONTROL_5_ADDRESS                              0x15700138UL
#define SMN_IOMMU0NBIO0_L2_PTC_LPT_CONTROL_5_ADDRESS                  0x15700138UL
#define SMN_IOMMU0NBIO1_L2_PTC_LPT_CONTROL_5_ADDRESS                  0x15900138UL
#define SMN_IOMMU1NBIO0_L2_PTC_LPT_CONTROL_5_ADDRESS                  0x15800138UL
#define SMN_IOMMU1NBIO1_L2_PTC_LPT_CONTROL_5_ADDRESS                  0x15a00138UL


/***********************************************************
* Register Name : L2_PTC_LPT_CONTROL_6
************************************************************/

#define L2_PTC_LPT_CONTROL_6_PTC_Phase1_Bank4_Page_Size_Sel_OFFSET 0
#define L2_PTC_LPT_CONTROL_6_PTC_Phase1_Bank4_Page_Size_Sel_MASK 0x1f

#define L2_PTC_LPT_CONTROL_6_PTC_Phase2_Bank4_Page_Size_Sel_OFFSET 5
#define L2_PTC_LPT_CONTROL_6_PTC_Phase2_Bank4_Page_Size_Sel_MASK 0x3e0

#define L2_PTC_LPT_CONTROL_6_PTC_Phase3_Bank4_Page_Size_Sel_OFFSET 10
#define L2_PTC_LPT_CONTROL_6_PTC_Phase3_Bank4_Page_Size_Sel_MASK 0x7c00

#define L2_PTC_LPT_CONTROL_6_Reserved_31_15_OFFSET             15
#define L2_PTC_LPT_CONTROL_6_Reserved_31_15_MASK               0xffff8000

typedef union {
  struct {
    UINT32                      PTC_Phase1_Bank4_Page_Size_Sel:5;
    UINT32                      PTC_Phase2_Bank4_Page_Size_Sel:5;
    UINT32                      PTC_Phase3_Bank4_Page_Size_Sel:5;
    UINT32                                      Reserved_31_15:17;
  } Field;
  UINT32 Value;
} L2_PTC_LPT_CONTROL_6_STRUCT;

#define SMN_L2_PTC_LPT_CONTROL_6_ADDRESS                              0x1570013cUL
#define SMN_IOMMU0NBIO0_L2_PTC_LPT_CONTROL_6_ADDRESS                  0x1570013cUL
#define SMN_IOMMU0NBIO1_L2_PTC_LPT_CONTROL_6_ADDRESS                  0x1590013cUL
#define SMN_IOMMU1NBIO0_L2_PTC_LPT_CONTROL_6_ADDRESS                  0x1580013cUL
#define SMN_IOMMU1NBIO1_L2_PTC_LPT_CONTROL_6_ADDRESS                  0x15a0013cUL


/***********************************************************
* Register Name : L2_PTC_LPT_CONTROL_7
************************************************************/

#define L2_PTC_LPT_CONTROL_7_PTC_Phase1_Bank5_Page_Size_Sel_OFFSET 0
#define L2_PTC_LPT_CONTROL_7_PTC_Phase1_Bank5_Page_Size_Sel_MASK 0x1f

#define L2_PTC_LPT_CONTROL_7_PTC_Phase2_Bank5_Page_Size_Sel_OFFSET 5
#define L2_PTC_LPT_CONTROL_7_PTC_Phase2_Bank5_Page_Size_Sel_MASK 0x3e0

#define L2_PTC_LPT_CONTROL_7_PTC_Phase3_Bank5_Page_Size_Sel_OFFSET 10
#define L2_PTC_LPT_CONTROL_7_PTC_Phase3_Bank5_Page_Size_Sel_MASK 0x7c00

#define L2_PTC_LPT_CONTROL_7_Reserved_31_15_OFFSET             15
#define L2_PTC_LPT_CONTROL_7_Reserved_31_15_MASK               0xffff8000

typedef union {
  struct {
    UINT32                      PTC_Phase1_Bank5_Page_Size_Sel:5;
    UINT32                      PTC_Phase2_Bank5_Page_Size_Sel:5;
    UINT32                      PTC_Phase3_Bank5_Page_Size_Sel:5;
    UINT32                                      Reserved_31_15:17;
  } Field;
  UINT32 Value;
} L2_PTC_LPT_CONTROL_7_STRUCT;

#define SMN_L2_PTC_LPT_CONTROL_7_ADDRESS                              0x15700140UL
#define SMN_IOMMU0NBIO0_L2_PTC_LPT_CONTROL_7_ADDRESS                  0x15700140UL
#define SMN_IOMMU0NBIO1_L2_PTC_LPT_CONTROL_7_ADDRESS                  0x15900140UL
#define SMN_IOMMU1NBIO0_L2_PTC_LPT_CONTROL_7_ADDRESS                  0x15800140UL
#define SMN_IOMMU1NBIO1_L2_PTC_LPT_CONTROL_7_ADDRESS                  0x15a00140UL


/***********************************************************
* Register Name : L2_PTC_LPT_CONTROL_8
************************************************************/

#define L2_PTC_LPT_CONTROL_8_PTC_Phase1_Bank6_Page_Size_Sel_OFFSET 0
#define L2_PTC_LPT_CONTROL_8_PTC_Phase1_Bank6_Page_Size_Sel_MASK 0x1f

#define L2_PTC_LPT_CONTROL_8_PTC_Phase2_Bank6_Page_Size_Sel_OFFSET 5
#define L2_PTC_LPT_CONTROL_8_PTC_Phase2_Bank6_Page_Size_Sel_MASK 0x3e0

#define L2_PTC_LPT_CONTROL_8_PTC_Phase3_Bank6_Page_Size_Sel_OFFSET 10
#define L2_PTC_LPT_CONTROL_8_PTC_Phase3_Bank6_Page_Size_Sel_MASK 0x7c00

#define L2_PTC_LPT_CONTROL_8_Reserved_31_15_OFFSET             15
#define L2_PTC_LPT_CONTROL_8_Reserved_31_15_MASK               0xffff8000

typedef union {
  struct {
    UINT32                      PTC_Phase1_Bank6_Page_Size_Sel:5;
    UINT32                      PTC_Phase2_Bank6_Page_Size_Sel:5;
    UINT32                      PTC_Phase3_Bank6_Page_Size_Sel:5;
    UINT32                                      Reserved_31_15:17;
  } Field;
  UINT32 Value;
} L2_PTC_LPT_CONTROL_8_STRUCT;

#define SMN_L2_PTC_LPT_CONTROL_8_ADDRESS                              0x15700144UL
#define SMN_IOMMU0NBIO0_L2_PTC_LPT_CONTROL_8_ADDRESS                  0x15700144UL
#define SMN_IOMMU0NBIO1_L2_PTC_LPT_CONTROL_8_ADDRESS                  0x15900144UL
#define SMN_IOMMU1NBIO0_L2_PTC_LPT_CONTROL_8_ADDRESS                  0x15800144UL
#define SMN_IOMMU1NBIO1_L2_PTC_LPT_CONTROL_8_ADDRESS                  0x15a00144UL


/***********************************************************
* Register Name : L2_PTC_LPT_CONTROL_9
************************************************************/

#define L2_PTC_LPT_CONTROL_9_PTC_Phase1_Bank7_Page_Size_Sel_OFFSET 0
#define L2_PTC_LPT_CONTROL_9_PTC_Phase1_Bank7_Page_Size_Sel_MASK 0x1f

#define L2_PTC_LPT_CONTROL_9_PTC_Phase2_Bank7_Page_Size_Sel_OFFSET 5
#define L2_PTC_LPT_CONTROL_9_PTC_Phase2_Bank7_Page_Size_Sel_MASK 0x3e0

#define L2_PTC_LPT_CONTROL_9_PTC_Phase3_Bank7_Page_Size_Sel_OFFSET 10
#define L2_PTC_LPT_CONTROL_9_PTC_Phase3_Bank7_Page_Size_Sel_MASK 0x7c00

#define L2_PTC_LPT_CONTROL_9_Reserved_31_15_OFFSET             15
#define L2_PTC_LPT_CONTROL_9_Reserved_31_15_MASK               0xffff8000

typedef union {
  struct {
    UINT32                      PTC_Phase1_Bank7_Page_Size_Sel:5;
    UINT32                      PTC_Phase2_Bank7_Page_Size_Sel:5;
    UINT32                      PTC_Phase3_Bank7_Page_Size_Sel:5;
    UINT32                                      Reserved_31_15:17;
  } Field;
  UINT32 Value;
} L2_PTC_LPT_CONTROL_9_STRUCT;

#define SMN_L2_PTC_LPT_CONTROL_9_ADDRESS                              0x15700148UL
#define SMN_IOMMU0NBIO0_L2_PTC_LPT_CONTROL_9_ADDRESS                  0x15700148UL
#define SMN_IOMMU0NBIO1_L2_PTC_LPT_CONTROL_9_ADDRESS                  0x15900148UL
#define SMN_IOMMU1NBIO0_L2_PTC_LPT_CONTROL_9_ADDRESS                  0x15800148UL
#define SMN_IOMMU1NBIO1_L2_PTC_LPT_CONTROL_9_ADDRESS                  0x15a00148UL


/***********************************************************
* Register Name : L2_PWRGATE_CNTRL_REG_0
************************************************************/

#define L2_PWRGATE_CNTRL_REG_0_IP_PG_thres_OFFSET              0
#define L2_PWRGATE_CNTRL_REG_0_IP_PG_thres_MASK                0xffffffff

typedef union {
  struct {
    UINT32                                         IP_PG_thres:32;
  } Field;
  UINT32 Value;
} L2_PWRGATE_CNTRL_REG_0_STRUCT;

#define SMN_L2_PWRGATE_CNTRL_REG_0_ADDRESS                            0x157000f8UL
#define SMN_IOMMU0NBIO0_L2_PWRGATE_CNTRL_REG_0_ADDRESS                0x157000f8UL
#define SMN_IOMMU0NBIO1_L2_PWRGATE_CNTRL_REG_0_ADDRESS                0x159000f8UL
#define SMN_IOMMU1NBIO0_L2_PWRGATE_CNTRL_REG_0_ADDRESS                0x158000f8UL
#define SMN_IOMMU1NBIO1_L2_PWRGATE_CNTRL_REG_0_ADDRESS                0x15a000f8UL


/***********************************************************
* Register Name : L2_PWRGATE_CNTRL_REG_3
************************************************************/

#define L2_PWRGATE_CNTRL_REG_3_IP_PG_en_OFFSET                 0
#define L2_PWRGATE_CNTRL_REG_3_IP_PG_en_MASK                   0x1

#define L2_PWRGATE_CNTRL_REG_3_IP_PG_busy_OFFSET               1
#define L2_PWRGATE_CNTRL_REG_3_IP_PG_busy_MASK                 0x2

#define L2_PWRGATE_CNTRL_REG_3_L2_PG_STATUS_OFFSET             2
#define L2_PWRGATE_CNTRL_REG_3_L2_PG_STATUS_MASK               0x4

#define L2_PWRGATE_CNTRL_REG_3_CFG_FW_PG_EXIT_EN_OFFSET        3
#define L2_PWRGATE_CNTRL_REG_3_CFG_FW_PG_EXIT_EN_MASK          0x18

#define L2_PWRGATE_CNTRL_REG_3_Reserved_31_5_OFFSET            5
#define L2_PWRGATE_CNTRL_REG_3_Reserved_31_5_MASK              0xffffffe0

typedef union {
  struct {
    UINT32                                            IP_PG_en:1;
    UINT32                                          IP_PG_busy:1;
    UINT32                                        L2_PG_STATUS:1;
    UINT32                                   CFG_FW_PG_EXIT_EN:2;
    UINT32                                       Reserved_31_5:27;
  } Field;
  UINT32 Value;
} L2_PWRGATE_CNTRL_REG_3_STRUCT;

#define SMN_L2_PWRGATE_CNTRL_REG_3_ADDRESS                            0x15700104UL
#define SMN_IOMMU0NBIO0_L2_PWRGATE_CNTRL_REG_3_ADDRESS                0x15700104UL
#define SMN_IOMMU0NBIO1_L2_PWRGATE_CNTRL_REG_3_ADDRESS                0x15900104UL
#define SMN_IOMMU1NBIO0_L2_PWRGATE_CNTRL_REG_3_ADDRESS                0x15800104UL
#define SMN_IOMMU1NBIO1_L2_PWRGATE_CNTRL_REG_3_ADDRESS                0x15a00104UL


/***********************************************************
* Register Name : L2_RIOMMUDCN_DEBUG
************************************************************/

#define L2_RIOMMUDCN_DEBUG_DISABLE_RIOMMUDCN_CMDS_OFFSET       0
#define L2_RIOMMUDCN_DEBUG_DISABLE_RIOMMUDCN_CMDS_MASK         0x1

#define L2_RIOMMUDCN_DEBUG_ALLOW_RIOMMUDCN_INVPAGE_OFFSET      1
#define L2_RIOMMUDCN_DEBUG_ALLOW_RIOMMUDCN_INVPAGE_MASK        0x2

#define L2_RIOMMUDCN_DEBUG_ALLOW_RIOMMUDCN_CMPLWAIT_OFFSET     2
#define L2_RIOMMUDCN_DEBUG_ALLOW_RIOMMUDCN_CMPLWAIT_MASK       0x4

#define L2_RIOMMUDCN_DEBUG_Reserved_31_3_OFFSET                3
#define L2_RIOMMUDCN_DEBUG_Reserved_31_3_MASK                  0xfffffff8

typedef union {
  struct {
    UINT32                              DISABLE_RIOMMUDCN_CMDS:1;
    UINT32                             ALLOW_RIOMMUDCN_INVPAGE:1;
    UINT32                            ALLOW_RIOMMUDCN_CMPLWAIT:1;
    UINT32                                       Reserved_31_3:29;
  } Field;
  UINT32 Value;
} L2_RIOMMUDCN_DEBUG_STRUCT;

#define SMN_L2_RIOMMUDCN_DEBUG_ADDRESS                                0x13f01308UL
#define SMN_IOMMU0NBIO0_L2_RIOMMUDCN_DEBUG_ADDRESS                    0x13f01308UL
#define SMN_IOMMU0NBIO1_L2_RIOMMUDCN_DEBUG_ADDRESS                    0x14101308UL
#define SMN_IOMMU1NBIO0_L2_RIOMMUDCN_DEBUG_ADDRESS                    0x14001308UL
#define SMN_IOMMU1NBIO1_L2_RIOMMUDCN_DEBUG_ADDRESS                    0x14201308UL


/***********************************************************
* Register Name : L2_RIOMMUDCN_FAULTNOTIF_0
************************************************************/

#define L2_RIOMMUDCN_FAULTNOTIF_0_RIOMMUDCN_EVENTLOG_DATAWORD_0_OFFSET 0
#define L2_RIOMMUDCN_FAULTNOTIF_0_RIOMMUDCN_EVENTLOG_DATAWORD_0_MASK 0xffffffff

typedef union {
  struct {
    UINT32                       RIOMMUDCN_EVENTLOG_DATAWORD_0:32;
  } Field;
  UINT32 Value;
} L2_RIOMMUDCN_FAULTNOTIF_0_STRUCT;

#define SMN_L2_RIOMMUDCN_FAULTNOTIF_0_ADDRESS                         0x13f012d8UL
#define SMN_IOMMU0NBIO0_L2_RIOMMUDCN_FAULTNOTIF_0_ADDRESS             0x13f012d8UL
#define SMN_IOMMU0NBIO1_L2_RIOMMUDCN_FAULTNOTIF_0_ADDRESS             0x141012d8UL
#define SMN_IOMMU1NBIO0_L2_RIOMMUDCN_FAULTNOTIF_0_ADDRESS             0x140012d8UL
#define SMN_IOMMU1NBIO1_L2_RIOMMUDCN_FAULTNOTIF_0_ADDRESS             0x142012d8UL


/***********************************************************
* Register Name : L2_RIOMMUDCN_FAULTNOTIF_1
************************************************************/

#define L2_RIOMMUDCN_FAULTNOTIF_1_RIOMMUDCN_EVENTLOG_DATAWORD_1_OFFSET 0
#define L2_RIOMMUDCN_FAULTNOTIF_1_RIOMMUDCN_EVENTLOG_DATAWORD_1_MASK 0xffffffff

typedef union {
  struct {
    UINT32                       RIOMMUDCN_EVENTLOG_DATAWORD_1:32;
  } Field;
  UINT32 Value;
} L2_RIOMMUDCN_FAULTNOTIF_1_STRUCT;

#define SMN_L2_RIOMMUDCN_FAULTNOTIF_1_ADDRESS                         0x13f012dcUL
#define SMN_IOMMU0NBIO0_L2_RIOMMUDCN_FAULTNOTIF_1_ADDRESS             0x13f012dcUL
#define SMN_IOMMU0NBIO1_L2_RIOMMUDCN_FAULTNOTIF_1_ADDRESS             0x141012dcUL
#define SMN_IOMMU1NBIO0_L2_RIOMMUDCN_FAULTNOTIF_1_ADDRESS             0x140012dcUL
#define SMN_IOMMU1NBIO1_L2_RIOMMUDCN_FAULTNOTIF_1_ADDRESS             0x142012dcUL


/***********************************************************
* Register Name : L2_RIOMMUDCN_FAULTNOTIF_2
************************************************************/

#define L2_RIOMMUDCN_FAULTNOTIF_2_RIOMMUDCN_EVENTLOG_DATAWORD_2_OFFSET 0
#define L2_RIOMMUDCN_FAULTNOTIF_2_RIOMMUDCN_EVENTLOG_DATAWORD_2_MASK 0xffffffff

typedef union {
  struct {
    UINT32                       RIOMMUDCN_EVENTLOG_DATAWORD_2:32;
  } Field;
  UINT32 Value;
} L2_RIOMMUDCN_FAULTNOTIF_2_STRUCT;

#define SMN_L2_RIOMMUDCN_FAULTNOTIF_2_ADDRESS                         0x13f012e0UL
#define SMN_IOMMU0NBIO0_L2_RIOMMUDCN_FAULTNOTIF_2_ADDRESS             0x13f012e0UL
#define SMN_IOMMU0NBIO1_L2_RIOMMUDCN_FAULTNOTIF_2_ADDRESS             0x141012e0UL
#define SMN_IOMMU1NBIO0_L2_RIOMMUDCN_FAULTNOTIF_2_ADDRESS             0x140012e0UL
#define SMN_IOMMU1NBIO1_L2_RIOMMUDCN_FAULTNOTIF_2_ADDRESS             0x142012e0UL


/***********************************************************
* Register Name : L2_RIOMMUDCN_FAULTNOTIF_3
************************************************************/

#define L2_RIOMMUDCN_FAULTNOTIF_3_RIOMMUDCN_EVENTLOG_DATAWORD_3_OFFSET 0
#define L2_RIOMMUDCN_FAULTNOTIF_3_RIOMMUDCN_EVENTLOG_DATAWORD_3_MASK 0xffffffff

typedef union {
  struct {
    UINT32                       RIOMMUDCN_EVENTLOG_DATAWORD_3:32;
  } Field;
  UINT32 Value;
} L2_RIOMMUDCN_FAULTNOTIF_3_STRUCT;

#define SMN_L2_RIOMMUDCN_FAULTNOTIF_3_ADDRESS                         0x13f012e4UL
#define SMN_IOMMU0NBIO0_L2_RIOMMUDCN_FAULTNOTIF_3_ADDRESS             0x13f012e4UL
#define SMN_IOMMU0NBIO1_L2_RIOMMUDCN_FAULTNOTIF_3_ADDRESS             0x141012e4UL
#define SMN_IOMMU1NBIO0_L2_RIOMMUDCN_FAULTNOTIF_3_ADDRESS             0x140012e4UL
#define SMN_IOMMU1NBIO1_L2_RIOMMUDCN_FAULTNOTIF_3_ADDRESS             0x142012e4UL


/***********************************************************
* Register Name : L2_RIOMMUDCN_INVACK_0
************************************************************/

#define L2_RIOMMUDCN_INVACK_0_RIOMMUDCN_INVALIDATION_STATUS_OFFSET 0
#define L2_RIOMMUDCN_INVACK_0_RIOMMUDCN_INVALIDATION_STATUS_MASK 0x3

#define L2_RIOMMUDCN_INVACK_0_RIOMMUDCN_INVALIDATION_ITAG_29_0_OFFSET 2
#define L2_RIOMMUDCN_INVACK_0_RIOMMUDCN_INVALIDATION_ITAG_29_0_MASK 0xfffffffc

typedef union {
  struct {
    UINT32                       RIOMMUDCN_INVALIDATION_STATUS:2;
    UINT32                    RIOMMUDCN_INVALIDATION_ITAG_29_0:30;
  } Field;
  UINT32 Value;
} L2_RIOMMUDCN_INVACK_0_STRUCT;

#define SMN_L2_RIOMMUDCN_INVACK_0_ADDRESS                             0x13f012d0UL
#define SMN_IOMMU0NBIO0_L2_RIOMMUDCN_INVACK_0_ADDRESS                 0x13f012d0UL
#define SMN_IOMMU0NBIO1_L2_RIOMMUDCN_INVACK_0_ADDRESS                 0x141012d0UL
#define SMN_IOMMU1NBIO0_L2_RIOMMUDCN_INVACK_0_ADDRESS                 0x140012d0UL
#define SMN_IOMMU1NBIO1_L2_RIOMMUDCN_INVACK_0_ADDRESS                 0x142012d0UL


/***********************************************************
* Register Name : L2_RIOMMUDCN_INVACK_1
************************************************************/

#define L2_RIOMMUDCN_INVACK_1_RIOMMUDCN_INVALIDATION_ITAG_31_30_OFFSET 0
#define L2_RIOMMUDCN_INVACK_1_RIOMMUDCN_INVALIDATION_ITAG_31_30_MASK 0x3

#define L2_RIOMMUDCN_INVACK_1_Reserved_31_2_OFFSET             2
#define L2_RIOMMUDCN_INVACK_1_Reserved_31_2_MASK               0xfffffffc

typedef union {
  struct {
    UINT32                   RIOMMUDCN_INVALIDATION_ITAG_31_30:2;
    UINT32                                       Reserved_31_2:30;
  } Field;
  UINT32 Value;
} L2_RIOMMUDCN_INVACK_1_STRUCT;

#define SMN_L2_RIOMMUDCN_INVACK_1_ADDRESS                             0x13f012d4UL
#define SMN_IOMMU0NBIO0_L2_RIOMMUDCN_INVACK_1_ADDRESS                 0x13f012d4UL
#define SMN_IOMMU0NBIO1_L2_RIOMMUDCN_INVACK_1_ADDRESS                 0x141012d4UL
#define SMN_IOMMU1NBIO0_L2_RIOMMUDCN_INVACK_1_ADDRESS                 0x140012d4UL
#define SMN_IOMMU1NBIO1_L2_RIOMMUDCN_INVACK_1_ADDRESS                 0x142012d4UL


/***********************************************************
* Register Name : L2_RIOMMUISP_DEBUG
************************************************************/

#define L2_RIOMMUISP_DEBUG_DISABLE_RIOMMUISP_CMDS_OFFSET       0
#define L2_RIOMMUISP_DEBUG_DISABLE_RIOMMUISP_CMDS_MASK         0x1

#define L2_RIOMMUISP_DEBUG_ALLOW_RIOMMUISP_INVPAGE_OFFSET      1
#define L2_RIOMMUISP_DEBUG_ALLOW_RIOMMUISP_INVPAGE_MASK        0x2

#define L2_RIOMMUISP_DEBUG_ALLOW_RIOMMUISP_CMPLWAIT_OFFSET     2
#define L2_RIOMMUISP_DEBUG_ALLOW_RIOMMUISP_CMPLWAIT_MASK       0x4

#define L2_RIOMMUISP_DEBUG_Reserved_31_3_OFFSET                3
#define L2_RIOMMUISP_DEBUG_Reserved_31_3_MASK                  0xfffffff8

typedef union {
  struct {
    UINT32                              DISABLE_RIOMMUISP_CMDS:1;
    UINT32                             ALLOW_RIOMMUISP_INVPAGE:1;
    UINT32                            ALLOW_RIOMMUISP_CMPLWAIT:1;
    UINT32                                       Reserved_31_3:29;
  } Field;
  UINT32 Value;
} L2_RIOMMUISP_DEBUG_STRUCT;

#define SMN_L2_RIOMMUISP_DEBUG_ADDRESS                                0x13f01304UL
#define SMN_IOMMU0NBIO0_L2_RIOMMUISP_DEBUG_ADDRESS                    0x13f01304UL
#define SMN_IOMMU0NBIO1_L2_RIOMMUISP_DEBUG_ADDRESS                    0x14101304UL
#define SMN_IOMMU1NBIO0_L2_RIOMMUISP_DEBUG_ADDRESS                    0x14001304UL
#define SMN_IOMMU1NBIO1_L2_RIOMMUISP_DEBUG_ADDRESS                    0x14201304UL


/***********************************************************
* Register Name : L2_RIOMMUISP_FAULTNOTIF_0
************************************************************/

#define L2_RIOMMUISP_FAULTNOTIF_0_RIOMMUISP_EVENTLOG_DATAWORD_0_OFFSET 0
#define L2_RIOMMUISP_FAULTNOTIF_0_RIOMMUISP_EVENTLOG_DATAWORD_0_MASK 0xffffffff

typedef union {
  struct {
    UINT32                       RIOMMUISP_EVENTLOG_DATAWORD_0:32;
  } Field;
  UINT32 Value;
} L2_RIOMMUISP_FAULTNOTIF_0_STRUCT;

#define SMN_L2_RIOMMUISP_FAULTNOTIF_0_ADDRESS                         0x13f012c0UL
#define SMN_IOMMU0NBIO0_L2_RIOMMUISP_FAULTNOTIF_0_ADDRESS             0x13f012c0UL
#define SMN_IOMMU0NBIO1_L2_RIOMMUISP_FAULTNOTIF_0_ADDRESS             0x141012c0UL
#define SMN_IOMMU1NBIO0_L2_RIOMMUISP_FAULTNOTIF_0_ADDRESS             0x140012c0UL
#define SMN_IOMMU1NBIO1_L2_RIOMMUISP_FAULTNOTIF_0_ADDRESS             0x142012c0UL


/***********************************************************
* Register Name : L2_RIOMMUISP_FAULTNOTIF_1
************************************************************/

#define L2_RIOMMUISP_FAULTNOTIF_1_RIOMMUISP_EVENTLOG_DATAWORD_1_OFFSET 0
#define L2_RIOMMUISP_FAULTNOTIF_1_RIOMMUISP_EVENTLOG_DATAWORD_1_MASK 0xffffffff

typedef union {
  struct {
    UINT32                       RIOMMUISP_EVENTLOG_DATAWORD_1:32;
  } Field;
  UINT32 Value;
} L2_RIOMMUISP_FAULTNOTIF_1_STRUCT;

#define SMN_L2_RIOMMUISP_FAULTNOTIF_1_ADDRESS                         0x13f012c4UL
#define SMN_IOMMU0NBIO0_L2_RIOMMUISP_FAULTNOTIF_1_ADDRESS             0x13f012c4UL
#define SMN_IOMMU0NBIO1_L2_RIOMMUISP_FAULTNOTIF_1_ADDRESS             0x141012c4UL
#define SMN_IOMMU1NBIO0_L2_RIOMMUISP_FAULTNOTIF_1_ADDRESS             0x140012c4UL
#define SMN_IOMMU1NBIO1_L2_RIOMMUISP_FAULTNOTIF_1_ADDRESS             0x142012c4UL


/***********************************************************
* Register Name : L2_RIOMMUISP_FAULTNOTIF_2
************************************************************/

#define L2_RIOMMUISP_FAULTNOTIF_2_RIOMMUISP_EVENTLOG_DATAWORD_2_OFFSET 0
#define L2_RIOMMUISP_FAULTNOTIF_2_RIOMMUISP_EVENTLOG_DATAWORD_2_MASK 0xffffffff

typedef union {
  struct {
    UINT32                       RIOMMUISP_EVENTLOG_DATAWORD_2:32;
  } Field;
  UINT32 Value;
} L2_RIOMMUISP_FAULTNOTIF_2_STRUCT;

#define SMN_L2_RIOMMUISP_FAULTNOTIF_2_ADDRESS                         0x13f012c8UL
#define SMN_IOMMU0NBIO0_L2_RIOMMUISP_FAULTNOTIF_2_ADDRESS             0x13f012c8UL
#define SMN_IOMMU0NBIO1_L2_RIOMMUISP_FAULTNOTIF_2_ADDRESS             0x141012c8UL
#define SMN_IOMMU1NBIO0_L2_RIOMMUISP_FAULTNOTIF_2_ADDRESS             0x140012c8UL
#define SMN_IOMMU1NBIO1_L2_RIOMMUISP_FAULTNOTIF_2_ADDRESS             0x142012c8UL


/***********************************************************
* Register Name : L2_RIOMMUISP_FAULTNOTIF_3
************************************************************/

#define L2_RIOMMUISP_FAULTNOTIF_3_RIOMMUISP_EVENTLOG_DATAWORD_3_OFFSET 0
#define L2_RIOMMUISP_FAULTNOTIF_3_RIOMMUISP_EVENTLOG_DATAWORD_3_MASK 0xffffffff

typedef union {
  struct {
    UINT32                       RIOMMUISP_EVENTLOG_DATAWORD_3:32;
  } Field;
  UINT32 Value;
} L2_RIOMMUISP_FAULTNOTIF_3_STRUCT;

#define SMN_L2_RIOMMUISP_FAULTNOTIF_3_ADDRESS                         0x13f012ccUL
#define SMN_IOMMU0NBIO0_L2_RIOMMUISP_FAULTNOTIF_3_ADDRESS             0x13f012ccUL
#define SMN_IOMMU0NBIO1_L2_RIOMMUISP_FAULTNOTIF_3_ADDRESS             0x141012ccUL
#define SMN_IOMMU1NBIO0_L2_RIOMMUISP_FAULTNOTIF_3_ADDRESS             0x140012ccUL
#define SMN_IOMMU1NBIO1_L2_RIOMMUISP_FAULTNOTIF_3_ADDRESS             0x142012ccUL


/***********************************************************
* Register Name : L2_RIOMMUISP_INVACK_0
************************************************************/

#define L2_RIOMMUISP_INVACK_0_RIOMMUISP_INVALIDATION_STATUS_OFFSET 0
#define L2_RIOMMUISP_INVACK_0_RIOMMUISP_INVALIDATION_STATUS_MASK 0x3

#define L2_RIOMMUISP_INVACK_0_RIOMMUISP_INVALIDATION_ITAG_29_0_OFFSET 2
#define L2_RIOMMUISP_INVACK_0_RIOMMUISP_INVALIDATION_ITAG_29_0_MASK 0xfffffffc

typedef union {
  struct {
    UINT32                       RIOMMUISP_INVALIDATION_STATUS:2;
    UINT32                    RIOMMUISP_INVALIDATION_ITAG_29_0:30;
  } Field;
  UINT32 Value;
} L2_RIOMMUISP_INVACK_0_STRUCT;

#define SMN_L2_RIOMMUISP_INVACK_0_ADDRESS                             0x13f012b8UL
#define SMN_IOMMU0NBIO0_L2_RIOMMUISP_INVACK_0_ADDRESS                 0x13f012b8UL
#define SMN_IOMMU0NBIO1_L2_RIOMMUISP_INVACK_0_ADDRESS                 0x141012b8UL
#define SMN_IOMMU1NBIO0_L2_RIOMMUISP_INVACK_0_ADDRESS                 0x140012b8UL
#define SMN_IOMMU1NBIO1_L2_RIOMMUISP_INVACK_0_ADDRESS                 0x142012b8UL


/***********************************************************
* Register Name : L2_RIOMMUISP_INVACK_1
************************************************************/

#define L2_RIOMMUISP_INVACK_1_RIOMMUISP_INVALIDATION_ITAG_31_30_OFFSET 0
#define L2_RIOMMUISP_INVACK_1_RIOMMUISP_INVALIDATION_ITAG_31_30_MASK 0x3

#define L2_RIOMMUISP_INVACK_1_Reserved_31_2_OFFSET             2
#define L2_RIOMMUISP_INVACK_1_Reserved_31_2_MASK               0xfffffffc

typedef union {
  struct {
    UINT32                   RIOMMUISP_INVALIDATION_ITAG_31_30:2;
    UINT32                                       Reserved_31_2:30;
  } Field;
  UINT32 Value;
} L2_RIOMMUISP_INVACK_1_STRUCT;

#define SMN_L2_RIOMMUISP_INVACK_1_ADDRESS                             0x13f012bcUL
#define SMN_IOMMU0NBIO0_L2_RIOMMUISP_INVACK_1_ADDRESS                 0x13f012bcUL
#define SMN_IOMMU0NBIO1_L2_RIOMMUISP_INVACK_1_ADDRESS                 0x141012bcUL
#define SMN_IOMMU1NBIO0_L2_RIOMMUISP_INVACK_1_ADDRESS                 0x140012bcUL
#define SMN_IOMMU1NBIO1_L2_RIOMMUISP_INVACK_1_ADDRESS                 0x142012bcUL


/***********************************************************
* Register Name : L2_RIOMMU_INV_CNTRL
************************************************************/

#define L2_RIOMMU_INV_CNTRL_Reserved_0_0_OFFSET                0
#define L2_RIOMMU_INV_CNTRL_Reserved_0_0_MASK                  0x1

#define L2_RIOMMU_INV_CNTRL_RIOMMU_MULTI_INV_LIM_OFFSET        1
#define L2_RIOMMU_INV_CNTRL_RIOMMU_MULTI_INV_LIM_MASK          0x3e

#define L2_RIOMMU_INV_CNTRL_RIOMMU_MULTI_INVACK_LIM_OFFSET     6
#define L2_RIOMMU_INV_CNTRL_RIOMMU_MULTI_INVACK_LIM_MASK       0x7c0

#define L2_RIOMMU_INV_CNTRL_Reserved_31_11_OFFSET              11
#define L2_RIOMMU_INV_CNTRL_Reserved_31_11_MASK                0xfffff800

typedef union {
  struct {
    UINT32                                        Reserved_0_0:1;
    UINT32                                RIOMMU_MULTI_INV_LIM:5;
    UINT32                             RIOMMU_MULTI_INVACK_LIM:5;
    UINT32                                      Reserved_31_11:21;
  } Field;
  UINT32 Value;
} L2_RIOMMU_INV_CNTRL_STRUCT;

#define SMN_L2_RIOMMU_INV_CNTRL_ADDRESS                               0x13f0130cUL
#define SMN_IOMMU0NBIO0_L2_RIOMMU_INV_CNTRL_ADDRESS                   0x13f0130cUL
#define SMN_IOMMU0NBIO1_L2_RIOMMU_INV_CNTRL_ADDRESS                   0x1410130cUL
#define SMN_IOMMU1NBIO0_L2_RIOMMU_INV_CNTRL_ADDRESS                   0x1400130cUL
#define SMN_IOMMU1NBIO1_L2_RIOMMU_INV_CNTRL_ADDRESS                   0x1420130cUL


/***********************************************************
* Register Name : L2_RT_CONTROL_0
************************************************************/

#define L2_RT_CONTROL_0_FLTCMB_CLIENTS_ARB_CTRL_OFFSET         0
#define L2_RT_CONTROL_0_FLTCMB_CLIENTS_ARB_CTRL_MASK           0x1f

#define L2_RT_CONTROL_0_Reserved_7_5_OFFSET                    5
#define L2_RT_CONTROL_0_Reserved_7_5_MASK                      0xe0

#define L2_RT_CONTROL_0_IFIFO_CLIENTS_ARB_CTRL_OFFSET          8
#define L2_RT_CONTROL_0_IFIFO_CLIENTS_ARB_CTRL_MASK            0xff00

#define L2_RT_CONTROL_0_PTC_CLIENTS_ARB_CTRL_OFFSET            16
#define L2_RT_CONTROL_0_PTC_CLIENTS_ARB_CTRL_MASK              0x70000

#define L2_RT_CONTROL_0_Reserved_19_19_OFFSET                  19
#define L2_RT_CONTROL_0_Reserved_19_19_MASK                    0x80000

#define L2_RT_CONTROL_0_PDC_CLIENTS_ARB_CTRL_OFFSET            20
#define L2_RT_CONTROL_0_PDC_CLIENTS_ARB_CTRL_MASK              0x300000

#define L2_RT_CONTROL_0_Reserved_31_22_OFFSET                  22
#define L2_RT_CONTROL_0_Reserved_31_22_MASK                    0xffc00000

typedef union {
  struct {
    UINT32                             FLTCMB_CLIENTS_ARB_CTRL:5;
    UINT32                                        Reserved_7_5:3;
    UINT32                              IFIFO_CLIENTS_ARB_CTRL:8;
    UINT32                                PTC_CLIENTS_ARB_CTRL:3;
    UINT32                                      Reserved_19_19:1;
    UINT32                                PDC_CLIENTS_ARB_CTRL:2;
    UINT32                                      Reserved_31_22:10;
  } Field;
  UINT32 Value;
} L2_RT_CONTROL_0_STRUCT;

#define SMN_L2_RT_CONTROL_0_ADDRESS                                   0x15700070UL
#define SMN_IOMMU0NBIO0_L2_RT_CONTROL_0_ADDRESS                       0x15700070UL
#define SMN_IOMMU0NBIO1_L2_RT_CONTROL_0_ADDRESS                       0x15900070UL
#define SMN_IOMMU1NBIO0_L2_RT_CONTROL_0_ADDRESS                       0x15800070UL
#define SMN_IOMMU1NBIO1_L2_RT_CONTROL_0_ADDRESS                       0x15a00070UL


/***********************************************************
* Register Name : L2_RT_CONTROL_CTRL_0
************************************************************/

#define L2_RT_CONTROL_CTRL_0_PDC_CLIENTS_ARB_CTRL_OFFSET       0
#define L2_RT_CONTROL_CTRL_0_PDC_CLIENTS_ARB_CTRL_MASK         0x3

#define L2_RT_CONTROL_CTRL_0_Reserved_3_2_OFFSET               2
#define L2_RT_CONTROL_CTRL_0_Reserved_3_2_MASK                 0xc

#define L2_RT_CONTROL_CTRL_0_PTW_CLIENTS_ARB_CTRL_OFFSET       4
#define L2_RT_CONTROL_CTRL_0_PTW_CLIENTS_ARB_CTRL_MASK         0x30

#define L2_RT_CONTROL_CTRL_0_FC1_RT_Override_OFFSET            6
#define L2_RT_CONTROL_CTRL_0_FC1_RT_Override_MASK              0x40

#define L2_RT_CONTROL_CTRL_0_FC1_RT_Credits_OFFSET             7
#define L2_RT_CONTROL_CTRL_0_FC1_RT_Credits_MASK               0xff80

#define L2_RT_CONTROL_CTRL_0_Reserved_31_16_OFFSET             16
#define L2_RT_CONTROL_CTRL_0_Reserved_31_16_MASK               0xffff0000

typedef union {
  struct {
    UINT32                                PDC_CLIENTS_ARB_CTRL:2;
    UINT32                                        Reserved_3_2:2;
    UINT32                                PTW_CLIENTS_ARB_CTRL:2;
    UINT32                                     FC1_RT_Override:1;
    UINT32                                      FC1_RT_Credits:9;
    UINT32                                      Reserved_31_16:16;
  } Field;
  UINT32 Value;
} L2_RT_CONTROL_CTRL_0_STRUCT;

#define SMN_L2_RT_CONTROL_CTRL_0_ADDRESS                              0x13f012a8UL
#define SMN_IOMMU0NBIO0_L2_RT_CONTROL_CTRL_0_ADDRESS                  0x13f012a8UL
#define SMN_IOMMU0NBIO1_L2_RT_CONTROL_CTRL_0_ADDRESS                  0x141012a8UL
#define SMN_IOMMU1NBIO0_L2_RT_CONTROL_CTRL_0_ADDRESS                  0x140012a8UL
#define SMN_IOMMU1NBIO1_L2_RT_CONTROL_CTRL_0_ADDRESS                  0x142012a8UL


/***********************************************************
* Register Name : L2_SB_LOCATION
************************************************************/

#define L2_SB_LOCATION_SBlocated_Port_OFFSET                   0
#define L2_SB_LOCATION_SBlocated_Port_MASK                     0xffff

#define L2_SB_LOCATION_SBlocated_Core_OFFSET                   16
#define L2_SB_LOCATION_SBlocated_Core_MASK                     0xffff0000

typedef union {
  struct {
    UINT32                                      SBlocated_Port:16;
    UINT32                                      SBlocated_Core:16;
  } Field;
  UINT32 Value;
} L2_SB_LOCATION_STRUCT;

#define SMN_L2_SB_LOCATION_ADDRESS                                    0x13f0112cUL
#define SMN_IOMMU0NBIO0_L2_SB_LOCATION_ADDRESS                        0x13f0112cUL
#define SMN_IOMMU0NBIO1_L2_SB_LOCATION_ADDRESS                        0x1410112cUL
#define SMN_IOMMU1NBIO0_L2_SB_LOCATION_ADDRESS                        0x1400112cUL
#define SMN_IOMMU1NBIO1_L2_SB_LOCATION_ADDRESS                        0x1420112cUL


/***********************************************************
* Register Name : L2_TW_CONTROL
************************************************************/

#define L2_TW_CONTROL_Reserved_5_0_OFFSET                      0
#define L2_TW_CONTROL_Reserved_5_0_MASK                        0x3f

#define L2_TW_CONTROL_TWForceCoherent_OFFSET                   6
#define L2_TW_CONTROL_TWForceCoherent_MASK                     0x40

#define L2_TW_CONTROL_Reserved_7_7_OFFSET                      7
#define L2_TW_CONTROL_Reserved_7_7_MASK                        0x80

#define L2_TW_CONTROL_TWPrefetchEn_OFFSET                      8
#define L2_TW_CONTROL_TWPrefetchEn_MASK                        0x100

#define L2_TW_CONTROL_TWPrefetchOnly4KDis_OFFSET               9
#define L2_TW_CONTROL_TWPrefetchOnly4KDis_MASK                 0x200

#define L2_TW_CONTROL_TWPTEOnUntransExcl_OFFSET                10
#define L2_TW_CONTROL_TWPTEOnUntransExcl_MASK                  0x400

#define L2_TW_CONTROL_TWPTEOnAddrTransExcl_OFFSET              11
#define L2_TW_CONTROL_TWPTEOnAddrTransExcl_MASK                0x800

#define L2_TW_CONTROL_TWPrefetchRange_OFFSET                   12
#define L2_TW_CONTROL_TWPrefetchRange_MASK                     0x7000

#define L2_TW_CONTROL_Reserved_15_15_OFFSET                    15
#define L2_TW_CONTROL_Reserved_15_15_MASK                      0x8000

#define L2_TW_CONTROL_TWFilter_Dis_OFFSET                      16
#define L2_TW_CONTROL_TWFilter_Dis_MASK                        0x10000

#define L2_TW_CONTROL_TWFilter_64B_Dis_OFFSET                  17
#define L2_TW_CONTROL_TWFilter_64B_Dis_MASK                    0x20000

#define L2_TW_CONTROL_TWContWalkOnPErrDis_OFFSET               18
#define L2_TW_CONTROL_TWContWalkOnPErrDis_MASK                 0x40000

#define L2_TW_CONTROL_TWSetAccessBit_Dis_OFFSET                19
#define L2_TW_CONTROL_TWSetAccessBit_Dis_MASK                  0x80000

#define L2_TW_CONTROL_TWClearAPBit_Dis_OFFSET                  20
#define L2_TW_CONTROL_TWClearAPBit_Dis_MASK                    0x100000

#define L2_TW_CONTROL_Reserved_24_21_OFFSET                    21
#define L2_TW_CONTROL_Reserved_24_21_MASK                      0x1e00000

#define L2_TW_CONTROL_TWCacheNestedPTE_OFFSET                  25
#define L2_TW_CONTROL_TWCacheNestedPTE_MASK                    0x2000000

#define L2_TW_CONTROL_Reserved_31_26_OFFSET                    26
#define L2_TW_CONTROL_Reserved_31_26_MASK                      0xfc000000

typedef union {
  struct {
    UINT32                                        Reserved_5_0:6;
    UINT32                                     TWForceCoherent:1;
    UINT32                                        Reserved_7_7:1;
    UINT32                                        TWPrefetchEn:1;
    UINT32                                 TWPrefetchOnly4KDis:1;
    UINT32                                  TWPTEOnUntransExcl:1;
    UINT32                                TWPTEOnAddrTransExcl:1;
    UINT32                                     TWPrefetchRange:3;
    UINT32                                      Reserved_15_15:1;
    UINT32                                        TWFilter_Dis:1;
    UINT32                                    TWFilter_64B_Dis:1;
    UINT32                                 TWContWalkOnPErrDis:1;
    UINT32                                  TWSetAccessBit_Dis:1;
    UINT32                                    TWClearAPBit_Dis:1;
    UINT32                                      Reserved_24_21:4;
    UINT32                                    TWCacheNestedPTE:1;
    UINT32                                      Reserved_31_26:6;
  } Field;
  UINT32 Value;
} L2_TW_CONTROL_STRUCT;

#define SMN_L2_TW_CONTROL_ADDRESS                                     0x13f01150UL
#define SMN_IOMMU0NBIO0_L2_TW_CONTROL_ADDRESS                         0x13f01150UL
#define SMN_IOMMU0NBIO1_L2_TW_CONTROL_ADDRESS                         0x14101150UL
#define SMN_IOMMU1NBIO0_L2_TW_CONTROL_ADDRESS                         0x14001150UL
#define SMN_IOMMU1NBIO1_L2_TW_CONTROL_ADDRESS                         0x14201150UL


/***********************************************************
* Register Name : L2_VC_CONTROL_0
************************************************************/

#define L2_VC_CONTROL_0_TW_VC_OFFSET                           0
#define L2_VC_CONTROL_0_TW_VC_MASK                             0x7

#define L2_VC_CONTROL_0_TW_QOS_VC_OFFSET                       3
#define L2_VC_CONTROL_0_TW_QOS_VC_MASK                         0x38

#define L2_VC_CONTROL_0_CMPL_WB_VC_OFFSET                      6
#define L2_VC_CONTROL_0_CMPL_WB_VC_MASK                        0x1c0

#define L2_VC_CONTROL_0_EVENTLOG_VC_OFFSET                     9
#define L2_VC_CONTROL_0_EVENTLOG_VC_MASK                       0xe00

#define L2_VC_CONTROL_0_AVIC_VC_OFFSET                         12
#define L2_VC_CONTROL_0_AVIC_VC_MASK                           0x7000

#define L2_VC_CONTROL_0_GALOG_VC_OFFSET                        15
#define L2_VC_CONTROL_0_GALOG_VC_MASK                          0x38000

#define L2_VC_CONTROL_0_PPRLOG_VC_OFFSET                       18
#define L2_VC_CONTROL_0_PPRLOG_VC_MASK                         0x1c0000

#define L2_VC_CONTROL_0_CP_VC_OFFSET                           21
#define L2_VC_CONTROL_0_CP_VC_MASK                             0xe00000

#define L2_VC_CONTROL_0_VF_MMIO_VC_OFFSET                      24
#define L2_VC_CONTROL_0_VF_MMIO_VC_MASK                        0x7000000

#define L2_VC_CONTROL_0_VIOMMU_VC_OFFSET                       27
#define L2_VC_CONTROL_0_VIOMMU_VC_MASK                         0x38000000

#define L2_VC_CONTROL_0_L2IMU_GSTINT_pri_OFFSET                30
#define L2_VC_CONTROL_0_L2IMU_GSTINT_pri_MASK                  0x40000000

#define L2_VC_CONTROL_0_L2IMU_TRANS_pri_OFFSET                 31
#define L2_VC_CONTROL_0_L2IMU_TRANS_pri_MASK                   0x80000000

typedef union {
  struct {
    UINT32                                               TW_VC:3;
    UINT32                                           TW_QOS_VC:3;
    UINT32                                          CMPL_WB_VC:3;
    UINT32                                         EVENTLOG_VC:3;
    UINT32                                             AVIC_VC:3;
    UINT32                                            GALOG_VC:3;
    UINT32                                           PPRLOG_VC:3;
    UINT32                                               CP_VC:3;
    UINT32                                          VF_MMIO_VC:3;
    UINT32                                           VIOMMU_VC:3;
    UINT32                                    L2IMU_GSTINT_pri:1;
    UINT32                                     L2IMU_TRANS_pri:1;
  } Field;
  UINT32 Value;
} L2_VC_CONTROL_0_STRUCT;

#define SMN_L2_VC_CONTROL_0_ADDRESS                                   0x13f011d0UL
#define SMN_IOMMU0NBIO0_L2_VC_CONTROL_0_ADDRESS                       0x13f011d0UL
#define SMN_IOMMU0NBIO1_L2_VC_CONTROL_0_ADDRESS                       0x141011d0UL
#define SMN_IOMMU1NBIO0_L2_VC_CONTROL_0_ADDRESS                       0x140011d0UL
#define SMN_IOMMU1NBIO1_L2_VC_CONTROL_0_ADDRESS                       0x142011d0UL


/***********************************************************
* Register Name : L2_WQ_CONTROL
************************************************************/

#define L2_WQ_CONTROL_REG_GSTCMD_Num_WQs_m1_OFFSET             0
#define L2_WQ_CONTROL_REG_GSTCMD_Num_WQs_m1_MASK               0x7

#define L2_WQ_CONTROL_Reserved_7_3_OFFSET                      3
#define L2_WQ_CONTROL_Reserved_7_3_MASK                        0xf8

#define L2_WQ_CONTROL_REG_PPR_One_WQ_OFFSET                    8
#define L2_WQ_CONTROL_REG_PPR_One_WQ_MASK                      0x100

#define L2_WQ_CONTROL_Reserved_31_9_OFFSET                     9
#define L2_WQ_CONTROL_Reserved_31_9_MASK                       0xfffffe00

typedef union {
  struct {
    UINT32                               REG_GSTCMD_Num_WQs_m1:3;
    UINT32                                        Reserved_7_3:5;
    UINT32                                      REG_PPR_One_WQ:1;
    UINT32                                       Reserved_31_9:23;
  } Field;
  UINT32 Value;
} L2_WQ_CONTROL_STRUCT;

#define SMN_L2_WQ_CONTROL_ADDRESS                                     0x13f012b4UL
#define SMN_IOMMU0NBIO0_L2_WQ_CONTROL_ADDRESS                         0x13f012b4UL
#define SMN_IOMMU0NBIO1_L2_WQ_CONTROL_ADDRESS                         0x141012b4UL
#define SMN_IOMMU1NBIO0_L2_WQ_CONTROL_ADDRESS                         0x140012b4UL
#define SMN_IOMMU1NBIO1_L2_WQ_CONTROL_ADDRESS                         0x142012b4UL


/***********************************************************
* Register Name : PPR_CONTROL
************************************************************/

#define PPR_CONTROL_PPR_IntTimeDelay_OFFSET                    0
#define PPR_CONTROL_PPR_IntTimeDelay_MASK                      0xff

#define PPR_CONTROL_PPR_IntReqDelay_OFFSET                     8
#define PPR_CONTROL_PPR_IntReqDelay_MASK                       0xff00

#define PPR_CONTROL_PPR_IntCoallesce_En_OFFSET                 16
#define PPR_CONTROL_PPR_IntCoallesce_En_MASK                   0x10000

#define PPR_CONTROL_Reserved_31_17_OFFSET                      17
#define PPR_CONTROL_Reserved_31_17_MASK                        0xfffe0000

typedef union {
  struct {
    UINT32                                    PPR_IntTimeDelay:8;
    UINT32                                     PPR_IntReqDelay:8;
    UINT32                                 PPR_IntCoallesce_En:1;
    UINT32                                      Reserved_31_17:15;
  } Field;
  UINT32 Value;
} PPR_CONTROL_STRUCT;

#define SMN_PPR_CONTROL_ADDRESS                                       0x13f01248UL
#define SMN_IOMMU0NBIO0_PPR_CONTROL_ADDRESS                           0x13f01248UL
#define SMN_IOMMU0NBIO1_PPR_CONTROL_ADDRESS                           0x14101248UL
#define SMN_IOMMU1NBIO0_PPR_CONTROL_ADDRESS                           0x14001248UL
#define SMN_IOMMU1NBIO1_PPR_CONTROL_ADDRESS                           0x14201248UL

#endif /* _IOMMUL2_H_ */

