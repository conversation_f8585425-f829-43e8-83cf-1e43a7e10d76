/*****************************************************************************
 * Copyright (C) 2008-2024 Advanced Micro Devices, Inc. All rights reserved.
 *
*****************************************************************************
*/
/* $NoKeywords:$ */
/**
 * @file
 *
 * Fch SATA AHCI controller Library
 *
 * SATA AHCI Library
 *
 * @xrefitem bom "File Content Label" "Release Content"
 * @e project:     AGESA
 * @e sub-project: FCH
 * @e \$Revision: 309083 $   @e \$Date: 2014-12-09 09:28:24 -0800 (Tue, 09 Dec 2014) $
 *
 */
#include "FchPlatform.h"
#include "Filecode.h"
#define FILECODE FCH_KUNLUN_FCHKUNLUNCORE_KUNLUN_KLSATA_KLAHCILIB_FILECODE

/**
 * sataAhciSetDeviceNumMsi - Program AHCI controller support
 * device number cap & MSI cap
 *
 *
 * @param[in] DieBusNum  Bus Number of current Die
 * @param[in] FchDataPtr Fch configuration structure pointer.
 *
 */
VOID
SataAhciSetDeviceNumMsi (
  IN  UINT32   DieBusNum,
  IN  VOID     *FchDataPtr
  )
{

//  FCH_INTERFACE   *LocalCfgPtr;

//  LocalCfgPtr = (FCH_INTERFACE *)FchDataPtr;

}



