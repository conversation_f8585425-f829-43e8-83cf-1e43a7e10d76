#;*****************************************************************************
#;
#; Copyright (C) 2019-2024 Advanced Micro Devices, Inc. All rights reserved.
#;
#;******************************************************************************
[Defines]
  INF_VERSION                    = 0x00010006
  BASE_NAME                      = AmdPspRomArmorLib
  FILE_GUID                      = 55871F80-4658-6EF3-A8AC-15B34FF8C2CE
  MODULE_TYPE                    = DXE_SMM_DRIVER
  VERSION_STRING                 = 1.0
  LIBRARY_CLASS                  = AmdPspRomArmorLib
  CONSTRUCTOR                    = AmdPspRomArmorLibConstructor

[Sources.common]
  AmdPspRomArmorLib.c

[Packages]
  MdePkg/MdePkg.dec
  AgesaPkg/AgesaPkg.dec
  AgesaModulePkg/AgesaCommonModulePkg.dec
  AgesaModulePkg/AgesaModulePspPkg.dec

[LibraryClasses]
  AmdBaseLib
  IdsLib
  SmmServicesTableLib
  AmdPspMboxLibV2

[Guids]

[Protocols]
  gEfiSmmBase2ProtocolGuid                    #Consumed
  gPspMboxSmmBufferAddressProtocolGuid        #Consumed

[Ppis]

[Pcd]

[Depex]
  gEfiSmmBase2ProtocolGuid AND
  gPspMboxSmmBufferAddressProtocolGuid


