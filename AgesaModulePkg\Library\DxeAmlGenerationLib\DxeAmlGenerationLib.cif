<component>
    name = "DxeAmlGenerationLib"
    category = ModulePart
    LocalRoot = "AgesaModulePkg\Library\DxeAmlGenerationLib\"
    RefName = "AgesaModulePkg.Library.DxeAmlGenerationLib"
[INF]
"AmlGenerationLib.inf"
[files]
"AmlArgObjects.c"
"AmlAssistFunctions.c"
"AmlDataObjects.c"
"AmlExpressionOpcodes.c"
"AmlLocalObjects.c"
"AmlNamedObject.c"
"AmlNamespaceModifierObjects.c"
"AmlNameString.c"
"AmlObjectsDebug.c"
"AmlPkgLength.c"
"AmlResourceDescriptor.c"
"AmlStatementOpcodes.c"
"AmlTable.c"
"LocalAmlLib.h"
"LocalAmlObjects.c"
"LocalAmlObjects.h"
<endComponent>
