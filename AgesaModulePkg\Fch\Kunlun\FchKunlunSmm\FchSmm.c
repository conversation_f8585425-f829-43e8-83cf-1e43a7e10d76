/*****************************************************************************
 *
 * Copyright (C) 2008-2024 Advanced Micro Devices, Inc. All rights reserved.
 *
 *******************************************************************************
 */

/* $NoKeywords:$ */
/**
 * @file
 *
 * FCH SMM Driver
 *
 *
 * @xrefitem bom "File Content Label" "Release Content"
 * @e project:      AGESA
 * @e sub-project   FCH SMM Driver
 * @e \$Revision: 309090 $   @e \$Date: 2014-12-09 10:28:05 -0800 (Tue, 09 Dec 2014) $
 *
 */

#include "FchSmm.h"
#define FILECODE FCH_KUNLUN_FCHKUNLUNSMM_FCHSMM_FILECODE

#include <Library/BaseMemoryLib.h>
#include <Library/PcdLib.h>

//FCH_INIT_PROTOCOL                   gFchInitInSmm;
FCH_DATA_BLOCK                      gFchDataInSmm;
UINT8                               *mFchPciIrqRoutingTable;
UINT8                               *mPspMboxSmmBuffer = NULL;
BOOLEAN                             *mPspMboxSmmFlagAddr = NULL;
UINT32                               DimmsPerChannel = 1;
BOOLEAN                              IsFencingEnabled = TRUE;

EFI_STATUS
FchSmmRegistrationCenter (
  VOID
  )
{
  EFI_STATUS     Status;

  Status = FchSmmRegisterSwSmi ();
  Status = FchSmmRegisterPowerButtonSmi ();
  Status = FchSmmRegisterPeriodicTimerSmi ();
  Status = FchSmmRegisterGpiSmi ();
  Status = FchSmmRegisterSxSmi ();
  Status = FchSmmRegisterIoTrapSmi ();
  return Status;
}


static
VOID
FchSmmDisableUsbPort (
  VOID
  )
{
  EFI_STATUS                          Status;
  PSP_MBOX_SMMBUFFER_ADDRESS_PROTOCOL *PspMboxSmmBufferAddressProtocol;
  UINT8                               *PspMboxSmmBuffer;
  BOOLEAN                             *PspMboxSmmFlagAddr;

  Status                              = EFI_SUCCESS;
  PspMboxSmmBufferAddressProtocol     = NULL;
  PspMboxSmmBuffer                    = NULL;
  PspMboxSmmFlagAddr                  = NULL;

  IDS_HDT_CONSOLE (FCH_TRACE, "%a Start\n", __FUNCTION__);

  Status = gSmst->SmmLocateProtocol (
                    &gPspMboxSmmBufferAddressProtocolGuid,
                    NULL,
                    (VOID**)&PspMboxSmmBufferAddressProtocol
                    );
  ASSERT (!EFI_ERROR (Status));
  if ( Status == EFI_SUCCESS ) {
    PspMboxSmmBuffer   = PspMboxSmmBufferAddressProtocol->PspMboxSmmBuffer;
    PspMboxSmmFlagAddr = PspMboxSmmBufferAddressProtocol->PspMboxSmmFlagAddr;

    FchKLXhciDisablePortByPspMbox (
      0,
      (UINT8)(PcdGetBool (PcdXhci0Enable)) | (((UINT8)(PcdGetBool (PcdXhci1Enable)))<<1),
      PcdGet32 (PcdXhciUsb2PortDisable) & 0xF,
      PcdGet32 (PcdXhciUsb3PortDisable) & 0xF,
      PspMboxSmmBuffer,
      PspMboxSmmFlagAddr
      );
  } else {
    IDS_HDT_CONSOLE (FCH_TRACE, "Fail to get Psp Mbox SMM buffer.\n");
  }

  IDS_HDT_CONSOLE (FCH_TRACE, "%a End\n", __FUNCTION__);
}


EFI_STATUS
EFIAPI
FchSmmReadyToBootCallBack (
  IN  CONST EFI_GUID    *Protocol,
  IN        VOID        *Interface,
  IN        EFI_HANDLE  Handle
  )
{
  IDS_HDT_CONSOLE (FCH_TRACE, "%a Start\n", __FUNCTION__);

  //
  // Disable USB port by PSP mail box if needed
  //
  if ( gFchDataInSmm.FchResetDataBlock.DisableXhciPortLate ) {
    FchSmmDisableUsbPort ();
  }

  IDS_HDT_CONSOLE (FCH_TRACE, "%a End\n", __FUNCTION__);
  return EFI_SUCCESS;
}


/**
 * @brief Entry point of the Kunlun FCH SMM Driver.
 *
 * @details Copy FCH_INIT_PROTOCOL to SMM, and register Fch Smm callbacks
 *
 * @param[in] ImageHandle EFI Image Handle for the DXE driver
 * @param[in] SystemTable Pointer to the EFI system table
 *
 * @returns EFI_STATUS
 * @retval EFI_SUCCESS   Module initialized successfully
 * @retval EFI_ERROR     Initialization failed (see error for more details)
 */
EFI_STATUS
EFIAPI
FchSmmEntryPoint (
  IN       EFI_HANDLE         ImageHandle,
  IN       EFI_SYSTEM_TABLE   *SystemTable
  )

{
  FCH_INIT_PROTOCOL                     *pFchCimxInitProtocol;
  FCH_SMM_INIT_PROTOCOL                 *pFchSmmInitProtocol;
  EFI_HANDLE                            FchSmmInitHandle;
  EFI_STATUS                            Status;
  VOID                                  *Registration;

  AGESA_TESTPOINT (TpFchSmmEntry, NULL);
  IDS_HDT_CONSOLE (FCH_TRACE, "%a Start\n", __FUNCTION__);

  //
  // Initialize global variables
  //

  // Determine DIMMS per channel
  DimmsPerChannel = FixedPcdGet8 (PcdAmdMemMaxDimmPerChannelV2);

  // Determine if Ixc Fencing is enabled or disabled
  IsFencingEnabled = PcdGetBool (PcdAmdFchIxcTelemetryPortsFenceControl);

  Status = gSmst->SmmAllocatePool (
                    EfiRuntimeServicesData,
                    0x100,
                    (VOID **)&mFchPciIrqRoutingTable
                    );
  if (EFI_ERROR (Status)) {
    return Status;
  }

  Status = gSmst->SmmAllocatePool (
                    EfiRuntimeServicesData,
                    sizeof (FCH_SMM_INIT_PROTOCOL),
                    (VOID **)&pFchSmmInitProtocol
                    );
  if (EFI_ERROR (Status)) {
    return Status;
  }

  Status = gBS->LocateProtocol (
                  &gFchInitProtocolGuid,
                  NULL,
                  (VOID **)&pFchCimxInitProtocol
                  );
  if (!EFI_ERROR (Status)) {
    CopyMem (
      &gFchDataInSmm,
      pFchCimxInitProtocol->FchPolicy,
      sizeof (FCH_DATA_BLOCK)
      );
  } else {
    return EFI_ABORTED;
  }

  //Copy OEM table pointed by Fch parameter
  Status = FchSmmCopyOemTable ();

  Status = FchSmmRegistrationCenter ();

  //
  // install SMM protocol
  //
  pFchSmmInitProtocol->Revision     = pFchCimxInitProtocol->Revision;
  pFchSmmInitProtocol->FchRev       = pFchCimxInitProtocol->FchRev;
  pFchSmmInitProtocol->FchSmmPolicy = &gFchDataInSmm;
  FchSmmInitHandle =  NULL;

  //
  // Disable USB port by PSP mail box if needed
  //
  if ( !gFchDataInSmm.FchResetDataBlock.DisableXhciPortLate ) {
    FchSmmDisableUsbPort ();
  }

  gSmst->SmmRegisterProtocolNotify (
         &gEdkiiSmmReadyToBootProtocolGuid,
         FchSmmReadyToBootCallBack,
         &Registration
         );

  Status = gSmst->SmmInstallProtocolInterface (
                    &FchSmmInitHandle,
                    &gFchSmmInitProtocolGuid,
                    EFI_NATIVE_INTERFACE,
                    pFchSmmInitProtocol
                    );
  if (EFI_ERROR (Status)) {
    return Status;
  }

  AGESA_TESTPOINT (TpFchSmmExit, NULL);
  IDS_HDT_CONSOLE (FCH_TRACE, "%a End\n", __FUNCTION__);

  return Status;
}

EFI_STATUS
FchSmmCopyOemTable (
  VOID
  )
{
  VOID                        *OemProgTblPtr;
  UINTN                       OemProgTblSize;
  UINT8                       i;
  ACPI_REG_WRITE              *pAcpiTbl;
  EFI_STATUS                  Status;

  Status = EFI_NOT_FOUND;                            //no table found by default
  //
  // Copy gFchDataInSmm.HwAcpi.OemProgrammingTablePtr to SMM space for SMM handler to access
  //
  OemProgTblSize = 0;
  i=0;
  pAcpiTbl = gFchDataInSmm.HwAcpi.OemProgrammingTablePtr;
  if (pAcpiTbl != NULL) {
    if ((pAcpiTbl->MmioReg == 0) && (pAcpiTbl->MmioBase == 0) && (pAcpiTbl->DataAndMask == 0xB0) && (pAcpiTbl->DataOrMask == 0xAC)) {
      // Signature Checking
      pAcpiTbl++;
      OemProgTblSize = sizeof (ACPI_REG_WRITE);
      for ( i = 1; pAcpiTbl->MmioBase < 0x1D; i++ ) {
        pAcpiTbl++;
      }
      OemProgTblSize += i*(sizeof (ACPI_REG_WRITE));
    }
    if (OemProgTblSize != 0) {
      Status = gSmst->SmmAllocatePool (
                        EfiRuntimeServicesData,
                        OemProgTblSize,
                        &OemProgTblPtr
                        );
      if (!EFI_ERROR (Status)) {
        CopyMem (
          OemProgTblPtr,
          gFchDataInSmm.HwAcpi.OemProgrammingTablePtr,
          OemProgTblSize
          );

        gFchDataInSmm.HwAcpi.OemProgrammingTablePtr = OemProgTblPtr;  //Update pointer in SMM
      }
    } else {
      gFchDataInSmm.HwAcpi.OemProgrammingTablePtr = NULL;  // If no valid OEM program table, set pointer to NULL
    }
  }
  return Status;
}



