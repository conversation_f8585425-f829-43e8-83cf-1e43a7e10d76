#;*****************************************************************************
#;
#; Copyright (C) 2008-2024 Advanced Micro Devices, Inc. All rights reserved.
#;
#;******************************************************************************

[defines]
  INF_VERSION                    = 0x00010005
  BASE_NAME                      = AmdLegacyInterrupt
  FILE_GUID                      = bfe44bbe-223a-44d3-bcea-89e820944036
  MODULE_TYPE                    = DXE_DRIVER
  VERSION_STRING                 = 1.0
  ENTRY_POINT                    = AmdInstallLegacyInterrupt


[sources.common]
  LegacyInterrupt.c
  LegacyInterrupt.h

[sources.ia32]

[sources.x64]

[LibraryClasses.common.DXE_DRIVER]
  BaseLib
  UefiLib
  HobLib
  PrintLib

[LibraryClasses]
  FchDxeLibV9

  UefiDriverEntryPoint
  UefiBootServicesTableLib
  DebugLib

[Guids]

[Protocols]
  gEfiLegacyInterruptProtocolGuid       #PRODUCED

[Packages]
  MdePkg/MdePkg.dec
  IntelFrameworkPkg/IntelFrameworkPkg.dec
  AgesaModulePkg/AgesaModuleFchPkg.dec
  AgesaModulePkg/Fch/Kunlun/FchKunlun.dec

[Depex]
 gAmdFchKunlunDepexProtocolGuid



