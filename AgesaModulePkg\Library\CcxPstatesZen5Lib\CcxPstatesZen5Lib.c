/*****************************************************************************
 * Copyright (C) 2008-2024 Advanced Micro Devices, Inc. All rights reserved.
 *
*****************************************************************************
*/
/* $NoKeywords:$ */
/**
 * @file
 *
 * AMD Ccx Pstates Library
 *
 *
 * @xrefitem bom "File Content Label" "Release Content"
 * @e project:      AGESA
 * @e sub-project:  Lib
 * @e \$Revision$   @e \$Date$
 *
 */


/*----------------------------------------------------------------------------------------
 *                             M O D U L E S    U S E D
 *----------------------------------------------------------------------------------------
 */
#include <Library/BaseLib.h>
#include <AGESA.h>
#include <cpuRegisters.h>
#include <CcxRegistersZen5.h>
#include <Filecode.h>
#include <Library/AmdBaseLib.h>
#include <Library/CcxPstatesLib.h>
#include <Library/CcxIdsCustomPstatesLib.h>

/*----------------------------------------------------------------------------------------
 *                   D E F I N I T I O N S    A N D    M A C R O S
 *----------------------------------------------------------------------------------------
 */
#define FILECODE LIBRARY_CCXPSTATESZEN5LIB_CCXPSTATESZEN5LIB_FILECODE

/*----------------------------------------------------------------------------------------
 *                  T Y P E D E F S     A N D     S T R U C T U R E S
 *----------------------------------------------------------------------------------------
 */


/*----------------------------------------------------------------------------------------
 *           P R O T O T Y P E S     O F     L O C A L     F U N C T I O N S
 *----------------------------------------------------------------------------------------
 */


/*----------------------------------------------------------------------------------------
 *                          E X P O R T E D    F U N C T I O N S
 *----------------------------------------------------------------------------------------
 */

/* -----------------------------------------------------------------------------*/
/**
 *
 *  CcxGetPstateInfo
 *
 *    Get Pstate information
 *
 *    @param[in]      Socket         Zero-based socket number to check
 *    @param[in]      Pstate         Which P-state to analyze
 *    @param[in]      Frequency      The P-State's frequency in MegaHertz
 *    @param[in]      VoltageInuV    The P-State's voltage in uV
 *    @param[in]      PowerInmW      The Power in milliWatts of that P-State
 *    @param[in]      StdHeader      Standard Head Pointer
 *
 *    @retval         TRUE           Get Pstate information successful.
 *    @retval         FALSE          Pstate is disabled
 *
 */
BOOLEAN
CcxGetPstateInfo (
  IN       UINTN               Socket,
  IN       CCX_PSTATE          Pstate,
     OUT   UINTN              *Frequency,
     OUT   UINTN              *VoltageInuV,
     OUT   UINTN              *PowerInmW,
  IN       AMD_CONFIG_PARAMS  *StdHeader
  )
{
  UINT32    CpuFid;
  UINT32    CpuVid;
  UINT32    IddValue;
  UINT32    IddDiv;
  UINT32    PstateNumber;
  UINT32    P0Vid;
  UINT32    P0Freq;
  UINT32    MaxCpuCofMhz;
  PSTATE_MSR  PstateMsr;
  PWR_MGMT_MISC_MSR PmgtMisc;

  PstateNumber = Pstate < HwPstate0 ? (UINT32) Pstate : (UINT32) Pstate - HwPstate0;
  PstateMsr.Value = AsmReadMsr64 (MSR_PSTATE_0 + PstateNumber);

  if (PstateMsr.Field.PstateEn == 0) {
    return FALSE;
  }

  IDS_HDT_CONSOLE (CPU_TRACE, "  Zen5GetPstateFrequency - P%d\n", PstateNumber);

  CpuFid   = (UINT32) PstateMsr.Field.CpuFid;
  CpuVid   = ((UINT32) PstateMsr.Field.CpuVid + ((UINT32) PstateMsr.Field.CpuVid_8 << 8));
  IddValue = (UINT32) PstateMsr.Field.IddValue;
  IddDiv   = (UINT32) PstateMsr.Field.IddDiv;

  *Frequency = (UINTN) (5 * CpuFid);

  if ((PstateNumber == 0) && (PcdGet8 (PcdAmdCcxP0Setting) == CCX_IDS_CORE_PSTATES_CUSTOM)) {
    P0Freq = PcdGet32 (PcdAmdCcxP0Freq);
    P0Vid = (UINT32) PcdGet32 (PcdAmdCcxP0Vid32);
    if ((P0Vid != 0) && (P0Vid != PcdGet32 (PcdAmdCcxPxAutoVid))) {
      CpuVid = P0Vid & 0x1FF;
    }
    if ((P0Freq != 0) && (P0Freq != PcdGet32 (PcdAmdCcxPxAutoFreq))) {
      PmgtMisc.Value = AsmReadMsr64 (MSR_PWR_MGMT_MISC);
      MaxCpuCofMhz = (UINT32) ((PmgtMisc.Field.MaxCpuCof * 100) + (PmgtMisc.Field.MaxCpuCofPlus50 * 50));

      if ((PmgtMisc.Field.MaxCpuCof != 0) && (P0Freq > MaxCpuCofMhz)) {
        IDS_HDT_CONSOLE (CPU_TRACE, "  P0Freq %d exceeds Frequency Max limit of %d\n", P0Freq, MaxCpuCofMhz);
        P0Freq = MaxCpuCofMhz;
      }
      *Frequency = P0Freq;
    }
  }
  IDS_HDT_CONSOLE (CPU_TRACE, "    FrequencyInMHz=%d, CpuFid=%d, CpuVid=%d\n", *Frequency, CpuFid, CpuVid);

  // Voltage
  if (CpuVid == 0) {
    *VoltageInuV = 0;
  } else {
    *VoltageInuV = 250000L + ((UINTN)5000L * (CpuVid - 1));
  }
  IDS_HDT_CONSOLE (CPU_TRACE, "    SVI3 VoltageInuV=0x%x\n", *VoltageInuV);

  // PowerInmW
  *PowerInmW = (*VoltageInuV) / 10 * IddValue;

  switch (IddDiv) {
  case 0:
    *PowerInmW = *PowerInmW / 100L;
    break;
  case 1:
    *PowerInmW = *PowerInmW / 1000L;
    break;
  case 2:
    *PowerInmW = *PowerInmW / 10000L;
    break;
  default:
    // IddDiv is set to an undefined value.  This is due to either a misfused CPU, or
    // an invalid P-state MSR write.
    ASSERT (FALSE);
    *PowerInmW = 0;
    break;
  }
  IDS_HDT_CONSOLE (CPU_TRACE, "    PowerInmW=0x%x\n", *PowerInmW);
  return TRUE;
}

/* -----------------------------------------------------------------------------*/
/**
 *
 *  CcxGetPstateNumber
 *
 *    Get number of Pstate
 *
 *    @param[in]      Socket               Zero-based socket number to check
 *    @param[in]      NumberOfBoostPstate  Number of boost Pstate
 *    @param[in]      NumberOfHwPstate     Number of hardware Pstate
 *    @param[in]      StdHeader            Standard Head Pointer
 *
 */
VOID
CcxGetPstateNumber (
  IN       UINTN               Socket,
     OUT   UINTN              *NumberOfBoostPstate,
     OUT   UINTN              *NumberOfHwPstate,
  IN       AMD_CONFIG_PARAMS  *StdHeader
  )
{
  PSTATE_CURLIM_MSR  CurLim;

  *NumberOfBoostPstate = 0;

  CurLim.Value = AsmReadMsr64 (MSR_PSTATE_CURRENT_LIMIT);
  *NumberOfHwPstate = (UINTN) CurLim.Field.PstateMaxVal;
}

/* -----------------------------------------------------------------------------*/
/**
 *
 *  CcxSwitchPstate
 *
 *    Switch to a certain Pstate
 *
 *    @param[in]      SwitchPstate            Target Pstate & wait for completion
 *
 */
VOID
CcxSwitchPstate (
  IN       CCX_SWITCH_PSTATE *SwitchPstate
  )
{
  PSTATE_MSR           PstateMsr;
  HW_PSTATE_STATUS_MSR HwPstateSts;

  PstateMsr.Value = AsmReadMsr64 (MSR_PSTATE_0 + (UINT32) (SwitchPstate->Pstate));
  if (PstateMsr.Field.PstateEn == 0) {
    ASSERT (FALSE);
    return;
  }

  AsmWriteMsr64 (MSR_PSTATE_CTL, SwitchPstate->Pstate);

  if (SwitchPstate->WaitForCompletion) {
    if (SwitchPstate->Pstate != 0) {
      // Switch to non-P0, check FID & DID
      do {
        HwPstateSts.Value = AsmReadMsr64 (MSR_HW_PSTATE_STATUS);
      } while ((HwPstateSts.Field.CurCpuFid != PstateMsr.Field.CpuFid));
    } else {
      // Switch to P0, check current frequency >= P0
      do {
        HwPstateSts.Value = AsmReadMsr64 (MSR_HW_PSTATE_STATUS);
      } while ((UINT32)HwPstateSts.Field.CurCpuFid < (UINT32)PstateMsr.Field.CpuFid);
    }
  }
}

/* -----------------------------------------------------------------------------*/
/**
 *
 *  CcxGetCurrentPstate
 *
 *    Get current Pstate
 *
 *    @retval  UINT8    current Pstate
 *
 */
UINT8
CcxGetCurrentPstate (
  )
{
  PSTATE_STS_MSR PstateSts;

  PstateSts.Value = AsmReadMsr64 (MSR_PSTATE_STS);
  return (UINT8) PstateSts.Field.CurPstate;
}

/*---------------------------------------------------------------------------------------
 *                           L O C A L    F U N C T I O N S
 *---------------------------------------------------------------------------------------
 */


