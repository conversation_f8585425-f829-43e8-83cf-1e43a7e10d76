#;*****************************************************************************
#;
#; Copyright (C) 2008-2024 Advanced Micro Devices, Inc. All rights reserved.
#;
#;******************************************************************************
[Defines]
  INF_VERSION                    = 0x00010005
  BASE_NAME                      = FchSpiAccessRom3V2Lib
  FILE_GUID                      = 11d6852d-7e22-43fc-bd72-b50a0e13dd6b
  MODULE_TYPE                    = DXE_DRIVER
  VERSION_STRING                 = 1.0
  LIBRARY_CLASS                  = FchSpiAccessLib|DXE_DRIVER DXE_CORE DXE_RUNTIME_DRIVER UEFI_DRIVER SMM_CORE DXE_SMM_DRIVER UEFI_APPLICATION
  CONSTRUCTOR                    = FchSpiAccessV2LibConstructor

#
# The following information is for reference only and not required by the build tools.
#
#  VALID_ARCHITECTURES           = IA32 X64
#
#
#  This instance for SPI ROM READ accordting to SPI ROM3 window mapping configuration on DXE phase, do not support runtime yet.

[Sources]
  FchSpiAccessCommon.c
  FchSpiAccessRom3V2Lib.c

[Packages]
  AgesaModulePkg/AgesaModuleFchPkg.dec
  MdePkg/MdePkg.dec
  AgesaPkg/AgesaPkg.dec

[LibraryClasses]
  UefiBootServicesTableLib
  IoLib
  PciLib
  FchBaseLib
  DebugLib
  BaseMemoryLib
  AmdBaseLib

