/*****************************************************************************
 *
 * Copyright (C) 2008-2024 Advanced Micro Devices, Inc. All rights reserved.
 *
 *******************************************************************************
 **/

#ifndef _SST_H_
#define _SST_H_


/***********************************************************
* Register Name : SION_WRAPPER_CFG_SSTSION_GLUE_CG_LCLK_CTRL_SOFT_OVERRIDE_CLK
************************************************************/

#define SION_WRAPPER_CFG_SSTSION_GLUE_CG_LCLK_CTRL_SOFT_OVERRIDE_CLK_CFG_SSTSION_GLUE_CG_LCLK_CTRL_0_SOFT_OVERRIDE_CLK0_OFFSET 0
#define SION_WRAPPER_CFG_SSTSION_GLUE_CG_LCLK_CTRL_SOFT_OVERRIDE_CLK_CFG_SSTSION_GLUE_CG_LCLK_CTRL_0_SOFT_OVERRIDE_CLK0_MASK 0x1

#define SION_WRAPPER_CFG_SSTSION_GLUE_CG_LCLK_CTRL_SOFT_OVERRIDE_CLK_CFG_SSTSION_GLUE_CG_LCLK_CTRL_0_SOFT_OVERRIDE_CLK1_OFFSET 1
#define SION_WRAPPER_CFG_SSTSION_GLUE_CG_LCLK_CTRL_SOFT_OVERRIDE_CLK_CFG_SSTSION_GLUE_CG_LCLK_CTRL_0_SOFT_OVERRIDE_CLK1_MASK 0x2

#define SION_WRAPPER_CFG_SSTSION_GLUE_CG_LCLK_CTRL_SOFT_OVERRIDE_CLK_CFG_SSTSION_GLUE_CG_LCLK_CTRL_0_SOFT_OVERRIDE_CLK2_OFFSET 2
#define SION_WRAPPER_CFG_SSTSION_GLUE_CG_LCLK_CTRL_SOFT_OVERRIDE_CLK_CFG_SSTSION_GLUE_CG_LCLK_CTRL_0_SOFT_OVERRIDE_CLK2_MASK 0x4

#define SION_WRAPPER_CFG_SSTSION_GLUE_CG_LCLK_CTRL_SOFT_OVERRIDE_CLK_CFG_SSTSION_GLUE_CG_LCLK_CTRL_0_SOFT_OVERRIDE_CLK3_OFFSET 3
#define SION_WRAPPER_CFG_SSTSION_GLUE_CG_LCLK_CTRL_SOFT_OVERRIDE_CLK_CFG_SSTSION_GLUE_CG_LCLK_CTRL_0_SOFT_OVERRIDE_CLK3_MASK 0x8

#define SION_WRAPPER_CFG_SSTSION_GLUE_CG_LCLK_CTRL_SOFT_OVERRIDE_CLK_CFG_SSTSION_GLUE_CG_LCLK_CTRL_0_SOFT_OVERRIDE_CLK4_OFFSET 4
#define SION_WRAPPER_CFG_SSTSION_GLUE_CG_LCLK_CTRL_SOFT_OVERRIDE_CLK_CFG_SSTSION_GLUE_CG_LCLK_CTRL_0_SOFT_OVERRIDE_CLK4_MASK 0x10
#define SION_WRAPPER_CFG_SSTSION_GLUE_CG_LCLK_CTRL_SOFT_OVERRIDE_CLK_CFG_SSTSION_GLUE_CG_LCLK_CTRL_0_SOFT_OVERRIDE_CLK4_DEFAULT     0x1

#define SION_WRAPPER_CFG_SSTSION_GLUE_CG_LCLK_CTRL_SOFT_OVERRIDE_CLK_CFG_SSTSION_GLUE_CG_LCLK_CTRL_0_SOFT_OVERRIDE_CLK5_OFFSET 5
#define SION_WRAPPER_CFG_SSTSION_GLUE_CG_LCLK_CTRL_SOFT_OVERRIDE_CLK_CFG_SSTSION_GLUE_CG_LCLK_CTRL_0_SOFT_OVERRIDE_CLK5_MASK 0x20

#define SION_WRAPPER_CFG_SSTSION_GLUE_CG_LCLK_CTRL_SOFT_OVERRIDE_CLK_CFG_SSTSION_GLUE_CG_LCLK_CTRL_0_SOFT_OVERRIDE_CLK6_OFFSET 6
#define SION_WRAPPER_CFG_SSTSION_GLUE_CG_LCLK_CTRL_SOFT_OVERRIDE_CLK_CFG_SSTSION_GLUE_CG_LCLK_CTRL_0_SOFT_OVERRIDE_CLK6_MASK 0x40

#define SION_WRAPPER_CFG_SSTSION_GLUE_CG_LCLK_CTRL_SOFT_OVERRIDE_CLK_CFG_SSTSION_GLUE_CG_LCLK_CTRL_0_SOFT_OVERRIDE_CLK7_OFFSET 7
#define SION_WRAPPER_CFG_SSTSION_GLUE_CG_LCLK_CTRL_SOFT_OVERRIDE_CLK_CFG_SSTSION_GLUE_CG_LCLK_CTRL_0_SOFT_OVERRIDE_CLK7_MASK 0x80

#define SION_WRAPPER_CFG_SSTSION_GLUE_CG_LCLK_CTRL_SOFT_OVERRIDE_CLK_CFG_SSTSION_GLUE_CG_LCLK_CTRL_0_SOFT_OVERRIDE_CLK8_OFFSET 8
#define SION_WRAPPER_CFG_SSTSION_GLUE_CG_LCLK_CTRL_SOFT_OVERRIDE_CLK_CFG_SSTSION_GLUE_CG_LCLK_CTRL_0_SOFT_OVERRIDE_CLK8_MASK 0x100

#define SION_WRAPPER_CFG_SSTSION_GLUE_CG_LCLK_CTRL_SOFT_OVERRIDE_CLK_CFG_SSTSION_GLUE_CG_LCLK_CTRL_0_SOFT_OVERRIDE_CLK9_OFFSET 9
#define SION_WRAPPER_CFG_SSTSION_GLUE_CG_LCLK_CTRL_SOFT_OVERRIDE_CLK_CFG_SSTSION_GLUE_CG_LCLK_CTRL_0_SOFT_OVERRIDE_CLK9_MASK 0x200

#define SION_WRAPPER_CFG_SSTSION_GLUE_CG_LCLK_CTRL_SOFT_OVERRIDE_CLK_Reserved_15_10_OFFSET 10
#define SION_WRAPPER_CFG_SSTSION_GLUE_CG_LCLK_CTRL_SOFT_OVERRIDE_CLK_Reserved_15_10_MASK 0xfc00

#define SION_WRAPPER_CFG_SSTSION_GLUE_CG_LCLK_CTRL_SOFT_OVERRIDE_CLK_Reserved_25_16_OFFSET 16
#define SION_WRAPPER_CFG_SSTSION_GLUE_CG_LCLK_CTRL_SOFT_OVERRIDE_CLK_Reserved_25_16_MASK 0x3ff0000

#define SION_WRAPPER_CFG_SSTSION_GLUE_CG_LCLK_CTRL_SOFT_OVERRIDE_CLK_Reserved_31_26_OFFSET 26
#define SION_WRAPPER_CFG_SSTSION_GLUE_CG_LCLK_CTRL_SOFT_OVERRIDE_CLK_Reserved_31_26_MASK 0xfc000000

typedef union {
  struct {
    UINT32  CFG_SSTSION_GLUE_CG_LCLK_CTRL_0_SOFT_OVERRIDE_CLK0:1;
    UINT32  CFG_SSTSION_GLUE_CG_LCLK_CTRL_0_SOFT_OVERRIDE_CLK1:1;
    UINT32  CFG_SSTSION_GLUE_CG_LCLK_CTRL_0_SOFT_OVERRIDE_CLK2:1;
    UINT32  CFG_SSTSION_GLUE_CG_LCLK_CTRL_0_SOFT_OVERRIDE_CLK3:1;
    UINT32  CFG_SSTSION_GLUE_CG_LCLK_CTRL_0_SOFT_OVERRIDE_CLK4:1;
    UINT32  CFG_SSTSION_GLUE_CG_LCLK_CTRL_0_SOFT_OVERRIDE_CLK5:1;
    UINT32  CFG_SSTSION_GLUE_CG_LCLK_CTRL_0_SOFT_OVERRIDE_CLK6:1;
    UINT32  CFG_SSTSION_GLUE_CG_LCLK_CTRL_0_SOFT_OVERRIDE_CLK7:1;
    UINT32  CFG_SSTSION_GLUE_CG_LCLK_CTRL_0_SOFT_OVERRIDE_CLK8:1;
    UINT32  CFG_SSTSION_GLUE_CG_LCLK_CTRL_0_SOFT_OVERRIDE_CLK9:1;
    UINT32                                      Reserved_15_10:6;
    UINT32                                      Reserved_25_16:10;
    UINT32                                      Reserved_31_26:6;
  } Field;
  UINT32 Value;
} SION_WRAPPER_CFG_SSTSION_GLUE_CG_LCLK_CTRL_SOFT_OVERRIDE_CLK_STRUCT;

#define SMN_SION_WRAPPER_CFG_SSTSION_GLUE_CG_LCLK_CTRL_SOFT_OVERRIDE_CLK_ADDRESS 0x17400404UL
#define SMN_SST0NBIO0_SION_WRAPPER_CFG_SSTSION_GLUE_CG_LCLK_CTRL_SOFT_OVERRIDE_CLK_ADDRESS 0x17400404UL
#define SMN_SST1NBIO0_SION_WRAPPER_CFG_SSTSION_GLUE_CG_LCLK_CTRL_SOFT_OVERRIDE_CLK_ADDRESS 0x17500404UL
#define SMN_SST1NBIO1_SION_WRAPPER_CFG_SSTSION_GLUE_CG_LCLK_CTRL_SOFT_OVERRIDE_CLK_ADDRESS 0x17700404UL


/***********************************************************
* Register Name : SST_CLOCK_CTRL
************************************************************/

#define SST_CLOCK_CTRL_TXCLKGATEEn_OFFSET                      0
#define SST_CLOCK_CTRL_TXCLKGATEEn_MASK                        0x1
#define SST_CLOCK_CTRL_TXCLKGATEEn_DEFAULT     0x1

#define SST_CLOCK_CTRL_Reserved_7_1_OFFSET                     1
#define SST_CLOCK_CTRL_Reserved_7_1_MASK                       0xfe

#define SST_CLOCK_CTRL_PCTRL_IDLE_TIME_OFFSET                  8
#define SST_CLOCK_CTRL_PCTRL_IDLE_TIME_MASK                    0xff00

#define SST_CLOCK_CTRL_RXCLKGATEEn_OFFSET                      16
#define SST_CLOCK_CTRL_RXCLKGATEEn_MASK                        0x10000
#define SST_CLOCK_CTRL_RXCLKGATEEn_DEFAULT     0x1

#define SST_CLOCK_CTRL_Reserved_31_17_OFFSET                   17
#define SST_CLOCK_CTRL_Reserved_31_17_MASK                     0xfffe0000

typedef union {
  struct {
    UINT32                                         TXCLKGATEEn:1;
    UINT32                                        Reserved_7_1:7;
    UINT32                                     PCTRL_IDLE_TIME:8;
    UINT32                                         RXCLKGATEEn:1;
    UINT32                                      Reserved_31_17:15;
  } Field;
  UINT32 Value;
} SST_CLOCK_CTRL_STRUCT;

#define SMN_SST_CLOCK_CTRL_ADDRESS                                    0x17400004UL
#define SMN_SST0NBIO0_SST_CLOCK_CTRL_ADDRESS                          0x17400004UL
#define SMN_SST1NBIO0_SST_CLOCK_CTRL_ADDRESS                          0x17500004UL
#define SMN_SST1NBIO1_SST_CLOCK_CTRL_ADDRESS                          0x17700004UL


/***********************************************************
* Register Name : SST_SION_PERF_CNT_CNTL0
************************************************************/

#define SST_SION_PERF_CNT_CNTL0_SST_SION_CNT_EN_OFFSET         0
#define SST_SION_PERF_CNT_CNTL0_SST_SION_CNT_EN_MASK           0x1

#define SST_SION_PERF_CNT_CNTL0_SST_SION_SHADOW_WR_OFFSET      1
#define SST_SION_PERF_CNT_CNTL0_SST_SION_SHADOW_WR_MASK        0x2

#define SST_SION_PERF_CNT_CNTL0_SST_SION_PERF_RESET_OFFSET     2
#define SST_SION_PERF_CNT_CNTL0_SST_SION_PERF_RESET_MASK       0x4

#define SST_SION_PERF_CNT_CNTL0_Reserved_7_3_OFFSET            3
#define SST_SION_PERF_CNT_CNTL0_Reserved_7_3_MASK              0xf8

#define SST_SION_PERF_CNT_CNTL0_SST_SION_SHADOW_DELAY_OFFSET   8
#define SST_SION_PERF_CNT_CNTL0_SST_SION_SHADOW_DELAY_MASK     0xf00

#define SST_SION_PERF_CNT_CNTL0_Reserved_14_12_OFFSET          12
#define SST_SION_PERF_CNT_CNTL0_Reserved_14_12_MASK            0x7000

#define SST_SION_PERF_CNT_CNTL0_SST_SION_SHADOW_DELAY_EN_OFFSET 15
#define SST_SION_PERF_CNT_CNTL0_SST_SION_SHADOW_DELAY_EN_MASK  0x8000

#define SST_SION_PERF_CNT_CNTL0_SST_SION_PERF_RESET_DELAY_OFFSET 16
#define SST_SION_PERF_CNT_CNTL0_SST_SION_PERF_RESET_DELAY_MASK 0xf0000

#define SST_SION_PERF_CNT_CNTL0_Reserved_22_20_OFFSET          20
#define SST_SION_PERF_CNT_CNTL0_Reserved_22_20_MASK            0x700000

#define SST_SION_PERF_CNT_CNTL0_SST_SION_PERF_RESET_DELAY_EN_OFFSET 23
#define SST_SION_PERF_CNT_CNTL0_SST_SION_PERF_RESET_DELAY_EN_MASK 0x800000

#define SST_SION_PERF_CNT_CNTL0_Reserved_31_24_OFFSET          24
#define SST_SION_PERF_CNT_CNTL0_Reserved_31_24_MASK            0xff000000

typedef union {
  struct {
    UINT32                                     SST_SION_CNT_EN:1;
    UINT32                                  SST_SION_SHADOW_WR:1;
    UINT32                                 SST_SION_PERF_RESET:1;
    UINT32                                        Reserved_7_3:5;
    UINT32                               SST_SION_SHADOW_DELAY:4;
    UINT32                                      Reserved_14_12:3;
    UINT32                            SST_SION_SHADOW_DELAY_EN:1;
    UINT32                           SST_SION_PERF_RESET_DELAY:4;
    UINT32                                      Reserved_22_20:3;
    UINT32                        SST_SION_PERF_RESET_DELAY_EN:1;
    UINT32                                      Reserved_31_24:8;
  } Field;
  UINT32 Value;
} SST_SION_PERF_CNT_CNTL0_STRUCT;

#define SMN_SST_SION_PERF_CNT_CNTL0_ADDRESS                           0x17401094UL
#define SMN_SST0NBIO0_SST_SION_PERF_CNT_CNTL0_ADDRESS                 0x17401094UL
#define SMN_SST1NBIO0_SST_SION_PERF_CNT_CNTL0_ADDRESS                 0x17501094UL
#define SMN_SST1NBIO1_SST_SION_PERF_CNT_CNTL0_ADDRESS                 0x17701094UL


/***********************************************************
* Register Name : SST_SION_PERF_CNT_CNTL1
************************************************************/

#define SST_SION_PERF_CNT_CNTL1_SST_SION_EVENT0_SEL_OFFSET     0
#define SST_SION_PERF_CNT_CNTL1_SST_SION_EVENT0_SEL_MASK       0xff

#define SST_SION_PERF_CNT_CNTL1_SST_SION_EVENT1_SEL_OFFSET     8
#define SST_SION_PERF_CNT_CNTL1_SST_SION_EVENT1_SEL_MASK       0xff00

#define SST_SION_PERF_CNT_CNTL1_SST_SION_EVENT2_SEL_OFFSET     16
#define SST_SION_PERF_CNT_CNTL1_SST_SION_EVENT2_SEL_MASK       0xff0000

#define SST_SION_PERF_CNT_CNTL1_SST_SION_EVENT3_SEL_OFFSET     24
#define SST_SION_PERF_CNT_CNTL1_SST_SION_EVENT3_SEL_MASK       0xff000000

typedef union {
  struct {
    UINT32                                 SST_SION_EVENT0_SEL:8;
    UINT32                                 SST_SION_EVENT1_SEL:8;
    UINT32                                 SST_SION_EVENT2_SEL:8;
    UINT32                                 SST_SION_EVENT3_SEL:8;
  } Field;
  UINT32 Value;
} SST_SION_PERF_CNT_CNTL1_STRUCT;

#define SMN_SST_SION_PERF_CNT_CNTL1_ADDRESS                           0x17401098UL
#define SMN_SST0NBIO0_SST_SION_PERF_CNT_CNTL1_ADDRESS                 0x17401098UL
#define SMN_SST1NBIO0_SST_SION_PERF_CNT_CNTL1_ADDRESS                 0x17501098UL
#define SMN_SST1NBIO1_SST_SION_PERF_CNT_CNTL1_ADDRESS                 0x17701098UL


/***********************************************************
* Register Name : SST_SION_PERF_COUNT0
************************************************************/

#define SST_SION_PERF_COUNT0_SST_SION_COUNTER0_OFFSET          0
#define SST_SION_PERF_COUNT0_SST_SION_COUNTER0_MASK            0xffffffff

typedef union {
  struct {
    UINT32                                   SST_SION_COUNTER0:32;
  } Field;
  UINT32 Value;
} SST_SION_PERF_COUNT0_STRUCT;

#define SMN_SST_SION_PERF_COUNT0_ADDRESS                              0x1740109cUL
#define SMN_SST0NBIO0_SST_SION_PERF_COUNT0_ADDRESS                    0x1740109cUL
#define SMN_SST1NBIO0_SST_SION_PERF_COUNT0_ADDRESS                    0x1750109cUL
#define SMN_SST1NBIO1_SST_SION_PERF_COUNT0_ADDRESS                    0x1770109cUL


/***********************************************************
* Register Name : SST_SION_PERF_COUNT0_UPPER
************************************************************/

#define SST_SION_PERF_COUNT0_UPPER_SST_SION_COUNTER0_UPPER_OFFSET 0
#define SST_SION_PERF_COUNT0_UPPER_SST_SION_COUNTER0_UPPER_MASK 0xffffff

#define SST_SION_PERF_COUNT0_UPPER_Reserved_31_24_OFFSET       24
#define SST_SION_PERF_COUNT0_UPPER_Reserved_31_24_MASK         0xff000000

typedef union {
  struct {
    UINT32                             SST_SION_COUNTER0_UPPER:24;
    UINT32                                      Reserved_31_24:8;
  } Field;
  UINT32 Value;
} SST_SION_PERF_COUNT0_UPPER_STRUCT;

#define SMN_SST_SION_PERF_COUNT0_UPPER_ADDRESS                        0x174010a0UL
#define SMN_SST0NBIO0_SST_SION_PERF_COUNT0_UPPER_ADDRESS              0x174010a0UL
#define SMN_SST1NBIO0_SST_SION_PERF_COUNT0_UPPER_ADDRESS              0x175010a0UL
#define SMN_SST1NBIO1_SST_SION_PERF_COUNT0_UPPER_ADDRESS              0x177010a0UL


/***********************************************************
* Register Name : SST_SION_PERF_COUNT1
************************************************************/

#define SST_SION_PERF_COUNT1_SST_SION_COUNTER1_OFFSET          0
#define SST_SION_PERF_COUNT1_SST_SION_COUNTER1_MASK            0xffffffff

typedef union {
  struct {
    UINT32                                   SST_SION_COUNTER1:32;
  } Field;
  UINT32 Value;
} SST_SION_PERF_COUNT1_STRUCT;

#define SMN_SST_SION_PERF_COUNT1_ADDRESS                              0x174010a4UL
#define SMN_SST0NBIO0_SST_SION_PERF_COUNT1_ADDRESS                    0x174010a4UL
#define SMN_SST1NBIO0_SST_SION_PERF_COUNT1_ADDRESS                    0x175010a4UL
#define SMN_SST1NBIO1_SST_SION_PERF_COUNT1_ADDRESS                    0x177010a4UL


/***********************************************************
* Register Name : SST_SION_PERF_COUNT1_UPPER
************************************************************/

#define SST_SION_PERF_COUNT1_UPPER_SST_SION_COUNTER1_UPPER_OFFSET 0
#define SST_SION_PERF_COUNT1_UPPER_SST_SION_COUNTER1_UPPER_MASK 0xffffff

#define SST_SION_PERF_COUNT1_UPPER_Reserved_31_24_OFFSET       24
#define SST_SION_PERF_COUNT1_UPPER_Reserved_31_24_MASK         0xff000000

typedef union {
  struct {
    UINT32                             SST_SION_COUNTER1_UPPER:24;
    UINT32                                      Reserved_31_24:8;
  } Field;
  UINT32 Value;
} SST_SION_PERF_COUNT1_UPPER_STRUCT;

#define SMN_SST_SION_PERF_COUNT1_UPPER_ADDRESS                        0x174010a8UL
#define SMN_SST0NBIO0_SST_SION_PERF_COUNT1_UPPER_ADDRESS              0x174010a8UL
#define SMN_SST1NBIO0_SST_SION_PERF_COUNT1_UPPER_ADDRESS              0x175010a8UL
#define SMN_SST1NBIO1_SST_SION_PERF_COUNT1_UPPER_ADDRESS              0x177010a8UL


/***********************************************************
* Register Name : SST_SION_PERF_COUNT2
************************************************************/

#define SST_SION_PERF_COUNT2_SST_SION_COUNTER2_OFFSET          0
#define SST_SION_PERF_COUNT2_SST_SION_COUNTER2_MASK            0xffffffff

typedef union {
  struct {
    UINT32                                   SST_SION_COUNTER2:32;
  } Field;
  UINT32 Value;
} SST_SION_PERF_COUNT2_STRUCT;

#define SMN_SST_SION_PERF_COUNT2_ADDRESS                              0x174010acUL
#define SMN_SST0NBIO0_SST_SION_PERF_COUNT2_ADDRESS                    0x174010acUL
#define SMN_SST1NBIO0_SST_SION_PERF_COUNT2_ADDRESS                    0x175010acUL
#define SMN_SST1NBIO1_SST_SION_PERF_COUNT2_ADDRESS                    0x177010acUL


/***********************************************************
* Register Name : SST_SION_PERF_COUNT2_UPPER
************************************************************/

#define SST_SION_PERF_COUNT2_UPPER_SST_SION_COUNTER2_UPPER_OFFSET 0
#define SST_SION_PERF_COUNT2_UPPER_SST_SION_COUNTER2_UPPER_MASK 0xffffff

#define SST_SION_PERF_COUNT2_UPPER_Reserved_31_24_OFFSET       24
#define SST_SION_PERF_COUNT2_UPPER_Reserved_31_24_MASK         0xff000000

typedef union {
  struct {
    UINT32                             SST_SION_COUNTER2_UPPER:24;
    UINT32                                      Reserved_31_24:8;
  } Field;
  UINT32 Value;
} SST_SION_PERF_COUNT2_UPPER_STRUCT;

#define SMN_SST_SION_PERF_COUNT2_UPPER_ADDRESS                        0x174010b0UL
#define SMN_SST0NBIO0_SST_SION_PERF_COUNT2_UPPER_ADDRESS              0x174010b0UL
#define SMN_SST1NBIO0_SST_SION_PERF_COUNT2_UPPER_ADDRESS              0x175010b0UL
#define SMN_SST1NBIO1_SST_SION_PERF_COUNT2_UPPER_ADDRESS              0x177010b0UL


/***********************************************************
* Register Name : SST_SION_PERF_COUNT3
************************************************************/

#define SST_SION_PERF_COUNT3_SST_SION_COUNTER3_OFFSET          0
#define SST_SION_PERF_COUNT3_SST_SION_COUNTER3_MASK            0xffffffff

typedef union {
  struct {
    UINT32                                   SST_SION_COUNTER3:32;
  } Field;
  UINT32 Value;
} SST_SION_PERF_COUNT3_STRUCT;

#define SMN_SST_SION_PERF_COUNT3_ADDRESS                              0x174010b4UL
#define SMN_SST0NBIO0_SST_SION_PERF_COUNT3_ADDRESS                    0x174010b4UL
#define SMN_SST1NBIO0_SST_SION_PERF_COUNT3_ADDRESS                    0x175010b4UL
#define SMN_SST1NBIO1_SST_SION_PERF_COUNT3_ADDRESS                    0x177010b4UL


/***********************************************************
* Register Name : SST_SION_PERF_COUNT3_UPPER
************************************************************/

#define SST_SION_PERF_COUNT3_UPPER_SST_SION_COUNTER3_UPPER_OFFSET 0
#define SST_SION_PERF_COUNT3_UPPER_SST_SION_COUNTER3_UPPER_MASK 0xffffff

#define SST_SION_PERF_COUNT3_UPPER_Reserved_31_24_OFFSET       24
#define SST_SION_PERF_COUNT3_UPPER_Reserved_31_24_MASK         0xff000000

typedef union {
  struct {
    UINT32                             SST_SION_COUNTER3_UPPER:24;
    UINT32                                      Reserved_31_24:8;
  } Field;
  UINT32 Value;
} SST_SION_PERF_COUNT3_UPPER_STRUCT;

#define SMN_SST_SION_PERF_COUNT3_UPPER_ADDRESS                        0x174010b8UL
#define SMN_SST0NBIO0_SST_SION_PERF_COUNT3_UPPER_ADDRESS              0x174010b8UL
#define SMN_SST1NBIO0_SST_SION_PERF_COUNT3_UPPER_ADDRESS              0x175010b8UL
#define SMN_SST1NBIO1_SST_SION_PERF_COUNT3_UPPER_ADDRESS              0x177010b8UL


/***********************************************************
* Register Name : CFG_SST_RdRspPoolCredit_Alloc_LO
************************************************************/

#define CFG_SST_RdRspPoolCredit_Alloc_LO_CFG_SST_RdRspPoolCredit_Alloc_LO_OFFSET 0
#define CFG_SST_RdRspPoolCredit_Alloc_LO_CFG_SST_RdRspPoolCredit_Alloc_LO_MASK 0xffffffff

typedef union {
  struct {
    UINT32                    CFG_SST_RdRspPoolCredit_Alloc_LO:32;
  } Field;
  UINT32 Value;
} CFG_SST_RdRspPoolCredit_Alloc_LO_STRUCT;

#define SMN_SST_CFG_SST_RdRspPoolCredit_Alloc_LO_ADDRESS             0x17400418UL
#define SMN_SST0NBIO0_CFG_SST_RdRspPoolCredit_Alloc_LO_ADDRESS       0x17400418UL
#define SMN_SST1NBIO0_CFG_SST_RdRspPoolCredit_Alloc_LO_ADDRESS       0x17500418UL
#define SMN_SST1NBIO1_CFG_SST_RdRspPoolCredit_Alloc_LO_ADDRESS       0x17700418UL


/***********************************************************
* Register Name : SST_FCH_CLKREQb_MAP_CNTL0
************************************************************/

#define SST_FCH_CLKREQb_MAP_CNTL0_SST_FCH_CLKREQB_0_MAP_OFFSET 0
#define SST_FCH_CLKREQb_MAP_CNTL0_SST_FCH_CLKREQB_0_MAP_MASK   0x7f

#define SST_FCH_CLKREQb_MAP_CNTL0_SST_FCH_CLKREQB_1_MAP_OFFSET 7
#define SST_FCH_CLKREQb_MAP_CNTL0_SST_FCH_CLKREQB_1_MAP_MASK   0x3f80

#define SST_FCH_CLKREQb_MAP_CNTL0_SST_FCH_CLKREQB_2_MAP_OFFSET 14
#define SST_FCH_CLKREQb_MAP_CNTL0_SST_FCH_CLKREQB_2_MAP_MASK   0x1fc000

#define SST_FCH_CLKREQb_MAP_CNTL0_SST_FCH_CLKREQB_3_MAP_OFFSET 21
#define SST_FCH_CLKREQb_MAP_CNTL0_SST_FCH_CLKREQB_3_MAP_MASK   0xfe00000

#define SST_FCH_CLKREQb_MAP_CNTL0_SST_FCH_CLKREQB_0_CNTL_MASK_OFFSET 28
#define SST_FCH_CLKREQb_MAP_CNTL0_SST_FCH_CLKREQB_0_CNTL_MASK_MASK 0x10000000

#define SST_FCH_CLKREQb_MAP_CNTL0_SST_FCH_CLKREQB_1_CNTL_MASK_OFFSET 29
#define SST_FCH_CLKREQb_MAP_CNTL0_SST_FCH_CLKREQB_1_CNTL_MASK_MASK 0x20000000

#define SST_FCH_CLKREQb_MAP_CNTL0_SST_FCH_CLKREQB_2_CNTL_MASK_OFFSET 30
#define SST_FCH_CLKREQb_MAP_CNTL0_SST_FCH_CLKREQB_2_CNTL_MASK_MASK 0x40000000

#define SST_FCH_CLKREQb_MAP_CNTL0_SST_FCH_CLKREQB_3_CNTL_MASK_OFFSET 31
#define SST_FCH_CLKREQb_MAP_CNTL0_SST_FCH_CLKREQB_3_CNTL_MASK_MASK 0x80000000

typedef union {
  struct {
    UINT32                               SST_FCH_CLKREQB_0_MAP:7;
    UINT32                               SST_FCH_CLKREQB_1_MAP:7;
    UINT32                               SST_FCH_CLKREQB_2_MAP:7;
    UINT32                               SST_FCH_CLKREQB_3_MAP:7;
    UINT32                         SST_FCH_CLKREQB_0_CNTL_MASK:1;
    UINT32                         SST_FCH_CLKREQB_1_CNTL_MASK:1;
    UINT32                         SST_FCH_CLKREQB_2_CNTL_MASK:1;
    UINT32                         SST_FCH_CLKREQB_3_CNTL_MASK:1;
  } Field;
  UINT32 Value;
} SST_FCH_CLKREQb_MAP_CNTL0_STRUCT;
#define SMN_SST0NBIO0_SST_FCH_CLKREQb_MAP_CNTL0_ADDRESS               0x17404000UL
#define SMN_SST1NBIO0_SST_FCH_CLKREQb_MAP_CNTL0_ADDRESS               0x17504000UL
#define SMN_SST1NBIO1_SST_FCH_CLKREQb_MAP_CNTL0_ADDRESS               0x17704000UL


/***********************************************************
* Register Name : SST_FCH_CLKREQb_MAP_CNTL1
************************************************************/

#define SST_FCH_CLKREQb_MAP_CNTL1_SST_FCH_CLKREQB_4_MAP_OFFSET 0
#define SST_FCH_CLKREQb_MAP_CNTL1_SST_FCH_CLKREQB_4_MAP_MASK   0x7f

#define SST_FCH_CLKREQb_MAP_CNTL1_SST_FCH_CLKREQB_5_MAP_OFFSET 7
#define SST_FCH_CLKREQb_MAP_CNTL1_SST_FCH_CLKREQB_5_MAP_MASK   0x3f80

#define SST_FCH_CLKREQb_MAP_CNTL1_SST_FCH_CLKREQB_6_MAP_OFFSET 14
#define SST_FCH_CLKREQb_MAP_CNTL1_SST_FCH_CLKREQB_6_MAP_MASK   0x1fc000

#define SST_FCH_CLKREQb_MAP_CNTL1_SST_FCH_CLKREQB_7_MAP_OFFSET 21
#define SST_FCH_CLKREQb_MAP_CNTL1_SST_FCH_CLKREQB_7_MAP_MASK   0xfe00000

#define SST_FCH_CLKREQb_MAP_CNTL1_Reserved_31_28_OFFSET        28
#define SST_FCH_CLKREQb_MAP_CNTL1_Reserved_31_28_MASK          0xf0000000

typedef union {
  struct {
    UINT32                               SST_FCH_CLKREQB_4_MAP:7;
    UINT32                               SST_FCH_CLKREQB_5_MAP:7;
    UINT32                               SST_FCH_CLKREQB_6_MAP:7;
    UINT32                               SST_FCH_CLKREQB_7_MAP:7;
    UINT32                                      Reserved_31_28:4;
  } Field;
  UINT32 Value;
} SST_FCH_CLKREQb_MAP_CNTL1_STRUCT;
#define SMN_SST0NBIO0_SST_FCH_CLKREQb_MAP_CNTL1_ADDRESS               0x17404004UL
#define SMN_SST1NBIO0_SST_FCH_CLKREQb_MAP_CNTL1_ADDRESS               0x17504004UL
#define SMN_SST1NBIO1_SST_FCH_CLKREQb_MAP_CNTL1_ADDRESS               0x17704004UL


/***********************************************************
* Register Name : SST_FCH_CLKREQb_MAP_CNTL2
************************************************************/

#define SST_FCH_CLKREQb_MAP_CNTL2_SST_FCH_CLKREQB_8_MAP_OFFSET 0
#define SST_FCH_CLKREQb_MAP_CNTL2_SST_FCH_CLKREQB_8_MAP_MASK   0x7f

#define SST_FCH_CLKREQb_MAP_CNTL2_Reserved_31_7_OFFSET         7
#define SST_FCH_CLKREQb_MAP_CNTL2_Reserved_31_7_MASK           0xffffff80

typedef union {
  struct {
    UINT32                               SST_FCH_CLKREQB_8_MAP:7;
    UINT32                                       Reserved_31_7:25;
  } Field;
  UINT32 Value;
} SST_FCH_CLKREQb_MAP_CNTL2_STRUCT;
#define SMN_SST0NBIO0_SST_FCH_CLKREQb_MAP_CNTL2_ADDRESS               0x17404010UL
#define SMN_SST1NBIO0_SST_FCH_CLKREQb_MAP_CNTL2_ADDRESS               0x17504010UL
#define SMN_SST1NBIO1_SST_FCH_CLKREQb_MAP_CNTL2_ADDRESS               0x17704010UL

#endif /* _SST_H_ */

