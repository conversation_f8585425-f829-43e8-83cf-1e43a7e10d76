/*****************************************************************************
 *
 * Copyright (C) 2008-2024 Advanced Micro Devices, Inc. All rights reserved.
 *
 *******************************************************************************
 */

#include "FchEspiCmdPei.h"

#define FILECODE FCH_COMMON_FCHESPICMDPEI_FCHESPICMDPEI_FILECODE

/**
  * PpiInBandRst - eSPI In Band Reset
  *
  * @param This                   Pointer to an EFI_PEI_ESPI_CMD_PPI structure.
  * @param[in]  EspiBase          Espi MMIO base
  *
  */
VOID
EFIAPI
PeiInBandRst (
  IN CONST EFI_PEI_ESPI_CMD_PPI  *This,
  IN  UINT32                      EspiBase
  )
{
  FchEspiCmd_InBandRst(EspiBase);
}

/**
  *  PeiGetConfiguration - eSPI Get Configuration
  *
  * @param This                   Pointer to an EFI_PEI_ESPI_CMD_PPI structure.
  * @param[in]  EspiBase          Espi MMIO base
  * @param[in]  RegAddr           Slave register address
  *
  * @retval    Register Value
  *
  */
UINT32
EFIAPI
PeiGetConfiguration (
  IN CONST EFI_PEI_ESPI_CMD_PPI  *This,
  IN  UINT32                      EspiBase,
  IN  UINT32                      RegAddr
 )
{
  return (FchEspiCmd_GetConfiguration(EspiBase, RegAddr));
}

/**
  * PeiSetConfiguration - eSPI Set Configuration
  *
  * @param This                   Pointer to an EFI_PEI_ESPI_CMD_PPI structure.
  * @param[in]  EspiBase          Espi MMIO base
  * @param[in]  RegAddr           Slave register address
  * @param[in]  Value             Slave register value
  *
  */
VOID
EFIAPI
PeiSetConfiguration (
  IN CONST EFI_PEI_ESPI_CMD_PPI  *This,
  IN  UINT32                      EspiBase,
  IN  UINT32                      RegAddr,
  IN  UINT32                      Value
)
{
  FchEspiCmd_SetConfiguration(EspiBase, RegAddr, Value);
}

/**
  * PeiSafsFlashRead - eSPI SAFS FLASH Read
  *
  * @param This                   Pointer to an EFI_PEI_ESPI_CMD_PPI structure.
  * @param[in]  EspiBase          Espi MMIO base
  * @param[in]  Address           Address to read
  * @param[in]  Length            Length in byte to read
  * @param[in]  Buffer            Pointer to the data read to
  *
  * @retval EFI_STATUS
  *
  */
EFI_STATUS
EFIAPI
PeiSafsFlashRead (
  IN CONST EFI_PEI_ESPI_CMD_PPI  *This,
  IN  UINT32                      EspiBase,
  IN  UINT32                      Address,
  IN  UINT32                      Length,
  OUT UINT8                       *Buffer
  )
{
  return (FchEspiCmd_SafsFlashRead(EspiBase, Address, Length, Buffer));
}

/**
  * PeiSafsFlashWrite - eSPI SAFS FLASH Write
  *
  * @param  This                  Pointer to an EFI_PEI_ESPI_CMD_PPI structure.
  * @param[in]  EspiBase          Espi MMIO base
  * @param[in]  Address           Address to write
  * @param[in]  Length            Length in byte to write
  * @param[in]  Value             Pointer to the data to write
  *
  * @retval EFI_STATUS
  *
  */
EFI_STATUS
EFIAPI
PeiSafsFlashWrite (
  IN CONST EFI_PEI_ESPI_CMD_PPI  *This,
  IN  UINT32                      EspiBase,
  IN  UINT32                      Address,
  IN  UINT32                      Length,
  IN  UINT8                       *Value
  )
{
  return (FchEspiCmd_SafsFlashWrite(EspiBase, Address, Length, Value));
}

/**
  * PeiSafsFlashErase - eSPI SAFS FLASH Erase
  *
  * @param  This                  Pointer to an EFI_PEI_ESPI_CMD_PPI structure.
  * @param[in]  EspiBase          Espi MMIO base
  * @param[in]  Address           Address to erase
  * @param[in]  Length            Block Size to erase
  *
  *  @retval EFI_STATUS
  *
  */
EFI_STATUS
EFIAPI
PeiSafsFlashErase (
  IN CONST EFI_PEI_ESPI_CMD_PPI  *This,
  IN  UINT32                      EspiBase,
  IN  UINT32                      Address,
  IN  UINT32                      Length
  )
{
  return (FchEspiCmd_SafsFlashErase(EspiBase, Address, Length));
}

/**
  * PeiSafsRpmcOp1 - eSPI SAFS RPMC OP1
  *
  * @param  This                  Pointer to an EFI_ESPI_CMD_PROTOCOL structure.
  * @param[in]  EspiBase          Espi MMIO base
  * @param[in]  RpmcFlashDev      Which RPMC flash device is targeted to
  * @param[in]  Length            Length in byte
  * @param[in]  Data              Pointer to data to send
  *
  * @retval EFI_STATUS
  *
  */
EFI_STATUS
EFIAPI
PeiSafsRpmcOp1 (
  IN CONST EFI_PEI_ESPI_CMD_PPI  *This,
  IN  UINT32                      EspiBase,
  IN  UINT8                       RpmcFlashDev,
  IN  UINT32                      Length,
  OUT UINT8                       *Data
  )
{
  return (FchEspiCmd_SafsRpmcOp1(EspiBase, RpmcFlashDev, Length, Data));
};

/**
  * PeiSafsRpmcOp2 - eSPI SAFS RPMC OP2
  *
  * @param  This                  Pointer to an EFI_ESPI_CMD_PROTOCOL structure.
  * @param[in]  EspiBase          Espi MMIO base
  * @param[in]  RpmcFlashDev      Which RPMC flash device is targeted to
  * @param[in]  Length            Length in byte
  * @param[in]  Buffer            Pointer to buffer to receive
  *
  * @retval EFI_STATUS
  *
  */
EFI_STATUS
EFIAPI
PeiSafsRpmcOp2 (
  IN CONST EFI_PEI_ESPI_CMD_PPI  *This,
  IN  UINT32                      EspiBase,
  IN  UINT8                       RpmcFlashDev,
  IN  UINT32                      Length,
  IN  UINT8                       *Buffer
  )
{
  return (FchEspiCmd_SafsRpmcOp2(EspiBase, RpmcFlashDev, Length, Buffer));
};

EFI_PEI_ESPI_CMD_PPI PeiFchEspiCmdPpi = {
  PeiInBandRst,
  PeiGetConfiguration,
  PeiSetConfiguration,
  PeiSafsFlashRead,
  PeiSafsFlashWrite,
  PeiSafsFlashErase,
  PeiSafsRpmcOp1,
  PeiSafsRpmcOp2
};

EFI_PEI_PPI_DESCRIPTOR PeiFchEspiCmdPpiDescriptor =
{
    (EFI_PEI_PPI_DESCRIPTOR_PPI | EFI_PEI_PPI_DESCRIPTOR_TERMINATE_LIST),
    &gAmdFchEspiCmdPpiGuid,
    &PeiFchEspiCmdPpi
};

/**
  *  @brief Entry point of the AMD FCH eSPI Command PEIM
  *
  */
EFI_STATUS
EFIAPI
AmdFchEspiCmdPeiInit (
  IN       EFI_PEI_FILE_HANDLE FileHandle,
  IN CONST EFI_PEI_SERVICES    **PeiServices
  )
{
  EFI_STATUS            Status;

  DEBUG ((DEBUG_INFO, "FchEspiCmdPei entry point: AmdFchEspiCmdPeiInit.\n"));

  Status = (*PeiServices)->InstallPpi (PeiServices, &PeiFchEspiCmdPpiDescriptor);

  if ( Status != EFI_SUCCESS ) {
    DEBUG ((DEBUG_ERROR, "[Warning]  InstallPpi failed (Status: 0x%x).\n", Status));
  }

  return (Status);
}

