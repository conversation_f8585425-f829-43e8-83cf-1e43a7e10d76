#;*****************************************************************************
#;
#; Copyright (C) 2008-2024 Advanced Micro Devices, Inc. All rights reserved.
#;
#;******************************************************************************

[Defines]
  INF_VERSION                    = 0x00010006
  BASE_NAME                      = AmdPspBaseLibV2
  FILE_GUID                      = 3463D317-7619-4350-A7BB-64DA224D7DFD
  MODULE_TYPE                    = BASE
  VERSION_STRING                 = 1.0
  LIBRARY_CLASS                  = AmdPspBaseLibV2

[Sources.common]
  AmdPspBaseLibV2.c

[Sources.Ia32]
  Ia32/RdSeed.nasm

[Sources.X64]
  X64/RdSeed.nasm

[Packages]
  MdePkg/MdePkg.dec
  AgesaPkg/AgesaPkg.dec
  AgesaModulePkg/AgesaCommonModulePkg.dec
  AgesaModulePkg/AgesaModuleCcxPkg.dec
  AgesaModulePkg/AgesaModulePspPkg.dec
  AgesaModulePkg/AgesaModuleFchPkg.dec
  AgesaPkg/AgesaPkg.dec

[LibraryClasses]
  BaseLib
  BaseMemoryLib
  AmdBaseLib
  AmdHeapLib
  PciLib
  AmdSocBaseLib
  SmnAccessLib
  AmdPspMmioLib
  AmdPspRegMuxLibV2
  AmdDirectoryBaseLib
  AmdPspRegBaseLib
  TimerLib
  IdsLib


[Protocols.X64]
  gAmdFabricResourceManagerServicesProtocolGuid

[Pcd]
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdAmdPspRecoveryFlagDetectEnable


