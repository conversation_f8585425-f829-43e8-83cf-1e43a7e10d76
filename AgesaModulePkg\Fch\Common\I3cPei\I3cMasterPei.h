/*****************************************************************************
 *
 * Copyright (C) 2015-2024 Advanced Micro Devices, Inc. All rights reserved.
 *
 *******************************************************************************
 */

#ifndef _AMD_I3C_MASTER_PEI_H_
#define _AMD_I3C_MASTER_PEI_H_

#include <Library/DebugLib.h>
#include <Library/PcdLib.h>
#include <Library/FchBaseLib.h>
#include <Library/BaseMemoryLib.h>
#include <Ppi/FchI3cMaster.h>
#include <Ppi/AmdFchInitPpi.h>

EFI_STATUS
EFIAPI
SetBusFrequencyI3c (
  IN       EFI_PEI_I3C_MASTER_PPI *This,
  IN       UINTN                  *BusClockHertz
  );

EFI_STATUS
EFIAPI
Reset (
  IN CONST EFI_PEI_I3C_MASTER_PPI *This
  );

EFI_STATUS
EFIAPI
StartRequest (
  IN CONST EFI_PEI_I3C_MASTER_PPI *This,
  IN       UINTN                  SlaveAddress,
  IN       EFI_I3C_REQUEST_PACKET *RequestPacket,
  IN       BOOLEAN                ReStartEnable
  );

EFI_STATUS
AmdI3cMasterPeiInit (
  IN       EFI_PEI_FILE_HANDLE    FileHandle,
  IN       CONST EFI_PEI_SERVICES **PeiServices
  );

#endif // _AMD_I3C_MASTER_PEI_H_

