/*****************************************************************************
 * Copyright (C) 2008-2024 Advanced Micro Devices, Inc. All rights reserved.
 *
*****************************************************************************
*/
/* $NoKeywords:$ */
/**
 * @file
 *
 * AMD Core Topology Services Protocol prototype definition
 *
 *
 * @xrefitem bom "File Content Label" "Release Content"
 * @e project:      AGESA
 * @e sub-project:  Fabric
 * @e \$Revision: 313706 $   @e \$Date: 2015-02-25 21:00:43 -0600 (Wed, 25 Feb 2015) $
 */

#ifndef _AMD_CORE_TOPOLOGY_SERVICES_PROTOCOL_H_
#define _AMD_CORE_TOPOLOGY_SERVICES_PROTOCOL_H_

/// Forward declaration for the AMD_CORE_TOPOLOGY_SERVICES_PROTOCOL.
typedef struct _AMD_CORE_TOPOLOGY_SERVICES_PROTOCOL AMD_CORE_TOPOLOGY_SERVICES_PROTOCOL;

/**
 * @brief Retrieves information about the layout of the cores on the given die.
 *
 * @param[in]  This              Pointer to the core topology services protocol instance. @see AMD_CORE_TOPOLOGY_SERVICES_PROTOCOL
 * @param[in]  Socket            Zero-based socket number to check.
 * @param[in]  Die               The target die's identifier within Socket.
 * @param[out] NumberOfComplexes Pointer to the number of enabled complexes on the given socket/die.
 * @param[out] NumberOfCores     Pointer to the number of enabled cores per complex on the given socket/die.
 * @param[out] NumberOfThreads   Pointer to the number of enabled threads per core on the given socket/die.
 *
 * @return EFI_STATUS - status of the operation.
 */
typedef
EFI_STATUS
(EFIAPI *AMD_CORE_TOPOLOGY_SERVICES_GET_CORE_TOPOLOGY_ON_DIE) (
  IN     AMD_CORE_TOPOLOGY_SERVICES_PROTOCOL *This,
  IN     UINTN                                Socket,
  IN     UINTN                                Die,
     OUT UINTN                               *NumberOfComplexes,
     OUT UINTN                               *NumberOfCores,
     OUT UINTN                               *NumberOfThreads
  );

/**
 * @brief Starts a core to fetch its first instructions from the reset vector. This service may only be called from the BSP.
 *
 * @param[in] This           Pointer to the core topology services protocol instance. @see AMD_CORE_TOPOLOGY_SERVICES_PROTOCOL
 * @param[in] Socket         Zero-based socket number of the target thread.
 * @param[in] Die            Zero-based die number within Socket of the target thread.
 * @param[in] LogicalComplex Zero-based logical complex number of the target thread.
 * @param[in] LogicalThread  Zero-based logical thread number of the target thread.
 *
 * @return EFI_STATUS - status of the operation.
 */
typedef
EFI_STATUS
(EFIAPI *AMD_CORE_TOPOLOGY_SERVICES_LAUNCH_THREAD) (
  IN     AMD_CORE_TOPOLOGY_SERVICES_PROTOCOL *This,
  IN     UINTN                                Socket,
  IN     UINTN                                Die,
  IN     UINTN                                LogicalComplex,
  IN     UINTN                                LogicalThread
  );

/// When installed, the AMD Core Topology Services PROTOCOL produces a collection of services that provide information on Core Topology.
struct _AMD_CORE_TOPOLOGY_SERVICES_PROTOCOL {
  AMD_CORE_TOPOLOGY_SERVICES_GET_CORE_TOPOLOGY_ON_DIE GetCoreTopologyOnDie; ///< @see AMD_CORE_TOPOLOGY_SERVICES_GET_CORE_TOPOLOGY_ON_DIE
  AMD_CORE_TOPOLOGY_SERVICES_LAUNCH_THREAD            LaunchThread;         ///< @see AMD_CORE_TOPOLOGY_SERVICES_LAUNCH_THREAD
};

/// GUID for core topology services protocol.
extern EFI_GUID gAmdCoreTopologyServicesProtocolGuid;

#endif

