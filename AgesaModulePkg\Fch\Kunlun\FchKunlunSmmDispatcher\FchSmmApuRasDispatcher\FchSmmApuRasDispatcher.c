/******************************************************************************
*
 * Copyright (C) 2008-2024 Advanced Micro Devices, Inc. All rights reserved.
 *
*******************************************************************************
**/

#include "FchSmmApuRasDispatcher.h"
#include "FchSmmUsbDispatcher.h"
#include "FchRasSmiLib.h"
#include "Filecode.h"
#include <Library/TimerLib.h>
#define FILECODE FCH_KUNLUN_FCHKUNLUNSMMDISPATCHER_FCHSMMAPURASDISPATCHER_FCHSMMAPURASDISPATCHER_FILECODE

extern UINTN                gSmmNumberOfSocketEnabled;

EFI_STATUS
EFIAPI
FchSmmApuRasDispatchRegister (
  IN       CONST FCH_SMM_APURAS_DISPATCH_PROTOCOL   *This,
  IN       FCH_SMM_APURAS_HANDLER_ENTRY_POINT       CallBackFunction,
  IN OUT   FCH_SMM_APURAS_REGISTER_CONTEXT          *MiscRegisterContext,
     OUT   EFI_HANDLE                               *DispatchHandle
  );

EFI_STATUS
EFIAPI
FchSmmApuRasDispatchUnRegister (
  IN       CONST FCH_SMM_APURAS_DISPATCH_PROTOCOL   *This,
  IN       EFI_HANDLE                               DispatchHandle
  );

FCH_SMM_APURAS_DISPATCH_PROTOCOL gFchSmmApuRasDispatchProtocol = {
  FchSmmApuRasDispatchRegister,
  FchSmmApuRasDispatchUnRegister
};

/*----------------------------------------------------------------------------------------*/
/**
 * FCH SMM APU RAS Smi dispatcher handler
 *
 *
 * @param[in]       SmmImageHandle        Image Handle
 * @param[in, out]   OPTIONAL CommunicationBuffer   Communication Buffer (see PI 1.1 for more details)
 * @param[in, out]   OPTIONAL SourceSize            Buffer size (see PI 1.1 for more details)

 * @retval          EFI_SUCCESS           SMI handled by dispatcher
 * @retval          EFI_UNSUPPORTED       SMI not supported by dispcther
 */
/*----------------------------------------------------------------------------------------*/
EFI_STATUS
EFIAPI
FchSmmApuRasDispatchHandler (
  IN       EFI_HANDLE   SmmImageHandle,
  IN OUT   VOID         *CommunicationBuffer OPTIONAL,
  IN OUT   UINTN        *SourceSize OPTIONAL
  )
{
  EFI_STATUS          Status;
  FCH_SMM_APURAS_NODE *CurrentFchSmmApuRasNodePtr;

  //
  // 1, call USB dispatcher first
  //
  Status = FchSmmUsbDispatchHandler (SmmImageHandle, CommunicationBuffer, SourceSize);

  //
  // 2, loop for any registered RAS callback
  //
  if (HeadFchSmmApuRasNodePtr->FchApuRasNodePtr == NULL) {
    Status = EFI_NOT_FOUND;
  } else {
    CurrentFchSmmApuRasNodePtr = HeadFchSmmApuRasNodePtr;

    if (CurrentFchSmmApuRasNodePtr->Context.Order == 0x65) {
      MicroSecondDelay (150 * 1000); //150ms
    }
    while (CurrentFchSmmApuRasNodePtr->FchApuRasNodePtr!= NULL) {
      if (CurrentFchSmmApuRasNodePtr->CallBackFunction != NULL) {
        Status = CurrentFchSmmApuRasNodePtr->CallBackFunction (
                                            CurrentFchSmmApuRasNodePtr->DispatchHandle,
                                            &CurrentFchSmmApuRasNodePtr->Context
                                            );
        DEBUG ((DEBUG_VERBOSE, "[FchSmmApuRasDispatchHandler] APU RAS SMM handler dispatched: Order = 0x%x, \
               return - %r\n", CurrentFchSmmApuRasNodePtr->Context.Order, Status));
      }
      CurrentFchSmmApuRasNodePtr = CurrentFchSmmApuRasNodePtr->FchApuRasNodePtr;
    }
  }

  FchSmmApuRasClearSmiSts ();

  return  Status;
}

/*----------------------------------------------------------------------------------------*/
/**
 * Register APU RAS child handler
 *
 *
 * @param[in]       This                  Pointer to protocol
 * @param[in]       CallBackFunction
 * @param[in, out]  ApuRasRegisterContext
 * @param[out]      DispatchHandle        Handle (see PI 1.1 for more details)
 *
 * @retval          EFI_SUCCESS           SMI handled by dispatcher
 * @retval          EFI_UNSUPPORTED       SMI not supported by dispcther
 */
/*----------------------------------------------------------------------------------------*/
EFI_STATUS
EFIAPI
FchSmmApuRasDispatchRegister (
  IN       CONST FCH_SMM_APURAS_DISPATCH_PROTOCOL   *This,
  IN       FCH_SMM_APURAS_HANDLER_ENTRY_POINT       CallBackFunction,
  IN OUT   FCH_SMM_APURAS_REGISTER_CONTEXT          *ApuRasRegisterContext,
     OUT   EFI_HANDLE                               *DispatchHandle
  )
{
  EFI_STATUS          Status;
  FCH_SMM_APURAS_NODE *NewFchSmmApuRasNodePtr;
  FCH_SMM_APURAS_NODE *CurrentFchSmmApuRasNodePtr;
  FCH_SMM_APURAS_NODE *PreviousFchSmmApuRasNodePtr;

  Status = EFI_OUT_OF_RESOURCES;

  if (CallBackFunction == NULL || ApuRasRegisterContext == NULL || DispatchHandle == NULL) {
    return EFI_INVALID_PARAMETER;
  }

  if ( ApuRasRegisterContext->Socket >= gSmmNumberOfSocketEnabled ) {
    IDS_HDT_CONSOLE (
      FCH_TRACE,
      "[%a] ERROR: Total %d socket(s), the value of socket argument %d is invalid.\n",
      __FUNCTION__,
      gSmmNumberOfSocketEnabled,
      ApuRasRegisterContext->Socket
      );
    return EFI_INVALID_PARAMETER;
  }

  Status = gSmst->SmmAllocatePool (
                    EfiRuntimeServicesData,
                    sizeof (FCH_SMM_APURAS_NODE),
                    (VOID **)&NewFchSmmApuRasNodePtr
                    );
  ASSERT_EFI_ERROR (Status);

  NewFchSmmApuRasNodePtr->CallBackFunction = CallBackFunction;
  NewFchSmmApuRasNodePtr->Context = *ApuRasRegisterContext;
  *DispatchHandle = &NewFchSmmApuRasNodePtr->DispatchHandle;
  NewFchSmmApuRasNodePtr->DispatchHandle = *DispatchHandle;

  DEBUG ((DEBUG_INFO, "[FchSmmDispatcher] Registering APU RAS SMM handler: Order = 0x%x\n", \
         ApuRasRegisterContext->Order));
  if (HeadFchSmmApuRasNodePtr->FchApuRasNodePtr == NULL) {
    NewFchSmmApuRasNodePtr->FchApuRasNodePtr = HeadFchSmmApuRasNodePtr;
    HeadFchSmmApuRasNodePtr = NewFchSmmApuRasNodePtr;
  } else {
    PreviousFchSmmApuRasNodePtr = HeadFchSmmApuRasNodePtr;
    CurrentFchSmmApuRasNodePtr = HeadFchSmmApuRasNodePtr;
    while (CurrentFchSmmApuRasNodePtr->FchApuRasNodePtr != NULL) {
      if (NewFchSmmApuRasNodePtr->Context.Order <= CurrentFchSmmApuRasNodePtr->Context.Order &&
          CurrentFchSmmApuRasNodePtr->Context.Socket == NewFchSmmApuRasNodePtr->Context.Socket &&
          CurrentFchSmmApuRasNodePtr->Context.NbioNumber == NewFchSmmApuRasNodePtr->Context.NbioNumber) {

        if (PreviousFchSmmApuRasNodePtr == CurrentFchSmmApuRasNodePtr) {
          NewFchSmmApuRasNodePtr->FchApuRasNodePtr = HeadFchSmmApuRasNodePtr;
          HeadFchSmmApuRasNodePtr = NewFchSmmApuRasNodePtr;
          Status = EFI_SUCCESS;
          return Status;
        }
        NewFchSmmApuRasNodePtr->FchApuRasNodePtr = PreviousFchSmmApuRasNodePtr->FchApuRasNodePtr;
        PreviousFchSmmApuRasNodePtr->FchApuRasNodePtr = NewFchSmmApuRasNodePtr;

        Status = EFI_SUCCESS;
        return  Status;
      }
      PreviousFchSmmApuRasNodePtr = CurrentFchSmmApuRasNodePtr;
      CurrentFchSmmApuRasNodePtr = CurrentFchSmmApuRasNodePtr->FchApuRasNodePtr;
    }
    PreviousFchSmmApuRasNodePtr->FchApuRasNodePtr = NewFchSmmApuRasNodePtr;
    NewFchSmmApuRasNodePtr->FchApuRasNodePtr = CurrentFchSmmApuRasNodePtr;
  }

  FchSmmApuRasSmiEnable (ApuRasRegisterContext);

  Status = EFI_SUCCESS;
  return  Status;
}


/*----------------------------------------------------------------------------------------*/
/**
 * Unregister APU RAS child handler
 *
 *
 * @param[in]       This                  Pointer to protocol
 * @param[in]       DispatchHandle
 *
 * @retval          EFI_SUCCESS           SMI handled by dispatcher
 * @retval          EFI_UNSUPPORTED       SMI not supported by dispcther
 */
/*----------------------------------------------------------------------------------------*/
EFI_STATUS
EFIAPI
FchSmmApuRasDispatchUnRegister (
  IN       CONST FCH_SMM_APURAS_DISPATCH_PROTOCOL   *This,
  IN       EFI_HANDLE                               DispatchHandle
  )
{
  EFI_STATUS          Status;
  FCH_SMM_APURAS_NODE *CurrentFchSmmApuRasNodePtr;
  FCH_SMM_APURAS_NODE *PreviousFchSmmApuRasNodePtr;

  if (DispatchHandle == NULL) {
    return EFI_INVALID_PARAMETER;
  }

  if (HeadFchSmmApuRasNodePtr->FchApuRasNodePtr == NULL) {
    Status = EFI_NOT_FOUND;
    return  Status;
  } else {
    PreviousFchSmmApuRasNodePtr = HeadFchSmmApuRasNodePtr;
    CurrentFchSmmApuRasNodePtr = HeadFchSmmApuRasNodePtr;
    if (CurrentFchSmmApuRasNodePtr->DispatchHandle == DispatchHandle) {
      HeadFchSmmApuRasNodePtr = CurrentFchSmmApuRasNodePtr->FchApuRasNodePtr;
    } else {
      while (CurrentFchSmmApuRasNodePtr->DispatchHandle != DispatchHandle) {
        PreviousFchSmmApuRasNodePtr = CurrentFchSmmApuRasNodePtr;
        CurrentFchSmmApuRasNodePtr = CurrentFchSmmApuRasNodePtr->FchApuRasNodePtr;
        if (CurrentFchSmmApuRasNodePtr->DispatchHandle == NULL) {
          Status = EFI_NOT_FOUND;
          return  Status;
        }
      }
      PreviousFchSmmApuRasNodePtr->FchApuRasNodePtr = CurrentFchSmmApuRasNodePtr->FchApuRasNodePtr;
    }

    FchSmmApuRasSmiDisable ((FCH_SMM_APURAS_REGISTER_CONTEXT*) &(CurrentFchSmmApuRasNodePtr->Context));

    Status = gSmst->SmmFreePool (
                       CurrentFchSmmApuRasNodePtr
                       );
    ASSERT_EFI_ERROR (Status);
  }
  Status = EFI_SUCCESS;
  return  Status;
}



