/*****************************************************************************
 *
 * Copyright (C) 2008-2024 Advanced Micro Devices, Inc. All rights reserved.
 *
 *******************************************************************************
 */

#include <PiDxe.h>
#include <Library/BaseLib.h>
#include <Library/BaseMemoryLib.h>
#include <Library/CcxZen5SegRmpDxeLib.h>
#include <Library/PcdLib.h>
#include <Library/FabricRegisterAccLib.h>
#include <Library/UefiBootServicesTableLib.h>
#include <Protocol/FabricTopologyServices2.h>
#include <Protocol/FabricResourceManagerServicesProtocol.h>
#include <Library/AmdBaseLib.h>
#include <FabricRegistersBrh.h>
#include <FabricInfoBrh.h>
#include <Filecode.h>
#include "CcxRegistersZen5.h"

#define FILECODE LIBRARY_CCXZEN5SEGRMPBRHDXELIB_CCXZEN5SEGRMPBRHDXELIB_FILECODE

/*----------------------------------------------------------------------------------------
 *                               D E F I N I T I O N S
 *----------------------------------------------------------------------------------------
 */
#define INVALID_SOCKET_ID 0xFFFFFFFF
#define RMP_SEGMENT_BASE_MASK 0xFFFFFFFF00000ull
#define RMP_MAPPED_SIZE_MASK 0xFFFFFull
#define MSR_RMP_BASE 0xC0010132
#define MSR_RMP_BASE_MASK 0xFFF0000000001FFFull
#define MSR_RMP_CFG 0xC0010136
#define MSR_RMP_CFG_MASK 0xFFFFFFFFFFFFC0FEull
#define RMP_SEG_SIZE_128GB 37u
#define RMP_SEG_SIZE_4TB   42u

// Dram regions + 64Bit MMIO Regions above 4GB
#define MAX_BRH_RMP_MEMORY_RANGES (BRH_NUMBER_OF_DRAM_REGIONS + BRH_MAX_SOCKETS*BRH_MAX_HOST_BRIDGES_PER_SOCKET)

/*----------------------------------------------------------------------------------------
 *                           G L O B A L   V A R I A B L E S
 *----------------------------------------------------------------------------------------
 */
#pragma pack (push, 1)
DRAM_ADDRESS_RANGES          DramAddressRanges[BRH_NUMBER_OF_DRAM_REGIONS];
RMP_MEMORY_RANGES            RmpMemoryRanges[MAX_BRH_RMP_MEMORY_RANGES];
RMP_SEGMENT_TABLE            RST[MAX_RMP_SEGMENTS];
UINT64                       RstAllocationBuffer[MAX_RMP_SEGMENTS];
RMP_SEGMENTS                 RmpSegments[MAX_RMP_SEGMENTS];
UINT8                        RmpSegSizeAsPowerOfTwo;
UINT64                       RmpSegSizeInBytes;
UINT8                        MinSegmentSize;
UINT64                       MinSegmentSizeInBytes;
UINT8                        MaxSegmentSize;
UINT64                       MaxSegmentSizeInBytes;
UINT32                       MaxSupportedSegments;
UINT32                       CachedSegments;
UINT32                       ActualSegmentCount;
UINT32                       MaxValidDramAddresRangeIndex;
UINT32                       MaxValidRmpMemoryRangeIndex;
BOOLEAN                      SegmentedRmpEnabled;

#pragma pack (pop)

EFI_STATUS
EFIAPI
GetAbove4GMMIOAddrRange (
  OUT RMP_MEMORY_RANGES* RmpMmioRanges,
  OUT UINT16*            RmpMmioRangesCount
);

BOOLEAN
EFIAPI
CheckIfRangesOverlap(
  IN ADDR_RANGE* range1,
  IN ADDR_RANGE* range2,
  OUT ADDR_RANGE* overlap
);

EFI_STATUS
EFIAPI
AllocateBufferInAddressRange (
  IN OUT EFI_PHYSICAL_ADDRESS *BaseAddr,
  IN EFI_PHYSICAL_ADDRESS LimitAddr,
  IN UINT64 BufferSize
);

/*----------------------------------------------------------------------------------------
 *           P R O T O T Y P E S     O F     L O C A L     F U N C T I O N S
 *----------------------------------------------------------------------------------------
 */

/**
 *
 *  InitializeDramAddressRanges
 *
 *  @param[in]  VOID
 *
 *  Description:
 *    Initialize DRAM Address Ranges
 *
 *  @retval VOID
 *
 */
VOID
InitializeDramAddressRanges  (
  VOID
  )
{
  UINT32                       DramMapIndex;
  DRAM_ADDRESS_CTL_REGISTER    DramAddressCtl;
  DRAM_BASE_ADDRESS_REGISTER   DramBaseAddr;
  DRAM_LIMIT_ADDRESS_REGISTER  DramLimitAddr;
  RMP_MEMORY_RANGES            RmpMmioRanges[BRH_MAX_SOCKETS*BRH_MAX_HOST_BRIDGES_PER_SOCKET];
  UINT16                       RmpMmioRangesCount;
  UINT16                       RmpMmioRangeIndex;

  EFI_STATUS                   Status;

  ZeroMem((VOID*)&DramAddressRanges, sizeof(DramAddressRanges));
  ZeroMem((VOID*)&RmpMemoryRanges, sizeof(RmpMemoryRanges));

  // Collect information about the memory ranges discovered by ABL
  // This code assumes the DRAM ranges are ordered from small to big.
  for (DramMapIndex = 0u; DramMapIndex < BRH_NUMBER_OF_DRAM_REGIONS; DramMapIndex++) {
    DramAddressCtl.Value = FabricRegisterAccRead (0, 0, DRAMADDRESSCTL_0_FUNC, (DRAMADDRESSCTL_0_REG + (DramMapIndex * BRH_DRAM_REGION_REGISTER_OFFSET)), BRH_IOMS2_INSTANCE_ID);
    if (DramAddressCtl.Field.AddrRngVal == 0x01){
      DramAddressRanges[DramMapIndex].SocketId = (DramAddressCtl.Field.DstFabricID >> BRH_FABRIC_ID_SOCKET_SHIFT) & BRH_FABRIC_ID_SOCKET_SIZE_MASK;
      // Enumerate DRAM ranges to read base, limit
      DramBaseAddr.Value = FabricRegisterAccRead (0, 0, DRAMBASEADDRESS_0_FUNC, (DRAMBASEADDRESS_0_REG + (DramMapIndex * BRH_DRAM_REGION_REGISTER_OFFSET)), BRH_IOMS2_INSTANCE_ID);
      DramLimitAddr.Value = FabricRegisterAccRead (0, 0, DRAMLIMITADDRESS_0_FUNC, (DRAMLIMITADDRESS_0_REG + (DramMapIndex * BRH_DRAM_REGION_REGISTER_OFFSET)), BRH_IOMS2_INSTANCE_ID);

      DramAddressRanges[DramMapIndex].DramBaseAddr = (UINT64)DramBaseAddr.Field.DramBaseAddr << 28;
      // Append the limit with 0xFFFFFFF
      DramAddressRanges[DramMapIndex].DramLimitAddr = ((UINT64)DramLimitAddr.Field.DramLimitAddr << 28) | 0xFFFFFFF ;

      MaxValidDramAddresRangeIndex = DramMapIndex;
      RmpMemoryRanges[DramMapIndex].RmpMemoryBaseAddr  = DramAddressRanges[DramMapIndex].DramBaseAddr;
      RmpMemoryRanges[DramMapIndex].RmpMemoryLimitAddr = DramAddressRanges[DramMapIndex].DramLimitAddr;

      IDS_HDT_CONSOLE (CPU_TRACE, "  InitializeDramAddressRanges DramBaseAddr=0x%lx, DramLimitAddr=0x%lx, SocketId=0x%x, MaxValidDramAddresRangeIndex=0x%x\n",\
                       DramAddressRanges[DramMapIndex].DramBaseAddr, DramAddressRanges[DramMapIndex].DramLimitAddr,\
                       DramAddressRanges[DramMapIndex].SocketId, MaxValidDramAddresRangeIndex);
    }
  }

  MaxValidRmpMemoryRangeIndex = MaxValidDramAddresRangeIndex;
  // Check if 64 bit MMIO needs to be included
  ZeroMem((VOID*)&RmpMmioRanges, sizeof(RmpMmioRanges));
  RmpMmioRangesCount = 0u;
  Status = GetAbove4GMMIOAddrRange(RmpMmioRanges, &RmpMmioRangesCount);

  if (!EFI_ERROR(Status)) {
    for (RmpMmioRangeIndex = 0u; RmpMmioRangeIndex < RmpMmioRangesCount; RmpMmioRangeIndex++) {
      MaxValidRmpMemoryRangeIndex = MaxValidRmpMemoryRangeIndex + 1u;
      ASSERT(MaxValidRmpMemoryRangeIndex <= MAX_BRH_RMP_MEMORY_RANGES);

      RmpMemoryRanges[MaxValidRmpMemoryRangeIndex].RmpMemoryBaseAddr = RmpMmioRanges[RmpMmioRangeIndex].RmpMemoryBaseAddr;
      RmpMemoryRanges[MaxValidRmpMemoryRangeIndex].RmpMemoryLimitAddr = RmpMmioRanges[RmpMmioRangeIndex].RmpMemoryLimitAddr;

      IDS_HDT_CONSOLE (CPU_TRACE, "InitializeDramAddressRanges RmpMmioRanges Index=0x%x, Base=0x%lx, Limit=0x%lx\n", \
                                  RmpMmioRangeIndex,
                                  RmpMmioRanges[RmpMmioRangeIndex].RmpMemoryBaseAddr,
                                  RmpMmioRanges[RmpMmioRangeIndex].RmpMemoryLimitAddr);
    }
  }


}

/**
 *
 *  GetOptimalRmpSegSize
 *
 *  @param[in]  VOID
 *
 *  Description:
 *    Get Get Optimal RmpSegSize based on Total System Memory and Max Supported Rmp Segments
 *
 *  @retval VOID
 *
 */
UINT8
GetOptimalRmpSegSize (
  VOID
  )
{
  UINT8   OptRmpSegSizeAsPowerOfTwo;
  UINT64  OptSegmentSizeInBytes;
  UINT8   SegSizeIndex;

  IDS_HDT_CONSOLE (CPU_TRACE, "  GetOptimalRmpSegSize Enter\n");
  IDS_HDT_CONSOLE (CPU_TRACE, "  GetOptimalRmpSegSize MinSegmentSize=%d, MaxSegmentSize=%d, MaxSupportedSegments=%d, MaxRmpMemoryLimitAddr=0x%lx\n",\
                                 MinSegmentSize, MaxSegmentSize, MaxSupportedSegments, RmpMemoryRanges[MaxValidRmpMemoryRangeIndex].RmpMemoryLimitAddr);
  if ((MinSegmentSize == 0u) || (MaxSegmentSize == 0u) || (MaxSupportedSegments == 0u)) {
  // It should not be here in any case
    OptRmpSegSizeAsPowerOfTwo = RMP_SEG_SIZE_4TB;
  } else {
    for (SegSizeIndex = MinSegmentSize; SegSizeIndex <= MaxSegmentSize; SegSizeIndex++) {
      OptRmpSegSizeAsPowerOfTwo = SegSizeIndex;
      OptSegmentSizeInBytes = 0x1ULL << OptRmpSegSizeAsPowerOfTwo;
      IDS_HDT_CONSOLE (CPU_TRACE, "  GetOptimalRmpSegSize Optimal-RmpSegSizeAsPowerOfTwo=%d, OptSegmentSizeInBytes=0x%lx\n",\
                                     OptRmpSegSizeAsPowerOfTwo, OptSegmentSizeInBytes);
      if (RmpMemoryRanges[MaxValidRmpMemoryRangeIndex].RmpMemoryLimitAddr <= (OptSegmentSizeInBytes * MaxSupportedSegments)) {
          IDS_HDT_CONSOLE (CPU_TRACE, "  GetOptimalRmpSegSize MaxRmpMemoryLimitAddr is less than or equal to Memory Covered by Segmented RMP\n");
        break;
      }
    }
  }
  IDS_HDT_CONSOLE (CPU_TRACE, "  GetOptimalRmpSegSize Exit. Optimal-RmpSegSizeAsPowerOfTwo=%d\n", OptRmpSegSizeAsPowerOfTwo);
  return OptRmpSegSizeAsPowerOfTwo;
}

/**
 *
 *  GetSegmentedRmpConfig
 *
 *  @param[in]  VOID
 *
 *  Description:
 *    Get various configuration parameters associated with Segmented RMP
 *
 *  @retval VOID
 *
 */
VOID
GetSegmentedRmpConfig  (
  VOID
  )
{
  UINT32                    SegmentLimited;
  SEGMENTED_RMP_SIZE        SegmentedRmpSizeEax;
  SEGMENTED_RMP             SegmentedRmpEbx;

  IDS_HDT_CONSOLE (CPU_TRACE, "  GetSegmentedRmpConfig Enter\n");

  SegmentedRmpSizeEax.Value = 0;
  SegmentedRmpEbx.Value = 0;

  //  Support for segmented RMP is indicated in CPUID Fn8000_001F_EAX[23]. When segmented RMP is supported, CPUID
  //  Fn8000_0025 indicates additional information about segmented RMP support:
  //
  //  |--------------|---------|-----------------|-------------------------------------------------|-----------------|-
  //  | CPUID        |  Bits   |   Name          |   Description                                   | Value           |
  //  |Fn8000_0025   |         |                 |                                                 | in Breithorn    |
  //  |--------------|---------|-----------------|-------------------------------------------------|-----------------|-
  //  |EAX           | [5:0]   | MinSegmentSize  |Indicates the minimum supported RMP segment size | 6'd36           |
  //  |              | [11:6]  | MaxSegmentSize  |Indicates the maximum supported RMP segment size | 6'd42           |
  //  |              | [31:12] | Reserved        |                                                 |                 |
  //  |--------------|---------|-----------------|-------------------------------------------------|-----------------|-
  //  |EBX           | [9:0]   | CachedSegments  |  Indicates the number of RMP segment definitions|  10'd16         |
  //  |              |         |                 |  that the CPU is capable of caching             |                 |
  //  |              |         |                 |                                                 |                 |
  //  |              | [10]    | SegmentsLimited |  Indicates if the number of cached segments     |  1'b1           |
  //  |              |         |                 |  is a hard limit                                |                 |
  //  |              |         |                 |                                                 |                 |
  //  |              | [31:11] |  Reserved       |                                                 |                 |
  //  |              |         |                 |                                                 |                 |
  //  |--------------|---------|-----------------|-------------------------------------------------|-----------------|-

  // Read RMP CPUID registers
  AsmCpuid (CPUID_AMD_SEGMENTED_RMP_TABLE, &(SegmentedRmpSizeEax.Value), &(SegmentedRmpEbx.Value), NULL, NULL);

  CachedSegments = SegmentedRmpEbx.Field.CachedSegments;
  SegmentLimited = SegmentedRmpEbx.Field.SegmentLimited;
  IDS_HDT_CONSOLE (CPU_TRACE, "  GetSegmentedRmpConfig CachedSegments=0x%x, SegmentLimited=0x%x", CachedSegments, SegmentLimited);
  if (SegmentLimited == 1u) {
    MaxSupportedSegments = CachedSegments;
  } else {
    MaxSupportedSegments = MAX_RMP_SEGMENTS;
  }

  IDS_HDT_CONSOLE (CPU_TRACE, "  GetSegmentedRmpConfig SegmentedRmpSizeEax.Field.MinSegmentSize=0x%x, "\
                                "SegmentedRmpSizeEax.Field.MaxSegmentSize=0x%x\n",\
                   SegmentedRmpSizeEax.Field.MinSegmentSize, SegmentedRmpSizeEax.Field.MaxSegmentSize);

  MinSegmentSize = (UINT8) SegmentedRmpSizeEax.Field.MinSegmentSize;
  MaxSegmentSize = (UINT8) SegmentedRmpSizeEax.Field.MaxSegmentSize;
  MinSegmentSizeInBytes = 0x1ULL << MinSegmentSize;
  MaxSegmentSizeInBytes = 0x1ULL << MaxSegmentSize;


    // Read PCD that gives RMP Segment size as power of 2
  RmpSegSizeAsPowerOfTwo = (UINT8)PcdGet8 (PcdAmdRmpSegSize);
  RmpSegSizeInBytes = 0x1ULL << RmpSegSizeAsPowerOfTwo;

  if ((MinSegmentSize <= RmpSegSizeAsPowerOfTwo) && (RmpSegSizeAsPowerOfTwo <= MaxSegmentSize)) {
    if (RmpMemoryRanges[MaxValidRmpMemoryRangeIndex].RmpMemoryLimitAddr > MaxSupportedSegments * RmpSegSizeInBytes) {
      // Reset the SegSize Vars to New Optimal Value
      RmpSegSizeAsPowerOfTwo = GetOptimalRmpSegSize();
      RmpSegSizeInBytes = 0x1ULL << RmpSegSizeAsPowerOfTwo;
    }
  } else {
    // Reset the SegSize Vars to New Optimal Value
    RmpSegSizeAsPowerOfTwo = GetOptimalRmpSegSize();
    RmpSegSizeInBytes = 0x1ULL << RmpSegSizeAsPowerOfTwo;
  }

  IDS_HDT_CONSOLE (CPU_TRACE, "  GetSegmentedRmpConfig RmpSegSizeAsPowerOfTwo=0x%x, RmpSegSizeInBytes=0x%lx, "\
                                 "MinSegmentSizeInBytes=0x%lx, MaxSegmentSizeInBytes=0x%lx\n",\
                   RmpSegSizeAsPowerOfTwo, RmpSegSizeInBytes, MinSegmentSizeInBytes, MaxSegmentSizeInBytes);

  IDS_HDT_CONSOLE (CPU_TRACE, "  GetSegmentedRmpConfig Exit\n");
}

/**
 *
 *  CreateRmpSegments
 *
 *  @param[in]  VOID
 *  @param[out] VOID
 *
 *  Description:
 *    Function to divide memory range in to segments and populate global DS with segment base, limit and valid memory
 *    size covered by that segment
 *
 *  @retval VOID
 *
 */
VOID
CreateRmpSegments  (
  VOID
  )
{
    UINT32                       RmpMapIndex;
    UINT64                       TempSegmentBaseAddr = 0u;
    UINT32                       SegmentIndex;
    ADDR_RANGE                   range1;
    ADDR_RANGE                   range2;
    ADDR_RANGE                   overlap;

  ZeroMem((VOID*)&RmpSegments, sizeof(RmpSegments));
  ZeroMem((VOID*)&range1, sizeof(range1));
  ZeroMem((VOID*)&range2, sizeof(range2));
  ZeroMem((VOID*)&overlap, sizeof(overlap));

  ActualSegmentCount = 0u;

 // Enumerate all the possible entries in RmpSegments based on MaxSupportedSegments as count
  for (SegmentIndex = 0u; SegmentIndex < MaxSupportedSegments; SegmentIndex++) {

    if (TempSegmentBaseAddr > RmpMemoryRanges[MaxValidRmpMemoryRangeIndex].RmpMemoryLimitAddr) {
      // If segment base goes out of bound, will need to break
      break;
    } else {
      // Initialize the RmpSegmentswith valid base and limit addresses.
      // Other fields will set to 0u for now, and will be initialized later.
      RmpSegments[SegmentIndex].SegmentBaseAddr = TempSegmentBaseAddr;
      RmpSegments[SegmentIndex].SegmentLimitAddr = TempSegmentBaseAddr + RmpSegSizeInBytes - 1;
      RmpSegments[SegmentIndex].SegmentMappedSizeInBytes = 0u;
      RmpSegments[SegmentIndex].SegmentRmpTableBaseAddr = 0u;
      RmpSegments[SegmentIndex].SegmentRmpTableSizeInBytes = 0u;
      ActualSegmentCount++;
    }
    // Increment the segment base using RMP Segment Size
    TempSegmentBaseAddr = TempSegmentBaseAddr + RmpSegSizeInBytes;
  }

  for (SegmentIndex = 0u; SegmentIndex < ActualSegmentCount; SegmentIndex++) {
    for (RmpMapIndex = 0u; RmpMapIndex <= MaxValidRmpMemoryRangeIndex; RmpMapIndex++) {
      range1.BaseAddr = RmpSegments[SegmentIndex].SegmentBaseAddr;
      range1.LimitAddr = RmpSegments[SegmentIndex].SegmentLimitAddr;
      range2.BaseAddr = RmpMemoryRanges[RmpMapIndex].RmpMemoryBaseAddr;
      range2.LimitAddr = RmpMemoryRanges[RmpMapIndex].RmpMemoryLimitAddr;

      if (CheckIfRangesOverlap(&range1, &range2, &overlap)) {
        if (overlap.LimitAddr < RmpSegments[SegmentIndex].SegmentLimitAddr) {
          RmpSegments[SegmentIndex].SegmentMappedSizeInBytes = overlap.LimitAddr\
                                                             - RmpSegments[SegmentIndex].SegmentBaseAddr + 1;
        } else {
          RmpSegments[SegmentIndex].SegmentMappedSizeInBytes = RmpSegments[SegmentIndex].SegmentLimitAddr \
                                                             - RmpSegments[SegmentIndex].SegmentBaseAddr + 1;
        }
        IDS_HDT_CONSOLE (CPU_TRACE, "CreateRmpSegments Overlap Range. SegmentIndex=%d, SegmentBaseAddr=0x%lx, "\
                                     "SegmentLimitAddr=0x%lx, SegmentMappedSizeInBytes=0x%lx\n",\
                         SegmentIndex, RmpSegments[SegmentIndex].SegmentBaseAddr,  RmpSegments[SegmentIndex].SegmentLimitAddr,\
                         RmpSegments[SegmentIndex].SegmentMappedSizeInBytes);
      } else {

        IDS_HDT_CONSOLE (CPU_TRACE, "CreateRmpSegments No overlap. R1.Base=0x%lx, R1.Limit=0x%lx, "\
                                     "R2.Base=0x%lx R2.Limit=0x%lx\n",\
                         range1.BaseAddr, range1.LimitAddr,  range2.BaseAddr, range2.LimitAddr);

      }
    }
  }
}

/**
 *
 *  AllocateSegmentedRmpTables
 *
 *  @param[in]  VOID
 *  @param[out] VOID
 *
 *  Description:
 *    Function to allocate RMP Table in each individual segment,
 *    and populate Data Structure RmpSegments with RMP table base addresses
 *
 *
 *  @retval VOID
 *
 */
VOID
AllocateSegmentedRmpTables  (
  VOID
  )
{
  UINT32                       SegmentIndex;
  EFI_PHYSICAL_ADDRESS         SegmentRmpTableBaseAddr;
  UINT64                       MappedSizeInBytes;
  UINT64                       MappedSizeInBytesGbAligned;
  EFI_STATUS                   Status;

  // Enumerate memory segments
  for (SegmentIndex = 0u; SegmentIndex < ActualSegmentCount; SegmentIndex++) {
    MappedSizeInBytes = RmpSegments[SegmentIndex].SegmentMappedSizeInBytes;
    MappedSizeInBytesGbAligned = (MappedSizeInBytes + SIZE_1GB - 1) & ~(SIZE_1GB - 1);

    // The size of RMP table (in MB) is dependent on MappedSizeInBytesGbAligned (equal to 4MB * MappedSizeInBytesGbAligned)
    RmpSegments[SegmentIndex].SegmentRmpTableSizeInBytes = (4 * (MappedSizeInBytesGbAligned >> 30u)) << 20u;
    SegmentRmpTableBaseAddr = RmpSegments[SegmentIndex].SegmentBaseAddr;

    Status = EFI_NOT_FOUND;

    if (DramAddressRanges[MaxValidDramAddresRangeIndex].DramLimitAddr > RmpSegments[SegmentIndex].SegmentBaseAddr) {
      Status = AllocateBufferInAddressRange(&SegmentRmpTableBaseAddr, (RmpSegments[SegmentIndex].SegmentBaseAddr + MappedSizeInBytes), RmpSegments[SegmentIndex].SegmentRmpTableSizeInBytes);
    }

    if (!EFI_ERROR(Status)){
      RmpSegments[SegmentIndex].SegmentRmpTableBaseAddr = SegmentRmpTableBaseAddr;
    } else {
      if (DramAddressRanges[MaxValidDramAddresRangeIndex].DramLimitAddr > RmpSegments[SegmentIndex].SegmentBaseAddr) {
        // Use GB aligned (floor value) Mapped Size Limit as Max address to search for RMP table base
        SegmentRmpTableBaseAddr = RmpSegments[SegmentIndex].SegmentBaseAddr + MappedSizeInBytes;
      } else {
        // If Segment is above max DRAM range, use last DRAM Limit with GB alignment (floor value) as Max address to search RMP table base
        SegmentRmpTableBaseAddr = DramAddressRanges[MaxValidDramAddresRangeIndex].DramLimitAddr;
      }

      Status = gBS->AllocatePages (
                     AllocateMaxAddress,
                     EfiReservedMemoryType,
                     EFI_SIZE_TO_PAGES (RmpSegments[SegmentIndex].SegmentRmpTableSizeInBytes + SIZE_1MB),
                    &SegmentRmpTableBaseAddr
                   );
      if (EFI_ERROR (Status)) {
        RmpSegments[SegmentIndex].SegmentRmpTableBaseAddr = 0u;
        RmpSegments[SegmentIndex].SegmentRmpTableSizeInBytes = 0u;
        RmpSegments[SegmentIndex].SegmentMappedSizeInBytes = 0u;
      } else {
        gBS->FreePages (SegmentRmpTableBaseAddr, EFI_SIZE_TO_PAGES (RmpSegments[SegmentIndex].SegmentRmpTableSizeInBytes + SIZE_1MB));
        SegmentRmpTableBaseAddr = (SegmentRmpTableBaseAddr + SIZE_1MB - 1) & ~(SIZE_1MB - 1);
        Status = gBS->AllocatePages (AllocateAddress,
                                     EfiReservedMemoryType,
                                     EFI_SIZE_TO_PAGES (RmpSegments[SegmentIndex].SegmentRmpTableSizeInBytes),
                                     &SegmentRmpTableBaseAddr
                                    );
        if (EFI_ERROR (Status)) {
          RmpSegments[SegmentIndex].SegmentRmpTableBaseAddr = 0u;
          RmpSegments[SegmentIndex].SegmentRmpTableSizeInBytes = 0u;
          RmpSegments[SegmentIndex].SegmentMappedSizeInBytes = 0u;
        } else {
          RmpSegments[SegmentIndex].SegmentRmpTableBaseAddr = SegmentRmpTableBaseAddr;
        }
      }
    }
  }
  IDS_HDT_CONSOLE (CPU_TRACE, "  \nDump RST Table Collected Data:-\n\n");

  for (SegmentIndex = 0u; SegmentIndex < ActualSegmentCount; SegmentIndex++) {
    IDS_HDT_CONSOLE (CPU_TRACE, "  AllocateSegmentedRmpTables SegmentIndex=0x%x, SegmentBaseAddr=0x%lx, SegmentLimitAddr=0x%lx, SegmentMappedSizeInBytes=0x%lx\n",\
    SegmentIndex, RmpSegments[SegmentIndex].SegmentBaseAddr, RmpSegments[SegmentIndex].SegmentLimitAddr, RmpSegments[SegmentIndex].SegmentMappedSizeInBytes);
    IDS_HDT_CONSOLE (CPU_TRACE, "  AllocateSegmentedRmpTables SegmentIndex=0x%x, SegmentRmpTableBaseAddr=0x%lx, SegmentRmpTableSizeInBytes=0x%lx\n",\
    SegmentIndex, RmpSegments[SegmentIndex].SegmentRmpTableBaseAddr, RmpSegments[SegmentIndex].SegmentRmpTableSizeInBytes);
  }
}

/**
 *
 *  AllocateBufferInAddressRange
 *
 *  @param[in,out]  BaseAddr    Base Address of the range
 *  @param[in]      LimitAddr   Limit Address of the range
 *  @param[in]      BufferSize  Buffer Size to be allocated
 *
 *  Description:
 *    Allocate buffer of the given size, within given address range
 *
 *  @retval EFI_SUCCESS
 *
 */
EFI_STATUS
EFIAPI
AllocateBufferInAddressRange (
  IN OUT EFI_PHYSICAL_ADDRESS *BaseAddr,
  IN     EFI_PHYSICAL_ADDRESS LimitAddr,
  IN     UINT64 BufferSize
)
{
  EFI_STATUS              Status;
  EFI_PHYSICAL_ADDRESS    BufferAddr;

  if ((BaseAddr == NULL) || (LimitAddr <= *BaseAddr) || (BufferSize == 0u)){
    return EFI_INVALID_PARAMETER;
  }

  Status = EFI_NOT_FOUND;

  BufferAddr = *BaseAddr;
  // Align the address to MB boundary
  BufferAddr = (BufferAddr + SIZE_1MB - 1) & ~(SIZE_1MB - 1);

  while ((BufferAddr + BufferSize) <= LimitAddr) {

    Status = gBS->AllocatePages (AllocateAddress,
                                 EfiReservedMemoryType,
                                 EFI_SIZE_TO_PAGES (BufferSize),
                                 &BufferAddr
                                );
    if (EFI_ERROR (Status)) {
      BufferAddr = BufferAddr + BufferSize;
      BufferAddr = (BufferAddr + SIZE_1MB - 1) & ~(SIZE_1MB - 1);
    } else {
      gBS->SetMem ((VOID *) BufferAddr, BufferSize, 0u);
      *BaseAddr = BufferAddr;
      Status = EFI_SUCCESS;
      break;
    }
  }

  return Status;
}

/**
 *
 *  AllocateRST
 *
 *  @param[in]  VOID
 *  @param[out] RstBaseAddrPtr
 *
 *  Description:
 *    Function to allocate RST
 *
 *
 *  @retval EFI_SUCCESS     RST allocated successfully
 *  @retval EFI_ERROR       RST memory allocation failed
 *
 */
EFI_STATUS
AllocateRST  (
  OUT EFI_PHYSICAL_ADDRESS* RstBaseAddrPtr
  )
{
  UINT32                       SegmentIndex;
  UINT64                       SegmentMappedSizeInGb;
  UINT64                       MappedSizeInBytesGbAligned;
  EFI_PHYSICAL_ADDRESS         RstBaseAddr;
  UINT32                       DramMapIndex;
  UINT32                       PrevDramMapIndex;
  EFI_STATUS                   Status = EFI_INVALID_PARAMETER;

  // Check for valid pointer
  if (RstBaseAddrPtr == NULL) {
    return Status;
  }

  ZeroMem((VOID*)&RST, sizeof(RST));
  ZeroMem ((VOID*)&RstAllocationBuffer, sizeof(RstAllocationBuffer));

  for (SegmentIndex = 0u; SegmentIndex < ActualSegmentCount; SegmentIndex++) {
    MappedSizeInBytesGbAligned = (RmpSegments[SegmentIndex].SegmentMappedSizeInBytes + SIZE_1GB - 1) & ~(SIZE_1GB - 1);
    SegmentMappedSizeInGb = MappedSizeInBytesGbAligned >> 30u;
    // Each entry in the RST Table should assigned value based on RMP Table Base address and Segment's Mapped Size in GB
    // The RMP Segment Table (RST) is a 4kB data structure that consists of 512 entries. Each entry is formatted as follows:
    // |------------------|----------------|------------|
    // | [63:52]          | [51:20]        | [19:0]     |
    // |------------------|----------------|------------|
    // |(Reserved, MBZ)   | RmpSegmentBase | MappedSize |
    // |------------------|----------------|------------|
    RST[SegmentIndex].Value = ((RmpSegments[SegmentIndex].SegmentRmpTableBaseAddr & RMP_SEGMENT_BASE_MASK) | (SegmentMappedSizeInGb & RMP_MAPPED_SIZE_MASK));
    RstAllocationBuffer[SegmentIndex] = RST[SegmentIndex].Value;

    IDS_HDT_CONSOLE (CPU_TRACE, "  AllocateRST RST Table SegmentIndex=0x%x, SegmentRmpTableBaseAddr=0x%lx, SegmentRmpTableSizeInBytes=0x%lx, SegmentMappedSizeInGb=0x%lx RST Entry=0x%lx\n",\
    SegmentIndex, RmpSegments[SegmentIndex].SegmentRmpTableBaseAddr, RmpSegments[SegmentIndex].SegmentRmpTableSizeInBytes, SegmentMappedSizeInGb, RST[SegmentIndex].Value);
  }

  // Get the DRAM Limit Address of Max Valid Address Range in Socket 0
  DramMapIndex = 0u;
  PrevDramMapIndex = 0u;
  while ((DramAddressRanges[DramMapIndex].SocketId == 0u) && (DramMapIndex <= MaxValidDramAddresRangeIndex)){
    PrevDramMapIndex = DramMapIndex;
    DramMapIndex++;
  }

  // Start finding the allocation base address below the Max DRAM Limit of Socket 0
  RstBaseAddr = DramAddressRanges[PrevDramMapIndex].DramLimitAddr;

  Status = gBS->AllocatePages (
               AllocateMaxAddress,
               EfiReservedMemoryType,
               EFI_SIZE_TO_PAGES (SIZE_1MB + SIZE_1MB),
               &RstBaseAddr
               );
    ASSERT (!EFI_ERROR (Status));
    if (EFI_ERROR (Status)) {
      IDS_HDT_CONSOLE (CPU_TRACE, "  [ERROR] Failed to allocate RST\n");
    } else {
      gBS->FreePages (RstBaseAddr, EFI_SIZE_TO_PAGES (SIZE_1MB + SIZE_1MB));

      IDS_HDT_CONSOLE (CPU_TRACE, "  AllocateRST RST Table AllocateMaxAddress RstBaseAddr=0x%lx\n", RstBaseAddr);

      // Once the base address in Max DRAM Limit of socket 0 is found, use that to allocate RST data structure
      RstBaseAddr = (RstBaseAddr + SIZE_1MB - 1) & ~(SIZE_1MB - 1);

      Status = gBS->AllocatePages (AllocateAddress,
                               EfiReservedMemoryType,
                               EFI_SIZE_TO_PAGES (SIZE_1MB),
                               &RstBaseAddr
                               );
      ASSERT (!EFI_ERROR (Status));
      if (EFI_ERROR (Status)) {
        IDS_HDT_CONSOLE (CPU_TRACE, "  [ERROR] Failed to allocate RST\n");
      } else {
        IDS_HDT_CONSOLE (CPU_TRACE, "  AllocateRST RST Table AllocateAddress RST Base Addr=0x%lx, RST Size=0x%lx\n", RstBaseAddr, sizeof(RstAllocationBuffer));

        gBS->SetMem ((VOID *) RstBaseAddr, SIZE_1MB, 0u);

        // Copy the RST Data Structure to Memory allocated for RST
        LibAmdMemCopy ((VOID *) (RstBaseAddr + SIZE_16KB), (VOID *)&RstAllocationBuffer, sizeof(RstAllocationBuffer), NULL);
        // return the RST allocation base address, to be set into RMP_BASE MSR
        *RstBaseAddrPtr = RstBaseAddr;
      }
    }
    return Status;
}

/**
 *
 *  InitSegmentedRmp
 *
 *  @param[in]  VOID
 *  @param[out] VOID
 *
 *  Description:
 *    Main function of the Segmented RMP Library that is called to
 *    from DXE driver to initialize Segmented RMP
 *
 *  @retval EFI_SUCCESS     Segmented RMP Initialized successfully
 *  @retval EFI_UNSUPPORTED Segmented RMP feature disabled or not supported
 *
 */
EFI_STATUS
InitSegmentedRmp  (
  VOID
  )
{
  EFI_STATUS Status;
  EFI_PHYSICAL_ADDRESS         RmpBaseAddr = 0u;
  UINT64                       MsrData;
  RMP_CFG_MSR                  RmpCfgMsr;

  // Check if Segmented RMP is enabled
  SegmentedRmpEnabled = PcdGetBool (PcdAmdSegmentedRmp);

  if (!SegmentedRmpEnabled) {
    IDS_HDT_CONSOLE (CPU_TRACE, "  InitSegmentedRmp Segmented RMP Not Enabled\n");
    return EFI_UNSUPPORTED;
  }
  // Read DRAM ranges as provided by DF IOM registers
  // To-Do: Enumerate CXL memory ranges
  InitializeDramAddressRanges ();

  // Read Segmented RMP configuration settings for the platform from CPUID registers
  GetSegmentedRmpConfig ();

  // Divide the memory ranges into segments using RmpSegSize
  CreateRmpSegments ();

  // Create RMP Table within each segment, based on the mapped memory size of that segment
  AllocateSegmentedRmpTables();

  // Allocate the RST Table which is a lookup for each RMP Table in individual segment
  Status = AllocateRST(&RmpBaseAddr);
  ASSERT (!EFI_ERROR (Status));

  if (EFI_ERROR (Status)) {
    IDS_HDT_CONSOLE (CPU_TRACE, "  [ERROR]InitSegmentedRmp Failed AllocateRST RmpBaseAddr=0x%lx, Status=0x%r\n", RmpBaseAddr, Status);
    return Status;
  } else {

    // Set RMP_BASE MSR
    MsrData = AsmReadMsr64 (MSR_RMP_BASE) & MSR_RMP_BASE_MASK;
    MsrData |= RmpBaseAddr;
    IDS_HDT_CONSOLE (CPU_TRACE, "  InitSegmentedRmp MSR RMP_BASE 0xC0010132 Value=0x%lx\n", MsrData);
    AsmWriteMsr64 (MSR_RMP_BASE, MsrData);

    // Set RMP_CFG MSR
    ZeroMem ((VOID*)&RmpCfgMsr, sizeof(RmpCfgMsr));
    RmpCfgMsr.Field.SegmentedRmpEn = 0x1u;
    RmpCfgMsr.Field.RmpSegSize = RmpSegSizeAsPowerOfTwo;
    MsrData = AsmReadMsr64 (MSR_RMP_CFG) & MSR_RMP_CFG_MASK;
    MsrData |= RmpCfgMsr.Value;
    IDS_HDT_CONSOLE (CPU_TRACE, "  InitSegmentedRmp MSR RMP_CFG 0xC0010136 Value=0x%lx\n", MsrData);
    AsmWriteMsr64 (MSR_RMP_CFG, MsrData);

    Status = EFI_SUCCESS;
  }

  return Status;
}

/**
 *
 *  GetAbove4GMMIOAddrRange
 *
 *  @param[out]  RMP_MEMORY_RANGES    Array of MMIO ranges to be covered
 *  @param[out]  RmpMmioRangesCount   MMIO ranges count
 *
 *  Description:
 *    Get Base and Limit of the 64bit MMIO ranges above 4GB
 *
 *  @retval EFI_SUCCESS
 *
 */
EFI_STATUS
EFIAPI
GetAbove4GMMIOAddrRange (
  OUT RMP_MEMORY_RANGES* RmpMmioRanges,
  OUT UINT16*            RmpMmioRangesCount
)
{
  FABRIC_RESOURCE_MANAGER_PROTOCOL*      FabricResourceManager = NULL;
  FABRIC_RESOURCE_FOR_EACH_RB            ResourceForEachRb;
  EFI_STATUS                             Status;
  UINTN                                  SocketCount;
  UINTN                                  RootBridgeCountPerSocket;
  AMD_FABRIC_TOPOLOGY_SERVICES2_PROTOCOL *FabricTopologyServices = NULL;
  UINT16                                 SocketRBMasks[BRH_MAX_SOCKETS];
  UINT16                                 RBMask;
  UINT8                                  Socket;
  UINT8                                  RootBridge;
  UINT16                                 RmpMmioRangeIndex;
  UINT8                                  RangeCoverageEn;

  Status = EFI_DEVICE_ERROR;

  if (!PcdGetBool(PcdAmdRmpCover64BitMMIORanges)) {
    IDS_HDT_CONSOLE (CPU_TRACE, "GetAbove4GMMIOAddrRange PcdAmdRmpCover64BitMMIORanges is FALSE\n");
    return EFI_UNSUPPORTED;
  }

  if (RmpMmioRanges == NULL) {
    IDS_HDT_CONSOLE (CPU_TRACE, "GetAbove4GMMIOAddrRange Parameter RmpMmioRanges is NULL\n");
    return EFI_INVALID_PARAMETER;
  }

  ZeroMem((VOID*)&SocketRBMasks, sizeof(SocketRBMasks));

  SocketRBMasks[0u] = PcdGet16(PcdAmdRmp64BitMmioS0RbMask);
  SocketRBMasks[1u] = PcdGet16(PcdAmdRmp64BitMmioS1RbMask);

  Status = gBS->LocateProtocol (&gAmdFabricTopologyServices2ProtocolGuid, NULL, (VOID **)&FabricTopologyServices);
  if (EFI_ERROR(Status) || EFI_ERROR (FabricTopologyServices->GetSystemInfo (FabricTopologyServices, &SocketCount, NULL, NULL, NULL, NULL))) {
    SocketCount = 1;
  }

  // Get available FabricResource
  Status = gBS->LocateProtocol (
                  &gAmdFabricResourceManagerServicesProtocolGuid,
                  NULL,
                  (VOID **)&FabricResourceManager
                  );
  ASSERT_EFI_ERROR (Status);
  if (EFI_ERROR (Status)) {
    IDS_HDT_CONSOLE (CPU_TRACE, "GetAbove4GMMIOAddrRange Failed to locate FabricResourceManagerServicesProtocol\n");
    return Status;
  }

  Status = FabricResourceManager->FabricGetAvailableResource (FabricResourceManager, &ResourceForEachRb);
  RmpMmioRangeIndex = 0u;

  if (!EFI_ERROR(Status)) {
    for (Socket=0u; Socket < SocketCount; Socket++){
      Status = FabricTopologyServices->GetProcessorInfo(FabricTopologyServices, Socket, NULL, &RootBridgeCountPerSocket);
      if (!EFI_ERROR(Status)) {
        for (RootBridge=0u; RootBridge < RootBridgeCountPerSocket; RootBridge++) {
          RBMask = (UINT16)(0x1u << RootBridge);
          RangeCoverageEn = (UINT8)((SocketRBMasks[Socket] & RBMask) >> RootBridge);
          IDS_HDT_CONSOLE (CPU_TRACE, "GetAbove4GMMIOAddrRange Socket=0x%x, RootBridge=0x%x, Range En=0x%x\n", Socket, RootBridge, RangeCoverageEn);
          if (RangeCoverageEn) {
            RmpMmioRanges[RmpMmioRangeIndex].RmpMemoryBaseAddr = ResourceForEachRb.PrefetchableMmioSizeAbove4G[Socket][RootBridge].Base;
            RmpMmioRanges[RmpMmioRangeIndex].RmpMemoryLimitAddr = ResourceForEachRb.PrefetchableMmioSizeAbove4G[Socket][RootBridge].Base + \
                                                                ResourceForEachRb.PrefetchableMmioSizeAbove4G[Socket][RootBridge].Size - 1;

            IDS_HDT_CONSOLE (CPU_TRACE, "GetAbove4GMMIOAddrRange RmpMmioRanges Index=0x%x, Base=0x%lx, Limit=0x%lx\n", \
                                                                             RmpMmioRangeIndex,
                                                                             RmpMmioRanges[RmpMmioRangeIndex].RmpMemoryBaseAddr,
                                                                             RmpMmioRanges[RmpMmioRangeIndex].RmpMemoryLimitAddr);

            RmpMmioRangeIndex++;
            ASSERT(RmpMmioRangeIndex <= (BRH_MAX_SOCKETS*BRH_MAX_HOST_BRIDGES_PER_SOCKET));
          }
        }
      } else {
        IDS_HDT_CONSOLE (CPU_TRACE, "GetAbove4GMMIOAddrRange Failed to GetProcessorInfo. Socket=0x%x\n", Socket);
        break;
      }
    }
  }
  *RmpMmioRangesCount = RmpMmioRangeIndex;
  return Status;
}

/**
 *
 *  CheckIfRangesOverlap
 *
 *  @param[in]  range1   Range 1 to check overlap
 *  @param[in]  range2   Range 2 to check overlap
 *  @param[out] overlap  Overlap range
 *  Description:
 *    Check if ranges overlap
 *
 *  @retval BOOLEAN
 *
 */
BOOLEAN
EFIAPI
CheckIfRangesOverlap(
  IN ADDR_RANGE* range1,
  IN ADDR_RANGE* range2,
  OUT ADDR_RANGE* overlap
  )
{
  BOOLEAN IsOverlap;

  if (range1 == NULL || range2 == NULL || overlap == NULL){
    IDS_HDT_CONSOLE (CPU_TRACE, "CheckIfRangesOverlap - Invalid Parameters");
    return FALSE;
  }

  if (range1->BaseAddr > range2->LimitAddr || range1->LimitAddr < range2->BaseAddr){
    IsOverlap = FALSE;
    IDS_HDT_CONSOLE (CPU_TRACE, "CheckIfRangesOverlap - R1.Base=0x%lx, R1.Limit=0x%lx, "\
                     "R2.Base=0x%lx, R2.Limit=0x%lx, IsOverlap=%d\n",
                     range1->BaseAddr, range1->LimitAddr, range2->BaseAddr, range2->LimitAddr, IsOverlap);
  } else {
    IsOverlap = TRUE;

    if (range1->BaseAddr < range2->BaseAddr){
      overlap->BaseAddr = range2->BaseAddr;
    } else {
      overlap->BaseAddr = range1->BaseAddr;
    }

    if (range1->LimitAddr > range2->LimitAddr){
      overlap->LimitAddr = range2->LimitAddr;
    } else {
      overlap->LimitAddr = range1->LimitAddr;
    }

    IDS_HDT_CONSOLE (CPU_TRACE, "CheckIfRangesOverlap - R1.Base=0x%lx, R1.Limit=0x%lx,\n\t\t"\
                     "R2.Base=0x%lx, R2.Limit=0x%lx,\n\t\t"\
                     "O.Base=0x%lx, O.Limit=0x%lx,\n\t\t"\
                     "IsOverlap=%d\n",
                     range1->BaseAddr, range1->LimitAddr, range2->BaseAddr, range2->LimitAddr,
                     overlap->BaseAddr, overlap->LimitAddr,
                     IsOverlap);

  }

  return IsOverlap;
}
