/*****************************************************************************
 * Copyright (C) 2008-2024 Advanced Micro Devices, Inc. All rights reserved.
 *
*****************************************************************************
*/
/* $NoKeywords:$ */
/**
 * @file
 *
 * FCH IO access common routine
 *
 *
 *
 * @xrefitem bom "File Content Label" "Release Content"
 * @e project:     AGESA
 * @e sub-project: FCH
 * @e \$Revision: 309090 $   @e \$Date: 2014-12-09 10:28:05 -0800 (Tue, 09 Dec 2014) $
 *
 */

#include "FchPlatform.h"
#define FILECODE FCH_KUNLUN_FCHKUNLUNCORE_COMMON_FCHPELIB_FILECODE

/*----------------------------------------------------------------------------------------*/
/**
 * ProgramPciByteTable - Program PCI register by table (8 bits data)
 *
 *
 *
 * @param[in] pPciByteTable    - Table data pointer
 * @param[in] dwTableSize      - Table length
 * @param[in] StdHeader
 *
 */
VOID
ProgramPciByteTable (
  IN       REG8_MASK           *pPciByteTable,
  IN       UINT16              dwTableSize,
  IN       AMD_CONFIG_PARAMS   *StdHeader
  )
{
  UINT8     i;
  UINT8     dbBusNo;
  UINT8     dbDevFnNo;
  UINTN     PciAddress;

  dbBusNo = pPciByteTable->RegIndex;
  dbDevFnNo = pPciByteTable->AndMask;
  pPciByteTable++;

  for ( i = 1; i < dwTableSize; i++ ) {
    if ( (pPciByteTable->RegIndex == 0xFF) && (pPciByteTable->AndMask == 0xFF) && (pPciByteTable->OrMask == 0xFF) ) {
      pPciByteTable++;
      dbBusNo = pPciByteTable->RegIndex;
      dbDevFnNo = pPciByteTable->AndMask;
      pPciByteTable++;
      i++;
    } else {
      PciAddress = (dbBusNo << 20) + (dbDevFnNo << 12) + pPciByteTable->RegIndex;
      PciAndThenOr8 (PciAddress, pPciByteTable->AndMask, pPciByteTable->OrMask);
      pPciByteTable++;
    }
  }
}

/*----------------------------------------------------------------------------------------*/
/**
 * ProgramFchAcpiMmioTbl - Program FCH ACPI MMIO register by table (8 bits data)
 *
 *
 *
 * @param[in] pAcpiTbl   - Table data pointer
 * @param[in] StdHeader
 *
 */
VOID
ProgramFchAcpiMmioTbl (
  IN       ACPI_REG_WRITE      *pAcpiTbl,
  IN       AMD_CONFIG_PARAMS   *StdHeader
  )
{
  UINT8   i;
  UINT8   Or8;
  UINT8   Mask8;
  UINT32  ddtempVar;

  if (pAcpiTbl != NULL) {
    if ((pAcpiTbl->MmioReg == 0) && (pAcpiTbl->MmioBase == 0) && (pAcpiTbl->DataAndMask == 0xB0) && (pAcpiTbl->DataOrMask == 0xAC)) {
      // Signature Checking
      pAcpiTbl++;
      for ( i = 1; pAcpiTbl->MmioBase < 0x1D; i++ ) {
        ddtempVar = ACPI_MMIO_BASE | (pAcpiTbl->MmioBase) << 8 | pAcpiTbl->MmioReg;
        Or8 = pAcpiTbl->DataOrMask;
        Mask8 = ~pAcpiTbl->DataAndMask;
        LibAmdMemRMW (AccessWidth8, (UINT64) ddtempVar, &Or8, &Mask8, StdHeader);
        pAcpiTbl++;
      }
    }
  }
}

/*----------------------------------------------------------------------------------------*/
/**
 * ProgramFchSciMapTbl - Program FCH SCI Map table (8 bits data)
 *
 *
 *
 * @param[in] pSciMapTbl   - Table data pointer
 * @param[in] FchResetDataBlock
 *
 */
VOID
ProgramFchSciMapTbl (
  IN       SCI_MAP_CONTROL  *pSciMapTbl,
  IN       FCH_RESET_DATA_BLOCK *FchResetDataBlock
  )
{
  AMD_CONFIG_PARAMS   *StdHeader;

  UINT32  ddtempVar;
  StdHeader = FchResetDataBlock->StdHeader;

  if (pSciMapTbl != NULL) {
    while (pSciMapTbl->InputPin != 0xFF) {
      if ((pSciMapTbl->InputPin >= 0x40) && (pSciMapTbl->InputPin < 0x80) && (pSciMapTbl->GpeMap < 0x20)) {
        ddtempVar = ACPI_MMIO_BASE | SMI_BASE | pSciMapTbl->InputPin;
        LibAmdMemWrite (AccessWidth8, (UINT64) ddtempVar, &pSciMapTbl->GpeMap, StdHeader);
      } else {
        //Assert Warning "SCI map is invalid"
      }
      pSciMapTbl++;
    }
  }
}

/*----------------------------------------------------------------------------------------*/
/**
 * ProgramSataPhyTbl - Program FCH Sata Phy table (8 bits data)
 *
 *
 *
 * @param[in] pSataPhyTbl   - Table data pointer
 * @param[in] FchResetDataBlock
 *
 */
VOID
ProgramFchSataPhyTbl (
  IN       SATA_PHY_CONTROL  *pSataPhyTbl,
  IN       FCH_RESET_DATA_BLOCK *FchResetDataBlock
  )
{
  if (pSataPhyTbl != NULL) {
    while (pSataPhyTbl->PhyData != 0xFFFFFFFF) {
      //to be implemented
      pSataPhyTbl++;
    }
  }
}

/**
 * GetChipSysMode - Get Chip status
 *
 *
 * @param[in] Value - Return Chip strap status
 *   StrapStatus [15.0] - Hudson-2 chip Strap Status
 *    @li <b>0001</b> - Not USED FWH
 *    @li <b>0002</b> - Not USED LPC ROM
 *    @li <b>0004</b> - EC enabled
 *    @li <b>0008</b> - Reserved
 *    @li <b>0010</b> - Internal Clock mode
 * @param[in] StdHeader
 *
 */
VOID
GetChipSysMode (
  IN       VOID                *Value,
  IN       AMD_CONFIG_PARAMS   *StdHeader
  )
{
  LibAmdMemRead (AccessWidth8, (UINT64) (ACPI_MMIO_BASE + MISC_BASE + FCH_MISC_REG80), Value, StdHeader);
}

/**
 * IsImcEnabled - Is IMC Enabled
 * @retval  TRUE for IMC Enabled; FALSE for IMC Disabled
 */
BOOLEAN
IsImcEnabled (
  IN       AMD_CONFIG_PARAMS   *StdHeader
  )
{
  return FALSE;
}


/**
 * GetEfuseStatue - Get Efuse status
 *
 *
 * @param[in] Value - Return Chip strap status
 * @param[in] StdHeader
 *
 */
VOID
GetEfuseStatus (
  IN       VOID                *Value,
  IN       AMD_CONFIG_PARAMS   *StdHeader
  )
{
  UINT8    Or8;
  UINT8    Mask8;

  Or8 = BIT5;
  Mask8 = BIT5;
  LibAmdMemRMW (AccessWidth8, (UINT64) (ACPI_MMIO_BASE + PMIO_BASE + FCH_PMIOA_REGC8), &Or8, &Mask8, StdHeader);
  LibAmdMemWrite (AccessWidth8, (UINT64) (ACPI_MMIO_BASE + PMIO_BASE + FCH_PMIOA_REGD8), Value, StdHeader);
  LibAmdMemRead (AccessWidth8, (UINT64) (ACPI_MMIO_BASE + PMIO_BASE + FCH_PMIOA_REGD8 + 1), Value, StdHeader);
  Or8 = 0;
  Mask8 = BIT5;
  LibAmdMemRMW (AccessWidth8, (UINT64) (ACPI_MMIO_BASE + PMIO_BASE + FCH_PMIOA_REGC8), &Or8, &Mask8, StdHeader);
}

/*----------------------------------------------------------------------------------------*/
/**
 * SbSleepTrapControl - SB Sleep Trap Control
 *
 *
 *
 * @param[in] SleepTrap    - Whether sleep trap is enabled
 *
 */
VOID
SbSleepTrapControl (
  IN        BOOLEAN          SleepTrap
  )
{
  if (SleepTrap) {
    ACPIMMIO32 (ACPI_MMIO_BASE + SMI_BASE + FCH_SMI_REGB0) &= ~(BIT2 + BIT3);
    ACPIMMIO32 (ACPI_MMIO_BASE + SMI_BASE + FCH_SMI_REGB0) |= BIT2;
    ACPIMMIO8 (ACPI_MMIO_BASE + PMIO_BASE + FCH_PMIOA_REGBE) &= ~ (BIT5);
    ACPIMMIO8 (ACPI_MMIO_BASE + PMIO_BASE + 0xB) &= ~ (BIT0 + BIT1);
    ACPIMMIO8 (ACPI_MMIO_BASE + PMIO_BASE + 0xB) |= BIT1;
  } else {
    ACPIMMIO8 (ACPI_MMIO_BASE + PMIO_BASE + FCH_PMIOA_REGBE) |= BIT5;
    ACPIMMIO8 (ACPI_MMIO_BASE + PMIO_BASE + 0xB) &= ~ (BIT0 + BIT1);
    ACPIMMIO8 (ACPI_MMIO_BASE + PMIO_BASE + 0xB) |= BIT0;
    ACPIMMIO32 (ACPI_MMIO_BASE + SMI_BASE + FCH_SMI_REGB0) &= ~(BIT2 + BIT3);
  }
}


/*----------------------------------------------------------------------------------------*/
/**
 * ProgramEmmcPins - Program eMMC pins
 *
 *
 *
 * @param[in] EmmcEn      - True to eMMC, False to keep as default for LPC
 *
 */
VOID
ProgramEmmcPins (
  IN       BOOLEAN   EmmcEn
  )
{
  if (EmmcEn) {
    ACPIMMIO8 (ACPI_MMIO_BASE + IOMUX_BASE + FCH_IOMUX_REG15) = 1;
    ACPIMMIO8 (ACPI_MMIO_BASE + IOMUX_BASE + FCH_IOMUX_REG16) = 1;
    ACPIMMIO8 (ACPI_MMIO_BASE + IOMUX_BASE + FCH_IOMUX_REG20) = 1;
    ACPIMMIO8 (ACPI_MMIO_BASE + IOMUX_BASE + FCH_IOMUX_REG44) = 1;
    ACPIMMIO8 (ACPI_MMIO_BASE + IOMUX_BASE + FCH_IOMUX_REG46) = 1;
    ACPIMMIO8 (ACPI_MMIO_BASE + IOMUX_BASE + FCH_IOMUX_REG68) = 1;
    ACPIMMIO8 (ACPI_MMIO_BASE + IOMUX_BASE + FCH_IOMUX_REG69) = 1;
    ACPIMMIO8 (ACPI_MMIO_BASE + IOMUX_BASE + FCH_IOMUX_REG6A) = 1;
    ACPIMMIO8 (ACPI_MMIO_BASE + IOMUX_BASE + FCH_IOMUX_REG6B) = 1;
    ACPIMMIO8 (ACPI_MMIO_BASE + IOMUX_BASE + FCH_IOMUX_REG4A) = 1;
    ACPIMMIO8 (ACPI_MMIO_BASE + IOMUX_BASE + FCH_IOMUX_REG58) = 1;
    ACPIMMIO8 (ACPI_MMIO_BASE + IOMUX_BASE + FCH_IOMUX_REG4B) = 1;
    ACPIMMIO8 (ACPI_MMIO_BASE + IOMUX_BASE + FCH_IOMUX_REG57) = 1;
    ACPIMMIO8 (ACPI_MMIO_BASE + IOMUX_BASE + FCH_IOMUX_REG6D) = 1;
    ACPIMMIO8 (ACPI_MMIO_BASE + IOMUX_BASE + FCH_IOMUX_REG1F) = 1;
  } else {
    ACPIMMIO8 (ACPI_MMIO_BASE + IOMUX_BASE + FCH_IOMUX_REG15) = 0;
    ACPIMMIO8 (ACPI_MMIO_BASE + IOMUX_BASE + FCH_IOMUX_REG20) = 0;
    //ACPIMMIO8 (ACPI_MMIO_BASE + IOMUX_BASE + FCH_IOMUX_REG44) = 0;
    //ACPIMMIO8 (ACPI_MMIO_BASE + IOMUX_BASE + FCH_IOMUX_REG46) = 0;
    ACPIMMIO8 (ACPI_MMIO_BASE + IOMUX_BASE + FCH_IOMUX_REG68) = 0;
    ACPIMMIO8 (ACPI_MMIO_BASE + IOMUX_BASE + FCH_IOMUX_REG69) = 0;
    ACPIMMIO8 (ACPI_MMIO_BASE + IOMUX_BASE + FCH_IOMUX_REG6A) = 0;
    ACPIMMIO8 (ACPI_MMIO_BASE + IOMUX_BASE + FCH_IOMUX_REG6B) = 0;
    ACPIMMIO8 (ACPI_MMIO_BASE + IOMUX_BASE + FCH_IOMUX_REG4A) = 0;
    ACPIMMIO8 (ACPI_MMIO_BASE + IOMUX_BASE + FCH_IOMUX_REG58) = 0;
    ACPIMMIO8 (ACPI_MMIO_BASE + IOMUX_BASE + FCH_IOMUX_REG4B) = 0;
    ACPIMMIO8 (ACPI_MMIO_BASE + IOMUX_BASE + FCH_IOMUX_REG57) = 0;
    ACPIMMIO8 (ACPI_MMIO_BASE + IOMUX_BASE + FCH_IOMUX_REG6D) = 0;
    ACPIMMIO8 (ACPI_MMIO_BASE + IOMUX_BASE + FCH_IOMUX_REG1F) = 0;
  }
}

/*----------------------------------------------------------------------------------------*/
/**
 * ProgramSdPins - Program SD pins
 *
 *
 *
 * @param[in] SdEn      - True to SD function, False to keep as default for LPC
 *
 */
VOID
ProgramSdPins (
  IN       BOOLEAN   SdEn
  )
{
  if (SdEn) {
    ACPIMMIO8 (ACPI_MMIO_BASE + IOMUX_BASE + FCH_IOMUX_REG15) = 1;
    ACPIMMIO8 (ACPI_MMIO_BASE + IOMUX_BASE + FCH_IOMUX_REG16) = 1;
    ACPIMMIO8 (ACPI_MMIO_BASE + IOMUX_BASE + FCH_IOMUX_REG20) = 1;
    ACPIMMIO8 (ACPI_MMIO_BASE + IOMUX_BASE + FCH_IOMUX_REG44) = 1;
    ACPIMMIO8 (ACPI_MMIO_BASE + IOMUX_BASE + FCH_IOMUX_REG46) = 1;
    ACPIMMIO8 (ACPI_MMIO_BASE + IOMUX_BASE + FCH_IOMUX_REG68) = 1;
    ACPIMMIO8 (ACPI_MMIO_BASE + IOMUX_BASE + FCH_IOMUX_REG69) = 1;
    ACPIMMIO8 (ACPI_MMIO_BASE + IOMUX_BASE + FCH_IOMUX_REG6A) = 1;
    ACPIMMIO8 (ACPI_MMIO_BASE + IOMUX_BASE + FCH_IOMUX_REG6B) = 1;
    ACPIMMIO8 (ACPI_MMIO_BASE + IOMUX_BASE + FCH_IOMUX_REG1F) = 1;
  } else {
    ACPIMMIO8 (ACPI_MMIO_BASE + IOMUX_BASE + FCH_IOMUX_REG15) = 0;
    ACPIMMIO8 (ACPI_MMIO_BASE + IOMUX_BASE + FCH_IOMUX_REG20) = 0;
    //ACPIMMIO8 (ACPI_MMIO_BASE + IOMUX_BASE + FCH_IOMUX_REG44) = 0;
    //ACPIMMIO8 (ACPI_MMIO_BASE + IOMUX_BASE + FCH_IOMUX_REG46) = 0;
    ACPIMMIO8 (ACPI_MMIO_BASE + IOMUX_BASE + FCH_IOMUX_REG68) = 0;
    ACPIMMIO8 (ACPI_MMIO_BASE + IOMUX_BASE + FCH_IOMUX_REG69) = 0;
    ACPIMMIO8 (ACPI_MMIO_BASE + IOMUX_BASE + FCH_IOMUX_REG6A) = 0;
    ACPIMMIO8 (ACPI_MMIO_BASE + IOMUX_BASE + FCH_IOMUX_REG6B) = 0;
    ACPIMMIO8 (ACPI_MMIO_BASE + IOMUX_BASE + FCH_IOMUX_REG1F) = 0;
  }
}


/* -----------------------------------------------------------------------------*/
/**
 *
 *
 *   This function Checks Brh Client
 *
 *   NOTE:
 *
 *
 */
BOOLEAN
FchCheckBrhClient (
  )
{
  return FALSE;
}


