#;*****************************************************************************
#;
#; Copyright (C) 2008-2024 Advanced Micro Devices, Inc. All rights reserved.
#;
#;******************************************************************************
[Defines]
  INF_VERSION                    = 0x00010006
  BASE_NAME                      = AmdPspFlashAccLibDxe
  FILE_GUID                      = 971D3B03-2062-49D9-90C5-A157DFD40B30
  MODULE_TYPE                    = DXE_DRIVER
  VERSION_STRING                 = 1.0
  LIBRARY_CLASS                  = AmdPspFlashAccLib | DXE_DRIVER

[Sources.common]
  AmdPspFlashAccLibDxe.c

[Packages]
  MdePkg/MdePkg.dec
  AgesaPkg/AgesaPkg.dec
  AgesaModulePkg/AgesaCommonModulePkg.dec
  AgesaModulePkg/AgesaModulePspPkg.dec

[LibraryClasses]
  UefiBootServicesTableLib
  MemoryAllocationLib
  BaseMemoryLib

[Guids]
  gPspSmmCommHandleGuid    ## CONSUMES

[Protocols]
  gPspFlashAccSmmCommReadyProtocolGuid    ## CONSUMES
  gEfiSmmCommunicationProtocolGuid        ## CONSUMES

[Pcd]
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdAmdSmmCommunicationAddress

[Depex]
#  gPspFlashAccSmmCommReadyProtocolGuid AND
#  gEfiSmmCommunicationProtocolGuid
  TRUE


