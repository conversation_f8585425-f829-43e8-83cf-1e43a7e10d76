/*
 ******************************************************************************
 *
 * Copyright (C) 2024 Advanced Micro Devices, Inc. All rights reserved.
 *
 *******************************************************************************
 */
/**
 * @file
 *
 * AMD CCX Startup code fixups
 *
 *
 * @xrefitem bom "File Content Label" "Release Content"
 * @e project:      AGESA
 * @e sub-project:  Ccx
 * @e \$Revision$   @e \$Date$
 *
 */

#include <PiPei.h>
#include <Library/BaseLib.h>
#include "AmdCcxZen5Pei.h"
#include <Filecode.h>


#define FILECODE CCX_ZEN5_PEI_X64_APSTARTUPFIXUPS_FILECODE


extern X86_ASSEMBLY_PATCH_LABEL  gApStartupCode;
extern X86_ASSEMBLY_PATCH_LABEL  gPatchProtectedModeJump;
extern X86_ASSEMBLY_PATCH_LABEL  gPatchPageTable;
extern X86_ASSEMBLY_PATCH_LABEL  gPatchLongModeJump;
extern X86_ASSEMBLY_PATCH_LABEL  gPatchApEntryInCOffset;
extern X86_ASSEMBLY_PATCH_LABEL  gPatchApLaunchGlobalData;
extern X86_ASSEMBLY_PATCH_LABEL  gApStartupCodeEnd;

/*
 *  Fix up startup assembly code to new location location
 *
 * @param[in] ApStartupMemoryMap  Structure to location of Startup Assembly code, GDT, etc.
 * @param[in] ApLaunchGlobalData  Potinter to Global Data used by AP Startup code.
 *
 */
VOID ApStartupFixups (AP_STARTUP_MEMORY_MAP *ApStartupMemoryMap, VOID *ApLaunchGlobalData)
{
  UINTN  ApStartupCodeBase = ApStartupMemoryMap->ApStartupCodeBase;
  UINT32 CsBase = ApStartupCodeBase & 0xffff0000;
  UINT32 PatchProtModeJumpOffset = (UINT32)((UINTN)gPatchProtectedModeJump - (UINTN)gApStartupCode);
  UINT32 PatchPageTableOffset = (UINT32)((UINTN)gPatchPageTable - (UINTN)gApStartupCode);
  UINT32 PatchLongModeJumpOffset = (UINT32)((UINTN)gPatchLongModeJump - (UINTN)gApStartupCode);
  UINT32 PatchApEntryOffset = (UINT32)((UINTN)gPatchApEntryInCOffset - (UINTN)gApStartupCode);
  UINT32 PatchApLaunchGlobalDataOffset = (UINT32)((UINTN)gPatchApLaunchGlobalData - (UINTN)gApStartupCode);
  UINT32 ApEntryInCOffset = (UINT32)(UINTN)ApAsmCode;
  UINT32 ProtModeFarJmpOffset = (UINT32)ApStartupCodeBase + PatchProtModeJumpOffset;
  UINT32 LongModeFarJmpOffset = (UINT32)ApStartupCodeBase + PatchLongModeJumpOffset;

  PatchInstructionX86 ((X86_ASSEMBLY_PATCH_LABEL*)(ApStartupCodeBase + PatchProtModeJumpOffset - 2), ProtModeFarJmpOffset, 4);
  PatchInstructionX86 ((X86_ASSEMBLY_PATCH_LABEL*)(ApStartupCodeBase + PatchPageTableOffset), CsBase + 0xffe8, 4);
  PatchInstructionX86 ((X86_ASSEMBLY_PATCH_LABEL*)(ApStartupCodeBase + PatchLongModeJumpOffset - 2), LongModeFarJmpOffset, 4);
  PatchInstructionX86 ((X86_ASSEMBLY_PATCH_LABEL*)(ApStartupCodeBase + PatchApEntryOffset), ApEntryInCOffset, 4);
  PatchInstructionX86 ((X86_ASSEMBLY_PATCH_LABEL*)(ApStartupCodeBase + PatchApLaunchGlobalDataOffset), (UINT32)(UINTN)ApLaunchGlobalData, 4);
}
