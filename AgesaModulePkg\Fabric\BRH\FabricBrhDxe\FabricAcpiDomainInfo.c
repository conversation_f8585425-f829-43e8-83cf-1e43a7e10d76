/*****************************************************************************
 *
 * Copyright (C) 2019-2024 Advanced Micro Devices, Inc. All rights reserved.
 *
 *****************************************************************************
 */

/*----------------------------------------------------------------------------------------
 *                             M O D U L E S    U S E D
 *----------------------------------------------------------------------------------------
 */
#include "Porting.h"
#include "AMD.h"
#include "FabricAcpiDomainInfo.h"
#include "FabricAcpiTable.h"
#include <Library/BaseLib.h>
#include <Library/AmdBaseLib.h>
#include <Library/BaseFabricTopologyLib.h>
#include <FabricRegistersBrh.h>
#include <FabricInfoBrh.h>
#include <Library/FabricRegisterAccLib.h>
#include <Library/AmdIdsHookLib.h>
#include <Library/UefiBootServicesTableLib.h>
#include <Library/AmdSocBaseLib.h>
#include <Protocol/FabricNumaServices2.h>
#include <Addendum/Apcb/Inc/BRH/APCB.h>
#include <Addendum/Apcb/Inc/BRH/APOB.h>
#include <Library/AmdPspApobLib.h>
#include <Protocol/AmdAcpiSratServicesProtocol.h>
#include <Protocol/AmdCxlServicesProtocol.h>
#include "Filecode.h"

/*----------------------------------------------------------------------------------------
 *                   D E F I N I T I O N S    A N D    M A C R O S
 *----------------------------------------------------------------------------------------
 */
#define FILECODE FABRIC_BRH_FABRICBRHDXE_FABRICACPIDOMAININFO_FILECODE

#define CS_CMP_SOCKET_MAP (((1 << BRH_NUM_CS_CMP_BLOCKS) - 1) << BRH_NUM_CS_UMC_BLOCKS)
#define CS_CMP_SYSTEM_MAP (CS_CMP_SOCKET_MAP | (CS_CMP_SOCKET_MAP << NORMALIZED_SOCKET_SHIFT))

#define SOCKET_CS_MAP ((1 << NORMALIZED_SOCKET_SHIFT) - 1)

#define MAX_HOISTED            3  // VGA, TOM, 1TB

#define DRAM_REGION_REGISTER_OFFSET (DRAMBASEADDRESS_1_REG - DRAMBASEADDRESS_0_REG)

#define SIZE_2MB_RSH16      (SIZE_2MB >> 16)
#define SIZE_2MB_RSH16_MASK (SIZE_2MB_RSH16 - 1)

#define MAX_ACPI_MEM_ENTRIES  ((BRH_NUMBER_OF_DRAM_REGIONS * BRH_NUM_CS_BLOCKS * BRH_MAX_SOCKETS) + MAX_HOISTED)

/*----------------------------------------------------------------------------------------
 *                  T Y P E D E F S     A N D     S T R U C T U R E S
 *----------------------------------------------------------------------------------------
 */
typedef struct {
  UINT32  Domain;
  UINT32  BaseLo;
  UINT32  BaseHi;
  UINT32  SizeLo;
  UINT32  SizeHi;
} ACPI_MEM_ENTRY_INFO;

typedef struct {
  UINT32  NormalizedMap;
  UINT32  RawBase;
  UINT32  RawLimit;
  UINT32  RawSize;
} MEM_REGION;

typedef struct {
  UINT32   Base;
  UINT32   Limit;
  BOOLEAN  MemoryLost;
} HOIST_REGION;

typedef struct {
  UINTN            PhyRBNum; ///< Physical root bridge number
  UINTN            BusBase;  ///< Bus# for Root-complex
  UINT32           RrIndex;  ///< Round-Robin Index
  PXM_DOMAIN_INFO  PxmInfo;  ///< Assigned PXM Domain(s)
} ROOT_COMPLEX_ASSIGNMENT;

/*----------------------------------------------------------------------------------------
 *           P R O T O T Y P E S     O F     L O C A L     F U N C T I O N S
 *----------------------------------------------------------------------------------------
 */
EFI_STATUS
BuildDomainInfo (
  VOID
  );

EFI_STATUS
EFIAPI
FabricBrhGetDomainInfo (
  IN     FABRIC_NUMA_SERVICES2_PROTOCOL  *This,
     OUT UINT32                          *NumberOfDomainsInSystem,
     OUT DOMAIN_INFO2                    **DomainInfo,
     OUT BOOLEAN                         *CcxAsNumaDomain
  );

EFI_STATUS
EFIAPI
FabricBrhDomainXlat (
  IN     FABRIC_NUMA_SERVICES2_PROTOCOL  *This,
  IN     UINTN                           Socket,
  IN     UINTN                           Die,
  IN     UINTN                           Ccd,
  IN     UINTN                           Ccx,
     OUT UINT32                          *Domain
  );

EFI_STATUS
EFIAPI
FabricBrhGetPhysDomainInfo (
  IN     FABRIC_NUMA_SERVICES2_PROTOCOL  *This,
     OUT UINT32                          *NumberOfPhysDomainsInSystem,
     OUT PHYS_DOMAIN_INFO                **PhysDomainInfo,
     OUT UINT32                          *PhysNodesPerSocket,
     OUT UINT32                          *NumberOfSystemSLinkDomains
  );

EFI_STATUS
EFIAPI
FabricBrhGetPxmDomainInfo (
  IN     FABRIC_NUMA_SERVICES2_PROTOCOL  *This,
  IN     PCI_ADDR                        RootPortBdf,
     OUT PXM_DOMAIN_INFO                 *PxmDomainInfo
  );

UINT8
FabricGetMaxDomains (
  VOID
  );

UINT32
BuildCxlInfo (
  IN UINT32  Index
  );

VOID
BuildCcdInfo (
  IN UINT32  SocketCount
  );

UINT32
GetPhysDomainOfCcd (
  IN UINT32  Socket,
  IN UINT32  PhysCcd
  );

UINT32
GetSocketMapFromNormalizedCsMap (
  IN UINT32  NormalizedCsMap
  );

EFI_STATUS
FabricCreateSystemAcpiDomainData (
  IN OUT UINT8                           **TableEnd,
  IN     PF_PUBLISH_ACPI_NUMA_MEM_ENTRY  PublishMemEntry,
     OUT UINT32                          *NumberOfEntriesPublished
  );

EFI_STATUS
CollectSystemAcpiDomainData (
  VOID
  );

EFI_STATUS
FabricGetMemoryInfo (
  OUT UINT32       *NumberOfDomains,
  OUT MEMORY_INFO  **MemoryInfo
  );

VOID
StoreMemEntry (
     OUT ACPI_MEM_ENTRY_INFO  *Entry,
  IN     UINT32               Domain,
  IN     UINT32               Base,
  IN     UINT32               Size
  );

UINT32
GetNumberOfSetBits (
  IN UINT32  BitMap
  );

EFI_STATUS
GetNthBitSet (
  IN     UINT32  BitMap,
  IN     UINT32  N,
     OUT UINT32  *BitPosition
  );

VOID
InitPxmDomainInfo (
  IN     UINTN            SocketId,
  IN     UINTN            PhyRBNum,
  IN     UINTN            NodesPerSocket,
  IN     UINT8            ActualNps,
  IN OUT UINT8            *NumOfNodesAssigned,
     OUT PXM_DOMAIN_INFO  *PxmDomainInfo
  );

UINT32
GetMaxAllowableNpsForCcxAsNuma (
  VOID
);

/*----------------------------------------------------------------------------------------
 *                           G L O B A L   V A R I A B L E S
 *----------------------------------------------------------------------------------------
 */

BOOLEAN           mDomainInfoValid = FALSE;

UINT32            mNumberOfReportedDomains = 0;
DOMAIN_INFO2      mReportedDomainInfo[MAX_REPORTED_DOMAINS]   = {0};
UINT32            mReportedDomainCcxMap[MAX_REPORTED_DOMAINS] = {0};
BOOLEAN           mCcxAsNuma = FALSE;
UINT32            mCcdCount[BRH_MAX_SOCKETS]  = {0};
UINT32            mCcxPerCcd[BRH_MAX_SOCKETS] = {0};
UINT32            mLogToPhysCcd[BRH_MAX_SOCKETS][BRH_MAX_CCD_PER_SOCKET] = {0};
UINT32            mNumberOfPhysicalDomains = 0;
PHYS_DOMAIN_INFO  mPhysicalDomainInfo[MAX_PHYSICAL_DOMAINS];
UINT32            mPhysNps;
UINT32            mSystemCxlCount;

UINT32               mNumberOfAcpiMemEntries = 0;
ACPI_MEM_ENTRY_INFO  mAcpiMemEntryInfo[MAX_ACPI_MEM_ENTRIES];
UINT32               mMemoryInfoCtr = 0;
MEMORY_INFO          mMemoryInfo[MAX_ACPI_MEM_ENTRIES];

BOOLEAN              mIsPxmDomainInfoInit = FALSE;
BOOLEAN              mRrNumaDomain = FALSE;
UINTN                mSockets;
UINTN                mHostBridges[BRH_MAX_SOCKETS];

ROOT_COMPLEX_ASSIGNMENT     mRootComplex[BRH_MAX_SOCKETS][BRH_MAX_HOST_BRIDGES_PER_SOCKET] = { { { 0 } } };
STATIC FABRIC_NUMA_SERVICES2_PROTOCOL mFabricNumaServices2Protocol = {
  0x3,
  FabricBrhGetDomainInfo,
  FabricBrhDomainXlat,
  FabricBrhGetPhysDomainInfo,
  FabricBrhGetPxmDomainInfo
};

STATIC UINTN (*CsDomainTable)[BRH_NUM_IOS_BLOCKS * BRH_MAX_SOCKETS] = NULL;

STATIC UINTN CsDomainTable_Bx[4][BRH_NUM_IOS_BLOCKS * BRH_MAX_SOCKETS] = {
// S0IOS0  S0IOS1  S0IOS2  S0IOS3  S0IOS4  S0IOS5  S0IOS6  S0IOS7  S1IOS0  S1IOS1  S1IOS2  S1IOS3  S1IOS4  S1IOS5  S1IOS6  S1IOS7
  {     0,      0,      0,      0,      0,      0,      0,      0,      0,      0,      0,      0,      0,      0,      0,      0}, //NPS0
  {     0,      0,      0,      0,      0,      0,      0,      0,      1,      1,      1,      1,      1,      1,      1,      1}, //NPS1
  {     0,      1,      0,      1,      0,      1,      0,      1,      2,      3,      2,      3,      2,      3,      2,      3}, //NPS2
  {     0,      3,      0,      3,      1,      2,      1,      2,      4,      7,      4,      7,      5,      6,      5,      6}, //NPS4
};

STATIC UINTN CsDomainTable_Ax[4][BRH_NUM_IOS_BLOCKS * BRH_MAX_SOCKETS] = {
// S0IOS0  S0IOS1  S0IOS2  S0IOS3  S0IOS4  S0IOS5  S0IOS6  S0IOS7  S1IOS0  S1IOS1  S1IOS2  S1IOS3  S1IOS4  S1IOS5  S1IOS6  S1IOS7
  {     0,      0,      0,      0,      0,      0,      0,      0,      0,      0,      0,      0,      0,      0,      0,      0}, //NPS0
  {     0,      0,      0,      0,      0,      0,      0,      0,      1,      1,      1,      1,      1,      1,      1,      1}, //NPS1
  {     0,      0,      0,      0,      1,      1,      1,      1,      2,      2,      2,      2,      3,      3,      3,      3}, //NPS2
  {     1,      1,      0,      0,      3,      3,      2,      2,      5,      5,      4,      4,      7,      7,      6,      6}, //NPS4
};

STATIC UINT32 CcdToQuadrant[] = {0, 3, 1, 2, 0, 3, 1, 2, 0, 3, 1, 2, 0, 3, 1, 2};

STATIC UINTN *CcxMaskDomainTable = NULL;

STATIC UINTN CcxMaskDomainTable4Domain[BRH_NUM_IOS_BLOCKS] = {
  0x1111, //IOS0 <--> CCD0 CCD4 CCD8  CCD12
  0x2222, //IOS1 <--> CCD1 CCD5 CCD9  CCD13
  0x1111, //IOS2 <--> CCD0 CCD4 CCD8  CCD12
  0x2222, //IOS3 <--> CCD1 CCD5 CCD9  CCD13
  0x4444, //IOS4 <--> CCD2 CCD6 CCD10 CCD14
  0x8888, //IOS5 <--> CCD3 CCD7 CCD11 CCD15
  0x4444, //IOS6 <--> CCD2 CCD6 CCD10 CCD14
  0x8888, //IOS7 <--> CCD3 CCD7 CCD11 CCD15
};

STATIC UINTN  CcxMaskDomainTable2Domain[BRH_NUM_IOS_BLOCKS] = {
  0x5555, // IOS0 <--> CCD0 CCD2 CCD4 CCD6 CCD8 CCD10 CCD12 CCD14
  0x5555, // IOS1 <--> CCD0 CCD2 CCD4 CCD6 CCD8 CCD10 CCD12 CCD14
  0x5555, // IOS2 <--> CCD0 CCD2 CCD4 CCD6 CCD8 CCD10 CCD12 CCD14
  0x5555, // IOS3 <--> CCD0 CCD2 CCD4 CCD6 CCD8 CCD10 CCD12 CCD14
  0xAAAA, // IOS4 <--> CCD1 CCD3 CCD5 CCD7 CCD9 CCD11 CCD13 CCD15
  0xAAAA, // IOS5 <--> CCD1 CCD3 CCD5 CCD7 CCD9 CCD11 CCD13 CCD15
  0xAAAA, // IOS6 <--> CCD1 CCD3 CCD5 CCD7 CCD9 CCD11 CCD13 CCD15
  0xAAAA, // IOS7 <--> CCD1 CCD3 CCD5 CCD7 CCD9 CCD11 CCD13 CCD15
};

STATIC UINTN  CcxMaskDomainTable1Domain[BRH_NUM_IOS_BLOCKS] = {
  0xFFFF, // IOS0 <--> CCD0 - CCD15
  0xFFFF, // IOS1 <--> CCD0 - CCD15
  0xFFFF, // IOS2 <--> CCD0 - CCD15
  0xFFFF, // IOS3 <--> CCD0 - CCD15
  0xFFFF, // IOS4 <--> CCD0 - CCD15
  0xFFFF, // IOS5 <--> CCD0 - CCD15
  0xFFFF, // IOS6 <--> CCD0 - CCD15
  0xFFFF, // IOS7 <--> CCD0 - CCD15
};

/*----------------------------------------------------------------------------------------
 *                          E X P O R T E D    F U N C T I O N S
 *----------------------------------------------------------------------------------------
 */

/*----------------------------------------------------------------------------------------*/
/**
 * @brief This function will install the NUMA services 2 protocol.
 *
 * @param[in] ImageHandle Image handle.
 * @param[in] SystemTable EFI system table.
 *
 * @return EFI_STATUS
 *
 * @retval EFI_SUCCESS - Services protocol installed.
 */
EFI_STATUS
EFIAPI
FabricBrhNumaServices2ProtocolInstall (
  IN EFI_HANDLE        ImageHandle,
  IN EFI_SYSTEM_TABLE  *SystemTable
  )
{
  EFI_STATUS Status;

  Status = BuildDomainInfo ();

  if (Status == EFI_SUCCESS) {
    Status = gBS->InstallProtocolInterface (&ImageHandle,
                                            &gAmdFabricNumaServices2ProtocolGuid,
                                            EFI_NATIVE_INTERFACE,
                                            &mFabricNumaServices2Protocol);
  }

  return Status;
}

/**
 * @brief This function returns information about the NUMA domains.
 *
 * @param[in]  This                    NUMA services.
 * @param[out] NumberOfDomainsInSystem Number of unique NUMA domains.
 * @param[out] DomainInfo              An array with information about each domain.
 * @param[out] CcxAsNumaDomain         TRUE - each core complex is its own domain, FALSE - physical mapping is employed.
 * @return EFI_STATUS
 *
 * @retval EFI_SUCCESS - Info returned is valid.
 */
EFI_STATUS
EFIAPI
FabricBrhGetDomainInfo (
  IN     FABRIC_NUMA_SERVICES2_PROTOCOL  *This,
     OUT UINT32                          *NumberOfDomainsInSystem,
     OUT DOMAIN_INFO2                    **DomainInfo,
     OUT BOOLEAN                         *CcxAsNumaDomain
  )
{
  ASSERT (mDomainInfoValid);

  if ((NumberOfDomainsInSystem == NULL) && (DomainInfo == NULL) && (CcxAsNumaDomain == NULL)) {
    return EFI_INVALID_PARAMETER;
  }

  if (NumberOfDomainsInSystem != NULL) {
    *NumberOfDomainsInSystem = mNumberOfReportedDomains;
  }

  if (DomainInfo != NULL) {
    *DomainInfo = &mReportedDomainInfo[0];
  }

  if (CcxAsNumaDomain != NULL) {
    *CcxAsNumaDomain = mCcxAsNuma;
  }

  return EFI_SUCCESS;
}

/**
 * @brief This function translates a core's physical location to the appropriate NUMA domain.
 *
 * @param[in]  This   NUMA services.
 * @param[in]  Socket Zero based socket that the core is attached to.
 * @param[in]  Die    DF die on socket that the core is attached to.
 * @param[in]  Ccd    Logical CCD the core is on.
 * @param[in]  Ccx    Logical core complex.
 * @param[out] Domain Domain the core belongs to.
 *
 * @retval EFI_STATUS
 *
 * @retval EFI_SUCCESS           - Domain is valid.
 * @retval EFI_INVALID_PARAMETER - No core at location.
 */
EFI_STATUS
EFIAPI
FabricBrhDomainXlat (
  IN     FABRIC_NUMA_SERVICES2_PROTOCOL  *This,
  IN     UINTN                           Socket,
  IN     UINTN                           Die,
  IN     UINTN                           Ccd,
  IN     UINTN                           Ccx,
     OUT UINT32                          *Domain
  )
{
  UINT32  i;

  ASSERT (mDomainInfoValid);

  if (Socket >= FabricTopologyGetNumberOfProcessorsPresent ()) {
    return EFI_INVALID_PARAMETER;
  }

  if (Die >= FabricTopologyGetNumberOfDiesOnSocket (Socket)) {
    return EFI_INVALID_PARAMETER;
  }

  if (Ccd >= mCcdCount[Socket]) {
    return EFI_INVALID_PARAMETER;
  }

  if (Ccx >= mCcxPerCcd[Socket]) {
    return EFI_INVALID_PARAMETER;
  }

  if (Domain == NULL) {
    return EFI_INVALID_PARAMETER;
  }

  for (i = 0; i < mNumberOfReportedDomains; i++) {
    if ((mReportedDomainCcxMap[i] & ((1 << (UINT32) Ccx) << (mLogToPhysCcd[Socket][Ccd] * MAX_CCX_PER_CCD) << ((UINT32) Socket * NORMALIZED_SOCKET_SHIFT))) != 0) {
      break;
    }
  }

  if (i >= mNumberOfReportedDomains) {
    ASSERT (FALSE);
    return EFI_INVALID_PARAMETER;
  }

  *Domain = i;
  return EFI_SUCCESS;
}

/**
 * @brief Returns the maximum number of domains in the system.
 *
 * @return UINT8
 */
UINT8
FabricGetMaxDomains (
  VOID
  )
{
  ASSERT (mDomainInfoValid);

  return ((PcdGet8 (PcdAmdNumberOfPhysicalSocket) * (mCcxAsNuma ? (UINT8) (mCcdCount[0] * mCcxPerCcd[0]) : (UINT8) mPhysNps)) + (UINT8) mSystemCxlCount);
}

/**
 * @brief This function returns information about the physical domains requested via NPS.
 *
 * @param[in]  This                        NUMA services.
 * @param[out] NumberOfPhysDomainsInSystem Number of valid domains in the system.
 * @param[out] PhysDomainInfo              An array with information about each physical domain.
 * @param[out] PhysNodesPerSocket          Actual NPS as determined by ABL (not including SLink).
 * @param[out] NumberOfSystemSLinkDomains  Number of domains describing SLink connected memory.
 *
 * @return EFI_STATUS
 *
 * @retval EFI_SUCCESS           - Requested info is valid.
 * @retval EFI_INVALID_PARAMETER - No valid pointers passed.
 *
 *---------------------------------------------------------------------------------------
 */
EFI_STATUS
EFIAPI
FabricBrhGetPhysDomainInfo (
  IN     FABRIC_NUMA_SERVICES2_PROTOCOL  *This,
     OUT UINT32                          *NumberOfPhysDomainsInSystem,
     OUT PHYS_DOMAIN_INFO                **PhysDomainInfo,
     OUT UINT32                          *PhysNodesPerSocket,
     OUT UINT32                          *NumberOfSystemSLinkDomains
  )
{
  ASSERT (mDomainInfoValid);

  if ((NumberOfPhysDomainsInSystem == NULL) && (PhysDomainInfo == NULL) && (PhysNodesPerSocket == NULL) && (NumberOfSystemSLinkDomains == NULL)) {
    return EFI_INVALID_PARAMETER;
  }

  if (NumberOfPhysDomainsInSystem != NULL) {
    *NumberOfPhysDomainsInSystem = mNumberOfPhysicalDomains;
  }

  if (PhysDomainInfo != NULL) {
    *PhysDomainInfo = &mPhysicalDomainInfo[0];
  }

  if (PhysNodesPerSocket != NULL) {
    *PhysNodesPerSocket = mPhysNps;
  }

  if (NumberOfSystemSLinkDomains != NULL) {
    *NumberOfSystemSLinkDomains = 0;
  }

  return EFI_SUCCESS;
}


/**
 * @brief This function returns Proximity Domain information about a PCIe root-port bridge.
 *
 * @details
 *    Given a root-bridge as specified by its Bus, Device, Function, this function identifies:
 *    - the NBIO to which the root-bridge belongs,
 *    - the Quadrant to which the NBIO belongs,
 *    - and finally, the NUMA node (or nodes) associated with the Quadrant.
 *
 *    There are two cases to consider:
 *    1) PcdAmdFabricCcxAsNumaDomain is disabled (FALSE):
 *    In this case the Quadrant is associated with a single NUMA node,
 *    and the NUMA node to which the Quadrant belongs is based on the NPS setting.
 *
 *    2) PcdAmdFabricCcxAsNumaDomain is enabled (TRUE):
 *    In this case the Quadrant may be associated with one or up to four NUMA nodes,
 *    depending on the number of CCXes in the Quadrant.
 *
 *    Based on PCD  (PcdAmdFabricRoundRobinNumaDomainForCcx), this function
 *    implements a round-robin scheme to return a single NUMA node from the list of nodes
 *    associated with a given Quadrant. Otherwise, this function returns the complete list of
 *    nodes associated with a given Quadrant, allowing for platform-defined implementations.
 *
 * @param[in]  This          NUMA services
 * @param[in]  RootPortBdf   BDF for root-port bridge in PCI_ADDR format.
 * @param[out] PxmDomainInfo Pointer to a structure returning associated NUMA node(s).
 *
 * @return EFI_STATUS
 *
 * @retval EFI_SUCCESS - The function completed successfully.
 * @retval Other       - The requested operation could not be completed.
 *
 *---------------------------------------------------------------------------------------
 */
EFI_STATUS
EFIAPI
FabricBrhGetPxmDomainInfo (
  IN     FABRIC_NUMA_SERVICES2_PROTOCOL  *This,
  IN     PCI_ADDR                        RootPortBdf,
     OUT PXM_DOMAIN_INFO                 *PxmDomainInfo
  )
{
  EFI_STATUS                       Status;
  APOB_SYSTEM_NPS_INFO_TYPE_STRUCT *NpsInfo;
  UINTN                            NodesPerSocket;
  UINT8                            NumOfNodesAssigned;
  UINT8                            ActualNps;
  UINT32                           i;
  UINT32                           j;
  UINT32                           k;
  AMD_NBIO_CXL_SERVICES_PROTOCOL   *CxlService;
  AMD_CXL_PORT_INFO_STRUCT         PortInfo;

  // init variables
  Status             = EFI_SUCCESS;
  NpsInfo            = NULL;
  NodesPerSocket     = 0;
  NumOfNodesAssigned = 0;
  ActualNps          = 0;

  // Need to intialize PXM array
  if (!mIsPxmDomainInfoInit) {
    // Get NPS setting
    Status = AmdPspGetApobEntryInstance(APOB_FABRIC, APOB_SYS_NPS_INFO_TYPE, 0, FALSE, (APOB_TYPE_HEADER **)&NpsInfo);
    if (EFI_ERROR(Status)) {
      return Status;
    }
    ActualNps = NpsInfo->ActualNps;

    // Get CCXAsNUMA setting
    mCcxAsNuma = PcdGetBool(PcdAmdFabricCcxAsNumaDomain);

    if (mCcxAsNuma) {
      // Get Round Robin setting
      mRrNumaDomain = PcdGetBool(PcdAmdFabricRoundRobinNumaDomainForCcx);
    }

    // Get number of sockets
    mSockets = FabricTopologyGetNumberOfProcessorsPresent ();

    // Initial tables
    CsDomainTable      = CsDomainTable_Bx;
    switch (GetMaxAllowableNpsForCcxAsNuma ()) {
    case 4:
      CcxMaskDomainTable = CcxMaskDomainTable4Domain;
      break;
    case 2:
      CcxMaskDomainTable = CcxMaskDomainTable2Domain;
      break;
    default:
      CcxMaskDomainTable = CcxMaskDomainTable1Domain;
    }

    if (IS_SOC_BRH_Ax && (FabricTopologyGetNumberOfProcessorsPresent() != 1)) {
      // sockets with 2 Ax CPU
      CsDomainTable      = CsDomainTable_Ax;
      for (i = 0; i < BRH_NUM_IOS_BLOCKS; i++) {
        if ((i % 2) == 0) {
          continue;
        }
        CcxMaskDomainTable[i] = 0;
      }
    }

    for (i = 0; i < mSockets; i++) {
      // init number of nodes assigned for this socket
      NumOfNodesAssigned = 0;

      if (mCcxAsNuma) {
        // NPS = CCX per socket
        NodesPerSocket = mCcdCount[i] * mCcxPerCcd[i];
      }

      // Get number of host (root-complex) bridges per socket
      mHostBridges[i] = FabricTopologyGetNumberOfRootBridgesOnSocket (i);

      // Set PXM info for each root-complex
      for (j = 0; j < mHostBridges[i]; j++) {
        mRootComplex[i][j].PhyRBNum = FabricTopologyGetPhysRootBridgeNumber (i, 0, j);
        mRootComplex[i][j].BusBase = FabricTopologyGetHostBridgeBusBase (i, 0, j);
        InitPxmDomainInfo (i, mRootComplex[i][j].PhyRBNum, NodesPerSocket, ActualNps, &NumOfNodesAssigned, &mRootComplex[i][j].PxmInfo);
      }
    }

    IDS_HDT_CONSOLE (TOPO_TRACE, "##> mSockets=0x%X\n\n", mSockets);
    for (i = 0; i < mSockets; i++) {
      IDS_HDT_CONSOLE (TOPO_TRACE, "##> socket %d\n", i);
      IDS_HDT_CONSOLE (TOPO_TRACE, "##>  mCcdCount=0x%X\n", mCcdCount[i]);
      IDS_HDT_CONSOLE (TOPO_TRACE, "##>  mCcxPerCcd=0x%X\n\n", mCcxPerCcd[i]);
      IDS_HDT_CONSOLE (TOPO_TRACE, "##>  mLogToPhysCcd[%d]= [", i);

      for (j = 0; j < BRH_MAX_CCD_PER_SOCKET; j++) {
        IDS_HDT_CONSOLE (TOPO_TRACE, "%d,", mLogToPhysCcd[i][j]);
      }
      IDS_HDT_CONSOLE (TOPO_TRACE, "]\n\n");

      IDS_HDT_CONSOLE (TOPO_TRACE, "##> mHostBridges[%d](# Rb on Socket)=0x%X\n\n", i, mHostBridges[i]);
      for (j = 0; j < mHostBridges[i]; j++) {
        IDS_HDT_CONSOLE (TOPO_TRACE, "##> mRootComplex[%d][%d]\n", i,j);
        IDS_HDT_CONSOLE (TOPO_TRACE, "##>   PhyRBNum = 0x%X\n", mRootComplex[i][j].PhyRBNum);
        IDS_HDT_CONSOLE (TOPO_TRACE, "##>   BusBase  = 0x%X\n", mRootComplex[i][j].BusBase);
        IDS_HDT_CONSOLE (TOPO_TRACE, "##>   RrIndex  = 0x%X\n", mRootComplex[i][j].RrIndex);
        IDS_HDT_CONSOLE (TOPO_TRACE, "##>   PxmInfo\n");
        IDS_HDT_CONSOLE (TOPO_TRACE, "##>     Count= 0x%X\n", mRootComplex[i][j].PxmInfo.Count);
        IDS_HDT_CONSOLE (TOPO_TRACE, "##>     Domain= [");
        for (k = 0; k < mRootComplex[i][j].PxmInfo.Count; k++) {
          IDS_HDT_CONSOLE (TOPO_TRACE, "0x%X,", mRootComplex[i][j].PxmInfo.Domain[k]);
        }
        IDS_HDT_CONSOLE (TOPO_TRACE, "]\n\n");
      }
    }
    mIsPxmDomainInfoInit = TRUE;
  }

  PxmDomainInfo->Count = 0;
  // Look for a matching root-bus in the PXM array
  for (i = 0; i < mSockets; i++) {
    for (j = 0; j < mHostBridges[i]; j++) {
      if (RootPortBdf.Address.Bus + RootPortBdf.Address.Segment * MAX_PCI_BUS_NUMBER_PER_SEGMENT == mRootComplex[i][j].BusBase) {
        if (mRrNumaDomain && mRootComplex[i][j].PxmInfo.Count > 1) {
          // return a single domain with round-robin scheme
          PxmDomainInfo->Count     = 1;
          PxmDomainInfo->Domain[0] = mRootComplex[i][j].PxmInfo.Domain[mRootComplex[i][j].RrIndex];
          mRootComplex[i][j].RrIndex += 1;
          if (mRootComplex[i][j].RrIndex == mRootComplex[i][j].PxmInfo.Count) {
            mRootComplex[i][j].RrIndex = 0;
          }
        } else {
          // return all pertinent domains (mutiple domains only for CCX as NUMA)
          PxmDomainInfo->Count = mRootComplex[i][j].PxmInfo.Count;
          ASSERT (PxmDomainInfo->Count < MAX_PXM_VALUES_PER_QUADRANT);
          for (k = 0; k < PxmDomainInfo->Count; k++) {
            PxmDomainInfo->Domain[k] = mRootComplex[i][j].PxmInfo.Domain[k];
          }
        }
        // found RB, so stop looking
        break;
      }
    }
  }

  if (PxmDomainInfo->Count == 0) {
    Status = gBS->LocateProtocol (&gAmdNbioCxlServicesProtocolGuid, NULL, (VOID **) &CxlService);
    if (Status == EFI_SUCCESS) {
      for (i = 0; i < CxlService->CxlCount; i++) {
        Status = CxlService->CxlGetRootPortInformation (CxlService, i, &PortInfo);
        if (EFI_ERROR (Status)) {
          continue;
        }
        if (PortInfo.EndPointBDF.AddressValue == RootPortBdf.AddressValue) {
          PxmDomainInfo->Count = 1;
          PxmDomainInfo->Domain[0] = mNumberOfPhysicalDomains - mSystemCxlCount + PortInfo.SocketID;
          break;
        }
      }
    }
  }

  ASSERT (PxmDomainInfo->Count > 0);
  if (PxmDomainInfo->Count == 0) {
    return EFI_NOT_FOUND;
  }

  IDS_HDT_CONSOLE (TOPO_TRACE, "##> FabricBrhGetPxmDomainInfo()\n");
  IDS_HDT_CONSOLE (TOPO_TRACE, "##> Find RootPortBdf.Address.Bus= 0x%X\n", RootPortBdf.Address.Bus);
  IDS_HDT_CONSOLE (TOPO_TRACE, "##> Domain(s)= [");
  for (k = 0; k < PxmDomainInfo->Count; k++) {
    IDS_HDT_CONSOLE (TOPO_TRACE, "0x%X,", PxmDomainInfo->Domain[k]);
  }
  IDS_HDT_CONSOLE (TOPO_TRACE, "]\n");

  return Status;
}

/**
 * @brief This function gathers data about the NUMA domains to be used by the protocol procedures.
 *
 * @return EFI_STATUS
 *
 * @retval EFI_SUCCESS      - Successful.
 * @retval EFI_DEVICE_ERROR - An error was encountered.  Data is not valid.
 */
EFI_STATUS
BuildDomainInfo (
  VOID
  )
{
  UINTN                            Ccd;
  UINTN                            Ccx;
  UINTN                            Domain;
  UINTN                            Half;
  UINTN                            Quad;
  UINTN                            Socket;
  UINTN                            NumberOfCpus;
  EFI_STATUS                       Status;
  APOB_SYSTEM_NPS_INFO_TYPE_STRUCT *NpsInfo;
  UINT32                           i;
  UINT32                           ReportedIndex;
  UINT32                           CxlBase;
  UINT32                           Nps0CcxMap   = 0xFFFFFFFF;
  UINT32                           Nps1CcxMap   = 0xFFFF;
  UINT32                           Nps2CcxMap[] = {0x5555, 0xAAAA};
  UINT32                           Nps4CcxMap[] = {0x1111, 0x4444, 0x8888, 0x2222};

  Status = AmdPspGetApobEntryInstance (APOB_FABRIC, APOB_SYS_NPS_INFO_TYPE, 0, FALSE, (APOB_TYPE_HEADER **) &NpsInfo);
  if (EFI_ERROR(Status)) {
    return EFI_DEVICE_ERROR;
  }

  NumberOfCpus = FabricTopologyGetNumberOfProcessorsPresent ();

  mCcxAsNuma = PcdGetBool (PcdAmdFabricCcxAsNumaDomain);

  // Fill module global CCD data
  BuildCcdInfo ((UINT32) NumberOfCpus);

  /*
  * NPS Settings for Breithorn A0
  *   values are for 1 socket. 2nd socket maps will be left shifted by 0x10
  *
  * NPS1:
  *   CCD: 0xFFFF
  *   CS:  0x0FFF
  * NPS2:
  *   CCD: 0x5555, 0xAAAA
  *   CS:  0x003F, 0x0FC0
  * NPS4:
  *   CCD: 0x1111, 0x4444, 0x8888, 0x2222
  *   CS:  0x0007, 0x0038, 0x01C0, 0x0E00
  */
  // Fill module global info about the 'physical' domains
  mNumberOfPhysicalDomains = 0;
  LibAmdMemFill ((VOID *) mReportedDomainCcxMap, 0x00, sizeof (mReportedDomainCcxMap), NULL);

  switch (NpsInfo->ActualNps) {
  case DF_DRAM_NPS0:
    ASSERT (NumberOfCpus == 2);
    mPhysicalDomainInfo[mNumberOfPhysicalDomains].NormalizedCsMap = ((1 << BRH_NUM_CS_UMC_BLOCKS) - 1) | (((1 << BRH_NUM_CS_UMC_BLOCKS) - 1) << NORMALIZED_SOCKET_SHIFT);
    mPhysNps = 0;
    if (mCcxAsNuma == FALSE) {
      mReportedDomainCcxMap[mNumberOfPhysicalDomains] = Nps0CcxMap;
    }
    mNumberOfPhysicalDomains++;
    break;
  case DF_DRAM_NPS1:
    for (Socket = 0; Socket < NumberOfCpus; Socket++) {
      mPhysicalDomainInfo[mNumberOfPhysicalDomains].NormalizedCsMap = (((1 << BRH_NUM_CS_UMC_BLOCKS) - 1) << ((UINT32) Socket * NORMALIZED_SOCKET_SHIFT));

      if (mCcxAsNuma == FALSE) {
        mReportedDomainCcxMap[mNumberOfPhysicalDomains] = Nps1CcxMap << ((UINT32) Socket * NORMALIZED_SOCKET_SHIFT);
      }
      mNumberOfPhysicalDomains++;
    }
    mPhysNps = 1;
    break;
  case DF_DRAM_NPS2:
    for (Socket = 0; Socket < NumberOfCpus; Socket++) {
      for (Half = 0; Half < 2; Half++) {
        mPhysicalDomainInfo[mNumberOfPhysicalDomains].NormalizedCsMap = \
              ((((1 << (BRH_NUM_CS_UMC_BLOCKS / 2)) - 1) << ((UINT32) Half * (BRH_NUM_CS_UMC_BLOCKS / 2))) << ((UINT32) Socket * NORMALIZED_SOCKET_SHIFT));
        if (mCcxAsNuma == FALSE) {
          mReportedDomainCcxMap[mNumberOfPhysicalDomains] = Nps2CcxMap[Half] << ((UINT32) Socket * NORMALIZED_SOCKET_SHIFT);
        }
        mNumberOfPhysicalDomains++;
      }
    }
    mPhysNps = 2;
    break;
  case DF_DRAM_NPS4:
    for (Socket = 0; Socket < NumberOfCpus; Socket++) {
      for (Quad = 0; Quad < 4; Quad++) {
        mPhysicalDomainInfo[mNumberOfPhysicalDomains].NormalizedCsMap = \
              ((((1 << (BRH_NUM_CS_UMC_BLOCKS / 4)) - 1) << ((UINT32) Quad * (BRH_NUM_CS_UMC_BLOCKS / 4))) << ((UINT32) Socket * NORMALIZED_SOCKET_SHIFT));
        if (mCcxAsNuma == FALSE) {
          mReportedDomainCcxMap[mNumberOfPhysicalDomains] = Nps4CcxMap[Quad] << ((UINT32) Socket * NORMALIZED_SOCKET_SHIFT);
        }
        mNumberOfPhysicalDomains++;
      }
    }
    mPhysNps = 4;
    break;
  default:
    ASSERT (FALSE);
    return EFI_DEVICE_ERROR;
    break;
  }

  // Build CXL info
  mSystemCxlCount = BuildCxlInfo (mNumberOfPhysicalDomains);
  mNumberOfPhysicalDomains += mSystemCxlCount;

  if (mCcxAsNuma) {
    mNumberOfReportedDomains = mSystemCxlCount;
    for (Socket = 0; Socket < NumberOfCpus; Socket++) {
      mNumberOfReportedDomains += (mCcdCount[Socket] * mCcxPerCcd[Socket]);
    }
  } else {
    mNumberOfReportedDomains = mNumberOfPhysicalDomains;
  }
  ASSERT (mNumberOfReportedDomains <= MAX_REPORTED_DOMAINS);

  // Build reported domain info
  if (mCcxAsNuma) {
    for (Domain = 0; Domain < MAX_PHYSICAL_DOMAINS; Domain++) {
      mPhysicalDomainInfo[Domain].SharingEntityCount = 0;
      mPhysicalDomainInfo[Domain].SharingEntityMap = 0;
    }
    // Start with non CXL
    ReportedIndex = 0;
    for (Socket = 0; Socket < NumberOfCpus; Socket++) {
      for (Ccd = 0; Ccd < mCcdCount[Socket]; Ccd++) {
        for (Ccx = 0; Ccx < mCcxPerCcd[Socket]; Ccx++) {
          mReportedDomainInfo[ReportedIndex].Type = NumaDram;
          mReportedDomainInfo[ReportedIndex].PhysicalDomain = GetPhysDomainOfCcd ((UINT32) Socket, mLogToPhysCcd[Socket][Ccd]);
          mReportedDomainInfo[ReportedIndex].SocketMap = GetSocketMapFromNormalizedCsMap (mPhysicalDomainInfo[mReportedDomainInfo[ReportedIndex].PhysicalDomain].NormalizedCsMap);
          mPhysicalDomainInfo[mReportedDomainInfo[ReportedIndex].PhysicalDomain].SharingEntityCount++;
          mPhysicalDomainInfo[mReportedDomainInfo[ReportedIndex].PhysicalDomain].SharingEntityMap |= (1 << ReportedIndex);
          mReportedDomainCcxMap[ReportedIndex] = (1 << (UINT32) Ccx) << (mLogToPhysCcd[Socket][Ccd] * MAX_CCX_PER_CCD) << ((UINT32) Socket * NORMALIZED_SOCKET_SHIFT);
          ReportedIndex++;
        }
      }
    }

    CxlBase = mNumberOfPhysicalDomains - mSystemCxlCount;
    for (i = 0; i < mSystemCxlCount; i++) {
      mReportedDomainInfo[ReportedIndex].Type = NumaCxl;
      mReportedDomainInfo[ReportedIndex].SocketMap = GetSocketMapFromNormalizedCsMap (mPhysicalDomainInfo[i + CxlBase].NormalizedCsMap);
      mReportedDomainInfo[ReportedIndex].PhysicalDomain = i + CxlBase;
      mPhysicalDomainInfo[mReportedDomainInfo[ReportedIndex].PhysicalDomain].SharingEntityCount = 1;
      mPhysicalDomainInfo[mReportedDomainInfo[ReportedIndex].PhysicalDomain].SharingEntityMap = (1 << ReportedIndex);
      ReportedIndex++;
    }
    ASSERT (ReportedIndex == mNumberOfReportedDomains);
  } else {
    for (Domain = 0; Domain < mNumberOfPhysicalDomains; Domain++) {
      mReportedDomainInfo[Domain].Type = ((mPhysicalDomainInfo[Domain].NormalizedCsMap & CS_CMP_SYSTEM_MAP) != 0) ? NumaCxl : NumaDram;
      mReportedDomainInfo[Domain].SocketMap = GetSocketMapFromNormalizedCsMap (mPhysicalDomainInfo[Domain].NormalizedCsMap);
      mReportedDomainInfo[Domain].PhysicalDomain = (UINT32) Domain;
      mPhysicalDomainInfo[Domain].SharingEntityCount = 1;
      mPhysicalDomainInfo[Domain].SharingEntityMap = (1 << (UINT32) Domain);
    }
  }

  mDomainInfoValid = TRUE;

  IDS_HDT_CONSOLE (MAIN_FLOW, "mNumberOfPhysicalDomains = %d\n", mNumberOfPhysicalDomains);
  for (Domain = 0; Domain < mNumberOfPhysicalDomains; Domain++) {
    IDS_HDT_CONSOLE (MAIN_FLOW, "PhysicalDomain[%d]\n", Domain);
    IDS_HDT_CONSOLE (MAIN_FLOW, "    mPhysicalDomainInfo\n");
    IDS_HDT_CONSOLE (MAIN_FLOW, "        NormalizedCsMap       = 0x%x\n", mPhysicalDomainInfo[Domain].NormalizedCsMap);
    IDS_HDT_CONSOLE (MAIN_FLOW, "        SharingEntityCount    = 0x%x\n", mPhysicalDomainInfo[Domain].SharingEntityCount);
    IDS_HDT_CONSOLE (MAIN_FLOW, "        SharingEntityMap      = 0x%x\n", mPhysicalDomainInfo[Domain].SharingEntityMap);
  }

  IDS_HDT_CONSOLE (MAIN_FLOW, "mNumberOfReportedDomains = %d\n", mNumberOfReportedDomains);
  for (Domain = 0; Domain < mNumberOfReportedDomains; Domain++) {
    IDS_HDT_CONSOLE (MAIN_FLOW, "Domain[%d]\n", Domain);
    IDS_HDT_CONSOLE (MAIN_FLOW, "    mReportedDomainInfo\n");
    IDS_HDT_CONSOLE (MAIN_FLOW, "        Type                  = 0x%x\n", mReportedDomainInfo[Domain].Type);
    IDS_HDT_CONSOLE (MAIN_FLOW, "        SocketMap             = 0x%x\n", mReportedDomainInfo[Domain].SocketMap);
    IDS_HDT_CONSOLE (MAIN_FLOW, "        PhysicalDomain        = 0x%x\n", mReportedDomainInfo[Domain].PhysicalDomain);
    IDS_HDT_CONSOLE (MAIN_FLOW, "    mReportedDomainCcxMap     = 0x%x\n", mReportedDomainCcxMap[Domain]);
  }
  return EFI_SUCCESS;
}

/**
 * @brief This function gathers data about CCDs to be used by the protocol procedures.
 *
 * @param[in] SocketCount Number of processors installed
 */
VOID
BuildCcdInfo (
  IN UINT32  SocketCount
  )
{
  UINTN                                                     i;
  UINT32                                                    j;
  UINT32                                                    CcdMap;
  CCDENABLE_REGISTER                                        CcdEnable;
  FABRIC_BLOCK_INSTANCE_INFORMATION3__CSNCSPIEALLM_REGISTER InstanceInfo3;

  for (i = 0; i < SocketCount; i++) {
    CcdMap = 0;
    mCcxPerCcd[i] = 1;
    for (j = 0; j < BRH_NUM_CCM_BLOCKS; j++) {
      InstanceInfo3.Value = FabricRegisterAccRead (i, 0, FABRICBLOCKINSTANCEINFORMATION3_CSNCSPIEALLM_FUNC, FABRICBLOCKINSTANCEINFORMATION3_CSNCSPIEALLM_REG, BRH_CCM0_INSTANCE_ID + j);
      if (InstanceInfo3.Field.BlockFabricID != 0x55) {
        CcdEnable.Value = FabricRegisterAccRead (i, 0, CCDENABLE_FUNC, CCDENABLE_REG, BRH_CCM0_INSTANCE_ID + j);
        CcdMap |= (CcdEnable.Field.CCDEn & 1) << j;
        if (CcdEnable.Field.SdpWideEn == 0) {
          // CCM is not in the wide mode, the second SDP port is for the second CCD
          CcdMap |= ((CcdEnable.Field.CCDEn >> 1) & 1) << BRH_NUM_CCM_BLOCKS << j;
        }
      }
    }

    mCcdCount[i] = 0;
    for (j = 0; CcdMap != 0; CcdMap &= ~(1 << j), j++) {
      if ((CcdMap & (1 << j)) != 0) {
        ASSERT (j < BRH_MAX_CCD_PER_SOCKET);
        mLogToPhysCcd[i][mCcdCount[i]++] = j;
      }
    }

    ASSERT (mCcdCount[i] <= BRH_MAX_CCD_PER_SOCKET);
  }
}

/**
 * @brief This function returns the physical domain that the given CCD belongs to.
 *
 * @param[in] Socket  Zero based socket number.
 * @param[in] PhysCcd Physical CCD number.
 *
 * @return UINT32 - Physical domain number.
 */
UINT32
GetPhysDomainOfCcd (
  IN UINT32  Socket,
  IN UINT32  PhysCcd
  )
{
  UINT32 i;

  if (PhysCcd >= sizeof (CcdToQuadrant) / sizeof (CcdToQuadrant[0])) {
    ASSERT (FALSE);
    return 0xFF;
  }

  for (i = 0; i < mNumberOfPhysicalDomains; i++) {
    if ((mPhysicalDomainInfo[i].NormalizedCsMap & ((0x7 << (CcdToQuadrant[PhysCcd] * 3)) << (Socket * NORMALIZED_SOCKET_SHIFT))) != 0) {
      break;
    }
  }
  ASSERT (i < mNumberOfPhysicalDomains);
  return i;
}

/**
 * @brief This function gathers data about CCMs to be used by the protocol procedures.
 *
 * @param[in] Index Array index into mPhysicalDomainInfo to place Cxl info.
 *
 * @return UINT32 - Number of Cxls populated with memory.
 */
UINT32
BuildCxlInfo (
  IN UINT32  Index
  )
{
  UINTN                            i;
  UINT32                           ActiveCxlCount;
  APOB_SYSTEM_CXL_INFO_TYPE_STRUCT *CxlMap;
  BOOLEAN                          CxlDeviceAttached[BRH_MAX_SOCKETS];

  ActiveCxlCount = 0;
  for (i = 0; i < BRH_MAX_SOCKETS; i++) {
    CxlDeviceAttached[i] = FALSE;
  }

  if (AmdPspGetApobEntryInstance (APOB_FABRIC, APOB_SYS_CXL_INFO_TYPE, 0, FALSE, (APOB_TYPE_HEADER **)&CxlMap) == EFI_SUCCESS) {
    for (i = 0; i < (sizeof (CxlMap->CxlInfo) / sizeof(CxlMap->CxlInfo[0])); i++) {
      if(CxlMap->CxlInfo[i].Size != 0 && CxlMap->CxlInfo[i].Status == CXL_ADDR_SUCCESS) {
        if (CxlMap->CxlInfo[i].Socket < BRH_MAX_SOCKETS) {
          CxlDeviceAttached[CxlMap->CxlInfo[i].Socket] = TRUE;
        }
      }
    }
  }

  for (i = 0; i < BRH_MAX_SOCKETS; i++) {
    if (CxlDeviceAttached[i]) {
      // Build the CS map
      mPhysicalDomainInfo[(Index + ActiveCxlCount)].NormalizedCsMap = 0xF000 << (i * NORMALIZED_SOCKET_SHIFT);
      ActiveCxlCount++;
    }
  }

  return ActiveCxlCount;
}

/**
 * @brief This function returns a bitmap of sockets belonging to the CS map.
 *
 * @param[in] NormalizedCsMap CS map to check.
 *
 * @return UINT32 - Socket bit map.
 */
UINT32
GetSocketMapFromNormalizedCsMap (
  IN UINT32  NormalizedCsMap
  )
{
  UINTN  i;
  UINT32 SocketMap;

  SocketMap = 0;
  for (i = 0; i < BRH_MAX_SOCKETS; i++) {
    SocketMap |= ((NormalizedCsMap & (SOCKET_CS_MAP << ((UINT32) i * NORMALIZED_SOCKET_SHIFT))) != 0) ? (1 << (UINT32) i) : 0;
  }

  return SocketMap;
}

/**
 * @brief This function creates the SRAT and CRAT memory structures.
 *
 * @param[in, out] TableEnd                 Current ACPI table pointer.
 * @param[in]      PublishMemEntry          Function to publish the data correctly for the desired table.
 * @param[out]     NumberOfEntriesPublished Number of records created.
 *
 * @return EFI_STATUS
 *
 * @retval EFI_SUCCESS - Table was updated successfully.
 */
EFI_STATUS
FabricCreateSystemAcpiDomainData (
  IN OUT UINT8                           **TableEnd,
  IN     PF_PUBLISH_ACPI_NUMA_MEM_ENTRY  PublishMemEntry,
     OUT UINT32                          *NumberOfEntriesPublished
  )
{
  UINTN      i;
  UINTN      PublishOrder[MAX_ACPI_MEM_ENTRIES];
  EFI_STATUS Status;

  Status = EFI_SUCCESS;
  if (mNumberOfAcpiMemEntries == 0) {
    Status = CollectSystemAcpiDomainData ();
  }

  // publish memory entries sorted by increasing domain
  PublishOrder[0] = 0;
  for (i = 1; i < mNumberOfAcpiMemEntries; i++) {
    UINTN j = i;
    PublishOrder[i] = i;

    while ((j > 0) && (mAcpiMemEntryInfo[j].Domain < mAcpiMemEntryInfo[j - 1].Domain)) {
      UINTN tmp = PublishOrder[j];
      PublishOrder[j] = PublishOrder[j - 1];
      PublishOrder[j - 1] = tmp;
      j--;
    }
  }

  for (i = 0; i < mNumberOfAcpiMemEntries; i++) {
    PublishMemEntry (TableEnd,
                     mAcpiMemEntryInfo[PublishOrder[i]].Domain,
                     mAcpiMemEntryInfo[PublishOrder[i]].BaseLo,
                     mAcpiMemEntryInfo[PublishOrder[i]].BaseHi,
                     mAcpiMemEntryInfo[PublishOrder[i]].SizeLo,
                     mAcpiMemEntryInfo[PublishOrder[i]].SizeHi);
  }

  if (NumberOfEntriesPublished != NULL) {
    *NumberOfEntriesPublished = mNumberOfAcpiMemEntries;
  }

  return Status;
}

/**
 * @brief This function gathers the data to publish to the ACPI tables.
 *
 * @param[out] AcpiMemEntryInfo ACPI memory information.
 *
 * @return UINT32 Number of ACPI memory entries stored in AcpiMemEntryInfo.
 */
EFI_STATUS
CollectSystemAcpiDomainData (
  VOID
  )
{
  UINT32                      ActualSize;
  UINT32                      DramMapIndex;
  UINT32                      SocketIndex;
  UINT32                      CsIndex;
  UINT32                      Entity;
  UINT32                      EntitySize;
  UINT32                      i;
  UINT32                      MemoryBase;
  UINT32                      NumberOfHoisted;
  UINT32                      PhysDomain;
  UINT32                      PreviousLimit;
  UINT32                      RegionBase;
  UINT32                      RegionSize;
  UINT32                      ReportedDomain;
  UINT32                      SizeRemaining;
  UINT32                      RegionSizeRemaining;
  VGAEN_REGISTER              VgaEn;
  DRAM_HOLE_CONTROL_REGISTER  DramHoleCtrl;
//DRAM_ADDRESS_INTLV_REGISTER DramAddressIntlv;
  DRAM_ADDRESS_CTL_REGISTER   DramAddressCtl;
  DRAM_BASE_ADDRESS_REGISTER  DramBaseAddr;
  DRAM_LIMIT_ADDRESS_REGISTER DramLimitAddr;
  DRAM_ADDRESS_CTL_REGISTER   CsDramAddressCtl;
  MEM_REGION                  MemRegion[BRH_NUMBER_OF_DRAM_REGIONS];
  HOIST_REGION                HoistRegion[MAX_HOISTED];
  BOOLEAN                     OneTBHoisted;

  // Initialize memory info structures
  mNumberOfAcpiMemEntries = 0;
  mMemoryInfoCtr = 0;
  NumberOfHoisted = 0;
  PreviousLimit = 0;
  LibAmdMemFill ((VOID *) mMemoryInfo, 0x00, sizeof (mMemoryInfo), NULL);
  LibAmdMemFill ((VOID *) MemRegion, 0x00, sizeof (MemRegion), NULL);

  // Check for the VGA hole
  VgaEn.Value = FabricRegisterAccRead (0, 0, VGAEN_FUNC, VGAEN_REG, FABRIC_REG_ACC_BC);
  if (VgaEn.Field.VgaEn_VE == 1) {
    HoistRegion[NumberOfHoisted].MemoryLost = TRUE;
    HoistRegion[NumberOfHoisted].Base       = 0xA;
    HoistRegion[NumberOfHoisted].Limit      = 0xC;

    NumberOfHoisted++;
  }

  // Check for the MMIO hole below 4GB
  DramHoleCtrl.Value = FabricRegisterAccRead (0, 0, DRAMHOLECONTROL_FUNC, DRAMHOLECONTROL_REG, FABRIC_REG_ACC_BC);
  if (DramHoleCtrl.Field.DramHoleValid == 1) {
    HoistRegion[NumberOfHoisted].MemoryLost = FALSE;
    HoistRegion[NumberOfHoisted].Base       = (DramHoleCtrl.Field.DramHoleBase << 8);
    HoistRegion[NumberOfHoisted].Limit      = 0x10000;

    NumberOfHoisted++;
  }

  OneTBHoisted = PcdGet8 (PcdAmdFabric1TbRemap) ? FALSE : TRUE;

  // Collect information about the memory ranges discovered by ABL
  for (DramMapIndex = 0; DramMapIndex < BRH_NUMBER_OF_DRAM_REGIONS; DramMapIndex++) {
    DramBaseAddr.Value = FabricRegisterAccRead (0, 0, DRAMBASEADDRESS_0_FUNC, (DRAMBASEADDRESS_0_REG + (DramMapIndex * DRAM_REGION_REGISTER_OFFSET)), BRH_IOMS2_INSTANCE_ID);
    //DramAddressIntlv.Value = FabricRegisterAccRead (0, 0, DRAMADDRESSINTLV_0_FUNC, (DRAMADDRESSINTLV_0_REG + (DramMapIndex * DRAM_REGION_REGISTER_OFFSET)), BRH_IOMS2_INSTANCE_ID);
    DramAddressCtl.Value = FabricRegisterAccRead (0, 0, DRAMADDRESSCTL_0_FUNC, (DRAMADDRESSCTL_0_REG + (DramMapIndex * DRAM_REGION_REGISTER_OFFSET)), BRH_IOMS2_INSTANCE_ID);
    if (DramAddressCtl.Field.AddrRngVal == 1) {
      // Valid region.  Construct normalized map which acts as an overall enable flag while processing
      DramLimitAddr.Value = FabricRegisterAccRead (0, 0, DRAMLIMITADDRESS_0_FUNC, (DRAMLIMITADDRESS_0_REG + (DramMapIndex * DRAM_REGION_REGISTER_OFFSET)), BRH_IOMS2_INSTANCE_ID);

      // Construct normalized map by traversal all the CS
      MemRegion[DramMapIndex].NormalizedMap = 0;
      for (SocketIndex = 0; SocketIndex < FabricTopologyGetNumberOfProcessorsPresent (); SocketIndex++) {
        for (CsIndex = 0; CsIndex < BRH_NUM_CS_BLOCKS; CsIndex++) {
          // Each CS have 4 Group DRAM MAP Registers
          for (i = 0; i < 4; i++) {
            CsDramAddressCtl.Value = FabricRegisterAccRead (SocketIndex, 0, DRAMADDRESSCTL_0_FUNC, DRAMADDRESSCTL_0_REG + (i * DRAM_REGION_REGISTER_OFFSET), BRH_CS0_INSTANCE_ID + CsIndex);
            if ((CsDramAddressCtl.Field.AddrRngVal  == 1) &&
                (CsDramAddressCtl.Field.DstFabricID == DramAddressCtl.Field.DstFabricID) &&
                (CsDramAddressCtl.Field.RemapEn     == DramAddressCtl.Field.RemapEn) &&
                (CsDramAddressCtl.Field.RemapSel    == DramAddressCtl.Field.RemapSel)) {
              MemRegion[DramMapIndex].NormalizedMap |= (1 << CsIndex << (NORMALIZED_SOCKET_SHIFT * SocketIndex));
              break;
            }
          }
        }
      }

      MemRegion[DramMapIndex].RawBase  = DramBaseAddr.Field.DramBaseAddr << 12;
      MemRegion[DramMapIndex].RawLimit = ((DramLimitAddr.Field.DramLimitAddr << 12) | 0xFFF) + 1;
      MemRegion[DramMapIndex].RawSize  = MemRegion[DramMapIndex].RawLimit - MemRegion[DramMapIndex].RawBase;

      if (DramAddressCtl.Field.LgcyMmioHoleEn == 1) {
        ASSERT (DramHoleCtrl.Field.DramHoleValid == 1);
        MemRegion[DramMapIndex].RawSize -= (0x10000 - (DramHoleCtrl.Field.DramHoleBase << 8));
      }
      //The 1TB hole is at 0xFD00000000 to 0x10000000000 since FSDL *******
      if ((MemRegion[DramMapIndex].RawBase == 0x1000000) && (PreviousLimit != 0x1000000) && !OneTBHoisted) {
        HoistRegion[NumberOfHoisted].MemoryLost = FALSE;
        HoistRegion[NumberOfHoisted].Base = PreviousLimit;
        HoistRegion[NumberOfHoisted++].Limit = 0x1000000;
        OneTBHoisted = TRUE;
        IDS_HDT_CONSOLE (MAIN_FLOW, "OneTBHoisted\n");
      }
      PreviousLimit = MemRegion[DramMapIndex].RawLimit;
    }
  }

  // Walk the physical domains to declare the memory entries
  for (DramMapIndex = 0; DramMapIndex < BRH_NUMBER_OF_DRAM_REGIONS; DramMapIndex++) {
    RegionSize = MemRegion[DramMapIndex].RawSize;
    RegionBase = MemRegion[DramMapIndex].RawBase;

    if (RegionSize != 0) {
      for (PhysDomain = 0; PhysDomain < mNumberOfPhysicalDomains; PhysDomain++) {
        if ((MemRegion[DramMapIndex].NormalizedMap & mPhysicalDomainInfo[PhysDomain].NormalizedCsMap) != 0) {
          break;
        }
      }
      ASSERT (PhysDomain < mNumberOfPhysicalDomains);

      // Spread the region across the shared domains
      ASSERT (mPhysicalDomainInfo[PhysDomain].SharingEntityCount != 0);
      EntitySize = RegionSize / mPhysicalDomainInfo[PhysDomain].SharingEntityCount;
      if ((EntitySize & SIZE_2MB_RSH16_MASK) != 0) {
        EntitySize &= ~SIZE_2MB_RSH16_MASK;
        EntitySize += SIZE_2MB_RSH16;
      }
      MemoryBase = RegionBase;
      RegionSizeRemaining = RegionSize;
      for (Entity = 0; Entity < mPhysicalDomainInfo[PhysDomain].SharingEntityCount; Entity++) {
        // Get reported domain number
        if (GetNthBitSet (mPhysicalDomainInfo[PhysDomain].SharingEntityMap, Entity, &ReportedDomain) != EFI_SUCCESS) {
          return EFI_ABORTED;
        }
        // Piece together this entity's range
        if (RegionSizeRemaining > EntitySize) {
          SizeRemaining = EntitySize;
        } else {
          SizeRemaining = RegionSizeRemaining;
          SizeRemaining &= ~SIZE_2MB_RSH16_MASK;
        }

        for (i = 0; i < NumberOfHoisted; i++) {
          if (MemoryBase == HoistRegion[i].Base) {
            MemoryBase = HoistRegion[i].Limit;
          }
          if ((MemoryBase < HoistRegion[i].Base) && ((MemoryBase + SizeRemaining) > HoistRegion[i].Base)) {
            StoreMemEntry (&mAcpiMemEntryInfo[mNumberOfAcpiMemEntries++], ReportedDomain, MemoryBase, HoistRegion[i].Base - MemoryBase);
            ActualSize = (HoistRegion[i].MemoryLost ? (HoistRegion[i].Limit - MemoryBase) : (HoistRegion[i].Base - MemoryBase));
            RegionSizeRemaining -= ActualSize;
            SizeRemaining -= ActualSize;
            MemoryBase = HoistRegion[i].Limit;
            mMemoryInfo[mMemoryInfoCtr].Domain = ReportedDomain;
            mMemoryInfo[mMemoryInfoCtr++].RegionSize = LShiftU64 (((UINT64) ActualSize), 16);
          }
        }
        StoreMemEntry (&mAcpiMemEntryInfo[mNumberOfAcpiMemEntries++], ReportedDomain, MemoryBase, SizeRemaining);
        mMemoryInfo[mMemoryInfoCtr].Domain = ReportedDomain;
        mMemoryInfo[mMemoryInfoCtr++].RegionSize = LShiftU64 (((UINT64) SizeRemaining), 16);
        MemoryBase += SizeRemaining;
        RegionSizeRemaining -= SizeRemaining;
      }
    }
  }

  ASSERT (mNumberOfAcpiMemEntries <= MAX_ACPI_MEM_ENTRIES);
  ASSERT (mMemoryInfoCtr <= MAX_ACPI_MEM_ENTRIES);
  return EFI_SUCCESS;
}

/**
 * @brief This function returns information about the domains declared via the SRAT.
 *
 * @param[out] NumberOfDomains Number of memory entries in the SRAT.
 * @param[out] MemoryInfo      Information about the SRAT memory entries.
 *
 * @return EFI_STATUS
 *
 * @retval EFI_SUCCESS           - Requested info valid.
 * @retval EFI_INVALID_PARAMETER - Requested info not returned (no valid pointers passed).
 */
EFI_STATUS
FabricGetMemoryInfo (
  OUT UINT32       *NumberOfDomains,
  OUT MEMORY_INFO  **MemoryInfo
  )
{
  if (mMemoryInfoCtr == 0) {
    return EFI_INVALID_PARAMETER;
  }

  if ((NumberOfDomains == NULL) && (MemoryInfo == NULL)) {
    return EFI_INVALID_PARAMETER;
  }

  if (NumberOfDomains != NULL) {
    *NumberOfDomains = mMemoryInfoCtr;
  }

  if (MemoryInfo != NULL) {
    *MemoryInfo = &mMemoryInfo[0];
  }

  return EFI_SUCCESS;
}

/**
 * @brief This function saves ACPI table entry information in the appropriate format.
 *
 * @param[out] Entry  Location to save the entry to.
 * @param[in]  Domain NUMA domain number.
 * @param[in]  Base   Bits 47:16 of the region's base address.
 * @param[in]  Size   Bits 47:16 of the region's size.
 */
VOID
StoreMemEntry (
     OUT ACPI_MEM_ENTRY_INFO  *Entry,
  IN     UINT32               Domain,
  IN     UINT32               Base,
  IN     UINT32               Size
  )
{
  Entry->Domain = Domain;
  Entry->BaseLo = (Base & 0x0000FFFF) << 16;
  Entry->BaseHi = (Base & 0xFFFF0000) >> 16;
  Entry->SizeLo = (Size & 0x0000FFFF) << 16;
  Entry->SizeHi = (Size & 0xFFFF0000) >> 16;
}

/**
 *  @brief This function returns the number of bits that are set in the given value
 *
 *  @param[in]      BitMap                     Value to check
 *
 *  @return         The number of bits that are set
 */
UINT32
GetNumberOfSetBits (
  IN UINT32  BitMap
  )
{
  INTN   LSbSet;
  UINT32 Count;

  for (Count = 0, LSbSet = LowBitSet32 (BitMap); LSbSet != -1; Count++, BitMap >>= (UINT32) (LSbSet + 1), LSbSet = LowBitSet32 (BitMap));

  return Count;
}

/**
 * @brief This function returns the bit position of the given set bit count.
 *
 * @param[in] BitMap      Value to check.
 * @param[in] N           0 - first bit set, 1 - second bit set, etc
 * @param[in] BitPosition Bit position within the BitMap.
 *
 * @return EFI_STATUS
 *
 * @retval EFI_SUCCESS           - BitPosition valid.
 * @retval EFI_INVALID_PARAMETER - Not enough bits are set in BitMap.
 */
EFI_STATUS
GetNthBitSet (
  IN     UINT32  BitMap,
  IN     UINT32  N,
     OUT UINT32  *BitPosition
  )
{
  INTN       LSbSet;
  UINT32     Found;
  EFI_STATUS Status;

  Status = EFI_INVALID_PARAMETER;
  Found = 0;
  for (LSbSet = LowBitSet32 (BitMap); LSbSet != -1; LSbSet = LowBitSet32 (BitMap)) {
    if (Found == N) {
      *BitPosition = (UINT32) LSbSet;
      Status = EFI_SUCCESS;
      break;
    } else {
      Found++;
      BitMap ^= (1 << (UINT32) LSbSet);
    }
  }
  return Status;
}

/**
 * @brief This function returns the proximity domain(s) for the given physical root bridge number.
 *
 * @param[in]     SocketId           socket ID
 * @param[in]     PhyRBNum           physical root bridge number to get domain for
 * @param[in]     NodesPerSocket     Number of nodes in the socket
 * @param[in]     ActualNps          Current NPS setting of system
 * @param[in out] NumOfNodesAssigned Number of node(s)/domain(s) assigned
 * @param[out]    PxmDomainInfo      Associated domain(s)
 */
VOID
InitPxmDomainInfo (
  IN     UINTN            SocketId,
  IN     UINTN            PhyRBNum,
  IN     UINTN            NodesPerSocket,
  IN     UINT8            ActualNps,
  IN OUT UINT8            *NumOfNodesAssigned,
     OUT PXM_DOMAIN_INFO  *PxmDomainInfo
  )
{
  UINTN              Ccd;
  UINTN              Ccx;
  UINTN              socket;
  UINTN              IosIndex;

  ASSERT (NodesPerSocket <= MAX_CCX_PER_CCD * MAX_CCD_PER_SOCKET);
  ASSERT (ActualNps <= MAX_NPS);

  PxmDomainInfo->Count = 0;
  socket = SocketId;
  IosIndex = PhyRBNum;
  if (IosIndex >= BRH_NUM_IOS_BLOCKS) {
    ASSERT (FALSE);
    return;
  }

  if (mCcxAsNuma) {
    for (Ccd = 0; Ccd < mCcdCount[socket]; Ccd++) {
      for (Ccx = 0; Ccx < mCcxPerCcd[socket]; Ccx++) {
        if ((((UINTN) 1) << Ccx << ((UINTN) mLogToPhysCcd[socket][Ccd] * (UINTN) MAX_CCX_PER_CCD)) & CcxMaskDomainTable[IosIndex]) {
          PxmDomainInfo->Domain[PxmDomainInfo->Count] = Ccd * mCcxPerCcd[socket] + Ccx + socket * NodesPerSocket;
          PxmDomainInfo->Count++;
          (*NumOfNodesAssigned)++;
          if (*NumOfNodesAssigned >= NodesPerSocket) {
            break;
          }
        }
      }
    }
  } else {
    PxmDomainInfo->Count = 1;
    PxmDomainInfo->Domain[0] = CsDomainTable[ActualNps][IosIndex + (socket * BRH_NUM_IOS_BLOCKS)];
  }
}

/**
 * @brief This function returns max allowable NPS for the CCX as NUMA mode
 */
UINT32
GetMaxAllowableNpsForCcxAsNuma (
  VOID
) {
  UINT32    CcdQuadrantMap[BRH_MAX_SOCKETS];
  BOOLEAN   Valid;
  UINT32    SocketLoop;
  UINT32    CcdLoop;

  for (SocketLoop = 0; SocketLoop < mSockets; SocketLoop++) {
    CcdQuadrantMap[SocketLoop] = 0;
    for (CcdLoop = 0; CcdLoop < mCcdCount[SocketLoop]; CcdLoop++) {
      CcdQuadrantMap[SocketLoop] |= (((UINT32)1) << CcdToQuadrant[mLogToPhysCcd[SocketLoop][CcdLoop]]);
    }
    ASSERT (CcdQuadrantMap[SocketLoop] != 0);
    ASSERT ((CcdQuadrantMap[SocketLoop] & 0xFFFFFFF0) == 0);
  }

  // Check if all the four quadrants have CCDs
  Valid = TRUE;
  for (SocketLoop = 0; SocketLoop < mSockets; SocketLoop++) {
    if ((CcdQuadrantMap[SocketLoop] & 0xF) != 0xF) {
      Valid = FALSE;
      break;
    }
  }
  if (Valid) {
    return 4;
  }

  // Check if left side (quadrant 0 and 1) have CCDs and right side (quadrant 2 and 3) have CCDs
  Valid = TRUE;
  for (SocketLoop = 0; SocketLoop < mSockets; SocketLoop++) {
    if (((CcdQuadrantMap[SocketLoop] & 0x3) == 0) || ((CcdQuadrantMap[SocketLoop] & 0xC) == 0)) {
      Valid = FALSE;
      break;
    }
  }
  if (Valid) {
    return 2;
  }

  return 1;
}

