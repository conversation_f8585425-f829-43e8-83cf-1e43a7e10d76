/*
 ******************************************************************************
 *
 * Copyright (C) 2008-2024 Advanced Micro Devices, Inc. All rights reserved.
 *
 *******************************************************************************
 */

/*----------------------------------------------------------------------------------------
 *                             M O D U L E S    U S E D
 *----------------------------------------------------------------------------------------
 */
#include <Uefi.h>
#include <AGESA.h>
#include <Library/AmdBaseLib.h>
#include <Library/AmdSocBaseLib.h>
#include <Library/AmdMpmRegBaseLib.h>
#include <Filecode.h>

#define FILECODE LIBRARY_AMDMPMREGBASELIB_AMDMPMREGBASELIB_FILECODE

/*----------------------------------------------------------------------------------------
 *                   D E F I N I T I O N S    A N D    M A C R O S
 *----------------------------------------------------------------------------------------
 */

#define INVALID_SMN_ADDRESS     0xFFFFFFFF

#pragma pack (push, 1)

typedef struct _AMD_MPM_REG_INFO_STRUCT {
  UINT32                      SocFamilyID;
  UINT32                      MpmC2pMsgRegBaseOffset;
  UINT32                      MpmP2cMsgRegBaseOffset;
} AMD_MPM_REG_INFO_STRUCT;

#pragma pack (pop)

/*----------------------------------------------------------------------------------------
 *                  T Y P E D E F S     A N D     S T R U C T U R E S
 *----------------------------------------------------------------------------------------
 */
AMD_MPM_REG_INFO_STRUCT gAmdMpmRegInfoTbl [] = {
  {F1A_STX1_RAW_ID,  MPM_C2PMSG_BASE_OFFSET_0X10900, MPM_P2CMSG_BASE_OFFSET_0X10500},
  {F1A_STXH_RAW_ID,  MPM_C2PMSG_BASE_OFFSET_0X10900, MPM_P2CMSG_BASE_OFFSET_0X10500},
  {F1A_KRK1_RAW_ID,  MPM_C2PMSG_BASE_OFFSET_0X10900, MPM_P2CMSG_BASE_OFFSET_0X10500},
};

/*----------------------------------------------------------------------------------------
 *           P R O T O T Y P E S     O F     L O C A L     F U N C T I O N S
 *----------------------------------------------------------------------------------------
 */

/**
 * @brief Get Mpm Register Info Entry
 *
 * @param [in,out]  AMD_MPM_REG_INFO_STRUCT **Entry
 *
 * @retval BOOLEAN  TRUE:  Found
 *                  FALSE: Not found
 */
BOOLEAN
GetAmdMpmRegInfoEntry (
  IN OUT AMD_MPM_REG_INFO_STRUCT **Entry
  )
{
  UINT32  Index;
  UINT32  Size;
  BOOLEAN IsFound;

  *Entry  = NULL;
  IsFound = FALSE;

  Size = sizeof (gAmdMpmRegInfoTbl)/sizeof (AMD_MPM_REG_INFO_STRUCT);
  for (Index = 0; Index < Size; Index++) {
    if (SocFamilyIdentificationCheck (gAmdMpmRegInfoTbl [Index].SocFamilyID)) {
      IsFound = TRUE;
      *Entry  = &gAmdMpmRegInfoTbl [Index];
      break;
    }
  }

  return IsFound;
}


/**
 * @brief Get Mpm C2P MSG Register Base Offset
 *
 * @param   VOID
 *
 * @retval  UINT32  BaseOffset
 */
UINT32
EFIAPI
GetMpmC2pMsgRegBaseOffset (
  VOID
  )
{
  AMD_MPM_REG_INFO_STRUCT *Entry;
  UINT32                   BaseOffset;

  Entry      = NULL;
  BaseOffset = MPM_C2PMSG_BASE_OFFSET_0X10500;

  if (GetAmdMpmRegInfoEntry (&Entry) == TRUE) {
    BaseOffset = Entry->MpmC2pMsgRegBaseOffset;
  }

  return BaseOffset;
}


/**
 * @brief Get Mpm C2P MSG Register Offset
 *
 * @param [in] UINT32 MpmC2pMsgNumber (Range 0 ~ 127)
 *
 * @retval     UINT32 RegOffset
 */
UINT32
EFIAPI
GetMpmC2pMsgRegOffset (
  UINT32 MpmC2pMsgNumber
  )
{
  UINT32 BaseOffset;
  UINT32 AlignOffset;
  UINT32 RegOffset;

  RegOffset = 0xFFFFFFFF;

  if (MpmC2pMsgNumber > MPM_C2PMSG_127) {
    return RegOffset;
  }

  //
  // RMB/PHX/MDN:
  //   MPM_C2PMSG_0  (Offset: 0x1_0500) ~ MPM_C2PMSG_31  (Offset: 0x1_057C)
  // BRH/STX/STXH
  //   MPM_C2PMSG_0  (Offset: 0x1_0900) ~ MPM_C2PMSG_31  (Offset: 0x1_097C)
  //
  // RMB/PHX/MDN/STX/STXH
  //   MPM_C2PMSG_32 (Offset: 0x1_0980) ~ MPM_C2PMSG_127 (Offset: 0x1_0AFC)
  //
  BaseOffset = GetMpmC2pMsgRegBaseOffset ();

  AlignOffset = 0;
  if ((MpmC2pMsgNumber >= MPM_C2PMSG_32) &&
      (BaseOffset == MPM_C2PMSG_BASE_OFFSET_0X10500)) {
    AlignOffset = MPM_ALIGN_OFFSET;
  }

  RegOffset = BaseOffset + AlignOffset + (MpmC2pMsgNumber * 4);

  return RegOffset;
}


/**
 * @brief Get Mpm P2C MSG Register Base Offset
 *
 * @param   VOID
 *
 * @retval  UINT32  BaseOffset
**/
UINT32
EFIAPI
GetMpmP2cMsgRegBaseOffset (
  VOID
  )
{
  AMD_MPM_REG_INFO_STRUCT *Entry;
  UINT32                  BaseOffset;

  Entry      = NULL;
  BaseOffset = MPM_P2CMSG_BASE_OFFSET_0X10680;

  if (GetAmdMpmRegInfoEntry (&Entry) == TRUE) {
    BaseOffset = Entry->MpmP2cMsgRegBaseOffset;
  }

  return BaseOffset;
}


/**
 * @brief Get Mpm P2C MSG Register Offset
 *
 * @param [in] UINT32 MpmP2cMsgNumber (Range 0 ~ 5)
 *
 * @retval     UINT32 RegOffset
 */
UINT32
EFIAPI
GetMpmP2cMsgRegOffset (
  UINT32 MpmP2cMsgNumber
  )
{
  UINT32 BaseOffset;
  UINT32 RegOffset;

  RegOffset = 0xFFFFFFFF;

  if (MpmP2cMsgNumber > MPM_P2CMSG_INTSTS) {
    return RegOffset;
  }

  BaseOffset = GetMpmP2cMsgRegBaseOffset ();

  RegOffset = BaseOffset + (MpmP2cMsgNumber * 4);

  return RegOffset;
}

