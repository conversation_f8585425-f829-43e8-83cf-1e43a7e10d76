#;*****************************************************************************
#;
#; Copyright (C) 2008-2024 Advanced Micro Devices, Inc. All rights reserved.
#;
#;******************************************************************************

[defines]
  INF_VERSION                    = 0x00010005
  BASE_NAME                      = FchKunlunSmmInit
  FILE_GUID                      = b8ab793e-27a7-4107-907f-3ff4320d7f15
  MODULE_TYPE                    = DXE_SMM_DRIVER
  PI_SPECIFICATION_VERSION       = 0x0001000A
  VERSION_STRING                 = 1.1
  ENTRY_POINT                    = FchSmmEntryPoint

[sources]
  FchSmm.c
  FchSmm.h
  GpiSmi.c
  IoTrapSmi.c
  MiscSmi.c
  PeriodicTimerSmi.c
  PowerButtonSmi.c
  SwSmi.c
  SxSmi.c

[sources.ia32]

[sources.x64]

[LibraryClasses]
  FchKunlunDxeLib
  FchSmmLibV9

  UefiDriverEntryPoint
  BaseMemoryLib
  BaseLib
  DebugLib
  SmmServicesTableLib
  UefiRuntimeServicesTableLib
  UefiBootServicesTableLib
  DevicePathLib
  MemoryAllocationLib
  NbioHandleLib
  NbioSmuBrhLib

  HobLib
  UefiLib

  PcdLib
  FchDxeLibV9
  AmdPspMboxLibV2

[Pcd]
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdNvdimmGpioForceSave
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdFchOemBeforePciRestoreSwSmi|0xD3|UINT8|0x0002F010
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdFchOemAfterPciRestoreSwSmi|0xD4|UINT8|0x0002F011
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdFchOemEnableAcpiSwSmi|0xA0|UINT8|0x0002F012
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdFchOemDisableAcpiSwSmi|0xA1|UINT8|0x0002F013
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdFchOemSpiUnlockSwSmi|0xAA|UINT8|0x0002F014
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdFchOemSpiLockSwSmi|0xAB|UINT8|0x0002F015
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdFchOemStartTimerSmi|0xBC|UINT8|0x0002F016
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdFchOemStopTimerSmi|0xBD|UINT8|0x0002F017
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdFchOemSecureSwSmi
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdFchOemSecureEnable
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdFchOemGpioFencing
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdFchOemCaptureSPDBusSmi
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdFchOemReleaseSPDBusSmi
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdXhci0Enable
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdXhci1Enable
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdXhciUsb2PortDisable
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdXhciUsb3PortDisable
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdAmdMemMaxDimmPerChannelV2
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdFchIxcTelemetryPortsFenceControl

[Guids]

[Protocols]
  gFchInitProtocolGuid                   #CONSUMED
  gFchSmmGpiDispatch2ProtocolGuid        #CONSUMED
  gFchSmmMiscDispatchProtocolGuid        #CONSUMED
  gFchSmmPeriodicalDispatch2ProtocolGuid #CONSUMED
  gFchSmmSxDispatch2ProtocolGuid         #CONSUMED
  gFchSmmPwrBtnDispatch2ProtocolGuid     #CONSUMED
  gFchSmmIoTrapDispatch2ProtocolGuid     #CONSUMED
  gFchSmmSwDispatch2ProtocolGuid         #CONSUMED
  gEfiSmmBase2ProtocolGuid               #CONSUMED
  gFchInitProtocolGuid                   #CONSUMED
  gFchSmmUsbDispatch2ProtocolGuid        #CONSUMED
  gFchSmmInitProtocolGuid                #PRODUCED
  gPspMboxSmmBufferAddressProtocolGuid   #CONSUMED
  gEdkiiSmmReadyToBootProtocolGuid       #CONSUMED

[Packages]
  MdePkg/MdePkg.dec
  MdeModulePkg/MdeModulePkg.dec
  AgesaPkg/AgesaPkg.dec
  AgesaModulePkg/AgesaModuleFchPkg.dec
  AgesaModulePkg/AgesaModulePspPkg.dec
  AgesaModulePkg/Fch/Kunlun/FchKunlun.dec
  AgesaModulePkg/AgesaCommonModulePkg.dec

[Depex]
  gEfiSmmBase2ProtocolGuid
  AND
  gFchSmmSxDispatch2ProtocolGuid
  AND
  gFchSmmSwDispatch2ProtocolGuid
  AND
  gFchSmmPwrBtnDispatch2ProtocolGuid
  AND
  gFchSmmIoTrapDispatch2ProtocolGuid
  AND
  gFchSmmPeriodicalDispatch2ProtocolGuid
  AND
  gFchSmmGpiDispatch2ProtocolGuid
  AND
  gFchSmmMiscDispatchProtocolGuid
  AND
  gFchInitProtocolGuid
  AND
  gAmdFchKunlunDepexProtocolGuid




