/*****************************************************************************
 * Copyright (C) 2008-2024 Advanced Micro Devices, Inc. All rights reserved.
 *
******************************************************************************/

/**
 * @file
 *
 * AMD Fabric PIE RAS initialization.
 *
 * This function initializes the RAS features of PIE.
 *
 * @xrefitem bom "File Content Label" "Release Content"
 * @e project:      AGESA
 * @e sub-project:  Fabric
 * @e \$Revision$   @e \$Date$
 *
 */

/*----------------------------------------------------------------------------------------
 *                             M O D U L E S    U S E D
 *----------------------------------------------------------------------------------------
 */
#include "AMD.h"
#include <Library/IdsLib.h>
#include <Library/BaseFabricTopologyLib.h>
#include <FabricRegistersBrh.h>
#include <Library/FabricRegisterAccLib.h>
#include <Library/AmdIdsHookLib.h>
#include <Ppi/AmdFabricSocSpecificServicesPpi.h>
#include "Filecode.h"
#include "FabricPieRasInit.h"

/*----------------------------------------------------------------------------------------
 *                   D E F I N I T I O N S    A N D    M A C R O S
 *----------------------------------------------------------------------------------------
 */

#define FILECODE FABRIC_BRH_FABRICBRHPEI_FABRICPIERASINIT_FILECODE

/*----------------------------------------------------------------------------------------
 *           P R O T O T Y P E S     O F     L O C A L     F U N C T I O N S
 *----------------------------------------------------------------------------------------
 */

/*----------------------------------------------------------------------------------------
 *                           G L O B A L   V A R I A B L E S
 *----------------------------------------------------------------------------------------
 */

/*----------------------------------------------------------------------------------------
 *                  T Y P E D E F S     A N D     S T R U C T U R E S
 *----------------------------------------------------------------------------------------
 */

/*----------------------------------------------------------------------------------------
 *                          E X P O R T E D    F U N C T I O N S
 *----------------------------------------------------------------------------------------
 */

/*----------------------------------------------------------------------------------------*/
/**
 * @brief This function initializes the RAS features of PIE.
 *
 * @param[in] PeiServices            Pointer to the PEI services object.
 * @param[in] SocLogicalIdPpi        Pointer to SOC logical PPI.
 * @param[in] SocSpecificServicesPpi Pointer to SOC specific services.
 */
VOID
FabricPieRasInit (
  IN     CONST EFI_PEI_SERVICES                   **PeiServices,
  IN     AMD_PEI_SOC_LOGICAL_ID_PPI                *SocLogicalIdPpi,
  IN     PEI_AMD_FABRIC_SOC_SPECIFIC_SERVICES_PPI  *SocSpecificServicesPpi
  )
{
  HARDWARE_ASSERT_STATUS_HIGH_REGISTER HwaStsHi;
  HARDWARE_ASSERT_STATUS_LOW_REGISTER  HwaStsLow;
  HARDWARE_ASSERT_MASK_HIGH_REGISTER   HwaMaskHi;
  HARDWARE_ASSERT_MASK_LOW_REGISTER    HwaMaskLow;
  DF_GLOBAL_CTRL_REGISTER              DfGlblCtrl;
  FABRIC_BLOCK_INSTANCE_COUNT_REGISTER FabricBlkInstCount;

  UINT32 WdtCfgRequest    = (UINT32) PcdGet8 (PcdAmdFabricWdtCfg);
  UINT32 WdtCntSelRequest = (UINT32) PcdGet8 (PcdAmdFabricWdtCntSel);

  if (WdtCntSelRequest == 0xFF) {
    // If PcdAmdFabricWdtCntSel is 'Auto', set WDTCntSel to 6 (5 seconds) for all IPs including PIE
    WdtCntSelRequest = 6;
  }

  for (UINTN i = 0; i < FabricTopologyGetNumberOfProcessorsPresent (); i++) {
    for (UINTN j = 0; j < FabricTopologyGetNumberOfDiesOnSocket (i); j++) {
      FabricBlkInstCount.Value = FabricRegisterAccRead (i,
                                                        j,
                                                        FABRICBLOCKINSTANCECOUNT_FUNC,
                                                        FABRICBLOCKINSTANCECOUNT_REG,
                                                        FABRIC_REG_ACC_BC);
      for (UINTN InstancesAccountedFor = 0;
           InstancesAccountedFor < FabricBlkInstCount.Field.BlkInstCount;
           InstancesAccountedFor++) {
        HwaStsLow.Value = FabricRegisterAccRead (i,
                                                 j,
                                                 HARDWAREASSERTSTATUSLOW_FUNC,
                                                 HARDWAREASSERTSTATUSLOW_REG,
                                                 InstancesAccountedFor);

        if (HwaStsLow.Value != 0) {
          HwaMaskLow.Value = FabricRegisterAccRead (i,
                                                    j,
                                                    HARDWAREASSERTMASKLOW_FUNC,
                                                    HARDWAREASSERTMASKLOW_REG,
                                                    InstancesAccountedFor);

          HwaStsLow.Value &= ~HwaMaskLow.Value;

          if (HwaStsLow.Value != 0) {
            IDS_HDT_CONSOLE (TOPO_TRACE,
                             "  DF Hardware Assert Low: Socket %d, Die %d, InstanceID %d, Value %x\n",
                             i,
                             j,
                             InstancesAccountedFor,
                             HwaStsLow.Value);

            HwaStsLow.Value = 0;
          }

          FabricRegisterAccWrite (i,
                                  j,
                                  HARDWAREASSERTSTATUSLOW_FUNC,
                                  HARDWAREASSERTSTATUSLOW_REG,
                                  InstancesAccountedFor,
                                  HwaStsLow.Value,
                                  FALSE);
        }

        HwaStsHi.Value = FabricRegisterAccRead (i,
                                                j,
                                                HARDWAREASSERTSTATUSHIGH_FUNC,
                                                HARDWAREASSERTSTATUSHIGH_REG,
                                                InstancesAccountedFor);

        if (HwaStsHi.Value != 0) {
          HwaMaskHi.Value = FabricRegisterAccRead (i,
                                                   j,
                                                   HARDWAREASSERTMASKHIGH_FUNC,
                                                   HARDWAREASSERTMASKHIGH_REG,
                                                   InstancesAccountedFor);

          HwaStsHi.Value &= ~HwaMaskHi.Value;

          if (HwaStsHi.Value != 0) {
            IDS_HDT_CONSOLE (TOPO_TRACE,
                             "  DF Hardware Assert High: Socket %d, Die %d, InstanceID %d, Value %x\n",
                             i,
                             j,
                             InstancesAccountedFor,
                             HwaStsHi.Value);

            HwaStsHi.Value = 0;
          }
          FabricRegisterAccWrite (i,
                                  j,
                                  HARDWAREASSERTSTATUSHIGH_FUNC,
                                  HARDWAREASSERTSTATUSHIGH_REG,
                                  InstancesAccountedFor,
                                  HwaStsHi.Value,
                                  FALSE);
        }
      }

      for (UINTN k = 0; SocSpecificServicesPpi->DfGlblCtrlInstanceIds[k] != 0xFFFFFFFF; k++) {
        DfGlblCtrl.Value = FabricRegisterAccRead (i,
                                                  j,
                                                  DFGLOBALCTRL_FUNC,
                                                  DFGLOBALCTRL_REG,
                                                  SocSpecificServicesPpi->DfGlblCtrlInstanceIds[k]);

        if (WdtCfgRequest <= 3) {
          DfGlblCtrl.Field.WDTBaseSel = WdtCfgRequest;
        }

        if (WdtCntSelRequest <= 7) {
          DfGlblCtrl.Field.PIEWDTCntSel = WdtCntSelRequest;
          DfGlblCtrl.Field.IOMWDTCntSel = WdtCntSelRequest;
          DfGlblCtrl.Field.CCMWDTCntSel = WdtCntSelRequest;
        }

        DfGlblCtrl.Field.DisImmSyncFloodOnFatalErr = PcdGetBool (PcdAmdFabricImmSyncFloodOnFatalErrCtrl) ? 0 : 1;

        FabricRegisterAccWrite (i,
                                j,
                                DFGLOBALCTRL_FUNC,
                                DFGLOBALCTRL_REG,
                                SocSpecificServicesPpi->DfGlblCtrlInstanceIds[k],
                                DfGlblCtrl.Value,
                                FALSE);
      }
    }
  }
}

