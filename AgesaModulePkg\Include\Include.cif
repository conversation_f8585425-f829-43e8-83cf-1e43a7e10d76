<component>
    name = "Include"
    category = ModulePart
    LocalRoot = "AgesaModulePkg\Include\"
    RefName = "AgesaModulePkg.Include"
[files]
"ActOptionsBrh.h"
"Agesa-Porting.h"
"ActOptions.h"
"AGESA.h"
"AgesaAcpiPostCode.h"
"AgesaPostCode.h"
"AMD.h"
"AmdApcbSmmCommunication.h"
"AmdPspSmmCommunication.h"
"AmdS3SaveLibData.h"
"AmdSmBios.h"
"AmdSmmCommunication.h"
"ApcbCommon.h"
"CcxCommon.h"
"CcxRegistersVh.h"
"CcxRegistersZen5.h"
"Compiler-Specs.h"
"CppcCommon.h"
"cpstack.inc"
"cpstackhooks.inc"
"cpstackhooksNasm.inc"
"cpstackNasm.inc"
"cpuRegisters.h"
"CpuRegistersDef.h"
"CxlComponentRegs.h"
"DwI2cRegs.h"
"DwI3cRegs.h"
"earlycpusupport.inc"
"earlycpusupportNasm.inc"
"FabricInfoBrh.h"
"FabricInfoRs.h"
"FabricRegistersBrh.h"
"FabricRegistersDf3.h"
"FabricRegistersDf4.h"
"FabricRegistersRs.h"
"FchBiosRamUsage.h"
"FchDebug.h"
"FchRegistersCommon.h"
"FchRegistersKL.h"
"Filecode.h"
"Gnb.h"
"GnbDxio.h"
"GnbIommu.h"
"GnbPcieAer.h"
"GnbRegistersBRH.h"
"GnbRegistersCommonV2.h"
"Ids.h"
"IdsHookId.h"
"IdsNvDefBRH.h"
"IdsNvIdBRH.h"
"IdsNvTable.h"
"MemDmi.h"
"mm.h"
"mn.h"
"MpioLib.h"
"mtspd5.h"
"NasmBase.inc"
"NbioRegisterTypes.h"
"OptionGnb.h"
"PlatformMemoryConfiguration.h"
"Porting.h"
"RefineAgesaPc.pl"
"SMU_BRH_if.h"
"SMU_BRH_MsgDef.h"
"smu_cppc_if.h"
"SocLogicalId.h"
[parts]
"AgesaModulePkg.Include.GnbRegistersBRH"
"AgesaModulePkg.Include.Guid"
"AgesaModulePkg.Include.Library"
"AgesaModulePkg.Include.Ppi"
"AgesaModulePkg.Include.Protocol"
<endComponent>
