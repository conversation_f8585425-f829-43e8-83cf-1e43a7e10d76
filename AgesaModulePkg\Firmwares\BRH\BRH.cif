<component>
    name = "BRH"
    category = ModulePart
    LocalRoot = "AgesaModulePkg\Firmwares\BRH\"
    RefName = "AgesaModulePkg.Firmwares.BRH"
[files]
"AblPostCode.h"
"APOB_NV_BRH.bin"
"BiosL1L2RtmSig.bin"
"BiosL1RtmSig.bin"
"bl_errorcodes.h"
"bl_syscall.h"
"PspSevEmptyData.bin"
"RtmPubSignedBrh.key"
"SecureEmptyToken.bin"
"Type0x64_AppbDdr5RdimmImem3_BRH.ecsbin"
"Type0x64_AppbDdr5RdimmImem3_BRH_C0.ecsbin"
"Type0x64_AppbDdr5RdimmImem4_BRH.ecsbin"
"Type0x64_AppbDdr5RdimmImem4_BRH_C0.ecsbin"
"Type0x64_AppbDdr5RdimmPosttrainImem10_BRH.ecsbin"
"Type0x64_AppbDdr5RdimmPosttrainImem10_BRH_C0.ecsbin"
"Type0x64_AppbDdr5RdimmPosttrainImem9_BRH.ecsbin"
"Type0x64_AppbDdr5RdimmPosttrainImem9_BRH_C0.ecsbin"
"Type0x64_AppbDdr5RdimmQuickbootImem11_BRH_C0.ecsbin"
"Type0x65_AppbDdr5RdimmDmem3_BRH.ecsbin"
"Type0x65_AppbDdr5RdimmDmem3_BRH_C0.ecsbin"
"Type0x65_AppbDdr5RdimmDmem4_BRH.ecsbin"
"Type0x65_AppbDdr5RdimmDmem4_BRH_C0.ecsbin"
"Type0x65_AppbDdr5RdimmPosttrainDmem10_BRH.ecsbin"
"Type0x65_AppbDdr5RdimmPosttrainDmem10_BRH_C0.ecsbin"
"Type0x65_AppbDdr5RdimmPosttrainDmem9_BRH.ecsbin"
"Type0x65_AppbDdr5RdimmPosttrainDmem9_BRH_C0.ecsbin"
"Type0x65_AppbDdr5RdimmQuickbootDmem11_BRH_C0.ecsbin"
"Type0x65_AppbDdr5RdimmQuickbootDmem12_BRH_C0.ecsbin"
"TypeId0x00_AmdPubKey_BRH.tkn"
"TypeId0x01_PspBl_BRH.esbin"
"TypeId0x02_PspOS_BRH.ecsbin"
"TypeId0x03_PspRecBl_BRH.esbin"
"TypeId0x08_SmuFirmware_breithorn.ecsbin"
"TypeId0x09_PspDebugUnlockToken_BRH.stkn"
"TypeId0x0A_PspAblPubKey_BRH.stkn"
"TypeId0x13_SduFw_BRH.ecsbin"
"TypeId0x15_IpKeyManagerDriver_BRH.ecsbin"
"TypeId0x1A_SevDriver_BRH.ecsbin"
"TypeId0x1B_BootDriver_BRH.ecsbin"
"TypeId0x1C_SocDriver_BRH.ecsbin"
"TypeId0x1D_HadDriver_BRH.ecsbin"
"TypeId0x1F_InterfaceDriver_BRH.ecsbin"
"TypeId0x208_SmuFirmware_BRHDense.ecsbin"
"TypeId0x21_PspAmdIkek_BRH.bin"
"TypeId0x224_RegisterAccessPolicy_BRHDense.cesbin"
"TypeId0x22A_SmuFirmware_BRHDense.ecsbin"
"TypeId0x22_SecureEmptyToken.bin"
"TypeId0x245_RegisterAccessPolicy_BRHDense.cesbin"
"TypeId0x24_RegisterAccessPolicy_BRH.cesbin"
"TypeId0x28_PspSystemDriver_BRH.ecsbin"
"TypeId0x2A_SmuFirmware_breithorn.ecsbin"
"TypeId0x2D_AblRt.ecsbin"
"TypeId0x30_AgesaBootLoaderU_BRH.ecsbin"
"TypeId0x38_PspSevEmptyData.bin"
"TypeId0x42_PhyFw_BRH.ecsbin"
"TypeId0x44_USB_PHY_BRH.esbin"
"TypeId0x45_RegisterAccessPolicy_BRH.cesbin"
"TypeId0x47_DRTMDriver_BRH.ecsbin"
"TypeId0x50_PspKeyDataBase_BRH.csbin"
"TypeId0x50_PspKeyDataBase_BRH.ecsbin"
"TypeId0x50_PspKeyDataBase_BRH.sbin"
"TypeId0x51_PspTosKeyDataBase_BRH.ecsbin"
"TypeId0x55_SPLTable_BRH.sbin"
"TypeId0x5DMpioFw_BRH.cesbin"
"TypeId0x64_RasDriver_BRH.ecsbin"
"TypeId0x65_ta_ras_prod_amdTEE.ecsbin"
"TypeId0x67_FHPDriver_BRH.ecsbin"
"TypeId0x68_SPDMDriver_BRH.ecsbin"
"TypeId0x69_DPEDriver_BRH.ecsbin"
"TypeId0x73_PspBl_BRH.ecsbin"
"TypeId0x76_DfRib_BRH.csbin"
"TypeId0x8C_MPDMATF_BRH.sbin"
"TypeId0x91_GmiPhyFw_BRH.esbin"
"TypeId0x92_Page_BRH.sbin"
"TypeId0x9D_AspSramFwExt_BRH.sbin"
"TypeId0x9F_psp_tos_wl_bin_brh.csbin"
"TypeId0x9F_psp_tos_wl_bin_brh.sbin"
"TypeId0xA0_S3Image_BRHD_A0.sbin"
"TypeId0xA0_S3Image_BRHD_B0.sbin"
"TypeId0xA0_S3Image_BRH_A0.sbin"
"TypeId0xA0_S3Image_BRH_B0.sbin"
"TypeId0xA0_S3Image_BRH_C0.sbin"
"TypeId0xA0_S3Image_BRH_C1.sbin"
"UcodePatch_BRHD_A0.bin"
"UcodePatch_BRHD_B0.bin"
"UcodePatch_BRH_A0.bin"
"UcodePatch_BRH_B0.bin"
"UcodePatch_BRH_C0.bin"
"UcodePatch_BRH_C1.bin"
<endComponent>
