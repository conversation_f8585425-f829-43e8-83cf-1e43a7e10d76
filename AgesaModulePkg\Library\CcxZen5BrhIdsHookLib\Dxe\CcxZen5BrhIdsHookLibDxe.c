
/*****************************************************************************
 *
 * Copyright (C) 2008-2024 Advanced Micro Devices, Inc. All rights reserved.
 *
 *******************************************************************************
 */

 /**
  * @file
  *
  * AMD CCX ZEN5 BRH API, and related functions.
  *
  * Contains code that initializes the core complex
  *
  * @xrefitem bom "File Content Label" "Release Content"
  * @e project:      AGESA
  * @e sub-project:  CCX
  * @e \$Revision$   @e \$Date$
  *
  */
#include <PiDxe.h>
#include <Library/BaseMemoryLib.h>
#include <Library/AmdBaseLib.h>
#include <Library/IdsLib.h>
#include <Library/AmdIdsHookLib.h>
#include <Library/UefiBootServicesTableLib.h>
#include <Library/CcxRolesLib.h>
#include <Protocol/AmdApcbProtocol.h>
#include <IdsHookId.h>
#include <IdsNvIdBRH.h>
#include "CcxZen5BrhIdsSyncMsr.h"
#include "CcxZen5BrhIdsCustomPstates.h"
#include <Filecode.h>
#include <Library/PcdLib.h>
#include <Library/AgesaConfigLib.h>
#include <ActOptions.h>
#include <BRH/ApcbV3TokenUid.h>
#include <Library/ApobCommonServiceLib.h>
#include <Library/AmdPspBaseLibV2.h>
#include <Protocol/MpService.h>

#define FILECODE LIBRARY_CCXZEN5BRHIDSHOOKLIB_DXE_CCXZEN5BRHIDSHOOKLIBDXE_FILECODE

/*----------------------------------------------------------------------------------------
 *                           G L O B A L   V A R I A B L E S
 *----------------------------------------------------------------------------------------
 */
STATIC EFI_EVENT CcxIdsZen5BrhDxeMpServiceEvent;
STATIC VOID     *CcxIdsZen5BrhForMpServiceEvent;

#define IDS_MAX_NUM_OF_SYNC_MSR 20
/*----------------------------------------------------------------------------------------
 *           P R O T O T Y P E S     O F     L O C A L     F U  N C T I O N S
 *----------------------------------------------------------------------------------------
 */
STATIC
VOID
EFIAPI
CcxIdsZen5BrhDxeMpServiceCallBack (
  IN EFI_EVENT        Event,
  IN VOID             *Context
  );

/*----------------------------------------------------------------------------------------
 *                          L O C A L    F U N C T I O N S
 *----------------------------------------------------------------------------------------
 */

IDS_HOOK_STATUS
CcxZen5BrhDxeAfterApLaunch (
  HOOK_ID   HookId,
  VOID      *Handle,
  VOID      *Data
  )
{
  EFI_STATUS        Status;
  AMD_CONFIG_PARAMS StdHeader;
  EFI_MP_SERVICES_PROTOCOL  MpService;

  Status = EFI_SUCCESS;

  if (CcxIsBsp (&StdHeader)) {
    IDS_HDT_CONSOLE (CPU_TRACE, "CcxZen5BrhDxeAfterApLaunch External Options\n");

    // L1 Stream HW Prefetcher --- AGESA
    // gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdL1StreamPrefetcher

    // L2 Stream HW Prefetcher --- AGESA
    // gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdL2StreamPrefetcher

    // Platform First Error Handling --- AGESA
    // gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdCcxCfgPFEHEnable

    // Core Performance Boost --- AGESA
    // gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdCpbMode

    // Global C-state Control --- AGESA
    // gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdCStateMode

    // Power Supply Idle Control --- AGESA
    // gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdPowerSupplyIdleControl

    // SEV-ES ASID Space Limit --- ABL
    // APCB_TOKEN_UID_CCX_MIN_SEV_ASID

    // XTRIG7 Workaround --- ABL
    // APCB_TOKEN_UID_XTRIG7_WORKAROUND

    // ACPI _CST C1 Declaration --- AGESA
    // gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdAcpiCstC1

    // Core Watchdog Timer Enable --- AGESA
    // gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdCpuWdtEn

    // Core Watchdog Timer Interval --- AGESA
    // gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdCpuWdtTimeout

    // Core Watchdog Timer Severity --- AGESA
    // gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdAmdCpuWdtSeverity

    // MCA error thresh enable --- AGESA
    // gEfiAmdAgesaPkgTokenSpaceGuid.PcdMcaErrThreshEn

    // MCA error thresh count --- AGESA
    // gEfiAmdAgesaPkgTokenSpaceGuid.PcdMcaErrThreshCount

    // RedirectForReturnDis --- AGESA
    // gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdRedirectForReturnDis

    // Streaming Stores Control --- AGESA
    // gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdStreamingStoresCtrl
  }

  //
  // Set up call back after gEfiMpServiceProtocolGuid.
  //
  Status = gBS->LocateProtocol (
                  &gEfiMpServiceProtocolGuid,
                  NULL,
                  (VOID *)&MpService
                  );
  if (Status == EFI_SUCCESS) {
    IDS_HDT_CONSOLE (CPU_TRACE, "gEfiMpServiceProtocolGuid already installed, executing CcxIdsZen5BrhDxeMpServiceCallBack\n");
    CcxIdsZen5BrhDxeMpServiceCallBack(NULL, NULL);
  }
  else if (Status == EFI_NOT_FOUND) {
    IDS_HDT_CONSOLE (CPU_TRACE, "gEfiMpServiceProtocolGuid not installed, creating event callback\n");
    Status = gBS->CreateEventEx (
                            EVT_NOTIFY_SIGNAL,
                            TPL_NOTIFY,
                            CcxIdsZen5BrhDxeMpServiceCallBack,
                            NULL,
                            NULL,
                            &CcxIdsZen5BrhDxeMpServiceEvent
                            );
    if (EFI_ERROR(Status)) {
      IDS_HDT_CONSOLE (CPU_TRACE, "CreateEventEx for CcxIdsZen5BrhDxeMpServiceEvent failed\n");
    }
    Status = gBS->RegisterProtocolNotify (
                            &gEfiMpServiceProtocolGuid,
                            CcxIdsZen5BrhDxeMpServiceEvent,
                            &(CcxIdsZen5BrhForMpServiceEvent)
                            );
    if (EFI_ERROR(Status)) {
      IDS_HDT_CONSOLE (CPU_TRACE, "RegisterProtocolNotify for CcxIdsZen5BrhDxeMpServiceEvent failed\n");
    }
  }

  if (Status == EFI_SUCCESS) {
    return IDS_HOOK_SUCCESS;
  } else {
    return IDS_HOOK_ERROR;
  }
}

/*---------------------------------------------------------------------------------------*/
/**
 * CcxIdsZen5BrhDxeMpServiceCallBack
 *
 *
 *  Parameters:
 *    @param[in]     Event
 *    @param[in]     *Context
 *
 *    @retval        VOID
 */
VOID
EFIAPI
CcxIdsZen5BrhDxeMpServiceCallBack (
  IN EFI_EVENT        Event,
  IN VOID             *Context
  )
{
  UINT16              i;
  IDS_BSC_AP_MSR_SYNC ApMsrSync[IDS_MAX_NUM_OF_SYNC_MSR];
  AMD_CONFIG_PARAMS   StdHeader;

  ZeroMem (ApMsrSync, sizeof (ApMsrSync));

  i = 0;

  // End of sync-up list

  ASSERT (i < IDS_MAX_NUM_OF_SYNC_MSR);

  CcxIdsZen5BrhDxeSyncMsr (ApMsrSync, &StdHeader);
}


/**
 *
 * This IDS HOOK Routine is called by CCX DXE driver
 * entry point. It publishes CCX PCD-based and Setup only
 * Options to ACT
 *
 *
 * @param[in] HookId          Ids Hook ID
 * @param[in] Handle          Handle passed to hook routine
 * @param[in] Data            Data pointer to data
 * @retval    IDS_HOOK_SUCCESS
 *
 **/
IDS_HOOK_STATUS
CcxOptionsToActIdsHook(
    HOOK_ID           HookId,
    VOID* Handle,
    VOID* Data
)
{
//    UINT64            IdsValue;
//    UINTN             Index;
//    STATIC struct {
//      UINT32          ActUid;
//      IDS_NV_ID       IdsNvId;
//    } ActCfgSettings[] = {
//      {ACT_CFG_UID_CmnCpuOcMode,              IDSNVID_CMN_CPU_OC_MODE},
//      {ACT_CFG_UID_CpuPstCustomP1,            IDSNVID_CPU_PST_CUSTOM_P1},
//      {ACT_CFG_UID_CpuPstCustomP2,            IDSNVID_CPU_PST_CUSTOM_P2},
//      {ACT_CFG_UID_CpuPstCustomP3,            IDSNVID_CPU_PST_CUSTOM_P3},
//      {ACT_CFG_UID_CpuPstCustomP4,            IDSNVID_CPU_PST_CUSTOM_P4},
//      {ACT_CFG_UID_CpuPstCustomP5,            IDSNVID_CPU_PST_CUSTOM_P5},
//      {ACT_CFG_UID_CpuPstCustomP6,            IDSNVID_CPU_PST_CUSTOM_P6},
//      {ACT_CFG_UID_CpuPstCustomP7,            IDSNVID_CPU_PST_CUSTOM_P7} };
//
//    for (Index = 0; Index < sizeof(ActCfgSettings) / sizeof(ActCfgSettings[0]); Index++) {
//      IdsValue = 0;
//      IDS_NV_READ_SKIP(ActCfgSettings[Index].IdsNvId, &IdsValue) {
//        SetAgesaCfg8(ActCfgSettings[Index].ActUid, (UINT8)IdsValue);
//      }
//    }

    SetAgesaCfg8 (ACT_CFG_UID_CpuPstCustomP0, PcdGet8(PcdAmdCcxP0Setting));
    SetAgesaCfg8 (ACT_CFG_UID_CmnCpuRMSS, (UINT8)PcdGetBool(PcdAmdEnableRMSS));
    SetAgesaCfg8 (ACT_CFG_UID_CmnCpuL1StreamHwPrefetcher, (UINT8)PcdGetBool(PcdAmdL1StreamPrefetcher));
    SetAgesaCfg8 (ACT_CFG_UID_CmnCpuL1StridePrefetcher, (UINT8)PcdGetBool(PcdAmdL1StridePrefetcher));
    SetAgesaCfg8 (ACT_CFG_UID_CmnCpuL1RegionPrefetcher, (UINT8)PcdGetBool(PcdAmdL1RegionPrefetcher));
    SetAgesaCfg8 (ACT_CFG_UID_CmnCpuL1BurstPrefetchMode, (UINT8)PcdGetBool(PcdAmdL1BurstPrefetch));
    SetAgesaCfg8 (ACT_CFG_UID_CmnCpuL2StreamHwPrefetcher, (UINT8)PcdGetBool(PcdAmdL2StreamPrefetcher));
    SetAgesaCfg8 (ACT_CFG_UID_CmnCpuL2UpDownPrefetcher, (UINT8)PcdGetBool(PcdAmdL2UpDownPrefetcher));
    SetAgesaCfg8 (ACT_CFG_UID_DbgCpuGenCpuWdt, (UINT8)PcdGetBool(PcdAmdCpuWdtEn));
    SetAgesaCfg16 (ACT_CFG_UID_DbgCpuGenCpuWdtTimeout, PcdGet16(PcdAmdCpuWdtTimeout));
    SetAgesaCfg8 (ACT_CFG_UID_CmnCpuGenWA05, PcdGet8(PcdAmdRedirectForReturnDis));
    SetAgesaCfg8 (ACT_CFG_UID_CmnCpuPfeh, (UINT8)PcdGetBool(PcdAmdCcxCfgPFEHEnable));
    SetAgesaCfg8 (ACT_CFG_UID_CmnCpuCpb, PcdGet8(PcdAmdCpbMode));
    SetAgesaCfg8 (ACT_CFG_UID_CmnCpuGlobalCstateCtrl, PcdGet8(PcdAmdCStateMode));
    SetAgesaCfg8 (ACT_CFG_UID_CmnGnbPowerSupplyIdleCtrl, PcdGet8(PcdAmdPowerSupplyIdleControl));
    SetAgesaCfg8 (ACT_CFG_UID_CmnCpuStreamingStoresCtrl, PcdGet8(PcdAmdStreamingStoresCtrl));
    SetAgesaCfg8 (ACT_CFG_UID_CmnCpuCstC1Ctrl, (UINT8)PcdGetBool(PcdAmdAcpiCstC1));
    SetAgesaCfg8 (ACT_CFG_UID_CmnCpuMcaErrThreshEn, (UINT8)PcdGetBool(PcdMcaErrThreshEn));
    SetAgesaCfg8 (ACT_CFG_UID_DbgCpuSnpMemCover, PcdGet8(PcdAmdSnpMemCover));
    SetAgesaCfg8 (ACT_CFG_UID_CmnCpuSmee, (UINT8)PcdGetBool(PcdAmdSmee));
    SetAgesaCfg8 (ACT_CFG_UID_CmnCpuFSRM, (UINT8)PcdGetBool(PcdAmdEnableFSRM));
    SetAgesaCfg8 (ACT_CFG_UID_CmnCpuERMS, (UINT8)PcdGetBool(PcdAmdEnableERMS));
    SetAgesaCfg8 (ACT_CFG_UID_CmnCpuLogTransparentErrors, (UINT8)PcdGetBool (PcdAmdTransparentErrorLoggingEnable));
    SetAgesaCfg8 (ACT_CFG_UID_CmnCpuAvx512, PcdGet8 (PcdAmdCcxEnableAvx512));
    SetAgesaCfg8 (ACT_CFG_UID_CmnCpuMonMwaitDis, PcdGet8 (PcdAmdMonMwaitDis));
    SetAgesaCfg8 (ACT_CFG_UID_CmnCpuDisFstStrErmsb, PcdGet8 (PcdAmdCcxDisFstStrErmsb));
    SetAgesaCfg8 (ACT_CFG_UID_CpuSpeculativeStoreModes, PcdGet8 (PcdAmdCpuSpeculativeStoreMode));
    SetAgesaCfg8 (ACT_CFG_UID_CmnCpuPauseCntSel_1_0, PcdGet8 (PcdAmdCpuPauseCntSel_1_0));
    SetAgesaCfg8 (ACT_CFG_UID_CmnCpuAdaptiveAlloc, PcdGet8 (PcdAmdCpuAdaptiveAlloc));

    return IDS_HOOK_SUCCESS;
}


#ifndef IDS_HOOK_INTERNAL_SUPPORT
  #define CCX_IDS_HOOKS_INT
#else
  #include <Internal/CcxZen5BrhIdsHookLibIntDxe.h>
#endif

STATIC IDS_HOOK_ELEMENT CcxZen5BrhIdsHooks[] = {
//  {
//    IDS_HOOK_CCX_AFTER_AP_LAUNCH,
//    &CcxZen5BrhDxeAfterApLaunch
//  },
  {
    IDS_HOOK_CCX_AFTER_AP_LAUNCH,
    &CcxOptionsToActIdsHook
  },
  {
    IDS_HOOK_CCX_CUSTOM_PSTATES,
    &CcxZen5BrhIdsDxeCustomPstates
  },
  CCX_IDS_HOOKS_INT
  IDS_HOOKS_END
};

STATIC IDS_HOOK_TABLE CcxZen5BrhIdsHookTable = {
  IDS_HOOK_TABLE_HEADER_REV1_DATA,
  CcxZen5BrhIdsHooks
};

AGESA_STATUS
GetIdsHookTable (
  IDS_HOOK_TABLE **IdsHookTable
  )
{
  *IdsHookTable = &CcxZen5BrhIdsHookTable;
  return AGESA_SUCCESS;
}

