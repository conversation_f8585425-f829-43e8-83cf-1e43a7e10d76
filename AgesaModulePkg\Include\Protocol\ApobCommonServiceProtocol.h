/*****************************************************************************
 * Copyright (C) 2008-2024 Advanced Micro Devices, Inc. All rights reserved.
 *
*****************************************************************************
*/
/* $NoKeywords:$ */
/**
 * @file
 *
 * APOB common service Protocol prototype definition
 *
 *
 * @xrefitem bom "File Content Label" "Release Content"
 * @e project:      AGESA
 * @e sub-project:  PSP
 * @e \$Revision: 309090 $   @e \$Date: 2014-12-10 02:28:05 +0800 (Wed, 10 Dec 2014) $
 */
#ifndef _AMD_PSP_COMMONSERVICE_PROTOCOL_H_
#define _AMD_PSP_COMMONSERVICE_PROTOCOL_H_

#include "ApobCommonServicePub.h"
#include <Addendum/Apcb/Inc/APOBCMN.h>


/**
 * @brief Structure for APCB Recovery Flag
 *
 * @param[in,out] ApcbRecoveryFlag               The bool value of the APCB revocvery flag
 */
typedef
EFI_STATUS
(*FP_GET_APCB_RECOVERY_FLAG)(
  IN OUT   BOOLEAN  *ApcbRecoveryFlag  ///< pointer to indicate if APCB recovery flag is set
  );

/**
 * @brief Structure for DIMM Config Flag
 *
 * @param[in,out] DimmCfgUpdatedFlag               The bool value of the DIMMCFG updataed flag
 */
typedef
EFI_STATUS
(*FP_GET_DIMMCFG_UPDATED_FLAG) (
  IN OUT   BOOLEAN  *DimmCfgUpdatedFlag  ///< pointer to indicate if Dimm config updated flag is set
  );

/**
 * @brief Structure for APCB instance
 *
 * @param[in,out] ApcbInstance               Point to the APCB instance
 */
typedef
EFI_STATUS
(*FP_GET_APCB_INSTANCE) (
  IN OUT   UINT8  *ApcbInstance  ///< pointer to APCB instance
  );

/**
 * @brief Structure for PHY CCD number
 *
 * @param[in]     ApobInstanceId               Index of APOB instance
 * @param[in]     CcdIndex                     Index of CDD
 * @param[in,out] PhysCcdNumber                Number of phys ccd
 */
typedef
EFI_STATUS
(*FP_GET_PHYS_CCD_NUMBER) (
  IN       UINT32 ApobInstanceId,  ///< APOB instance Id
  IN       UINT32 CcdIndex,        ///< CCD index value
  IN OUT   UINT8 *PhysCcdNumber    ///< PHY CCD number
);

/**
 * @brief Structure for PHY complex number
 *
 * @param[in]     ApobInstanceId               Index of APOB instance
 * @param[in]     CcdIndex                     Index of CDD
 * @param[in]     CcxIndex                     Index of CCX
 * @param[in,out] PhysComplexNumber            Number of phys complex number
 */
typedef
EFI_STATUS
(*FP_GET_PHYS_COMPLEX_NUMBER) (
  IN       UINT32 ApobInstanceId,    ///< APOB instance Id
  IN       UINT32 CcdIndex,          ///< CCD index number
  IN       UINT32 CcxIndex,          ///< CCX index number
  IN OUT   UINT8 *PhysComplexNumber  ///< PHY complex number
);

/**
 * @brief Structure for PHY core number
 *
 * @param[in]     ApobInstanceId               Index of APOB instance
 * @param[in]     CcdIndex                     Index of CDD
 * @param[in]     CcxIndex                     Index of CCX
 * @param[in]     CoreIndex                    Index of core
 * @param[in,out] PhysCoreNumber               Number of phys core number
 */
typedef
EFI_STATUS
(*FP_GET_PHYSCORE_NUMBER) (
  IN       UINT32 ApobInstanceId,  ///< APOB instance Id
  IN       UINT32 CcdIndex,        ///< CCD index number
  IN       UINT32 CcxIndex,        ///< CCX index number
  IN       UINT32 CoreIndex,       ///< Core index number
  IN OUT   UINT8  *PhysCoreNumber  ///< PHY core number
);

/**
 * @brief Structure for Thread Information
 *
 * @param[in]     ApobInstanceId               Index of APOB instance
 * @param[in]     CcdIndex                     Index of CDD
 * @param[in]     CcxIndex                     Index of CCX
 * @param[in]     CoreIndex                    Index of core
 * @param[in]     ThreadIndex                  Index of thread
 * @param[in,out] IsThreadEnabled              Boolean value
 */
typedef
EFI_STATUS
(*FP_GET_IS_THREAD_ENABLED) (
  IN       UINT32 ApobInstanceId,    ///< APOB instance Id
  IN       UINT32 CcdIndex,          ///< CCD index value
  IN       UINT32 CcxIndex,          ///< core complex index value
  IN       UINT32 CoreIndex,         ///< core index value
  IN       UINT32 ThreadIndex,       ///< thread index value
  IN OUT   BOOLEAN *IsThreadEnabled  ///< pointer to indicate if thread is enabled
);

/**
 * @brief Structure for ECC information for a channel
 *
 * @param[in]     ApobInstanceId               Index of APOB instance
 * @param[in]     ChannelIndex                 Index of channel
 * @param[in,out] EccEnable                    Boolean value
 */
typedef
EFI_STATUS
(*FP_GET_ECC_ENABLE) (
  IN       UINT32 ApobInstanceId,  ///< APOB instance Id
  IN       UINT32 ChannelIndex,    ///< channel index value
  IN OUT   BOOLEAN *EccEnable      ///< pointer to indicate if ECC is enabled
);

/**
 * @brief Structure to detect Non volatile DIMM in system
 *
 * @param[in]     ApobInstanceId               Index of APOB instance
 * @param[in,out] NvdimmPresentInSystem        Boolean value
 */
typedef
EFI_STATUS
(*FP_GET_NVDIMM_PRESENT_IN_SYSTEM) (
  IN       UINT32 ApobInstanceId,          ///< APOB instance Id
  IN OUT   BOOLEAN *NvdimmPresentInSystem  ///< pointer to indicate if NV DIMM is present
);

/**
 * @brief Structure for Non volatile DIMM information
 *
 * @param[in]     ApobInstanceId               index of APOB instance
 * @param[in,out] NvdimmInfo                   point to the Nvdimm information
 * @param[in,out] NvdimmInfoSize               point to the size of the Nvdimm information
 */
typedef
EFI_STATUS
(*FP_GET_NVDIMM_INFO) (
  IN       UINT32 ApobInstanceId,  ///< APOB instance Id
  IN OUT   UINT32 **NvdimmInfo,    ///< NV DIMM information
  IN OUT   UINT32 *NvdimmInfoSize  ///< NV DIMM info size
);

/**
 * @brief Structure for Max DIMMs per channel
 *
 * @param[in]     ApobInstanceId               Index of APOB instance
 * @param[in,out] MaxDimmsPerChannel           Point to the max DIMMS number per channel
 */
typedef
EFI_STATUS
(*FP_GET_MAX_DIMMS_PER_CHANNEL) (
  IN       UINT32 ApobInstanceId,     ///< APOB instance Id
  IN OUT   UINT8 *MaxDimmsPerChannel  ///< pointer to indicate max DIMMs per channel
);

/**
 * @brief Structure for Max channels per socket
 *
 * @param[in]     ApobInstanceId               Index of APOB instance
 * @param[in,out] MaxChannelsPerSocket         Point to the max channels number per socket
 */
typedef
EFI_STATUS
(*FP_GET_MAX_CHANNELS_PER_SOCKET) (
  IN       UINT32 ApobInstanceId,       ///< APOB instance Id
  IN OUT   UINT8 *MaxChannelsPerSocket  ///< pointer to indicate max channels per socket
);

/**
 * @brief Structure for Max channels per die
 *
 * @param[in]     ApobInstanceId               Index of APOB instance
 * @param[in,out] MaxChannelsPerDie            Point to the max channels number per die
 */
typedef
EFI_STATUS
(*FP_GET_MAX_CHANNELS_PER_DIE) (
  IN       UINT32 ApobInstanceId,    ///< APOB instance Id
  IN OUT   UINT8 *MaxChannelsPerDie  ///< pointer for max channel per die
);

/**
 * @brief Structure for DIMM System Management Bus information
 *
 * @param[in]     ApobInstanceId               Index of APOB instance
 * @param[in]     Index                        Index to visit
 * @param[in,out] DimmSmbusInfo                Point to the dimm smbus information
 * @param[in,out] BufferSize                   Buffer size
 */
typedef
EFI_STATUS
(*FP_GET_DIMM_SMBUS_INFO) (
  IN       UINT32 ApobInstanceId,           ///< APOB instance Id
  IN       UINT32 Index,                    ///< index value
  IN OUT   VOID **DimmSmbusInfo,            ///< APOB struct pointer to indicate DIMM SMBUS information
  IN OUT   UINTN  *BufferSize               ///< Buffer size of the APOB struct
);

/**
 * @brief Structure for Memory Frequency
 *
 * @param[in]     ApobInstanceId               Index of APOB instance
 * @param[in,out] AmdMemoryFrequency           Point to the memory frequency
 */
typedef
EFI_STATUS
(*FP_GET_MEMORY_FREQUENCY) (
  IN       UINT32 ApobInstanceId,      ///< APOB instance Id
  IN OUT   UINT16 *AmdMemoryFrequency  ///< pointer to memory frequency value
);

/**
 * @brief Structure for maximum double data rate
 *
 * @param[in]     ApobInstanceId               Index of APOB instance
 * @param[in,out] DdrMaxRate                   Point to the memory frequency
 */
typedef
EFI_STATUS
(*FP_GET_DDR_MAX_RATE) (
  IN       UINT32 ApobInstanceId,  ///< APOB instance Id
  IN OUT   UINT16 *DdrMaxRate      ///< pointer to indicate max DDR rate
);

/**
 * @brief Structure for memory gen element
 *
 * @param[in]     ApobInstanceId               Index of APOB instance
 * @param[in]     ElementId                    Index of element
 * @param[in,out] ElementSizeInByte            Point to the element size
 * @param[in,out] ElementValue                 Point to the elemet value
 */
typedef
EFI_STATUS
(*FP_GET_MEM_GEN_INFO_ELEMENT) (
  IN       UINT32 ApobInstanceId,      ///< APOB instance Id
  IN       UINT32 ElementId,           ///< element Id
  IN OUT   UINT16 *ElementSizeInByte,  ///< element size
  IN OUT   UINT64 *ElementValue        ///< element value
  );

/**
 * @brief Structure for valid thresholds
 *
 * @param[in]   ApobInstanceId               Index of APOB instance
 * @param[in]   NumberOfValidThresholds      Point to the number of valid threshold
 */
typedef
EFI_STATUS
(*FP_GET_NUMBER_OF_VALID_THRESHOLDS) (
  IN       UINT32 ApobInstanceId,           ///< APOB instance Id
  IN       UINT32 *NumberOfValidThresholds  ///< pointer to indicate number of valid thresholds
  );

/**
 * @brief Structure for Threshold information
 *
 * @param[in]   ApobInstanceId               Index of APOB instance
 * @param[in]   Index                        Index to visit
 * @param[in]   Thresholds                   Point to the threshold
 */
typedef
EFI_STATUS
(*FP_GET_THRESHOLDS) (
  IN       UINT32 ApobInstanceId,               ///< APOB instance Id
  IN       UINT32 Index,                        ///< index value
  IN       EDC_THROTTLE_THRESHOLD **Thresholds  ///< EDC structure pointer for threshold value
  );

/**
 * @brief Structure for board mask information, stored in APCB header
 *
 * @param[in]   ApobInstanceId               Index of APOB instance
 * @param[in]   BoardMask                    Point to the BoardMask
 */
typedef
EFI_STATUS
(*FP_GET_BOARDMASK) (
  IN       UINT32 ApobInstanceId,  ///< APOB instance Id
  IN       UINT16 *BoardMask       ///< sixteen bit board mask flag
  );

/**
 * @brief Structure for subprogram field, accessed by firmware for chip variants
 *
 * @param[in,out] SubProgram                    Point to the SubProgram
 */
typedef
EFI_STATUS
(*FP_GET_SUBPROGRAM) (
  OUT      UINT32 *SubProgram  ///< subprogram field value
  );

/**
 * @brief Structure for DIMM SPD data
 *
 * @param[in]     ApobInstanceId               Index of APOB instance
 * @param[in]     Socket                       Index of socket
 * @param[in]     Channel                      Index of channel
 * @param[in]     Dimm                         Index of dimm
 * @param[in]     BufSize                      Buffer size
 * @param[in,out] SpdBufPtr                    Point to the spd buffer
 */
typedef
EFI_STATUS
(*FP_GET_DIMM_SPD_DATA) (
  IN       UINT32 ApobInstanceId,  ///< APOB instance Id
  IN       UINT8  Socket,          ///< socket value
  IN       UINT8  Channel,         ///< channel number
  IN       UINT8  Dimm,            ///< DIMM value
  IN       UINT32 BufSize,         ///< SPD buffer size
  IN OUT   UINT8  *SpdBufPtr       ///< pointer to SPD buffer
  );

/**
 *  @brief DXE Protocol Prototype
 *
 *  @details Defines APOB_COMMON_SERVICE_PROTOCOL, which public the common APOB service across all programs
 */
typedef struct _APOB_COMMON_SERVICE_PROTOCOL {
  FP_GET_APCB_INSTANCE                    ApobGetApcbInstance;             ///< APCB instance value
  FP_GET_APCB_RECOVERY_FLAG               ApobGetApcbRecoveryFlag;         ///< APCB recovery flag
  FP_GET_DIMMCFG_UPDATED_FLAG             ApobGetDimmCfgUpdatedFlag;       ///< DIMM config updated flag
  FP_GET_PHYS_CCD_NUMBER                  ApobGetPhysCcdNumber;            ///< PHY CCD number
  FP_GET_PHYS_COMPLEX_NUMBER              ApobGetPhysComplexNumber;        ///< PHY complex number
  FP_GET_PHYSCORE_NUMBER                  ApobGetPhysCoreNumber;           ///< PHY core number
  FP_GET_IS_THREAD_ENABLED                ApobGetIsThreadEnabled;          ///< confirms if thread is enabled
  FP_GET_ECC_ENABLE                       ApobGetEccEnable;                ///< confirms if ECC is enabled
  FP_GET_NVDIMM_PRESENT_IN_SYSTEM         ApobGetNvdimmPresentInSystem;    ///< confirms if NV DIMM exists in syste,
  FP_GET_NVDIMM_INFO                      ApobGetNvdimmInfo;               ///< Non volatile DIMM information
  FP_GET_MAX_DIMMS_PER_CHANNEL            ApobGetMaxDimmsPerChannel;       ///< Max number of DIMMS per channel supported
  FP_GET_MAX_CHANNELS_PER_DIE             ApobGetMaxChannelsPerDie;        ///< Max number of channels per die supported
  FP_GET_MAX_CHANNELS_PER_SOCKET          ApobGetMaxChannelsPerSocket;     ///< Max number of channels per socket supported
  FP_GET_DIMM_SMBUS_INFO                  ApobGetDimmSmbusInfo;            ///< DIMM SMBUS information
  FP_GET_MEMORY_FREQUENCY                 ApobGetMemoryFrequency;          ///< Memory Frequency value
  FP_GET_DDR_MAX_RATE                     ApobGetDdrMaxRate;               ///< Max DDR rate value
  FP_GET_MEM_GEN_INFO_ELEMENT             ApobGetMemGenInfoElement;        ///< Memory gen Element
  FP_GET_NUMBER_OF_VALID_THRESHOLDS       ApobGetNumberOfValidThresholds;  ///< number of valid thresholds
  FP_GET_THRESHOLDS                       ApobGetThresholds;               ///< Threshold value
  FP_GET_BOARDMASK                        ApobGetBoardMask;                ///< Board mask flag value
  FP_GET_SUBPROGRAM                       ApobGetSubProgram;               ///< Sub-program field for chip variant firmware modules
  FP_GET_DIMM_SPD_DATA                    ApobGetDimmSpdData;              ///< SPD data value
} APOB_COMMON_SERVICE_PROTOCOL;

///
/// Guid declaration for the APOB_COMMON_SERVICE_PROTOCOL.
///
extern EFI_GUID gApobCommonServiceProtocolGuid;

#endif //_AMD_PSP_COMMONSERVICE_PROTOCOL_H_



