/*****************************************************************************
 * Copyright (C) 2008-2024 Advanced Micro Devices, Inc. All rights reserved.
 *
*****************************************************************************
*/
/* $NoKeywords:$ */
/**
 * @file
 *
 * Config Fch SATA controller
 *
 * Init SATA features.
 *
 * @xrefitem bom "File Content Label" "Release Content"
 * @e project:     AGESA
 * @e sub-project: FCH
 * @e \$Revision: 309083 $   @e \$Date: 2014-12-09 09:28:24 -0800 (Tue, 09 Dec 2014) $
 *
 */
#include "FchPlatform.h"
#include "Filecode.h"
#define FILECODE FCH_KUNLUN_FCHKUNLUNCORE_KUNLUN_KLSATA_KLSATAMID_FILECODE

/**
 * FchInitMidSata - Config SATA controller after PCI emulation
 *
 *
 *
 * @param[in] FchDataPtr Fch configuration structure pointer.
 *
 */
/*
VOID
FchInitMidSata (
  IN  VOID     *FchDataPtr
  )
{
}
*/
VOID
FchInitMidSata (
  IN  VOID     *FchDataPtr
  )
{
  UINT32                 SataController;
  FCH_DATA_BLOCK         *LocalCfgPtr;
//  AMD_CONFIG_PARAMS      *StdHeader;

  LocalCfgPtr = (FCH_DATA_BLOCK *) FchDataPtr;
//  StdHeader = LocalCfgPtr->StdHeader;

  AGESA_TESTPOINT (TpFchInitMidSata, NULL);

  for (SataController = 0; SataController < KUNLUN_SATA_CONTROLLER_NUM; SataController++) {
    if (LocalCfgPtr->Sata[SataController].SataEnable) {
      SataEnableWriteAccessKL (0, SataController);
      FchKLInitMidProgramSataRegs (0, SataController, FchDataPtr);

      SataDisableWriteAccessKL (0, SataController);
    } else {
      continue;
    }
  }
}



