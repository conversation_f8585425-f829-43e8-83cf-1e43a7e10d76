#;*****************************************************************************
#;
#; Copyright (C) 2008-2024 Advanced Micro Devices, Inc. All rights reserved.
#;
#;******************************************************************************
[Defines]
  INF_VERSION                    = 0x00010006
  BASE_NAME                      = CcxZen5BrhIdsHookLibDxe
  FILE_GUID                      = 79F70100-5462-4C89-8EB5-F67A640BA494
  MODULE_TYPE                    = BASE
  VERSION_STRING                 = 1.0
  LIBRARY_CLASS                  = CcxZen5IdsHookLibDxe

[Sources.common]
  CcxZen5BrhIdsHookLibDxe.c
  CcxZen5BrhIdsSyncMsr.c
  CcxZen5BrhIdsSyncMsr.h
  CcxZen5BrhIdsCustomPstates.c
  CcxZen5BrhIdsCustomPstates.h

[Packages]
  MdePkg/MdePkg.dec
  AgesaModulePkg/AgesaCommonModulePkg.dec
  AgesaModulePkg/AgesaModuleCcxPkg.dec
  AgesaModulePkg/AgesaModuleDfPkg.dec
  AgesaModulePkg/AgesaModuleNbioPkg.dec
  AgesaPkg/AgesaPkg.dec

[LibraryClasses]
  BaseLib
  AmdBaseLib
  BaseMemoryLib
  AmdIdsDebugPrintLib
  CcxRolesLib
  AmdPspApobLib
  CcxPstatesLib
  CcxMpServicesLib
  AgesaConfigLib
  PcdLib
  ApobApcbLib
  ApobCommonServiceLib
  AmdPspBaseLibV2

[Guids]

[Protocols]
  gEfiMpServiceProtocolGuid               #CONSUME
  gAmdFabricTopologyServices2ProtocolGuid #CONSUME
  gAmdCoreTopologyServicesV2ProtocolGuid  #CONSUME
  gAmdNbioSmuServicesProtocolGuid         #CONSUME
  gEfiSmmControl2ProtocolGuid             #CONSUME
  gAmdSocZen3ServicesProtocolGuid         #CONSUME
  gAmdApcbDxeServiceProtocolGuid          #CONSUME

[Ppis]

[FeaturePcd]

[Pcd]
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdOcDisable
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdOcVoltageMax
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdOcFrequencyMax
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdCcxP0Setting
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdCcxP0Vid32
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdCcxP0Freq
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdAmdCcxPxAutoSetting
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdAmdCcxPxAutoVid
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdAmdCcxPxAutoFreq
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdEnableRMSS
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdL1StreamPrefetcher
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdL1StridePrefetcher
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdL1RegionPrefetcher
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdL1BurstPrefetch
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdL2StreamPrefetcher
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdL2UpDownPrefetcher
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdCpuWdtEn
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdCpuWdtTimeout
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdRedirectForReturnDis
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdCcxCfgPFEHEnable
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdCpbMode
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdCStateMode
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdPowerSupplyIdleControl
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdStreamingStoresCtrl
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdAcpiCstC1
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdMcaErrThreshEn
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdSnpMemCover
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdSmee
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdEnableFSRM
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdEnableERMS
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdMonMwaitDis
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdTransparentErrorLoggingEnable
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdCcxEnableAvx512
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdCfgIommuSupport
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdAmdStartupAllAPsSingleThread
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdCcxDisFstStrErmsb
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdCpuSpeculativeStoreMode
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdCpuPauseCntSel_1_0
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdCpuAdaptiveAlloc

[Depex]
  TRUE

