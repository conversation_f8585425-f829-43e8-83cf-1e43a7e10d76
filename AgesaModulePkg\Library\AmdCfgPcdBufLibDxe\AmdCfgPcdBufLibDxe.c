/*
*****************************************************************************
*
 * Copyright (C) 2021-2024 Advanced Micro Devices, Inc. All rights reserved.
 *
*******************************************************************************
*
*/
/* $NoKeywords:$ */
/**
 * @file
 *
 * Service procedure to cache PCD setting.
 *
 *
 *
 * @xrefitem bom "File Content Label" "Release Content"
 * @e project:     AGESA
 * @e sub-project: LIB
 * @e \$Revision: 321005 $   @e \$Date: 2021-09-07 14:15:16 +0800 (Tu<PERSON>, 7 Sep 2021) $
 *
 */
/*----------------------------------------------------------------------------------------
 *                             M O D U L E S    U S E D
 *----------------------------------------------------------------------------------------
 */
#include <Uefi.h>
#include <Library/PcdLib.h>
#include <Library/IdsLib.h>
#include <Library/BaseMemoryLib.h>
#include <Library/UefiBootServicesTableLib.h>
#include <Library/AmdCfgPcdBufLib.h>
#include <Protocol/Pcd.h>
#include <Protocol/PiPcd.h>

#include <Filecode.h>

#define FILECODE LIBRARY_AMDCFGPCDBUFLIBDXE_AMDCFGPCDBUFLIBDXE_FILECODE

/*----------------------------------------------------------------------------------------
 *                   D E F I N I T I O N S    A N D    M A C R O S
 *----------------------------------------------------------------------------------------
 */


/*----------------------------------------------------------------------------------------
 *                  T Y P E D E F S     A N D     S T R U C T U  R E S
 *----------------------------------------------------------------------------------------
 */
static CFG_PCD_BUF_STRUCT mCfgPcdBufStructAddress;

/**
 * @brief The constructor function used to Initial Pcd Buffer Data in DXE.
 *
 * @param  ImageHandle   The firmware allocated handle for the EFI image.
 * @param  SystemTable   A pointer to the EFI System Table.
 *
 * @return  EFI_STATUS  EFI_SUCCESS       Success
 */
EFI_STATUS
EFIAPI
AmdCfgPcdBufLibDxeConstructor (
  IN EFI_HANDLE        ImageHandle,
  IN EFI_SYSTEM_TABLE  *SystemTable
)
{
  EFI_STATUS       Status;
  PCD_PROTOCOL     *PcdProtocol;
  EFI_PCD_PROTOCOL *PiPcdProtocol;

  Status        = EFI_SUCCESS;
  PcdProtocol   = NULL;
  PiPcdProtocol = NULL;

  ZeroMem (&mCfgPcdBufStructAddress, sizeof(CFG_PCD_BUF_STRUCT));

  Status = gBS->LocateProtocol (
                  &gPcdProtocolGuid,
                  NULL,
                  (VOID **)&PcdProtocol
                  );
  if(EFI_ERROR (Status)) {
    IDS_HDT_CONSOLE_PSP_TRACE ("[%a] LocateProtocol: gPcdProtocolGuid, Status: %r\n", \
      __FUNCTION__, Status);
    return EFI_SUCCESS;
  }

  Status = gBS->LocateProtocol (
                  &gEfiPcdProtocolGuid,
                  NULL,
                  (VOID **)
                  &PiPcdProtocol
                  );
  if(EFI_ERROR (Status)) {
    IDS_HDT_CONSOLE_PSP_TRACE ("[%a] LocateProtocol: gEfiPcdProtocolGuid, Status: %r\n", \
      __FUNCTION__, Status);
    return EFI_SUCCESS;
  }

  mCfgPcdBufStructAddress.BufPcdAgesaTestPointEnable       = PcdGetBool (PcdAgesaTestPointEnable);

  mCfgPcdBufStructAddress.BufPcdAgesaTestPointEnableSmm    = PcdGetBool (PcdAgesaTestPointEnableSmm);

  mCfgPcdBufStructAddress.BufPcdAgesaTestPointWidth        = PcdGet8 (PcdAgesaTestPointWidth);

  mCfgPcdBufStructAddress.BufPcdIdsDebugPort               = PcdGet16 (PcdIdsDebugPort);

  mCfgPcdBufStructAddress.BufPcdAgesaAssertEnable          = PcdGetBool (PcdAgesaAssertEnable);

  mCfgPcdBufStructAddress.BufPcdAgesaTestPointToStb        = PcdGetBool (PcdAgesaTestPointToStb);

  mCfgPcdBufStructAddress.BufPcdStbEnable                  = PcdGetBool (PcdStbEnable);

  mCfgPcdBufStructAddress.BufPcdGlobalStbVerbosityControl  = PcdGet8 (PcdGlobalStbVerbosityControl);

  mCfgPcdBufStructAddress.BufPcdStbBiosVerbosityControl    = PcdGet8 (PcdStbBiosVerbosityControl);

  mCfgPcdBufStructAddress.BufPcdStbSmuVerbosityControl     = PcdGet8 (PcdStbSmuVerbosityControl);

  mCfgPcdBufStructAddress.BufPcdStbPspVerbosityControl     = PcdGet8 (PcdStbPspVerbosityControl);

  mCfgPcdBufStructAddress.BufPcdStbSmnAddress              = PcdGet32 (PcdStbSmnAddress);

  mCfgPcdBufStructAddress.BufPcdStbFilterMaskEnable        = PcdGetBool (PcdStbFilterMaskEnable);

  mCfgPcdBufStructAddress.BufPcdMpPostcodeIp0SmnAddress    = PcdGet32 (PcdMpPostcodeIp0SmnAddress);

  mCfgPcdBufStructAddress.BufPcdMpPostcodeConfigSmnAddress = PcdGet32 (PcdMpPostcodeConfigSmnAddress);

  mCfgPcdBufStructAddress.BufPcdStbIbvSourceId             = PcdGet8 (PcdStbIbvSourceId);

  mCfgPcdBufStructAddress.BufPcdStbMpioVerbosityControl    = PcdGet8 (PcdStbMpioVerbosityControl);

  return EFI_SUCCESS;
}

/**
 * @brief Get Pcd Variable
 * @details Get Pcd Value from PcdGetxx in Pei Phase.
 *          Get Pcd Value from Buffer in Dxe/Smm Phase.
 * @param[in]           CFG_PCD_BUF_ENUM  Enum Value
 * @param[in]           *VOID             The Pointer of Value
 * @param[in]           UINTN             The Value Size
 *
 * @return  EFI_STATUS  EFI_SUCCESS       Success
 *                      EFI_UNSUPPORTED   Function Unsupported
 */
EFI_STATUS
AmdCfgPcdBufGetVariable (
  CFG_PCD_BUF_ENUM  CfgPcdBufEnum,
  VOID             *Value,
  UINTN            ValueSize
)
{
  EFI_STATUS  Status;

  Status = EFI_SUCCESS;

  switch (CfgPcdBufEnum) {
    case  EnumPcdAgesaTestPointEnable:
      ASSERT (ValueSize == sizeof(BOOLEAN));
      *(BOOLEAN *)Value = mCfgPcdBufStructAddress.BufPcdAgesaTestPointEnable;
      break;

    case  EnumPcdAgesaTestPointEnableSmm:
      ASSERT (ValueSize == sizeof(BOOLEAN));
      *(BOOLEAN *)Value = mCfgPcdBufStructAddress.BufPcdAgesaTestPointEnableSmm;
      break;

    case  EnumPcdAgesaTestPointWidth:
      ASSERT (ValueSize == sizeof(UINT8));
      *(UINT8 *)Value   = mCfgPcdBufStructAddress.BufPcdAgesaTestPointWidth;
      break;

    case  EnumPcdIdsDebugPort:
      ASSERT (ValueSize == sizeof(UINT16));
      *(UINT16 *)Value  = mCfgPcdBufStructAddress.BufPcdIdsDebugPort;
      break;

    case  EnumPcdAgesaAssertEnable:
      ASSERT (ValueSize == sizeof(BOOLEAN));
      *(BOOLEAN *)Value = mCfgPcdBufStructAddress.BufPcdAgesaAssertEnable;
      break;

    case  EnumPcdAgesaTestPointToStb:
      ASSERT (ValueSize == sizeof(BOOLEAN));
      *(BOOLEAN *)Value = mCfgPcdBufStructAddress.BufPcdAgesaTestPointToStb;
      break;

    case  EnumPcdStbEnable:
      ASSERT (ValueSize == sizeof(BOOLEAN));
      *(BOOLEAN *)Value = mCfgPcdBufStructAddress.BufPcdStbEnable;
      break;

    case  EnumPcdGlobalStbVerbosityControl:
      ASSERT (ValueSize == sizeof(UINT8));
      *(UINT8 *)Value   = mCfgPcdBufStructAddress.BufPcdGlobalStbVerbosityControl;
      break;

    case  EnumPcdStbBiosVerbosityControl:
      ASSERT (ValueSize == sizeof(UINT8));
      *(UINT8 *)Value   = mCfgPcdBufStructAddress.BufPcdStbBiosVerbosityControl;
      break;

    case  EnumPcdStbSmuVerbosityControl:
      ASSERT (ValueSize == sizeof(UINT8));
      *(UINT8 *)Value   = mCfgPcdBufStructAddress.BufPcdStbSmuVerbosityControl;
      break;

    case  EnumPcdStbPspVerbosityControl:
      ASSERT (ValueSize == sizeof(UINT8));
      *(UINT8 *)Value   = mCfgPcdBufStructAddress.BufPcdStbPspVerbosityControl;
      break;

    case  EnumPcdStbSmnAddress:
      ASSERT (ValueSize == sizeof(UINT32));
      *(UINT32 *)Value  = mCfgPcdBufStructAddress.BufPcdStbSmnAddress;
      break;

    case  EnumPcdStbFilterMaskEnable:
      ASSERT (ValueSize == sizeof(BOOLEAN));
      *(BOOLEAN *)Value = mCfgPcdBufStructAddress.BufPcdStbFilterMaskEnable;
      break;

    case  EnumPcdMpPostcodeIp0SmnAddress:
      ASSERT (ValueSize == sizeof(UINT32));
      *(UINT32 *)Value = mCfgPcdBufStructAddress.BufPcdMpPostcodeIp0SmnAddress;
      break;

    case  EnumPcdMpPostcodeConfigSmnAddress:
      ASSERT (ValueSize == sizeof(UINT32));
      *(UINT32 *)Value = mCfgPcdBufStructAddress.BufPcdMpPostcodeConfigSmnAddress;
      break;

    case  EnumPcdStbIbvSourceId:
      ASSERT (ValueSize == sizeof(UINT8));
      *(UINT8 *)Value  = mCfgPcdBufStructAddress.BufPcdStbIbvSourceId;
      break;

    case  EnumPcdStbMpioVerbosityControl:
      ASSERT (ValueSize == sizeof(UINT8));
      *(UINT8 *)Value   = mCfgPcdBufStructAddress.BufPcdStbMpioVerbosityControl;
      break;

    default:
      ASSERT (0);
      break;
  }

  return Status;
}


/**
 * @brief Get BOOLEAN Pcd Value
 * @details Get BOOLEAN Pcd Value from PcdGetxx in Pei Phase.
 *          Get BOOLEAN Pcd Value from Buffer in Dxe/Smm Phase.
 * @param[in]       CFG_PCD_BUF_ENUM  Enum Value
 *
 * @return BOOLEAN  Return BOOLEAN Value
 */
BOOLEAN
AmdCfgPcdBufGetBool (
 CFG_PCD_BUF_ENUM CfgPcdBufEnum
)
{
  BOOLEAN Value;

  AmdCfgPcdBufGetVariable (
    CfgPcdBufEnum,
    (VOID *) &Value,
    (UINTN) sizeof (BOOLEAN)
    );

#if AMD_CFG_PCD_BUF_DUMP
  IDS_HDT_CONSOLE (MAIN_FLOW, "\t|- [Dxe] Get Bool, Enum [0x%02x] = 0x%08x\n", CfgPcdBufEnum, Value);
#endif

  return (BOOLEAN) Value;
}


/**
 * @brief Get UINT8 Pcd Value
 * @details Get UINT8 Pcd Value from PcdGetxx in Pei Phase.
 *          Get UINT8 Pcd Value from Buffer in Dxe/Smm Phase.
 * @param[in]       CFG_PCD_BUF_ENUM  Enum Value
 *
 * @return UINT8    Return UINT8 Value
 */
UINT8
AmdCfgPcdBufGet8 (
 CFG_PCD_BUF_ENUM CfgPcdBufEnum
)
{
  UINT8 Value;

  AmdCfgPcdBufGetVariable (
    CfgPcdBufEnum,
    (VOID *) &Value,
    (UINTN) sizeof (UINT8)
    );

#if AMD_CFG_PCD_BUF_DUMP
  IDS_HDT_CONSOLE (MAIN_FLOW, "\t|- [Dxe] Get 8,    Enum [0x%02x] = 0x%08x\n", CfgPcdBufEnum, Value);
#endif

  return (UINT8) Value;
}


/**
 * @brief Get UINT16 Pcd Value
 * @details Get UINT16 Pcd Value from PcdGetxx in Pei Phase.
 *          Get UINT16 Pcd Value from Buffer in Dxe/Smm Phase.
 * @param[in]       CFG_PCD_BUF_ENUM  Enum Value
 *
 * @return UINT16   Return UINT16 Value
 */
UINT16
AmdCfgPcdBufGet16 (
 CFG_PCD_BUF_ENUM CfgPcdBufEnum
)
{
  UINT16 Value;

  AmdCfgPcdBufGetVariable (
    CfgPcdBufEnum,
    (VOID *) &Value,
    (UINTN) sizeof (UINT16)
    );

#if AMD_CFG_PCD_BUF_DUMP
  IDS_HDT_CONSOLE (MAIN_FLOW, "\t|- [Dxe] Get 16,   Enum [0x%02x] = 0x%08x\n", CfgPcdBufEnum, Value);
#endif

  return (UINT16) Value;
}


/**
 * @brief Get UINT32 Pcd Value
 * @details Get UINT32 Pcd Value from PcdGetxx in Pei Phase.
 *          Get UINT32 Pcd Value from Buffer in Dxe/Smm Phase.
 * @param[in]       CFG_PCD_BUF_ENUM  Enum Value
 *
 * @return UINT32   Return UINT32 Value
 */
UINT32
AmdCfgPcdBufGet32 (
 CFG_PCD_BUF_ENUM CfgPcdBufEnum
)
{
  UINT32 Value;

  AmdCfgPcdBufGetVariable (
    CfgPcdBufEnum,
    (VOID *) &Value,
    (UINTN) sizeof (UINT32)
    );

#if AMD_CFG_PCD_BUF_DUMP
  IDS_HDT_CONSOLE (MAIN_FLOW, "\t|- [Dxe] Get 32,   Enum [0x%02x] = 0x%08x\n", CfgPcdBufEnum, Value);
#endif

  return (UINT32) Value;
}

