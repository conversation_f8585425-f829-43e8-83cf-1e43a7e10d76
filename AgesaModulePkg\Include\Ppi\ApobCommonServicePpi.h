/*****************************************************************************
 * Copyright (C) 2008-2024 Advanced Micro Devices, Inc. All rights reserved.
 *
*****************************************************************************
*/
/* $NoKeywords:$ */
/**
 * @file
 *
 * APOB common service PPI prototype definition
 *
 *
 * @xrefitem bom "File Content Label" "Release Content"
 * @e project:      AGESA
 * @e sub-project:  PSP
 * @e \$Revision: 309090 $   @e \$Date: 2014-12-10 02:28:05 +0800 (Wed, 10 Dec 2014) $
 */
#ifndef _APOB_COMMONSERVICE_PPI_H_
#define _APOB_COMMONSERVICE_PPI_H_

#include "ApobCommonServicePub.h"
#include <Addendum/Apcb/Inc/APOBCMN.h>

/**
 * @brief Get the APCB revocery flag
 *
 * @param[in,out] ApcbRecoveryFlag               The bool value of the APCB revocvery flag
 *
 */
typedef
EFI_STATUS
(*FP_GET_APCB_RECOVERY_FLAG)(
  IN OUT   BOOLEAN  *ApcbRecoveryFlag
  );

/**
 * @brief Get the DIMMCFG updated flag
 *
 * @param[in,out] DimmCfgUpdatedFlag               The bool value of the DIMMCFG updataed flag
 *
 */
typedef
EFI_STATUS
(*FP_GET_DIMMCFG_UPDATED_FLAG) (
  IN OUT   BOOLEAN  *DimmCfgUpdatedFlag
  );

/**
 * @brief Get the APCB instance
 *
 * @param[in,out] ApcbInstance               Point to the APCB instance
 */
typedef
EFI_STATUS
(*FP_GET_APCB_INSTANCE) (
  IN OUT   UINT8  *ApcbInstance
  );

/**
 * @brief Get the PHYS CDD number
 *
 * @param[in]     ApobInstanceId               Index of APOB instance
 * @param[in]     CcdIndex                     Index of CDD
 * @param[in,out] PhysCcdNumber                Number of phys ccd
 *
 */
typedef
EFI_STATUS
(*FP_GET_PHYS_CCD_NUMBER) (
  IN       UINT32 ApobInstanceId,
  IN       UINT32 CcdIndex,
  IN OUT   UINT8 *PhysCcdNumber
);

/**
 * @brief Get the PHYS complex number
 *
 * @param[in]     ApobInstanceId               Index of APOB instance
 * @param[in]     CcdIndex                     Index of CDD
 * @param[in]     CcxIndex                     Index of CCX
 * @param[in,out] PhysComplexNumber            Number of phys complex number
 *
 */
typedef
EFI_STATUS
(*FP_GET_PHYS_COMPLEX_NUMBER) (
  IN       UINT32 ApobInstanceId,
  IN       UINT32 CcdIndex,
  IN       UINT32 CcxIndex,
  IN OUT   UINT8 *PhysComplexNumber
);

/**
 * @brief Get the PHY core number
 *
 * @param[in]     ApobInstanceId               Index of APOB instance
 * @param[in]     CcdIndex                     Index of CDD
 * @param[in]     CcxIndex                     Index of CCX
 * @param[in]     CoreIndex                    Index of core
 * @param[in,out] PhysCoreNumber               Physical core number
 *
 */
typedef
EFI_STATUS
(*FP_GET_PHYSCORE_NUMBER) (
  IN       UINT32 ApobInstanceId,
  IN       UINT32 CcdIndex,
  IN       UINT32 CcxIndex,
  IN       UINT32 CoreIndex,
  IN OUT   UINT8  *PhysCoreNumber
);

/**
 * @brief Check if the thread enabled
 *
 * @param[in]     ApobInstanceId               Index of APOB instance
 * @param[in]     CcdIndex                     Index of CDD
 * @param[in]     CcxIndex                     Index of CCX
 * @param[in]     CoreIndex                    Index of core
 * @param[in]     ThreadIndex                  Index of thread
 * @param[in,out] IsThreadEnabled              Boolean value
 *
 */
typedef
EFI_STATUS
(*FP_GET_IS_THREAD_ENABLED) (
  IN       UINT32 ApobInstanceId,
  IN       UINT32 CcdIndex,
  IN       UINT32 CcxIndex,
  IN       UINT32 CoreIndex,
  IN       UINT32 ThreadIndex,
  IN OUT   BOOLEAN *IsThreadEnabled
);

/**
 * @brief Check if the ECC enabled
 *
 * @param[in]     ApobInstanceId               Index of APOB instance
 * @param[in]     ChannelIndex                 Index of channel
 * @param[in,out] EccEnable                    Boolean value
 *
 */
typedef
EFI_STATUS
(*FP_GET_ECC_ENABLE) (
  IN       UINT32 ApobInstanceId,
  IN       UINT32 ChannelIndex,
  IN OUT   BOOLEAN *EccEnable
);

/**
 * @brief check if the NVDIMM is present in system
 *
 * @param[in]     ApobInstanceId               Index of APOB instance
 * @param[in,out] NvdimmPresentInSystem        Boolean value
 *
 */
typedef
EFI_STATUS
(*FP_GET_NVDIMM_PRESENT_IN_SYSTEM) (
  IN       UINT32 ApobInstanceId,
  IN OUT   BOOLEAN *NvdimmPresentInSystem
);

/**
 * @brief Get NVDIMM information
 *
 * @param[in]     ApobInstanceId               Index of APOB instance
 * @param[in,out] NvdimmInfo                   Point to the Nvdimm information
 * @param[in,out] NvdimmInfoSize               Point to the size of the Nvdimm information
 *
 */
typedef
EFI_STATUS
(*FP_GET_NVDIMM_INFO) (
  IN       UINT32 ApobInstanceId,
  IN OUT   UINT32 **NvdimmInfo,
  IN OUT   UINT32 *NvdimmInfoSize
);

/**
 * @brief Get max DIMMS per channel
 *
 * @param[in]     ApobInstanceId               Index of APOB instance
 * @param[in,out] MaxDimmsPerChannel           Point to the max DIMMS number per channel
 *
 */
typedef
EFI_STATUS
(*FP_GET_MAX_DIMMS_PER_CHANNEL) (
  IN       UINT32 ApobInstanceId,
  IN OUT   UINT8 *MaxDimmsPerChannel
);

/**
 * @brief Get max channels per socket
 *
 * @param[in]     ApobInstanceId               Index of APOB instance
 * @param[in,out] MaxChannelsPerSocket         Point to the max channels number per socket
 *
 */
typedef
EFI_STATUS
(*FP_GET_MAX_CHANNELS_PER_SOCKET) (
  IN       UINT32 ApobInstanceId,
  IN OUT   UINT8 *MaxChannelsPerSocket
);

/**
 * @brief Get max channels per die
 *
 * @param[in]     ApobInstanceId               Index of APOB instance
 * @param[in,out] MaxChannelsPerDie            Point to the max channels number per die
 *
 */
typedef
EFI_STATUS
(*FP_GET_MAX_CHANNELS_PER_DIE) (
  IN       UINT32 ApobInstanceId,
  IN OUT   UINT8 *MaxChannelsPerDie
);

/**
 * @brief Get DIMM smbus information
 *
 * @param[in]     ApobInstanceId               Index of APOB instance
 * @param[in]     Index                        Index to visit
 * @param[in,out] DimmSmbusInfo                Point to the dimm smbus information
 * @param[in,out] BufferSize                   Buffer size
 *
 */
typedef
EFI_STATUS
(*FP_GET_DIMM_SMBUS_INFO) (
  IN       UINT32 ApobInstanceId,
  IN       UINT32 Index,
  IN OUT   VOID **DimmSmbusInfo,
  IN OUT   UINTN  *BufferSize
);

/**
 * @brief Get memory frequency
 *
 * @param[in]     ApobInstanceId               Index of APOB instance
 * @param[in,out] AmdMemoryFrequency           Point to the memory frequency
 *
 */
typedef
EFI_STATUS
(*FP_GET_MEMORY_FREQUENCY) (
  IN       UINT32 ApobInstanceId,
  IN OUT   UINT16 *AmdMemoryFrequency
);

 /**
 * @brief Get ddr max rate
 *
 * @param[in]     ApobInstanceId               Index of APOB instance
 * @param[in,out] DdrMaxRate                   Point to the memory frequency
 *
 */
typedef
EFI_STATUS
(*FP_GET_DDR_MAX_RATE) (
  IN       UINT32 ApobInstanceId,
  IN OUT   UINT16 *DdrMaxRate
);

/**
 * @brief Get memory gen information element
 *
 * @param[in]     ApobInstanceId               Index of APOB instance
 * @param[in]     ElementId                    Index of element
 * @param[in,out] ElementSizeInByte            Point to the element size
 * @param[in,out] ElementValue                 Point to the elemet value
 *
 */
typedef
EFI_STATUS
(*FP_GET_MEM_GEN_INFO_ELEMENT) (
  IN       UINT32 ApobInstanceId,
  IN       UINT32 ElementId,
  IN OUT   UINT16 *ElementSizeInByte,
  IN OUT   UINT64 *ElementValue
  );

/**
 * @brief Get the number of valid threshold
 *
 * @param[in]   ApobInstanceId               Index of APOB instance
 * @param[in]   NumberOfValidThresholds      Point to the number of valid threshold
 *
 */
typedef
EFI_STATUS
(*FP_GET_NUMBER_OF_VALID_THRESHOLDS) (
  IN       UINT32 ApobInstanceId,
  IN       UINT32 *NumberOfValidThresholds
  );

/**
 * @brief Get the threshold
 *
 * @param[in]   ApobInstanceId               Index of APOB instance
 * @param[in]   Index                        Index to visit
 * @param[in]   Thresholds                   Point to the threshold
 *
 */
typedef
EFI_STATUS
(*FP_GET_THRESHOLDS) (
  IN       UINT32 ApobInstanceId,
  IN       UINT32 Index,
  IN       EDC_THROTTLE_THRESHOLD **Thresholds
  );

/**
 * @brief Get the boardmask
 *
 * @param[in]   ApobInstanceId               Index of APOB instance
 * @param[in]   BoardMask                    Point to the BoardMask
 *
 */
typedef
EFI_STATUS
(*FP_GET_BOARDMASK) (
  IN       UINT32 ApobInstanceId,
  IN       UINT16 *BoardMask
  );

/**
 * @brief Get the subprogram
 *
 * @param[in,out] SubProgram                    Point to the SubProgram
 *
 */
typedef
EFI_STATUS
(*FP_GET_SUBPROGRAM) (
  OUT      UINT32 *SubProgram
  );


/**
 * @brief Get dimm spd data
 *
 * @param[in]     ApobInstanceId               Index of APOB instance
 * @param[in]     Socket                       Index of socket
 * @param[in]     Channel                      Index of channel
 * @param[in]     Dimm                         Index of dimm
 * @param[in]     BufSize                      Buffer size
 * @param[in,out] SpdBufPtr                    Point to the spd buffer
 *
 */
typedef
EFI_STATUS
(*FP_GET_DIMM_SPD_DATA) (
  IN       UINT32 ApobInstanceId,
  IN       UINT8  Socket,
  IN       UINT8  Channel,
  IN       UINT8  Dimm,
  IN       UINT32 BufSize,
  IN OUT   UINT8  *SpdBufPtr
  );
///
/// PPI prototype
///
/// Defines APOB_COMMON_SERVICE_PPI, which public the common APOB service across all programs
///
typedef struct _APOB_COMMON_SERVICE_PPI {
  FP_GET_APCB_INSTANCE                    ApobGetApcbInstance;            ///< Get APCB Instance
  FP_GET_APCB_RECOVERY_FLAG               ApobGetApcbRecoveryFlag;        ///< Get APCB Recovery Flag
  FP_GET_DIMMCFG_UPDATED_FLAG             ApobGetDimmCfgUpdatedFlag;      ///< Return DimmCfgUpdatedFlag
  FP_GET_PHYS_CCD_NUMBER                  ApobGetPhysCcdNumber;           ///< Get Phys CCD Number
  FP_GET_PHYS_COMPLEX_NUMBER              ApobGetPhysComplexNumber;       ///< Get Phys Complex Number
  FP_GET_PHYSCORE_NUMBER                  ApobGetPhysCoreNumber;          ///< Get Phys Core Number
  FP_GET_IS_THREAD_ENABLED                ApobGetIsThreadEnabled;         ///< Check if thread is enabled
  FP_GET_ECC_ENABLE                       ApobGetEccEnable;               ///< Check if ECC is enabled
  FP_GET_NVDIMM_PRESENT_IN_SYSTEM         ApobGetNvdimmPresentInSystem;   ///< Check if NVDIMM is present in system
  FP_GET_NVDIMM_INFO                      ApobGetNvdimmInfo;              ///< Get NVDIMM information
  FP_GET_MAX_DIMMS_PER_CHANNEL            ApobGetMaxDimmsPerChannel;      ///< Get the max number of DIMM per channel
  FP_GET_MAX_CHANNELS_PER_DIE             ApobGetMaxChannelsPerDie;       ///< Get the max number of channel per die
  FP_GET_MAX_CHANNELS_PER_SOCKET          ApobGetMaxChannelsPerSocket;    ///< Get the max number of channel per socket
  FP_GET_DIMM_SMBUS_INFO                  ApobGetDimmSmbusInfo;           ///< Get DIMM SMbus information
  FP_GET_MEMORY_FREQUENCY                 ApobGetMemoryFrequency;         ///< Get memory frequency
  FP_GET_DDR_MAX_RATE                     ApobGetDdrMaxRate;              ///< Get memory max rate
  FP_GET_MEM_GEN_INFO_ELEMENT             ApobGetMemGenInfoElement;       ///< Get memory gen information element
  FP_GET_NUMBER_OF_VALID_THRESHOLDS       ApobGetNumberOfValidThresholds; ///< Get number of valid threshold
  FP_GET_THRESHOLDS                       ApobGetThresholds;              ///< Get threshold
  FP_GET_BOARDMASK                        ApobGetBoardMask;               ///< Get boardmask
  FP_GET_SUBPROGRAM                       ApobGetSubProgram;              ///< Get subprogram
  FP_GET_DIMM_SPD_DATA                    ApobGetDimmSpdData;             ///< Get DIMM SPD data
} APOB_COMMON_SERVICE_PPI;

extern EFI_GUID gApobCommonServicePpiGuid;  ///< Apob Common Service Ppi Guid

#endif //_APOB_COMMONSERVICE_PPI_H_



