/*****************************************************************************
 *
 * Copyright (C) 2016-2024 Advanced Micro Devices, Inc. All rights reserved.
 *
 *******************************************************************************
 */
#ifndef _AMD_NBIO_IOMMU_FEATURE_PPI_H_
#define _AMD_NBIO_IOMMU_FEATURE_PPI_H_

// Current PPI revision
#define AMD_NBIO_IOMMU_FEATURE_REVISION   0x00

typedef struct {
    UINT32                Iommu_Sup:1;
    UINT32                XT_Sup:1;
    UINT32                UNUSED:30;
} IOMMU_FEATURE_INFO;

typedef struct _PEI_AMD_NBIO_IOMMU_FEATURE_PPI PEI_AMD_NBIO_IOMMU_FEATURE_PPI;

//
// PPI prototype
//
/**
  Reads a fuse value based on an enumerated list of fuse "names"

  Parameters:
  This
    A pointer to the PEI_AMD_NBIO_IOMMU_FEATURE_PPI instance.
  IommuFeatureInfo
    Pointer to memory that will contain the value of the Iommu features.

  Status Codes Returned:
  EFI_SUCCESS           - The fuse was located and the FuseValue returned is valid
  EFI_INVALID_PARAMETER - One of the input parameters was invalid
                        - InstanceId did not reference a valid NBIO instance
                        - FuseName was not found in the list of supported fuse identifier values
  EFI_UNSUPPORTED       - This function is not supported by this version of the driver
**/
typedef
EFI_STATUS
(EFIAPI *AMD_IOMMU_FEATURE_GET_INFO) (
  IN       PEI_AMD_NBIO_IOMMU_FEATURE_PPI  *This,
  OUT      IOMMU_FEATURE_INFO              *IommuFeatureInfo
  );

///
/// The Ppi of IOMMU Feature
///
struct _PEI_AMD_NBIO_IOMMU_FEATURE_PPI {
  UINT32                                     Revision;                ///< revision
  AMD_IOMMU_FEATURE_GET_INFO                 IommuFeatureGetInfo;     ///<
};

extern EFI_GUID gAmdNbioIommuFeaturePpiGuid ;

#endif //



