/*****************************************************************************
 *
 * Copyright (C) 2008-2024 Advanced Micro Devices, Inc. All rights reserved.
 *
 *******************************************************************************
 **/

#ifndef _NTB_H_
#define _NTB_H_


/***********************************************************
* Register Name : NTB_SMU_CLKOFFCTRL
************************************************************/

#define NTB_SMU_CLKOFFCTRL_CSRCLKOFF_EN_OFFSET                 0
#define NTB_SMU_CLKOFFCTRL_CSRCLKOFF_EN_MASK                   0x1

#define NTB_SMU_CLKOFFCTRL_CORECLKOFF_EN_OFFSET                1
#define NTB_SMU_CLKOFFCTRL_CORECLKOFF_EN_MASK                  0x2

#define NTB_SMU_CLKOFFCTRL_Reserved_7_2_OFFSET                 2
#define NTB_SMU_CLKOFFCTRL_Reserved_7_2_MASK                   0xfc

#define NTB_SMU_CLKOFFCTRL_CSRCLKOFF_HYST_OFFSET               8
#define NTB_SMU_CLKOFFCTRL_CSRCLKOFF_HYST_MASK                 0xff00

#define NTB_SMU_CLKOFFCTRL_CORECLKOFF_HYST_OFFSET              16
#define NTB_SMU_CLKOFFCTRL_CORECLKOFF_HYST_MASK                0xff0000

#define NTB_SMU_CLKOFFCTRL_Reserved_31_24_OFFSET               24
#define NTB_SMU_CLKOFFCTRL_Reserved_31_24_MASK                 0xff000000

typedef union {
  struct {
    UINT32                                        CSRCLKOFF_EN:1;
    UINT32                                       CORECLKOFF_EN:1;
    UINT32                                        Reserved_7_2:6;
    UINT32                                      CSRCLKOFF_HYST:8;
    UINT32                                     CORECLKOFF_HYST:8;
    UINT32                                      Reserved_31_24:8;
  } Field;
  UINT32 Value;
} NTB_SMU_CLKOFFCTRL_STRUCT;

#define SMN_NTB_SMU_CLKOFFCTRL_ADDRESS                                0x4000078UL
#define SMN_NTB0NBIO0_NTB_SMU_CLKOFFCTRL_ADDRESS                      0x4000078UL
#define SMN_NTB0NBIO1_NTB_SMU_CLKOFFCTRL_ADDRESS                      0x4200078UL
#define SMN_NTB1NBIO0_NTB_SMU_CLKOFFCTRL_ADDRESS                      0x4100078UL
#define SMN_NTB1NBIO1_NTB_SMU_CLKOFFCTRL_ADDRESS                      0x4300078UL


/***********************************************************
* Register Name : NTB_SMU_LCLKCTL_OVERRIDE
************************************************************/

#define NTB_SMU_LCLKCTL_OVERRIDE_LCLKCTL_OVERRIDE_VAL_OFFSET   0
#define NTB_SMU_LCLKCTL_OVERRIDE_LCLKCTL_OVERRIDE_VAL_MASK     0x1

#define NTB_SMU_LCLKCTL_OVERRIDE_LCLKCTL_OVERRIDE_EN_OFFSET    1
#define NTB_SMU_LCLKCTL_OVERRIDE_LCLKCTL_OVERRIDE_EN_MASK      0x2

#define NTB_SMU_LCLKCTL_OVERRIDE_Reserved_31_2_OFFSET          2
#define NTB_SMU_LCLKCTL_OVERRIDE_Reserved_31_2_MASK            0xfffffffc

typedef union {
  struct {
    UINT32                                LCLKCTL_OVERRIDE_VAL:1;
    UINT32                                 LCLKCTL_OVERRIDE_EN:1;
    UINT32                                       Reserved_31_2:30;
  } Field;
  UINT32 Value;
} NTB_SMU_LCLKCTL_OVERRIDE_STRUCT;

#define SMN_NTB_SMU_LCLKCTL_OVERRIDE_ADDRESS                          0x400007cUL
#define SMN_NTB0NBIO0_NTB_SMU_LCLKCTL_OVERRIDE_ADDRESS                0x400007cUL
#define SMN_NTB0NBIO1_NTB_SMU_LCLKCTL_OVERRIDE_ADDRESS                0x420007cUL
#define SMN_NTB1NBIO0_NTB_SMU_LCLKCTL_OVERRIDE_ADDRESS                0x410007cUL
#define SMN_NTB1NBIO1_NTB_SMU_LCLKCTL_OVERRIDE_ADDRESS                0x430007cUL


/***********************************************************
* Register Name : NTB_SMU_PCTRL0
************************************************************/

#define NTB_SMU_PCTRL0_Reserved_0_0_OFFSET                     0
#define NTB_SMU_PCTRL0_Reserved_0_0_MASK                       0x1

#define NTB_SMU_PCTRL0_PRESET_NOTICE_OFFSET                    1
#define NTB_SMU_PCTRL0_PRESET_NOTICE_MASK                      0x2

#define NTB_SMU_PCTRL0_PD3_NOTICE_OFFSET                       2
#define NTB_SMU_PCTRL0_PD3_NOTICE_MASK                         0x4

#define NTB_SMU_PCTRL0_PPMETO_NOTICE_OFFSET                    3
#define NTB_SMU_PCTRL0_PPMETO_NOTICE_MASK                      0x8

#define NTB_SMU_PCTRL0_PD0_NOTICE_OFFSET                       4
#define NTB_SMU_PCTRL0_PD0_NOTICE_MASK                         0x10

#define NTB_SMU_PCTRL0_PMISC0_NOTICE_OFFSET                    5
#define NTB_SMU_PCTRL0_PMISC0_NOTICE_MASK                      0xffe0

#define NTB_SMU_PCTRL0_PMISC1_NOTICE_OFFSET                    16
#define NTB_SMU_PCTRL0_PMISC1_NOTICE_MASK                      0xff0000

#define NTB_SMU_PCTRL0_Reserved_31_24_OFFSET                   24
#define NTB_SMU_PCTRL0_Reserved_31_24_MASK                     0xff000000

typedef union {
  struct {
    UINT32                                        Reserved_0_0:1;
    UINT32                                       PRESET_NOTICE:1;
    UINT32                                          PD3_NOTICE:1;
    UINT32                                       PPMETO_NOTICE:1;
    UINT32                                          PD0_NOTICE:1;
    UINT32                                       PMISC0_NOTICE:11;
    UINT32                                       PMISC1_NOTICE:8;
    UINT32                                      Reserved_31_24:8;
  } Field;
  UINT32 Value;
} NTB_SMU_PCTRL0_STRUCT;

#define SMN_NTB_SMU_PCTRL0_ADDRESS                                    0x4000000UL
#define SMN_NTB0NBIO0_NTB_SMU_PCTRL0_ADDRESS                          0x4000000UL
#define SMN_NTB0NBIO1_NTB_SMU_PCTRL0_ADDRESS                          0x4200000UL
#define SMN_NTB1NBIO0_NTB_SMU_PCTRL0_ADDRESS                          0x4100000UL
#define SMN_NTB1NBIO1_NTB_SMU_PCTRL0_ADDRESS                          0x4300000UL


/***********************************************************
* Register Name : NTB_SMU_PINTMASK
************************************************************/

#define NTB_SMU_PINTMASK_PLTR_UPDATE_INTMASK_OFFSET            0
#define NTB_SMU_PINTMASK_PLTR_UPDATE_INTMASK_MASK              0x1

#define NTB_SMU_PINTMASK_PRESET_ACK_INTMASK_OFFSET             1
#define NTB_SMU_PINTMASK_PRESET_ACK_INTMASK_MASK               0x2

#define NTB_SMU_PINTMASK_PD3_ACK_INTMASK_OFFSET                2
#define NTB_SMU_PINTMASK_PD3_ACK_INTMASK_MASK                  0x4

#define NTB_SMU_PINTMASK_PPMETO_ACK_INTMASK_OFFSET             3
#define NTB_SMU_PINTMASK_PPMETO_ACK_INTMASK_MASK               0x8

#define NTB_SMU_PINTMASK_PD0_ACK_INTMASK_OFFSET                4
#define NTB_SMU_PINTMASK_PD0_ACK_INTMASK_MASK                  0x10

#define NTB_SMU_PINTMASK_PMISC0_ACK_INTMASK_OFFSET             5
#define NTB_SMU_PINTMASK_PMISC0_ACK_INTMASK_MASK               0xffe0

#define NTB_SMU_PINTMASK_PMISC1_ACK_INTMASK_OFFSET             16
#define NTB_SMU_PINTMASK_PMISC1_ACK_INTMASK_MASK               0xff0000

#define NTB_SMU_PINTMASK_Reserved_31_24_OFFSET                 24
#define NTB_SMU_PINTMASK_Reserved_31_24_MASK                   0xff000000

typedef union {
  struct {
    UINT32                                 PLTR_UPDATE_INTMASK:1;
    UINT32                                  PRESET_ACK_INTMASK:1;
    UINT32                                     PD3_ACK_INTMASK:1;
    UINT32                                  PPMETO_ACK_INTMASK:1;
    UINT32                                     PD0_ACK_INTMASK:1;
    UINT32                                  PMISC0_ACK_INTMASK:11;
    UINT32                                  PMISC1_ACK_INTMASK:8;
    UINT32                                      Reserved_31_24:8;
  } Field;
  UINT32 Value;
} NTB_SMU_PINTMASK_STRUCT;

#define SMN_NTB_SMU_PINTMASK_ADDRESS                                  0x4000008UL
#define SMN_NTB0NBIO0_NTB_SMU_PINTMASK_ADDRESS                        0x4000008UL
#define SMN_NTB0NBIO1_NTB_SMU_PINTMASK_ADDRESS                        0x4200008UL
#define SMN_NTB1NBIO0_NTB_SMU_PINTMASK_ADDRESS                        0x4100008UL
#define SMN_NTB1NBIO1_NTB_SMU_PINTMASK_ADDRESS                        0x4300008UL


/***********************************************************
* Register Name : NTB_SMU_PINTSTA
************************************************************/

#define NTB_SMU_PINTSTA_PLTR_UPDATE_INTSTA_OFFSET              0
#define NTB_SMU_PINTSTA_PLTR_UPDATE_INTSTA_MASK                0x1

#define NTB_SMU_PINTSTA_PRESET_ACK_INTSTA_OFFSET               1
#define NTB_SMU_PINTSTA_PRESET_ACK_INTSTA_MASK                 0x2

#define NTB_SMU_PINTSTA_PD3_ACK_INTSTA_OFFSET                  2
#define NTB_SMU_PINTSTA_PD3_ACK_INTSTA_MASK                    0x4

#define NTB_SMU_PINTSTA_PPMETO_ACK_INTSTA_OFFSET               3
#define NTB_SMU_PINTSTA_PPMETO_ACK_INTSTA_MASK                 0x8

#define NTB_SMU_PINTSTA_PD0_ACK_INTSTA_OFFSET                  4
#define NTB_SMU_PINTSTA_PD0_ACK_INTSTA_MASK                    0x10

#define NTB_SMU_PINTSTA_PMISC0_ACK_INTSTA_OFFSET               5
#define NTB_SMU_PINTSTA_PMISC0_ACK_INTSTA_MASK                 0xffe0

#define NTB_SMU_PINTSTA_PMISC1_ACK_INTSTA_OFFSET               16
#define NTB_SMU_PINTSTA_PMISC1_ACK_INTSTA_MASK                 0xff0000

#define NTB_SMU_PINTSTA_Reserved_31_24_OFFSET                  24
#define NTB_SMU_PINTSTA_Reserved_31_24_MASK                    0xff000000

typedef union {
  struct {
    UINT32                                  PLTR_UPDATE_INTSTA:1;
    UINT32                                   PRESET_ACK_INTSTA:1;
    UINT32                                      PD3_ACK_INTSTA:1;
    UINT32                                   PPMETO_ACK_INTSTA:1;
    UINT32                                      PD0_ACK_INTSTA:1;
    UINT32                                   PMISC0_ACK_INTSTA:11;
    UINT32                                   PMISC1_ACK_INTSTA:8;
    UINT32                                      Reserved_31_24:8;
  } Field;
  UINT32 Value;
} NTB_SMU_PINTSTA_STRUCT;

#define SMN_NTB_SMU_PINTSTA_ADDRESS                                   0x400000cUL
#define SMN_NTB0NBIO0_NTB_SMU_PINTSTA_ADDRESS                         0x400000cUL
#define SMN_NTB0NBIO1_NTB_SMU_PINTSTA_ADDRESS                         0x420000cUL
#define SMN_NTB1NBIO0_NTB_SMU_PINTSTA_ADDRESS                         0x410000cUL
#define SMN_NTB1NBIO1_NTB_SMU_PINTSTA_ADDRESS                         0x430000cUL


/***********************************************************
* Register Name : NTB_SMU_PPOLLSTA
************************************************************/

#define NTB_SMU_PPOLLSTA_Reserved_0_0_OFFSET                   0
#define NTB_SMU_PPOLLSTA_Reserved_0_0_MASK                     0x1

#define NTB_SMU_PPOLLSTA_PRESET_ACK_POLLSTA_OFFSET             1
#define NTB_SMU_PPOLLSTA_PRESET_ACK_POLLSTA_MASK               0x2

#define NTB_SMU_PPOLLSTA_PD3_ACK_POLLSTA_OFFSET                2
#define NTB_SMU_PPOLLSTA_PD3_ACK_POLLSTA_MASK                  0x4

#define NTB_SMU_PPOLLSTA_PPMETO_ACK_POLLSTA_OFFSET             3
#define NTB_SMU_PPOLLSTA_PPMETO_ACK_POLLSTA_MASK               0x8

#define NTB_SMU_PPOLLSTA_PD0_ACK_POLLSTA_OFFSET                4
#define NTB_SMU_PPOLLSTA_PD0_ACK_POLLSTA_MASK                  0x10

#define NTB_SMU_PPOLLSTA_PMISC0_ACK_POLLSTA_OFFSET             5
#define NTB_SMU_PPOLLSTA_PMISC0_ACK_POLLSTA_MASK               0xffe0

#define NTB_SMU_PPOLLSTA_PMISC1_ACK_POLLSTA_OFFSET             16
#define NTB_SMU_PPOLLSTA_PMISC1_ACK_POLLSTA_MASK               0xff0000

#define NTB_SMU_PPOLLSTA_Reserved_31_24_OFFSET                 24
#define NTB_SMU_PPOLLSTA_Reserved_31_24_MASK                   0xff000000

typedef union {
  struct {
    UINT32                                        Reserved_0_0:1;
    UINT32                                  PRESET_ACK_POLLSTA:1;
    UINT32                                     PD3_ACK_POLLSTA:1;
    UINT32                                  PPMETO_ACK_POLLSTA:1;
    UINT32                                     PD0_ACK_POLLSTA:1;
    UINT32                                  PMISC0_ACK_POLLSTA:11;
    UINT32                                  PMISC1_ACK_POLLSTA:8;
    UINT32                                      Reserved_31_24:8;
  } Field;
  UINT32 Value;
} NTB_SMU_PPOLLSTA_STRUCT;

#define SMN_NTB_SMU_PPOLLSTA_ADDRESS                                  0x4000020UL
#define SMN_NTB0NBIO0_NTB_SMU_PPOLLSTA_ADDRESS                        0x4000020UL
#define SMN_NTB0NBIO1_NTB_SMU_PPOLLSTA_ADDRESS                        0x4200020UL
#define SMN_NTB1NBIO0_NTB_SMU_PPOLLSTA_ADDRESS                        0x4100020UL
#define SMN_NTB1NBIO1_NTB_SMU_PPOLLSTA_ADDRESS                        0x4300020UL


/***********************************************************
* Register Name : NTB_SMU_RCHAINCTRL
************************************************************/

#define NTB_SMU_RCHAINCTRL_PRCHAIN_IN_FORCE_OFFSET             0
#define NTB_SMU_RCHAINCTRL_PRCHAIN_IN_FORCE_MASK               0x1

#define NTB_SMU_RCHAINCTRL_SRCHAIN_IN_FORCE_OFFSET             1
#define NTB_SMU_RCHAINCTRL_SRCHAIN_IN_FORCE_MASK               0x2

#define NTB_SMU_RCHAINCTRL_RCHAIN_OUT_KEEP_VMOD_OFFSET         2
#define NTB_SMU_RCHAINCTRL_RCHAIN_OUT_KEEP_VMOD_MASK           0x4

#define NTB_SMU_RCHAINCTRL_Reserved_31_3_OFFSET                3
#define NTB_SMU_RCHAINCTRL_Reserved_31_3_MASK                  0xfffffff8

typedef union {
  struct {
    UINT32                                    PRCHAIN_IN_FORCE:1;
    UINT32                                    SRCHAIN_IN_FORCE:1;
    UINT32                                RCHAIN_OUT_KEEP_VMOD:1;
    UINT32                                       Reserved_31_3:29;
  } Field;
  UINT32 Value;
} NTB_SMU_RCHAINCTRL_STRUCT;

#define SMN_NTB_SMU_RCHAINCTRL_ADDRESS                                0x4000074UL
#define SMN_NTB0NBIO0_NTB_SMU_RCHAINCTRL_ADDRESS                      0x4000074UL
#define SMN_NTB0NBIO1_NTB_SMU_RCHAINCTRL_ADDRESS                      0x4200074UL
#define SMN_NTB1NBIO0_NTB_SMU_RCHAINCTRL_ADDRESS                      0x4100074UL
#define SMN_NTB1NBIO1_NTB_SMU_RCHAINCTRL_ADDRESS                      0x4300074UL


/***********************************************************
* Register Name : NTB_SMU_RSVTAGNUM_VC1
************************************************************/

#define NTB_SMU_RSVTAGNUM_VC1_RSVTAGNUM_VC1_OFFSET             0
#define NTB_SMU_RSVTAGNUM_VC1_RSVTAGNUM_VC1_MASK               0xf

#define NTB_SMU_RSVTAGNUM_VC1_Reserved_31_4_OFFSET             4
#define NTB_SMU_RSVTAGNUM_VC1_Reserved_31_4_MASK               0xfffffff0

typedef union {
  struct {
    UINT32                                       RSVTAGNUM_VC1:4;
    UINT32                                       Reserved_31_4:28;
  } Field;
  UINT32 Value;
} NTB_SMU_RSVTAGNUM_VC1_STRUCT;

#define SMN_NTB_SMU_RSVTAGNUM_VC1_ADDRESS                             0x4000068UL
#define SMN_NTB0NBIO0_NTB_SMU_RSVTAGNUM_VC1_ADDRESS                   0x4000068UL
#define SMN_NTB0NBIO1_NTB_SMU_RSVTAGNUM_VC1_ADDRESS                   0x4200068UL
#define SMN_NTB1NBIO0_NTB_SMU_RSVTAGNUM_VC1_ADDRESS                   0x4100068UL
#define SMN_NTB1NBIO1_NTB_SMU_RSVTAGNUM_VC1_ADDRESS                   0x4300068UL


/***********************************************************
* Register Name : NTB_SMU_SCTRL0
************************************************************/

#define NTB_SMU_SCTRL0_Reserved_0_0_OFFSET                     0
#define NTB_SMU_SCTRL0_Reserved_0_0_MASK                       0x1

#define NTB_SMU_SCTRL0_SRESET_NOTICE_OFFSET                    1
#define NTB_SMU_SCTRL0_SRESET_NOTICE_MASK                      0x2

#define NTB_SMU_SCTRL0_SD3_NOTICE_OFFSET                       2
#define NTB_SMU_SCTRL0_SD3_NOTICE_MASK                         0x4

#define NTB_SMU_SCTRL0_SPMETO_NOTICE_OFFSET                    3
#define NTB_SMU_SCTRL0_SPMETO_NOTICE_MASK                      0x8

#define NTB_SMU_SCTRL0_SD0_NOTICE_OFFSET                       4
#define NTB_SMU_SCTRL0_SD0_NOTICE_MASK                         0x10

#define NTB_SMU_SCTRL0_SMISC0_NOTICE_OFFSET                    5
#define NTB_SMU_SCTRL0_SMISC0_NOTICE_MASK                      0xffe0

#define NTB_SMU_SCTRL0_SMISC1_NOTICE_OFFSET                    16
#define NTB_SMU_SCTRL0_SMISC1_NOTICE_MASK                      0xff0000

#define NTB_SMU_SCTRL0_Reserved_31_24_OFFSET                   24
#define NTB_SMU_SCTRL0_Reserved_31_24_MASK                     0xff000000

typedef union {
  struct {
    UINT32                                        Reserved_0_0:1;
    UINT32                                       SRESET_NOTICE:1;
    UINT32                                          SD3_NOTICE:1;
    UINT32                                       SPMETO_NOTICE:1;
    UINT32                                          SD0_NOTICE:1;
    UINT32                                       SMISC0_NOTICE:11;
    UINT32                                       SMISC1_NOTICE:8;
    UINT32                                      Reserved_31_24:8;
  } Field;
  UINT32 Value;
} NTB_SMU_SCTRL0_STRUCT;

#define SMN_NTB_SMU_SCTRL0_ADDRESS                                    0x4000010UL
#define SMN_NTB0NBIO0_NTB_SMU_SCTRL0_ADDRESS                          0x4000010UL
#define SMN_NTB0NBIO1_NTB_SMU_SCTRL0_ADDRESS                          0x4200010UL
#define SMN_NTB1NBIO0_NTB_SMU_SCTRL0_ADDRESS                          0x4100010UL
#define SMN_NTB1NBIO1_NTB_SMU_SCTRL0_ADDRESS                          0x4300010UL


/***********************************************************
* Register Name : NTB_SMU_SDPPORTEN
************************************************************/

#define NTB_SMU_SDPPORTEN_PRI_ORGEN_OFFSET                     0
#define NTB_SMU_SDPPORTEN_PRI_ORGEN_MASK                       0x1

#define NTB_SMU_SDPPORTEN_PRI_CMPEN_OFFSET                     1
#define NTB_SMU_SDPPORTEN_PRI_CMPEN_MASK                       0x2

#define NTB_SMU_SDPPORTEN_SEC_ORGEN_OFFSET                     2
#define NTB_SMU_SDPPORTEN_SEC_ORGEN_MASK                       0x4

#define NTB_SMU_SDPPORTEN_SEC_CMPEN_OFFSET                     3
#define NTB_SMU_SDPPORTEN_SEC_CMPEN_MASK                       0x8

#define NTB_SMU_SDPPORTEN_Reserved_31_4_OFFSET                 4
#define NTB_SMU_SDPPORTEN_Reserved_31_4_MASK                   0xfffffff0

typedef union {
  struct {
    UINT32                                           PRI_ORGEN:1;
    UINT32                                           PRI_CMPEN:1;
    UINT32                                           SEC_ORGEN:1;
    UINT32                                           SEC_CMPEN:1;
    UINT32                                       Reserved_31_4:28;
  } Field;
  UINT32 Value;
} NTB_SMU_SDPPORTEN_STRUCT;

#define SMN_NTB_SMU_SDPPORTEN_ADDRESS                                 0x400006cUL
#define SMN_NTB0NBIO0_NTB_SMU_SDPPORTEN_ADDRESS                       0x400006cUL
#define SMN_NTB0NBIO1_NTB_SMU_SDPPORTEN_ADDRESS                       0x420006cUL
#define SMN_NTB1NBIO0_NTB_SMU_SDPPORTEN_ADDRESS                       0x410006cUL
#define SMN_NTB1NBIO1_NTB_SMU_SDPPORTEN_ADDRESS                       0x430006cUL


/***********************************************************
* Register Name : NTB_SMU_SINTMASK
************************************************************/

#define NTB_SMU_SINTMASK_SLTR_UPDATE_INTMASK_OFFSET            0
#define NTB_SMU_SINTMASK_SLTR_UPDATE_INTMASK_MASK              0x1

#define NTB_SMU_SINTMASK_SRESET_ACK_INTMASK_OFFSET             1
#define NTB_SMU_SINTMASK_SRESET_ACK_INTMASK_MASK               0x2

#define NTB_SMU_SINTMASK_SD3_ACK_INTMASK_OFFSET                2
#define NTB_SMU_SINTMASK_SD3_ACK_INTMASK_MASK                  0x4

#define NTB_SMU_SINTMASK_SPMETO_ACK_INTMASK_OFFSET             3
#define NTB_SMU_SINTMASK_SPMETO_ACK_INTMASK_MASK               0x8

#define NTB_SMU_SINTMASK_SD0_ACK_INTMASK_OFFSET                4
#define NTB_SMU_SINTMASK_SD0_ACK_INTMASK_MASK                  0x10

#define NTB_SMU_SINTMASK_SMISC0_ACK_INTMASK_OFFSET             5
#define NTB_SMU_SINTMASK_SMISC0_ACK_INTMASK_MASK               0xffe0

#define NTB_SMU_SINTMASK_SMISC1_ACK_INTMASK_OFFSET             16
#define NTB_SMU_SINTMASK_SMISC1_ACK_INTMASK_MASK               0xff0000

#define NTB_SMU_SINTMASK_Reserved_31_24_OFFSET                 24
#define NTB_SMU_SINTMASK_Reserved_31_24_MASK                   0xff000000

typedef union {
  struct {
    UINT32                                 SLTR_UPDATE_INTMASK:1;
    UINT32                                  SRESET_ACK_INTMASK:1;
    UINT32                                     SD3_ACK_INTMASK:1;
    UINT32                                  SPMETO_ACK_INTMASK:1;
    UINT32                                     SD0_ACK_INTMASK:1;
    UINT32                                  SMISC0_ACK_INTMASK:11;
    UINT32                                  SMISC1_ACK_INTMASK:8;
    UINT32                                      Reserved_31_24:8;
  } Field;
  UINT32 Value;
} NTB_SMU_SINTMASK_STRUCT;

#define SMN_NTB_SMU_SINTMASK_ADDRESS                                  0x4000018UL
#define SMN_NTB0NBIO0_NTB_SMU_SINTMASK_ADDRESS                        0x4000018UL
#define SMN_NTB0NBIO1_NTB_SMU_SINTMASK_ADDRESS                        0x4200018UL
#define SMN_NTB1NBIO0_NTB_SMU_SINTMASK_ADDRESS                        0x4100018UL
#define SMN_NTB1NBIO1_NTB_SMU_SINTMASK_ADDRESS                        0x4300018UL


/***********************************************************
* Register Name : NTB_SMU_SINTSTA
************************************************************/

#define NTB_SMU_SINTSTA_SLTR_UPDATE_INTSTA_OFFSET              0
#define NTB_SMU_SINTSTA_SLTR_UPDATE_INTSTA_MASK                0x1

#define NTB_SMU_SINTSTA_SRESET_ACK_INTSTA_OFFSET               1
#define NTB_SMU_SINTSTA_SRESET_ACK_INTSTA_MASK                 0x2

#define NTB_SMU_SINTSTA_SD3_ACK_INTSTA_OFFSET                  2
#define NTB_SMU_SINTSTA_SD3_ACK_INTSTA_MASK                    0x4

#define NTB_SMU_SINTSTA_SPMETO_ACK_INTSTA_OFFSET               3
#define NTB_SMU_SINTSTA_SPMETO_ACK_INTSTA_MASK                 0x8

#define NTB_SMU_SINTSTA_SD0_ACK_INTSTA_OFFSET                  4
#define NTB_SMU_SINTSTA_SD0_ACK_INTSTA_MASK                    0x10

#define NTB_SMU_SINTSTA_SMISC0_ACK_INTSTA_OFFSET               5
#define NTB_SMU_SINTSTA_SMISC0_ACK_INTSTA_MASK                 0xffe0

#define NTB_SMU_SINTSTA_SMISC1_ACK_INTSTA_OFFSET               16
#define NTB_SMU_SINTSTA_SMISC1_ACK_INTSTA_MASK                 0xff0000

#define NTB_SMU_SINTSTA_Reserved_31_24_OFFSET                  24
#define NTB_SMU_SINTSTA_Reserved_31_24_MASK                    0xff000000

typedef union {
  struct {
    UINT32                                  SLTR_UPDATE_INTSTA:1;
    UINT32                                   SRESET_ACK_INTSTA:1;
    UINT32                                      SD3_ACK_INTSTA:1;
    UINT32                                   SPMETO_ACK_INTSTA:1;
    UINT32                                      SD0_ACK_INTSTA:1;
    UINT32                                   SMISC0_ACK_INTSTA:11;
    UINT32                                   SMISC1_ACK_INTSTA:8;
    UINT32                                      Reserved_31_24:8;
  } Field;
  UINT32 Value;
} NTB_SMU_SINTSTA_STRUCT;

#define SMN_NTB_SMU_SINTSTA_ADDRESS                                   0x400001cUL
#define SMN_NTB0NBIO0_NTB_SMU_SINTSTA_ADDRESS                         0x400001cUL
#define SMN_NTB0NBIO1_NTB_SMU_SINTSTA_ADDRESS                         0x420001cUL
#define SMN_NTB1NBIO0_NTB_SMU_SINTSTA_ADDRESS                         0x410001cUL
#define SMN_NTB1NBIO1_NTB_SMU_SINTSTA_ADDRESS                         0x430001cUL


/***********************************************************
* Register Name : NTB_SMU_SPOLLSTA
************************************************************/

#define NTB_SMU_SPOLLSTA_Reserved_0_0_OFFSET                   0
#define NTB_SMU_SPOLLSTA_Reserved_0_0_MASK                     0x1

#define NTB_SMU_SPOLLSTA_SRESET_ACK_POLLSTA_OFFSET             1
#define NTB_SMU_SPOLLSTA_SRESET_ACK_POLLSTA_MASK               0x2

#define NTB_SMU_SPOLLSTA_SD3_ACK_POLLSTA_OFFSET                2
#define NTB_SMU_SPOLLSTA_SD3_ACK_POLLSTA_MASK                  0x4

#define NTB_SMU_SPOLLSTA_SPMETO_ACK_POLLSTA_OFFSET             3
#define NTB_SMU_SPOLLSTA_SPMETO_ACK_POLLSTA_MASK               0x8

#define NTB_SMU_SPOLLSTA_SD0_ACK_POLLSTA_OFFSET                4
#define NTB_SMU_SPOLLSTA_SD0_ACK_POLLSTA_MASK                  0x10

#define NTB_SMU_SPOLLSTA_SMISC0_ACK_POLLSTA_OFFSET             5
#define NTB_SMU_SPOLLSTA_SMISC0_ACK_POLLSTA_MASK               0xffe0

#define NTB_SMU_SPOLLSTA_SMISC1_ACK_POLLSTA_OFFSET             16
#define NTB_SMU_SPOLLSTA_SMISC1_ACK_POLLSTA_MASK               0xff0000

#define NTB_SMU_SPOLLSTA_Reserved_31_24_OFFSET                 24
#define NTB_SMU_SPOLLSTA_Reserved_31_24_MASK                   0xff000000

typedef union {
  struct {
    UINT32                                        Reserved_0_0:1;
    UINT32                                  SRESET_ACK_POLLSTA:1;
    UINT32                                     SD3_ACK_POLLSTA:1;
    UINT32                                  SPMETO_ACK_POLLSTA:1;
    UINT32                                     SD0_ACK_POLLSTA:1;
    UINT32                                  SMISC0_ACK_POLLSTA:11;
    UINT32                                  SMISC1_ACK_POLLSTA:8;
    UINT32                                      Reserved_31_24:8;
  } Field;
  UINT32 Value;
} NTB_SMU_SPOLLSTA_STRUCT;

#define SMN_NTB_SMU_SPOLLSTA_ADDRESS                                  0x4000030UL
#define SMN_NTB0NBIO0_NTB_SMU_SPOLLSTA_ADDRESS                        0x4000030UL
#define SMN_NTB0NBIO1_NTB_SMU_SPOLLSTA_ADDRESS                        0x4200030UL
#define SMN_NTB1NBIO0_NTB_SMU_SPOLLSTA_ADDRESS                        0x4100030UL
#define SMN_NTB1NBIO1_NTB_SMU_SPOLLSTA_ADDRESS                        0x4300030UL


/***********************************************************
* Register Name : NTB_SMU_VC0WREN
************************************************************/

#define NTB_SMU_VC0WREN_VC0WREN_VMOD_OFFSET                    0
#define NTB_SMU_VC0WREN_VC0WREN_VMOD_MASK                      0x1

#define NTB_SMU_VC0WREN_VC0WREN_LMOD_OFFSET                    1
#define NTB_SMU_VC0WREN_VC0WREN_LMOD_MASK                      0x2

#define NTB_SMU_VC0WREN_Reserved_31_2_OFFSET                   2
#define NTB_SMU_VC0WREN_Reserved_31_2_MASK                     0xfffffffc

typedef union {
  struct {
    UINT32                                        VC0WREN_VMOD:1;
    UINT32                                        VC0WREN_LMOD:1;
    UINT32                                       Reserved_31_2:30;
  } Field;
  UINT32 Value;
} NTB_SMU_VC0WREN_STRUCT;

#define SMN_NTB_SMU_VC0WREN_ADDRESS                                   0x4000070UL
#define SMN_NTB0NBIO0_NTB_SMU_VC0WREN_ADDRESS                         0x4000070UL
#define SMN_NTB0NBIO1_NTB_SMU_VC0WREN_ADDRESS                         0x4200070UL
#define SMN_NTB1NBIO0_NTB_SMU_VC0WREN_ADDRESS                         0x4100070UL
#define SMN_NTB1NBIO1_NTB_SMU_VC0WREN_ADDRESS                         0x4300070UL


/***********************************************************
* Register Name : NTB_SMU_VCMAPEN
************************************************************/

#define NTB_SMU_VCMAPEN_VCMAPEN_VMOD_OFFSET                    0
#define NTB_SMU_VCMAPEN_VCMAPEN_VMOD_MASK                      0x1

#define NTB_SMU_VCMAPEN_VCMAPEN_LMOD_OFFSET                    1
#define NTB_SMU_VCMAPEN_VCMAPEN_LMOD_MASK                      0x2

#define NTB_SMU_VCMAPEN_Reserved_31_2_OFFSET                   2
#define NTB_SMU_VCMAPEN_Reserved_31_2_MASK                     0xfffffffc

typedef union {
  struct {
    UINT32                                        VCMAPEN_VMOD:1;
    UINT32                                        VCMAPEN_LMOD:1;
    UINT32                                       Reserved_31_2:30;
  } Field;
  UINT32 Value;
} NTB_SMU_VCMAPEN_STRUCT;

#define SMN_NTB_SMU_VCMAPEN_ADDRESS                                   0x4000064UL
#define SMN_NTB0NBIO0_NTB_SMU_VCMAPEN_ADDRESS                         0x4000064UL
#define SMN_NTB0NBIO1_NTB_SMU_VCMAPEN_ADDRESS                         0x4200064UL
#define SMN_NTB1NBIO0_NTB_SMU_VCMAPEN_ADDRESS                         0x4100064UL
#define SMN_NTB1NBIO1_NTB_SMU_VCMAPEN_ADDRESS                         0x4300064UL


/***********************************************************
* Register Name : NTB_SMU_VMODE
************************************************************/

#define NTB_SMU_VMODE_NTB_SMU_VMODEEN_OFFSET                   0
#define NTB_SMU_VMODE_NTB_SMU_VMODEEN_MASK                     0x1

#define NTB_SMU_VMODE_Reserved_31_1_OFFSET                     1
#define NTB_SMU_VMODE_Reserved_31_1_MASK                       0xfffffffe

typedef union {
  struct {
    UINT32                                     NTB_SMU_VMODEEN:1;
    UINT32                                       Reserved_31_1:31;
  } Field;
  UINT32 Value;
} NTB_SMU_VMODE_STRUCT;

#define SMN_NTB_SMU_VMODE_ADDRESS                                     0x4000060UL
#define SMN_NTB0NBIO0_NTB_SMU_VMODE_ADDRESS                           0x4000060UL
#define SMN_NTB0NBIO1_NTB_SMU_VMODE_ADDRESS                           0x4200060UL
#define SMN_NTB1NBIO0_NTB_SMU_VMODE_ADDRESS                           0x4100060UL
#define SMN_NTB1NBIO1_NTB_SMU_VMODE_ADDRESS                           0x4300060UL


/***********************************************************
* Register Name : PBAR1LMT
************************************************************/

#define PBAR1LMT_Reserved_11_0_OFFSET                          0
#define PBAR1LMT_Reserved_11_0_MASK                            0xfff

#define PBAR1LMT_PBAR1LMT_OFFSET                               12
#define PBAR1LMT_PBAR1LMT_MASK                                 0xfffff000

typedef union {
  struct {
    UINT32                                       Reserved_11_0:12;
    UINT32                                            PBAR1LMT:20;
  } Field;
  UINT32 Value;
} PBAR1LMT_STRUCT;

#define SMN_PBAR1LMT_ADDRESS                                          0x4000414UL
#define SMN_NTB0NBIO0_PBAR1LMT_ADDRESS                                0x4000414UL
#define SMN_NTB0NBIO1_PBAR1LMT_ADDRESS                                0x4200414UL
#define SMN_NTB1NBIO0_PBAR1LMT_ADDRESS                                0x4100414UL
#define SMN_NTB1NBIO1_PBAR1LMT_ADDRESS                                0x4300414UL


/***********************************************************
* Register Name : PBAR1XLAT_HI
************************************************************/

#define PBAR1XLAT_HI_PBAR1XLAT_HI_OFFSET                       0
#define PBAR1XLAT_HI_PBAR1XLAT_HI_MASK                         0xffffffff

typedef union {
  struct {
    UINT32                                        PBAR1XLAT_HI:32;
  } Field;
  UINT32 Value;
} PBAR1XLAT_HI_STRUCT;

#define SMN_PBAR1XLAT_HI_ADDRESS                                      0x4000434UL
#define SMN_NTB0NBIO0_PBAR1XLAT_HI_ADDRESS                            0x4000434UL
#define SMN_NTB0NBIO1_PBAR1XLAT_HI_ADDRESS                            0x4200434UL
#define SMN_NTB1NBIO0_PBAR1XLAT_HI_ADDRESS                            0x4100434UL
#define SMN_NTB1NBIO1_PBAR1XLAT_HI_ADDRESS                            0x4300434UL


/***********************************************************
* Register Name : PBAR1XLAT_LO
************************************************************/

#define PBAR1XLAT_LO_NS_CTRL_OFFSET                            0
#define PBAR1XLAT_LO_NS_CTRL_MASK                              0x3

#define PBAR1XLAT_LO_TPH_CTRL_OFFSET                           2
#define PBAR1XLAT_LO_TPH_CTRL_MASK                             0xc

#define PBAR1XLAT_LO_TPH_ST_OFFSET                             4
#define PBAR1XLAT_LO_TPH_ST_MASK                               0xff0

#define PBAR1XLAT_LO_PBAR1XLAT_LO_OFFSET                       12
#define PBAR1XLAT_LO_PBAR1XLAT_LO_MASK                         0xfffff000

typedef union {
  struct {
    UINT32                                             NS_CTRL:2;
    UINT32                                            TPH_CTRL:2;
    UINT32                                              TPH_ST:8;
    UINT32                                        PBAR1XLAT_LO:20;
  } Field;
  UINT32 Value;
} PBAR1XLAT_LO_STRUCT;

#define SMN_PBAR1XLAT_LO_ADDRESS                                      0x4000430UL
#define SMN_NTB0NBIO0_PBAR1XLAT_LO_ADDRESS                            0x4000430UL
#define SMN_NTB0NBIO1_PBAR1XLAT_LO_ADDRESS                            0x4200430UL
#define SMN_NTB1NBIO0_PBAR1XLAT_LO_ADDRESS                            0x4100430UL
#define SMN_NTB1NBIO1_PBAR1XLAT_LO_ADDRESS                            0x4300430UL


/***********************************************************
* Register Name : PBAR23LMT_HI
************************************************************/

#define PBAR23LMT_HI_PBAR23LMT_HI_OFFSET                       0
#define PBAR23LMT_HI_PBAR23LMT_HI_MASK                         0xffffffff

typedef union {
  struct {
    UINT32                                        PBAR23LMT_HI:32;
  } Field;
  UINT32 Value;
} PBAR23LMT_HI_STRUCT;

#define SMN_PBAR23LMT_HI_ADDRESS                                      0x400041cUL
#define SMN_NTB0NBIO0_PBAR23LMT_HI_ADDRESS                            0x400041cUL
#define SMN_NTB0NBIO1_PBAR23LMT_HI_ADDRESS                            0x420041cUL
#define SMN_NTB1NBIO0_PBAR23LMT_HI_ADDRESS                            0x410041cUL
#define SMN_NTB1NBIO1_PBAR23LMT_HI_ADDRESS                            0x430041cUL


/***********************************************************
* Register Name : PBAR23LMT_LO
************************************************************/

#define PBAR23LMT_LO_Reserved_11_0_OFFSET                      0
#define PBAR23LMT_LO_Reserved_11_0_MASK                        0xfff

#define PBAR23LMT_LO_PBAR23LMT_LO_OFFSET                       12
#define PBAR23LMT_LO_PBAR23LMT_LO_MASK                         0xfffff000

typedef union {
  struct {
    UINT32                                       Reserved_11_0:12;
    UINT32                                        PBAR23LMT_LO:20;
  } Field;
  UINT32 Value;
} PBAR23LMT_LO_STRUCT;

#define SMN_PBAR23LMT_LO_ADDRESS                                      0x4000418UL
#define SMN_NTB0NBIO0_PBAR23LMT_LO_ADDRESS                            0x4000418UL
#define SMN_NTB0NBIO1_PBAR23LMT_LO_ADDRESS                            0x4200418UL
#define SMN_NTB1NBIO0_PBAR23LMT_LO_ADDRESS                            0x4100418UL
#define SMN_NTB1NBIO1_PBAR23LMT_LO_ADDRESS                            0x4300418UL


/***********************************************************
* Register Name : PBAR23XLAT1
************************************************************/

#define PBAR23XLAT1_TPH_PH_CTRL_OFFSET                         0
#define PBAR23XLAT1_TPH_PH_CTRL_MASK                           0x1

#define PBAR23XLAT1_TPH_PH_OFFSET                              1
#define PBAR23XLAT1_TPH_PH_MASK                                0x6

#define PBAR23XLAT1_Reserved_31_3_OFFSET                       3
#define PBAR23XLAT1_Reserved_31_3_MASK                         0xfffffff8

typedef union {
  struct {
    UINT32                                         TPH_PH_CTRL:1;
    UINT32                                              TPH_PH:2;
    UINT32                                       Reserved_31_3:29;
  } Field;
  UINT32 Value;
} PBAR23XLAT1_STRUCT;

#define SMN_PBAR23XLAT1_ADDRESS                                       0x4000448UL
#define SMN_NTB0NBIO0_PBAR23XLAT1_ADDRESS                             0x4000448UL
#define SMN_NTB0NBIO1_PBAR23XLAT1_ADDRESS                             0x4200448UL
#define SMN_NTB1NBIO0_PBAR23XLAT1_ADDRESS                             0x4100448UL
#define SMN_NTB1NBIO1_PBAR23XLAT1_ADDRESS                             0x4300448UL


/***********************************************************
* Register Name : PBAR23XLAT_HI
************************************************************/

#define PBAR23XLAT_HI_PBAR23XLAT_HI_OFFSET                     0
#define PBAR23XLAT_HI_PBAR23XLAT_HI_MASK                       0xffffffff

typedef union {
  struct {
    UINT32                                       PBAR23XLAT_HI:32;
  } Field;
  UINT32 Value;
} PBAR23XLAT_HI_STRUCT;

#define SMN_PBAR23XLAT_HI_ADDRESS                                     0x400043cUL
#define SMN_NTB0NBIO0_PBAR23XLAT_HI_ADDRESS                           0x400043cUL
#define SMN_NTB0NBIO1_PBAR23XLAT_HI_ADDRESS                           0x420043cUL
#define SMN_NTB1NBIO0_PBAR23XLAT_HI_ADDRESS                           0x410043cUL
#define SMN_NTB1NBIO1_PBAR23XLAT_HI_ADDRESS                           0x430043cUL


/***********************************************************
* Register Name : PBAR23XLAT_LO
************************************************************/

#define PBAR23XLAT_LO_NS_CTRL_OFFSET                           0
#define PBAR23XLAT_LO_NS_CTRL_MASK                             0x3

#define PBAR23XLAT_LO_TPH_CTRL_OFFSET                          2
#define PBAR23XLAT_LO_TPH_CTRL_MASK                            0xc

#define PBAR23XLAT_LO_TPH_ST_OFFSET                            4
#define PBAR23XLAT_LO_TPH_ST_MASK                              0xff0

#define PBAR23XLAT_LO_PBAR23XLAT_LO_OFFSET                     12
#define PBAR23XLAT_LO_PBAR23XLAT_LO_MASK                       0xfffff000

typedef union {
  struct {
    UINT32                                             NS_CTRL:2;
    UINT32                                            TPH_CTRL:2;
    UINT32                                              TPH_ST:8;
    UINT32                                       PBAR23XLAT_LO:20;
  } Field;
  UINT32 Value;
} PBAR23XLAT_LO_STRUCT;

#define SMN_PBAR23XLAT_LO_ADDRESS                                     0x4000438UL
#define SMN_NTB0NBIO0_PBAR23XLAT_LO_ADDRESS                           0x4000438UL
#define SMN_NTB0NBIO1_PBAR23XLAT_LO_ADDRESS                           0x4200438UL
#define SMN_NTB1NBIO0_PBAR23XLAT_LO_ADDRESS                           0x4100438UL
#define SMN_NTB1NBIO1_PBAR23XLAT_LO_ADDRESS                           0x4300438UL


/***********************************************************
* Register Name : PBAR45LMT_HI
************************************************************/

#define PBAR45LMT_HI_PBAR45LMT_HI_OFFSET                       0
#define PBAR45LMT_HI_PBAR45LMT_HI_MASK                         0xffffffff

typedef union {
  struct {
    UINT32                                        PBAR45LMT_HI:32;
  } Field;
  UINT32 Value;
} PBAR45LMT_HI_STRUCT;

#define SMN_PBAR45LMT_HI_ADDRESS                                      0x4000424UL
#define SMN_NTB0NBIO0_PBAR45LMT_HI_ADDRESS                            0x4000424UL
#define SMN_NTB0NBIO1_PBAR45LMT_HI_ADDRESS                            0x4200424UL
#define SMN_NTB1NBIO0_PBAR45LMT_HI_ADDRESS                            0x4100424UL
#define SMN_NTB1NBIO1_PBAR45LMT_HI_ADDRESS                            0x4300424UL


/***********************************************************
* Register Name : PBAR45LMT_LO
************************************************************/

#define PBAR45LMT_LO_Reserved_11_0_OFFSET                      0
#define PBAR45LMT_LO_Reserved_11_0_MASK                        0xfff

#define PBAR45LMT_LO_PBAR45LMT_LO_OFFSET                       12
#define PBAR45LMT_LO_PBAR45LMT_LO_MASK                         0xfffff000

typedef union {
  struct {
    UINT32                                       Reserved_11_0:12;
    UINT32                                        PBAR45LMT_LO:20;
  } Field;
  UINT32 Value;
} PBAR45LMT_LO_STRUCT;

#define SMN_PBAR45LMT_LO_ADDRESS                                      0x4000420UL
#define SMN_NTB0NBIO0_PBAR45LMT_LO_ADDRESS                            0x4000420UL
#define SMN_NTB0NBIO1_PBAR45LMT_LO_ADDRESS                            0x4200420UL
#define SMN_NTB1NBIO0_PBAR45LMT_LO_ADDRESS                            0x4100420UL
#define SMN_NTB1NBIO1_PBAR45LMT_LO_ADDRESS                            0x4300420UL


/***********************************************************
* Register Name : PBAR45XLAT1
************************************************************/

#define PBAR45XLAT1_TPH_PH_CTRL_OFFSET                         0
#define PBAR45XLAT1_TPH_PH_CTRL_MASK                           0x1

#define PBAR45XLAT1_TPH_PH_OFFSET                              1
#define PBAR45XLAT1_TPH_PH_MASK                                0x6

#define PBAR45XLAT1_Reserved_31_3_OFFSET                       3
#define PBAR45XLAT1_Reserved_31_3_MASK                         0xfffffff8

typedef union {
  struct {
    UINT32                                         TPH_PH_CTRL:1;
    UINT32                                              TPH_PH:2;
    UINT32                                       Reserved_31_3:29;
  } Field;
  UINT32 Value;
} PBAR45XLAT1_STRUCT;

#define SMN_PBAR45XLAT1_ADDRESS                                       0x400044cUL
#define SMN_NTB0NBIO0_PBAR45XLAT1_ADDRESS                             0x400044cUL
#define SMN_NTB0NBIO1_PBAR45XLAT1_ADDRESS                             0x420044cUL
#define SMN_NTB1NBIO0_PBAR45XLAT1_ADDRESS                             0x410044cUL
#define SMN_NTB1NBIO1_PBAR45XLAT1_ADDRESS                             0x430044cUL


/***********************************************************
* Register Name : PBAR45XLAT_HI
************************************************************/

#define PBAR45XLAT_HI_PBAR45XLAT_HI_OFFSET                     0
#define PBAR45XLAT_HI_PBAR45XLAT_HI_MASK                       0xffffffff

typedef union {
  struct {
    UINT32                                       PBAR45XLAT_HI:32;
  } Field;
  UINT32 Value;
} PBAR45XLAT_HI_STRUCT;

#define SMN_PBAR45XLAT_HI_ADDRESS                                     0x4000444UL
#define SMN_NTB0NBIO0_PBAR45XLAT_HI_ADDRESS                           0x4000444UL
#define SMN_NTB0NBIO1_PBAR45XLAT_HI_ADDRESS                           0x4200444UL
#define SMN_NTB1NBIO0_PBAR45XLAT_HI_ADDRESS                           0x4100444UL
#define SMN_NTB1NBIO1_PBAR45XLAT_HI_ADDRESS                           0x4300444UL


/***********************************************************
* Register Name : PBAR45XLAT_LO
************************************************************/

#define PBAR45XLAT_LO_NS_CTRL_OFFSET                           0
#define PBAR45XLAT_LO_NS_CTRL_MASK                             0x3

#define PBAR45XLAT_LO_TPH_CTRL_OFFSET                          2
#define PBAR45XLAT_LO_TPH_CTRL_MASK                            0xc

#define PBAR45XLAT_LO_TPH_ST_OFFSET                            4
#define PBAR45XLAT_LO_TPH_ST_MASK                              0xff0

#define PBAR45XLAT_LO_PBAR45XLAT_LO_OFFSET                     12
#define PBAR45XLAT_LO_PBAR45XLAT_LO_MASK                       0xfffff000

typedef union {
  struct {
    UINT32                                             NS_CTRL:2;
    UINT32                                            TPH_CTRL:2;
    UINT32                                              TPH_ST:8;
    UINT32                                       PBAR45XLAT_LO:20;
  } Field;
  UINT32 Value;
} PBAR45XLAT_LO_STRUCT;

#define SMN_PBAR45XLAT_LO_ADDRESS                                     0x4000440UL
#define SMN_NTB0NBIO0_PBAR45XLAT_LO_ADDRESS                           0x4000440UL
#define SMN_NTB0NBIO1_PBAR45XLAT_LO_ADDRESS                           0x4200440UL
#define SMN_NTB1NBIO0_PBAR45XLAT_LO_ADDRESS                           0x4100440UL
#define SMN_NTB1NBIO1_PBAR45XLAT_LO_ADDRESS                           0x4300440UL


/***********************************************************
* Register Name : PCTXRAM_ECC_ERRCNT
************************************************************/

#define PCTXRAM_ECC_ERRCNT_PCTXRAM_ECC_SECNT_OFFSET            0
#define PCTXRAM_ECC_ERRCNT_PCTXRAM_ECC_SECNT_MASK              0xff

#define PCTXRAM_ECC_ERRCNT_PCTXRAM_ECC_DECNT_OFFSET            8
#define PCTXRAM_ECC_ERRCNT_PCTXRAM_ECC_DECNT_MASK              0xff00

#define PCTXRAM_ECC_ERRCNT_Reserved_16_16_OFFSET               16
#define PCTXRAM_ECC_ERRCNT_Reserved_16_16_MASK                 0x10000

#define PCTXRAM_ECC_ERRCNT_PCTXRAM_ECCSEI_EN_OFFSET            17
#define PCTXRAM_ECC_ERRCNT_PCTXRAM_ECCSEI_EN_MASK              0x20000

#define PCTXRAM_ECC_ERRCNT_PCTXRAM_ECCDEI_EN_OFFSET            18
#define PCTXRAM_ECC_ERRCNT_PCTXRAM_ECCDEI_EN_MASK              0x40000

#define PCTXRAM_ECC_ERRCNT_PCTXRAM_ECC_ERREVT_EN_OFFSET        19
#define PCTXRAM_ECC_ERRCNT_PCTXRAM_ECC_ERREVT_EN_MASK          0x80000

#define PCTXRAM_ECC_ERRCNT_PCTXRAM_ECCDEI_WAY_OFFSET           20
#define PCTXRAM_ECC_ERRCNT_PCTXRAM_ECCDEI_WAY_MASK             0x100000

#define PCTXRAM_ECC_ERRCNT_Reserved_31_21_OFFSET               21
#define PCTXRAM_ECC_ERRCNT_Reserved_31_21_MASK                 0xffe00000

typedef union {
  struct {
    UINT32                                   PCTXRAM_ECC_SECNT:8;
    UINT32                                   PCTXRAM_ECC_DECNT:8;
    UINT32                                      Reserved_16_16:1;
    UINT32                                   PCTXRAM_ECCSEI_EN:1;
    UINT32                                   PCTXRAM_ECCDEI_EN:1;
    UINT32                               PCTXRAM_ECC_ERREVT_EN:1;
    UINT32                                  PCTXRAM_ECCDEI_WAY:1;
    UINT32                                      Reserved_31_21:11;
  } Field;
  UINT32 Value;
} PCTXRAM_ECC_ERRCNT_STRUCT;

#define SMN_PCTXRAM_ECC_ERRCNT_ADDRESS                                0x4000504UL
#define SMN_NTB0NBIO0_PCTXRAM_ECC_ERRCNT_ADDRESS                      0x4000504UL
#define SMN_NTB0NBIO1_PCTXRAM_ECC_ERRCNT_ADDRESS                      0x4200504UL
#define SMN_NTB1NBIO0_PCTXRAM_ECC_ERRCNT_ADDRESS                      0x4100504UL
#define SMN_NTB1NBIO1_PCTXRAM_ECC_ERRCNT_ADDRESS                      0x4300504UL


/***********************************************************
* Register Name : PDBFM
************************************************************/

#define PDBFM_PDBFM_OFFSET                                     0
#define PDBFM_PDBFM_MASK                                       0xffff

#define PDBFM_Reserved_31_16_OFFSET                            16
#define PDBFM_Reserved_31_16_MASK                              0xffff0000

typedef union {
  struct {
    UINT32                                               PDBFM:16;
    UINT32                                      Reserved_31_16:16;
  } Field;
  UINT32 Value;
} PDBFM_STRUCT;

#define SMN_PDBFM_ADDRESS                                             0x4000450UL
#define SMN_NTB0NBIO0_PDBFM_ADDRESS                                   0x4000450UL
#define SMN_NTB0NBIO1_PDBFM_ADDRESS                                   0x4200450UL
#define SMN_NTB1NBIO0_PDBFM_ADDRESS                                   0x4100450UL
#define SMN_NTB1NBIO1_PDBFM_ADDRESS                                   0x4300450UL


/***********************************************************
* Register Name : PDBMASK
************************************************************/

#define PDBMASK_PDBMASK_OFFSET                                 0
#define PDBMASK_PDBMASK_MASK                                   0xffff

#define PDBMASK_Reserved_31_16_OFFSET                          16
#define PDBMASK_Reserved_31_16_MASK                            0xffff0000

typedef union {
  struct {
    UINT32                                             PDBMASK:16;
    UINT32                                      Reserved_31_16:16;
  } Field;
  UINT32 Value;
} PDBMASK_STRUCT;

#define SMN_PDBMASK_ADDRESS                                           0x400045cUL
#define SMN_NTB0NBIO0_PDBMASK_ADDRESS                                 0x400045cUL
#define SMN_NTB0NBIO1_PDBMASK_ADDRESS                                 0x420045cUL
#define SMN_NTB1NBIO0_PDBMASK_ADDRESS                                 0x410045cUL
#define SMN_NTB1NBIO1_PDBMASK_ADDRESS                                 0x430045cUL


/***********************************************************
* Register Name : PDBREQ
************************************************************/

#define PDBREQ_PDBREQ_OFFSET                                   0
#define PDBREQ_PDBREQ_MASK                                     0xffff

#define PDBREQ_Reserved_31_16_OFFSET                           16
#define PDBREQ_Reserved_31_16_MASK                             0xffff0000

typedef union {
  struct {
    UINT32                                              PDBREQ:16;
    UINT32                                      Reserved_31_16:16;
  } Field;
  UINT32 Value;
} PDBREQ_STRUCT;

#define SMN_PDBREQ_ADDRESS                                            0x4000454UL
#define SMN_NTB0NBIO0_PDBREQ_ADDRESS                                  0x4000454UL
#define SMN_NTB0NBIO1_PDBREQ_ADDRESS                                  0x4200454UL
#define SMN_NTB1NBIO0_PDBREQ_ADDRESS                                  0x4100454UL
#define SMN_NTB1NBIO1_PDBREQ_ADDRESS                                  0x4300454UL


/***********************************************************
* Register Name : PDBSTA
************************************************************/

#define PDBSTA_PDBSTA_OFFSET                                   0
#define PDBSTA_PDBSTA_MASK                                     0xffff

#define PDBSTA_Reserved_31_16_OFFSET                           16
#define PDBSTA_Reserved_31_16_MASK                             0xffff0000

typedef union {
  struct {
    UINT32                                              PDBSTA:16;
    UINT32                                      Reserved_31_16:16;
  } Field;
  UINT32 Value;
} PDBSTA_STRUCT;

#define SMN_PDBSTA_ADDRESS                                            0x4000460UL
#define SMN_NTB0NBIO0_PDBSTA_ADDRESS                                  0x4000460UL
#define SMN_NTB0NBIO1_PDBSTA_ADDRESS                                  0x4200460UL
#define SMN_NTB1NBIO0_PDBSTA_ADDRESS                                  0x4100460UL
#define SMN_NTB1NBIO1_PDBSTA_ADDRESS                                  0x4300460UL


/***********************************************************
* Register Name : PFLUSH_MOD
************************************************************/

#define PFLUSH_MOD_PFLUSH_MOD_OFFSET                           0
#define PFLUSH_MOD_PFLUSH_MOD_MASK                             0x1

#define PFLUSH_MOD_Reserved_31_1_OFFSET                        1
#define PFLUSH_MOD_Reserved_31_1_MASK                          0xfffffffe

typedef union {
  struct {
    UINT32                                          PFLUSH_MOD:1;
    UINT32                                       Reserved_31_1:31;
  } Field;
  UINT32 Value;
} PFLUSH_MOD_STRUCT;

#define SMN_PFLUSH_MOD_ADDRESS                                        0x400049cUL
#define SMN_NTB0NBIO0_PFLUSH_MOD_ADDRESS                              0x400049cUL
#define SMN_NTB0NBIO1_PFLUSH_MOD_ADDRESS                              0x420049cUL
#define SMN_NTB1NBIO0_PFLUSH_MOD_ADDRESS                              0x410049cUL
#define SMN_NTB1NBIO1_PFLUSH_MOD_ADDRESS                              0x430049cUL


/***********************************************************
* Register Name : PFLUSH_TRIG
************************************************************/

#define PFLUSH_TRIG_PFLUSH_TRIG_OFFSET                         0
#define PFLUSH_TRIG_PFLUSH_TRIG_MASK                           0x1

#define PFLUSH_TRIG_Reserved_31_1_OFFSET                       1
#define PFLUSH_TRIG_Reserved_31_1_MASK                         0xfffffffe

typedef union {
  struct {
    UINT32                                         PFLUSH_TRIG:1;
    UINT32                                       Reserved_31_1:31;
  } Field;
  UINT32 Value;
} PFLUSH_TRIG_STRUCT;

#define SMN_PFLUSH_TRIG_ADDRESS                                       0x4000498UL
#define SMN_NTB0NBIO0_PFLUSH_TRIG_ADDRESS                             0x4000498UL
#define SMN_NTB0NBIO1_PFLUSH_TRIG_ADDRESS                             0x4200498UL
#define SMN_NTB1NBIO0_PFLUSH_TRIG_ADDRESS                             0x4100498UL
#define SMN_NTB1NBIO1_PFLUSH_TRIG_ADDRESS                             0x4300498UL


/***********************************************************
* Register Name : PINTMASK
************************************************************/

#define PINTMASK_PFLUSH_INTMASK_OFFSET                         0
#define PINTMASK_PFLUSH_INTMASK_MASK                           0x1

#define PINTMASK_PRESETNOTICE_INTMASK_OFFSET                   1
#define PINTMASK_PRESETNOTICE_INTMASK_MASK                     0x2

#define PINTMASK_PD3NOTICE_INTMASK_OFFSET                      2
#define PINTMASK_PD3NOTICE_INTMASK_MASK                        0x4

#define PINTMASK_PPMETONOTICE_INTMASK_OFFSET                   3
#define PINTMASK_PPMETONOTICE_INTMASK_MASK                     0x8

#define PINTMASK_PD0NOTICE_INTMASK_OFFSET                      4
#define PINTMASK_PD0NOTICE_INTMASK_MASK                        0x10

#define PINTMASK_PMISC0_INTMASK_OFFSET                         5
#define PINTMASK_PMISC0_INTMASK_MASK                           0xffe0

#define PINTMASK_PMISC1_INTMASK_OFFSET                         16
#define PINTMASK_PMISC1_INTMASK_MASK                           0xff0000

#define PINTMASK_PSDPERR_INTMASK_OFFSET                        24
#define PINTMASK_PSDPERR_INTMASK_MASK                          0xf000000

#define PINTMASK_PECCSE_INTMASK_OFFSET                         28
#define PINTMASK_PECCSE_INTMASK_MASK                           0x10000000

#define PINTMASK_PECCDE_INTMASK_OFFSET                         29
#define PINTMASK_PECCDE_INTMASK_MASK                           0x20000000

#define PINTMASK_PLUT_INTMASK_OFFSET                           30
#define PINTMASK_PLUT_INTMASK_MASK                             0x40000000

#define PINTMASK_Reserved_31_31_OFFSET                         31
#define PINTMASK_Reserved_31_31_MASK                           0x80000000

typedef union {
  struct {
    UINT32                                      PFLUSH_INTMASK:1;
    UINT32                                PRESETNOTICE_INTMASK:1;
    UINT32                                   PD3NOTICE_INTMASK:1;
    UINT32                                PPMETONOTICE_INTMASK:1;
    UINT32                                   PD0NOTICE_INTMASK:1;
    UINT32                                      PMISC0_INTMASK:11;
    UINT32                                      PMISC1_INTMASK:8;
    UINT32                                     PSDPERR_INTMASK:4;
    UINT32                                      PECCSE_INTMASK:1;
    UINT32                                      PECCDE_INTMASK:1;
    UINT32                                        PLUT_INTMASK:1;
    UINT32                                      Reserved_31_31:1;
  } Field;
  UINT32 Value;
} PINTMASK_STRUCT;

#define SMN_PINTMASK_ADDRESS                                          0x4000470UL
#define SMN_NTB0NBIO0_PINTMASK_ADDRESS                                0x4000470UL
#define SMN_NTB0NBIO1_PINTMASK_ADDRESS                                0x4200470UL
#define SMN_NTB1NBIO0_PINTMASK_ADDRESS                                0x4100470UL
#define SMN_NTB1NBIO1_PINTMASK_ADDRESS                                0x4300470UL


/***********************************************************
* Register Name : PINTMASK1
************************************************************/

#define PINTMASK1_PTAGRAM_ECCSE_INTMASK_OFFSET                 0
#define PINTMASK1_PTAGRAM_ECCSE_INTMASK_MASK                   0x1

#define PINTMASK1_PTAGRAM_ECCDE_INTMASK_OFFSET                 1
#define PINTMASK1_PTAGRAM_ECCDE_INTMASK_MASK                   0x2

#define PINTMASK1_PCTXRAM_ECCSE_INTMASK_OFFSET                 2
#define PINTMASK1_PCTXRAM_ECCSE_INTMASK_MASK                   0x4

#define PINTMASK1_PCTXRAM_ECCDE_INTMASK_OFFSET                 3
#define PINTMASK1_PCTXRAM_ECCDE_INTMASK_MASK                   0x8

#define PINTMASK1_Reserved_31_4_OFFSET                         4
#define PINTMASK1_Reserved_31_4_MASK                           0xfffffff0

typedef union {
  struct {
    UINT32                               PTAGRAM_ECCSE_INTMASK:1;
    UINT32                               PTAGRAM_ECCDE_INTMASK:1;
    UINT32                               PCTXRAM_ECCSE_INTMASK:1;
    UINT32                               PCTXRAM_ECCDE_INTMASK:1;
    UINT32                                       Reserved_31_4:28;
  } Field;
  UINT32 Value;
} PINTMASK1_STRUCT;

#define SMN_PINTMASK1_ADDRESS                                         0x4000478UL
#define SMN_NTB0NBIO0_PINTMASK1_ADDRESS                               0x4000478UL
#define SMN_NTB0NBIO1_PINTMASK1_ADDRESS                               0x4200478UL
#define SMN_NTB1NBIO0_PINTMASK1_ADDRESS                               0x4100478UL
#define SMN_NTB1NBIO1_PINTMASK1_ADDRESS                               0x4300478UL


/***********************************************************
* Register Name : PINTSTA
************************************************************/

#define PINTSTA_PFLUSH_INTSTA_OFFSET                           0
#define PINTSTA_PFLUSH_INTSTA_MASK                             0x1

#define PINTSTA_PRESETNOTICE_INTSTA_OFFSET                     1
#define PINTSTA_PRESETNOTICE_INTSTA_MASK                       0x2

#define PINTSTA_PD3NOTICE_INTSTA_OFFSET                        2
#define PINTSTA_PD3NOTICE_INTSTA_MASK                          0x4

#define PINTSTA_PPMETONOTICE_INTSTA_OFFSET                     3
#define PINTSTA_PPMETONOTICE_INTSTA_MASK                       0x8

#define PINTSTA_PD0NOTICE_INTSTA_OFFSET                        4
#define PINTSTA_PD0NOTICE_INTSTA_MASK                          0x10

#define PINTSTA_PMISC0_INTSTA_OFFSET                           5
#define PINTSTA_PMISC0_INTSTA_MASK                             0xffe0

#define PINTSTA_PMISC1_INTSTA_OFFSET                           16
#define PINTSTA_PMISC1_INTSTA_MASK                             0xff0000

#define PINTSTA_PSDPERR_INTSTA_OFFSET                          24
#define PINTSTA_PSDPERR_INTSTA_MASK                            0xf000000

#define PINTSTA_PECCSE_INTSTA_OFFSET                           28
#define PINTSTA_PECCSE_INTSTA_MASK                             0x10000000

#define PINTSTA_PECCDE_INTSTA_OFFSET                           29
#define PINTSTA_PECCDE_INTSTA_MASK                             0x20000000

#define PINTSTA_PLUT_INTSTA_OFFSET                             30
#define PINTSTA_PLUT_INTSTA_MASK                               0x40000000

#define PINTSTA_Reserved_31_31_OFFSET                          31
#define PINTSTA_Reserved_31_31_MASK                            0x80000000

typedef union {
  struct {
    UINT32                                       PFLUSH_INTSTA:1;
    UINT32                                 PRESETNOTICE_INTSTA:1;
    UINT32                                    PD3NOTICE_INTSTA:1;
    UINT32                                 PPMETONOTICE_INTSTA:1;
    UINT32                                    PD0NOTICE_INTSTA:1;
    UINT32                                       PMISC0_INTSTA:11;
    UINT32                                       PMISC1_INTSTA:8;
    UINT32                                      PSDPERR_INTSTA:4;
    UINT32                                       PECCSE_INTSTA:1;
    UINT32                                       PECCDE_INTSTA:1;
    UINT32                                         PLUT_INTSTA:1;
    UINT32                                      Reserved_31_31:1;
  } Field;
  UINT32 Value;
} PINTSTA_STRUCT;

#define SMN_PINTSTA_ADDRESS                                           0x4000474UL
#define SMN_NTB0NBIO0_PINTSTA_ADDRESS                                 0x4000474UL
#define SMN_NTB0NBIO1_PINTSTA_ADDRESS                                 0x4200474UL
#define SMN_NTB1NBIO0_PINTSTA_ADDRESS                                 0x4100474UL
#define SMN_NTB1NBIO1_PINTSTA_ADDRESS                                 0x4300474UL


/***********************************************************
* Register Name : PINTSTA1
************************************************************/

#define PINTSTA1_PTAGRAM_ECCSE_INTSTA_OFFSET                   0
#define PINTSTA1_PTAGRAM_ECCSE_INTSTA_MASK                     0x1

#define PINTSTA1_PTAGRAM_ECCDE_INTSTA_OFFSET                   1
#define PINTSTA1_PTAGRAM_ECCDE_INTSTA_MASK                     0x2

#define PINTSTA1_PCTXRAM_ECCSE_INTSTA_OFFSET                   2
#define PINTSTA1_PCTXRAM_ECCSE_INTSTA_MASK                     0x4

#define PINTSTA1_PCTXRAM_ECCDE_INTSTA_OFFSET                   3
#define PINTSTA1_PCTXRAM_ECCDE_INTSTA_MASK                     0x8

#define PINTSTA1_Reserved_31_4_OFFSET                          4
#define PINTSTA1_Reserved_31_4_MASK                            0xfffffff0

typedef union {
  struct {
    UINT32                                PTAGRAM_ECCSE_INTSTA:1;
    UINT32                                PTAGRAM_ECCDE_INTSTA:1;
    UINT32                                PCTXRAM_ECCSE_INTSTA:1;
    UINT32                                PCTXRAM_ECCDE_INTSTA:1;
    UINT32                                       Reserved_31_4:28;
  } Field;
  UINT32 Value;
} PINTSTA1_STRUCT;

#define SMN_PINTSTA1_ADDRESS                                          0x400047cUL
#define SMN_NTB0NBIO0_PINTSTA1_ADDRESS                                0x400047cUL
#define SMN_NTB0NBIO1_PINTSTA1_ADDRESS                                0x420047cUL
#define SMN_NTB1NBIO0_PINTSTA1_ADDRESS                                0x410047cUL
#define SMN_NTB1NBIO1_PINTSTA1_ADDRESS                                0x430047cUL


/***********************************************************
* Register Name : PLTR_LATENCY
************************************************************/

#define PLTR_LATENCY_PLTR_NSNP_LTNCY_OFFSET                    0
#define PLTR_LATENCY_PLTR_NSNP_LTNCY_MASK                      0xffff

#define PLTR_LATENCY_PLTR_SNP_LTNCY_OFFSET                     16
#define PLTR_LATENCY_PLTR_SNP_LTNCY_MASK                       0xffff0000

typedef union {
  struct {
    UINT32                                     PLTR_NSNP_LTNCY:16;
    UINT32                                      PLTR_SNP_LTNCY:16;
  } Field;
  UINT32 Value;
} PLTR_LATENCY_STRUCT;

#define SMN_PLTR_LATENCY_ADDRESS                                      0x4000494UL
#define SMN_NTB0NBIO0_PLTR_LATENCY_ADDRESS                            0x4000494UL
#define SMN_NTB0NBIO1_PLTR_LATENCY_ADDRESS                            0x4200494UL
#define SMN_NTB1NBIO0_PLTR_LATENCY_ADDRESS                            0x4100494UL
#define SMN_NTB1NBIO1_PLTR_LATENCY_ADDRESS                            0x4300494UL


/***********************************************************
* Register Name : PLUT_CTRL
************************************************************/

#define PLUT_CTRL_PLUT_EN_OFFSET                               0
#define PLUT_CTRL_PLUT_EN_MASK                                 0x1

#define PLUT_CTRL_PLUT_ALLOC_OFFSET                            1
#define PLUT_CTRL_PLUT_ALLOC_MASK                              0x2

#define PLUT_CTRL_Reserved_7_2_OFFSET                          2
#define PLUT_CTRL_Reserved_7_2_MASK                            0xfc

#define PLUT_CTRL_PLUT_SIZEUNIT_OFFSET                         8
#define PLUT_CTRL_PLUT_SIZEUNIT_MASK                           0xff00

#define PLUT_CTRL_Reserved_31_16_OFFSET                        16
#define PLUT_CTRL_Reserved_31_16_MASK                          0xffff0000

typedef union {
  struct {
    UINT32                                             PLUT_EN:1;
    UINT32                                          PLUT_ALLOC:1;
    UINT32                                        Reserved_7_2:6;
    UINT32                                       PLUT_SIZEUNIT:8;
    UINT32                                      Reserved_31_16:16;
  } Field;
  UINT32 Value;
} PLUT_CTRL_STRUCT;

#define SMN_PLUT_CTRL_ADDRESS                                         0x40004f0UL
#define SMN_NTB0NBIO0_PLUT_CTRL_ADDRESS                               0x40004f0UL
#define SMN_NTB0NBIO1_PLUT_CTRL_ADDRESS                               0x42004f0UL
#define SMN_NTB1NBIO0_PLUT_CTRL_ADDRESS                               0x41004f0UL
#define SMN_NTB1NBIO1_PLUT_CTRL_ADDRESS                               0x43004f0UL


/***********************************************************
* Register Name : PLUT_ECC_ERRCNT
************************************************************/

#define PLUT_ECC_ERRCNT_PLUT_ECC_SECNT_OFFSET                  0
#define PLUT_ECC_ERRCNT_PLUT_ECC_SECNT_MASK                    0xff

#define PLUT_ECC_ERRCNT_PLUT_ECC_DECNT_OFFSET                  8
#define PLUT_ECC_ERRCNT_PLUT_ECC_DECNT_MASK                    0xff00

#define PLUT_ECC_ERRCNT_PLUT_ECCERR_DROP_EN_OFFSET             16
#define PLUT_ECC_ERRCNT_PLUT_ECCERR_DROP_EN_MASK               0x10000

#define PLUT_ECC_ERRCNT_NTB_PLUT_ECCSEI_EN_OFFSET              17
#define PLUT_ECC_ERRCNT_NTB_PLUT_ECCSEI_EN_MASK                0x20000

#define PLUT_ECC_ERRCNT_NTB_PLUT_ECCDEI_EN_OFFSET              18
#define PLUT_ECC_ERRCNT_NTB_PLUT_ECCDEI_EN_MASK                0x40000

#define PLUT_ECC_ERRCNT_NTB_PLUT_ECC_ERREVT_EN_OFFSET          19
#define PLUT_ECC_ERRCNT_NTB_PLUT_ECC_ERREVT_EN_MASK            0x80000

#define PLUT_ECC_ERRCNT_NTB_PLUT_ECCDEI_WAY_OFFSET             20
#define PLUT_ECC_ERRCNT_NTB_PLUT_ECCDEI_WAY_MASK               0x100000

#define PLUT_ECC_ERRCNT_Reserved_31_21_OFFSET                  21
#define PLUT_ECC_ERRCNT_Reserved_31_21_MASK                    0xffe00000

typedef union {
  struct {
    UINT32                                      PLUT_ECC_SECNT:8;
    UINT32                                      PLUT_ECC_DECNT:8;
    UINT32                                 PLUT_ECCERR_DROP_EN:1;
    UINT32                                  NTB_PLUT_ECCSEI_EN:1;
    UINT32                                  NTB_PLUT_ECCDEI_EN:1;
    UINT32                              NTB_PLUT_ECC_ERREVT_EN:1;
    UINT32                                 NTB_PLUT_ECCDEI_WAY:1;
    UINT32                                      Reserved_31_21:11;
  } Field;
  UINT32 Value;
} PLUT_ECC_ERRCNT_STRUCT;

#define SMN_PLUT_ECC_ERRCNT_ADDRESS                                   0x40004fcUL
#define SMN_NTB0NBIO0_PLUT_ECC_ERRCNT_ADDRESS                         0x40004fcUL
#define SMN_NTB0NBIO1_PLUT_ECC_ERRCNT_ADDRESS                         0x42004fcUL
#define SMN_NTB1NBIO0_PLUT_ECC_ERRCNT_ADDRESS                         0x41004fcUL
#define SMN_NTB1NBIO1_PLUT_ECC_ERRCNT_ADDRESS                         0x43004fcUL


/***********************************************************
* Register Name : PLUT_STRIDE
************************************************************/

#define PLUT_STRIDE_PLUT_STRIDE_OFFSET                         0
#define PLUT_STRIDE_PLUT_STRIDE_MASK                           0xff

#define PLUT_STRIDE_Reserved_31_8_OFFSET                       8
#define PLUT_STRIDE_Reserved_31_8_MASK                         0xffffff00

typedef union {
  struct {
    UINT32                                         PLUT_STRIDE:8;
    UINT32                                       Reserved_31_8:24;
  } Field;
  UINT32 Value;
} PLUT_STRIDE_STRUCT;

#define SMN_PLUT_STRIDE_ADDRESS                                       0x40004f8UL
#define SMN_NTB0NBIO0_PLUT_STRIDE_ADDRESS                             0x40004f8UL
#define SMN_NTB0NBIO1_PLUT_STRIDE_ADDRESS                             0x42004f8UL
#define SMN_NTB1NBIO0_PLUT_STRIDE_ADDRESS                             0x41004f8UL
#define SMN_NTB1NBIO1_PLUT_STRIDE_ADDRESS                             0x43004f8UL


/***********************************************************
* Register Name : PLUT_TOTAL_ENTRY
************************************************************/

#define PLUT_TOTAL_ENTRY_PLUT_TOTAL_ENTRY_OFFSET               0
#define PLUT_TOTAL_ENTRY_PLUT_TOTAL_ENTRY_MASK                 0xf

#define PLUT_TOTAL_ENTRY_Reserved_31_4_OFFSET                  4
#define PLUT_TOTAL_ENTRY_Reserved_31_4_MASK                    0xfffffff0

typedef union {
  struct {
    UINT32                                    PLUT_TOTAL_ENTRY:4;
    UINT32                                       Reserved_31_4:28;
  } Field;
  UINT32 Value;
} PLUT_TOTAL_ENTRY_STRUCT;

#define SMN_PLUT_TOTAL_ENTRY_ADDRESS                                  0x40004f4UL
#define SMN_NTB0NBIO0_PLUT_TOTAL_ENTRY_ADDRESS                        0x40004f4UL
#define SMN_NTB0NBIO1_PLUT_TOTAL_ENTRY_ADDRESS                        0x42004f4UL
#define SMN_NTB1NBIO0_PLUT_TOTAL_ENTRY_ADDRESS                        0x41004f4UL
#define SMN_NTB1NBIO1_PLUT_TOTAL_ENTRY_ADDRESS                        0x43004f4UL


/***********************************************************
* Register Name : PMIRR_SDBSTA
************************************************************/

#define PMIRR_SDBSTA_PMIRR_SDBSTA_OFFSET                       0
#define PMIRR_SDBSTA_PMIRR_SDBSTA_MASK                         0xffff

#define PMIRR_SDBSTA_Reserved_31_16_OFFSET                     16
#define PMIRR_SDBSTA_Reserved_31_16_MASK                       0xffff0000

typedef union {
  struct {
    UINT32                                        PMIRR_SDBSTA:16;
    UINT32                                      Reserved_31_16:16;
  } Field;
  UINT32 Value;
} PMIRR_SDBSTA_STRUCT;

#define SMN_PMIRR_SDBSTA_ADDRESS                                      0x4000458UL
#define SMN_NTB0NBIO0_PMIRR_SDBSTA_ADDRESS                            0x4000458UL
#define SMN_NTB0NBIO1_PMIRR_SDBSTA_ADDRESS                            0x4200458UL
#define SMN_NTB1NBIO0_PMIRR_SDBSTA_ADDRESS                            0x4100458UL
#define SMN_NTB1NBIO1_PMIRR_SDBSTA_ADDRESS                            0x4300458UL


/***********************************************************
* Register Name : PPME_STA
************************************************************/

#define PPME_STA_PPME_STA_OFFSET                               0
#define PPME_STA_PPME_STA_MASK                                 0x1

#define PPME_STA_Reserved_31_1_OFFSET                          1
#define PPME_STA_Reserved_31_1_MASK                            0xfffffffe

typedef union {
  struct {
    UINT32                                            PPME_STA:1;
    UINT32                                       Reserved_31_1:31;
  } Field;
  UINT32 Value;
} PPME_STA_STRUCT;

#define SMN_PPME_STA_ADDRESS                                          0x4000480UL
#define SMN_NTB0NBIO0_PPME_STA_ADDRESS                                0x4000480UL
#define SMN_NTB0NBIO1_PPME_STA_ADDRESS                                0x4200480UL
#define SMN_NTB1NBIO0_PPME_STA_ADDRESS                                0x4100480UL
#define SMN_NTB1NBIO1_PPME_STA_ADDRESS                                0x4300480UL


/***********************************************************
* Register Name : PRDECERR_CTRL
************************************************************/

#define PRDECERR_CTRL_NTB_PLUT_WdataPchk_ERREVT_EN_OFFSET      0
#define PRDECERR_CTRL_NTB_PLUT_WdataPchk_ERREVT_EN_MASK        0x1

#define PRDECERR_CTRL_Reserved_31_1_OFFSET                     1
#define PRDECERR_CTRL_Reserved_31_1_MASK                       0xfffffffe

typedef union {
  struct {
    UINT32                        NTB_PLUT_WdataPchk_ERREVT_EN:1;
    UINT32                                       Reserved_31_1:31;
  } Field;
  UINT32 Value;
} PRDECERR_CTRL_STRUCT;

#define SMN_PRDECERR_CTRL_ADDRESS                                     0x4000508UL
#define SMN_NTB0NBIO0_PRDECERR_CTRL_ADDRESS                           0x4000508UL
#define SMN_NTB0NBIO1_PRDECERR_CTRL_ADDRESS                           0x4200508UL
#define SMN_NTB1NBIO0_PRDECERR_CTRL_ADDRESS                           0x4100508UL
#define SMN_NTB1NBIO1_PRDECERR_CTRL_ADDRESS                           0x4300508UL


/***********************************************************
* Register Name : PROMBARLMT
************************************************************/

#define PROMBARLMT_PROMBARLMT_OFFSET                           0
#define PROMBARLMT_PROMBARLMT_MASK                             0xffffffff

typedef union {
  struct {
    UINT32                                          PROMBARLMT:32;
  } Field;
  UINT32 Value;
} PROMBARLMT_STRUCT;

#define SMN_PROMBARLMT_ADDRESS                                        0x4000410UL
#define SMN_NTB0NBIO0_PROMBARLMT_ADDRESS                              0x4000410UL
#define SMN_NTB0NBIO1_PROMBARLMT_ADDRESS                              0x4200410UL
#define SMN_NTB1NBIO0_PROMBARLMT_ADDRESS                              0x4100410UL
#define SMN_NTB1NBIO1_PROMBARLMT_ADDRESS                              0x4300410UL


/***********************************************************
* Register Name : PROMBARXLAT_HI
************************************************************/

#define PROMBARXLAT_HI_PROMBARXLAT_HI_OFFSET                   0
#define PROMBARXLAT_HI_PROMBARXLAT_HI_MASK                     0xffffffff

typedef union {
  struct {
    UINT32                                      PROMBARXLAT_HI:32;
  } Field;
  UINT32 Value;
} PROMBARXLAT_HI_STRUCT;

#define SMN_PROMBARXLAT_HI_ADDRESS                                    0x400042cUL
#define SMN_NTB0NBIO0_PROMBARXLAT_HI_ADDRESS                          0x400042cUL
#define SMN_NTB0NBIO1_PROMBARXLAT_HI_ADDRESS                          0x420042cUL
#define SMN_NTB1NBIO0_PROMBARXLAT_HI_ADDRESS                          0x410042cUL
#define SMN_NTB1NBIO1_PROMBARXLAT_HI_ADDRESS                          0x430042cUL


/***********************************************************
* Register Name : PROMBARXLAT_LO
************************************************************/

#define PROMBARXLAT_LO_Reserved_4_0_OFFSET                     0
#define PROMBARXLAT_LO_Reserved_4_0_MASK                       0x1f

#define PROMBARXLAT_LO_PROMBARXLAT_LO_OFFSET                   5
#define PROMBARXLAT_LO_PROMBARXLAT_LO_MASK                     0xffffffe0

typedef union {
  struct {
    UINT32                                        Reserved_4_0:5;
    UINT32                                      PROMBARXLAT_LO:27;
  } Field;
  UINT32 Value;
} PROMBARXLAT_LO_STRUCT;

#define SMN_PROMBARXLAT_LO_ADDRESS                                    0x4000428UL
#define SMN_NTB0NBIO0_PROMBARXLAT_LO_ADDRESS                          0x4000428UL
#define SMN_NTB0NBIO1_PROMBARXLAT_LO_ADDRESS                          0x4200428UL
#define SMN_NTB1NBIO0_PROMBARXLAT_LO_ADDRESS                          0x4100428UL
#define SMN_NTB1NBIO1_PROMBARXLAT_LO_ADDRESS                          0x4300428UL


/***********************************************************
* Register Name : PSIDE_INFO
************************************************************/

#define PSIDE_INFO_PSIDE_INFO_OFFSET                           0
#define PSIDE_INFO_PSIDE_INFO_MASK                             0x1

#define PSIDE_INFO_PSIDE_EN_OFFSET                             1
#define PSIDE_INFO_PSIDE_EN_MASK                               0x2

#define PSIDE_INFO_Reserved_31_2_OFFSET                        2
#define PSIDE_INFO_Reserved_31_2_MASK                          0xfffffffc

typedef union {
  struct {
    UINT32                                          PSIDE_INFO:1;
    UINT32                                            PSIDE_EN:1;
    UINT32                                       Reserved_31_2:30;
  } Field;
  UINT32 Value;
} PSIDE_INFO_STRUCT;

#define SMN_PSIDE_INFO_ADDRESS                                        0x4000408UL
#define SMN_NTB0NBIO0_PSIDE_INFO_ADDRESS                              0x4000408UL
#define SMN_NTB0NBIO1_PSIDE_INFO_ADDRESS                              0x4200408UL
#define SMN_NTB1NBIO0_PSIDE_INFO_ADDRESS                              0x4100408UL
#define SMN_NTB1NBIO1_PSIDE_INFO_ADDRESS                              0x4300408UL


/***********************************************************
* Register Name : PSMU_SPADMUTEX
************************************************************/

#define PSMU_SPADMUTEX_PSMU_SPADMUTEXID_OFFSET                 0
#define PSMU_SPADMUTEX_PSMU_SPADMUTEXID_MASK                   0x7fffffff

#define PSMU_SPADMUTEX_PSMU_SPADMUTEXCLEAR_OFFSET              31
#define PSMU_SPADMUTEX_PSMU_SPADMUTEXCLEAR_MASK                0x80000000

typedef union {
  struct {
    UINT32                                    PSMU_SPADMUTEXID:31;
    UINT32                                 PSMU_SPADMUTEXCLEAR:1;
  } Field;
  UINT32 Value;
} PSMU_SPADMUTEX_STRUCT;

#define SMN_PSMU_SPADMUTEX_ADDRESS                                    0x40004b0UL
#define SMN_NTB0NBIO0_PSMU_SPADMUTEX_ADDRESS                          0x40004b0UL
#define SMN_NTB0NBIO1_PSMU_SPADMUTEX_ADDRESS                          0x42004b0UL
#define SMN_NTB1NBIO0_PSMU_SPADMUTEX_ADDRESS                          0x41004b0UL
#define SMN_NTB1NBIO1_PSMU_SPADMUTEX_ADDRESS                          0x43004b0UL


/***********************************************************
* Register Name : PSMU_SPADREG
************************************************************/

#define PSMU_SPADREG_PSMU_SPADREG_OFFSET                       0
#define PSMU_SPADREG_PSMU_SPADREG_MASK                         0xffffffff

typedef union {
  struct {
    UINT32                                        PSMU_SPADREG:32;
  } Field;
  UINT32 Value;
} PSMU_SPADREG_STRUCT;

#define SMN_PSMU_SPADREG_ADDRESS                                      0x40004b4UL
#define SMN_NTB0_N0NBIO0_PSMU_SPADREG_ADDRESS                         0x40004b4UL
#define SMN_NTB0_N0NBIO1_PSMU_SPADREG_ADDRESS                         0x42004b4UL
#define SMN_NTB0_N1NBIO0_PSMU_SPADREG_ADDRESS                         0x40004b8UL
#define SMN_NTB0_N1NBIO1_PSMU_SPADREG_ADDRESS                         0x42004b8UL
#define SMN_NTB0_N2NBIO0_PSMU_SPADREG_ADDRESS                         0x40004bcUL
#define SMN_NTB0_N2NBIO1_PSMU_SPADREG_ADDRESS                         0x42004bcUL
#define SMN_NTB0_N3NBIO0_PSMU_SPADREG_ADDRESS                         0x40004c0UL
#define SMN_NTB0_N3NBIO1_PSMU_SPADREG_ADDRESS                         0x42004c0UL
#define SMN_NTB1_N0NBIO0_PSMU_SPADREG_ADDRESS                         0x41004b4UL
#define SMN_NTB1_N0NBIO1_PSMU_SPADREG_ADDRESS                         0x43004b4UL
#define SMN_NTB1_N1NBIO0_PSMU_SPADREG_ADDRESS                         0x41004b8UL
#define SMN_NTB1_N1NBIO1_PSMU_SPADREG_ADDRESS                         0x43004b8UL
#define SMN_NTB1_N2NBIO0_PSMU_SPADREG_ADDRESS                         0x41004bcUL
#define SMN_NTB1_N2NBIO1_PSMU_SPADREG_ADDRESS                         0x43004bcUL
#define SMN_NTB1_N3NBIO0_PSMU_SPADREG_ADDRESS                         0x41004c0UL
#define SMN_NTB1_N3NBIO1_PSMU_SPADREG_ADDRESS                         0x43004c0UL


/***********************************************************
* Register Name : PTAGRAM_ECC_ERRCNT
************************************************************/

#define PTAGRAM_ECC_ERRCNT_PTAGRAM_ECC_SECNT_OFFSET            0
#define PTAGRAM_ECC_ERRCNT_PTAGRAM_ECC_SECNT_MASK              0xff

#define PTAGRAM_ECC_ERRCNT_PTAGRAM_ECC_DECNT_OFFSET            8
#define PTAGRAM_ECC_ERRCNT_PTAGRAM_ECC_DECNT_MASK              0xff00

#define PTAGRAM_ECC_ERRCNT_Reserved_16_16_OFFSET               16
#define PTAGRAM_ECC_ERRCNT_Reserved_16_16_MASK                 0x10000

#define PTAGRAM_ECC_ERRCNT_PTAGRAM_ECCSEI_EN_OFFSET            17
#define PTAGRAM_ECC_ERRCNT_PTAGRAM_ECCSEI_EN_MASK              0x20000

#define PTAGRAM_ECC_ERRCNT_PTAGRAM_ECCDEI_EN_OFFSET            18
#define PTAGRAM_ECC_ERRCNT_PTAGRAM_ECCDEI_EN_MASK              0x40000

#define PTAGRAM_ECC_ERRCNT_PTAGRAM_ECC_ERREVT_EN_OFFSET        19
#define PTAGRAM_ECC_ERRCNT_PTAGRAM_ECC_ERREVT_EN_MASK          0x80000

#define PTAGRAM_ECC_ERRCNT_PTAGRAM_ECCDEI_WAY_OFFSET           20
#define PTAGRAM_ECC_ERRCNT_PTAGRAM_ECCDEI_WAY_MASK             0x100000

#define PTAGRAM_ECC_ERRCNT_Reserved_31_21_OFFSET               21
#define PTAGRAM_ECC_ERRCNT_Reserved_31_21_MASK                 0xffe00000

typedef union {
  struct {
    UINT32                                   PTAGRAM_ECC_SECNT:8;
    UINT32                                   PTAGRAM_ECC_DECNT:8;
    UINT32                                      Reserved_16_16:1;
    UINT32                                   PTAGRAM_ECCSEI_EN:1;
    UINT32                                   PTAGRAM_ECCDEI_EN:1;
    UINT32                               PTAGRAM_ECC_ERREVT_EN:1;
    UINT32                                  PTAGRAM_ECCDEI_WAY:1;
    UINT32                                      Reserved_31_21:11;
  } Field;
  UINT32 Value;
} PTAGRAM_ECC_ERRCNT_STRUCT;

#define SMN_PTAGRAM_ECC_ERRCNT_ADDRESS                                0x4000500UL
#define SMN_NTB0NBIO0_PTAGRAM_ECC_ERRCNT_ADDRESS                      0x4000500UL
#define SMN_NTB0NBIO1_PTAGRAM_ECC_ERRCNT_ADDRESS                      0x4200500UL
#define SMN_NTB1NBIO0_PTAGRAM_ECC_ERRCNT_ADDRESS                      0x4100500UL
#define SMN_NTB1NBIO1_PTAGRAM_ECC_ERRCNT_ADDRESS                      0x4300500UL


/***********************************************************
* Register Name : P_RSPNUM
************************************************************/

#define P_RSPNUM_PWRRSP_NUM_OFFSET                             0
#define P_RSPNUM_PWRRSP_NUM_MASK                               0xffff

#define P_RSPNUM_PRDRSP_NUM_OFFSET                             16
#define P_RSPNUM_PRDRSP_NUM_MASK                               0xffff0000

typedef union {
  struct {
    UINT32                                          PWRRSP_NUM:16;
    UINT32                                          PRDRSP_NUM:16;
  } Field;
  UINT32 Value;
} P_RSPNUM_STRUCT;

#define SMN_P_RSPNUM_ADDRESS                                          0x40004a8UL
#define SMN_NTB0NBIO0_P_RSPNUM_ADDRESS                                0x40004a8UL
#define SMN_NTB0NBIO1_P_RSPNUM_ADDRESS                                0x42004a8UL
#define SMN_NTB1NBIO0_P_RSPNUM_ADDRESS                                0x41004a8UL
#define SMN_NTB1NBIO1_P_RSPNUM_ADDRESS                                0x43004a8UL


/***********************************************************
* Register Name : P_SINRST
************************************************************/

#define P_SINRST_P_SINRST_OFFSET                               0
#define P_SINRST_P_SINRST_MASK                                 0x1

#define P_SINRST_Reserved_31_1_OFFSET                          1
#define P_SINRST_Reserved_31_1_MASK                            0xfffffffe

typedef union {
  struct {
    UINT32                                            P_SINRST:1;
    UINT32                                       Reserved_31_1:31;
  } Field;
  UINT32 Value;
} P_SINRST_STRUCT;

#define SMN_P_SINRST_ADDRESS                                          0x40004a4UL
#define SMN_NTB0NBIO0_P_SINRST_ADDRESS                                0x40004a4UL
#define SMN_NTB0NBIO1_P_SINRST_ADDRESS                                0x42004a4UL
#define SMN_NTB1NBIO0_P_SINRST_ADDRESS                                0x41004a4UL
#define SMN_NTB1NBIO1_P_SINRST_ADDRESS                                0x43004a4UL


/***********************************************************
* Register Name : P_SMU_ACK
************************************************************/

#define P_SMU_ACK_Reserved_0_0_OFFSET                          0
#define P_SMU_ACK_Reserved_0_0_MASK                            0x1

#define P_SMU_ACK_PRST_ACK_OFFSET                              1
#define P_SMU_ACK_PRST_ACK_MASK                                0x2

#define P_SMU_ACK_PD3_ACK_OFFSET                               2
#define P_SMU_ACK_PD3_ACK_MASK                                 0x4

#define P_SMU_ACK_PPMETO_ACK_OFFSET                            3
#define P_SMU_ACK_PPMETO_ACK_MASK                              0x8

#define P_SMU_ACK_PD0_ACK_OFFSET                               4
#define P_SMU_ACK_PD0_ACK_MASK                                 0x10

#define P_SMU_ACK_PMISC0_ACK_OFFSET                            5
#define P_SMU_ACK_PMISC0_ACK_MASK                              0xffe0

#define P_SMU_ACK_PMISC1_ACK_OFFSET                            16
#define P_SMU_ACK_PMISC1_ACK_MASK                              0xff0000

#define P_SMU_ACK_Reserved_31_24_OFFSET                        24
#define P_SMU_ACK_Reserved_31_24_MASK                          0xff000000

typedef union {
  struct {
    UINT32                                        Reserved_0_0:1;
    UINT32                                            PRST_ACK:1;
    UINT32                                             PD3_ACK:1;
    UINT32                                          PPMETO_ACK:1;
    UINT32                                             PD0_ACK:1;
    UINT32                                          PMISC0_ACK:11;
    UINT32                                          PMISC1_ACK:8;
    UINT32                                      Reserved_31_24:8;
  } Field;
  UINT32 Value;
} P_SMU_ACK_STRUCT;

#define SMN_P_SMU_ACK_ADDRESS                                         0x40004a0UL
#define SMN_NTB0NBIO0_P_SMU_ACK_ADDRESS                               0x40004a0UL
#define SMN_NTB0NBIO1_P_SMU_ACK_ADDRESS                               0x42004a0UL
#define SMN_NTB1NBIO0_P_SMU_ACK_ADDRESS                               0x41004a0UL
#define SMN_NTB1NBIO1_P_SMU_ACK_ADDRESS                               0x43004a0UL


/***********************************************************
* Register Name : SBAR1LMT
************************************************************/

#define SBAR1LMT_Reserved_11_0_OFFSET                          0
#define SBAR1LMT_Reserved_11_0_MASK                            0xfff

#define SBAR1LMT_SBAR1LMT_OFFSET                               12
#define SBAR1LMT_SBAR1LMT_MASK                                 0xfffff000

typedef union {
  struct {
    UINT32                                       Reserved_11_0:12;
    UINT32                                            SBAR1LMT:20;
  } Field;
  UINT32 Value;
} SBAR1LMT_STRUCT;

#define SMN_SBAR1LMT_ADDRESS                                          0x4000814UL
#define SMN_NTB0NBIO0_SBAR1LMT_ADDRESS                                0x4000814UL
#define SMN_NTB0NBIO1_SBAR1LMT_ADDRESS                                0x4200814UL
#define SMN_NTB1NBIO0_SBAR1LMT_ADDRESS                                0x4100814UL
#define SMN_NTB1NBIO1_SBAR1LMT_ADDRESS                                0x4300814UL


/***********************************************************
* Register Name : SBAR1XLAT_HI
************************************************************/

#define SBAR1XLAT_HI_SBAR1XLAT_HI_OFFSET                       0
#define SBAR1XLAT_HI_SBAR1XLAT_HI_MASK                         0xffffffff

typedef union {
  struct {
    UINT32                                        SBAR1XLAT_HI:32;
  } Field;
  UINT32 Value;
} SBAR1XLAT_HI_STRUCT;

#define SMN_SBAR1XLAT_HI_ADDRESS                                      0x4000834UL
#define SMN_NTB0NBIO0_SBAR1XLAT_HI_ADDRESS                            0x4000834UL
#define SMN_NTB0NBIO1_SBAR1XLAT_HI_ADDRESS                            0x4200834UL
#define SMN_NTB1NBIO0_SBAR1XLAT_HI_ADDRESS                            0x4100834UL
#define SMN_NTB1NBIO1_SBAR1XLAT_HI_ADDRESS                            0x4300834UL


/***********************************************************
* Register Name : SBAR1XLAT_LO
************************************************************/

#define SBAR1XLAT_LO_NS_CTRL_OFFSET                            0
#define SBAR1XLAT_LO_NS_CTRL_MASK                              0x3

#define SBAR1XLAT_LO_TPH_CTRL_OFFSET                           2
#define SBAR1XLAT_LO_TPH_CTRL_MASK                             0xc

#define SBAR1XLAT_LO_TPH_ST_OFFSET                             4
#define SBAR1XLAT_LO_TPH_ST_MASK                               0xff0

#define SBAR1XLAT_LO_SBAR1XLAT_LO_OFFSET                       12
#define SBAR1XLAT_LO_SBAR1XLAT_LO_MASK                         0xfffff000

typedef union {
  struct {
    UINT32                                             NS_CTRL:2;
    UINT32                                            TPH_CTRL:2;
    UINT32                                              TPH_ST:8;
    UINT32                                        SBAR1XLAT_LO:20;
  } Field;
  UINT32 Value;
} SBAR1XLAT_LO_STRUCT;

#define SMN_SBAR1XLAT_LO_ADDRESS                                      0x4000830UL
#define SMN_NTB0NBIO0_SBAR1XLAT_LO_ADDRESS                            0x4000830UL
#define SMN_NTB0NBIO1_SBAR1XLAT_LO_ADDRESS                            0x4200830UL
#define SMN_NTB1NBIO0_SBAR1XLAT_LO_ADDRESS                            0x4100830UL
#define SMN_NTB1NBIO1_SBAR1XLAT_LO_ADDRESS                            0x4300830UL


/***********************************************************
* Register Name : SBAR23LMT_HI
************************************************************/

#define SBAR23LMT_HI_SBAR23LMT_HI_OFFSET                       0
#define SBAR23LMT_HI_SBAR23LMT_HI_MASK                         0xffffffff

typedef union {
  struct {
    UINT32                                        SBAR23LMT_HI:32;
  } Field;
  UINT32 Value;
} SBAR23LMT_HI_STRUCT;

#define SMN_SBAR23LMT_HI_ADDRESS                                      0x400081cUL
#define SMN_NTB0NBIO0_SBAR23LMT_HI_ADDRESS                            0x400081cUL
#define SMN_NTB0NBIO1_SBAR23LMT_HI_ADDRESS                            0x420081cUL
#define SMN_NTB1NBIO0_SBAR23LMT_HI_ADDRESS                            0x410081cUL
#define SMN_NTB1NBIO1_SBAR23LMT_HI_ADDRESS                            0x430081cUL


/***********************************************************
* Register Name : SBAR23LMT_LO
************************************************************/

#define SBAR23LMT_LO_Reserved_11_0_OFFSET                      0
#define SBAR23LMT_LO_Reserved_11_0_MASK                        0xfff

#define SBAR23LMT_LO_SBAR23LMT_LO_OFFSET                       12
#define SBAR23LMT_LO_SBAR23LMT_LO_MASK                         0xfffff000

typedef union {
  struct {
    UINT32                                       Reserved_11_0:12;
    UINT32                                        SBAR23LMT_LO:20;
  } Field;
  UINT32 Value;
} SBAR23LMT_LO_STRUCT;

#define SMN_SBAR23LMT_LO_ADDRESS                                      0x4000818UL
#define SMN_NTB0NBIO0_SBAR23LMT_LO_ADDRESS                            0x4000818UL
#define SMN_NTB0NBIO1_SBAR23LMT_LO_ADDRESS                            0x4200818UL
#define SMN_NTB1NBIO0_SBAR23LMT_LO_ADDRESS                            0x4100818UL
#define SMN_NTB1NBIO1_SBAR23LMT_LO_ADDRESS                            0x4300818UL


/***********************************************************
* Register Name : SBAR23XLAT1
************************************************************/

#define SBAR23XLAT1_TPH_PH_CTRL_OFFSET                         0
#define SBAR23XLAT1_TPH_PH_CTRL_MASK                           0x1

#define SBAR23XLAT1_TPH_PH_OFFSET                              1
#define SBAR23XLAT1_TPH_PH_MASK                                0x6

#define SBAR23XLAT1_Reserved_31_3_OFFSET                       3
#define SBAR23XLAT1_Reserved_31_3_MASK                         0xfffffff8

typedef union {
  struct {
    UINT32                                         TPH_PH_CTRL:1;
    UINT32                                              TPH_PH:2;
    UINT32                                       Reserved_31_3:29;
  } Field;
  UINT32 Value;
} SBAR23XLAT1_STRUCT;

#define SMN_SBAR23XLAT1_ADDRESS                                       0x4000848UL
#define SMN_NTB0NBIO0_SBAR23XLAT1_ADDRESS                             0x4000848UL
#define SMN_NTB0NBIO1_SBAR23XLAT1_ADDRESS                             0x4200848UL
#define SMN_NTB1NBIO0_SBAR23XLAT1_ADDRESS                             0x4100848UL
#define SMN_NTB1NBIO1_SBAR23XLAT1_ADDRESS                             0x4300848UL


/***********************************************************
* Register Name : SBAR23XLAT_HI
************************************************************/

#define SBAR23XLAT_HI_SBAR23XLAT_HI_OFFSET                     0
#define SBAR23XLAT_HI_SBAR23XLAT_HI_MASK                       0xffffffff

typedef union {
  struct {
    UINT32                                       SBAR23XLAT_HI:32;
  } Field;
  UINT32 Value;
} SBAR23XLAT_HI_STRUCT;

#define SMN_SBAR23XLAT_HI_ADDRESS                                     0x400083cUL
#define SMN_NTB0NBIO0_SBAR23XLAT_HI_ADDRESS                           0x400083cUL
#define SMN_NTB0NBIO1_SBAR23XLAT_HI_ADDRESS                           0x420083cUL
#define SMN_NTB1NBIO0_SBAR23XLAT_HI_ADDRESS                           0x410083cUL
#define SMN_NTB1NBIO1_SBAR23XLAT_HI_ADDRESS                           0x430083cUL


/***********************************************************
* Register Name : SBAR23XLAT_LO
************************************************************/

#define SBAR23XLAT_LO_NS_CTRL_OFFSET                           0
#define SBAR23XLAT_LO_NS_CTRL_MASK                             0x3

#define SBAR23XLAT_LO_TPH_CTRL_OFFSET                          2
#define SBAR23XLAT_LO_TPH_CTRL_MASK                            0xc

#define SBAR23XLAT_LO_TPH_ST_OFFSET                            4
#define SBAR23XLAT_LO_TPH_ST_MASK                              0xff0

#define SBAR23XLAT_LO_SBAR23XLAT_LO_OFFSET                     12
#define SBAR23XLAT_LO_SBAR23XLAT_LO_MASK                       0xfffff000

typedef union {
  struct {
    UINT32                                             NS_CTRL:2;
    UINT32                                            TPH_CTRL:2;
    UINT32                                              TPH_ST:8;
    UINT32                                       SBAR23XLAT_LO:20;
  } Field;
  UINT32 Value;
} SBAR23XLAT_LO_STRUCT;

#define SMN_SBAR23XLAT_LO_ADDRESS                                     0x4000838UL
#define SMN_NTB0NBIO0_SBAR23XLAT_LO_ADDRESS                           0x4000838UL
#define SMN_NTB0NBIO1_SBAR23XLAT_LO_ADDRESS                           0x4200838UL
#define SMN_NTB1NBIO0_SBAR23XLAT_LO_ADDRESS                           0x4100838UL
#define SMN_NTB1NBIO1_SBAR23XLAT_LO_ADDRESS                           0x4300838UL


/***********************************************************
* Register Name : SBAR45LMT_HI
************************************************************/

#define SBAR45LMT_HI_SBAR45LMT_HI_OFFSET                       0
#define SBAR45LMT_HI_SBAR45LMT_HI_MASK                         0xffffffff

typedef union {
  struct {
    UINT32                                        SBAR45LMT_HI:32;
  } Field;
  UINT32 Value;
} SBAR45LMT_HI_STRUCT;

#define SMN_SBAR45LMT_HI_ADDRESS                                      0x4000824UL
#define SMN_NTB0NBIO0_SBAR45LMT_HI_ADDRESS                            0x4000824UL
#define SMN_NTB0NBIO1_SBAR45LMT_HI_ADDRESS                            0x4200824UL
#define SMN_NTB1NBIO0_SBAR45LMT_HI_ADDRESS                            0x4100824UL
#define SMN_NTB1NBIO1_SBAR45LMT_HI_ADDRESS                            0x4300824UL


/***********************************************************
* Register Name : SBAR45LMT_LO
************************************************************/

#define SBAR45LMT_LO_Reserved_11_0_OFFSET                      0
#define SBAR45LMT_LO_Reserved_11_0_MASK                        0xfff

#define SBAR45LMT_LO_SBAR45LMT_LO_OFFSET                       12
#define SBAR45LMT_LO_SBAR45LMT_LO_MASK                         0xfffff000

typedef union {
  struct {
    UINT32                                       Reserved_11_0:12;
    UINT32                                        SBAR45LMT_LO:20;
  } Field;
  UINT32 Value;
} SBAR45LMT_LO_STRUCT;

#define SMN_SBAR45LMT_LO_ADDRESS                                      0x4000820UL
#define SMN_NTB0NBIO0_SBAR45LMT_LO_ADDRESS                            0x4000820UL
#define SMN_NTB0NBIO1_SBAR45LMT_LO_ADDRESS                            0x4200820UL
#define SMN_NTB1NBIO0_SBAR45LMT_LO_ADDRESS                            0x4100820UL
#define SMN_NTB1NBIO1_SBAR45LMT_LO_ADDRESS                            0x4300820UL


/***********************************************************
* Register Name : SBAR45XLAT1
************************************************************/

#define SBAR45XLAT1_TPH_PH_CTRL_OFFSET                         0
#define SBAR45XLAT1_TPH_PH_CTRL_MASK                           0x1

#define SBAR45XLAT1_TPH_PH_OFFSET                              1
#define SBAR45XLAT1_TPH_PH_MASK                                0x6

#define SBAR45XLAT1_Reserved_31_3_OFFSET                       3
#define SBAR45XLAT1_Reserved_31_3_MASK                         0xfffffff8

typedef union {
  struct {
    UINT32                                         TPH_PH_CTRL:1;
    UINT32                                              TPH_PH:2;
    UINT32                                       Reserved_31_3:29;
  } Field;
  UINT32 Value;
} SBAR45XLAT1_STRUCT;

#define SMN_SBAR45XLAT1_ADDRESS                                       0x400084cUL
#define SMN_NTB0NBIO0_SBAR45XLAT1_ADDRESS                             0x400084cUL
#define SMN_NTB0NBIO1_SBAR45XLAT1_ADDRESS                             0x420084cUL
#define SMN_NTB1NBIO0_SBAR45XLAT1_ADDRESS                             0x410084cUL
#define SMN_NTB1NBIO1_SBAR45XLAT1_ADDRESS                             0x430084cUL


/***********************************************************
* Register Name : SBAR45XLAT_HI
************************************************************/

#define SBAR45XLAT_HI_SBAR45XLAT_HI_OFFSET                     0
#define SBAR45XLAT_HI_SBAR45XLAT_HI_MASK                       0xffffffff

typedef union {
  struct {
    UINT32                                       SBAR45XLAT_HI:32;
  } Field;
  UINT32 Value;
} SBAR45XLAT_HI_STRUCT;

#define SMN_SBAR45XLAT_HI_ADDRESS                                     0x4000844UL
#define SMN_NTB0NBIO0_SBAR45XLAT_HI_ADDRESS                           0x4000844UL
#define SMN_NTB0NBIO1_SBAR45XLAT_HI_ADDRESS                           0x4200844UL
#define SMN_NTB1NBIO0_SBAR45XLAT_HI_ADDRESS                           0x4100844UL
#define SMN_NTB1NBIO1_SBAR45XLAT_HI_ADDRESS                           0x4300844UL


/***********************************************************
* Register Name : SBAR45XLAT_LO
************************************************************/

#define SBAR45XLAT_LO_NS_CTRL_OFFSET                           0
#define SBAR45XLAT_LO_NS_CTRL_MASK                             0x3

#define SBAR45XLAT_LO_TPH_CTRL_OFFSET                          2
#define SBAR45XLAT_LO_TPH_CTRL_MASK                            0xc

#define SBAR45XLAT_LO_TPH_ST_OFFSET                            4
#define SBAR45XLAT_LO_TPH_ST_MASK                              0xff0

#define SBAR45XLAT_LO_SBAR45XLAT_LO_OFFSET                     12
#define SBAR45XLAT_LO_SBAR45XLAT_LO_MASK                       0xfffff000

typedef union {
  struct {
    UINT32                                             NS_CTRL:2;
    UINT32                                            TPH_CTRL:2;
    UINT32                                              TPH_ST:8;
    UINT32                                       SBAR45XLAT_LO:20;
  } Field;
  UINT32 Value;
} SBAR45XLAT_LO_STRUCT;

#define SMN_SBAR45XLAT_LO_ADDRESS                                     0x4000840UL
#define SMN_NTB0NBIO0_SBAR45XLAT_LO_ADDRESS                           0x4000840UL
#define SMN_NTB0NBIO1_SBAR45XLAT_LO_ADDRESS                           0x4200840UL
#define SMN_NTB1NBIO0_SBAR45XLAT_LO_ADDRESS                           0x4100840UL
#define SMN_NTB1NBIO1_SBAR45XLAT_LO_ADDRESS                           0x4300840UL


/***********************************************************
* Register Name : SDBFM
************************************************************/

#define SDBFM_SDBFM_OFFSET                                     0
#define SDBFM_SDBFM_MASK                                       0xffff

#define SDBFM_Reserved_31_16_OFFSET                            16
#define SDBFM_Reserved_31_16_MASK                              0xffff0000

typedef union {
  struct {
    UINT32                                               SDBFM:16;
    UINT32                                      Reserved_31_16:16;
  } Field;
  UINT32 Value;
} SDBFM_STRUCT;

#define SMN_SDBFM_ADDRESS                                             0x4000850UL
#define SMN_NTB0NBIO0_SDBFM_ADDRESS                                   0x4000850UL
#define SMN_NTB0NBIO1_SDBFM_ADDRESS                                   0x4200850UL
#define SMN_NTB1NBIO0_SDBFM_ADDRESS                                   0x4100850UL
#define SMN_NTB1NBIO1_SDBFM_ADDRESS                                   0x4300850UL


/***********************************************************
* Register Name : SDBMASK
************************************************************/

#define SDBMASK_SDBMASK_OFFSET                                 0
#define SDBMASK_SDBMASK_MASK                                   0xffff

#define SDBMASK_Reserved_31_16_OFFSET                          16
#define SDBMASK_Reserved_31_16_MASK                            0xffff0000

typedef union {
  struct {
    UINT32                                             SDBMASK:16;
    UINT32                                      Reserved_31_16:16;
  } Field;
  UINT32 Value;
} SDBMASK_STRUCT;

#define SMN_SDBMASK_ADDRESS                                           0x400085cUL
#define SMN_NTB0NBIO0_SDBMASK_ADDRESS                                 0x400085cUL
#define SMN_NTB0NBIO1_SDBMASK_ADDRESS                                 0x420085cUL
#define SMN_NTB1NBIO0_SDBMASK_ADDRESS                                 0x410085cUL
#define SMN_NTB1NBIO1_SDBMASK_ADDRESS                                 0x430085cUL


/***********************************************************
* Register Name : SDBREQ
************************************************************/

#define SDBREQ_SDBREQ_OFFSET                                   0
#define SDBREQ_SDBREQ_MASK                                     0xffff

#define SDBREQ_Reserved_31_16_OFFSET                           16
#define SDBREQ_Reserved_31_16_MASK                             0xffff0000

typedef union {
  struct {
    UINT32                                              SDBREQ:16;
    UINT32                                      Reserved_31_16:16;
  } Field;
  UINT32 Value;
} SDBREQ_STRUCT;

#define SMN_SDBREQ_ADDRESS                                            0x4000854UL
#define SMN_NTB0NBIO0_SDBREQ_ADDRESS                                  0x4000854UL
#define SMN_NTB0NBIO1_SDBREQ_ADDRESS                                  0x4200854UL
#define SMN_NTB1NBIO0_SDBREQ_ADDRESS                                  0x4100854UL
#define SMN_NTB1NBIO1_SDBREQ_ADDRESS                                  0x4300854UL


/***********************************************************
* Register Name : SDBSTA
************************************************************/

#define SDBSTA_SDBSTA_OFFSET                                   0
#define SDBSTA_SDBSTA_MASK                                     0xffff

#define SDBSTA_Reserved_31_16_OFFSET                           16
#define SDBSTA_Reserved_31_16_MASK                             0xffff0000

typedef union {
  struct {
    UINT32                                              SDBSTA:16;
    UINT32                                      Reserved_31_16:16;
  } Field;
  UINT32 Value;
} SDBSTA_STRUCT;

#define SMN_SDBSTA_ADDRESS                                            0x4000860UL
#define SMN_NTB0NBIO0_SDBSTA_ADDRESS                                  0x4000860UL
#define SMN_NTB0NBIO1_SDBSTA_ADDRESS                                  0x4200860UL
#define SMN_NTB1NBIO0_SDBSTA_ADDRESS                                  0x4100860UL
#define SMN_NTB1NBIO1_SDBSTA_ADDRESS                                  0x4300860UL


/***********************************************************
* Register Name : SFLUSH_MOD
************************************************************/

#define SFLUSH_MOD_SFLUSH_MOD_OFFSET                           0
#define SFLUSH_MOD_SFLUSH_MOD_MASK                             0x1

#define SFLUSH_MOD_Reserved_31_1_OFFSET                        1
#define SFLUSH_MOD_Reserved_31_1_MASK                          0xfffffffe

typedef union {
  struct {
    UINT32                                          SFLUSH_MOD:1;
    UINT32                                       Reserved_31_1:31;
  } Field;
  UINT32 Value;
} SFLUSH_MOD_STRUCT;

#define SMN_SFLUSH_MOD_ADDRESS                                        0x400089cUL
#define SMN_NTB0NBIO0_SFLUSH_MOD_ADDRESS                              0x400089cUL
#define SMN_NTB0NBIO1_SFLUSH_MOD_ADDRESS                              0x420089cUL
#define SMN_NTB1NBIO0_SFLUSH_MOD_ADDRESS                              0x410089cUL
#define SMN_NTB1NBIO1_SFLUSH_MOD_ADDRESS                              0x430089cUL


/***********************************************************
* Register Name : SFLUSH_TRIG
************************************************************/

#define SFLUSH_TRIG_SFLUSH_TRIG_OFFSET                         0
#define SFLUSH_TRIG_SFLUSH_TRIG_MASK                           0x1

#define SFLUSH_TRIG_Reserved_31_1_OFFSET                       1
#define SFLUSH_TRIG_Reserved_31_1_MASK                         0xfffffffe

typedef union {
  struct {
    UINT32                                         SFLUSH_TRIG:1;
    UINT32                                       Reserved_31_1:31;
  } Field;
  UINT32 Value;
} SFLUSH_TRIG_STRUCT;

#define SMN_SFLUSH_TRIG_ADDRESS                                       0x4000898UL
#define SMN_NTB0NBIO0_SFLUSH_TRIG_ADDRESS                             0x4000898UL
#define SMN_NTB0NBIO1_SFLUSH_TRIG_ADDRESS                             0x4200898UL
#define SMN_NTB1NBIO0_SFLUSH_TRIG_ADDRESS                             0x4100898UL
#define SMN_NTB1NBIO1_SFLUSH_TRIG_ADDRESS                             0x4300898UL


/***********************************************************
* Register Name : SINTMASK
************************************************************/

#define SINTMASK_SFLUSH_INTMASK_OFFSET                         0
#define SINTMASK_SFLUSH_INTMASK_MASK                           0x1

#define SINTMASK_SRESETNOTICE_INTMASK_OFFSET                   1
#define SINTMASK_SRESETNOTICE_INTMASK_MASK                     0x2

#define SINTMASK_SD3NOTICE_INTMASK_OFFSET                      2
#define SINTMASK_SD3NOTICE_INTMASK_MASK                        0x4

#define SINTMASK_SPMETONOTICE_INTMASK_OFFSET                   3
#define SINTMASK_SPMETONOTICE_INTMASK_MASK                     0x8

#define SINTMASK_SD0NOTICE_INTMASK_OFFSET                      4
#define SINTMASK_SD0NOTICE_INTMASK_MASK                        0x10

#define SINTMASK_SMISC0_INTMASK_OFFSET                         5
#define SINTMASK_SMISC0_INTMASK_MASK                           0xffe0

#define SINTMASK_SMISC1_INTMASK_OFFSET                         16
#define SINTMASK_SMISC1_INTMASK_MASK                           0xff0000

#define SINTMASK_SSDPERR_INTMASK_OFFSET                        24
#define SINTMASK_SSDPERR_INTMASK_MASK                          0xf000000

#define SINTMASK_SECCSE_INTMASK_OFFSET                         28
#define SINTMASK_SECCSE_INTMASK_MASK                           0x10000000

#define SINTMASK_SECCDE_INTMASK_OFFSET                         29
#define SINTMASK_SECCDE_INTMASK_MASK                           0x20000000

#define SINTMASK_SLUT_INTMASK_OFFSET                           30
#define SINTMASK_SLUT_INTMASK_MASK                             0x40000000

#define SINTMASK_Reserved_31_31_OFFSET                         31
#define SINTMASK_Reserved_31_31_MASK                           0x80000000

typedef union {
  struct {
    UINT32                                      SFLUSH_INTMASK:1;
    UINT32                                SRESETNOTICE_INTMASK:1;
    UINT32                                   SD3NOTICE_INTMASK:1;
    UINT32                                SPMETONOTICE_INTMASK:1;
    UINT32                                   SD0NOTICE_INTMASK:1;
    UINT32                                      SMISC0_INTMASK:11;
    UINT32                                      SMISC1_INTMASK:8;
    UINT32                                     SSDPERR_INTMASK:4;
    UINT32                                      SECCSE_INTMASK:1;
    UINT32                                      SECCDE_INTMASK:1;
    UINT32                                        SLUT_INTMASK:1;
    UINT32                                      Reserved_31_31:1;
  } Field;
  UINT32 Value;
} SINTMASK_STRUCT;

#define SMN_SINTMASK_ADDRESS                                          0x4000870UL
#define SMN_NTB0NBIO0_SINTMASK_ADDRESS                                0x4000870UL
#define SMN_NTB0NBIO1_SINTMASK_ADDRESS                                0x4200870UL
#define SMN_NTB1NBIO0_SINTMASK_ADDRESS                                0x4100870UL
#define SMN_NTB1NBIO1_SINTMASK_ADDRESS                                0x4300870UL


/***********************************************************
* Register Name : SINTSTA
************************************************************/

#define SINTSTA_SFLUSH_INTSTA_OFFSET                           0
#define SINTSTA_SFLUSH_INTSTA_MASK                             0x1

#define SINTSTA_SRESETNOTICE_INTSTA_OFFSET                     1
#define SINTSTA_SRESETNOTICE_INTSTA_MASK                       0x2

#define SINTSTA_SD3NOTICE_INTSTA_OFFSET                        2
#define SINTSTA_SD3NOTICE_INTSTA_MASK                          0x4

#define SINTSTA_SPMETONOTICE_INTSTA_OFFSET                     3
#define SINTSTA_SPMETONOTICE_INTSTA_MASK                       0x8

#define SINTSTA_SD0NOTICE_INTSTA_OFFSET                        4
#define SINTSTA_SD0NOTICE_INTSTA_MASK                          0x10

#define SINTSTA_SMISC0_INTSTA_OFFSET                           5
#define SINTSTA_SMISC0_INTSTA_MASK                             0xffe0

#define SINTSTA_SMISC1_INTSTA_OFFSET                           16
#define SINTSTA_SMISC1_INTSTA_MASK                             0xff0000

#define SINTSTA_SSDPERR_INTSTA_OFFSET                          24
#define SINTSTA_SSDPERR_INTSTA_MASK                            0xf000000

#define SINTSTA_SECCSE_INTSTA_OFFSET                           28
#define SINTSTA_SECCSE_INTSTA_MASK                             0x10000000

#define SINTSTA_SECCDE_INTSTA_OFFSET                           29
#define SINTSTA_SECCDE_INTSTA_MASK                             0x20000000

#define SINTSTA_SLUT_INTSTA_OFFSET                             30
#define SINTSTA_SLUT_INTSTA_MASK                               0x40000000

#define SINTSTA_Reserved_31_31_OFFSET                          31
#define SINTSTA_Reserved_31_31_MASK                            0x80000000

typedef union {
  struct {
    UINT32                                       SFLUSH_INTSTA:1;
    UINT32                                 SRESETNOTICE_INTSTA:1;
    UINT32                                    SD3NOTICE_INTSTA:1;
    UINT32                                 SPMETONOTICE_INTSTA:1;
    UINT32                                    SD0NOTICE_INTSTA:1;
    UINT32                                       SMISC0_INTSTA:11;
    UINT32                                       SMISC1_INTSTA:8;
    UINT32                                      SSDPERR_INTSTA:4;
    UINT32                                       SECCSE_INTSTA:1;
    UINT32                                       SECCDE_INTSTA:1;
    UINT32                                         SLUT_INTSTA:1;
    UINT32                                      Reserved_31_31:1;
  } Field;
  UINT32 Value;
} SINTSTA_STRUCT;

#define SMN_SINTSTA_ADDRESS                                           0x4000874UL
#define SMN_NTB0NBIO0_SINTSTA_ADDRESS                                 0x4000874UL
#define SMN_NTB0NBIO1_SINTSTA_ADDRESS                                 0x4200874UL
#define SMN_NTB1NBIO0_SINTSTA_ADDRESS                                 0x4100874UL
#define SMN_NTB1NBIO1_SINTSTA_ADDRESS                                 0x4300874UL


/***********************************************************
* Register Name : SLTR_LATENCY
************************************************************/

#define SLTR_LATENCY_SLTR_NSNP_LTNCY_OFFSET                    0
#define SLTR_LATENCY_SLTR_NSNP_LTNCY_MASK                      0xffff

#define SLTR_LATENCY_SLTR_SNP_LTNCY_OFFSET                     16
#define SLTR_LATENCY_SLTR_SNP_LTNCY_MASK                       0xffff0000

typedef union {
  struct {
    UINT32                                     SLTR_NSNP_LTNCY:16;
    UINT32                                      SLTR_SNP_LTNCY:16;
  } Field;
  UINT32 Value;
} SLTR_LATENCY_STRUCT;

#define SMN_SLTR_LATENCY_ADDRESS                                      0x4000894UL
#define SMN_NTB0NBIO0_SLTR_LATENCY_ADDRESS                            0x4000894UL
#define SMN_NTB0NBIO1_SLTR_LATENCY_ADDRESS                            0x4200894UL
#define SMN_NTB1NBIO0_SLTR_LATENCY_ADDRESS                            0x4100894UL
#define SMN_NTB1NBIO1_SLTR_LATENCY_ADDRESS                            0x4300894UL


/***********************************************************
* Register Name : SLUT_CTRL
************************************************************/

#define SLUT_CTRL_SLUT_EN_OFFSET                               0
#define SLUT_CTRL_SLUT_EN_MASK                                 0x1

#define SLUT_CTRL_SLUT_ALLOC_OFFSET                            1
#define SLUT_CTRL_SLUT_ALLOC_MASK                              0x2

#define SLUT_CTRL_Reserved_7_2_OFFSET                          2
#define SLUT_CTRL_Reserved_7_2_MASK                            0xfc

#define SLUT_CTRL_SLUT_SIZEUNIT_OFFSET                         8
#define SLUT_CTRL_SLUT_SIZEUNIT_MASK                           0xff00

#define SLUT_CTRL_Reserved_31_16_OFFSET                        16
#define SLUT_CTRL_Reserved_31_16_MASK                          0xffff0000

typedef union {
  struct {
    UINT32                                             SLUT_EN:1;
    UINT32                                          SLUT_ALLOC:1;
    UINT32                                        Reserved_7_2:6;
    UINT32                                       SLUT_SIZEUNIT:8;
    UINT32                                      Reserved_31_16:16;
  } Field;
  UINT32 Value;
} SLUT_CTRL_STRUCT;

#define SMN_SLUT_CTRL_ADDRESS                                         0x40008f0UL
#define SMN_NTB0NBIO0_SLUT_CTRL_ADDRESS                               0x40008f0UL
#define SMN_NTB0NBIO1_SLUT_CTRL_ADDRESS                               0x42008f0UL
#define SMN_NTB1NBIO0_SLUT_CTRL_ADDRESS                               0x41008f0UL
#define SMN_NTB1NBIO1_SLUT_CTRL_ADDRESS                               0x43008f0UL


/***********************************************************
* Register Name : SLUT_ECC_ERRCNT
************************************************************/

#define SLUT_ECC_ERRCNT_SLUT_ECC_SECNT_OFFSET                  0
#define SLUT_ECC_ERRCNT_SLUT_ECC_SECNT_MASK                    0xff

#define SLUT_ECC_ERRCNT_SLUT_ECC_DECNT_OFFSET                  8
#define SLUT_ECC_ERRCNT_SLUT_ECC_DECNT_MASK                    0xff00

#define SLUT_ECC_ERRCNT_SLUT_ECCERR_DROP_EN_OFFSET             16
#define SLUT_ECC_ERRCNT_SLUT_ECCERR_DROP_EN_MASK               0x10000

#define SLUT_ECC_ERRCNT_NTB_SLUT_ECCSEI_EN_OFFSET              17
#define SLUT_ECC_ERRCNT_NTB_SLUT_ECCSEI_EN_MASK                0x20000

#define SLUT_ECC_ERRCNT_NTB_SLUT_ECCDEI_EN_OFFSET              18
#define SLUT_ECC_ERRCNT_NTB_SLUT_ECCDEI_EN_MASK                0x40000

#define SLUT_ECC_ERRCNT_NTB_SLUT_ECC_ERREVT_EN_OFFSET          19
#define SLUT_ECC_ERRCNT_NTB_SLUT_ECC_ERREVT_EN_MASK            0x80000

#define SLUT_ECC_ERRCNT_NTB_SLUT_ECCDEI_WAY_OFFSET             20
#define SLUT_ECC_ERRCNT_NTB_SLUT_ECCDEI_WAY_MASK               0x100000

#define SLUT_ECC_ERRCNT_Reserved_31_21_OFFSET                  21
#define SLUT_ECC_ERRCNT_Reserved_31_21_MASK                    0xffe00000

typedef union {
  struct {
    UINT32                                      SLUT_ECC_SECNT:8;
    UINT32                                      SLUT_ECC_DECNT:8;
    UINT32                                 SLUT_ECCERR_DROP_EN:1;
    UINT32                                  NTB_SLUT_ECCSEI_EN:1;
    UINT32                                  NTB_SLUT_ECCDEI_EN:1;
    UINT32                              NTB_SLUT_ECC_ERREVT_EN:1;
    UINT32                                 NTB_SLUT_ECCDEI_WAY:1;
    UINT32                                      Reserved_31_21:11;
  } Field;
  UINT32 Value;
} SLUT_ECC_ERRCNT_STRUCT;

#define SMN_SLUT_ECC_ERRCNT_ADDRESS                                   0x40008fcUL
#define SMN_NTB0NBIO0_SLUT_ECC_ERRCNT_ADDRESS                         0x40008fcUL
#define SMN_NTB0NBIO1_SLUT_ECC_ERRCNT_ADDRESS                         0x42008fcUL
#define SMN_NTB1NBIO0_SLUT_ECC_ERRCNT_ADDRESS                         0x41008fcUL
#define SMN_NTB1NBIO1_SLUT_ECC_ERRCNT_ADDRESS                         0x43008fcUL


/***********************************************************
* Register Name : SLUT_STRIDE
************************************************************/

#define SLUT_STRIDE_SLUT_STRIDE_OFFSET                         0
#define SLUT_STRIDE_SLUT_STRIDE_MASK                           0xff

#define SLUT_STRIDE_Reserved_31_8_OFFSET                       8
#define SLUT_STRIDE_Reserved_31_8_MASK                         0xffffff00

typedef union {
  struct {
    UINT32                                         SLUT_STRIDE:8;
    UINT32                                       Reserved_31_8:24;
  } Field;
  UINT32 Value;
} SLUT_STRIDE_STRUCT;

#define SMN_SLUT_STRIDE_ADDRESS                                       0x40008f8UL
#define SMN_NTB0NBIO0_SLUT_STRIDE_ADDRESS                             0x40008f8UL
#define SMN_NTB0NBIO1_SLUT_STRIDE_ADDRESS                             0x42008f8UL
#define SMN_NTB1NBIO0_SLUT_STRIDE_ADDRESS                             0x41008f8UL
#define SMN_NTB1NBIO1_SLUT_STRIDE_ADDRESS                             0x43008f8UL


/***********************************************************
* Register Name : SLUT_TOTAL_ENTRY
************************************************************/

#define SLUT_TOTAL_ENTRY_SLUT_TOTAL_ENTRY_OFFSET               0
#define SLUT_TOTAL_ENTRY_SLUT_TOTAL_ENTRY_MASK                 0xf

#define SLUT_TOTAL_ENTRY_Reserved_31_4_OFFSET                  4
#define SLUT_TOTAL_ENTRY_Reserved_31_4_MASK                    0xfffffff0

typedef union {
  struct {
    UINT32                                    SLUT_TOTAL_ENTRY:4;
    UINT32                                       Reserved_31_4:28;
  } Field;
  UINT32 Value;
} SLUT_TOTAL_ENTRY_STRUCT;

#define SMN_SLUT_TOTAL_ENTRY_ADDRESS                                  0x40008f4UL
#define SMN_NTB0NBIO0_SLUT_TOTAL_ENTRY_ADDRESS                        0x40008f4UL
#define SMN_NTB0NBIO1_SLUT_TOTAL_ENTRY_ADDRESS                        0x42008f4UL
#define SMN_NTB1NBIO0_SLUT_TOTAL_ENTRY_ADDRESS                        0x41008f4UL
#define SMN_NTB1NBIO1_SLUT_TOTAL_ENTRY_ADDRESS                        0x43008f4UL


/***********************************************************
* Register Name : SMIRR_PDBSTA
************************************************************/

#define SMIRR_PDBSTA_SMIRR_PDBSTA_OFFSET                       0
#define SMIRR_PDBSTA_SMIRR_PDBSTA_MASK                         0xffff

#define SMIRR_PDBSTA_Reserved_31_16_OFFSET                     16
#define SMIRR_PDBSTA_Reserved_31_16_MASK                       0xffff0000

typedef union {
  struct {
    UINT32                                        SMIRR_PDBSTA:16;
    UINT32                                      Reserved_31_16:16;
  } Field;
  UINT32 Value;
} SMIRR_PDBSTA_STRUCT;

#define SMN_SMIRR_PDBSTA_ADDRESS                                      0x4000858UL
#define SMN_NTB0NBIO0_SMIRR_PDBSTA_ADDRESS                            0x4000858UL
#define SMN_NTB0NBIO1_SMIRR_PDBSTA_ADDRESS                            0x4200858UL
#define SMN_NTB1NBIO0_SMIRR_PDBSTA_ADDRESS                            0x4100858UL
#define SMN_NTB1NBIO1_SMIRR_PDBSTA_ADDRESS                            0x4300858UL


/***********************************************************
* Register Name : SMSG_TRIG
************************************************************/

#define SMSG_TRIG_SPME_TRIG_OFFSET                             0
#define SMSG_TRIG_SPME_TRIG_MASK                               0x1

#define SMSG_TRIG_Reserved_31_1_OFFSET                         1
#define SMSG_TRIG_Reserved_31_1_MASK                           0xfffffffe

typedef union {
  struct {
    UINT32                                           SPME_TRIG:1;
    UINT32                                       Reserved_31_1:31;
  } Field;
  UINT32 Value;
} SMSG_TRIG_STRUCT;

#define SMN_SMSG_TRIG_ADDRESS                                         0x4000890UL
#define SMN_NTB0NBIO0_SMSG_TRIG_ADDRESS                               0x4000890UL
#define SMN_NTB0NBIO1_SMSG_TRIG_ADDRESS                               0x4200890UL
#define SMN_NTB1NBIO0_SMSG_TRIG_ADDRESS                               0x4100890UL
#define SMN_NTB1NBIO1_SMSG_TRIG_ADDRESS                               0x4300890UL


/***********************************************************
* Register Name : SPADMUTEX
************************************************************/

#define SPADMUTEX_SPADMUTEXID_OFFSET                           0
#define SPADMUTEX_SPADMUTEXID_MASK                             0x7fffffff

#define SPADMUTEX_SPADMUTEXCLEAR_OFFSET                        31
#define SPADMUTEX_SPADMUTEXCLEAR_MASK                          0x80000000

typedef union {
  struct {
    UINT32                                         SPADMUTEXID:31;
    UINT32                                      SPADMUTEXCLEAR:1;
  } Field;
  UINT32 Value;
} SPADMUTEX_STRUCT;

#define SMN_SPADMUTEX_ADDRESS                                         0x400020cUL
#define SMN_NTB0NBIO0_SPADMUTEX_ADDRESS                               0x400020cUL
#define SMN_NTB0NBIO1_SPADMUTEX_ADDRESS                               0x420020cUL
#define SMN_NTB1NBIO0_SPADMUTEX_ADDRESS                               0x410020cUL
#define SMN_NTB1NBIO1_SPADMUTEX_ADDRESS                               0x430020cUL


/***********************************************************
* Register Name : SPADREG
************************************************************/

#define SPADREG_SPADREG_OFFSET                                 0
#define SPADREG_SPADREG_MASK                                   0xffffffff

typedef union {
  struct {
    UINT32                                             SPADREG:32;
  } Field;
  UINT32 Value;
} SPADREG_STRUCT;

#define SMN_SPADREG_ADDRESS                                           0x4000210UL
#define SMN_NTB0_N0NBIO0_SPADREG_ADDRESS                              0x4000210UL
#define SMN_NTB0_N0NBIO1_SPADREG_ADDRESS                              0x4200210UL
#define SMN_NTB0_N10NBIO0_SPADREG_ADDRESS                             0x4000238UL
#define SMN_NTB0_N10NBIO1_SPADREG_ADDRESS                             0x4200238UL
#define SMN_NTB0_N11NBIO0_SPADREG_ADDRESS                             0x400023cUL
#define SMN_NTB0_N11NBIO1_SPADREG_ADDRESS                             0x420023cUL
#define SMN_NTB0_N12NBIO0_SPADREG_ADDRESS                             0x4000240UL
#define SMN_NTB0_N12NBIO1_SPADREG_ADDRESS                             0x4200240UL
#define SMN_NTB0_N13NBIO0_SPADREG_ADDRESS                             0x4000244UL
#define SMN_NTB0_N13NBIO1_SPADREG_ADDRESS                             0x4200244UL
#define SMN_NTB0_N14NBIO0_SPADREG_ADDRESS                             0x4000248UL
#define SMN_NTB0_N14NBIO1_SPADREG_ADDRESS                             0x4200248UL
#define SMN_NTB0_N15NBIO0_SPADREG_ADDRESS                             0x400024cUL
#define SMN_NTB0_N15NBIO1_SPADREG_ADDRESS                             0x420024cUL
#define SMN_NTB0_N1NBIO0_SPADREG_ADDRESS                              0x4000214UL
#define SMN_NTB0_N1NBIO1_SPADREG_ADDRESS                              0x4200214UL
#define SMN_NTB0_N2NBIO0_SPADREG_ADDRESS                              0x4000218UL
#define SMN_NTB0_N2NBIO1_SPADREG_ADDRESS                              0x4200218UL
#define SMN_NTB0_N3NBIO0_SPADREG_ADDRESS                              0x400021cUL
#define SMN_NTB0_N3NBIO1_SPADREG_ADDRESS                              0x420021cUL
#define SMN_NTB0_N4NBIO0_SPADREG_ADDRESS                              0x4000220UL
#define SMN_NTB0_N4NBIO1_SPADREG_ADDRESS                              0x4200220UL
#define SMN_NTB0_N5NBIO0_SPADREG_ADDRESS                              0x4000224UL
#define SMN_NTB0_N5NBIO1_SPADREG_ADDRESS                              0x4200224UL
#define SMN_NTB0_N6NBIO0_SPADREG_ADDRESS                              0x4000228UL
#define SMN_NTB0_N6NBIO1_SPADREG_ADDRESS                              0x4200228UL
#define SMN_NTB0_N7NBIO0_SPADREG_ADDRESS                              0x400022cUL
#define SMN_NTB0_N7NBIO1_SPADREG_ADDRESS                              0x420022cUL
#define SMN_NTB0_N8NBIO0_SPADREG_ADDRESS                              0x4000230UL
#define SMN_NTB0_N8NBIO1_SPADREG_ADDRESS                              0x4200230UL
#define SMN_NTB0_N9NBIO0_SPADREG_ADDRESS                              0x4000234UL
#define SMN_NTB0_N9NBIO1_SPADREG_ADDRESS                              0x4200234UL
#define SMN_NTB1_N0NBIO0_SPADREG_ADDRESS                              0x4100210UL
#define SMN_NTB1_N0NBIO1_SPADREG_ADDRESS                              0x4300210UL
#define SMN_NTB1_N10NBIO0_SPADREG_ADDRESS                             0x4100238UL
#define SMN_NTB1_N10NBIO1_SPADREG_ADDRESS                             0x4300238UL
#define SMN_NTB1_N11NBIO0_SPADREG_ADDRESS                             0x410023cUL
#define SMN_NTB1_N11NBIO1_SPADREG_ADDRESS                             0x430023cUL
#define SMN_NTB1_N12NBIO0_SPADREG_ADDRESS                             0x4100240UL
#define SMN_NTB1_N12NBIO1_SPADREG_ADDRESS                             0x4300240UL
#define SMN_NTB1_N13NBIO0_SPADREG_ADDRESS                             0x4100244UL
#define SMN_NTB1_N13NBIO1_SPADREG_ADDRESS                             0x4300244UL
#define SMN_NTB1_N14NBIO0_SPADREG_ADDRESS                             0x4100248UL
#define SMN_NTB1_N14NBIO1_SPADREG_ADDRESS                             0x4300248UL
#define SMN_NTB1_N15NBIO0_SPADREG_ADDRESS                             0x410024cUL
#define SMN_NTB1_N15NBIO1_SPADREG_ADDRESS                             0x430024cUL
#define SMN_NTB1_N1NBIO0_SPADREG_ADDRESS                              0x4100214UL
#define SMN_NTB1_N1NBIO1_SPADREG_ADDRESS                              0x4300214UL
#define SMN_NTB1_N2NBIO0_SPADREG_ADDRESS                              0x4100218UL
#define SMN_NTB1_N2NBIO1_SPADREG_ADDRESS                              0x4300218UL
#define SMN_NTB1_N3NBIO0_SPADREG_ADDRESS                              0x410021cUL
#define SMN_NTB1_N3NBIO1_SPADREG_ADDRESS                              0x430021cUL
#define SMN_NTB1_N4NBIO0_SPADREG_ADDRESS                              0x4100220UL
#define SMN_NTB1_N4NBIO1_SPADREG_ADDRESS                              0x4300220UL
#define SMN_NTB1_N5NBIO0_SPADREG_ADDRESS                              0x4100224UL
#define SMN_NTB1_N5NBIO1_SPADREG_ADDRESS                              0x4300224UL
#define SMN_NTB1_N6NBIO0_SPADREG_ADDRESS                              0x4100228UL
#define SMN_NTB1_N6NBIO1_SPADREG_ADDRESS                              0x4300228UL
#define SMN_NTB1_N7NBIO0_SPADREG_ADDRESS                              0x410022cUL
#define SMN_NTB1_N7NBIO1_SPADREG_ADDRESS                              0x430022cUL
#define SMN_NTB1_N8NBIO0_SPADREG_ADDRESS                              0x4100230UL
#define SMN_NTB1_N8NBIO1_SPADREG_ADDRESS                              0x4300230UL
#define SMN_NTB1_N9NBIO0_SPADREG_ADDRESS                              0x4100234UL
#define SMN_NTB1_N9NBIO1_SPADREG_ADDRESS                              0x4300234UL


/***********************************************************
* Register Name : SPME_STA
************************************************************/

#define SPME_STA_SPME_STA_OFFSET                               0
#define SPME_STA_SPME_STA_MASK                                 0x1

#define SPME_STA_Reserved_31_1_OFFSET                          1
#define SPME_STA_Reserved_31_1_MASK                            0xfffffffe

typedef union {
  struct {
    UINT32                                            SPME_STA:1;
    UINT32                                       Reserved_31_1:31;
  } Field;
  UINT32 Value;
} SPME_STA_STRUCT;

#define SMN_SPME_STA_ADDRESS                                          0x4000880UL
#define SMN_NTB0NBIO0_SPME_STA_ADDRESS                                0x4000880UL
#define SMN_NTB0NBIO1_SPME_STA_ADDRESS                                0x4200880UL
#define SMN_NTB1NBIO0_SPME_STA_ADDRESS                                0x4100880UL
#define SMN_NTB1NBIO1_SPME_STA_ADDRESS                                0x4300880UL


/***********************************************************
* Register Name : SRDECERR_CTRL
************************************************************/

#define SRDECERR_CTRL_NTB_SLUT_WdataPchk_ERREVT_EN_OFFSET      0
#define SRDECERR_CTRL_NTB_SLUT_WdataPchk_ERREVT_EN_MASK        0x1

#define SRDECERR_CTRL_Reserved_31_1_OFFSET                     1
#define SRDECERR_CTRL_Reserved_31_1_MASK                       0xfffffffe

typedef union {
  struct {
    UINT32                        NTB_SLUT_WdataPchk_ERREVT_EN:1;
    UINT32                                       Reserved_31_1:31;
  } Field;
  UINT32 Value;
} SRDECERR_CTRL_STRUCT;

#define SMN_SRDECERR_CTRL_ADDRESS                                     0x4000908UL
#define SMN_NTB0NBIO0_SRDECERR_CTRL_ADDRESS                           0x4000908UL
#define SMN_NTB0NBIO1_SRDECERR_CTRL_ADDRESS                           0x4200908UL
#define SMN_NTB1NBIO0_SRDECERR_CTRL_ADDRESS                           0x4100908UL
#define SMN_NTB1NBIO1_SRDECERR_CTRL_ADDRESS                           0x4300908UL


/***********************************************************
* Register Name : SROMBARLMT
************************************************************/

#define SROMBARLMT_SROMBARLMT_OFFSET                           0
#define SROMBARLMT_SROMBARLMT_MASK                             0xffffffff

typedef union {
  struct {
    UINT32                                          SROMBARLMT:32;
  } Field;
  UINT32 Value;
} SROMBARLMT_STRUCT;

#define SMN_SROMBARLMT_ADDRESS                                        0x4000810UL
#define SMN_NTB0NBIO0_SROMBARLMT_ADDRESS                              0x4000810UL
#define SMN_NTB0NBIO1_SROMBARLMT_ADDRESS                              0x4200810UL
#define SMN_NTB1NBIO0_SROMBARLMT_ADDRESS                              0x4100810UL
#define SMN_NTB1NBIO1_SROMBARLMT_ADDRESS                              0x4300810UL


/***********************************************************
* Register Name : SROMBARXLAT_HI
************************************************************/

#define SROMBARXLAT_HI_SROMBARXLAT_HI_OFFSET                   0
#define SROMBARXLAT_HI_SROMBARXLAT_HI_MASK                     0xffffffff

typedef union {
  struct {
    UINT32                                      SROMBARXLAT_HI:32;
  } Field;
  UINT32 Value;
} SROMBARXLAT_HI_STRUCT;

#define SMN_SROMBARXLAT_HI_ADDRESS                                    0x400082cUL
#define SMN_NTB0NBIO0_SROMBARXLAT_HI_ADDRESS                          0x400082cUL
#define SMN_NTB0NBIO1_SROMBARXLAT_HI_ADDRESS                          0x420082cUL
#define SMN_NTB1NBIO0_SROMBARXLAT_HI_ADDRESS                          0x410082cUL
#define SMN_NTB1NBIO1_SROMBARXLAT_HI_ADDRESS                          0x430082cUL


/***********************************************************
* Register Name : SROMBARXLAT_LO
************************************************************/

#define SROMBARXLAT_LO_Reserved_4_0_OFFSET                     0
#define SROMBARXLAT_LO_Reserved_4_0_MASK                       0x1f

#define SROMBARXLAT_LO_SROMBARXLAT_LO_OFFSET                   5
#define SROMBARXLAT_LO_SROMBARXLAT_LO_MASK                     0xffffffe0

typedef union {
  struct {
    UINT32                                        Reserved_4_0:5;
    UINT32                                      SROMBARXLAT_LO:27;
  } Field;
  UINT32 Value;
} SROMBARXLAT_LO_STRUCT;

#define SMN_SROMBARXLAT_LO_ADDRESS                                    0x4000828UL
#define SMN_NTB0NBIO0_SROMBARXLAT_LO_ADDRESS                          0x4000828UL
#define SMN_NTB0NBIO1_SROMBARXLAT_LO_ADDRESS                          0x4200828UL
#define SMN_NTB1NBIO0_SROMBARXLAT_LO_ADDRESS                          0x4100828UL
#define SMN_NTB1NBIO1_SROMBARXLAT_LO_ADDRESS                          0x4300828UL


/***********************************************************
* Register Name : SSIDE_INFO
************************************************************/

#define SSIDE_INFO_SSIDE_INFO_OFFSET                           0
#define SSIDE_INFO_SSIDE_INFO_MASK                             0x1

#define SSIDE_INFO_SSIDE_EN_OFFSET                             1
#define SSIDE_INFO_SSIDE_EN_MASK                               0x2

#define SSIDE_INFO_Reserved_31_2_OFFSET                        2
#define SSIDE_INFO_Reserved_31_2_MASK                          0xfffffffc

typedef union {
  struct {
    UINT32                                          SSIDE_INFO:1;
    UINT32                                            SSIDE_EN:1;
    UINT32                                       Reserved_31_2:30;
  } Field;
  UINT32 Value;
} SSIDE_INFO_STRUCT;

#define SMN_SSIDE_INFO_ADDRESS                                        0x4000808UL
#define SMN_NTB0NBIO0_SSIDE_INFO_ADDRESS                              0x4000808UL
#define SMN_NTB0NBIO1_SSIDE_INFO_ADDRESS                              0x4200808UL
#define SMN_NTB1NBIO0_SSIDE_INFO_ADDRESS                              0x4100808UL
#define SMN_NTB1NBIO1_SSIDE_INFO_ADDRESS                              0x4300808UL


/***********************************************************
* Register Name : SSMU_SPADMUTEX
************************************************************/

#define SSMU_SPADMUTEX_SSMU_SPADMUTEXID_OFFSET                 0
#define SSMU_SPADMUTEX_SSMU_SPADMUTEXID_MASK                   0x7fffffff

#define SSMU_SPADMUTEX_SSMU_SPADMUTEXCLEAR_OFFSET              31
#define SSMU_SPADMUTEX_SSMU_SPADMUTEXCLEAR_MASK                0x80000000

typedef union {
  struct {
    UINT32                                    SSMU_SPADMUTEXID:31;
    UINT32                                 SSMU_SPADMUTEXCLEAR:1;
  } Field;
  UINT32 Value;
} SSMU_SPADMUTEX_STRUCT;

#define SMN_SSMU_SPADMUTEX_ADDRESS                                    0x40008b0UL
#define SMN_NTB0NBIO0_SSMU_SPADMUTEX_ADDRESS                          0x40008b0UL
#define SMN_NTB0NBIO1_SSMU_SPADMUTEX_ADDRESS                          0x42008b0UL
#define SMN_NTB1NBIO0_SSMU_SPADMUTEX_ADDRESS                          0x41008b0UL
#define SMN_NTB1NBIO1_SSMU_SPADMUTEX_ADDRESS                          0x43008b0UL


/***********************************************************
* Register Name : SSMU_SPADREG
************************************************************/

#define SSMU_SPADREG_SSMU_SPADREG_OFFSET                       0
#define SSMU_SPADREG_SSMU_SPADREG_MASK                         0xffffffff

typedef union {
  struct {
    UINT32                                        SSMU_SPADREG:32;
  } Field;
  UINT32 Value;
} SSMU_SPADREG_STRUCT;

#define SMN_SSMU_SPADREG_ADDRESS                                      0x40008b4UL
#define SMN_NTB0_N0NBIO0_SSMU_SPADREG_ADDRESS                         0x40008b4UL
#define SMN_NTB0_N0NBIO1_SSMU_SPADREG_ADDRESS                         0x42008b4UL
#define SMN_NTB0_N1NBIO0_SSMU_SPADREG_ADDRESS                         0x40008b8UL
#define SMN_NTB0_N1NBIO1_SSMU_SPADREG_ADDRESS                         0x42008b8UL
#define SMN_NTB0_N2NBIO0_SSMU_SPADREG_ADDRESS                         0x40008bcUL
#define SMN_NTB0_N2NBIO1_SSMU_SPADREG_ADDRESS                         0x42008bcUL
#define SMN_NTB0_N3NBIO0_SSMU_SPADREG_ADDRESS                         0x40008c0UL
#define SMN_NTB0_N3NBIO1_SSMU_SPADREG_ADDRESS                         0x42008c0UL
#define SMN_NTB1_N0NBIO0_SSMU_SPADREG_ADDRESS                         0x41008b4UL
#define SMN_NTB1_N0NBIO1_SSMU_SPADREG_ADDRESS                         0x43008b4UL
#define SMN_NTB1_N1NBIO0_SSMU_SPADREG_ADDRESS                         0x41008b8UL
#define SMN_NTB1_N1NBIO1_SSMU_SPADREG_ADDRESS                         0x43008b8UL
#define SMN_NTB1_N2NBIO0_SSMU_SPADREG_ADDRESS                         0x41008bcUL
#define SMN_NTB1_N2NBIO1_SSMU_SPADREG_ADDRESS                         0x43008bcUL
#define SMN_NTB1_N3NBIO0_SSMU_SPADREG_ADDRESS                         0x41008c0UL
#define SMN_NTB1_N3NBIO1_SSMU_SPADREG_ADDRESS                         0x43008c0UL


/***********************************************************
* Register Name : S_PINRST
************************************************************/

#define S_PINRST_S_PINRST_OFFSET                               0
#define S_PINRST_S_PINRST_MASK                                 0x1

#define S_PINRST_Reserved_31_1_OFFSET                          1
#define S_PINRST_Reserved_31_1_MASK                            0xfffffffe

typedef union {
  struct {
    UINT32                                            S_PINRST:1;
    UINT32                                       Reserved_31_1:31;
  } Field;
  UINT32 Value;
} S_PINRST_STRUCT;

#define SMN_S_PINRST_ADDRESS                                          0x40008a4UL
#define SMN_NTB0NBIO0_S_PINRST_ADDRESS                                0x40008a4UL
#define SMN_NTB0NBIO1_S_PINRST_ADDRESS                                0x42008a4UL
#define SMN_NTB1NBIO0_S_PINRST_ADDRESS                                0x41008a4UL
#define SMN_NTB1NBIO1_S_PINRST_ADDRESS                                0x43008a4UL


/***********************************************************
* Register Name : S_RSPNUM
************************************************************/

#define S_RSPNUM_SWRRSP_NUM_OFFSET                             0
#define S_RSPNUM_SWRRSP_NUM_MASK                               0xffff

#define S_RSPNUM_SRDRSP_NUM_OFFSET                             16
#define S_RSPNUM_SRDRSP_NUM_MASK                               0xffff0000

typedef union {
  struct {
    UINT32                                          SWRRSP_NUM:16;
    UINT32                                          SRDRSP_NUM:16;
  } Field;
  UINT32 Value;
} S_RSPNUM_STRUCT;

#define SMN_S_RSPNUM_ADDRESS                                          0x40008a8UL
#define SMN_NTB0NBIO0_S_RSPNUM_ADDRESS                                0x40008a8UL
#define SMN_NTB0NBIO1_S_RSPNUM_ADDRESS                                0x42008a8UL
#define SMN_NTB1NBIO0_S_RSPNUM_ADDRESS                                0x41008a8UL
#define SMN_NTB1NBIO1_S_RSPNUM_ADDRESS                                0x43008a8UL


/***********************************************************
* Register Name : S_SMU_ACK
************************************************************/

#define S_SMU_ACK_Reserved_0_0_OFFSET                          0
#define S_SMU_ACK_Reserved_0_0_MASK                            0x1

#define S_SMU_ACK_SRST_ACK_OFFSET                              1
#define S_SMU_ACK_SRST_ACK_MASK                                0x2

#define S_SMU_ACK_SD3_ACK_OFFSET                               2
#define S_SMU_ACK_SD3_ACK_MASK                                 0x4

#define S_SMU_ACK_SPMETO_ACK_OFFSET                            3
#define S_SMU_ACK_SPMETO_ACK_MASK                              0x8

#define S_SMU_ACK_SD0_ACK_OFFSET                               4
#define S_SMU_ACK_SD0_ACK_MASK                                 0x10

#define S_SMU_ACK_SMISC0_ACK_OFFSET                            5
#define S_SMU_ACK_SMISC0_ACK_MASK                              0xffe0

#define S_SMU_ACK_SMISC1_ACK_OFFSET                            16
#define S_SMU_ACK_SMISC1_ACK_MASK                              0xff0000

#define S_SMU_ACK_Reserved_31_24_OFFSET                        24
#define S_SMU_ACK_Reserved_31_24_MASK                          0xff000000

typedef union {
  struct {
    UINT32                                        Reserved_0_0:1;
    UINT32                                            SRST_ACK:1;
    UINT32                                             SD3_ACK:1;
    UINT32                                          SPMETO_ACK:1;
    UINT32                                             SD0_ACK:1;
    UINT32                                          SMISC0_ACK:11;
    UINT32                                          SMISC1_ACK:8;
    UINT32                                      Reserved_31_24:8;
  } Field;
  UINT32 Value;
} S_SMU_ACK_STRUCT;

#define SMN_S_SMU_ACK_ADDRESS                                         0x40008a0UL
#define SMN_NTB0NBIO0_S_SMU_ACK_ADDRESS                               0x40008a0UL
#define SMN_NTB0NBIO1_S_SMU_ACK_ADDRESS                               0x42008a0UL
#define SMN_NTB1NBIO0_S_SMU_ACK_ADDRESS                               0x41008a0UL
#define SMN_NTB1NBIO1_S_SMU_ACK_ADDRESS                               0x43008a0UL

#endif /* _NTB_H_ */

