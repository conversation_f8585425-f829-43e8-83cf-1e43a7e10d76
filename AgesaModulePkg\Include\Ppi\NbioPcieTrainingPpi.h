/*****************************************************************************
 *
 * Copyright (C) 2016-2024 Advanced Micro Devices, Inc. All rights reserved.
 *
 *******************************************************************************
 */
#ifndef _AMD_NBIO_PCIE_TRAINING_PPI_H_
#define _AMD_NBIO_PCIE_TRAINING_PPI_H_

/**
 * @file NbioPcieTrainingPpi.h
 *
 * @brief   PPI for PCIe TRAINING NOTIFICATION
 *
 * @details PCIe Training PPI serve as notification points at the onset of particular phases during the link training process
 *
 */
 
/// Global ID for the AMD_NBIO_PCIE_SERVICES_PPI
///

#define PEI_AMD_NBIO_PCIE_TRAINING_PPI_GUID \
{ \
  0x72166411, 0x442c, 0x4aab, { 0xa2, 0x60, 0x57, 0xd5, 0x84, 0xd3, 0x21, 0x39} \
}

// Current PPI revision
/**
 *  @brief Boolean macro AMD_TRAINING_START_STATUS
 *  @details Defined as true, and is to be used in PEI_AMD_NBIO_PCIE_TRAINING_START_PPI
 */
#define AMD_TRAINING_START_STATUS  TRUE
/**
 *  @brief Boolean macro AMD_DEASSERT_RESET_STATUS
 *  @details Defined as true, and is to be used in PEI_AMD_NBIO_DEASSERT_RESET_PPI
 */
#define AMD_DEASSERT_RESET_STATUS  TRUE
/**
 *  @brief Boolean macro AMD_CXL_INIT_START_STATUS
 *  @details Defined as true, and is to be used in PEI_AMD_NBIO_CXL_INIT_START_PPI
 */
#define AMD_CXL_INIT_START_STATUS  TRUE
/**
 *  @brief Boolean macro AMD_TRAINING_DONE_STATUS
 *  @details Defined as true, and is to be used in PEI_AMD_NBIO_PCIE_TRAINING_DONE_PPI
 */
#define AMD_TRAINING_DONE_STATUS   TRUE

/**
 * @brief  PEI_AMD_NBIO_PCIE_TRAINING_START_PPI prototype
 *
 * @details Defines PEI_AMD_NBIO_PCIE_TRAINING_START_PPI, which notify that PCIe link training has started
 */
typedef struct _PEI_AMD_NBIO_PCIE_TRAINING_START_PPI {
  BOOLEAN   TrainingStart;            ///< Training Start
} PEI_AMD_NBIO_PCIE_TRAINING_START_PPI;

/**
 * @brief  PEI_AMD_NBIO_DEASSERT_RESET_PPI prototype
 *
 * @details Defines PEI_AMD_NBIO_DEASSERT_RESET_PPI, which notify that PCIe reset has been deasserted
 */
typedef struct _PEI_AMD_NBIO_DEASSERT_RESET_PPI {
  BOOLEAN   DeassertResetStatus;      ///< Deassert Reset Status
} PEI_AMD_NBIO_DEASSERT_RESET_PPI;

/**
 * @brief  PEI_AMD_NBIO_CXL_INIT_START_PPI prototype
 *
 * @details Defines PEI_AMD_NBIO_CXL_INIT_START_PPI, which notify that CXL configuration has started
 */
typedef struct _PEI_AMD_NBIO_CXL_INIT_START_PPI {
  BOOLEAN   CxlInitStart;             ///< Cxl Init Start
} PEI_AMD_NBIO_CXL_INIT_START_PPI;

/**
 * @brief  PEI_AMD_NBIO_PCIE_TRAINING_DONE_PPI prototype
 *
 * @details Defines PEI_AMD_NBIO_PCIE_TRAINING_DONE_PPI, which notify that PCIe link training has finished
 */
typedef struct _PEI_AMD_NBIO_PCIE_TRAINING_DONE_PPI {
  BOOLEAN   TrainingComplete;         ///< Training Complete
} PEI_AMD_NBIO_PCIE_TRAINING_DONE_PPI;

extern EFI_GUID gAmdNbioPcieTrainingStartPpiGuid;       ///< Amd Nbio Pcie Training Start PpiGuid
extern EFI_GUID gAmdNbioPcieTrainingDonePpiGuid;        ///< Amd Nbio Pcie Training Done PpiGuid

#endif //



