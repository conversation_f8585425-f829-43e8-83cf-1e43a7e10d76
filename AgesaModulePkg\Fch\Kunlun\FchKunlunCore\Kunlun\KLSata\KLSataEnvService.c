/*****************************************************************************
 *
 * Copyright (C) 2008-2024 Advanced Micro Devices, Inc. All rights reserved.
 *
 *******************************************************************************
 **/


/* $NoKeywords:$ */
/**
 * @file
 *
 * SATA Controller family specific service procedure
 *
 *
 *
 * @xrefitem bom "File Content Label" "Release Content"
 * @e project:     AGESA
 * @e sub-project: FCH
 * @e \$Revision: 309083 $   @e \$Date: 2014-12-09 09:28:24 -0800 (Tue, 09 Dec 2014) $
 *
 */

/*----------------------------------------------------------------------------------------
 *                             M O D U L E S    U S E D
 *----------------------------------------------------------------------------------------
 */
#include  "FchPlatform.h"
#include  "Filecode.h"
#define FILECODE FCH_KUNLUN_FCHKUNLUNCORE_KUNLUN_KLSATA_KLSATAENVSERVICE_FILECODE

/*----------------------------------------------------------------------------------------
 *                   D E F I N I T I O N S    A N D    M A C R O S
 *----------------------------------------------------------------------------------------
 */
//
// Local Routine
//


/**
 * FchKLInitEnvProgramSata - Sata Init before PCI scan
 *
 *
 * @param[in] DieBusNum - Bus Number of current Die.
 * @param[in] Controller Sata controller number.
 * @param[in] FchDataPtr - Fch configuration structure pointer
 *
 */
VOID
FchKLInitEnvProgramSata (
  IN  UINT32   DieBusNum,
  IN  UINT32   Controller,
  IN  VOID     *FchDataPtr
  )
{
  FCH_DATA_BLOCK         *LocalCfgPtr;
//  AMD_CONFIG_PARAMS      *StdHeader;

  LocalCfgPtr = (FCH_DATA_BLOCK *) FchDataPtr;
//  StdHeader = LocalCfgPtr->StdHeader;

  // Do Sata init
  FchKLSataInitRsmuCtrl (DieBusNum, Controller, FchDataPtr);

  FchKLSataInitCtrlReg (DieBusNum, Controller, FchDataPtr);

  FchKLSataInitEsata (DieBusNum, Controller, FchDataPtr);

  FchKLSataInitEnableErr (DieBusNum, Controller, FchDataPtr);

  FchKLSataSetPortGenMode (DieBusNum, Controller, FchDataPtr);

  FchKLSataSetBISTLComplianceMode (DieBusNum, Controller, FchDataPtr);

  if (LocalCfgPtr->Sata[Controller].SataSgpio0) {
    FchKLSataGpioInitial (DieBusNum, Controller, FchDataPtr);
  } else {
    // If Sgpio is not enable, set to MPIO mode
    FchSmnRW (DieBusNum, FCH_KL_SMN_SATA_CONTROL_BAR5 + Controller * FCH_KL_SMN_SATA_STEP + FCH_SATA_BAR5_REGE0, ~(UINT32) BIT28, BIT28, NULL);

    // If UBM Diagnostic Mode is enabled
    if ( LocalCfgPtr->Sata[Controller].SataUBMDiagMode ) {
      // GHC_CFG_CAP_EMS
      FchSmnRW (DieBusNum, FCH_KL_SMN_SATA_CONTROL_BAR5 + Controller * FCH_KL_SMN_SATA_STEP + FCH_SATA_BAR5_REGFC, ~(UINT32) (BIT27), BIT27, NULL);
      FchSmnRW (DieBusNum, FCH_KL_SMN_SATA_CONTROL_BAR5 + Controller * FCH_KL_SMN_SATA_STEP + FCH_SATA_BAR5_REGE0, ~(UINT32) (BIT27), BIT27, NULL);
    }
  }

  FchKLSataInitDevSlp (DieBusNum, Controller, FchDataPtr);

  if (Controller == 0) {
    FchKLSataInitMpssMap (DieBusNum, FchDataPtr);
  }
}



