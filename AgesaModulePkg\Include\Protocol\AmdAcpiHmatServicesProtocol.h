/*****************************************************************************
 * Copyright (C) 2008-2024 Advanced Micro Devices, Inc. All rights reserved.
 *
*****************************************************************************
*/
/* $NoKeywords:$ */
/**
 * @file
 *
 * AMD HMAT Services Protocol prototype definition
 *
 */
#ifndef _AMD_ACPI_HMAT_SERVICES_PROTOCOL_H_
#define _AMD_ACPI_HMAT_SERVICES_PROTOCOL_H_
#include <Gnb.h>

///
/// Heterogeneous Memory Attribute Table
///
typedef struct {
  ACPI_TABLE_HEADER Common;
  UINT8     Reserved[4];
} HMAT_HEADER;

///
/// HMAT Structure Header
///
typedef struct {
  UINT16    Type;
  UINT8     Reserved[2];
  UINT32    Length;
} HMAT_STRUCTURE_HEADER;

///
/// Flags of Memory Proximity Domain Attributes Structure
///
typedef union {
  UINT16 Value;
  struct {
    UINT16 InitiatorProximityDomainValid :1;
    UINT16 Reserved                      :15;
  } Fields;
} HMAT_MEM_PROXIMITY_DOMAIN_ATTRIBUTES_FLAGS;

///
/// Memory Proximity Domain Attributes Structure
///
typedef struct {
  UINT16                                      Type;
  UINT8                                       Reserved[2];
  UINT32                                      Length;
  HMAT_MEM_PROXIMITY_DOMAIN_ATTRIBUTES_FLAGS  Flags;
  UINT8                                       Reserved1[2];
  UINT32                                      InitiatorProximityDomain;
  UINT32                                      MemoryProximityDomain;
  UINT8                                       Reserved2[20];
} HMAT_MEMORY_PROXIMITY_DOMAIN_ATTRIBUTES_STRUCTURE;

///
/// Flags of System Locality Latency and Bandwidth Information Structure
///
typedef union {
  UINT8 Value;
  struct {
    UINT8    MemoryHierarchy  :4;
    UINT8    AccessAttributes :2;
    UINT8    Reserved         :2;
  } Fields;
} HMAT_SYS_LOCALITY_LATENCY_BANDWIDTH_INFO_FLAGS;

///
/// System Locality Latency and Bandwidth Information Structure
///
typedef struct {
  UINT16                                          Type;
  UINT8                                           Reserved[2];
  UINT32                                          Length;
  HMAT_SYS_LOCALITY_LATENCY_BANDWIDTH_INFO_FLAGS  Flags;
  UINT8                                           DataType;
  UINT8                                           MinTransferSize;
  UINT8                                           Reserved1;
  UINT32                                          NumberOfInitiatorProximityDomains;
  UINT32                                          NumberOfTargetProximityDomains;
  UINT8                                           Reserved2[4];
  UINT64                                          EntryBaseUnit;
} HMAT_SYSTEM_LOCALITY_LATENCY_AND_BANDWIDTH_INFO_STRUCTURE;

///
/// Cache Attributes of Memory Side Cache Information Structure
///
typedef union {
  UINT32 Value;
  struct {
    UINT32   TotalCacheLevels    :4;
    UINT32   CacheLevel          :4;
    UINT32   CacheAssociativity  :4;
    UINT32   WritePolicy         :4;
    UINT32   CacheLineSize       :16;
  } Fields;
} HMAT_MEM_SIDE_CACHE_ATTRIBUTES;

///
/// Memory Side Cache Information Structure
///
typedef struct {
  UINT16                          Type;
  UINT8                           Reserved[2];
  UINT32                          Length;
  UINT32                          MemoryProximityDomain;
  UINT8                           Reserved1[4];
  UINT64                          MemorySideCacheSize;
  HMAT_MEM_SIDE_CACHE_ATTRIBUTES  CacheAttributes;
  UINT8                           Reserved2[2];
  UINT16                          NumberOfSmbiosHandles;
} HMAT_MEMORY_SIDE_CACHE_INFO_STRUCTURE;

/*----------------------------------------------------------------------------------------
 *                    T Y P E D E F S     A N D     S T R U C T U R E S
 *----------------------------------------------------------------------------------------
 */

/*----------------------------------------------------------------------------------------
 *                 D E F I N I T I O N S     A N D     M A C R O S
 *----------------------------------------------------------------------------------------
 */

#define HMAT_REVISION                                                                 0x02

#define HMAT_MEMORY_PROXIMITY_DOMAIN_ATTRIBUTES_TYPE                                  0x00
#define HMAT_SYSTEM_LOCALITY_LATENCY_AND_BANDWIDTH_INFO_TYPE                          0x01
#define HMAT_MEMORY_SIDE_CACHE_INFO_TYPE                                              0x02

/// The revision number of AMD_ACPI_HMAT_SERVICES_PROTOCOL.
#define AMD_ACPI_HMAT_SERVICES_PROTOCOL_REVISION                                         1

#define HMAT_SYSTEM_LOCALITY_LATENCY_AND_BANDWIDTH_INFO_DATA_TYPE_ACCESS_LATENCY         0
#define HMAT_SYSTEM_LOCALITY_LATENCY_AND_BANDWIDTH_INFO_DATA_TYPE_READ_LATENCY           1
#define HMAT_SYSTEM_LOCALITY_LATENCY_AND_BANDWIDTH_INFO_DATA_TYPE_WRITE_LATENCY          2
#define HMAT_SYSTEM_LOCALITY_LATENCY_AND_BANDWIDTH_INFO_DATA_TYPE_ACCESS_BANDWIDTH       3
#define HMAT_SYSTEM_LOCALITY_LATENCY_AND_BANDWIDTH_INFO_DATA_TYPE_READ_BANDWIDTH         4
#define HMAT_SYSTEM_LOCALITY_LATENCY_AND_BANDWIDTH_INFO_DATA_TYPE_WRITE_BANDWIDTH        5

/// Forward declaration for the AMD_ACPI_HMAT_SERVICES_PROTOCOL.
typedef struct _AMD_ACPI_HMAT_SERVICES_PROTOCOL AMD_ACPI_HMAT_SERVICES_PROTOCOL;

/**
 * @brief Create the HMAT structure.
 *
 * @param[in]      This                Pointer to the ACPI HMAT services protocol instance. @see AMD_ACPI_HMAT_SERVICES_PROTOCOL
 * @param[in]      TablePtr            Pointer to the header of HMAT ACPI table structure.
 * @param[in, out] TableEnd            Point to the end of this table
 *
 * @return EFI_STATUS - status of the operation.
 */
typedef
EFI_STATUS
(EFIAPI *AMD_HMAT_SERVICES_CREATE_STRUCTURE) (
  IN     AMD_ACPI_HMAT_SERVICES_PROTOCOL        *This,
  IN     VOID                                   *TableHeaderPtr,
  IN OUT UINT8                                 **TableEnd
  );

/**
 * @brief Free the buffer of the added HMAT structures.
 *
 * @param[in]      This                Pointer to the ACPI HMAT services protocol instance. @see AMD_ACPI_HMAT_SERVICES_PROTOCOL
 *
 * @return EFI_STATUS - status of the operation.
 */
typedef
EFI_STATUS
(EFIAPI *AMD_HMAT_SERVICES_FREE_BUFFER) (
  IN     AMD_ACPI_HMAT_SERVICES_PROTOCOL        *This
  );

/**
 * @brief Add the Memory Proximity Domain Attributes Structure
 *
 * @param[in]      This                        Pointer to the ACPI HMAT services protocol instance. @see AMD_ACPI_HMAT_SERVICES_PROTOCOL
 * @param[in]      Flags                       Flags @see HMAT_MEM_PROXIMITY_DOMAIN_ATTRIBUTES_FLAGS
 * @param[in]      InitiatorProximityDomain    Proximity Domain for the Attached Initiator
 * @param[in]      MemoryProximityDomain       Proximity Domain for the Memory
 *
 * @return EFI_STATUS - status of the operation.
 */
typedef
EFI_STATUS
(EFIAPI *AMD_HMAT_SERVICES_ADD_MEM_PROXIMITY_DOMAIN_ATTRIBUTES) (
  IN     AMD_ACPI_HMAT_SERVICES_PROTOCOL                *This,
  IN     HMAT_MEM_PROXIMITY_DOMAIN_ATTRIBUTES_FLAGS      Flags,
  IN     UINT32                                          InitiatorProximityDomain,
  IN     UINT32                                          MemoryProximityDomain
  );

/**
 * @brief Add the System Locality Latency and Bandwidth Information Structure
 *
 * @param[in]      This                               Pointer to the ACPI HMAT services protocol instance. @see AMD_ACPI_HMAT_SERVICES_PROTOCOL
 * @param[in]      Flags                              Flags @see HMAT_SYS_LOCALITY_LATENCY_BANDWIDTH_INFO_FLAGS
 * @param[in]      DataType                           Data Type
 * @param[in]      MinTransferSize                    MinTransferSize
 * @param[in]      NumberOfInitiatorProximityDomains  Number of Initiator Proximity Domains
 * @param[in]      NumberOfTargetProximityDomains     Number of Target Proximity Domains
 * @param[in]      EntryBaseUnit                      Entry Base Unit
 * @param[in]      InitiatorProximityDomains          Array of Initiator Proximity Domain List
 * @param[in]      TargetProximityDomains             Array of Target Proximity Domain List
 * @param[in]      LatencyBandwidthEntries            Two-dimension array of Latency / bandwidth values
 *
 * @return EFI_STATUS - status of the operation.
 */
typedef
EFI_STATUS
(EFIAPI *AMD_HMAT_SERVICES_ADD_SYS_LOCALITY_LATENCY_BANDWIDTH_INFO) (
  IN     AMD_ACPI_HMAT_SERVICES_PROTOCOL                *This,
  IN     HMAT_SYS_LOCALITY_LATENCY_BANDWIDTH_INFO_FLAGS  Flags,
  IN     UINT8                                           DataType,
  IN     UINT8                                           MinTransferSize,
  IN     UINT32                                          NumberOfInitiatorProximityDomains,
  IN     UINT32                                          NumberOfTargetProximityDomains,
  IN     UINT64                                          EntryBaseUnit,
  IN     UINT32                                         *InitiatorProximityDomains,
  IN     UINT32                                         *TargetProximityDomains,
  IN     UINT16                                         *LatencyBandwidthEntries
  );

/**
 * @brief Add the Memory Side Cache Information Structure
 *
 * @param[in]      This                   Pointer to the ACPI HMAT services protocol instance. @see AMD_ACPI_HMAT_SERVICES_PROTOCOL
 * @param[in]      MemoryProximityDomain  Proximity Domain for the Memory
 * @param[in]      MemorySideCacheSize    Memory Side Cache Size
 * @param[in]      CacheAttributes        Cache Attributes. @see HMAT_MEM_SIDE_CACHE_ATTRITUBES
 * @param[in]      NumberOfSmbiosHandles  Number of SMBIOS handles
 * @param[in]      SmbiosHandles          SMBIOS handles
 *
 * @return EFI_STATUS - status of the operation.
 */
typedef
EFI_STATUS
(EFIAPI *AMD_HMAT_SERVICES_ADD_MEM_SIDE_CACHE_INFO) (
  IN     AMD_ACPI_HMAT_SERVICES_PROTOCOL           *This,
  IN     UINT32                                     MemoryProximityDomain,
  IN     UINT64                                     MemorySideCacheSize,
  IN     HMAT_MEM_SIDE_CACHE_ATTRIBUTES             CacheAttributes,
  IN     UINT16                                     NumberOfSmbiosHandles,
  IN     UINT16                                    *SmbiosHandles
  );

/// When installed, the HMAT Services Protocol produces a collection of services to generate HMAT.
struct _AMD_ACPI_HMAT_SERVICES_PROTOCOL {
  UINTN                                                      Revision;                              ///< Revision Number.
  AMD_HMAT_SERVICES_CREATE_STRUCTURE                         CreateStructure;                       ///< @see AMD_HMAT_SERVICES_CREATE_STRUCTURE
  AMD_HMAT_SERVICES_FREE_BUFFER                              FreeBuffer;                            ///< @see AMD_HMAT_SERVICES_FREE_BUFFER
  AMD_HMAT_SERVICES_ADD_MEM_PROXIMITY_DOMAIN_ATTRIBUTES      AddMemoryProximityDomainAttributes;    ///< @see AMD_HMAT_SERVICES_ADD_MEM_PROXIMITY_DOMAIN_ATTRIBUTES
  AMD_HMAT_SERVICES_ADD_SYS_LOCALITY_LATENCY_BANDWIDTH_INFO  AddSystemLocalityLatencyBandwidthInfo; ///< @see AMD_HMAT_SERVICES_ADD_SYS_LOCALITY_LATENCY_BANDWIDTH_INFO
  AMD_HMAT_SERVICES_ADD_MEM_SIDE_CACHE_INFO                  AddMemorySideCacheInfo;                ///< @see AMD_HMAT_SERVICES_ADD_MEM_SIDE_CACHE_INFO
};

/// GUID for ACPI HMAT services protocol
extern EFI_GUID gAmdAcpiHmatServicesProtocolGuid;

#endif // _AMD_ACPI_HMAT_SERVICES_PROTOCOL_H_

