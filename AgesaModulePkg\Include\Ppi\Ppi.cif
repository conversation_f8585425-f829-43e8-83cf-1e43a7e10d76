<component>
    name = "Ppi"
    category = ModulePart
    LocalRoot = "AgesaModulePkg\Include\Ppi\"
    RefName = "AgesaModulePkg.Include.Ppi"
[files]
"AmdApcbPpi.h"
"AmdCcxPpi.h"
"AmdCoreTopologyServicesPpi.h"
"AmdCoreTopologyServicesV2Ppi.h"
"AmdCoreTopologyServicesV3Ppi.h"
"AmdErrorLogPpi.h"
"AmdFabricSocSpecificServicesPpi.h"
"AmdFchInitPpi.h"
"AmdI3cMasterConsumerSPD5Ppi.h"
"AmdIdsDebugPrintPpi.h"
"AmdMbistPpi.h"
"AmdMemPpi.h"
"AmdMemPpiBrh.h"
"AmdMultiFchInitPpi.h"
"AmdSocPpi.h"
"ApobCommonServicePpi.h"
"FabricTopologyServices2Ppi.h"
"FchEspiCmdPpi.h"
"FchI2cMasterPostPpi.h"
"FchI3cMaster.h"
"FchPiI3c.h"
"NbioBaseServicesZPPpi.h"
"NbioIommuFeaturePpi.h"
"NbioPcieDpcStatusPpi.h"
"NbioPcieServicesPpi.h"
"NbioPcieTrainingPpi.h"
"NbioSmuServicesPpi.h"
"SocLogicalIdPpi.h"
"SocZen5ServicesPpi.h"
<endComponent>
