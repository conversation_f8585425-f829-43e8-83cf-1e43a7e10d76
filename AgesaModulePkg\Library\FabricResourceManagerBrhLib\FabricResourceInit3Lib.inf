#;*****************************************************************************
#;
#; Copyright (C) 2008-2024 Advanced Micro Devices, Inc. All rights reserved.
#;
#;******************************************************************************
#For EDKII use Only
[Defines]
  INF_VERSION                    = 0x00010005
  BASE_NAME                      = FabricResourceInit3Lib
  FILE_GUID                      = 1930D7FF-5069-4D44-908F-6B3CB4F38708
  MODULE_TYPE                    = BASE
  VERSION_STRING                 = 1.0
  LIBRARY_CLASS                  = FabricResourceInitLib

[Sources]
  FabricResourceInit3.c
  FabricResourceInitBasedOnNv3.c
  FabricResourceManager3.h

[Packages]
  MdePkg/MdePkg.dec
  AgesaModulePkg/AgesaCommonModulePkg.dec
  AgesaModulePkg/AgesaModuleDfPkg.dec
  AgesaPkg/AgesaPkg.dec

[LibraryClasses]
  BaseLib
  AmdBaseLib
  AmdS3SaveLib
  AmdHeapLib
  BaseFabricTopologyLib
  BaseCoreLogicalIdLib
  FabricRegisterAccLib
  IdsLib
  FabricResourceSizeForEachRbLib
  AmdSocBaseLib

[Guids]
  gFabricRootBridgeOrderInfoHobGuid

[Protocols]

[Ppis]

[Pcd]
  gEfiMdePkgTokenSpaceGuid.PcdPciExpressBaseAddress
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdBottomMmioReservedForPrimaryRb
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdMmioAbove4GLimit
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdMmioSizePerRbForNonPciDevice
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdAbove4GMmioSizePerRbForNonPciDevice
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdMmioPercentageForPrefetchable
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdFabricResourceDefaultMap
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdFabricResourceDefaultSizePtr
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdSmee
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdFabric1TbRemap

[Depex]

