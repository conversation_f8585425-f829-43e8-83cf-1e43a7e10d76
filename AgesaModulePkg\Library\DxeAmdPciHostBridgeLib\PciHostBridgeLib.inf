#;*****************************************************************************
#;
#; Copyright (C) 2008-2024 Advanced Micro Devices, Inc. All rights reserved.
#;
#;******************************************************************************

#;/** @file
#;  Library instance of PciHostBridgeLib library class for coreboot.
#;
#;  Copyright (C) 2016, Red Hat, Inc.
#;  Copyright (c) 2016, Intel Corporation. All rights reserved.<BR>
#;
#;  This program and the accompanying materials are licensed and made available
#;  under the terms and conditions of the BSD License which accompanies this
#;  distribution.  The full text of the license may be found at
#;  http://opensource.org/licenses/bsd-license.php.
#;
#;  THE PROGRAM IS DISTRIBUTED UNDER THE BSD LICENSE ON AN "AS IS" BASIS, WITHOUT
#;  WARRANTIES OR REPRESENTATIONS OF ANY KIND, EITHER EXPRESS OR IMPLIED.
#;
#;**/

[Defines]
  INF_VERSION                    = 0x00010005
  BASE_NAME                      = PciHostBridgeLib
  FILE_GUID                      = 74967FD7-A9A4-4B16-8B41-B26BC3230D0A
  MODULE_TYPE                    = DXE_DRIVER
  VERSION_STRING                 = 1.0
  LIBRARY_CLASS                  = PciHostBridgeLib

#
# The following information is for reference only and not required by the build
# tools.
#
#  VALID_ARCHITECTURES           = IA32 X64
#

[Sources]
  PciHostBridgeLib.c

[Packages]
  MdePkg/MdePkg.dec
  MdeModulePkg/MdeModulePkg.dec
  AgesaModulePkg/AgesaCommonModulePkg.dec
  AgesaModulePkg/AgesaModuleDfPkg.dec
  AgesaModulePkg/AgesaModuleNbioPkg.dec
  AgesaPkg/AgesaPkg.dec

[LibraryClasses]
  BaseMemoryLib
  PciHostBridgeLib
  DevicePathLib
  MemoryAllocationLib
  DebugLib
  UefiBootServicesTableLib
  AmdBaseLib

[Protocols]
  gAmdFabricResourceManagerServicesProtocolGuid   #CONSUMED
  gAmdFabricTopologyServices2ProtocolGuid         #CONSUMED
  gAmdCoreTopologyServicesProtocolGuid            #CONSUMED
  gAmdApcbDxeServiceProtocolGuid                  #CONSUMED
  gAmdNbioCxlServicesProtocolGuid                 #CONSUMED
  gEfiPciRootBridgeIoProtocolGuid                 #CONSUMED
  gEfiPciHostBridgeResourceAllocationProtocolGuid #CONSUMED

[Depex]
  gAmdFabricResourceManagerServicesProtocolGuid AND
  gAmdFabricTopologyServices2ProtocolGuid AND
  gAmdApcbDxeServiceProtocolGuid




