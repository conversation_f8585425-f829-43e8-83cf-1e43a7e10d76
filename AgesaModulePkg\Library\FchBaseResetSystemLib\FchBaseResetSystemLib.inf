#;*****************************************************************************
#;
#; Copyright (C) 2008-2024 Advanced Micro Devices, Inc. All rights reserved.
#;
#;******************************************************************************

[Defines]
  INF_VERSION                    = 0x00010006
  BASE_NAME                      = BaseResetSystemLib
  FILE_GUID                      = e669c365-2df2-4540-a343-afec4e85b198
  MODULE_TYPE                    = BASE
  VERSION_STRING                 = 1.0
  LIBRARY_CLASS                  = BaseResetSystemLib

#
#  VALID_ARCHITECTURES           = IA32 X64
#

[Sources]
  FchBaseResetSystemLib.c

[Packages]
  MdePkg/MdePkg.dec
  AgesaPkg/AgesaPkg.dec
  MdeModulePkg/MdeModulePkg.dec
  AgesaModulePkg/AgesaModuleFchPkg.dec

[LibraryClasses]
  BaseLib
  PrintLib
  IoLib
  FchBaseLib

[Protocols]

[Guids]

[Pcd]
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdFchFullHardReset

