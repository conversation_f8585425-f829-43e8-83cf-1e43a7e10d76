/*****************************************************************************
 * Copyright (C) 2024 Advanced Micro Devices, Inc. All rights reserved.
 *
*****************************************************************************
*/

/*----------------------------------------------------------------------------------------
 *                             M O D U L E S    U S E D
 *----------------------------------------------------------------------------------------
 */
#include <Uefi.h>
#include <Base.h>
#include <Library/IdsLib.h>
#include <Addendum/Apcb/Inc/CommonV3/ApcbV3Priority.h>
#include <Library/ApcbCoreLib.h>
#include <Library/ApcbLibV3Pei.h>

#include <Filecode.h>

/*----------------------------------------------------------------------------------------
 *                   D E F I N I T I O N S    A N D    M A C R O S
 *----------------------------------------------------------------------------------------
 */
#define FILECODE        LIBRARY_APCBLIBV3PEI_APCBLIBV3PEISERVICES_FILECODE
/*----------------------------------------------------------------------------------------
 *                  T Y P E D E F S     A N D     S T R U C T U R E S
 *----------------------------------------------------------------------------------------
 */

/*----------------------------------------------------------------------------------------
 *           P R O T O T Y P E S     O F     L O C A L     F U N C T I O N S
 *----------------------------------------------------------------------------------------
 */


/*----------------------------------------------------------------------------------------
 *                          E X P O R T E D    F U N C T I O N S
 *----------------------------------------------------------------------------------------
 */

/*----------------------------------------------------------------------------------------
 *                          G L O B A L        V A L U E S
 *----------------------------------------------------------------------------------------
 */

/*---------------------------------------------------------------------------------------*/
/**
 * @brief This function retrieves a boolean APCB token
 *
 * @param[in]         ApcbToken       - APCB token ID
 * @param[out]        Value           - APCB token value
 *
 * @retval            TRUE            - The token value is successfully retrived
 *                    FALSE           - The token cannot be found
 */
BOOLEAN
ApcbGetBool (
  IN       UINT32   ApcbToken,
  IN  OUT  BOOLEAN  *Value
  )
{
  UINT8                     *ApcbBuf;
  UINT32                    ApcbBufSize;
  APCB_PEI_VARIABLE_STRUCT  *pApcbPeiVariableStruct;

  pApcbPeiVariableStruct = GetApcbPeiVariableStructAddress ();
  if (pApcbPeiVariableStruct == NULL) {
    return FALSE;
  }

  ApcbBuf = (UINT8 *) GetApcbShadowCopy (&ApcbBufSize);
  if (NULL != ApcbBuf) {
    if (CORE_APCB_OK ==
        coreApcbGetBool (
          ApcbBuf, APCB_PRIORITY_LEVEL_TO_MASK (pApcbPeiVariableStruct->CurrentPriorityLevel),
          pApcbPeiVariableStruct->CurrentBoardMask, 0, ApcbToken, Value)) {
      return TRUE;
    }
  }

  return FALSE;
}

/*---------------------------------------------------------------------------------------*/
/**
 * @brief This function retrieves a UINT8 APCB token
 *
 * @param[in]         ApcbToken       - APCB token ID
 * @param[out]        Value           - APCB token value
 *
 * @retval            TRUE            - The token value is successfully retrived
 *                    FALSE           - The token cannot be found
 */
BOOLEAN
ApcbGet8 (
  IN       UINT32   ApcbToken,
  IN  OUT  UINT8    *Value
  )
{
  UINT8                     *ApcbBuf;
  UINT32                    ApcbBufSize;
  APCB_PEI_VARIABLE_STRUCT  *pApcbPeiVariableStruct;

  pApcbPeiVariableStruct = GetApcbPeiVariableStructAddress ();
  if (pApcbPeiVariableStruct == NULL) {
    return FALSE;
  }

  ApcbBuf = (UINT8 *) GetApcbShadowCopy (&ApcbBufSize);
  if (NULL != ApcbBuf) {
    if (CORE_APCB_OK ==
        coreApcbGet8 (
          ApcbBuf, APCB_PRIORITY_LEVEL_TO_MASK (pApcbPeiVariableStruct->CurrentPriorityLevel),
          pApcbPeiVariableStruct->CurrentBoardMask, 0, ApcbToken, Value)) {
      return TRUE;
    }
  }

  return FALSE;
}

/*---------------------------------------------------------------------------------------*/
/**
 * @brief This function retrieves a UINT16 APCB token
 *
 * @param[in]         ApcbToken       - APCB token ID
 * @param[out]        Value           - APCB token value
 *
 * @retval            TRUE            - The token value is successfully retrived
 *                    FALSE           - The token cannot be found
 */
BOOLEAN
ApcbGet16 (
  IN       UINT32   ApcbToken,
  IN  OUT  UINT16   *Value
  )
{
  UINT8                     *ApcbBuf;
  UINT32                    ApcbBufSize;
  APCB_PEI_VARIABLE_STRUCT  *pApcbPeiVariableStruct;

  pApcbPeiVariableStruct = GetApcbPeiVariableStructAddress ();
  if (pApcbPeiVariableStruct == NULL) {
    return FALSE;
  }

  ApcbBuf = (UINT8 *) GetApcbShadowCopy (&ApcbBufSize);
  if (NULL != ApcbBuf) {
    if (CORE_APCB_OK ==
        coreApcbGet16 (
          ApcbBuf, APCB_PRIORITY_LEVEL_TO_MASK (pApcbPeiVariableStruct->CurrentPriorityLevel),
          pApcbPeiVariableStruct->CurrentBoardMask, 0, ApcbToken, Value)) {
      return TRUE;
    }
  }

  return FALSE;
}


/*---------------------------------------------------------------------------------------*/
/**
 * @brief This function retrieves a UINT32 APCB token
 *
 * @param[in]         ApcbToken       - APCB token ID
 * @param[out]        Value           - APCB token value
 *
 * @retval            TRUE            - The token value is successfully retrived
 *                    FALSE           - The token cannot be found
 */
BOOLEAN
ApcbGet32 (
  IN       UINT32   ApcbToken,
  IN  OUT  UINT32   *Value
  )
{
  UINT8                     *ApcbBuf;
  UINT32                    ApcbBufSize;
  APCB_PEI_VARIABLE_STRUCT  *pApcbPeiVariableStruct;

  pApcbPeiVariableStruct = GetApcbPeiVariableStructAddress ();
  if (pApcbPeiVariableStruct == NULL) {
    return FALSE;
  }

  ApcbBuf = (UINT8 *) GetApcbShadowCopy (&ApcbBufSize);
  if (NULL != ApcbBuf) {
    if (CORE_APCB_OK ==
        coreApcbGet32 (
          ApcbBuf, APCB_PRIORITY_LEVEL_TO_MASK (pApcbPeiVariableStruct->CurrentPriorityLevel),
          pApcbPeiVariableStruct->CurrentBoardMask, 0, ApcbToken, Value)) {
      return TRUE;
    }
  }

  return FALSE;
}


/*---------------------------------------------------------------------------------------*/
/**
 * @brief This function gets the data of a specified type
 *
 * @param[in]         GroupId         - Group ID
 * @param[in]         TypeId          - Type ID
 * @param[in]         InstanceId      - Instance ID
 * @param[out]        DataBuf         - Pointer to the type data
 * @param[out]        DataSize        - Pointer to the size of the type data
 *
 * @retval            TRUE            - The type data is retrieved successfully
 *                    FALSE           - The type data cannot be retrieved
 */
BOOLEAN
ApcbGetType (
  IN       UINT16   GroupId,
  IN       UINT16   TypeId,
  IN       UINT16   InstanceId,
  IN  OUT  UINT8    **DataBuf,
  IN  OUT  UINT32   *DataSize
  )
{
  UINT8                     *ApcbBuf;
  UINT32                    ApcbBufSize;
  APCB_PEI_VARIABLE_STRUCT  *pApcbPeiVariableStruct;

  pApcbPeiVariableStruct = GetApcbPeiVariableStructAddress ();
  if (pApcbPeiVariableStruct == NULL) {
    return FALSE;
  }

  ApcbBuf = (UINT8 *) GetApcbShadowCopy (&ApcbBufSize);
  if (NULL != ApcbBuf) {
    if (CORE_APCB_OK ==
        coreApcbGetType (
          ApcbBuf, GroupId, TypeId, APCB_PRIORITY_LEVEL_TO_MASK (pApcbPeiVariableStruct->CurrentPriorityLevel),
          pApcbPeiVariableStruct->CurrentBoardMask, InstanceId, DataBuf, DataSize)) {
      return TRUE;
    }
  }

  return FALSE;
}

