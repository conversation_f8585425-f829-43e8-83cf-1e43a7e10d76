<?xml version="1.0" ?>
<DIRS AddressMode = "1">
  <PSP_DIR Level="1" Base="458752" Size="0x290000" SpiBlockSize="0x10000">
    <IMAGE_ENTRY Type="0x0" File="AgesaModulePkg\Firmwares\BRH\TypeId0x00_AmdPubKey_BRH.tkn"/>
    <POINT_ENTRY Type="0x1" Address="3408896" Size="0x30000"/>
    <IMAGE_ENTRY Type="0x2" File="AgesaModulePkg\Firmwares\BRH\TypeId0x02_PspOS_BRH.ecsbin"/>
    <IMAGE_ENTRY Type="0x3" File="AgesaModulePkg\Firmwares\BRH\TypeId0x03_PspRecBl_BRH.esbin"/>
    <IMAGE_ENTRY Type="0x8" File="AgesaModulePkg\Firmwares\BRH\TypeId0x08_SmuFirmware_breithorn.ecsbin"/>
    <IMAGE_ENTRY Type="0x208" File="AgesaModulePkg\Firmwares\BRH\TypeId0x208_SmuFirmware_BRHDense.ecsbin"/>
    <IMAGE_ENTRY Type="0xA" File="AgesaModulePkg\Firmwares\BRH\TypeId0x0A_PspAblPubKey_BRH.stkn"/>
    <VALUE_ENTRY Type="0xB" Value="0x01"/>
    <IMAGE_ENTRY Type="0x13" File="AgesaModulePkg\Firmwares\BRH\TypeId0x13_SduFw_BRH.ecsbin"/>
    <IMAGE_ENTRY Type="0x1A" File="AgesaModulePkg\Firmwares\BRH\TypeId0x1A_SevDriver_BRH.ecsbin"/>
    <IMAGE_ENTRY Type="0x15" File="AgesaModulePkg\Firmwares\BRH\TypeId0x15_IpKeyManagerDriver_BRH.ecsbin"/>
    <IMAGE_ENTRY Type="0x1B" File="AgesaModulePkg\Firmwares\BRH\TypeId0x1B_BootDriver_BRH.ecsbin"/>
    <IMAGE_ENTRY Type="0x1C" File="AgesaModulePkg\Firmwares\BRH\TypeId0x1C_SocDriver_BRH.ecsbin"/>
    <IMAGE_ENTRY Type="0x1D" File="AgesaModulePkg\Firmwares\BRH\TypeId0x1D_HadDriver_BRH.ecsbin"/>
    <IMAGE_ENTRY Type="0x1F" File="AgesaModulePkg\Firmwares\BRH\TypeId0x1F_InterfaceDriver_BRH.ecsbin"/>
    <IMAGE_ENTRY Type="0x21" File="AgesaModulePkg\Firmwares\BRH\TypeId0x21_PspAmdIkek_BRH.bin"/>
    <IMAGE_ENTRY Type="0x22" File="AgesaModulePkg\Firmwares\BRH\TypeId0x22_SecureEmptyToken.bin"/>
    <IMAGE_ENTRY Type="0x24" File="AgesaModulePkg\Firmwares\BRH\TypeId0x24_RegisterAccessPolicy_BRH.cesbin"/>
    <IMAGE_ENTRY Type="0x224" File="AgesaModulePkg\Firmwares\BRH\TypeId0x224_RegisterAccessPolicy_BRHDense.cesbin"/>
    <IMAGE_ENTRY Type="0x28" File="AgesaModulePkg\Firmwares\BRH\TypeId0x28_PspSystemDriver_BRH.ecsbin"/>
    <IMAGE_ENTRY Type="0x2a" File="AgesaModulePkg\Firmwares\BRH\TypeId0x2A_SmuFirmware_breithorn.ecsbin"/>
    <IMAGE_ENTRY Type="0x22a" File="AgesaModulePkg\Firmwares\BRH\TypeId0x22A_SmuFirmware_BRHDense.ecsbin"/>
    <IMAGE_ENTRY Type="0x2D" File="AgesaModulePkg\Firmwares\BRH\TypeId0x2D_AblRt.ecsbin"/>
    <IMAGE_ENTRY Type="0x30" File="AgesaModulePkg\Firmwares\BRH\TypeId0x30_AgesaBootLoaderU_BRH.ecsbin"/>
    <POINT_ENTRY Type="0x40" Address="3407872" Size="0x400"/>
    <IMAGE_ENTRY Type="0x42" File="AgesaModulePkg\Firmwares\BRH\TypeId0x42_PhyFw_BRH.ecsbin"/>
    <IMAGE_ENTRY Type="0x44" File="AgesaModulePkg\Firmwares\BRH\TypeId0x44_USB_PHY_BRH.esbin"/>
    <IMAGE_ENTRY Type="0x45" File="AgesaModulePkg\Firmwares\BRH\TypeId0x45_RegisterAccessPolicy_BRH.cesbin"/>
    <IMAGE_ENTRY Type="0x245" File="AgesaModulePkg\Firmwares\BRH\TypeId0x245_RegisterAccessPolicy_BRHDense.cesbin"/>
    <IMAGE_ENTRY Type="0x47" File="AgesaModulePkg\Firmwares\BRH\TypeId0x47_DRTMDriver_BRH.ecsbin"/>
    <IMAGE_ENTRY Type="0x50" File="AgesaModulePkg\Firmwares\BRH\TypeId0x50_PspKeyDataBase_BRH.sbin"/>
    <IMAGE_ENTRY Type="0x51" File="AgesaModulePkg\Firmwares\BRH\TypeId0x51_PspTosKeyDataBase_BRH.ecsbin"/>
    <IMAGE_ENTRY Type="0x55" File="AgesaModulePkg\Firmwares\BRH\TypeId0x55_SPLTable_BRH.sbin"/>
    <IMAGE_ENTRY Type="0x5D" File="AgesaModulePkg\Firmwares\BRH\TypeId0x5DMpioFw_BRH.cesbin"/>
    <IMAGE_ENTRY Type="0x64" File="AgesaModulePkg\Firmwares\BRH\TypeId0x64_RasDriver_BRH.ecsbin"/>
    <IMAGE_ENTRY Type="0x65" File="AgesaModulePkg\Firmwares\BRH\TypeId0x65_ta_ras_prod_amdTEE.ecsbin"/>
    <IMAGE_ENTRY Type="0x67" File="AgesaModulePkg\Firmwares\BRH\TypeId0x67_FHPDriver_BRH.ecsbin"/>
    <IMAGE_ENTRY Type="0x68" File="AgesaModulePkg\Firmwares\BRH\TypeId0x68_SPDMDriver_BRH.ecsbin"/>
    <IMAGE_ENTRY Type="0x69" File="AgesaModulePkg\Firmwares\BRH\TypeId0x69_DPEDriver_BRH.ecsbin"/>
    <IMAGE_ENTRY Type="0x73" File="AgesaModulePkg\Firmwares\BRH\TypeId0x73_PspBl_BRH.ecsbin"/>
    <IMAGE_ENTRY Type="0x76" File="AgesaModulePkg\Firmwares\BRH\TypeId0x76_DfRib_BRH.csbin"/>
    <IMAGE_ENTRY Type="0x8C" File="AgesaModulePkg\Firmwares\BRH\TypeId0x8C_MPDMATF_BRH.sbin"/>
    <IMAGE_ENTRY Type="0x91" File="AgesaModulePkg\Firmwares\BRH\TypeId0x91_GmiPhyFw_BRH.esbin"/>
    <IMAGE_ENTRY Type="0x92" File="AgesaModulePkg\Firmwares\BRH\TypeId0x92_Page_BRH.sbin"/>
    <IMAGE_ENTRY Type="0x9D" File="AgesaModulePkg\Firmwares\BRH\TypeId0x9D_AspSramFwExt_BRH.sbin"/>
    <IMAGE_ENTRY Type="0x9F" File="AgesaModulePkg\Firmwares\BRH\TypeId0x9F_psp_tos_wl_bin_brh.sbin"/>
    <IMAGE_ENTRY Type="0xA0" Instance="0x00" File="AgesaModulePkg\Firmwares\BRH\TypeId0xA0_S3Image_BRH_A0.sbin"/>
    <IMAGE_ENTRY Type="0xA0" Instance="0x01" File="AgesaModulePkg\Firmwares\BRH\TypeId0xA0_S3Image_BRHD_A0.sbin"/>
    <IMAGE_ENTRY Type="0xA0" Instance="0x02" File="AgesaModulePkg\Firmwares\BRH\TypeId0xA0_S3Image_BRH_B0.sbin"/>
    <IMAGE_ENTRY Type="0xA0" Instance="0x03" File="AgesaModulePkg\Firmwares\BRH\TypeId0xA0_S3Image_BRH_C0.sbin"/>
    <IMAGE_ENTRY Type="0xA0" Instance="0x04" File="AgesaModulePkg\Firmwares\BRH\TypeId0xA0_S3Image_BRHD_B0.sbin"/>
    <!--  Type=0xA0, Instance=0x05 is reserved for internal use  -->
    <IMAGE_ENTRY Type="0xA0" Instance="0x06" File="AgesaModulePkg\Firmwares\BRH\TypeId0xA0_S3Image_BRH_C1.sbin"/>
  </PSP_DIR>
  <BIOS_DIR Level="1" Base="3145728" Size="0x40000" SpiBlockSize=" 0x10000">
    <IMAGE_ENTRY Type="0x68" Instance="0x00" SubProgram="0x0" File="Build\Apcb\Turin\APCB_BRH_D4_DefaultRecovery.bin" Size="0xC000"/>
    <IMAGE_ENTRY Type="0x68" Instance="0x08" SubProgram="0x0" File="Build\Apcb\Turin\APCB_BRH_D4_Updatable.bin"/>
    <IMAGE_ENTRY Type="0x68" Instance="0x09" SubProgram="0x0" File="Build\Apcb\Turin\APCB_BRH_D4_EventLog.bin"/>
    <POINT_ENTRY Type="0x61" Address="0x0" Size="0x0" Destination="1974861824"/>
    <POINT_ENTRY Type="0x62" Address="12976128" Destination="1975910400" Size="0x3A0000">
     <TypeAttrib Compressed="0" Copy="0x1" ReadOnly="0x0" RegionType="0x0" ResetImage="0x1"/>
    </POINT_ENTRY>
    <IMAGE_ENTRY Type="0x64" Instance="0x03" SubProgram="0x0" File="AgesaModulePkg\Firmwares\BRH\Type0x64_AppbDdr5RdimmImem3_BRH.ecsbin"/>
    <IMAGE_ENTRY Type="0x64" Instance="0x03" SubProgram="0x4" File="AgesaModulePkg\Firmwares\BRH\Type0x64_AppbDdr5RdimmImem3_BRH_C0.ecsbin"/>
    <IMAGE_ENTRY Type="0x64" Instance="0x04" SubProgram="0x0" File="AgesaModulePkg\Firmwares\BRH\Type0x64_AppbDdr5RdimmImem4_BRH.ecsbin"/>
    <IMAGE_ENTRY Type="0x64" Instance="0x04" SubProgram="0x4" File="AgesaModulePkg\Firmwares\BRH\Type0x64_AppbDdr5RdimmImem4_BRH_C0.ecsbin"/>
    <IMAGE_ENTRY Type="0x64" Instance="0x09" SubProgram="0x0" File="AgesaModulePkg\Firmwares\BRH\Type0x64_AppbDdr5RdimmPosttrainImem9_BRH.ecsbin"/>
    <IMAGE_ENTRY Type="0x64" Instance="0x09" SubProgram="0x4" File="AgesaModulePkg\Firmwares\BRH\Type0x64_AppbDdr5RdimmPosttrainImem9_BRH_C0.ecsbin"/>
    <IMAGE_ENTRY Type="0x64" Instance="0x0A" SubProgram="0x0" File="AgesaModulePkg\Firmwares\BRH\Type0x64_AppbDdr5RdimmPosttrainImem10_BRH.ecsbin"/>
    <IMAGE_ENTRY Type="0x64" Instance="0x0A" SubProgram="0x4" File="AgesaModulePkg\Firmwares\BRH\Type0x64_AppbDdr5RdimmPosttrainImem10_BRH_C0.ecsbin"/>
    <IMAGE_ENTRY Type="0x64" Instance="0x0B" SubProgram="0x4" File="AgesaModulePkg\Firmwares\BRH\Type0x64_AppbDdr5RdimmQuickbootImem11_BRH_C0.ecsbin"/>
    <IMAGE_ENTRY Type="0x65" Instance="0x03" SubProgram="0x0" File="AgesaModulePkg\Firmwares\BRH\Type0x65_AppbDdr5RdimmDmem3_BRH.ecsbin"/>
    <IMAGE_ENTRY Type="0x65" Instance="0x03" SubProgram="0x4" File="AgesaModulePkg\Firmwares\BRH\Type0x65_AppbDdr5RdimmDmem3_BRH_C0.ecsbin"/>
    <IMAGE_ENTRY Type="0x65" Instance="0x04" SubProgram="0x0" File="AgesaModulePkg\Firmwares\BRH\Type0x65_AppbDdr5RdimmDmem4_BRH.ecsbin"/>
    <IMAGE_ENTRY Type="0x65" Instance="0x04" SubProgram="0x4" File="AgesaModulePkg\Firmwares\BRH\Type0x65_AppbDdr5RdimmDmem4_BRH_C0.ecsbin"/>
    <IMAGE_ENTRY Type="0x65" Instance="0x09" SubProgram="0x0" File="AgesaModulePkg\Firmwares\BRH\Type0x65_AppbDdr5RdimmPosttrainDmem9_BRH.ecsbin"/>
    <IMAGE_ENTRY Type="0x65" Instance="0x09" SubProgram="0x4" File="AgesaModulePkg\Firmwares\BRH\Type0x65_AppbDdr5RdimmPosttrainDmem9_BRH_C0.ecsbin"/>
    <IMAGE_ENTRY Type="0x65" Instance="0x0A" SubProgram="0x0" File="AgesaModulePkg\Firmwares\BRH\Type0x65_AppbDdr5RdimmPosttrainDmem10_BRH.ecsbin"/>
    <IMAGE_ENTRY Type="0x65" Instance="0x0A" SubProgram="0x4" File="AgesaModulePkg\Firmwares\BRH\Type0x65_AppbDdr5RdimmPosttrainDmem10_BRH_C0.ecsbin"/>
    <IMAGE_ENTRY Type="0x65" Instance="0x0B" SubProgram="0x4" File="AgesaModulePkg\Firmwares\BRH\Type0x65_AppbDdr5RdimmQuickbootDmem11_BRH_C0.ecsbin"/>
    <IMAGE_ENTRY Type="0x65" Instance="0x0C" SubProgram="0x4" File="AgesaModulePkg\Firmwares\BRH\Type0x65_AppbDdr5RdimmQuickbootDmem12_BRH_C0.ecsbin"/>
    <POINT_ENTRY Type="0x70" Address="6750208" Size="0x400"/>
  </BIOS_DIR>
  <PSP_DIR Level="2" Base="3407872" Size="0x330000" SpiBlockSize="0x10000">
    <IMAGE_ENTRY Type="0x1" File="AgesaModulePkg\Firmwares\BRH\TypeId0x01_PspBl_BRH.esbin" Size="0x30000"/>
    <IMAGE_ENTRY Type="0x2" File="AgesaModulePkg\Firmwares\BRH\TypeId0x02_PspOS_BRH.ecsbin"/>
    <IMAGE_ENTRY Type="0x8" File="AgesaModulePkg\Firmwares\BRH\TypeId0x08_SmuFirmware_breithorn.ecsbin"/>
    <IMAGE_ENTRY Type="0x208" File="AgesaModulePkg\Firmwares\BRH\TypeId0x208_SmuFirmware_BRHDense.ecsbin"/>
    <IMAGE_ENTRY Type="0x9" File="AgesaModulePkg\Firmwares\BRH\TypeId0x09_PspDebugUnlockToken_BRH.stkn"/>
    <IMAGE_ENTRY Type="0xA" File="AgesaModulePkg\Firmwares\BRH\TypeId0x0A_PspAblPubKey_BRH.stkn"/>
    <VALUE_ENTRY Type="0xB" Value="0x01"/>
    <IMAGE_ENTRY Type="0x13" File="AgesaModulePkg\Firmwares\BRH\TypeId0x13_SduFw_BRH.ecsbin"/>
    <IMAGE_ENTRY Type="0x15" File="AgesaModulePkg\Firmwares\BRH\TypeId0x15_IpKeyManagerDriver_BRH.ecsbin"/>
    <IMAGE_ENTRY Type="0x1A" File="AgesaModulePkg\Firmwares\BRH\TypeId0x1A_SevDriver_BRH.ecsbin"/>
    <IMAGE_ENTRY Type="0x1B" File="AgesaModulePkg\Firmwares\BRH\TypeId0x1B_BootDriver_BRH.ecsbin"/>
    <IMAGE_ENTRY Type="0x1C" File="AgesaModulePkg\Firmwares\BRH\TypeId0x1C_SocDriver_BRH.ecsbin"/>
    <IMAGE_ENTRY Type="0x1D" File="AgesaModulePkg\Firmwares\BRH\TypeId0x1D_HadDriver_BRH.ecsbin"/>
    <IMAGE_ENTRY Type="0x1F" File="AgesaModulePkg\Firmwares\BRH\TypeId0x1F_InterfaceDriver_BRH.ecsbin"/>
    <IMAGE_ENTRY Type="0x22" File="AgesaModulePkg\Firmwares\BRH\TypeId0x22_SecureEmptyToken.bin"/>
    <IMAGE_ENTRY Type="0x24" File="AgesaModulePkg\Firmwares\BRH\TypeId0x24_RegisterAccessPolicy_BRH.cesbin"/>
    <IMAGE_ENTRY Type="0x224" File="AgesaModulePkg\Firmwares\BRH\TypeId0x224_RegisterAccessPolicy_BRHDense.cesbin"/>
    <IMAGE_ENTRY Type="0x28" File="AgesaModulePkg\Firmwares\BRH\TypeId0x28_PspSystemDriver_BRH.ecsbin"/>
    <IMAGE_ENTRY Type="0x2a" File="AgesaModulePkg\Firmwares\BRH\TypeId0x2A_SmuFirmware_breithorn.ecsbin"/>
    <IMAGE_ENTRY Type="0x22a" File="AgesaModulePkg\Firmwares\BRH\TypeId0x22A_SmuFirmware_BRHDense.ecsbin"/>
    <IMAGE_ENTRY Type="0x2D" File="AgesaModulePkg\Firmwares\BRH\TypeId0x2D_AblRt.ecsbin"/>
    <IMAGE_ENTRY Type="0x30" File="AgesaModulePkg\Firmwares\BRH\TypeId0x30_AgesaBootLoaderU_BRH.ecsbin"/>
    <IMAGE_ENTRY Type="0x38" File="AgesaModulePkg\Firmwares\BRH\TypeId0x38_PspSevEmptyData.bin" Size="0x10000"/>
    <IMAGE_ENTRY Type="0x42" File="AgesaModulePkg\Firmwares\BRH\TypeId0x42_PhyFw_BRH.ecsbin"/>
    <IMAGE_ENTRY Type="0x44" File="AgesaModulePkg\Firmwares\BRH\TypeId0x44_USB_PHY_BRH.esbin"/>
    <IMAGE_ENTRY Type="0x45" File="AgesaModulePkg\Firmwares\BRH\TypeId0x45_RegisterAccessPolicy_BRH.cesbin"/>
    <IMAGE_ENTRY Type="0x245" File="AgesaModulePkg\Firmwares\BRH\TypeId0x245_RegisterAccessPolicy_BRHDense.cesbin"/>
    <IMAGE_ENTRY Type="0x47" File="AgesaModulePkg\Firmwares\BRH\TypeId0x47_DRTMDriver_BRH.ecsbin"/>
    <IMAGE_ENTRY Type="0x50" File="AgesaModulePkg\Firmwares\BRH\TypeId0x50_PspKeyDataBase_BRH.sbin"/>
    <IMAGE_ENTRY Type="0x51" File="AgesaModulePkg\Firmwares\BRH\TypeId0x51_PspTosKeyDataBase_BRH.ecsbin"/>
    <IMAGE_ENTRY Type="0x5D" File="AgesaModulePkg\Firmwares\BRH\TypeId0x5DMpioFw_BRH.cesbin"/>
    <IMAGE_ENTRY Type="0x64" File="AgesaModulePkg\Firmwares\BRH\TypeId0x64_RasDriver_BRH.ecsbin"/>
    <IMAGE_ENTRY Type="0x65" File="AgesaModulePkg\Firmwares\BRH\TypeId0x65_ta_ras_prod_amdTEE.ecsbin"/>
    <IMAGE_ENTRY Type="0x67" File="AgesaModulePkg\Firmwares\BRH\TypeId0x67_FHPDriver_BRH.ecsbin"/>
    <IMAGE_ENTRY Type="0x68" File="AgesaModulePkg\Firmwares\BRH\TypeId0x68_SPDMDriver_BRH.ecsbin"/>
    <IMAGE_ENTRY Type="0x69" File="AgesaModulePkg\Firmwares\BRH\TypeId0x69_DPEDriver_BRH.ecsbin"/>
    <IMAGE_ENTRY Type="0x73" File="AgesaModulePkg\Firmwares\BRH\TypeId0x73_PspBl_BRH.ecsbin"/>
    <IMAGE_ENTRY Type="0x76" File="AgesaModulePkg\Firmwares\BRH\TypeId0x76_DfRib_BRH.csbin"/>
    <IMAGE_ENTRY Type="0x8C" File="AgesaModulePkg\Firmwares\BRH\TypeId0x8C_MPDMATF_BRH.sbin"/>
    <IMAGE_ENTRY Type="0x91" File="AgesaModulePkg\Firmwares\BRH\TypeId0x91_GmiPhyFw_BRH.esbin"/>
    <IMAGE_ENTRY Type="0x92" File="AgesaModulePkg\Firmwares\BRH\TypeId0x92_Page_BRH.sbin"/>
    <IMAGE_ENTRY Type="0x9F" File="AgesaModulePkg\Firmwares\BRH\TypeId0x9F_psp_tos_wl_bin_brh.sbin"/>
    <IMAGE_ENTRY Type="0xA0" Instance="0x00" File="AgesaModulePkg\Firmwares\BRH\TypeId0xA0_S3Image_BRH_A0.sbin"/>
    <IMAGE_ENTRY Type="0xA0" Instance="0x01" File="AgesaModulePkg\Firmwares\BRH\TypeId0xA0_S3Image_BRHD_A0.sbin"/>
    <IMAGE_ENTRY Type="0xA0" Instance="0x02" File="AgesaModulePkg\Firmwares\BRH\TypeId0xA0_S3Image_BRH_B0.sbin"/>
    <IMAGE_ENTRY Type="0xA0" Instance="0x03" File="AgesaModulePkg\Firmwares\BRH\TypeId0xA0_S3Image_BRH_C0.sbin"/>
    <IMAGE_ENTRY Type="0xA0" Instance="0x04" File="AgesaModulePkg\Firmwares\BRH\TypeId0xA0_S3Image_BRHD_B0.sbin"/>
    <!--  Type=0xA0, Instance=0x05 is reserved for internal use  -->
    <IMAGE_ENTRY Type="0xA0" Instance="0x06" File="AgesaModulePkg\Firmwares\BRH\TypeId0xA0_S3Image_BRH_C1.sbin"/>
  </PSP_DIR>
  <BIOS_DIR Level="2" Base="6750208" Size="0x180000" SpiBlockSize=" 0x10000">
    <IMAGE_ENTRY Type="0x68" Instance="0x00" SubProgram="0x0" File="Build\Apcb\Turin\APCB_BRH_D4_DefaultRecovery.bin" Size="0xC000"/>
    <IMAGE_ENTRY Type="0x68" Instance="0x08" SubProgram="0x0" File="Build\Apcb\Turin\APCB_BRH_D4_Updatable.bin"/>
    <IMAGE_ENTRY Type="0x68" Instance="0x09" SubProgram="0x0" File="Build\Apcb\Turin\APCB_BRH_D4_EventLog.bin"/>
    <IMAGE_ENTRY Type="0x60" Instance="0x00" SubProgram="0x0" File="Build\Apcb\Turin\APCB_BRH_D4_Updatable.bin" Size="0x10000"/>
    <IMAGE_ENTRY Type="0x60" Instance="0x01" SubProgram="0x0" File="Build\Apcb\Turin\APCB_BRH_D4_EventLog.bin" Size="0x10000"/>
    <POINT_ENTRY Type="0x61" Address="0x0" Size="0x0" Destination="1974861824"/>
    <POINT_ENTRY Type="0x62" Address="12976128" Destination="1975910400" Size="0x3A0000">
     <TypeAttrib Compressed="0" Copy="0x1" ReadOnly="0x0" RegionType="0x0" ResetImage="0x1"/>
    </POINT_ENTRY>
    <IMAGE_ENTRY Type="0x63" Instance="0x00" SubProgram="0x0" File="AgesaModulePkg\Firmwares\BRH\APOB_NV_BRH.bin"/>
    <IMAGE_ENTRY Type="0x64" Instance="0x03" SubProgram="0x0" File="AgesaModulePkg\Firmwares\BRH\Type0x64_AppbDdr5RdimmImem3_BRH.ecsbin"/>
    <IMAGE_ENTRY Type="0x64" Instance="0x03" SubProgram="0x4" File="AgesaModulePkg\Firmwares\BRH\Type0x64_AppbDdr5RdimmImem3_BRH_C0.ecsbin"/>
    <IMAGE_ENTRY Type="0x64" Instance="0x04" SubProgram="0x0" File="AgesaModulePkg\Firmwares\BRH\Type0x64_AppbDdr5RdimmImem4_BRH.ecsbin"/>
    <IMAGE_ENTRY Type="0x64" Instance="0x04" SubProgram="0x4" File="AgesaModulePkg\Firmwares\BRH\Type0x64_AppbDdr5RdimmImem4_BRH_C0.ecsbin"/>
    <IMAGE_ENTRY Type="0x64" Instance="0x09" SubProgram="0x0" File="AgesaModulePkg\Firmwares\BRH\Type0x64_AppbDdr5RdimmPosttrainImem9_BRH.ecsbin"/>
    <IMAGE_ENTRY Type="0x64" Instance="0x09" SubProgram="0x4" File="AgesaModulePkg\Firmwares\BRH\Type0x64_AppbDdr5RdimmPosttrainImem9_BRH_C0.ecsbin"/>
    <IMAGE_ENTRY Type="0x64" Instance="0x0A" SubProgram="0x0" File="AgesaModulePkg\Firmwares\BRH\Type0x64_AppbDdr5RdimmPosttrainImem10_BRH.ecsbin"/>
    <IMAGE_ENTRY Type="0x64" Instance="0x0A" SubProgram="0x4" File="AgesaModulePkg\Firmwares\BRH\Type0x64_AppbDdr5RdimmPosttrainImem10_BRH_C0.ecsbin"/>
    <IMAGE_ENTRY Type="0x64" Instance="0x0B" SubProgram="0x4" File="AgesaModulePkg\Firmwares\BRH\Type0x64_AppbDdr5RdimmQuickbootImem11_BRH_C0.ecsbin"/>
    <IMAGE_ENTRY Type="0x65" Instance="0x03" SubProgram="0x0" File="AgesaModulePkg\Firmwares\BRH\Type0x65_AppbDdr5RdimmDmem3_BRH.ecsbin"/>
    <IMAGE_ENTRY Type="0x65" Instance="0x03" SubProgram="0x4" File="AgesaModulePkg\Firmwares\BRH\Type0x65_AppbDdr5RdimmDmem3_BRH_C0.ecsbin"/>
    <IMAGE_ENTRY Type="0x65" Instance="0x04" SubProgram="0x0" File="AgesaModulePkg\Firmwares\BRH\Type0x65_AppbDdr5RdimmDmem4_BRH.ecsbin"/>
    <IMAGE_ENTRY Type="0x65" Instance="0x04" SubProgram="0x4" File="AgesaModulePkg\Firmwares\BRH\Type0x65_AppbDdr5RdimmDmem4_BRH_C0.ecsbin"/>
    <IMAGE_ENTRY Type="0x65" Instance="0x09" SubProgram="0x0" File="AgesaModulePkg\Firmwares\BRH\Type0x65_AppbDdr5RdimmPosttrainDmem9_BRH.ecsbin"/>
    <IMAGE_ENTRY Type="0x65" Instance="0x09" SubProgram="0x4" File="AgesaModulePkg\Firmwares\BRH\Type0x65_AppbDdr5RdimmPosttrainDmem9_BRH_C0.ecsbin"/>
    <IMAGE_ENTRY Type="0x65" Instance="0x0A" SubProgram="0x0" File="AgesaModulePkg\Firmwares\BRH\Type0x65_AppbDdr5RdimmPosttrainDmem10_BRH.ecsbin"/>
    <IMAGE_ENTRY Type="0x65" Instance="0x0A" SubProgram="0x4" File="AgesaModulePkg\Firmwares\BRH\Type0x65_AppbDdr5RdimmPosttrainDmem10_BRH_C0.ecsbin"/>
    <IMAGE_ENTRY Type="0x65" Instance="0x0B" SubProgram="0x4" File="AgesaModulePkg\Firmwares\BRH\Type0x65_AppbDdr5RdimmQuickbootDmem11_BRH_C0.ecsbin"/>
    <IMAGE_ENTRY Type="0x65" Instance="0x0C" SubProgram="0x4" File="AgesaModulePkg\Firmwares\BRH\Type0x65_AppbDdr5RdimmQuickbootDmem12_BRH_C0.ecsbin"/>
    <IMAGE_ENTRY Type="0x66" Instance="0x00" File="AgesaModulePkg\Firmwares\BRH\UcodePatch_BRH_A0.bin"/>
    <IMAGE_ENTRY Type="0x66" Instance="0x01" File="AgesaModulePkg\Firmwares\BRH\UcodePatch_BRH_B0.bin"/>
    <IMAGE_ENTRY Type="0x66" Instance="0x02" File="AgesaModulePkg\Firmwares\BRH\UcodePatch_BRH_C0.bin"/>
    <IMAGE_ENTRY Type="0x66" Instance="0x03" File="AgesaModulePkg\Firmwares\BRH\UcodePatch_BRH_C1.bin"/>
    <IMAGE_ENTRY Type="0x66" Instance="0x04" File="AgesaModulePkg\Firmwares\BRH\UcodePatch_BRHD_A0.bin"/>
    <IMAGE_ENTRY Type="0x66" Instance="0x05" File="AgesaModulePkg\Firmwares\BRH\UcodePatch_BRHD_B0.bin"/>
    <IMAGE_ENTRY Type="0x69" File="earlyVgaProg.bin"/>
  </BIOS_DIR>
</DIRS>
