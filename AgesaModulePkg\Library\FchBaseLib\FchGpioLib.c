/*****************************************************************************
 *
 * Copyright (C) 2008-2024 Advanced Micro Devices, Inc. All rights reserved.
 *
 *******************************************************************************
 */

#include <Filecode.h>
#include <Library/AmdBaseLib.h>
#include <Library/FchBaseLib.h>
#include "FchRegistersCommon.h"

#define FILECODE LIBRARY_FCHBASELIB_FCHGPIOLIB_FILECODE

#define GPIO_IOMUX_VAILD    1
#define GPIO_IOMUX_INVAILD  0
#define GPIO_IOMUX_00       0
#define GPIO_IOMUX_01       1
#define GPIO_IOMUX_02       2
#define GPIO_IOMUX_03       3
#define GPIO_IOMUX_UNKNOW   0xFF


typedef struct {
  UINT8 GpioExisted;
  UINT8 GpioIomuxFunc;
} GPIO_IOMUX_INFO;


GPIO_IOMUX_INFO gGpioIoMuxFuncTable[] = {
  { GPIO_IOMUX_VAILD,   GPIO_IOMUX_01 },     //GPIO0   at  GPIO_IOMUX_01
  { GPIO_IOMUX_VAILD,   GPIO_IOMUX_01 },     //GPIO1   at  GPIO_IOMUX_01
  { GPIO_IOMUX_VAILD,   GPIO_IOMUX_01 },     //GPIO2   at  GPIO_IOMUX_01
  { GPIO_IOMUX_VAILD,   GPIO_IOMUX_00 },     //GPIO3   at  GPIO_IOMUX_00
  { GPIO_IOMUX_VAILD,   GPIO_IOMUX_00 },     //GPIO4   at  GPIO_IOMUX_00
  { GPIO_IOMUX_VAILD,   GPIO_IOMUX_00 },     //GPIO5   at  GPIO_IOMUX_00
  { GPIO_IOMUX_VAILD,   GPIO_IOMUX_00 },     //GPIO6   at  GPIO_IOMUX_00
  { GPIO_IOMUX_INVAILD, GPIO_IOMUX_UNKNOW }, //GPIO7   X
  { GPIO_IOMUX_VAILD,   GPIO_IOMUX_00 },     //GPIO8   at  GPIO_IOMUX_00
  { GPIO_IOMUX_VAILD,   GPIO_IOMUX_00 },     //GPIO9   at  GPIO_IOMUX_00
  { GPIO_IOMUX_VAILD,   GPIO_IOMUX_00 },     //GPIO10  at  GPIO_IOMUX_00
  { GPIO_IOMUX_VAILD,   GPIO_IOMUX_01 },     //GPIO11  at  GPIO_IOMUX_01
  { GPIO_IOMUX_VAILD,   GPIO_IOMUX_01 },     //GPIO12  at  GPIO_IOMUX_01
  { GPIO_IOMUX_INVAILD, GPIO_IOMUX_UNKNOW }, //GPIO13  X
  { GPIO_IOMUX_INVAILD, GPIO_IOMUX_UNKNOW }, //GPIO14  X
  { GPIO_IOMUX_INVAILD, GPIO_IOMUX_UNKNOW }, //GPIO15  X
  { GPIO_IOMUX_VAILD,   GPIO_IOMUX_01 },     //GPIO16  at  GPIO_IOMUX_01
  { GPIO_IOMUX_VAILD,   GPIO_IOMUX_01 },     //GPIO17  at  GPIO_IOMUX_01
  { GPIO_IOMUX_INVAILD, GPIO_IOMUX_UNKNOW }, //GPIO18  X
  { GPIO_IOMUX_VAILD,   GPIO_IOMUX_02 },     //GPIO19  at  GPIO_IOMUX_02
  { GPIO_IOMUX_VAILD,   GPIO_IOMUX_02 },     //GPIO20  at  GPIO_IOMUX_02
  { GPIO_IOMUX_VAILD,   GPIO_IOMUX_02 },     //GPIO21  at  GPIO_IOMUX_02
  { GPIO_IOMUX_VAILD,   GPIO_IOMUX_02 },     //GPIO22  at  GPIO_IOMUX_02
  { GPIO_IOMUX_VAILD,   GPIO_IOMUX_00 },     //GPIO23  at  GPIO_IOMUX_00
  { GPIO_IOMUX_INVAILD, GPIO_IOMUX_UNKNOW }, //GPIO24  X
  { GPIO_IOMUX_INVAILD, GPIO_IOMUX_UNKNOW }, //GPIO25  X
  { GPIO_IOMUX_VAILD,   GPIO_IOMUX_01 },     //GPIO26   at  GPIO_IOMUX_01
  { GPIO_IOMUX_VAILD,   GPIO_IOMUX_01 },     //GPIO27   at  GPIO_IOMUX_01
  { GPIO_IOMUX_VAILD,   GPIO_IOMUX_01 },     //GPIO28   at  GPIO_IOMUX_01
  { GPIO_IOMUX_VAILD,   GPIO_IOMUX_00 },     //GPIO29   at  GPIO_IOMUX_00
  { GPIO_IOMUX_VAILD,   GPIO_IOMUX_00 },     //GPIO30   at  GPIO_IOMUX_00
  { GPIO_IOMUX_VAILD,   GPIO_IOMUX_00 },     //GPIO31   at  GPIO_IOMUX_00
  { GPIO_IOMUX_VAILD,   GPIO_IOMUX_00 },     //GPIO32   at  GPIO_IOMUX_00
  { GPIO_IOMUX_INVAILD, GPIO_IOMUX_UNKNOW }, //GPIO33  X
  { GPIO_IOMUX_INVAILD, GPIO_IOMUX_UNKNOW }, //GPIO34  X
  { GPIO_IOMUX_INVAILD, GPIO_IOMUX_UNKNOW }, //GPIO35  X
  { GPIO_IOMUX_INVAILD, GPIO_IOMUX_UNKNOW }, //GPIO36  X
  { GPIO_IOMUX_INVAILD, GPIO_IOMUX_UNKNOW }, //GPIO37  X
  { GPIO_IOMUX_INVAILD, GPIO_IOMUX_UNKNOW }, //GPIO38  X
  { GPIO_IOMUX_INVAILD, GPIO_IOMUX_UNKNOW }, //GPIO39  X
  { GPIO_IOMUX_VAILD,   GPIO_IOMUX_00 },     //GPIO40  at  GPIO_IOMUX_00
  { GPIO_IOMUX_INVAILD, GPIO_IOMUX_UNKNOW }, //GPIO41  X
  { GPIO_IOMUX_VAILD,   GPIO_IOMUX_00 },     //GPIO42  at  GPIO_IOMUX_00
  { GPIO_IOMUX_INVAILD, GPIO_IOMUX_UNKNOW }, //GPIO43  X
  { GPIO_IOMUX_INVAILD, GPIO_IOMUX_UNKNOW }, //GPIO44  X
  { GPIO_IOMUX_INVAILD, GPIO_IOMUX_UNKNOW }, //GPIO45  X
  { GPIO_IOMUX_INVAILD, GPIO_IOMUX_UNKNOW }, //GPIO46  X
  { GPIO_IOMUX_INVAILD, GPIO_IOMUX_UNKNOW }, //GPIO47  X
  { GPIO_IOMUX_INVAILD, GPIO_IOMUX_UNKNOW }, //GPIO48  X
  { GPIO_IOMUX_INVAILD, GPIO_IOMUX_UNKNOW }, //GPIO49  X
  { GPIO_IOMUX_INVAILD, GPIO_IOMUX_UNKNOW }, //GPIO50  X
  { GPIO_IOMUX_INVAILD, GPIO_IOMUX_UNKNOW }, //GPIO51  X
  { GPIO_IOMUX_INVAILD, GPIO_IOMUX_UNKNOW }, //GPIO52  X
  { GPIO_IOMUX_INVAILD, GPIO_IOMUX_UNKNOW }, //GPIO53  X
  { GPIO_IOMUX_INVAILD, GPIO_IOMUX_UNKNOW }, //GPIO54  X
  { GPIO_IOMUX_INVAILD, GPIO_IOMUX_UNKNOW }, //GPIO55  X
  { GPIO_IOMUX_INVAILD, GPIO_IOMUX_UNKNOW }, //GPIO56  X
  { GPIO_IOMUX_INVAILD, GPIO_IOMUX_UNKNOW }, //GPIO57  X
  { GPIO_IOMUX_INVAILD, GPIO_IOMUX_UNKNOW }, //GPIO58  X
  { GPIO_IOMUX_INVAILD, GPIO_IOMUX_UNKNOW }, //GPIO59  X
  { GPIO_IOMUX_INVAILD, GPIO_IOMUX_UNKNOW }, //GPIO60  X
  { GPIO_IOMUX_INVAILD, GPIO_IOMUX_UNKNOW }, //GPIO61  X
  { GPIO_IOMUX_INVAILD, GPIO_IOMUX_UNKNOW }, //GPIO62  X
  { GPIO_IOMUX_INVAILD, GPIO_IOMUX_UNKNOW }, //GPIO63  X
  { GPIO_IOMUX_INVAILD, GPIO_IOMUX_UNKNOW }, //GPIO64  X
  { GPIO_IOMUX_INVAILD, GPIO_IOMUX_UNKNOW }, //GPIO65  X
  { GPIO_IOMUX_INVAILD, GPIO_IOMUX_UNKNOW }, //GPIO66  X
  { GPIO_IOMUX_INVAILD, GPIO_IOMUX_UNKNOW }, //GPIO67  X
  { GPIO_IOMUX_INVAILD, GPIO_IOMUX_UNKNOW }, //GPIO68  X
  { GPIO_IOMUX_INVAILD, GPIO_IOMUX_UNKNOW }, //GPIO69  X
  { GPIO_IOMUX_VAILD,   GPIO_IOMUX_00 },     //GPIO70  at  GPIO_IOMUX_00
  { GPIO_IOMUX_INVAILD, GPIO_IOMUX_UNKNOW }, //GPIO71  X
  { GPIO_IOMUX_INVAILD, GPIO_IOMUX_UNKNOW }, //GPIO72  X
  { GPIO_IOMUX_INVAILD, GPIO_IOMUX_UNKNOW }, //GPIO73  X
  { GPIO_IOMUX_VAILD,   GPIO_IOMUX_02 },     //GPIO74  at  GPIO_IOMUX_02
  { GPIO_IOMUX_VAILD,   GPIO_IOMUX_02 },     //GPIO75  at  GPIO_IOMUX_02
  { GPIO_IOMUX_VAILD,   GPIO_IOMUX_00 },     //GPIO76  at  GPIO_IOMUX_00
  { GPIO_IOMUX_INVAILD, GPIO_IOMUX_UNKNOW }, //GPIO77  X
  { GPIO_IOMUX_INVAILD, GPIO_IOMUX_UNKNOW }, //GPIO78  X
  { GPIO_IOMUX_INVAILD, GPIO_IOMUX_UNKNOW }, //GPIO79  X
  { GPIO_IOMUX_INVAILD, GPIO_IOMUX_UNKNOW }, //GPIO80  X
  { GPIO_IOMUX_INVAILD, GPIO_IOMUX_UNKNOW }, //GPIO81  X
  { GPIO_IOMUX_INVAILD, GPIO_IOMUX_UNKNOW }, //GPIO82  X
  { GPIO_IOMUX_INVAILD, GPIO_IOMUX_UNKNOW }, //GPIO83  X
  { GPIO_IOMUX_VAILD,   GPIO_IOMUX_01 },     //GPIO84  at  GPIO_IOMUX_01
  { GPIO_IOMUX_VAILD,   GPIO_IOMUX_01 },     //GPIO85  at  GPIO_IOMUX_01
  { GPIO_IOMUX_VAILD,   GPIO_IOMUX_01 },     //GPIO86  at  GPIO_IOMUX_01
  { GPIO_IOMUX_VAILD,   GPIO_IOMUX_02 },     //GPIO87  at  GPIO_IOMUX_02
  { GPIO_IOMUX_VAILD,   GPIO_IOMUX_02 },     //GPIO88  at  GPIO_IOMUX_02
  { GPIO_IOMUX_VAILD,   GPIO_IOMUX_02 },     //GPIO89  at  GPIO_IOMUX_02
  { GPIO_IOMUX_VAILD,   GPIO_IOMUX_02 },     //GPIO90  at  GPIO_IOMUX_02
  { GPIO_IOMUX_VAILD,   GPIO_IOMUX_01 },     //GPIO91  at  GPIO_IOMUX_01
  { GPIO_IOMUX_VAILD,   GPIO_IOMUX_03 },     //GPIO92  at  GPIO_IOMUX_03
  { GPIO_IOMUX_INVAILD, GPIO_IOMUX_UNKNOW }, //GPIO93  X
  { GPIO_IOMUX_INVAILD, GPIO_IOMUX_UNKNOW }, //GPIO94  X
  { GPIO_IOMUX_INVAILD, GPIO_IOMUX_UNKNOW }, //GPIO95  X
  { GPIO_IOMUX_INVAILD, GPIO_IOMUX_UNKNOW }, //GPIO96  X
  { GPIO_IOMUX_INVAILD, GPIO_IOMUX_UNKNOW }, //GPIO97  X
  { GPIO_IOMUX_INVAILD, GPIO_IOMUX_UNKNOW }, //GPIO98  X
  { GPIO_IOMUX_INVAILD, GPIO_IOMUX_UNKNOW }, //GPIO99  X
  { GPIO_IOMUX_INVAILD, GPIO_IOMUX_UNKNOW }, //GPIO100 X
  { GPIO_IOMUX_INVAILD, GPIO_IOMUX_UNKNOW }, //GPIO101 X
  { GPIO_IOMUX_INVAILD, GPIO_IOMUX_UNKNOW }, //GPIO102 X
  { GPIO_IOMUX_INVAILD, GPIO_IOMUX_UNKNOW }, //GPIO103 X
  { GPIO_IOMUX_VAILD,   GPIO_IOMUX_03 },     //GPIO104 at  GPIO_IOMUX_03
  { GPIO_IOMUX_VAILD,   GPIO_IOMUX_03 },     //GPIO105 at  GPIO_IOMUX_03
  { GPIO_IOMUX_VAILD,   GPIO_IOMUX_03 },     //GPIO106 at  GPIO_IOMUX_03
  { GPIO_IOMUX_VAILD,   GPIO_IOMUX_03 },     //GPIO107 at  GPIO_IOMUX_03
  { GPIO_IOMUX_VAILD,   GPIO_IOMUX_02 },     //GPIO108 at  GPIO_IOMUX_02
  { GPIO_IOMUX_VAILD,   GPIO_IOMUX_02 },     //GPIO109 at  GPIO_IOMUX_02
  { GPIO_IOMUX_INVAILD, GPIO_IOMUX_UNKNOW }, //GPIO110 X
  { GPIO_IOMUX_INVAILD, GPIO_IOMUX_UNKNOW }, //GPIO111 X
  { GPIO_IOMUX_INVAILD, GPIO_IOMUX_UNKNOW }, //GPIO112 X
  { GPIO_IOMUX_VAILD,   GPIO_IOMUX_02 },     //GPIO113 at  GPIO_IOMUX_02
  { GPIO_IOMUX_VAILD,   GPIO_IOMUX_02 },     //GPIO114 at  GPIO_IOMUX_02
  { GPIO_IOMUX_VAILD,   GPIO_IOMUX_01 },     //GPIO115 at  GPIO_IOMUX_01
  { GPIO_IOMUX_VAILD,   GPIO_IOMUX_01 },     //GPIO116 at  GPIO_IOMUX_01
  { GPIO_IOMUX_VAILD,   GPIO_IOMUX_01 },     //GPIO117 at  GPIO_IOMUX_01
  { GPIO_IOMUX_VAILD,   GPIO_IOMUX_01 },     //GPIO118 at  GPIO_IOMUX_01
  { GPIO_IOMUX_VAILD,   GPIO_IOMUX_02 },     //GPIO119 at  GPIO_IOMUX_02
  { GPIO_IOMUX_VAILD,   GPIO_IOMUX_01 },     //GPIO120 at  GPIO_IOMUX_01
  { GPIO_IOMUX_VAILD,   GPIO_IOMUX_01 },     //GPIO121 at  GPIO_IOMUX_01
  { GPIO_IOMUX_VAILD,   GPIO_IOMUX_01 },     //GPIO122 at  GPIO_IOMUX_01
  { GPIO_IOMUX_INVAILD, GPIO_IOMUX_UNKNOW }, //GPIO123 X
  { GPIO_IOMUX_INVAILD, GPIO_IOMUX_UNKNOW }, //GPIO124 X
  { GPIO_IOMUX_INVAILD, GPIO_IOMUX_UNKNOW }, //GPIO125 X
  { GPIO_IOMUX_INVAILD, GPIO_IOMUX_UNKNOW }, //GPIO126 X
  { GPIO_IOMUX_INVAILD, GPIO_IOMUX_UNKNOW }, //GPIO127 X
  { GPIO_IOMUX_INVAILD, GPIO_IOMUX_UNKNOW }, //GPIO128 X
  { GPIO_IOMUX_VAILD,   GPIO_IOMUX_02 },     //GPIO129 at GPIO_IOMUX_02
  { GPIO_IOMUX_VAILD,   GPIO_IOMUX_01 },     //GPIO130 at GPIO_IOMUX_01
  { GPIO_IOMUX_VAILD,   GPIO_IOMUX_03 },     //GPIO131 at GPIO_IOMUX_03
  { GPIO_IOMUX_VAILD,   GPIO_IOMUX_01 },     //GPIO132 at GPIO_IOMUX_01
  { GPIO_IOMUX_VAILD,   GPIO_IOMUX_01 },     //GPIO133 at GPIO_IOMUX_01
  { GPIO_IOMUX_VAILD,   GPIO_IOMUX_01 },     //GPIO134 at GPIO_IOMUX_01
  { GPIO_IOMUX_VAILD,   GPIO_IOMUX_02 },     //GPIO135 at GPIO_IOMUX_02
  { GPIO_IOMUX_VAILD,   GPIO_IOMUX_01 },     //GPIO136 at GPIO_IOMUX_01
  { GPIO_IOMUX_VAILD,   GPIO_IOMUX_02 },     //GPIO137 at GPIO_IOMUX_02
  { GPIO_IOMUX_VAILD,   GPIO_IOMUX_01 },     //GPIO138 at GPIO_IOMUX_01
  { GPIO_IOMUX_VAILD,   GPIO_IOMUX_01 },     //GPIO139 at GPIO_IOMUX_01
  { GPIO_IOMUX_VAILD,   GPIO_IOMUX_02 },     //GPIO140 at GPIO_IOMUX_02
  { GPIO_IOMUX_VAILD,   GPIO_IOMUX_01 },     //GPIO141 at GPIO_IOMUX_01
  { GPIO_IOMUX_VAILD,   GPIO_IOMUX_02 },     //GPIO142 at GPIO_IOMUX_02
  { GPIO_IOMUX_VAILD,   GPIO_IOMUX_01 },     //GPIO143 at GPIO_IOMUX_01
  { GPIO_IOMUX_VAILD,   GPIO_IOMUX_01 },     //GPIO144 at GPIO_IOMUX_01
  { GPIO_IOMUX_VAILD,   GPIO_IOMUX_01 },     //GPIO145 at GPIO_IOMUX_01
  { GPIO_IOMUX_VAILD,   GPIO_IOMUX_01 },     //GPIO146 at GPIO_IOMUX_01
  { GPIO_IOMUX_VAILD,   GPIO_IOMUX_01 },     //GPIO147 at GPIO_IOMUX_01
  { GPIO_IOMUX_VAILD,   GPIO_IOMUX_01 },     //GPIO148 at GPIO_IOMUX_01
  { GPIO_IOMUX_VAILD,   GPIO_IOMUX_01 },     //GPIO149 at GPIO_IOMUX_01
  { GPIO_IOMUX_VAILD,   GPIO_IOMUX_01 },     //GPIO150 at GPIO_IOMUX_01
  { GPIO_IOMUX_VAILD,   GPIO_IOMUX_01 },     //GPIO151 at GPIO_IOMUX_01
  { GPIO_IOMUX_VAILD,   GPIO_IOMUX_01 },     //GPIO152 at GPIO_IOMUX_01
};

GPIO_IOMUX_INFO gStoneGpioIoMuxFuncTable[] = {
  { GPIO_IOMUX_VAILD,   GPIO_IOMUX_01 },     //GPIO0   at  GPIO_IOMUX_01
  { GPIO_IOMUX_VAILD,   GPIO_IOMUX_01 },     //GPIO1   at  GPIO_IOMUX_01
  { GPIO_IOMUX_VAILD,   GPIO_IOMUX_01 },     //GPIO2   at  GPIO_IOMUX_01
  { GPIO_IOMUX_VAILD,   GPIO_IOMUX_00 },     //GPIO3   at  GPIO_IOMUX_00
  { GPIO_IOMUX_VAILD,   GPIO_IOMUX_00 },     //GPIO4   at  GPIO_IOMUX_00
  { GPIO_IOMUX_VAILD,   GPIO_IOMUX_00 },     //GPIO5   at  GPIO_IOMUX_00
  { GPIO_IOMUX_VAILD,   GPIO_IOMUX_00 },     //GPIO6   at  GPIO_IOMUX_00
  { GPIO_IOMUX_VAILD,   GPIO_IOMUX_00 },     //GPIO7   at  GPIO_IOMUX_00
  { GPIO_IOMUX_INVAILD, GPIO_IOMUX_UNKNOW }, //GPIO8   X
  { GPIO_IOMUX_INVAILD, GPIO_IOMUX_UNKNOW }, //GPIO9   X
  { GPIO_IOMUX_INVAILD, GPIO_IOMUX_UNKNOW }, //GPIO10  X
  { GPIO_IOMUX_INVAILD, GPIO_IOMUX_UNKNOW }, //GPIO11  X
  { GPIO_IOMUX_VAILD,   GPIO_IOMUX_01 },     //GPIO12  at  GPIO_IOMUX_01
  { GPIO_IOMUX_VAILD,   GPIO_IOMUX_02 },     //GPIO13  at  GPIO_IOMUX_02
  { GPIO_IOMUX_VAILD,   GPIO_IOMUX_02 },     //GPIO14  at  GPIO_IOMUX_02
  { GPIO_IOMUX_INVAILD, GPIO_IOMUX_UNKNOW }, //GPIO15  X
  { GPIO_IOMUX_VAILD,   GPIO_IOMUX_01 },     //GPIO16  at  GPIO_IOMUX_01
  { GPIO_IOMUX_VAILD,   GPIO_IOMUX_01 },     //GPIO17  at  GPIO_IOMUX_01
  { GPIO_IOMUX_INVAILD, GPIO_IOMUX_UNKNOW }, //GPIO18  X
  { GPIO_IOMUX_VAILD,   GPIO_IOMUX_02 },     //GPIO19  at  GPIO_IOMUX_02
  { GPIO_IOMUX_VAILD,   GPIO_IOMUX_02 },     //GPIO20  at  GPIO_IOMUX_02
  { GPIO_IOMUX_VAILD,   GPIO_IOMUX_00 },     //GPIO21  at  GPIO_IOMUX_00
  { GPIO_IOMUX_VAILD,   GPIO_IOMUX_00 },     //GPIO22  at  GPIO_IOMUX_00
  { GPIO_IOMUX_VAILD,   GPIO_IOMUX_01 },     //GPIO23  at  GPIO_IOMUX_01
  { GPIO_IOMUX_VAILD,   GPIO_IOMUX_01 },     //GPIO24  at  GPIO_IOMUX_01
  { GPIO_IOMUX_INVAILD, GPIO_IOMUX_UNKNOW }, //GPIO25  X
  { GPIO_IOMUX_VAILD,   GPIO_IOMUX_01 },     //GPIO26  at  GPIO_IOMUX_01
  { GPIO_IOMUX_INVAILD, GPIO_IOMUX_UNKNOW }, //GPIO27  X
  { GPIO_IOMUX_VAILD,   GPIO_IOMUX_01 },     //GPIO28  at  GPIO_IOMUX_01
  { GPIO_IOMUX_INVAILD, GPIO_IOMUX_UNKNOW }, //GPIO29  X
  { GPIO_IOMUX_INVAILD, GPIO_IOMUX_UNKNOW }, //GPIO30  X
  { GPIO_IOMUX_INVAILD, GPIO_IOMUX_UNKNOW }, //GPIO31  X
  { GPIO_IOMUX_INVAILD, GPIO_IOMUX_UNKNOW }, //GPIO32  X
  { GPIO_IOMUX_INVAILD, GPIO_IOMUX_UNKNOW }, //GPIO33  X
  { GPIO_IOMUX_INVAILD, GPIO_IOMUX_UNKNOW }, //GPIO34  X
  { GPIO_IOMUX_INVAILD, GPIO_IOMUX_UNKNOW }, //GPIO35  X
  { GPIO_IOMUX_INVAILD, GPIO_IOMUX_UNKNOW }, //GPIO36  X
  { GPIO_IOMUX_INVAILD, GPIO_IOMUX_UNKNOW }, //GPIO37  X
  { GPIO_IOMUX_INVAILD, GPIO_IOMUX_UNKNOW }, //GPIO38  X
  { GPIO_IOMUX_INVAILD, GPIO_IOMUX_UNKNOW }, //GPIO39  X
  { GPIO_IOMUX_INVAILD, GPIO_IOMUX_UNKNOW }, //GPIO40  X
  { GPIO_IOMUX_INVAILD, GPIO_IOMUX_UNKNOW }, //GPIO41  X
  { GPIO_IOMUX_INVAILD, GPIO_IOMUX_UNKNOW }, //GPIO42  X
  { GPIO_IOMUX_INVAILD, GPIO_IOMUX_UNKNOW }, //GPIO43  X
  { GPIO_IOMUX_INVAILD, GPIO_IOMUX_UNKNOW }, //GPIO44  X
  { GPIO_IOMUX_INVAILD, GPIO_IOMUX_UNKNOW }, //GPIO45  X
  { GPIO_IOMUX_INVAILD, GPIO_IOMUX_UNKNOW }, //GPIO46  X
  { GPIO_IOMUX_INVAILD, GPIO_IOMUX_UNKNOW }, //GPIO47  X
  { GPIO_IOMUX_INVAILD, GPIO_IOMUX_UNKNOW }, //GPIO48  X
  { GPIO_IOMUX_INVAILD, GPIO_IOMUX_UNKNOW }, //GPIO49  X
  { GPIO_IOMUX_INVAILD, GPIO_IOMUX_UNKNOW }, //GPIO50  X
  { GPIO_IOMUX_INVAILD, GPIO_IOMUX_UNKNOW }, //GPIO51  X
  { GPIO_IOMUX_INVAILD, GPIO_IOMUX_UNKNOW }, //GPIO52  X
  { GPIO_IOMUX_INVAILD, GPIO_IOMUX_UNKNOW }, //GPIO53  X
  { GPIO_IOMUX_INVAILD, GPIO_IOMUX_UNKNOW }, //GPIO54  X
  { GPIO_IOMUX_INVAILD, GPIO_IOMUX_UNKNOW }, //GPIO55  X
  { GPIO_IOMUX_INVAILD, GPIO_IOMUX_UNKNOW }, //GPIO56  X
  { GPIO_IOMUX_INVAILD, GPIO_IOMUX_UNKNOW }, //GPIO57  X
  { GPIO_IOMUX_INVAILD, GPIO_IOMUX_UNKNOW }, //GPIO58  X
  { GPIO_IOMUX_INVAILD, GPIO_IOMUX_UNKNOW }, //GPIO59  X
  { GPIO_IOMUX_INVAILD, GPIO_IOMUX_UNKNOW }, //GPIO60  X
  { GPIO_IOMUX_INVAILD, GPIO_IOMUX_UNKNOW }, //GPIO61  X
  { GPIO_IOMUX_INVAILD, GPIO_IOMUX_UNKNOW }, //GPIO62  X
  { GPIO_IOMUX_INVAILD, GPIO_IOMUX_UNKNOW }, //GPIO63  X
  { GPIO_IOMUX_INVAILD, GPIO_IOMUX_UNKNOW }, //GPIO64  X
  { GPIO_IOMUX_INVAILD, GPIO_IOMUX_UNKNOW }, //GPIO65  X
  { GPIO_IOMUX_INVAILD, GPIO_IOMUX_UNKNOW }, //GPIO66  X
  { GPIO_IOMUX_INVAILD, GPIO_IOMUX_UNKNOW }, //GPIO67  X
  { GPIO_IOMUX_INVAILD, GPIO_IOMUX_UNKNOW }, //GPIO68  X
  { GPIO_IOMUX_INVAILD, GPIO_IOMUX_UNKNOW }, //GPIO69  X
  { GPIO_IOMUX_INVAILD, GPIO_IOMUX_UNKNOW }, //GPIO70  X
  { GPIO_IOMUX_INVAILD, GPIO_IOMUX_UNKNOW }, //GPIO71  X
  { GPIO_IOMUX_INVAILD, GPIO_IOMUX_UNKNOW }, //GPIO72  X
  { GPIO_IOMUX_INVAILD, GPIO_IOMUX_UNKNOW }, //GPIO73  X
  { GPIO_IOMUX_VAILD,   GPIO_IOMUX_02 },     //GPIO74  at  GPIO_IOMUX_02
  { GPIO_IOMUX_VAILD,   GPIO_IOMUX_00 },     //GPIO75  at  GPIO_IOMUX_00
  { GPIO_IOMUX_VAILD,   GPIO_IOMUX_00 },     //GPIO76  at  GPIO_IOMUX_00
  { GPIO_IOMUX_INVAILD, GPIO_IOMUX_UNKNOW }, //GPIO77  X
  { GPIO_IOMUX_INVAILD, GPIO_IOMUX_UNKNOW }, //GPIO78  X
  { GPIO_IOMUX_INVAILD, GPIO_IOMUX_UNKNOW }, //GPIO79  X
  { GPIO_IOMUX_INVAILD, GPIO_IOMUX_UNKNOW }, //GPIO80  X
  { GPIO_IOMUX_INVAILD, GPIO_IOMUX_UNKNOW }, //GPIO81  X
  { GPIO_IOMUX_INVAILD, GPIO_IOMUX_UNKNOW }, //GPIO82  X
  { GPIO_IOMUX_INVAILD, GPIO_IOMUX_UNKNOW }, //GPIO83  X
  { GPIO_IOMUX_INVAILD, GPIO_IOMUX_UNKNOW }, //GPIO84  X
  { GPIO_IOMUX_INVAILD, GPIO_IOMUX_UNKNOW }, //GPIO85  X
  { GPIO_IOMUX_VAILD,   GPIO_IOMUX_00 },     //GPIO86  at  GPIO_IOMUX_00
  { GPIO_IOMUX_VAILD,   GPIO_IOMUX_00 },     //GPIO87  at  GPIO_IOMUX_00
  { GPIO_IOMUX_VAILD,   GPIO_IOMUX_00 },     //GPIO88  at  GPIO_IOMUX_00
  { GPIO_IOMUX_VAILD,   GPIO_IOMUX_02 },     //GPIO89  at  GPIO_IOMUX_02
  { GPIO_IOMUX_INVAILD, GPIO_IOMUX_UNKNOW }, //GPIO90  X
  { GPIO_IOMUX_INVAILD, GPIO_IOMUX_UNKNOW }, //GPIO91  X
  { GPIO_IOMUX_INVAILD, GPIO_IOMUX_UNKNOW }, //GPIO92  X
  { GPIO_IOMUX_INVAILD, GPIO_IOMUX_UNKNOW }, //GPIO93  X
  { GPIO_IOMUX_INVAILD, GPIO_IOMUX_UNKNOW }, //GPIO94  X
  { GPIO_IOMUX_INVAILD, GPIO_IOMUX_UNKNOW }, //GPIO95  X
  { GPIO_IOMUX_INVAILD, GPIO_IOMUX_UNKNOW }, //GPIO96  X
  { GPIO_IOMUX_INVAILD, GPIO_IOMUX_UNKNOW }, //GPIO97  X
  { GPIO_IOMUX_INVAILD, GPIO_IOMUX_UNKNOW }, //GPIO98  X
  { GPIO_IOMUX_INVAILD, GPIO_IOMUX_UNKNOW }, //GPIO99  X
  { GPIO_IOMUX_INVAILD, GPIO_IOMUX_UNKNOW }, //GPIO100 X
  { GPIO_IOMUX_INVAILD, GPIO_IOMUX_UNKNOW }, //GPIO101 X
  { GPIO_IOMUX_INVAILD, GPIO_IOMUX_UNKNOW }, //GPIO102 X
  { GPIO_IOMUX_INVAILD, GPIO_IOMUX_UNKNOW }, //GPIO103 X
  { GPIO_IOMUX_VAILD,   GPIO_IOMUX_00 },     //GPIO104 at  GPIO_IOMUX_00
  { GPIO_IOMUX_VAILD,   GPIO_IOMUX_00 },     //GPIO105 at  GPIO_IOMUX_00
  { GPIO_IOMUX_VAILD,   GPIO_IOMUX_00 },     //GPIO106 at  GPIO_IOMUX_00
  { GPIO_IOMUX_VAILD,   GPIO_IOMUX_00 },     //GPIO107 at  GPIO_IOMUX_00
  { GPIO_IOMUX_VAILD,   GPIO_IOMUX_00 },     //GPIO108 at  GPIO_IOMUX_00
  { GPIO_IOMUX_VAILD,   GPIO_IOMUX_01 },     //GPIO109 at  GPIO_IOMUX_01
  { GPIO_IOMUX_VAILD,   GPIO_IOMUX_01 },     //GPIO110 at  GPIO_IOMUX_01
  { GPIO_IOMUX_INVAILD, GPIO_IOMUX_UNKNOW }, //GPIO111 X
  { GPIO_IOMUX_INVAILD, GPIO_IOMUX_UNKNOW }, //GPIO112 X
  { GPIO_IOMUX_INVAILD, GPIO_IOMUX_UNKNOW }, //GPIO113 X
  { GPIO_IOMUX_INVAILD, GPIO_IOMUX_UNKNOW }, //GPIO114 X
  { GPIO_IOMUX_VAILD,   GPIO_IOMUX_00 },     //GPIO115 at  GPIO_IOMUX_00
  { GPIO_IOMUX_VAILD,   GPIO_IOMUX_00 },     //GPIO116 at  GPIO_IOMUX_00
  { GPIO_IOMUX_VAILD,   GPIO_IOMUX_01 },     //GPIO117 at  GPIO_IOMUX_01
  { GPIO_IOMUX_VAILD,   GPIO_IOMUX_01 },     //GPIO118 at  GPIO_IOMUX_01
  { GPIO_IOMUX_VAILD,   GPIO_IOMUX_01 },     //GPIO119 at  GPIO_IOMUX_01
  { GPIO_IOMUX_VAILD,   GPIO_IOMUX_01 },     //GPIO120 at  GPIO_IOMUX_01
  { GPIO_IOMUX_VAILD,   GPIO_IOMUX_01 },     //GPIO121 at  GPIO_IOMUX_01
  { GPIO_IOMUX_VAILD,   GPIO_IOMUX_01 },     //GPIO122 at  GPIO_IOMUX_01
  { GPIO_IOMUX_VAILD,   GPIO_IOMUX_01 },     //GPIO123 at  GPIO_IOMUX_01
  { GPIO_IOMUX_VAILD,   GPIO_IOMUX_01 },     //GPIO124 at  GPIO_IOMUX_01
  { GPIO_IOMUX_VAILD,   GPIO_IOMUX_01 },     //GPIO125 at  GPIO_IOMUX_01
  { GPIO_IOMUX_VAILD,   GPIO_IOMUX_01 },     //GPIO126 at  GPIO_IOMUX_01
  { GPIO_IOMUX_INVAILD, GPIO_IOMUX_UNKNOW }, //GPIO127 X
  { GPIO_IOMUX_INVAILD, GPIO_IOMUX_UNKNOW }, //GPIO128 X
  { GPIO_IOMUX_VAILD,   GPIO_IOMUX_02 },     //GPIO129 at GPIO_IOMUX_02
  { GPIO_IOMUX_INVAILD, GPIO_IOMUX_UNKNOW }, //GPIO130 X
  { GPIO_IOMUX_VAILD,   GPIO_IOMUX_01 },     //GPIO131 at GPIO_IOMUX_01
  { GPIO_IOMUX_VAILD,   GPIO_IOMUX_01 },     //GPIO132 at GPIO_IOMUX_01
  { GPIO_IOMUX_VAILD,   GPIO_IOMUX_01 },     //GPIO133 at GPIO_IOMUX_01
  { GPIO_IOMUX_VAILD,   GPIO_IOMUX_01 },     //GPIO134 at GPIO_IOMUX_01
  { GPIO_IOMUX_VAILD,   GPIO_IOMUX_02 },     //GPIO135 at GPIO_IOMUX_02
  { GPIO_IOMUX_VAILD,   GPIO_IOMUX_01 },     //GPIO136 at GPIO_IOMUX_01
  { GPIO_IOMUX_VAILD,   GPIO_IOMUX_02 },     //GPIO137 at GPIO_IOMUX_02
  { GPIO_IOMUX_VAILD,   GPIO_IOMUX_01 },     //GPIO138 at GPIO_IOMUX_01
  { GPIO_IOMUX_VAILD,   GPIO_IOMUX_01 },     //GPIO139 at GPIO_IOMUX_01
  { GPIO_IOMUX_INVAILD, GPIO_IOMUX_UNKNOW }, //GPIO140 X
  { GPIO_IOMUX_VAILD,   GPIO_IOMUX_01 },     //GPIO141 at GPIO_IOMUX_01
  { GPIO_IOMUX_VAILD,   GPIO_IOMUX_01 },     //GPIO142 at GPIO_IOMUX_01
  { GPIO_IOMUX_INVAILD, GPIO_IOMUX_UNKNOW }, //GPIO143 X
  { GPIO_IOMUX_INVAILD, GPIO_IOMUX_UNKNOW }, //GPIO144 X
  { GPIO_IOMUX_VAILD,   GPIO_IOMUX_03 },     //GPIO145 at  GPIO_IOMUX_03
  { GPIO_IOMUX_VAILD,   GPIO_IOMUX_03 },     //GPIO146 at  GPIO_IOMUX_03
  { GPIO_IOMUX_VAILD,   GPIO_IOMUX_02 },     //GPIO147 at  GPIO_IOMUX_02
  { GPIO_IOMUX_VAILD,   GPIO_IOMUX_02 },     //GPIO148 at  GPIO_IOMUX_02
  { GPIO_IOMUX_VAILD,   GPIO_IOMUX_02 },     //GPIO149 at  GPIO_IOMUX_02
  { GPIO_IOMUX_VAILD,   GPIO_IOMUX_02 },     //GPIO150 at  GPIO_IOMUX_02
  { GPIO_IOMUX_VAILD,   GPIO_IOMUX_02 },     //GPIO151 at  GPIO_IOMUX_02
  { GPIO_IOMUX_VAILD,   GPIO_IOMUX_02 },     //GPIO152 at  GPIO_IOMUX_02
};


/*----------------------------------------------------------------------------------------*/
/**
 * @brief  FchGpioConfigIomux
 *
 *
 *
 * @param IN UINT8 Socket
 * @param IN UINT8 Die
 * @param IN UINT8 GpioNum
 *
 * return FALSE: fail, TRUE success;
 */

BOOLEAN
FchGpioConfigIomux (
  IN UINT32 Socket,
  IN UINT32 Die,
  IN UINT32 GpioNum
  )
{
  UINTN mTotallyGpio = sizeof(gGpioIoMuxFuncTable) / sizeof(GPIO_IOMUX_INFO);
  GPIO_IOMUX_INFO *mGpioIomuxInfo = (GPIO_IOMUX_INFO *) gGpioIoMuxFuncTable;
  UINTN mGpioIomux = ACPI_MMIO_BASE + IOMUX_BASE;

  if (!FchCheckGnSoc() && !FchCheckSspSoc() && !FchCheckBaSoc()) {
    mTotallyGpio = sizeof(gStoneGpioIoMuxFuncTable) / sizeof(GPIO_IOMUX_INFO);
    mGpioIomuxInfo = (GPIO_IOMUX_INFO *) gStoneGpioIoMuxFuncTable;
  }

  if ( mTotallyGpio <= GpioNum )
    //GpioNum bigger than platform has.
    return FALSE;

  mGpioIomuxInfo += GpioNum;

  if ( !mGpioIomuxInfo->GpioExisted )
    //The Iomux function information does not existed
    return FALSE;

  mGpioIomux += GpioNum;

  //clear iomux_gpio_x of IOMUX register
  *((volatile UINT8 *)(mGpioIomux)) &= ~0x03;
  //set Iomux register to GPIO
  *((volatile UINT8 *)(mGpioIomux)) |= mGpioIomuxInfo->GpioIomuxFunc;

  return TRUE;
}

