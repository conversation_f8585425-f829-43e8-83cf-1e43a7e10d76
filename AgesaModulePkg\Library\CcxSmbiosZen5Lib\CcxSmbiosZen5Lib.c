/*
 ******************************************************************************
 *
 * Copyright (C) 2008-2024 Advanced Micro Devices, Inc. All rights reserved.
 *
 *******************************************************************************
 */
/* $NoKeywords:$ */
/**
 * @file
 *
 * AMD Ccx SMBIOS Zen5 Library
 *
 *
 * @xrefitem bom "File Content Label" "Release Content"
 * @e project:      AGESA
 * @e sub-project:  Lib
 * @e \$Revision$   @e \$Date$
 *
 */

/*----------------------------------------------------------------------------------------
 *                             M O D U L E S    U S E D
 *----------------------------------------------------------------------------------------
 */
#include <Library/BaseLib.h>
#include <AGESA.h>
#include <cpuRegisters.h>
#include <Filecode.h>
#include "CcxSmbiosZen5CommonLib.h"
#include <PiDxe.h>
#include <Library/AmdBaseLib.h>
#include <Library/CcxSmbiosLib.h>
#include <Library/CcxMpServicesLib.h>
#include <Library/UefiBootServicesTableLib.h>
#include <Protocol/FabricTopologyServices2.h>
#include <AmdSoc.h>

/*----------------------------------------------------------------------------------------
 *                   D E F I N I T I O N S    A N D    M A C R O S
 *----------------------------------------------------------------------------------------
 */
#define FILECODE LIBRARY_CCXSMBIOSZEN5LIB_CCXSMBIOSZEN5LIB_FILECODE

/*----------------------------------------------------------------------------------------
 *                  T Y P E D E F S     A N D     S T R U C T U R E S
 *----------------------------------------------------------------------------------------
 */

/*----------------------------------------------------------------------------------------
 *           P R O T O T Y P E S     O F     L O C A L     F U N C T I O N S
 *----------------------------------------------------------------------------------------
 */
UINT8
CcxGetDmiCacheAssociative (
  IN       UINT32  NumOfWays
  );


/*----------------------------------------------------------------------------------------
 *                          E X P O R T E D    F U N C T I O N S
 *----------------------------------------------------------------------------------------
 */

/* -----------------------------------------------------------------------------*/
/**
 *
 *  CcxGetProcessorFamilyForSmbios
 *
 *    Get processor family information for SMBIOS
 *
 *    @param[in]      Socket         Zero-based socket number to check
 *    @param[in]      StdHeader      Standard Head Pointer
 *
 */
UINT16
CcxGetProcessorFamilyForSmbios (
  IN       UINTN               Socket,
  IN       AMD_CONFIG_PARAMS  *StdHeader
  )
{
  return 0x6B;
}

/* -----------------------------------------------------------------------------*/
/**
 *
 *  CcxGetProcessorUpgradeForSmbios
 *
 *    Get processor upgrade information for SMBIOS
 *
 *    @param[in]      Socket         Zero-based socket number to check
 *    @param[in]      StdHeader      Standard Head Pointer
 *
 */
UINT8
CcxGetProcessorUpgradeForSmbios (
  IN       UINTN               Socket,
  IN       AMD_CONFIG_PARAMS  *StdHeader
  )
{
  UINT8       PackageType;
  UINT8       ProcUpgrade;
  UINT32      EBX_Reg = 0;

  AsmCpuid (
      AMD_CPUID_FMF,
      NULL,
      &EBX_Reg,
      NULL,
      NULL
      );
  PackageType = (UINT8) (EBX_Reg >> 28) & 0xF; // bit 31:28

  // Processor Upgrade field is reported as "Other"
  // for the Sockets not registered with DMTF SMBIOS
  switch (PackageType) {
  case ZEN5_PKG_AM5:
    ProcUpgrade = P_UPGRADE_AM5;
    break;
  case ZEN5_PKG_FP8:
    ProcUpgrade = P_UPGRADE_OTHER;
    break;
  case ZEN5_PKG_SP5:
    ProcUpgrade = P_UPGRADE_SP5;
    break;
  case ZEN5_SHP_PKG_SP6:
    ProcUpgrade = P_UPGRADE_SP6;
    break;
  default:
    ProcUpgrade = P_UPGRADE_UNKNOWN;
    break;
  }

  return ProcUpgrade;
}

VOID
EFIAPI
CcxGetL3CacheSize (
  IN OUT   VOID                *Buffer
  )
{
  UINT32                EBX_Reg;
  UINT32                ECX_Reg;
  UINT32               *CacheSize;

  CacheSize = (UINT32 *)Buffer;
  // (CacheNumSets+1)*(CacheNumWays+1)*(CacheLineSize+1)= CacheSize (Byte)
  // CPUID_Fn8000001D_ECX_x03 Core::X86::Cpuid::CachePropEcx3[31:0] CacheNumSets: cache number of sets.
  // CPUID_Fn8000001D_EBX_x03 Core::X86::Cpuid::CachePropEbx3[31:22] CacheNumWays: cache number of ways.
  // CPUID_Fn8000001D_EBX_x03 Core::X86::Cpuid::CachePropEbx3[11:0] CacheLineSize: cache line size in bytes.
  EBX_Reg = 0;
  ECX_Reg = 0;
  AsmCpuidEx (
      AMD_CPUID_CacheProperties,
      3,
      NULL,
      &EBX_Reg,
      &ECX_Reg,
      NULL
      );
  // AdjustGranularity input default is 1K granularity
  *CacheSize = *CacheSize + ((ECX_Reg + 1) * (((EBX_Reg >> 22) & 0x3FF) + 1) * ((EBX_Reg & 0xFFF) + 1)) / 1024; // KB
}

VOID
CcxGetCacheInfo (
  IN       UINTN               Socket,
  IN       GET_CACHE_INFO     *CacheInfo,
  IN       AMD_CONFIG_PARAMS  *StdHeader
  )
{
  UINT32                CacheSize;
  UINT32                NumThreads;
  UINT32                NumSharing;
  UINT32                EAX_Reg;
  UINT32                EBX_Reg;
  UINT32                ECX_Reg;
  UINT32                EDX_Reg;
  UINTN                 NumberOfSockets;
  EFI_STATUS            Status;
  AMD_FABRIC_TOPOLOGY_SERVICES2_PROTOCOL  *FabricTopology;

  ECX_Reg = 0;
  AsmCpuidEx (
      0x80000008,
      0,
      NULL,
      NULL,
      &ECX_Reg,
      NULL
      );
  NumThreads = ((ECX_Reg & 0xFFF) + 1);

  // L1 Size & Associativity
  EAX_Reg = 0;
  AsmCpuidEx (
      AMD_CPUID_CacheProperties,
      0,
      &EAX_Reg,
      NULL,
      NULL,
      NULL
      );
  NumSharing = (((EAX_Reg >> 14) & 0xFFF) + 1);

  ECX_Reg = 0;
  AsmCpuid (
      AMD_CPUID_TLB_L1Cache,
      NULL,
      NULL,
      &ECX_Reg,
      NULL
      );
  CacheSize = (ECX_Reg >> 24) * (NumThreads / NumSharing);

  EAX_Reg = 0;
  AsmCpuidEx (
      AMD_CPUID_CacheProperties,
      1,
      &EAX_Reg,
      NULL,
      NULL,
      NULL
      );
  NumSharing = (((EAX_Reg >> 14) & 0xFFF) + 1);

  EDX_Reg = 0;
  AsmCpuid (
      AMD_CPUID_TLB_L1Cache,
      NULL,
      NULL,
      NULL,
      &EDX_Reg
      );
  CacheSize += (EDX_Reg >> 24) * (NumThreads / NumSharing);
  CacheInfo->CacheEachLevelInfo[CpuL1Cache].CacheSize = AdjustGranularity (&CacheSize);
  CacheInfo->CacheEachLevelInfo[CpuL1Cache].Associativity = DMI_ASSOCIATIVE_8_WAY;

  // L2 Size & Associativity
  EAX_Reg = 0;
  AsmCpuidEx (
      AMD_CPUID_CacheProperties,
      2,
      &EAX_Reg,
      NULL,
      NULL,
      NULL
      );
  NumSharing = (((EAX_Reg >> 14) & 0xFFF) + 1);

  ECX_Reg = 0;
  AsmCpuid (
      AMD_CPUID_L2L3Cache_L2TLB,
      NULL,
      NULL,
      &ECX_Reg,
      NULL
      );
  CacheSize = (ECX_Reg >> 16) * (NumThreads / NumSharing);
  CacheInfo->CacheEachLevelInfo[CpuL2Cache].CacheSize = AdjustGranularity (&CacheSize);
  CacheInfo->CacheEachLevelInfo[CpuL2Cache].Associativity = DMI_ASSOCIATIVE_16_WAY;

  // L3 Size & Associativity
  EDX_Reg = 0;
  AsmCpuid (
      AMD_CPUID_L2L3Cache_L2TLB,
      NULL,
      NULL,
      NULL,
      &EDX_Reg
      );
  // CPUID_Fn80000006_EDX Core::X86::Cpuid::L3CacheId[15:12] L3Assoc 9h, Invalid, not reported here.
  // There are insufficient available encodings to represent all possible L3 associativities
  if (((EDX_Reg >> 12) & 0xF) == 0x9) {
    CacheSize = 0;
    CcxGetL3CacheSize ((VOID *)&CacheSize);
    CcxRunFunctionOnAps (ALL_COMPLEX_PRIMARY, CcxGetL3CacheSize, (VOID *)&CacheSize, BLOCKING_MODE);
  } else {
    CacheSize = (EDX_Reg >> 18) * 512;
  }

  NumberOfSockets = 1;

  // Locate FabricTopologyServices2Protocol
  Status = gBS->LocateProtocol (&gAmdFabricTopologyServices2ProtocolGuid, NULL, (VOID **) &FabricTopology);
  ASSERT_EFI_ERROR (Status);

  if (!EFI_ERROR (Status)) {
    Status = FabricTopology->GetSystemInfo (FabricTopology, &NumberOfSockets, NULL, NULL, NULL, NULL);
    ASSERT_EFI_ERROR (Status);
  }

  if (NumberOfSockets > 1) {
    // in multi-socket system, assume sockets are the same (total L3 cache size is equally distributed between sockets)
    CacheSize /= (UINT32) NumberOfSockets;
  }
  CacheInfo->CacheEachLevelInfo[CpuL3Cache].CacheSize = AdjustGranularity (&CacheSize);

  EBX_Reg = 0;
  AsmCpuidEx (
      AMD_CPUID_CacheProperties,
      3,
      NULL,
      &EBX_Reg,
      NULL,
      NULL
      );
  CacheInfo->CacheEachLevelInfo[CpuL3Cache].Associativity = CcxGetDmiCacheAssociative (((EBX_Reg >> 22) & 0x3FF) + 1);
}

/*---------------------------------------------------------------------------------------
 *                           L O C A L    F U N C T I O N S
 *---------------------------------------------------------------------------------------
 */
UINT8
CcxGetDmiCacheAssociative (
  IN       UINT32  NumOfWays
  )
{
  UINT8  Associativity;

  switch (NumOfWays) {
  case 1:
    Associativity = DMI_ASSOCIATIVE_DIRECT_MAPPED;
    break;
  case 2:
    Associativity = DMI_ASSOCIATIVE_2_WAY;
    break;
  case 4:
    Associativity = DMI_ASSOCIATIVE_4_WAY;
    break;
  case 8:
    Associativity = DMI_ASSOCIATIVE_8_WAY;
    break;
  case 12:
    Associativity = DMI_ASSOCIATIVE_12_WAY;
    break;
  case 16:
    Associativity = DMI_ASSOCIATIVE_16_WAY;
    break;
  case 20:
    Associativity = DMI_ASSOCIATIVE_20_WAY;
    break;
  case 24:
    Associativity = DMI_ASSOCIATIVE_24_WAY;
    break;
  case 32:
    Associativity = DMI_ASSOCIATIVE_32_WAY;
    break;
  case 48:
    Associativity = DMI_ASSOCIATIVE_48_WAY;
    break;
  case 64:
    Associativity = DMI_ASSOCIATIVE_64_WAY;
    break;
  default:
    Associativity = DMI_ASSOCIATIVE_UNKNOWN;
    break;
  }

  return Associativity;
}

