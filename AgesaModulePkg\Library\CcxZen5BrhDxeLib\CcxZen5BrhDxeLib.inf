#;*****************************************************************************
#;
#; Copyright (C) 2008-2024 Advanced Micro Devices, Inc. All rights reserved.
#;
#;*****************************************************************************

[Defines]
  INF_VERSION                    = 0x00010005
  BASE_NAME                      = CcxZen5BrhDxeLib
  FILE_GUID                      = D7E48973-57B8-4A2C-B9CE-13C13FF48EEF
  MODULE_TYPE                    = DXE_DRIVER
  VERSION_STRING                 = 1.0
  LIBRARY_CLASS                  = CcxZen5DxeLib

[Sources]
  CcxZen5BrhDxeLib.c

[Packages]
  MdePkg/MdePkg.dec
  AgesaModulePkg/AgesaCommonModulePkg.dec
  AgesaModulePkg/AgesaModuleCcxPkg.dec
  AgesaModulePkg/AgesaModuleDfPkg.dec
  AgesaPkg/AgesaPkg.dec

[LibraryClasses]
  FabricRegisterAccLib
  PcdLib
  IdsLib

[Guids]

[Protocols]
  gAmdFabricResourceManagerServicesProtocolGuid #CONSUMED
  gAmdFabricTopologyServices2ProtocolGuid #CONSUMED

[Pcd]
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdRmpCover64BitMMIORanges
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdRmp64BitMmioS0RbMask
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdRmp64BitMmioS1RbMask

[Depex]
  gAmdFabricTopologyServices2ProtocolGuid  AND
  gAmdFabricResourceManagerServicesProtocolGuid
