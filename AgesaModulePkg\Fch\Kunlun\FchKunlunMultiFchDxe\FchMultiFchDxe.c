/*****************************************************************************
 * Copyright (C) 2008-2024 Advanced Micro Devices, Inc. All rights reserved.
 *
*****************************************************************************
*/
#include "FchMultiFchDxe.h"
#include <Protocol/FabricTopologyServices2.h>
#include <Protocol/AmdApcbProtocol.h>


extern EFI_GUID gFchMultiFchResetDataHobGuid;

static BOOLEAN gFchSpdControlOwnership;

VOID
GetApcbValue (
  )
{
  EFI_STATUS                Status;
  AMD_APCB_SERVICE_PROTOCOL *ApcbDxeServiceProtocol;
  UINT8                     ApcbPurpose;

  Status                  = EFI_SUCCESS;
  ApcbDxeServiceProtocol  = NULL;
  ApcbPurpose             = 0;

  IDS_HDT_CONSOLE (FCH_TRACE, "%a - Start\n", __FUNCTION__);

  gFchSpdControlOwnership = ApcbFchSpdCtrlOwnershipLinked;

  IDS_HDT_CONSOLE (FCH_TRACE, "Locate Apcb Dxe Service Protocol.\n");
  Status = gBS->LocateProtocol (&gAmdApcbDxeServiceProtocolGuid, NULL, (VOID **) &ApcbDxeServiceProtocol);
  IDS_HDT_CONSOLE (FCH_TRACE, "Status = %r\n", Status);
  ASSERT(!EFI_ERROR(Status));
  if (!EFI_ERROR (Status)) {
    // Get Value of APCB_TOKEN_UID_FCH_SPD_CONTROL_OWNERSHIP
    IDS_HDT_CONSOLE (FCH_TRACE, "Get Value of APCB_TOKEN_UID_FCH_SPD_CONTROL_OWNERSHIP\n");
    ApcbPurpose = 0;
    Status = ApcbDxeServiceProtocol->ApcbGetTokenBool (
                                       ApcbDxeServiceProtocol,
                                       &ApcbPurpose,
                                       APCB_TOKEN_UID_FCH_SPD_CONTROL_OWNERSHIP,
                                       &gFchSpdControlOwnership
                                       );
    IDS_HDT_CONSOLE (FCH_TRACE, "Status = %r\n", Status);
    if (EFI_ERROR (Status)) {
      IDS_HDT_CONSOLE (FCH_TRACE, "Cannot get the APCB_TOKEN_UID_FCH_SPD_CONTROL_OWNERSHIP\n");
    } else {
      IDS_HDT_CONSOLE (FCH_TRACE, "gFchSpdControlOwnership = %d\n", gFchSpdControlOwnership);
    }
  }

  IDS_HDT_CONSOLE (FCH_TRACE, "%a - End\n", __FUNCTION__);
}

/**
 * @brief Get Socket 1 Bus Number.
 *
 * @returns UINTN        2nd socket bus number
 */
UINTN
FchDxeGetSocket1Bus (
  VOID
  )
{
  EFI_STATUS                              Status;
  UINTN                                   BusNumberBase;
  AMD_FABRIC_TOPOLOGY_SERVICES2_PROTOCOL  *FabricTopology;
  BOOLEAN                                 HasFchDevice;
  UINTN                                   i;
  UINTN                                   NumberOfRootBridges;

  Status          = EFI_SUCCESS;
  FabricTopology  = NULL;
  BusNumberBase   = 0;

  Status = gBS->LocateProtocol (
                    &gAmdFabricTopologyServices2ProtocolGuid,
                    NULL,
                    (VOID **) &FabricTopology
                    );
  if (Status == EFI_SUCCESS) {
    Status = FabricTopology->GetDieInfo (
                               FabricTopology,
                               FCH_SECOND_SOCKET,
                               FCH_SECOND_SOCKET_DIE,
                               &NumberOfRootBridges,
                               NULL,
                               NULL
                               );
    if (Status == EFI_SUCCESS) {
      for (i = 0; i < NumberOfRootBridges; i++) {
        Status = FabricTopology->GetRootBridgeInfo (
                                   FabricTopology,
                                   FCH_SECOND_SOCKET,
                                   FCH_SECOND_SOCKET_DIE,
                                   i,
                                   NULL,
                                   &BusNumberBase,
                                   NULL,
                                   NULL,
                                   &HasFchDevice,
                                   NULL
                                   );
        if (!EFI_ERROR (Status) && HasFchDevice) {
          DEBUG ((DEBUG_INFO, "[%a] Multi FCH DXE Get 2nd Socket Bus Number = 0x%x\n", __FUNCTION__, BusNumberBase));
          return BusNumberBase;
        }
      }
    }
  }
  ASSERT (FALSE);
  return 0;
}

/**
 * @brief Entry point of the Kunlun mutlti FCH DXE Driver.
 *
 * @details Entry point of the AMD FCH MCM DXE driver
 *   Perform the configuration init, resource reservation, early post init
 *   and install all the supported protocol for FCH MCM support
 *
 * @param[in] ImageHandle EFI Image Handle for the DXE driver
 * @param[in] SystemTable Pointer to the EFI system table
 *
 * @returns EFI_STATUS
 * @retval EFI_SUCCESS   Module initialized successfully
 * @retval EFI_ERROR     Initialization failed (see error for more details)
 */
EFI_STATUS
EFIAPI
MultiFchDxeInit (
  IN       EFI_HANDLE         ImageHandle,
  IN       EFI_SYSTEM_TABLE   *SystemTable
  )
{
  UINT32              FchBusNum;
  UINT8               SataController;
  UINT8               SataEnable2;
  UINT8               SataSgpioMultiDieEnable;
  UINT64              SataMultiDiePortShutDown;
  UINT64              SataMultiDiePortESP;
  UINT64              SataMultiDieDevSlp;
  UINT64              SataPortMode;
  FCH_INIT_PROTOCOL   *FchInit;
  FCH_DATA_BLOCK      *FchDataPtr;
  EFI_STATUS          Status;
  EFI_HANDLE          Handle;
  UINTN               NumberOfInstalledProcessors;
  UINTN               TotalNumberOfDie;
  UINTN               TotalNumberOfRootBridges;
  EFI_HOB_GUID_TYPE          *FchHob;
  FCH_MULTI_FCH_DXE_PRIVATE  *FchMfPrivate;
  FCH_MULITI_FCH_DATA_BLOCK  *FchMfData;
  AMD_FABRIC_TOPOLOGY_SERVICES2_PROTOCOL  *FabricTopology;
  UINT8               DevSlp0ControllerNum;
  UINT8               DevSlp1ControllerNum;
  PCI_ADDR            NbioPciAddress;
  UINT32              SmuArg[6];
  UINT8               syncFloodResetDelay = 0;

  AGESA_TESTPOINT (TpFchMultiFchDxeEntry, NULL);
  //
  // Check if socket1 exists
  //
  Status = gBS->LocateProtocol (
                  &gAmdFabricTopologyServices2ProtocolGuid,
                  NULL,
                  (VOID**)&FabricTopology
                  );
  ASSERT_EFI_ERROR ( Status);
  Status = FabricTopology->GetSystemInfo (
                             FabricTopology,
                             &NumberOfInstalledProcessors,
                             &TotalNumberOfDie,
                             &TotalNumberOfRootBridges,
                             NULL,
                             NULL
                             );
  ASSERT_EFI_ERROR ( Status);
  IDS_HDT_CONSOLE (
    FCH_TRACE,
    "[%a] NumberOfInstalledProcessors = 0x%x TotalNumberOfDie = 0x%x TotalNumberOfRootBridges = 0x%x\n",
    __FUNCTION__,
    NumberOfInstalledProcessors,
    TotalNumberOfDie,
    TotalNumberOfRootBridges
    );
  if (NumberOfInstalledProcessors == 1) {
    AGESA_TESTPOINT (TpFchMultiFchDxeExit, NULL);
    IDS_HDT_CONSOLE (FCH_TRACE, "[%a] No Socket1 FCH...Exit\n", __FUNCTION__);
    return EFI_SUCCESS;
  }

  // Get APCB value
  GetApcbValue ();

  //
  // Initialize the configuration structure and private data area
  //
  //find HOB and update with reset data block
  FchHob = GetFirstGuidHob (&gFchMultiFchResetDataHobGuid);
  if (FchHob == NULL) {
    IDS_HDT_CONSOLE (FCH_TRACE, "FCH HOB Not located, Exiting.\n");
    return EFI_UNSUPPORTED;
  }
  FchHob++;

  Status = gBS->AllocatePool (
                  EfiBootServicesData,
                  sizeof (FCH_MULTI_FCH_DXE_PRIVATE),
                  (VOID **)&FchMfPrivate
                  );
  ASSERT_EFI_ERROR (Status);

  // Update Private Data
  ZeroMem (FchMfPrivate, sizeof (FCH_MULTI_FCH_DXE_PRIVATE));
  FchMfPrivate->Signature = FCH_MULTI_FCH_DXE_PRIVATE_DATA_SIGNATURE;
  FchMfPrivate->FchMultiFchInit.Revision     = FCH_MULTI_FCH_INIT_REV;
  FchMfData = &FchMfPrivate->FchMultiFchInit.FchMfData;
  gBS->CopyMem (
         FchMfData,
         FchHob,
         sizeof (FCH_MULITI_FCH_DATA_BLOCK)
         );

  //
  // Locate Fch INIT Protocol
  //
  Status = gBS->LocateProtocol (
                  &gFchInitProtocolGuid,
                  NULL,
                  (VOID **)&FchInit
                  );
  ASSERT_EFI_ERROR (Status);

  // Allocate memory for the local data
  Status = gBS->AllocatePool (
                  EfiBootServicesData,
                  sizeof (FCH_DATA_BLOCK),
                  (VOID **)&FchDataPtr
                  );
  ASSERT_EFI_ERROR (Status);

  gBS->CopyMem (
         FchDataPtr,
         FchInit->FchPolicy,
         sizeof (FCH_DATA_BLOCK)
         );

  //
  // Update local Data Structure
  //
  SataEnable2 = PcdGet8 (PcdSataEnable2) >> 4;
  SataMultiDiePortShutDown = PcdGet64 (PcdSataMultiDiePortShutDown) >> 32;
  SataMultiDiePortESP = PcdGet64 (PcdSataMultiDiePortESP) >> 32;
  SataMultiDieDevSlp = PcdGet64 (PcdSataMultiDieDevSlp);
  SataSgpioMultiDieEnable = PcdGet8 (PcdSataSgpioMultiDieEnable) >> 4;
  SataPortMode     = PcdGet64 (PcdSataIoDie1PortMode);

  for (SataController = 0; SataController < KUNLUN_SATA_CONTROLLER_NUM; SataController++) {
    FchDataPtr->Sata[SataController].SataEnable       = (SataEnable2 >> SataController) & BIT0;
    FchDataPtr->Sata[SataController].SataEspPort      = (UINT8)(SataMultiDiePortESP >> (8 * SataController));
    FchDataPtr->Sata[SataController].SataPortPower    = (UINT8)(SataMultiDiePortShutDown >> (8 * SataController));
    FchDataPtr->Sata[SataController].SataPortMd       = (UINT16)(SataPortMode >> (16 * SataController));
    FchDataPtr->Sata[SataController].SataSgpio0       = (UINT8)((SataSgpioMultiDieEnable >> SataController) & BIT0);
    FchDataPtr->Sata[SataController].SataUBMDiagMode  = PcdGetBool (PcdSataUBMDiagMode);
    FchDataPtr->Sata[SataController].SataDevSlpPort0  = FALSE;    // Reset DevSlp0
    FchDataPtr->Sata[SataController].SataDevSlpPort1  = FALSE;    // Reset DevSlp1
  }

  IDS_HDT_CONSOLE (FCH_TRACE, "[MultiFchDxeInit] SataMultiDieDevSlp0: %d.\n", SataMultiDieDevSlp & BIT0);
  if ((SataMultiDieDevSlp & BIT0) == BIT0) {
    DevSlp0ControllerNum = ((SataMultiDieDevSlp & 0xF0) >> 4) - 4; // Note: Second socket controller number starts from 4
    ASSERT (DevSlp0ControllerNum < 4);
    FchDataPtr->Sata[DevSlp0ControllerNum].SataDevSlpPort0 = TRUE;
    FchDataPtr->Sata[DevSlp0ControllerNum].SataDevSlpPort0Num = (SataMultiDieDevSlp & 0x0F) >> 1;
    IDS_HDT_CONSOLE (FCH_TRACE, "[FchInitDataBlock] MultiDieDevSlp0 ControllerNum: %d, PortNum: %d.\n", DevSlp0ControllerNum, FchDataPtr->Sata[DevSlp0ControllerNum].SataDevSlpPort0Num);
  }

  IDS_HDT_CONSOLE (FCH_TRACE, "[MultiFchDxeInit] SataMultiDieDevSlp0: %d.\n", SataMultiDieDevSlp & BIT8);
  if ((SataMultiDieDevSlp & BIT8) == BIT8) {
    DevSlp1ControllerNum = ((SataMultiDieDevSlp & 0xF000) >> 12) - 4; // Note: Second socket controller number starts from 4
    ASSERT (DevSlp1ControllerNum < 4);
    FchDataPtr->Sata[DevSlp1ControllerNum].SataDevSlpPort1 = TRUE;
    FchDataPtr->Sata[DevSlp1ControllerNum].SataDevSlpPort1Num = (SataMultiDieDevSlp & 0xF00) >> 9;
    IDS_HDT_CONSOLE (FCH_TRACE, "[FchInitDataBlock] MultiDieDevSlp1 ControllerNum: %d, PortNum: %d.\n", DevSlp1ControllerNum, FchDataPtr->Sata[DevSlp1ControllerNum].SataDevSlpPort1Num);
  }

  FchDataPtr->HwAcpi.FchAcpiMmioBase               = (UINT32) FchMfData->FchAcpiMmioBase[1];
  ASSERT (FchDataPtr->HwAcpi.FchAcpiMmioBase!=NULL);

  FchBusNum = (UINT32) FchDxeGetSocket1Bus ();
  ASSERT (FchBusNum!=0);

  //Secondary Fch init

  // Send SMU SyncFlood setting
  NbioSmuServiceCommonInitArguments (SmuArg);
  SmuArg[0] = PcdGetBool (PcdResetCpuOnSyncFlood) ? 1 : 0;
  NbioPciAddress.AddressValue = MAKE_SBDFO (DF_GET_SEGMENT(FchBusNum), DF_GET_BUS(FchBusNum), 0, 0, 0);
  if (NbioSmuServiceRequest (NbioPciAddress, BIOSSMC_MSG_SetResetCpuOnSyncFlood, SmuArg, 0)) {
    DEBUG ((DEBUG_INFO, "Send Reset Cpu On Sync Flood successful\n"));
  } else {
    DEBUG ((DEBUG_INFO, "Send Reset Cpu On Sync Flood failed\n"));
  }
  //send SMC delay sync flood when PcdResetCpuOnSyncFlood enabled
  if (PcdGetBool (PcdResetCpuOnSyncFlood)) {
    syncFloodResetDelay = PcdGet8 ( PcdDelayResetCpuOnSyncFlood);
    DEBUG ((DEBUG_INFO, "syncFloodResetDelay: %d min for socket1 \n", syncFloodResetDelay));
    if (  syncFloodResetDelay > 0 && syncFloodResetDelay < 5){
    // Allow rang 5 to 255 minutes for enabled sync flood reset delay. Treat it as 0 if it is not within enabled range.
      DEBUG ((DEBUG_INFO, "disable syncFloodResetDelay for socket1\n"));
      syncFloodResetDelay = 0;
    }
    SmuArg[0] =  syncFloodResetDelay;
    NbioSmuServiceRequest (NbioPciAddress, BIOSSMC_MSG_SetDelayResetCpuOnSyncFlood, SmuArg, 0);
  }

  //USB
  FchKLXhciIohcPmeDisable (FchBusNum, TRUE);

  //SATA
  FchKLSecondaryFchInitSataDxe (1, FchBusNum, FchDataPtr);

  // set SATA enable PCD
  SataEnable2 = PcdGet8 (PcdSataEnable2);
  SataEnable2 &= 0x0F;
  for (SataController = 0; SataController < KUNLUN_SATA_CONTROLLER_NUM; SataController++) {
    if (FchDataPtr->Sata[SataController].SataEnable) {
      SataEnable2 |= BIT0 << (SataController + 4);
    }
  }
  PcdSet8S (PcdSataEnable2, SataEnable2);

  //
  // Install gFchMultiFchInitProtocolGuid to signal Platform
  //
  Handle = ImageHandle;
  Status = gBS->InstallProtocolInterface (
                  &Handle,
                  &gFchMultiFchInitProtocolGuid,
                  EFI_NATIVE_INTERFACE,
                  &FchMfPrivate->FchMultiFchInit
                  );
  ASSERT_EFI_ERROR (Status);
  //
  // Register the event handling function for FchInitLate to be launched after
  // Ready to Boot
  //
  Status = EfiCreateEventReadyToBootEx (
             TPL_CALLBACK,
             MultiFchInitRtb,
             NULL,
             &FchMfPrivate->EventReadyToBoot
             );
  ASSERT_EFI_ERROR (Status);

  Status = gBS->FreePool (FchDataPtr);
  ASSERT_EFI_ERROR (Status);

  AGESA_TESTPOINT (TpFchMultiFchDxeExit, NULL);
  return (Status);
}

VOID
FchKLSecondaryFchInitSataDxe (
  IN  UINT8       Die,
  IN  UINT32      DieBusNum,
  IN  VOID        *FchDataPtr
  )
{
  UINT8                     SataController;
  FCH_DATA_BLOCK            *LocalCfgPtr;

  LocalCfgPtr = (FCH_DATA_BLOCK *)FchDataPtr;

  IDS_HDT_CONSOLE (FCH_TRACE, "[FchKLSecondaryFchInitSataDxe] SATA Pre-PCI Init On IODie %d...Start.\n", Die);
  for (SataController = 0; SataController < KUNLUN_SATA_CONTROLLER_NUM; SataController++) {
    //
    // Check if Sata is enabled by NBIO
    //
    //if (!FchKLSataInitCheckSataPci (DieBusNum32, SataController, FchDataPtr)) {
    //  LocalCfgPtr->Sata[SataController].SataEnable = FALSE;
    //}

    if (LocalCfgPtr->Sata[SataController].SataEnable) {
      IDS_HDT_CONSOLE (FCH_TRACE, "[FchKLSecondaryFchInitSataDxe] SATA Controller%d On IODie %d is enabled.\n", SataController, Die);
      SataEnableWriteAccessKL (DieBusNum, SataController);
      FchKLInitEnvProgramSata (DieBusNum, SataController, FchDataPtr);

      /*
      Because of security consideration, x86 is forbidden to access nBIF straps.
      Move code to ABL.

      //
      // Call Sub-function for each Sata mode
      //
      if (( LocalCfgPtr->Sata[SataController].SataClass == SataAhci7804) || (LocalCfgPtr->Sata[SataController].SataClass == SataAhci )) {
        FchInitEnvSataAhciKL ( DieBusNum, SataController, FchDataPtr );
      }

      if ( LocalCfgPtr->Sata[SataController].SataClass == SataRaid) {
        FchInitEnvSataRaidKL ( DieBusNum, SataController, FchDataPtr );
      }
      */

      SataDisableWriteAccessKL (DieBusNum, SataController);
      //FchKLSataAutoShutdownController (DieBusNum, SataController, FchDataPtr);
    } else {
      IDS_HDT_CONSOLE (FCH_TRACE, "[FchKLSecondaryFchInitSataDxe] SATA Controller%d On IODie %d is disabled.\n", SataController, Die);
    }
  }

  // check if Sata0 and Sata1 are both disabled
  if ((!LocalCfgPtr->Sata[0].SataEnable) && (!LocalCfgPtr->Sata[1].SataEnable)) {
    FchKLSataInitHideNbifDev1Pci (DieBusNum, 0, FchDataPtr);
  }
  // check if Sata2 and Sata3 are both disabled
  if ((!LocalCfgPtr->Sata[2].SataEnable) && (!LocalCfgPtr->Sata[3].SataEnable)) {
    FchKLSataInitHideNbifDev1Pci (DieBusNum, 3, FchDataPtr);
  }

  IDS_HDT_CONSOLE (FCH_TRACE, "[FchKLSecondaryFchInitSataDxe] SATA Pre-PCI Init On IODie %d...Complete.\n", Die);
}


VOID
EFIAPI
MultiFchInitRtb (
  IN       EFI_EVENT        Event,
  IN       VOID             *Context
  )
{
  UINT32              FchBusNum;
  UINT8               SataController;
  UINT8               SataEnable2;
  UINT8               SataSgpioMultiDieEnable;
  UINT64              SataMultiDiePortShutDown;
  UINT64              SataMultiDiePortESP;
  UINT64              SataMultiDieDevSlp;
  UINT64              SataPortMode;
  FCH_INIT_PROTOCOL   *FchInit;
  FCH_DATA_BLOCK      *FchDataPtr;
  EFI_STATUS          Status;
  FCH_MULTI_FCH_INIT_PROTOCOL  *FchMfInit;
  UINT8               DevSlp0ControllerNum;
  UINT8               DevSlp1ControllerNum;

  //
  // Locate Fch INIT Protocol
  //
  Status = gBS->LocateProtocol (
                  &gFchInitProtocolGuid,
                  NULL,
                  (VOID **)&FchInit
                  );
  ASSERT_EFI_ERROR (Status);

  Status = gBS->LocateProtocol (
                  &gFchMultiFchInitProtocolGuid,
                  NULL,
                  (VOID **)&FchMfInit
                  );
  ASSERT_EFI_ERROR (Status);

  // Allocate memory for the local data
  Status = gBS->AllocatePool (
                  EfiBootServicesData,
                  sizeof (FCH_DATA_BLOCK),
                  (VOID **)&FchDataPtr
                  );
  ASSERT_EFI_ERROR (Status);

  gBS->CopyMem (
         FchDataPtr,
         FchInit->FchPolicy,
         sizeof (FCH_DATA_BLOCK)
         );

  //
  // Update local Data Structure
  //
  SataEnable2              = PcdGet8 (PcdSataEnable2) >> 4;
  SataMultiDiePortShutDown = PcdGet64 (PcdSataMultiDiePortShutDown) >> 32;
  SataMultiDiePortESP      = PcdGet64 (PcdSataMultiDiePortESP) >> 32;
  SataMultiDieDevSlp       = PcdGet64 (PcdSataMultiDieDevSlp);
  SataSgpioMultiDieEnable  = PcdGet8 (PcdSataSgpioMultiDieEnable) >> 4;
  SataPortMode             = PcdGet64 (PcdSataIoDie1PortMode);

  for (SataController = 0; SataController < KUNLUN_SATA_CONTROLLER_NUM; SataController++) {
    FchDataPtr->Sata[SataController].SataEnable       = (SataEnable2 >> SataController) & BIT0;
    FchDataPtr->Sata[SataController].SataEspPort      = (UINT8)(SataMultiDiePortESP >> (8 * SataController));
    FchDataPtr->Sata[SataController].SataPortPower    = (UINT8)(SataMultiDiePortShutDown >> (8 * SataController));
    FchDataPtr->Sata[SataController].SataPortMd       = (UINT16)(SataPortMode >> (16 * SataController));
    FchDataPtr->Sata[SataController].SataSgpio0       = (UINT8)((SataSgpioMultiDieEnable >> SataController) & BIT0);
    FchDataPtr->Sata[SataController].SataUBMDiagMode  = PcdGetBool (PcdSataUBMDiagMode);
    FchDataPtr->Sata[SataController].SataDevSlpPort0  = FALSE;    // Reset DevSlp0
    FchDataPtr->Sata[SataController].SataDevSlpPort1  = FALSE;    // Reset DevSlp1
  }

  if ((SataMultiDieDevSlp & BIT0) == BIT0) {
    DevSlp0ControllerNum = ((SataMultiDieDevSlp & 0xF0) >> 4) - 4; // Note: Second socket controller number starts from 4
    FchDataPtr->Sata[DevSlp0ControllerNum].SataDevSlpPort0 = TRUE;
    FchDataPtr->Sata[DevSlp0ControllerNum].SataDevSlpPort0Num = (SataMultiDieDevSlp & 0x0F) >> 1;
  }

  if ((SataMultiDieDevSlp & BIT8) == BIT8) {
    DevSlp1ControllerNum = ((SataMultiDieDevSlp & 0xF000) >> 12) - 4; // Note: Second socket controller number starts from 4
    FchDataPtr->Sata[DevSlp1ControllerNum].SataDevSlpPort1 = TRUE;
    FchDataPtr->Sata[DevSlp1ControllerNum].SataDevSlpPort1Num = (SataMultiDieDevSlp & 0xF00) >> 9;
  }

  FchDataPtr->HwAcpi.FchAcpiMmioBase               = (UINT32) FchMfInit->FchMfData.FchAcpiMmioBase[1];
  ASSERT (FchDataPtr->HwAcpi.FchAcpiMmioBase!=NULL);

  FchBusNum = (UINT32) FchDxeGetSocket1Bus ();
  ASSERT (FchBusNum!=0);

  //Secondary Fch init

  //Telemetry
  FchSecondarySendStartTelemetryMsg(FchBusNum, FchDataPtr);

  //USB

  //SATA
  FchKLSecondaryFchInitSataRtb (1, FchBusNum, FchDataPtr);

  Status = gBS->FreePool (FchDataPtr);
  ASSERT_EFI_ERROR (Status);

  gBS->CloseEvent (Event);
}

VOID
FchKLSecondaryFchInitSataRtb (
  IN  UINT8       Die,
  IN  UINT32      DieBusNum,
  IN  VOID        *FchDataPtr
  )
{
  UINT8                     SataController;
  FCH_DATA_BLOCK            *LocalCfgPtr;

  LocalCfgPtr = (FCH_DATA_BLOCK *)FchDataPtr;

  IDS_HDT_CONSOLE (FCH_TRACE, "[FchKLSecondaryFchInitSataRtb] SATA RTB init On IODie %d...Start.\n", Die);
  for (SataController = 0; SataController < KUNLUN_SATA_CONTROLLER_NUM; SataController++) {
    if (LocalCfgPtr->Sata[SataController].SataEnable) {
      IDS_HDT_CONSOLE (FCH_TRACE, "[FchKLSecondaryFchInitSataRtb] SATA Controller%d On IODie %d is enabled.\n", SataController, Die);
      SataEnableWriteAccessKL (DieBusNum, SataController);
      //
      // Call Sub-function for each Sata mode
      //
      if (( LocalCfgPtr->Sata[SataController].SataClass == SataAhci7804) || (LocalCfgPtr->Sata[SataController].SataClass == SataAhci )) {
        FchInitLateSataAhciKL ( DieBusNum, SataController, FchDataPtr );
      }

      if ( LocalCfgPtr->Sata[SataController].SataClass == SataRaid) {
        FchInitLateSataRaidKL ( DieBusNum, SataController, FchDataPtr );
      }

      FchKLInitLateProgramSataRegs (DieBusNum, SataController, FchDataPtr);
      SataDisableWriteAccessKL (DieBusNum, SataController);
    }
  }
  IDS_HDT_CONSOLE (FCH_TRACE, "[FchKLSecondaryFchInitSataRtb] SATA RTB init On IODie %d...Complete.\n", Die);
}


VOID
FchSecondarySendStartTelemetryMsg(
  IN  UINT32      DieBusNum,
  IN  VOID        *FchDataPtr
  )
{
  UINT32              SmuArg[6];
  UINT32              SmuStatus;
  PCI_ADDR            NbioPciAddress;
  FCH_DATA_BLOCK      *LocalCfgPtr;

  LocalCfgPtr = (FCH_DATA_BLOCK *)FchDataPtr;

  IDS_HDT_CONSOLE (FCH_TRACE, "[FchSecondarySendStartTelemetryMsg] start.\n");

  if (!(LocalCfgPtr->HwAcpi.SpdHostCtrlRelease)) {

    IDS_HDT_CONSOLE (FCH_TRACE, "[FchSecondarySendStartTelemetryMsg] Do not release SPD host controller to BMC.\n");
    if (LocalCfgPtr->HwAcpi.DimmTelemetry) {
      IDS_HDT_CONSOLE (
        FCH_TRACE,
        "PcdRTDeviceEnableMap=0x%x, PcdRTDeviceEnableMapEx=0x%x\n",
        LocalCfgPtr->FchRunTime.FchDeviceEnableMap,
        LocalCfgPtr->FchRunTime.FchDeviceEnableMapEx
        );
      // Send message to PMFW
      NbioSmuServiceCommonInitArguments (SmuArg);

      if (LocalCfgPtr->FchRunTime.FchDeviceEnableMap & BIT21) {
        SmuArg[0] = 1;  // Use I3C controller
      } else if (LocalCfgPtr->FchRunTime.FchDeviceEnableMap & BIT5) {
        SmuArg[0] = 0;  // Use I2C controller
      }

      NbioPciAddress.AddressValue = MAKE_SBDFO (DF_GET_SEGMENT(DieBusNum), DF_GET_BUS(DieBusNum), 0, 0, 0);
      SmuStatus = NbioSmuServiceRequest (
                    NbioPciAddress,
                    BIOSSMC_MSG_StartDimmTelemetryReading,                                //SmuMsg,
                    SmuArg,
                    0
                    );
      if (SmuStatus) {
        IDS_HDT_CONSOLE (FCH_TRACE, "Success:Send message to socket 1 SMU for PMFW poll DIMM telemetry.\n");
      } else {
        IDS_HDT_CONSOLE (FCH_TRACE, "Failure:Send message to socket 1 SMU for PMFW poll DIMM telemetry.\n");
      }

    } else {
      IDS_HDT_CONSOLE (FCH_TRACE, "Disabled:PMFW poll DDR telemetry is disabled.\n");
    }
  } else {
    IDS_HDT_CONSOLE (FCH_TRACE, "gFchSpdControlOwnership = 0x%x\n", gFchSpdControlOwnership);

    if ( gFchSpdControlOwnership == ApcbFchSpdCtrlOwnershipSeparate ) {
      IDS_HDT_CONSOLE (FCH_TRACE, "Release P1 SPD_HOST_CTRL_L to BMC.\n");
      FchSmnRead (DieBusNum, FCH_KL_SMN_GPIO_BASE + FCH_REG0C, &SmuArg[0], NULL);
      IDS_HDT_CONSOLE (FCH_TRACE, "Org FCH::GPIO::AGPIO3_SPD_HOST_CTRL_L 0x%x\n", SmuArg[0]);
      FchSmnRW (
        DieBusNum,
        FCH_KL_SMN_GPIO_BASE + FCH_REG0C,
        (UINT32)(~(BIT23 + BIT22)),
        (UINT32)(BIT23 + BIT22),          // Output enable & high
        NULL
        );
      FchSmnRead (DieBusNum, FCH_KL_SMN_GPIO_BASE + FCH_REG0C, &SmuArg[0], NULL);
      IDS_HDT_CONSOLE (FCH_TRACE, "Mod FCH::GPIO::AGPIO3_SPD_HOST_CTRL_L 0x%x\n", SmuArg[0]);
    }
  }
}



