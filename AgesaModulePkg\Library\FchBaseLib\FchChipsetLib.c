/*
*****************************************************************************
*
 * Copyright (C) 2008-2024 Advanced Micro Devices, Inc. All rights reserved.
 *
*******************************************************************************
*/

/*----------------------------------------------------------------------------------------
 *                             M O D U L E S    U S E D
 *----------------------------------------------------------------------------------------
 */
#include <Filecode.h>
#include <Library/AmdBaseLib.h>
#include <Library/FchBaseLib.h>
#include "FchRegistersCommon.h"
#include "FchDebug.h"

/*----------------------------------------------------------------------------------------
 *                   D E F I N I T I O N S    A N D    M A C R O S
 *----------------------------------------------------------------------------------------
 */
#define FILECODE LIBRARY_FCHBASELIB_FCHCHIPSETLIB_FILECODE
#define FCHOEM_OUTPUT_80_PORT               0x80
#define FCHOEM_OUTPUT_1080_PORT             0x1080
#define USB_PORT_DETECTION_MAX_TRY          100
#define FCH_MAX_SCIMAP_NUM                  64
/*----------------------------------------------------------------------------------------
 *                  T Y P E D E F S     A N D     S T R U C T U R E S
 *----------------------------------------------------------------------------------------
 */
typedef struct _ACPI_REG_WRITE {
  UINT8        MmioBase;               /// MmioBase: Index of Fch block (For instance GPIO_BASE:0x01 SMI_BASE:0x02)
  UINT8        MmioReg;                /// MmioReg      : Register index
  UINT8        DataAndMask;            /// DataANDMask  : AND Register Data
  UINT8        DataOrMask;             /// DataOrMask   : Or Register Data
} ACPI_REG_WRITE;

static ACPI_REG_WRITE FchInitResetRtcextTable[] =
{
  {00, 00, 0xB0, 0xAC},
  {PMIO_BASE >> 8,  FCH_PMIOA_REG5E, 0x00, 0x00},
  {PMIO_BASE >> 8,  FCH_PMIOA_REG5F, 0x00, 0x00},
  {PMIO_BASE >> 8,  FCH_PMIOA_REG5E, 0x00, 0x01},
  {PMIO_BASE >> 8,  FCH_PMIOA_REG5F, 0x00, 0x00},
  {PMIO_BASE >> 8,  FCH_PMIOA_REG5E, 0x00, 0x02},
  {PMIO_BASE >> 8,  FCH_PMIOA_REG5F, 0x00, 0x00},
  {PMIO_BASE >> 8,  FCH_PMIOA_REG5E, 0x00, 0x03},
  {PMIO_BASE >> 8,  FCH_PMIOA_REG5F, 0x00, 0x00},
  {PMIO_BASE >> 8,  FCH_PMIOA_REG5E, 0x00, 0x04},
  {PMIO_BASE >> 8,  FCH_PMIOA_REG5F, 0x00, 0x00},
  {PMIO_BASE >> 8,  FCH_PMIOA_REG5E, 0x00, 0x10},
  {PMIO_BASE >> 8,  FCH_PMIOA_REG5F, 0x00, 0x00},
  {PMIO_BASE >> 8,  FCH_PMIOA_REG5E, 0x00, 0x11},
  {PMIO_BASE >> 8,  FCH_PMIOA_REG5F, 0x00, 0x00},
  {PMIO_BASE >> 8,  FCH_PMIOA_REG5E, 0x00, 0x12},
  {PMIO_BASE >> 8,  FCH_PMIOA_REG5F, 0x00, 0x00},
  {PMIO_BASE >> 8,  FCH_PMIOA_REG5E, 0x00, 0x13},
  {PMIO_BASE >> 8,  FCH_PMIOA_REG5F, 0x00, 0x00},
  {PMIO_BASE >> 8,  FCH_PMIOA_REG5E, 0x00, 0x14},
  {PMIO_BASE >> 8,  FCH_PMIOA_REG5F, 0x00, 0x00},
  {0xFF, 0xFF, 0xFF, 0xFF},
};

/*----------------------------------------------------------------------------------------
 *           P R O T O T Y P E S     O F     L O C A L     F U N C T I O N S
 *----------------------------------------------------------------------------------------
 */

/*----------------------------------------------------------------------------------------*/
/**
 * ProgramFchAcpiMmioTbl - Program FCH ACPI MMIO register by table (8 bits data)
 *
 *
 * @param[in] pAcpiTbl   - Table data pointer
 * @param[in] StdHeader
 *
 */
static VOID
ProgramFchAcpiMmioTbl (
  IN       ACPI_REG_WRITE      *pAcpiTbl,
  IN       AMD_CONFIG_PARAMS   *StdHeader
  )
{
  UINT8   i;
  UINT8   Or8;
  UINT8   Mask8;
  UINT32  ddtempVar;

  if (pAcpiTbl != NULL) {
    if ((pAcpiTbl->MmioReg == 0)
      && (pAcpiTbl->MmioBase == 0)
      && (pAcpiTbl->DataAndMask == 0xB0)
      && (pAcpiTbl->DataOrMask == 0xAC))
    {
      // Signature Checking
      pAcpiTbl++;
      for ( i = 1; pAcpiTbl->MmioBase < 0x1D; i++ ) {
        ddtempVar = ACPI_MMIO_BASE | (pAcpiTbl->MmioBase) << 8 | pAcpiTbl->MmioReg;
        Or8 = pAcpiTbl->DataOrMask;
        Mask8 = ~pAcpiTbl->DataAndMask;
        LibAmdMemRMW (AccessWidth8, (UINT64) ddtempVar, &Or8, &Mask8, StdHeader);
        pAcpiTbl++;
      }
    }
  }
}

/*----------------------------------------------------------------------------------------*/
/**
 * @brief  FchRtcextValid - Check RTCEXT register to seee if any Field is invalid.
 *
 *
 */
BOOLEAN
FchRtcextValid (
  VOID
  )
{
  UINT8 RtcExtData;
  UINT8 Or8;
  UINT8 Mask8 = 0xFF;
  UINT32 checkField;

  Or8 = 0x1; // SprFwdCtrl
  LibAmdMemRMW (AccessWidth8, (UINT64)(ACPI_MMIO_BASE + PMIO_BASE + FCH_PMIOA_REG5E), &Or8, &Mask8, NULL);
  LibAmdMemRead (AccessWidth8, (UINT64)(ACPI_MMIO_BASE + PMIO_BASE + FCH_PMIOA_REG5F), &RtcExtData, NULL);
  // check if RTCext data lost
  if (RtcExtData == 0xFF) {
    IDS_HDT_CONSOLE (CPU_TRACE, "FchRtcextValid:  RTCext data lost\n");
    return FALSE;
  }
  // check if SprFwdHour is valid, SprFwdCtrl][5:0] should be 0,1
  checkField = RtcExtData & 0x3F;
  if (checkField > 0x1) {
    IDS_HDT_CONSOLE (CPU_TRACE, "FchRtcextValid:  SprFwdHour is invalid, 0x%x\n", checkField);
    return FALSE;
  }

  Or8 = 0x2; // SprFwdMonth
  LibAmdMemRMW (AccessWidth8, (UINT64)(ACPI_MMIO_BASE + PMIO_BASE + FCH_PMIOA_REG5E), &Or8, &Mask8, NULL);
  LibAmdMemRead (AccessWidth8, (UINT64)(ACPI_MMIO_BASE + PMIO_BASE + FCH_PMIOA_REG5F), &RtcExtData, NULL);
  // check if SprFwdMonth is valid, SprFwdMonth][4:0] should be 0,1
  checkField = RtcExtData & 0x1F;
  if (checkField > 0x1) {
    IDS_HDT_CONSOLE (CPU_TRACE, "FchRtcextValid:  SprFwdMonth is invalid, 0x%x\n", checkField);
    return FALSE;
  }

  Or8 = 0x3; // FallBackHour
  LibAmdMemRMW (AccessWidth8, (UINT64)(ACPI_MMIO_BASE + PMIO_BASE + FCH_PMIOA_REG5E), &Or8, &Mask8, NULL);
  LibAmdMemRead (AccessWidth8, (UINT64)(ACPI_MMIO_BASE + PMIO_BASE + FCH_PMIOA_REG5F), &RtcExtData, NULL);
  // check if FallBackHour is valid, FallBackHour[5:0] should be 00h ~ 09h,10h~19h,20~24h
  checkField = RtcExtData & 0x3F;
  if ( (checkField > 0x9 && checkField < 0x10)
    || (checkField > 0x19 && checkField < 0x20)
    || (checkField > 0x24))
  {
    IDS_HDT_CONSOLE (CPU_TRACE, "FchRtcextValid:  FallBackHour is invalid, 0x%x\n", checkField);
    return FALSE;
  }

  Or8 = 0x4; // FallBackMonth
  LibAmdMemRMW (AccessWidth8, (UINT64)(ACPI_MMIO_BASE + PMIO_BASE + FCH_PMIOA_REG5E), &Or8, &Mask8, NULL);
  LibAmdMemRead (AccessWidth8, (UINT64)(ACPI_MMIO_BASE + PMIO_BASE + FCH_PMIOA_REG5F), &RtcExtData, NULL);
  // check if FallBackMonth is valid, FallBackMonth][4:0] should be 00h ~ 09h,10h~12h
  checkField = RtcExtData & 0x1F;
  if ( (checkField > 0x9 && checkField < 0x10)
    || (checkField > 0x12))
  {
    IDS_HDT_CONSOLE (CPU_TRACE, "FchRtcextValid:  FallBackMonth is invalid, 0x%x\n", checkField);
    return FALSE;
  }

  Or8 = 0x10; // WeekTimerControl
  LibAmdMemRMW (AccessWidth8, (UINT64)(ACPI_MMIO_BASE + PMIO_BASE + FCH_PMIOA_REG5E), &Or8, &Mask8, NULL);
  LibAmdMemRead (AccessWidth8, (UINT64)(ACPI_MMIO_BASE + PMIO_BASE + FCH_PMIOA_REG5F), &RtcExtData, NULL);
  // check if WeekTimerControl is valid, WeekTimerControl][2:1] should be 00h ~ 02h
  checkField = (RtcExtData & 0x6) >> 1;
  if (checkField > 0x2) {
    IDS_HDT_CONSOLE (CPU_TRACE, "FchRtcextValid:  WeekTimerControl is invalid, 0x%x\n", checkField);
    return FALSE;
  }

  return TRUE;
}

/*----------------------------------------------------------------------------------------
 *                          E X P O R T E D    F U N C T I O N S
 *----------------------------------------------------------------------------------------
 */
/*----------------------------------------------------------------------------------------*/
/**
 * @brief  FchGetChipStrap - Get Chip Strap status
 *
 *
 * @param[in] Value - Return Chip strap status
 * @param[in] StdHeader
 *
 */
VOID
FchGetChipStrap (
  IN       VOID                *Value,
  IN       AMD_CONFIG_PARAMS   *StdHeader
  )
{
  LibAmdMemRead (AccessWidth8, (UINT64) (ACPI_MMIO_BASE + MISC_BASE + FCH_MISC_REG80), Value, StdHeader);
}

/*----------------------------------------------------------------------------------------*/
/**
 * @brief  FchGetEfuseStatus - Get Efuse status
 *
 *
 * @param[in] Value - Return Efuse Status
 * @param[in] StdHeader
 *
 */
VOID
FchGetEfuseStatus (
  IN       VOID                *Value,
  IN       AMD_CONFIG_PARAMS   *StdHeader
  )
{
  UINT8    Or8;
  UINT8    Mask8;

  Or8 = BIT5;
  Mask8 = BIT5;
  LibAmdMemRMW (AccessWidth8, (UINT64) (ACPI_MMIO_BASE + PMIO_BASE + FCH_PMIOA_REGC8), &Or8, &Mask8, StdHeader);
  LibAmdMemWrite (AccessWidth8, (UINT64) (ACPI_MMIO_BASE + PMIO_BASE + FCH_PMIOA_REGD8), Value, StdHeader);
  LibAmdMemRead (AccessWidth8, (UINT64) (ACPI_MMIO_BASE + PMIO_BASE + FCH_PMIOA_REGD8 + 1), Value, StdHeader);
  Or8 = 0;
  Mask8 = BIT5;
  LibAmdMemRMW (AccessWidth8, (UINT64) (ACPI_MMIO_BASE + PMIO_BASE + FCH_PMIOA_REGC8), &Or8, &Mask8, StdHeader);
}

/*----------------------------------------------------------------------------------------*/
/**
 * @brief  FchAcLossControl - control the action of AC power loss
 *
 *
 * @param[in] AcLossControlValue - the value to control AC power loss
 * @param[in] StdHeader - Standard configuration header
 *
 *
 */
VOID
FchAcLossControl (
  IN       UINT8 AcLossControlValue,
  IN       AMD_CONFIG_PARAMS   *StdHeader
  )
{
  UINT8    Mask8;
  //[Replaced] RwMem (ACPI_MMIO_BASE + PMIO_BASE + FCH_PMIOA_REG5B, AccessWidth8, 0xF0, AcLossControlValue);
  Mask8 = 0x0F;
  AcLossControlValue &= 0x03;
  AcLossControlValue |= BIT2;
  LibAmdMemRMW (
    AccessWidth8,
    (UINT64) (ACPI_MMIO_BASE + PMIO_BASE + FCH_PMIOA_REG5B),
    &AcLossControlValue,
    &Mask8,
    StdHeader
    );
}

/*----------------------------------------------------------------------------------------*/
/**
 * @brief  OutPort80 - output post code to Port 80
 *
 *
 * @param[in] pcode - post code
 * @param[in] StdHeader - Standard configuration header
 *
 */
VOID
FchOutPort80 (
  IN       UINT32              pcode,
  IN       AMD_CONFIG_PARAMS   *StdHeader
  )
{
  LibAmdIoWrite (AccessWidth8, FCHOEM_OUTPUT_80_PORT, &pcode, StdHeader);
  return;
}

/*----------------------------------------------------------------------------------------*/
/**
 * @brief  OutPort1080 - output post code to Port 1080
 *
 *
 * @param[in] pcode - post code
 * @param[in] StdHeader - Standard configuration header
 *
 */
VOID
FchOutPort1080 (
  IN       UINT32              pcode,
  IN       AMD_CONFIG_PARAMS   *StdHeader
  )
{
  LibAmdIoWrite (AccessWidth32, FCHOEM_OUTPUT_1080_PORT, &pcode, StdHeader);
  return;
}

/*----------------------------------------------------------------------------------------*/
/**
 * @brief  FchResetRtcExt - Reset RTCEXT if any invalid field detected.
 *
 *
 */
VOID
FchResetRtcExt(
  VOID
  )
{
  if (!FchRtcextValid ()) {
    IDS_HDT_CONSOLE(CPU_TRACE, "FchResetRtcExt: program RTCEXT table\n");
    ProgramFchAcpiMmioTbl((ACPI_REG_WRITE *)(&FchInitResetRtcextTable[0]), NULL);
  }
  else {
    IDS_HDT_CONSOLE(CPU_TRACE, "FchResetRtcExt: RTCEXT is valid, no need to program\n");
  }
}

/*----------------------------------------------------------------------------------------*/
/**
 * @brief  FchResetUnusedSciMap - Reset unused SciMap to the the latest unused event.
 *
 *
 */
VOID
FchResetUnusedSciMap (
  VOID
  )
{
  UINT8                  SciMap, Event, UnusedEvent;
  UINT32                 UnusedEventBitFlag;
  UINT64                 UnusedSciMapBitFlag;

  // Set unused event bit flag to UnusedEventBitFlag
  // Set unused SciMap bit flag to UnusedSciMapBitFlag
  UnusedEventBitFlag = 0xFFFFFFFF;
  UnusedSciMapBitFlag = 0;

  IDS_FCH_FUNCTION_ENTER ();

  for (SciMap = 0; SciMap < FCH_MAX_SCIMAP_NUM; SciMap++) {
    Event = ACPIMMIO8 (ACPI_MMIO_BASE + SMI_BASE + FCH_SMI_Gevent0 + SciMap) & 0x1F;
    if (Event != 0) {
      // Clear unused event bit flag when Event is not equal to 0
      UnusedEventBitFlag &= ~((UINT32) 1 << Event);
    } else if (SciMap != 0) {
      // Set unused SciMap bit flag when Event is equal to 0
      UnusedSciMapBitFlag |= (UINT64) LShiftU64 (1, SciMap);
    } else {
      // Clear unused event bit 0 flag when both of SciMap and Event are equal to 0
      UnusedEventBitFlag &= ~(UINT32) BIT0;
    }
  }
  IDS_FCH_INFO ("UnusedEventBitFlag  = 0x%08X\n", UnusedEventBitFlag);
  IDS_FCH_INFO ("UnusedSciMapBitFlag = 0x%016LX\n", UnusedSciMapBitFlag);

  // Find the latest unused event with BitScanReverse
  for (UnusedEvent = 32; UnusedEvent > 0; UnusedEvent--) {
    if (UnusedEventBitFlag & ((UINT32) 1 << (UnusedEvent - 1))) {
      break;
    }
  }
  if (UnusedEvent != 0) {
    UnusedEvent--;
  }
  IDS_FCH_INFO ("UnusedEvent         = 0x%02X\n", UnusedEvent);

  // Program the latest unused event to all of unused SciMap,
  // when the latest unused event is not equal to 0
  if (UnusedEvent != 0) {
    for (SciMap = 0; SciMap < FCH_MAX_SCIMAP_NUM; SciMap++) {
      if (UnusedSciMapBitFlag & ((UINT64) LShiftU64 (1, SciMap))) {
        ACPIMMIO8 (ACPI_MMIO_BASE + SMI_BASE + FCH_SMI_Gevent0 + SciMap) = UnusedEvent;
      }
    }
  }

  IDS_FCH_FUNCTION_EXIT ();
}

/**
 * FchUsbPortPowerDown  -  Fch Usb Port Power Down
 * Input
 *   PortscAddress : PortSC SMN Address
 *
 *
 */
VOID
FchUsbPortPowerDown (
  IN  UINT32   PortscAddress
  )
{
  UINT32                      PortSc;
  UINT32                      Retry = 0;

  IDS_HDT_CONSOLE (FCH_TRACE, "[FCH]FchUsbPortPowerDown starts on %x\n", PortscAddress);

  // 1. Disable the port from portsc by setting PED bit of portsc to 1b.
  FchSmnRW (0, PortscAddress, ~ (UINT32) (BIT1), BIT1, NULL);  // PED

  // 2. Make sure the port is disabled from portsc (PED is 0b)
  FchSmnRead (0, PortscAddress, &PortSc, NULL);
  Retry = 0;
  while ((PortSc & BIT1) && (Retry < USB_PORT_DETECTION_MAX_TRY)){
    FchSmnRead (0, PortscAddress, &PortSc, NULL);
    FchStall(10, NULL);
    Retry++;
  }
  if (Retry == USB_PORT_DETECTION_MAX_TRY) {
    IDS_HDT_CONSOLE (FCH_TRACE, "[FCH]FchUsbPortPowerDown time-out when clearing PED on %x\n", PortscAddress);
  }

  // 3. Clear PP bit (bit 9 port power) of the portsc.
  FchSmnRW (0, PortscAddress, ~ (UINT32) (BIT9), 0, NULL);  // PP
  FchSmnRead (0, PortscAddress, &PortSc, NULL);
  Retry = 0;
  while ((PortSc & BIT9) && (Retry < USB_PORT_DETECTION_MAX_TRY)) {
    FchSmnRead (0, PortscAddress, &PortSc, NULL);
    FchStall(10, NULL);
    Retry++;
  }
  if (Retry == USB_PORT_DETECTION_MAX_TRY) {
    IDS_HDT_CONSOLE (FCH_TRACE, "[FCH]FchUsbPortPowerDown time-out when clearing PP on %x\n", PortscAddress);
  }
}

