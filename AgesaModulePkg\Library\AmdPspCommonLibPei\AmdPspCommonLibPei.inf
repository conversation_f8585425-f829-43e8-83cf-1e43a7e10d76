#;*****************************************************************************
#;
#; Copyright (C) 2008-2024 Advanced Micro Devices, Inc. All rights reserved.
#;
#;******************************************************************************

[Defines]
  INF_VERSION                    = 0x00010006
  BASE_NAME                      = AmdPspCommonLib
  FILE_GUID                      = 891598E7-AAD3-410A-A993-3E48166141A0
  MODULE_TYPE                    = BASE
  VERSION_STRING                 = 1.0
  LIBRARY_CLASS                  = AmdPspCommonLib|PEIM

[Sources.common]
  AmdPspCommonLibPei.c

[Packages]
  MdePkg/MdePkg.dec
  AgesaPkg/AgesaPkg.dec
  AgesaModulePkg/AgesaCommonModulePkg.dec
  AgesaModulePkg/AgesaModulePspPkg.dec

[LibraryClasses]
  AmdBaseLib
  IdsLib
  PeiServicesTablePointerLib

[Guids]

[Protocols]

[Ppis]
  gAmdPspCommonServicePpiGuid #Consume

[Pcd]

[Depex]
  gAmdPspCommonServicePpiGuid




