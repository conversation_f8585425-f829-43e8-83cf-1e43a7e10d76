/** @file

Cxl Error Log Driver.

**/
/******************************************************************************
 * Copyright (C) 2024 Advanced Micro Devices, Inc. All rights reserved.
 *
 ***************************************************************************/

#include <Filecode.h>
#include <AMD.h>
#include <Porting.h>
#include <Library/IdsLib.h>
#include <Library/BaseMemoryLib.h>
#include <Library/MemoryAllocationLib.h>

#include <Library/AmdErrorLogLib.h>
#include <Protocol/AmdErrorLogProtocol.h>
#include <Protocol/AmdErrorLogServiceProtocol.h>
#include <Protocol/AmdCxlErrorLogProtocol.h>
#include "AmdCxlErrorLogDxe.h"



/*----------------------------------------------------------------------------------------
 *                   D E F I N I T I O N S    A N D    M A C R O S
 *----------------------------------------------------------------------------------------
 */
#define FILECODE ERRORLOG_AMDCXLERRORLOGDXE_AMDCXLERRORLOGDXE_FILECODE

/*----------------------------------------------------------------------------------------
 *           P R O T O C O L   I N S T A N T I A T I O N
 *----------------------------------------------------------------------------------------
 */

// Produced Protocols
AMD_CXL_ERROR_LOG_PROTOCOL  mAmdCxlErrorLogProtocol = {
  AMD_CXL_ERROR_LOG_PROTOCOL_REVISION,
  CxlAddErrorLog,
  CxlAcquireErrorLog,
  CxlDisplayErrorLog,
  CxlDisplayErrorLogMessages,
};
/*----------------------------------------------------------------------------------------
 *                  T Y P E D E F S     A N D     S T R U C T U R E S
 *----------------------------------------------------------------------------------------
 */

STATIC DXE_AMD_ERROR_LOG_PROTOCOL            *mAmdErrorLog = NULL; 
STATIC DXE_AMD_ERROR_LOG_SERVICES_PROTOCOL   *mAmdErrorLogService = NULL;

typedef struct {
  UINT32    ErrorCode;
  CHAR16    *String;
} CXL_ERRORCODE_STRING;

// CXL Error String
STATIC CXL_ERRORCODE_STRING CxlErrorMessages [] = {
  // ABL CXL Errors
  {0x434C0201, L"Incorrect CXL Mixed Interleave Configuration on Socket 0x%x.\n"},
  {0x434C0202, L"CXL Mixed Interleave truncated memory size on Socket 0x%x.\n"},

  // CXL Early Initialization Errors
  {0x434C0501, L"CXL Device Segment 0x%x Bus 0x%x DVSEC Capability Not Found.\n"},
  {0x434C0502, L"Assign CXL 1.1 Device Root Bridge Segment 0x%x Bus 0x%x MMIO address space error.\n"},

  // CXL DOE Errors
  {0x434C0601, L"CXL Device Segment 0x%x Bus 0x%x DOE Response Ready Timeout.\n"},
  {0x434C0602, L"CXL Device Segment 0x%x Bus 0x%x DOE Mailbox Busy.\n"},
  {0x434C0603, L"CXL Device Segment 0x%x Bus 0x%x DOE Extended Capability Not Supported.\n"},

  // CXL CDAT Errors
  {0x434C0701, L"CDAT Supported Not Enabled.\n"},
  {0x434C0702, L"CXL Device Segment 0x%x Bus 0x%x Unsupported CDAT Type.\n"},
  {0x434C0703, L"CXL Device Segment 0x%x Bus 0x%x Unsupported CDAT Entry.\n"},
  {0x434C0704, L"CXL Device Segment 0x%x Bus 0x%x CDAT Entry Type Not Found.\n"},
  {0x434C0705, L"CXL Device Segment 0x%x Bus 0x%x No CDAT Data.\n"},

  // CXL Mailbox Errors
  {0x434C0801, L"CXL Device Segment 0x%x Bus 0x%x Primary Mailbox Not Found.\n"},
  {0x434C0802, L"CXL Device Segment 0x%x Bus 0x%x Mailbox Command Opcode 0x%x Error.\n"},
  {0x434C0803, L"CXL Device Segment 0x%x Bus 0x%x Mailbox Command Opcode 0x%x Timeout.\n"},
  {0x434C0804, L"CXL Device Segment 0x%x Bus 0x%x Mailbox Command Opcode 0x%x Get Support Logs Failed.\n"},
  {0x434C0805, L"CXL Device Segment 0x%x Bus 0x%x Mailbox Command Opcode 0x%x Get Log Failed.\n"},
  {0x434C0806, L"CXL Device Segment 0x%x Bus 0x%x Mailbox Command Opcode 0x%x Get Log Data Failed.\n"},
  {0x434C0807, L"CXL Device Segment 0x%x Bus 0x%x Mailbox Interface Not Ready.\n"},

  // CXL Endpoint Initialization and Configuration Errors
  {0x434C0901, L"CXL Device Segment 0x%x Bus 0x%x DVSEC Capability Not Found.\n"},
  {0x434C0902, L"CXL Device Segment 0x%x Bus 0x%x Memory Media Error.\n"},
  {0x434C0903, L"CXL Device Segment 0x%x Bus 0x%x Memory Device Error.\n"},
  {0x434C0904, L"CXL Device Segment 0x%x Bus 0x%x Memory Enable Error.\n"},
  {0x434C0905, L"CXL Device Segment 0x%x Bus 0x%x Memory Not Active!\n"},
  {0x434C0906, L"CXL Device Segment 0x%x Bus 0x%x Component Register Not Found.\n"},
  {0x434C0907, L"CXL Device Segment 0x%x Bus 0x%x HDM Decoder Capability Not Found.\n"},
  {0x434C0908, L"CXL Device Segment 0x%x Bus 0x%x HDM Decoder Range Error.\n"},
  {0x434C0909, L"CXL Device Segment 0x%x Bus 0x%x HDM Decoder Commit Failed.\n"},
  {0x434C090A, L"Failed to convert CXL Memory GcdMemoryType and Attributes.\n"},

  // CXL Switch and Switch CXL Endpoint Errors
  {0x434C0A01, L"CXL Switch Segment 0x%x Bus 0x%x Endpoint Not Found.\n"},
  {0x434C0A02, L"CXL Switch Segment 0x%x Bus 0x%x HDM Decoder Capability Not Found.\n"},
  {0x434C0A03, L"CXL Switch Segment 0x%x Bus 0x%x HDM Decoder Lock On Commit Failed.\n"},
};
/*----------------------------------------------------------------------------------------*/
/**
 * @brief Dump CXL Error Log.
 *
 * @param[in]  CxlErrorLogPtr    Pointer to CXL Error Log structure.
 *
 * @retval None
 */

VOID
EFIAPI
CxlDumpErrorLog (
  IN ERROR_LOG_DATA_STRUCT  *CxlErrorLogPtr
)
{
  UINTN    Index;

  if (CxlErrorLogPtr->Count == 0) {
    IDS_HDT_CONSOLE (MAIN_FLOW, "\nNo CXL Error!\n");
    return;
  }
  IDS_HDT_CONSOLE (MAIN_FLOW, "\n\n************* Dump CXL Error Logs ************\n");
  IDS_HDT_CONSOLE (MAIN_FLOW, "\nError Info:      Data1    Data2    Data3   Data4\n");
  IDS_HDT_CONSOLE (MAIN_FLOW, "====================================================\n");
  for (Index = 0; Index < CxlErrorLogPtr->Count; Index++) {
    IDS_HDT_CONSOLE (MAIN_FLOW, "%x        %4x     %4x     %4x    %4x\n",
                     CxlErrorLogPtr->ErrorLog_Param[Index].ErrorInfo, 
                     CxlErrorLogPtr->ErrorLog_Param[Index].DataParam1,
                     CxlErrorLogPtr->ErrorLog_Param[Index].DataParam2,
                     CxlErrorLogPtr->ErrorLog_Param[Index].DataParam3,
                     CxlErrorLogPtr->ErrorLog_Param[Index].DataParam4);
  }
  IDS_HDT_CONSOLE (MAIN_FLOW, "\nCXL Error Count = %d\n",CxlErrorLogPtr->Count);
}
/**
 * @brief Add CXL Error Log.
 *
 * @param[in]   This               Pointer to the AMD_CXL_ERROR_LOG_PROTOCOL instance.
 * @param[out]  CxlErrorLog        Pointer to the CXL Error Log entry to add in the log.
 *
 * @retval EFI_SUCCESS             Successful add CXL error log.
 * @retval                         EFI_NOT_FOUND and other errors.
 */

EFI_STATUS
EFIAPI
CxlAddErrorLog (
   IN  AMD_CXL_ERROR_LOG_PROTOCOL   *This,
   IN  ERROR_LOG_PARAMS             *CxlErrorLog
)
{
    EFI_STATUS Status;

    if (mAmdErrorLog == NULL) {
      return EFI_NOT_FOUND;
    }

    Status = mAmdErrorLog->AmdErrorLogDxe (mAmdErrorLog,
                             CxlErrorLog->ErrorClass,
                             CxlErrorLog->ErrorInfo,
                             CxlErrorLog->DataParam1,
                             CxlErrorLog->DataParam2,
                             CxlErrorLog->DataParam3,
                             CxlErrorLog->DataParam4
                             );
    return Status;
}
/**
 * @brief Acquire CXL Error Log.
 *
 * @param[in]   This               Pointer to the AMD_CXL_ERROR_LOG_PROTOCOL instance.
 * @param[out]  CxlErrorLogDataPtr Pointer to CXL Error Log structure.
 *
 * @retval EFI_SUCCESS             Successful acquire CXL error log.
 * @retval                         EFI_NOT_FOUND and other errors.
 */

EFI_STATUS
EFIAPI
CxlAcquireErrorLog (
   IN  AMD_CXL_ERROR_LOG_PROTOCOL   *This,
   OUT ERROR_LOG_DATA_STRUCT        **CxlErrorLogDataPtr
)
{
    EFI_STATUS Status;
    ERROR_LOG_DATA_STRUCT        ErrorLogData;
    ERROR_LOG_DATA_STRUCT        *CxlErrorLogData;
    UINTN                        Index;
    UINTN                        Index2 = 0;

    Status = EFI_NOT_FOUND;
    ZeroMem (&ErrorLogData, sizeof (ERROR_LOG_DATA_STRUCT));

    Status = mAmdErrorLogService->AmdAquireErrorLogWithFlagDxe (
                                    mAmdErrorLogService, 
                                    &ErrorLogData, 
                                    FALSE);
    if (EFI_ERROR (Status)) {
      return Status;
    }
    if (ErrorLogData.Count == 0) {
      return EFI_NOT_FOUND;
    }
    CxlErrorLogData = AllocateZeroPool (sizeof (ERROR_LOG_DATA_STRUCT));
    for (Index = 0; Index < ErrorLogData.Count; Index++) {
      if ((ErrorLogData.ErrorLog_Param[Index].ErrorInfo & CXL_ERROR_LOG_ID) == CXL_ERROR_LOG_ID) {
        CopyMem (&(CxlErrorLogData->ErrorLog_Param[Index2]), &(ErrorLogData.ErrorLog_Param[Index]), sizeof (ERROR_LOG_PARAMS));
        CxlErrorLogData->Count++;
        Index2++;
      }
    }
    *CxlErrorLogDataPtr = CxlErrorLogData;
    return EFI_SUCCESS;
}
/**
 * @brief Display CXL Error Logs.
 *
 * @param[in]   This               Pointer to the AMD_CXL_ERROR_LOG_PROTOCOL instance.
 *
 * @retval EFI_SUCCESS             Successful display CXL error log.
 * @retval                         EFI_NOT_FOUND or other errors.
 */

EFI_STATUS
EFIAPI
CxlDisplayErrorLog (
  IN     AMD_CXL_ERROR_LOG_PROTOCOL   *This
  )
{
  EFI_STATUS                            Status;
  ERROR_LOG_DATA_STRUCT                 *CxlErrorLogPtr = NULL;
  
  if (This == NULL) {
    return EFI_INVALID_PARAMETER;
  }
  Status = CxlAcquireErrorLog (This, &CxlErrorLogPtr);
  if (EFI_ERROR (Status)) {
    return Status;
  }
  if (CxlErrorLogPtr->Count == 0) {
    return EFI_NOT_FOUND;
  }
  CxlDumpErrorLog (CxlErrorLogPtr);
  gBS->FreePool (CxlErrorLogPtr);
  return EFI_SUCCESS;
}
/**
 * @brief Display CXL Error Messages.
 *
 * @param[in]   This               Pointer to the AMD_CXL_ERROR_LOG_PROTOCOL instance.
 *
 * @retval EFI_SUCCESS             Successful display CXL error messages.
 * @retval                         EFI_NOT_FOUND or other errors.
 */
EFI_STATUS
EFIAPI
CxlDisplayErrorLogMessages (
  IN     AMD_CXL_ERROR_LOG_PROTOCOL   *This
  )
{
  EFI_STATUS                       Status;
  ERROR_LOG_DATA_STRUCT            *CxlErrorLogPtr = NULL;
  CXL_ERRORCODE_STRING             *TargetString;
  UINT16                           StringIndex;
  UINT16                           CxlErrorStringLength;
  UINT8                            Index;

  if (This == NULL) {
    return EFI_INVALID_PARAMETER;
  }
  Status = CxlAcquireErrorLog (This, &CxlErrorLogPtr);
  if (EFI_ERROR (Status)) {
    return Status;
  }
  if (CxlErrorLogPtr->Count == 0) {
    return EFI_NOT_FOUND;
  }

  CxlErrorStringLength = sizeof (CxlErrorMessages) / sizeof (CxlErrorMessages[0]);
  TargetString = &CxlErrorMessages[0];
  for (Index = 0; Index < CxlErrorLogPtr->Count; Index++) {
    for (StringIndex = 0; StringIndex < CxlErrorStringLength; StringIndex++) {
      if (CxlErrorLogPtr->ErrorLog_Param[Index].ErrorInfo == TargetString[StringIndex].ErrorCode) {
        Print (L"CXL Error Code: 0x%x  --> ", TargetString[StringIndex].ErrorCode);
        Print (TargetString[StringIndex].String, CxlErrorLogPtr->ErrorLog_Param[Index].DataParam1, CxlErrorLogPtr->ErrorLog_Param[Index].DataParam2, CxlErrorLogPtr->ErrorLog_Param[Index].DataParam3);
        Print (L"\n");
      }
    }
  }

  gBS->FreePool (CxlErrorLogPtr);
  return EFI_SUCCESS;
}
/*********************************************************************************
 * Name: CxlErrorLogDxeInit
 *
 * Description
 *   Entry point of the AMD Error Log DXE driver
 *   Perform the configuration init, resource reservation, early post init
 *   and install all the supported protocol
 *
 * Input
 *   ImageHandle : EFI Image Handle for the DXE driver
 *   SystemTable : pointer to the EFI system table
 *
 * Output
 *   EFI_SUCCESS : Module initialized successfully
 *   EFI_ERROR   : Initialization failed (see error for more details)
 *
 *********************************************************************************/
EFI_STATUS
EFIAPI
CxlErrorLogDxeInit (
  IN       EFI_HANDLE         ImageHandle,
  IN       EFI_SYSTEM_TABLE   *SystemTable
  )
{
  EFI_STATUS                            Status = EFI_SUCCESS;
  EFI_HANDLE                            Handle;

  Handle = ImageHandle;

  Status = gBS->LocateProtocol (&gAmdErrorLogProtocolGuid, 
                                NULL, 
                                (VOID **)&mAmdErrorLog
                                );
  if (EFI_ERROR (Status)) {
    IDS_HDT_CONSOLE (MAIN_FLOW, "\nLocate AMD Error Log Protocol failed! Status = %r\n", Status);
    return Status;
  }
  Status = gBS->LocateProtocol (&gAmdErrorLogServiceProtocolGuid, 
                                NULL, 
                                (VOID **)&mAmdErrorLogService
                                );
  if (EFI_ERROR (Status)) {
    IDS_HDT_CONSOLE (MAIN_FLOW, "\nLocate AMD Error Log Service Protocol failed! Status = %r\n", Status);
    return Status;
  }

  // Publish AMD CXL Error Log Protocol
   Status = gBS->InstallProtocolInterface (
                &Handle,
                &gAmdCxlErrorLogProtocolGuid,
                EFI_NATIVE_INTERFACE,
                &mAmdCxlErrorLogProtocol
                );
  if (EFI_ERROR (Status)) {
    IDS_HDT_CONSOLE (MAIN_FLOW, "\nInstall AMD CXL Error Log Protocol failed!\n");
  }

  return Status;

}

