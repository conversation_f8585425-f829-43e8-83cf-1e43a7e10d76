/*****************************************************************************
 * Copyright (C) 2008-2024 Advanced Micro Devices, Inc. All rights reserved.
 *
*****************************************************************************
*/
/* $NoKeywords:$ */
/**
 * @file
 *
 * FCH routine definition
 *
 *
 *
 * @xrefitem bom "File Content Label" "Release Content"
 * @e project:     AGESA
 * @e sub-project: FCH
 * @e \$Revision: 309090 $   @e \$Date: 2014-12-09 10:28:05 -0800 (Tue, 09 Dec 2014) $
 *
 */


#ifndef _FCH_DEF_H_
#define _FCH_DEF_H_

AGESA_STATUS  FchInitReset (IN FCH_RESET_DATA_BLOCK *FchParams);
AGESA_STATUS  FchInitEnv (IN FCH_DATA_BLOCK *FchDataBlock);
AGESA_STATUS  FchInitMid (IN FCH_DATA_BLOCK *FchDataBlock);
AGESA_STATUS  FchInitLate (IN FCH_DATA_BLOCK *FchDataBlock);

UINT32        ReadAlink (IN UINT32 Index, IN AMD_CONFIG_PARAMS *StdHeader);
VOID          WriteAlink (IN UINT32 Index, IN UINT32 Data, IN AMD_CONFIG_PARAMS *StdHeader);
VOID          RwAlink (IN UINT32 Index, IN UINT32 AndMask, IN UINT32 OrMask, IN AMD_CONFIG_PARAMS *StdHeader);
VOID          ReadMem (IN UINT32 Address, IN UINT8 OpFlag, IN VOID *ValuePtr);
VOID          WriteMem (IN UINT32 Address, IN UINT8 OpFlag, IN VOID *ValuePtr);
VOID          RwMem (IN UINT32 Address, IN UINT8 OpFlag, IN UINT32 Mask, IN UINT32 Data);
VOID          ReadPci (IN UINT32 Address, IN UINT8 OpFlag, IN VOID *Value, IN AMD_CONFIG_PARAMS *StdHeader);
VOID          WritePci (IN UINT32 Address, IN UINT8 OpFlag, IN VOID *Value, IN AMD_CONFIG_PARAMS *StdHeader);
VOID          RwPci (IN UINT32 Address, IN UINT8 OpFlag, IN UINT32 Mask, IN UINT32  Data, IN AMD_CONFIG_PARAMS *StdHeader);
VOID          ProgramPciByteTable (IN REG8_MASK* pPciByteTable, IN UINT16 dwTableSize, IN AMD_CONFIG_PARAMS *StdHeader);
VOID          ProgramFchAcpiMmioTbl (IN ACPI_REG_WRITE  *pAcpiTbl, IN AMD_CONFIG_PARAMS *StdHeader);
VOID          ProgramFchSciMapTbl (IN SCI_MAP_CONTROL  *pSciMapTbl, IN FCH_RESET_DATA_BLOCK *FchResetDataBlock);
VOID          ProgramFchGpioTbl (IN GPIO_CONTROL  *pGpioTbl, IN FCH_RESET_DATA_BLOCK *FchResetDataBlock);
VOID          ProgramFchSataPhyTbl (IN SATA_PHY_CONTROL  *pSataPhyTbl, IN FCH_RESET_DATA_BLOCK *FchResetDataBlock);
VOID          GetChipSysMode (IN VOID *Value, IN AMD_CONFIG_PARAMS *StdHeader);
BOOLEAN       IsImcEnabled (IN AMD_CONFIG_PARAMS *StdHeader);
VOID          ReadPmio (IN UINT8 Address, IN UINT8 OpFlag, IN VOID *Value, IN AMD_CONFIG_PARAMS *StdHeader);
VOID          WritePmio (IN UINT8 Address, IN UINT8 OpFlag, IN VOID *Value, IN AMD_CONFIG_PARAMS *StdHeader);
VOID          RwPmio (IN UINT8 Address, IN UINT8 OpFlag, IN UINT32 AndMask, IN UINT32 OrMask, IN AMD_CONFIG_PARAMS *StdHeader);
VOID          ReadPmio2 (IN UINT8 Address, IN UINT8 OpFlag, IN VOID *Value, IN AMD_CONFIG_PARAMS *StdHeader);
VOID          WritePmio2 (IN UINT8 Address, IN UINT8 OpFlag, IN VOID *Value, IN AMD_CONFIG_PARAMS *StdHeader);
VOID          RwPmio2 (IN UINT8 Address, IN UINT8  OpFlag, IN UINT32 AndMask, IN UINT32 OrMask, IN AMD_CONFIG_PARAMS *StdHeader);
VOID          ReadBiosram (IN UINT8 Address, IN UINT8 OpFlag, IN VOID *Value, IN AMD_CONFIG_PARAMS *StdHeader);
VOID          WriteBiosram (IN UINT8 Address, IN UINT8 OpFlag, IN VOID *Value, IN AMD_CONFIG_PARAMS *StdHeader);
VOID          GetFchAcpiMmioBase (OUT UINT32 *AcpiMmioBase, IN AMD_CONFIG_PARAMS *StdHeader);
VOID          GetFchAcpiPmBase (OUT  UINT16 *AcpiPmBase, IN AMD_CONFIG_PARAMS *StdHeader);
UINT8         ReadFchSleepType (IN AMD_CONFIG_PARAMS *StdHeader);
UINT8         ReadFchChipsetRevision (IN AMD_CONFIG_PARAMS *StdHeader);
UINT32        ReadSocType (VOID);
UINT32        ReadSocDieBusNum (UINT8 Die);
BOOLEAN       FchCheckBrhClient (VOID);

VOID          FchSmnRead (IN UINT32 IohcBus, IN UINT32 SmnAddress, IN UINT32 *Value, IN AMD_CONFIG_PARAMS *StdHeader);
VOID          FchSmnWrite (IN UINT32 IohcBus, IN UINT32 SmnAddress, IN UINT32 *Value, IN AMD_CONFIG_PARAMS *StdHeader);
VOID          FchSmnRW (IN UINT32 IohcBus, IN UINT32 SmnAddress, IN UINT32 AndMask, IN UINT32 OrMask, IN AMD_CONFIG_PARAMS *StdHeader);
VOID          FchSmnRead8 (IN UINT32 IohcBus, IN UINT32 SmnAddress, IN UINT8 *Value8, IN AMD_CONFIG_PARAMS *StdHeader);
VOID          FchSmnWrite8 (IN UINT32 IohcBus, IN UINT32 SmnAddress, IN UINT8 *Value8, IN AMD_CONFIG_PARAMS *StdHeader);
VOID          FchSmnRW8 (IN UINT32 IohcBus, IN UINT32 SmnAddress, IN UINT8 AndMask, IN UINT8 OrMask, IN AMD_CONFIG_PARAMS *StdHeader);

///
/// Fch Ab Routines
///
///  Pei Phase
///
VOID  FchInitResetAb (IN VOID* FchDataPtr);
VOID  FchProgramAbPowerOnReset (IN VOID* FchDataPtr);
///
///  Dxe Phase
///
VOID  FchInitEnvAb (IN VOID* FchDataPtr);
VOID  FchInitEnvAbSpecial (IN VOID* FchDataPtr);
VOID  FchInitMidAb (IN VOID* FchDataPtr);
VOID  FchInitLateAb (IN VOID* FchDataPtr);
///
///  Other Public Routines
///
VOID  FchInitEnvAbLinkInit (IN VOID* FchDataPtr);
VOID  FchAbLateProgram (IN VOID* FchDataPtr);

///
/// Fch Pcie Routines
///
///
///  Dxe Phase
///
VOID  ProgramPcieNativeMode (IN VOID* FchDataPtr);


///
/// Fch HwAcpi Routines
///
///  Pei Phase
///
VOID  FchInitResetHwAcpiP          (IN VOID  *FchDataPtr);
VOID  FchInitResetHwAcpi           (IN VOID  *FchDataPtr);
VOID  ProgramFchHwAcpiResetP       (IN VOID  *FchDataPtr);
VOID  FchInitEnableIxC             (IN VOID  *FchDataPtr);
VOID  FchInitEnableWdt             (IN VOID  *FchDataPtr);
VOID  ProgramResetRtcExt           (IN VOID  *FchDataPtr);
VOID  ProgramCpuRstBTmr            (IN VOID  *FchDataPtr);
VOID  FchInitEnableBootTimer       (IN VOID  *FchDataPtr);
///
///  Dxe Phase
///
VOID  FchInitEnvHwAcpiP                      (IN VOID  *FchDataPtr);
VOID  FchInitEnvHwAcpi                       (IN VOID  *FchDataPtr);
VOID  ProgramEnvPFchAcpiMmio                 (IN VOID *FchDataPtr);
VOID  ProgramEnvPFchiLa1MTraceMemoryEn       (IN VOID *FchDataPtr);
VOID  ProgramFchEnvHwAcpiPciReg              (IN VOID *FchDataPtr);
VOID  ProgramSpecificFchInitEnvAcpiMmio      (IN VOID *FchDataPtr);
VOID  ProgramFchEnvSpreadSpectrum            (IN VOID *FchDataPtr);
VOID  ProgramFchEnvAoacInit                  (IN VOID *FchDataPtr);
VOID  PciIntVwInit                           (IN VOID *FchDataPtr);
VOID  FchInternalDeviceIrqInit               (IN VOID *FchDataPtr);
VOID  FchInitMidHwAcpi      (IN VOID  *FchDataPtr);
VOID  FchInitLateHwAcpi     (IN VOID  *FchDataPtr);

///
///  Other Public Routines
///
VOID HpetInit               (IN VOID  *FchDataPtr);
VOID MtC1eEnable            (IN VOID  *FchDataPtr);
VOID GcpuRelatedSetting     (IN VOID  *FchDataPtr);
VOID StressResetModeLate    (IN VOID  *FchDataPtr);
VOID FchEventInitUsbGpe     (IN VOID  *FchDataPtr);
VOID FchEventInitLate       (IN VOID  *FchDataPtr);
VOID FchAl2ahbInit          (IN VOID  *FchDataPtr);
VOID FchI2cUartInit         (IN VOID  *FchDataPtr);
VOID FchI2cUartInitLate     (IN VOID  *FchDataPtr);
VOID FchAlinkRasEnable      (IN VOID  *FchDataPtr);
VOID FchCppcSciInit         (IN VOID  *FchDataPtr);
VOID FchI2cReleaseControlLate (IN VOID *FchDataPtr);

///
/// Fch SATA Routines
///
///  Pei Phase
///
VOID  FchInitResetSata          (IN VOID  *FchDataPtr);
VOID  FchInitResetSataProgram   (IN UINT32 DieBusNum, IN VOID  *FchDataPtr);

///
///  Dxe Phase
///
VOID  FchInitMidSata                   (IN VOID  *FchDataPtr);
VOID  FchInitEnvSata                   (IN VOID  *FchDataPtr);
VOID  FchInitLateSata                  (IN VOID  *FchDataPtr);
VOID  FchKLInitEnvProgramSata          (IN UINT32 DieBusNum, IN UINT32 Controller, IN VOID  *FchDataPtr);
VOID  FchKLInitMidProgramSataRegs      (IN UINT32 DieBusNum, IN UINT32 Controller, IN VOID  *FchDataPtr);
VOID  FchKLInitLateProgramSataRegs     (IN UINT32 DieBusNum, IN UINT32 Controller, IN VOID  *FchDataPtr);

VOID  FchInitEnvSataAhciKL             (IN UINT32 DieBusNum, IN UINT32 Controller, IN VOID  *FchDataPtr);
VOID  FchInitLateSataAhciKL            (IN UINT32 DieBusNum, IN UINT32 Controller, IN VOID  *FchDataPtr);
VOID  FchInitEnvSataRaidKL             (IN UINT32 DieBusNum, IN UINT32 Controller, IN VOID  *FchDataPtr);
VOID  FchInitLateSataRaidKL            (IN UINT32 DieBusNum, IN UINT32 Controller, IN VOID  *FchDataPtr);

VOID  SataAhciSetDeviceNumMsi          (IN UINT32 DieBusNum, IN VOID  *FchDataPtr);
VOID  SataRaidSetDeviceNumMsi          (IN UINT32 DieBusNum, IN VOID  *FchDataPtr);
VOID  SataSetIrqIntResource            (IN VOID  *FchDataPtr, IN AMD_CONFIG_PARAMS *StdHeader);

VOID  SataEnableWriteAccessKL          (IN UINT32 DieBusNum, IN UINT32 Controller);
VOID  SataDisableWriteAccessKL         (IN UINT32 DieBusNum, IN UINT32 Controller);
VOID  FchKLSataInitEnableSata          (IN UINT32 DieBusNum, IN UINT32 Controller, IN VOID  *FchDataPtr);
VOID  FchKLSataInitDisableSata         (IN UINT32 DieBusNum, IN UINT32 Controller, IN VOID  *FchDataPtr);
VOID  FchKLSataInitHideSataPci         (IN UINT32 DieBusNum, IN UINT32 Controller, IN VOID  *FchDataPtr);
VOID  FchKLSataInitHideNbifDev1Pci     (IN UINT32 DieBusNum, IN UINT32 NbioSata, IN VOID  *FchDataPtr);
VOID  FchKLSataInitEnableNbifDev1Pci   (IN UINT32 DieBusNum, IN UINT32 NbioSata, IN VOID  *FchDataPtr);
BOOLEAN  FchKLSataInitCheckNbifDev1Pci (IN UINT32 DieBusNum, IN UINT32 NbioSata, IN VOID  *FchDataPtr);
BOOLEAN  FchKLSataInitCheckSataPci     (IN UINT32 DieBusNum, IN UINT32 Controller, IN VOID  *FchDataPtr);
VOID  FchKLSataInitHideUnconnectedSataPci     (IN UINT32 DieBusNum, IN VOID  *FchDataPtr);
VOID  FchKLSataInitStaggeredSpinStep2  (IN UINT32 DieBusNum, IN UINT32 Controller, IN VOID  *FchDataPtr);
VOID  FchKLSataInitPortOffline         (IN UINT32 DieBusNum, IN UINT32 Controller, IN UINT32 PortNum, IN VOID *FchDataPtr);
VOID  FchKLSataInitPortActive          (IN UINT32 DieBusNum, IN UINT32 Controller, IN UINT32 PortNum, IN VOID *FchDataPtr);
VOID  FchKLSataInitPortClearFre        (IN UINT32 DieBusNum, IN UINT32 Controller, IN UINT32 PortNum, IN VOID *FchDataPtr);
VOID  FchKLSataInitEnableErr           (IN UINT32 DieBusNum, IN UINT32 Controller, IN VOID  *FchDataPtr);
VOID  FchKLSataInitEsata               (IN UINT32 DieBusNum, IN UINT32 Controller, IN VOID  *FchDataPtr);
VOID  FchKLSataInitRsmuCtrl            (IN UINT32 DieBusNum, IN UINT32 Controller, IN VOID  *FchDataPtr);
VOID  FchKLSataInitCtrlReg             (IN UINT32 DieBusNum, IN UINT32 Controller, IN VOID  *FchDataPtr);
VOID  FchKLSataInitMMC                 (IN UINT32 DieBusNum, IN UINT32 Controller, IN VOID  *FchDataPtr);
VOID  FchKLSataSetPortGenMode          (IN UINT32 DieBusNum, IN UINT32 Controller, IN VOID  *FchDataPtr);
VOID  FchKLSataControllerSetPortGenMode (IN UINT32 DieBusNum, IN UINT32 Controller, IN UINT16 PortMode, IN VOID *FchDataPtr);
VOID  FchKLSataShutdownUnconnectedSataPortClock (IN UINT32 DieBusNum, IN UINT32 Controller, IN VOID  *FchDataPtr);
VOID  FchKLSataGpioInitial             (IN UINT32 DieBusNum, IN UINT32 Controller, IN VOID  *FchDataPtr);
VOID  FchKLSataGpioSetPad              (IN UINT32 DieBusNum, IN UINT32 Controller, IN VOID  *FchDataPtr);
VOID  FchKLSataAutoShutdownController  (IN UINT32 DieBusNum, IN UINT32 Controller, IN VOID  *FchDataPtr);
VOID  FchKLSataInitMsi                 (IN UINT32 DieBusNum, IN UINT32 Controller, IN VOID  *FchDataPtr);
VOID  FchKLSataEnableSataMac           (IN UINT32 DieBusNum, IN UINT32 Controller, IN VOID  *FchDataPtr);

VOID  FchKLSataInitDevSlp (IN UINT32 DieBusNum, IN UINT32 Controller, VOID *FchDataPtr);
VOID  FchKLSataInitMpssMap (IN UINT32 DieBusNum, IN VOID *FchDataPtr);

VOID  FchKLSataSetBISTLComplianceMode (IN UINT32 DieBusNum, IN UINT32 Controller, IN VOID *FchDataPtr);

///
/// FCH USB Controller Public Function
///
///  Pei Phase
///
VOID  FchInitResetUsb            (IN VOID  *FchDataPtr);
VOID  FchInitResetXhci           (IN VOID  *FchDataPtr);
VOID  FchInitXhciEnableKL        (IN VOID  *FchDataPtr);
///
///  Dxe Phase
///
VOID  FchInitEnvUsb              (IN VOID  *FchDataPtr);
VOID  FchInitMidUsb              (IN VOID  *FchDataPtr);
VOID  FchInitLateUsb             (IN VOID  *FchDataPtr);
VOID  FchInitEnvUsbXhci          (IN VOID  *FchDataPtr);
VOID  FchInitMidUsbXhci          (IN VOID  *FchDataPtr);
VOID  FchInitLateUsbXhci         (IN VOID  *FchDataPtr);
///
///  Other Public Routines
///
VOID FchKLXhciInitBootProgram    (IN UINT32 DieBusNum, IN VOID *FchDataPtr);
VOID FchKLXhciInitS3ExitProgram  (IN UINT32 DieBusNum, IN VOID *FchDataPtr);
VOID FchKLXhciInitS3EntryProgram (IN UINT32 DieBusNum, IN VOID *FchDataPtr);
VOID FchKLXhciInitSsid (IN UINT32 DieBusNum, IN UINT32 Ssid);
VOID FchKLXhciIohcPmeDisable     (IN UINT32 DieBusNum, IN BOOLEAN  PMEDis);

VOID
FchKLXhciDisablePortByPspMbox (
  IN      UINT32    SocketId,
  IN      UINT8     XhciEnable,
  IN      UINT32    Usb2PortDisable,
  IN      UINT32    Usb3PortDisable,
  IN      UINT8     *SmmBuffer,
  IN      BOOLEAN   *SmmFlag
  );


///
/// Fch Sd Routines
///
VOID  FchInitEnvSd  (IN VOID  *FchDataPtr);
VOID  FchInitMidSd  (IN VOID  *FchDataPtr);
VOID  FchInitLateSd (IN VOID  *FchDataPtr);

///
///  Other Public Routines
///

VOID FchInitEnvSdProgram (IN VOID  *FchDataPtr);

///
/// Fch Spi Routines
///
///  Pei Phase
///
VOID  FchInitResetSpi        (IN VOID  *FchDataPtr);
VOID  FchInitResetLpc        (IN VOID  *FchDataPtr);
VOID  FchInitResetLpcProgram (IN VOID  *FchDataPtr);
///
///  Dxe Phase
///
VOID  FchInitEnvSpi          (IN VOID  *FchDataPtr);
VOID  FchInitMidSpi          (IN VOID  *FchDataPtr);
VOID  FchInitLateSpi         (IN VOID  *FchDataPtr);
VOID  FchInitEnvLpc          (IN VOID  *FchDataPtr);
VOID  FchInitMidLpc          (IN VOID  *FchDataPtr);
VOID  FchInitLateLpc         (IN VOID  *FchDataPtr);
VOID  FchInitEnvLpcProgram   (IN VOID  *FchDataPtr);
///
///  Other Public Routines
///
VOID  FchSpiUnlock       (IN VOID  *FchDataPtr);
VOID  FchSpiLock         (IN VOID  *FchDataPtr);

///
/// Fch ESPI Routines
///
///
VOID  FchInitResetEspi   (IN VOID  *FchDataPtr);
UINT32 getESPIBase ();
UINT32 getESPI1Base ();
VOID  FchEspiSendSmiOutbVW (IN UINT32  EspiBase, IN UINT32 Smib_Type);

/*--------------------------- Documentation Pages ---------------------------*/
VOID  FchStall (IN UINT32 uSec, IN AMD_CONFIG_PARAMS *StdHeader);
VOID  CimFchStall (IN UINT32 uSec, IN AMD_CONFIG_PARAMS *StdHeader);
VOID  FchPciReset (IN AMD_CONFIG_PARAMS *StdHeader);
VOID  OutPort80 (IN UINT32 pcode, IN AMD_CONFIG_PARAMS *StdHeader);
VOID  OutPort1080 (IN UINT32 pcode, IN AMD_CONFIG_PARAMS *StdHeader);
VOID  GetEfuseStatus (IN VOID* Value, IN AMD_CONFIG_PARAMS *StdHeader);
VOID  TurnOffCG2 (OUT VOID);
VOID  BackUpCG2 (OUT VOID);
VOID  FchCopyMem (IN VOID* pDest, IN VOID* pSource, IN UINTN Length);
VOID* GetRomSigPtr (IN UINTN* RomSigPtr, IN AMD_CONFIG_PARAMS *StdHeader);
VOID  RwXhciIndReg (IN UINT32 Index, IN UINT32 AndMask, IN UINT32 OrMask, IN AMD_CONFIG_PARAMS *StdHeader);
VOID  RwXhci0IndReg (IN UINT32 Index, IN UINT32 AndMask, IN UINT32 OrMask, IN AMD_CONFIG_PARAMS *StdHeader);
VOID  RwXhci1IndReg (IN UINT32 Index, IN UINT32 AndMask, IN UINT32 OrMask, IN AMD_CONFIG_PARAMS *StdHeader);
VOID  ReadXhci0Phy (IN UINT32 Port, IN UINT32 Address, IN UINT32 *Value, IN AMD_CONFIG_PARAMS *StdHeader);
VOID  ReadXhci1Phy (IN UINT32 Port, IN UINT32 Address, IN UINT32 *Value, IN AMD_CONFIG_PARAMS *StdHeader);
VOID  RwSsicIndReg (IN UINT32 Index, IN UINT32 AndMask, IN UINT32 OrMask, IN AMD_CONFIG_PARAMS *StdHeader);
VOID  AcLossControl (IN UINT8 AcLossControlValue);
VOID  FchVgaInit (OUT VOID);
VOID  RecordFchConfigPtr (IN UINT32 FchConfigPtr);
VOID  ValidateFchVariant (IN VOID  *FchDataPtr);
VOID  RecordSmiStatus (IN AMD_CONFIG_PARAMS *StdHeader);
VOID  ClearAllSmiStatus (IN AMD_CONFIG_PARAMS *StdHeader);
BOOLEAN  IsExternalClockMode (IN VOID  *FchDataPtr);
VOID  SbSleepTrapControl (IN BOOLEAN SleepTrap);

AGESA_STATUS
FchSpiTransfer (
  IN       UINT8    PrefixCode,
  IN       UINT8    Opcode,
  IN  OUT  UINT8    *DataPtr,
  IN       UINT8    *AddressPtr,
  IN       UINT8    Length,
  IN       BOOLEAN  WriteFlag,
  IN       BOOLEAN  AddressFlag,
  IN       BOOLEAN  DataFlag,
  IN       BOOLEAN  FinishedFlag
  );

BOOLEAN
FchConfigureSpiDeviceDummyCycle (
  IN       UINT32     DeviceID,
  IN       UINT8      SpiMode
  );

UINT32
FchReadSpiId (
  IN       BOOLEAN    Flag
  );

VOID
FchConfigureSpiControllerDummyCycle (
  );

BOOLEAN
FchPlatformSpiQe (
  IN       VOID     *FchDataPtr
  );

VOID
ProgramPMEDis (
  IN       UINT8     dbBusNo,
  IN       BOOLEAN   PMEDis
  );

VOID
FchSmiGenerateControl (
  IN       BOOLEAN   SmiControl
  );

VOID
ProgramEmmcPins (
  IN       BOOLEAN   EmmcEn
  );

VOID
ProgramSdPins (
  IN       BOOLEAN   SdEn
  );

VOID
ProgramLpcPins (
  IN       BOOLEAN   LpcEn
  );

VOID
FchProgramXhciPmeEn (
  IN  UINT32    DieBusNum,
  IN  BOOLEAN   Xhci0Enable,
  IN  BOOLEAN   Xhci1Enable
  );

#endif



