/*****************************************************************************
 *
 * Copyright (C) 2015-2024 Advanced Micro Devices, Inc. All rights reserved.
 *
 *******************************************************************************
 **/
#ifndef _FCH_I2C_MASTER_POST_PPI_H_
#define _FCH_I2C_MASTER_POST_PPI_H_

//-------------------------------------------------------
//
//  FCH I2C Master POST PEI  PPI
//
//
//-------------------------------------------------------

typedef struct _PEI_FCH_I2C_MASTER_POST_PPI {
  UINTN    Revision;               ///< Revision Number
} PEI_FCH_I2C_MASTER_POST_PPI;

// Current PPI revision
#define PEI_FCH_I2C_MASTER_post_PPI_REVISION   0x00

///
/// I2C will re-init again if I2C hold time or Bus frequency PCD value is updated via CBS
/// Then , install this Ppi.
///
extern EFI_GUID gPeiI2cMasterPostPpiGuid;

#endif



