/*****************************************************************************
 * Copyright (C) 2008-2024 Advanced Micro Devices, Inc. All rights reserved.
 *
*****************************************************************************
*/
#include "FchMultiFchPei.h"

#define FILECODE FCH_KUNLUN_FCHKUNLUNMULTIFCHPEI_FCHMULTIFCHPEI_FILECODE

extern EFI_GUID gFchMultiFchResetDataHobGuid;

extern
BOOLEAN
FchKLXhciCheckUsbSkipFlag (
  IN  UINT32   DieBusNum,
  IN  VOID     *FchDataPtr
  );

//
// Module globals
//

#if FCH_DEBUG
VOID
PrintFabricInfo (
  IN CONST EFI_PEI_SERVICES     **PeiServices
  )
{
  EFI_STATUS                            Status;
  AMD_PEI_FABRIC_TOPOLOGY_SERVICES2_PPI *FabricTopologyServicesPpi;
  UINTN                                 NumberOfProcessors;
  UINTN                                 TotalNumberOfDie;
  UINTN                                 TotalNumberOfRootBridges;
  UINTN                                 SystemIdOffset;
  UINTN                                 SystemFabricID;
  UINTN                                 BusNumberBase;
  UINTN                                 BusNumberLimit;
  UINTN                                 SocketIndex;
  UINTN                                 DieIndex;
  UINTN                                 RootBridgeIndex;
  UINTN                                 PhysicalRootBridgeNumber;
  BOOLEAN                               HasFchDevice;
  BOOLEAN                               HasSystemMgmtUnit;
  ROOT_BRIDGE_LOCATION                  FchLocation;
  ROOT_BRIDGE_LOCATION                  SmuLocation;
  AMD_FABRIC_TOPOLOGY_DIE_DEVICE_MAP    *FabricIdMap;

  Status                    = EFI_SUCCESS;
  FabricTopologyServicesPpi = NULL;
  FabricIdMap               = NULL;

  IDS_HDT_CONSOLE (FCH_TRACE, "%a - Enter\n", __FUNCTION__);

  Status = (*PeiServices)->LocatePpi (
                             PeiServices,
                             &gAmdFabricTopologyServices2PpiGuid,
                             0,
                             NULL,
                             (VOID **) &FabricTopologyServicesPpi
                             );
  if ( EFI_ERROR (Status) ) {
    IDS_HDT_CONSOLE (FCH_TRACE, "Cannot fine Fabric PPI. Quit\n");
    return;
  } else {
    Status = FabricTopologyServicesPpi->GetSystemInfo (
                                          &NumberOfProcessors,
                                          &TotalNumberOfDie,
                                          &TotalNumberOfRootBridges,
                                          &FchLocation,
                                          &SmuLocation
                                          );
    IDS_HDT_CONSOLE (
      FCH_TRACE,
      "Get System Info:\n"
      "  NumberOfProcessors       : %d\n"
      "  TotalNumberOfDie         : %d\n"
      "  TotalNumberOfRootBridges : %d\n"
      "  Fch Location             : Socket=0x%x, Die=0x%x, Index=0x%x\n"
      "  Smu Location             : Socket=0x%x, Die=0x%x, Index=0x%x\n",
      NumberOfProcessors,
      TotalNumberOfDie,
      TotalNumberOfRootBridges,
      FchLocation.Socket, FchLocation.Die, FchLocation.Index,
      SmuLocation.Socket, SmuLocation.Die, SmuLocation.Index
      );

    for (SocketIndex = 0; SocketIndex < NumberOfProcessors; SocketIndex++) {
      FabricTopologyServicesPpi->GetProcessorInfo (
                                   SocketIndex,
                                   &TotalNumberOfDie,
                                   &TotalNumberOfRootBridges
                                   );
      IDS_HDT_CONSOLE (
        FCH_TRACE,
        "Socket %d has total %d dies and total %d root bridges\n",
        SocketIndex,
        TotalNumberOfDie,
        TotalNumberOfRootBridges
        );

      for (DieIndex = 0; DieIndex < TotalNumberOfDie; DieIndex++) {
        FabricIdMap = NULL;
        FabricTopologyServicesPpi->GetDieInfo (
                                     SocketIndex,
                                     DieIndex,
                                     &TotalNumberOfRootBridges,
                                     &SystemIdOffset,
                                     &FabricIdMap
                                     );
        IDS_HDT_CONSOLE (
          FCH_TRACE,
          "  Die %d has total %d Root Bridges, SystemIdOffset is 0x%x\n",
          DieIndex,
          TotalNumberOfRootBridges,
          SystemIdOffset
          );

        for (RootBridgeIndex = 0; RootBridgeIndex < TotalNumberOfRootBridges; RootBridgeIndex++) {
          FabricTopologyServicesPpi->GetRootBridgeInfo (
                                       SocketIndex,
                                       DieIndex,
                                       RootBridgeIndex,
                                       &SystemFabricID,
                                       &BusNumberBase,
                                       &BusNumberLimit,
                                       &PhysicalRootBridgeNumber,
                                       &HasFchDevice,
                                       &HasSystemMgmtUnit
                                       );
          IDS_HDT_CONSOLE (
            FCH_TRACE,
            "    Root Bridge %d. SystemFabricID=0x%x, BusNumberBase=0x%x, BusNumberLimit=0x%x, "
            "PhysicalRootBridgeNumber=0x%x, HasFchDevice=%d, HasSystemMgmtUnit=%d\n",
            RootBridgeIndex,
            SystemFabricID,
            BusNumberBase,
            BusNumberLimit,
            PhysicalRootBridgeNumber,
            HasFchDevice,
            HasSystemMgmtUnit
            );
        }
      }

      IDS_HDT_CONSOLE (FCH_TRACE, "\n");
    }
  }

  IDS_HDT_CONSOLE (FCH_TRACE, "%a - Exit\n", __FUNCTION__);
}
#endif


VOID
RedirectIoTranscation (
  IN CONST EFI_PEI_SERVICES             **PeiServices,
  IN       X86_IOBASE_ADDRESS_REGISTER  *IoBase,
  IN       X86_IOLIMIT_ADDRESS_REGISTER *IoLimit
  )
{
  UINT32  i;

  i       = 0;

  IDS_HDT_CONSOLE (FCH_TRACE, "[%a] - Start\n", __FUNCTION__);

  // Save original value
  for (i = 0; i < FCH_MAX_SOCKET; i++) {
    IoBase[i].Value = FabricRegisterAccRead (
                        i,                                       //> Socket X
                        0,                                       //> Die 0
                        X86IOBASEADDRESS_0_FUNC,                 //> 0x00
                        X86IOBASEADDRESS_0_REG,                  //> 0xD00
                        FABRIC_REG_ACC_BC                        //> BC
                        );
    IDS_HDT_CONSOLE (FCH_TRACE, " Skt %d IoBase Reg Org = 0x%x\n", i, IoBase[i].Value);

    IoLimit[i].Value = FabricRegisterAccRead (
                         i,                                       //> Socket X
                         0,                                       //> Die 0
                         X86IOLIMITADDRESS_0_FUNC,                //> 0x00
                         X86IOLIMITADDRESS_0_REG,                 //> 0xD04
                         FABRIC_REG_ACC_BC                        //> BC
                         );
    IDS_HDT_CONSOLE (FCH_TRACE, " Skt %d IoLimit Reg Org = 0x%x\n", i, IoLimit[i].Value);
  }

#if FCH_DEBUG
  for (i = 0; i < FCH_MAX_SOCKET; i++) {
    IDS_HDT_CONSOLE (
      FCH_TRACE,
      "IoLimit[%d].Value = 0x%x\n",
      i,
      IoLimit[i].Value | (1<<FCH_FABRIC_ID_SOCKET_SHIFT)
      );
  }

  IDS_HDT_CONSOLE (FCH_TRACE, "DEBUG: Lost UART\n");
#endif

  // Redirect CD8/CDC IO transaction to second FCH
  for (i = 0; i < FCH_MAX_SOCKET; i++) {
    FabricRegisterAccWrite (
      i,                                       //> Socket X
      0,                                       //> Die 0
      X86IOBASEADDRESS_0_FUNC,                 //> 0x00
      X86IOBASEADDRESS_0_REG,                  //> 0xD00
      FABRIC_REG_ACC_BC,                       //> BC
      3,                                       //> RE=1 & WE=1
      FALSE
      );
    FabricRegisterAccWrite (
      i,                                       //> Socket X
      0,                                       //> Die 0
      X86IOLIMITADDRESS_0_FUNC,                //> 0x00
      X86IOLIMITADDRESS_0_REG,                 //> 0xD04
      FABRIC_REG_ACC_BC,                       //> BC
      IoLimit[i].Value | (1<<FCH_FABRIC_ID_SOCKET_SHIFT),
      FALSE
      );
  }

  //IDS_HDT_CONSOLE (FCH_TRACE, "[%a] - End\n", __FUNCTION__);
}


VOID
RestoreIoTranscation (
  IN CONST EFI_PEI_SERVICES             **PeiServices,
  IN       X86_IOBASE_ADDRESS_REGISTER  *IoBase,
  IN       X86_IOLIMIT_ADDRESS_REGISTER *IoLimit
  )
{
  UINT32  i;

  i = 0;

  //IDS_HDT_CONSOLE (FCH_TRACE, "[%a] - Start\n", __FUNCTION__);

  for (i = 0; i < FCH_MAX_SOCKET; i++) {
    FabricRegisterAccWrite (
      i,                                       //> Socket X
      0,                                       //> Die 0
      X86IOBASEADDRESS_0_FUNC,                 //> 0x00
      X86IOBASEADDRESS_0_REG,                  //> 0xD00
      FABRIC_REG_ACC_BC,                       //> BC
      IoBase[i].Value,                         //>
      FALSE
      );
    FabricRegisterAccWrite (
      i,                                       //> Socket X
      0,                                       //> Die 0
      X86IOLIMITADDRESS_0_FUNC,                //> 0x00
      X86IOLIMITADDRESS_0_REG,                 //> 0xD04
      FABRIC_REG_ACC_BC,                       //> BC
      IoLimit[i].Value,
      FALSE
      );
  }

#if FCH_DEBUG
  IDS_HDT_CONSOLE (FCH_TRACE, "DEBUG: UART is back\n");
#endif

  IDS_HDT_CONSOLE (FCH_TRACE, "[%a] - End\n", __FUNCTION__);
}

/**
 * @brief Get Socket 1 Bus Number.
 *
 * @returns UINTN        2nd socket bus number
 */
UINTN
FchPeiGetSocket1Bus (
  IN CONST EFI_PEI_SERVICES     **PeiServices
  )
{
  EFI_STATUS                              Status;
  UINTN                                   BusNumberBase;
  AMD_PEI_FABRIC_TOPOLOGY_SERVICES2_PPI   *FabricTopology;
  BOOLEAN                                 HasFchDevice;
  UINTN                                   i;
  UINTN                                   NumberOfRootBridges;

  Status          = EFI_SUCCESS;
  FabricTopology  = NULL;
  BusNumberBase   = 0;

  Status = (*PeiServices)->LocatePpi (
                             PeiServices,
                             &gAmdFabricTopologyServices2PpiGuid,
                             0,
                             NULL,
                             (VOID **) &FabricTopology
                             );
  if (Status == EFI_SUCCESS) {
    Status = FabricTopology->GetDieInfo (
                               FCH_SECOND_SOCKET,
                               FCH_SECOND_SOCKET_DIE,
                               &NumberOfRootBridges,
                               NULL,
                               NULL
                               );
    if (Status == EFI_SUCCESS) {
      for (i = 0; i < NumberOfRootBridges; i++) {
        Status = FabricTopology->GetRootBridgeInfo (
                                   FCH_SECOND_SOCKET,
                                   FCH_SECOND_SOCKET_DIE,
                                   i,
                                   NULL,
                                   &BusNumberBase,
                                   NULL,
                                   NULL,
                                   &HasFchDevice,
                                   NULL
                                   );
        if (!EFI_ERROR (Status) && HasFchDevice) {
          DEBUG ((DEBUG_INFO, "[%a] Multi FCH PEI Get 2nd Socket Bus Number = 0x%x\n", __FUNCTION__, BusNumberBase));
          return BusNumberBase;
        }
      }
    }
  }
  ASSERT (FALSE);
  return 0;
}

/**
 * @brief Entry point of the Kunlun multi FCH PEIM.
 *
 * @param[in] FileHandle Pointer to the firmware file system header
 * @param[in] PeiServices Pointer to the PEI service table
 *
 * @returns EFI_STATUS
 * @retval EFI_SUCCESS   Module initialized successfully
 * @retval EFI_ERROR     Initialization failed (see error for more details)
 */
EFI_STATUS
EFIAPI
MultiFchPeiInit (
  IN       EFI_PEI_FILE_HANDLE  FileHandle,
  IN CONST EFI_PEI_SERVICES     **PeiServices
  )
{
  UINTN                     NumberOfInstalledProcessors;
  UINTN                     TotalNumberOfDie;
  UINTN                     TotalNumberOfRootBridges;
  UINT32                    FchBusNum;
  EFI_STATUS                Status;
  EFI_HOB_GUID_TYPE         *FchHob;
  AMD_FCH_INIT_PPI          *FchInitPpi;
  FCH_RESET_DATA_BLOCK      *FchResetDataPtr;
  FCH_MULITI_FCH_RESET_DATA_BLOCK  *FchMfResetData;
  FCH_MULTI_FCH_PEI_PRIVATE *FchMultiFchPeiPrivate;
  EFI_PEI_PPI_DESCRIPTOR    *PpiListMultiFchInit;
  AMD_PEI_FABRIC_TOPOLOGY_SERVICES2_PPI  *FabricTopologyServices;
  X86_IOBASE_ADDRESS_REGISTER            IoBaseReg[FCH_MAX_SOCKET];
  X86_IOLIMIT_ADDRESS_REGISTER           IoLimitReg[FCH_MAX_SOCKET];

  AGESA_TESTPOINT (TpFchMultiFchPeiEntry, NULL);
  IDS_HDT_CONSOLE (FCH_TRACE, "[FCH] MultiFchPeiInit... started.\n");

  NumberOfInstalledProcessors   = 0;
  TotalNumberOfDie              = 0;
  TotalNumberOfRootBridges      = 0;

#if FCH_DEBUG
  PrintFabricInfo (PeiServices);
#endif

  //
  // Check if 2nd socket is installed and enabled
  //
  Status = (*PeiServices)->LocatePpi (
                             PeiServices,
                             &gAmdFabricTopologyServices2PpiGuid,
                             0,
                             NULL,
                             (VOID **)&FabricTopologyServices
                             );
  ASSERT_EFI_ERROR ( Status);

  FabricTopologyServices->GetSystemInfo (
    &NumberOfInstalledProcessors,
    &TotalNumberOfDie,
    &TotalNumberOfRootBridges,
    NULL,
    NULL
    );
  IDS_HDT_CONSOLE (
    FCH_TRACE,
    "[FCH] MultiFchPeiInit: NumberOfInstalledProcessors = %x, TotalNumberOfDie = %x, TotalNumberOfRootBridges = %x\n",
    NumberOfInstalledProcessors,
    TotalNumberOfDie,
    TotalNumberOfRootBridges
    );

  if (NumberOfInstalledProcessors == 1) {
    AGESA_TESTPOINT (TpFchMultiFchPeiExit, NULL);
    IDS_HDT_CONSOLE (FCH_TRACE, "[%a] No Socket1 FCH...Exit\n", __FUNCTION__);
    return EFI_SUCCESS;
  }

  // Create Fch GUID HOB to save RESET_DATA_BLOCK
  Status = (*PeiServices)->CreateHob (
                             PeiServices,
                             EFI_HOB_TYPE_GUID_EXTENSION,
                             sizeof (EFI_HOB_GUID_TYPE) + sizeof (FCH_MULITI_FCH_RESET_DATA_BLOCK),
                             (VOID **)&FchHob
                             );

  ASSERT_EFI_ERROR (Status);
  CopyMem (&FchHob->Name, &gFchMultiFchResetDataHobGuid, sizeof (EFI_GUID));
  FchHob++;
  FchMfResetData = (FCH_MULITI_FCH_RESET_DATA_BLOCK *)FchHob;

  //Update DATA BLOCK
  ZeroMem (FchMfResetData, sizeof (FCH_MULITI_FCH_RESET_DATA_BLOCK));
  FchMfResetData->FchAcpiMmioBase[0] = 0xFED80000ul;

  //Init FCH_MULTI_FCH_PEI_PRIVATE
  Status = (*PeiServices)->AllocatePool (
                             PeiServices,
                             sizeof (FCH_MULTI_FCH_PEI_PRIVATE),
                             (VOID **)&FchMultiFchPeiPrivate
                             );
  ASSERT_EFI_ERROR ( Status);

  ZeroMem (FchMultiFchPeiPrivate, sizeof (FCH_MULTI_FCH_PEI_PRIVATE));

  FchMultiFchPeiPrivate->Signature                          = MULTI_FCH_PEI_PRIVATE_DATA_SIGNATURE;
  FchMultiFchPeiPrivate->FchMultiFchInitPpi.Revision        = AMD_MULTI_FCH_INIT_PPI_REV;

  //
  // Locat Fch Init PPI
  //
  Status = (*PeiServices)->LocatePpi (
                             PeiServices,
                             &gAmdFchInitPpiGuid,
                             0,
                             NULL,
                             (VOID **)&FchInitPpi
                             );

  ASSERT_EFI_ERROR ( Status);

  //
  // Init local Data structure
  //
  Status = (*PeiServices)->AllocatePool (
                             PeiServices,
                             sizeof (FCH_RESET_DATA_BLOCK),
                             (VOID **)&FchResetDataPtr
                             );

  CopyMem (FchResetDataPtr, FchInitPpi->FchResetData, sizeof (FCH_RESET_DATA_BLOCK));

#if FCH_DEBUG
{
  UINT32 TestValue;
  // Test IO
  TestValue = ReadAlink (FCH_ABCFG_REG00 | (UINT32) (ABCFG << 29), NULL);
  IDS_HDT_CONSOLE (FCH_TRACE, "DEBUG: Skt 0 AB CFG VID = 0x%x\n", TestValue);
}
#endif

  //
  // Check 2nd socket IOD
  //
  IDS_HDT_CONSOLE (FCH_TRACE, "1. Get second socket bus number.\n");
  FchBusNum = (UINT32) FchPeiGetSocket1Bus (PeiServices);
  ASSERT (FchBusNum!=0);

  IDS_HDT_CONSOLE (FCH_TRACE, "2. Assign MMIO for second FCH\n");
  FchSecondaryFchMmioRelocate (PeiServices, FchBusNum, FchResetDataPtr);

  IDS_HDT_CONSOLE (FCH_TRACE, "3. Configure second FCH AB\n");
  RedirectIoTranscation (PeiServices, IoBaseReg, IoLimitReg);
#if FCH_DEBUG
  //FCH_BREAKPOINT (2);
#endif
  FchSecondaryFchIORegInit (FchBusNum, FchResetDataPtr);
  RestoreIoTranscation (PeiServices, IoBaseReg, IoLimitReg);

  IDS_HDT_CONSOLE (FCH_TRACE, "4. Disable SD\n");
  FchSecondaryFchDisableSD (FchBusNum, FchResetDataPtr);

  //USB
  IDS_HDT_CONSOLE (FCH_TRACE, "5. Init 2nd FCH USB\n");
  FchSecondaryFchInitUsbPei (FchBusNum, FchResetDataPtr);

  //SATA
  FchSecondaryFchInitSataPei (FchBusNum, FchResetDataPtr);

  //Update Globle Data
  FchMfResetData->FchAcpiMmioBase[1] = (UINT64) FchResetDataPtr->FchAcpiMmioBase;

  // Update PPI data
  CopyMem (
    &FchMultiFchPeiPrivate->FchMultiFchInitPpi.FchMfResetData,
    FchMfResetData,
    sizeof (FCH_MULITI_FCH_RESET_DATA_BLOCK)
    );

  // Allocate memory for the PPI descriptor
  Status = (*PeiServices)->AllocatePool (
                             PeiServices,
                             sizeof (EFI_PEI_PPI_DESCRIPTOR),
                             (VOID **)&PpiListMultiFchInit
                             );
  ASSERT_EFI_ERROR ( Status);

  PpiListMultiFchInit->Flags = (EFI_PEI_PPI_DESCRIPTOR_PPI | EFI_PEI_PPI_DESCRIPTOR_TERMINATE_LIST);
  PpiListMultiFchInit->Guid  = &gAmdFchMultiFchInitPpiGuid;
  PpiListMultiFchInit->Ppi   = &FchMultiFchPeiPrivate->FchMultiFchInitPpi;

  Status = (*PeiServices)->InstallPpi (
                             PeiServices,
                             PpiListMultiFchInit
                             );

  IDS_HDT_CONSOLE (FCH_TRACE, "[FCH] MultiFchPeiInit... Completed.\n");
  AGESA_TESTPOINT (TpFchMultiFchPeiExit, NULL);
  return Status;
}


VOID
FchSecondaryFchInitUsbPei (
  IN  UINT32      DieBusNum,
  IN  VOID        *FchDataPtr
  )
{
  FCH_RESET_DATA_BLOCK    *LocalCfgPtr;
  FCH_KL_USB_OEM_PLATFORM_TABLE *FchUsbOemPlatformTable;

  BOOLEAN   Xhci0Enable;
  BOOLEAN   Xhci1Enable;
  UINT32    Xhci0DevRemovable;
  UINT32    XhciUsb3PortDisable;
  UINT32    XhciUsb2PortDisable;
  UINT32    Usb20OcPin0;
  UINT16    Usb31OcPin0;
  UINT32    Usb20OcPin1;
  UINT16    Usb31OcPin1;

  LocalCfgPtr = (FCH_RESET_DATA_BLOCK *)FchDataPtr;
  FchUsbOemPlatformTable = (FCH_KL_USB_OEM_PLATFORM_TABLE *)(LocalCfgPtr->OemUsbConfigurationTablePtr);

  IDS_HDT_CONSOLE (FCH_TRACE, "[%a] Start.\n", __FUNCTION__);

  if (FchKLXhciCheckUsbSkipFlag (DieBusNum, FchDataPtr)) {
    IDS_HDT_CONSOLE (FCH_TRACE, "[%a] Skip Secondary USB Controller.\n", __FUNCTION__);
    return;
  }

  //
  // Set Usb parameters
  //
  Xhci0Enable         = LocalCfgPtr->FchReset.Xhci0Enable;
  Xhci1Enable         = LocalCfgPtr->FchReset.Xhci1Enable;
  Xhci0DevRemovable   = LocalCfgPtr->Xhci0DevRemovable;
  XhciUsb3PortDisable = LocalCfgPtr->XhciUsb3PortDisable;
  XhciUsb2PortDisable = LocalCfgPtr->XhciUsb2PortDisable;
  Usb20OcPin0         = LocalCfgPtr->XhciOCpinSelect[0].Usb20OcPin;
  Usb31OcPin0         = LocalCfgPtr->XhciOCpinSelect[0].Usb31OcPin;
  Usb20OcPin1         = LocalCfgPtr->XhciOCpinSelect[1].Usb20OcPin;
  Usb31OcPin1         = LocalCfgPtr->XhciOCpinSelect[1].Usb31OcPin;

  LocalCfgPtr->FchReset.Xhci0Enable = PcdGetBool (PcdXhci2Enable);
  LocalCfgPtr->FchReset.Xhci1Enable = PcdGetBool (PcdXhci3Enable);
  LocalCfgPtr->Xhci0DevRemovable    = (UINT32) (PcdGet32 (PcdXhci0DevRemovable)  >> 4);
  LocalCfgPtr->XhciUsb3PortDisable  = (UINT32) (PcdGet32 (PcdXhciUsb3PortDisable) >> 4);
  LocalCfgPtr->XhciUsb2PortDisable  = (UINT32) (PcdGet32 (PcdXhciUsb2PortDisable) >> 4);
  LocalCfgPtr->XhciOCpinSelect[0].Usb20OcPin = (UINT32)(RShiftU64 ( PcdGet64 (PcdXhciUsb20OcPinSelect), 32));
  LocalCfgPtr->XhciOCpinSelect[0].Usb31OcPin = (UINT16)(PcdGet32 (PcdXhciUsb31OcPinSelect) >> 16);
  LocalCfgPtr->XhciOCpinSelect[1].Usb20OcPin = (UINT32)(RShiftU64 (PcdGet64 (PcdXhciUsb20OcPinSelect), 40));
  LocalCfgPtr->XhciOCpinSelect[1].Usb31OcPin = (UINT16)(PcdGet32 (PcdXhciUsb31OcPinSelect) >> 24);

  if ( FchUsbOemPlatformTable->Version_Major == FCH_USB_MAJOR_VERSION
    && FchUsbOemPlatformTable->Version_Minor == FCH_USB_MINOR_VERSION
    && FchUsbOemPlatformTable->TableLength   == sizeof (FCH_KL_USB_OEM_PLATFORM_TABLE) )  //Breithorn (Kunlun) USB D.13
  {
    FchUsbOemPlatformTable->Usb20PhyEnable = FchUsbOemPlatformTable->S1Usb20PhyEnable;
    CopyMem (FchUsbOemPlatformTable->Usb20PhyPort, FchUsbOemPlatformTable->S1Usb20PhyPort, sizeof (FCH_USB20_PHY) * 4);
    FchUsbOemPlatformTable->Usb31PhyEnable = FchUsbOemPlatformTable->S1Usb31PhyEnable;
    CopyMem (FchUsbOemPlatformTable->Usb31PhyPort, FchUsbOemPlatformTable->S1Usb31PhyPort, sizeof (FCH_USB31_PHY) * 4);
  }

  IDS_HDT_CONSOLE (
    FCH_TRACE,
    "Secondary FCH Xhci0Enable = 0x%x, Xhci1Enable = 0x%x\n"
    "PcdXhci2Enable = 0x%x, PcdXhci3Enable = 0x%x\n",
    LocalCfgPtr->FchReset.Xhci0Enable,
    LocalCfgPtr->FchReset.Xhci1Enable,
    PcdGetBool (PcdXhci2Enable),
    PcdGetBool (PcdXhci3Enable)
    );

  if ((FchReadSleepType () == AMD_ACPI_S3) || (AmdCapsuleGetStatus ())) {
    FchKLXhciInitS3ExitProgram (DieBusNum, FchDataPtr);
  } else {
    FchKLXhciInitBootProgram (DieBusNum, FchDataPtr);
  }

  LocalCfgPtr->FchReset.Xhci0Enable          = Xhci0Enable;
  LocalCfgPtr->FchReset.Xhci1Enable          = Xhci1Enable;
  LocalCfgPtr->Xhci0DevRemovable             = Xhci0DevRemovable;
  LocalCfgPtr->XhciUsb3PortDisable           = XhciUsb3PortDisable;
  LocalCfgPtr->XhciUsb2PortDisable           = XhciUsb2PortDisable;
  LocalCfgPtr->XhciOCpinSelect[0].Usb20OcPin = Usb20OcPin0;
  LocalCfgPtr->XhciOCpinSelect[0].Usb31OcPin = Usb31OcPin0;
  LocalCfgPtr->XhciOCpinSelect[1].Usb20OcPin = Usb20OcPin1;
  LocalCfgPtr->XhciOCpinSelect[1].Usb31OcPin = Usb31OcPin1;

  IDS_HDT_CONSOLE (FCH_TRACE, "[%a] End.\n", __FUNCTION__);
}


VOID
FchSecondaryFchInitSataPei (
  IN  UINT32      DieBusNum,
  IN  VOID        *FchDataPtr
  )
{
  UINT8                     SataIod2Enable;
  UINT8                     SataController;
  UINT32                    SataRxPolarity;
  FCH_RESET_DATA_BLOCK      *LocalCfgPtr;

  SataIod2Enable = PcdGet8 (PcdSataEnable2);
  SataRxPolarity = (UINT32) RShiftU64 (PcdGet64 (PcdSataMultiDiePortRxPolarity), 32);
  LocalCfgPtr = (FCH_RESET_DATA_BLOCK *)FchDataPtr;

  IDS_HDT_CONSOLE (FCH_TRACE, "[%a] - Start.\n", __FUNCTION__);
  //
  // Update local Data Structure for each Die
  //
  SataIod2Enable = SataIod2Enable >> 4;
  for (SataController = 0; SataController < KUNLUN_SATA_CONTROLLER_NUM; SataController++) {
    if (SataIod2Enable & (1 << SataController)) {
      LocalCfgPtr->SataEnable[SataController]     = TRUE;
      LocalCfgPtr->SataRxPolarity[SataController] = (UINT8) (SataRxPolarity >> (8 * SataController));
    } else {
      LocalCfgPtr->SataEnable[SataController] = FALSE;
    }
  }

  FchInitResetSataProgram (DieBusNum, LocalCfgPtr);

  IDS_HDT_CONSOLE (FCH_TRACE, "[%a] - Complete.\n", __FUNCTION__);
}


UINT16
GetSocket1RootBridgeNum (
  IN CONST EFI_PEI_SERVICES     **PeiServices
  )
{
  EFI_STATUS                              Status;
  UINTN                                   BusNumberBase;
  AMD_PEI_FABRIC_TOPOLOGY_SERVICES2_PPI   *FabricTopology;
  BOOLEAN                                 HasFchDevice;
  UINTN                                   i;
  UINTN                                   NumberOfRootBridges;

  Status          = EFI_SUCCESS;
  FabricTopology  = NULL;
  BusNumberBase   = 0;

  Status = (*PeiServices)->LocatePpi (
                             PeiServices,
                             &gAmdFabricTopologyServices2PpiGuid,
                             0,
                             NULL,
                             (VOID **) &FabricTopology
                             );
  if (Status == EFI_SUCCESS) {
    Status = FabricTopology->GetDieInfo (
                               FCH_SECOND_SOCKET,
                               FCH_SECOND_SOCKET_DIE,
                               &NumberOfRootBridges,
                               NULL,
                               NULL
                               );
    if (Status == EFI_SUCCESS) {
      for (i = 0; i < NumberOfRootBridges; i++) {
        Status = FabricTopology->GetRootBridgeInfo (
                                   FCH_SECOND_SOCKET,
                                   FCH_SECOND_SOCKET_DIE,
                                   i,
                                   NULL,
                                   &BusNumberBase,
                                   NULL,
                                   NULL,
                                   &HasFchDevice,
                                   NULL
                                   );
        if (!EFI_ERROR (Status) && HasFchDevice) {
          DEBUG ((DEBUG_INFO, "[%a] Multi FCH PEI Get 2nd Socket root bridge number = 0x%x\n", __FUNCTION__, i));
          return (UINT16) i;
        }
      }
    }
  }

  return (UINT16) -1;
}


VOID
FchSecondaryFchMmioRelocate (
  IN CONST EFI_PEI_SERVICES     **PeiServices,
  IN  UINT32      DieBusNum,
  IN  VOID        *FchDataPtr
  )
{
  UINT16                    AltMmioBase;
  UINT64                    Length;
  UINT64                    FchMmioBase;
  FABRIC_TARGET             MmioTarget;
  FABRIC_MMIO_ATTRIBUTE     Attributes;
  FCH_RESET_DATA_BLOCK      *LocalCfgPtr;
#if FCH_DEBUG
  UINT8                     Value8;
#endif
  IDS_HDT_CONSOLE (FCH_TRACE, "[%a] - Start\n", __FUNCTION__);

  LocalCfgPtr = (FCH_RESET_DATA_BLOCK *)FchDataPtr;

  ZeroMem (&Attributes, sizeof (Attributes));
  ZeroMem (&MmioTarget, sizeof (MmioTarget));

  Length                  = 0x2000;
  MmioTarget.TgtType      = TARGET_RB;
  MmioTarget.SocketNum    = 1;          //Socket1
  MmioTarget.RbNum        = GetSocket1RootBridgeNum (PeiServices);

  Attributes.ReadEnable   = 1;
  Attributes.WriteEnable  = 1;
  Attributes.NonPosted    = 0;
  Attributes.MmioType     = NON_PCI_DEVICE_BELOW_4G;

#if FCH_DEBUG
  IDS_HDT_CONSOLE (FCH_TRACE, " MmioTarget.TgtType = 0x%x, MmioTarget.RbNum = 0x%x\n", MmioTarget.TgtType, MmioTarget.RbNum);
#endif

  if (FabricAllocateMmio (&FchMmioBase, &Length, ALIGN_64K, MmioTarget, &Attributes) == EFI_SUCCESS) {
    // Assign MMIO to 2nd FCH.
    IDS_HDT_CONSOLE (FCH_TRACE, " FchMmioBase = 0x%lx\n", FchMmioBase);
    AltMmioBase = (UINT16) (RShiftU64 (FchMmioBase, 16) & 0xFFFF);
    FchSmnRW8 (DieBusNum, FCH_KL_SMN_PMIO_BASE + FCH_PMIOA_REGD6, 0x00, (UINT8) AltMmioBase       , NULL);
    FchSmnRW8 (DieBusNum, FCH_KL_SMN_PMIO_BASE + FCH_PMIOA_REGD7, 0x00, (UINT8) (AltMmioBase >> 8), NULL);
    FchSmnRW8 (DieBusNum, FCH_KL_SMN_PMIO_BASE + FCH_PMIOA_REGD5, 0xFC,  0x1                      , NULL);
    LocalCfgPtr->FchAcpiMmioBase = (UINT32)FchMmioBase;
    IDS_HDT_CONSOLE (FCH_TRACE, " LocalCfgPtr->FchAcpiMmioBase = 0x%x\n", LocalCfgPtr->FchAcpiMmioBase);

#if FCH_DEBUG
    FchSmnRead8 (DieBusNum, FCH_KL_SMN_PMIO_BASE + FCH_PMIOA_REGD5, &Value8, NULL);
    IDS_HDT_CONSOLE (FCH_TRACE, " Read back D5 = 0x%x.\n", Value8);
    FchSmnRead8 (DieBusNum, FCH_KL_SMN_PMIO_BASE + FCH_PMIOA_REGD6, &Value8, NULL);
    IDS_HDT_CONSOLE (FCH_TRACE, " Read back D6 = 0x%x.\n", Value8);
    FchSmnRead8 (DieBusNum, FCH_KL_SMN_PMIO_BASE + FCH_PMIOA_REGD7, &Value8, NULL);
    IDS_HDT_CONSOLE (FCH_TRACE, " Read back D7 = 0x%x.\n", Value8);

    // Test 2nd FCH MMIO
    IDS_HDT_CONSOLE (FCH_TRACE, " Test if FCH MMIO is accessible.\n");
    ReadMem (LocalCfgPtr->FchAcpiMmioBase + PMIO_BASE + FCH_PMIOA_REGD6, AccessWidth16, &AltMmioBase);
    IDS_HDT_CONSOLE (
      FCH_TRACE,
      " AltMmioBase (0x%x) = 0x%x.\n",
      LocalCfgPtr->FchAcpiMmioBase + PMIO_BASE + FCH_PMIOA_REGD6,
      AltMmioBase
      );
    if ( AltMmioBase != ((LocalCfgPtr->FchAcpiMmioBase)>>16) ) {
      IDS_HDT_CONSOLE (
        FCH_TRACE,
        "ERROR: Read 2nd FCH::PM::altmmiobase is not 0x%x. in File %a Line %d\n",
        (LocalCfgPtr->FchAcpiMmioBase)>>16,
        __FILE__,
        __LINE__
        );
      FCH_BREAKPOINT (1);
    }
#endif
  }

  IDS_HDT_CONSOLE (FCH_TRACE, "[%a] - End\n", __FUNCTION__);
}


VOID
FchSecondaryFchIORegInit (
  IN  UINT32      DieBusNum,
  IN  VOID        *FchDataPtr
  )
{
  // Init AB registers
  FchSecondaryFchInitABPei (DieBusNum, FchDataPtr);
  // Init PMIO registers
  //FchSecondaryFchInitPmioPei (DieBusNum, FchDataPtr);
}


VOID
FchSecondaryFchInitABPei (
  IN  UINT32      DieBusNum,
  IN  VOID        *FchDataPtr
  )
{
  FCH_RESET_DATA_BLOCK      *LocalCfgPtr;

  LocalCfgPtr = (FCH_RESET_DATA_BLOCK *)FchDataPtr;

  //IDS_HDT_CONSOLE (FCH_TRACE, "[%a] Program Fch AB registers...Start.\n", __FUNCTION__);

  // Enable FCH::AB::ABIndex & FCH::AB::ABData by FCH::PM::abregbar
  FchSmnRW (DieBusNum, FCH_KL_SMN_PMIO_BASE + FCH_PMIOA_REGE0, 0x00, ALINK_ACCESS_INDEX, NULL);

#if FCH_DEBUG
  // Test IO
  if ( (ReadAlink (FCH_ABCFG_REG00 | (UINT32) (ABCFG << 29), NULL) & 0xFFFF) != FCH_AB_VENDORID ) {
    volatile UINT32 *Ptr32 = (volatile UINT32*)0x100000;
    *Ptr32 = ReadAlink (FCH_ABCFG_REG00 | (UINT32) (ABCFG << 29), NULL);
    Ptr32 ++;
    *Ptr32 = 0xDEADFBFB;
    FCH_BREAKPOINT (3);
  }
#endif

  // AB register init
  RwAlink (FCH_ABCFG_REG80 | (UINT32) (ABCFG << 29), ~(UINT32) (BIT17 + BIT18 + BIT0), BIT17 + BIT18 + BIT0, NULL);
  RwAlink (FCH_ABCFG_REG90 | (UINT32) (ABCFG << 29), ~(UINT32) BIT21, BIT21, NULL);
  //RwAlink (FCH_ABCFG_REG94 | (UINT32) (ABCFG << 29), ~(UINT32) BIT20, BIT20 + 0x00FEE, NULL);
  RwAlink (FCH_ABCFG_REG10054 | (UINT32) (ABCFG << 29), 0xFF000000, 0x7FF, NULL);
  RwAlink (FCH_ABCFG_REG10060 | (UINT32) (ABCFG << 29), 0xFBFFFFFF, 0x02000000, NULL);
  RwAlink (FCH_ABCFG_REG10090 | (UINT32) (ABCFG << 29), ~(UINT32) BIT16, BIT16, NULL);

  if ( PcdGet8 (PcdAbClockGating) ) {
    RwAlink (FCH_ABCFG_REG54 | (UINT32) (ABCFG << 29), ~ (UINT32) (0x1 << 4), (UINT32) (0x1 << 4), NULL);
    RwAlink (FCH_ABCFG_REG54 | (UINT32) (ABCFG << 29), ~ (UINT32) (0x1 << 24), (UINT32) (0x1 << 24), NULL);
    RwAlink (FCH_ABCFG_REG10054 | (UINT32) (ABCFG << 29), ~ (UINT32) (0x3 << 24), (UINT32) (0x3 << 24), NULL);
  } else {
    RwAlink (FCH_ABCFG_REG10054 | (UINT32) (ABCFG << 29), ~ (UINT32) (0x3 << 24), 0, NULL);
    RwAlink (FCH_ABCFG_REG54 | (UINT32) (ABCFG << 29), ~ (UINT32) (0x1 << 24), 0, NULL);
    RwAlink (FCH_ABCFG_REG54 | (UINT32) (ABCFG << 29), ~ (UINT32) (0x1 << 4), 0, NULL);
  }

  //AbDmaMemoryWrtie3264B
  RwAlink (FCH_ABCFG_REG54 | (UINT32) (ABCFG << 29), ~ (UINT32) (0x1 << 0), (UINT32) (0x0  << 0), NULL);
  RwAlink (FCH_ABCFG_REG54 | (UINT32) (ABCFG << 29), ~ (UINT32) (0x1 << 2), (UINT32) (0x1  << 2), NULL);
  //AbMemoryPowerSaving
  RwMem (LocalCfgPtr->FchAcpiMmioBase + MISC_BASE + FCH_MISC_REG68, AccessWidth8, 0xFB, 0x00);
  RwAlink (FCH_ABCFG_REGBC | (UINT32) (ABCFG << 29), ~ (UINT32) (0x1 << 4), (UINT32) (0x1  << 4), NULL);
  RwAlink (FCH_ABCFG_REG58 | (UINT32) (ABCFG << 29), ~ (UINT32) (0x1 << 29), (UINT32) (0x1  << 29), NULL);
  RwAlink (FCH_ABCFG_REG58 | (UINT32) (ABCFG << 29), ~ ((UINT32) 0x1 << 31), ((UINT32) 0x1  << 31), NULL);

  //
  // FCH BTS Check Table
  //
  RwMem (LocalCfgPtr->FchAcpiMmioBase + MISC_BASE + FCH_MISC_REG6C + 2, AccessWidth8, 0xDF, BIT5);  // MISC 0x6C BIT21
  RwMem (LocalCfgPtr->FchAcpiMmioBase + MISC_BASE + FCH_MISC_REG6C + 3, AccessWidth8, 0xF7, BIT3);  // MISC 0x6C BIT27

  //
  // A/B Clock Gate-OFF
  //
  if ( PcdGet8 (PcdALinkClkGateOff) ) {
    RwMem (LocalCfgPtr->FchAcpiMmioBase + MISC_BASE + FCH_MISC_REG2C + 2, AccessWidth8, 0xFE, BIT0);
  } else {
    RwMem (LocalCfgPtr->FchAcpiMmioBase + MISC_BASE + FCH_MISC_REG2C + 2, AccessWidth8, 0xFE, 0);
  }
  if ( PcdGet8 (PcdBLinkClkGateOff) ) {
    RwMem (LocalCfgPtr->FchAcpiMmioBase + MISC_BASE + FCH_MISC_REG2C + 2, AccessWidth8, 0xFD, BIT1);
    FchSmnRW (DieBusNum, FCH_KL_SMN_RSMU_BASE + 0x1C, ~(UINT32) BIT31, BIT31, NULL);
  } else {
    RwMem (LocalCfgPtr->FchAcpiMmioBase + MISC_BASE + FCH_MISC_REG2C + 2, AccessWidth8, 0xFD, 0);
    FchSmnRW (DieBusNum, FCH_KL_SMN_RSMU_BASE + 0x1C, ~(UINT32) BIT31, 0, NULL);
  }
  if ( PcdGet8 (PcdALinkClkGateOff) | PcdGet8 (PcdBLinkClkGateOff) ) {
    RwMem (LocalCfgPtr->FchAcpiMmioBase + PMIO_BASE + FCH_PMIOA_REG04 + 2, AccessWidth8, 0xFE, BIT0);
  } else {
    RwMem (LocalCfgPtr->FchAcpiMmioBase + PMIO_BASE + FCH_PMIOA_REG04 + 2, AccessWidth8, 0xFE, 0);
  }

  if ( PcdGetBool (PcdSbgMemoryPowerSaving) ) {
    RwMem (LocalCfgPtr->FchAcpiMmioBase + MISC_BASE + FCH_MISC_REG68, AccessWidth8, 0xFD, 0x00);
    RwAlink (FCH_ABCFG_REG208 | (UINT32) (ABCFG << 29), ~ (UINT32) (0x5 << 7), (UINT32) (0x5 << 7), NULL);
    RwAlink (FCH_ABCFG_REG238 | (UINT32) (ABCFG << 29), ~ (UINT32) (0x1 << 10), (UINT32) (0x1  << 10), NULL);
  } else {
    RwAlink (FCH_ABCFG_REG208 | (UINT32) (ABCFG << 29), ~ (UINT32) (0x5 << 7), (UINT32) 0x0, NULL);
    RwAlink (FCH_ABCFG_REG238 | (UINT32) (ABCFG << 29), ~ (UINT32) (0x1 << 10), (UINT32) 0x0, NULL);
    RwMem (LocalCfgPtr->FchAcpiMmioBase + MISC_BASE + FCH_MISC_REG68, AccessWidth8, 0xFD, 0x02);
  }
  //
  // SDP Internal Clock Gating
  //
  if ( PcdGetBool (PcdSbgClockGating)) {
    RwAlink (
      FCH_ABCFG_REG208 | (UINT32) (ABCFG << 29),
      ~ (UINT32) (BIT15 + BIT16 + BIT17 + BIT22),
      (UINT32) (BIT15 + BIT16 + BIT17 + BIT22),
      NULL
      );
    RwAlink (FCH_ABCFG_REG228 | (UINT32) (ABCFG << 29), ~ (UINT32) (BIT1 + BIT3), (UINT32) (BIT1 + BIT3), NULL);
  } else {
    RwAlink (
      FCH_ABCFG_REG208 | (UINT32) (ABCFG << 29),
      ~ (UINT32) (BIT15 + BIT16 + BIT17 + BIT22),
      (UINT32) 0x0,
      NULL
      );
    RwAlink (FCH_ABCFG_REG228 | (UINT32) (ABCFG << 29), ~ (UINT32) (BIT1 + BIT3), (UINT32) 0x0, NULL);
  }
  //
  // XDMA DMA Write 16 byte Mode
  //
  if ( PcdGetBool (PcdXdmaDmaWrite16ByteMode)) {
    RwAlink (FCH_ABCFG_REG180 | (UINT32) (ABCFG << 29), ~ (UINT32) (0x1 << 0), (UINT32) (0x1  << 0), NULL);
  } else {
    RwAlink (FCH_ABCFG_REG180 | (UINT32) (ABCFG << 29), ~ (UINT32) (0x1 << 0), (UINT32) (0x0), NULL);
  }
  //
  // XDMA Memory Power Saving
  //
  if ( PcdGetBool (PcdXdmaMemoryPowerSaving) ) {
    RwAlink (FCH_ABCFG_REG184 | (UINT32) (ABCFG << 29), ~ (UINT32) (0x1 << 2), (UINT32) (0x1  << 2), NULL);
  } else {
    RwAlink (FCH_ABCFG_REG184 | (UINT32) (ABCFG << 29), ~ (UINT32) (0x1 << 2), (UINT32) 0x0, NULL);
  }
  //
  // XDMA Pending NPR Threshold
  //
  if ( PcdGet8 (PcdXdmaPendingNprThreshold) ) {
    RwAlink (
      FCH_ABCFG_REG180 | (UINT32) (ABCFG << 29),
      ~ (UINT32) (0x1F << 8),
      (UINT32) (PcdGet8 (PcdXdmaPendingNprThreshold)  << 8),
      NULL
      );
  } else {
    RwAlink (FCH_ABCFG_REG180 | (UINT32) (ABCFG << 29), ~ (UINT32) (0x1F << 8), (UINT32) (0x0), NULL);
  }
  //
  // XDMA DNCPL Order Dis
  //
  if ( PcdGetBool (PcdXdmaDncplOrderDis) ) {
    RwAlink (FCH_ABCFG_REG180 | (UINT32) (ABCFG << 29), ~ (UINT32) (0x1 << 5), (UINT32) (0x1  << 5), NULL);
  } else {
    RwAlink (FCH_ABCFG_REG180 | (UINT32) (ABCFG << 29), ~ (UINT32) (0x1 << 5), (UINT32) (0x0), NULL);
  }
  //
  // SDPHOST_BYPASS_DATA_PAC
  //
  if ( PcdGetBool (PcdSdphostBypassDataPack) ) {
    RwAlink (FCH_ABCFG_REG22C | (UINT32) (ABCFG << 29), ~ (UINT32) (0x1 << 11), (UINT32) (0x1  << 11), NULL);
  } else {
    RwAlink (FCH_ABCFG_REG22C | (UINT32) (ABCFG << 29), ~ (UINT32) (0x1 << 11), (UINT32) (0x0), NULL);
  }
  //
  // SDPHOST_DIS_NPMWR_PROTECT
  //
  if ( PcdGetBool (PcdSdphostDisNpmwrProtect) ) {
    RwAlink (FCH_ABCFG_REG22C | (UINT32) (ABCFG << 29), ~ (UINT32) (0x1 << 14), (UINT32) (0x1  << 14), NULL);
  } else {
    RwAlink (FCH_ABCFG_REG22C | (UINT32) (ABCFG << 29), ~ (UINT32) (0x1 << 14), (UINT32) (0x0), NULL);
  }

  // Disable Syncflood
  RwAlink (FCH_ABCFG_REG10050 | (UINT32) (ABCFG << 29), ~(UINT32) BIT2, 0, NULL);

  FchSmnRW (DieBusNum, FCH_KL_SMN_PMIO_BASE + FCH_PMIOA_REGE0, 0x00, 0x00, NULL);
  //IDS_HDT_CONSOLE (FCH_TRACE, "[FchQTSecondaryFchInitABPei] Program Fch AB registers...Complete.\n");
}


VOID
FchSecondaryFchDisableSD (
  IN  UINT32      DieBusNum,
  IN  VOID        *FchDataPtr
  )
{
  UINT32                    FchAcpiMmio;
  FCH_RESET_DATA_BLOCK      *LocalCfgPtr;

  LocalCfgPtr = (FCH_RESET_DATA_BLOCK *)FchDataPtr;
  FchAcpiMmio = LocalCfgPtr->FchAcpiMmioBase;

  RwMem (FchAcpiMmio + PMIO_BASE + FCH_PMIOA_REGD3, AccessWidth8, 0xBF, 0x00);
  RwMem (FchAcpiMmio + PMIO_BASE + FCH_PMIOA_REGE8, AccessWidth8, 0xFE, 0x00);
  RwMem (FchAcpiMmio + AOAC_BASE + FCH_AOAC_REG72,  AccessWidth8, 0xF7, 0x00);
}



