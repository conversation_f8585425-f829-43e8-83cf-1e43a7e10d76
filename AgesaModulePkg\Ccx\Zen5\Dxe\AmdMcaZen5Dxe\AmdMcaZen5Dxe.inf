#;*****************************************************************************
#;
#; Copyright (C) 2008-2024 Advanced Micro Devices, Inc. All rights reserved.
#;
#;******************************************************************************

[Defines]
  INF_VERSION                    = 0x00010005
  BASE_NAME                      = AmdMcaZen5Dxe
  FILE_GUID                      = 254BAFFE-F25E-45F1-A06F-5EF11443ACA4
  MODULE_TYPE                    = DXE_DRIVER
  VERSION_STRING                 = 1.0
  ENTRY_POINT                    = AmdMcaZen5DxeInit

[Packages]
  MdePkg/MdePkg.dec
  AgesaModulePkg/AgesaModuleCcxPkg.dec
  AgesaModulePkg/AgesaModuleDfPkg.dec
  AgesaModulePkg/AgesaModuleNbioPkg.dec
  AgesaModulePkg/AgesaModuleRasPkg.dec
  AgesaPkg/AgesaPkg.dec

[LibraryClasses.common.DXE_DRIVER]
  BaseLib
  UefiLib
  BaseMemoryLib
  CoreTopologyV3Lib
  AmdSocBaseLib

[LibraryClasses]
  BaseLib
  UefiDriverEntryPoint
  AmdBaseLib
  AmdPspApobLib

[Sources]
  AmdMcaZen5Dxe.c
  AmdMcaZen5Dxe.h

[Guids]

[Protocols]
  gAmdCoreTopologyServicesV3ProtocolGuid  #CONSUMED
  gEfiMpServiceProtocolGuid               #CONSUMED

[Pcd]

[Depex]
  gAmdMcaZen5DepexProtocolGuid AND
  gAmdCoreTopologyServicesV3ProtocolGuid AND
  gEfiMpServiceProtocolGuid


